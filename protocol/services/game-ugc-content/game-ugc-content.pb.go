// Code generated by protoc-gen-go. DO NOT EDIT.
// source: game-ugc-content/game-ugc-content.proto

package game_ugc_content // import "golang.52tt.com/protocol/services/game-ugc-content"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 创建动态tab和绑定话题相关
// 推送人群
type CrowdType int32

const (
	CrowdType_CrowdTypeNone  CrowdType = 0
	CrowdType_CrowdTypeAll   CrowdType = 1
	CrowdType_CrowdTypeGroup CrowdType = 2
)

var CrowdType_name = map[int32]string{
	0: "CrowdTypeNone",
	1: "CrowdTypeAll",
	2: "CrowdTypeGroup",
}
var CrowdType_value = map[string]int32{
	"CrowdTypeNone":  0,
	"CrowdTypeAll":   1,
	"CrowdTypeGroup": 2,
}

func (x CrowdType) String() string {
	return proto.EnumName(CrowdType_name, int32(x))
}
func (CrowdType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{0}
}

// 区分不同类型的tab
type ConfigTabType int32

const (
	ConfigTabType_ConfigTabType_None         ConfigTabType = 0
	ConfigTabType_ConfigTabType_ChannelList  ConfigTabType = 1
	ConfigTabType_ConfigTabType_PostTab      ConfigTabType = 2
	ConfigTabType_ConfigTabType_ActivityPost ConfigTabType = 3
	ConfigTabType_ConfigTabType_ActivitySet  ConfigTabType = 4
	ConfigTabType_ConfigTabType_GamePalCard  ConfigTabType = 5
	ConfigTabType_ConfigTabType_GroupChat    ConfigTabType = 6
)

var ConfigTabType_name = map[int32]string{
	0: "ConfigTabType_None",
	1: "ConfigTabType_ChannelList",
	2: "ConfigTabType_PostTab",
	3: "ConfigTabType_ActivityPost",
	4: "ConfigTabType_ActivitySet",
	5: "ConfigTabType_GamePalCard",
	6: "ConfigTabType_GroupChat",
}
var ConfigTabType_value = map[string]int32{
	"ConfigTabType_None":         0,
	"ConfigTabType_ChannelList":  1,
	"ConfigTabType_PostTab":      2,
	"ConfigTabType_ActivityPost": 3,
	"ConfigTabType_ActivitySet":  4,
	"ConfigTabType_GamePalCard":  5,
	"ConfigTabType_GroupChat":    6,
}

func (x ConfigTabType) String() string {
	return proto.EnumName(ConfigTabType_name, int32(x))
}
func (ConfigTabType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{1}
}

type TabSubType int32

const (
	TabSubType_GAME                  TabSubType = 0
	TabSubType_COMPREHENSIVE_CHANNEL TabSubType = 1
)

var TabSubType_name = map[int32]string{
	0: "GAME",
	1: "COMPREHENSIVE_CHANNEL",
}
var TabSubType_value = map[string]int32{
	"GAME":                  0,
	"COMPREHENSIVE_CHANNEL": 1,
}

func (x TabSubType) String() string {
	return proto.EnumName(TabSubType_name, int32(x))
}
func (TabSubType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{2}
}

// 接口请求来源
type RequestSource int32

const (
	RequestSource_RequestSource_None        RequestSource = 0
	RequestSource_RequestSource_ConfigTab   RequestSource = 1
	RequestSource_RequestSource_Title       RequestSource = 2
	RequestSource_RequestSource_ActivityTab RequestSource = 3
)

var RequestSource_name = map[int32]string{
	0: "RequestSource_None",
	1: "RequestSource_ConfigTab",
	2: "RequestSource_Title",
	3: "RequestSource_ActivityTab",
}
var RequestSource_value = map[string]int32{
	"RequestSource_None":        0,
	"RequestSource_ConfigTab":   1,
	"RequestSource_Title":       2,
	"RequestSource_ActivityTab": 3,
}

func (x RequestSource) String() string {
	return proto.EnumName(RequestSource_name, int32(x))
}
func (RequestSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{3}
}

// 禁止发帖配置类型
type BanConfigType int32

const (
	BanConfigType_BanConfigTypeNone       BanConfigType = 0
	BanConfigType_BanConfigTypeUser       BanConfigType = 1
	BanConfigType_BanConfigTypeCrowdGroup BanConfigType = 2
)

var BanConfigType_name = map[int32]string{
	0: "BanConfigTypeNone",
	1: "BanConfigTypeUser",
	2: "BanConfigTypeCrowdGroup",
}
var BanConfigType_value = map[string]int32{
	"BanConfigTypeNone":       0,
	"BanConfigTypeUser":       1,
	"BanConfigTypeCrowdGroup": 2,
}

func (x BanConfigType) String() string {
	return proto.EnumName(BanConfigType_name, int32(x))
}
func (BanConfigType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{4}
}

// 配置生效状态
type Status int32

const (
	Status_StatusNone        Status = 0
	Status_StatusActive      Status = 1
	Status_StatusInEffective Status = 2
	Status_StatusExpired     Status = 3
)

var Status_name = map[int32]string{
	0: "StatusNone",
	1: "StatusActive",
	2: "StatusInEffective",
	3: "StatusExpired",
}
var Status_value = map[string]int32{
	"StatusNone":        0,
	"StatusActive":      1,
	"StatusInEffective": 2,
	"StatusExpired":     3,
}

func (x Status) String() string {
	return proto.EnumName(Status_name, int32(x))
}
func (Status) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{5}
}

// 配置类型
type BanPostType int32

const (
	BanPostType_BanPostTypePost            BanPostType = 0
	BanPostType_BanPostTypeGamePalCard     BanPostType = 1
	BanPostType_BanPostTypeHideGamePalCard BanPostType = 2
)

var BanPostType_name = map[int32]string{
	0: "BanPostTypePost",
	1: "BanPostTypeGamePalCard",
	2: "BanPostTypeHideGamePalCard",
}
var BanPostType_value = map[string]int32{
	"BanPostTypePost":            0,
	"BanPostTypeGamePalCard":     1,
	"BanPostTypeHideGamePalCard": 2,
}

func (x BanPostType) String() string {
	return proto.EnumName(BanPostType_name, int32(x))
}
func (BanPostType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{6}
}

type ReqSource int32

const (
	ReqSource_INVALID_SOURCE   ReqSource = 0
	ReqSource_SEARCH_SOURCE    ReqSource = 1
	ReqSource_GAME_RCMD_SOURCE ReqSource = 2
)

var ReqSource_name = map[int32]string{
	0: "INVALID_SOURCE",
	1: "SEARCH_SOURCE",
	2: "GAME_RCMD_SOURCE",
}
var ReqSource_value = map[string]int32{
	"INVALID_SOURCE":   0,
	"SEARCH_SOURCE":    1,
	"GAME_RCMD_SOURCE": 2,
}

func (x ReqSource) String() string {
	return proto.EnumName(ReqSource_name, int32(x))
}
func (ReqSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{7}
}

type UnmarshalType int32

const (
	UnmarshalType_UNMARSHAL_TYPE_DEFAULT  UnmarshalType = 0
	UnmarshalType_UNMARSHAL_TYPE_PROTOBUF UnmarshalType = 1
	UnmarshalType_UNMARSHAL_TYPE_JSON     UnmarshalType = 2
)

var UnmarshalType_name = map[int32]string{
	0: "UNMARSHAL_TYPE_DEFAULT",
	1: "UNMARSHAL_TYPE_PROTOBUF",
	2: "UNMARSHAL_TYPE_JSON",
}
var UnmarshalType_value = map[string]int32{
	"UNMARSHAL_TYPE_DEFAULT":  0,
	"UNMARSHAL_TYPE_PROTOBUF": 1,
	"UNMARSHAL_TYPE_JSON":     2,
}

func (x UnmarshalType) String() string {
	return proto.EnumName(UnmarshalType_name, int32(x))
}
func (UnmarshalType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{8}
}

// 话题相关
type Topic struct {
	TopicId              string   `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	TopicName            string   `protobuf:"bytes,2,opt,name=topic_name,json=topicName,proto3" json:"topic_name,omitempty"`
	PostCount            uint32   `protobuf:"varint,3,opt,name=post_count,json=postCount,proto3" json:"post_count,omitempty"`
	CreateAt             int64    `protobuf:"varint,4,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	DisplayTopicName     string   `protobuf:"bytes,5,opt,name=display_topic_name,json=displayTopicName,proto3" json:"display_topic_name,omitempty"`
	IsDisplay            bool     `protobuf:"varint,6,opt,name=is_display,json=isDisplay,proto3" json:"is_display,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Topic) Reset()         { *m = Topic{} }
func (m *Topic) String() string { return proto.CompactTextString(m) }
func (*Topic) ProtoMessage()    {}
func (*Topic) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{0}
}
func (m *Topic) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Topic.Unmarshal(m, b)
}
func (m *Topic) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Topic.Marshal(b, m, deterministic)
}
func (dst *Topic) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Topic.Merge(dst, src)
}
func (m *Topic) XXX_Size() int {
	return xxx_messageInfo_Topic.Size(m)
}
func (m *Topic) XXX_DiscardUnknown() {
	xxx_messageInfo_Topic.DiscardUnknown(m)
}

var xxx_messageInfo_Topic proto.InternalMessageInfo

func (m *Topic) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *Topic) GetTopicName() string {
	if m != nil {
		return m.TopicName
	}
	return ""
}

func (m *Topic) GetPostCount() uint32 {
	if m != nil {
		return m.PostCount
	}
	return 0
}

func (m *Topic) GetCreateAt() int64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *Topic) GetDisplayTopicName() string {
	if m != nil {
		return m.DisplayTopicName
	}
	return ""
}

func (m *Topic) GetIsDisplay() bool {
	if m != nil {
		return m.IsDisplay
	}
	return false
}

type UpsertTopicReq struct {
	TopicId              string   `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	TopicName            string   `protobuf:"bytes,2,opt,name=topic_name,json=topicName,proto3" json:"topic_name,omitempty"`
	DisplayTopicName     string   `protobuf:"bytes,3,opt,name=display_topic_name,json=displayTopicName,proto3" json:"display_topic_name,omitempty"`
	IsDisplay            bool     `protobuf:"varint,4,opt,name=is_display,json=isDisplay,proto3" json:"is_display,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertTopicReq) Reset()         { *m = UpsertTopicReq{} }
func (m *UpsertTopicReq) String() string { return proto.CompactTextString(m) }
func (*UpsertTopicReq) ProtoMessage()    {}
func (*UpsertTopicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{1}
}
func (m *UpsertTopicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertTopicReq.Unmarshal(m, b)
}
func (m *UpsertTopicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertTopicReq.Marshal(b, m, deterministic)
}
func (dst *UpsertTopicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertTopicReq.Merge(dst, src)
}
func (m *UpsertTopicReq) XXX_Size() int {
	return xxx_messageInfo_UpsertTopicReq.Size(m)
}
func (m *UpsertTopicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertTopicReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertTopicReq proto.InternalMessageInfo

func (m *UpsertTopicReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *UpsertTopicReq) GetTopicName() string {
	if m != nil {
		return m.TopicName
	}
	return ""
}

func (m *UpsertTopicReq) GetDisplayTopicName() string {
	if m != nil {
		return m.DisplayTopicName
	}
	return ""
}

func (m *UpsertTopicReq) GetIsDisplay() bool {
	if m != nil {
		return m.IsDisplay
	}
	return false
}

type UpsertTopicResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertTopicResp) Reset()         { *m = UpsertTopicResp{} }
func (m *UpsertTopicResp) String() string { return proto.CompactTextString(m) }
func (*UpsertTopicResp) ProtoMessage()    {}
func (*UpsertTopicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{2}
}
func (m *UpsertTopicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertTopicResp.Unmarshal(m, b)
}
func (m *UpsertTopicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertTopicResp.Marshal(b, m, deterministic)
}
func (dst *UpsertTopicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertTopicResp.Merge(dst, src)
}
func (m *UpsertTopicResp) XXX_Size() int {
	return xxx_messageInfo_UpsertTopicResp.Size(m)
}
func (m *UpsertTopicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertTopicResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertTopicResp proto.InternalMessageInfo

type GetTopicListReq struct {
	Page                 uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size                 uint32   `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	NeedCount            bool     `protobuf:"varint,3,opt,name=need_count,json=needCount,proto3" json:"need_count,omitempty"`
	TopicId              string   `protobuf:"bytes,4,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	TopicName            string   `protobuf:"bytes,5,opt,name=topic_name,json=topicName,proto3" json:"topic_name,omitempty"`
	DisplayTopicName     string   `protobuf:"bytes,6,opt,name=display_topic_name,json=displayTopicName,proto3" json:"display_topic_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTopicListReq) Reset()         { *m = GetTopicListReq{} }
func (m *GetTopicListReq) String() string { return proto.CompactTextString(m) }
func (*GetTopicListReq) ProtoMessage()    {}
func (*GetTopicListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{3}
}
func (m *GetTopicListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicListReq.Unmarshal(m, b)
}
func (m *GetTopicListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicListReq.Marshal(b, m, deterministic)
}
func (dst *GetTopicListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicListReq.Merge(dst, src)
}
func (m *GetTopicListReq) XXX_Size() int {
	return xxx_messageInfo_GetTopicListReq.Size(m)
}
func (m *GetTopicListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicListReq proto.InternalMessageInfo

func (m *GetTopicListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetTopicListReq) GetSize() uint32 {
	if m != nil {
		return m.Size
	}
	return 0
}

func (m *GetTopicListReq) GetNeedCount() bool {
	if m != nil {
		return m.NeedCount
	}
	return false
}

func (m *GetTopicListReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *GetTopicListReq) GetTopicName() string {
	if m != nil {
		return m.TopicName
	}
	return ""
}

func (m *GetTopicListReq) GetDisplayTopicName() string {
	if m != nil {
		return m.DisplayTopicName
	}
	return ""
}

type GetTopicListResp struct {
	TopicList            []*Topic `protobuf:"bytes,1,rep,name=topic_list,json=topicList,proto3" json:"topic_list,omitempty"`
	Total                uint32   `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTopicListResp) Reset()         { *m = GetTopicListResp{} }
func (m *GetTopicListResp) String() string { return proto.CompactTextString(m) }
func (*GetTopicListResp) ProtoMessage()    {}
func (*GetTopicListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{4}
}
func (m *GetTopicListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicListResp.Unmarshal(m, b)
}
func (m *GetTopicListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicListResp.Marshal(b, m, deterministic)
}
func (dst *GetTopicListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicListResp.Merge(dst, src)
}
func (m *GetTopicListResp) XXX_Size() int {
	return xxx_messageInfo_GetTopicListResp.Size(m)
}
func (m *GetTopicListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicListResp proto.InternalMessageInfo

func (m *GetTopicListResp) GetTopicList() []*Topic {
	if m != nil {
		return m.TopicList
	}
	return nil
}

func (m *GetTopicListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type DelTopicReq struct {
	TopicId              string   `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelTopicReq) Reset()         { *m = DelTopicReq{} }
func (m *DelTopicReq) String() string { return proto.CompactTextString(m) }
func (*DelTopicReq) ProtoMessage()    {}
func (*DelTopicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{5}
}
func (m *DelTopicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelTopicReq.Unmarshal(m, b)
}
func (m *DelTopicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelTopicReq.Marshal(b, m, deterministic)
}
func (dst *DelTopicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelTopicReq.Merge(dst, src)
}
func (m *DelTopicReq) XXX_Size() int {
	return xxx_messageInfo_DelTopicReq.Size(m)
}
func (m *DelTopicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelTopicReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelTopicReq proto.InternalMessageInfo

func (m *DelTopicReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

type DelTopicResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelTopicResp) Reset()         { *m = DelTopicResp{} }
func (m *DelTopicResp) String() string { return proto.CompactTextString(m) }
func (*DelTopicResp) ProtoMessage()    {}
func (*DelTopicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{6}
}
func (m *DelTopicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelTopicResp.Unmarshal(m, b)
}
func (m *DelTopicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelTopicResp.Marshal(b, m, deterministic)
}
func (dst *DelTopicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelTopicResp.Merge(dst, src)
}
func (m *DelTopicResp) XXX_Size() int {
	return xxx_messageInfo_DelTopicResp.Size(m)
}
func (m *DelTopicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelTopicResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelTopicResp proto.InternalMessageInfo

// 动态tab配置
type ConfigTab struct {
	ConfigTabId          string        `protobuf:"bytes,1,opt,name=config_tab_id,json=configTabId,proto3" json:"config_tab_id,omitempty"`
	ConfigTabName        string        `protobuf:"bytes,2,opt,name=config_tab_name,json=configTabName,proto3" json:"config_tab_name,omitempty"`
	CrowdType            CrowdType     `protobuf:"varint,3,opt,name=crowd_type,json=crowdType,proto3,enum=game_ugc_content.CrowdType" json:"crowd_type,omitempty"`
	CrowdGroupId         string        `protobuf:"bytes,4,opt,name=crowd_group_id,json=crowdGroupId,proto3" json:"crowd_group_id,omitempty"`
	DefaultTopic         *Topic        `protobuf:"bytes,5,opt,name=default_topic,json=defaultTopic,proto3" json:"default_topic,omitempty"`
	OtherTopics          []*Topic      `protobuf:"bytes,6,rep,name=other_topics,json=otherTopics,proto3" json:"other_topics,omitempty"`
	Titles               []string      `protobuf:"bytes,7,rep,name=titles,proto3" json:"titles,omitempty"`
	TabId                uint32        `protobuf:"varint,8,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	ConfigTabType        ConfigTabType `protobuf:"varint,9,opt,name=config_tab_type,json=configTabType,proto3,enum=game_ugc_content.ConfigTabType" json:"config_tab_type,omitempty"`
	CrowdGroupName       string        `protobuf:"bytes,10,opt,name=crowd_group_name,json=crowdGroupName,proto3" json:"crowd_group_name,omitempty"`
	CrowdGroupUserCount  uint32        `protobuf:"varint,11,opt,name=crowd_group_user_count,json=crowdGroupUserCount,proto3" json:"crowd_group_user_count,omitempty"`
	RecRules             []*RecRule    `protobuf:"bytes,12,rep,name=rec_rules,json=recRules,proto3" json:"rec_rules,omitempty"`
	ForceRecRule         uint32        `protobuf:"varint,13,opt,name=force_rec_rule,json=forceRecRule,proto3" json:"force_rec_rule,omitempty"`
	TabSubType           TabSubType    `protobuf:"varint,14,opt,name=tab_sub_type,json=tabSubType,proto3,enum=game_ugc_content.TabSubType" json:"tab_sub_type,omitempty"`
	IsHide               bool          `protobuf:"varint,15,opt,name=is_hide,json=isHide,proto3" json:"is_hide,omitempty"`
	PostTeleport         *PostTeleport `protobuf:"bytes,16,opt,name=post_teleport,json=postTeleport,proto3" json:"post_teleport,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ConfigTab) Reset()         { *m = ConfigTab{} }
func (m *ConfigTab) String() string { return proto.CompactTextString(m) }
func (*ConfigTab) ProtoMessage()    {}
func (*ConfigTab) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{7}
}
func (m *ConfigTab) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfigTab.Unmarshal(m, b)
}
func (m *ConfigTab) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfigTab.Marshal(b, m, deterministic)
}
func (dst *ConfigTab) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfigTab.Merge(dst, src)
}
func (m *ConfigTab) XXX_Size() int {
	return xxx_messageInfo_ConfigTab.Size(m)
}
func (m *ConfigTab) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfigTab.DiscardUnknown(m)
}

var xxx_messageInfo_ConfigTab proto.InternalMessageInfo

func (m *ConfigTab) GetConfigTabId() string {
	if m != nil {
		return m.ConfigTabId
	}
	return ""
}

func (m *ConfigTab) GetConfigTabName() string {
	if m != nil {
		return m.ConfigTabName
	}
	return ""
}

func (m *ConfigTab) GetCrowdType() CrowdType {
	if m != nil {
		return m.CrowdType
	}
	return CrowdType_CrowdTypeNone
}

func (m *ConfigTab) GetCrowdGroupId() string {
	if m != nil {
		return m.CrowdGroupId
	}
	return ""
}

func (m *ConfigTab) GetDefaultTopic() *Topic {
	if m != nil {
		return m.DefaultTopic
	}
	return nil
}

func (m *ConfigTab) GetOtherTopics() []*Topic {
	if m != nil {
		return m.OtherTopics
	}
	return nil
}

func (m *ConfigTab) GetTitles() []string {
	if m != nil {
		return m.Titles
	}
	return nil
}

func (m *ConfigTab) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *ConfigTab) GetConfigTabType() ConfigTabType {
	if m != nil {
		return m.ConfigTabType
	}
	return ConfigTabType_ConfigTabType_None
}

func (m *ConfigTab) GetCrowdGroupName() string {
	if m != nil {
		return m.CrowdGroupName
	}
	return ""
}

func (m *ConfigTab) GetCrowdGroupUserCount() uint32 {
	if m != nil {
		return m.CrowdGroupUserCount
	}
	return 0
}

func (m *ConfigTab) GetRecRules() []*RecRule {
	if m != nil {
		return m.RecRules
	}
	return nil
}

func (m *ConfigTab) GetForceRecRule() uint32 {
	if m != nil {
		return m.ForceRecRule
	}
	return 0
}

func (m *ConfigTab) GetTabSubType() TabSubType {
	if m != nil {
		return m.TabSubType
	}
	return TabSubType_GAME
}

func (m *ConfigTab) GetIsHide() bool {
	if m != nil {
		return m.IsHide
	}
	return false
}

func (m *ConfigTab) GetPostTeleport() *PostTeleport {
	if m != nil {
		return m.PostTeleport
	}
	return nil
}

type RecRule struct {
	RecRules             uint32   `protobuf:"varint,1,opt,name=rec_rules,json=recRules,proto3" json:"rec_rules,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecRule) Reset()         { *m = RecRule{} }
func (m *RecRule) String() string { return proto.CompactTextString(m) }
func (*RecRule) ProtoMessage()    {}
func (*RecRule) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{8}
}
func (m *RecRule) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecRule.Unmarshal(m, b)
}
func (m *RecRule) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecRule.Marshal(b, m, deterministic)
}
func (dst *RecRule) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecRule.Merge(dst, src)
}
func (m *RecRule) XXX_Size() int {
	return xxx_messageInfo_RecRule.Size(m)
}
func (m *RecRule) XXX_DiscardUnknown() {
	xxx_messageInfo_RecRule.DiscardUnknown(m)
}

var xxx_messageInfo_RecRule proto.InternalMessageInfo

func (m *RecRule) GetRecRules() uint32 {
	if m != nil {
		return m.RecRules
	}
	return 0
}

func (m *RecRule) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type PostTeleport struct {
	Text                 string   `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	Url                  string   `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PostTeleport) Reset()         { *m = PostTeleport{} }
func (m *PostTeleport) String() string { return proto.CompactTextString(m) }
func (*PostTeleport) ProtoMessage()    {}
func (*PostTeleport) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{9}
}
func (m *PostTeleport) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostTeleport.Unmarshal(m, b)
}
func (m *PostTeleport) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostTeleport.Marshal(b, m, deterministic)
}
func (dst *PostTeleport) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostTeleport.Merge(dst, src)
}
func (m *PostTeleport) XXX_Size() int {
	return xxx_messageInfo_PostTeleport.Size(m)
}
func (m *PostTeleport) XXX_DiscardUnknown() {
	xxx_messageInfo_PostTeleport.DiscardUnknown(m)
}

var xxx_messageInfo_PostTeleport proto.InternalMessageInfo

func (m *PostTeleport) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *PostTeleport) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

type UpsertConfigTabReq struct {
	Config               *ConfigTab    `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	Source               RequestSource `protobuf:"varint,2,opt,name=source,proto3,enum=game_ugc_content.RequestSource" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UpsertConfigTabReq) Reset()         { *m = UpsertConfigTabReq{} }
func (m *UpsertConfigTabReq) String() string { return proto.CompactTextString(m) }
func (*UpsertConfigTabReq) ProtoMessage()    {}
func (*UpsertConfigTabReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{10}
}
func (m *UpsertConfigTabReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertConfigTabReq.Unmarshal(m, b)
}
func (m *UpsertConfigTabReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertConfigTabReq.Marshal(b, m, deterministic)
}
func (dst *UpsertConfigTabReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertConfigTabReq.Merge(dst, src)
}
func (m *UpsertConfigTabReq) XXX_Size() int {
	return xxx_messageInfo_UpsertConfigTabReq.Size(m)
}
func (m *UpsertConfigTabReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertConfigTabReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertConfigTabReq proto.InternalMessageInfo

func (m *UpsertConfigTabReq) GetConfig() *ConfigTab {
	if m != nil {
		return m.Config
	}
	return nil
}

func (m *UpsertConfigTabReq) GetSource() RequestSource {
	if m != nil {
		return m.Source
	}
	return RequestSource_RequestSource_None
}

type UpsertConfigTabResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertConfigTabResp) Reset()         { *m = UpsertConfigTabResp{} }
func (m *UpsertConfigTabResp) String() string { return proto.CompactTextString(m) }
func (*UpsertConfigTabResp) ProtoMessage()    {}
func (*UpsertConfigTabResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{11}
}
func (m *UpsertConfigTabResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertConfigTabResp.Unmarshal(m, b)
}
func (m *UpsertConfigTabResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertConfigTabResp.Marshal(b, m, deterministic)
}
func (dst *UpsertConfigTabResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertConfigTabResp.Merge(dst, src)
}
func (m *UpsertConfigTabResp) XXX_Size() int {
	return xxx_messageInfo_UpsertConfigTabResp.Size(m)
}
func (m *UpsertConfigTabResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertConfigTabResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertConfigTabResp proto.InternalMessageInfo

type GetConfigTabsReq struct {
	Page                 uint32        `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size                 uint32        `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	NeedCount            bool          `protobuf:"varint,3,opt,name=need_count,json=needCount,proto3" json:"need_count,omitempty"`
	ConfigTabId          string        `protobuf:"bytes,4,opt,name=config_tab_id,json=configTabId,proto3" json:"config_tab_id,omitempty"`
	ConfigTabName        string        `protobuf:"bytes,5,opt,name=config_tab_name,json=configTabName,proto3" json:"config_tab_name,omitempty"`
	TopicId              string        `protobuf:"bytes,6,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	TabId                uint32        `protobuf:"varint,7,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Source               RequestSource `protobuf:"varint,8,opt,name=source,proto3,enum=game_ugc_content.RequestSource" json:"source,omitempty"`
	TeleportUrl          string        `protobuf:"bytes,9,opt,name=teleport_url,json=teleportUrl,proto3" json:"teleport_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetConfigTabsReq) Reset()         { *m = GetConfigTabsReq{} }
func (m *GetConfigTabsReq) String() string { return proto.CompactTextString(m) }
func (*GetConfigTabsReq) ProtoMessage()    {}
func (*GetConfigTabsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{12}
}
func (m *GetConfigTabsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConfigTabsReq.Unmarshal(m, b)
}
func (m *GetConfigTabsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConfigTabsReq.Marshal(b, m, deterministic)
}
func (dst *GetConfigTabsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConfigTabsReq.Merge(dst, src)
}
func (m *GetConfigTabsReq) XXX_Size() int {
	return xxx_messageInfo_GetConfigTabsReq.Size(m)
}
func (m *GetConfigTabsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConfigTabsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetConfigTabsReq proto.InternalMessageInfo

func (m *GetConfigTabsReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetConfigTabsReq) GetSize() uint32 {
	if m != nil {
		return m.Size
	}
	return 0
}

func (m *GetConfigTabsReq) GetNeedCount() bool {
	if m != nil {
		return m.NeedCount
	}
	return false
}

func (m *GetConfigTabsReq) GetConfigTabId() string {
	if m != nil {
		return m.ConfigTabId
	}
	return ""
}

func (m *GetConfigTabsReq) GetConfigTabName() string {
	if m != nil {
		return m.ConfigTabName
	}
	return ""
}

func (m *GetConfigTabsReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *GetConfigTabsReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetConfigTabsReq) GetSource() RequestSource {
	if m != nil {
		return m.Source
	}
	return RequestSource_RequestSource_None
}

func (m *GetConfigTabsReq) GetTeleportUrl() string {
	if m != nil {
		return m.TeleportUrl
	}
	return ""
}

type GetConfigTabsResp struct {
	Configs              []*ConfigTab `protobuf:"bytes,1,rep,name=configs,proto3" json:"configs,omitempty"`
	Total                uint32       `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetConfigTabsResp) Reset()         { *m = GetConfigTabsResp{} }
func (m *GetConfigTabsResp) String() string { return proto.CompactTextString(m) }
func (*GetConfigTabsResp) ProtoMessage()    {}
func (*GetConfigTabsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{13}
}
func (m *GetConfigTabsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConfigTabsResp.Unmarshal(m, b)
}
func (m *GetConfigTabsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConfigTabsResp.Marshal(b, m, deterministic)
}
func (dst *GetConfigTabsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConfigTabsResp.Merge(dst, src)
}
func (m *GetConfigTabsResp) XXX_Size() int {
	return xxx_messageInfo_GetConfigTabsResp.Size(m)
}
func (m *GetConfigTabsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConfigTabsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetConfigTabsResp proto.InternalMessageInfo

func (m *GetConfigTabsResp) GetConfigs() []*ConfigTab {
	if m != nil {
		return m.Configs
	}
	return nil
}

func (m *GetConfigTabsResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type DelConfigTabReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelConfigTabReq) Reset()         { *m = DelConfigTabReq{} }
func (m *DelConfigTabReq) String() string { return proto.CompactTextString(m) }
func (*DelConfigTabReq) ProtoMessage()    {}
func (*DelConfigTabReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{14}
}
func (m *DelConfigTabReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelConfigTabReq.Unmarshal(m, b)
}
func (m *DelConfigTabReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelConfigTabReq.Marshal(b, m, deterministic)
}
func (dst *DelConfigTabReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelConfigTabReq.Merge(dst, src)
}
func (m *DelConfigTabReq) XXX_Size() int {
	return xxx_messageInfo_DelConfigTabReq.Size(m)
}
func (m *DelConfigTabReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelConfigTabReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelConfigTabReq proto.InternalMessageInfo

func (m *DelConfigTabReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type DelConfigTabResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelConfigTabResp) Reset()         { *m = DelConfigTabResp{} }
func (m *DelConfigTabResp) String() string { return proto.CompactTextString(m) }
func (*DelConfigTabResp) ProtoMessage()    {}
func (*DelConfigTabResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{15}
}
func (m *DelConfigTabResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelConfigTabResp.Unmarshal(m, b)
}
func (m *DelConfigTabResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelConfigTabResp.Marshal(b, m, deterministic)
}
func (dst *DelConfigTabResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelConfigTabResp.Merge(dst, src)
}
func (m *DelConfigTabResp) XXX_Size() int {
	return xxx_messageInfo_DelConfigTabResp.Size(m)
}
func (m *DelConfigTabResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelConfigTabResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelConfigTabResp proto.InternalMessageInfo

type GameConfigTab struct {
	TabId                uint32       `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	ConfigTabs           []*ConfigTab `protobuf:"bytes,2,rep,name=config_tabs,json=configTabs,proto3" json:"config_tabs,omitempty"`
	TabSubType           TabSubType   `protobuf:"varint,3,opt,name=tab_sub_type,json=tabSubType,proto3,enum=game_ugc_content.TabSubType" json:"tab_sub_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GameConfigTab) Reset()         { *m = GameConfigTab{} }
func (m *GameConfigTab) String() string { return proto.CompactTextString(m) }
func (*GameConfigTab) ProtoMessage()    {}
func (*GameConfigTab) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{16}
}
func (m *GameConfigTab) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameConfigTab.Unmarshal(m, b)
}
func (m *GameConfigTab) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameConfigTab.Marshal(b, m, deterministic)
}
func (dst *GameConfigTab) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameConfigTab.Merge(dst, src)
}
func (m *GameConfigTab) XXX_Size() int {
	return xxx_messageInfo_GameConfigTab.Size(m)
}
func (m *GameConfigTab) XXX_DiscardUnknown() {
	xxx_messageInfo_GameConfigTab.DiscardUnknown(m)
}

var xxx_messageInfo_GameConfigTab proto.InternalMessageInfo

func (m *GameConfigTab) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GameConfigTab) GetConfigTabs() []*ConfigTab {
	if m != nil {
		return m.ConfigTabs
	}
	return nil
}

func (m *GameConfigTab) GetTabSubType() TabSubType {
	if m != nil {
		return m.TabSubType
	}
	return TabSubType_GAME
}

type SaveGameConfigTabReq struct {
	GameConfigTab        *GameConfigTab `protobuf:"bytes,1,opt,name=game_config_tab,json=gameConfigTab,proto3" json:"game_config_tab,omitempty"`
	TabSubType           TabSubType     `protobuf:"varint,2,opt,name=tab_sub_type,json=tabSubType,proto3,enum=game_ugc_content.TabSubType" json:"tab_sub_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *SaveGameConfigTabReq) Reset()         { *m = SaveGameConfigTabReq{} }
func (m *SaveGameConfigTabReq) String() string { return proto.CompactTextString(m) }
func (*SaveGameConfigTabReq) ProtoMessage()    {}
func (*SaveGameConfigTabReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{17}
}
func (m *SaveGameConfigTabReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveGameConfigTabReq.Unmarshal(m, b)
}
func (m *SaveGameConfigTabReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveGameConfigTabReq.Marshal(b, m, deterministic)
}
func (dst *SaveGameConfigTabReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveGameConfigTabReq.Merge(dst, src)
}
func (m *SaveGameConfigTabReq) XXX_Size() int {
	return xxx_messageInfo_SaveGameConfigTabReq.Size(m)
}
func (m *SaveGameConfigTabReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveGameConfigTabReq.DiscardUnknown(m)
}

var xxx_messageInfo_SaveGameConfigTabReq proto.InternalMessageInfo

func (m *SaveGameConfigTabReq) GetGameConfigTab() *GameConfigTab {
	if m != nil {
		return m.GameConfigTab
	}
	return nil
}

func (m *SaveGameConfigTabReq) GetTabSubType() TabSubType {
	if m != nil {
		return m.TabSubType
	}
	return TabSubType_GAME
}

type SaveGameConfigTabResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SaveGameConfigTabResp) Reset()         { *m = SaveGameConfigTabResp{} }
func (m *SaveGameConfigTabResp) String() string { return proto.CompactTextString(m) }
func (*SaveGameConfigTabResp) ProtoMessage()    {}
func (*SaveGameConfigTabResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{18}
}
func (m *SaveGameConfigTabResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveGameConfigTabResp.Unmarshal(m, b)
}
func (m *SaveGameConfigTabResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveGameConfigTabResp.Marshal(b, m, deterministic)
}
func (dst *SaveGameConfigTabResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveGameConfigTabResp.Merge(dst, src)
}
func (m *SaveGameConfigTabResp) XXX_Size() int {
	return xxx_messageInfo_SaveGameConfigTabResp.Size(m)
}
func (m *SaveGameConfigTabResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveGameConfigTabResp.DiscardUnknown(m)
}

var xxx_messageInfo_SaveGameConfigTabResp proto.InternalMessageInfo

type GetGameConfigTabListReq struct {
	TabId                uint32     `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabSubType           TabSubType `protobuf:"varint,2,opt,name=tab_sub_type,json=tabSubType,proto3,enum=game_ugc_content.TabSubType" json:"tab_sub_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetGameConfigTabListReq) Reset()         { *m = GetGameConfigTabListReq{} }
func (m *GetGameConfigTabListReq) String() string { return proto.CompactTextString(m) }
func (*GetGameConfigTabListReq) ProtoMessage()    {}
func (*GetGameConfigTabListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{19}
}
func (m *GetGameConfigTabListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameConfigTabListReq.Unmarshal(m, b)
}
func (m *GetGameConfigTabListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameConfigTabListReq.Marshal(b, m, deterministic)
}
func (dst *GetGameConfigTabListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameConfigTabListReq.Merge(dst, src)
}
func (m *GetGameConfigTabListReq) XXX_Size() int {
	return xxx_messageInfo_GetGameConfigTabListReq.Size(m)
}
func (m *GetGameConfigTabListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameConfigTabListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameConfigTabListReq proto.InternalMessageInfo

func (m *GetGameConfigTabListReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetGameConfigTabListReq) GetTabSubType() TabSubType {
	if m != nil {
		return m.TabSubType
	}
	return TabSubType_GAME
}

type GetGameConfigTabListResp struct {
	GameConfigTabList    []*GameConfigTab `protobuf:"bytes,1,rep,name=game_config_tab_list,json=gameConfigTabList,proto3" json:"game_config_tab_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetGameConfigTabListResp) Reset()         { *m = GetGameConfigTabListResp{} }
func (m *GetGameConfigTabListResp) String() string { return proto.CompactTextString(m) }
func (*GetGameConfigTabListResp) ProtoMessage()    {}
func (*GetGameConfigTabListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{20}
}
func (m *GetGameConfigTabListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameConfigTabListResp.Unmarshal(m, b)
}
func (m *GetGameConfigTabListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameConfigTabListResp.Marshal(b, m, deterministic)
}
func (dst *GetGameConfigTabListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameConfigTabListResp.Merge(dst, src)
}
func (m *GetGameConfigTabListResp) XXX_Size() int {
	return xxx_messageInfo_GetGameConfigTabListResp.Size(m)
}
func (m *GetGameConfigTabListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameConfigTabListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameConfigTabListResp proto.InternalMessageInfo

func (m *GetGameConfigTabListResp) GetGameConfigTabList() []*GameConfigTab {
	if m != nil {
		return m.GameConfigTabList
	}
	return nil
}

type DelGameConfigTabReq struct {
	TabId                uint32     `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabSubType           TabSubType `protobuf:"varint,2,opt,name=tab_sub_type,json=tabSubType,proto3,enum=game_ugc_content.TabSubType" json:"tab_sub_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *DelGameConfigTabReq) Reset()         { *m = DelGameConfigTabReq{} }
func (m *DelGameConfigTabReq) String() string { return proto.CompactTextString(m) }
func (*DelGameConfigTabReq) ProtoMessage()    {}
func (*DelGameConfigTabReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{21}
}
func (m *DelGameConfigTabReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelGameConfigTabReq.Unmarshal(m, b)
}
func (m *DelGameConfigTabReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelGameConfigTabReq.Marshal(b, m, deterministic)
}
func (dst *DelGameConfigTabReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelGameConfigTabReq.Merge(dst, src)
}
func (m *DelGameConfigTabReq) XXX_Size() int {
	return xxx_messageInfo_DelGameConfigTabReq.Size(m)
}
func (m *DelGameConfigTabReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelGameConfigTabReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelGameConfigTabReq proto.InternalMessageInfo

func (m *DelGameConfigTabReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *DelGameConfigTabReq) GetTabSubType() TabSubType {
	if m != nil {
		return m.TabSubType
	}
	return TabSubType_GAME
}

type DelGameConfigTabResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelGameConfigTabResp) Reset()         { *m = DelGameConfigTabResp{} }
func (m *DelGameConfigTabResp) String() string { return proto.CompactTextString(m) }
func (*DelGameConfigTabResp) ProtoMessage()    {}
func (*DelGameConfigTabResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{22}
}
func (m *DelGameConfigTabResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelGameConfigTabResp.Unmarshal(m, b)
}
func (m *DelGameConfigTabResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelGameConfigTabResp.Marshal(b, m, deterministic)
}
func (dst *DelGameConfigTabResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelGameConfigTabResp.Merge(dst, src)
}
func (m *DelGameConfigTabResp) XXX_Size() int {
	return xxx_messageInfo_DelGameConfigTabResp.Size(m)
}
func (m *DelGameConfigTabResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelGameConfigTabResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelGameConfigTabResp proto.InternalMessageInfo

type GameConfigTabDetail struct {
	TabId                uint32           `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	ConfigTabs           []*ConfigTabInfo `protobuf:"bytes,2,rep,name=config_tabs,json=configTabs,proto3" json:"config_tabs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GameConfigTabDetail) Reset()         { *m = GameConfigTabDetail{} }
func (m *GameConfigTabDetail) String() string { return proto.CompactTextString(m) }
func (*GameConfigTabDetail) ProtoMessage()    {}
func (*GameConfigTabDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{23}
}
func (m *GameConfigTabDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameConfigTabDetail.Unmarshal(m, b)
}
func (m *GameConfigTabDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameConfigTabDetail.Marshal(b, m, deterministic)
}
func (dst *GameConfigTabDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameConfigTabDetail.Merge(dst, src)
}
func (m *GameConfigTabDetail) XXX_Size() int {
	return xxx_messageInfo_GameConfigTabDetail.Size(m)
}
func (m *GameConfigTabDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_GameConfigTabDetail.DiscardUnknown(m)
}

var xxx_messageInfo_GameConfigTabDetail proto.InternalMessageInfo

func (m *GameConfigTabDetail) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GameConfigTabDetail) GetConfigTabs() []*ConfigTabInfo {
	if m != nil {
		return m.ConfigTabs
	}
	return nil
}

type GetGameConfigTabDetailByTabIdReq struct {
	TabId                uint32     `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabSubType           TabSubType `protobuf:"varint,2,opt,name=tab_sub_type,json=tabSubType,proto3,enum=game_ugc_content.TabSubType" json:"tab_sub_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetGameConfigTabDetailByTabIdReq) Reset()         { *m = GetGameConfigTabDetailByTabIdReq{} }
func (m *GetGameConfigTabDetailByTabIdReq) String() string { return proto.CompactTextString(m) }
func (*GetGameConfigTabDetailByTabIdReq) ProtoMessage()    {}
func (*GetGameConfigTabDetailByTabIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{24}
}
func (m *GetGameConfigTabDetailByTabIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameConfigTabDetailByTabIdReq.Unmarshal(m, b)
}
func (m *GetGameConfigTabDetailByTabIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameConfigTabDetailByTabIdReq.Marshal(b, m, deterministic)
}
func (dst *GetGameConfigTabDetailByTabIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameConfigTabDetailByTabIdReq.Merge(dst, src)
}
func (m *GetGameConfigTabDetailByTabIdReq) XXX_Size() int {
	return xxx_messageInfo_GetGameConfigTabDetailByTabIdReq.Size(m)
}
func (m *GetGameConfigTabDetailByTabIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameConfigTabDetailByTabIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameConfigTabDetailByTabIdReq proto.InternalMessageInfo

func (m *GetGameConfigTabDetailByTabIdReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetGameConfigTabDetailByTabIdReq) GetTabSubType() TabSubType {
	if m != nil {
		return m.TabSubType
	}
	return TabSubType_GAME
}

type GetGameConfigTabDetailByTabIdResp struct {
	Config               *GameConfigTabDetail `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetGameConfigTabDetailByTabIdResp) Reset()         { *m = GetGameConfigTabDetailByTabIdResp{} }
func (m *GetGameConfigTabDetailByTabIdResp) String() string { return proto.CompactTextString(m) }
func (*GetGameConfigTabDetailByTabIdResp) ProtoMessage()    {}
func (*GetGameConfigTabDetailByTabIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{25}
}
func (m *GetGameConfigTabDetailByTabIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameConfigTabDetailByTabIdResp.Unmarshal(m, b)
}
func (m *GetGameConfigTabDetailByTabIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameConfigTabDetailByTabIdResp.Marshal(b, m, deterministic)
}
func (dst *GetGameConfigTabDetailByTabIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameConfigTabDetailByTabIdResp.Merge(dst, src)
}
func (m *GetGameConfigTabDetailByTabIdResp) XXX_Size() int {
	return xxx_messageInfo_GetGameConfigTabDetailByTabIdResp.Size(m)
}
func (m *GetGameConfigTabDetailByTabIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameConfigTabDetailByTabIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameConfigTabDetailByTabIdResp proto.InternalMessageInfo

func (m *GetGameConfigTabDetailByTabIdResp) GetConfig() *GameConfigTabDetail {
	if m != nil {
		return m.Config
	}
	return nil
}

type ConfigTabInfo struct {
	ConfigTabId          string        `protobuf:"bytes,1,opt,name=config_tab_id,json=configTabId,proto3" json:"config_tab_id,omitempty"`
	ConfigTabName        string        `protobuf:"bytes,2,opt,name=config_tab_name,json=configTabName,proto3" json:"config_tab_name,omitempty"`
	CrowdType            CrowdType     `protobuf:"varint,3,opt,name=crowd_type,json=crowdType,proto3,enum=game_ugc_content.CrowdType" json:"crowd_type,omitempty"`
	CrowdGroupId         string        `protobuf:"bytes,4,opt,name=crowd_group_id,json=crowdGroupId,proto3" json:"crowd_group_id,omitempty"`
	DefaultTopicId       string        `protobuf:"bytes,5,opt,name=default_topic_id,json=defaultTopicId,proto3" json:"default_topic_id,omitempty"`
	OtherTopicIds        []string      `protobuf:"bytes,6,rep,name=other_topic_ids,json=otherTopicIds,proto3" json:"other_topic_ids,omitempty"`
	Titles               []string      `protobuf:"bytes,7,rep,name=titles,proto3" json:"titles,omitempty"`
	ConfigTabType        ConfigTabType `protobuf:"varint,8,opt,name=config_tab_type,json=configTabType,proto3,enum=game_ugc_content.ConfigTabType" json:"config_tab_type,omitempty"`
	RecRules             []*RecRule    `protobuf:"bytes,9,rep,name=rec_rules,json=recRules,proto3" json:"rec_rules,omitempty"`
	ForceRecRule         uint32        `protobuf:"varint,10,opt,name=force_rec_rule,json=forceRecRule,proto3" json:"force_rec_rule,omitempty"`
	PostTeleport         *PostTeleport `protobuf:"bytes,11,opt,name=post_teleport,json=postTeleport,proto3" json:"post_teleport,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ConfigTabInfo) Reset()         { *m = ConfigTabInfo{} }
func (m *ConfigTabInfo) String() string { return proto.CompactTextString(m) }
func (*ConfigTabInfo) ProtoMessage()    {}
func (*ConfigTabInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{26}
}
func (m *ConfigTabInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfigTabInfo.Unmarshal(m, b)
}
func (m *ConfigTabInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfigTabInfo.Marshal(b, m, deterministic)
}
func (dst *ConfigTabInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfigTabInfo.Merge(dst, src)
}
func (m *ConfigTabInfo) XXX_Size() int {
	return xxx_messageInfo_ConfigTabInfo.Size(m)
}
func (m *ConfigTabInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfigTabInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ConfigTabInfo proto.InternalMessageInfo

func (m *ConfigTabInfo) GetConfigTabId() string {
	if m != nil {
		return m.ConfigTabId
	}
	return ""
}

func (m *ConfigTabInfo) GetConfigTabName() string {
	if m != nil {
		return m.ConfigTabName
	}
	return ""
}

func (m *ConfigTabInfo) GetCrowdType() CrowdType {
	if m != nil {
		return m.CrowdType
	}
	return CrowdType_CrowdTypeNone
}

func (m *ConfigTabInfo) GetCrowdGroupId() string {
	if m != nil {
		return m.CrowdGroupId
	}
	return ""
}

func (m *ConfigTabInfo) GetDefaultTopicId() string {
	if m != nil {
		return m.DefaultTopicId
	}
	return ""
}

func (m *ConfigTabInfo) GetOtherTopicIds() []string {
	if m != nil {
		return m.OtherTopicIds
	}
	return nil
}

func (m *ConfigTabInfo) GetTitles() []string {
	if m != nil {
		return m.Titles
	}
	return nil
}

func (m *ConfigTabInfo) GetConfigTabType() ConfigTabType {
	if m != nil {
		return m.ConfigTabType
	}
	return ConfigTabType_ConfigTabType_None
}

func (m *ConfigTabInfo) GetRecRules() []*RecRule {
	if m != nil {
		return m.RecRules
	}
	return nil
}

func (m *ConfigTabInfo) GetForceRecRule() uint32 {
	if m != nil {
		return m.ForceRecRule
	}
	return 0
}

func (m *ConfigTabInfo) GetPostTeleport() *PostTeleport {
	if m != nil {
		return m.PostTeleport
	}
	return nil
}

type GetConfigTabInfoByIdReq struct {
	ConfigTabId          string   `protobuf:"bytes,1,opt,name=config_tab_id,json=configTabId,proto3" json:"config_tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetConfigTabInfoByIdReq) Reset()         { *m = GetConfigTabInfoByIdReq{} }
func (m *GetConfigTabInfoByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetConfigTabInfoByIdReq) ProtoMessage()    {}
func (*GetConfigTabInfoByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{27}
}
func (m *GetConfigTabInfoByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConfigTabInfoByIdReq.Unmarshal(m, b)
}
func (m *GetConfigTabInfoByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConfigTabInfoByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetConfigTabInfoByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConfigTabInfoByIdReq.Merge(dst, src)
}
func (m *GetConfigTabInfoByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetConfigTabInfoByIdReq.Size(m)
}
func (m *GetConfigTabInfoByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConfigTabInfoByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetConfigTabInfoByIdReq proto.InternalMessageInfo

func (m *GetConfigTabInfoByIdReq) GetConfigTabId() string {
	if m != nil {
		return m.ConfigTabId
	}
	return ""
}

type GetConfigTabInfoByIdResp struct {
	ConfigTabInfo        *ConfigTabInfo `protobuf:"bytes,1,opt,name=config_tab_info,json=configTabInfo,proto3" json:"config_tab_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetConfigTabInfoByIdResp) Reset()         { *m = GetConfigTabInfoByIdResp{} }
func (m *GetConfigTabInfoByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetConfigTabInfoByIdResp) ProtoMessage()    {}
func (*GetConfigTabInfoByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{28}
}
func (m *GetConfigTabInfoByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConfigTabInfoByIdResp.Unmarshal(m, b)
}
func (m *GetConfigTabInfoByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConfigTabInfoByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetConfigTabInfoByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConfigTabInfoByIdResp.Merge(dst, src)
}
func (m *GetConfigTabInfoByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetConfigTabInfoByIdResp.Size(m)
}
func (m *GetConfigTabInfoByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConfigTabInfoByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetConfigTabInfoByIdResp proto.InternalMessageInfo

func (m *GetConfigTabInfoByIdResp) GetConfigTabInfo() *ConfigTabInfo {
	if m != nil {
		return m.ConfigTabInfo
	}
	return nil
}

// 对应开黑专区发帖上报的业务数据
type GamePostBussInfo struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	ConfigTabId          string   `protobuf:"bytes,2,opt,name=config_tab_id,json=configTabId,proto3" json:"config_tab_id,omitempty"`
	Title                string   `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GamePostBussInfo) Reset()         { *m = GamePostBussInfo{} }
func (m *GamePostBussInfo) String() string { return proto.CompactTextString(m) }
func (*GamePostBussInfo) ProtoMessage()    {}
func (*GamePostBussInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{29}
}
func (m *GamePostBussInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GamePostBussInfo.Unmarshal(m, b)
}
func (m *GamePostBussInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GamePostBussInfo.Marshal(b, m, deterministic)
}
func (dst *GamePostBussInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GamePostBussInfo.Merge(dst, src)
}
func (m *GamePostBussInfo) XXX_Size() int {
	return xxx_messageInfo_GamePostBussInfo.Size(m)
}
func (m *GamePostBussInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GamePostBussInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GamePostBussInfo proto.InternalMessageInfo

func (m *GamePostBussInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GamePostBussInfo) GetConfigTabId() string {
	if m != nil {
		return m.ConfigTabId
	}
	return ""
}

func (m *GamePostBussInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

type GetGamePalTabsReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGamePalTabsReq) Reset()         { *m = GetGamePalTabsReq{} }
func (m *GetGamePalTabsReq) String() string { return proto.CompactTextString(m) }
func (*GetGamePalTabsReq) ProtoMessage()    {}
func (*GetGamePalTabsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{30}
}
func (m *GetGamePalTabsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGamePalTabsReq.Unmarshal(m, b)
}
func (m *GetGamePalTabsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGamePalTabsReq.Marshal(b, m, deterministic)
}
func (dst *GetGamePalTabsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGamePalTabsReq.Merge(dst, src)
}
func (m *GetGamePalTabsReq) XXX_Size() int {
	return xxx_messageInfo_GetGamePalTabsReq.Size(m)
}
func (m *GetGamePalTabsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGamePalTabsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGamePalTabsReq proto.InternalMessageInfo

type GetGamePalTabsResp struct {
	GamePalTabs          map[uint32]*ConfigTabInfo `protobuf:"bytes,1,rep,name=game_pal_tabs,json=gamePalTabs,proto3" json:"game_pal_tabs,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetGamePalTabsResp) Reset()         { *m = GetGamePalTabsResp{} }
func (m *GetGamePalTabsResp) String() string { return proto.CompactTextString(m) }
func (*GetGamePalTabsResp) ProtoMessage()    {}
func (*GetGamePalTabsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{31}
}
func (m *GetGamePalTabsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGamePalTabsResp.Unmarshal(m, b)
}
func (m *GetGamePalTabsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGamePalTabsResp.Marshal(b, m, deterministic)
}
func (dst *GetGamePalTabsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGamePalTabsResp.Merge(dst, src)
}
func (m *GetGamePalTabsResp) XXX_Size() int {
	return xxx_messageInfo_GetGamePalTabsResp.Size(m)
}
func (m *GetGamePalTabsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGamePalTabsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGamePalTabsResp proto.InternalMessageInfo

func (m *GetGamePalTabsResp) GetGamePalTabs() map[uint32]*ConfigTabInfo {
	if m != nil {
		return m.GamePalTabs
	}
	return nil
}

type BanUserPostConfig struct {
	Id                   string        `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ConfigType           BanConfigType `protobuf:"varint,2,opt,name=config_type,json=configType,proto3,enum=game_ugc_content.BanConfigType" json:"config_type,omitempty"`
	Ttid                 string        `protobuf:"bytes,3,opt,name=ttid,proto3" json:"ttid,omitempty"`
	CrowdGroupId         string        `protobuf:"bytes,4,opt,name=crowd_group_id,json=crowdGroupId,proto3" json:"crowd_group_id,omitempty"`
	TabIds               []uint32      `protobuf:"varint,5,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	StartTime            int64         `protobuf:"varint,6,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              int64         `protobuf:"varint,7,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	BanReason            string        `protobuf:"bytes,8,opt,name=ban_reason,json=banReason,proto3" json:"ban_reason,omitempty"`
	OperateTime          int64         `protobuf:"varint,9,opt,name=operate_time,json=operateTime,proto3" json:"operate_time,omitempty"`
	Operator             string        `protobuf:"bytes,10,opt,name=operator,proto3" json:"operator,omitempty"`
	Status               Status        `protobuf:"varint,11,opt,name=status,proto3,enum=game_ugc_content.Status" json:"status,omitempty"`
	CrowdGroupName       string        `protobuf:"bytes,12,opt,name=crowd_group_name,json=crowdGroupName,proto3" json:"crowd_group_name,omitempty"`
	CrowdGroupUserCount  uint32        `protobuf:"varint,13,opt,name=crowd_group_user_count,json=crowdGroupUserCount,proto3" json:"crowd_group_user_count,omitempty"`
	UserNickname         string        `protobuf:"bytes,14,opt,name=user_nickname,json=userNickname,proto3" json:"user_nickname,omitempty"`
	BanPostType          BanPostType   `protobuf:"varint,15,opt,name=ban_post_type,json=banPostType,proto3,enum=game_ugc_content.BanPostType" json:"ban_post_type,omitempty"`
	Uid                  uint32        `protobuf:"varint,16,opt,name=uid,proto3" json:"uid,omitempty"`
	WarningMessage       string        `protobuf:"bytes,17,opt,name=warning_message,json=warningMessage,proto3" json:"warning_message,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *BanUserPostConfig) Reset()         { *m = BanUserPostConfig{} }
func (m *BanUserPostConfig) String() string { return proto.CompactTextString(m) }
func (*BanUserPostConfig) ProtoMessage()    {}
func (*BanUserPostConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{32}
}
func (m *BanUserPostConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BanUserPostConfig.Unmarshal(m, b)
}
func (m *BanUserPostConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BanUserPostConfig.Marshal(b, m, deterministic)
}
func (dst *BanUserPostConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BanUserPostConfig.Merge(dst, src)
}
func (m *BanUserPostConfig) XXX_Size() int {
	return xxx_messageInfo_BanUserPostConfig.Size(m)
}
func (m *BanUserPostConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_BanUserPostConfig.DiscardUnknown(m)
}

var xxx_messageInfo_BanUserPostConfig proto.InternalMessageInfo

func (m *BanUserPostConfig) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *BanUserPostConfig) GetConfigType() BanConfigType {
	if m != nil {
		return m.ConfigType
	}
	return BanConfigType_BanConfigTypeNone
}

func (m *BanUserPostConfig) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *BanUserPostConfig) GetCrowdGroupId() string {
	if m != nil {
		return m.CrowdGroupId
	}
	return ""
}

func (m *BanUserPostConfig) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

func (m *BanUserPostConfig) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *BanUserPostConfig) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *BanUserPostConfig) GetBanReason() string {
	if m != nil {
		return m.BanReason
	}
	return ""
}

func (m *BanUserPostConfig) GetOperateTime() int64 {
	if m != nil {
		return m.OperateTime
	}
	return 0
}

func (m *BanUserPostConfig) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *BanUserPostConfig) GetStatus() Status {
	if m != nil {
		return m.Status
	}
	return Status_StatusNone
}

func (m *BanUserPostConfig) GetCrowdGroupName() string {
	if m != nil {
		return m.CrowdGroupName
	}
	return ""
}

func (m *BanUserPostConfig) GetCrowdGroupUserCount() uint32 {
	if m != nil {
		return m.CrowdGroupUserCount
	}
	return 0
}

func (m *BanUserPostConfig) GetUserNickname() string {
	if m != nil {
		return m.UserNickname
	}
	return ""
}

func (m *BanUserPostConfig) GetBanPostType() BanPostType {
	if m != nil {
		return m.BanPostType
	}
	return BanPostType_BanPostTypePost
}

func (m *BanUserPostConfig) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BanUserPostConfig) GetWarningMessage() string {
	if m != nil {
		return m.WarningMessage
	}
	return ""
}

type BatchAddBanUserPostConfigReq struct {
	Ttids                []string      `protobuf:"bytes,1,rep,name=ttids,proto3" json:"ttids,omitempty"`
	StartTime            int64         `protobuf:"varint,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              int64         `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	BanReason            string        `protobuf:"bytes,4,opt,name=ban_reason,json=banReason,proto3" json:"ban_reason,omitempty"`
	WarningMessage       string        `protobuf:"bytes,5,opt,name=warning_message,json=warningMessage,proto3" json:"warning_message,omitempty"`
	BanPostTypes         []BanPostType `protobuf:"varint,6,rep,packed,name=ban_post_types,json=banPostTypes,proto3,enum=game_ugc_content.BanPostType" json:"ban_post_types,omitempty"`
	Operator             string        `protobuf:"bytes,7,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *BatchAddBanUserPostConfigReq) Reset()         { *m = BatchAddBanUserPostConfigReq{} }
func (m *BatchAddBanUserPostConfigReq) String() string { return proto.CompactTextString(m) }
func (*BatchAddBanUserPostConfigReq) ProtoMessage()    {}
func (*BatchAddBanUserPostConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{33}
}
func (m *BatchAddBanUserPostConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddBanUserPostConfigReq.Unmarshal(m, b)
}
func (m *BatchAddBanUserPostConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddBanUserPostConfigReq.Marshal(b, m, deterministic)
}
func (dst *BatchAddBanUserPostConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddBanUserPostConfigReq.Merge(dst, src)
}
func (m *BatchAddBanUserPostConfigReq) XXX_Size() int {
	return xxx_messageInfo_BatchAddBanUserPostConfigReq.Size(m)
}
func (m *BatchAddBanUserPostConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddBanUserPostConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddBanUserPostConfigReq proto.InternalMessageInfo

func (m *BatchAddBanUserPostConfigReq) GetTtids() []string {
	if m != nil {
		return m.Ttids
	}
	return nil
}

func (m *BatchAddBanUserPostConfigReq) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *BatchAddBanUserPostConfigReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *BatchAddBanUserPostConfigReq) GetBanReason() string {
	if m != nil {
		return m.BanReason
	}
	return ""
}

func (m *BatchAddBanUserPostConfigReq) GetWarningMessage() string {
	if m != nil {
		return m.WarningMessage
	}
	return ""
}

func (m *BatchAddBanUserPostConfigReq) GetBanPostTypes() []BanPostType {
	if m != nil {
		return m.BanPostTypes
	}
	return nil
}

func (m *BatchAddBanUserPostConfigReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type BatchAddBanUserPostConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchAddBanUserPostConfigResp) Reset()         { *m = BatchAddBanUserPostConfigResp{} }
func (m *BatchAddBanUserPostConfigResp) String() string { return proto.CompactTextString(m) }
func (*BatchAddBanUserPostConfigResp) ProtoMessage()    {}
func (*BatchAddBanUserPostConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{34}
}
func (m *BatchAddBanUserPostConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddBanUserPostConfigResp.Unmarshal(m, b)
}
func (m *BatchAddBanUserPostConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddBanUserPostConfigResp.Marshal(b, m, deterministic)
}
func (dst *BatchAddBanUserPostConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddBanUserPostConfigResp.Merge(dst, src)
}
func (m *BatchAddBanUserPostConfigResp) XXX_Size() int {
	return xxx_messageInfo_BatchAddBanUserPostConfigResp.Size(m)
}
func (m *BatchAddBanUserPostConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddBanUserPostConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddBanUserPostConfigResp proto.InternalMessageInfo

type UpsertBanUserPostConfigReq struct {
	Config               *BanUserPostConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *UpsertBanUserPostConfigReq) Reset()         { *m = UpsertBanUserPostConfigReq{} }
func (m *UpsertBanUserPostConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpsertBanUserPostConfigReq) ProtoMessage()    {}
func (*UpsertBanUserPostConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{35}
}
func (m *UpsertBanUserPostConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertBanUserPostConfigReq.Unmarshal(m, b)
}
func (m *UpsertBanUserPostConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertBanUserPostConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpsertBanUserPostConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertBanUserPostConfigReq.Merge(dst, src)
}
func (m *UpsertBanUserPostConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpsertBanUserPostConfigReq.Size(m)
}
func (m *UpsertBanUserPostConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertBanUserPostConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertBanUserPostConfigReq proto.InternalMessageInfo

func (m *UpsertBanUserPostConfigReq) GetConfig() *BanUserPostConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type UpsertBanUserPostConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertBanUserPostConfigResp) Reset()         { *m = UpsertBanUserPostConfigResp{} }
func (m *UpsertBanUserPostConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpsertBanUserPostConfigResp) ProtoMessage()    {}
func (*UpsertBanUserPostConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{36}
}
func (m *UpsertBanUserPostConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertBanUserPostConfigResp.Unmarshal(m, b)
}
func (m *UpsertBanUserPostConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertBanUserPostConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpsertBanUserPostConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertBanUserPostConfigResp.Merge(dst, src)
}
func (m *UpsertBanUserPostConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpsertBanUserPostConfigResp.Size(m)
}
func (m *UpsertBanUserPostConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertBanUserPostConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertBanUserPostConfigResp proto.InternalMessageInfo

type GetBanUserPostConfigListReq struct {
	Page                 uint32        `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size                 uint32        `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	NeedCount            bool          `protobuf:"varint,3,opt,name=need_count,json=needCount,proto3" json:"need_count,omitempty"`
	Ttid                 string        `protobuf:"bytes,4,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Status               Status        `protobuf:"varint,5,opt,name=status,proto3,enum=game_ugc_content.Status" json:"status,omitempty"`
	BanPostTypes         []BanPostType `protobuf:"varint,6,rep,packed,name=ban_post_types,json=banPostTypes,proto3,enum=game_ugc_content.BanPostType" json:"ban_post_types,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetBanUserPostConfigListReq) Reset()         { *m = GetBanUserPostConfigListReq{} }
func (m *GetBanUserPostConfigListReq) String() string { return proto.CompactTextString(m) }
func (*GetBanUserPostConfigListReq) ProtoMessage()    {}
func (*GetBanUserPostConfigListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{37}
}
func (m *GetBanUserPostConfigListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBanUserPostConfigListReq.Unmarshal(m, b)
}
func (m *GetBanUserPostConfigListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBanUserPostConfigListReq.Marshal(b, m, deterministic)
}
func (dst *GetBanUserPostConfigListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBanUserPostConfigListReq.Merge(dst, src)
}
func (m *GetBanUserPostConfigListReq) XXX_Size() int {
	return xxx_messageInfo_GetBanUserPostConfigListReq.Size(m)
}
func (m *GetBanUserPostConfigListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBanUserPostConfigListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBanUserPostConfigListReq proto.InternalMessageInfo

func (m *GetBanUserPostConfigListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetBanUserPostConfigListReq) GetSize() uint32 {
	if m != nil {
		return m.Size
	}
	return 0
}

func (m *GetBanUserPostConfigListReq) GetNeedCount() bool {
	if m != nil {
		return m.NeedCount
	}
	return false
}

func (m *GetBanUserPostConfigListReq) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *GetBanUserPostConfigListReq) GetStatus() Status {
	if m != nil {
		return m.Status
	}
	return Status_StatusNone
}

func (m *GetBanUserPostConfigListReq) GetBanPostTypes() []BanPostType {
	if m != nil {
		return m.BanPostTypes
	}
	return nil
}

type GetBanUserPostConfigListResp struct {
	Configs              []*BanUserPostConfig `protobuf:"bytes,1,rep,name=configs,proto3" json:"configs,omitempty"`
	Total                uint32               `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetBanUserPostConfigListResp) Reset()         { *m = GetBanUserPostConfigListResp{} }
func (m *GetBanUserPostConfigListResp) String() string { return proto.CompactTextString(m) }
func (*GetBanUserPostConfigListResp) ProtoMessage()    {}
func (*GetBanUserPostConfigListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{38}
}
func (m *GetBanUserPostConfigListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBanUserPostConfigListResp.Unmarshal(m, b)
}
func (m *GetBanUserPostConfigListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBanUserPostConfigListResp.Marshal(b, m, deterministic)
}
func (dst *GetBanUserPostConfigListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBanUserPostConfigListResp.Merge(dst, src)
}
func (m *GetBanUserPostConfigListResp) XXX_Size() int {
	return xxx_messageInfo_GetBanUserPostConfigListResp.Size(m)
}
func (m *GetBanUserPostConfigListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBanUserPostConfigListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBanUserPostConfigListResp proto.InternalMessageInfo

func (m *GetBanUserPostConfigListResp) GetConfigs() []*BanUserPostConfig {
	if m != nil {
		return m.Configs
	}
	return nil
}

func (m *GetBanUserPostConfigListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type DelBanUserPostConfigReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelBanUserPostConfigReq) Reset()         { *m = DelBanUserPostConfigReq{} }
func (m *DelBanUserPostConfigReq) String() string { return proto.CompactTextString(m) }
func (*DelBanUserPostConfigReq) ProtoMessage()    {}
func (*DelBanUserPostConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{39}
}
func (m *DelBanUserPostConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelBanUserPostConfigReq.Unmarshal(m, b)
}
func (m *DelBanUserPostConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelBanUserPostConfigReq.Marshal(b, m, deterministic)
}
func (dst *DelBanUserPostConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelBanUserPostConfigReq.Merge(dst, src)
}
func (m *DelBanUserPostConfigReq) XXX_Size() int {
	return xxx_messageInfo_DelBanUserPostConfigReq.Size(m)
}
func (m *DelBanUserPostConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelBanUserPostConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelBanUserPostConfigReq proto.InternalMessageInfo

func (m *DelBanUserPostConfigReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type DelBanUserPostConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelBanUserPostConfigResp) Reset()         { *m = DelBanUserPostConfigResp{} }
func (m *DelBanUserPostConfigResp) String() string { return proto.CompactTextString(m) }
func (*DelBanUserPostConfigResp) ProtoMessage()    {}
func (*DelBanUserPostConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{40}
}
func (m *DelBanUserPostConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelBanUserPostConfigResp.Unmarshal(m, b)
}
func (m *DelBanUserPostConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelBanUserPostConfigResp.Marshal(b, m, deterministic)
}
func (dst *DelBanUserPostConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelBanUserPostConfigResp.Merge(dst, src)
}
func (m *DelBanUserPostConfigResp) XXX_Size() int {
	return xxx_messageInfo_DelBanUserPostConfigResp.Size(m)
}
func (m *DelBanUserPostConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelBanUserPostConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelBanUserPostConfigResp proto.InternalMessageInfo

type ActiveBanUserPostConfig struct {
	ConfigType           BanConfigType `protobuf:"varint,1,opt,name=config_type,json=configType,proto3,enum=game_ugc_content.BanConfigType" json:"config_type,omitempty"`
	Uid                  uint32        `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	CrowdGroupId         string        `protobuf:"bytes,3,opt,name=crowd_group_id,json=crowdGroupId,proto3" json:"crowd_group_id,omitempty"`
	TabId                uint32        `protobuf:"varint,4,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	BanReason            string        `protobuf:"bytes,5,opt,name=ban_reason,json=banReason,proto3" json:"ban_reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ActiveBanUserPostConfig) Reset()         { *m = ActiveBanUserPostConfig{} }
func (m *ActiveBanUserPostConfig) String() string { return proto.CompactTextString(m) }
func (*ActiveBanUserPostConfig) ProtoMessage()    {}
func (*ActiveBanUserPostConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{41}
}
func (m *ActiveBanUserPostConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActiveBanUserPostConfig.Unmarshal(m, b)
}
func (m *ActiveBanUserPostConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActiveBanUserPostConfig.Marshal(b, m, deterministic)
}
func (dst *ActiveBanUserPostConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActiveBanUserPostConfig.Merge(dst, src)
}
func (m *ActiveBanUserPostConfig) XXX_Size() int {
	return xxx_messageInfo_ActiveBanUserPostConfig.Size(m)
}
func (m *ActiveBanUserPostConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_ActiveBanUserPostConfig.DiscardUnknown(m)
}

var xxx_messageInfo_ActiveBanUserPostConfig proto.InternalMessageInfo

func (m *ActiveBanUserPostConfig) GetConfigType() BanConfigType {
	if m != nil {
		return m.ConfigType
	}
	return BanConfigType_BanConfigTypeNone
}

func (m *ActiveBanUserPostConfig) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ActiveBanUserPostConfig) GetCrowdGroupId() string {
	if m != nil {
		return m.CrowdGroupId
	}
	return ""
}

func (m *ActiveBanUserPostConfig) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *ActiveBanUserPostConfig) GetBanReason() string {
	if m != nil {
		return m.BanReason
	}
	return ""
}

type GetActiveBanUserPostConfigWithCacheReq struct {
	TabId                uint32      `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Uid                  uint32      `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	BanPostType          BanPostType `protobuf:"varint,3,opt,name=ban_post_type,json=banPostType,proto3,enum=game_ugc_content.BanPostType" json:"ban_post_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetActiveBanUserPostConfigWithCacheReq) Reset() {
	*m = GetActiveBanUserPostConfigWithCacheReq{}
}
func (m *GetActiveBanUserPostConfigWithCacheReq) String() string { return proto.CompactTextString(m) }
func (*GetActiveBanUserPostConfigWithCacheReq) ProtoMessage()    {}
func (*GetActiveBanUserPostConfigWithCacheReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{42}
}
func (m *GetActiveBanUserPostConfigWithCacheReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActiveBanUserPostConfigWithCacheReq.Unmarshal(m, b)
}
func (m *GetActiveBanUserPostConfigWithCacheReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActiveBanUserPostConfigWithCacheReq.Marshal(b, m, deterministic)
}
func (dst *GetActiveBanUserPostConfigWithCacheReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActiveBanUserPostConfigWithCacheReq.Merge(dst, src)
}
func (m *GetActiveBanUserPostConfigWithCacheReq) XXX_Size() int {
	return xxx_messageInfo_GetActiveBanUserPostConfigWithCacheReq.Size(m)
}
func (m *GetActiveBanUserPostConfigWithCacheReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActiveBanUserPostConfigWithCacheReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetActiveBanUserPostConfigWithCacheReq proto.InternalMessageInfo

func (m *GetActiveBanUserPostConfigWithCacheReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetActiveBanUserPostConfigWithCacheReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetActiveBanUserPostConfigWithCacheReq) GetBanPostType() BanPostType {
	if m != nil {
		return m.BanPostType
	}
	return BanPostType_BanPostTypePost
}

type GetActiveBanUserPostConfigWithCacheResp struct {
	Configs              []*ActiveBanUserPostConfig `protobuf:"bytes,1,rep,name=configs,proto3" json:"configs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetActiveBanUserPostConfigWithCacheResp) Reset() {
	*m = GetActiveBanUserPostConfigWithCacheResp{}
}
func (m *GetActiveBanUserPostConfigWithCacheResp) String() string { return proto.CompactTextString(m) }
func (*GetActiveBanUserPostConfigWithCacheResp) ProtoMessage()    {}
func (*GetActiveBanUserPostConfigWithCacheResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{43}
}
func (m *GetActiveBanUserPostConfigWithCacheResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActiveBanUserPostConfigWithCacheResp.Unmarshal(m, b)
}
func (m *GetActiveBanUserPostConfigWithCacheResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActiveBanUserPostConfigWithCacheResp.Marshal(b, m, deterministic)
}
func (dst *GetActiveBanUserPostConfigWithCacheResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActiveBanUserPostConfigWithCacheResp.Merge(dst, src)
}
func (m *GetActiveBanUserPostConfigWithCacheResp) XXX_Size() int {
	return xxx_messageInfo_GetActiveBanUserPostConfigWithCacheResp.Size(m)
}
func (m *GetActiveBanUserPostConfigWithCacheResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActiveBanUserPostConfigWithCacheResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetActiveBanUserPostConfigWithCacheResp proto.InternalMessageInfo

func (m *GetActiveBanUserPostConfigWithCacheResp) GetConfigs() []*ActiveBanUserPostConfig {
	if m != nil {
		return m.Configs
	}
	return nil
}

type BatchUpdateConfigTabsBaseInfoReq struct {
	ConfigTabs           []*ConfigTab `protobuf:"bytes,1,rep,name=config_tabs,json=configTabs,proto3" json:"config_tabs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *BatchUpdateConfigTabsBaseInfoReq) Reset()         { *m = BatchUpdateConfigTabsBaseInfoReq{} }
func (m *BatchUpdateConfigTabsBaseInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchUpdateConfigTabsBaseInfoReq) ProtoMessage()    {}
func (*BatchUpdateConfigTabsBaseInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{44}
}
func (m *BatchUpdateConfigTabsBaseInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUpdateConfigTabsBaseInfoReq.Unmarshal(m, b)
}
func (m *BatchUpdateConfigTabsBaseInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUpdateConfigTabsBaseInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatchUpdateConfigTabsBaseInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUpdateConfigTabsBaseInfoReq.Merge(dst, src)
}
func (m *BatchUpdateConfigTabsBaseInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatchUpdateConfigTabsBaseInfoReq.Size(m)
}
func (m *BatchUpdateConfigTabsBaseInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUpdateConfigTabsBaseInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUpdateConfigTabsBaseInfoReq proto.InternalMessageInfo

func (m *BatchUpdateConfigTabsBaseInfoReq) GetConfigTabs() []*ConfigTab {
	if m != nil {
		return m.ConfigTabs
	}
	return nil
}

type BatchUpdateConfigTabsBaseInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchUpdateConfigTabsBaseInfoResp) Reset()         { *m = BatchUpdateConfigTabsBaseInfoResp{} }
func (m *BatchUpdateConfigTabsBaseInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatchUpdateConfigTabsBaseInfoResp) ProtoMessage()    {}
func (*BatchUpdateConfigTabsBaseInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{45}
}
func (m *BatchUpdateConfigTabsBaseInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUpdateConfigTabsBaseInfoResp.Unmarshal(m, b)
}
func (m *BatchUpdateConfigTabsBaseInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUpdateConfigTabsBaseInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatchUpdateConfigTabsBaseInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUpdateConfigTabsBaseInfoResp.Merge(dst, src)
}
func (m *BatchUpdateConfigTabsBaseInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatchUpdateConfigTabsBaseInfoResp.Size(m)
}
func (m *BatchUpdateConfigTabsBaseInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUpdateConfigTabsBaseInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUpdateConfigTabsBaseInfoResp proto.InternalMessageInfo

type InsertDefaultConfigTabsReq struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InsertDefaultConfigTabsReq) Reset()         { *m = InsertDefaultConfigTabsReq{} }
func (m *InsertDefaultConfigTabsReq) String() string { return proto.CompactTextString(m) }
func (*InsertDefaultConfigTabsReq) ProtoMessage()    {}
func (*InsertDefaultConfigTabsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{46}
}
func (m *InsertDefaultConfigTabsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsertDefaultConfigTabsReq.Unmarshal(m, b)
}
func (m *InsertDefaultConfigTabsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsertDefaultConfigTabsReq.Marshal(b, m, deterministic)
}
func (dst *InsertDefaultConfigTabsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsertDefaultConfigTabsReq.Merge(dst, src)
}
func (m *InsertDefaultConfigTabsReq) XXX_Size() int {
	return xxx_messageInfo_InsertDefaultConfigTabsReq.Size(m)
}
func (m *InsertDefaultConfigTabsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_InsertDefaultConfigTabsReq.DiscardUnknown(m)
}

var xxx_messageInfo_InsertDefaultConfigTabsReq proto.InternalMessageInfo

func (m *InsertDefaultConfigTabsReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type InsertDefaultConfigTabsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InsertDefaultConfigTabsResp) Reset()         { *m = InsertDefaultConfigTabsResp{} }
func (m *InsertDefaultConfigTabsResp) String() string { return proto.CompactTextString(m) }
func (*InsertDefaultConfigTabsResp) ProtoMessage()    {}
func (*InsertDefaultConfigTabsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{47}
}
func (m *InsertDefaultConfigTabsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsertDefaultConfigTabsResp.Unmarshal(m, b)
}
func (m *InsertDefaultConfigTabsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsertDefaultConfigTabsResp.Marshal(b, m, deterministic)
}
func (dst *InsertDefaultConfigTabsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsertDefaultConfigTabsResp.Merge(dst, src)
}
func (m *InsertDefaultConfigTabsResp) XXX_Size() int {
	return xxx_messageInfo_InsertDefaultConfigTabsResp.Size(m)
}
func (m *InsertDefaultConfigTabsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_InsertDefaultConfigTabsResp.DiscardUnknown(m)
}

var xxx_messageInfo_InsertDefaultConfigTabsResp proto.InternalMessageInfo

type BatchGetGameFeedPbsReq struct {
	PostId               []string `protobuf:"bytes,1,rep,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	ReqSource            uint32   `protobuf:"varint,2,opt,name=req_source,json=reqSource,proto3" json:"req_source,omitempty"`
	ReqUid               uint32   `protobuf:"varint,3,opt,name=req_uid,json=reqUid,proto3" json:"req_uid,omitempty"`
	MarketId             uint32   `protobuf:"varint,4,opt,name=marketId,proto3" json:"marketId,omitempty"`
	ClientType           uint32   `protobuf:"varint,5,opt,name=clientType,proto3" json:"clientType,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetGameFeedPbsReq) Reset()         { *m = BatchGetGameFeedPbsReq{} }
func (m *BatchGetGameFeedPbsReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetGameFeedPbsReq) ProtoMessage()    {}
func (*BatchGetGameFeedPbsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{48}
}
func (m *BatchGetGameFeedPbsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetGameFeedPbsReq.Unmarshal(m, b)
}
func (m *BatchGetGameFeedPbsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetGameFeedPbsReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetGameFeedPbsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetGameFeedPbsReq.Merge(dst, src)
}
func (m *BatchGetGameFeedPbsReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetGameFeedPbsReq.Size(m)
}
func (m *BatchGetGameFeedPbsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetGameFeedPbsReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetGameFeedPbsReq proto.InternalMessageInfo

func (m *BatchGetGameFeedPbsReq) GetPostId() []string {
	if m != nil {
		return m.PostId
	}
	return nil
}

func (m *BatchGetGameFeedPbsReq) GetReqSource() uint32 {
	if m != nil {
		return m.ReqSource
	}
	return 0
}

func (m *BatchGetGameFeedPbsReq) GetReqUid() uint32 {
	if m != nil {
		return m.ReqUid
	}
	return 0
}

func (m *BatchGetGameFeedPbsReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *BatchGetGameFeedPbsReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

type GameFeedContent struct {
	UnmarshalType        uint32   `protobuf:"varint,1,opt,name=unmarshal_type,json=unmarshalType,proto3" json:"unmarshal_type,omitempty"`
	Content              []byte   `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameFeedContent) Reset()         { *m = GameFeedContent{} }
func (m *GameFeedContent) String() string { return proto.CompactTextString(m) }
func (*GameFeedContent) ProtoMessage()    {}
func (*GameFeedContent) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{49}
}
func (m *GameFeedContent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameFeedContent.Unmarshal(m, b)
}
func (m *GameFeedContent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameFeedContent.Marshal(b, m, deterministic)
}
func (dst *GameFeedContent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameFeedContent.Merge(dst, src)
}
func (m *GameFeedContent) XXX_Size() int {
	return xxx_messageInfo_GameFeedContent.Size(m)
}
func (m *GameFeedContent) XXX_DiscardUnknown() {
	xxx_messageInfo_GameFeedContent.DiscardUnknown(m)
}

var xxx_messageInfo_GameFeedContent proto.InternalMessageInfo

func (m *GameFeedContent) GetUnmarshalType() uint32 {
	if m != nil {
		return m.UnmarshalType
	}
	return 0
}

func (m *GameFeedContent) GetContent() []byte {
	if m != nil {
		return m.Content
	}
	return nil
}

type BatchGetGameFeedPbsResp struct {
	GameFeedContent      []*GameFeedContent `protobuf:"bytes,1,rep,name=game_feed_content,json=gameFeedContent,proto3" json:"game_feed_content,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *BatchGetGameFeedPbsResp) Reset()         { *m = BatchGetGameFeedPbsResp{} }
func (m *BatchGetGameFeedPbsResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetGameFeedPbsResp) ProtoMessage()    {}
func (*BatchGetGameFeedPbsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_content_556dee98c2728bd3, []int{50}
}
func (m *BatchGetGameFeedPbsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetGameFeedPbsResp.Unmarshal(m, b)
}
func (m *BatchGetGameFeedPbsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetGameFeedPbsResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetGameFeedPbsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetGameFeedPbsResp.Merge(dst, src)
}
func (m *BatchGetGameFeedPbsResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetGameFeedPbsResp.Size(m)
}
func (m *BatchGetGameFeedPbsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetGameFeedPbsResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetGameFeedPbsResp proto.InternalMessageInfo

func (m *BatchGetGameFeedPbsResp) GetGameFeedContent() []*GameFeedContent {
	if m != nil {
		return m.GameFeedContent
	}
	return nil
}

func init() {
	proto.RegisterType((*Topic)(nil), "game_ugc_content.Topic")
	proto.RegisterType((*UpsertTopicReq)(nil), "game_ugc_content.UpsertTopicReq")
	proto.RegisterType((*UpsertTopicResp)(nil), "game_ugc_content.UpsertTopicResp")
	proto.RegisterType((*GetTopicListReq)(nil), "game_ugc_content.GetTopicListReq")
	proto.RegisterType((*GetTopicListResp)(nil), "game_ugc_content.GetTopicListResp")
	proto.RegisterType((*DelTopicReq)(nil), "game_ugc_content.DelTopicReq")
	proto.RegisterType((*DelTopicResp)(nil), "game_ugc_content.DelTopicResp")
	proto.RegisterType((*ConfigTab)(nil), "game_ugc_content.ConfigTab")
	proto.RegisterType((*RecRule)(nil), "game_ugc_content.RecRule")
	proto.RegisterType((*PostTeleport)(nil), "game_ugc_content.PostTeleport")
	proto.RegisterType((*UpsertConfigTabReq)(nil), "game_ugc_content.UpsertConfigTabReq")
	proto.RegisterType((*UpsertConfigTabResp)(nil), "game_ugc_content.UpsertConfigTabResp")
	proto.RegisterType((*GetConfigTabsReq)(nil), "game_ugc_content.GetConfigTabsReq")
	proto.RegisterType((*GetConfigTabsResp)(nil), "game_ugc_content.GetConfigTabsResp")
	proto.RegisterType((*DelConfigTabReq)(nil), "game_ugc_content.DelConfigTabReq")
	proto.RegisterType((*DelConfigTabResp)(nil), "game_ugc_content.DelConfigTabResp")
	proto.RegisterType((*GameConfigTab)(nil), "game_ugc_content.GameConfigTab")
	proto.RegisterType((*SaveGameConfigTabReq)(nil), "game_ugc_content.SaveGameConfigTabReq")
	proto.RegisterType((*SaveGameConfigTabResp)(nil), "game_ugc_content.SaveGameConfigTabResp")
	proto.RegisterType((*GetGameConfigTabListReq)(nil), "game_ugc_content.GetGameConfigTabListReq")
	proto.RegisterType((*GetGameConfigTabListResp)(nil), "game_ugc_content.GetGameConfigTabListResp")
	proto.RegisterType((*DelGameConfigTabReq)(nil), "game_ugc_content.DelGameConfigTabReq")
	proto.RegisterType((*DelGameConfigTabResp)(nil), "game_ugc_content.DelGameConfigTabResp")
	proto.RegisterType((*GameConfigTabDetail)(nil), "game_ugc_content.GameConfigTabDetail")
	proto.RegisterType((*GetGameConfigTabDetailByTabIdReq)(nil), "game_ugc_content.GetGameConfigTabDetailByTabIdReq")
	proto.RegisterType((*GetGameConfigTabDetailByTabIdResp)(nil), "game_ugc_content.GetGameConfigTabDetailByTabIdResp")
	proto.RegisterType((*ConfigTabInfo)(nil), "game_ugc_content.ConfigTabInfo")
	proto.RegisterType((*GetConfigTabInfoByIdReq)(nil), "game_ugc_content.GetConfigTabInfoByIdReq")
	proto.RegisterType((*GetConfigTabInfoByIdResp)(nil), "game_ugc_content.GetConfigTabInfoByIdResp")
	proto.RegisterType((*GamePostBussInfo)(nil), "game_ugc_content.GamePostBussInfo")
	proto.RegisterType((*GetGamePalTabsReq)(nil), "game_ugc_content.GetGamePalTabsReq")
	proto.RegisterType((*GetGamePalTabsResp)(nil), "game_ugc_content.GetGamePalTabsResp")
	proto.RegisterMapType((map[uint32]*ConfigTabInfo)(nil), "game_ugc_content.GetGamePalTabsResp.GamePalTabsEntry")
	proto.RegisterType((*BanUserPostConfig)(nil), "game_ugc_content.BanUserPostConfig")
	proto.RegisterType((*BatchAddBanUserPostConfigReq)(nil), "game_ugc_content.BatchAddBanUserPostConfigReq")
	proto.RegisterType((*BatchAddBanUserPostConfigResp)(nil), "game_ugc_content.BatchAddBanUserPostConfigResp")
	proto.RegisterType((*UpsertBanUserPostConfigReq)(nil), "game_ugc_content.UpsertBanUserPostConfigReq")
	proto.RegisterType((*UpsertBanUserPostConfigResp)(nil), "game_ugc_content.UpsertBanUserPostConfigResp")
	proto.RegisterType((*GetBanUserPostConfigListReq)(nil), "game_ugc_content.GetBanUserPostConfigListReq")
	proto.RegisterType((*GetBanUserPostConfigListResp)(nil), "game_ugc_content.GetBanUserPostConfigListResp")
	proto.RegisterType((*DelBanUserPostConfigReq)(nil), "game_ugc_content.DelBanUserPostConfigReq")
	proto.RegisterType((*DelBanUserPostConfigResp)(nil), "game_ugc_content.DelBanUserPostConfigResp")
	proto.RegisterType((*ActiveBanUserPostConfig)(nil), "game_ugc_content.ActiveBanUserPostConfig")
	proto.RegisterType((*GetActiveBanUserPostConfigWithCacheReq)(nil), "game_ugc_content.GetActiveBanUserPostConfigWithCacheReq")
	proto.RegisterType((*GetActiveBanUserPostConfigWithCacheResp)(nil), "game_ugc_content.GetActiveBanUserPostConfigWithCacheResp")
	proto.RegisterType((*BatchUpdateConfigTabsBaseInfoReq)(nil), "game_ugc_content.BatchUpdateConfigTabsBaseInfoReq")
	proto.RegisterType((*BatchUpdateConfigTabsBaseInfoResp)(nil), "game_ugc_content.BatchUpdateConfigTabsBaseInfoResp")
	proto.RegisterType((*InsertDefaultConfigTabsReq)(nil), "game_ugc_content.InsertDefaultConfigTabsReq")
	proto.RegisterType((*InsertDefaultConfigTabsResp)(nil), "game_ugc_content.InsertDefaultConfigTabsResp")
	proto.RegisterType((*BatchGetGameFeedPbsReq)(nil), "game_ugc_content.BatchGetGameFeedPbsReq")
	proto.RegisterType((*GameFeedContent)(nil), "game_ugc_content.GameFeedContent")
	proto.RegisterType((*BatchGetGameFeedPbsResp)(nil), "game_ugc_content.BatchGetGameFeedPbsResp")
	proto.RegisterEnum("game_ugc_content.CrowdType", CrowdType_name, CrowdType_value)
	proto.RegisterEnum("game_ugc_content.ConfigTabType", ConfigTabType_name, ConfigTabType_value)
	proto.RegisterEnum("game_ugc_content.TabSubType", TabSubType_name, TabSubType_value)
	proto.RegisterEnum("game_ugc_content.RequestSource", RequestSource_name, RequestSource_value)
	proto.RegisterEnum("game_ugc_content.BanConfigType", BanConfigType_name, BanConfigType_value)
	proto.RegisterEnum("game_ugc_content.Status", Status_name, Status_value)
	proto.RegisterEnum("game_ugc_content.BanPostType", BanPostType_name, BanPostType_value)
	proto.RegisterEnum("game_ugc_content.ReqSource", ReqSource_name, ReqSource_value)
	proto.RegisterEnum("game_ugc_content.UnmarshalType", UnmarshalType_name, UnmarshalType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GameUgcContentClient is the client API for GameUgcContent service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GameUgcContentClient interface {
	// 运营后台--
	// 创建/更新话题
	UpsertTopic(ctx context.Context, in *UpsertTopicReq, opts ...grpc.CallOption) (*UpsertTopicResp, error)
	// 获取话题列表
	GetTopicList(ctx context.Context, in *GetTopicListReq, opts ...grpc.CallOption) (*GetTopicListResp, error)
	// 删除话题
	DelTopic(ctx context.Context, in *DelTopicReq, opts ...grpc.CallOption) (*DelTopicResp, error)
	// 创建/更新动态tab配置
	UpsertConfigTab(ctx context.Context, in *UpsertConfigTabReq, opts ...grpc.CallOption) (*UpsertConfigTabResp, error)
	// 获取动态tab配置列表
	GetConfigTabs(ctx context.Context, in *GetConfigTabsReq, opts ...grpc.CallOption) (*GetConfigTabsResp, error)
	// 删除动态tab配置
	DelConfigTab(ctx context.Context, in *DelConfigTabReq, opts ...grpc.CallOption) (*DelConfigTabResp, error)
	// 为玩法创建默认的开黑房和搭子卡tab，在创建玩法时调用
	InsertDefaultConfigTabs(ctx context.Context, in *InsertDefaultConfigTabsReq, opts ...grpc.CallOption) (*InsertDefaultConfigTabsResp, error)
	// 更新玩法绑定的动态tab配置
	SaveGameConfigTab(ctx context.Context, in *SaveGameConfigTabReq, opts ...grpc.CallOption) (*SaveGameConfigTabResp, error)
	// 获取玩法绑定的动态tab配置列表
	GetGameConfigTabList(ctx context.Context, in *GetGameConfigTabListReq, opts ...grpc.CallOption) (*GetGameConfigTabListResp, error)
	// 删除玩法绑定的动态tab配置
	DelGameConfigTab(ctx context.Context, in *DelGameConfigTabReq, opts ...grpc.CallOption) (*DelGameConfigTabResp, error)
	// 更新房间tab和搭子卡tab的名称的基本信息
	BatchUpdateConfigTabsBaseInfo(ctx context.Context, in *BatchUpdateConfigTabsBaseInfoReq, opts ...grpc.CallOption) (*BatchUpdateConfigTabsBaseInfoResp, error)
	// 创建/更新禁止用户发帖/搭子卡配置
	UpsertBanUserPostConfig(ctx context.Context, in *UpsertBanUserPostConfigReq, opts ...grpc.CallOption) (*UpsertBanUserPostConfigResp, error)
	// 获取禁止用户发帖/搭子卡配置列表
	GetBanUserPostConfigList(ctx context.Context, in *GetBanUserPostConfigListReq, opts ...grpc.CallOption) (*GetBanUserPostConfigListResp, error)
	// 删除禁止用户发帖/搭子卡配置
	DelBanUserPostConfig(ctx context.Context, in *DelBanUserPostConfigReq, opts ...grpc.CallOption) (*DelBanUserPostConfigResp, error)
	// 批量添加禁止用户使用搭子卡功能/屏蔽搭子卡配置
	BatchAddBanUserPostConfig(ctx context.Context, in *BatchAddBanUserPostConfigReq, opts ...grpc.CallOption) (*BatchAddBanUserPostConfigResp, error)
	// 根据tab_id获取玩法绑定的动态tab配置(查缓存)
	GetGameConfigTabDetailByTabId(ctx context.Context, in *GetGameConfigTabDetailByTabIdReq, opts ...grpc.CallOption) (*GetGameConfigTabDetailByTabIdResp, error)
	// 根据config_tab_id获取对应配置信息(查缓存)
	GetConfigTabInfoById(ctx context.Context, in *GetConfigTabInfoByIdReq, opts ...grpc.CallOption) (*GetConfigTabInfoByIdResp, error)
	// 查询是否有用户禁止发帖配置(查缓存)
	GetActiveBanUserPostConfigWithCache(ctx context.Context, in *GetActiveBanUserPostConfigWithCacheReq, opts ...grpc.CallOption) (*GetActiveBanUserPostConfigWithCacheResp, error)
	// 查询所有玩法配置展示中的搭子卡配置(查缓存)
	GetGamePalTabs(ctx context.Context, in *GetGamePalTabsReq, opts ...grpc.CallOption) (*GetGamePalTabsResp, error)
	// 批量获取帖子展示信息
	BatchGetGameFeedPbs(ctx context.Context, in *BatchGetGameFeedPbsReq, opts ...grpc.CallOption) (*BatchGetGameFeedPbsResp, error)
}

type gameUgcContentClient struct {
	cc *grpc.ClientConn
}

func NewGameUgcContentClient(cc *grpc.ClientConn) GameUgcContentClient {
	return &gameUgcContentClient{cc}
}

func (c *gameUgcContentClient) UpsertTopic(ctx context.Context, in *UpsertTopicReq, opts ...grpc.CallOption) (*UpsertTopicResp, error) {
	out := new(UpsertTopicResp)
	err := c.cc.Invoke(ctx, "/game_ugc_content.GameUgcContent/UpsertTopic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUgcContentClient) GetTopicList(ctx context.Context, in *GetTopicListReq, opts ...grpc.CallOption) (*GetTopicListResp, error) {
	out := new(GetTopicListResp)
	err := c.cc.Invoke(ctx, "/game_ugc_content.GameUgcContent/GetTopicList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUgcContentClient) DelTopic(ctx context.Context, in *DelTopicReq, opts ...grpc.CallOption) (*DelTopicResp, error) {
	out := new(DelTopicResp)
	err := c.cc.Invoke(ctx, "/game_ugc_content.GameUgcContent/DelTopic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUgcContentClient) UpsertConfigTab(ctx context.Context, in *UpsertConfigTabReq, opts ...grpc.CallOption) (*UpsertConfigTabResp, error) {
	out := new(UpsertConfigTabResp)
	err := c.cc.Invoke(ctx, "/game_ugc_content.GameUgcContent/UpsertConfigTab", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUgcContentClient) GetConfigTabs(ctx context.Context, in *GetConfigTabsReq, opts ...grpc.CallOption) (*GetConfigTabsResp, error) {
	out := new(GetConfigTabsResp)
	err := c.cc.Invoke(ctx, "/game_ugc_content.GameUgcContent/GetConfigTabs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUgcContentClient) DelConfigTab(ctx context.Context, in *DelConfigTabReq, opts ...grpc.CallOption) (*DelConfigTabResp, error) {
	out := new(DelConfigTabResp)
	err := c.cc.Invoke(ctx, "/game_ugc_content.GameUgcContent/DelConfigTab", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUgcContentClient) InsertDefaultConfigTabs(ctx context.Context, in *InsertDefaultConfigTabsReq, opts ...grpc.CallOption) (*InsertDefaultConfigTabsResp, error) {
	out := new(InsertDefaultConfigTabsResp)
	err := c.cc.Invoke(ctx, "/game_ugc_content.GameUgcContent/InsertDefaultConfigTabs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUgcContentClient) SaveGameConfigTab(ctx context.Context, in *SaveGameConfigTabReq, opts ...grpc.CallOption) (*SaveGameConfigTabResp, error) {
	out := new(SaveGameConfigTabResp)
	err := c.cc.Invoke(ctx, "/game_ugc_content.GameUgcContent/SaveGameConfigTab", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUgcContentClient) GetGameConfigTabList(ctx context.Context, in *GetGameConfigTabListReq, opts ...grpc.CallOption) (*GetGameConfigTabListResp, error) {
	out := new(GetGameConfigTabListResp)
	err := c.cc.Invoke(ctx, "/game_ugc_content.GameUgcContent/GetGameConfigTabList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUgcContentClient) DelGameConfigTab(ctx context.Context, in *DelGameConfigTabReq, opts ...grpc.CallOption) (*DelGameConfigTabResp, error) {
	out := new(DelGameConfigTabResp)
	err := c.cc.Invoke(ctx, "/game_ugc_content.GameUgcContent/DelGameConfigTab", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUgcContentClient) BatchUpdateConfigTabsBaseInfo(ctx context.Context, in *BatchUpdateConfigTabsBaseInfoReq, opts ...grpc.CallOption) (*BatchUpdateConfigTabsBaseInfoResp, error) {
	out := new(BatchUpdateConfigTabsBaseInfoResp)
	err := c.cc.Invoke(ctx, "/game_ugc_content.GameUgcContent/BatchUpdateConfigTabsBaseInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUgcContentClient) UpsertBanUserPostConfig(ctx context.Context, in *UpsertBanUserPostConfigReq, opts ...grpc.CallOption) (*UpsertBanUserPostConfigResp, error) {
	out := new(UpsertBanUserPostConfigResp)
	err := c.cc.Invoke(ctx, "/game_ugc_content.GameUgcContent/UpsertBanUserPostConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUgcContentClient) GetBanUserPostConfigList(ctx context.Context, in *GetBanUserPostConfigListReq, opts ...grpc.CallOption) (*GetBanUserPostConfigListResp, error) {
	out := new(GetBanUserPostConfigListResp)
	err := c.cc.Invoke(ctx, "/game_ugc_content.GameUgcContent/GetBanUserPostConfigList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUgcContentClient) DelBanUserPostConfig(ctx context.Context, in *DelBanUserPostConfigReq, opts ...grpc.CallOption) (*DelBanUserPostConfigResp, error) {
	out := new(DelBanUserPostConfigResp)
	err := c.cc.Invoke(ctx, "/game_ugc_content.GameUgcContent/DelBanUserPostConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUgcContentClient) BatchAddBanUserPostConfig(ctx context.Context, in *BatchAddBanUserPostConfigReq, opts ...grpc.CallOption) (*BatchAddBanUserPostConfigResp, error) {
	out := new(BatchAddBanUserPostConfigResp)
	err := c.cc.Invoke(ctx, "/game_ugc_content.GameUgcContent/BatchAddBanUserPostConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUgcContentClient) GetGameConfigTabDetailByTabId(ctx context.Context, in *GetGameConfigTabDetailByTabIdReq, opts ...grpc.CallOption) (*GetGameConfigTabDetailByTabIdResp, error) {
	out := new(GetGameConfigTabDetailByTabIdResp)
	err := c.cc.Invoke(ctx, "/game_ugc_content.GameUgcContent/GetGameConfigTabDetailByTabId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUgcContentClient) GetConfigTabInfoById(ctx context.Context, in *GetConfigTabInfoByIdReq, opts ...grpc.CallOption) (*GetConfigTabInfoByIdResp, error) {
	out := new(GetConfigTabInfoByIdResp)
	err := c.cc.Invoke(ctx, "/game_ugc_content.GameUgcContent/GetConfigTabInfoById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUgcContentClient) GetActiveBanUserPostConfigWithCache(ctx context.Context, in *GetActiveBanUserPostConfigWithCacheReq, opts ...grpc.CallOption) (*GetActiveBanUserPostConfigWithCacheResp, error) {
	out := new(GetActiveBanUserPostConfigWithCacheResp)
	err := c.cc.Invoke(ctx, "/game_ugc_content.GameUgcContent/GetActiveBanUserPostConfigWithCache", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUgcContentClient) GetGamePalTabs(ctx context.Context, in *GetGamePalTabsReq, opts ...grpc.CallOption) (*GetGamePalTabsResp, error) {
	out := new(GetGamePalTabsResp)
	err := c.cc.Invoke(ctx, "/game_ugc_content.GameUgcContent/GetGamePalTabs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUgcContentClient) BatchGetGameFeedPbs(ctx context.Context, in *BatchGetGameFeedPbsReq, opts ...grpc.CallOption) (*BatchGetGameFeedPbsResp, error) {
	out := new(BatchGetGameFeedPbsResp)
	err := c.cc.Invoke(ctx, "/game_ugc_content.GameUgcContent/BatchGetGameFeedPbs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GameUgcContentServer is the server API for GameUgcContent service.
type GameUgcContentServer interface {
	// 运营后台--
	// 创建/更新话题
	UpsertTopic(context.Context, *UpsertTopicReq) (*UpsertTopicResp, error)
	// 获取话题列表
	GetTopicList(context.Context, *GetTopicListReq) (*GetTopicListResp, error)
	// 删除话题
	DelTopic(context.Context, *DelTopicReq) (*DelTopicResp, error)
	// 创建/更新动态tab配置
	UpsertConfigTab(context.Context, *UpsertConfigTabReq) (*UpsertConfigTabResp, error)
	// 获取动态tab配置列表
	GetConfigTabs(context.Context, *GetConfigTabsReq) (*GetConfigTabsResp, error)
	// 删除动态tab配置
	DelConfigTab(context.Context, *DelConfigTabReq) (*DelConfigTabResp, error)
	// 为玩法创建默认的开黑房和搭子卡tab，在创建玩法时调用
	InsertDefaultConfigTabs(context.Context, *InsertDefaultConfigTabsReq) (*InsertDefaultConfigTabsResp, error)
	// 更新玩法绑定的动态tab配置
	SaveGameConfigTab(context.Context, *SaveGameConfigTabReq) (*SaveGameConfigTabResp, error)
	// 获取玩法绑定的动态tab配置列表
	GetGameConfigTabList(context.Context, *GetGameConfigTabListReq) (*GetGameConfigTabListResp, error)
	// 删除玩法绑定的动态tab配置
	DelGameConfigTab(context.Context, *DelGameConfigTabReq) (*DelGameConfigTabResp, error)
	// 更新房间tab和搭子卡tab的名称的基本信息
	BatchUpdateConfigTabsBaseInfo(context.Context, *BatchUpdateConfigTabsBaseInfoReq) (*BatchUpdateConfigTabsBaseInfoResp, error)
	// 创建/更新禁止用户发帖/搭子卡配置
	UpsertBanUserPostConfig(context.Context, *UpsertBanUserPostConfigReq) (*UpsertBanUserPostConfigResp, error)
	// 获取禁止用户发帖/搭子卡配置列表
	GetBanUserPostConfigList(context.Context, *GetBanUserPostConfigListReq) (*GetBanUserPostConfigListResp, error)
	// 删除禁止用户发帖/搭子卡配置
	DelBanUserPostConfig(context.Context, *DelBanUserPostConfigReq) (*DelBanUserPostConfigResp, error)
	// 批量添加禁止用户使用搭子卡功能/屏蔽搭子卡配置
	BatchAddBanUserPostConfig(context.Context, *BatchAddBanUserPostConfigReq) (*BatchAddBanUserPostConfigResp, error)
	// 根据tab_id获取玩法绑定的动态tab配置(查缓存)
	GetGameConfigTabDetailByTabId(context.Context, *GetGameConfigTabDetailByTabIdReq) (*GetGameConfigTabDetailByTabIdResp, error)
	// 根据config_tab_id获取对应配置信息(查缓存)
	GetConfigTabInfoById(context.Context, *GetConfigTabInfoByIdReq) (*GetConfigTabInfoByIdResp, error)
	// 查询是否有用户禁止发帖配置(查缓存)
	GetActiveBanUserPostConfigWithCache(context.Context, *GetActiveBanUserPostConfigWithCacheReq) (*GetActiveBanUserPostConfigWithCacheResp, error)
	// 查询所有玩法配置展示中的搭子卡配置(查缓存)
	GetGamePalTabs(context.Context, *GetGamePalTabsReq) (*GetGamePalTabsResp, error)
	// 批量获取帖子展示信息
	BatchGetGameFeedPbs(context.Context, *BatchGetGameFeedPbsReq) (*BatchGetGameFeedPbsResp, error)
}

func RegisterGameUgcContentServer(s *grpc.Server, srv GameUgcContentServer) {
	s.RegisterService(&_GameUgcContent_serviceDesc, srv)
}

func _GameUgcContent_UpsertTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertTopicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUgcContentServer).UpsertTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ugc_content.GameUgcContent/UpsertTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUgcContentServer).UpsertTopic(ctx, req.(*UpsertTopicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUgcContent_GetTopicList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopicListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUgcContentServer).GetTopicList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ugc_content.GameUgcContent/GetTopicList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUgcContentServer).GetTopicList(ctx, req.(*GetTopicListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUgcContent_DelTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelTopicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUgcContentServer).DelTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ugc_content.GameUgcContent/DelTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUgcContentServer).DelTopic(ctx, req.(*DelTopicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUgcContent_UpsertConfigTab_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertConfigTabReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUgcContentServer).UpsertConfigTab(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ugc_content.GameUgcContent/UpsertConfigTab",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUgcContentServer).UpsertConfigTab(ctx, req.(*UpsertConfigTabReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUgcContent_GetConfigTabs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConfigTabsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUgcContentServer).GetConfigTabs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ugc_content.GameUgcContent/GetConfigTabs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUgcContentServer).GetConfigTabs(ctx, req.(*GetConfigTabsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUgcContent_DelConfigTab_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelConfigTabReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUgcContentServer).DelConfigTab(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ugc_content.GameUgcContent/DelConfigTab",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUgcContentServer).DelConfigTab(ctx, req.(*DelConfigTabReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUgcContent_InsertDefaultConfigTabs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InsertDefaultConfigTabsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUgcContentServer).InsertDefaultConfigTabs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ugc_content.GameUgcContent/InsertDefaultConfigTabs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUgcContentServer).InsertDefaultConfigTabs(ctx, req.(*InsertDefaultConfigTabsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUgcContent_SaveGameConfigTab_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveGameConfigTabReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUgcContentServer).SaveGameConfigTab(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ugc_content.GameUgcContent/SaveGameConfigTab",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUgcContentServer).SaveGameConfigTab(ctx, req.(*SaveGameConfigTabReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUgcContent_GetGameConfigTabList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameConfigTabListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUgcContentServer).GetGameConfigTabList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ugc_content.GameUgcContent/GetGameConfigTabList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUgcContentServer).GetGameConfigTabList(ctx, req.(*GetGameConfigTabListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUgcContent_DelGameConfigTab_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelGameConfigTabReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUgcContentServer).DelGameConfigTab(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ugc_content.GameUgcContent/DelGameConfigTab",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUgcContentServer).DelGameConfigTab(ctx, req.(*DelGameConfigTabReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUgcContent_BatchUpdateConfigTabsBaseInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUpdateConfigTabsBaseInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUgcContentServer).BatchUpdateConfigTabsBaseInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ugc_content.GameUgcContent/BatchUpdateConfigTabsBaseInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUgcContentServer).BatchUpdateConfigTabsBaseInfo(ctx, req.(*BatchUpdateConfigTabsBaseInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUgcContent_UpsertBanUserPostConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertBanUserPostConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUgcContentServer).UpsertBanUserPostConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ugc_content.GameUgcContent/UpsertBanUserPostConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUgcContentServer).UpsertBanUserPostConfig(ctx, req.(*UpsertBanUserPostConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUgcContent_GetBanUserPostConfigList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBanUserPostConfigListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUgcContentServer).GetBanUserPostConfigList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ugc_content.GameUgcContent/GetBanUserPostConfigList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUgcContentServer).GetBanUserPostConfigList(ctx, req.(*GetBanUserPostConfigListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUgcContent_DelBanUserPostConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelBanUserPostConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUgcContentServer).DelBanUserPostConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ugc_content.GameUgcContent/DelBanUserPostConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUgcContentServer).DelBanUserPostConfig(ctx, req.(*DelBanUserPostConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUgcContent_BatchAddBanUserPostConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchAddBanUserPostConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUgcContentServer).BatchAddBanUserPostConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ugc_content.GameUgcContent/BatchAddBanUserPostConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUgcContentServer).BatchAddBanUserPostConfig(ctx, req.(*BatchAddBanUserPostConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUgcContent_GetGameConfigTabDetailByTabId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameConfigTabDetailByTabIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUgcContentServer).GetGameConfigTabDetailByTabId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ugc_content.GameUgcContent/GetGameConfigTabDetailByTabId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUgcContentServer).GetGameConfigTabDetailByTabId(ctx, req.(*GetGameConfigTabDetailByTabIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUgcContent_GetConfigTabInfoById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConfigTabInfoByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUgcContentServer).GetConfigTabInfoById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ugc_content.GameUgcContent/GetConfigTabInfoById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUgcContentServer).GetConfigTabInfoById(ctx, req.(*GetConfigTabInfoByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUgcContent_GetActiveBanUserPostConfigWithCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActiveBanUserPostConfigWithCacheReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUgcContentServer).GetActiveBanUserPostConfigWithCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ugc_content.GameUgcContent/GetActiveBanUserPostConfigWithCache",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUgcContentServer).GetActiveBanUserPostConfigWithCache(ctx, req.(*GetActiveBanUserPostConfigWithCacheReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUgcContent_GetGamePalTabs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGamePalTabsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUgcContentServer).GetGamePalTabs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ugc_content.GameUgcContent/GetGamePalTabs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUgcContentServer).GetGamePalTabs(ctx, req.(*GetGamePalTabsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUgcContent_BatchGetGameFeedPbs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetGameFeedPbsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUgcContentServer).BatchGetGameFeedPbs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ugc_content.GameUgcContent/BatchGetGameFeedPbs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUgcContentServer).BatchGetGameFeedPbs(ctx, req.(*BatchGetGameFeedPbsReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GameUgcContent_serviceDesc = grpc.ServiceDesc{
	ServiceName: "game_ugc_content.GameUgcContent",
	HandlerType: (*GameUgcContentServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpsertTopic",
			Handler:    _GameUgcContent_UpsertTopic_Handler,
		},
		{
			MethodName: "GetTopicList",
			Handler:    _GameUgcContent_GetTopicList_Handler,
		},
		{
			MethodName: "DelTopic",
			Handler:    _GameUgcContent_DelTopic_Handler,
		},
		{
			MethodName: "UpsertConfigTab",
			Handler:    _GameUgcContent_UpsertConfigTab_Handler,
		},
		{
			MethodName: "GetConfigTabs",
			Handler:    _GameUgcContent_GetConfigTabs_Handler,
		},
		{
			MethodName: "DelConfigTab",
			Handler:    _GameUgcContent_DelConfigTab_Handler,
		},
		{
			MethodName: "InsertDefaultConfigTabs",
			Handler:    _GameUgcContent_InsertDefaultConfigTabs_Handler,
		},
		{
			MethodName: "SaveGameConfigTab",
			Handler:    _GameUgcContent_SaveGameConfigTab_Handler,
		},
		{
			MethodName: "GetGameConfigTabList",
			Handler:    _GameUgcContent_GetGameConfigTabList_Handler,
		},
		{
			MethodName: "DelGameConfigTab",
			Handler:    _GameUgcContent_DelGameConfigTab_Handler,
		},
		{
			MethodName: "BatchUpdateConfigTabsBaseInfo",
			Handler:    _GameUgcContent_BatchUpdateConfigTabsBaseInfo_Handler,
		},
		{
			MethodName: "UpsertBanUserPostConfig",
			Handler:    _GameUgcContent_UpsertBanUserPostConfig_Handler,
		},
		{
			MethodName: "GetBanUserPostConfigList",
			Handler:    _GameUgcContent_GetBanUserPostConfigList_Handler,
		},
		{
			MethodName: "DelBanUserPostConfig",
			Handler:    _GameUgcContent_DelBanUserPostConfig_Handler,
		},
		{
			MethodName: "BatchAddBanUserPostConfig",
			Handler:    _GameUgcContent_BatchAddBanUserPostConfig_Handler,
		},
		{
			MethodName: "GetGameConfigTabDetailByTabId",
			Handler:    _GameUgcContent_GetGameConfigTabDetailByTabId_Handler,
		},
		{
			MethodName: "GetConfigTabInfoById",
			Handler:    _GameUgcContent_GetConfigTabInfoById_Handler,
		},
		{
			MethodName: "GetActiveBanUserPostConfigWithCache",
			Handler:    _GameUgcContent_GetActiveBanUserPostConfigWithCache_Handler,
		},
		{
			MethodName: "GetGamePalTabs",
			Handler:    _GameUgcContent_GetGamePalTabs_Handler,
		},
		{
			MethodName: "BatchGetGameFeedPbs",
			Handler:    _GameUgcContent_BatchGetGameFeedPbs_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "game-ugc-content/game-ugc-content.proto",
}

func init() {
	proto.RegisterFile("game-ugc-content/game-ugc-content.proto", fileDescriptor_game_ugc_content_556dee98c2728bd3)
}

var fileDescriptor_game_ugc_content_556dee98c2728bd3 = []byte{
	// 2784 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x5a, 0x5f, 0x73, 0xdb, 0xc6,
	0xb5, 0x17, 0x48, 0x89, 0x12, 0x0f, 0x09, 0x0a, 0x5a, 0xd9, 0x26, 0x4d, 0xc5, 0x8e, 0x04, 0xc7,
	0xb6, 0xac, 0x89, 0xe5, 0x7b, 0xe5, 0x38, 0x37, 0xd7, 0x37, 0xb9, 0x53, 0x89, 0x52, 0x24, 0xb5,
	0xb6, 0xac, 0x81, 0xa4, 0x64, 0x92, 0x99, 0x16, 0x86, 0x80, 0x15, 0x85, 0x09, 0x04, 0x40, 0x58,
	0xd0, 0x89, 0xda, 0x4e, 0x1f, 0x3a, 0x9d, 0x3e, 0xf4, 0xb5, 0x9d, 0xc9, 0x4b, 0x67, 0xfa, 0xd2,
	0x7e, 0x82, 0xbe, 0xf5, 0xa9, 0xaf, 0xed, 0x53, 0x3f, 0x40, 0xbf, 0x43, 0xbf, 0x42, 0x67, 0x77,
	0x01, 0xe2, 0xdf, 0x82, 0xa4, 0xf2, 0xe7, 0xa1, 0x6f, 0xd8, 0xb3, 0x67, 0x77, 0xcf, 0xf9, 0x9d,
	0x3f, 0x7b, 0xce, 0x92, 0xf0, 0xb0, 0x6f, 0x5c, 0xe0, 0xc7, 0x83, 0xbe, 0xf9, 0xd8, 0xf4, 0xdc,
	0x10, 0xbb, 0xe1, 0x93, 0x3c, 0x61, 0xdd, 0x0f, 0xbc, 0xd0, 0x43, 0x0a, 0xa5, 0xeb, 0x83, 0xbe,
	0xa9, 0x47, 0x74, 0xf5, 0xef, 0x12, 0xcc, 0x1c, 0x7b, 0xbe, 0x6d, 0xa2, 0xdb, 0x30, 0x17, 0xd2,
	0x0f, 0xdd, 0xb6, 0x3a, 0xd2, 0xb2, 0xb4, 0x5a, 0xd7, 0x66, 0xd9, 0x78, 0xdf, 0x42, 0x77, 0x00,
	0xf8, 0x94, 0x6b, 0x5c, 0xe0, 0x4e, 0x85, 0x4d, 0xd6, 0x19, 0xe5, 0xc0, 0xb8, 0xc0, 0x74, 0xda,
	0xf7, 0x48, 0xa8, 0x9b, 0xde, 0xc0, 0x0d, 0x3b, 0xd5, 0x65, 0x69, 0x55, 0xd6, 0xea, 0x94, 0xd2,
	0xa3, 0x04, 0xb4, 0x04, 0x75, 0x33, 0xc0, 0x46, 0x88, 0x75, 0x23, 0xec, 0x4c, 0x2f, 0x4b, 0xab,
	0x55, 0x6d, 0x8e, 0x13, 0x36, 0x43, 0xf4, 0x2e, 0x20, 0xcb, 0x26, 0xbe, 0x63, 0x5c, 0xe9, 0xa9,
	0x23, 0x66, 0xd8, 0x11, 0x4a, 0x34, 0x73, 0x9c, 0x3e, 0xc9, 0x26, 0x7a, 0x44, 0xee, 0xd4, 0x96,
	0xa5, 0xd5, 0x39, 0xad, 0x6e, 0x93, 0x6d, 0x4e, 0x50, 0xbf, 0x96, 0xa0, 0x75, 0xe2, 0x13, 0x1c,
	0x84, 0x6c, 0x89, 0x86, 0x2f, 0xbf, 0x85, 0x56, 0x62, 0xc9, 0xaa, 0x13, 0x49, 0x36, 0x9d, 0x97,
	0x6c, 0x01, 0xe6, 0x33, 0x82, 0x11, 0x5f, 0xfd, 0xab, 0x04, 0xf3, 0xbb, 0x98, 0x13, 0x5e, 0xd8,
	0x24, 0xa4, 0xd2, 0x22, 0x98, 0xf6, 0x8d, 0x3e, 0x66, 0x92, 0xca, 0x1a, 0xfb, 0xa6, 0x34, 0x62,
	0xff, 0x94, 0x0b, 0x28, 0x6b, 0xec, 0x9b, 0x9e, 0xe6, 0x62, 0x6c, 0xa5, 0x10, 0x9f, 0xd3, 0xea,
	0x94, 0xc2, 0x11, 0x4f, 0x2b, 0x3d, 0x3d, 0x4a, 0xe9, 0x99, 0xc9, 0x94, 0xae, 0x89, 0x95, 0x56,
	0x5f, 0x83, 0x92, 0xd5, 0x80, 0xf8, 0xe8, 0xfd, 0xf8, 0x00, 0xc7, 0x26, 0x61, 0x47, 0x5a, 0xae,
	0xae, 0x36, 0x36, 0xda, 0xeb, 0x79, 0xbf, 0x5b, 0xe7, 0x38, 0xf0, 0x93, 0xe9, 0x5a, 0x74, 0x03,
	0x66, 0x42, 0x2f, 0x34, 0x9c, 0x48, 0x4f, 0x3e, 0x50, 0x57, 0xa1, 0xb1, 0x8d, 0x9d, 0x09, 0xac,
	0xa9, 0xb6, 0xa0, 0x99, 0x70, 0x12, 0x5f, 0xfd, 0x75, 0x0d, 0xea, 0x3d, 0xcf, 0x3d, 0xb3, 0xfb,
	0xc7, 0xc6, 0x29, 0x52, 0x41, 0x36, 0xd9, 0x40, 0x0f, 0x8d, 0xd3, 0x64, 0x75, 0xc3, 0x8c, 0x39,
	0xf6, 0x2d, 0xf4, 0x00, 0xe6, 0x53, 0x3c, 0x29, 0xa7, 0x90, 0x87, 0x5c, 0x0c, 0xa3, 0xe7, 0x00,
	0x66, 0xe0, 0x7d, 0x69, 0xe9, 0xe1, 0x95, 0xcf, 0x1d, 0xa2, 0xb5, 0xb1, 0x54, 0xd4, 0xb0, 0x47,
	0x79, 0x8e, 0xaf, 0x7c, 0xac, 0xd5, 0xcd, 0xf8, 0x13, 0xbd, 0x03, 0x2d, 0xbe, 0xb6, 0x1f, 0x78,
	0x03, 0x3f, 0xb1, 0x4f, 0x93, 0x51, 0x77, 0x29, 0x71, 0xdf, 0x42, 0x1f, 0x82, 0x6c, 0xe1, 0x33,
	0x63, 0xe0, 0x84, 0xdc, 0x0a, 0xcc, 0x4e, 0x23, 0x60, 0x6c, 0x46, 0xdc, 0x3c, 0x90, 0x9f, 0x43,
	0xd3, 0x0b, 0xcf, 0x71, 0xc0, 0xd7, 0x92, 0x4e, 0x6d, 0xb4, 0x0d, 0x1a, 0x8c, 0x99, 0x7d, 0x13,
	0x74, 0x0b, 0x6a, 0xa1, 0x1d, 0x3a, 0x98, 0x74, 0x66, 0x97, 0xab, 0xab, 0x75, 0x2d, 0x1a, 0xa1,
	0x9b, 0x50, 0x8b, 0x80, 0x9b, 0x8b, 0xcc, 0xc3, 0x20, 0xdb, 0xcd, 0x40, 0xc6, 0xf0, 0xa8, 0x33,
	0x3c, 0xde, 0x16, 0xe0, 0x11, 0x83, 0xc8, 0x30, 0x49, 0x30, 0x65, 0xb8, 0xac, 0x82, 0x92, 0xc6,
	0x85, 0x81, 0x0f, 0x0c, 0x99, 0x56, 0x82, 0x0c, 0x43, 0xff, 0x29, 0xdc, 0x4a, 0x73, 0x0e, 0x08,
	0x0e, 0xa2, 0x30, 0x68, 0x30, 0xc9, 0x16, 0x13, 0xfe, 0x13, 0x82, 0x03, 0x1e, 0x10, 0xef, 0x43,
	0x3d, 0xc0, 0xa6, 0x1e, 0x0c, 0xa8, 0x66, 0x4d, 0x86, 0xc7, 0xed, 0xa2, 0x84, 0x1a, 0x36, 0xb5,
	0x81, 0x83, 0xb5, 0xb9, 0x80, 0x7f, 0x10, 0x6a, 0xae, 0x33, 0x2f, 0x30, 0xb1, 0x1e, 0xaf, 0xee,
	0xc8, 0xec, 0x90, 0x26, 0xa3, 0x46, 0xfc, 0xe8, 0xff, 0xa1, 0x49, 0xd5, 0x27, 0x83, 0x08, 0x82,
	0x16, 0x83, 0xe0, 0x2d, 0x01, 0xe0, 0xc6, 0xe9, 0xd1, 0x80, 0xeb, 0x0f, 0xe1, 0xf0, 0x1b, 0xb5,
	0x61, 0xd6, 0x26, 0xfa, 0xb9, 0x6d, 0xe1, 0xce, 0x3c, 0x0b, 0xe5, 0x9a, 0x4d, 0xf6, 0x6c, 0x0b,
	0xa3, 0x1e, 0xc8, 0x2c, 0xb1, 0x86, 0xd8, 0xc1, 0xbe, 0x17, 0x84, 0x1d, 0x85, 0xf9, 0xc1, 0xdd,
	0xe2, 0xce, 0x87, 0x1e, 0x09, 0x8f, 0x23, 0x2e, 0xad, 0xe9, 0xa7, 0x46, 0xea, 0x73, 0x98, 0x8d,
	0x05, 0x5d, 0x4a, 0xc3, 0xc0, 0x73, 0x4c, 0xa2, 0x2b, 0x82, 0xe9, 0x94, 0xcf, 0xb3, 0x6f, 0xf5,
	0x3d, 0x68, 0xa6, 0x77, 0xa6, 0x3c, 0x21, 0xfe, 0x2a, 0x8c, 0xa2, 0x87, 0x7d, 0x23, 0x05, 0xaa,
	0x83, 0xc0, 0x89, 0x96, 0xd1, 0x4f, 0xf5, 0x97, 0x12, 0x20, 0x9e, 0xed, 0x86, 0x36, 0xa7, 0xc1,
	0xfb, 0x14, 0x6a, 0xdc, 0xe8, 0x6c, 0x79, 0x43, 0x18, 0x33, 0x43, 0xfe, 0x88, 0x15, 0xfd, 0x0f,
	0xd4, 0x88, 0x37, 0x08, 0x4c, 0x2e, 0x97, 0xd0, 0xb1, 0x34, 0x7c, 0x39, 0xc0, 0x24, 0x3c, 0x62,
	0x6c, 0x5a, 0xc4, 0xae, 0xde, 0x84, 0xc5, 0x82, 0x0c, 0xc4, 0x57, 0xff, 0x5c, 0x61, 0x39, 0x6b,
	0x48, 0x24, 0xdf, 0x61, 0xda, 0x2d, 0x24, 0x99, 0xe9, 0x89, 0x92, 0xcc, 0x8c, 0x28, 0xc9, 0xa4,
	0x33, 0x5d, 0x2d, 0x9b, 0xc2, 0x93, 0x58, 0x9c, 0x4d, 0xc7, 0x62, 0x82, 0xd4, 0xdc, 0xb5, 0x90,
	0x42, 0x2b, 0xd0, 0x8c, 0x1d, 0x4c, 0xa7, 0x96, 0xac, 0x73, 0xa9, 0x63, 0xda, 0x49, 0xe0, 0xa8,
	0xaf, 0x61, 0x21, 0x07, 0x1a, 0xf1, 0xd1, 0x33, 0x98, 0xe5, 0x32, 0x93, 0x28, 0xcd, 0x8f, 0x34,
	0x68, 0xcc, 0x5b, 0x92, 0xe8, 0x57, 0x60, 0x7e, 0x1b, 0x3b, 0x19, 0x7f, 0x69, 0x41, 0x65, 0x98,
	0xa8, 0x2b, 0xb6, 0xa5, 0x22, 0x50, 0xb2, 0x2c, 0xc4, 0x57, 0xff, 0x28, 0x81, 0xbc, 0x6b, 0x5c,
	0xe0, 0x24, 0xd3, 0x27, 0xe8, 0x48, 0x69, 0x74, 0x3e, 0x84, 0x46, 0x82, 0x3b, 0xe9, 0x54, 0xc6,
	0x0b, 0x0c, 0x43, 0x83, 0x90, 0x42, 0x84, 0x57, 0xaf, 0x17, 0xe1, 0xea, 0x1f, 0x24, 0xb8, 0x71,
	0x64, 0xbc, 0xc1, 0x19, 0x51, 0xa9, 0x8e, 0xbb, 0x30, 0xcf, 0xf6, 0x48, 0x64, 0x8b, 0x82, 0x43,
	0x60, 0xbd, 0xec, 0x62, 0xb9, 0x9f, 0x51, 0x3b, 0x2f, 0x61, 0xe5, 0x9a, 0x12, 0xb6, 0xe1, 0xa6,
	0x40, 0x40, 0xe2, 0xab, 0x3e, 0xb4, 0x77, 0x71, 0x98, 0xa1, 0xc7, 0xd5, 0x4a, 0x09, 0xd4, 0xdf,
	0x56, 0x14, 0x07, 0x3a, 0xe2, 0x13, 0x89, 0x8f, 0x0e, 0xe1, 0x46, 0x0e, 0xaf, 0x74, 0x9d, 0x31,
	0x16, 0xb4, 0x85, 0x7e, 0x7e, 0x57, 0xd5, 0x81, 0xc5, 0x6d, 0xec, 0x14, 0x0c, 0xf3, 0x3d, 0xe9,
	0x76, 0x0b, 0x6e, 0x14, 0x4f, 0x23, 0xbe, 0xea, 0xc2, 0x62, 0x86, 0xb8, 0x8d, 0x43, 0xc3, 0x76,
	0xca, 0xa4, 0xf8, 0x81, 0xc8, 0x99, 0x47, 0x5d, 0xb9, 0xfb, 0xee, 0x99, 0x97, 0x76, 0x68, 0xf5,
	0x0a, 0x96, 0xf3, 0x18, 0xf3, 0x23, 0xb7, 0xae, 0x58, 0x9e, 0xfa, 0x1e, 0x21, 0x38, 0x85, 0x95,
	0x31, 0x47, 0x13, 0x1f, 0x7d, 0x94, 0xbb, 0x2b, 0xee, 0x8f, 0xb1, 0x2c, 0xdf, 0x21, 0xbe, 0x35,
	0xd4, 0xdf, 0x4e, 0x83, 0x9c, 0x51, 0xfe, 0x3f, 0xac, 0x00, 0x5c, 0x05, 0x25, 0x53, 0x00, 0x52,
	0x3e, 0x7e, 0x4d, 0xb4, 0xd2, 0xa5, 0x1e, 0x97, 0x39, 0x55, 0xec, 0xe9, 0xb6, 0xc5, 0xeb, 0xbd,
	0xba, 0x26, 0x27, 0x65, 0xdd, 0xbe, 0x55, 0x5e, 0xd8, 0x09, 0x2a, 0xb8, 0xb9, 0x6f, 0x54, 0xc1,
	0x65, 0x4a, 0xac, 0xfa, 0xb7, 0x29, 0xb1, 0x40, 0x50, 0x62, 0x15, 0x2a, 0xa1, 0xc6, 0x37, 0xa8,
	0x84, 0x3e, 0x62, 0xa9, 0x2c, 0xe3, 0x17, 0x5b, 0x57, 0xdc, 0xd7, 0x27, 0x70, 0x0f, 0xd5, 0x64,
	0x79, 0x49, 0xb0, 0x9c, 0xf8, 0x39, 0x18, 0x6d, 0xf7, 0xcc, 0x2b, 0xcf, 0xe3, 0xd9, 0xa8, 0x4c,
	0x60, 0xa4, 0x43, 0xd5, 0x04, 0x85, 0x3a, 0x36, 0xd5, 0x62, 0x6b, 0x40, 0x08, 0xf3, 0xdd, 0x92,
	0x40, 0x2c, 0xc8, 0x5c, 0x29, 0xba, 0x34, 0xbd, 0x6c, 0xa9, 0xa1, 0xa3, 0xbe, 0x95, 0x0f, 0xd4,
	0x45, 0x76, 0x9d, 0xb3, 0x73, 0x0c, 0x27, 0x2a, 0x82, 0xd4, 0x7f, 0x48, 0x80, 0xf2, 0x54, 0xe2,
	0xa3, 0xcf, 0x80, 0xdd, 0x34, 0xba, 0x6f, 0x38, 0x3c, 0xdb, 0xf0, 0x54, 0xfb, 0x4c, 0x10, 0x90,
	0x85, 0xc5, 0xeb, 0xa9, 0xf1, 0x8e, 0x1b, 0x06, 0x57, 0x5a, 0xa3, 0x9f, 0x50, 0xba, 0x7a, 0xa4,
	0x6b, 0x8a, 0x81, 0x56, 0x93, 0x5f, 0xe0, 0xab, 0x48, 0x51, 0xfa, 0x89, 0x9e, 0xc1, 0xcc, 0x1b,
	0xc3, 0x19, 0xf0, 0x58, 0x9c, 0x00, 0x50, 0xce, 0xfd, 0xbc, 0xf2, 0x81, 0xa4, 0xfe, 0x6a, 0x06,
	0x16, 0xb6, 0x0c, 0x97, 0xf6, 0x01, 0x87, 0xec, 0x39, 0x82, 0x95, 0x94, 0xb9, 0xba, 0x22, 0x9d,
	0x4d, 0x93, 0x7c, 0x26, 0x38, 0x66, 0xcb, 0x70, 0xa3, 0x93, 0x58, 0x4a, 0x33, 0x87, 0xdf, 0xac,
	0x2c, 0x0e, 0x6d, 0x2b, 0x02, 0x99, 0x7d, 0x4f, 0x18, 0xe8, 0x6d, 0x98, 0xe5, 0xc6, 0x23, 0x9d,
	0x99, 0xe5, 0xea, 0xaa, 0xac, 0xd5, 0x98, 0x6d, 0x09, 0x2d, 0x35, 0x49, 0x68, 0x04, 0xa1, 0x1e,
	0xda, 0x51, 0x03, 0x5e, 0xd5, 0xea, 0x8c, 0x72, 0x6c, 0xf3, 0xf2, 0x10, 0xbb, 0x16, 0x9f, 0x9c,
	0x65, 0x93, 0xb3, 0xd8, 0xb5, 0xd8, 0xd4, 0x1d, 0x80, 0x53, 0xc3, 0xd5, 0x03, 0x6c, 0x10, 0xcf,
	0x65, 0xc1, 0x5c, 0xd7, 0xea, 0xa7, 0x86, 0xab, 0x31, 0x02, 0xad, 0xf6, 0x3c, 0x1f, 0x07, 0x46,
	0x88, 0xf9, 0xea, 0x3a, 0x5b, 0xdd, 0x88, 0x68, 0x6c, 0x87, 0x2e, 0xcc, 0xf1, 0xa1, 0x17, 0x44,
	0x4d, 0xd8, 0x70, 0x8c, 0xfe, 0x0b, 0x6a, 0x24, 0x34, 0xc2, 0x01, 0x61, 0x11, 0xd8, 0xda, 0xe8,
	0x14, 0x71, 0x3a, 0x62, 0xf3, 0x5a, 0xc4, 0x27, 0x6c, 0xed, 0x9a, 0xd7, 0x6c, 0xed, 0xe4, 0xf2,
	0xd6, 0xee, 0x1e, 0xc8, 0x8c, 0xd1, 0xb5, 0xcd, 0x2f, 0xd8, 0xde, 0x2d, 0x0e, 0x33, 0x25, 0x1e,
	0x44, 0x34, 0xb4, 0x09, 0x32, 0xc5, 0x84, 0xa7, 0x10, 0x6a, 0xe4, 0x79, 0x26, 0xfc, 0x1d, 0xa1,
	0x91, 0x59, 0x06, 0xa1, 0x26, 0x6e, 0x9c, 0x26, 0x03, 0xd6, 0xe6, 0xd8, 0x16, 0xeb, 0xc0, 0x64,
	0x8d, 0x7e, 0xa2, 0x87, 0x30, 0xff, 0xa5, 0x11, 0xb8, 0xb6, 0xdb, 0xd7, 0x2f, 0x30, 0x21, 0xb4,
	0x81, 0x58, 0xe0, 0x7a, 0x45, 0xe4, 0x97, 0x9c, 0xaa, 0x7e, 0x5d, 0x81, 0xb7, 0xb6, 0x8c, 0xd0,
	0x3c, 0xdf, 0xb4, 0xac, 0x82, 0x3b, 0xd2, 0xec, 0x43, 0xa3, 0x34, 0xa4, 0x3e, 0x20, 0xb1, 0xdc,
	0xcc, 0x07, 0x39, 0x17, 0xa8, 0x8c, 0x72, 0x81, 0xea, 0x28, 0x17, 0x98, 0xce, 0xbb, 0x80, 0x40,
	0xf0, 0x19, 0x91, 0xe0, 0xa8, 0x07, 0xad, 0x0c, 0x6c, 0xfc, 0x6e, 0x19, 0x8b, 0x5b, 0x33, 0x85,
	0x1b, 0xc9, 0x78, 0xd3, 0x6c, 0xd6, 0x9b, 0xd4, 0xb7, 0xe1, 0xce, 0x08, 0x60, 0x88, 0xaf, 0x7e,
	0x06, 0x5d, 0xde, 0xc5, 0x09, 0x71, 0xfb, 0xbf, 0x5c, 0x95, 0x70, 0x4f, 0x28, 0x57, 0x6e, 0x5d,
	0x5c, 0x23, 0xdc, 0x81, 0xa5, 0xd2, 0xad, 0x89, 0xaf, 0xfe, 0x4b, 0x82, 0xa5, 0x5d, 0x5c, 0x9c,
	0xfc, 0x8e, 0x9f, 0xea, 0xe2, 0xd4, 0x31, 0x9d, 0x4a, 0x1d, 0x49, 0x8c, 0xcd, 0x4c, 0x18, 0x63,
	0xdf, 0x85, 0xa1, 0x54, 0x02, 0x6f, 0x95, 0x2b, 0xcc, 0x6a, 0xb2, 0x5c, 0xbf, 0x37, 0x11, 0xdc,
	0x63, 0xfa, 0xbe, 0x47, 0xd0, 0xde, 0xc6, 0x8e, 0xd0, 0xba, 0xf9, 0xfe, 0xaf, 0x0b, 0x1d, 0x31,
	0x2b, 0xf1, 0xd5, 0xbf, 0x49, 0xd0, 0xde, 0x34, 0x43, 0xfb, 0x0d, 0x2e, 0xe6, 0xfb, 0x5c, 0x7e,
	0x97, 0xae, 0x9f, 0xdf, 0xa3, 0xd8, 0xaf, 0x24, 0xb1, 0x5f, 0xcc, 0xee, 0x55, 0x41, 0x76, 0x4f,
	0x2e, 0xee, 0xe9, 0xf4, 0xc5, 0x9d, 0x0d, 0xcf, 0x99, 0x5c, 0x78, 0xaa, 0xbf, 0x93, 0xe0, 0xc1,
	0x2e, 0x0e, 0x4b, 0xd4, 0xf9, 0xd4, 0x0e, 0xcf, 0x7b, 0x86, 0x79, 0x8e, 0x47, 0x94, 0xe8, 0x45,
	0x79, 0x0b, 0x09, 0xb0, 0x7a, 0xdd, 0x04, 0xa8, 0xba, 0xf0, 0x70, 0x22, 0xa9, 0x88, 0x8f, 0x7a,
	0x79, 0x4f, 0x79, 0x54, 0x3c, 0xa7, 0x64, 0xa3, 0xa1, 0xbf, 0xa8, 0xaf, 0x61, 0x99, 0xe5, 0x86,
	0x13, 0xdf, 0x32, 0xc2, 0xa4, 0xd2, 0x27, 0x5b, 0x06, 0xc1, 0xec, 0xa2, 0xc7, 0x97, 0xf9, 0xae,
	0x5e, 0xba, 0x56, 0x57, 0xaf, 0xde, 0x83, 0x95, 0x31, 0x27, 0x10, 0x5f, 0x7d, 0x0a, 0xdd, 0x7d,
	0x97, 0xa6, 0x89, 0x6d, 0x5e, 0x78, 0x67, 0x5f, 0x8e, 0xc4, 0x06, 0xa0, 0xb9, 0xa5, 0x74, 0x11,
	0xf1, 0xd5, 0x3f, 0x49, 0x70, 0x8b, 0x9d, 0x1c, 0x95, 0x4c, 0x1f, 0x63, 0x6c, 0x1d, 0xf2, 0x0d,
	0xdb, 0x30, 0xcb, 0x8c, 0xc4, 0x76, 0x64, 0x85, 0x3a, 0x1d, 0x72, 0xa7, 0x09, 0xf0, 0xa5, 0x9e,
	0x7a, 0x0c, 0x93, 0xb5, 0x7a, 0x80, 0x2f, 0xf9, 0x63, 0x0e, 0x5d, 0x47, 0xa7, 0x07, 0x91, 0x27,
	0xca, 0x5a, 0x2d, 0xc0, 0x97, 0x27, 0xb6, 0x45, 0xd3, 0xef, 0x85, 0x11, 0x7c, 0x81, 0xc3, 0xfd,
	0xd8, 0x0b, 0x87, 0x63, 0x74, 0x17, 0xc0, 0x74, 0x6c, 0xec, 0x32, 0x03, 0x33, 0x47, 0x94, 0xb5,
	0x14, 0x45, 0xd5, 0x60, 0x3e, 0x16, 0xaf, 0xc7, 0x91, 0x44, 0xf7, 0xa1, 0x35, 0x70, 0x2f, 0x8c,
	0x80, 0x9c, 0xd3, 0x7a, 0x30, 0x8e, 0x27, 0x59, 0x93, 0x87, 0x54, 0x16, 0x31, 0x1d, 0xe6, 0x01,
	0x74, 0x05, 0x13, 0xb5, 0xa9, 0xc5, 0x43, 0xf5, 0x1c, 0xda, 0x42, 0xd5, 0x89, 0x8f, 0x5e, 0x02,
	0xeb, 0xcf, 0xf5, 0x33, 0x9e, 0x2f, 0xf9, 0x72, 0x6e, 0xd3, 0x15, 0x71, 0xff, 0x97, 0x92, 0x4c,
	0x63, 0x0f, 0x29, 0x29, 0xc2, 0xda, 0x36, 0xd4, 0x87, 0x2d, 0x18, 0x5a, 0x00, 0x79, 0x38, 0x38,
	0xf0, 0x5c, 0xac, 0x4c, 0x21, 0x05, 0x9a, 0x43, 0xd2, 0xa6, 0xe3, 0x28, 0x12, 0x42, 0xd0, 0x1a,
	0x52, 0x58, 0x0c, 0x2b, 0x95, 0xb5, 0x7f, 0x4a, 0xa9, 0x56, 0x92, 0x6d, 0x75, 0x0b, 0x50, 0x86,
	0xa0, 0x47, 0xfb, 0xdd, 0x81, 0xdb, 0x59, 0x7a, 0xef, 0xdc, 0x70, 0x5d, 0xec, 0xd0, 0x04, 0xaa,
	0x48, 0xe8, 0x36, 0xdc, 0xcc, 0x4e, 0xb3, 0xc8, 0x32, 0x4e, 0x95, 0x0a, 0xba, 0x0b, 0xdd, 0xec,
	0x14, 0x0b, 0x0e, 0x3b, 0xbc, 0xa2, 0x2c, 0x4a, 0xb5, 0xb8, 0x73, 0x3c, 0x7f, 0x84, 0x43, 0x65,
	0xba, 0x38, 0x1d, 0x55, 0xd5, 0x3d, 0x23, 0xb0, 0x94, 0x19, 0xb4, 0x04, 0xed, 0xdc, 0x34, 0x55,
	0xad, 0x77, 0x6e, 0x84, 0x4a, 0x6d, 0xed, 0xbf, 0x01, 0x92, 0x3e, 0x1d, 0xcd, 0xc1, 0xf4, 0xee,
	0xe6, 0xcb, 0x1d, 0x65, 0x8a, 0x49, 0xfb, 0xea, 0xe5, 0xa1, 0xb6, 0xb3, 0xb7, 0x73, 0x70, 0xb4,
	0xff, 0xc9, 0x8e, 0xde, 0xdb, 0xdb, 0x3c, 0x38, 0xd8, 0x79, 0xa1, 0x48, 0x6b, 0x3f, 0x07, 0x39,
	0xf3, 0x90, 0x48, 0x01, 0xc9, 0x10, 0x62, 0x40, 0x96, 0xa0, 0x9d, 0xa5, 0x0f, 0xc5, 0x50, 0x24,
	0xd4, 0x86, 0xc5, 0xec, 0xe4, 0x31, 0x6d, 0x4d, 0x94, 0x0a, 0xd5, 0x26, 0x3b, 0x11, 0x2b, 0x4b,
	0xd7, 0x55, 0xd7, 0x3e, 0x07, 0x39, 0x93, 0xa8, 0xd1, 0x4d, 0x56, 0xe3, 0x27, 0x84, 0xe8, 0xf0,
	0x3c, 0x99, 0xa6, 0x19, 0x45, 0xa2, 0x32, 0x65, 0xc8, 0xbd, 0x61, 0xbe, 0x56, 0x2a, 0x6b, 0xc7,
	0x50, 0xe3, 0x17, 0x2b, 0x6a, 0x01, 0xf0, 0xaf, 0xc4, 0x57, 0xf8, 0x98, 0xa7, 0x2d, 0x45, 0xa2,
	0xfb, 0x73, 0xca, 0xbe, 0xbb, 0x73, 0x76, 0x86, 0x39, 0xb9, 0x42, 0xfd, 0x8c, 0x93, 0x77, 0xbe,
	0xf2, 0xed, 0x00, 0x5b, 0x4a, 0x75, 0xed, 0x27, 0xd0, 0x48, 0x25, 0x55, 0xb4, 0x08, 0xf3, 0xa9,
	0x21, 0xb3, 0xf0, 0x14, 0xea, 0xd2, 0x84, 0x30, 0x24, 0xa6, 0xed, 0x27, 0x51, 0xef, 0x48, 0xcd,
	0xed, 0xd9, 0x56, 0x66, 0xbe, 0xb2, 0xb6, 0x07, 0x75, 0x6d, 0x98, 0x07, 0x10, 0xb4, 0xf6, 0x0f,
	0x3e, 0xd9, 0x7c, 0xb1, 0xbf, 0xad, 0x1f, 0xbd, 0x3a, 0xd1, 0x7a, 0xd4, 0x96, 0x54, 0xa6, 0x9d,
	0x4d, 0xad, 0xb7, 0x17, 0x93, 0x24, 0x74, 0x03, 0x14, 0x6a, 0x68, 0x5d, 0xeb, 0xbd, 0x1c, 0x32,
	0x56, 0xd6, 0x0c, 0x90, 0x4f, 0x32, 0x61, 0xdc, 0x85, 0x5b, 0x27, 0x07, 0x2f, 0x37, 0xb5, 0xa3,
	0xbd, 0xcd, 0x17, 0xfa, 0xf1, 0x67, 0x87, 0x3b, 0xfa, 0xf6, 0xce, 0xc7, 0x9b, 0x27, 0x2f, 0x8e,
	0xb9, 0x75, 0x73, 0x73, 0x87, 0xda, 0xab, 0xe3, 0x57, 0x5b, 0x27, 0x1f, 0x73, 0xeb, 0xe6, 0x26,
	0x7f, 0x78, 0xf4, 0xea, 0x40, 0xa9, 0x6c, 0xfc, 0x65, 0x01, 0x5a, 0x54, 0xfc, 0x93, 0xbe, 0x19,
	0xa7, 0x94, 0x63, 0x68, 0xa4, 0x7e, 0x1b, 0x45, 0xcb, 0xc5, 0x50, 0xcf, 0xfe, 0xa6, 0xdb, 0x5d,
	0x19, 0xc3, 0x41, 0x7c, 0x75, 0x0a, 0x7d, 0x0a, 0xcd, 0xf4, 0x6f, 0x93, 0x68, 0x45, 0xd8, 0xb0,
	0xa6, 0x7f, 0x7d, 0xed, 0xaa, 0xe3, 0x58, 0xd8, 0xc6, 0x3f, 0x82, 0xb9, 0xf8, 0x87, 0x46, 0x24,
	0xb8, 0x3f, 0x53, 0x3f, 0x57, 0x76, 0xef, 0x8e, 0x9a, 0x66, 0x9b, 0xbd, 0x8e, 0x7f, 0x17, 0x4e,
	0x5e, 0x72, 0xdf, 0x29, 0xd3, 0x2e, 0xfd, 0x3e, 0xd9, 0xbd, 0x3f, 0x01, 0x17, 0x3b, 0xe1, 0x73,
	0x90, 0x33, 0x4f, 0xf7, 0x48, 0xac, 0x65, 0xe6, 0x5a, 0xeb, 0xde, 0x1b, 0xcb, 0x13, 0x63, 0x9c,
	0x7e, 0x91, 0x17, 0x61, 0x9c, 0x7b, 0xd4, 0x17, 0x61, 0x5c, 0x78, 0xd4, 0x9f, 0x42, 0x5f, 0x41,
	0xbb, 0xe4, 0xfe, 0x44, 0xef, 0x16, 0x37, 0x28, 0xbf, 0x9f, 0xbb, 0x8f, 0xaf, 0xc1, 0xcd, 0x4e,
	0x3e, 0x83, 0x85, 0xc2, 0x3b, 0x38, 0x7a, 0x20, 0x28, 0xc0, 0x05, 0xaf, 0xf9, 0xdd, 0x87, 0x13,
	0xf1, 0xb1, 0x73, 0x3c, 0xb8, 0x21, 0x7a, 0xe4, 0x46, 0x8f, 0x4a, 0xdf, 0x55, 0xf2, 0xcf, 0xef,
	0xdd, 0xb5, 0x49, 0x59, 0xd9, 0x81, 0x26, 0xfb, 0xf5, 0x24, 0xab, 0xd7, 0x7d, 0xa1, 0x31, 0x0a,
	0x6a, 0x3d, 0x98, 0x84, 0x8d, 0x1d, 0xf2, 0x1b, 0x29, 0x6a, 0xe8, 0xca, 0x4a, 0x2a, 0xb4, 0x21,
	0xaa, 0x38, 0x47, 0x57, 0x79, 0xdd, 0xa7, 0xd7, 0x5e, 0x13, 0x3b, 0x51, 0x49, 0x83, 0x27, 0x72,
	0xa2, 0xf2, 0x36, 0x53, 0xe4, 0x44, 0xa3, 0x3a, 0xc7, 0x29, 0xf4, 0x33, 0xf6, 0x52, 0x28, 0xec,
	0xa4, 0xd0, 0x63, 0xa1, 0xd5, 0xca, 0xda, 0xcc, 0xee, 0xfa, 0x75, 0xd8, 0x63, 0xcf, 0x12, 0xb5,
	0x49, 0x22, 0xcf, 0x2a, 0xe9, 0xbc, 0x44, 0x9e, 0x55, 0xda, 0x79, 0x4d, 0xa1, 0x5f, 0xc0, 0xed,
	0xd2, 0x26, 0x1e, 0xad, 0x97, 0xd8, 0xae, 0xe4, 0x29, 0xa4, 0xfb, 0xe4, 0x5a, 0xfc, 0x43, 0xa7,
	0x1b, 0xf9, 0x8b, 0x82, 0xc8, 0xe9, 0xc6, 0xfd, 0xfa, 0x21, 0x72, 0xba, 0xb1, 0x3f, 0x5b, 0x0c,
	0xe3, 0xba, 0xf0, 0x48, 0x5c, 0x12, 0xd7, 0xa2, 0xb7, 0xe8, 0x92, 0xb8, 0x16, 0xbe, 0x3b, 0xab,
	0x53, 0xe8, 0xf7, 0x12, 0xdc, 0x9b, 0xa0, 0x2f, 0x43, 0x1f, 0x08, 0x77, 0x9d, 0xa0, 0xc9, 0xec,
	0xfe, 0xef, 0x37, 0x5c, 0xc9, 0xc4, 0xfb, 0x31, 0xb4, 0xb2, 0xef, 0xc2, 0xe8, 0xde, 0xf8, 0x97,
	0xe3, 0xcb, 0xee, 0x3b, 0x93, 0x3c, 0x2f, 0xab, 0x53, 0xc8, 0x81, 0x45, 0x41, 0x37, 0x81, 0x56,
	0x4b, 0xbc, 0xa8, 0xd0, 0x6f, 0x75, 0x1f, 0x4d, 0xc8, 0x49, 0x4f, 0xdb, 0x7a, 0xef, 0xf3, 0x8d,
	0xbe, 0xe7, 0x18, 0x6e, 0x7f, 0xfd, 0xd9, 0x46, 0x18, 0xae, 0x9b, 0xde, 0xc5, 0x13, 0xf6, 0xbf,
	0x3a, 0xd3, 0x73, 0x9e, 0x10, 0x1c, 0xbc, 0xb1, 0x4d, 0x4c, 0x0a, 0x7f, 0xbd, 0x3b, 0xad, 0x31,
	0x9e, 0xa7, 0xff, 0x0e, 0x00, 0x00, 0xff, 0xff, 0xe1, 0x3f, 0x29, 0xee, 0xa6, 0x27, 0x00, 0x00,
}
