// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/esport-trade/esport-trade.proto

package esport_trade // import "golang.52tt.com/protocol/services/esport-trade"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"
import _ "golang.52tt.com/protocol/services/extension/options"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 订单状态
type OrderStatus int32

const (
	OrderStatus_ORDER_STATUS_UNSPECIFIED  OrderStatus = 0
	OrderStatus_ORDER_STATUS_PAYED        OrderStatus = 1
	OrderStatus_ORDER_STATUS_RECEIVED     OrderStatus = 2
	OrderStatus_ORDER_STATUS_IN_REFUNDING OrderStatus = 3
	OrderStatus_ORDER_STATUS_FINISHED     OrderStatus = 4
	OrderStatus_ORDER_STATUS_CANCELED     OrderStatus = 5
	OrderStatus_ORDER_STATUS_REFUNDED     OrderStatus = 6
)

var OrderStatus_name = map[int32]string{
	0: "ORDER_STATUS_UNSPECIFIED",
	1: "ORDER_STATUS_PAYED",
	2: "ORDER_STATUS_RECEIVED",
	3: "ORDER_STATUS_IN_REFUNDING",
	4: "ORDER_STATUS_FINISHED",
	5: "ORDER_STATUS_CANCELED",
	6: "ORDER_STATUS_REFUNDED",
}
var OrderStatus_value = map[string]int32{
	"ORDER_STATUS_UNSPECIFIED":  0,
	"ORDER_STATUS_PAYED":        1,
	"ORDER_STATUS_RECEIVED":     2,
	"ORDER_STATUS_IN_REFUNDING": 3,
	"ORDER_STATUS_FINISHED":     4,
	"ORDER_STATUS_CANCELED":     5,
	"ORDER_STATUS_REFUNDED":     6,
}

func (x OrderStatus) String() string {
	return proto.EnumName(OrderStatus_name, int32(x))
}
func (OrderStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{0}
}

// 订单已取消子状态
type CanceledOrderSubStatus int32

const (
	CanceledOrderSubStatus_CANCELED_ORDER_SUB_STATUS_UNSPECIFIED   CanceledOrderSubStatus = 0
	CanceledOrderSubStatus_CANCELED_ORDER_SUB_STATUS_PLAYER_CANCEL CanceledOrderSubStatus = 1
	CanceledOrderSubStatus_CANCELED_ORDER_SUB_STATUS_COACH_REFUSE  CanceledOrderSubStatus = 2
	CanceledOrderSubStatus_CANCELED_ORDER_SUB_STATUS_COACH_TIMEOUT CanceledOrderSubStatus = 3
)

var CanceledOrderSubStatus_name = map[int32]string{
	0: "CANCELED_ORDER_SUB_STATUS_UNSPECIFIED",
	1: "CANCELED_ORDER_SUB_STATUS_PLAYER_CANCEL",
	2: "CANCELED_ORDER_SUB_STATUS_COACH_REFUSE",
	3: "CANCELED_ORDER_SUB_STATUS_COACH_TIMEOUT",
}
var CanceledOrderSubStatus_value = map[string]int32{
	"CANCELED_ORDER_SUB_STATUS_UNSPECIFIED":   0,
	"CANCELED_ORDER_SUB_STATUS_PLAYER_CANCEL": 1,
	"CANCELED_ORDER_SUB_STATUS_COACH_REFUSE":  2,
	"CANCELED_ORDER_SUB_STATUS_COACH_TIMEOUT": 3,
}

func (x CanceledOrderSubStatus) String() string {
	return proto.EnumName(CanceledOrderSubStatus_name, int32(x))
}
func (CanceledOrderSubStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{1}
}

// 订单进行中的子状态
type ReceivedOrderSubStatus int32

const (
	ReceivedOrderSubStatus_RECEIVED_ORDER_SUB_STATUS_UNSPECIFIED       ReceivedOrderSubStatus = 0
	ReceivedOrderSubStatus_RECEIVED_ORDER_SUB_STATUS_NOT_NOTIFY_FINISH ReceivedOrderSubStatus = 1
	ReceivedOrderSubStatus_RECEIVED_ORDER_SUB_STATUS_NOTIFY_FINISH     ReceivedOrderSubStatus = 2
)

var ReceivedOrderSubStatus_name = map[int32]string{
	0: "RECEIVED_ORDER_SUB_STATUS_UNSPECIFIED",
	1: "RECEIVED_ORDER_SUB_STATUS_NOT_NOTIFY_FINISH",
	2: "RECEIVED_ORDER_SUB_STATUS_NOTIFY_FINISH",
}
var ReceivedOrderSubStatus_value = map[string]int32{
	"RECEIVED_ORDER_SUB_STATUS_UNSPECIFIED":       0,
	"RECEIVED_ORDER_SUB_STATUS_NOT_NOTIFY_FINISH": 1,
	"RECEIVED_ORDER_SUB_STATUS_NOTIFY_FINISH":     2,
}

func (x ReceivedOrderSubStatus) String() string {
	return proto.EnumName(ReceivedOrderSubStatus_name, int32(x))
}
func (ReceivedOrderSubStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{2}
}

type PayType int32

const (
	PayType_PAY_TYPE_UNSPECIFIED PayType = 0
	PayType_PAY_TYPE_ALIPAY      PayType = 1
	PayType_PAY_TYPE_WECHAT      PayType = 2
)

var PayType_name = map[int32]string{
	0: "PAY_TYPE_UNSPECIFIED",
	1: "PAY_TYPE_ALIPAY",
	2: "PAY_TYPE_WECHAT",
}
var PayType_value = map[string]int32{
	"PAY_TYPE_UNSPECIFIED": 0,
	"PAY_TYPE_ALIPAY":      1,
	"PAY_TYPE_WECHAT":      2,
}

func (x PayType) String() string {
	return proto.EnumName(PayType_name, int32(x))
}
func (PayType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{3}
}

type ManualGrantType int32

const (
	ManualGrantType_MANUAL_GRANT_TYPE_UNSPECIFIED ManualGrantType = 0
	ManualGrantType_MANUAL_GRANT_TYPE_UID_LIST    ManualGrantType = 1
	ManualGrantType_MANUAL_GRANT_TYPE_TTID_LIST   ManualGrantType = 2
	ManualGrantType_MANUAL_GRANT_TYPE_UID_FILE    ManualGrantType = 3
	ManualGrantType_MANUAL_GRANT_TYPE_TTID_FILE   ManualGrantType = 4
	ManualGrantType_MANUAL_GRANT_TYPE_GROUP_ID    ManualGrantType = 5
)

var ManualGrantType_name = map[int32]string{
	0: "MANUAL_GRANT_TYPE_UNSPECIFIED",
	1: "MANUAL_GRANT_TYPE_UID_LIST",
	2: "MANUAL_GRANT_TYPE_TTID_LIST",
	3: "MANUAL_GRANT_TYPE_UID_FILE",
	4: "MANUAL_GRANT_TYPE_TTID_FILE",
	5: "MANUAL_GRANT_TYPE_GROUP_ID",
}
var ManualGrantType_value = map[string]int32{
	"MANUAL_GRANT_TYPE_UNSPECIFIED": 0,
	"MANUAL_GRANT_TYPE_UID_LIST":    1,
	"MANUAL_GRANT_TYPE_TTID_LIST":   2,
	"MANUAL_GRANT_TYPE_UID_FILE":    3,
	"MANUAL_GRANT_TYPE_TTID_FILE":   4,
	"MANUAL_GRANT_TYPE_GROUP_ID":    5,
}

func (x ManualGrantType) String() string {
	return proto.EnumName(ManualGrantType_name, int32(x))
}
func (ManualGrantType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{4}
}

type CouponTaskStatus int32

const (
	CouponTaskStatus_COUPON_TASK_STATUS_INIT       CouponTaskStatus = 0
	CouponTaskStatus_COUPON_TASK_STATUS_GRANTED    CouponTaskStatus = 1
	CouponTaskStatus_COUPON_TASK_STATUS_GRANTING   CouponTaskStatus = 2
	CouponTaskStatus_COUPON_TASK_STATUS_TERMINATED CouponTaskStatus = 3
	CouponTaskStatus_COUPON_TASK_STATUS_REVOKING   CouponTaskStatus = 4
	CouponTaskStatus_COUPON_TASK_STATUS_REVOKED    CouponTaskStatus = 5
)

var CouponTaskStatus_name = map[int32]string{
	0: "COUPON_TASK_STATUS_INIT",
	1: "COUPON_TASK_STATUS_GRANTED",
	2: "COUPON_TASK_STATUS_GRANTING",
	3: "COUPON_TASK_STATUS_TERMINATED",
	4: "COUPON_TASK_STATUS_REVOKING",
	5: "COUPON_TASK_STATUS_REVOKED",
}
var CouponTaskStatus_value = map[string]int32{
	"COUPON_TASK_STATUS_INIT":       0,
	"COUPON_TASK_STATUS_GRANTED":    1,
	"COUPON_TASK_STATUS_GRANTING":   2,
	"COUPON_TASK_STATUS_TERMINATED": 3,
	"COUPON_TASK_STATUS_REVOKING":   4,
	"COUPON_TASK_STATUS_REVOKED":    5,
}

func (x CouponTaskStatus) String() string {
	return proto.EnumName(CouponTaskStatus_name, int32(x))
}
func (CouponTaskStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{5}
}

type CouponGainSource int32

const (
	CouponGainSource_GAIN_SOURCE_UNSPECIFIED  CouponGainSource = 0
	CouponGainSource_GAIN_SOURCE_COMMIT_ORDER CouponGainSource = 1
)

var CouponGainSource_name = map[int32]string{
	0: "GAIN_SOURCE_UNSPECIFIED",
	1: "GAIN_SOURCE_COMMIT_ORDER",
}
var CouponGainSource_value = map[string]int32{
	"GAIN_SOURCE_UNSPECIFIED":  0,
	"GAIN_SOURCE_COMMIT_ORDER": 1,
}

func (x CouponGainSource) String() string {
	return proto.EnumName(CouponGainSource_name, int32(x))
}
func (CouponGainSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{6}
}

type CouponStatus int32

const (
	CouponStatus_COUPON_STATUS_UNUSED  CouponStatus = 0
	CouponStatus_COUPON_STATUS_EXPIRED CouponStatus = 1
)

var CouponStatus_name = map[int32]string{
	0: "COUPON_STATUS_UNUSED",
	1: "COUPON_STATUS_EXPIRED",
}
var CouponStatus_value = map[string]int32{
	"COUPON_STATUS_UNUSED":  0,
	"COUPON_STATUS_EXPIRED": 1,
}

func (x CouponStatus) String() string {
	return proto.EnumName(CouponStatus_name, int32(x))
}
func (CouponStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{7}
}

type GetOrderListRequest_OrderType int32

const (
	GetOrderListRequest_ORDER_TYPE_UNSPECIFIED  GetOrderListRequest_OrderType = 0
	GetOrderListRequest_ORDER_TYPE_PLAYER_ORDER GetOrderListRequest_OrderType = 1
	GetOrderListRequest_ORDER_TYPE_COACH_ORDER  GetOrderListRequest_OrderType = 2
)

var GetOrderListRequest_OrderType_name = map[int32]string{
	0: "ORDER_TYPE_UNSPECIFIED",
	1: "ORDER_TYPE_PLAYER_ORDER",
	2: "ORDER_TYPE_COACH_ORDER",
}
var GetOrderListRequest_OrderType_value = map[string]int32{
	"ORDER_TYPE_UNSPECIFIED":  0,
	"ORDER_TYPE_PLAYER_ORDER": 1,
	"ORDER_TYPE_COACH_ORDER":  2,
}

func (x GetOrderListRequest_OrderType) String() string {
	return proto.EnumName(GetOrderListRequest_OrderType_name, int32(x))
}
func (GetOrderListRequest_OrderType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{22, 0}
}

type GetOrderListRequest_StatusType int32

const (
	GetOrderListRequest_STATUS_TYPE_UNSPECIFIED GetOrderListRequest_StatusType = 0
	GetOrderListRequest_STATUS_TYPE_FINISHED    GetOrderListRequest_StatusType = 1
	GetOrderListRequest_STATUS_TYPE_UNFINISHED  GetOrderListRequest_StatusType = 2
)

var GetOrderListRequest_StatusType_name = map[int32]string{
	0: "STATUS_TYPE_UNSPECIFIED",
	1: "STATUS_TYPE_FINISHED",
	2: "STATUS_TYPE_UNFINISHED",
}
var GetOrderListRequest_StatusType_value = map[string]int32{
	"STATUS_TYPE_UNSPECIFIED": 0,
	"STATUS_TYPE_FINISHED":    1,
	"STATUS_TYPE_UNFINISHED":  2,
}

func (x GetOrderListRequest_StatusType) String() string {
	return proto.EnumName(GetOrderListRequest_StatusType_name, int32(x))
}
func (GetOrderListRequest_StatusType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{22, 1}
}

type CouponConfig_CouponType int32

const (
	CouponConfig_COUPON_TYPE_UNSPECIFIED CouponConfig_CouponType = 0
	CouponConfig_COUPON_TYPE_REDUCE      CouponConfig_CouponType = 1
	CouponConfig_COUPON_TYPE_DISCOUNT    CouponConfig_CouponType = 2
)

var CouponConfig_CouponType_name = map[int32]string{
	0: "COUPON_TYPE_UNSPECIFIED",
	1: "COUPON_TYPE_REDUCE",
	2: "COUPON_TYPE_DISCOUNT",
}
var CouponConfig_CouponType_value = map[string]int32{
	"COUPON_TYPE_UNSPECIFIED": 0,
	"COUPON_TYPE_REDUCE":      1,
	"COUPON_TYPE_DISCOUNT":    2,
}

func (x CouponConfig_CouponType) String() string {
	return proto.EnumName(CouponConfig_CouponType_name, int32(x))
}
func (CouponConfig_CouponType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{55, 0}
}

type CouponConfig_UsageLimitType int32

const (
	CouponConfig_USAGE_LIMIT_TYPE_UNSPECIFIED       CouponConfig_UsageLimitType = 0
	CouponConfig_USAGE_LIMIT_TYPE_NONE              CouponConfig_UsageLimitType = 1
	CouponConfig_USAGE_LIMIT_TYPE_NO_HISTORY_ORDER  CouponConfig_UsageLimitType = 2
	CouponConfig_USAGE_LIMIT_TYPE_NO_ORDER_RECENTLY CouponConfig_UsageLimitType = 3
	CouponConfig_USAGE_LIMIT_TYPE_HAS_HISTORY_ORDER CouponConfig_UsageLimitType = 4
)

var CouponConfig_UsageLimitType_name = map[int32]string{
	0: "USAGE_LIMIT_TYPE_UNSPECIFIED",
	1: "USAGE_LIMIT_TYPE_NONE",
	2: "USAGE_LIMIT_TYPE_NO_HISTORY_ORDER",
	3: "USAGE_LIMIT_TYPE_NO_ORDER_RECENTLY",
	4: "USAGE_LIMIT_TYPE_HAS_HISTORY_ORDER",
}
var CouponConfig_UsageLimitType_value = map[string]int32{
	"USAGE_LIMIT_TYPE_UNSPECIFIED":       0,
	"USAGE_LIMIT_TYPE_NONE":              1,
	"USAGE_LIMIT_TYPE_NO_HISTORY_ORDER":  2,
	"USAGE_LIMIT_TYPE_NO_ORDER_RECENTLY": 3,
	"USAGE_LIMIT_TYPE_HAS_HISTORY_ORDER": 4,
}

func (x CouponConfig_UsageLimitType) String() string {
	return proto.EnumName(CouponConfig_UsageLimitType_name, int32(x))
}
func (CouponConfig_UsageLimitType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{55, 1}
}

type GetOrderHintRequest_Scene int32

const (
	GetOrderHintRequest_SCENE_PAY_ORDER_COACH_GLOBAL    GetOrderHintRequest_Scene = 0
	GetOrderHintRequest_SCENE_FINISH_ORDER_COACH_IM     GetOrderHintRequest_Scene = 1
	GetOrderHintRequest_SCENE_PAY_ORDER_PLAYER_IM       GetOrderHintRequest_Scene = 2
	GetOrderHintRequest_SCENE_SKILL_DETAIL_ORDER_BUTTON GetOrderHintRequest_Scene = 3
)

var GetOrderHintRequest_Scene_name = map[int32]string{
	0: "SCENE_PAY_ORDER_COACH_GLOBAL",
	1: "SCENE_FINISH_ORDER_COACH_IM",
	2: "SCENE_PAY_ORDER_PLAYER_IM",
	3: "SCENE_SKILL_DETAIL_ORDER_BUTTON",
}
var GetOrderHintRequest_Scene_value = map[string]int32{
	"SCENE_PAY_ORDER_COACH_GLOBAL":    0,
	"SCENE_FINISH_ORDER_COACH_IM":     1,
	"SCENE_PAY_ORDER_PLAYER_IM":       2,
	"SCENE_SKILL_DETAIL_ORDER_BUTTON": 3,
}

func (x GetOrderHintRequest_Scene) String() string {
	return proto.EnumName(GetOrderHintRequest_Scene_name, int32(x))
}
func (GetOrderHintRequest_Scene) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{96, 0}
}

type PriceInfo struct {
	Price                 uint32   `protobuf:"varint,1,opt,name=price,proto3" json:"price,omitempty"`
	PriceUnit             string   `protobuf:"bytes,2,opt,name=price_unit,json=priceUnit,proto3" json:"price_unit,omitempty"`
	MeasureCnt            uint32   `protobuf:"varint,3,opt,name=measure_cnt,json=measureCnt,proto3" json:"measure_cnt,omitempty"`
	MeasureUnit           string   `protobuf:"bytes,4,opt,name=measure_unit,json=measureUnit,proto3" json:"measure_unit,omitempty"`
	HasFirstRoundDiscount bool     `protobuf:"varint,6,opt,name=has_first_round_discount,json=hasFirstRoundDiscount,proto3" json:"has_first_round_discount,omitempty"`
	FirstRoundPrice       uint32   `protobuf:"varint,7,opt,name=first_round_price,json=firstRoundPrice,proto3" json:"first_round_price,omitempty"`
	HasDiscount           bool     `protobuf:"varint,8,opt,name=has_discount,json=hasDiscount,proto3" json:"has_discount,omitempty"`
	DiscountPrice         uint32   `protobuf:"varint,9,opt,name=discount_price,json=discountPrice,proto3" json:"discount_price,omitempty"`
	DiscountType          uint32   `protobuf:"varint,10,opt,name=discount_type,json=discountType,proto3" json:"discount_type,omitempty"`
	DiscountDesc          string   `protobuf:"bytes,11,opt,name=discount_desc,json=discountDesc,proto3" json:"discount_desc,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *PriceInfo) Reset()         { *m = PriceInfo{} }
func (m *PriceInfo) String() string { return proto.CompactTextString(m) }
func (*PriceInfo) ProtoMessage()    {}
func (*PriceInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{0}
}
func (m *PriceInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PriceInfo.Unmarshal(m, b)
}
func (m *PriceInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PriceInfo.Marshal(b, m, deterministic)
}
func (dst *PriceInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PriceInfo.Merge(dst, src)
}
func (m *PriceInfo) XXX_Size() int {
	return xxx_messageInfo_PriceInfo.Size(m)
}
func (m *PriceInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PriceInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PriceInfo proto.InternalMessageInfo

func (m *PriceInfo) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *PriceInfo) GetPriceUnit() string {
	if m != nil {
		return m.PriceUnit
	}
	return ""
}

func (m *PriceInfo) GetMeasureCnt() uint32 {
	if m != nil {
		return m.MeasureCnt
	}
	return 0
}

func (m *PriceInfo) GetMeasureUnit() string {
	if m != nil {
		return m.MeasureUnit
	}
	return ""
}

func (m *PriceInfo) GetHasFirstRoundDiscount() bool {
	if m != nil {
		return m.HasFirstRoundDiscount
	}
	return false
}

func (m *PriceInfo) GetFirstRoundPrice() uint32 {
	if m != nil {
		return m.FirstRoundPrice
	}
	return 0
}

func (m *PriceInfo) GetHasDiscount() bool {
	if m != nil {
		return m.HasDiscount
	}
	return false
}

func (m *PriceInfo) GetDiscountPrice() uint32 {
	if m != nil {
		return m.DiscountPrice
	}
	return 0
}

func (m *PriceInfo) GetDiscountType() uint32 {
	if m != nil {
		return m.DiscountType
	}
	return 0
}

func (m *PriceInfo) GetDiscountDesc() string {
	if m != nil {
		return m.DiscountDesc
	}
	return ""
}

// 商品订单
type ProductOrder struct {
	CoachUid             uint32                `protobuf:"varint,1,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	ProductId            uint64                `protobuf:"varint,2,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	Name                 string                `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Icon                 string                `protobuf:"bytes,4,opt,name=icon,proto3" json:"icon,omitempty"`
	PriceInfo            *PriceInfo            `protobuf:"bytes,5,opt,name=price_info,json=priceInfo,proto3" json:"price_info,omitempty"`
	Count                uint32                `protobuf:"varint,6,opt,name=count,proto3" json:"count,omitempty"`
	TotalPrice           uint32                `protobuf:"varint,7,opt,name=total_price,json=totalPrice,proto3" json:"total_price,omitempty"`
	GameId               uint32                `protobuf:"varint,8,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Tag                  string                `protobuf:"bytes,9,opt,name=tag,proto3" json:"tag,omitempty"`
	GuaranteeWinTexts    []string              `protobuf:"bytes,10,rep,name=guarantee_win_texts,json=guaranteeWinTexts,proto3" json:"guarantee_win_texts,omitempty"`
	IsGuaranteeWin       bool                  `protobuf:"varint,11,opt,name=is_guarantee_win,json=isGuaranteeWin,proto3" json:"is_guarantee_win,omitempty"`
	CoachTotalPrice      uint32                `protobuf:"varint,12,opt,name=coach_total_price,json=coachTotalPrice,proto3" json:"coach_total_price,omitempty"`
	CouponUseDetail      *CouponUseDetail      `protobuf:"bytes,13,opt,name=coupon_use_detail,json=couponUseDetail,proto3" json:"coupon_use_detail,omitempty"`
	NewCustomerUseDetail *NewCustomerUseDetail `protobuf:"bytes,14,opt,name=new_customer_use_detail,json=newCustomerUseDetail,proto3" json:"new_customer_use_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *ProductOrder) Reset()         { *m = ProductOrder{} }
func (m *ProductOrder) String() string { return proto.CompactTextString(m) }
func (*ProductOrder) ProtoMessage()    {}
func (*ProductOrder) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{1}
}
func (m *ProductOrder) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductOrder.Unmarshal(m, b)
}
func (m *ProductOrder) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductOrder.Marshal(b, m, deterministic)
}
func (dst *ProductOrder) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductOrder.Merge(dst, src)
}
func (m *ProductOrder) XXX_Size() int {
	return xxx_messageInfo_ProductOrder.Size(m)
}
func (m *ProductOrder) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductOrder.DiscardUnknown(m)
}

var xxx_messageInfo_ProductOrder proto.InternalMessageInfo

func (m *ProductOrder) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

func (m *ProductOrder) GetProductId() uint64 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *ProductOrder) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ProductOrder) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *ProductOrder) GetPriceInfo() *PriceInfo {
	if m != nil {
		return m.PriceInfo
	}
	return nil
}

func (m *ProductOrder) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *ProductOrder) GetTotalPrice() uint32 {
	if m != nil {
		return m.TotalPrice
	}
	return 0
}

func (m *ProductOrder) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *ProductOrder) GetTag() string {
	if m != nil {
		return m.Tag
	}
	return ""
}

func (m *ProductOrder) GetGuaranteeWinTexts() []string {
	if m != nil {
		return m.GuaranteeWinTexts
	}
	return nil
}

func (m *ProductOrder) GetIsGuaranteeWin() bool {
	if m != nil {
		return m.IsGuaranteeWin
	}
	return false
}

func (m *ProductOrder) GetCoachTotalPrice() uint32 {
	if m != nil {
		return m.CoachTotalPrice
	}
	return 0
}

func (m *ProductOrder) GetCouponUseDetail() *CouponUseDetail {
	if m != nil {
		return m.CouponUseDetail
	}
	return nil
}

func (m *ProductOrder) GetNewCustomerUseDetail() *NewCustomerUseDetail {
	if m != nil {
		return m.NewCustomerUseDetail
	}
	return nil
}

// 优惠券使用详情
type CouponUseDetail struct {
	UseCoupon            bool     `protobuf:"varint,1,opt,name=use_coupon,json=useCoupon,proto3" json:"use_coupon,omitempty"`
	CouponMoney          uint32   `protobuf:"varint,2,opt,name=coupon_money,json=couponMoney,proto3" json:"coupon_money,omitempty"`
	CouponIds            []string `protobuf:"bytes,3,rep,name=coupon_ids,json=couponIds,proto3" json:"coupon_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CouponUseDetail) Reset()         { *m = CouponUseDetail{} }
func (m *CouponUseDetail) String() string { return proto.CompactTextString(m) }
func (*CouponUseDetail) ProtoMessage()    {}
func (*CouponUseDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{2}
}
func (m *CouponUseDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CouponUseDetail.Unmarshal(m, b)
}
func (m *CouponUseDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CouponUseDetail.Marshal(b, m, deterministic)
}
func (dst *CouponUseDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CouponUseDetail.Merge(dst, src)
}
func (m *CouponUseDetail) XXX_Size() int {
	return xxx_messageInfo_CouponUseDetail.Size(m)
}
func (m *CouponUseDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_CouponUseDetail.DiscardUnknown(m)
}

var xxx_messageInfo_CouponUseDetail proto.InternalMessageInfo

func (m *CouponUseDetail) GetUseCoupon() bool {
	if m != nil {
		return m.UseCoupon
	}
	return false
}

func (m *CouponUseDetail) GetCouponMoney() uint32 {
	if m != nil {
		return m.CouponMoney
	}
	return 0
}

func (m *CouponUseDetail) GetCouponIds() []string {
	if m != nil {
		return m.CouponIds
	}
	return nil
}

// 新客优惠使用详情
type NewCustomerUseDetail struct {
	UseNewCustomerDiscount bool     `protobuf:"varint,1,opt,name=use_new_customer_discount,json=useNewCustomerDiscount,proto3" json:"use_new_customer_discount,omitempty"`
	NewCustomerPrice       uint32   `protobuf:"varint,2,opt,name=new_customer_price,json=newCustomerPrice,proto3" json:"new_customer_price,omitempty"`
	PlatBonusFee           uint32   `protobuf:"varint,3,opt,name=plat_bonus_fee,json=platBonusFee,proto3" json:"plat_bonus_fee,omitempty"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *NewCustomerUseDetail) Reset()         { *m = NewCustomerUseDetail{} }
func (m *NewCustomerUseDetail) String() string { return proto.CompactTextString(m) }
func (*NewCustomerUseDetail) ProtoMessage()    {}
func (*NewCustomerUseDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{3}
}
func (m *NewCustomerUseDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewCustomerUseDetail.Unmarshal(m, b)
}
func (m *NewCustomerUseDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewCustomerUseDetail.Marshal(b, m, deterministic)
}
func (dst *NewCustomerUseDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewCustomerUseDetail.Merge(dst, src)
}
func (m *NewCustomerUseDetail) XXX_Size() int {
	return xxx_messageInfo_NewCustomerUseDetail.Size(m)
}
func (m *NewCustomerUseDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_NewCustomerUseDetail.DiscardUnknown(m)
}

var xxx_messageInfo_NewCustomerUseDetail proto.InternalMessageInfo

func (m *NewCustomerUseDetail) GetUseNewCustomerDiscount() bool {
	if m != nil {
		return m.UseNewCustomerDiscount
	}
	return false
}

func (m *NewCustomerUseDetail) GetNewCustomerPrice() uint32 {
	if m != nil {
		return m.NewCustomerPrice
	}
	return 0
}

func (m *NewCustomerUseDetail) GetPlatBonusFee() uint32 {
	if m != nil {
		return m.PlatBonusFee
	}
	return 0
}

// 商品订单详情
type SkillProductOrderDetail struct {
	PlayerUid            uint32        `protobuf:"varint,1,opt,name=player_uid,json=playerUid,proto3" json:"player_uid,omitempty"`
	ProductOrder         *ProductOrder `protobuf:"bytes,2,opt,name=product_order,json=productOrder,proto3" json:"product_order,omitempty"`
	OrderId              string        `protobuf:"bytes,3,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Status               uint32        `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	SubStatus            uint32        `protobuf:"varint,5,opt,name=sub_status,json=subStatus,proto3" json:"sub_status,omitempty"`
	PayTime              int64         `protobuf:"varint,6,opt,name=pay_time,json=payTime,proto3" json:"pay_time,omitempty"`
	ReceiveTime          int64         `protobuf:"varint,7,opt,name=receive_time,json=receiveTime,proto3" json:"receive_time,omitempty"`
	FinishTime           int64         `protobuf:"varint,8,opt,name=finish_time,json=finishTime,proto3" json:"finish_time,omitempty"`
	CancelTime           int64         `protobuf:"varint,9,opt,name=cancel_time,json=cancelTime,proto3" json:"cancel_time,omitempty"`
	OrderEndTime         int64         `protobuf:"varint,10,opt,name=order_end_time,json=orderEndTime,proto3" json:"order_end_time,omitempty"`
	Desc                 string        `protobuf:"bytes,11,opt,name=desc,proto3" json:"desc,omitempty"`
	OrderNumber          string        `protobuf:"bytes,12,opt,name=order_number,json=orderNumber,proto3" json:"order_number,omitempty"`
	PlayerDel            bool          `protobuf:"varint,13,opt,name=player_del,json=playerDel,proto3" json:"player_del,omitempty"`
	CoachDel             bool          `protobuf:"varint,14,opt,name=coach_del,json=coachDel,proto3" json:"coach_del,omitempty"`
	UpdateTime           int64         `protobuf:"varint,15,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	Remark               string        `protobuf:"bytes,16,opt,name=remark,proto3" json:"remark,omitempty"`
	EvaluateEntry        bool          `protobuf:"varint,17,opt,name=evaluate_entry,json=evaluateEntry,proto3" json:"evaluate_entry,omitempty"`
	EvaluateInfo         *EvaluateInfo `protobuf:"bytes,18,opt,name=evaluate_info,json=evaluateInfo,proto3" json:"evaluate_info,omitempty"`
	CoachUid             uint32        `protobuf:"varint,19,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SkillProductOrderDetail) Reset()         { *m = SkillProductOrderDetail{} }
func (m *SkillProductOrderDetail) String() string { return proto.CompactTextString(m) }
func (*SkillProductOrderDetail) ProtoMessage()    {}
func (*SkillProductOrderDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{4}
}
func (m *SkillProductOrderDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SkillProductOrderDetail.Unmarshal(m, b)
}
func (m *SkillProductOrderDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SkillProductOrderDetail.Marshal(b, m, deterministic)
}
func (dst *SkillProductOrderDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SkillProductOrderDetail.Merge(dst, src)
}
func (m *SkillProductOrderDetail) XXX_Size() int {
	return xxx_messageInfo_SkillProductOrderDetail.Size(m)
}
func (m *SkillProductOrderDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_SkillProductOrderDetail.DiscardUnknown(m)
}

var xxx_messageInfo_SkillProductOrderDetail proto.InternalMessageInfo

func (m *SkillProductOrderDetail) GetPlayerUid() uint32 {
	if m != nil {
		return m.PlayerUid
	}
	return 0
}

func (m *SkillProductOrderDetail) GetProductOrder() *ProductOrder {
	if m != nil {
		return m.ProductOrder
	}
	return nil
}

func (m *SkillProductOrderDetail) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *SkillProductOrderDetail) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *SkillProductOrderDetail) GetSubStatus() uint32 {
	if m != nil {
		return m.SubStatus
	}
	return 0
}

func (m *SkillProductOrderDetail) GetPayTime() int64 {
	if m != nil {
		return m.PayTime
	}
	return 0
}

func (m *SkillProductOrderDetail) GetReceiveTime() int64 {
	if m != nil {
		return m.ReceiveTime
	}
	return 0
}

func (m *SkillProductOrderDetail) GetFinishTime() int64 {
	if m != nil {
		return m.FinishTime
	}
	return 0
}

func (m *SkillProductOrderDetail) GetCancelTime() int64 {
	if m != nil {
		return m.CancelTime
	}
	return 0
}

func (m *SkillProductOrderDetail) GetOrderEndTime() int64 {
	if m != nil {
		return m.OrderEndTime
	}
	return 0
}

func (m *SkillProductOrderDetail) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *SkillProductOrderDetail) GetOrderNumber() string {
	if m != nil {
		return m.OrderNumber
	}
	return ""
}

func (m *SkillProductOrderDetail) GetPlayerDel() bool {
	if m != nil {
		return m.PlayerDel
	}
	return false
}

func (m *SkillProductOrderDetail) GetCoachDel() bool {
	if m != nil {
		return m.CoachDel
	}
	return false
}

func (m *SkillProductOrderDetail) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *SkillProductOrderDetail) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *SkillProductOrderDetail) GetEvaluateEntry() bool {
	if m != nil {
		return m.EvaluateEntry
	}
	return false
}

func (m *SkillProductOrderDetail) GetEvaluateInfo() *EvaluateInfo {
	if m != nil {
		return m.EvaluateInfo
	}
	return nil
}

func (m *SkillProductOrderDetail) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

// 玩家下单/支付
type PlayerPayOrderRequest struct {
	Uid                  uint32        `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Order                *ProductOrder `protobuf:"bytes,2,opt,name=order,proto3" json:"order,omitempty"`
	Remark               string        `protobuf:"bytes,3,opt,name=remark,proto3" json:"remark,omitempty"`
	Source               uint32        `protobuf:"varint,4,opt,name=source,proto3" json:"source,omitempty"`
	PayType              uint32        `protobuf:"varint,5,opt,name=pay_type,json=payType,proto3" json:"pay_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PlayerPayOrderRequest) Reset()         { *m = PlayerPayOrderRequest{} }
func (m *PlayerPayOrderRequest) String() string { return proto.CompactTextString(m) }
func (*PlayerPayOrderRequest) ProtoMessage()    {}
func (*PlayerPayOrderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{5}
}
func (m *PlayerPayOrderRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlayerPayOrderRequest.Unmarshal(m, b)
}
func (m *PlayerPayOrderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlayerPayOrderRequest.Marshal(b, m, deterministic)
}
func (dst *PlayerPayOrderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlayerPayOrderRequest.Merge(dst, src)
}
func (m *PlayerPayOrderRequest) XXX_Size() int {
	return xxx_messageInfo_PlayerPayOrderRequest.Size(m)
}
func (m *PlayerPayOrderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PlayerPayOrderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PlayerPayOrderRequest proto.InternalMessageInfo

func (m *PlayerPayOrderRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PlayerPayOrderRequest) GetOrder() *ProductOrder {
	if m != nil {
		return m.Order
	}
	return nil
}

func (m *PlayerPayOrderRequest) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *PlayerPayOrderRequest) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *PlayerPayOrderRequest) GetPayType() uint32 {
	if m != nil {
		return m.PayType
	}
	return 0
}

type PlayerPayOrderResponse struct {
	Balance              uint64   `protobuf:"varint,1,opt,name=balance,proto3" json:"balance,omitempty"`
	OrderId              string   `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlayerPayOrderResponse) Reset()         { *m = PlayerPayOrderResponse{} }
func (m *PlayerPayOrderResponse) String() string { return proto.CompactTextString(m) }
func (*PlayerPayOrderResponse) ProtoMessage()    {}
func (*PlayerPayOrderResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{6}
}
func (m *PlayerPayOrderResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlayerPayOrderResponse.Unmarshal(m, b)
}
func (m *PlayerPayOrderResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlayerPayOrderResponse.Marshal(b, m, deterministic)
}
func (dst *PlayerPayOrderResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlayerPayOrderResponse.Merge(dst, src)
}
func (m *PlayerPayOrderResponse) XXX_Size() int {
	return xxx_messageInfo_PlayerPayOrderResponse.Size(m)
}
func (m *PlayerPayOrderResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PlayerPayOrderResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PlayerPayOrderResponse proto.InternalMessageInfo

func (m *PlayerPayOrderResponse) GetBalance() uint64 {
	if m != nil {
		return m.Balance
	}
	return 0
}

func (m *PlayerPayOrderResponse) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

// 玩家取消订单
type PlayerCancelOrderRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OrderId              string   `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Reason               string   `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlayerCancelOrderRequest) Reset()         { *m = PlayerCancelOrderRequest{} }
func (m *PlayerCancelOrderRequest) String() string { return proto.CompactTextString(m) }
func (*PlayerCancelOrderRequest) ProtoMessage()    {}
func (*PlayerCancelOrderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{7}
}
func (m *PlayerCancelOrderRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlayerCancelOrderRequest.Unmarshal(m, b)
}
func (m *PlayerCancelOrderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlayerCancelOrderRequest.Marshal(b, m, deterministic)
}
func (dst *PlayerCancelOrderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlayerCancelOrderRequest.Merge(dst, src)
}
func (m *PlayerCancelOrderRequest) XXX_Size() int {
	return xxx_messageInfo_PlayerCancelOrderRequest.Size(m)
}
func (m *PlayerCancelOrderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PlayerCancelOrderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PlayerCancelOrderRequest proto.InternalMessageInfo

func (m *PlayerCancelOrderRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PlayerCancelOrderRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *PlayerCancelOrderRequest) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

type PlayerCancelOrderResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlayerCancelOrderResponse) Reset()         { *m = PlayerCancelOrderResponse{} }
func (m *PlayerCancelOrderResponse) String() string { return proto.CompactTextString(m) }
func (*PlayerCancelOrderResponse) ProtoMessage()    {}
func (*PlayerCancelOrderResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{8}
}
func (m *PlayerCancelOrderResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlayerCancelOrderResponse.Unmarshal(m, b)
}
func (m *PlayerCancelOrderResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlayerCancelOrderResponse.Marshal(b, m, deterministic)
}
func (dst *PlayerCancelOrderResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlayerCancelOrderResponse.Merge(dst, src)
}
func (m *PlayerCancelOrderResponse) XXX_Size() int {
	return xxx_messageInfo_PlayerCancelOrderResponse.Size(m)
}
func (m *PlayerCancelOrderResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PlayerCancelOrderResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PlayerCancelOrderResponse proto.InternalMessageInfo

// 玩家确认完成订单
type PlayerFinishOrderRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OrderId              string   `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Desc                 string   `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlayerFinishOrderRequest) Reset()         { *m = PlayerFinishOrderRequest{} }
func (m *PlayerFinishOrderRequest) String() string { return proto.CompactTextString(m) }
func (*PlayerFinishOrderRequest) ProtoMessage()    {}
func (*PlayerFinishOrderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{9}
}
func (m *PlayerFinishOrderRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlayerFinishOrderRequest.Unmarshal(m, b)
}
func (m *PlayerFinishOrderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlayerFinishOrderRequest.Marshal(b, m, deterministic)
}
func (dst *PlayerFinishOrderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlayerFinishOrderRequest.Merge(dst, src)
}
func (m *PlayerFinishOrderRequest) XXX_Size() int {
	return xxx_messageInfo_PlayerFinishOrderRequest.Size(m)
}
func (m *PlayerFinishOrderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PlayerFinishOrderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PlayerFinishOrderRequest proto.InternalMessageInfo

func (m *PlayerFinishOrderRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PlayerFinishOrderRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *PlayerFinishOrderRequest) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

type PlayerFinishOrderResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlayerFinishOrderResponse) Reset()         { *m = PlayerFinishOrderResponse{} }
func (m *PlayerFinishOrderResponse) String() string { return proto.CompactTextString(m) }
func (*PlayerFinishOrderResponse) ProtoMessage()    {}
func (*PlayerFinishOrderResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{10}
}
func (m *PlayerFinishOrderResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlayerFinishOrderResponse.Unmarshal(m, b)
}
func (m *PlayerFinishOrderResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlayerFinishOrderResponse.Marshal(b, m, deterministic)
}
func (dst *PlayerFinishOrderResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlayerFinishOrderResponse.Merge(dst, src)
}
func (m *PlayerFinishOrderResponse) XXX_Size() int {
	return xxx_messageInfo_PlayerFinishOrderResponse.Size(m)
}
func (m *PlayerFinishOrderResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PlayerFinishOrderResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PlayerFinishOrderResponse proto.InternalMessageInfo

// 电竞指导接单
type CoachReceiveOrderRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OrderId              string   `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CoachReceiveOrderRequest) Reset()         { *m = CoachReceiveOrderRequest{} }
func (m *CoachReceiveOrderRequest) String() string { return proto.CompactTextString(m) }
func (*CoachReceiveOrderRequest) ProtoMessage()    {}
func (*CoachReceiveOrderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{11}
}
func (m *CoachReceiveOrderRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CoachReceiveOrderRequest.Unmarshal(m, b)
}
func (m *CoachReceiveOrderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CoachReceiveOrderRequest.Marshal(b, m, deterministic)
}
func (dst *CoachReceiveOrderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CoachReceiveOrderRequest.Merge(dst, src)
}
func (m *CoachReceiveOrderRequest) XXX_Size() int {
	return xxx_messageInfo_CoachReceiveOrderRequest.Size(m)
}
func (m *CoachReceiveOrderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CoachReceiveOrderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CoachReceiveOrderRequest proto.InternalMessageInfo

func (m *CoachReceiveOrderRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CoachReceiveOrderRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type CoachReceiveOrderResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CoachReceiveOrderResponse) Reset()         { *m = CoachReceiveOrderResponse{} }
func (m *CoachReceiveOrderResponse) String() string { return proto.CompactTextString(m) }
func (*CoachReceiveOrderResponse) ProtoMessage()    {}
func (*CoachReceiveOrderResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{12}
}
func (m *CoachReceiveOrderResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CoachReceiveOrderResponse.Unmarshal(m, b)
}
func (m *CoachReceiveOrderResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CoachReceiveOrderResponse.Marshal(b, m, deterministic)
}
func (dst *CoachReceiveOrderResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CoachReceiveOrderResponse.Merge(dst, src)
}
func (m *CoachReceiveOrderResponse) XXX_Size() int {
	return xxx_messageInfo_CoachReceiveOrderResponse.Size(m)
}
func (m *CoachReceiveOrderResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CoachReceiveOrderResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CoachReceiveOrderResponse proto.InternalMessageInfo

// 电竞指导拒绝接单
type CoachRefuseOrderRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OrderId              string   `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Reason               string   `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CoachRefuseOrderRequest) Reset()         { *m = CoachRefuseOrderRequest{} }
func (m *CoachRefuseOrderRequest) String() string { return proto.CompactTextString(m) }
func (*CoachRefuseOrderRequest) ProtoMessage()    {}
func (*CoachRefuseOrderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{13}
}
func (m *CoachRefuseOrderRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CoachRefuseOrderRequest.Unmarshal(m, b)
}
func (m *CoachRefuseOrderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CoachRefuseOrderRequest.Marshal(b, m, deterministic)
}
func (dst *CoachRefuseOrderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CoachRefuseOrderRequest.Merge(dst, src)
}
func (m *CoachRefuseOrderRequest) XXX_Size() int {
	return xxx_messageInfo_CoachRefuseOrderRequest.Size(m)
}
func (m *CoachRefuseOrderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CoachRefuseOrderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CoachRefuseOrderRequest proto.InternalMessageInfo

func (m *CoachRefuseOrderRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CoachRefuseOrderRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *CoachRefuseOrderRequest) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

type CoachRefuseOrderResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CoachRefuseOrderResponse) Reset()         { *m = CoachRefuseOrderResponse{} }
func (m *CoachRefuseOrderResponse) String() string { return proto.CompactTextString(m) }
func (*CoachRefuseOrderResponse) ProtoMessage()    {}
func (*CoachRefuseOrderResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{14}
}
func (m *CoachRefuseOrderResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CoachRefuseOrderResponse.Unmarshal(m, b)
}
func (m *CoachRefuseOrderResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CoachRefuseOrderResponse.Marshal(b, m, deterministic)
}
func (dst *CoachRefuseOrderResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CoachRefuseOrderResponse.Merge(dst, src)
}
func (m *CoachRefuseOrderResponse) XXX_Size() int {
	return xxx_messageInfo_CoachRefuseOrderResponse.Size(m)
}
func (m *CoachRefuseOrderResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CoachRefuseOrderResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CoachRefuseOrderResponse proto.InternalMessageInfo

// 电竞指导提醒玩家去完成订单
type CoachNotifyFinishOrderRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OrderId              string   `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CoachNotifyFinishOrderRequest) Reset()         { *m = CoachNotifyFinishOrderRequest{} }
func (m *CoachNotifyFinishOrderRequest) String() string { return proto.CompactTextString(m) }
func (*CoachNotifyFinishOrderRequest) ProtoMessage()    {}
func (*CoachNotifyFinishOrderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{15}
}
func (m *CoachNotifyFinishOrderRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CoachNotifyFinishOrderRequest.Unmarshal(m, b)
}
func (m *CoachNotifyFinishOrderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CoachNotifyFinishOrderRequest.Marshal(b, m, deterministic)
}
func (dst *CoachNotifyFinishOrderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CoachNotifyFinishOrderRequest.Merge(dst, src)
}
func (m *CoachNotifyFinishOrderRequest) XXX_Size() int {
	return xxx_messageInfo_CoachNotifyFinishOrderRequest.Size(m)
}
func (m *CoachNotifyFinishOrderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CoachNotifyFinishOrderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CoachNotifyFinishOrderRequest proto.InternalMessageInfo

func (m *CoachNotifyFinishOrderRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CoachNotifyFinishOrderRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type CoachNotifyFinishOrderResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CoachNotifyFinishOrderResponse) Reset()         { *m = CoachNotifyFinishOrderResponse{} }
func (m *CoachNotifyFinishOrderResponse) String() string { return proto.CompactTextString(m) }
func (*CoachNotifyFinishOrderResponse) ProtoMessage()    {}
func (*CoachNotifyFinishOrderResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{16}
}
func (m *CoachNotifyFinishOrderResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CoachNotifyFinishOrderResponse.Unmarshal(m, b)
}
func (m *CoachNotifyFinishOrderResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CoachNotifyFinishOrderResponse.Marshal(b, m, deterministic)
}
func (dst *CoachNotifyFinishOrderResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CoachNotifyFinishOrderResponse.Merge(dst, src)
}
func (m *CoachNotifyFinishOrderResponse) XXX_Size() int {
	return xxx_messageInfo_CoachNotifyFinishOrderResponse.Size(m)
}
func (m *CoachNotifyFinishOrderResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CoachNotifyFinishOrderResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CoachNotifyFinishOrderResponse proto.InternalMessageInfo

// 获取订单详情
type GetOrderDetailRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OrderId              string   `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	NeedAllStatusTime    bool     `protobuf:"varint,3,opt,name=need_all_status_time,json=needAllStatusTime,proto3" json:"need_all_status_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOrderDetailRequest) Reset()         { *m = GetOrderDetailRequest{} }
func (m *GetOrderDetailRequest) String() string { return proto.CompactTextString(m) }
func (*GetOrderDetailRequest) ProtoMessage()    {}
func (*GetOrderDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{17}
}
func (m *GetOrderDetailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderDetailRequest.Unmarshal(m, b)
}
func (m *GetOrderDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderDetailRequest.Marshal(b, m, deterministic)
}
func (dst *GetOrderDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderDetailRequest.Merge(dst, src)
}
func (m *GetOrderDetailRequest) XXX_Size() int {
	return xxx_messageInfo_GetOrderDetailRequest.Size(m)
}
func (m *GetOrderDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderDetailRequest proto.InternalMessageInfo

func (m *GetOrderDetailRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetOrderDetailRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *GetOrderDetailRequest) GetNeedAllStatusTime() bool {
	if m != nil {
		return m.NeedAllStatusTime
	}
	return false
}

type GetOrderDetailResponse struct {
	Order                *SkillProductOrderDetail `protobuf:"bytes,1,opt,name=order,proto3" json:"order,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetOrderDetailResponse) Reset()         { *m = GetOrderDetailResponse{} }
func (m *GetOrderDetailResponse) String() string { return proto.CompactTextString(m) }
func (*GetOrderDetailResponse) ProtoMessage()    {}
func (*GetOrderDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{18}
}
func (m *GetOrderDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderDetailResponse.Unmarshal(m, b)
}
func (m *GetOrderDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderDetailResponse.Marshal(b, m, deterministic)
}
func (dst *GetOrderDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderDetailResponse.Merge(dst, src)
}
func (m *GetOrderDetailResponse) XXX_Size() int {
	return xxx_messageInfo_GetOrderDetailResponse.Size(m)
}
func (m *GetOrderDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderDetailResponse proto.InternalMessageInfo

func (m *GetOrderDetailResponse) GetOrder() *SkillProductOrderDetail {
	if m != nil {
		return m.Order
	}
	return nil
}

// 订单简略信息
type OrderSimpleInfo struct {
	PlayerUid            uint32        `protobuf:"varint,1,opt,name=player_uid,json=playerUid,proto3" json:"player_uid,omitempty"`
	ProductOrder         *ProductOrder `protobuf:"bytes,2,opt,name=product_order,json=productOrder,proto3" json:"product_order,omitempty"`
	OrderId              string        `protobuf:"bytes,3,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Status               uint32        `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	SubStatus            uint32        `protobuf:"varint,5,opt,name=sub_status,json=subStatus,proto3" json:"sub_status,omitempty"`
	PayTime              int64         `protobuf:"varint,6,opt,name=pay_time,json=payTime,proto3" json:"pay_time,omitempty"`
	OrderEndTime         int64         `protobuf:"varint,7,opt,name=order_end_time,json=orderEndTime,proto3" json:"order_end_time,omitempty"`
	OffsetId             string        `protobuf:"bytes,8,opt,name=offset_id,json=offsetId,proto3" json:"offset_id,omitempty"`
	Desc                 string        `protobuf:"bytes,9,opt,name=desc,proto3" json:"desc,omitempty"`
	OrderNumber          string        `protobuf:"bytes,10,opt,name=order_number,json=orderNumber,proto3" json:"order_number,omitempty"`
	UpdateTime           int64         `protobuf:"varint,11,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	EvaluateEntry        bool          `protobuf:"varint,12,opt,name=evaluate_entry,json=evaluateEntry,proto3" json:"evaluate_entry,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *OrderSimpleInfo) Reset()         { *m = OrderSimpleInfo{} }
func (m *OrderSimpleInfo) String() string { return proto.CompactTextString(m) }
func (*OrderSimpleInfo) ProtoMessage()    {}
func (*OrderSimpleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{19}
}
func (m *OrderSimpleInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderSimpleInfo.Unmarshal(m, b)
}
func (m *OrderSimpleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderSimpleInfo.Marshal(b, m, deterministic)
}
func (dst *OrderSimpleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderSimpleInfo.Merge(dst, src)
}
func (m *OrderSimpleInfo) XXX_Size() int {
	return xxx_messageInfo_OrderSimpleInfo.Size(m)
}
func (m *OrderSimpleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderSimpleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OrderSimpleInfo proto.InternalMessageInfo

func (m *OrderSimpleInfo) GetPlayerUid() uint32 {
	if m != nil {
		return m.PlayerUid
	}
	return 0
}

func (m *OrderSimpleInfo) GetProductOrder() *ProductOrder {
	if m != nil {
		return m.ProductOrder
	}
	return nil
}

func (m *OrderSimpleInfo) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *OrderSimpleInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *OrderSimpleInfo) GetSubStatus() uint32 {
	if m != nil {
		return m.SubStatus
	}
	return 0
}

func (m *OrderSimpleInfo) GetPayTime() int64 {
	if m != nil {
		return m.PayTime
	}
	return 0
}

func (m *OrderSimpleInfo) GetOrderEndTime() int64 {
	if m != nil {
		return m.OrderEndTime
	}
	return 0
}

func (m *OrderSimpleInfo) GetOffsetId() string {
	if m != nil {
		return m.OffsetId
	}
	return ""
}

func (m *OrderSimpleInfo) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *OrderSimpleInfo) GetOrderNumber() string {
	if m != nil {
		return m.OrderNumber
	}
	return ""
}

func (m *OrderSimpleInfo) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *OrderSimpleInfo) GetEvaluateEntry() bool {
	if m != nil {
		return m.EvaluateEntry
	}
	return false
}

// im页获取与对方正在进行中的订单
type GetImOngoingOrderListRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetImOngoingOrderListRequest) Reset()         { *m = GetImOngoingOrderListRequest{} }
func (m *GetImOngoingOrderListRequest) String() string { return proto.CompactTextString(m) }
func (*GetImOngoingOrderListRequest) ProtoMessage()    {}
func (*GetImOngoingOrderListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{20}
}
func (m *GetImOngoingOrderListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetImOngoingOrderListRequest.Unmarshal(m, b)
}
func (m *GetImOngoingOrderListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetImOngoingOrderListRequest.Marshal(b, m, deterministic)
}
func (dst *GetImOngoingOrderListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetImOngoingOrderListRequest.Merge(dst, src)
}
func (m *GetImOngoingOrderListRequest) XXX_Size() int {
	return xxx_messageInfo_GetImOngoingOrderListRequest.Size(m)
}
func (m *GetImOngoingOrderListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetImOngoingOrderListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetImOngoingOrderListRequest proto.InternalMessageInfo

func (m *GetImOngoingOrderListRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetImOngoingOrderListRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type GetImOngoingOrderListResponse struct {
	OrderList            []*OrderSimpleInfo `protobuf:"bytes,1,rep,name=order_list,json=orderList,proto3" json:"order_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetImOngoingOrderListResponse) Reset()         { *m = GetImOngoingOrderListResponse{} }
func (m *GetImOngoingOrderListResponse) String() string { return proto.CompactTextString(m) }
func (*GetImOngoingOrderListResponse) ProtoMessage()    {}
func (*GetImOngoingOrderListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{21}
}
func (m *GetImOngoingOrderListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetImOngoingOrderListResponse.Unmarshal(m, b)
}
func (m *GetImOngoingOrderListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetImOngoingOrderListResponse.Marshal(b, m, deterministic)
}
func (dst *GetImOngoingOrderListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetImOngoingOrderListResponse.Merge(dst, src)
}
func (m *GetImOngoingOrderListResponse) XXX_Size() int {
	return xxx_messageInfo_GetImOngoingOrderListResponse.Size(m)
}
func (m *GetImOngoingOrderListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetImOngoingOrderListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetImOngoingOrderListResponse proto.InternalMessageInfo

func (m *GetImOngoingOrderListResponse) GetOrderList() []*OrderSimpleInfo {
	if m != nil {
		return m.OrderList
	}
	return nil
}

// 获取订单记录列表
type GetOrderListRequest struct {
	Uid        uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OrderType  uint32 `protobuf:"varint,2,opt,name=order_type,json=orderType,proto3" json:"order_type,omitempty"`
	StatusType uint32 `protobuf:"varint,3,opt,name=status_type,json=statusType,proto3" json:"status_type,omitempty"`
	// string offset_id = 4;     // 上一页最后一条记录的位置id, 废弃
	Limit                uint32   `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	Offset               uint32   `protobuf:"varint,6,opt,name=offset,proto3" json:"offset,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOrderListRequest) Reset()         { *m = GetOrderListRequest{} }
func (m *GetOrderListRequest) String() string { return proto.CompactTextString(m) }
func (*GetOrderListRequest) ProtoMessage()    {}
func (*GetOrderListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{22}
}
func (m *GetOrderListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderListRequest.Unmarshal(m, b)
}
func (m *GetOrderListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderListRequest.Marshal(b, m, deterministic)
}
func (dst *GetOrderListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderListRequest.Merge(dst, src)
}
func (m *GetOrderListRequest) XXX_Size() int {
	return xxx_messageInfo_GetOrderListRequest.Size(m)
}
func (m *GetOrderListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderListRequest proto.InternalMessageInfo

func (m *GetOrderListRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetOrderListRequest) GetOrderType() uint32 {
	if m != nil {
		return m.OrderType
	}
	return 0
}

func (m *GetOrderListRequest) GetStatusType() uint32 {
	if m != nil {
		return m.StatusType
	}
	return 0
}

func (m *GetOrderListRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetOrderListRequest) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

type GetOrderListResponse struct {
	OrderList            []*OrderSimpleInfo `protobuf:"bytes,1,rep,name=order_list,json=orderList,proto3" json:"order_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetOrderListResponse) Reset()         { *m = GetOrderListResponse{} }
func (m *GetOrderListResponse) String() string { return proto.CompactTextString(m) }
func (*GetOrderListResponse) ProtoMessage()    {}
func (*GetOrderListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{23}
}
func (m *GetOrderListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderListResponse.Unmarshal(m, b)
}
func (m *GetOrderListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderListResponse.Marshal(b, m, deterministic)
}
func (dst *GetOrderListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderListResponse.Merge(dst, src)
}
func (m *GetOrderListResponse) XXX_Size() int {
	return xxx_messageInfo_GetOrderListResponse.Size(m)
}
func (m *GetOrderListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderListResponse proto.InternalMessageInfo

func (m *GetOrderListResponse) GetOrderList() []*OrderSimpleInfo {
	if m != nil {
		return m.OrderList
	}
	return nil
}

// 删除订单
type DelOrderRecordRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OrderId              string   `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelOrderRecordRequest) Reset()         { *m = DelOrderRecordRequest{} }
func (m *DelOrderRecordRequest) String() string { return proto.CompactTextString(m) }
func (*DelOrderRecordRequest) ProtoMessage()    {}
func (*DelOrderRecordRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{24}
}
func (m *DelOrderRecordRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelOrderRecordRequest.Unmarshal(m, b)
}
func (m *DelOrderRecordRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelOrderRecordRequest.Marshal(b, m, deterministic)
}
func (dst *DelOrderRecordRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelOrderRecordRequest.Merge(dst, src)
}
func (m *DelOrderRecordRequest) XXX_Size() int {
	return xxx_messageInfo_DelOrderRecordRequest.Size(m)
}
func (m *DelOrderRecordRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DelOrderRecordRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DelOrderRecordRequest proto.InternalMessageInfo

func (m *DelOrderRecordRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DelOrderRecordRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type DelOrderRecordResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelOrderRecordResponse) Reset()         { *m = DelOrderRecordResponse{} }
func (m *DelOrderRecordResponse) String() string { return proto.CompactTextString(m) }
func (*DelOrderRecordResponse) ProtoMessage()    {}
func (*DelOrderRecordResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{25}
}
func (m *DelOrderRecordResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelOrderRecordResponse.Unmarshal(m, b)
}
func (m *DelOrderRecordResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelOrderRecordResponse.Marshal(b, m, deterministic)
}
func (dst *DelOrderRecordResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelOrderRecordResponse.Merge(dst, src)
}
func (m *DelOrderRecordResponse) XXX_Size() int {
	return xxx_messageInfo_DelOrderRecordResponse.Size(m)
}
func (m *DelOrderRecordResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DelOrderRecordResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DelOrderRecordResponse proto.InternalMessageInfo

// 进入退款申诉状态
type EnterRefundStatusRequest struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EnterRefundStatusRequest) Reset()         { *m = EnterRefundStatusRequest{} }
func (m *EnterRefundStatusRequest) String() string { return proto.CompactTextString(m) }
func (*EnterRefundStatusRequest) ProtoMessage()    {}
func (*EnterRefundStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{26}
}
func (m *EnterRefundStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EnterRefundStatusRequest.Unmarshal(m, b)
}
func (m *EnterRefundStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EnterRefundStatusRequest.Marshal(b, m, deterministic)
}
func (dst *EnterRefundStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EnterRefundStatusRequest.Merge(dst, src)
}
func (m *EnterRefundStatusRequest) XXX_Size() int {
	return xxx_messageInfo_EnterRefundStatusRequest.Size(m)
}
func (m *EnterRefundStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_EnterRefundStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_EnterRefundStatusRequest proto.InternalMessageInfo

func (m *EnterRefundStatusRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type EnterRefundStatusResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EnterRefundStatusResponse) Reset()         { *m = EnterRefundStatusResponse{} }
func (m *EnterRefundStatusResponse) String() string { return proto.CompactTextString(m) }
func (*EnterRefundStatusResponse) ProtoMessage()    {}
func (*EnterRefundStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{27}
}
func (m *EnterRefundStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EnterRefundStatusResponse.Unmarshal(m, b)
}
func (m *EnterRefundStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EnterRefundStatusResponse.Marshal(b, m, deterministic)
}
func (dst *EnterRefundStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EnterRefundStatusResponse.Merge(dst, src)
}
func (m *EnterRefundStatusResponse) XXX_Size() int {
	return xxx_messageInfo_EnterRefundStatusResponse.Size(m)
}
func (m *EnterRefundStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_EnterRefundStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_EnterRefundStatusResponse proto.InternalMessageInfo

// 退款
type RefundRequest struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Reason               string   `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	RefundCount          uint32   `protobuf:"varint,3,opt,name=refund_count,json=refundCount,proto3" json:"refund_count,omitempty"`
	RefundPrice          uint32   `protobuf:"varint,4,opt,name=refund_price,json=refundPrice,proto3" json:"refund_price,omitempty"`
	DealToken            string   `protobuf:"bytes,5,opt,name=deal_token,json=dealToken,proto3" json:"deal_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RefundRequest) Reset()         { *m = RefundRequest{} }
func (m *RefundRequest) String() string { return proto.CompactTextString(m) }
func (*RefundRequest) ProtoMessage()    {}
func (*RefundRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{28}
}
func (m *RefundRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RefundRequest.Unmarshal(m, b)
}
func (m *RefundRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RefundRequest.Marshal(b, m, deterministic)
}
func (dst *RefundRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RefundRequest.Merge(dst, src)
}
func (m *RefundRequest) XXX_Size() int {
	return xxx_messageInfo_RefundRequest.Size(m)
}
func (m *RefundRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RefundRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RefundRequest proto.InternalMessageInfo

func (m *RefundRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *RefundRequest) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *RefundRequest) GetRefundCount() uint32 {
	if m != nil {
		return m.RefundCount
	}
	return 0
}

func (m *RefundRequest) GetRefundPrice() uint32 {
	if m != nil {
		return m.RefundPrice
	}
	return 0
}

func (m *RefundRequest) GetDealToken() string {
	if m != nil {
		return m.DealToken
	}
	return ""
}

type RefundResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RefundResponse) Reset()         { *m = RefundResponse{} }
func (m *RefundResponse) String() string { return proto.CompactTextString(m) }
func (*RefundResponse) ProtoMessage()    {}
func (*RefundResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{29}
}
func (m *RefundResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RefundResponse.Unmarshal(m, b)
}
func (m *RefundResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RefundResponse.Marshal(b, m, deterministic)
}
func (dst *RefundResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RefundResponse.Merge(dst, src)
}
func (m *RefundResponse) XXX_Size() int {
	return xxx_messageInfo_RefundResponse.Size(m)
}
func (m *RefundResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RefundResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RefundResponse proto.InternalMessageInfo

// 获取订单消费信息
type GetOrderConsumeRequest struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOrderConsumeRequest) Reset()         { *m = GetOrderConsumeRequest{} }
func (m *GetOrderConsumeRequest) String() string { return proto.CompactTextString(m) }
func (*GetOrderConsumeRequest) ProtoMessage()    {}
func (*GetOrderConsumeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{30}
}
func (m *GetOrderConsumeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderConsumeRequest.Unmarshal(m, b)
}
func (m *GetOrderConsumeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderConsumeRequest.Marshal(b, m, deterministic)
}
func (dst *GetOrderConsumeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderConsumeRequest.Merge(dst, src)
}
func (m *GetOrderConsumeRequest) XXX_Size() int {
	return xxx_messageInfo_GetOrderConsumeRequest.Size(m)
}
func (m *GetOrderConsumeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderConsumeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderConsumeRequest proto.InternalMessageInfo

func (m *GetOrderConsumeRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type GetOrderConsumeResponse struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	CoachUid             uint32   `protobuf:"varint,2,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	PlayerUid            uint32   `protobuf:"varint,3,opt,name=player_uid,json=playerUid,proto3" json:"player_uid,omitempty"`
	CommitCount          uint32   `protobuf:"varint,4,opt,name=commit_count,json=commitCount,proto3" json:"commit_count,omitempty"`
	CommitTotalPrice     uint32   `protobuf:"varint,5,opt,name=commit_total_price,json=commitTotalPrice,proto3" json:"commit_total_price,omitempty"`
	CommitTime           int64    `protobuf:"varint,6,opt,name=commit_time,json=commitTime,proto3" json:"commit_time,omitempty"`
	SignGuildId          uint32   `protobuf:"varint,7,opt,name=sign_guild_id,json=signGuildId,proto3" json:"sign_guild_id,omitempty"`
	DealToken            string   `protobuf:"bytes,8,opt,name=deal_token,json=dealToken,proto3" json:"deal_token,omitempty"`
	CoachTotalPrice      uint32   `protobuf:"varint,9,opt,name=coach_total_price,json=coachTotalPrice,proto3" json:"coach_total_price,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOrderConsumeResponse) Reset()         { *m = GetOrderConsumeResponse{} }
func (m *GetOrderConsumeResponse) String() string { return proto.CompactTextString(m) }
func (*GetOrderConsumeResponse) ProtoMessage()    {}
func (*GetOrderConsumeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{31}
}
func (m *GetOrderConsumeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderConsumeResponse.Unmarshal(m, b)
}
func (m *GetOrderConsumeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderConsumeResponse.Marshal(b, m, deterministic)
}
func (dst *GetOrderConsumeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderConsumeResponse.Merge(dst, src)
}
func (m *GetOrderConsumeResponse) XXX_Size() int {
	return xxx_messageInfo_GetOrderConsumeResponse.Size(m)
}
func (m *GetOrderConsumeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderConsumeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderConsumeResponse proto.InternalMessageInfo

func (m *GetOrderConsumeResponse) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *GetOrderConsumeResponse) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

func (m *GetOrderConsumeResponse) GetPlayerUid() uint32 {
	if m != nil {
		return m.PlayerUid
	}
	return 0
}

func (m *GetOrderConsumeResponse) GetCommitCount() uint32 {
	if m != nil {
		return m.CommitCount
	}
	return 0
}

func (m *GetOrderConsumeResponse) GetCommitTotalPrice() uint32 {
	if m != nil {
		return m.CommitTotalPrice
	}
	return 0
}

func (m *GetOrderConsumeResponse) GetCommitTime() int64 {
	if m != nil {
		return m.CommitTime
	}
	return 0
}

func (m *GetOrderConsumeResponse) GetSignGuildId() uint32 {
	if m != nil {
		return m.SignGuildId
	}
	return 0
}

func (m *GetOrderConsumeResponse) GetDealToken() string {
	if m != nil {
		return m.DealToken
	}
	return ""
}

func (m *GetOrderConsumeResponse) GetCoachTotalPrice() uint32 {
	if m != nil {
		return m.CoachTotalPrice
	}
	return 0
}

type EvaluateScore struct {
	AvgScore             float32  `protobuf:"fixed32,1,opt,name=avg_score,json=avgScore,proto3" json:"avg_score,omitempty"`
	ServiceScore         float32  `protobuf:"fixed32,2,opt,name=service_score,json=serviceScore,proto3" json:"service_score,omitempty"`
	SkillScore           float32  `protobuf:"fixed32,3,opt,name=skill_score,json=skillScore,proto3" json:"skill_score,omitempty"`
	VoiceScore           float32  `protobuf:"fixed32,4,opt,name=voice_score,json=voiceScore,proto3" json:"voice_score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EvaluateScore) Reset()         { *m = EvaluateScore{} }
func (m *EvaluateScore) String() string { return proto.CompactTextString(m) }
func (*EvaluateScore) ProtoMessage()    {}
func (*EvaluateScore) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{32}
}
func (m *EvaluateScore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EvaluateScore.Unmarshal(m, b)
}
func (m *EvaluateScore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EvaluateScore.Marshal(b, m, deterministic)
}
func (dst *EvaluateScore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EvaluateScore.Merge(dst, src)
}
func (m *EvaluateScore) XXX_Size() int {
	return xxx_messageInfo_EvaluateScore.Size(m)
}
func (m *EvaluateScore) XXX_DiscardUnknown() {
	xxx_messageInfo_EvaluateScore.DiscardUnknown(m)
}

var xxx_messageInfo_EvaluateScore proto.InternalMessageInfo

func (m *EvaluateScore) GetAvgScore() float32 {
	if m != nil {
		return m.AvgScore
	}
	return 0
}

func (m *EvaluateScore) GetServiceScore() float32 {
	if m != nil {
		return m.ServiceScore
	}
	return 0
}

func (m *EvaluateScore) GetSkillScore() float32 {
	if m != nil {
		return m.SkillScore
	}
	return 0
}

func (m *EvaluateScore) GetVoiceScore() float32 {
	if m != nil {
		return m.VoiceScore
	}
	return 0
}

type EvaluateWordCnt struct {
	Word                 string   `protobuf:"bytes,1,opt,name=word,proto3" json:"word,omitempty"`
	Cnt                  uint32   `protobuf:"varint,2,opt,name=cnt,proto3" json:"cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EvaluateWordCnt) Reset()         { *m = EvaluateWordCnt{} }
func (m *EvaluateWordCnt) String() string { return proto.CompactTextString(m) }
func (*EvaluateWordCnt) ProtoMessage()    {}
func (*EvaluateWordCnt) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{33}
}
func (m *EvaluateWordCnt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EvaluateWordCnt.Unmarshal(m, b)
}
func (m *EvaluateWordCnt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EvaluateWordCnt.Marshal(b, m, deterministic)
}
func (dst *EvaluateWordCnt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EvaluateWordCnt.Merge(dst, src)
}
func (m *EvaluateWordCnt) XXX_Size() int {
	return xxx_messageInfo_EvaluateWordCnt.Size(m)
}
func (m *EvaluateWordCnt) XXX_DiscardUnknown() {
	xxx_messageInfo_EvaluateWordCnt.DiscardUnknown(m)
}

var xxx_messageInfo_EvaluateWordCnt proto.InternalMessageInfo

func (m *EvaluateWordCnt) GetWord() string {
	if m != nil {
		return m.Word
	}
	return ""
}

func (m *EvaluateWordCnt) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

// 评价汇总
type EvaluateSummary struct {
	TotalCnt             uint32             `protobuf:"varint,1,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	ScoreInfo            *EvaluateScore     `protobuf:"bytes,2,opt,name=score_info,json=scoreInfo,proto3" json:"score_info,omitempty"`
	WordCntList          []*EvaluateWordCnt `protobuf:"bytes,3,rep,name=word_cnt_list,json=wordCntList,proto3" json:"word_cnt_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *EvaluateSummary) Reset()         { *m = EvaluateSummary{} }
func (m *EvaluateSummary) String() string { return proto.CompactTextString(m) }
func (*EvaluateSummary) ProtoMessage()    {}
func (*EvaluateSummary) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{34}
}
func (m *EvaluateSummary) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EvaluateSummary.Unmarshal(m, b)
}
func (m *EvaluateSummary) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EvaluateSummary.Marshal(b, m, deterministic)
}
func (dst *EvaluateSummary) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EvaluateSummary.Merge(dst, src)
}
func (m *EvaluateSummary) XXX_Size() int {
	return xxx_messageInfo_EvaluateSummary.Size(m)
}
func (m *EvaluateSummary) XXX_DiscardUnknown() {
	xxx_messageInfo_EvaluateSummary.DiscardUnknown(m)
}

var xxx_messageInfo_EvaluateSummary proto.InternalMessageInfo

func (m *EvaluateSummary) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

func (m *EvaluateSummary) GetScoreInfo() *EvaluateScore {
	if m != nil {
		return m.ScoreInfo
	}
	return nil
}

func (m *EvaluateSummary) GetWordCntList() []*EvaluateWordCnt {
	if m != nil {
		return m.WordCntList
	}
	return nil
}

// 订单评价信息
type EvaluateInfo struct {
	IsAnonymous          bool           `protobuf:"varint,1,opt,name=is_anonymous,json=isAnonymous,proto3" json:"is_anonymous,omitempty"`
	EvaluateTime         int64          `protobuf:"varint,2,opt,name=evaluate_time,json=evaluateTime,proto3" json:"evaluate_time,omitempty"`
	WordList             []string       `protobuf:"bytes,3,rep,name=word_list,json=wordList,proto3" json:"word_list,omitempty"`
	Content              string         `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	ScoreInfo            *EvaluateScore `protobuf:"bytes,5,opt,name=score_info,json=scoreInfo,proto3" json:"score_info,omitempty"`
	OrderId              string         `protobuf:"bytes,6,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	PlayerUid            uint32         `protobuf:"varint,7,opt,name=player_uid,json=playerUid,proto3" json:"player_uid,omitempty"`
	CoachUid             uint32         `protobuf:"varint,8,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	ReviewStatus         uint32         `protobuf:"varint,9,opt,name=review_status,json=reviewStatus,proto3" json:"review_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *EvaluateInfo) Reset()         { *m = EvaluateInfo{} }
func (m *EvaluateInfo) String() string { return proto.CompactTextString(m) }
func (*EvaluateInfo) ProtoMessage()    {}
func (*EvaluateInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{35}
}
func (m *EvaluateInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EvaluateInfo.Unmarshal(m, b)
}
func (m *EvaluateInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EvaluateInfo.Marshal(b, m, deterministic)
}
func (dst *EvaluateInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EvaluateInfo.Merge(dst, src)
}
func (m *EvaluateInfo) XXX_Size() int {
	return xxx_messageInfo_EvaluateInfo.Size(m)
}
func (m *EvaluateInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_EvaluateInfo.DiscardUnknown(m)
}

var xxx_messageInfo_EvaluateInfo proto.InternalMessageInfo

func (m *EvaluateInfo) GetIsAnonymous() bool {
	if m != nil {
		return m.IsAnonymous
	}
	return false
}

func (m *EvaluateInfo) GetEvaluateTime() int64 {
	if m != nil {
		return m.EvaluateTime
	}
	return 0
}

func (m *EvaluateInfo) GetWordList() []string {
	if m != nil {
		return m.WordList
	}
	return nil
}

func (m *EvaluateInfo) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *EvaluateInfo) GetScoreInfo() *EvaluateScore {
	if m != nil {
		return m.ScoreInfo
	}
	return nil
}

func (m *EvaluateInfo) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *EvaluateInfo) GetPlayerUid() uint32 {
	if m != nil {
		return m.PlayerUid
	}
	return 0
}

func (m *EvaluateInfo) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

func (m *EvaluateInfo) GetReviewStatus() uint32 {
	if m != nil {
		return m.ReviewStatus
	}
	return 0
}

// 分页获取用户评价列表
type GetEvaluateListRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Word                 string   `protobuf:"bytes,2,opt,name=word,proto3" json:"word,omitempty"`
	Offset               uint32   `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	GameId               uint32   `protobuf:"varint,5,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEvaluateListRequest) Reset()         { *m = GetEvaluateListRequest{} }
func (m *GetEvaluateListRequest) String() string { return proto.CompactTextString(m) }
func (*GetEvaluateListRequest) ProtoMessage()    {}
func (*GetEvaluateListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{36}
}
func (m *GetEvaluateListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEvaluateListRequest.Unmarshal(m, b)
}
func (m *GetEvaluateListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEvaluateListRequest.Marshal(b, m, deterministic)
}
func (dst *GetEvaluateListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEvaluateListRequest.Merge(dst, src)
}
func (m *GetEvaluateListRequest) XXX_Size() int {
	return xxx_messageInfo_GetEvaluateListRequest.Size(m)
}
func (m *GetEvaluateListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEvaluateListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetEvaluateListRequest proto.InternalMessageInfo

func (m *GetEvaluateListRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetEvaluateListRequest) GetWord() string {
	if m != nil {
		return m.Word
	}
	return ""
}

func (m *GetEvaluateListRequest) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetEvaluateListRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetEvaluateListRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetEvaluateListResponse struct {
	List                 []*EvaluateInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetEvaluateListResponse) Reset()         { *m = GetEvaluateListResponse{} }
func (m *GetEvaluateListResponse) String() string { return proto.CompactTextString(m) }
func (*GetEvaluateListResponse) ProtoMessage()    {}
func (*GetEvaluateListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{37}
}
func (m *GetEvaluateListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEvaluateListResponse.Unmarshal(m, b)
}
func (m *GetEvaluateListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEvaluateListResponse.Marshal(b, m, deterministic)
}
func (dst *GetEvaluateListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEvaluateListResponse.Merge(dst, src)
}
func (m *GetEvaluateListResponse) XXX_Size() int {
	return xxx_messageInfo_GetEvaluateListResponse.Size(m)
}
func (m *GetEvaluateListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEvaluateListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetEvaluateListResponse proto.InternalMessageInfo

func (m *GetEvaluateListResponse) GetList() []*EvaluateInfo {
	if m != nil {
		return m.List
	}
	return nil
}

// 获取用户评价汇总
type GetEvaluateSummaryRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEvaluateSummaryRequest) Reset()         { *m = GetEvaluateSummaryRequest{} }
func (m *GetEvaluateSummaryRequest) String() string { return proto.CompactTextString(m) }
func (*GetEvaluateSummaryRequest) ProtoMessage()    {}
func (*GetEvaluateSummaryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{38}
}
func (m *GetEvaluateSummaryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEvaluateSummaryRequest.Unmarshal(m, b)
}
func (m *GetEvaluateSummaryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEvaluateSummaryRequest.Marshal(b, m, deterministic)
}
func (dst *GetEvaluateSummaryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEvaluateSummaryRequest.Merge(dst, src)
}
func (m *GetEvaluateSummaryRequest) XXX_Size() int {
	return xxx_messageInfo_GetEvaluateSummaryRequest.Size(m)
}
func (m *GetEvaluateSummaryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEvaluateSummaryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetEvaluateSummaryRequest proto.InternalMessageInfo

func (m *GetEvaluateSummaryRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetEvaluateSummaryRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetEvaluateSummaryResponse struct {
	Summary              *EvaluateSummary `protobuf:"bytes,1,opt,name=summary,proto3" json:"summary,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetEvaluateSummaryResponse) Reset()         { *m = GetEvaluateSummaryResponse{} }
func (m *GetEvaluateSummaryResponse) String() string { return proto.CompactTextString(m) }
func (*GetEvaluateSummaryResponse) ProtoMessage()    {}
func (*GetEvaluateSummaryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{39}
}
func (m *GetEvaluateSummaryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEvaluateSummaryResponse.Unmarshal(m, b)
}
func (m *GetEvaluateSummaryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEvaluateSummaryResponse.Marshal(b, m, deterministic)
}
func (dst *GetEvaluateSummaryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEvaluateSummaryResponse.Merge(dst, src)
}
func (m *GetEvaluateSummaryResponse) XXX_Size() int {
	return xxx_messageInfo_GetEvaluateSummaryResponse.Size(m)
}
func (m *GetEvaluateSummaryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEvaluateSummaryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetEvaluateSummaryResponse proto.InternalMessageInfo

func (m *GetEvaluateSummaryResponse) GetSummary() *EvaluateSummary {
	if m != nil {
		return m.Summary
	}
	return nil
}

type EvaluateScoreSummary struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AvgScore             float32  `protobuf:"fixed32,2,opt,name=avg_score,json=avgScore,proto3" json:"avg_score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EvaluateScoreSummary) Reset()         { *m = EvaluateScoreSummary{} }
func (m *EvaluateScoreSummary) String() string { return proto.CompactTextString(m) }
func (*EvaluateScoreSummary) ProtoMessage()    {}
func (*EvaluateScoreSummary) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{40}
}
func (m *EvaluateScoreSummary) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EvaluateScoreSummary.Unmarshal(m, b)
}
func (m *EvaluateScoreSummary) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EvaluateScoreSummary.Marshal(b, m, deterministic)
}
func (dst *EvaluateScoreSummary) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EvaluateScoreSummary.Merge(dst, src)
}
func (m *EvaluateScoreSummary) XXX_Size() int {
	return xxx_messageInfo_EvaluateScoreSummary.Size(m)
}
func (m *EvaluateScoreSummary) XXX_DiscardUnknown() {
	xxx_messageInfo_EvaluateScoreSummary.DiscardUnknown(m)
}

var xxx_messageInfo_EvaluateScoreSummary proto.InternalMessageInfo

func (m *EvaluateScoreSummary) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *EvaluateScoreSummary) GetAvgScore() float32 {
	if m != nil {
		return m.AvgScore
	}
	return 0
}

// 批量获取用户评价汇总
type BatGetEvaluateScoreSummaryRequest struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetEvaluateScoreSummaryRequest) Reset()         { *m = BatGetEvaluateScoreSummaryRequest{} }
func (m *BatGetEvaluateScoreSummaryRequest) String() string { return proto.CompactTextString(m) }
func (*BatGetEvaluateScoreSummaryRequest) ProtoMessage()    {}
func (*BatGetEvaluateScoreSummaryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{41}
}
func (m *BatGetEvaluateScoreSummaryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetEvaluateScoreSummaryRequest.Unmarshal(m, b)
}
func (m *BatGetEvaluateScoreSummaryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetEvaluateScoreSummaryRequest.Marshal(b, m, deterministic)
}
func (dst *BatGetEvaluateScoreSummaryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetEvaluateScoreSummaryRequest.Merge(dst, src)
}
func (m *BatGetEvaluateScoreSummaryRequest) XXX_Size() int {
	return xxx_messageInfo_BatGetEvaluateScoreSummaryRequest.Size(m)
}
func (m *BatGetEvaluateScoreSummaryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetEvaluateScoreSummaryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetEvaluateScoreSummaryRequest proto.InternalMessageInfo

func (m *BatGetEvaluateScoreSummaryRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatGetEvaluateScoreSummaryRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type BatGetEvaluateScoreSummaryResponse struct {
	SummaryList          []*EvaluateScoreSummary `protobuf:"bytes,1,rep,name=summary_list,json=summaryList,proto3" json:"summary_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *BatGetEvaluateScoreSummaryResponse) Reset()         { *m = BatGetEvaluateScoreSummaryResponse{} }
func (m *BatGetEvaluateScoreSummaryResponse) String() string { return proto.CompactTextString(m) }
func (*BatGetEvaluateScoreSummaryResponse) ProtoMessage()    {}
func (*BatGetEvaluateScoreSummaryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{42}
}
func (m *BatGetEvaluateScoreSummaryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetEvaluateScoreSummaryResponse.Unmarshal(m, b)
}
func (m *BatGetEvaluateScoreSummaryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetEvaluateScoreSummaryResponse.Marshal(b, m, deterministic)
}
func (dst *BatGetEvaluateScoreSummaryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetEvaluateScoreSummaryResponse.Merge(dst, src)
}
func (m *BatGetEvaluateScoreSummaryResponse) XXX_Size() int {
	return xxx_messageInfo_BatGetEvaluateScoreSummaryResponse.Size(m)
}
func (m *BatGetEvaluateScoreSummaryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetEvaluateScoreSummaryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetEvaluateScoreSummaryResponse proto.InternalMessageInfo

func (m *BatGetEvaluateScoreSummaryResponse) GetSummaryList() []*EvaluateScoreSummary {
	if m != nil {
		return m.SummaryList
	}
	return nil
}

// 评价
type EvaluateRequest struct {
	Uid                  uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OrderId              string         `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	WordList             []string       `protobuf:"bytes,3,rep,name=word_list,json=wordList,proto3" json:"word_list,omitempty"`
	Content              string         `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	ScoreInfo            *EvaluateScore `protobuf:"bytes,5,opt,name=score_info,json=scoreInfo,proto3" json:"score_info,omitempty"`
	IsAnonymous          bool           `protobuf:"varint,6,opt,name=is_anonymous,json=isAnonymous,proto3" json:"is_anonymous,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *EvaluateRequest) Reset()         { *m = EvaluateRequest{} }
func (m *EvaluateRequest) String() string { return proto.CompactTextString(m) }
func (*EvaluateRequest) ProtoMessage()    {}
func (*EvaluateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{43}
}
func (m *EvaluateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EvaluateRequest.Unmarshal(m, b)
}
func (m *EvaluateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EvaluateRequest.Marshal(b, m, deterministic)
}
func (dst *EvaluateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EvaluateRequest.Merge(dst, src)
}
func (m *EvaluateRequest) XXX_Size() int {
	return xxx_messageInfo_EvaluateRequest.Size(m)
}
func (m *EvaluateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_EvaluateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_EvaluateRequest proto.InternalMessageInfo

func (m *EvaluateRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *EvaluateRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *EvaluateRequest) GetWordList() []string {
	if m != nil {
		return m.WordList
	}
	return nil
}

func (m *EvaluateRequest) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *EvaluateRequest) GetScoreInfo() *EvaluateScore {
	if m != nil {
		return m.ScoreInfo
	}
	return nil
}

func (m *EvaluateRequest) GetIsAnonymous() bool {
	if m != nil {
		return m.IsAnonymous
	}
	return false
}

type EvaluateResponse struct {
	Toast                string   `protobuf:"bytes,1,opt,name=toast,proto3" json:"toast,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EvaluateResponse) Reset()         { *m = EvaluateResponse{} }
func (m *EvaluateResponse) String() string { return proto.CompactTextString(m) }
func (*EvaluateResponse) ProtoMessage()    {}
func (*EvaluateResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{44}
}
func (m *EvaluateResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EvaluateResponse.Unmarshal(m, b)
}
func (m *EvaluateResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EvaluateResponse.Marshal(b, m, deterministic)
}
func (dst *EvaluateResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EvaluateResponse.Merge(dst, src)
}
func (m *EvaluateResponse) XXX_Size() int {
	return xxx_messageInfo_EvaluateResponse.Size(m)
}
func (m *EvaluateResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_EvaluateResponse.DiscardUnknown(m)
}

var xxx_messageInfo_EvaluateResponse proto.InternalMessageInfo

func (m *EvaluateResponse) GetToast() string {
	if m != nil {
		return m.Toast
	}
	return ""
}

// 审核结果回调
type AuditResultCallbackRequest struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Result               uint32   `protobuf:"varint,2,opt,name=result,proto3" json:"result,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AuditResultCallbackRequest) Reset()         { *m = AuditResultCallbackRequest{} }
func (m *AuditResultCallbackRequest) String() string { return proto.CompactTextString(m) }
func (*AuditResultCallbackRequest) ProtoMessage()    {}
func (*AuditResultCallbackRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{45}
}
func (m *AuditResultCallbackRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AuditResultCallbackRequest.Unmarshal(m, b)
}
func (m *AuditResultCallbackRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AuditResultCallbackRequest.Marshal(b, m, deterministic)
}
func (dst *AuditResultCallbackRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AuditResultCallbackRequest.Merge(dst, src)
}
func (m *AuditResultCallbackRequest) XXX_Size() int {
	return xxx_messageInfo_AuditResultCallbackRequest.Size(m)
}
func (m *AuditResultCallbackRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AuditResultCallbackRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AuditResultCallbackRequest proto.InternalMessageInfo

func (m *AuditResultCallbackRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *AuditResultCallbackRequest) GetResult() uint32 {
	if m != nil {
		return m.Result
	}
	return 0
}

type AuditResultCallbackResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AuditResultCallbackResponse) Reset()         { *m = AuditResultCallbackResponse{} }
func (m *AuditResultCallbackResponse) String() string { return proto.CompactTextString(m) }
func (*AuditResultCallbackResponse) ProtoMessage()    {}
func (*AuditResultCallbackResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{46}
}
func (m *AuditResultCallbackResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AuditResultCallbackResponse.Unmarshal(m, b)
}
func (m *AuditResultCallbackResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AuditResultCallbackResponse.Marshal(b, m, deterministic)
}
func (dst *AuditResultCallbackResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AuditResultCallbackResponse.Merge(dst, src)
}
func (m *AuditResultCallbackResponse) XXX_Size() int {
	return xxx_messageInfo_AuditResultCallbackResponse.Size(m)
}
func (m *AuditResultCallbackResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AuditResultCallbackResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AuditResultCallbackResponse proto.InternalMessageInfo

// 防刷单校验
type CheckUserOrderCntLimitRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CoachUid             uint32   `protobuf:"varint,2,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUserOrderCntLimitRequest) Reset()         { *m = CheckUserOrderCntLimitRequest{} }
func (m *CheckUserOrderCntLimitRequest) String() string { return proto.CompactTextString(m) }
func (*CheckUserOrderCntLimitRequest) ProtoMessage()    {}
func (*CheckUserOrderCntLimitRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{47}
}
func (m *CheckUserOrderCntLimitRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserOrderCntLimitRequest.Unmarshal(m, b)
}
func (m *CheckUserOrderCntLimitRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserOrderCntLimitRequest.Marshal(b, m, deterministic)
}
func (dst *CheckUserOrderCntLimitRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserOrderCntLimitRequest.Merge(dst, src)
}
func (m *CheckUserOrderCntLimitRequest) XXX_Size() int {
	return xxx_messageInfo_CheckUserOrderCntLimitRequest.Size(m)
}
func (m *CheckUserOrderCntLimitRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserOrderCntLimitRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserOrderCntLimitRequest proto.InternalMessageInfo

func (m *CheckUserOrderCntLimitRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckUserOrderCntLimitRequest) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

type CheckUserOrderCntLimitResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUserOrderCntLimitResponse) Reset()         { *m = CheckUserOrderCntLimitResponse{} }
func (m *CheckUserOrderCntLimitResponse) String() string { return proto.CompactTextString(m) }
func (*CheckUserOrderCntLimitResponse) ProtoMessage()    {}
func (*CheckUserOrderCntLimitResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{48}
}
func (m *CheckUserOrderCntLimitResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserOrderCntLimitResponse.Unmarshal(m, b)
}
func (m *CheckUserOrderCntLimitResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserOrderCntLimitResponse.Marshal(b, m, deterministic)
}
func (dst *CheckUserOrderCntLimitResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserOrderCntLimitResponse.Merge(dst, src)
}
func (m *CheckUserOrderCntLimitResponse) XXX_Size() int {
	return xxx_messageInfo_CheckUserOrderCntLimitResponse.Size(m)
}
func (m *CheckUserOrderCntLimitResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserOrderCntLimitResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserOrderCntLimitResponse proto.InternalMessageInfo

type GetUserTotalExpenditureAmountMonthlyRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Month                int64    `protobuf:"varint,2,opt,name=month,proto3" json:"month,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserTotalExpenditureAmountMonthlyRequest) Reset() {
	*m = GetUserTotalExpenditureAmountMonthlyRequest{}
}
func (m *GetUserTotalExpenditureAmountMonthlyRequest) String() string {
	return proto.CompactTextString(m)
}
func (*GetUserTotalExpenditureAmountMonthlyRequest) ProtoMessage() {}
func (*GetUserTotalExpenditureAmountMonthlyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{49}
}
func (m *GetUserTotalExpenditureAmountMonthlyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserTotalExpenditureAmountMonthlyRequest.Unmarshal(m, b)
}
func (m *GetUserTotalExpenditureAmountMonthlyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserTotalExpenditureAmountMonthlyRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserTotalExpenditureAmountMonthlyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserTotalExpenditureAmountMonthlyRequest.Merge(dst, src)
}
func (m *GetUserTotalExpenditureAmountMonthlyRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserTotalExpenditureAmountMonthlyRequest.Size(m)
}
func (m *GetUserTotalExpenditureAmountMonthlyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserTotalExpenditureAmountMonthlyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserTotalExpenditureAmountMonthlyRequest proto.InternalMessageInfo

func (m *GetUserTotalExpenditureAmountMonthlyRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserTotalExpenditureAmountMonthlyRequest) GetMonth() int64 {
	if m != nil {
		return m.Month
	}
	return 0
}

type GetUserTotalExpenditureAmountMonthlyResponse struct {
	TotalAmount          int64    `protobuf:"varint,1,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserTotalExpenditureAmountMonthlyResponse) Reset() {
	*m = GetUserTotalExpenditureAmountMonthlyResponse{}
}
func (m *GetUserTotalExpenditureAmountMonthlyResponse) String() string {
	return proto.CompactTextString(m)
}
func (*GetUserTotalExpenditureAmountMonthlyResponse) ProtoMessage() {}
func (*GetUserTotalExpenditureAmountMonthlyResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{50}
}
func (m *GetUserTotalExpenditureAmountMonthlyResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserTotalExpenditureAmountMonthlyResponse.Unmarshal(m, b)
}
func (m *GetUserTotalExpenditureAmountMonthlyResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserTotalExpenditureAmountMonthlyResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserTotalExpenditureAmountMonthlyResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserTotalExpenditureAmountMonthlyResponse.Merge(dst, src)
}
func (m *GetUserTotalExpenditureAmountMonthlyResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserTotalExpenditureAmountMonthlyResponse.Size(m)
}
func (m *GetUserTotalExpenditureAmountMonthlyResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserTotalExpenditureAmountMonthlyResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserTotalExpenditureAmountMonthlyResponse proto.InternalMessageInfo

func (m *GetUserTotalExpenditureAmountMonthlyResponse) GetTotalAmount() int64 {
	if m != nil {
		return m.TotalAmount
	}
	return 0
}

type SearchOrderRequest struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	StatusList           []uint32 `protobuf:"varint,3,rep,packed,name=status_list,json=statusList,proto3" json:"status_list,omitempty"`
	CoachUidList         []uint32 `protobuf:"varint,4,rep,packed,name=coach_uid_list,json=coachUidList,proto3" json:"coach_uid_list,omitempty"`
	StartTime            int64    `protobuf:"varint,5,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              int64    `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchOrderRequest) Reset()         { *m = SearchOrderRequest{} }
func (m *SearchOrderRequest) String() string { return proto.CompactTextString(m) }
func (*SearchOrderRequest) ProtoMessage()    {}
func (*SearchOrderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{51}
}
func (m *SearchOrderRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchOrderRequest.Unmarshal(m, b)
}
func (m *SearchOrderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchOrderRequest.Marshal(b, m, deterministic)
}
func (dst *SearchOrderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchOrderRequest.Merge(dst, src)
}
func (m *SearchOrderRequest) XXX_Size() int {
	return xxx_messageInfo_SearchOrderRequest.Size(m)
}
func (m *SearchOrderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchOrderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SearchOrderRequest proto.InternalMessageInfo

func (m *SearchOrderRequest) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *SearchOrderRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *SearchOrderRequest) GetStatusList() []uint32 {
	if m != nil {
		return m.StatusList
	}
	return nil
}

func (m *SearchOrderRequest) GetCoachUidList() []uint32 {
	if m != nil {
		return m.CoachUidList
	}
	return nil
}

func (m *SearchOrderRequest) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *SearchOrderRequest) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type SearchOrderResponse struct {
	OrderList            []*OrderSimpleInfo `protobuf:"bytes,1,rep,name=order_list,json=orderList,proto3" json:"order_list,omitempty"`
	NextOffset           uint32             `protobuf:"varint,2,opt,name=next_offset,json=nextOffset,proto3" json:"next_offset,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SearchOrderResponse) Reset()         { *m = SearchOrderResponse{} }
func (m *SearchOrderResponse) String() string { return proto.CompactTextString(m) }
func (*SearchOrderResponse) ProtoMessage()    {}
func (*SearchOrderResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{52}
}
func (m *SearchOrderResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchOrderResponse.Unmarshal(m, b)
}
func (m *SearchOrderResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchOrderResponse.Marshal(b, m, deterministic)
}
func (dst *SearchOrderResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchOrderResponse.Merge(dst, src)
}
func (m *SearchOrderResponse) XXX_Size() int {
	return xxx_messageInfo_SearchOrderResponse.Size(m)
}
func (m *SearchOrderResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchOrderResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SearchOrderResponse proto.InternalMessageInfo

func (m *SearchOrderResponse) GetOrderList() []*OrderSimpleInfo {
	if m != nil {
		return m.OrderList
	}
	return nil
}

func (m *SearchOrderResponse) GetNextOffset() uint32 {
	if m != nil {
		return m.NextOffset
	}
	return 0
}

type GetCoachOrderStatRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	StartTime            int64    `protobuf:"varint,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              int64    `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCoachOrderStatRequest) Reset()         { *m = GetCoachOrderStatRequest{} }
func (m *GetCoachOrderStatRequest) String() string { return proto.CompactTextString(m) }
func (*GetCoachOrderStatRequest) ProtoMessage()    {}
func (*GetCoachOrderStatRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{53}
}
func (m *GetCoachOrderStatRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachOrderStatRequest.Unmarshal(m, b)
}
func (m *GetCoachOrderStatRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachOrderStatRequest.Marshal(b, m, deterministic)
}
func (dst *GetCoachOrderStatRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachOrderStatRequest.Merge(dst, src)
}
func (m *GetCoachOrderStatRequest) XXX_Size() int {
	return xxx_messageInfo_GetCoachOrderStatRequest.Size(m)
}
func (m *GetCoachOrderStatRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachOrderStatRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachOrderStatRequest proto.InternalMessageInfo

func (m *GetCoachOrderStatRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetCoachOrderStatRequest) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *GetCoachOrderStatRequest) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetCoachOrderStatResponse struct {
	OrderCnt             uint32   `protobuf:"varint,1,opt,name=order_cnt,json=orderCnt,proto3" json:"order_cnt,omitempty"`
	CommitAmount         uint32   `protobuf:"varint,2,opt,name=commit_amount,json=commitAmount,proto3" json:"commit_amount,omitempty"`
	OrderFlow            uint32   `protobuf:"varint,3,opt,name=order_flow,json=orderFlow,proto3" json:"order_flow,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCoachOrderStatResponse) Reset()         { *m = GetCoachOrderStatResponse{} }
func (m *GetCoachOrderStatResponse) String() string { return proto.CompactTextString(m) }
func (*GetCoachOrderStatResponse) ProtoMessage()    {}
func (*GetCoachOrderStatResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{54}
}
func (m *GetCoachOrderStatResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachOrderStatResponse.Unmarshal(m, b)
}
func (m *GetCoachOrderStatResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachOrderStatResponse.Marshal(b, m, deterministic)
}
func (dst *GetCoachOrderStatResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachOrderStatResponse.Merge(dst, src)
}
func (m *GetCoachOrderStatResponse) XXX_Size() int {
	return xxx_messageInfo_GetCoachOrderStatResponse.Size(m)
}
func (m *GetCoachOrderStatResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachOrderStatResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachOrderStatResponse proto.InternalMessageInfo

func (m *GetCoachOrderStatResponse) GetOrderCnt() uint32 {
	if m != nil {
		return m.OrderCnt
	}
	return 0
}

func (m *GetCoachOrderStatResponse) GetCommitAmount() uint32 {
	if m != nil {
		return m.CommitAmount
	}
	return 0
}

func (m *GetCoachOrderStatResponse) GetOrderFlow() uint32 {
	if m != nil {
		return m.OrderFlow
	}
	return 0
}

// ========================== 优惠券 ===========================
// 优惠券配置
type CouponConfig struct {
	CouponName           string   `protobuf:"bytes,2,opt,name=coupon_name,json=couponName,proto3" json:"coupon_name,omitempty"`
	CouponType           uint32   `protobuf:"varint,3,opt,name=coupon_type,json=couponType,proto3" json:"coupon_type,omitempty"`
	ReduceRequire        uint32   `protobuf:"varint,4,opt,name=reduce_require,json=reduceRequire,proto3" json:"reduce_require,omitempty"`
	ReducePrice          uint32   `protobuf:"varint,5,opt,name=reduce_price,json=reducePrice,proto3" json:"reduce_price,omitempty"`
	Discount             uint32   `protobuf:"varint,6,opt,name=discount,proto3" json:"discount,omitempty"`
	UsageLimitType       uint32   `protobuf:"varint,7,opt,name=usage_limit_type,json=usageLimitType,proto3" json:"usage_limit_type,omitempty"`
	UsageLimitNoOrderDay uint32   `protobuf:"varint,8,opt,name=usage_limit_no_order_day,json=usageLimitNoOrderDay,proto3" json:"usage_limit_no_order_day,omitempty"`
	UsageLimitText       string   `protobuf:"bytes,9,opt,name=usage_limit_text,json=usageLimitText,proto3" json:"usage_limit_text,omitempty"`
	EffectiveDay         uint32   `protobuf:"varint,10,opt,name=effective_day,json=effectiveDay,proto3" json:"effective_day,omitempty"`
	ExpireDay            uint32   `protobuf:"varint,11,opt,name=expire_day,json=expireDay,proto3" json:"expire_day,omitempty"`
	MustInCoachPool      bool     `protobuf:"varint,12,opt,name=must_in_coach_pool,json=mustInCoachPool,proto3" json:"must_in_coach_pool,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CouponConfig) Reset()         { *m = CouponConfig{} }
func (m *CouponConfig) String() string { return proto.CompactTextString(m) }
func (*CouponConfig) ProtoMessage()    {}
func (*CouponConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{55}
}
func (m *CouponConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CouponConfig.Unmarshal(m, b)
}
func (m *CouponConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CouponConfig.Marshal(b, m, deterministic)
}
func (dst *CouponConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CouponConfig.Merge(dst, src)
}
func (m *CouponConfig) XXX_Size() int {
	return xxx_messageInfo_CouponConfig.Size(m)
}
func (m *CouponConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_CouponConfig.DiscardUnknown(m)
}

var xxx_messageInfo_CouponConfig proto.InternalMessageInfo

func (m *CouponConfig) GetCouponName() string {
	if m != nil {
		return m.CouponName
	}
	return ""
}

func (m *CouponConfig) GetCouponType() uint32 {
	if m != nil {
		return m.CouponType
	}
	return 0
}

func (m *CouponConfig) GetReduceRequire() uint32 {
	if m != nil {
		return m.ReduceRequire
	}
	return 0
}

func (m *CouponConfig) GetReducePrice() uint32 {
	if m != nil {
		return m.ReducePrice
	}
	return 0
}

func (m *CouponConfig) GetDiscount() uint32 {
	if m != nil {
		return m.Discount
	}
	return 0
}

func (m *CouponConfig) GetUsageLimitType() uint32 {
	if m != nil {
		return m.UsageLimitType
	}
	return 0
}

func (m *CouponConfig) GetUsageLimitNoOrderDay() uint32 {
	if m != nil {
		return m.UsageLimitNoOrderDay
	}
	return 0
}

func (m *CouponConfig) GetUsageLimitText() string {
	if m != nil {
		return m.UsageLimitText
	}
	return ""
}

func (m *CouponConfig) GetEffectiveDay() uint32 {
	if m != nil {
		return m.EffectiveDay
	}
	return 0
}

func (m *CouponConfig) GetExpireDay() uint32 {
	if m != nil {
		return m.ExpireDay
	}
	return 0
}

func (m *CouponConfig) GetMustInCoachPool() bool {
	if m != nil {
		return m.MustInCoachPool
	}
	return false
}

type CouponConfigRecord struct {
	Id                   uint32        `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CouponConfig         *CouponConfig `protobuf:"bytes,2,opt,name=coupon_config,json=couponConfig,proto3" json:"coupon_config,omitempty"`
	Operator             string        `protobuf:"bytes,3,opt,name=operator,proto3" json:"operator,omitempty"`
	CreateTime           int64         `protobuf:"varint,4,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CouponConfigRecord) Reset()         { *m = CouponConfigRecord{} }
func (m *CouponConfigRecord) String() string { return proto.CompactTextString(m) }
func (*CouponConfigRecord) ProtoMessage()    {}
func (*CouponConfigRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{56}
}
func (m *CouponConfigRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CouponConfigRecord.Unmarshal(m, b)
}
func (m *CouponConfigRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CouponConfigRecord.Marshal(b, m, deterministic)
}
func (dst *CouponConfigRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CouponConfigRecord.Merge(dst, src)
}
func (m *CouponConfigRecord) XXX_Size() int {
	return xxx_messageInfo_CouponConfigRecord.Size(m)
}
func (m *CouponConfigRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_CouponConfigRecord.DiscardUnknown(m)
}

var xxx_messageInfo_CouponConfigRecord proto.InternalMessageInfo

func (m *CouponConfigRecord) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CouponConfigRecord) GetCouponConfig() *CouponConfig {
	if m != nil {
		return m.CouponConfig
	}
	return nil
}

func (m *CouponConfigRecord) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *CouponConfigRecord) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

type CreateCouponConfigRequest struct {
	CouponConfig         *CouponConfig `protobuf:"bytes,1,opt,name=coupon_config,json=couponConfig,proto3" json:"coupon_config,omitempty"`
	Operator             string        `protobuf:"bytes,2,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CreateCouponConfigRequest) Reset()         { *m = CreateCouponConfigRequest{} }
func (m *CreateCouponConfigRequest) String() string { return proto.CompactTextString(m) }
func (*CreateCouponConfigRequest) ProtoMessage()    {}
func (*CreateCouponConfigRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{57}
}
func (m *CreateCouponConfigRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateCouponConfigRequest.Unmarshal(m, b)
}
func (m *CreateCouponConfigRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateCouponConfigRequest.Marshal(b, m, deterministic)
}
func (dst *CreateCouponConfigRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateCouponConfigRequest.Merge(dst, src)
}
func (m *CreateCouponConfigRequest) XXX_Size() int {
	return xxx_messageInfo_CreateCouponConfigRequest.Size(m)
}
func (m *CreateCouponConfigRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateCouponConfigRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateCouponConfigRequest proto.InternalMessageInfo

func (m *CreateCouponConfigRequest) GetCouponConfig() *CouponConfig {
	if m != nil {
		return m.CouponConfig
	}
	return nil
}

func (m *CreateCouponConfigRequest) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type CreateCouponConfigResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateCouponConfigResponse) Reset()         { *m = CreateCouponConfigResponse{} }
func (m *CreateCouponConfigResponse) String() string { return proto.CompactTextString(m) }
func (*CreateCouponConfigResponse) ProtoMessage()    {}
func (*CreateCouponConfigResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{58}
}
func (m *CreateCouponConfigResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateCouponConfigResponse.Unmarshal(m, b)
}
func (m *CreateCouponConfigResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateCouponConfigResponse.Marshal(b, m, deterministic)
}
func (dst *CreateCouponConfigResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateCouponConfigResponse.Merge(dst, src)
}
func (m *CreateCouponConfigResponse) XXX_Size() int {
	return xxx_messageInfo_CreateCouponConfigResponse.Size(m)
}
func (m *CreateCouponConfigResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateCouponConfigResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateCouponConfigResponse proto.InternalMessageInfo

type GetCouponConfigListRequest struct {
	Page                 uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCouponConfigListRequest) Reset()         { *m = GetCouponConfigListRequest{} }
func (m *GetCouponConfigListRequest) String() string { return proto.CompactTextString(m) }
func (*GetCouponConfigListRequest) ProtoMessage()    {}
func (*GetCouponConfigListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{59}
}
func (m *GetCouponConfigListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCouponConfigListRequest.Unmarshal(m, b)
}
func (m *GetCouponConfigListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCouponConfigListRequest.Marshal(b, m, deterministic)
}
func (dst *GetCouponConfigListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCouponConfigListRequest.Merge(dst, src)
}
func (m *GetCouponConfigListRequest) XXX_Size() int {
	return xxx_messageInfo_GetCouponConfigListRequest.Size(m)
}
func (m *GetCouponConfigListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCouponConfigListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCouponConfigListRequest proto.InternalMessageInfo

func (m *GetCouponConfigListRequest) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetCouponConfigListRequest) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetCouponConfigListResponse struct {
	Total                uint32                `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	ConfList             []*CouponConfigRecord `protobuf:"bytes,2,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetCouponConfigListResponse) Reset()         { *m = GetCouponConfigListResponse{} }
func (m *GetCouponConfigListResponse) String() string { return proto.CompactTextString(m) }
func (*GetCouponConfigListResponse) ProtoMessage()    {}
func (*GetCouponConfigListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{60}
}
func (m *GetCouponConfigListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCouponConfigListResponse.Unmarshal(m, b)
}
func (m *GetCouponConfigListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCouponConfigListResponse.Marshal(b, m, deterministic)
}
func (dst *GetCouponConfigListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCouponConfigListResponse.Merge(dst, src)
}
func (m *GetCouponConfigListResponse) XXX_Size() int {
	return xxx_messageInfo_GetCouponConfigListResponse.Size(m)
}
func (m *GetCouponConfigListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCouponConfigListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCouponConfigListResponse proto.InternalMessageInfo

func (m *GetCouponConfigListResponse) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetCouponConfigListResponse) GetConfList() []*CouponConfigRecord {
	if m != nil {
		return m.ConfList
	}
	return nil
}

type GetCouponConfigByIdsRequest struct {
	ConfIds              []uint32 `protobuf:"varint,1,rep,packed,name=conf_ids,json=confIds,proto3" json:"conf_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCouponConfigByIdsRequest) Reset()         { *m = GetCouponConfigByIdsRequest{} }
func (m *GetCouponConfigByIdsRequest) String() string { return proto.CompactTextString(m) }
func (*GetCouponConfigByIdsRequest) ProtoMessage()    {}
func (*GetCouponConfigByIdsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{61}
}
func (m *GetCouponConfigByIdsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCouponConfigByIdsRequest.Unmarshal(m, b)
}
func (m *GetCouponConfigByIdsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCouponConfigByIdsRequest.Marshal(b, m, deterministic)
}
func (dst *GetCouponConfigByIdsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCouponConfigByIdsRequest.Merge(dst, src)
}
func (m *GetCouponConfigByIdsRequest) XXX_Size() int {
	return xxx_messageInfo_GetCouponConfigByIdsRequest.Size(m)
}
func (m *GetCouponConfigByIdsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCouponConfigByIdsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCouponConfigByIdsRequest proto.InternalMessageInfo

func (m *GetCouponConfigByIdsRequest) GetConfIds() []uint32 {
	if m != nil {
		return m.ConfIds
	}
	return nil
}

type GetCouponConfigByIdsResponse struct {
	ConfList             []*CouponConfigRecord `protobuf:"bytes,1,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetCouponConfigByIdsResponse) Reset()         { *m = GetCouponConfigByIdsResponse{} }
func (m *GetCouponConfigByIdsResponse) String() string { return proto.CompactTextString(m) }
func (*GetCouponConfigByIdsResponse) ProtoMessage()    {}
func (*GetCouponConfigByIdsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{62}
}
func (m *GetCouponConfigByIdsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCouponConfigByIdsResponse.Unmarshal(m, b)
}
func (m *GetCouponConfigByIdsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCouponConfigByIdsResponse.Marshal(b, m, deterministic)
}
func (dst *GetCouponConfigByIdsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCouponConfigByIdsResponse.Merge(dst, src)
}
func (m *GetCouponConfigByIdsResponse) XXX_Size() int {
	return xxx_messageInfo_GetCouponConfigByIdsResponse.Size(m)
}
func (m *GetCouponConfigByIdsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCouponConfigByIdsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCouponConfigByIdsResponse proto.InternalMessageInfo

func (m *GetCouponConfigByIdsResponse) GetConfList() []*CouponConfigRecord {
	if m != nil {
		return m.ConfList
	}
	return nil
}

type AddManualGrantCouponTaskRequest struct {
	CouponConfIds        []uint32 `protobuf:"varint,1,rep,packed,name=coupon_conf_ids,json=couponConfIds,proto3" json:"coupon_conf_ids,omitempty"`
	GrantType            uint32   `protobuf:"varint,2,opt,name=grant_type,json=grantType,proto3" json:"grant_type,omitempty"`
	IdList               []string `protobuf:"bytes,3,rep,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	FilePath             string   `protobuf:"bytes,4,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
	GroupId              []uint32 `protobuf:"varint,5,rep,packed,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Operator             string   `protobuf:"bytes,6,opt,name=operator,proto3" json:"operator,omitempty"`
	GrantTime            uint32   `protobuf:"varint,7,opt,name=grant_time,json=grantTime,proto3" json:"grant_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddManualGrantCouponTaskRequest) Reset()         { *m = AddManualGrantCouponTaskRequest{} }
func (m *AddManualGrantCouponTaskRequest) String() string { return proto.CompactTextString(m) }
func (*AddManualGrantCouponTaskRequest) ProtoMessage()    {}
func (*AddManualGrantCouponTaskRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{63}
}
func (m *AddManualGrantCouponTaskRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddManualGrantCouponTaskRequest.Unmarshal(m, b)
}
func (m *AddManualGrantCouponTaskRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddManualGrantCouponTaskRequest.Marshal(b, m, deterministic)
}
func (dst *AddManualGrantCouponTaskRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddManualGrantCouponTaskRequest.Merge(dst, src)
}
func (m *AddManualGrantCouponTaskRequest) XXX_Size() int {
	return xxx_messageInfo_AddManualGrantCouponTaskRequest.Size(m)
}
func (m *AddManualGrantCouponTaskRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddManualGrantCouponTaskRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddManualGrantCouponTaskRequest proto.InternalMessageInfo

func (m *AddManualGrantCouponTaskRequest) GetCouponConfIds() []uint32 {
	if m != nil {
		return m.CouponConfIds
	}
	return nil
}

func (m *AddManualGrantCouponTaskRequest) GetGrantType() uint32 {
	if m != nil {
		return m.GrantType
	}
	return 0
}

func (m *AddManualGrantCouponTaskRequest) GetIdList() []string {
	if m != nil {
		return m.IdList
	}
	return nil
}

func (m *AddManualGrantCouponTaskRequest) GetFilePath() string {
	if m != nil {
		return m.FilePath
	}
	return ""
}

func (m *AddManualGrantCouponTaskRequest) GetGroupId() []uint32 {
	if m != nil {
		return m.GroupId
	}
	return nil
}

func (m *AddManualGrantCouponTaskRequest) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *AddManualGrantCouponTaskRequest) GetGrantTime() uint32 {
	if m != nil {
		return m.GrantTime
	}
	return 0
}

type AddManualGrantCouponTaskResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddManualGrantCouponTaskResponse) Reset()         { *m = AddManualGrantCouponTaskResponse{} }
func (m *AddManualGrantCouponTaskResponse) String() string { return proto.CompactTextString(m) }
func (*AddManualGrantCouponTaskResponse) ProtoMessage()    {}
func (*AddManualGrantCouponTaskResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{64}
}
func (m *AddManualGrantCouponTaskResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddManualGrantCouponTaskResponse.Unmarshal(m, b)
}
func (m *AddManualGrantCouponTaskResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddManualGrantCouponTaskResponse.Marshal(b, m, deterministic)
}
func (dst *AddManualGrantCouponTaskResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddManualGrantCouponTaskResponse.Merge(dst, src)
}
func (m *AddManualGrantCouponTaskResponse) XXX_Size() int {
	return xxx_messageInfo_AddManualGrantCouponTaskResponse.Size(m)
}
func (m *AddManualGrantCouponTaskResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddManualGrantCouponTaskResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddManualGrantCouponTaskResponse proto.InternalMessageInfo

type StopManualGrantCouponTaskRequest struct {
	TaskId               uint32   `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	Operator             string   `protobuf:"bytes,2,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StopManualGrantCouponTaskRequest) Reset()         { *m = StopManualGrantCouponTaskRequest{} }
func (m *StopManualGrantCouponTaskRequest) String() string { return proto.CompactTextString(m) }
func (*StopManualGrantCouponTaskRequest) ProtoMessage()    {}
func (*StopManualGrantCouponTaskRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{65}
}
func (m *StopManualGrantCouponTaskRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StopManualGrantCouponTaskRequest.Unmarshal(m, b)
}
func (m *StopManualGrantCouponTaskRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StopManualGrantCouponTaskRequest.Marshal(b, m, deterministic)
}
func (dst *StopManualGrantCouponTaskRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StopManualGrantCouponTaskRequest.Merge(dst, src)
}
func (m *StopManualGrantCouponTaskRequest) XXX_Size() int {
	return xxx_messageInfo_StopManualGrantCouponTaskRequest.Size(m)
}
func (m *StopManualGrantCouponTaskRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_StopManualGrantCouponTaskRequest.DiscardUnknown(m)
}

var xxx_messageInfo_StopManualGrantCouponTaskRequest proto.InternalMessageInfo

func (m *StopManualGrantCouponTaskRequest) GetTaskId() uint32 {
	if m != nil {
		return m.TaskId
	}
	return 0
}

func (m *StopManualGrantCouponTaskRequest) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type StopManualGrantCouponTaskResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StopManualGrantCouponTaskResponse) Reset()         { *m = StopManualGrantCouponTaskResponse{} }
func (m *StopManualGrantCouponTaskResponse) String() string { return proto.CompactTextString(m) }
func (*StopManualGrantCouponTaskResponse) ProtoMessage()    {}
func (*StopManualGrantCouponTaskResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{66}
}
func (m *StopManualGrantCouponTaskResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StopManualGrantCouponTaskResponse.Unmarshal(m, b)
}
func (m *StopManualGrantCouponTaskResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StopManualGrantCouponTaskResponse.Marshal(b, m, deterministic)
}
func (dst *StopManualGrantCouponTaskResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StopManualGrantCouponTaskResponse.Merge(dst, src)
}
func (m *StopManualGrantCouponTaskResponse) XXX_Size() int {
	return xxx_messageInfo_StopManualGrantCouponTaskResponse.Size(m)
}
func (m *StopManualGrantCouponTaskResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_StopManualGrantCouponTaskResponse.DiscardUnknown(m)
}

var xxx_messageInfo_StopManualGrantCouponTaskResponse proto.InternalMessageInfo

type RevokeManualGrantCouponTaskRequest struct {
	TaskId               uint32   `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	Operator             string   `protobuf:"bytes,2,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RevokeManualGrantCouponTaskRequest) Reset()         { *m = RevokeManualGrantCouponTaskRequest{} }
func (m *RevokeManualGrantCouponTaskRequest) String() string { return proto.CompactTextString(m) }
func (*RevokeManualGrantCouponTaskRequest) ProtoMessage()    {}
func (*RevokeManualGrantCouponTaskRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{67}
}
func (m *RevokeManualGrantCouponTaskRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RevokeManualGrantCouponTaskRequest.Unmarshal(m, b)
}
func (m *RevokeManualGrantCouponTaskRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RevokeManualGrantCouponTaskRequest.Marshal(b, m, deterministic)
}
func (dst *RevokeManualGrantCouponTaskRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RevokeManualGrantCouponTaskRequest.Merge(dst, src)
}
func (m *RevokeManualGrantCouponTaskRequest) XXX_Size() int {
	return xxx_messageInfo_RevokeManualGrantCouponTaskRequest.Size(m)
}
func (m *RevokeManualGrantCouponTaskRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RevokeManualGrantCouponTaskRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RevokeManualGrantCouponTaskRequest proto.InternalMessageInfo

func (m *RevokeManualGrantCouponTaskRequest) GetTaskId() uint32 {
	if m != nil {
		return m.TaskId
	}
	return 0
}

func (m *RevokeManualGrantCouponTaskRequest) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type RevokeManualGrantCouponTaskResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RevokeManualGrantCouponTaskResponse) Reset()         { *m = RevokeManualGrantCouponTaskResponse{} }
func (m *RevokeManualGrantCouponTaskResponse) String() string { return proto.CompactTextString(m) }
func (*RevokeManualGrantCouponTaskResponse) ProtoMessage()    {}
func (*RevokeManualGrantCouponTaskResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{68}
}
func (m *RevokeManualGrantCouponTaskResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RevokeManualGrantCouponTaskResponse.Unmarshal(m, b)
}
func (m *RevokeManualGrantCouponTaskResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RevokeManualGrantCouponTaskResponse.Marshal(b, m, deterministic)
}
func (dst *RevokeManualGrantCouponTaskResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RevokeManualGrantCouponTaskResponse.Merge(dst, src)
}
func (m *RevokeManualGrantCouponTaskResponse) XXX_Size() int {
	return xxx_messageInfo_RevokeManualGrantCouponTaskResponse.Size(m)
}
func (m *RevokeManualGrantCouponTaskResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RevokeManualGrantCouponTaskResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RevokeManualGrantCouponTaskResponse proto.InternalMessageInfo

type ManualGrantCouponTask struct {
	TaskId               uint32                `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	ConfList             []*CouponConfigRecord `protobuf:"bytes,2,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	GrantType            uint32                `protobuf:"varint,3,opt,name=grant_type,json=grantType,proto3" json:"grant_type,omitempty"`
	IdList               []string              `protobuf:"bytes,4,rep,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	FilePath             string                `protobuf:"bytes,5,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
	GroupId              []uint32              `protobuf:"varint,6,rep,packed,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Operator             string                `protobuf:"bytes,7,opt,name=operator,proto3" json:"operator,omitempty"`
	CreateTime           int64                 `protobuf:"varint,8,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	TaskUserCount        uint32                `protobuf:"varint,9,opt,name=task_user_count,json=taskUserCount,proto3" json:"task_user_count,omitempty"`
	GrantUserCount       uint32                `protobuf:"varint,10,opt,name=grant_user_count,json=grantUserCount,proto3" json:"grant_user_count,omitempty"`
	Status               uint32                `protobuf:"varint,11,opt,name=status,proto3" json:"status,omitempty"`
	TaskGrantMoney       uint32                `protobuf:"varint,12,opt,name=task_grant_money,json=taskGrantMoney,proto3" json:"task_grant_money,omitempty"`
	RevokeUserCount      uint32                `protobuf:"varint,13,opt,name=revoke_user_count,json=revokeUserCount,proto3" json:"revoke_user_count,omitempty"`
	RevokeCouponCount    uint32                `protobuf:"varint,14,opt,name=revoke_coupon_count,json=revokeCouponCount,proto3" json:"revoke_coupon_count,omitempty"`
	RevokeCouponMoney    uint32                `protobuf:"varint,15,opt,name=revoke_coupon_money,json=revokeCouponMoney,proto3" json:"revoke_coupon_money,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *ManualGrantCouponTask) Reset()         { *m = ManualGrantCouponTask{} }
func (m *ManualGrantCouponTask) String() string { return proto.CompactTextString(m) }
func (*ManualGrantCouponTask) ProtoMessage()    {}
func (*ManualGrantCouponTask) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{69}
}
func (m *ManualGrantCouponTask) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ManualGrantCouponTask.Unmarshal(m, b)
}
func (m *ManualGrantCouponTask) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ManualGrantCouponTask.Marshal(b, m, deterministic)
}
func (dst *ManualGrantCouponTask) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ManualGrantCouponTask.Merge(dst, src)
}
func (m *ManualGrantCouponTask) XXX_Size() int {
	return xxx_messageInfo_ManualGrantCouponTask.Size(m)
}
func (m *ManualGrantCouponTask) XXX_DiscardUnknown() {
	xxx_messageInfo_ManualGrantCouponTask.DiscardUnknown(m)
}

var xxx_messageInfo_ManualGrantCouponTask proto.InternalMessageInfo

func (m *ManualGrantCouponTask) GetTaskId() uint32 {
	if m != nil {
		return m.TaskId
	}
	return 0
}

func (m *ManualGrantCouponTask) GetConfList() []*CouponConfigRecord {
	if m != nil {
		return m.ConfList
	}
	return nil
}

func (m *ManualGrantCouponTask) GetGrantType() uint32 {
	if m != nil {
		return m.GrantType
	}
	return 0
}

func (m *ManualGrantCouponTask) GetIdList() []string {
	if m != nil {
		return m.IdList
	}
	return nil
}

func (m *ManualGrantCouponTask) GetFilePath() string {
	if m != nil {
		return m.FilePath
	}
	return ""
}

func (m *ManualGrantCouponTask) GetGroupId() []uint32 {
	if m != nil {
		return m.GroupId
	}
	return nil
}

func (m *ManualGrantCouponTask) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *ManualGrantCouponTask) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *ManualGrantCouponTask) GetTaskUserCount() uint32 {
	if m != nil {
		return m.TaskUserCount
	}
	return 0
}

func (m *ManualGrantCouponTask) GetGrantUserCount() uint32 {
	if m != nil {
		return m.GrantUserCount
	}
	return 0
}

func (m *ManualGrantCouponTask) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *ManualGrantCouponTask) GetTaskGrantMoney() uint32 {
	if m != nil {
		return m.TaskGrantMoney
	}
	return 0
}

func (m *ManualGrantCouponTask) GetRevokeUserCount() uint32 {
	if m != nil {
		return m.RevokeUserCount
	}
	return 0
}

func (m *ManualGrantCouponTask) GetRevokeCouponCount() uint32 {
	if m != nil {
		return m.RevokeCouponCount
	}
	return 0
}

func (m *ManualGrantCouponTask) GetRevokeCouponMoney() uint32 {
	if m != nil {
		return m.RevokeCouponMoney
	}
	return 0
}

type GetManualGrantCouponTasksRequest struct {
	Page                 uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetManualGrantCouponTasksRequest) Reset()         { *m = GetManualGrantCouponTasksRequest{} }
func (m *GetManualGrantCouponTasksRequest) String() string { return proto.CompactTextString(m) }
func (*GetManualGrantCouponTasksRequest) ProtoMessage()    {}
func (*GetManualGrantCouponTasksRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{70}
}
func (m *GetManualGrantCouponTasksRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetManualGrantCouponTasksRequest.Unmarshal(m, b)
}
func (m *GetManualGrantCouponTasksRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetManualGrantCouponTasksRequest.Marshal(b, m, deterministic)
}
func (dst *GetManualGrantCouponTasksRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetManualGrantCouponTasksRequest.Merge(dst, src)
}
func (m *GetManualGrantCouponTasksRequest) XXX_Size() int {
	return xxx_messageInfo_GetManualGrantCouponTasksRequest.Size(m)
}
func (m *GetManualGrantCouponTasksRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetManualGrantCouponTasksRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetManualGrantCouponTasksRequest proto.InternalMessageInfo

func (m *GetManualGrantCouponTasksRequest) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetManualGrantCouponTasksRequest) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetManualGrantCouponTasksResponse struct {
	Total                uint32                   `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	TaskList             []*ManualGrantCouponTask `protobuf:"bytes,2,rep,name=task_list,json=taskList,proto3" json:"task_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetManualGrantCouponTasksResponse) Reset()         { *m = GetManualGrantCouponTasksResponse{} }
func (m *GetManualGrantCouponTasksResponse) String() string { return proto.CompactTextString(m) }
func (*GetManualGrantCouponTasksResponse) ProtoMessage()    {}
func (*GetManualGrantCouponTasksResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{71}
}
func (m *GetManualGrantCouponTasksResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetManualGrantCouponTasksResponse.Unmarshal(m, b)
}
func (m *GetManualGrantCouponTasksResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetManualGrantCouponTasksResponse.Marshal(b, m, deterministic)
}
func (dst *GetManualGrantCouponTasksResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetManualGrantCouponTasksResponse.Merge(dst, src)
}
func (m *GetManualGrantCouponTasksResponse) XXX_Size() int {
	return xxx_messageInfo_GetManualGrantCouponTasksResponse.Size(m)
}
func (m *GetManualGrantCouponTasksResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetManualGrantCouponTasksResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetManualGrantCouponTasksResponse proto.InternalMessageInfo

func (m *GetManualGrantCouponTasksResponse) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetManualGrantCouponTasksResponse) GetTaskList() []*ManualGrantCouponTask {
	if m != nil {
		return m.TaskList
	}
	return nil
}

type CouponGianSourceCommitOrder struct {
	CoachUid             uint32   `protobuf:"varint,1,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	CoachGuildId         uint32   `protobuf:"varint,2,opt,name=coach_guild_id,json=coachGuildId,proto3" json:"coach_guild_id,omitempty"`
	OrderId              string   `protobuf:"bytes,3,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CouponGianSourceCommitOrder) Reset()         { *m = CouponGianSourceCommitOrder{} }
func (m *CouponGianSourceCommitOrder) String() string { return proto.CompactTextString(m) }
func (*CouponGianSourceCommitOrder) ProtoMessage()    {}
func (*CouponGianSourceCommitOrder) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{72}
}
func (m *CouponGianSourceCommitOrder) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CouponGianSourceCommitOrder.Unmarshal(m, b)
}
func (m *CouponGianSourceCommitOrder) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CouponGianSourceCommitOrder.Marshal(b, m, deterministic)
}
func (dst *CouponGianSourceCommitOrder) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CouponGianSourceCommitOrder.Merge(dst, src)
}
func (m *CouponGianSourceCommitOrder) XXX_Size() int {
	return xxx_messageInfo_CouponGianSourceCommitOrder.Size(m)
}
func (m *CouponGianSourceCommitOrder) XXX_DiscardUnknown() {
	xxx_messageInfo_CouponGianSourceCommitOrder.DiscardUnknown(m)
}

var xxx_messageInfo_CouponGianSourceCommitOrder proto.InternalMessageInfo

func (m *CouponGianSourceCommitOrder) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

func (m *CouponGianSourceCommitOrder) GetCoachGuildId() uint32 {
	if m != nil {
		return m.CoachGuildId
	}
	return 0
}

func (m *CouponGianSourceCommitOrder) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type CouponGianSourceManualGrant struct {
	TaskId               uint32   `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	MsgId                uint32   `protobuf:"varint,2,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CouponGianSourceManualGrant) Reset()         { *m = CouponGianSourceManualGrant{} }
func (m *CouponGianSourceManualGrant) String() string { return proto.CompactTextString(m) }
func (*CouponGianSourceManualGrant) ProtoMessage()    {}
func (*CouponGianSourceManualGrant) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{73}
}
func (m *CouponGianSourceManualGrant) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CouponGianSourceManualGrant.Unmarshal(m, b)
}
func (m *CouponGianSourceManualGrant) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CouponGianSourceManualGrant.Marshal(b, m, deterministic)
}
func (dst *CouponGianSourceManualGrant) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CouponGianSourceManualGrant.Merge(dst, src)
}
func (m *CouponGianSourceManualGrant) XXX_Size() int {
	return xxx_messageInfo_CouponGianSourceManualGrant.Size(m)
}
func (m *CouponGianSourceManualGrant) XXX_DiscardUnknown() {
	xxx_messageInfo_CouponGianSourceManualGrant.DiscardUnknown(m)
}

var xxx_messageInfo_CouponGianSourceManualGrant proto.InternalMessageInfo

func (m *CouponGianSourceManualGrant) GetTaskId() uint32 {
	if m != nil {
		return m.TaskId
	}
	return 0
}

func (m *CouponGianSourceManualGrant) GetMsgId() uint32 {
	if m != nil {
		return m.MsgId
	}
	return 0
}

// 优惠券信息
type Coupon struct {
	CouponId             string                       `protobuf:"bytes,1,opt,name=coupon_id,json=couponId,proto3" json:"coupon_id,omitempty"`
	CoponName            string                       `protobuf:"bytes,2,opt,name=copon_name,json=coponName,proto3" json:"copon_name,omitempty"`
	CouponType           uint32                       `protobuf:"varint,3,opt,name=coupon_type,json=couponType,proto3" json:"coupon_type,omitempty"`
	ReduceRequire        uint32                       `protobuf:"varint,4,opt,name=reduce_require,json=reduceRequire,proto3" json:"reduce_require,omitempty"`
	ReducePrice          uint32                       `protobuf:"varint,5,opt,name=reduce_price,json=reducePrice,proto3" json:"reduce_price,omitempty"`
	Discount             uint32                       `protobuf:"varint,6,opt,name=discount,proto3" json:"discount,omitempty"`
	UsageLimitText       string                       `protobuf:"bytes,7,opt,name=usage_limit_text,json=usageLimitText,proto3" json:"usage_limit_text,omitempty"`
	ConfId               uint32                       `protobuf:"varint,8,opt,name=conf_id,json=confId,proto3" json:"conf_id,omitempty"`
	GainSource           uint32                       `protobuf:"varint,9,opt,name=gain_source,json=gainSource,proto3" json:"gain_source,omitempty"`
	GainSourceInfo       *CouponGianSourceCommitOrder `protobuf:"bytes,10,opt,name=gain_source_info,json=gainSourceInfo,proto3" json:"gain_source_info,omitempty"`
	EffectTime           uint32                       `protobuf:"varint,11,opt,name=effect_time,json=effectTime,proto3" json:"effect_time,omitempty"`
	ExpireTime           uint32                       `protobuf:"varint,12,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	CreateTime           uint32                       `protobuf:"varint,13,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UsageLimitNoOrderDay uint32                       `protobuf:"varint,14,opt,name=usage_limit_no_order_day,json=usageLimitNoOrderDay,proto3" json:"usage_limit_no_order_day,omitempty"`
	UsageLimitType       uint32                       `protobuf:"varint,15,opt,name=usage_limit_type,json=usageLimitType,proto3" json:"usage_limit_type,omitempty"`
	MustInCoachPool      bool                         `protobuf:"varint,16,opt,name=must_in_coach_pool,json=mustInCoachPool,proto3" json:"must_in_coach_pool,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *Coupon) Reset()         { *m = Coupon{} }
func (m *Coupon) String() string { return proto.CompactTextString(m) }
func (*Coupon) ProtoMessage()    {}
func (*Coupon) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{74}
}
func (m *Coupon) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Coupon.Unmarshal(m, b)
}
func (m *Coupon) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Coupon.Marshal(b, m, deterministic)
}
func (dst *Coupon) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Coupon.Merge(dst, src)
}
func (m *Coupon) XXX_Size() int {
	return xxx_messageInfo_Coupon.Size(m)
}
func (m *Coupon) XXX_DiscardUnknown() {
	xxx_messageInfo_Coupon.DiscardUnknown(m)
}

var xxx_messageInfo_Coupon proto.InternalMessageInfo

func (m *Coupon) GetCouponId() string {
	if m != nil {
		return m.CouponId
	}
	return ""
}

func (m *Coupon) GetCoponName() string {
	if m != nil {
		return m.CoponName
	}
	return ""
}

func (m *Coupon) GetCouponType() uint32 {
	if m != nil {
		return m.CouponType
	}
	return 0
}

func (m *Coupon) GetReduceRequire() uint32 {
	if m != nil {
		return m.ReduceRequire
	}
	return 0
}

func (m *Coupon) GetReducePrice() uint32 {
	if m != nil {
		return m.ReducePrice
	}
	return 0
}

func (m *Coupon) GetDiscount() uint32 {
	if m != nil {
		return m.Discount
	}
	return 0
}

func (m *Coupon) GetUsageLimitText() string {
	if m != nil {
		return m.UsageLimitText
	}
	return ""
}

func (m *Coupon) GetConfId() uint32 {
	if m != nil {
		return m.ConfId
	}
	return 0
}

func (m *Coupon) GetGainSource() uint32 {
	if m != nil {
		return m.GainSource
	}
	return 0
}

func (m *Coupon) GetGainSourceInfo() *CouponGianSourceCommitOrder {
	if m != nil {
		return m.GainSourceInfo
	}
	return nil
}

func (m *Coupon) GetEffectTime() uint32 {
	if m != nil {
		return m.EffectTime
	}
	return 0
}

func (m *Coupon) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *Coupon) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *Coupon) GetUsageLimitNoOrderDay() uint32 {
	if m != nil {
		return m.UsageLimitNoOrderDay
	}
	return 0
}

func (m *Coupon) GetUsageLimitType() uint32 {
	if m != nil {
		return m.UsageLimitType
	}
	return 0
}

func (m *Coupon) GetMustInCoachPool() bool {
	if m != nil {
		return m.MustInCoachPool
	}
	return false
}

// 获取用户优惠券
type GetUserCouponRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserCouponRequest) Reset()         { *m = GetUserCouponRequest{} }
func (m *GetUserCouponRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserCouponRequest) ProtoMessage()    {}
func (*GetUserCouponRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{75}
}
func (m *GetUserCouponRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCouponRequest.Unmarshal(m, b)
}
func (m *GetUserCouponRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCouponRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserCouponRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCouponRequest.Merge(dst, src)
}
func (m *GetUserCouponRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserCouponRequest.Size(m)
}
func (m *GetUserCouponRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCouponRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCouponRequest proto.InternalMessageInfo

func (m *GetUserCouponRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserCouponResponse struct {
	CouponList           []*Coupon `protobuf:"bytes,1,rep,name=coupon_list,json=couponList,proto3" json:"coupon_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetUserCouponResponse) Reset()         { *m = GetUserCouponResponse{} }
func (m *GetUserCouponResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserCouponResponse) ProtoMessage()    {}
func (*GetUserCouponResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{76}
}
func (m *GetUserCouponResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCouponResponse.Unmarshal(m, b)
}
func (m *GetUserCouponResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCouponResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserCouponResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCouponResponse.Merge(dst, src)
}
func (m *GetUserCouponResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserCouponResponse.Size(m)
}
func (m *GetUserCouponResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCouponResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCouponResponse proto.InternalMessageInfo

func (m *GetUserCouponResponse) GetCouponList() []*Coupon {
	if m != nil {
		return m.CouponList
	}
	return nil
}

type GetUserCouponByPageRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Page                 uint32   `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	CouponStatus         uint32   `protobuf:"varint,4,opt,name=coupon_status,json=couponStatus,proto3" json:"coupon_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserCouponByPageRequest) Reset()         { *m = GetUserCouponByPageRequest{} }
func (m *GetUserCouponByPageRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserCouponByPageRequest) ProtoMessage()    {}
func (*GetUserCouponByPageRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{77}
}
func (m *GetUserCouponByPageRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCouponByPageRequest.Unmarshal(m, b)
}
func (m *GetUserCouponByPageRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCouponByPageRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserCouponByPageRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCouponByPageRequest.Merge(dst, src)
}
func (m *GetUserCouponByPageRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserCouponByPageRequest.Size(m)
}
func (m *GetUserCouponByPageRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCouponByPageRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCouponByPageRequest proto.InternalMessageInfo

func (m *GetUserCouponByPageRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserCouponByPageRequest) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetUserCouponByPageRequest) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetUserCouponByPageRequest) GetCouponStatus() uint32 {
	if m != nil {
		return m.CouponStatus
	}
	return 0
}

type GetUserCouponByPageResponse struct {
	CouponList           []*Coupon `protobuf:"bytes,1,rep,name=coupon_list,json=couponList,proto3" json:"coupon_list,omitempty"`
	Total                uint32    `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	HasMore              bool      `protobuf:"varint,3,opt,name=has_more,json=hasMore,proto3" json:"has_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetUserCouponByPageResponse) Reset()         { *m = GetUserCouponByPageResponse{} }
func (m *GetUserCouponByPageResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserCouponByPageResponse) ProtoMessage()    {}
func (*GetUserCouponByPageResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{78}
}
func (m *GetUserCouponByPageResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCouponByPageResponse.Unmarshal(m, b)
}
func (m *GetUserCouponByPageResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCouponByPageResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserCouponByPageResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCouponByPageResponse.Merge(dst, src)
}
func (m *GetUserCouponByPageResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserCouponByPageResponse.Size(m)
}
func (m *GetUserCouponByPageResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCouponByPageResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCouponByPageResponse proto.InternalMessageInfo

func (m *GetUserCouponByPageResponse) GetCouponList() []*Coupon {
	if m != nil {
		return m.CouponList
	}
	return nil
}

func (m *GetUserCouponByPageResponse) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetUserCouponByPageResponse) GetHasMore() bool {
	if m != nil {
		return m.HasMore
	}
	return false
}

type ContCouponUseTimesReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ContCouponUseTimesReq) Reset()         { *m = ContCouponUseTimesReq{} }
func (m *ContCouponUseTimesReq) String() string { return proto.CompactTextString(m) }
func (*ContCouponUseTimesReq) ProtoMessage()    {}
func (*ContCouponUseTimesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{79}
}
func (m *ContCouponUseTimesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ContCouponUseTimesReq.Unmarshal(m, b)
}
func (m *ContCouponUseTimesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ContCouponUseTimesReq.Marshal(b, m, deterministic)
}
func (dst *ContCouponUseTimesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ContCouponUseTimesReq.Merge(dst, src)
}
func (m *ContCouponUseTimesReq) XXX_Size() int {
	return xxx_messageInfo_ContCouponUseTimesReq.Size(m)
}
func (m *ContCouponUseTimesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ContCouponUseTimesReq.DiscardUnknown(m)
}

var xxx_messageInfo_ContCouponUseTimesReq proto.InternalMessageInfo

func (m *ContCouponUseTimesReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type ContCouponUseTimesResp struct {
	Count                uint32   `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ContCouponUseTimesResp) Reset()         { *m = ContCouponUseTimesResp{} }
func (m *ContCouponUseTimesResp) String() string { return proto.CompactTextString(m) }
func (*ContCouponUseTimesResp) ProtoMessage()    {}
func (*ContCouponUseTimesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{80}
}
func (m *ContCouponUseTimesResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ContCouponUseTimesResp.Unmarshal(m, b)
}
func (m *ContCouponUseTimesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ContCouponUseTimesResp.Marshal(b, m, deterministic)
}
func (dst *ContCouponUseTimesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ContCouponUseTimesResp.Merge(dst, src)
}
func (m *ContCouponUseTimesResp) XXX_Size() int {
	return xxx_messageInfo_ContCouponUseTimesResp.Size(m)
}
func (m *ContCouponUseTimesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ContCouponUseTimesResp.DiscardUnknown(m)
}

var xxx_messageInfo_ContCouponUseTimesResp proto.InternalMessageInfo

func (m *ContCouponUseTimesResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetCoachCouponUseTimesRequest struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCoachCouponUseTimesRequest) Reset()         { *m = GetCoachCouponUseTimesRequest{} }
func (m *GetCoachCouponUseTimesRequest) String() string { return proto.CompactTextString(m) }
func (*GetCoachCouponUseTimesRequest) ProtoMessage()    {}
func (*GetCoachCouponUseTimesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{81}
}
func (m *GetCoachCouponUseTimesRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachCouponUseTimesRequest.Unmarshal(m, b)
}
func (m *GetCoachCouponUseTimesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachCouponUseTimesRequest.Marshal(b, m, deterministic)
}
func (dst *GetCoachCouponUseTimesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachCouponUseTimesRequest.Merge(dst, src)
}
func (m *GetCoachCouponUseTimesRequest) XXX_Size() int {
	return xxx_messageInfo_GetCoachCouponUseTimesRequest.Size(m)
}
func (m *GetCoachCouponUseTimesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachCouponUseTimesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachCouponUseTimesRequest proto.InternalMessageInfo

func (m *GetCoachCouponUseTimesRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type GetCoachCouponUseTimesResponse struct {
	CountMap             map[uint32]uint32 `protobuf:"bytes,1,rep,name=count_map,json=countMap,proto3" json:"count_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetCoachCouponUseTimesResponse) Reset()         { *m = GetCoachCouponUseTimesResponse{} }
func (m *GetCoachCouponUseTimesResponse) String() string { return proto.CompactTextString(m) }
func (*GetCoachCouponUseTimesResponse) ProtoMessage()    {}
func (*GetCoachCouponUseTimesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{82}
}
func (m *GetCoachCouponUseTimesResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachCouponUseTimesResponse.Unmarshal(m, b)
}
func (m *GetCoachCouponUseTimesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachCouponUseTimesResponse.Marshal(b, m, deterministic)
}
func (dst *GetCoachCouponUseTimesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachCouponUseTimesResponse.Merge(dst, src)
}
func (m *GetCoachCouponUseTimesResponse) XXX_Size() int {
	return xxx_messageInfo_GetCoachCouponUseTimesResponse.Size(m)
}
func (m *GetCoachCouponUseTimesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachCouponUseTimesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachCouponUseTimesResponse proto.InternalMessageInfo

func (m *GetCoachCouponUseTimesResponse) GetCountMap() map[uint32]uint32 {
	if m != nil {
		return m.CountMap
	}
	return nil
}

type ClearCouponGainCacheRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearCouponGainCacheRequest) Reset()         { *m = ClearCouponGainCacheRequest{} }
func (m *ClearCouponGainCacheRequest) String() string { return proto.CompactTextString(m) }
func (*ClearCouponGainCacheRequest) ProtoMessage()    {}
func (*ClearCouponGainCacheRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{83}
}
func (m *ClearCouponGainCacheRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearCouponGainCacheRequest.Unmarshal(m, b)
}
func (m *ClearCouponGainCacheRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearCouponGainCacheRequest.Marshal(b, m, deterministic)
}
func (dst *ClearCouponGainCacheRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearCouponGainCacheRequest.Merge(dst, src)
}
func (m *ClearCouponGainCacheRequest) XXX_Size() int {
	return xxx_messageInfo_ClearCouponGainCacheRequest.Size(m)
}
func (m *ClearCouponGainCacheRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearCouponGainCacheRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ClearCouponGainCacheRequest proto.InternalMessageInfo

func (m *ClearCouponGainCacheRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type ClearCouponGainCacheResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearCouponGainCacheResponse) Reset()         { *m = ClearCouponGainCacheResponse{} }
func (m *ClearCouponGainCacheResponse) String() string { return proto.CompactTextString(m) }
func (*ClearCouponGainCacheResponse) ProtoMessage()    {}
func (*ClearCouponGainCacheResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{84}
}
func (m *ClearCouponGainCacheResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearCouponGainCacheResponse.Unmarshal(m, b)
}
func (m *ClearCouponGainCacheResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearCouponGainCacheResponse.Marshal(b, m, deterministic)
}
func (dst *ClearCouponGainCacheResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearCouponGainCacheResponse.Merge(dst, src)
}
func (m *ClearCouponGainCacheResponse) XXX_Size() int {
	return xxx_messageInfo_ClearCouponGainCacheResponse.Size(m)
}
func (m *ClearCouponGainCacheResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearCouponGainCacheResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ClearCouponGainCacheResponse proto.InternalMessageInfo

type GetCouponByIdRequest struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCouponByIdRequest) Reset()         { *m = GetCouponByIdRequest{} }
func (m *GetCouponByIdRequest) String() string { return proto.CompactTextString(m) }
func (*GetCouponByIdRequest) ProtoMessage()    {}
func (*GetCouponByIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{85}
}
func (m *GetCouponByIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCouponByIdRequest.Unmarshal(m, b)
}
func (m *GetCouponByIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCouponByIdRequest.Marshal(b, m, deterministic)
}
func (dst *GetCouponByIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCouponByIdRequest.Merge(dst, src)
}
func (m *GetCouponByIdRequest) XXX_Size() int {
	return xxx_messageInfo_GetCouponByIdRequest.Size(m)
}
func (m *GetCouponByIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCouponByIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCouponByIdRequest proto.InternalMessageInfo

func (m *GetCouponByIdRequest) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GetCouponByIdRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetCouponByIdResponse struct {
	Coupon               *Coupon  `protobuf:"bytes,1,opt,name=coupon,proto3" json:"coupon,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCouponByIdResponse) Reset()         { *m = GetCouponByIdResponse{} }
func (m *GetCouponByIdResponse) String() string { return proto.CompactTextString(m) }
func (*GetCouponByIdResponse) ProtoMessage()    {}
func (*GetCouponByIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{86}
}
func (m *GetCouponByIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCouponByIdResponse.Unmarshal(m, b)
}
func (m *GetCouponByIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCouponByIdResponse.Marshal(b, m, deterministic)
}
func (dst *GetCouponByIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCouponByIdResponse.Merge(dst, src)
}
func (m *GetCouponByIdResponse) XXX_Size() int {
	return xxx_messageInfo_GetCouponByIdResponse.Size(m)
}
func (m *GetCouponByIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCouponByIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCouponByIdResponse proto.InternalMessageInfo

func (m *GetCouponByIdResponse) GetCoupon() *Coupon {
	if m != nil {
		return m.Coupon
	}
	return nil
}

type GetCouponByIdsRequest struct {
	Id                   []string `protobuf:"bytes,1,rep,name=id,proto3" json:"id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCouponByIdsRequest) Reset()         { *m = GetCouponByIdsRequest{} }
func (m *GetCouponByIdsRequest) String() string { return proto.CompactTextString(m) }
func (*GetCouponByIdsRequest) ProtoMessage()    {}
func (*GetCouponByIdsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{87}
}
func (m *GetCouponByIdsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCouponByIdsRequest.Unmarshal(m, b)
}
func (m *GetCouponByIdsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCouponByIdsRequest.Marshal(b, m, deterministic)
}
func (dst *GetCouponByIdsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCouponByIdsRequest.Merge(dst, src)
}
func (m *GetCouponByIdsRequest) XXX_Size() int {
	return xxx_messageInfo_GetCouponByIdsRequest.Size(m)
}
func (m *GetCouponByIdsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCouponByIdsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCouponByIdsRequest proto.InternalMessageInfo

func (m *GetCouponByIdsRequest) GetId() []string {
	if m != nil {
		return m.Id
	}
	return nil
}

func (m *GetCouponByIdsRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetCouponByIdsResponse struct {
	Coupon               []*Coupon `protobuf:"bytes,1,rep,name=coupon,proto3" json:"coupon,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetCouponByIdsResponse) Reset()         { *m = GetCouponByIdsResponse{} }
func (m *GetCouponByIdsResponse) String() string { return proto.CompactTextString(m) }
func (*GetCouponByIdsResponse) ProtoMessage()    {}
func (*GetCouponByIdsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{88}
}
func (m *GetCouponByIdsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCouponByIdsResponse.Unmarshal(m, b)
}
func (m *GetCouponByIdsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCouponByIdsResponse.Marshal(b, m, deterministic)
}
func (dst *GetCouponByIdsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCouponByIdsResponse.Merge(dst, src)
}
func (m *GetCouponByIdsResponse) XXX_Size() int {
	return xxx_messageInfo_GetCouponByIdsResponse.Size(m)
}
func (m *GetCouponByIdsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCouponByIdsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCouponByIdsResponse proto.InternalMessageInfo

func (m *GetCouponByIdsResponse) GetCoupon() []*Coupon {
	if m != nil {
		return m.Coupon
	}
	return nil
}

type GetUnreadManualGrantCouponRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUnreadManualGrantCouponRequest) Reset()         { *m = GetUnreadManualGrantCouponRequest{} }
func (m *GetUnreadManualGrantCouponRequest) String() string { return proto.CompactTextString(m) }
func (*GetUnreadManualGrantCouponRequest) ProtoMessage()    {}
func (*GetUnreadManualGrantCouponRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{89}
}
func (m *GetUnreadManualGrantCouponRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUnreadManualGrantCouponRequest.Unmarshal(m, b)
}
func (m *GetUnreadManualGrantCouponRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUnreadManualGrantCouponRequest.Marshal(b, m, deterministic)
}
func (dst *GetUnreadManualGrantCouponRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUnreadManualGrantCouponRequest.Merge(dst, src)
}
func (m *GetUnreadManualGrantCouponRequest) XXX_Size() int {
	return xxx_messageInfo_GetUnreadManualGrantCouponRequest.Size(m)
}
func (m *GetUnreadManualGrantCouponRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUnreadManualGrantCouponRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUnreadManualGrantCouponRequest proto.InternalMessageInfo

func (m *GetUnreadManualGrantCouponRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUnreadManualGrantCouponResponse struct {
	CouponList           []*Coupon `protobuf:"bytes,1,rep,name=coupon_list,json=couponList,proto3" json:"coupon_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetUnreadManualGrantCouponResponse) Reset()         { *m = GetUnreadManualGrantCouponResponse{} }
func (m *GetUnreadManualGrantCouponResponse) String() string { return proto.CompactTextString(m) }
func (*GetUnreadManualGrantCouponResponse) ProtoMessage()    {}
func (*GetUnreadManualGrantCouponResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{90}
}
func (m *GetUnreadManualGrantCouponResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUnreadManualGrantCouponResponse.Unmarshal(m, b)
}
func (m *GetUnreadManualGrantCouponResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUnreadManualGrantCouponResponse.Marshal(b, m, deterministic)
}
func (dst *GetUnreadManualGrantCouponResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUnreadManualGrantCouponResponse.Merge(dst, src)
}
func (m *GetUnreadManualGrantCouponResponse) XXX_Size() int {
	return xxx_messageInfo_GetUnreadManualGrantCouponResponse.Size(m)
}
func (m *GetUnreadManualGrantCouponResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUnreadManualGrantCouponResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUnreadManualGrantCouponResponse proto.InternalMessageInfo

func (m *GetUnreadManualGrantCouponResponse) GetCouponList() []*Coupon {
	if m != nil {
		return m.CouponList
	}
	return nil
}

type MarkManualGrantCouponReadRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarkManualGrantCouponReadRequest) Reset()         { *m = MarkManualGrantCouponReadRequest{} }
func (m *MarkManualGrantCouponReadRequest) String() string { return proto.CompactTextString(m) }
func (*MarkManualGrantCouponReadRequest) ProtoMessage()    {}
func (*MarkManualGrantCouponReadRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{91}
}
func (m *MarkManualGrantCouponReadRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkManualGrantCouponReadRequest.Unmarshal(m, b)
}
func (m *MarkManualGrantCouponReadRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkManualGrantCouponReadRequest.Marshal(b, m, deterministic)
}
func (dst *MarkManualGrantCouponReadRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkManualGrantCouponReadRequest.Merge(dst, src)
}
func (m *MarkManualGrantCouponReadRequest) XXX_Size() int {
	return xxx_messageInfo_MarkManualGrantCouponReadRequest.Size(m)
}
func (m *MarkManualGrantCouponReadRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkManualGrantCouponReadRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MarkManualGrantCouponReadRequest proto.InternalMessageInfo

func (m *MarkManualGrantCouponReadRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type MarkManualGrantCouponReadResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarkManualGrantCouponReadResponse) Reset()         { *m = MarkManualGrantCouponReadResponse{} }
func (m *MarkManualGrantCouponReadResponse) String() string { return proto.CompactTextString(m) }
func (*MarkManualGrantCouponReadResponse) ProtoMessage()    {}
func (*MarkManualGrantCouponReadResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{92}
}
func (m *MarkManualGrantCouponReadResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkManualGrantCouponReadResponse.Unmarshal(m, b)
}
func (m *MarkManualGrantCouponReadResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkManualGrantCouponReadResponse.Marshal(b, m, deterministic)
}
func (dst *MarkManualGrantCouponReadResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkManualGrantCouponReadResponse.Merge(dst, src)
}
func (m *MarkManualGrantCouponReadResponse) XXX_Size() int {
	return xxx_messageInfo_MarkManualGrantCouponReadResponse.Size(m)
}
func (m *MarkManualGrantCouponReadResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkManualGrantCouponReadResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MarkManualGrantCouponReadResponse proto.InternalMessageInfo

// 获取人群包信息
type GetUserGroupInfoRequest struct {
	GroupId              []uint32 `protobuf:"varint,1,rep,packed,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserGroupInfoRequest) Reset()         { *m = GetUserGroupInfoRequest{} }
func (m *GetUserGroupInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserGroupInfoRequest) ProtoMessage()    {}
func (*GetUserGroupInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{93}
}
func (m *GetUserGroupInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserGroupInfoRequest.Unmarshal(m, b)
}
func (m *GetUserGroupInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserGroupInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserGroupInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserGroupInfoRequest.Merge(dst, src)
}
func (m *GetUserGroupInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserGroupInfoRequest.Size(m)
}
func (m *GetUserGroupInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserGroupInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserGroupInfoRequest proto.InternalMessageInfo

func (m *GetUserGroupInfoRequest) GetGroupId() []uint32 {
	if m != nil {
		return m.GroupId
	}
	return nil
}

type GetUserGroupInfoResponse struct {
	UserGroupInfo        []*UserGroupInfo `protobuf:"bytes,1,rep,name=user_group_info,json=userGroupInfo,proto3" json:"user_group_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetUserGroupInfoResponse) Reset()         { *m = GetUserGroupInfoResponse{} }
func (m *GetUserGroupInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserGroupInfoResponse) ProtoMessage()    {}
func (*GetUserGroupInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{94}
}
func (m *GetUserGroupInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserGroupInfoResponse.Unmarshal(m, b)
}
func (m *GetUserGroupInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserGroupInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserGroupInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserGroupInfoResponse.Merge(dst, src)
}
func (m *GetUserGroupInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserGroupInfoResponse.Size(m)
}
func (m *GetUserGroupInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserGroupInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserGroupInfoResponse proto.InternalMessageInfo

func (m *GetUserGroupInfoResponse) GetUserGroupInfo() []*UserGroupInfo {
	if m != nil {
		return m.UserGroupInfo
	}
	return nil
}

type UserGroupInfo struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	GroupName            string   `protobuf:"bytes,2,opt,name=group_name,json=groupName,proto3" json:"group_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserGroupInfo) Reset()         { *m = UserGroupInfo{} }
func (m *UserGroupInfo) String() string { return proto.CompactTextString(m) }
func (*UserGroupInfo) ProtoMessage()    {}
func (*UserGroupInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{95}
}
func (m *UserGroupInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserGroupInfo.Unmarshal(m, b)
}
func (m *UserGroupInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserGroupInfo.Marshal(b, m, deterministic)
}
func (dst *UserGroupInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserGroupInfo.Merge(dst, src)
}
func (m *UserGroupInfo) XXX_Size() int {
	return xxx_messageInfo_UserGroupInfo.Size(m)
}
func (m *UserGroupInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserGroupInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserGroupInfo proto.InternalMessageInfo

func (m *UserGroupInfo) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *UserGroupInfo) GetGroupName() string {
	if m != nil {
		return m.GroupName
	}
	return ""
}

type GetOrderHintRequest struct {
	Uid                  uint32                    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Scene                GetOrderHintRequest_Scene `protobuf:"varint,2,opt,name=scene,proto3,enum=esport_trade.GetOrderHintRequest_Scene" json:"scene,omitempty"`
	OrderType            uint32                    `protobuf:"varint,3,opt,name=order_type,json=orderType,proto3" json:"order_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetOrderHintRequest) Reset()         { *m = GetOrderHintRequest{} }
func (m *GetOrderHintRequest) String() string { return proto.CompactTextString(m) }
func (*GetOrderHintRequest) ProtoMessage()    {}
func (*GetOrderHintRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{96}
}
func (m *GetOrderHintRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderHintRequest.Unmarshal(m, b)
}
func (m *GetOrderHintRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderHintRequest.Marshal(b, m, deterministic)
}
func (dst *GetOrderHintRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderHintRequest.Merge(dst, src)
}
func (m *GetOrderHintRequest) XXX_Size() int {
	return xxx_messageInfo_GetOrderHintRequest.Size(m)
}
func (m *GetOrderHintRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderHintRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderHintRequest proto.InternalMessageInfo

func (m *GetOrderHintRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetOrderHintRequest) GetScene() GetOrderHintRequest_Scene {
	if m != nil {
		return m.Scene
	}
	return GetOrderHintRequest_SCENE_PAY_ORDER_COACH_GLOBAL
}

func (m *GetOrderHintRequest) GetOrderType() uint32 {
	if m != nil {
		return m.OrderType
	}
	return 0
}

type GetOrderHintResponse struct {
	HintText             string   `protobuf:"bytes,1,opt,name=hint_text,json=hintText,proto3" json:"hint_text,omitempty"`
	HintTitle            string   `protobuf:"bytes,2,opt,name=hint_title,json=hintTitle,proto3" json:"hint_title,omitempty"`
	HintContent          string   `protobuf:"bytes,3,opt,name=hint_content,json=hintContent,proto3" json:"hint_content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOrderHintResponse) Reset()         { *m = GetOrderHintResponse{} }
func (m *GetOrderHintResponse) String() string { return proto.CompactTextString(m) }
func (*GetOrderHintResponse) ProtoMessage()    {}
func (*GetOrderHintResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{97}
}
func (m *GetOrderHintResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderHintResponse.Unmarshal(m, b)
}
func (m *GetOrderHintResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderHintResponse.Marshal(b, m, deterministic)
}
func (dst *GetOrderHintResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderHintResponse.Merge(dst, src)
}
func (m *GetOrderHintResponse) XXX_Size() int {
	return xxx_messageInfo_GetOrderHintResponse.Size(m)
}
func (m *GetOrderHintResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderHintResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderHintResponse proto.InternalMessageInfo

func (m *GetOrderHintResponse) GetHintText() string {
	if m != nil {
		return m.HintText
	}
	return ""
}

func (m *GetOrderHintResponse) GetHintTitle() string {
	if m != nil {
		return m.HintTitle
	}
	return ""
}

func (m *GetOrderHintResponse) GetHintContent() string {
	if m != nil {
		return m.HintContent
	}
	return ""
}

type AutoGrantCouponRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	OrderId              string       `protobuf:"bytes,3,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Source               uint32       `protobuf:"varint,4,opt,name=source,proto3" json:"source,omitempty"`
	PreCheck             bool         `protobuf:"varint,5,opt,name=pre_check,json=preCheck,proto3" json:"pre_check,omitempty"`
	CoachUid             uint32       `protobuf:"varint,6,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	OrderTotalPrice      uint32       `protobuf:"varint,7,opt,name=order_total_price,json=orderTotalPrice,proto3" json:"order_total_price,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AutoGrantCouponRequest) Reset()         { *m = AutoGrantCouponRequest{} }
func (m *AutoGrantCouponRequest) String() string { return proto.CompactTextString(m) }
func (*AutoGrantCouponRequest) ProtoMessage()    {}
func (*AutoGrantCouponRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{98}
}
func (m *AutoGrantCouponRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AutoGrantCouponRequest.Unmarshal(m, b)
}
func (m *AutoGrantCouponRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AutoGrantCouponRequest.Marshal(b, m, deterministic)
}
func (dst *AutoGrantCouponRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AutoGrantCouponRequest.Merge(dst, src)
}
func (m *AutoGrantCouponRequest) XXX_Size() int {
	return xxx_messageInfo_AutoGrantCouponRequest.Size(m)
}
func (m *AutoGrantCouponRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AutoGrantCouponRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AutoGrantCouponRequest proto.InternalMessageInfo

func (m *AutoGrantCouponRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *AutoGrantCouponRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AutoGrantCouponRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *AutoGrantCouponRequest) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *AutoGrantCouponRequest) GetPreCheck() bool {
	if m != nil {
		return m.PreCheck
	}
	return false
}

func (m *AutoGrantCouponRequest) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

func (m *AutoGrantCouponRequest) GetOrderTotalPrice() uint32 {
	if m != nil {
		return m.OrderTotalPrice
	}
	return 0
}

type AutoGrantCouponResponse struct {
	CanGrant             bool      `protobuf:"varint,1,opt,name=can_grant,json=canGrant,proto3" json:"can_grant,omitempty"`
	CouponList           []*Coupon `protobuf:"bytes,2,rep,name=coupon_list,json=couponList,proto3" json:"coupon_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *AutoGrantCouponResponse) Reset()         { *m = AutoGrantCouponResponse{} }
func (m *AutoGrantCouponResponse) String() string { return proto.CompactTextString(m) }
func (*AutoGrantCouponResponse) ProtoMessage()    {}
func (*AutoGrantCouponResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_6d5257546a66bf5b, []int{99}
}
func (m *AutoGrantCouponResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AutoGrantCouponResponse.Unmarshal(m, b)
}
func (m *AutoGrantCouponResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AutoGrantCouponResponse.Marshal(b, m, deterministic)
}
func (dst *AutoGrantCouponResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AutoGrantCouponResponse.Merge(dst, src)
}
func (m *AutoGrantCouponResponse) XXX_Size() int {
	return xxx_messageInfo_AutoGrantCouponResponse.Size(m)
}
func (m *AutoGrantCouponResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AutoGrantCouponResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AutoGrantCouponResponse proto.InternalMessageInfo

func (m *AutoGrantCouponResponse) GetCanGrant() bool {
	if m != nil {
		return m.CanGrant
	}
	return false
}

func (m *AutoGrantCouponResponse) GetCouponList() []*Coupon {
	if m != nil {
		return m.CouponList
	}
	return nil
}

func init() {
	proto.RegisterType((*PriceInfo)(nil), "esport_trade.PriceInfo")
	proto.RegisterType((*ProductOrder)(nil), "esport_trade.ProductOrder")
	proto.RegisterType((*CouponUseDetail)(nil), "esport_trade.CouponUseDetail")
	proto.RegisterType((*NewCustomerUseDetail)(nil), "esport_trade.NewCustomerUseDetail")
	proto.RegisterType((*SkillProductOrderDetail)(nil), "esport_trade.SkillProductOrderDetail")
	proto.RegisterType((*PlayerPayOrderRequest)(nil), "esport_trade.PlayerPayOrderRequest")
	proto.RegisterType((*PlayerPayOrderResponse)(nil), "esport_trade.PlayerPayOrderResponse")
	proto.RegisterType((*PlayerCancelOrderRequest)(nil), "esport_trade.PlayerCancelOrderRequest")
	proto.RegisterType((*PlayerCancelOrderResponse)(nil), "esport_trade.PlayerCancelOrderResponse")
	proto.RegisterType((*PlayerFinishOrderRequest)(nil), "esport_trade.PlayerFinishOrderRequest")
	proto.RegisterType((*PlayerFinishOrderResponse)(nil), "esport_trade.PlayerFinishOrderResponse")
	proto.RegisterType((*CoachReceiveOrderRequest)(nil), "esport_trade.CoachReceiveOrderRequest")
	proto.RegisterType((*CoachReceiveOrderResponse)(nil), "esport_trade.CoachReceiveOrderResponse")
	proto.RegisterType((*CoachRefuseOrderRequest)(nil), "esport_trade.CoachRefuseOrderRequest")
	proto.RegisterType((*CoachRefuseOrderResponse)(nil), "esport_trade.CoachRefuseOrderResponse")
	proto.RegisterType((*CoachNotifyFinishOrderRequest)(nil), "esport_trade.CoachNotifyFinishOrderRequest")
	proto.RegisterType((*CoachNotifyFinishOrderResponse)(nil), "esport_trade.CoachNotifyFinishOrderResponse")
	proto.RegisterType((*GetOrderDetailRequest)(nil), "esport_trade.GetOrderDetailRequest")
	proto.RegisterType((*GetOrderDetailResponse)(nil), "esport_trade.GetOrderDetailResponse")
	proto.RegisterType((*OrderSimpleInfo)(nil), "esport_trade.OrderSimpleInfo")
	proto.RegisterType((*GetImOngoingOrderListRequest)(nil), "esport_trade.GetImOngoingOrderListRequest")
	proto.RegisterType((*GetImOngoingOrderListResponse)(nil), "esport_trade.GetImOngoingOrderListResponse")
	proto.RegisterType((*GetOrderListRequest)(nil), "esport_trade.GetOrderListRequest")
	proto.RegisterType((*GetOrderListResponse)(nil), "esport_trade.GetOrderListResponse")
	proto.RegisterType((*DelOrderRecordRequest)(nil), "esport_trade.DelOrderRecordRequest")
	proto.RegisterType((*DelOrderRecordResponse)(nil), "esport_trade.DelOrderRecordResponse")
	proto.RegisterType((*EnterRefundStatusRequest)(nil), "esport_trade.EnterRefundStatusRequest")
	proto.RegisterType((*EnterRefundStatusResponse)(nil), "esport_trade.EnterRefundStatusResponse")
	proto.RegisterType((*RefundRequest)(nil), "esport_trade.RefundRequest")
	proto.RegisterType((*RefundResponse)(nil), "esport_trade.RefundResponse")
	proto.RegisterType((*GetOrderConsumeRequest)(nil), "esport_trade.GetOrderConsumeRequest")
	proto.RegisterType((*GetOrderConsumeResponse)(nil), "esport_trade.GetOrderConsumeResponse")
	proto.RegisterType((*EvaluateScore)(nil), "esport_trade.EvaluateScore")
	proto.RegisterType((*EvaluateWordCnt)(nil), "esport_trade.EvaluateWordCnt")
	proto.RegisterType((*EvaluateSummary)(nil), "esport_trade.EvaluateSummary")
	proto.RegisterType((*EvaluateInfo)(nil), "esport_trade.EvaluateInfo")
	proto.RegisterType((*GetEvaluateListRequest)(nil), "esport_trade.GetEvaluateListRequest")
	proto.RegisterType((*GetEvaluateListResponse)(nil), "esport_trade.GetEvaluateListResponse")
	proto.RegisterType((*GetEvaluateSummaryRequest)(nil), "esport_trade.GetEvaluateSummaryRequest")
	proto.RegisterType((*GetEvaluateSummaryResponse)(nil), "esport_trade.GetEvaluateSummaryResponse")
	proto.RegisterType((*EvaluateScoreSummary)(nil), "esport_trade.EvaluateScoreSummary")
	proto.RegisterType((*BatGetEvaluateScoreSummaryRequest)(nil), "esport_trade.BatGetEvaluateScoreSummaryRequest")
	proto.RegisterType((*BatGetEvaluateScoreSummaryResponse)(nil), "esport_trade.BatGetEvaluateScoreSummaryResponse")
	proto.RegisterType((*EvaluateRequest)(nil), "esport_trade.EvaluateRequest")
	proto.RegisterType((*EvaluateResponse)(nil), "esport_trade.EvaluateResponse")
	proto.RegisterType((*AuditResultCallbackRequest)(nil), "esport_trade.AuditResultCallbackRequest")
	proto.RegisterType((*AuditResultCallbackResponse)(nil), "esport_trade.AuditResultCallbackResponse")
	proto.RegisterType((*CheckUserOrderCntLimitRequest)(nil), "esport_trade.CheckUserOrderCntLimitRequest")
	proto.RegisterType((*CheckUserOrderCntLimitResponse)(nil), "esport_trade.CheckUserOrderCntLimitResponse")
	proto.RegisterType((*GetUserTotalExpenditureAmountMonthlyRequest)(nil), "esport_trade.GetUserTotalExpenditureAmountMonthlyRequest")
	proto.RegisterType((*GetUserTotalExpenditureAmountMonthlyResponse)(nil), "esport_trade.GetUserTotalExpenditureAmountMonthlyResponse")
	proto.RegisterType((*SearchOrderRequest)(nil), "esport_trade.SearchOrderRequest")
	proto.RegisterType((*SearchOrderResponse)(nil), "esport_trade.SearchOrderResponse")
	proto.RegisterType((*GetCoachOrderStatRequest)(nil), "esport_trade.GetCoachOrderStatRequest")
	proto.RegisterType((*GetCoachOrderStatResponse)(nil), "esport_trade.GetCoachOrderStatResponse")
	proto.RegisterType((*CouponConfig)(nil), "esport_trade.CouponConfig")
	proto.RegisterType((*CouponConfigRecord)(nil), "esport_trade.CouponConfigRecord")
	proto.RegisterType((*CreateCouponConfigRequest)(nil), "esport_trade.CreateCouponConfigRequest")
	proto.RegisterType((*CreateCouponConfigResponse)(nil), "esport_trade.CreateCouponConfigResponse")
	proto.RegisterType((*GetCouponConfigListRequest)(nil), "esport_trade.GetCouponConfigListRequest")
	proto.RegisterType((*GetCouponConfigListResponse)(nil), "esport_trade.GetCouponConfigListResponse")
	proto.RegisterType((*GetCouponConfigByIdsRequest)(nil), "esport_trade.GetCouponConfigByIdsRequest")
	proto.RegisterType((*GetCouponConfigByIdsResponse)(nil), "esport_trade.GetCouponConfigByIdsResponse")
	proto.RegisterType((*AddManualGrantCouponTaskRequest)(nil), "esport_trade.AddManualGrantCouponTaskRequest")
	proto.RegisterType((*AddManualGrantCouponTaskResponse)(nil), "esport_trade.AddManualGrantCouponTaskResponse")
	proto.RegisterType((*StopManualGrantCouponTaskRequest)(nil), "esport_trade.StopManualGrantCouponTaskRequest")
	proto.RegisterType((*StopManualGrantCouponTaskResponse)(nil), "esport_trade.StopManualGrantCouponTaskResponse")
	proto.RegisterType((*RevokeManualGrantCouponTaskRequest)(nil), "esport_trade.RevokeManualGrantCouponTaskRequest")
	proto.RegisterType((*RevokeManualGrantCouponTaskResponse)(nil), "esport_trade.RevokeManualGrantCouponTaskResponse")
	proto.RegisterType((*ManualGrantCouponTask)(nil), "esport_trade.ManualGrantCouponTask")
	proto.RegisterType((*GetManualGrantCouponTasksRequest)(nil), "esport_trade.GetManualGrantCouponTasksRequest")
	proto.RegisterType((*GetManualGrantCouponTasksResponse)(nil), "esport_trade.GetManualGrantCouponTasksResponse")
	proto.RegisterType((*CouponGianSourceCommitOrder)(nil), "esport_trade.CouponGianSourceCommitOrder")
	proto.RegisterType((*CouponGianSourceManualGrant)(nil), "esport_trade.CouponGianSourceManualGrant")
	proto.RegisterType((*Coupon)(nil), "esport_trade.Coupon")
	proto.RegisterType((*GetUserCouponRequest)(nil), "esport_trade.GetUserCouponRequest")
	proto.RegisterType((*GetUserCouponResponse)(nil), "esport_trade.GetUserCouponResponse")
	proto.RegisterType((*GetUserCouponByPageRequest)(nil), "esport_trade.GetUserCouponByPageRequest")
	proto.RegisterType((*GetUserCouponByPageResponse)(nil), "esport_trade.GetUserCouponByPageResponse")
	proto.RegisterType((*ContCouponUseTimesReq)(nil), "esport_trade.ContCouponUseTimesReq")
	proto.RegisterType((*ContCouponUseTimesResp)(nil), "esport_trade.ContCouponUseTimesResp")
	proto.RegisterType((*GetCoachCouponUseTimesRequest)(nil), "esport_trade.GetCoachCouponUseTimesRequest")
	proto.RegisterType((*GetCoachCouponUseTimesResponse)(nil), "esport_trade.GetCoachCouponUseTimesResponse")
	proto.RegisterMapType((map[uint32]uint32)(nil), "esport_trade.GetCoachCouponUseTimesResponse.CountMapEntry")
	proto.RegisterType((*ClearCouponGainCacheRequest)(nil), "esport_trade.ClearCouponGainCacheRequest")
	proto.RegisterType((*ClearCouponGainCacheResponse)(nil), "esport_trade.ClearCouponGainCacheResponse")
	proto.RegisterType((*GetCouponByIdRequest)(nil), "esport_trade.GetCouponByIdRequest")
	proto.RegisterType((*GetCouponByIdResponse)(nil), "esport_trade.GetCouponByIdResponse")
	proto.RegisterType((*GetCouponByIdsRequest)(nil), "esport_trade.GetCouponByIdsRequest")
	proto.RegisterType((*GetCouponByIdsResponse)(nil), "esport_trade.GetCouponByIdsResponse")
	proto.RegisterType((*GetUnreadManualGrantCouponRequest)(nil), "esport_trade.GetUnreadManualGrantCouponRequest")
	proto.RegisterType((*GetUnreadManualGrantCouponResponse)(nil), "esport_trade.GetUnreadManualGrantCouponResponse")
	proto.RegisterType((*MarkManualGrantCouponReadRequest)(nil), "esport_trade.MarkManualGrantCouponReadRequest")
	proto.RegisterType((*MarkManualGrantCouponReadResponse)(nil), "esport_trade.MarkManualGrantCouponReadResponse")
	proto.RegisterType((*GetUserGroupInfoRequest)(nil), "esport_trade.GetUserGroupInfoRequest")
	proto.RegisterType((*GetUserGroupInfoResponse)(nil), "esport_trade.GetUserGroupInfoResponse")
	proto.RegisterType((*UserGroupInfo)(nil), "esport_trade.UserGroupInfo")
	proto.RegisterType((*GetOrderHintRequest)(nil), "esport_trade.GetOrderHintRequest")
	proto.RegisterType((*GetOrderHintResponse)(nil), "esport_trade.GetOrderHintResponse")
	proto.RegisterType((*AutoGrantCouponRequest)(nil), "esport_trade.AutoGrantCouponRequest")
	proto.RegisterType((*AutoGrantCouponResponse)(nil), "esport_trade.AutoGrantCouponResponse")
	proto.RegisterEnum("esport_trade.OrderStatus", OrderStatus_name, OrderStatus_value)
	proto.RegisterEnum("esport_trade.CanceledOrderSubStatus", CanceledOrderSubStatus_name, CanceledOrderSubStatus_value)
	proto.RegisterEnum("esport_trade.ReceivedOrderSubStatus", ReceivedOrderSubStatus_name, ReceivedOrderSubStatus_value)
	proto.RegisterEnum("esport_trade.PayType", PayType_name, PayType_value)
	proto.RegisterEnum("esport_trade.ManualGrantType", ManualGrantType_name, ManualGrantType_value)
	proto.RegisterEnum("esport_trade.CouponTaskStatus", CouponTaskStatus_name, CouponTaskStatus_value)
	proto.RegisterEnum("esport_trade.CouponGainSource", CouponGainSource_name, CouponGainSource_value)
	proto.RegisterEnum("esport_trade.CouponStatus", CouponStatus_name, CouponStatus_value)
	proto.RegisterEnum("esport_trade.GetOrderListRequest_OrderType", GetOrderListRequest_OrderType_name, GetOrderListRequest_OrderType_value)
	proto.RegisterEnum("esport_trade.GetOrderListRequest_StatusType", GetOrderListRequest_StatusType_name, GetOrderListRequest_StatusType_value)
	proto.RegisterEnum("esport_trade.CouponConfig_CouponType", CouponConfig_CouponType_name, CouponConfig_CouponType_value)
	proto.RegisterEnum("esport_trade.CouponConfig_UsageLimitType", CouponConfig_UsageLimitType_name, CouponConfig_UsageLimitType_value)
	proto.RegisterEnum("esport_trade.GetOrderHintRequest_Scene", GetOrderHintRequest_Scene_name, GetOrderHintRequest_Scene_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// EsportTradeClient is the client API for EsportTrade service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type EsportTradeClient interface {
	// 玩家下单
	PlayerPayOrder(ctx context.Context, in *PlayerPayOrderRequest, opts ...grpc.CallOption) (*PlayerPayOrderResponse, error)
	// 玩家取消订单
	PlayerCancelOrder(ctx context.Context, in *PlayerCancelOrderRequest, opts ...grpc.CallOption) (*PlayerCancelOrderResponse, error)
	// 玩家确认完成订单
	PlayerFinishOrder(ctx context.Context, in *PlayerFinishOrderRequest, opts ...grpc.CallOption) (*PlayerFinishOrderResponse, error)
	// 电竞指导接单
	CoachReceiveOrder(ctx context.Context, in *CoachReceiveOrderRequest, opts ...grpc.CallOption) (*CoachReceiveOrderResponse, error)
	// 电竞指导拒绝接单
	CoachRefuseOrder(ctx context.Context, in *CoachRefuseOrderRequest, opts ...grpc.CallOption) (*CoachRefuseOrderResponse, error)
	// 电竞指导提醒玩家去完成订单
	CoachNotifyFinishOrder(ctx context.Context, in *CoachNotifyFinishOrderRequest, opts ...grpc.CallOption) (*CoachNotifyFinishOrderResponse, error)
	// im页获取与对方正在进行中的订单
	GetImOngoingOrderList(ctx context.Context, in *GetImOngoingOrderListRequest, opts ...grpc.CallOption) (*GetImOngoingOrderListResponse, error)
	// 获取订单详情
	GetOrderDetail(ctx context.Context, in *GetOrderDetailRequest, opts ...grpc.CallOption) (*GetOrderDetailResponse, error)
	// 获取订单列表
	GetOrderList(ctx context.Context, in *GetOrderListRequest, opts ...grpc.CallOption) (*GetOrderListResponse, error)
	// 删除订单
	DelOrderRecord(ctx context.Context, in *DelOrderRecordRequest, opts ...grpc.CallOption) (*DelOrderRecordResponse, error)
	// 进入退款申诉状态
	EnterRefundStatus(ctx context.Context, in *EnterRefundStatusRequest, opts ...grpc.CallOption) (*EnterRefundStatusResponse, error)
	// 退款
	Refund(ctx context.Context, in *RefundRequest, opts ...grpc.CallOption) (*RefundResponse, error)
	// 评价
	Evaluate(ctx context.Context, in *EvaluateRequest, opts ...grpc.CallOption) (*EvaluateResponse, error)
	// 获取用户评价列表
	GetEvaluateList(ctx context.Context, in *GetEvaluateListRequest, opts ...grpc.CallOption) (*GetEvaluateListResponse, error)
	// 获取用户评价汇总信息
	GetEvaluateSummary(ctx context.Context, in *GetEvaluateSummaryRequest, opts ...grpc.CallOption) (*GetEvaluateSummaryResponse, error)
	// 批量获取用户评价分汇总信息
	BatGetEvaluateScoreSummary(ctx context.Context, in *BatGetEvaluateScoreSummaryRequest, opts ...grpc.CallOption) (*BatGetEvaluateScoreSummaryResponse, error)
	// 审核结果回调
	AuditResultCallback(ctx context.Context, in *AuditResultCallbackRequest, opts ...grpc.CallOption) (*AuditResultCallbackResponse, error)
	// 消费数据对账
	GetPayTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetPayOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// 获取订单消费信息
	GetOrderConsume(ctx context.Context, in *GetOrderConsumeRequest, opts ...grpc.CallOption) (*GetOrderConsumeResponse, error)
	// 防刷单校验
	CheckUserOrderCntLimit(ctx context.Context, in *CheckUserOrderCntLimitRequest, opts ...grpc.CallOption) (*CheckUserOrderCntLimitResponse, error)
	// 获取用户月度总消费金额
	GetUserTotalExpenditureAmountMonthly(ctx context.Context, in *GetUserTotalExpenditureAmountMonthlyRequest, opts ...grpc.CallOption) (*GetUserTotalExpenditureAmountMonthlyResponse, error)
	// 搜索订单
	SearchOrder(ctx context.Context, in *SearchOrderRequest, opts ...grpc.CallOption) (*SearchOrderResponse, error)
	// 获取订单统计信息
	GetCoachOrderStat(ctx context.Context, in *GetCoachOrderStatRequest, opts ...grpc.CallOption) (*GetCoachOrderStatResponse, error)
	// 获取订单提示信息
	GetOrderHint(ctx context.Context, in *GetOrderHintRequest, opts ...grpc.CallOption) (*GetOrderHintResponse, error)
	// 完成订单自动发放优惠券
	AutoGrantCoupon(ctx context.Context, in *AutoGrantCouponRequest, opts ...grpc.CallOption) (*AutoGrantCouponResponse, error)
	// 清除缓存（完成订单时，发放优惠券），debug用
	ClearCouponGainCache(ctx context.Context, in *ClearCouponGainCacheRequest, opts ...grpc.CallOption) (*ClearCouponGainCacheResponse, error)
	// 获取用户优惠券
	GetUserCoupon(ctx context.Context, in *GetUserCouponRequest, opts ...grpc.CallOption) (*GetUserCouponResponse, error)
	// 分页获取用户优惠券
	GetUserCouponByPage(ctx context.Context, in *GetUserCouponByPageRequest, opts ...grpc.CallOption) (*GetUserCouponByPageResponse, error)
	// 根据id获取优惠券
	GetCouponById(ctx context.Context, in *GetCouponByIdRequest, opts ...grpc.CallOption) (*GetCouponByIdResponse, error)
	// 根据id，批量获取优惠券
	GetCouponByIds(ctx context.Context, in *GetCouponByIdsRequest, opts ...grpc.CallOption) (*GetCouponByIdsResponse, error)
	// 统计用户优惠券使用次数，默认是今天
	ContCouponUseTimes(ctx context.Context, in *ContCouponUseTimesReq, opts ...grpc.CallOption) (*ContCouponUseTimesResp, error)
	// 获取大神今日优惠券使用次数
	GetCoachCouponUseTimes(ctx context.Context, in *GetCoachCouponUseTimesRequest, opts ...grpc.CallOption) (*GetCoachCouponUseTimesResponse, error)
	// 获取未读的手动发放优惠券
	GetUnreadManualGrantCoupon(ctx context.Context, in *GetUnreadManualGrantCouponRequest, opts ...grpc.CallOption) (*GetUnreadManualGrantCouponResponse, error)
	// 标记手动发放的优惠券已读
	MarkManualGrantCouponRead(ctx context.Context, in *MarkManualGrantCouponReadRequest, opts ...grpc.CallOption) (*MarkManualGrantCouponReadResponse, error)
	// =================== 优惠券管理后台 ==========================
	// 创建优惠券配置
	CreateCouponConfig(ctx context.Context, in *CreateCouponConfigRequest, opts ...grpc.CallOption) (*CreateCouponConfigResponse, error)
	// 获取优惠券配置列表
	GetCouponConfigList(ctx context.Context, in *GetCouponConfigListRequest, opts ...grpc.CallOption) (*GetCouponConfigListResponse, error)
	// 根据ID获取优惠券配置
	GetCouponConfigByIds(ctx context.Context, in *GetCouponConfigByIdsRequest, opts ...grpc.CallOption) (*GetCouponConfigByIdsResponse, error)
	// 添加手动发放优惠券任务
	AddManualGrantCouponTask(ctx context.Context, in *AddManualGrantCouponTaskRequest, opts ...grpc.CallOption) (*AddManualGrantCouponTaskResponse, error)
	// 终止手动发放优惠券任务
	StopManualGrantCouponTask(ctx context.Context, in *StopManualGrantCouponTaskRequest, opts ...grpc.CallOption) (*StopManualGrantCouponTaskResponse, error)
	// 撤回手动发放优惠券任务
	RevokeManualGrantCouponTask(ctx context.Context, in *RevokeManualGrantCouponTaskRequest, opts ...grpc.CallOption) (*RevokeManualGrantCouponTaskResponse, error)
	// 获取手动发放优惠券任务列表
	GetManualGrantCouponTasks(ctx context.Context, in *GetManualGrantCouponTasksRequest, opts ...grpc.CallOption) (*GetManualGrantCouponTasksResponse, error)
	// 获取人群包信息
	GetUserGroupInfo(ctx context.Context, in *GetUserGroupInfoRequest, opts ...grpc.CallOption) (*GetUserGroupInfoResponse, error)
}

type esportTradeClient struct {
	cc *grpc.ClientConn
}

func NewEsportTradeClient(cc *grpc.ClientConn) EsportTradeClient {
	return &esportTradeClient{cc}
}

func (c *esportTradeClient) PlayerPayOrder(ctx context.Context, in *PlayerPayOrderRequest, opts ...grpc.CallOption) (*PlayerPayOrderResponse, error) {
	out := new(PlayerPayOrderResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/PlayerPayOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) PlayerCancelOrder(ctx context.Context, in *PlayerCancelOrderRequest, opts ...grpc.CallOption) (*PlayerCancelOrderResponse, error) {
	out := new(PlayerCancelOrderResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/PlayerCancelOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) PlayerFinishOrder(ctx context.Context, in *PlayerFinishOrderRequest, opts ...grpc.CallOption) (*PlayerFinishOrderResponse, error) {
	out := new(PlayerFinishOrderResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/PlayerFinishOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) CoachReceiveOrder(ctx context.Context, in *CoachReceiveOrderRequest, opts ...grpc.CallOption) (*CoachReceiveOrderResponse, error) {
	out := new(CoachReceiveOrderResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/CoachReceiveOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) CoachRefuseOrder(ctx context.Context, in *CoachRefuseOrderRequest, opts ...grpc.CallOption) (*CoachRefuseOrderResponse, error) {
	out := new(CoachRefuseOrderResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/CoachRefuseOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) CoachNotifyFinishOrder(ctx context.Context, in *CoachNotifyFinishOrderRequest, opts ...grpc.CallOption) (*CoachNotifyFinishOrderResponse, error) {
	out := new(CoachNotifyFinishOrderResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/CoachNotifyFinishOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) GetImOngoingOrderList(ctx context.Context, in *GetImOngoingOrderListRequest, opts ...grpc.CallOption) (*GetImOngoingOrderListResponse, error) {
	out := new(GetImOngoingOrderListResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/GetImOngoingOrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) GetOrderDetail(ctx context.Context, in *GetOrderDetailRequest, opts ...grpc.CallOption) (*GetOrderDetailResponse, error) {
	out := new(GetOrderDetailResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/GetOrderDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) GetOrderList(ctx context.Context, in *GetOrderListRequest, opts ...grpc.CallOption) (*GetOrderListResponse, error) {
	out := new(GetOrderListResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/GetOrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) DelOrderRecord(ctx context.Context, in *DelOrderRecordRequest, opts ...grpc.CallOption) (*DelOrderRecordResponse, error) {
	out := new(DelOrderRecordResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/DelOrderRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) EnterRefundStatus(ctx context.Context, in *EnterRefundStatusRequest, opts ...grpc.CallOption) (*EnterRefundStatusResponse, error) {
	out := new(EnterRefundStatusResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/EnterRefundStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) Refund(ctx context.Context, in *RefundRequest, opts ...grpc.CallOption) (*RefundResponse, error) {
	out := new(RefundResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/Refund", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) Evaluate(ctx context.Context, in *EvaluateRequest, opts ...grpc.CallOption) (*EvaluateResponse, error) {
	out := new(EvaluateResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/Evaluate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) GetEvaluateList(ctx context.Context, in *GetEvaluateListRequest, opts ...grpc.CallOption) (*GetEvaluateListResponse, error) {
	out := new(GetEvaluateListResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/GetEvaluateList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) GetEvaluateSummary(ctx context.Context, in *GetEvaluateSummaryRequest, opts ...grpc.CallOption) (*GetEvaluateSummaryResponse, error) {
	out := new(GetEvaluateSummaryResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/GetEvaluateSummary", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) BatGetEvaluateScoreSummary(ctx context.Context, in *BatGetEvaluateScoreSummaryRequest, opts ...grpc.CallOption) (*BatGetEvaluateScoreSummaryResponse, error) {
	out := new(BatGetEvaluateScoreSummaryResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/BatGetEvaluateScoreSummary", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) AuditResultCallback(ctx context.Context, in *AuditResultCallbackRequest, opts ...grpc.CallOption) (*AuditResultCallbackResponse, error) {
	out := new(AuditResultCallbackResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/AuditResultCallback", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) GetPayTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/GetPayTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) GetPayOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/GetPayOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) GetOrderConsume(ctx context.Context, in *GetOrderConsumeRequest, opts ...grpc.CallOption) (*GetOrderConsumeResponse, error) {
	out := new(GetOrderConsumeResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/GetOrderConsume", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) CheckUserOrderCntLimit(ctx context.Context, in *CheckUserOrderCntLimitRequest, opts ...grpc.CallOption) (*CheckUserOrderCntLimitResponse, error) {
	out := new(CheckUserOrderCntLimitResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/CheckUserOrderCntLimit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) GetUserTotalExpenditureAmountMonthly(ctx context.Context, in *GetUserTotalExpenditureAmountMonthlyRequest, opts ...grpc.CallOption) (*GetUserTotalExpenditureAmountMonthlyResponse, error) {
	out := new(GetUserTotalExpenditureAmountMonthlyResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/GetUserTotalExpenditureAmountMonthly", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) SearchOrder(ctx context.Context, in *SearchOrderRequest, opts ...grpc.CallOption) (*SearchOrderResponse, error) {
	out := new(SearchOrderResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/SearchOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) GetCoachOrderStat(ctx context.Context, in *GetCoachOrderStatRequest, opts ...grpc.CallOption) (*GetCoachOrderStatResponse, error) {
	out := new(GetCoachOrderStatResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/GetCoachOrderStat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) GetOrderHint(ctx context.Context, in *GetOrderHintRequest, opts ...grpc.CallOption) (*GetOrderHintResponse, error) {
	out := new(GetOrderHintResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/GetOrderHint", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) AutoGrantCoupon(ctx context.Context, in *AutoGrantCouponRequest, opts ...grpc.CallOption) (*AutoGrantCouponResponse, error) {
	out := new(AutoGrantCouponResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/AutoGrantCoupon", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) ClearCouponGainCache(ctx context.Context, in *ClearCouponGainCacheRequest, opts ...grpc.CallOption) (*ClearCouponGainCacheResponse, error) {
	out := new(ClearCouponGainCacheResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/ClearCouponGainCache", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) GetUserCoupon(ctx context.Context, in *GetUserCouponRequest, opts ...grpc.CallOption) (*GetUserCouponResponse, error) {
	out := new(GetUserCouponResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/GetUserCoupon", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) GetUserCouponByPage(ctx context.Context, in *GetUserCouponByPageRequest, opts ...grpc.CallOption) (*GetUserCouponByPageResponse, error) {
	out := new(GetUserCouponByPageResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/GetUserCouponByPage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) GetCouponById(ctx context.Context, in *GetCouponByIdRequest, opts ...grpc.CallOption) (*GetCouponByIdResponse, error) {
	out := new(GetCouponByIdResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/GetCouponById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) GetCouponByIds(ctx context.Context, in *GetCouponByIdsRequest, opts ...grpc.CallOption) (*GetCouponByIdsResponse, error) {
	out := new(GetCouponByIdsResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/GetCouponByIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) ContCouponUseTimes(ctx context.Context, in *ContCouponUseTimesReq, opts ...grpc.CallOption) (*ContCouponUseTimesResp, error) {
	out := new(ContCouponUseTimesResp)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/ContCouponUseTimes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) GetCoachCouponUseTimes(ctx context.Context, in *GetCoachCouponUseTimesRequest, opts ...grpc.CallOption) (*GetCoachCouponUseTimesResponse, error) {
	out := new(GetCoachCouponUseTimesResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/GetCoachCouponUseTimes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) GetUnreadManualGrantCoupon(ctx context.Context, in *GetUnreadManualGrantCouponRequest, opts ...grpc.CallOption) (*GetUnreadManualGrantCouponResponse, error) {
	out := new(GetUnreadManualGrantCouponResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/GetUnreadManualGrantCoupon", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) MarkManualGrantCouponRead(ctx context.Context, in *MarkManualGrantCouponReadRequest, opts ...grpc.CallOption) (*MarkManualGrantCouponReadResponse, error) {
	out := new(MarkManualGrantCouponReadResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/MarkManualGrantCouponRead", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) CreateCouponConfig(ctx context.Context, in *CreateCouponConfigRequest, opts ...grpc.CallOption) (*CreateCouponConfigResponse, error) {
	out := new(CreateCouponConfigResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/CreateCouponConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) GetCouponConfigList(ctx context.Context, in *GetCouponConfigListRequest, opts ...grpc.CallOption) (*GetCouponConfigListResponse, error) {
	out := new(GetCouponConfigListResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/GetCouponConfigList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) GetCouponConfigByIds(ctx context.Context, in *GetCouponConfigByIdsRequest, opts ...grpc.CallOption) (*GetCouponConfigByIdsResponse, error) {
	out := new(GetCouponConfigByIdsResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/GetCouponConfigByIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) AddManualGrantCouponTask(ctx context.Context, in *AddManualGrantCouponTaskRequest, opts ...grpc.CallOption) (*AddManualGrantCouponTaskResponse, error) {
	out := new(AddManualGrantCouponTaskResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/AddManualGrantCouponTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) StopManualGrantCouponTask(ctx context.Context, in *StopManualGrantCouponTaskRequest, opts ...grpc.CallOption) (*StopManualGrantCouponTaskResponse, error) {
	out := new(StopManualGrantCouponTaskResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/StopManualGrantCouponTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) RevokeManualGrantCouponTask(ctx context.Context, in *RevokeManualGrantCouponTaskRequest, opts ...grpc.CallOption) (*RevokeManualGrantCouponTaskResponse, error) {
	out := new(RevokeManualGrantCouponTaskResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/RevokeManualGrantCouponTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) GetManualGrantCouponTasks(ctx context.Context, in *GetManualGrantCouponTasksRequest, opts ...grpc.CallOption) (*GetManualGrantCouponTasksResponse, error) {
	out := new(GetManualGrantCouponTasksResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/GetManualGrantCouponTasks", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportTradeClient) GetUserGroupInfo(ctx context.Context, in *GetUserGroupInfoRequest, opts ...grpc.CallOption) (*GetUserGroupInfoResponse, error) {
	out := new(GetUserGroupInfoResponse)
	err := c.cc.Invoke(ctx, "/esport_trade.EsportTrade/GetUserGroupInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EsportTradeServer is the server API for EsportTrade service.
type EsportTradeServer interface {
	// 玩家下单
	PlayerPayOrder(context.Context, *PlayerPayOrderRequest) (*PlayerPayOrderResponse, error)
	// 玩家取消订单
	PlayerCancelOrder(context.Context, *PlayerCancelOrderRequest) (*PlayerCancelOrderResponse, error)
	// 玩家确认完成订单
	PlayerFinishOrder(context.Context, *PlayerFinishOrderRequest) (*PlayerFinishOrderResponse, error)
	// 电竞指导接单
	CoachReceiveOrder(context.Context, *CoachReceiveOrderRequest) (*CoachReceiveOrderResponse, error)
	// 电竞指导拒绝接单
	CoachRefuseOrder(context.Context, *CoachRefuseOrderRequest) (*CoachRefuseOrderResponse, error)
	// 电竞指导提醒玩家去完成订单
	CoachNotifyFinishOrder(context.Context, *CoachNotifyFinishOrderRequest) (*CoachNotifyFinishOrderResponse, error)
	// im页获取与对方正在进行中的订单
	GetImOngoingOrderList(context.Context, *GetImOngoingOrderListRequest) (*GetImOngoingOrderListResponse, error)
	// 获取订单详情
	GetOrderDetail(context.Context, *GetOrderDetailRequest) (*GetOrderDetailResponse, error)
	// 获取订单列表
	GetOrderList(context.Context, *GetOrderListRequest) (*GetOrderListResponse, error)
	// 删除订单
	DelOrderRecord(context.Context, *DelOrderRecordRequest) (*DelOrderRecordResponse, error)
	// 进入退款申诉状态
	EnterRefundStatus(context.Context, *EnterRefundStatusRequest) (*EnterRefundStatusResponse, error)
	// 退款
	Refund(context.Context, *RefundRequest) (*RefundResponse, error)
	// 评价
	Evaluate(context.Context, *EvaluateRequest) (*EvaluateResponse, error)
	// 获取用户评价列表
	GetEvaluateList(context.Context, *GetEvaluateListRequest) (*GetEvaluateListResponse, error)
	// 获取用户评价汇总信息
	GetEvaluateSummary(context.Context, *GetEvaluateSummaryRequest) (*GetEvaluateSummaryResponse, error)
	// 批量获取用户评价分汇总信息
	BatGetEvaluateScoreSummary(context.Context, *BatGetEvaluateScoreSummaryRequest) (*BatGetEvaluateScoreSummaryResponse, error)
	// 审核结果回调
	AuditResultCallback(context.Context, *AuditResultCallbackRequest) (*AuditResultCallbackResponse, error)
	// 消费数据对账
	GetPayTotalCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetPayOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// 获取订单消费信息
	GetOrderConsume(context.Context, *GetOrderConsumeRequest) (*GetOrderConsumeResponse, error)
	// 防刷单校验
	CheckUserOrderCntLimit(context.Context, *CheckUserOrderCntLimitRequest) (*CheckUserOrderCntLimitResponse, error)
	// 获取用户月度总消费金额
	GetUserTotalExpenditureAmountMonthly(context.Context, *GetUserTotalExpenditureAmountMonthlyRequest) (*GetUserTotalExpenditureAmountMonthlyResponse, error)
	// 搜索订单
	SearchOrder(context.Context, *SearchOrderRequest) (*SearchOrderResponse, error)
	// 获取订单统计信息
	GetCoachOrderStat(context.Context, *GetCoachOrderStatRequest) (*GetCoachOrderStatResponse, error)
	// 获取订单提示信息
	GetOrderHint(context.Context, *GetOrderHintRequest) (*GetOrderHintResponse, error)
	// 完成订单自动发放优惠券
	AutoGrantCoupon(context.Context, *AutoGrantCouponRequest) (*AutoGrantCouponResponse, error)
	// 清除缓存（完成订单时，发放优惠券），debug用
	ClearCouponGainCache(context.Context, *ClearCouponGainCacheRequest) (*ClearCouponGainCacheResponse, error)
	// 获取用户优惠券
	GetUserCoupon(context.Context, *GetUserCouponRequest) (*GetUserCouponResponse, error)
	// 分页获取用户优惠券
	GetUserCouponByPage(context.Context, *GetUserCouponByPageRequest) (*GetUserCouponByPageResponse, error)
	// 根据id获取优惠券
	GetCouponById(context.Context, *GetCouponByIdRequest) (*GetCouponByIdResponse, error)
	// 根据id，批量获取优惠券
	GetCouponByIds(context.Context, *GetCouponByIdsRequest) (*GetCouponByIdsResponse, error)
	// 统计用户优惠券使用次数，默认是今天
	ContCouponUseTimes(context.Context, *ContCouponUseTimesReq) (*ContCouponUseTimesResp, error)
	// 获取大神今日优惠券使用次数
	GetCoachCouponUseTimes(context.Context, *GetCoachCouponUseTimesRequest) (*GetCoachCouponUseTimesResponse, error)
	// 获取未读的手动发放优惠券
	GetUnreadManualGrantCoupon(context.Context, *GetUnreadManualGrantCouponRequest) (*GetUnreadManualGrantCouponResponse, error)
	// 标记手动发放的优惠券已读
	MarkManualGrantCouponRead(context.Context, *MarkManualGrantCouponReadRequest) (*MarkManualGrantCouponReadResponse, error)
	// =================== 优惠券管理后台 ==========================
	// 创建优惠券配置
	CreateCouponConfig(context.Context, *CreateCouponConfigRequest) (*CreateCouponConfigResponse, error)
	// 获取优惠券配置列表
	GetCouponConfigList(context.Context, *GetCouponConfigListRequest) (*GetCouponConfigListResponse, error)
	// 根据ID获取优惠券配置
	GetCouponConfigByIds(context.Context, *GetCouponConfigByIdsRequest) (*GetCouponConfigByIdsResponse, error)
	// 添加手动发放优惠券任务
	AddManualGrantCouponTask(context.Context, *AddManualGrantCouponTaskRequest) (*AddManualGrantCouponTaskResponse, error)
	// 终止手动发放优惠券任务
	StopManualGrantCouponTask(context.Context, *StopManualGrantCouponTaskRequest) (*StopManualGrantCouponTaskResponse, error)
	// 撤回手动发放优惠券任务
	RevokeManualGrantCouponTask(context.Context, *RevokeManualGrantCouponTaskRequest) (*RevokeManualGrantCouponTaskResponse, error)
	// 获取手动发放优惠券任务列表
	GetManualGrantCouponTasks(context.Context, *GetManualGrantCouponTasksRequest) (*GetManualGrantCouponTasksResponse, error)
	// 获取人群包信息
	GetUserGroupInfo(context.Context, *GetUserGroupInfoRequest) (*GetUserGroupInfoResponse, error)
}

func RegisterEsportTradeServer(s *grpc.Server, srv EsportTradeServer) {
	s.RegisterService(&_EsportTrade_serviceDesc, srv)
}

func _EsportTrade_PlayerPayOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PlayerPayOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).PlayerPayOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/PlayerPayOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).PlayerPayOrder(ctx, req.(*PlayerPayOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_PlayerCancelOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PlayerCancelOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).PlayerCancelOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/PlayerCancelOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).PlayerCancelOrder(ctx, req.(*PlayerCancelOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_PlayerFinishOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PlayerFinishOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).PlayerFinishOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/PlayerFinishOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).PlayerFinishOrder(ctx, req.(*PlayerFinishOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_CoachReceiveOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CoachReceiveOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).CoachReceiveOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/CoachReceiveOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).CoachReceiveOrder(ctx, req.(*CoachReceiveOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_CoachRefuseOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CoachRefuseOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).CoachRefuseOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/CoachRefuseOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).CoachRefuseOrder(ctx, req.(*CoachRefuseOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_CoachNotifyFinishOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CoachNotifyFinishOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).CoachNotifyFinishOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/CoachNotifyFinishOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).CoachNotifyFinishOrder(ctx, req.(*CoachNotifyFinishOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_GetImOngoingOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetImOngoingOrderListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).GetImOngoingOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/GetImOngoingOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).GetImOngoingOrderList(ctx, req.(*GetImOngoingOrderListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_GetOrderDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).GetOrderDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/GetOrderDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).GetOrderDetail(ctx, req.(*GetOrderDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_GetOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).GetOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/GetOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).GetOrderList(ctx, req.(*GetOrderListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_DelOrderRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelOrderRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).DelOrderRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/DelOrderRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).DelOrderRecord(ctx, req.(*DelOrderRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_EnterRefundStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EnterRefundStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).EnterRefundStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/EnterRefundStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).EnterRefundStatus(ctx, req.(*EnterRefundStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_Refund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).Refund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/Refund",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).Refund(ctx, req.(*RefundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_Evaluate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EvaluateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).Evaluate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/Evaluate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).Evaluate(ctx, req.(*EvaluateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_GetEvaluateList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEvaluateListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).GetEvaluateList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/GetEvaluateList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).GetEvaluateList(ctx, req.(*GetEvaluateListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_GetEvaluateSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEvaluateSummaryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).GetEvaluateSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/GetEvaluateSummary",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).GetEvaluateSummary(ctx, req.(*GetEvaluateSummaryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_BatGetEvaluateScoreSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetEvaluateScoreSummaryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).BatGetEvaluateScoreSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/BatGetEvaluateScoreSummary",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).BatGetEvaluateScoreSummary(ctx, req.(*BatGetEvaluateScoreSummaryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_AuditResultCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuditResultCallbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).AuditResultCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/AuditResultCallback",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).AuditResultCallback(ctx, req.(*AuditResultCallbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_GetPayTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).GetPayTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/GetPayTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).GetPayTotalCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_GetPayOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).GetPayOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/GetPayOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).GetPayOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_GetOrderConsume_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderConsumeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).GetOrderConsume(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/GetOrderConsume",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).GetOrderConsume(ctx, req.(*GetOrderConsumeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_CheckUserOrderCntLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserOrderCntLimitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).CheckUserOrderCntLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/CheckUserOrderCntLimit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).CheckUserOrderCntLimit(ctx, req.(*CheckUserOrderCntLimitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_GetUserTotalExpenditureAmountMonthly_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserTotalExpenditureAmountMonthlyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).GetUserTotalExpenditureAmountMonthly(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/GetUserTotalExpenditureAmountMonthly",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).GetUserTotalExpenditureAmountMonthly(ctx, req.(*GetUserTotalExpenditureAmountMonthlyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_SearchOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).SearchOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/SearchOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).SearchOrder(ctx, req.(*SearchOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_GetCoachOrderStat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCoachOrderStatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).GetCoachOrderStat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/GetCoachOrderStat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).GetCoachOrderStat(ctx, req.(*GetCoachOrderStatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_GetOrderHint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderHintRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).GetOrderHint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/GetOrderHint",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).GetOrderHint(ctx, req.(*GetOrderHintRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_AutoGrantCoupon_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AutoGrantCouponRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).AutoGrantCoupon(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/AutoGrantCoupon",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).AutoGrantCoupon(ctx, req.(*AutoGrantCouponRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_ClearCouponGainCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearCouponGainCacheRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).ClearCouponGainCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/ClearCouponGainCache",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).ClearCouponGainCache(ctx, req.(*ClearCouponGainCacheRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_GetUserCoupon_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserCouponRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).GetUserCoupon(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/GetUserCoupon",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).GetUserCoupon(ctx, req.(*GetUserCouponRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_GetUserCouponByPage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserCouponByPageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).GetUserCouponByPage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/GetUserCouponByPage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).GetUserCouponByPage(ctx, req.(*GetUserCouponByPageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_GetCouponById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCouponByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).GetCouponById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/GetCouponById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).GetCouponById(ctx, req.(*GetCouponByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_GetCouponByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCouponByIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).GetCouponByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/GetCouponByIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).GetCouponByIds(ctx, req.(*GetCouponByIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_ContCouponUseTimes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ContCouponUseTimesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).ContCouponUseTimes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/ContCouponUseTimes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).ContCouponUseTimes(ctx, req.(*ContCouponUseTimesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_GetCoachCouponUseTimes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCoachCouponUseTimesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).GetCoachCouponUseTimes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/GetCoachCouponUseTimes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).GetCoachCouponUseTimes(ctx, req.(*GetCoachCouponUseTimesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_GetUnreadManualGrantCoupon_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUnreadManualGrantCouponRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).GetUnreadManualGrantCoupon(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/GetUnreadManualGrantCoupon",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).GetUnreadManualGrantCoupon(ctx, req.(*GetUnreadManualGrantCouponRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_MarkManualGrantCouponRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkManualGrantCouponReadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).MarkManualGrantCouponRead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/MarkManualGrantCouponRead",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).MarkManualGrantCouponRead(ctx, req.(*MarkManualGrantCouponReadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_CreateCouponConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCouponConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).CreateCouponConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/CreateCouponConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).CreateCouponConfig(ctx, req.(*CreateCouponConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_GetCouponConfigList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCouponConfigListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).GetCouponConfigList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/GetCouponConfigList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).GetCouponConfigList(ctx, req.(*GetCouponConfigListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_GetCouponConfigByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCouponConfigByIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).GetCouponConfigByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/GetCouponConfigByIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).GetCouponConfigByIds(ctx, req.(*GetCouponConfigByIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_AddManualGrantCouponTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddManualGrantCouponTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).AddManualGrantCouponTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/AddManualGrantCouponTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).AddManualGrantCouponTask(ctx, req.(*AddManualGrantCouponTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_StopManualGrantCouponTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopManualGrantCouponTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).StopManualGrantCouponTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/StopManualGrantCouponTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).StopManualGrantCouponTask(ctx, req.(*StopManualGrantCouponTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_RevokeManualGrantCouponTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RevokeManualGrantCouponTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).RevokeManualGrantCouponTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/RevokeManualGrantCouponTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).RevokeManualGrantCouponTask(ctx, req.(*RevokeManualGrantCouponTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_GetManualGrantCouponTasks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetManualGrantCouponTasksRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).GetManualGrantCouponTasks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/GetManualGrantCouponTasks",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).GetManualGrantCouponTasks(ctx, req.(*GetManualGrantCouponTasksRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportTrade_GetUserGroupInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserGroupInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportTradeServer).GetUserGroupInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade.EsportTrade/GetUserGroupInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportTradeServer).GetUserGroupInfo(ctx, req.(*GetUserGroupInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _EsportTrade_serviceDesc = grpc.ServiceDesc{
	ServiceName: "esport_trade.EsportTrade",
	HandlerType: (*EsportTradeServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PlayerPayOrder",
			Handler:    _EsportTrade_PlayerPayOrder_Handler,
		},
		{
			MethodName: "PlayerCancelOrder",
			Handler:    _EsportTrade_PlayerCancelOrder_Handler,
		},
		{
			MethodName: "PlayerFinishOrder",
			Handler:    _EsportTrade_PlayerFinishOrder_Handler,
		},
		{
			MethodName: "CoachReceiveOrder",
			Handler:    _EsportTrade_CoachReceiveOrder_Handler,
		},
		{
			MethodName: "CoachRefuseOrder",
			Handler:    _EsportTrade_CoachRefuseOrder_Handler,
		},
		{
			MethodName: "CoachNotifyFinishOrder",
			Handler:    _EsportTrade_CoachNotifyFinishOrder_Handler,
		},
		{
			MethodName: "GetImOngoingOrderList",
			Handler:    _EsportTrade_GetImOngoingOrderList_Handler,
		},
		{
			MethodName: "GetOrderDetail",
			Handler:    _EsportTrade_GetOrderDetail_Handler,
		},
		{
			MethodName: "GetOrderList",
			Handler:    _EsportTrade_GetOrderList_Handler,
		},
		{
			MethodName: "DelOrderRecord",
			Handler:    _EsportTrade_DelOrderRecord_Handler,
		},
		{
			MethodName: "EnterRefundStatus",
			Handler:    _EsportTrade_EnterRefundStatus_Handler,
		},
		{
			MethodName: "Refund",
			Handler:    _EsportTrade_Refund_Handler,
		},
		{
			MethodName: "Evaluate",
			Handler:    _EsportTrade_Evaluate_Handler,
		},
		{
			MethodName: "GetEvaluateList",
			Handler:    _EsportTrade_GetEvaluateList_Handler,
		},
		{
			MethodName: "GetEvaluateSummary",
			Handler:    _EsportTrade_GetEvaluateSummary_Handler,
		},
		{
			MethodName: "BatGetEvaluateScoreSummary",
			Handler:    _EsportTrade_BatGetEvaluateScoreSummary_Handler,
		},
		{
			MethodName: "AuditResultCallback",
			Handler:    _EsportTrade_AuditResultCallback_Handler,
		},
		{
			MethodName: "GetPayTotalCount",
			Handler:    _EsportTrade_GetPayTotalCount_Handler,
		},
		{
			MethodName: "GetPayOrderIds",
			Handler:    _EsportTrade_GetPayOrderIds_Handler,
		},
		{
			MethodName: "GetOrderConsume",
			Handler:    _EsportTrade_GetOrderConsume_Handler,
		},
		{
			MethodName: "CheckUserOrderCntLimit",
			Handler:    _EsportTrade_CheckUserOrderCntLimit_Handler,
		},
		{
			MethodName: "GetUserTotalExpenditureAmountMonthly",
			Handler:    _EsportTrade_GetUserTotalExpenditureAmountMonthly_Handler,
		},
		{
			MethodName: "SearchOrder",
			Handler:    _EsportTrade_SearchOrder_Handler,
		},
		{
			MethodName: "GetCoachOrderStat",
			Handler:    _EsportTrade_GetCoachOrderStat_Handler,
		},
		{
			MethodName: "GetOrderHint",
			Handler:    _EsportTrade_GetOrderHint_Handler,
		},
		{
			MethodName: "AutoGrantCoupon",
			Handler:    _EsportTrade_AutoGrantCoupon_Handler,
		},
		{
			MethodName: "ClearCouponGainCache",
			Handler:    _EsportTrade_ClearCouponGainCache_Handler,
		},
		{
			MethodName: "GetUserCoupon",
			Handler:    _EsportTrade_GetUserCoupon_Handler,
		},
		{
			MethodName: "GetUserCouponByPage",
			Handler:    _EsportTrade_GetUserCouponByPage_Handler,
		},
		{
			MethodName: "GetCouponById",
			Handler:    _EsportTrade_GetCouponById_Handler,
		},
		{
			MethodName: "GetCouponByIds",
			Handler:    _EsportTrade_GetCouponByIds_Handler,
		},
		{
			MethodName: "ContCouponUseTimes",
			Handler:    _EsportTrade_ContCouponUseTimes_Handler,
		},
		{
			MethodName: "GetCoachCouponUseTimes",
			Handler:    _EsportTrade_GetCoachCouponUseTimes_Handler,
		},
		{
			MethodName: "GetUnreadManualGrantCoupon",
			Handler:    _EsportTrade_GetUnreadManualGrantCoupon_Handler,
		},
		{
			MethodName: "MarkManualGrantCouponRead",
			Handler:    _EsportTrade_MarkManualGrantCouponRead_Handler,
		},
		{
			MethodName: "CreateCouponConfig",
			Handler:    _EsportTrade_CreateCouponConfig_Handler,
		},
		{
			MethodName: "GetCouponConfigList",
			Handler:    _EsportTrade_GetCouponConfigList_Handler,
		},
		{
			MethodName: "GetCouponConfigByIds",
			Handler:    _EsportTrade_GetCouponConfigByIds_Handler,
		},
		{
			MethodName: "AddManualGrantCouponTask",
			Handler:    _EsportTrade_AddManualGrantCouponTask_Handler,
		},
		{
			MethodName: "StopManualGrantCouponTask",
			Handler:    _EsportTrade_StopManualGrantCouponTask_Handler,
		},
		{
			MethodName: "RevokeManualGrantCouponTask",
			Handler:    _EsportTrade_RevokeManualGrantCouponTask_Handler,
		},
		{
			MethodName: "GetManualGrantCouponTasks",
			Handler:    _EsportTrade_GetManualGrantCouponTasks_Handler,
		},
		{
			MethodName: "GetUserGroupInfo",
			Handler:    _EsportTrade_GetUserGroupInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/esport-trade/esport-trade.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/esport-trade/esport-trade.proto", fileDescriptor_esport_trade_6d5257546a66bf5b)
}

var fileDescriptor_esport_trade_6d5257546a66bf5b = []byte{
	// 5383 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x7c, 0xdd, 0x6f, 0x1b, 0x49,
	0x72, 0xf8, 0x92, 0xd4, 0x07, 0x59, 0x14, 0x25, 0x7a, 0x2c, 0x4b, 0x14, 0x65, 0xaf, 0xe5, 0xf1,
	0xda, 0xab, 0x95, 0xf7, 0xe4, 0xfd, 0xf9, 0x6e, 0x6f, 0x3f, 0xee, 0x77, 0x48, 0x68, 0x8a, 0x92,
	0x07, 0x2b, 0x51, 0xca, 0x90, 0x5c, 0x9f, 0xb3, 0xb8, 0x9d, 0x8c, 0xc9, 0x16, 0x35, 0x11, 0x39,
	0xc3, 0x9d, 0x0f, 0xd9, 0xba, 0x3b, 0xe0, 0x80, 0x20, 0x79, 0xcd, 0x4b, 0x80, 0xe4, 0x29, 0x40,
	0x72, 0x40, 0x92, 0xa7, 0xbc, 0x26, 0x79, 0xc8, 0x5b, 0x92, 0x87, 0x00, 0x41, 0x80, 0x00, 0x87,
	0x24, 0xff, 0x43, 0x5e, 0x83, 0x3c, 0x04, 0x08, 0x10, 0x74, 0x75, 0xcf, 0x4c, 0xcf, 0xb0, 0x87,
	0xd2, 0x7a, 0x17, 0x48, 0x90, 0x07, 0xc3, 0xec, 0xea, 0xea, 0xea, 0xea, 0xee, 0xaa, 0xea, 0xaa,
	0xea, 0x1a, 0xc1, 0xae, 0xef, 0x3f, 0xfe, 0x2a, 0xb0, 0xfa, 0xe7, 0x9e, 0x35, 0xba, 0x20, 0xee,
	0x63, 0xe2, 0x4d, 0x1c, 0xd7, 0xff, 0x8e, 0xef, 0x9a, 0x03, 0x92, 0x68, 0xec, 0x4e, 0x5c, 0xc7,
	0x77, 0x94, 0x25, 0x06, 0x33, 0x10, 0x56, 0xaf, 0x0c, 0x4d, 0xe3, 0xa5, 0xe9, 0xf1, 0xce, 0xfa,
	0x14, 0xb1, 0xd7, 0x3e, 0xb1, 0x3d, 0xcb, 0xb1, 0x1f, 0x3b, 0x13, 0xdf, 0x72, 0x6c, 0x2f, 0xfc,
	0x3f, 0x03, 0xdf, 0x25, 0x7d, 0xc7, 0xee, 0x5b, 0x23, 0xf2, 0x9d, 0x8b, 0x27, 0x89, 0x06, 0xc3,
	0x57, 0xff, 0x2b, 0x0f, 0xa5, 0x13, 0xd7, 0xea, 0x13, 0xcd, 0x3e, 0x75, 0x94, 0x55, 0x98, 0x9f,
	0xd0, 0x46, 0x2d, 0xb7, 0x95, 0xdb, 0xae, 0xe8, 0xac, 0xa1, 0xdc, 0x01, 0xc0, 0x1f, 0x46, 0x60,
	0x5b, 0x7e, 0x2d, 0xbf, 0x95, 0xdb, 0x2e, 0xe9, 0x25, 0x84, 0xf4, 0x6c, 0xcb, 0x57, 0xee, 0x42,
	0x79, 0x4c, 0x4c, 0x2f, 0x70, 0x89, 0xd1, 0xb7, 0xfd, 0x5a, 0x01, 0x87, 0x02, 0x07, 0x35, 0x6d,
	0x5f, 0xb9, 0x07, 0x4b, 0x21, 0x02, 0x52, 0x98, 0x43, 0x0a, 0xe1, 0x20, 0xa4, 0xf1, 0x11, 0xd4,
	0xce, 0x4c, 0xcf, 0x38, 0xb5, 0x5c, 0xcf, 0x37, 0x5c, 0x27, 0xb0, 0x07, 0xc6, 0xc0, 0xf2, 0xfa,
	0x4e, 0x60, 0xfb, 0xb5, 0x85, 0xad, 0xdc, 0x76, 0x51, 0xbf, 0x75, 0x66, 0x7a, 0xfb, 0xb4, 0x5b,
	0xa7, 0xbd, 0x7b, 0xbc, 0x53, 0xd9, 0x81, 0x1b, 0xe2, 0x20, 0xc6, 0xfd, 0x22, 0xb2, 0xb0, 0x72,
	0x1a, 0xa1, 0xe3, 0x0a, 0x29, 0x1f, 0x74, 0x92, 0x88, 0x70, 0x11, 0x09, 0x97, 0xcf, 0x4c, 0x2f,
	0x22, 0xf7, 0x00, 0x96, 0xc3, 0x6e, 0x4e, 0xab, 0x84, 0xb4, 0x2a, 0x21, 0x94, 0x51, 0xba, 0x0f,
	0x11, 0xc0, 0xf0, 0x2f, 0x27, 0xa4, 0x06, 0x88, 0xb5, 0x14, 0x02, 0xbb, 0x97, 0x93, 0x24, 0xd2,
	0x80, 0x78, 0xfd, 0x5a, 0x19, 0xd7, 0x1d, 0x21, 0xed, 0x11, 0xaf, 0xaf, 0xfe, 0xd5, 0x1c, 0x2c,
	0x9d, 0xb8, 0xce, 0x20, 0xe8, 0xfb, 0xc7, 0xee, 0x80, 0xb8, 0xca, 0x26, 0x94, 0xfa, 0x8e, 0xd9,
	0x3f, 0x33, 0x02, 0x6b, 0xc0, 0x8f, 0xa1, 0x88, 0x80, 0x9e, 0x35, 0x60, 0x27, 0x81, 0xc8, 0x86,
	0x35, 0xc0, 0x93, 0x98, 0xa3, 0x27, 0x81, 0x10, 0x6d, 0xa0, 0x28, 0x30, 0x67, 0x9b, 0x63, 0x82,
	0x47, 0x50, 0xd2, 0xf1, 0x37, 0x85, 0x59, 0x7d, 0xc7, 0xe6, 0x9b, 0x8e, 0xbf, 0x95, 0xef, 0x87,
	0x07, 0x6a, 0xd9, 0xa7, 0x4e, 0x6d, 0x7e, 0x2b, 0xb7, 0x5d, 0x7e, 0xb2, 0xbe, 0x2b, 0x8a, 0xe1,
	0x6e, 0x24, 0x13, 0xfc, 0xa4, 0x43, 0xf1, 0x88, 0x8f, 0xa4, 0xa2, 0xb3, 0x06, 0x3d, 0x7f, 0xdf,
	0xf1, 0xcd, 0x51, 0x62, 0xf3, 0x01, 0x41, 0x6c, 0xb7, 0xd6, 0x61, 0x71, 0x68, 0x8e, 0x09, 0x65,
	0xb9, 0x88, 0x9d, 0x0b, 0xb4, 0xa9, 0x0d, 0x94, 0x2a, 0x14, 0x7c, 0x73, 0x88, 0x5b, 0x5c, 0xd2,
	0xe9, 0x4f, 0x65, 0x17, 0x6e, 0x0e, 0x03, 0xd3, 0x35, 0x6d, 0x9f, 0x10, 0xe3, 0x95, 0x65, 0x1b,
	0x3e, 0x79, 0xed, 0x7b, 0x35, 0xd8, 0x2a, 0x6c, 0x97, 0xf4, 0x1b, 0x51, 0xd7, 0x73, 0xcb, 0xee,
	0xd2, 0x0e, 0x65, 0x1b, 0xaa, 0x96, 0x67, 0x24, 0x86, 0xe0, 0x36, 0x17, 0xf5, 0x65, 0xcb, 0x3b,
	0x10, 0xd0, 0xa9, 0xa0, 0xb0, 0x7d, 0x15, 0x79, 0x5d, 0x62, 0x82, 0x82, 0x1d, 0xdd, 0x98, 0x61,
	0x8d, 0xe2, 0x06, 0x13, 0xc7, 0x36, 0x02, 0x8f, 0x18, 0x03, 0xe2, 0x9b, 0xd6, 0xa8, 0x56, 0xc1,
	0x6d, 0xba, 0x93, 0xdc, 0xa6, 0x26, 0xa2, 0xf5, 0x3c, 0xb2, 0x87, 0x48, 0x94, 0x54, 0x02, 0xa0,
	0xbc, 0x80, 0x75, 0x9b, 0xbc, 0x32, 0xfa, 0x81, 0xe7, 0x3b, 0x63, 0xe2, 0x8a, 0x04, 0x97, 0x91,
	0xa0, 0x9a, 0x24, 0xd8, 0x26, 0xaf, 0x9a, 0x1c, 0x37, 0xa6, 0xba, 0x6a, 0x4b, 0xa0, 0xaa, 0x0b,
	0x2b, 0xa9, 0xe9, 0xa9, 0x7c, 0xd0, 0x09, 0x18, 0x13, 0x28, 0x3d, 0x45, 0xbd, 0x14, 0x78, 0x84,
	0xe1, 0x51, 0x05, 0xe0, 0xeb, 0x1a, 0x3b, 0x36, 0xb9, 0x44, 0x01, 0xaa, 0xe8, 0x65, 0x06, 0x3b,
	0xa2, 0x20, 0x4a, 0x81, 0xa3, 0x58, 0x03, 0xaf, 0x56, 0xc0, 0x7d, 0x2f, 0x31, 0x88, 0x36, 0xf0,
	0xd4, 0x3f, 0xc9, 0xc1, 0xaa, 0x8c, 0x45, 0xe5, 0x13, 0xd8, 0xa0, 0x33, 0x27, 0xd6, 0x1a, 0x29,
	0x1a, 0x63, 0x64, 0x2d, 0xf0, 0x88, 0x30, 0x36, 0xd2, 0xb9, 0xf7, 0x41, 0x49, 0x0c, 0x63, 0x47,
	0xc3, 0x78, 0xab, 0x0a, 0x2b, 0x67, 0x67, 0xf3, 0x0e, 0x2c, 0x4f, 0x46, 0xa6, 0x6f, 0xbc, 0x74,
	0xec, 0xc0, 0x33, 0x4e, 0x09, 0xe1, 0x06, 0x67, 0x89, 0x42, 0x9f, 0x52, 0xe0, 0x3e, 0x21, 0xea,
	0x2f, 0xe6, 0x61, 0xbd, 0x73, 0x6e, 0x8d, 0x46, 0xa2, 0x6e, 0xc5, 0x9b, 0x34, 0x19, 0x99, 0x97,
	0xf4, 0x30, 0x22, 0x15, 0x2b, 0x31, 0x08, 0xd5, 0xb1, 0x5f, 0x81, 0x4a, 0xa8, 0x63, 0x0e, 0x1d,
	0x85, 0x9c, 0x94, 0x9f, 0xd4, 0xd3, 0xfa, 0x11, 0xd3, 0xd5, 0x97, 0x26, 0xa2, 0x06, 0x6f, 0x40,
	0x11, 0x07, 0x52, 0x79, 0x67, 0x9a, 0xb8, 0x88, 0x6d, 0x6d, 0xa0, 0xac, 0xc1, 0x82, 0xe7, 0x9b,
	0x7e, 0xe0, 0xa1, 0x3a, 0x56, 0x74, 0xde, 0xa2, 0x2c, 0x79, 0xc1, 0x4b, 0x83, 0xf7, 0xcd, 0x33,
	0x96, 0xbc, 0xe0, 0x65, 0x87, 0x75, 0x6f, 0x40, 0x71, 0x62, 0x5e, 0x1a, 0xbe, 0x35, 0x26, 0xa8,
	0x7a, 0x05, 0x7d, 0x71, 0x62, 0x5e, 0x76, 0xad, 0x31, 0xda, 0x34, 0x97, 0xf4, 0x89, 0x75, 0x41,
	0x58, 0xf7, 0x22, 0x76, 0x97, 0x39, 0x0c, 0x51, 0xee, 0x42, 0xf9, 0xd4, 0xb2, 0x2d, 0xef, 0x8c,
	0x61, 0x14, 0x11, 0x03, 0x18, 0x28, 0x44, 0xe8, 0x9b, 0x76, 0x9f, 0x8c, 0x18, 0x42, 0x89, 0x21,
	0x30, 0x10, 0x22, 0xbc, 0x03, 0xcb, 0x6c, 0x45, 0xc4, 0x1e, 0x30, 0x1c, 0x40, 0x9c, 0x25, 0x84,
	0xb6, 0xec, 0x01, 0x62, 0x29, 0x30, 0x27, 0x98, 0x39, 0xfc, 0x4d, 0xd9, 0x63, 0x23, 0xed, 0x60,
	0xfc, 0x92, 0xb8, 0xa8, 0x70, 0x25, 0xbd, 0x8c, 0xb0, 0x36, 0x82, 0x84, 0xe3, 0x18, 0x10, 0xa6,
	0x65, 0xc5, 0xf0, 0x38, 0xf6, 0xc8, 0x28, 0xb6, 0x87, 0xb4, 0x77, 0x19, 0x7b, 0x99, 0x3d, 0xa4,
	0x9d, 0x77, 0xa1, 0x1c, 0x4c, 0x06, 0xa6, 0xcf, 0x17, 0xbf, 0xc2, 0x38, 0x67, 0x20, 0xe4, 0x69,
	0x0d, 0x16, 0x5c, 0x32, 0x36, 0xdd, 0xf3, 0x5a, 0x15, 0x67, 0xe6, 0x2d, 0x6a, 0xe7, 0xc9, 0x85,
	0x39, 0x0a, 0xe8, 0x50, 0x62, 0xfb, 0xee, 0x65, 0xed, 0x06, 0x92, 0xae, 0x84, 0xd0, 0x16, 0x05,
	0x52, 0x59, 0x88, 0xd0, 0xd0, 0x56, 0x2a, 0x32, 0x59, 0x68, 0x71, 0x14, 0x34, 0x97, 0x4b, 0x44,
	0x68, 0x25, 0xad, 0xf9, 0xcd, 0xa4, 0x35, 0x57, 0xff, 0x34, 0x07, 0xb7, 0x4e, 0x70, 0xa1, 0x27,
	0xe6, 0x25, 0x93, 0x24, 0xf2, 0x55, 0x40, 0x3c, 0x9f, 0x1a, 0xc6, 0x58, 0x36, 0xe9, 0x4f, 0xe5,
	0x03, 0x98, 0xbf, 0xae, 0x34, 0x32, 0x44, 0x61, 0xe9, 0x85, 0xc4, 0xd2, 0xa9, 0x0c, 0x3a, 0x81,
	0xdb, 0x27, 0x91, 0x0c, 0x62, 0x2b, 0x12, 0x32, 0x7a, 0x9d, 0x31, 0x09, 0x44, 0x21, 0xbb, 0x9c,
	0x10, 0xf5, 0x08, 0xd6, 0xd2, 0x7c, 0x7a, 0x13, 0xc7, 0xf6, 0x88, 0x52, 0x83, 0xc5, 0x97, 0xe6,
	0x88, 0x4a, 0x0a, 0x32, 0x3b, 0xa7, 0x87, 0xcd, 0x84, 0x16, 0xe4, 0x13, 0x5a, 0xa0, 0x1a, 0x50,
	0x63, 0xe4, 0x9a, 0x28, 0x62, 0x57, 0xac, 0x3c, 0x9b, 0x10, 0x5b, 0xa2, 0xe9, 0x39, 0x76, 0xbc,
	0x44, 0xda, 0x52, 0x37, 0x61, 0x43, 0x32, 0x01, 0x63, 0x59, 0xfd, 0x22, 0x9c, 0x7d, 0x1f, 0x35,
	0xe0, 0xcd, 0x67, 0x0f, 0xe5, 0xbd, 0x10, 0xcb, 0x7b, 0x3c, 0x73, 0x82, 0x38, 0x9f, 0xf9, 0x00,
	0x6a, 0x4d, 0x7a, 0xf6, 0x3a, 0x53, 0xce, 0x37, 0x9e, 0x99, 0xce, 0x22, 0x21, 0xc4, 0x67, 0xf9,
	0x12, 0xd6, 0x79, 0xe7, 0x69, 0xe0, 0x91, 0x6f, 0x7f, 0x73, 0xeb, 0xd1, 0x2a, 0x04, 0xfa, 0x7c,
	0xee, 0x43, 0xb8, 0x83, 0x7d, 0x6d, 0xc7, 0xb7, 0x4e, 0x2f, 0xbf, 0xe1, 0x06, 0xab, 0x5b, 0xf0,
	0x76, 0x16, 0x35, 0x3e, 0x5f, 0x00, 0xb7, 0x0e, 0x88, 0x68, 0xdc, 0xdf, 0x68, 0xa5, 0x8f, 0x61,
	0xd5, 0x26, 0x64, 0x60, 0x98, 0xa3, 0x11, 0x37, 0xc1, 0xcc, 0x9c, 0x14, 0xd0, 0x24, 0xdc, 0xa0,
	0x7d, 0x8d, 0xd1, 0x88, 0xd9, 0x62, 0x6a, 0x55, 0xd4, 0x1e, 0xac, 0xa5, 0xa7, 0xe5, 0xfa, 0xf0,
	0x83, 0x50, 0x4d, 0x73, 0xa8, 0xa6, 0x0f, 0x92, 0x6a, 0x9a, 0x71, 0x23, 0x71, 0x8d, 0x55, 0xff,
	0xa8, 0x00, 0x2b, 0x08, 0xee, 0x58, 0xe3, 0xc9, 0x88, 0x19, 0x90, 0xff, 0x53, 0x97, 0xd5, 0xf4,
	0x3d, 0xb2, 0x28, 0xb9, 0x47, 0x36, 0xa1, 0xe4, 0x9c, 0x9e, 0x7a, 0xc4, 0x0f, 0x1d, 0xc6, 0x92,
	0x5e, 0x64, 0x00, 0x41, 0xe9, 0x4a, 0x33, 0x2e, 0x19, 0x98, 0xbe, 0x64, 0x52, 0x17, 0x45, 0x79,
	0xea, 0xa2, 0x98, 0xbe, 0x10, 0x96, 0x24, 0x17, 0x82, 0x7a, 0x0c, 0xb7, 0x0f, 0x88, 0xaf, 0x8d,
	0x8f, 0xed, 0xa1, 0x63, 0xd9, 0x43, 0xdc, 0xc3, 0x43, 0xcb, 0xf3, 0xb3, 0xe5, 0xee, 0x0e, 0x80,
	0x6f, 0xba, 0x43, 0xe2, 0xe3, 0x01, 0x32, 0xaf, 0xa6, 0xc4, 0x20, 0xf4, 0x0e, 0xf8, 0x31, 0xdc,
	0xc9, 0x20, 0xc8, 0x25, 0xea, 0xff, 0x03, 0xb0, 0xc5, 0x8d, 0x2c, 0x8f, 0x7a, 0x52, 0x85, 0x69,
	0x27, 0x34, 0x25, 0x33, 0x7a, 0xc9, 0x09, 0xa9, 0xa8, 0xff, 0x9a, 0x87, 0x9b, 0xa1, 0xa8, 0x5e,
	0xc9, 0x27, 0x9b, 0x07, 0x2f, 0x00, 0xce, 0x27, 0x42, 0x30, 0x98, 0xb9, 0x0b, 0xe5, 0x50, 0x35,
	0x68, 0x3f, 0x0f, 0xf2, 0x18, 0x08, 0x11, 0x56, 0x61, 0x7e, 0x64, 0x8d, 0x2d, 0x9f, 0x0b, 0x04,
	0x6b, 0x50, 0x19, 0x62, 0x47, 0xc7, 0x43, 0x06, 0xde, 0x52, 0x7f, 0x03, 0x4a, 0xc7, 0x11, 0xed,
	0x3a, 0xac, 0x1d, 0xeb, 0x7b, 0x2d, 0xdd, 0xe8, 0xbe, 0x38, 0x69, 0x19, 0xbd, 0x76, 0xe7, 0xa4,
	0xd5, 0xd4, 0xf6, 0xb5, 0xd6, 0x5e, 0xf5, 0x2d, 0x65, 0x13, 0xd6, 0x85, 0xbe, 0x93, 0xc3, 0xc6,
	0x8b, 0x96, 0x6e, 0x20, 0xa4, 0x9a, 0x4b, 0x0d, 0x6c, 0x1e, 0x37, 0x9a, 0xcf, 0x78, 0x5f, 0x5e,
	0x35, 0x00, 0x3a, 0x31, 0x77, 0x9b, 0xb0, 0xde, 0xe9, 0x36, 0xba, 0xbd, 0x8e, 0x6c, 0x8e, 0x1a,
	0xac, 0x8a, 0x9d, 0xfb, 0x5a, 0x5b, 0xeb, 0x3c, 0x6b, 0xed, 0xb1, 0x09, 0x92, 0xc3, 0xa2, 0xbe,
	0xbc, 0xda, 0x85, 0xd5, 0xe4, 0xce, 0x7e, 0x2b, 0x07, 0xb6, 0x07, 0xb7, 0xf6, 0xa2, 0x1b, 0xab,
	0xef, 0xb8, 0x83, 0x37, 0xb2, 0x9c, 0x35, 0x58, 0x4b, 0x53, 0xe1, 0x16, 0xf3, 0x43, 0xa8, 0xb5,
	0x6c, 0x9f, 0x82, 0x4f, 0x03, 0x7b, 0xc0, 0x76, 0x28, 0x9c, 0x42, 0x24, 0x98, 0x9b, 0xba, 0x71,
	0x24, 0xc3, 0x38, 0xcd, 0x3f, 0xcb, 0x41, 0x85, 0x75, 0x5c, 0x4d, 0x49, 0xb8, 0x56, 0xf2, 0xe2,
	0xb5, 0xc2, 0x1c, 0x59, 0x4a, 0xc3, 0x60, 0x31, 0x03, 0x93, 0xb0, 0x32, 0x83, 0x35, 0x31, 0x50,
	0x88, 0x51, 0x58, 0x88, 0x30, 0x27, 0xa2, 0x9c, 0x84, 0xa9, 0x8a, 0x01, 0x31, 0x47, 0x86, 0xef,
	0x9c, 0x13, 0x1b, 0x45, 0xb1, 0xa4, 0x97, 0x28, 0xa4, 0x4b, 0x01, 0x6a, 0x15, 0x96, 0x43, 0x46,
	0x39, 0xef, 0xdf, 0x8d, 0x4d, 0x79, 0xd3, 0xb1, 0xbd, 0x60, 0x4c, 0xae, 0xb1, 0x1b, 0xff, 0x94,
	0x87, 0xf5, 0xa9, 0x51, 0xfc, 0xf8, 0x67, 0x2c, 0x3d, 0xe1, 0x0c, 0xe6, 0x25, 0xa1, 0x7d, 0x6c,
	0xe8, 0x0b, 0x69, 0x43, 0x8f, 0xa1, 0xdb, 0x78, 0x6c, 0xf9, 0x7c, 0x7b, 0xe6, 0xc2, 0xd0, 0x8d,
	0xc2, 0x9a, 0x61, 0x1c, 0xc5, 0x51, 0xc4, 0x10, 0x97, 0xa9, 0x63, 0x95, 0xf5, 0x08, 0x31, 0x2e,
	0x75, 0xfa, 0x39, 0x76, 0x6c, 0xa9, 0x81, 0xa3, 0x51, 0x8b, 0xa8, 0x42, 0xc5, 0xb3, 0x86, 0xb6,
	0x31, 0x0c, 0xac, 0xd1, 0x80, 0xae, 0x86, 0x05, 0xf6, 0x65, 0x0a, 0x3c, 0xa0, 0x30, 0x6d, 0x90,
	0xda, 0xee, 0x62, 0x6a, 0xbb, 0xe5, 0x31, 0x77, 0x49, 0x1a, 0x73, 0xab, 0xbf, 0x97, 0x83, 0x4a,
	0xe8, 0x48, 0x77, 0xfa, 0x8e, 0x8b, 0xf7, 0x80, 0x79, 0x31, 0x34, 0x3c, 0xda, 0xc0, 0xad, 0xcc,
	0xeb, 0x45, 0xf3, 0x62, 0xc8, 0x3a, 0xef, 0x43, 0xc5, 0x23, 0xee, 0x85, 0xd5, 0x27, 0x1c, 0x21,
	0x8f, 0x08, 0x4b, 0x1c, 0xc8, 0x90, 0xa8, 0xd1, 0xa2, 0x57, 0x2e, 0x47, 0x29, 0x20, 0x0a, 0x20,
	0x28, 0x42, 0xb8, 0x70, 0x62, 0x1a, 0x73, 0x0c, 0x01, 0x41, 0x88, 0xa0, 0x7e, 0x04, 0x2b, 0x21,
	0x53, 0xcf, 0x1d, 0x77, 0xd0, 0xb4, 0x7d, 0x7a, 0x03, 0xbd, 0x72, 0xdc, 0xf0, 0x70, 0xf1, 0x37,
	0x55, 0xce, 0xbe, 0xed, 0xf3, 0x33, 0xa5, 0x3f, 0xd5, 0x3f, 0xcf, 0xc5, 0x23, 0x3b, 0xc1, 0x78,
	0x6c, 0xba, 0x97, 0x74, 0x41, 0x6c, 0x23, 0xfa, 0x3c, 0x26, 0xae, 0xe8, 0x45, 0x04, 0x50, 0xb2,
	0x9f, 0x02, 0x20, 0x13, 0x2c, 0xce, 0x60, 0xd7, 0xf8, 0xa6, 0x3c, 0xce, 0x40, 0xd6, 0xf4, 0x12,
	0xa2, 0xa3, 0x93, 0xd0, 0x80, 0x0a, 0x65, 0x83, 0xd2, 0x65, 0x56, 0xa7, 0x20, 0xb3, 0x3a, 0xa9,
	0x85, 0xe8, 0xe5, 0x57, 0xec, 0x07, 0xda, 0x9d, 0xbf, 0xcb, 0xc3, 0x92, 0x18, 0xc7, 0x50, 0x81,
	0xb3, 0x3c, 0xc3, 0xb4, 0x1d, 0xfb, 0x72, 0xec, 0x04, 0x1e, 0x8f, 0xe1, 0xcb, 0x96, 0xd7, 0x08,
	0x41, 0xf4, 0x0c, 0xa2, 0x3b, 0x13, 0x85, 0x28, 0xcf, 0x6e, 0xf3, 0x10, 0x18, 0xde, 0xe6, 0xc8,
	0x5b, 0xc4, 0x57, 0x49, 0x2f, 0x52, 0x00, 0x9d, 0x95, 0x86, 0x0f, 0x7d, 0xc7, 0xf6, 0x89, 0x1d,
	0x26, 0x05, 0xc3, 0x66, 0x6a, 0x3b, 0xe6, 0xbf, 0xd6, 0x76, 0x88, 0x2a, 0xb8, 0x90, 0x54, 0xc1,
	0xa4, 0x96, 0x2d, 0xa6, 0xb5, 0x2c, 0xa1, 0xa1, 0xc5, 0x94, 0x86, 0xde, 0x87, 0x8a, 0x4b, 0x2e,
	0x2c, 0xf2, 0x2a, 0x74, 0x7d, 0x98, 0x24, 0x2f, 0x31, 0x20, 0xb3, 0x89, 0xea, 0xef, 0xe4, 0xd0,
	0xa0, 0x84, 0xbc, 0xcd, 0xbe, 0x73, 0x43, 0x51, 0xca, 0x0b, 0xa2, 0x14, 0xdf, 0x98, 0x05, 0xf1,
	0xc6, 0x8c, 0xef, 0xd7, 0x39, 0xf1, 0x7e, 0x15, 0x52, 0x6b, 0xf3, 0x62, 0x6a, 0x4d, 0xd5, 0xd0,
	0x42, 0x25, 0xd9, 0xe0, 0x16, 0x6a, 0x17, 0xe6, 0x84, 0xab, 0x69, 0x56, 0x2c, 0x8b, 0x78, 0xea,
	0x3e, 0x6c, 0x08, 0xa4, 0xb8, 0x30, 0x67, 0x2f, 0x4a, 0x60, 0x29, 0x9f, 0x60, 0xa9, 0x07, 0x75,
	0x19, 0x1d, 0xce, 0xd5, 0x47, 0xb0, 0xe8, 0x31, 0x10, 0xf7, 0x9d, 0x33, 0xa4, 0x37, 0x1c, 0x17,
	0x62, 0xab, 0x2d, 0x58, 0x4d, 0x48, 0x42, 0xa8, 0x6d, 0xd3, 0x9c, 0x25, 0x0c, 0x4a, 0x3e, 0x69,
	0x50, 0xd4, 0xe7, 0x70, 0xef, 0xa9, 0xe9, 0x8b, 0x0c, 0x0a, 0xc4, 0x84, 0x3b, 0x21, 0xb0, 0x06,
	0xf1, 0xcd, 0x5e, 0xd1, 0x17, 0x03, 0x8b, 0x89, 0x72, 0xe6, 0xb2, 0xcf, 0x41, 0x9d, 0x45, 0x98,
	0x2f, 0xbf, 0x05, 0x4b, 0x7c, 0x41, 0xa2, 0xdf, 0xa0, 0xce, 0x90, 0xf8, 0x90, 0x42, 0x99, 0x8f,
	0x43, 0x35, 0xfe, 0xa5, 0x60, 0x76, 0xde, 0x28, 0x16, 0xfa, 0x1f, 0x50, 0xd7, 0xb4, 0xa5, 0x59,
	0x98, 0xb2, 0x34, 0xea, 0x36, 0x54, 0xe3, 0x55, 0xf1, 0x1d, 0x5b, 0x85, 0x79, 0xdf, 0x31, 0x3d,
	0x9f, 0x1b, 0x62, 0xd6, 0x50, 0x8f, 0xa1, 0xde, 0x08, 0x06, 0x16, 0x95, 0xf6, 0x60, 0xe4, 0x37,
	0xcd, 0xd1, 0xe8, 0xa5, 0xd9, 0x3f, 0xbf, 0xae, 0x5f, 0x42, 0xc7, 0x84, 0xc7, 0xc7, 0x5a, 0xea,
	0x1d, 0xd8, 0x94, 0x12, 0xe4, 0xfe, 0x43, 0x1b, 0xee, 0x34, 0xcf, 0x48, 0xff, 0xbc, 0xe7, 0x11,
	0x97, 0xf9, 0x03, 0xd4, 0xa0, 0x8e, 0xad, 0x19, 0x5a, 0x3f, 0xcb, 0x0d, 0xc0, 0x98, 0x37, 0x83,
	0x1e, 0x9f, 0xb1, 0x07, 0x8f, 0x0e, 0x88, 0x4f, 0xfb, 0xf1, 0xf6, 0x6c, 0xbd, 0x9e, 0x10, 0x7b,
	0x60, 0xf9, 0x81, 0x4b, 0x1a, 0x63, 0xea, 0x07, 0x1c, 0x39, 0xb6, 0x7f, 0x36, 0x9a, 0xa1, 0xa0,
	0xab, 0x30, 0x3f, 0xa6, 0x38, 0xdc, 0x5c, 0xb3, 0x86, 0xfa, 0x6b, 0xf0, 0xfe, 0xf5, 0xc8, 0xf2,
	0xed, 0xbf, 0x07, 0x4b, 0xec, 0x32, 0x33, 0xc7, 0x51, 0x8e, 0xb7, 0xa0, 0xb3, 0x97, 0x00, 0x36,
	0x42, 0xfd, 0x9b, 0x1c, 0x28, 0x1d, 0x62, 0xba, 0xfd, 0x64, 0x0e, 0x20, 0xb6, 0x70, 0x39, 0xb9,
	0x85, 0xcb, 0x8b, 0x16, 0x2e, 0x0e, 0x3c, 0x22, 0x91, 0x8c, 0x02, 0x0f, 0x14, 0xca, 0x77, 0x60,
	0x39, 0xda, 0x4e, 0x86, 0x33, 0x87, 0x38, 0x4b, 0xe1, 0x9e, 0x22, 0x16, 0x0d, 0x5a, 0x7d, 0xd3,
	0xe5, 0xde, 0xce, 0x3c, 0x32, 0x5b, 0x42, 0x08, 0xde, 0x52, 0x1b, 0x50, 0x8c, 0x62, 0x52, 0x1e,
	0xb4, 0x12, 0x16, 0x8e, 0xaa, 0x3e, 0xdc, 0x4c, 0x2c, 0xe2, 0xdb, 0x70, 0xf3, 0xe9, 0xaa, 0x6c,
	0xf2, 0xda, 0x37, 0xf8, 0x46, 0xb0, 0x15, 0x03, 0x05, 0x1d, 0xb3, 0x00, 0xe9, 0x14, 0x6a, 0x07,
	0xc4, 0xc7, 0xf4, 0x07, 0x23, 0xe3, 0x9b, 0xb3, 0x83, 0x37, 0x61, 0x75, 0xf9, 0x59, 0xab, 0x2b,
	0x24, 0x57, 0xf7, 0x33, 0x34, 0xee, 0xe9, 0x79, 0xf8, 0x1a, 0x69, 0x24, 0x8e, 0x6b, 0x14, 0x1c,
	0x16, 0x87, 0x0b, 0x25, 0xbd, 0x0e, 0xb9, 0x03, 0xc9, 0x25, 0x80, 0x2d, 0x82, 0xbb, 0xa9, 0x4c,
	0x04, 0xe2, 0xa8, 0xf2, 0x74, 0xe4, 0xbc, 0x0a, 0xbd, 0x5a, 0x84, 0xec, 0x8f, 0x9c, 0x57, 0xea,
	0xbf, 0xcc, 0xc3, 0x12, 0x7b, 0x9b, 0x68, 0x3a, 0xf6, 0xa9, 0x35, 0x64, 0x5e, 0x29, 0x3e, 0x3f,
	0xe0, 0x43, 0x16, 0x33, 0x4e, 0xfc, 0x45, 0xa2, 0x6d, 0xf2, 0x5c, 0x35, 0x43, 0x10, 0xe3, 0x50,
	0x06, 0xc2, 0x48, 0xef, 0x01, 0x2c, 0xbb, 0x64, 0x10, 0xf4, 0x89, 0xe1, 0x92, 0xaf, 0x02, 0xcb,
	0x0d, 0xc3, 0x84, 0x0a, 0x83, 0xea, 0x0c, 0xc8, 0x62, 0x09, 0x44, 0x13, 0xdd, 0xe4, 0x32, 0x83,
	0x31, 0x0f, 0xb9, 0x0e, 0xc5, 0xc4, 0x1b, 0x64, 0x45, 0x8f, 0xda, 0xca, 0x36, 0x54, 0x03, 0xcf,
	0x1c, 0x12, 0x03, 0x85, 0x94, 0xf1, 0xc2, 0xbc, 0x89, 0x65, 0x84, 0xa3, 0xca, 0x22, 0x3f, 0xdf,
	0x87, 0x9a, 0x88, 0x69, 0x3b, 0x2c, 0x51, 0x63, 0x0c, 0xcc, 0x4b, 0xee, 0x61, 0xac, 0xc6, 0x23,
	0xda, 0x0e, 0x4b, 0x0b, 0x99, 0x97, 0x53, 0x33, 0x90, 0xd7, 0x3e, 0x4f, 0x7a, 0x88, 0x33, 0x90,
	0xd7, 0x78, 0x10, 0xe4, 0xf4, 0x94, 0xf4, 0x7d, 0xeb, 0x82, 0x20, 0x59, 0xfe, 0x18, 0x19, 0x01,
	0x29, 0xb9, 0x3b, 0x00, 0xe4, 0xf5, 0xc4, 0x72, 0x19, 0x46, 0x99, 0x1d, 0x04, 0x83, 0xd0, 0xee,
	0x47, 0xa0, 0x8c, 0x03, 0xcf, 0x37, 0x2c, 0xdb, 0x60, 0xca, 0x34, 0x71, 0x9c, 0x11, 0x4f, 0x81,
	0xac, 0xd0, 0x1e, 0xcd, 0x46, 0x19, 0x39, 0x71, 0x9c, 0x91, 0xfa, 0x05, 0x40, 0x33, 0xde, 0xf0,
	0x4d, 0x58, 0x6f, 0x1e, 0xf7, 0x4e, 0x8e, 0xdb, 0xb2, 0xd0, 0x7a, 0x0d, 0x14, 0xb1, 0x53, 0x6f,
	0xed, 0xf5, 0x9a, 0xad, 0x6a, 0x8e, 0x86, 0xdc, 0x22, 0x7c, 0x4f, 0xeb, 0x34, 0x8f, 0x7b, 0xed,
	0x6e, 0x35, 0xaf, 0xfe, 0x6d, 0x0e, 0x96, 0x7b, 0xc9, 0x2d, 0xdc, 0x82, 0xdb, 0xbd, 0x4e, 0xe3,
	0xa0, 0x65, 0x1c, 0x6a, 0x47, 0x5a, 0x57, 0x36, 0xcd, 0x06, 0xdc, 0x9a, 0xc2, 0x68, 0x1f, 0xb7,
	0xe9, 0x4c, 0x0f, 0xe0, 0x9e, 0xa4, 0xcb, 0x78, 0xa6, 0x75, 0xba, 0xc7, 0xfa, 0x8b, 0x30, 0x5d,
	0xa0, 0x3c, 0x04, 0x55, 0x86, 0xc6, 0xd2, 0x0b, 0x7a, 0xab, 0xd9, 0x6a, 0x77, 0x0f, 0x5f, 0x54,
	0x0b, 0x52, 0xbc, 0x67, 0x8d, 0x4e, 0x8a, 0xde, 0x9c, 0xfa, 0x8b, 0x1c, 0x28, 0xa2, 0x64, 0xb3,
	0x30, 0x5c, 0x59, 0x86, 0x7c, 0xa4, 0xb9, 0x79, 0x96, 0xbf, 0xe3, 0xe2, 0xdc, 0x47, 0x34, 0x79,
	0xfe, 0x2e, 0x41, 0x88, 0x3f, 0xe1, 0x71, 0x85, 0xa9, 0x43, 0xd1, 0x99, 0x10, 0xd7, 0xf4, 0x1d,
	0x97, 0xe7, 0xef, 0xa2, 0x36, 0xea, 0x8a, 0x4b, 0x22, 0xef, 0x7c, 0x8e, 0x87, 0x78, 0x08, 0x42,
	0xe5, 0x7f, 0x0d, 0x1b, 0x4d, 0x6c, 0x25, 0x39, 0x65, 0x56, 0x66, 0x8a, 0xb5, 0xdc, 0x37, 0x60,
	0x2d, 0x9f, 0x64, 0x4d, 0xbd, 0x0d, 0x75, 0xd9, 0xcc, 0xfc, 0x8a, 0x3b, 0x42, 0x4f, 0x51, 0xec,
	0x12, 0xfd, 0x68, 0x05, 0xe6, 0x26, 0xe6, 0x30, 0xac, 0x51, 0xc0, 0xdf, 0xd4, 0x52, 0xd1, 0xff,
	0x0d, 0xcf, 0xfa, 0x49, 0x98, 0xbc, 0x2a, 0x52, 0x40, 0xc7, 0xfa, 0x09, 0x51, 0x5d, 0xd8, 0x94,
	0x92, 0x13, 0x1d, 0x09, 0xdf, 0x1c, 0x85, 0x45, 0x0f, 0xd8, 0x50, 0x7e, 0x48, 0x6f, 0x69, 0xfb,
	0x94, 0x99, 0xf7, 0x3c, 0x9a, 0xf7, 0xad, 0x19, 0x4b, 0x67, 0x59, 0x96, 0x22, 0x1d, 0x82, 0x8e,
	0xd8, 0xc7, 0x53, 0x73, 0x3e, 0xbd, 0xd4, 0x06, 0x62, 0xaa, 0x05, 0xa9, 0x5b, 0x03, 0x2f, 0x74,
	0x24, 0x69, 0x5b, 0x1b, 0x78, 0xea, 0x8f, 0x31, 0xc5, 0x28, 0x19, 0xc9, 0xd9, 0x4d, 0x30, 0x96,
	0xfb, 0xda, 0x8c, 0xfd, 0x7b, 0x0e, 0xee, 0x36, 0x06, 0x83, 0x23, 0xd3, 0x0e, 0xcc, 0xd1, 0x81,
	0x6b, 0xda, 0x7c, 0xaa, 0xae, 0xe9, 0x45, 0x6e, 0xd2, 0x43, 0x58, 0x11, 0x8e, 0x5e, 0x60, 0xb2,
	0x12, 0x1f, 0xb0, 0x36, 0xc0, 0x4c, 0xf0, 0x90, 0x52, 0x48, 0xe4, 0x0c, 0x11, 0x82, 0x7a, 0xbb,
	0x0e, 0x8b, 0x56, 0xc2, 0x93, 0x5c, 0xe0, 0x97, 0xf1, 0x26, 0x94, 0x4e, 0xad, 0x11, 0x31, 0x26,
	0xa6, 0x7f, 0xc6, 0x3d, 0xc9, 0x22, 0x05, 0x9c, 0x98, 0xfe, 0x19, 0xdd, 0x9a, 0xa1, 0xeb, 0x04,
	0x13, 0x16, 0xd3, 0xe0, 0xd6, 0x60, 0x5b, 0x1b, 0x24, 0x24, 0x6a, 0x21, 0x25, 0xec, 0x31, 0x2f,
	0x61, 0x5e, 0x39, 0xe2, 0x85, 0x8a, 0xba, 0x0a, 0x5b, 0xd9, 0xab, 0xe6, 0x62, 0xf7, 0x1c, 0xb6,
	0x3a, 0xbe, 0x33, 0x99, 0xb9, 0x35, 0xeb, 0xb0, 0xe8, 0x9b, 0xde, 0xb9, 0x11, 0x69, 0xf1, 0x02,
	0x6d, 0xa6, 0x78, 0x4b, 0x4b, 0xfb, 0x7d, 0xb8, 0x37, 0x83, 0x30, 0x9f, 0xfd, 0x05, 0xa8, 0x3a,
	0xb9, 0x70, 0xce, 0xc9, 0xb7, 0x3f, 0xff, 0x03, 0xb8, 0x3f, 0x93, 0x34, 0xe7, 0xe0, 0xaf, 0xe7,
	0xe0, 0x96, 0x14, 0x23, 0x7b, 0xd6, 0x6f, 0xa6, 0x25, 0x29, 0x01, 0x2a, 0xcc, 0x10, 0xa0, 0xb9,
	0x6c, 0x01, 0x9a, 0x9f, 0x21, 0x40, 0x0b, 0xd9, 0x02, 0xb4, 0x38, 0xdb, 0x5a, 0x16, 0xd3, 0xd6,
	0x92, 0x6a, 0x05, 0x6e, 0x42, 0xe0, 0x51, 0x8f, 0x08, 0xdd, 0x02, 0x5e, 0x1c, 0x44, 0xc1, 0xd4,
	0x73, 0x6e, 0x86, 0xbe, 0x01, 0x5b, 0x94, 0x80, 0xc8, 0xae, 0xe4, 0x65, 0x84, 0xc7, 0x98, 0xf1,
	0x0b, 0x4b, 0x39, 0xf1, 0xc2, 0xb2, 0x0d, 0x55, 0x9c, 0x89, 0x91, 0x61, 0xb5, 0x1a, 0xac, 0x54,
	0x65, 0x99, 0xc2, 0xf1, 0x74, 0x58, 0xb9, 0xc6, 0x0e, 0xdc, 0x70, 0xf1, 0x64, 0xc5, 0xc9, 0x2a,
	0x2c, 0xc3, 0xc6, 0x3a, 0xe2, 0xd9, 0x76, 0xe1, 0x26, 0xc7, 0x8d, 0x94, 0x9b, 0x62, 0x2f, 0x23,
	0x36, 0x27, 0x13, 0x9e, 0x96, 0x14, 0x9f, 0x31, 0xb2, 0x32, 0x8d, 0x8f, 0xbc, 0xa8, 0x1d, 0xd8,
	0x3a, 0x20, 0xbe, 0x54, 0x80, 0xbc, 0x37, 0xb6, 0xdd, 0x3f, 0x85, 0x7b, 0x33, 0x88, 0xce, 0xb4,
	0xe0, 0xbf, 0x0a, 0x25, 0xdc, 0x45, 0x41, 0x36, 0xef, 0x27, 0x65, 0x53, 0xae, 0x0e, 0x45, 0x3a,
	0x0a, 0x6d, 0xe5, 0x4f, 0x61, 0x93, 0xc1, 0x0f, 0x2c, 0xd3, 0xee, 0xe0, 0x33, 0x79, 0x13, 0xbd,
	0xdb, 0x6b, 0x94, 0x6a, 0x45, 0x61, 0x49, 0x94, 0x3f, 0x8d, 0xfc, 0x63, 0xb3, 0x7f, 0x16, 0x26,
	0x50, 0xb3, 0x9f, 0xdf, 0xd4, 0xa3, 0xe9, 0xc9, 0x05, 0x7e, 0xb3, 0x55, 0xf2, 0x16, 0x2c, 0x8c,
	0xbd, 0x61, 0x3c, 0xe1, 0xfc, 0xd8, 0x1b, 0x6a, 0x03, 0xf5, 0x3f, 0xe6, 0x60, 0x81, 0x97, 0x01,
	0x21, 0xdf, 0xbc, 0xc6, 0x87, 0x87, 0xc1, 0xc5, 0xb0, 0xc4, 0x87, 0x15, 0x00, 0xa5, 0x1c, 0xf0,
	0x12, 0x42, 0xfe, 0x37, 0xfb, 0xdf, 0xd4, 0x3b, 0x5e, 0x94, 0x7a, 0xc7, 0xeb, 0x98, 0xb3, 0x38,
	0x15, 0x8a, 0xcf, 0xd8, 0x45, 0x4b, 0x57, 0x32, 0x34, 0x2d, 0xdb, 0xe0, 0xc5, 0x10, 0x4c, 0x95,
	0x81, 0x82, 0xd8, 0x66, 0x2b, 0x1d, 0xa8, 0x0a, 0x08, 0x2c, 0xb3, 0x01, 0xe8, 0x03, 0xbd, 0x27,
	0x33, 0x71, 0x52, 0x19, 0xd1, 0x97, 0x63, 0x82, 0x98, 0xec, 0xb8, 0x0b, 0x65, 0xe6, 0x97, 0xc7,
	0x0f, 0x91, 0x15, 0x1d, 0x18, 0x28, 0x2c, 0xc6, 0xe1, 0x8e, 0x3a, 0x22, 0x2c, 0x71, 0x04, 0x04,
	0x45, 0xd5, 0x3a, 0x82, 0x9d, 0xaa, 0xf0, 0x13, 0x88, 0xed, 0xd4, 0xac, 0x88, 0x63, 0xf9, 0x6b,
	0x44, 0x1c, 0xf4, 0x7c, 0x57, 0xa4, 0x31, 0x8d, 0x3c, 0x5a, 0xa8, 0xca, 0xa3, 0x85, 0x6d, 0x7c,
	0x27, 0xe3, 0x66, 0x68, 0xe2, 0xd8, 0x99, 0x51, 0xac, 0xda, 0xc6, 0xd7, 0x7c, 0x11, 0x93, 0xeb,
	0xf7, 0x87, 0x91, 0xd0, 0x09, 0x4e, 0xcf, 0xaa, 0xec, 0x10, 0x42, 0x51, 0x44, 0xf5, 0xfd, 0xed,
	0x1c, 0xfa, 0x91, 0x31, 0xc1, 0xa7, 0x97, 0x27, 0xe6, 0x90, 0xcc, 0xcc, 0xc7, 0xa2, 0x75, 0xca,
	0x67, 0x59, 0xa7, 0x42, 0xd2, 0x3a, 0xb1, 0x18, 0x18, 0x19, 0x4b, 0xbc, 0x94, 0x73, 0x3f, 0x38,
	0x4e, 0x09, 0x6f, 0x4a, 0xd9, 0xf8, 0x46, 0xab, 0x8b, 0x8d, 0x5e, 0x5e, 0x34, 0x7a, 0x1b, 0x50,
	0x3c, 0x33, 0x3d, 0x63, 0x1c, 0xbe, 0x77, 0x14, 0xf5, 0xc5, 0x33, 0xd3, 0x3b, 0x72, 0x5c, 0xa2,
	0xbe, 0x07, 0xb7, 0x9a, 0x4e, 0x68, 0xe9, 0x7a, 0x1e, 0x0a, 0x0b, 0x35, 0xcc, 0x92, 0x93, 0xd8,
	0x85, 0x35, 0x19, 0xaa, 0x37, 0x89, 0x4b, 0x40, 0x73, 0x42, 0x09, 0xa8, 0xfa, 0x29, 0xbe, 0x62,
	0xe3, 0x99, 0x4f, 0x91, 0xbf, 0x22, 0x71, 0xaa, 0xfe, 0x45, 0x0e, 0xde, 0xce, 0x1a, 0xcc, 0x77,
	0xe8, 0x39, 0x1a, 0x2c, 0x7a, 0x15, 0x9a, 0x13, 0xbe, 0x3f, 0x9f, 0x26, 0xf7, 0x67, 0x36, 0x81,
	0x5d, 0xbc, 0xd0, 0x8e, 0xcc, 0x09, 0x3e, 0xe2, 0xa3, 0xb1, 0xc3, 0x66, 0xfd, 0x07, 0x50, 0x49,
	0x74, 0xd1, 0xad, 0x38, 0x27, 0x97, 0xe1, 0x56, 0x9c, 0x93, 0x4b, 0xba, 0xe0, 0x0b, 0x73, 0x14,
	0x84, 0x42, 0xc1, 0x1a, 0x9f, 0xe6, 0x3f, 0xce, 0xa9, 0x8f, 0x61, 0xb3, 0x39, 0x22, 0x26, 0x3f,
	0xd4, 0x03, 0xd3, 0xb2, 0x9b, 0x66, 0xff, 0x2c, 0x5b, 0xbc, 0xd4, 0xb7, 0xe1, 0xb6, 0x7c, 0x00,
	0xf7, 0xbf, 0x3e, 0x46, 0x4d, 0x09, 0x65, 0x44, 0x8b, 0x5e, 0x53, 0xe3, 0xa0, 0xb1, 0x84, 0x41,
	0x23, 0xa7, 0x9c, 0x8f, 0x29, 0xb7, 0x50, 0x73, 0xc4, 0x91, 0x7c, 0xe7, 0xde, 0x87, 0x05, 0xa1,
	0x18, 0x34, 0x4b, 0xac, 0x38, 0x8e, 0xfa, 0x49, 0x8a, 0x8c, 0x97, 0xe6, 0xa0, 0x90, 0xc9, 0xc1,
	0x3e, 0x3e, 0x7b, 0x24, 0x86, 0x4a, 0x58, 0x28, 0x5c, 0xc9, 0xc2, 0x87, 0x78, 0xdf, 0xf7, 0x6c,
	0x97, 0x98, 0xd3, 0xde, 0x7a, 0xf6, 0xd6, 0x7e, 0x01, 0xea, 0xac, 0x61, 0xdf, 0xcc, 0x8e, 0x7c,
	0x0f, 0xb6, 0x8e, 0x4c, 0xf7, 0x5c, 0x42, 0xd7, 0xcc, 0x7e, 0x9e, 0xa7, 0x4e, 0xff, 0x8c, 0x51,
	0xfc, 0xc8, 0xbf, 0x87, 0xcf, 0x34, 0xd4, 0x34, 0x1c, 0xa0, 0x8b, 0x6a, 0x9f, 0x3a, 0x82, 0xca,
	0x44, 0x6e, 0x6c, 0x2e, 0xe1, 0xc6, 0xaa, 0x06, 0x26, 0x07, 0x53, 0xa3, 0xf8, 0x1a, 0x9b, 0xb0,
	0x82, 0xae, 0x20, 0x1f, 0x4b, 0x2f, 0x2d, 0xb6, 0xce, 0x54, 0x3a, 0x3e, 0x39, 0xba, 0x12, 0x88,
	0x4d, 0x55, 0x83, 0x4a, 0xa2, 0x3f, 0xc5, 0x4c, 0x4e, 0xf4, 0xa9, 0xd1, 0x87, 0xa7, 0x5d, 0xa2,
	0xc3, 0x80, 0x10, 0xea, 0x30, 0xa8, 0xbf, 0x2f, 0x54, 0xa0, 0x3c, 0xb3, 0xec, 0x19, 0x49, 0xcc,
	0x1f, 0xc2, 0xbc, 0xd7, 0x27, 0x36, 0xa3, 0xb1, 0xfc, 0xe4, 0xdd, 0x29, 0x0d, 0x4f, 0xd3, 0xd8,
	0xed, 0x50, 0x74, 0x9d, 0x8d, 0x4a, 0x15, 0xb0, 0x14, 0x52, 0x05, 0x2c, 0xea, 0xef, 0xe6, 0x60,
	0x1e, 0xf1, 0x95, 0x2d, 0xb8, 0xdd, 0x69, 0xb6, 0xda, 0x2d, 0xe3, 0xa4, 0xc1, 0xf3, 0x35, 0xbc,
	0x74, 0xe4, 0xe0, 0xf0, 0xf8, 0x69, 0xe3, 0xb0, 0xfa, 0x96, 0x72, 0x17, 0x36, 0x19, 0x06, 0x2b,
	0xf7, 0x48, 0x20, 0x69, 0x47, 0xd5, 0x9c, 0x72, 0x07, 0x36, 0xd2, 0x24, 0x78, 0x69, 0x8a, 0x76,
	0x54, 0xcd, 0x2b, 0xf7, 0xe1, 0x2e, 0xeb, 0xee, 0x7c, 0xa6, 0x1d, 0x1e, 0x1a, 0x7b, 0xad, 0x6e,
	0x43, 0x3b, 0xe4, 0x78, 0x4f, 0x7b, 0xdd, 0xee, 0x71, 0xbb, 0x5a, 0x50, 0x83, 0xb8, 0x7e, 0x84,
	0xad, 0x29, 0x4e, 0xba, 0x9e, 0x59, 0x36, 0xf7, 0x69, 0xb8, 0x77, 0x46, 0x01, 0xe8, 0xcd, 0xdc,
	0x01, 0x60, 0x9d, 0x96, 0x3f, 0x8a, 0x36, 0x1b, 0x7b, 0x29, 0x00, 0xbf, 0x70, 0xa0, 0xdd, 0xe1,
	0x2b, 0x0d, 0x73, 0x29, 0xcb, 0x14, 0xd6, 0x64, 0x20, 0xf5, 0xdf, 0x72, 0xb0, 0xd6, 0x08, 0x7c,
	0x47, 0xa2, 0x56, 0x0f, 0xa1, 0xf8, 0xd2, 0xf4, 0xd0, 0x71, 0xe3, 0xe6, 0xa2, 0xbc, 0x3b, 0x34,
	0x77, 0x9f, 0x9a, 0x1e, 0x35, 0x6a, 0xfa, 0xe2, 0x4b, 0xf6, 0x63, 0x5a, 0xfb, 0xaf, 0xaa, 0x22,
	0x93, 0x95, 0x9b, 0xd2, 0x7b, 0xd5, 0x25, 0x46, 0xff, 0x8c, 0xf4, 0xcf, 0xd1, 0xfb, 0x2b, 0xea,
	0xc5, 0x89, 0x4b, 0xf0, 0xf1, 0x23, 0xe9, 0x59, 0x2f, 0xa4, 0x3c, 0xeb, 0x1d, 0xb8, 0xc1, 0x0f,
	0x7a, 0xea, 0xab, 0x83, 0x15, 0x76, 0xde, 0x71, 0x55, 0xc1, 0x18, 0xd6, 0xa7, 0x16, 0x1b, 0xef,
	0x73, 0xdf, 0xb4, 0x59, 0x8c, 0xc5, 0x5f, 0xb7, 0x8b, 0x7d, 0xd3, 0x66, 0xde, 0x75, 0xca, 0x52,
	0xe4, 0xaf, 0x67, 0x29, 0x76, 0xfe, 0x31, 0x07, 0xe5, 0x28, 0x8d, 0x1e, 0x78, 0xca, 0x6d, 0xa8,
	0xb1, 0x53, 0xe7, 0x55, 0x44, 0x53, 0xc9, 0xd1, 0x44, 0xef, 0x49, 0xe3, 0x05, 0x56, 0x1d, 0x6d,
	0xc0, 0xad, 0x04, 0x5c, 0x6f, 0x35, 0x5b, 0xda, 0xe7, 0xad, 0xbd, 0x6a, 0x9e, 0x0a, 0x5e, 0xa2,
	0x4b, 0x6b, 0x1b, 0x7a, 0x6b, 0xbf, 0xd7, 0xde, 0xd3, 0xda, 0x07, 0xd5, 0xc2, 0xd4, 0xc8, 0xa8,
	0x5c, 0x69, 0x6e, 0xaa, 0xab, 0xd9, 0x68, 0x37, 0x5b, 0x87, 0xad, 0xbd, 0xea, 0xbc, 0x64, 0x3e,
	0x4a, 0xb1, 0xb5, 0x57, 0x5d, 0xd8, 0xf9, 0xfb, 0x1c, 0xac, 0xb1, 0x22, 0x5a, 0x32, 0x60, 0x0b,
	0x8b, 0xea, 0xfc, 0xde, 0x83, 0x07, 0x21, 0x0d, 0x2e, 0xda, 0x9d, 0xde, 0x53, 0xf9, 0x42, 0x1f,
	0xc1, 0xbb, 0xd9, 0xa8, 0x5c, 0x71, 0x18, 0x42, 0x35, 0xa7, 0xec, 0xc0, 0xc3, 0x6c, 0x64, 0xa6,
	0x83, 0x94, 0xc1, 0x4e, 0xab, 0x9a, 0x9f, 0x4d, 0x98, 0xe1, 0x76, 0xb5, 0xa3, 0xd6, 0x71, 0xaf,
	0x5b, 0x2d, 0xec, 0xfc, 0x71, 0x0e, 0xd6, 0x78, 0xc5, 0xac, 0x64, 0x2d, 0xe1, 0x26, 0x5f, 0xb5,
	0x96, 0xc7, 0xf0, 0x28, 0x1b, 0xb5, 0x7d, 0xdc, 0xa5, 0xff, 0xb4, 0xfd, 0x17, 0x7c, 0xeb, 0xab,
	0x39, 0xca, 0xe3, 0xcc, 0x01, 0x02, 0x72, 0x7e, 0xe7, 0x08, 0x16, 0x4f, 0x58, 0xd1, 0xb5, 0x52,
	0x83, 0x55, 0x6a, 0x5d, 0x24, 0xd9, 0xee, 0x9b, 0xb0, 0x12, 0xf5, 0x34, 0x0e, 0xb5, 0x93, 0xc6,
	0x8b, 0x6a, 0x2e, 0x01, 0x7c, 0xde, 0x6a, 0x3e, 0x6b, 0x74, 0xab, 0xf9, 0x9d, 0x7f, 0xce, 0xc1,
	0x8a, 0x70, 0x01, 0x21, 0xdd, 0x7b, 0x70, 0xe7, 0xa8, 0xd1, 0xee, 0x35, 0x0e, 0x8d, 0x03, 0xbd,
	0xd1, 0x96, 0xa6, 0xd3, 0xdf, 0x86, 0xba, 0x04, 0x45, 0xdb, 0x33, 0x0e, 0xb5, 0x4e, 0xb7, 0x9a,
	0xa3, 0xf6, 0x71, 0xba, 0xbf, 0xdb, 0x0d, 0x11, 0xf2, 0xd9, 0x04, 0xf6, 0xb5, 0xc3, 0x56, 0xb5,
	0x30, 0x83, 0x00, 0x22, 0xcc, 0xc9, 0x09, 0x1c, 0xe8, 0xc7, 0xbd, 0x13, 0x43, 0xdb, 0xab, 0xce,
	0xef, 0xfc, 0x32, 0x07, 0xd5, 0x38, 0x64, 0xe7, 0xa7, 0x28, 0xbc, 0x44, 0x34, 0x3a, 0x9f, 0xc5,
	0x2a, 0xa2, 0x75, 0xd9, 0x9a, 0x24, 0x9d, 0x48, 0x1d, 0x95, 0xee, 0x2e, 0x6c, 0x66, 0xf5, 0x53,
	0xdd, 0xca, 0xd3, 0x7d, 0x93, 0x20, 0x74, 0x5b, 0xfa, 0x91, 0xd6, 0x6e, 0x50, 0x1a, 0x85, 0x0c,
	0x1a, 0x7a, 0xeb, 0xf3, 0xe3, 0xcf, 0x28, 0x8d, 0xb9, 0x0c, 0x26, 0x10, 0x81, 0x6a, 0xe2, 0xce,
	0x51, 0xb8, 0xaa, 0x83, 0x38, 0x0c, 0xdd, 0x84, 0xf5, 0x83, 0x86, 0xd6, 0x36, 0x3a, 0xc7, 0x3d,
	0xbd, 0x99, 0x3e, 0xa9, 0xdb, 0x50, 0x13, 0x3b, 0x9b, 0xc7, 0x47, 0x47, 0x5a, 0x37, 0xac, 0x8f,
	0xdc, 0x69, 0x86, 0xaf, 0x6b, 0x7c, 0x83, 0xe2, 0x57, 0x97, 0x48, 0xb4, 0x7b, 0x9d, 0xf0, 0x01,
	0x25, 0xd9, 0xd3, 0xfa, 0xd1, 0x89, 0xa6, 0xd3, 0x8d, 0x79, 0xf2, 0x07, 0xf7, 0xa1, 0xdc, 0x42,
	0xbb, 0xd7, 0xa5, 0x66, 0x4f, 0xf9, 0x02, 0x96, 0x93, 0x1f, 0x03, 0x28, 0xa9, 0xac, 0x8a, 0xf4,
	0x93, 0x86, 0xfa, 0x3b, 0xb3, 0x91, 0xb8, 0x51, 0x1e, 0xc0, 0x8d, 0xa9, 0xca, 0x7d, 0xe5, 0xa1,
	0x6c, 0xe8, 0xf4, 0xb7, 0x03, 0xf5, 0x77, 0xaf, 0xc4, 0x4b, 0xcf, 0x22, 0xd4, 0x94, 0xcb, 0x67,
	0x99, 0x2e, 0x61, 0x97, 0xcf, 0x22, 0x29, 0x4e, 0xa7, 0xb3, 0x4c, 0x55, 0xe9, 0xa7, 0x67, 0xc9,
	0xfa, 0x1e, 0x20, 0x3d, 0x4b, 0x66, 0xb9, 0xbf, 0x62, 0x52, 0x91, 0x49, 0x96, 0xe3, 0x2b, 0x0f,
	0xa4, 0x83, 0xd3, 0x9f, 0x03, 0xd4, 0x1f, 0x5e, 0x85, 0xc6, 0xa7, 0xf0, 0x68, 0x34, 0x28, 0xab,
	0xc3, 0x57, 0x1e, 0x49, 0x28, 0x64, 0xd5, 0xfe, 0xd7, 0xdf, 0xbf, 0x1e, 0x32, 0x9f, 0x74, 0x82,
	0xb1, 0xc8, 0x74, 0x61, 0xb4, 0xb2, 0x33, 0xe5, 0x17, 0x66, 0x96, 0x63, 0xd7, 0x1f, 0x5d, 0x0b,
	0x97, 0xcf, 0xf8, 0x05, 0x2c, 0x27, 0xab, 0xfa, 0xd3, 0x82, 0x2d, 0xfd, 0xd4, 0x20, 0x2d, 0xd8,
	0x19, 0x1f, 0x06, 0xf4, 0x60, 0x49, 0xac, 0x16, 0x56, 0xee, 0xc9, 0x47, 0x89, 0xcc, 0xab, 0xb3,
	0x50, 0x62, 0x9e, 0x93, 0x85, 0xbe, 0x69, 0x9e, 0xa5, 0xc5, 0xc4, 0x69, 0x9e, 0xe5, 0xb5, 0xc2,
	0x54, 0x80, 0xa7, 0x8a, 0x7e, 0xd3, 0x02, 0x9c, 0x55, 0x4c, 0x9c, 0x16, 0xe0, 0xcc, 0xea, 0x61,
	0xa5, 0x09, 0x0b, 0x0c, 0xae, 0xa4, 0x22, 0x94, 0x44, 0x49, 0x71, 0xfd, 0xb6, 0xbc, 0x93, 0x13,
	0xd1, 0xa0, 0x18, 0x16, 0x08, 0x29, 0x19, 0x85, 0x63, 0x21, 0xa1, 0xb7, 0xb3, 0xba, 0x39, 0xa9,
	0x2f, 0x61, 0x25, 0x55, 0x39, 0xa7, 0x4c, 0x1f, 0xb1, 0xa4, 0xbe, 0xaf, 0xfe, 0xe0, 0x0a, 0x2c,
	0x4e, 0x7f, 0x08, 0xca, 0x74, 0x19, 0x9c, 0xf2, 0x6e, 0xe6, 0xe0, 0x64, 0x09, 0x5a, 0x7d, 0xfb,
	0x6a, 0x44, 0x3e, 0xd1, 0xcf, 0xa1, 0x9e, 0x5d, 0x78, 0xa6, 0x3c, 0x4e, 0xd2, 0xb9, 0xb2, 0xf6,
	0xad, 0xfe, 0xc1, 0xf5, 0x07, 0x70, 0x06, 0x7e, 0x13, 0x6e, 0x4a, 0x4a, 0xa7, 0x94, 0xd4, 0x0a,
	0xb2, 0xcb, 0xb5, 0xea, 0xef, 0x5d, 0x03, 0x93, 0xcf, 0x75, 0x00, 0xd5, 0x03, 0xe2, 0x53, 0xdf,
	0x09, 0x0b, 0x6a, 0x31, 0x49, 0xbc, 0xb1, 0xab, 0x87, 0x1f, 0xbc, 0x7f, 0xfe, 0x64, 0xb7, 0x6b,
	0x8d, 0x89, 0x6e, 0xda, 0x98, 0xff, 0xab, 0xaf, 0x25, 0xba, 0x10, 0x9d, 0x92, 0x52, 0xdf, 0x52,
	0x9e, 0xa1, 0x15, 0x08, 0x2f, 0x26, 0x6d, 0xe0, 0xcd, 0x22, 0x93, 0xec, 0x0a, 0x47, 0x70, 0x4a,
	0x4c, 0x90, 0xc4, 0x22, 0x71, 0x25, 0xc3, 0x56, 0x24, 0x2b, 0xcf, 0x25, 0x82, 0x24, 0xad, 0x34,
	0xa7, 0x66, 0x59, 0x5a, 0x2a, 0x36, 0x65, 0x96, 0x67, 0x15, 0xa8, 0x4d, 0x99, 0xe5, 0x99, 0xd5,
	0x67, 0xca, 0x1f, 0xe6, 0xe0, 0x9d, 0xeb, 0xd4, 0x89, 0x29, 0x9f, 0x4c, 0x2d, 0xe2, 0xba, 0x25,
	0x6b, 0xf5, 0x4f, 0xdf, 0x64, 0x28, 0xe7, 0x4f, 0x87, 0xb2, 0x50, 0xad, 0xa5, 0xa4, 0x1e, 0x23,
	0xa7, 0xab, 0xd1, 0xea, 0xf7, 0x66, 0x60, 0xc4, 0x76, 0x70, 0xaa, 0x46, 0x2a, 0x6d, 0x07, 0xb3,
	0x8a, 0xb5, 0xea, 0xef, 0x5e, 0x89, 0x37, 0x7d, 0x43, 0x3c, 0xb3, 0xec, 0xcc, 0x1b, 0x42, 0xc8,
	0x7f, 0x64, 0xdd, 0x10, 0x89, 0x74, 0xc2, 0x97, 0xb0, 0x92, 0x8a, 0x80, 0xd3, 0x52, 0x28, 0xcf,
	0x06, 0xa4, 0xa5, 0x30, 0x2b, 0x8c, 0x1e, 0xc3, 0xaa, 0x2c, 0xa9, 0xa9, 0xa4, 0xdf, 0x48, 0xb2,
	0x33, 0xa5, 0xf5, 0x9d, 0xeb, 0xa0, 0xf2, 0xe9, 0x7e, 0x04, 0x95, 0x44, 0x2e, 0x5d, 0x51, 0xa5,
	0xc2, 0x92, 0x5c, 0xca, 0xfd, 0x99, 0x38, 0xb1, 0xb5, 0x92, 0x64, 0xe9, 0x95, 0xed, 0x19, 0x63,
	0x13, 0xef, 0x09, 0x69, 0x6b, 0x35, 0x2b, 0xe5, 0xcf, 0x56, 0x11, 0x67, 0x4b, 0x25, 0xab, 0x98,
	0x4a, 0x03, 0x4b, 0x56, 0x21, 0x49, 0xf8, 0x32, 0x27, 0x46, 0xc8, 0xc3, 0x2a, 0xb3, 0x86, 0x79,
	0xd9, 0x4e, 0x8c, 0x2c, 0x95, 0x6b, 0x82, 0x32, 0xfd, 0x2c, 0x90, 0x9e, 0x40, 0xfa, 0xc6, 0x90,
	0x9e, 0x40, 0xfe, 0xba, 0xa0, 0xbe, 0x45, 0x8d, 0x9a, 0x3c, 0x97, 0xaf, 0x3c, 0xba, 0x5e, 0xc6,
	0x5f, 0x6a, 0xd4, 0xae, 0x78, 0x5f, 0xf8, 0x39, 0x7b, 0x27, 0x92, 0x67, 0x8f, 0xd3, 0x37, 0xe5,
	0x95, 0xe9, 0xe9, 0xf4, 0x4d, 0x79, 0x8d, 0xc4, 0xf4, 0xcf, 0x60, 0x23, 0x33, 0x57, 0xac, 0xec,
	0xa6, 0x1f, 0xad, 0x67, 0xa7, 0xa2, 0xeb, 0x8f, 0xaf, 0x8d, 0x1f, 0x7b, 0x24, 0xd3, 0xc5, 0x58,
	0x69, 0x8f, 0x24, 0xb3, 0x50, 0x2c, 0xed, 0x91, 0x64, 0xd7, 0x75, 0x71, 0x15, 0x4b, 0x17, 0x62,
	0x49, 0x54, 0x2c, 0xa3, 0xf4, 0x4b, 0xa2, 0x62, 0x99, 0x55, 0x5d, 0x63, 0xe1, 0x31, 0x45, 0x28,
	0xa3, 0x52, 0x66, 0x93, 0x48, 0x28, 0xc5, 0xce, 0x75, 0x50, 0xf9, 0x74, 0x97, 0x50, 0xcb, 0xaa,
	0x2f, 0x52, 0xbe, 0x93, 0xb2, 0xa4, 0xb3, 0xab, 0xaf, 0xea, 0xbb, 0xd7, 0x45, 0x8f, 0x85, 0x27,
	0xb3, 0xba, 0x28, 0x2d, 0x3c, 0x57, 0xd5, 0x37, 0xa5, 0x85, 0xe7, 0xca, 0xb2, 0x25, 0xe5, 0xb7,
	0x72, 0xb0, 0x39, 0xa3, 0xb8, 0x48, 0xf9, 0x20, 0xed, 0xb7, 0x5f, 0x55, 0xe2, 0x54, 0xff, 0x7f,
	0x5f, 0x63, 0x44, 0xbc, 0x05, 0x99, 0x55, 0x22, 0xe9, 0x2d, 0xb8, 0xaa, 0x46, 0xa5, 0xfe, 0xf8,
	0xda, 0xf8, 0x71, 0x08, 0x9e, 0x7e, 0x8e, 0x51, 0x1e, 0x48, 0x2f, 0x83, 0xf4, 0x23, 0x4f, 0xfd,
	0xe1, 0x55, 0x68, 0x6c, 0x8a, 0xfa, 0xcd, 0xff, 0xfc, 0xcb, 0x7f, 0xe8, 0x2e, 0xc3, 0x92, 0xf8,
	0xe7, 0xa3, 0x9e, 0x7e, 0xf0, 0xeb, 0xbb, 0x43, 0x67, 0x64, 0xda, 0xc3, 0xdd, 0x0f, 0x9f, 0xf8,
	0xfe, 0x6e, 0xdf, 0x19, 0x3f, 0xc6, 0x3f, 0xea, 0xd4, 0x77, 0x46, 0x8f, 0xf9, 0x77, 0x70, 0x5e,
	0xe2, 0x0f, 0x4e, 0xbd, 0x5c, 0xc0, 0xfe, 0xef, 0xfe, 0x77, 0x00, 0x00, 0x00, 0xff, 0xff, 0x75,
	0x1c, 0xf4, 0xc8, 0xa3, 0x4a, 0x00, 0x00,
}
