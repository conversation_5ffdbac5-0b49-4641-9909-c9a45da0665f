// Code generated by protoc-gen-go. DO NOT EDIT.
// source: smash-egg/smash-egg.proto

package smash_egg // import "golang.52tt.com/protocol/services/smash-egg"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 砸蛋方式
type Source int32

const (
	Source_Manual Source = 0
	Source_AUTO   Source = 1
)

var Source_name = map[int32]string{
	0: "Manual",
	1: "AUTO",
}
var Source_value = map[string]int32{
	"Manual": 0,
	"AUTO":   1,
}

func (x Source) String() string {
	return proto.EnumName(Source_name, int32(x))
}
func (Source) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{0}
}

// 砸蛋状态
type Flag int32

const (
	Flag_NORMAL Flag = 0
	Flag_MORPH  Flag = 1
)

var Flag_name = map[int32]string{
	0: "NORMAL",
	1: "MORPH",
}
var Flag_value = map[string]int32{
	"NORMAL": 0,
	"MORPH":  1,
}

func (x Flag) String() string {
	return proto.EnumName(Flag_name, int32(x))
}
func (Flag) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{1}
}

// 砸蛋方式
type Mode int32

const (
	Mode_NORMAL_MODE Mode = 0
	Mode_GOLD_MODE   Mode = 1
)

var Mode_name = map[int32]string{
	0: "NORMAL_MODE",
	1: "GOLD_MODE",
}
var Mode_value = map[string]int32{
	"NORMAL_MODE": 0,
	"GOLD_MODE":   1,
}

func (x Mode) String() string {
	return proto.EnumName(Mode_name, int32(x))
}
func (Mode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{2}
}

type Status int32

const (
	Status_ENABLE  Status = 0
	Status_DISABLE Status = 1
)

var Status_name = map[int32]string{
	0: "ENABLE",
	1: "DISABLE",
}
var Status_value = map[string]int32{
	"ENABLE":  0,
	"DISABLE": 1,
}

func (x Status) String() string {
	return proto.EnumName(Status_name, int32(x))
}
func (Status) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{3}
}

// 平台
type Platform int32

const (
	Platform_Unknown_Platform Platform = 0
	Platform_Android          Platform = 1
	Platform_iOS              Platform = 2
)

var Platform_name = map[int32]string{
	0: "Unknown_Platform",
	1: "Android",
	2: "iOS",
}
var Platform_value = map[string]int32{
	"Unknown_Platform": 0,
	"Android":          1,
	"iOS":              2,
}

func (x Platform) String() string {
	return proto.EnumName(Platform_name, int32(x))
}
func (Platform) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{4}
}

// 应用
type App int32

const (
	App_Unknown_App App = 0
	App_TT          App = 1
	App_HUANYOU     App = 2
	App_ZAIYA       App = 3
)

var App_name = map[int32]string{
	0: "Unknown_App",
	1: "TT",
	2: "HUANYOU",
	3: "ZAIYA",
}
var App_value = map[string]int32{
	"Unknown_App": 0,
	"TT":          1,
	"HUANYOU":     2,
	"ZAIYA":       3,
}

func (x App) String() string {
	return proto.EnumName(App_name, int32(x))
}
func (App) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{5}
}

type CurThemeType int32

const (
	CurThemeType_CUR_THEME_TYPE_UNSPECIFIED CurThemeType = 0
	CurThemeType_CUR_THEME_TYPE_A           CurThemeType = 1
	CurThemeType_CUR_THEME_TYPE_B           CurThemeType = 2
)

var CurThemeType_name = map[int32]string{
	0: "CUR_THEME_TYPE_UNSPECIFIED",
	1: "CUR_THEME_TYPE_A",
	2: "CUR_THEME_TYPE_B",
}
var CurThemeType_value = map[string]int32{
	"CUR_THEME_TYPE_UNSPECIFIED": 0,
	"CUR_THEME_TYPE_A":           1,
	"CUR_THEME_TYPE_B":           2,
}

func (x CurThemeType) String() string {
	return proto.EnumName(CurThemeType_name, int32(x))
}
func (CurThemeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{6}
}

// 道具退还相关
type RefundStatus int32

const (
	RefundStatus_REFUND_STATUS_UNSPECIFIED  RefundStatus = 0
	RefundStatus_REFUND_STATUS_NOT_SUMMITED RefundStatus = 1
	RefundStatus_REFUND_STATUS_APPLYING     RefundStatus = 2
	RefundStatus_REFUND_STATUS_REFUNDING    RefundStatus = 3
	RefundStatus_REFUND_STATUS_REFUNDED     RefundStatus = 4
	RefundStatus_REFUND_STATUS_NOT_SHOW     RefundStatus = 5
)

var RefundStatus_name = map[int32]string{
	0: "REFUND_STATUS_UNSPECIFIED",
	1: "REFUND_STATUS_NOT_SUMMITED",
	2: "REFUND_STATUS_APPLYING",
	3: "REFUND_STATUS_REFUNDING",
	4: "REFUND_STATUS_REFUNDED",
	5: "REFUND_STATUS_NOT_SHOW",
}
var RefundStatus_value = map[string]int32{
	"REFUND_STATUS_UNSPECIFIED":  0,
	"REFUND_STATUS_NOT_SUMMITED": 1,
	"REFUND_STATUS_APPLYING":     2,
	"REFUND_STATUS_REFUNDING":    3,
	"REFUND_STATUS_REFUNDED":     4,
	"REFUND_STATUS_NOT_SHOW":     5,
}

func (x RefundStatus) String() string {
	return proto.EnumName(RefundStatus_name, int32(x))
}
func (RefundStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{7}
}

// 结算操作类型
type SettlementType int32

const (
	SettlementType_SETTLEMENT_TYPE_UNSPECIFIED SettlementType = 0
	SettlementType_SETTLEMENT_TYPE_FREEZE      SettlementType = 1
	SettlementType_SETTLEMENT_TYPE_COMMIT      SettlementType = 2
)

var SettlementType_name = map[int32]string{
	0: "SETTLEMENT_TYPE_UNSPECIFIED",
	1: "SETTLEMENT_TYPE_FREEZE",
	2: "SETTLEMENT_TYPE_COMMIT",
}
var SettlementType_value = map[string]int32{
	"SETTLEMENT_TYPE_UNSPECIFIED": 0,
	"SETTLEMENT_TYPE_FREEZE":      1,
	"SETTLEMENT_TYPE_COMMIT":      2,
}

func (x SettlementType) String() string {
	return proto.EnumName(SettlementType_name, int32(x))
}
func (SettlementType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{8}
}

// 消费记录
type ConsumeRecord struct {
	Id                   uint64   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Amount               uint32   `protobuf:"varint,3,opt,name=amount,proto3" json:"amount,omitempty"`
	Fee                  uint32   `protobuf:"varint,4,opt,name=fee,proto3" json:"fee,omitempty"`
	OrderId              string   `protobuf:"bytes,5,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	CreateTime           uint32   `protobuf:"varint,6,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConsumeRecord) Reset()         { *m = ConsumeRecord{} }
func (m *ConsumeRecord) String() string { return proto.CompactTextString(m) }
func (*ConsumeRecord) ProtoMessage()    {}
func (*ConsumeRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{0}
}
func (m *ConsumeRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConsumeRecord.Unmarshal(m, b)
}
func (m *ConsumeRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConsumeRecord.Marshal(b, m, deterministic)
}
func (dst *ConsumeRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConsumeRecord.Merge(dst, src)
}
func (m *ConsumeRecord) XXX_Size() int {
	return xxx_messageInfo_ConsumeRecord.Size(m)
}
func (m *ConsumeRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_ConsumeRecord.DiscardUnknown(m)
}

var xxx_messageInfo_ConsumeRecord proto.InternalMessageInfo

func (m *ConsumeRecord) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ConsumeRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ConsumeRecord) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *ConsumeRecord) GetFee() uint32 {
	if m != nil {
		return m.Fee
	}
	return 0
}

func (m *ConsumeRecord) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *ConsumeRecord) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

// 中奖记录
type WinningRecord struct {
	Id         uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Uid        uint32 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Source     Source `protobuf:"varint,3,opt,name=source,proto3,enum=ga.smash_egg.Source" json:"source,omitempty"`
	Flag       Flag   `protobuf:"varint,4,opt,name=flag,proto3,enum=ga.smash_egg.Flag" json:"flag,omitempty"`
	PackId     uint32 `protobuf:"varint,5,opt,name=pack_id,json=packId,proto3" json:"pack_id,omitempty"`
	PackWorth  uint32 `protobuf:"varint,6,opt,name=pack_worth,json=packWorth,proto3" json:"pack_worth,omitempty"`
	PackName   string `protobuf:"bytes,7,opt,name=pack_name,json=packName,proto3" json:"pack_name,omitempty"`
	PackPic    string `protobuf:"bytes,8,opt,name=pack_pic,json=packPic,proto3" json:"pack_pic,omitempty"`
	PackAmount uint32 `protobuf:"varint,9,opt,name=pack_amount,json=packAmount,proto3" json:"pack_amount,omitempty"`
	PackDesc   string `protobuf:"bytes,10,opt,name=pack_desc,json=packDesc,proto3" json:"pack_desc,omitempty"`
	CreateTime uint32 `protobuf:"varint,11,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	ChannelId  uint32 `protobuf:"varint,12,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	OrderId    string `protobuf:"bytes,13,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Mode       Mode   `protobuf:"varint,14,opt,name=mode,proto3,enum=ga.smash_egg.Mode" json:"mode,omitempty"`
	// 中奖光效
	LightEffectId        uint32   `protobuf:"varint,15,opt,name=light_effect_id,json=lightEffectId,proto3" json:"light_effect_id,omitempty"`
	LightEffectText      string   `protobuf:"bytes,16,opt,name=light_effect_text,json=lightEffectText,proto3" json:"light_effect_text,omitempty"`
	LightEffectUrl       string   `protobuf:"bytes,17,opt,name=light_effect_url,json=lightEffectUrl,proto3" json:"light_effect_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WinningRecord) Reset()         { *m = WinningRecord{} }
func (m *WinningRecord) String() string { return proto.CompactTextString(m) }
func (*WinningRecord) ProtoMessage()    {}
func (*WinningRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{1}
}
func (m *WinningRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WinningRecord.Unmarshal(m, b)
}
func (m *WinningRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WinningRecord.Marshal(b, m, deterministic)
}
func (dst *WinningRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WinningRecord.Merge(dst, src)
}
func (m *WinningRecord) XXX_Size() int {
	return xxx_messageInfo_WinningRecord.Size(m)
}
func (m *WinningRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_WinningRecord.DiscardUnknown(m)
}

var xxx_messageInfo_WinningRecord proto.InternalMessageInfo

func (m *WinningRecord) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *WinningRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *WinningRecord) GetSource() Source {
	if m != nil {
		return m.Source
	}
	return Source_Manual
}

func (m *WinningRecord) GetFlag() Flag {
	if m != nil {
		return m.Flag
	}
	return Flag_NORMAL
}

func (m *WinningRecord) GetPackId() uint32 {
	if m != nil {
		return m.PackId
	}
	return 0
}

func (m *WinningRecord) GetPackWorth() uint32 {
	if m != nil {
		return m.PackWorth
	}
	return 0
}

func (m *WinningRecord) GetPackName() string {
	if m != nil {
		return m.PackName
	}
	return ""
}

func (m *WinningRecord) GetPackPic() string {
	if m != nil {
		return m.PackPic
	}
	return ""
}

func (m *WinningRecord) GetPackAmount() uint32 {
	if m != nil {
		return m.PackAmount
	}
	return 0
}

func (m *WinningRecord) GetPackDesc() string {
	if m != nil {
		return m.PackDesc
	}
	return ""
}

func (m *WinningRecord) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *WinningRecord) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *WinningRecord) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *WinningRecord) GetMode() Mode {
	if m != nil {
		return m.Mode
	}
	return Mode_NORMAL_MODE
}

func (m *WinningRecord) GetLightEffectId() uint32 {
	if m != nil {
		return m.LightEffectId
	}
	return 0
}

func (m *WinningRecord) GetLightEffectText() string {
	if m != nil {
		return m.LightEffectText
	}
	return ""
}

func (m *WinningRecord) GetLightEffectUrl() string {
	if m != nil {
		return m.LightEffectUrl
	}
	return ""
}

type Config struct {
	MorphHits      uint32     `protobuf:"varint,1,opt,name=morph_hits,json=morphHits,proto3" json:"morph_hits,omitempty"`
	MorphDuration  uint32     `protobuf:"varint,2,opt,name=morph_duration,json=morphDuration,proto3" json:"morph_duration,omitempty"`
	DailyLimit     uint32     `protobuf:"varint,3,opt,name=daily_limit,json=dailyLimit,proto3" json:"daily_limit,omitempty"`
	LimitPlatform  []Platform `protobuf:"varint,4,rep,packed,name=limit_platform,json=limitPlatform,proto3,enum=ga.smash_egg.Platform" json:"limit_platform,omitempty"`
	LimitApp       []App      `protobuf:"varint,5,rep,packed,name=limit_app,json=limitApp,proto3,enum=ga.smash_egg.App" json:"limit_app,omitempty"`
	Status         uint32     `protobuf:"varint,6,opt,name=status,proto3" json:"status,omitempty"`
	DeficitLimit   uint32     `protobuf:"varint,7,opt,name=deficit_limit,json=deficitLimit,proto3" json:"deficit_limit,omitempty"`
	DeficitWarning uint32     `protobuf:"varint,8,opt,name=deficit_warning,json=deficitWarning,proto3" json:"deficit_warning,omitempty"`
	WealthLimit    uint64     `protobuf:"varint,9,opt,name=wealth_limit,json=wealthLimit,proto3" json:"wealth_limit,omitempty"`
	SpeedStep      uint32     `protobuf:"varint,10,opt,name=speed_step,json=speedStep,proto3" json:"speed_step,omitempty"`
	SpeedUp        uint32     `protobuf:"varint,11,opt,name=speed_up,json=speedUp,proto3" json:"speed_up,omitempty"`
	MaxSpeed       float32    `protobuf:"fixed32,12,opt,name=max_speed,json=maxSpeed,proto3" json:"max_speed,omitempty"`
	MaxRandN       uint32     `protobuf:"varint,13,opt,name=max_rand_n,json=maxRandN,proto3" json:"max_rand_n,omitempty"`
	CharmLimit     uint64     `protobuf:"varint,14,opt,name=charm_limit,json=charmLimit,proto3" json:"charm_limit,omitempty"`
	LevelLimit     uint32     `protobuf:"varint,15,opt,name=level_limit,json=levelLimit,proto3" json:"level_limit,omitempty"`
	OverallSpeed   uint32     `protobuf:"varint,16,opt,name=overall_speed,json=overallSpeed,proto3" json:"overall_speed,omitempty"`
	// 充值红钻包裹id
	RechargePackId       uint32   `protobuf:"varint,17,opt,name=recharge_pack_id,json=rechargePackId,proto3" json:"recharge_pack_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Config) Reset()         { *m = Config{} }
func (m *Config) String() string { return proto.CompactTextString(m) }
func (*Config) ProtoMessage()    {}
func (*Config) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{2}
}
func (m *Config) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Config.Unmarshal(m, b)
}
func (m *Config) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Config.Marshal(b, m, deterministic)
}
func (dst *Config) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Config.Merge(dst, src)
}
func (m *Config) XXX_Size() int {
	return xxx_messageInfo_Config.Size(m)
}
func (m *Config) XXX_DiscardUnknown() {
	xxx_messageInfo_Config.DiscardUnknown(m)
}

var xxx_messageInfo_Config proto.InternalMessageInfo

func (m *Config) GetMorphHits() uint32 {
	if m != nil {
		return m.MorphHits
	}
	return 0
}

func (m *Config) GetMorphDuration() uint32 {
	if m != nil {
		return m.MorphDuration
	}
	return 0
}

func (m *Config) GetDailyLimit() uint32 {
	if m != nil {
		return m.DailyLimit
	}
	return 0
}

func (m *Config) GetLimitPlatform() []Platform {
	if m != nil {
		return m.LimitPlatform
	}
	return nil
}

func (m *Config) GetLimitApp() []App {
	if m != nil {
		return m.LimitApp
	}
	return nil
}

func (m *Config) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *Config) GetDeficitLimit() uint32 {
	if m != nil {
		return m.DeficitLimit
	}
	return 0
}

func (m *Config) GetDeficitWarning() uint32 {
	if m != nil {
		return m.DeficitWarning
	}
	return 0
}

func (m *Config) GetWealthLimit() uint64 {
	if m != nil {
		return m.WealthLimit
	}
	return 0
}

func (m *Config) GetSpeedStep() uint32 {
	if m != nil {
		return m.SpeedStep
	}
	return 0
}

func (m *Config) GetSpeedUp() uint32 {
	if m != nil {
		return m.SpeedUp
	}
	return 0
}

func (m *Config) GetMaxSpeed() float32 {
	if m != nil {
		return m.MaxSpeed
	}
	return 0
}

func (m *Config) GetMaxRandN() uint32 {
	if m != nil {
		return m.MaxRandN
	}
	return 0
}

func (m *Config) GetCharmLimit() uint64 {
	if m != nil {
		return m.CharmLimit
	}
	return 0
}

func (m *Config) GetLevelLimit() uint32 {
	if m != nil {
		return m.LevelLimit
	}
	return 0
}

func (m *Config) GetOverallSpeed() uint32 {
	if m != nil {
		return m.OverallSpeed
	}
	return 0
}

func (m *Config) GetRechargePackId() uint32 {
	if m != nil {
		return m.RechargePackId
	}
	return 0
}

type Prize struct {
	Id                   uint64   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Flag                 Flag     `protobuf:"varint,2,opt,name=flag,proto3,enum=ga.smash_egg.Flag" json:"flag,omitempty"`
	Status               Status   `protobuf:"varint,3,opt,name=status,proto3,enum=ga.smash_egg.Status" json:"status,omitempty"`
	Weight               uint32   `protobuf:"varint,4,opt,name=weight,proto3" json:"weight,omitempty"`
	PackId               uint32   `protobuf:"varint,5,opt,name=pack_id,json=packId,proto3" json:"pack_id,omitempty"`
	PackWorth            uint32   `protobuf:"varint,6,opt,name=pack_worth,json=packWorth,proto3" json:"pack_worth,omitempty"`
	PackName             string   `protobuf:"bytes,7,opt,name=pack_name,json=packName,proto3" json:"pack_name,omitempty"`
	PackPic              string   `protobuf:"bytes,8,opt,name=pack_pic,json=packPic,proto3" json:"pack_pic,omitempty"`
	PackAmount           uint32   `protobuf:"varint,9,opt,name=pack_amount,json=packAmount,proto3" json:"pack_amount,omitempty"`
	PackDesc             string   `protobuf:"bytes,10,opt,name=pack_desc,json=packDesc,proto3" json:"pack_desc,omitempty"`
	CreateTime           uint32   `protobuf:"varint,11,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Mode                 Mode     `protobuf:"varint,12,opt,name=mode,proto3,enum=ga.smash_egg.Mode" json:"mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Prize) Reset()         { *m = Prize{} }
func (m *Prize) String() string { return proto.CompactTextString(m) }
func (*Prize) ProtoMessage()    {}
func (*Prize) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{3}
}
func (m *Prize) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Prize.Unmarshal(m, b)
}
func (m *Prize) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Prize.Marshal(b, m, deterministic)
}
func (dst *Prize) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Prize.Merge(dst, src)
}
func (m *Prize) XXX_Size() int {
	return xxx_messageInfo_Prize.Size(m)
}
func (m *Prize) XXX_DiscardUnknown() {
	xxx_messageInfo_Prize.DiscardUnknown(m)
}

var xxx_messageInfo_Prize proto.InternalMessageInfo

func (m *Prize) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Prize) GetFlag() Flag {
	if m != nil {
		return m.Flag
	}
	return Flag_NORMAL
}

func (m *Prize) GetStatus() Status {
	if m != nil {
		return m.Status
	}
	return Status_ENABLE
}

func (m *Prize) GetWeight() uint32 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *Prize) GetPackId() uint32 {
	if m != nil {
		return m.PackId
	}
	return 0
}

func (m *Prize) GetPackWorth() uint32 {
	if m != nil {
		return m.PackWorth
	}
	return 0
}

func (m *Prize) GetPackName() string {
	if m != nil {
		return m.PackName
	}
	return ""
}

func (m *Prize) GetPackPic() string {
	if m != nil {
		return m.PackPic
	}
	return ""
}

func (m *Prize) GetPackAmount() uint32 {
	if m != nil {
		return m.PackAmount
	}
	return 0
}

func (m *Prize) GetPackDesc() string {
	if m != nil {
		return m.PackDesc
	}
	return ""
}

func (m *Prize) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *Prize) GetMode() Mode {
	if m != nil {
		return m.Mode
	}
	return Mode_NORMAL_MODE
}

type GetConsumeRecordReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OrderId              string   `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Offset               uint64   `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	BeginTime            uint32   `protobuf:"varint,5,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetConsumeRecordReq) Reset()         { *m = GetConsumeRecordReq{} }
func (m *GetConsumeRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetConsumeRecordReq) ProtoMessage()    {}
func (*GetConsumeRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{4}
}
func (m *GetConsumeRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConsumeRecordReq.Unmarshal(m, b)
}
func (m *GetConsumeRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConsumeRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetConsumeRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConsumeRecordReq.Merge(dst, src)
}
func (m *GetConsumeRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetConsumeRecordReq.Size(m)
}
func (m *GetConsumeRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConsumeRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetConsumeRecordReq proto.InternalMessageInfo

func (m *GetConsumeRecordReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetConsumeRecordReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *GetConsumeRecordReq) GetOffset() uint64 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetConsumeRecordReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetConsumeRecordReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetConsumeRecordReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetConsumeRecordResp struct {
	ConsumeRecordList    []*ConsumeRecord `protobuf:"bytes,1,rep,name=consume_record_list,json=consumeRecordList,proto3" json:"consume_record_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetConsumeRecordResp) Reset()         { *m = GetConsumeRecordResp{} }
func (m *GetConsumeRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetConsumeRecordResp) ProtoMessage()    {}
func (*GetConsumeRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{5}
}
func (m *GetConsumeRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConsumeRecordResp.Unmarshal(m, b)
}
func (m *GetConsumeRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConsumeRecordResp.Marshal(b, m, deterministic)
}
func (dst *GetConsumeRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConsumeRecordResp.Merge(dst, src)
}
func (m *GetConsumeRecordResp) XXX_Size() int {
	return xxx_messageInfo_GetConsumeRecordResp.Size(m)
}
func (m *GetConsumeRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConsumeRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetConsumeRecordResp proto.InternalMessageInfo

func (m *GetConsumeRecordResp) GetConsumeRecordList() []*ConsumeRecord {
	if m != nil {
		return m.ConsumeRecordList
	}
	return nil
}

type GetWinningRecordReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Offset               uint64   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	BeginTime            uint32   `protobuf:"varint,4,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Rare                 bool     `protobuf:"varint,6,opt,name=rare,proto3" json:"rare,omitempty"`
	Page                 string   `protobuf:"bytes,7,opt,name=page,proto3" json:"page,omitempty"`
	Mode                 Mode     `protobuf:"varint,8,opt,name=mode,proto3,enum=ga.smash_egg.Mode" json:"mode,omitempty"`
	MorphFlag            Flag     `protobuf:"varint,9,opt,name=morph_flag,json=morphFlag,proto3,enum=ga.smash_egg.Flag" json:"morph_flag,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWinningRecordReq) Reset()         { *m = GetWinningRecordReq{} }
func (m *GetWinningRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetWinningRecordReq) ProtoMessage()    {}
func (*GetWinningRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{6}
}
func (m *GetWinningRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWinningRecordReq.Unmarshal(m, b)
}
func (m *GetWinningRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWinningRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetWinningRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWinningRecordReq.Merge(dst, src)
}
func (m *GetWinningRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetWinningRecordReq.Size(m)
}
func (m *GetWinningRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWinningRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetWinningRecordReq proto.InternalMessageInfo

func (m *GetWinningRecordReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetWinningRecordReq) GetOffset() uint64 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetWinningRecordReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetWinningRecordReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetWinningRecordReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetWinningRecordReq) GetRare() bool {
	if m != nil {
		return m.Rare
	}
	return false
}

func (m *GetWinningRecordReq) GetPage() string {
	if m != nil {
		return m.Page
	}
	return ""
}

func (m *GetWinningRecordReq) GetMode() Mode {
	if m != nil {
		return m.Mode
	}
	return Mode_NORMAL_MODE
}

func (m *GetWinningRecordReq) GetMorphFlag() Flag {
	if m != nil {
		return m.MorphFlag
	}
	return Flag_NORMAL
}

type GetWinningRecordResp struct {
	WinningRecordList    []*WinningRecord `protobuf:"bytes,1,rep,name=winning_record_list,json=winningRecordList,proto3" json:"winning_record_list,omitempty"`
	Page                 string           `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetWinningRecordResp) Reset()         { *m = GetWinningRecordResp{} }
func (m *GetWinningRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetWinningRecordResp) ProtoMessage()    {}
func (*GetWinningRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{7}
}
func (m *GetWinningRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWinningRecordResp.Unmarshal(m, b)
}
func (m *GetWinningRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWinningRecordResp.Marshal(b, m, deterministic)
}
func (dst *GetWinningRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWinningRecordResp.Merge(dst, src)
}
func (m *GetWinningRecordResp) XXX_Size() int {
	return xxx_messageInfo_GetWinningRecordResp.Size(m)
}
func (m *GetWinningRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWinningRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetWinningRecordResp proto.InternalMessageInfo

func (m *GetWinningRecordResp) GetWinningRecordList() []*WinningRecord {
	if m != nil {
		return m.WinningRecordList
	}
	return nil
}

func (m *GetWinningRecordResp) GetPage() string {
	if m != nil {
		return m.Page
	}
	return ""
}

type GuaranteedInfo struct {
	Mode                 Mode     `protobuf:"varint,1,opt,name=mode,proto3,enum=ga.smash_egg.Mode" json:"mode,omitempty"`
	MorphFlag            Flag     `protobuf:"varint,2,opt,name=morph_flag,json=morphFlag,proto3,enum=ga.smash_egg.Flag" json:"morph_flag,omitempty"`
	Speed                uint32   `protobuf:"varint,3,opt,name=speed,proto3" json:"speed,omitempty"`
	OverallSpeed         uint32   `protobuf:"varint,4,opt,name=overall_speed,json=overallSpeed,proto3" json:"overall_speed,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuaranteedInfo) Reset()         { *m = GuaranteedInfo{} }
func (m *GuaranteedInfo) String() string { return proto.CompactTextString(m) }
func (*GuaranteedInfo) ProtoMessage()    {}
func (*GuaranteedInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{8}
}
func (m *GuaranteedInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuaranteedInfo.Unmarshal(m, b)
}
func (m *GuaranteedInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuaranteedInfo.Marshal(b, m, deterministic)
}
func (dst *GuaranteedInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuaranteedInfo.Merge(dst, src)
}
func (m *GuaranteedInfo) XXX_Size() int {
	return xxx_messageInfo_GuaranteedInfo.Size(m)
}
func (m *GuaranteedInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GuaranteedInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GuaranteedInfo proto.InternalMessageInfo

func (m *GuaranteedInfo) GetMode() Mode {
	if m != nil {
		return m.Mode
	}
	return Mode_NORMAL_MODE
}

func (m *GuaranteedInfo) GetMorphFlag() Flag {
	if m != nil {
		return m.MorphFlag
	}
	return Flag_NORMAL
}

func (m *GuaranteedInfo) GetSpeed() uint32 {
	if m != nil {
		return m.Speed
	}
	return 0
}

func (m *GuaranteedInfo) GetOverallSpeed() uint32 {
	if m != nil {
		return m.OverallSpeed
	}
	return 0
}

type GetSmashStatusReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSmashStatusReq) Reset()         { *m = GetSmashStatusReq{} }
func (m *GetSmashStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetSmashStatusReq) ProtoMessage()    {}
func (*GetSmashStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{9}
}
func (m *GetSmashStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSmashStatusReq.Unmarshal(m, b)
}
func (m *GetSmashStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSmashStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetSmashStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSmashStatusReq.Merge(dst, src)
}
func (m *GetSmashStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetSmashStatusReq.Size(m)
}
func (m *GetSmashStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSmashStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSmashStatusReq proto.InternalMessageInfo

func (m *GetSmashStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetSmashStatusResp struct {
	CurrentHits          uint32            `protobuf:"varint,1,opt,name=current_hits,json=currentHits,proto3" json:"current_hits,omitempty"`
	MyRemainHits         uint32            `protobuf:"varint,2,opt,name=my_remain_hits,json=myRemainHits,proto3" json:"my_remain_hits,omitempty"`
	MyTodayHits          uint32            `protobuf:"varint,3,opt,name=my_today_hits,json=myTodayHits,proto3" json:"my_today_hits,omitempty"`
	MorphFlag            Flag              `protobuf:"varint,4,opt,name=morph_flag,json=morphFlag,proto3,enum=ga.smash_egg.Flag" json:"morph_flag,omitempty"`
	MorphEndTime         uint32            `protobuf:"varint,5,opt,name=morph_end_time,json=morphEndTime,proto3" json:"morph_end_time,omitempty"`
	Speed                uint32            `protobuf:"varint,6,opt,name=speed,proto3" json:"speed,omitempty"`
	OverallSpeed         uint32            `protobuf:"varint,7,opt,name=overall_speed,json=overallSpeed,proto3" json:"overall_speed,omitempty"`
	GuaranteedInfoList   []*GuaranteedInfo `protobuf:"bytes,8,rep,name=guaranteed_info_list,json=guaranteedInfoList,proto3" json:"guaranteed_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetSmashStatusResp) Reset()         { *m = GetSmashStatusResp{} }
func (m *GetSmashStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetSmashStatusResp) ProtoMessage()    {}
func (*GetSmashStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{10}
}
func (m *GetSmashStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSmashStatusResp.Unmarshal(m, b)
}
func (m *GetSmashStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSmashStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetSmashStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSmashStatusResp.Merge(dst, src)
}
func (m *GetSmashStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetSmashStatusResp.Size(m)
}
func (m *GetSmashStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSmashStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSmashStatusResp proto.InternalMessageInfo

func (m *GetSmashStatusResp) GetCurrentHits() uint32 {
	if m != nil {
		return m.CurrentHits
	}
	return 0
}

func (m *GetSmashStatusResp) GetMyRemainHits() uint32 {
	if m != nil {
		return m.MyRemainHits
	}
	return 0
}

func (m *GetSmashStatusResp) GetMyTodayHits() uint32 {
	if m != nil {
		return m.MyTodayHits
	}
	return 0
}

func (m *GetSmashStatusResp) GetMorphFlag() Flag {
	if m != nil {
		return m.MorphFlag
	}
	return Flag_NORMAL
}

func (m *GetSmashStatusResp) GetMorphEndTime() uint32 {
	if m != nil {
		return m.MorphEndTime
	}
	return 0
}

func (m *GetSmashStatusResp) GetSpeed() uint32 {
	if m != nil {
		return m.Speed
	}
	return 0
}

func (m *GetSmashStatusResp) GetOverallSpeed() uint32 {
	if m != nil {
		return m.OverallSpeed
	}
	return 0
}

func (m *GetSmashStatusResp) GetGuaranteedInfoList() []*GuaranteedInfo {
	if m != nil {
		return m.GuaranteedInfoList
	}
	return nil
}

type RechargeReq struct {
	Record               *ConsumeRecord `protobuf:"bytes,1,opt,name=record,proto3" json:"record,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *RechargeReq) Reset()         { *m = RechargeReq{} }
func (m *RechargeReq) String() string { return proto.CompactTextString(m) }
func (*RechargeReq) ProtoMessage()    {}
func (*RechargeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{11}
}
func (m *RechargeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RechargeReq.Unmarshal(m, b)
}
func (m *RechargeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RechargeReq.Marshal(b, m, deterministic)
}
func (dst *RechargeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RechargeReq.Merge(dst, src)
}
func (m *RechargeReq) XXX_Size() int {
	return xxx_messageInfo_RechargeReq.Size(m)
}
func (m *RechargeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RechargeReq.DiscardUnknown(m)
}

var xxx_messageInfo_RechargeReq proto.InternalMessageInfo

func (m *RechargeReq) GetRecord() *ConsumeRecord {
	if m != nil {
		return m.Record
	}
	return nil
}

type RechargeResp struct {
	Amount               uint32   `protobuf:"varint,1,opt,name=amount,proto3" json:"amount,omitempty"`
	OrderId              string   `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RechargeResp) Reset()         { *m = RechargeResp{} }
func (m *RechargeResp) String() string { return proto.CompactTextString(m) }
func (*RechargeResp) ProtoMessage()    {}
func (*RechargeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{12}
}
func (m *RechargeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RechargeResp.Unmarshal(m, b)
}
func (m *RechargeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RechargeResp.Marshal(b, m, deterministic)
}
func (dst *RechargeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RechargeResp.Merge(dst, src)
}
func (m *RechargeResp) XXX_Size() int {
	return xxx_messageInfo_RechargeResp.Size(m)
}
func (m *RechargeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RechargeResp.DiscardUnknown(m)
}

var xxx_messageInfo_RechargeResp proto.InternalMessageInfo

func (m *RechargeResp) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *RechargeResp) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type SmashReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Amount               uint32   `protobuf:"varint,2,opt,name=amount,proto3" json:"amount,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelType          uint32   `protobuf:"varint,4,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	Platform             Platform `protobuf:"varint,5,opt,name=platform,proto3,enum=ga.smash_egg.Platform" json:"platform,omitempty"`
	Source               Source   `protobuf:"varint,6,opt,name=source,proto3,enum=ga.smash_egg.Source" json:"source,omitempty"`
	Mode                 Mode     `protobuf:"varint,7,opt,name=mode,proto3,enum=ga.smash_egg.Mode" json:"mode,omitempty"`
	MorphFlag            Flag     `protobuf:"varint,8,opt,name=morph_flag,json=morphFlag,proto3,enum=ga.smash_egg.Flag" json:"morph_flag,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SmashReq) Reset()         { *m = SmashReq{} }
func (m *SmashReq) String() string { return proto.CompactTextString(m) }
func (*SmashReq) ProtoMessage()    {}
func (*SmashReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{13}
}
func (m *SmashReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SmashReq.Unmarshal(m, b)
}
func (m *SmashReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SmashReq.Marshal(b, m, deterministic)
}
func (dst *SmashReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SmashReq.Merge(dst, src)
}
func (m *SmashReq) XXX_Size() int {
	return xxx_messageInfo_SmashReq.Size(m)
}
func (m *SmashReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SmashReq.DiscardUnknown(m)
}

var xxx_messageInfo_SmashReq proto.InternalMessageInfo

func (m *SmashReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SmashReq) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *SmashReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SmashReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *SmashReq) GetPlatform() Platform {
	if m != nil {
		return m.Platform
	}
	return Platform_Unknown_Platform
}

func (m *SmashReq) GetSource() Source {
	if m != nil {
		return m.Source
	}
	return Source_Manual
}

func (m *SmashReq) GetMode() Mode {
	if m != nil {
		return m.Mode
	}
	return Mode_NORMAL_MODE
}

func (m *SmashReq) GetMorphFlag() Flag {
	if m != nil {
		return m.MorphFlag
	}
	return Flag_NORMAL
}

type SmashResp struct {
	RemainChance         uint32           `protobuf:"varint,1,opt,name=remain_chance,json=remainChance,proto3" json:"remain_chance,omitempty"`
	WinningRecordList    []*WinningRecord `protobuf:"bytes,2,rep,name=winning_record_list,json=winningRecordList,proto3" json:"winning_record_list,omitempty"`
	CurrentHits          uint32           `protobuf:"varint,3,opt,name=current_hits,json=currentHits,proto3" json:"current_hits,omitempty"`
	Speed                uint32           `protobuf:"varint,4,opt,name=speed,proto3" json:"speed,omitempty"`
	OverallSpeed         uint32           `protobuf:"varint,5,opt,name=overall_speed,json=overallSpeed,proto3" json:"overall_speed,omitempty"`
	IsBingo              bool             `protobuf:"varint,6,opt,name=isBingo,proto3" json:"isBingo,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *SmashResp) Reset()         { *m = SmashResp{} }
func (m *SmashResp) String() string { return proto.CompactTextString(m) }
func (*SmashResp) ProtoMessage()    {}
func (*SmashResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{14}
}
func (m *SmashResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SmashResp.Unmarshal(m, b)
}
func (m *SmashResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SmashResp.Marshal(b, m, deterministic)
}
func (dst *SmashResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SmashResp.Merge(dst, src)
}
func (m *SmashResp) XXX_Size() int {
	return xxx_messageInfo_SmashResp.Size(m)
}
func (m *SmashResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SmashResp.DiscardUnknown(m)
}

var xxx_messageInfo_SmashResp proto.InternalMessageInfo

func (m *SmashResp) GetRemainChance() uint32 {
	if m != nil {
		return m.RemainChance
	}
	return 0
}

func (m *SmashResp) GetWinningRecordList() []*WinningRecord {
	if m != nil {
		return m.WinningRecordList
	}
	return nil
}

func (m *SmashResp) GetCurrentHits() uint32 {
	if m != nil {
		return m.CurrentHits
	}
	return 0
}

func (m *SmashResp) GetSpeed() uint32 {
	if m != nil {
		return m.Speed
	}
	return 0
}

func (m *SmashResp) GetOverallSpeed() uint32 {
	if m != nil {
		return m.OverallSpeed
	}
	return 0
}

func (m *SmashResp) GetIsBingo() bool {
	if m != nil {
		return m.IsBingo
	}
	return false
}

type GetSmashConfigReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSmashConfigReq) Reset()         { *m = GetSmashConfigReq{} }
func (m *GetSmashConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetSmashConfigReq) ProtoMessage()    {}
func (*GetSmashConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{15}
}
func (m *GetSmashConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSmashConfigReq.Unmarshal(m, b)
}
func (m *GetSmashConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSmashConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetSmashConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSmashConfigReq.Merge(dst, src)
}
func (m *GetSmashConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetSmashConfigReq.Size(m)
}
func (m *GetSmashConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSmashConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSmashConfigReq proto.InternalMessageInfo

func (m *GetSmashConfigReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetSmashConfigResp struct {
	Config               *Config  `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	Accessible           bool     `protobuf:"varint,2,opt,name=accessible,proto3" json:"accessible,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSmashConfigResp) Reset()         { *m = GetSmashConfigResp{} }
func (m *GetSmashConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetSmashConfigResp) ProtoMessage()    {}
func (*GetSmashConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{16}
}
func (m *GetSmashConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSmashConfigResp.Unmarshal(m, b)
}
func (m *GetSmashConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSmashConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetSmashConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSmashConfigResp.Merge(dst, src)
}
func (m *GetSmashConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetSmashConfigResp.Size(m)
}
func (m *GetSmashConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSmashConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSmashConfigResp proto.InternalMessageInfo

func (m *GetSmashConfigResp) GetConfig() *Config {
	if m != nil {
		return m.Config
	}
	return nil
}

func (m *GetSmashConfigResp) GetAccessible() bool {
	if m != nil {
		return m.Accessible
	}
	return false
}

type SetSmashConfigReq struct {
	Config               *Config  `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetSmashConfigReq) Reset()         { *m = SetSmashConfigReq{} }
func (m *SetSmashConfigReq) String() string { return proto.CompactTextString(m) }
func (*SetSmashConfigReq) ProtoMessage()    {}
func (*SetSmashConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{17}
}
func (m *SetSmashConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetSmashConfigReq.Unmarshal(m, b)
}
func (m *SetSmashConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetSmashConfigReq.Marshal(b, m, deterministic)
}
func (dst *SetSmashConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetSmashConfigReq.Merge(dst, src)
}
func (m *SetSmashConfigReq) XXX_Size() int {
	return xxx_messageInfo_SetSmashConfigReq.Size(m)
}
func (m *SetSmashConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetSmashConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetSmashConfigReq proto.InternalMessageInfo

func (m *SetSmashConfigReq) GetConfig() *Config {
	if m != nil {
		return m.Config
	}
	return nil
}

type SetSmashConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetSmashConfigResp) Reset()         { *m = SetSmashConfigResp{} }
func (m *SetSmashConfigResp) String() string { return proto.CompactTextString(m) }
func (*SetSmashConfigResp) ProtoMessage()    {}
func (*SetSmashConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{18}
}
func (m *SetSmashConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetSmashConfigResp.Unmarshal(m, b)
}
func (m *SetSmashConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetSmashConfigResp.Marshal(b, m, deterministic)
}
func (dst *SetSmashConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetSmashConfigResp.Merge(dst, src)
}
func (m *SetSmashConfigResp) XXX_Size() int {
	return xxx_messageInfo_SetSmashConfigResp.Size(m)
}
func (m *SetSmashConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetSmashConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetSmashConfigResp proto.InternalMessageInfo

type GetPrizePoolReq struct {
	Flag                 Flag     `protobuf:"varint,1,opt,name=flag,proto3,enum=ga.smash_egg.Flag" json:"flag,omitempty"`
	Mode                 Mode     `protobuf:"varint,2,opt,name=mode,proto3,enum=ga.smash_egg.Mode" json:"mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPrizePoolReq) Reset()         { *m = GetPrizePoolReq{} }
func (m *GetPrizePoolReq) String() string { return proto.CompactTextString(m) }
func (*GetPrizePoolReq) ProtoMessage()    {}
func (*GetPrizePoolReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{19}
}
func (m *GetPrizePoolReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPrizePoolReq.Unmarshal(m, b)
}
func (m *GetPrizePoolReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPrizePoolReq.Marshal(b, m, deterministic)
}
func (dst *GetPrizePoolReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPrizePoolReq.Merge(dst, src)
}
func (m *GetPrizePoolReq) XXX_Size() int {
	return xxx_messageInfo_GetPrizePoolReq.Size(m)
}
func (m *GetPrizePoolReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPrizePoolReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPrizePoolReq proto.InternalMessageInfo

func (m *GetPrizePoolReq) GetFlag() Flag {
	if m != nil {
		return m.Flag
	}
	return Flag_NORMAL
}

func (m *GetPrizePoolReq) GetMode() Mode {
	if m != nil {
		return m.Mode
	}
	return Mode_NORMAL_MODE
}

type GetPrizePoolResp struct {
	PrizeList            []*Prize `protobuf:"bytes,1,rep,name=prize_list,json=prizeList,proto3" json:"prize_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPrizePoolResp) Reset()         { *m = GetPrizePoolResp{} }
func (m *GetPrizePoolResp) String() string { return proto.CompactTextString(m) }
func (*GetPrizePoolResp) ProtoMessage()    {}
func (*GetPrizePoolResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{20}
}
func (m *GetPrizePoolResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPrizePoolResp.Unmarshal(m, b)
}
func (m *GetPrizePoolResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPrizePoolResp.Marshal(b, m, deterministic)
}
func (dst *GetPrizePoolResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPrizePoolResp.Merge(dst, src)
}
func (m *GetPrizePoolResp) XXX_Size() int {
	return xxx_messageInfo_GetPrizePoolResp.Size(m)
}
func (m *GetPrizePoolResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPrizePoolResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPrizePoolResp proto.InternalMessageInfo

func (m *GetPrizePoolResp) GetPrizeList() []*Prize {
	if m != nil {
		return m.PrizeList
	}
	return nil
}

// 获取未生效奖池
type GetPrizePoolTmpReq struct {
	Flag                 Flag     `protobuf:"varint,1,opt,name=flag,proto3,enum=ga.smash_egg.Flag" json:"flag,omitempty"`
	Mode                 Mode     `protobuf:"varint,2,opt,name=mode,proto3,enum=ga.smash_egg.Mode" json:"mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPrizePoolTmpReq) Reset()         { *m = GetPrizePoolTmpReq{} }
func (m *GetPrizePoolTmpReq) String() string { return proto.CompactTextString(m) }
func (*GetPrizePoolTmpReq) ProtoMessage()    {}
func (*GetPrizePoolTmpReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{21}
}
func (m *GetPrizePoolTmpReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPrizePoolTmpReq.Unmarshal(m, b)
}
func (m *GetPrizePoolTmpReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPrizePoolTmpReq.Marshal(b, m, deterministic)
}
func (dst *GetPrizePoolTmpReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPrizePoolTmpReq.Merge(dst, src)
}
func (m *GetPrizePoolTmpReq) XXX_Size() int {
	return xxx_messageInfo_GetPrizePoolTmpReq.Size(m)
}
func (m *GetPrizePoolTmpReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPrizePoolTmpReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPrizePoolTmpReq proto.InternalMessageInfo

func (m *GetPrizePoolTmpReq) GetFlag() Flag {
	if m != nil {
		return m.Flag
	}
	return Flag_NORMAL
}

func (m *GetPrizePoolTmpReq) GetMode() Mode {
	if m != nil {
		return m.Mode
	}
	return Mode_NORMAL_MODE
}

type GetPrizePoolTmpResp struct {
	PrizeList            []*Prize `protobuf:"bytes,1,rep,name=prize_list,json=prizeList,proto3" json:"prize_list,omitempty"`
	EffectTime           uint32   `protobuf:"varint,2,opt,name=effect_time,json=effectTime,proto3" json:"effect_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPrizePoolTmpResp) Reset()         { *m = GetPrizePoolTmpResp{} }
func (m *GetPrizePoolTmpResp) String() string { return proto.CompactTextString(m) }
func (*GetPrizePoolTmpResp) ProtoMessage()    {}
func (*GetPrizePoolTmpResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{22}
}
func (m *GetPrizePoolTmpResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPrizePoolTmpResp.Unmarshal(m, b)
}
func (m *GetPrizePoolTmpResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPrizePoolTmpResp.Marshal(b, m, deterministic)
}
func (dst *GetPrizePoolTmpResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPrizePoolTmpResp.Merge(dst, src)
}
func (m *GetPrizePoolTmpResp) XXX_Size() int {
	return xxx_messageInfo_GetPrizePoolTmpResp.Size(m)
}
func (m *GetPrizePoolTmpResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPrizePoolTmpResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPrizePoolTmpResp proto.InternalMessageInfo

func (m *GetPrizePoolTmpResp) GetPrizeList() []*Prize {
	if m != nil {
		return m.PrizeList
	}
	return nil
}

func (m *GetPrizePoolTmpResp) GetEffectTime() uint32 {
	if m != nil {
		return m.EffectTime
	}
	return 0
}

type SetPrizePoolReq struct {
	Flag                 Flag     `protobuf:"varint,1,opt,name=flag,proto3,enum=ga.smash_egg.Flag" json:"flag,omitempty"`
	PrizeList            []*Prize `protobuf:"bytes,2,rep,name=prize_list,json=prizeList,proto3" json:"prize_list,omitempty"`
	Mode                 Mode     `protobuf:"varint,3,opt,name=mode,proto3,enum=ga.smash_egg.Mode" json:"mode,omitempty"`
	EffectTime           uint32   `protobuf:"varint,4,opt,name=effect_time,json=effectTime,proto3" json:"effect_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPrizePoolReq) Reset()         { *m = SetPrizePoolReq{} }
func (m *SetPrizePoolReq) String() string { return proto.CompactTextString(m) }
func (*SetPrizePoolReq) ProtoMessage()    {}
func (*SetPrizePoolReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{23}
}
func (m *SetPrizePoolReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPrizePoolReq.Unmarshal(m, b)
}
func (m *SetPrizePoolReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPrizePoolReq.Marshal(b, m, deterministic)
}
func (dst *SetPrizePoolReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPrizePoolReq.Merge(dst, src)
}
func (m *SetPrizePoolReq) XXX_Size() int {
	return xxx_messageInfo_SetPrizePoolReq.Size(m)
}
func (m *SetPrizePoolReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPrizePoolReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetPrizePoolReq proto.InternalMessageInfo

func (m *SetPrizePoolReq) GetFlag() Flag {
	if m != nil {
		return m.Flag
	}
	return Flag_NORMAL
}

func (m *SetPrizePoolReq) GetPrizeList() []*Prize {
	if m != nil {
		return m.PrizeList
	}
	return nil
}

func (m *SetPrizePoolReq) GetMode() Mode {
	if m != nil {
		return m.Mode
	}
	return Mode_NORMAL_MODE
}

func (m *SetPrizePoolReq) GetEffectTime() uint32 {
	if m != nil {
		return m.EffectTime
	}
	return 0
}

type SetPrizePoolResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPrizePoolResp) Reset()         { *m = SetPrizePoolResp{} }
func (m *SetPrizePoolResp) String() string { return proto.CompactTextString(m) }
func (*SetPrizePoolResp) ProtoMessage()    {}
func (*SetPrizePoolResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{24}
}
func (m *SetPrizePoolResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPrizePoolResp.Unmarshal(m, b)
}
func (m *SetPrizePoolResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPrizePoolResp.Marshal(b, m, deterministic)
}
func (dst *SetPrizePoolResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPrizePoolResp.Merge(dst, src)
}
func (m *SetPrizePoolResp) XXX_Size() int {
	return xxx_messageInfo_SetPrizePoolResp.Size(m)
}
func (m *SetPrizePoolResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPrizePoolResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetPrizePoolResp proto.InternalMessageInfo

type DelTmpPrizePoolReq struct {
	Flag                 Flag     `protobuf:"varint,1,opt,name=flag,proto3,enum=ga.smash_egg.Flag" json:"flag,omitempty"`
	Mode                 Mode     `protobuf:"varint,2,opt,name=mode,proto3,enum=ga.smash_egg.Mode" json:"mode,omitempty"`
	EffectTime           uint32   `protobuf:"varint,3,opt,name=effect_time,json=effectTime,proto3" json:"effect_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelTmpPrizePoolReq) Reset()         { *m = DelTmpPrizePoolReq{} }
func (m *DelTmpPrizePoolReq) String() string { return proto.CompactTextString(m) }
func (*DelTmpPrizePoolReq) ProtoMessage()    {}
func (*DelTmpPrizePoolReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{25}
}
func (m *DelTmpPrizePoolReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelTmpPrizePoolReq.Unmarshal(m, b)
}
func (m *DelTmpPrizePoolReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelTmpPrizePoolReq.Marshal(b, m, deterministic)
}
func (dst *DelTmpPrizePoolReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelTmpPrizePoolReq.Merge(dst, src)
}
func (m *DelTmpPrizePoolReq) XXX_Size() int {
	return xxx_messageInfo_DelTmpPrizePoolReq.Size(m)
}
func (m *DelTmpPrizePoolReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelTmpPrizePoolReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelTmpPrizePoolReq proto.InternalMessageInfo

func (m *DelTmpPrizePoolReq) GetFlag() Flag {
	if m != nil {
		return m.Flag
	}
	return Flag_NORMAL
}

func (m *DelTmpPrizePoolReq) GetMode() Mode {
	if m != nil {
		return m.Mode
	}
	return Mode_NORMAL_MODE
}

func (m *DelTmpPrizePoolReq) GetEffectTime() uint32 {
	if m != nil {
		return m.EffectTime
	}
	return 0
}

type DelTmpPrizePoolResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelTmpPrizePoolResp) Reset()         { *m = DelTmpPrizePoolResp{} }
func (m *DelTmpPrizePoolResp) String() string { return proto.CompactTextString(m) }
func (*DelTmpPrizePoolResp) ProtoMessage()    {}
func (*DelTmpPrizePoolResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{26}
}
func (m *DelTmpPrizePoolResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelTmpPrizePoolResp.Unmarshal(m, b)
}
func (m *DelTmpPrizePoolResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelTmpPrizePoolResp.Marshal(b, m, deterministic)
}
func (dst *DelTmpPrizePoolResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelTmpPrizePoolResp.Merge(dst, src)
}
func (m *DelTmpPrizePoolResp) XXX_Size() int {
	return xxx_messageInfo_DelTmpPrizePoolResp.Size(m)
}
func (m *DelTmpPrizePoolResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelTmpPrizePoolResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelTmpPrizePoolResp proto.InternalMessageInfo

type SimulateWithPrizePoolReq struct {
	SmashProfit          uint32   `protobuf:"varint,1,opt,name=smash_profit,json=smashProfit,proto3" json:"smash_profit,omitempty"`
	SmashTimes           uint32   `protobuf:"varint,2,opt,name=smash_times,json=smashTimes,proto3" json:"smash_times,omitempty"`
	Flag                 Flag     `protobuf:"varint,3,opt,name=flag,proto3,enum=ga.smash_egg.Flag" json:"flag,omitempty"`
	PrizeList            []*Prize `protobuf:"bytes,4,rep,name=prize_list,json=prizeList,proto3" json:"prize_list,omitempty"`
	Mode                 Mode     `protobuf:"varint,5,opt,name=mode,proto3,enum=ga.smash_egg.Mode" json:"mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SimulateWithPrizePoolReq) Reset()         { *m = SimulateWithPrizePoolReq{} }
func (m *SimulateWithPrizePoolReq) String() string { return proto.CompactTextString(m) }
func (*SimulateWithPrizePoolReq) ProtoMessage()    {}
func (*SimulateWithPrizePoolReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{27}
}
func (m *SimulateWithPrizePoolReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SimulateWithPrizePoolReq.Unmarshal(m, b)
}
func (m *SimulateWithPrizePoolReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SimulateWithPrizePoolReq.Marshal(b, m, deterministic)
}
func (dst *SimulateWithPrizePoolReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SimulateWithPrizePoolReq.Merge(dst, src)
}
func (m *SimulateWithPrizePoolReq) XXX_Size() int {
	return xxx_messageInfo_SimulateWithPrizePoolReq.Size(m)
}
func (m *SimulateWithPrizePoolReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SimulateWithPrizePoolReq.DiscardUnknown(m)
}

var xxx_messageInfo_SimulateWithPrizePoolReq proto.InternalMessageInfo

func (m *SimulateWithPrizePoolReq) GetSmashProfit() uint32 {
	if m != nil {
		return m.SmashProfit
	}
	return 0
}

func (m *SimulateWithPrizePoolReq) GetSmashTimes() uint32 {
	if m != nil {
		return m.SmashTimes
	}
	return 0
}

func (m *SimulateWithPrizePoolReq) GetFlag() Flag {
	if m != nil {
		return m.Flag
	}
	return Flag_NORMAL
}

func (m *SimulateWithPrizePoolReq) GetPrizeList() []*Prize {
	if m != nil {
		return m.PrizeList
	}
	return nil
}

func (m *SimulateWithPrizePoolReq) GetMode() Mode {
	if m != nil {
		return m.Mode
	}
	return Mode_NORMAL_MODE
}

type SimulateWithPrizePoolResp struct {
	ExpectProfit         int32    `protobuf:"varint,1,opt,name=expect_profit,json=expectProfit,proto3" json:"expect_profit,omitempty"`
	AverageProfit        int32    `protobuf:"varint,2,opt,name=average_profit,json=averageProfit,proto3" json:"average_profit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SimulateWithPrizePoolResp) Reset()         { *m = SimulateWithPrizePoolResp{} }
func (m *SimulateWithPrizePoolResp) String() string { return proto.CompactTextString(m) }
func (*SimulateWithPrizePoolResp) ProtoMessage()    {}
func (*SimulateWithPrizePoolResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{28}
}
func (m *SimulateWithPrizePoolResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SimulateWithPrizePoolResp.Unmarshal(m, b)
}
func (m *SimulateWithPrizePoolResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SimulateWithPrizePoolResp.Marshal(b, m, deterministic)
}
func (dst *SimulateWithPrizePoolResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SimulateWithPrizePoolResp.Merge(dst, src)
}
func (m *SimulateWithPrizePoolResp) XXX_Size() int {
	return xxx_messageInfo_SimulateWithPrizePoolResp.Size(m)
}
func (m *SimulateWithPrizePoolResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SimulateWithPrizePoolResp.DiscardUnknown(m)
}

var xxx_messageInfo_SimulateWithPrizePoolResp proto.InternalMessageInfo

func (m *SimulateWithPrizePoolResp) GetExpectProfit() int32 {
	if m != nil {
		return m.ExpectProfit
	}
	return 0
}

func (m *SimulateWithPrizePoolResp) GetAverageProfit() int32 {
	if m != nil {
		return m.AverageProfit
	}
	return 0
}

type CheckWhitelistReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckWhitelistReq) Reset()         { *m = CheckWhitelistReq{} }
func (m *CheckWhitelistReq) String() string { return proto.CompactTextString(m) }
func (*CheckWhitelistReq) ProtoMessage()    {}
func (*CheckWhitelistReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{29}
}
func (m *CheckWhitelistReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckWhitelistReq.Unmarshal(m, b)
}
func (m *CheckWhitelistReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckWhitelistReq.Marshal(b, m, deterministic)
}
func (dst *CheckWhitelistReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckWhitelistReq.Merge(dst, src)
}
func (m *CheckWhitelistReq) XXX_Size() int {
	return xxx_messageInfo_CheckWhitelistReq.Size(m)
}
func (m *CheckWhitelistReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckWhitelistReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckWhitelistReq proto.InternalMessageInfo

func (m *CheckWhitelistReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CheckWhitelistResp struct {
	Exist                bool     `protobuf:"varint,1,opt,name=exist,proto3" json:"exist,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckWhitelistResp) Reset()         { *m = CheckWhitelistResp{} }
func (m *CheckWhitelistResp) String() string { return proto.CompactTextString(m) }
func (*CheckWhitelistResp) ProtoMessage()    {}
func (*CheckWhitelistResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{30}
}
func (m *CheckWhitelistResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckWhitelistResp.Unmarshal(m, b)
}
func (m *CheckWhitelistResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckWhitelistResp.Marshal(b, m, deterministic)
}
func (dst *CheckWhitelistResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckWhitelistResp.Merge(dst, src)
}
func (m *CheckWhitelistResp) XXX_Size() int {
	return xxx_messageInfo_CheckWhitelistResp.Size(m)
}
func (m *CheckWhitelistResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckWhitelistResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckWhitelistResp proto.InternalMessageInfo

func (m *CheckWhitelistResp) GetExist() bool {
	if m != nil {
		return m.Exist
	}
	return false
}

type CheckTesterReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckTesterReq) Reset()         { *m = CheckTesterReq{} }
func (m *CheckTesterReq) String() string { return proto.CompactTextString(m) }
func (*CheckTesterReq) ProtoMessage()    {}
func (*CheckTesterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{31}
}
func (m *CheckTesterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckTesterReq.Unmarshal(m, b)
}
func (m *CheckTesterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckTesterReq.Marshal(b, m, deterministic)
}
func (dst *CheckTesterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckTesterReq.Merge(dst, src)
}
func (m *CheckTesterReq) XXX_Size() int {
	return xxx_messageInfo_CheckTesterReq.Size(m)
}
func (m *CheckTesterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckTesterReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckTesterReq proto.InternalMessageInfo

func (m *CheckTesterReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CheckTesterResp struct {
	Exist                bool     `protobuf:"varint,1,opt,name=exist,proto3" json:"exist,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckTesterResp) Reset()         { *m = CheckTesterResp{} }
func (m *CheckTesterResp) String() string { return proto.CompactTextString(m) }
func (*CheckTesterResp) ProtoMessage()    {}
func (*CheckTesterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{32}
}
func (m *CheckTesterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckTesterResp.Unmarshal(m, b)
}
func (m *CheckTesterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckTesterResp.Marshal(b, m, deterministic)
}
func (dst *CheckTesterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckTesterResp.Merge(dst, src)
}
func (m *CheckTesterResp) XXX_Size() int {
	return xxx_messageInfo_CheckTesterResp.Size(m)
}
func (m *CheckTesterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckTesterResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckTesterResp proto.InternalMessageInfo

func (m *CheckTesterResp) GetExist() bool {
	if m != nil {
		return m.Exist
	}
	return false
}

type GetSmashEggExemptValueReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSmashEggExemptValueReq) Reset()         { *m = GetSmashEggExemptValueReq{} }
func (m *GetSmashEggExemptValueReq) String() string { return proto.CompactTextString(m) }
func (*GetSmashEggExemptValueReq) ProtoMessage()    {}
func (*GetSmashEggExemptValueReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{33}
}
func (m *GetSmashEggExemptValueReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSmashEggExemptValueReq.Unmarshal(m, b)
}
func (m *GetSmashEggExemptValueReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSmashEggExemptValueReq.Marshal(b, m, deterministic)
}
func (dst *GetSmashEggExemptValueReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSmashEggExemptValueReq.Merge(dst, src)
}
func (m *GetSmashEggExemptValueReq) XXX_Size() int {
	return xxx_messageInfo_GetSmashEggExemptValueReq.Size(m)
}
func (m *GetSmashEggExemptValueReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSmashEggExemptValueReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSmashEggExemptValueReq proto.InternalMessageInfo

func (m *GetSmashEggExemptValueReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetSmashEggExemptValueResp struct {
	SmashFlag            bool     `protobuf:"varint,1,opt,name=smash_flag,json=smashFlag,proto3" json:"smash_flag,omitempty"`
	RemainChance         uint32   `protobuf:"varint,2,opt,name=remain_chance,json=remainChance,proto3" json:"remain_chance,omitempty"`
	LuckValue            uint32   `protobuf:"varint,3,opt,name=luck_value,json=luckValue,proto3" json:"luck_value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSmashEggExemptValueResp) Reset()         { *m = GetSmashEggExemptValueResp{} }
func (m *GetSmashEggExemptValueResp) String() string { return proto.CompactTextString(m) }
func (*GetSmashEggExemptValueResp) ProtoMessage()    {}
func (*GetSmashEggExemptValueResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{34}
}
func (m *GetSmashEggExemptValueResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSmashEggExemptValueResp.Unmarshal(m, b)
}
func (m *GetSmashEggExemptValueResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSmashEggExemptValueResp.Marshal(b, m, deterministic)
}
func (dst *GetSmashEggExemptValueResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSmashEggExemptValueResp.Merge(dst, src)
}
func (m *GetSmashEggExemptValueResp) XXX_Size() int {
	return xxx_messageInfo_GetSmashEggExemptValueResp.Size(m)
}
func (m *GetSmashEggExemptValueResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSmashEggExemptValueResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSmashEggExemptValueResp proto.InternalMessageInfo

func (m *GetSmashEggExemptValueResp) GetSmashFlag() bool {
	if m != nil {
		return m.SmashFlag
	}
	return false
}

func (m *GetSmashEggExemptValueResp) GetRemainChance() uint32 {
	if m != nil {
		return m.RemainChance
	}
	return 0
}

func (m *GetSmashEggExemptValueResp) GetLuckValue() uint32 {
	if m != nil {
		return m.LuckValue
	}
	return 0
}

type SmashActivityConfig struct {
	ActivityName       string `protobuf:"bytes,1,opt,name=activity_name,json=activityName,proto3" json:"activity_name,omitempty"`
	BeginTime          int64  `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime            int64  `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	SmashPropId        uint32 `protobuf:"varint,4,opt,name=smash_prop_id,json=smashPropId,proto3" json:"smash_prop_id,omitempty"`
	PropDuration       uint32 `protobuf:"varint,5,opt,name=prop_duration,json=propDuration,proto3" json:"prop_duration,omitempty"`
	PropGiftPackId     uint32 `protobuf:"varint,6,opt,name=prop_gift_pack_id,json=propGiftPackId,proto3" json:"prop_gift_pack_id,omitempty"`
	BingoPackId        uint32 `protobuf:"varint,7,opt,name=bingo_pack_id,json=bingoPackId,proto3" json:"bingo_pack_id,omitempty"`
	VisionId           uint32 `protobuf:"varint,8,opt,name=vision_id,json=visionId,proto3" json:"vision_id,omitempty"`
	RiskId             uint32 `protobuf:"varint,9,opt,name=risk_id,json=riskId,proto3" json:"risk_id,omitempty"`
	RiskSecret         string `protobuf:"bytes,10,opt,name=risk_secret,json=riskSecret,proto3" json:"risk_secret,omitempty"`
	BingoBreakingNewId uint32 `protobuf:"varint,11,opt,name=bingo_breaking_new_id,json=bingoBreakingNewId,proto3" json:"bingo_breaking_new_id,omitempty"`
	TbeanAppId         string `protobuf:"bytes,12,opt,name=tbean_app_id,json=tbeanAppId,proto3" json:"tbean_app_id,omitempty"`
	// 规则资源后缀
	ActivityRules        string   `protobuf:"bytes,13,opt,name=activity_rules,json=activityRules,proto3" json:"activity_rules,omitempty"`
	RegularPrizePool     string   `protobuf:"bytes,14,opt,name=regular_prize_pool,json=regularPrizePool,proto3" json:"regular_prize_pool,omitempty"`
	GoldPrizePool        string   `protobuf:"bytes,15,opt,name=gold_prize_pool,json=goldPrizePool,proto3" json:"gold_prize_pool,omitempty"`
	MorphRules           string   `protobuf:"bytes,16,opt,name=morph_rules,json=morphRules,proto3" json:"morph_rules,omitempty"`
	LuckPointRules       string   `protobuf:"bytes,17,opt,name=luck_point_rules,json=luckPointRules,proto3" json:"luck_point_rules,omitempty"`
	GoldLuckPointRules   string   `protobuf:"bytes,18,opt,name=gold_luck_point_rules,json=goldLuckPointRules,proto3" json:"gold_luck_point_rules,omitempty"`
	CompositeRules       string   `protobuf:"bytes,19,opt,name=composite_rules,json=compositeRules,proto3" json:"composite_rules,omitempty"`
	DarkCompositeRules   string   `protobuf:"bytes,20,opt,name=dark_composite_rules,json=darkCompositeRules,proto3" json:"dark_composite_rules,omitempty"`
	DarkEnergyStones     string   `protobuf:"bytes,21,opt,name=dark_energy_stones,json=darkEnergyStones,proto3" json:"dark_energy_stones,omitempty"`
	GoldActivityRules    string   `protobuf:"bytes,22,opt,name=gold_activity_rules,json=goldActivityRules,proto3" json:"gold_activity_rules,omitempty"`
	UpdateTime           int64    `protobuf:"varint,23,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	PropPrice            uint32   `protobuf:"varint,24,opt,name=prop_price,json=propPrice,proto3" json:"prop_price,omitempty"`
	MorphPrizePool       string   `protobuf:"bytes,25,opt,name=morph_prize_pool,json=morphPrizePool,proto3" json:"morph_prize_pool,omitempty"`
	MorphLuckPointRules  string   `protobuf:"bytes,26,opt,name=morph_luck_point_rules,json=morphLuckPointRules,proto3" json:"morph_luck_point_rules,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SmashActivityConfig) Reset()         { *m = SmashActivityConfig{} }
func (m *SmashActivityConfig) String() string { return proto.CompactTextString(m) }
func (*SmashActivityConfig) ProtoMessage()    {}
func (*SmashActivityConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{35}
}
func (m *SmashActivityConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SmashActivityConfig.Unmarshal(m, b)
}
func (m *SmashActivityConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SmashActivityConfig.Marshal(b, m, deterministic)
}
func (dst *SmashActivityConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SmashActivityConfig.Merge(dst, src)
}
func (m *SmashActivityConfig) XXX_Size() int {
	return xxx_messageInfo_SmashActivityConfig.Size(m)
}
func (m *SmashActivityConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_SmashActivityConfig.DiscardUnknown(m)
}

var xxx_messageInfo_SmashActivityConfig proto.InternalMessageInfo

func (m *SmashActivityConfig) GetActivityName() string {
	if m != nil {
		return m.ActivityName
	}
	return ""
}

func (m *SmashActivityConfig) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *SmashActivityConfig) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *SmashActivityConfig) GetSmashPropId() uint32 {
	if m != nil {
		return m.SmashPropId
	}
	return 0
}

func (m *SmashActivityConfig) GetPropDuration() uint32 {
	if m != nil {
		return m.PropDuration
	}
	return 0
}

func (m *SmashActivityConfig) GetPropGiftPackId() uint32 {
	if m != nil {
		return m.PropGiftPackId
	}
	return 0
}

func (m *SmashActivityConfig) GetBingoPackId() uint32 {
	if m != nil {
		return m.BingoPackId
	}
	return 0
}

func (m *SmashActivityConfig) GetVisionId() uint32 {
	if m != nil {
		return m.VisionId
	}
	return 0
}

func (m *SmashActivityConfig) GetRiskId() uint32 {
	if m != nil {
		return m.RiskId
	}
	return 0
}

func (m *SmashActivityConfig) GetRiskSecret() string {
	if m != nil {
		return m.RiskSecret
	}
	return ""
}

func (m *SmashActivityConfig) GetBingoBreakingNewId() uint32 {
	if m != nil {
		return m.BingoBreakingNewId
	}
	return 0
}

func (m *SmashActivityConfig) GetTbeanAppId() string {
	if m != nil {
		return m.TbeanAppId
	}
	return ""
}

func (m *SmashActivityConfig) GetActivityRules() string {
	if m != nil {
		return m.ActivityRules
	}
	return ""
}

func (m *SmashActivityConfig) GetRegularPrizePool() string {
	if m != nil {
		return m.RegularPrizePool
	}
	return ""
}

func (m *SmashActivityConfig) GetGoldPrizePool() string {
	if m != nil {
		return m.GoldPrizePool
	}
	return ""
}

func (m *SmashActivityConfig) GetMorphRules() string {
	if m != nil {
		return m.MorphRules
	}
	return ""
}

func (m *SmashActivityConfig) GetLuckPointRules() string {
	if m != nil {
		return m.LuckPointRules
	}
	return ""
}

func (m *SmashActivityConfig) GetGoldLuckPointRules() string {
	if m != nil {
		return m.GoldLuckPointRules
	}
	return ""
}

func (m *SmashActivityConfig) GetCompositeRules() string {
	if m != nil {
		return m.CompositeRules
	}
	return ""
}

func (m *SmashActivityConfig) GetDarkCompositeRules() string {
	if m != nil {
		return m.DarkCompositeRules
	}
	return ""
}

func (m *SmashActivityConfig) GetDarkEnergyStones() string {
	if m != nil {
		return m.DarkEnergyStones
	}
	return ""
}

func (m *SmashActivityConfig) GetGoldActivityRules() string {
	if m != nil {
		return m.GoldActivityRules
	}
	return ""
}

func (m *SmashActivityConfig) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *SmashActivityConfig) GetPropPrice() uint32 {
	if m != nil {
		return m.PropPrice
	}
	return 0
}

func (m *SmashActivityConfig) GetMorphPrizePool() string {
	if m != nil {
		return m.MorphPrizePool
	}
	return ""
}

func (m *SmashActivityConfig) GetMorphLuckPointRules() string {
	if m != nil {
		return m.MorphLuckPointRules
	}
	return ""
}

// 覆盖更新玩法基本信息配置,不存在则新增
type UpdateSmashEggActivityConfigReq struct {
	Config               *SmashActivityConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *UpdateSmashEggActivityConfigReq) Reset()         { *m = UpdateSmashEggActivityConfigReq{} }
func (m *UpdateSmashEggActivityConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpdateSmashEggActivityConfigReq) ProtoMessage()    {}
func (*UpdateSmashEggActivityConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{36}
}
func (m *UpdateSmashEggActivityConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateSmashEggActivityConfigReq.Unmarshal(m, b)
}
func (m *UpdateSmashEggActivityConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateSmashEggActivityConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpdateSmashEggActivityConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateSmashEggActivityConfigReq.Merge(dst, src)
}
func (m *UpdateSmashEggActivityConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpdateSmashEggActivityConfigReq.Size(m)
}
func (m *UpdateSmashEggActivityConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateSmashEggActivityConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateSmashEggActivityConfigReq proto.InternalMessageInfo

func (m *UpdateSmashEggActivityConfigReq) GetConfig() *SmashActivityConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type UpdateSmashEggActivityConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateSmashEggActivityConfigResp) Reset()         { *m = UpdateSmashEggActivityConfigResp{} }
func (m *UpdateSmashEggActivityConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpdateSmashEggActivityConfigResp) ProtoMessage()    {}
func (*UpdateSmashEggActivityConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{37}
}
func (m *UpdateSmashEggActivityConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateSmashEggActivityConfigResp.Unmarshal(m, b)
}
func (m *UpdateSmashEggActivityConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateSmashEggActivityConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpdateSmashEggActivityConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateSmashEggActivityConfigResp.Merge(dst, src)
}
func (m *UpdateSmashEggActivityConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpdateSmashEggActivityConfigResp.Size(m)
}
func (m *UpdateSmashEggActivityConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateSmashEggActivityConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateSmashEggActivityConfigResp proto.InternalMessageInfo

// 各模式配置
type ModeCfg struct {
	Mode                   Mode     `protobuf:"varint,1,opt,name=mode,proto3,enum=ga.smash_egg.Mode" json:"mode,omitempty"`
	MorphFlag              Flag     `protobuf:"varint,2,opt,name=morph_flag,json=morphFlag,proto3,enum=ga.smash_egg.Flag" json:"morph_flag,omitempty"`
	RechargePropNumOptions []uint32 `protobuf:"varint,3,rep,packed,name=recharge_prop_num_options,json=rechargePropNumOptions,proto3" json:"recharge_prop_num_options,omitempty"`
	SmashCostPropNum       uint32   `protobuf:"varint,4,opt,name=smash_cost_prop_num,json=smashCostPropNum,proto3" json:"smash_cost_prop_num,omitempty"`
	GuaranteedPackId       uint32   `protobuf:"varint,5,opt,name=guaranteed_pack_id,json=guaranteedPackId,proto3" json:"guaranteed_pack_id,omitempty"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *ModeCfg) Reset()         { *m = ModeCfg{} }
func (m *ModeCfg) String() string { return proto.CompactTextString(m) }
func (*ModeCfg) ProtoMessage()    {}
func (*ModeCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{38}
}
func (m *ModeCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModeCfg.Unmarshal(m, b)
}
func (m *ModeCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModeCfg.Marshal(b, m, deterministic)
}
func (dst *ModeCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModeCfg.Merge(dst, src)
}
func (m *ModeCfg) XXX_Size() int {
	return xxx_messageInfo_ModeCfg.Size(m)
}
func (m *ModeCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_ModeCfg.DiscardUnknown(m)
}

var xxx_messageInfo_ModeCfg proto.InternalMessageInfo

func (m *ModeCfg) GetMode() Mode {
	if m != nil {
		return m.Mode
	}
	return Mode_NORMAL_MODE
}

func (m *ModeCfg) GetMorphFlag() Flag {
	if m != nil {
		return m.MorphFlag
	}
	return Flag_NORMAL
}

func (m *ModeCfg) GetRechargePropNumOptions() []uint32 {
	if m != nil {
		return m.RechargePropNumOptions
	}
	return nil
}

func (m *ModeCfg) GetSmashCostPropNum() uint32 {
	if m != nil {
		return m.SmashCostPropNum
	}
	return 0
}

func (m *ModeCfg) GetGuaranteedPackId() uint32 {
	if m != nil {
		return m.GuaranteedPackId
	}
	return 0
}

// 获取玩法基本信息配置
type GetSmashEggActivityConfigReq struct {
	WithRulesConf        bool     `protobuf:"varint,1,opt,name=with_rules_conf,json=withRulesConf,proto3" json:"with_rules_conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSmashEggActivityConfigReq) Reset()         { *m = GetSmashEggActivityConfigReq{} }
func (m *GetSmashEggActivityConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetSmashEggActivityConfigReq) ProtoMessage()    {}
func (*GetSmashEggActivityConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{39}
}
func (m *GetSmashEggActivityConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSmashEggActivityConfigReq.Unmarshal(m, b)
}
func (m *GetSmashEggActivityConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSmashEggActivityConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetSmashEggActivityConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSmashEggActivityConfigReq.Merge(dst, src)
}
func (m *GetSmashEggActivityConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetSmashEggActivityConfigReq.Size(m)
}
func (m *GetSmashEggActivityConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSmashEggActivityConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSmashEggActivityConfigReq proto.InternalMessageInfo

func (m *GetSmashEggActivityConfigReq) GetWithRulesConf() bool {
	if m != nil {
		return m.WithRulesConf
	}
	return false
}

type GetSmashEggActivityConfigResp struct {
	Config               *SmashActivityConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	ModeCfgList          []*ModeCfg           `protobuf:"bytes,2,rep,name=mode_cfg_list,json=modeCfgList,proto3" json:"mode_cfg_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetSmashEggActivityConfigResp) Reset()         { *m = GetSmashEggActivityConfigResp{} }
func (m *GetSmashEggActivityConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetSmashEggActivityConfigResp) ProtoMessage()    {}
func (*GetSmashEggActivityConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{40}
}
func (m *GetSmashEggActivityConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSmashEggActivityConfigResp.Unmarshal(m, b)
}
func (m *GetSmashEggActivityConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSmashEggActivityConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetSmashEggActivityConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSmashEggActivityConfigResp.Merge(dst, src)
}
func (m *GetSmashEggActivityConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetSmashEggActivityConfigResp.Size(m)
}
func (m *GetSmashEggActivityConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSmashEggActivityConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSmashEggActivityConfigResp proto.InternalMessageInfo

func (m *GetSmashEggActivityConfigResp) GetConfig() *SmashActivityConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

func (m *GetSmashEggActivityConfigResp) GetModeCfgList() []*ModeCfg {
	if m != nil {
		return m.ModeCfgList
	}
	return nil
}

type GetSmashEggActivityConfWithCacheReq struct {
	WithRulesConf        bool     `protobuf:"varint,1,opt,name=with_rules_conf,json=withRulesConf,proto3" json:"with_rules_conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSmashEggActivityConfWithCacheReq) Reset()         { *m = GetSmashEggActivityConfWithCacheReq{} }
func (m *GetSmashEggActivityConfWithCacheReq) String() string { return proto.CompactTextString(m) }
func (*GetSmashEggActivityConfWithCacheReq) ProtoMessage()    {}
func (*GetSmashEggActivityConfWithCacheReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{41}
}
func (m *GetSmashEggActivityConfWithCacheReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSmashEggActivityConfWithCacheReq.Unmarshal(m, b)
}
func (m *GetSmashEggActivityConfWithCacheReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSmashEggActivityConfWithCacheReq.Marshal(b, m, deterministic)
}
func (dst *GetSmashEggActivityConfWithCacheReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSmashEggActivityConfWithCacheReq.Merge(dst, src)
}
func (m *GetSmashEggActivityConfWithCacheReq) XXX_Size() int {
	return xxx_messageInfo_GetSmashEggActivityConfWithCacheReq.Size(m)
}
func (m *GetSmashEggActivityConfWithCacheReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSmashEggActivityConfWithCacheReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSmashEggActivityConfWithCacheReq proto.InternalMessageInfo

func (m *GetSmashEggActivityConfWithCacheReq) GetWithRulesConf() bool {
	if m != nil {
		return m.WithRulesConf
	}
	return false
}

type GetSmashEggActivityConfWithCacheResp struct {
	Config               *SmashActivityConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	ModeCfgList          []*ModeCfg           `protobuf:"bytes,2,rep,name=mode_cfg_list,json=modeCfgList,proto3" json:"mode_cfg_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetSmashEggActivityConfWithCacheResp) Reset()         { *m = GetSmashEggActivityConfWithCacheResp{} }
func (m *GetSmashEggActivityConfWithCacheResp) String() string { return proto.CompactTextString(m) }
func (*GetSmashEggActivityConfWithCacheResp) ProtoMessage()    {}
func (*GetSmashEggActivityConfWithCacheResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{42}
}
func (m *GetSmashEggActivityConfWithCacheResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSmashEggActivityConfWithCacheResp.Unmarshal(m, b)
}
func (m *GetSmashEggActivityConfWithCacheResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSmashEggActivityConfWithCacheResp.Marshal(b, m, deterministic)
}
func (dst *GetSmashEggActivityConfWithCacheResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSmashEggActivityConfWithCacheResp.Merge(dst, src)
}
func (m *GetSmashEggActivityConfWithCacheResp) XXX_Size() int {
	return xxx_messageInfo_GetSmashEggActivityConfWithCacheResp.Size(m)
}
func (m *GetSmashEggActivityConfWithCacheResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSmashEggActivityConfWithCacheResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSmashEggActivityConfWithCacheResp proto.InternalMessageInfo

func (m *GetSmashEggActivityConfWithCacheResp) GetConfig() *SmashActivityConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

func (m *GetSmashEggActivityConfWithCacheResp) GetModeCfgList() []*ModeCfg {
	if m != nil {
		return m.ModeCfgList
	}
	return nil
}

// 检查玩法基本信息配置
type CheckSmashEggActivityConfigReq struct {
	Config               *SmashActivityConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *CheckSmashEggActivityConfigReq) Reset()         { *m = CheckSmashEggActivityConfigReq{} }
func (m *CheckSmashEggActivityConfigReq) String() string { return proto.CompactTextString(m) }
func (*CheckSmashEggActivityConfigReq) ProtoMessage()    {}
func (*CheckSmashEggActivityConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{43}
}
func (m *CheckSmashEggActivityConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckSmashEggActivityConfigReq.Unmarshal(m, b)
}
func (m *CheckSmashEggActivityConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckSmashEggActivityConfigReq.Marshal(b, m, deterministic)
}
func (dst *CheckSmashEggActivityConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckSmashEggActivityConfigReq.Merge(dst, src)
}
func (m *CheckSmashEggActivityConfigReq) XXX_Size() int {
	return xxx_messageInfo_CheckSmashEggActivityConfigReq.Size(m)
}
func (m *CheckSmashEggActivityConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckSmashEggActivityConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckSmashEggActivityConfigReq proto.InternalMessageInfo

func (m *CheckSmashEggActivityConfigReq) GetConfig() *SmashActivityConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

// 玩法信息校验不通过时，报错返回
type CheckSmashEggActivityConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckSmashEggActivityConfigResp) Reset()         { *m = CheckSmashEggActivityConfigResp{} }
func (m *CheckSmashEggActivityConfigResp) String() string { return proto.CompactTextString(m) }
func (*CheckSmashEggActivityConfigResp) ProtoMessage()    {}
func (*CheckSmashEggActivityConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{44}
}
func (m *CheckSmashEggActivityConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckSmashEggActivityConfigResp.Unmarshal(m, b)
}
func (m *CheckSmashEggActivityConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckSmashEggActivityConfigResp.Marshal(b, m, deterministic)
}
func (dst *CheckSmashEggActivityConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckSmashEggActivityConfigResp.Merge(dst, src)
}
func (m *CheckSmashEggActivityConfigResp) XXX_Size() int {
	return xxx_messageInfo_CheckSmashEggActivityConfigResp.Size(m)
}
func (m *CheckSmashEggActivityConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckSmashEggActivityConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckSmashEggActivityConfigResp proto.InternalMessageInfo

// 删除玩法基本信息配置
type DelSmashEggActivityConfigReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelSmashEggActivityConfigReq) Reset()         { *m = DelSmashEggActivityConfigReq{} }
func (m *DelSmashEggActivityConfigReq) String() string { return proto.CompactTextString(m) }
func (*DelSmashEggActivityConfigReq) ProtoMessage()    {}
func (*DelSmashEggActivityConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{45}
}
func (m *DelSmashEggActivityConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelSmashEggActivityConfigReq.Unmarshal(m, b)
}
func (m *DelSmashEggActivityConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelSmashEggActivityConfigReq.Marshal(b, m, deterministic)
}
func (dst *DelSmashEggActivityConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelSmashEggActivityConfigReq.Merge(dst, src)
}
func (m *DelSmashEggActivityConfigReq) XXX_Size() int {
	return xxx_messageInfo_DelSmashEggActivityConfigReq.Size(m)
}
func (m *DelSmashEggActivityConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelSmashEggActivityConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelSmashEggActivityConfigReq proto.InternalMessageInfo

type DelSmashEggActivityConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelSmashEggActivityConfigResp) Reset()         { *m = DelSmashEggActivityConfigResp{} }
func (m *DelSmashEggActivityConfigResp) String() string { return proto.CompactTextString(m) }
func (*DelSmashEggActivityConfigResp) ProtoMessage()    {}
func (*DelSmashEggActivityConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{46}
}
func (m *DelSmashEggActivityConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelSmashEggActivityConfigResp.Unmarshal(m, b)
}
func (m *DelSmashEggActivityConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelSmashEggActivityConfigResp.Marshal(b, m, deterministic)
}
func (dst *DelSmashEggActivityConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelSmashEggActivityConfigResp.Merge(dst, src)
}
func (m *DelSmashEggActivityConfigResp) XXX_Size() int {
	return xxx_messageInfo_DelSmashEggActivityConfigResp.Size(m)
}
func (m *DelSmashEggActivityConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelSmashEggActivityConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelSmashEggActivityConfigResp proto.InternalMessageInfo

type SmashLightEffect struct {
	ConfId               uint32   `protobuf:"varint,1,opt,name=conf_id,json=confId,proto3" json:"conf_id,omitempty"`
	Level                uint32   `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`
	EffectRes            string   `protobuf:"bytes,3,opt,name=effect_res,json=effectRes,proto3" json:"effect_res,omitempty"`
	Text                 string   `protobuf:"bytes,4,opt,name=text,proto3" json:"text,omitempty"`
	OpUser               string   `protobuf:"bytes,5,opt,name=op_user,json=opUser,proto3" json:"op_user,omitempty"`
	OpRemind             string   `protobuf:"bytes,6,opt,name=op_remind,json=opRemind,proto3" json:"op_remind,omitempty"`
	UpdateTime           int64    `protobuf:"varint,7,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SmashLightEffect) Reset()         { *m = SmashLightEffect{} }
func (m *SmashLightEffect) String() string { return proto.CompactTextString(m) }
func (*SmashLightEffect) ProtoMessage()    {}
func (*SmashLightEffect) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{47}
}
func (m *SmashLightEffect) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SmashLightEffect.Unmarshal(m, b)
}
func (m *SmashLightEffect) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SmashLightEffect.Marshal(b, m, deterministic)
}
func (dst *SmashLightEffect) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SmashLightEffect.Merge(dst, src)
}
func (m *SmashLightEffect) XXX_Size() int {
	return xxx_messageInfo_SmashLightEffect.Size(m)
}
func (m *SmashLightEffect) XXX_DiscardUnknown() {
	xxx_messageInfo_SmashLightEffect.DiscardUnknown(m)
}

var xxx_messageInfo_SmashLightEffect proto.InternalMessageInfo

func (m *SmashLightEffect) GetConfId() uint32 {
	if m != nil {
		return m.ConfId
	}
	return 0
}

func (m *SmashLightEffect) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *SmashLightEffect) GetEffectRes() string {
	if m != nil {
		return m.EffectRes
	}
	return ""
}

func (m *SmashLightEffect) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *SmashLightEffect) GetOpUser() string {
	if m != nil {
		return m.OpUser
	}
	return ""
}

func (m *SmashLightEffect) GetOpRemind() string {
	if m != nil {
		return m.OpRemind
	}
	return ""
}

func (m *SmashLightEffect) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

// 中奖光效管理
type AddSmashLightEffectsV2Req struct {
	Conf                 *SmashLightEffect `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *AddSmashLightEffectsV2Req) Reset()         { *m = AddSmashLightEffectsV2Req{} }
func (m *AddSmashLightEffectsV2Req) String() string { return proto.CompactTextString(m) }
func (*AddSmashLightEffectsV2Req) ProtoMessage()    {}
func (*AddSmashLightEffectsV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{48}
}
func (m *AddSmashLightEffectsV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddSmashLightEffectsV2Req.Unmarshal(m, b)
}
func (m *AddSmashLightEffectsV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddSmashLightEffectsV2Req.Marshal(b, m, deterministic)
}
func (dst *AddSmashLightEffectsV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddSmashLightEffectsV2Req.Merge(dst, src)
}
func (m *AddSmashLightEffectsV2Req) XXX_Size() int {
	return xxx_messageInfo_AddSmashLightEffectsV2Req.Size(m)
}
func (m *AddSmashLightEffectsV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_AddSmashLightEffectsV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_AddSmashLightEffectsV2Req proto.InternalMessageInfo

func (m *AddSmashLightEffectsV2Req) GetConf() *SmashLightEffect {
	if m != nil {
		return m.Conf
	}
	return nil
}

type AddSmashLightEffectsV2Resp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddSmashLightEffectsV2Resp) Reset()         { *m = AddSmashLightEffectsV2Resp{} }
func (m *AddSmashLightEffectsV2Resp) String() string { return proto.CompactTextString(m) }
func (*AddSmashLightEffectsV2Resp) ProtoMessage()    {}
func (*AddSmashLightEffectsV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{49}
}
func (m *AddSmashLightEffectsV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddSmashLightEffectsV2Resp.Unmarshal(m, b)
}
func (m *AddSmashLightEffectsV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddSmashLightEffectsV2Resp.Marshal(b, m, deterministic)
}
func (dst *AddSmashLightEffectsV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddSmashLightEffectsV2Resp.Merge(dst, src)
}
func (m *AddSmashLightEffectsV2Resp) XXX_Size() int {
	return xxx_messageInfo_AddSmashLightEffectsV2Resp.Size(m)
}
func (m *AddSmashLightEffectsV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddSmashLightEffectsV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_AddSmashLightEffectsV2Resp proto.InternalMessageInfo

// 编辑中奖光效,根据光效id覆盖更新
type UpdateSmashLightEffectV2Req struct {
	Conf                 *SmashLightEffect `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UpdateSmashLightEffectV2Req) Reset()         { *m = UpdateSmashLightEffectV2Req{} }
func (m *UpdateSmashLightEffectV2Req) String() string { return proto.CompactTextString(m) }
func (*UpdateSmashLightEffectV2Req) ProtoMessage()    {}
func (*UpdateSmashLightEffectV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{50}
}
func (m *UpdateSmashLightEffectV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateSmashLightEffectV2Req.Unmarshal(m, b)
}
func (m *UpdateSmashLightEffectV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateSmashLightEffectV2Req.Marshal(b, m, deterministic)
}
func (dst *UpdateSmashLightEffectV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateSmashLightEffectV2Req.Merge(dst, src)
}
func (m *UpdateSmashLightEffectV2Req) XXX_Size() int {
	return xxx_messageInfo_UpdateSmashLightEffectV2Req.Size(m)
}
func (m *UpdateSmashLightEffectV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateSmashLightEffectV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateSmashLightEffectV2Req proto.InternalMessageInfo

func (m *UpdateSmashLightEffectV2Req) GetConf() *SmashLightEffect {
	if m != nil {
		return m.Conf
	}
	return nil
}

type UpdateSmashLightEffectV2Resp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateSmashLightEffectV2Resp) Reset()         { *m = UpdateSmashLightEffectV2Resp{} }
func (m *UpdateSmashLightEffectV2Resp) String() string { return proto.CompactTextString(m) }
func (*UpdateSmashLightEffectV2Resp) ProtoMessage()    {}
func (*UpdateSmashLightEffectV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{51}
}
func (m *UpdateSmashLightEffectV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateSmashLightEffectV2Resp.Unmarshal(m, b)
}
func (m *UpdateSmashLightEffectV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateSmashLightEffectV2Resp.Marshal(b, m, deterministic)
}
func (dst *UpdateSmashLightEffectV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateSmashLightEffectV2Resp.Merge(dst, src)
}
func (m *UpdateSmashLightEffectV2Resp) XXX_Size() int {
	return xxx_messageInfo_UpdateSmashLightEffectV2Resp.Size(m)
}
func (m *UpdateSmashLightEffectV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateSmashLightEffectV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateSmashLightEffectV2Resp proto.InternalMessageInfo

// 获取对应玩法的所有光效配置
type GetAllSmashLightEffectsV2Req struct {
	SearchVal            uint32   `protobuf:"varint,1,opt,name=search_val,json=searchVal,proto3" json:"search_val,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllSmashLightEffectsV2Req) Reset()         { *m = GetAllSmashLightEffectsV2Req{} }
func (m *GetAllSmashLightEffectsV2Req) String() string { return proto.CompactTextString(m) }
func (*GetAllSmashLightEffectsV2Req) ProtoMessage()    {}
func (*GetAllSmashLightEffectsV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{52}
}
func (m *GetAllSmashLightEffectsV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllSmashLightEffectsV2Req.Unmarshal(m, b)
}
func (m *GetAllSmashLightEffectsV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllSmashLightEffectsV2Req.Marshal(b, m, deterministic)
}
func (dst *GetAllSmashLightEffectsV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllSmashLightEffectsV2Req.Merge(dst, src)
}
func (m *GetAllSmashLightEffectsV2Req) XXX_Size() int {
	return xxx_messageInfo_GetAllSmashLightEffectsV2Req.Size(m)
}
func (m *GetAllSmashLightEffectsV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllSmashLightEffectsV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllSmashLightEffectsV2Req proto.InternalMessageInfo

func (m *GetAllSmashLightEffectsV2Req) GetSearchVal() uint32 {
	if m != nil {
		return m.SearchVal
	}
	return 0
}

type GetAllSmashLightEffectsV2Resp struct {
	ConfList             []*SmashLightEffect `protobuf:"bytes,1,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetAllSmashLightEffectsV2Resp) Reset()         { *m = GetAllSmashLightEffectsV2Resp{} }
func (m *GetAllSmashLightEffectsV2Resp) String() string { return proto.CompactTextString(m) }
func (*GetAllSmashLightEffectsV2Resp) ProtoMessage()    {}
func (*GetAllSmashLightEffectsV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{53}
}
func (m *GetAllSmashLightEffectsV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllSmashLightEffectsV2Resp.Unmarshal(m, b)
}
func (m *GetAllSmashLightEffectsV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllSmashLightEffectsV2Resp.Marshal(b, m, deterministic)
}
func (dst *GetAllSmashLightEffectsV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllSmashLightEffectsV2Resp.Merge(dst, src)
}
func (m *GetAllSmashLightEffectsV2Resp) XXX_Size() int {
	return xxx_messageInfo_GetAllSmashLightEffectsV2Resp.Size(m)
}
func (m *GetAllSmashLightEffectsV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllSmashLightEffectsV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllSmashLightEffectsV2Resp proto.InternalMessageInfo

func (m *GetAllSmashLightEffectsV2Resp) GetConfList() []*SmashLightEffect {
	if m != nil {
		return m.ConfList
	}
	return nil
}

// 删除光效
type DelLightEffectByconfIdReq struct {
	ConfId               uint32   `protobuf:"varint,1,opt,name=conf_id,json=confId,proto3" json:"conf_id,omitempty"`
	OpUser               string   `protobuf:"bytes,3,opt,name=op_user,json=opUser,proto3" json:"op_user,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelLightEffectByconfIdReq) Reset()         { *m = DelLightEffectByconfIdReq{} }
func (m *DelLightEffectByconfIdReq) String() string { return proto.CompactTextString(m) }
func (*DelLightEffectByconfIdReq) ProtoMessage()    {}
func (*DelLightEffectByconfIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{54}
}
func (m *DelLightEffectByconfIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelLightEffectByconfIdReq.Unmarshal(m, b)
}
func (m *DelLightEffectByconfIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelLightEffectByconfIdReq.Marshal(b, m, deterministic)
}
func (dst *DelLightEffectByconfIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelLightEffectByconfIdReq.Merge(dst, src)
}
func (m *DelLightEffectByconfIdReq) XXX_Size() int {
	return xxx_messageInfo_DelLightEffectByconfIdReq.Size(m)
}
func (m *DelLightEffectByconfIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelLightEffectByconfIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelLightEffectByconfIdReq proto.InternalMessageInfo

func (m *DelLightEffectByconfIdReq) GetConfId() uint32 {
	if m != nil {
		return m.ConfId
	}
	return 0
}

func (m *DelLightEffectByconfIdReq) GetOpUser() string {
	if m != nil {
		return m.OpUser
	}
	return ""
}

type DelLightEffectByconfIdResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelLightEffectByconfIdResp) Reset()         { *m = DelLightEffectByconfIdResp{} }
func (m *DelLightEffectByconfIdResp) String() string { return proto.CompactTextString(m) }
func (*DelLightEffectByconfIdResp) ProtoMessage()    {}
func (*DelLightEffectByconfIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{55}
}
func (m *DelLightEffectByconfIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelLightEffectByconfIdResp.Unmarshal(m, b)
}
func (m *DelLightEffectByconfIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelLightEffectByconfIdResp.Marshal(b, m, deterministic)
}
func (dst *DelLightEffectByconfIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelLightEffectByconfIdResp.Merge(dst, src)
}
func (m *DelLightEffectByconfIdResp) XXX_Size() int {
	return xxx_messageInfo_DelLightEffectByconfIdResp.Size(m)
}
func (m *DelLightEffectByconfIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelLightEffectByconfIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelLightEffectByconfIdResp proto.InternalMessageInfo

// 用户道具
type UserProp struct {
	PropId               uint32   `protobuf:"varint,1,opt,name=prop_id,json=propId,proto3" json:"prop_id,omitempty"`
	Num                  uint32   `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`
	ExpireTime           int64    `protobuf:"varint,3,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserProp) Reset()         { *m = UserProp{} }
func (m *UserProp) String() string { return proto.CompactTextString(m) }
func (*UserProp) ProtoMessage()    {}
func (*UserProp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{56}
}
func (m *UserProp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserProp.Unmarshal(m, b)
}
func (m *UserProp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserProp.Marshal(b, m, deterministic)
}
func (dst *UserProp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserProp.Merge(dst, src)
}
func (m *UserProp) XXX_Size() int {
	return xxx_messageInfo_UserProp.Size(m)
}
func (m *UserProp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserProp.DiscardUnknown(m)
}

var xxx_messageInfo_UserProp proto.InternalMessageInfo

func (m *UserProp) GetPropId() uint32 {
	if m != nil {
		return m.PropId
	}
	return 0
}

func (m *UserProp) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *UserProp) GetExpireTime() int64 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

// 获取用户未过期的道具
type GetUserPropReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PropId               uint32   `protobuf:"varint,2,opt,name=prop_id,json=propId,proto3" json:"prop_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserPropReq) Reset()         { *m = GetUserPropReq{} }
func (m *GetUserPropReq) String() string { return proto.CompactTextString(m) }
func (*GetUserPropReq) ProtoMessage()    {}
func (*GetUserPropReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{57}
}
func (m *GetUserPropReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPropReq.Unmarshal(m, b)
}
func (m *GetUserPropReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPropReq.Marshal(b, m, deterministic)
}
func (dst *GetUserPropReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPropReq.Merge(dst, src)
}
func (m *GetUserPropReq) XXX_Size() int {
	return xxx_messageInfo_GetUserPropReq.Size(m)
}
func (m *GetUserPropReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPropReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPropReq proto.InternalMessageInfo

func (m *GetUserPropReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserPropReq) GetPropId() uint32 {
	if m != nil {
		return m.PropId
	}
	return 0
}

type GetUserPropResp struct {
	UserPropList         []*UserProp `protobuf:"bytes,1,rep,name=user_prop_list,json=userPropList,proto3" json:"user_prop_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetUserPropResp) Reset()         { *m = GetUserPropResp{} }
func (m *GetUserPropResp) String() string { return proto.CompactTextString(m) }
func (*GetUserPropResp) ProtoMessage()    {}
func (*GetUserPropResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{58}
}
func (m *GetUserPropResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPropResp.Unmarshal(m, b)
}
func (m *GetUserPropResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPropResp.Marshal(b, m, deterministic)
}
func (dst *GetUserPropResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPropResp.Merge(dst, src)
}
func (m *GetUserPropResp) XXX_Size() int {
	return xxx_messageInfo_GetUserPropResp.Size(m)
}
func (m *GetUserPropResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPropResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPropResp proto.InternalMessageInfo

func (m *GetUserPropResp) GetUserPropList() []*UserProp {
	if m != nil {
		return m.UserPropList
	}
	return nil
}

type GetCurActivityTypeReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCurActivityTypeReq) Reset()         { *m = GetCurActivityTypeReq{} }
func (m *GetCurActivityTypeReq) String() string { return proto.CompactTextString(m) }
func (*GetCurActivityTypeReq) ProtoMessage()    {}
func (*GetCurActivityTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{59}
}
func (m *GetCurActivityTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCurActivityTypeReq.Unmarshal(m, b)
}
func (m *GetCurActivityTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCurActivityTypeReq.Marshal(b, m, deterministic)
}
func (dst *GetCurActivityTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCurActivityTypeReq.Merge(dst, src)
}
func (m *GetCurActivityTypeReq) XXX_Size() int {
	return xxx_messageInfo_GetCurActivityTypeReq.Size(m)
}
func (m *GetCurActivityTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCurActivityTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCurActivityTypeReq proto.InternalMessageInfo

type GetCurActivityTypeResp struct {
	CurActivityType      CurThemeType `protobuf:"varint,1,opt,name=cur_activity_type,json=curActivityType,proto3,enum=ga.smash_egg.CurThemeType" json:"cur_activity_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetCurActivityTypeResp) Reset()         { *m = GetCurActivityTypeResp{} }
func (m *GetCurActivityTypeResp) String() string { return proto.CompactTextString(m) }
func (*GetCurActivityTypeResp) ProtoMessage()    {}
func (*GetCurActivityTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{60}
}
func (m *GetCurActivityTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCurActivityTypeResp.Unmarshal(m, b)
}
func (m *GetCurActivityTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCurActivityTypeResp.Marshal(b, m, deterministic)
}
func (dst *GetCurActivityTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCurActivityTypeResp.Merge(dst, src)
}
func (m *GetCurActivityTypeResp) XXX_Size() int {
	return xxx_messageInfo_GetCurActivityTypeResp.Size(m)
}
func (m *GetCurActivityTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCurActivityTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCurActivityTypeResp proto.InternalMessageInfo

func (m *GetCurActivityTypeResp) GetCurActivityType() CurThemeType {
	if m != nil {
		return m.CurActivityType
	}
	return CurThemeType_CUR_THEME_TYPE_UNSPECIFIED
}

type RefundPropInfo struct {
	ActivityName string `protobuf:"bytes,1,opt,name=activity_name,json=activityName,proto3" json:"activity_name,omitempty"`
	// 下架时间
	OfflineTime          int64        `protobuf:"varint,2,opt,name=offline_time,json=offlineTime,proto3" json:"offline_time,omitempty"`
	PropName             string       `protobuf:"bytes,3,opt,name=prop_name,json=propName,proto3" json:"prop_name,omitempty"`
	RemainUserCnt        uint32       `protobuf:"varint,4,opt,name=remain_user_cnt,json=remainUserCnt,proto3" json:"remain_user_cnt,omitempty"`
	RemainPropCnt        uint32       `protobuf:"varint,5,opt,name=remain_prop_cnt,json=remainPropCnt,proto3" json:"remain_prop_cnt,omitempty"`
	Status               RefundStatus `protobuf:"varint,6,opt,name=status,proto3,enum=ga.smash_egg.RefundStatus" json:"status,omitempty"`
	FinishTime           int64        `protobuf:"varint,7,opt,name=finish_time,json=finishTime,proto3" json:"finish_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *RefundPropInfo) Reset()         { *m = RefundPropInfo{} }
func (m *RefundPropInfo) String() string { return proto.CompactTextString(m) }
func (*RefundPropInfo) ProtoMessage()    {}
func (*RefundPropInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{61}
}
func (m *RefundPropInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RefundPropInfo.Unmarshal(m, b)
}
func (m *RefundPropInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RefundPropInfo.Marshal(b, m, deterministic)
}
func (dst *RefundPropInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RefundPropInfo.Merge(dst, src)
}
func (m *RefundPropInfo) XXX_Size() int {
	return xxx_messageInfo_RefundPropInfo.Size(m)
}
func (m *RefundPropInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RefundPropInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RefundPropInfo proto.InternalMessageInfo

func (m *RefundPropInfo) GetActivityName() string {
	if m != nil {
		return m.ActivityName
	}
	return ""
}

func (m *RefundPropInfo) GetOfflineTime() int64 {
	if m != nil {
		return m.OfflineTime
	}
	return 0
}

func (m *RefundPropInfo) GetPropName() string {
	if m != nil {
		return m.PropName
	}
	return ""
}

func (m *RefundPropInfo) GetRemainUserCnt() uint32 {
	if m != nil {
		return m.RemainUserCnt
	}
	return 0
}

func (m *RefundPropInfo) GetRemainPropCnt() uint32 {
	if m != nil {
		return m.RemainPropCnt
	}
	return 0
}

func (m *RefundPropInfo) GetStatus() RefundStatus {
	if m != nil {
		return m.Status
	}
	return RefundStatus_REFUND_STATUS_UNSPECIFIED
}

func (m *RefundPropInfo) GetFinishTime() int64 {
	if m != nil {
		return m.FinishTime
	}
	return 0
}

// 查询当前是否有可退还道具的记录信息
// （先调用GetCurActivityType接口(请求任意a\b服务的该接口都可)判断当前生效中的活动类型，如果cur_activity_type==CUR_THEME_TYPE_A,则调用smash-egg-b服务的该接口
// 反之，则调用smash-egg-a服务的该接口）
type GetRefundPropInfoReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRefundPropInfoReq) Reset()         { *m = GetRefundPropInfoReq{} }
func (m *GetRefundPropInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetRefundPropInfoReq) ProtoMessage()    {}
func (*GetRefundPropInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{62}
}
func (m *GetRefundPropInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRefundPropInfoReq.Unmarshal(m, b)
}
func (m *GetRefundPropInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRefundPropInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetRefundPropInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRefundPropInfoReq.Merge(dst, src)
}
func (m *GetRefundPropInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetRefundPropInfoReq.Size(m)
}
func (m *GetRefundPropInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRefundPropInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRefundPropInfoReq proto.InternalMessageInfo

type GetRefundPropInfoResp struct {
	Info                 *RefundPropInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetRefundPropInfoResp) Reset()         { *m = GetRefundPropInfoResp{} }
func (m *GetRefundPropInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetRefundPropInfoResp) ProtoMessage()    {}
func (*GetRefundPropInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{63}
}
func (m *GetRefundPropInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRefundPropInfoResp.Unmarshal(m, b)
}
func (m *GetRefundPropInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRefundPropInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetRefundPropInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRefundPropInfoResp.Merge(dst, src)
}
func (m *GetRefundPropInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetRefundPropInfoResp.Size(m)
}
func (m *GetRefundPropInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRefundPropInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRefundPropInfoResp proto.InternalMessageInfo

func (m *GetRefundPropInfoResp) GetInfo() *RefundPropInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

// 设置退还记录状态，可设置 REFUND_STATUS_NOT_SUMMITED，REFUND_STATUS_APPLYING，REFUND_STATUS_NOT_SHOW
type SetRefundStatusReq struct {
	Status               RefundStatus `protobuf:"varint,1,opt,name=status,proto3,enum=ga.smash_egg.RefundStatus" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetRefundStatusReq) Reset()         { *m = SetRefundStatusReq{} }
func (m *SetRefundStatusReq) String() string { return proto.CompactTextString(m) }
func (*SetRefundStatusReq) ProtoMessage()    {}
func (*SetRefundStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{64}
}
func (m *SetRefundStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetRefundStatusReq.Unmarshal(m, b)
}
func (m *SetRefundStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetRefundStatusReq.Marshal(b, m, deterministic)
}
func (dst *SetRefundStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetRefundStatusReq.Merge(dst, src)
}
func (m *SetRefundStatusReq) XXX_Size() int {
	return xxx_messageInfo_SetRefundStatusReq.Size(m)
}
func (m *SetRefundStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetRefundStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetRefundStatusReq proto.InternalMessageInfo

func (m *SetRefundStatusReq) GetStatus() RefundStatus {
	if m != nil {
		return m.Status
	}
	return RefundStatus_REFUND_STATUS_UNSPECIFIED
}

type SetRefundStatusResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetRefundStatusResp) Reset()         { *m = SetRefundStatusResp{} }
func (m *SetRefundStatusResp) String() string { return proto.CompactTextString(m) }
func (*SetRefundStatusResp) ProtoMessage()    {}
func (*SetRefundStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{65}
}
func (m *SetRefundStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetRefundStatusResp.Unmarshal(m, b)
}
func (m *SetRefundStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetRefundStatusResp.Marshal(b, m, deterministic)
}
func (dst *SetRefundStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetRefundStatusResp.Merge(dst, src)
}
func (m *SetRefundStatusResp) XXX_Size() int {
	return xxx_messageInfo_SetRefundStatusResp.Size(m)
}
func (m *SetRefundStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetRefundStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetRefundStatusResp proto.InternalMessageInfo

// 提交退还道具发放请求
type ConfirmRefundPropReq struct {
	Info                 *RefundPropInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ConfirmRefundPropReq) Reset()         { *m = ConfirmRefundPropReq{} }
func (m *ConfirmRefundPropReq) String() string { return proto.CompactTextString(m) }
func (*ConfirmRefundPropReq) ProtoMessage()    {}
func (*ConfirmRefundPropReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{66}
}
func (m *ConfirmRefundPropReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfirmRefundPropReq.Unmarshal(m, b)
}
func (m *ConfirmRefundPropReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfirmRefundPropReq.Marshal(b, m, deterministic)
}
func (dst *ConfirmRefundPropReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfirmRefundPropReq.Merge(dst, src)
}
func (m *ConfirmRefundPropReq) XXX_Size() int {
	return xxx_messageInfo_ConfirmRefundPropReq.Size(m)
}
func (m *ConfirmRefundPropReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfirmRefundPropReq.DiscardUnknown(m)
}

var xxx_messageInfo_ConfirmRefundPropReq proto.InternalMessageInfo

func (m *ConfirmRefundPropReq) GetInfo() *RefundPropInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type ConfirmRefundPropResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConfirmRefundPropResp) Reset()         { *m = ConfirmRefundPropResp{} }
func (m *ConfirmRefundPropResp) String() string { return proto.CompactTextString(m) }
func (*ConfirmRefundPropResp) ProtoMessage()    {}
func (*ConfirmRefundPropResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{67}
}
func (m *ConfirmRefundPropResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfirmRefundPropResp.Unmarshal(m, b)
}
func (m *ConfirmRefundPropResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfirmRefundPropResp.Marshal(b, m, deterministic)
}
func (dst *ConfirmRefundPropResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfirmRefundPropResp.Merge(dst, src)
}
func (m *ConfirmRefundPropResp) XXX_Size() int {
	return xxx_messageInfo_ConfirmRefundPropResp.Size(m)
}
func (m *ConfirmRefundPropResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfirmRefundPropResp.DiscardUnknown(m)
}

var xxx_messageInfo_ConfirmRefundPropResp proto.InternalMessageInfo

// 道具结算
type PropSettlementReq struct {
	OpType               SettlementType `protobuf:"varint,1,opt,name=op_type,json=opType,proto3,enum=ga.smash_egg.SettlementType" json:"op_type,omitempty"`
	SecretKey            string         `protobuf:"bytes,2,opt,name=secret_key,json=secretKey,proto3" json:"secret_key,omitempty"`
	OfflineTime          int64          `protobuf:"varint,3,opt,name=offline_time,json=offlineTime,proto3" json:"offline_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *PropSettlementReq) Reset()         { *m = PropSettlementReq{} }
func (m *PropSettlementReq) String() string { return proto.CompactTextString(m) }
func (*PropSettlementReq) ProtoMessage()    {}
func (*PropSettlementReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{68}
}
func (m *PropSettlementReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PropSettlementReq.Unmarshal(m, b)
}
func (m *PropSettlementReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PropSettlementReq.Marshal(b, m, deterministic)
}
func (dst *PropSettlementReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PropSettlementReq.Merge(dst, src)
}
func (m *PropSettlementReq) XXX_Size() int {
	return xxx_messageInfo_PropSettlementReq.Size(m)
}
func (m *PropSettlementReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PropSettlementReq.DiscardUnknown(m)
}

var xxx_messageInfo_PropSettlementReq proto.InternalMessageInfo

func (m *PropSettlementReq) GetOpType() SettlementType {
	if m != nil {
		return m.OpType
	}
	return SettlementType_SETTLEMENT_TYPE_UNSPECIFIED
}

func (m *PropSettlementReq) GetSecretKey() string {
	if m != nil {
		return m.SecretKey
	}
	return ""
}

func (m *PropSettlementReq) GetOfflineTime() int64 {
	if m != nil {
		return m.OfflineTime
	}
	return 0
}

type PropSettlementResp struct {
	Msg                  string   `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PropSettlementResp) Reset()         { *m = PropSettlementResp{} }
func (m *PropSettlementResp) String() string { return proto.CompactTextString(m) }
func (*PropSettlementResp) ProtoMessage()    {}
func (*PropSettlementResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{69}
}
func (m *PropSettlementResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PropSettlementResp.Unmarshal(m, b)
}
func (m *PropSettlementResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PropSettlementResp.Marshal(b, m, deterministic)
}
func (dst *PropSettlementResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PropSettlementResp.Merge(dst, src)
}
func (m *PropSettlementResp) XXX_Size() int {
	return xxx_messageInfo_PropSettlementResp.Size(m)
}
func (m *PropSettlementResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PropSettlementResp.DiscardUnknown(m)
}

var xxx_messageInfo_PropSettlementResp proto.InternalMessageInfo

func (m *PropSettlementResp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type GetUserPropExpireDetailReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PropId               uint32   `protobuf:"varint,2,opt,name=prop_id,json=propId,proto3" json:"prop_id,omitempty"`
	BeginTs              int64    `protobuf:"varint,3,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                int64    `protobuf:"varint,4,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	PageSize             uint32   `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageIdx              uint32   `protobuf:"varint,6,opt,name=page_idx,json=pageIdx,proto3" json:"page_idx,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserPropExpireDetailReq) Reset()         { *m = GetUserPropExpireDetailReq{} }
func (m *GetUserPropExpireDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetUserPropExpireDetailReq) ProtoMessage()    {}
func (*GetUserPropExpireDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{70}
}
func (m *GetUserPropExpireDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPropExpireDetailReq.Unmarshal(m, b)
}
func (m *GetUserPropExpireDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPropExpireDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetUserPropExpireDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPropExpireDetailReq.Merge(dst, src)
}
func (m *GetUserPropExpireDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetUserPropExpireDetailReq.Size(m)
}
func (m *GetUserPropExpireDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPropExpireDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPropExpireDetailReq proto.InternalMessageInfo

func (m *GetUserPropExpireDetailReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserPropExpireDetailReq) GetPropId() uint32 {
	if m != nil {
		return m.PropId
	}
	return 0
}

func (m *GetUserPropExpireDetailReq) GetBeginTs() int64 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *GetUserPropExpireDetailReq) GetEndTs() int64 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *GetUserPropExpireDetailReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetUserPropExpireDetailReq) GetPageIdx() uint32 {
	if m != nil {
		return m.PageIdx
	}
	return 0
}

type GetUserPropExpireDetailResp struct {
	UserPropList         []*UserProp `protobuf:"bytes,1,rep,name=user_prop_list,json=userPropList,proto3" json:"user_prop_list,omitempty"`
	TotalCnt             uint32      `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetUserPropExpireDetailResp) Reset()         { *m = GetUserPropExpireDetailResp{} }
func (m *GetUserPropExpireDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetUserPropExpireDetailResp) ProtoMessage()    {}
func (*GetUserPropExpireDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_2982dd97161610b4, []int{71}
}
func (m *GetUserPropExpireDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPropExpireDetailResp.Unmarshal(m, b)
}
func (m *GetUserPropExpireDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPropExpireDetailResp.Marshal(b, m, deterministic)
}
func (dst *GetUserPropExpireDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPropExpireDetailResp.Merge(dst, src)
}
func (m *GetUserPropExpireDetailResp) XXX_Size() int {
	return xxx_messageInfo_GetUserPropExpireDetailResp.Size(m)
}
func (m *GetUserPropExpireDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPropExpireDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPropExpireDetailResp proto.InternalMessageInfo

func (m *GetUserPropExpireDetailResp) GetUserPropList() []*UserProp {
	if m != nil {
		return m.UserPropList
	}
	return nil
}

func (m *GetUserPropExpireDetailResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

func init() {
	proto.RegisterType((*ConsumeRecord)(nil), "ga.smash_egg.ConsumeRecord")
	proto.RegisterType((*WinningRecord)(nil), "ga.smash_egg.WinningRecord")
	proto.RegisterType((*Config)(nil), "ga.smash_egg.Config")
	proto.RegisterType((*Prize)(nil), "ga.smash_egg.Prize")
	proto.RegisterType((*GetConsumeRecordReq)(nil), "ga.smash_egg.GetConsumeRecordReq")
	proto.RegisterType((*GetConsumeRecordResp)(nil), "ga.smash_egg.GetConsumeRecordResp")
	proto.RegisterType((*GetWinningRecordReq)(nil), "ga.smash_egg.GetWinningRecordReq")
	proto.RegisterType((*GetWinningRecordResp)(nil), "ga.smash_egg.GetWinningRecordResp")
	proto.RegisterType((*GuaranteedInfo)(nil), "ga.smash_egg.GuaranteedInfo")
	proto.RegisterType((*GetSmashStatusReq)(nil), "ga.smash_egg.GetSmashStatusReq")
	proto.RegisterType((*GetSmashStatusResp)(nil), "ga.smash_egg.GetSmashStatusResp")
	proto.RegisterType((*RechargeReq)(nil), "ga.smash_egg.RechargeReq")
	proto.RegisterType((*RechargeResp)(nil), "ga.smash_egg.RechargeResp")
	proto.RegisterType((*SmashReq)(nil), "ga.smash_egg.SmashReq")
	proto.RegisterType((*SmashResp)(nil), "ga.smash_egg.SmashResp")
	proto.RegisterType((*GetSmashConfigReq)(nil), "ga.smash_egg.GetSmashConfigReq")
	proto.RegisterType((*GetSmashConfigResp)(nil), "ga.smash_egg.GetSmashConfigResp")
	proto.RegisterType((*SetSmashConfigReq)(nil), "ga.smash_egg.SetSmashConfigReq")
	proto.RegisterType((*SetSmashConfigResp)(nil), "ga.smash_egg.SetSmashConfigResp")
	proto.RegisterType((*GetPrizePoolReq)(nil), "ga.smash_egg.GetPrizePoolReq")
	proto.RegisterType((*GetPrizePoolResp)(nil), "ga.smash_egg.GetPrizePoolResp")
	proto.RegisterType((*GetPrizePoolTmpReq)(nil), "ga.smash_egg.GetPrizePoolTmpReq")
	proto.RegisterType((*GetPrizePoolTmpResp)(nil), "ga.smash_egg.GetPrizePoolTmpResp")
	proto.RegisterType((*SetPrizePoolReq)(nil), "ga.smash_egg.SetPrizePoolReq")
	proto.RegisterType((*SetPrizePoolResp)(nil), "ga.smash_egg.SetPrizePoolResp")
	proto.RegisterType((*DelTmpPrizePoolReq)(nil), "ga.smash_egg.DelTmpPrizePoolReq")
	proto.RegisterType((*DelTmpPrizePoolResp)(nil), "ga.smash_egg.DelTmpPrizePoolResp")
	proto.RegisterType((*SimulateWithPrizePoolReq)(nil), "ga.smash_egg.SimulateWithPrizePoolReq")
	proto.RegisterType((*SimulateWithPrizePoolResp)(nil), "ga.smash_egg.SimulateWithPrizePoolResp")
	proto.RegisterType((*CheckWhitelistReq)(nil), "ga.smash_egg.CheckWhitelistReq")
	proto.RegisterType((*CheckWhitelistResp)(nil), "ga.smash_egg.CheckWhitelistResp")
	proto.RegisterType((*CheckTesterReq)(nil), "ga.smash_egg.CheckTesterReq")
	proto.RegisterType((*CheckTesterResp)(nil), "ga.smash_egg.CheckTesterResp")
	proto.RegisterType((*GetSmashEggExemptValueReq)(nil), "ga.smash_egg.GetSmashEggExemptValueReq")
	proto.RegisterType((*GetSmashEggExemptValueResp)(nil), "ga.smash_egg.GetSmashEggExemptValueResp")
	proto.RegisterType((*SmashActivityConfig)(nil), "ga.smash_egg.SmashActivityConfig")
	proto.RegisterType((*UpdateSmashEggActivityConfigReq)(nil), "ga.smash_egg.UpdateSmashEggActivityConfigReq")
	proto.RegisterType((*UpdateSmashEggActivityConfigResp)(nil), "ga.smash_egg.UpdateSmashEggActivityConfigResp")
	proto.RegisterType((*ModeCfg)(nil), "ga.smash_egg.ModeCfg")
	proto.RegisterType((*GetSmashEggActivityConfigReq)(nil), "ga.smash_egg.GetSmashEggActivityConfigReq")
	proto.RegisterType((*GetSmashEggActivityConfigResp)(nil), "ga.smash_egg.GetSmashEggActivityConfigResp")
	proto.RegisterType((*GetSmashEggActivityConfWithCacheReq)(nil), "ga.smash_egg.GetSmashEggActivityConfWithCacheReq")
	proto.RegisterType((*GetSmashEggActivityConfWithCacheResp)(nil), "ga.smash_egg.GetSmashEggActivityConfWithCacheResp")
	proto.RegisterType((*CheckSmashEggActivityConfigReq)(nil), "ga.smash_egg.CheckSmashEggActivityConfigReq")
	proto.RegisterType((*CheckSmashEggActivityConfigResp)(nil), "ga.smash_egg.CheckSmashEggActivityConfigResp")
	proto.RegisterType((*DelSmashEggActivityConfigReq)(nil), "ga.smash_egg.DelSmashEggActivityConfigReq")
	proto.RegisterType((*DelSmashEggActivityConfigResp)(nil), "ga.smash_egg.DelSmashEggActivityConfigResp")
	proto.RegisterType((*SmashLightEffect)(nil), "ga.smash_egg.SmashLightEffect")
	proto.RegisterType((*AddSmashLightEffectsV2Req)(nil), "ga.smash_egg.AddSmashLightEffectsV2Req")
	proto.RegisterType((*AddSmashLightEffectsV2Resp)(nil), "ga.smash_egg.AddSmashLightEffectsV2Resp")
	proto.RegisterType((*UpdateSmashLightEffectV2Req)(nil), "ga.smash_egg.UpdateSmashLightEffectV2Req")
	proto.RegisterType((*UpdateSmashLightEffectV2Resp)(nil), "ga.smash_egg.UpdateSmashLightEffectV2Resp")
	proto.RegisterType((*GetAllSmashLightEffectsV2Req)(nil), "ga.smash_egg.GetAllSmashLightEffectsV2Req")
	proto.RegisterType((*GetAllSmashLightEffectsV2Resp)(nil), "ga.smash_egg.GetAllSmashLightEffectsV2Resp")
	proto.RegisterType((*DelLightEffectByconfIdReq)(nil), "ga.smash_egg.DelLightEffectByconfIdReq")
	proto.RegisterType((*DelLightEffectByconfIdResp)(nil), "ga.smash_egg.DelLightEffectByconfIdResp")
	proto.RegisterType((*UserProp)(nil), "ga.smash_egg.UserProp")
	proto.RegisterType((*GetUserPropReq)(nil), "ga.smash_egg.GetUserPropReq")
	proto.RegisterType((*GetUserPropResp)(nil), "ga.smash_egg.GetUserPropResp")
	proto.RegisterType((*GetCurActivityTypeReq)(nil), "ga.smash_egg.GetCurActivityTypeReq")
	proto.RegisterType((*GetCurActivityTypeResp)(nil), "ga.smash_egg.GetCurActivityTypeResp")
	proto.RegisterType((*RefundPropInfo)(nil), "ga.smash_egg.RefundPropInfo")
	proto.RegisterType((*GetRefundPropInfoReq)(nil), "ga.smash_egg.GetRefundPropInfoReq")
	proto.RegisterType((*GetRefundPropInfoResp)(nil), "ga.smash_egg.GetRefundPropInfoResp")
	proto.RegisterType((*SetRefundStatusReq)(nil), "ga.smash_egg.SetRefundStatusReq")
	proto.RegisterType((*SetRefundStatusResp)(nil), "ga.smash_egg.SetRefundStatusResp")
	proto.RegisterType((*ConfirmRefundPropReq)(nil), "ga.smash_egg.ConfirmRefundPropReq")
	proto.RegisterType((*ConfirmRefundPropResp)(nil), "ga.smash_egg.ConfirmRefundPropResp")
	proto.RegisterType((*PropSettlementReq)(nil), "ga.smash_egg.PropSettlementReq")
	proto.RegisterType((*PropSettlementResp)(nil), "ga.smash_egg.PropSettlementResp")
	proto.RegisterType((*GetUserPropExpireDetailReq)(nil), "ga.smash_egg.GetUserPropExpireDetailReq")
	proto.RegisterType((*GetUserPropExpireDetailResp)(nil), "ga.smash_egg.GetUserPropExpireDetailResp")
	proto.RegisterEnum("ga.smash_egg.Source", Source_name, Source_value)
	proto.RegisterEnum("ga.smash_egg.Flag", Flag_name, Flag_value)
	proto.RegisterEnum("ga.smash_egg.Mode", Mode_name, Mode_value)
	proto.RegisterEnum("ga.smash_egg.Status", Status_name, Status_value)
	proto.RegisterEnum("ga.smash_egg.Platform", Platform_name, Platform_value)
	proto.RegisterEnum("ga.smash_egg.App", App_name, App_value)
	proto.RegisterEnum("ga.smash_egg.CurThemeType", CurThemeType_name, CurThemeType_value)
	proto.RegisterEnum("ga.smash_egg.RefundStatus", RefundStatus_name, RefundStatus_value)
	proto.RegisterEnum("ga.smash_egg.SettlementType", SettlementType_name, SettlementType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// SmashEggClient is the client API for SmashEgg service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type SmashEggClient interface {
	// 获取消费记录
	GetConsumeRecord(ctx context.Context, in *GetConsumeRecordReq, opts ...grpc.CallOption) (*GetConsumeRecordResp, error)
	// 获取中奖记录
	GetWinningRecord(ctx context.Context, in *GetWinningRecordReq, opts ...grpc.CallOption) (*GetWinningRecordResp, error)
	// 充值
	Recharge(ctx context.Context, in *RechargeReq, opts ...grpc.CallOption) (*RechargeResp, error)
	// 砸
	Smash(ctx context.Context, in *SmashReq, opts ...grpc.CallOption) (*SmashResp, error)
	// 获取状态
	GetSmashStatus(ctx context.Context, in *GetSmashStatusReq, opts ...grpc.CallOption) (*GetSmashStatusResp, error)
	// 获取配置
	GetSmashConfig(ctx context.Context, in *GetSmashConfigReq, opts ...grpc.CallOption) (*GetSmashConfigResp, error)
	// 设置配置
	SetSmashConfig(ctx context.Context, in *SetSmashConfigReq, opts ...grpc.CallOption) (*SetSmashConfigResp, error)
	// 获取奖励池
	GetPrizePool(ctx context.Context, in *GetPrizePoolReq, opts ...grpc.CallOption) (*GetPrizePoolResp, error)
	// 获取未生效奖励池
	GetPrizePoolTmp(ctx context.Context, in *GetPrizePoolTmpReq, opts ...grpc.CallOption) (*GetPrizePoolTmpResp, error)
	// 设置奖励池
	SetPrizePool(ctx context.Context, in *SetPrizePoolReq, opts ...grpc.CallOption) (*SetPrizePoolResp, error)
	DelTmpPrizePool(ctx context.Context, in *DelTmpPrizePoolReq, opts ...grpc.CallOption) (*DelTmpPrizePoolResp, error)
	// 指定奖励池进行收益模拟
	SimulateWithPrizePool(ctx context.Context, in *SimulateWithPrizePoolReq, opts ...grpc.CallOption) (*SimulateWithPrizePoolResp, error)
	// 白名单检测
	CheckWhitelist(ctx context.Context, in *CheckWhitelistReq, opts ...grpc.CallOption) (*CheckWhitelistResp, error)
	CheckTester(ctx context.Context, in *CheckTesterReq, opts ...grpc.CallOption) (*CheckTesterResp, error)
	// 豁免条件值
	GetSmashEggExemptValue(ctx context.Context, in *GetSmashEggExemptValueReq, opts ...grpc.CallOption) (*GetSmashEggExemptValueResp, error)
	// 发放包裹数据对账
	GetAwardTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetAwardOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// T豆消费数据对账
	GetConsumeTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetConsumeOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	UpdateSmashEggActivityConfig(ctx context.Context, in *UpdateSmashEggActivityConfigReq, opts ...grpc.CallOption) (*UpdateSmashEggActivityConfigResp, error)
	GetSmashEggActivityConfig(ctx context.Context, in *GetSmashEggActivityConfigReq, opts ...grpc.CallOption) (*GetSmashEggActivityConfigResp, error)
	CheckSmashEggActivityConfig(ctx context.Context, in *CheckSmashEggActivityConfigReq, opts ...grpc.CallOption) (*CheckSmashEggActivityConfigResp, error)
	DelSmashEggActivityConfig(ctx context.Context, in *DelSmashEggActivityConfigReq, opts ...grpc.CallOption) (*DelSmashEggActivityConfigResp, error)
	GetSmashEggActivityConfWithCache(ctx context.Context, in *GetSmashEggActivityConfWithCacheReq, opts ...grpc.CallOption) (*GetSmashEggActivityConfWithCacheResp, error)
	// 中奖光效管理
	AddSmashLightEffectsV2(ctx context.Context, in *AddSmashLightEffectsV2Req, opts ...grpc.CallOption) (*AddSmashLightEffectsV2Resp, error)
	UpdateSmashLightEffectV2(ctx context.Context, in *UpdateSmashLightEffectV2Req, opts ...grpc.CallOption) (*UpdateSmashLightEffectV2Resp, error)
	GetAllSmashLightEffectsV2(ctx context.Context, in *GetAllSmashLightEffectsV2Req, opts ...grpc.CallOption) (*GetAllSmashLightEffectsV2Resp, error)
	DelLightEffectByconfId(ctx context.Context, in *DelLightEffectByconfIdReq, opts ...grpc.CallOption) (*DelLightEffectByconfIdResp, error)
	// 用户道具
	GetUserProp(ctx context.Context, in *GetUserPropReq, opts ...grpc.CallOption) (*GetUserPropResp, error)
	// 获取当前活动类型
	GetCurActivityType(ctx context.Context, in *GetCurActivityTypeReq, opts ...grpc.CallOption) (*GetCurActivityTypeResp, error)
	// 道具退还相关
	GetRefundPropInfo(ctx context.Context, in *GetRefundPropInfoReq, opts ...grpc.CallOption) (*GetRefundPropInfoResp, error)
	ConfirmRefundProp(ctx context.Context, in *ConfirmRefundPropReq, opts ...grpc.CallOption) (*ConfirmRefundPropResp, error)
	SetRefundStatus(ctx context.Context, in *SetRefundStatusReq, opts ...grpc.CallOption) (*SetRefundStatusResp, error)
	PropSettlement(ctx context.Context, in *PropSettlementReq, opts ...grpc.CallOption) (*PropSettlementResp, error)
	// ----------------- 用户道具查询后台 -----------------
	// 分页获取用户道具有效期
	GetUserPropExpireDetail(ctx context.Context, in *GetUserPropExpireDetailReq, opts ...grpc.CallOption) (*GetUserPropExpireDetailResp, error)
}

type smashEggClient struct {
	cc *grpc.ClientConn
}

func NewSmashEggClient(cc *grpc.ClientConn) SmashEggClient {
	return &smashEggClient{cc}
}

func (c *smashEggClient) GetConsumeRecord(ctx context.Context, in *GetConsumeRecordReq, opts ...grpc.CallOption) (*GetConsumeRecordResp, error) {
	out := new(GetConsumeRecordResp)
	err := c.cc.Invoke(ctx, "/ga.smash_egg.SmashEgg/GetConsumeRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smashEggClient) GetWinningRecord(ctx context.Context, in *GetWinningRecordReq, opts ...grpc.CallOption) (*GetWinningRecordResp, error) {
	out := new(GetWinningRecordResp)
	err := c.cc.Invoke(ctx, "/ga.smash_egg.SmashEgg/GetWinningRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smashEggClient) Recharge(ctx context.Context, in *RechargeReq, opts ...grpc.CallOption) (*RechargeResp, error) {
	out := new(RechargeResp)
	err := c.cc.Invoke(ctx, "/ga.smash_egg.SmashEgg/Recharge", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smashEggClient) Smash(ctx context.Context, in *SmashReq, opts ...grpc.CallOption) (*SmashResp, error) {
	out := new(SmashResp)
	err := c.cc.Invoke(ctx, "/ga.smash_egg.SmashEgg/Smash", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smashEggClient) GetSmashStatus(ctx context.Context, in *GetSmashStatusReq, opts ...grpc.CallOption) (*GetSmashStatusResp, error) {
	out := new(GetSmashStatusResp)
	err := c.cc.Invoke(ctx, "/ga.smash_egg.SmashEgg/GetSmashStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smashEggClient) GetSmashConfig(ctx context.Context, in *GetSmashConfigReq, opts ...grpc.CallOption) (*GetSmashConfigResp, error) {
	out := new(GetSmashConfigResp)
	err := c.cc.Invoke(ctx, "/ga.smash_egg.SmashEgg/GetSmashConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smashEggClient) SetSmashConfig(ctx context.Context, in *SetSmashConfigReq, opts ...grpc.CallOption) (*SetSmashConfigResp, error) {
	out := new(SetSmashConfigResp)
	err := c.cc.Invoke(ctx, "/ga.smash_egg.SmashEgg/SetSmashConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smashEggClient) GetPrizePool(ctx context.Context, in *GetPrizePoolReq, opts ...grpc.CallOption) (*GetPrizePoolResp, error) {
	out := new(GetPrizePoolResp)
	err := c.cc.Invoke(ctx, "/ga.smash_egg.SmashEgg/GetPrizePool", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smashEggClient) GetPrizePoolTmp(ctx context.Context, in *GetPrizePoolTmpReq, opts ...grpc.CallOption) (*GetPrizePoolTmpResp, error) {
	out := new(GetPrizePoolTmpResp)
	err := c.cc.Invoke(ctx, "/ga.smash_egg.SmashEgg/GetPrizePoolTmp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smashEggClient) SetPrizePool(ctx context.Context, in *SetPrizePoolReq, opts ...grpc.CallOption) (*SetPrizePoolResp, error) {
	out := new(SetPrizePoolResp)
	err := c.cc.Invoke(ctx, "/ga.smash_egg.SmashEgg/SetPrizePool", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smashEggClient) DelTmpPrizePool(ctx context.Context, in *DelTmpPrizePoolReq, opts ...grpc.CallOption) (*DelTmpPrizePoolResp, error) {
	out := new(DelTmpPrizePoolResp)
	err := c.cc.Invoke(ctx, "/ga.smash_egg.SmashEgg/DelTmpPrizePool", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smashEggClient) SimulateWithPrizePool(ctx context.Context, in *SimulateWithPrizePoolReq, opts ...grpc.CallOption) (*SimulateWithPrizePoolResp, error) {
	out := new(SimulateWithPrizePoolResp)
	err := c.cc.Invoke(ctx, "/ga.smash_egg.SmashEgg/SimulateWithPrizePool", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smashEggClient) CheckWhitelist(ctx context.Context, in *CheckWhitelistReq, opts ...grpc.CallOption) (*CheckWhitelistResp, error) {
	out := new(CheckWhitelistResp)
	err := c.cc.Invoke(ctx, "/ga.smash_egg.SmashEgg/CheckWhitelist", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smashEggClient) CheckTester(ctx context.Context, in *CheckTesterReq, opts ...grpc.CallOption) (*CheckTesterResp, error) {
	out := new(CheckTesterResp)
	err := c.cc.Invoke(ctx, "/ga.smash_egg.SmashEgg/CheckTester", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smashEggClient) GetSmashEggExemptValue(ctx context.Context, in *GetSmashEggExemptValueReq, opts ...grpc.CallOption) (*GetSmashEggExemptValueResp, error) {
	out := new(GetSmashEggExemptValueResp)
	err := c.cc.Invoke(ctx, "/ga.smash_egg.SmashEgg/GetSmashEggExemptValue", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smashEggClient) GetAwardTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/ga.smash_egg.SmashEgg/GetAwardTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smashEggClient) GetAwardOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/ga.smash_egg.SmashEgg/GetAwardOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smashEggClient) GetConsumeTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/ga.smash_egg.SmashEgg/GetConsumeTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smashEggClient) GetConsumeOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/ga.smash_egg.SmashEgg/GetConsumeOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smashEggClient) UpdateSmashEggActivityConfig(ctx context.Context, in *UpdateSmashEggActivityConfigReq, opts ...grpc.CallOption) (*UpdateSmashEggActivityConfigResp, error) {
	out := new(UpdateSmashEggActivityConfigResp)
	err := c.cc.Invoke(ctx, "/ga.smash_egg.SmashEgg/UpdateSmashEggActivityConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smashEggClient) GetSmashEggActivityConfig(ctx context.Context, in *GetSmashEggActivityConfigReq, opts ...grpc.CallOption) (*GetSmashEggActivityConfigResp, error) {
	out := new(GetSmashEggActivityConfigResp)
	err := c.cc.Invoke(ctx, "/ga.smash_egg.SmashEgg/GetSmashEggActivityConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smashEggClient) CheckSmashEggActivityConfig(ctx context.Context, in *CheckSmashEggActivityConfigReq, opts ...grpc.CallOption) (*CheckSmashEggActivityConfigResp, error) {
	out := new(CheckSmashEggActivityConfigResp)
	err := c.cc.Invoke(ctx, "/ga.smash_egg.SmashEgg/CheckSmashEggActivityConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smashEggClient) DelSmashEggActivityConfig(ctx context.Context, in *DelSmashEggActivityConfigReq, opts ...grpc.CallOption) (*DelSmashEggActivityConfigResp, error) {
	out := new(DelSmashEggActivityConfigResp)
	err := c.cc.Invoke(ctx, "/ga.smash_egg.SmashEgg/DelSmashEggActivityConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smashEggClient) GetSmashEggActivityConfWithCache(ctx context.Context, in *GetSmashEggActivityConfWithCacheReq, opts ...grpc.CallOption) (*GetSmashEggActivityConfWithCacheResp, error) {
	out := new(GetSmashEggActivityConfWithCacheResp)
	err := c.cc.Invoke(ctx, "/ga.smash_egg.SmashEgg/GetSmashEggActivityConfWithCache", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smashEggClient) AddSmashLightEffectsV2(ctx context.Context, in *AddSmashLightEffectsV2Req, opts ...grpc.CallOption) (*AddSmashLightEffectsV2Resp, error) {
	out := new(AddSmashLightEffectsV2Resp)
	err := c.cc.Invoke(ctx, "/ga.smash_egg.SmashEgg/AddSmashLightEffectsV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smashEggClient) UpdateSmashLightEffectV2(ctx context.Context, in *UpdateSmashLightEffectV2Req, opts ...grpc.CallOption) (*UpdateSmashLightEffectV2Resp, error) {
	out := new(UpdateSmashLightEffectV2Resp)
	err := c.cc.Invoke(ctx, "/ga.smash_egg.SmashEgg/UpdateSmashLightEffectV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smashEggClient) GetAllSmashLightEffectsV2(ctx context.Context, in *GetAllSmashLightEffectsV2Req, opts ...grpc.CallOption) (*GetAllSmashLightEffectsV2Resp, error) {
	out := new(GetAllSmashLightEffectsV2Resp)
	err := c.cc.Invoke(ctx, "/ga.smash_egg.SmashEgg/GetAllSmashLightEffectsV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smashEggClient) DelLightEffectByconfId(ctx context.Context, in *DelLightEffectByconfIdReq, opts ...grpc.CallOption) (*DelLightEffectByconfIdResp, error) {
	out := new(DelLightEffectByconfIdResp)
	err := c.cc.Invoke(ctx, "/ga.smash_egg.SmashEgg/DelLightEffectByconfId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smashEggClient) GetUserProp(ctx context.Context, in *GetUserPropReq, opts ...grpc.CallOption) (*GetUserPropResp, error) {
	out := new(GetUserPropResp)
	err := c.cc.Invoke(ctx, "/ga.smash_egg.SmashEgg/GetUserProp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smashEggClient) GetCurActivityType(ctx context.Context, in *GetCurActivityTypeReq, opts ...grpc.CallOption) (*GetCurActivityTypeResp, error) {
	out := new(GetCurActivityTypeResp)
	err := c.cc.Invoke(ctx, "/ga.smash_egg.SmashEgg/GetCurActivityType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smashEggClient) GetRefundPropInfo(ctx context.Context, in *GetRefundPropInfoReq, opts ...grpc.CallOption) (*GetRefundPropInfoResp, error) {
	out := new(GetRefundPropInfoResp)
	err := c.cc.Invoke(ctx, "/ga.smash_egg.SmashEgg/GetRefundPropInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smashEggClient) ConfirmRefundProp(ctx context.Context, in *ConfirmRefundPropReq, opts ...grpc.CallOption) (*ConfirmRefundPropResp, error) {
	out := new(ConfirmRefundPropResp)
	err := c.cc.Invoke(ctx, "/ga.smash_egg.SmashEgg/ConfirmRefundProp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smashEggClient) SetRefundStatus(ctx context.Context, in *SetRefundStatusReq, opts ...grpc.CallOption) (*SetRefundStatusResp, error) {
	out := new(SetRefundStatusResp)
	err := c.cc.Invoke(ctx, "/ga.smash_egg.SmashEgg/SetRefundStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smashEggClient) PropSettlement(ctx context.Context, in *PropSettlementReq, opts ...grpc.CallOption) (*PropSettlementResp, error) {
	out := new(PropSettlementResp)
	err := c.cc.Invoke(ctx, "/ga.smash_egg.SmashEgg/PropSettlement", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smashEggClient) GetUserPropExpireDetail(ctx context.Context, in *GetUserPropExpireDetailReq, opts ...grpc.CallOption) (*GetUserPropExpireDetailResp, error) {
	out := new(GetUserPropExpireDetailResp)
	err := c.cc.Invoke(ctx, "/ga.smash_egg.SmashEgg/GetUserPropExpireDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SmashEggServer is the server API for SmashEgg service.
type SmashEggServer interface {
	// 获取消费记录
	GetConsumeRecord(context.Context, *GetConsumeRecordReq) (*GetConsumeRecordResp, error)
	// 获取中奖记录
	GetWinningRecord(context.Context, *GetWinningRecordReq) (*GetWinningRecordResp, error)
	// 充值
	Recharge(context.Context, *RechargeReq) (*RechargeResp, error)
	// 砸
	Smash(context.Context, *SmashReq) (*SmashResp, error)
	// 获取状态
	GetSmashStatus(context.Context, *GetSmashStatusReq) (*GetSmashStatusResp, error)
	// 获取配置
	GetSmashConfig(context.Context, *GetSmashConfigReq) (*GetSmashConfigResp, error)
	// 设置配置
	SetSmashConfig(context.Context, *SetSmashConfigReq) (*SetSmashConfigResp, error)
	// 获取奖励池
	GetPrizePool(context.Context, *GetPrizePoolReq) (*GetPrizePoolResp, error)
	// 获取未生效奖励池
	GetPrizePoolTmp(context.Context, *GetPrizePoolTmpReq) (*GetPrizePoolTmpResp, error)
	// 设置奖励池
	SetPrizePool(context.Context, *SetPrizePoolReq) (*SetPrizePoolResp, error)
	DelTmpPrizePool(context.Context, *DelTmpPrizePoolReq) (*DelTmpPrizePoolResp, error)
	// 指定奖励池进行收益模拟
	SimulateWithPrizePool(context.Context, *SimulateWithPrizePoolReq) (*SimulateWithPrizePoolResp, error)
	// 白名单检测
	CheckWhitelist(context.Context, *CheckWhitelistReq) (*CheckWhitelistResp, error)
	CheckTester(context.Context, *CheckTesterReq) (*CheckTesterResp, error)
	// 豁免条件值
	GetSmashEggExemptValue(context.Context, *GetSmashEggExemptValueReq) (*GetSmashEggExemptValueResp, error)
	// 发放包裹数据对账
	GetAwardTotalCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetAwardOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// T豆消费数据对账
	GetConsumeTotalCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetConsumeOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	UpdateSmashEggActivityConfig(context.Context, *UpdateSmashEggActivityConfigReq) (*UpdateSmashEggActivityConfigResp, error)
	GetSmashEggActivityConfig(context.Context, *GetSmashEggActivityConfigReq) (*GetSmashEggActivityConfigResp, error)
	CheckSmashEggActivityConfig(context.Context, *CheckSmashEggActivityConfigReq) (*CheckSmashEggActivityConfigResp, error)
	DelSmashEggActivityConfig(context.Context, *DelSmashEggActivityConfigReq) (*DelSmashEggActivityConfigResp, error)
	GetSmashEggActivityConfWithCache(context.Context, *GetSmashEggActivityConfWithCacheReq) (*GetSmashEggActivityConfWithCacheResp, error)
	// 中奖光效管理
	AddSmashLightEffectsV2(context.Context, *AddSmashLightEffectsV2Req) (*AddSmashLightEffectsV2Resp, error)
	UpdateSmashLightEffectV2(context.Context, *UpdateSmashLightEffectV2Req) (*UpdateSmashLightEffectV2Resp, error)
	GetAllSmashLightEffectsV2(context.Context, *GetAllSmashLightEffectsV2Req) (*GetAllSmashLightEffectsV2Resp, error)
	DelLightEffectByconfId(context.Context, *DelLightEffectByconfIdReq) (*DelLightEffectByconfIdResp, error)
	// 用户道具
	GetUserProp(context.Context, *GetUserPropReq) (*GetUserPropResp, error)
	// 获取当前活动类型
	GetCurActivityType(context.Context, *GetCurActivityTypeReq) (*GetCurActivityTypeResp, error)
	// 道具退还相关
	GetRefundPropInfo(context.Context, *GetRefundPropInfoReq) (*GetRefundPropInfoResp, error)
	ConfirmRefundProp(context.Context, *ConfirmRefundPropReq) (*ConfirmRefundPropResp, error)
	SetRefundStatus(context.Context, *SetRefundStatusReq) (*SetRefundStatusResp, error)
	PropSettlement(context.Context, *PropSettlementReq) (*PropSettlementResp, error)
	// ----------------- 用户道具查询后台 -----------------
	// 分页获取用户道具有效期
	GetUserPropExpireDetail(context.Context, *GetUserPropExpireDetailReq) (*GetUserPropExpireDetailResp, error)
}

func RegisterSmashEggServer(s *grpc.Server, srv SmashEggServer) {
	s.RegisterService(&_SmashEgg_serviceDesc, srv)
}

func _SmashEgg_GetConsumeRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConsumeRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmashEggServer).GetConsumeRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.smash_egg.SmashEgg/GetConsumeRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmashEggServer).GetConsumeRecord(ctx, req.(*GetConsumeRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmashEgg_GetWinningRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWinningRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmashEggServer).GetWinningRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.smash_egg.SmashEgg/GetWinningRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmashEggServer).GetWinningRecord(ctx, req.(*GetWinningRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmashEgg_Recharge_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RechargeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmashEggServer).Recharge(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.smash_egg.SmashEgg/Recharge",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmashEggServer).Recharge(ctx, req.(*RechargeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmashEgg_Smash_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SmashReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmashEggServer).Smash(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.smash_egg.SmashEgg/Smash",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmashEggServer).Smash(ctx, req.(*SmashReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmashEgg_GetSmashStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSmashStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmashEggServer).GetSmashStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.smash_egg.SmashEgg/GetSmashStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmashEggServer).GetSmashStatus(ctx, req.(*GetSmashStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmashEgg_GetSmashConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSmashConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmashEggServer).GetSmashConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.smash_egg.SmashEgg/GetSmashConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmashEggServer).GetSmashConfig(ctx, req.(*GetSmashConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmashEgg_SetSmashConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetSmashConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmashEggServer).SetSmashConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.smash_egg.SmashEgg/SetSmashConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmashEggServer).SetSmashConfig(ctx, req.(*SetSmashConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmashEgg_GetPrizePool_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPrizePoolReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmashEggServer).GetPrizePool(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.smash_egg.SmashEgg/GetPrizePool",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmashEggServer).GetPrizePool(ctx, req.(*GetPrizePoolReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmashEgg_GetPrizePoolTmp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPrizePoolTmpReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmashEggServer).GetPrizePoolTmp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.smash_egg.SmashEgg/GetPrizePoolTmp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmashEggServer).GetPrizePoolTmp(ctx, req.(*GetPrizePoolTmpReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmashEgg_SetPrizePool_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetPrizePoolReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmashEggServer).SetPrizePool(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.smash_egg.SmashEgg/SetPrizePool",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmashEggServer).SetPrizePool(ctx, req.(*SetPrizePoolReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmashEgg_DelTmpPrizePool_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelTmpPrizePoolReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmashEggServer).DelTmpPrizePool(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.smash_egg.SmashEgg/DelTmpPrizePool",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmashEggServer).DelTmpPrizePool(ctx, req.(*DelTmpPrizePoolReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmashEgg_SimulateWithPrizePool_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SimulateWithPrizePoolReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmashEggServer).SimulateWithPrizePool(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.smash_egg.SmashEgg/SimulateWithPrizePool",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmashEggServer).SimulateWithPrizePool(ctx, req.(*SimulateWithPrizePoolReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmashEgg_CheckWhitelist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckWhitelistReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmashEggServer).CheckWhitelist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.smash_egg.SmashEgg/CheckWhitelist",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmashEggServer).CheckWhitelist(ctx, req.(*CheckWhitelistReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmashEgg_CheckTester_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckTesterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmashEggServer).CheckTester(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.smash_egg.SmashEgg/CheckTester",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmashEggServer).CheckTester(ctx, req.(*CheckTesterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmashEgg_GetSmashEggExemptValue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSmashEggExemptValueReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmashEggServer).GetSmashEggExemptValue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.smash_egg.SmashEgg/GetSmashEggExemptValue",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmashEggServer).GetSmashEggExemptValue(ctx, req.(*GetSmashEggExemptValueReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmashEgg_GetAwardTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmashEggServer).GetAwardTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.smash_egg.SmashEgg/GetAwardTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmashEggServer).GetAwardTotalCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmashEgg_GetAwardOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmashEggServer).GetAwardOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.smash_egg.SmashEgg/GetAwardOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmashEggServer).GetAwardOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmashEgg_GetConsumeTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmashEggServer).GetConsumeTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.smash_egg.SmashEgg/GetConsumeTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmashEggServer).GetConsumeTotalCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmashEgg_GetConsumeOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmashEggServer).GetConsumeOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.smash_egg.SmashEgg/GetConsumeOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmashEggServer).GetConsumeOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmashEgg_UpdateSmashEggActivityConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSmashEggActivityConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmashEggServer).UpdateSmashEggActivityConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.smash_egg.SmashEgg/UpdateSmashEggActivityConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmashEggServer).UpdateSmashEggActivityConfig(ctx, req.(*UpdateSmashEggActivityConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmashEgg_GetSmashEggActivityConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSmashEggActivityConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmashEggServer).GetSmashEggActivityConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.smash_egg.SmashEgg/GetSmashEggActivityConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmashEggServer).GetSmashEggActivityConfig(ctx, req.(*GetSmashEggActivityConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmashEgg_CheckSmashEggActivityConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckSmashEggActivityConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmashEggServer).CheckSmashEggActivityConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.smash_egg.SmashEgg/CheckSmashEggActivityConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmashEggServer).CheckSmashEggActivityConfig(ctx, req.(*CheckSmashEggActivityConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmashEgg_DelSmashEggActivityConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelSmashEggActivityConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmashEggServer).DelSmashEggActivityConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.smash_egg.SmashEgg/DelSmashEggActivityConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmashEggServer).DelSmashEggActivityConfig(ctx, req.(*DelSmashEggActivityConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmashEgg_GetSmashEggActivityConfWithCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSmashEggActivityConfWithCacheReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmashEggServer).GetSmashEggActivityConfWithCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.smash_egg.SmashEgg/GetSmashEggActivityConfWithCache",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmashEggServer).GetSmashEggActivityConfWithCache(ctx, req.(*GetSmashEggActivityConfWithCacheReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmashEgg_AddSmashLightEffectsV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddSmashLightEffectsV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmashEggServer).AddSmashLightEffectsV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.smash_egg.SmashEgg/AddSmashLightEffectsV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmashEggServer).AddSmashLightEffectsV2(ctx, req.(*AddSmashLightEffectsV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmashEgg_UpdateSmashLightEffectV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSmashLightEffectV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmashEggServer).UpdateSmashLightEffectV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.smash_egg.SmashEgg/UpdateSmashLightEffectV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmashEggServer).UpdateSmashLightEffectV2(ctx, req.(*UpdateSmashLightEffectV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmashEgg_GetAllSmashLightEffectsV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllSmashLightEffectsV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmashEggServer).GetAllSmashLightEffectsV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.smash_egg.SmashEgg/GetAllSmashLightEffectsV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmashEggServer).GetAllSmashLightEffectsV2(ctx, req.(*GetAllSmashLightEffectsV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmashEgg_DelLightEffectByconfId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelLightEffectByconfIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmashEggServer).DelLightEffectByconfId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.smash_egg.SmashEgg/DelLightEffectByconfId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmashEggServer).DelLightEffectByconfId(ctx, req.(*DelLightEffectByconfIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmashEgg_GetUserProp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserPropReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmashEggServer).GetUserProp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.smash_egg.SmashEgg/GetUserProp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmashEggServer).GetUserProp(ctx, req.(*GetUserPropReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmashEgg_GetCurActivityType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCurActivityTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmashEggServer).GetCurActivityType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.smash_egg.SmashEgg/GetCurActivityType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmashEggServer).GetCurActivityType(ctx, req.(*GetCurActivityTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmashEgg_GetRefundPropInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRefundPropInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmashEggServer).GetRefundPropInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.smash_egg.SmashEgg/GetRefundPropInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmashEggServer).GetRefundPropInfo(ctx, req.(*GetRefundPropInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmashEgg_ConfirmRefundProp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfirmRefundPropReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmashEggServer).ConfirmRefundProp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.smash_egg.SmashEgg/ConfirmRefundProp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmashEggServer).ConfirmRefundProp(ctx, req.(*ConfirmRefundPropReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmashEgg_SetRefundStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetRefundStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmashEggServer).SetRefundStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.smash_egg.SmashEgg/SetRefundStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmashEggServer).SetRefundStatus(ctx, req.(*SetRefundStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmashEgg_PropSettlement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PropSettlementReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmashEggServer).PropSettlement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.smash_egg.SmashEgg/PropSettlement",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmashEggServer).PropSettlement(ctx, req.(*PropSettlementReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmashEgg_GetUserPropExpireDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserPropExpireDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmashEggServer).GetUserPropExpireDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.smash_egg.SmashEgg/GetUserPropExpireDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmashEggServer).GetUserPropExpireDetail(ctx, req.(*GetUserPropExpireDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _SmashEgg_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.smash_egg.SmashEgg",
	HandlerType: (*SmashEggServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetConsumeRecord",
			Handler:    _SmashEgg_GetConsumeRecord_Handler,
		},
		{
			MethodName: "GetWinningRecord",
			Handler:    _SmashEgg_GetWinningRecord_Handler,
		},
		{
			MethodName: "Recharge",
			Handler:    _SmashEgg_Recharge_Handler,
		},
		{
			MethodName: "Smash",
			Handler:    _SmashEgg_Smash_Handler,
		},
		{
			MethodName: "GetSmashStatus",
			Handler:    _SmashEgg_GetSmashStatus_Handler,
		},
		{
			MethodName: "GetSmashConfig",
			Handler:    _SmashEgg_GetSmashConfig_Handler,
		},
		{
			MethodName: "SetSmashConfig",
			Handler:    _SmashEgg_SetSmashConfig_Handler,
		},
		{
			MethodName: "GetPrizePool",
			Handler:    _SmashEgg_GetPrizePool_Handler,
		},
		{
			MethodName: "GetPrizePoolTmp",
			Handler:    _SmashEgg_GetPrizePoolTmp_Handler,
		},
		{
			MethodName: "SetPrizePool",
			Handler:    _SmashEgg_SetPrizePool_Handler,
		},
		{
			MethodName: "DelTmpPrizePool",
			Handler:    _SmashEgg_DelTmpPrizePool_Handler,
		},
		{
			MethodName: "SimulateWithPrizePool",
			Handler:    _SmashEgg_SimulateWithPrizePool_Handler,
		},
		{
			MethodName: "CheckWhitelist",
			Handler:    _SmashEgg_CheckWhitelist_Handler,
		},
		{
			MethodName: "CheckTester",
			Handler:    _SmashEgg_CheckTester_Handler,
		},
		{
			MethodName: "GetSmashEggExemptValue",
			Handler:    _SmashEgg_GetSmashEggExemptValue_Handler,
		},
		{
			MethodName: "GetAwardTotalCount",
			Handler:    _SmashEgg_GetAwardTotalCount_Handler,
		},
		{
			MethodName: "GetAwardOrderIds",
			Handler:    _SmashEgg_GetAwardOrderIds_Handler,
		},
		{
			MethodName: "GetConsumeTotalCount",
			Handler:    _SmashEgg_GetConsumeTotalCount_Handler,
		},
		{
			MethodName: "GetConsumeOrderIds",
			Handler:    _SmashEgg_GetConsumeOrderIds_Handler,
		},
		{
			MethodName: "UpdateSmashEggActivityConfig",
			Handler:    _SmashEgg_UpdateSmashEggActivityConfig_Handler,
		},
		{
			MethodName: "GetSmashEggActivityConfig",
			Handler:    _SmashEgg_GetSmashEggActivityConfig_Handler,
		},
		{
			MethodName: "CheckSmashEggActivityConfig",
			Handler:    _SmashEgg_CheckSmashEggActivityConfig_Handler,
		},
		{
			MethodName: "DelSmashEggActivityConfig",
			Handler:    _SmashEgg_DelSmashEggActivityConfig_Handler,
		},
		{
			MethodName: "GetSmashEggActivityConfWithCache",
			Handler:    _SmashEgg_GetSmashEggActivityConfWithCache_Handler,
		},
		{
			MethodName: "AddSmashLightEffectsV2",
			Handler:    _SmashEgg_AddSmashLightEffectsV2_Handler,
		},
		{
			MethodName: "UpdateSmashLightEffectV2",
			Handler:    _SmashEgg_UpdateSmashLightEffectV2_Handler,
		},
		{
			MethodName: "GetAllSmashLightEffectsV2",
			Handler:    _SmashEgg_GetAllSmashLightEffectsV2_Handler,
		},
		{
			MethodName: "DelLightEffectByconfId",
			Handler:    _SmashEgg_DelLightEffectByconfId_Handler,
		},
		{
			MethodName: "GetUserProp",
			Handler:    _SmashEgg_GetUserProp_Handler,
		},
		{
			MethodName: "GetCurActivityType",
			Handler:    _SmashEgg_GetCurActivityType_Handler,
		},
		{
			MethodName: "GetRefundPropInfo",
			Handler:    _SmashEgg_GetRefundPropInfo_Handler,
		},
		{
			MethodName: "ConfirmRefundProp",
			Handler:    _SmashEgg_ConfirmRefundProp_Handler,
		},
		{
			MethodName: "SetRefundStatus",
			Handler:    _SmashEgg_SetRefundStatus_Handler,
		},
		{
			MethodName: "PropSettlement",
			Handler:    _SmashEgg_PropSettlement_Handler,
		},
		{
			MethodName: "GetUserPropExpireDetail",
			Handler:    _SmashEgg_GetUserPropExpireDetail_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "smash-egg/smash-egg.proto",
}

func init() {
	proto.RegisterFile("smash-egg/smash-egg.proto", fileDescriptor_smash_egg_2982dd97161610b4)
}

var fileDescriptor_smash_egg_2982dd97161610b4 = []byte{
	// 4060 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x3b, 0xcb, 0x6e, 0x23, 0x49,
	0x72, 0xe2, 0x43, 0x14, 0x19, 0x14, 0x29, 0x2a, 0xf5, 0x68, 0x8a, 0xdd, 0x6a, 0xa9, 0xab, 0x67,
	0xba, 0x35, 0xda, 0x6e, 0xcd, 0x8e, 0x06, 0xbb, 0xc0, 0x78, 0xbd, 0x07, 0xb6, 0xc4, 0xee, 0x96,
	0x47, 0x2f, 0x17, 0xa9, 0x6e, 0xcf, 0xec, 0xc2, 0xe5, 0xea, 0xaa, 0x24, 0x55, 0xdb, 0xc5, 0xaa,
	0x74, 0x55, 0x51, 0x8f, 0x81, 0x81, 0x3d, 0xf9, 0xb4, 0x3e, 0x19, 0x30, 0x0c, 0x18, 0x3e, 0xfb,
	0xe0, 0x83, 0x01, 0xc3, 0x80, 0x8f, 0xfe, 0x01, 0xff, 0x85, 0x01, 0x1f, 0x7d, 0xf4, 0x6d, 0x0f,
	0x46, 0x46, 0x66, 0x51, 0xf5, 0x22, 0x25, 0xcd, 0x60, 0x60, 0x60, 0x6f, 0xcc, 0x88, 0xc8, 0xc8,
	0x88, 0xc8, 0xc8, 0xc8, 0xc8, 0x88, 0x22, 0xac, 0xf9, 0x43, 0xdd, 0x3f, 0x7f, 0x49, 0x07, 0x83,
	0xcf, 0xc7, 0xbf, 0x76, 0x98, 0xe7, 0x06, 0x2e, 0x99, 0x1f, 0xe8, 0x3b, 0x08, 0xd3, 0xe8, 0x60,
	0xd0, 0xda, 0xf0, 0xa8, 0xe1, 0x3a, 0x86, 0x65, 0xd3, 0x97, 0x17, 0xbb, 0x9f, 0x47, 0x07, 0x82,
	0x5c, 0xf9, 0xfb, 0x1c, 0xd4, 0xf6, 0x5c, 0xc7, 0x1f, 0x0d, 0xa9, 0x4a, 0x0d, 0xd7, 0x33, 0x49,
	0x1d, 0xf2, 0x96, 0xd9, 0xcc, 0x6d, 0xe6, 0xb6, 0x8a, 0x6a, 0xde, 0x32, 0x49, 0x03, 0x0a, 0x23,
	0xcb, 0x6c, 0xe6, 0x37, 0x73, 0x5b, 0x35, 0x95, 0xff, 0x24, 0xab, 0x50, 0xd2, 0x87, 0xee, 0xc8,
	0x09, 0x9a, 0x05, 0x04, 0xca, 0x11, 0xa7, 0xec, 0x53, 0xda, 0x2c, 0x0a, 0xca, 0x3e, 0xa5, 0x64,
	0x0d, 0xca, 0xae, 0x67, 0x52, 0x4f, 0xb3, 0xcc, 0xe6, 0xec, 0x66, 0x6e, 0xab, 0xa2, 0xce, 0xe1,
	0xf8, 0xc0, 0x24, 0x1b, 0x50, 0x35, 0x3c, 0xaa, 0x07, 0x54, 0x0b, 0xac, 0x21, 0x6d, 0x96, 0x70,
	0x12, 0x08, 0x50, 0xcf, 0x1a, 0x52, 0xe5, 0xdf, 0x8b, 0x50, 0x7b, 0x6f, 0x39, 0x8e, 0xe5, 0x0c,
	0xee, 0x2c, 0xd9, 0x0b, 0x28, 0xf9, 0xee, 0xc8, 0x33, 0x28, 0x4a, 0x56, 0xdf, 0x5d, 0xde, 0x89,
	0x5a, 0x63, 0xa7, 0x8b, 0x38, 0x55, 0xd2, 0x90, 0x67, 0x50, 0xec, 0xdb, 0xfa, 0x00, 0x05, 0xae,
	0xef, 0x92, 0x38, 0xed, 0x6b, 0x5b, 0x1f, 0xa8, 0x88, 0x27, 0x0f, 0x60, 0x8e, 0xe9, 0xc6, 0xc7,
	0x50, 0x89, 0x9a, 0x5a, 0xe2, 0xc3, 0x03, 0x93, 0xac, 0x03, 0x20, 0xe2, 0xd2, 0xf5, 0x82, 0x73,
	0xa9, 0x42, 0x85, 0x43, 0xde, 0x73, 0x00, 0x79, 0x08, 0x38, 0xd0, 0x1c, 0x7d, 0x48, 0x9b, 0x73,
	0xa8, 0x7e, 0x99, 0x03, 0x8e, 0xf5, 0x21, 0x9a, 0x06, 0x91, 0xcc, 0x32, 0x9a, 0x65, 0x61, 0x1a,
	0x3e, 0x3e, 0xb5, 0x0c, 0x6e, 0x1a, 0x44, 0x49, 0x23, 0x57, 0x84, 0x69, 0x38, 0xa8, 0x2d, 0x0c,
	0x1d, 0x32, 0x36, 0xa9, 0x6f, 0x34, 0xe1, 0x86, 0xf1, 0x3e, 0xf5, 0x8d, 0xa4, 0x61, 0xab, 0x49,
	0xc3, 0x72, 0xa9, 0x8d, 0x73, 0xdd, 0x71, 0xa8, 0xcd, 0x35, 0x9a, 0x17, 0x52, 0x4b, 0xc8, 0x81,
	0x19, 0xdb, 0xb3, 0x5a, 0x7c, 0xcf, 0x9e, 0x41, 0x71, 0xe8, 0x9a, 0xb4, 0x59, 0xcf, 0x32, 0xd8,
	0x91, 0x6b, 0x52, 0x15, 0xf1, 0xe4, 0x19, 0x2c, 0xd8, 0xd6, 0xe0, 0x3c, 0xd0, 0x68, 0xbf, 0x4f,
	0x8d, 0x80, 0x73, 0x5a, 0xc0, 0x65, 0x6a, 0x08, 0xee, 0x20, 0xf4, 0xc0, 0x24, 0xdb, 0xb0, 0x18,
	0xa3, 0x0b, 0xe8, 0x55, 0xd0, 0x6c, 0xe0, 0x9a, 0x0b, 0x11, 0xca, 0x1e, 0xbd, 0x0a, 0xc8, 0x16,
	0x34, 0x62, 0xb4, 0x23, 0xcf, 0x6e, 0x2e, 0x22, 0x69, 0x3d, 0x42, 0x7a, 0xe6, 0xd9, 0xca, 0xff,
	0x14, 0xa1, 0xb4, 0xe7, 0x3a, 0x7d, 0x6b, 0xc0, 0x55, 0x1d, 0xba, 0x1e, 0x3b, 0xd7, 0xce, 0xad,
	0xc0, 0x47, 0xcf, 0xa9, 0xa9, 0x15, 0x84, 0xbc, 0xb5, 0x02, 0x9f, 0x7c, 0x0a, 0x75, 0x81, 0x36,
	0x47, 0x9e, 0x1e, 0x58, 0xae, 0x23, 0x7d, 0xa9, 0x86, 0xd0, 0x7d, 0x09, 0xe4, 0x16, 0x35, 0x75,
	0xcb, 0xbe, 0xd6, 0x6c, 0x6b, 0x68, 0x85, 0x4e, 0x0f, 0x08, 0x3a, 0xe4, 0x10, 0xf2, 0x4b, 0xa8,
	0x23, 0x4a, 0x63, 0xb6, 0x1e, 0xf4, 0x5d, 0x6f, 0xd8, 0x2c, 0x6e, 0x16, 0xb6, 0xea, 0xbb, 0xab,
	0x71, 0x0b, 0x9d, 0x4a, 0x2c, 0x37, 0xc3, 0xd0, 0x0a, 0xc2, 0x21, 0xd9, 0x81, 0x8a, 0x98, 0xae,
	0x33, 0xd6, 0x9c, 0xc5, 0x99, 0x8b, 0xf1, 0x99, 0x6d, 0xc6, 0xd4, 0x32, 0xd2, 0xb4, 0x19, 0xe3,
	0xe7, 0xcf, 0x0f, 0xf4, 0x60, 0xe4, 0x4b, 0x97, 0x93, 0x23, 0xf2, 0x14, 0x6a, 0x26, 0xed, 0x5b,
	0x86, 0x15, 0x48, 0x49, 0xe7, 0x10, 0x3d, 0x2f, 0x81, 0x42, 0xd6, 0xe7, 0xb0, 0x10, 0x12, 0x5d,
	0xea, 0x1e, 0x3f, 0x5d, 0xe8, 0x7e, 0x35, 0xb5, 0x2e, 0xc1, 0xef, 0x05, 0x94, 0x3c, 0x81, 0xf9,
	0x4b, 0xaa, 0xdb, 0xc1, 0xb9, 0x64, 0x56, 0xc1, 0x73, 0x57, 0x15, 0x30, 0xc1, 0x6b, 0x1d, 0xc0,
	0x67, 0x94, 0x9a, 0x9a, 0x1f, 0x50, 0x86, 0x8e, 0x58, 0x53, 0x2b, 0x08, 0xe9, 0x06, 0x94, 0x71,
	0x4f, 0x12, 0xe8, 0x11, 0x93, 0x6e, 0x38, 0x87, 0xe3, 0x33, 0xc6, 0x3d, 0x78, 0xa8, 0x5f, 0x69,
	0x38, 0x44, 0x17, 0xcc, 0xab, 0xe5, 0xa1, 0x7e, 0xd5, 0xe5, 0x63, 0xf2, 0x08, 0x80, 0x23, 0x3d,
	0xdd, 0x31, 0x35, 0x07, 0x7d, 0xb0, 0x86, 0x58, 0x55, 0x77, 0xcc, 0x63, 0xf4, 0xef, 0x73, 0xdd,
	0x1b, 0x4a, 0xb1, 0xea, 0x28, 0x16, 0x20, 0x48, 0x48, 0xb5, 0x01, 0x55, 0x9b, 0x5e, 0x50, 0x5b,
	0x12, 0x08, 0xcf, 0x03, 0x04, 0x09, 0x82, 0xa7, 0x50, 0x73, 0x2f, 0xa8, 0xa7, 0xdb, 0xb6, 0x14,
	0xa0, 0x21, 0xec, 0x24, 0x81, 0x42, 0x88, 0x2d, 0x68, 0x78, 0x94, 0x73, 0x1d, 0x50, 0x2d, 0x3c,
	0xfd, 0x8b, 0xc2, 0x50, 0x21, 0xfc, 0x14, 0xa3, 0x80, 0xf2, 0xfb, 0x3c, 0xcc, 0x9e, 0x7a, 0xd6,
	0x77, 0x34, 0x15, 0xa0, 0xc2, 0x00, 0x93, 0xbf, 0x25, 0xc0, 0xbc, 0x18, 0x6f, 0x68, 0x76, 0xd8,
	0x42, 0xdc, 0x78, 0x9b, 0x57, 0xa1, 0x74, 0x49, 0xb9, 0xcb, 0xcb, 0x48, 0x2b, 0x47, 0x7f, 0x68,
	0x61, 0x2a, 0x0c, 0x36, 0xf3, 0xd3, 0x83, 0x8d, 0xf2, 0xcf, 0x39, 0x58, 0x7a, 0x43, 0x83, 0xd8,
	0x25, 0xa6, 0xd2, 0xbf, 0x0c, 0x6f, 0x87, 0xdc, 0xcd, 0xed, 0x10, 0x8d, 0x6c, 0xf9, 0x78, 0x64,
	0x5b, 0x85, 0x92, 0xdb, 0xef, 0xfb, 0x54, 0x9c, 0xee, 0xa2, 0x2a, 0x47, 0x64, 0x19, 0x66, 0x85,
	0x17, 0x09, 0x53, 0x8b, 0x01, 0x37, 0xe8, 0x07, 0x3a, 0xb0, 0x1c, 0x21, 0xba, 0x30, 0x76, 0x05,
	0x21, 0x28, 0xf9, 0x1a, 0x94, 0xa9, 0x63, 0x46, 0xef, 0xb5, 0x39, 0xea, 0x98, 0x78, 0xa9, 0x19,
	0xb0, 0x9c, 0x96, 0xd5, 0x67, 0xe4, 0x6b, 0x58, 0x32, 0x04, 0x50, 0xf3, 0x10, 0xaa, 0xd9, 0x96,
	0x1f, 0x34, 0x73, 0x9b, 0x85, 0xad, 0xea, 0xee, 0xc3, 0xb8, 0xee, 0xf1, 0xd9, 0x8b, 0x46, 0x74,
	0x78, 0x68, 0xf9, 0x81, 0xf2, 0xb7, 0x79, 0xb4, 0x48, 0xec, 0xf2, 0xcc, 0xb6, 0xc8, 0x8d, 0xda,
	0xf9, 0x6c, 0xb5, 0x0b, 0x93, 0xd5, 0x2e, 0x4e, 0x53, 0x7b, 0x36, 0xa6, 0x36, 0x21, 0x50, 0xf4,
	0x74, 0x4f, 0x58, 0xa3, 0xac, 0xe2, 0x6f, 0x0e, 0x63, 0xfa, 0x20, 0xf4, 0x38, 0xfc, 0x3d, 0xde,
	0xf3, 0xf2, 0x2d, 0x17, 0xcc, 0x17, 0x61, 0x5c, 0xc7, 0xe3, 0x55, 0x99, 0x78, 0xbc, 0x44, 0xac,
	0xe7, 0x3f, 0x95, 0x4b, 0xb4, 0x7c, 0xc2, 0x26, 0xc2, 0xf2, 0x97, 0x02, 0x78, 0xbb, 0xe5, 0xe3,
	0xb3, 0x17, 0x2f, 0xa3, 0x43, 0x6e, 0xf9, 0xb1, 0x4e, 0xf9, 0x1b, 0x9d, 0x94, 0x7f, 0xca, 0x41,
	0xfd, 0xcd, 0x48, 0xf7, 0x74, 0x27, 0xa0, 0xd4, 0x3c, 0x70, 0xfa, 0xee, 0x58, 0xcd, 0xdc, 0xbd,
	0xd4, 0xcc, 0xdf, 0x41, 0x4d, 0xbe, 0x73, 0x22, 0xa6, 0xc9, 0x9d, 0xc3, 0x41, 0x3a, 0xe2, 0x15,
	0xd3, 0x11, 0x4f, 0xf9, 0x14, 0x16, 0xdf, 0xd0, 0xa0, 0xcb, 0x59, 0xcb, 0x88, 0x93, 0xe5, 0x33,
	0xca, 0x7f, 0xe7, 0x81, 0x24, 0xe9, 0x7c, 0xc6, 0xaf, 0x0b, 0x63, 0xe4, 0x79, 0xd4, 0x09, 0xa2,
	0x97, 0x6d, 0x55, 0xc2, 0xf0, 0xba, 0xfd, 0x04, 0xea, 0xc3, 0x6b, 0xcd, 0xa3, 0x43, 0xdd, 0x72,
	0x04, 0x91, 0xb8, 0x6e, 0xe7, 0x87, 0xd7, 0x2a, 0x02, 0x91, 0x4a, 0x81, 0xda, 0xf0, 0x5a, 0x0b,
	0x5c, 0x53, 0xbf, 0x16, 0x44, 0x42, 0x93, 0xea, 0xf0, 0xba, 0xc7, 0x61, 0x48, 0x13, 0x37, 0x4c,
	0xf1, 0x2e, 0x86, 0xf9, 0x24, 0xbc, 0xeb, 0x13, 0x3e, 0x3a, 0x8f, 0xd0, 0x8e, 0x74, 0xd4, 0xb1,
	0xf9, 0x4a, 0x53, 0xcd, 0x37, 0x97, 0x71, 0x61, 0x1c, 0xc3, 0xf2, 0x60, 0xbc, 0xcd, 0x9a, 0xe5,
	0xf4, 0x5d, 0xe1, 0x49, 0x65, 0xf4, 0xa4, 0x47, 0x71, 0xe9, 0xe2, 0x0e, 0xa1, 0x92, 0x41, 0x6c,
	0x8c, 0xa7, 0xf8, 0x15, 0x54, 0x55, 0x79, 0xd1, 0xf0, 0x8d, 0xf8, 0x12, 0x4a, 0xc2, 0x3f, 0xd1,
	0xb2, 0xb7, 0x04, 0x05, 0x49, 0xaa, 0xb4, 0x61, 0xfe, 0x86, 0x87, 0xcf, 0x22, 0x99, 0x7b, 0x2e,
	0x96, 0xb9, 0x4f, 0x8e, 0x8c, 0xca, 0xbf, 0xe5, 0xa1, 0x8c, 0x7b, 0x3d, 0x31, 0x82, 0x48, 0x8e,
	0xf9, 0x18, 0xc7, 0x78, 0x92, 0x59, 0x48, 0x26, 0x99, 0xdc, 0x5b, 0x24, 0x3a, 0xb8, 0x66, 0x61,
	0x30, 0xa9, 0x4a, 0x58, 0xef, 0x9a, 0x51, 0xb2, 0x0b, 0xe5, 0x71, 0x3a, 0x35, 0x8b, 0x3b, 0x3c,
	0x29, 0x9d, 0x1a, 0xd3, 0x45, 0xf2, 0xff, 0xd2, 0xdd, 0xf2, 0x7f, 0x3c, 0x86, 0x73, 0xf7, 0x3a,
	0x86, 0xe5, 0xbb, 0x44, 0x9b, 0xff, 0xcd, 0x41, 0x45, 0x5a, 0xcd, 0x67, 0xdc, 0x7f, 0xa4, 0xd7,
	0x73, 0x05, 0x0d, 0x2a, 0x0d, 0x38, 0x2f, 0x80, 0x7b, 0x08, 0x9b, 0x14, 0x88, 0xf2, 0xdf, 0x2b,
	0x10, 0x25, 0x4f, 0x63, 0x21, 0x7d, 0x1a, 0xc7, 0xae, 0x5e, 0x9c, 0xea, 0xea, 0xb3, 0x19, 0xae,
	0xde, 0x84, 0x39, 0xcb, 0x7f, 0x65, 0x39, 0x03, 0x57, 0x46, 0xf4, 0x70, 0x18, 0x8d, 0x21, 0x22,
	0x05, 0xcf, 0x8e, 0x21, 0x1f, 0x6e, 0x42, 0x48, 0x48, 0xe6, 0x33, 0xbe, 0x7b, 0x06, 0x8e, 0xa4,
	0x8b, 0x2f, 0xa7, 0x5c, 0x9c, 0x53, 0x4a, 0x1a, 0xf2, 0x18, 0x40, 0x37, 0x0c, 0xea, 0xfb, 0xd6,
	0x07, 0x5b, 0x44, 0xdc, 0xb2, 0x1a, 0x81, 0x28, 0x6d, 0x58, 0xec, 0xa6, 0x44, 0xb9, 0xd7, 0x12,
	0xca, 0x32, 0x90, 0x6e, 0x4a, 0x4c, 0x45, 0x87, 0x85, 0x37, 0x34, 0xc0, 0x8c, 0xef, 0xd4, 0x75,
	0x6d, 0xce, 0x36, 0x4c, 0xf4, 0x72, 0xb7, 0x24, 0x7a, 0xa1, 0xc7, 0xe5, 0x6f, 0xc9, 0x69, 0x5e,
	0x43, 0x23, 0xbe, 0x84, 0xcf, 0xc8, 0x2e, 0x00, 0xe3, 0x80, 0xe8, 0xfd, 0xb4, 0x94, 0x38, 0x11,
	0x1c, 0xaf, 0x56, 0x90, 0x0c, 0x63, 0x88, 0x89, 0x76, 0x1e, 0xf3, 0xe9, 0x0d, 0xd9, 0x8f, 0x21,
	0xed, 0x6f, 0x30, 0xdd, 0x88, 0xaf, 0xf2, 0xfd, 0x04, 0xe6, 0x59, 0x61, 0xf8, 0x16, 0xe4, 0x21,
	0x5a, 0xc4, 0x14, 0x10, 0x20, 0x51, 0x15, 0xc8, 0xc1, 0x42, 0xf7, 0x7b, 0x5a, 0x3f, 0x2e, 0x50,
	0xfe, 0x4e, 0x02, 0x85, 0x36, 0x28, 0xdc, 0x12, 0x23, 0x12, 0x82, 0x17, 0x53, 0x82, 0x13, 0x68,
	0x74, 0x13, 0x5b, 0xaa, 0xfc, 0x75, 0x0e, 0xc8, 0x3e, 0xe5, 0x06, 0xfb, 0x31, 0xbd, 0x29, 0x29,
	0x5b, 0x21, 0x25, 0xdb, 0x0a, 0x2c, 0xa5, 0xc4, 0xf0, 0x99, 0xf2, 0x5f, 0x39, 0x68, 0x76, 0xad,
	0xe1, 0xc8, 0xd6, 0x03, 0xfa, 0xde, 0x0a, 0xce, 0x63, 0x42, 0x3e, 0x81, 0x79, 0xb1, 0x18, 0xf3,
	0xdc, 0xbe, 0x15, 0x5e, 0x28, 0x55, 0x84, 0x9d, 0x22, 0x88, 0xaf, 0x2b, 0x48, 0xf8, 0xb2, 0xe1,
	0x65, 0x0f, 0x08, 0xe2, 0xcb, 0xfa, 0x63, 0x45, 0x0b, 0xf7, 0xda, 0xb8, 0xe2, 0xbd, 0x36, 0x6e,
	0xf6, 0x16, 0xe7, 0x1d, 0xc0, 0xda, 0x04, 0x1d, 0x45, 0xe0, 0xa6, 0x57, 0x8c, 0x5b, 0x2e, 0xa2,
	0xe5, 0xac, 0x3a, 0x2f, 0x80, 0x52, 0xcd, 0x4f, 0xa1, 0xae, 0xf3, 0xe8, 0xc8, 0x1f, 0x8a, 0x82,
	0x2a, 0x8f, 0x54, 0x35, 0x09, 0x15, 0x64, 0x3c, 0x34, 0xee, 0x9d, 0x53, 0xe3, 0xe3, 0xfb, 0x73,
	0x2b, 0xa0, 0x5c, 0x91, 0xec, 0xd0, 0xb8, 0x0d, 0x24, 0x49, 0xe6, 0x33, 0x1e, 0xac, 0xe9, 0x95,
	0x38, 0x46, 0x3c, 0xce, 0x89, 0x81, 0xa2, 0x40, 0x1d, 0x69, 0x7b, 0xd4, 0x0f, 0xa8, 0x97, 0xcd,
	0xef, 0x39, 0x2c, 0xc4, 0x68, 0x26, 0x32, 0x7b, 0x09, 0x6b, 0x61, 0x4c, 0xee, 0x0c, 0x06, 0x9d,
	0x2b, 0x3a, 0x64, 0xc1, 0x3b, 0xdd, 0x1e, 0xd1, 0x6c, 0xbe, 0xbf, 0x85, 0xd6, 0x24, 0x72, 0x9f,
	0x61, 0x65, 0x00, 0xad, 0x3d, 0x76, 0xe4, 0xb2, 0x5a, 0x41, 0x08, 0x26, 0x63, 0xa9, 0x0b, 0x31,
	0x9f, 0x71, 0x21, 0xae, 0x03, 0xd8, 0x23, 0xe3, 0xa3, 0x76, 0xc1, 0xb9, 0x86, 0x29, 0x04, 0x87,
	0xe0, 0x32, 0xca, 0xef, 0xca, 0xb0, 0x84, 0xcb, 0xb7, 0x8d, 0xc0, 0xba, 0xb0, 0x82, 0x6b, 0x59,
	0xf3, 0x79, 0x0a, 0x35, 0x5d, 0x42, 0xc4, 0x93, 0x36, 0x87, 0x09, 0xcd, 0x7c, 0x08, 0xc4, 0x67,
	0x6d, 0xfc, 0x29, 0xc3, 0x57, 0x2f, 0x4c, 0x7a, 0xca, 0x14, 0x10, 0x39, 0x7e, 0xca, 0x28, 0x50,
	0x1b, 0xfb, 0x3d, 0xe3, 0xb9, 0x4d, 0x31, 0xee, 0xf8, 0xec, 0x00, 0x2f, 0x51, 0xc4, 0x8e, 0xcb,
	0x4a, 0xf2, 0x12, 0xe5, 0xc0, 0x71, 0x55, 0xe9, 0x33, 0x58, 0x44, 0xa2, 0x81, 0xd5, 0x0f, 0xc6,
	0x15, 0x06, 0x91, 0x76, 0xd6, 0x39, 0xe2, 0x8d, 0xd5, 0x0f, 0x44, 0x85, 0x81, 0xaf, 0xf9, 0x81,
	0x5f, 0xaf, 0x63, 0x32, 0x91, 0x7f, 0x56, 0x11, 0x28, 0x69, 0x1e, 0x42, 0xe5, 0xc2, 0xf2, 0x2d,
	0xd7, 0xe1, 0x78, 0x51, 0xd1, 0x29, 0x0b, 0xc0, 0x81, 0x49, 0x1e, 0xc0, 0x9c, 0x67, 0xf9, 0x38,
	0x55, 0x3c, 0xd3, 0x4b, 0x7c, 0x28, 0xaa, 0xb0, 0x88, 0xf0, 0xa9, 0xe1, 0xd1, 0x40, 0x3e, 0xd2,
	0x81, 0x83, 0xba, 0x08, 0x21, 0x5f, 0xc0, 0x8a, 0x58, 0xfa, 0x83, 0x47, 0xf5, 0x8f, 0x3c, 0x39,
	0x71, 0xe8, 0x25, 0xe7, 0x23, 0x1e, 0xec, 0x04, 0x91, 0xaf, 0x24, 0xee, 0x98, 0x5e, 0x1e, 0x98,
	0x64, 0x13, 0xe6, 0x83, 0x0f, 0x54, 0x77, 0x34, 0x9d, 0xb1, 0xb0, 0xc2, 0x58, 0x51, 0x01, 0x61,
	0x6d, 0xc6, 0xed, 0xc3, 0x4f, 0x4c, 0xb8, 0x45, 0xde, 0xc8, 0xa6, 0xbe, 0x2c, 0x34, 0x8e, 0x37,
	0x4e, 0xe5, 0x40, 0xf2, 0x02, 0x88, 0x47, 0x07, 0x23, 0x5b, 0xf7, 0x34, 0x71, 0xfc, 0x99, 0xeb,
	0xda, 0x58, 0xf0, 0xa9, 0xa8, 0x0d, 0x89, 0x19, 0x9f, 0x57, 0xf2, 0x0c, 0x16, 0x06, 0xae, 0x6d,
	0x46, 0x49, 0x17, 0x04, 0x57, 0x0e, 0xbe, 0xa1, 0xdb, 0x80, 0xaa, 0xc8, 0xe6, 0xc4, 0xca, 0xa2,
	0xdc, 0x28, 0x12, 0x3c, 0xb1, 0xec, 0x16, 0x34, 0xd0, 0xef, 0x98, 0x6b, 0x39, 0x81, 0xa4, 0x0a,
	0x2b, 0x8d, 0x23, 0xe3, 0xe3, 0x29, 0x07, 0x0b, 0xca, 0x2f, 0x60, 0x05, 0x97, 0x4c, 0x91, 0x13,
	0x24, 0x27, 0x1c, 0x79, 0x18, 0x9f, 0xf2, 0x1c, 0x16, 0x0c, 0x77, 0xc8, 0x5c, 0xdf, 0x0a, 0xa8,
	0x24, 0x5e, 0x12, 0xbc, 0xc7, 0x60, 0x41, 0xf8, 0x53, 0x58, 0x36, 0x75, 0xef, 0xa3, 0x96, 0xa4,
	0x5e, 0x16, 0xac, 0x39, 0x6e, 0x2f, 0x3e, 0xe3, 0x05, 0x20, 0x54, 0xa3, 0x0e, 0xf5, 0x06, 0xd7,
	0x9a, 0x1f, 0xb8, 0x0e, 0xf5, 0x9b, 0x2b, 0xc2, 0x5c, 0x1c, 0xd3, 0x41, 0x44, 0x17, 0xe1, 0x64,
	0x07, 0x96, 0x50, 0xf6, 0xc4, 0x46, 0xac, 0x22, 0xf9, 0x22, 0x47, 0xb5, 0x63, 0x9b, 0xb1, 0x01,
	0xd5, 0x11, 0x33, 0xc7, 0xf5, 0x9a, 0x07, 0x78, 0x2a, 0x40, 0x80, 0xc2, 0xb2, 0x32, 0xfa, 0x33,
	0xf3, 0x2c, 0x83, 0x36, 0x9b, 0xb2, 0xca, 0xe4, 0xb9, 0xfc, 0x52, 0x31, 0x28, 0xb7, 0xaa, 0x30,
	0x7b, 0x64, 0x7f, 0xd6, 0x84, 0xe6, 0x08, 0xbf, 0xd9, 0xa0, 0x2f, 0x61, 0x55, 0x50, 0xa6, 0xcc,
	0xda, 0x42, 0xfa, 0x25, 0xc4, 0xc6, 0xed, 0xaa, 0xfc, 0x1a, 0x36, 0xce, 0x50, 0x96, 0x30, 0x22,
	0xc5, 0xa3, 0x02, 0x8f, 0x61, 0x5f, 0x25, 0x72, 0xbf, 0x27, 0x89, 0xc7, 0x41, 0x3a, 0x96, 0x8c,
	0x13, 0x41, 0x05, 0x36, 0xa7, 0x73, 0xf7, 0x99, 0xf2, 0xfb, 0x1c, 0xcc, 0xf1, 0x7b, 0x65, 0xaf,
	0x3f, 0xf8, 0x31, 0x1f, 0xf8, 0x5f, 0xc1, 0xda, 0x4d, 0x5d, 0x92, 0xdb, 0xdb, 0x19, 0x0d, 0x35,
	0x97, 0xf1, 0x90, 0xc2, 0xd3, 0xfc, 0xc2, 0x56, 0x4d, 0x5d, 0x1d, 0x17, 0x28, 0x3d, 0x97, 0x1d,
	0x8f, 0x86, 0x27, 0x02, 0x4b, 0x5e, 0xc2, 0x92, 0xe0, 0x6b, 0xb8, 0x7e, 0x30, 0x9e, 0x2c, 0x03,
	0x58, 0xc3, 0x17, 0x69, 0xae, 0x1f, 0xc8, 0x59, 0xdc, 0x9f, 0x22, 0x0f, 0xda, 0x78, 0x69, 0xb1,
	0x71, 0x83, 0x91, 0x55, 0xd0, 0xd7, 0xf0, 0x28, 0x72, 0x1f, 0xa4, 0xad, 0xff, 0x0c, 0x16, 0x2e,
	0xad, 0x40, 0x9e, 0x3a, 0x8d, 0xdb, 0x55, 0x5e, 0x0b, 0x35, 0x0e, 0xc6, 0x4d, 0xe4, 0xc4, 0xca,
	0xdf, 0xe5, 0x60, 0x7d, 0x0a, 0x23, 0x9f, 0xfd, 0x80, 0x7d, 0x24, 0x5f, 0x41, 0x8d, 0xdb, 0x5d,
	0x33, 0xfa, 0x83, 0x68, 0x12, 0xb8, 0x92, 0xde, 0xa0, 0xbd, 0xfe, 0x40, 0xad, 0x0e, 0xc5, 0x0f,
	0x4c, 0xa5, 0x8f, 0xe0, 0xe9, 0x04, 0xb1, 0x78, 0xda, 0xb0, 0xa7, 0x1b, 0xe7, 0xf4, 0x3e, 0x6a,
	0xfe, 0x63, 0x0e, 0x3e, 0xb9, 0x9d, 0xdf, 0xff, 0x9b, 0xb6, 0xbf, 0x82, 0xc7, 0x98, 0x35, 0xfc,
	0x28, 0xa7, 0xe9, 0x09, 0x6c, 0x4c, 0x65, 0xee, 0x33, 0xe5, 0x31, 0x3c, 0xda, 0xa7, 0xf6, 0xc4,
	0xd5, 0x95, 0x0d, 0x58, 0x9f, 0x82, 0xf7, 0x99, 0xf2, 0x9f, 0x39, 0x68, 0x20, 0xfa, 0xf0, 0xa6,
	0x39, 0xc4, 0xaf, 0x41, 0x2e, 0x82, 0x36, 0xce, 0x64, 0x50, 0xa2, 0x03, 0x13, 0xeb, 0x9d, 0xf4,
	0x82, 0xda, 0x32, 0x0f, 0x11, 0x03, 0x1e, 0xd1, 0x64, 0xde, 0xec, 0x51, 0xf1, 0x84, 0xae, 0xa8,
	0x15, 0x01, 0x51, 0xa9, 0x4f, 0x08, 0x14, 0xb1, 0x61, 0x55, 0x14, 0xc5, 0x3e, 0xfe, 0x9b, 0xaf,
	0xe0, 0x32, 0x6d, 0xe4, 0x53, 0x4f, 0xf6, 0x3b, 0x4b, 0x2e, 0x3b, 0xf3, 0xa9, 0xc7, 0xaf, 0x67,
	0x97, 0x69, 0x1e, 0x1d, 0x5a, 0x8e, 0xb8, 0xe5, 0x2b, 0x6a, 0xd9, 0x65, 0x2a, 0x8e, 0x93, 0xb1,
	0x75, 0x2e, 0x19, 0x5b, 0x95, 0x13, 0x58, 0x6b, 0x9b, 0x66, 0x52, 0x1f, 0xff, 0xdd, 0x2e, 0xdf,
	0x89, 0x5d, 0x28, 0x8e, 0xfd, 0xac, 0xba, 0xfb, 0x38, 0x63, 0x1f, 0x22, 0x73, 0x54, 0xa4, 0x55,
	0x1e, 0x41, 0x6b, 0x12, 0x43, 0x9f, 0x29, 0x7f, 0x0a, 0x0f, 0x23, 0xe1, 0x2e, 0x42, 0xf0, 0xfd,
	0x17, 0x7c, 0x0c, 0x8f, 0x26, 0xb3, 0xf4, 0x99, 0xf2, 0x4b, 0x0c, 0x1f, 0x6d, 0xdb, 0x9e, 0xa0,
	0x24, 0x4f, 0x28, 0xa9, 0xee, 0x19, 0xe7, 0x3c, 0x1d, 0x0c, 0x3b, 0x79, 0x02, 0xf2, 0x4e, 0xb7,
	0x95, 0x5f, 0x63, 0xd0, 0x98, 0x34, 0xdd, 0x67, 0xe4, 0x17, 0x50, 0xc1, 0xad, 0x8f, 0xbc, 0x45,
	0x6f, 0x13, 0xbc, 0xcc, 0x27, 0xc8, 0xb3, 0xbf, 0xb6, 0x4f, 0xed, 0x08, 0xee, 0xd5, 0xb5, 0x70,
	0x1c, 0x2e, 0xd9, 0x44, 0xa7, 0x8a, 0xf8, 0x42, 0x21, 0xea, 0x0b, 0xdc, 0xf8, 0x93, 0xd8, 0xf9,
	0x4c, 0x79, 0x07, 0x65, 0x4e, 0xc5, 0xa3, 0x30, 0xb6, 0x74, 0x64, 0x9a, 0x29, 0x79, 0x33, 0x91,
	0x61, 0x36, 0xa0, 0xc0, 0x43, 0xb7, 0x6c, 0x7d, 0x3b, 0xa3, 0x21, 0x3e, 0xf2, 0xae, 0x98, 0xe5,
	0xd1, 0x68, 0xd6, 0x0a, 0x02, 0x84, 0x3e, 0xf4, 0x0b, 0xa8, 0xbf, 0xa1, 0x41, 0xc8, 0x3a, 0xbb,
	0x9a, 0x17, 0x59, 0x2f, 0x1f, 0x5d, 0x4f, 0x39, 0xc1, 0x9a, 0xc7, 0xcd, 0x64, 0x9f, 0x91, 0x3f,
	0x86, 0x3a, 0xd7, 0x4d, 0xdc, 0x23, 0x11, 0xb3, 0x26, 0xaa, 0x74, 0xe3, 0x39, 0xf3, 0x23, 0xf9,
	0x0b, 0x4d, 0xfa, 0x00, 0x56, 0xde, 0xd0, 0x60, 0x6f, 0xe4, 0x85, 0x67, 0xb7, 0x77, 0xcd, 0x78,
	0x00, 0x55, 0xfe, 0x02, 0x56, 0xb3, 0x10, 0x3e, 0x23, 0xaf, 0x61, 0xd1, 0x18, 0x79, 0x37, 0x09,
	0x0b, 0x16, 0x0e, 0xc5, 0x0d, 0xdb, 0x4a, 0x94, 0x71, 0x46, 0x5e, 0xef, 0x9c, 0x0e, 0x29, 0x4e,
	0x5d, 0x30, 0xe2, 0xbc, 0x94, 0x7f, 0xc8, 0x43, 0x5d, 0xa5, 0xfd, 0x91, 0x63, 0x62, 0xba, 0xee,
	0xf4, 0xdd, 0xbb, 0xbd, 0x19, 0x9e, 0xc0, 0xbc, 0xdb, 0xef, 0xdb, 0x96, 0x43, 0xa3, 0xaf, 0x86,
	0xaa, 0x84, 0x61, 0x0e, 0xf4, 0x10, 0x2a, 0xe2, 0x5a, 0xd5, 0xe5, 0x16, 0x54, 0xd4, 0x32, 0x07,
	0xe0, 0xfc, 0x67, 0xb0, 0x20, 0x1f, 0x3d, 0x68, 0x37, 0xc3, 0x09, 0xbb, 0x4a, 0xf2, 0x2d, 0xc4,
	0x2d, 0xb5, 0xe7, 0x04, 0x11, 0x3a, 0xe4, 0xc5, 0xe9, 0x66, 0xa3, 0x74, 0x5c, 0x6a, 0x4e, 0xb7,
	0x1b, 0x6b, 0x03, 0xa7, 0x8c, 0x20, 0x54, 0x4c, 0xf4, 0x0e, 0x37, 0xa0, 0xda, 0xb7, 0x1c, 0x4b,
	0xbe, 0xc9, 0xc3, 0x48, 0x23, 0x40, 0xe8, 0x25, 0xab, 0xd8, 0x26, 0x89, 0x9b, 0x87, 0x6f, 0xcb,
	0x01, 0xee, 0x57, 0x12, 0xee, 0x33, 0xf2, 0x53, 0x28, 0x5a, 0x4e, 0xdf, 0x95, 0xc1, 0xe0, 0x51,
	0x96, 0x0c, 0x63, 0x7a, 0xa4, 0x54, 0xde, 0x62, 0x55, 0x2d, 0x26, 0x1e, 0x06, 0x95, 0x50, 0x9b,
	0xdc, 0x5d, 0xb5, 0x51, 0x56, 0x60, 0x29, 0xc5, 0xc9, 0x67, 0xca, 0x5b, 0x58, 0xc6, 0x9b, 0xc0,
	0x1b, 0xde, 0xac, 0xcf, 0x97, 0xb8, 0xbf, 0xa8, 0x0f, 0x60, 0x25, 0x83, 0x93, 0xcf, 0x94, 0xbf,
	0xc9, 0xc1, 0x22, 0x1f, 0x74, 0x69, 0x10, 0xd8, 0x74, 0x48, 0x1d, 0x7c, 0xcd, 0xff, 0x0c, 0x4f,
	0x7c, 0xc4, 0x2f, 0x13, 0x6b, 0xdc, 0x50, 0xa3, 0x67, 0x96, 0x5c, 0x86, 0x95, 0x6e, 0x8c, 0x6d,
	0xfc, 0xb5, 0xa5, 0x7d, 0xa4, 0xd7, 0xb2, 0xfe, 0x5e, 0x11, 0x90, 0xaf, 0xe9, 0x75, 0xca, 0xef,
	0x0a, 0x29, 0xbf, 0x53, 0x9e, 0x01, 0x49, 0x4a, 0xe3, 0x33, 0x7e, 0xbe, 0x87, 0xfe, 0x40, 0xfa,
	0x32, 0xff, 0xa9, 0xfc, 0x6b, 0x0e, 0x5f, 0xed, 0xe1, 0x99, 0xec, 0x60, 0x74, 0xd8, 0xa7, 0x81,
	0x6e, 0xd9, 0xf7, 0x0b, 0x08, 0xfc, 0x85, 0x2c, 0x1f, 0xd0, 0x7e, 0xf8, 0x42, 0x16, 0xcf, 0x67,
	0x9f, 0xac, 0x40, 0x09, 0x1f, 0xcf, 0x3e, 0xba, 0x77, 0x41, 0x9d, 0xe5, 0x4f, 0x67, 0x5f, 0x74,
	0x83, 0x07, 0x54, 0xf3, 0xad, 0xef, 0xc2, 0xde, 0x4b, 0x99, 0x03, 0xba, 0xd6, 0x77, 0xb2, 0xcd,
	0x3c, 0xa0, 0x9a, 0x65, 0x5e, 0x85, 0x2d, 0x53, 0x3e, 0x3e, 0x30, 0xaf, 0x94, 0x2b, 0x78, 0x38,
	0x51, 0xe4, 0x1f, 0x1a, 0x86, 0xb8, 0x50, 0x81, 0x1b, 0xe8, 0x36, 0x9e, 0x32, 0xa1, 0x61, 0x19,
	0x01, 0x7b, 0x4e, 0xb0, 0xfd, 0x18, 0x4a, 0xa2, 0x63, 0x40, 0x00, 0x4a, 0x47, 0xba, 0x33, 0xd2,
	0xed, 0xc6, 0x0c, 0x29, 0x43, 0xb1, 0x7d, 0xd6, 0x3b, 0x69, 0xe4, 0xb6, 0xd7, 0xa1, 0x88, 0x29,
	0x39, 0x40, 0xe9, 0xf8, 0x44, 0x3d, 0x6a, 0x1f, 0x36, 0x66, 0x48, 0x05, 0x66, 0x8f, 0x4e, 0xd4,
	0xd3, 0xb7, 0x8d, 0xdc, 0xf6, 0x33, 0x28, 0xf2, 0xdc, 0x8a, 0x2c, 0x40, 0x55, 0xa0, 0xb5, 0xa3,
	0x93, 0xfd, 0x4e, 0x63, 0x86, 0xd4, 0xa0, 0xf2, 0xe6, 0xe4, 0x70, 0x5f, 0x0c, 0x73, 0xdb, 0x4f,
	0xa0, 0x24, 0x9c, 0x97, 0x33, 0xea, 0x1c, 0xb7, 0x5f, 0x1d, 0x72, 0xa2, 0x2a, 0xcc, 0xed, 0x1f,
	0x74, 0x71, 0x90, 0xdb, 0xfe, 0x39, 0x94, 0xc7, 0x5f, 0x8b, 0x2c, 0x43, 0xe3, 0xcc, 0xf9, 0xe8,
	0xb8, 0x97, 0x8e, 0x16, 0xc2, 0x04, 0x79, 0xdb, 0x31, 0x3d, 0xd7, 0x32, 0x1b, 0x39, 0x32, 0x07,
	0x05, 0xeb, 0xa4, 0xdb, 0xc8, 0x6f, 0xff, 0x1c, 0x0a, 0x6d, 0xc6, 0xb8, 0x04, 0xe1, 0x94, 0x36,
	0x63, 0x8d, 0x19, 0x52, 0x82, 0x7c, 0xaf, 0xd7, 0xc8, 0xf1, 0x59, 0x6f, 0xcf, 0xda, 0xc7, 0xdf,
	0x9c, 0x9c, 0x35, 0xf2, 0x5c, 0xf4, 0x6f, 0xdb, 0x07, 0xdf, 0xb4, 0x1b, 0x85, 0xed, 0x6f, 0x61,
	0x3e, 0x1a, 0x43, 0xc9, 0x63, 0x68, 0xed, 0x9d, 0xa9, 0x5a, 0xef, 0x6d, 0xe7, 0xa8, 0xa3, 0xf5,
	0xbe, 0x39, 0xed, 0x68, 0x67, 0xc7, 0xdd, 0xd3, 0xce, 0xde, 0xc1, 0xeb, 0x83, 0xce, 0x7e, 0x63,
	0x86, 0xcb, 0x94, 0xc0, 0xb7, 0x1b, 0xb9, 0x0c, 0xe8, 0xab, 0x46, 0x7e, 0xfb, 0x3f, 0x72, 0x30,
	0x1f, 0x3d, 0xb2, 0x64, 0x1d, 0xd6, 0xd4, 0xce, 0xeb, 0xb3, 0xe3, 0x7d, 0xad, 0xdb, 0x6b, 0xf7,
	0xce, 0xba, 0x09, 0xde, 0x8f, 0xa1, 0x15, 0x47, 0x1f, 0x9f, 0xf4, 0xb4, 0xee, 0xd9, 0xd1, 0xd1,
	0x41, 0xaf, 0xb3, 0xdf, 0xc8, 0x91, 0x16, 0xac, 0xc6, 0xf1, 0xed, 0xd3, 0xd3, 0xc3, 0x6f, 0x0e,
	0x8e, 0xdf, 0x34, 0xf2, 0xe4, 0x21, 0x3c, 0x88, 0xe3, 0xc4, 0x88, 0x23, 0x0b, 0xe9, 0x89, 0x62,
	0xd4, 0xd9, 0x6f, 0x14, 0xd3, 0x38, 0x5c, 0xf4, 0xed, 0xc9, 0xfb, 0xc6, 0xec, 0xb6, 0x05, 0xf5,
	0xf8, 0x41, 0x26, 0x1b, 0xf0, 0xb0, 0xdb, 0xe9, 0xf5, 0x0e, 0x3b, 0x47, 0x9d, 0xe3, 0x5e, 0x96,
	0x7d, 0x5a, 0xb0, 0x9a, 0x24, 0x78, 0xad, 0x76, 0x3a, 0xdf, 0x76, 0x84, 0xfc, 0x49, 0xdc, 0xde,
	0x09, 0xd7, 0xae, 0x91, 0xdf, 0xfd, 0x97, 0x96, 0x6c, 0xbe, 0x75, 0x06, 0x03, 0xf2, 0x2b, 0x6c,
	0x0a, 0xc4, 0x3f, 0xd6, 0x4b, 0x64, 0xdd, 0x19, 0xdf, 0x41, 0xb4, 0x94, 0xdb, 0x48, 0x7c, 0xa6,
	0xcc, 0x48, 0xe6, 0xf1, 0xef, 0xed, 0xd2, 0xcc, 0x93, 0x9f, 0x14, 0x64, 0x30, 0x4f, 0x75, 0xd8,
	0x95, 0x19, 0xb2, 0x07, 0xe5, 0xb0, 0x0d, 0x49, 0xd6, 0x92, 0x61, 0x77, 0xdc, 0xe2, 0x6c, 0xb5,
	0x26, 0xa1, 0x90, 0xc9, 0x1f, 0xc1, 0x2c, 0x9a, 0x82, 0xac, 0x66, 0xe4, 0x6d, 0x7c, 0xfa, 0x83,
	0x4c, 0x38, 0xce, 0x3d, 0xc3, 0xdc, 0x27, 0xd2, 0xb2, 0x26, 0x1b, 0x29, 0xc1, 0xe3, 0x8d, 0xef,
	0xd6, 0xe6, 0x74, 0x82, 0x24, 0x5b, 0x59, 0x7c, 0x9c, 0xc0, 0x76, 0xfc, 0x70, 0x99, 0xc4, 0x36,
	0xf2, 0x72, 0x41, 0xb6, 0xdd, 0xa9, 0x6c, 0xbb, 0xb7, 0xb1, 0xed, 0x66, 0xb1, 0x3d, 0x81, 0xf9,
	0x68, 0x9b, 0x86, 0xac, 0xa7, 0x44, 0x89, 0x16, 0xf8, 0x5b, 0x8f, 0xa7, 0xa1, 0x91, 0xe1, 0x9f,
	0xc5, 0x1b, 0x61, 0xbd, 0x21, 0x23, 0x9b, 0x93, 0x27, 0x89, 0xe6, 0x53, 0xeb, 0xc9, 0x2d, 0x14,
	0xa1, 0xa8, 0xdd, 0x29, 0xa2, 0x76, 0xa7, 0x8b, 0xda, 0xcd, 0x14, 0x35, 0xd1, 0xe1, 0x48, 0x8a,
	0x9a, 0xee, 0xc3, 0x24, 0x45, 0xcd, 0x6a, 0x91, 0xcc, 0x90, 0xdf, 0xc0, 0x4a, 0x66, 0xff, 0x80,
	0x3c, 0x4b, 0x08, 0x35, 0xa1, 0x91, 0xd2, 0x7a, 0x7e, 0x27, 0xba, 0xd0, 0x31, 0xe2, 0xbd, 0x81,
	0xa4, 0x63, 0xa4, 0x1a, 0x0c, 0x49, 0xc7, 0x48, 0xb7, 0x16, 0x94, 0x19, 0x72, 0x08, 0xd5, 0x48,
	0x8b, 0x80, 0x3c, 0xca, 0x98, 0x32, 0xee, 0x30, 0xb4, 0xd6, 0xa7, 0x60, 0x91, 0xdb, 0x10, 0x13,
	0xf8, 0x8c, 0xc6, 0x00, 0x79, 0x9e, 0xed, 0xfb, 0xa9, 0x6e, 0x43, 0x6b, 0xeb, 0x6e, 0x84, 0xb8,
	0xdc, 0x01, 0xb6, 0x38, 0xdb, 0x97, 0xba, 0x67, 0xf6, 0xf0, 0xe2, 0x16, 0x1f, 0x34, 0xf0, 0xe8,
	0x21, 0xbe, 0x75, 0x7e, 0xb7, 0xbb, 0xc3, 0x73, 0x24, 0x55, 0x77, 0x44, 0x94, 0x59, 0x8d, 0xa1,
	0x90, 0x5c, 0xb2, 0xfa, 0x13, 0x8c, 0x81, 0xc8, 0xea, 0x44, 0x7c, 0xfd, 0xe0, 0x4f, 0x63, 0x14,
	0x47, 0x85, 0x33, 0x24, 0xaf, 0xaf, 0xa3, 0x1f, 0x7a, 0xfd, 0x50, 0xc1, 0x0e, 0x51, 0x47, 0xc9,
	0xec, 0x07, 0x8b, 0xf6, 0xdb, 0xd8, 0x53, 0x3c, 0x55, 0x3e, 0x21, 0x2f, 0x13, 0x99, 0xd3, 0xf4,
	0xb2, 0x6a, 0x6b, 0xe7, 0x3e, 0xe4, 0x28, 0xc0, 0x45, 0xac, 0xd3, 0x94, 0x58, 0x7d, 0x7b, 0xe2,
	0xde, 0xa7, 0x97, 0xfe, 0xc9, 0x9d, 0x69, 0x71, 0xdd, 0xbf, 0x82, 0x87, 0x53, 0xea, 0x4e, 0xe4,
	0x45, 0x86, 0x67, 0x4f, 0x5e, 0xfb, 0xe5, 0x3d, 0xa8, 0x43, 0xad, 0x27, 0x96, 0xac, 0x92, 0x5a,
	0x4f, 0xab, 0x7d, 0x25, 0xb5, 0x9e, 0x5e, 0x07, 0x9b, 0x21, 0xbf, 0xcb, 0xc1, 0xe6, 0x6d, 0x95,
	0x46, 0xf2, 0xc5, 0x9d, 0x2c, 0x19, 0xad, 0x74, 0xb6, 0x76, 0xef, 0x3b, 0x25, 0x8c, 0x0e, 0xd9,
	0x85, 0xa7, 0x64, 0x74, 0x98, 0x58, 0xef, 0x4a, 0x46, 0x87, 0x29, 0x75, 0xac, 0x19, 0xe2, 0x43,
	0x73, 0x52, 0xd9, 0x89, 0x7c, 0x36, 0xd1, 0x71, 0x93, 0x15, 0xaf, 0xd6, 0xf6, 0x5d, 0x49, 0x23,
	0xfe, 0x9d, 0x5d, 0x8c, 0xca, 0xf0, 0xef, 0x89, 0x45, 0xaf, 0x0c, 0xff, 0x9e, 0x5c, 0xe1, 0x12,
	0xb6, 0xcd, 0xae, 0x2b, 0x25, 0x6d, 0x3b, 0xb1, 0x98, 0x95, 0xb4, 0xed, 0x94, 0x32, 0x15, 0x5e,
	0x1b, 0x91, 0x87, 0x59, 0xf2, 0xda, 0x88, 0xd7, 0x9a, 0x5a, 0xeb, 0x53, 0xb0, 0xc8, 0x4d, 0x17,
	0x31, 0x2e, 0x5e, 0xab, 0x21, 0x4f, 0xd3, 0xc9, 0x6b, 0xaa, 0x64, 0xd4, 0xfa, 0xe4, 0x76, 0x22,
	0x5c, 0xe2, 0xcf, 0xf1, 0xe3, 0xa4, 0x44, 0xe9, 0x27, 0x9d, 0xc1, 0xa6, 0x8a, 0x1f, 0xad, 0xa7,
	0xb7, 0xd2, 0x84, 0xfc, 0x53, 0xd5, 0x82, 0x24, 0xff, 0xac, 0xc2, 0x44, 0x92, 0x7f, 0x76, 0xc9,
	0x01, 0x93, 0x98, 0x44, 0xb9, 0x83, 0xa4, 0xf3, 0xbe, 0x44, 0x5d, 0x25, 0x99, 0xc4, 0x64, 0xd5,
	0x4b, 0x30, 0xb1, 0x88, 0xd7, 0x0f, 0x92, 0x89, 0x45, 0xaa, 0xd6, 0x91, 0x4c, 0x2c, 0xd2, 0xe5,
	0x07, 0x65, 0x86, 0x30, 0x78, 0x30, 0xe1, 0xe9, 0x4e, 0xb6, 0x26, 0xfa, 0x43, 0xa2, 0x28, 0xd1,
	0xfa, 0xec, 0x8e, 0x94, 0x7c, 0xc5, 0x57, 0x2f, 0xbf, 0xfd, 0xc9, 0xc0, 0xb5, 0x75, 0x67, 0xb0,
	0xf3, 0xb3, 0xdd, 0x20, 0xd8, 0x31, 0xdc, 0xe1, 0xe7, 0xf8, 0x3f, 0x27, 0xc3, 0xb5, 0x3f, 0xf7,
	0xa9, 0x77, 0x61, 0x19, 0xd4, 0xbf, 0xf9, 0xcb, 0xd4, 0x87, 0x12, 0x22, 0xbf, 0xfc, 0xbf, 0x00,
	0x00, 0x00, 0xff, 0xff, 0x39, 0xd3, 0xb1, 0x92, 0x50, 0x35, 0x00, 0x00,
}
