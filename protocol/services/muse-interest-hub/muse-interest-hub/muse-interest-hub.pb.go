// Code generated by protoc-gen-go. DO NOT EDIT.
// source: muse-interest-hub/muse-interest-hub.proto

package muse_interest_hub // import "golang.52tt.com/protocol/services/muse-interest-hub/muse-interest-hub"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/extension/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 兴趣内容相关 开关合集
type GetMuseSwitchHubRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMuseSwitchHubRequest) Reset()         { *m = GetMuseSwitchHubRequest{} }
func (m *GetMuseSwitchHubRequest) String() string { return proto.CompactTextString(m) }
func (*GetMuseSwitchHubRequest) ProtoMessage()    {}
func (*GetMuseSwitchHubRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_b28f4405b1946f5a, []int{0}
}
func (m *GetMuseSwitchHubRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMuseSwitchHubRequest.Unmarshal(m, b)
}
func (m *GetMuseSwitchHubRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMuseSwitchHubRequest.Marshal(b, m, deterministic)
}
func (dst *GetMuseSwitchHubRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMuseSwitchHubRequest.Merge(dst, src)
}
func (m *GetMuseSwitchHubRequest) XXX_Size() int {
	return xxx_messageInfo_GetMuseSwitchHubRequest.Size(m)
}
func (m *GetMuseSwitchHubRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMuseSwitchHubRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetMuseSwitchHubRequest proto.InternalMessageInfo

type GetMuseSwitchHubResponse struct {
	IsOpenMap            map[uint32]bool `protobuf:"bytes,1,rep,name=is_open_map,json=isOpenMap,proto3" json:"is_open_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetMuseSwitchHubResponse) Reset()         { *m = GetMuseSwitchHubResponse{} }
func (m *GetMuseSwitchHubResponse) String() string { return proto.CompactTextString(m) }
func (*GetMuseSwitchHubResponse) ProtoMessage()    {}
func (*GetMuseSwitchHubResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_b28f4405b1946f5a, []int{1}
}
func (m *GetMuseSwitchHubResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMuseSwitchHubResponse.Unmarshal(m, b)
}
func (m *GetMuseSwitchHubResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMuseSwitchHubResponse.Marshal(b, m, deterministic)
}
func (dst *GetMuseSwitchHubResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMuseSwitchHubResponse.Merge(dst, src)
}
func (m *GetMuseSwitchHubResponse) XXX_Size() int {
	return xxx_messageInfo_GetMuseSwitchHubResponse.Size(m)
}
func (m *GetMuseSwitchHubResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMuseSwitchHubResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetMuseSwitchHubResponse proto.InternalMessageInfo

func (m *GetMuseSwitchHubResponse) GetIsOpenMap() map[uint32]bool {
	if m != nil {
		return m.IsOpenMap
	}
	return nil
}

type SetMuseSwitchHubRequest struct {
	SwitchType           uint32   `protobuf:"varint,1,opt,name=switch_type,json=switchType,proto3" json:"switch_type,omitempty"`
	IsOpen               bool     `protobuf:"varint,2,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetMuseSwitchHubRequest) Reset()         { *m = SetMuseSwitchHubRequest{} }
func (m *SetMuseSwitchHubRequest) String() string { return proto.CompactTextString(m) }
func (*SetMuseSwitchHubRequest) ProtoMessage()    {}
func (*SetMuseSwitchHubRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_b28f4405b1946f5a, []int{2}
}
func (m *SetMuseSwitchHubRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMuseSwitchHubRequest.Unmarshal(m, b)
}
func (m *SetMuseSwitchHubRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMuseSwitchHubRequest.Marshal(b, m, deterministic)
}
func (dst *SetMuseSwitchHubRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMuseSwitchHubRequest.Merge(dst, src)
}
func (m *SetMuseSwitchHubRequest) XXX_Size() int {
	return xxx_messageInfo_SetMuseSwitchHubRequest.Size(m)
}
func (m *SetMuseSwitchHubRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMuseSwitchHubRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetMuseSwitchHubRequest proto.InternalMessageInfo

func (m *SetMuseSwitchHubRequest) GetSwitchType() uint32 {
	if m != nil {
		return m.SwitchType
	}
	return 0
}

func (m *SetMuseSwitchHubRequest) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

type SetMuseSwitchHubResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetMuseSwitchHubResponse) Reset()         { *m = SetMuseSwitchHubResponse{} }
func (m *SetMuseSwitchHubResponse) String() string { return proto.CompactTextString(m) }
func (*SetMuseSwitchHubResponse) ProtoMessage()    {}
func (*SetMuseSwitchHubResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_b28f4405b1946f5a, []int{3}
}
func (m *SetMuseSwitchHubResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMuseSwitchHubResponse.Unmarshal(m, b)
}
func (m *SetMuseSwitchHubResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMuseSwitchHubResponse.Marshal(b, m, deterministic)
}
func (dst *SetMuseSwitchHubResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMuseSwitchHubResponse.Merge(dst, src)
}
func (m *SetMuseSwitchHubResponse) XXX_Size() int {
	return xxx_messageInfo_SetMuseSwitchHubResponse.Size(m)
}
func (m *SetMuseSwitchHubResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMuseSwitchHubResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetMuseSwitchHubResponse proto.InternalMessageInfo

type BatGetUserMuseSwitchRequest struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	SwitchType           uint32   `protobuf:"varint,2,opt,name=switch_type,json=switchType,proto3" json:"switch_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetUserMuseSwitchRequest) Reset()         { *m = BatGetUserMuseSwitchRequest{} }
func (m *BatGetUserMuseSwitchRequest) String() string { return proto.CompactTextString(m) }
func (*BatGetUserMuseSwitchRequest) ProtoMessage()    {}
func (*BatGetUserMuseSwitchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_b28f4405b1946f5a, []int{4}
}
func (m *BatGetUserMuseSwitchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetUserMuseSwitchRequest.Unmarshal(m, b)
}
func (m *BatGetUserMuseSwitchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetUserMuseSwitchRequest.Marshal(b, m, deterministic)
}
func (dst *BatGetUserMuseSwitchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetUserMuseSwitchRequest.Merge(dst, src)
}
func (m *BatGetUserMuseSwitchRequest) XXX_Size() int {
	return xxx_messageInfo_BatGetUserMuseSwitchRequest.Size(m)
}
func (m *BatGetUserMuseSwitchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetUserMuseSwitchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetUserMuseSwitchRequest proto.InternalMessageInfo

func (m *BatGetUserMuseSwitchRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatGetUserMuseSwitchRequest) GetSwitchType() uint32 {
	if m != nil {
		return m.SwitchType
	}
	return 0
}

type BatGetUserMuseSwitchResponse struct {
	IsOpenMap            map[uint32]bool `protobuf:"bytes,1,rep,name=is_open_map,json=isOpenMap,proto3" json:"is_open_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatGetUserMuseSwitchResponse) Reset()         { *m = BatGetUserMuseSwitchResponse{} }
func (m *BatGetUserMuseSwitchResponse) String() string { return proto.CompactTextString(m) }
func (*BatGetUserMuseSwitchResponse) ProtoMessage()    {}
func (*BatGetUserMuseSwitchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_b28f4405b1946f5a, []int{5}
}
func (m *BatGetUserMuseSwitchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetUserMuseSwitchResponse.Unmarshal(m, b)
}
func (m *BatGetUserMuseSwitchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetUserMuseSwitchResponse.Marshal(b, m, deterministic)
}
func (dst *BatGetUserMuseSwitchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetUserMuseSwitchResponse.Merge(dst, src)
}
func (m *BatGetUserMuseSwitchResponse) XXX_Size() int {
	return xxx_messageInfo_BatGetUserMuseSwitchResponse.Size(m)
}
func (m *BatGetUserMuseSwitchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetUserMuseSwitchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetUserMuseSwitchResponse proto.InternalMessageInfo

func (m *BatGetUserMuseSwitchResponse) GetIsOpenMap() map[uint32]bool {
	if m != nil {
		return m.IsOpenMap
	}
	return nil
}

func init() {
	proto.RegisterType((*GetMuseSwitchHubRequest)(nil), "muse_interest_hub.muse_interest_hub.GetMuseSwitchHubRequest")
	proto.RegisterType((*GetMuseSwitchHubResponse)(nil), "muse_interest_hub.muse_interest_hub.GetMuseSwitchHubResponse")
	proto.RegisterMapType((map[uint32]bool)(nil), "muse_interest_hub.muse_interest_hub.GetMuseSwitchHubResponse.IsOpenMapEntry")
	proto.RegisterType((*SetMuseSwitchHubRequest)(nil), "muse_interest_hub.muse_interest_hub.SetMuseSwitchHubRequest")
	proto.RegisterType((*SetMuseSwitchHubResponse)(nil), "muse_interest_hub.muse_interest_hub.SetMuseSwitchHubResponse")
	proto.RegisterType((*BatGetUserMuseSwitchRequest)(nil), "muse_interest_hub.muse_interest_hub.BatGetUserMuseSwitchRequest")
	proto.RegisterType((*BatGetUserMuseSwitchResponse)(nil), "muse_interest_hub.muse_interest_hub.BatGetUserMuseSwitchResponse")
	proto.RegisterMapType((map[uint32]bool)(nil), "muse_interest_hub.muse_interest_hub.BatGetUserMuseSwitchResponse.IsOpenMapEntry")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MuseInterestHubClient is the client API for MuseInterestHub service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MuseInterestHubClient interface {
	// 兴趣内容相关 开关合集
	GetMuseSwitchHub(ctx context.Context, in *GetMuseSwitchHubRequest, opts ...grpc.CallOption) (*GetMuseSwitchHubResponse, error)
	SetMuseSwitchHub(ctx context.Context, in *SetMuseSwitchHubRequest, opts ...grpc.CallOption) (*SetMuseSwitchHubResponse, error)
	BatGetUserMuseSwitch(ctx context.Context, in *BatGetUserMuseSwitchRequest, opts ...grpc.CallOption) (*BatGetUserMuseSwitchResponse, error)
}

type museInterestHubClient struct {
	cc *grpc.ClientConn
}

func NewMuseInterestHubClient(cc *grpc.ClientConn) MuseInterestHubClient {
	return &museInterestHubClient{cc}
}

func (c *museInterestHubClient) GetMuseSwitchHub(ctx context.Context, in *GetMuseSwitchHubRequest, opts ...grpc.CallOption) (*GetMuseSwitchHubResponse, error) {
	out := new(GetMuseSwitchHubResponse)
	err := c.cc.Invoke(ctx, "/muse_interest_hub.muse_interest_hub.MuseInterestHub/GetMuseSwitchHub", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museInterestHubClient) SetMuseSwitchHub(ctx context.Context, in *SetMuseSwitchHubRequest, opts ...grpc.CallOption) (*SetMuseSwitchHubResponse, error) {
	out := new(SetMuseSwitchHubResponse)
	err := c.cc.Invoke(ctx, "/muse_interest_hub.muse_interest_hub.MuseInterestHub/SetMuseSwitchHub", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museInterestHubClient) BatGetUserMuseSwitch(ctx context.Context, in *BatGetUserMuseSwitchRequest, opts ...grpc.CallOption) (*BatGetUserMuseSwitchResponse, error) {
	out := new(BatGetUserMuseSwitchResponse)
	err := c.cc.Invoke(ctx, "/muse_interest_hub.muse_interest_hub.MuseInterestHub/BatGetUserMuseSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MuseInterestHubServer is the server API for MuseInterestHub service.
type MuseInterestHubServer interface {
	// 兴趣内容相关 开关合集
	GetMuseSwitchHub(context.Context, *GetMuseSwitchHubRequest) (*GetMuseSwitchHubResponse, error)
	SetMuseSwitchHub(context.Context, *SetMuseSwitchHubRequest) (*SetMuseSwitchHubResponse, error)
	BatGetUserMuseSwitch(context.Context, *BatGetUserMuseSwitchRequest) (*BatGetUserMuseSwitchResponse, error)
}

func RegisterMuseInterestHubServer(s *grpc.Server, srv MuseInterestHubServer) {
	s.RegisterService(&_MuseInterestHub_serviceDesc, srv)
}

func _MuseInterestHub_GetMuseSwitchHub_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMuseSwitchHubRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseInterestHubServer).GetMuseSwitchHub(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_interest_hub.muse_interest_hub.MuseInterestHub/GetMuseSwitchHub",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseInterestHubServer).GetMuseSwitchHub(ctx, req.(*GetMuseSwitchHubRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseInterestHub_SetMuseSwitchHub_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetMuseSwitchHubRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseInterestHubServer).SetMuseSwitchHub(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_interest_hub.muse_interest_hub.MuseInterestHub/SetMuseSwitchHub",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseInterestHubServer).SetMuseSwitchHub(ctx, req.(*SetMuseSwitchHubRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseInterestHub_BatGetUserMuseSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetUserMuseSwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseInterestHubServer).BatGetUserMuseSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_interest_hub.muse_interest_hub.MuseInterestHub/BatGetUserMuseSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseInterestHubServer).BatGetUserMuseSwitch(ctx, req.(*BatGetUserMuseSwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _MuseInterestHub_serviceDesc = grpc.ServiceDesc{
	ServiceName: "muse_interest_hub.muse_interest_hub.MuseInterestHub",
	HandlerType: (*MuseInterestHubServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetMuseSwitchHub",
			Handler:    _MuseInterestHub_GetMuseSwitchHub_Handler,
		},
		{
			MethodName: "SetMuseSwitchHub",
			Handler:    _MuseInterestHub_SetMuseSwitchHub_Handler,
		},
		{
			MethodName: "BatGetUserMuseSwitch",
			Handler:    _MuseInterestHub_BatGetUserMuseSwitch_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "muse-interest-hub/muse-interest-hub.proto",
}

func init() {
	proto.RegisterFile("muse-interest-hub/muse-interest-hub.proto", fileDescriptor_muse_interest_hub_b28f4405b1946f5a)
}

var fileDescriptor_muse_interest_hub_b28f4405b1946f5a = []byte{
	// 451 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x94, 0xdf, 0x6a, 0xd4, 0x40,
	0x14, 0xc6, 0x3b, 0xbb, 0xd8, 0xd6, 0xb3, 0x54, 0xeb, 0x58, 0xd8, 0x34, 0x0a, 0x5d, 0xc6, 0x9b,
	0xf5, 0xa2, 0x09, 0xac, 0x08, 0x22, 0x15, 0xb4, 0x50, 0xb6, 0x85, 0x16, 0x25, 0xa9, 0x17, 0x7a,
	0x13, 0xb2, 0xdb, 0x43, 0x3b, 0x98, 0x9d, 0x19, 0x33, 0x33, 0xd5, 0x7d, 0x0c, 0x1f, 0xc0, 0xe7,
	0xf0, 0x0d, 0xbc, 0x12, 0x5f, 0xc6, 0x3b, 0xaf, 0x24, 0xff, 0x10, 0x9b, 0xac, 0x2c, 0x0d, 0xbd,
	0x4a, 0xe6, 0x7c, 0xc9, 0x37, 0xbf, 0x99, 0xef, 0x70, 0xe0, 0xf1, 0xcc, 0x6a, 0xdc, 0xe5, 0xc2,
	0x60, 0x8a, 0xda, 0xec, 0x5e, 0xd8, 0x89, 0x5f, 0xab, 0x78, 0x2a, 0x95, 0x46, 0xd2, 0x47, 0x99,
	0x10, 0x55, 0x42, 0x94, 0x09, 0xb5, 0x8a, 0xbb, 0x83, 0x9f, 0x0d, 0x0a, 0xcd, 0xa5, 0xf0, 0xa5,
	0x32, 0x5c, 0x0a, 0x5d, 0x3d, 0x0b, 0x17, 0xb6, 0x0d, 0xfd, 0x31, 0x9a, 0x13, 0xab, 0x31, 0xfc,
	0xc4, 0xcd, 0xf4, 0xe2, 0xd0, 0x4e, 0x02, 0xfc, 0x68, 0x51, 0x1b, 0xf6, 0x9d, 0x80, 0x53, 0xd7,
	0xb4, 0x92, 0x42, 0x23, 0x4d, 0xa0, 0xc7, 0x75, 0x24, 0x15, 0x8a, 0x68, 0x16, 0x2b, 0x87, 0x0c,
	0xba, 0xc3, 0xde, 0xe8, 0xd8, 0x5b, 0x82, 0xc9, 0x5b, 0xe4, 0xe9, 0x1d, 0xe9, 0xd7, 0x0a, 0xc5,
	0x49, 0xac, 0x0e, 0x84, 0x49, 0xe7, 0xc1, 0x6d, 0x5e, 0xad, 0xdd, 0x3d, 0xb8, 0xf3, 0xaf, 0x48,
	0x37, 0xa1, 0xfb, 0x01, 0xe7, 0x0e, 0x19, 0x90, 0xe1, 0x46, 0x90, 0xbd, 0xd2, 0x2d, 0xb8, 0x75,
	0x19, 0x27, 0x16, 0x9d, 0xce, 0x80, 0x0c, 0xd7, 0x83, 0x62, 0xf1, 0xbc, 0xf3, 0x8c, 0xb0, 0x10,
	0xfa, 0x61, 0xf3, 0x19, 0xe9, 0x0e, 0xf4, 0x74, 0x5e, 0x8b, 0xcc, 0x5c, 0x61, 0x69, 0x07, 0x45,
	0xe9, 0x74, 0xae, 0x90, 0xf6, 0x61, 0xad, 0x3c, 0x67, 0xe9, 0xbb, 0x5a, 0x50, 0x31, 0x17, 0x9c,
	0x70, 0xc1, 0x41, 0xd8, 0x3b, 0x78, 0xb0, 0x1f, 0x9b, 0x31, 0x9a, 0xb7, 0x1a, 0xd3, 0xbf, 0x9f,
	0x54, 0x9b, 0x6e, 0xc3, 0xba, 0xe5, 0x67, 0x51, 0xc2, 0xb5, 0xc9, 0x2f, 0x6e, 0x23, 0x58, 0xb3,
	0xfc, 0xec, 0x98, 0xd7, 0x79, 0x3a, 0x57, 0x79, 0xd8, 0x4f, 0x02, 0x0f, 0x9b, 0xbd, 0xcb, 0x60,
	0x54, 0x53, 0x30, 0x6f, 0x96, 0x0a, 0xe6, 0x7f, 0xbe, 0x37, 0x15, 0xce, 0xe8, 0x57, 0x17, 0xee,
	0x66, 0xdb, 0x1d, 0x95, 0x24, 0x87, 0x76, 0x42, 0xbf, 0x10, 0xd8, 0xbc, 0xda, 0x25, 0x74, 0xef,
	0x9a, 0xcd, 0x95, 0xdf, 0xb9, 0xfb, 0xa2, 0x55, 0x6b, 0xb2, 0x95, 0x9c, 0x29, 0xbc, 0x1e, 0x53,
	0xd8, 0x8a, 0x29, 0x5c, 0xcc, 0xf4, 0x95, 0xc0, 0x56, 0x53, 0x68, 0xf4, 0x65, 0x8b, 0xbc, 0x0b,
	0xb6, 0x57, 0xad, 0x3b, 0x86, 0xad, 0xb8, 0xce, 0xef, 0x6f, 0x3f, 0x4e, 0xef, 0xc3, 0xbd, 0xda,
	0x08, 0xdb, 0x1f, 0xbf, 0x3f, 0x38, 0x97, 0x49, 0x2c, 0xce, 0xbd, 0xa7, 0x23, 0x63, 0xbc, 0xa9,
	0x9c, 0xf9, 0xf9, 0x3c, 0x9a, 0xca, 0xc4, 0xd7, 0x98, 0x5e, 0xf2, 0x29, 0x6a, 0x7f, 0x89, 0x59,
	0x38, 0x59, 0xcd, 0x7f, 0x7b, 0xf2, 0x27, 0x00, 0x00, 0xff, 0xff, 0x2f, 0x6d, 0x18, 0x90, 0x39,
	0x05, 0x00, 0x00,
}
