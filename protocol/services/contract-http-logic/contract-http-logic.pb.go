// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/contract-http-logic/contract-http-logic.proto

package contract_http_logic // import "golang.52tt.com/protocol/services/contract-http-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type KnowledgeTaskStatus int32

const (
	KnowledgeTaskStatus_KnowledgeTaskStatus_Invalid   KnowledgeTaskStatus = 0
	KnowledgeTaskStatus_KnowledgeTaskStatus_NotFinish KnowledgeTaskStatus = 1
	KnowledgeTaskStatus_KnowledgeTaskStatus_Finished  KnowledgeTaskStatus = 2
)

var KnowledgeTaskStatus_name = map[int32]string{
	0: "KnowledgeTaskStatus_Invalid",
	1: "KnowledgeTaskStatus_NotFinish",
	2: "KnowledgeTaskStatus_Finished",
}
var KnowledgeTaskStatus_value = map[string]int32{
	"KnowledgeTaskStatus_Invalid":   0,
	"KnowledgeTaskStatus_NotFinish": 1,
	"KnowledgeTaskStatus_Finished":  2,
}

func (x KnowledgeTaskStatus) String() string {
	return proto.EnumName(KnowledgeTaskStatus_name, int32(x))
}
func (KnowledgeTaskStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{0}
}

// 新手任务类型
type NewbieAnchorTaskType int32

const (
	NewbieAnchorTaskType_NEWBIE_ANCHOR_TASK_TYPE_UNSPECIFIED NewbieAnchorTaskType = 0
	NewbieAnchorTaskType_NEWBIE_ANCHOR_TASK_TYPE_BEGINNER    NewbieAnchorTaskType = 1
	NewbieAnchorTaskType_NEWBIE_ANCHOR_TASK_TYPE_ADVANCED    NewbieAnchorTaskType = 2
)

var NewbieAnchorTaskType_name = map[int32]string{
	0: "NEWBIE_ANCHOR_TASK_TYPE_UNSPECIFIED",
	1: "NEWBIE_ANCHOR_TASK_TYPE_BEGINNER",
	2: "NEWBIE_ANCHOR_TASK_TYPE_ADVANCED",
}
var NewbieAnchorTaskType_value = map[string]int32{
	"NEWBIE_ANCHOR_TASK_TYPE_UNSPECIFIED": 0,
	"NEWBIE_ANCHOR_TASK_TYPE_BEGINNER":    1,
	"NEWBIE_ANCHOR_TASK_TYPE_ADVANCED":    2,
}

func (x NewbieAnchorTaskType) String() string {
	return proto.EnumName(NewbieAnchorTaskType_name, int32(x))
}
func (NewbieAnchorTaskType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{1}
}

// 新手任务奖励状态
type NewbieRewardStatus int32

const (
	NewbieRewardStatus_NEWBIE_REWARD_STATUS_UNSPECIFIED NewbieRewardStatus = 0
	NewbieRewardStatus_NEWBIE_REWARD_STATUS_NOT_CLAIMED NewbieRewardStatus = 1
	NewbieRewardStatus_NEWBIE_REWARD_STATUS_CLAIMED     NewbieRewardStatus = 2
	NewbieRewardStatus_NEWBIE_REWARD_STATUS_EXPIRED     NewbieRewardStatus = 3
)

var NewbieRewardStatus_name = map[int32]string{
	0: "NEWBIE_REWARD_STATUS_UNSPECIFIED",
	1: "NEWBIE_REWARD_STATUS_NOT_CLAIMED",
	2: "NEWBIE_REWARD_STATUS_CLAIMED",
	3: "NEWBIE_REWARD_STATUS_EXPIRED",
}
var NewbieRewardStatus_value = map[string]int32{
	"NEWBIE_REWARD_STATUS_UNSPECIFIED": 0,
	"NEWBIE_REWARD_STATUS_NOT_CLAIMED": 1,
	"NEWBIE_REWARD_STATUS_CLAIMED":     2,
	"NEWBIE_REWARD_STATUS_EXPIRED":     3,
}

func (x NewbieRewardStatus) String() string {
	return proto.EnumName(NewbieRewardStatus_name, int32(x))
}
func (NewbieRewardStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{2}
}

type ProofContent_ProofType int32

const (
	ProofContent_ProofType_Invalid ProofContent_ProofType = 0
	ProofContent_ProofType_Video   ProofContent_ProofType = 1
	ProofContent_ProofType_Image   ProofContent_ProofType = 2
)

var ProofContent_ProofType_name = map[int32]string{
	0: "ProofType_Invalid",
	1: "ProofType_Video",
	2: "ProofType_Image",
}
var ProofContent_ProofType_value = map[string]int32{
	"ProofType_Invalid": 0,
	"ProofType_Video":   1,
	"ProofType_Image":   2,
}

func (x ProofContent_ProofType) String() string {
	return proto.EnumName(ProofContent_ProofType_name, int32(x))
}
func (ProofContent_ProofType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{26, 0}
}

type ProofShowContent_ProofType int32

const (
	ProofShowContent_ProofType_Invalid ProofShowContent_ProofType = 0
	ProofShowContent_ProofType_Video   ProofShowContent_ProofType = 1
	ProofShowContent_ProofType_Image   ProofShowContent_ProofType = 2
)

var ProofShowContent_ProofType_name = map[int32]string{
	0: "ProofType_Invalid",
	1: "ProofType_Video",
	2: "ProofType_Image",
}
var ProofShowContent_ProofType_value = map[string]int32{
	"ProofType_Invalid": 0,
	"ProofType_Video":   1,
	"ProofType_Image":   2,
}

func (x ProofShowContent_ProofType) String() string {
	return proto.EnumName(ProofShowContent_ProofType_name, int32(x))
}
func (ProofShowContent_ProofType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{27, 0}
}

type CheckCanCancelContractResp_CancelStage int32

const (
	CheckCanCancelContractResp_CancelContractTypeDefault                         CheckCanCancelContractResp_CancelStage = 0
	CheckCanCancelContractResp_CancelContractTypeWaitingForMaster                CheckCanCancelContractResp_CancelStage = 1
	CheckCanCancelContractResp_CancelContractTypeWaitingForOfficial              CheckCanCancelContractResp_CancelStage = 2
	CheckCanCancelContractResp_CancelContractTypeWaitingForMasterQuiet           CheckCanCancelContractResp_CancelStage = 3
	CheckCanCancelContractResp_CancelContractTypeWaitingForMasterNoReason        CheckCanCancelContractResp_CancelStage = 4
	CheckCanCancelContractResp_CancelContractTypeWaitingForMasterLive            CheckCanCancelContractResp_CancelStage = 5
	CheckCanCancelContractResp_CancelContractTypeWaitingForOfficialPay           CheckCanCancelContractResp_CancelStage = 6
	CheckCanCancelContractResp_CancelContractTypeWaitingForMasterPayAccept       CheckCanCancelContractResp_CancelStage = 7
	CheckCanCancelContractResp_CancelContractTypeWaitingForMasterNegotiateAccept CheckCanCancelContractResp_CancelStage = 8
)

var CheckCanCancelContractResp_CancelStage_name = map[int32]string{
	0: "CancelContractTypeDefault",
	1: "CancelContractTypeWaitingForMaster",
	2: "CancelContractTypeWaitingForOfficial",
	3: "CancelContractTypeWaitingForMasterQuiet",
	4: "CancelContractTypeWaitingForMasterNoReason",
	5: "CancelContractTypeWaitingForMasterLive",
	6: "CancelContractTypeWaitingForOfficialPay",
	7: "CancelContractTypeWaitingForMasterPayAccept",
	8: "CancelContractTypeWaitingForMasterNegotiateAccept",
}
var CheckCanCancelContractResp_CancelStage_value = map[string]int32{
	"CancelContractTypeDefault":                         0,
	"CancelContractTypeWaitingForMaster":                1,
	"CancelContractTypeWaitingForOfficial":              2,
	"CancelContractTypeWaitingForMasterQuiet":           3,
	"CancelContractTypeWaitingForMasterNoReason":        4,
	"CancelContractTypeWaitingForMasterLive":            5,
	"CancelContractTypeWaitingForOfficialPay":           6,
	"CancelContractTypeWaitingForMasterPayAccept":       7,
	"CancelContractTypeWaitingForMasterNegotiateAccept": 8,
}

func (x CheckCanCancelContractResp_CancelStage) String() string {
	return proto.EnumName(CheckCanCancelContractResp_CancelStage_name, int32(x))
}
func (CheckCanCancelContractResp_CancelStage) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{32, 0}
}

// 处理结果
type ProcPromoteInviteReq_ProcResType int32

const (
	ProcPromoteInviteReq_ProcResType_Invalid  ProcPromoteInviteReq_ProcResType = 0
	ProcPromoteInviteReq_ProcResType_Agree    ProcPromoteInviteReq_ProcResType = 1
	ProcPromoteInviteReq_ProcResType_NO_Agree ProcPromoteInviteReq_ProcResType = 2
)

var ProcPromoteInviteReq_ProcResType_name = map[int32]string{
	0: "ProcResType_Invalid",
	1: "ProcResType_Agree",
	2: "ProcResType_NO_Agree",
}
var ProcPromoteInviteReq_ProcResType_value = map[string]int32{
	"ProcResType_Invalid":  0,
	"ProcResType_Agree":    1,
	"ProcResType_NO_Agree": 2,
}

func (x ProcPromoteInviteReq_ProcResType) String() string {
	return proto.EnumName(ProcPromoteInviteReq_ProcResType_name, int32(x))
}
func (ProcPromoteInviteReq_ProcResType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{54, 0}
}

type NewbieAnchorTaskDay_Status int32

const (
	NewbieAnchorTaskDay_STATUS_UNSPECIFIED   NewbieAnchorTaskDay_Status = 0
	NewbieAnchorTaskDay_STATUS_NOT_COMPLETED NewbieAnchorTaskDay_Status = 1
	NewbieAnchorTaskDay_STATUS_COMPLETED     NewbieAnchorTaskDay_Status = 2
	NewbieAnchorTaskDay_STATUS_EXPIRED       NewbieAnchorTaskDay_Status = 3
)

var NewbieAnchorTaskDay_Status_name = map[int32]string{
	0: "STATUS_UNSPECIFIED",
	1: "STATUS_NOT_COMPLETED",
	2: "STATUS_COMPLETED",
	3: "STATUS_EXPIRED",
}
var NewbieAnchorTaskDay_Status_value = map[string]int32{
	"STATUS_UNSPECIFIED":   0,
	"STATUS_NOT_COMPLETED": 1,
	"STATUS_COMPLETED":     2,
	"STATUS_EXPIRED":       3,
}

func (x NewbieAnchorTaskDay_Status) String() string {
	return proto.EnumName(NewbieAnchorTaskDay_Status_name, int32(x))
}
func (NewbieAnchorTaskDay_Status) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{85, 0}
}

type GetHallTaskResp struct {
	DayTaskList          []*HallTaskDetial `protobuf:"bytes,1,rep,name=day_task_list,json=dayTaskList,proto3" json:"day_task_list,omitempty"`
	WeekTaskList         []*HallTaskDetial `protobuf:"bytes,2,rep,name=week_task_list,json=weekTaskList,proto3" json:"week_task_list,omitempty"`
	RewardMsg            string            `protobuf:"bytes,3,opt,name=reward_msg,json=rewardMsg,proto3" json:"reward_msg,omitempty"`
	IsShow               bool              `protobuf:"varint,4,opt,name=is_show,json=isShow,proto3" json:"is_show,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetHallTaskResp) Reset()         { *m = GetHallTaskResp{} }
func (m *GetHallTaskResp) String() string { return proto.CompactTextString(m) }
func (*GetHallTaskResp) ProtoMessage()    {}
func (*GetHallTaskResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{0}
}
func (m *GetHallTaskResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHallTaskResp.Unmarshal(m, b)
}
func (m *GetHallTaskResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHallTaskResp.Marshal(b, m, deterministic)
}
func (dst *GetHallTaskResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHallTaskResp.Merge(dst, src)
}
func (m *GetHallTaskResp) XXX_Size() int {
	return xxx_messageInfo_GetHallTaskResp.Size(m)
}
func (m *GetHallTaskResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHallTaskResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetHallTaskResp proto.InternalMessageInfo

func (m *GetHallTaskResp) GetDayTaskList() []*HallTaskDetial {
	if m != nil {
		return m.DayTaskList
	}
	return nil
}

func (m *GetHallTaskResp) GetWeekTaskList() []*HallTaskDetial {
	if m != nil {
		return m.WeekTaskList
	}
	return nil
}

func (m *GetHallTaskResp) GetRewardMsg() string {
	if m != nil {
		return m.RewardMsg
	}
	return ""
}

func (m *GetHallTaskResp) GetIsShow() bool {
	if m != nil {
		return m.IsShow
	}
	return false
}

type HallTaskDetial struct {
	TaskName             string   `protobuf:"bytes,1,opt,name=task_name,json=taskName,proto3" json:"task_name,omitempty"`
	TaskProgress         string   `protobuf:"bytes,2,opt,name=task_progress,json=taskProgress,proto3" json:"task_progress,omitempty"`
	Rate                 float32  `protobuf:"fixed32,3,opt,name=rate,proto3" json:"rate,omitempty"`
	TaskVal              uint32   `protobuf:"varint,4,opt,name=task_val,json=taskVal,proto3" json:"task_val,omitempty"`
	ValList              []string `protobuf:"bytes,5,rep,name=val_list,json=valList,proto3" json:"val_list,omitempty"`
	Date                 string   `protobuf:"bytes,6,opt,name=date,proto3" json:"date,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HallTaskDetial) Reset()         { *m = HallTaskDetial{} }
func (m *HallTaskDetial) String() string { return proto.CompactTextString(m) }
func (*HallTaskDetial) ProtoMessage()    {}
func (*HallTaskDetial) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{1}
}
func (m *HallTaskDetial) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HallTaskDetial.Unmarshal(m, b)
}
func (m *HallTaskDetial) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HallTaskDetial.Marshal(b, m, deterministic)
}
func (dst *HallTaskDetial) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HallTaskDetial.Merge(dst, src)
}
func (m *HallTaskDetial) XXX_Size() int {
	return xxx_messageInfo_HallTaskDetial.Size(m)
}
func (m *HallTaskDetial) XXX_DiscardUnknown() {
	xxx_messageInfo_HallTaskDetial.DiscardUnknown(m)
}

var xxx_messageInfo_HallTaskDetial proto.InternalMessageInfo

func (m *HallTaskDetial) GetTaskName() string {
	if m != nil {
		return m.TaskName
	}
	return ""
}

func (m *HallTaskDetial) GetTaskProgress() string {
	if m != nil {
		return m.TaskProgress
	}
	return ""
}

func (m *HallTaskDetial) GetRate() float32 {
	if m != nil {
		return m.Rate
	}
	return 0
}

func (m *HallTaskDetial) GetTaskVal() uint32 {
	if m != nil {
		return m.TaskVal
	}
	return 0
}

func (m *HallTaskDetial) GetValList() []string {
	if m != nil {
		return m.ValList
	}
	return nil
}

func (m *HallTaskDetial) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

type GetHallTaskHistoryResp struct {
	List                 []*HallTaskHistoryDetial `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetHallTaskHistoryResp) Reset()         { *m = GetHallTaskHistoryResp{} }
func (m *GetHallTaskHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetHallTaskHistoryResp) ProtoMessage()    {}
func (*GetHallTaskHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{2}
}
func (m *GetHallTaskHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHallTaskHistoryResp.Unmarshal(m, b)
}
func (m *GetHallTaskHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHallTaskHistoryResp.Marshal(b, m, deterministic)
}
func (dst *GetHallTaskHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHallTaskHistoryResp.Merge(dst, src)
}
func (m *GetHallTaskHistoryResp) XXX_Size() int {
	return xxx_messageInfo_GetHallTaskHistoryResp.Size(m)
}
func (m *GetHallTaskHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHallTaskHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetHallTaskHistoryResp proto.InternalMessageInfo

func (m *GetHallTaskHistoryResp) GetList() []*HallTaskHistoryDetial {
	if m != nil {
		return m.List
	}
	return nil
}

type HallTaskHistoryDetial struct {
	Date                 string            `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	DayTaskList          []*HallTaskDetial `protobuf:"bytes,2,rep,name=day_task_list,json=dayTaskList,proto3" json:"day_task_list,omitempty"`
	WeekTaskList         []*HallTaskDetial `protobuf:"bytes,3,rep,name=week_task_list,json=weekTaskList,proto3" json:"week_task_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *HallTaskHistoryDetial) Reset()         { *m = HallTaskHistoryDetial{} }
func (m *HallTaskHistoryDetial) String() string { return proto.CompactTextString(m) }
func (*HallTaskHistoryDetial) ProtoMessage()    {}
func (*HallTaskHistoryDetial) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{3}
}
func (m *HallTaskHistoryDetial) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HallTaskHistoryDetial.Unmarshal(m, b)
}
func (m *HallTaskHistoryDetial) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HallTaskHistoryDetial.Marshal(b, m, deterministic)
}
func (dst *HallTaskHistoryDetial) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HallTaskHistoryDetial.Merge(dst, src)
}
func (m *HallTaskHistoryDetial) XXX_Size() int {
	return xxx_messageInfo_HallTaskHistoryDetial.Size(m)
}
func (m *HallTaskHistoryDetial) XXX_DiscardUnknown() {
	xxx_messageInfo_HallTaskHistoryDetial.DiscardUnknown(m)
}

var xxx_messageInfo_HallTaskHistoryDetial proto.InternalMessageInfo

func (m *HallTaskHistoryDetial) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

func (m *HallTaskHistoryDetial) GetDayTaskList() []*HallTaskDetial {
	if m != nil {
		return m.DayTaskList
	}
	return nil
}

func (m *HallTaskHistoryDetial) GetWeekTaskList() []*HallTaskDetial {
	if m != nil {
		return m.WeekTaskList
	}
	return nil
}

type GetESportCoachInfoReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	CoachUid             uint32   `protobuf:"varint,2,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetESportCoachInfoReq) Reset()         { *m = GetESportCoachInfoReq{} }
func (m *GetESportCoachInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetESportCoachInfoReq) ProtoMessage()    {}
func (*GetESportCoachInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{4}
}
func (m *GetESportCoachInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetESportCoachInfoReq.Unmarshal(m, b)
}
func (m *GetESportCoachInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetESportCoachInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetESportCoachInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetESportCoachInfoReq.Merge(dst, src)
}
func (m *GetESportCoachInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetESportCoachInfoReq.Size(m)
}
func (m *GetESportCoachInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetESportCoachInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetESportCoachInfoReq proto.InternalMessageInfo

func (m *GetESportCoachInfoReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetESportCoachInfoReq) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

type UserSkillItem struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GameName             string   `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	GameIcon             string   `protobuf:"bytes,3,opt,name=game_icon,json=gameIcon,proto3" json:"game_icon,omitempty"`
	GameType             uint32   `protobuf:"varint,4,opt,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
	TypeSort             uint32   `protobuf:"varint,5,opt,name=type_sort,json=typeSort,proto3" json:"type_sort,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserSkillItem) Reset()         { *m = UserSkillItem{} }
func (m *UserSkillItem) String() string { return proto.CompactTextString(m) }
func (*UserSkillItem) ProtoMessage()    {}
func (*UserSkillItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{5}
}
func (m *UserSkillItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserSkillItem.Unmarshal(m, b)
}
func (m *UserSkillItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserSkillItem.Marshal(b, m, deterministic)
}
func (dst *UserSkillItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserSkillItem.Merge(dst, src)
}
func (m *UserSkillItem) XXX_Size() int {
	return xxx_messageInfo_UserSkillItem.Size(m)
}
func (m *UserSkillItem) XXX_DiscardUnknown() {
	xxx_messageInfo_UserSkillItem.DiscardUnknown(m)
}

var xxx_messageInfo_UserSkillItem proto.InternalMessageInfo

func (m *UserSkillItem) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *UserSkillItem) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *UserSkillItem) GetGameIcon() string {
	if m != nil {
		return m.GameIcon
	}
	return ""
}

func (m *UserSkillItem) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

func (m *UserSkillItem) GetTypeSort() uint32 {
	if m != nil {
		return m.TypeSort
	}
	return 0
}

type ESportMonthData struct {
	OrderAmount          uint64   `protobuf:"varint,11,opt,name=order_amount,json=orderAmount,proto3" json:"order_amount,omitempty"`
	TotalOrders          uint64   `protobuf:"varint,12,opt,name=total_orders,json=totalOrders,proto3" json:"total_orders,omitempty"`
	TakeOrderDays        uint32   `protobuf:"varint,13,opt,name=take_order_days,json=takeOrderDays,proto3" json:"take_order_days,omitempty"`
	ActiveDays           uint32   `protobuf:"varint,14,opt,name=active_days,json=activeDays,proto3" json:"active_days,omitempty"`
	PeopleServed         uint32   `protobuf:"varint,15,opt,name=people_served,json=peopleServed,proto3" json:"people_served,omitempty"`
	NewCustomers         uint32   `protobuf:"varint,16,opt,name=new_customers,json=newCustomers,proto3" json:"new_customers,omitempty"`
	Repurchases          uint32   `protobuf:"varint,17,opt,name=repurchases,proto3" json:"repurchases,omitempty"`
	ViolationsA          uint32   `protobuf:"varint,18,opt,name=violations_a,json=violationsA,proto3" json:"violations_a,omitempty"`
	ViolationsB          uint32   `protobuf:"varint,19,opt,name=violations_b,json=violationsB,proto3" json:"violations_b,omitempty"`
	ViolationsC          uint32   `protobuf:"varint,20,opt,name=violations_c,json=violationsC,proto3" json:"violations_c,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ESportMonthData) Reset()         { *m = ESportMonthData{} }
func (m *ESportMonthData) String() string { return proto.CompactTextString(m) }
func (*ESportMonthData) ProtoMessage()    {}
func (*ESportMonthData) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{6}
}
func (m *ESportMonthData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ESportMonthData.Unmarshal(m, b)
}
func (m *ESportMonthData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ESportMonthData.Marshal(b, m, deterministic)
}
func (dst *ESportMonthData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ESportMonthData.Merge(dst, src)
}
func (m *ESportMonthData) XXX_Size() int {
	return xxx_messageInfo_ESportMonthData.Size(m)
}
func (m *ESportMonthData) XXX_DiscardUnknown() {
	xxx_messageInfo_ESportMonthData.DiscardUnknown(m)
}

var xxx_messageInfo_ESportMonthData proto.InternalMessageInfo

func (m *ESportMonthData) GetOrderAmount() uint64 {
	if m != nil {
		return m.OrderAmount
	}
	return 0
}

func (m *ESportMonthData) GetTotalOrders() uint64 {
	if m != nil {
		return m.TotalOrders
	}
	return 0
}

func (m *ESportMonthData) GetTakeOrderDays() uint32 {
	if m != nil {
		return m.TakeOrderDays
	}
	return 0
}

func (m *ESportMonthData) GetActiveDays() uint32 {
	if m != nil {
		return m.ActiveDays
	}
	return 0
}

func (m *ESportMonthData) GetPeopleServed() uint32 {
	if m != nil {
		return m.PeopleServed
	}
	return 0
}

func (m *ESportMonthData) GetNewCustomers() uint32 {
	if m != nil {
		return m.NewCustomers
	}
	return 0
}

func (m *ESportMonthData) GetRepurchases() uint32 {
	if m != nil {
		return m.Repurchases
	}
	return 0
}

func (m *ESportMonthData) GetViolationsA() uint32 {
	if m != nil {
		return m.ViolationsA
	}
	return 0
}

func (m *ESportMonthData) GetViolationsB() uint32 {
	if m != nil {
		return m.ViolationsB
	}
	return 0
}

func (m *ESportMonthData) GetViolationsC() uint32 {
	if m != nil {
		return m.ViolationsC
	}
	return 0
}

type GetESportCoachInfoResp struct {
	GuildId           uint32 `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GuildShortId      uint32 `protobuf:"varint,2,opt,name=guild_short_id,json=guildShortId,proto3" json:"guild_short_id,omitempty"`
	GuildName         string `protobuf:"bytes,3,opt,name=guild_name,json=guildName,proto3" json:"guild_name,omitempty"`
	CoachUid          uint32 `protobuf:"varint,4,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	CoachTtid         string `protobuf:"bytes,5,opt,name=coach_ttid,json=coachTtid,proto3" json:"coach_ttid,omitempty"`
	CoachNick         string `protobuf:"bytes,6,opt,name=coach_nick,json=coachNick,proto3" json:"coach_nick,omitempty"`
	ContractBeginTime int64  `protobuf:"varint,7,opt,name=contract_begin_time,json=contractBeginTime,proto3" json:"contract_begin_time,omitempty"`
	ContractEndTime   int64  `protobuf:"varint,8,opt,name=contract_end_time,json=contractEndTime,proto3" json:"contract_end_time,omitempty"`
	// 技能
	SkillList []*UserSkillItem `protobuf:"bytes,9,rep,name=skill_list,json=skillList,proto3" json:"skill_list,omitempty"`
	// 数据
	ThisMonthData        *ESportMonthData `protobuf:"bytes,21,opt,name=this_month_data,json=thisMonthData,proto3" json:"this_month_data,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetESportCoachInfoResp) Reset()         { *m = GetESportCoachInfoResp{} }
func (m *GetESportCoachInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetESportCoachInfoResp) ProtoMessage()    {}
func (*GetESportCoachInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{7}
}
func (m *GetESportCoachInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetESportCoachInfoResp.Unmarshal(m, b)
}
func (m *GetESportCoachInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetESportCoachInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetESportCoachInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetESportCoachInfoResp.Merge(dst, src)
}
func (m *GetESportCoachInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetESportCoachInfoResp.Size(m)
}
func (m *GetESportCoachInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetESportCoachInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetESportCoachInfoResp proto.InternalMessageInfo

func (m *GetESportCoachInfoResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetESportCoachInfoResp) GetGuildShortId() uint32 {
	if m != nil {
		return m.GuildShortId
	}
	return 0
}

func (m *GetESportCoachInfoResp) GetGuildName() string {
	if m != nil {
		return m.GuildName
	}
	return ""
}

func (m *GetESportCoachInfoResp) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

func (m *GetESportCoachInfoResp) GetCoachTtid() string {
	if m != nil {
		return m.CoachTtid
	}
	return ""
}

func (m *GetESportCoachInfoResp) GetCoachNick() string {
	if m != nil {
		return m.CoachNick
	}
	return ""
}

func (m *GetESportCoachInfoResp) GetContractBeginTime() int64 {
	if m != nil {
		return m.ContractBeginTime
	}
	return 0
}

func (m *GetESportCoachInfoResp) GetContractEndTime() int64 {
	if m != nil {
		return m.ContractEndTime
	}
	return 0
}

func (m *GetESportCoachInfoResp) GetSkillList() []*UserSkillItem {
	if m != nil {
		return m.SkillList
	}
	return nil
}

func (m *GetESportCoachInfoResp) GetThisMonthData() *ESportMonthData {
	if m != nil {
		return m.ThisMonthData
	}
	return nil
}

// 段位等信息
type SectionInfo struct {
	SectionName          string   `protobuf:"bytes,1,opt,name=section_name,json=sectionName,proto3" json:"section_name,omitempty"`
	ItemList             []string `protobuf:"bytes,2,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	SectionId            uint32   `protobuf:"varint,3,opt,name=section_id,json=sectionId,proto3" json:"section_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SectionInfo) Reset()         { *m = SectionInfo{} }
func (m *SectionInfo) String() string { return proto.CompactTextString(m) }
func (*SectionInfo) ProtoMessage()    {}
func (*SectionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{8}
}
func (m *SectionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SectionInfo.Unmarshal(m, b)
}
func (m *SectionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SectionInfo.Marshal(b, m, deterministic)
}
func (dst *SectionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SectionInfo.Merge(dst, src)
}
func (m *SectionInfo) XXX_Size() int {
	return xxx_messageInfo_SectionInfo.Size(m)
}
func (m *SectionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SectionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SectionInfo proto.InternalMessageInfo

func (m *SectionInfo) GetSectionName() string {
	if m != nil {
		return m.SectionName
	}
	return ""
}

func (m *SectionInfo) GetItemList() []string {
	if m != nil {
		return m.ItemList
	}
	return nil
}

func (m *SectionInfo) GetSectionId() uint32 {
	if m != nil {
		return m.SectionId
	}
	return 0
}

// 用户游戏资料信息
type UserSkillInfo struct {
	GameId               uint32         `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GameName             string         `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	SkillEvidence        string         `protobuf:"bytes,3,opt,name=skill_evidence,json=skillEvidence,proto3" json:"skill_evidence,omitempty"`
	SkillDesc            string         `protobuf:"bytes,4,opt,name=skill_desc,json=skillDesc,proto3" json:"skill_desc,omitempty"`
	Audio                string         `protobuf:"bytes,5,opt,name=audio,proto3" json:"audio,omitempty"`
	AudioDuration        uint32         `protobuf:"varint,6,opt,name=audio_duration,json=audioDuration,proto3" json:"audio_duration,omitempty"`
	SectionList          []*SectionInfo `protobuf:"bytes,7,rep,name=section_list,json=sectionList,proto3" json:"section_list,omitempty"`
	TextDesc             string         `protobuf:"bytes,8,opt,name=text_desc,json=textDesc,proto3" json:"text_desc,omitempty"`
	GameRank             uint32         `protobuf:"varint,9,opt,name=game_rank,json=gameRank,proto3" json:"game_rank,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UserSkillInfo) Reset()         { *m = UserSkillInfo{} }
func (m *UserSkillInfo) String() string { return proto.CompactTextString(m) }
func (*UserSkillInfo) ProtoMessage()    {}
func (*UserSkillInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{9}
}
func (m *UserSkillInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserSkillInfo.Unmarshal(m, b)
}
func (m *UserSkillInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserSkillInfo.Marshal(b, m, deterministic)
}
func (dst *UserSkillInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserSkillInfo.Merge(dst, src)
}
func (m *UserSkillInfo) XXX_Size() int {
	return xxx_messageInfo_UserSkillInfo.Size(m)
}
func (m *UserSkillInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserSkillInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserSkillInfo proto.InternalMessageInfo

func (m *UserSkillInfo) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *UserSkillInfo) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *UserSkillInfo) GetSkillEvidence() string {
	if m != nil {
		return m.SkillEvidence
	}
	return ""
}

func (m *UserSkillInfo) GetSkillDesc() string {
	if m != nil {
		return m.SkillDesc
	}
	return ""
}

func (m *UserSkillInfo) GetAudio() string {
	if m != nil {
		return m.Audio
	}
	return ""
}

func (m *UserSkillInfo) GetAudioDuration() uint32 {
	if m != nil {
		return m.AudioDuration
	}
	return 0
}

func (m *UserSkillInfo) GetSectionList() []*SectionInfo {
	if m != nil {
		return m.SectionList
	}
	return nil
}

func (m *UserSkillInfo) GetTextDesc() string {
	if m != nil {
		return m.TextDesc
	}
	return ""
}

func (m *UserSkillInfo) GetGameRank() uint32 {
	if m != nil {
		return m.GameRank
	}
	return 0
}

type GetESportUserSkillInfoReq struct {
	CoachUid             uint32   `protobuf:"varint,1,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetESportUserSkillInfoReq) Reset()         { *m = GetESportUserSkillInfoReq{} }
func (m *GetESportUserSkillInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetESportUserSkillInfoReq) ProtoMessage()    {}
func (*GetESportUserSkillInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{10}
}
func (m *GetESportUserSkillInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetESportUserSkillInfoReq.Unmarshal(m, b)
}
func (m *GetESportUserSkillInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetESportUserSkillInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetESportUserSkillInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetESportUserSkillInfoReq.Merge(dst, src)
}
func (m *GetESportUserSkillInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetESportUserSkillInfoReq.Size(m)
}
func (m *GetESportUserSkillInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetESportUserSkillInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetESportUserSkillInfoReq proto.InternalMessageInfo

func (m *GetESportUserSkillInfoReq) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

func (m *GetESportUserSkillInfoReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetESportUserSkillInfoResp struct {
	CoachUid             uint32         `protobuf:"varint,1,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	GameId               uint32         `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	SkillInfo            *UserSkillInfo `protobuf:"bytes,3,opt,name=skill_info,json=skillInfo,proto3" json:"skill_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetESportUserSkillInfoResp) Reset()         { *m = GetESportUserSkillInfoResp{} }
func (m *GetESportUserSkillInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetESportUserSkillInfoResp) ProtoMessage()    {}
func (*GetESportUserSkillInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{11}
}
func (m *GetESportUserSkillInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetESportUserSkillInfoResp.Unmarshal(m, b)
}
func (m *GetESportUserSkillInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetESportUserSkillInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetESportUserSkillInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetESportUserSkillInfoResp.Merge(dst, src)
}
func (m *GetESportUserSkillInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetESportUserSkillInfoResp.Size(m)
}
func (m *GetESportUserSkillInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetESportUserSkillInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetESportUserSkillInfoResp proto.InternalMessageInfo

func (m *GetESportUserSkillInfoResp) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

func (m *GetESportUserSkillInfoResp) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetESportUserSkillInfoResp) GetSkillInfo() *UserSkillInfo {
	if m != nil {
		return m.SkillInfo
	}
	return nil
}

// 申请签约身份前置检查
type SignIdentityPreCheckReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	IdentityType         uint32   `protobuf:"varint,2,opt,name=identity_type,json=identityType,proto3" json:"identity_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SignIdentityPreCheckReq) Reset()         { *m = SignIdentityPreCheckReq{} }
func (m *SignIdentityPreCheckReq) String() string { return proto.CompactTextString(m) }
func (*SignIdentityPreCheckReq) ProtoMessage()    {}
func (*SignIdentityPreCheckReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{12}
}
func (m *SignIdentityPreCheckReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SignIdentityPreCheckReq.Unmarshal(m, b)
}
func (m *SignIdentityPreCheckReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SignIdentityPreCheckReq.Marshal(b, m, deterministic)
}
func (dst *SignIdentityPreCheckReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SignIdentityPreCheckReq.Merge(dst, src)
}
func (m *SignIdentityPreCheckReq) XXX_Size() int {
	return xxx_messageInfo_SignIdentityPreCheckReq.Size(m)
}
func (m *SignIdentityPreCheckReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SignIdentityPreCheckReq.DiscardUnknown(m)
}

var xxx_messageInfo_SignIdentityPreCheckReq proto.InternalMessageInfo

func (m *SignIdentityPreCheckReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *SignIdentityPreCheckReq) GetIdentityType() uint32 {
	if m != nil {
		return m.IdentityType
	}
	return 0
}

type SignIdentityPreCheckResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SignIdentityPreCheckResp) Reset()         { *m = SignIdentityPreCheckResp{} }
func (m *SignIdentityPreCheckResp) String() string { return proto.CompactTextString(m) }
func (*SignIdentityPreCheckResp) ProtoMessage()    {}
func (*SignIdentityPreCheckResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{13}
}
func (m *SignIdentityPreCheckResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SignIdentityPreCheckResp.Unmarshal(m, b)
}
func (m *SignIdentityPreCheckResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SignIdentityPreCheckResp.Marshal(b, m, deterministic)
}
func (dst *SignIdentityPreCheckResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SignIdentityPreCheckResp.Merge(dst, src)
}
func (m *SignIdentityPreCheckResp) XXX_Size() int {
	return xxx_messageInfo_SignIdentityPreCheckResp.Size(m)
}
func (m *SignIdentityPreCheckResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SignIdentityPreCheckResp.DiscardUnknown(m)
}

var xxx_messageInfo_SignIdentityPreCheckResp proto.InternalMessageInfo

// 申请签约-触发风控事件检查
type ApplySignRiskCheckReq struct {
	IdentityType         uint32   `protobuf:"varint,1,opt,name=identity_type,json=identityType,proto3" json:"identity_type,omitempty"`
	DeviceId             string   `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	ClientType           uint32   `protobuf:"varint,3,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	AppVersion           uint32   `protobuf:"varint,4,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`
	FaceAuthResultToken  string   `protobuf:"bytes,5,opt,name=face_auth_result_token,json=faceAuthResultToken,proto3" json:"face_auth_result_token,omitempty"`
	GuildId              uint32   `protobuf:"varint,6,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplySignRiskCheckReq) Reset()         { *m = ApplySignRiskCheckReq{} }
func (m *ApplySignRiskCheckReq) String() string { return proto.CompactTextString(m) }
func (*ApplySignRiskCheckReq) ProtoMessage()    {}
func (*ApplySignRiskCheckReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{14}
}
func (m *ApplySignRiskCheckReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplySignRiskCheckReq.Unmarshal(m, b)
}
func (m *ApplySignRiskCheckReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplySignRiskCheckReq.Marshal(b, m, deterministic)
}
func (dst *ApplySignRiskCheckReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplySignRiskCheckReq.Merge(dst, src)
}
func (m *ApplySignRiskCheckReq) XXX_Size() int {
	return xxx_messageInfo_ApplySignRiskCheckReq.Size(m)
}
func (m *ApplySignRiskCheckReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplySignRiskCheckReq.DiscardUnknown(m)
}

var xxx_messageInfo_ApplySignRiskCheckReq proto.InternalMessageInfo

func (m *ApplySignRiskCheckReq) GetIdentityType() uint32 {
	if m != nil {
		return m.IdentityType
	}
	return 0
}

func (m *ApplySignRiskCheckReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *ApplySignRiskCheckReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *ApplySignRiskCheckReq) GetAppVersion() uint32 {
	if m != nil {
		return m.AppVersion
	}
	return 0
}

func (m *ApplySignRiskCheckReq) GetFaceAuthResultToken() string {
	if m != nil {
		return m.FaceAuthResultToken
	}
	return ""
}

func (m *ApplySignRiskCheckReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type ApplySignRiskCheckResp struct {
	// 人脸context
	FaceAuthContextJson  string   `protobuf:"bytes,1,opt,name=face_auth_context_json,json=faceAuthContextJson,proto3" json:"face_auth_context_json,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplySignRiskCheckResp) Reset()         { *m = ApplySignRiskCheckResp{} }
func (m *ApplySignRiskCheckResp) String() string { return proto.CompactTextString(m) }
func (*ApplySignRiskCheckResp) ProtoMessage()    {}
func (*ApplySignRiskCheckResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{15}
}
func (m *ApplySignRiskCheckResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplySignRiskCheckResp.Unmarshal(m, b)
}
func (m *ApplySignRiskCheckResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplySignRiskCheckResp.Marshal(b, m, deterministic)
}
func (dst *ApplySignRiskCheckResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplySignRiskCheckResp.Merge(dst, src)
}
func (m *ApplySignRiskCheckResp) XXX_Size() int {
	return xxx_messageInfo_ApplySignRiskCheckResp.Size(m)
}
func (m *ApplySignRiskCheckResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplySignRiskCheckResp.DiscardUnknown(m)
}

var xxx_messageInfo_ApplySignRiskCheckResp proto.InternalMessageInfo

func (m *ApplySignRiskCheckResp) GetFaceAuthContextJson() string {
	if m != nil {
		return m.FaceAuthContextJson
	}
	return ""
}

type ApplySignReq struct {
	IdentityType         uint32   `protobuf:"varint,1,opt,name=identity_type,json=identityType,proto3" json:"identity_type,omitempty"`
	ContractNames        []string `protobuf:"bytes,2,rep,name=contract_names,json=contractNames,proto3" json:"contract_names,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplySignReq) Reset()         { *m = ApplySignReq{} }
func (m *ApplySignReq) String() string { return proto.CompactTextString(m) }
func (*ApplySignReq) ProtoMessage()    {}
func (*ApplySignReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{16}
}
func (m *ApplySignReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplySignReq.Unmarshal(m, b)
}
func (m *ApplySignReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplySignReq.Marshal(b, m, deterministic)
}
func (dst *ApplySignReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplySignReq.Merge(dst, src)
}
func (m *ApplySignReq) XXX_Size() int {
	return xxx_messageInfo_ApplySignReq.Size(m)
}
func (m *ApplySignReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplySignReq.DiscardUnknown(m)
}

var xxx_messageInfo_ApplySignReq proto.InternalMessageInfo

func (m *ApplySignReq) GetIdentityType() uint32 {
	if m != nil {
		return m.IdentityType
	}
	return 0
}

func (m *ApplySignReq) GetContractNames() []string {
	if m != nil {
		return m.ContractNames
	}
	return nil
}

type ApplySignResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplySignResp) Reset()         { *m = ApplySignResp{} }
func (m *ApplySignResp) String() string { return proto.CompactTextString(m) }
func (*ApplySignResp) ProtoMessage()    {}
func (*ApplySignResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{17}
}
func (m *ApplySignResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplySignResp.Unmarshal(m, b)
}
func (m *ApplySignResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplySignResp.Marshal(b, m, deterministic)
}
func (dst *ApplySignResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplySignResp.Merge(dst, src)
}
func (m *ApplySignResp) XXX_Size() int {
	return xxx_messageInfo_ApplySignResp.Size(m)
}
func (m *ApplySignResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplySignResp.DiscardUnknown(m)
}

var xxx_messageInfo_ApplySignResp proto.InternalMessageInfo

// 公会业务类型
type GuildBusiness struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Type                 uint32   `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	Available            bool     `protobuf:"varint,3,opt,name=available,proto3" json:"available,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildBusiness) Reset()         { *m = GuildBusiness{} }
func (m *GuildBusiness) String() string { return proto.CompactTextString(m) }
func (*GuildBusiness) ProtoMessage()    {}
func (*GuildBusiness) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{18}
}
func (m *GuildBusiness) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildBusiness.Unmarshal(m, b)
}
func (m *GuildBusiness) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildBusiness.Marshal(b, m, deterministic)
}
func (dst *GuildBusiness) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildBusiness.Merge(dst, src)
}
func (m *GuildBusiness) XXX_Size() int {
	return xxx_messageInfo_GuildBusiness.Size(m)
}
func (m *GuildBusiness) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildBusiness.DiscardUnknown(m)
}

var xxx_messageInfo_GuildBusiness proto.InternalMessageInfo

func (m *GuildBusiness) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GuildBusiness) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GuildBusiness) GetAvailable() bool {
	if m != nil {
		return m.Available
	}
	return false
}

// 获取公会信息
type TopGuildInfo struct {
	GuildId      uint32           `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ShortId      uint32           `protobuf:"varint,2,opt,name=short_id,json=shortId,proto3" json:"short_id,omitempty"`
	Name         string           `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	GuildTag     string           `protobuf:"bytes,4,opt,name=guild_tag,json=guildTag,proto3" json:"guild_tag,omitempty"`
	OwnerName    string           `protobuf:"bytes,5,opt,name=owner_name,json=ownerName,proto3" json:"owner_name,omitempty"`
	OwnerAccount string           `protobuf:"bytes,6,opt,name=owner_account,json=ownerAccount,proto3" json:"owner_account,omitempty"`
	OwnerAlias   string           `protobuf:"bytes,7,opt,name=owner_alias,json=ownerAlias,proto3" json:"owner_alias,omitempty"`
	OwnerUid     uint32           `protobuf:"varint,8,opt,name=owner_uid,json=ownerUid,proto3" json:"owner_uid,omitempty"`
	BusinessList []*GuildBusiness `protobuf:"bytes,9,rep,name=business_list,json=businessList,proto3" json:"business_list,omitempty"`
	//
	Rank                 uint32   `protobuf:"varint,10,opt,name=rank,proto3" json:"rank,omitempty"`
	RecommendTag         string   `protobuf:"bytes,11,opt,name=recommend_tag,json=recommendTag,proto3" json:"recommend_tag,omitempty"`
	AbilityTag           string   `protobuf:"bytes,12,opt,name=ability_tag,json=abilityTag,proto3" json:"ability_tag,omitempty"` // Deprecated: Do not use.
	HonorTitle           string   `protobuf:"bytes,13,opt,name=honor_title,json=honorTitle,proto3" json:"honor_title,omitempty"`
	FromTime             int64    `protobuf:"varint,14,opt,name=from_time,json=fromTime,proto3" json:"from_time,omitempty"`
	ToTime               int64    `protobuf:"varint,15,opt,name=to_time,json=toTime,proto3" json:"to_time,omitempty"`
	AbilityTagList       []string `protobuf:"bytes,16,rep,name=ability_tag_list,json=abilityTagList,proto3" json:"ability_tag_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TopGuildInfo) Reset()         { *m = TopGuildInfo{} }
func (m *TopGuildInfo) String() string { return proto.CompactTextString(m) }
func (*TopGuildInfo) ProtoMessage()    {}
func (*TopGuildInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{19}
}
func (m *TopGuildInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopGuildInfo.Unmarshal(m, b)
}
func (m *TopGuildInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopGuildInfo.Marshal(b, m, deterministic)
}
func (dst *TopGuildInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopGuildInfo.Merge(dst, src)
}
func (m *TopGuildInfo) XXX_Size() int {
	return xxx_messageInfo_TopGuildInfo.Size(m)
}
func (m *TopGuildInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TopGuildInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TopGuildInfo proto.InternalMessageInfo

func (m *TopGuildInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *TopGuildInfo) GetShortId() uint32 {
	if m != nil {
		return m.ShortId
	}
	return 0
}

func (m *TopGuildInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *TopGuildInfo) GetGuildTag() string {
	if m != nil {
		return m.GuildTag
	}
	return ""
}

func (m *TopGuildInfo) GetOwnerName() string {
	if m != nil {
		return m.OwnerName
	}
	return ""
}

func (m *TopGuildInfo) GetOwnerAccount() string {
	if m != nil {
		return m.OwnerAccount
	}
	return ""
}

func (m *TopGuildInfo) GetOwnerAlias() string {
	if m != nil {
		return m.OwnerAlias
	}
	return ""
}

func (m *TopGuildInfo) GetOwnerUid() uint32 {
	if m != nil {
		return m.OwnerUid
	}
	return 0
}

func (m *TopGuildInfo) GetBusinessList() []*GuildBusiness {
	if m != nil {
		return m.BusinessList
	}
	return nil
}

func (m *TopGuildInfo) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *TopGuildInfo) GetRecommendTag() string {
	if m != nil {
		return m.RecommendTag
	}
	return ""
}

// Deprecated: Do not use.
func (m *TopGuildInfo) GetAbilityTag() string {
	if m != nil {
		return m.AbilityTag
	}
	return ""
}

func (m *TopGuildInfo) GetHonorTitle() string {
	if m != nil {
		return m.HonorTitle
	}
	return ""
}

func (m *TopGuildInfo) GetFromTime() int64 {
	if m != nil {
		return m.FromTime
	}
	return 0
}

func (m *TopGuildInfo) GetToTime() int64 {
	if m != nil {
		return m.ToTime
	}
	return 0
}

func (m *TopGuildInfo) GetAbilityTagList() []string {
	if m != nil {
		return m.AbilityTagList
	}
	return nil
}

// 获取推荐置顶公会列表
type GetRecommendTopGuildListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecommendTopGuildListReq) Reset()         { *m = GetRecommendTopGuildListReq{} }
func (m *GetRecommendTopGuildListReq) String() string { return proto.CompactTextString(m) }
func (*GetRecommendTopGuildListReq) ProtoMessage()    {}
func (*GetRecommendTopGuildListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{20}
}
func (m *GetRecommendTopGuildListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendTopGuildListReq.Unmarshal(m, b)
}
func (m *GetRecommendTopGuildListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendTopGuildListReq.Marshal(b, m, deterministic)
}
func (dst *GetRecommendTopGuildListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendTopGuildListReq.Merge(dst, src)
}
func (m *GetRecommendTopGuildListReq) XXX_Size() int {
	return xxx_messageInfo_GetRecommendTopGuildListReq.Size(m)
}
func (m *GetRecommendTopGuildListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendTopGuildListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendTopGuildListReq proto.InternalMessageInfo

type GetRecommendTopGuildListResp struct {
	GuildList            []*TopGuildInfo `protobuf:"bytes,1,rep,name=guild_list,json=guildList,proto3" json:"guild_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetRecommendTopGuildListResp) Reset()         { *m = GetRecommendTopGuildListResp{} }
func (m *GetRecommendTopGuildListResp) String() string { return proto.CompactTextString(m) }
func (*GetRecommendTopGuildListResp) ProtoMessage()    {}
func (*GetRecommendTopGuildListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{21}
}
func (m *GetRecommendTopGuildListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendTopGuildListResp.Unmarshal(m, b)
}
func (m *GetRecommendTopGuildListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendTopGuildListResp.Marshal(b, m, deterministic)
}
func (dst *GetRecommendTopGuildListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendTopGuildListResp.Merge(dst, src)
}
func (m *GetRecommendTopGuildListResp) XXX_Size() int {
	return xxx_messageInfo_GetRecommendTopGuildListResp.Size(m)
}
func (m *GetRecommendTopGuildListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendTopGuildListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendTopGuildListResp proto.InternalMessageInfo

func (m *GetRecommendTopGuildListResp) GetGuildList() []*TopGuildInfo {
	if m != nil {
		return m.GuildList
	}
	return nil
}

// 获取解约方式
type GetCancelContractTypesReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Scene                uint32   `protobuf:"varint,2,opt,name=scene,proto3" json:"scene,omitempty"`
	WorkerType           uint32   `protobuf:"varint,3,opt,name=worker_type,json=workerType,proto3" json:"worker_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCancelContractTypesReq) Reset()         { *m = GetCancelContractTypesReq{} }
func (m *GetCancelContractTypesReq) String() string { return proto.CompactTextString(m) }
func (*GetCancelContractTypesReq) ProtoMessage()    {}
func (*GetCancelContractTypesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{22}
}
func (m *GetCancelContractTypesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCancelContractTypesReq.Unmarshal(m, b)
}
func (m *GetCancelContractTypesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCancelContractTypesReq.Marshal(b, m, deterministic)
}
func (dst *GetCancelContractTypesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCancelContractTypesReq.Merge(dst, src)
}
func (m *GetCancelContractTypesReq) XXX_Size() int {
	return xxx_messageInfo_GetCancelContractTypesReq.Size(m)
}
func (m *GetCancelContractTypesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCancelContractTypesReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCancelContractTypesReq proto.InternalMessageInfo

func (m *GetCancelContractTypesReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetCancelContractTypesReq) GetScene() uint32 {
	if m != nil {
		return m.Scene
	}
	return 0
}

func (m *GetCancelContractTypesReq) GetWorkerType() uint32 {
	if m != nil {
		return m.WorkerType
	}
	return 0
}

type CanCancelContract struct {
	CancelType           uint32   `protobuf:"varint,1,opt,name=cancel_type,json=cancelType,proto3" json:"cancel_type,omitempty"`
	Enabled              bool     `protobuf:"varint,2,opt,name=enabled,proto3" json:"enabled,omitempty"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Desc                 string   `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc,omitempty"`
	ShowButDisable       bool     `protobuf:"varint,5,opt,name=show_but_disable,json=showButDisable,proto3" json:"show_but_disable,omitempty"`
	DisableMsg           string   `protobuf:"bytes,6,opt,name=disable_msg,json=disableMsg,proto3" json:"disable_msg,omitempty"`
	IsNew                bool     `protobuf:"varint,7,opt,name=is_new,json=isNew,proto3" json:"is_new,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CanCancelContract) Reset()         { *m = CanCancelContract{} }
func (m *CanCancelContract) String() string { return proto.CompactTextString(m) }
func (*CanCancelContract) ProtoMessage()    {}
func (*CanCancelContract) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{23}
}
func (m *CanCancelContract) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CanCancelContract.Unmarshal(m, b)
}
func (m *CanCancelContract) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CanCancelContract.Marshal(b, m, deterministic)
}
func (dst *CanCancelContract) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CanCancelContract.Merge(dst, src)
}
func (m *CanCancelContract) XXX_Size() int {
	return xxx_messageInfo_CanCancelContract.Size(m)
}
func (m *CanCancelContract) XXX_DiscardUnknown() {
	xxx_messageInfo_CanCancelContract.DiscardUnknown(m)
}

var xxx_messageInfo_CanCancelContract proto.InternalMessageInfo

func (m *CanCancelContract) GetCancelType() uint32 {
	if m != nil {
		return m.CancelType
	}
	return 0
}

func (m *CanCancelContract) GetEnabled() bool {
	if m != nil {
		return m.Enabled
	}
	return false
}

func (m *CanCancelContract) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *CanCancelContract) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *CanCancelContract) GetShowButDisable() bool {
	if m != nil {
		return m.ShowButDisable
	}
	return false
}

func (m *CanCancelContract) GetDisableMsg() string {
	if m != nil {
		return m.DisableMsg
	}
	return ""
}

func (m *CanCancelContract) GetIsNew() bool {
	if m != nil {
		return m.IsNew
	}
	return false
}

type GetCancelContractTypesResp struct {
	CancelTypes          []*CanCancelContract `protobuf:"bytes,1,rep,name=cancel_types,json=cancelTypes,proto3" json:"cancel_types,omitempty"`
	PayCost              int64                `protobuf:"varint,2,opt,name=pay_cost,json=payCost,proto3" json:"pay_cost,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetCancelContractTypesResp) Reset()         { *m = GetCancelContractTypesResp{} }
func (m *GetCancelContractTypesResp) String() string { return proto.CompactTextString(m) }
func (*GetCancelContractTypesResp) ProtoMessage()    {}
func (*GetCancelContractTypesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{24}
}
func (m *GetCancelContractTypesResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCancelContractTypesResp.Unmarshal(m, b)
}
func (m *GetCancelContractTypesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCancelContractTypesResp.Marshal(b, m, deterministic)
}
func (dst *GetCancelContractTypesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCancelContractTypesResp.Merge(dst, src)
}
func (m *GetCancelContractTypesResp) XXX_Size() int {
	return xxx_messageInfo_GetCancelContractTypesResp.Size(m)
}
func (m *GetCancelContractTypesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCancelContractTypesResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCancelContractTypesResp proto.InternalMessageInfo

func (m *GetCancelContractTypesResp) GetCancelTypes() []*CanCancelContract {
	if m != nil {
		return m.CancelTypes
	}
	return nil
}

func (m *GetCancelContractTypesResp) GetPayCost() int64 {
	if m != nil {
		return m.PayCost
	}
	return 0
}

// 申请解约
type ApplyCancelContractReq struct {
	GuildId              uint32          `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	CancelType           uint32          `protobuf:"varint,2,opt,name=cancel_type,json=cancelType,proto3" json:"cancel_type,omitempty"`
	ProofUrls            []string        `protobuf:"bytes,3,rep,name=proof_urls,json=proofUrls,proto3" json:"proof_urls,omitempty"`
	PayDesc              string          `protobuf:"bytes,4,opt,name=pay_desc,json=payDesc,proto3" json:"pay_desc,omitempty"`
	ProofList            []*ProofContent `protobuf:"bytes,5,rep,name=proof_list,json=proofList,proto3" json:"proof_list,omitempty"`
	CancelReason         string          `protobuf:"bytes,6,opt,name=cancel_reason,json=cancelReason,proto3" json:"cancel_reason,omitempty"`
	NegotiateReasonType  []string        `protobuf:"bytes,7,rep,name=negotiate_reason_type,json=negotiateReasonType,proto3" json:"negotiate_reason_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ApplyCancelContractReq) Reset()         { *m = ApplyCancelContractReq{} }
func (m *ApplyCancelContractReq) String() string { return proto.CompactTextString(m) }
func (*ApplyCancelContractReq) ProtoMessage()    {}
func (*ApplyCancelContractReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{25}
}
func (m *ApplyCancelContractReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyCancelContractReq.Unmarshal(m, b)
}
func (m *ApplyCancelContractReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyCancelContractReq.Marshal(b, m, deterministic)
}
func (dst *ApplyCancelContractReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyCancelContractReq.Merge(dst, src)
}
func (m *ApplyCancelContractReq) XXX_Size() int {
	return xxx_messageInfo_ApplyCancelContractReq.Size(m)
}
func (m *ApplyCancelContractReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyCancelContractReq.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyCancelContractReq proto.InternalMessageInfo

func (m *ApplyCancelContractReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *ApplyCancelContractReq) GetCancelType() uint32 {
	if m != nil {
		return m.CancelType
	}
	return 0
}

func (m *ApplyCancelContractReq) GetProofUrls() []string {
	if m != nil {
		return m.ProofUrls
	}
	return nil
}

func (m *ApplyCancelContractReq) GetPayDesc() string {
	if m != nil {
		return m.PayDesc
	}
	return ""
}

func (m *ApplyCancelContractReq) GetProofList() []*ProofContent {
	if m != nil {
		return m.ProofList
	}
	return nil
}

func (m *ApplyCancelContractReq) GetCancelReason() string {
	if m != nil {
		return m.CancelReason
	}
	return ""
}

func (m *ApplyCancelContractReq) GetNegotiateReasonType() []string {
	if m != nil {
		return m.NegotiateReasonType
	}
	return nil
}

type ProofContent struct {
	Key                  string                 `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	CensorKey            string                 `protobuf:"bytes,2,opt,name=censor_key,json=censorKey,proto3" json:"censor_key,omitempty"`
	Type                 ProofContent_ProofType `protobuf:"varint,3,opt,name=type,proto3,enum=contract_http_logic.ProofContent_ProofType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *ProofContent) Reset()         { *m = ProofContent{} }
func (m *ProofContent) String() string { return proto.CompactTextString(m) }
func (*ProofContent) ProtoMessage()    {}
func (*ProofContent) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{26}
}
func (m *ProofContent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProofContent.Unmarshal(m, b)
}
func (m *ProofContent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProofContent.Marshal(b, m, deterministic)
}
func (dst *ProofContent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProofContent.Merge(dst, src)
}
func (m *ProofContent) XXX_Size() int {
	return xxx_messageInfo_ProofContent.Size(m)
}
func (m *ProofContent) XXX_DiscardUnknown() {
	xxx_messageInfo_ProofContent.DiscardUnknown(m)
}

var xxx_messageInfo_ProofContent proto.InternalMessageInfo

func (m *ProofContent) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *ProofContent) GetCensorKey() string {
	if m != nil {
		return m.CensorKey
	}
	return ""
}

func (m *ProofContent) GetType() ProofContent_ProofType {
	if m != nil {
		return m.Type
	}
	return ProofContent_ProofType_Invalid
}

type ProofShowContent struct {
	Url                  string                     `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Type                 ProofShowContent_ProofType `protobuf:"varint,2,opt,name=type,proto3,enum=contract_http_logic.ProofShowContent_ProofType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *ProofShowContent) Reset()         { *m = ProofShowContent{} }
func (m *ProofShowContent) String() string { return proto.CompactTextString(m) }
func (*ProofShowContent) ProtoMessage()    {}
func (*ProofShowContent) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{27}
}
func (m *ProofShowContent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProofShowContent.Unmarshal(m, b)
}
func (m *ProofShowContent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProofShowContent.Marshal(b, m, deterministic)
}
func (dst *ProofShowContent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProofShowContent.Merge(dst, src)
}
func (m *ProofShowContent) XXX_Size() int {
	return xxx_messageInfo_ProofShowContent.Size(m)
}
func (m *ProofShowContent) XXX_DiscardUnknown() {
	xxx_messageInfo_ProofShowContent.DiscardUnknown(m)
}

var xxx_messageInfo_ProofShowContent proto.InternalMessageInfo

func (m *ProofShowContent) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *ProofShowContent) GetType() ProofShowContent_ProofType {
	if m != nil {
		return m.Type
	}
	return ProofShowContent_ProofType_Invalid
}

type ApplyCancelContractResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplyCancelContractResp) Reset()         { *m = ApplyCancelContractResp{} }
func (m *ApplyCancelContractResp) String() string { return proto.CompactTextString(m) }
func (*ApplyCancelContractResp) ProtoMessage()    {}
func (*ApplyCancelContractResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{28}
}
func (m *ApplyCancelContractResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyCancelContractResp.Unmarshal(m, b)
}
func (m *ApplyCancelContractResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyCancelContractResp.Marshal(b, m, deterministic)
}
func (dst *ApplyCancelContractResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyCancelContractResp.Merge(dst, src)
}
func (m *ApplyCancelContractResp) XXX_Size() int {
	return xxx_messageInfo_ApplyCancelContractResp.Size(m)
}
func (m *ApplyCancelContractResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyCancelContractResp.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyCancelContractResp proto.InternalMessageInfo

type LockPayCancelAmountReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LockPayCancelAmountReq) Reset()         { *m = LockPayCancelAmountReq{} }
func (m *LockPayCancelAmountReq) String() string { return proto.CompactTextString(m) }
func (*LockPayCancelAmountReq) ProtoMessage()    {}
func (*LockPayCancelAmountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{29}
}
func (m *LockPayCancelAmountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LockPayCancelAmountReq.Unmarshal(m, b)
}
func (m *LockPayCancelAmountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LockPayCancelAmountReq.Marshal(b, m, deterministic)
}
func (dst *LockPayCancelAmountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LockPayCancelAmountReq.Merge(dst, src)
}
func (m *LockPayCancelAmountReq) XXX_Size() int {
	return xxx_messageInfo_LockPayCancelAmountReq.Size(m)
}
func (m *LockPayCancelAmountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LockPayCancelAmountReq.DiscardUnknown(m)
}

var xxx_messageInfo_LockPayCancelAmountReq proto.InternalMessageInfo

func (m *LockPayCancelAmountReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type LockPayCancelAmountResp struct {
	PayCost              int64    `protobuf:"varint,1,opt,name=pay_cost,json=payCost,proto3" json:"pay_cost,omitempty"`
	ExpireTs             int64    `protobuf:"varint,2,opt,name=expire_ts,json=expireTs,proto3" json:"expire_ts,omitempty"`
	StartTs              int64    `protobuf:"varint,3,opt,name=start_ts,json=startTs,proto3" json:"start_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LockPayCancelAmountResp) Reset()         { *m = LockPayCancelAmountResp{} }
func (m *LockPayCancelAmountResp) String() string { return proto.CompactTextString(m) }
func (*LockPayCancelAmountResp) ProtoMessage()    {}
func (*LockPayCancelAmountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{30}
}
func (m *LockPayCancelAmountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LockPayCancelAmountResp.Unmarshal(m, b)
}
func (m *LockPayCancelAmountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LockPayCancelAmountResp.Marshal(b, m, deterministic)
}
func (dst *LockPayCancelAmountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LockPayCancelAmountResp.Merge(dst, src)
}
func (m *LockPayCancelAmountResp) XXX_Size() int {
	return xxx_messageInfo_LockPayCancelAmountResp.Size(m)
}
func (m *LockPayCancelAmountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_LockPayCancelAmountResp.DiscardUnknown(m)
}

var xxx_messageInfo_LockPayCancelAmountResp proto.InternalMessageInfo

func (m *LockPayCancelAmountResp) GetPayCost() int64 {
	if m != nil {
		return m.PayCost
	}
	return 0
}

func (m *LockPayCancelAmountResp) GetExpireTs() int64 {
	if m != nil {
		return m.ExpireTs
	}
	return 0
}

func (m *LockPayCancelAmountResp) GetStartTs() int64 {
	if m != nil {
		return m.StartTs
	}
	return 0
}

// 检查是否可以解约
type CheckCanCancelContractReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckCanCancelContractReq) Reset()         { *m = CheckCanCancelContractReq{} }
func (m *CheckCanCancelContractReq) String() string { return proto.CompactTextString(m) }
func (*CheckCanCancelContractReq) ProtoMessage()    {}
func (*CheckCanCancelContractReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{31}
}
func (m *CheckCanCancelContractReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckCanCancelContractReq.Unmarshal(m, b)
}
func (m *CheckCanCancelContractReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckCanCancelContractReq.Marshal(b, m, deterministic)
}
func (dst *CheckCanCancelContractReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckCanCancelContractReq.Merge(dst, src)
}
func (m *CheckCanCancelContractReq) XXX_Size() int {
	return xxx_messageInfo_CheckCanCancelContractReq.Size(m)
}
func (m *CheckCanCancelContractReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckCanCancelContractReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckCanCancelContractReq proto.InternalMessageInfo

func (m *CheckCanCancelContractReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type CheckCanCancelContractResp struct {
	LockPayCost          int64                                  `protobuf:"varint,1,opt,name=lock_pay_cost,json=lockPayCost,proto3" json:"lock_pay_cost,omitempty"`
	LockExpireTs         int64                                  `protobuf:"varint,2,opt,name=lock_expire_ts,json=lockExpireTs,proto3" json:"lock_expire_ts,omitempty"`
	LockStartTs          int64                                  `protobuf:"varint,3,opt,name=lock_start_ts,json=lockStartTs,proto3" json:"lock_start_ts,omitempty"`
	CancelStage          CheckCanCancelContractResp_CancelStage `protobuf:"varint,4,opt,name=cancel_stage,json=cancelStage,proto3,enum=contract_http_logic.CheckCanCancelContractResp_CancelStage" json:"cancel_stage,omitempty"`
	StageExpireTs        int64                                  `protobuf:"varint,5,opt,name=stage_expire_ts,json=stageExpireTs,proto3" json:"stage_expire_ts,omitempty"`
	RejectReason         string                                 `protobuf:"bytes,6,opt,name=reject_reason,json=rejectReason,proto3" json:"reject_reason,omitempty"`
	ProofList            []*ProofShowContent                    `protobuf:"bytes,7,rep,name=proof_list,json=proofList,proto3" json:"proof_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                               `json:"-"`
	XXX_unrecognized     []byte                                 `json:"-"`
	XXX_sizecache        int32                                  `json:"-"`
}

func (m *CheckCanCancelContractResp) Reset()         { *m = CheckCanCancelContractResp{} }
func (m *CheckCanCancelContractResp) String() string { return proto.CompactTextString(m) }
func (*CheckCanCancelContractResp) ProtoMessage()    {}
func (*CheckCanCancelContractResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{32}
}
func (m *CheckCanCancelContractResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckCanCancelContractResp.Unmarshal(m, b)
}
func (m *CheckCanCancelContractResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckCanCancelContractResp.Marshal(b, m, deterministic)
}
func (dst *CheckCanCancelContractResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckCanCancelContractResp.Merge(dst, src)
}
func (m *CheckCanCancelContractResp) XXX_Size() int {
	return xxx_messageInfo_CheckCanCancelContractResp.Size(m)
}
func (m *CheckCanCancelContractResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckCanCancelContractResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckCanCancelContractResp proto.InternalMessageInfo

func (m *CheckCanCancelContractResp) GetLockPayCost() int64 {
	if m != nil {
		return m.LockPayCost
	}
	return 0
}

func (m *CheckCanCancelContractResp) GetLockExpireTs() int64 {
	if m != nil {
		return m.LockExpireTs
	}
	return 0
}

func (m *CheckCanCancelContractResp) GetLockStartTs() int64 {
	if m != nil {
		return m.LockStartTs
	}
	return 0
}

func (m *CheckCanCancelContractResp) GetCancelStage() CheckCanCancelContractResp_CancelStage {
	if m != nil {
		return m.CancelStage
	}
	return CheckCanCancelContractResp_CancelContractTypeDefault
}

func (m *CheckCanCancelContractResp) GetStageExpireTs() int64 {
	if m != nil {
		return m.StageExpireTs
	}
	return 0
}

func (m *CheckCanCancelContractResp) GetRejectReason() string {
	if m != nil {
		return m.RejectReason
	}
	return ""
}

func (m *CheckCanCancelContractResp) GetProofList() []*ProofShowContent {
	if m != nil {
		return m.ProofList
	}
	return nil
}

// 获取解约申请列表
type GetApplyCancelContractListReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Page                 uint32   `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageNum              uint32   `protobuf:"varint,3,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetApplyCancelContractListReq) Reset()         { *m = GetApplyCancelContractListReq{} }
func (m *GetApplyCancelContractListReq) String() string { return proto.CompactTextString(m) }
func (*GetApplyCancelContractListReq) ProtoMessage()    {}
func (*GetApplyCancelContractListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{33}
}
func (m *GetApplyCancelContractListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetApplyCancelContractListReq.Unmarshal(m, b)
}
func (m *GetApplyCancelContractListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetApplyCancelContractListReq.Marshal(b, m, deterministic)
}
func (dst *GetApplyCancelContractListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetApplyCancelContractListReq.Merge(dst, src)
}
func (m *GetApplyCancelContractListReq) XXX_Size() int {
	return xxx_messageInfo_GetApplyCancelContractListReq.Size(m)
}
func (m *GetApplyCancelContractListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetApplyCancelContractListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetApplyCancelContractListReq proto.InternalMessageInfo

func (m *GetApplyCancelContractListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetApplyCancelContractListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetApplyCancelContractListReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

type ApplyCancelContractItem struct {
	Uid                  uint32              `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32              `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	CancelType           uint32              `protobuf:"varint,3,opt,name=cancel_type,json=cancelType,proto3" json:"cancel_type,omitempty"`
	Account              string              `protobuf:"bytes,4,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string              `protobuf:"bytes,5,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Alias                string              `protobuf:"bytes,6,opt,name=alias,proto3" json:"alias,omitempty"`
	ApplyTime            uint32              `protobuf:"varint,7,opt,name=apply_time,json=applyTime,proto3" json:"apply_time,omitempty"`
	EndTime              uint32              `protobuf:"varint,8,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	AnchorIdentityList   []uint32            `protobuf:"varint,9,rep,packed,name=anchor_identity_list,json=anchorIdentityList,proto3" json:"anchor_identity_list,omitempty"`
	Reason               string              `protobuf:"bytes,10,opt,name=reason,proto3" json:"reason,omitempty"`
	ShowAcceptBtn        bool                `protobuf:"varint,11,opt,name=show_accept_btn,json=showAcceptBtn,proto3" json:"show_accept_btn,omitempty"`
	ShowRejectBtn        bool                `protobuf:"varint,12,opt,name=show_reject_btn,json=showRejectBtn,proto3" json:"show_reject_btn,omitempty"`
	ProofList            []*ProofShowContent `protobuf:"bytes,13,rep,name=proof_list,json=proofList,proto3" json:"proof_list,omitempty"`
	CancelReason         string              `protobuf:"bytes,14,opt,name=cancel_reason,json=cancelReason,proto3" json:"cancel_reason,omitempty"`
	NegotiateReasonType  []string            `protobuf:"bytes,15,rep,name=negotiate_reason_type,json=negotiateReasonType,proto3" json:"negotiate_reason_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *ApplyCancelContractItem) Reset()         { *m = ApplyCancelContractItem{} }
func (m *ApplyCancelContractItem) String() string { return proto.CompactTextString(m) }
func (*ApplyCancelContractItem) ProtoMessage()    {}
func (*ApplyCancelContractItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{34}
}
func (m *ApplyCancelContractItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyCancelContractItem.Unmarshal(m, b)
}
func (m *ApplyCancelContractItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyCancelContractItem.Marshal(b, m, deterministic)
}
func (dst *ApplyCancelContractItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyCancelContractItem.Merge(dst, src)
}
func (m *ApplyCancelContractItem) XXX_Size() int {
	return xxx_messageInfo_ApplyCancelContractItem.Size(m)
}
func (m *ApplyCancelContractItem) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyCancelContractItem.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyCancelContractItem proto.InternalMessageInfo

func (m *ApplyCancelContractItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ApplyCancelContractItem) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *ApplyCancelContractItem) GetCancelType() uint32 {
	if m != nil {
		return m.CancelType
	}
	return 0
}

func (m *ApplyCancelContractItem) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ApplyCancelContractItem) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ApplyCancelContractItem) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

func (m *ApplyCancelContractItem) GetApplyTime() uint32 {
	if m != nil {
		return m.ApplyTime
	}
	return 0
}

func (m *ApplyCancelContractItem) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *ApplyCancelContractItem) GetAnchorIdentityList() []uint32 {
	if m != nil {
		return m.AnchorIdentityList
	}
	return nil
}

func (m *ApplyCancelContractItem) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *ApplyCancelContractItem) GetShowAcceptBtn() bool {
	if m != nil {
		return m.ShowAcceptBtn
	}
	return false
}

func (m *ApplyCancelContractItem) GetShowRejectBtn() bool {
	if m != nil {
		return m.ShowRejectBtn
	}
	return false
}

func (m *ApplyCancelContractItem) GetProofList() []*ProofShowContent {
	if m != nil {
		return m.ProofList
	}
	return nil
}

func (m *ApplyCancelContractItem) GetCancelReason() string {
	if m != nil {
		return m.CancelReason
	}
	return ""
}

func (m *ApplyCancelContractItem) GetNegotiateReasonType() []string {
	if m != nil {
		return m.NegotiateReasonType
	}
	return nil
}

type GetApplyCancelContractListResp struct {
	List                 []*ApplyCancelContractItem `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	NextPage             uint32                     `protobuf:"varint,2,opt,name=next_page,json=nextPage,proto3" json:"next_page,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetApplyCancelContractListResp) Reset()         { *m = GetApplyCancelContractListResp{} }
func (m *GetApplyCancelContractListResp) String() string { return proto.CompactTextString(m) }
func (*GetApplyCancelContractListResp) ProtoMessage()    {}
func (*GetApplyCancelContractListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{35}
}
func (m *GetApplyCancelContractListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetApplyCancelContractListResp.Unmarshal(m, b)
}
func (m *GetApplyCancelContractListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetApplyCancelContractListResp.Marshal(b, m, deterministic)
}
func (dst *GetApplyCancelContractListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetApplyCancelContractListResp.Merge(dst, src)
}
func (m *GetApplyCancelContractListResp) XXX_Size() int {
	return xxx_messageInfo_GetApplyCancelContractListResp.Size(m)
}
func (m *GetApplyCancelContractListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetApplyCancelContractListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetApplyCancelContractListResp proto.InternalMessageInfo

func (m *GetApplyCancelContractListResp) GetList() []*ApplyCancelContractItem {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetApplyCancelContractListResp) GetNextPage() uint32 {
	if m != nil {
		return m.NextPage
	}
	return 0
}

type GetNegotiateReasonTypeReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNegotiateReasonTypeReq) Reset()         { *m = GetNegotiateReasonTypeReq{} }
func (m *GetNegotiateReasonTypeReq) String() string { return proto.CompactTextString(m) }
func (*GetNegotiateReasonTypeReq) ProtoMessage()    {}
func (*GetNegotiateReasonTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{36}
}
func (m *GetNegotiateReasonTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNegotiateReasonTypeReq.Unmarshal(m, b)
}
func (m *GetNegotiateReasonTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNegotiateReasonTypeReq.Marshal(b, m, deterministic)
}
func (dst *GetNegotiateReasonTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNegotiateReasonTypeReq.Merge(dst, src)
}
func (m *GetNegotiateReasonTypeReq) XXX_Size() int {
	return xxx_messageInfo_GetNegotiateReasonTypeReq.Size(m)
}
func (m *GetNegotiateReasonTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNegotiateReasonTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNegotiateReasonTypeReq proto.InternalMessageInfo

type GetNegotiateReasonTypeResp struct {
	NegotiateReasonType  []string `protobuf:"bytes,1,rep,name=negotiate_reason_type,json=negotiateReasonType,proto3" json:"negotiate_reason_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNegotiateReasonTypeResp) Reset()         { *m = GetNegotiateReasonTypeResp{} }
func (m *GetNegotiateReasonTypeResp) String() string { return proto.CompactTextString(m) }
func (*GetNegotiateReasonTypeResp) ProtoMessage()    {}
func (*GetNegotiateReasonTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{37}
}
func (m *GetNegotiateReasonTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNegotiateReasonTypeResp.Unmarshal(m, b)
}
func (m *GetNegotiateReasonTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNegotiateReasonTypeResp.Marshal(b, m, deterministic)
}
func (dst *GetNegotiateReasonTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNegotiateReasonTypeResp.Merge(dst, src)
}
func (m *GetNegotiateReasonTypeResp) XXX_Size() int {
	return xxx_messageInfo_GetNegotiateReasonTypeResp.Size(m)
}
func (m *GetNegotiateReasonTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNegotiateReasonTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNegotiateReasonTypeResp proto.InternalMessageInfo

func (m *GetNegotiateReasonTypeResp) GetNegotiateReasonType() []string {
	if m != nil {
		return m.NegotiateReasonType
	}
	return nil
}

// 同意解约
type AcceptApplyCancelContractReq struct {
	TargetUid            uint32   `protobuf:"varint,1,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AcceptApplyCancelContractReq) Reset()         { *m = AcceptApplyCancelContractReq{} }
func (m *AcceptApplyCancelContractReq) String() string { return proto.CompactTextString(m) }
func (*AcceptApplyCancelContractReq) ProtoMessage()    {}
func (*AcceptApplyCancelContractReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{38}
}
func (m *AcceptApplyCancelContractReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AcceptApplyCancelContractReq.Unmarshal(m, b)
}
func (m *AcceptApplyCancelContractReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AcceptApplyCancelContractReq.Marshal(b, m, deterministic)
}
func (dst *AcceptApplyCancelContractReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AcceptApplyCancelContractReq.Merge(dst, src)
}
func (m *AcceptApplyCancelContractReq) XXX_Size() int {
	return xxx_messageInfo_AcceptApplyCancelContractReq.Size(m)
}
func (m *AcceptApplyCancelContractReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AcceptApplyCancelContractReq.DiscardUnknown(m)
}

var xxx_messageInfo_AcceptApplyCancelContractReq proto.InternalMessageInfo

func (m *AcceptApplyCancelContractReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *AcceptApplyCancelContractReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type AcceptApplyCancelContractResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AcceptApplyCancelContractResp) Reset()         { *m = AcceptApplyCancelContractResp{} }
func (m *AcceptApplyCancelContractResp) String() string { return proto.CompactTextString(m) }
func (*AcceptApplyCancelContractResp) ProtoMessage()    {}
func (*AcceptApplyCancelContractResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{39}
}
func (m *AcceptApplyCancelContractResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AcceptApplyCancelContractResp.Unmarshal(m, b)
}
func (m *AcceptApplyCancelContractResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AcceptApplyCancelContractResp.Marshal(b, m, deterministic)
}
func (dst *AcceptApplyCancelContractResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AcceptApplyCancelContractResp.Merge(dst, src)
}
func (m *AcceptApplyCancelContractResp) XXX_Size() int {
	return xxx_messageInfo_AcceptApplyCancelContractResp.Size(m)
}
func (m *AcceptApplyCancelContractResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AcceptApplyCancelContractResp.DiscardUnknown(m)
}

var xxx_messageInfo_AcceptApplyCancelContractResp proto.InternalMessageInfo

// 拒绝解约
type RejectApplyCancelContractReq struct {
	TargetUid            uint32          `protobuf:"varint,1,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	GuildId              uint32          `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ProofUrls            []string        `protobuf:"bytes,3,rep,name=proof_urls,json=proofUrls,proto3" json:"proof_urls,omitempty"`
	RejectReason         string          `protobuf:"bytes,4,opt,name=reject_reason,json=rejectReason,proto3" json:"reject_reason,omitempty"`
	ProofList            []*ProofContent `protobuf:"bytes,5,rep,name=proof_list,json=proofList,proto3" json:"proof_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *RejectApplyCancelContractReq) Reset()         { *m = RejectApplyCancelContractReq{} }
func (m *RejectApplyCancelContractReq) String() string { return proto.CompactTextString(m) }
func (*RejectApplyCancelContractReq) ProtoMessage()    {}
func (*RejectApplyCancelContractReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{40}
}
func (m *RejectApplyCancelContractReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RejectApplyCancelContractReq.Unmarshal(m, b)
}
func (m *RejectApplyCancelContractReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RejectApplyCancelContractReq.Marshal(b, m, deterministic)
}
func (dst *RejectApplyCancelContractReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RejectApplyCancelContractReq.Merge(dst, src)
}
func (m *RejectApplyCancelContractReq) XXX_Size() int {
	return xxx_messageInfo_RejectApplyCancelContractReq.Size(m)
}
func (m *RejectApplyCancelContractReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RejectApplyCancelContractReq.DiscardUnknown(m)
}

var xxx_messageInfo_RejectApplyCancelContractReq proto.InternalMessageInfo

func (m *RejectApplyCancelContractReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *RejectApplyCancelContractReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *RejectApplyCancelContractReq) GetProofUrls() []string {
	if m != nil {
		return m.ProofUrls
	}
	return nil
}

func (m *RejectApplyCancelContractReq) GetRejectReason() string {
	if m != nil {
		return m.RejectReason
	}
	return ""
}

func (m *RejectApplyCancelContractReq) GetProofList() []*ProofContent {
	if m != nil {
		return m.ProofList
	}
	return nil
}

type RejectApplyCancelContractResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RejectApplyCancelContractResp) Reset()         { *m = RejectApplyCancelContractResp{} }
func (m *RejectApplyCancelContractResp) String() string { return proto.CompactTextString(m) }
func (*RejectApplyCancelContractResp) ProtoMessage()    {}
func (*RejectApplyCancelContractResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{41}
}
func (m *RejectApplyCancelContractResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RejectApplyCancelContractResp.Unmarshal(m, b)
}
func (m *RejectApplyCancelContractResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RejectApplyCancelContractResp.Marshal(b, m, deterministic)
}
func (dst *RejectApplyCancelContractResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RejectApplyCancelContractResp.Merge(dst, src)
}
func (m *RejectApplyCancelContractResp) XXX_Size() int {
	return xxx_messageInfo_RejectApplyCancelContractResp.Size(m)
}
func (m *RejectApplyCancelContractResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RejectApplyCancelContractResp.DiscardUnknown(m)
}

var xxx_messageInfo_RejectApplyCancelContractResp proto.InternalMessageInfo

type ContractWorker struct {
	WorkerType           uint32   `protobuf:"varint,1,opt,name=worker_type,json=workerType,proto3" json:"worker_type,omitempty"`
	WorkerName           string   `protobuf:"bytes,2,opt,name=worker_name,json=workerName,proto3" json:"worker_name,omitempty"`
	WorkerDesc           string   `protobuf:"bytes,3,opt,name=worker_desc,json=workerDesc,proto3" json:"worker_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ContractWorker) Reset()         { *m = ContractWorker{} }
func (m *ContractWorker) String() string { return proto.CompactTextString(m) }
func (*ContractWorker) ProtoMessage()    {}
func (*ContractWorker) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{42}
}
func (m *ContractWorker) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ContractWorker.Unmarshal(m, b)
}
func (m *ContractWorker) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ContractWorker.Marshal(b, m, deterministic)
}
func (dst *ContractWorker) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ContractWorker.Merge(dst, src)
}
func (m *ContractWorker) XXX_Size() int {
	return xxx_messageInfo_ContractWorker.Size(m)
}
func (m *ContractWorker) XXX_DiscardUnknown() {
	xxx_messageInfo_ContractWorker.DiscardUnknown(m)
}

var xxx_messageInfo_ContractWorker proto.InternalMessageInfo

func (m *ContractWorker) GetWorkerType() uint32 {
	if m != nil {
		return m.WorkerType
	}
	return 0
}

func (m *ContractWorker) GetWorkerName() string {
	if m != nil {
		return m.WorkerName
	}
	return ""
}

func (m *ContractWorker) GetWorkerDesc() string {
	if m != nil {
		return m.WorkerDesc
	}
	return ""
}

type ContractPrivilege struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Icon                 string   `protobuf:"bytes,2,opt,name=icon,proto3" json:"icon,omitempty"`
	IsSelect             bool     `protobuf:"varint,3,opt,name=is_select,json=isSelect,proto3" json:"is_select,omitempty"`
	Note                 string   `protobuf:"bytes,4,opt,name=note,proto3" json:"note,omitempty"`
	IsNew                bool     `protobuf:"varint,5,opt,name=is_new,json=isNew,proto3" json:"is_new,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ContractPrivilege) Reset()         { *m = ContractPrivilege{} }
func (m *ContractPrivilege) String() string { return proto.CompactTextString(m) }
func (*ContractPrivilege) ProtoMessage()    {}
func (*ContractPrivilege) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{43}
}
func (m *ContractPrivilege) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ContractPrivilege.Unmarshal(m, b)
}
func (m *ContractPrivilege) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ContractPrivilege.Marshal(b, m, deterministic)
}
func (dst *ContractPrivilege) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ContractPrivilege.Merge(dst, src)
}
func (m *ContractPrivilege) XXX_Size() int {
	return xxx_messageInfo_ContractPrivilege.Size(m)
}
func (m *ContractPrivilege) XXX_DiscardUnknown() {
	xxx_messageInfo_ContractPrivilege.DiscardUnknown(m)
}

var xxx_messageInfo_ContractPrivilege proto.InternalMessageInfo

func (m *ContractPrivilege) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ContractPrivilege) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *ContractPrivilege) GetIsSelect() bool {
	if m != nil {
		return m.IsSelect
	}
	return false
}

func (m *ContractPrivilege) GetNote() string {
	if m != nil {
		return m.Note
	}
	return ""
}

func (m *ContractPrivilege) GetIsNew() bool {
	if m != nil {
		return m.IsNew
	}
	return false
}

type ContractPrivilegeGroup struct {
	Name                 string               `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	PrivilegeList        []*ContractPrivilege `protobuf:"bytes,2,rep,name=privilege_list,json=privilegeList,proto3" json:"privilege_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *ContractPrivilegeGroup) Reset()         { *m = ContractPrivilegeGroup{} }
func (m *ContractPrivilegeGroup) String() string { return proto.CompactTextString(m) }
func (*ContractPrivilegeGroup) ProtoMessage()    {}
func (*ContractPrivilegeGroup) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{44}
}
func (m *ContractPrivilegeGroup) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ContractPrivilegeGroup.Unmarshal(m, b)
}
func (m *ContractPrivilegeGroup) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ContractPrivilegeGroup.Marshal(b, m, deterministic)
}
func (dst *ContractPrivilegeGroup) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ContractPrivilegeGroup.Merge(dst, src)
}
func (m *ContractPrivilegeGroup) XXX_Size() int {
	return xxx_messageInfo_ContractPrivilegeGroup.Size(m)
}
func (m *ContractPrivilegeGroup) XXX_DiscardUnknown() {
	xxx_messageInfo_ContractPrivilegeGroup.DiscardUnknown(m)
}

var xxx_messageInfo_ContractPrivilegeGroup proto.InternalMessageInfo

func (m *ContractPrivilegeGroup) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ContractPrivilegeGroup) GetPrivilegeList() []*ContractPrivilege {
	if m != nil {
		return m.PrivilegeList
	}
	return nil
}

type GetContractPrivilegeListReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	WorkerType           uint32   `protobuf:"varint,2,opt,name=worker_type,json=workerType,proto3" json:"worker_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetContractPrivilegeListReq) Reset()         { *m = GetContractPrivilegeListReq{} }
func (m *GetContractPrivilegeListReq) String() string { return proto.CompactTextString(m) }
func (*GetContractPrivilegeListReq) ProtoMessage()    {}
func (*GetContractPrivilegeListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{45}
}
func (m *GetContractPrivilegeListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetContractPrivilegeListReq.Unmarshal(m, b)
}
func (m *GetContractPrivilegeListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetContractPrivilegeListReq.Marshal(b, m, deterministic)
}
func (dst *GetContractPrivilegeListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetContractPrivilegeListReq.Merge(dst, src)
}
func (m *GetContractPrivilegeListReq) XXX_Size() int {
	return xxx_messageInfo_GetContractPrivilegeListReq.Size(m)
}
func (m *GetContractPrivilegeListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetContractPrivilegeListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetContractPrivilegeListReq proto.InternalMessageInfo

func (m *GetContractPrivilegeListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetContractPrivilegeListReq) GetWorkerType() uint32 {
	if m != nil {
		return m.WorkerType
	}
	return 0
}

type GetContractPrivilegeListResp struct {
	GroupList            []*ContractPrivilegeGroup `protobuf:"bytes,1,rep,name=group_list,json=groupList,proto3" json:"group_list,omitempty"`
	WorkerList           []*ContractWorker         `protobuf:"bytes,2,rep,name=worker_list,json=workerList,proto3" json:"worker_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetContractPrivilegeListResp) Reset()         { *m = GetContractPrivilegeListResp{} }
func (m *GetContractPrivilegeListResp) String() string { return proto.CompactTextString(m) }
func (*GetContractPrivilegeListResp) ProtoMessage()    {}
func (*GetContractPrivilegeListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{46}
}
func (m *GetContractPrivilegeListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetContractPrivilegeListResp.Unmarshal(m, b)
}
func (m *GetContractPrivilegeListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetContractPrivilegeListResp.Marshal(b, m, deterministic)
}
func (dst *GetContractPrivilegeListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetContractPrivilegeListResp.Merge(dst, src)
}
func (m *GetContractPrivilegeListResp) XXX_Size() int {
	return xxx_messageInfo_GetContractPrivilegeListResp.Size(m)
}
func (m *GetContractPrivilegeListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetContractPrivilegeListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetContractPrivilegeListResp proto.InternalMessageInfo

func (m *GetContractPrivilegeListResp) GetGroupList() []*ContractPrivilegeGroup {
	if m != nil {
		return m.GroupList
	}
	return nil
}

func (m *GetContractPrivilegeListResp) GetWorkerList() []*ContractWorker {
	if m != nil {
		return m.WorkerList
	}
	return nil
}

type SignLiveSubmit struct {
	Tag                  uint32   `protobuf:"varint,1,opt,name=tag,proto3" json:"tag,omitempty"`
	Link                 string   `protobuf:"bytes,2,opt,name=link,proto3" json:"link,omitempty"`
	Contact              string   `protobuf:"bytes,3,opt,name=contact,proto3" json:"contact,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SignLiveSubmit) Reset()         { *m = SignLiveSubmit{} }
func (m *SignLiveSubmit) String() string { return proto.CompactTextString(m) }
func (*SignLiveSubmit) ProtoMessage()    {}
func (*SignLiveSubmit) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{47}
}
func (m *SignLiveSubmit) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SignLiveSubmit.Unmarshal(m, b)
}
func (m *SignLiveSubmit) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SignLiveSubmit.Marshal(b, m, deterministic)
}
func (dst *SignLiveSubmit) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SignLiveSubmit.Merge(dst, src)
}
func (m *SignLiveSubmit) XXX_Size() int {
	return xxx_messageInfo_SignLiveSubmit.Size(m)
}
func (m *SignLiveSubmit) XXX_DiscardUnknown() {
	xxx_messageInfo_SignLiveSubmit.DiscardUnknown(m)
}

var xxx_messageInfo_SignLiveSubmit proto.InternalMessageInfo

func (m *SignLiveSubmit) GetTag() uint32 {
	if m != nil {
		return m.Tag
	}
	return 0
}

func (m *SignLiveSubmit) GetLink() string {
	if m != nil {
		return m.Link
	}
	return ""
}

func (m *SignLiveSubmit) GetContact() string {
	if m != nil {
		return m.Contact
	}
	return ""
}

// 申请签约
type ApplySignContractReq struct {
	GuildId              uint32          `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	IdentityType         uint32          `protobuf:"varint,2,opt,name=identity_type,json=identityType,proto3" json:"identity_type,omitempty"`
	Duration             uint32          `protobuf:"varint,3,opt,name=duration,proto3" json:"duration,omitempty"`
	WorkerType           uint32          `protobuf:"varint,4,opt,name=worker_type,json=workerType,proto3" json:"worker_type,omitempty"`
	Submit               *SignLiveSubmit `protobuf:"bytes,5,opt,name=submit,proto3" json:"submit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ApplySignContractReq) Reset()         { *m = ApplySignContractReq{} }
func (m *ApplySignContractReq) String() string { return proto.CompactTextString(m) }
func (*ApplySignContractReq) ProtoMessage()    {}
func (*ApplySignContractReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{48}
}
func (m *ApplySignContractReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplySignContractReq.Unmarshal(m, b)
}
func (m *ApplySignContractReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplySignContractReq.Marshal(b, m, deterministic)
}
func (dst *ApplySignContractReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplySignContractReq.Merge(dst, src)
}
func (m *ApplySignContractReq) XXX_Size() int {
	return xxx_messageInfo_ApplySignContractReq.Size(m)
}
func (m *ApplySignContractReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplySignContractReq.DiscardUnknown(m)
}

var xxx_messageInfo_ApplySignContractReq proto.InternalMessageInfo

func (m *ApplySignContractReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *ApplySignContractReq) GetIdentityType() uint32 {
	if m != nil {
		return m.IdentityType
	}
	return 0
}

func (m *ApplySignContractReq) GetDuration() uint32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

func (m *ApplySignContractReq) GetWorkerType() uint32 {
	if m != nil {
		return m.WorkerType
	}
	return 0
}

func (m *ApplySignContractReq) GetSubmit() *SignLiveSubmit {
	if m != nil {
		return m.Submit
	}
	return nil
}

type ApplySignContractResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplySignContractResp) Reset()         { *m = ApplySignContractResp{} }
func (m *ApplySignContractResp) String() string { return proto.CompactTextString(m) }
func (*ApplySignContractResp) ProtoMessage()    {}
func (*ApplySignContractResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{49}
}
func (m *ApplySignContractResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplySignContractResp.Unmarshal(m, b)
}
func (m *ApplySignContractResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplySignContractResp.Marshal(b, m, deterministic)
}
func (dst *ApplySignContractResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplySignContractResp.Merge(dst, src)
}
func (m *ApplySignContractResp) XXX_Size() int {
	return xxx_messageInfo_ApplySignContractResp.Size(m)
}
func (m *ApplySignContractResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplySignContractResp.DiscardUnknown(m)
}

var xxx_messageInfo_ApplySignContractResp proto.InternalMessageInfo

// 晋升信息
type PromoteInfo struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	SignMonths           uint32   `protobuf:"varint,2,opt,name=sign_months,json=signMonths,proto3" json:"sign_months,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromoteInfo) Reset()         { *m = PromoteInfo{} }
func (m *PromoteInfo) String() string { return proto.CompactTextString(m) }
func (*PromoteInfo) ProtoMessage()    {}
func (*PromoteInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{50}
}
func (m *PromoteInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromoteInfo.Unmarshal(m, b)
}
func (m *PromoteInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromoteInfo.Marshal(b, m, deterministic)
}
func (dst *PromoteInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromoteInfo.Merge(dst, src)
}
func (m *PromoteInfo) XXX_Size() int {
	return xxx_messageInfo_PromoteInfo.Size(m)
}
func (m *PromoteInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PromoteInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PromoteInfo proto.InternalMessageInfo

func (m *PromoteInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PromoteInfo) GetSignMonths() uint32 {
	if m != nil {
		return m.SignMonths
	}
	return 0
}

// 签约信息
type UserContractInfo struct {
	WorkerType           uint32   `protobuf:"varint,1,opt,name=worker_type,json=workerType,proto3" json:"worker_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserContractInfo) Reset()         { *m = UserContractInfo{} }
func (m *UserContractInfo) String() string { return proto.CompactTextString(m) }
func (*UserContractInfo) ProtoMessage()    {}
func (*UserContractInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{51}
}
func (m *UserContractInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserContractInfo.Unmarshal(m, b)
}
func (m *UserContractInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserContractInfo.Marshal(b, m, deterministic)
}
func (dst *UserContractInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserContractInfo.Merge(dst, src)
}
func (m *UserContractInfo) XXX_Size() int {
	return xxx_messageInfo_UserContractInfo.Size(m)
}
func (m *UserContractInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserContractInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserContractInfo proto.InternalMessageInfo

func (m *UserContractInfo) GetWorkerType() uint32 {
	if m != nil {
		return m.WorkerType
	}
	return 0
}

// 签约管理， 获取用户的签约相关信息 /contract-http-logic/contract/GetUserContractInfo
type GetUserContractInfoReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserContractInfoReq) Reset()         { *m = GetUserContractInfoReq{} }
func (m *GetUserContractInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserContractInfoReq) ProtoMessage()    {}
func (*GetUserContractInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{52}
}
func (m *GetUserContractInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserContractInfoReq.Unmarshal(m, b)
}
func (m *GetUserContractInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserContractInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserContractInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserContractInfoReq.Merge(dst, src)
}
func (m *GetUserContractInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserContractInfoReq.Size(m)
}
func (m *GetUserContractInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserContractInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserContractInfoReq proto.InternalMessageInfo

type GetUserContractInfoResp struct {
	PromoteInfo          *PromoteInfo      `protobuf:"bytes,1,opt,name=promote_info,json=promoteInfo,proto3" json:"promote_info,omitempty"`
	ContractInfo         *UserContractInfo `protobuf:"bytes,2,opt,name=contract_info,json=contractInfo,proto3" json:"contract_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetUserContractInfoResp) Reset()         { *m = GetUserContractInfoResp{} }
func (m *GetUserContractInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserContractInfoResp) ProtoMessage()    {}
func (*GetUserContractInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{53}
}
func (m *GetUserContractInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserContractInfoResp.Unmarshal(m, b)
}
func (m *GetUserContractInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserContractInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserContractInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserContractInfoResp.Merge(dst, src)
}
func (m *GetUserContractInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserContractInfoResp.Size(m)
}
func (m *GetUserContractInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserContractInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserContractInfoResp proto.InternalMessageInfo

func (m *GetUserContractInfoResp) GetPromoteInfo() *PromoteInfo {
	if m != nil {
		return m.PromoteInfo
	}
	return nil
}

func (m *GetUserContractInfoResp) GetContractInfo() *UserContractInfo {
	if m != nil {
		return m.ContractInfo
	}
	return nil
}

// 处理晋升邀请
type ProcPromoteInviteReq struct {
	ProcRes              uint32   `protobuf:"varint,1,opt,name=proc_res,json=procRes,proto3" json:"proc_res,omitempty"`
	Id                   uint32   `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProcPromoteInviteReq) Reset()         { *m = ProcPromoteInviteReq{} }
func (m *ProcPromoteInviteReq) String() string { return proto.CompactTextString(m) }
func (*ProcPromoteInviteReq) ProtoMessage()    {}
func (*ProcPromoteInviteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{54}
}
func (m *ProcPromoteInviteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProcPromoteInviteReq.Unmarshal(m, b)
}
func (m *ProcPromoteInviteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProcPromoteInviteReq.Marshal(b, m, deterministic)
}
func (dst *ProcPromoteInviteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProcPromoteInviteReq.Merge(dst, src)
}
func (m *ProcPromoteInviteReq) XXX_Size() int {
	return xxx_messageInfo_ProcPromoteInviteReq.Size(m)
}
func (m *ProcPromoteInviteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ProcPromoteInviteReq.DiscardUnknown(m)
}

var xxx_messageInfo_ProcPromoteInviteReq proto.InternalMessageInfo

func (m *ProcPromoteInviteReq) GetProcRes() uint32 {
	if m != nil {
		return m.ProcRes
	}
	return 0
}

func (m *ProcPromoteInviteReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type ProcPromoteInviteResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProcPromoteInviteResp) Reset()         { *m = ProcPromoteInviteResp{} }
func (m *ProcPromoteInviteResp) String() string { return proto.CompactTextString(m) }
func (*ProcPromoteInviteResp) ProtoMessage()    {}
func (*ProcPromoteInviteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{55}
}
func (m *ProcPromoteInviteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProcPromoteInviteResp.Unmarshal(m, b)
}
func (m *ProcPromoteInviteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProcPromoteInviteResp.Marshal(b, m, deterministic)
}
func (dst *ProcPromoteInviteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProcPromoteInviteResp.Merge(dst, src)
}
func (m *ProcPromoteInviteResp) XXX_Size() int {
	return xxx_messageInfo_ProcPromoteInviteResp.Size(m)
}
func (m *ProcPromoteInviteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ProcPromoteInviteResp.DiscardUnknown(m)
}

var xxx_messageInfo_ProcPromoteInviteResp proto.InternalMessageInfo

// 获取公会的管理员列表
type GetGuildMgrListReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildMgrListReq) Reset()         { *m = GetGuildMgrListReq{} }
func (m *GetGuildMgrListReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildMgrListReq) ProtoMessage()    {}
func (*GetGuildMgrListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{56}
}
func (m *GetGuildMgrListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildMgrListReq.Unmarshal(m, b)
}
func (m *GetGuildMgrListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildMgrListReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildMgrListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildMgrListReq.Merge(dst, src)
}
func (m *GetGuildMgrListReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildMgrListReq.Size(m)
}
func (m *GetGuildMgrListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildMgrListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildMgrListReq proto.InternalMessageInfo

func (m *GetGuildMgrListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetGuildMgrListResp struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildMgrListResp) Reset()         { *m = GetGuildMgrListResp{} }
func (m *GetGuildMgrListResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildMgrListResp) ProtoMessage()    {}
func (*GetGuildMgrListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{57}
}
func (m *GetGuildMgrListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildMgrListResp.Unmarshal(m, b)
}
func (m *GetGuildMgrListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildMgrListResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildMgrListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildMgrListResp.Merge(dst, src)
}
func (m *GetGuildMgrListResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildMgrListResp.Size(m)
}
func (m *GetGuildMgrListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildMgrListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildMgrListResp proto.InternalMessageInfo

func (m *GetGuildMgrListResp) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

// 审核上传的视频
type CensorVideoReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	VideoKey             []string `protobuf:"bytes,3,rep,name=video_key,json=videoKey,proto3" json:"video_key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CensorVideoReq) Reset()         { *m = CensorVideoReq{} }
func (m *CensorVideoReq) String() string { return proto.CompactTextString(m) }
func (*CensorVideoReq) ProtoMessage()    {}
func (*CensorVideoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{58}
}
func (m *CensorVideoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CensorVideoReq.Unmarshal(m, b)
}
func (m *CensorVideoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CensorVideoReq.Marshal(b, m, deterministic)
}
func (dst *CensorVideoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CensorVideoReq.Merge(dst, src)
}
func (m *CensorVideoReq) XXX_Size() int {
	return xxx_messageInfo_CensorVideoReq.Size(m)
}
func (m *CensorVideoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CensorVideoReq.DiscardUnknown(m)
}

var xxx_messageInfo_CensorVideoReq proto.InternalMessageInfo

func (m *CensorVideoReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *CensorVideoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CensorVideoReq) GetVideoKey() []string {
	if m != nil {
		return m.VideoKey
	}
	return nil
}

type CensorVideoResp struct {
	CensorResult         []*CensorResult `protobuf:"bytes,1,rep,name=censor_result,json=censorResult,proto3" json:"censor_result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *CensorVideoResp) Reset()         { *m = CensorVideoResp{} }
func (m *CensorVideoResp) String() string { return proto.CompactTextString(m) }
func (*CensorVideoResp) ProtoMessage()    {}
func (*CensorVideoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{59}
}
func (m *CensorVideoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CensorVideoResp.Unmarshal(m, b)
}
func (m *CensorVideoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CensorVideoResp.Marshal(b, m, deterministic)
}
func (dst *CensorVideoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CensorVideoResp.Merge(dst, src)
}
func (m *CensorVideoResp) XXX_Size() int {
	return xxx_messageInfo_CensorVideoResp.Size(m)
}
func (m *CensorVideoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CensorVideoResp.DiscardUnknown(m)
}

var xxx_messageInfo_CensorVideoResp proto.InternalMessageInfo

func (m *CensorVideoResp) GetCensorResult() []*CensorResult {
	if m != nil {
		return m.CensorResult
	}
	return nil
}

type CensorResult struct {
	VideoKey             string   `protobuf:"bytes,1,opt,name=video_key,json=videoKey,proto3" json:"video_key,omitempty"`
	CensorKey            string   `protobuf:"bytes,2,opt,name=censor_key,json=censorKey,proto3" json:"censor_key,omitempty"`
	TranscodeUrl         string   `protobuf:"bytes,3,opt,name=transcode_url,json=transcodeUrl,proto3" json:"transcode_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CensorResult) Reset()         { *m = CensorResult{} }
func (m *CensorResult) String() string { return proto.CompactTextString(m) }
func (*CensorResult) ProtoMessage()    {}
func (*CensorResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{60}
}
func (m *CensorResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CensorResult.Unmarshal(m, b)
}
func (m *CensorResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CensorResult.Marshal(b, m, deterministic)
}
func (dst *CensorResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CensorResult.Merge(dst, src)
}
func (m *CensorResult) XXX_Size() int {
	return xxx_messageInfo_CensorResult.Size(m)
}
func (m *CensorResult) XXX_DiscardUnknown() {
	xxx_messageInfo_CensorResult.DiscardUnknown(m)
}

var xxx_messageInfo_CensorResult proto.InternalMessageInfo

func (m *CensorResult) GetVideoKey() string {
	if m != nil {
		return m.VideoKey
	}
	return ""
}

func (m *CensorResult) GetCensorKey() string {
	if m != nil {
		return m.CensorKey
	}
	return ""
}

func (m *CensorResult) GetTranscodeUrl() string {
	if m != nil {
		return m.TranscodeUrl
	}
	return ""
}

// 获取是否需要进行成员身份确认
type GetNeedConfirmWorkerTypeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNeedConfirmWorkerTypeReq) Reset()         { *m = GetNeedConfirmWorkerTypeReq{} }
func (m *GetNeedConfirmWorkerTypeReq) String() string { return proto.CompactTextString(m) }
func (*GetNeedConfirmWorkerTypeReq) ProtoMessage()    {}
func (*GetNeedConfirmWorkerTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{61}
}
func (m *GetNeedConfirmWorkerTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNeedConfirmWorkerTypeReq.Unmarshal(m, b)
}
func (m *GetNeedConfirmWorkerTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNeedConfirmWorkerTypeReq.Marshal(b, m, deterministic)
}
func (dst *GetNeedConfirmWorkerTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNeedConfirmWorkerTypeReq.Merge(dst, src)
}
func (m *GetNeedConfirmWorkerTypeReq) XXX_Size() int {
	return xxx_messageInfo_GetNeedConfirmWorkerTypeReq.Size(m)
}
func (m *GetNeedConfirmWorkerTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNeedConfirmWorkerTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNeedConfirmWorkerTypeReq proto.InternalMessageInfo

func (m *GetNeedConfirmWorkerTypeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetNeedConfirmWorkerTypeReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetNeedConfirmWorkerTypeResp struct {
	NeedConfirm          bool     `protobuf:"varint,1,opt,name=need_confirm,json=needConfirm,proto3" json:"need_confirm,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNeedConfirmWorkerTypeResp) Reset()         { *m = GetNeedConfirmWorkerTypeResp{} }
func (m *GetNeedConfirmWorkerTypeResp) String() string { return proto.CompactTextString(m) }
func (*GetNeedConfirmWorkerTypeResp) ProtoMessage()    {}
func (*GetNeedConfirmWorkerTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{62}
}
func (m *GetNeedConfirmWorkerTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNeedConfirmWorkerTypeResp.Unmarshal(m, b)
}
func (m *GetNeedConfirmWorkerTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNeedConfirmWorkerTypeResp.Marshal(b, m, deterministic)
}
func (dst *GetNeedConfirmWorkerTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNeedConfirmWorkerTypeResp.Merge(dst, src)
}
func (m *GetNeedConfirmWorkerTypeResp) XXX_Size() int {
	return xxx_messageInfo_GetNeedConfirmWorkerTypeResp.Size(m)
}
func (m *GetNeedConfirmWorkerTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNeedConfirmWorkerTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNeedConfirmWorkerTypeResp proto.InternalMessageInfo

func (m *GetNeedConfirmWorkerTypeResp) GetNeedConfirm() bool {
	if m != nil {
		return m.NeedConfirm
	}
	return false
}

// 进行成员身份修改
type ModifyWorkerTypeReq struct {
	WorkerType           uint32   `protobuf:"varint,1,opt,name=worker_type,json=workerType,proto3" json:"worker_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyWorkerTypeReq) Reset()         { *m = ModifyWorkerTypeReq{} }
func (m *ModifyWorkerTypeReq) String() string { return proto.CompactTextString(m) }
func (*ModifyWorkerTypeReq) ProtoMessage()    {}
func (*ModifyWorkerTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{63}
}
func (m *ModifyWorkerTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyWorkerTypeReq.Unmarshal(m, b)
}
func (m *ModifyWorkerTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyWorkerTypeReq.Marshal(b, m, deterministic)
}
func (dst *ModifyWorkerTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyWorkerTypeReq.Merge(dst, src)
}
func (m *ModifyWorkerTypeReq) XXX_Size() int {
	return xxx_messageInfo_ModifyWorkerTypeReq.Size(m)
}
func (m *ModifyWorkerTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyWorkerTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyWorkerTypeReq proto.InternalMessageInfo

func (m *ModifyWorkerTypeReq) GetWorkerType() uint32 {
	if m != nil {
		return m.WorkerType
	}
	return 0
}

type ModifyWorkerTypeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyWorkerTypeResp) Reset()         { *m = ModifyWorkerTypeResp{} }
func (m *ModifyWorkerTypeResp) String() string { return proto.CompactTextString(m) }
func (*ModifyWorkerTypeResp) ProtoMessage()    {}
func (*ModifyWorkerTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{64}
}
func (m *ModifyWorkerTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyWorkerTypeResp.Unmarshal(m, b)
}
func (m *ModifyWorkerTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyWorkerTypeResp.Marshal(b, m, deterministic)
}
func (dst *ModifyWorkerTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyWorkerTypeResp.Merge(dst, src)
}
func (m *ModifyWorkerTypeResp) XXX_Size() int {
	return xxx_messageInfo_ModifyWorkerTypeResp.Size(m)
}
func (m *ModifyWorkerTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyWorkerTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyWorkerTypeResp proto.InternalMessageInfo

// 新主播任务
type AnchorLevelNewTask struct {
	RemainTime             uint32   `protobuf:"varint,1,opt,name=remain_time,json=remainTime,proto3" json:"remain_time,omitempty"`
	NeedWeekActiveDays     uint32   `protobuf:"varint,2,opt,name=need_week_active_days,json=needWeekActiveDays,proto3" json:"need_week_active_days,omitempty"`
	RemainWeekActiveDays   uint32   `protobuf:"varint,3,opt,name=remain_week_active_days,json=remainWeekActiveDays,proto3" json:"remain_week_active_days,omitempty"`
	NeedWeekPlatformDays   uint32   `protobuf:"varint,4,opt,name=need_week_platform_days,json=needWeekPlatformDays,proto3" json:"need_week_platform_days,omitempty"`
	RemainWeekPlatformDays uint32   `protobuf:"varint,5,opt,name=remain_week_platform_days,json=remainWeekPlatformDays,proto3" json:"remain_week_platform_days,omitempty"`
	WeekGiftValue          uint32   `protobuf:"varint,6,opt,name=week_gift_value,json=weekGiftValue,proto3" json:"week_gift_value,omitempty"`
	AnchorCheckLevel       string   `protobuf:"bytes,7,opt,name=anchor_check_level,json=anchorCheckLevel,proto3" json:"anchor_check_level,omitempty"`
	WeekNum                uint32   `protobuf:"varint,8,opt,name=week_num,json=weekNum,proto3" json:"week_num,omitempty"`
	TaskType               uint32   `protobuf:"varint,9,opt,name=task_type,json=taskType,proto3" json:"task_type,omitempty"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *AnchorLevelNewTask) Reset()         { *m = AnchorLevelNewTask{} }
func (m *AnchorLevelNewTask) String() string { return proto.CompactTextString(m) }
func (*AnchorLevelNewTask) ProtoMessage()    {}
func (*AnchorLevelNewTask) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{65}
}
func (m *AnchorLevelNewTask) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorLevelNewTask.Unmarshal(m, b)
}
func (m *AnchorLevelNewTask) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorLevelNewTask.Marshal(b, m, deterministic)
}
func (dst *AnchorLevelNewTask) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorLevelNewTask.Merge(dst, src)
}
func (m *AnchorLevelNewTask) XXX_Size() int {
	return xxx_messageInfo_AnchorLevelNewTask.Size(m)
}
func (m *AnchorLevelNewTask) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorLevelNewTask.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorLevelNewTask proto.InternalMessageInfo

func (m *AnchorLevelNewTask) GetRemainTime() uint32 {
	if m != nil {
		return m.RemainTime
	}
	return 0
}

func (m *AnchorLevelNewTask) GetNeedWeekActiveDays() uint32 {
	if m != nil {
		return m.NeedWeekActiveDays
	}
	return 0
}

func (m *AnchorLevelNewTask) GetRemainWeekActiveDays() uint32 {
	if m != nil {
		return m.RemainWeekActiveDays
	}
	return 0
}

func (m *AnchorLevelNewTask) GetNeedWeekPlatformDays() uint32 {
	if m != nil {
		return m.NeedWeekPlatformDays
	}
	return 0
}

func (m *AnchorLevelNewTask) GetRemainWeekPlatformDays() uint32 {
	if m != nil {
		return m.RemainWeekPlatformDays
	}
	return 0
}

func (m *AnchorLevelNewTask) GetWeekGiftValue() uint32 {
	if m != nil {
		return m.WeekGiftValue
	}
	return 0
}

func (m *AnchorLevelNewTask) GetAnchorCheckLevel() string {
	if m != nil {
		return m.AnchorCheckLevel
	}
	return ""
}

func (m *AnchorLevelNewTask) GetWeekNum() uint32 {
	if m != nil {
		return m.WeekNum
	}
	return 0
}

func (m *AnchorLevelNewTask) GetTaskType() uint32 {
	if m != nil {
		return m.TaskType
	}
	return 0
}

// 查看合约权益与解约方式变更
type GetContractChangeInfoReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetContractChangeInfoReq) Reset()         { *m = GetContractChangeInfoReq{} }
func (m *GetContractChangeInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetContractChangeInfoReq) ProtoMessage()    {}
func (*GetContractChangeInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{66}
}
func (m *GetContractChangeInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetContractChangeInfoReq.Unmarshal(m, b)
}
func (m *GetContractChangeInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetContractChangeInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetContractChangeInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetContractChangeInfoReq.Merge(dst, src)
}
func (m *GetContractChangeInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetContractChangeInfoReq.Size(m)
}
func (m *GetContractChangeInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetContractChangeInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetContractChangeInfoReq proto.InternalMessageInfo

func (m *GetContractChangeInfoReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetContractChangeInfoResp struct {
	GroupList            []*ContractPrivilegeGroupChange `protobuf:"bytes,1,rep,name=group_list,json=groupList,proto3" json:"group_list,omitempty"`
	CancelTypes          []*CanCancelContractChange      `protobuf:"bytes,2,rep,name=cancel_types,json=cancelTypes,proto3" json:"cancel_types,omitempty"`
	IsAccept             bool                            `protobuf:"varint,3,opt,name=is_accept,json=isAccept,proto3" json:"is_accept,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *GetContractChangeInfoResp) Reset()         { *m = GetContractChangeInfoResp{} }
func (m *GetContractChangeInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetContractChangeInfoResp) ProtoMessage()    {}
func (*GetContractChangeInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{67}
}
func (m *GetContractChangeInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetContractChangeInfoResp.Unmarshal(m, b)
}
func (m *GetContractChangeInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetContractChangeInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetContractChangeInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetContractChangeInfoResp.Merge(dst, src)
}
func (m *GetContractChangeInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetContractChangeInfoResp.Size(m)
}
func (m *GetContractChangeInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetContractChangeInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetContractChangeInfoResp proto.InternalMessageInfo

func (m *GetContractChangeInfoResp) GetGroupList() []*ContractPrivilegeGroupChange {
	if m != nil {
		return m.GroupList
	}
	return nil
}

func (m *GetContractChangeInfoResp) GetCancelTypes() []*CanCancelContractChange {
	if m != nil {
		return m.CancelTypes
	}
	return nil
}

func (m *GetContractChangeInfoResp) GetIsAccept() bool {
	if m != nil {
		return m.IsAccept
	}
	return false
}

type ContractPrivilegeGroupChange struct {
	Name                 string               `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	PrivilegeList        []*ContractPrivilege `protobuf:"bytes,2,rep,name=privilege_list,json=privilegeList,proto3" json:"privilege_list,omitempty"`
	RemovedPrivilegeList []*ContractPrivilege `protobuf:"bytes,3,rep,name=removed_privilege_list,json=removedPrivilegeList,proto3" json:"removed_privilege_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *ContractPrivilegeGroupChange) Reset()         { *m = ContractPrivilegeGroupChange{} }
func (m *ContractPrivilegeGroupChange) String() string { return proto.CompactTextString(m) }
func (*ContractPrivilegeGroupChange) ProtoMessage()    {}
func (*ContractPrivilegeGroupChange) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{68}
}
func (m *ContractPrivilegeGroupChange) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ContractPrivilegeGroupChange.Unmarshal(m, b)
}
func (m *ContractPrivilegeGroupChange) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ContractPrivilegeGroupChange.Marshal(b, m, deterministic)
}
func (dst *ContractPrivilegeGroupChange) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ContractPrivilegeGroupChange.Merge(dst, src)
}
func (m *ContractPrivilegeGroupChange) XXX_Size() int {
	return xxx_messageInfo_ContractPrivilegeGroupChange.Size(m)
}
func (m *ContractPrivilegeGroupChange) XXX_DiscardUnknown() {
	xxx_messageInfo_ContractPrivilegeGroupChange.DiscardUnknown(m)
}

var xxx_messageInfo_ContractPrivilegeGroupChange proto.InternalMessageInfo

func (m *ContractPrivilegeGroupChange) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ContractPrivilegeGroupChange) GetPrivilegeList() []*ContractPrivilege {
	if m != nil {
		return m.PrivilegeList
	}
	return nil
}

func (m *ContractPrivilegeGroupChange) GetRemovedPrivilegeList() []*ContractPrivilege {
	if m != nil {
		return m.RemovedPrivilegeList
	}
	return nil
}

type CanCancelContractChange struct {
	CancelTypes          []*CanCancelContract `protobuf:"bytes,1,rep,name=cancel_types,json=cancelTypes,proto3" json:"cancel_types,omitempty"`
	RemovedCancelTypes   []*CanCancelContract `protobuf:"bytes,2,rep,name=removed_cancel_types,json=removedCancelTypes,proto3" json:"removed_cancel_types,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *CanCancelContractChange) Reset()         { *m = CanCancelContractChange{} }
func (m *CanCancelContractChange) String() string { return proto.CompactTextString(m) }
func (*CanCancelContractChange) ProtoMessage()    {}
func (*CanCancelContractChange) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{69}
}
func (m *CanCancelContractChange) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CanCancelContractChange.Unmarshal(m, b)
}
func (m *CanCancelContractChange) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CanCancelContractChange.Marshal(b, m, deterministic)
}
func (dst *CanCancelContractChange) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CanCancelContractChange.Merge(dst, src)
}
func (m *CanCancelContractChange) XXX_Size() int {
	return xxx_messageInfo_CanCancelContractChange.Size(m)
}
func (m *CanCancelContractChange) XXX_DiscardUnknown() {
	xxx_messageInfo_CanCancelContractChange.DiscardUnknown(m)
}

var xxx_messageInfo_CanCancelContractChange proto.InternalMessageInfo

func (m *CanCancelContractChange) GetCancelTypes() []*CanCancelContract {
	if m != nil {
		return m.CancelTypes
	}
	return nil
}

func (m *CanCancelContractChange) GetRemovedCancelTypes() []*CanCancelContract {
	if m != nil {
		return m.RemovedCancelTypes
	}
	return nil
}

type HandleContractChangeReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	IsAgree              bool     `protobuf:"varint,2,opt,name=is_agree,json=isAgree,proto3" json:"is_agree,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HandleContractChangeReq) Reset()         { *m = HandleContractChangeReq{} }
func (m *HandleContractChangeReq) String() string { return proto.CompactTextString(m) }
func (*HandleContractChangeReq) ProtoMessage()    {}
func (*HandleContractChangeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{70}
}
func (m *HandleContractChangeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleContractChangeReq.Unmarshal(m, b)
}
func (m *HandleContractChangeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleContractChangeReq.Marshal(b, m, deterministic)
}
func (dst *HandleContractChangeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleContractChangeReq.Merge(dst, src)
}
func (m *HandleContractChangeReq) XXX_Size() int {
	return xxx_messageInfo_HandleContractChangeReq.Size(m)
}
func (m *HandleContractChangeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleContractChangeReq.DiscardUnknown(m)
}

var xxx_messageInfo_HandleContractChangeReq proto.InternalMessageInfo

func (m *HandleContractChangeReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *HandleContractChangeReq) GetIsAgree() bool {
	if m != nil {
		return m.IsAgree
	}
	return false
}

type HandleContractChangeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HandleContractChangeResp) Reset()         { *m = HandleContractChangeResp{} }
func (m *HandleContractChangeResp) String() string { return proto.CompactTextString(m) }
func (*HandleContractChangeResp) ProtoMessage()    {}
func (*HandleContractChangeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{71}
}
func (m *HandleContractChangeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleContractChangeResp.Unmarshal(m, b)
}
func (m *HandleContractChangeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleContractChangeResp.Marshal(b, m, deterministic)
}
func (dst *HandleContractChangeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleContractChangeResp.Merge(dst, src)
}
func (m *HandleContractChangeResp) XXX_Size() int {
	return xxx_messageInfo_HandleContractChangeResp.Size(m)
}
func (m *HandleContractChangeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleContractChangeResp.DiscardUnknown(m)
}

var xxx_messageInfo_HandleContractChangeResp proto.InternalMessageInfo

// 查看拒绝理由
type GetRejectReasonReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRejectReasonReq) Reset()         { *m = GetRejectReasonReq{} }
func (m *GetRejectReasonReq) String() string { return proto.CompactTextString(m) }
func (*GetRejectReasonReq) ProtoMessage()    {}
func (*GetRejectReasonReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{72}
}
func (m *GetRejectReasonReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRejectReasonReq.Unmarshal(m, b)
}
func (m *GetRejectReasonReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRejectReasonReq.Marshal(b, m, deterministic)
}
func (dst *GetRejectReasonReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRejectReasonReq.Merge(dst, src)
}
func (m *GetRejectReasonReq) XXX_Size() int {
	return xxx_messageInfo_GetRejectReasonReq.Size(m)
}
func (m *GetRejectReasonReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRejectReasonReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRejectReasonReq proto.InternalMessageInfo

func (m *GetRejectReasonReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetRejectReasonResp struct {
	RejectReason         string              `protobuf:"bytes,1,opt,name=reject_reason,json=rejectReason,proto3" json:"reject_reason,omitempty"`
	ProofList            []*ProofShowContent `protobuf:"bytes,2,rep,name=proof_list,json=proofList,proto3" json:"proof_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetRejectReasonResp) Reset()         { *m = GetRejectReasonResp{} }
func (m *GetRejectReasonResp) String() string { return proto.CompactTextString(m) }
func (*GetRejectReasonResp) ProtoMessage()    {}
func (*GetRejectReasonResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{73}
}
func (m *GetRejectReasonResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRejectReasonResp.Unmarshal(m, b)
}
func (m *GetRejectReasonResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRejectReasonResp.Marshal(b, m, deterministic)
}
func (dst *GetRejectReasonResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRejectReasonResp.Merge(dst, src)
}
func (m *GetRejectReasonResp) XXX_Size() int {
	return xxx_messageInfo_GetRejectReasonResp.Size(m)
}
func (m *GetRejectReasonResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRejectReasonResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRejectReasonResp proto.InternalMessageInfo

func (m *GetRejectReasonResp) GetRejectReason() string {
	if m != nil {
		return m.RejectReason
	}
	return ""
}

func (m *GetRejectReasonResp) GetProofList() []*ProofShowContent {
	if m != nil {
		return m.ProofList
	}
	return nil
}

// 获取新手主播信息
type GetNewbieAnchorInfoReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNewbieAnchorInfoReq) Reset()         { *m = GetNewbieAnchorInfoReq{} }
func (m *GetNewbieAnchorInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetNewbieAnchorInfoReq) ProtoMessage()    {}
func (*GetNewbieAnchorInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{74}
}
func (m *GetNewbieAnchorInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewbieAnchorInfoReq.Unmarshal(m, b)
}
func (m *GetNewbieAnchorInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewbieAnchorInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetNewbieAnchorInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewbieAnchorInfoReq.Merge(dst, src)
}
func (m *GetNewbieAnchorInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetNewbieAnchorInfoReq.Size(m)
}
func (m *GetNewbieAnchorInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewbieAnchorInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewbieAnchorInfoReq proto.InternalMessageInfo

type IdentityObtainDay struct {
	IdentityType         uint32   `protobuf:"varint,1,opt,name=identity_type,json=identityType,proto3" json:"identity_type,omitempty"`
	ObtainDay            uint32   `protobuf:"varint,2,opt,name=obtain_day,json=obtainDay,proto3" json:"obtain_day,omitempty"`
	IsNewbieAnchor       bool     `protobuf:"varint,3,opt,name=is_newbie_anchor,json=isNewbieAnchor,proto3" json:"is_newbie_anchor,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IdentityObtainDay) Reset()         { *m = IdentityObtainDay{} }
func (m *IdentityObtainDay) String() string { return proto.CompactTextString(m) }
func (*IdentityObtainDay) ProtoMessage()    {}
func (*IdentityObtainDay) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{75}
}
func (m *IdentityObtainDay) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IdentityObtainDay.Unmarshal(m, b)
}
func (m *IdentityObtainDay) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IdentityObtainDay.Marshal(b, m, deterministic)
}
func (dst *IdentityObtainDay) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IdentityObtainDay.Merge(dst, src)
}
func (m *IdentityObtainDay) XXX_Size() int {
	return xxx_messageInfo_IdentityObtainDay.Size(m)
}
func (m *IdentityObtainDay) XXX_DiscardUnknown() {
	xxx_messageInfo_IdentityObtainDay.DiscardUnknown(m)
}

var xxx_messageInfo_IdentityObtainDay proto.InternalMessageInfo

func (m *IdentityObtainDay) GetIdentityType() uint32 {
	if m != nil {
		return m.IdentityType
	}
	return 0
}

func (m *IdentityObtainDay) GetObtainDay() uint32 {
	if m != nil {
		return m.ObtainDay
	}
	return 0
}

func (m *IdentityObtainDay) GetIsNewbieAnchor() bool {
	if m != nil {
		return m.IsNewbieAnchor
	}
	return false
}

type GetNewbieAnchorInfoResp struct {
	IdentityList         []*IdentityObtainDay `protobuf:"bytes,2,rep,name=identity_list,json=identityList,proto3" json:"identity_list,omitempty"`
	SignDay              uint32               `protobuf:"varint,3,opt,name=sign_day,json=signDay,proto3" json:"sign_day,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetNewbieAnchorInfoResp) Reset()         { *m = GetNewbieAnchorInfoResp{} }
func (m *GetNewbieAnchorInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetNewbieAnchorInfoResp) ProtoMessage()    {}
func (*GetNewbieAnchorInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{76}
}
func (m *GetNewbieAnchorInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewbieAnchorInfoResp.Unmarshal(m, b)
}
func (m *GetNewbieAnchorInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewbieAnchorInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetNewbieAnchorInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewbieAnchorInfoResp.Merge(dst, src)
}
func (m *GetNewbieAnchorInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetNewbieAnchorInfoResp.Size(m)
}
func (m *GetNewbieAnchorInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewbieAnchorInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewbieAnchorInfoResp proto.InternalMessageInfo

func (m *GetNewbieAnchorInfoResp) GetIdentityList() []*IdentityObtainDay {
	if m != nil {
		return m.IdentityList
	}
	return nil
}

func (m *GetNewbieAnchorInfoResp) GetSignDay() uint32 {
	if m != nil {
		return m.SignDay
	}
	return 0
}

// 知识点任务
type KnowledgeTask struct {
	TaskId               string   `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	TaskName             string   `protobuf:"bytes,2,opt,name=task_name,json=taskName,proto3" json:"task_name,omitempty"`
	JumpUrl              string   `protobuf:"bytes,3,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	Status               uint32   `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KnowledgeTask) Reset()         { *m = KnowledgeTask{} }
func (m *KnowledgeTask) String() string { return proto.CompactTextString(m) }
func (*KnowledgeTask) ProtoMessage()    {}
func (*KnowledgeTask) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{77}
}
func (m *KnowledgeTask) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KnowledgeTask.Unmarshal(m, b)
}
func (m *KnowledgeTask) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KnowledgeTask.Marshal(b, m, deterministic)
}
func (dst *KnowledgeTask) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KnowledgeTask.Merge(dst, src)
}
func (m *KnowledgeTask) XXX_Size() int {
	return xxx_messageInfo_KnowledgeTask.Size(m)
}
func (m *KnowledgeTask) XXX_DiscardUnknown() {
	xxx_messageInfo_KnowledgeTask.DiscardUnknown(m)
}

var xxx_messageInfo_KnowledgeTask proto.InternalMessageInfo

func (m *KnowledgeTask) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

func (m *KnowledgeTask) GetTaskName() string {
	if m != nil {
		return m.TaskName
	}
	return ""
}

func (m *KnowledgeTask) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *KnowledgeTask) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type NewbieGuideContent struct {
	Title                string   `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Content              string   `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	Urls                 []string `protobuf:"bytes,3,rep,name=urls,proto3" json:"urls,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewbieGuideContent) Reset()         { *m = NewbieGuideContent{} }
func (m *NewbieGuideContent) String() string { return proto.CompactTextString(m) }
func (*NewbieGuideContent) ProtoMessage()    {}
func (*NewbieGuideContent) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{78}
}
func (m *NewbieGuideContent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewbieGuideContent.Unmarshal(m, b)
}
func (m *NewbieGuideContent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewbieGuideContent.Marshal(b, m, deterministic)
}
func (dst *NewbieGuideContent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewbieGuideContent.Merge(dst, src)
}
func (m *NewbieGuideContent) XXX_Size() int {
	return xxx_messageInfo_NewbieGuideContent.Size(m)
}
func (m *NewbieGuideContent) XXX_DiscardUnknown() {
	xxx_messageInfo_NewbieGuideContent.DiscardUnknown(m)
}

var xxx_messageInfo_NewbieGuideContent proto.InternalMessageInfo

func (m *NewbieGuideContent) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *NewbieGuideContent) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *NewbieGuideContent) GetUrls() []string {
	if m != nil {
		return m.Urls
	}
	return nil
}

// 子主题信息
type NewbieGuideSubTheme struct {
	ThemeName            string                `protobuf:"bytes,1,opt,name=theme_name,json=themeName,proto3" json:"theme_name,omitempty"`
	ContentList          []*NewbieGuideContent `protobuf:"bytes,2,rep,name=content_list,json=contentList,proto3" json:"content_list,omitempty"`
	TaskList             []*KnowledgeTask      `protobuf:"bytes,3,rep,name=task_list,json=taskList,proto3" json:"task_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *NewbieGuideSubTheme) Reset()         { *m = NewbieGuideSubTheme{} }
func (m *NewbieGuideSubTheme) String() string { return proto.CompactTextString(m) }
func (*NewbieGuideSubTheme) ProtoMessage()    {}
func (*NewbieGuideSubTheme) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{79}
}
func (m *NewbieGuideSubTheme) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewbieGuideSubTheme.Unmarshal(m, b)
}
func (m *NewbieGuideSubTheme) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewbieGuideSubTheme.Marshal(b, m, deterministic)
}
func (dst *NewbieGuideSubTheme) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewbieGuideSubTheme.Merge(dst, src)
}
func (m *NewbieGuideSubTheme) XXX_Size() int {
	return xxx_messageInfo_NewbieGuideSubTheme.Size(m)
}
func (m *NewbieGuideSubTheme) XXX_DiscardUnknown() {
	xxx_messageInfo_NewbieGuideSubTheme.DiscardUnknown(m)
}

var xxx_messageInfo_NewbieGuideSubTheme proto.InternalMessageInfo

func (m *NewbieGuideSubTheme) GetThemeName() string {
	if m != nil {
		return m.ThemeName
	}
	return ""
}

func (m *NewbieGuideSubTheme) GetContentList() []*NewbieGuideContent {
	if m != nil {
		return m.ContentList
	}
	return nil
}

func (m *NewbieGuideSubTheme) GetTaskList() []*KnowledgeTask {
	if m != nil {
		return m.TaskList
	}
	return nil
}

// 主题信息
type NewbieGuideTheme struct {
	ThemeNameUrl         string                 `protobuf:"bytes,1,opt,name=theme_name_url,json=themeNameUrl,proto3" json:"theme_name_url,omitempty"`
	SubThemeList         []*NewbieGuideSubTheme `protobuf:"bytes,2,rep,name=sub_theme_list,json=subThemeList,proto3" json:"sub_theme_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *NewbieGuideTheme) Reset()         { *m = NewbieGuideTheme{} }
func (m *NewbieGuideTheme) String() string { return proto.CompactTextString(m) }
func (*NewbieGuideTheme) ProtoMessage()    {}
func (*NewbieGuideTheme) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{80}
}
func (m *NewbieGuideTheme) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewbieGuideTheme.Unmarshal(m, b)
}
func (m *NewbieGuideTheme) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewbieGuideTheme.Marshal(b, m, deterministic)
}
func (dst *NewbieGuideTheme) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewbieGuideTheme.Merge(dst, src)
}
func (m *NewbieGuideTheme) XXX_Size() int {
	return xxx_messageInfo_NewbieGuideTheme.Size(m)
}
func (m *NewbieGuideTheme) XXX_DiscardUnknown() {
	xxx_messageInfo_NewbieGuideTheme.DiscardUnknown(m)
}

var xxx_messageInfo_NewbieGuideTheme proto.InternalMessageInfo

func (m *NewbieGuideTheme) GetThemeNameUrl() string {
	if m != nil {
		return m.ThemeNameUrl
	}
	return ""
}

func (m *NewbieGuideTheme) GetSubThemeList() []*NewbieGuideSubTheme {
	if m != nil {
		return m.SubThemeList
	}
	return nil
}

// 获取新手指南信息
type GetNewbieAnchorGuideInfoReq struct {
	IdentityType         uint32   `protobuf:"varint,1,opt,name=identity_type,json=identityType,proto3" json:"identity_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNewbieAnchorGuideInfoReq) Reset()         { *m = GetNewbieAnchorGuideInfoReq{} }
func (m *GetNewbieAnchorGuideInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetNewbieAnchorGuideInfoReq) ProtoMessage()    {}
func (*GetNewbieAnchorGuideInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{81}
}
func (m *GetNewbieAnchorGuideInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewbieAnchorGuideInfoReq.Unmarshal(m, b)
}
func (m *GetNewbieAnchorGuideInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewbieAnchorGuideInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetNewbieAnchorGuideInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewbieAnchorGuideInfoReq.Merge(dst, src)
}
func (m *GetNewbieAnchorGuideInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetNewbieAnchorGuideInfoReq.Size(m)
}
func (m *GetNewbieAnchorGuideInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewbieAnchorGuideInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewbieAnchorGuideInfoReq proto.InternalMessageInfo

func (m *GetNewbieAnchorGuideInfoReq) GetIdentityType() uint32 {
	if m != nil {
		return m.IdentityType
	}
	return 0
}

type GetNewbieAnchorGuideInfoResp struct {
	ThemeList            []*NewbieGuideTheme `protobuf:"bytes,1,rep,name=theme_list,json=themeList,proto3" json:"theme_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetNewbieAnchorGuideInfoResp) Reset()         { *m = GetNewbieAnchorGuideInfoResp{} }
func (m *GetNewbieAnchorGuideInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetNewbieAnchorGuideInfoResp) ProtoMessage()    {}
func (*GetNewbieAnchorGuideInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{82}
}
func (m *GetNewbieAnchorGuideInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewbieAnchorGuideInfoResp.Unmarshal(m, b)
}
func (m *GetNewbieAnchorGuideInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewbieAnchorGuideInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetNewbieAnchorGuideInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewbieAnchorGuideInfoResp.Merge(dst, src)
}
func (m *GetNewbieAnchorGuideInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetNewbieAnchorGuideInfoResp.Size(m)
}
func (m *GetNewbieAnchorGuideInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewbieAnchorGuideInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewbieAnchorGuideInfoResp proto.InternalMessageInfo

func (m *GetNewbieAnchorGuideInfoResp) GetThemeList() []*NewbieGuideTheme {
	if m != nil {
		return m.ThemeList
	}
	return nil
}

// 获取新手任务
type GetNewbieAnchorTaskReq struct {
	IdentityType         uint32   `protobuf:"varint,1,opt,name=identity_type,json=identityType,proto3" json:"identity_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNewbieAnchorTaskReq) Reset()         { *m = GetNewbieAnchorTaskReq{} }
func (m *GetNewbieAnchorTaskReq) String() string { return proto.CompactTextString(m) }
func (*GetNewbieAnchorTaskReq) ProtoMessage()    {}
func (*GetNewbieAnchorTaskReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{83}
}
func (m *GetNewbieAnchorTaskReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewbieAnchorTaskReq.Unmarshal(m, b)
}
func (m *GetNewbieAnchorTaskReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewbieAnchorTaskReq.Marshal(b, m, deterministic)
}
func (dst *GetNewbieAnchorTaskReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewbieAnchorTaskReq.Merge(dst, src)
}
func (m *GetNewbieAnchorTaskReq) XXX_Size() int {
	return xxx_messageInfo_GetNewbieAnchorTaskReq.Size(m)
}
func (m *GetNewbieAnchorTaskReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewbieAnchorTaskReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewbieAnchorTaskReq proto.InternalMessageInfo

func (m *GetNewbieAnchorTaskReq) GetIdentityType() uint32 {
	if m != nil {
		return m.IdentityType
	}
	return 0
}

// 新手任务项
type NewbieAnchorTaskItem struct {
	TaskString           string   `protobuf:"bytes,1,opt,name=task_string,json=taskString,proto3" json:"task_string,omitempty"`
	TaskIcon             string   `protobuf:"bytes,2,opt,name=task_icon,json=taskIcon,proto3" json:"task_icon,omitempty"`
	IsCompleted          uint32   `protobuf:"varint,3,opt,name=is_completed,json=isCompleted,proto3" json:"is_completed,omitempty"`
	Progress             uint32   `protobuf:"varint,4,opt,name=progress,proto3" json:"progress,omitempty"`
	Total                uint32   `protobuf:"varint,5,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewbieAnchorTaskItem) Reset()         { *m = NewbieAnchorTaskItem{} }
func (m *NewbieAnchorTaskItem) String() string { return proto.CompactTextString(m) }
func (*NewbieAnchorTaskItem) ProtoMessage()    {}
func (*NewbieAnchorTaskItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{84}
}
func (m *NewbieAnchorTaskItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewbieAnchorTaskItem.Unmarshal(m, b)
}
func (m *NewbieAnchorTaskItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewbieAnchorTaskItem.Marshal(b, m, deterministic)
}
func (dst *NewbieAnchorTaskItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewbieAnchorTaskItem.Merge(dst, src)
}
func (m *NewbieAnchorTaskItem) XXX_Size() int {
	return xxx_messageInfo_NewbieAnchorTaskItem.Size(m)
}
func (m *NewbieAnchorTaskItem) XXX_DiscardUnknown() {
	xxx_messageInfo_NewbieAnchorTaskItem.DiscardUnknown(m)
}

var xxx_messageInfo_NewbieAnchorTaskItem proto.InternalMessageInfo

func (m *NewbieAnchorTaskItem) GetTaskString() string {
	if m != nil {
		return m.TaskString
	}
	return ""
}

func (m *NewbieAnchorTaskItem) GetTaskIcon() string {
	if m != nil {
		return m.TaskIcon
	}
	return ""
}

func (m *NewbieAnchorTaskItem) GetIsCompleted() uint32 {
	if m != nil {
		return m.IsCompleted
	}
	return 0
}

func (m *NewbieAnchorTaskItem) GetProgress() uint32 {
	if m != nil {
		return m.Progress
	}
	return 0
}

func (m *NewbieAnchorTaskItem) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 新手任务日期
type NewbieAnchorTaskDay struct {
	Day                  uint64                     `protobuf:"varint,1,opt,name=day,proto3" json:"day,omitempty"`
	CompleteStatus       NewbieAnchorTaskDay_Status `protobuf:"varint,2,opt,name=complete_status,json=completeStatus,proto3,enum=contract_http_logic.NewbieAnchorTaskDay_Status" json:"complete_status,omitempty"`
	TaskList             []*NewbieAnchorTaskItem    `protobuf:"bytes,3,rep,name=task_list,json=taskList,proto3" json:"task_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *NewbieAnchorTaskDay) Reset()         { *m = NewbieAnchorTaskDay{} }
func (m *NewbieAnchorTaskDay) String() string { return proto.CompactTextString(m) }
func (*NewbieAnchorTaskDay) ProtoMessage()    {}
func (*NewbieAnchorTaskDay) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{85}
}
func (m *NewbieAnchorTaskDay) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewbieAnchorTaskDay.Unmarshal(m, b)
}
func (m *NewbieAnchorTaskDay) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewbieAnchorTaskDay.Marshal(b, m, deterministic)
}
func (dst *NewbieAnchorTaskDay) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewbieAnchorTaskDay.Merge(dst, src)
}
func (m *NewbieAnchorTaskDay) XXX_Size() int {
	return xxx_messageInfo_NewbieAnchorTaskDay.Size(m)
}
func (m *NewbieAnchorTaskDay) XXX_DiscardUnknown() {
	xxx_messageInfo_NewbieAnchorTaskDay.DiscardUnknown(m)
}

var xxx_messageInfo_NewbieAnchorTaskDay proto.InternalMessageInfo

func (m *NewbieAnchorTaskDay) GetDay() uint64 {
	if m != nil {
		return m.Day
	}
	return 0
}

func (m *NewbieAnchorTaskDay) GetCompleteStatus() NewbieAnchorTaskDay_Status {
	if m != nil {
		return m.CompleteStatus
	}
	return NewbieAnchorTaskDay_STATUS_UNSPECIFIED
}

func (m *NewbieAnchorTaskDay) GetTaskList() []*NewbieAnchorTaskItem {
	if m != nil {
		return m.TaskList
	}
	return nil
}

// 获取新手入门任务
type NewbieAnchorTaskBeginner struct {
	Days                 []*NewbieAnchorTaskDay             `protobuf:"bytes,1,rep,name=days,proto3" json:"days,omitempty"`
	CompletedDays        uint32                             `protobuf:"varint,2,opt,name=completed_days,json=completedDays,proto3" json:"completed_days,omitempty"`
	Rewards              []*NewbieAnchorTaskBeginner_Reward `protobuf:"bytes,6,rep,name=rewards,proto3" json:"rewards,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *NewbieAnchorTaskBeginner) Reset()         { *m = NewbieAnchorTaskBeginner{} }
func (m *NewbieAnchorTaskBeginner) String() string { return proto.CompactTextString(m) }
func (*NewbieAnchorTaskBeginner) ProtoMessage()    {}
func (*NewbieAnchorTaskBeginner) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{86}
}
func (m *NewbieAnchorTaskBeginner) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewbieAnchorTaskBeginner.Unmarshal(m, b)
}
func (m *NewbieAnchorTaskBeginner) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewbieAnchorTaskBeginner.Marshal(b, m, deterministic)
}
func (dst *NewbieAnchorTaskBeginner) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewbieAnchorTaskBeginner.Merge(dst, src)
}
func (m *NewbieAnchorTaskBeginner) XXX_Size() int {
	return xxx_messageInfo_NewbieAnchorTaskBeginner.Size(m)
}
func (m *NewbieAnchorTaskBeginner) XXX_DiscardUnknown() {
	xxx_messageInfo_NewbieAnchorTaskBeginner.DiscardUnknown(m)
}

var xxx_messageInfo_NewbieAnchorTaskBeginner proto.InternalMessageInfo

func (m *NewbieAnchorTaskBeginner) GetDays() []*NewbieAnchorTaskDay {
	if m != nil {
		return m.Days
	}
	return nil
}

func (m *NewbieAnchorTaskBeginner) GetCompletedDays() uint32 {
	if m != nil {
		return m.CompletedDays
	}
	return 0
}

func (m *NewbieAnchorTaskBeginner) GetRewards() []*NewbieAnchorTaskBeginner_Reward {
	if m != nil {
		return m.Rewards
	}
	return nil
}

type NewbieAnchorTaskBeginner_Reward struct {
	ConditionDays        uint32             `protobuf:"varint,1,opt,name=condition_days,json=conditionDays,proto3" json:"condition_days,omitempty"`
	RewardText           string             `protobuf:"bytes,2,opt,name=reward_text,json=rewardText,proto3" json:"reward_text,omitempty"`
	RewardIcon           string             `protobuf:"bytes,3,opt,name=reward_icon,json=rewardIcon,proto3" json:"reward_icon,omitempty"`
	ClaimStatus          NewbieRewardStatus `protobuf:"varint,4,opt,name=claim_status,json=claimStatus,proto3,enum=contract_http_logic.NewbieRewardStatus" json:"claim_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *NewbieAnchorTaskBeginner_Reward) Reset()         { *m = NewbieAnchorTaskBeginner_Reward{} }
func (m *NewbieAnchorTaskBeginner_Reward) String() string { return proto.CompactTextString(m) }
func (*NewbieAnchorTaskBeginner_Reward) ProtoMessage()    {}
func (*NewbieAnchorTaskBeginner_Reward) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{86, 0}
}
func (m *NewbieAnchorTaskBeginner_Reward) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewbieAnchorTaskBeginner_Reward.Unmarshal(m, b)
}
func (m *NewbieAnchorTaskBeginner_Reward) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewbieAnchorTaskBeginner_Reward.Marshal(b, m, deterministic)
}
func (dst *NewbieAnchorTaskBeginner_Reward) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewbieAnchorTaskBeginner_Reward.Merge(dst, src)
}
func (m *NewbieAnchorTaskBeginner_Reward) XXX_Size() int {
	return xxx_messageInfo_NewbieAnchorTaskBeginner_Reward.Size(m)
}
func (m *NewbieAnchorTaskBeginner_Reward) XXX_DiscardUnknown() {
	xxx_messageInfo_NewbieAnchorTaskBeginner_Reward.DiscardUnknown(m)
}

var xxx_messageInfo_NewbieAnchorTaskBeginner_Reward proto.InternalMessageInfo

func (m *NewbieAnchorTaskBeginner_Reward) GetConditionDays() uint32 {
	if m != nil {
		return m.ConditionDays
	}
	return 0
}

func (m *NewbieAnchorTaskBeginner_Reward) GetRewardText() string {
	if m != nil {
		return m.RewardText
	}
	return ""
}

func (m *NewbieAnchorTaskBeginner_Reward) GetRewardIcon() string {
	if m != nil {
		return m.RewardIcon
	}
	return ""
}

func (m *NewbieAnchorTaskBeginner_Reward) GetClaimStatus() NewbieRewardStatus {
	if m != nil {
		return m.ClaimStatus
	}
	return NewbieRewardStatus_NEWBIE_REWARD_STATUS_UNSPECIFIED
}

// 获取新手进阶任务
type NewbieAnchorTaskAdvanced struct {
	Period               uint32                                 `protobuf:"varint,1,opt,name=period,proto3" json:"period,omitempty"`
	PeriodStartTime      uint64                                 `protobuf:"varint,2,opt,name=period_start_time,json=periodStartTime,proto3" json:"period_start_time,omitempty"`
	PeriodEndTime        uint64                                 `protobuf:"varint,3,opt,name=period_end_time,json=periodEndTime,proto3" json:"period_end_time,omitempty"`
	TaskList             []*NewbieAnchorTaskItem                `protobuf:"bytes,4,rep,name=task_list,json=taskList,proto3" json:"task_list,omitempty"`
	ClaimStatus          NewbieRewardStatus                     `protobuf:"varint,5,opt,name=claim_status,json=claimStatus,proto3,enum=contract_http_logic.NewbieRewardStatus" json:"claim_status,omitempty"`
	RewardItems          []*NewbieAnchorTaskAdvanced_RewardItem `protobuf:"bytes,6,rep,name=reward_items,json=rewardItems,proto3" json:"reward_items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                               `json:"-"`
	XXX_unrecognized     []byte                                 `json:"-"`
	XXX_sizecache        int32                                  `json:"-"`
}

func (m *NewbieAnchorTaskAdvanced) Reset()         { *m = NewbieAnchorTaskAdvanced{} }
func (m *NewbieAnchorTaskAdvanced) String() string { return proto.CompactTextString(m) }
func (*NewbieAnchorTaskAdvanced) ProtoMessage()    {}
func (*NewbieAnchorTaskAdvanced) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{87}
}
func (m *NewbieAnchorTaskAdvanced) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewbieAnchorTaskAdvanced.Unmarshal(m, b)
}
func (m *NewbieAnchorTaskAdvanced) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewbieAnchorTaskAdvanced.Marshal(b, m, deterministic)
}
func (dst *NewbieAnchorTaskAdvanced) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewbieAnchorTaskAdvanced.Merge(dst, src)
}
func (m *NewbieAnchorTaskAdvanced) XXX_Size() int {
	return xxx_messageInfo_NewbieAnchorTaskAdvanced.Size(m)
}
func (m *NewbieAnchorTaskAdvanced) XXX_DiscardUnknown() {
	xxx_messageInfo_NewbieAnchorTaskAdvanced.DiscardUnknown(m)
}

var xxx_messageInfo_NewbieAnchorTaskAdvanced proto.InternalMessageInfo

func (m *NewbieAnchorTaskAdvanced) GetPeriod() uint32 {
	if m != nil {
		return m.Period
	}
	return 0
}

func (m *NewbieAnchorTaskAdvanced) GetPeriodStartTime() uint64 {
	if m != nil {
		return m.PeriodStartTime
	}
	return 0
}

func (m *NewbieAnchorTaskAdvanced) GetPeriodEndTime() uint64 {
	if m != nil {
		return m.PeriodEndTime
	}
	return 0
}

func (m *NewbieAnchorTaskAdvanced) GetTaskList() []*NewbieAnchorTaskItem {
	if m != nil {
		return m.TaskList
	}
	return nil
}

func (m *NewbieAnchorTaskAdvanced) GetClaimStatus() NewbieRewardStatus {
	if m != nil {
		return m.ClaimStatus
	}
	return NewbieRewardStatus_NEWBIE_REWARD_STATUS_UNSPECIFIED
}

func (m *NewbieAnchorTaskAdvanced) GetRewardItems() []*NewbieAnchorTaskAdvanced_RewardItem {
	if m != nil {
		return m.RewardItems
	}
	return nil
}

type NewbieAnchorTaskAdvanced_RewardItem struct {
	RewardName           string   `protobuf:"bytes,1,opt,name=reward_name,json=rewardName,proto3" json:"reward_name,omitempty"`
	RewardIcon           string   `protobuf:"bytes,2,opt,name=reward_icon,json=rewardIcon,proto3" json:"reward_icon,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewbieAnchorTaskAdvanced_RewardItem) Reset()         { *m = NewbieAnchorTaskAdvanced_RewardItem{} }
func (m *NewbieAnchorTaskAdvanced_RewardItem) String() string { return proto.CompactTextString(m) }
func (*NewbieAnchorTaskAdvanced_RewardItem) ProtoMessage()    {}
func (*NewbieAnchorTaskAdvanced_RewardItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{87, 0}
}
func (m *NewbieAnchorTaskAdvanced_RewardItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewbieAnchorTaskAdvanced_RewardItem.Unmarshal(m, b)
}
func (m *NewbieAnchorTaskAdvanced_RewardItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewbieAnchorTaskAdvanced_RewardItem.Marshal(b, m, deterministic)
}
func (dst *NewbieAnchorTaskAdvanced_RewardItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewbieAnchorTaskAdvanced_RewardItem.Merge(dst, src)
}
func (m *NewbieAnchorTaskAdvanced_RewardItem) XXX_Size() int {
	return xxx_messageInfo_NewbieAnchorTaskAdvanced_RewardItem.Size(m)
}
func (m *NewbieAnchorTaskAdvanced_RewardItem) XXX_DiscardUnknown() {
	xxx_messageInfo_NewbieAnchorTaskAdvanced_RewardItem.DiscardUnknown(m)
}

var xxx_messageInfo_NewbieAnchorTaskAdvanced_RewardItem proto.InternalMessageInfo

func (m *NewbieAnchorTaskAdvanced_RewardItem) GetRewardName() string {
	if m != nil {
		return m.RewardName
	}
	return ""
}

func (m *NewbieAnchorTaskAdvanced_RewardItem) GetRewardIcon() string {
	if m != nil {
		return m.RewardIcon
	}
	return ""
}

type GetNewbieAnchorTaskResp struct {
	BeginnerTask         *NewbieAnchorTaskBeginner   `protobuf:"bytes,1,opt,name=beginner_task,json=beginnerTask,proto3" json:"beginner_task,omitempty"`
	AdvancedTaskList     []*NewbieAnchorTaskAdvanced `protobuf:"bytes,2,rep,name=advanced_task_list,json=advancedTaskList,proto3" json:"advanced_task_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetNewbieAnchorTaskResp) Reset()         { *m = GetNewbieAnchorTaskResp{} }
func (m *GetNewbieAnchorTaskResp) String() string { return proto.CompactTextString(m) }
func (*GetNewbieAnchorTaskResp) ProtoMessage()    {}
func (*GetNewbieAnchorTaskResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{88}
}
func (m *GetNewbieAnchorTaskResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewbieAnchorTaskResp.Unmarshal(m, b)
}
func (m *GetNewbieAnchorTaskResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewbieAnchorTaskResp.Marshal(b, m, deterministic)
}
func (dst *GetNewbieAnchorTaskResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewbieAnchorTaskResp.Merge(dst, src)
}
func (m *GetNewbieAnchorTaskResp) XXX_Size() int {
	return xxx_messageInfo_GetNewbieAnchorTaskResp.Size(m)
}
func (m *GetNewbieAnchorTaskResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewbieAnchorTaskResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewbieAnchorTaskResp proto.InternalMessageInfo

func (m *GetNewbieAnchorTaskResp) GetBeginnerTask() *NewbieAnchorTaskBeginner {
	if m != nil {
		return m.BeginnerTask
	}
	return nil
}

func (m *GetNewbieAnchorTaskResp) GetAdvancedTaskList() []*NewbieAnchorTaskAdvanced {
	if m != nil {
		return m.AdvancedTaskList
	}
	return nil
}

// 领取新手入门任务奖励
type ClaimNewbieAnchorTaskRewardReq struct {
	IdentityType         uint32   `protobuf:"varint,1,opt,name=identity_type,json=identityType,proto3" json:"identity_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClaimNewbieAnchorTaskRewardReq) Reset()         { *m = ClaimNewbieAnchorTaskRewardReq{} }
func (m *ClaimNewbieAnchorTaskRewardReq) String() string { return proto.CompactTextString(m) }
func (*ClaimNewbieAnchorTaskRewardReq) ProtoMessage()    {}
func (*ClaimNewbieAnchorTaskRewardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{89}
}
func (m *ClaimNewbieAnchorTaskRewardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClaimNewbieAnchorTaskRewardReq.Unmarshal(m, b)
}
func (m *ClaimNewbieAnchorTaskRewardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClaimNewbieAnchorTaskRewardReq.Marshal(b, m, deterministic)
}
func (dst *ClaimNewbieAnchorTaskRewardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClaimNewbieAnchorTaskRewardReq.Merge(dst, src)
}
func (m *ClaimNewbieAnchorTaskRewardReq) XXX_Size() int {
	return xxx_messageInfo_ClaimNewbieAnchorTaskRewardReq.Size(m)
}
func (m *ClaimNewbieAnchorTaskRewardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ClaimNewbieAnchorTaskRewardReq.DiscardUnknown(m)
}

var xxx_messageInfo_ClaimNewbieAnchorTaskRewardReq proto.InternalMessageInfo

func (m *ClaimNewbieAnchorTaskRewardReq) GetIdentityType() uint32 {
	if m != nil {
		return m.IdentityType
	}
	return 0
}

// 领取新手进阶任务奖励
type ClaimNewbieAnchorTaskAdvancedRewardReq struct {
	IdentityType         uint32   `protobuf:"varint,1,opt,name=identity_type,json=identityType,proto3" json:"identity_type,omitempty"`
	Period               uint32   `protobuf:"varint,2,opt,name=period,proto3" json:"period,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClaimNewbieAnchorTaskAdvancedRewardReq) Reset() {
	*m = ClaimNewbieAnchorTaskAdvancedRewardReq{}
}
func (m *ClaimNewbieAnchorTaskAdvancedRewardReq) String() string { return proto.CompactTextString(m) }
func (*ClaimNewbieAnchorTaskAdvancedRewardReq) ProtoMessage()    {}
func (*ClaimNewbieAnchorTaskAdvancedRewardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{90}
}
func (m *ClaimNewbieAnchorTaskAdvancedRewardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClaimNewbieAnchorTaskAdvancedRewardReq.Unmarshal(m, b)
}
func (m *ClaimNewbieAnchorTaskAdvancedRewardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClaimNewbieAnchorTaskAdvancedRewardReq.Marshal(b, m, deterministic)
}
func (dst *ClaimNewbieAnchorTaskAdvancedRewardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClaimNewbieAnchorTaskAdvancedRewardReq.Merge(dst, src)
}
func (m *ClaimNewbieAnchorTaskAdvancedRewardReq) XXX_Size() int {
	return xxx_messageInfo_ClaimNewbieAnchorTaskAdvancedRewardReq.Size(m)
}
func (m *ClaimNewbieAnchorTaskAdvancedRewardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ClaimNewbieAnchorTaskAdvancedRewardReq.DiscardUnknown(m)
}

var xxx_messageInfo_ClaimNewbieAnchorTaskAdvancedRewardReq proto.InternalMessageInfo

func (m *ClaimNewbieAnchorTaskAdvancedRewardReq) GetIdentityType() uint32 {
	if m != nil {
		return m.IdentityType
	}
	return 0
}

func (m *ClaimNewbieAnchorTaskAdvancedRewardReq) GetPeriod() uint32 {
	if m != nil {
		return m.Period
	}
	return 0
}

// 领取新手任务奖励
type ClaimNewbieAnchorTaskRewardResp struct {
	Rewards              []*TaskRewardItem `protobuf:"bytes,2,rep,name=rewards,proto3" json:"rewards,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ClaimNewbieAnchorTaskRewardResp) Reset()         { *m = ClaimNewbieAnchorTaskRewardResp{} }
func (m *ClaimNewbieAnchorTaskRewardResp) String() string { return proto.CompactTextString(m) }
func (*ClaimNewbieAnchorTaskRewardResp) ProtoMessage()    {}
func (*ClaimNewbieAnchorTaskRewardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{91}
}
func (m *ClaimNewbieAnchorTaskRewardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClaimNewbieAnchorTaskRewardResp.Unmarshal(m, b)
}
func (m *ClaimNewbieAnchorTaskRewardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClaimNewbieAnchorTaskRewardResp.Marshal(b, m, deterministic)
}
func (dst *ClaimNewbieAnchorTaskRewardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClaimNewbieAnchorTaskRewardResp.Merge(dst, src)
}
func (m *ClaimNewbieAnchorTaskRewardResp) XXX_Size() int {
	return xxx_messageInfo_ClaimNewbieAnchorTaskRewardResp.Size(m)
}
func (m *ClaimNewbieAnchorTaskRewardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ClaimNewbieAnchorTaskRewardResp.DiscardUnknown(m)
}

var xxx_messageInfo_ClaimNewbieAnchorTaskRewardResp proto.InternalMessageInfo

func (m *ClaimNewbieAnchorTaskRewardResp) GetRewards() []*TaskRewardItem {
	if m != nil {
		return m.Rewards
	}
	return nil
}

// 单个奖励项
type TaskRewardItem struct {
	RewardName           string   `protobuf:"bytes,1,opt,name=reward_name,json=rewardName,proto3" json:"reward_name,omitempty"`
	RewardIcon           string   `protobuf:"bytes,2,opt,name=reward_icon,json=rewardIcon,proto3" json:"reward_icon,omitempty"`
	Count                uint32   `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TaskRewardItem) Reset()         { *m = TaskRewardItem{} }
func (m *TaskRewardItem) String() string { return proto.CompactTextString(m) }
func (*TaskRewardItem) ProtoMessage()    {}
func (*TaskRewardItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{92}
}
func (m *TaskRewardItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TaskRewardItem.Unmarshal(m, b)
}
func (m *TaskRewardItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TaskRewardItem.Marshal(b, m, deterministic)
}
func (dst *TaskRewardItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaskRewardItem.Merge(dst, src)
}
func (m *TaskRewardItem) XXX_Size() int {
	return xxx_messageInfo_TaskRewardItem.Size(m)
}
func (m *TaskRewardItem) XXX_DiscardUnknown() {
	xxx_messageInfo_TaskRewardItem.DiscardUnknown(m)
}

var xxx_messageInfo_TaskRewardItem proto.InternalMessageInfo

func (m *TaskRewardItem) GetRewardName() string {
	if m != nil {
		return m.RewardName
	}
	return ""
}

func (m *TaskRewardItem) GetRewardIcon() string {
	if m != nil {
		return m.RewardIcon
	}
	return ""
}

func (m *TaskRewardItem) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

// 奖励条件
type TaskRewardCondition struct {
	ConditionDesc        string            `protobuf:"bytes,1,opt,name=condition_desc,json=conditionDesc,proto3" json:"condition_desc,omitempty"`
	Rewards              []*TaskRewardItem `protobuf:"bytes,2,rep,name=rewards,proto3" json:"rewards,omitempty"`
	IsClaimed            bool              `protobuf:"varint,3,opt,name=is_claimed,json=isClaimed,proto3" json:"is_claimed,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *TaskRewardCondition) Reset()         { *m = TaskRewardCondition{} }
func (m *TaskRewardCondition) String() string { return proto.CompactTextString(m) }
func (*TaskRewardCondition) ProtoMessage()    {}
func (*TaskRewardCondition) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{93}
}
func (m *TaskRewardCondition) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TaskRewardCondition.Unmarshal(m, b)
}
func (m *TaskRewardCondition) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TaskRewardCondition.Marshal(b, m, deterministic)
}
func (dst *TaskRewardCondition) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaskRewardCondition.Merge(dst, src)
}
func (m *TaskRewardCondition) XXX_Size() int {
	return xxx_messageInfo_TaskRewardCondition.Size(m)
}
func (m *TaskRewardCondition) XXX_DiscardUnknown() {
	xxx_messageInfo_TaskRewardCondition.DiscardUnknown(m)
}

var xxx_messageInfo_TaskRewardCondition proto.InternalMessageInfo

func (m *TaskRewardCondition) GetConditionDesc() string {
	if m != nil {
		return m.ConditionDesc
	}
	return ""
}

func (m *TaskRewardCondition) GetRewards() []*TaskRewardItem {
	if m != nil {
		return m.Rewards
	}
	return nil
}

func (m *TaskRewardCondition) GetIsClaimed() bool {
	if m != nil {
		return m.IsClaimed
	}
	return false
}

// 获取新手任务奖励列表
type GetNewbieAnchorTaskRewardListReq struct {
	IdentityType         uint32               `protobuf:"varint,1,opt,name=identity_type,json=identityType,proto3" json:"identity_type,omitempty"`
	TaskType             NewbieAnchorTaskType `protobuf:"varint,2,opt,name=task_type,json=taskType,proto3,enum=contract_http_logic.NewbieAnchorTaskType" json:"task_type,omitempty"`
	Period               uint32               `protobuf:"varint,3,opt,name=period,proto3" json:"period,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetNewbieAnchorTaskRewardListReq) Reset()         { *m = GetNewbieAnchorTaskRewardListReq{} }
func (m *GetNewbieAnchorTaskRewardListReq) String() string { return proto.CompactTextString(m) }
func (*GetNewbieAnchorTaskRewardListReq) ProtoMessage()    {}
func (*GetNewbieAnchorTaskRewardListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{94}
}
func (m *GetNewbieAnchorTaskRewardListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewbieAnchorTaskRewardListReq.Unmarshal(m, b)
}
func (m *GetNewbieAnchorTaskRewardListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewbieAnchorTaskRewardListReq.Marshal(b, m, deterministic)
}
func (dst *GetNewbieAnchorTaskRewardListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewbieAnchorTaskRewardListReq.Merge(dst, src)
}
func (m *GetNewbieAnchorTaskRewardListReq) XXX_Size() int {
	return xxx_messageInfo_GetNewbieAnchorTaskRewardListReq.Size(m)
}
func (m *GetNewbieAnchorTaskRewardListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewbieAnchorTaskRewardListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewbieAnchorTaskRewardListReq proto.InternalMessageInfo

func (m *GetNewbieAnchorTaskRewardListReq) GetIdentityType() uint32 {
	if m != nil {
		return m.IdentityType
	}
	return 0
}

func (m *GetNewbieAnchorTaskRewardListReq) GetTaskType() NewbieAnchorTaskType {
	if m != nil {
		return m.TaskType
	}
	return NewbieAnchorTaskType_NEWBIE_ANCHOR_TASK_TYPE_UNSPECIFIED
}

func (m *GetNewbieAnchorTaskRewardListReq) GetPeriod() uint32 {
	if m != nil {
		return m.Period
	}
	return 0
}

type GetNewbieAnchorTaskRewardListResp struct {
	RewardConditionList  []*TaskRewardCondition `protobuf:"bytes,1,rep,name=reward_condition_list,json=rewardConditionList,proto3" json:"reward_condition_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetNewbieAnchorTaskRewardListResp) Reset()         { *m = GetNewbieAnchorTaskRewardListResp{} }
func (m *GetNewbieAnchorTaskRewardListResp) String() string { return proto.CompactTextString(m) }
func (*GetNewbieAnchorTaskRewardListResp) ProtoMessage()    {}
func (*GetNewbieAnchorTaskRewardListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_contract_http_logic_b7f585e6aec5a3a2, []int{95}
}
func (m *GetNewbieAnchorTaskRewardListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewbieAnchorTaskRewardListResp.Unmarshal(m, b)
}
func (m *GetNewbieAnchorTaskRewardListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewbieAnchorTaskRewardListResp.Marshal(b, m, deterministic)
}
func (dst *GetNewbieAnchorTaskRewardListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewbieAnchorTaskRewardListResp.Merge(dst, src)
}
func (m *GetNewbieAnchorTaskRewardListResp) XXX_Size() int {
	return xxx_messageInfo_GetNewbieAnchorTaskRewardListResp.Size(m)
}
func (m *GetNewbieAnchorTaskRewardListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewbieAnchorTaskRewardListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewbieAnchorTaskRewardListResp proto.InternalMessageInfo

func (m *GetNewbieAnchorTaskRewardListResp) GetRewardConditionList() []*TaskRewardCondition {
	if m != nil {
		return m.RewardConditionList
	}
	return nil
}

func init() {
	proto.RegisterType((*GetHallTaskResp)(nil), "contract_http_logic.GetHallTaskResp")
	proto.RegisterType((*HallTaskDetial)(nil), "contract_http_logic.HallTaskDetial")
	proto.RegisterType((*GetHallTaskHistoryResp)(nil), "contract_http_logic.GetHallTaskHistoryResp")
	proto.RegisterType((*HallTaskHistoryDetial)(nil), "contract_http_logic.HallTaskHistoryDetial")
	proto.RegisterType((*GetESportCoachInfoReq)(nil), "contract_http_logic.GetESportCoachInfoReq")
	proto.RegisterType((*UserSkillItem)(nil), "contract_http_logic.UserSkillItem")
	proto.RegisterType((*ESportMonthData)(nil), "contract_http_logic.ESportMonthData")
	proto.RegisterType((*GetESportCoachInfoResp)(nil), "contract_http_logic.GetESportCoachInfoResp")
	proto.RegisterType((*SectionInfo)(nil), "contract_http_logic.SectionInfo")
	proto.RegisterType((*UserSkillInfo)(nil), "contract_http_logic.UserSkillInfo")
	proto.RegisterType((*GetESportUserSkillInfoReq)(nil), "contract_http_logic.GetESportUserSkillInfoReq")
	proto.RegisterType((*GetESportUserSkillInfoResp)(nil), "contract_http_logic.GetESportUserSkillInfoResp")
	proto.RegisterType((*SignIdentityPreCheckReq)(nil), "contract_http_logic.SignIdentityPreCheckReq")
	proto.RegisterType((*SignIdentityPreCheckResp)(nil), "contract_http_logic.SignIdentityPreCheckResp")
	proto.RegisterType((*ApplySignRiskCheckReq)(nil), "contract_http_logic.ApplySignRiskCheckReq")
	proto.RegisterType((*ApplySignRiskCheckResp)(nil), "contract_http_logic.ApplySignRiskCheckResp")
	proto.RegisterType((*ApplySignReq)(nil), "contract_http_logic.ApplySignReq")
	proto.RegisterType((*ApplySignResp)(nil), "contract_http_logic.ApplySignResp")
	proto.RegisterType((*GuildBusiness)(nil), "contract_http_logic.GuildBusiness")
	proto.RegisterType((*TopGuildInfo)(nil), "contract_http_logic.TopGuildInfo")
	proto.RegisterType((*GetRecommendTopGuildListReq)(nil), "contract_http_logic.GetRecommendTopGuildListReq")
	proto.RegisterType((*GetRecommendTopGuildListResp)(nil), "contract_http_logic.GetRecommendTopGuildListResp")
	proto.RegisterType((*GetCancelContractTypesReq)(nil), "contract_http_logic.GetCancelContractTypesReq")
	proto.RegisterType((*CanCancelContract)(nil), "contract_http_logic.CanCancelContract")
	proto.RegisterType((*GetCancelContractTypesResp)(nil), "contract_http_logic.GetCancelContractTypesResp")
	proto.RegisterType((*ApplyCancelContractReq)(nil), "contract_http_logic.ApplyCancelContractReq")
	proto.RegisterType((*ProofContent)(nil), "contract_http_logic.ProofContent")
	proto.RegisterType((*ProofShowContent)(nil), "contract_http_logic.ProofShowContent")
	proto.RegisterType((*ApplyCancelContractResp)(nil), "contract_http_logic.ApplyCancelContractResp")
	proto.RegisterType((*LockPayCancelAmountReq)(nil), "contract_http_logic.LockPayCancelAmountReq")
	proto.RegisterType((*LockPayCancelAmountResp)(nil), "contract_http_logic.LockPayCancelAmountResp")
	proto.RegisterType((*CheckCanCancelContractReq)(nil), "contract_http_logic.CheckCanCancelContractReq")
	proto.RegisterType((*CheckCanCancelContractResp)(nil), "contract_http_logic.CheckCanCancelContractResp")
	proto.RegisterType((*GetApplyCancelContractListReq)(nil), "contract_http_logic.GetApplyCancelContractListReq")
	proto.RegisterType((*ApplyCancelContractItem)(nil), "contract_http_logic.ApplyCancelContractItem")
	proto.RegisterType((*GetApplyCancelContractListResp)(nil), "contract_http_logic.GetApplyCancelContractListResp")
	proto.RegisterType((*GetNegotiateReasonTypeReq)(nil), "contract_http_logic.GetNegotiateReasonTypeReq")
	proto.RegisterType((*GetNegotiateReasonTypeResp)(nil), "contract_http_logic.GetNegotiateReasonTypeResp")
	proto.RegisterType((*AcceptApplyCancelContractReq)(nil), "contract_http_logic.AcceptApplyCancelContractReq")
	proto.RegisterType((*AcceptApplyCancelContractResp)(nil), "contract_http_logic.AcceptApplyCancelContractResp")
	proto.RegisterType((*RejectApplyCancelContractReq)(nil), "contract_http_logic.RejectApplyCancelContractReq")
	proto.RegisterType((*RejectApplyCancelContractResp)(nil), "contract_http_logic.RejectApplyCancelContractResp")
	proto.RegisterType((*ContractWorker)(nil), "contract_http_logic.ContractWorker")
	proto.RegisterType((*ContractPrivilege)(nil), "contract_http_logic.ContractPrivilege")
	proto.RegisterType((*ContractPrivilegeGroup)(nil), "contract_http_logic.ContractPrivilegeGroup")
	proto.RegisterType((*GetContractPrivilegeListReq)(nil), "contract_http_logic.GetContractPrivilegeListReq")
	proto.RegisterType((*GetContractPrivilegeListResp)(nil), "contract_http_logic.GetContractPrivilegeListResp")
	proto.RegisterType((*SignLiveSubmit)(nil), "contract_http_logic.SignLiveSubmit")
	proto.RegisterType((*ApplySignContractReq)(nil), "contract_http_logic.ApplySignContractReq")
	proto.RegisterType((*ApplySignContractResp)(nil), "contract_http_logic.ApplySignContractResp")
	proto.RegisterType((*PromoteInfo)(nil), "contract_http_logic.PromoteInfo")
	proto.RegisterType((*UserContractInfo)(nil), "contract_http_logic.UserContractInfo")
	proto.RegisterType((*GetUserContractInfoReq)(nil), "contract_http_logic.GetUserContractInfoReq")
	proto.RegisterType((*GetUserContractInfoResp)(nil), "contract_http_logic.GetUserContractInfoResp")
	proto.RegisterType((*ProcPromoteInviteReq)(nil), "contract_http_logic.ProcPromoteInviteReq")
	proto.RegisterType((*ProcPromoteInviteResp)(nil), "contract_http_logic.ProcPromoteInviteResp")
	proto.RegisterType((*GetGuildMgrListReq)(nil), "contract_http_logic.GetGuildMgrListReq")
	proto.RegisterType((*GetGuildMgrListResp)(nil), "contract_http_logic.GetGuildMgrListResp")
	proto.RegisterType((*CensorVideoReq)(nil), "contract_http_logic.CensorVideoReq")
	proto.RegisterType((*CensorVideoResp)(nil), "contract_http_logic.CensorVideoResp")
	proto.RegisterType((*CensorResult)(nil), "contract_http_logic.CensorResult")
	proto.RegisterType((*GetNeedConfirmWorkerTypeReq)(nil), "contract_http_logic.GetNeedConfirmWorkerTypeReq")
	proto.RegisterType((*GetNeedConfirmWorkerTypeResp)(nil), "contract_http_logic.GetNeedConfirmWorkerTypeResp")
	proto.RegisterType((*ModifyWorkerTypeReq)(nil), "contract_http_logic.ModifyWorkerTypeReq")
	proto.RegisterType((*ModifyWorkerTypeResp)(nil), "contract_http_logic.ModifyWorkerTypeResp")
	proto.RegisterType((*AnchorLevelNewTask)(nil), "contract_http_logic.AnchorLevelNewTask")
	proto.RegisterType((*GetContractChangeInfoReq)(nil), "contract_http_logic.GetContractChangeInfoReq")
	proto.RegisterType((*GetContractChangeInfoResp)(nil), "contract_http_logic.GetContractChangeInfoResp")
	proto.RegisterType((*ContractPrivilegeGroupChange)(nil), "contract_http_logic.ContractPrivilegeGroupChange")
	proto.RegisterType((*CanCancelContractChange)(nil), "contract_http_logic.CanCancelContractChange")
	proto.RegisterType((*HandleContractChangeReq)(nil), "contract_http_logic.HandleContractChangeReq")
	proto.RegisterType((*HandleContractChangeResp)(nil), "contract_http_logic.HandleContractChangeResp")
	proto.RegisterType((*GetRejectReasonReq)(nil), "contract_http_logic.GetRejectReasonReq")
	proto.RegisterType((*GetRejectReasonResp)(nil), "contract_http_logic.GetRejectReasonResp")
	proto.RegisterType((*GetNewbieAnchorInfoReq)(nil), "contract_http_logic.GetNewbieAnchorInfoReq")
	proto.RegisterType((*IdentityObtainDay)(nil), "contract_http_logic.IdentityObtainDay")
	proto.RegisterType((*GetNewbieAnchorInfoResp)(nil), "contract_http_logic.GetNewbieAnchorInfoResp")
	proto.RegisterType((*KnowledgeTask)(nil), "contract_http_logic.KnowledgeTask")
	proto.RegisterType((*NewbieGuideContent)(nil), "contract_http_logic.NewbieGuideContent")
	proto.RegisterType((*NewbieGuideSubTheme)(nil), "contract_http_logic.NewbieGuideSubTheme")
	proto.RegisterType((*NewbieGuideTheme)(nil), "contract_http_logic.NewbieGuideTheme")
	proto.RegisterType((*GetNewbieAnchorGuideInfoReq)(nil), "contract_http_logic.GetNewbieAnchorGuideInfoReq")
	proto.RegisterType((*GetNewbieAnchorGuideInfoResp)(nil), "contract_http_logic.GetNewbieAnchorGuideInfoResp")
	proto.RegisterType((*GetNewbieAnchorTaskReq)(nil), "contract_http_logic.GetNewbieAnchorTaskReq")
	proto.RegisterType((*NewbieAnchorTaskItem)(nil), "contract_http_logic.NewbieAnchorTaskItem")
	proto.RegisterType((*NewbieAnchorTaskDay)(nil), "contract_http_logic.NewbieAnchorTaskDay")
	proto.RegisterType((*NewbieAnchorTaskBeginner)(nil), "contract_http_logic.NewbieAnchorTaskBeginner")
	proto.RegisterType((*NewbieAnchorTaskBeginner_Reward)(nil), "contract_http_logic.NewbieAnchorTaskBeginner.Reward")
	proto.RegisterType((*NewbieAnchorTaskAdvanced)(nil), "contract_http_logic.NewbieAnchorTaskAdvanced")
	proto.RegisterType((*NewbieAnchorTaskAdvanced_RewardItem)(nil), "contract_http_logic.NewbieAnchorTaskAdvanced.RewardItem")
	proto.RegisterType((*GetNewbieAnchorTaskResp)(nil), "contract_http_logic.GetNewbieAnchorTaskResp")
	proto.RegisterType((*ClaimNewbieAnchorTaskRewardReq)(nil), "contract_http_logic.ClaimNewbieAnchorTaskRewardReq")
	proto.RegisterType((*ClaimNewbieAnchorTaskAdvancedRewardReq)(nil), "contract_http_logic.ClaimNewbieAnchorTaskAdvancedRewardReq")
	proto.RegisterType((*ClaimNewbieAnchorTaskRewardResp)(nil), "contract_http_logic.ClaimNewbieAnchorTaskRewardResp")
	proto.RegisterType((*TaskRewardItem)(nil), "contract_http_logic.TaskRewardItem")
	proto.RegisterType((*TaskRewardCondition)(nil), "contract_http_logic.TaskRewardCondition")
	proto.RegisterType((*GetNewbieAnchorTaskRewardListReq)(nil), "contract_http_logic.GetNewbieAnchorTaskRewardListReq")
	proto.RegisterType((*GetNewbieAnchorTaskRewardListResp)(nil), "contract_http_logic.GetNewbieAnchorTaskRewardListResp")
	proto.RegisterEnum("contract_http_logic.KnowledgeTaskStatus", KnowledgeTaskStatus_name, KnowledgeTaskStatus_value)
	proto.RegisterEnum("contract_http_logic.NewbieAnchorTaskType", NewbieAnchorTaskType_name, NewbieAnchorTaskType_value)
	proto.RegisterEnum("contract_http_logic.NewbieRewardStatus", NewbieRewardStatus_name, NewbieRewardStatus_value)
	proto.RegisterEnum("contract_http_logic.ProofContent_ProofType", ProofContent_ProofType_name, ProofContent_ProofType_value)
	proto.RegisterEnum("contract_http_logic.ProofShowContent_ProofType", ProofShowContent_ProofType_name, ProofShowContent_ProofType_value)
	proto.RegisterEnum("contract_http_logic.CheckCanCancelContractResp_CancelStage", CheckCanCancelContractResp_CancelStage_name, CheckCanCancelContractResp_CancelStage_value)
	proto.RegisterEnum("contract_http_logic.ProcPromoteInviteReq_ProcResType", ProcPromoteInviteReq_ProcResType_name, ProcPromoteInviteReq_ProcResType_value)
	proto.RegisterEnum("contract_http_logic.NewbieAnchorTaskDay_Status", NewbieAnchorTaskDay_Status_name, NewbieAnchorTaskDay_Status_value)
}

func init() {
	proto.RegisterFile("tt/quicksilver/contract-http-logic/contract-http-logic.proto", fileDescriptor_contract_http_logic_b7f585e6aec5a3a2)
}

var fileDescriptor_contract_http_logic_b7f585e6aec5a3a2 = []byte{
	// 4718 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x3b, 0x4d, 0x73, 0x1b, 0x47,
	0x76, 0x1e, 0x90, 0x22, 0x81, 0x47, 0x00, 0x84, 0x86, 0x94, 0x08, 0xc9, 0xd2, 0x8a, 0x1a, 0xc9,
	0x32, 0x23, 0xaf, 0xa5, 0xb5, 0x1c, 0x6f, 0x92, 0xda, 0x4f, 0x0a, 0xa4, 0x68, 0xda, 0x12, 0xc4,
	0x1d, 0x52, 0x96, 0xec, 0xdd, 0xca, 0xec, 0x70, 0xa6, 0x09, 0xb6, 0x31, 0x98, 0x19, 0x4f, 0x37,
	0x48, 0xb1, 0xf6, 0x90, 0x6c, 0x72, 0x49, 0x55, 0x0e, 0x39, 0xe5, 0xb2, 0xe5, 0xaa, 0xbd, 0xa4,
	0x92, 0xda, 0x5c, 0xb2, 0xb7, 0x9c, 0xf2, 0x75, 0xda, 0xaa, 0x1c, 0x52, 0x39, 0xa6, 0x2a, 0x95,
	0xe4, 0x07, 0xe4, 0x9c, 0x7b, 0xea, 0xbd, 0xee, 0x19, 0x0c, 0x80, 0x01, 0x40, 0xc9, 0x4e, 0x6e,
	0xd3, 0xef, 0xbd, 0x7e, 0xfd, 0xfa, 0xf5, 0xfb, 0xea, 0x8f, 0x81, 0xef, 0x4a, 0x79, 0xff, 0x8b,
	0x3e, 0xf7, 0xba, 0x82, 0x07, 0x27, 0x2c, 0xb9, 0xef, 0x45, 0xa1, 0x4c, 0x5c, 0x4f, 0xbe, 0x7b,
	0x2c, 0x65, 0xfc, 0x6e, 0x10, 0x75, 0xb8, 0x57, 0x04, 0xbb, 0x17, 0x27, 0x91, 0x8c, 0xcc, 0x95,
	0x14, 0xe5, 0x20, 0xca, 0x21, 0x94, 0xf5, 0x1f, 0x06, 0x2c, 0xef, 0x30, 0xf9, 0xa1, 0x1b, 0x04,
	0x07, 0xae, 0xe8, 0xda, 0x4c, 0xc4, 0xe6, 0x0e, 0xd4, 0x7c, 0xf7, 0xcc, 0x91, 0xae, 0xe8, 0x3a,
	0x01, 0x17, 0xb2, 0x69, 0xac, 0xcf, 0x6d, 0x2c, 0x3d, 0xb8, 0x75, 0xaf, 0x80, 0xc1, 0xbd, 0xb4,
	0xe7, 0x16, 0x93, 0xdc, 0x0d, 0xec, 0x25, 0xdf, 0x3d, 0xc3, 0xe6, 0x63, 0x2e, 0xa4, 0xb9, 0x0b,
	0xf5, 0x53, 0xc6, 0xba, 0x39, 0x4e, 0xa5, 0xf3, 0x73, 0xaa, 0x62, 0xd7, 0x8c, 0xd5, 0x75, 0x80,
	0x84, 0x9d, 0xba, 0x89, 0xef, 0xf4, 0x44, 0xa7, 0x39, 0xb7, 0x6e, 0x6c, 0x54, 0xec, 0x8a, 0x82,
	0x3c, 0x11, 0x1d, 0x73, 0x0d, 0x16, 0xb9, 0x70, 0xc4, 0x71, 0x74, 0xda, 0x9c, 0x5f, 0x37, 0x36,
	0xca, 0xf6, 0x02, 0x17, 0xfb, 0xc7, 0xd1, 0xa9, 0xf5, 0x6b, 0x03, 0xea, 0xc3, 0x8c, 0xcd, 0x37,
	0xa1, 0x42, 0x02, 0x85, 0x6e, 0x8f, 0x35, 0x0d, 0xe2, 0x54, 0x46, 0x40, 0xdb, 0xed, 0x31, 0xf3,
	0x16, 0xd4, 0x08, 0x19, 0x27, 0x51, 0x27, 0x61, 0x42, 0x34, 0x4b, 0x44, 0x50, 0x45, 0xe0, 0x9e,
	0x86, 0x99, 0x26, 0xcc, 0x27, 0xae, 0x64, 0x24, 0x46, 0xc9, 0xa6, 0x6f, 0xf3, 0x0a, 0x10, 0x13,
	0xe7, 0xc4, 0x0d, 0x48, 0x84, 0x9a, 0xbd, 0x88, 0xed, 0x4f, 0xdc, 0x00, 0x51, 0x27, 0x6e, 0xa0,
	0x14, 0x70, 0x61, 0x7d, 0x6e, 0xa3, 0x62, 0x2f, 0x9e, 0xb8, 0x01, 0x4d, 0xcb, 0x84, 0x79, 0x1f,
	0x39, 0x2d, 0xd0, 0x28, 0xf4, 0x6d, 0xbd, 0x80, 0xcb, 0xb9, 0x15, 0xf9, 0x90, 0x0b, 0x19, 0x25,
	0x67, 0xb4, 0x30, 0xdf, 0x87, 0xf9, 0xdc, 0x7a, 0xdc, 0x9d, 0xaa, 0x45, 0xdd, 0x4f, 0x2b, 0x93,
	0xfa, 0x59, 0xff, 0x60, 0xc0, 0xa5, 0x42, 0x7c, 0x26, 0x87, 0x31, 0x90, 0x63, 0xdc, 0x0c, 0x4a,
	0x5f, 0x9b, 0x19, 0xcc, 0xbd, 0xa6, 0x19, 0x58, 0x4f, 0xe1, 0xd2, 0x0e, 0x93, 0xdb, 0xfb, 0x71,
	0x94, 0xc8, 0x56, 0xe4, 0x7a, 0xc7, 0xbb, 0xe1, 0x51, 0x64, 0xb3, 0x2f, 0x50, 0xc7, 0x9d, 0x3e,
	0x0f, 0x7c, 0x87, 0xfb, 0x34, 0x89, 0x9a, 0xbd, 0x48, 0xed, 0x5d, 0x1f, 0xd7, 0xdb, 0x43, 0x52,
	0xa7, 0xcf, 0x7d, 0x5a, 0xce, 0x9a, 0x5d, 0x26, 0xc0, 0x33, 0xee, 0x5b, 0x5f, 0x1a, 0x50, 0x7b,
	0x26, 0x58, 0xb2, 0xdf, 0xe5, 0x41, 0xb0, 0x2b, 0x59, 0x0f, 0x4d, 0xa9, 0xe3, 0xf6, 0xd8, 0x80,
	0xd1, 0x02, 0x36, 0x15, 0x1f, 0x42, 0x90, 0xdd, 0x28, 0xb3, 0x28, 0x23, 0x80, 0xec, 0x26, 0x45,
	0x72, 0x2f, 0x0a, 0xb5, 0x79, 0x12, 0x72, 0xd7, 0x8b, 0xc2, 0x0c, 0x29, 0xcf, 0x62, 0xa6, 0x8d,
	0x83, 0x90, 0x07, 0x67, 0x31, 0xf5, 0x44, 0xb8, 0x23, 0xa2, 0x04, 0xcd, 0x83, 0x90, 0x08, 0xd8,
	0x8f, 0x12, 0x69, 0xfd, 0x4f, 0x09, 0x96, 0xd5, 0x6c, 0x9f, 0x44, 0xa1, 0x3c, 0xde, 0x72, 0xa5,
	0x6b, 0xde, 0x84, 0x6a, 0x94, 0xf8, 0x2c, 0x71, 0xdc, 0x5e, 0xd4, 0x0f, 0x65, 0x73, 0x69, 0xdd,
	0xd8, 0x98, 0xb7, 0x97, 0x08, 0xb6, 0x49, 0x20, 0x24, 0x91, 0x91, 0x74, 0x03, 0x87, 0x80, 0xa2,
	0x59, 0x55, 0x24, 0x04, 0x7b, 0x4a, 0x20, 0xf3, 0x0e, 0x2c, 0x4b, 0xb7, 0xcb, 0x14, 0x85, 0xe3,
	0xbb, 0x67, 0xa2, 0x59, 0xa3, 0xc1, 0x6b, 0x08, 0x26, 0xa2, 0x2d, 0xf7, 0x4c, 0x98, 0x37, 0x60,
	0xc9, 0xf5, 0x24, 0x3f, 0x61, 0x8a, 0xa6, 0x4e, 0x34, 0xa0, 0x40, 0x44, 0x70, 0x0b, 0x6a, 0x31,
	0x8b, 0xe2, 0x80, 0x39, 0x82, 0x25, 0x27, 0xcc, 0x6f, 0x2e, 0x13, 0x49, 0x55, 0x01, 0xf7, 0x09,
	0x86, 0x44, 0x21, 0x3b, 0x75, 0xbc, 0xbe, 0x90, 0x51, 0x0f, 0x25, 0x6a, 0x28, 0xa2, 0x90, 0x9d,
	0xb6, 0x52, 0x98, 0xb9, 0x0e, 0x4b, 0x09, 0x8b, 0xfb, 0x89, 0x77, 0xec, 0x0a, 0x26, 0x9a, 0x17,
	0x89, 0x24, 0x0f, 0xc2, 0x79, 0x9d, 0xf0, 0x28, 0x70, 0x25, 0x8f, 0x42, 0xe1, 0xb8, 0x4d, 0x53,
	0x91, 0x0c, 0x60, 0x9b, 0x23, 0x24, 0x87, 0xcd, 0x95, 0x51, 0x92, 0x87, 0x23, 0x24, 0x5e, 0x73,
	0x75, 0x94, 0xa4, 0x65, 0xfd, 0xe3, 0x1c, 0x39, 0xe1, 0x98, 0xa1, 0x89, 0x78, 0x9a, 0xa5, 0xdd,
	0x86, 0xba, 0x42, 0x89, 0xe3, 0x28, 0x91, 0x4e, 0x66, 0x6e, 0x55, 0x82, 0xee, 0x23, 0x70, 0xd7,
	0xc7, 0x50, 0xa6, 0xa8, 0xc8, 0x90, 0x74, 0x28, 0x23, 0x48, 0x6a, 0x49, 0x03, 0x73, 0x9d, 0x1f,
	0x36, 0x57, 0xec, 0xab, 0x90, 0x52, 0x72, 0x9f, 0xac, 0xa5, 0x62, 0x2b, 0xf2, 0x03, 0x99, 0x47,
	0x87, 0xdc, 0xeb, 0xea, 0xa0, 0xa2, 0xd0, 0x6d, 0xee, 0x75, 0xcd, 0x7b, 0x30, 0xc8, 0x01, 0x87,
	0xac, 0xc3, 0x43, 0x47, 0xf2, 0x1e, 0x6b, 0x2e, 0xae, 0x1b, 0x1b, 0x73, 0xf6, 0xc5, 0x14, 0xf5,
	0x10, 0x31, 0x07, 0xbc, 0xc7, 0xcc, 0xbb, 0x90, 0x01, 0x1d, 0x16, 0xfa, 0x8a, 0xba, 0x4c, 0xd4,
	0xcb, 0x29, 0x62, 0x3b, 0xf4, 0x89, 0x76, 0x13, 0x40, 0xa0, 0x0f, 0x29, 0x07, 0xaf, 0x90, 0x83,
	0x5b, 0x85, 0x0e, 0x3e, 0xe4, 0x6e, 0x76, 0x85, 0x7a, 0x51, 0x9c, 0x78, 0x0c, 0xcb, 0xf2, 0x98,
	0x0b, 0xa7, 0x87, 0xa6, 0xee, 0xf8, 0xae, 0x74, 0x9b, 0x97, 0xd6, 0x8d, 0x8d, 0xa5, 0x07, 0xb7,
	0x0b, 0xf9, 0x8c, 0xf8, 0x85, 0x5d, 0xc3, 0xce, 0x59, 0xd3, 0x0a, 0x60, 0x69, 0x9f, 0x79, 0xb8,
	0x9e, 0xb8, 0x74, 0xb8, 0xe8, 0x42, 0x35, 0xf3, 0x81, 0x7f, 0x49, 0xc3, 0x52, 0xcd, 0x73, 0xc9,
	0x7a, 0x83, 0x60, 0x57, 0xb1, 0xcb, 0x08, 0x48, 0x13, 0x50, 0xda, 0x9f, 0xfb, 0xb4, 0x6a, 0x35,
	0xbb, 0xa2, 0x21, 0xbb, 0xbe, 0xf5, 0x9b, 0x52, 0x3e, 0x8e, 0xe0, 0x80, 0xaf, 0x17, 0x47, 0xde,
	0x82, 0xba, 0x52, 0x23, 0x3b, 0xe1, 0x3e, 0x0b, 0xbd, 0xd4, 0x40, 0x6a, 0x04, 0xdd, 0xd6, 0x40,
	0x92, 0x86, 0xc8, 0x7c, 0x26, 0x3c, 0xb2, 0x92, 0x8a, 0xd6, 0xe4, 0x16, 0x13, 0x9e, 0xb9, 0x0a,
	0x17, 0xdc, 0xbe, 0xcf, 0x23, 0x6d, 0x21, 0xaa, 0x81, 0xbc, 0xe9, 0xc3, 0xf1, 0xfb, 0x09, 0x19,
	0x3a, 0x59, 0x48, 0xcd, 0xae, 0x11, 0x74, 0x4b, 0x03, 0xcd, 0xd6, 0x40, 0x53, 0xa4, 0x89, 0x45,
	0x5a, 0xcb, 0xf5, 0xc2, 0x35, 0xc8, 0x69, 0x38, 0xd3, 0x25, 0xa9, 0x0b, 0xa3, 0x1a, 0x7b, 0x29,
	0x95, 0x7c, 0x65, 0x9d, 0x64, 0xd9, 0x4b, 0x49, 0xe2, 0xa5, 0x1a, 0x48, 0xdc, 0xb0, 0xdb, 0xac,
	0x0c, 0xe2, 0xa1, 0xed, 0x86, 0x5d, 0xeb, 0x47, 0x70, 0x25, 0xf3, 0xbc, 0x21, 0x8d, 0x62, 0x98,
	0x1f, 0x72, 0x0e, 0x63, 0xc4, 0x39, 0x72, 0x1a, 0x2f, 0xe5, 0x35, 0x6e, 0xfd, 0xb9, 0x01, 0x57,
	0x27, 0xf1, 0x14, 0xf1, 0xeb, 0x31, 0x1d, 0x18, 0x3c, 0x0f, 0x8f, 0x22, 0x5a, 0xa5, 0xd9, 0x06,
	0x8f, 0x23, 0xaa, 0x65, 0xc2, 0x4f, 0xeb, 0x53, 0x58, 0xdb, 0xe7, 0x9d, 0x70, 0xd7, 0x67, 0xa1,
	0xe4, 0xf2, 0x6c, 0x2f, 0x61, 0xad, 0x63, 0xe6, 0x75, 0x67, 0xe4, 0xb3, 0x5b, 0x50, 0xe3, 0xba,
	0x87, 0xca, 0x28, 0x3a, 0xc8, 0xa4, 0x40, 0xcc, 0x2a, 0xd6, 0x55, 0x68, 0x16, 0xb3, 0x16, 0xb1,
	0xf5, 0xdf, 0x06, 0x5c, 0xda, 0x8c, 0xe3, 0xe0, 0x0c, 0x29, 0x6c, 0x2e, 0xba, 0xd9, 0xa8, 0x63,
	0xac, 0x8d, 0x71, 0xd6, 0xa8, 0x2e, 0x9f, 0x9d, 0x70, 0x2f, 0xd3, 0x49, 0xc5, 0x2e, 0x2b, 0xc0,
	0xae, 0x8f, 0xe9, 0xc2, 0x0b, 0x38, 0x0b, 0xa5, 0xea, 0xaf, 0xfc, 0x04, 0x14, 0x88, 0x7a, 0x63,
	0x3e, 0x89, 0x63, 0xe7, 0x84, 0x25, 0x02, 0x2d, 0x70, 0x5e, 0xe7, 0x93, 0x38, 0xfe, 0x44, 0x41,
	0xcc, 0xf7, 0xe1, 0xf2, 0x91, 0xeb, 0x31, 0xc7, 0xed, 0xcb, 0x63, 0x27, 0x61, 0xa2, 0x1f, 0x48,
	0x47, 0x46, 0x5d, 0x16, 0x6a, 0x63, 0x5e, 0x41, 0xec, 0x66, 0x5f, 0x1e, 0xdb, 0x84, 0x3b, 0x40,
	0xd4, 0x90, 0xba, 0x16, 0x86, 0xd4, 0x65, 0x3d, 0x81, 0xcb, 0x45, 0x93, 0x15, 0xf1, 0xf0, 0x48,
	0xb8, 0x70, 0x68, 0xb0, 0x9f, 0x8b, 0x28, 0xd4, 0xc1, 0x21, 0x1b, 0xa9, 0xa5, 0x70, 0x1f, 0x89,
	0x28, 0xb4, 0x3e, 0x83, 0xea, 0x80, 0xdd, 0x79, 0x55, 0xf6, 0x16, 0xd4, 0x33, 0xc3, 0x40, 0xb7,
	0x17, 0x3a, 0xbc, 0xd4, 0x52, 0x28, 0xfa, 0xbe, 0xb0, 0x96, 0xa1, 0x96, 0xe3, 0x2d, 0x62, 0xeb,
	0x19, 0xd4, 0x76, 0x70, 0x1a, 0x0f, 0xfb, 0x82, 0x87, 0xba, 0xf2, 0xcc, 0x45, 0x2f, 0xfa, 0x46,
	0x58, 0xce, 0x0c, 0xe8, 0xdb, 0xbc, 0x06, 0x15, 0xf7, 0xc4, 0xe5, 0x81, 0x7b, 0x18, 0xa8, 0x45,
	0x28, 0xdb, 0x03, 0x80, 0xf5, 0xcb, 0x79, 0xa8, 0x1e, 0x44, 0x31, 0xb1, 0xa6, 0x58, 0x35, 0xc5,
	0xda, 0xae, 0x40, 0x79, 0x24, 0x9b, 0x2d, 0x0a, 0x9d, 0xc8, 0x52, 0x61, 0xe6, 0x72, 0xc2, 0xa0,
	0x6b, 0x13, 0x27, 0xe9, 0x76, 0x74, 0x5c, 0x52, 0xac, 0x0f, 0xdc, 0x0e, 0x46, 0xad, 0xe8, 0x34,
	0x64, 0x89, 0x0a, 0x7d, 0x3a, 0x7b, 0x11, 0x24, 0xad, 0xbd, 0x15, 0xda, 0xf5, 0x3c, 0xaa, 0x6c,
	0x54, 0x02, 0xab, 0x12, 0x70, 0x53, 0xc1, 0xd0, 0x7e, 0x34, 0x51, 0xc0, 0x5d, 0x41, 0xb9, 0xab,
	0x62, 0x2b, 0xb6, 0x9b, 0x08, 0x41, 0x09, 0x14, 0x01, 0x7a, 0x73, 0x59, 0x79, 0x33, 0x01, 0xd0,
	0x9b, 0x77, 0xa0, 0x76, 0xa8, 0x75, 0x39, 0x3b, 0x51, 0x0d, 0xa9, 0xde, 0xae, 0xa6, 0x1d, 0xd3,
	0xc2, 0x9d, 0xa2, 0x17, 0x28, 0xa5, 0xe3, 0x37, 0xca, 0x9f, 0x30, 0x2f, 0xea, 0xf5, 0x28, 0x57,
	0xba, 0x1d, 0xaa, 0xcc, 0x2a, 0x76, 0x35, 0x03, 0xa2, 0x0e, 0x6e, 0xc1, 0x92, 0x7b, 0xc8, 0x03,
	0x32, 0x17, 0xb7, 0x43, 0x95, 0x59, 0xe5, 0x61, 0xa9, 0x69, 0xd8, 0xa0, 0xc1, 0x48, 0x74, 0x03,
	0x96, 0x8e, 0xa3, 0x30, 0x4a, 0x1c, 0xc9, 0x65, 0xc0, 0xa8, 0x30, 0xab, 0xd8, 0x40, 0xa0, 0x03,
	0x84, 0xe0, 0x24, 0x8f, 0x92, 0xa8, 0xa7, 0x32, 0x72, 0x9d, 0x32, 0x72, 0x19, 0x01, 0x94, 0x8a,
	0xd7, 0x60, 0x51, 0x46, 0x0a, 0xb5, 0x4c, 0xa8, 0x05, 0x19, 0x11, 0x62, 0x03, 0x1a, 0xb9, 0xb1,
	0x95, 0x02, 0x1a, 0x64, 0x88, 0xf5, 0xc1, 0xe0, 0x54, 0x67, 0x5f, 0x87, 0x37, 0x77, 0x98, 0xb4,
	0x33, 0xc1, 0xb5, 0xb1, 0x20, 0xce, 0x66, 0x5f, 0x58, 0x3f, 0x85, 0x6b, 0x93, 0xd1, 0x22, 0x36,
	0x7f, 0x98, 0x96, 0x38, 0xb9, 0xed, 0xca, 0xcd, 0x42, 0x1d, 0xe7, 0xcd, 0x50, 0x57, 0x41, 0x24,
	0x40, 0x8f, 0xb2, 0x40, 0xcb, 0x0d, 0x3d, 0x16, 0xb4, 0x74, 0x3f, 0x74, 0x25, 0x31, 0x23, 0x38,
	0xae, 0xc2, 0x05, 0xe1, 0xb1, 0x30, 0xf5, 0x06, 0xd5, 0x40, 0x7d, 0x9e, 0x46, 0x49, 0x97, 0x25,
	0x43, 0x51, 0x49, 0x81, 0x28, 0x5c, 0xfe, 0x9b, 0x01, 0x17, 0x5b, 0x6e, 0x38, 0x3c, 0x1e, 0x05,
	0x33, 0x82, 0xe4, 0x3d, 0x1b, 0x14, 0x88, 0xfc, 0xba, 0x09, 0x8b, 0x2c, 0x44, 0x97, 0x52, 0xbe,
	0x51, 0xb6, 0xd3, 0x66, 0xa1, 0x6f, 0xe0, 0x26, 0x6b, 0x90, 0xae, 0xe9, 0x1b, 0x97, 0x04, 0x77,
	0xad, 0xce, 0x61, 0x5f, 0x3a, 0x3e, 0x17, 0xe4, 0xaf, 0x17, 0x88, 0x55, 0x1d, 0xe1, 0x0f, 0xfb,
	0x72, 0x4b, 0x41, 0x51, 0x18, 0x4d, 0x40, 0x5b, 0x60, 0xe5, 0x1b, 0xa0, 0x41, 0xb8, 0x07, 0xbe,
	0x04, 0x0b, 0x5c, 0x38, 0x21, 0x3b, 0x25, 0xa7, 0x28, 0xdb, 0x17, 0xb8, 0x68, 0xb3, 0x53, 0xeb,
	0x8f, 0x54, 0xf2, 0x2b, 0x54, 0xa5, 0x88, 0xcd, 0x5d, 0xa8, 0xe6, 0xe6, 0x28, 0xf4, 0x62, 0xdd,
	0x29, 0x5c, 0xac, 0x31, 0x0d, 0xd9, 0x4b, 0x03, 0x65, 0x08, 0x5c, 0x96, 0xd8, 0x3d, 0x73, 0xbc,
	0x88, 0xca, 0x27, 0x34, 0xbc, 0xc5, 0xd8, 0x3d, 0x6b, 0x45, 0x42, 0x5a, 0xbf, 0x2a, 0xe9, 0x28,
	0x3c, 0xd2, 0x7f, 0xfa, 0x62, 0x8e, 0xe8, 0xbf, 0x34, 0xa6, 0xff, 0xeb, 0x00, 0x71, 0x12, 0x45,
	0x47, 0x4e, 0x3f, 0x09, 0x04, 0xed, 0x2a, 0x2b, 0x76, 0x85, 0x20, 0xcf, 0x92, 0x20, 0x13, 0x28,
	0xa7, 0x74, 0x14, 0x88, 0x4a, 0x90, 0x1f, 0xa6, 0x3d, 0xb3, 0x5d, 0xf9, 0x24, 0x0b, 0xdd, 0x43,
	0x32, 0xca, 0x00, 0xa1, 0xd4, 0xcc, 0x29, 0x02, 0xdc, 0x82, 0x9a, 0x16, 0x2e, 0x61, 0xae, 0xd0,
	0xc5, 0x54, 0xc5, 0xd6, 0xda, 0xb4, 0x09, 0x66, 0x3e, 0x80, 0x4b, 0x21, 0xeb, 0x44, 0x92, 0xbb,
	0x92, 0x69, 0x3a, 0x35, 0x97, 0x45, 0x92, 0x75, 0x25, 0x43, 0x2a, 0x7a, 0xb2, 0xc5, 0x7f, 0x31,
	0xa0, 0x9a, 0x1f, 0xd4, 0x6c, 0xc0, 0x5c, 0x97, 0x9d, 0xe9, 0x98, 0x8f, 0x9f, 0x54, 0xe7, 0xb3,
	0x50, 0x44, 0x89, 0x83, 0x88, 0x92, 0xae, 0xf3, 0x09, 0xf2, 0x31, 0x3b, 0x33, 0x7f, 0xa0, 0x33,
	0x02, 0x1a, 0x5f, 0xfd, 0xc1, 0x3b, 0x33, 0xa7, 0xa5, 0x1a, 0x38, 0xb8, 0x4a, 0x1f, 0xd6, 0x63,
	0xa8, 0x64, 0x20, 0xf3, 0x12, 0x5c, 0xcc, 0x1a, 0xce, 0x6e, 0x78, 0xe2, 0x06, 0xdc, 0x6f, 0xbc,
	0x61, 0xae, 0xc0, 0xf2, 0x00, 0xfc, 0x09, 0xf7, 0x59, 0xd4, 0x30, 0x86, 0x81, 0xbb, 0x3d, 0xb7,
	0xc3, 0x1a, 0x25, 0xeb, 0x6f, 0x0d, 0x68, 0x10, 0x74, 0xff, 0x38, 0x3a, 0xcd, 0x4d, 0xaa, 0x9f,
	0x04, 0xe9, 0xa4, 0xfa, 0x49, 0x60, 0xb6, 0x72, 0x79, 0xac, 0xfe, 0xe0, 0xfe, 0x64, 0xa9, 0x73,
	0x6c, 0xfe, 0x8f, 0x25, 0xbf, 0x02, 0x6b, 0x85, 0x56, 0x2b, 0x62, 0xeb, 0x7d, 0xb8, 0xfc, 0x38,
	0xf2, 0xba, 0x7b, 0xae, 0x46, 0xaa, 0x9d, 0xf7, 0x74, 0x83, 0xb6, 0x02, 0x58, 0x2b, 0xec, 0xa4,
	0xb6, 0x95, 0x99, 0xf3, 0x18, 0x43, 0xce, 0x83, 0xc1, 0x9e, 0xbd, 0x8c, 0x79, 0xc2, 0x1c, 0x29,
	0xb4, 0x63, 0x95, 0x15, 0xe0, 0x80, 0x6c, 0x5c, 0x48, 0x37, 0x91, 0x88, 0x9b, 0x53, 0xfd, 0xa8,
	0x7d, 0x20, 0xac, 0x6f, 0xc3, 0x15, 0x2a, 0x76, 0xc6, 0xdd, 0x76, 0xba, 0x94, 0x5f, 0x2e, 0xc0,
	0xd5, 0x49, 0x1d, 0x45, 0x6c, 0x5a, 0x50, 0x0b, 0x22, 0xaf, 0xeb, 0x8c, 0x88, 0xbb, 0x14, 0xe8,
	0x99, 0xa1, 0xc8, 0xb7, 0xa1, 0x4e, 0x34, 0xa3, 0x72, 0x57, 0x11, 0xba, 0x9d, 0xca, 0x9e, 0x72,
	0x1a, 0x99, 0x00, 0x71, 0xda, 0x57, 0x93, 0x30, 0x7f, 0x3f, 0x8b, 0x4f, 0x42, 0xba, 0x1d, 0x75,
	0x7c, 0x52, 0x7f, 0xf0, 0x9d, 0xe2, 0xf8, 0x34, 0x51, 0xe8, 0x7b, 0x0a, 0xb4, 0x8f, 0x2c, 0xd2,
	0xa0, 0x45, 0x0d, 0xf3, 0x0e, 0x2c, 0x13, 0xe3, 0x9c, 0xa8, 0x17, 0x48, 0x8a, 0x1a, 0x81, 0x33,
	0x59, 0x29, 0xb9, 0x7f, 0xce, 0x3c, 0x39, 0xe2, 0xee, 0x0a, 0xa8, 0xdd, 0x7d, 0x6b, 0x28, 0xaa,
	0xa8, 0x8d, 0xd3, 0x5b, 0xe7, 0x32, 0xe4, 0x5c, 0x64, 0xb1, 0xbe, 0x9c, 0x83, 0xa5, 0x9c, 0xbc,
	0xe6, 0x75, 0xb8, 0x32, 0x1e, 0xbd, 0xb7, 0xd8, 0x91, 0xdb, 0x0f, 0x64, 0xe3, 0x0d, 0xf3, 0x0e,
	0x58, 0xe3, 0xe8, 0xe7, 0x2e, 0x97, 0x3c, 0xec, 0x3c, 0x8a, 0x92, 0x27, 0xae, 0x90, 0x2c, 0x69,
	0x18, 0xe6, 0x06, 0xdc, 0x9e, 0x46, 0xf7, 0xf4, 0xe8, 0x88, 0x7b, 0xdc, 0x0d, 0x1a, 0x25, 0xf3,
	0x1d, 0x78, 0x7b, 0x36, 0xc7, 0x1f, 0xf5, 0x39, 0x93, 0x8d, 0x39, 0xf3, 0x1e, 0xdc, 0x9d, 0x4d,
	0xdc, 0x8e, 0x94, 0x86, 0x1a, 0xf3, 0xe6, 0x5d, 0xb8, 0x33, 0x9b, 0xfe, 0x31, 0x3f, 0x61, 0x8d,
	0x0b, 0xb3, 0x04, 0x49, 0x45, 0xde, 0x73, 0xcf, 0x1a, 0x0b, 0xe6, 0x7d, 0x78, 0x67, 0x36, 0xe3,
	0x3d, 0xf7, 0x6c, 0xd3, 0xf3, 0x58, 0x2c, 0x1b, 0x8b, 0xe6, 0x07, 0xf0, 0xde, 0x39, 0x24, 0x4f,
	0x23, 0xb4, 0xee, 0x56, 0xb6, 0x38, 0x5c, 0xdf, 0x61, 0xb2, 0x20, 0x2e, 0xe8, 0xea, 0x68, 0x5a,
	0x46, 0x33, 0x61, 0x3e, 0x46, 0x2b, 0xd6, 0xb5, 0x3a, 0x7e, 0x2b, 0xcf, 0xef, 0x30, 0x27, 0xec,
	0xf7, 0x74, 0x65, 0xb2, 0x88, 0xed, 0x76, 0xbf, 0x67, 0xfd, 0xe5, 0x7c, 0x61, 0x00, 0xa2, 0x73,
	0x4a, 0x0c, 0xa0, 0xd9, 0x00, 0xf8, 0x39, 0x34, 0x6e, 0x69, 0x6a, 0x26, 0x9d, 0x2b, 0xaa, 0x64,
	0xd2, 0xaa, 0x5b, 0x67, 0x4a, 0xdd, 0x34, 0xaf, 0x42, 0x39, 0xe4, 0x5e, 0x37, 0x57, 0xb2, 0x67,
	0x6d, 0x3a, 0x67, 0xa0, 0x32, 0x7c, 0x41, 0x9f, 0x33, 0x50, 0x05, 0x7e, 0x1d, 0x70, 0x3f, 0x17,
	0x9c, 0x0d, 0x4e, 0x97, 0x6a, 0x76, 0x85, 0x20, 0x54, 0x85, 0x5e, 0x81, 0xf2, 0xd0, 0x61, 0x52,
	0x0d, 0xab, 0x26, 0x75, 0x88, 0xf4, 0x2d, 0x58, 0x75, 0x43, 0xef, 0x38, 0x4a, 0x9c, 0x6c, 0x4f,
	0x95, 0x55, 0xe9, 0x35, 0xdb, 0x54, 0xb8, 0x74, 0x4f, 0x4b, 0x59, 0xf8, 0x32, 0x2c, 0x68, 0x7f,
	0x04, 0x12, 0x41, 0xb7, 0xc8, 0xad, 0xb1, 0xae, 0x72, 0x69, 0xd5, 0x9c, 0x43, 0x19, 0x52, 0x35,
	0x5e, 0xb6, 0x6b, 0x08, 0x56, 0x6b, 0xf9, 0x50, 0x0e, 0xe8, 0xb4, 0x6f, 0x23, 0x5d, 0x75, 0x40,
	0x67, 0x13, 0x14, 0xe9, 0x86, 0x3d, 0xbb, 0xf6, 0x7a, 0x9e, 0x3d, 0x5e, 0x33, 0xd4, 0x5f, 0xa5,
	0x66, 0x58, 0x9e, 0x5c, 0x33, 0xfc, 0x01, 0x7c, 0x63, 0x9a, 0x4d, 0x52, 0x49, 0x9e, 0xbf, 0x3b,
	0xf8, 0x66, 0xa1, 0xe8, 0x13, 0x4c, 0x4d, 0xdd, 0x1e, 0x60, 0x1a, 0x0a, 0x71, 0x87, 0x9c, 0x33,
	0xe0, 0x32, 0x02, 0xf6, 0xdc, 0x0e, 0xb3, 0xde, 0xa4, 0x7a, 0xbd, 0x3d, 0x2e, 0x1a, 0x6e, 0x17,
	0xf6, 0xa8, 0x02, 0x2d, 0x44, 0x8a, 0x78, 0xf2, 0x7c, 0x8d, 0xc9, 0xf3, 0x7d, 0x01, 0xd7, 0xd4,
	0x1a, 0x4e, 0x28, 0x2a, 0xaf, 0x03, 0x48, 0x37, 0xe9, 0x30, 0x99, 0x3b, 0xd3, 0xa9, 0x28, 0xc8,
	0xb3, 0xa9, 0x9e, 0x62, 0xdd, 0x80, 0xeb, 0x53, 0x38, 0x8b, 0xd8, 0xfa, 0x4f, 0x03, 0xae, 0x29,
	0xbb, 0xf8, 0xba, 0xc7, 0x9e, 0x55, 0xce, 0x8e, 0xa5, 0xa0, 0xf9, 0x82, 0x14, 0xf4, 0x95, 0x0b,
	0x5b, 0xd4, 0xc0, 0x94, 0xf9, 0x89, 0xd8, 0x12, 0x50, 0x4f, 0xdb, 0xcf, 0x69, 0x0b, 0x35, 0xba,
	0xbf, 0x32, 0x46, 0xf7, 0x57, 0x39, 0x82, 0xdc, 0xa9, 0xa7, 0x26, 0xa0, 0xbd, 0xff, 0x80, 0x80,
	0xaa, 0xf5, 0xb9, 0x3c, 0x01, 0x16, 0xec, 0xd6, 0x1f, 0xe3, 0x0e, 0x4d, 0x8f, 0xba, 0x97, 0xf0,
	0x13, 0x1e, 0xb0, 0x0e, 0x9b, 0x74, 0x1e, 0x42, 0xb7, 0x30, 0x6a, 0x10, 0xfa, 0xa6, 0xa3, 0x5d,
	0xe1, 0x08, 0x16, 0x30, 0x4f, 0xea, 0xf3, 0x90, 0x32, 0x17, 0xfb, 0xd4, 0x26, 0x26, 0x91, 0x64,
	0xe9, 0xbe, 0x0c, 0xbf, 0x73, 0x9b, 0xa9, 0x0b, 0xf9, 0xcd, 0xd4, 0xcf, 0xe0, 0xf2, 0x98, 0x10,
	0x3b, 0x49, 0xd4, 0x8f, 0x0b, 0x25, 0x79, 0x02, 0xf5, 0x38, 0xa5, 0xca, 0x5f, 0xa1, 0x4d, 0xd8,
	0x5d, 0x8d, 0x32, 0xb6, 0x6b, 0x59, 0x6f, 0x5a, 0x98, 0x4f, 0x69, 0x53, 0x3e, 0x46, 0x76, 0x8e,
	0xb4, 0x33, 0xb2, 0x3e, 0xa5, 0xb1, 0xfd, 0xef, 0xaf, 0x0d, 0xda, 0xd1, 0x4f, 0xe0, 0x2d, 0x62,
	0xf3, 0x23, 0x80, 0x0e, 0xce, 0x33, 0xbf, 0xa3, 0x7f, 0xe7, 0x7c, 0xd3, 0x20, 0xfd, 0xd8, 0x15,
	0xea, 0x4e, 0x51, 0x70, 0x2b, 0x93, 0x66, 0xe6, 0xb5, 0xe2, 0xb0, 0x9d, 0xa5, 0x22, 0x93, 0x36,
	0xf6, 0xa0, 0xbe, 0xcf, 0x3b, 0x21, 0x56, 0x0a, 0xfb, 0xfd, 0xc3, 0x1e, 0xa7, 0x2d, 0x85, 0x74,
	0x3b, 0x69, 0x46, 0x94, 0x6e, 0x07, 0x17, 0x25, 0xe0, 0x61, 0x37, 0x35, 0x05, 0xfc, 0xc6, 0x4c,
	0x87, 0x23, 0xb9, 0xda, 0x10, 0x2a, 0x76, 0xda, 0xb4, 0xfe, 0xd5, 0x80, 0xd5, 0xec, 0xfc, 0xed,
	0x9c, 0x5b, 0xd4, 0xf3, 0x1c, 0xc6, 0x62, 0x0a, 0xcd, 0x8e, 0xdc, 0x55, 0xea, 0xcd, 0xda, 0xa3,
	0x4b, 0x33, 0x3f, 0xe6, 0x3a, 0xdf, 0x81, 0x05, 0x41, 0xf3, 0x23, 0x4b, 0x9c, 0xa4, 0xa8, 0x61,
	0x55, 0xd8, 0xba, 0x8b, 0xb5, 0x96, 0x3b, 0xe9, 0x1d, 0xf2, 0xe1, 0xef, 0xc3, 0xd2, 0x5e, 0x12,
	0xf5, 0x22, 0xc9, 0xe8, 0x00, 0xb0, 0x0e, 0xa5, 0x6c, 0x6e, 0x25, 0x4e, 0x06, 0x23, 0x78, 0x27,
	0x54, 0x57, 0x31, 0x22, 0x35, 0x18, 0x04, 0xd1, 0x05, 0x8b, 0xb0, 0xde, 0x87, 0xc6, 0x33, 0xc1,
	0x92, 0x2c, 0x4d, 0x20, 0x93, 0x59, 0x51, 0xc0, 0x6a, 0xd2, 0xa5, 0xda, 0x68, 0x3f, 0xcc, 0x10,
	0x7f, 0x6d, 0xc0, 0x5a, 0x21, 0x4a, 0xc4, 0x66, 0x0b, 0xaa, 0xb1, 0x12, 0x55, 0x1d, 0xb5, 0x1b,
	0xa4, 0x86, 0xf5, 0x49, 0x31, 0x2d, 0x9d, 0x93, 0xbd, 0x14, 0xe7, 0x26, 0xf8, 0x11, 0x64, 0x67,
	0xad, 0x8a, 0x4b, 0x89, 0xb8, 0xbc, 0x35, 0xf1, 0xc0, 0x7e, 0x48, 0x8c, 0xaa, 0x97, 0x6b, 0x59,
	0xbf, 0x30, 0x60, 0x75, 0x2f, 0x89, 0xbc, 0x6c, 0xb0, 0x13, 0x8e, 0xc9, 0x89, 0xec, 0x24, 0x4e,
	0x22, 0xcf, 0x49, 0xe8, 0x1c, 0x45, 0x55, 0x72, 0x49, 0xe4, 0xd9, 0x4c, 0x68, 0x05, 0x97, 0x52,
	0x05, 0x5b, 0xcf, 0x49, 0xff, 0x88, 0xa2, 0x45, 0x5e, 0x83, 0x95, 0x5c, 0x33, 0xb7, 0x57, 0x55,
	0x5b, 0xd8, 0x0c, 0xb1, 0xd9, 0x49, 0x18, 0x6b, 0x18, 0x66, 0x53, 0x49, 0x90, 0x82, 0xdb, 0x4f,
	0x35, 0xa6, 0x84, 0x2b, 0x5e, 0x20, 0x9b, 0x88, 0xad, 0xfb, 0x60, 0xee, 0x30, 0x49, 0x87, 0x6d,
	0x4f, 0x3a, 0xc9, 0xec, 0xa0, 0x61, 0x7d, 0x0b, 0x56, 0xc6, 0x3a, 0xa8, 0x8d, 0x6a, 0x9f, 0xe7,
	0x4e, 0xf6, 0x6a, 0xf6, 0x62, 0x9f, 0xab, 0x43, 0xbb, 0xcf, 0xa0, 0xde, 0xa2, 0x43, 0x08, 0xda,
	0x54, 0xcf, 0xf0, 0x1c, 0x5d, 0xbf, 0x96, 0x06, 0xf5, 0xeb, 0x9b, 0x50, 0x39, 0xc1, 0x8e, 0x74,
	0xa8, 0xa1, 0xb2, 0x5f, 0x99, 0x00, 0x1f, 0xb3, 0x33, 0xeb, 0x53, 0x58, 0x1e, 0xe2, 0x2d, 0x62,
	0xf3, 0x11, 0xd4, 0xf4, 0x29, 0x88, 0xba, 0x26, 0x98, 0x7a, 0xd0, 0xa8, 0x3a, 0xab, 0x3b, 0x03,
	0xbb, 0xea, 0xe5, 0x5a, 0x56, 0x04, 0xd5, 0x3c, 0x76, 0x58, 0x0e, 0xfd, 0x40, 0x24, 0x95, 0x63,
	0xd6, 0xd1, 0xcb, 0x2d, 0xa8, 0xc9, 0xc4, 0x0d, 0x85, 0x17, 0xf9, 0x0c, 0xd3, 0xb8, 0x8e, 0x31,
	0xd5, 0x0c, 0xf8, 0x2c, 0x09, 0xac, 0x8f, 0x28, 0x90, 0xb7, 0x19, 0xf3, 0x5b, 0x51, 0x78, 0xc4,
	0x93, 0xde, 0xf3, 0xcc, 0x47, 0x50, 0x69, 0xaf, 0x52, 0xd9, 0x5b, 0x9b, 0x14, 0xb8, 0x27, 0xf0,
	0x12, 0xb1, 0x79, 0x13, 0xaa, 0x21, 0x63, 0xbe, 0xe3, 0x29, 0x2c, 0x71, 0x2d, 0xdb, 0x4b, 0xe1,
	0xa0, 0x83, 0xf5, 0x6d, 0x58, 0x79, 0x12, 0xf9, 0xfc, 0xe8, 0x6c, 0x58, 0x8c, 0x99, 0xee, 0x7c,
	0x19, 0x56, 0xc7, 0xfb, 0x89, 0xd8, 0xfa, 0xe5, 0x1c, 0x98, 0x9b, 0x54, 0xaa, 0x3f, 0x66, 0x27,
	0x2c, 0x68, 0xb3, 0xd3, 0x03, 0x57, 0x74, 0x91, 0x5f, 0xc2, 0x7a, 0x6e, 0x7a, 0xeb, 0xac, 0xf9,
	0x29, 0x10, 0x55, 0xff, 0xef, 0x61, 0x21, 0xc8, 0x7c, 0x87, 0x1e, 0x8b, 0xe4, 0x1f, 0x1d, 0xa8,
	0x29, 0x9b, 0x88, 0x7c, 0xce, 0x58, 0x77, 0x73, 0xf0, 0xf8, 0xe0, 0x03, 0x58, 0xd3, 0x3c, 0xc7,
	0x3a, 0xa9, 0x40, 0xbb, 0xaa, 0xd0, 0xe3, 0xdd, 0x06, 0x23, 0xc5, 0x81, 0x2b, 0x8f, 0xa2, 0xa4,
	0xa7, 0xba, 0xa9, 0x00, 0xbc, 0x9a, 0x8e, 0xb5, 0xa7, 0x91, 0xd4, 0xed, 0xf7, 0xe0, 0x4a, 0x7e,
	0xb4, 0xe1, 0x8e, 0xea, 0xe9, 0xc6, 0xe5, 0xc1, 0x78, 0x43, 0x5d, 0xef, 0xc0, 0x32, 0xf5, 0xe9,
	0xf0, 0x23, 0xe9, 0x9c, 0xb8, 0x41, 0x9f, 0xa5, 0x97, 0xaf, 0x08, 0xde, 0xe1, 0x47, 0xf2, 0x13,
	0x04, 0x9a, 0xdf, 0x04, 0xbd, 0xcb, 0x71, 0xbc, 0x63, 0xe6, 0x75, 0x9d, 0x00, 0x35, 0xa8, 0x6f,
	0x39, 0x1a, 0x0a, 0x43, 0xa7, 0x1c, 0xa4, 0x59, 0xb4, 0x0b, 0xe2, 0x8a, 0x5b, 0x47, 0xbd, 0x95,
	0xc2, 0x76, 0xbb, 0xdf, 0xcb, 0x5e, 0x39, 0xd1, 0xda, 0xe9, 0x3b, 0x56, 0x04, 0xd0, 0xca, 0xdd,
	0x85, 0x66, 0x2e, 0xdb, 0xb7, 0x8e, 0xdd, 0xb0, 0xc3, 0xd2, 0x2b, 0xd6, 0x91, 0x54, 0x60, 0xfd,
	0xbb, 0xa1, 0x8e, 0xe2, 0x0b, 0x88, 0x45, 0x6c, 0xee, 0x15, 0xd4, 0x05, 0xef, 0xbd, 0x42, 0x5d,
	0xa0, 0xd8, 0xe5, 0xab, 0x83, 0xa7, 0x23, 0x07, 0xd2, 0xa5, 0x29, 0x1b, 0x96, 0xb1, 0xb3, 0x1e,
	0xcd, 0x6e, 0xe8, 0x58, 0x5a, 0xd5, 0x7e, 0x6a, 0x23, 0x38, 0xa8, 0xfd, 0x54, 0x91, 0x6f, 0xfd,
	0x97, 0x01, 0xd7, 0xa6, 0x49, 0xf6, 0xff, 0x50, 0xd7, 0x99, 0x3f, 0x01, 0xb4, 0x9a, 0xe8, 0x84,
	0xf9, 0xce, 0x08, 0xdb, 0xb9, 0x57, 0x62, 0xbb, 0xaa, 0xb9, 0x0c, 0x55, 0x6f, 0xd6, 0xdf, 0x1b,
	0xb0, 0x36, 0x41, 0x4f, 0x5f, 0xe7, 0xe1, 0xff, 0x0b, 0x48, 0x87, 0x77, 0x0a, 0x96, 0xef, 0xbc,
	0x2c, 0x4d, 0xcd, 0xa3, 0x35, 0xe0, 0x6c, 0x6d, 0xc1, 0xda, 0x87, 0x6e, 0xe8, 0x07, 0x6c, 0x64,
	0x91, 0xc7, 0x6d, 0x15, 0xfd, 0x01, 0x97, 0x1a, 0x53, 0x61, 0x7a, 0x21, 0xc3, 0x05, 0x65, 0x46,
	0xeb, 0x2a, 0x34, 0x8b, 0xb9, 0x88, 0xd8, 0xba, 0x4d, 0xa9, 0xd1, 0xce, 0x6d, 0xa3, 0x8a, 0x1c,
	0xe1, 0x0f, 0x0d, 0x4a, 0x88, 0xc3, 0x64, 0x22, 0x1e, 0xdf, 0x96, 0x19, 0x33, 0x4f, 0x06, 0x4b,
	0xaf, 0x79, 0x32, 0xa8, 0x0a, 0xa8, 0x36, 0x3b, 0x3d, 0xe4, 0x4c, 0x45, 0xd8, 0xb4, 0x80, 0xfa,
	0xb9, 0x01, 0x17, 0xd3, 0x83, 0x91, 0xa7, 0x87, 0xd2, 0xe5, 0xe1, 0x96, 0x7b, 0x76, 0xbe, 0xcb,
	0xe9, 0xeb, 0x00, 0x11, 0xf5, 0xc0, 0x38, 0xa6, 0x63, 0x6d, 0x25, 0xca, 0x78, 0x6c, 0x40, 0x43,
	0xed, 0x84, 0x0e, 0x39, 0x73, 0x54, 0x04, 0xd2, 0x5e, 0x54, 0xa7, 0x3d, 0x51, 0x26, 0x0a, 0xca,
	0xb0, 0x56, 0x28, 0x9e, 0x88, 0xcd, 0x8f, 0x73, 0x92, 0xcc, 0xf4, 0x98, 0xb1, 0x89, 0x0c, 0x24,
	0x26, 0x87, 0xb9, 0x02, 0x65, 0xaa, 0x4e, 0x51, 0x5e, 0x7d, 0x62, 0x86, 0xed, 0x2d, 0xf7, 0xcc,
	0x7a, 0x09, 0xb5, 0x8f, 0xc3, 0xe8, 0x34, 0x60, 0x7e, 0x87, 0x51, 0xd6, 0x59, 0x03, 0x7a, 0x87,
	0x99, 0x16, 0x20, 0x15, 0x7b, 0x01, 0x9b, 0xea, 0x19, 0xce, 0xe0, 0x19, 0x68, 0x69, 0xe4, 0x19,
	0xe8, 0x15, 0x28, 0x7f, 0xde, 0xef, 0xc5, 0xb9, 0x0c, 0xbe, 0x88, 0xed, 0x67, 0x49, 0x60, 0x5e,
	0x86, 0x05, 0x21, 0x5d, 0xd9, 0x4f, 0x53, 0x85, 0x6e, 0x59, 0x2f, 0xc0, 0x54, 0x33, 0xdf, 0xe9,
	0x73, 0x9f, 0xa5, 0xd7, 0x1c, 0xab, 0x70, 0x41, 0xdd, 0xe1, 0xaa, 0xc1, 0x55, 0x23, 0xdd, 0x83,
	0xb0, 0x50, 0xea, 0x91, 0xd3, 0x26, 0x86, 0x9b, 0xdc, 0xe6, 0x9f, 0xbe, 0xad, 0x7f, 0x32, 0x60,
	0x25, 0xc7, 0x7a, 0xbf, 0x7f, 0x78, 0x70, 0xcc, 0x7a, 0xb4, 0x70, 0x12, 0x3f, 0xf2, 0x0f, 0x9a,
	0x2a, 0x04, 0xa1, 0x39, 0x7c, 0x04, 0x55, 0xcd, 0x35, 0xaf, 0xf1, 0xb7, 0x0b, 0x35, 0x3e, 0x2e,
	0xb9, 0xbd, 0xa4, 0x3b, 0x93, 0xc6, 0x7f, 0xa0, 0x95, 0x95, 0x8b, 0x4a, 0xc5, 0x77, 0xe6, 0x43,
	0xca, 0x57, 0x0a, 0x25, 0xcb, 0xfd, 0x13, 0x03, 0x1a, 0xb9, 0x41, 0xd4, 0x04, 0x6e, 0x43, 0x7d,
	0x30, 0x01, 0x67, 0x70, 0x1d, 0x54, 0xcd, 0x26, 0x81, 0x0a, 0x6f, 0x43, 0x5d, 0xf4, 0x0f, 0x1d,
	0x45, 0x99, 0x9b, 0xc9, 0xc6, 0xac, 0x99, 0xa4, 0x8a, 0xb2, 0xab, 0x42, 0x7f, 0x91, 0x28, 0x0f,
	0x75, 0xf5, 0x35, 0xb0, 0x52, 0xa2, 0x4e, 0xf3, 0xdf, 0x79, 0x7c, 0xc6, 0xf2, 0x75, 0xd5, 0x55,
	0xc8, 0x43, 0xc4, 0xe8, 0xee, 0x39, 0x79, 0x8d, 0x29, 0xee, 0x3e, 0xaa, 0x14, 0xbd, 0x82, 0x24,
	0xe9, 0xf7, 0xc6, 0xdc, 0x5d, 0xbd, 0xd1, 0x3e, 0xa7, 0x90, 0xbf, 0x32, 0x60, 0x75, 0xb4, 0x33,
	0x1d, 0x1d, 0xdf, 0x80, 0x25, 0x5a, 0x4d, 0x21, 0x13, 0x1e, 0x76, 0xb4, 0xd2, 0x01, 0x41, 0xfb,
	0x04, 0xc9, 0x7c, 0x23, 0x77, 0x8e, 0x42, 0x4b, 0x49, 0xaf, 0x59, 0x6f, 0x42, 0x95, 0x0b, 0xc7,
	0x8b, 0x7a, 0x71, 0xc0, 0x24, 0x4b, 0xdf, 0xc2, 0x2d, 0x71, 0xd1, 0x4a, 0x41, 0xb8, 0xe1, 0xcd,
	0x1e, 0x50, 0xeb, 0x27, 0x8c, 0x69, 0x9b, 0x3c, 0x22, 0x92, 0x6e, 0xa0, 0x0b, 0x26, 0xd5, 0xb0,
	0xfe, 0xa6, 0x94, 0xda, 0xf8, 0x40, 0x56, 0x8c, 0x3e, 0x0d, 0x98, 0x43, 0x2f, 0x37, 0xe8, 0x01,
	0x2b, 0x7e, 0x9a, 0x2f, 0x60, 0x39, 0x1d, 0xdb, 0xd1, 0x8e, 0x38, 0xed, 0xc6, 0xb0, 0x80, 0xe9,
	0xbd, 0x7d, 0xea, 0x66, 0xd7, 0x53, 0x3e, 0xaa, 0x6d, 0x3e, 0x1a, 0x37, 0xf2, 0xdf, 0x3a, 0x17,
	0x4f, 0x3a, 0x24, 0x1d, 0xd8, 0xba, 0x0f, 0x0b, 0x9a, 0xe3, 0x65, 0x30, 0xf7, 0x0f, 0x36, 0x0f,
	0x9e, 0xed, 0x3b, 0xcf, 0xda, 0xfb, 0x7b, 0xdb, 0xad, 0xdd, 0x47, 0xbb, 0xdb, 0x5b, 0x8d, 0x37,
	0x70, 0xfb, 0xa6, 0xe1, 0xed, 0xa7, 0x07, 0x4e, 0xeb, 0xe9, 0x93, 0xbd, 0xc7, 0xdb, 0x07, 0xdb,
	0x5b, 0x0d, 0xc3, 0x5c, 0x85, 0x86, 0xc6, 0x0c, 0xa0, 0x25, 0xd3, 0x84, 0xba, 0x86, 0x6e, 0xbf,
	0xd8, 0xdb, 0xb5, 0xb7, 0xb7, 0x1a, 0x73, 0xd6, 0x9f, 0xcd, 0x41, 0x73, 0x54, 0x10, 0x7a, 0xba,
	0x19, 0xb2, 0xc4, 0xfc, 0x2e, 0xcc, 0x53, 0x51, 0x6a, 0xcc, 0xf4, 0x94, 0x21, 0xcd, 0xd8, 0xd4,
	0x4b, 0x3d, 0x57, 0xd2, 0x6b, 0x99, 0xaf, 0xc0, 0x6b, 0x19, 0x94, 0x6a, 0xda, 0x36, 0x2c, 0xaa,
	0x17, 0xf8, 0xa2, 0xb9, 0x40, 0xe3, 0xfc, 0xf6, 0xb9, 0xc6, 0x49, 0x85, 0xbc, 0x67, 0x53, 0x67,
	0x3b, 0x65, 0x72, 0xf5, 0xef, 0x0c, 0x58, 0x50, 0x30, 0xfd, 0x60, 0xca, 0xe7, 0xf4, 0x0a, 0x51,
	0xcf, 0x44, 0x4b, 0xa0, 0xa1, 0xe9, 0xe3, 0x64, 0xfd, 0x57, 0x80, 0x64, 0x2f, 0xd3, 0x58, 0xaa,
	0x7f, 0x14, 0x38, 0x60, 0x2f, 0x65, 0x8e, 0x20, 0xf7, 0x30, 0x5b, 0x13, 0x90, 0x31, 0x63, 0x90,
	0x0c, 0x5c, 0xde, 0x73, 0x72, 0x31, 0xbd, 0x3e, 0x35, 0x48, 0x2a, 0x09, 0xb5, 0x09, 0x2d, 0x51,
	0x67, 0xd5, 0xb0, 0x7e, 0x53, 0xb0, 0x22, 0x9b, 0xfe, 0x09, 0x96, 0x32, 0x3e, 0xa6, 0x8d, 0x98,
	0x25, 0x3c, 0xca, 0x5e, 0x83, 0xaa, 0x96, 0x79, 0x17, 0x2e, 0xaa, 0xaf, 0xf4, 0x16, 0x94, 0xeb,
	0x74, 0x34, 0x6f, 0x2f, 0x2b, 0x84, 0xba, 0x09, 0xc5, 0x0d, 0xd2, 0x1d, 0xd0, 0xa0, 0xc1, 0x6b,
	0xdc, 0x39, 0xa2, 0xac, 0x29, 0x70, 0xfa, 0x16, 0x77, 0xc8, 0x90, 0xe7, 0x5f, 0xdb, 0x90, 0xc7,
	0x94, 0x73, 0xe1, 0xf5, 0x95, 0x63, 0xfe, 0x18, 0xaa, 0xe9, 0x4a, 0x48, 0xd6, 0x4b, 0x2d, 0xe6,
	0x77, 0xcf, 0x25, 0x56, 0xaa, 0x44, 0x6d, 0x31, 0x24, 0xa5, 0x5e, 0x57, 0xfc, 0x16, 0x57, 0xdb,
	0x00, 0x03, 0x54, 0x6e, 0xd1, 0x73, 0x89, 0x51, 0x2f, 0x7a, 0x7a, 0xd8, 0x9c, 0xb7, 0x8a, 0xd2,
	0xa8, 0x55, 0x58, 0xff, 0x3c, 0x5e, 0xc9, 0x64, 0x7f, 0xc7, 0xd8, 0x50, 0x3b, 0xd4, 0x16, 0x4c,
	0x7f, 0x34, 0xe8, 0xf3, 0xa8, 0x77, 0x5f, 0xc9, 0xf6, 0xed, 0x6a, 0xca, 0x83, 0x8a, 0x94, 0x1f,
	0x83, 0xe9, 0xea, 0x39, 0x8e, 0xfd, 0x6f, 0xf1, 0xee, 0x2b, 0xa9, 0xc8, 0x6e, 0xa4, 0x8c, 0xb2,
	0x7f, 0x26, 0xb6, 0xe1, 0x1b, 0x2d, 0x5c, 0x88, 0xf1, 0xd9, 0x90, 0xfb, 0x9d, 0x37, 0x9b, 0x30,
	0xb8, 0x53, 0xc8, 0x26, 0x1b, 0xf9, 0x95, 0xd8, 0xe5, 0xfc, 0xa1, 0x94, 0xf7, 0x07, 0xeb, 0xa7,
	0x70, 0x63, 0xaa, 0xb4, 0x22, 0x36, 0xbf, 0x37, 0x88, 0x3b, 0xd3, 0xce, 0x8e, 0x07, 0xbd, 0xc8,
	0x60, 0xd2, 0x3e, 0xd6, 0x31, 0xd4, 0x87, 0x51, 0x5f, 0xdd, 0x60, 0x30, 0xa9, 0xa9, 0xcb, 0x53,
	0x95, 0x0c, 0x55, 0xc3, 0xfa, 0x85, 0x01, 0x2b, 0x83, 0xa1, 0x5a, 0x69, 0xe8, 0x1a, 0x89, 0x6e,
	0x4c, 0x78, 0x7a, 0xc8, 0x5c, 0x74, 0x63, 0xc2, 0xfb, 0x8a, 0xf3, 0xc4, 0xf2, 0x10, 0xf3, 0x34,
	0x2a, 0x53, 0x67, 0xe9, 0xb2, 0x5d, 0xe1, 0xa2, 0xa5, 0x00, 0xd6, 0x5f, 0x19, 0xb0, 0x5e, 0x68,
	0xe3, 0xd8, 0x3b, 0x3d, 0x1e, 0x3c, 0xd7, 0x52, 0x3e, 0xca, 0x1f, 0x35, 0xa8, 0x5c, 0x7c, 0xbe,
	0x70, 0x43, 0x27, 0x46, 0xd9, 0xa9, 0x44, 0xce, 0x24, 0xe6, 0x86, 0x4c, 0xe2, 0xe7, 0x06, 0xdc,
	0x9c, 0x21, 0xa9, 0x88, 0xcd, 0x9f, 0xc0, 0x25, 0xbd, 0x46, 0x03, 0xdd, 0xe6, 0xaa, 0xaf, 0x8d,
	0x19, 0xba, 0xcb, 0x56, 0xc7, 0x5e, 0x49, 0x86, 0x01, 0x38, 0xc2, 0xdd, 0x9f, 0xc1, 0xca, 0x50,
	0x69, 0xab, 0xa3, 0xda, 0x0d, 0x78, 0xb3, 0x00, 0x9c, 0x3b, 0xbf, 0xbd, 0x09, 0xd7, 0x8b, 0x08,
	0xda, 0x91, 0x7c, 0xc4, 0x43, 0x2e, 0x8e, 0x1b, 0x86, 0xb9, 0x0e, 0xd7, 0x8a, 0x48, 0x14, 0x9e,
	0xf9, 0x8d, 0xd2, 0xdd, 0x3f, 0x2d, 0x28, 0xe4, 0x48, 0x63, 0x6f, 0xc3, 0xad, 0xf6, 0xf6, 0xf3,
	0x87, 0xbb, 0xdb, 0xce, 0x66, 0xbb, 0xf5, 0xe1, 0x53, 0xdb, 0x39, 0xd8, 0xdc, 0xff, 0xd8, 0x39,
	0xf8, 0x74, 0x6f, 0x7b, 0xa4, 0xe0, 0xb8, 0x0d, 0xeb, 0x93, 0x08, 0x1f, 0x6e, 0xef, 0xec, 0xb6,
	0xdb, 0xdb, 0x76, 0xc3, 0x98, 0x46, 0xb5, 0xb9, 0xf5, 0xc9, 0x66, 0xbb, 0x85, 0xc5, 0xc8, 0xdd,
	0xbf, 0x30, 0xd2, 0x9d, 0x4e, 0x3e, 0xda, 0xe7, 0x3a, 0xdb, 0xdb, 0xcf, 0x37, 0xed, 0x2d, 0xa7,
	0xb0, 0xf2, 0x99, 0x44, 0x45, 0x75, 0xd0, 0xe3, 0xcd, 0xdd, 0x27, 0x54, 0x05, 0xad, 0xc3, 0xb5,
	0x42, 0xaa, 0x94, 0xa2, 0x34, 0x91, 0x22, 0xab, 0x8f, 0x1e, 0xfe, 0xce, 0x67, 0x1f, 0x74, 0xa2,
	0xc0, 0x0d, 0x3b, 0xf7, 0x3e, 0x78, 0x20, 0xe5, 0x3d, 0x2f, 0xea, 0xdd, 0xa7, 0xff, 0x20, 0xbd,
	0x28, 0xb8, 0x2f, 0x58, 0x72, 0xc2, 0x3d, 0x26, 0x8a, 0xfe, 0x96, 0x3c, 0x5c, 0x20, 0xb2, 0xf7,
	0xff, 0x37, 0x00, 0x00, 0xff, 0xff, 0x13, 0x6e, 0x32, 0xb2, 0x6e, 0x39, 0x00, 0x00,
}
