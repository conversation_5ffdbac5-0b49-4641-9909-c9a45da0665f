// Code generated by protoc-gen-go. DO NOT EDIT.
// source: treasure-house/treasure-house.proto

package treasure_house // import "golang.52tt.com/protocol/services/treasure-house"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 阶段类型 ga.treasure_house_logic.PhaseType
type PhaseType int32

const (
	PhaseType_PHASE_TYPE_UNSPECIFIED PhaseType = 0
	PhaseType_PHASE_TYPE_WARMUP      PhaseType = 1
	PhaseType_PHASE_TYPE_CONDITION   PhaseType = 2
	PhaseType_PHASE_TYPE_ACTIVITY    PhaseType = 3
	PhaseType_PHASE_TYPE_TOP_UP      PhaseType = 4
	PhaseType_PHASE_TYPE_PURCHASE    PhaseType = 5
	PhaseType_PHASE_TYPE_ENDING      PhaseType = 6
)

var PhaseType_name = map[int32]string{
	0: "PHASE_TYPE_UNSPECIFIED",
	1: "PHASE_TYPE_WARMUP",
	2: "PHASE_TYPE_CONDITION",
	3: "PHASE_TYPE_ACTIVITY",
	4: "PHASE_TYPE_TOP_UP",
	5: "PHASE_TYPE_PURCHASE",
	6: "PHASE_TYPE_ENDING",
}
var PhaseType_value = map[string]int32{
	"PHASE_TYPE_UNSPECIFIED": 0,
	"PHASE_TYPE_WARMUP":      1,
	"PHASE_TYPE_CONDITION":   2,
	"PHASE_TYPE_ACTIVITY":    3,
	"PHASE_TYPE_TOP_UP":      4,
	"PHASE_TYPE_PURCHASE":    5,
	"PHASE_TYPE_ENDING":      6,
}

func (x PhaseType) String() string {
	return proto.EnumName(PhaseType_name, int32(x))
}
func (PhaseType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{0}
}

// 礼物领取方式
type PresentGetMethod int32

const (
	PresentGetMethod_PRESENT_GET_METHOD_UNSPECIFIED PresentGetMethod = 0
	PresentGetMethod_PRESENT_GET_METHOD_MANUAL      PresentGetMethod = 1
	PresentGetMethod_PRESENT_GET_METHOD_AUTO        PresentGetMethod = 2
)

var PresentGetMethod_name = map[int32]string{
	0: "PRESENT_GET_METHOD_UNSPECIFIED",
	1: "PRESENT_GET_METHOD_MANUAL",
	2: "PRESENT_GET_METHOD_AUTO",
}
var PresentGetMethod_value = map[string]int32{
	"PRESENT_GET_METHOD_UNSPECIFIED": 0,
	"PRESENT_GET_METHOD_MANUAL":      1,
	"PRESENT_GET_METHOD_AUTO":        2,
}

func (x PresentGetMethod) String() string {
	return proto.EnumName(PresentGetMethod_name, int32(x))
}
func (PresentGetMethod) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{1}
}

// 期限类型
type PresentValidTimeType int32

const (
	PresentValidTimeType_PRESENT_VALID_TIME_TYPE_UNSPECIFIED PresentValidTimeType = 0
	PresentValidTimeType_PRESENT_VALID_TIME_TYPE_DURATION    PresentValidTimeType = 1
	PresentValidTimeType_PRESENT_VALID_TIME_TYPE_DEADLINE    PresentValidTimeType = 2
)

var PresentValidTimeType_name = map[int32]string{
	0: "PRESENT_VALID_TIME_TYPE_UNSPECIFIED",
	1: "PRESENT_VALID_TIME_TYPE_DURATION",
	2: "PRESENT_VALID_TIME_TYPE_DEADLINE",
}
var PresentValidTimeType_value = map[string]int32{
	"PRESENT_VALID_TIME_TYPE_UNSPECIFIED": 0,
	"PRESENT_VALID_TIME_TYPE_DURATION":    1,
	"PRESENT_VALID_TIME_TYPE_DEADLINE":    2,
}

func (x PresentValidTimeType) String() string {
	return proto.EnumName(PresentValidTimeType_name, int32(x))
}
func (PresentValidTimeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{2}
}

// 按钮类型
type ButtonAction int32

const (
	ButtonAction_BUTTON_ACTION_UNSPECIFIED   ButtonAction = 0
	ButtonAction_BUTTON_ACTION_DISABLE       ButtonAction = 1
	ButtonAction_BUTTON_ACTION_MSG_BOX       ButtonAction = 2
	ButtonAction_BUTTON_ACTION_ACTIVITY_JUMP ButtonAction = 3
	ButtonAction_BUTTON_ACTION_JUMP          ButtonAction = 4
)

var ButtonAction_name = map[int32]string{
	0: "BUTTON_ACTION_UNSPECIFIED",
	1: "BUTTON_ACTION_DISABLE",
	2: "BUTTON_ACTION_MSG_BOX",
	3: "BUTTON_ACTION_ACTIVITY_JUMP",
	4: "BUTTON_ACTION_JUMP",
}
var ButtonAction_value = map[string]int32{
	"BUTTON_ACTION_UNSPECIFIED":   0,
	"BUTTON_ACTION_DISABLE":       1,
	"BUTTON_ACTION_MSG_BOX":       2,
	"BUTTON_ACTION_ACTIVITY_JUMP": 3,
	"BUTTON_ACTION_JUMP":          4,
}

func (x ButtonAction) String() string {
	return proto.EnumName(ButtonAction_name, int32(x))
}
func (ButtonAction) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{3}
}

// 活动状态
type ActivityStatus int32

const (
	ActivityStatus_ACTIVITY_STATUS_UNSPECIFIED ActivityStatus = 0
	ActivityStatus_ACTIVITY_STATUS_IN_PROGRESS ActivityStatus = 1
	ActivityStatus_ACTIVITY_STATUS_NOT_STARTED ActivityStatus = 2
	ActivityStatus_ACTIVITY_STATUS_ENDED       ActivityStatus = 3
)

var ActivityStatus_name = map[int32]string{
	0: "ACTIVITY_STATUS_UNSPECIFIED",
	1: "ACTIVITY_STATUS_IN_PROGRESS",
	2: "ACTIVITY_STATUS_NOT_STARTED",
	3: "ACTIVITY_STATUS_ENDED",
}
var ActivityStatus_value = map[string]int32{
	"ACTIVITY_STATUS_UNSPECIFIED": 0,
	"ACTIVITY_STATUS_IN_PROGRESS": 1,
	"ACTIVITY_STATUS_NOT_STARTED": 2,
	"ACTIVITY_STATUS_ENDED":       3,
}

func (x ActivityStatus) String() string {
	return proto.EnumName(ActivityStatus_name, int32(x))
}
func (ActivityStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{4}
}

// 条件类型
type ConditionType int32

const (
	ConditionType_CONDITION_TYPE_UNSPECIFIED    ConditionType = 0
	ConditionType_CONDITION_TYPE_TOP_UP         ConditionType = 1
	ConditionType_CONDITION_TYPE_SEND_PRESENT   ConditionType = 2
	ConditionType_CONDITION_TYPE_NOBILITY_LEVEL ConditionType = 3
	ConditionType_CONDITION_TYPE_RICH_LEVEL     ConditionType = 4
	ConditionType_CONDITION_TYPE_CONSUME        ConditionType = 5
)

var ConditionType_name = map[int32]string{
	0: "CONDITION_TYPE_UNSPECIFIED",
	1: "CONDITION_TYPE_TOP_UP",
	2: "CONDITION_TYPE_SEND_PRESENT",
	3: "CONDITION_TYPE_NOBILITY_LEVEL",
	4: "CONDITION_TYPE_RICH_LEVEL",
	5: "CONDITION_TYPE_CONSUME",
}
var ConditionType_value = map[string]int32{
	"CONDITION_TYPE_UNSPECIFIED":    0,
	"CONDITION_TYPE_TOP_UP":         1,
	"CONDITION_TYPE_SEND_PRESENT":   2,
	"CONDITION_TYPE_NOBILITY_LEVEL": 3,
	"CONDITION_TYPE_RICH_LEVEL":     4,
	"CONDITION_TYPE_CONSUME":        5,
}

func (x ConditionType) String() string {
	return proto.EnumName(ConditionType_name, int32(x))
}
func (ConditionType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{5}
}

// 条件值
type ConditionValue struct {
	Value                uint64   `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConditionValue) Reset()         { *m = ConditionValue{} }
func (m *ConditionValue) String() string { return proto.CompactTextString(m) }
func (*ConditionValue) ProtoMessage()    {}
func (*ConditionValue) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{0}
}
func (m *ConditionValue) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConditionValue.Unmarshal(m, b)
}
func (m *ConditionValue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConditionValue.Marshal(b, m, deterministic)
}
func (dst *ConditionValue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConditionValue.Merge(dst, src)
}
func (m *ConditionValue) XXX_Size() int {
	return xxx_messageInfo_ConditionValue.Size(m)
}
func (m *ConditionValue) XXX_DiscardUnknown() {
	xxx_messageInfo_ConditionValue.DiscardUnknown(m)
}

var xxx_messageInfo_ConditionValue proto.InternalMessageInfo

func (m *ConditionValue) GetValue() uint64 {
	if m != nil {
		return m.Value
	}
	return 0
}

// 条件
type Condition struct {
	Type                 ConditionType   `protobuf:"varint,1,opt,name=type,proto3,enum=treasure_house.ConditionType" json:"type,omitempty"`
	Value                *ConditionValue `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *Condition) Reset()         { *m = Condition{} }
func (m *Condition) String() string { return proto.CompactTextString(m) }
func (*Condition) ProtoMessage()    {}
func (*Condition) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{1}
}
func (m *Condition) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Condition.Unmarshal(m, b)
}
func (m *Condition) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Condition.Marshal(b, m, deterministic)
}
func (dst *Condition) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Condition.Merge(dst, src)
}
func (m *Condition) XXX_Size() int {
	return xxx_messageInfo_Condition.Size(m)
}
func (m *Condition) XXX_DiscardUnknown() {
	xxx_messageInfo_Condition.DiscardUnknown(m)
}

var xxx_messageInfo_Condition proto.InternalMessageInfo

func (m *Condition) GetType() ConditionType {
	if m != nil {
		return m.Type
	}
	return ConditionType_CONDITION_TYPE_UNSPECIFIED
}

func (m *Condition) GetValue() *ConditionValue {
	if m != nil {
		return m.Value
	}
	return nil
}

// 礼物信息
type PresentInfo struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IconUrl              string   `protobuf:"bytes,3,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	Price                uint64   `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentInfo) Reset()         { *m = PresentInfo{} }
func (m *PresentInfo) String() string { return proto.CompactTextString(m) }
func (*PresentInfo) ProtoMessage()    {}
func (*PresentInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{2}
}
func (m *PresentInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentInfo.Unmarshal(m, b)
}
func (m *PresentInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentInfo.Marshal(b, m, deterministic)
}
func (dst *PresentInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentInfo.Merge(dst, src)
}
func (m *PresentInfo) XXX_Size() int {
	return xxx_messageInfo_PresentInfo.Size(m)
}
func (m *PresentInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PresentInfo proto.InternalMessageInfo

func (m *PresentInfo) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *PresentInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *PresentInfo) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *PresentInfo) GetPrice() uint64 {
	if m != nil {
		return m.Price
	}
	return 0
}

// 马甲包跳转配置
type PackageJumpConfig struct {
	App                  uint32   `protobuf:"varint,1,opt,name=app,proto3" json:"app,omitempty"`
	Platform             uint32   `protobuf:"varint,2,opt,name=platform,proto3" json:"platform,omitempty"`
	Link                 string   `protobuf:"bytes,3,opt,name=link,proto3" json:"link,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PackageJumpConfig) Reset()         { *m = PackageJumpConfig{} }
func (m *PackageJumpConfig) String() string { return proto.CompactTextString(m) }
func (*PackageJumpConfig) ProtoMessage()    {}
func (*PackageJumpConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{3}
}
func (m *PackageJumpConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PackageJumpConfig.Unmarshal(m, b)
}
func (m *PackageJumpConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PackageJumpConfig.Marshal(b, m, deterministic)
}
func (dst *PackageJumpConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PackageJumpConfig.Merge(dst, src)
}
func (m *PackageJumpConfig) XXX_Size() int {
	return xxx_messageInfo_PackageJumpConfig.Size(m)
}
func (m *PackageJumpConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_PackageJumpConfig.DiscardUnknown(m)
}

var xxx_messageInfo_PackageJumpConfig proto.InternalMessageInfo

func (m *PackageJumpConfig) GetApp() uint32 {
	if m != nil {
		return m.App
	}
	return 0
}

func (m *PackageJumpConfig) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

func (m *PackageJumpConfig) GetLink() string {
	if m != nil {
		return m.Link
	}
	return ""
}

// 批量发放
type BatchGiveUser struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGiveUser) Reset()         { *m = BatchGiveUser{} }
func (m *BatchGiveUser) String() string { return proto.CompactTextString(m) }
func (*BatchGiveUser) ProtoMessage()    {}
func (*BatchGiveUser) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{4}
}
func (m *BatchGiveUser) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGiveUser.Unmarshal(m, b)
}
func (m *BatchGiveUser) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGiveUser.Marshal(b, m, deterministic)
}
func (dst *BatchGiveUser) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGiveUser.Merge(dst, src)
}
func (m *BatchGiveUser) XXX_Size() int {
	return xxx_messageInfo_BatchGiveUser.Size(m)
}
func (m *BatchGiveUser) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGiveUser.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGiveUser proto.InternalMessageInfo

func (m *BatchGiveUser) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

// 活动配置
type ActivityConfig struct {
	ActivityId           uint32              `protobuf:"varint,1,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	GiftId               uint32              `protobuf:"varint,2,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	StartTime            uint32              `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              uint32              `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Forever              bool                `protobuf:"varint,6,opt,name=forever,proto3" json:"forever,omitempty"`
	Sort                 uint32              `protobuf:"varint,7,opt,name=sort,proto3" json:"sort,omitempty"`
	BackgroundUrl        string              `protobuf:"bytes,8,opt,name=background_url,json=backgroundUrl,proto3" json:"background_url,omitempty"`
	VideoUrl             string              `protobuf:"bytes,9,opt,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty"`
	VideoMd5             string              `protobuf:"bytes,10,opt,name=video_md5,json=videoMd5,proto3" json:"video_md5,omitempty"`
	Remark               string              `protobuf:"bytes,11,opt,name=remark,proto3" json:"remark,omitempty"`
	Operator             string              `protobuf:"bytes,12,opt,name=operator,proto3" json:"operator,omitempty"`
	CreateTime           uint32              `protobuf:"varint,13,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	ActivityPhaseList    []*ActivityPhase    `protobuf:"bytes,14,rep,name=activity_phase_list,json=activityPhaseList,proto3" json:"activity_phase_list,omitempty"`
	UserPermissionInfo   *UserPermissionInfo `protobuf:"bytes,15,opt,name=user_permission_info,json=userPermissionInfo,proto3" json:"user_permission_info,omitempty"`
	Status               ActivityStatus      `protobuf:"varint,16,opt,name=status,proto3,enum=treasure_house.ActivityStatus" json:"status,omitempty"`
	PresentInfo          *PresentInfo        `protobuf:"bytes,17,opt,name=present_info,json=presentInfo,proto3" json:"present_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *ActivityConfig) Reset()         { *m = ActivityConfig{} }
func (m *ActivityConfig) String() string { return proto.CompactTextString(m) }
func (*ActivityConfig) ProtoMessage()    {}
func (*ActivityConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{5}
}
func (m *ActivityConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActivityConfig.Unmarshal(m, b)
}
func (m *ActivityConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActivityConfig.Marshal(b, m, deterministic)
}
func (dst *ActivityConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActivityConfig.Merge(dst, src)
}
func (m *ActivityConfig) XXX_Size() int {
	return xxx_messageInfo_ActivityConfig.Size(m)
}
func (m *ActivityConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_ActivityConfig.DiscardUnknown(m)
}

var xxx_messageInfo_ActivityConfig proto.InternalMessageInfo

func (m *ActivityConfig) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *ActivityConfig) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *ActivityConfig) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *ActivityConfig) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *ActivityConfig) GetForever() bool {
	if m != nil {
		return m.Forever
	}
	return false
}

func (m *ActivityConfig) GetSort() uint32 {
	if m != nil {
		return m.Sort
	}
	return 0
}

func (m *ActivityConfig) GetBackgroundUrl() string {
	if m != nil {
		return m.BackgroundUrl
	}
	return ""
}

func (m *ActivityConfig) GetVideoUrl() string {
	if m != nil {
		return m.VideoUrl
	}
	return ""
}

func (m *ActivityConfig) GetVideoMd5() string {
	if m != nil {
		return m.VideoMd5
	}
	return ""
}

func (m *ActivityConfig) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *ActivityConfig) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *ActivityConfig) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *ActivityConfig) GetActivityPhaseList() []*ActivityPhase {
	if m != nil {
		return m.ActivityPhaseList
	}
	return nil
}

func (m *ActivityConfig) GetUserPermissionInfo() *UserPermissionInfo {
	if m != nil {
		return m.UserPermissionInfo
	}
	return nil
}

func (m *ActivityConfig) GetStatus() ActivityStatus {
	if m != nil {
		return m.Status
	}
	return ActivityStatus_ACTIVITY_STATUS_UNSPECIFIED
}

func (m *ActivityConfig) GetPresentInfo() *PresentInfo {
	if m != nil {
		return m.PresentInfo
	}
	return nil
}

// 活动阶段配置
type ActivityPhase struct {
	ActivityId           uint32               `protobuf:"varint,1,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	PhaseId              uint32               `protobuf:"varint,2,opt,name=phase_id,json=phaseId,proto3" json:"phase_id,omitempty"`
	PhaseType            PhaseType            `protobuf:"varint,3,opt,name=phase_type,json=phaseType,proto3,enum=treasure_house.PhaseType" json:"phase_type,omitempty"`
	StartTime            uint32               `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              uint32               `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Forever              bool                 `protobuf:"varint,6,opt,name=forever,proto3" json:"forever,omitempty"`
	Price                uint64               `protobuf:"varint,7,opt,name=price,proto3" json:"price,omitempty"`
	DiscountPrice        uint64               `protobuf:"varint,8,opt,name=discount_price,json=discountPrice,proto3" json:"discount_price,omitempty"`
	ButtonAction         ButtonAction         `protobuf:"varint,9,opt,name=button_action,json=buttonAction,proto3,enum=treasure_house.ButtonAction" json:"button_action,omitempty"`
	ButtonLink           string               `protobuf:"bytes,10,opt,name=button_link,json=buttonLink,proto3" json:"button_link,omitempty"`
	ButtonTopText        string               `protobuf:"bytes,11,opt,name=button_top_text,json=buttonTopText,proto3" json:"button_top_text,omitempty"`
	InterrogationText    string               `protobuf:"bytes,12,opt,name=interrogation_text,json=interrogationText,proto3" json:"interrogation_text,omitempty"`
	MsgBoxTitle          string               `protobuf:"bytes,13,opt,name=msg_box_title,json=msgBoxTitle,proto3" json:"msg_box_title,omitempty"`
	MsgBoxContent        string               `protobuf:"bytes,14,opt,name=msg_box_content,json=msgBoxContent,proto3" json:"msg_box_content,omitempty"`
	GetMethod            PresentGetMethod     `protobuf:"varint,15,opt,name=get_method,json=getMethod,proto3,enum=treasure_house.PresentGetMethod" json:"get_method,omitempty"`
	ValidTimeType        PresentValidTimeType `protobuf:"varint,16,opt,name=valid_time_type,json=validTimeType,proto3,enum=treasure_house.PresentValidTimeType" json:"valid_time_type,omitempty"`
	ValidTimeDay         uint32               `protobuf:"varint,17,opt,name=valid_time_day,json=validTimeDay,proto3" json:"valid_time_day,omitempty"`
	ValidTimeHour        uint32               `protobuf:"varint,18,opt,name=valid_time_hour,json=validTimeHour,proto3" json:"valid_time_hour,omitempty"`
	ValidTimeDeadline    uint32               `protobuf:"varint,19,opt,name=valid_time_deadline,json=validTimeDeadline,proto3" json:"valid_time_deadline,omitempty"`
	ConditionList        []*Condition         `protobuf:"bytes,20,rep,name=condition_list,json=conditionList,proto3" json:"condition_list,omitempty"`
	StatStartTime        uint32               `protobuf:"varint,21,opt,name=stat_start_time,json=statStartTime,proto3" json:"stat_start_time,omitempty"`
	StatEndTime          uint32               `protobuf:"varint,22,opt,name=stat_end_time,json=statEndTime,proto3" json:"stat_end_time,omitempty"`
	TopUpAmount          uint64               `protobuf:"varint,23,opt,name=top_up_amount,json=topUpAmount,proto3" json:"top_up_amount,omitempty"`
	ValidTimeSeconds     uint32               `protobuf:"varint,25,opt,name=valid_time_seconds,json=validTimeSeconds,proto3" json:"valid_time_seconds,omitempty"`
	PurchasedCount       uint32               `protobuf:"varint,26,opt,name=purchased_count,json=purchasedCount,proto3" json:"purchased_count,omitempty"`
	PackageJumpConfig    []*PackageJumpConfig `protobuf:"bytes,27,rep,name=package_jump_config,json=packageJumpConfig,proto3" json:"package_jump_config,omitempty"`
	BatchGiveUserList    []*BatchGiveUser     `protobuf:"bytes,28,rep,name=batch_give_user_list,json=batchGiveUserList,proto3" json:"batch_give_user_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *ActivityPhase) Reset()         { *m = ActivityPhase{} }
func (m *ActivityPhase) String() string { return proto.CompactTextString(m) }
func (*ActivityPhase) ProtoMessage()    {}
func (*ActivityPhase) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{6}
}
func (m *ActivityPhase) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActivityPhase.Unmarshal(m, b)
}
func (m *ActivityPhase) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActivityPhase.Marshal(b, m, deterministic)
}
func (dst *ActivityPhase) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActivityPhase.Merge(dst, src)
}
func (m *ActivityPhase) XXX_Size() int {
	return xxx_messageInfo_ActivityPhase.Size(m)
}
func (m *ActivityPhase) XXX_DiscardUnknown() {
	xxx_messageInfo_ActivityPhase.DiscardUnknown(m)
}

var xxx_messageInfo_ActivityPhase proto.InternalMessageInfo

func (m *ActivityPhase) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *ActivityPhase) GetPhaseId() uint32 {
	if m != nil {
		return m.PhaseId
	}
	return 0
}

func (m *ActivityPhase) GetPhaseType() PhaseType {
	if m != nil {
		return m.PhaseType
	}
	return PhaseType_PHASE_TYPE_UNSPECIFIED
}

func (m *ActivityPhase) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *ActivityPhase) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *ActivityPhase) GetForever() bool {
	if m != nil {
		return m.Forever
	}
	return false
}

func (m *ActivityPhase) GetPrice() uint64 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *ActivityPhase) GetDiscountPrice() uint64 {
	if m != nil {
		return m.DiscountPrice
	}
	return 0
}

func (m *ActivityPhase) GetButtonAction() ButtonAction {
	if m != nil {
		return m.ButtonAction
	}
	return ButtonAction_BUTTON_ACTION_UNSPECIFIED
}

func (m *ActivityPhase) GetButtonLink() string {
	if m != nil {
		return m.ButtonLink
	}
	return ""
}

func (m *ActivityPhase) GetButtonTopText() string {
	if m != nil {
		return m.ButtonTopText
	}
	return ""
}

func (m *ActivityPhase) GetInterrogationText() string {
	if m != nil {
		return m.InterrogationText
	}
	return ""
}

func (m *ActivityPhase) GetMsgBoxTitle() string {
	if m != nil {
		return m.MsgBoxTitle
	}
	return ""
}

func (m *ActivityPhase) GetMsgBoxContent() string {
	if m != nil {
		return m.MsgBoxContent
	}
	return ""
}

func (m *ActivityPhase) GetGetMethod() PresentGetMethod {
	if m != nil {
		return m.GetMethod
	}
	return PresentGetMethod_PRESENT_GET_METHOD_UNSPECIFIED
}

func (m *ActivityPhase) GetValidTimeType() PresentValidTimeType {
	if m != nil {
		return m.ValidTimeType
	}
	return PresentValidTimeType_PRESENT_VALID_TIME_TYPE_UNSPECIFIED
}

func (m *ActivityPhase) GetValidTimeDay() uint32 {
	if m != nil {
		return m.ValidTimeDay
	}
	return 0
}

func (m *ActivityPhase) GetValidTimeHour() uint32 {
	if m != nil {
		return m.ValidTimeHour
	}
	return 0
}

func (m *ActivityPhase) GetValidTimeDeadline() uint32 {
	if m != nil {
		return m.ValidTimeDeadline
	}
	return 0
}

func (m *ActivityPhase) GetConditionList() []*Condition {
	if m != nil {
		return m.ConditionList
	}
	return nil
}

func (m *ActivityPhase) GetStatStartTime() uint32 {
	if m != nil {
		return m.StatStartTime
	}
	return 0
}

func (m *ActivityPhase) GetStatEndTime() uint32 {
	if m != nil {
		return m.StatEndTime
	}
	return 0
}

func (m *ActivityPhase) GetTopUpAmount() uint64 {
	if m != nil {
		return m.TopUpAmount
	}
	return 0
}

func (m *ActivityPhase) GetValidTimeSeconds() uint32 {
	if m != nil {
		return m.ValidTimeSeconds
	}
	return 0
}

func (m *ActivityPhase) GetPurchasedCount() uint32 {
	if m != nil {
		return m.PurchasedCount
	}
	return 0
}

func (m *ActivityPhase) GetPackageJumpConfig() []*PackageJumpConfig {
	if m != nil {
		return m.PackageJumpConfig
	}
	return nil
}

func (m *ActivityPhase) GetBatchGiveUserList() []*BatchGiveUser {
	if m != nil {
		return m.BatchGiveUserList
	}
	return nil
}

// 用户权限信息
type UserPermissionInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PresentPermission    uint32   `protobuf:"varint,2,opt,name=present_permission,json=presentPermission,proto3" json:"present_permission,omitempty"`
	PresentPrivilege     uint32   `protobuf:"varint,3,opt,name=present_privilege,json=presentPrivilege,proto3" json:"present_privilege,omitempty"`
	TopUpProgress        uint64   `protobuf:"varint,4,opt,name=top_up_progress,json=topUpProgress,proto3" json:"top_up_progress,omitempty"`
	ExpireTime           uint32   `protobuf:"varint,5,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserPermissionInfo) Reset()         { *m = UserPermissionInfo{} }
func (m *UserPermissionInfo) String() string { return proto.CompactTextString(m) }
func (*UserPermissionInfo) ProtoMessage()    {}
func (*UserPermissionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{7}
}
func (m *UserPermissionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserPermissionInfo.Unmarshal(m, b)
}
func (m *UserPermissionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserPermissionInfo.Marshal(b, m, deterministic)
}
func (dst *UserPermissionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserPermissionInfo.Merge(dst, src)
}
func (m *UserPermissionInfo) XXX_Size() int {
	return xxx_messageInfo_UserPermissionInfo.Size(m)
}
func (m *UserPermissionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserPermissionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserPermissionInfo proto.InternalMessageInfo

func (m *UserPermissionInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserPermissionInfo) GetPresentPermission() uint32 {
	if m != nil {
		return m.PresentPermission
	}
	return 0
}

func (m *UserPermissionInfo) GetPresentPrivilege() uint32 {
	if m != nil {
		return m.PresentPrivilege
	}
	return 0
}

func (m *UserPermissionInfo) GetTopUpProgress() uint64 {
	if m != nil {
		return m.TopUpProgress
	}
	return 0
}

func (m *UserPermissionInfo) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

type GetActivityConfigListReq struct {
	Offset               uint32         `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32         `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Uid                  uint32         `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	Status               ActivityStatus `protobuf:"varint,4,opt,name=status,proto3,enum=treasure_house.ActivityStatus" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetActivityConfigListReq) Reset()         { *m = GetActivityConfigListReq{} }
func (m *GetActivityConfigListReq) String() string { return proto.CompactTextString(m) }
func (*GetActivityConfigListReq) ProtoMessage()    {}
func (*GetActivityConfigListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{8}
}
func (m *GetActivityConfigListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActivityConfigListReq.Unmarshal(m, b)
}
func (m *GetActivityConfigListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActivityConfigListReq.Marshal(b, m, deterministic)
}
func (dst *GetActivityConfigListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActivityConfigListReq.Merge(dst, src)
}
func (m *GetActivityConfigListReq) XXX_Size() int {
	return xxx_messageInfo_GetActivityConfigListReq.Size(m)
}
func (m *GetActivityConfigListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActivityConfigListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetActivityConfigListReq proto.InternalMessageInfo

func (m *GetActivityConfigListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetActivityConfigListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetActivityConfigListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetActivityConfigListReq) GetStatus() ActivityStatus {
	if m != nil {
		return m.Status
	}
	return ActivityStatus_ACTIVITY_STATUS_UNSPECIFIED
}

type GetActivityConfigListResp struct {
	ActivityConfigList   []*ActivityConfig `protobuf:"bytes,1,rep,name=activity_config_list,json=activityConfigList,proto3" json:"activity_config_list,omitempty"`
	Total                uint32            `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetActivityConfigListResp) Reset()         { *m = GetActivityConfigListResp{} }
func (m *GetActivityConfigListResp) String() string { return proto.CompactTextString(m) }
func (*GetActivityConfigListResp) ProtoMessage()    {}
func (*GetActivityConfigListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{9}
}
func (m *GetActivityConfigListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActivityConfigListResp.Unmarshal(m, b)
}
func (m *GetActivityConfigListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActivityConfigListResp.Marshal(b, m, deterministic)
}
func (dst *GetActivityConfigListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActivityConfigListResp.Merge(dst, src)
}
func (m *GetActivityConfigListResp) XXX_Size() int {
	return xxx_messageInfo_GetActivityConfigListResp.Size(m)
}
func (m *GetActivityConfigListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActivityConfigListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetActivityConfigListResp proto.InternalMessageInfo

func (m *GetActivityConfigListResp) GetActivityConfigList() []*ActivityConfig {
	if m != nil {
		return m.ActivityConfigList
	}
	return nil
}

func (m *GetActivityConfigListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetActivityConfigReq struct {
	ActivityId           uint32   `protobuf:"varint,1,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetActivityConfigReq) Reset()         { *m = GetActivityConfigReq{} }
func (m *GetActivityConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetActivityConfigReq) ProtoMessage()    {}
func (*GetActivityConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{10}
}
func (m *GetActivityConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActivityConfigReq.Unmarshal(m, b)
}
func (m *GetActivityConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActivityConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetActivityConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActivityConfigReq.Merge(dst, src)
}
func (m *GetActivityConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetActivityConfigReq.Size(m)
}
func (m *GetActivityConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActivityConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetActivityConfigReq proto.InternalMessageInfo

func (m *GetActivityConfigReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *GetActivityConfigReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetActivityConfigResp struct {
	ActivityConfig       *ActivityConfig `protobuf:"bytes,1,opt,name=activity_config,json=activityConfig,proto3" json:"activity_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetActivityConfigResp) Reset()         { *m = GetActivityConfigResp{} }
func (m *GetActivityConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetActivityConfigResp) ProtoMessage()    {}
func (*GetActivityConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{11}
}
func (m *GetActivityConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActivityConfigResp.Unmarshal(m, b)
}
func (m *GetActivityConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActivityConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetActivityConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActivityConfigResp.Merge(dst, src)
}
func (m *GetActivityConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetActivityConfigResp.Size(m)
}
func (m *GetActivityConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActivityConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetActivityConfigResp proto.InternalMessageInfo

func (m *GetActivityConfigResp) GetActivityConfig() *ActivityConfig {
	if m != nil {
		return m.ActivityConfig
	}
	return nil
}

type CreateActivityConfigReq struct {
	ActivityConfig       *ActivityConfig `protobuf:"bytes,1,opt,name=activity_config,json=activityConfig,proto3" json:"activity_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *CreateActivityConfigReq) Reset()         { *m = CreateActivityConfigReq{} }
func (m *CreateActivityConfigReq) String() string { return proto.CompactTextString(m) }
func (*CreateActivityConfigReq) ProtoMessage()    {}
func (*CreateActivityConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{12}
}
func (m *CreateActivityConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateActivityConfigReq.Unmarshal(m, b)
}
func (m *CreateActivityConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateActivityConfigReq.Marshal(b, m, deterministic)
}
func (dst *CreateActivityConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateActivityConfigReq.Merge(dst, src)
}
func (m *CreateActivityConfigReq) XXX_Size() int {
	return xxx_messageInfo_CreateActivityConfigReq.Size(m)
}
func (m *CreateActivityConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateActivityConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateActivityConfigReq proto.InternalMessageInfo

func (m *CreateActivityConfigReq) GetActivityConfig() *ActivityConfig {
	if m != nil {
		return m.ActivityConfig
	}
	return nil
}

type CreateActivityConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateActivityConfigResp) Reset()         { *m = CreateActivityConfigResp{} }
func (m *CreateActivityConfigResp) String() string { return proto.CompactTextString(m) }
func (*CreateActivityConfigResp) ProtoMessage()    {}
func (*CreateActivityConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{13}
}
func (m *CreateActivityConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateActivityConfigResp.Unmarshal(m, b)
}
func (m *CreateActivityConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateActivityConfigResp.Marshal(b, m, deterministic)
}
func (dst *CreateActivityConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateActivityConfigResp.Merge(dst, src)
}
func (m *CreateActivityConfigResp) XXX_Size() int {
	return xxx_messageInfo_CreateActivityConfigResp.Size(m)
}
func (m *CreateActivityConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateActivityConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateActivityConfigResp proto.InternalMessageInfo

type DeleteActivityConfigReq struct {
	ActivityId           uint32   `protobuf:"varint,1,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	Operator             string   `protobuf:"bytes,2,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteActivityConfigReq) Reset()         { *m = DeleteActivityConfigReq{} }
func (m *DeleteActivityConfigReq) String() string { return proto.CompactTextString(m) }
func (*DeleteActivityConfigReq) ProtoMessage()    {}
func (*DeleteActivityConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{14}
}
func (m *DeleteActivityConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteActivityConfigReq.Unmarshal(m, b)
}
func (m *DeleteActivityConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteActivityConfigReq.Marshal(b, m, deterministic)
}
func (dst *DeleteActivityConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteActivityConfigReq.Merge(dst, src)
}
func (m *DeleteActivityConfigReq) XXX_Size() int {
	return xxx_messageInfo_DeleteActivityConfigReq.Size(m)
}
func (m *DeleteActivityConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteActivityConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteActivityConfigReq proto.InternalMessageInfo

func (m *DeleteActivityConfigReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *DeleteActivityConfigReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type DeleteActivityConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteActivityConfigResp) Reset()         { *m = DeleteActivityConfigResp{} }
func (m *DeleteActivityConfigResp) String() string { return proto.CompactTextString(m) }
func (*DeleteActivityConfigResp) ProtoMessage()    {}
func (*DeleteActivityConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{15}
}
func (m *DeleteActivityConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteActivityConfigResp.Unmarshal(m, b)
}
func (m *DeleteActivityConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteActivityConfigResp.Marshal(b, m, deterministic)
}
func (dst *DeleteActivityConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteActivityConfigResp.Merge(dst, src)
}
func (m *DeleteActivityConfigResp) XXX_Size() int {
	return xxx_messageInfo_DeleteActivityConfigResp.Size(m)
}
func (m *DeleteActivityConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteActivityConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteActivityConfigResp proto.InternalMessageInfo

type ClaimPresentPermissionReq struct {
	ActivityId           uint32    `protobuf:"varint,1,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	PhaseType            PhaseType `protobuf:"varint,2,opt,name=phase_type,json=phaseType,proto3,enum=treasure_house.PhaseType" json:"phase_type,omitempty"`
	Uid                  uint32    `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *ClaimPresentPermissionReq) Reset()         { *m = ClaimPresentPermissionReq{} }
func (m *ClaimPresentPermissionReq) String() string { return proto.CompactTextString(m) }
func (*ClaimPresentPermissionReq) ProtoMessage()    {}
func (*ClaimPresentPermissionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{16}
}
func (m *ClaimPresentPermissionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClaimPresentPermissionReq.Unmarshal(m, b)
}
func (m *ClaimPresentPermissionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClaimPresentPermissionReq.Marshal(b, m, deterministic)
}
func (dst *ClaimPresentPermissionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClaimPresentPermissionReq.Merge(dst, src)
}
func (m *ClaimPresentPermissionReq) XXX_Size() int {
	return xxx_messageInfo_ClaimPresentPermissionReq.Size(m)
}
func (m *ClaimPresentPermissionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ClaimPresentPermissionReq.DiscardUnknown(m)
}

var xxx_messageInfo_ClaimPresentPermissionReq proto.InternalMessageInfo

func (m *ClaimPresentPermissionReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *ClaimPresentPermissionReq) GetPhaseType() PhaseType {
	if m != nil {
		return m.PhaseType
	}
	return PhaseType_PHASE_TYPE_UNSPECIFIED
}

func (m *ClaimPresentPermissionReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type ClaimPresentPermissionResp struct {
	ActivityConfig       *ActivityConfig `protobuf:"bytes,1,opt,name=activity_config,json=activityConfig,proto3" json:"activity_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ClaimPresentPermissionResp) Reset()         { *m = ClaimPresentPermissionResp{} }
func (m *ClaimPresentPermissionResp) String() string { return proto.CompactTextString(m) }
func (*ClaimPresentPermissionResp) ProtoMessage()    {}
func (*ClaimPresentPermissionResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{17}
}
func (m *ClaimPresentPermissionResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClaimPresentPermissionResp.Unmarshal(m, b)
}
func (m *ClaimPresentPermissionResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClaimPresentPermissionResp.Marshal(b, m, deterministic)
}
func (dst *ClaimPresentPermissionResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClaimPresentPermissionResp.Merge(dst, src)
}
func (m *ClaimPresentPermissionResp) XXX_Size() int {
	return xxx_messageInfo_ClaimPresentPermissionResp.Size(m)
}
func (m *ClaimPresentPermissionResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ClaimPresentPermissionResp.DiscardUnknown(m)
}

var xxx_messageInfo_ClaimPresentPermissionResp proto.InternalMessageInfo

func (m *ClaimPresentPermissionResp) GetActivityConfig() *ActivityConfig {
	if m != nil {
		return m.ActivityConfig
	}
	return nil
}

type BuyPresentPermissionReq struct {
	ActivityId           uint32    `protobuf:"varint,1,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	PhaseType            PhaseType `protobuf:"varint,2,opt,name=phase_type,json=phaseType,proto3,enum=treasure_house.PhaseType" json:"phase_type,omitempty"`
	Uid                  uint32    `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *BuyPresentPermissionReq) Reset()         { *m = BuyPresentPermissionReq{} }
func (m *BuyPresentPermissionReq) String() string { return proto.CompactTextString(m) }
func (*BuyPresentPermissionReq) ProtoMessage()    {}
func (*BuyPresentPermissionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{18}
}
func (m *BuyPresentPermissionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuyPresentPermissionReq.Unmarshal(m, b)
}
func (m *BuyPresentPermissionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuyPresentPermissionReq.Marshal(b, m, deterministic)
}
func (dst *BuyPresentPermissionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuyPresentPermissionReq.Merge(dst, src)
}
func (m *BuyPresentPermissionReq) XXX_Size() int {
	return xxx_messageInfo_BuyPresentPermissionReq.Size(m)
}
func (m *BuyPresentPermissionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BuyPresentPermissionReq.DiscardUnknown(m)
}

var xxx_messageInfo_BuyPresentPermissionReq proto.InternalMessageInfo

func (m *BuyPresentPermissionReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *BuyPresentPermissionReq) GetPhaseType() PhaseType {
	if m != nil {
		return m.PhaseType
	}
	return PhaseType_PHASE_TYPE_UNSPECIFIED
}

func (m *BuyPresentPermissionReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type BuyPresentPermissionResp struct {
	ActivityConfig       *ActivityConfig `protobuf:"bytes,1,opt,name=activity_config,json=activityConfig,proto3" json:"activity_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BuyPresentPermissionResp) Reset()         { *m = BuyPresentPermissionResp{} }
func (m *BuyPresentPermissionResp) String() string { return proto.CompactTextString(m) }
func (*BuyPresentPermissionResp) ProtoMessage()    {}
func (*BuyPresentPermissionResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{19}
}
func (m *BuyPresentPermissionResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuyPresentPermissionResp.Unmarshal(m, b)
}
func (m *BuyPresentPermissionResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuyPresentPermissionResp.Marshal(b, m, deterministic)
}
func (dst *BuyPresentPermissionResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuyPresentPermissionResp.Merge(dst, src)
}
func (m *BuyPresentPermissionResp) XXX_Size() int {
	return xxx_messageInfo_BuyPresentPermissionResp.Size(m)
}
func (m *BuyPresentPermissionResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BuyPresentPermissionResp.DiscardUnknown(m)
}

var xxx_messageInfo_BuyPresentPermissionResp proto.InternalMessageInfo

func (m *BuyPresentPermissionResp) GetActivityConfig() *ActivityConfig {
	if m != nil {
		return m.ActivityConfig
	}
	return nil
}

type GiveActivityTreasurePresentReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TreasureId           uint32   `protobuf:"varint,2,opt,name=treasure_id,json=treasureId,proto3" json:"treasure_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GiveActivityTreasurePresentReq) Reset()         { *m = GiveActivityTreasurePresentReq{} }
func (m *GiveActivityTreasurePresentReq) String() string { return proto.CompactTextString(m) }
func (*GiveActivityTreasurePresentReq) ProtoMessage()    {}
func (*GiveActivityTreasurePresentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{20}
}
func (m *GiveActivityTreasurePresentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GiveActivityTreasurePresentReq.Unmarshal(m, b)
}
func (m *GiveActivityTreasurePresentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GiveActivityTreasurePresentReq.Marshal(b, m, deterministic)
}
func (dst *GiveActivityTreasurePresentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GiveActivityTreasurePresentReq.Merge(dst, src)
}
func (m *GiveActivityTreasurePresentReq) XXX_Size() int {
	return xxx_messageInfo_GiveActivityTreasurePresentReq.Size(m)
}
func (m *GiveActivityTreasurePresentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GiveActivityTreasurePresentReq.DiscardUnknown(m)
}

var xxx_messageInfo_GiveActivityTreasurePresentReq proto.InternalMessageInfo

func (m *GiveActivityTreasurePresentReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GiveActivityTreasurePresentReq) GetTreasureId() uint32 {
	if m != nil {
		return m.TreasureId
	}
	return 0
}

type GiveActivityTreasurePresentResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GiveActivityTreasurePresentResp) Reset()         { *m = GiveActivityTreasurePresentResp{} }
func (m *GiveActivityTreasurePresentResp) String() string { return proto.CompactTextString(m) }
func (*GiveActivityTreasurePresentResp) ProtoMessage()    {}
func (*GiveActivityTreasurePresentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{21}
}
func (m *GiveActivityTreasurePresentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GiveActivityTreasurePresentResp.Unmarshal(m, b)
}
func (m *GiveActivityTreasurePresentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GiveActivityTreasurePresentResp.Marshal(b, m, deterministic)
}
func (dst *GiveActivityTreasurePresentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GiveActivityTreasurePresentResp.Merge(dst, src)
}
func (m *GiveActivityTreasurePresentResp) XXX_Size() int {
	return xxx_messageInfo_GiveActivityTreasurePresentResp.Size(m)
}
func (m *GiveActivityTreasurePresentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GiveActivityTreasurePresentResp.DiscardUnknown(m)
}

var xxx_messageInfo_GiveActivityTreasurePresentResp proto.InternalMessageInfo

type CheckActivityPresentStatusReq struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	ActivityStartTime    uint32   `protobuf:"varint,2,opt,name=activity_start_time,json=activityStartTime,proto3" json:"activity_start_time,omitempty"`
	ActivityEndTime      uint32   `protobuf:"varint,3,opt,name=activity_end_time,json=activityEndTime,proto3" json:"activity_end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckActivityPresentStatusReq) Reset()         { *m = CheckActivityPresentStatusReq{} }
func (m *CheckActivityPresentStatusReq) String() string { return proto.CompactTextString(m) }
func (*CheckActivityPresentStatusReq) ProtoMessage()    {}
func (*CheckActivityPresentStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{22}
}
func (m *CheckActivityPresentStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckActivityPresentStatusReq.Unmarshal(m, b)
}
func (m *CheckActivityPresentStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckActivityPresentStatusReq.Marshal(b, m, deterministic)
}
func (dst *CheckActivityPresentStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckActivityPresentStatusReq.Merge(dst, src)
}
func (m *CheckActivityPresentStatusReq) XXX_Size() int {
	return xxx_messageInfo_CheckActivityPresentStatusReq.Size(m)
}
func (m *CheckActivityPresentStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckActivityPresentStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckActivityPresentStatusReq proto.InternalMessageInfo

func (m *CheckActivityPresentStatusReq) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *CheckActivityPresentStatusReq) GetActivityStartTime() uint32 {
	if m != nil {
		return m.ActivityStartTime
	}
	return 0
}

func (m *CheckActivityPresentStatusReq) GetActivityEndTime() uint32 {
	if m != nil {
		return m.ActivityEndTime
	}
	return 0
}

type CheckActivityPresentStatusResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckActivityPresentStatusResp) Reset()         { *m = CheckActivityPresentStatusResp{} }
func (m *CheckActivityPresentStatusResp) String() string { return proto.CompactTextString(m) }
func (*CheckActivityPresentStatusResp) ProtoMessage()    {}
func (*CheckActivityPresentStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{23}
}
func (m *CheckActivityPresentStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckActivityPresentStatusResp.Unmarshal(m, b)
}
func (m *CheckActivityPresentStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckActivityPresentStatusResp.Marshal(b, m, deterministic)
}
func (dst *CheckActivityPresentStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckActivityPresentStatusResp.Merge(dst, src)
}
func (m *CheckActivityPresentStatusResp) XXX_Size() int {
	return xxx_messageInfo_CheckActivityPresentStatusResp.Size(m)
}
func (m *CheckActivityPresentStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckActivityPresentStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckActivityPresentStatusResp proto.InternalMessageInfo

type GetActivityUpdateInfoReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetActivityUpdateInfoReq) Reset()         { *m = GetActivityUpdateInfoReq{} }
func (m *GetActivityUpdateInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetActivityUpdateInfoReq) ProtoMessage()    {}
func (*GetActivityUpdateInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{24}
}
func (m *GetActivityUpdateInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActivityUpdateInfoReq.Unmarshal(m, b)
}
func (m *GetActivityUpdateInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActivityUpdateInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetActivityUpdateInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActivityUpdateInfoReq.Merge(dst, src)
}
func (m *GetActivityUpdateInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetActivityUpdateInfoReq.Size(m)
}
func (m *GetActivityUpdateInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActivityUpdateInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetActivityUpdateInfoReq proto.InternalMessageInfo

type GetActivityUpdateInfoResp struct {
	ShowMoreFloatLayout    bool     `protobuf:"varint,1,opt,name=show_more_float_layout,json=showMoreFloatLayout,proto3" json:"show_more_float_layout,omitempty"`
	MoreFloatLayoutText    string   `protobuf:"bytes,2,opt,name=more_float_layout_text,json=moreFloatLayoutText,proto3" json:"more_float_layout_text,omitempty"`
	MoreFloatLayoutSeconds uint32   `protobuf:"varint,3,opt,name=more_float_layout_seconds,json=moreFloatLayoutSeconds,proto3" json:"more_float_layout_seconds,omitempty"`
	ShowPresentEntrance    bool     `protobuf:"varint,4,opt,name=show_present_entrance,json=showPresentEntrance,proto3" json:"show_present_entrance,omitempty"`
	PresentEntranceText    string   `protobuf:"bytes,5,opt,name=present_entrance_text,json=presentEntranceText,proto3" json:"present_entrance_text,omitempty"`
	PresentEntranceSeconds uint32   `protobuf:"varint,6,opt,name=present_entrance_seconds,json=presentEntranceSeconds,proto3" json:"present_entrance_seconds,omitempty"`
	ShowChannelMsg         bool     `protobuf:"varint,7,opt,name=show_channel_msg,json=showChannelMsg,proto3" json:"show_channel_msg,omitempty"`
	ChannelMsgText         string   `protobuf:"bytes,8,opt,name=channel_msg_text,json=channelMsgText,proto3" json:"channel_msg_text,omitempty"`
	ChannelMsgColor        string   `protobuf:"bytes,9,opt,name=channel_msg_color,json=channelMsgColor,proto3" json:"channel_msg_color,omitempty"`
	ShowRedDot             bool     `protobuf:"varint,10,opt,name=show_red_dot,json=showRedDot,proto3" json:"show_red_dot,omitempty"`
	RemindStartTime        uint32   `protobuf:"varint,11,opt,name=remind_start_time,json=remindStartTime,proto3" json:"remind_start_time,omitempty"`
	RemindEndTime          uint32   `protobuf:"varint,12,opt,name=remind_end_time,json=remindEndTime,proto3" json:"remind_end_time,omitempty"`
	LastUpdateTime         uint32   `protobuf:"varint,13,opt,name=last_update_time,json=lastUpdateTime,proto3" json:"last_update_time,omitempty"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *GetActivityUpdateInfoResp) Reset()         { *m = GetActivityUpdateInfoResp{} }
func (m *GetActivityUpdateInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetActivityUpdateInfoResp) ProtoMessage()    {}
func (*GetActivityUpdateInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{25}
}
func (m *GetActivityUpdateInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActivityUpdateInfoResp.Unmarshal(m, b)
}
func (m *GetActivityUpdateInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActivityUpdateInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetActivityUpdateInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActivityUpdateInfoResp.Merge(dst, src)
}
func (m *GetActivityUpdateInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetActivityUpdateInfoResp.Size(m)
}
func (m *GetActivityUpdateInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActivityUpdateInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetActivityUpdateInfoResp proto.InternalMessageInfo

func (m *GetActivityUpdateInfoResp) GetShowMoreFloatLayout() bool {
	if m != nil {
		return m.ShowMoreFloatLayout
	}
	return false
}

func (m *GetActivityUpdateInfoResp) GetMoreFloatLayoutText() string {
	if m != nil {
		return m.MoreFloatLayoutText
	}
	return ""
}

func (m *GetActivityUpdateInfoResp) GetMoreFloatLayoutSeconds() uint32 {
	if m != nil {
		return m.MoreFloatLayoutSeconds
	}
	return 0
}

func (m *GetActivityUpdateInfoResp) GetShowPresentEntrance() bool {
	if m != nil {
		return m.ShowPresentEntrance
	}
	return false
}

func (m *GetActivityUpdateInfoResp) GetPresentEntranceText() string {
	if m != nil {
		return m.PresentEntranceText
	}
	return ""
}

func (m *GetActivityUpdateInfoResp) GetPresentEntranceSeconds() uint32 {
	if m != nil {
		return m.PresentEntranceSeconds
	}
	return 0
}

func (m *GetActivityUpdateInfoResp) GetShowChannelMsg() bool {
	if m != nil {
		return m.ShowChannelMsg
	}
	return false
}

func (m *GetActivityUpdateInfoResp) GetChannelMsgText() string {
	if m != nil {
		return m.ChannelMsgText
	}
	return ""
}

func (m *GetActivityUpdateInfoResp) GetChannelMsgColor() string {
	if m != nil {
		return m.ChannelMsgColor
	}
	return ""
}

func (m *GetActivityUpdateInfoResp) GetShowRedDot() bool {
	if m != nil {
		return m.ShowRedDot
	}
	return false
}

func (m *GetActivityUpdateInfoResp) GetRemindStartTime() uint32 {
	if m != nil {
		return m.RemindStartTime
	}
	return 0
}

func (m *GetActivityUpdateInfoResp) GetRemindEndTime() uint32 {
	if m != nil {
		return m.RemindEndTime
	}
	return 0
}

func (m *GetActivityUpdateInfoResp) GetLastUpdateTime() uint32 {
	if m != nil {
		return m.LastUpdateTime
	}
	return 0
}

type SetActivityUpdateInfoReq struct {
	ShowMoreFloatLayout    bool     `protobuf:"varint,1,opt,name=show_more_float_layout,json=showMoreFloatLayout,proto3" json:"show_more_float_layout,omitempty"`
	MoreFloatLayoutText    string   `protobuf:"bytes,2,opt,name=more_float_layout_text,json=moreFloatLayoutText,proto3" json:"more_float_layout_text,omitempty"`
	MoreFloatLayoutSeconds uint32   `protobuf:"varint,3,opt,name=more_float_layout_seconds,json=moreFloatLayoutSeconds,proto3" json:"more_float_layout_seconds,omitempty"`
	ShowPresentEntrance    bool     `protobuf:"varint,4,opt,name=show_present_entrance,json=showPresentEntrance,proto3" json:"show_present_entrance,omitempty"`
	PresentEntranceText    string   `protobuf:"bytes,5,opt,name=present_entrance_text,json=presentEntranceText,proto3" json:"present_entrance_text,omitempty"`
	PresentEntranceSeconds uint32   `protobuf:"varint,6,opt,name=present_entrance_seconds,json=presentEntranceSeconds,proto3" json:"present_entrance_seconds,omitempty"`
	ShowChannelMsg         bool     `protobuf:"varint,7,opt,name=show_channel_msg,json=showChannelMsg,proto3" json:"show_channel_msg,omitempty"`
	ChannelMsgText         string   `protobuf:"bytes,8,opt,name=channel_msg_text,json=channelMsgText,proto3" json:"channel_msg_text,omitempty"`
	ChannelMsgColor        string   `protobuf:"bytes,9,opt,name=channel_msg_color,json=channelMsgColor,proto3" json:"channel_msg_color,omitempty"`
	ShowRedDot             bool     `protobuf:"varint,10,opt,name=show_red_dot,json=showRedDot,proto3" json:"show_red_dot,omitempty"`
	RemindStartTime        uint32   `protobuf:"varint,11,opt,name=remind_start_time,json=remindStartTime,proto3" json:"remind_start_time,omitempty"`
	RemindEndTime          uint32   `protobuf:"varint,12,opt,name=remind_end_time,json=remindEndTime,proto3" json:"remind_end_time,omitempty"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *SetActivityUpdateInfoReq) Reset()         { *m = SetActivityUpdateInfoReq{} }
func (m *SetActivityUpdateInfoReq) String() string { return proto.CompactTextString(m) }
func (*SetActivityUpdateInfoReq) ProtoMessage()    {}
func (*SetActivityUpdateInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{26}
}
func (m *SetActivityUpdateInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetActivityUpdateInfoReq.Unmarshal(m, b)
}
func (m *SetActivityUpdateInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetActivityUpdateInfoReq.Marshal(b, m, deterministic)
}
func (dst *SetActivityUpdateInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetActivityUpdateInfoReq.Merge(dst, src)
}
func (m *SetActivityUpdateInfoReq) XXX_Size() int {
	return xxx_messageInfo_SetActivityUpdateInfoReq.Size(m)
}
func (m *SetActivityUpdateInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetActivityUpdateInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetActivityUpdateInfoReq proto.InternalMessageInfo

func (m *SetActivityUpdateInfoReq) GetShowMoreFloatLayout() bool {
	if m != nil {
		return m.ShowMoreFloatLayout
	}
	return false
}

func (m *SetActivityUpdateInfoReq) GetMoreFloatLayoutText() string {
	if m != nil {
		return m.MoreFloatLayoutText
	}
	return ""
}

func (m *SetActivityUpdateInfoReq) GetMoreFloatLayoutSeconds() uint32 {
	if m != nil {
		return m.MoreFloatLayoutSeconds
	}
	return 0
}

func (m *SetActivityUpdateInfoReq) GetShowPresentEntrance() bool {
	if m != nil {
		return m.ShowPresentEntrance
	}
	return false
}

func (m *SetActivityUpdateInfoReq) GetPresentEntranceText() string {
	if m != nil {
		return m.PresentEntranceText
	}
	return ""
}

func (m *SetActivityUpdateInfoReq) GetPresentEntranceSeconds() uint32 {
	if m != nil {
		return m.PresentEntranceSeconds
	}
	return 0
}

func (m *SetActivityUpdateInfoReq) GetShowChannelMsg() bool {
	if m != nil {
		return m.ShowChannelMsg
	}
	return false
}

func (m *SetActivityUpdateInfoReq) GetChannelMsgText() string {
	if m != nil {
		return m.ChannelMsgText
	}
	return ""
}

func (m *SetActivityUpdateInfoReq) GetChannelMsgColor() string {
	if m != nil {
		return m.ChannelMsgColor
	}
	return ""
}

func (m *SetActivityUpdateInfoReq) GetShowRedDot() bool {
	if m != nil {
		return m.ShowRedDot
	}
	return false
}

func (m *SetActivityUpdateInfoReq) GetRemindStartTime() uint32 {
	if m != nil {
		return m.RemindStartTime
	}
	return 0
}

func (m *SetActivityUpdateInfoReq) GetRemindEndTime() uint32 {
	if m != nil {
		return m.RemindEndTime
	}
	return 0
}

type SetActivityUpdateInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetActivityUpdateInfoResp) Reset()         { *m = SetActivityUpdateInfoResp{} }
func (m *SetActivityUpdateInfoResp) String() string { return proto.CompactTextString(m) }
func (*SetActivityUpdateInfoResp) ProtoMessage()    {}
func (*SetActivityUpdateInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{27}
}
func (m *SetActivityUpdateInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetActivityUpdateInfoResp.Unmarshal(m, b)
}
func (m *SetActivityUpdateInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetActivityUpdateInfoResp.Marshal(b, m, deterministic)
}
func (dst *SetActivityUpdateInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetActivityUpdateInfoResp.Merge(dst, src)
}
func (m *SetActivityUpdateInfoResp) XXX_Size() int {
	return xxx_messageInfo_SetActivityUpdateInfoResp.Size(m)
}
func (m *SetActivityUpdateInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetActivityUpdateInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetActivityUpdateInfoResp proto.InternalMessageInfo

type GetWhiteListReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Ttid                 string   `protobuf:"bytes,3,opt,name=ttid,proto3" json:"ttid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWhiteListReq) Reset()         { *m = GetWhiteListReq{} }
func (m *GetWhiteListReq) String() string { return proto.CompactTextString(m) }
func (*GetWhiteListReq) ProtoMessage()    {}
func (*GetWhiteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{28}
}
func (m *GetWhiteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWhiteListReq.Unmarshal(m, b)
}
func (m *GetWhiteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWhiteListReq.Marshal(b, m, deterministic)
}
func (dst *GetWhiteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWhiteListReq.Merge(dst, src)
}
func (m *GetWhiteListReq) XXX_Size() int {
	return xxx_messageInfo_GetWhiteListReq.Size(m)
}
func (m *GetWhiteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWhiteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetWhiteListReq proto.InternalMessageInfo

func (m *GetWhiteListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetWhiteListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetWhiteListReq) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

type GetWhiteListResp struct {
	UidList              []*GetWhiteListResp_Item `protobuf:"bytes,1,rep,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	Total                uint32                   `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetWhiteListResp) Reset()         { *m = GetWhiteListResp{} }
func (m *GetWhiteListResp) String() string { return proto.CompactTextString(m) }
func (*GetWhiteListResp) ProtoMessage()    {}
func (*GetWhiteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{29}
}
func (m *GetWhiteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWhiteListResp.Unmarshal(m, b)
}
func (m *GetWhiteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWhiteListResp.Marshal(b, m, deterministic)
}
func (dst *GetWhiteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWhiteListResp.Merge(dst, src)
}
func (m *GetWhiteListResp) XXX_Size() int {
	return xxx_messageInfo_GetWhiteListResp.Size(m)
}
func (m *GetWhiteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWhiteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetWhiteListResp proto.InternalMessageInfo

func (m *GetWhiteListResp) GetUidList() []*GetWhiteListResp_Item {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetWhiteListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetWhiteListResp_Item struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CreateTime           uint32   `protobuf:"varint,2,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Operator             string   `protobuf:"bytes,3,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWhiteListResp_Item) Reset()         { *m = GetWhiteListResp_Item{} }
func (m *GetWhiteListResp_Item) String() string { return proto.CompactTextString(m) }
func (*GetWhiteListResp_Item) ProtoMessage()    {}
func (*GetWhiteListResp_Item) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{29, 0}
}
func (m *GetWhiteListResp_Item) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWhiteListResp_Item.Unmarshal(m, b)
}
func (m *GetWhiteListResp_Item) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWhiteListResp_Item.Marshal(b, m, deterministic)
}
func (dst *GetWhiteListResp_Item) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWhiteListResp_Item.Merge(dst, src)
}
func (m *GetWhiteListResp_Item) XXX_Size() int {
	return xxx_messageInfo_GetWhiteListResp_Item.Size(m)
}
func (m *GetWhiteListResp_Item) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWhiteListResp_Item.DiscardUnknown(m)
}

var xxx_messageInfo_GetWhiteListResp_Item proto.InternalMessageInfo

func (m *GetWhiteListResp_Item) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetWhiteListResp_Item) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *GetWhiteListResp_Item) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type BatchSetWhiteListReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	Operator             string   `protobuf:"bytes,2,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchSetWhiteListReq) Reset()         { *m = BatchSetWhiteListReq{} }
func (m *BatchSetWhiteListReq) String() string { return proto.CompactTextString(m) }
func (*BatchSetWhiteListReq) ProtoMessage()    {}
func (*BatchSetWhiteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{30}
}
func (m *BatchSetWhiteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSetWhiteListReq.Unmarshal(m, b)
}
func (m *BatchSetWhiteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSetWhiteListReq.Marshal(b, m, deterministic)
}
func (dst *BatchSetWhiteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSetWhiteListReq.Merge(dst, src)
}
func (m *BatchSetWhiteListReq) XXX_Size() int {
	return xxx_messageInfo_BatchSetWhiteListReq.Size(m)
}
func (m *BatchSetWhiteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSetWhiteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSetWhiteListReq proto.InternalMessageInfo

func (m *BatchSetWhiteListReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatchSetWhiteListReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type BatchSetWhiteListResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchSetWhiteListResp) Reset()         { *m = BatchSetWhiteListResp{} }
func (m *BatchSetWhiteListResp) String() string { return proto.CompactTextString(m) }
func (*BatchSetWhiteListResp) ProtoMessage()    {}
func (*BatchSetWhiteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{31}
}
func (m *BatchSetWhiteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSetWhiteListResp.Unmarshal(m, b)
}
func (m *BatchSetWhiteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSetWhiteListResp.Marshal(b, m, deterministic)
}
func (dst *BatchSetWhiteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSetWhiteListResp.Merge(dst, src)
}
func (m *BatchSetWhiteListResp) XXX_Size() int {
	return xxx_messageInfo_BatchSetWhiteListResp.Size(m)
}
func (m *BatchSetWhiteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSetWhiteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSetWhiteListResp proto.InternalMessageInfo

type DeleteWhiteListReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteWhiteListReq) Reset()         { *m = DeleteWhiteListReq{} }
func (m *DeleteWhiteListReq) String() string { return proto.CompactTextString(m) }
func (*DeleteWhiteListReq) ProtoMessage()    {}
func (*DeleteWhiteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{32}
}
func (m *DeleteWhiteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteWhiteListReq.Unmarshal(m, b)
}
func (m *DeleteWhiteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteWhiteListReq.Marshal(b, m, deterministic)
}
func (dst *DeleteWhiteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteWhiteListReq.Merge(dst, src)
}
func (m *DeleteWhiteListReq) XXX_Size() int {
	return xxx_messageInfo_DeleteWhiteListReq.Size(m)
}
func (m *DeleteWhiteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteWhiteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteWhiteListReq proto.InternalMessageInfo

func (m *DeleteWhiteListReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type DeleteWhiteListResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteWhiteListResp) Reset()         { *m = DeleteWhiteListResp{} }
func (m *DeleteWhiteListResp) String() string { return proto.CompactTextString(m) }
func (*DeleteWhiteListResp) ProtoMessage()    {}
func (*DeleteWhiteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_treasure_house_efc62ec643b50c38, []int{33}
}
func (m *DeleteWhiteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteWhiteListResp.Unmarshal(m, b)
}
func (m *DeleteWhiteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteWhiteListResp.Marshal(b, m, deterministic)
}
func (dst *DeleteWhiteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteWhiteListResp.Merge(dst, src)
}
func (m *DeleteWhiteListResp) XXX_Size() int {
	return xxx_messageInfo_DeleteWhiteListResp.Size(m)
}
func (m *DeleteWhiteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteWhiteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteWhiteListResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*ConditionValue)(nil), "treasure_house.ConditionValue")
	proto.RegisterType((*Condition)(nil), "treasure_house.Condition")
	proto.RegisterType((*PresentInfo)(nil), "treasure_house.PresentInfo")
	proto.RegisterType((*PackageJumpConfig)(nil), "treasure_house.PackageJumpConfig")
	proto.RegisterType((*BatchGiveUser)(nil), "treasure_house.BatchGiveUser")
	proto.RegisterType((*ActivityConfig)(nil), "treasure_house.ActivityConfig")
	proto.RegisterType((*ActivityPhase)(nil), "treasure_house.ActivityPhase")
	proto.RegisterType((*UserPermissionInfo)(nil), "treasure_house.UserPermissionInfo")
	proto.RegisterType((*GetActivityConfigListReq)(nil), "treasure_house.GetActivityConfigListReq")
	proto.RegisterType((*GetActivityConfigListResp)(nil), "treasure_house.GetActivityConfigListResp")
	proto.RegisterType((*GetActivityConfigReq)(nil), "treasure_house.GetActivityConfigReq")
	proto.RegisterType((*GetActivityConfigResp)(nil), "treasure_house.GetActivityConfigResp")
	proto.RegisterType((*CreateActivityConfigReq)(nil), "treasure_house.CreateActivityConfigReq")
	proto.RegisterType((*CreateActivityConfigResp)(nil), "treasure_house.CreateActivityConfigResp")
	proto.RegisterType((*DeleteActivityConfigReq)(nil), "treasure_house.DeleteActivityConfigReq")
	proto.RegisterType((*DeleteActivityConfigResp)(nil), "treasure_house.DeleteActivityConfigResp")
	proto.RegisterType((*ClaimPresentPermissionReq)(nil), "treasure_house.ClaimPresentPermissionReq")
	proto.RegisterType((*ClaimPresentPermissionResp)(nil), "treasure_house.ClaimPresentPermissionResp")
	proto.RegisterType((*BuyPresentPermissionReq)(nil), "treasure_house.BuyPresentPermissionReq")
	proto.RegisterType((*BuyPresentPermissionResp)(nil), "treasure_house.BuyPresentPermissionResp")
	proto.RegisterType((*GiveActivityTreasurePresentReq)(nil), "treasure_house.GiveActivityTreasurePresentReq")
	proto.RegisterType((*GiveActivityTreasurePresentResp)(nil), "treasure_house.GiveActivityTreasurePresentResp")
	proto.RegisterType((*CheckActivityPresentStatusReq)(nil), "treasure_house.CheckActivityPresentStatusReq")
	proto.RegisterType((*CheckActivityPresentStatusResp)(nil), "treasure_house.CheckActivityPresentStatusResp")
	proto.RegisterType((*GetActivityUpdateInfoReq)(nil), "treasure_house.GetActivityUpdateInfoReq")
	proto.RegisterType((*GetActivityUpdateInfoResp)(nil), "treasure_house.GetActivityUpdateInfoResp")
	proto.RegisterType((*SetActivityUpdateInfoReq)(nil), "treasure_house.SetActivityUpdateInfoReq")
	proto.RegisterType((*SetActivityUpdateInfoResp)(nil), "treasure_house.SetActivityUpdateInfoResp")
	proto.RegisterType((*GetWhiteListReq)(nil), "treasure_house.GetWhiteListReq")
	proto.RegisterType((*GetWhiteListResp)(nil), "treasure_house.GetWhiteListResp")
	proto.RegisterType((*GetWhiteListResp_Item)(nil), "treasure_house.GetWhiteListResp.Item")
	proto.RegisterType((*BatchSetWhiteListReq)(nil), "treasure_house.BatchSetWhiteListReq")
	proto.RegisterType((*BatchSetWhiteListResp)(nil), "treasure_house.BatchSetWhiteListResp")
	proto.RegisterType((*DeleteWhiteListReq)(nil), "treasure_house.DeleteWhiteListReq")
	proto.RegisterType((*DeleteWhiteListResp)(nil), "treasure_house.DeleteWhiteListResp")
	proto.RegisterEnum("treasure_house.PhaseType", PhaseType_name, PhaseType_value)
	proto.RegisterEnum("treasure_house.PresentGetMethod", PresentGetMethod_name, PresentGetMethod_value)
	proto.RegisterEnum("treasure_house.PresentValidTimeType", PresentValidTimeType_name, PresentValidTimeType_value)
	proto.RegisterEnum("treasure_house.ButtonAction", ButtonAction_name, ButtonAction_value)
	proto.RegisterEnum("treasure_house.ActivityStatus", ActivityStatus_name, ActivityStatus_value)
	proto.RegisterEnum("treasure_house.ConditionType", ConditionType_name, ConditionType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// TreasureHouseClient is the client API for TreasureHouse service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type TreasureHouseClient interface {
	// 获取活动配置列表
	GetActivityConfigList(ctx context.Context, in *GetActivityConfigListReq, opts ...grpc.CallOption) (*GetActivityConfigListResp, error)
	// 获取活动配置
	GetActivityConfig(ctx context.Context, in *GetActivityConfigReq, opts ...grpc.CallOption) (*GetActivityConfigResp, error)
	// 创建活动配置
	CreateActivityConfig(ctx context.Context, in *CreateActivityConfigReq, opts ...grpc.CallOption) (*CreateActivityConfigResp, error)
	// 删除活动
	DeleteActivityConfig(ctx context.Context, in *DeleteActivityConfigReq, opts ...grpc.CallOption) (*DeleteActivityConfigResp, error)
	// 领取礼物权
	ClaimPresentPermission(ctx context.Context, in *ClaimPresentPermissionReq, opts ...grpc.CallOption) (*ClaimPresentPermissionResp, error)
	// 购买礼物权
	BuyPresentPermission(ctx context.Context, in *BuyPresentPermissionReq, opts ...grpc.CallOption) (*BuyPresentPermissionResp, error)
	// 活动发放礼物权
	GiveActivityTreasurePresent(ctx context.Context, in *GiveActivityTreasurePresentReq, opts ...grpc.CallOption) (*GiveActivityTreasurePresentResp, error)
	// 检查活动礼物状态
	CheckActivityPresentStatus(ctx context.Context, in *CheckActivityPresentStatusReq, opts ...grpc.CallOption) (*CheckActivityPresentStatusResp, error)
	// 获取时间范围内礼物券的订单列表
	GetTreasureOrderValueByTimeRange(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetTreasureOrderListByTimeRange(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	FixPrivilegeOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error)
	// 获取活动更新提醒信息
	GetActivityUpdateInfo(ctx context.Context, in *GetActivityUpdateInfoReq, opts ...grpc.CallOption) (*GetActivityUpdateInfoResp, error)
	// 设置活动更新提醒信息
	SetActivityUpdateInfo(ctx context.Context, in *SetActivityUpdateInfoReq, opts ...grpc.CallOption) (*SetActivityUpdateInfoResp, error)
	// 获取入口白名单
	GetWhiteList(ctx context.Context, in *GetWhiteListReq, opts ...grpc.CallOption) (*GetWhiteListResp, error)
	// 批量设置入口白名单
	BatchSetWhiteList(ctx context.Context, in *BatchSetWhiteListReq, opts ...grpc.CallOption) (*BatchSetWhiteListResp, error)
	// 删除入口白名单
	DeleteWhiteList(ctx context.Context, in *DeleteWhiteListReq, opts ...grpc.CallOption) (*DeleteWhiteListResp, error)
}

type treasureHouseClient struct {
	cc *grpc.ClientConn
}

func NewTreasureHouseClient(cc *grpc.ClientConn) TreasureHouseClient {
	return &treasureHouseClient{cc}
}

func (c *treasureHouseClient) GetActivityConfigList(ctx context.Context, in *GetActivityConfigListReq, opts ...grpc.CallOption) (*GetActivityConfigListResp, error) {
	out := new(GetActivityConfigListResp)
	err := c.cc.Invoke(ctx, "/treasure_house.TreasureHouse/GetActivityConfigList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *treasureHouseClient) GetActivityConfig(ctx context.Context, in *GetActivityConfigReq, opts ...grpc.CallOption) (*GetActivityConfigResp, error) {
	out := new(GetActivityConfigResp)
	err := c.cc.Invoke(ctx, "/treasure_house.TreasureHouse/GetActivityConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *treasureHouseClient) CreateActivityConfig(ctx context.Context, in *CreateActivityConfigReq, opts ...grpc.CallOption) (*CreateActivityConfigResp, error) {
	out := new(CreateActivityConfigResp)
	err := c.cc.Invoke(ctx, "/treasure_house.TreasureHouse/CreateActivityConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *treasureHouseClient) DeleteActivityConfig(ctx context.Context, in *DeleteActivityConfigReq, opts ...grpc.CallOption) (*DeleteActivityConfigResp, error) {
	out := new(DeleteActivityConfigResp)
	err := c.cc.Invoke(ctx, "/treasure_house.TreasureHouse/DeleteActivityConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *treasureHouseClient) ClaimPresentPermission(ctx context.Context, in *ClaimPresentPermissionReq, opts ...grpc.CallOption) (*ClaimPresentPermissionResp, error) {
	out := new(ClaimPresentPermissionResp)
	err := c.cc.Invoke(ctx, "/treasure_house.TreasureHouse/ClaimPresentPermission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *treasureHouseClient) BuyPresentPermission(ctx context.Context, in *BuyPresentPermissionReq, opts ...grpc.CallOption) (*BuyPresentPermissionResp, error) {
	out := new(BuyPresentPermissionResp)
	err := c.cc.Invoke(ctx, "/treasure_house.TreasureHouse/BuyPresentPermission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *treasureHouseClient) GiveActivityTreasurePresent(ctx context.Context, in *GiveActivityTreasurePresentReq, opts ...grpc.CallOption) (*GiveActivityTreasurePresentResp, error) {
	out := new(GiveActivityTreasurePresentResp)
	err := c.cc.Invoke(ctx, "/treasure_house.TreasureHouse/GiveActivityTreasurePresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *treasureHouseClient) CheckActivityPresentStatus(ctx context.Context, in *CheckActivityPresentStatusReq, opts ...grpc.CallOption) (*CheckActivityPresentStatusResp, error) {
	out := new(CheckActivityPresentStatusResp)
	err := c.cc.Invoke(ctx, "/treasure_house.TreasureHouse/CheckActivityPresentStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *treasureHouseClient) GetTreasureOrderValueByTimeRange(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/treasure_house.TreasureHouse/GetTreasureOrderValueByTimeRange", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *treasureHouseClient) GetTreasureOrderListByTimeRange(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/treasure_house.TreasureHouse/GetTreasureOrderListByTimeRange", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *treasureHouseClient) FixPrivilegeOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	out := new(reconcile_v2.EmptyResp)
	err := c.cc.Invoke(ctx, "/treasure_house.TreasureHouse/FixPrivilegeOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *treasureHouseClient) GetActivityUpdateInfo(ctx context.Context, in *GetActivityUpdateInfoReq, opts ...grpc.CallOption) (*GetActivityUpdateInfoResp, error) {
	out := new(GetActivityUpdateInfoResp)
	err := c.cc.Invoke(ctx, "/treasure_house.TreasureHouse/GetActivityUpdateInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *treasureHouseClient) SetActivityUpdateInfo(ctx context.Context, in *SetActivityUpdateInfoReq, opts ...grpc.CallOption) (*SetActivityUpdateInfoResp, error) {
	out := new(SetActivityUpdateInfoResp)
	err := c.cc.Invoke(ctx, "/treasure_house.TreasureHouse/SetActivityUpdateInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *treasureHouseClient) GetWhiteList(ctx context.Context, in *GetWhiteListReq, opts ...grpc.CallOption) (*GetWhiteListResp, error) {
	out := new(GetWhiteListResp)
	err := c.cc.Invoke(ctx, "/treasure_house.TreasureHouse/GetWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *treasureHouseClient) BatchSetWhiteList(ctx context.Context, in *BatchSetWhiteListReq, opts ...grpc.CallOption) (*BatchSetWhiteListResp, error) {
	out := new(BatchSetWhiteListResp)
	err := c.cc.Invoke(ctx, "/treasure_house.TreasureHouse/BatchSetWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *treasureHouseClient) DeleteWhiteList(ctx context.Context, in *DeleteWhiteListReq, opts ...grpc.CallOption) (*DeleteWhiteListResp, error) {
	out := new(DeleteWhiteListResp)
	err := c.cc.Invoke(ctx, "/treasure_house.TreasureHouse/DeleteWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TreasureHouseServer is the server API for TreasureHouse service.
type TreasureHouseServer interface {
	// 获取活动配置列表
	GetActivityConfigList(context.Context, *GetActivityConfigListReq) (*GetActivityConfigListResp, error)
	// 获取活动配置
	GetActivityConfig(context.Context, *GetActivityConfigReq) (*GetActivityConfigResp, error)
	// 创建活动配置
	CreateActivityConfig(context.Context, *CreateActivityConfigReq) (*CreateActivityConfigResp, error)
	// 删除活动
	DeleteActivityConfig(context.Context, *DeleteActivityConfigReq) (*DeleteActivityConfigResp, error)
	// 领取礼物权
	ClaimPresentPermission(context.Context, *ClaimPresentPermissionReq) (*ClaimPresentPermissionResp, error)
	// 购买礼物权
	BuyPresentPermission(context.Context, *BuyPresentPermissionReq) (*BuyPresentPermissionResp, error)
	// 活动发放礼物权
	GiveActivityTreasurePresent(context.Context, *GiveActivityTreasurePresentReq) (*GiveActivityTreasurePresentResp, error)
	// 检查活动礼物状态
	CheckActivityPresentStatus(context.Context, *CheckActivityPresentStatusReq) (*CheckActivityPresentStatusResp, error)
	// 获取时间范围内礼物券的订单列表
	GetTreasureOrderValueByTimeRange(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetTreasureOrderListByTimeRange(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	FixPrivilegeOrder(context.Context, *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error)
	// 获取活动更新提醒信息
	GetActivityUpdateInfo(context.Context, *GetActivityUpdateInfoReq) (*GetActivityUpdateInfoResp, error)
	// 设置活动更新提醒信息
	SetActivityUpdateInfo(context.Context, *SetActivityUpdateInfoReq) (*SetActivityUpdateInfoResp, error)
	// 获取入口白名单
	GetWhiteList(context.Context, *GetWhiteListReq) (*GetWhiteListResp, error)
	// 批量设置入口白名单
	BatchSetWhiteList(context.Context, *BatchSetWhiteListReq) (*BatchSetWhiteListResp, error)
	// 删除入口白名单
	DeleteWhiteList(context.Context, *DeleteWhiteListReq) (*DeleteWhiteListResp, error)
}

func RegisterTreasureHouseServer(s *grpc.Server, srv TreasureHouseServer) {
	s.RegisterService(&_TreasureHouse_serviceDesc, srv)
}

func _TreasureHouse_GetActivityConfigList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActivityConfigListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TreasureHouseServer).GetActivityConfigList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/treasure_house.TreasureHouse/GetActivityConfigList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TreasureHouseServer).GetActivityConfigList(ctx, req.(*GetActivityConfigListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TreasureHouse_GetActivityConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActivityConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TreasureHouseServer).GetActivityConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/treasure_house.TreasureHouse/GetActivityConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TreasureHouseServer).GetActivityConfig(ctx, req.(*GetActivityConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TreasureHouse_CreateActivityConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateActivityConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TreasureHouseServer).CreateActivityConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/treasure_house.TreasureHouse/CreateActivityConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TreasureHouseServer).CreateActivityConfig(ctx, req.(*CreateActivityConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TreasureHouse_DeleteActivityConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteActivityConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TreasureHouseServer).DeleteActivityConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/treasure_house.TreasureHouse/DeleteActivityConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TreasureHouseServer).DeleteActivityConfig(ctx, req.(*DeleteActivityConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TreasureHouse_ClaimPresentPermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClaimPresentPermissionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TreasureHouseServer).ClaimPresentPermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/treasure_house.TreasureHouse/ClaimPresentPermission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TreasureHouseServer).ClaimPresentPermission(ctx, req.(*ClaimPresentPermissionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TreasureHouse_BuyPresentPermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BuyPresentPermissionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TreasureHouseServer).BuyPresentPermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/treasure_house.TreasureHouse/BuyPresentPermission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TreasureHouseServer).BuyPresentPermission(ctx, req.(*BuyPresentPermissionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TreasureHouse_GiveActivityTreasurePresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GiveActivityTreasurePresentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TreasureHouseServer).GiveActivityTreasurePresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/treasure_house.TreasureHouse/GiveActivityTreasurePresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TreasureHouseServer).GiveActivityTreasurePresent(ctx, req.(*GiveActivityTreasurePresentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TreasureHouse_CheckActivityPresentStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckActivityPresentStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TreasureHouseServer).CheckActivityPresentStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/treasure_house.TreasureHouse/CheckActivityPresentStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TreasureHouseServer).CheckActivityPresentStatus(ctx, req.(*CheckActivityPresentStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TreasureHouse_GetTreasureOrderValueByTimeRange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TreasureHouseServer).GetTreasureOrderValueByTimeRange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/treasure_house.TreasureHouse/GetTreasureOrderValueByTimeRange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TreasureHouseServer).GetTreasureOrderValueByTimeRange(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TreasureHouse_GetTreasureOrderListByTimeRange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TreasureHouseServer).GetTreasureOrderListByTimeRange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/treasure_house.TreasureHouse/GetTreasureOrderListByTimeRange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TreasureHouseServer).GetTreasureOrderListByTimeRange(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TreasureHouse_FixPrivilegeOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.ReplaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TreasureHouseServer).FixPrivilegeOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/treasure_house.TreasureHouse/FixPrivilegeOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TreasureHouseServer).FixPrivilegeOrder(ctx, req.(*reconcile_v2.ReplaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TreasureHouse_GetActivityUpdateInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActivityUpdateInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TreasureHouseServer).GetActivityUpdateInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/treasure_house.TreasureHouse/GetActivityUpdateInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TreasureHouseServer).GetActivityUpdateInfo(ctx, req.(*GetActivityUpdateInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TreasureHouse_SetActivityUpdateInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetActivityUpdateInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TreasureHouseServer).SetActivityUpdateInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/treasure_house.TreasureHouse/SetActivityUpdateInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TreasureHouseServer).SetActivityUpdateInfo(ctx, req.(*SetActivityUpdateInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TreasureHouse_GetWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TreasureHouseServer).GetWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/treasure_house.TreasureHouse/GetWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TreasureHouseServer).GetWhiteList(ctx, req.(*GetWhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TreasureHouse_BatchSetWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchSetWhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TreasureHouseServer).BatchSetWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/treasure_house.TreasureHouse/BatchSetWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TreasureHouseServer).BatchSetWhiteList(ctx, req.(*BatchSetWhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TreasureHouse_DeleteWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteWhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TreasureHouseServer).DeleteWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/treasure_house.TreasureHouse/DeleteWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TreasureHouseServer).DeleteWhiteList(ctx, req.(*DeleteWhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _TreasureHouse_serviceDesc = grpc.ServiceDesc{
	ServiceName: "treasure_house.TreasureHouse",
	HandlerType: (*TreasureHouseServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetActivityConfigList",
			Handler:    _TreasureHouse_GetActivityConfigList_Handler,
		},
		{
			MethodName: "GetActivityConfig",
			Handler:    _TreasureHouse_GetActivityConfig_Handler,
		},
		{
			MethodName: "CreateActivityConfig",
			Handler:    _TreasureHouse_CreateActivityConfig_Handler,
		},
		{
			MethodName: "DeleteActivityConfig",
			Handler:    _TreasureHouse_DeleteActivityConfig_Handler,
		},
		{
			MethodName: "ClaimPresentPermission",
			Handler:    _TreasureHouse_ClaimPresentPermission_Handler,
		},
		{
			MethodName: "BuyPresentPermission",
			Handler:    _TreasureHouse_BuyPresentPermission_Handler,
		},
		{
			MethodName: "GiveActivityTreasurePresent",
			Handler:    _TreasureHouse_GiveActivityTreasurePresent_Handler,
		},
		{
			MethodName: "CheckActivityPresentStatus",
			Handler:    _TreasureHouse_CheckActivityPresentStatus_Handler,
		},
		{
			MethodName: "GetTreasureOrderValueByTimeRange",
			Handler:    _TreasureHouse_GetTreasureOrderValueByTimeRange_Handler,
		},
		{
			MethodName: "GetTreasureOrderListByTimeRange",
			Handler:    _TreasureHouse_GetTreasureOrderListByTimeRange_Handler,
		},
		{
			MethodName: "FixPrivilegeOrder",
			Handler:    _TreasureHouse_FixPrivilegeOrder_Handler,
		},
		{
			MethodName: "GetActivityUpdateInfo",
			Handler:    _TreasureHouse_GetActivityUpdateInfo_Handler,
		},
		{
			MethodName: "SetActivityUpdateInfo",
			Handler:    _TreasureHouse_SetActivityUpdateInfo_Handler,
		},
		{
			MethodName: "GetWhiteList",
			Handler:    _TreasureHouse_GetWhiteList_Handler,
		},
		{
			MethodName: "BatchSetWhiteList",
			Handler:    _TreasureHouse_BatchSetWhiteList_Handler,
		},
		{
			MethodName: "DeleteWhiteList",
			Handler:    _TreasureHouse_DeleteWhiteList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "treasure-house/treasure-house.proto",
}

func init() {
	proto.RegisterFile("treasure-house/treasure-house.proto", fileDescriptor_treasure_house_efc62ec643b50c38)
}

var fileDescriptor_treasure_house_efc62ec643b50c38 = []byte{
	// 2597 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x5a, 0x4b, 0x6f, 0x23, 0x4b,
	0xf5, 0x1f, 0x3b, 0xce, 0xeb, 0x24, 0x76, 0xda, 0x95, 0x57, 0xdb, 0xb9, 0x93, 0x64, 0x7a, 0x5e,
	0xf9, 0xe7, 0xcf, 0x38, 0x90, 0xcb, 0xa0, 0xcb, 0x06, 0xae, 0x63, 0x7b, 0x32, 0xbe, 0xc4, 0x0f,
	0xda, 0x76, 0x86, 0x41, 0x48, 0x4d, 0xc7, 0x5d, 0x71, 0x9a, 0xd8, 0xdd, 0x3d, 0xdd, 0xe5, 0x90,
	0x2c, 0x90, 0x10, 0x08, 0x84, 0x84, 0xc4, 0x02, 0x21, 0xb1, 0xe1, 0x83, 0xb0, 0x65, 0x89, 0xd8,
	0xc3, 0xd7, 0x41, 0x55, 0xd5, 0xdd, 0xee, 0x97, 0x9d, 0xcc, 0xd5, 0x48, 0x6c, 0xee, 0xce, 0x75,
	0xce, 0xaf, 0xce, 0xab, 0x4e, 0x9d, 0x3a, 0xa7, 0x13, 0x78, 0x4a, 0x6c, 0xac, 0x3a, 0x63, 0x1b,
	0xbf, 0xba, 0x32, 0xc7, 0x0e, 0x3e, 0x0a, 0x2f, 0x4b, 0x96, 0x6d, 0x12, 0x13, 0xe5, 0x3c, 0xaa,
	0xc2, 0xa8, 0xc5, 0x3d, 0x1b, 0xf7, 0x4d, 0xa3, 0xaf, 0x0f, 0xf1, 0xab, 0x9b, 0xe3, 0xa3, 0xe0,
	0x82, 0x6f, 0x90, 0x5e, 0x40, 0xae, 0x62, 0x1a, 0x9a, 0x4e, 0x74, 0xd3, 0x38, 0x57, 0x87, 0x63,
	0x8c, 0x36, 0x60, 0xfe, 0x86, 0xfe, 0x10, 0x53, 0xfb, 0xa9, 0x83, 0x8c, 0xcc, 0x17, 0x12, 0x81,
	0x65, 0x1f, 0x87, 0xbe, 0x03, 0x19, 0x72, 0x67, 0x71, 0x44, 0xee, 0xf8, 0x71, 0x29, 0xac, 0xb4,
	0xe4, 0x03, 0xbb, 0x77, 0x16, 0x96, 0x19, 0x14, 0x7d, 0xd7, 0x93, 0x9a, 0xde, 0x4f, 0x1d, 0xac,
	0x1c, 0xef, 0x4e, 0xdd, 0xc3, 0x8c, 0xf0, 0xb4, 0x5e, 0xc3, 0x4a, 0xdb, 0xc6, 0x0e, 0x36, 0x48,
	0xdd, 0xb8, 0x34, 0xd1, 0x36, 0x2c, 0x0e, 0xf4, 0x4b, 0xa2, 0xe8, 0x1a, 0x53, 0x9d, 0x95, 0x17,
	0xe8, 0xb2, 0xae, 0x21, 0x04, 0x19, 0x43, 0x1d, 0x71, 0xe1, 0xcb, 0x32, 0xfb, 0x8d, 0x0a, 0xb0,
	0xa4, 0xf7, 0x4d, 0x43, 0x19, 0xdb, 0x43, 0x71, 0x8e, 0xd1, 0x17, 0xe9, 0xba, 0x67, 0x0f, 0xa9,
	0x8b, 0x96, 0xad, 0xf7, 0xb1, 0x98, 0xe1, 0x2e, 0xb2, 0x85, 0xd4, 0x83, 0x7c, 0x5b, 0xed, 0x5f,
	0xab, 0x03, 0xfc, 0xd5, 0x78, 0x64, 0x55, 0x4c, 0xe3, 0x52, 0x1f, 0x20, 0x01, 0xe6, 0x54, 0xcb,
	0x72, 0xd5, 0xd1, 0x9f, 0xa8, 0x08, 0x4b, 0xd6, 0x50, 0x25, 0x97, 0xa6, 0x3d, 0x62, 0xfa, 0xb2,
	0xb2, 0xbf, 0xa6, 0x76, 0x0c, 0x75, 0xe3, 0xda, 0xd5, 0xc7, 0x7e, 0x4b, 0x4f, 0x20, 0x7b, 0xa2,
	0x92, 0xfe, 0xd5, 0xa9, 0x7e, 0x83, 0x7b, 0x0e, 0xb6, 0xa9, 0xc8, 0xb1, 0xef, 0x01, 0xfd, 0x29,
	0xfd, 0x6e, 0x1e, 0x72, 0xe5, 0x3e, 0xd1, 0x6f, 0x74, 0x72, 0xe7, 0xea, 0xdd, 0x83, 0x15, 0xd5,
	0xa5, 0x4c, 0xdc, 0x05, 0x8f, 0x54, 0xd7, 0x82, 0xb1, 0x48, 0x87, 0x62, 0xf1, 0x18, 0xc0, 0x21,
	0xaa, 0x4d, 0x14, 0xa2, 0x8f, 0x30, 0xb3, 0x24, 0x2b, 0x2f, 0x33, 0x4a, 0x57, 0xe7, 0x61, 0xc1,
	0x86, 0xc6, 0x99, 0x19, 0xc6, 0x5c, 0xc4, 0x86, 0xc6, 0x58, 0x22, 0x2c, 0x5e, 0x9a, 0x36, 0xbe,
	0xc1, 0xb6, 0xb8, 0xb0, 0x9f, 0x3a, 0x58, 0x92, 0xbd, 0x25, 0xf5, 0xcb, 0x31, 0x6d, 0x22, 0x2e,
	0xb2, 0x0d, 0xec, 0x37, 0x7a, 0x0e, 0xb9, 0x0b, 0xb5, 0x7f, 0x3d, 0xb0, 0xcd, 0xb1, 0xa1, 0xb1,
	0x28, 0x2f, 0x31, 0xaf, 0xb3, 0x13, 0x2a, 0x8d, 0xf5, 0x0e, 0x2c, 0xdf, 0xe8, 0x1a, 0x36, 0x19,
	0x62, 0x99, 0x21, 0x96, 0x18, 0x21, 0xc4, 0x1c, 0x69, 0xaf, 0x45, 0x08, 0x30, 0x1b, 0xda, 0x6b,
	0xb4, 0x05, 0x0b, 0x36, 0x1e, 0xa9, 0xf6, 0xb5, 0xb8, 0xc2, 0x38, 0xee, 0x8a, 0x1e, 0x80, 0x69,
	0x61, 0x5b, 0x25, 0xa6, 0x2d, 0xae, 0xf2, 0x3d, 0xde, 0x9a, 0x86, 0xad, 0x6f, 0x63, 0x95, 0x60,
	0xee, 0x60, 0x96, 0x87, 0x8d, 0x93, 0x98, 0x8f, 0x0d, 0x58, 0xf7, 0xe3, 0x6a, 0x5d, 0xa9, 0x0e,
	0x56, 0x86, 0xba, 0x43, 0xc4, 0xdc, 0xfe, 0xdc, 0xc1, 0x4a, 0x3c, 0x93, 0xbd, 0x43, 0x69, 0x53,
	0xa4, 0x9c, 0x57, 0x83, 0xcb, 0x33, 0xdd, 0x21, 0xa8, 0x0b, 0x1b, 0x63, 0x07, 0xdb, 0x8a, 0x85,
	0xed, 0x91, 0xee, 0x38, 0xba, 0x69, 0x28, 0xba, 0x71, 0x69, 0x8a, 0x6b, 0x2c, 0xcb, 0xa5, 0xa8,
	0x3c, 0x7a, 0xfe, 0x6d, 0x1f, 0x4a, 0x73, 0x5a, 0x46, 0xe3, 0x18, 0x0d, 0x7d, 0x0f, 0x16, 0x1c,
	0xa2, 0x92, 0xb1, 0x23, 0x0a, 0xec, 0x86, 0xed, 0x4e, 0xb3, 0xab, 0xc3, 0x50, 0xb2, 0x8b, 0x46,
	0x3f, 0x80, 0x55, 0x8b, 0x5f, 0x17, 0x6e, 0x45, 0x9e, 0x59, 0xb1, 0x13, 0xdd, 0x1d, 0xb8, 0x52,
	0xf2, 0x8a, 0x35, 0x59, 0x48, 0xff, 0x5e, 0x86, 0x6c, 0xc8, 0xe5, 0xfb, 0xd3, 0xb0, 0x00, 0x4b,
	0x3c, 0x8c, 0x7e, 0x1e, 0x2e, 0xb2, 0x75, 0x5d, 0x43, 0x5f, 0x00, 0x70, 0x16, 0xab, 0x15, 0x73,
	0xcc, 0x93, 0x42, 0xcc, 0x16, 0x8a, 0x60, 0x75, 0x62, 0xd9, 0xf2, 0x7e, 0x46, 0x52, 0x38, 0x33,
	0x2b, 0x85, 0xe7, 0x1f, 0x9a, 0xc2, 0xfe, 0x9d, 0x5f, 0x0c, 0xdc, 0x79, 0x9a, 0xc4, 0x9a, 0xee,
	0xf4, 0xcd, 0xb1, 0x41, 0x14, 0xce, 0x5e, 0x62, 0xec, 0xac, 0x47, 0x6d, 0x33, 0x58, 0x19, 0xb2,
	0x17, 0x63, 0x42, 0x4c, 0x43, 0xa1, 0xae, 0x9b, 0x06, 0x4b, 0xe4, 0xdc, 0xf1, 0x67, 0x51, 0x6f,
	0x4e, 0x18, 0xa8, 0xcc, 0x30, 0xf2, 0xea, 0x45, 0x60, 0x45, 0x23, 0xe9, 0x8a, 0x60, 0x15, 0x82,
	0x27, 0x3b, 0x70, 0xd2, 0x99, 0x6e, 0x5c, 0xa3, 0x17, 0xb0, 0xe6, 0x02, 0x88, 0x69, 0x29, 0x04,
	0xdf, 0x12, 0x37, 0xef, 0x5d, 0xd5, 0x5d, 0xd3, 0xea, 0xe2, 0x5b, 0x82, 0x5e, 0x01, 0xd2, 0x0d,
	0x82, 0x6d, 0xdb, 0x1c, 0xa8, 0x54, 0x32, 0x87, 0xf2, 0x8b, 0x90, 0x0f, 0x71, 0x18, 0x5c, 0x82,
	0xec, 0xc8, 0x19, 0x28, 0x17, 0xe6, 0xad, 0x42, 0x74, 0x32, 0xe4, 0x77, 0x62, 0x59, 0x5e, 0x19,
	0x39, 0x83, 0x13, 0xf3, 0xb6, 0x4b, 0x49, 0x54, 0xb5, 0x87, 0xe9, 0x9b, 0x06, 0xc1, 0x06, 0xbd,
	0x10, 0x4c, 0x35, 0x47, 0x55, 0x38, 0x11, 0xfd, 0x10, 0x60, 0x80, 0x89, 0x32, 0xc2, 0xe4, 0xca,
	0xd4, 0x58, 0x8e, 0xe7, 0x8e, 0xf7, 0xa7, 0x64, 0xd7, 0x29, 0x26, 0x0d, 0x86, 0x93, 0x97, 0x07,
	0xde, 0x4f, 0x74, 0x06, 0x6b, 0x37, 0xea, 0x50, 0xe7, 0x67, 0xc7, 0xf3, 0x82, 0x67, 0xf8, 0xb3,
	0x29, 0x52, 0xce, 0x29, 0x9a, 0x9e, 0x2c, 0x4b, 0x91, 0xec, 0x4d, 0x70, 0x89, 0x9e, 0x41, 0x2e,
	0x20, 0x4d, 0x53, 0xef, 0x58, 0xc2, 0x67, 0xe5, 0x55, 0x1f, 0x56, 0x55, 0xef, 0xa8, 0x73, 0x01,
	0xd4, 0x95, 0x39, 0xb6, 0x45, 0xc4, 0x60, 0x13, 0x69, 0x6f, 0xcd, 0xb1, 0x8d, 0x4a, 0xb0, 0x1e,
	0x94, 0x86, 0x55, 0x6d, 0xa8, 0x1b, 0x58, 0x5c, 0x67, 0xd8, 0xfc, 0x44, 0xa4, 0xcb, 0x40, 0x5f,
	0x42, 0xae, 0xef, 0x3d, 0x5a, 0xbc, 0x88, 0x6c, 0xb0, 0x22, 0x52, 0x98, 0xfa, 0xb4, 0xc9, 0x59,
	0x7f, 0x03, 0x2b, 0x1e, 0x2f, 0x60, 0x8d, 0x5e, 0x5c, 0x25, 0x90, 0xeb, 0x9b, 0xdc, 0x32, 0x4a,
	0xee, 0xf8, 0xf9, 0x2e, 0x01, 0x23, 0x28, 0x7e, 0xd2, 0x6f, 0x31, 0xd4, 0x0a, 0x25, 0xd6, 0xdc,
	0xc4, 0x97, 0x20, 0x4b, 0xd3, 0x66, 0x6c, 0x29, 0xea, 0x88, 0xe6, 0xad, 0xb8, 0xcd, 0xf2, 0x78,
	0x85, 0x98, 0x56, 0xcf, 0x2a, 0x33, 0x12, 0xfa, 0x16, 0xa0, 0x80, 0x87, 0x0e, 0x6d, 0x06, 0x34,
	0x47, 0x2c, 0x30, 0x61, 0x82, 0xef, 0x60, 0x87, 0xd3, 0xd1, 0x4b, 0x58, 0xb3, 0xc6, 0x76, 0x9f,
	0x5e, 0x4a, 0x4d, 0x61, 0x77, 0x41, 0x2c, 0x32, 0x68, 0xce, 0x27, 0x57, 0x98, 0xd8, 0x1f, 0xc3,
	0xba, 0xc5, 0xdf, 0x4d, 0xe5, 0x17, 0xe3, 0x91, 0x45, 0x53, 0xe8, 0x52, 0x1f, 0x88, 0x3b, 0x2c,
	0x1a, 0x4f, 0x62, 0x07, 0x1b, 0x7d, 0x62, 0xe5, 0xbc, 0x15, 0x7b, 0x75, 0x9b, 0xb0, 0x71, 0x41,
	0xdf, 0x4c, 0x65, 0xa0, 0xdf, 0x60, 0x85, 0x55, 0x58, 0x16, 0xe1, 0xcf, 0x92, 0xcb, 0x74, 0xe8,
	0x7d, 0x95, 0xf3, 0x17, 0xc1, 0x25, 0x8d, 0xb4, 0xf4, 0xaf, 0x14, 0xa0, 0x78, 0xed, 0x8d, 0xbf,
	0xc4, 0xf4, 0x72, 0x79, 0x15, 0x74, 0x52, 0xd2, 0xdd, 0xc2, 0x96, 0x77, 0x39, 0x13, 0x21, 0xe8,
	0xff, 0x21, 0xef, 0xc3, 0x6d, 0xfd, 0x46, 0x1f, 0xe2, 0x81, 0xf7, 0xe4, 0x0a, 0x1e, 0xda, 0xa3,
	0xd3, 0xe3, 0x76, 0x8f, 0xc8, 0xb2, 0xcd, 0x81, 0x8d, 0x1d, 0xc7, 0xed, 0x3f, 0xb2, 0xec, 0x90,
	0xda, 0x2e, 0x91, 0x56, 0x0a, 0x7c, 0x6b, 0xe9, 0x36, 0x0e, 0x56, 0x38, 0xe0, 0x24, 0x7a, 0x40,
	0xd2, 0x9f, 0x53, 0x20, 0x9e, 0x62, 0x12, 0xee, 0x18, 0xa8, 0x9f, 0x32, 0xfe, 0x40, 0x5f, 0x4d,
	0xf3, 0xf2, 0xd2, 0xc1, 0xc4, 0x6b, 0x91, 0xf8, 0x8a, 0xd6, 0xbf, 0xa1, 0x3e, 0xd2, 0x89, 0xeb,
	0x0c, 0x5f, 0x78, 0x11, 0x98, 0x9b, 0x44, 0x60, 0xf2, 0xf6, 0x64, 0x3e, 0xe6, 0xed, 0x91, 0x7e,
	0x9b, 0x82, 0xc2, 0x14, 0xa3, 0x1c, 0x0b, 0xb5, 0x61, 0xc3, 0x7f, 0x47, 0x78, 0x7e, 0xf0, 0x03,
	0x4d, 0xb1, 0x03, 0x9d, 0xaa, 0xc3, 0xcd, 0x10, 0xa4, 0xc6, 0xa4, 0x52, 0x7f, 0x88, 0x49, 0xd4,
	0xa1, 0xe7, 0x0f, 0x5b, 0x48, 0x75, 0xd8, 0x88, 0x19, 0x41, 0xa3, 0x72, 0xef, 0x3b, 0xe6, 0x06,
	0x22, 0x3d, 0x69, 0xca, 0x7e, 0x0e, 0x9b, 0x09, 0xa2, 0x1c, 0x0b, 0x9d, 0xc2, 0x5a, 0xc4, 0x17,
	0x26, 0xef, 0x7e, 0x37, 0x72, 0x61, 0x37, 0xa4, 0x0b, 0xd8, 0xae, 0xb0, 0xce, 0x24, 0x6e, 0xef,
	0x27, 0xd3, 0x51, 0x04, 0x31, 0x59, 0x87, 0x63, 0x49, 0xe7, 0xb0, 0x5d, 0xc5, 0x43, 0x9c, 0xa4,
	0xff, 0xde, 0x78, 0x05, 0x9b, 0xb0, 0x74, 0xb8, 0x09, 0xa3, 0x3a, 0x93, 0xe5, 0x3a, 0x96, 0xf4,
	0x87, 0x14, 0x14, 0x2a, 0x43, 0x55, 0x1f, 0xb5, 0xa3, 0x97, 0xe9, 0x41, 0x6a, 0xc3, 0x3d, 0x45,
	0xfa, 0x23, 0x7a, 0x8a, 0x58, 0xa6, 0x4b, 0x18, 0x8a, 0xd3, 0x2c, 0xf9, 0x94, 0xa7, 0xfc, 0xfb,
	0x14, 0x6c, 0x9f, 0x8c, 0xef, 0xfe, 0xf7, 0xfe, 0xf6, 0x41, 0x4c, 0xb6, 0xe3, 0x53, 0x7a, 0xdb,
	0x81, 0x5d, 0x5a, 0x79, 0x3d, 0x54, 0xd7, 0xdd, 0xec, 0x6a, 0xa5, 0x3e, 0xc7, 0x8b, 0xee, 0x1e,
	0xac, 0xf8, 0x4a, 0xfc, 0x3b, 0x08, 0x1e, 0xa9, 0xae, 0x49, 0x4f, 0x60, 0x6f, 0xa6, 0x50, 0xc7,
	0x92, 0xfe, 0x92, 0x82, 0xc7, 0x95, 0x2b, 0xdc, 0xbf, 0xf6, 0xfb, 0x57, 0xce, 0x74, 0xab, 0x14,
	0xfe, 0x30, 0x7d, 0x78, 0x2c, 0x05, 0x46, 0x82, 0xc0, 0x53, 0xec, 0x16, 0x7d, 0x75, 0x52, 0xee,
	0xdc, 0xe7, 0xf8, 0x10, 0x7c, 0xe2, 0xe4, 0x49, 0xe6, 0x71, 0xf6, 0x83, 0xe8, 0x3e, 0xcb, 0xd2,
	0x3e, 0xec, 0xce, 0xb2, 0xca, 0xb1, 0xe8, 0x65, 0x09, 0x94, 0x99, 0x9e, 0xa5, 0xa9, 0x04, 0xb3,
	0xce, 0x1c, 0x7f, 0x90, 0x7e, 0x33, 0x1f, 0xaa, 0xa9, 0x41, 0xa6, 0x63, 0xa1, 0xcf, 0x61, 0xcb,
	0xb9, 0x32, 0x7f, 0xa9, 0x8c, 0x4c, 0x1b, 0x2b, 0x97, 0x43, 0x53, 0x25, 0xca, 0x50, 0xbd, 0x33,
	0xc7, 0xbc, 0xf2, 0x2f, 0xc9, 0xeb, 0x94, 0xdb, 0x30, 0x6d, 0xfc, 0x86, 0xf2, 0xce, 0x18, 0x8b,
	0x6e, 0x8a, 0xe1, 0x79, 0x07, 0xc9, 0x6f, 0xf1, 0xfa, 0x28, 0xbc, 0x81, 0xf5, 0x90, 0xdf, 0x87,
	0x42, 0x7c, 0x93, 0xd7, 0x3f, 0x70, 0xcf, 0xb7, 0x22, 0xfb, 0xbc, 0x2e, 0xe2, 0x18, 0x36, 0x99,
	0x91, 0xde, 0x33, 0x89, 0x0d, 0x62, 0xab, 0x86, 0x3b, 0x7a, 0xbb, 0x36, 0xba, 0x41, 0xa9, 0xb9,
	0x2c, 0xba, 0x27, 0x0a, 0xe7, 0x26, 0xce, 0x73, 0x13, 0xad, 0x30, 0x9e, 0x99, 0xf8, 0x05, 0x88,
	0xb1, 0x3d, 0x9e, 0x85, 0x0b, 0xdc, 0xc2, 0xc8, 0x36, 0xcf, 0xc2, 0x03, 0x10, 0x98, 0x85, 0xfd,
	0x2b, 0xd5, 0x30, 0xf0, 0x50, 0x19, 0x39, 0x03, 0x36, 0x23, 0x2c, 0xc9, 0x39, 0x4a, 0xaf, 0x70,
	0x72, 0xc3, 0x19, 0x50, 0x64, 0x00, 0xc4, 0x4d, 0xe2, 0x33, 0x6f, 0xae, 0xef, 0xa3, 0x98, 0x35,
	0x87, 0x90, 0x0f, 0x22, 0xfb, 0xe6, 0xd0, 0xb4, 0xdd, 0xe1, 0x77, 0x6d, 0x02, 0xad, 0x50, 0x32,
	0xda, 0x87, 0x55, 0xa6, 0xdf, 0xc6, 0x9a, 0xa2, 0x99, 0x84, 0x4d, 0x06, 0x4b, 0x32, 0x50, 0x9a,
	0x8c, 0xb5, 0xaa, 0xc9, 0xa4, 0xd9, 0x78, 0xa4, 0x1b, 0x5a, 0x30, 0x3d, 0x57, 0x78, 0xc2, 0x71,
	0xc6, 0x24, 0x39, 0x5f, 0x80, 0x4b, 0x9a, 0xa4, 0xe6, 0x2a, 0xef, 0x29, 0x39, 0xd9, 0xeb, 0x17,
	0x0f, 0x40, 0x18, 0xaa, 0x0e, 0x51, 0xc6, 0x2c, 0xa7, 0x82, 0xd3, 0x72, 0x8e, 0xd2, 0x79, 0xaa,
	0xb1, 0x14, 0xfe, 0x4f, 0x06, 0xc4, 0xce, 0x94, 0x0c, 0xfd, 0x26, 0x07, 0xbf, 0xc9, 0xc1, 0xaf,
	0x9d, 0x83, 0xd2, 0x0e, 0x14, 0x3a, 0xd3, 0xaa, 0x9b, 0xd4, 0x81, 0xb5, 0x53, 0x4c, 0xde, 0x5d,
	0xe9, 0x04, 0x7f, 0xbd, 0xd6, 0x16, 0x41, 0x86, 0x10, 0xf7, 0x05, 0x5c, 0x96, 0xd9, 0x6f, 0xe9,
	0x1f, 0x29, 0x10, 0xc2, 0x52, 0x1d, 0x0b, 0x7d, 0x09, 0x4b, 0x63, 0x5d, 0x0b, 0xf6, 0xa3, 0xcf,
	0xa3, 0x8f, 0x5e, 0x74, 0x4f, 0xa9, 0x4e, 0xf0, 0x48, 0x5e, 0x1c, 0xeb, 0xda, 0xf4, 0x5e, 0xb4,
	0xd8, 0x83, 0x0c, 0x85, 0x25, 0x3f, 0x78, 0xc1, 0xaf, 0x54, 0xe9, 0xd8, 0x57, 0xaa, 0x60, 0x77,
	0x35, 0x17, 0xe9, 0xae, 0x1a, 0xb0, 0xc1, 0xe6, 0x9d, 0x4e, 0x24, 0x3a, 0x85, 0x88, 0x1b, 0xd9,
	0x89, 0x7d, 0xb3, 0x9a, 0xb5, 0x6d, 0xd8, 0x4c, 0x10, 0xe7, 0x58, 0xd2, 0x11, 0x20, 0xde, 0xc5,
	0x3d, 0x50, 0x8b, 0xb4, 0x09, 0xeb, 0xb1, 0x0d, 0x8e, 0x75, 0xf8, 0xf7, 0x14, 0x2c, 0xfb, 0x1d,
	0x0a, 0x2a, 0xc2, 0x56, 0xfb, 0x6d, 0xb9, 0x53, 0x53, 0xba, 0xef, 0xdb, 0x35, 0xa5, 0xd7, 0xec,
	0xb4, 0x6b, 0x95, 0xfa, 0x9b, 0x7a, 0xad, 0x2a, 0x3c, 0x42, 0x9b, 0x90, 0x0f, 0xf0, 0xde, 0x95,
	0xe5, 0x46, 0xaf, 0x2d, 0xa4, 0x90, 0x08, 0x1b, 0x01, 0x72, 0xa5, 0xd5, 0xac, 0xd6, 0xbb, 0xf5,
	0x56, 0x53, 0x48, 0xa3, 0x6d, 0x58, 0x0f, 0x70, 0xca, 0x95, 0x6e, 0xfd, 0xbc, 0xde, 0x7d, 0x2f,
	0xcc, 0x45, 0x24, 0x75, 0x5b, 0x6d, 0xa5, 0xd7, 0x16, 0x32, 0x11, 0x7c, 0xbb, 0x27, 0x57, 0xe8,
	0x4a, 0x98, 0x8f, 0xe0, 0x6b, 0xcd, 0x6a, 0xbd, 0x79, 0x2a, 0x2c, 0x1c, 0xda, 0x20, 0x44, 0xbf,
	0x66, 0x20, 0x09, 0x76, 0xdb, 0x72, 0xad, 0x53, 0x6b, 0x76, 0x95, 0xd3, 0x5a, 0x57, 0x69, 0xd4,
	0xba, 0x6f, 0x5b, 0xd5, 0x88, 0x23, 0x8f, 0xa1, 0x90, 0x80, 0x69, 0x94, 0x9b, 0xbd, 0xf2, 0x99,
	0x90, 0x42, 0x3b, 0xb0, 0x9d, 0xc0, 0x2e, 0xf7, 0xba, 0x2d, 0x21, 0x7d, 0xf8, 0xc7, 0x14, 0x6c,
	0x24, 0x7d, 0xfc, 0x40, 0x2f, 0xe1, 0xa9, 0xb7, 0xeb, 0xbc, 0x7c, 0x56, 0xaf, 0x2a, 0xdd, 0x7a,
	0x23, 0x31, 0x8c, 0xcf, 0x60, 0x7f, 0x1a, 0xb0, 0xda, 0x93, 0xcb, 0x2c, 0x76, 0xa9, 0x99, 0xa8,
	0x5a, 0xb9, 0x7a, 0x56, 0x6f, 0xd6, 0x84, 0xf4, 0xe1, 0xdf, 0x52, 0xb0, 0x1a, 0xfc, 0xa8, 0x45,
	0x5d, 0x3b, 0xe9, 0x75, 0xbb, 0xad, 0x26, 0x0b, 0x77, 0xab, 0x19, 0xd1, 0x5d, 0x80, 0xcd, 0x30,
	0xbb, 0x5a, 0xef, 0x94, 0x4f, 0xce, 0x6a, 0x42, 0x2a, 0xce, 0x6a, 0x74, 0x4e, 0x95, 0x93, 0xd6,
	0x4f, 0x84, 0x34, 0xda, 0x83, 0x9d, 0x30, 0xcb, 0x3b, 0x4a, 0xe5, 0xab, 0x5e, 0xa3, 0x2d, 0xcc,
	0xa1, 0x2d, 0x40, 0x61, 0x00, 0xa3, 0x67, 0x0e, 0xff, 0x94, 0x9a, 0x7c, 0x38, 0xe7, 0x3d, 0x15,
	0x95, 0xe5, 0xef, 0xee, 0x74, 0xcb, 0xdd, 0x5e, 0x27, 0x62, 0x62, 0x02, 0xa0, 0xde, 0x54, 0xda,
	0x72, 0xeb, 0x54, 0xae, 0x75, 0x3a, 0x42, 0x2a, 0x09, 0xd0, 0x6c, 0x75, 0xe9, 0x4f, 0xb9, 0x5b,
	0xab, 0x0a, 0x69, 0xea, 0x49, 0x14, 0x50, 0x6b, 0x56, 0x6b, 0x55, 0x61, 0xee, 0xf0, 0x9f, 0x29,
	0xc8, 0x86, 0xfe, 0xfc, 0x81, 0x76, 0xa1, 0xe8, 0xa7, 0x6c, 0xd2, 0x69, 0x15, 0x60, 0x33, 0xc2,
	0x77, 0xd3, 0x95, 0x19, 0x12, 0x61, 0x75, 0x6a, 0xcd, 0xaa, 0xe2, 0x1e, 0x9b, 0x90, 0x46, 0x4f,
	0xe0, 0x71, 0x04, 0xd0, 0x6c, 0x9d, 0xd4, 0xcf, 0xa8, 0x5d, 0x67, 0xb5, 0xf3, 0xda, 0x99, 0x30,
	0x47, 0xcf, 0x2b, 0x02, 0x91, 0xeb, 0x95, 0xb7, 0x2e, 0x3b, 0x43, 0xaf, 0x63, 0x84, 0x5d, 0x69,
	0x35, 0x3b, 0xbd, 0x46, 0x4d, 0x98, 0x3f, 0xfe, 0x6b, 0x16, 0xb2, 0x5e, 0xab, 0xfd, 0x96, 0x96,
	0x41, 0x64, 0x24, 0x8c, 0xc4, 0xac, 0xc0, 0x1c, 0x24, 0x14, 0xcc, 0xc4, 0xcf, 0x13, 0xc5, 0xff,
	0x7b, 0x20, 0xd2, 0xb1, 0xa4, 0x47, 0xe8, 0x02, 0xf2, 0x31, 0x36, 0x7a, 0x76, 0xaf, 0x04, 0xaa,
	0xe7, 0xf9, 0x03, 0x50, 0x4c, 0xc7, 0x35, 0x6c, 0x24, 0x0d, 0xc8, 0xe8, 0x65, 0xec, 0x33, 0x5e,
	0xf2, 0xa8, 0x5e, 0x3c, 0x78, 0x18, 0xd0, 0x53, 0x96, 0x34, 0x19, 0xc7, 0x95, 0x4d, 0x99, 0xcb,
	0xe3, 0xca, 0xa6, 0x0e, 0xda, 0x8f, 0xd0, 0x07, 0xd8, 0x4a, 0x9e, 0x6f, 0x51, 0xec, 0x10, 0xa6,
	0x4e, 0xe4, 0xc5, 0xc3, 0x87, 0x42, 0x3d, 0xff, 0x92, 0x46, 0xcc, 0xb8, 0x7f, 0x53, 0x06, 0xe2,
	0xb8, 0x7f, 0xd3, 0x26, 0x56, 0xe9, 0x11, 0xfa, 0x75, 0x0a, 0x76, 0x66, 0x8c, 0x85, 0xa8, 0x14,
	0x4b, 0x81, 0x99, 0x83, 0x69, 0xf1, 0xe8, 0xa3, 0xf0, 0xcc, 0x84, 0x5f, 0x41, 0x71, 0xfa, 0x78,
	0x87, 0x5e, 0xc5, 0x62, 0x37, 0x6b, 0x40, 0x2d, 0x96, 0x3e, 0x06, 0xce, 0xd4, 0xbf, 0x83, 0xfd,
	0x53, 0x4c, 0x3c, 0xd3, 0x5a, 0xb6, 0x86, 0x6d, 0xf6, 0xe7, 0xd3, 0x93, 0x3b, 0xfa, 0x6a, 0xc8,
	0xaa, 0x31, 0xc0, 0xa8, 0x50, 0x92, 0xbd, 0xbf, 0xfa, 0x9e, 0x1f, 0x97, 0x7c, 0x3a, 0x55, 0xb8,
	0x15, 0x62, 0xb1, 0x8f, 0xb9, 0xae, 0xe0, 0xf7, 0xb0, 0x17, 0x15, 0x4c, 0xaf, 0xe5, 0x03, 0xe5,
	0x86, 0x59, 0x6c, 0x77, 0x5d, 0xf3, 0x6c, 0xfe, 0x11, 0xe4, 0xdf, 0xe8, 0xb7, 0xfe, 0x57, 0x51,
	0xc6, 0x45, 0x9f, 0x85, 0x76, 0xc8, 0xd8, 0x1a, 0xaa, 0x7d, 0xce, 0x8a, 0xdb, 0x59, 0x1b, 0x59,
	0xe4, 0xce, 0x15, 0x16, 0x2e, 0x48, 0x93, 0x0e, 0x72, 0x66, 0x41, 0x0a, 0x4d, 0x30, 0x33, 0x0b,
	0x52, 0xa4, 0x25, 0x65, 0xfa, 0x3a, 0x0f, 0xd3, 0xd7, 0x79, 0xb0, 0xbe, 0xce, 0x0c, 0x7d, 0x1d,
	0x58, 0x0d, 0xb6, 0x9e, 0x68, 0x6f, 0x76, 0x63, 0xfa, 0xa1, 0xb8, 0x7f, 0x5f, 0xe7, 0xca, 0xab,
	0x6a, 0xac, 0xe3, 0x8b, 0x57, 0xd5, 0xa4, 0x1e, 0x33, 0x5e, 0x55, 0x93, 0x5b, 0xc7, 0x47, 0xe8,
	0x67, 0xb0, 0x16, 0xe9, 0x05, 0x91, 0x94, 0x5c, 0xba, 0x42, 0xf2, 0x9f, 0xde, 0x8b, 0xa1, 0xd2,
	0x4f, 0x8e, 0x7f, 0xfa, 0xed, 0x81, 0x39, 0x54, 0x8d, 0x41, 0xe9, 0xf5, 0x31, 0x21, 0xa5, 0xbe,
	0x39, 0x3a, 0x62, 0xff, 0xcd, 0xd0, 0x37, 0x87, 0x47, 0x0e, 0xb6, 0x6f, 0xf4, 0x3e, 0x76, 0x22,
	0xff, 0x1f, 0x71, 0xb1, 0xc0, 0x10, 0x9f, 0xff, 0x37, 0x00, 0x00, 0xff, 0xff, 0x73, 0xb7, 0xe8,
	0x6e, 0x47, 0x21, 0x00, 0x00,
}
