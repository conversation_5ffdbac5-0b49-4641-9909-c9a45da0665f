// Code generated by protoc-gen-gogo.
// source: src/channellivesvr/channellive.proto
// DO NOT EDIT!

/*
	Package channellive is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/channellivesvr/channellive.proto

	It has these top-level messages:
		QueueUpMicApplyReq
		QueueUpMicApplyResp
		QueueUpMicHandleReq
		QueueUpMicHandleResp
		GetQueueUpMicApplyListReq
		GetQueueUpMicApplyListResp
		CleanQueueUpMicApplyListReq
		CleanQueueUpMicApplyListResp
*/
package channellive

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import io1 "io"
import fmt2 "fmt"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// 普通房间的排麦申请
type QueueUpMicApplyReq struct {
	ChannelId uint32 `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	OpUid     uint32 `protobuf:"varint,2,opt,name=op_uid,json=opUid,proto3" json:"op_uid,omitempty"`
	IsCancel  bool   `protobuf:"varint,3,opt,name=is_cancel,json=isCancel,proto3" json:"is_cancel,omitempty"`
}

func (m *QueueUpMicApplyReq) Reset()                    { *m = QueueUpMicApplyReq{} }
func (m *QueueUpMicApplyReq) String() string            { return proto.CompactTextString(m) }
func (*QueueUpMicApplyReq) ProtoMessage()               {}
func (*QueueUpMicApplyReq) Descriptor() ([]byte, []int) { return fileDescriptorChannellive, []int{0} }

func (m *QueueUpMicApplyReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *QueueUpMicApplyReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *QueueUpMicApplyReq) GetIsCancel() bool {
	if m != nil {
		return m.IsCancel
	}
	return false
}

type QueueUpMicApplyResp struct {
	ServerTimeMs  uint64 `protobuf:"varint,1,opt,name=server_time_ms,json=serverTimeMs,proto3" json:"server_time_ms,omitempty"`
	RaminApplyCnt uint32 `protobuf:"varint,2,opt,name=ramin_apply_cnt,json=raminApplyCnt,proto3" json:"ramin_apply_cnt,omitempty"`
	SuccessUid    uint32 `protobuf:"varint,3,opt,name=success_uid,json=successUid,proto3" json:"success_uid,omitempty"`
}

func (m *QueueUpMicApplyResp) Reset()                    { *m = QueueUpMicApplyResp{} }
func (m *QueueUpMicApplyResp) String() string            { return proto.CompactTextString(m) }
func (*QueueUpMicApplyResp) ProtoMessage()               {}
func (*QueueUpMicApplyResp) Descriptor() ([]byte, []int) { return fileDescriptorChannellive, []int{1} }

func (m *QueueUpMicApplyResp) GetServerTimeMs() uint64 {
	if m != nil {
		return m.ServerTimeMs
	}
	return 0
}

func (m *QueueUpMicApplyResp) GetRaminApplyCnt() uint32 {
	if m != nil {
		return m.RaminApplyCnt
	}
	return 0
}

func (m *QueueUpMicApplyResp) GetSuccessUid() uint32 {
	if m != nil {
		return m.SuccessUid
	}
	return 0
}

// 普通房间的排麦处理
type QueueUpMicHandleReq struct {
	ChannelId uint32 `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TargetUid uint32 `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	OpUid     uint32 `protobuf:"varint,3,opt,name=op_uid,json=opUid,proto3" json:"op_uid,omitempty"`
	IsAllow   bool   `protobuf:"varint,4,opt,name=is_allow,json=isAllow,proto3" json:"is_allow,omitempty"`
}

func (m *QueueUpMicHandleReq) Reset()                    { *m = QueueUpMicHandleReq{} }
func (m *QueueUpMicHandleReq) String() string            { return proto.CompactTextString(m) }
func (*QueueUpMicHandleReq) ProtoMessage()               {}
func (*QueueUpMicHandleReq) Descriptor() ([]byte, []int) { return fileDescriptorChannellive, []int{2} }

func (m *QueueUpMicHandleReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *QueueUpMicHandleReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *QueueUpMicHandleReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *QueueUpMicHandleReq) GetIsAllow() bool {
	if m != nil {
		return m.IsAllow
	}
	return false
}

type QueueUpMicHandleResp struct {
	ServerTimeMs  uint64 `protobuf:"varint,1,opt,name=server_time_ms,json=serverTimeMs,proto3" json:"server_time_ms,omitempty"`
	RaminApplyCnt uint32 `protobuf:"varint,2,opt,name=ramin_apply_cnt,json=raminApplyCnt,proto3" json:"ramin_apply_cnt,omitempty"`
	SuccessUid    uint32 `protobuf:"varint,3,opt,name=success_uid,json=successUid,proto3" json:"success_uid,omitempty"`
}

func (m *QueueUpMicHandleResp) Reset()                    { *m = QueueUpMicHandleResp{} }
func (m *QueueUpMicHandleResp) String() string            { return proto.CompactTextString(m) }
func (*QueueUpMicHandleResp) ProtoMessage()               {}
func (*QueueUpMicHandleResp) Descriptor() ([]byte, []int) { return fileDescriptorChannellive, []int{3} }

func (m *QueueUpMicHandleResp) GetServerTimeMs() uint64 {
	if m != nil {
		return m.ServerTimeMs
	}
	return 0
}

func (m *QueueUpMicHandleResp) GetRaminApplyCnt() uint32 {
	if m != nil {
		return m.RaminApplyCnt
	}
	return 0
}

func (m *QueueUpMicHandleResp) GetSuccessUid() uint32 {
	if m != nil {
		return m.SuccessUid
	}
	return 0
}

// 普通房间的排麦列表获取
type GetQueueUpMicApplyListReq struct {
	ChannelId uint32 `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Offset    uint32 `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	LimitCnt  uint32 `protobuf:"varint,3,opt,name=limit_cnt,json=limitCnt,proto3" json:"limit_cnt,omitempty"`
}

func (m *GetQueueUpMicApplyListReq) Reset()         { *m = GetQueueUpMicApplyListReq{} }
func (m *GetQueueUpMicApplyListReq) String() string { return proto.CompactTextString(m) }
func (*GetQueueUpMicApplyListReq) ProtoMessage()    {}
func (*GetQueueUpMicApplyListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannellive, []int{4}
}

func (m *GetQueueUpMicApplyListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetQueueUpMicApplyListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetQueueUpMicApplyListReq) GetLimitCnt() uint32 {
	if m != nil {
		return m.LimitCnt
	}
	return 0
}

type GetQueueUpMicApplyListResp struct {
	UidList     []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList" json:"uid_list,omitempty"`
	AllApplyCnt uint32   `protobuf:"varint,2,opt,name=all_apply_cnt,json=allApplyCnt,proto3" json:"all_apply_cnt,omitempty"`
}

func (m *GetQueueUpMicApplyListResp) Reset()         { *m = GetQueueUpMicApplyListResp{} }
func (m *GetQueueUpMicApplyListResp) String() string { return proto.CompactTextString(m) }
func (*GetQueueUpMicApplyListResp) ProtoMessage()    {}
func (*GetQueueUpMicApplyListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannellive, []int{5}
}

func (m *GetQueueUpMicApplyListResp) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetQueueUpMicApplyListResp) GetAllApplyCnt() uint32 {
	if m != nil {
		return m.AllApplyCnt
	}
	return 0
}

type CleanQueueUpMicApplyListReq struct {
	ChannelId uint32 `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	OpUid     uint32 `protobuf:"varint,2,opt,name=op_uid,json=opUid,proto3" json:"op_uid,omitempty"`
}

func (m *CleanQueueUpMicApplyListReq) Reset()         { *m = CleanQueueUpMicApplyListReq{} }
func (m *CleanQueueUpMicApplyListReq) String() string { return proto.CompactTextString(m) }
func (*CleanQueueUpMicApplyListReq) ProtoMessage()    {}
func (*CleanQueueUpMicApplyListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannellive, []int{6}
}

func (m *CleanQueueUpMicApplyListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CleanQueueUpMicApplyListReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

type CleanQueueUpMicApplyListResp struct {
}

func (m *CleanQueueUpMicApplyListResp) Reset()         { *m = CleanQueueUpMicApplyListResp{} }
func (m *CleanQueueUpMicApplyListResp) String() string { return proto.CompactTextString(m) }
func (*CleanQueueUpMicApplyListResp) ProtoMessage()    {}
func (*CleanQueueUpMicApplyListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannellive, []int{7}
}

func init() {
	proto.RegisterType((*QueueUpMicApplyReq)(nil), "channellive.QueueUpMicApplyReq")
	proto.RegisterType((*QueueUpMicApplyResp)(nil), "channellive.QueueUpMicApplyResp")
	proto.RegisterType((*QueueUpMicHandleReq)(nil), "channellive.QueueUpMicHandleReq")
	proto.RegisterType((*QueueUpMicHandleResp)(nil), "channellive.QueueUpMicHandleResp")
	proto.RegisterType((*GetQueueUpMicApplyListReq)(nil), "channellive.GetQueueUpMicApplyListReq")
	proto.RegisterType((*GetQueueUpMicApplyListResp)(nil), "channellive.GetQueueUpMicApplyListResp")
	proto.RegisterType((*CleanQueueUpMicApplyListReq)(nil), "channellive.CleanQueueUpMicApplyListReq")
	proto.RegisterType((*CleanQueueUpMicApplyListResp)(nil), "channellive.CleanQueueUpMicApplyListResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for ChannelLive service

type ChannelLiveClient interface {
	// 普通房间的排麦功能
	QueueUpMicApply(ctx context.Context, in *QueueUpMicApplyReq, opts ...grpc.CallOption) (*QueueUpMicApplyResp, error)
	QueueUpMicHandle(ctx context.Context, in *QueueUpMicHandleReq, opts ...grpc.CallOption) (*QueueUpMicHandleResp, error)
	GetQueueUpMicApplyList(ctx context.Context, in *GetQueueUpMicApplyListReq, opts ...grpc.CallOption) (*GetQueueUpMicApplyListResp, error)
	CleanQueueUpMicApplyList(ctx context.Context, in *CleanQueueUpMicApplyListReq, opts ...grpc.CallOption) (*CleanQueueUpMicApplyListResp, error)
}

type channelLiveClient struct {
	cc *grpc.ClientConn
}

func NewChannelLiveClient(cc *grpc.ClientConn) ChannelLiveClient {
	return &channelLiveClient{cc}
}

func (c *channelLiveClient) QueueUpMicApply(ctx context.Context, in *QueueUpMicApplyReq, opts ...grpc.CallOption) (*QueueUpMicApplyResp, error) {
	out := new(QueueUpMicApplyResp)
	err := grpc.Invoke(ctx, "/channellive.ChannelLive/QueueUpMicApply", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveClient) QueueUpMicHandle(ctx context.Context, in *QueueUpMicHandleReq, opts ...grpc.CallOption) (*QueueUpMicHandleResp, error) {
	out := new(QueueUpMicHandleResp)
	err := grpc.Invoke(ctx, "/channellive.ChannelLive/QueueUpMicHandle", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveClient) GetQueueUpMicApplyList(ctx context.Context, in *GetQueueUpMicApplyListReq, opts ...grpc.CallOption) (*GetQueueUpMicApplyListResp, error) {
	out := new(GetQueueUpMicApplyListResp)
	err := grpc.Invoke(ctx, "/channellive.ChannelLive/GetQueueUpMicApplyList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveClient) CleanQueueUpMicApplyList(ctx context.Context, in *CleanQueueUpMicApplyListReq, opts ...grpc.CallOption) (*CleanQueueUpMicApplyListResp, error) {
	out := new(CleanQueueUpMicApplyListResp)
	err := grpc.Invoke(ctx, "/channellive.ChannelLive/CleanQueueUpMicApplyList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for ChannelLive service

type ChannelLiveServer interface {
	// 普通房间的排麦功能
	QueueUpMicApply(context.Context, *QueueUpMicApplyReq) (*QueueUpMicApplyResp, error)
	QueueUpMicHandle(context.Context, *QueueUpMicHandleReq) (*QueueUpMicHandleResp, error)
	GetQueueUpMicApplyList(context.Context, *GetQueueUpMicApplyListReq) (*GetQueueUpMicApplyListResp, error)
	CleanQueueUpMicApplyList(context.Context, *CleanQueueUpMicApplyListReq) (*CleanQueueUpMicApplyListResp, error)
}

func RegisterChannelLiveServer(s *grpc.Server, srv ChannelLiveServer) {
	s.RegisterService(&_ChannelLive_serviceDesc, srv)
}

func _ChannelLive_QueueUpMicApply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueueUpMicApplyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveServer).QueueUpMicApply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellive.ChannelLive/QueueUpMicApply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveServer).QueueUpMicApply(ctx, req.(*QueueUpMicApplyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLive_QueueUpMicHandle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueueUpMicHandleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveServer).QueueUpMicHandle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellive.ChannelLive/QueueUpMicHandle",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveServer).QueueUpMicHandle(ctx, req.(*QueueUpMicHandleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLive_GetQueueUpMicApplyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetQueueUpMicApplyListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveServer).GetQueueUpMicApplyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellive.ChannelLive/GetQueueUpMicApplyList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveServer).GetQueueUpMicApplyList(ctx, req.(*GetQueueUpMicApplyListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLive_CleanQueueUpMicApplyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CleanQueueUpMicApplyListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveServer).CleanQueueUpMicApplyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellive.ChannelLive/CleanQueueUpMicApplyList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveServer).CleanQueueUpMicApplyList(ctx, req.(*CleanQueueUpMicApplyListReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelLive_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channellive.ChannelLive",
	HandlerType: (*ChannelLiveServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueueUpMicApply",
			Handler:    _ChannelLive_QueueUpMicApply_Handler,
		},
		{
			MethodName: "QueueUpMicHandle",
			Handler:    _ChannelLive_QueueUpMicHandle_Handler,
		},
		{
			MethodName: "GetQueueUpMicApplyList",
			Handler:    _ChannelLive_GetQueueUpMicApplyList_Handler,
		},
		{
			MethodName: "CleanQueueUpMicApplyList",
			Handler:    _ChannelLive_CleanQueueUpMicApplyList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/channellivesvr/channellive.proto",
}

func (m *QueueUpMicApplyReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QueueUpMicApplyReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ChannelId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintChannellive(dAtA, i, uint64(m.ChannelId))
	}
	if m.OpUid != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintChannellive(dAtA, i, uint64(m.OpUid))
	}
	if m.IsCancel {
		dAtA[i] = 0x18
		i++
		if m.IsCancel {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	return i, nil
}

func (m *QueueUpMicApplyResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QueueUpMicApplyResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ServerTimeMs != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintChannellive(dAtA, i, uint64(m.ServerTimeMs))
	}
	if m.RaminApplyCnt != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintChannellive(dAtA, i, uint64(m.RaminApplyCnt))
	}
	if m.SuccessUid != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintChannellive(dAtA, i, uint64(m.SuccessUid))
	}
	return i, nil
}

func (m *QueueUpMicHandleReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QueueUpMicHandleReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ChannelId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintChannellive(dAtA, i, uint64(m.ChannelId))
	}
	if m.TargetUid != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintChannellive(dAtA, i, uint64(m.TargetUid))
	}
	if m.OpUid != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintChannellive(dAtA, i, uint64(m.OpUid))
	}
	if m.IsAllow {
		dAtA[i] = 0x20
		i++
		if m.IsAllow {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	return i, nil
}

func (m *QueueUpMicHandleResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QueueUpMicHandleResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ServerTimeMs != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintChannellive(dAtA, i, uint64(m.ServerTimeMs))
	}
	if m.RaminApplyCnt != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintChannellive(dAtA, i, uint64(m.RaminApplyCnt))
	}
	if m.SuccessUid != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintChannellive(dAtA, i, uint64(m.SuccessUid))
	}
	return i, nil
}

func (m *GetQueueUpMicApplyListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetQueueUpMicApplyListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ChannelId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintChannellive(dAtA, i, uint64(m.ChannelId))
	}
	if m.Offset != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintChannellive(dAtA, i, uint64(m.Offset))
	}
	if m.LimitCnt != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintChannellive(dAtA, i, uint64(m.LimitCnt))
	}
	return i, nil
}

func (m *GetQueueUpMicApplyListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetQueueUpMicApplyListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		dAtA2 := make([]byte, len(m.UidList)*10)
		var j1 int
		for _, num := range m.UidList {
			for num >= 1<<7 {
				dAtA2[j1] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j1++
			}
			dAtA2[j1] = uint8(num)
			j1++
		}
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannellive(dAtA, i, uint64(j1))
		i += copy(dAtA[i:], dAtA2[:j1])
	}
	if m.AllApplyCnt != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintChannellive(dAtA, i, uint64(m.AllApplyCnt))
	}
	return i, nil
}

func (m *CleanQueueUpMicApplyListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CleanQueueUpMicApplyListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ChannelId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintChannellive(dAtA, i, uint64(m.ChannelId))
	}
	if m.OpUid != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintChannellive(dAtA, i, uint64(m.OpUid))
	}
	return i, nil
}

func (m *CleanQueueUpMicApplyListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CleanQueueUpMicApplyListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func encodeFixed64Channellive(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Channellive(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintChannellive(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *QueueUpMicApplyReq) Size() (n int) {
	var l int
	_ = l
	if m.ChannelId != 0 {
		n += 1 + sovChannellive(uint64(m.ChannelId))
	}
	if m.OpUid != 0 {
		n += 1 + sovChannellive(uint64(m.OpUid))
	}
	if m.IsCancel {
		n += 2
	}
	return n
}

func (m *QueueUpMicApplyResp) Size() (n int) {
	var l int
	_ = l
	if m.ServerTimeMs != 0 {
		n += 1 + sovChannellive(uint64(m.ServerTimeMs))
	}
	if m.RaminApplyCnt != 0 {
		n += 1 + sovChannellive(uint64(m.RaminApplyCnt))
	}
	if m.SuccessUid != 0 {
		n += 1 + sovChannellive(uint64(m.SuccessUid))
	}
	return n
}

func (m *QueueUpMicHandleReq) Size() (n int) {
	var l int
	_ = l
	if m.ChannelId != 0 {
		n += 1 + sovChannellive(uint64(m.ChannelId))
	}
	if m.TargetUid != 0 {
		n += 1 + sovChannellive(uint64(m.TargetUid))
	}
	if m.OpUid != 0 {
		n += 1 + sovChannellive(uint64(m.OpUid))
	}
	if m.IsAllow {
		n += 2
	}
	return n
}

func (m *QueueUpMicHandleResp) Size() (n int) {
	var l int
	_ = l
	if m.ServerTimeMs != 0 {
		n += 1 + sovChannellive(uint64(m.ServerTimeMs))
	}
	if m.RaminApplyCnt != 0 {
		n += 1 + sovChannellive(uint64(m.RaminApplyCnt))
	}
	if m.SuccessUid != 0 {
		n += 1 + sovChannellive(uint64(m.SuccessUid))
	}
	return n
}

func (m *GetQueueUpMicApplyListReq) Size() (n int) {
	var l int
	_ = l
	if m.ChannelId != 0 {
		n += 1 + sovChannellive(uint64(m.ChannelId))
	}
	if m.Offset != 0 {
		n += 1 + sovChannellive(uint64(m.Offset))
	}
	if m.LimitCnt != 0 {
		n += 1 + sovChannellive(uint64(m.LimitCnt))
	}
	return n
}

func (m *GetQueueUpMicApplyListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		l = 0
		for _, e := range m.UidList {
			l += sovChannellive(uint64(e))
		}
		n += 1 + sovChannellive(uint64(l)) + l
	}
	if m.AllApplyCnt != 0 {
		n += 1 + sovChannellive(uint64(m.AllApplyCnt))
	}
	return n
}

func (m *CleanQueueUpMicApplyListReq) Size() (n int) {
	var l int
	_ = l
	if m.ChannelId != 0 {
		n += 1 + sovChannellive(uint64(m.ChannelId))
	}
	if m.OpUid != 0 {
		n += 1 + sovChannellive(uint64(m.OpUid))
	}
	return n
}

func (m *CleanQueueUpMicApplyListResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func sovChannellive(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozChannellive(x uint64) (n int) {
	return sovChannellive(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *QueueUpMicApplyReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannellive
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: QueueUpMicApplyReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: QueueUpMicApplyReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannellive
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannellive
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsCancel", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannellive
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsCancel = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipChannellive(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannellive
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *QueueUpMicApplyResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannellive
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: QueueUpMicApplyResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: QueueUpMicApplyResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ServerTimeMs", wireType)
			}
			m.ServerTimeMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannellive
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ServerTimeMs |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RaminApplyCnt", wireType)
			}
			m.RaminApplyCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannellive
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RaminApplyCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SuccessUid", wireType)
			}
			m.SuccessUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannellive
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SuccessUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannellive(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannellive
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *QueueUpMicHandleReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannellive
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: QueueUpMicHandleReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: QueueUpMicHandleReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannellive
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannellive
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannellive
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsAllow", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannellive
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsAllow = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipChannellive(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannellive
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *QueueUpMicHandleResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannellive
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: QueueUpMicHandleResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: QueueUpMicHandleResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ServerTimeMs", wireType)
			}
			m.ServerTimeMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannellive
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ServerTimeMs |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RaminApplyCnt", wireType)
			}
			m.RaminApplyCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannellive
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RaminApplyCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SuccessUid", wireType)
			}
			m.SuccessUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannellive
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SuccessUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannellive(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannellive
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetQueueUpMicApplyListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannellive
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetQueueUpMicApplyListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetQueueUpMicApplyListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannellive
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannellive
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LimitCnt", wireType)
			}
			m.LimitCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannellive
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LimitCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannellive(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannellive
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetQueueUpMicApplyListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannellive
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetQueueUpMicApplyListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetQueueUpMicApplyListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannellive
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannellive
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannellive
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannellive
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AllApplyCnt", wireType)
			}
			m.AllApplyCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannellive
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AllApplyCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannellive(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannellive
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CleanQueueUpMicApplyListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannellive
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CleanQueueUpMicApplyListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CleanQueueUpMicApplyListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannellive
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannellive
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannellive(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannellive
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CleanQueueUpMicApplyListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannellive
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CleanQueueUpMicApplyListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CleanQueueUpMicApplyListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannellive(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannellive
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipChannellive(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowChannellive
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChannellive
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChannellive
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthChannellive
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowChannellive
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipChannellive(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthChannellive = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowChannellive   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/channellivesvr/channellive.proto", fileDescriptorChannellive) }

var fileDescriptorChannellive = []byte{
	// 658 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x94, 0x41, 0x4f, 0x13, 0x41,
	0x14, 0xc7, 0x5d, 0x16, 0x4a, 0x79, 0xb5, 0x40, 0x46, 0x25, 0x65, 0x91, 0x76, 0xd8, 0x20, 0x96,
	0x43, 0x21, 0xd1, 0x93, 0x4d, 0xb3, 0x09, 0xd6, 0x44, 0x4d, 0x20, 0xd1, 0x2a, 0x27, 0x0f, 0x9b,
	0x65, 0x77, 0xc0, 0x09, 0xb3, 0xbb, 0xe3, 0xce, 0x6c, 0x85, 0x9b, 0x07, 0x4d, 0x8c, 0x27, 0xa2,
	0x7e, 0x84, 0x7a, 0xf0, 0x9b, 0x78, 0xf4, 0x23, 0x18, 0xbc, 0xf4, 0x0b, 0x78, 0x37, 0x3b, 0xbb,
	0xc5, 0x96, 0x52, 0x6c, 0x3c, 0x78, 0x7c, 0x6f, 0xdf, 0xbc, 0xf7, 0xfb, 0xbf, 0xfe, 0x5f, 0x61,
	0x55, 0x44, 0xee, 0xa6, 0xfb, 0xd2, 0x09, 0x02, 0xc2, 0x18, 0x6d, 0x13, 0xd1, 0x8e, 0xfa, 0xc3,
	0x0d, 0x1e, 0x85, 0x32, 0x44, 0x85, 0xbe, 0x94, 0xb1, 0xea, 0x86, 0xbe, 0x1f, 0x06, 0x9b, 0x92,
	0xb5, 0x39, 0x75, 0x0f, 0x19, 0xd9, 0x14, 0x87, 0x7b, 0x31, 0x65, 0x92, 0x06, 0xf2, 0x98, 0x67,
	0x4f, 0xcc, 0x03, 0x40, 0x4f, 0x63, 0x12, 0x93, 0x5d, 0xbe, 0x43, 0xdd, 0x2d, 0xce, 0xd9, 0x71,
	0x8b, 0xbc, 0x42, 0xcb, 0x00, 0x59, 0x2b, 0x9b, 0x7a, 0x25, 0x0d, 0x6b, 0xd5, 0x62, 0x6b, 0x26,
	0xcb, 0x3c, 0xf6, 0xd0, 0x0d, 0xc8, 0x85, 0xdc, 0x8e, 0xa9, 0x57, 0x9a, 0x50, 0x9f, 0xa6, 0x42,
	0xbe, 0x4b, 0x3d, 0xb4, 0x04, 0x33, 0x54, 0xd8, 0xae, 0x13, 0xb8, 0x84, 0x95, 0x74, 0xac, 0x55,
	0xf3, 0xad, 0x3c, 0x15, 0x4d, 0x15, 0x9b, 0x6f, 0x35, 0xb8, 0x36, 0x34, 0x49, 0x70, 0xb4, 0x0a,
	0xb3, 0x82, 0x44, 0x6d, 0x12, 0xd9, 0x92, 0xfa, 0xc4, 0xf6, 0x85, 0x1a, 0x37, 0xd9, 0xba, 0x9a,
	0x66, 0x9f, 0x53, 0x9f, 0xec, 0x08, 0xb4, 0x06, 0x73, 0x91, 0xe3, 0xd3, 0xc0, 0x76, 0x92, 0x87,
	0xb6, 0x1b, 0xc8, 0x6c, 0x74, 0x51, 0xa5, 0x55, 0xbb, 0x66, 0x20, 0x51, 0x05, 0x0a, 0x22, 0x76,
	0x5d, 0x22, 0x84, 0xc2, 0xd3, 0x55, 0x0d, 0x64, 0xa9, 0x5d, 0xea, 0x99, 0xef, 0x06, 0x30, 0x1e,
	0x39, 0x81, 0xc7, 0xc8, 0x18, 0x8a, 0x97, 0x01, 0xa4, 0x13, 0x1d, 0x10, 0xd9, 0xa7, 0x7a, 0x26,
	0xcd, 0x24, 0xca, 0xff, 0x2c, 0x44, 0xef, 0x5f, 0xc8, 0x22, 0xe4, 0xa9, 0xb0, 0x1d, 0xc6, 0xc2,
	0xd7, 0xa5, 0x49, 0xb5, 0x8f, 0x69, 0x2a, 0xb6, 0x92, 0x30, 0xe1, 0xb8, 0x3e, 0xcc, 0xf1, 0xff,
	0xf7, 0x11, 0xc2, 0xe2, 0x43, 0x22, 0xcf, 0xfd, 0x30, 0xdb, 0x54, 0xc8, 0x31, 0x96, 0xb2, 0x00,
	0xb9, 0x70, 0x7f, 0x5f, 0x90, 0xde, 0xec, 0x2c, 0x4a, 0x7c, 0xc0, 0xa8, 0x4f, 0xa5, 0xc2, 0x4a,
	0x47, 0xe6, 0x55, 0xa2, 0x19, 0x48, 0xf3, 0x05, 0x18, 0xa3, 0x06, 0x0a, 0x9e, 0x6c, 0x2c, 0xa6,
	0x9e, 0xcd, 0xa8, 0x90, 0x25, 0x0d, 0xeb, 0xd5, 0x62, 0x6b, 0x3a, 0xa6, 0x5e, 0xf2, 0x19, 0x99,
	0x50, 0x74, 0x18, 0x1b, 0x12, 0x5c, 0x70, 0x18, 0xeb, 0xc9, 0x35, 0x9f, 0xc1, 0x52, 0x93, 0x11,
	0x27, 0xf8, 0x37, 0x3d, 0x17, 0xdb, 0xda, 0x2c, 0xc3, 0xcd, 0xd1, 0x4d, 0x05, 0xbf, 0xf3, 0x69,
	0x0a, 0x0a, 0xcd, 0xb4, 0xc9, 0x36, 0x6d, 0x13, 0xf4, 0x59, 0x83, 0xb9, 0x73, 0xb5, 0xa8, 0xb2,
	0xd1, 0x7f, 0xad, 0xc3, 0x17, 0x67, 0xe0, 0xcb, 0x0b, 0x04, 0x37, 0xad, 0x37, 0x9d, 0xae, 0x5e,
	0xfe, 0xd0, 0xe9, 0xea, 0xb9, 0xa3, 0x7a, 0x58, 0xe7, 0xf5, 0x8f, 0x9d, 0xae, 0xbe, 0x5e, 0x3b,
	0xc2, 0x8d, 0x9e, 0x82, 0x07, 0x16, 0xae, 0x85, 0xb8, 0x91, 0x6a, 0xb0, 0x70, 0x8d, 0xe3, 0x06,
	0x15, 0x38, 0xbd, 0x47, 0x0b, 0x7d, 0xd5, 0x60, 0xfe, 0xbc, 0xe3, 0xd0, 0xa8, 0xb1, 0x67, 0x87,
	0x61, 0xac, 0xfc, 0xa5, 0x42, 0x70, 0xf3, 0x49, 0x42, 0x56, 0x49, 0xc8, 0xf2, 0x09, 0x99, 0xcc,
	0xd8, 0xee, 0x5d, 0xc6, 0x26, 0x71, 0x23, 0x3d, 0x1f, 0xac, 0xe2, 0x1e, 0xac, 0xba, 0x15, 0x0b,
	0x7d, 0xd1, 0x60, 0xe1, 0x62, 0x97, 0xa0, 0xb5, 0x01, 0x9e, 0x91, 0xde, 0x35, 0x6e, 0x8f, 0x55,
	0x27, 0xb8, 0x59, 0x4f, 0xe8, 0xf1, 0xd9, 0x5e, 0x99, 0x62, 0xbf, 0x75, 0x11, 0xbb, 0x72, 0xb7,
	0x85, 0x6b, 0x0c, 0x37, 0x94, 0x9d, 0x2d, 0x74, 0xa2, 0x41, 0x69, 0x94, 0x37, 0x50, 0x75, 0x80,
	0xe0, 0x12, 0x5f, 0x1a, 0xeb, 0x63, 0x56, 0x0a, 0x6e, 0x56, 0x12, 0xda, 0x95, 0x84, 0x76, 0xe2,
	0x48, 0x91, 0xce, 0x0e, 0x92, 0x1a, 0xb9, 0xf7, 0x9d, 0xae, 0xfe, 0xab, 0x7d, 0x7f, 0xfe, 0xdb,
	0x69, 0x59, 0xfb, 0x7e, 0x5a, 0xd6, 0x7e, 0x9c, 0x96, 0xb5, 0x93, 0x9f, 0xe5, 0x2b, 0x7b, 0x39,
	0xf5, 0x8f, 0x7f, 0xf7, 0x77, 0x00, 0x00, 0x00, 0xff, 0xff, 0x0e, 0x68, 0x62, 0x21, 0x4c, 0x06,
	0x00, 0x00,
}
