// Code generated by protoc-gen-go. DO NOT EDIT.
// source: officialcert-go/officialcert-go.proto

package officialcert_go // import "golang.52tt.com/protocol/services/officialcert-go"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/extension/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 枚举值为 1 2 4 8 16 32 ... 位运算
type ECertAttrType int32

const (
	ECertAttrType_ECertAttrTypeNone      ECertAttrType = 0
	ECertAttrType_ECertAttrTypeImNoLimit ECertAttrType = 1
)

var ECertAttrType_name = map[int32]string{
	0: "ECertAttrTypeNone",
	1: "ECertAttrTypeImNoLimit",
}
var ECertAttrType_value = map[string]int32{
	"ECertAttrTypeNone":      0,
	"ECertAttrTypeImNoLimit": 1,
}

func (x ECertAttrType) String() string {
	return proto.EnumName(ECertAttrType_name, int32(x))
}
func (ECertAttrType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{0}
}

type LocationType int32

const (
	LocationType_Personal_Page    LocationType = 0
	LocationType_Channel          LocationType = 1
	LocationType_Anchor_Card      LocationType = 2
	LocationType_PersonalityDress LocationType = 3
)

var LocationType_name = map[int32]string{
	0: "Personal_Page",
	1: "Channel",
	2: "Anchor_Card",
	3: "PersonalityDress",
}
var LocationType_value = map[string]int32{
	"Personal_Page":    0,
	"Channel":          1,
	"Anchor_Card":      2,
	"PersonalityDress": 3,
}

func (x LocationType) String() string {
	return proto.EnumName(LocationType_name, int32(x))
}
func (LocationType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{1}
}

type STATUS_TYPE int32

const (
	STATUS_TYPE_STATUS_TYPE_UNUSE    STATUS_TYPE = 0
	STATUS_TYPE_STATUS_TYPE_USE      STATUS_TYPE = 1
	STATUS_TYPE_STATUS_TYPE_UNEFFECT STATUS_TYPE = 2
	STATUS_TYPE_STATUS_TYPE_DEL      STATUS_TYPE = 3
	STATUS_TYPE_STATUS_TYPE_OVERDUE  STATUS_TYPE = 4
)

var STATUS_TYPE_name = map[int32]string{
	0: "STATUS_TYPE_UNUSE",
	1: "STATUS_TYPE_USE",
	2: "STATUS_TYPE_UNEFFECT",
	3: "STATUS_TYPE_DEL",
	4: "STATUS_TYPE_OVERDUE",
}
var STATUS_TYPE_value = map[string]int32{
	"STATUS_TYPE_UNUSE":    0,
	"STATUS_TYPE_USE":      1,
	"STATUS_TYPE_UNEFFECT": 2,
	"STATUS_TYPE_DEL":      3,
	"STATUS_TYPE_OVERDUE":  4,
}

func (x STATUS_TYPE) String() string {
	return proto.EnumName(STATUS_TYPE_name, int32(x))
}
func (STATUS_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{2}
}

type QUERY_TYPE int32

const (
	QUERY_TYPE_QUERY_TYPE_VID QUERY_TYPE = 0
	QUERY_TYPE_QUERY_TYPE_UID QUERY_TYPE = 1
)

var QUERY_TYPE_name = map[int32]string{
	0: "QUERY_TYPE_VID",
	1: "QUERY_TYPE_UID",
}
var QUERY_TYPE_value = map[string]int32{
	"QUERY_TYPE_VID": 0,
	"QUERY_TYPE_UID": 1,
}

func (x QUERY_TYPE) String() string {
	return proto.EnumName(QUERY_TYPE_name, int32(x))
}
func (QUERY_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{3}
}

type QUERY_ALIAS_TYPE int32

const (
	QUERY_ALIAS_TYPE_QUERY_ALIAS_TYPE_UID       QUERY_ALIAS_TYPE = 0
	QUERY_ALIAS_TYPE_QUERY_ALIAS_TYPE_CHANNELID QUERY_ALIAS_TYPE = 1
)

var QUERY_ALIAS_TYPE_name = map[int32]string{
	0: "QUERY_ALIAS_TYPE_UID",
	1: "QUERY_ALIAS_TYPE_CHANNELID",
}
var QUERY_ALIAS_TYPE_value = map[string]int32{
	"QUERY_ALIAS_TYPE_UID":       0,
	"QUERY_ALIAS_TYPE_CHANNELID": 1,
}

func (x QUERY_ALIAS_TYPE) String() string {
	return proto.EnumName(QUERY_ALIAS_TYPE_name, int32(x))
}
func (QUERY_ALIAS_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{4}
}

type AliasSignStatus int32

const (
	AliasSignStatus_AliasSignStatusNone     AliasSignStatus = 0
	AliasSignStatus_AliasSignStatusActivate AliasSignStatus = 1
	AliasSignStatus_AliasSignStatusRecycled AliasSignStatus = 2
)

var AliasSignStatus_name = map[int32]string{
	0: "AliasSignStatusNone",
	1: "AliasSignStatusActivate",
	2: "AliasSignStatusRecycled",
}
var AliasSignStatus_value = map[string]int32{
	"AliasSignStatusNone":     0,
	"AliasSignStatusActivate": 1,
	"AliasSignStatusRecycled": 2,
}

func (x AliasSignStatus) String() string {
	return proto.EnumName(AliasSignStatus_name, int32(x))
}
func (AliasSignStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{5}
}

type AliasInfo_HandlerType int32

const (
	AliasInfo_HANDLER_TYPE_UNSPECIFIED AliasInfo_HandlerType = 0
	// 靓号更换
	AliasInfo_HANDLER_TYPE_UPDATE AliasInfo_HandlerType = 1
	// 创建账号
	AliasInfo_HANDLER_TYPE_CREATE AliasInfo_HandlerType = 2
)

var AliasInfo_HandlerType_name = map[int32]string{
	0: "HANDLER_TYPE_UNSPECIFIED",
	1: "HANDLER_TYPE_UPDATE",
	2: "HANDLER_TYPE_CREATE",
}
var AliasInfo_HandlerType_value = map[string]int32{
	"HANDLER_TYPE_UNSPECIFIED": 0,
	"HANDLER_TYPE_UPDATE":      1,
	"HANDLER_TYPE_CREATE":      2,
}

func (x AliasInfo_HandlerType) String() string {
	return proto.EnumName(AliasInfo_HandlerType_name, int32(x))
}
func (AliasInfo_HandlerType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{53, 0}
}

type BatchCheckAliasSignReq_CheckType int32

const (
	BatchCheckAliasSignReq_CheckTypeAdd     BatchCheckAliasSignReq_CheckType = 0
	BatchCheckAliasSignReq_CheckTypeRecycle BatchCheckAliasSignReq_CheckType = 1
)

var BatchCheckAliasSignReq_CheckType_name = map[int32]string{
	0: "CheckTypeAdd",
	1: "CheckTypeRecycle",
}
var BatchCheckAliasSignReq_CheckType_value = map[string]int32{
	"CheckTypeAdd":     0,
	"CheckTypeRecycle": 1,
}

func (x BatchCheckAliasSignReq_CheckType) String() string {
	return proto.EnumName(BatchCheckAliasSignReq_CheckType_name, int32(x))
}
func (BatchCheckAliasSignReq_CheckType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{61, 0}
}

type OfficialCertInfo struct {
	Uid       uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Title     string `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Intro     string `protobuf:"bytes,3,opt,name=intro,proto3" json:"intro,omitempty"`
	Style     string `protobuf:"bytes,4,opt,name=style,proto3" json:"style,omitempty"`
	Id        uint32 `protobuf:"varint,5,opt,name=id,proto3" json:"id,omitempty"`
	BeginTs   uint64 `protobuf:"varint,6,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs     uint64 `protobuf:"varint,7,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	IsUse     bool   `protobuf:"varint,8,opt,name=is_use,json=isUse,proto3" json:"is_use,omitempty"`
	Attribute uint32 `protobuf:"varint,9,opt,name=attribute,proto3" json:"attribute,omitempty"`
	// 大v图标动效
	CertifySpecialEffectTitle string   `protobuf:"bytes,10,opt,name=certify_special_effect_title,json=certifySpecialEffectTitle,proto3" json:"certify_special_effect_title,omitempty"`
	CertifySpecialEffectIcon  string   `protobuf:"bytes,11,opt,name=certify_special_effect_icon,json=certifySpecialEffectIcon,proto3" json:"certify_special_effect_icon,omitempty"`
	Status                    uint32   `protobuf:"varint,12,opt,name=status,proto3" json:"status,omitempty"`
	VId                       uint32   `protobuf:"varint,13,opt,name=v_id,json=vId,proto3" json:"v_id,omitempty"`
	Ttid                      string   `protobuf:"bytes,14,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Handler                   string   `protobuf:"bytes,15,opt,name=handler,proto3" json:"handler,omitempty"`
	XXX_NoUnkeyedLiteral      struct{} `json:"-"`
	XXX_unrecognized          []byte   `json:"-"`
	XXX_sizecache             int32    `json:"-"`
}

func (m *OfficialCertInfo) Reset()         { *m = OfficialCertInfo{} }
func (m *OfficialCertInfo) String() string { return proto.CompactTextString(m) }
func (*OfficialCertInfo) ProtoMessage()    {}
func (*OfficialCertInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{0}
}
func (m *OfficialCertInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfficialCertInfo.Unmarshal(m, b)
}
func (m *OfficialCertInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfficialCertInfo.Marshal(b, m, deterministic)
}
func (dst *OfficialCertInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfficialCertInfo.Merge(dst, src)
}
func (m *OfficialCertInfo) XXX_Size() int {
	return xxx_messageInfo_OfficialCertInfo.Size(m)
}
func (m *OfficialCertInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OfficialCertInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OfficialCertInfo proto.InternalMessageInfo

func (m *OfficialCertInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *OfficialCertInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *OfficialCertInfo) GetIntro() string {
	if m != nil {
		return m.Intro
	}
	return ""
}

func (m *OfficialCertInfo) GetStyle() string {
	if m != nil {
		return m.Style
	}
	return ""
}

func (m *OfficialCertInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *OfficialCertInfo) GetBeginTs() uint64 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *OfficialCertInfo) GetEndTs() uint64 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *OfficialCertInfo) GetIsUse() bool {
	if m != nil {
		return m.IsUse
	}
	return false
}

func (m *OfficialCertInfo) GetAttribute() uint32 {
	if m != nil {
		return m.Attribute
	}
	return 0
}

func (m *OfficialCertInfo) GetCertifySpecialEffectTitle() string {
	if m != nil {
		return m.CertifySpecialEffectTitle
	}
	return ""
}

func (m *OfficialCertInfo) GetCertifySpecialEffectIcon() string {
	if m != nil {
		return m.CertifySpecialEffectIcon
	}
	return ""
}

func (m *OfficialCertInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *OfficialCertInfo) GetVId() uint32 {
	if m != nil {
		return m.VId
	}
	return 0
}

func (m *OfficialCertInfo) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *OfficialCertInfo) GetHandler() string {
	if m != nil {
		return m.Handler
	}
	return ""
}

type UserOfficialCertInfo struct {
	IsCertified          bool                `protobuf:"varint,1,opt,name=is_certified,json=isCertified,proto3" json:"is_certified,omitempty"`
	Cert                 *OfficialCertInfo   `protobuf:"bytes,2,opt,name=cert,proto3" json:"cert,omitempty"`
	CertList             []*OfficialCertInfo `protobuf:"bytes,3,rep,name=cert_list,json=certList,proto3" json:"cert_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *UserOfficialCertInfo) Reset()         { *m = UserOfficialCertInfo{} }
func (m *UserOfficialCertInfo) String() string { return proto.CompactTextString(m) }
func (*UserOfficialCertInfo) ProtoMessage()    {}
func (*UserOfficialCertInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{1}
}
func (m *UserOfficialCertInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserOfficialCertInfo.Unmarshal(m, b)
}
func (m *UserOfficialCertInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserOfficialCertInfo.Marshal(b, m, deterministic)
}
func (dst *UserOfficialCertInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserOfficialCertInfo.Merge(dst, src)
}
func (m *UserOfficialCertInfo) XXX_Size() int {
	return xxx_messageInfo_UserOfficialCertInfo.Size(m)
}
func (m *UserOfficialCertInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserOfficialCertInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserOfficialCertInfo proto.InternalMessageInfo

func (m *UserOfficialCertInfo) GetIsCertified() bool {
	if m != nil {
		return m.IsCertified
	}
	return false
}

func (m *UserOfficialCertInfo) GetCert() *OfficialCertInfo {
	if m != nil {
		return m.Cert
	}
	return nil
}

func (m *UserOfficialCertInfo) GetCertList() []*OfficialCertInfo {
	if m != nil {
		return m.CertList
	}
	return nil
}

type GetUserOfficialCertReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RequestType          uint32   `protobuf:"varint,2,opt,name=request_type,json=requestType,proto3" json:"request_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserOfficialCertReq) Reset()         { *m = GetUserOfficialCertReq{} }
func (m *GetUserOfficialCertReq) String() string { return proto.CompactTextString(m) }
func (*GetUserOfficialCertReq) ProtoMessage()    {}
func (*GetUserOfficialCertReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{2}
}
func (m *GetUserOfficialCertReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserOfficialCertReq.Unmarshal(m, b)
}
func (m *GetUserOfficialCertReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserOfficialCertReq.Marshal(b, m, deterministic)
}
func (dst *GetUserOfficialCertReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserOfficialCertReq.Merge(dst, src)
}
func (m *GetUserOfficialCertReq) XXX_Size() int {
	return xxx_messageInfo_GetUserOfficialCertReq.Size(m)
}
func (m *GetUserOfficialCertReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserOfficialCertReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserOfficialCertReq proto.InternalMessageInfo

func (m *GetUserOfficialCertReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserOfficialCertReq) GetRequestType() uint32 {
	if m != nil {
		return m.RequestType
	}
	return 0
}

type GetUserOfficialCertResp struct {
	Info                 *UserOfficialCertInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetUserOfficialCertResp) Reset()         { *m = GetUserOfficialCertResp{} }
func (m *GetUserOfficialCertResp) String() string { return proto.CompactTextString(m) }
func (*GetUserOfficialCertResp) ProtoMessage()    {}
func (*GetUserOfficialCertResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{3}
}
func (m *GetUserOfficialCertResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserOfficialCertResp.Unmarshal(m, b)
}
func (m *GetUserOfficialCertResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserOfficialCertResp.Marshal(b, m, deterministic)
}
func (dst *GetUserOfficialCertResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserOfficialCertResp.Merge(dst, src)
}
func (m *GetUserOfficialCertResp) XXX_Size() int {
	return xxx_messageInfo_GetUserOfficialCertResp.Size(m)
}
func (m *GetUserOfficialCertResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserOfficialCertResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserOfficialCertResp proto.InternalMessageInfo

func (m *GetUserOfficialCertResp) GetInfo() *UserOfficialCertInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type ListUserOfficialCertReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListUserOfficialCertReq) Reset()         { *m = ListUserOfficialCertReq{} }
func (m *ListUserOfficialCertReq) String() string { return proto.CompactTextString(m) }
func (*ListUserOfficialCertReq) ProtoMessage()    {}
func (*ListUserOfficialCertReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{4}
}
func (m *ListUserOfficialCertReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListUserOfficialCertReq.Unmarshal(m, b)
}
func (m *ListUserOfficialCertReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListUserOfficialCertReq.Marshal(b, m, deterministic)
}
func (dst *ListUserOfficialCertReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListUserOfficialCertReq.Merge(dst, src)
}
func (m *ListUserOfficialCertReq) XXX_Size() int {
	return xxx_messageInfo_ListUserOfficialCertReq.Size(m)
}
func (m *ListUserOfficialCertReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListUserOfficialCertReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListUserOfficialCertReq proto.InternalMessageInfo

type ListUserOfficialCertResp struct {
	CertList             []*OfficialCertInfo `protobuf:"bytes,1,rep,name=cert_list,json=certList,proto3" json:"cert_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *ListUserOfficialCertResp) Reset()         { *m = ListUserOfficialCertResp{} }
func (m *ListUserOfficialCertResp) String() string { return proto.CompactTextString(m) }
func (*ListUserOfficialCertResp) ProtoMessage()    {}
func (*ListUserOfficialCertResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{5}
}
func (m *ListUserOfficialCertResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListUserOfficialCertResp.Unmarshal(m, b)
}
func (m *ListUserOfficialCertResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListUserOfficialCertResp.Marshal(b, m, deterministic)
}
func (dst *ListUserOfficialCertResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListUserOfficialCertResp.Merge(dst, src)
}
func (m *ListUserOfficialCertResp) XXX_Size() int {
	return xxx_messageInfo_ListUserOfficialCertResp.Size(m)
}
func (m *ListUserOfficialCertResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListUserOfficialCertResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListUserOfficialCertResp proto.InternalMessageInfo

func (m *ListUserOfficialCertResp) GetCertList() []*OfficialCertInfo {
	if m != nil {
		return m.CertList
	}
	return nil
}

type SetUserOfficialCertReq struct {
	Cert                 *OfficialCertInfo `protobuf:"bytes,1,opt,name=cert,proto3" json:"cert,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SetUserOfficialCertReq) Reset()         { *m = SetUserOfficialCertReq{} }
func (m *SetUserOfficialCertReq) String() string { return proto.CompactTextString(m) }
func (*SetUserOfficialCertReq) ProtoMessage()    {}
func (*SetUserOfficialCertReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{6}
}
func (m *SetUserOfficialCertReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserOfficialCertReq.Unmarshal(m, b)
}
func (m *SetUserOfficialCertReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserOfficialCertReq.Marshal(b, m, deterministic)
}
func (dst *SetUserOfficialCertReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserOfficialCertReq.Merge(dst, src)
}
func (m *SetUserOfficialCertReq) XXX_Size() int {
	return xxx_messageInfo_SetUserOfficialCertReq.Size(m)
}
func (m *SetUserOfficialCertReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserOfficialCertReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserOfficialCertReq proto.InternalMessageInfo

func (m *SetUserOfficialCertReq) GetCert() *OfficialCertInfo {
	if m != nil {
		return m.Cert
	}
	return nil
}

type SetUserOfficialCertResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserOfficialCertResp) Reset()         { *m = SetUserOfficialCertResp{} }
func (m *SetUserOfficialCertResp) String() string { return proto.CompactTextString(m) }
func (*SetUserOfficialCertResp) ProtoMessage()    {}
func (*SetUserOfficialCertResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{7}
}
func (m *SetUserOfficialCertResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserOfficialCertResp.Unmarshal(m, b)
}
func (m *SetUserOfficialCertResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserOfficialCertResp.Marshal(b, m, deterministic)
}
func (dst *SetUserOfficialCertResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserOfficialCertResp.Merge(dst, src)
}
func (m *SetUserOfficialCertResp) XXX_Size() int {
	return xxx_messageInfo_SetUserOfficialCertResp.Size(m)
}
func (m *SetUserOfficialCertResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserOfficialCertResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserOfficialCertResp proto.InternalMessageInfo

type DelUserOfficialCertReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Id                   uint32   `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelUserOfficialCertReq) Reset()         { *m = DelUserOfficialCertReq{} }
func (m *DelUserOfficialCertReq) String() string { return proto.CompactTextString(m) }
func (*DelUserOfficialCertReq) ProtoMessage()    {}
func (*DelUserOfficialCertReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{8}
}
func (m *DelUserOfficialCertReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelUserOfficialCertReq.Unmarshal(m, b)
}
func (m *DelUserOfficialCertReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelUserOfficialCertReq.Marshal(b, m, deterministic)
}
func (dst *DelUserOfficialCertReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelUserOfficialCertReq.Merge(dst, src)
}
func (m *DelUserOfficialCertReq) XXX_Size() int {
	return xxx_messageInfo_DelUserOfficialCertReq.Size(m)
}
func (m *DelUserOfficialCertReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelUserOfficialCertReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelUserOfficialCertReq proto.InternalMessageInfo

func (m *DelUserOfficialCertReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DelUserOfficialCertReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DelUserOfficialCertResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelUserOfficialCertResp) Reset()         { *m = DelUserOfficialCertResp{} }
func (m *DelUserOfficialCertResp) String() string { return proto.CompactTextString(m) }
func (*DelUserOfficialCertResp) ProtoMessage()    {}
func (*DelUserOfficialCertResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{9}
}
func (m *DelUserOfficialCertResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelUserOfficialCertResp.Unmarshal(m, b)
}
func (m *DelUserOfficialCertResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelUserOfficialCertResp.Marshal(b, m, deterministic)
}
func (dst *DelUserOfficialCertResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelUserOfficialCertResp.Merge(dst, src)
}
func (m *DelUserOfficialCertResp) XXX_Size() int {
	return xxx_messageInfo_DelUserOfficialCertResp.Size(m)
}
func (m *DelUserOfficialCertResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelUserOfficialCertResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelUserOfficialCertResp proto.InternalMessageInfo

type DelCertInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Id                   uint32   `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelCertInfo) Reset()         { *m = DelCertInfo{} }
func (m *DelCertInfo) String() string { return proto.CompactTextString(m) }
func (*DelCertInfo) ProtoMessage()    {}
func (*DelCertInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{10}
}
func (m *DelCertInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelCertInfo.Unmarshal(m, b)
}
func (m *DelCertInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelCertInfo.Marshal(b, m, deterministic)
}
func (dst *DelCertInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelCertInfo.Merge(dst, src)
}
func (m *DelCertInfo) XXX_Size() int {
	return xxx_messageInfo_DelCertInfo.Size(m)
}
func (m *DelCertInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DelCertInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DelCertInfo proto.InternalMessageInfo

func (m *DelCertInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DelCertInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type BatchDelOfficalCertsReq struct {
	InfoList             []*DelCertInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *BatchDelOfficalCertsReq) Reset()         { *m = BatchDelOfficalCertsReq{} }
func (m *BatchDelOfficalCertsReq) String() string { return proto.CompactTextString(m) }
func (*BatchDelOfficalCertsReq) ProtoMessage()    {}
func (*BatchDelOfficalCertsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{11}
}
func (m *BatchDelOfficalCertsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelOfficalCertsReq.Unmarshal(m, b)
}
func (m *BatchDelOfficalCertsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelOfficalCertsReq.Marshal(b, m, deterministic)
}
func (dst *BatchDelOfficalCertsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelOfficalCertsReq.Merge(dst, src)
}
func (m *BatchDelOfficalCertsReq) XXX_Size() int {
	return xxx_messageInfo_BatchDelOfficalCertsReq.Size(m)
}
func (m *BatchDelOfficalCertsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelOfficalCertsReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelOfficalCertsReq proto.InternalMessageInfo

func (m *BatchDelOfficalCertsReq) GetInfoList() []*DelCertInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type BatchDelOfficalCertsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDelOfficalCertsResp) Reset()         { *m = BatchDelOfficalCertsResp{} }
func (m *BatchDelOfficalCertsResp) String() string { return proto.CompactTextString(m) }
func (*BatchDelOfficalCertsResp) ProtoMessage()    {}
func (*BatchDelOfficalCertsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{12}
}
func (m *BatchDelOfficalCertsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelOfficalCertsResp.Unmarshal(m, b)
}
func (m *BatchDelOfficalCertsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelOfficalCertsResp.Marshal(b, m, deterministic)
}
func (dst *BatchDelOfficalCertsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelOfficalCertsResp.Merge(dst, src)
}
func (m *BatchDelOfficalCertsResp) XXX_Size() int {
	return xxx_messageInfo_BatchDelOfficalCertsResp.Size(m)
}
func (m *BatchDelOfficalCertsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelOfficalCertsResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelOfficalCertsResp proto.InternalMessageInfo

type BatchGetUserOfficialCertReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetUserOfficialCertReq) Reset()         { *m = BatchGetUserOfficialCertReq{} }
func (m *BatchGetUserOfficialCertReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserOfficialCertReq) ProtoMessage()    {}
func (*BatchGetUserOfficialCertReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{13}
}
func (m *BatchGetUserOfficialCertReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserOfficialCertReq.Unmarshal(m, b)
}
func (m *BatchGetUserOfficialCertReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserOfficialCertReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserOfficialCertReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserOfficialCertReq.Merge(dst, src)
}
func (m *BatchGetUserOfficialCertReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserOfficialCertReq.Size(m)
}
func (m *BatchGetUserOfficialCertReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserOfficialCertReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserOfficialCertReq proto.InternalMessageInfo

func (m *BatchGetUserOfficialCertReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchGetUserOfficialCertResp struct {
	InfoList             []*UserOfficialCertInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *BatchGetUserOfficialCertResp) Reset()         { *m = BatchGetUserOfficialCertResp{} }
func (m *BatchGetUserOfficialCertResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserOfficialCertResp) ProtoMessage()    {}
func (*BatchGetUserOfficialCertResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{14}
}
func (m *BatchGetUserOfficialCertResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserOfficialCertResp.Unmarshal(m, b)
}
func (m *BatchGetUserOfficialCertResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserOfficialCertResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserOfficialCertResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserOfficialCertResp.Merge(dst, src)
}
func (m *BatchGetUserOfficialCertResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserOfficialCertResp.Size(m)
}
func (m *BatchGetUserOfficialCertResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserOfficialCertResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserOfficialCertResp proto.InternalMessageInfo

func (m *BatchGetUserOfficialCertResp) GetInfoList() []*UserOfficialCertInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

// 设置用户佩戴的大v认证样式
type SetUserWearCertificationReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Id                   uint32   `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserWearCertificationReq) Reset()         { *m = SetUserWearCertificationReq{} }
func (m *SetUserWearCertificationReq) String() string { return proto.CompactTextString(m) }
func (*SetUserWearCertificationReq) ProtoMessage()    {}
func (*SetUserWearCertificationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{15}
}
func (m *SetUserWearCertificationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserWearCertificationReq.Unmarshal(m, b)
}
func (m *SetUserWearCertificationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserWearCertificationReq.Marshal(b, m, deterministic)
}
func (dst *SetUserWearCertificationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserWearCertificationReq.Merge(dst, src)
}
func (m *SetUserWearCertificationReq) XXX_Size() int {
	return xxx_messageInfo_SetUserWearCertificationReq.Size(m)
}
func (m *SetUserWearCertificationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserWearCertificationReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserWearCertificationReq proto.InternalMessageInfo

func (m *SetUserWearCertificationReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserWearCertificationReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type SetUserWearCertificationResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserWearCertificationResp) Reset()         { *m = SetUserWearCertificationResp{} }
func (m *SetUserWearCertificationResp) String() string { return proto.CompactTextString(m) }
func (*SetUserWearCertificationResp) ProtoMessage()    {}
func (*SetUserWearCertificationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{16}
}
func (m *SetUserWearCertificationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserWearCertificationResp.Unmarshal(m, b)
}
func (m *SetUserWearCertificationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserWearCertificationResp.Marshal(b, m, deterministic)
}
func (dst *SetUserWearCertificationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserWearCertificationResp.Merge(dst, src)
}
func (m *SetUserWearCertificationResp) XXX_Size() int {
	return xxx_messageInfo_SetUserWearCertificationResp.Size(m)
}
func (m *SetUserWearCertificationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserWearCertificationResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserWearCertificationResp proto.InternalMessageInfo

type GetUserAllOfficialCertsReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RequestType          uint32   `protobuf:"varint,2,opt,name=request_type,json=requestType,proto3" json:"request_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserAllOfficialCertsReq) Reset()         { *m = GetUserAllOfficialCertsReq{} }
func (m *GetUserAllOfficialCertsReq) String() string { return proto.CompactTextString(m) }
func (*GetUserAllOfficialCertsReq) ProtoMessage()    {}
func (*GetUserAllOfficialCertsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{17}
}
func (m *GetUserAllOfficialCertsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAllOfficialCertsReq.Unmarshal(m, b)
}
func (m *GetUserAllOfficialCertsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAllOfficialCertsReq.Marshal(b, m, deterministic)
}
func (dst *GetUserAllOfficialCertsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAllOfficialCertsReq.Merge(dst, src)
}
func (m *GetUserAllOfficialCertsReq) XXX_Size() int {
	return xxx_messageInfo_GetUserAllOfficialCertsReq.Size(m)
}
func (m *GetUserAllOfficialCertsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAllOfficialCertsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAllOfficialCertsReq proto.InternalMessageInfo

func (m *GetUserAllOfficialCertsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserAllOfficialCertsReq) GetRequestType() uint32 {
	if m != nil {
		return m.RequestType
	}
	return 0
}

type GetUserAllOfficialCertsResp struct {
	IsCertified          bool                `protobuf:"varint,1,opt,name=is_certified,json=isCertified,proto3" json:"is_certified,omitempty"`
	CertList             []*OfficialCertInfo `protobuf:"bytes,2,rep,name=cert_list,json=certList,proto3" json:"cert_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetUserAllOfficialCertsResp) Reset()         { *m = GetUserAllOfficialCertsResp{} }
func (m *GetUserAllOfficialCertsResp) String() string { return proto.CompactTextString(m) }
func (*GetUserAllOfficialCertsResp) ProtoMessage()    {}
func (*GetUserAllOfficialCertsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{18}
}
func (m *GetUserAllOfficialCertsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAllOfficialCertsResp.Unmarshal(m, b)
}
func (m *GetUserAllOfficialCertsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAllOfficialCertsResp.Marshal(b, m, deterministic)
}
func (dst *GetUserAllOfficialCertsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAllOfficialCertsResp.Merge(dst, src)
}
func (m *GetUserAllOfficialCertsResp) XXX_Size() int {
	return xxx_messageInfo_GetUserAllOfficialCertsResp.Size(m)
}
func (m *GetUserAllOfficialCertsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAllOfficialCertsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAllOfficialCertsResp proto.InternalMessageInfo

func (m *GetUserAllOfficialCertsResp) GetIsCertified() bool {
	if m != nil {
		return m.IsCertified
	}
	return false
}

func (m *GetUserAllOfficialCertsResp) GetCertList() []*OfficialCertInfo {
	if m != nil {
		return m.CertList
	}
	return nil
}

// 获取用户有效的特权属性
type GetUserCertAttributeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserCertAttributeReq) Reset()         { *m = GetUserCertAttributeReq{} }
func (m *GetUserCertAttributeReq) String() string { return proto.CompactTextString(m) }
func (*GetUserCertAttributeReq) ProtoMessage()    {}
func (*GetUserCertAttributeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{19}
}
func (m *GetUserCertAttributeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCertAttributeReq.Unmarshal(m, b)
}
func (m *GetUserCertAttributeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCertAttributeReq.Marshal(b, m, deterministic)
}
func (dst *GetUserCertAttributeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCertAttributeReq.Merge(dst, src)
}
func (m *GetUserCertAttributeReq) XXX_Size() int {
	return xxx_messageInfo_GetUserCertAttributeReq.Size(m)
}
func (m *GetUserCertAttributeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCertAttributeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCertAttributeReq proto.InternalMessageInfo

func (m *GetUserCertAttributeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

// 有效的全部特权 ECertAttrType
type GetUserCertAttributeResp struct {
	Attr                 uint32   `protobuf:"varint,1,opt,name=attr,proto3" json:"attr,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserCertAttributeResp) Reset()         { *m = GetUserCertAttributeResp{} }
func (m *GetUserCertAttributeResp) String() string { return proto.CompactTextString(m) }
func (*GetUserCertAttributeResp) ProtoMessage()    {}
func (*GetUserCertAttributeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{20}
}
func (m *GetUserCertAttributeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCertAttributeResp.Unmarshal(m, b)
}
func (m *GetUserCertAttributeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCertAttributeResp.Marshal(b, m, deterministic)
}
func (dst *GetUserCertAttributeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCertAttributeResp.Merge(dst, src)
}
func (m *GetUserCertAttributeResp) XXX_Size() int {
	return xxx_messageInfo_GetUserCertAttributeResp.Size(m)
}
func (m *GetUserCertAttributeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCertAttributeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCertAttributeResp proto.InternalMessageInfo

func (m *GetUserCertAttributeResp) GetAttr() uint32 {
	if m != nil {
		return m.Attr
	}
	return 0
}

// 查询是否有如下特权
type CheckUserCertAttributeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Attr                 uint32   `protobuf:"varint,2,opt,name=attr,proto3" json:"attr,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUserCertAttributeReq) Reset()         { *m = CheckUserCertAttributeReq{} }
func (m *CheckUserCertAttributeReq) String() string { return proto.CompactTextString(m) }
func (*CheckUserCertAttributeReq) ProtoMessage()    {}
func (*CheckUserCertAttributeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{21}
}
func (m *CheckUserCertAttributeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserCertAttributeReq.Unmarshal(m, b)
}
func (m *CheckUserCertAttributeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserCertAttributeReq.Marshal(b, m, deterministic)
}
func (dst *CheckUserCertAttributeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserCertAttributeReq.Merge(dst, src)
}
func (m *CheckUserCertAttributeReq) XXX_Size() int {
	return xxx_messageInfo_CheckUserCertAttributeReq.Size(m)
}
func (m *CheckUserCertAttributeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserCertAttributeReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserCertAttributeReq proto.InternalMessageInfo

func (m *CheckUserCertAttributeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckUserCertAttributeReq) GetAttr() uint32 {
	if m != nil {
		return m.Attr
	}
	return 0
}

type CheckUserCertAttributeResp struct {
	Ok                   bool     `protobuf:"varint,1,opt,name=ok,proto3" json:"ok,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUserCertAttributeResp) Reset()         { *m = CheckUserCertAttributeResp{} }
func (m *CheckUserCertAttributeResp) String() string { return proto.CompactTextString(m) }
func (*CheckUserCertAttributeResp) ProtoMessage()    {}
func (*CheckUserCertAttributeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{22}
}
func (m *CheckUserCertAttributeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserCertAttributeResp.Unmarshal(m, b)
}
func (m *CheckUserCertAttributeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserCertAttributeResp.Marshal(b, m, deterministic)
}
func (dst *CheckUserCertAttributeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserCertAttributeResp.Merge(dst, src)
}
func (m *CheckUserCertAttributeResp) XXX_Size() int {
	return xxx_messageInfo_CheckUserCertAttributeResp.Size(m)
}
func (m *CheckUserCertAttributeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserCertAttributeResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserCertAttributeResp proto.InternalMessageInfo

func (m *CheckUserCertAttributeResp) GetOk() bool {
	if m != nil {
		return m.Ok
	}
	return false
}

// 新增
// 主理人信息
type DirectorCertInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Ttid                 string   `protobuf:"bytes,2,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	CooperationType      uint32   `protobuf:"varint,4,opt,name=cooperation_type,json=cooperationType,proto3" json:"cooperation_type,omitempty"`
	Status               uint32   `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	Introduce            string   `protobuf:"bytes,6,opt,name=introduce,proto3" json:"introduce,omitempty"`
	Manager              string   `protobuf:"bytes,7,opt,name=manager,proto3" json:"manager,omitempty"`
	Ts                   uint64   `protobuf:"varint,8,opt,name=ts,proto3" json:"ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DirectorCertInfo) Reset()         { *m = DirectorCertInfo{} }
func (m *DirectorCertInfo) String() string { return proto.CompactTextString(m) }
func (*DirectorCertInfo) ProtoMessage()    {}
func (*DirectorCertInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{23}
}
func (m *DirectorCertInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DirectorCertInfo.Unmarshal(m, b)
}
func (m *DirectorCertInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DirectorCertInfo.Marshal(b, m, deterministic)
}
func (dst *DirectorCertInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DirectorCertInfo.Merge(dst, src)
}
func (m *DirectorCertInfo) XXX_Size() int {
	return xxx_messageInfo_DirectorCertInfo.Size(m)
}
func (m *DirectorCertInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DirectorCertInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DirectorCertInfo proto.InternalMessageInfo

func (m *DirectorCertInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DirectorCertInfo) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *DirectorCertInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *DirectorCertInfo) GetCooperationType() uint32 {
	if m != nil {
		return m.CooperationType
	}
	return 0
}

func (m *DirectorCertInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *DirectorCertInfo) GetIntroduce() string {
	if m != nil {
		return m.Introduce
	}
	return ""
}

func (m *DirectorCertInfo) GetManager() string {
	if m != nil {
		return m.Manager
	}
	return ""
}

func (m *DirectorCertInfo) GetTs() uint64 {
	if m != nil {
		return m.Ts
	}
	return 0
}

type UserDirectorCertInfo struct {
	IsCertified          bool              `protobuf:"varint,1,opt,name=is_certified,json=isCertified,proto3" json:"is_certified,omitempty"`
	Cert                 *DirectorCertInfo `protobuf:"bytes,2,opt,name=cert,proto3" json:"cert,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UserDirectorCertInfo) Reset()         { *m = UserDirectorCertInfo{} }
func (m *UserDirectorCertInfo) String() string { return proto.CompactTextString(m) }
func (*UserDirectorCertInfo) ProtoMessage()    {}
func (*UserDirectorCertInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{24}
}
func (m *UserDirectorCertInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDirectorCertInfo.Unmarshal(m, b)
}
func (m *UserDirectorCertInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDirectorCertInfo.Marshal(b, m, deterministic)
}
func (dst *UserDirectorCertInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDirectorCertInfo.Merge(dst, src)
}
func (m *UserDirectorCertInfo) XXX_Size() int {
	return xxx_messageInfo_UserDirectorCertInfo.Size(m)
}
func (m *UserDirectorCertInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDirectorCertInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserDirectorCertInfo proto.InternalMessageInfo

func (m *UserDirectorCertInfo) GetIsCertified() bool {
	if m != nil {
		return m.IsCertified
	}
	return false
}

func (m *UserDirectorCertInfo) GetCert() *DirectorCertInfo {
	if m != nil {
		return m.Cert
	}
	return nil
}

type GetUserDirectorCertReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserDirectorCertReq) Reset()         { *m = GetUserDirectorCertReq{} }
func (m *GetUserDirectorCertReq) String() string { return proto.CompactTextString(m) }
func (*GetUserDirectorCertReq) ProtoMessage()    {}
func (*GetUserDirectorCertReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{25}
}
func (m *GetUserDirectorCertReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserDirectorCertReq.Unmarshal(m, b)
}
func (m *GetUserDirectorCertReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserDirectorCertReq.Marshal(b, m, deterministic)
}
func (dst *GetUserDirectorCertReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserDirectorCertReq.Merge(dst, src)
}
func (m *GetUserDirectorCertReq) XXX_Size() int {
	return xxx_messageInfo_GetUserDirectorCertReq.Size(m)
}
func (m *GetUserDirectorCertReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserDirectorCertReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserDirectorCertReq proto.InternalMessageInfo

func (m *GetUserDirectorCertReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserDirectorCertResp struct {
	Info                 *UserDirectorCertInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetUserDirectorCertResp) Reset()         { *m = GetUserDirectorCertResp{} }
func (m *GetUserDirectorCertResp) String() string { return proto.CompactTextString(m) }
func (*GetUserDirectorCertResp) ProtoMessage()    {}
func (*GetUserDirectorCertResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{26}
}
func (m *GetUserDirectorCertResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserDirectorCertResp.Unmarshal(m, b)
}
func (m *GetUserDirectorCertResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserDirectorCertResp.Marshal(b, m, deterministic)
}
func (dst *GetUserDirectorCertResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserDirectorCertResp.Merge(dst, src)
}
func (m *GetUserDirectorCertResp) XXX_Size() int {
	return xxx_messageInfo_GetUserDirectorCertResp.Size(m)
}
func (m *GetUserDirectorCertResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserDirectorCertResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserDirectorCertResp proto.InternalMessageInfo

func (m *GetUserDirectorCertResp) GetInfo() *UserDirectorCertInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type BatchGetUserDirectorCertReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetUserDirectorCertReq) Reset()         { *m = BatchGetUserDirectorCertReq{} }
func (m *BatchGetUserDirectorCertReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserDirectorCertReq) ProtoMessage()    {}
func (*BatchGetUserDirectorCertReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{27}
}
func (m *BatchGetUserDirectorCertReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserDirectorCertReq.Unmarshal(m, b)
}
func (m *BatchGetUserDirectorCertReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserDirectorCertReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserDirectorCertReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserDirectorCertReq.Merge(dst, src)
}
func (m *BatchGetUserDirectorCertReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserDirectorCertReq.Size(m)
}
func (m *BatchGetUserDirectorCertReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserDirectorCertReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserDirectorCertReq proto.InternalMessageInfo

func (m *BatchGetUserDirectorCertReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchGetUserDirectorCertResp struct {
	InfoList             []*UserDirectorCertInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *BatchGetUserDirectorCertResp) Reset()         { *m = BatchGetUserDirectorCertResp{} }
func (m *BatchGetUserDirectorCertResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserDirectorCertResp) ProtoMessage()    {}
func (*BatchGetUserDirectorCertResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{28}
}
func (m *BatchGetUserDirectorCertResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserDirectorCertResp.Unmarshal(m, b)
}
func (m *BatchGetUserDirectorCertResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserDirectorCertResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserDirectorCertResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserDirectorCertResp.Merge(dst, src)
}
func (m *BatchGetUserDirectorCertResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserDirectorCertResp.Size(m)
}
func (m *BatchGetUserDirectorCertResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserDirectorCertResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserDirectorCertResp proto.InternalMessageInfo

func (m *BatchGetUserDirectorCertResp) GetInfoList() []*UserDirectorCertInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type SetUserDirectorCertReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Status               uint32   `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	CooperationType      uint32   `protobuf:"varint,3,opt,name=cooperation_type,json=cooperationType,proto3" json:"cooperation_type,omitempty"`
	Introduce            string   `protobuf:"bytes,4,opt,name=introduce,proto3" json:"introduce,omitempty"`
	Manager              string   `protobuf:"bytes,5,opt,name=manager,proto3" json:"manager,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserDirectorCertReq) Reset()         { *m = SetUserDirectorCertReq{} }
func (m *SetUserDirectorCertReq) String() string { return proto.CompactTextString(m) }
func (*SetUserDirectorCertReq) ProtoMessage()    {}
func (*SetUserDirectorCertReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{29}
}
func (m *SetUserDirectorCertReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserDirectorCertReq.Unmarshal(m, b)
}
func (m *SetUserDirectorCertReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserDirectorCertReq.Marshal(b, m, deterministic)
}
func (dst *SetUserDirectorCertReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserDirectorCertReq.Merge(dst, src)
}
func (m *SetUserDirectorCertReq) XXX_Size() int {
	return xxx_messageInfo_SetUserDirectorCertReq.Size(m)
}
func (m *SetUserDirectorCertReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserDirectorCertReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserDirectorCertReq proto.InternalMessageInfo

func (m *SetUserDirectorCertReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserDirectorCertReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *SetUserDirectorCertReq) GetCooperationType() uint32 {
	if m != nil {
		return m.CooperationType
	}
	return 0
}

func (m *SetUserDirectorCertReq) GetIntroduce() string {
	if m != nil {
		return m.Introduce
	}
	return ""
}

func (m *SetUserDirectorCertReq) GetManager() string {
	if m != nil {
		return m.Manager
	}
	return ""
}

type SetUserDirectorCertResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserDirectorCertResp) Reset()         { *m = SetUserDirectorCertResp{} }
func (m *SetUserDirectorCertResp) String() string { return proto.CompactTextString(m) }
func (*SetUserDirectorCertResp) ProtoMessage()    {}
func (*SetUserDirectorCertResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{30}
}
func (m *SetUserDirectorCertResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserDirectorCertResp.Unmarshal(m, b)
}
func (m *SetUserDirectorCertResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserDirectorCertResp.Marshal(b, m, deterministic)
}
func (dst *SetUserDirectorCertResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserDirectorCertResp.Merge(dst, src)
}
func (m *SetUserDirectorCertResp) XXX_Size() int {
	return xxx_messageInfo_SetUserDirectorCertResp.Size(m)
}
func (m *SetUserDirectorCertResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserDirectorCertResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserDirectorCertResp proto.InternalMessageInfo

type AddUserDirectorCertReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CooperationType      uint32   `protobuf:"varint,2,opt,name=cooperation_type,json=cooperationType,proto3" json:"cooperation_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUserDirectorCertReq) Reset()         { *m = AddUserDirectorCertReq{} }
func (m *AddUserDirectorCertReq) String() string { return proto.CompactTextString(m) }
func (*AddUserDirectorCertReq) ProtoMessage()    {}
func (*AddUserDirectorCertReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{31}
}
func (m *AddUserDirectorCertReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserDirectorCertReq.Unmarshal(m, b)
}
func (m *AddUserDirectorCertReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserDirectorCertReq.Marshal(b, m, deterministic)
}
func (dst *AddUserDirectorCertReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserDirectorCertReq.Merge(dst, src)
}
func (m *AddUserDirectorCertReq) XXX_Size() int {
	return xxx_messageInfo_AddUserDirectorCertReq.Size(m)
}
func (m *AddUserDirectorCertReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserDirectorCertReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserDirectorCertReq proto.InternalMessageInfo

func (m *AddUserDirectorCertReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddUserDirectorCertReq) GetCooperationType() uint32 {
	if m != nil {
		return m.CooperationType
	}
	return 0
}

type AddUserDirectorCertResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUserDirectorCertResp) Reset()         { *m = AddUserDirectorCertResp{} }
func (m *AddUserDirectorCertResp) String() string { return proto.CompactTextString(m) }
func (*AddUserDirectorCertResp) ProtoMessage()    {}
func (*AddUserDirectorCertResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{32}
}
func (m *AddUserDirectorCertResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserDirectorCertResp.Unmarshal(m, b)
}
func (m *AddUserDirectorCertResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserDirectorCertResp.Marshal(b, m, deterministic)
}
func (dst *AddUserDirectorCertResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserDirectorCertResp.Merge(dst, src)
}
func (m *AddUserDirectorCertResp) XXX_Size() int {
	return xxx_messageInfo_AddUserDirectorCertResp.Size(m)
}
func (m *AddUserDirectorCertResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserDirectorCertResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserDirectorCertResp proto.InternalMessageInfo

type ListUserDirectorCertReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	PageSize             uint32   `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Ttid                 string   `protobuf:"bytes,3,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Nickname             string   `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Manager              string   `protobuf:"bytes,5,opt,name=manager,proto3" json:"manager,omitempty"`
	CooperationType      uint32   `protobuf:"varint,6,opt,name=cooperation_type,json=cooperationType,proto3" json:"cooperation_type,omitempty"`
	Status               uint32   `protobuf:"varint,7,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListUserDirectorCertReq) Reset()         { *m = ListUserDirectorCertReq{} }
func (m *ListUserDirectorCertReq) String() string { return proto.CompactTextString(m) }
func (*ListUserDirectorCertReq) ProtoMessage()    {}
func (*ListUserDirectorCertReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{33}
}
func (m *ListUserDirectorCertReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListUserDirectorCertReq.Unmarshal(m, b)
}
func (m *ListUserDirectorCertReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListUserDirectorCertReq.Marshal(b, m, deterministic)
}
func (dst *ListUserDirectorCertReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListUserDirectorCertReq.Merge(dst, src)
}
func (m *ListUserDirectorCertReq) XXX_Size() int {
	return xxx_messageInfo_ListUserDirectorCertReq.Size(m)
}
func (m *ListUserDirectorCertReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListUserDirectorCertReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListUserDirectorCertReq proto.InternalMessageInfo

func (m *ListUserDirectorCertReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *ListUserDirectorCertReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *ListUserDirectorCertReq) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *ListUserDirectorCertReq) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ListUserDirectorCertReq) GetManager() string {
	if m != nil {
		return m.Manager
	}
	return ""
}

func (m *ListUserDirectorCertReq) GetCooperationType() uint32 {
	if m != nil {
		return m.CooperationType
	}
	return 0
}

func (m *ListUserDirectorCertReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type ListUserDirectorCertResp struct {
	Total                uint32                  `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	InfoList             []*UserDirectorCertInfo `protobuf:"bytes,2,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *ListUserDirectorCertResp) Reset()         { *m = ListUserDirectorCertResp{} }
func (m *ListUserDirectorCertResp) String() string { return proto.CompactTextString(m) }
func (*ListUserDirectorCertResp) ProtoMessage()    {}
func (*ListUserDirectorCertResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{34}
}
func (m *ListUserDirectorCertResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListUserDirectorCertResp.Unmarshal(m, b)
}
func (m *ListUserDirectorCertResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListUserDirectorCertResp.Marshal(b, m, deterministic)
}
func (dst *ListUserDirectorCertResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListUserDirectorCertResp.Merge(dst, src)
}
func (m *ListUserDirectorCertResp) XXX_Size() int {
	return xxx_messageInfo_ListUserDirectorCertResp.Size(m)
}
func (m *ListUserDirectorCertResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListUserDirectorCertResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListUserDirectorCertResp proto.InternalMessageInfo

func (m *ListUserDirectorCertResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *ListUserDirectorCertResp) GetInfoList() []*UserDirectorCertInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type CertifyItemInfo struct {
	VId                  uint32   `protobuf:"varint,1,opt,name=v_id,json=vId,proto3" json:"v_id,omitempty"`
	Style                string   `protobuf:"bytes,2,opt,name=style,proto3" json:"style,omitempty"`
	Title                string   `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Handler              string   `protobuf:"bytes,4,opt,name=handler,proto3" json:"handler,omitempty"`
	CreateTs             uint32   `protobuf:"varint,5,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CertifyItemInfo) Reset()         { *m = CertifyItemInfo{} }
func (m *CertifyItemInfo) String() string { return proto.CompactTextString(m) }
func (*CertifyItemInfo) ProtoMessage()    {}
func (*CertifyItemInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{35}
}
func (m *CertifyItemInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CertifyItemInfo.Unmarshal(m, b)
}
func (m *CertifyItemInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CertifyItemInfo.Marshal(b, m, deterministic)
}
func (dst *CertifyItemInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CertifyItemInfo.Merge(dst, src)
}
func (m *CertifyItemInfo) XXX_Size() int {
	return xxx_messageInfo_CertifyItemInfo.Size(m)
}
func (m *CertifyItemInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CertifyItemInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CertifyItemInfo proto.InternalMessageInfo

func (m *CertifyItemInfo) GetVId() uint32 {
	if m != nil {
		return m.VId
	}
	return 0
}

func (m *CertifyItemInfo) GetStyle() string {
	if m != nil {
		return m.Style
	}
	return ""
}

func (m *CertifyItemInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *CertifyItemInfo) GetHandler() string {
	if m != nil {
		return m.Handler
	}
	return ""
}

func (m *CertifyItemInfo) GetCreateTs() uint32 {
	if m != nil {
		return m.CreateTs
	}
	return 0
}

type BatchAddCertItemReq struct {
	Info                 []*CertifyItemInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	Handler              string             `protobuf:"bytes,2,opt,name=handler,proto3" json:"handler,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *BatchAddCertItemReq) Reset()         { *m = BatchAddCertItemReq{} }
func (m *BatchAddCertItemReq) String() string { return proto.CompactTextString(m) }
func (*BatchAddCertItemReq) ProtoMessage()    {}
func (*BatchAddCertItemReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{36}
}
func (m *BatchAddCertItemReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddCertItemReq.Unmarshal(m, b)
}
func (m *BatchAddCertItemReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddCertItemReq.Marshal(b, m, deterministic)
}
func (dst *BatchAddCertItemReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddCertItemReq.Merge(dst, src)
}
func (m *BatchAddCertItemReq) XXX_Size() int {
	return xxx_messageInfo_BatchAddCertItemReq.Size(m)
}
func (m *BatchAddCertItemReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddCertItemReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddCertItemReq proto.InternalMessageInfo

func (m *BatchAddCertItemReq) GetInfo() []*CertifyItemInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *BatchAddCertItemReq) GetHandler() string {
	if m != nil {
		return m.Handler
	}
	return ""
}

type BatchAddCertItemResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchAddCertItemResp) Reset()         { *m = BatchAddCertItemResp{} }
func (m *BatchAddCertItemResp) String() string { return proto.CompactTextString(m) }
func (*BatchAddCertItemResp) ProtoMessage()    {}
func (*BatchAddCertItemResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{37}
}
func (m *BatchAddCertItemResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddCertItemResp.Unmarshal(m, b)
}
func (m *BatchAddCertItemResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddCertItemResp.Marshal(b, m, deterministic)
}
func (dst *BatchAddCertItemResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddCertItemResp.Merge(dst, src)
}
func (m *BatchAddCertItemResp) XXX_Size() int {
	return xxx_messageInfo_BatchAddCertItemResp.Size(m)
}
func (m *BatchAddCertItemResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddCertItemResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddCertItemResp proto.InternalMessageInfo

type ListCertItemReq struct {
	VIds                 []uint32 `protobuf:"varint,1,rep,packed,name=v_ids,json=vIds,proto3" json:"v_ids,omitempty"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Style                string   `protobuf:"bytes,3,opt,name=style,proto3" json:"style,omitempty"`
	Page                 uint32   `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	PageNum              uint32   `protobuf:"varint,5,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListCertItemReq) Reset()         { *m = ListCertItemReq{} }
func (m *ListCertItemReq) String() string { return proto.CompactTextString(m) }
func (*ListCertItemReq) ProtoMessage()    {}
func (*ListCertItemReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{38}
}
func (m *ListCertItemReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListCertItemReq.Unmarshal(m, b)
}
func (m *ListCertItemReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListCertItemReq.Marshal(b, m, deterministic)
}
func (dst *ListCertItemReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListCertItemReq.Merge(dst, src)
}
func (m *ListCertItemReq) XXX_Size() int {
	return xxx_messageInfo_ListCertItemReq.Size(m)
}
func (m *ListCertItemReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListCertItemReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListCertItemReq proto.InternalMessageInfo

func (m *ListCertItemReq) GetVIds() []uint32 {
	if m != nil {
		return m.VIds
	}
	return nil
}

func (m *ListCertItemReq) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ListCertItemReq) GetStyle() string {
	if m != nil {
		return m.Style
	}
	return ""
}

func (m *ListCertItemReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *ListCertItemReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

type ListCertItemResp struct {
	Total                uint32             `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List                 []*CertifyItemInfo `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *ListCertItemResp) Reset()         { *m = ListCertItemResp{} }
func (m *ListCertItemResp) String() string { return proto.CompactTextString(m) }
func (*ListCertItemResp) ProtoMessage()    {}
func (*ListCertItemResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{39}
}
func (m *ListCertItemResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListCertItemResp.Unmarshal(m, b)
}
func (m *ListCertItemResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListCertItemResp.Marshal(b, m, deterministic)
}
func (dst *ListCertItemResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListCertItemResp.Merge(dst, src)
}
func (m *ListCertItemResp) XXX_Size() int {
	return xxx_messageInfo_ListCertItemResp.Size(m)
}
func (m *ListCertItemResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListCertItemResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListCertItemResp proto.InternalMessageInfo

func (m *ListCertItemResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *ListCertItemResp) GetList() []*CertifyItemInfo {
	if m != nil {
		return m.List
	}
	return nil
}

type GrantOfficialCertInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	VId                  uint32   `protobuf:"varint,2,opt,name=v_id,json=vId,proto3" json:"v_id,omitempty"`
	Intro                string   `protobuf:"bytes,3,opt,name=intro,proto3" json:"intro,omitempty"`
	ShowDay              uint64   `protobuf:"varint,4,opt,name=show_day,json=showDay,proto3" json:"show_day,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GrantOfficialCertInfo) Reset()         { *m = GrantOfficialCertInfo{} }
func (m *GrantOfficialCertInfo) String() string { return proto.CompactTextString(m) }
func (*GrantOfficialCertInfo) ProtoMessage()    {}
func (*GrantOfficialCertInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{40}
}
func (m *GrantOfficialCertInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GrantOfficialCertInfo.Unmarshal(m, b)
}
func (m *GrantOfficialCertInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GrantOfficialCertInfo.Marshal(b, m, deterministic)
}
func (dst *GrantOfficialCertInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GrantOfficialCertInfo.Merge(dst, src)
}
func (m *GrantOfficialCertInfo) XXX_Size() int {
	return xxx_messageInfo_GrantOfficialCertInfo.Size(m)
}
func (m *GrantOfficialCertInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GrantOfficialCertInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GrantOfficialCertInfo proto.InternalMessageInfo

func (m *GrantOfficialCertInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GrantOfficialCertInfo) GetVId() uint32 {
	if m != nil {
		return m.VId
	}
	return 0
}

func (m *GrantOfficialCertInfo) GetIntro() string {
	if m != nil {
		return m.Intro
	}
	return ""
}

func (m *GrantOfficialCertInfo) GetShowDay() uint64 {
	if m != nil {
		return m.ShowDay
	}
	return 0
}

type BatchGrantOfficialCertReq struct {
	List                 []*GrantOfficialCertInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	BeginTs              uint64                   `protobuf:"varint,2,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	Attribute            uint32                   `protobuf:"varint,3,opt,name=attribute,proto3" json:"attribute,omitempty"`
	Handler              string                   `protobuf:"bytes,4,opt,name=handler,proto3" json:"handler,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *BatchGrantOfficialCertReq) Reset()         { *m = BatchGrantOfficialCertReq{} }
func (m *BatchGrantOfficialCertReq) String() string { return proto.CompactTextString(m) }
func (*BatchGrantOfficialCertReq) ProtoMessage()    {}
func (*BatchGrantOfficialCertReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{41}
}
func (m *BatchGrantOfficialCertReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGrantOfficialCertReq.Unmarshal(m, b)
}
func (m *BatchGrantOfficialCertReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGrantOfficialCertReq.Marshal(b, m, deterministic)
}
func (dst *BatchGrantOfficialCertReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGrantOfficialCertReq.Merge(dst, src)
}
func (m *BatchGrantOfficialCertReq) XXX_Size() int {
	return xxx_messageInfo_BatchGrantOfficialCertReq.Size(m)
}
func (m *BatchGrantOfficialCertReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGrantOfficialCertReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGrantOfficialCertReq proto.InternalMessageInfo

func (m *BatchGrantOfficialCertReq) GetList() []*GrantOfficialCertInfo {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *BatchGrantOfficialCertReq) GetBeginTs() uint64 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *BatchGrantOfficialCertReq) GetAttribute() uint32 {
	if m != nil {
		return m.Attribute
	}
	return 0
}

func (m *BatchGrantOfficialCertReq) GetHandler() string {
	if m != nil {
		return m.Handler
	}
	return ""
}

type BatchGrantOfficialCertResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGrantOfficialCertResp) Reset()         { *m = BatchGrantOfficialCertResp{} }
func (m *BatchGrantOfficialCertResp) String() string { return proto.CompactTextString(m) }
func (*BatchGrantOfficialCertResp) ProtoMessage()    {}
func (*BatchGrantOfficialCertResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{42}
}
func (m *BatchGrantOfficialCertResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGrantOfficialCertResp.Unmarshal(m, b)
}
func (m *BatchGrantOfficialCertResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGrantOfficialCertResp.Marshal(b, m, deterministic)
}
func (dst *BatchGrantOfficialCertResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGrantOfficialCertResp.Merge(dst, src)
}
func (m *BatchGrantOfficialCertResp) XXX_Size() int {
	return xxx_messageInfo_BatchGrantOfficialCertResp.Size(m)
}
func (m *BatchGrantOfficialCertResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGrantOfficialCertResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGrantOfficialCertResp proto.InternalMessageInfo

type ListOfficialCertReq struct {
	QueryType            uint32   `protobuf:"varint,1,opt,name=query_type,json=queryType,proto3" json:"query_type,omitempty"`
	ReqIds               []uint32 `protobuf:"varint,2,rep,packed,name=req_ids,json=reqIds,proto3" json:"req_ids,omitempty"`
	Page                 uint32   `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageNum              uint32   `protobuf:"varint,4,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	BeginTs              uint64   `protobuf:"varint,5,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint64   `protobuf:"varint,6,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	Status               uint32   `protobuf:"varint,7,opt,name=status,proto3" json:"status,omitempty"`
	CertifyStyleList     []string `protobuf:"bytes,8,rep,name=certify_style_list,json=certifyStyleList,proto3" json:"certify_style_list,omitempty"`
	Vids                 []uint32 `protobuf:"varint,9,rep,packed,name=vids,proto3" json:"vids,omitempty"`
	Uids                 []uint32 `protobuf:"varint,10,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListOfficialCertReq) Reset()         { *m = ListOfficialCertReq{} }
func (m *ListOfficialCertReq) String() string { return proto.CompactTextString(m) }
func (*ListOfficialCertReq) ProtoMessage()    {}
func (*ListOfficialCertReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{43}
}
func (m *ListOfficialCertReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListOfficialCertReq.Unmarshal(m, b)
}
func (m *ListOfficialCertReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListOfficialCertReq.Marshal(b, m, deterministic)
}
func (dst *ListOfficialCertReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListOfficialCertReq.Merge(dst, src)
}
func (m *ListOfficialCertReq) XXX_Size() int {
	return xxx_messageInfo_ListOfficialCertReq.Size(m)
}
func (m *ListOfficialCertReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListOfficialCertReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListOfficialCertReq proto.InternalMessageInfo

func (m *ListOfficialCertReq) GetQueryType() uint32 {
	if m != nil {
		return m.QueryType
	}
	return 0
}

func (m *ListOfficialCertReq) GetReqIds() []uint32 {
	if m != nil {
		return m.ReqIds
	}
	return nil
}

func (m *ListOfficialCertReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *ListOfficialCertReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

func (m *ListOfficialCertReq) GetBeginTs() uint64 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *ListOfficialCertReq) GetEndTs() uint64 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *ListOfficialCertReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *ListOfficialCertReq) GetCertifyStyleList() []string {
	if m != nil {
		return m.CertifyStyleList
	}
	return nil
}

func (m *ListOfficialCertReq) GetVids() []uint32 {
	if m != nil {
		return m.Vids
	}
	return nil
}

func (m *ListOfficialCertReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type ListOfficialCertResp struct {
	Total                uint32              `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List                 []*OfficialCertInfo `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *ListOfficialCertResp) Reset()         { *m = ListOfficialCertResp{} }
func (m *ListOfficialCertResp) String() string { return proto.CompactTextString(m) }
func (*ListOfficialCertResp) ProtoMessage()    {}
func (*ListOfficialCertResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{44}
}
func (m *ListOfficialCertResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListOfficialCertResp.Unmarshal(m, b)
}
func (m *ListOfficialCertResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListOfficialCertResp.Marshal(b, m, deterministic)
}
func (dst *ListOfficialCertResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListOfficialCertResp.Merge(dst, src)
}
func (m *ListOfficialCertResp) XXX_Size() int {
	return xxx_messageInfo_ListOfficialCertResp.Size(m)
}
func (m *ListOfficialCertResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListOfficialCertResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListOfficialCertResp proto.InternalMessageInfo

func (m *ListOfficialCertResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *ListOfficialCertResp) GetList() []*OfficialCertInfo {
	if m != nil {
		return m.List
	}
	return nil
}

type DelOfficialCertReq struct {
	Id                   []uint32 `protobuf:"varint,1,rep,packed,name=id,proto3" json:"id,omitempty"`
	Handler              string   `protobuf:"bytes,2,opt,name=handler,proto3" json:"handler,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelOfficialCertReq) Reset()         { *m = DelOfficialCertReq{} }
func (m *DelOfficialCertReq) String() string { return proto.CompactTextString(m) }
func (*DelOfficialCertReq) ProtoMessage()    {}
func (*DelOfficialCertReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{45}
}
func (m *DelOfficialCertReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelOfficialCertReq.Unmarshal(m, b)
}
func (m *DelOfficialCertReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelOfficialCertReq.Marshal(b, m, deterministic)
}
func (dst *DelOfficialCertReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelOfficialCertReq.Merge(dst, src)
}
func (m *DelOfficialCertReq) XXX_Size() int {
	return xxx_messageInfo_DelOfficialCertReq.Size(m)
}
func (m *DelOfficialCertReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelOfficialCertReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelOfficialCertReq proto.InternalMessageInfo

func (m *DelOfficialCertReq) GetId() []uint32 {
	if m != nil {
		return m.Id
	}
	return nil
}

func (m *DelOfficialCertReq) GetHandler() string {
	if m != nil {
		return m.Handler
	}
	return ""
}

type DelOfficialCertResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelOfficialCertResp) Reset()         { *m = DelOfficialCertResp{} }
func (m *DelOfficialCertResp) String() string { return proto.CompactTextString(m) }
func (*DelOfficialCertResp) ProtoMessage()    {}
func (*DelOfficialCertResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{46}
}
func (m *DelOfficialCertResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelOfficialCertResp.Unmarshal(m, b)
}
func (m *DelOfficialCertResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelOfficialCertResp.Marshal(b, m, deterministic)
}
func (dst *DelOfficialCertResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelOfficialCertResp.Merge(dst, src)
}
func (m *DelOfficialCertResp) XXX_Size() int {
	return xxx_messageInfo_DelOfficialCertResp.Size(m)
}
func (m *DelOfficialCertResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelOfficialCertResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelOfficialCertResp proto.InternalMessageInfo

type UpdateAliasReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Alias                string   `protobuf:"bytes,2,opt,name=alias,proto3" json:"alias,omitempty"`
	Handler              string   `protobuf:"bytes,3,opt,name=handler,proto3" json:"handler,omitempty"`
	WithSign             bool     `protobuf:"varint,4,opt,name=with_sign,json=withSign,proto3" json:"with_sign,omitempty"`
	AliasSignLevel       string   `protobuf:"bytes,5,opt,name=alias_sign_level,json=aliasSignLevel,proto3" json:"alias_sign_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateAliasReq) Reset()         { *m = UpdateAliasReq{} }
func (m *UpdateAliasReq) String() string { return proto.CompactTextString(m) }
func (*UpdateAliasReq) ProtoMessage()    {}
func (*UpdateAliasReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{47}
}
func (m *UpdateAliasReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAliasReq.Unmarshal(m, b)
}
func (m *UpdateAliasReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAliasReq.Marshal(b, m, deterministic)
}
func (dst *UpdateAliasReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAliasReq.Merge(dst, src)
}
func (m *UpdateAliasReq) XXX_Size() int {
	return xxx_messageInfo_UpdateAliasReq.Size(m)
}
func (m *UpdateAliasReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAliasReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAliasReq proto.InternalMessageInfo

func (m *UpdateAliasReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateAliasReq) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

func (m *UpdateAliasReq) GetHandler() string {
	if m != nil {
		return m.Handler
	}
	return ""
}

func (m *UpdateAliasReq) GetWithSign() bool {
	if m != nil {
		return m.WithSign
	}
	return false
}

func (m *UpdateAliasReq) GetAliasSignLevel() string {
	if m != nil {
		return m.AliasSignLevel
	}
	return ""
}

type UpdateAliasResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateAliasResp) Reset()         { *m = UpdateAliasResp{} }
func (m *UpdateAliasResp) String() string { return proto.CompactTextString(m) }
func (*UpdateAliasResp) ProtoMessage()    {}
func (*UpdateAliasResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{48}
}
func (m *UpdateAliasResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAliasResp.Unmarshal(m, b)
}
func (m *UpdateAliasResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAliasResp.Marshal(b, m, deterministic)
}
func (dst *UpdateAliasResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAliasResp.Merge(dst, src)
}
func (m *UpdateAliasResp) XXX_Size() int {
	return xxx_messageInfo_UpdateAliasResp.Size(m)
}
func (m *UpdateAliasResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAliasResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAliasResp proto.InternalMessageInfo

// 更新房间的displayID
type ChangeDisplayIDReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	NewChannelViewId     string   `protobuf:"bytes,2,opt,name=new_channel_view_id,json=newChannelViewId,proto3" json:"new_channel_view_id,omitempty"`
	Handler              string   `protobuf:"bytes,3,opt,name=handler,proto3" json:"handler,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeDisplayIDReq) Reset()         { *m = ChangeDisplayIDReq{} }
func (m *ChangeDisplayIDReq) String() string { return proto.CompactTextString(m) }
func (*ChangeDisplayIDReq) ProtoMessage()    {}
func (*ChangeDisplayIDReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{49}
}
func (m *ChangeDisplayIDReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeDisplayIDReq.Unmarshal(m, b)
}
func (m *ChangeDisplayIDReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeDisplayIDReq.Marshal(b, m, deterministic)
}
func (dst *ChangeDisplayIDReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeDisplayIDReq.Merge(dst, src)
}
func (m *ChangeDisplayIDReq) XXX_Size() int {
	return xxx_messageInfo_ChangeDisplayIDReq.Size(m)
}
func (m *ChangeDisplayIDReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeDisplayIDReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeDisplayIDReq proto.InternalMessageInfo

func (m *ChangeDisplayIDReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChangeDisplayIDReq) GetNewChannelViewId() string {
	if m != nil {
		return m.NewChannelViewId
	}
	return ""
}

func (m *ChangeDisplayIDReq) GetHandler() string {
	if m != nil {
		return m.Handler
	}
	return ""
}

type ChangeDisplayIDResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeDisplayIDResp) Reset()         { *m = ChangeDisplayIDResp{} }
func (m *ChangeDisplayIDResp) String() string { return proto.CompactTextString(m) }
func (*ChangeDisplayIDResp) ProtoMessage()    {}
func (*ChangeDisplayIDResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{50}
}
func (m *ChangeDisplayIDResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeDisplayIDResp.Unmarshal(m, b)
}
func (m *ChangeDisplayIDResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeDisplayIDResp.Marshal(b, m, deterministic)
}
func (dst *ChangeDisplayIDResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeDisplayIDResp.Merge(dst, src)
}
func (m *ChangeDisplayIDResp) XXX_Size() int {
	return xxx_messageInfo_ChangeDisplayIDResp.Size(m)
}
func (m *ChangeDisplayIDResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeDisplayIDResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeDisplayIDResp proto.InternalMessageInfo

type ListAliasReq struct {
	QueryType            uint32   `protobuf:"varint,1,opt,name=query_type,json=queryType,proto3" json:"query_type,omitempty"`
	Alias                string   `protobuf:"bytes,2,opt,name=alias,proto3" json:"alias,omitempty"`
	Ttid                 string   `protobuf:"bytes,3,opt,name=ttid,proto3" json:"ttid,omitempty"`
	ChannelViewId        string   `protobuf:"bytes,4,opt,name=channel_view_id,json=channelViewId,proto3" json:"channel_view_id,omitempty"`
	NewChannelViewId     string   `protobuf:"bytes,5,opt,name=new_channel_view_id,json=newChannelViewId,proto3" json:"new_channel_view_id,omitempty"`
	BeginTime            uint32   `protobuf:"varint,6,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,7,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Page                 uint32   `protobuf:"varint,8,opt,name=page,proto3" json:"page,omitempty"`
	PageNum              uint32   `protobuf:"varint,9,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	Uid                  uint32   `protobuf:"varint,10,opt,name=uid,proto3" json:"uid,omitempty"`
	AliasSignLevel       string   `protobuf:"bytes,11,opt,name=alias_sign_level,json=aliasSignLevel,proto3" json:"alias_sign_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListAliasReq) Reset()         { *m = ListAliasReq{} }
func (m *ListAliasReq) String() string { return proto.CompactTextString(m) }
func (*ListAliasReq) ProtoMessage()    {}
func (*ListAliasReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{51}
}
func (m *ListAliasReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListAliasReq.Unmarshal(m, b)
}
func (m *ListAliasReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListAliasReq.Marshal(b, m, deterministic)
}
func (dst *ListAliasReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListAliasReq.Merge(dst, src)
}
func (m *ListAliasReq) XXX_Size() int {
	return xxx_messageInfo_ListAliasReq.Size(m)
}
func (m *ListAliasReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListAliasReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListAliasReq proto.InternalMessageInfo

func (m *ListAliasReq) GetQueryType() uint32 {
	if m != nil {
		return m.QueryType
	}
	return 0
}

func (m *ListAliasReq) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

func (m *ListAliasReq) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *ListAliasReq) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

func (m *ListAliasReq) GetNewChannelViewId() string {
	if m != nil {
		return m.NewChannelViewId
	}
	return ""
}

func (m *ListAliasReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *ListAliasReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *ListAliasReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *ListAliasReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

func (m *ListAliasReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ListAliasReq) GetAliasSignLevel() string {
	if m != nil {
		return m.AliasSignLevel
	}
	return ""
}

type ListAliasResp struct {
	Total                uint32       `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List                 []*AliasInfo `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ListAliasResp) Reset()         { *m = ListAliasResp{} }
func (m *ListAliasResp) String() string { return proto.CompactTextString(m) }
func (*ListAliasResp) ProtoMessage()    {}
func (*ListAliasResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{52}
}
func (m *ListAliasResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListAliasResp.Unmarshal(m, b)
}
func (m *ListAliasResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListAliasResp.Marshal(b, m, deterministic)
}
func (dst *ListAliasResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListAliasResp.Merge(dst, src)
}
func (m *ListAliasResp) XXX_Size() int {
	return xxx_messageInfo_ListAliasResp.Size(m)
}
func (m *ListAliasResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListAliasResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListAliasResp proto.InternalMessageInfo

func (m *ListAliasResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *ListAliasResp) GetList() []*AliasInfo {
	if m != nil {
		return m.List
	}
	return nil
}

type AliasInfo struct {
	Uid              uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Ttid             string `protobuf:"bytes,2,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Alias            string `protobuf:"bytes,3,opt,name=alias,proto3" json:"alias,omitempty"`
	Nickname         string `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	ChannelId        uint32 `protobuf:"varint,5,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelViewId    string `protobuf:"bytes,6,opt,name=channel_view_id,json=channelViewId,proto3" json:"channel_view_id,omitempty"`
	NewChannelViewId string `protobuf:"bytes,7,opt,name=new_channel_view_id,json=newChannelViewId,proto3" json:"new_channel_view_id,omitempty"`
	ChannelName      string `protobuf:"bytes,8,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	ChannelType      uint32 `protobuf:"varint,9,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	Handler          string `protobuf:"bytes,10,opt,name=handler,proto3" json:"handler,omitempty"`
	HandlerTime      uint32 `protobuf:"varint,11,opt,name=handler_time,json=handlerTime,proto3" json:"handler_time,omitempty"`
	// 发放方式
	HandlerType AliasInfo_HandlerType `protobuf:"varint,12,opt,name=handler_type,json=handlerType,proto3,enum=officialcert_go.AliasInfo_HandlerType" json:"handler_type,omitempty"`
	// 初始密码
	InitPwd              string   `protobuf:"bytes,13,opt,name=init_pwd,json=initPwd,proto3" json:"init_pwd,omitempty"`
	AliasSignStatus      uint32   `protobuf:"varint,14,opt,name=alias_sign_status,json=aliasSignStatus,proto3" json:"alias_sign_status,omitempty"`
	HandlerTimeSign      uint32   `protobuf:"varint,15,opt,name=handler_time_sign,json=handlerTimeSign,proto3" json:"handler_time_sign,omitempty"`
	HandlerSign          string   `protobuf:"bytes,16,opt,name=handler_sign,json=handlerSign,proto3" json:"handler_sign,omitempty"`
	AliasSignLevel       string   `protobuf:"bytes,17,opt,name=alias_sign_level,json=aliasSignLevel,proto3" json:"alias_sign_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AliasInfo) Reset()         { *m = AliasInfo{} }
func (m *AliasInfo) String() string { return proto.CompactTextString(m) }
func (*AliasInfo) ProtoMessage()    {}
func (*AliasInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{53}
}
func (m *AliasInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AliasInfo.Unmarshal(m, b)
}
func (m *AliasInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AliasInfo.Marshal(b, m, deterministic)
}
func (dst *AliasInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AliasInfo.Merge(dst, src)
}
func (m *AliasInfo) XXX_Size() int {
	return xxx_messageInfo_AliasInfo.Size(m)
}
func (m *AliasInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AliasInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AliasInfo proto.InternalMessageInfo

func (m *AliasInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AliasInfo) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *AliasInfo) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

func (m *AliasInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *AliasInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AliasInfo) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

func (m *AliasInfo) GetNewChannelViewId() string {
	if m != nil {
		return m.NewChannelViewId
	}
	return ""
}

func (m *AliasInfo) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *AliasInfo) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *AliasInfo) GetHandler() string {
	if m != nil {
		return m.Handler
	}
	return ""
}

func (m *AliasInfo) GetHandlerTime() uint32 {
	if m != nil {
		return m.HandlerTime
	}
	return 0
}

func (m *AliasInfo) GetHandlerType() AliasInfo_HandlerType {
	if m != nil {
		return m.HandlerType
	}
	return AliasInfo_HANDLER_TYPE_UNSPECIFIED
}

func (m *AliasInfo) GetInitPwd() string {
	if m != nil {
		return m.InitPwd
	}
	return ""
}

func (m *AliasInfo) GetAliasSignStatus() uint32 {
	if m != nil {
		return m.AliasSignStatus
	}
	return 0
}

func (m *AliasInfo) GetHandlerTimeSign() uint32 {
	if m != nil {
		return m.HandlerTimeSign
	}
	return 0
}

func (m *AliasInfo) GetHandlerSign() string {
	if m != nil {
		return m.HandlerSign
	}
	return ""
}

func (m *AliasInfo) GetAliasSignLevel() string {
	if m != nil {
		return m.AliasSignLevel
	}
	return ""
}

type CreateUserByAliasReq struct {
	Alias                string   `protobuf:"bytes,1,opt,name=alias,proto3" json:"alias,omitempty"`
	Handler              string   `protobuf:"bytes,2,opt,name=handler,proto3" json:"handler,omitempty"`
	WithSign             bool     `protobuf:"varint,3,opt,name=with_sign,json=withSign,proto3" json:"with_sign,omitempty"`
	AliasSignLevel       string   `protobuf:"bytes,4,opt,name=alias_sign_level,json=aliasSignLevel,proto3" json:"alias_sign_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateUserByAliasReq) Reset()         { *m = CreateUserByAliasReq{} }
func (m *CreateUserByAliasReq) String() string { return proto.CompactTextString(m) }
func (*CreateUserByAliasReq) ProtoMessage()    {}
func (*CreateUserByAliasReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{54}
}
func (m *CreateUserByAliasReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateUserByAliasReq.Unmarshal(m, b)
}
func (m *CreateUserByAliasReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateUserByAliasReq.Marshal(b, m, deterministic)
}
func (dst *CreateUserByAliasReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateUserByAliasReq.Merge(dst, src)
}
func (m *CreateUserByAliasReq) XXX_Size() int {
	return xxx_messageInfo_CreateUserByAliasReq.Size(m)
}
func (m *CreateUserByAliasReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateUserByAliasReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateUserByAliasReq proto.InternalMessageInfo

func (m *CreateUserByAliasReq) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

func (m *CreateUserByAliasReq) GetHandler() string {
	if m != nil {
		return m.Handler
	}
	return ""
}

func (m *CreateUserByAliasReq) GetWithSign() bool {
	if m != nil {
		return m.WithSign
	}
	return false
}

func (m *CreateUserByAliasReq) GetAliasSignLevel() string {
	if m != nil {
		return m.AliasSignLevel
	}
	return ""
}

type CreateUserByAliasResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateUserByAliasResp) Reset()         { *m = CreateUserByAliasResp{} }
func (m *CreateUserByAliasResp) String() string { return proto.CompactTextString(m) }
func (*CreateUserByAliasResp) ProtoMessage()    {}
func (*CreateUserByAliasResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{55}
}
func (m *CreateUserByAliasResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateUserByAliasResp.Unmarshal(m, b)
}
func (m *CreateUserByAliasResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateUserByAliasResp.Marshal(b, m, deterministic)
}
func (dst *CreateUserByAliasResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateUserByAliasResp.Merge(dst, src)
}
func (m *CreateUserByAliasResp) XXX_Size() int {
	return xxx_messageInfo_CreateUserByAliasResp.Size(m)
}
func (m *CreateUserByAliasResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateUserByAliasResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateUserByAliasResp proto.InternalMessageInfo

type BatchAddAliasSignReq struct {
	AliasSignList        []uint32         `protobuf:"varint,1,rep,packed,name=alias_sign_list,json=aliasSignList,proto3" json:"alias_sign_list,omitempty"`
	Handler              string           `protobuf:"bytes,2,opt,name=handler,proto3" json:"handler,omitempty"`
	AliasSignInfoList    []*AliasSignInfo `protobuf:"bytes,3,rep,name=alias_sign_info_list,json=aliasSignInfoList,proto3" json:"alias_sign_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *BatchAddAliasSignReq) Reset()         { *m = BatchAddAliasSignReq{} }
func (m *BatchAddAliasSignReq) String() string { return proto.CompactTextString(m) }
func (*BatchAddAliasSignReq) ProtoMessage()    {}
func (*BatchAddAliasSignReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{56}
}
func (m *BatchAddAliasSignReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddAliasSignReq.Unmarshal(m, b)
}
func (m *BatchAddAliasSignReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddAliasSignReq.Marshal(b, m, deterministic)
}
func (dst *BatchAddAliasSignReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddAliasSignReq.Merge(dst, src)
}
func (m *BatchAddAliasSignReq) XXX_Size() int {
	return xxx_messageInfo_BatchAddAliasSignReq.Size(m)
}
func (m *BatchAddAliasSignReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddAliasSignReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddAliasSignReq proto.InternalMessageInfo

func (m *BatchAddAliasSignReq) GetAliasSignList() []uint32 {
	if m != nil {
		return m.AliasSignList
	}
	return nil
}

func (m *BatchAddAliasSignReq) GetHandler() string {
	if m != nil {
		return m.Handler
	}
	return ""
}

func (m *BatchAddAliasSignReq) GetAliasSignInfoList() []*AliasSignInfo {
	if m != nil {
		return m.AliasSignInfoList
	}
	return nil
}

type AliasSignInfo struct {
	Uid                  uint32          `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Status               AliasSignStatus `protobuf:"varint,2,opt,name=status,proto3,enum=officialcert_go.AliasSignStatus" json:"status,omitempty"`
	Level                string          `protobuf:"bytes,3,opt,name=level,proto3" json:"level,omitempty"`
	AliasExtendInfo      []byte          `protobuf:"bytes,4,opt,name=alias_extend_info,json=aliasExtendInfo,proto3" json:"alias_extend_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *AliasSignInfo) Reset()         { *m = AliasSignInfo{} }
func (m *AliasSignInfo) String() string { return proto.CompactTextString(m) }
func (*AliasSignInfo) ProtoMessage()    {}
func (*AliasSignInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{57}
}
func (m *AliasSignInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AliasSignInfo.Unmarshal(m, b)
}
func (m *AliasSignInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AliasSignInfo.Marshal(b, m, deterministic)
}
func (dst *AliasSignInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AliasSignInfo.Merge(dst, src)
}
func (m *AliasSignInfo) XXX_Size() int {
	return xxx_messageInfo_AliasSignInfo.Size(m)
}
func (m *AliasSignInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AliasSignInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AliasSignInfo proto.InternalMessageInfo

func (m *AliasSignInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AliasSignInfo) GetStatus() AliasSignStatus {
	if m != nil {
		return m.Status
	}
	return AliasSignStatus_AliasSignStatusNone
}

func (m *AliasSignInfo) GetLevel() string {
	if m != nil {
		return m.Level
	}
	return ""
}

func (m *AliasSignInfo) GetAliasExtendInfo() []byte {
	if m != nil {
		return m.AliasExtendInfo
	}
	return nil
}

type BatchAddAliasSignResp struct {
	ErrContent           string   `protobuf:"bytes,1,opt,name=err_content,json=errContent,proto3" json:"err_content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchAddAliasSignResp) Reset()         { *m = BatchAddAliasSignResp{} }
func (m *BatchAddAliasSignResp) String() string { return proto.CompactTextString(m) }
func (*BatchAddAliasSignResp) ProtoMessage()    {}
func (*BatchAddAliasSignResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{58}
}
func (m *BatchAddAliasSignResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddAliasSignResp.Unmarshal(m, b)
}
func (m *BatchAddAliasSignResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddAliasSignResp.Marshal(b, m, deterministic)
}
func (dst *BatchAddAliasSignResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddAliasSignResp.Merge(dst, src)
}
func (m *BatchAddAliasSignResp) XXX_Size() int {
	return xxx_messageInfo_BatchAddAliasSignResp.Size(m)
}
func (m *BatchAddAliasSignResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddAliasSignResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddAliasSignResp proto.InternalMessageInfo

func (m *BatchAddAliasSignResp) GetErrContent() string {
	if m != nil {
		return m.ErrContent
	}
	return ""
}

type BatchDelAliasSignReq struct {
	Uid                  []uint32 `protobuf:"varint,1,rep,packed,name=uid,proto3" json:"uid,omitempty"`
	Handler              string   `protobuf:"bytes,2,opt,name=handler,proto3" json:"handler,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDelAliasSignReq) Reset()         { *m = BatchDelAliasSignReq{} }
func (m *BatchDelAliasSignReq) String() string { return proto.CompactTextString(m) }
func (*BatchDelAliasSignReq) ProtoMessage()    {}
func (*BatchDelAliasSignReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{59}
}
func (m *BatchDelAliasSignReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelAliasSignReq.Unmarshal(m, b)
}
func (m *BatchDelAliasSignReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelAliasSignReq.Marshal(b, m, deterministic)
}
func (dst *BatchDelAliasSignReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelAliasSignReq.Merge(dst, src)
}
func (m *BatchDelAliasSignReq) XXX_Size() int {
	return xxx_messageInfo_BatchDelAliasSignReq.Size(m)
}
func (m *BatchDelAliasSignReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelAliasSignReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelAliasSignReq proto.InternalMessageInfo

func (m *BatchDelAliasSignReq) GetUid() []uint32 {
	if m != nil {
		return m.Uid
	}
	return nil
}

func (m *BatchDelAliasSignReq) GetHandler() string {
	if m != nil {
		return m.Handler
	}
	return ""
}

type BatchDelAliasSignResp struct {
	ErrContent           string   `protobuf:"bytes,1,opt,name=err_content,json=errContent,proto3" json:"err_content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDelAliasSignResp) Reset()         { *m = BatchDelAliasSignResp{} }
func (m *BatchDelAliasSignResp) String() string { return proto.CompactTextString(m) }
func (*BatchDelAliasSignResp) ProtoMessage()    {}
func (*BatchDelAliasSignResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{60}
}
func (m *BatchDelAliasSignResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelAliasSignResp.Unmarshal(m, b)
}
func (m *BatchDelAliasSignResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelAliasSignResp.Marshal(b, m, deterministic)
}
func (dst *BatchDelAliasSignResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelAliasSignResp.Merge(dst, src)
}
func (m *BatchDelAliasSignResp) XXX_Size() int {
	return xxx_messageInfo_BatchDelAliasSignResp.Size(m)
}
func (m *BatchDelAliasSignResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelAliasSignResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelAliasSignResp proto.InternalMessageInfo

func (m *BatchDelAliasSignResp) GetErrContent() string {
	if m != nil {
		return m.ErrContent
	}
	return ""
}

type BatchCheckAliasSignReq struct {
	Uid                  []uint32                         `protobuf:"varint,1,rep,packed,name=uid,proto3" json:"uid,omitempty"`
	CheckType            BatchCheckAliasSignReq_CheckType `protobuf:"varint,2,opt,name=check_type,json=checkType,proto3,enum=officialcert_go.BatchCheckAliasSignReq_CheckType" json:"check_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *BatchCheckAliasSignReq) Reset()         { *m = BatchCheckAliasSignReq{} }
func (m *BatchCheckAliasSignReq) String() string { return proto.CompactTextString(m) }
func (*BatchCheckAliasSignReq) ProtoMessage()    {}
func (*BatchCheckAliasSignReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{61}
}
func (m *BatchCheckAliasSignReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckAliasSignReq.Unmarshal(m, b)
}
func (m *BatchCheckAliasSignReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckAliasSignReq.Marshal(b, m, deterministic)
}
func (dst *BatchCheckAliasSignReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckAliasSignReq.Merge(dst, src)
}
func (m *BatchCheckAliasSignReq) XXX_Size() int {
	return xxx_messageInfo_BatchCheckAliasSignReq.Size(m)
}
func (m *BatchCheckAliasSignReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckAliasSignReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckAliasSignReq proto.InternalMessageInfo

func (m *BatchCheckAliasSignReq) GetUid() []uint32 {
	if m != nil {
		return m.Uid
	}
	return nil
}

func (m *BatchCheckAliasSignReq) GetCheckType() BatchCheckAliasSignReq_CheckType {
	if m != nil {
		return m.CheckType
	}
	return BatchCheckAliasSignReq_CheckTypeAdd
}

type BatchCheckAliasSignResp struct {
	ErrContent           string   `protobuf:"bytes,1,opt,name=err_content,json=errContent,proto3" json:"err_content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchCheckAliasSignResp) Reset()         { *m = BatchCheckAliasSignResp{} }
func (m *BatchCheckAliasSignResp) String() string { return proto.CompactTextString(m) }
func (*BatchCheckAliasSignResp) ProtoMessage()    {}
func (*BatchCheckAliasSignResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{62}
}
func (m *BatchCheckAliasSignResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckAliasSignResp.Unmarshal(m, b)
}
func (m *BatchCheckAliasSignResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckAliasSignResp.Marshal(b, m, deterministic)
}
func (dst *BatchCheckAliasSignResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckAliasSignResp.Merge(dst, src)
}
func (m *BatchCheckAliasSignResp) XXX_Size() int {
	return xxx_messageInfo_BatchCheckAliasSignResp.Size(m)
}
func (m *BatchCheckAliasSignResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckAliasSignResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckAliasSignResp proto.InternalMessageInfo

func (m *BatchCheckAliasSignResp) GetErrContent() string {
	if m != nil {
		return m.ErrContent
	}
	return ""
}

type BatchGetAliasSignReq struct {
	Uid                  []uint32 `protobuf:"varint,1,rep,packed,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetAliasSignReq) Reset()         { *m = BatchGetAliasSignReq{} }
func (m *BatchGetAliasSignReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetAliasSignReq) ProtoMessage()    {}
func (*BatchGetAliasSignReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{63}
}
func (m *BatchGetAliasSignReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAliasSignReq.Unmarshal(m, b)
}
func (m *BatchGetAliasSignReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAliasSignReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetAliasSignReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAliasSignReq.Merge(dst, src)
}
func (m *BatchGetAliasSignReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetAliasSignReq.Size(m)
}
func (m *BatchGetAliasSignReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAliasSignReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAliasSignReq proto.InternalMessageInfo

func (m *BatchGetAliasSignReq) GetUid() []uint32 {
	if m != nil {
		return m.Uid
	}
	return nil
}

type BatchGetAliasSignResp struct {
	AliasSignList        []*AliasSignInfo `protobuf:"bytes,1,rep,name=alias_sign_list,json=aliasSignList,proto3" json:"alias_sign_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *BatchGetAliasSignResp) Reset()         { *m = BatchGetAliasSignResp{} }
func (m *BatchGetAliasSignResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetAliasSignResp) ProtoMessage()    {}
func (*BatchGetAliasSignResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_go_318bf82ae709273b, []int{64}
}
func (m *BatchGetAliasSignResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAliasSignResp.Unmarshal(m, b)
}
func (m *BatchGetAliasSignResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAliasSignResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetAliasSignResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAliasSignResp.Merge(dst, src)
}
func (m *BatchGetAliasSignResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetAliasSignResp.Size(m)
}
func (m *BatchGetAliasSignResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAliasSignResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAliasSignResp proto.InternalMessageInfo

func (m *BatchGetAliasSignResp) GetAliasSignList() []*AliasSignInfo {
	if m != nil {
		return m.AliasSignList
	}
	return nil
}

func init() {
	proto.RegisterType((*OfficialCertInfo)(nil), "officialcert_go.OfficialCertInfo")
	proto.RegisterType((*UserOfficialCertInfo)(nil), "officialcert_go.UserOfficialCertInfo")
	proto.RegisterType((*GetUserOfficialCertReq)(nil), "officialcert_go.GetUserOfficialCertReq")
	proto.RegisterType((*GetUserOfficialCertResp)(nil), "officialcert_go.GetUserOfficialCertResp")
	proto.RegisterType((*ListUserOfficialCertReq)(nil), "officialcert_go.ListUserOfficialCertReq")
	proto.RegisterType((*ListUserOfficialCertResp)(nil), "officialcert_go.ListUserOfficialCertResp")
	proto.RegisterType((*SetUserOfficialCertReq)(nil), "officialcert_go.SetUserOfficialCertReq")
	proto.RegisterType((*SetUserOfficialCertResp)(nil), "officialcert_go.SetUserOfficialCertResp")
	proto.RegisterType((*DelUserOfficialCertReq)(nil), "officialcert_go.DelUserOfficialCertReq")
	proto.RegisterType((*DelUserOfficialCertResp)(nil), "officialcert_go.DelUserOfficialCertResp")
	proto.RegisterType((*DelCertInfo)(nil), "officialcert_go.DelCertInfo")
	proto.RegisterType((*BatchDelOfficalCertsReq)(nil), "officialcert_go.BatchDelOfficalCertsReq")
	proto.RegisterType((*BatchDelOfficalCertsResp)(nil), "officialcert_go.BatchDelOfficalCertsResp")
	proto.RegisterType((*BatchGetUserOfficialCertReq)(nil), "officialcert_go.BatchGetUserOfficialCertReq")
	proto.RegisterType((*BatchGetUserOfficialCertResp)(nil), "officialcert_go.BatchGetUserOfficialCertResp")
	proto.RegisterType((*SetUserWearCertificationReq)(nil), "officialcert_go.SetUserWearCertificationReq")
	proto.RegisterType((*SetUserWearCertificationResp)(nil), "officialcert_go.SetUserWearCertificationResp")
	proto.RegisterType((*GetUserAllOfficialCertsReq)(nil), "officialcert_go.GetUserAllOfficialCertsReq")
	proto.RegisterType((*GetUserAllOfficialCertsResp)(nil), "officialcert_go.GetUserAllOfficialCertsResp")
	proto.RegisterType((*GetUserCertAttributeReq)(nil), "officialcert_go.GetUserCertAttributeReq")
	proto.RegisterType((*GetUserCertAttributeResp)(nil), "officialcert_go.GetUserCertAttributeResp")
	proto.RegisterType((*CheckUserCertAttributeReq)(nil), "officialcert_go.CheckUserCertAttributeReq")
	proto.RegisterType((*CheckUserCertAttributeResp)(nil), "officialcert_go.CheckUserCertAttributeResp")
	proto.RegisterType((*DirectorCertInfo)(nil), "officialcert_go.DirectorCertInfo")
	proto.RegisterType((*UserDirectorCertInfo)(nil), "officialcert_go.UserDirectorCertInfo")
	proto.RegisterType((*GetUserDirectorCertReq)(nil), "officialcert_go.GetUserDirectorCertReq")
	proto.RegisterType((*GetUserDirectorCertResp)(nil), "officialcert_go.GetUserDirectorCertResp")
	proto.RegisterType((*BatchGetUserDirectorCertReq)(nil), "officialcert_go.BatchGetUserDirectorCertReq")
	proto.RegisterType((*BatchGetUserDirectorCertResp)(nil), "officialcert_go.BatchGetUserDirectorCertResp")
	proto.RegisterType((*SetUserDirectorCertReq)(nil), "officialcert_go.SetUserDirectorCertReq")
	proto.RegisterType((*SetUserDirectorCertResp)(nil), "officialcert_go.SetUserDirectorCertResp")
	proto.RegisterType((*AddUserDirectorCertReq)(nil), "officialcert_go.AddUserDirectorCertReq")
	proto.RegisterType((*AddUserDirectorCertResp)(nil), "officialcert_go.AddUserDirectorCertResp")
	proto.RegisterType((*ListUserDirectorCertReq)(nil), "officialcert_go.ListUserDirectorCertReq")
	proto.RegisterType((*ListUserDirectorCertResp)(nil), "officialcert_go.ListUserDirectorCertResp")
	proto.RegisterType((*CertifyItemInfo)(nil), "officialcert_go.CertifyItemInfo")
	proto.RegisterType((*BatchAddCertItemReq)(nil), "officialcert_go.BatchAddCertItemReq")
	proto.RegisterType((*BatchAddCertItemResp)(nil), "officialcert_go.BatchAddCertItemResp")
	proto.RegisterType((*ListCertItemReq)(nil), "officialcert_go.ListCertItemReq")
	proto.RegisterType((*ListCertItemResp)(nil), "officialcert_go.ListCertItemResp")
	proto.RegisterType((*GrantOfficialCertInfo)(nil), "officialcert_go.GrantOfficialCertInfo")
	proto.RegisterType((*BatchGrantOfficialCertReq)(nil), "officialcert_go.BatchGrantOfficialCertReq")
	proto.RegisterType((*BatchGrantOfficialCertResp)(nil), "officialcert_go.BatchGrantOfficialCertResp")
	proto.RegisterType((*ListOfficialCertReq)(nil), "officialcert_go.ListOfficialCertReq")
	proto.RegisterType((*ListOfficialCertResp)(nil), "officialcert_go.ListOfficialCertResp")
	proto.RegisterType((*DelOfficialCertReq)(nil), "officialcert_go.DelOfficialCertReq")
	proto.RegisterType((*DelOfficialCertResp)(nil), "officialcert_go.DelOfficialCertResp")
	proto.RegisterType((*UpdateAliasReq)(nil), "officialcert_go.UpdateAliasReq")
	proto.RegisterType((*UpdateAliasResp)(nil), "officialcert_go.UpdateAliasResp")
	proto.RegisterType((*ChangeDisplayIDReq)(nil), "officialcert_go.ChangeDisplayIDReq")
	proto.RegisterType((*ChangeDisplayIDResp)(nil), "officialcert_go.ChangeDisplayIDResp")
	proto.RegisterType((*ListAliasReq)(nil), "officialcert_go.ListAliasReq")
	proto.RegisterType((*ListAliasResp)(nil), "officialcert_go.ListAliasResp")
	proto.RegisterType((*AliasInfo)(nil), "officialcert_go.AliasInfo")
	proto.RegisterType((*CreateUserByAliasReq)(nil), "officialcert_go.CreateUserByAliasReq")
	proto.RegisterType((*CreateUserByAliasResp)(nil), "officialcert_go.CreateUserByAliasResp")
	proto.RegisterType((*BatchAddAliasSignReq)(nil), "officialcert_go.BatchAddAliasSignReq")
	proto.RegisterType((*AliasSignInfo)(nil), "officialcert_go.AliasSignInfo")
	proto.RegisterType((*BatchAddAliasSignResp)(nil), "officialcert_go.BatchAddAliasSignResp")
	proto.RegisterType((*BatchDelAliasSignReq)(nil), "officialcert_go.BatchDelAliasSignReq")
	proto.RegisterType((*BatchDelAliasSignResp)(nil), "officialcert_go.BatchDelAliasSignResp")
	proto.RegisterType((*BatchCheckAliasSignReq)(nil), "officialcert_go.BatchCheckAliasSignReq")
	proto.RegisterType((*BatchCheckAliasSignResp)(nil), "officialcert_go.BatchCheckAliasSignResp")
	proto.RegisterType((*BatchGetAliasSignReq)(nil), "officialcert_go.BatchGetAliasSignReq")
	proto.RegisterType((*BatchGetAliasSignResp)(nil), "officialcert_go.BatchGetAliasSignResp")
	proto.RegisterEnum("officialcert_go.ECertAttrType", ECertAttrType_name, ECertAttrType_value)
	proto.RegisterEnum("officialcert_go.LocationType", LocationType_name, LocationType_value)
	proto.RegisterEnum("officialcert_go.STATUS_TYPE", STATUS_TYPE_name, STATUS_TYPE_value)
	proto.RegisterEnum("officialcert_go.QUERY_TYPE", QUERY_TYPE_name, QUERY_TYPE_value)
	proto.RegisterEnum("officialcert_go.QUERY_ALIAS_TYPE", QUERY_ALIAS_TYPE_name, QUERY_ALIAS_TYPE_value)
	proto.RegisterEnum("officialcert_go.AliasSignStatus", AliasSignStatus_name, AliasSignStatus_value)
	proto.RegisterEnum("officialcert_go.AliasInfo_HandlerType", AliasInfo_HandlerType_name, AliasInfo_HandlerType_value)
	proto.RegisterEnum("officialcert_go.BatchCheckAliasSignReq_CheckType", BatchCheckAliasSignReq_CheckType_name, BatchCheckAliasSignReq_CheckType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// OfficialCertClient is the client API for OfficialCert service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type OfficialCertClient interface {
	GetUserOfficialCert(ctx context.Context, in *GetUserOfficialCertReq, opts ...grpc.CallOption) (*GetUserOfficialCertResp, error)
	ListUserOfficialCert(ctx context.Context, in *ListUserOfficialCertReq, opts ...grpc.CallOption) (*ListUserOfficialCertResp, error)
	// 包含主理人style
	ListUserOfficialCertV2(ctx context.Context, in *ListUserOfficialCertReq, opts ...grpc.CallOption) (*ListUserOfficialCertResp, error)
	SetUserOfficialCert(ctx context.Context, in *SetUserOfficialCertReq, opts ...grpc.CallOption) (*SetUserOfficialCertResp, error)
	DelUserOfficialCert(ctx context.Context, in *DelUserOfficialCertReq, opts ...grpc.CallOption) (*DelUserOfficialCertResp, error)
	BatchGetUserOfficialCert(ctx context.Context, in *BatchGetUserOfficialCertReq, opts ...grpc.CallOption) (*BatchGetUserOfficialCertResp, error)
	SetUserWearCertification(ctx context.Context, in *SetUserWearCertificationReq, opts ...grpc.CallOption) (*SetUserWearCertificationResp, error)
	GetUserAllOfficialCerts(ctx context.Context, in *GetUserAllOfficialCertsReq, opts ...grpc.CallOption) (*GetUserAllOfficialCertsResp, error)
	GetUserCertAttribute(ctx context.Context, in *GetUserCertAttributeReq, opts ...grpc.CallOption) (*GetUserCertAttributeResp, error)
	CheckUserCertAttribute(ctx context.Context, in *CheckUserCertAttributeReq, opts ...grpc.CallOption) (*CheckUserCertAttributeResp, error)
	BatchDelOfficalCerts(ctx context.Context, in *BatchDelOfficalCertsReq, opts ...grpc.CallOption) (*BatchDelOfficalCertsResp, error)
	// 获取用户的主理人认证信息
	GetUserDirectorCert(ctx context.Context, in *GetUserDirectorCertReq, opts ...grpc.CallOption) (*GetUserDirectorCertResp, error)
	// 批量获取用户的主理人认证信息
	BatchGetUserDirectorCert(ctx context.Context, in *BatchGetUserDirectorCertReq, opts ...grpc.CallOption) (*BatchGetUserDirectorCertResp, error)
	// 后台创建主理人
	AddUserDirectorCert(ctx context.Context, in *AddUserDirectorCertReq, opts ...grpc.CallOption) (*AddUserDirectorCertResp, error)
	// 后台配置主理人相关信息
	SetUserDirectorCert(ctx context.Context, in *SetUserDirectorCertReq, opts ...grpc.CallOption) (*SetUserDirectorCertResp, error)
	// 获取主理人列表
	ListUserDirectorCert(ctx context.Context, in *ListUserDirectorCertReq, opts ...grpc.CallOption) (*ListUserDirectorCertResp, error)
	// 新运营后台接口
	BatchAddCertItem(ctx context.Context, in *BatchAddCertItemReq, opts ...grpc.CallOption) (*BatchAddCertItemResp, error)
	ListCertItem(ctx context.Context, in *ListCertItemReq, opts ...grpc.CallOption) (*ListCertItemResp, error)
	BatchGrantOfficialCert(ctx context.Context, in *BatchGrantOfficialCertReq, opts ...grpc.CallOption) (*BatchGrantOfficialCertResp, error)
	DelOfficialCert(ctx context.Context, in *DelOfficialCertReq, opts ...grpc.CallOption) (*DelOfficialCertResp, error)
	ListOfficialCert(ctx context.Context, in *ListOfficialCertReq, opts ...grpc.CallOption) (*ListOfficialCertResp, error)
	// 靓号原有接口
	// 靓号管理
	UpdateAlias(ctx context.Context, in *UpdateAliasReq, opts ...grpc.CallOption) (*UpdateAliasResp, error)
	ListAlias(ctx context.Context, in *ListAliasReq, opts ...grpc.CallOption) (*ListAliasResp, error)
	// 创建账号
	CreateUserByAlias(ctx context.Context, in *CreateUserByAliasReq, opts ...grpc.CallOption) (*CreateUserByAliasResp, error)
	// 靓号认证相关
	BatchAddAliasSign(ctx context.Context, in *BatchAddAliasSignReq, opts ...grpc.CallOption) (*BatchAddAliasSignResp, error)
	BatchDelAliasSign(ctx context.Context, in *BatchDelAliasSignReq, opts ...grpc.CallOption) (*BatchDelAliasSignResp, error)
	BatchCheckAliasSign(ctx context.Context, in *BatchCheckAliasSignReq, opts ...grpc.CallOption) (*BatchCheckAliasSignResp, error)
	BatchGetAliasSign(ctx context.Context, in *BatchGetAliasSignReq, opts ...grpc.CallOption) (*BatchGetAliasSignResp, error)
}

type officialCertClient struct {
	cc *grpc.ClientConn
}

func NewOfficialCertClient(cc *grpc.ClientConn) OfficialCertClient {
	return &officialCertClient{cc}
}

func (c *officialCertClient) GetUserOfficialCert(ctx context.Context, in *GetUserOfficialCertReq, opts ...grpc.CallOption) (*GetUserOfficialCertResp, error) {
	out := new(GetUserOfficialCertResp)
	err := c.cc.Invoke(ctx, "/officialcert_go.OfficialCert/GetUserOfficialCert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) ListUserOfficialCert(ctx context.Context, in *ListUserOfficialCertReq, opts ...grpc.CallOption) (*ListUserOfficialCertResp, error) {
	out := new(ListUserOfficialCertResp)
	err := c.cc.Invoke(ctx, "/officialcert_go.OfficialCert/ListUserOfficialCert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) ListUserOfficialCertV2(ctx context.Context, in *ListUserOfficialCertReq, opts ...grpc.CallOption) (*ListUserOfficialCertResp, error) {
	out := new(ListUserOfficialCertResp)
	err := c.cc.Invoke(ctx, "/officialcert_go.OfficialCert/ListUserOfficialCertV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) SetUserOfficialCert(ctx context.Context, in *SetUserOfficialCertReq, opts ...grpc.CallOption) (*SetUserOfficialCertResp, error) {
	out := new(SetUserOfficialCertResp)
	err := c.cc.Invoke(ctx, "/officialcert_go.OfficialCert/SetUserOfficialCert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) DelUserOfficialCert(ctx context.Context, in *DelUserOfficialCertReq, opts ...grpc.CallOption) (*DelUserOfficialCertResp, error) {
	out := new(DelUserOfficialCertResp)
	err := c.cc.Invoke(ctx, "/officialcert_go.OfficialCert/DelUserOfficialCert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) BatchGetUserOfficialCert(ctx context.Context, in *BatchGetUserOfficialCertReq, opts ...grpc.CallOption) (*BatchGetUserOfficialCertResp, error) {
	out := new(BatchGetUserOfficialCertResp)
	err := c.cc.Invoke(ctx, "/officialcert_go.OfficialCert/BatchGetUserOfficialCert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) SetUserWearCertification(ctx context.Context, in *SetUserWearCertificationReq, opts ...grpc.CallOption) (*SetUserWearCertificationResp, error) {
	out := new(SetUserWearCertificationResp)
	err := c.cc.Invoke(ctx, "/officialcert_go.OfficialCert/SetUserWearCertification", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) GetUserAllOfficialCerts(ctx context.Context, in *GetUserAllOfficialCertsReq, opts ...grpc.CallOption) (*GetUserAllOfficialCertsResp, error) {
	out := new(GetUserAllOfficialCertsResp)
	err := c.cc.Invoke(ctx, "/officialcert_go.OfficialCert/GetUserAllOfficialCerts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) GetUserCertAttribute(ctx context.Context, in *GetUserCertAttributeReq, opts ...grpc.CallOption) (*GetUserCertAttributeResp, error) {
	out := new(GetUserCertAttributeResp)
	err := c.cc.Invoke(ctx, "/officialcert_go.OfficialCert/GetUserCertAttribute", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) CheckUserCertAttribute(ctx context.Context, in *CheckUserCertAttributeReq, opts ...grpc.CallOption) (*CheckUserCertAttributeResp, error) {
	out := new(CheckUserCertAttributeResp)
	err := c.cc.Invoke(ctx, "/officialcert_go.OfficialCert/CheckUserCertAttribute", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) BatchDelOfficalCerts(ctx context.Context, in *BatchDelOfficalCertsReq, opts ...grpc.CallOption) (*BatchDelOfficalCertsResp, error) {
	out := new(BatchDelOfficalCertsResp)
	err := c.cc.Invoke(ctx, "/officialcert_go.OfficialCert/BatchDelOfficalCerts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) GetUserDirectorCert(ctx context.Context, in *GetUserDirectorCertReq, opts ...grpc.CallOption) (*GetUserDirectorCertResp, error) {
	out := new(GetUserDirectorCertResp)
	err := c.cc.Invoke(ctx, "/officialcert_go.OfficialCert/GetUserDirectorCert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) BatchGetUserDirectorCert(ctx context.Context, in *BatchGetUserDirectorCertReq, opts ...grpc.CallOption) (*BatchGetUserDirectorCertResp, error) {
	out := new(BatchGetUserDirectorCertResp)
	err := c.cc.Invoke(ctx, "/officialcert_go.OfficialCert/BatchGetUserDirectorCert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) AddUserDirectorCert(ctx context.Context, in *AddUserDirectorCertReq, opts ...grpc.CallOption) (*AddUserDirectorCertResp, error) {
	out := new(AddUserDirectorCertResp)
	err := c.cc.Invoke(ctx, "/officialcert_go.OfficialCert/AddUserDirectorCert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) SetUserDirectorCert(ctx context.Context, in *SetUserDirectorCertReq, opts ...grpc.CallOption) (*SetUserDirectorCertResp, error) {
	out := new(SetUserDirectorCertResp)
	err := c.cc.Invoke(ctx, "/officialcert_go.OfficialCert/SetUserDirectorCert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) ListUserDirectorCert(ctx context.Context, in *ListUserDirectorCertReq, opts ...grpc.CallOption) (*ListUserDirectorCertResp, error) {
	out := new(ListUserDirectorCertResp)
	err := c.cc.Invoke(ctx, "/officialcert_go.OfficialCert/ListUserDirectorCert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) BatchAddCertItem(ctx context.Context, in *BatchAddCertItemReq, opts ...grpc.CallOption) (*BatchAddCertItemResp, error) {
	out := new(BatchAddCertItemResp)
	err := c.cc.Invoke(ctx, "/officialcert_go.OfficialCert/BatchAddCertItem", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) ListCertItem(ctx context.Context, in *ListCertItemReq, opts ...grpc.CallOption) (*ListCertItemResp, error) {
	out := new(ListCertItemResp)
	err := c.cc.Invoke(ctx, "/officialcert_go.OfficialCert/ListCertItem", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) BatchGrantOfficialCert(ctx context.Context, in *BatchGrantOfficialCertReq, opts ...grpc.CallOption) (*BatchGrantOfficialCertResp, error) {
	out := new(BatchGrantOfficialCertResp)
	err := c.cc.Invoke(ctx, "/officialcert_go.OfficialCert/BatchGrantOfficialCert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) DelOfficialCert(ctx context.Context, in *DelOfficialCertReq, opts ...grpc.CallOption) (*DelOfficialCertResp, error) {
	out := new(DelOfficialCertResp)
	err := c.cc.Invoke(ctx, "/officialcert_go.OfficialCert/DelOfficialCert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) ListOfficialCert(ctx context.Context, in *ListOfficialCertReq, opts ...grpc.CallOption) (*ListOfficialCertResp, error) {
	out := new(ListOfficialCertResp)
	err := c.cc.Invoke(ctx, "/officialcert_go.OfficialCert/ListOfficialCert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) UpdateAlias(ctx context.Context, in *UpdateAliasReq, opts ...grpc.CallOption) (*UpdateAliasResp, error) {
	out := new(UpdateAliasResp)
	err := c.cc.Invoke(ctx, "/officialcert_go.OfficialCert/UpdateAlias", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) ListAlias(ctx context.Context, in *ListAliasReq, opts ...grpc.CallOption) (*ListAliasResp, error) {
	out := new(ListAliasResp)
	err := c.cc.Invoke(ctx, "/officialcert_go.OfficialCert/ListAlias", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) CreateUserByAlias(ctx context.Context, in *CreateUserByAliasReq, opts ...grpc.CallOption) (*CreateUserByAliasResp, error) {
	out := new(CreateUserByAliasResp)
	err := c.cc.Invoke(ctx, "/officialcert_go.OfficialCert/CreateUserByAlias", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) BatchAddAliasSign(ctx context.Context, in *BatchAddAliasSignReq, opts ...grpc.CallOption) (*BatchAddAliasSignResp, error) {
	out := new(BatchAddAliasSignResp)
	err := c.cc.Invoke(ctx, "/officialcert_go.OfficialCert/BatchAddAliasSign", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) BatchDelAliasSign(ctx context.Context, in *BatchDelAliasSignReq, opts ...grpc.CallOption) (*BatchDelAliasSignResp, error) {
	out := new(BatchDelAliasSignResp)
	err := c.cc.Invoke(ctx, "/officialcert_go.OfficialCert/BatchDelAliasSign", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) BatchCheckAliasSign(ctx context.Context, in *BatchCheckAliasSignReq, opts ...grpc.CallOption) (*BatchCheckAliasSignResp, error) {
	out := new(BatchCheckAliasSignResp)
	err := c.cc.Invoke(ctx, "/officialcert_go.OfficialCert/BatchCheckAliasSign", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) BatchGetAliasSign(ctx context.Context, in *BatchGetAliasSignReq, opts ...grpc.CallOption) (*BatchGetAliasSignResp, error) {
	out := new(BatchGetAliasSignResp)
	err := c.cc.Invoke(ctx, "/officialcert_go.OfficialCert/BatchGetAliasSign", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OfficialCertServer is the server API for OfficialCert service.
type OfficialCertServer interface {
	GetUserOfficialCert(context.Context, *GetUserOfficialCertReq) (*GetUserOfficialCertResp, error)
	ListUserOfficialCert(context.Context, *ListUserOfficialCertReq) (*ListUserOfficialCertResp, error)
	// 包含主理人style
	ListUserOfficialCertV2(context.Context, *ListUserOfficialCertReq) (*ListUserOfficialCertResp, error)
	SetUserOfficialCert(context.Context, *SetUserOfficialCertReq) (*SetUserOfficialCertResp, error)
	DelUserOfficialCert(context.Context, *DelUserOfficialCertReq) (*DelUserOfficialCertResp, error)
	BatchGetUserOfficialCert(context.Context, *BatchGetUserOfficialCertReq) (*BatchGetUserOfficialCertResp, error)
	SetUserWearCertification(context.Context, *SetUserWearCertificationReq) (*SetUserWearCertificationResp, error)
	GetUserAllOfficialCerts(context.Context, *GetUserAllOfficialCertsReq) (*GetUserAllOfficialCertsResp, error)
	GetUserCertAttribute(context.Context, *GetUserCertAttributeReq) (*GetUserCertAttributeResp, error)
	CheckUserCertAttribute(context.Context, *CheckUserCertAttributeReq) (*CheckUserCertAttributeResp, error)
	BatchDelOfficalCerts(context.Context, *BatchDelOfficalCertsReq) (*BatchDelOfficalCertsResp, error)
	// 获取用户的主理人认证信息
	GetUserDirectorCert(context.Context, *GetUserDirectorCertReq) (*GetUserDirectorCertResp, error)
	// 批量获取用户的主理人认证信息
	BatchGetUserDirectorCert(context.Context, *BatchGetUserDirectorCertReq) (*BatchGetUserDirectorCertResp, error)
	// 后台创建主理人
	AddUserDirectorCert(context.Context, *AddUserDirectorCertReq) (*AddUserDirectorCertResp, error)
	// 后台配置主理人相关信息
	SetUserDirectorCert(context.Context, *SetUserDirectorCertReq) (*SetUserDirectorCertResp, error)
	// 获取主理人列表
	ListUserDirectorCert(context.Context, *ListUserDirectorCertReq) (*ListUserDirectorCertResp, error)
	// 新运营后台接口
	BatchAddCertItem(context.Context, *BatchAddCertItemReq) (*BatchAddCertItemResp, error)
	ListCertItem(context.Context, *ListCertItemReq) (*ListCertItemResp, error)
	BatchGrantOfficialCert(context.Context, *BatchGrantOfficialCertReq) (*BatchGrantOfficialCertResp, error)
	DelOfficialCert(context.Context, *DelOfficialCertReq) (*DelOfficialCertResp, error)
	ListOfficialCert(context.Context, *ListOfficialCertReq) (*ListOfficialCertResp, error)
	// 靓号原有接口
	// 靓号管理
	UpdateAlias(context.Context, *UpdateAliasReq) (*UpdateAliasResp, error)
	ListAlias(context.Context, *ListAliasReq) (*ListAliasResp, error)
	// 创建账号
	CreateUserByAlias(context.Context, *CreateUserByAliasReq) (*CreateUserByAliasResp, error)
	// 靓号认证相关
	BatchAddAliasSign(context.Context, *BatchAddAliasSignReq) (*BatchAddAliasSignResp, error)
	BatchDelAliasSign(context.Context, *BatchDelAliasSignReq) (*BatchDelAliasSignResp, error)
	BatchCheckAliasSign(context.Context, *BatchCheckAliasSignReq) (*BatchCheckAliasSignResp, error)
	BatchGetAliasSign(context.Context, *BatchGetAliasSignReq) (*BatchGetAliasSignResp, error)
}

func RegisterOfficialCertServer(s *grpc.Server, srv OfficialCertServer) {
	s.RegisterService(&_OfficialCert_serviceDesc, srv)
}

func _OfficialCert_GetUserOfficialCert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserOfficialCertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).GetUserOfficialCert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert_go.OfficialCert/GetUserOfficialCert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).GetUserOfficialCert(ctx, req.(*GetUserOfficialCertReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_ListUserOfficialCert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUserOfficialCertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).ListUserOfficialCert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert_go.OfficialCert/ListUserOfficialCert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).ListUserOfficialCert(ctx, req.(*ListUserOfficialCertReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_ListUserOfficialCertV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUserOfficialCertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).ListUserOfficialCertV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert_go.OfficialCert/ListUserOfficialCertV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).ListUserOfficialCertV2(ctx, req.(*ListUserOfficialCertReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_SetUserOfficialCert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserOfficialCertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).SetUserOfficialCert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert_go.OfficialCert/SetUserOfficialCert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).SetUserOfficialCert(ctx, req.(*SetUserOfficialCertReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_DelUserOfficialCert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelUserOfficialCertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).DelUserOfficialCert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert_go.OfficialCert/DelUserOfficialCert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).DelUserOfficialCert(ctx, req.(*DelUserOfficialCertReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_BatchGetUserOfficialCert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserOfficialCertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).BatchGetUserOfficialCert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert_go.OfficialCert/BatchGetUserOfficialCert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).BatchGetUserOfficialCert(ctx, req.(*BatchGetUserOfficialCertReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_SetUserWearCertification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserWearCertificationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).SetUserWearCertification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert_go.OfficialCert/SetUserWearCertification",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).SetUserWearCertification(ctx, req.(*SetUserWearCertificationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_GetUserAllOfficialCerts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserAllOfficialCertsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).GetUserAllOfficialCerts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert_go.OfficialCert/GetUserAllOfficialCerts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).GetUserAllOfficialCerts(ctx, req.(*GetUserAllOfficialCertsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_GetUserCertAttribute_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserCertAttributeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).GetUserCertAttribute(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert_go.OfficialCert/GetUserCertAttribute",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).GetUserCertAttribute(ctx, req.(*GetUserCertAttributeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_CheckUserCertAttribute_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserCertAttributeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).CheckUserCertAttribute(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert_go.OfficialCert/CheckUserCertAttribute",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).CheckUserCertAttribute(ctx, req.(*CheckUserCertAttributeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_BatchDelOfficalCerts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDelOfficalCertsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).BatchDelOfficalCerts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert_go.OfficialCert/BatchDelOfficalCerts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).BatchDelOfficalCerts(ctx, req.(*BatchDelOfficalCertsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_GetUserDirectorCert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserDirectorCertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).GetUserDirectorCert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert_go.OfficialCert/GetUserDirectorCert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).GetUserDirectorCert(ctx, req.(*GetUserDirectorCertReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_BatchGetUserDirectorCert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserDirectorCertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).BatchGetUserDirectorCert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert_go.OfficialCert/BatchGetUserDirectorCert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).BatchGetUserDirectorCert(ctx, req.(*BatchGetUserDirectorCertReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_AddUserDirectorCert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserDirectorCertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).AddUserDirectorCert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert_go.OfficialCert/AddUserDirectorCert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).AddUserDirectorCert(ctx, req.(*AddUserDirectorCertReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_SetUserDirectorCert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserDirectorCertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).SetUserDirectorCert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert_go.OfficialCert/SetUserDirectorCert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).SetUserDirectorCert(ctx, req.(*SetUserDirectorCertReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_ListUserDirectorCert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUserDirectorCertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).ListUserDirectorCert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert_go.OfficialCert/ListUserDirectorCert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).ListUserDirectorCert(ctx, req.(*ListUserDirectorCertReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_BatchAddCertItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchAddCertItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).BatchAddCertItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert_go.OfficialCert/BatchAddCertItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).BatchAddCertItem(ctx, req.(*BatchAddCertItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_ListCertItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCertItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).ListCertItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert_go.OfficialCert/ListCertItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).ListCertItem(ctx, req.(*ListCertItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_BatchGrantOfficialCert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGrantOfficialCertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).BatchGrantOfficialCert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert_go.OfficialCert/BatchGrantOfficialCert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).BatchGrantOfficialCert(ctx, req.(*BatchGrantOfficialCertReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_DelOfficialCert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelOfficialCertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).DelOfficialCert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert_go.OfficialCert/DelOfficialCert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).DelOfficialCert(ctx, req.(*DelOfficialCertReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_ListOfficialCert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListOfficialCertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).ListOfficialCert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert_go.OfficialCert/ListOfficialCert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).ListOfficialCert(ctx, req.(*ListOfficialCertReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_UpdateAlias_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAliasReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).UpdateAlias(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert_go.OfficialCert/UpdateAlias",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).UpdateAlias(ctx, req.(*UpdateAliasReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_ListAlias_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAliasReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).ListAlias(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert_go.OfficialCert/ListAlias",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).ListAlias(ctx, req.(*ListAliasReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_CreateUserByAlias_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateUserByAliasReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).CreateUserByAlias(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert_go.OfficialCert/CreateUserByAlias",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).CreateUserByAlias(ctx, req.(*CreateUserByAliasReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_BatchAddAliasSign_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchAddAliasSignReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).BatchAddAliasSign(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert_go.OfficialCert/BatchAddAliasSign",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).BatchAddAliasSign(ctx, req.(*BatchAddAliasSignReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_BatchDelAliasSign_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDelAliasSignReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).BatchDelAliasSign(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert_go.OfficialCert/BatchDelAliasSign",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).BatchDelAliasSign(ctx, req.(*BatchDelAliasSignReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_BatchCheckAliasSign_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCheckAliasSignReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).BatchCheckAliasSign(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert_go.OfficialCert/BatchCheckAliasSign",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).BatchCheckAliasSign(ctx, req.(*BatchCheckAliasSignReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_BatchGetAliasSign_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetAliasSignReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).BatchGetAliasSign(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert_go.OfficialCert/BatchGetAliasSign",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).BatchGetAliasSign(ctx, req.(*BatchGetAliasSignReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _OfficialCert_serviceDesc = grpc.ServiceDesc{
	ServiceName: "officialcert_go.OfficialCert",
	HandlerType: (*OfficialCertServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserOfficialCert",
			Handler:    _OfficialCert_GetUserOfficialCert_Handler,
		},
		{
			MethodName: "ListUserOfficialCert",
			Handler:    _OfficialCert_ListUserOfficialCert_Handler,
		},
		{
			MethodName: "ListUserOfficialCertV2",
			Handler:    _OfficialCert_ListUserOfficialCertV2_Handler,
		},
		{
			MethodName: "SetUserOfficialCert",
			Handler:    _OfficialCert_SetUserOfficialCert_Handler,
		},
		{
			MethodName: "DelUserOfficialCert",
			Handler:    _OfficialCert_DelUserOfficialCert_Handler,
		},
		{
			MethodName: "BatchGetUserOfficialCert",
			Handler:    _OfficialCert_BatchGetUserOfficialCert_Handler,
		},
		{
			MethodName: "SetUserWearCertification",
			Handler:    _OfficialCert_SetUserWearCertification_Handler,
		},
		{
			MethodName: "GetUserAllOfficialCerts",
			Handler:    _OfficialCert_GetUserAllOfficialCerts_Handler,
		},
		{
			MethodName: "GetUserCertAttribute",
			Handler:    _OfficialCert_GetUserCertAttribute_Handler,
		},
		{
			MethodName: "CheckUserCertAttribute",
			Handler:    _OfficialCert_CheckUserCertAttribute_Handler,
		},
		{
			MethodName: "BatchDelOfficalCerts",
			Handler:    _OfficialCert_BatchDelOfficalCerts_Handler,
		},
		{
			MethodName: "GetUserDirectorCert",
			Handler:    _OfficialCert_GetUserDirectorCert_Handler,
		},
		{
			MethodName: "BatchGetUserDirectorCert",
			Handler:    _OfficialCert_BatchGetUserDirectorCert_Handler,
		},
		{
			MethodName: "AddUserDirectorCert",
			Handler:    _OfficialCert_AddUserDirectorCert_Handler,
		},
		{
			MethodName: "SetUserDirectorCert",
			Handler:    _OfficialCert_SetUserDirectorCert_Handler,
		},
		{
			MethodName: "ListUserDirectorCert",
			Handler:    _OfficialCert_ListUserDirectorCert_Handler,
		},
		{
			MethodName: "BatchAddCertItem",
			Handler:    _OfficialCert_BatchAddCertItem_Handler,
		},
		{
			MethodName: "ListCertItem",
			Handler:    _OfficialCert_ListCertItem_Handler,
		},
		{
			MethodName: "BatchGrantOfficialCert",
			Handler:    _OfficialCert_BatchGrantOfficialCert_Handler,
		},
		{
			MethodName: "DelOfficialCert",
			Handler:    _OfficialCert_DelOfficialCert_Handler,
		},
		{
			MethodName: "ListOfficialCert",
			Handler:    _OfficialCert_ListOfficialCert_Handler,
		},
		{
			MethodName: "UpdateAlias",
			Handler:    _OfficialCert_UpdateAlias_Handler,
		},
		{
			MethodName: "ListAlias",
			Handler:    _OfficialCert_ListAlias_Handler,
		},
		{
			MethodName: "CreateUserByAlias",
			Handler:    _OfficialCert_CreateUserByAlias_Handler,
		},
		{
			MethodName: "BatchAddAliasSign",
			Handler:    _OfficialCert_BatchAddAliasSign_Handler,
		},
		{
			MethodName: "BatchDelAliasSign",
			Handler:    _OfficialCert_BatchDelAliasSign_Handler,
		},
		{
			MethodName: "BatchCheckAliasSign",
			Handler:    _OfficialCert_BatchCheckAliasSign_Handler,
		},
		{
			MethodName: "BatchGetAliasSign",
			Handler:    _OfficialCert_BatchGetAliasSign_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "officialcert-go/officialcert-go.proto",
}

func init() {
	proto.RegisterFile("officialcert-go/officialcert-go.proto", fileDescriptor_officialcert_go_318bf82ae709273b)
}

var fileDescriptor_officialcert_go_318bf82ae709273b = []byte{
	// 2863 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x1a, 0x4d, 0x73, 0xdb, 0xc6,
	0x55, 0x00, 0x49, 0x89, 0x7c, 0x94, 0x44, 0x68, 0x25, 0x4b, 0x14, 0x2d, 0xdb, 0x32, 0x9a, 0xb8,
	0x8a, 0x62, 0xcb, 0x13, 0x27, 0x99, 0x49, 0x32, 0xd3, 0x64, 0x68, 0x91, 0x4e, 0x38, 0xa3, 0xca,
	0x0a, 0x29, 0xba, 0x93, 0xcc, 0x34, 0x1c, 0x18, 0x58, 0x49, 0x88, 0x49, 0x00, 0xc6, 0x42, 0x52,
	0x99, 0xe9, 0xa1, 0x39, 0xf5, 0xd4, 0x1f, 0xd0, 0x99, 0xf6, 0xd8, 0x43, 0x7b, 0x6a, 0x2f, 0xfd,
	0x09, 0xfd, 0x01, 0xed, 0xa5, 0x33, 0x3d, 0xf4, 0xa7, 0xb4, 0xb3, 0xbb, 0x00, 0xb8, 0x00, 0x16,
	0x14, 0x9c, 0x69, 0x4f, 0xc4, 0xbe, 0x7d, 0xfb, 0xbe, 0xdf, 0xdb, 0x7d, 0xbb, 0x84, 0xb7, 0xdd,
	0xb3, 0x33, 0xdb, 0xb4, 0x8d, 0xb1, 0x89, 0xfd, 0xe0, 0xd1, 0xb9, 0xfb, 0x38, 0x35, 0x3e, 0xf0,
	0x7c, 0x37, 0x70, 0x51, 0x43, 0x04, 0x8f, 0xce, 0xdd, 0xd6, 0x3d, 0xfc, 0x8b, 0x00, 0x3b, 0xc4,
	0x76, 0x9d, 0xc7, 0xae, 0x17, 0xd8, 0xae, 0x43, 0xa2, 0x5f, 0xbe, 0x42, 0xff, 0x6b, 0x09, 0xb4,
	0xe7, 0xe1, 0xa2, 0x43, 0xec, 0x07, 0x3d, 0xe7, 0xcc, 0x45, 0x1a, 0x94, 0x2e, 0x6d, 0xab, 0xa9,
	0xec, 0x2a, 0x7b, 0x2b, 0x7d, 0xfa, 0x89, 0x36, 0xa0, 0x12, 0xd8, 0xc1, 0x18, 0x37, 0xd5, 0x5d,
	0x65, 0xaf, 0xd6, 0xe7, 0x03, 0x0a, 0xb5, 0x9d, 0xc0, 0x77, 0x9b, 0x25, 0x0e, 0x65, 0x03, 0x0a,
	0x25, 0xc1, 0x74, 0x8c, 0x9b, 0x65, 0x0e, 0x65, 0x03, 0xb4, 0x0a, 0xaa, 0x6d, 0x35, 0x2b, 0x8c,
	0xa4, 0x6a, 0x5b, 0x68, 0x1b, 0xaa, 0x2f, 0xf1, 0xb9, 0xed, 0x8c, 0x02, 0xd2, 0x5c, 0xdc, 0x55,
	0xf6, 0xca, 0xfd, 0x25, 0x36, 0x3e, 0x25, 0xe8, 0x16, 0x2c, 0x62, 0xc7, 0xa2, 0x13, 0x4b, 0x6c,
	0xa2, 0x82, 0x1d, 0x8b, 0x83, 0x6d, 0x32, 0xba, 0x24, 0xb8, 0x59, 0xdd, 0x55, 0xf6, 0xaa, 0xfd,
	0x8a, 0x4d, 0x86, 0x04, 0xa3, 0x1d, 0xa8, 0x19, 0x41, 0xe0, 0xdb, 0x2f, 0x2f, 0x03, 0xdc, 0xac,
	0x31, 0xfa, 0x33, 0x00, 0xfa, 0x0c, 0x76, 0xa8, 0x2d, 0xec, 0xb3, 0xe9, 0x88, 0x78, 0x98, 0x6a,
	0x39, 0xc2, 0x67, 0x67, 0xd8, 0x0c, 0x46, 0x5c, 0x1f, 0x60, 0x32, 0x6e, 0x87, 0x38, 0x03, 0x8e,
	0xd2, 0x65, 0x18, 0xa7, 0x4c, 0xc7, 0x9f, 0xc0, 0xed, 0x1c, 0x02, 0xb6, 0xe9, 0x3a, 0xcd, 0x3a,
	0x5b, 0xdf, 0x94, 0xad, 0xef, 0x99, 0xae, 0x83, 0x36, 0x61, 0x91, 0x04, 0x46, 0x70, 0x49, 0x9a,
	0xcb, 0x4c, 0xb4, 0x70, 0x84, 0xd6, 0xa0, 0x7c, 0x35, 0xb2, 0xad, 0xe6, 0x0a, 0xb7, 0xf1, 0x55,
	0xcf, 0x42, 0x08, 0xca, 0x41, 0x60, 0x5b, 0xcd, 0x55, 0x46, 0x92, 0x7d, 0xa3, 0x26, 0x2c, 0x5d,
	0x18, 0x8e, 0x35, 0xc6, 0x7e, 0xb3, 0xc1, 0xc0, 0xd1, 0x50, 0xff, 0xb3, 0x02, 0x1b, 0x43, 0x82,
	0xfd, 0x8c, 0xf3, 0xee, 0xc3, 0xb2, 0x4d, 0x46, 0x5c, 0x20, 0x1b, 0x73, 0x2f, 0x56, 0xfb, 0x75,
	0x9b, 0x1c, 0x46, 0x20, 0xf4, 0x21, 0x94, 0xe9, 0x3c, 0x73, 0x66, 0xfd, 0xc9, 0xfd, 0x83, 0x54,
	0xd4, 0x1c, 0xa4, 0x69, 0xf6, 0x19, 0x3a, 0xfa, 0x14, 0x6a, 0x0c, 0x63, 0x6c, 0x93, 0xa0, 0x59,
	0xda, 0x2d, 0x15, 0x5b, 0x5b, 0xa5, 0x33, 0x47, 0x36, 0x09, 0xf4, 0x9f, 0xc2, 0xe6, 0xe7, 0x38,
	0x48, 0x0b, 0xdd, 0xc7, 0xaf, 0x25, 0x01, 0x77, 0x1f, 0x96, 0x7d, 0xfc, 0xfa, 0x12, 0x93, 0x60,
	0x14, 0x4c, 0x3d, 0x1e, 0x77, 0x2b, 0xfd, 0x7a, 0x08, 0x3b, 0x9d, 0x7a, 0x58, 0x3f, 0x85, 0x2d,
	0x29, 0x39, 0xe2, 0xa1, 0x8f, 0xa1, 0x6c, 0x3b, 0x67, 0x2e, 0x23, 0x58, 0x7f, 0xf2, 0x76, 0x46,
	0x48, 0x99, 0xe1, 0xfa, 0x6c, 0x89, 0xbe, 0x0d, 0x5b, 0x54, 0x58, 0x89, 0x94, 0xfa, 0xd7, 0xd0,
	0x94, 0x4f, 0x11, 0x2f, 0x69, 0x1b, 0xe5, 0xcd, 0x6d, 0xf3, 0x1c, 0x36, 0x07, 0x72, 0xdb, 0x44,
	0xce, 0x52, 0xde, 0xc8, 0x59, 0x54, 0x8f, 0x81, 0xdc, 0x3a, 0xfa, 0x27, 0xb0, 0xd9, 0xc1, 0xe3,
	0x62, 0x7e, 0xe0, 0x69, 0xab, 0x46, 0x69, 0x4b, 0xc9, 0x4a, 0xd7, 0x12, 0x4f, 0x7f, 0x0c, 0xf5,
	0x0e, 0x9e, 0x57, 0x44, 0xd2, 0xb4, 0x4e, 0x61, 0xeb, 0xa9, 0x11, 0x98, 0x17, 0x1d, 0x3c, 0x66,
	0xc4, 0x38, 0x2d, 0x42, 0x05, 0xf9, 0x18, 0x6a, 0xd4, 0x1b, 0xa2, 0x39, 0x77, 0x32, 0x9a, 0x0b,
	0xdc, 0xfa, 0x55, 0x8a, 0xce, 0x2c, 0xd9, 0x82, 0xa6, 0x9c, 0x2a, 0xf1, 0xf4, 0x8f, 0xe0, 0x36,
	0x9b, 0xcb, 0x09, 0xc3, 0x6d, 0xa8, 0x5e, 0xda, 0xd6, 0x8c, 0xe9, 0x4a, 0x7f, 0xe9, 0xd2, 0xb6,
	0x18, 0xd5, 0x97, 0xb0, 0x93, 0xbf, 0x92, 0x78, 0xe8, 0x69, 0x56, 0xe0, 0x82, 0x61, 0x37, 0x93,
	0xfc, 0x33, 0xb8, 0x1d, 0xba, 0xec, 0x67, 0xd8, 0xf0, 0xc3, 0x74, 0x35, 0x0d, 0x5a, 0xae, 0x8b,
	0x39, 0xe7, 0x2e, 0xec, 0xe4, 0x13, 0x20, 0x9e, 0xfe, 0x25, 0xb4, 0x42, 0xf9, 0xdb, 0xe3, 0xb1,
	0x28, 0x08, 0xf9, 0xc1, 0x49, 0xf8, 0x2b, 0x05, 0x6e, 0xe7, 0xd2, 0x24, 0x5e, 0x91, 0x6a, 0x94,
	0x48, 0x1d, 0xf5, 0xcd, 0x53, 0xe7, 0xdd, 0xb8, 0x0e, 0xd0, 0xc9, 0x76, 0x54, 0xfa, 0xa5, 0x2a,
	0xe9, 0x07, 0xd0, 0x94, 0x23, 0x13, 0x8f, 0x16, 0x60, 0xba, 0x71, 0x84, 0xe8, 0xec, 0x5b, 0x6f,
	0xc3, 0xf6, 0xe1, 0x05, 0x36, 0x5f, 0x15, 0x23, 0x1f, 0x93, 0x50, 0x05, 0x12, 0x0f, 0xa1, 0x95,
	0x47, 0x82, 0x78, 0xd4, 0x87, 0xee, 0xab, 0xd0, 0x2c, 0xaa, 0xfb, 0x4a, 0xff, 0xb7, 0x02, 0x5a,
	0xc7, 0xf6, 0xb1, 0x19, 0xb8, 0xfe, 0x9c, 0x5c, 0x8a, 0x36, 0x0b, 0x55, 0xd8, 0x2c, 0x5a, 0x50,
	0x75, 0x6c, 0xf3, 0x95, 0x63, 0x4c, 0x70, 0xb8, 0x23, 0xc7, 0x63, 0xf4, 0x0e, 0x68, 0xa6, 0xeb,
	0x7a, 0xd8, 0x67, 0xd1, 0xc0, 0xdd, 0x59, 0x66, 0xe4, 0x1a, 0x02, 0x9c, 0xba, 0x54, 0xd8, 0xb2,
	0x2a, 0x89, 0x2d, 0x6b, 0x87, 0x86, 0x78, 0xe0, 0xbb, 0xd6, 0xa5, 0x89, 0xd9, 0x96, 0x5d, 0xeb,
	0xcf, 0x00, 0x74, 0xa7, 0x9a, 0x18, 0x8e, 0x71, 0x8e, 0x7d, 0xb6, 0x6b, 0xd7, 0xfa, 0xd1, 0x90,
	0x6a, 0x18, 0x10, 0xb6, 0x67, 0x97, 0xfb, 0x6a, 0x40, 0x74, 0x8f, 0x6f, 0x5c, 0x19, 0x25, 0xff,
	0x07, 0x1b, 0x57, 0x9a, 0x66, 0x58, 0x0b, 0xf7, 0xe3, 0x8d, 0x47, 0x44, 0x90, 0x07, 0xc8, 0x6c,
	0x57, 0x49, 0xe2, 0x16, 0xdc, 0x55, 0xb2, 0x12, 0xb0, 0x5d, 0x25, 0x55, 0x78, 0xd2, 0x62, 0x14,
	0x2f, 0x3c, 0x19, 0xa1, 0x0a, 0x17, 0x9e, 0x8c, 0x64, 0xb3, 0xc2, 0xf3, 0x07, 0x25, 0xde, 0x7d,
	0x6e, 0x34, 0x90, 0x10, 0x1e, 0x6a, 0x22, 0x3c, 0x64, 0x11, 0x56, 0x92, 0x47, 0x58, 0x22, 0x92,
	0xca, 0x73, 0x22, 0xa9, 0x92, 0x88, 0x24, 0x61, 0x4f, 0x4b, 0x9b, 0x41, 0x1f, 0xc2, 0x66, 0xdb,
	0xb2, 0x8a, 0x69, 0x20, 0x93, 0x54, 0x95, 0x4a, 0x4a, 0x39, 0x4a, 0xc9, 0x12, 0x4f, 0xff, 0x97,
	0x32, 0x3b, 0x29, 0xa4, 0x79, 0x6e, 0xc2, 0xa2, 0x7b, 0x76, 0x46, 0x70, 0x10, 0xb2, 0x0d, 0x47,
	0xe8, 0x36, 0xd4, 0x3c, 0xe3, 0x1c, 0x8f, 0x88, 0xfd, 0x5d, 0xc4, 0xb2, 0x4a, 0x01, 0x03, 0xfb,
	0x3b, 0x1c, 0xa7, 0x74, 0x29, 0x27, 0xa5, 0xcb, 0xa9, 0x94, 0xce, 0xb5, 0x93, 0x54, 0xc1, 0xc5,
	0x9b, 0x92, 0x7d, 0x49, 0xf4, 0xa6, 0x1e, 0xcc, 0xce, 0x3a, 0x99, 0x90, 0xa3, 0xcd, 0x80, 0x1b,
	0x18, 0xe3, 0x50, 0x39, 0x3e, 0x48, 0x06, 0xa2, 0xfa, 0xc3, 0x02, 0xf1, 0xd7, 0x0a, 0x34, 0x78,
	0xb6, 0x4f, 0x7b, 0x01, 0x9e, 0xb0, 0xb2, 0x10, 0x9d, 0x94, 0x95, 0xd9, 0x49, 0x39, 0xee, 0x30,
	0x54, 0xb1, 0xc3, 0x88, 0x7b, 0x94, 0x92, 0xd8, 0xa3, 0x08, 0x27, 0xe8, 0x72, 0xe2, 0x04, 0x4d,
	0x9d, 0x61, 0xfa, 0xd8, 0x08, 0x30, 0xed, 0x34, 0x78, 0xa9, 0xab, 0x72, 0xc0, 0x29, 0xd1, 0x31,
	0xac, 0xb3, 0xb4, 0x6b, 0x5b, 0x16, 0x93, 0x33, 0xc0, 0x13, 0xea, 0xd8, 0x0f, 0xe2, 0x12, 0x40,
	0xf5, 0xdb, 0xcd, 0xe8, 0x97, 0x12, 0x9e, 0x67, 0xbf, 0x28, 0x83, 0x9a, 0x3c, 0xc5, 0x6f, 0xc2,
	0x46, 0x96, 0x0d, 0xf1, 0xf4, 0xef, 0x15, 0x68, 0x50, 0x8b, 0x88, 0xbc, 0xd7, 0xa1, 0x42, 0x0d,
	0x41, 0xc2, 0x0a, 0x51, 0xbe, 0xea, 0x59, 0x24, 0xbf, 0x31, 0xe3, 0x06, 0x2a, 0x89, 0x06, 0x42,
	0x50, 0xa6, 0xc1, 0x16, 0xd6, 0x7d, 0xf6, 0x4d, 0x2b, 0x0f, 0x8b, 0x48, 0xe7, 0x72, 0x12, 0xda,
	0x60, 0x89, 0x8e, 0x8f, 0x2f, 0x27, 0xfa, 0x37, 0xa0, 0x25, 0x45, 0xc8, 0x75, 0xfd, 0x07, 0x50,
	0x16, 0xbc, 0x5e, 0xc0, 0x2a, 0x14, 0x5b, 0x9f, 0xc0, 0xad, 0xcf, 0x7d, 0xc3, 0x09, 0x0a, 0xb4,
	0x9f, 0x51, 0x0c, 0xa8, 0x89, 0x18, 0x90, 0xf4, 0x9e, 0xdb, 0x50, 0x25, 0x17, 0xee, 0xf5, 0xc8,
	0x32, 0xa6, 0x4c, 0xcd, 0x72, 0x7f, 0x89, 0x8e, 0x3b, 0xc6, 0x54, 0xff, 0x93, 0x02, 0xdb, 0xbc,
	0x92, 0xa6, 0x99, 0x52, 0xe3, 0x7e, 0x12, 0xaa, 0xc0, 0x1d, 0xfb, 0x20, 0xa3, 0x82, 0x54, 0x52,
	0xae, 0x48, 0xa2, 0x95, 0x55, 0x93, 0xad, 0x6c, 0xa2, 0x39, 0x2d, 0xa5, 0x9b, 0xd3, 0xdc, 0xd8,
	0xd4, 0x77, 0xa0, 0x95, 0x27, 0x2b, 0xf1, 0xf4, 0xdf, 0xa9, 0xb0, 0x4e, 0x5d, 0x93, 0x56, 0xe2,
	0x0e, 0xc0, 0xeb, 0x4b, 0xec, 0x4f, 0x79, 0xc6, 0x73, 0xfb, 0xd5, 0x18, 0x84, 0xe5, 0xfa, 0x16,
	0x2c, 0xf9, 0xf8, 0x35, 0x0b, 0x21, 0x95, 0x85, 0xd0, 0xa2, 0x8f, 0x5f, 0xd3, 0x20, 0x8a, 0x02,
	0xa3, 0x94, 0x13, 0x18, 0xe5, 0x44, 0x60, 0x24, 0xf4, 0xad, 0xe4, 0xb5, 0xee, 0x8b, 0x62, 0xeb,
	0x9e, 0x53, 0x65, 0xd0, 0x43, 0x40, 0x71, 0x73, 0x4d, 0x43, 0x94, 0x17, 0x8f, 0xea, 0x6e, 0x69,
	0xaf, 0xd6, 0xd7, 0xa2, 0x9e, 0x9a, 0x4e, 0x50, 0x6d, 0xa9, 0x98, 0x57, 0x54, 0xf8, 0x5a, 0x18,
	0xff, 0x36, 0x17, 0xfd, 0x92, 0xc2, 0x80, 0xc3, 0xe8, 0xb7, 0x6e, 0xc2, 0x46, 0xd6, 0x3a, 0xb9,
	0xc1, 0xfb, 0x61, 0x22, 0x78, 0x8b, 0xf4, 0x57, 0x2c, 0x7a, 0x3f, 0x05, 0x14, 0x75, 0x18, 0x82,
	0x07, 0xf8, 0x89, 0x9c, 0x27, 0xa8, 0x9a, 0xec, 0xdf, 0x53, 0x99, 0x7f, 0x0b, 0xd6, 0x33, 0xeb,
	0x89, 0xa7, 0xff, 0x56, 0x81, 0xd5, 0xa1, 0x67, 0x19, 0x01, 0x6e, 0x8f, 0x6d, 0x23, 0xe7, 0x5c,
	0xbe, 0x01, 0x15, 0x83, 0xce, 0x46, 0x49, 0xcf, 0x06, 0x22, 0xaf, 0x52, 0xa6, 0xd2, 0x5d, 0xdb,
	0xc1, 0xc5, 0x88, 0xd8, 0xe7, 0x0e, 0x73, 0x66, 0xb5, 0x5f, 0xa5, 0x80, 0x81, 0x7d, 0xee, 0xa0,
	0x3d, 0xd0, 0xd8, 0x7a, 0x36, 0x3b, 0x1a, 0xe3, 0x2b, 0x3c, 0x0e, 0xf7, 0x93, 0x55, 0x06, 0xa7,
	0x48, 0x47, 0x14, 0xaa, 0xaf, 0x41, 0x23, 0x21, 0x1a, 0xf1, 0xf4, 0x5f, 0x02, 0x3a, 0xbc, 0x30,
	0x9c, 0x73, 0xdc, 0xb1, 0x89, 0x37, 0x36, 0xa6, 0xbd, 0x4e, 0x18, 0x87, 0xe6, 0x85, 0xe1, 0x38,
	0x78, 0x3c, 0x2b, 0xdc, 0xb5, 0x10, 0xd2, 0xb3, 0xd0, 0x23, 0x58, 0x77, 0xf0, 0xf5, 0x28, 0x42,
	0xb9, 0xb2, 0xf1, 0xf5, 0x28, 0x3e, 0xca, 0x6a, 0x0e, 0xbe, 0x3e, 0xe4, 0x33, 0x2f, 0x6c, 0x7c,
	0xdd, 0xb3, 0xf2, 0xf5, 0xa2, 0x36, 0xcc, 0x70, 0x27, 0x9e, 0xfe, 0x0f, 0x15, 0x96, 0x69, 0x00,
	0xc4, 0x16, 0xbc, 0x21, 0x2f, 0xe4, 0xe6, 0x94, 0x6d, 0xc7, 0x0f, 0xa0, 0x91, 0x96, 0x9a, 0x27,
	0xee, 0x8a, 0x99, 0x10, 0x39, 0x47, 0xc3, 0x4a, 0x8e, 0x86, 0x77, 0x00, 0xc2, 0x84, 0xb2, 0x27,
	0xd1, 0x4e, 0x5d, 0xe3, 0x29, 0x65, 0x4f, 0x58, 0x2a, 0xb2, 0xa4, 0xa2, 0x93, 0x3c, 0x7f, 0x96,
	0x68, 0x5a, 0xd1, 0xa9, 0x28, 0x73, 0xab, 0x39, 0x99, 0x5b, 0x4b, 0x66, 0x6e, 0x18, 0x4a, 0x30,
	0x0b, 0x25, 0x99, 0xf7, 0xeb, 0x52, 0xef, 0x0f, 0x61, 0x45, 0x30, 0x6a, 0x6e, 0x3a, 0x1d, 0x24,
	0xd2, 0xa9, 0x95, 0x49, 0x27, 0xb6, 0x5e, 0xc8, 0xa3, 0xbf, 0x55, 0xa0, 0x16, 0xc3, 0x0a, 0x36,
	0x3a, 0xb1, 0xc3, 0x4a, 0xa2, 0xc3, 0xe6, 0x9d, 0x95, 0x92, 0x11, 0x59, 0x49, 0x47, 0xa4, 0xc4,
	0xaf, 0x8b, 0x6f, 0xe0, 0xd7, 0xa5, 0x1c, 0xbf, 0xde, 0x87, 0xe5, 0x08, 0x95, 0x49, 0x55, 0x65,
	0x78, 0xf5, 0x10, 0x76, 0x4c, 0x05, 0x13, 0x50, 0x58, 0x70, 0x72, 0x87, 0x45, 0x28, 0x2c, 0x3c,
	0x85, 0xf8, 0x87, 0x64, 0x5e, 0xdf, 0x87, 0xe5, 0xf0, 0x93, 0x07, 0x47, 0x9d, 0x2f, 0x0e, 0x61,
	0x2c, 0x40, 0x7a, 0x02, 0x0a, 0xa5, 0xbf, 0xbc, 0xab, 0xec, 0xad, 0x4a, 0xf6, 0xb7, 0xd8, 0x05,
	0x07, 0x5f, 0x84, 0xab, 0xa7, 0x1e, 0x9e, 0x91, 0xa2, 0x72, 0x6c, 0x43, 0xd5, 0x76, 0xec, 0x60,
	0xe4, 0x5d, 0xf3, 0x6b, 0xcb, 0x5a, 0x7f, 0x89, 0x8e, 0x4f, 0xae, 0x2d, 0xb4, 0x0f, 0x6b, 0x42,
	0x14, 0x85, 0xa5, 0x7e, 0x95, 0x9f, 0x38, 0xe3, 0x30, 0x1a, 0xf0, 0x9a, 0xbf, 0x0f, 0x6b, 0xa2,
	0xd0, 0xbc, 0x28, 0x35, 0x38, 0xae, 0x20, 0x39, 0xab, 0x4d, 0x82, 0x82, 0x0c, 0x4d, 0xe3, 0x06,
	0x0c, 0x61, 0xb9, 0xe5, 0x6b, 0x4d, 0x1a, 0xc0, 0x3f, 0x87, 0xba, 0xa0, 0x1b, 0xda, 0x81, 0xe6,
	0x17, 0xed, 0xe3, 0xce, 0x51, 0xb7, 0x3f, 0x3a, 0xfd, 0xea, 0xa4, 0x3b, 0x1a, 0x1e, 0x0f, 0x4e,
	0xba, 0x87, 0xbd, 0x67, 0xbd, 0x6e, 0x47, 0x5b, 0x40, 0x5b, 0xb0, 0x9e, 0x9c, 0x3d, 0xe9, 0xb4,
	0x4f, 0xbb, 0x9a, 0x92, 0x99, 0x38, 0xec, 0x77, 0xe9, 0x84, 0xaa, 0xff, 0x46, 0x81, 0x8d, 0x43,
	0x76, 0x7c, 0xa4, 0x87, 0xdc, 0xa7, 0xd3, 0xb8, 0xfa, 0xc4, 0xd1, 0xaa, 0xe4, 0x54, 0x6b, 0x75,
	0x4e, 0xb5, 0x2e, 0x15, 0xa8, 0xd6, 0x65, 0xa9, 0xba, 0x5b, 0x70, 0x4b, 0x22, 0x0e, 0xf1, 0xf4,
	0x3f, 0x2a, 0xb3, 0x43, 0x67, 0x3b, 0x5a, 0x43, 0x05, 0x7d, 0x00, 0x0d, 0x91, 0xf6, 0xac, 0x19,
	0x5d, 0x99, 0x91, 0xa6, 0xfb, 0x70, 0xbe, 0xe8, 0xcf, 0x61, 0x43, 0xa0, 0x30, 0x6b, 0x07, 0xf8,
	0x65, 0xf1, 0x5d, 0x79, 0xd4, 0x51, 0xba, 0xac, 0x20, 0xac, 0x19, 0xe2, 0x90, 0x35, 0x04, 0xbf,
	0x57, 0x60, 0x25, 0x81, 0x24, 0xa9, 0x10, 0x1f, 0x25, 0x1a, 0xd2, 0x55, 0xc9, 0xf9, 0xb3, 0x9d,
	0x0c, 0xc1, 0xf8, 0xf8, 0xb1, 0x01, 0x15, 0x6e, 0xc1, 0xb0, 0x8e, 0xb0, 0xc1, 0x2c, 0x98, 0xd9,
	0xdb, 0x89, 0xc5, 0xd4, 0x60, 0x36, 0x5e, 0x0e, 0x83, 0xb9, 0xcb, 0xe0, 0x3d, 0xde, 0xd7, 0xdf,
	0x92, 0x98, 0x92, 0x78, 0xe8, 0x1e, 0xd4, 0xb1, 0xef, 0x8f, 0x4c, 0xd7, 0x09, 0xb0, 0x13, 0x84,
	0xae, 0x07, 0xec, 0xfb, 0x87, 0x1c, 0xa2, 0x3f, 0x0d, 0x9d, 0xd0, 0xc1, 0xe3, 0x84, 0x13, 0x62,
	0xfd, 0x4a, 0x91, 0x7e, 0xf9, 0x67, 0x88, 0x88, 0x7b, 0x92, 0x46, 0x11, 0xee, 0x7f, 0x51, 0x60,
	0x93, 0x2d, 0x65, 0x37, 0x53, 0x37, 0x08, 0x70, 0x42, 0x8b, 0x27, 0x36, 0x5f, 0xcd, 0x3a, 0xe5,
	0xd5, 0x27, 0xef, 0x65, 0x8c, 0x2c, 0x27, 0x77, 0xc0, 0x20, 0xac, 0x98, 0xd4, 0xcc, 0xe8, 0x53,
	0x7f, 0x1f, 0x6a, 0x31, 0x1c, 0x69, 0xb0, 0x1c, 0x0f, 0xda, 0x96, 0xa5, 0x2d, 0xa0, 0x0d, 0xd0,
	0x66, 0xcb, 0xb0, 0x39, 0x35, 0xc7, 0x58, 0x53, 0xf4, 0x4f, 0xc2, 0xeb, 0xe2, 0x34, 0x8f, 0x22,
	0xfa, 0xee, 0x85, 0xd6, 0xfe, 0x1c, 0x07, 0xf3, 0x95, 0xd5, 0x47, 0xa1, 0x4d, 0x93, 0x98, 0xc4,
	0x43, 0xcf, 0xe4, 0xd9, 0x71, 0x73, 0x58, 0x27, 0xb3, 0x67, 0xff, 0x29, 0xac, 0x74, 0xa3, 0x6b,
	0x40, 0xa6, 0xff, 0x2d, 0x58, 0x4b, 0x00, 0x8e, 0x5d, 0x07, 0x6b, 0x0b, 0xa8, 0x05, 0x9b, 0x09,
	0x70, 0x6f, 0x72, 0xec, 0x1e, 0xd9, 0x13, 0x3b, 0xd0, 0x94, 0xfd, 0x21, 0x2c, 0x1f, 0xb9, 0xe6,
	0xac, 0x8b, 0x5f, 0x83, 0x95, 0x13, 0xec, 0x13, 0xd7, 0x31, 0xc6, 0xa3, 0x13, 0xe3, 0x9c, 0x2e,
	0xaf, 0xc3, 0x52, 0xb8, 0x19, 0x69, 0x0a, 0x6a, 0x40, 0xbd, 0xed, 0x98, 0x17, 0xae, 0x3f, 0x3a,
	0x34, 0x7c, 0x4b, 0x53, 0xa9, 0x85, 0xa3, 0x05, 0x76, 0x30, 0xed, 0xf8, 0x98, 0x10, 0xad, 0xb4,
	0xff, 0xbd, 0x02, 0xf5, 0xc1, 0x69, 0xfb, 0x74, 0x38, 0x60, 0xb5, 0x8d, 0x4a, 0x26, 0x0c, 0x47,
	0xc3, 0xe3, 0xe1, 0xa0, 0xab, 0x2d, 0xa0, 0x75, 0x68, 0x24, 0xc0, 0x03, 0x5a, 0x17, 0x9b, 0xb0,
	0x91, 0xc4, 0xed, 0x3e, 0x7b, 0xd6, 0x3d, 0x3c, 0xd5, 0xd4, 0x34, 0x7a, 0xa7, 0x7b, 0xa4, 0x95,
	0x68, 0x19, 0x15, 0x81, 0xcf, 0x5f, 0x74, 0xfb, 0x9d, 0x61, 0x57, 0x2b, 0xef, 0x7f, 0x00, 0xf0,
	0xe5, 0xb0, 0xdb, 0xff, 0x8a, 0x4b, 0x80, 0x60, 0x75, 0x36, 0x1a, 0xbd, 0xe8, 0xd1, 0xd2, 0x9c,
	0x84, 0x0d, 0x7b, 0x1d, 0x4d, 0xd9, 0x3f, 0x02, 0x8d, 0xc3, 0xda, 0x47, 0xbd, 0x76, 0x28, 0x7d,
	0x13, 0x36, 0xd2, 0x30, 0x86, 0xbd, 0x80, 0xee, 0x42, 0x2b, 0x33, 0x73, 0xf8, 0x45, 0xfb, 0xf8,
	0xb8, 0x7b, 0xc4, 0xa8, 0x59, 0xd0, 0x48, 0x95, 0x0c, 0x2a, 0x6f, 0x0a, 0x14, 0xba, 0xe9, 0x36,
	0x6c, 0xa5, 0x26, 0xda, 0x66, 0x60, 0x5f, 0x19, 0x01, 0xd6, 0x14, 0xc9, 0x64, 0x18, 0xce, 0x96,
	0xa6, 0x3e, 0xf9, 0xe7, 0x16, 0x2c, 0x8b, 0xe7, 0x7f, 0xf4, 0x2d, 0xac, 0x4b, 0x9e, 0x17, 0xd0,
	0x8f, 0xb3, 0xcd, 0xa8, 0xf4, 0xf9, 0xa2, 0xb5, 0x57, 0x0c, 0x91, 0x78, 0xfa, 0x02, 0x9a, 0xf0,
	0x1e, 0x29, 0xc3, 0x2c, 0x4b, 0x23, 0xe7, 0x35, 0xac, 0xf5, 0x4e, 0x41, 0x4c, 0xc6, 0xce, 0x85,
	0x4d, 0xd9, 0xec, 0x8b, 0x27, 0xff, 0x2f, 0x86, 0xdf, 0xc2, 0xfa, 0xa0, 0x90, 0x2d, 0x07, 0x45,
	0x6d, 0x39, 0xc0, 0x73, 0x78, 0x49, 0xde, 0xc4, 0x24, 0xbc, 0xe4, 0xaf, 0x6e, 0x12, 0x5e, 0x79,
	0x4f, 0x6c, 0x0b, 0x68, 0x1a, 0xbe, 0x6e, 0xc9, 0x02, 0xe5, 0xa1, 0xbc, 0x26, 0xe7, 0x44, 0xcb,
	0xa3, 0x37, 0xc0, 0x8e, 0x58, 0xe7, 0xbd, 0x2e, 0x49, 0x58, 0xcf, 0x79, 0xc9, 0x92, 0xb0, 0x9e,
	0xfb, 0x6c, 0xb5, 0x80, 0xae, 0xe2, 0x4b, 0xf9, 0xf4, 0x23, 0x13, 0x7a, 0x37, 0x2f, 0xe8, 0x25,
	0x4f, 0x5c, 0xad, 0x87, 0xc5, 0x91, 0xa3, 0x2c, 0x91, 0xbd, 0x16, 0xa1, 0xdc, 0x4c, 0x4b, 0x3f,
	0x11, 0x49, 0x82, 0x36, 0xef, 0xf9, 0x49, 0x5f, 0x40, 0x04, 0x36, 0xe5, 0x2f, 0x45, 0x68, 0x3f,
	0x7b, 0xa7, 0x96, 0xf7, 0x2a, 0xd5, 0x7a, 0xb7, 0x30, 0x6e, 0xa4, 0xa3, 0xec, 0xbd, 0x54, 0xa2,
	0x63, 0xce, 0x63, 0xad, 0x44, 0xc7, 0xdc, 0x07, 0xd8, 0x05, 0xa1, 0xc8, 0x89, 0xf7, 0xc0, 0xf9,
	0x45, 0x2e, 0x75, 0xb5, 0x9e, 0x5f, 0xe4, 0x32, 0x17, 0xf4, 0x99, 0x64, 0x49, 0x30, 0x9c, 0x9f,
	0x2c, 0x69, 0xae, 0x8f, 0xde, 0x00, 0x3b, 0x52, 0x53, 0xf2, 0x70, 0x20, 0x51, 0x53, 0xfe, 0x6a,
	0x21, 0x51, 0x33, 0xef, 0x1d, 0x42, 0xac, 0x75, 0x37, 0xf0, 0x1a, 0x14, 0x35, 0xe9, 0x20, 0x57,
	0x2f, 0x61, 0xdf, 0x48, 0x30, 0xcb, 0x2f, 0xe3, 0x69, 0x6e, 0xef, 0x14, 0xc4, 0x64, 0xec, 0x0c,
	0xd0, 0xd2, 0xf7, 0xe3, 0xe8, 0x2d, 0xb9, 0x2f, 0x92, 0x37, 0xf5, 0xad, 0xb7, 0x0b, 0x60, 0x31,
	0x16, 0x43, 0x7e, 0x59, 0x14, 0x93, 0xdf, 0x95, 0xca, 0x27, 0x92, 0xbe, 0x7f, 0x03, 0x46, 0x94,
	0xcb, 0xf2, 0x1b, 0x5c, 0x49, 0x2e, 0xe7, 0x5e, 0x4b, 0x4b, 0x72, 0x79, 0xce, 0xb5, 0xf0, 0x02,
	0xfa, 0x06, 0x1a, 0xa9, 0x4b, 0x45, 0xf4, 0x23, 0xd9, 0xe6, 0x92, 0x66, 0xf3, 0xd6, 0xcd, 0x48,
	0x91, 0x3b, 0xd2, 0x37, 0xab, 0x12, 0x77, 0x48, 0xae, 0xa6, 0x25, 0xee, 0x90, 0x5d, 0xd1, 0xea,
	0x0b, 0xa8, 0x0f, 0x75, 0xe1, 0x92, 0x11, 0xdd, 0xcb, 0x3e, 0x21, 0x25, 0x6e, 0x47, 0x5b, 0xbb,
	0xf3, 0x11, 0x18, 0xcd, 0x23, 0xa8, 0xc5, 0x57, 0x57, 0xe8, 0x8e, 0x54, 0x92, 0x98, 0xde, 0xdd,
	0x79, 0xd3, 0x8c, 0x9a, 0x05, 0x6b, 0x99, 0xc6, 0x1a, 0x65, 0xf5, 0x93, 0xdd, 0x05, 0xb4, 0x1e,
	0x14, 0x41, 0x8b, 0xb8, 0x64, 0x3a, 0x4b, 0x94, 0x1f, 0xd4, 0x62, 0x57, 0x23, 0xe1, 0x22, 0x6d,
	0x52, 0x05, 0x2e, 0x62, 0x07, 0x99, 0xc7, 0x25, 0xd5, 0xa9, 0xe6, 0x71, 0x49, 0x37, 0xa3, 0xbc,
	0x40, 0x49, 0x3a, 0x37, 0x49, 0x81, 0x92, 0xf7, 0x90, 0xad, 0xbd, 0x62, 0x88, 0x09, 0x8d, 0xc4,
	0xfe, 0x2d, 0x4f, 0xa3, 0x54, 0x37, 0x98, 0xa7, 0x51, 0xba, 0x15, 0xd4, 0x17, 0x5a, 0x77, 0xfe,
	0xfe, 0x9f, 0x7b, 0xdb, 0x22, 0x72, 0xe2, 0x99, 0xe0, 0xe9, 0xfb, 0x5f, 0xbf, 0x77, 0xee, 0x8e,
	0x0d, 0xe7, 0xfc, 0xe0, 0xc3, 0x27, 0x41, 0x70, 0x60, 0xba, 0x93, 0xc7, 0xec, 0xef, 0x96, 0xa6,
	0x3b, 0x7e, 0x4c, 0xb0, 0x7f, 0x65, 0x9b, 0x98, 0xa4, 0xff, 0xc2, 0xf9, 0x72, 0x91, 0xa1, 0xbc,
	0xff, 0xdf, 0x00, 0x00, 0x00, 0xff, 0xff, 0x0c, 0xc9, 0x42, 0x9f, 0xec, 0x29, 0x00, 0x00,
}
