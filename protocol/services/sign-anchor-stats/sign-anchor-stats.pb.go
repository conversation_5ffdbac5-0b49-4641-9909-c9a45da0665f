// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/sign-anchor-stats/sign-anchor-stats.proto

package sign_anchor_stats // import "golang.52tt.com/protocol/services/sign-anchor-stats"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import channellivefans "golang.52tt.com/protocol/services/channellivefans"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type TimeFilterUnit int32

const (
	TimeFilterUnit_BY_DAY   TimeFilterUnit = 0
	TimeFilterUnit_BY_WEEK  TimeFilterUnit = 1
	TimeFilterUnit_BY_MONTH TimeFilterUnit = 2
)

var TimeFilterUnit_name = map[int32]string{
	0: "BY_DAY",
	1: "BY_WEEK",
	2: "BY_MONTH",
}
var TimeFilterUnit_value = map[string]int32{
	"BY_DAY":   0,
	"BY_WEEK":  1,
	"BY_MONTH": 2,
}

func (x TimeFilterUnit) String() string {
	return proto.EnumName(TimeFilterUnit_name, int32(x))
}
func (TimeFilterUnit) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{0}
}

// 能力维度
type AbilityType int32

const (
	AbilityType_Ability_Invalid   AbilityType = 0
	AbilityType_Ability_Quality   AbilityType = 1
	AbilityType_Ability_Potential AbilityType = 2
)

var AbilityType_name = map[int32]string{
	0: "Ability_Invalid",
	1: "Ability_Quality",
	2: "Ability_Potential",
}
var AbilityType_value = map[string]int32{
	"Ability_Invalid":   0,
	"Ability_Quality":   1,
	"Ability_Potential": 2,
}

func (x AbilityType) String() string {
	return proto.EnumName(AbilityType_name, int32(x))
}
func (AbilityType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{1}
}

type TYPE_MULTIPLAYER_INFO int32

const (
	TYPE_MULTIPLAYER_INFO_TYPE_MULTIPLAYER_INFO_INVAILD TYPE_MULTIPLAYER_INFO = 0
	TYPE_MULTIPLAYER_INFO_TYPE_MULTIPLAYER_INFO_TODAY   TYPE_MULTIPLAYER_INFO = 1
	TYPE_MULTIPLAYER_INFO_TYPE_MULTIPLAYER_INFO_WEEK    TYPE_MULTIPLAYER_INFO = 2
	TYPE_MULTIPLAYER_INFO_TYPE_MULTIPLAYER_INFO_MONTH   TYPE_MULTIPLAYER_INFO = 3
)

var TYPE_MULTIPLAYER_INFO_name = map[int32]string{
	0: "TYPE_MULTIPLAYER_INFO_INVAILD",
	1: "TYPE_MULTIPLAYER_INFO_TODAY",
	2: "TYPE_MULTIPLAYER_INFO_WEEK",
	3: "TYPE_MULTIPLAYER_INFO_MONTH",
}
var TYPE_MULTIPLAYER_INFO_value = map[string]int32{
	"TYPE_MULTIPLAYER_INFO_INVAILD": 0,
	"TYPE_MULTIPLAYER_INFO_TODAY":   1,
	"TYPE_MULTIPLAYER_INFO_WEEK":    2,
	"TYPE_MULTIPLAYER_INFO_MONTH":   3,
}

func (x TYPE_MULTIPLAYER_INFO) String() string {
	return proto.EnumName(TYPE_MULTIPLAYER_INFO_name, int32(x))
}
func (TYPE_MULTIPLAYER_INFO) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{2}
}

// 场景类型
type SceneType int32

const (
	SceneType_SCENE_TYPE_INVALID_UNSPECIFIED SceneType = 0
	SceneType_SCENE_TYPE_USERCARD_LIVE       SceneType = 1
	SceneType_SCENE_TYPE_USERCARD_PGC        SceneType = 2
	SceneType_SCENE_TYPE_IM_PAGE             SceneType = 3
)

var SceneType_name = map[int32]string{
	0: "SCENE_TYPE_INVALID_UNSPECIFIED",
	1: "SCENE_TYPE_USERCARD_LIVE",
	2: "SCENE_TYPE_USERCARD_PGC",
	3: "SCENE_TYPE_IM_PAGE",
}
var SceneType_value = map[string]int32{
	"SCENE_TYPE_INVALID_UNSPECIFIED": 0,
	"SCENE_TYPE_USERCARD_LIVE":       1,
	"SCENE_TYPE_USERCARD_PGC":        2,
	"SCENE_TYPE_IM_PAGE":             3,
}

func (x SceneType) String() string {
	return proto.EnumName(SceneType_name, int32(x))
}
func (SceneType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{3}
}

type InteractUserType int32

const (
	InteractUserType_INTERACT_USER_TYPE_INVALID_UNSPECIFIED InteractUserType = 0
	InteractUserType_INTERACT_USER_TYPE_COMMON              InteractUserType = 1
	InteractUserType_INTERACT_USER_TYPE_EXPIRE_FANS         InteractUserType = 2
	InteractUserType_INTERACT_USER_TYPE_LIVE_SEVEN_NEW      InteractUserType = 3
	InteractUserType_INTERACT_USER_TYPE_PGC_SEVEN_NEW       InteractUserType = 4
)

var InteractUserType_name = map[int32]string{
	0: "INTERACT_USER_TYPE_INVALID_UNSPECIFIED",
	1: "INTERACT_USER_TYPE_COMMON",
	2: "INTERACT_USER_TYPE_EXPIRE_FANS",
	3: "INTERACT_USER_TYPE_LIVE_SEVEN_NEW",
	4: "INTERACT_USER_TYPE_PGC_SEVEN_NEW",
}
var InteractUserType_value = map[string]int32{
	"INTERACT_USER_TYPE_INVALID_UNSPECIFIED": 0,
	"INTERACT_USER_TYPE_COMMON":              1,
	"INTERACT_USER_TYPE_EXPIRE_FANS":         2,
	"INTERACT_USER_TYPE_LIVE_SEVEN_NEW":      3,
	"INTERACT_USER_TYPE_PGC_SEVEN_NEW":       4,
}

func (x InteractUserType) String() string {
	return proto.EnumName(InteractUserType_name, int32(x))
}
func (InteractUserType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{4}
}

// 日任务类型
type HALL_TASK_DAY_TYPE int32

const (
	HALL_TASK_DAY_TYPE_HALL_TASK_DAY_TYPE_INVALID            HALL_TASK_DAY_TYPE = 0
	HALL_TASK_DAY_TYPE_HALL_TASK_DAY_TYPE_HOLD_TIME          HALL_TASK_DAY_TYPE = 1
	HALL_TASK_DAY_TYPE_HALL_TASK_DAY_TYPE_VALID_OPEN_CNT     HALL_TASK_DAY_TYPE = 2
	HALL_TASK_DAY_TYPE_HALL_TASK_DAY_TYPE_GOD_FLY_TICKET_CNT HALL_TASK_DAY_TYPE = 3
	HALL_TASK_DAY_TYPE_HALL_TASK_DAY_TYPE_TBEAN_INCOME       HALL_TASK_DAY_TYPE = 4
)

var HALL_TASK_DAY_TYPE_name = map[int32]string{
	0: "HALL_TASK_DAY_TYPE_INVALID",
	1: "HALL_TASK_DAY_TYPE_HOLD_TIME",
	2: "HALL_TASK_DAY_TYPE_VALID_OPEN_CNT",
	3: "HALL_TASK_DAY_TYPE_GOD_FLY_TICKET_CNT",
	4: "HALL_TASK_DAY_TYPE_TBEAN_INCOME",
}
var HALL_TASK_DAY_TYPE_value = map[string]int32{
	"HALL_TASK_DAY_TYPE_INVALID":            0,
	"HALL_TASK_DAY_TYPE_HOLD_TIME":          1,
	"HALL_TASK_DAY_TYPE_VALID_OPEN_CNT":     2,
	"HALL_TASK_DAY_TYPE_GOD_FLY_TICKET_CNT": 3,
	"HALL_TASK_DAY_TYPE_TBEAN_INCOME":       4,
}

func (x HALL_TASK_DAY_TYPE) String() string {
	return proto.EnumName(HALL_TASK_DAY_TYPE_name, int32(x))
}
func (HALL_TASK_DAY_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{5}
}

// 周任务类型
type HALL_TASK_WEEK_TYPE int32

const (
	HALL_TASK_WEEK_TYPE_HALL_TASK_WEEK_TYPE_INVALID             HALL_TASK_WEEK_TYPE = 0
	HALL_TASK_WEEK_TYPE_HALL_TASK_WEEK_TYPE_VALID_HOLD_DAY_CNT  HALL_TASK_WEEK_TYPE = 1
	HALL_TASK_WEEK_TYPE_HALL_TASK_WEEK_TYPE_FINISH_DAY_TASK_CNT HALL_TASK_WEEK_TYPE = 2
	HALL_TASK_WEEK_TYPE_HALL_TASK_WEEK_TYPE_VALID_OPEN_CNT      HALL_TASK_WEEK_TYPE = 3
	HALL_TASK_WEEK_TYPE_HALL_TASK_WEEK_TYPE_GOD_FLY_TICKET_CNT  HALL_TASK_WEEK_TYPE = 4
	HALL_TASK_WEEK_TYPE_HALL_TASK_WEEK_TYPE_TBEAN_INCOME        HALL_TASK_WEEK_TYPE = 5
)

var HALL_TASK_WEEK_TYPE_name = map[int32]string{
	0: "HALL_TASK_WEEK_TYPE_INVALID",
	1: "HALL_TASK_WEEK_TYPE_VALID_HOLD_DAY_CNT",
	2: "HALL_TASK_WEEK_TYPE_FINISH_DAY_TASK_CNT",
	3: "HALL_TASK_WEEK_TYPE_VALID_OPEN_CNT",
	4: "HALL_TASK_WEEK_TYPE_GOD_FLY_TICKET_CNT",
	5: "HALL_TASK_WEEK_TYPE_TBEAN_INCOME",
}
var HALL_TASK_WEEK_TYPE_value = map[string]int32{
	"HALL_TASK_WEEK_TYPE_INVALID":             0,
	"HALL_TASK_WEEK_TYPE_VALID_HOLD_DAY_CNT":  1,
	"HALL_TASK_WEEK_TYPE_FINISH_DAY_TASK_CNT": 2,
	"HALL_TASK_WEEK_TYPE_VALID_OPEN_CNT":      3,
	"HALL_TASK_WEEK_TYPE_GOD_FLY_TICKET_CNT":  4,
	"HALL_TASK_WEEK_TYPE_TBEAN_INCOME":        5,
}

func (x HALL_TASK_WEEK_TYPE) String() string {
	return proto.EnumName(HALL_TASK_WEEK_TYPE_name, int32(x))
}
func (HALL_TASK_WEEK_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{6}
}

// 承接大厅分配任务变更类型
type HALL_TASK_CHANGE_TYPE int32

const (
	HALL_TASK_CHANGE_TYPE_HALL_TASK_CHANGE_TYPE_INVALID                HALL_TASK_CHANGE_TYPE = 0
	HALL_TASK_CHANGE_TYPE_HALL_TASK_CHANGE_TYPE_ADD                    HALL_TASK_CHANGE_TYPE = 1
	HALL_TASK_CHANGE_TYPE_HALL_TASK_CHANGE_TYPE_UPDATE                 HALL_TASK_CHANGE_TYPE = 2
	HALL_TASK_CHANGE_TYPE_HALL_TASK_CHANGE_TYPE_CANCEL_CONTRACT_DELETE HALL_TASK_CHANGE_TYPE = 3
	HALL_TASK_CHANGE_TYPE_HALL_TASK_CHANGE_TYPE_GUILD_DELETE           HALL_TASK_CHANGE_TYPE = 4
	HALL_TASK_CHANGE_TYPE_HALL_TASK_CHANGE_TYPE_HALL_CHANNEL_DELETE    HALL_TASK_CHANGE_TYPE = 5
)

var HALL_TASK_CHANGE_TYPE_name = map[int32]string{
	0: "HALL_TASK_CHANGE_TYPE_INVALID",
	1: "HALL_TASK_CHANGE_TYPE_ADD",
	2: "HALL_TASK_CHANGE_TYPE_UPDATE",
	3: "HALL_TASK_CHANGE_TYPE_CANCEL_CONTRACT_DELETE",
	4: "HALL_TASK_CHANGE_TYPE_GUILD_DELETE",
	5: "HALL_TASK_CHANGE_TYPE_HALL_CHANNEL_DELETE",
}
var HALL_TASK_CHANGE_TYPE_value = map[string]int32{
	"HALL_TASK_CHANGE_TYPE_INVALID":                0,
	"HALL_TASK_CHANGE_TYPE_ADD":                    1,
	"HALL_TASK_CHANGE_TYPE_UPDATE":                 2,
	"HALL_TASK_CHANGE_TYPE_CANCEL_CONTRACT_DELETE": 3,
	"HALL_TASK_CHANGE_TYPE_GUILD_DELETE":           4,
	"HALL_TASK_CHANGE_TYPE_HALL_CHANNEL_DELETE":    5,
}

func (x HALL_TASK_CHANGE_TYPE) String() string {
	return proto.EnumName(HALL_TASK_CHANGE_TYPE_name, int32(x))
}
func (HALL_TASK_CHANGE_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{7}
}

// 承接大厅送礼流水记录 礼物来源
type HALL_TASK_PRESENT_SOURCE_TYPE int32

const (
	HALL_TASK_PRESENT_SOURCE_TYPE_HALL_TASK_PRESENT_SOURCE_TYPE_INVALID HALL_TASK_PRESENT_SOURCE_TYPE = 0
	HALL_TASK_PRESENT_SOURCE_TYPE_HALL_TASK_PRESENT_SOURCE_TYPE_TICKET  HALL_TASK_PRESENT_SOURCE_TYPE = 1
	HALL_TASK_PRESENT_SOURCE_TYPE_HALL_TASK_PRESENT_SOURCE_TYPE_TBEAN   HALL_TASK_PRESENT_SOURCE_TYPE = 2
)

var HALL_TASK_PRESENT_SOURCE_TYPE_name = map[int32]string{
	0: "HALL_TASK_PRESENT_SOURCE_TYPE_INVALID",
	1: "HALL_TASK_PRESENT_SOURCE_TYPE_TICKET",
	2: "HALL_TASK_PRESENT_SOURCE_TYPE_TBEAN",
}
var HALL_TASK_PRESENT_SOURCE_TYPE_value = map[string]int32{
	"HALL_TASK_PRESENT_SOURCE_TYPE_INVALID": 0,
	"HALL_TASK_PRESENT_SOURCE_TYPE_TICKET":  1,
	"HALL_TASK_PRESENT_SOURCE_TYPE_TBEAN":   2,
}

func (x HALL_TASK_PRESENT_SOURCE_TYPE) String() string {
	return proto.EnumName(HALL_TASK_PRESENT_SOURCE_TYPE_name, int32(x))
}
func (HALL_TASK_PRESENT_SOURCE_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{8}
}

type HallStatisticsType int32

const (
	HallStatisticsType_HallStatisticsDay  HallStatisticsType = 0
	HallStatisticsType_HallStatisticsWeek HallStatisticsType = 1
)

var HallStatisticsType_name = map[int32]string{
	0: "HallStatisticsDay",
	1: "HallStatisticsWeek",
}
var HallStatisticsType_value = map[string]int32{
	"HallStatisticsDay":  0,
	"HallStatisticsWeek": 1,
}

func (x HallStatisticsType) String() string {
	return proto.EnumName(HallStatisticsType_name, int32(x))
}
func (HallStatisticsType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{9}
}

type GetMultiThisMonthChannelStatReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	ChannelIds           []uint32 `protobuf:"varint,4,rep,packed,name=channel_ids,json=channelIds,proto3" json:"channel_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMultiThisMonthChannelStatReq) Reset()         { *m = GetMultiThisMonthChannelStatReq{} }
func (m *GetMultiThisMonthChannelStatReq) String() string { return proto.CompactTextString(m) }
func (*GetMultiThisMonthChannelStatReq) ProtoMessage()    {}
func (*GetMultiThisMonthChannelStatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{0}
}
func (m *GetMultiThisMonthChannelStatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMultiThisMonthChannelStatReq.Unmarshal(m, b)
}
func (m *GetMultiThisMonthChannelStatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMultiThisMonthChannelStatReq.Marshal(b, m, deterministic)
}
func (dst *GetMultiThisMonthChannelStatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMultiThisMonthChannelStatReq.Merge(dst, src)
}
func (m *GetMultiThisMonthChannelStatReq) XXX_Size() int {
	return xxx_messageInfo_GetMultiThisMonthChannelStatReq.Size(m)
}
func (m *GetMultiThisMonthChannelStatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMultiThisMonthChannelStatReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMultiThisMonthChannelStatReq proto.InternalMessageInfo

func (m *GetMultiThisMonthChannelStatReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetMultiThisMonthChannelStatReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetMultiThisMonthChannelStatReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetMultiThisMonthChannelStatReq) GetChannelIds() []uint32 {
	if m != nil {
		return m.ChannelIds
	}
	return nil
}

type MultiThisMonthChannelStatItem struct {
	ChannelId              uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ThisMonthFee           uint64   `protobuf:"varint,2,opt,name=this_month_fee,json=thisMonthFee,proto3" json:"this_month_fee,omitempty"`
	LastMonthFee           uint64   `protobuf:"varint,3,opt,name=last_month_fee,json=lastMonthFee,proto3" json:"last_month_fee,omitempty"`
	LastMonthSamePeriodFee uint64   `protobuf:"varint,4,opt,name=last_month_same_period_fee,json=lastMonthSamePeriodFee,proto3" json:"last_month_same_period_fee,omitempty"`
	ChainRatio             float32  `protobuf:"fixed32,5,opt,name=chain_ratio,json=chainRatio,proto3" json:"chain_ratio,omitempty"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *MultiThisMonthChannelStatItem) Reset()         { *m = MultiThisMonthChannelStatItem{} }
func (m *MultiThisMonthChannelStatItem) String() string { return proto.CompactTextString(m) }
func (*MultiThisMonthChannelStatItem) ProtoMessage()    {}
func (*MultiThisMonthChannelStatItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{1}
}
func (m *MultiThisMonthChannelStatItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MultiThisMonthChannelStatItem.Unmarshal(m, b)
}
func (m *MultiThisMonthChannelStatItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MultiThisMonthChannelStatItem.Marshal(b, m, deterministic)
}
func (dst *MultiThisMonthChannelStatItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultiThisMonthChannelStatItem.Merge(dst, src)
}
func (m *MultiThisMonthChannelStatItem) XXX_Size() int {
	return xxx_messageInfo_MultiThisMonthChannelStatItem.Size(m)
}
func (m *MultiThisMonthChannelStatItem) XXX_DiscardUnknown() {
	xxx_messageInfo_MultiThisMonthChannelStatItem.DiscardUnknown(m)
}

var xxx_messageInfo_MultiThisMonthChannelStatItem proto.InternalMessageInfo

func (m *MultiThisMonthChannelStatItem) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MultiThisMonthChannelStatItem) GetThisMonthFee() uint64 {
	if m != nil {
		return m.ThisMonthFee
	}
	return 0
}

func (m *MultiThisMonthChannelStatItem) GetLastMonthFee() uint64 {
	if m != nil {
		return m.LastMonthFee
	}
	return 0
}

func (m *MultiThisMonthChannelStatItem) GetLastMonthSamePeriodFee() uint64 {
	if m != nil {
		return m.LastMonthSamePeriodFee
	}
	return 0
}

func (m *MultiThisMonthChannelStatItem) GetChainRatio() float32 {
	if m != nil {
		return m.ChainRatio
	}
	return 0
}

type GetMultiThisMonthChannelStatResp struct {
	List                 []*MultiThisMonthChannelStatItem `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Total                uint32                           `protobuf:"varint,5,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *GetMultiThisMonthChannelStatResp) Reset()         { *m = GetMultiThisMonthChannelStatResp{} }
func (m *GetMultiThisMonthChannelStatResp) String() string { return proto.CompactTextString(m) }
func (*GetMultiThisMonthChannelStatResp) ProtoMessage()    {}
func (*GetMultiThisMonthChannelStatResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{2}
}
func (m *GetMultiThisMonthChannelStatResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMultiThisMonthChannelStatResp.Unmarshal(m, b)
}
func (m *GetMultiThisMonthChannelStatResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMultiThisMonthChannelStatResp.Marshal(b, m, deterministic)
}
func (dst *GetMultiThisMonthChannelStatResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMultiThisMonthChannelStatResp.Merge(dst, src)
}
func (m *GetMultiThisMonthChannelStatResp) XXX_Size() int {
	return xxx_messageInfo_GetMultiThisMonthChannelStatResp.Size(m)
}
func (m *GetMultiThisMonthChannelStatResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMultiThisMonthChannelStatResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMultiThisMonthChannelStatResp proto.InternalMessageInfo

func (m *GetMultiThisMonthChannelStatResp) GetList() []*MultiThisMonthChannelStatItem {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetMultiThisMonthChannelStatResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetMultiAnchorMonthStatReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	Uid                  []uint32 `protobuf:"varint,4,rep,packed,name=uid,proto3" json:"uid,omitempty"`
	StartTime            uint32   `protobuf:"varint,5,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	AnchorType           uint32   `protobuf:"varint,7,opt,name=anchor_type,json=anchorType,proto3" json:"anchor_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMultiAnchorMonthStatReq) Reset()         { *m = GetMultiAnchorMonthStatReq{} }
func (m *GetMultiAnchorMonthStatReq) String() string { return proto.CompactTextString(m) }
func (*GetMultiAnchorMonthStatReq) ProtoMessage()    {}
func (*GetMultiAnchorMonthStatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{3}
}
func (m *GetMultiAnchorMonthStatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMultiAnchorMonthStatReq.Unmarshal(m, b)
}
func (m *GetMultiAnchorMonthStatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMultiAnchorMonthStatReq.Marshal(b, m, deterministic)
}
func (dst *GetMultiAnchorMonthStatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMultiAnchorMonthStatReq.Merge(dst, src)
}
func (m *GetMultiAnchorMonthStatReq) XXX_Size() int {
	return xxx_messageInfo_GetMultiAnchorMonthStatReq.Size(m)
}
func (m *GetMultiAnchorMonthStatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMultiAnchorMonthStatReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMultiAnchorMonthStatReq proto.InternalMessageInfo

func (m *GetMultiAnchorMonthStatReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetMultiAnchorMonthStatReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetMultiAnchorMonthStatReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetMultiAnchorMonthStatReq) GetUid() []uint32 {
	if m != nil {
		return m.Uid
	}
	return nil
}

func (m *GetMultiAnchorMonthStatReq) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *GetMultiAnchorMonthStatReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetMultiAnchorMonthStatReq) GetAnchorType() uint32 {
	if m != nil {
		return m.AnchorType
	}
	return 0
}

type MultiAnchorMonthStatItem struct {
	Date                 string   `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	RegisterTime         uint32   `protobuf:"varint,3,opt,name=register_time,json=registerTime,proto3" json:"register_time,omitempty"`
	ContractStartTime    uint64   `protobuf:"varint,4,opt,name=contractStartTime,proto3" json:"contractStartTime,omitempty"`
	ContractEndTime      uint64   `protobuf:"varint,5,opt,name=contractEndTime,proto3" json:"contractEndTime,omitempty"`
	MonthValidDay        uint32   `protobuf:"varint,6,opt,name=monthValidDay,proto3" json:"monthValidDay,omitempty"`
	MonthValidHour       uint32   `protobuf:"varint,7,opt,name=monthValidHour,proto3" json:"monthValidHour,omitempty"`
	Fee                  uint64   `protobuf:"varint,8,opt,name=fee,proto3" json:"fee,omitempty"`
	AbilityType          uint32   `protobuf:"varint,9,opt,name=ability_type,json=abilityType,proto3" json:"ability_type,omitempty"`
	ValidSec             uint32   `protobuf:"varint,10,opt,name=valid_sec,json=validSec,proto3" json:"valid_sec,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MultiAnchorMonthStatItem) Reset()         { *m = MultiAnchorMonthStatItem{} }
func (m *MultiAnchorMonthStatItem) String() string { return proto.CompactTextString(m) }
func (*MultiAnchorMonthStatItem) ProtoMessage()    {}
func (*MultiAnchorMonthStatItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{4}
}
func (m *MultiAnchorMonthStatItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MultiAnchorMonthStatItem.Unmarshal(m, b)
}
func (m *MultiAnchorMonthStatItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MultiAnchorMonthStatItem.Marshal(b, m, deterministic)
}
func (dst *MultiAnchorMonthStatItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultiAnchorMonthStatItem.Merge(dst, src)
}
func (m *MultiAnchorMonthStatItem) XXX_Size() int {
	return xxx_messageInfo_MultiAnchorMonthStatItem.Size(m)
}
func (m *MultiAnchorMonthStatItem) XXX_DiscardUnknown() {
	xxx_messageInfo_MultiAnchorMonthStatItem.DiscardUnknown(m)
}

var xxx_messageInfo_MultiAnchorMonthStatItem proto.InternalMessageInfo

func (m *MultiAnchorMonthStatItem) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

func (m *MultiAnchorMonthStatItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MultiAnchorMonthStatItem) GetRegisterTime() uint32 {
	if m != nil {
		return m.RegisterTime
	}
	return 0
}

func (m *MultiAnchorMonthStatItem) GetContractStartTime() uint64 {
	if m != nil {
		return m.ContractStartTime
	}
	return 0
}

func (m *MultiAnchorMonthStatItem) GetContractEndTime() uint64 {
	if m != nil {
		return m.ContractEndTime
	}
	return 0
}

func (m *MultiAnchorMonthStatItem) GetMonthValidDay() uint32 {
	if m != nil {
		return m.MonthValidDay
	}
	return 0
}

func (m *MultiAnchorMonthStatItem) GetMonthValidHour() uint32 {
	if m != nil {
		return m.MonthValidHour
	}
	return 0
}

func (m *MultiAnchorMonthStatItem) GetFee() uint64 {
	if m != nil {
		return m.Fee
	}
	return 0
}

func (m *MultiAnchorMonthStatItem) GetAbilityType() uint32 {
	if m != nil {
		return m.AbilityType
	}
	return 0
}

func (m *MultiAnchorMonthStatItem) GetValidSec() uint32 {
	if m != nil {
		return m.ValidSec
	}
	return 0
}

type GetMultiAnchorMonthStatResp struct {
	List                 []*MultiAnchorMonthStatItem `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Total                uint32                      `protobuf:"varint,5,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetMultiAnchorMonthStatResp) Reset()         { *m = GetMultiAnchorMonthStatResp{} }
func (m *GetMultiAnchorMonthStatResp) String() string { return proto.CompactTextString(m) }
func (*GetMultiAnchorMonthStatResp) ProtoMessage()    {}
func (*GetMultiAnchorMonthStatResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{5}
}
func (m *GetMultiAnchorMonthStatResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMultiAnchorMonthStatResp.Unmarshal(m, b)
}
func (m *GetMultiAnchorMonthStatResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMultiAnchorMonthStatResp.Marshal(b, m, deterministic)
}
func (dst *GetMultiAnchorMonthStatResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMultiAnchorMonthStatResp.Merge(dst, src)
}
func (m *GetMultiAnchorMonthStatResp) XXX_Size() int {
	return xxx_messageInfo_GetMultiAnchorMonthStatResp.Size(m)
}
func (m *GetMultiAnchorMonthStatResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMultiAnchorMonthStatResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMultiAnchorMonthStatResp proto.InternalMessageInfo

func (m *GetMultiAnchorMonthStatResp) GetList() []*MultiAnchorMonthStatItem {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetMultiAnchorMonthStatResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetMultiAnchorChannelStatReq struct {
	GuildId              uint32         `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Offset               uint32         `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32         `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	Uid                  []uint32       `protobuf:"varint,4,rep,packed,name=uid,proto3" json:"uid,omitempty"`
	StartTime            uint32         `protobuf:"varint,5,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              uint32         `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Unit                 TimeFilterUnit `protobuf:"varint,7,opt,name=unit,proto3,enum=sign_anchor_stats.TimeFilterUnit" json:"unit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetMultiAnchorChannelStatReq) Reset()         { *m = GetMultiAnchorChannelStatReq{} }
func (m *GetMultiAnchorChannelStatReq) String() string { return proto.CompactTextString(m) }
func (*GetMultiAnchorChannelStatReq) ProtoMessage()    {}
func (*GetMultiAnchorChannelStatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{6}
}
func (m *GetMultiAnchorChannelStatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMultiAnchorChannelStatReq.Unmarshal(m, b)
}
func (m *GetMultiAnchorChannelStatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMultiAnchorChannelStatReq.Marshal(b, m, deterministic)
}
func (dst *GetMultiAnchorChannelStatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMultiAnchorChannelStatReq.Merge(dst, src)
}
func (m *GetMultiAnchorChannelStatReq) XXX_Size() int {
	return xxx_messageInfo_GetMultiAnchorChannelStatReq.Size(m)
}
func (m *GetMultiAnchorChannelStatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMultiAnchorChannelStatReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMultiAnchorChannelStatReq proto.InternalMessageInfo

func (m *GetMultiAnchorChannelStatReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetMultiAnchorChannelStatReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetMultiAnchorChannelStatReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetMultiAnchorChannelStatReq) GetUid() []uint32 {
	if m != nil {
		return m.Uid
	}
	return nil
}

func (m *GetMultiAnchorChannelStatReq) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *GetMultiAnchorChannelStatReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetMultiAnchorChannelStatReq) GetUnit() TimeFilterUnit {
	if m != nil {
		return m.Unit
	}
	return TimeFilterUnit_BY_DAY
}

type MultiAnchorChannelStatItem struct {
	Date                 string   `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	DateTimestamp        uint64   `protobuf:"varint,4,opt,name=date_timestamp,json=dateTimestamp,proto3" json:"date_timestamp,omitempty"`
	MonthValidDay        uint32   `protobuf:"varint,6,opt,name=monthValidDay,proto3" json:"monthValidDay,omitempty"`
	MonthValidHour       uint32   `protobuf:"varint,7,opt,name=monthValidHour,proto3" json:"monthValidHour,omitempty"`
	Income               uint64   `protobuf:"varint,8,opt,name=income,proto3" json:"income,omitempty"`
	PresentIncome        uint64   `protobuf:"varint,9,opt,name=present_income,json=presentIncome,proto3" json:"present_income,omitempty"`
	WerewolfIncome       uint64   `protobuf:"varint,10,opt,name=werewolf_income,json=werewolfIncome,proto3" json:"werewolf_income,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MultiAnchorChannelStatItem) Reset()         { *m = MultiAnchorChannelStatItem{} }
func (m *MultiAnchorChannelStatItem) String() string { return proto.CompactTextString(m) }
func (*MultiAnchorChannelStatItem) ProtoMessage()    {}
func (*MultiAnchorChannelStatItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{7}
}
func (m *MultiAnchorChannelStatItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MultiAnchorChannelStatItem.Unmarshal(m, b)
}
func (m *MultiAnchorChannelStatItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MultiAnchorChannelStatItem.Marshal(b, m, deterministic)
}
func (dst *MultiAnchorChannelStatItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultiAnchorChannelStatItem.Merge(dst, src)
}
func (m *MultiAnchorChannelStatItem) XXX_Size() int {
	return xxx_messageInfo_MultiAnchorChannelStatItem.Size(m)
}
func (m *MultiAnchorChannelStatItem) XXX_DiscardUnknown() {
	xxx_messageInfo_MultiAnchorChannelStatItem.DiscardUnknown(m)
}

var xxx_messageInfo_MultiAnchorChannelStatItem proto.InternalMessageInfo

func (m *MultiAnchorChannelStatItem) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

func (m *MultiAnchorChannelStatItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MultiAnchorChannelStatItem) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MultiAnchorChannelStatItem) GetDateTimestamp() uint64 {
	if m != nil {
		return m.DateTimestamp
	}
	return 0
}

func (m *MultiAnchorChannelStatItem) GetMonthValidDay() uint32 {
	if m != nil {
		return m.MonthValidDay
	}
	return 0
}

func (m *MultiAnchorChannelStatItem) GetMonthValidHour() uint32 {
	if m != nil {
		return m.MonthValidHour
	}
	return 0
}

func (m *MultiAnchorChannelStatItem) GetIncome() uint64 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *MultiAnchorChannelStatItem) GetPresentIncome() uint64 {
	if m != nil {
		return m.PresentIncome
	}
	return 0
}

func (m *MultiAnchorChannelStatItem) GetWerewolfIncome() uint64 {
	if m != nil {
		return m.WerewolfIncome
	}
	return 0
}

type GetMultiAnchorChannelStatResp struct {
	List                 []*MultiAnchorChannelStatItem `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Total                uint32                        `protobuf:"varint,5,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetMultiAnchorChannelStatResp) Reset()         { *m = GetMultiAnchorChannelStatResp{} }
func (m *GetMultiAnchorChannelStatResp) String() string { return proto.CompactTextString(m) }
func (*GetMultiAnchorChannelStatResp) ProtoMessage()    {}
func (*GetMultiAnchorChannelStatResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{8}
}
func (m *GetMultiAnchorChannelStatResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMultiAnchorChannelStatResp.Unmarshal(m, b)
}
func (m *GetMultiAnchorChannelStatResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMultiAnchorChannelStatResp.Marshal(b, m, deterministic)
}
func (dst *GetMultiAnchorChannelStatResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMultiAnchorChannelStatResp.Merge(dst, src)
}
func (m *GetMultiAnchorChannelStatResp) XXX_Size() int {
	return xxx_messageInfo_GetMultiAnchorChannelStatResp.Size(m)
}
func (m *GetMultiAnchorChannelStatResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMultiAnchorChannelStatResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMultiAnchorChannelStatResp proto.InternalMessageInfo

func (m *GetMultiAnchorChannelStatResp) GetList() []*MultiAnchorChannelStatItem {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetMultiAnchorChannelStatResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetUserTbeanConsumeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BeginTs              uint32   `protobuf:"varint,2,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,3,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserTbeanConsumeReq) Reset()         { *m = GetUserTbeanConsumeReq{} }
func (m *GetUserTbeanConsumeReq) String() string { return proto.CompactTextString(m) }
func (*GetUserTbeanConsumeReq) ProtoMessage()    {}
func (*GetUserTbeanConsumeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{9}
}
func (m *GetUserTbeanConsumeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserTbeanConsumeReq.Unmarshal(m, b)
}
func (m *GetUserTbeanConsumeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserTbeanConsumeReq.Marshal(b, m, deterministic)
}
func (dst *GetUserTbeanConsumeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserTbeanConsumeReq.Merge(dst, src)
}
func (m *GetUserTbeanConsumeReq) XXX_Size() int {
	return xxx_messageInfo_GetUserTbeanConsumeReq.Size(m)
}
func (m *GetUserTbeanConsumeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserTbeanConsumeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserTbeanConsumeReq proto.InternalMessageInfo

func (m *GetUserTbeanConsumeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserTbeanConsumeReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *GetUserTbeanConsumeReq) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

type GetUserTbeanConsumeResp struct {
	Total                uint32   `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserTbeanConsumeResp) Reset()         { *m = GetUserTbeanConsumeResp{} }
func (m *GetUserTbeanConsumeResp) String() string { return proto.CompactTextString(m) }
func (*GetUserTbeanConsumeResp) ProtoMessage()    {}
func (*GetUserTbeanConsumeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{10}
}
func (m *GetUserTbeanConsumeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserTbeanConsumeResp.Unmarshal(m, b)
}
func (m *GetUserTbeanConsumeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserTbeanConsumeResp.Marshal(b, m, deterministic)
}
func (dst *GetUserTbeanConsumeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserTbeanConsumeResp.Merge(dst, src)
}
func (m *GetUserTbeanConsumeResp) XXX_Size() int {
	return xxx_messageInfo_GetUserTbeanConsumeResp.Size(m)
}
func (m *GetUserTbeanConsumeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserTbeanConsumeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserTbeanConsumeResp proto.InternalMessageInfo

func (m *GetUserTbeanConsumeResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetMultiAnchorDailyStatsListByGuildIdReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	AnchorUids           []uint32 `protobuf:"varint,2,rep,packed,name=anchor_uids,json=anchorUids,proto3" json:"anchor_uids,omitempty"`
	BeginTs              uint32   `protobuf:"varint,3,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,4,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMultiAnchorDailyStatsListByGuildIdReq) Reset() {
	*m = GetMultiAnchorDailyStatsListByGuildIdReq{}
}
func (m *GetMultiAnchorDailyStatsListByGuildIdReq) String() string { return proto.CompactTextString(m) }
func (*GetMultiAnchorDailyStatsListByGuildIdReq) ProtoMessage()    {}
func (*GetMultiAnchorDailyStatsListByGuildIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{11}
}
func (m *GetMultiAnchorDailyStatsListByGuildIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMultiAnchorDailyStatsListByGuildIdReq.Unmarshal(m, b)
}
func (m *GetMultiAnchorDailyStatsListByGuildIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMultiAnchorDailyStatsListByGuildIdReq.Marshal(b, m, deterministic)
}
func (dst *GetMultiAnchorDailyStatsListByGuildIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMultiAnchorDailyStatsListByGuildIdReq.Merge(dst, src)
}
func (m *GetMultiAnchorDailyStatsListByGuildIdReq) XXX_Size() int {
	return xxx_messageInfo_GetMultiAnchorDailyStatsListByGuildIdReq.Size(m)
}
func (m *GetMultiAnchorDailyStatsListByGuildIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMultiAnchorDailyStatsListByGuildIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMultiAnchorDailyStatsListByGuildIdReq proto.InternalMessageInfo

func (m *GetMultiAnchorDailyStatsListByGuildIdReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetMultiAnchorDailyStatsListByGuildIdReq) GetAnchorUids() []uint32 {
	if m != nil {
		return m.AnchorUids
	}
	return nil
}

func (m *GetMultiAnchorDailyStatsListByGuildIdReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *GetMultiAnchorDailyStatsListByGuildIdReq) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

type GetMultiAnchorDailyStatsListByGuildIdResp struct {
	List                 []*MultiAnchorDailyStats `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetMultiAnchorDailyStatsListByGuildIdResp) Reset() {
	*m = GetMultiAnchorDailyStatsListByGuildIdResp{}
}
func (m *GetMultiAnchorDailyStatsListByGuildIdResp) String() string {
	return proto.CompactTextString(m)
}
func (*GetMultiAnchorDailyStatsListByGuildIdResp) ProtoMessage() {}
func (*GetMultiAnchorDailyStatsListByGuildIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{12}
}
func (m *GetMultiAnchorDailyStatsListByGuildIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMultiAnchorDailyStatsListByGuildIdResp.Unmarshal(m, b)
}
func (m *GetMultiAnchorDailyStatsListByGuildIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMultiAnchorDailyStatsListByGuildIdResp.Marshal(b, m, deterministic)
}
func (dst *GetMultiAnchorDailyStatsListByGuildIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMultiAnchorDailyStatsListByGuildIdResp.Merge(dst, src)
}
func (m *GetMultiAnchorDailyStatsListByGuildIdResp) XXX_Size() int {
	return xxx_messageInfo_GetMultiAnchorDailyStatsListByGuildIdResp.Size(m)
}
func (m *GetMultiAnchorDailyStatsListByGuildIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMultiAnchorDailyStatsListByGuildIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMultiAnchorDailyStatsListByGuildIdResp proto.InternalMessageInfo

func (m *GetMultiAnchorDailyStatsListByGuildIdResp) GetList() []*MultiAnchorDailyStats {
	if m != nil {
		return m.List
	}
	return nil
}

type MultiAnchorDailyStats struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Date                 uint32   `protobuf:"varint,3,opt,name=date,proto3" json:"date,omitempty"`
	AnchorIncome         uint32   `protobuf:"varint,4,opt,name=anchor_income,json=anchorIncome,proto3" json:"anchor_income,omitempty"`
	IsValidDay           bool     `protobuf:"varint,5,opt,name=is_valid_day,json=isValidDay,proto3" json:"is_valid_day,omitempty"`
	ValidSec             uint32   `protobuf:"varint,6,opt,name=valid_sec,json=validSec,proto3" json:"valid_sec,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MultiAnchorDailyStats) Reset()         { *m = MultiAnchorDailyStats{} }
func (m *MultiAnchorDailyStats) String() string { return proto.CompactTextString(m) }
func (*MultiAnchorDailyStats) ProtoMessage()    {}
func (*MultiAnchorDailyStats) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{13}
}
func (m *MultiAnchorDailyStats) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MultiAnchorDailyStats.Unmarshal(m, b)
}
func (m *MultiAnchorDailyStats) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MultiAnchorDailyStats.Marshal(b, m, deterministic)
}
func (dst *MultiAnchorDailyStats) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultiAnchorDailyStats.Merge(dst, src)
}
func (m *MultiAnchorDailyStats) XXX_Size() int {
	return xxx_messageInfo_MultiAnchorDailyStats.Size(m)
}
func (m *MultiAnchorDailyStats) XXX_DiscardUnknown() {
	xxx_messageInfo_MultiAnchorDailyStats.DiscardUnknown(m)
}

var xxx_messageInfo_MultiAnchorDailyStats proto.InternalMessageInfo

func (m *MultiAnchorDailyStats) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MultiAnchorDailyStats) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *MultiAnchorDailyStats) GetDate() uint32 {
	if m != nil {
		return m.Date
	}
	return 0
}

func (m *MultiAnchorDailyStats) GetAnchorIncome() uint32 {
	if m != nil {
		return m.AnchorIncome
	}
	return 0
}

func (m *MultiAnchorDailyStats) GetIsValidDay() bool {
	if m != nil {
		return m.IsValidDay
	}
	return false
}

func (m *MultiAnchorDailyStats) GetValidSec() uint32 {
	if m != nil {
		return m.ValidSec
	}
	return 0
}

// 获取多人互动主播日维度信息
type GetMultiAnchorDailyStatsListReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	UidList              []uint32 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	BeginTs              uint32   `protobuf:"varint,3,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,4,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	Offset               uint32   `protobuf:"varint,5,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,6,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMultiAnchorDailyStatsListReq) Reset()         { *m = GetMultiAnchorDailyStatsListReq{} }
func (m *GetMultiAnchorDailyStatsListReq) String() string { return proto.CompactTextString(m) }
func (*GetMultiAnchorDailyStatsListReq) ProtoMessage()    {}
func (*GetMultiAnchorDailyStatsListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{14}
}
func (m *GetMultiAnchorDailyStatsListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMultiAnchorDailyStatsListReq.Unmarshal(m, b)
}
func (m *GetMultiAnchorDailyStatsListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMultiAnchorDailyStatsListReq.Marshal(b, m, deterministic)
}
func (dst *GetMultiAnchorDailyStatsListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMultiAnchorDailyStatsListReq.Merge(dst, src)
}
func (m *GetMultiAnchorDailyStatsListReq) XXX_Size() int {
	return xxx_messageInfo_GetMultiAnchorDailyStatsListReq.Size(m)
}
func (m *GetMultiAnchorDailyStatsListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMultiAnchorDailyStatsListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMultiAnchorDailyStatsListReq proto.InternalMessageInfo

func (m *GetMultiAnchorDailyStatsListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetMultiAnchorDailyStatsListReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetMultiAnchorDailyStatsListReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *GetMultiAnchorDailyStatsListReq) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *GetMultiAnchorDailyStatsListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetMultiAnchorDailyStatsListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetMultiAnchorDailyStatsListResp struct {
	List                 []*MultiAnchorDailyStats `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	TotalCnt             uint32                   `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetMultiAnchorDailyStatsListResp) Reset()         { *m = GetMultiAnchorDailyStatsListResp{} }
func (m *GetMultiAnchorDailyStatsListResp) String() string { return proto.CompactTextString(m) }
func (*GetMultiAnchorDailyStatsListResp) ProtoMessage()    {}
func (*GetMultiAnchorDailyStatsListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{15}
}
func (m *GetMultiAnchorDailyStatsListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMultiAnchorDailyStatsListResp.Unmarshal(m, b)
}
func (m *GetMultiAnchorDailyStatsListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMultiAnchorDailyStatsListResp.Marshal(b, m, deterministic)
}
func (dst *GetMultiAnchorDailyStatsListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMultiAnchorDailyStatsListResp.Merge(dst, src)
}
func (m *GetMultiAnchorDailyStatsListResp) XXX_Size() int {
	return xxx_messageInfo_GetMultiAnchorDailyStatsListResp.Size(m)
}
func (m *GetMultiAnchorDailyStatsListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMultiAnchorDailyStatsListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMultiAnchorDailyStatsListResp proto.InternalMessageInfo

func (m *GetMultiAnchorDailyStatsListResp) GetList() []*MultiAnchorDailyStats {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetMultiAnchorDailyStatsListResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

// pgc房间月信息
type PgcMonthlyInfo struct {
	Ts                      uint32   `protobuf:"varint,1,opt,name=ts,proto3" json:"ts,omitempty"`
	ChannelId               uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelFee              uint64   `protobuf:"varint,3,opt,name=channel_fee,json=channelFee,proto3" json:"channel_fee,omitempty"`
	ValidAnchorCnt          uint32   `protobuf:"varint,4,opt,name=valid_anchor_cnt,json=validAnchorCnt,proto3" json:"valid_anchor_cnt,omitempty"`
	ActiveAnchorCnt         uint32   `protobuf:"varint,5,opt,name=active_anchor_cnt,json=activeAnchorCnt,proto3" json:"active_anchor_cnt,omitempty"`
	QualityAnchorCnt        uint32   `protobuf:"varint,6,opt,name=quality_anchor_cnt,json=qualityAnchorCnt,proto3" json:"quality_anchor_cnt,omitempty"`
	ActiveAnchorFee         uint64   `protobuf:"varint,7,opt,name=active_anchor_fee,json=activeAnchorFee,proto3" json:"active_anchor_fee,omitempty"`
	QualityAnchorFee        uint64   `protobuf:"varint,8,opt,name=quality_anchor_fee,json=qualityAnchorFee,proto3" json:"quality_anchor_fee,omitempty"`
	QualityAnchorCntRatio   float32  `protobuf:"fixed32,9,opt,name=quality_anchor_cnt_ratio,json=qualityAnchorCntRatio,proto3" json:"quality_anchor_cnt_ratio,omitempty"`
	QualityAnchorFeeRatio   float32  `protobuf:"fixed32,10,opt,name=quality_anchor_fee_ratio,json=qualityAnchorFeeRatio,proto3" json:"quality_anchor_fee_ratio,omitempty"`
	TotalChannelFee         uint64   `protobuf:"varint,11,opt,name=total_channel_fee,json=totalChannelFee,proto3" json:"total_channel_fee,omitempty"`
	QualityAnchorTransRatio float32  `protobuf:"fixed32,12,opt,name=quality_anchor_trans_ratio,json=qualityAnchorTransRatio,proto3" json:"quality_anchor_trans_ratio,omitempty"`
	ChannelPkgFeeRatio      float32  `protobuf:"fixed32,13,opt,name=channel_pkg_fee_ratio,json=channelPkgFeeRatio,proto3" json:"channel_pkg_fee_ratio,omitempty"`
	ProfessionPracCnt       uint32   `protobuf:"varint,14,opt,name=profession_prac_cnt,json=professionPracCnt,proto3" json:"profession_prac_cnt,omitempty"`
	ValidSignMemberCnt      uint32   `protobuf:"varint,15,opt,name=valid_sign_member_cnt,json=validSignMemberCnt,proto3" json:"valid_sign_member_cnt,omitempty"`
	XXX_NoUnkeyedLiteral    struct{} `json:"-"`
	XXX_unrecognized        []byte   `json:"-"`
	XXX_sizecache           int32    `json:"-"`
}

func (m *PgcMonthlyInfo) Reset()         { *m = PgcMonthlyInfo{} }
func (m *PgcMonthlyInfo) String() string { return proto.CompactTextString(m) }
func (*PgcMonthlyInfo) ProtoMessage()    {}
func (*PgcMonthlyInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{16}
}
func (m *PgcMonthlyInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcMonthlyInfo.Unmarshal(m, b)
}
func (m *PgcMonthlyInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcMonthlyInfo.Marshal(b, m, deterministic)
}
func (dst *PgcMonthlyInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcMonthlyInfo.Merge(dst, src)
}
func (m *PgcMonthlyInfo) XXX_Size() int {
	return xxx_messageInfo_PgcMonthlyInfo.Size(m)
}
func (m *PgcMonthlyInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcMonthlyInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PgcMonthlyInfo proto.InternalMessageInfo

func (m *PgcMonthlyInfo) GetTs() uint32 {
	if m != nil {
		return m.Ts
	}
	return 0
}

func (m *PgcMonthlyInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PgcMonthlyInfo) GetChannelFee() uint64 {
	if m != nil {
		return m.ChannelFee
	}
	return 0
}

func (m *PgcMonthlyInfo) GetValidAnchorCnt() uint32 {
	if m != nil {
		return m.ValidAnchorCnt
	}
	return 0
}

func (m *PgcMonthlyInfo) GetActiveAnchorCnt() uint32 {
	if m != nil {
		return m.ActiveAnchorCnt
	}
	return 0
}

func (m *PgcMonthlyInfo) GetQualityAnchorCnt() uint32 {
	if m != nil {
		return m.QualityAnchorCnt
	}
	return 0
}

func (m *PgcMonthlyInfo) GetActiveAnchorFee() uint64 {
	if m != nil {
		return m.ActiveAnchorFee
	}
	return 0
}

func (m *PgcMonthlyInfo) GetQualityAnchorFee() uint64 {
	if m != nil {
		return m.QualityAnchorFee
	}
	return 0
}

func (m *PgcMonthlyInfo) GetQualityAnchorCntRatio() float32 {
	if m != nil {
		return m.QualityAnchorCntRatio
	}
	return 0
}

func (m *PgcMonthlyInfo) GetQualityAnchorFeeRatio() float32 {
	if m != nil {
		return m.QualityAnchorFeeRatio
	}
	return 0
}

func (m *PgcMonthlyInfo) GetTotalChannelFee() uint64 {
	if m != nil {
		return m.TotalChannelFee
	}
	return 0
}

func (m *PgcMonthlyInfo) GetQualityAnchorTransRatio() float32 {
	if m != nil {
		return m.QualityAnchorTransRatio
	}
	return 0
}

func (m *PgcMonthlyInfo) GetChannelPkgFeeRatio() float32 {
	if m != nil {
		return m.ChannelPkgFeeRatio
	}
	return 0
}

func (m *PgcMonthlyInfo) GetProfessionPracCnt() uint32 {
	if m != nil {
		return m.ProfessionPracCnt
	}
	return 0
}

func (m *PgcMonthlyInfo) GetValidSignMemberCnt() uint32 {
	if m != nil {
		return m.ValidSignMemberCnt
	}
	return 0
}

// 获取公会公开房月信息列表
type GetPgcMonthlyInfoListReq struct {
	MonthTs              uint32   `protobuf:"varint,1,opt,name=month_ts,json=monthTs,proto3" json:"month_ts,omitempty"`
	CidList              []uint32 `protobuf:"varint,2,rep,packed,name=cid_list,json=cidList,proto3" json:"cid_list,omitempty"`
	Page                 uint32   `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	GuildId              uint32   `protobuf:"varint,5,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPgcMonthlyInfoListReq) Reset()         { *m = GetPgcMonthlyInfoListReq{} }
func (m *GetPgcMonthlyInfoListReq) String() string { return proto.CompactTextString(m) }
func (*GetPgcMonthlyInfoListReq) ProtoMessage()    {}
func (*GetPgcMonthlyInfoListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{17}
}
func (m *GetPgcMonthlyInfoListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPgcMonthlyInfoListReq.Unmarshal(m, b)
}
func (m *GetPgcMonthlyInfoListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPgcMonthlyInfoListReq.Marshal(b, m, deterministic)
}
func (dst *GetPgcMonthlyInfoListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPgcMonthlyInfoListReq.Merge(dst, src)
}
func (m *GetPgcMonthlyInfoListReq) XXX_Size() int {
	return xxx_messageInfo_GetPgcMonthlyInfoListReq.Size(m)
}
func (m *GetPgcMonthlyInfoListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPgcMonthlyInfoListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPgcMonthlyInfoListReq proto.InternalMessageInfo

func (m *GetPgcMonthlyInfoListReq) GetMonthTs() uint32 {
	if m != nil {
		return m.MonthTs
	}
	return 0
}

func (m *GetPgcMonthlyInfoListReq) GetCidList() []uint32 {
	if m != nil {
		return m.CidList
	}
	return nil
}

func (m *GetPgcMonthlyInfoListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetPgcMonthlyInfoListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetPgcMonthlyInfoListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetPgcMonthlyInfoListResp struct {
	InfoList             []*PgcMonthlyInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	TotalCnt             uint32            `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	NextPage             uint32            `protobuf:"varint,3,opt,name=next_page,json=nextPage,proto3" json:"next_page,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetPgcMonthlyInfoListResp) Reset()         { *m = GetPgcMonthlyInfoListResp{} }
func (m *GetPgcMonthlyInfoListResp) String() string { return proto.CompactTextString(m) }
func (*GetPgcMonthlyInfoListResp) ProtoMessage()    {}
func (*GetPgcMonthlyInfoListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{18}
}
func (m *GetPgcMonthlyInfoListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPgcMonthlyInfoListResp.Unmarshal(m, b)
}
func (m *GetPgcMonthlyInfoListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPgcMonthlyInfoListResp.Marshal(b, m, deterministic)
}
func (dst *GetPgcMonthlyInfoListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPgcMonthlyInfoListResp.Merge(dst, src)
}
func (m *GetPgcMonthlyInfoListResp) XXX_Size() int {
	return xxx_messageInfo_GetPgcMonthlyInfoListResp.Size(m)
}
func (m *GetPgcMonthlyInfoListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPgcMonthlyInfoListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPgcMonthlyInfoListResp proto.InternalMessageInfo

func (m *GetPgcMonthlyInfoListResp) GetInfoList() []*PgcMonthlyInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetPgcMonthlyInfoListResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

func (m *GetPgcMonthlyInfoListResp) GetNextPage() uint32 {
	if m != nil {
		return m.NextPage
	}
	return 0
}

// 公会公开房日信息
type PgcDailyInfo struct {
	Ts                   uint32   `protobuf:"varint,1,opt,name=ts,proto3" json:"ts,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelFee           uint64   `protobuf:"varint,3,opt,name=channel_fee,json=channelFee,proto3" json:"channel_fee,omitempty"`
	AnchorFee            uint32   `protobuf:"varint,4,opt,name=anchor_fee,json=anchorFee,proto3" json:"anchor_fee,omitempty"`
	PkgGiftFee           uint32   `protobuf:"varint,5,opt,name=pkg_gift_fee,json=pkgGiftFee,proto3" json:"pkg_gift_fee,omitempty"`
	TbeanGiftFee         uint32   `protobuf:"varint,6,opt,name=tbean_gift_fee,json=tbeanGiftFee,proto3" json:"tbean_gift_fee,omitempty"`
	SendGiftCnt          uint32   `protobuf:"varint,7,opt,name=send_gift_cnt,json=sendGiftCnt,proto3" json:"send_gift_cnt,omitempty"`
	OneHourValidCnt      uint32   `protobuf:"varint,8,opt,name=one_hour_valid_cnt,json=oneHourValidCnt,proto3" json:"one_hour_valid_cnt,omitempty"`
	TwoHourValidCnt      uint32   `protobuf:"varint,9,opt,name=two_hour_valid_cnt,json=twoHourValidCnt,proto3" json:"two_hour_valid_cnt,omitempty"`
	FourHourValidCnt     uint32   `protobuf:"varint,10,opt,name=four_hour_valid_cnt,json=fourHourValidCnt,proto3" json:"four_hour_valid_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PgcDailyInfo) Reset()         { *m = PgcDailyInfo{} }
func (m *PgcDailyInfo) String() string { return proto.CompactTextString(m) }
func (*PgcDailyInfo) ProtoMessage()    {}
func (*PgcDailyInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{19}
}
func (m *PgcDailyInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcDailyInfo.Unmarshal(m, b)
}
func (m *PgcDailyInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcDailyInfo.Marshal(b, m, deterministic)
}
func (dst *PgcDailyInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcDailyInfo.Merge(dst, src)
}
func (m *PgcDailyInfo) XXX_Size() int {
	return xxx_messageInfo_PgcDailyInfo.Size(m)
}
func (m *PgcDailyInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcDailyInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PgcDailyInfo proto.InternalMessageInfo

func (m *PgcDailyInfo) GetTs() uint32 {
	if m != nil {
		return m.Ts
	}
	return 0
}

func (m *PgcDailyInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PgcDailyInfo) GetChannelFee() uint64 {
	if m != nil {
		return m.ChannelFee
	}
	return 0
}

func (m *PgcDailyInfo) GetAnchorFee() uint32 {
	if m != nil {
		return m.AnchorFee
	}
	return 0
}

func (m *PgcDailyInfo) GetPkgGiftFee() uint32 {
	if m != nil {
		return m.PkgGiftFee
	}
	return 0
}

func (m *PgcDailyInfo) GetTbeanGiftFee() uint32 {
	if m != nil {
		return m.TbeanGiftFee
	}
	return 0
}

func (m *PgcDailyInfo) GetSendGiftCnt() uint32 {
	if m != nil {
		return m.SendGiftCnt
	}
	return 0
}

func (m *PgcDailyInfo) GetOneHourValidCnt() uint32 {
	if m != nil {
		return m.OneHourValidCnt
	}
	return 0
}

func (m *PgcDailyInfo) GetTwoHourValidCnt() uint32 {
	if m != nil {
		return m.TwoHourValidCnt
	}
	return 0
}

func (m *PgcDailyInfo) GetFourHourValidCnt() uint32 {
	if m != nil {
		return m.FourHourValidCnt
	}
	return 0
}

// 获取公会公开房日信息列表
type GetPgcDailyInfoListReq struct {
	BeginTs              uint32   `protobuf:"varint,1,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,2,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	CidList              []uint32 `protobuf:"varint,3,rep,packed,name=cid_list,json=cidList,proto3" json:"cid_list,omitempty"`
	Page                 uint32   `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	GuildId              uint32   `protobuf:"varint,6,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPgcDailyInfoListReq) Reset()         { *m = GetPgcDailyInfoListReq{} }
func (m *GetPgcDailyInfoListReq) String() string { return proto.CompactTextString(m) }
func (*GetPgcDailyInfoListReq) ProtoMessage()    {}
func (*GetPgcDailyInfoListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{20}
}
func (m *GetPgcDailyInfoListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPgcDailyInfoListReq.Unmarshal(m, b)
}
func (m *GetPgcDailyInfoListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPgcDailyInfoListReq.Marshal(b, m, deterministic)
}
func (dst *GetPgcDailyInfoListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPgcDailyInfoListReq.Merge(dst, src)
}
func (m *GetPgcDailyInfoListReq) XXX_Size() int {
	return xxx_messageInfo_GetPgcDailyInfoListReq.Size(m)
}
func (m *GetPgcDailyInfoListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPgcDailyInfoListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPgcDailyInfoListReq proto.InternalMessageInfo

func (m *GetPgcDailyInfoListReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *GetPgcDailyInfoListReq) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *GetPgcDailyInfoListReq) GetCidList() []uint32 {
	if m != nil {
		return m.CidList
	}
	return nil
}

func (m *GetPgcDailyInfoListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetPgcDailyInfoListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetPgcDailyInfoListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetPgcDailyInfoListResp struct {
	InfoList             []*PgcDailyInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	TotalCnt             uint32          `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	NextPage             uint32          `protobuf:"varint,3,opt,name=next_page,json=nextPage,proto3" json:"next_page,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetPgcDailyInfoListResp) Reset()         { *m = GetPgcDailyInfoListResp{} }
func (m *GetPgcDailyInfoListResp) String() string { return proto.CompactTextString(m) }
func (*GetPgcDailyInfoListResp) ProtoMessage()    {}
func (*GetPgcDailyInfoListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{21}
}
func (m *GetPgcDailyInfoListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPgcDailyInfoListResp.Unmarshal(m, b)
}
func (m *GetPgcDailyInfoListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPgcDailyInfoListResp.Marshal(b, m, deterministic)
}
func (dst *GetPgcDailyInfoListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPgcDailyInfoListResp.Merge(dst, src)
}
func (m *GetPgcDailyInfoListResp) XXX_Size() int {
	return xxx_messageInfo_GetPgcDailyInfoListResp.Size(m)
}
func (m *GetPgcDailyInfoListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPgcDailyInfoListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPgcDailyInfoListResp proto.InternalMessageInfo

func (m *GetPgcDailyInfoListResp) GetInfoList() []*PgcDailyInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetPgcDailyInfoListResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

func (m *GetPgcDailyInfoListResp) GetNextPage() uint32 {
	if m != nil {
		return m.NextPage
	}
	return 0
}

// 公会月信息
type GuildMonthlyStatsInfo struct {
	Ts                    uint32   `protobuf:"varint,1,opt,name=ts,proto3" json:"ts,omitempty"`
	GuildFee              uint64   `protobuf:"varint,2,opt,name=guild_fee,json=guildFee,proto3" json:"guild_fee,omitempty"`
	SignAnchorCnt         uint32   `protobuf:"varint,3,opt,name=sign_anchor_cnt,json=signAnchorCnt,proto3" json:"sign_anchor_cnt,omitempty"`
	ActiveAnchorCnt       uint32   `protobuf:"varint,4,opt,name=active_anchor_cnt,json=activeAnchorCnt,proto3" json:"active_anchor_cnt,omitempty"`
	QualityAnchorCnt      uint32   `protobuf:"varint,5,opt,name=quality_anchor_cnt,json=qualityAnchorCnt,proto3" json:"quality_anchor_cnt,omitempty"`
	ActiveAnchorFee       uint64   `protobuf:"varint,6,opt,name=active_anchor_fee,json=activeAnchorFee,proto3" json:"active_anchor_fee,omitempty"`
	QualityAnchorFee      uint64   `protobuf:"varint,7,opt,name=quality_anchor_fee,json=qualityAnchorFee,proto3" json:"quality_anchor_fee,omitempty"`
	QualityAnchorCntRatio float32  `protobuf:"fixed32,8,opt,name=quality_anchor_cnt_ratio,json=qualityAnchorCntRatio,proto3" json:"quality_anchor_cnt_ratio,omitempty"`
	QualityAnchorFeeRatio float32  `protobuf:"fixed32,9,opt,name=quality_anchor_fee_ratio,json=qualityAnchorFeeRatio,proto3" json:"quality_anchor_fee_ratio,omitempty"`
	GuildPkgFeeRatio      float32  `protobuf:"fixed32,10,opt,name=guild_pkg_fee_ratio,json=guildPkgFeeRatio,proto3" json:"guild_pkg_fee_ratio,omitempty"`
	ProfessionPracCnt     uint32   `protobuf:"varint,11,opt,name=profession_prac_cnt,json=professionPracCnt,proto3" json:"profession_prac_cnt,omitempty"`
	NewProfessionPracCnt  uint32   `protobuf:"varint,12,opt,name=new_profession_prac_cnt,json=newProfessionPracCnt,proto3" json:"new_profession_prac_cnt,omitempty"`
	ValidSignMemberCnt    uint32   `protobuf:"varint,13,opt,name=valid_sign_member_cnt,json=validSignMemberCnt,proto3" json:"valid_sign_member_cnt,omitempty"`
	NewValidSignMemCnt    uint32   `protobuf:"varint,14,opt,name=new_valid_sign_mem_cnt,json=newValidSignMemCnt,proto3" json:"new_valid_sign_mem_cnt,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *GuildMonthlyStatsInfo) Reset()         { *m = GuildMonthlyStatsInfo{} }
func (m *GuildMonthlyStatsInfo) String() string { return proto.CompactTextString(m) }
func (*GuildMonthlyStatsInfo) ProtoMessage()    {}
func (*GuildMonthlyStatsInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{22}
}
func (m *GuildMonthlyStatsInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildMonthlyStatsInfo.Unmarshal(m, b)
}
func (m *GuildMonthlyStatsInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildMonthlyStatsInfo.Marshal(b, m, deterministic)
}
func (dst *GuildMonthlyStatsInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildMonthlyStatsInfo.Merge(dst, src)
}
func (m *GuildMonthlyStatsInfo) XXX_Size() int {
	return xxx_messageInfo_GuildMonthlyStatsInfo.Size(m)
}
func (m *GuildMonthlyStatsInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildMonthlyStatsInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GuildMonthlyStatsInfo proto.InternalMessageInfo

func (m *GuildMonthlyStatsInfo) GetTs() uint32 {
	if m != nil {
		return m.Ts
	}
	return 0
}

func (m *GuildMonthlyStatsInfo) GetGuildFee() uint64 {
	if m != nil {
		return m.GuildFee
	}
	return 0
}

func (m *GuildMonthlyStatsInfo) GetSignAnchorCnt() uint32 {
	if m != nil {
		return m.SignAnchorCnt
	}
	return 0
}

func (m *GuildMonthlyStatsInfo) GetActiveAnchorCnt() uint32 {
	if m != nil {
		return m.ActiveAnchorCnt
	}
	return 0
}

func (m *GuildMonthlyStatsInfo) GetQualityAnchorCnt() uint32 {
	if m != nil {
		return m.QualityAnchorCnt
	}
	return 0
}

func (m *GuildMonthlyStatsInfo) GetActiveAnchorFee() uint64 {
	if m != nil {
		return m.ActiveAnchorFee
	}
	return 0
}

func (m *GuildMonthlyStatsInfo) GetQualityAnchorFee() uint64 {
	if m != nil {
		return m.QualityAnchorFee
	}
	return 0
}

func (m *GuildMonthlyStatsInfo) GetQualityAnchorCntRatio() float32 {
	if m != nil {
		return m.QualityAnchorCntRatio
	}
	return 0
}

func (m *GuildMonthlyStatsInfo) GetQualityAnchorFeeRatio() float32 {
	if m != nil {
		return m.QualityAnchorFeeRatio
	}
	return 0
}

func (m *GuildMonthlyStatsInfo) GetGuildPkgFeeRatio() float32 {
	if m != nil {
		return m.GuildPkgFeeRatio
	}
	return 0
}

func (m *GuildMonthlyStatsInfo) GetProfessionPracCnt() uint32 {
	if m != nil {
		return m.ProfessionPracCnt
	}
	return 0
}

func (m *GuildMonthlyStatsInfo) GetNewProfessionPracCnt() uint32 {
	if m != nil {
		return m.NewProfessionPracCnt
	}
	return 0
}

func (m *GuildMonthlyStatsInfo) GetValidSignMemberCnt() uint32 {
	if m != nil {
		return m.ValidSignMemberCnt
	}
	return 0
}

func (m *GuildMonthlyStatsInfo) GetNewValidSignMemCnt() uint32 {
	if m != nil {
		return m.NewValidSignMemCnt
	}
	return 0
}

// 获取公会月信息列表
type GetGuildMonthlyStatsInfoListReq struct {
	BeginMonth           uint32   `protobuf:"varint,1,opt,name=begin_month,json=beginMonth,proto3" json:"begin_month,omitempty"`
	EndMonth             uint32   `protobuf:"varint,2,opt,name=end_month,json=endMonth,proto3" json:"end_month,omitempty"`
	GuildId              uint32   `protobuf:"varint,3,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Page                 uint32   `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildMonthlyStatsInfoListReq) Reset()         { *m = GetGuildMonthlyStatsInfoListReq{} }
func (m *GetGuildMonthlyStatsInfoListReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildMonthlyStatsInfoListReq) ProtoMessage()    {}
func (*GetGuildMonthlyStatsInfoListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{23}
}
func (m *GetGuildMonthlyStatsInfoListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildMonthlyStatsInfoListReq.Unmarshal(m, b)
}
func (m *GetGuildMonthlyStatsInfoListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildMonthlyStatsInfoListReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildMonthlyStatsInfoListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildMonthlyStatsInfoListReq.Merge(dst, src)
}
func (m *GetGuildMonthlyStatsInfoListReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildMonthlyStatsInfoListReq.Size(m)
}
func (m *GetGuildMonthlyStatsInfoListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildMonthlyStatsInfoListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildMonthlyStatsInfoListReq proto.InternalMessageInfo

func (m *GetGuildMonthlyStatsInfoListReq) GetBeginMonth() uint32 {
	if m != nil {
		return m.BeginMonth
	}
	return 0
}

func (m *GetGuildMonthlyStatsInfoListReq) GetEndMonth() uint32 {
	if m != nil {
		return m.EndMonth
	}
	return 0
}

func (m *GetGuildMonthlyStatsInfoListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildMonthlyStatsInfoListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetGuildMonthlyStatsInfoListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetGuildMonthlyStatsInfoListResp struct {
	GuildIdList          []uint32                 `protobuf:"varint,1,rep,packed,name=guild_id_list,json=guildIdList,proto3" json:"guild_id_list,omitempty"`
	InfoList             []*GuildMonthlyStatsInfo `protobuf:"bytes,2,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	TotalCnt             uint32                   `protobuf:"varint,3,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	NextPage             uint32                   `protobuf:"varint,4,opt,name=next_page,json=nextPage,proto3" json:"next_page,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetGuildMonthlyStatsInfoListResp) Reset()         { *m = GetGuildMonthlyStatsInfoListResp{} }
func (m *GetGuildMonthlyStatsInfoListResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildMonthlyStatsInfoListResp) ProtoMessage()    {}
func (*GetGuildMonthlyStatsInfoListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{24}
}
func (m *GetGuildMonthlyStatsInfoListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildMonthlyStatsInfoListResp.Unmarshal(m, b)
}
func (m *GetGuildMonthlyStatsInfoListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildMonthlyStatsInfoListResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildMonthlyStatsInfoListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildMonthlyStatsInfoListResp.Merge(dst, src)
}
func (m *GetGuildMonthlyStatsInfoListResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildMonthlyStatsInfoListResp.Size(m)
}
func (m *GetGuildMonthlyStatsInfoListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildMonthlyStatsInfoListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildMonthlyStatsInfoListResp proto.InternalMessageInfo

func (m *GetGuildMonthlyStatsInfoListResp) GetGuildIdList() []uint32 {
	if m != nil {
		return m.GuildIdList
	}
	return nil
}

func (m *GetGuildMonthlyStatsInfoListResp) GetInfoList() []*GuildMonthlyStatsInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetGuildMonthlyStatsInfoListResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

func (m *GetGuildMonthlyStatsInfoListResp) GetNextPage() uint32 {
	if m != nil {
		return m.NextPage
	}
	return 0
}

// /httpgo/multiplayer/getHomePageInfo 获取首页数据
type GetMultiPlayerHomepageReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ClientType           uint32   `protobuf:"varint,2,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	MarketId             uint32   `protobuf:"varint,3,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMultiPlayerHomepageReq) Reset()         { *m = GetMultiPlayerHomepageReq{} }
func (m *GetMultiPlayerHomepageReq) String() string { return proto.CompactTextString(m) }
func (*GetMultiPlayerHomepageReq) ProtoMessage()    {}
func (*GetMultiPlayerHomepageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{25}
}
func (m *GetMultiPlayerHomepageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMultiPlayerHomepageReq.Unmarshal(m, b)
}
func (m *GetMultiPlayerHomepageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMultiPlayerHomepageReq.Marshal(b, m, deterministic)
}
func (dst *GetMultiPlayerHomepageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMultiPlayerHomepageReq.Merge(dst, src)
}
func (m *GetMultiPlayerHomepageReq) XXX_Size() int {
	return xxx_messageInfo_GetMultiPlayerHomepageReq.Size(m)
}
func (m *GetMultiPlayerHomepageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMultiPlayerHomepageReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMultiPlayerHomepageReq proto.InternalMessageInfo

func (m *GetMultiPlayerHomepageReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetMultiPlayerHomepageReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *GetMultiPlayerHomepageReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

type GetMultiPlayerHomepageResp struct {
	Uid          uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account      string `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Nickname     string `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	GuildId      uint32 `protobuf:"varint,4,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GuildShortId uint32 `protobuf:"varint,5,opt,name=guild_short_id,json=guildShortId,proto3" json:"guild_short_id,omitempty"`
	GuildName    string `protobuf:"bytes,6,opt,name=guild_name,json=guildName,proto3" json:"guild_name,omitempty"`
	SignDays     uint32 `protobuf:"varint,7,opt,name=sign_days,json=signDays,proto3" json:"sign_days,omitempty"`
	FansCnt      uint32 `protobuf:"varint,8,opt,name=fans_cnt,json=fansCnt,proto3" json:"fans_cnt,omitempty"`
	FriendsCnt   uint32 `protobuf:"varint,10,opt,name=friends_cnt,json=friendsCnt,proto3" json:"friends_cnt,omitempty"`
	// 今日数据
	TodayInfo *MultiPlayerDailyInfo `protobuf:"bytes,11,opt,name=today_info,json=todayInfo,proto3" json:"today_info,omitempty"`
	// 本周
	WeekInfo *MultiPlayerWeekInfo `protobuf:"bytes,12,opt,name=week_info,json=weekInfo,proto3" json:"week_info,omitempty"`
	// 本月
	MonthInfo *MultiPlayerMonthInfo `protobuf:"bytes,13,opt,name=month_info,json=monthInfo,proto3" json:"month_info,omitempty"`
	// 公众号消息标题
	PublicMsg string `protobuf:"bytes,14,opt,name=public_msg,json=publicMsg,proto3" json:"public_msg,omitempty"`
	// 当月消费榜前三头像
	ConsumeTop3AccountList []string `protobuf:"bytes,15,rep,name=consume_top3_account_list,json=consumeTop3AccountList,proto3" json:"consume_top3_account_list,omitempty"`
	// 当月扩圈数据
	ThisMonthCommunityInfo *MultiPlayerMonthCommunityInfo `protobuf:"bytes,16,opt,name=this_month_community_info,json=thisMonthCommunityInfo,proto3" json:"this_month_community_info,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}                       `json:"-"`
	XXX_unrecognized       []byte                         `json:"-"`
	XXX_sizecache          int32                          `json:"-"`
}

func (m *GetMultiPlayerHomepageResp) Reset()         { *m = GetMultiPlayerHomepageResp{} }
func (m *GetMultiPlayerHomepageResp) String() string { return proto.CompactTextString(m) }
func (*GetMultiPlayerHomepageResp) ProtoMessage()    {}
func (*GetMultiPlayerHomepageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{26}
}
func (m *GetMultiPlayerHomepageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMultiPlayerHomepageResp.Unmarshal(m, b)
}
func (m *GetMultiPlayerHomepageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMultiPlayerHomepageResp.Marshal(b, m, deterministic)
}
func (dst *GetMultiPlayerHomepageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMultiPlayerHomepageResp.Merge(dst, src)
}
func (m *GetMultiPlayerHomepageResp) XXX_Size() int {
	return xxx_messageInfo_GetMultiPlayerHomepageResp.Size(m)
}
func (m *GetMultiPlayerHomepageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMultiPlayerHomepageResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMultiPlayerHomepageResp proto.InternalMessageInfo

func (m *GetMultiPlayerHomepageResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetMultiPlayerHomepageResp) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *GetMultiPlayerHomepageResp) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *GetMultiPlayerHomepageResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetMultiPlayerHomepageResp) GetGuildShortId() uint32 {
	if m != nil {
		return m.GuildShortId
	}
	return 0
}

func (m *GetMultiPlayerHomepageResp) GetGuildName() string {
	if m != nil {
		return m.GuildName
	}
	return ""
}

func (m *GetMultiPlayerHomepageResp) GetSignDays() uint32 {
	if m != nil {
		return m.SignDays
	}
	return 0
}

func (m *GetMultiPlayerHomepageResp) GetFansCnt() uint32 {
	if m != nil {
		return m.FansCnt
	}
	return 0
}

func (m *GetMultiPlayerHomepageResp) GetFriendsCnt() uint32 {
	if m != nil {
		return m.FriendsCnt
	}
	return 0
}

func (m *GetMultiPlayerHomepageResp) GetTodayInfo() *MultiPlayerDailyInfo {
	if m != nil {
		return m.TodayInfo
	}
	return nil
}

func (m *GetMultiPlayerHomepageResp) GetWeekInfo() *MultiPlayerWeekInfo {
	if m != nil {
		return m.WeekInfo
	}
	return nil
}

func (m *GetMultiPlayerHomepageResp) GetMonthInfo() *MultiPlayerMonthInfo {
	if m != nil {
		return m.MonthInfo
	}
	return nil
}

func (m *GetMultiPlayerHomepageResp) GetPublicMsg() string {
	if m != nil {
		return m.PublicMsg
	}
	return ""
}

func (m *GetMultiPlayerHomepageResp) GetConsumeTop3AccountList() []string {
	if m != nil {
		return m.ConsumeTop3AccountList
	}
	return nil
}

func (m *GetMultiPlayerHomepageResp) GetThisMonthCommunityInfo() *MultiPlayerMonthCommunityInfo {
	if m != nil {
		return m.ThisMonthCommunityInfo
	}
	return nil
}

type MultiPlayerMonthCommunityInfo struct {
	Date                  string `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	MonthNewRelation      uint32 `protobuf:"varint,2,opt,name=month_new_relation,json=monthNewRelation,proto3" json:"month_new_relation,omitempty"`
	MonthNewValidRelation uint32 `protobuf:"varint,3,opt,name=month_new_valid_relation,json=monthNewValidRelation,proto3" json:"month_new_valid_relation,omitempty"`
	MonthFocusTotal       uint32 `protobuf:"varint,4,opt,name=month_focus_total,json=monthFocusTotal,proto3" json:"month_focus_total,omitempty"`
	MonthNewFocusCnt      uint32 `protobuf:"varint,5,opt,name=month_new_focus_cnt,json=monthNewFocusCnt,proto3" json:"month_new_focus_cnt,omitempty"`
	MonthLossFriendCnt    uint32 `protobuf:"varint,6,opt,name=month_loss_friend_cnt,json=monthLossFriendCnt,proto3" json:"month_loss_friend_cnt,omitempty"`
	MonthLossFocusCnt     uint32 `protobuf:"varint,7,opt,name=month_loss_focus_cnt,json=monthLossFocusCnt,proto3" json:"month_loss_focus_cnt,omitempty"`
	// 月环比，有正负
	MonthNewRelationGrowrate      float32  `protobuf:"fixed32,8,opt,name=month_new_relation_growrate,json=monthNewRelationGrowrate,proto3" json:"month_new_relation_growrate,omitempty"`
	MonthNewValidRelationGrowrate float32  `protobuf:"fixed32,9,opt,name=month_new_valid_relation_growrate,json=monthNewValidRelationGrowrate,proto3" json:"month_new_valid_relation_growrate,omitempty"`
	MonthFocusTotalGrowrate       float32  `protobuf:"fixed32,10,opt,name=month_focus_total_growrate,json=monthFocusTotalGrowrate,proto3" json:"month_focus_total_growrate,omitempty"`
	MonthNewFocusCntGrowrate      float32  `protobuf:"fixed32,11,opt,name=month_new_focus_cnt_growrate,json=monthNewFocusCntGrowrate,proto3" json:"month_new_focus_cnt_growrate,omitempty"`
	MonthLossFriendCntGrowrate    float32  `protobuf:"fixed32,12,opt,name=month_loss_friend_cnt_growrate,json=monthLossFriendCntGrowrate,proto3" json:"month_loss_friend_cnt_growrate,omitempty"`
	MonthLossFocusCntGrowrate     float32  `protobuf:"fixed32,13,opt,name=month_loss_focus_cnt_growrate,json=monthLossFocusCntGrowrate,proto3" json:"month_loss_focus_cnt_growrate,omitempty"`
	XXX_NoUnkeyedLiteral          struct{} `json:"-"`
	XXX_unrecognized              []byte   `json:"-"`
	XXX_sizecache                 int32    `json:"-"`
}

func (m *MultiPlayerMonthCommunityInfo) Reset()         { *m = MultiPlayerMonthCommunityInfo{} }
func (m *MultiPlayerMonthCommunityInfo) String() string { return proto.CompactTextString(m) }
func (*MultiPlayerMonthCommunityInfo) ProtoMessage()    {}
func (*MultiPlayerMonthCommunityInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{27}
}
func (m *MultiPlayerMonthCommunityInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MultiPlayerMonthCommunityInfo.Unmarshal(m, b)
}
func (m *MultiPlayerMonthCommunityInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MultiPlayerMonthCommunityInfo.Marshal(b, m, deterministic)
}
func (dst *MultiPlayerMonthCommunityInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultiPlayerMonthCommunityInfo.Merge(dst, src)
}
func (m *MultiPlayerMonthCommunityInfo) XXX_Size() int {
	return xxx_messageInfo_MultiPlayerMonthCommunityInfo.Size(m)
}
func (m *MultiPlayerMonthCommunityInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MultiPlayerMonthCommunityInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MultiPlayerMonthCommunityInfo proto.InternalMessageInfo

func (m *MultiPlayerMonthCommunityInfo) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

func (m *MultiPlayerMonthCommunityInfo) GetMonthNewRelation() uint32 {
	if m != nil {
		return m.MonthNewRelation
	}
	return 0
}

func (m *MultiPlayerMonthCommunityInfo) GetMonthNewValidRelation() uint32 {
	if m != nil {
		return m.MonthNewValidRelation
	}
	return 0
}

func (m *MultiPlayerMonthCommunityInfo) GetMonthFocusTotal() uint32 {
	if m != nil {
		return m.MonthFocusTotal
	}
	return 0
}

func (m *MultiPlayerMonthCommunityInfo) GetMonthNewFocusCnt() uint32 {
	if m != nil {
		return m.MonthNewFocusCnt
	}
	return 0
}

func (m *MultiPlayerMonthCommunityInfo) GetMonthLossFriendCnt() uint32 {
	if m != nil {
		return m.MonthLossFriendCnt
	}
	return 0
}

func (m *MultiPlayerMonthCommunityInfo) GetMonthLossFocusCnt() uint32 {
	if m != nil {
		return m.MonthLossFocusCnt
	}
	return 0
}

func (m *MultiPlayerMonthCommunityInfo) GetMonthNewRelationGrowrate() float32 {
	if m != nil {
		return m.MonthNewRelationGrowrate
	}
	return 0
}

func (m *MultiPlayerMonthCommunityInfo) GetMonthNewValidRelationGrowrate() float32 {
	if m != nil {
		return m.MonthNewValidRelationGrowrate
	}
	return 0
}

func (m *MultiPlayerMonthCommunityInfo) GetMonthFocusTotalGrowrate() float32 {
	if m != nil {
		return m.MonthFocusTotalGrowrate
	}
	return 0
}

func (m *MultiPlayerMonthCommunityInfo) GetMonthNewFocusCntGrowrate() float32 {
	if m != nil {
		return m.MonthNewFocusCntGrowrate
	}
	return 0
}

func (m *MultiPlayerMonthCommunityInfo) GetMonthLossFriendCntGrowrate() float32 {
	if m != nil {
		return m.MonthLossFriendCntGrowrate
	}
	return 0
}

func (m *MultiPlayerMonthCommunityInfo) GetMonthLossFocusCntGrowrate() float32 {
	if m != nil {
		return m.MonthLossFocusCntGrowrate
	}
	return 0
}

type MultiPlayerDailyInfo struct {
	Date                    string   `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	Fee                     string   `protobuf:"bytes,2,opt,name=fee,proto3" json:"fee,omitempty"`
	SendpresentUidsCnt      uint32   `protobuf:"varint,3,opt,name=sendpresent_uids_cnt,json=sendpresentUidsCnt,proto3" json:"sendpresent_uids_cnt,omitempty"`
	FirstSendpresentUidsCnt uint32   `protobuf:"varint,4,opt,name=first_sendpresent_uids_cnt,json=firstSendpresentUidsCnt,proto3" json:"first_sendpresent_uids_cnt,omitempty"`
	HoldHour                string   `protobuf:"bytes,5,opt,name=hold_hour,json=holdHour,proto3" json:"hold_hour,omitempty"`
	ActiveHour              string   `protobuf:"bytes,6,opt,name=active_hour,json=activeHour,proto3" json:"active_hour,omitempty"`
	NewRelation             uint32   `protobuf:"varint,7,opt,name=new_relation,json=newRelation,proto3" json:"new_relation,omitempty"`
	XXX_NoUnkeyedLiteral    struct{} `json:"-"`
	XXX_unrecognized        []byte   `json:"-"`
	XXX_sizecache           int32    `json:"-"`
}

func (m *MultiPlayerDailyInfo) Reset()         { *m = MultiPlayerDailyInfo{} }
func (m *MultiPlayerDailyInfo) String() string { return proto.CompactTextString(m) }
func (*MultiPlayerDailyInfo) ProtoMessage()    {}
func (*MultiPlayerDailyInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{28}
}
func (m *MultiPlayerDailyInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MultiPlayerDailyInfo.Unmarshal(m, b)
}
func (m *MultiPlayerDailyInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MultiPlayerDailyInfo.Marshal(b, m, deterministic)
}
func (dst *MultiPlayerDailyInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultiPlayerDailyInfo.Merge(dst, src)
}
func (m *MultiPlayerDailyInfo) XXX_Size() int {
	return xxx_messageInfo_MultiPlayerDailyInfo.Size(m)
}
func (m *MultiPlayerDailyInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MultiPlayerDailyInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MultiPlayerDailyInfo proto.InternalMessageInfo

func (m *MultiPlayerDailyInfo) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

func (m *MultiPlayerDailyInfo) GetFee() string {
	if m != nil {
		return m.Fee
	}
	return ""
}

func (m *MultiPlayerDailyInfo) GetSendpresentUidsCnt() uint32 {
	if m != nil {
		return m.SendpresentUidsCnt
	}
	return 0
}

func (m *MultiPlayerDailyInfo) GetFirstSendpresentUidsCnt() uint32 {
	if m != nil {
		return m.FirstSendpresentUidsCnt
	}
	return 0
}

func (m *MultiPlayerDailyInfo) GetHoldHour() string {
	if m != nil {
		return m.HoldHour
	}
	return ""
}

func (m *MultiPlayerDailyInfo) GetActiveHour() string {
	if m != nil {
		return m.ActiveHour
	}
	return ""
}

func (m *MultiPlayerDailyInfo) GetNewRelation() uint32 {
	if m != nil {
		return m.NewRelation
	}
	return 0
}

type MultiPlayerWeekInfo struct {
	Date                    string   `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	Fee                     string   `protobuf:"bytes,2,opt,name=fee,proto3" json:"fee,omitempty"`
	SendpresentUidsCnt      uint32   `protobuf:"varint,3,opt,name=sendpresent_uids_cnt,json=sendpresentUidsCnt,proto3" json:"sendpresent_uids_cnt,omitempty"`
	FirstSendpresentUidsCnt uint32   `protobuf:"varint,4,opt,name=first_sendpresent_uids_cnt,json=firstSendpresentUidsCnt,proto3" json:"first_sendpresent_uids_cnt,omitempty"`
	HoldDaysCnt             uint32   `protobuf:"varint,5,opt,name=hold_days_cnt,json=holdDaysCnt,proto3" json:"hold_days_cnt,omitempty"`
	ActiveDaysCnt           uint32   `protobuf:"varint,6,opt,name=active_days_cnt,json=activeDaysCnt,proto3" json:"active_days_cnt,omitempty"`
	NewRelation             uint32   `protobuf:"varint,7,opt,name=new_relation,json=newRelation,proto3" json:"new_relation,omitempty"`
	NewValidRelation        uint32   `protobuf:"varint,8,opt,name=new_valid_relation,json=newValidRelation,proto3" json:"new_valid_relation,omitempty"`
	XXX_NoUnkeyedLiteral    struct{} `json:"-"`
	XXX_unrecognized        []byte   `json:"-"`
	XXX_sizecache           int32    `json:"-"`
}

func (m *MultiPlayerWeekInfo) Reset()         { *m = MultiPlayerWeekInfo{} }
func (m *MultiPlayerWeekInfo) String() string { return proto.CompactTextString(m) }
func (*MultiPlayerWeekInfo) ProtoMessage()    {}
func (*MultiPlayerWeekInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{29}
}
func (m *MultiPlayerWeekInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MultiPlayerWeekInfo.Unmarshal(m, b)
}
func (m *MultiPlayerWeekInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MultiPlayerWeekInfo.Marshal(b, m, deterministic)
}
func (dst *MultiPlayerWeekInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultiPlayerWeekInfo.Merge(dst, src)
}
func (m *MultiPlayerWeekInfo) XXX_Size() int {
	return xxx_messageInfo_MultiPlayerWeekInfo.Size(m)
}
func (m *MultiPlayerWeekInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MultiPlayerWeekInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MultiPlayerWeekInfo proto.InternalMessageInfo

func (m *MultiPlayerWeekInfo) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

func (m *MultiPlayerWeekInfo) GetFee() string {
	if m != nil {
		return m.Fee
	}
	return ""
}

func (m *MultiPlayerWeekInfo) GetSendpresentUidsCnt() uint32 {
	if m != nil {
		return m.SendpresentUidsCnt
	}
	return 0
}

func (m *MultiPlayerWeekInfo) GetFirstSendpresentUidsCnt() uint32 {
	if m != nil {
		return m.FirstSendpresentUidsCnt
	}
	return 0
}

func (m *MultiPlayerWeekInfo) GetHoldDaysCnt() uint32 {
	if m != nil {
		return m.HoldDaysCnt
	}
	return 0
}

func (m *MultiPlayerWeekInfo) GetActiveDaysCnt() uint32 {
	if m != nil {
		return m.ActiveDaysCnt
	}
	return 0
}

func (m *MultiPlayerWeekInfo) GetNewRelation() uint32 {
	if m != nil {
		return m.NewRelation
	}
	return 0
}

func (m *MultiPlayerWeekInfo) GetNewValidRelation() uint32 {
	if m != nil {
		return m.NewValidRelation
	}
	return 0
}

type MultiPlayerMonthInfo struct {
	Date                    string   `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	Fee                     string   `protobuf:"bytes,2,opt,name=fee,proto3" json:"fee,omitempty"`
	SendpresentUidsCnt      uint32   `protobuf:"varint,3,opt,name=sendpresent_uids_cnt,json=sendpresentUidsCnt,proto3" json:"sendpresent_uids_cnt,omitempty"`
	FirstSendpresentUidsCnt uint32   `protobuf:"varint,4,opt,name=first_sendpresent_uids_cnt,json=firstSendpresentUidsCnt,proto3" json:"first_sendpresent_uids_cnt,omitempty"`
	HoldDaysCnt             uint32   `protobuf:"varint,5,opt,name=hold_days_cnt,json=holdDaysCnt,proto3" json:"hold_days_cnt,omitempty"`
	ActiveDaysCnt           uint32   `protobuf:"varint,6,opt,name=active_days_cnt,json=activeDaysCnt,proto3" json:"active_days_cnt,omitempty"`
	NewRelation             uint32   `protobuf:"varint,7,opt,name=new_relation,json=newRelation,proto3" json:"new_relation,omitempty"`
	NewValidRelation        uint32   `protobuf:"varint,8,opt,name=new_valid_relation,json=newValidRelation,proto3" json:"new_valid_relation,omitempty"`
	ViolationTotalCnt       uint32   `protobuf:"varint,9,opt,name=violation_total_cnt,json=violationTotalCnt,proto3" json:"violation_total_cnt,omitempty"`
	Violation_ACnt          uint32   `protobuf:"varint,10,opt,name=violation_A_cnt,json=violationACnt,proto3" json:"violation_A_cnt,omitempty"`
	Violation_BCnt          uint32   `protobuf:"varint,11,opt,name=violation_B_cnt,json=violationBCnt,proto3" json:"violation_B_cnt,omitempty"`
	Violation_CCnt          uint32   `protobuf:"varint,12,opt,name=violation_C_cnt,json=violationCCnt,proto3" json:"violation_C_cnt,omitempty"`
	XXX_NoUnkeyedLiteral    struct{} `json:"-"`
	XXX_unrecognized        []byte   `json:"-"`
	XXX_sizecache           int32    `json:"-"`
}

func (m *MultiPlayerMonthInfo) Reset()         { *m = MultiPlayerMonthInfo{} }
func (m *MultiPlayerMonthInfo) String() string { return proto.CompactTextString(m) }
func (*MultiPlayerMonthInfo) ProtoMessage()    {}
func (*MultiPlayerMonthInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{30}
}
func (m *MultiPlayerMonthInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MultiPlayerMonthInfo.Unmarshal(m, b)
}
func (m *MultiPlayerMonthInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MultiPlayerMonthInfo.Marshal(b, m, deterministic)
}
func (dst *MultiPlayerMonthInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultiPlayerMonthInfo.Merge(dst, src)
}
func (m *MultiPlayerMonthInfo) XXX_Size() int {
	return xxx_messageInfo_MultiPlayerMonthInfo.Size(m)
}
func (m *MultiPlayerMonthInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MultiPlayerMonthInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MultiPlayerMonthInfo proto.InternalMessageInfo

func (m *MultiPlayerMonthInfo) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

func (m *MultiPlayerMonthInfo) GetFee() string {
	if m != nil {
		return m.Fee
	}
	return ""
}

func (m *MultiPlayerMonthInfo) GetSendpresentUidsCnt() uint32 {
	if m != nil {
		return m.SendpresentUidsCnt
	}
	return 0
}

func (m *MultiPlayerMonthInfo) GetFirstSendpresentUidsCnt() uint32 {
	if m != nil {
		return m.FirstSendpresentUidsCnt
	}
	return 0
}

func (m *MultiPlayerMonthInfo) GetHoldDaysCnt() uint32 {
	if m != nil {
		return m.HoldDaysCnt
	}
	return 0
}

func (m *MultiPlayerMonthInfo) GetActiveDaysCnt() uint32 {
	if m != nil {
		return m.ActiveDaysCnt
	}
	return 0
}

func (m *MultiPlayerMonthInfo) GetNewRelation() uint32 {
	if m != nil {
		return m.NewRelation
	}
	return 0
}

func (m *MultiPlayerMonthInfo) GetNewValidRelation() uint32 {
	if m != nil {
		return m.NewValidRelation
	}
	return 0
}

func (m *MultiPlayerMonthInfo) GetViolationTotalCnt() uint32 {
	if m != nil {
		return m.ViolationTotalCnt
	}
	return 0
}

func (m *MultiPlayerMonthInfo) GetViolation_ACnt() uint32 {
	if m != nil {
		return m.Violation_ACnt
	}
	return 0
}

func (m *MultiPlayerMonthInfo) GetViolation_BCnt() uint32 {
	if m != nil {
		return m.Violation_BCnt
	}
	return 0
}

func (m *MultiPlayerMonthInfo) GetViolation_CCnt() uint32 {
	if m != nil {
		return m.Violation_CCnt
	}
	return 0
}

type MultiPlayerMonthDetialInfo struct {
	Date                    string `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	Fee                     string `protobuf:"bytes,2,opt,name=fee,proto3" json:"fee,omitempty"`
	SendpresentUidsCnt      uint32 `protobuf:"varint,3,opt,name=sendpresent_uids_cnt,json=sendpresentUidsCnt,proto3" json:"sendpresent_uids_cnt,omitempty"`
	FirstSendpresentUidsCnt uint32 `protobuf:"varint,4,opt,name=first_sendpresent_uids_cnt,json=firstSendpresentUidsCnt,proto3" json:"first_sendpresent_uids_cnt,omitempty"`
	HoldDaysCnt             uint32 `protobuf:"varint,5,opt,name=hold_days_cnt,json=holdDaysCnt,proto3" json:"hold_days_cnt,omitempty"`
	ActiveDaysCnt           uint32 `protobuf:"varint,6,opt,name=active_days_cnt,json=activeDaysCnt,proto3" json:"active_days_cnt,omitempty"`
	ViolationTotalCnt       uint32 `protobuf:"varint,7,opt,name=violation_total_cnt,json=violationTotalCnt,proto3" json:"violation_total_cnt,omitempty"`
	Violation_ACnt          uint32 `protobuf:"varint,8,opt,name=violation_A_cnt,json=violationACnt,proto3" json:"violation_A_cnt,omitempty"`
	Violation_BCnt          uint32 `protobuf:"varint,9,opt,name=violation_B_cnt,json=violationBCnt,proto3" json:"violation_B_cnt,omitempty"`
	Violation_CCnt          uint32 `protobuf:"varint,10,opt,name=violation_C_cnt,json=violationCCnt,proto3" json:"violation_C_cnt,omitempty"`
	// 月环比，有正负
	MonthFeeGrowrate                     float32  `protobuf:"fixed32,11,opt,name=month_fee_growrate,json=monthFeeGrowrate,proto3" json:"month_fee_growrate,omitempty"`
	MonthSendpresentUidsCntGrowrate      float32  `protobuf:"fixed32,12,opt,name=month_sendpresent_uids_cnt_growrate,json=monthSendpresentUidsCntGrowrate,proto3" json:"month_sendpresent_uids_cnt_growrate,omitempty"`
	MonthFirstSendpresentUidsCntGrowrate float32  `protobuf:"fixed32,13,opt,name=month_first_sendpresent_uids_cnt_growrate,json=monthFirstSendpresentUidsCntGrowrate,proto3" json:"month_first_sendpresent_uids_cnt_growrate,omitempty"`
	MonthHoldDaysCntGrowrate             float32  `protobuf:"fixed32,14,opt,name=month_hold_days_cnt_growrate,json=monthHoldDaysCntGrowrate,proto3" json:"month_hold_days_cnt_growrate,omitempty"`
	MonthActiveDaysCntGrowrate           float32  `protobuf:"fixed32,15,opt,name=month_active_days_cnt_growrate,json=monthActiveDaysCntGrowrate,proto3" json:"month_active_days_cnt_growrate,omitempty"`
	MonthViolationTotalCntGrowrate       float32  `protobuf:"fixed32,16,opt,name=month_violation_total_cnt_growrate,json=monthViolationTotalCntGrowrate,proto3" json:"month_violation_total_cnt_growrate,omitempty"`
	XXX_NoUnkeyedLiteral                 struct{} `json:"-"`
	XXX_unrecognized                     []byte   `json:"-"`
	XXX_sizecache                        int32    `json:"-"`
}

func (m *MultiPlayerMonthDetialInfo) Reset()         { *m = MultiPlayerMonthDetialInfo{} }
func (m *MultiPlayerMonthDetialInfo) String() string { return proto.CompactTextString(m) }
func (*MultiPlayerMonthDetialInfo) ProtoMessage()    {}
func (*MultiPlayerMonthDetialInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{31}
}
func (m *MultiPlayerMonthDetialInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MultiPlayerMonthDetialInfo.Unmarshal(m, b)
}
func (m *MultiPlayerMonthDetialInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MultiPlayerMonthDetialInfo.Marshal(b, m, deterministic)
}
func (dst *MultiPlayerMonthDetialInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultiPlayerMonthDetialInfo.Merge(dst, src)
}
func (m *MultiPlayerMonthDetialInfo) XXX_Size() int {
	return xxx_messageInfo_MultiPlayerMonthDetialInfo.Size(m)
}
func (m *MultiPlayerMonthDetialInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MultiPlayerMonthDetialInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MultiPlayerMonthDetialInfo proto.InternalMessageInfo

func (m *MultiPlayerMonthDetialInfo) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

func (m *MultiPlayerMonthDetialInfo) GetFee() string {
	if m != nil {
		return m.Fee
	}
	return ""
}

func (m *MultiPlayerMonthDetialInfo) GetSendpresentUidsCnt() uint32 {
	if m != nil {
		return m.SendpresentUidsCnt
	}
	return 0
}

func (m *MultiPlayerMonthDetialInfo) GetFirstSendpresentUidsCnt() uint32 {
	if m != nil {
		return m.FirstSendpresentUidsCnt
	}
	return 0
}

func (m *MultiPlayerMonthDetialInfo) GetHoldDaysCnt() uint32 {
	if m != nil {
		return m.HoldDaysCnt
	}
	return 0
}

func (m *MultiPlayerMonthDetialInfo) GetActiveDaysCnt() uint32 {
	if m != nil {
		return m.ActiveDaysCnt
	}
	return 0
}

func (m *MultiPlayerMonthDetialInfo) GetViolationTotalCnt() uint32 {
	if m != nil {
		return m.ViolationTotalCnt
	}
	return 0
}

func (m *MultiPlayerMonthDetialInfo) GetViolation_ACnt() uint32 {
	if m != nil {
		return m.Violation_ACnt
	}
	return 0
}

func (m *MultiPlayerMonthDetialInfo) GetViolation_BCnt() uint32 {
	if m != nil {
		return m.Violation_BCnt
	}
	return 0
}

func (m *MultiPlayerMonthDetialInfo) GetViolation_CCnt() uint32 {
	if m != nil {
		return m.Violation_CCnt
	}
	return 0
}

func (m *MultiPlayerMonthDetialInfo) GetMonthFeeGrowrate() float32 {
	if m != nil {
		return m.MonthFeeGrowrate
	}
	return 0
}

func (m *MultiPlayerMonthDetialInfo) GetMonthSendpresentUidsCntGrowrate() float32 {
	if m != nil {
		return m.MonthSendpresentUidsCntGrowrate
	}
	return 0
}

func (m *MultiPlayerMonthDetialInfo) GetMonthFirstSendpresentUidsCntGrowrate() float32 {
	if m != nil {
		return m.MonthFirstSendpresentUidsCntGrowrate
	}
	return 0
}

func (m *MultiPlayerMonthDetialInfo) GetMonthHoldDaysCntGrowrate() float32 {
	if m != nil {
		return m.MonthHoldDaysCntGrowrate
	}
	return 0
}

func (m *MultiPlayerMonthDetialInfo) GetMonthActiveDaysCntGrowrate() float32 {
	if m != nil {
		return m.MonthActiveDaysCntGrowrate
	}
	return 0
}

func (m *MultiPlayerMonthDetialInfo) GetMonthViolationTotalCntGrowrate() float32 {
	if m != nil {
		return m.MonthViolationTotalCntGrowrate
	}
	return 0
}

// /httpgo/multiplayer/getBaseInfo 获取日，周，月数据详情
type GetMultiPlayerBaseInfoReq struct {
	Type                 uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMultiPlayerBaseInfoReq) Reset()         { *m = GetMultiPlayerBaseInfoReq{} }
func (m *GetMultiPlayerBaseInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetMultiPlayerBaseInfoReq) ProtoMessage()    {}
func (*GetMultiPlayerBaseInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{32}
}
func (m *GetMultiPlayerBaseInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMultiPlayerBaseInfoReq.Unmarshal(m, b)
}
func (m *GetMultiPlayerBaseInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMultiPlayerBaseInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetMultiPlayerBaseInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMultiPlayerBaseInfoReq.Merge(dst, src)
}
func (m *GetMultiPlayerBaseInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetMultiPlayerBaseInfoReq.Size(m)
}
func (m *GetMultiPlayerBaseInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMultiPlayerBaseInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMultiPlayerBaseInfoReq proto.InternalMessageInfo

func (m *GetMultiPlayerBaseInfoReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GetMultiPlayerBaseInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetMultiPlayerBaseInfoResp struct {
	DailyInfoList        []*MultiPlayerDailyInfo       `protobuf:"bytes,1,rep,name=daily_info_list,json=dailyInfoList,proto3" json:"daily_info_list,omitempty"`
	WeekInfoList         []*MultiPlayerDailyInfo       `protobuf:"bytes,2,rep,name=week_info_list,json=weekInfoList,proto3" json:"week_info_list,omitempty"`
	MonthInfoList        []*MultiPlayerMonthDetialInfo `protobuf:"bytes,3,rep,name=month_info_list,json=monthInfoList,proto3" json:"month_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetMultiPlayerBaseInfoResp) Reset()         { *m = GetMultiPlayerBaseInfoResp{} }
func (m *GetMultiPlayerBaseInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetMultiPlayerBaseInfoResp) ProtoMessage()    {}
func (*GetMultiPlayerBaseInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{33}
}
func (m *GetMultiPlayerBaseInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMultiPlayerBaseInfoResp.Unmarshal(m, b)
}
func (m *GetMultiPlayerBaseInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMultiPlayerBaseInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetMultiPlayerBaseInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMultiPlayerBaseInfoResp.Merge(dst, src)
}
func (m *GetMultiPlayerBaseInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetMultiPlayerBaseInfoResp.Size(m)
}
func (m *GetMultiPlayerBaseInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMultiPlayerBaseInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMultiPlayerBaseInfoResp proto.InternalMessageInfo

func (m *GetMultiPlayerBaseInfoResp) GetDailyInfoList() []*MultiPlayerDailyInfo {
	if m != nil {
		return m.DailyInfoList
	}
	return nil
}

func (m *GetMultiPlayerBaseInfoResp) GetWeekInfoList() []*MultiPlayerDailyInfo {
	if m != nil {
		return m.WeekInfoList
	}
	return nil
}

func (m *GetMultiPlayerBaseInfoResp) GetMonthInfoList() []*MultiPlayerMonthDetialInfo {
	if m != nil {
		return m.MonthInfoList
	}
	return nil
}

// /httpgo/multiplayer/getMonthConsumeTop10 获取月消费用户榜
type GetMultiPlayerMonthConsumeTop10Req struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMultiPlayerMonthConsumeTop10Req) Reset()         { *m = GetMultiPlayerMonthConsumeTop10Req{} }
func (m *GetMultiPlayerMonthConsumeTop10Req) String() string { return proto.CompactTextString(m) }
func (*GetMultiPlayerMonthConsumeTop10Req) ProtoMessage()    {}
func (*GetMultiPlayerMonthConsumeTop10Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{34}
}
func (m *GetMultiPlayerMonthConsumeTop10Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMultiPlayerMonthConsumeTop10Req.Unmarshal(m, b)
}
func (m *GetMultiPlayerMonthConsumeTop10Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMultiPlayerMonthConsumeTop10Req.Marshal(b, m, deterministic)
}
func (dst *GetMultiPlayerMonthConsumeTop10Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMultiPlayerMonthConsumeTop10Req.Merge(dst, src)
}
func (m *GetMultiPlayerMonthConsumeTop10Req) XXX_Size() int {
	return xxx_messageInfo_GetMultiPlayerMonthConsumeTop10Req.Size(m)
}
func (m *GetMultiPlayerMonthConsumeTop10Req) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMultiPlayerMonthConsumeTop10Req.DiscardUnknown(m)
}

var xxx_messageInfo_GetMultiPlayerMonthConsumeTop10Req proto.InternalMessageInfo

func (m *GetMultiPlayerMonthConsumeTop10Req) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetMultiPlayerMonthConsumeTop10Resp struct {
	ThisMonthConsumeTop10 []*GetMultiPlayerMonthConsumeTop10RespComsumer `protobuf:"bytes,1,rep,name=this_month_consume_top10,json=thisMonthConsumeTop10,proto3" json:"this_month_consume_top10,omitempty"`
	LastMonthConsumeTop10 []*GetMultiPlayerMonthConsumeTop10RespComsumer `protobuf:"bytes,2,rep,name=last_month_consume_top10,json=lastMonthConsumeTop10,proto3" json:"last_month_consume_top10,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}                                       `json:"-"`
	XXX_unrecognized      []byte                                         `json:"-"`
	XXX_sizecache         int32                                          `json:"-"`
}

func (m *GetMultiPlayerMonthConsumeTop10Resp) Reset()         { *m = GetMultiPlayerMonthConsumeTop10Resp{} }
func (m *GetMultiPlayerMonthConsumeTop10Resp) String() string { return proto.CompactTextString(m) }
func (*GetMultiPlayerMonthConsumeTop10Resp) ProtoMessage()    {}
func (*GetMultiPlayerMonthConsumeTop10Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{35}
}
func (m *GetMultiPlayerMonthConsumeTop10Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMultiPlayerMonthConsumeTop10Resp.Unmarshal(m, b)
}
func (m *GetMultiPlayerMonthConsumeTop10Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMultiPlayerMonthConsumeTop10Resp.Marshal(b, m, deterministic)
}
func (dst *GetMultiPlayerMonthConsumeTop10Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMultiPlayerMonthConsumeTop10Resp.Merge(dst, src)
}
func (m *GetMultiPlayerMonthConsumeTop10Resp) XXX_Size() int {
	return xxx_messageInfo_GetMultiPlayerMonthConsumeTop10Resp.Size(m)
}
func (m *GetMultiPlayerMonthConsumeTop10Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMultiPlayerMonthConsumeTop10Resp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMultiPlayerMonthConsumeTop10Resp proto.InternalMessageInfo

func (m *GetMultiPlayerMonthConsumeTop10Resp) GetThisMonthConsumeTop10() []*GetMultiPlayerMonthConsumeTop10RespComsumer {
	if m != nil {
		return m.ThisMonthConsumeTop10
	}
	return nil
}

func (m *GetMultiPlayerMonthConsumeTop10Resp) GetLastMonthConsumeTop10() []*GetMultiPlayerMonthConsumeTop10RespComsumer {
	if m != nil {
		return m.LastMonthConsumeTop10
	}
	return nil
}

type GetMultiPlayerMonthConsumeTop10RespComsumer struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	MonthConsumeFee      uint64   `protobuf:"varint,4,opt,name=month_consume_fee,json=monthConsumeFee,proto3" json:"month_consume_fee,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMultiPlayerMonthConsumeTop10RespComsumer) Reset() {
	*m = GetMultiPlayerMonthConsumeTop10RespComsumer{}
}
func (m *GetMultiPlayerMonthConsumeTop10RespComsumer) String() string {
	return proto.CompactTextString(m)
}
func (*GetMultiPlayerMonthConsumeTop10RespComsumer) ProtoMessage() {}
func (*GetMultiPlayerMonthConsumeTop10RespComsumer) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{35, 0}
}
func (m *GetMultiPlayerMonthConsumeTop10RespComsumer) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMultiPlayerMonthConsumeTop10RespComsumer.Unmarshal(m, b)
}
func (m *GetMultiPlayerMonthConsumeTop10RespComsumer) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMultiPlayerMonthConsumeTop10RespComsumer.Marshal(b, m, deterministic)
}
func (dst *GetMultiPlayerMonthConsumeTop10RespComsumer) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMultiPlayerMonthConsumeTop10RespComsumer.Merge(dst, src)
}
func (m *GetMultiPlayerMonthConsumeTop10RespComsumer) XXX_Size() int {
	return xxx_messageInfo_GetMultiPlayerMonthConsumeTop10RespComsumer.Size(m)
}
func (m *GetMultiPlayerMonthConsumeTop10RespComsumer) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMultiPlayerMonthConsumeTop10RespComsumer.DiscardUnknown(m)
}

var xxx_messageInfo_GetMultiPlayerMonthConsumeTop10RespComsumer proto.InternalMessageInfo

func (m *GetMultiPlayerMonthConsumeTop10RespComsumer) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetMultiPlayerMonthConsumeTop10RespComsumer) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *GetMultiPlayerMonthConsumeTop10RespComsumer) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *GetMultiPlayerMonthConsumeTop10RespComsumer) GetMonthConsumeFee() uint64 {
	if m != nil {
		return m.MonthConsumeFee
	}
	return 0
}

// /httpgo/multiplayer/getMonthCommunityInfo 获取月扩圈数据详情
type GetMultiPlayerMonthCommunityInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMultiPlayerMonthCommunityInfoReq) Reset()         { *m = GetMultiPlayerMonthCommunityInfoReq{} }
func (m *GetMultiPlayerMonthCommunityInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetMultiPlayerMonthCommunityInfoReq) ProtoMessage()    {}
func (*GetMultiPlayerMonthCommunityInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{36}
}
func (m *GetMultiPlayerMonthCommunityInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMultiPlayerMonthCommunityInfoReq.Unmarshal(m, b)
}
func (m *GetMultiPlayerMonthCommunityInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMultiPlayerMonthCommunityInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetMultiPlayerMonthCommunityInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMultiPlayerMonthCommunityInfoReq.Merge(dst, src)
}
func (m *GetMultiPlayerMonthCommunityInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetMultiPlayerMonthCommunityInfoReq.Size(m)
}
func (m *GetMultiPlayerMonthCommunityInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMultiPlayerMonthCommunityInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMultiPlayerMonthCommunityInfoReq proto.InternalMessageInfo

func (m *GetMultiPlayerMonthCommunityInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetMultiPlayerMonthCommunityInfoResp struct {
	ThisMonthCommunityInfo *MultiPlayerMonthCommunityInfo `protobuf:"bytes,1,opt,name=this_month_community_info,json=thisMonthCommunityInfo,proto3" json:"this_month_community_info,omitempty"`
	LastMonthCommunityInfo *MultiPlayerMonthCommunityInfo `protobuf:"bytes,2,opt,name=last_month_community_info,json=lastMonthCommunityInfo,proto3" json:"last_month_community_info,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}                       `json:"-"`
	XXX_unrecognized       []byte                         `json:"-"`
	XXX_sizecache          int32                          `json:"-"`
}

func (m *GetMultiPlayerMonthCommunityInfoResp) Reset()         { *m = GetMultiPlayerMonthCommunityInfoResp{} }
func (m *GetMultiPlayerMonthCommunityInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetMultiPlayerMonthCommunityInfoResp) ProtoMessage()    {}
func (*GetMultiPlayerMonthCommunityInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{37}
}
func (m *GetMultiPlayerMonthCommunityInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMultiPlayerMonthCommunityInfoResp.Unmarshal(m, b)
}
func (m *GetMultiPlayerMonthCommunityInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMultiPlayerMonthCommunityInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetMultiPlayerMonthCommunityInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMultiPlayerMonthCommunityInfoResp.Merge(dst, src)
}
func (m *GetMultiPlayerMonthCommunityInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetMultiPlayerMonthCommunityInfoResp.Size(m)
}
func (m *GetMultiPlayerMonthCommunityInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMultiPlayerMonthCommunityInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMultiPlayerMonthCommunityInfoResp proto.InternalMessageInfo

func (m *GetMultiPlayerMonthCommunityInfoResp) GetThisMonthCommunityInfo() *MultiPlayerMonthCommunityInfo {
	if m != nil {
		return m.ThisMonthCommunityInfo
	}
	return nil
}

func (m *GetMultiPlayerMonthCommunityInfoResp) GetLastMonthCommunityInfo() *MultiPlayerMonthCommunityInfo {
	if m != nil {
		return m.LastMonthCommunityInfo
	}
	return nil
}

type DailyInfo struct {
	Income               uint32   `protobuf:"varint,1,opt,name=income,proto3" json:"income,omitempty"`
	ValidSec             uint32   `protobuf:"varint,2,opt,name=valid_sec,json=validSec,proto3" json:"valid_sec,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DailyInfo) Reset()         { *m = DailyInfo{} }
func (m *DailyInfo) String() string { return proto.CompactTextString(m) }
func (*DailyInfo) ProtoMessage()    {}
func (*DailyInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{38}
}
func (m *DailyInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DailyInfo.Unmarshal(m, b)
}
func (m *DailyInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DailyInfo.Marshal(b, m, deterministic)
}
func (dst *DailyInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DailyInfo.Merge(dst, src)
}
func (m *DailyInfo) XXX_Size() int {
	return xxx_messageInfo_DailyInfo.Size(m)
}
func (m *DailyInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DailyInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DailyInfo proto.InternalMessageInfo

func (m *DailyInfo) GetIncome() uint32 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *DailyInfo) GetValidSec() uint32 {
	if m != nil {
		return m.ValidSec
	}
	return 0
}

type DurInfo struct {
	Income               uint32   `protobuf:"varint,1,opt,name=income,proto3" json:"income,omitempty"`
	ValidDay             uint32   `protobuf:"varint,2,opt,name=valid_day,json=validDay,proto3" json:"valid_day,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DurInfo) Reset()         { *m = DurInfo{} }
func (m *DurInfo) String() string { return proto.CompactTextString(m) }
func (*DurInfo) ProtoMessage()    {}
func (*DurInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{39}
}
func (m *DurInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DurInfo.Unmarshal(m, b)
}
func (m *DurInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DurInfo.Marshal(b, m, deterministic)
}
func (dst *DurInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DurInfo.Merge(dst, src)
}
func (m *DurInfo) XXX_Size() int {
	return xxx_messageInfo_DurInfo.Size(m)
}
func (m *DurInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DurInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DurInfo proto.InternalMessageInfo

func (m *DurInfo) GetIncome() uint32 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *DurInfo) GetValidDay() uint32 {
	if m != nil {
		return m.ValidDay
	}
	return 0
}

type MultiPlayerCacheInfo struct {
	DailyInfo            *DailyInfo `protobuf:"bytes,1,opt,name=daily_info,json=dailyInfo,proto3" json:"daily_info,omitempty"`
	WeekInfo             *DurInfo   `protobuf:"bytes,2,opt,name=week_info,json=weekInfo,proto3" json:"week_info,omitempty"`
	MonthInfo            *DurInfo   `protobuf:"bytes,3,opt,name=month_info,json=monthInfo,proto3" json:"month_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *MultiPlayerCacheInfo) Reset()         { *m = MultiPlayerCacheInfo{} }
func (m *MultiPlayerCacheInfo) String() string { return proto.CompactTextString(m) }
func (*MultiPlayerCacheInfo) ProtoMessage()    {}
func (*MultiPlayerCacheInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{40}
}
func (m *MultiPlayerCacheInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MultiPlayerCacheInfo.Unmarshal(m, b)
}
func (m *MultiPlayerCacheInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MultiPlayerCacheInfo.Marshal(b, m, deterministic)
}
func (dst *MultiPlayerCacheInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultiPlayerCacheInfo.Merge(dst, src)
}
func (m *MultiPlayerCacheInfo) XXX_Size() int {
	return xxx_messageInfo_MultiPlayerCacheInfo.Size(m)
}
func (m *MultiPlayerCacheInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MultiPlayerCacheInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MultiPlayerCacheInfo proto.InternalMessageInfo

func (m *MultiPlayerCacheInfo) GetDailyInfo() *DailyInfo {
	if m != nil {
		return m.DailyInfo
	}
	return nil
}

func (m *MultiPlayerCacheInfo) GetWeekInfo() *DurInfo {
	if m != nil {
		return m.WeekInfo
	}
	return nil
}

func (m *MultiPlayerCacheInfo) GetMonthInfo() *DurInfo {
	if m != nil {
		return m.MonthInfo
	}
	return nil
}

type MultiPlayerDetialCacheInfo struct {
	DailyInfoList        []*DailyInfo `protobuf:"bytes,1,rep,name=daily_info_list,json=dailyInfoList,proto3" json:"daily_info_list,omitempty"`
	MonthInfoList        []*DurInfo   `protobuf:"bytes,2,rep,name=month_info_list,json=monthInfoList,proto3" json:"month_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *MultiPlayerDetialCacheInfo) Reset()         { *m = MultiPlayerDetialCacheInfo{} }
func (m *MultiPlayerDetialCacheInfo) String() string { return proto.CompactTextString(m) }
func (*MultiPlayerDetialCacheInfo) ProtoMessage()    {}
func (*MultiPlayerDetialCacheInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{41}
}
func (m *MultiPlayerDetialCacheInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MultiPlayerDetialCacheInfo.Unmarshal(m, b)
}
func (m *MultiPlayerDetialCacheInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MultiPlayerDetialCacheInfo.Marshal(b, m, deterministic)
}
func (dst *MultiPlayerDetialCacheInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultiPlayerDetialCacheInfo.Merge(dst, src)
}
func (m *MultiPlayerDetialCacheInfo) XXX_Size() int {
	return xxx_messageInfo_MultiPlayerDetialCacheInfo.Size(m)
}
func (m *MultiPlayerDetialCacheInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MultiPlayerDetialCacheInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MultiPlayerDetialCacheInfo proto.InternalMessageInfo

func (m *MultiPlayerDetialCacheInfo) GetDailyInfoList() []*DailyInfo {
	if m != nil {
		return m.DailyInfoList
	}
	return nil
}

func (m *MultiPlayerDetialCacheInfo) GetMonthInfoList() []*DurInfo {
	if m != nil {
		return m.MonthInfoList
	}
	return nil
}

// 检查用户是否有互动消息查看入口
type CheckUserInteractEntryReq struct {
	SceneType            uint32   `protobuf:"varint,1,opt,name=scene_type,json=sceneType,proto3" json:"scene_type,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	InteractUid          uint32   `protobuf:"varint,3,opt,name=interact_uid,json=interactUid,proto3" json:"interact_uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelLiveId        uint64   `protobuf:"varint,5,opt,name=channel_live_id,json=channelLiveId,proto3" json:"channel_live_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUserInteractEntryReq) Reset()         { *m = CheckUserInteractEntryReq{} }
func (m *CheckUserInteractEntryReq) String() string { return proto.CompactTextString(m) }
func (*CheckUserInteractEntryReq) ProtoMessage()    {}
func (*CheckUserInteractEntryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{42}
}
func (m *CheckUserInteractEntryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserInteractEntryReq.Unmarshal(m, b)
}
func (m *CheckUserInteractEntryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserInteractEntryReq.Marshal(b, m, deterministic)
}
func (dst *CheckUserInteractEntryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserInteractEntryReq.Merge(dst, src)
}
func (m *CheckUserInteractEntryReq) XXX_Size() int {
	return xxx_messageInfo_CheckUserInteractEntryReq.Size(m)
}
func (m *CheckUserInteractEntryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserInteractEntryReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserInteractEntryReq proto.InternalMessageInfo

func (m *CheckUserInteractEntryReq) GetSceneType() uint32 {
	if m != nil {
		return m.SceneType
	}
	return 0
}

func (m *CheckUserInteractEntryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckUserInteractEntryReq) GetInteractUid() uint32 {
	if m != nil {
		return m.InteractUid
	}
	return 0
}

func (m *CheckUserInteractEntryReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CheckUserInteractEntryReq) GetChannelLiveId() uint64 {
	if m != nil {
		return m.ChannelLiveId
	}
	return 0
}

type CheckUserInteractEntryResp struct {
	IsHasEntry           bool     `protobuf:"varint,1,opt,name=is_has_entry,json=isHasEntry,proto3" json:"is_has_entry,omitempty"`
	EntryText            string   `protobuf:"bytes,2,opt,name=entry_text,json=entryText,proto3" json:"entry_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUserInteractEntryResp) Reset()         { *m = CheckUserInteractEntryResp{} }
func (m *CheckUserInteractEntryResp) String() string { return proto.CompactTextString(m) }
func (*CheckUserInteractEntryResp) ProtoMessage()    {}
func (*CheckUserInteractEntryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{43}
}
func (m *CheckUserInteractEntryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserInteractEntryResp.Unmarshal(m, b)
}
func (m *CheckUserInteractEntryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserInteractEntryResp.Marshal(b, m, deterministic)
}
func (dst *CheckUserInteractEntryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserInteractEntryResp.Merge(dst, src)
}
func (m *CheckUserInteractEntryResp) XXX_Size() int {
	return xxx_messageInfo_CheckUserInteractEntryResp.Size(m)
}
func (m *CheckUserInteractEntryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserInteractEntryResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserInteractEntryResp proto.InternalMessageInfo

func (m *CheckUserInteractEntryResp) GetIsHasEntry() bool {
	if m != nil {
		return m.IsHasEntry
	}
	return false
}

func (m *CheckUserInteractEntryResp) GetEntryText() string {
	if m != nil {
		return m.EntryText
	}
	return ""
}

// 互动消息
type InteractInfo struct {
	InteractType         string   `protobuf:"bytes,1,opt,name=interact_type,json=interactType,proto3" json:"interact_type,omitempty"`
	InteractValue        string   `protobuf:"bytes,2,opt,name=interact_value,json=interactValue,proto3" json:"interact_value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InteractInfo) Reset()         { *m = InteractInfo{} }
func (m *InteractInfo) String() string { return proto.CompactTextString(m) }
func (*InteractInfo) ProtoMessage()    {}
func (*InteractInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{44}
}
func (m *InteractInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InteractInfo.Unmarshal(m, b)
}
func (m *InteractInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InteractInfo.Marshal(b, m, deterministic)
}
func (dst *InteractInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InteractInfo.Merge(dst, src)
}
func (m *InteractInfo) XXX_Size() int {
	return xxx_messageInfo_InteractInfo.Size(m)
}
func (m *InteractInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_InteractInfo.DiscardUnknown(m)
}

var xxx_messageInfo_InteractInfo proto.InternalMessageInfo

func (m *InteractInfo) GetInteractType() string {
	if m != nil {
		return m.InteractType
	}
	return ""
}

func (m *InteractInfo) GetInteractValue() string {
	if m != nil {
		return m.InteractValue
	}
	return ""
}

// 互动标识
type InteractCertInfo struct {
	FansInfo             *channellivefans.FansInfo `protobuf:"bytes,1,opt,name=fans_info,json=fansInfo,proto3" json:"fans_info,omitempty"`
	CertMsg              string                    `protobuf:"bytes,2,opt,name=cert_msg,json=certMsg,proto3" json:"cert_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *InteractCertInfo) Reset()         { *m = InteractCertInfo{} }
func (m *InteractCertInfo) String() string { return proto.CompactTextString(m) }
func (*InteractCertInfo) ProtoMessage()    {}
func (*InteractCertInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{45}
}
func (m *InteractCertInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InteractCertInfo.Unmarshal(m, b)
}
func (m *InteractCertInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InteractCertInfo.Marshal(b, m, deterministic)
}
func (dst *InteractCertInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InteractCertInfo.Merge(dst, src)
}
func (m *InteractCertInfo) XXX_Size() int {
	return xxx_messageInfo_InteractCertInfo.Size(m)
}
func (m *InteractCertInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_InteractCertInfo.DiscardUnknown(m)
}

var xxx_messageInfo_InteractCertInfo proto.InternalMessageInfo

func (m *InteractCertInfo) GetFansInfo() *channellivefans.FansInfo {
	if m != nil {
		return m.FansInfo
	}
	return nil
}

func (m *InteractCertInfo) GetCertMsg() string {
	if m != nil {
		return m.CertMsg
	}
	return ""
}

// 获取用户的互动消息
type GetUserInteractInfoReq struct {
	SceneType            uint32   `protobuf:"varint,1,opt,name=scene_type,json=sceneType,proto3" json:"scene_type,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	InteractUid          uint32   `protobuf:"varint,3,opt,name=interact_uid,json=interactUid,proto3" json:"interact_uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelLiveId        uint64   `protobuf:"varint,5,opt,name=channel_live_id,json=channelLiveId,proto3" json:"channel_live_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserInteractInfoReq) Reset()         { *m = GetUserInteractInfoReq{} }
func (m *GetUserInteractInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserInteractInfoReq) ProtoMessage()    {}
func (*GetUserInteractInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{46}
}
func (m *GetUserInteractInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserInteractInfoReq.Unmarshal(m, b)
}
func (m *GetUserInteractInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserInteractInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserInteractInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserInteractInfoReq.Merge(dst, src)
}
func (m *GetUserInteractInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserInteractInfoReq.Size(m)
}
func (m *GetUserInteractInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserInteractInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserInteractInfoReq proto.InternalMessageInfo

func (m *GetUserInteractInfoReq) GetSceneType() uint32 {
	if m != nil {
		return m.SceneType
	}
	return 0
}

func (m *GetUserInteractInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserInteractInfoReq) GetInteractUid() uint32 {
	if m != nil {
		return m.InteractUid
	}
	return 0
}

func (m *GetUserInteractInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetUserInteractInfoReq) GetChannelLiveId() uint64 {
	if m != nil {
		return m.ChannelLiveId
	}
	return 0
}

type GetUserInteractInfoResp struct {
	UserType             uint32            `protobuf:"varint,1,opt,name=user_type,json=userType,proto3" json:"user_type,omitempty"`
	InfoList             []*InteractInfo   `protobuf:"bytes,2,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	CertInfo             *InteractCertInfo `protobuf:"bytes,3,opt,name=cert_info,json=certInfo,proto3" json:"cert_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetUserInteractInfoResp) Reset()         { *m = GetUserInteractInfoResp{} }
func (m *GetUserInteractInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserInteractInfoResp) ProtoMessage()    {}
func (*GetUserInteractInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{47}
}
func (m *GetUserInteractInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserInteractInfoResp.Unmarshal(m, b)
}
func (m *GetUserInteractInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserInteractInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserInteractInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserInteractInfoResp.Merge(dst, src)
}
func (m *GetUserInteractInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserInteractInfoResp.Size(m)
}
func (m *GetUserInteractInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserInteractInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserInteractInfoResp proto.InternalMessageInfo

func (m *GetUserInteractInfoResp) GetUserType() uint32 {
	if m != nil {
		return m.UserType
	}
	return 0
}

func (m *GetUserInteractInfoResp) GetInfoList() []*InteractInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetUserInteractInfoResp) GetCertInfo() *InteractCertInfo {
	if m != nil {
		return m.CertInfo
	}
	return nil
}

// 获取用户互动资料隐私查看权限
type GetUserInteractViewPerReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserInteractViewPerReq) Reset()         { *m = GetUserInteractViewPerReq{} }
func (m *GetUserInteractViewPerReq) String() string { return proto.CompactTextString(m) }
func (*GetUserInteractViewPerReq) ProtoMessage()    {}
func (*GetUserInteractViewPerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{48}
}
func (m *GetUserInteractViewPerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserInteractViewPerReq.Unmarshal(m, b)
}
func (m *GetUserInteractViewPerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserInteractViewPerReq.Marshal(b, m, deterministic)
}
func (dst *GetUserInteractViewPerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserInteractViewPerReq.Merge(dst, src)
}
func (m *GetUserInteractViewPerReq) XXX_Size() int {
	return xxx_messageInfo_GetUserInteractViewPerReq.Size(m)
}
func (m *GetUserInteractViewPerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserInteractViewPerReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserInteractViewPerReq proto.InternalMessageInfo

func (m *GetUserInteractViewPerReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserInteractViewPerResp struct {
	IsOpen               bool     `protobuf:"varint,1,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserInteractViewPerResp) Reset()         { *m = GetUserInteractViewPerResp{} }
func (m *GetUserInteractViewPerResp) String() string { return proto.CompactTextString(m) }
func (*GetUserInteractViewPerResp) ProtoMessage()    {}
func (*GetUserInteractViewPerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{49}
}
func (m *GetUserInteractViewPerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserInteractViewPerResp.Unmarshal(m, b)
}
func (m *GetUserInteractViewPerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserInteractViewPerResp.Marshal(b, m, deterministic)
}
func (dst *GetUserInteractViewPerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserInteractViewPerResp.Merge(dst, src)
}
func (m *GetUserInteractViewPerResp) XXX_Size() int {
	return xxx_messageInfo_GetUserInteractViewPerResp.Size(m)
}
func (m *GetUserInteractViewPerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserInteractViewPerResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserInteractViewPerResp proto.InternalMessageInfo

func (m *GetUserInteractViewPerResp) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

// 设置互动资料隐私查看权限
type SetUserInteractViewPerReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	IsOpen               bool     `protobuf:"varint,2,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserInteractViewPerReq) Reset()         { *m = SetUserInteractViewPerReq{} }
func (m *SetUserInteractViewPerReq) String() string { return proto.CompactTextString(m) }
func (*SetUserInteractViewPerReq) ProtoMessage()    {}
func (*SetUserInteractViewPerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{50}
}
func (m *SetUserInteractViewPerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserInteractViewPerReq.Unmarshal(m, b)
}
func (m *SetUserInteractViewPerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserInteractViewPerReq.Marshal(b, m, deterministic)
}
func (dst *SetUserInteractViewPerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserInteractViewPerReq.Merge(dst, src)
}
func (m *SetUserInteractViewPerReq) XXX_Size() int {
	return xxx_messageInfo_SetUserInteractViewPerReq.Size(m)
}
func (m *SetUserInteractViewPerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserInteractViewPerReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserInteractViewPerReq proto.InternalMessageInfo

func (m *SetUserInteractViewPerReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserInteractViewPerReq) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

type SetUserInteractViewPerResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserInteractViewPerResp) Reset()         { *m = SetUserInteractViewPerResp{} }
func (m *SetUserInteractViewPerResp) String() string { return proto.CompactTextString(m) }
func (*SetUserInteractViewPerResp) ProtoMessage()    {}
func (*SetUserInteractViewPerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{51}
}
func (m *SetUserInteractViewPerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserInteractViewPerResp.Unmarshal(m, b)
}
func (m *SetUserInteractViewPerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserInteractViewPerResp.Marshal(b, m, deterministic)
}
func (dst *SetUserInteractViewPerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserInteractViewPerResp.Merge(dst, src)
}
func (m *SetUserInteractViewPerResp) XXX_Size() int {
	return xxx_messageInfo_SetUserInteractViewPerResp.Size(m)
}
func (m *SetUserInteractViewPerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserInteractViewPerResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserInteractViewPerResp proto.InternalMessageInfo

// *** 多人互动大厅任务 ***//
type GetGuildChannelListReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildChannelListReq) Reset()         { *m = GetGuildChannelListReq{} }
func (m *GetGuildChannelListReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildChannelListReq) ProtoMessage()    {}
func (*GetGuildChannelListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{52}
}
func (m *GetGuildChannelListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildChannelListReq.Unmarshal(m, b)
}
func (m *GetGuildChannelListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildChannelListReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildChannelListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildChannelListReq.Merge(dst, src)
}
func (m *GetGuildChannelListReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildChannelListReq.Size(m)
}
func (m *GetGuildChannelListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildChannelListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildChannelListReq proto.InternalMessageInfo

func (m *GetGuildChannelListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetGuildChannelListResp struct {
	List                 []*MultiPlayerHallInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetGuildChannelListResp) Reset()         { *m = GetGuildChannelListResp{} }
func (m *GetGuildChannelListResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildChannelListResp) ProtoMessage()    {}
func (*GetGuildChannelListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{53}
}
func (m *GetGuildChannelListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildChannelListResp.Unmarshal(m, b)
}
func (m *GetGuildChannelListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildChannelListResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildChannelListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildChannelListResp.Merge(dst, src)
}
func (m *GetGuildChannelListResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildChannelListResp.Size(m)
}
func (m *GetGuildChannelListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildChannelListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildChannelListResp proto.InternalMessageInfo

func (m *GetGuildChannelListResp) GetList() []*MultiPlayerHallInfo {
	if m != nil {
		return m.List
	}
	return nil
}

type AddMultiPlayerHallReq struct {
	List                 []*MultiPlayerHallInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	IsLimitNoselfGuild   bool                   `protobuf:"varint,3,opt,name=is_limit_noself_guild,json=isLimitNoselfGuild,proto3" json:"is_limit_noself_guild,omitempty"`
	IsCheck              bool                   `protobuf:"varint,4,opt,name=is_check,json=isCheck,proto3" json:"is_check,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *AddMultiPlayerHallReq) Reset()         { *m = AddMultiPlayerHallReq{} }
func (m *AddMultiPlayerHallReq) String() string { return proto.CompactTextString(m) }
func (*AddMultiPlayerHallReq) ProtoMessage()    {}
func (*AddMultiPlayerHallReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{54}
}
func (m *AddMultiPlayerHallReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddMultiPlayerHallReq.Unmarshal(m, b)
}
func (m *AddMultiPlayerHallReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddMultiPlayerHallReq.Marshal(b, m, deterministic)
}
func (dst *AddMultiPlayerHallReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddMultiPlayerHallReq.Merge(dst, src)
}
func (m *AddMultiPlayerHallReq) XXX_Size() int {
	return xxx_messageInfo_AddMultiPlayerHallReq.Size(m)
}
func (m *AddMultiPlayerHallReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddMultiPlayerHallReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddMultiPlayerHallReq proto.InternalMessageInfo

func (m *AddMultiPlayerHallReq) GetList() []*MultiPlayerHallInfo {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *AddMultiPlayerHallReq) GetIsLimitNoselfGuild() bool {
	if m != nil {
		return m.IsLimitNoselfGuild
	}
	return false
}

func (m *AddMultiPlayerHallReq) GetIsCheck() bool {
	if m != nil {
		return m.IsCheck
	}
	return false
}

type AddMultiPlayerHallResp struct {
	IsDiffent            bool     `protobuf:"varint,5,opt,name=is_diffent,json=isDiffent,proto3" json:"is_diffent,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddMultiPlayerHallResp) Reset()         { *m = AddMultiPlayerHallResp{} }
func (m *AddMultiPlayerHallResp) String() string { return proto.CompactTextString(m) }
func (*AddMultiPlayerHallResp) ProtoMessage()    {}
func (*AddMultiPlayerHallResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{55}
}
func (m *AddMultiPlayerHallResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddMultiPlayerHallResp.Unmarshal(m, b)
}
func (m *AddMultiPlayerHallResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddMultiPlayerHallResp.Marshal(b, m, deterministic)
}
func (dst *AddMultiPlayerHallResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddMultiPlayerHallResp.Merge(dst, src)
}
func (m *AddMultiPlayerHallResp) XXX_Size() int {
	return xxx_messageInfo_AddMultiPlayerHallResp.Size(m)
}
func (m *AddMultiPlayerHallResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddMultiPlayerHallResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddMultiPlayerHallResp proto.InternalMessageInfo

func (m *AddMultiPlayerHallResp) GetIsDiffent() bool {
	if m != nil {
		return m.IsDiffent
	}
	return false
}

type ListMultiPlayerHallReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Page                 uint32   `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListMultiPlayerHallReq) Reset()         { *m = ListMultiPlayerHallReq{} }
func (m *ListMultiPlayerHallReq) String() string { return proto.CompactTextString(m) }
func (*ListMultiPlayerHallReq) ProtoMessage()    {}
func (*ListMultiPlayerHallReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{56}
}
func (m *ListMultiPlayerHallReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMultiPlayerHallReq.Unmarshal(m, b)
}
func (m *ListMultiPlayerHallReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMultiPlayerHallReq.Marshal(b, m, deterministic)
}
func (dst *ListMultiPlayerHallReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMultiPlayerHallReq.Merge(dst, src)
}
func (m *ListMultiPlayerHallReq) XXX_Size() int {
	return xxx_messageInfo_ListMultiPlayerHallReq.Size(m)
}
func (m *ListMultiPlayerHallReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMultiPlayerHallReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListMultiPlayerHallReq proto.InternalMessageInfo

func (m *ListMultiPlayerHallReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *ListMultiPlayerHallReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *ListMultiPlayerHallReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type ListMultiPlayerHallResp struct {
	Total                uint32                 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List                 []*MultiPlayerHallInfo `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *ListMultiPlayerHallResp) Reset()         { *m = ListMultiPlayerHallResp{} }
func (m *ListMultiPlayerHallResp) String() string { return proto.CompactTextString(m) }
func (*ListMultiPlayerHallResp) ProtoMessage()    {}
func (*ListMultiPlayerHallResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{57}
}
func (m *ListMultiPlayerHallResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMultiPlayerHallResp.Unmarshal(m, b)
}
func (m *ListMultiPlayerHallResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMultiPlayerHallResp.Marshal(b, m, deterministic)
}
func (dst *ListMultiPlayerHallResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMultiPlayerHallResp.Merge(dst, src)
}
func (m *ListMultiPlayerHallResp) XXX_Size() int {
	return xxx_messageInfo_ListMultiPlayerHallResp.Size(m)
}
func (m *ListMultiPlayerHallResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMultiPlayerHallResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListMultiPlayerHallResp proto.InternalMessageInfo

func (m *ListMultiPlayerHallResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *ListMultiPlayerHallResp) GetList() []*MultiPlayerHallInfo {
	if m != nil {
		return m.List
	}
	return nil
}

type DelMultiPlayerHallReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelMultiPlayerHallReq) Reset()         { *m = DelMultiPlayerHallReq{} }
func (m *DelMultiPlayerHallReq) String() string { return proto.CompactTextString(m) }
func (*DelMultiPlayerHallReq) ProtoMessage()    {}
func (*DelMultiPlayerHallReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{58}
}
func (m *DelMultiPlayerHallReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelMultiPlayerHallReq.Unmarshal(m, b)
}
func (m *DelMultiPlayerHallReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelMultiPlayerHallReq.Marshal(b, m, deterministic)
}
func (dst *DelMultiPlayerHallReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelMultiPlayerHallReq.Merge(dst, src)
}
func (m *DelMultiPlayerHallReq) XXX_Size() int {
	return xxx_messageInfo_DelMultiPlayerHallReq.Size(m)
}
func (m *DelMultiPlayerHallReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelMultiPlayerHallReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelMultiPlayerHallReq proto.InternalMessageInfo

func (m *DelMultiPlayerHallReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *DelMultiPlayerHallReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type DelMultiPlayerHallResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelMultiPlayerHallResp) Reset()         { *m = DelMultiPlayerHallResp{} }
func (m *DelMultiPlayerHallResp) String() string { return proto.CompactTextString(m) }
func (*DelMultiPlayerHallResp) ProtoMessage()    {}
func (*DelMultiPlayerHallResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{59}
}
func (m *DelMultiPlayerHallResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelMultiPlayerHallResp.Unmarshal(m, b)
}
func (m *DelMultiPlayerHallResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelMultiPlayerHallResp.Marshal(b, m, deterministic)
}
func (dst *DelMultiPlayerHallResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelMultiPlayerHallResp.Merge(dst, src)
}
func (m *DelMultiPlayerHallResp) XXX_Size() int {
	return xxx_messageInfo_DelMultiPlayerHallResp.Size(m)
}
func (m *DelMultiPlayerHallResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelMultiPlayerHallResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelMultiPlayerHallResp proto.InternalMessageInfo

type MultiPlayerHallInfo struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GuildShortId         uint32   `protobuf:"varint,2,opt,name=guild_short_id,json=guildShortId,proto3" json:"guild_short_id,omitempty"`
	GuildName            string   `protobuf:"bytes,3,opt,name=guild_name,json=guildName,proto3" json:"guild_name,omitempty"`
	ChannelId            uint32   `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelViewId        string   `protobuf:"bytes,5,opt,name=channel_view_id,json=channelViewId,proto3" json:"channel_view_id,omitempty"`
	ChannelName          string   `protobuf:"bytes,6,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,7,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	IsLimitNoselfGuild   bool     `protobuf:"varint,8,opt,name=is_limit_noself_guild,json=isLimitNoselfGuild,proto3" json:"is_limit_noself_guild,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MultiPlayerHallInfo) Reset()         { *m = MultiPlayerHallInfo{} }
func (m *MultiPlayerHallInfo) String() string { return proto.CompactTextString(m) }
func (*MultiPlayerHallInfo) ProtoMessage()    {}
func (*MultiPlayerHallInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{60}
}
func (m *MultiPlayerHallInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MultiPlayerHallInfo.Unmarshal(m, b)
}
func (m *MultiPlayerHallInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MultiPlayerHallInfo.Marshal(b, m, deterministic)
}
func (dst *MultiPlayerHallInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultiPlayerHallInfo.Merge(dst, src)
}
func (m *MultiPlayerHallInfo) XXX_Size() int {
	return xxx_messageInfo_MultiPlayerHallInfo.Size(m)
}
func (m *MultiPlayerHallInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MultiPlayerHallInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MultiPlayerHallInfo proto.InternalMessageInfo

func (m *MultiPlayerHallInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *MultiPlayerHallInfo) GetGuildShortId() uint32 {
	if m != nil {
		return m.GuildShortId
	}
	return 0
}

func (m *MultiPlayerHallInfo) GetGuildName() string {
	if m != nil {
		return m.GuildName
	}
	return ""
}

func (m *MultiPlayerHallInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MultiPlayerHallInfo) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

func (m *MultiPlayerHallInfo) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *MultiPlayerHallInfo) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *MultiPlayerHallInfo) GetIsLimitNoselfGuild() bool {
	if m != nil {
		return m.IsLimitNoselfGuild
	}
	return false
}

type GetGuildMultiPlayerHallReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildMultiPlayerHallReq) Reset()         { *m = GetGuildMultiPlayerHallReq{} }
func (m *GetGuildMultiPlayerHallReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildMultiPlayerHallReq) ProtoMessage()    {}
func (*GetGuildMultiPlayerHallReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{61}
}
func (m *GetGuildMultiPlayerHallReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildMultiPlayerHallReq.Unmarshal(m, b)
}
func (m *GetGuildMultiPlayerHallReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildMultiPlayerHallReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildMultiPlayerHallReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildMultiPlayerHallReq.Merge(dst, src)
}
func (m *GetGuildMultiPlayerHallReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildMultiPlayerHallReq.Size(m)
}
func (m *GetGuildMultiPlayerHallReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildMultiPlayerHallReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildMultiPlayerHallReq proto.InternalMessageInfo

func (m *GetGuildMultiPlayerHallReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetGuildMultiPlayerHallResp struct {
	List                 []*GetGuildMultiPlayerHallResp_GuildMultiPlayerHall `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                            `json:"-"`
	XXX_unrecognized     []byte                                              `json:"-"`
	XXX_sizecache        int32                                               `json:"-"`
}

func (m *GetGuildMultiPlayerHallResp) Reset()         { *m = GetGuildMultiPlayerHallResp{} }
func (m *GetGuildMultiPlayerHallResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildMultiPlayerHallResp) ProtoMessage()    {}
func (*GetGuildMultiPlayerHallResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{62}
}
func (m *GetGuildMultiPlayerHallResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildMultiPlayerHallResp.Unmarshal(m, b)
}
func (m *GetGuildMultiPlayerHallResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildMultiPlayerHallResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildMultiPlayerHallResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildMultiPlayerHallResp.Merge(dst, src)
}
func (m *GetGuildMultiPlayerHallResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildMultiPlayerHallResp.Size(m)
}
func (m *GetGuildMultiPlayerHallResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildMultiPlayerHallResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildMultiPlayerHallResp proto.InternalMessageInfo

func (m *GetGuildMultiPlayerHallResp) GetList() []*GetGuildMultiPlayerHallResp_GuildMultiPlayerHall {
	if m != nil {
		return m.List
	}
	return nil
}

type GetGuildMultiPlayerHallResp_GuildMultiPlayerHall struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	IsDel                bool     `protobuf:"varint,2,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildMultiPlayerHallResp_GuildMultiPlayerHall) Reset() {
	*m = GetGuildMultiPlayerHallResp_GuildMultiPlayerHall{}
}
func (m *GetGuildMultiPlayerHallResp_GuildMultiPlayerHall) String() string {
	return proto.CompactTextString(m)
}
func (*GetGuildMultiPlayerHallResp_GuildMultiPlayerHall) ProtoMessage() {}
func (*GetGuildMultiPlayerHallResp_GuildMultiPlayerHall) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{62, 0}
}
func (m *GetGuildMultiPlayerHallResp_GuildMultiPlayerHall) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildMultiPlayerHallResp_GuildMultiPlayerHall.Unmarshal(m, b)
}
func (m *GetGuildMultiPlayerHallResp_GuildMultiPlayerHall) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildMultiPlayerHallResp_GuildMultiPlayerHall.Marshal(b, m, deterministic)
}
func (dst *GetGuildMultiPlayerHallResp_GuildMultiPlayerHall) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildMultiPlayerHallResp_GuildMultiPlayerHall.Merge(dst, src)
}
func (m *GetGuildMultiPlayerHallResp_GuildMultiPlayerHall) XXX_Size() int {
	return xxx_messageInfo_GetGuildMultiPlayerHallResp_GuildMultiPlayerHall.Size(m)
}
func (m *GetGuildMultiPlayerHallResp_GuildMultiPlayerHall) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildMultiPlayerHallResp_GuildMultiPlayerHall.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildMultiPlayerHallResp_GuildMultiPlayerHall proto.InternalMessageInfo

func (m *GetGuildMultiPlayerHallResp_GuildMultiPlayerHall) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetGuildMultiPlayerHallResp_GuildMultiPlayerHall) GetIsDel() bool {
	if m != nil {
		return m.IsDel
	}
	return false
}

type GetHallTaskConfListReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	TaskId               uint32   `protobuf:"varint,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	TaskGroupName        string   `protobuf:"bytes,3,opt,name=task_group_name,json=taskGroupName,proto3" json:"task_group_name,omitempty"`
	Page                 uint32   `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,5,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetHallTaskConfListReq) Reset()         { *m = GetHallTaskConfListReq{} }
func (m *GetHallTaskConfListReq) String() string { return proto.CompactTextString(m) }
func (*GetHallTaskConfListReq) ProtoMessage()    {}
func (*GetHallTaskConfListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{63}
}
func (m *GetHallTaskConfListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHallTaskConfListReq.Unmarshal(m, b)
}
func (m *GetHallTaskConfListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHallTaskConfListReq.Marshal(b, m, deterministic)
}
func (dst *GetHallTaskConfListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHallTaskConfListReq.Merge(dst, src)
}
func (m *GetHallTaskConfListReq) XXX_Size() int {
	return xxx_messageInfo_GetHallTaskConfListReq.Size(m)
}
func (m *GetHallTaskConfListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHallTaskConfListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetHallTaskConfListReq proto.InternalMessageInfo

func (m *GetHallTaskConfListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetHallTaskConfListReq) GetTaskId() uint32 {
	if m != nil {
		return m.TaskId
	}
	return 0
}

func (m *GetHallTaskConfListReq) GetTaskGroupName() string {
	if m != nil {
		return m.TaskGroupName
	}
	return ""
}

func (m *GetHallTaskConfListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetHallTaskConfListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetHallTaskConfListResp struct {
	Total                uint32                    `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List                 []*HallTaskConfDetialInfo `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetHallTaskConfListResp) Reset()         { *m = GetHallTaskConfListResp{} }
func (m *GetHallTaskConfListResp) String() string { return proto.CompactTextString(m) }
func (*GetHallTaskConfListResp) ProtoMessage()    {}
func (*GetHallTaskConfListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{64}
}
func (m *GetHallTaskConfListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHallTaskConfListResp.Unmarshal(m, b)
}
func (m *GetHallTaskConfListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHallTaskConfListResp.Marshal(b, m, deterministic)
}
func (dst *GetHallTaskConfListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHallTaskConfListResp.Merge(dst, src)
}
func (m *GetHallTaskConfListResp) XXX_Size() int {
	return xxx_messageInfo_GetHallTaskConfListResp.Size(m)
}
func (m *GetHallTaskConfListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHallTaskConfListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetHallTaskConfListResp proto.InternalMessageInfo

func (m *GetHallTaskConfListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetHallTaskConfListResp) GetList() []*HallTaskConfDetialInfo {
	if m != nil {
		return m.List
	}
	return nil
}

// 任务配置详情
type HallTaskConfDetialInfo struct {
	TaskId               uint32              `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	TaskGroupName        string              `protobuf:"bytes,2,opt,name=task_group_name,json=taskGroupName,proto3" json:"task_group_name,omitempty"`
	DayTaskList          []*HallTaskConfInfo `protobuf:"bytes,3,rep,name=day_task_list,json=dayTaskList,proto3" json:"day_task_list,omitempty"`
	WeekTaskList         []*HallTaskConfInfo `protobuf:"bytes,4,rep,name=week_task_list,json=weekTaskList,proto3" json:"week_task_list,omitempty"`
	RewardMsg            string              `protobuf:"bytes,5,opt,name=reward_msg,json=rewardMsg,proto3" json:"reward_msg,omitempty"`
	HasNextWeekEffect    bool                `protobuf:"varint,6,opt,name=has_next_week_effect,json=hasNextWeekEffect,proto3" json:"has_next_week_effect,omitempty"`
	HasDistribute        bool                `protobuf:"varint,7,opt,name=has_distribute,json=hasDistribute,proto3" json:"has_distribute,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *HallTaskConfDetialInfo) Reset()         { *m = HallTaskConfDetialInfo{} }
func (m *HallTaskConfDetialInfo) String() string { return proto.CompactTextString(m) }
func (*HallTaskConfDetialInfo) ProtoMessage()    {}
func (*HallTaskConfDetialInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{65}
}
func (m *HallTaskConfDetialInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HallTaskConfDetialInfo.Unmarshal(m, b)
}
func (m *HallTaskConfDetialInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HallTaskConfDetialInfo.Marshal(b, m, deterministic)
}
func (dst *HallTaskConfDetialInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HallTaskConfDetialInfo.Merge(dst, src)
}
func (m *HallTaskConfDetialInfo) XXX_Size() int {
	return xxx_messageInfo_HallTaskConfDetialInfo.Size(m)
}
func (m *HallTaskConfDetialInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_HallTaskConfDetialInfo.DiscardUnknown(m)
}

var xxx_messageInfo_HallTaskConfDetialInfo proto.InternalMessageInfo

func (m *HallTaskConfDetialInfo) GetTaskId() uint32 {
	if m != nil {
		return m.TaskId
	}
	return 0
}

func (m *HallTaskConfDetialInfo) GetTaskGroupName() string {
	if m != nil {
		return m.TaskGroupName
	}
	return ""
}

func (m *HallTaskConfDetialInfo) GetDayTaskList() []*HallTaskConfInfo {
	if m != nil {
		return m.DayTaskList
	}
	return nil
}

func (m *HallTaskConfDetialInfo) GetWeekTaskList() []*HallTaskConfInfo {
	if m != nil {
		return m.WeekTaskList
	}
	return nil
}

func (m *HallTaskConfDetialInfo) GetRewardMsg() string {
	if m != nil {
		return m.RewardMsg
	}
	return ""
}

func (m *HallTaskConfDetialInfo) GetHasNextWeekEffect() bool {
	if m != nil {
		return m.HasNextWeekEffect
	}
	return false
}

func (m *HallTaskConfDetialInfo) GetHasDistribute() bool {
	if m != nil {
		return m.HasDistribute
	}
	return false
}

type HallTaskConfInfo struct {
	TaskType             uint32    `protobuf:"varint,1,opt,name=task_type,json=taskType,proto3" json:"task_type,omitempty"`
	ValList              []float32 `protobuf:"fixed32,2,rep,packed,name=val_list,json=valList,proto3" json:"val_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *HallTaskConfInfo) Reset()         { *m = HallTaskConfInfo{} }
func (m *HallTaskConfInfo) String() string { return proto.CompactTextString(m) }
func (*HallTaskConfInfo) ProtoMessage()    {}
func (*HallTaskConfInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{66}
}
func (m *HallTaskConfInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HallTaskConfInfo.Unmarshal(m, b)
}
func (m *HallTaskConfInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HallTaskConfInfo.Marshal(b, m, deterministic)
}
func (dst *HallTaskConfInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HallTaskConfInfo.Merge(dst, src)
}
func (m *HallTaskConfInfo) XXX_Size() int {
	return xxx_messageInfo_HallTaskConfInfo.Size(m)
}
func (m *HallTaskConfInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_HallTaskConfInfo.DiscardUnknown(m)
}

var xxx_messageInfo_HallTaskConfInfo proto.InternalMessageInfo

func (m *HallTaskConfInfo) GetTaskType() uint32 {
	if m != nil {
		return m.TaskType
	}
	return 0
}

func (m *HallTaskConfInfo) GetValList() []float32 {
	if m != nil {
		return m.ValList
	}
	return nil
}

type AddHallTaskConfReq struct {
	GuildId              uint32                  `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Info                 *HallTaskConfDetialInfo `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *AddHallTaskConfReq) Reset()         { *m = AddHallTaskConfReq{} }
func (m *AddHallTaskConfReq) String() string { return proto.CompactTextString(m) }
func (*AddHallTaskConfReq) ProtoMessage()    {}
func (*AddHallTaskConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{67}
}
func (m *AddHallTaskConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddHallTaskConfReq.Unmarshal(m, b)
}
func (m *AddHallTaskConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddHallTaskConfReq.Marshal(b, m, deterministic)
}
func (dst *AddHallTaskConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddHallTaskConfReq.Merge(dst, src)
}
func (m *AddHallTaskConfReq) XXX_Size() int {
	return xxx_messageInfo_AddHallTaskConfReq.Size(m)
}
func (m *AddHallTaskConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddHallTaskConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddHallTaskConfReq proto.InternalMessageInfo

func (m *AddHallTaskConfReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *AddHallTaskConfReq) GetInfo() *HallTaskConfDetialInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type AddHallTaskConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddHallTaskConfResp) Reset()         { *m = AddHallTaskConfResp{} }
func (m *AddHallTaskConfResp) String() string { return proto.CompactTextString(m) }
func (*AddHallTaskConfResp) ProtoMessage()    {}
func (*AddHallTaskConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{68}
}
func (m *AddHallTaskConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddHallTaskConfResp.Unmarshal(m, b)
}
func (m *AddHallTaskConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddHallTaskConfResp.Marshal(b, m, deterministic)
}
func (dst *AddHallTaskConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddHallTaskConfResp.Merge(dst, src)
}
func (m *AddHallTaskConfResp) XXX_Size() int {
	return xxx_messageInfo_AddHallTaskConfResp.Size(m)
}
func (m *AddHallTaskConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddHallTaskConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddHallTaskConfResp proto.InternalMessageInfo

type DelHallTaskConfReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	TaskId               uint32   `protobuf:"varint,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	OpUid                uint32   `protobuf:"varint,3,opt,name=op_uid,json=opUid,proto3" json:"op_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelHallTaskConfReq) Reset()         { *m = DelHallTaskConfReq{} }
func (m *DelHallTaskConfReq) String() string { return proto.CompactTextString(m) }
func (*DelHallTaskConfReq) ProtoMessage()    {}
func (*DelHallTaskConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{69}
}
func (m *DelHallTaskConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelHallTaskConfReq.Unmarshal(m, b)
}
func (m *DelHallTaskConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelHallTaskConfReq.Marshal(b, m, deterministic)
}
func (dst *DelHallTaskConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelHallTaskConfReq.Merge(dst, src)
}
func (m *DelHallTaskConfReq) XXX_Size() int {
	return xxx_messageInfo_DelHallTaskConfReq.Size(m)
}
func (m *DelHallTaskConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelHallTaskConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelHallTaskConfReq proto.InternalMessageInfo

func (m *DelHallTaskConfReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *DelHallTaskConfReq) GetTaskId() uint32 {
	if m != nil {
		return m.TaskId
	}
	return 0
}

func (m *DelHallTaskConfReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

type DelHallTaskConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelHallTaskConfResp) Reset()         { *m = DelHallTaskConfResp{} }
func (m *DelHallTaskConfResp) String() string { return proto.CompactTextString(m) }
func (*DelHallTaskConfResp) ProtoMessage()    {}
func (*DelHallTaskConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{70}
}
func (m *DelHallTaskConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelHallTaskConfResp.Unmarshal(m, b)
}
func (m *DelHallTaskConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelHallTaskConfResp.Marshal(b, m, deterministic)
}
func (dst *DelHallTaskConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelHallTaskConfResp.Merge(dst, src)
}
func (m *DelHallTaskConfResp) XXX_Size() int {
	return xxx_messageInfo_DelHallTaskConfResp.Size(m)
}
func (m *DelHallTaskConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelHallTaskConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelHallTaskConfResp proto.InternalMessageInfo

// 分配任务
type DistributeHallTaskReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	TaskId               uint32   `protobuf:"varint,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	UidList              []uint32 `protobuf:"varint,3,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	IsForceUpdate        bool     `protobuf:"varint,4,opt,name=is_force_update,json=isForceUpdate,proto3" json:"is_force_update,omitempty"`
	OpUid                uint32   `protobuf:"varint,5,opt,name=op_uid,json=opUid,proto3" json:"op_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DistributeHallTaskReq) Reset()         { *m = DistributeHallTaskReq{} }
func (m *DistributeHallTaskReq) String() string { return proto.CompactTextString(m) }
func (*DistributeHallTaskReq) ProtoMessage()    {}
func (*DistributeHallTaskReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{71}
}
func (m *DistributeHallTaskReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DistributeHallTaskReq.Unmarshal(m, b)
}
func (m *DistributeHallTaskReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DistributeHallTaskReq.Marshal(b, m, deterministic)
}
func (dst *DistributeHallTaskReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DistributeHallTaskReq.Merge(dst, src)
}
func (m *DistributeHallTaskReq) XXX_Size() int {
	return xxx_messageInfo_DistributeHallTaskReq.Size(m)
}
func (m *DistributeHallTaskReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DistributeHallTaskReq.DiscardUnknown(m)
}

var xxx_messageInfo_DistributeHallTaskReq proto.InternalMessageInfo

func (m *DistributeHallTaskReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *DistributeHallTaskReq) GetTaskId() uint32 {
	if m != nil {
		return m.TaskId
	}
	return 0
}

func (m *DistributeHallTaskReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *DistributeHallTaskReq) GetIsForceUpdate() bool {
	if m != nil {
		return m.IsForceUpdate
	}
	return false
}

func (m *DistributeHallTaskReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

type DistributeHallTaskResp struct {
	ConfictList          []*HallTaskInfo `protobuf:"bytes,1,rep,name=confict_list,json=confictList,proto3" json:"confict_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *DistributeHallTaskResp) Reset()         { *m = DistributeHallTaskResp{} }
func (m *DistributeHallTaskResp) String() string { return proto.CompactTextString(m) }
func (*DistributeHallTaskResp) ProtoMessage()    {}
func (*DistributeHallTaskResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{72}
}
func (m *DistributeHallTaskResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DistributeHallTaskResp.Unmarshal(m, b)
}
func (m *DistributeHallTaskResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DistributeHallTaskResp.Marshal(b, m, deterministic)
}
func (dst *DistributeHallTaskResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DistributeHallTaskResp.Merge(dst, src)
}
func (m *DistributeHallTaskResp) XXX_Size() int {
	return xxx_messageInfo_DistributeHallTaskResp.Size(m)
}
func (m *DistributeHallTaskResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DistributeHallTaskResp.DiscardUnknown(m)
}

var xxx_messageInfo_DistributeHallTaskResp proto.InternalMessageInfo

func (m *DistributeHallTaskResp) GetConfictList() []*HallTaskInfo {
	if m != nil {
		return m.ConfictList
	}
	return nil
}

type HallTaskInfo struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	TaskId               uint32   `protobuf:"varint,5,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	TaskGroupName        string   `protobuf:"bytes,6,opt,name=task_group_name,json=taskGroupName,proto3" json:"task_group_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HallTaskInfo) Reset()         { *m = HallTaskInfo{} }
func (m *HallTaskInfo) String() string { return proto.CompactTextString(m) }
func (*HallTaskInfo) ProtoMessage()    {}
func (*HallTaskInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{73}
}
func (m *HallTaskInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HallTaskInfo.Unmarshal(m, b)
}
func (m *HallTaskInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HallTaskInfo.Marshal(b, m, deterministic)
}
func (dst *HallTaskInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HallTaskInfo.Merge(dst, src)
}
func (m *HallTaskInfo) XXX_Size() int {
	return xxx_messageInfo_HallTaskInfo.Size(m)
}
func (m *HallTaskInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_HallTaskInfo.DiscardUnknown(m)
}

var xxx_messageInfo_HallTaskInfo proto.InternalMessageInfo

func (m *HallTaskInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *HallTaskInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *HallTaskInfo) GetTaskId() uint32 {
	if m != nil {
		return m.TaskId
	}
	return 0
}

func (m *HallTaskInfo) GetTaskGroupName() string {
	if m != nil {
		return m.TaskGroupName
	}
	return ""
}

// 查询分配任务
type GetGuildHallTaskReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	TaskGroupName        string   `protobuf:"bytes,2,opt,name=task_group_name,json=taskGroupName,proto3" json:"task_group_name,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	Page                 uint32   `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,5,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildHallTaskReq) Reset()         { *m = GetGuildHallTaskReq{} }
func (m *GetGuildHallTaskReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildHallTaskReq) ProtoMessage()    {}
func (*GetGuildHallTaskReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{74}
}
func (m *GetGuildHallTaskReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildHallTaskReq.Unmarshal(m, b)
}
func (m *GetGuildHallTaskReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildHallTaskReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildHallTaskReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildHallTaskReq.Merge(dst, src)
}
func (m *GetGuildHallTaskReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildHallTaskReq.Size(m)
}
func (m *GetGuildHallTaskReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildHallTaskReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildHallTaskReq proto.InternalMessageInfo

func (m *GetGuildHallTaskReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildHallTaskReq) GetTaskGroupName() string {
	if m != nil {
		return m.TaskGroupName
	}
	return ""
}

func (m *GetGuildHallTaskReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetGuildHallTaskReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetGuildHallTaskReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetGuildHallTaskResp struct {
	Total                uint32          `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List                 []*HallTaskInfo `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetGuildHallTaskResp) Reset()         { *m = GetGuildHallTaskResp{} }
func (m *GetGuildHallTaskResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildHallTaskResp) ProtoMessage()    {}
func (*GetGuildHallTaskResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{75}
}
func (m *GetGuildHallTaskResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildHallTaskResp.Unmarshal(m, b)
}
func (m *GetGuildHallTaskResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildHallTaskResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildHallTaskResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildHallTaskResp.Merge(dst, src)
}
func (m *GetGuildHallTaskResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildHallTaskResp.Size(m)
}
func (m *GetGuildHallTaskResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildHallTaskResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildHallTaskResp proto.InternalMessageInfo

func (m *GetGuildHallTaskResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetGuildHallTaskResp) GetList() []*HallTaskInfo {
	if m != nil {
		return m.List
	}
	return nil
}

type GetGuildHallTaskStatsReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	StatsType            uint32   `protobuf:"varint,2,opt,name=stats_type,json=statsType,proto3" json:"stats_type,omitempty"`
	BeginTs              uint32   `protobuf:"varint,3,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,4,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	Page                 uint32   `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,6,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildHallTaskStatsReq) Reset()         { *m = GetGuildHallTaskStatsReq{} }
func (m *GetGuildHallTaskStatsReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildHallTaskStatsReq) ProtoMessage()    {}
func (*GetGuildHallTaskStatsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{76}
}
func (m *GetGuildHallTaskStatsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildHallTaskStatsReq.Unmarshal(m, b)
}
func (m *GetGuildHallTaskStatsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildHallTaskStatsReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildHallTaskStatsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildHallTaskStatsReq.Merge(dst, src)
}
func (m *GetGuildHallTaskStatsReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildHallTaskStatsReq.Size(m)
}
func (m *GetGuildHallTaskStatsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildHallTaskStatsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildHallTaskStatsReq proto.InternalMessageInfo

func (m *GetGuildHallTaskStatsReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildHallTaskStatsReq) GetStatsType() uint32 {
	if m != nil {
		return m.StatsType
	}
	return 0
}

func (m *GetGuildHallTaskStatsReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *GetGuildHallTaskStatsReq) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *GetGuildHallTaskStatsReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetGuildHallTaskStatsReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetGuildHallTaskStatsResp struct {
	Total                uint32                `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List                 []*HallStatisticsInfo `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetGuildHallTaskStatsResp) Reset()         { *m = GetGuildHallTaskStatsResp{} }
func (m *GetGuildHallTaskStatsResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildHallTaskStatsResp) ProtoMessage()    {}
func (*GetGuildHallTaskStatsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{77}
}
func (m *GetGuildHallTaskStatsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildHallTaskStatsResp.Unmarshal(m, b)
}
func (m *GetGuildHallTaskStatsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildHallTaskStatsResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildHallTaskStatsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildHallTaskStatsResp.Merge(dst, src)
}
func (m *GetGuildHallTaskStatsResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildHallTaskStatsResp.Size(m)
}
func (m *GetGuildHallTaskStatsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildHallTaskStatsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildHallTaskStatsResp proto.InternalMessageInfo

func (m *GetGuildHallTaskStatsResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetGuildHallTaskStatsResp) GetList() []*HallStatisticsInfo {
	if m != nil {
		return m.List
	}
	return nil
}

type HallStatisticsInfo struct {
	Date                 string   `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	EnterChannelCnt      uint32   `protobuf:"varint,4,opt,name=enter_channel_cnt,json=enterChannelCnt,proto3" json:"enter_channel_cnt,omitempty"`
	HasTicketCnt         uint32   `protobuf:"varint,5,opt,name=has_ticket_cnt,json=hasTicketCnt,proto3" json:"has_ticket_cnt,omitempty"`
	UsedTicketCnt        uint32   `protobuf:"varint,6,opt,name=used_ticket_cnt,json=usedTicketCnt,proto3" json:"used_ticket_cnt,omitempty"`
	HoldSignCnt          uint32   `protobuf:"varint,7,opt,name=hold_sign_cnt,json=holdSignCnt,proto3" json:"hold_sign_cnt,omitempty"`
	ChannelFee           uint64   `protobuf:"varint,8,opt,name=channel_fee,json=channelFee,proto3" json:"channel_fee,omitempty"`
	RecordId             uint32   `protobuf:"varint,9,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HallStatisticsInfo) Reset()         { *m = HallStatisticsInfo{} }
func (m *HallStatisticsInfo) String() string { return proto.CompactTextString(m) }
func (*HallStatisticsInfo) ProtoMessage()    {}
func (*HallStatisticsInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{78}
}
func (m *HallStatisticsInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HallStatisticsInfo.Unmarshal(m, b)
}
func (m *HallStatisticsInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HallStatisticsInfo.Marshal(b, m, deterministic)
}
func (dst *HallStatisticsInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HallStatisticsInfo.Merge(dst, src)
}
func (m *HallStatisticsInfo) XXX_Size() int {
	return xxx_messageInfo_HallStatisticsInfo.Size(m)
}
func (m *HallStatisticsInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_HallStatisticsInfo.DiscardUnknown(m)
}

var xxx_messageInfo_HallStatisticsInfo proto.InternalMessageInfo

func (m *HallStatisticsInfo) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

func (m *HallStatisticsInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *HallStatisticsInfo) GetEnterChannelCnt() uint32 {
	if m != nil {
		return m.EnterChannelCnt
	}
	return 0
}

func (m *HallStatisticsInfo) GetHasTicketCnt() uint32 {
	if m != nil {
		return m.HasTicketCnt
	}
	return 0
}

func (m *HallStatisticsInfo) GetUsedTicketCnt() uint32 {
	if m != nil {
		return m.UsedTicketCnt
	}
	return 0
}

func (m *HallStatisticsInfo) GetHoldSignCnt() uint32 {
	if m != nil {
		return m.HoldSignCnt
	}
	return 0
}

func (m *HallStatisticsInfo) GetChannelFee() uint64 {
	if m != nil {
		return m.ChannelFee
	}
	return 0
}

func (m *HallStatisticsInfo) GetRecordId() uint32 {
	if m != nil {
		return m.RecordId
	}
	return 0
}

type GetGuildHallTaskStatsDetialReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	StatsType            uint32   `protobuf:"varint,2,opt,name=stats_type,json=statsType,proto3" json:"stats_type,omitempty"`
	RecordId             uint32   `protobuf:"varint,3,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"`
	TaskType             uint32   `protobuf:"varint,4,opt,name=task_type,json=taskType,proto3" json:"task_type,omitempty"`
	Uid                  uint32   `protobuf:"varint,5,opt,name=uid,proto3" json:"uid,omitempty"`
	Page                 uint32   `protobuf:"varint,6,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,7,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildHallTaskStatsDetialReq) Reset()         { *m = GetGuildHallTaskStatsDetialReq{} }
func (m *GetGuildHallTaskStatsDetialReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildHallTaskStatsDetialReq) ProtoMessage()    {}
func (*GetGuildHallTaskStatsDetialReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{79}
}
func (m *GetGuildHallTaskStatsDetialReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildHallTaskStatsDetialReq.Unmarshal(m, b)
}
func (m *GetGuildHallTaskStatsDetialReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildHallTaskStatsDetialReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildHallTaskStatsDetialReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildHallTaskStatsDetialReq.Merge(dst, src)
}
func (m *GetGuildHallTaskStatsDetialReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildHallTaskStatsDetialReq.Size(m)
}
func (m *GetGuildHallTaskStatsDetialReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildHallTaskStatsDetialReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildHallTaskStatsDetialReq proto.InternalMessageInfo

func (m *GetGuildHallTaskStatsDetialReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildHallTaskStatsDetialReq) GetStatsType() uint32 {
	if m != nil {
		return m.StatsType
	}
	return 0
}

func (m *GetGuildHallTaskStatsDetialReq) GetRecordId() uint32 {
	if m != nil {
		return m.RecordId
	}
	return 0
}

func (m *GetGuildHallTaskStatsDetialReq) GetTaskType() uint32 {
	if m != nil {
		return m.TaskType
	}
	return 0
}

func (m *GetGuildHallTaskStatsDetialReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetGuildHallTaskStatsDetialReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetGuildHallTaskStatsDetialReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetGuildHallTaskStatsDetialResp struct {
	Total                uint32                      `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List                 []*GuildHallTaskStatsDetial `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetGuildHallTaskStatsDetialResp) Reset()         { *m = GetGuildHallTaskStatsDetialResp{} }
func (m *GetGuildHallTaskStatsDetialResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildHallTaskStatsDetialResp) ProtoMessage()    {}
func (*GetGuildHallTaskStatsDetialResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{80}
}
func (m *GetGuildHallTaskStatsDetialResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildHallTaskStatsDetialResp.Unmarshal(m, b)
}
func (m *GetGuildHallTaskStatsDetialResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildHallTaskStatsDetialResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildHallTaskStatsDetialResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildHallTaskStatsDetialResp.Merge(dst, src)
}
func (m *GetGuildHallTaskStatsDetialResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildHallTaskStatsDetialResp.Size(m)
}
func (m *GetGuildHallTaskStatsDetialResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildHallTaskStatsDetialResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildHallTaskStatsDetialResp proto.InternalMessageInfo

func (m *GetGuildHallTaskStatsDetialResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetGuildHallTaskStatsDetialResp) GetList() []*GuildHallTaskStatsDetial {
	if m != nil {
		return m.List
	}
	return nil
}

type GuildHallTaskStatsDetial struct {
	Date                 string   `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	TaskName             string   `protobuf:"bytes,2,opt,name=task_name,json=taskName,proto3" json:"task_name,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	Val                  string   `protobuf:"bytes,6,opt,name=val,proto3" json:"val,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildHallTaskStatsDetial) Reset()         { *m = GuildHallTaskStatsDetial{} }
func (m *GuildHallTaskStatsDetial) String() string { return proto.CompactTextString(m) }
func (*GuildHallTaskStatsDetial) ProtoMessage()    {}
func (*GuildHallTaskStatsDetial) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{81}
}
func (m *GuildHallTaskStatsDetial) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildHallTaskStatsDetial.Unmarshal(m, b)
}
func (m *GuildHallTaskStatsDetial) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildHallTaskStatsDetial.Marshal(b, m, deterministic)
}
func (dst *GuildHallTaskStatsDetial) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildHallTaskStatsDetial.Merge(dst, src)
}
func (m *GuildHallTaskStatsDetial) XXX_Size() int {
	return xxx_messageInfo_GuildHallTaskStatsDetial.Size(m)
}
func (m *GuildHallTaskStatsDetial) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildHallTaskStatsDetial.DiscardUnknown(m)
}

var xxx_messageInfo_GuildHallTaskStatsDetial proto.InternalMessageInfo

func (m *GuildHallTaskStatsDetial) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

func (m *GuildHallTaskStatsDetial) GetTaskName() string {
	if m != nil {
		return m.TaskName
	}
	return ""
}

func (m *GuildHallTaskStatsDetial) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GuildHallTaskStatsDetial) GetVal() string {
	if m != nil {
		return m.Val
	}
	return ""
}

type GetHallTaskReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetHallTaskReq) Reset()         { *m = GetHallTaskReq{} }
func (m *GetHallTaskReq) String() string { return proto.CompactTextString(m) }
func (*GetHallTaskReq) ProtoMessage()    {}
func (*GetHallTaskReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{82}
}
func (m *GetHallTaskReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHallTaskReq.Unmarshal(m, b)
}
func (m *GetHallTaskReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHallTaskReq.Marshal(b, m, deterministic)
}
func (dst *GetHallTaskReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHallTaskReq.Merge(dst, src)
}
func (m *GetHallTaskReq) XXX_Size() int {
	return xxx_messageInfo_GetHallTaskReq.Size(m)
}
func (m *GetHallTaskReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHallTaskReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetHallTaskReq proto.InternalMessageInfo

func (m *GetHallTaskReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetHallTaskReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetHallTaskResp struct {
	TaskId               uint32            `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	TaskGroupName        string            `protobuf:"bytes,2,opt,name=task_group_name,json=taskGroupName,proto3" json:"task_group_name,omitempty"`
	RewardMsg            string            `protobuf:"bytes,3,opt,name=reward_msg,json=rewardMsg,proto3" json:"reward_msg,omitempty"`
	DayTaskList          []*HallTaskDetial `protobuf:"bytes,4,rep,name=day_task_list,json=dayTaskList,proto3" json:"day_task_list,omitempty"`
	WeekTaskList         []*HallTaskDetial `protobuf:"bytes,5,rep,name=week_task_list,json=weekTaskList,proto3" json:"week_task_list,omitempty"`
	JumpUrl              string            `protobuf:"bytes,6,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetHallTaskResp) Reset()         { *m = GetHallTaskResp{} }
func (m *GetHallTaskResp) String() string { return proto.CompactTextString(m) }
func (*GetHallTaskResp) ProtoMessage()    {}
func (*GetHallTaskResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{83}
}
func (m *GetHallTaskResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHallTaskResp.Unmarshal(m, b)
}
func (m *GetHallTaskResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHallTaskResp.Marshal(b, m, deterministic)
}
func (dst *GetHallTaskResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHallTaskResp.Merge(dst, src)
}
func (m *GetHallTaskResp) XXX_Size() int {
	return xxx_messageInfo_GetHallTaskResp.Size(m)
}
func (m *GetHallTaskResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHallTaskResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetHallTaskResp proto.InternalMessageInfo

func (m *GetHallTaskResp) GetTaskId() uint32 {
	if m != nil {
		return m.TaskId
	}
	return 0
}

func (m *GetHallTaskResp) GetTaskGroupName() string {
	if m != nil {
		return m.TaskGroupName
	}
	return ""
}

func (m *GetHallTaskResp) GetRewardMsg() string {
	if m != nil {
		return m.RewardMsg
	}
	return ""
}

func (m *GetHallTaskResp) GetDayTaskList() []*HallTaskDetial {
	if m != nil {
		return m.DayTaskList
	}
	return nil
}

func (m *GetHallTaskResp) GetWeekTaskList() []*HallTaskDetial {
	if m != nil {
		return m.WeekTaskList
	}
	return nil
}

func (m *GetHallTaskResp) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

type HallTaskDetial struct {
	TaskName             string    `protobuf:"bytes,1,opt,name=task_name,json=taskName,proto3" json:"task_name,omitempty"`
	TaskProgress         string    `protobuf:"bytes,2,opt,name=task_progress,json=taskProgress,proto3" json:"task_progress,omitempty"`
	Rate                 float32   `protobuf:"fixed32,3,opt,name=rate,proto3" json:"rate,omitempty"`
	TaskVal              uint32    `protobuf:"varint,4,opt,name=task_val,json=taskVal,proto3" json:"task_val,omitempty"`
	ValList              []float32 `protobuf:"fixed32,5,rep,packed,name=val_list,json=valList,proto3" json:"val_list,omitempty"`
	Date                 string    `protobuf:"bytes,6,opt,name=date,proto3" json:"date,omitempty"`
	TaskType             uint32    `protobuf:"varint,7,opt,name=task_type,json=taskType,proto3" json:"task_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *HallTaskDetial) Reset()         { *m = HallTaskDetial{} }
func (m *HallTaskDetial) String() string { return proto.CompactTextString(m) }
func (*HallTaskDetial) ProtoMessage()    {}
func (*HallTaskDetial) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{84}
}
func (m *HallTaskDetial) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HallTaskDetial.Unmarshal(m, b)
}
func (m *HallTaskDetial) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HallTaskDetial.Marshal(b, m, deterministic)
}
func (dst *HallTaskDetial) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HallTaskDetial.Merge(dst, src)
}
func (m *HallTaskDetial) XXX_Size() int {
	return xxx_messageInfo_HallTaskDetial.Size(m)
}
func (m *HallTaskDetial) XXX_DiscardUnknown() {
	xxx_messageInfo_HallTaskDetial.DiscardUnknown(m)
}

var xxx_messageInfo_HallTaskDetial proto.InternalMessageInfo

func (m *HallTaskDetial) GetTaskName() string {
	if m != nil {
		return m.TaskName
	}
	return ""
}

func (m *HallTaskDetial) GetTaskProgress() string {
	if m != nil {
		return m.TaskProgress
	}
	return ""
}

func (m *HallTaskDetial) GetRate() float32 {
	if m != nil {
		return m.Rate
	}
	return 0
}

func (m *HallTaskDetial) GetTaskVal() uint32 {
	if m != nil {
		return m.TaskVal
	}
	return 0
}

func (m *HallTaskDetial) GetValList() []float32 {
	if m != nil {
		return m.ValList
	}
	return nil
}

func (m *HallTaskDetial) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

func (m *HallTaskDetial) GetTaskType() uint32 {
	if m != nil {
		return m.TaskType
	}
	return 0
}

type GetHallTaskHistoryReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetHallTaskHistoryReq) Reset()         { *m = GetHallTaskHistoryReq{} }
func (m *GetHallTaskHistoryReq) String() string { return proto.CompactTextString(m) }
func (*GetHallTaskHistoryReq) ProtoMessage()    {}
func (*GetHallTaskHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{85}
}
func (m *GetHallTaskHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHallTaskHistoryReq.Unmarshal(m, b)
}
func (m *GetHallTaskHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHallTaskHistoryReq.Marshal(b, m, deterministic)
}
func (dst *GetHallTaskHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHallTaskHistoryReq.Merge(dst, src)
}
func (m *GetHallTaskHistoryReq) XXX_Size() int {
	return xxx_messageInfo_GetHallTaskHistoryReq.Size(m)
}
func (m *GetHallTaskHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHallTaskHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetHallTaskHistoryReq proto.InternalMessageInfo

func (m *GetHallTaskHistoryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetHallTaskHistoryResp struct {
	List                 []*HallTaskHistoryDetial `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetHallTaskHistoryResp) Reset()         { *m = GetHallTaskHistoryResp{} }
func (m *GetHallTaskHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetHallTaskHistoryResp) ProtoMessage()    {}
func (*GetHallTaskHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{86}
}
func (m *GetHallTaskHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHallTaskHistoryResp.Unmarshal(m, b)
}
func (m *GetHallTaskHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHallTaskHistoryResp.Marshal(b, m, deterministic)
}
func (dst *GetHallTaskHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHallTaskHistoryResp.Merge(dst, src)
}
func (m *GetHallTaskHistoryResp) XXX_Size() int {
	return xxx_messageInfo_GetHallTaskHistoryResp.Size(m)
}
func (m *GetHallTaskHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHallTaskHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetHallTaskHistoryResp proto.InternalMessageInfo

func (m *GetHallTaskHistoryResp) GetList() []*HallTaskHistoryDetial {
	if m != nil {
		return m.List
	}
	return nil
}

type HallTaskHistoryDetial struct {
	Date                 string            `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	DayTaskList          []*HallTaskDetial `protobuf:"bytes,2,rep,name=day_task_list,json=dayTaskList,proto3" json:"day_task_list,omitempty"`
	WeekTaskList         []*HallTaskDetial `protobuf:"bytes,3,rep,name=week_task_list,json=weekTaskList,proto3" json:"week_task_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *HallTaskHistoryDetial) Reset()         { *m = HallTaskHistoryDetial{} }
func (m *HallTaskHistoryDetial) String() string { return proto.CompactTextString(m) }
func (*HallTaskHistoryDetial) ProtoMessage()    {}
func (*HallTaskHistoryDetial) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{87}
}
func (m *HallTaskHistoryDetial) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HallTaskHistoryDetial.Unmarshal(m, b)
}
func (m *HallTaskHistoryDetial) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HallTaskHistoryDetial.Marshal(b, m, deterministic)
}
func (dst *HallTaskHistoryDetial) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HallTaskHistoryDetial.Merge(dst, src)
}
func (m *HallTaskHistoryDetial) XXX_Size() int {
	return xxx_messageInfo_HallTaskHistoryDetial.Size(m)
}
func (m *HallTaskHistoryDetial) XXX_DiscardUnknown() {
	xxx_messageInfo_HallTaskHistoryDetial.DiscardUnknown(m)
}

var xxx_messageInfo_HallTaskHistoryDetial proto.InternalMessageInfo

func (m *HallTaskHistoryDetial) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

func (m *HallTaskHistoryDetial) GetDayTaskList() []*HallTaskDetial {
	if m != nil {
		return m.DayTaskList
	}
	return nil
}

func (m *HallTaskHistoryDetial) GetWeekTaskList() []*HallTaskDetial {
	if m != nil {
		return m.WeekTaskList
	}
	return nil
}

type GetHallTaskDistributeHistoryReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetHallTaskDistributeHistoryReq) Reset()         { *m = GetHallTaskDistributeHistoryReq{} }
func (m *GetHallTaskDistributeHistoryReq) String() string { return proto.CompactTextString(m) }
func (*GetHallTaskDistributeHistoryReq) ProtoMessage()    {}
func (*GetHallTaskDistributeHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{88}
}
func (m *GetHallTaskDistributeHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHallTaskDistributeHistoryReq.Unmarshal(m, b)
}
func (m *GetHallTaskDistributeHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHallTaskDistributeHistoryReq.Marshal(b, m, deterministic)
}
func (dst *GetHallTaskDistributeHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHallTaskDistributeHistoryReq.Merge(dst, src)
}
func (m *GetHallTaskDistributeHistoryReq) XXX_Size() int {
	return xxx_messageInfo_GetHallTaskDistributeHistoryReq.Size(m)
}
func (m *GetHallTaskDistributeHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHallTaskDistributeHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetHallTaskDistributeHistoryReq proto.InternalMessageInfo

func (m *GetHallTaskDistributeHistoryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetHallTaskDistributeHistoryResp struct {
	List                 []*GetHallTaskDistributeHistory `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *GetHallTaskDistributeHistoryResp) Reset()         { *m = GetHallTaskDistributeHistoryResp{} }
func (m *GetHallTaskDistributeHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetHallTaskDistributeHistoryResp) ProtoMessage()    {}
func (*GetHallTaskDistributeHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{89}
}
func (m *GetHallTaskDistributeHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHallTaskDistributeHistoryResp.Unmarshal(m, b)
}
func (m *GetHallTaskDistributeHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHallTaskDistributeHistoryResp.Marshal(b, m, deterministic)
}
func (dst *GetHallTaskDistributeHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHallTaskDistributeHistoryResp.Merge(dst, src)
}
func (m *GetHallTaskDistributeHistoryResp) XXX_Size() int {
	return xxx_messageInfo_GetHallTaskDistributeHistoryResp.Size(m)
}
func (m *GetHallTaskDistributeHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHallTaskDistributeHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetHallTaskDistributeHistoryResp proto.InternalMessageInfo

func (m *GetHallTaskDistributeHistoryResp) GetList() []*GetHallTaskDistributeHistory {
	if m != nil {
		return m.List
	}
	return nil
}

type GetHallTaskDistributeHistory struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SignGuildId          uint32   `protobuf:"varint,2,opt,name=sign_guild_id,json=signGuildId,proto3" json:"sign_guild_id,omitempty"`
	GuildId              uint32   `protobuf:"varint,3,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	TaskId               uint32   `protobuf:"varint,4,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	OpUid                uint32   `protobuf:"varint,5,opt,name=op_uid,json=opUid,proto3" json:"op_uid,omitempty"`
	ChangeType           uint32   `protobuf:"varint,6,opt,name=change_type,json=changeType,proto3" json:"change_type,omitempty"`
	CreateTime           uint32   `protobuf:"varint,7,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetHallTaskDistributeHistory) Reset()         { *m = GetHallTaskDistributeHistory{} }
func (m *GetHallTaskDistributeHistory) String() string { return proto.CompactTextString(m) }
func (*GetHallTaskDistributeHistory) ProtoMessage()    {}
func (*GetHallTaskDistributeHistory) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{90}
}
func (m *GetHallTaskDistributeHistory) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHallTaskDistributeHistory.Unmarshal(m, b)
}
func (m *GetHallTaskDistributeHistory) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHallTaskDistributeHistory.Marshal(b, m, deterministic)
}
func (dst *GetHallTaskDistributeHistory) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHallTaskDistributeHistory.Merge(dst, src)
}
func (m *GetHallTaskDistributeHistory) XXX_Size() int {
	return xxx_messageInfo_GetHallTaskDistributeHistory.Size(m)
}
func (m *GetHallTaskDistributeHistory) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHallTaskDistributeHistory.DiscardUnknown(m)
}

var xxx_messageInfo_GetHallTaskDistributeHistory proto.InternalMessageInfo

func (m *GetHallTaskDistributeHistory) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetHallTaskDistributeHistory) GetSignGuildId() uint32 {
	if m != nil {
		return m.SignGuildId
	}
	return 0
}

func (m *GetHallTaskDistributeHistory) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetHallTaskDistributeHistory) GetTaskId() uint32 {
	if m != nil {
		return m.TaskId
	}
	return 0
}

func (m *GetHallTaskDistributeHistory) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *GetHallTaskDistributeHistory) GetChangeType() uint32 {
	if m != nil {
		return m.ChangeType
	}
	return 0
}

func (m *GetHallTaskDistributeHistory) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

type DelHallTaskReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	OpUid                uint32   `protobuf:"varint,3,opt,name=op_uid,json=opUid,proto3" json:"op_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelHallTaskReq) Reset()         { *m = DelHallTaskReq{} }
func (m *DelHallTaskReq) String() string { return proto.CompactTextString(m) }
func (*DelHallTaskReq) ProtoMessage()    {}
func (*DelHallTaskReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{91}
}
func (m *DelHallTaskReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelHallTaskReq.Unmarshal(m, b)
}
func (m *DelHallTaskReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelHallTaskReq.Marshal(b, m, deterministic)
}
func (dst *DelHallTaskReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelHallTaskReq.Merge(dst, src)
}
func (m *DelHallTaskReq) XXX_Size() int {
	return xxx_messageInfo_DelHallTaskReq.Size(m)
}
func (m *DelHallTaskReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelHallTaskReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelHallTaskReq proto.InternalMessageInfo

func (m *DelHallTaskReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DelHallTaskReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *DelHallTaskReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

type DelHallTaskResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelHallTaskResp) Reset()         { *m = DelHallTaskResp{} }
func (m *DelHallTaskResp) String() string { return proto.CompactTextString(m) }
func (*DelHallTaskResp) ProtoMessage()    {}
func (*DelHallTaskResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{92}
}
func (m *DelHallTaskResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelHallTaskResp.Unmarshal(m, b)
}
func (m *DelHallTaskResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelHallTaskResp.Marshal(b, m, deterministic)
}
func (dst *DelHallTaskResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelHallTaskResp.Merge(dst, src)
}
func (m *DelHallTaskResp) XXX_Size() int {
	return xxx_messageInfo_DelHallTaskResp.Size(m)
}
func (m *DelHallTaskResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelHallTaskResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelHallTaskResp proto.InternalMessageInfo

// 大厅任务缓存结构
type HallTaskCacheInfo struct {
	Uid                  uint32       `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32       `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	TaskId               uint32       `protobuf:"varint,3,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	DayTaskVal           *HallTaskVal `protobuf:"bytes,4,opt,name=day_task_val,json=dayTaskVal,proto3" json:"day_task_val,omitempty"`
	WeekTaskVal          *HallTaskVal `protobuf:"bytes,5,opt,name=week_task_val,json=weekTaskVal,proto3" json:"week_task_val,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *HallTaskCacheInfo) Reset()         { *m = HallTaskCacheInfo{} }
func (m *HallTaskCacheInfo) String() string { return proto.CompactTextString(m) }
func (*HallTaskCacheInfo) ProtoMessage()    {}
func (*HallTaskCacheInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{93}
}
func (m *HallTaskCacheInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HallTaskCacheInfo.Unmarshal(m, b)
}
func (m *HallTaskCacheInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HallTaskCacheInfo.Marshal(b, m, deterministic)
}
func (dst *HallTaskCacheInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HallTaskCacheInfo.Merge(dst, src)
}
func (m *HallTaskCacheInfo) XXX_Size() int {
	return xxx_messageInfo_HallTaskCacheInfo.Size(m)
}
func (m *HallTaskCacheInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_HallTaskCacheInfo.DiscardUnknown(m)
}

var xxx_messageInfo_HallTaskCacheInfo proto.InternalMessageInfo

func (m *HallTaskCacheInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *HallTaskCacheInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *HallTaskCacheInfo) GetTaskId() uint32 {
	if m != nil {
		return m.TaskId
	}
	return 0
}

func (m *HallTaskCacheInfo) GetDayTaskVal() *HallTaskVal {
	if m != nil {
		return m.DayTaskVal
	}
	return nil
}

func (m *HallTaskCacheInfo) GetWeekTaskVal() *HallTaskVal {
	if m != nil {
		return m.WeekTaskVal
	}
	return nil
}

// 各种值，共用一个msg
type HallTaskVal struct {
	ValidSec             uint32   `protobuf:"varint,1,opt,name=valid_sec,json=validSec,proto3" json:"valid_sec,omitempty"`
	ValidOpenCnt         uint32   `protobuf:"varint,2,opt,name=valid_open_cnt,json=validOpenCnt,proto3" json:"valid_open_cnt,omitempty"`
	TicketCnt            uint32   `protobuf:"varint,3,opt,name=ticket_cnt,json=ticketCnt,proto3" json:"ticket_cnt,omitempty"`
	Income               uint32   `protobuf:"varint,4,opt,name=income,proto3" json:"income,omitempty"`
	ValidHoldDay         uint32   `protobuf:"varint,5,opt,name=valid_hold_day,json=validHoldDay,proto3" json:"valid_hold_day,omitempty"`
	FinishDayTaskCnt     uint32   `protobuf:"varint,6,opt,name=finish_day_task_cnt,json=finishDayTaskCnt,proto3" json:"finish_day_task_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HallTaskVal) Reset()         { *m = HallTaskVal{} }
func (m *HallTaskVal) String() string { return proto.CompactTextString(m) }
func (*HallTaskVal) ProtoMessage()    {}
func (*HallTaskVal) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{94}
}
func (m *HallTaskVal) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HallTaskVal.Unmarshal(m, b)
}
func (m *HallTaskVal) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HallTaskVal.Marshal(b, m, deterministic)
}
func (dst *HallTaskVal) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HallTaskVal.Merge(dst, src)
}
func (m *HallTaskVal) XXX_Size() int {
	return xxx_messageInfo_HallTaskVal.Size(m)
}
func (m *HallTaskVal) XXX_DiscardUnknown() {
	xxx_messageInfo_HallTaskVal.DiscardUnknown(m)
}

var xxx_messageInfo_HallTaskVal proto.InternalMessageInfo

func (m *HallTaskVal) GetValidSec() uint32 {
	if m != nil {
		return m.ValidSec
	}
	return 0
}

func (m *HallTaskVal) GetValidOpenCnt() uint32 {
	if m != nil {
		return m.ValidOpenCnt
	}
	return 0
}

func (m *HallTaskVal) GetTicketCnt() uint32 {
	if m != nil {
		return m.TicketCnt
	}
	return 0
}

func (m *HallTaskVal) GetIncome() uint32 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *HallTaskVal) GetValidHoldDay() uint32 {
	if m != nil {
		return m.ValidHoldDay
	}
	return 0
}

func (m *HallTaskVal) GetFinishDayTaskCnt() uint32 {
	if m != nil {
		return m.FinishDayTaskCnt
	}
	return 0
}

type GetHallTaskConfByIdReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	TaskId               uint32   `protobuf:"varint,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	QueryType            uint32   `protobuf:"varint,3,opt,name=query_type,json=queryType,proto3" json:"query_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetHallTaskConfByIdReq) Reset()         { *m = GetHallTaskConfByIdReq{} }
func (m *GetHallTaskConfByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetHallTaskConfByIdReq) ProtoMessage()    {}
func (*GetHallTaskConfByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{95}
}
func (m *GetHallTaskConfByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHallTaskConfByIdReq.Unmarshal(m, b)
}
func (m *GetHallTaskConfByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHallTaskConfByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetHallTaskConfByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHallTaskConfByIdReq.Merge(dst, src)
}
func (m *GetHallTaskConfByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetHallTaskConfByIdReq.Size(m)
}
func (m *GetHallTaskConfByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHallTaskConfByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetHallTaskConfByIdReq proto.InternalMessageInfo

func (m *GetHallTaskConfByIdReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetHallTaskConfByIdReq) GetTaskId() uint32 {
	if m != nil {
		return m.TaskId
	}
	return 0
}

func (m *GetHallTaskConfByIdReq) GetQueryType() uint32 {
	if m != nil {
		return m.QueryType
	}
	return 0
}

type GetHallTaskConfByIdResp struct {
	Info                 *HallTaskConfDetialInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetHallTaskConfByIdResp) Reset()         { *m = GetHallTaskConfByIdResp{} }
func (m *GetHallTaskConfByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetHallTaskConfByIdResp) ProtoMessage()    {}
func (*GetHallTaskConfByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{96}
}
func (m *GetHallTaskConfByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHallTaskConfByIdResp.Unmarshal(m, b)
}
func (m *GetHallTaskConfByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHallTaskConfByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetHallTaskConfByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHallTaskConfByIdResp.Merge(dst, src)
}
func (m *GetHallTaskConfByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetHallTaskConfByIdResp.Size(m)
}
func (m *GetHallTaskConfByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHallTaskConfByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetHallTaskConfByIdResp proto.InternalMessageInfo

func (m *GetHallTaskConfByIdResp) GetInfo() *HallTaskConfDetialInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type GetValidHoldDayUidReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	BeginTs              uint32   `protobuf:"varint,2,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,3,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetValidHoldDayUidReq) Reset()         { *m = GetValidHoldDayUidReq{} }
func (m *GetValidHoldDayUidReq) String() string { return proto.CompactTextString(m) }
func (*GetValidHoldDayUidReq) ProtoMessage()    {}
func (*GetValidHoldDayUidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{97}
}
func (m *GetValidHoldDayUidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetValidHoldDayUidReq.Unmarshal(m, b)
}
func (m *GetValidHoldDayUidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetValidHoldDayUidReq.Marshal(b, m, deterministic)
}
func (dst *GetValidHoldDayUidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetValidHoldDayUidReq.Merge(dst, src)
}
func (m *GetValidHoldDayUidReq) XXX_Size() int {
	return xxx_messageInfo_GetValidHoldDayUidReq.Size(m)
}
func (m *GetValidHoldDayUidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetValidHoldDayUidReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetValidHoldDayUidReq proto.InternalMessageInfo

func (m *GetValidHoldDayUidReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetValidHoldDayUidReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *GetValidHoldDayUidReq) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

type GetValidHoldDayUidResp struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetValidHoldDayUidResp) Reset()         { *m = GetValidHoldDayUidResp{} }
func (m *GetValidHoldDayUidResp) String() string { return proto.CompactTextString(m) }
func (*GetValidHoldDayUidResp) ProtoMessage()    {}
func (*GetValidHoldDayUidResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{98}
}
func (m *GetValidHoldDayUidResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetValidHoldDayUidResp.Unmarshal(m, b)
}
func (m *GetValidHoldDayUidResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetValidHoldDayUidResp.Marshal(b, m, deterministic)
}
func (dst *GetValidHoldDayUidResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetValidHoldDayUidResp.Merge(dst, src)
}
func (m *GetValidHoldDayUidResp) XXX_Size() int {
	return xxx_messageInfo_GetValidHoldDayUidResp.Size(m)
}
func (m *GetValidHoldDayUidResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetValidHoldDayUidResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetValidHoldDayUidResp proto.InternalMessageInfo

func (m *GetValidHoldDayUidResp) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

// 检查公会的子母关系
type GetBindGuildInfoReq struct {
	GuildA               uint32   `protobuf:"varint,1,opt,name=guild_a,json=guildA,proto3" json:"guild_a,omitempty"`
	GuildB               uint32   `protobuf:"varint,2,opt,name=guild_b,json=guildB,proto3" json:"guild_b,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBindGuildInfoReq) Reset()         { *m = GetBindGuildInfoReq{} }
func (m *GetBindGuildInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetBindGuildInfoReq) ProtoMessage()    {}
func (*GetBindGuildInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{99}
}
func (m *GetBindGuildInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBindGuildInfoReq.Unmarshal(m, b)
}
func (m *GetBindGuildInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBindGuildInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetBindGuildInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBindGuildInfoReq.Merge(dst, src)
}
func (m *GetBindGuildInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetBindGuildInfoReq.Size(m)
}
func (m *GetBindGuildInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBindGuildInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBindGuildInfoReq proto.InternalMessageInfo

func (m *GetBindGuildInfoReq) GetGuildA() uint32 {
	if m != nil {
		return m.GuildA
	}
	return 0
}

func (m *GetBindGuildInfoReq) GetGuildB() uint32 {
	if m != nil {
		return m.GuildB
	}
	return 0
}

type GetBindGuildInfoResp struct {
	IsBind               bool     `protobuf:"varint,1,opt,name=is_bind,json=isBind,proto3" json:"is_bind,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBindGuildInfoResp) Reset()         { *m = GetBindGuildInfoResp{} }
func (m *GetBindGuildInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetBindGuildInfoResp) ProtoMessage()    {}
func (*GetBindGuildInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{100}
}
func (m *GetBindGuildInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBindGuildInfoResp.Unmarshal(m, b)
}
func (m *GetBindGuildInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBindGuildInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetBindGuildInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBindGuildInfoResp.Merge(dst, src)
}
func (m *GetBindGuildInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetBindGuildInfoResp.Size(m)
}
func (m *GetBindGuildInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBindGuildInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBindGuildInfoResp proto.InternalMessageInfo

func (m *GetBindGuildInfoResp) GetIsBind() bool {
	if m != nil {
		return m.IsBind
	}
	return false
}

type GuildAb struct {
	GuildA               uint32   `protobuf:"varint,1,opt,name=guild_a,json=guildA,proto3" json:"guild_a,omitempty"`
	GuildB               uint32   `protobuf:"varint,2,opt,name=guild_b,json=guildB,proto3" json:"guild_b,omitempty"`
	IsBind               bool     `protobuf:"varint,3,opt,name=is_bind,json=isBind,proto3" json:"is_bind,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildAb) Reset()         { *m = GuildAb{} }
func (m *GuildAb) String() string { return proto.CompactTextString(m) }
func (*GuildAb) ProtoMessage()    {}
func (*GuildAb) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{101}
}
func (m *GuildAb) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildAb.Unmarshal(m, b)
}
func (m *GuildAb) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildAb.Marshal(b, m, deterministic)
}
func (dst *GuildAb) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildAb.Merge(dst, src)
}
func (m *GuildAb) XXX_Size() int {
	return xxx_messageInfo_GuildAb.Size(m)
}
func (m *GuildAb) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildAb.DiscardUnknown(m)
}

var xxx_messageInfo_GuildAb proto.InternalMessageInfo

func (m *GuildAb) GetGuildA() uint32 {
	if m != nil {
		return m.GuildA
	}
	return 0
}

func (m *GuildAb) GetGuildB() uint32 {
	if m != nil {
		return m.GuildB
	}
	return 0
}

func (m *GuildAb) GetIsBind() bool {
	if m != nil {
		return m.IsBind
	}
	return false
}

// 批量获取公会绑定关系
type BatchGetBindGuildInfoReq struct {
	List                 []*GuildAb `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *BatchGetBindGuildInfoReq) Reset()         { *m = BatchGetBindGuildInfoReq{} }
func (m *BatchGetBindGuildInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetBindGuildInfoReq) ProtoMessage()    {}
func (*BatchGetBindGuildInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{102}
}
func (m *BatchGetBindGuildInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetBindGuildInfoReq.Unmarshal(m, b)
}
func (m *BatchGetBindGuildInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetBindGuildInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetBindGuildInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetBindGuildInfoReq.Merge(dst, src)
}
func (m *BatchGetBindGuildInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetBindGuildInfoReq.Size(m)
}
func (m *BatchGetBindGuildInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetBindGuildInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetBindGuildInfoReq proto.InternalMessageInfo

func (m *BatchGetBindGuildInfoReq) GetList() []*GuildAb {
	if m != nil {
		return m.List
	}
	return nil
}

type BatchGetBindGuildInfoResp struct {
	List                 []*GuildAb `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *BatchGetBindGuildInfoResp) Reset()         { *m = BatchGetBindGuildInfoResp{} }
func (m *BatchGetBindGuildInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetBindGuildInfoResp) ProtoMessage()    {}
func (*BatchGetBindGuildInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{103}
}
func (m *BatchGetBindGuildInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetBindGuildInfoResp.Unmarshal(m, b)
}
func (m *BatchGetBindGuildInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetBindGuildInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetBindGuildInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetBindGuildInfoResp.Merge(dst, src)
}
func (m *BatchGetBindGuildInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetBindGuildInfoResp.Size(m)
}
func (m *BatchGetBindGuildInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetBindGuildInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetBindGuildInfoResp proto.InternalMessageInfo

func (m *BatchGetBindGuildInfoResp) GetList() []*GuildAb {
	if m != nil {
		return m.List
	}
	return nil
}

// 配置里面，会设置自己是自己的子公会，使用的时候需要注意
type BindGuildInfo struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	BindGuildId          uint32   `protobuf:"varint,2,opt,name=bind_guild_id,json=bindGuildId,proto3" json:"bind_guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BindGuildInfo) Reset()         { *m = BindGuildInfo{} }
func (m *BindGuildInfo) String() string { return proto.CompactTextString(m) }
func (*BindGuildInfo) ProtoMessage()    {}
func (*BindGuildInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{104}
}
func (m *BindGuildInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BindGuildInfo.Unmarshal(m, b)
}
func (m *BindGuildInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BindGuildInfo.Marshal(b, m, deterministic)
}
func (dst *BindGuildInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BindGuildInfo.Merge(dst, src)
}
func (m *BindGuildInfo) XXX_Size() int {
	return xxx_messageInfo_BindGuildInfo.Size(m)
}
func (m *BindGuildInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BindGuildInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BindGuildInfo proto.InternalMessageInfo

func (m *BindGuildInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *BindGuildInfo) GetBindGuildId() uint32 {
	if m != nil {
		return m.BindGuildId
	}
	return 0
}

// 获取子母公会配置列表
type GetBindGuildInfoListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBindGuildInfoListReq) Reset()         { *m = GetBindGuildInfoListReq{} }
func (m *GetBindGuildInfoListReq) String() string { return proto.CompactTextString(m) }
func (*GetBindGuildInfoListReq) ProtoMessage()    {}
func (*GetBindGuildInfoListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{105}
}
func (m *GetBindGuildInfoListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBindGuildInfoListReq.Unmarshal(m, b)
}
func (m *GetBindGuildInfoListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBindGuildInfoListReq.Marshal(b, m, deterministic)
}
func (dst *GetBindGuildInfoListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBindGuildInfoListReq.Merge(dst, src)
}
func (m *GetBindGuildInfoListReq) XXX_Size() int {
	return xxx_messageInfo_GetBindGuildInfoListReq.Size(m)
}
func (m *GetBindGuildInfoListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBindGuildInfoListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBindGuildInfoListReq proto.InternalMessageInfo

type GetBindGuildInfoListResp struct {
	InfoList             []*BindGuildInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetBindGuildInfoListResp) Reset()         { *m = GetBindGuildInfoListResp{} }
func (m *GetBindGuildInfoListResp) String() string { return proto.CompactTextString(m) }
func (*GetBindGuildInfoListResp) ProtoMessage()    {}
func (*GetBindGuildInfoListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5, []int{106}
}
func (m *GetBindGuildInfoListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBindGuildInfoListResp.Unmarshal(m, b)
}
func (m *GetBindGuildInfoListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBindGuildInfoListResp.Marshal(b, m, deterministic)
}
func (dst *GetBindGuildInfoListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBindGuildInfoListResp.Merge(dst, src)
}
func (m *GetBindGuildInfoListResp) XXX_Size() int {
	return xxx_messageInfo_GetBindGuildInfoListResp.Size(m)
}
func (m *GetBindGuildInfoListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBindGuildInfoListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBindGuildInfoListResp proto.InternalMessageInfo

func (m *GetBindGuildInfoListResp) GetInfoList() []*BindGuildInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func init() {
	proto.RegisterType((*GetMultiThisMonthChannelStatReq)(nil), "sign_anchor_stats.GetMultiThisMonthChannelStatReq")
	proto.RegisterType((*MultiThisMonthChannelStatItem)(nil), "sign_anchor_stats.MultiThisMonthChannelStatItem")
	proto.RegisterType((*GetMultiThisMonthChannelStatResp)(nil), "sign_anchor_stats.GetMultiThisMonthChannelStatResp")
	proto.RegisterType((*GetMultiAnchorMonthStatReq)(nil), "sign_anchor_stats.GetMultiAnchorMonthStatReq")
	proto.RegisterType((*MultiAnchorMonthStatItem)(nil), "sign_anchor_stats.MultiAnchorMonthStatItem")
	proto.RegisterType((*GetMultiAnchorMonthStatResp)(nil), "sign_anchor_stats.GetMultiAnchorMonthStatResp")
	proto.RegisterType((*GetMultiAnchorChannelStatReq)(nil), "sign_anchor_stats.GetMultiAnchorChannelStatReq")
	proto.RegisterType((*MultiAnchorChannelStatItem)(nil), "sign_anchor_stats.MultiAnchorChannelStatItem")
	proto.RegisterType((*GetMultiAnchorChannelStatResp)(nil), "sign_anchor_stats.GetMultiAnchorChannelStatResp")
	proto.RegisterType((*GetUserTbeanConsumeReq)(nil), "sign_anchor_stats.GetUserTbeanConsumeReq")
	proto.RegisterType((*GetUserTbeanConsumeResp)(nil), "sign_anchor_stats.GetUserTbeanConsumeResp")
	proto.RegisterType((*GetMultiAnchorDailyStatsListByGuildIdReq)(nil), "sign_anchor_stats.GetMultiAnchorDailyStatsListByGuildIdReq")
	proto.RegisterType((*GetMultiAnchorDailyStatsListByGuildIdResp)(nil), "sign_anchor_stats.GetMultiAnchorDailyStatsListByGuildIdResp")
	proto.RegisterType((*MultiAnchorDailyStats)(nil), "sign_anchor_stats.MultiAnchorDailyStats")
	proto.RegisterType((*GetMultiAnchorDailyStatsListReq)(nil), "sign_anchor_stats.GetMultiAnchorDailyStatsListReq")
	proto.RegisterType((*GetMultiAnchorDailyStatsListResp)(nil), "sign_anchor_stats.GetMultiAnchorDailyStatsListResp")
	proto.RegisterType((*PgcMonthlyInfo)(nil), "sign_anchor_stats.PgcMonthlyInfo")
	proto.RegisterType((*GetPgcMonthlyInfoListReq)(nil), "sign_anchor_stats.GetPgcMonthlyInfoListReq")
	proto.RegisterType((*GetPgcMonthlyInfoListResp)(nil), "sign_anchor_stats.GetPgcMonthlyInfoListResp")
	proto.RegisterType((*PgcDailyInfo)(nil), "sign_anchor_stats.PgcDailyInfo")
	proto.RegisterType((*GetPgcDailyInfoListReq)(nil), "sign_anchor_stats.GetPgcDailyInfoListReq")
	proto.RegisterType((*GetPgcDailyInfoListResp)(nil), "sign_anchor_stats.GetPgcDailyInfoListResp")
	proto.RegisterType((*GuildMonthlyStatsInfo)(nil), "sign_anchor_stats.GuildMonthlyStatsInfo")
	proto.RegisterType((*GetGuildMonthlyStatsInfoListReq)(nil), "sign_anchor_stats.GetGuildMonthlyStatsInfoListReq")
	proto.RegisterType((*GetGuildMonthlyStatsInfoListResp)(nil), "sign_anchor_stats.GetGuildMonthlyStatsInfoListResp")
	proto.RegisterType((*GetMultiPlayerHomepageReq)(nil), "sign_anchor_stats.GetMultiPlayerHomepageReq")
	proto.RegisterType((*GetMultiPlayerHomepageResp)(nil), "sign_anchor_stats.GetMultiPlayerHomepageResp")
	proto.RegisterType((*MultiPlayerMonthCommunityInfo)(nil), "sign_anchor_stats.MultiPlayerMonthCommunityInfo")
	proto.RegisterType((*MultiPlayerDailyInfo)(nil), "sign_anchor_stats.MultiPlayerDailyInfo")
	proto.RegisterType((*MultiPlayerWeekInfo)(nil), "sign_anchor_stats.MultiPlayerWeekInfo")
	proto.RegisterType((*MultiPlayerMonthInfo)(nil), "sign_anchor_stats.MultiPlayerMonthInfo")
	proto.RegisterType((*MultiPlayerMonthDetialInfo)(nil), "sign_anchor_stats.MultiPlayerMonthDetialInfo")
	proto.RegisterType((*GetMultiPlayerBaseInfoReq)(nil), "sign_anchor_stats.GetMultiPlayerBaseInfoReq")
	proto.RegisterType((*GetMultiPlayerBaseInfoResp)(nil), "sign_anchor_stats.GetMultiPlayerBaseInfoResp")
	proto.RegisterType((*GetMultiPlayerMonthConsumeTop10Req)(nil), "sign_anchor_stats.GetMultiPlayerMonthConsumeTop10Req")
	proto.RegisterType((*GetMultiPlayerMonthConsumeTop10Resp)(nil), "sign_anchor_stats.GetMultiPlayerMonthConsumeTop10Resp")
	proto.RegisterType((*GetMultiPlayerMonthConsumeTop10RespComsumer)(nil), "sign_anchor_stats.GetMultiPlayerMonthConsumeTop10Resp.comsumer")
	proto.RegisterType((*GetMultiPlayerMonthCommunityInfoReq)(nil), "sign_anchor_stats.GetMultiPlayerMonthCommunityInfoReq")
	proto.RegisterType((*GetMultiPlayerMonthCommunityInfoResp)(nil), "sign_anchor_stats.GetMultiPlayerMonthCommunityInfoResp")
	proto.RegisterType((*DailyInfo)(nil), "sign_anchor_stats.DailyInfo")
	proto.RegisterType((*DurInfo)(nil), "sign_anchor_stats.DurInfo")
	proto.RegisterType((*MultiPlayerCacheInfo)(nil), "sign_anchor_stats.MultiPlayerCacheInfo")
	proto.RegisterType((*MultiPlayerDetialCacheInfo)(nil), "sign_anchor_stats.MultiPlayerDetialCacheInfo")
	proto.RegisterType((*CheckUserInteractEntryReq)(nil), "sign_anchor_stats.CheckUserInteractEntryReq")
	proto.RegisterType((*CheckUserInteractEntryResp)(nil), "sign_anchor_stats.CheckUserInteractEntryResp")
	proto.RegisterType((*InteractInfo)(nil), "sign_anchor_stats.InteractInfo")
	proto.RegisterType((*InteractCertInfo)(nil), "sign_anchor_stats.InteractCertInfo")
	proto.RegisterType((*GetUserInteractInfoReq)(nil), "sign_anchor_stats.GetUserInteractInfoReq")
	proto.RegisterType((*GetUserInteractInfoResp)(nil), "sign_anchor_stats.GetUserInteractInfoResp")
	proto.RegisterType((*GetUserInteractViewPerReq)(nil), "sign_anchor_stats.GetUserInteractViewPerReq")
	proto.RegisterType((*GetUserInteractViewPerResp)(nil), "sign_anchor_stats.GetUserInteractViewPerResp")
	proto.RegisterType((*SetUserInteractViewPerReq)(nil), "sign_anchor_stats.SetUserInteractViewPerReq")
	proto.RegisterType((*SetUserInteractViewPerResp)(nil), "sign_anchor_stats.SetUserInteractViewPerResp")
	proto.RegisterType((*GetGuildChannelListReq)(nil), "sign_anchor_stats.GetGuildChannelListReq")
	proto.RegisterType((*GetGuildChannelListResp)(nil), "sign_anchor_stats.GetGuildChannelListResp")
	proto.RegisterType((*AddMultiPlayerHallReq)(nil), "sign_anchor_stats.AddMultiPlayerHallReq")
	proto.RegisterType((*AddMultiPlayerHallResp)(nil), "sign_anchor_stats.AddMultiPlayerHallResp")
	proto.RegisterType((*ListMultiPlayerHallReq)(nil), "sign_anchor_stats.ListMultiPlayerHallReq")
	proto.RegisterType((*ListMultiPlayerHallResp)(nil), "sign_anchor_stats.ListMultiPlayerHallResp")
	proto.RegisterType((*DelMultiPlayerHallReq)(nil), "sign_anchor_stats.DelMultiPlayerHallReq")
	proto.RegisterType((*DelMultiPlayerHallResp)(nil), "sign_anchor_stats.DelMultiPlayerHallResp")
	proto.RegisterType((*MultiPlayerHallInfo)(nil), "sign_anchor_stats.MultiPlayerHallInfo")
	proto.RegisterType((*GetGuildMultiPlayerHallReq)(nil), "sign_anchor_stats.GetGuildMultiPlayerHallReq")
	proto.RegisterType((*GetGuildMultiPlayerHallResp)(nil), "sign_anchor_stats.GetGuildMultiPlayerHallResp")
	proto.RegisterType((*GetGuildMultiPlayerHallResp_GuildMultiPlayerHall)(nil), "sign_anchor_stats.GetGuildMultiPlayerHallResp.GuildMultiPlayerHall")
	proto.RegisterType((*GetHallTaskConfListReq)(nil), "sign_anchor_stats.GetHallTaskConfListReq")
	proto.RegisterType((*GetHallTaskConfListResp)(nil), "sign_anchor_stats.GetHallTaskConfListResp")
	proto.RegisterType((*HallTaskConfDetialInfo)(nil), "sign_anchor_stats.HallTaskConfDetialInfo")
	proto.RegisterType((*HallTaskConfInfo)(nil), "sign_anchor_stats.HallTaskConfInfo")
	proto.RegisterType((*AddHallTaskConfReq)(nil), "sign_anchor_stats.AddHallTaskConfReq")
	proto.RegisterType((*AddHallTaskConfResp)(nil), "sign_anchor_stats.AddHallTaskConfResp")
	proto.RegisterType((*DelHallTaskConfReq)(nil), "sign_anchor_stats.DelHallTaskConfReq")
	proto.RegisterType((*DelHallTaskConfResp)(nil), "sign_anchor_stats.DelHallTaskConfResp")
	proto.RegisterType((*DistributeHallTaskReq)(nil), "sign_anchor_stats.DistributeHallTaskReq")
	proto.RegisterType((*DistributeHallTaskResp)(nil), "sign_anchor_stats.DistributeHallTaskResp")
	proto.RegisterType((*HallTaskInfo)(nil), "sign_anchor_stats.HallTaskInfo")
	proto.RegisterType((*GetGuildHallTaskReq)(nil), "sign_anchor_stats.GetGuildHallTaskReq")
	proto.RegisterType((*GetGuildHallTaskResp)(nil), "sign_anchor_stats.GetGuildHallTaskResp")
	proto.RegisterType((*GetGuildHallTaskStatsReq)(nil), "sign_anchor_stats.GetGuildHallTaskStatsReq")
	proto.RegisterType((*GetGuildHallTaskStatsResp)(nil), "sign_anchor_stats.GetGuildHallTaskStatsResp")
	proto.RegisterType((*HallStatisticsInfo)(nil), "sign_anchor_stats.HallStatisticsInfo")
	proto.RegisterType((*GetGuildHallTaskStatsDetialReq)(nil), "sign_anchor_stats.GetGuildHallTaskStatsDetialReq")
	proto.RegisterType((*GetGuildHallTaskStatsDetialResp)(nil), "sign_anchor_stats.GetGuildHallTaskStatsDetialResp")
	proto.RegisterType((*GuildHallTaskStatsDetial)(nil), "sign_anchor_stats.GuildHallTaskStatsDetial")
	proto.RegisterType((*GetHallTaskReq)(nil), "sign_anchor_stats.GetHallTaskReq")
	proto.RegisterType((*GetHallTaskResp)(nil), "sign_anchor_stats.GetHallTaskResp")
	proto.RegisterType((*HallTaskDetial)(nil), "sign_anchor_stats.HallTaskDetial")
	proto.RegisterType((*GetHallTaskHistoryReq)(nil), "sign_anchor_stats.GetHallTaskHistoryReq")
	proto.RegisterType((*GetHallTaskHistoryResp)(nil), "sign_anchor_stats.GetHallTaskHistoryResp")
	proto.RegisterType((*HallTaskHistoryDetial)(nil), "sign_anchor_stats.HallTaskHistoryDetial")
	proto.RegisterType((*GetHallTaskDistributeHistoryReq)(nil), "sign_anchor_stats.GetHallTaskDistributeHistoryReq")
	proto.RegisterType((*GetHallTaskDistributeHistoryResp)(nil), "sign_anchor_stats.GetHallTaskDistributeHistoryResp")
	proto.RegisterType((*GetHallTaskDistributeHistory)(nil), "sign_anchor_stats.GetHallTaskDistributeHistory")
	proto.RegisterType((*DelHallTaskReq)(nil), "sign_anchor_stats.DelHallTaskReq")
	proto.RegisterType((*DelHallTaskResp)(nil), "sign_anchor_stats.DelHallTaskResp")
	proto.RegisterType((*HallTaskCacheInfo)(nil), "sign_anchor_stats.HallTaskCacheInfo")
	proto.RegisterType((*HallTaskVal)(nil), "sign_anchor_stats.HallTaskVal")
	proto.RegisterType((*GetHallTaskConfByIdReq)(nil), "sign_anchor_stats.GetHallTaskConfByIdReq")
	proto.RegisterType((*GetHallTaskConfByIdResp)(nil), "sign_anchor_stats.GetHallTaskConfByIdResp")
	proto.RegisterType((*GetValidHoldDayUidReq)(nil), "sign_anchor_stats.GetValidHoldDayUidReq")
	proto.RegisterType((*GetValidHoldDayUidResp)(nil), "sign_anchor_stats.GetValidHoldDayUidResp")
	proto.RegisterType((*GetBindGuildInfoReq)(nil), "sign_anchor_stats.GetBindGuildInfoReq")
	proto.RegisterType((*GetBindGuildInfoResp)(nil), "sign_anchor_stats.GetBindGuildInfoResp")
	proto.RegisterType((*GuildAb)(nil), "sign_anchor_stats.GuildAb")
	proto.RegisterType((*BatchGetBindGuildInfoReq)(nil), "sign_anchor_stats.BatchGetBindGuildInfoReq")
	proto.RegisterType((*BatchGetBindGuildInfoResp)(nil), "sign_anchor_stats.BatchGetBindGuildInfoResp")
	proto.RegisterType((*BindGuildInfo)(nil), "sign_anchor_stats.BindGuildInfo")
	proto.RegisterType((*GetBindGuildInfoListReq)(nil), "sign_anchor_stats.GetBindGuildInfoListReq")
	proto.RegisterType((*GetBindGuildInfoListResp)(nil), "sign_anchor_stats.GetBindGuildInfoListResp")
	proto.RegisterEnum("sign_anchor_stats.TimeFilterUnit", TimeFilterUnit_name, TimeFilterUnit_value)
	proto.RegisterEnum("sign_anchor_stats.AbilityType", AbilityType_name, AbilityType_value)
	proto.RegisterEnum("sign_anchor_stats.TYPE_MULTIPLAYER_INFO", TYPE_MULTIPLAYER_INFO_name, TYPE_MULTIPLAYER_INFO_value)
	proto.RegisterEnum("sign_anchor_stats.SceneType", SceneType_name, SceneType_value)
	proto.RegisterEnum("sign_anchor_stats.InteractUserType", InteractUserType_name, InteractUserType_value)
	proto.RegisterEnum("sign_anchor_stats.HALL_TASK_DAY_TYPE", HALL_TASK_DAY_TYPE_name, HALL_TASK_DAY_TYPE_value)
	proto.RegisterEnum("sign_anchor_stats.HALL_TASK_WEEK_TYPE", HALL_TASK_WEEK_TYPE_name, HALL_TASK_WEEK_TYPE_value)
	proto.RegisterEnum("sign_anchor_stats.HALL_TASK_CHANGE_TYPE", HALL_TASK_CHANGE_TYPE_name, HALL_TASK_CHANGE_TYPE_value)
	proto.RegisterEnum("sign_anchor_stats.HALL_TASK_PRESENT_SOURCE_TYPE", HALL_TASK_PRESENT_SOURCE_TYPE_name, HALL_TASK_PRESENT_SOURCE_TYPE_value)
	proto.RegisterEnum("sign_anchor_stats.HallStatisticsType", HallStatisticsType_name, HallStatisticsType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// SignAnchorStatsClient is the client API for SignAnchorStats service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type SignAnchorStatsClient interface {
	// 获取公会（当月）房间统计
	GetMultiThisMonthChannelStat(ctx context.Context, in *GetMultiThisMonthChannelStatReq, opts ...grpc.CallOption) (*GetMultiThisMonthChannelStatResp, error)
	// 获取公会签约成员统计（按月）
	GetMultiAnchorMonthStat(ctx context.Context, in *GetMultiAnchorMonthStatReq, opts ...grpc.CallOption) (*GetMultiAnchorMonthStatResp, error)
	// 获取成员房间接档统计（按日、月）
	GetMultiAnchorChannelStat(ctx context.Context, in *GetMultiAnchorChannelStatReq, opts ...grpc.CallOption) (*GetMultiAnchorChannelStatResp, error)
	// 获取用户在房间类型4，7 的t豆消费
	GetUserTbeanConsume(ctx context.Context, in *GetUserTbeanConsumeReq, opts ...grpc.CallOption) (*GetUserTbeanConsumeResp, error)
	// 获取公会成员日记录统计
	GetMultiAnchorDailyStatsListByGuildId(ctx context.Context, in *GetMultiAnchorDailyStatsListByGuildIdReq, opts ...grpc.CallOption) (*GetMultiAnchorDailyStatsListByGuildIdResp, error)
	// 获取公会公开房月信息列表
	GetPgcMonthlyInfoList(ctx context.Context, in *GetPgcMonthlyInfoListReq, opts ...grpc.CallOption) (*GetPgcMonthlyInfoListResp, error)
	// 获取公会公开房日信息列表
	GetPgcDailyInfoList(ctx context.Context, in *GetPgcDailyInfoListReq, opts ...grpc.CallOption) (*GetPgcDailyInfoListResp, error)
	// 获取公会月信息列表
	GetGuildMonthlyStatsInfoList(ctx context.Context, in *GetGuildMonthlyStatsInfoListReq, opts ...grpc.CallOption) (*GetGuildMonthlyStatsInfoListResp, error)
	// 获取多人互动主播日维度信息
	GetMultiAnchorDailyStatsList(ctx context.Context, in *GetMultiAnchorDailyStatsListReq, opts ...grpc.CallOption) (*GetMultiAnchorDailyStatsListResp, error)
	//
	GetMultiPlayerHomepage(ctx context.Context, in *GetMultiPlayerHomepageReq, opts ...grpc.CallOption) (*GetMultiPlayerHomepageResp, error)
	GetMultiPlayerBaseInfo(ctx context.Context, in *GetMultiPlayerBaseInfoReq, opts ...grpc.CallOption) (*GetMultiPlayerBaseInfoResp, error)
	GetMultiPlayerMonthConsumeTop10(ctx context.Context, in *GetMultiPlayerMonthConsumeTop10Req, opts ...grpc.CallOption) (*GetMultiPlayerMonthConsumeTop10Resp, error)
	GetMultiPlayerMonthCommunityInfo(ctx context.Context, in *GetMultiPlayerMonthCommunityInfoReq, opts ...grpc.CallOption) (*GetMultiPlayerMonthCommunityInfoResp, error)
	// *** 互动消息 ***//
	CheckUserInteractEntry(ctx context.Context, in *CheckUserInteractEntryReq, opts ...grpc.CallOption) (*CheckUserInteractEntryResp, error)
	GetUserInteractInfo(ctx context.Context, in *GetUserInteractInfoReq, opts ...grpc.CallOption) (*GetUserInteractInfoResp, error)
	GetUserInteractViewPer(ctx context.Context, in *GetUserInteractViewPerReq, opts ...grpc.CallOption) (*GetUserInteractViewPerResp, error)
	SetUserInteractViewPer(ctx context.Context, in *SetUserInteractViewPerReq, opts ...grpc.CallOption) (*SetUserInteractViewPerResp, error)
	// *** 多人互动大厅任务 ***//
	// 运营后台
	// 查询公会房列表
	GetGuildChannelList(ctx context.Context, in *GetGuildChannelListReq, opts ...grpc.CallOption) (*GetGuildChannelListResp, error)
	// 配置承接大厅
	AddMultiPlayerHall(ctx context.Context, in *AddMultiPlayerHallReq, opts ...grpc.CallOption) (*AddMultiPlayerHallResp, error)
	// 查询承接大厅
	ListMultiPlayerHall(ctx context.Context, in *ListMultiPlayerHallReq, opts ...grpc.CallOption) (*ListMultiPlayerHallResp, error)
	// 回收承接大厅
	DelMultiPlayerHall(ctx context.Context, in *DelMultiPlayerHallReq, opts ...grpc.CallOption) (*DelMultiPlayerHallResp, error)
	GetGuildMultiPlayerHall(ctx context.Context, in *GetGuildMultiPlayerHallReq, opts ...grpc.CallOption) (*GetGuildMultiPlayerHallResp, error)
	// 公会查询任务配置信息
	GetHallTaskConfList(ctx context.Context, in *GetHallTaskConfListReq, opts ...grpc.CallOption) (*GetHallTaskConfListResp, error)
	GetHallTaskConfById(ctx context.Context, in *GetHallTaskConfByIdReq, opts ...grpc.CallOption) (*GetHallTaskConfByIdResp, error)
	// 公会配置任务
	AddHallTaskConf(ctx context.Context, in *AddHallTaskConfReq, opts ...grpc.CallOption) (*AddHallTaskConfResp, error)
	// 公会删除任务配置
	DelHallTaskConf(ctx context.Context, in *DelHallTaskConfReq, opts ...grpc.CallOption) (*DelHallTaskConfResp, error)
	// 公会分配任务
	DistributeHallTask(ctx context.Context, in *DistributeHallTaskReq, opts ...grpc.CallOption) (*DistributeHallTaskResp, error)
	// 删除成员任务
	DelHallTask(ctx context.Context, in *DelHallTaskReq, opts ...grpc.CallOption) (*DelHallTaskResp, error)
	// 公会查询分配任务
	GetGuildHallTask(ctx context.Context, in *GetGuildHallTaskReq, opts ...grpc.CallOption) (*GetGuildHallTaskResp, error)
	// 公会查询厅数据
	GetGuildHallTaskStats(ctx context.Context, in *GetGuildHallTaskStatsReq, opts ...grpc.CallOption) (*GetGuildHallTaskStatsResp, error)
	// 公会查询当天接档成员详情
	GetGuildHallTaskStatsDetial(ctx context.Context, in *GetGuildHallTaskStatsDetialReq, opts ...grpc.CallOption) (*GetGuildHallTaskStatsDetialResp, error)
	// 查签约成员被分配任务历史记录
	GetHallTaskDistributeHistory(ctx context.Context, in *GetHallTaskDistributeHistoryReq, opts ...grpc.CallOption) (*GetHallTaskDistributeHistoryResp, error)
	// 查询承接大厅任务缓存信息
	GetHallTaskCacheInfo(ctx context.Context, in *GetHallTaskReq, opts ...grpc.CallOption) (*GetHallTaskResp, error)
	// 查询签约成员当前周期正在进行的大厅任务
	GetHallTask(ctx context.Context, in *GetHallTaskReq, opts ...grpc.CallOption) (*GetHallTaskResp, error)
	// 查询签约成员大厅任务历史
	GetHallTaskHistory(ctx context.Context, in *GetHallTaskHistoryReq, opts ...grpc.CallOption) (*GetHallTaskHistoryResp, error)
	// 对账接口
	// 获取时间范围内礼物券的数量和金额
	GetTicketOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	// 获取时间范围内礼物券的订单列表
	GetTicketOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// 获取时间范围内T豆礼物的数量和金额
	GetPresentOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	// 获取时间范围内T豆礼物的订单列表
	GetPresentOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	ReplaceOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error)
	// 查询时间范围内在指定房间有效接档的签约成员uid
	GetValidHoldDayUid(ctx context.Context, in *GetValidHoldDayUidReq, opts ...grpc.CallOption) (*GetValidHoldDayUidResp, error)
	// 检查公会的子母关系
	GetBindGuildInfo(ctx context.Context, in *GetBindGuildInfoReq, opts ...grpc.CallOption) (*GetBindGuildInfoResp, error)
	// 批量检查公会的子母关系
	BatchGetBindGuildInfo(ctx context.Context, in *BatchGetBindGuildInfoReq, opts ...grpc.CallOption) (*BatchGetBindGuildInfoResp, error)
	// 获取子母公会配置列表
	GetBindGuildInfoList(ctx context.Context, in *GetBindGuildInfoListReq, opts ...grpc.CallOption) (*GetBindGuildInfoListResp, error)
}

type signAnchorStatsClient struct {
	cc *grpc.ClientConn
}

func NewSignAnchorStatsClient(cc *grpc.ClientConn) SignAnchorStatsClient {
	return &signAnchorStatsClient{cc}
}

func (c *signAnchorStatsClient) GetMultiThisMonthChannelStat(ctx context.Context, in *GetMultiThisMonthChannelStatReq, opts ...grpc.CallOption) (*GetMultiThisMonthChannelStatResp, error) {
	out := new(GetMultiThisMonthChannelStatResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/GetMultiThisMonthChannelStat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) GetMultiAnchorMonthStat(ctx context.Context, in *GetMultiAnchorMonthStatReq, opts ...grpc.CallOption) (*GetMultiAnchorMonthStatResp, error) {
	out := new(GetMultiAnchorMonthStatResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/GetMultiAnchorMonthStat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) GetMultiAnchorChannelStat(ctx context.Context, in *GetMultiAnchorChannelStatReq, opts ...grpc.CallOption) (*GetMultiAnchorChannelStatResp, error) {
	out := new(GetMultiAnchorChannelStatResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/GetMultiAnchorChannelStat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) GetUserTbeanConsume(ctx context.Context, in *GetUserTbeanConsumeReq, opts ...grpc.CallOption) (*GetUserTbeanConsumeResp, error) {
	out := new(GetUserTbeanConsumeResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/GetUserTbeanConsume", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) GetMultiAnchorDailyStatsListByGuildId(ctx context.Context, in *GetMultiAnchorDailyStatsListByGuildIdReq, opts ...grpc.CallOption) (*GetMultiAnchorDailyStatsListByGuildIdResp, error) {
	out := new(GetMultiAnchorDailyStatsListByGuildIdResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/GetMultiAnchorDailyStatsListByGuildId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) GetPgcMonthlyInfoList(ctx context.Context, in *GetPgcMonthlyInfoListReq, opts ...grpc.CallOption) (*GetPgcMonthlyInfoListResp, error) {
	out := new(GetPgcMonthlyInfoListResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/GetPgcMonthlyInfoList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) GetPgcDailyInfoList(ctx context.Context, in *GetPgcDailyInfoListReq, opts ...grpc.CallOption) (*GetPgcDailyInfoListResp, error) {
	out := new(GetPgcDailyInfoListResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/GetPgcDailyInfoList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) GetGuildMonthlyStatsInfoList(ctx context.Context, in *GetGuildMonthlyStatsInfoListReq, opts ...grpc.CallOption) (*GetGuildMonthlyStatsInfoListResp, error) {
	out := new(GetGuildMonthlyStatsInfoListResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/GetGuildMonthlyStatsInfoList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) GetMultiAnchorDailyStatsList(ctx context.Context, in *GetMultiAnchorDailyStatsListReq, opts ...grpc.CallOption) (*GetMultiAnchorDailyStatsListResp, error) {
	out := new(GetMultiAnchorDailyStatsListResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/GetMultiAnchorDailyStatsList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) GetMultiPlayerHomepage(ctx context.Context, in *GetMultiPlayerHomepageReq, opts ...grpc.CallOption) (*GetMultiPlayerHomepageResp, error) {
	out := new(GetMultiPlayerHomepageResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/GetMultiPlayerHomepage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) GetMultiPlayerBaseInfo(ctx context.Context, in *GetMultiPlayerBaseInfoReq, opts ...grpc.CallOption) (*GetMultiPlayerBaseInfoResp, error) {
	out := new(GetMultiPlayerBaseInfoResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/GetMultiPlayerBaseInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) GetMultiPlayerMonthConsumeTop10(ctx context.Context, in *GetMultiPlayerMonthConsumeTop10Req, opts ...grpc.CallOption) (*GetMultiPlayerMonthConsumeTop10Resp, error) {
	out := new(GetMultiPlayerMonthConsumeTop10Resp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/GetMultiPlayerMonthConsumeTop10", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) GetMultiPlayerMonthCommunityInfo(ctx context.Context, in *GetMultiPlayerMonthCommunityInfoReq, opts ...grpc.CallOption) (*GetMultiPlayerMonthCommunityInfoResp, error) {
	out := new(GetMultiPlayerMonthCommunityInfoResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/GetMultiPlayerMonthCommunityInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) CheckUserInteractEntry(ctx context.Context, in *CheckUserInteractEntryReq, opts ...grpc.CallOption) (*CheckUserInteractEntryResp, error) {
	out := new(CheckUserInteractEntryResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/CheckUserInteractEntry", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) GetUserInteractInfo(ctx context.Context, in *GetUserInteractInfoReq, opts ...grpc.CallOption) (*GetUserInteractInfoResp, error) {
	out := new(GetUserInteractInfoResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/GetUserInteractInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) GetUserInteractViewPer(ctx context.Context, in *GetUserInteractViewPerReq, opts ...grpc.CallOption) (*GetUserInteractViewPerResp, error) {
	out := new(GetUserInteractViewPerResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/GetUserInteractViewPer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) SetUserInteractViewPer(ctx context.Context, in *SetUserInteractViewPerReq, opts ...grpc.CallOption) (*SetUserInteractViewPerResp, error) {
	out := new(SetUserInteractViewPerResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/SetUserInteractViewPer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) GetGuildChannelList(ctx context.Context, in *GetGuildChannelListReq, opts ...grpc.CallOption) (*GetGuildChannelListResp, error) {
	out := new(GetGuildChannelListResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/GetGuildChannelList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) AddMultiPlayerHall(ctx context.Context, in *AddMultiPlayerHallReq, opts ...grpc.CallOption) (*AddMultiPlayerHallResp, error) {
	out := new(AddMultiPlayerHallResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/AddMultiPlayerHall", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) ListMultiPlayerHall(ctx context.Context, in *ListMultiPlayerHallReq, opts ...grpc.CallOption) (*ListMultiPlayerHallResp, error) {
	out := new(ListMultiPlayerHallResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/ListMultiPlayerHall", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) DelMultiPlayerHall(ctx context.Context, in *DelMultiPlayerHallReq, opts ...grpc.CallOption) (*DelMultiPlayerHallResp, error) {
	out := new(DelMultiPlayerHallResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/DelMultiPlayerHall", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) GetGuildMultiPlayerHall(ctx context.Context, in *GetGuildMultiPlayerHallReq, opts ...grpc.CallOption) (*GetGuildMultiPlayerHallResp, error) {
	out := new(GetGuildMultiPlayerHallResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/GetGuildMultiPlayerHall", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) GetHallTaskConfList(ctx context.Context, in *GetHallTaskConfListReq, opts ...grpc.CallOption) (*GetHallTaskConfListResp, error) {
	out := new(GetHallTaskConfListResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/GetHallTaskConfList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) GetHallTaskConfById(ctx context.Context, in *GetHallTaskConfByIdReq, opts ...grpc.CallOption) (*GetHallTaskConfByIdResp, error) {
	out := new(GetHallTaskConfByIdResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/GetHallTaskConfById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) AddHallTaskConf(ctx context.Context, in *AddHallTaskConfReq, opts ...grpc.CallOption) (*AddHallTaskConfResp, error) {
	out := new(AddHallTaskConfResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/AddHallTaskConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) DelHallTaskConf(ctx context.Context, in *DelHallTaskConfReq, opts ...grpc.CallOption) (*DelHallTaskConfResp, error) {
	out := new(DelHallTaskConfResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/DelHallTaskConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) DistributeHallTask(ctx context.Context, in *DistributeHallTaskReq, opts ...grpc.CallOption) (*DistributeHallTaskResp, error) {
	out := new(DistributeHallTaskResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/DistributeHallTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) DelHallTask(ctx context.Context, in *DelHallTaskReq, opts ...grpc.CallOption) (*DelHallTaskResp, error) {
	out := new(DelHallTaskResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/DelHallTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) GetGuildHallTask(ctx context.Context, in *GetGuildHallTaskReq, opts ...grpc.CallOption) (*GetGuildHallTaskResp, error) {
	out := new(GetGuildHallTaskResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/GetGuildHallTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) GetGuildHallTaskStats(ctx context.Context, in *GetGuildHallTaskStatsReq, opts ...grpc.CallOption) (*GetGuildHallTaskStatsResp, error) {
	out := new(GetGuildHallTaskStatsResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/GetGuildHallTaskStats", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) GetGuildHallTaskStatsDetial(ctx context.Context, in *GetGuildHallTaskStatsDetialReq, opts ...grpc.CallOption) (*GetGuildHallTaskStatsDetialResp, error) {
	out := new(GetGuildHallTaskStatsDetialResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/GetGuildHallTaskStatsDetial", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) GetHallTaskDistributeHistory(ctx context.Context, in *GetHallTaskDistributeHistoryReq, opts ...grpc.CallOption) (*GetHallTaskDistributeHistoryResp, error) {
	out := new(GetHallTaskDistributeHistoryResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/GetHallTaskDistributeHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) GetHallTaskCacheInfo(ctx context.Context, in *GetHallTaskReq, opts ...grpc.CallOption) (*GetHallTaskResp, error) {
	out := new(GetHallTaskResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/GetHallTaskCacheInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) GetHallTask(ctx context.Context, in *GetHallTaskReq, opts ...grpc.CallOption) (*GetHallTaskResp, error) {
	out := new(GetHallTaskResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/GetHallTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) GetHallTaskHistory(ctx context.Context, in *GetHallTaskHistoryReq, opts ...grpc.CallOption) (*GetHallTaskHistoryResp, error) {
	out := new(GetHallTaskHistoryResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/GetHallTaskHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) GetTicketOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/GetTicketOrderCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) GetTicketOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/GetTicketOrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) GetPresentOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/GetPresentOrderCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) GetPresentOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/GetPresentOrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) ReplaceOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	out := new(reconcile_v2.EmptyResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/ReplaceOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) GetValidHoldDayUid(ctx context.Context, in *GetValidHoldDayUidReq, opts ...grpc.CallOption) (*GetValidHoldDayUidResp, error) {
	out := new(GetValidHoldDayUidResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/GetValidHoldDayUid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) GetBindGuildInfo(ctx context.Context, in *GetBindGuildInfoReq, opts ...grpc.CallOption) (*GetBindGuildInfoResp, error) {
	out := new(GetBindGuildInfoResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/GetBindGuildInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) BatchGetBindGuildInfo(ctx context.Context, in *BatchGetBindGuildInfoReq, opts ...grpc.CallOption) (*BatchGetBindGuildInfoResp, error) {
	out := new(BatchGetBindGuildInfoResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/BatchGetBindGuildInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signAnchorStatsClient) GetBindGuildInfoList(ctx context.Context, in *GetBindGuildInfoListReq, opts ...grpc.CallOption) (*GetBindGuildInfoListResp, error) {
	out := new(GetBindGuildInfoListResp)
	err := c.cc.Invoke(ctx, "/sign_anchor_stats.SignAnchorStats/GetBindGuildInfoList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SignAnchorStatsServer is the server API for SignAnchorStats service.
type SignAnchorStatsServer interface {
	// 获取公会（当月）房间统计
	GetMultiThisMonthChannelStat(context.Context, *GetMultiThisMonthChannelStatReq) (*GetMultiThisMonthChannelStatResp, error)
	// 获取公会签约成员统计（按月）
	GetMultiAnchorMonthStat(context.Context, *GetMultiAnchorMonthStatReq) (*GetMultiAnchorMonthStatResp, error)
	// 获取成员房间接档统计（按日、月）
	GetMultiAnchorChannelStat(context.Context, *GetMultiAnchorChannelStatReq) (*GetMultiAnchorChannelStatResp, error)
	// 获取用户在房间类型4，7 的t豆消费
	GetUserTbeanConsume(context.Context, *GetUserTbeanConsumeReq) (*GetUserTbeanConsumeResp, error)
	// 获取公会成员日记录统计
	GetMultiAnchorDailyStatsListByGuildId(context.Context, *GetMultiAnchorDailyStatsListByGuildIdReq) (*GetMultiAnchorDailyStatsListByGuildIdResp, error)
	// 获取公会公开房月信息列表
	GetPgcMonthlyInfoList(context.Context, *GetPgcMonthlyInfoListReq) (*GetPgcMonthlyInfoListResp, error)
	// 获取公会公开房日信息列表
	GetPgcDailyInfoList(context.Context, *GetPgcDailyInfoListReq) (*GetPgcDailyInfoListResp, error)
	// 获取公会月信息列表
	GetGuildMonthlyStatsInfoList(context.Context, *GetGuildMonthlyStatsInfoListReq) (*GetGuildMonthlyStatsInfoListResp, error)
	// 获取多人互动主播日维度信息
	GetMultiAnchorDailyStatsList(context.Context, *GetMultiAnchorDailyStatsListReq) (*GetMultiAnchorDailyStatsListResp, error)
	//
	GetMultiPlayerHomepage(context.Context, *GetMultiPlayerHomepageReq) (*GetMultiPlayerHomepageResp, error)
	GetMultiPlayerBaseInfo(context.Context, *GetMultiPlayerBaseInfoReq) (*GetMultiPlayerBaseInfoResp, error)
	GetMultiPlayerMonthConsumeTop10(context.Context, *GetMultiPlayerMonthConsumeTop10Req) (*GetMultiPlayerMonthConsumeTop10Resp, error)
	GetMultiPlayerMonthCommunityInfo(context.Context, *GetMultiPlayerMonthCommunityInfoReq) (*GetMultiPlayerMonthCommunityInfoResp, error)
	// *** 互动消息 ***//
	CheckUserInteractEntry(context.Context, *CheckUserInteractEntryReq) (*CheckUserInteractEntryResp, error)
	GetUserInteractInfo(context.Context, *GetUserInteractInfoReq) (*GetUserInteractInfoResp, error)
	GetUserInteractViewPer(context.Context, *GetUserInteractViewPerReq) (*GetUserInteractViewPerResp, error)
	SetUserInteractViewPer(context.Context, *SetUserInteractViewPerReq) (*SetUserInteractViewPerResp, error)
	// *** 多人互动大厅任务 ***//
	// 运营后台
	// 查询公会房列表
	GetGuildChannelList(context.Context, *GetGuildChannelListReq) (*GetGuildChannelListResp, error)
	// 配置承接大厅
	AddMultiPlayerHall(context.Context, *AddMultiPlayerHallReq) (*AddMultiPlayerHallResp, error)
	// 查询承接大厅
	ListMultiPlayerHall(context.Context, *ListMultiPlayerHallReq) (*ListMultiPlayerHallResp, error)
	// 回收承接大厅
	DelMultiPlayerHall(context.Context, *DelMultiPlayerHallReq) (*DelMultiPlayerHallResp, error)
	GetGuildMultiPlayerHall(context.Context, *GetGuildMultiPlayerHallReq) (*GetGuildMultiPlayerHallResp, error)
	// 公会查询任务配置信息
	GetHallTaskConfList(context.Context, *GetHallTaskConfListReq) (*GetHallTaskConfListResp, error)
	GetHallTaskConfById(context.Context, *GetHallTaskConfByIdReq) (*GetHallTaskConfByIdResp, error)
	// 公会配置任务
	AddHallTaskConf(context.Context, *AddHallTaskConfReq) (*AddHallTaskConfResp, error)
	// 公会删除任务配置
	DelHallTaskConf(context.Context, *DelHallTaskConfReq) (*DelHallTaskConfResp, error)
	// 公会分配任务
	DistributeHallTask(context.Context, *DistributeHallTaskReq) (*DistributeHallTaskResp, error)
	// 删除成员任务
	DelHallTask(context.Context, *DelHallTaskReq) (*DelHallTaskResp, error)
	// 公会查询分配任务
	GetGuildHallTask(context.Context, *GetGuildHallTaskReq) (*GetGuildHallTaskResp, error)
	// 公会查询厅数据
	GetGuildHallTaskStats(context.Context, *GetGuildHallTaskStatsReq) (*GetGuildHallTaskStatsResp, error)
	// 公会查询当天接档成员详情
	GetGuildHallTaskStatsDetial(context.Context, *GetGuildHallTaskStatsDetialReq) (*GetGuildHallTaskStatsDetialResp, error)
	// 查签约成员被分配任务历史记录
	GetHallTaskDistributeHistory(context.Context, *GetHallTaskDistributeHistoryReq) (*GetHallTaskDistributeHistoryResp, error)
	// 查询承接大厅任务缓存信息
	GetHallTaskCacheInfo(context.Context, *GetHallTaskReq) (*GetHallTaskResp, error)
	// 查询签约成员当前周期正在进行的大厅任务
	GetHallTask(context.Context, *GetHallTaskReq) (*GetHallTaskResp, error)
	// 查询签约成员大厅任务历史
	GetHallTaskHistory(context.Context, *GetHallTaskHistoryReq) (*GetHallTaskHistoryResp, error)
	// 对账接口
	// 获取时间范围内礼物券的数量和金额
	GetTicketOrderCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	// 获取时间范围内礼物券的订单列表
	GetTicketOrderList(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// 获取时间范围内T豆礼物的数量和金额
	GetPresentOrderCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	// 获取时间范围内T豆礼物的订单列表
	GetPresentOrderList(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	ReplaceOrder(context.Context, *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error)
	// 查询时间范围内在指定房间有效接档的签约成员uid
	GetValidHoldDayUid(context.Context, *GetValidHoldDayUidReq) (*GetValidHoldDayUidResp, error)
	// 检查公会的子母关系
	GetBindGuildInfo(context.Context, *GetBindGuildInfoReq) (*GetBindGuildInfoResp, error)
	// 批量检查公会的子母关系
	BatchGetBindGuildInfo(context.Context, *BatchGetBindGuildInfoReq) (*BatchGetBindGuildInfoResp, error)
	// 获取子母公会配置列表
	GetBindGuildInfoList(context.Context, *GetBindGuildInfoListReq) (*GetBindGuildInfoListResp, error)
}

func RegisterSignAnchorStatsServer(s *grpc.Server, srv SignAnchorStatsServer) {
	s.RegisterService(&_SignAnchorStats_serviceDesc, srv)
}

func _SignAnchorStats_GetMultiThisMonthChannelStat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMultiThisMonthChannelStatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).GetMultiThisMonthChannelStat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/GetMultiThisMonthChannelStat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).GetMultiThisMonthChannelStat(ctx, req.(*GetMultiThisMonthChannelStatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_GetMultiAnchorMonthStat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMultiAnchorMonthStatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).GetMultiAnchorMonthStat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/GetMultiAnchorMonthStat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).GetMultiAnchorMonthStat(ctx, req.(*GetMultiAnchorMonthStatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_GetMultiAnchorChannelStat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMultiAnchorChannelStatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).GetMultiAnchorChannelStat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/GetMultiAnchorChannelStat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).GetMultiAnchorChannelStat(ctx, req.(*GetMultiAnchorChannelStatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_GetUserTbeanConsume_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserTbeanConsumeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).GetUserTbeanConsume(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/GetUserTbeanConsume",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).GetUserTbeanConsume(ctx, req.(*GetUserTbeanConsumeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_GetMultiAnchorDailyStatsListByGuildId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMultiAnchorDailyStatsListByGuildIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).GetMultiAnchorDailyStatsListByGuildId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/GetMultiAnchorDailyStatsListByGuildId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).GetMultiAnchorDailyStatsListByGuildId(ctx, req.(*GetMultiAnchorDailyStatsListByGuildIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_GetPgcMonthlyInfoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPgcMonthlyInfoListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).GetPgcMonthlyInfoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/GetPgcMonthlyInfoList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).GetPgcMonthlyInfoList(ctx, req.(*GetPgcMonthlyInfoListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_GetPgcDailyInfoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPgcDailyInfoListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).GetPgcDailyInfoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/GetPgcDailyInfoList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).GetPgcDailyInfoList(ctx, req.(*GetPgcDailyInfoListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_GetGuildMonthlyStatsInfoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildMonthlyStatsInfoListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).GetGuildMonthlyStatsInfoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/GetGuildMonthlyStatsInfoList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).GetGuildMonthlyStatsInfoList(ctx, req.(*GetGuildMonthlyStatsInfoListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_GetMultiAnchorDailyStatsList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMultiAnchorDailyStatsListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).GetMultiAnchorDailyStatsList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/GetMultiAnchorDailyStatsList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).GetMultiAnchorDailyStatsList(ctx, req.(*GetMultiAnchorDailyStatsListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_GetMultiPlayerHomepage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMultiPlayerHomepageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).GetMultiPlayerHomepage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/GetMultiPlayerHomepage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).GetMultiPlayerHomepage(ctx, req.(*GetMultiPlayerHomepageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_GetMultiPlayerBaseInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMultiPlayerBaseInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).GetMultiPlayerBaseInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/GetMultiPlayerBaseInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).GetMultiPlayerBaseInfo(ctx, req.(*GetMultiPlayerBaseInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_GetMultiPlayerMonthConsumeTop10_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMultiPlayerMonthConsumeTop10Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).GetMultiPlayerMonthConsumeTop10(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/GetMultiPlayerMonthConsumeTop10",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).GetMultiPlayerMonthConsumeTop10(ctx, req.(*GetMultiPlayerMonthConsumeTop10Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_GetMultiPlayerMonthCommunityInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMultiPlayerMonthCommunityInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).GetMultiPlayerMonthCommunityInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/GetMultiPlayerMonthCommunityInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).GetMultiPlayerMonthCommunityInfo(ctx, req.(*GetMultiPlayerMonthCommunityInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_CheckUserInteractEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserInteractEntryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).CheckUserInteractEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/CheckUserInteractEntry",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).CheckUserInteractEntry(ctx, req.(*CheckUserInteractEntryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_GetUserInteractInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserInteractInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).GetUserInteractInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/GetUserInteractInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).GetUserInteractInfo(ctx, req.(*GetUserInteractInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_GetUserInteractViewPer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserInteractViewPerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).GetUserInteractViewPer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/GetUserInteractViewPer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).GetUserInteractViewPer(ctx, req.(*GetUserInteractViewPerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_SetUserInteractViewPer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserInteractViewPerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).SetUserInteractViewPer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/SetUserInteractViewPer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).SetUserInteractViewPer(ctx, req.(*SetUserInteractViewPerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_GetGuildChannelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildChannelListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).GetGuildChannelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/GetGuildChannelList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).GetGuildChannelList(ctx, req.(*GetGuildChannelListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_AddMultiPlayerHall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddMultiPlayerHallReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).AddMultiPlayerHall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/AddMultiPlayerHall",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).AddMultiPlayerHall(ctx, req.(*AddMultiPlayerHallReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_ListMultiPlayerHall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMultiPlayerHallReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).ListMultiPlayerHall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/ListMultiPlayerHall",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).ListMultiPlayerHall(ctx, req.(*ListMultiPlayerHallReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_DelMultiPlayerHall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelMultiPlayerHallReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).DelMultiPlayerHall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/DelMultiPlayerHall",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).DelMultiPlayerHall(ctx, req.(*DelMultiPlayerHallReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_GetGuildMultiPlayerHall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildMultiPlayerHallReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).GetGuildMultiPlayerHall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/GetGuildMultiPlayerHall",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).GetGuildMultiPlayerHall(ctx, req.(*GetGuildMultiPlayerHallReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_GetHallTaskConfList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHallTaskConfListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).GetHallTaskConfList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/GetHallTaskConfList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).GetHallTaskConfList(ctx, req.(*GetHallTaskConfListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_GetHallTaskConfById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHallTaskConfByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).GetHallTaskConfById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/GetHallTaskConfById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).GetHallTaskConfById(ctx, req.(*GetHallTaskConfByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_AddHallTaskConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddHallTaskConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).AddHallTaskConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/AddHallTaskConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).AddHallTaskConf(ctx, req.(*AddHallTaskConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_DelHallTaskConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelHallTaskConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).DelHallTaskConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/DelHallTaskConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).DelHallTaskConf(ctx, req.(*DelHallTaskConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_DistributeHallTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DistributeHallTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).DistributeHallTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/DistributeHallTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).DistributeHallTask(ctx, req.(*DistributeHallTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_DelHallTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelHallTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).DelHallTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/DelHallTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).DelHallTask(ctx, req.(*DelHallTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_GetGuildHallTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildHallTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).GetGuildHallTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/GetGuildHallTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).GetGuildHallTask(ctx, req.(*GetGuildHallTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_GetGuildHallTaskStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildHallTaskStatsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).GetGuildHallTaskStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/GetGuildHallTaskStats",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).GetGuildHallTaskStats(ctx, req.(*GetGuildHallTaskStatsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_GetGuildHallTaskStatsDetial_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildHallTaskStatsDetialReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).GetGuildHallTaskStatsDetial(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/GetGuildHallTaskStatsDetial",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).GetGuildHallTaskStatsDetial(ctx, req.(*GetGuildHallTaskStatsDetialReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_GetHallTaskDistributeHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHallTaskDistributeHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).GetHallTaskDistributeHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/GetHallTaskDistributeHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).GetHallTaskDistributeHistory(ctx, req.(*GetHallTaskDistributeHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_GetHallTaskCacheInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHallTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).GetHallTaskCacheInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/GetHallTaskCacheInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).GetHallTaskCacheInfo(ctx, req.(*GetHallTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_GetHallTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHallTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).GetHallTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/GetHallTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).GetHallTask(ctx, req.(*GetHallTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_GetHallTaskHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHallTaskHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).GetHallTaskHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/GetHallTaskHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).GetHallTaskHistory(ctx, req.(*GetHallTaskHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_GetTicketOrderCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).GetTicketOrderCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/GetTicketOrderCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).GetTicketOrderCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_GetTicketOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).GetTicketOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/GetTicketOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).GetTicketOrderList(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_GetPresentOrderCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).GetPresentOrderCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/GetPresentOrderCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).GetPresentOrderCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_GetPresentOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).GetPresentOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/GetPresentOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).GetPresentOrderList(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_ReplaceOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.ReplaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).ReplaceOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/ReplaceOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).ReplaceOrder(ctx, req.(*reconcile_v2.ReplaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_GetValidHoldDayUid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetValidHoldDayUidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).GetValidHoldDayUid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/GetValidHoldDayUid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).GetValidHoldDayUid(ctx, req.(*GetValidHoldDayUidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_GetBindGuildInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBindGuildInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).GetBindGuildInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/GetBindGuildInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).GetBindGuildInfo(ctx, req.(*GetBindGuildInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_BatchGetBindGuildInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetBindGuildInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).BatchGetBindGuildInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/BatchGetBindGuildInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).BatchGetBindGuildInfo(ctx, req.(*BatchGetBindGuildInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignAnchorStats_GetBindGuildInfoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBindGuildInfoListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignAnchorStatsServer).GetBindGuildInfoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sign_anchor_stats.SignAnchorStats/GetBindGuildInfoList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignAnchorStatsServer).GetBindGuildInfoList(ctx, req.(*GetBindGuildInfoListReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _SignAnchorStats_serviceDesc = grpc.ServiceDesc{
	ServiceName: "sign_anchor_stats.SignAnchorStats",
	HandlerType: (*SignAnchorStatsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetMultiThisMonthChannelStat",
			Handler:    _SignAnchorStats_GetMultiThisMonthChannelStat_Handler,
		},
		{
			MethodName: "GetMultiAnchorMonthStat",
			Handler:    _SignAnchorStats_GetMultiAnchorMonthStat_Handler,
		},
		{
			MethodName: "GetMultiAnchorChannelStat",
			Handler:    _SignAnchorStats_GetMultiAnchorChannelStat_Handler,
		},
		{
			MethodName: "GetUserTbeanConsume",
			Handler:    _SignAnchorStats_GetUserTbeanConsume_Handler,
		},
		{
			MethodName: "GetMultiAnchorDailyStatsListByGuildId",
			Handler:    _SignAnchorStats_GetMultiAnchorDailyStatsListByGuildId_Handler,
		},
		{
			MethodName: "GetPgcMonthlyInfoList",
			Handler:    _SignAnchorStats_GetPgcMonthlyInfoList_Handler,
		},
		{
			MethodName: "GetPgcDailyInfoList",
			Handler:    _SignAnchorStats_GetPgcDailyInfoList_Handler,
		},
		{
			MethodName: "GetGuildMonthlyStatsInfoList",
			Handler:    _SignAnchorStats_GetGuildMonthlyStatsInfoList_Handler,
		},
		{
			MethodName: "GetMultiAnchorDailyStatsList",
			Handler:    _SignAnchorStats_GetMultiAnchorDailyStatsList_Handler,
		},
		{
			MethodName: "GetMultiPlayerHomepage",
			Handler:    _SignAnchorStats_GetMultiPlayerHomepage_Handler,
		},
		{
			MethodName: "GetMultiPlayerBaseInfo",
			Handler:    _SignAnchorStats_GetMultiPlayerBaseInfo_Handler,
		},
		{
			MethodName: "GetMultiPlayerMonthConsumeTop10",
			Handler:    _SignAnchorStats_GetMultiPlayerMonthConsumeTop10_Handler,
		},
		{
			MethodName: "GetMultiPlayerMonthCommunityInfo",
			Handler:    _SignAnchorStats_GetMultiPlayerMonthCommunityInfo_Handler,
		},
		{
			MethodName: "CheckUserInteractEntry",
			Handler:    _SignAnchorStats_CheckUserInteractEntry_Handler,
		},
		{
			MethodName: "GetUserInteractInfo",
			Handler:    _SignAnchorStats_GetUserInteractInfo_Handler,
		},
		{
			MethodName: "GetUserInteractViewPer",
			Handler:    _SignAnchorStats_GetUserInteractViewPer_Handler,
		},
		{
			MethodName: "SetUserInteractViewPer",
			Handler:    _SignAnchorStats_SetUserInteractViewPer_Handler,
		},
		{
			MethodName: "GetGuildChannelList",
			Handler:    _SignAnchorStats_GetGuildChannelList_Handler,
		},
		{
			MethodName: "AddMultiPlayerHall",
			Handler:    _SignAnchorStats_AddMultiPlayerHall_Handler,
		},
		{
			MethodName: "ListMultiPlayerHall",
			Handler:    _SignAnchorStats_ListMultiPlayerHall_Handler,
		},
		{
			MethodName: "DelMultiPlayerHall",
			Handler:    _SignAnchorStats_DelMultiPlayerHall_Handler,
		},
		{
			MethodName: "GetGuildMultiPlayerHall",
			Handler:    _SignAnchorStats_GetGuildMultiPlayerHall_Handler,
		},
		{
			MethodName: "GetHallTaskConfList",
			Handler:    _SignAnchorStats_GetHallTaskConfList_Handler,
		},
		{
			MethodName: "GetHallTaskConfById",
			Handler:    _SignAnchorStats_GetHallTaskConfById_Handler,
		},
		{
			MethodName: "AddHallTaskConf",
			Handler:    _SignAnchorStats_AddHallTaskConf_Handler,
		},
		{
			MethodName: "DelHallTaskConf",
			Handler:    _SignAnchorStats_DelHallTaskConf_Handler,
		},
		{
			MethodName: "DistributeHallTask",
			Handler:    _SignAnchorStats_DistributeHallTask_Handler,
		},
		{
			MethodName: "DelHallTask",
			Handler:    _SignAnchorStats_DelHallTask_Handler,
		},
		{
			MethodName: "GetGuildHallTask",
			Handler:    _SignAnchorStats_GetGuildHallTask_Handler,
		},
		{
			MethodName: "GetGuildHallTaskStats",
			Handler:    _SignAnchorStats_GetGuildHallTaskStats_Handler,
		},
		{
			MethodName: "GetGuildHallTaskStatsDetial",
			Handler:    _SignAnchorStats_GetGuildHallTaskStatsDetial_Handler,
		},
		{
			MethodName: "GetHallTaskDistributeHistory",
			Handler:    _SignAnchorStats_GetHallTaskDistributeHistory_Handler,
		},
		{
			MethodName: "GetHallTaskCacheInfo",
			Handler:    _SignAnchorStats_GetHallTaskCacheInfo_Handler,
		},
		{
			MethodName: "GetHallTask",
			Handler:    _SignAnchorStats_GetHallTask_Handler,
		},
		{
			MethodName: "GetHallTaskHistory",
			Handler:    _SignAnchorStats_GetHallTaskHistory_Handler,
		},
		{
			MethodName: "GetTicketOrderCount",
			Handler:    _SignAnchorStats_GetTicketOrderCount_Handler,
		},
		{
			MethodName: "GetTicketOrderList",
			Handler:    _SignAnchorStats_GetTicketOrderList_Handler,
		},
		{
			MethodName: "GetPresentOrderCount",
			Handler:    _SignAnchorStats_GetPresentOrderCount_Handler,
		},
		{
			MethodName: "GetPresentOrderList",
			Handler:    _SignAnchorStats_GetPresentOrderList_Handler,
		},
		{
			MethodName: "ReplaceOrder",
			Handler:    _SignAnchorStats_ReplaceOrder_Handler,
		},
		{
			MethodName: "GetValidHoldDayUid",
			Handler:    _SignAnchorStats_GetValidHoldDayUid_Handler,
		},
		{
			MethodName: "GetBindGuildInfo",
			Handler:    _SignAnchorStats_GetBindGuildInfo_Handler,
		},
		{
			MethodName: "BatchGetBindGuildInfo",
			Handler:    _SignAnchorStats_BatchGetBindGuildInfo_Handler,
		},
		{
			MethodName: "GetBindGuildInfoList",
			Handler:    _SignAnchorStats_GetBindGuildInfoList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/sign-anchor-stats/sign-anchor-stats.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/sign-anchor-stats/sign-anchor-stats.proto", fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5)
}

var fileDescriptor_sign_anchor_stats_9b9e2bd8dcc586d5 = []byte{
	// 6299 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x7d, 0xdd, 0x6b, 0x1c, 0x4b,
	0x76, 0xb8, 0x7a, 0x46, 0x1f, 0x33, 0x47, 0x5f, 0xe3, 0xb6, 0x25, 0x4b, 0x63, 0xfb, 0xda, 0x6e,
	0x7f, 0x5c, 0x5b, 0xd7, 0x5f, 0xd7, 0xe6, 0xae, 0x7f, 0xbb, 0x77, 0x3f, 0xee, 0x68, 0x34, 0x92,
	0xe7, 0x5e, 0x7d, 0xed, 0x68, 0x24, 0xaf, 0x97, 0xfd, 0xd1, 0xb4, 0x66, 0x4a, 0x52, 0x47, 0xa3,
	0xee, 0xf1, 0x54, 0x8f, 0x64, 0x2d, 0xc9, 0xb2, 0x84, 0x10, 0x58, 0x12, 0x08, 0x49, 0x58, 0x48,
	0xc2, 0xe6, 0x61, 0xc9, 0x26, 0x2f, 0x79, 0x08, 0x61, 0x61, 0x43, 0x48, 0xc8, 0x53, 0x60, 0x21,
	0xb0, 0x79, 0xc8, 0x43, 0x9e, 0x12, 0x42, 0x20, 0x21, 0x90, 0xa7, 0x90, 0xbf, 0x20, 0xd4, 0xa9,
	0xea, 0xee, 0xea, 0xee, 0xea, 0xf9, 0xf0, 0xf5, 0xc2, 0x42, 0xf2, 0xa4, 0xe9, 0x53, 0xa7, 0x4e,
	0x9d, 0x3a, 0xe7, 0xd4, 0x39, 0xa7, 0xaa, 0x4e, 0xb7, 0xe0, 0xff, 0x79, 0xde, 0xe3, 0xd7, 0x5d,
	0xbb, 0x71, 0x4c, 0xed, 0xd6, 0x29, 0xe9, 0x3c, 0xa6, 0xf6, 0xa1, 0xf3, 0xd0, 0x72, 0x1a, 0x47,
	0x6e, 0xe7, 0x21, 0xf5, 0x2c, 0x8f, 0x26, 0x21, 0x8f, 0xda, 0x1d, 0xd7, 0x73, 0xf5, 0x0b, 0xac,
	0xc1, 0xe4, 0x0d, 0x26, 0x36, 0x14, 0xe3, 0xc4, 0x1a, 0x47, 0x96, 0xe3, 0x90, 0xd6, 0xc3, 0x96,
	0x7d, 0x4a, 0x1e, 0x1e, 0x58, 0x0e, 0x4d, 0x42, 0x38, 0xb1, 0xe2, 0xa3, 0x58, 0xcf, 0x0e, 0x69,
	0xb8, 0x4e, 0xc3, 0x6e, 0x91, 0x87, 0xa7, 0x4f, 0x23, 0x0f, 0x1c, 0xdf, 0xf8, 0x0d, 0x0d, 0xae,
	0xaf, 0x11, 0x6f, 0xa3, 0xdb, 0xf2, 0xec, 0xfa, 0x91, 0x4d, 0x37, 0x5c, 0xc7, 0x3b, 0x2a, 0x73,
	0xe2, 0x3b, 0x9e, 0xe5, 0xd5, 0xc8, 0x6b, 0x7d, 0x11, 0x72, 0x87, 0x5d, 0xbb, 0xd5, 0x34, 0xed,
	0xe6, 0x82, 0x76, 0x43, 0xbb, 0x37, 0x5d, 0x9b, 0xc0, 0xe7, 0x6a, 0x53, 0x9f, 0x87, 0x71, 0xf7,
	0xe0, 0x80, 0x12, 0x6f, 0x21, 0x83, 0x0d, 0xe2, 0x49, 0xbf, 0x04, 0x63, 0x2d, 0xfb, 0xc4, 0xf6,
	0x16, 0xb2, 0x08, 0xe6, 0x0f, 0xfa, 0x75, 0x98, 0x14, 0x7c, 0x9b, 0x76, 0x93, 0x2e, 0x8c, 0xde,
	0xc8, 0xde, 0x9b, 0xae, 0x81, 0x00, 0x55, 0x9b, 0xd4, 0xf8, 0x0f, 0x0d, 0xae, 0xa5, 0xb2, 0x52,
	0xf5, 0xc8, 0x89, 0x7e, 0x0d, 0x20, 0x24, 0x21, 0xb8, 0xc9, 0x07, 0x14, 0xf4, 0xdb, 0x30, 0xe3,
	0x1d, 0xd9, 0xd4, 0x3c, 0x61, 0x7d, 0xcd, 0x03, 0x42, 0x90, 0xaf, 0xd1, 0xda, 0x94, 0xe7, 0x13,
	0x5c, 0x25, 0x84, 0x61, 0xb5, 0x2c, 0xea, 0x49, 0x58, 0x59, 0x8e, 0xc5, 0xa0, 0x01, 0xd6, 0x97,
	0xa0, 0x28, 0x61, 0x51, 0xeb, 0x84, 0x98, 0x6d, 0xd2, 0xb1, 0xdd, 0x26, 0xf6, 0x18, 0xc5, 0x1e,
	0xf3, 0x41, 0x8f, 0x1d, 0xeb, 0x84, 0x6c, 0x63, 0x33, 0xeb, 0xcb, 0x67, 0x6a, 0x3b, 0x66, 0xc7,
	0xf2, 0x6c, 0x77, 0x61, 0xec, 0x86, 0x76, 0x2f, 0x83, 0x33, 0xb5, 0x9d, 0x1a, 0x83, 0x18, 0xdf,
	0x81, 0x1b, 0xbd, 0xc5, 0x4e, 0xdb, 0xfa, 0x0a, 0x8c, 0xb6, 0x6c, 0xea, 0x2d, 0x68, 0x37, 0xb2,
	0xf7, 0x26, 0x9f, 0x3e, 0x79, 0x94, 0xb0, 0x93, 0x47, 0x3d, 0x65, 0x55, 0xc3, 0xde, 0x4c, 0x15,
	0x9e, 0xeb, 0x59, 0x2d, 0x64, 0x62, 0xba, 0xc6, 0x1f, 0x8c, 0x7f, 0xd0, 0xa0, 0xe8, 0x33, 0x50,
	0x42, 0x92, 0x7c, 0x16, 0xef, 0x5a, 0xe5, 0x05, 0xc8, 0x76, 0xed, 0xa6, 0x50, 0x35, 0xfb, 0xc9,
	0x34, 0x48, 0x3d, 0xab, 0xe3, 0x99, 0x9e, 0x7d, 0x42, 0x04, 0x53, 0x79, 0x84, 0xd4, 0xed, 0x13,
	0xc2, 0x46, 0x26, 0x4e, 0x93, 0x37, 0x8e, 0xf3, 0x91, 0x89, 0xd3, 0xc4, 0xa6, 0xeb, 0x30, 0x29,
	0x66, 0xef, 0x9d, 0xb7, 0xc9, 0xc2, 0x04, 0xb6, 0x02, 0x07, 0xd5, 0xcf, 0xdb, 0xc4, 0xf8, 0xe7,
	0x0c, 0x2c, 0xa8, 0x66, 0x84, 0x96, 0xa3, 0xc3, 0x68, 0xd3, 0xf2, 0x08, 0x4e, 0x27, 0x5f, 0xc3,
	0xdf, 0x3e, 0x77, 0x7c, 0x22, 0xc8, 0xdd, 0x2d, 0x98, 0xee, 0x90, 0x43, 0x9b, 0x7a, 0xa4, 0xc3,
	0x79, 0xe0, 0xb3, 0x99, 0xf2, 0x81, 0xc8, 0xc8, 0x03, 0xb8, 0xd0, 0x70, 0x1d, 0xaf, 0x63, 0x35,
	0xbc, 0x1d, 0x9f, 0x71, 0x61, 0x10, 0xc9, 0x06, 0xfd, 0x1e, 0xcc, 0xfa, 0xc0, 0x0a, 0x9f, 0x09,
	0xce, 0x7a, 0xb4, 0x16, 0x07, 0xeb, 0xb7, 0x61, 0x1a, 0x8d, 0x6d, 0xcf, 0x6a, 0xd9, 0xcd, 0x15,
	0xeb, 0x5c, 0x08, 0x20, 0x0a, 0xd4, 0xef, 0xc2, 0x4c, 0x08, 0x78, 0xe1, 0x76, 0x3b, 0x42, 0x12,
	0x31, 0x28, 0x9b, 0x1c, 0x33, 0xd4, 0x1c, 0x8e, 0xc5, 0x7e, 0xea, 0x37, 0x61, 0xca, 0xda, 0xb7,
	0x5b, 0xb6, 0x77, 0xce, 0x25, 0x98, 0xc7, 0x7e, 0x93, 0x02, 0xc6, 0x44, 0xa8, 0x5f, 0x81, 0xfc,
	0x29, 0xa3, 0x60, 0x52, 0xd2, 0x58, 0x00, 0x6c, 0xcf, 0x21, 0x60, 0x87, 0x34, 0x0c, 0x0f, 0xae,
	0xa4, 0xda, 0x0c, 0x6d, 0xeb, 0x5f, 0x8b, 0xd8, 0xeb, 0x07, 0x69, 0xf6, 0xaa, 0x50, 0x4e, 0x4f,
	0x53, 0xfd, 0x4f, 0x0d, 0xae, 0x46, 0x87, 0xfd, 0x79, 0xf9, 0xa7, 0x77, 0x69, 0xac, 0x1f, 0xc1,
	0x68, 0xd7, 0xb1, 0x3d, 0xd4, 0xcd, 0xcc, 0xd3, 0x9b, 0x0a, 0x61, 0x30, 0xb4, 0x55, 0xbb, 0xe5,
	0x91, 0xce, 0xae, 0x63, 0x7b, 0x35, 0x44, 0x37, 0xfe, 0x26, 0x03, 0x45, 0xf5, 0x4c, 0x87, 0x30,
	0xe2, 0xa8, 0x93, 0xcc, 0xc6, 0x9d, 0xe4, 0x1d, 0x98, 0x61, 0x1d, 0x91, 0x6d, 0xea, 0x59, 0x27,
	0x6d, 0x61, 0xbb, 0xd3, 0x0c, 0x5a, 0xf7, 0x81, 0xef, 0xd8, 0x1a, 0xe7, 0x61, 0xdc, 0x76, 0x1a,
	0xee, 0x89, 0x6f, 0x90, 0xe2, 0x89, 0x31, 0xd3, 0xee, 0x10, 0x4a, 0x1c, 0xcf, 0x14, 0xed, 0x79,
	0xce, 0x8c, 0x80, 0x56, 0x39, 0xda, 0xfb, 0x30, 0x7b, 0x46, 0x3a, 0xe4, 0xcc, 0x6d, 0x1d, 0xf8,
	0x78, 0x80, 0x78, 0x33, 0x3e, 0x98, 0x23, 0x1a, 0x6f, 0xe0, 0x5a, 0x0f, 0x63, 0xa1, 0x6d, 0xbd,
	0x14, 0xb1, 0xd2, 0x87, 0xbd, 0xad, 0x74, 0x18, 0x97, 0xfa, 0x2d, 0x98, 0x5f, 0x23, 0xde, 0x2e,
	0x25, 0x9d, 0xfa, 0x3e, 0xb1, 0x9c, 0xb2, 0xeb, 0xd0, 0xee, 0x09, 0x61, 0x06, 0x2a, 0x34, 0xa4,
	0x85, 0x1a, 0x5a, 0x84, 0xdc, 0x3e, 0x39, 0xb4, 0x1d, 0xd3, 0xa3, 0x42, 0x71, 0x13, 0xf8, 0x5c,
	0xa7, 0xfa, 0x1c, 0x8c, 0xa3, 0x4d, 0x51, 0xdf, 0x36, 0x99, 0x45, 0x51, 0xe3, 0x31, 0x5c, 0x56,
	0x52, 0xa7, 0xed, 0x90, 0x1d, 0x4d, 0x66, 0xe7, 0xf7, 0x34, 0xb8, 0x17, 0x95, 0xc4, 0x8a, 0x65,
	0xb7, 0xce, 0xd9, 0x54, 0xe8, 0xba, 0x4d, 0xbd, 0xe5, 0xf3, 0x35, 0xbe, 0x48, 0xfa, 0x2c, 0xa1,
	0xd0, 0xeb, 0x76, 0x59, 0xd0, 0xce, 0xf0, 0xa0, 0xcd, 0x41, 0xbb, 0x76, 0x93, 0x46, 0xe6, 0x92,
	0x4d, 0x9b, 0xcb, 0xa8, 0x3c, 0x17, 0x1b, 0xee, 0x0f, 0xc8, 0x19, 0x6d, 0xeb, 0x5f, 0x8e, 0xe8,
	0xeb, 0x5e, 0x6f, 0x7d, 0x85, 0x84, 0xb8, 0xaa, 0x8c, 0xbf, 0xd6, 0x60, 0x4e, 0xd9, 0xae, 0x56,
	0x4a, 0x20, 0x84, 0x4c, 0x54, 0x08, 0xfe, 0xba, 0xe3, 0xf3, 0xe3, 0xeb, 0xee, 0x16, 0x4c, 0x0b,
	0x36, 0x84, 0x41, 0xf2, 0x39, 0x4e, 0x71, 0xa0, 0xb0, 0xdb, 0x1b, 0x30, 0x65, 0x53, 0x93, 0xbb,
	0xd4, 0xa6, 0x75, 0x8e, 0x16, 0x93, 0xab, 0x81, 0x4d, 0x83, 0x05, 0x14, 0xf1, 0xb8, 0xe3, 0x31,
	0x8f, 0xfb, 0x17, 0x52, 0x7a, 0xa6, 0x12, 0x55, 0x1f, 0xdd, 0x2d, 0x42, 0xae, 0x6b, 0x37, 0x4d,
	0x94, 0x1f, 0x57, 0xdc, 0x44, 0xd7, 0x6e, 0xb2, 0x8e, 0xc3, 0x6b, 0x4d, 0xf2, 0xa5, 0x63, 0x6a,
	0x5f, 0x3a, 0x2e, 0xf9, 0x52, 0xe3, 0x57, 0xc2, 0x04, 0x47, 0xcd, 0xf8, 0xe7, 0x55, 0x2d, 0x13,
	0x1c, 0x5a, 0xba, 0xd9, 0x70, 0x7c, 0xf7, 0x9e, 0x43, 0x40, 0xd9, 0xf1, 0x8c, 0x3f, 0x1b, 0x83,
	0x99, 0xed, 0xc3, 0x06, 0x46, 0x99, 0xd6, 0x79, 0xd5, 0x39, 0x70, 0xf5, 0x19, 0xc8, 0x78, 0x54,
	0x48, 0x28, 0xe3, 0xd1, 0x98, 0x97, 0xcc, 0xc4, 0xbd, 0xa4, 0x94, 0xac, 0x86, 0x19, 0xa2, 0xdf,
	0x83, 0xe5, 0x78, 0xf7, 0xa0, 0xc0, 0x15, 0x27, 0x38, 0x66, 0x6c, 0x70, 0x81, 0xcd, 0x20, 0x5c,
	0x78, 0x10, 0xc7, 0xd3, 0x97, 0xe0, 0x82, 0xd5, 0xf0, 0xec, 0x53, 0x22, 0xa3, 0x72, 0x21, 0xce,
	0xf2, 0x86, 0x10, 0xf7, 0x01, 0xe8, 0xaf, 0xbb, 0x16, 0xc6, 0x68, 0x09, 0x99, 0x8b, 0xb6, 0x20,
	0x5a, 0x7a, 0x50, 0x66, 0xac, 0x4e, 0xf0, 0xec, 0x42, 0xa6, 0xcc, 0xf8, 0x4d, 0x52, 0x0e, 0xd3,
	0x83, 0x28, 0x65, 0x86, 0xfd, 0x1c, 0x16, 0x92, 0x7c, 0x88, 0x74, 0x36, 0x8f, 0xe9, 0xec, 0x5c,
	0x9c, 0x1b, 0xcc, 0x6c, 0x15, 0x1d, 0x0f, 0x08, 0x11, 0x1d, 0x41, 0xd1, 0x71, 0x95, 0x10, 0xde,
	0x71, 0x09, 0x2e, 0x08, 0x7d, 0x4a, 0x62, 0x9f, 0xe4, 0x73, 0xe1, 0x7a, 0x0d, 0x65, 0xff, 0x31,
	0x14, 0x63, 0x83, 0x78, 0x1d, 0xcb, 0xa1, 0x62, 0x98, 0x29, 0x1c, 0xe6, 0x72, 0x64, 0x98, 0x3a,
	0x6b, 0xe7, 0x03, 0x7d, 0x08, 0x73, 0xfe, 0x10, 0xed, 0xe3, 0x43, 0x89, 0xbd, 0x69, 0xec, 0xa7,
	0x8b, 0xc6, 0xed, 0xe3, 0xc3, 0x80, 0xb7, 0x47, 0x70, 0xb1, 0xdd, 0x71, 0x0f, 0x08, 0xa5, 0xb6,
	0xeb, 0x98, 0xed, 0x8e, 0xd5, 0x40, 0xb5, 0xcc, 0xa0, 0x5a, 0x2e, 0x84, 0x4d, 0xdb, 0x1d, 0xab,
	0xc1, 0xf4, 0xf2, 0x21, 0xcc, 0x89, 0x45, 0xcd, 0x4c, 0xfa, 0x84, 0x9c, 0xec, 0x13, 0xae, 0xc8,
	0x59, 0xec, 0xa1, 0xf3, 0x05, 0x6e, 0x1f, 0x3a, 0x1b, 0xd8, 0xc4, 0x2c, 0xf6, 0x07, 0x1a, 0x2c,
	0xac, 0x11, 0x2f, 0x6a, 0xb4, 0xd2, 0x1a, 0xe7, 0xdb, 0x90, 0xc0, 0x82, 0x27, 0xf0, 0xb9, 0x8e,
	0xee, 0xb7, 0x11, 0x5b, 0xe3, 0x0d, 0xb1, 0xc6, 0x75, 0x18, 0x6d, 0x5b, 0x87, 0x81, 0xd7, 0x62,
	0xbf, 0xd9, 0xaa, 0x61, 0x7f, 0x4d, 0x6a, 0x7f, 0xdb, 0xf7, 0x58, 0x39, 0x06, 0xd8, 0xb1, 0xbf,
	0x4d, 0x22, 0xae, 0x64, 0x2c, 0xe2, 0x4a, 0x8c, 0xef, 0x6b, 0xb0, 0x98, 0xc2, 0x1e, 0x6d, 0xeb,
	0x5f, 0x85, 0xbc, 0xed, 0x1c, 0xb8, 0xa6, 0xb4, 0x9c, 0x55, 0x29, 0x4f, 0xb4, 0x77, 0x2d, 0x67,
	0x0b, 0x1a, 0x3d, 0xd7, 0x32, 0x6b, 0x74, 0xc8, 0x1b, 0xcf, 0x94, 0xe6, 0x92, 0x63, 0x80, 0x6d,
	0xeb, 0x90, 0x18, 0xff, 0x9d, 0x81, 0xa9, 0xed, 0xc3, 0x06, 0x7a, 0x87, 0x9f, 0xcb, 0x32, 0xbf,
	0x06, 0x20, 0x2d, 0x17, 0x2e, 0xb1, 0xbc, 0x15, 0xac, 0x93, 0x1b, 0x30, 0xc5, 0x8c, 0xe8, 0xd0,
	0x3e, 0xf0, 0x10, 0x81, 0x8b, 0x0d, 0xda, 0xc7, 0x87, 0x6b, 0xf6, 0x81, 0x27, 0x76, 0x9b, 0x1e,
	0x0b, 0xd9, 0x21, 0x0e, 0x5f, 0xcd, 0x53, 0x08, 0xf5, 0xb1, 0x0c, 0x98, 0xa6, 0xcc, 0xeb, 0x22,
	0x12, 0x93, 0x02, 0x4f, 0xa3, 0x26, 0x19, 0x90, 0xe1, 0x30, 0x41, 0x7c, 0x00, 0xba, 0xeb, 0x10,
	0xf3, 0xc8, 0xed, 0x76, 0x44, 0x48, 0x61, 0x88, 0x39, 0xee, 0x48, 0x5c, 0x87, 0xb0, 0x44, 0x0b,
	0xe3, 0x8a, 0x40, 0xf6, 0xce, 0xdc, 0x38, 0x32, 0x4f, 0xf9, 0x67, 0xbd, 0x33, 0x37, 0x82, 0xfc,
	0x10, 0x2e, 0x1e, 0x30, 0xc4, 0x18, 0x36, 0xdf, 0x00, 0x14, 0x58, 0x93, 0x8c, 0x6e, 0xfc, 0xb9,
	0x86, 0xb9, 0x8e, 0x2c, 0x77, 0xc9, 0x52, 0x83, 0xb8, 0xa2, 0xa5, 0xc5, 0x95, 0x8c, 0x1c, 0x57,
	0x64, 0x03, 0xce, 0xaa, 0x0d, 0x78, 0x34, 0xcd, 0x80, 0xc7, 0x7a, 0x18, 0xf0, 0x78, 0xd4, 0x80,
	0x7f, 0x5b, 0xc3, 0x0c, 0x2a, 0xc9, 0x33, 0x06, 0xa2, 0x84, 0xf9, 0x5e, 0x57, 0x9b, 0x6f, 0xd0,
	0xf7, 0x9d, 0x18, 0xef, 0x6f, 0x8d, 0xc1, 0x1c, 0xe6, 0x3a, 0x62, 0x55, 0x60, 0x78, 0x53, 0x5a,
	0xf1, 0x15, 0xc8, 0xf3, 0x89, 0x85, 0x67, 0x1a, 0x7c, 0xa6, 0xcc, 0x76, 0xee, 0xc2, 0xac, 0xcc,
	0x2c, 0x63, 0x83, 0x8f, 0x34, 0xcd, 0xc0, 0x7d, 0xe2, 0xd0, 0xe8, 0x30, 0x71, 0x68, 0x6c, 0x98,
	0x38, 0x34, 0x3e, 0x4c, 0x1c, 0x9a, 0x78, 0x8b, 0x38, 0x94, 0x7b, 0xdb, 0x38, 0x94, 0xef, 0x15,
	0x87, 0x1e, 0xc2, 0x45, 0x2e, 0xea, 0x68, 0x70, 0xe0, 0xb1, 0xab, 0x80, 0x4d, 0x03, 0x84, 0x86,
	0xc9, 0xb4, 0xd0, 0xf0, 0x11, 0x5c, 0x76, 0xc8, 0x99, 0xa9, 0xea, 0x33, 0x85, 0x7d, 0x2e, 0x39,
	0xe4, 0x6c, 0x7b, 0xf0, 0x88, 0x32, 0x9d, 0x16, 0x51, 0xf4, 0xa7, 0x30, 0xcf, 0x46, 0x8a, 0x76,
	0x93, 0xe2, 0x96, 0xee, 0x90, 0xb3, 0x3d, 0xa9, 0x1b, 0x5b, 0xd9, 0x7f, 0xca, 0x13, 0x4e, 0xa5,
	0x51, 0xfa, 0x4b, 0xfc, 0x3a, 0x4c, 0xf2, 0x25, 0x8e, 0x21, 0x48, 0x18, 0x29, 0x20, 0x08, 0xbb,
	0x30, 0x63, 0x65, 0x0b, 0x9d, 0x37, 0x8b, 0x05, 0x41, 0x1c, 0x4e, 0x2f, 0xb2, 0x44, 0xb3, 0x89,
	0x2c, 0x7b, 0xa8, 0xe5, 0x6e, 0xfc, 0x54, 0xc3, 0x2c, 0xb3, 0x07, 0xb7, 0xb4, 0xcd, 0x3c, 0xab,
	0x3f, 0x60, 0xb8, 0xc0, 0xa7, 0x6b, 0x93, 0x62, 0x54, 0x5c, 0xc2, 0x15, 0xd9, 0x01, 0x64, 0x52,
	0xd3, 0x51, 0xe5, 0x40, 0x69, 0x9e, 0x20, 0xdb, 0xcb, 0x13, 0x8c, 0xc6, 0x3c, 0xc1, 0x31, 0x46,
	0x57, 0x4c, 0x77, 0xb7, 0x5b, 0xd6, 0x39, 0xe9, 0xbc, 0x70, 0x4f, 0x08, 0xc3, 0x54, 0xef, 0x1f,
	0x59, 0xd4, 0x6a, 0xd9, 0x6c, 0xd3, 0x8c, 0x07, 0x39, 0x5c, 0xc6, 0xc0, 0x41, 0xfe, 0x39, 0xce,
	0x89, 0xd5, 0x39, 0x26, 0x5e, 0x28, 0xe6, 0x1c, 0x07, 0x54, 0x9b, 0xc6, 0x5f, 0x8e, 0x85, 0x87,
	0x7f, 0xf1, 0xd1, 0x68, 0x5b, 0x31, 0xdc, 0x02, 0x4c, 0x58, 0x8d, 0x86, 0xdb, 0x15, 0xfe, 0x2d,
	0x5f, 0xf3, 0x1f, 0xf5, 0x22, 0xe4, 0x1c, 0xbb, 0x71, 0xec, 0x58, 0xe2, 0xa8, 0x2c, 0x5f, 0x0b,
	0x9e, 0x23, 0x9a, 0x1e, 0x8d, 0x6a, 0xfa, 0x36, 0xcc, 0xf0, 0x26, 0x7a, 0xe4, 0x76, 0xbc, 0x30,
	0xdd, 0x98, 0x42, 0xe8, 0x0e, 0x03, 0x56, 0xf1, 0x1c, 0x83, 0x63, 0x21, 0xf9, 0x71, 0x24, 0xcf,
	0xdd, 0xe0, 0x26, 0xa3, 0x7f, 0x05, 0xf2, 0xa8, 0xa2, 0xa6, 0x75, 0x4e, 0x45, 0xb8, 0xcc, 0x31,
	0xc0, 0x8a, 0x75, 0x8e, 0x51, 0xe5, 0x80, 0x65, 0x84, 0x61, 0x84, 0x9c, 0x60, 0xcf, 0x4c, 0x11,
	0xd7, 0x61, 0xf2, 0xa0, 0x63, 0x13, 0xa7, 0x49, 0xa5, 0x20, 0x07, 0x02, 0xc4, 0x10, 0x56, 0x01,
	0x3c, 0xb7, 0x69, 0x9d, 0x9b, 0x4c, 0xb1, 0xb8, 0x92, 0x27, 0x9f, 0xbe, 0x9f, 0xb6, 0x3b, 0xe1,
	0x02, 0x0c, 0xe3, 0x42, 0x1e, 0xbb, 0xa2, 0x13, 0x2f, 0x43, 0xfe, 0x8c, 0x90, 0x63, 0x4e, 0x66,
	0x0a, 0xc9, 0xdc, 0xed, 0x4d, 0xe6, 0x25, 0x21, 0xc7, 0xdc, 0xa6, 0xce, 0xc4, 0x2f, 0xc6, 0x0c,
	0x4f, 0xfd, 0x90, 0xca, 0xf4, 0x20, 0xcc, 0xa0, 0x85, 0x72, 0x66, 0x4e, 0xfc, 0x9f, 0x4c, 0x98,
	0xed, 0xee, 0x7e, 0xcb, 0x6e, 0x98, 0x27, 0xf4, 0x10, 0x3d, 0x40, 0xbe, 0x96, 0xe7, 0x90, 0x0d,
	0x7a, 0xa8, 0x7f, 0x11, 0x16, 0x1b, 0xfc, 0x4c, 0xc1, 0xf4, 0xdc, 0xf6, 0x33, 0x53, 0x28, 0x98,
	0xaf, 0x88, 0xd9, 0x1b, 0xd9, 0x7b, 0xf9, 0xda, 0xbc, 0x40, 0xa8, 0xbb, 0xed, 0x67, 0x25, 0xde,
	0x8c, 0x56, 0x7f, 0x0c, 0x8b, 0xd2, 0xa1, 0x7b, 0xc3, 0x3d, 0x39, 0xe9, 0x3a, 0xcc, 0xed, 0x22,
	0xc3, 0x05, 0x64, 0xf8, 0xc9, 0x00, 0x0c, 0x97, 0xfd, 0x8e, 0xc8, 0xf9, 0x7c, 0x70, 0x62, 0x1f,
	0x81, 0x1b, 0xff, 0x3e, 0x26, 0xae, 0x08, 0xd2, 0x7a, 0x2a, 0xcf, 0xc8, 0x1e, 0x80, 0xce, 0xb9,
	0x63, 0x0e, 0xb1, 0x43, 0x5a, 0xcc, 0x73, 0x3b, 0x62, 0xd9, 0x14, 0xb0, 0x65, 0x93, 0x9c, 0xd5,
	0x04, 0x9c, 0x85, 0x8e, 0x10, 0x9b, 0xbb, 0xcf, 0xa0, 0x0f, 0x5f, 0x4b, 0x73, 0x7e, 0x1f, 0xf4,
	0x9f, 0x41, 0xc7, 0x25, 0xb8, 0x20, 0xee, 0x14, 0xdc, 0x46, 0x97, 0x9a, 0xfc, 0x54, 0x46, 0x04,
	0x58, 0x6c, 0x58, 0x65, 0xf0, 0x3a, 0x03, 0xb3, 0x30, 0x13, 0x0e, 0xc2, 0xf1, 0xa5, 0x08, 0xeb,
	0xd3, 0xc7, 0x0e, 0xc2, 0xff, 0x73, 0xf4, 0x96, 0x4b, 0xa9, 0xc9, 0x8d, 0x55, 0xda, 0x1a, 0xf2,
	0xe9, 0xad, 0xbb, 0x94, 0xae, 0x62, 0x13, 0xeb, 0xf2, 0x18, 0x2e, 0xc9, 0x5d, 0x82, 0x21, 0xf8,
	0x52, 0xb9, 0x10, 0xf6, 0xf0, 0xc7, 0xf8, 0x0a, 0x5c, 0x49, 0x4a, 0xc9, 0x3c, 0xec, 0xb8, 0x67,
	0x1d, 0x26, 0x50, 0x1e, 0x6e, 0x17, 0xe2, 0xe2, 0x5a, 0x13, 0xed, 0xfa, 0x0b, 0xb8, 0x99, 0x26,
	0xb6, 0x90, 0x08, 0x0f, 0xbd, 0xd7, 0x94, 0xf2, 0x0b, 0x28, 0x7d, 0x0c, 0xc5, 0x84, 0x1c, 0x43,
	0x12, 0x3c, 0x12, 0x5f, 0x8e, 0x09, 0x34, 0xe8, 0xfc, 0x55, 0xb8, 0xaa, 0x10, 0x6c, 0xd8, 0x7d,
	0x32, 0x3a, 0x0d, 0x7f, 0xf6, 0x41, 0xff, 0x65, 0x78, 0x4f, 0x29, 0xe9, 0x90, 0x02, 0xdf, 0x5f,
	0x16, 0x93, 0x22, 0x0f, 0x68, 0x7c, 0x02, 0xd7, 0x54, 0xa2, 0x0f, 0x49, 0xf0, 0xad, 0xe6, 0x62,
	0x42, 0x07, 0x3e, 0x05, 0xe3, 0x37, 0x33, 0x70, 0x49, 0xe5, 0x5f, 0xd2, 0x8e, 0x80, 0xfd, 0xbc,
	0x30, 0xcf, 0x8f, 0xfa, 0x9f, 0xc0, 0x25, 0xb6, 0x73, 0xf0, 0x8f, 0x56, 0xbb, 0xb6, 0x70, 0x76,
	0xdc, 0x7c, 0x75, 0xa9, 0x6d, 0xd7, 0xe6, 0x4e, 0xef, 0x63, 0x28, 0x1e, 0xd8, 0x1d, 0xea, 0x99,
	0xca, 0x7e, 0xdc, 0x88, 0x2f, 0x23, 0xc6, 0x4e, 0xb2, 0xf3, 0x15, 0xc8, 0x1f, 0xb9, 0xad, 0x26,
	0xee, 0x1f, 0xd0, 0x84, 0xf3, 0xb5, 0x1c, 0x03, 0xe0, 0xd1, 0xef, 0x75, 0x98, 0x14, 0xc9, 0x21,
	0x36, 0x73, 0x3f, 0x0e, 0x1c, 0x84, 0x08, 0x37, 0x61, 0x2a, 0xb2, 0x2e, 0xc5, 0xd6, 0xc7, 0x09,
	0x6d, 0xcc, 0xf8, 0x69, 0x06, 0x2e, 0x2a, 0xfc, 0xe4, 0x2f, 0xa6, 0x34, 0x0c, 0x98, 0x46, 0x69,
	0xb0, 0xc0, 0x24, 0x2d, 0xea, 0x49, 0x06, 0x64, 0xc1, 0x89, 0xe1, 0xdc, 0x05, 0x91, 0x18, 0x87,
	0x58, 0xe2, 0x7c, 0x9d, 0x83, 0x7d, 0xbc, 0xfe, 0xb2, 0x61, 0xce, 0x4d, 0xe1, 0xa8, 0x78, 0xd0,
	0x2b, 0x38, 0xb1, 0x35, 0x66, 0xfc, 0x57, 0x36, 0x62, 0x58, 0x41, 0xac, 0xf8, 0x3f, 0x51, 0x0e,
	0x2b, 0x4a, 0x96, 0xfa, 0x9f, 0xda, 0xae, 0xf0, 0x70, 0x61, 0xe2, 0xc7, 0xf7, 0xd8, 0x17, 0x82,
	0xa6, 0xba, 0x9f, 0x01, 0xde, 0x85, 0xd9, 0x10, 0xbf, 0x24, 0x25, 0x1f, 0xd3, 0x01, 0xb8, 0x94,
	0xc0, 0x5b, 0x96, 0xb6, 0x13, 0x21, 0xde, 0x72, 0x02, 0xaf, 0x2c, 0x6d, 0x21, 0x42, 0xbc, 0x32,
	0x4b, 0xea, 0xff, 0x6e, 0x5c, 0x5c, 0x2a, 0x49, 0x2a, 0x5f, 0x21, 0x9e, 0x6d, 0xb5, 0xfe, 0x77,
	0x28, 0x3e, 0x45, 0x4f, 0x13, 0x43, 0xe8, 0x29, 0x37, 0xa0, 0x9e, 0xf2, 0x03, 0xea, 0x09, 0x14,
	0x7a, 0x0a, 0xb3, 0x14, 0xb6, 0xeb, 0x8c, 0xc5, 0x2b, 0x9e, 0x11, 0xac, 0x12, 0x12, 0xc4, 0x98,
	0x75, 0xb8, 0x25, 0x4a, 0x13, 0x14, 0xe2, 0x8d, 0x07, 0xab, 0xeb, 0x88, 0x9a, 0x94, 0x73, 0x40,
	0xed, 0x25, 0xdc, 0x17, 0x63, 0xa7, 0xaa, 0x2c, 0x1e, 0xbd, 0x6e, 0x73, 0x96, 0xd4, 0x0a, 0x4c,
	0x86, 0xe3, 0x88, 0x3a, 0x43, 0x5a, 0x33, 0x52, 0x38, 0x7e, 0x11, 0x2a, 0x37, 0x19, 0x8e, 0x63,
	0xaa, 0x0e, 0x29, 0xcc, 0x4a, 0xe1, 0xb8, 0x24, 0x2b, 0x3e, 0xa0, 0xf1, 0x29, 0x18, 0x9c, 0x86,
	0xc2, 0x0c, 0x42, 0x3a, 0x05, 0xa4, 0xc3, 0x47, 0xdb, 0x8b, 0x1b, 0x45, 0x10, 0x98, 0x4b, 0xf1,
	0x9d, 0xda, 0xb2, 0x45, 0x09, 0xa6, 0xac, 0xe4, 0x35, 0x5b, 0x4a, 0xb8, 0x21, 0xe3, 0x7b, 0x27,
	0xfc, 0x9d, 0xbc, 0x9f, 0x35, 0x7e, 0x37, 0x13, 0xdf, 0x7f, 0x85, 0x34, 0x68, 0x5b, 0xdf, 0x82,
	0xd9, 0x26, 0x0b, 0xf7, 0x66, 0xfc, 0x4c, 0x6a, 0xe0, 0x3d, 0xc8, 0x74, 0x53, 0x3e, 0xe2, 0xd2,
	0x37, 0x60, 0x26, 0xd8, 0x87, 0xc8, 0x5b, 0xdc, 0x81, 0xe9, 0x4d, 0xf9, 0xbb, 0x11, 0x24, 0xb7,
	0x0b, 0xb3, 0xe1, 0x8e, 0x24, 0x3c, 0xb7, 0xeb, 0x71, 0x99, 0xaa, 0xf4, 0x3b, 0xe2, 0x26, 0xd9,
	0x27, 0x6b, 0x7c, 0x01, 0x8c, 0xa8, 0x50, 0x44, 0x6e, 0xef, 0xef, 0x39, 0x3e, 0x7c, 0xa2, 0xdc,
	0x0b, 0x1b, 0xbf, 0x9f, 0x85, 0x5b, 0x7d, 0x3b, 0xd2, 0xb6, 0xfe, 0x06, 0x16, 0x22, 0xdb, 0x94,
	0x60, 0xb3, 0xf3, 0xe1, 0x13, 0x21, 0xdf, 0xaf, 0xa9, 0xb6, 0xfc, 0xfd, 0x29, 0x3f, 0x6a, 0xb8,
	0x27, 0x0c, 0xd0, 0xa9, 0xcd, 0x49, 0x9b, 0x96, 0x10, 0x87, 0x8d, 0x2c, 0x55, 0x12, 0x45, 0x47,
	0xce, 0xbc, 0xa3, 0x91, 0x83, 0x42, 0x24, 0x19, 0xa7, 0xf8, 0x1d, 0xc8, 0xf9, 0x28, 0xef, 0x6c,
	0x5b, 0x1f, 0x6c, 0x72, 0xfc, 0x89, 0x84, 0xe5, 0x50, 0xdc, 0x2e, 0x04, 0x07, 0xab, 0x84, 0x18,
	0xcf, 0x53, 0x54, 0x23, 0xef, 0xf4, 0x94, 0x4a, 0xfd, 0x6e, 0x06, 0x6e, 0xf7, 0xef, 0x49, 0xdb,
	0xbd, 0x37, 0x9f, 0xda, 0xbb, 0xdd, 0x7c, 0xb2, 0xc1, 0x22, 0x8a, 0x8c, 0x0c, 0x96, 0x79, 0xdb,
	0xc1, 0x24, 0xd5, 0xc9, 0x3b, 0xdd, 0x4f, 0x20, 0x1f, 0x66, 0xfd, 0x61, 0xf9, 0x04, 0x17, 0x92,
	0x5f, 0x3e, 0x11, 0xb9, 0x3d, 0xce, 0xc4, 0x6e, 0x8f, 0xbf, 0x0a, 0x13, 0x2b, 0xdd, 0xce, 0x60,
	0xfd, 0x9b, 0xd6, 0x79, 0xa4, 0xff, 0x8a, 0x75, 0x6e, 0xfc, 0x54, 0x8b, 0xa4, 0x8a, 0x65, 0xab,
	0x71, 0x84, 0x5e, 0x4a, 0xff, 0x18, 0x20, 0xf4, 0x50, 0x42, 0xca, 0x57, 0x15, 0x13, 0x97, 0x4e,
	0x45, 0x02, 0x8f, 0xa4, 0x3f, 0x97, 0x4f, 0x45, 0xb8, 0xd0, 0x8a, 0xaa, 0xbe, 0x9c, 0x73, 0xe9,
	0x24, 0xe4, 0x8b, 0x91, 0x93, 0x90, 0x6c, 0xdf, 0x9e, 0xe1, 0xe1, 0x87, 0xf1, 0x27, 0x5a, 0x24,
	0x03, 0xe2, 0x4e, 0x28, 0x9c, 0xcf, 0x4a, 0x9a, 0xc7, 0xed, 0x3d, 0xa9, 0x98, 0x9b, 0x5d, 0x4e,
	0xfa, 0x45, 0xbe, 0xba, 0x7b, 0x31, 0x19, 0x73, 0x82, 0x3f, 0xd1, 0x60, 0xb1, 0x7c, 0x44, 0x1a,
	0xc7, 0xbb, 0x94, 0x74, 0xaa, 0x8e, 0x47, 0x78, 0x7d, 0x98, 0xd7, 0x39, 0x67, 0xeb, 0xe4, 0x1a,
	0x00, 0x6d, 0x10, 0x87, 0x98, 0x52, 0x90, 0xc9, 0x23, 0xa4, 0xae, 0x8c, 0x34, 0x2c, 0xe5, 0xb5,
	0x05, 0x11, 0x16, 0xc6, 0x45, 0xb2, 0x36, 0xe9, 0xc3, 0x76, 0x13, 0xc5, 0x42, 0xa3, 0xf1, 0xfb,
	0xb1, 0xbb, 0x30, 0xeb, 0x37, 0xb7, 0x58, 0xf8, 0x15, 0x47, 0x75, 0xa3, 0xb5, 0x69, 0x01, 0x5e,
	0xb7, 0x4f, 0x49, 0xb5, 0x69, 0xfc, 0x7f, 0x28, 0xa6, 0xf1, 0x4d, 0xdb, 0xa2, 0x0c, 0xe2, 0xc8,
	0xa2, 0x26, 0x61, 0x30, 0x64, 0x1d, 0xcb, 0x20, 0x5e, 0x58, 0x14, 0xb1, 0x18, 0x1b, 0xd8, 0x64,
	0x7a, 0xe4, 0x8d, 0xef, 0x8e, 0xf2, 0x08, 0xa9, 0x93, 0x37, 0x9e, 0xf1, 0x4d, 0x98, 0xf2, 0xa9,
	0xa2, 0xc6, 0x6e, 0xc1, 0x74, 0x30, 0xb1, 0x40, 0x18, 0xf9, 0x5a, 0x30, 0x5b, 0x94, 0xc7, 0x1d,
	0x98, 0x09, 0x90, 0x4e, 0xad, 0x56, 0xd7, 0xcf, 0x67, 0x83, 0xae, 0x7b, 0x0c, 0x68, 0x10, 0x28,
	0xf8, 0xb4, 0xcb, 0xa4, 0xc3, 0xe9, 0x7f, 0x01, 0xf2, 0x78, 0x7c, 0x28, 0x19, 0xf8, 0xe2, 0x23,
	0x31, 0x63, 0x26, 0x07, 0x2c, 0xb9, 0x5d, 0xb5, 0x1c, 0x71, 0x02, 0x7c, 0x20, 0x7e, 0xe1, 0x65,
	0x16, 0xe9, 0x78, 0x78, 0xc6, 0x26, 0x7c, 0x2a, 0x7b, 0xde, 0xa0, 0x87, 0xc6, 0x8f, 0xb5, 0xa0,
	0x40, 0x48, 0x9e, 0xca, 0x2f, 0xb8, 0x5e, 0x7f, 0xa2, 0x05, 0x85, 0x47, 0x51, 0xae, 0x69, 0x9b,
	0x39, 0x8f, 0x2e, 0x25, 0x1d, 0x99, 0xeb, 0x1c, 0x03, 0x20, 0xd3, 0x5f, 0x4e, 0x1e, 0xa9, 0xab,
	0xee, 0xd4, 0x22, 0x44, 0xc3, 0x93, 0xf4, 0x4f, 0x20, 0x8f, 0x72, 0x94, 0x96, 0xfa, 0xad, 0x1e,
	0xbd, 0x7d, 0xbd, 0xd5, 0x50, 0xfa, 0xb8, 0xe4, 0x1f, 0x62, 0x9e, 0x26, 0xf3, 0xbd, 0x67, 0x93,
	0xb3, 0x6d, 0xd2, 0x51, 0x07, 0x9c, 0x8f, 0x30, 0x25, 0x53, 0xa2, 0xd3, 0xb6, 0x7e, 0x19, 0x26,
	0x6c, 0x6a, 0xba, 0x6d, 0xe2, 0x08, 0xd3, 0x1d, 0xb7, 0xe9, 0x56, 0x9b, 0x38, 0xc6, 0x2a, 0x2c,
	0xee, 0x0c, 0x3e, 0x8a, 0x4c, 0x27, 0x13, 0xa1, 0x73, 0x15, 0x8a, 0x3b, 0xa9, 0xc3, 0x1b, 0xcf,
	0xd0, 0x72, 0xf0, 0xf6, 0xa1, 0xec, 0x6b, 0xa7, 0x5f, 0xf1, 0x8f, 0xb1, 0x8b, 0x8a, 0x4b, 0x76,
	0xa2, 0x6d, 0xfd, 0x4b, 0x91, 0xc2, 0x9b, 0x3e, 0x67, 0xd2, 0x2f, 0xac, 0x16, 0xcf, 0xd7, 0x78,
	0x45, 0xd5, 0x0f, 0x35, 0x98, 0x2b, 0x35, 0x9b, 0x31, 0x04, 0xc6, 0xcb, 0xe7, 0xa0, 0xaa, 0x7f,
	0x08, 0x73, 0x36, 0x35, 0xb1, 0x74, 0xc8, 0x74, 0x5c, 0x4a, 0x5a, 0x07, 0x26, 0xce, 0x03, 0x75,
	0x9f, 0xab, 0xe9, 0x36, 0x5d, 0x67, 0x6d, 0x9b, 0xd8, 0x84, 0x73, 0x62, 0x53, 0xb7, 0xa9, 0xd9,
	0x60, 0x4e, 0x07, 0xcd, 0x3b, 0x57, 0x9b, 0xb0, 0x29, 0xfa, 0x20, 0xe3, 0x39, 0xcc, 0xab, 0x58,
	0xa4, 0x6d, 0xb6, 0x2a, 0x6c, 0x6a, 0x36, 0xed, 0x83, 0x03, 0x22, 0xf6, 0x94, 0xb9, 0x5a, 0xde,
	0xa6, 0x2b, 0x1c, 0x60, 0x34, 0x61, 0x9e, 0x09, 0x49, 0x31, 0xb9, 0x1e, 0x55, 0x56, 0xfe, 0xb5,
	0x55, 0x26, 0xed, 0xda, 0x2a, 0x1b, 0xbb, 0xb6, 0x3a, 0x86, 0xcb, 0xca, 0x51, 0xd2, 0x6a, 0xf9,
	0x02, 0xc9, 0x66, 0xde, 0x42, 0x5f, 0x5f, 0x87, 0xb9, 0x15, 0xd2, 0x1a, 0x6e, 0x46, 0xbd, 0x6b,
	0x26, 0x8c, 0x05, 0x98, 0x57, 0x91, 0xa4, 0x6d, 0xe3, 0xc7, 0xd1, 0x63, 0x3a, 0x9f, 0x95, 0x5e,
	0x63, 0x25, 0xaf, 0x82, 0x32, 0x7d, 0xaf, 0x82, 0xb2, 0xf1, 0xab, 0xa0, 0xc1, 0x9d, 0xdd, 0xa9,
	0x4d, 0xce, 0x7c, 0x67, 0x97, 0x0f, 0x9c, 0x1d, 0x5b, 0x6c, 0x55, 0x74, 0xab, 0x3e, 0x9e, 0x74,
	0xe5, 0xe4, 0x17, 0x88, 0xe0, 0x48, 0xd7, 0x61, 0xb2, 0xdb, 0x0e, 0xca, 0x67, 0xfd, 0x22, 0x74,
	0x0e, 0xc2, 0xc2, 0xdf, 0x54, 0x4b, 0xce, 0xa5, 0x59, 0xb2, 0xf1, 0x1c, 0x7d, 0x0f, 0xbf, 0x5c,
	0x1c, 0x46, 0x4f, 0xc6, 0xdf, 0x6a, 0x58, 0x91, 0xad, 0xee, 0x49, 0xdb, 0xfa, 0xcb, 0xc8, 0x8a,
	0x2c, 0xab, 0x37, 0x19, 0x69, 0xbd, 0x1f, 0x29, 0x1b, 0x90, 0x60, 0x71, 0x1d, 0x2e, 0xa9, 0x5a,
	0xfb, 0xbd, 0x9e, 0x31, 0x07, 0xe3, 0x6c, 0xf5, 0x91, 0x96, 0xf0, 0x7e, 0x63, 0x36, 0x5d, 0x21,
	0x2d, 0xe3, 0x47, 0x3c, 0x32, 0x32, 0x0a, 0x75, 0x8b, 0x1e, 0x97, 0x5d, 0xe7, 0x60, 0x80, 0xe2,
	0xc6, 0xcb, 0x30, 0xe1, 0x59, 0xf4, 0x38, 0xb4, 0x98, 0x71, 0xf6, 0xc8, 0xb5, 0x8d, 0x0d, 0x87,
	0x1d, 0xb7, 0xdb, 0x96, 0x0d, 0x66, 0x9a, 0x81, 0xd7, 0x18, 0x14, 0x55, 0xa9, 0xba, 0x6e, 0x2e,
	0x42, 0xb0, 0x4c, 0x13, 0xb7, 0xcd, 0x0e, 0x3a, 0xd4, 0x24, 0x97, 0xa9, 0xcb, 0xf6, 0x2b, 0x91,
	0x65, 0x7b, 0x5f, 0x21, 0x7e, 0x99, 0x98, 0xb4, 0x33, 0xe6, 0x2b, 0xf7, 0xdf, 0x32, 0x30, 0xaf,
	0x46, 0x90, 0xe7, 0xae, 0xf5, 0x9b, 0x7b, 0x46, 0x35, 0xf7, 0x35, 0x98, 0x6e, 0x5a, 0xe7, 0x26,
	0xe2, 0x4a, 0x3b, 0xf8, 0x5b, 0x7d, 0x78, 0x44, 0xee, 0x26, 0x9b, 0xd6, 0x39, 0x03, 0x60, 0xa0,
	0xae, 0x8a, 0xb3, 0x85, 0x90, 0xd2, 0xe8, 0xe0, 0x94, 0xf0, 0x5c, 0x21, 0x20, 0x75, 0x0d, 0xa0,
	0x43, 0xce, 0xac, 0x4e, 0x13, 0xb3, 0x27, 0xbe, 0x40, 0xf3, 0x1c, 0xb2, 0x41, 0x0f, 0xf5, 0xc7,
	0x70, 0x89, 0x25, 0x90, 0x78, 0x87, 0x8e, 0x43, 0x92, 0x83, 0x03, 0xd2, 0xe0, 0x47, 0x7e, 0xb9,
	0xda, 0x85, 0x23, 0x8b, 0x6e, 0x92, 0x37, 0xde, 0x4b, 0x42, 0x8e, 0x2b, 0xd8, 0xc0, 0xd2, 0x3f,
	0xd6, 0xa1, 0x69, 0x53, 0xaf, 0x63, 0xef, 0x77, 0x3d, 0xbe, 0x5a, 0x73, 0xb5, 0xe9, 0x23, 0x8b,
	0xae, 0x04, 0x40, 0xe3, 0x53, 0x28, 0xc4, 0x19, 0xc3, 0x8b, 0x7c, 0x36, 0x21, 0x39, 0xb3, 0x61,
	0x00, 0xcc, 0x6c, 0x16, 0x81, 0x6d, 0x91, 0xc2, 0xc4, 0x26, 0x53, 0x9b, 0x38, 0xb5, 0x30, 0xb8,
	0x1a, 0x0e, 0xe8, 0xa5, 0x66, 0x53, 0x26, 0xd7, 0xc7, 0x88, 0xbf, 0x02, 0xa3, 0xd2, 0x3e, 0x68,
	0x18, 0x13, 0x61, 0xdd, 0x8c, 0x39, 0xb8, 0x98, 0x18, 0x8f, 0xb6, 0x0d, 0x13, 0xf4, 0x15, 0xd2,
	0x1a, 0x82, 0x8d, 0xd4, 0xb5, 0x34, 0x07, 0xe3, 0x6e, 0x5b, 0x4a, 0x31, 0xc7, 0xdc, 0xf6, 0xae,
	0xdd, 0x64, 0xe3, 0x26, 0x06, 0xa0, 0x6d, 0xe3, 0x8f, 0x35, 0x98, 0x0b, 0x25, 0xeb, 0x37, 0xbf,
	0xed, 0xd8, 0x72, 0xf5, 0x72, 0x36, 0x5a, 0xbd, 0x7c, 0x17, 0x66, 0x6d, 0x6a, 0x1e, 0xb8, 0x9d,
	0x06, 0x31, 0xb9, 0xef, 0x15, 0x29, 0xc0, 0xb4, 0x4d, 0x57, 0x19, 0x74, 0x17, 0x81, 0x12, 0xfb,
	0x63, 0x32, 0xfb, 0xdf, 0x82, 0x79, 0x15, 0x9b, 0xb4, 0xad, 0x2f, 0xc3, 0x54, 0xc3, 0x75, 0x0e,
	0xec, 0x86, 0xd7, 0xaf, 0x18, 0xcc, 0xef, 0xc6, 0x97, 0x84, 0xe8, 0x84, 0x46, 0xf0, 0xcb, 0x30,
	0x25, 0x37, 0xf6, 0x9a, 0x7b, 0x32, 0xb3, 0x97, 0xa4, 0x31, 0xd6, 0x6f, 0x65, 0x8f, 0x2b, 0x56,
	0xb6, 0xf1, 0x07, 0x1a, 0x5c, 0xf4, 0xbd, 0xfa, 0x80, 0x1a, 0x18, 0xd4, 0x69, 0x08, 0x6e, 0xb3,
	0x21, 0xb7, 0xc3, 0xba, 0x50, 0x0b, 0x2e, 0x25, 0x79, 0x4b, 0xf5, 0x9f, 0xcf, 0x22, 0xfe, 0xb3,
	0xaf, 0x12, 0xb8, 0xd7, 0xfc, 0x09, 0xaf, 0xa3, 0x8d, 0x8c, 0xc1, 0xcb, 0xc6, 0xfb, 0xe6, 0x3c,
	0x48, 0x53, 0xae, 0xa8, 0xc9, 0x23, 0xc4, 0x5f, 0xf4, 0x43, 0xd6, 0xcb, 0xfb, 0xb2, 0x19, 0x4b,
	0x91, 0xcd, 0x78, 0x4c, 0x36, 0x2d, 0xdc, 0xb0, 0xa8, 0xf8, 0x4e, 0x15, 0xd0, 0x17, 0x23, 0x02,
	0xba, 0x93, 0x22, 0x20, 0x46, 0xc5, 0xa6, 0x9e, 0xdd, 0xa0, 0x92, 0x98, 0xfe, 0x28, 0x03, 0x7a,
	0xb2, 0x51, 0x79, 0x17, 0xd4, 0xa7, 0x82, 0x76, 0x09, 0x2e, 0x10, 0xb6, 0x67, 0x09, 0xea, 0xb6,
	0xa5, 0xaa, 0x42, 0x6c, 0x10, 0xbb, 0x8f, 0xb2, 0xe3, 0xb1, 0x64, 0x8f, 0xb9, 0x64, 0xcf, 0x6e,
	0x1c, 0x13, 0x4f, 0xba, 0xd6, 0x99, 0x3a, 0xb2, 0x68, 0x1d, 0x81, 0xe2, 0xbe, 0xa4, 0x4b, 0x49,
	0x53, 0x46, 0x13, 0xf7, 0x3a, 0x0c, 0x1c, 0xe2, 0xf9, 0x77, 0x44, 0x38, 0x6d, 0xa9, 0x66, 0x96,
	0x01, 0x77, 0xec, 0x43, 0x47, 0x14, 0xfb, 0xc8, 0xf5, 0xbd, 0xb9, 0x44, 0x7d, 0xef, 0x15, 0xc8,
	0x77, 0x48, 0xc3, 0xed, 0xa0, 0x4d, 0xf0, 0xeb, 0x9b, 0x1c, 0x07, 0x54, 0x9b, 0xc6, 0x3f, 0x6a,
	0xf0, 0x9e, 0x52, 0x29, 0xdc, 0x13, 0x7f, 0x3e, 0x93, 0x8a, 0x8c, 0x9c, 0x8d, 0x8e, 0x1c, 0x8d,
	0x40, 0xa3, 0xb1, 0x08, 0x24, 0x16, 0xe2, 0x58, 0x72, 0x21, 0x8e, 0xa7, 0x18, 0xdb, 0x44, 0xcc,
	0xd8, 0xde, 0x84, 0x65, 0x7e, 0xca, 0x79, 0xa5, 0x9a, 0xdc, 0xd7, 0x22, 0x26, 0xf7, 0x41, 0x5a,
	0x91, 0x9c, 0x8a, 0x28, 0x37, 0x3c, 0x17, 0x16, 0xd2, 0x30, 0x94, 0xd6, 0xe7, 0x0b, 0x42, 0x72,
	0x4b, 0x28, 0x88, 0x14, 0x8f, 0x54, 0x80, 0xec, 0xa9, 0xd5, 0x12, 0xae, 0x91, 0xfd, 0x34, 0x4a,
	0x30, 0x23, 0xa5, 0x6d, 0xea, 0x7d, 0x79, 0x9f, 0x0d, 0xcf, 0x0f, 0x32, 0x30, 0x1b, 0xa1, 0xc1,
	0x8f, 0x04, 0x3e, 0x5f, 0x0a, 0x16, 0x4d, 0x77, 0xb2, 0xf1, 0x74, 0xa7, 0x12, 0xcf, 0xd0, 0x46,
	0x53, 0xcb, 0xea, 0x7d, 0xbe, 0x84, 0x9c, 0x23, 0xf9, 0xd9, 0x5a, 0x22, 0x3f, 0x1b, 0x1b, 0x94,
	0x4e, 0x34, 0x3b, 0x5b, 0x84, 0xdc, 0x2f, 0x75, 0x4f, 0xda, 0x66, 0xb7, 0xe3, 0x4b, 0x77, 0x82,
	0x3d, 0xef, 0x76, 0x5a, 0xc6, 0xcf, 0x34, 0x98, 0x89, 0xf6, 0x8d, 0x6a, 0x4d, 0x8b, 0x69, 0xed,
	0x16, 0xa0, 0x28, 0xcc, 0x76, 0xc7, 0x3d, 0xec, 0x10, 0x4a, 0x85, 0x7c, 0xa6, 0x18, 0x70, 0x5b,
	0xc0, 0x98, 0x2d, 0x74, 0xfc, 0x57, 0xae, 0x32, 0x35, 0xfc, 0xcd, 0x78, 0xc0, 0x8e, 0xa7, 0x41,
	0x59, 0x15, 0xea, 0x60, 0xcf, 0x6a, 0x45, 0x92, 0xb2, 0xb1, 0x48, 0x52, 0x16, 0x58, 0xd5, 0xb8,
	0xc2, 0xaa, 0xa4, 0x37, 0x89, 0x83, 0xe5, 0x65, 0xdc, 0x87, 0x39, 0x49, 0xdb, 0x2f, 0x6c, 0xea,
	0xb9, 0xfc, 0xfc, 0x35, 0x79, 0x6c, 0xb4, 0x17, 0xd9, 0xb9, 0x04, 0xa8, 0x03, 0xbd, 0xdc, 0x14,
	0xeb, 0x15, 0x59, 0x25, 0x7f, 0xa5, 0xc1, 0x9c, 0xb2, 0x5d, 0xb9, 0x46, 0x12, 0xb6, 0x92, 0x79,
	0x47, 0xb6, 0x92, 0x7d, 0x2b, 0x5b, 0x31, 0x9e, 0xa1, 0x77, 0x09, 0x50, 0xc2, 0x54, 0xab, 0x97,
	0x28, 0x0f, 0xb1, 0x96, 0xb7, 0x47, 0x27, 0xda, 0xd6, 0xcb, 0x11, 0xa1, 0x3e, 0x56, 0x6f, 0x68,
	0xd3, 0x49, 0x70, 0xd9, 0xfe, 0x2b, 0x7f, 0xa1, 0x38, 0x15, 0x4d, 0xe1, 0x1f, 0x0c, 0xc0, 0x52,
	0x7a, 0x33, 0xf6, 0x7e, 0xe0, 0x24, 0x03, 0xae, 0x85, 0x2f, 0xdb, 0xa5, 0x15, 0x36, 0x4b, 0xbe,
	0x62, 0x34, 0x25, 0xbd, 0x96, 0xf3, 0x53, 0x3f, 0x68, 0x1d, 0x8a, 0x03, 0xe1, 0x71, 0x51, 0xde,
	0x8b, 0x20, 0x0c, 0x00, 0x0c, 0xa1, 0x43, 0xe2, 0xa7, 0x10, 0x1c, 0x54, 0xb7, 0x4f, 0x88, 0x51,
	0x87, 0x19, 0x29, 0x41, 0x4f, 0x7d, 0x09, 0x35, 0xed, 0x7d, 0xc7, 0x94, 0xb4, 0xff, 0x02, 0xcc,
	0x46, 0xa8, 0xd2, 0xb6, 0xf1, 0x2f, 0x1a, 0x5c, 0x08, 0xf6, 0x01, 0xc1, 0x85, 0xca, 0x50, 0x83,
	0x49, 0xd2, 0xc9, 0x46, 0xa4, 0xf3, 0x09, 0x4c, 0x05, 0x66, 0xed, 0x2f, 0xf9, 0xc9, 0xa7, 0xef,
	0xf5, 0xb0, 0xc6, 0x3d, 0xab, 0x55, 0x03, 0x61, 0xd2, 0xcc, 0x2b, 0x2c, 0xc3, 0x74, 0x68, 0xd1,
	0xa7, 0xe2, 0x8d, 0xdd, 0xfe, 0x24, 0x26, 0x7d, 0x6b, 0xde, 0xb3, 0x5a, 0xc6, 0x3f, 0x69, 0x30,
	0x29, 0x35, 0x46, 0xaf, 0xdc, 0xb4, 0xe8, 0x95, 0x1b, 0x4b, 0x70, 0x78, 0xa3, 0xdb, 0x26, 0x8e,
	0xf4, 0x42, 0xc8, 0x14, 0x42, 0xb7, 0xda, 0x04, 0x93, 0x92, 0x6b, 0x00, 0x52, 0x6e, 0x23, 0x5e,
	0xd0, 0xf6, 0x82, 0xbc, 0x26, 0xbc, 0xac, 0x1b, 0x8d, 0x5c, 0xd6, 0x05, 0xc4, 0xfd, 0x52, 0x0a,
	0x3f, 0x7b, 0x3a, 0xe5, 0xaf, 0x59, 0x63, 0xf1, 0x04, 0xbe, 0xcb, 0x63, 0x3b, 0x36, 0x3d, 0x32,
	0x03, 0xe1, 0x49, 0xaf, 0x10, 0xf2, 0xa6, 0x15, 0x2e, 0xa2, 0xb2, 0xe3, 0x19, 0xc7, 0x89, 0xb3,
	0x97, 0xe5, 0xf3, 0xbe, 0x2f, 0x05, 0xa7, 0xee, 0xd9, 0xae, 0x01, 0xbc, 0xee, 0x92, 0x8e, 0xf8,
	0xc0, 0x80, 0x98, 0x19, 0x42, 0xd0, 0xb3, 0x7e, 0x23, 0x71, 0x84, 0xc2, 0x07, 0xa3, 0xed, 0x60,
	0x27, 0xac, 0xbd, 0xdd, 0x4e, 0xf8, 0x08, 0x7d, 0xf6, 0x9e, 0x24, 0x88, 0x5d, 0xbb, 0x29, 0xee,
	0x56, 0x7a, 0x1d, 0x49, 0x0d, 0xff, 0x26, 0xf6, 0x03, 0x14, 0x58, 0x62, 0x24, 0xda, 0x66, 0xae,
	0x19, 0xdf, 0x91, 0xe6, 0x2f, 0x18, 0xe0, 0x6f, 0x63, 0x0d, 0x77, 0x63, 0xcb, 0xb6, 0xd3, 0xe4,
	0x7e, 0x42, 0xdc, 0xf8, 0x5c, 0x06, 0x2e, 0x4b, 0xd3, 0xf2, 0xb3, 0x07, 0x7c, 0x2c, 0x85, 0x0d,
	0xfb, 0xbe, 0x64, 0xf1, 0x71, 0xd9, 0x78, 0x8c, 0x5b, 0xa7, 0x18, 0xa1, 0xe0, 0x6a, 0x62, 0xdf,
	0x76, 0x9a, 0xe1, 0xd5, 0x04, 0xc3, 0x32, 0xea, 0x30, 0x81, 0x98, 0xa5, 0xfd, 0xe1, 0x47, 0x93,
	0xa9, 0x66, 0x23, 0x54, 0x3f, 0x85, 0x85, 0x65, 0xcb, 0x6b, 0x1c, 0xa9, 0x26, 0xf5, 0x28, 0xe2,
	0x9d, 0x8b, 0x69, 0xb9, 0x61, 0x69, 0x5f, 0x38, 0xe2, 0xcf, 0x60, 0x31, 0x85, 0x16, 0x6d, 0x0f,
	0x4d, 0x6c, 0x13, 0xa6, 0x23, 0x44, 0x7a, 0x99, 0xaf, 0x01, 0xd3, 0x6c, 0x6a, 0x09, 0x77, 0xbe,
	0x1f, 0x10, 0x68, 0x1a, 0x8b, 0x68, 0xaa, 0x11, 0x92, 0xe2, 0x50, 0xd2, 0x78, 0x85, 0x3b, 0x4c,
	0x45, 0x13, 0x9a, 0x71, 0xe2, 0x55, 0xb2, 0x1b, 0x0a, 0xde, 0xa3, 0xf3, 0x0d, 0xee, 0xbd, 0x96,
	0x9e, 0xc3, 0x4c, 0xf4, 0xbb, 0x10, 0x3a, 0xc0, 0xf8, 0xf2, 0x2b, 0x73, 0xa5, 0xf4, 0xaa, 0x30,
	0xa2, 0x4f, 0xc2, 0xc4, 0xf2, 0x2b, 0xf3, 0x65, 0xa5, 0xf2, 0x59, 0x41, 0xd3, 0xa7, 0x20, 0xb7,
	0xfc, 0xca, 0xdc, 0xd8, 0xda, 0xac, 0xbf, 0x28, 0x64, 0x96, 0x36, 0x61, 0xb2, 0x24, 0x7d, 0xc7,
	0xe3, 0x22, 0xcc, 0x8a, 0x47, 0xb3, 0xea, 0xa0, 0x7b, 0x28, 0x8c, 0xc8, 0xc0, 0xaf, 0xf3, 0x57,
	0x9f, 0x0a, 0x9a, 0x3e, 0x07, 0x17, 0x7c, 0xe0, 0xb6, 0xeb, 0x11, 0x87, 0xad, 0xab, 0x42, 0x66,
	0xe9, 0x07, 0x1a, 0xcc, 0xd5, 0x5f, 0x6d, 0x57, 0xcc, 0x8d, 0xdd, 0xf5, 0x7a, 0x75, 0x7b, 0xbd,
	0xf4, 0xaa, 0x52, 0x33, 0xab, 0x9b, 0xab, 0x5b, 0xfa, 0x4d, 0xb8, 0xa6, 0x6c, 0x30, 0xab, 0x9b,
	0x7b, 0xa5, 0xea, 0xfa, 0x4a, 0x61, 0x44, 0xbf, 0x0e, 0x57, 0xd4, 0x28, 0xf5, 0x2d, 0x36, 0x11,
	0x4d, 0x7f, 0x0f, 0x8a, 0x6a, 0x04, 0x9c, 0x5b, 0x26, 0x9d, 0x00, 0x9f, 0x6e, 0x76, 0xe9, 0x57,
	0x35, 0xc8, 0xef, 0x04, 0x17, 0xa4, 0x06, 0xbc, 0xb7, 0x53, 0xae, 0x6c, 0x56, 0x4c, 0xec, 0xc4,
	0xf8, 0x58, 0xaf, 0xae, 0x98, 0xbb, 0x9b, 0x3b, 0xdb, 0x95, 0x72, 0x75, 0xb5, 0x5a, 0x61, 0x3c,
	0x5d, 0x85, 0x05, 0x09, 0x67, 0x77, 0xa7, 0x52, 0x2b, 0x97, 0x6a, 0x2b, 0xe6, 0x7a, 0x75, 0xaf,
	0x52, 0xd0, 0xf4, 0x2b, 0x70, 0x59, 0xd5, 0xba, 0xbd, 0x56, 0x2e, 0x64, 0xf4, 0x79, 0xd0, 0x65,
	0xf2, 0x1b, 0xe6, 0x76, 0x69, 0xad, 0x52, 0xc8, 0x2e, 0xfd, 0xbd, 0x16, 0xde, 0x1c, 0xef, 0xfa,
	0xf7, 0x9e, 0x4b, 0x70, 0xb7, 0xba, 0x59, 0xaf, 0xd4, 0x4a, 0xe5, 0x3a, 0xd2, 0xe9, 0xc5, 0xd3,
	0x35, 0x58, 0x54, 0xe0, 0x96, 0xb7, 0x36, 0x36, 0xb6, 0x36, 0x0b, 0x1a, 0x9b, 0x96, 0xa2, 0xb9,
	0xf2, 0x8d, 0xed, 0x6a, 0xad, 0x62, 0xae, 0x96, 0x36, 0x77, 0x0a, 0x19, 0xfd, 0x0e, 0xdc, 0x54,
	0xe0, 0xb0, 0x59, 0x99, 0x3b, 0x95, 0xbd, 0xca, 0xa6, 0xb9, 0x59, 0x79, 0x59, 0xc8, 0xea, 0xb7,
	0xe1, 0x86, 0x02, 0x6d, 0x7b, 0xad, 0x2c, 0x61, 0x8d, 0x2e, 0xfd, 0x4c, 0x03, 0xfd, 0x45, 0x69,
	0x7d, 0xdd, 0xac, 0x97, 0x76, 0x3e, 0x63, 0x36, 0x87, 0x68, 0x4c, 0x5b, 0x49, 0xa8, 0x3f, 0xa5,
	0xc2, 0x88, 0x7e, 0x03, 0xae, 0x2a, 0xda, 0x5f, 0x6c, 0xad, 0xaf, 0x98, 0xf5, 0xea, 0x06, 0x13,
	0xef, 0x1d, 0xb8, 0xa9, 0xc0, 0xe0, 0x22, 0xd9, 0xda, 0xae, 0x6c, 0x9a, 0xe5, 0xcd, 0x7a, 0x21,
	0xa3, 0xdf, 0x87, 0x3b, 0x0a, 0xb4, 0xb5, 0xad, 0x15, 0x73, 0x75, 0xfd, 0x95, 0x59, 0xaf, 0x96,
	0x3f, 0xab, 0xd4, 0x11, 0x35, 0xab, 0xdf, 0x82, 0xeb, 0x0a, 0xd4, 0xfa, 0x72, 0xa5, 0xb4, 0x69,
	0x56, 0x37, 0xcb, 0x5b, 0x1b, 0x95, 0xc2, 0xe8, 0xd2, 0xf7, 0x32, 0x70, 0x31, 0xc4, 0x62, 0xb6,
	0xc5, 0x27, 0x74, 0x1d, 0xae, 0x28, 0xc0, 0xd2, 0x8c, 0x96, 0xe0, 0xae, 0x0a, 0x81, 0x33, 0x8c,
	0x13, 0x63, 0x43, 0x32, 0x4e, 0x34, 0xfd, 0x03, 0x78, 0x5f, 0x85, 0xbb, 0x5a, 0xdd, 0xac, 0xee,
	0xbc, 0xe0, 0xac, 0xb1, 0x16, 0x3e, 0xc3, 0xbb, 0x60, 0xa4, 0x13, 0x0e, 0x24, 0x91, 0x4d, 0x63,
	0x40, 0x21, 0x8a, 0x51, 0xa6, 0x5b, 0x15, 0x6e, 0x44, 0x16, 0x63, 0x4b, 0xbf, 0x96, 0x81, 0xb9,
	0x10, 0xad, 0xfc, 0xa2, 0xb4, 0xb9, 0xc6, 0x0d, 0x9a, 0x2d, 0x68, 0x65, 0x83, 0x24, 0x8f, 0x6b,
	0xb0, 0xa8, 0x46, 0x29, 0xad, 0xac, 0x14, 0xb4, 0xa8, 0x01, 0xc8, 0xcd, 0xbb, 0xdb, 0x2b, 0xa5,
	0x7a, 0xa5, 0x90, 0xd1, 0x9f, 0xc0, 0x03, 0x35, 0x46, 0xb9, 0xb4, 0x59, 0xae, 0xac, 0x9b, 0xe5,
	0xad, 0xcd, 0x3a, 0x1a, 0xe7, 0x4a, 0x65, 0xbd, 0x52, 0xaf, 0x14, 0xb2, 0x51, 0x49, 0xc9, 0x3d,
	0xd6, 0x76, 0xab, 0x4c, 0xfe, 0x1c, 0x6f, 0x54, 0x7f, 0x08, 0xf7, 0xd5, 0x78, 0x08, 0x65, 0x80,
	0xcd, 0xca, 0xba, 0x8f, 0x3e, 0xb6, 0xf4, 0x87, 0x9a, 0x3c, 0xdb, 0xed, 0x5a, 0x65, 0xa7, 0xb2,
	0x59, 0x37, 0x77, 0xb6, 0x76, 0x6b, 0x65, 0x21, 0x8e, 0x88, 0x11, 0x2a, 0x10, 0x24, 0xb1, 0xdc,
	0x83, 0xdb, 0xbd, 0x51, 0xb9, 0x9e, 0x0a, 0x9a, 0xfe, 0x3e, 0xdc, 0xea, 0x83, 0xc9, 0xb4, 0x55,
	0xc8, 0x2c, 0x95, 0xe3, 0xc7, 0x72, 0xe8, 0x54, 0xe6, 0x78, 0x92, 0x1d, 0x42, 0x57, 0xac, 0xf3,
	0xc2, 0x08, 0x73, 0x4c, 0x51, 0xf0, 0x4b, 0x42, 0x8e, 0x0b, 0xda, 0xd3, 0x1f, 0xbe, 0x0f, 0xb3,
	0x3b, 0xc1, 0xab, 0xbf, 0xfc, 0x7b, 0x27, 0xdf, 0x93, 0x3e, 0xa3, 0xa4, 0xfa, 0x64, 0x98, 0xfe,
	0xb4, 0x47, 0x0d, 0x62, 0xca, 0xa7, 0xe1, 0x8a, 0xcf, 0x86, 0xee, 0x43, 0xdb, 0xc6, 0x88, 0xfe,
	0x6d, 0x8c, 0xad, 0xaa, 0xaf, 0x41, 0xe9, 0x0f, 0x7b, 0x50, 0x4c, 0x7e, 0xa8, 0xac, 0xf8, 0x68,
	0x18, 0x74, 0x1c, 0xfb, 0xbb, 0x5a, 0x58, 0xc0, 0x9b, 0xf8, 0xc8, 0x8f, 0xfe, 0xb8, 0x2f, 0xbd,
	0x98, 0x04, 0x9e, 0x0c, 0xd7, 0x01, 0x59, 0x70, 0x30, 0x27, 0x8c, 0x7f, 0xcb, 0x47, 0xbf, 0xaf,
	0x26, 0xa5, 0xf8, 0xa2, 0x50, 0x71, 0x69, 0x50, 0x54, 0x1c, 0xef, 0x47, 0x1a, 0xdc, 0x19, 0xe8,
	0x83, 0x3b, 0xfa, 0xc7, 0x7d, 0x67, 0x93, 0xfe, 0x11, 0xa1, 0xe2, 0x97, 0xdf, 0xbe, 0x33, 0xb2,
	0xe9, 0x61, 0x0a, 0x9f, 0xfc, 0xc2, 0x84, 0xfe, 0x81, 0x9a, 0xb0, 0xf2, 0x53, 0x19, 0xc5, 0x07,
	0x83, 0x23, 0x4b, 0xca, 0x88, 0x7f, 0x16, 0x20, 0x4d, 0x19, 0x8a, 0x4f, 0x1e, 0xa4, 0x29, 0x43,
	0xf5, 0xa5, 0x01, 0x63, 0xc4, 0x5f, 0x87, 0xa9, 0xef, 0x2c, 0xa7, 0xad, 0xc3, 0x5e, 0xaf, 0x64,
	0xa7, 0xad, 0xc3, 0x9e, 0x2f, 0x46, 0x87, 0xbc, 0xa4, 0x6a, 0xa8, 0xa7, 0x4f, 0x48, 0xf9, 0x1e,
	0x51, 0x4f, 0x9f, 0x90, 0xf6, 0x29, 0x20, 0x63, 0x44, 0x3f, 0xc3, 0x6d, 0x95, 0xe2, 0x9d, 0x64,
	0xfd, 0x41, 0xdf, 0xe2, 0x68, 0xe9, 0x65, 0xe9, 0xe2, 0xc3, 0x21, 0xb0, 0xd5, 0x03, 0xfb, 0xc5,
	0xf8, 0x03, 0x0c, 0x2c, 0xd5, 0xfe, 0x0f, 0x30, 0xb0, 0x5c, 0xe5, 0x6f, 0x8c, 0xe8, 0xbf, 0x23,
	0x7d, 0xdc, 0x29, 0xa5, 0xc8, 0x5b, 0xff, 0xe8, 0x6d, 0x0a, 0xc3, 0x5f, 0x17, 0xbf, 0xf0, 0x76,
	0xf5, 0xe4, 0xc6, 0x88, 0xfe, 0x7d, 0x2d, 0xfc, 0x70, 0x53, 0xea, 0x2b, 0xb6, 0x03, 0x93, 0x8f,
	0xd6, 0x79, 0x17, 0x9f, 0xbf, 0x55, 0x3f, 0x5f, 0x4b, 0xea, 0xfa, 0x52, 0xa5, 0x96, 0x52, 0x4b,
	0x68, 0x95, 0x5a, 0x4a, 0x2f, 0x5c, 0x8d, 0x38, 0xeb, 0x48, 0x01, 0x6a, 0x0f, 0x67, 0x1d, 0xab,
	0xee, 0xec, 0xe5, 0xac, 0xe3, 0x25, 0x95, 0x81, 0x39, 0x2a, 0x2a, 0x01, 0xd3, 0xcc, 0x51, 0x5d,
	0x7c, 0x98, 0x66, 0x8e, 0x69, 0x25, 0x86, 0x38, 0xf0, 0xce, 0xe0, 0x03, 0xef, 0x0c, 0x35, 0xf0,
	0x4e, 0xaf, 0x81, 0x9d, 0xf0, 0xc2, 0x5a, 0x2a, 0x54, 0x4c, 0x93, 0xb0, 0xa2, 0x0a, 0x32, 0x4d,
	0xc2, 0xaa, 0xda, 0x47, 0x63, 0x44, 0x3f, 0xc6, 0x22, 0x8d, 0x78, 0xe9, 0x92, 0xea, 0x84, 0x5e,
	0x59, 0xe7, 0x58, 0xbc, 0x3f, 0x20, 0xa6, 0x3f, 0x39, 0x45, 0xad, 0x9f, 0x72, 0x72, 0xea, 0xca,
	0x43, 0xe5, 0xe4, 0x52, 0xca, 0x07, 0xf9, 0xe4, 0x92, 0xb5, 0x79, 0xca, 0xc9, 0x29, 0xab, 0x02,
	0x95, 0x93, 0x4b, 0x29, 0xf6, 0xf3, 0xf3, 0x38, 0x65, 0x25, 0xd8, 0xc3, 0x61, 0x8a, 0xcd, 0x52,
	0xf3, 0xb8, 0xb4, 0xda, 0xb4, 0xc0, 0x6a, 0xe2, 0xd5, 0x58, 0x69, 0x56, 0xa3, 0xa8, 0x2d, 0x4b,
	0xb3, 0x1a, 0x55, 0x81, 0x97, 0x72, 0xbc, 0xe5, 0xf3, 0x6a, 0x73, 0x90, 0xf1, 0xc4, 0x79, 0xea,
	0x20, 0xe3, 0xf9, 0xa7, 0xa1, 0xc6, 0x88, 0xbe, 0x0f, 0xb3, 0xb1, 0xd2, 0x1e, 0xfd, 0x8e, 0xda,
	0xf0, 0x62, 0x75, 0x3e, 0xc5, 0xbb, 0x83, 0xa0, 0xf9, 0x63, 0xc4, 0xca, 0x78, 0x94, 0x63, 0x24,
	0x6b, 0x89, 0x94, 0x63, 0xa8, 0x2a, 0x82, 0xb8, 0x41, 0x26, 0x6a, 0x6d, 0xd4, 0x06, 0xa9, 0xaa,
	0x1c, 0x52, 0x1b, 0xa4, 0xb2, 0x78, 0xc7, 0x18, 0xd1, 0xf7, 0x60, 0x52, 0xe2, 0x42, 0xbf, 0xd9,
	0x9b, 0x4b, 0x46, 0xde, 0xe8, 0x87, 0x82, 0x74, 0x09, 0x14, 0xe2, 0xd7, 0xe5, 0xfa, 0xdd, 0x1e,
	0x26, 0x2b, 0x8f, 0xf0, 0xfe, 0x40, 0x78, 0x52, 0x06, 0x9c, 0xbc, 0x1e, 0x4f, 0xcb, 0x80, 0x95,
	0x45, 0x2e, 0x69, 0x19, 0xb0, 0xba, 0xb2, 0xc4, 0x18, 0xd1, 0x7f, 0x5d, 0xaa, 0x22, 0x55, 0xdd,
	0xca, 0x7f, 0x38, 0x28, 0xbd, 0xa0, 0x28, 0xa2, 0xf8, 0x74, 0xd8, 0x2e, 0x72, 0x3a, 0x9a, 0x7e,
	0x31, 0xf7, 0x74, 0xd8, 0x0b, 0xbf, 0xf4, 0x74, 0xb4, 0xe7, 0x3d, 0xa3, 0x31, 0xa2, 0x9b, 0x78,
	0xdc, 0x9e, 0xbc, 0xd9, 0xba, 0xd9, 0x9b, 0x5c, 0x9a, 0x49, 0xc5, 0xaa, 0x07, 0xb8, 0xa9, 0x4a,
	0xc0, 0x77, 0x47, 0xf7, 0x18, 0xf4, 0xe4, 0x8d, 0xb4, 0x72, 0xbd, 0x29, 0xef, 0xb8, 0x8b, 0xf7,
	0x07, 0xc4, 0xc4, 0xc1, 0x3e, 0x45, 0xa7, 0xc8, 0x2b, 0x72, 0xb6, 0x3a, 0x4d, 0xd2, 0x29, 0xe3,
	0xeb, 0x83, 0x8b, 0x8f, 0x6a, 0xfe, 0xa7, 0xe6, 0xf7, 0x9e, 0xe2, 0x87, 0x8e, 0x6b, 0x96, 0xc3,
	0xd3, 0xf3, 0xf9, 0x48, 0x13, 0xa2, 0x0b, 0x5a, 0xeb, 0xc8, 0xb8, 0x44, 0x8b, 0x97, 0x1d, 0xa4,
	0x93, 0x8a, 0x36, 0x61, 0x97, 0x6a, 0xd3, 0x37, 0xea, 0xcf, 0x50, 0x7f, 0xdb, 0xfc, 0x9d, 0xe4,
	0xcf, 0xcb, 0xda, 0x06, 0xdf, 0x23, 0x4a, 0xc4, 0x3e, 0x17, 0x6f, 0xab, 0x30, 0x55, 0x23, 0xed,
	0x96, 0xd5, 0x20, 0xd8, 0xa0, 0x5f, 0x8d, 0x20, 0xcb, 0x4d, 0x49, 0xb6, 0x2a, 0x27, 0x6d, 0xef,
	0x3c, 0xa2, 0xea, 0xd8, 0x4d, 0x54, 0x9a, 0xaa, 0x93, 0x57, 0x63, 0x69, 0xaa, 0x56, 0x5c, 0x6d,
	0x05, 0x2e, 0x30, 0x7a, 0xc5, 0x92, 0xe2, 0x02, 0xe3, 0x17, 0x43, 0x69, 0x2e, 0x30, 0x71, 0xe9,
	0xc3, 0x5d, 0xa0, 0xf2, 0x4e, 0x48, 0xe9, 0x02, 0xd3, 0x6e, 0xa2, 0x94, 0x2e, 0x30, 0xf5, 0xaa,
	0xc9, 0x18, 0xd1, 0x5f, 0x27, 0x2f, 0xd7, 0x50, 0xc3, 0x4b, 0x03, 0x30, 0xee, 0xa7, 0x13, 0x1f,
	0x0c, 0x8c, 0xcb, 0x86, 0x5c, 0xfe, 0xe8, 0x9b, 0xcf, 0x0e, 0xdd, 0x96, 0xe5, 0x1c, 0x3e, 0xfa,
	0xe8, 0xa9, 0xe7, 0x3d, 0x6a, 0xb8, 0x27, 0x8f, 0xf1, 0x5f, 0x32, 0x34, 0xdc, 0xd6, 0x63, 0x4a,
	0x3a, 0xa7, 0x76, 0x83, 0x28, 0xfe, 0x67, 0xc4, 0xfe, 0x38, 0x22, 0x3d, 0xfb, 0x9f, 0x00, 0x00,
	0x00, 0xff, 0xff, 0x66, 0xce, 0x95, 0x30, 0x70, 0x62, 0x00, 0x00,
}
