// Code generated by protoc-gen-gogo.
// source: src/asynctexthandler/asynctexthandler.proto
// DO NOT EDIT!

/*
Package asynctexthandler is a generated protocol buffer package.

namespace

It is generated from these files:

	src/asynctexthandler/asynctexthandler.proto

It has these top-level messages:

	UserInfoContext
	GuildModifyGroupNameContext
	TempGroupModifyNameContext
	TGroupModifyDescContext
	TGroupModifyNameContext
	GroupPublishBulletinHandlerContext
	ChannelModifyNameContext
	ChannelModifyDescContext
	ChannelModifyTopicDetailContext
	ChannelModifyWelcomeMsgContext
	TextCheckContext
	GuildInfoModifyContext
	GuildCircleTopicContext
	GuildCircleCommentContext
	ModifyNicknameContext
	ModifyGameNickContext
	GuildOrGroupEnterVerifyContext
*/
package asynctexthandler

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import io1 "io"
import fmt2 "fmt"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type UserInfoContext struct {
	Category string `protobuf:"bytes,1,opt,name=category,proto3" json:"category,omitempty"`
}

func (m *UserInfoContext) Reset()                    { *m = UserInfoContext{} }
func (m *UserInfoContext) String() string            { return proto.CompactTextString(m) }
func (*UserInfoContext) ProtoMessage()               {}
func (*UserInfoContext) Descriptor() ([]byte, []int) { return fileDescriptorAsynctexthandler, []int{0} }

func (m *UserInfoContext) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

// AntiSpamDataType::GUILD_GROUP_NAME (see AntiSpamDataType)
type GuildModifyGroupNameContext struct {
	GuildId uint32 `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GroupId uint32 `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
}

func (m *GuildModifyGroupNameContext) Reset()         { *m = GuildModifyGroupNameContext{} }
func (m *GuildModifyGroupNameContext) String() string { return proto.CompactTextString(m) }
func (*GuildModifyGroupNameContext) ProtoMessage()    {}
func (*GuildModifyGroupNameContext) Descriptor() ([]byte, []int) {
	return fileDescriptorAsynctexthandler, []int{1}
}

func (m *GuildModifyGroupNameContext) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildModifyGroupNameContext) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

// AntiSpamDataType::TEMP_GROUP_NAME (see AntiSpamDataType)
type TempGroupModifyNameContext struct {
	GroupId uint32 `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
}

func (m *TempGroupModifyNameContext) Reset()         { *m = TempGroupModifyNameContext{} }
func (m *TempGroupModifyNameContext) String() string { return proto.CompactTextString(m) }
func (*TempGroupModifyNameContext) ProtoMessage()    {}
func (*TempGroupModifyNameContext) Descriptor() ([]byte, []int) {
	return fileDescriptorAsynctexthandler, []int{2}
}

func (m *TempGroupModifyNameContext) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

// AntiSpamDataType::TGROUP_DESC (see AntiSpamDataType)
type TGroupModifyDescContext struct {
	GroupId   uint32 `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	NewGameId uint32 `protobuf:"varint,2,opt,name=new_game_id,json=newGameId,proto3" json:"new_game_id,omitempty"`
	CityCode  string `protobuf:"bytes,3,opt,name=city_code,json=cityCode,proto3" json:"city_code,omitempty"`
	CityName  string `protobuf:"bytes,4,opt,name=city_name,json=cityName,proto3" json:"city_name,omitempty"`
}

func (m *TGroupModifyDescContext) Reset()         { *m = TGroupModifyDescContext{} }
func (m *TGroupModifyDescContext) String() string { return proto.CompactTextString(m) }
func (*TGroupModifyDescContext) ProtoMessage()    {}
func (*TGroupModifyDescContext) Descriptor() ([]byte, []int) {
	return fileDescriptorAsynctexthandler, []int{3}
}

func (m *TGroupModifyDescContext) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *TGroupModifyDescContext) GetNewGameId() uint32 {
	if m != nil {
		return m.NewGameId
	}
	return 0
}

func (m *TGroupModifyDescContext) GetCityCode() string {
	if m != nil {
		return m.CityCode
	}
	return ""
}

func (m *TGroupModifyDescContext) GetCityName() string {
	if m != nil {
		return m.CityName
	}
	return ""
}

// AntiSpamDataType::TGROUP_NAME (see AntiSpamDataType)
type TGroupModifyNameContext struct {
	GroupId uint32 `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
}

func (m *TGroupModifyNameContext) Reset()         { *m = TGroupModifyNameContext{} }
func (m *TGroupModifyNameContext) String() string { return proto.CompactTextString(m) }
func (*TGroupModifyNameContext) ProtoMessage()    {}
func (*TGroupModifyNameContext) Descriptor() ([]byte, []int) {
	return fileDescriptorAsynctexthandler, []int{4}
}

func (m *TGroupModifyNameContext) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

// AntiSpamDataType::GUILD_OR_GROUP_POST_NOTICE (see AntiSpamDataType)
type GroupPublishBulletinHandlerContext struct {
	GuildId    uint32 `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GroupId    uint32 `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	GroupType  uint32 `protobuf:"varint,3,opt,name=group_type,json=groupType,proto3" json:"group_type,omitempty"`
	SplitIndex uint32 `protobuf:"varint,4,opt,name=split_index,json=splitIndex,proto3" json:"split_index,omitempty"`
}

func (m *GroupPublishBulletinHandlerContext) Reset()         { *m = GroupPublishBulletinHandlerContext{} }
func (m *GroupPublishBulletinHandlerContext) String() string { return proto.CompactTextString(m) }
func (*GroupPublishBulletinHandlerContext) ProtoMessage()    {}
func (*GroupPublishBulletinHandlerContext) Descriptor() ([]byte, []int) {
	return fileDescriptorAsynctexthandler, []int{5}
}

func (m *GroupPublishBulletinHandlerContext) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GroupPublishBulletinHandlerContext) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GroupPublishBulletinHandlerContext) GetGroupType() uint32 {
	if m != nil {
		return m.GroupType
	}
	return 0
}

func (m *GroupPublishBulletinHandlerContext) GetSplitIndex() uint32 {
	if m != nil {
		return m.SplitIndex
	}
	return 0
}

// AntiSpamDataType::CHANNEL_NAME (see AntiSpamDataType)
type ChannelModifyNameContext struct {
	ChannelId   uint32 `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	OperUid     uint32 `protobuf:"varint,2,opt,name=oper_uid,json=operUid,proto3" json:"oper_uid,omitempty"`
	Timestamp   uint32 `protobuf:"varint,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	ChannelType uint32 `protobuf:"varint,4,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	Appid       uint32 `protobuf:"varint,5,opt,name=appid,proto3" json:"appid,omitempty"`
	MarketId    uint32 `protobuf:"varint,6,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
}

func (m *ChannelModifyNameContext) Reset()         { *m = ChannelModifyNameContext{} }
func (m *ChannelModifyNameContext) String() string { return proto.CompactTextString(m) }
func (*ChannelModifyNameContext) ProtoMessage()    {}
func (*ChannelModifyNameContext) Descriptor() ([]byte, []int) {
	return fileDescriptorAsynctexthandler, []int{6}
}

func (m *ChannelModifyNameContext) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelModifyNameContext) GetOperUid() uint32 {
	if m != nil {
		return m.OperUid
	}
	return 0
}

func (m *ChannelModifyNameContext) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *ChannelModifyNameContext) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *ChannelModifyNameContext) GetAppid() uint32 {
	if m != nil {
		return m.Appid
	}
	return 0
}

func (m *ChannelModifyNameContext) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

// AntispamLogic::CHANNEL_TOPIC (see AntiSpamDataType)
type ChannelModifyDescContext struct {
	ChannelId    uint32 `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	OperUid      uint32 `protobuf:"varint,2,opt,name=oper_uid,json=operUid,proto3" json:"oper_uid,omitempty"`
	Timestamp    uint32 `protobuf:"varint,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	ChannelType  uint32 `protobuf:"varint,4,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	Desc         string `protobuf:"bytes,5,opt,name=desc,proto3" json:"desc,omitempty"`
	TopicDetail  string `protobuf:"bytes,6,opt,name=topic_detail,json=topicDetail,proto3" json:"topic_detail,omitempty"`
	OperPlatform string `protobuf:"bytes,7,opt,name=oper_platform,json=operPlatform,proto3" json:"oper_platform,omitempty"`
	OperApp      string `protobuf:"bytes,8,opt,name=oper_app,json=operApp,proto3" json:"oper_app,omitempty"`
}

func (m *ChannelModifyDescContext) Reset()         { *m = ChannelModifyDescContext{} }
func (m *ChannelModifyDescContext) String() string { return proto.CompactTextString(m) }
func (*ChannelModifyDescContext) ProtoMessage()    {}
func (*ChannelModifyDescContext) Descriptor() ([]byte, []int) {
	return fileDescriptorAsynctexthandler, []int{7}
}

func (m *ChannelModifyDescContext) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelModifyDescContext) GetOperUid() uint32 {
	if m != nil {
		return m.OperUid
	}
	return 0
}

func (m *ChannelModifyDescContext) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *ChannelModifyDescContext) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *ChannelModifyDescContext) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *ChannelModifyDescContext) GetTopicDetail() string {
	if m != nil {
		return m.TopicDetail
	}
	return ""
}

func (m *ChannelModifyDescContext) GetOperPlatform() string {
	if m != nil {
		return m.OperPlatform
	}
	return ""
}

func (m *ChannelModifyDescContext) GetOperApp() string {
	if m != nil {
		return m.OperApp
	}
	return ""
}

// AntispamLogic::CHANNEL_TOPIC_DETAIL (see AntiSpamDataType)
type ChannelModifyTopicDetailContext struct {
	ChannelId    uint32 `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	OperUid      uint32 `protobuf:"varint,2,opt,name=oper_uid,json=operUid,proto3" json:"oper_uid,omitempty"`
	Timestamp    uint32 `protobuf:"varint,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	ChannelType  uint32 `protobuf:"varint,4,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	Desc         string `protobuf:"bytes,5,opt,name=desc,proto3" json:"desc,omitempty"`
	TopicDetail  string `protobuf:"bytes,6,opt,name=topic_detail,json=topicDetail,proto3" json:"topic_detail,omitempty"`
	OperPlatform string `protobuf:"bytes,7,opt,name=oper_platform,json=operPlatform,proto3" json:"oper_platform,omitempty"`
	OperApp      string `protobuf:"bytes,8,opt,name=oper_app,json=operApp,proto3" json:"oper_app,omitempty"`
}

func (m *ChannelModifyTopicDetailContext) Reset()         { *m = ChannelModifyTopicDetailContext{} }
func (m *ChannelModifyTopicDetailContext) String() string { return proto.CompactTextString(m) }
func (*ChannelModifyTopicDetailContext) ProtoMessage()    {}
func (*ChannelModifyTopicDetailContext) Descriptor() ([]byte, []int) {
	return fileDescriptorAsynctexthandler, []int{8}
}

func (m *ChannelModifyTopicDetailContext) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelModifyTopicDetailContext) GetOperUid() uint32 {
	if m != nil {
		return m.OperUid
	}
	return 0
}

func (m *ChannelModifyTopicDetailContext) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *ChannelModifyTopicDetailContext) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *ChannelModifyTopicDetailContext) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *ChannelModifyTopicDetailContext) GetTopicDetail() string {
	if m != nil {
		return m.TopicDetail
	}
	return ""
}

func (m *ChannelModifyTopicDetailContext) GetOperPlatform() string {
	if m != nil {
		return m.OperPlatform
	}
	return ""
}

func (m *ChannelModifyTopicDetailContext) GetOperApp() string {
	if m != nil {
		return m.OperApp
	}
	return ""
}

// AntispamLogic::CHNNNEL_WELCOME (see AntiSpamDataType)
type ChannelModifyWelcomeMsgContext struct {
	ChannelId   uint32 `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	OperUid     uint32 `protobuf:"varint,2,opt,name=oper_uid,json=operUid,proto3" json:"oper_uid,omitempty"`
	Timestamp   uint32 `protobuf:"varint,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	ChannelType uint32 `protobuf:"varint,4,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
}

func (m *ChannelModifyWelcomeMsgContext) Reset()         { *m = ChannelModifyWelcomeMsgContext{} }
func (m *ChannelModifyWelcomeMsgContext) String() string { return proto.CompactTextString(m) }
func (*ChannelModifyWelcomeMsgContext) ProtoMessage()    {}
func (*ChannelModifyWelcomeMsgContext) Descriptor() ([]byte, []int) {
	return fileDescriptorAsynctexthandler, []int{9}
}

func (m *ChannelModifyWelcomeMsgContext) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelModifyWelcomeMsgContext) GetOperUid() uint32 {
	if m != nil {
		return m.OperUid
	}
	return 0
}

func (m *ChannelModifyWelcomeMsgContext) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *ChannelModifyWelcomeMsgContext) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

type TextCheckContext struct {
	Text        string `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	Uid         uint32 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	DataType    uint32 `protobuf:"varint,3,opt,name=data_type,json=dataType,proto3" json:"data_type,omitempty"`
	Result      uint32 `protobuf:"varint,4,opt,name=result,proto3" json:"result,omitempty"`
	Cxt         []byte `protobuf:"bytes,5,opt,name=cxt,proto3" json:"cxt,omitempty"`
	BizRecordId string `protobuf:"bytes,6,opt,name=biz_record_id,json=bizRecordId,proto3" json:"biz_record_id,omitempty"`
	Account     string `protobuf:"bytes,7,opt,name=account,proto3" json:"account,omitempty"`
	ProduceTime uint32 `protobuf:"varint,8,opt,name=produce_time,json=produceTime,proto3" json:"produce_time,omitempty"`
}

func (m *TextCheckContext) Reset()         { *m = TextCheckContext{} }
func (m *TextCheckContext) String() string { return proto.CompactTextString(m) }
func (*TextCheckContext) ProtoMessage()    {}
func (*TextCheckContext) Descriptor() ([]byte, []int) {
	return fileDescriptorAsynctexthandler, []int{10}
}

func (m *TextCheckContext) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *TextCheckContext) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TextCheckContext) GetDataType() uint32 {
	if m != nil {
		return m.DataType
	}
	return 0
}

func (m *TextCheckContext) GetResult() uint32 {
	if m != nil {
		return m.Result
	}
	return 0
}

func (m *TextCheckContext) GetCxt() []byte {
	if m != nil {
		return m.Cxt
	}
	return nil
}

func (m *TextCheckContext) GetBizRecordId() string {
	if m != nil {
		return m.BizRecordId
	}
	return ""
}

func (m *TextCheckContext) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *TextCheckContext) GetProduceTime() uint32 {
	if m != nil {
		return m.ProduceTime
	}
	return 0
}

type GuildInfoModifyContext struct {
	GuildId    uint32 `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Timestamp  uint32 `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	BeforeText string `protobuf:"bytes,3,opt,name=before_text,json=beforeText,proto3" json:"before_text,omitempty"`
	AppId      uint32 `protobuf:"varint,4,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	MarketId   uint32 `protobuf:"varint,5,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
}

func (m *GuildInfoModifyContext) Reset()         { *m = GuildInfoModifyContext{} }
func (m *GuildInfoModifyContext) String() string { return proto.CompactTextString(m) }
func (*GuildInfoModifyContext) ProtoMessage()    {}
func (*GuildInfoModifyContext) Descriptor() ([]byte, []int) {
	return fileDescriptorAsynctexthandler, []int{11}
}

func (m *GuildInfoModifyContext) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildInfoModifyContext) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *GuildInfoModifyContext) GetBeforeText() string {
	if m != nil {
		return m.BeforeText
	}
	return ""
}

func (m *GuildInfoModifyContext) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *GuildInfoModifyContext) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

type GuildCircleTopicContext struct {
	GuildId         uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Timestamp       uint32   `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	TopicTitle      string   `protobuf:"bytes,3,opt,name=topic_title,json=topicTitle,proto3" json:"topic_title,omitempty"`
	TopicContent    string   `protobuf:"bytes,4,opt,name=topic_content,json=topicContent,proto3" json:"topic_content,omitempty"`
	TopicImgKeylist []string `protobuf:"bytes,5,rep,name=topic_img_keylist,json=topicImgKeylist" json:"topic_img_keylist,omitempty"`
	IsHighlight     bool     `protobuf:"varint,6,opt,name=is_highlight,json=isHighlight,proto3" json:"is_highlight,omitempty"`
}

func (m *GuildCircleTopicContext) Reset()         { *m = GuildCircleTopicContext{} }
func (m *GuildCircleTopicContext) String() string { return proto.CompactTextString(m) }
func (*GuildCircleTopicContext) ProtoMessage()    {}
func (*GuildCircleTopicContext) Descriptor() ([]byte, []int) {
	return fileDescriptorAsynctexthandler, []int{12}
}

func (m *GuildCircleTopicContext) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildCircleTopicContext) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *GuildCircleTopicContext) GetTopicTitle() string {
	if m != nil {
		return m.TopicTitle
	}
	return ""
}

func (m *GuildCircleTopicContext) GetTopicContent() string {
	if m != nil {
		return m.TopicContent
	}
	return ""
}

func (m *GuildCircleTopicContext) GetTopicImgKeylist() []string {
	if m != nil {
		return m.TopicImgKeylist
	}
	return nil
}

func (m *GuildCircleTopicContext) GetIsHighlight() bool {
	if m != nil {
		return m.IsHighlight
	}
	return false
}

type GuildCircleCommentContext struct {
	GuildId      uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Timestamp    uint32   `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	TopicId      uint32   `protobuf:"varint,3,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	RefCommentId uint32   `protobuf:"varint,4,opt,name=ref_comment_id,json=refCommentId,proto3" json:"ref_comment_id,omitempty"`
	Content      string   `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	ImgKeylist   []string `protobuf:"bytes,6,rep,name=img_keylist,json=imgKeylist" json:"img_keylist,omitempty"`
}

func (m *GuildCircleCommentContext) Reset()         { *m = GuildCircleCommentContext{} }
func (m *GuildCircleCommentContext) String() string { return proto.CompactTextString(m) }
func (*GuildCircleCommentContext) ProtoMessage()    {}
func (*GuildCircleCommentContext) Descriptor() ([]byte, []int) {
	return fileDescriptorAsynctexthandler, []int{13}
}

func (m *GuildCircleCommentContext) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildCircleCommentContext) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *GuildCircleCommentContext) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *GuildCircleCommentContext) GetRefCommentId() uint32 {
	if m != nil {
		return m.RefCommentId
	}
	return 0
}

func (m *GuildCircleCommentContext) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *GuildCircleCommentContext) GetImgKeylist() []string {
	if m != nil {
		return m.ImgKeylist
	}
	return nil
}

type ModifyNicknameContext struct {
	PrefixValid bool   `protobuf:"varint,1,opt,name=prefix_valid,json=prefixValid,proto3" json:"prefix_valid,omitempty"`
	AppId       uint32 `protobuf:"varint,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	MarketId    uint32 `protobuf:"varint,3,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	DeviceIdHex string `protobuf:"bytes,4,opt,name=device_id_hex,json=deviceIdHex,proto3" json:"device_id_hex,omitempty"`
}

func (m *ModifyNicknameContext) Reset()         { *m = ModifyNicknameContext{} }
func (m *ModifyNicknameContext) String() string { return proto.CompactTextString(m) }
func (*ModifyNicknameContext) ProtoMessage()    {}
func (*ModifyNicknameContext) Descriptor() ([]byte, []int) {
	return fileDescriptorAsynctexthandler, []int{14}
}

func (m *ModifyNicknameContext) GetPrefixValid() bool {
	if m != nil {
		return m.PrefixValid
	}
	return false
}

func (m *ModifyNicknameContext) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *ModifyNicknameContext) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *ModifyNicknameContext) GetDeviceIdHex() string {
	if m != nil {
		return m.DeviceIdHex
	}
	return ""
}

type ModifyGameNickContext struct {
	Timestamp         uint32 `protobuf:"varint,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Appid             uint32 `protobuf:"varint,2,opt,name=appid,proto3" json:"appid,omitempty"`
	MarketId          uint32 `protobuf:"varint,3,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ReqTagType        uint32 `protobuf:"varint,4,opt,name=req_tag_type,json=reqTagType,proto3" json:"req_tag_type,omitempty"`
	TagInfo           []byte `protobuf:"bytes,5,opt,name=tag_info,json=tagInfo,proto3" json:"tag_info,omitempty"`
	OssTagListStr     string `protobuf:"bytes,6,opt,name=oss_tag_list_str,json=ossTagListStr,proto3" json:"oss_tag_list_str,omitempty"`
	OssGameTagListStr string `protobuf:"bytes,7,opt,name=oss_game_tag_list_str,json=ossGameTagListStr,proto3" json:"oss_game_tag_list_str,omitempty"`
	OssGameTagNickStr string `protobuf:"bytes,8,opt,name=oss_game_tag_nick_str,json=ossGameTagNickStr,proto3" json:"oss_game_tag_nick_str,omitempty"`
}

func (m *ModifyGameNickContext) Reset()         { *m = ModifyGameNickContext{} }
func (m *ModifyGameNickContext) String() string { return proto.CompactTextString(m) }
func (*ModifyGameNickContext) ProtoMessage()    {}
func (*ModifyGameNickContext) Descriptor() ([]byte, []int) {
	return fileDescriptorAsynctexthandler, []int{15}
}

func (m *ModifyGameNickContext) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *ModifyGameNickContext) GetAppid() uint32 {
	if m != nil {
		return m.Appid
	}
	return 0
}

func (m *ModifyGameNickContext) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *ModifyGameNickContext) GetReqTagType() uint32 {
	if m != nil {
		return m.ReqTagType
	}
	return 0
}

func (m *ModifyGameNickContext) GetTagInfo() []byte {
	if m != nil {
		return m.TagInfo
	}
	return nil
}

func (m *ModifyGameNickContext) GetOssTagListStr() string {
	if m != nil {
		return m.OssTagListStr
	}
	return ""
}

func (m *ModifyGameNickContext) GetOssGameTagListStr() string {
	if m != nil {
		return m.OssGameTagListStr
	}
	return ""
}

func (m *ModifyGameNickContext) GetOssGameTagNickStr() string {
	if m != nil {
		return m.OssGameTagNickStr
	}
	return ""
}

type GuildOrGroupEnterVerifyContext struct {
	NeedVerify uint32 `protobuf:"varint,1,opt,name=need_verify,json=needVerify,proto3" json:"need_verify,omitempty"`
	Question   string `protobuf:"bytes,2,opt,name=question,proto3" json:"question,omitempty"`
	Answer     string `protobuf:"bytes,3,opt,name=answer,proto3" json:"answer,omitempty"`
	GroupId    uint32 `protobuf:"varint,4,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
}

func (m *GuildOrGroupEnterVerifyContext) Reset()         { *m = GuildOrGroupEnterVerifyContext{} }
func (m *GuildOrGroupEnterVerifyContext) String() string { return proto.CompactTextString(m) }
func (*GuildOrGroupEnterVerifyContext) ProtoMessage()    {}
func (*GuildOrGroupEnterVerifyContext) Descriptor() ([]byte, []int) {
	return fileDescriptorAsynctexthandler, []int{16}
}

func (m *GuildOrGroupEnterVerifyContext) GetNeedVerify() uint32 {
	if m != nil {
		return m.NeedVerify
	}
	return 0
}

func (m *GuildOrGroupEnterVerifyContext) GetQuestion() string {
	if m != nil {
		return m.Question
	}
	return ""
}

func (m *GuildOrGroupEnterVerifyContext) GetAnswer() string {
	if m != nil {
		return m.Answer
	}
	return ""
}

func (m *GuildOrGroupEnterVerifyContext) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func init() {
	proto.RegisterType((*UserInfoContext)(nil), "asynctexthandler.UserInfoContext")
	proto.RegisterType((*GuildModifyGroupNameContext)(nil), "asynctexthandler.GuildModifyGroupNameContext")
	proto.RegisterType((*TempGroupModifyNameContext)(nil), "asynctexthandler.TempGroupModifyNameContext")
	proto.RegisterType((*TGroupModifyDescContext)(nil), "asynctexthandler.TGroupModifyDescContext")
	proto.RegisterType((*TGroupModifyNameContext)(nil), "asynctexthandler.TGroupModifyNameContext")
	proto.RegisterType((*GroupPublishBulletinHandlerContext)(nil), "asynctexthandler.GroupPublishBulletinHandlerContext")
	proto.RegisterType((*ChannelModifyNameContext)(nil), "asynctexthandler.ChannelModifyNameContext")
	proto.RegisterType((*ChannelModifyDescContext)(nil), "asynctexthandler.ChannelModifyDescContext")
	proto.RegisterType((*ChannelModifyTopicDetailContext)(nil), "asynctexthandler.ChannelModifyTopicDetailContext")
	proto.RegisterType((*ChannelModifyWelcomeMsgContext)(nil), "asynctexthandler.ChannelModifyWelcomeMsgContext")
	proto.RegisterType((*TextCheckContext)(nil), "asynctexthandler.TextCheckContext")
	proto.RegisterType((*GuildInfoModifyContext)(nil), "asynctexthandler.GuildInfoModifyContext")
	proto.RegisterType((*GuildCircleTopicContext)(nil), "asynctexthandler.GuildCircleTopicContext")
	proto.RegisterType((*GuildCircleCommentContext)(nil), "asynctexthandler.GuildCircleCommentContext")
	proto.RegisterType((*ModifyNicknameContext)(nil), "asynctexthandler.ModifyNicknameContext")
	proto.RegisterType((*ModifyGameNickContext)(nil), "asynctexthandler.ModifyGameNickContext")
	proto.RegisterType((*GuildOrGroupEnterVerifyContext)(nil), "asynctexthandler.GuildOrGroupEnterVerifyContext")
}
func (m *UserInfoContext) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserInfoContext) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Category) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(len(m.Category)))
		i += copy(dAtA[i:], m.Category)
	}
	return i, nil
}

func (m *GuildModifyGroupNameContext) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildModifyGroupNameContext) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.GuildId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.GuildId))
	}
	if m.GroupId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.GroupId))
	}
	return i, nil
}

func (m *TempGroupModifyNameContext) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TempGroupModifyNameContext) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.GroupId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.GroupId))
	}
	return i, nil
}

func (m *TGroupModifyDescContext) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TGroupModifyDescContext) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.GroupId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.GroupId))
	}
	if m.NewGameId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.NewGameId))
	}
	if len(m.CityCode) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(len(m.CityCode)))
		i += copy(dAtA[i:], m.CityCode)
	}
	if len(m.CityName) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(len(m.CityName)))
		i += copy(dAtA[i:], m.CityName)
	}
	return i, nil
}

func (m *TGroupModifyNameContext) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TGroupModifyNameContext) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.GroupId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.GroupId))
	}
	return i, nil
}

func (m *GroupPublishBulletinHandlerContext) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GroupPublishBulletinHandlerContext) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.GuildId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.GuildId))
	}
	if m.GroupId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.GroupId))
	}
	if m.GroupType != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.GroupType))
	}
	if m.SplitIndex != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.SplitIndex))
	}
	return i, nil
}

func (m *ChannelModifyNameContext) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelModifyNameContext) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ChannelId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.ChannelId))
	}
	if m.OperUid != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.OperUid))
	}
	if m.Timestamp != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.Timestamp))
	}
	if m.ChannelType != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.ChannelType))
	}
	if m.Appid != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.Appid))
	}
	if m.MarketId != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.MarketId))
	}
	return i, nil
}

func (m *ChannelModifyDescContext) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelModifyDescContext) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ChannelId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.ChannelId))
	}
	if m.OperUid != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.OperUid))
	}
	if m.Timestamp != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.Timestamp))
	}
	if m.ChannelType != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.ChannelType))
	}
	if len(m.Desc) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(len(m.Desc)))
		i += copy(dAtA[i:], m.Desc)
	}
	if len(m.TopicDetail) > 0 {
		dAtA[i] = 0x32
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(len(m.TopicDetail)))
		i += copy(dAtA[i:], m.TopicDetail)
	}
	if len(m.OperPlatform) > 0 {
		dAtA[i] = 0x3a
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(len(m.OperPlatform)))
		i += copy(dAtA[i:], m.OperPlatform)
	}
	if len(m.OperApp) > 0 {
		dAtA[i] = 0x42
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(len(m.OperApp)))
		i += copy(dAtA[i:], m.OperApp)
	}
	return i, nil
}

func (m *ChannelModifyTopicDetailContext) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelModifyTopicDetailContext) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ChannelId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.ChannelId))
	}
	if m.OperUid != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.OperUid))
	}
	if m.Timestamp != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.Timestamp))
	}
	if m.ChannelType != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.ChannelType))
	}
	if len(m.Desc) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(len(m.Desc)))
		i += copy(dAtA[i:], m.Desc)
	}
	if len(m.TopicDetail) > 0 {
		dAtA[i] = 0x32
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(len(m.TopicDetail)))
		i += copy(dAtA[i:], m.TopicDetail)
	}
	if len(m.OperPlatform) > 0 {
		dAtA[i] = 0x3a
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(len(m.OperPlatform)))
		i += copy(dAtA[i:], m.OperPlatform)
	}
	if len(m.OperApp) > 0 {
		dAtA[i] = 0x42
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(len(m.OperApp)))
		i += copy(dAtA[i:], m.OperApp)
	}
	return i, nil
}

func (m *ChannelModifyWelcomeMsgContext) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelModifyWelcomeMsgContext) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ChannelId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.ChannelId))
	}
	if m.OperUid != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.OperUid))
	}
	if m.Timestamp != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.Timestamp))
	}
	if m.ChannelType != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.ChannelType))
	}
	return i, nil
}

func (m *TextCheckContext) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TextCheckContext) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Text) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(len(m.Text)))
		i += copy(dAtA[i:], m.Text)
	}
	if m.Uid != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.Uid))
	}
	if m.DataType != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.DataType))
	}
	if m.Result != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.Result))
	}
	if len(m.Cxt) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(len(m.Cxt)))
		i += copy(dAtA[i:], m.Cxt)
	}
	if len(m.BizRecordId) > 0 {
		dAtA[i] = 0x32
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(len(m.BizRecordId)))
		i += copy(dAtA[i:], m.BizRecordId)
	}
	if len(m.Account) > 0 {
		dAtA[i] = 0x3a
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(len(m.Account)))
		i += copy(dAtA[i:], m.Account)
	}
	if m.ProduceTime != 0 {
		dAtA[i] = 0x40
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.ProduceTime))
	}
	return i, nil
}

func (m *GuildInfoModifyContext) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildInfoModifyContext) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.GuildId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.GuildId))
	}
	if m.Timestamp != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.Timestamp))
	}
	if len(m.BeforeText) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(len(m.BeforeText)))
		i += copy(dAtA[i:], m.BeforeText)
	}
	if m.AppId != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.AppId))
	}
	if m.MarketId != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.MarketId))
	}
	return i, nil
}

func (m *GuildCircleTopicContext) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleTopicContext) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.GuildId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.GuildId))
	}
	if m.Timestamp != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.Timestamp))
	}
	if len(m.TopicTitle) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(len(m.TopicTitle)))
		i += copy(dAtA[i:], m.TopicTitle)
	}
	if len(m.TopicContent) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(len(m.TopicContent)))
		i += copy(dAtA[i:], m.TopicContent)
	}
	if len(m.TopicImgKeylist) > 0 {
		for _, s := range m.TopicImgKeylist {
			dAtA[i] = 0x2a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if m.IsHighlight {
		dAtA[i] = 0x30
		i++
		if m.IsHighlight {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	return i, nil
}

func (m *GuildCircleCommentContext) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleCommentContext) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.GuildId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.GuildId))
	}
	if m.Timestamp != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.Timestamp))
	}
	if m.TopicId != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.TopicId))
	}
	if m.RefCommentId != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.RefCommentId))
	}
	if len(m.Content) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(len(m.Content)))
		i += copy(dAtA[i:], m.Content)
	}
	if len(m.ImgKeylist) > 0 {
		for _, s := range m.ImgKeylist {
			dAtA[i] = 0x32
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *ModifyNicknameContext) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyNicknameContext) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.PrefixValid {
		dAtA[i] = 0x8
		i++
		if m.PrefixValid {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	if m.AppId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.AppId))
	}
	if m.MarketId != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.MarketId))
	}
	if len(m.DeviceIdHex) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(len(m.DeviceIdHex)))
		i += copy(dAtA[i:], m.DeviceIdHex)
	}
	return i, nil
}

func (m *ModifyGameNickContext) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyGameNickContext) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Timestamp != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.Timestamp))
	}
	if m.Appid != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.Appid))
	}
	if m.MarketId != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.MarketId))
	}
	if m.ReqTagType != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.ReqTagType))
	}
	if len(m.TagInfo) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(len(m.TagInfo)))
		i += copy(dAtA[i:], m.TagInfo)
	}
	if len(m.OssTagListStr) > 0 {
		dAtA[i] = 0x32
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(len(m.OssTagListStr)))
		i += copy(dAtA[i:], m.OssTagListStr)
	}
	if len(m.OssGameTagListStr) > 0 {
		dAtA[i] = 0x3a
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(len(m.OssGameTagListStr)))
		i += copy(dAtA[i:], m.OssGameTagListStr)
	}
	if len(m.OssGameTagNickStr) > 0 {
		dAtA[i] = 0x42
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(len(m.OssGameTagNickStr)))
		i += copy(dAtA[i:], m.OssGameTagNickStr)
	}
	return i, nil
}

func (m *GuildOrGroupEnterVerifyContext) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildOrGroupEnterVerifyContext) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.NeedVerify != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.NeedVerify))
	}
	if len(m.Question) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(len(m.Question)))
		i += copy(dAtA[i:], m.Question)
	}
	if len(m.Answer) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(len(m.Answer)))
		i += copy(dAtA[i:], m.Answer)
	}
	if m.GroupId != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintAsynctexthandler(dAtA, i, uint64(m.GroupId))
	}
	return i, nil
}

func encodeFixed64Asynctexthandler(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Asynctexthandler(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintAsynctexthandler(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *UserInfoContext) Size() (n int) {
	var l int
	_ = l
	l = len(m.Category)
	if l > 0 {
		n += 1 + l + sovAsynctexthandler(uint64(l))
	}
	return n
}

func (m *GuildModifyGroupNameContext) Size() (n int) {
	var l int
	_ = l
	if m.GuildId != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.GuildId))
	}
	if m.GroupId != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.GroupId))
	}
	return n
}

func (m *TempGroupModifyNameContext) Size() (n int) {
	var l int
	_ = l
	if m.GroupId != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.GroupId))
	}
	return n
}

func (m *TGroupModifyDescContext) Size() (n int) {
	var l int
	_ = l
	if m.GroupId != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.GroupId))
	}
	if m.NewGameId != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.NewGameId))
	}
	l = len(m.CityCode)
	if l > 0 {
		n += 1 + l + sovAsynctexthandler(uint64(l))
	}
	l = len(m.CityName)
	if l > 0 {
		n += 1 + l + sovAsynctexthandler(uint64(l))
	}
	return n
}

func (m *TGroupModifyNameContext) Size() (n int) {
	var l int
	_ = l
	if m.GroupId != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.GroupId))
	}
	return n
}

func (m *GroupPublishBulletinHandlerContext) Size() (n int) {
	var l int
	_ = l
	if m.GuildId != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.GuildId))
	}
	if m.GroupId != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.GroupId))
	}
	if m.GroupType != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.GroupType))
	}
	if m.SplitIndex != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.SplitIndex))
	}
	return n
}

func (m *ChannelModifyNameContext) Size() (n int) {
	var l int
	_ = l
	if m.ChannelId != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.ChannelId))
	}
	if m.OperUid != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.OperUid))
	}
	if m.Timestamp != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.Timestamp))
	}
	if m.ChannelType != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.ChannelType))
	}
	if m.Appid != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.Appid))
	}
	if m.MarketId != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.MarketId))
	}
	return n
}

func (m *ChannelModifyDescContext) Size() (n int) {
	var l int
	_ = l
	if m.ChannelId != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.ChannelId))
	}
	if m.OperUid != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.OperUid))
	}
	if m.Timestamp != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.Timestamp))
	}
	if m.ChannelType != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.ChannelType))
	}
	l = len(m.Desc)
	if l > 0 {
		n += 1 + l + sovAsynctexthandler(uint64(l))
	}
	l = len(m.TopicDetail)
	if l > 0 {
		n += 1 + l + sovAsynctexthandler(uint64(l))
	}
	l = len(m.OperPlatform)
	if l > 0 {
		n += 1 + l + sovAsynctexthandler(uint64(l))
	}
	l = len(m.OperApp)
	if l > 0 {
		n += 1 + l + sovAsynctexthandler(uint64(l))
	}
	return n
}

func (m *ChannelModifyTopicDetailContext) Size() (n int) {
	var l int
	_ = l
	if m.ChannelId != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.ChannelId))
	}
	if m.OperUid != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.OperUid))
	}
	if m.Timestamp != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.Timestamp))
	}
	if m.ChannelType != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.ChannelType))
	}
	l = len(m.Desc)
	if l > 0 {
		n += 1 + l + sovAsynctexthandler(uint64(l))
	}
	l = len(m.TopicDetail)
	if l > 0 {
		n += 1 + l + sovAsynctexthandler(uint64(l))
	}
	l = len(m.OperPlatform)
	if l > 0 {
		n += 1 + l + sovAsynctexthandler(uint64(l))
	}
	l = len(m.OperApp)
	if l > 0 {
		n += 1 + l + sovAsynctexthandler(uint64(l))
	}
	return n
}

func (m *ChannelModifyWelcomeMsgContext) Size() (n int) {
	var l int
	_ = l
	if m.ChannelId != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.ChannelId))
	}
	if m.OperUid != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.OperUid))
	}
	if m.Timestamp != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.Timestamp))
	}
	if m.ChannelType != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.ChannelType))
	}
	return n
}

func (m *TextCheckContext) Size() (n int) {
	var l int
	_ = l
	l = len(m.Text)
	if l > 0 {
		n += 1 + l + sovAsynctexthandler(uint64(l))
	}
	if m.Uid != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.Uid))
	}
	if m.DataType != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.DataType))
	}
	if m.Result != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.Result))
	}
	l = len(m.Cxt)
	if l > 0 {
		n += 1 + l + sovAsynctexthandler(uint64(l))
	}
	l = len(m.BizRecordId)
	if l > 0 {
		n += 1 + l + sovAsynctexthandler(uint64(l))
	}
	l = len(m.Account)
	if l > 0 {
		n += 1 + l + sovAsynctexthandler(uint64(l))
	}
	if m.ProduceTime != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.ProduceTime))
	}
	return n
}

func (m *GuildInfoModifyContext) Size() (n int) {
	var l int
	_ = l
	if m.GuildId != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.GuildId))
	}
	if m.Timestamp != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.Timestamp))
	}
	l = len(m.BeforeText)
	if l > 0 {
		n += 1 + l + sovAsynctexthandler(uint64(l))
	}
	if m.AppId != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.AppId))
	}
	if m.MarketId != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.MarketId))
	}
	return n
}

func (m *GuildCircleTopicContext) Size() (n int) {
	var l int
	_ = l
	if m.GuildId != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.GuildId))
	}
	if m.Timestamp != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.Timestamp))
	}
	l = len(m.TopicTitle)
	if l > 0 {
		n += 1 + l + sovAsynctexthandler(uint64(l))
	}
	l = len(m.TopicContent)
	if l > 0 {
		n += 1 + l + sovAsynctexthandler(uint64(l))
	}
	if len(m.TopicImgKeylist) > 0 {
		for _, s := range m.TopicImgKeylist {
			l = len(s)
			n += 1 + l + sovAsynctexthandler(uint64(l))
		}
	}
	if m.IsHighlight {
		n += 2
	}
	return n
}

func (m *GuildCircleCommentContext) Size() (n int) {
	var l int
	_ = l
	if m.GuildId != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.GuildId))
	}
	if m.Timestamp != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.Timestamp))
	}
	if m.TopicId != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.TopicId))
	}
	if m.RefCommentId != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.RefCommentId))
	}
	l = len(m.Content)
	if l > 0 {
		n += 1 + l + sovAsynctexthandler(uint64(l))
	}
	if len(m.ImgKeylist) > 0 {
		for _, s := range m.ImgKeylist {
			l = len(s)
			n += 1 + l + sovAsynctexthandler(uint64(l))
		}
	}
	return n
}

func (m *ModifyNicknameContext) Size() (n int) {
	var l int
	_ = l
	if m.PrefixValid {
		n += 2
	}
	if m.AppId != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.AppId))
	}
	if m.MarketId != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.MarketId))
	}
	l = len(m.DeviceIdHex)
	if l > 0 {
		n += 1 + l + sovAsynctexthandler(uint64(l))
	}
	return n
}

func (m *ModifyGameNickContext) Size() (n int) {
	var l int
	_ = l
	if m.Timestamp != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.Timestamp))
	}
	if m.Appid != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.Appid))
	}
	if m.MarketId != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.MarketId))
	}
	if m.ReqTagType != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.ReqTagType))
	}
	l = len(m.TagInfo)
	if l > 0 {
		n += 1 + l + sovAsynctexthandler(uint64(l))
	}
	l = len(m.OssTagListStr)
	if l > 0 {
		n += 1 + l + sovAsynctexthandler(uint64(l))
	}
	l = len(m.OssGameTagListStr)
	if l > 0 {
		n += 1 + l + sovAsynctexthandler(uint64(l))
	}
	l = len(m.OssGameTagNickStr)
	if l > 0 {
		n += 1 + l + sovAsynctexthandler(uint64(l))
	}
	return n
}

func (m *GuildOrGroupEnterVerifyContext) Size() (n int) {
	var l int
	_ = l
	if m.NeedVerify != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.NeedVerify))
	}
	l = len(m.Question)
	if l > 0 {
		n += 1 + l + sovAsynctexthandler(uint64(l))
	}
	l = len(m.Answer)
	if l > 0 {
		n += 1 + l + sovAsynctexthandler(uint64(l))
	}
	if m.GroupId != 0 {
		n += 1 + sovAsynctexthandler(uint64(m.GroupId))
	}
	return n
}

func sovAsynctexthandler(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozAsynctexthandler(x uint64) (n int) {
	return sovAsynctexthandler(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *UserInfoContext) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAsynctexthandler
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserInfoContext: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserInfoContext: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Category", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Category = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAsynctexthandler(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildModifyGroupNameContext) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAsynctexthandler
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildModifyGroupNameContext: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildModifyGroupNameContext: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupId", wireType)
			}
			m.GroupId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAsynctexthandler(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TempGroupModifyNameContext) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAsynctexthandler
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TempGroupModifyNameContext: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TempGroupModifyNameContext: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupId", wireType)
			}
			m.GroupId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAsynctexthandler(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TGroupModifyDescContext) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAsynctexthandler
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TGroupModifyDescContext: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TGroupModifyDescContext: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupId", wireType)
			}
			m.GroupId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NewGameId", wireType)
			}
			m.NewGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NewGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CityCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CityCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CityName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CityName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAsynctexthandler(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TGroupModifyNameContext) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAsynctexthandler
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TGroupModifyNameContext: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TGroupModifyNameContext: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupId", wireType)
			}
			m.GroupId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAsynctexthandler(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GroupPublishBulletinHandlerContext) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAsynctexthandler
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GroupPublishBulletinHandlerContext: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GroupPublishBulletinHandlerContext: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupId", wireType)
			}
			m.GroupId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupType", wireType)
			}
			m.GroupType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SplitIndex", wireType)
			}
			m.SplitIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SplitIndex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAsynctexthandler(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelModifyNameContext) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAsynctexthandler
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelModifyNameContext: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelModifyNameContext: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperUid", wireType)
			}
			m.OperUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OperUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Appid", wireType)
			}
			m.Appid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Appid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MarketId", wireType)
			}
			m.MarketId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MarketId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAsynctexthandler(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelModifyDescContext) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAsynctexthandler
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelModifyDescContext: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelModifyDescContext: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperUid", wireType)
			}
			m.OperUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OperUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Desc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Desc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicDetail", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TopicDetail = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperPlatform", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OperPlatform = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperApp", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OperApp = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAsynctexthandler(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelModifyTopicDetailContext) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAsynctexthandler
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelModifyTopicDetailContext: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelModifyTopicDetailContext: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperUid", wireType)
			}
			m.OperUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OperUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Desc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Desc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicDetail", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TopicDetail = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperPlatform", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OperPlatform = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperApp", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OperApp = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAsynctexthandler(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelModifyWelcomeMsgContext) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAsynctexthandler
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelModifyWelcomeMsgContext: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelModifyWelcomeMsgContext: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperUid", wireType)
			}
			m.OperUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OperUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAsynctexthandler(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TextCheckContext) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAsynctexthandler
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TextCheckContext: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TextCheckContext: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Text", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Text = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DataType", wireType)
			}
			m.DataType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DataType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			m.Result = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Result |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Cxt", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Cxt = append(m.Cxt[:0], dAtA[iNdEx:postIndex]...)
			if m.Cxt == nil {
				m.Cxt = []byte{}
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BizRecordId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BizRecordId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProduceTime", wireType)
			}
			m.ProduceTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProduceTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAsynctexthandler(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildInfoModifyContext) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAsynctexthandler
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildInfoModifyContext: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildInfoModifyContext: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeforeText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BeforeText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MarketId", wireType)
			}
			m.MarketId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MarketId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAsynctexthandler(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleTopicContext) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAsynctexthandler
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleTopicContext: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleTopicContext: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicTitle", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TopicTitle = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicContent", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TopicContent = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicImgKeylist", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TopicImgKeylist = append(m.TopicImgKeylist, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsHighlight", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsHighlight = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipAsynctexthandler(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleCommentContext) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAsynctexthandler
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleCommentContext: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleCommentContext: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RefCommentId", wireType)
			}
			m.RefCommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RefCommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImgKeylist", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImgKeylist = append(m.ImgKeylist, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAsynctexthandler(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyNicknameContext) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAsynctexthandler
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifyNicknameContext: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifyNicknameContext: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PrefixValid", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.PrefixValid = bool(v != 0)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MarketId", wireType)
			}
			m.MarketId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MarketId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceIdHex", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceIdHex = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAsynctexthandler(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyGameNickContext) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAsynctexthandler
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifyGameNickContext: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifyGameNickContext: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Appid", wireType)
			}
			m.Appid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Appid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MarketId", wireType)
			}
			m.MarketId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MarketId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReqTagType", wireType)
			}
			m.ReqTagType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReqTagType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagInfo", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TagInfo = append(m.TagInfo[:0], dAtA[iNdEx:postIndex]...)
			if m.TagInfo == nil {
				m.TagInfo = []byte{}
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OssTagListStr", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OssTagListStr = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OssGameTagListStr", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OssGameTagListStr = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OssGameTagNickStr", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OssGameTagNickStr = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAsynctexthandler(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildOrGroupEnterVerifyContext) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAsynctexthandler
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildOrGroupEnterVerifyContext: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildOrGroupEnterVerifyContext: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NeedVerify", wireType)
			}
			m.NeedVerify = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NeedVerify |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Question", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Question = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Answer", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Answer = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupId", wireType)
			}
			m.GroupId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAsynctexthandler(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAsynctexthandler
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipAsynctexthandler(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowAsynctexthandler
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAsynctexthandler
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthAsynctexthandler
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowAsynctexthandler
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipAsynctexthandler(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthAsynctexthandler = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowAsynctexthandler   = fmt2.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("src/asynctexthandler/asynctexthandler.proto", fileDescriptorAsynctexthandler)
}

var fileDescriptorAsynctexthandler = []byte{
	// 1116 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe4, 0x56, 0xcd, 0x8e, 0x1b, 0x45,
	0x10, 0x66, 0xbc, 0x7f, 0x9e, 0xf2, 0x9a, 0x38, 0x23, 0x36, 0xf1, 0x6e, 0x88, 0x37, 0x0c, 0x91,
	0x88, 0x40, 0xb0, 0x48, 0x20, 0x71, 0x26, 0x0e, 0xda, 0xb5, 0x20, 0x10, 0xcd, 0x3a, 0xe1, 0x38,
	0x6a, 0x77, 0x97, 0xc7, 0x2d, 0xcf, 0x4c, 0xcf, 0x76, 0xb7, 0x77, 0xed, 0x3c, 0x03, 0x42, 0x11,
	0xdc, 0xe0, 0xce, 0x03, 0xf0, 0x14, 0x39, 0xe6, 0x11, 0xa2, 0xe5, 0xc0, 0x6b, 0xa0, 0xee, 0x9e,
	0xf1, 0x5f, 0x14, 0x88, 0x14, 0xa4, 0x1c, 0x72, 0xb2, 0xeb, 0xab, 0xae, 0x9e, 0xaf, 0xbe, 0xaa,
	0xae, 0x6e, 0xf8, 0x44, 0x49, 0x7a, 0x44, 0xd4, 0x2c, 0xa7, 0x1a, 0xa7, 0x7a, 0x44, 0x72, 0x96,
	0xa2, 0x7c, 0x01, 0xf8, 0xac, 0x90, 0x42, 0x8b, 0xa0, 0xb5, 0x8e, 0x1f, 0xdc, 0xa6, 0x22, 0xcb,
	0x44, 0x7e, 0xa4, 0xd3, 0xf3, 0x82, 0xd3, 0x71, 0x8a, 0x47, 0x6a, 0x3c, 0x98, 0xf0, 0x54, 0xf3,
	0x5c, 0xcf, 0x0a, 0x74, 0x71, 0xe1, 0xa7, 0x70, 0xe5, 0xa1, 0x42, 0xd9, 0xcb, 0x87, 0xa2, 0x2b,
	0x72, 0x13, 0x1f, 0x1c, 0x40, 0x9d, 0x12, 0x8d, 0x89, 0x90, 0xb3, 0xb6, 0x77, 0xcb, 0xbb, 0xe3,
	0x47, 0x73, 0x3b, 0x3c, 0x85, 0x1b, 0xc7, 0x13, 0x9e, 0xb2, 0xfb, 0x82, 0xf1, 0xe1, 0xec, 0x58,
	0x8a, 0x49, 0xf1, 0x3d, 0xc9, 0xb0, 0x0a, 0xdd, 0x87, 0x7a, 0x62, 0xdc, 0x31, 0x67, 0x36, 0xb4,
	0x19, 0xed, 0x58, 0xbb, 0xc7, 0xac, 0xcb, 0x2c, 0x37, 0xae, 0x5a, 0xe9, 0x32, 0x76, 0x8f, 0x85,
	0x5f, 0xc1, 0x41, 0x1f, 0xb3, 0xc2, 0xee, 0xe6, 0x36, 0x5e, 0xdf, 0xb3, 0x0a, 0xf4, 0x56, 0x03,
	0x7f, 0xf6, 0xe0, 0x7a, 0x7f, 0x29, 0xec, 0x1e, 0x2a, 0xfa, 0xdf, 0x61, 0x41, 0x07, 0x1a, 0x39,
	0x5e, 0xc4, 0x09, 0xc9, 0x70, 0xc1, 0xc6, 0xcf, 0xf1, 0xe2, 0x98, 0x64, 0xd8, 0x63, 0xc1, 0x0d,
	0xf0, 0x29, 0xd7, 0xb3, 0x98, 0x0a, 0x86, 0xed, 0x8d, 0x52, 0x01, 0xae, 0x67, 0x5d, 0xc1, 0x70,
	0xee, 0xcc, 0x49, 0x86, 0xed, 0xcd, 0x85, 0xd3, 0x50, 0x0e, 0xbf, 0x5c, 0xe5, 0xf3, 0x8a, 0x69,
	0xfc, 0xe6, 0x41, 0x68, 0xa3, 0x1e, 0x4c, 0x06, 0x29, 0x57, 0xa3, 0xbb, 0x93, 0x34, 0x45, 0xcd,
	0xf3, 0x13, 0x57, 0xc9, 0xd7, 0x12, 0x37, 0xb8, 0x09, 0xe0, 0x5c, 0xa6, 0xe8, 0x36, 0x9b, 0x66,
	0xe4, 0x5b, 0xa4, 0x3f, 0x2b, 0x30, 0x38, 0x84, 0x86, 0x2a, 0x52, 0xae, 0x63, 0x9e, 0x33, 0x9c,
	0xda, 0x84, 0x9a, 0x11, 0x58, 0xa8, 0x67, 0x90, 0xf0, 0xa9, 0x07, 0xed, 0xee, 0x88, 0xe4, 0x39,
	0xa6, 0x2f, 0x26, 0x75, 0x13, 0x80, 0x3a, 0xdf, 0x82, 0x94, 0x5f, 0x22, 0x8e, 0x96, 0x28, 0x50,
	0xc6, 0x93, 0x05, 0x2d, 0x63, 0x3f, 0xe4, 0x2c, 0x78, 0x1f, 0x7c, 0xcd, 0x33, 0x54, 0x9a, 0x64,
	0x45, 0xc5, 0x6a, 0x0e, 0x04, 0x1f, 0xc0, 0x6e, 0xb5, 0xaf, 0xa5, 0xed, 0x68, 0x35, 0x4a, 0xcc,
	0x12, 0x7f, 0x0f, 0xb6, 0x48, 0x51, 0x70, 0xd6, 0xde, 0xb2, 0x3e, 0x67, 0x98, 0xea, 0x64, 0x44,
	0x8e, 0x51, 0x1b, 0x3e, 0xdb, 0xd6, 0x53, 0x77, 0x40, 0x8f, 0x85, 0x3f, 0xd5, 0xd6, 0x52, 0x59,
	0xee, 0x97, 0x37, 0x98, 0x4a, 0x00, 0x9b, 0x0c, 0x15, 0xb5, 0x99, 0xf8, 0x91, 0xfd, 0x6f, 0xc2,
	0xb4, 0x28, 0x38, 0x8d, 0x19, 0x6a, 0xc2, 0x53, 0x9b, 0x8b, 0x1f, 0x35, 0x2c, 0x76, 0xcf, 0x42,
	0xc1, 0x87, 0xd0, 0xb4, 0x94, 0x8a, 0x94, 0xe8, 0xa1, 0x90, 0x59, 0x7b, 0xc7, 0xae, 0xd9, 0x35,
	0xe0, 0x83, 0x12, 0x9b, 0xf3, 0x26, 0x45, 0xd1, 0xae, 0x5b, 0xbf, 0xe5, 0xfd, 0x75, 0x51, 0x84,
	0xbf, 0xd6, 0xe0, 0x70, 0x45, 0x8e, 0xfe, 0x62, 0xf3, 0xb7, 0x57, 0x95, 0xdf, 0x3d, 0xe8, 0xac,
	0xa8, 0xf2, 0x23, 0xa6, 0x54, 0x64, 0x78, 0x5f, 0x25, 0x6f, 0x5e, 0x94, 0xf0, 0xb9, 0x07, 0xad,
	0x3e, 0x4e, 0x75, 0x77, 0x84, 0x74, 0x5c, 0xf1, 0x09, 0x60, 0xd3, 0xfc, 0x96, 0xc3, 0xda, 0xfe,
	0x0f, 0x5a, 0xb0, 0xb1, 0xf8, 0xbe, 0xf9, 0x6b, 0x8e, 0x06, 0x23, 0x9a, 0x2c, 0xcf, 0x81, 0xba,
	0x01, 0xac, 0xd8, 0xd7, 0x60, 0x5b, 0xa2, 0x9a, 0xa4, 0xba, 0xfc, 0x68, 0x69, 0x99, 0x6d, 0xe8,
	0x54, 0xdb, 0x1a, 0xec, 0x46, 0xe6, 0x6f, 0x10, 0x42, 0x73, 0xc0, 0x1f, 0xc7, 0x12, 0xa9, 0x90,
	0xac, 0x3a, 0x65, 0x7e, 0xd4, 0x18, 0xf0, 0xc7, 0x91, 0xc5, 0x7a, 0x2c, 0x68, 0xc3, 0x0e, 0xa1,
	0x54, 0x4c, 0x72, 0x5d, 0xaa, 0x5f, 0x99, 0x26, 0xc5, 0x42, 0x0a, 0x36, 0xa1, 0x18, 0x9b, 0xbc,
	0xad, 0xf8, 0xcd, 0xa8, 0x51, 0x62, 0x7d, 0x9e, 0x61, 0xf8, 0x87, 0x07, 0xd7, 0xec, 0x1d, 0x63,
	0xee, 0x24, 0x57, 0x82, 0x57, 0x98, 0x80, 0x2b, 0xca, 0xd6, 0xd6, 0x95, 0x3d, 0x84, 0xc6, 0x00,
	0x87, 0x42, 0x62, 0x6c, 0x85, 0x72, 0x33, 0x1d, 0x1c, 0x64, 0xe4, 0x0c, 0xf6, 0x60, 0x9b, 0x14,
	0x76, 0x7c, 0x6e, 0xce, 0xc7, 0x49, 0x6f, 0x6d, 0x9c, 0x6c, 0xad, 0x8d, 0x93, 0xbf, 0x3d, 0xb8,
	0x6e, 0x89, 0x76, 0xb9, 0xa4, 0x29, 0xda, 0xd3, 0xf3, 0x7f, 0x30, 0x75, 0x1d, 0xae, 0xb9, 0x4e,
	0xab, 0xdb, 0x07, 0x2c, 0xd4, 0x37, 0x88, 0xe9, 0x6f, 0xb7, 0x80, 0x9a, 0x4f, 0xe5, 0xba, 0xbc,
	0x83, 0xdc, 0xb9, 0xe8, 0x3a, 0x2c, 0xf8, 0x18, 0xae, 0xba, 0x45, 0x3c, 0x4b, 0xe2, 0x31, 0xce,
	0x52, 0xae, 0x4c, 0x11, 0x37, 0xee, 0xf8, 0xd1, 0x15, 0xeb, 0xe8, 0x65, 0xc9, 0xb7, 0x0e, 0x36,
	0x25, 0xe1, 0x2a, 0x1e, 0xf1, 0x64, 0x94, 0xf2, 0x64, 0xa4, 0x6d, 0x3d, 0xeb, 0x51, 0x83, 0xab,
	0x93, 0x0a, 0x0a, 0x9f, 0x79, 0xb0, 0xbf, 0x94, 0x69, 0x57, 0x64, 0x19, 0xe6, 0xfa, 0xb5, 0x73,
	0xdd, 0x87, 0x7a, 0xc9, 0x92, 0x95, 0x0d, 0xb9, 0xe3, 0xc8, 0xb1, 0xe0, 0x36, 0xbc, 0x2b, 0x71,
	0x18, 0x53, 0xf7, 0xa5, 0x45, 0x5d, 0x76, 0x25, 0x0e, 0xcb, 0xcf, 0xbb, 0x3e, 0xab, 0x54, 0x70,
	0x53, 0xa2, 0x32, 0x8d, 0x8c, 0xcb, 0xa9, 0x6f, 0xdb, 0xd4, 0x81, 0xcf, 0xb3, 0x0e, 0x7f, 0xf1,
	0x60, 0xaf, 0xbc, 0xcf, 0x38, 0x1d, 0xe7, 0x4b, 0x77, 0x9a, 0x6d, 0x51, 0x1c, 0xf2, 0x69, 0x7c,
	0x4e, 0xd2, 0x32, 0xa5, 0xba, 0x69, 0x51, 0x83, 0x3d, 0x32, 0xd0, 0x52, 0xb7, 0xd4, 0x5e, 0xda,
	0x2d, 0x1b, 0xab, 0xdd, 0x62, 0xce, 0x0d, 0xc3, 0x73, 0x4e, 0xcd, 0x93, 0x23, 0x1e, 0x95, 0x57,
	0xad, 0x1f, 0x35, 0x1c, 0xd8, 0x63, 0x27, 0x38, 0x0d, 0xff, 0xac, 0x55, 0xa4, 0xcc, 0x4b, 0xc4,
	0x10, 0xab, 0x48, 0xad, 0x08, 0xe9, 0xad, 0x0b, 0x39, 0xbf, 0x0b, 0x6b, 0x2f, 0xbd, 0x0b, 0xd7,
	0xe9, 0xdc, 0x82, 0x5d, 0x89, 0x67, 0xb1, 0x26, 0xc9, 0xf2, 0xac, 0x01, 0x89, 0x67, 0x7d, 0x92,
	0xd8, 0x91, 0x60, 0xaa, 0x43, 0x92, 0x98, 0xe7, 0x43, 0x51, 0x9e, 0xff, 0x1d, 0x4d, 0x12, 0x73,
	0x28, 0x83, 0x8f, 0xa0, 0x25, 0x94, 0xb2, 0xc1, 0x46, 0xcc, 0x58, 0x69, 0x59, 0x8e, 0x81, 0xa6,
	0x50, 0xaa, 0x4f, 0x92, 0xef, 0xb8, 0xd2, 0xa7, 0x5a, 0x06, 0x9f, 0xc3, 0x9e, 0x59, 0x68, 0x5f,
	0x5a, 0x2b, 0xab, 0xdd, 0x58, 0xb8, 0x2a, 0x94, 0x32, 0x99, 0xfe, 0x4b, 0x44, 0xce, 0xe9, 0xd8,
	0x46, 0xd4, 0xd7, 0x23, 0x8c, 0x3c, 0xa7, 0x5a, 0x86, 0x4f, 0x3c, 0xe8, 0xd8, 0xe6, 0xfc, 0x41,
	0xda, 0x47, 0xd4, 0x37, 0xb9, 0x46, 0xf9, 0x08, 0xe5, 0xd2, 0xdc, 0x38, 0x34, 0x0f, 0x3e, 0x64,
	0xf1, 0xb9, 0x45, 0x4b, 0xfd, 0xc0, 0x40, 0x6e, 0x9d, 0x79, 0xf2, 0x9e, 0x4d, 0x50, 0x69, 0x2e,
	0x72, 0xab, 0xa1, 0x1f, 0xcd, 0x6d, 0x33, 0x1a, 0x49, 0xae, 0x2e, 0x50, 0x96, 0x87, 0xb1, 0xb4,
	0x56, 0xde, 0x5c, 0x9b, 0x2b, 0x6f, 0xae, 0xbb, 0xad, 0xa7, 0x97, 0x1d, 0xef, 0xd9, 0x65, 0xc7,
	0x7b, 0x7e, 0xd9, 0xf1, 0x9e, 0xfc, 0xd5, 0x79, 0x67, 0xb0, 0x6d, 0x5f, 0xdb, 0x5f, 0xfc, 0x13,
	0x00, 0x00, 0xff, 0xff, 0x9c, 0xcf, 0x2f, 0x79, 0xd4, 0x0b, 0x00, 0x00,
}
