// Code generated by protoc-gen-gogo.
// source: src/smssvr/sms.proto
// DO NOT EDIT!

/*
	Package Sms is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/smssvr/sms.proto

	It has these top-level messages:
		SendSmsReq
		DirectSendSmsReq
		CreateVerifyCodeReq
		CreateVerifyCodeResp
		ValidateVerifyCodeReq
		CheckUrlValidApkUrlReq
		CheckUrlValidApkUrlResp
		DownLoadUrlReq
		DownLoadUrlResp
		DownLoadUrlByteReq
		DownLoadUrlByteResp
		Foo
		PostUrlDataReq
		PostUrlDataResp
		SendVoiceVerifyCodeReq
		SendVoiceVerifyCodeResp
		SendSmsWithProviderReq
		SendSmsWithProviderResp
		RecordVerifyCodePassReq
		RecordVerifyCodePassResp
		SendMarketingSmsReq
		SendMarketingPhoneErrResult
		SendMarketingSmsResp
*/
package Sms

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import tlvpickle "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// 专用错误码从-20开始
type ERR_SMS int32

const (
	ERR_SMS_ERR_SMS_VERFIYCODE_VALIDATE_FAIL   ERR_SMS = -20
	ERR_SMS_ERR_SMS_TOO_MANY_PHONE             ERR_SMS = -21
	ERR_SMS_ERR_SMS_SEND_SMS_FREQ              ERR_SMS = -22
	ERR_SMS_ERR_SMS_TYPE_INVALID               ERR_SMS = -23
	ERR_SMS_ERR_SMS_NO_ENOUGH_PARAMS           ERR_SMS = -24
	ERR_SMS_ERR_SMS_SEND_SMS_THRESHHOLD_EXCEED ERR_SMS = -25
	ERR_SMS_ERR_SMS_INVALID_PHONE              ERR_SMS = -26
	ERR_SMS_ERR_SMS_INVALID_VERIFYCODE         ERR_SMS = -27
	ERR_SMS_ERR_SMS_NOT_SUPPORT_INN            ERR_SMS = -28
	ERR_SMS_ERR_VALID_APK_URL_CANNOT_REACH     ERR_SMS = -31
	ERR_SMS_ERR_VALID_APK_URL_NOT_APK          ERR_SMS = -32
)

var ERR_SMS_name = map[int32]string{
	-20: "ERR_SMS_VERFIYCODE_VALIDATE_FAIL",
	-21: "ERR_SMS_TOO_MANY_PHONE",
	-22: "ERR_SMS_SEND_SMS_FREQ",
	-23: "ERR_SMS_TYPE_INVALID",
	-24: "ERR_SMS_NO_ENOUGH_PARAMS",
	-25: "ERR_SMS_SEND_SMS_THRESHHOLD_EXCEED",
	-26: "ERR_SMS_INVALID_PHONE",
	-27: "ERR_SMS_INVALID_VERIFYCODE",
	-28: "ERR_SMS_NOT_SUPPORT_INN",
	-31: "ERR_VALID_APK_URL_CANNOT_REACH",
	-32: "ERR_VALID_APK_URL_NOT_APK",
}
var ERR_SMS_value = map[string]int32{
	"ERR_SMS_VERFIYCODE_VALIDATE_FAIL":   -20,
	"ERR_SMS_TOO_MANY_PHONE":             -21,
	"ERR_SMS_SEND_SMS_FREQ":              -22,
	"ERR_SMS_TYPE_INVALID":               -23,
	"ERR_SMS_NO_ENOUGH_PARAMS":           -24,
	"ERR_SMS_SEND_SMS_THRESHHOLD_EXCEED": -25,
	"ERR_SMS_INVALID_PHONE":              -26,
	"ERR_SMS_INVALID_VERIFYCODE":         -27,
	"ERR_SMS_NOT_SUPPORT_INN":            -28,
	"ERR_VALID_APK_URL_CANNOT_REACH":     -31,
	"ERR_VALID_APK_URL_NOT_APK":          -32,
}

func (x ERR_SMS) Enum() *ERR_SMS {
	p := new(ERR_SMS)
	*p = x
	return p
}
func (x ERR_SMS) String() string {
	return proto.EnumName(ERR_SMS_name, int32(x))
}
func (x *ERR_SMS) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ERR_SMS_value, data, "ERR_SMS")
	if err != nil {
		return err
	}
	*x = ERR_SMS(value)
	return nil
}
func (ERR_SMS) EnumDescriptor() ([]byte, []int) { return fileDescriptorSms, []int{0} }

// 单发短信
type SendSmsReq struct {
	Phone           string   `protobuf:"bytes,1,req,name=phone" json:"phone"`
	SmsType         uint32   `protobuf:"varint,2,req,name=sms_type,json=smsType" json:"sms_type"`
	ParamList       []string `protobuf:"bytes,3,rep,name=param_list,json=paramList" json:"param_list,omitempty"`
	WithoutCooldown bool     `protobuf:"varint,4,opt,name=without_cooldown,json=withoutCooldown" json:"without_cooldown"`
	VerifyCodeKey   string   `protobuf:"bytes,5,opt,name=verify_code_key,json=verifyCodeKey" json:"verify_code_key"`
	VerifyCodeUsage string   `protobuf:"bytes,6,opt,name=verify_code_usage,json=verifyCodeUsage" json:"verify_code_usage"`
	MarketId        uint32   `protobuf:"varint,7,opt,name=market_id,json=marketId" json:"market_id"`
	// 	required uint32 retry_times = 4;	//当前的重试次数(start with 0)
	BizId      uint32 `protobuf:"varint,8,opt,name=biz_id,json=bizId" json:"biz_id"`
	ExtBizId   uint32 `protobuf:"varint,9,opt,name=ext_biz_id,json=extBizId" json:"ext_biz_id"`
	RequestId  string `protobuf:"bytes,10,opt,name=request_id,json=requestId" json:"request_id"`
	CreateTime uint64 `protobuf:"varint,11,opt,name=create_time,json=createTime" json:"create_time"`
}

func (m *SendSmsReq) Reset()                    { *m = SendSmsReq{} }
func (m *SendSmsReq) String() string            { return proto.CompactTextString(m) }
func (*SendSmsReq) ProtoMessage()               {}
func (*SendSmsReq) Descriptor() ([]byte, []int) { return fileDescriptorSms, []int{0} }

func (m *SendSmsReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *SendSmsReq) GetSmsType() uint32 {
	if m != nil {
		return m.SmsType
	}
	return 0
}

func (m *SendSmsReq) GetParamList() []string {
	if m != nil {
		return m.ParamList
	}
	return nil
}

func (m *SendSmsReq) GetWithoutCooldown() bool {
	if m != nil {
		return m.WithoutCooldown
	}
	return false
}

func (m *SendSmsReq) GetVerifyCodeKey() string {
	if m != nil {
		return m.VerifyCodeKey
	}
	return ""
}

func (m *SendSmsReq) GetVerifyCodeUsage() string {
	if m != nil {
		return m.VerifyCodeUsage
	}
	return ""
}

func (m *SendSmsReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *SendSmsReq) GetBizId() uint32 {
	if m != nil {
		return m.BizId
	}
	return 0
}

func (m *SendSmsReq) GetExtBizId() uint32 {
	if m != nil {
		return m.ExtBizId
	}
	return 0
}

func (m *SendSmsReq) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

func (m *SendSmsReq) GetCreateTime() uint64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

type DirectSendSmsReq struct {
	Phone    string `protobuf:"bytes,1,req,name=phone" json:"phone"`
	Text     string `protobuf:"bytes,2,req,name=text" json:"text"`
	BizId    uint32 `protobuf:"varint,3,opt,name=biz_id,json=bizId" json:"biz_id"`
	ExtBizId uint32 `protobuf:"varint,4,opt,name=ext_biz_id,json=extBizId" json:"ext_biz_id"`
}

func (m *DirectSendSmsReq) Reset()                    { *m = DirectSendSmsReq{} }
func (m *DirectSendSmsReq) String() string            { return proto.CompactTextString(m) }
func (*DirectSendSmsReq) ProtoMessage()               {}
func (*DirectSendSmsReq) Descriptor() ([]byte, []int) { return fileDescriptorSms, []int{1} }

func (m *DirectSendSmsReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *DirectSendSmsReq) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *DirectSendSmsReq) GetBizId() uint32 {
	if m != nil {
		return m.BizId
	}
	return 0
}

func (m *DirectSendSmsReq) GetExtBizId() uint32 {
	if m != nil {
		return m.ExtBizId
	}
	return 0
}

// //////////////////////////////////////////////
// 验证码相关逻辑， 暂时放在此server， 以后独立
// //////////////////////////////////////////////
// 为uid生成验证码
type CreateVerifyCodeReq struct {
	Key     string `protobuf:"bytes,1,req,name=key" json:"key"`
	CodeLen uint32 `protobuf:"varint,2,opt,name=code_len,json=codeLen" json:"code_len"`
}

func (m *CreateVerifyCodeReq) Reset()                    { *m = CreateVerifyCodeReq{} }
func (m *CreateVerifyCodeReq) String() string            { return proto.CompactTextString(m) }
func (*CreateVerifyCodeReq) ProtoMessage()               {}
func (*CreateVerifyCodeReq) Descriptor() ([]byte, []int) { return fileDescriptorSms, []int{2} }

func (m *CreateVerifyCodeReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *CreateVerifyCodeReq) GetCodeLen() uint32 {
	if m != nil {
		return m.CodeLen
	}
	return 0
}

type CreateVerifyCodeResp struct {
	Key        string `protobuf:"bytes,1,req,name=key" json:"key"`
	VerifyCode string `protobuf:"bytes,2,req,name=verify_code,json=verifyCode" json:"verify_code"`
	ExpireTime uint32 `protobuf:"varint,3,req,name=expire_time,json=expireTime" json:"expire_time"`
}

func (m *CreateVerifyCodeResp) Reset()                    { *m = CreateVerifyCodeResp{} }
func (m *CreateVerifyCodeResp) String() string            { return proto.CompactTextString(m) }
func (*CreateVerifyCodeResp) ProtoMessage()               {}
func (*CreateVerifyCodeResp) Descriptor() ([]byte, []int) { return fileDescriptorSms, []int{3} }

func (m *CreateVerifyCodeResp) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *CreateVerifyCodeResp) GetVerifyCode() string {
	if m != nil {
		return m.VerifyCode
	}
	return ""
}

func (m *CreateVerifyCodeResp) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

// 验证验证码是否正确
type ValidateVerifyCodeReq struct {
	Key        string `protobuf:"bytes,1,req,name=key" json:"key"`
	VerifyCode string `protobuf:"bytes,2,req,name=verify_code,json=verifyCode" json:"verify_code"`
}

func (m *ValidateVerifyCodeReq) Reset()                    { *m = ValidateVerifyCodeReq{} }
func (m *ValidateVerifyCodeReq) String() string            { return proto.CompactTextString(m) }
func (*ValidateVerifyCodeReq) ProtoMessage()               {}
func (*ValidateVerifyCodeReq) Descriptor() ([]byte, []int) { return fileDescriptorSms, []int{4} }

func (m *ValidateVerifyCodeReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *ValidateVerifyCodeReq) GetVerifyCode() string {
	if m != nil {
		return m.VerifyCode
	}
	return ""
}

type CheckUrlValidApkUrlReq struct {
	Url string `protobuf:"bytes,1,req,name=url" json:"url"`
}

func (m *CheckUrlValidApkUrlReq) Reset()                    { *m = CheckUrlValidApkUrlReq{} }
func (m *CheckUrlValidApkUrlReq) String() string            { return proto.CompactTextString(m) }
func (*CheckUrlValidApkUrlReq) ProtoMessage()               {}
func (*CheckUrlValidApkUrlReq) Descriptor() ([]byte, []int) { return fileDescriptorSms, []int{5} }

func (m *CheckUrlValidApkUrlReq) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

type CheckUrlValidApkUrlResp struct {
	ContentLength uint32 `protobuf:"varint,1,req,name=content_length,json=contentLength" json:"content_length"`
}

func (m *CheckUrlValidApkUrlResp) Reset()                    { *m = CheckUrlValidApkUrlResp{} }
func (m *CheckUrlValidApkUrlResp) String() string            { return proto.CompactTextString(m) }
func (*CheckUrlValidApkUrlResp) ProtoMessage()               {}
func (*CheckUrlValidApkUrlResp) Descriptor() ([]byte, []int) { return fileDescriptorSms, []int{6} }

func (m *CheckUrlValidApkUrlResp) GetContentLength() uint32 {
	if m != nil {
		return m.ContentLength
	}
	return 0
}

type DownLoadUrlReq struct {
	Url     string `protobuf:"bytes,1,req,name=url" json:"url"`
	Timeout uint32 `protobuf:"varint,2,opt,name=timeout" json:"timeout"`
}

func (m *DownLoadUrlReq) Reset()                    { *m = DownLoadUrlReq{} }
func (m *DownLoadUrlReq) String() string            { return proto.CompactTextString(m) }
func (*DownLoadUrlReq) ProtoMessage()               {}
func (*DownLoadUrlReq) Descriptor() ([]byte, []int) { return fileDescriptorSms, []int{7} }

func (m *DownLoadUrlReq) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *DownLoadUrlReq) GetTimeout() uint32 {
	if m != nil {
		return m.Timeout
	}
	return 0
}

type DownLoadUrlResp struct {
	Msg string `protobuf:"bytes,1,req,name=msg" json:"msg"`
}

func (m *DownLoadUrlResp) Reset()                    { *m = DownLoadUrlResp{} }
func (m *DownLoadUrlResp) String() string            { return proto.CompactTextString(m) }
func (*DownLoadUrlResp) ProtoMessage()               {}
func (*DownLoadUrlResp) Descriptor() ([]byte, []int) { return fileDescriptorSms, []int{8} }

func (m *DownLoadUrlResp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type DownLoadUrlByteReq struct {
	Url     string `protobuf:"bytes,1,req,name=url" json:"url"`
	Timeout uint32 `protobuf:"varint,2,opt,name=timeout" json:"timeout"`
}

func (m *DownLoadUrlByteReq) Reset()                    { *m = DownLoadUrlByteReq{} }
func (m *DownLoadUrlByteReq) String() string            { return proto.CompactTextString(m) }
func (*DownLoadUrlByteReq) ProtoMessage()               {}
func (*DownLoadUrlByteReq) Descriptor() ([]byte, []int) { return fileDescriptorSms, []int{9} }

func (m *DownLoadUrlByteReq) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *DownLoadUrlByteReq) GetTimeout() uint32 {
	if m != nil {
		return m.Timeout
	}
	return 0
}

type DownLoadUrlByteResp struct {
	Msg []byte `protobuf:"bytes,1,req,name=msg" json:"msg"`
}

func (m *DownLoadUrlByteResp) Reset()                    { *m = DownLoadUrlByteResp{} }
func (m *DownLoadUrlByteResp) String() string            { return proto.CompactTextString(m) }
func (*DownLoadUrlByteResp) ProtoMessage()               {}
func (*DownLoadUrlByteResp) Descriptor() ([]byte, []int) { return fileDescriptorSms, []int{10} }

func (m *DownLoadUrlByteResp) GetMsg() []byte {
	if m != nil {
		return m.Msg
	}
	return nil
}

type Foo struct {
}

func (m *Foo) Reset()                    { *m = Foo{} }
func (m *Foo) String() string            { return proto.CompactTextString(m) }
func (*Foo) ProtoMessage()               {}
func (*Foo) Descriptor() ([]byte, []int) { return fileDescriptorSms, []int{11} }

// post url 接口
type PostUrlDataReq struct {
	Url          string   `protobuf:"bytes,1,req,name=url" json:"url"`
	HeadInfoList [][]byte `protobuf:"bytes,2,rep,name=head_info_list,json=headInfoList" json:"head_info_list,omitempty"`
	DataInfo     []byte   `protobuf:"bytes,3,req,name=data_info,json=dataInfo" json:"data_info"`
}

func (m *PostUrlDataReq) Reset()                    { *m = PostUrlDataReq{} }
func (m *PostUrlDataReq) String() string            { return proto.CompactTextString(m) }
func (*PostUrlDataReq) ProtoMessage()               {}
func (*PostUrlDataReq) Descriptor() ([]byte, []int) { return fileDescriptorSms, []int{12} }

func (m *PostUrlDataReq) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *PostUrlDataReq) GetHeadInfoList() [][]byte {
	if m != nil {
		return m.HeadInfoList
	}
	return nil
}

func (m *PostUrlDataReq) GetDataInfo() []byte {
	if m != nil {
		return m.DataInfo
	}
	return nil
}

type PostUrlDataResp struct {
	RespMsg []byte `protobuf:"bytes,1,req,name=resp_msg,json=respMsg" json:"resp_msg"`
}

func (m *PostUrlDataResp) Reset()                    { *m = PostUrlDataResp{} }
func (m *PostUrlDataResp) String() string            { return proto.CompactTextString(m) }
func (*PostUrlDataResp) ProtoMessage()               {}
func (*PostUrlDataResp) Descriptor() ([]byte, []int) { return fileDescriptorSms, []int{13} }

func (m *PostUrlDataResp) GetRespMsg() []byte {
	if m != nil {
		return m.RespMsg
	}
	return nil
}

type SendVoiceVerifyCodeReq struct {
	Phone      string   `protobuf:"bytes,1,req,name=phone" json:"phone"`
	VerifyCode string   `protobuf:"bytes,2,req,name=verify_code,json=verifyCode" json:"verify_code"`
	Uid        uint32   `protobuf:"varint,3,opt,name=uid" json:"uid"`
	NationCode string   `protobuf:"bytes,5,opt,name=nation_code,json=nationCode" json:"nation_code"`
	VoiceType  uint32   `protobuf:"varint,6,opt,name=voice_type,json=voiceType" json:"voice_type"`
	ParamList  []string `protobuf:"bytes,7,rep,name=param_list,json=paramList" json:"param_list,omitempty"`
	BizId      uint32   `protobuf:"varint,8,opt,name=biz_id,json=bizId" json:"biz_id"`
	ExtBizId   uint32   `protobuf:"varint,9,opt,name=ext_biz_id,json=extBizId" json:"ext_biz_id"`
}

func (m *SendVoiceVerifyCodeReq) Reset()                    { *m = SendVoiceVerifyCodeReq{} }
func (m *SendVoiceVerifyCodeReq) String() string            { return proto.CompactTextString(m) }
func (*SendVoiceVerifyCodeReq) ProtoMessage()               {}
func (*SendVoiceVerifyCodeReq) Descriptor() ([]byte, []int) { return fileDescriptorSms, []int{14} }

func (m *SendVoiceVerifyCodeReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *SendVoiceVerifyCodeReq) GetVerifyCode() string {
	if m != nil {
		return m.VerifyCode
	}
	return ""
}

func (m *SendVoiceVerifyCodeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SendVoiceVerifyCodeReq) GetNationCode() string {
	if m != nil {
		return m.NationCode
	}
	return ""
}

func (m *SendVoiceVerifyCodeReq) GetVoiceType() uint32 {
	if m != nil {
		return m.VoiceType
	}
	return 0
}

func (m *SendVoiceVerifyCodeReq) GetParamList() []string {
	if m != nil {
		return m.ParamList
	}
	return nil
}

func (m *SendVoiceVerifyCodeReq) GetBizId() uint32 {
	if m != nil {
		return m.BizId
	}
	return 0
}

func (m *SendVoiceVerifyCodeReq) GetExtBizId() uint32 {
	if m != nil {
		return m.ExtBizId
	}
	return 0
}

type SendVoiceVerifyCodeResp struct {
}

func (m *SendVoiceVerifyCodeResp) Reset()                    { *m = SendVoiceVerifyCodeResp{} }
func (m *SendVoiceVerifyCodeResp) String() string            { return proto.CompactTextString(m) }
func (*SendVoiceVerifyCodeResp) ProtoMessage()               {}
func (*SendVoiceVerifyCodeResp) Descriptor() ([]byte, []int) { return fileDescriptorSms, []int{15} }

type SendSmsWithProviderReq struct {
	Provider  string   `protobuf:"bytes,1,req,name=provider" json:"provider"`
	Phones    []string `protobuf:"bytes,2,rep,name=phones" json:"phones,omitempty"`
	SmsType   uint32   `protobuf:"varint,3,req,name=sms_type,json=smsType" json:"sms_type"`
	ParamList []string `protobuf:"bytes,4,rep,name=param_list,json=paramList" json:"param_list,omitempty"`
	MarketId  uint32   `protobuf:"varint,5,opt,name=market_id,json=marketId" json:"market_id"`
}

func (m *SendSmsWithProviderReq) Reset()                    { *m = SendSmsWithProviderReq{} }
func (m *SendSmsWithProviderReq) String() string            { return proto.CompactTextString(m) }
func (*SendSmsWithProviderReq) ProtoMessage()               {}
func (*SendSmsWithProviderReq) Descriptor() ([]byte, []int) { return fileDescriptorSms, []int{16} }

func (m *SendSmsWithProviderReq) GetProvider() string {
	if m != nil {
		return m.Provider
	}
	return ""
}

func (m *SendSmsWithProviderReq) GetPhones() []string {
	if m != nil {
		return m.Phones
	}
	return nil
}

func (m *SendSmsWithProviderReq) GetSmsType() uint32 {
	if m != nil {
		return m.SmsType
	}
	return 0
}

func (m *SendSmsWithProviderReq) GetParamList() []string {
	if m != nil {
		return m.ParamList
	}
	return nil
}

func (m *SendSmsWithProviderReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

type SendSmsWithProviderResp struct {
}

func (m *SendSmsWithProviderResp) Reset()                    { *m = SendSmsWithProviderResp{} }
func (m *SendSmsWithProviderResp) String() string            { return proto.CompactTextString(m) }
func (*SendSmsWithProviderResp) ProtoMessage()               {}
func (*SendSmsWithProviderResp) Descriptor() ([]byte, []int) { return fileDescriptorSms, []int{17} }

type RecordVerifyCodePassReq struct {
	VerifyCodeKey string `protobuf:"bytes,1,req,name=verify_code_key,json=verifyCodeKey" json:"verify_code_key"`
	VerifyAt      uint32 `protobuf:"varint,2,opt,name=verify_at,json=verifyAt" json:"verify_at"`
}

func (m *RecordVerifyCodePassReq) Reset()                    { *m = RecordVerifyCodePassReq{} }
func (m *RecordVerifyCodePassReq) String() string            { return proto.CompactTextString(m) }
func (*RecordVerifyCodePassReq) ProtoMessage()               {}
func (*RecordVerifyCodePassReq) Descriptor() ([]byte, []int) { return fileDescriptorSms, []int{18} }

func (m *RecordVerifyCodePassReq) GetVerifyCodeKey() string {
	if m != nil {
		return m.VerifyCodeKey
	}
	return ""
}

func (m *RecordVerifyCodePassReq) GetVerifyAt() uint32 {
	if m != nil {
		return m.VerifyAt
	}
	return 0
}

type RecordVerifyCodePassResp struct {
}

func (m *RecordVerifyCodePassResp) Reset()                    { *m = RecordVerifyCodePassResp{} }
func (m *RecordVerifyCodePassResp) String() string            { return proto.CompactTextString(m) }
func (*RecordVerifyCodePassResp) ProtoMessage()               {}
func (*RecordVerifyCodePassResp) Descriptor() ([]byte, []int) { return fileDescriptorSms, []int{19} }

// 多个phone 对应一个message 或者
// 多个phone 对应 同样数量的message，对应关系发送
type SendMarketingSmsReq struct {
	Phones   []string `protobuf:"bytes,1,rep,name=phones" json:"phones,omitempty"`
	Messages []string `protobuf:"bytes,2,rep,name=messages" json:"messages,omitempty"`
	BizId    uint32   `protobuf:"varint,3,opt,name=biz_id,json=bizId" json:"biz_id"`
	ExtBizId uint32   `protobuf:"varint,4,opt,name=ext_biz_id,json=extBizId" json:"ext_biz_id"`
}

func (m *SendMarketingSmsReq) Reset()                    { *m = SendMarketingSmsReq{} }
func (m *SendMarketingSmsReq) String() string            { return proto.CompactTextString(m) }
func (*SendMarketingSmsReq) ProtoMessage()               {}
func (*SendMarketingSmsReq) Descriptor() ([]byte, []int) { return fileDescriptorSms, []int{20} }

func (m *SendMarketingSmsReq) GetPhones() []string {
	if m != nil {
		return m.Phones
	}
	return nil
}

func (m *SendMarketingSmsReq) GetMessages() []string {
	if m != nil {
		return m.Messages
	}
	return nil
}

func (m *SendMarketingSmsReq) GetBizId() uint32 {
	if m != nil {
		return m.BizId
	}
	return 0
}

func (m *SendMarketingSmsReq) GetExtBizId() uint32 {
	if m != nil {
		return m.ExtBizId
	}
	return 0
}

type SendMarketingPhoneErrResult struct {
	Phone string `protobuf:"bytes,1,opt,name=phone" json:"phone"`
	Code  int32  `protobuf:"varint,2,opt,name=code" json:"code"`
}

func (m *SendMarketingPhoneErrResult) Reset()                    { *m = SendMarketingPhoneErrResult{} }
func (m *SendMarketingPhoneErrResult) String() string            { return proto.CompactTextString(m) }
func (*SendMarketingPhoneErrResult) ProtoMessage()               {}
func (*SendMarketingPhoneErrResult) Descriptor() ([]byte, []int) { return fileDescriptorSms, []int{21} }

func (m *SendMarketingPhoneErrResult) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *SendMarketingPhoneErrResult) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

type SendMarketingSmsResp struct {
	ReqResult int32                          `protobuf:"varint,1,req,name=req_result,json=reqResult" json:"req_result"`
	ErrPhones []*SendMarketingPhoneErrResult `protobuf:"bytes,2,rep,name=err_phones,json=errPhones" json:"err_phones,omitempty"`
}

func (m *SendMarketingSmsResp) Reset()                    { *m = SendMarketingSmsResp{} }
func (m *SendMarketingSmsResp) String() string            { return proto.CompactTextString(m) }
func (*SendMarketingSmsResp) ProtoMessage()               {}
func (*SendMarketingSmsResp) Descriptor() ([]byte, []int) { return fileDescriptorSms, []int{22} }

func (m *SendMarketingSmsResp) GetReqResult() int32 {
	if m != nil {
		return m.ReqResult
	}
	return 0
}

func (m *SendMarketingSmsResp) GetErrPhones() []*SendMarketingPhoneErrResult {
	if m != nil {
		return m.ErrPhones
	}
	return nil
}

func init() {
	proto.RegisterType((*SendSmsReq)(nil), "Sms.SendSmsReq")
	proto.RegisterType((*DirectSendSmsReq)(nil), "Sms.DirectSendSmsReq")
	proto.RegisterType((*CreateVerifyCodeReq)(nil), "Sms.createVerifyCodeReq")
	proto.RegisterType((*CreateVerifyCodeResp)(nil), "Sms.createVerifyCodeResp")
	proto.RegisterType((*ValidateVerifyCodeReq)(nil), "Sms.validateVerifyCodeReq")
	proto.RegisterType((*CheckUrlValidApkUrlReq)(nil), "Sms.checkUrlValidApkUrlReq")
	proto.RegisterType((*CheckUrlValidApkUrlResp)(nil), "Sms.checkUrlValidApkUrlResp")
	proto.RegisterType((*DownLoadUrlReq)(nil), "Sms.downLoadUrlReq")
	proto.RegisterType((*DownLoadUrlResp)(nil), "Sms.downLoadUrlResp")
	proto.RegisterType((*DownLoadUrlByteReq)(nil), "Sms.downLoadUrlByteReq")
	proto.RegisterType((*DownLoadUrlByteResp)(nil), "Sms.downLoadUrlByteResp")
	proto.RegisterType((*Foo)(nil), "Sms.Foo")
	proto.RegisterType((*PostUrlDataReq)(nil), "Sms.PostUrlDataReq")
	proto.RegisterType((*PostUrlDataResp)(nil), "Sms.PostUrlDataResp")
	proto.RegisterType((*SendVoiceVerifyCodeReq)(nil), "Sms.SendVoiceVerifyCodeReq")
	proto.RegisterType((*SendVoiceVerifyCodeResp)(nil), "Sms.SendVoiceVerifyCodeResp")
	proto.RegisterType((*SendSmsWithProviderReq)(nil), "Sms.SendSmsWithProviderReq")
	proto.RegisterType((*SendSmsWithProviderResp)(nil), "Sms.SendSmsWithProviderResp")
	proto.RegisterType((*RecordVerifyCodePassReq)(nil), "Sms.RecordVerifyCodePassReq")
	proto.RegisterType((*RecordVerifyCodePassResp)(nil), "Sms.RecordVerifyCodePassResp")
	proto.RegisterType((*SendMarketingSmsReq)(nil), "Sms.SendMarketingSmsReq")
	proto.RegisterType((*SendMarketingPhoneErrResult)(nil), "Sms.SendMarketingPhoneErrResult")
	proto.RegisterType((*SendMarketingSmsResp)(nil), "Sms.SendMarketingSmsResp")
	proto.RegisterEnum("Sms.ERR_SMS", ERR_SMS_name, ERR_SMS_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for Sms service

type SmsClient interface {
	SendSms(ctx context.Context, in *SendSmsReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 这个接口测试用, 业务不要调用该接口
	SendSmsWithProvider(ctx context.Context, in *SendSmsWithProviderReq, opts ...grpc.CallOption) (*SendSmsWithProviderResp, error)
	CheckUrlValidApkUrl(ctx context.Context, in *CheckUrlValidApkUrlReq, opts ...grpc.CallOption) (*CheckUrlValidApkUrlResp, error)
	DownLoadUrl(ctx context.Context, in *DownLoadUrlReq, opts ...grpc.CallOption) (*DownLoadUrlResp, error)
	DirectSendSms(ctx context.Context, in *DirectSendSmsReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	PostUrlData(ctx context.Context, in *PostUrlDataReq, opts ...grpc.CallOption) (*PostUrlDataResp, error)
	DownLoadUrlByte(ctx context.Context, in *DownLoadUrlByteReq, opts ...grpc.CallOption) (*DownLoadUrlByteResp, error)
	SendVoiceVerifyCode(ctx context.Context, in *SendVoiceVerifyCodeReq, opts ...grpc.CallOption) (*SendVoiceVerifyCodeResp, error)
	RecordVerifyCodePass(ctx context.Context, in *RecordVerifyCodePassReq, opts ...grpc.CallOption) (*RecordVerifyCodePassResp, error)
	SendMarketingSms(ctx context.Context, in *SendMarketingSmsReq, opts ...grpc.CallOption) (*SendMarketingSmsResp, error)
}

type smsClient struct {
	cc *grpc.ClientConn
}

func NewSmsClient(cc *grpc.ClientConn) SmsClient {
	return &smsClient{cc}
}

func (c *smsClient) SendSms(ctx context.Context, in *SendSmsReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/Sms.Sms/SendSms", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsClient) SendSmsWithProvider(ctx context.Context, in *SendSmsWithProviderReq, opts ...grpc.CallOption) (*SendSmsWithProviderResp, error) {
	out := new(SendSmsWithProviderResp)
	err := grpc.Invoke(ctx, "/Sms.Sms/SendSmsWithProvider", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsClient) CheckUrlValidApkUrl(ctx context.Context, in *CheckUrlValidApkUrlReq, opts ...grpc.CallOption) (*CheckUrlValidApkUrlResp, error) {
	out := new(CheckUrlValidApkUrlResp)
	err := grpc.Invoke(ctx, "/Sms.Sms/CheckUrlValidApkUrl", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsClient) DownLoadUrl(ctx context.Context, in *DownLoadUrlReq, opts ...grpc.CallOption) (*DownLoadUrlResp, error) {
	out := new(DownLoadUrlResp)
	err := grpc.Invoke(ctx, "/Sms.Sms/DownLoadUrl", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsClient) DirectSendSms(ctx context.Context, in *DirectSendSmsReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/Sms.Sms/DirectSendSms", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsClient) PostUrlData(ctx context.Context, in *PostUrlDataReq, opts ...grpc.CallOption) (*PostUrlDataResp, error) {
	out := new(PostUrlDataResp)
	err := grpc.Invoke(ctx, "/Sms.Sms/PostUrlData", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsClient) DownLoadUrlByte(ctx context.Context, in *DownLoadUrlByteReq, opts ...grpc.CallOption) (*DownLoadUrlByteResp, error) {
	out := new(DownLoadUrlByteResp)
	err := grpc.Invoke(ctx, "/Sms.Sms/DownLoadUrlByte", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsClient) SendVoiceVerifyCode(ctx context.Context, in *SendVoiceVerifyCodeReq, opts ...grpc.CallOption) (*SendVoiceVerifyCodeResp, error) {
	out := new(SendVoiceVerifyCodeResp)
	err := grpc.Invoke(ctx, "/Sms.Sms/SendVoiceVerifyCode", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsClient) RecordVerifyCodePass(ctx context.Context, in *RecordVerifyCodePassReq, opts ...grpc.CallOption) (*RecordVerifyCodePassResp, error) {
	out := new(RecordVerifyCodePassResp)
	err := grpc.Invoke(ctx, "/Sms.Sms/RecordVerifyCodePass", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsClient) SendMarketingSms(ctx context.Context, in *SendMarketingSmsReq, opts ...grpc.CallOption) (*SendMarketingSmsResp, error) {
	out := new(SendMarketingSmsResp)
	err := grpc.Invoke(ctx, "/Sms.Sms/SendMarketingSms", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for Sms service

type SmsServer interface {
	SendSms(context.Context, *SendSmsReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 这个接口测试用, 业务不要调用该接口
	SendSmsWithProvider(context.Context, *SendSmsWithProviderReq) (*SendSmsWithProviderResp, error)
	CheckUrlValidApkUrl(context.Context, *CheckUrlValidApkUrlReq) (*CheckUrlValidApkUrlResp, error)
	DownLoadUrl(context.Context, *DownLoadUrlReq) (*DownLoadUrlResp, error)
	DirectSendSms(context.Context, *DirectSendSmsReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	PostUrlData(context.Context, *PostUrlDataReq) (*PostUrlDataResp, error)
	DownLoadUrlByte(context.Context, *DownLoadUrlByteReq) (*DownLoadUrlByteResp, error)
	SendVoiceVerifyCode(context.Context, *SendVoiceVerifyCodeReq) (*SendVoiceVerifyCodeResp, error)
	RecordVerifyCodePass(context.Context, *RecordVerifyCodePassReq) (*RecordVerifyCodePassResp, error)
	SendMarketingSms(context.Context, *SendMarketingSmsReq) (*SendMarketingSmsResp, error)
}

func RegisterSmsServer(s *grpc.Server, srv SmsServer) {
	s.RegisterService(&_Sms_serviceDesc, srv)
}

func _Sms_SendSms_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendSmsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).SendSms(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Sms.Sms/SendSms",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).SendSms(ctx, req.(*SendSmsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sms_SendSmsWithProvider_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendSmsWithProviderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).SendSmsWithProvider(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Sms.Sms/SendSmsWithProvider",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).SendSmsWithProvider(ctx, req.(*SendSmsWithProviderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sms_CheckUrlValidApkUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUrlValidApkUrlReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).CheckUrlValidApkUrl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Sms.Sms/CheckUrlValidApkUrl",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).CheckUrlValidApkUrl(ctx, req.(*CheckUrlValidApkUrlReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sms_DownLoadUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DownLoadUrlReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).DownLoadUrl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Sms.Sms/DownLoadUrl",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).DownLoadUrl(ctx, req.(*DownLoadUrlReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sms_DirectSendSms_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DirectSendSmsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).DirectSendSms(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Sms.Sms/DirectSendSms",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).DirectSendSms(ctx, req.(*DirectSendSmsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sms_PostUrlData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PostUrlDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).PostUrlData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Sms.Sms/PostUrlData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).PostUrlData(ctx, req.(*PostUrlDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sms_DownLoadUrlByte_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DownLoadUrlByteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).DownLoadUrlByte(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Sms.Sms/DownLoadUrlByte",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).DownLoadUrlByte(ctx, req.(*DownLoadUrlByteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sms_SendVoiceVerifyCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendVoiceVerifyCodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).SendVoiceVerifyCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Sms.Sms/SendVoiceVerifyCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).SendVoiceVerifyCode(ctx, req.(*SendVoiceVerifyCodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sms_RecordVerifyCodePass_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordVerifyCodePassReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).RecordVerifyCodePass(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Sms.Sms/RecordVerifyCodePass",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).RecordVerifyCodePass(ctx, req.(*RecordVerifyCodePassReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sms_SendMarketingSms_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendMarketingSmsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).SendMarketingSms(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Sms.Sms/SendMarketingSms",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).SendMarketingSms(ctx, req.(*SendMarketingSmsReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Sms_serviceDesc = grpc.ServiceDesc{
	ServiceName: "Sms.Sms",
	HandlerType: (*SmsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendSms",
			Handler:    _Sms_SendSms_Handler,
		},
		{
			MethodName: "SendSmsWithProvider",
			Handler:    _Sms_SendSmsWithProvider_Handler,
		},
		{
			MethodName: "CheckUrlValidApkUrl",
			Handler:    _Sms_CheckUrlValidApkUrl_Handler,
		},
		{
			MethodName: "DownLoadUrl",
			Handler:    _Sms_DownLoadUrl_Handler,
		},
		{
			MethodName: "DirectSendSms",
			Handler:    _Sms_DirectSendSms_Handler,
		},
		{
			MethodName: "PostUrlData",
			Handler:    _Sms_PostUrlData_Handler,
		},
		{
			MethodName: "DownLoadUrlByte",
			Handler:    _Sms_DownLoadUrlByte_Handler,
		},
		{
			MethodName: "SendVoiceVerifyCode",
			Handler:    _Sms_SendVoiceVerifyCode_Handler,
		},
		{
			MethodName: "RecordVerifyCodePass",
			Handler:    _Sms_RecordVerifyCodePass_Handler,
		},
		{
			MethodName: "SendMarketingSms",
			Handler:    _Sms_SendMarketingSms_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/smssvr/sms.proto",
}

func (m *SendSmsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendSmsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSms(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	dAtA[i] = 0x10
	i++
	i = encodeVarintSms(dAtA, i, uint64(m.SmsType))
	if len(m.ParamList) > 0 {
		for _, s := range m.ParamList {
			dAtA[i] = 0x1a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x20
	i++
	if m.WithoutCooldown {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x2a
	i++
	i = encodeVarintSms(dAtA, i, uint64(len(m.VerifyCodeKey)))
	i += copy(dAtA[i:], m.VerifyCodeKey)
	dAtA[i] = 0x32
	i++
	i = encodeVarintSms(dAtA, i, uint64(len(m.VerifyCodeUsage)))
	i += copy(dAtA[i:], m.VerifyCodeUsage)
	dAtA[i] = 0x38
	i++
	i = encodeVarintSms(dAtA, i, uint64(m.MarketId))
	dAtA[i] = 0x40
	i++
	i = encodeVarintSms(dAtA, i, uint64(m.BizId))
	dAtA[i] = 0x48
	i++
	i = encodeVarintSms(dAtA, i, uint64(m.ExtBizId))
	dAtA[i] = 0x52
	i++
	i = encodeVarintSms(dAtA, i, uint64(len(m.RequestId)))
	i += copy(dAtA[i:], m.RequestId)
	dAtA[i] = 0x58
	i++
	i = encodeVarintSms(dAtA, i, uint64(m.CreateTime))
	return i, nil
}

func (m *DirectSendSmsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DirectSendSmsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSms(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	dAtA[i] = 0x12
	i++
	i = encodeVarintSms(dAtA, i, uint64(len(m.Text)))
	i += copy(dAtA[i:], m.Text)
	dAtA[i] = 0x18
	i++
	i = encodeVarintSms(dAtA, i, uint64(m.BizId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintSms(dAtA, i, uint64(m.ExtBizId))
	return i, nil
}

func (m *CreateVerifyCodeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateVerifyCodeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSms(dAtA, i, uint64(len(m.Key)))
	i += copy(dAtA[i:], m.Key)
	dAtA[i] = 0x10
	i++
	i = encodeVarintSms(dAtA, i, uint64(m.CodeLen))
	return i, nil
}

func (m *CreateVerifyCodeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateVerifyCodeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSms(dAtA, i, uint64(len(m.Key)))
	i += copy(dAtA[i:], m.Key)
	dAtA[i] = 0x12
	i++
	i = encodeVarintSms(dAtA, i, uint64(len(m.VerifyCode)))
	i += copy(dAtA[i:], m.VerifyCode)
	dAtA[i] = 0x18
	i++
	i = encodeVarintSms(dAtA, i, uint64(m.ExpireTime))
	return i, nil
}

func (m *ValidateVerifyCodeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ValidateVerifyCodeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSms(dAtA, i, uint64(len(m.Key)))
	i += copy(dAtA[i:], m.Key)
	dAtA[i] = 0x12
	i++
	i = encodeVarintSms(dAtA, i, uint64(len(m.VerifyCode)))
	i += copy(dAtA[i:], m.VerifyCode)
	return i, nil
}

func (m *CheckUrlValidApkUrlReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUrlValidApkUrlReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSms(dAtA, i, uint64(len(m.Url)))
	i += copy(dAtA[i:], m.Url)
	return i, nil
}

func (m *CheckUrlValidApkUrlResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUrlValidApkUrlResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSms(dAtA, i, uint64(m.ContentLength))
	return i, nil
}

func (m *DownLoadUrlReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DownLoadUrlReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSms(dAtA, i, uint64(len(m.Url)))
	i += copy(dAtA[i:], m.Url)
	dAtA[i] = 0x10
	i++
	i = encodeVarintSms(dAtA, i, uint64(m.Timeout))
	return i, nil
}

func (m *DownLoadUrlResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DownLoadUrlResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSms(dAtA, i, uint64(len(m.Msg)))
	i += copy(dAtA[i:], m.Msg)
	return i, nil
}

func (m *DownLoadUrlByteReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DownLoadUrlByteReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSms(dAtA, i, uint64(len(m.Url)))
	i += copy(dAtA[i:], m.Url)
	dAtA[i] = 0x10
	i++
	i = encodeVarintSms(dAtA, i, uint64(m.Timeout))
	return i, nil
}

func (m *DownLoadUrlByteResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DownLoadUrlByteResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Msg != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSms(dAtA, i, uint64(len(m.Msg)))
		i += copy(dAtA[i:], m.Msg)
	}
	return i, nil
}

func (m *Foo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Foo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *PostUrlDataReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PostUrlDataReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSms(dAtA, i, uint64(len(m.Url)))
	i += copy(dAtA[i:], m.Url)
	if len(m.HeadInfoList) > 0 {
		for _, b := range m.HeadInfoList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintSms(dAtA, i, uint64(len(b)))
			i += copy(dAtA[i:], b)
		}
	}
	if m.DataInfo != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintSms(dAtA, i, uint64(len(m.DataInfo)))
		i += copy(dAtA[i:], m.DataInfo)
	}
	return i, nil
}

func (m *PostUrlDataResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PostUrlDataResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.RespMsg != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSms(dAtA, i, uint64(len(m.RespMsg)))
		i += copy(dAtA[i:], m.RespMsg)
	}
	return i, nil
}

func (m *SendVoiceVerifyCodeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendVoiceVerifyCodeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSms(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	dAtA[i] = 0x12
	i++
	i = encodeVarintSms(dAtA, i, uint64(len(m.VerifyCode)))
	i += copy(dAtA[i:], m.VerifyCode)
	dAtA[i] = 0x18
	i++
	i = encodeVarintSms(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintSms(dAtA, i, uint64(len(m.NationCode)))
	i += copy(dAtA[i:], m.NationCode)
	dAtA[i] = 0x30
	i++
	i = encodeVarintSms(dAtA, i, uint64(m.VoiceType))
	if len(m.ParamList) > 0 {
		for _, s := range m.ParamList {
			dAtA[i] = 0x3a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x40
	i++
	i = encodeVarintSms(dAtA, i, uint64(m.BizId))
	dAtA[i] = 0x48
	i++
	i = encodeVarintSms(dAtA, i, uint64(m.ExtBizId))
	return i, nil
}

func (m *SendVoiceVerifyCodeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendVoiceVerifyCodeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *SendSmsWithProviderReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendSmsWithProviderReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSms(dAtA, i, uint64(len(m.Provider)))
	i += copy(dAtA[i:], m.Provider)
	if len(m.Phones) > 0 {
		for _, s := range m.Phones {
			dAtA[i] = 0x12
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintSms(dAtA, i, uint64(m.SmsType))
	if len(m.ParamList) > 0 {
		for _, s := range m.ParamList {
			dAtA[i] = 0x22
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x28
	i++
	i = encodeVarintSms(dAtA, i, uint64(m.MarketId))
	return i, nil
}

func (m *SendSmsWithProviderResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendSmsWithProviderResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *RecordVerifyCodePassReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecordVerifyCodePassReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSms(dAtA, i, uint64(len(m.VerifyCodeKey)))
	i += copy(dAtA[i:], m.VerifyCodeKey)
	dAtA[i] = 0x10
	i++
	i = encodeVarintSms(dAtA, i, uint64(m.VerifyAt))
	return i, nil
}

func (m *RecordVerifyCodePassResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecordVerifyCodePassResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *SendMarketingSmsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendMarketingSmsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Phones) > 0 {
		for _, s := range m.Phones {
			dAtA[i] = 0xa
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if len(m.Messages) > 0 {
		for _, s := range m.Messages {
			dAtA[i] = 0x12
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintSms(dAtA, i, uint64(m.BizId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintSms(dAtA, i, uint64(m.ExtBizId))
	return i, nil
}

func (m *SendMarketingPhoneErrResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendMarketingPhoneErrResult) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSms(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	dAtA[i] = 0x10
	i++
	i = encodeVarintSms(dAtA, i, uint64(m.Code))
	return i, nil
}

func (m *SendMarketingSmsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendMarketingSmsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSms(dAtA, i, uint64(m.ReqResult))
	if len(m.ErrPhones) > 0 {
		for _, msg := range m.ErrPhones {
			dAtA[i] = 0x12
			i++
			i = encodeVarintSms(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func encodeFixed64Sms(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Sms(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintSms(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *SendSmsReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Phone)
	n += 1 + l + sovSms(uint64(l))
	n += 1 + sovSms(uint64(m.SmsType))
	if len(m.ParamList) > 0 {
		for _, s := range m.ParamList {
			l = len(s)
			n += 1 + l + sovSms(uint64(l))
		}
	}
	n += 2
	l = len(m.VerifyCodeKey)
	n += 1 + l + sovSms(uint64(l))
	l = len(m.VerifyCodeUsage)
	n += 1 + l + sovSms(uint64(l))
	n += 1 + sovSms(uint64(m.MarketId))
	n += 1 + sovSms(uint64(m.BizId))
	n += 1 + sovSms(uint64(m.ExtBizId))
	l = len(m.RequestId)
	n += 1 + l + sovSms(uint64(l))
	n += 1 + sovSms(uint64(m.CreateTime))
	return n
}

func (m *DirectSendSmsReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Phone)
	n += 1 + l + sovSms(uint64(l))
	l = len(m.Text)
	n += 1 + l + sovSms(uint64(l))
	n += 1 + sovSms(uint64(m.BizId))
	n += 1 + sovSms(uint64(m.ExtBizId))
	return n
}

func (m *CreateVerifyCodeReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Key)
	n += 1 + l + sovSms(uint64(l))
	n += 1 + sovSms(uint64(m.CodeLen))
	return n
}

func (m *CreateVerifyCodeResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.Key)
	n += 1 + l + sovSms(uint64(l))
	l = len(m.VerifyCode)
	n += 1 + l + sovSms(uint64(l))
	n += 1 + sovSms(uint64(m.ExpireTime))
	return n
}

func (m *ValidateVerifyCodeReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Key)
	n += 1 + l + sovSms(uint64(l))
	l = len(m.VerifyCode)
	n += 1 + l + sovSms(uint64(l))
	return n
}

func (m *CheckUrlValidApkUrlReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Url)
	n += 1 + l + sovSms(uint64(l))
	return n
}

func (m *CheckUrlValidApkUrlResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSms(uint64(m.ContentLength))
	return n
}

func (m *DownLoadUrlReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Url)
	n += 1 + l + sovSms(uint64(l))
	n += 1 + sovSms(uint64(m.Timeout))
	return n
}

func (m *DownLoadUrlResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.Msg)
	n += 1 + l + sovSms(uint64(l))
	return n
}

func (m *DownLoadUrlByteReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Url)
	n += 1 + l + sovSms(uint64(l))
	n += 1 + sovSms(uint64(m.Timeout))
	return n
}

func (m *DownLoadUrlByteResp) Size() (n int) {
	var l int
	_ = l
	if m.Msg != nil {
		l = len(m.Msg)
		n += 1 + l + sovSms(uint64(l))
	}
	return n
}

func (m *Foo) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *PostUrlDataReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Url)
	n += 1 + l + sovSms(uint64(l))
	if len(m.HeadInfoList) > 0 {
		for _, b := range m.HeadInfoList {
			l = len(b)
			n += 1 + l + sovSms(uint64(l))
		}
	}
	if m.DataInfo != nil {
		l = len(m.DataInfo)
		n += 1 + l + sovSms(uint64(l))
	}
	return n
}

func (m *PostUrlDataResp) Size() (n int) {
	var l int
	_ = l
	if m.RespMsg != nil {
		l = len(m.RespMsg)
		n += 1 + l + sovSms(uint64(l))
	}
	return n
}

func (m *SendVoiceVerifyCodeReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Phone)
	n += 1 + l + sovSms(uint64(l))
	l = len(m.VerifyCode)
	n += 1 + l + sovSms(uint64(l))
	n += 1 + sovSms(uint64(m.Uid))
	l = len(m.NationCode)
	n += 1 + l + sovSms(uint64(l))
	n += 1 + sovSms(uint64(m.VoiceType))
	if len(m.ParamList) > 0 {
		for _, s := range m.ParamList {
			l = len(s)
			n += 1 + l + sovSms(uint64(l))
		}
	}
	n += 1 + sovSms(uint64(m.BizId))
	n += 1 + sovSms(uint64(m.ExtBizId))
	return n
}

func (m *SendVoiceVerifyCodeResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *SendSmsWithProviderReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Provider)
	n += 1 + l + sovSms(uint64(l))
	if len(m.Phones) > 0 {
		for _, s := range m.Phones {
			l = len(s)
			n += 1 + l + sovSms(uint64(l))
		}
	}
	n += 1 + sovSms(uint64(m.SmsType))
	if len(m.ParamList) > 0 {
		for _, s := range m.ParamList {
			l = len(s)
			n += 1 + l + sovSms(uint64(l))
		}
	}
	n += 1 + sovSms(uint64(m.MarketId))
	return n
}

func (m *SendSmsWithProviderResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *RecordVerifyCodePassReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.VerifyCodeKey)
	n += 1 + l + sovSms(uint64(l))
	n += 1 + sovSms(uint64(m.VerifyAt))
	return n
}

func (m *RecordVerifyCodePassResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *SendMarketingSmsReq) Size() (n int) {
	var l int
	_ = l
	if len(m.Phones) > 0 {
		for _, s := range m.Phones {
			l = len(s)
			n += 1 + l + sovSms(uint64(l))
		}
	}
	if len(m.Messages) > 0 {
		for _, s := range m.Messages {
			l = len(s)
			n += 1 + l + sovSms(uint64(l))
		}
	}
	n += 1 + sovSms(uint64(m.BizId))
	n += 1 + sovSms(uint64(m.ExtBizId))
	return n
}

func (m *SendMarketingPhoneErrResult) Size() (n int) {
	var l int
	_ = l
	l = len(m.Phone)
	n += 1 + l + sovSms(uint64(l))
	n += 1 + sovSms(uint64(m.Code))
	return n
}

func (m *SendMarketingSmsResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSms(uint64(m.ReqResult))
	if len(m.ErrPhones) > 0 {
		for _, e := range m.ErrPhones {
			l = e.Size()
			n += 1 + l + sovSms(uint64(l))
		}
	}
	return n
}

func sovSms(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozSms(x uint64) (n int) {
	return sovSms(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *SendSmsReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSms
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendSmsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendSmsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSms
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SmsType", wireType)
			}
			m.SmsType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SmsType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ParamList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSms
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ParamList = append(m.ParamList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field WithoutCooldown", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.WithoutCooldown = bool(v != 0)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VerifyCodeKey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSms
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.VerifyCodeKey = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VerifyCodeUsage", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSms
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.VerifyCodeUsage = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MarketId", wireType)
			}
			m.MarketId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MarketId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BizId", wireType)
			}
			m.BizId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BizId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtBizId", wireType)
			}
			m.ExtBizId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExtBizId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RequestId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSms
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RequestId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateTime", wireType)
			}
			m.CreateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSms(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSms
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("phone")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("sms_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DirectSendSmsReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSms
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DirectSendSmsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DirectSendSmsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSms
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Text", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSms
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Text = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BizId", wireType)
			}
			m.BizId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BizId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtBizId", wireType)
			}
			m.ExtBizId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExtBizId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSms(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSms
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("phone")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("text")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateVerifyCodeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSms
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: createVerifyCodeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: createVerifyCodeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSms
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CodeLen", wireType)
			}
			m.CodeLen = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CodeLen |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSms(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSms
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("key")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateVerifyCodeResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSms
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: createVerifyCodeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: createVerifyCodeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSms
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VerifyCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSms
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.VerifyCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpireTime", wireType)
			}
			m.ExpireTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpireTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipSms(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSms
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("key")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("verify_code")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("expire_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ValidateVerifyCodeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSms
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: validateVerifyCodeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: validateVerifyCodeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSms
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VerifyCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSms
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.VerifyCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipSms(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSms
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("key")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("verify_code")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckUrlValidApkUrlReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSms
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: checkUrlValidApkUrlReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: checkUrlValidApkUrlReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSms
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Url = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSms(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSms
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("url")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckUrlValidApkUrlResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSms
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: checkUrlValidApkUrlResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: checkUrlValidApkUrlResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContentLength", wireType)
			}
			m.ContentLength = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ContentLength |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSms(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSms
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("content_length")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DownLoadUrlReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSms
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: downLoadUrlReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: downLoadUrlReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSms
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Url = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timeout", wireType)
			}
			m.Timeout = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timeout |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSms(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSms
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("url")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DownLoadUrlResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSms
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: downLoadUrlResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: downLoadUrlResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSms
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Msg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSms(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSms
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("msg")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DownLoadUrlByteReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSms
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: downLoadUrlByteReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: downLoadUrlByteReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSms
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Url = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timeout", wireType)
			}
			m.Timeout = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timeout |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSms(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSms
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("url")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DownLoadUrlByteResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSms
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: downLoadUrlByteResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: downLoadUrlByteResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthSms
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Msg = append(m.Msg[:0], dAtA[iNdEx:postIndex]...)
			if m.Msg == nil {
				m.Msg = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSms(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSms
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("msg")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *Foo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSms
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: Foo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: Foo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipSms(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSms
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PostUrlDataReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSms
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PostUrlDataReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PostUrlDataReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSms
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Url = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HeadInfoList", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthSms
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.HeadInfoList = append(m.HeadInfoList, make([]byte, postIndex-iNdEx))
			copy(m.HeadInfoList[len(m.HeadInfoList)-1], dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DataInfo", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthSms
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DataInfo = append(m.DataInfo[:0], dAtA[iNdEx:postIndex]...)
			if m.DataInfo == nil {
				m.DataInfo = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipSms(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSms
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("url")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("data_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PostUrlDataResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSms
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PostUrlDataResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PostUrlDataResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RespMsg", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthSms
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RespMsg = append(m.RespMsg[:0], dAtA[iNdEx:postIndex]...)
			if m.RespMsg == nil {
				m.RespMsg = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSms(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSms
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("resp_msg")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendVoiceVerifyCodeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSms
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendVoiceVerifyCodeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendVoiceVerifyCodeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSms
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VerifyCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSms
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.VerifyCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NationCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSms
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.NationCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VoiceType", wireType)
			}
			m.VoiceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VoiceType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ParamList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSms
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ParamList = append(m.ParamList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BizId", wireType)
			}
			m.BizId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BizId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtBizId", wireType)
			}
			m.ExtBizId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExtBizId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSms(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSms
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("phone")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("verify_code")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendVoiceVerifyCodeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSms
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendVoiceVerifyCodeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendVoiceVerifyCodeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipSms(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSms
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendSmsWithProviderReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSms
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendSmsWithProviderReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendSmsWithProviderReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Provider", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSms
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Provider = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phones", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSms
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phones = append(m.Phones, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SmsType", wireType)
			}
			m.SmsType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SmsType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ParamList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSms
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ParamList = append(m.ParamList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MarketId", wireType)
			}
			m.MarketId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MarketId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSms(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSms
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("provider")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("sms_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendSmsWithProviderResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSms
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendSmsWithProviderResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendSmsWithProviderResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipSms(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSms
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecordVerifyCodePassReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSms
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecordVerifyCodePassReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecordVerifyCodePassReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VerifyCodeKey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSms
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.VerifyCodeKey = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VerifyAt", wireType)
			}
			m.VerifyAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VerifyAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSms(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSms
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("verify_code_key")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecordVerifyCodePassResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSms
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecordVerifyCodePassResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecordVerifyCodePassResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipSms(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSms
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendMarketingSmsReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSms
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendMarketingSmsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendMarketingSmsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phones", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSms
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phones = append(m.Phones, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Messages", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSms
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Messages = append(m.Messages, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BizId", wireType)
			}
			m.BizId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BizId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtBizId", wireType)
			}
			m.ExtBizId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExtBizId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSms(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSms
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendMarketingPhoneErrResult) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSms
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendMarketingPhoneErrResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendMarketingPhoneErrResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSms
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			m.Code = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Code |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSms(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSms
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendMarketingSmsResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSms
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendMarketingSmsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendMarketingSmsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReqResult", wireType)
			}
			m.ReqResult = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReqResult |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ErrPhones", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSms
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSms
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ErrPhones = append(m.ErrPhones, &SendMarketingPhoneErrResult{})
			if err := m.ErrPhones[len(m.ErrPhones)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSms(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSms
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("req_result")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipSms(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowSms
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowSms
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowSms
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthSms
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowSms
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipSms(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthSms = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowSms   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/smssvr/sms.proto", fileDescriptorSms) }

var fileDescriptorSms = []byte{
	// 1684 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x56, 0xcd, 0x6e, 0xe3, 0xc8,
	0x11, 0x1e, 0x9a, 0xfe, 0x91, 0xca, 0x3f, 0x52, 0x68, 0x8f, 0xad, 0xd1, 0xec, 0x7a, 0x7b, 0x38,
	0x33, 0x1b, 0x67, 0x77, 0x65, 0x2f, 0xe6, 0xa8, 0x28, 0x0a, 0x64, 0x8b, 0x8e, 0x85, 0x91, 0x65,
	0x85, 0x92, 0x9d, 0x0c, 0x16, 0x0b, 0x82, 0x23, 0xf6, 0x58, 0x8c, 0x44, 0x91, 0xc3, 0x6e, 0x79,
	0xad, 0x45, 0x0e, 0x39, 0x26, 0x7b, 0x58, 0x04, 0xc9, 0x03, 0xe4, 0x32, 0x40, 0x90, 0x6b, 0x80,
	0xbc, 0x41, 0x0e, 0x7b, 0xcc, 0x13, 0x04, 0xbb, 0x93, 0x3f, 0xe7, 0xe7, 0x1d, 0xb2, 0xe8, 0x66,
	0x4b, 0x22, 0x29, 0xda, 0x63, 0x60, 0x7c, 0xb1, 0x58, 0xf5, 0x75, 0x55, 0x7d, 0xd5, 0xd5, 0x55,
	0x05, 0x1b, 0xc4, 0xef, 0xec, 0x11, 0x87, 0x90, 0x0b, 0x9f, 0xfd, 0xdb, 0xf5, 0x7c, 0x97, 0xba,
	0x8a, 0xdc, 0x72, 0x48, 0xfe, 0x51, 0xc7, 0x75, 0x1c, 0x77, 0xb0, 0x47, 0xfb, 0x17, 0x9e, 0xdd,
	0xe9, 0xf5, 0xf1, 0x1e, 0xe9, 0x3d, 0x1f, 0xda, 0x7d, 0x6a, 0x0f, 0xe8, 0xc8, 0xc3, 0x01, 0x54,
	0xfd, 0x83, 0x0c, 0xd0, 0xc2, 0x03, 0xab, 0xe5, 0x10, 0x1d, 0xbf, 0x54, 0xf2, 0xb0, 0xe0, 0x75,
	0xdd, 0x01, 0xce, 0x49, 0x68, 0x6e, 0x27, 0xbd, 0x3f, 0xff, 0xd5, 0x5f, 0xdf, 0xbb, 0xa3, 0x07,
	0x22, 0xe5, 0x3d, 0x48, 0x11, 0x87, 0x18, 0xec, 0x70, 0x6e, 0x0e, 0xcd, 0xed, 0xac, 0x0a, 0xf5,
	0x12, 0x71, 0x48, 0x7b, 0xe4, 0x61, 0xe5, 0x5d, 0x00, 0xcf, 0xf4, 0x4d, 0xc7, 0xe8, 0xdb, 0x84,
	0xe6, 0x64, 0x24, 0xef, 0xa4, 0xf5, 0x34, 0x97, 0xd4, 0x6d, 0x42, 0x95, 0x3d, 0xc8, 0x7e, 0x66,
	0xd3, 0xae, 0x3b, 0xa4, 0x46, 0xc7, 0x75, 0xfb, 0x96, 0xfb, 0xd9, 0x20, 0x37, 0x8f, 0xa4, 0x9d,
	0x94, 0xb0, 0x93, 0x11, 0xda, 0x03, 0xa1, 0x54, 0x3e, 0x82, 0xcc, 0x05, 0xf6, 0xed, 0x17, 0x23,
	0xa3, 0xe3, 0x5a, 0xd8, 0xe8, 0xe1, 0x51, 0x6e, 0x01, 0x49, 0x93, 0xb0, 0x56, 0x03, 0xe5, 0x81,
	0x6b, 0xe1, 0xa7, 0x78, 0xa4, 0x7c, 0x0c, 0xdf, 0x09, 0xa3, 0x87, 0xc4, 0x3c, 0xc7, 0xb9, 0xc5,
	0x10, 0x3e, 0x33, 0xc5, 0x9f, 0x32, 0xa5, 0xf2, 0x00, 0xd2, 0x8e, 0xe9, 0xf7, 0x30, 0x35, 0x6c,
	0x2b, 0xb7, 0x84, 0xa4, 0x09, 0xa3, 0x54, 0x20, 0xae, 0x59, 0xca, 0x7d, 0x58, 0x7c, 0x6e, 0x7f,
	0xce, 0xf4, 0xa9, 0x90, 0x7e, 0xe1, 0xb9, 0xfd, 0x79, 0xcd, 0x52, 0x54, 0x00, 0x7c, 0x49, 0x0d,
	0x01, 0x48, 0x87, 0x0d, 0xe0, 0x4b, 0xba, 0xcf, 0x31, 0x0f, 0x01, 0x7c, 0xfc, 0x72, 0x88, 0x09,
	0x77, 0x02, 0xa1, 0x70, 0xd2, 0x42, 0x5e, 0xb3, 0x94, 0xc7, 0xb0, 0xdc, 0xf1, 0xb1, 0x49, 0xb1,
	0x41, 0x6d, 0x07, 0xe7, 0x96, 0x91, 0xb4, 0x33, 0x2f, 0x50, 0x10, 0x28, 0xda, 0xb6, 0x83, 0xd5,
	0x5f, 0x49, 0x90, 0xad, 0xda, 0x3e, 0xee, 0xd0, 0x5b, 0xde, 0x58, 0x0e, 0xe6, 0x29, 0xbe, 0xa4,
	0xfc, 0xb6, 0xc6, 0x2a, 0x2e, 0x09, 0xf1, 0x92, 0xdf, 0xc4, 0x6b, 0x3e, 0x89, 0x97, 0xda, 0x80,
	0xf5, 0x20, 0xb2, 0xb3, 0x49, 0x52, 0x59, 0x34, 0x9b, 0x20, 0xb3, 0x6b, 0x0a, 0xc7, 0xc2, 0x04,
	0xac, 0x76, 0xf8, 0xad, 0xf4, 0xf1, 0x20, 0x37, 0x17, 0x32, 0xb8, 0xc4, 0xa4, 0x75, 0x3c, 0x50,
	0x7f, 0x0e, 0x1b, 0xb3, 0xf6, 0x88, 0x77, 0xad, 0xc1, 0xc7, 0xb0, 0x1c, 0xba, 0xed, 0x08, 0x43,
	0x98, 0xde, 0x33, 0x83, 0xe1, 0x4b, 0xcf, 0xf6, 0x45, 0x66, 0xe5, 0x50, 0xd9, 0x42, 0xa0, 0xe0,
	0x99, 0x3d, 0x83, 0xbb, 0x17, 0x66, 0xdf, 0xb6, 0x6e, 0xcd, 0xe7, 0x76, 0xee, 0xd5, 0x8f, 0x61,
	0xb3, 0xd3, 0xc5, 0x9d, 0xde, 0xa9, 0xdf, 0x3f, 0x63, 0xf6, 0x2b, 0x1e, 0xfb, 0x29, 0x0c, 0x0f,
	0xfd, 0x7e, 0xd4, 0xf0, 0xd0, 0xef, 0xab, 0x87, 0xb0, 0x95, 0x78, 0x82, 0x78, 0xca, 0x87, 0xb0,
	0xd6, 0x71, 0x07, 0x14, 0x0f, 0x28, 0x4b, 0xe3, 0x39, 0xed, 0xf2, 0xd3, 0x63, 0x3a, 0xab, 0x42,
	0x57, 0xe7, 0x2a, 0xf5, 0x08, 0xd6, 0xd8, 0x1b, 0xaa, 0xbb, 0xa6, 0x75, 0xb3, 0x47, 0x65, 0x1b,
	0x96, 0x58, 0x6e, 0xdc, 0x21, 0x8d, 0xde, 0x8c, 0x10, 0xaa, 0xdf, 0x83, 0x4c, 0xc4, 0x52, 0x70,
	0x29, 0x0e, 0x39, 0x8f, 0x9a, 0x72, 0xc8, 0xb9, 0x5a, 0x07, 0x25, 0x04, 0xdd, 0x1f, 0x51, 0xfc,
	0x36, 0x8e, 0x0b, 0xb0, 0x3e, 0x63, 0x2d, 0xea, 0x7c, 0x25, 0xec, 0x7c, 0x01, 0xe4, 0x43, 0xd7,
	0x55, 0x5f, 0xc2, 0x5a, 0xd3, 0x25, 0xf4, 0xd4, 0xef, 0x57, 0x4d, 0x6a, 0xde, 0xe4, 0xff, 0x11,
	0xac, 0x75, 0xb1, 0x69, 0x19, 0xf6, 0xe0, 0x85, 0x1b, 0xb4, 0xac, 0x39, 0x24, 0xef, 0xac, 0xe8,
	0x2b, 0x4c, 0x5a, 0x1b, 0xbc, 0x70, 0x79, 0xd7, 0x7a, 0x00, 0x69, 0xcb, 0xa4, 0x26, 0x47, 0xf1,
	0xfa, 0x19, 0x3b, 0x4d, 0x31, 0x31, 0x83, 0xa9, 0x4f, 0x20, 0x13, 0x71, 0x49, 0x3c, 0x56, 0xef,
	0x3e, 0x26, 0x9e, 0x11, 0x8f, 0x74, 0x89, 0x49, 0x8f, 0xc9, 0xb9, 0xfa, 0xbb, 0x39, 0xd8, 0x64,
	0xaf, 0xf8, 0xcc, 0xb5, 0x3b, 0xb1, 0x9a, 0xbb, 0xe9, 0x45, 0xdf, 0xb2, 0xec, 0x19, 0xe5, 0xd8,
	0xdb, 0x66, 0x02, 0x76, 0x7c, 0x60, 0x52, 0xdb, 0x1d, 0x04, 0xc7, 0xc3, 0xdd, 0x14, 0x02, 0x05,
	0x3f, 0xfe, 0x10, 0xe0, 0x82, 0xc5, 0x15, 0xf4, 0xfa, 0xc5, 0x90, 0x95, 0x34, 0x97, 0x27, 0x74,
	0xfb, 0xa5, 0x78, 0xb7, 0x7f, 0xdb, 0xce, 0xa9, 0xde, 0x83, 0xad, 0xc4, 0x04, 0x11, 0x4f, 0xfd,
	0x93, 0x14, 0x24, 0xaf, 0xe5, 0x90, 0x9f, 0xd8, 0xb4, 0xdb, 0xf4, 0xdd, 0x0b, 0xdb, 0xc2, 0x3e,
	0x4b, 0x1e, 0x82, 0x94, 0x27, 0x3e, 0x23, 0xf9, 0x9b, 0x48, 0x95, 0x4d, 0x58, 0xe4, 0xb9, 0x24,
	0xfc, 0xba, 0xd3, 0xba, 0xf8, 0x8a, 0x8c, 0x37, 0xf9, 0xcd, 0xe3, 0x6d, 0x3e, 0x4e, 0x38, 0x32,
	0x4d, 0x16, 0x92, 0xa6, 0xc9, 0x98, 0xd2, 0x4c, 0xd8, 0xc4, 0x53, 0x7f, 0x06, 0x5b, 0x3a, 0xee,
	0xb8, 0xbe, 0x35, 0xa5, 0xda, 0x34, 0x09, 0xef, 0xf0, 0x09, 0x63, 0x30, 0xcc, 0x2c, 0x36, 0x06,
	0x1f, 0x40, 0x5a, 0xa0, 0xcd, 0xe8, 0xbb, 0x4a, 0x05, 0xe2, 0x0a, 0x55, 0xf3, 0x90, 0x4b, 0xf6,
	0x45, 0x3c, 0xf5, 0x0b, 0x09, 0xd6, 0x59, 0x8c, 0xc7, 0x3c, 0x66, 0x7b, 0x70, 0x2e, 0xc6, 0xcc,
	0x34, 0x6b, 0x52, 0x24, 0x6b, 0x79, 0x48, 0x39, 0x98, 0xb0, 0x71, 0x3a, 0xce, 0xe7, 0xe4, 0xfb,
	0xed, 0x87, 0x4c, 0x0b, 0xee, 0x47, 0x62, 0x69, 0x32, 0x9f, 0x9a, 0xcf, 0x32, 0x36, 0xec, 0xd3,
	0xf0, 0x43, 0x91, 0x12, 0x46, 0x9f, 0x78, 0x21, 0xd2, 0xce, 0xc2, 0x78, 0xf4, 0x31, 0x09, 0x9b,
	0x34, 0xb3, 0x04, 0x89, 0x27, 0x26, 0xb5, 0xe1, 0x73, 0xdb, 0x3c, 0xc3, 0x0b, 0xa1, 0x49, 0x2d,
	0x5c, 0xfe, 0x10, 0x00, 0xfb, 0xbe, 0x11, 0x2a, 0xa0, 0xe5, 0x27, 0x68, 0xb7, 0xe5, 0x90, 0xdd,
	0x1b, 0x02, 0xd5, 0xd3, 0xd8, 0xf7, 0xb9, 0x88, 0x7c, 0xf0, 0x67, 0x19, 0x96, 0x34, 0x5d, 0x37,
	0x5a, 0xc7, 0x2d, 0xa5, 0x00, 0x48, 0xfc, 0x34, 0xce, 0x34, 0xfd, 0xb0, 0xf6, 0xec, 0xe0, 0xa4,
	0xaa, 0x19, 0x67, 0x95, 0x7a, 0xad, 0x5a, 0x69, 0x6b, 0xc6, 0x61, 0xa5, 0x56, 0xcf, 0xfe, 0xef,
	0xff, 0xe2, 0x4f, 0x52, 0x1e, 0xc2, 0xe6, 0x18, 0xde, 0x3e, 0x39, 0x31, 0x8e, 0x2b, 0x8d, 0x67,
	0x46, 0xf3, 0xe8, 0xa4, 0xa1, 0x65, 0xff, 0x3b, 0x05, 0xa9, 0x70, 0x77, 0x0c, 0x6a, 0x69, 0x8d,
	0x2a, 0xff, 0x71, 0xa8, 0x6b, 0x3f, 0xce, 0xfe, 0x67, 0x8a, 0x79, 0x00, 0x1b, 0x13, 0x43, 0xcf,
	0x9a, 0x9a, 0x51, 0x6b, 0x70, 0x9f, 0xd9, 0x7f, 0x4f, 0x21, 0x8f, 0x21, 0x37, 0x86, 0x34, 0x4e,
	0x0c, 0xad, 0x71, 0x72, 0xfa, 0xa3, 0x23, 0xa3, 0x59, 0xd1, 0x2b, 0xc7, 0xad, 0xec, 0xd5, 0x14,
	0xb6, 0x07, 0xea, 0x8c, 0xb7, 0xf6, 0x91, 0xae, 0xb5, 0x8e, 0x8e, 0x4e, 0xea, 0x55, 0x43, 0xfb,
	0xe9, 0x81, 0xa6, 0x55, 0xb3, 0xff, 0x4a, 0x0c, 0x4f, 0x78, 0x15, 0x14, 0xfe, 0x39, 0xc5, 0x7c,
	0x17, 0xf2, 0x71, 0xcc, 0x99, 0xa6, 0xd7, 0x0e, 0x79, 0x7a, 0xb2, 0xff, 0x98, 0x02, 0x1f, 0xc1,
	0xd6, 0x34, 0xc8, 0xb6, 0xd1, 0x3a, 0x6d, 0x36, 0x4f, 0xf4, 0xb6, 0x51, 0x6b, 0x34, 0xb2, 0x7f,
	0x9f, 0xa2, 0x3e, 0x84, 0x6d, 0x86, 0x0a, 0x0c, 0x55, 0x9a, 0x4f, 0x8d, 0x53, 0xbd, 0x6e, 0x1c,
	0x54, 0x1a, 0xec, 0x88, 0xae, 0x55, 0x0e, 0x8e, 0xb2, 0xdf, 0x4c, 0xc1, 0xef, 0xc3, 0xbd, 0x59,
	0x30, 0x43, 0x56, 0x9a, 0x4f, 0xb3, 0x5f, 0x4f, 0x70, 0x4f, 0x7e, 0x0b, 0xc0, 0x96, 0x6c, 0xe5,
	0x4b, 0x09, 0x96, 0xc4, 0x93, 0x56, 0x32, 0x93, 0x3a, 0x08, 0xde, 0x4c, 0xfe, 0x9d, 0xdd, 0xc9,
	0xee, 0xbd, 0xdb, 0x7a, 0xba, 0x1f, 0xec, 0xde, 0x9a, 0xe3, 0xd1, 0x91, 0xd1, 0xdc, 0x57, 0xdb,
	0xbf, 0x78, 0x75, 0x25, 0x4b, 0x5f, 0xbc, 0xba, 0x92, 0x53, 0xb4, 0x48, 0x8a, 0x5e, 0xd1, 0x29,
	0xfe, 0xe6, 0xd5, 0x95, 0xfc, 0x83, 0x02, 0x45, 0x25, 0x8a, 0xfb, 0x98, 0xd7, 0x58, 0x19, 0x15,
	0x08, 0x2a, 0x11, 0x87, 0x20, 0xd6, 0x97, 0xca, 0xa8, 0xe0, 0xa1, 0x12, 0x6f, 0x39, 0x88, 0x35,
	0xa1, 0x32, 0xfa, 0xa4, 0xe0, 0xa0, 0xd2, 0xa4, 0xed, 0x94, 0x3f, 0x55, 0xbe, 0x11, 0xef, 0x37,
	0xd6, 0x63, 0x94, 0xfb, 0xe1, 0xe0, 0x62, 0x4d, 0x33, 0xff, 0xce, 0xf5, 0x4a, 0xe2, 0xa9, 0x5f,
	0x4a, 0x2c, 0x52, 0x99, 0x45, 0xba, 0x42, 0x8a, 0xb4, 0xe8, 0x14, 0xbd, 0xa2, 0x59, 0x1c, 0xf2,
	0x68, 0x09, 0x0b, 0xaf, 0x85, 0xfd, 0x0b, 0xbb, 0x83, 0xd1, 0xb8, 0xc5, 0x96, 0x11, 0xe3, 0xd0,
	0x0e, 0x71, 0x70, 0x50, 0xe9, 0xd8, 0xb5, 0x86, 0x7d, 0x1c, 0xa2, 0xd1, 0x8c, 0xd0, 0x18, 0xa2,
	0xd2, 0xd0, 0xb6, 0x3e, 0x42, 0x43, 0x82, 0x2d, 0xf4, 0xc2, 0xf5, 0x51, 0xd7, 0x24, 0xdd, 0x1e,
	0x1e, 0x95, 0x3f, 0x45, 0x9f, 0x14, 0xcc, 0x28, 0xc7, 0x1e, 0xac, 0x1f, 0xcc, 0xee, 0x48, 0x82,
	0x62, 0xf2, 0xbe, 0x25, 0x28, 0x5e, 0xb3, 0x5a, 0xa9, 0xf7, 0x18, 0xc3, 0x15, 0xc6, 0x70, 0xae,
	0xc7, 0x79, 0xa5, 0x0a, 0x3d, 0x54, 0x1a, 0xfa, 0xfd, 0xb2, 0xd2, 0x82, 0xe5, 0xea, 0x74, 0x0b,
	0x51, 0xd6, 0xb9, 0x9d, 0xe8, 0x6a, 0x95, 0xdf, 0x98, 0x15, 0x8e, 0x8d, 0xae, 0x26, 0x1a, 0xf5,
	0x60, 0x35, 0xb2, 0xc8, 0x2b, 0x77, 0xb9, 0x85, 0xf8, 0x72, 0xff, 0x86, 0x0a, 0xfa, 0x80, 0x39,
	0x58, 0x63, 0x0e, 0xe6, 0xa9, 0xa8, 0x9e, 0xad, 0x78, 0xf5, 0xb0, 0xf2, 0x20, 0xe7, 0x65, 0x65,
	0x08, 0xcb, 0xa1, 0x1d, 0x45, 0xd0, 0x88, 0x2e, 0x4a, 0x82, 0x46, 0x6c, 0x95, 0x51, 0xbf, 0xcf,
	0xbc, 0x64, 0x98, 0x97, 0xc5, 0x5e, 0xf1, 0xb2, 0x48, 0xb8, 0x9f, 0xf7, 0xc7, 0x54, 0x50, 0xe1,
	0x12, 0x95, 0xd8, 0xc2, 0x84, 0xd8, 0x82, 0x14, 0x94, 0x2b, 0x5b, 0x8c, 0x82, 0x4f, 0xc5, 0x80,
	0x4c, 0x35, 0xba, 0xc3, 0x29, 0x5b, 0xf1, 0x64, 0x89, 0x3d, 0x31, 0x9f, 0x4b, 0x56, 0x8c, 0x33,
	0x99, 0x4d, 0xcc, 0xe4, 0xef, 0x45, 0xbd, 0xc7, 0xd6, 0x84, 0x50, 0xbd, 0xcf, 0x6e, 0x58, 0xa1,
	0x7a, 0x4f, 0xda, 0x2e, 0x4e, 0x99, 0xb7, 0x0d, 0xf1, 0x30, 0x71, 0xd1, 0x2d, 0x7a, 0xdc, 0x67,
	0x39, 0x9e, 0x5a, 0x8c, 0x4a, 0xc1, 0x48, 0x65, 0x63, 0xa5, 0x8c, 0x0a, 0x2e, 0x2a, 0xf1, 0xed,
	0x28, 0xf9, 0xa9, 0x2a, 0x16, 0x6c, 0x24, 0x4d, 0x5d, 0x25, 0x08, 0xe6, 0x9a, 0xe1, 0x9f, 0x7f,
	0xf7, 0x06, 0x2d, 0xf1, 0xd4, 0x0c, 0x8b, 0xf5, 0x2e, 0x8b, 0xf5, 0x0e, 0x8b, 0xf1, 0x8e, 0x42,
	0x20, 0x1b, 0x9f, 0x6e, 0x4a, 0x6e, 0x76, 0x40, 0x89, 0xfa, 0xba, 0x77, 0x8d, 0x86, 0x78, 0x41,
	0x71, 0x6d, 0xce, 0x16, 0x57, 0xb8, 0xb0, 0x82, 0x41, 0x5f, 0xce, 0x2f, 0xfe, 0xf2, 0xd5, 0x95,
	0xfc, 0x47, 0x67, 0x3f, 0xfb, 0xd5, 0xeb, 0x6d, 0xe9, 0x2f, 0xaf, 0xb7, 0xa5, 0xaf, 0x5f, 0x6f,
	0x4b, 0xbf, 0xfe, 0xdb, 0xf6, 0x9d, 0x6f, 0x03, 0x00, 0x00, 0xff, 0xff, 0x2d, 0xdc, 0xe4, 0xf8,
	0xa0, 0x10, 0x00, 0x00,
}
