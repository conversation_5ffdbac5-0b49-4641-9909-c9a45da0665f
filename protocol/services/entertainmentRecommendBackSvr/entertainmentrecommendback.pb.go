// Code generated by protoc-gen-go. DO NOT EDIT.
// source: src/entertainmentRecommendBackSvr/entertainmentrecommendback.proto

package entertainmentRecommendBack

/*
namespace
*/

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ADV_TYPE int32

const (
	ADV_TYPE_AT_CHANNEL_FUNNY    ADV_TYPE = 0
	ADV_TYPE_AT_CHANNEL_HOMEPAGE ADV_TYPE = 1
)

var ADV_TYPE_name = map[int32]string{
	0: "AT_CHANNEL_FUNNY",
	1: "AT_CHANNEL_HOMEPAGE",
}
var ADV_TYPE_value = map[string]int32{
	"AT_CHANNEL_FUNNY":    0,
	"AT_CHANNEL_HOMEPAGE": 1,
}

func (x ADV_TYPE) Enum() *ADV_TYPE {
	p := new(ADV_TYPE)
	*p = x
	return p
}
func (x ADV_TYPE) String() string {
	return proto.EnumName(ADV_TYPE_name, int32(x))
}
func (x *ADV_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ADV_TYPE_value, data, "ADV_TYPE")
	if err != nil {
		return err
	}
	*x = ADV_TYPE(value)
	return nil
}
func (ADV_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{0}
}

type UserCategory int32

const (
	UserCategory_NewUser_Type        UserCategory = 0
	UserCategory_OldUser_Type        UserCategory = 1
	UserCategory_BothUser_Type       UserCategory = 2
	UserCategory_QuickEnterUser_Type UserCategory = 3
)

var UserCategory_name = map[int32]string{
	0: "NewUser_Type",
	1: "OldUser_Type",
	2: "BothUser_Type",
	3: "QuickEnterUser_Type",
}
var UserCategory_value = map[string]int32{
	"NewUser_Type":        0,
	"OldUser_Type":        1,
	"BothUser_Type":       2,
	"QuickEnterUser_Type": 3,
}

func (x UserCategory) Enum() *UserCategory {
	p := new(UserCategory)
	*p = x
	return p
}
func (x UserCategory) String() string {
	return proto.EnumName(UserCategory_name, int32(x))
}
func (x *UserCategory) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(UserCategory_value, data, "UserCategory")
	if err != nil {
		return err
	}
	*x = UserCategory(value)
	return nil
}
func (UserCategory) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{1}
}

type ChannelCategory int32

const (
	ChannelCategory_Activity_BIG   ChannelCategory = 0
	ChannelCategory_Activity_SAMLL ChannelCategory = 1
	ChannelCategory_Used_Enter     ChannelCategory = 2
	ChannelCategory_Normal_Level   ChannelCategory = 3
	ChannelCategory_Hot_Channel    ChannelCategory = 4
)

var ChannelCategory_name = map[int32]string{
	0: "Activity_BIG",
	1: "Activity_SAMLL",
	2: "Used_Enter",
	3: "Normal_Level",
	4: "Hot_Channel",
}
var ChannelCategory_value = map[string]int32{
	"Activity_BIG":   0,
	"Activity_SAMLL": 1,
	"Used_Enter":     2,
	"Normal_Level":   3,
	"Hot_Channel":    4,
}

func (x ChannelCategory) Enum() *ChannelCategory {
	p := new(ChannelCategory)
	*p = x
	return p
}
func (x ChannelCategory) String() string {
	return proto.EnumName(ChannelCategory_name, int32(x))
}
func (x *ChannelCategory) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ChannelCategory_value, data, "ChannelCategory")
	if err != nil {
		return err
	}
	*x = ChannelCategory(value)
	return nil
}
func (ChannelCategory) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{2}
}

// 活动房间状态
type ActivityChStatus int32

const (
	ActivityChStatus_ACT_STATUS_DEFAULT    ActivityChStatus = 0
	ActivityChStatus_ACT_STATUS_NOW_VALID  ActivityChStatus = 1
	ActivityChStatus_ACT_STATUS_SOON_VALID ActivityChStatus = 2
	ActivityChStatus_ACT_STATUS_EXPIRE     ActivityChStatus = 3
)

var ActivityChStatus_name = map[int32]string{
	0: "ACT_STATUS_DEFAULT",
	1: "ACT_STATUS_NOW_VALID",
	2: "ACT_STATUS_SOON_VALID",
	3: "ACT_STATUS_EXPIRE",
}
var ActivityChStatus_value = map[string]int32{
	"ACT_STATUS_DEFAULT":    0,
	"ACT_STATUS_NOW_VALID":  1,
	"ACT_STATUS_SOON_VALID": 2,
	"ACT_STATUS_EXPIRE":     3,
}

func (x ActivityChStatus) Enum() *ActivityChStatus {
	p := new(ActivityChStatus)
	*p = x
	return p
}
func (x ActivityChStatus) String() string {
	return proto.EnumName(ActivityChStatus_name, int32(x))
}
func (x *ActivityChStatus) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ActivityChStatus_value, data, "ActivityChStatus")
	if err != nil {
		return err
	}
	*x = ActivityChStatus(value)
	return nil
}
func (ActivityChStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{3}
}

type ChannelLevel int32

const (
	ChannelLevel_Channel_Invalid ChannelLevel = 0
	ChannelLevel_Channel_Level_S ChannelLevel = 1
	ChannelLevel_Channel_Level_A ChannelLevel = 2
	ChannelLevel_Channel_Level_B ChannelLevel = 3
	ChannelLevel_Channel_Level_C ChannelLevel = 4
	ChannelLevel_Channel_Level_D ChannelLevel = 5
	ChannelLevel_Channel_Level_E ChannelLevel = 6
	ChannelLevel_Channel_Level_F ChannelLevel = 7
	ChannelLevel_Channel_Level_G ChannelLevel = 8
)

var ChannelLevel_name = map[int32]string{
	0: "Channel_Invalid",
	1: "Channel_Level_S",
	2: "Channel_Level_A",
	3: "Channel_Level_B",
	4: "Channel_Level_C",
	5: "Channel_Level_D",
	6: "Channel_Level_E",
	7: "Channel_Level_F",
	8: "Channel_Level_G",
}
var ChannelLevel_value = map[string]int32{
	"Channel_Invalid": 0,
	"Channel_Level_S": 1,
	"Channel_Level_A": 2,
	"Channel_Level_B": 3,
	"Channel_Level_C": 4,
	"Channel_Level_D": 5,
	"Channel_Level_E": 6,
	"Channel_Level_F": 7,
	"Channel_Level_G": 8,
}

func (x ChannelLevel) Enum() *ChannelLevel {
	p := new(ChannelLevel)
	*p = x
	return p
}
func (x ChannelLevel) String() string {
	return proto.EnumName(ChannelLevel_name, int32(x))
}
func (x *ChannelLevel) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ChannelLevel_value, data, "ChannelLevel")
	if err != nil {
		return err
	}
	*x = ChannelLevel(value)
	return nil
}
func (ChannelLevel) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{4}
}

type ELivePrepareConfType int32

const (
	ELivePrepareConfType_EPREPARE_CONF_PERMANENT ELivePrepareConfType = 0
	ELivePrepareConfType_EPREPARE_CONF_CONSTANT  ELivePrepareConfType = 1
)

var ELivePrepareConfType_name = map[int32]string{
	0: "EPREPARE_CONF_PERMANENT",
	1: "EPREPARE_CONF_CONSTANT",
}
var ELivePrepareConfType_value = map[string]int32{
	"EPREPARE_CONF_PERMANENT": 0,
	"EPREPARE_CONF_CONSTANT":  1,
}

func (x ELivePrepareConfType) Enum() *ELivePrepareConfType {
	p := new(ELivePrepareConfType)
	*p = x
	return p
}
func (x ELivePrepareConfType) String() string {
	return proto.EnumName(ELivePrepareConfType_name, int32(x))
}
func (x *ELivePrepareConfType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ELivePrepareConfType_value, data, "ELivePrepareConfType")
	if err != nil {
		return err
	}
	*x = ELivePrepareConfType(value)
	return nil
}
func (ELivePrepareConfType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{5}
}

type SubTagQueryType int32

const (
	SubTagQueryType_Sub_Query_All  SubTagQueryType = 0
	SubTagQueryType_Sub_Query_No   SubTagQueryType = 1
	SubTagQueryType_Sub_Query_Have SubTagQueryType = 2
)

var SubTagQueryType_name = map[int32]string{
	0: "Sub_Query_All",
	1: "Sub_Query_No",
	2: "Sub_Query_Have",
}
var SubTagQueryType_value = map[string]int32{
	"Sub_Query_All":  0,
	"Sub_Query_No":   1,
	"Sub_Query_Have": 2,
}

func (x SubTagQueryType) Enum() *SubTagQueryType {
	p := new(SubTagQueryType)
	*p = x
	return p
}
func (x SubTagQueryType) String() string {
	return proto.EnumName(SubTagQueryType_name, int32(x))
}
func (x *SubTagQueryType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(SubTagQueryType_value, data, "SubTagQueryType")
	if err != nil {
		return err
	}
	*x = SubTagQueryType(value)
	return nil
}
func (SubTagQueryType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{6}
}

type TimeSection struct {
	BeginTime            *uint32  `protobuf:"varint,1,req,name=begin_time,json=beginTime" json:"begin_time,omitempty"`
	EndTime              *uint32  `protobuf:"varint,2,req,name=end_time,json=endTime" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TimeSection) Reset()         { *m = TimeSection{} }
func (m *TimeSection) String() string { return proto.CompactTextString(m) }
func (*TimeSection) ProtoMessage()    {}
func (*TimeSection) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{0}
}
func (m *TimeSection) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TimeSection.Unmarshal(m, b)
}
func (m *TimeSection) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TimeSection.Marshal(b, m, deterministic)
}
func (dst *TimeSection) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TimeSection.Merge(dst, src)
}
func (m *TimeSection) XXX_Size() int {
	return xxx_messageInfo_TimeSection.Size(m)
}
func (m *TimeSection) XXX_DiscardUnknown() {
	xxx_messageInfo_TimeSection.DiscardUnknown(m)
}

var xxx_messageInfo_TimeSection proto.InternalMessageInfo

func (m *TimeSection) GetBeginTime() uint32 {
	if m != nil && m.BeginTime != nil {
		return *m.BeginTime
	}
	return 0
}

func (m *TimeSection) GetEndTime() uint32 {
	if m != nil && m.EndTime != nil {
		return *m.EndTime
	}
	return 0
}

//
type ChannelCommonInfo struct {
	ChannelId            *uint32        `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	TagId                *uint32        `protobuf:"varint,2,req,name=tag_id,json=tagId" json:"tag_id,omitempty"`
	SubTag               *string        `protobuf:"bytes,3,opt,name=sub_tag,json=subTag" json:"sub_tag,omitempty"`
	Sections             []*TimeSection `protobuf:"bytes,4,rep,name=sections" json:"sections,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *ChannelCommonInfo) Reset()         { *m = ChannelCommonInfo{} }
func (m *ChannelCommonInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelCommonInfo) ProtoMessage()    {}
func (*ChannelCommonInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{1}
}
func (m *ChannelCommonInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelCommonInfo.Unmarshal(m, b)
}
func (m *ChannelCommonInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelCommonInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelCommonInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelCommonInfo.Merge(dst, src)
}
func (m *ChannelCommonInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelCommonInfo.Size(m)
}
func (m *ChannelCommonInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelCommonInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelCommonInfo proto.InternalMessageInfo

func (m *ChannelCommonInfo) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *ChannelCommonInfo) GetTagId() uint32 {
	if m != nil && m.TagId != nil {
		return *m.TagId
	}
	return 0
}

func (m *ChannelCommonInfo) GetSubTag() string {
	if m != nil && m.SubTag != nil {
		return *m.SubTag
	}
	return ""
}

func (m *ChannelCommonInfo) GetSections() []*TimeSection {
	if m != nil {
		return m.Sections
	}
	return nil
}

type ChannelCacheInfo struct {
	NewLevel             *uint32  `protobuf:"varint,1,req,name=new_level,json=newLevel" json:"new_level,omitempty"`
	OldLevel             *uint32  `protobuf:"varint,2,req,name=old_level,json=oldLevel" json:"old_level,omitempty"`
	QuickLevel           *uint32  `protobuf:"varint,3,opt,name=quick_level,json=quickLevel" json:"quick_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelCacheInfo) Reset()         { *m = ChannelCacheInfo{} }
func (m *ChannelCacheInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelCacheInfo) ProtoMessage()    {}
func (*ChannelCacheInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{2}
}
func (m *ChannelCacheInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelCacheInfo.Unmarshal(m, b)
}
func (m *ChannelCacheInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelCacheInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelCacheInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelCacheInfo.Merge(dst, src)
}
func (m *ChannelCacheInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelCacheInfo.Size(m)
}
func (m *ChannelCacheInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelCacheInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelCacheInfo proto.InternalMessageInfo

func (m *ChannelCacheInfo) GetNewLevel() uint32 {
	if m != nil && m.NewLevel != nil {
		return *m.NewLevel
	}
	return 0
}

func (m *ChannelCacheInfo) GetOldLevel() uint32 {
	if m != nil && m.OldLevel != nil {
		return *m.OldLevel
	}
	return 0
}

func (m *ChannelCacheInfo) GetQuickLevel() uint32 {
	if m != nil && m.QuickLevel != nil {
		return *m.QuickLevel
	}
	return 0
}

// 预备库信息
type PrepareChannelInfo struct {
	ChannelId            *uint32        `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	TagId                *uint32        `protobuf:"varint,2,req,name=tag_id,json=tagId" json:"tag_id,omitempty"`
	NewLevel             *uint32        `protobuf:"varint,3,req,name=new_level,json=newLevel" json:"new_level,omitempty"`
	OldLevel             *uint32        `protobuf:"varint,4,req,name=old_level,json=oldLevel" json:"old_level,omitempty"`
	NewStartTime         *uint32        `protobuf:"varint,5,req,name=new_start_time,json=newStartTime" json:"new_start_time,omitempty"`
	OldStartTime         *uint32        `protobuf:"varint,6,req,name=old_start_time,json=oldStartTime" json:"old_start_time,omitempty"`
	SubTag               *string        `protobuf:"bytes,7,opt,name=sub_tag,json=subTag" json:"sub_tag,omitempty"`
	ModifyTime           *uint32        `protobuf:"varint,8,opt,name=modify_time,json=modifyTime" json:"modify_time,omitempty"`
	Sections             []*TimeSection `protobuf:"bytes,9,rep,name=sections" json:"sections,omitempty"`
	QuickLevel           *uint32        `protobuf:"varint,10,opt,name=quick_level,json=quickLevel" json:"quick_level,omitempty"`
	QuickStartTime       *uint32        `protobuf:"varint,11,opt,name=quick_start_time,json=quickStartTime" json:"quick_start_time,omitempty"`
	ChannelType          *uint32        `protobuf:"varint,12,opt,name=channel_type,json=channelType" json:"channel_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *PrepareChannelInfo) Reset()         { *m = PrepareChannelInfo{} }
func (m *PrepareChannelInfo) String() string { return proto.CompactTextString(m) }
func (*PrepareChannelInfo) ProtoMessage()    {}
func (*PrepareChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{3}
}
func (m *PrepareChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PrepareChannelInfo.Unmarshal(m, b)
}
func (m *PrepareChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PrepareChannelInfo.Marshal(b, m, deterministic)
}
func (dst *PrepareChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PrepareChannelInfo.Merge(dst, src)
}
func (m *PrepareChannelInfo) XXX_Size() int {
	return xxx_messageInfo_PrepareChannelInfo.Size(m)
}
func (m *PrepareChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PrepareChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PrepareChannelInfo proto.InternalMessageInfo

func (m *PrepareChannelInfo) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *PrepareChannelInfo) GetTagId() uint32 {
	if m != nil && m.TagId != nil {
		return *m.TagId
	}
	return 0
}

func (m *PrepareChannelInfo) GetNewLevel() uint32 {
	if m != nil && m.NewLevel != nil {
		return *m.NewLevel
	}
	return 0
}

func (m *PrepareChannelInfo) GetOldLevel() uint32 {
	if m != nil && m.OldLevel != nil {
		return *m.OldLevel
	}
	return 0
}

func (m *PrepareChannelInfo) GetNewStartTime() uint32 {
	if m != nil && m.NewStartTime != nil {
		return *m.NewStartTime
	}
	return 0
}

func (m *PrepareChannelInfo) GetOldStartTime() uint32 {
	if m != nil && m.OldStartTime != nil {
		return *m.OldStartTime
	}
	return 0
}

func (m *PrepareChannelInfo) GetSubTag() string {
	if m != nil && m.SubTag != nil {
		return *m.SubTag
	}
	return ""
}

func (m *PrepareChannelInfo) GetModifyTime() uint32 {
	if m != nil && m.ModifyTime != nil {
		return *m.ModifyTime
	}
	return 0
}

func (m *PrepareChannelInfo) GetSections() []*TimeSection {
	if m != nil {
		return m.Sections
	}
	return nil
}

func (m *PrepareChannelInfo) GetQuickLevel() uint32 {
	if m != nil && m.QuickLevel != nil {
		return *m.QuickLevel
	}
	return 0
}

func (m *PrepareChannelInfo) GetQuickStartTime() uint32 {
	if m != nil && m.QuickStartTime != nil {
		return *m.QuickStartTime
	}
	return 0
}

func (m *PrepareChannelInfo) GetChannelType() uint32 {
	if m != nil && m.ChannelType != nil {
		return *m.ChannelType
	}
	return 0
}

// 主持人信息
type McSimpleInfo struct {
	Uid                  *uint32  `protobuf:"varint,1,req,name=uid" json:"uid,omitempty"`
	Account              *string  `protobuf:"bytes,2,opt,name=account" json:"account,omitempty"`
	NickName             *string  `protobuf:"bytes,3,opt,name=nick_name,json=nickName" json:"nick_name,omitempty"`
	HeadImg              *string  `protobuf:"bytes,4,opt,name=head_img,json=headImg" json:"head_img,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *McSimpleInfo) Reset()         { *m = McSimpleInfo{} }
func (m *McSimpleInfo) String() string { return proto.CompactTextString(m) }
func (*McSimpleInfo) ProtoMessage()    {}
func (*McSimpleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{4}
}
func (m *McSimpleInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_McSimpleInfo.Unmarshal(m, b)
}
func (m *McSimpleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_McSimpleInfo.Marshal(b, m, deterministic)
}
func (dst *McSimpleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_McSimpleInfo.Merge(dst, src)
}
func (m *McSimpleInfo) XXX_Size() int {
	return xxx_messageInfo_McSimpleInfo.Size(m)
}
func (m *McSimpleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_McSimpleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_McSimpleInfo proto.InternalMessageInfo

func (m *McSimpleInfo) GetUid() uint32 {
	if m != nil && m.Uid != nil {
		return *m.Uid
	}
	return 0
}

func (m *McSimpleInfo) GetAccount() string {
	if m != nil && m.Account != nil {
		return *m.Account
	}
	return ""
}

func (m *McSimpleInfo) GetNickName() string {
	if m != nil && m.NickName != nil {
		return *m.NickName
	}
	return ""
}

func (m *McSimpleInfo) GetHeadImg() string {
	if m != nil && m.HeadImg != nil {
		return *m.HeadImg
	}
	return ""
}

// 推荐流房间信息
type ChannelRecommendSimpleInfo struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	Category             *uint32  `protobuf:"varint,2,req,name=category" json:"category,omitempty"`
	TagId                *uint32  `protobuf:"varint,3,opt,name=tag_id,json=tagId" json:"tag_id,omitempty"`
	SubTag               *string  `protobuf:"bytes,4,opt,name=sub_tag,json=subTag" json:"sub_tag,omitempty"`
	ChannelLevel         *uint32  `protobuf:"varint,5,opt,name=channel_level,json=channelLevel" json:"channel_level,omitempty"`
	Score                *uint32  `protobuf:"varint,6,opt,name=score" json:"score,omitempty"`
	TagName              *string  `protobuf:"bytes,7,opt,name=tag_name,json=tagName" json:"tag_name,omitempty"`
	Label                *string  `protobuf:"bytes,8,opt,name=label" json:"label,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelRecommendSimpleInfo) Reset()         { *m = ChannelRecommendSimpleInfo{} }
func (m *ChannelRecommendSimpleInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelRecommendSimpleInfo) ProtoMessage()    {}
func (*ChannelRecommendSimpleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{5}
}
func (m *ChannelRecommendSimpleInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelRecommendSimpleInfo.Unmarshal(m, b)
}
func (m *ChannelRecommendSimpleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelRecommendSimpleInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelRecommendSimpleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelRecommendSimpleInfo.Merge(dst, src)
}
func (m *ChannelRecommendSimpleInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelRecommendSimpleInfo.Size(m)
}
func (m *ChannelRecommendSimpleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelRecommendSimpleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelRecommendSimpleInfo proto.InternalMessageInfo

func (m *ChannelRecommendSimpleInfo) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *ChannelRecommendSimpleInfo) GetCategory() uint32 {
	if m != nil && m.Category != nil {
		return *m.Category
	}
	return 0
}

func (m *ChannelRecommendSimpleInfo) GetTagId() uint32 {
	if m != nil && m.TagId != nil {
		return *m.TagId
	}
	return 0
}

func (m *ChannelRecommendSimpleInfo) GetSubTag() string {
	if m != nil && m.SubTag != nil {
		return *m.SubTag
	}
	return ""
}

func (m *ChannelRecommendSimpleInfo) GetChannelLevel() uint32 {
	if m != nil && m.ChannelLevel != nil {
		return *m.ChannelLevel
	}
	return 0
}

func (m *ChannelRecommendSimpleInfo) GetScore() uint32 {
	if m != nil && m.Score != nil {
		return *m.Score
	}
	return 0
}

func (m *ChannelRecommendSimpleInfo) GetTagName() string {
	if m != nil && m.TagName != nil {
		return *m.TagName
	}
	return ""
}

func (m *ChannelRecommendSimpleInfo) GetLabel() string {
	if m != nil && m.Label != nil {
		return *m.Label
	}
	return ""
}

type ActivityChannelInfo struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	BeginTime            *uint32  `protobuf:"varint,2,req,name=begin_time,json=beginTime" json:"begin_time,omitempty"`
	EndTime              *uint32  `protobuf:"varint,3,req,name=end_time,json=endTime" json:"end_time,omitempty"`
	Category             *uint32  `protobuf:"varint,4,req,name=category" json:"category,omitempty"`
	TagId                *uint32  `protobuf:"varint,5,opt,name=tag_id,json=tagId" json:"tag_id,omitempty"`
	SubTag               *string  `protobuf:"bytes,6,opt,name=sub_tag,json=subTag" json:"sub_tag,omitempty"`
	Id                   *uint32  `protobuf:"varint,7,opt,name=id" json:"id,omitempty"`
	ActStatus            *uint32  `protobuf:"varint,8,opt,name=act_status,json=actStatus" json:"act_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ActivityChannelInfo) Reset()         { *m = ActivityChannelInfo{} }
func (m *ActivityChannelInfo) String() string { return proto.CompactTextString(m) }
func (*ActivityChannelInfo) ProtoMessage()    {}
func (*ActivityChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{6}
}
func (m *ActivityChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActivityChannelInfo.Unmarshal(m, b)
}
func (m *ActivityChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActivityChannelInfo.Marshal(b, m, deterministic)
}
func (dst *ActivityChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActivityChannelInfo.Merge(dst, src)
}
func (m *ActivityChannelInfo) XXX_Size() int {
	return xxx_messageInfo_ActivityChannelInfo.Size(m)
}
func (m *ActivityChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ActivityChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ActivityChannelInfo proto.InternalMessageInfo

func (m *ActivityChannelInfo) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *ActivityChannelInfo) GetBeginTime() uint32 {
	if m != nil && m.BeginTime != nil {
		return *m.BeginTime
	}
	return 0
}

func (m *ActivityChannelInfo) GetEndTime() uint32 {
	if m != nil && m.EndTime != nil {
		return *m.EndTime
	}
	return 0
}

func (m *ActivityChannelInfo) GetCategory() uint32 {
	if m != nil && m.Category != nil {
		return *m.Category
	}
	return 0
}

func (m *ActivityChannelInfo) GetTagId() uint32 {
	if m != nil && m.TagId != nil {
		return *m.TagId
	}
	return 0
}

func (m *ActivityChannelInfo) GetSubTag() string {
	if m != nil && m.SubTag != nil {
		return *m.SubTag
	}
	return ""
}

func (m *ActivityChannelInfo) GetId() uint32 {
	if m != nil && m.Id != nil {
		return *m.Id
	}
	return 0
}

func (m *ActivityChannelInfo) GetActStatus() uint32 {
	if m != nil && m.ActStatus != nil {
		return *m.ActStatus
	}
	return 0
}

// 备选库相关
type AddPrepareChannelReq struct {
	PrepareChannelList   []*PrepareChannelInfo `protobuf:"bytes,1,rep,name=prepare_channel_list,json=prepareChannelList" json:"prepare_channel_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *AddPrepareChannelReq) Reset()         { *m = AddPrepareChannelReq{} }
func (m *AddPrepareChannelReq) String() string { return proto.CompactTextString(m) }
func (*AddPrepareChannelReq) ProtoMessage()    {}
func (*AddPrepareChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{7}
}
func (m *AddPrepareChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPrepareChannelReq.Unmarshal(m, b)
}
func (m *AddPrepareChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPrepareChannelReq.Marshal(b, m, deterministic)
}
func (dst *AddPrepareChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPrepareChannelReq.Merge(dst, src)
}
func (m *AddPrepareChannelReq) XXX_Size() int {
	return xxx_messageInfo_AddPrepareChannelReq.Size(m)
}
func (m *AddPrepareChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPrepareChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddPrepareChannelReq proto.InternalMessageInfo

func (m *AddPrepareChannelReq) GetPrepareChannelList() []*PrepareChannelInfo {
	if m != nil {
		return m.PrepareChannelList
	}
	return nil
}

type AddPrepareChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddPrepareChannelResp) Reset()         { *m = AddPrepareChannelResp{} }
func (m *AddPrepareChannelResp) String() string { return proto.CompactTextString(m) }
func (*AddPrepareChannelResp) ProtoMessage()    {}
func (*AddPrepareChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{8}
}
func (m *AddPrepareChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPrepareChannelResp.Unmarshal(m, b)
}
func (m *AddPrepareChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPrepareChannelResp.Marshal(b, m, deterministic)
}
func (dst *AddPrepareChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPrepareChannelResp.Merge(dst, src)
}
func (m *AddPrepareChannelResp) XXX_Size() int {
	return xxx_messageInfo_AddPrepareChannelResp.Size(m)
}
func (m *AddPrepareChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPrepareChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddPrepareChannelResp proto.InternalMessageInfo

type GetPrepareChannelByChannelIDReq struct {
	ChannelList          []uint32 `protobuf:"varint,1,rep,name=channel_list,json=channelList" json:"channel_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPrepareChannelByChannelIDReq) Reset()         { *m = GetPrepareChannelByChannelIDReq{} }
func (m *GetPrepareChannelByChannelIDReq) String() string { return proto.CompactTextString(m) }
func (*GetPrepareChannelByChannelIDReq) ProtoMessage()    {}
func (*GetPrepareChannelByChannelIDReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{9}
}
func (m *GetPrepareChannelByChannelIDReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPrepareChannelByChannelIDReq.Unmarshal(m, b)
}
func (m *GetPrepareChannelByChannelIDReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPrepareChannelByChannelIDReq.Marshal(b, m, deterministic)
}
func (dst *GetPrepareChannelByChannelIDReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPrepareChannelByChannelIDReq.Merge(dst, src)
}
func (m *GetPrepareChannelByChannelIDReq) XXX_Size() int {
	return xxx_messageInfo_GetPrepareChannelByChannelIDReq.Size(m)
}
func (m *GetPrepareChannelByChannelIDReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPrepareChannelByChannelIDReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPrepareChannelByChannelIDReq proto.InternalMessageInfo

func (m *GetPrepareChannelByChannelIDReq) GetChannelList() []uint32 {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

type GetPrepareChannelByChannelIDResp struct {
	PrepareChannelList   []*PrepareChannelInfo `protobuf:"bytes,1,rep,name=prepare_channel_list,json=prepareChannelList" json:"prepare_channel_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetPrepareChannelByChannelIDResp) Reset()         { *m = GetPrepareChannelByChannelIDResp{} }
func (m *GetPrepareChannelByChannelIDResp) String() string { return proto.CompactTextString(m) }
func (*GetPrepareChannelByChannelIDResp) ProtoMessage()    {}
func (*GetPrepareChannelByChannelIDResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{10}
}
func (m *GetPrepareChannelByChannelIDResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPrepareChannelByChannelIDResp.Unmarshal(m, b)
}
func (m *GetPrepareChannelByChannelIDResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPrepareChannelByChannelIDResp.Marshal(b, m, deterministic)
}
func (dst *GetPrepareChannelByChannelIDResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPrepareChannelByChannelIDResp.Merge(dst, src)
}
func (m *GetPrepareChannelByChannelIDResp) XXX_Size() int {
	return xxx_messageInfo_GetPrepareChannelByChannelIDResp.Size(m)
}
func (m *GetPrepareChannelByChannelIDResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPrepareChannelByChannelIDResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPrepareChannelByChannelIDResp proto.InternalMessageInfo

func (m *GetPrepareChannelByChannelIDResp) GetPrepareChannelList() []*PrepareChannelInfo {
	if m != nil {
		return m.PrepareChannelList
	}
	return nil
}

// 尽量不要使用GetPrepareChannelByPara接口 性能很低
// 可以使用 GetPrepareIDListByChannelType 或者 GetPrepareChannelListV2
type GetPrepareChannelByParaReq struct {
	TagId                *uint32  `protobuf:"varint,1,opt,name=tag_id,json=tagId" json:"tag_id,omitempty"`
	NewLevel             *uint32  `protobuf:"varint,2,opt,name=new_level,json=newLevel" json:"new_level,omitempty"`
	OldLevel             *uint32  `protobuf:"varint,3,opt,name=old_level,json=oldLevel" json:"old_level,omitempty"`
	IsBegin              *bool    `protobuf:"varint,4,opt,name=is_begin,json=isBegin" json:"is_begin,omitempty"`
	IsFilterLiveAll      *bool    `protobuf:"varint,5,opt,name=is_filter_live_all,json=isFilterLiveAll" json:"is_filter_live_all,omitempty"`
	FilterLiveByTs       *uint32  `protobuf:"varint,6,opt,name=filter_live_by_ts,json=filterLiveByTs" json:"filter_live_by_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPrepareChannelByParaReq) Reset()         { *m = GetPrepareChannelByParaReq{} }
func (m *GetPrepareChannelByParaReq) String() string { return proto.CompactTextString(m) }
func (*GetPrepareChannelByParaReq) ProtoMessage()    {}
func (*GetPrepareChannelByParaReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{11}
}
func (m *GetPrepareChannelByParaReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPrepareChannelByParaReq.Unmarshal(m, b)
}
func (m *GetPrepareChannelByParaReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPrepareChannelByParaReq.Marshal(b, m, deterministic)
}
func (dst *GetPrepareChannelByParaReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPrepareChannelByParaReq.Merge(dst, src)
}
func (m *GetPrepareChannelByParaReq) XXX_Size() int {
	return xxx_messageInfo_GetPrepareChannelByParaReq.Size(m)
}
func (m *GetPrepareChannelByParaReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPrepareChannelByParaReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPrepareChannelByParaReq proto.InternalMessageInfo

func (m *GetPrepareChannelByParaReq) GetTagId() uint32 {
	if m != nil && m.TagId != nil {
		return *m.TagId
	}
	return 0
}

func (m *GetPrepareChannelByParaReq) GetNewLevel() uint32 {
	if m != nil && m.NewLevel != nil {
		return *m.NewLevel
	}
	return 0
}

func (m *GetPrepareChannelByParaReq) GetOldLevel() uint32 {
	if m != nil && m.OldLevel != nil {
		return *m.OldLevel
	}
	return 0
}

func (m *GetPrepareChannelByParaReq) GetIsBegin() bool {
	if m != nil && m.IsBegin != nil {
		return *m.IsBegin
	}
	return false
}

func (m *GetPrepareChannelByParaReq) GetIsFilterLiveAll() bool {
	if m != nil && m.IsFilterLiveAll != nil {
		return *m.IsFilterLiveAll
	}
	return false
}

func (m *GetPrepareChannelByParaReq) GetFilterLiveByTs() uint32 {
	if m != nil && m.FilterLiveByTs != nil {
		return *m.FilterLiveByTs
	}
	return 0
}

type GetPrepareChannelByParaResp struct {
	PrepareChannelList   []*PrepareChannelInfo `protobuf:"bytes,1,rep,name=prepare_channel_list,json=prepareChannelList" json:"prepare_channel_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetPrepareChannelByParaResp) Reset()         { *m = GetPrepareChannelByParaResp{} }
func (m *GetPrepareChannelByParaResp) String() string { return proto.CompactTextString(m) }
func (*GetPrepareChannelByParaResp) ProtoMessage()    {}
func (*GetPrepareChannelByParaResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{12}
}
func (m *GetPrepareChannelByParaResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPrepareChannelByParaResp.Unmarshal(m, b)
}
func (m *GetPrepareChannelByParaResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPrepareChannelByParaResp.Marshal(b, m, deterministic)
}
func (dst *GetPrepareChannelByParaResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPrepareChannelByParaResp.Merge(dst, src)
}
func (m *GetPrepareChannelByParaResp) XXX_Size() int {
	return xxx_messageInfo_GetPrepareChannelByParaResp.Size(m)
}
func (m *GetPrepareChannelByParaResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPrepareChannelByParaResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPrepareChannelByParaResp proto.InternalMessageInfo

func (m *GetPrepareChannelByParaResp) GetPrepareChannelList() []*PrepareChannelInfo {
	if m != nil {
		return m.PrepareChannelList
	}
	return nil
}

type GetPrepareIDListByChannelTypeReq struct {
	ChannelTypeList      []uint32 `protobuf:"varint,1,rep,name=channel_type_list,json=channelTypeList" json:"channel_type_list,omitempty"`
	Offset               *uint32  `protobuf:"varint,2,opt,name=offset" json:"offset,omitempty"`
	Limit                *uint32  `protobuf:"varint,3,opt,name=limit" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPrepareIDListByChannelTypeReq) Reset()         { *m = GetPrepareIDListByChannelTypeReq{} }
func (m *GetPrepareIDListByChannelTypeReq) String() string { return proto.CompactTextString(m) }
func (*GetPrepareIDListByChannelTypeReq) ProtoMessage()    {}
func (*GetPrepareIDListByChannelTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{13}
}
func (m *GetPrepareIDListByChannelTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPrepareIDListByChannelTypeReq.Unmarshal(m, b)
}
func (m *GetPrepareIDListByChannelTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPrepareIDListByChannelTypeReq.Marshal(b, m, deterministic)
}
func (dst *GetPrepareIDListByChannelTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPrepareIDListByChannelTypeReq.Merge(dst, src)
}
func (m *GetPrepareIDListByChannelTypeReq) XXX_Size() int {
	return xxx_messageInfo_GetPrepareIDListByChannelTypeReq.Size(m)
}
func (m *GetPrepareIDListByChannelTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPrepareIDListByChannelTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPrepareIDListByChannelTypeReq proto.InternalMessageInfo

func (m *GetPrepareIDListByChannelTypeReq) GetChannelTypeList() []uint32 {
	if m != nil {
		return m.ChannelTypeList
	}
	return nil
}

func (m *GetPrepareIDListByChannelTypeReq) GetOffset() uint32 {
	if m != nil && m.Offset != nil {
		return *m.Offset
	}
	return 0
}

func (m *GetPrepareIDListByChannelTypeReq) GetLimit() uint32 {
	if m != nil && m.Limit != nil {
		return *m.Limit
	}
	return 0
}

type GetPrepareIDListByChannelTypeResp struct {
	ChannelIdList        []uint32 `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPrepareIDListByChannelTypeResp) Reset()         { *m = GetPrepareIDListByChannelTypeResp{} }
func (m *GetPrepareIDListByChannelTypeResp) String() string { return proto.CompactTextString(m) }
func (*GetPrepareIDListByChannelTypeResp) ProtoMessage()    {}
func (*GetPrepareIDListByChannelTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{14}
}
func (m *GetPrepareIDListByChannelTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPrepareIDListByChannelTypeResp.Unmarshal(m, b)
}
func (m *GetPrepareIDListByChannelTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPrepareIDListByChannelTypeResp.Marshal(b, m, deterministic)
}
func (dst *GetPrepareIDListByChannelTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPrepareIDListByChannelTypeResp.Merge(dst, src)
}
func (m *GetPrepareIDListByChannelTypeResp) XXX_Size() int {
	return xxx_messageInfo_GetPrepareIDListByChannelTypeResp.Size(m)
}
func (m *GetPrepareIDListByChannelTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPrepareIDListByChannelTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPrepareIDListByChannelTypeResp proto.InternalMessageInfo

func (m *GetPrepareIDListByChannelTypeResp) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type GetPrepareChannelListV2Req struct {
	ChannelTypeList      []uint32 `protobuf:"varint,1,rep,name=channel_type_list,json=channelTypeList" json:"channel_type_list,omitempty"`
	IsEnableCache        *bool    `protobuf:"varint,2,opt,name=is_enable_cache,json=isEnableCache" json:"is_enable_cache,omitempty"`
	Offset               *uint32  `protobuf:"varint,3,opt,name=offset" json:"offset,omitempty"`
	Limit                *uint32  `protobuf:"varint,4,opt,name=limit" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPrepareChannelListV2Req) Reset()         { *m = GetPrepareChannelListV2Req{} }
func (m *GetPrepareChannelListV2Req) String() string { return proto.CompactTextString(m) }
func (*GetPrepareChannelListV2Req) ProtoMessage()    {}
func (*GetPrepareChannelListV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{15}
}
func (m *GetPrepareChannelListV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPrepareChannelListV2Req.Unmarshal(m, b)
}
func (m *GetPrepareChannelListV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPrepareChannelListV2Req.Marshal(b, m, deterministic)
}
func (dst *GetPrepareChannelListV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPrepareChannelListV2Req.Merge(dst, src)
}
func (m *GetPrepareChannelListV2Req) XXX_Size() int {
	return xxx_messageInfo_GetPrepareChannelListV2Req.Size(m)
}
func (m *GetPrepareChannelListV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPrepareChannelListV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_GetPrepareChannelListV2Req proto.InternalMessageInfo

func (m *GetPrepareChannelListV2Req) GetChannelTypeList() []uint32 {
	if m != nil {
		return m.ChannelTypeList
	}
	return nil
}

func (m *GetPrepareChannelListV2Req) GetIsEnableCache() bool {
	if m != nil && m.IsEnableCache != nil {
		return *m.IsEnableCache
	}
	return false
}

func (m *GetPrepareChannelListV2Req) GetOffset() uint32 {
	if m != nil && m.Offset != nil {
		return *m.Offset
	}
	return 0
}

func (m *GetPrepareChannelListV2Req) GetLimit() uint32 {
	if m != nil && m.Limit != nil {
		return *m.Limit
	}
	return 0
}

type GetPrepareChannelListV2Resp struct {
	PrepareChannelList   []*PrepareChannelInfo `protobuf:"bytes,1,rep,name=prepare_channel_list,json=prepareChannelList" json:"prepare_channel_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetPrepareChannelListV2Resp) Reset()         { *m = GetPrepareChannelListV2Resp{} }
func (m *GetPrepareChannelListV2Resp) String() string { return proto.CompactTextString(m) }
func (*GetPrepareChannelListV2Resp) ProtoMessage()    {}
func (*GetPrepareChannelListV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{16}
}
func (m *GetPrepareChannelListV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPrepareChannelListV2Resp.Unmarshal(m, b)
}
func (m *GetPrepareChannelListV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPrepareChannelListV2Resp.Marshal(b, m, deterministic)
}
func (dst *GetPrepareChannelListV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPrepareChannelListV2Resp.Merge(dst, src)
}
func (m *GetPrepareChannelListV2Resp) XXX_Size() int {
	return xxx_messageInfo_GetPrepareChannelListV2Resp.Size(m)
}
func (m *GetPrepareChannelListV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPrepareChannelListV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPrepareChannelListV2Resp proto.InternalMessageInfo

func (m *GetPrepareChannelListV2Resp) GetPrepareChannelList() []*PrepareChannelInfo {
	if m != nil {
		return m.PrepareChannelList
	}
	return nil
}

// 活动库相关
type AddActivityChannelReq struct {
	ActivityChannelList  []*ActivityChannelInfo `protobuf:"bytes,1,rep,name=activity_channel_list,json=activityChannelList" json:"activity_channel_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *AddActivityChannelReq) Reset()         { *m = AddActivityChannelReq{} }
func (m *AddActivityChannelReq) String() string { return proto.CompactTextString(m) }
func (*AddActivityChannelReq) ProtoMessage()    {}
func (*AddActivityChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{17}
}
func (m *AddActivityChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddActivityChannelReq.Unmarshal(m, b)
}
func (m *AddActivityChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddActivityChannelReq.Marshal(b, m, deterministic)
}
func (dst *AddActivityChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddActivityChannelReq.Merge(dst, src)
}
func (m *AddActivityChannelReq) XXX_Size() int {
	return xxx_messageInfo_AddActivityChannelReq.Size(m)
}
func (m *AddActivityChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddActivityChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddActivityChannelReq proto.InternalMessageInfo

func (m *AddActivityChannelReq) GetActivityChannelList() []*ActivityChannelInfo {
	if m != nil {
		return m.ActivityChannelList
	}
	return nil
}

type AddActivityChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddActivityChannelResp) Reset()         { *m = AddActivityChannelResp{} }
func (m *AddActivityChannelResp) String() string { return proto.CompactTextString(m) }
func (*AddActivityChannelResp) ProtoMessage()    {}
func (*AddActivityChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{18}
}
func (m *AddActivityChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddActivityChannelResp.Unmarshal(m, b)
}
func (m *AddActivityChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddActivityChannelResp.Marshal(b, m, deterministic)
}
func (dst *AddActivityChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddActivityChannelResp.Merge(dst, src)
}
func (m *AddActivityChannelResp) XXX_Size() int {
	return xxx_messageInfo_AddActivityChannelResp.Size(m)
}
func (m *AddActivityChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddActivityChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddActivityChannelResp proto.InternalMessageInfo

type GetActivityChannelListReq struct {
	IsBegin              *bool    `protobuf:"varint,1,opt,name=is_begin,json=isBegin" json:"is_begin,omitempty"`
	Category             *uint32  `protobuf:"varint,2,opt,name=category" json:"category,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetActivityChannelListReq) Reset()         { *m = GetActivityChannelListReq{} }
func (m *GetActivityChannelListReq) String() string { return proto.CompactTextString(m) }
func (*GetActivityChannelListReq) ProtoMessage()    {}
func (*GetActivityChannelListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{19}
}
func (m *GetActivityChannelListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActivityChannelListReq.Unmarshal(m, b)
}
func (m *GetActivityChannelListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActivityChannelListReq.Marshal(b, m, deterministic)
}
func (dst *GetActivityChannelListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActivityChannelListReq.Merge(dst, src)
}
func (m *GetActivityChannelListReq) XXX_Size() int {
	return xxx_messageInfo_GetActivityChannelListReq.Size(m)
}
func (m *GetActivityChannelListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActivityChannelListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetActivityChannelListReq proto.InternalMessageInfo

func (m *GetActivityChannelListReq) GetIsBegin() bool {
	if m != nil && m.IsBegin != nil {
		return *m.IsBegin
	}
	return false
}

func (m *GetActivityChannelListReq) GetCategory() uint32 {
	if m != nil && m.Category != nil {
		return *m.Category
	}
	return 0
}

type GetActivityChannelListResp struct {
	ActivityChannelList  []*ActivityChannelInfo `protobuf:"bytes,1,rep,name=activity_channel_list,json=activityChannelList" json:"activity_channel_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetActivityChannelListResp) Reset()         { *m = GetActivityChannelListResp{} }
func (m *GetActivityChannelListResp) String() string { return proto.CompactTextString(m) }
func (*GetActivityChannelListResp) ProtoMessage()    {}
func (*GetActivityChannelListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{20}
}
func (m *GetActivityChannelListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActivityChannelListResp.Unmarshal(m, b)
}
func (m *GetActivityChannelListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActivityChannelListResp.Marshal(b, m, deterministic)
}
func (dst *GetActivityChannelListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActivityChannelListResp.Merge(dst, src)
}
func (m *GetActivityChannelListResp) XXX_Size() int {
	return xxx_messageInfo_GetActivityChannelListResp.Size(m)
}
func (m *GetActivityChannelListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActivityChannelListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetActivityChannelListResp proto.InternalMessageInfo

func (m *GetActivityChannelListResp) GetActivityChannelList() []*ActivityChannelInfo {
	if m != nil {
		return m.ActivityChannelList
	}
	return nil
}

// 活动房间支持配置多个时间段，给运营后台提供一个新的获取列表接口
type GetActivityChannelListV2Req struct {
	Offset               *uint32  `protobuf:"varint,1,req,name=offset" json:"offset,omitempty"`
	Limit                *uint32  `protobuf:"varint,2,req,name=limit" json:"limit,omitempty"`
	TagId                *uint32  `protobuf:"varint,3,opt,name=tag_id,json=tagId" json:"tag_id,omitempty"`
	ChannelId            *uint32  `protobuf:"varint,4,opt,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	Category             *uint32  `protobuf:"varint,5,opt,name=category" json:"category,omitempty"`
	CidList              []uint32 `protobuf:"varint,6,rep,name=cid_list,json=cidList" json:"cid_list,omitempty"`
	MinTs                *uint32  `protobuf:"varint,7,opt,name=min_ts,json=minTs" json:"min_ts,omitempty"`
	MaxTs                *uint32  `protobuf:"varint,8,opt,name=max_ts,json=maxTs" json:"max_ts,omitempty"`
	ActStatus            *uint32  `protobuf:"varint,9,opt,name=act_status,json=actStatus" json:"act_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetActivityChannelListV2Req) Reset()         { *m = GetActivityChannelListV2Req{} }
func (m *GetActivityChannelListV2Req) String() string { return proto.CompactTextString(m) }
func (*GetActivityChannelListV2Req) ProtoMessage()    {}
func (*GetActivityChannelListV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{21}
}
func (m *GetActivityChannelListV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActivityChannelListV2Req.Unmarshal(m, b)
}
func (m *GetActivityChannelListV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActivityChannelListV2Req.Marshal(b, m, deterministic)
}
func (dst *GetActivityChannelListV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActivityChannelListV2Req.Merge(dst, src)
}
func (m *GetActivityChannelListV2Req) XXX_Size() int {
	return xxx_messageInfo_GetActivityChannelListV2Req.Size(m)
}
func (m *GetActivityChannelListV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActivityChannelListV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_GetActivityChannelListV2Req proto.InternalMessageInfo

func (m *GetActivityChannelListV2Req) GetOffset() uint32 {
	if m != nil && m.Offset != nil {
		return *m.Offset
	}
	return 0
}

func (m *GetActivityChannelListV2Req) GetLimit() uint32 {
	if m != nil && m.Limit != nil {
		return *m.Limit
	}
	return 0
}

func (m *GetActivityChannelListV2Req) GetTagId() uint32 {
	if m != nil && m.TagId != nil {
		return *m.TagId
	}
	return 0
}

func (m *GetActivityChannelListV2Req) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *GetActivityChannelListV2Req) GetCategory() uint32 {
	if m != nil && m.Category != nil {
		return *m.Category
	}
	return 0
}

func (m *GetActivityChannelListV2Req) GetCidList() []uint32 {
	if m != nil {
		return m.CidList
	}
	return nil
}

func (m *GetActivityChannelListV2Req) GetMinTs() uint32 {
	if m != nil && m.MinTs != nil {
		return *m.MinTs
	}
	return 0
}

func (m *GetActivityChannelListV2Req) GetMaxTs() uint32 {
	if m != nil && m.MaxTs != nil {
		return *m.MaxTs
	}
	return 0
}

func (m *GetActivityChannelListV2Req) GetActStatus() uint32 {
	if m != nil && m.ActStatus != nil {
		return *m.ActStatus
	}
	return 0
}

type GetActivityChannelListV2Resp struct {
	InfoList             []*ActivityChannelInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList" json:"info_list,omitempty"`
	NextOffset           *uint32                `protobuf:"varint,2,req,name=next_offset,json=nextOffset" json:"next_offset,omitempty"`
	TotalCount           *uint32                `protobuf:"varint,3,req,name=total_count,json=totalCount" json:"total_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetActivityChannelListV2Resp) Reset()         { *m = GetActivityChannelListV2Resp{} }
func (m *GetActivityChannelListV2Resp) String() string { return proto.CompactTextString(m) }
func (*GetActivityChannelListV2Resp) ProtoMessage()    {}
func (*GetActivityChannelListV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{22}
}
func (m *GetActivityChannelListV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActivityChannelListV2Resp.Unmarshal(m, b)
}
func (m *GetActivityChannelListV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActivityChannelListV2Resp.Marshal(b, m, deterministic)
}
func (dst *GetActivityChannelListV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActivityChannelListV2Resp.Merge(dst, src)
}
func (m *GetActivityChannelListV2Resp) XXX_Size() int {
	return xxx_messageInfo_GetActivityChannelListV2Resp.Size(m)
}
func (m *GetActivityChannelListV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActivityChannelListV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_GetActivityChannelListV2Resp proto.InternalMessageInfo

func (m *GetActivityChannelListV2Resp) GetInfoList() []*ActivityChannelInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetActivityChannelListV2Resp) GetNextOffset() uint32 {
	if m != nil && m.NextOffset != nil {
		return *m.NextOffset
	}
	return 0
}

func (m *GetActivityChannelListV2Resp) GetTotalCount() uint32 {
	if m != nil && m.TotalCount != nil {
		return *m.TotalCount
	}
	return 0
}

type UpdateActivityChannelReq struct {
	Info                 *ActivityChannelInfo `protobuf:"bytes,1,opt,name=info" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *UpdateActivityChannelReq) Reset()         { *m = UpdateActivityChannelReq{} }
func (m *UpdateActivityChannelReq) String() string { return proto.CompactTextString(m) }
func (*UpdateActivityChannelReq) ProtoMessage()    {}
func (*UpdateActivityChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{23}
}
func (m *UpdateActivityChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateActivityChannelReq.Unmarshal(m, b)
}
func (m *UpdateActivityChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateActivityChannelReq.Marshal(b, m, deterministic)
}
func (dst *UpdateActivityChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateActivityChannelReq.Merge(dst, src)
}
func (m *UpdateActivityChannelReq) XXX_Size() int {
	return xxx_messageInfo_UpdateActivityChannelReq.Size(m)
}
func (m *UpdateActivityChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateActivityChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateActivityChannelReq proto.InternalMessageInfo

func (m *UpdateActivityChannelReq) GetInfo() *ActivityChannelInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type UpdateActivityChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateActivityChannelResp) Reset()         { *m = UpdateActivityChannelResp{} }
func (m *UpdateActivityChannelResp) String() string { return proto.CompactTextString(m) }
func (*UpdateActivityChannelResp) ProtoMessage()    {}
func (*UpdateActivityChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{24}
}
func (m *UpdateActivityChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateActivityChannelResp.Unmarshal(m, b)
}
func (m *UpdateActivityChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateActivityChannelResp.Marshal(b, m, deterministic)
}
func (dst *UpdateActivityChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateActivityChannelResp.Merge(dst, src)
}
func (m *UpdateActivityChannelResp) XXX_Size() int {
	return xxx_messageInfo_UpdateActivityChannelResp.Size(m)
}
func (m *UpdateActivityChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateActivityChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateActivityChannelResp proto.InternalMessageInfo

// tag配置
type ChannelTagConfigInfo struct {
	TagId                *uint32  `protobuf:"varint,1,req,name=tag_id,json=tagId" json:"tag_id,omitempty"`
	Name                 *string  `protobuf:"bytes,2,req,name=name" json:"name,omitempty"`
	BkColor              *string  `protobuf:"bytes,3,opt,name=bk_color,json=bkColor" json:"bk_color,omitempty"`
	Icon                 *string  `protobuf:"bytes,4,opt,name=icon" json:"icon,omitempty"`
	TagType              *uint32  `protobuf:"varint,5,opt,name=tag_type,json=tagType" json:"tag_type,omitempty"`
	SubTag               *string  `protobuf:"bytes,6,opt,name=sub_tag,json=subTag" json:"sub_tag,omitempty"`
	TagUrl               *string  `protobuf:"bytes,7,opt,name=tag_url,json=tagUrl" json:"tag_url,omitempty"`
	BgUrl                *string  `protobuf:"bytes,8,opt,name=bg_url,json=bgUrl" json:"bg_url,omitempty"`
	WelcomeText          *string  `protobuf:"bytes,9,opt,name=welcome_text,json=welcomeText" json:"welcome_text,omitempty"`
	ButtonColor          *string  `protobuf:"bytes,10,opt,name=button_color,json=buttonColor" json:"button_color,omitempty"`
	MultiColor           []string `protobuf:"bytes,11,rep,name=multi_color,json=multiColor" json:"multi_color,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelTagConfigInfo) Reset()         { *m = ChannelTagConfigInfo{} }
func (m *ChannelTagConfigInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelTagConfigInfo) ProtoMessage()    {}
func (*ChannelTagConfigInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{25}
}
func (m *ChannelTagConfigInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelTagConfigInfo.Unmarshal(m, b)
}
func (m *ChannelTagConfigInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelTagConfigInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelTagConfigInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelTagConfigInfo.Merge(dst, src)
}
func (m *ChannelTagConfigInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelTagConfigInfo.Size(m)
}
func (m *ChannelTagConfigInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelTagConfigInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelTagConfigInfo proto.InternalMessageInfo

func (m *ChannelTagConfigInfo) GetTagId() uint32 {
	if m != nil && m.TagId != nil {
		return *m.TagId
	}
	return 0
}

func (m *ChannelTagConfigInfo) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *ChannelTagConfigInfo) GetBkColor() string {
	if m != nil && m.BkColor != nil {
		return *m.BkColor
	}
	return ""
}

func (m *ChannelTagConfigInfo) GetIcon() string {
	if m != nil && m.Icon != nil {
		return *m.Icon
	}
	return ""
}

func (m *ChannelTagConfigInfo) GetTagType() uint32 {
	if m != nil && m.TagType != nil {
		return *m.TagType
	}
	return 0
}

func (m *ChannelTagConfigInfo) GetSubTag() string {
	if m != nil && m.SubTag != nil {
		return *m.SubTag
	}
	return ""
}

func (m *ChannelTagConfigInfo) GetTagUrl() string {
	if m != nil && m.TagUrl != nil {
		return *m.TagUrl
	}
	return ""
}

func (m *ChannelTagConfigInfo) GetBgUrl() string {
	if m != nil && m.BgUrl != nil {
		return *m.BgUrl
	}
	return ""
}

func (m *ChannelTagConfigInfo) GetWelcomeText() string {
	if m != nil && m.WelcomeText != nil {
		return *m.WelcomeText
	}
	return ""
}

func (m *ChannelTagConfigInfo) GetButtonColor() string {
	if m != nil && m.ButtonColor != nil {
		return *m.ButtonColor
	}
	return ""
}

func (m *ChannelTagConfigInfo) GetMultiColor() []string {
	if m != nil {
		return m.MultiColor
	}
	return nil
}

// 多级tab配置
type ChannelMultiTagConfigInfo struct {
	RootTagName          *string                 `protobuf:"bytes,1,req,name=root_tag_name,json=rootTagName" json:"root_tag_name,omitempty"`
	RootTagId            *uint32                 `protobuf:"varint,2,req,name=root_tag_id,json=rootTagId" json:"root_tag_id,omitempty"`
	SubTagList           []*ChannelTagConfigInfo `protobuf:"bytes,3,rep,name=sub_tag_list,json=subTagList" json:"sub_tag_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *ChannelMultiTagConfigInfo) Reset()         { *m = ChannelMultiTagConfigInfo{} }
func (m *ChannelMultiTagConfigInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelMultiTagConfigInfo) ProtoMessage()    {}
func (*ChannelMultiTagConfigInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{26}
}
func (m *ChannelMultiTagConfigInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelMultiTagConfigInfo.Unmarshal(m, b)
}
func (m *ChannelMultiTagConfigInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelMultiTagConfigInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelMultiTagConfigInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelMultiTagConfigInfo.Merge(dst, src)
}
func (m *ChannelMultiTagConfigInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelMultiTagConfigInfo.Size(m)
}
func (m *ChannelMultiTagConfigInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelMultiTagConfigInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelMultiTagConfigInfo proto.InternalMessageInfo

func (m *ChannelMultiTagConfigInfo) GetRootTagName() string {
	if m != nil && m.RootTagName != nil {
		return *m.RootTagName
	}
	return ""
}

func (m *ChannelMultiTagConfigInfo) GetRootTagId() uint32 {
	if m != nil && m.RootTagId != nil {
		return *m.RootTagId
	}
	return 0
}

func (m *ChannelMultiTagConfigInfo) GetSubTagList() []*ChannelTagConfigInfo {
	if m != nil {
		return m.SubTagList
	}
	return nil
}

type GetChannelTagConfigInfoReq struct {
	RegisterAt           *uint32  `protobuf:"varint,1,opt,name=register_at,json=registerAt" json:"register_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelTagConfigInfoReq) Reset()         { *m = GetChannelTagConfigInfoReq{} }
func (m *GetChannelTagConfigInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelTagConfigInfoReq) ProtoMessage()    {}
func (*GetChannelTagConfigInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{27}
}
func (m *GetChannelTagConfigInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelTagConfigInfoReq.Unmarshal(m, b)
}
func (m *GetChannelTagConfigInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelTagConfigInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelTagConfigInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelTagConfigInfoReq.Merge(dst, src)
}
func (m *GetChannelTagConfigInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelTagConfigInfoReq.Size(m)
}
func (m *GetChannelTagConfigInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelTagConfigInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelTagConfigInfoReq proto.InternalMessageInfo

func (m *GetChannelTagConfigInfoReq) GetRegisterAt() uint32 {
	if m != nil && m.RegisterAt != nil {
		return *m.RegisterAt
	}
	return 0
}

type GetChannelTagConfigInfoResp struct {
	ChannelTagList       []*ChannelTagConfigInfo      `protobuf:"bytes,1,rep,name=channel_tag_list,json=channelTagList" json:"channel_tag_list,omitempty"`
	ChannelMultiTagList  []*ChannelMultiTagConfigInfo `protobuf:"bytes,2,rep,name=channel_multi_tag_list,json=channelMultiTagList" json:"channel_multi_tag_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *GetChannelTagConfigInfoResp) Reset()         { *m = GetChannelTagConfigInfoResp{} }
func (m *GetChannelTagConfigInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelTagConfigInfoResp) ProtoMessage()    {}
func (*GetChannelTagConfigInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{28}
}
func (m *GetChannelTagConfigInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelTagConfigInfoResp.Unmarshal(m, b)
}
func (m *GetChannelTagConfigInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelTagConfigInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelTagConfigInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelTagConfigInfoResp.Merge(dst, src)
}
func (m *GetChannelTagConfigInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelTagConfigInfoResp.Size(m)
}
func (m *GetChannelTagConfigInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelTagConfigInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelTagConfigInfoResp proto.InternalMessageInfo

func (m *GetChannelTagConfigInfoResp) GetChannelTagList() []*ChannelTagConfigInfo {
	if m != nil {
		return m.ChannelTagList
	}
	return nil
}

func (m *GetChannelTagConfigInfoResp) GetChannelMultiTagList() []*ChannelMultiTagConfigInfo {
	if m != nil {
		return m.ChannelMultiTagList
	}
	return nil
}

type GetChannelTagReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelTagReq) Reset()         { *m = GetChannelTagReq{} }
func (m *GetChannelTagReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelTagReq) ProtoMessage()    {}
func (*GetChannelTagReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{29}
}
func (m *GetChannelTagReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelTagReq.Unmarshal(m, b)
}
func (m *GetChannelTagReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelTagReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelTagReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelTagReq.Merge(dst, src)
}
func (m *GetChannelTagReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelTagReq.Size(m)
}
func (m *GetChannelTagReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelTagReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelTagReq proto.InternalMessageInfo

func (m *GetChannelTagReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

type GetChannelTagResp struct {
	TagInfo              *ChannelTagConfigInfo `protobuf:"bytes,1,req,name=tag_info,json=tagInfo" json:"tag_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetChannelTagResp) Reset()         { *m = GetChannelTagResp{} }
func (m *GetChannelTagResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelTagResp) ProtoMessage()    {}
func (*GetChannelTagResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{30}
}
func (m *GetChannelTagResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelTagResp.Unmarshal(m, b)
}
func (m *GetChannelTagResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelTagResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelTagResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelTagResp.Merge(dst, src)
}
func (m *GetChannelTagResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelTagResp.Size(m)
}
func (m *GetChannelTagResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelTagResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelTagResp proto.InternalMessageInfo

func (m *GetChannelTagResp) GetTagInfo() *ChannelTagConfigInfo {
	if m != nil {
		return m.TagInfo
	}
	return nil
}

type BatchGetChannelTagReq struct {
	ChannelIdList        []uint32 `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetChannelTagReq) Reset()         { *m = BatchGetChannelTagReq{} }
func (m *BatchGetChannelTagReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelTagReq) ProtoMessage()    {}
func (*BatchGetChannelTagReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{31}
}
func (m *BatchGetChannelTagReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelTagReq.Unmarshal(m, b)
}
func (m *BatchGetChannelTagReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelTagReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelTagReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelTagReq.Merge(dst, src)
}
func (m *BatchGetChannelTagReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelTagReq.Size(m)
}
func (m *BatchGetChannelTagReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelTagReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelTagReq proto.InternalMessageInfo

func (m *BatchGetChannelTagReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type BatchGetChannelTagResp struct {
	ChannelTagList       []*ChannelTagConfigInfo `protobuf:"bytes,1,rep,name=channel_tag_list,json=channelTagList" json:"channel_tag_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *BatchGetChannelTagResp) Reset()         { *m = BatchGetChannelTagResp{} }
func (m *BatchGetChannelTagResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelTagResp) ProtoMessage()    {}
func (*BatchGetChannelTagResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{32}
}
func (m *BatchGetChannelTagResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelTagResp.Unmarshal(m, b)
}
func (m *BatchGetChannelTagResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelTagResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelTagResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelTagResp.Merge(dst, src)
}
func (m *BatchGetChannelTagResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelTagResp.Size(m)
}
func (m *BatchGetChannelTagResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelTagResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelTagResp proto.InternalMessageInfo

func (m *BatchGetChannelTagResp) GetChannelTagList() []*ChannelTagConfigInfo {
	if m != nil {
		return m.ChannelTagList
	}
	return nil
}

type DeletePrepareChannelReq struct {
	ChannelList          []uint32 `protobuf:"varint,1,rep,name=channel_list,json=channelList" json:"channel_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeletePrepareChannelReq) Reset()         { *m = DeletePrepareChannelReq{} }
func (m *DeletePrepareChannelReq) String() string { return proto.CompactTextString(m) }
func (*DeletePrepareChannelReq) ProtoMessage()    {}
func (*DeletePrepareChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{33}
}
func (m *DeletePrepareChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeletePrepareChannelReq.Unmarshal(m, b)
}
func (m *DeletePrepareChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeletePrepareChannelReq.Marshal(b, m, deterministic)
}
func (dst *DeletePrepareChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeletePrepareChannelReq.Merge(dst, src)
}
func (m *DeletePrepareChannelReq) XXX_Size() int {
	return xxx_messageInfo_DeletePrepareChannelReq.Size(m)
}
func (m *DeletePrepareChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeletePrepareChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeletePrepareChannelReq proto.InternalMessageInfo

func (m *DeletePrepareChannelReq) GetChannelList() []uint32 {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

type DeletePrepareChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeletePrepareChannelResp) Reset()         { *m = DeletePrepareChannelResp{} }
func (m *DeletePrepareChannelResp) String() string { return proto.CompactTextString(m) }
func (*DeletePrepareChannelResp) ProtoMessage()    {}
func (*DeletePrepareChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{34}
}
func (m *DeletePrepareChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeletePrepareChannelResp.Unmarshal(m, b)
}
func (m *DeletePrepareChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeletePrepareChannelResp.Marshal(b, m, deterministic)
}
func (dst *DeletePrepareChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeletePrepareChannelResp.Merge(dst, src)
}
func (m *DeletePrepareChannelResp) XXX_Size() int {
	return xxx_messageInfo_DeletePrepareChannelResp.Size(m)
}
func (m *DeletePrepareChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeletePrepareChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeletePrepareChannelResp proto.InternalMessageInfo

type DeleteActivityChannelReq struct {
	ChannelList          []uint32 `protobuf:"varint,1,rep,name=channel_list,json=channelList" json:"channel_list,omitempty"`
	IdList               []uint32 `protobuf:"varint,2,rep,name=id_list,json=idList" json:"id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteActivityChannelReq) Reset()         { *m = DeleteActivityChannelReq{} }
func (m *DeleteActivityChannelReq) String() string { return proto.CompactTextString(m) }
func (*DeleteActivityChannelReq) ProtoMessage()    {}
func (*DeleteActivityChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{35}
}
func (m *DeleteActivityChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteActivityChannelReq.Unmarshal(m, b)
}
func (m *DeleteActivityChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteActivityChannelReq.Marshal(b, m, deterministic)
}
func (dst *DeleteActivityChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteActivityChannelReq.Merge(dst, src)
}
func (m *DeleteActivityChannelReq) XXX_Size() int {
	return xxx_messageInfo_DeleteActivityChannelReq.Size(m)
}
func (m *DeleteActivityChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteActivityChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteActivityChannelReq proto.InternalMessageInfo

func (m *DeleteActivityChannelReq) GetChannelList() []uint32 {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

func (m *DeleteActivityChannelReq) GetIdList() []uint32 {
	if m != nil {
		return m.IdList
	}
	return nil
}

type DeleteActivityChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteActivityChannelResp) Reset()         { *m = DeleteActivityChannelResp{} }
func (m *DeleteActivityChannelResp) String() string { return proto.CompactTextString(m) }
func (*DeleteActivityChannelResp) ProtoMessage()    {}
func (*DeleteActivityChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{36}
}
func (m *DeleteActivityChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteActivityChannelResp.Unmarshal(m, b)
}
func (m *DeleteActivityChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteActivityChannelResp.Marshal(b, m, deterministic)
}
func (dst *DeleteActivityChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteActivityChannelResp.Merge(dst, src)
}
func (m *DeleteActivityChannelResp) XXX_Size() int {
	return xxx_messageInfo_DeleteActivityChannelResp.Size(m)
}
func (m *DeleteActivityChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteActivityChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteActivityChannelResp proto.InternalMessageInfo

type SetChannelCommonReq struct {
	ChannelId            *uint32        `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	TagId                *uint32        `protobuf:"varint,2,req,name=tag_id,json=tagId" json:"tag_id,omitempty"`
	SubTag               *string        `protobuf:"bytes,3,opt,name=sub_tag,json=subTag" json:"sub_tag,omitempty"`
	ChannelType          *uint32        `protobuf:"varint,4,opt,name=channel_type,json=channelType" json:"channel_type,omitempty"`
	EffectTimes          []*TimeSection `protobuf:"bytes,5,rep,name=effect_times,json=effectTimes" json:"effect_times,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *SetChannelCommonReq) Reset()         { *m = SetChannelCommonReq{} }
func (m *SetChannelCommonReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelCommonReq) ProtoMessage()    {}
func (*SetChannelCommonReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{37}
}
func (m *SetChannelCommonReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelCommonReq.Unmarshal(m, b)
}
func (m *SetChannelCommonReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelCommonReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelCommonReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelCommonReq.Merge(dst, src)
}
func (m *SetChannelCommonReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelCommonReq.Size(m)
}
func (m *SetChannelCommonReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelCommonReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelCommonReq proto.InternalMessageInfo

func (m *SetChannelCommonReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *SetChannelCommonReq) GetTagId() uint32 {
	if m != nil && m.TagId != nil {
		return *m.TagId
	}
	return 0
}

func (m *SetChannelCommonReq) GetSubTag() string {
	if m != nil && m.SubTag != nil {
		return *m.SubTag
	}
	return ""
}

func (m *SetChannelCommonReq) GetChannelType() uint32 {
	if m != nil && m.ChannelType != nil {
		return *m.ChannelType
	}
	return 0
}

func (m *SetChannelCommonReq) GetEffectTimes() []*TimeSection {
	if m != nil {
		return m.EffectTimes
	}
	return nil
}

type SetChannelCommonResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelCommonResp) Reset()         { *m = SetChannelCommonResp{} }
func (m *SetChannelCommonResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelCommonResp) ProtoMessage()    {}
func (*SetChannelCommonResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{38}
}
func (m *SetChannelCommonResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelCommonResp.Unmarshal(m, b)
}
func (m *SetChannelCommonResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelCommonResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelCommonResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelCommonResp.Merge(dst, src)
}
func (m *SetChannelCommonResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelCommonResp.Size(m)
}
func (m *SetChannelCommonResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelCommonResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelCommonResp proto.InternalMessageInfo

type GetRecommendChannelReq struct {
	UserCategory         *uint32  `protobuf:"varint,1,req,name=user_category,json=userCategory" json:"user_category,omitempty"`
	Start                *uint32  `protobuf:"varint,2,req,name=start" json:"start,omitempty"`
	Count                *uint32  `protobuf:"varint,3,req,name=count" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecommendChannelReq) Reset()         { *m = GetRecommendChannelReq{} }
func (m *GetRecommendChannelReq) String() string { return proto.CompactTextString(m) }
func (*GetRecommendChannelReq) ProtoMessage()    {}
func (*GetRecommendChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{39}
}
func (m *GetRecommendChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendChannelReq.Unmarshal(m, b)
}
func (m *GetRecommendChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendChannelReq.Marshal(b, m, deterministic)
}
func (dst *GetRecommendChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendChannelReq.Merge(dst, src)
}
func (m *GetRecommendChannelReq) XXX_Size() int {
	return xxx_messageInfo_GetRecommendChannelReq.Size(m)
}
func (m *GetRecommendChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendChannelReq proto.InternalMessageInfo

func (m *GetRecommendChannelReq) GetUserCategory() uint32 {
	if m != nil && m.UserCategory != nil {
		return *m.UserCategory
	}
	return 0
}

func (m *GetRecommendChannelReq) GetStart() uint32 {
	if m != nil && m.Start != nil {
		return *m.Start
	}
	return 0
}

func (m *GetRecommendChannelReq) GetCount() uint32 {
	if m != nil && m.Count != nil {
		return *m.Count
	}
	return 0
}

type GetRecommendChannelResp struct {
	ChannelList          []*ChannelRecommendSimpleInfo `protobuf:"bytes,1,rep,name=channel_list,json=channelList" json:"channel_list,omitempty"`
	IsEnd                *bool                         `protobuf:"varint,2,req,name=is_end,json=isEnd" json:"is_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetRecommendChannelResp) Reset()         { *m = GetRecommendChannelResp{} }
func (m *GetRecommendChannelResp) String() string { return proto.CompactTextString(m) }
func (*GetRecommendChannelResp) ProtoMessage()    {}
func (*GetRecommendChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{40}
}
func (m *GetRecommendChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendChannelResp.Unmarshal(m, b)
}
func (m *GetRecommendChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendChannelResp.Marshal(b, m, deterministic)
}
func (dst *GetRecommendChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendChannelResp.Merge(dst, src)
}
func (m *GetRecommendChannelResp) XXX_Size() int {
	return xxx_messageInfo_GetRecommendChannelResp.Size(m)
}
func (m *GetRecommendChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendChannelResp proto.InternalMessageInfo

func (m *GetRecommendChannelResp) GetChannelList() []*ChannelRecommendSimpleInfo {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

func (m *GetRecommendChannelResp) GetIsEnd() bool {
	if m != nil && m.IsEnd != nil {
		return *m.IsEnd
	}
	return false
}

type GetChannelByTagIdReq struct {
	TagId                *uint32  `protobuf:"varint,1,req,name=tag_id,json=tagId" json:"tag_id,omitempty"`
	Start                *uint32  `protobuf:"varint,2,req,name=start" json:"start,omitempty"`
	Count                *uint32  `protobuf:"varint,3,req,name=count" json:"count,omitempty"`
	UserCategory         *uint32  `protobuf:"varint,4,req,name=user_category,json=userCategory" json:"user_category,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelByTagIdReq) Reset()         { *m = GetChannelByTagIdReq{} }
func (m *GetChannelByTagIdReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelByTagIdReq) ProtoMessage()    {}
func (*GetChannelByTagIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{41}
}
func (m *GetChannelByTagIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelByTagIdReq.Unmarshal(m, b)
}
func (m *GetChannelByTagIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelByTagIdReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelByTagIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelByTagIdReq.Merge(dst, src)
}
func (m *GetChannelByTagIdReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelByTagIdReq.Size(m)
}
func (m *GetChannelByTagIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelByTagIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelByTagIdReq proto.InternalMessageInfo

func (m *GetChannelByTagIdReq) GetTagId() uint32 {
	if m != nil && m.TagId != nil {
		return *m.TagId
	}
	return 0
}

func (m *GetChannelByTagIdReq) GetStart() uint32 {
	if m != nil && m.Start != nil {
		return *m.Start
	}
	return 0
}

func (m *GetChannelByTagIdReq) GetCount() uint32 {
	if m != nil && m.Count != nil {
		return *m.Count
	}
	return 0
}

func (m *GetChannelByTagIdReq) GetUserCategory() uint32 {
	if m != nil && m.UserCategory != nil {
		return *m.UserCategory
	}
	return 0
}

type GetChannelByTagIdResp struct {
	ChannelList          []*ChannelRecommendSimpleInfo `protobuf:"bytes,1,rep,name=channel_list,json=channelList" json:"channel_list,omitempty"`
	IsEnd                *bool                         `protobuf:"varint,2,req,name=is_end,json=isEnd" json:"is_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetChannelByTagIdResp) Reset()         { *m = GetChannelByTagIdResp{} }
func (m *GetChannelByTagIdResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelByTagIdResp) ProtoMessage()    {}
func (*GetChannelByTagIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{42}
}
func (m *GetChannelByTagIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelByTagIdResp.Unmarshal(m, b)
}
func (m *GetChannelByTagIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelByTagIdResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelByTagIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelByTagIdResp.Merge(dst, src)
}
func (m *GetChannelByTagIdResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelByTagIdResp.Size(m)
}
func (m *GetChannelByTagIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelByTagIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelByTagIdResp proto.InternalMessageInfo

func (m *GetChannelByTagIdResp) GetChannelList() []*ChannelRecommendSimpleInfo {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

func (m *GetChannelByTagIdResp) GetIsEnd() bool {
	if m != nil && m.IsEnd != nil {
		return *m.IsEnd
	}
	return false
}

type HotChannelInfo struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	ChannelType          *uint32  `protobuf:"varint,2,req,name=channel_type,json=channelType" json:"channel_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HotChannelInfo) Reset()         { *m = HotChannelInfo{} }
func (m *HotChannelInfo) String() string { return proto.CompactTextString(m) }
func (*HotChannelInfo) ProtoMessage()    {}
func (*HotChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{43}
}
func (m *HotChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HotChannelInfo.Unmarshal(m, b)
}
func (m *HotChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HotChannelInfo.Marshal(b, m, deterministic)
}
func (dst *HotChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HotChannelInfo.Merge(dst, src)
}
func (m *HotChannelInfo) XXX_Size() int {
	return xxx_messageInfo_HotChannelInfo.Size(m)
}
func (m *HotChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_HotChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_HotChannelInfo proto.InternalMessageInfo

func (m *HotChannelInfo) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *HotChannelInfo) GetChannelType() uint32 {
	if m != nil && m.ChannelType != nil {
		return *m.ChannelType
	}
	return 0
}

type GetHotChannelReq struct {
	Start                *uint32  `protobuf:"varint,1,req,name=start" json:"start,omitempty"`
	Count                *uint32  `protobuf:"varint,2,req,name=count" json:"count,omitempty"`
	Ts                   *uint32  `protobuf:"varint,3,opt,name=ts" json:"ts,omitempty"`
	Uid                  *uint32  `protobuf:"varint,4,opt,name=uid" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetHotChannelReq) Reset()         { *m = GetHotChannelReq{} }
func (m *GetHotChannelReq) String() string { return proto.CompactTextString(m) }
func (*GetHotChannelReq) ProtoMessage()    {}
func (*GetHotChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{44}
}
func (m *GetHotChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHotChannelReq.Unmarshal(m, b)
}
func (m *GetHotChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHotChannelReq.Marshal(b, m, deterministic)
}
func (dst *GetHotChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHotChannelReq.Merge(dst, src)
}
func (m *GetHotChannelReq) XXX_Size() int {
	return xxx_messageInfo_GetHotChannelReq.Size(m)
}
func (m *GetHotChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHotChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetHotChannelReq proto.InternalMessageInfo

func (m *GetHotChannelReq) GetStart() uint32 {
	if m != nil && m.Start != nil {
		return *m.Start
	}
	return 0
}

func (m *GetHotChannelReq) GetCount() uint32 {
	if m != nil && m.Count != nil {
		return *m.Count
	}
	return 0
}

func (m *GetHotChannelReq) GetTs() uint32 {
	if m != nil && m.Ts != nil {
		return *m.Ts
	}
	return 0
}

func (m *GetHotChannelReq) GetUid() uint32 {
	if m != nil && m.Uid != nil {
		return *m.Uid
	}
	return 0
}

type GetHotChannelResp struct {
	ChannelIdList        []uint32 `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
	ReachEnd             *bool    `protobuf:"varint,2,req,name=reach_end,json=reachEnd" json:"reach_end,omitempty"`
	ActivityIdList       []uint32 `protobuf:"varint,3,rep,name=activity_id_list,json=activityIdList" json:"activity_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetHotChannelResp) Reset()         { *m = GetHotChannelResp{} }
func (m *GetHotChannelResp) String() string { return proto.CompactTextString(m) }
func (*GetHotChannelResp) ProtoMessage()    {}
func (*GetHotChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{45}
}
func (m *GetHotChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHotChannelResp.Unmarshal(m, b)
}
func (m *GetHotChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHotChannelResp.Marshal(b, m, deterministic)
}
func (dst *GetHotChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHotChannelResp.Merge(dst, src)
}
func (m *GetHotChannelResp) XXX_Size() int {
	return xxx_messageInfo_GetHotChannelResp.Size(m)
}
func (m *GetHotChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHotChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetHotChannelResp proto.InternalMessageInfo

func (m *GetHotChannelResp) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

func (m *GetHotChannelResp) GetReachEnd() bool {
	if m != nil && m.ReachEnd != nil {
		return *m.ReachEnd
	}
	return false
}

func (m *GetHotChannelResp) GetActivityIdList() []uint32 {
	if m != nil {
		return m.ActivityIdList
	}
	return nil
}

type ChannelTagAdv struct {
	PicUrl               *string  `protobuf:"bytes,1,req,name=pic_url,json=picUrl" json:"pic_url,omitempty"`
	AdvUrl               *string  `protobuf:"bytes,2,req,name=adv_url,json=advUrl" json:"adv_url,omitempty"`
	ScoreIdx             *uint32  `protobuf:"varint,3,opt,name=score_idx,json=scoreIdx" json:"score_idx,omitempty"`
	AppId                *uint32  `protobuf:"varint,4,opt,name=app_id,json=appId" json:"app_id,omitempty"`
	MarketId             *uint32  `protobuf:"varint,5,opt,name=market_id,json=marketId" json:"market_id,omitempty"`
	StartTime            *uint32  `protobuf:"varint,6,opt,name=start_time,json=startTime" json:"start_time,omitempty"`
	EndTime              *uint32  `protobuf:"varint,7,opt,name=end_time,json=endTime" json:"end_time,omitempty"`
	ClientType           *uint32  `protobuf:"varint,8,opt,name=client_type,json=clientType" json:"client_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelTagAdv) Reset()         { *m = ChannelTagAdv{} }
func (m *ChannelTagAdv) String() string { return proto.CompactTextString(m) }
func (*ChannelTagAdv) ProtoMessage()    {}
func (*ChannelTagAdv) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{46}
}
func (m *ChannelTagAdv) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelTagAdv.Unmarshal(m, b)
}
func (m *ChannelTagAdv) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelTagAdv.Marshal(b, m, deterministic)
}
func (dst *ChannelTagAdv) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelTagAdv.Merge(dst, src)
}
func (m *ChannelTagAdv) XXX_Size() int {
	return xxx_messageInfo_ChannelTagAdv.Size(m)
}
func (m *ChannelTagAdv) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelTagAdv.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelTagAdv proto.InternalMessageInfo

func (m *ChannelTagAdv) GetPicUrl() string {
	if m != nil && m.PicUrl != nil {
		return *m.PicUrl
	}
	return ""
}

func (m *ChannelTagAdv) GetAdvUrl() string {
	if m != nil && m.AdvUrl != nil {
		return *m.AdvUrl
	}
	return ""
}

func (m *ChannelTagAdv) GetScoreIdx() uint32 {
	if m != nil && m.ScoreIdx != nil {
		return *m.ScoreIdx
	}
	return 0
}

func (m *ChannelTagAdv) GetAppId() uint32 {
	if m != nil && m.AppId != nil {
		return *m.AppId
	}
	return 0
}

func (m *ChannelTagAdv) GetMarketId() uint32 {
	if m != nil && m.MarketId != nil {
		return *m.MarketId
	}
	return 0
}

func (m *ChannelTagAdv) GetStartTime() uint32 {
	if m != nil && m.StartTime != nil {
		return *m.StartTime
	}
	return 0
}

func (m *ChannelTagAdv) GetEndTime() uint32 {
	if m != nil && m.EndTime != nil {
		return *m.EndTime
	}
	return 0
}

func (m *ChannelTagAdv) GetClientType() uint32 {
	if m != nil && m.ClientType != nil {
		return *m.ClientType
	}
	return 0
}

type GetChannelTagAdvReq struct {
	AdvType              *uint32  `protobuf:"varint,1,opt,name=adv_type,json=advType" json:"adv_type,omitempty"`
	AppId                *uint32  `protobuf:"varint,2,opt,name=app_id,json=appId" json:"app_id,omitempty"`
	MarketId             *uint32  `protobuf:"varint,3,opt,name=market_id,json=marketId" json:"market_id,omitempty"`
	IsInterior           *bool    `protobuf:"varint,4,opt,name=is_interior,json=isInterior" json:"is_interior,omitempty"`
	ClientType           *uint32  `protobuf:"varint,5,opt,name=client_type,json=clientType" json:"client_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelTagAdvReq) Reset()         { *m = GetChannelTagAdvReq{} }
func (m *GetChannelTagAdvReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelTagAdvReq) ProtoMessage()    {}
func (*GetChannelTagAdvReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{47}
}
func (m *GetChannelTagAdvReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelTagAdvReq.Unmarshal(m, b)
}
func (m *GetChannelTagAdvReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelTagAdvReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelTagAdvReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelTagAdvReq.Merge(dst, src)
}
func (m *GetChannelTagAdvReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelTagAdvReq.Size(m)
}
func (m *GetChannelTagAdvReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelTagAdvReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelTagAdvReq proto.InternalMessageInfo

func (m *GetChannelTagAdvReq) GetAdvType() uint32 {
	if m != nil && m.AdvType != nil {
		return *m.AdvType
	}
	return 0
}

func (m *GetChannelTagAdvReq) GetAppId() uint32 {
	if m != nil && m.AppId != nil {
		return *m.AppId
	}
	return 0
}

func (m *GetChannelTagAdvReq) GetMarketId() uint32 {
	if m != nil && m.MarketId != nil {
		return *m.MarketId
	}
	return 0
}

func (m *GetChannelTagAdvReq) GetIsInterior() bool {
	if m != nil && m.IsInterior != nil {
		return *m.IsInterior
	}
	return false
}

func (m *GetChannelTagAdvReq) GetClientType() uint32 {
	if m != nil && m.ClientType != nil {
		return *m.ClientType
	}
	return 0
}

type GetChannelTagAdvResp struct {
	TagAdvList           []*ChannelTagAdv `protobuf:"bytes,1,rep,name=tag_adv_list,json=tagAdvList" json:"tag_adv_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetChannelTagAdvResp) Reset()         { *m = GetChannelTagAdvResp{} }
func (m *GetChannelTagAdvResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelTagAdvResp) ProtoMessage()    {}
func (*GetChannelTagAdvResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{48}
}
func (m *GetChannelTagAdvResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelTagAdvResp.Unmarshal(m, b)
}
func (m *GetChannelTagAdvResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelTagAdvResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelTagAdvResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelTagAdvResp.Merge(dst, src)
}
func (m *GetChannelTagAdvResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelTagAdvResp.Size(m)
}
func (m *GetChannelTagAdvResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelTagAdvResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelTagAdvResp proto.InternalMessageInfo

func (m *GetChannelTagAdvResp) GetTagAdvList() []*ChannelTagAdv {
	if m != nil {
		return m.TagAdvList
	}
	return nil
}

type AddChannelTagAdvReq struct {
	PicUrl               *string  `protobuf:"bytes,1,req,name=pic_url,json=picUrl" json:"pic_url,omitempty"`
	AdvUrl               *string  `protobuf:"bytes,2,req,name=adv_url,json=advUrl" json:"adv_url,omitempty"`
	AdvType              *uint32  `protobuf:"varint,3,opt,name=adv_type,json=advType" json:"adv_type,omitempty"`
	ScoreIdx             *uint32  `protobuf:"varint,4,opt,name=score_idx,json=scoreIdx" json:"score_idx,omitempty"`
	MarketId             *uint32  `protobuf:"varint,5,opt,name=market_id,json=marketId" json:"market_id,omitempty"`
	AppId                *uint32  `protobuf:"varint,6,opt,name=app_id,json=appId" json:"app_id,omitempty"`
	StartTime            *string  `protobuf:"bytes,7,opt,name=start_time,json=startTime" json:"start_time,omitempty"`
	EndTime              *string  `protobuf:"bytes,8,opt,name=end_time,json=endTime" json:"end_time,omitempty"`
	ClientType           *uint32  `protobuf:"varint,9,opt,name=client_type,json=clientType" json:"client_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddChannelTagAdvReq) Reset()         { *m = AddChannelTagAdvReq{} }
func (m *AddChannelTagAdvReq) String() string { return proto.CompactTextString(m) }
func (*AddChannelTagAdvReq) ProtoMessage()    {}
func (*AddChannelTagAdvReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{49}
}
func (m *AddChannelTagAdvReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddChannelTagAdvReq.Unmarshal(m, b)
}
func (m *AddChannelTagAdvReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddChannelTagAdvReq.Marshal(b, m, deterministic)
}
func (dst *AddChannelTagAdvReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddChannelTagAdvReq.Merge(dst, src)
}
func (m *AddChannelTagAdvReq) XXX_Size() int {
	return xxx_messageInfo_AddChannelTagAdvReq.Size(m)
}
func (m *AddChannelTagAdvReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddChannelTagAdvReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddChannelTagAdvReq proto.InternalMessageInfo

func (m *AddChannelTagAdvReq) GetPicUrl() string {
	if m != nil && m.PicUrl != nil {
		return *m.PicUrl
	}
	return ""
}

func (m *AddChannelTagAdvReq) GetAdvUrl() string {
	if m != nil && m.AdvUrl != nil {
		return *m.AdvUrl
	}
	return ""
}

func (m *AddChannelTagAdvReq) GetAdvType() uint32 {
	if m != nil && m.AdvType != nil {
		return *m.AdvType
	}
	return 0
}

func (m *AddChannelTagAdvReq) GetScoreIdx() uint32 {
	if m != nil && m.ScoreIdx != nil {
		return *m.ScoreIdx
	}
	return 0
}

func (m *AddChannelTagAdvReq) GetMarketId() uint32 {
	if m != nil && m.MarketId != nil {
		return *m.MarketId
	}
	return 0
}

func (m *AddChannelTagAdvReq) GetAppId() uint32 {
	if m != nil && m.AppId != nil {
		return *m.AppId
	}
	return 0
}

func (m *AddChannelTagAdvReq) GetStartTime() string {
	if m != nil && m.StartTime != nil {
		return *m.StartTime
	}
	return ""
}

func (m *AddChannelTagAdvReq) GetEndTime() string {
	if m != nil && m.EndTime != nil {
		return *m.EndTime
	}
	return ""
}

func (m *AddChannelTagAdvReq) GetClientType() uint32 {
	if m != nil && m.ClientType != nil {
		return *m.ClientType
	}
	return 0
}

type AddChannelTagAdvResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddChannelTagAdvResp) Reset()         { *m = AddChannelTagAdvResp{} }
func (m *AddChannelTagAdvResp) String() string { return proto.CompactTextString(m) }
func (*AddChannelTagAdvResp) ProtoMessage()    {}
func (*AddChannelTagAdvResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{50}
}
func (m *AddChannelTagAdvResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddChannelTagAdvResp.Unmarshal(m, b)
}
func (m *AddChannelTagAdvResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddChannelTagAdvResp.Marshal(b, m, deterministic)
}
func (dst *AddChannelTagAdvResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddChannelTagAdvResp.Merge(dst, src)
}
func (m *AddChannelTagAdvResp) XXX_Size() int {
	return xxx_messageInfo_AddChannelTagAdvResp.Size(m)
}
func (m *AddChannelTagAdvResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddChannelTagAdvResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddChannelTagAdvResp proto.InternalMessageInfo

type DelChannelTagAdvReq struct {
	PicUrl               *string  `protobuf:"bytes,1,req,name=pic_url,json=picUrl" json:"pic_url,omitempty"`
	MarketId             *uint32  `protobuf:"varint,2,opt,name=market_id,json=marketId" json:"market_id,omitempty"`
	AppId                *uint32  `protobuf:"varint,3,opt,name=app_id,json=appId" json:"app_id,omitempty"`
	AdvType              *uint32  `protobuf:"varint,4,opt,name=adv_type,json=advType" json:"adv_type,omitempty"`
	ClientType           *uint32  `protobuf:"varint,5,opt,name=client_type,json=clientType" json:"client_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelChannelTagAdvReq) Reset()         { *m = DelChannelTagAdvReq{} }
func (m *DelChannelTagAdvReq) String() string { return proto.CompactTextString(m) }
func (*DelChannelTagAdvReq) ProtoMessage()    {}
func (*DelChannelTagAdvReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{51}
}
func (m *DelChannelTagAdvReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelChannelTagAdvReq.Unmarshal(m, b)
}
func (m *DelChannelTagAdvReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelChannelTagAdvReq.Marshal(b, m, deterministic)
}
func (dst *DelChannelTagAdvReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelChannelTagAdvReq.Merge(dst, src)
}
func (m *DelChannelTagAdvReq) XXX_Size() int {
	return xxx_messageInfo_DelChannelTagAdvReq.Size(m)
}
func (m *DelChannelTagAdvReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelChannelTagAdvReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelChannelTagAdvReq proto.InternalMessageInfo

func (m *DelChannelTagAdvReq) GetPicUrl() string {
	if m != nil && m.PicUrl != nil {
		return *m.PicUrl
	}
	return ""
}

func (m *DelChannelTagAdvReq) GetMarketId() uint32 {
	if m != nil && m.MarketId != nil {
		return *m.MarketId
	}
	return 0
}

func (m *DelChannelTagAdvReq) GetAppId() uint32 {
	if m != nil && m.AppId != nil {
		return *m.AppId
	}
	return 0
}

func (m *DelChannelTagAdvReq) GetAdvType() uint32 {
	if m != nil && m.AdvType != nil {
		return *m.AdvType
	}
	return 0
}

func (m *DelChannelTagAdvReq) GetClientType() uint32 {
	if m != nil && m.ClientType != nil {
		return *m.ClientType
	}
	return 0
}

type DelChannelTagAdvResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelChannelTagAdvResp) Reset()         { *m = DelChannelTagAdvResp{} }
func (m *DelChannelTagAdvResp) String() string { return proto.CompactTextString(m) }
func (*DelChannelTagAdvResp) ProtoMessage()    {}
func (*DelChannelTagAdvResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{52}
}
func (m *DelChannelTagAdvResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelChannelTagAdvResp.Unmarshal(m, b)
}
func (m *DelChannelTagAdvResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelChannelTagAdvResp.Marshal(b, m, deterministic)
}
func (dst *DelChannelTagAdvResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelChannelTagAdvResp.Merge(dst, src)
}
func (m *DelChannelTagAdvResp) XXX_Size() int {
	return xxx_messageInfo_DelChannelTagAdvResp.Size(m)
}
func (m *DelChannelTagAdvResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelChannelTagAdvResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelChannelTagAdvResp proto.InternalMessageInfo

type AddChannelGiftReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	Gift                 *uint32  `protobuf:"varint,2,req,name=gift" json:"gift,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddChannelGiftReq) Reset()         { *m = AddChannelGiftReq{} }
func (m *AddChannelGiftReq) String() string { return proto.CompactTextString(m) }
func (*AddChannelGiftReq) ProtoMessage()    {}
func (*AddChannelGiftReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{53}
}
func (m *AddChannelGiftReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddChannelGiftReq.Unmarshal(m, b)
}
func (m *AddChannelGiftReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddChannelGiftReq.Marshal(b, m, deterministic)
}
func (dst *AddChannelGiftReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddChannelGiftReq.Merge(dst, src)
}
func (m *AddChannelGiftReq) XXX_Size() int {
	return xxx_messageInfo_AddChannelGiftReq.Size(m)
}
func (m *AddChannelGiftReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddChannelGiftReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddChannelGiftReq proto.InternalMessageInfo

func (m *AddChannelGiftReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *AddChannelGiftReq) GetGift() uint32 {
	if m != nil && m.Gift != nil {
		return *m.Gift
	}
	return 0
}

type AddChannelGiftResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddChannelGiftResp) Reset()         { *m = AddChannelGiftResp{} }
func (m *AddChannelGiftResp) String() string { return proto.CompactTextString(m) }
func (*AddChannelGiftResp) ProtoMessage()    {}
func (*AddChannelGiftResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{54}
}
func (m *AddChannelGiftResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddChannelGiftResp.Unmarshal(m, b)
}
func (m *AddChannelGiftResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddChannelGiftResp.Marshal(b, m, deterministic)
}
func (dst *AddChannelGiftResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddChannelGiftResp.Merge(dst, src)
}
func (m *AddChannelGiftResp) XXX_Size() int {
	return xxx_messageInfo_AddChannelGiftResp.Size(m)
}
func (m *AddChannelGiftResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddChannelGiftResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddChannelGiftResp proto.InternalMessageInfo

type GetRankChannelsReq struct {
	RankType             *uint32  `protobuf:"varint,1,req,name=rank_type,json=rankType" json:"rank_type,omitempty"`
	Page                 *uint32  `protobuf:"varint,2,req,name=page" json:"page,omitempty"`
	Count                *uint32  `protobuf:"varint,3,req,name=count" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRankChannelsReq) Reset()         { *m = GetRankChannelsReq{} }
func (m *GetRankChannelsReq) String() string { return proto.CompactTextString(m) }
func (*GetRankChannelsReq) ProtoMessage()    {}
func (*GetRankChannelsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{55}
}
func (m *GetRankChannelsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRankChannelsReq.Unmarshal(m, b)
}
func (m *GetRankChannelsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRankChannelsReq.Marshal(b, m, deterministic)
}
func (dst *GetRankChannelsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRankChannelsReq.Merge(dst, src)
}
func (m *GetRankChannelsReq) XXX_Size() int {
	return xxx_messageInfo_GetRankChannelsReq.Size(m)
}
func (m *GetRankChannelsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRankChannelsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRankChannelsReq proto.InternalMessageInfo

func (m *GetRankChannelsReq) GetRankType() uint32 {
	if m != nil && m.RankType != nil {
		return *m.RankType
	}
	return 0
}

func (m *GetRankChannelsReq) GetPage() uint32 {
	if m != nil && m.Page != nil {
		return *m.Page
	}
	return 0
}

func (m *GetRankChannelsReq) GetCount() uint32 {
	if m != nil && m.Count != nil {
		return *m.Count
	}
	return 0
}

type GetRankChannelsResp struct {
	ChannelIdList        []uint32 `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
	ReachEnd             *bool    `protobuf:"varint,2,req,name=reach_end,json=reachEnd" json:"reach_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRankChannelsResp) Reset()         { *m = GetRankChannelsResp{} }
func (m *GetRankChannelsResp) String() string { return proto.CompactTextString(m) }
func (*GetRankChannelsResp) ProtoMessage()    {}
func (*GetRankChannelsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{56}
}
func (m *GetRankChannelsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRankChannelsResp.Unmarshal(m, b)
}
func (m *GetRankChannelsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRankChannelsResp.Marshal(b, m, deterministic)
}
func (dst *GetRankChannelsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRankChannelsResp.Merge(dst, src)
}
func (m *GetRankChannelsResp) XXX_Size() int {
	return xxx_messageInfo_GetRankChannelsResp.Size(m)
}
func (m *GetRankChannelsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRankChannelsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRankChannelsResp proto.InternalMessageInfo

func (m *GetRankChannelsResp) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

func (m *GetRankChannelsResp) GetReachEnd() bool {
	if m != nil && m.ReachEnd != nil {
		return *m.ReachEnd
	}
	return false
}

type ChannelTagRankItem struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	TagId                *uint32  `protobuf:"varint,2,req,name=tag_id,json=tagId" json:"tag_id,omitempty"`
	Value                *uint32  `protobuf:"varint,3,req,name=value" json:"value,omitempty"`
	Rank                 *uint32  `protobuf:"varint,4,req,name=rank" json:"rank,omitempty"`
	LastWeekRank         *uint32  `protobuf:"varint,5,req,name=last_week_rank,json=lastWeekRank" json:"last_week_rank,omitempty"`
	TagName              *string  `protobuf:"bytes,6,req,name=tag_name,json=tagName" json:"tag_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelTagRankItem) Reset()         { *m = ChannelTagRankItem{} }
func (m *ChannelTagRankItem) String() string { return proto.CompactTextString(m) }
func (*ChannelTagRankItem) ProtoMessage()    {}
func (*ChannelTagRankItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{57}
}
func (m *ChannelTagRankItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelTagRankItem.Unmarshal(m, b)
}
func (m *ChannelTagRankItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelTagRankItem.Marshal(b, m, deterministic)
}
func (dst *ChannelTagRankItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelTagRankItem.Merge(dst, src)
}
func (m *ChannelTagRankItem) XXX_Size() int {
	return xxx_messageInfo_ChannelTagRankItem.Size(m)
}
func (m *ChannelTagRankItem) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelTagRankItem.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelTagRankItem proto.InternalMessageInfo

func (m *ChannelTagRankItem) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *ChannelTagRankItem) GetTagId() uint32 {
	if m != nil && m.TagId != nil {
		return *m.TagId
	}
	return 0
}

func (m *ChannelTagRankItem) GetValue() uint32 {
	if m != nil && m.Value != nil {
		return *m.Value
	}
	return 0
}

func (m *ChannelTagRankItem) GetRank() uint32 {
	if m != nil && m.Rank != nil {
		return *m.Rank
	}
	return 0
}

func (m *ChannelTagRankItem) GetLastWeekRank() uint32 {
	if m != nil && m.LastWeekRank != nil {
		return *m.LastWeekRank
	}
	return 0
}

func (m *ChannelTagRankItem) GetTagName() string {
	if m != nil && m.TagName != nil {
		return *m.TagName
	}
	return ""
}

type GetChannelTagRankReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	TagId                *uint32  `protobuf:"varint,2,opt,name=tag_id,json=tagId" json:"tag_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelTagRankReq) Reset()         { *m = GetChannelTagRankReq{} }
func (m *GetChannelTagRankReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelTagRankReq) ProtoMessage()    {}
func (*GetChannelTagRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{58}
}
func (m *GetChannelTagRankReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelTagRankReq.Unmarshal(m, b)
}
func (m *GetChannelTagRankReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelTagRankReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelTagRankReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelTagRankReq.Merge(dst, src)
}
func (m *GetChannelTagRankReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelTagRankReq.Size(m)
}
func (m *GetChannelTagRankReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelTagRankReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelTagRankReq proto.InternalMessageInfo

func (m *GetChannelTagRankReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *GetChannelTagRankReq) GetTagId() uint32 {
	if m != nil && m.TagId != nil {
		return *m.TagId
	}
	return 0
}

type GetChannelTagRankResp struct {
	Ranks                []*ChannelTagRankItem `protobuf:"bytes,1,rep,name=ranks" json:"ranks,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetChannelTagRankResp) Reset()         { *m = GetChannelTagRankResp{} }
func (m *GetChannelTagRankResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelTagRankResp) ProtoMessage()    {}
func (*GetChannelTagRankResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{59}
}
func (m *GetChannelTagRankResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelTagRankResp.Unmarshal(m, b)
}
func (m *GetChannelTagRankResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelTagRankResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelTagRankResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelTagRankResp.Merge(dst, src)
}
func (m *GetChannelTagRankResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelTagRankResp.Size(m)
}
func (m *GetChannelTagRankResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelTagRankResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelTagRankResp proto.InternalMessageInfo

func (m *GetChannelTagRankResp) GetRanks() []*ChannelTagRankItem {
	if m != nil {
		return m.Ranks
	}
	return nil
}

type BatchGetChannelRecommendLevelReq struct {
	CidList              []uint32 `protobuf:"varint,1,rep,name=cid_list,json=cidList" json:"cid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetChannelRecommendLevelReq) Reset()         { *m = BatchGetChannelRecommendLevelReq{} }
func (m *BatchGetChannelRecommendLevelReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelRecommendLevelReq) ProtoMessage()    {}
func (*BatchGetChannelRecommendLevelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{60}
}
func (m *BatchGetChannelRecommendLevelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelRecommendLevelReq.Unmarshal(m, b)
}
func (m *BatchGetChannelRecommendLevelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelRecommendLevelReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelRecommendLevelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelRecommendLevelReq.Merge(dst, src)
}
func (m *BatchGetChannelRecommendLevelReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelRecommendLevelReq.Size(m)
}
func (m *BatchGetChannelRecommendLevelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelRecommendLevelReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelRecommendLevelReq proto.InternalMessageInfo

func (m *BatchGetChannelRecommendLevelReq) GetCidList() []uint32 {
	if m != nil {
		return m.CidList
	}
	return nil
}

type ChannelRecommendLevel struct {
	Cid                  *uint32  `protobuf:"varint,1,req,name=cid" json:"cid,omitempty"`
	RecommendLevel       *uint32  `protobuf:"varint,2,req,name=recommend_level,json=recommendLevel" json:"recommend_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelRecommendLevel) Reset()         { *m = ChannelRecommendLevel{} }
func (m *ChannelRecommendLevel) String() string { return proto.CompactTextString(m) }
func (*ChannelRecommendLevel) ProtoMessage()    {}
func (*ChannelRecommendLevel) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{61}
}
func (m *ChannelRecommendLevel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelRecommendLevel.Unmarshal(m, b)
}
func (m *ChannelRecommendLevel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelRecommendLevel.Marshal(b, m, deterministic)
}
func (dst *ChannelRecommendLevel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelRecommendLevel.Merge(dst, src)
}
func (m *ChannelRecommendLevel) XXX_Size() int {
	return xxx_messageInfo_ChannelRecommendLevel.Size(m)
}
func (m *ChannelRecommendLevel) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelRecommendLevel.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelRecommendLevel proto.InternalMessageInfo

func (m *ChannelRecommendLevel) GetCid() uint32 {
	if m != nil && m.Cid != nil {
		return *m.Cid
	}
	return 0
}

func (m *ChannelRecommendLevel) GetRecommendLevel() uint32 {
	if m != nil && m.RecommendLevel != nil {
		return *m.RecommendLevel
	}
	return 0
}

type BatchGetChannelRecommendLevelResp struct {
	RecommendLevelList   []*ChannelRecommendLevel `protobuf:"bytes,1,rep,name=recommend_level_list,json=recommendLevelList" json:"recommend_level_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *BatchGetChannelRecommendLevelResp) Reset()         { *m = BatchGetChannelRecommendLevelResp{} }
func (m *BatchGetChannelRecommendLevelResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelRecommendLevelResp) ProtoMessage()    {}
func (*BatchGetChannelRecommendLevelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{62}
}
func (m *BatchGetChannelRecommendLevelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelRecommendLevelResp.Unmarshal(m, b)
}
func (m *BatchGetChannelRecommendLevelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelRecommendLevelResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelRecommendLevelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelRecommendLevelResp.Merge(dst, src)
}
func (m *BatchGetChannelRecommendLevelResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelRecommendLevelResp.Size(m)
}
func (m *BatchGetChannelRecommendLevelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelRecommendLevelResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelRecommendLevelResp proto.InternalMessageInfo

func (m *BatchGetChannelRecommendLevelResp) GetRecommendLevelList() []*ChannelRecommendLevel {
	if m != nil {
		return m.RecommendLevelList
	}
	return nil
}

// 根据tagId获取快速进房的推荐房
type GetQuickRecommendChannelByTagIdReq struct {
	TagId                *uint32  `protobuf:"varint,1,req,name=tag_id,json=tagId" json:"tag_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetQuickRecommendChannelByTagIdReq) Reset()         { *m = GetQuickRecommendChannelByTagIdReq{} }
func (m *GetQuickRecommendChannelByTagIdReq) String() string { return proto.CompactTextString(m) }
func (*GetQuickRecommendChannelByTagIdReq) ProtoMessage()    {}
func (*GetQuickRecommendChannelByTagIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{63}
}
func (m *GetQuickRecommendChannelByTagIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetQuickRecommendChannelByTagIdReq.Unmarshal(m, b)
}
func (m *GetQuickRecommendChannelByTagIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetQuickRecommendChannelByTagIdReq.Marshal(b, m, deterministic)
}
func (dst *GetQuickRecommendChannelByTagIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetQuickRecommendChannelByTagIdReq.Merge(dst, src)
}
func (m *GetQuickRecommendChannelByTagIdReq) XXX_Size() int {
	return xxx_messageInfo_GetQuickRecommendChannelByTagIdReq.Size(m)
}
func (m *GetQuickRecommendChannelByTagIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetQuickRecommendChannelByTagIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetQuickRecommendChannelByTagIdReq proto.InternalMessageInfo

func (m *GetQuickRecommendChannelByTagIdReq) GetTagId() uint32 {
	if m != nil && m.TagId != nil {
		return *m.TagId
	}
	return 0
}

type GetQuickRecommendChannelByTagIdResp struct {
	ChannelId            *uint32  `protobuf:"varint,1,opt,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetQuickRecommendChannelByTagIdResp) Reset()         { *m = GetQuickRecommendChannelByTagIdResp{} }
func (m *GetQuickRecommendChannelByTagIdResp) String() string { return proto.CompactTextString(m) }
func (*GetQuickRecommendChannelByTagIdResp) ProtoMessage()    {}
func (*GetQuickRecommendChannelByTagIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{64}
}
func (m *GetQuickRecommendChannelByTagIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetQuickRecommendChannelByTagIdResp.Unmarshal(m, b)
}
func (m *GetQuickRecommendChannelByTagIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetQuickRecommendChannelByTagIdResp.Marshal(b, m, deterministic)
}
func (dst *GetQuickRecommendChannelByTagIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetQuickRecommendChannelByTagIdResp.Merge(dst, src)
}
func (m *GetQuickRecommendChannelByTagIdResp) XXX_Size() int {
	return xxx_messageInfo_GetQuickRecommendChannelByTagIdResp.Size(m)
}
func (m *GetQuickRecommendChannelByTagIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetQuickRecommendChannelByTagIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetQuickRecommendChannelByTagIdResp proto.InternalMessageInfo

func (m *GetQuickRecommendChannelByTagIdResp) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

// 每天的时间段
type DayTimeSection struct {
	BeginTs              *uint32  `protobuf:"varint,1,opt,name=begin_ts,json=beginTs" json:"begin_ts,omitempty"`
	EndTs                *uint32  `protobuf:"varint,2,opt,name=end_ts,json=endTs" json:"end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DayTimeSection) Reset()         { *m = DayTimeSection{} }
func (m *DayTimeSection) String() string { return proto.CompactTextString(m) }
func (*DayTimeSection) ProtoMessage()    {}
func (*DayTimeSection) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{65}
}
func (m *DayTimeSection) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DayTimeSection.Unmarshal(m, b)
}
func (m *DayTimeSection) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DayTimeSection.Marshal(b, m, deterministic)
}
func (dst *DayTimeSection) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DayTimeSection.Merge(dst, src)
}
func (m *DayTimeSection) XXX_Size() int {
	return xxx_messageInfo_DayTimeSection.Size(m)
}
func (m *DayTimeSection) XXX_DiscardUnknown() {
	xxx_messageInfo_DayTimeSection.DiscardUnknown(m)
}

var xxx_messageInfo_DayTimeSection proto.InternalMessageInfo

func (m *DayTimeSection) GetBeginTs() uint32 {
	if m != nil && m.BeginTs != nil {
		return *m.BeginTs
	}
	return 0
}

func (m *DayTimeSection) GetEndTs() uint32 {
	if m != nil && m.EndTs != nil {
		return *m.EndTs
	}
	return 0
}

type DayTimeSectionList struct {
	SectList             []*DayTimeSection `protobuf:"bytes,1,rep,name=sect_list,json=sectList" json:"sect_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *DayTimeSectionList) Reset()         { *m = DayTimeSectionList{} }
func (m *DayTimeSectionList) String() string { return proto.CompactTextString(m) }
func (*DayTimeSectionList) ProtoMessage()    {}
func (*DayTimeSectionList) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{66}
}
func (m *DayTimeSectionList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DayTimeSectionList.Unmarshal(m, b)
}
func (m *DayTimeSectionList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DayTimeSectionList.Marshal(b, m, deterministic)
}
func (dst *DayTimeSectionList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DayTimeSectionList.Merge(dst, src)
}
func (m *DayTimeSectionList) XXX_Size() int {
	return xxx_messageInfo_DayTimeSectionList.Size(m)
}
func (m *DayTimeSectionList) XXX_DiscardUnknown() {
	xxx_messageInfo_DayTimeSectionList.DiscardUnknown(m)
}

var xxx_messageInfo_DayTimeSectionList proto.InternalMessageInfo

func (m *DayTimeSectionList) GetSectList() []*DayTimeSection {
	if m != nil {
		return m.SectList
	}
	return nil
}

type LiveQuickEnterPrepareInfo struct {
	Type                 *uint32             `protobuf:"varint,1,opt,name=type" json:"type,omitempty"`
	QuickLevel           *uint32             `protobuf:"varint,2,opt,name=quick_level,json=quickLevel" json:"quick_level,omitempty"`
	Sections             *TimeSection        `protobuf:"bytes,3,opt,name=sections" json:"sections,omitempty"`
	DaySectionList       *DayTimeSectionList `protobuf:"bytes,4,opt,name=day_section_list,json=daySectionList" json:"day_section_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *LiveQuickEnterPrepareInfo) Reset()         { *m = LiveQuickEnterPrepareInfo{} }
func (m *LiveQuickEnterPrepareInfo) String() string { return proto.CompactTextString(m) }
func (*LiveQuickEnterPrepareInfo) ProtoMessage()    {}
func (*LiveQuickEnterPrepareInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{67}
}
func (m *LiveQuickEnterPrepareInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LiveQuickEnterPrepareInfo.Unmarshal(m, b)
}
func (m *LiveQuickEnterPrepareInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LiveQuickEnterPrepareInfo.Marshal(b, m, deterministic)
}
func (dst *LiveQuickEnterPrepareInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LiveQuickEnterPrepareInfo.Merge(dst, src)
}
func (m *LiveQuickEnterPrepareInfo) XXX_Size() int {
	return xxx_messageInfo_LiveQuickEnterPrepareInfo.Size(m)
}
func (m *LiveQuickEnterPrepareInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_LiveQuickEnterPrepareInfo.DiscardUnknown(m)
}

var xxx_messageInfo_LiveQuickEnterPrepareInfo proto.InternalMessageInfo

func (m *LiveQuickEnterPrepareInfo) GetType() uint32 {
	if m != nil && m.Type != nil {
		return *m.Type
	}
	return 0
}

func (m *LiveQuickEnterPrepareInfo) GetQuickLevel() uint32 {
	if m != nil && m.QuickLevel != nil {
		return *m.QuickLevel
	}
	return 0
}

func (m *LiveQuickEnterPrepareInfo) GetSections() *TimeSection {
	if m != nil {
		return m.Sections
	}
	return nil
}

func (m *LiveQuickEnterPrepareInfo) GetDaySectionList() *DayTimeSectionList {
	if m != nil {
		return m.DaySectionList
	}
	return nil
}

// 语音直播房预备库信息
type LivePrepareChannelInfo struct {
	Id                   *uint32                    `protobuf:"varint,1,req,name=id" json:"id,omitempty"`
	Uid                  *uint32                    `protobuf:"varint,2,req,name=uid" json:"uid,omitempty"`
	ChannelId            *uint32                    `protobuf:"varint,3,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	GuildId              *uint32                    `protobuf:"varint,4,req,name=guild_id,json=guildId" json:"guild_id,omitempty"`
	TagId                *uint32                    `protobuf:"varint,5,req,name=tag_id,json=tagId" json:"tag_id,omitempty"`
	Level                *uint32                    `protobuf:"varint,6,req,name=level" json:"level,omitempty"`
	TagSections          *TimeSection               `protobuf:"bytes,7,req,name=tag_sections,json=tagSections" json:"tag_sections,omitempty"`
	SubTag               *string                    `protobuf:"bytes,8,opt,name=sub_tag,json=subTag" json:"sub_tag,omitempty"`
	SubSections          *TimeSection               `protobuf:"bytes,9,opt,name=sub_sections,json=subSections" json:"sub_sections,omitempty"`
	TagSectionList       *DayTimeSectionList        `protobuf:"bytes,10,opt,name=tag_section_list,json=tagSectionList" json:"tag_section_list,omitempty"`
	ConfType             *uint32                    `protobuf:"varint,11,opt,name=conf_type,json=confType" json:"conf_type,omitempty"`
	QuickEnterInfo       *LiveQuickEnterPrepareInfo `protobuf:"bytes,12,opt,name=quick_enter_info,json=quickEnterInfo" json:"quick_enter_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *LivePrepareChannelInfo) Reset()         { *m = LivePrepareChannelInfo{} }
func (m *LivePrepareChannelInfo) String() string { return proto.CompactTextString(m) }
func (*LivePrepareChannelInfo) ProtoMessage()    {}
func (*LivePrepareChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{68}
}
func (m *LivePrepareChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LivePrepareChannelInfo.Unmarshal(m, b)
}
func (m *LivePrepareChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LivePrepareChannelInfo.Marshal(b, m, deterministic)
}
func (dst *LivePrepareChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LivePrepareChannelInfo.Merge(dst, src)
}
func (m *LivePrepareChannelInfo) XXX_Size() int {
	return xxx_messageInfo_LivePrepareChannelInfo.Size(m)
}
func (m *LivePrepareChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_LivePrepareChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_LivePrepareChannelInfo proto.InternalMessageInfo

func (m *LivePrepareChannelInfo) GetId() uint32 {
	if m != nil && m.Id != nil {
		return *m.Id
	}
	return 0
}

func (m *LivePrepareChannelInfo) GetUid() uint32 {
	if m != nil && m.Uid != nil {
		return *m.Uid
	}
	return 0
}

func (m *LivePrepareChannelInfo) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *LivePrepareChannelInfo) GetGuildId() uint32 {
	if m != nil && m.GuildId != nil {
		return *m.GuildId
	}
	return 0
}

func (m *LivePrepareChannelInfo) GetTagId() uint32 {
	if m != nil && m.TagId != nil {
		return *m.TagId
	}
	return 0
}

func (m *LivePrepareChannelInfo) GetLevel() uint32 {
	if m != nil && m.Level != nil {
		return *m.Level
	}
	return 0
}

func (m *LivePrepareChannelInfo) GetTagSections() *TimeSection {
	if m != nil {
		return m.TagSections
	}
	return nil
}

func (m *LivePrepareChannelInfo) GetSubTag() string {
	if m != nil && m.SubTag != nil {
		return *m.SubTag
	}
	return ""
}

func (m *LivePrepareChannelInfo) GetSubSections() *TimeSection {
	if m != nil {
		return m.SubSections
	}
	return nil
}

func (m *LivePrepareChannelInfo) GetTagSectionList() *DayTimeSectionList {
	if m != nil {
		return m.TagSectionList
	}
	return nil
}

func (m *LivePrepareChannelInfo) GetConfType() uint32 {
	if m != nil && m.ConfType != nil {
		return *m.ConfType
	}
	return 0
}

func (m *LivePrepareChannelInfo) GetQuickEnterInfo() *LiveQuickEnterPrepareInfo {
	if m != nil {
		return m.QuickEnterInfo
	}
	return nil
}

// 增加语音直播推荐库
type AddLivePrepareChannelReq struct {
	ChannelList          []*LivePrepareChannelInfo `protobuf:"bytes,1,rep,name=channel_list,json=channelList" json:"channel_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *AddLivePrepareChannelReq) Reset()         { *m = AddLivePrepareChannelReq{} }
func (m *AddLivePrepareChannelReq) String() string { return proto.CompactTextString(m) }
func (*AddLivePrepareChannelReq) ProtoMessage()    {}
func (*AddLivePrepareChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{69}
}
func (m *AddLivePrepareChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddLivePrepareChannelReq.Unmarshal(m, b)
}
func (m *AddLivePrepareChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddLivePrepareChannelReq.Marshal(b, m, deterministic)
}
func (dst *AddLivePrepareChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddLivePrepareChannelReq.Merge(dst, src)
}
func (m *AddLivePrepareChannelReq) XXX_Size() int {
	return xxx_messageInfo_AddLivePrepareChannelReq.Size(m)
}
func (m *AddLivePrepareChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddLivePrepareChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddLivePrepareChannelReq proto.InternalMessageInfo

func (m *AddLivePrepareChannelReq) GetChannelList() []*LivePrepareChannelInfo {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

type AddLivePrepareChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddLivePrepareChannelResp) Reset()         { *m = AddLivePrepareChannelResp{} }
func (m *AddLivePrepareChannelResp) String() string { return proto.CompactTextString(m) }
func (*AddLivePrepareChannelResp) ProtoMessage()    {}
func (*AddLivePrepareChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{70}
}
func (m *AddLivePrepareChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddLivePrepareChannelResp.Unmarshal(m, b)
}
func (m *AddLivePrepareChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddLivePrepareChannelResp.Marshal(b, m, deterministic)
}
func (dst *AddLivePrepareChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddLivePrepareChannelResp.Merge(dst, src)
}
func (m *AddLivePrepareChannelResp) XXX_Size() int {
	return xxx_messageInfo_AddLivePrepareChannelResp.Size(m)
}
func (m *AddLivePrepareChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddLivePrepareChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddLivePrepareChannelResp proto.InternalMessageInfo

// 分页获取语音直播推荐库
type GetLivePrepareChannelListReq struct {
	Offset               *uint32  `protobuf:"varint,1,req,name=offset" json:"offset,omitempty"`
	Limit                *uint32  `protobuf:"varint,2,req,name=limit" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLivePrepareChannelListReq) Reset()         { *m = GetLivePrepareChannelListReq{} }
func (m *GetLivePrepareChannelListReq) String() string { return proto.CompactTextString(m) }
func (*GetLivePrepareChannelListReq) ProtoMessage()    {}
func (*GetLivePrepareChannelListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{71}
}
func (m *GetLivePrepareChannelListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLivePrepareChannelListReq.Unmarshal(m, b)
}
func (m *GetLivePrepareChannelListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLivePrepareChannelListReq.Marshal(b, m, deterministic)
}
func (dst *GetLivePrepareChannelListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLivePrepareChannelListReq.Merge(dst, src)
}
func (m *GetLivePrepareChannelListReq) XXX_Size() int {
	return xxx_messageInfo_GetLivePrepareChannelListReq.Size(m)
}
func (m *GetLivePrepareChannelListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLivePrepareChannelListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLivePrepareChannelListReq proto.InternalMessageInfo

func (m *GetLivePrepareChannelListReq) GetOffset() uint32 {
	if m != nil && m.Offset != nil {
		return *m.Offset
	}
	return 0
}

func (m *GetLivePrepareChannelListReq) GetLimit() uint32 {
	if m != nil && m.Limit != nil {
		return *m.Limit
	}
	return 0
}

type GetLivePrepareChannelListResp struct {
	InfoList             []*LivePrepareChannelInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList" json:"info_list,omitempty"`
	NextOffset           *uint32                   `protobuf:"varint,2,req,name=next_offset,json=nextOffset" json:"next_offset,omitempty"`
	TotalCount           *uint32                   `protobuf:"varint,3,req,name=total_count,json=totalCount" json:"total_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetLivePrepareChannelListResp) Reset()         { *m = GetLivePrepareChannelListResp{} }
func (m *GetLivePrepareChannelListResp) String() string { return proto.CompactTextString(m) }
func (*GetLivePrepareChannelListResp) ProtoMessage()    {}
func (*GetLivePrepareChannelListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{72}
}
func (m *GetLivePrepareChannelListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLivePrepareChannelListResp.Unmarshal(m, b)
}
func (m *GetLivePrepareChannelListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLivePrepareChannelListResp.Marshal(b, m, deterministic)
}
func (dst *GetLivePrepareChannelListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLivePrepareChannelListResp.Merge(dst, src)
}
func (m *GetLivePrepareChannelListResp) XXX_Size() int {
	return xxx_messageInfo_GetLivePrepareChannelListResp.Size(m)
}
func (m *GetLivePrepareChannelListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLivePrepareChannelListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLivePrepareChannelListResp proto.InternalMessageInfo

func (m *GetLivePrepareChannelListResp) GetInfoList() []*LivePrepareChannelInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetLivePrepareChannelListResp) GetNextOffset() uint32 {
	if m != nil && m.NextOffset != nil {
		return *m.NextOffset
	}
	return 0
}

func (m *GetLivePrepareChannelListResp) GetTotalCount() uint32 {
	if m != nil && m.TotalCount != nil {
		return *m.TotalCount
	}
	return 0
}

// 更新语音直播推荐库
type UpdateLivePrepareChannelReq struct {
	Id                   *uint32                    `protobuf:"varint,1,req,name=id" json:"id,omitempty"`
	Level                *uint32                    `protobuf:"varint,2,opt,name=level" json:"level,omitempty"`
	TagSections          *TimeSection               `protobuf:"bytes,3,opt,name=tag_sections,json=tagSections" json:"tag_sections,omitempty"`
	SubTag               *string                    `protobuf:"bytes,4,opt,name=sub_tag,json=subTag" json:"sub_tag,omitempty"`
	SubSections          *TimeSection               `protobuf:"bytes,5,opt,name=sub_sections,json=subSections" json:"sub_sections,omitempty"`
	TagDaysections       *DayTimeSectionList        `protobuf:"bytes,6,opt,name=tag_daysections,json=tagDaysections" json:"tag_daysections,omitempty"`
	ConfType             *uint32                    `protobuf:"varint,7,opt,name=conf_type,json=confType" json:"conf_type,omitempty"`
	QuickEnterInfo       *LiveQuickEnterPrepareInfo `protobuf:"bytes,8,opt,name=quick_enter_info,json=quickEnterInfo" json:"quick_enter_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *UpdateLivePrepareChannelReq) Reset()         { *m = UpdateLivePrepareChannelReq{} }
func (m *UpdateLivePrepareChannelReq) String() string { return proto.CompactTextString(m) }
func (*UpdateLivePrepareChannelReq) ProtoMessage()    {}
func (*UpdateLivePrepareChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{73}
}
func (m *UpdateLivePrepareChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateLivePrepareChannelReq.Unmarshal(m, b)
}
func (m *UpdateLivePrepareChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateLivePrepareChannelReq.Marshal(b, m, deterministic)
}
func (dst *UpdateLivePrepareChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateLivePrepareChannelReq.Merge(dst, src)
}
func (m *UpdateLivePrepareChannelReq) XXX_Size() int {
	return xxx_messageInfo_UpdateLivePrepareChannelReq.Size(m)
}
func (m *UpdateLivePrepareChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateLivePrepareChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateLivePrepareChannelReq proto.InternalMessageInfo

func (m *UpdateLivePrepareChannelReq) GetId() uint32 {
	if m != nil && m.Id != nil {
		return *m.Id
	}
	return 0
}

func (m *UpdateLivePrepareChannelReq) GetLevel() uint32 {
	if m != nil && m.Level != nil {
		return *m.Level
	}
	return 0
}

func (m *UpdateLivePrepareChannelReq) GetTagSections() *TimeSection {
	if m != nil {
		return m.TagSections
	}
	return nil
}

func (m *UpdateLivePrepareChannelReq) GetSubTag() string {
	if m != nil && m.SubTag != nil {
		return *m.SubTag
	}
	return ""
}

func (m *UpdateLivePrepareChannelReq) GetSubSections() *TimeSection {
	if m != nil {
		return m.SubSections
	}
	return nil
}

func (m *UpdateLivePrepareChannelReq) GetTagDaysections() *DayTimeSectionList {
	if m != nil {
		return m.TagDaysections
	}
	return nil
}

func (m *UpdateLivePrepareChannelReq) GetConfType() uint32 {
	if m != nil && m.ConfType != nil {
		return *m.ConfType
	}
	return 0
}

func (m *UpdateLivePrepareChannelReq) GetQuickEnterInfo() *LiveQuickEnterPrepareInfo {
	if m != nil {
		return m.QuickEnterInfo
	}
	return nil
}

type UpdateLivePrepareChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateLivePrepareChannelResp) Reset()         { *m = UpdateLivePrepareChannelResp{} }
func (m *UpdateLivePrepareChannelResp) String() string { return proto.CompactTextString(m) }
func (*UpdateLivePrepareChannelResp) ProtoMessage()    {}
func (*UpdateLivePrepareChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{74}
}
func (m *UpdateLivePrepareChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateLivePrepareChannelResp.Unmarshal(m, b)
}
func (m *UpdateLivePrepareChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateLivePrepareChannelResp.Marshal(b, m, deterministic)
}
func (dst *UpdateLivePrepareChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateLivePrepareChannelResp.Merge(dst, src)
}
func (m *UpdateLivePrepareChannelResp) XXX_Size() int {
	return xxx_messageInfo_UpdateLivePrepareChannelResp.Size(m)
}
func (m *UpdateLivePrepareChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateLivePrepareChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateLivePrepareChannelResp proto.InternalMessageInfo

// 删除语音直播推荐库
type DelLivePrepareChannelReq struct {
	IdList               []uint32 `protobuf:"varint,1,rep,name=id_list,json=idList" json:"id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelLivePrepareChannelReq) Reset()         { *m = DelLivePrepareChannelReq{} }
func (m *DelLivePrepareChannelReq) String() string { return proto.CompactTextString(m) }
func (*DelLivePrepareChannelReq) ProtoMessage()    {}
func (*DelLivePrepareChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{75}
}
func (m *DelLivePrepareChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelLivePrepareChannelReq.Unmarshal(m, b)
}
func (m *DelLivePrepareChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelLivePrepareChannelReq.Marshal(b, m, deterministic)
}
func (dst *DelLivePrepareChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelLivePrepareChannelReq.Merge(dst, src)
}
func (m *DelLivePrepareChannelReq) XXX_Size() int {
	return xxx_messageInfo_DelLivePrepareChannelReq.Size(m)
}
func (m *DelLivePrepareChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelLivePrepareChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelLivePrepareChannelReq proto.InternalMessageInfo

func (m *DelLivePrepareChannelReq) GetIdList() []uint32 {
	if m != nil {
		return m.IdList
	}
	return nil
}

type DelLivePrepareChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelLivePrepareChannelResp) Reset()         { *m = DelLivePrepareChannelResp{} }
func (m *DelLivePrepareChannelResp) String() string { return proto.CompactTextString(m) }
func (*DelLivePrepareChannelResp) ProtoMessage()    {}
func (*DelLivePrepareChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{76}
}
func (m *DelLivePrepareChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelLivePrepareChannelResp.Unmarshal(m, b)
}
func (m *DelLivePrepareChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelLivePrepareChannelResp.Marshal(b, m, deterministic)
}
func (dst *DelLivePrepareChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelLivePrepareChannelResp.Merge(dst, src)
}
func (m *DelLivePrepareChannelResp) XXX_Size() int {
	return xxx_messageInfo_DelLivePrepareChannelResp.Size(m)
}
func (m *DelLivePrepareChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelLivePrepareChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelLivePrepareChannelResp proto.InternalMessageInfo

// 清空语音直播推荐库
type ClearLivePrepareChannelReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearLivePrepareChannelReq) Reset()         { *m = ClearLivePrepareChannelReq{} }
func (m *ClearLivePrepareChannelReq) String() string { return proto.CompactTextString(m) }
func (*ClearLivePrepareChannelReq) ProtoMessage()    {}
func (*ClearLivePrepareChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{77}
}
func (m *ClearLivePrepareChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearLivePrepareChannelReq.Unmarshal(m, b)
}
func (m *ClearLivePrepareChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearLivePrepareChannelReq.Marshal(b, m, deterministic)
}
func (dst *ClearLivePrepareChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearLivePrepareChannelReq.Merge(dst, src)
}
func (m *ClearLivePrepareChannelReq) XXX_Size() int {
	return xxx_messageInfo_ClearLivePrepareChannelReq.Size(m)
}
func (m *ClearLivePrepareChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearLivePrepareChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_ClearLivePrepareChannelReq proto.InternalMessageInfo

type ClearLivePrepareChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearLivePrepareChannelResp) Reset()         { *m = ClearLivePrepareChannelResp{} }
func (m *ClearLivePrepareChannelResp) String() string { return proto.CompactTextString(m) }
func (*ClearLivePrepareChannelResp) ProtoMessage()    {}
func (*ClearLivePrepareChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{78}
}
func (m *ClearLivePrepareChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearLivePrepareChannelResp.Unmarshal(m, b)
}
func (m *ClearLivePrepareChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearLivePrepareChannelResp.Marshal(b, m, deterministic)
}
func (dst *ClearLivePrepareChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearLivePrepareChannelResp.Merge(dst, src)
}
func (m *ClearLivePrepareChannelResp) XXX_Size() int {
	return xxx_messageInfo_ClearLivePrepareChannelResp.Size(m)
}
func (m *ClearLivePrepareChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearLivePrepareChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_ClearLivePrepareChannelResp proto.InternalMessageInfo

// 语音直播自动加入推荐库
type AutoAddLivePrepareChannelReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	TagId                *uint32  `protobuf:"varint,2,req,name=tag_id,json=tagId" json:"tag_id,omitempty"`
	Level                *uint32  `protobuf:"varint,3,req,name=level" json:"level,omitempty"`
	Uid                  *uint32  `protobuf:"varint,4,req,name=uid" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AutoAddLivePrepareChannelReq) Reset()         { *m = AutoAddLivePrepareChannelReq{} }
func (m *AutoAddLivePrepareChannelReq) String() string { return proto.CompactTextString(m) }
func (*AutoAddLivePrepareChannelReq) ProtoMessage()    {}
func (*AutoAddLivePrepareChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{79}
}
func (m *AutoAddLivePrepareChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AutoAddLivePrepareChannelReq.Unmarshal(m, b)
}
func (m *AutoAddLivePrepareChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AutoAddLivePrepareChannelReq.Marshal(b, m, deterministic)
}
func (dst *AutoAddLivePrepareChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AutoAddLivePrepareChannelReq.Merge(dst, src)
}
func (m *AutoAddLivePrepareChannelReq) XXX_Size() int {
	return xxx_messageInfo_AutoAddLivePrepareChannelReq.Size(m)
}
func (m *AutoAddLivePrepareChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AutoAddLivePrepareChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_AutoAddLivePrepareChannelReq proto.InternalMessageInfo

func (m *AutoAddLivePrepareChannelReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *AutoAddLivePrepareChannelReq) GetTagId() uint32 {
	if m != nil && m.TagId != nil {
		return *m.TagId
	}
	return 0
}

func (m *AutoAddLivePrepareChannelReq) GetLevel() uint32 {
	if m != nil && m.Level != nil {
		return *m.Level
	}
	return 0
}

func (m *AutoAddLivePrepareChannelReq) GetUid() uint32 {
	if m != nil && m.Uid != nil {
		return *m.Uid
	}
	return 0
}

type AutoAddLivePrepareChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AutoAddLivePrepareChannelResp) Reset()         { *m = AutoAddLivePrepareChannelResp{} }
func (m *AutoAddLivePrepareChannelResp) String() string { return proto.CompactTextString(m) }
func (*AutoAddLivePrepareChannelResp) ProtoMessage()    {}
func (*AutoAddLivePrepareChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{80}
}
func (m *AutoAddLivePrepareChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AutoAddLivePrepareChannelResp.Unmarshal(m, b)
}
func (m *AutoAddLivePrepareChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AutoAddLivePrepareChannelResp.Marshal(b, m, deterministic)
}
func (dst *AutoAddLivePrepareChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AutoAddLivePrepareChannelResp.Merge(dst, src)
}
func (m *AutoAddLivePrepareChannelResp) XXX_Size() int {
	return xxx_messageInfo_AutoAddLivePrepareChannelResp.Size(m)
}
func (m *AutoAddLivePrepareChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AutoAddLivePrepareChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_AutoAddLivePrepareChannelResp proto.InternalMessageInfo

// 多条件查询
type GetLivePrepareChannelByParaReq struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,name=uids" json:"uids,omitempty"`
	GuildId              *uint32  `protobuf:"varint,2,opt,name=guild_id,json=guildId" json:"guild_id,omitempty"`
	TagId                *uint32  `protobuf:"varint,3,opt,name=tag_id,json=tagId" json:"tag_id,omitempty"`
	Level                *uint32  `protobuf:"varint,4,opt,name=level" json:"level,omitempty"`
	SubQueryType         *uint32  `protobuf:"varint,5,opt,name=sub_query_type,json=subQueryType" json:"sub_query_type,omitempty"`
	Id                   *uint32  `protobuf:"varint,6,opt,name=id" json:"id,omitempty"`
	ConfType             *uint32  `protobuf:"varint,7,opt,name=conf_type,json=confType" json:"conf_type,omitempty"`
	QuickConfType        *uint32  `protobuf:"varint,8,opt,name=quick_conf_type,json=quickConfType" json:"quick_conf_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLivePrepareChannelByParaReq) Reset()         { *m = GetLivePrepareChannelByParaReq{} }
func (m *GetLivePrepareChannelByParaReq) String() string { return proto.CompactTextString(m) }
func (*GetLivePrepareChannelByParaReq) ProtoMessage()    {}
func (*GetLivePrepareChannelByParaReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{81}
}
func (m *GetLivePrepareChannelByParaReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLivePrepareChannelByParaReq.Unmarshal(m, b)
}
func (m *GetLivePrepareChannelByParaReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLivePrepareChannelByParaReq.Marshal(b, m, deterministic)
}
func (dst *GetLivePrepareChannelByParaReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLivePrepareChannelByParaReq.Merge(dst, src)
}
func (m *GetLivePrepareChannelByParaReq) XXX_Size() int {
	return xxx_messageInfo_GetLivePrepareChannelByParaReq.Size(m)
}
func (m *GetLivePrepareChannelByParaReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLivePrepareChannelByParaReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLivePrepareChannelByParaReq proto.InternalMessageInfo

func (m *GetLivePrepareChannelByParaReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

func (m *GetLivePrepareChannelByParaReq) GetGuildId() uint32 {
	if m != nil && m.GuildId != nil {
		return *m.GuildId
	}
	return 0
}

func (m *GetLivePrepareChannelByParaReq) GetTagId() uint32 {
	if m != nil && m.TagId != nil {
		return *m.TagId
	}
	return 0
}

func (m *GetLivePrepareChannelByParaReq) GetLevel() uint32 {
	if m != nil && m.Level != nil {
		return *m.Level
	}
	return 0
}

func (m *GetLivePrepareChannelByParaReq) GetSubQueryType() uint32 {
	if m != nil && m.SubQueryType != nil {
		return *m.SubQueryType
	}
	return 0
}

func (m *GetLivePrepareChannelByParaReq) GetId() uint32 {
	if m != nil && m.Id != nil {
		return *m.Id
	}
	return 0
}

func (m *GetLivePrepareChannelByParaReq) GetConfType() uint32 {
	if m != nil && m.ConfType != nil {
		return *m.ConfType
	}
	return 0
}

func (m *GetLivePrepareChannelByParaReq) GetQuickConfType() uint32 {
	if m != nil && m.QuickConfType != nil {
		return *m.QuickConfType
	}
	return 0
}

type GetLivePrepareChannelByParaResp struct {
	InfoList             []*LivePrepareChannelInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetLivePrepareChannelByParaResp) Reset()         { *m = GetLivePrepareChannelByParaResp{} }
func (m *GetLivePrepareChannelByParaResp) String() string { return proto.CompactTextString(m) }
func (*GetLivePrepareChannelByParaResp) ProtoMessage()    {}
func (*GetLivePrepareChannelByParaResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{82}
}
func (m *GetLivePrepareChannelByParaResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLivePrepareChannelByParaResp.Unmarshal(m, b)
}
func (m *GetLivePrepareChannelByParaResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLivePrepareChannelByParaResp.Marshal(b, m, deterministic)
}
func (dst *GetLivePrepareChannelByParaResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLivePrepareChannelByParaResp.Merge(dst, src)
}
func (m *GetLivePrepareChannelByParaResp) XXX_Size() int {
	return xxx_messageInfo_GetLivePrepareChannelByParaResp.Size(m)
}
func (m *GetLivePrepareChannelByParaResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLivePrepareChannelByParaResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLivePrepareChannelByParaResp proto.InternalMessageInfo

func (m *GetLivePrepareChannelByParaResp) GetInfoList() []*LivePrepareChannelInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type AutoGenReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AutoGenReq) Reset()         { *m = AutoGenReq{} }
func (m *AutoGenReq) String() string { return proto.CompactTextString(m) }
func (*AutoGenReq) ProtoMessage()    {}
func (*AutoGenReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{83}
}
func (m *AutoGenReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AutoGenReq.Unmarshal(m, b)
}
func (m *AutoGenReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AutoGenReq.Marshal(b, m, deterministic)
}
func (dst *AutoGenReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AutoGenReq.Merge(dst, src)
}
func (m *AutoGenReq) XXX_Size() int {
	return xxx_messageInfo_AutoGenReq.Size(m)
}
func (m *AutoGenReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AutoGenReq.DiscardUnknown(m)
}

var xxx_messageInfo_AutoGenReq proto.InternalMessageInfo

type AutoGenResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AutoGenResp) Reset()         { *m = AutoGenResp{} }
func (m *AutoGenResp) String() string { return proto.CompactTextString(m) }
func (*AutoGenResp) ProtoMessage()    {}
func (*AutoGenResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{84}
}
func (m *AutoGenResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AutoGenResp.Unmarshal(m, b)
}
func (m *AutoGenResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AutoGenResp.Marshal(b, m, deterministic)
}
func (dst *AutoGenResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AutoGenResp.Merge(dst, src)
}
func (m *AutoGenResp) XXX_Size() int {
	return xxx_messageInfo_AutoGenResp.Size(m)
}
func (m *AutoGenResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AutoGenResp.DiscardUnknown(m)
}

var xxx_messageInfo_AutoGenResp proto.InternalMessageInfo

// 战歌主播推荐位信息
type WarSongRecommendInfo struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	Uid                  *uint32  `protobuf:"varint,2,req,name=uid" json:"uid,omitempty"`
	LabelId              *string  `protobuf:"bytes,3,req,name=label_id,json=labelId" json:"label_id,omitempty"`
	Intro                *string  `protobuf:"bytes,4,req,name=intro" json:"intro,omitempty"`
	Weight               *uint32  `protobuf:"varint,5,req,name=weight" json:"weight,omitempty"`
	UpdateTs             *uint32  `protobuf:"varint,6,req,name=update_ts,json=updateTs" json:"update_ts,omitempty"`
	OpUid                *uint32  `protobuf:"varint,7,req,name=op_uid,json=opUid" json:"op_uid,omitempty"`
	ChannelType          *uint32  `protobuf:"varint,8,opt,name=channel_type,json=channelType" json:"channel_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WarSongRecommendInfo) Reset()         { *m = WarSongRecommendInfo{} }
func (m *WarSongRecommendInfo) String() string { return proto.CompactTextString(m) }
func (*WarSongRecommendInfo) ProtoMessage()    {}
func (*WarSongRecommendInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{85}
}
func (m *WarSongRecommendInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WarSongRecommendInfo.Unmarshal(m, b)
}
func (m *WarSongRecommendInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WarSongRecommendInfo.Marshal(b, m, deterministic)
}
func (dst *WarSongRecommendInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WarSongRecommendInfo.Merge(dst, src)
}
func (m *WarSongRecommendInfo) XXX_Size() int {
	return xxx_messageInfo_WarSongRecommendInfo.Size(m)
}
func (m *WarSongRecommendInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WarSongRecommendInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WarSongRecommendInfo proto.InternalMessageInfo

func (m *WarSongRecommendInfo) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *WarSongRecommendInfo) GetUid() uint32 {
	if m != nil && m.Uid != nil {
		return *m.Uid
	}
	return 0
}

func (m *WarSongRecommendInfo) GetLabelId() string {
	if m != nil && m.LabelId != nil {
		return *m.LabelId
	}
	return ""
}

func (m *WarSongRecommendInfo) GetIntro() string {
	if m != nil && m.Intro != nil {
		return *m.Intro
	}
	return ""
}

func (m *WarSongRecommendInfo) GetWeight() uint32 {
	if m != nil && m.Weight != nil {
		return *m.Weight
	}
	return 0
}

func (m *WarSongRecommendInfo) GetUpdateTs() uint32 {
	if m != nil && m.UpdateTs != nil {
		return *m.UpdateTs
	}
	return 0
}

func (m *WarSongRecommendInfo) GetOpUid() uint32 {
	if m != nil && m.OpUid != nil {
		return *m.OpUid
	}
	return 0
}

func (m *WarSongRecommendInfo) GetChannelType() uint32 {
	if m != nil && m.ChannelType != nil {
		return *m.ChannelType
	}
	return 0
}

type AddWarSongRecommendReq struct {
	InfoList             []*WarSongRecommendInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *AddWarSongRecommendReq) Reset()         { *m = AddWarSongRecommendReq{} }
func (m *AddWarSongRecommendReq) String() string { return proto.CompactTextString(m) }
func (*AddWarSongRecommendReq) ProtoMessage()    {}
func (*AddWarSongRecommendReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{86}
}
func (m *AddWarSongRecommendReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddWarSongRecommendReq.Unmarshal(m, b)
}
func (m *AddWarSongRecommendReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddWarSongRecommendReq.Marshal(b, m, deterministic)
}
func (dst *AddWarSongRecommendReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddWarSongRecommendReq.Merge(dst, src)
}
func (m *AddWarSongRecommendReq) XXX_Size() int {
	return xxx_messageInfo_AddWarSongRecommendReq.Size(m)
}
func (m *AddWarSongRecommendReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddWarSongRecommendReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddWarSongRecommendReq proto.InternalMessageInfo

func (m *AddWarSongRecommendReq) GetInfoList() []*WarSongRecommendInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type AddWarSongRecommendResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddWarSongRecommendResp) Reset()         { *m = AddWarSongRecommendResp{} }
func (m *AddWarSongRecommendResp) String() string { return proto.CompactTextString(m) }
func (*AddWarSongRecommendResp) ProtoMessage()    {}
func (*AddWarSongRecommendResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{87}
}
func (m *AddWarSongRecommendResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddWarSongRecommendResp.Unmarshal(m, b)
}
func (m *AddWarSongRecommendResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddWarSongRecommendResp.Marshal(b, m, deterministic)
}
func (dst *AddWarSongRecommendResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddWarSongRecommendResp.Merge(dst, src)
}
func (m *AddWarSongRecommendResp) XXX_Size() int {
	return xxx_messageInfo_AddWarSongRecommendResp.Size(m)
}
func (m *AddWarSongRecommendResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddWarSongRecommendResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddWarSongRecommendResp proto.InternalMessageInfo

type GetWarSongRecommendListReq struct {
	Offset               *uint32  `protobuf:"varint,1,req,name=offset" json:"offset,omitempty"`
	Limit                *uint32  `protobuf:"varint,2,req,name=limit" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWarSongRecommendListReq) Reset()         { *m = GetWarSongRecommendListReq{} }
func (m *GetWarSongRecommendListReq) String() string { return proto.CompactTextString(m) }
func (*GetWarSongRecommendListReq) ProtoMessage()    {}
func (*GetWarSongRecommendListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{88}
}
func (m *GetWarSongRecommendListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWarSongRecommendListReq.Unmarshal(m, b)
}
func (m *GetWarSongRecommendListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWarSongRecommendListReq.Marshal(b, m, deterministic)
}
func (dst *GetWarSongRecommendListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWarSongRecommendListReq.Merge(dst, src)
}
func (m *GetWarSongRecommendListReq) XXX_Size() int {
	return xxx_messageInfo_GetWarSongRecommendListReq.Size(m)
}
func (m *GetWarSongRecommendListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWarSongRecommendListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetWarSongRecommendListReq proto.InternalMessageInfo

func (m *GetWarSongRecommendListReq) GetOffset() uint32 {
	if m != nil && m.Offset != nil {
		return *m.Offset
	}
	return 0
}

func (m *GetWarSongRecommendListReq) GetLimit() uint32 {
	if m != nil && m.Limit != nil {
		return *m.Limit
	}
	return 0
}

type GetWarSongRecommendListResp struct {
	InfoList             []*WarSongRecommendInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList" json:"info_list,omitempty"`
	NextOffset           *uint32                 `protobuf:"varint,2,req,name=next_offset,json=nextOffset" json:"next_offset,omitempty"`
	TotalCount           *uint32                 `protobuf:"varint,3,req,name=total_count,json=totalCount" json:"total_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetWarSongRecommendListResp) Reset()         { *m = GetWarSongRecommendListResp{} }
func (m *GetWarSongRecommendListResp) String() string { return proto.CompactTextString(m) }
func (*GetWarSongRecommendListResp) ProtoMessage()    {}
func (*GetWarSongRecommendListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{89}
}
func (m *GetWarSongRecommendListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWarSongRecommendListResp.Unmarshal(m, b)
}
func (m *GetWarSongRecommendListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWarSongRecommendListResp.Marshal(b, m, deterministic)
}
func (dst *GetWarSongRecommendListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWarSongRecommendListResp.Merge(dst, src)
}
func (m *GetWarSongRecommendListResp) XXX_Size() int {
	return xxx_messageInfo_GetWarSongRecommendListResp.Size(m)
}
func (m *GetWarSongRecommendListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWarSongRecommendListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetWarSongRecommendListResp proto.InternalMessageInfo

func (m *GetWarSongRecommendListResp) GetInfoList() []*WarSongRecommendInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetWarSongRecommendListResp) GetNextOffset() uint32 {
	if m != nil && m.NextOffset != nil {
		return *m.NextOffset
	}
	return 0
}

func (m *GetWarSongRecommendListResp) GetTotalCount() uint32 {
	if m != nil && m.TotalCount != nil {
		return *m.TotalCount
	}
	return 0
}

type UpdateWarSongRecommendReq struct {
	Info                 *WarSongRecommendInfo `protobuf:"bytes,1,req,name=info" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *UpdateWarSongRecommendReq) Reset()         { *m = UpdateWarSongRecommendReq{} }
func (m *UpdateWarSongRecommendReq) String() string { return proto.CompactTextString(m) }
func (*UpdateWarSongRecommendReq) ProtoMessage()    {}
func (*UpdateWarSongRecommendReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{90}
}
func (m *UpdateWarSongRecommendReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateWarSongRecommendReq.Unmarshal(m, b)
}
func (m *UpdateWarSongRecommendReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateWarSongRecommendReq.Marshal(b, m, deterministic)
}
func (dst *UpdateWarSongRecommendReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateWarSongRecommendReq.Merge(dst, src)
}
func (m *UpdateWarSongRecommendReq) XXX_Size() int {
	return xxx_messageInfo_UpdateWarSongRecommendReq.Size(m)
}
func (m *UpdateWarSongRecommendReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateWarSongRecommendReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateWarSongRecommendReq proto.InternalMessageInfo

func (m *UpdateWarSongRecommendReq) GetInfo() *WarSongRecommendInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type UpdateWarSongRecommendResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateWarSongRecommendResp) Reset()         { *m = UpdateWarSongRecommendResp{} }
func (m *UpdateWarSongRecommendResp) String() string { return proto.CompactTextString(m) }
func (*UpdateWarSongRecommendResp) ProtoMessage()    {}
func (*UpdateWarSongRecommendResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{91}
}
func (m *UpdateWarSongRecommendResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateWarSongRecommendResp.Unmarshal(m, b)
}
func (m *UpdateWarSongRecommendResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateWarSongRecommendResp.Marshal(b, m, deterministic)
}
func (dst *UpdateWarSongRecommendResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateWarSongRecommendResp.Merge(dst, src)
}
func (m *UpdateWarSongRecommendResp) XXX_Size() int {
	return xxx_messageInfo_UpdateWarSongRecommendResp.Size(m)
}
func (m *UpdateWarSongRecommendResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateWarSongRecommendResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateWarSongRecommendResp proto.InternalMessageInfo

type DelWarSongRecommendReq struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelWarSongRecommendReq) Reset()         { *m = DelWarSongRecommendReq{} }
func (m *DelWarSongRecommendReq) String() string { return proto.CompactTextString(m) }
func (*DelWarSongRecommendReq) ProtoMessage()    {}
func (*DelWarSongRecommendReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{92}
}
func (m *DelWarSongRecommendReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelWarSongRecommendReq.Unmarshal(m, b)
}
func (m *DelWarSongRecommendReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelWarSongRecommendReq.Marshal(b, m, deterministic)
}
func (dst *DelWarSongRecommendReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelWarSongRecommendReq.Merge(dst, src)
}
func (m *DelWarSongRecommendReq) XXX_Size() int {
	return xxx_messageInfo_DelWarSongRecommendReq.Size(m)
}
func (m *DelWarSongRecommendReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelWarSongRecommendReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelWarSongRecommendReq proto.InternalMessageInfo

func (m *DelWarSongRecommendReq) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

type DelWarSongRecommendResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelWarSongRecommendResp) Reset()         { *m = DelWarSongRecommendResp{} }
func (m *DelWarSongRecommendResp) String() string { return proto.CompactTextString(m) }
func (*DelWarSongRecommendResp) ProtoMessage()    {}
func (*DelWarSongRecommendResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{93}
}
func (m *DelWarSongRecommendResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelWarSongRecommendResp.Unmarshal(m, b)
}
func (m *DelWarSongRecommendResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelWarSongRecommendResp.Marshal(b, m, deterministic)
}
func (dst *DelWarSongRecommendResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelWarSongRecommendResp.Merge(dst, src)
}
func (m *DelWarSongRecommendResp) XXX_Size() int {
	return xxx_messageInfo_DelWarSongRecommendResp.Size(m)
}
func (m *DelWarSongRecommendResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelWarSongRecommendResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelWarSongRecommendResp proto.InternalMessageInfo

// 清空战歌位推荐库
type ClearWarSongRecommendReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearWarSongRecommendReq) Reset()         { *m = ClearWarSongRecommendReq{} }
func (m *ClearWarSongRecommendReq) String() string { return proto.CompactTextString(m) }
func (*ClearWarSongRecommendReq) ProtoMessage()    {}
func (*ClearWarSongRecommendReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{94}
}
func (m *ClearWarSongRecommendReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearWarSongRecommendReq.Unmarshal(m, b)
}
func (m *ClearWarSongRecommendReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearWarSongRecommendReq.Marshal(b, m, deterministic)
}
func (dst *ClearWarSongRecommendReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearWarSongRecommendReq.Merge(dst, src)
}
func (m *ClearWarSongRecommendReq) XXX_Size() int {
	return xxx_messageInfo_ClearWarSongRecommendReq.Size(m)
}
func (m *ClearWarSongRecommendReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearWarSongRecommendReq.DiscardUnknown(m)
}

var xxx_messageInfo_ClearWarSongRecommendReq proto.InternalMessageInfo

type ClearWarSongRecommendResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearWarSongRecommendResp) Reset()         { *m = ClearWarSongRecommendResp{} }
func (m *ClearWarSongRecommendResp) String() string { return proto.CompactTextString(m) }
func (*ClearWarSongRecommendResp) ProtoMessage()    {}
func (*ClearWarSongRecommendResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{95}
}
func (m *ClearWarSongRecommendResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearWarSongRecommendResp.Unmarshal(m, b)
}
func (m *ClearWarSongRecommendResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearWarSongRecommendResp.Marshal(b, m, deterministic)
}
func (dst *ClearWarSongRecommendResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearWarSongRecommendResp.Merge(dst, src)
}
func (m *ClearWarSongRecommendResp) XXX_Size() int {
	return xxx_messageInfo_ClearWarSongRecommendResp.Size(m)
}
func (m *ClearWarSongRecommendResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearWarSongRecommendResp.DiscardUnknown(m)
}

var xxx_messageInfo_ClearWarSongRecommendResp proto.InternalMessageInfo

// 战歌处房间推荐信息
type WarSongRecChannelInfo struct {
	ChannelId            *uint32  `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	Uid                  *uint32  `protobuf:"varint,2,req,name=uid" json:"uid,omitempty"`
	Intro                *string  `protobuf:"bytes,3,req,name=intro" json:"intro,omitempty"`
	BgColor              *string  `protobuf:"bytes,4,req,name=bg_color,json=bgColor" json:"bg_color,omitempty"`
	TextColor            *string  `protobuf:"bytes,5,req,name=text_color,json=textColor" json:"text_color,omitempty"`
	Content              *string  `protobuf:"bytes,6,req,name=content" json:"content,omitempty"`
	ChannelType          *uint32  `protobuf:"varint,7,opt,name=channel_type,json=channelType" json:"channel_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WarSongRecChannelInfo) Reset()         { *m = WarSongRecChannelInfo{} }
func (m *WarSongRecChannelInfo) String() string { return proto.CompactTextString(m) }
func (*WarSongRecChannelInfo) ProtoMessage()    {}
func (*WarSongRecChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{96}
}
func (m *WarSongRecChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WarSongRecChannelInfo.Unmarshal(m, b)
}
func (m *WarSongRecChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WarSongRecChannelInfo.Marshal(b, m, deterministic)
}
func (dst *WarSongRecChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WarSongRecChannelInfo.Merge(dst, src)
}
func (m *WarSongRecChannelInfo) XXX_Size() int {
	return xxx_messageInfo_WarSongRecChannelInfo.Size(m)
}
func (m *WarSongRecChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WarSongRecChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WarSongRecChannelInfo proto.InternalMessageInfo

func (m *WarSongRecChannelInfo) GetChannelId() uint32 {
	if m != nil && m.ChannelId != nil {
		return *m.ChannelId
	}
	return 0
}

func (m *WarSongRecChannelInfo) GetUid() uint32 {
	if m != nil && m.Uid != nil {
		return *m.Uid
	}
	return 0
}

func (m *WarSongRecChannelInfo) GetIntro() string {
	if m != nil && m.Intro != nil {
		return *m.Intro
	}
	return ""
}

func (m *WarSongRecChannelInfo) GetBgColor() string {
	if m != nil && m.BgColor != nil {
		return *m.BgColor
	}
	return ""
}

func (m *WarSongRecChannelInfo) GetTextColor() string {
	if m != nil && m.TextColor != nil {
		return *m.TextColor
	}
	return ""
}

func (m *WarSongRecChannelInfo) GetContent() string {
	if m != nil && m.Content != nil {
		return *m.Content
	}
	return ""
}

func (m *WarSongRecChannelInfo) GetChannelType() uint32 {
	if m != nil && m.ChannelType != nil {
		return *m.ChannelType
	}
	return 0
}

type GetWarSongRecChannelListReq struct {
	IsNewVer             *bool    `protobuf:"varint,1,opt,name=is_new_ver,json=isNewVer" json:"is_new_ver,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWarSongRecChannelListReq) Reset()         { *m = GetWarSongRecChannelListReq{} }
func (m *GetWarSongRecChannelListReq) String() string { return proto.CompactTextString(m) }
func (*GetWarSongRecChannelListReq) ProtoMessage()    {}
func (*GetWarSongRecChannelListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{97}
}
func (m *GetWarSongRecChannelListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWarSongRecChannelListReq.Unmarshal(m, b)
}
func (m *GetWarSongRecChannelListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWarSongRecChannelListReq.Marshal(b, m, deterministic)
}
func (dst *GetWarSongRecChannelListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWarSongRecChannelListReq.Merge(dst, src)
}
func (m *GetWarSongRecChannelListReq) XXX_Size() int {
	return xxx_messageInfo_GetWarSongRecChannelListReq.Size(m)
}
func (m *GetWarSongRecChannelListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWarSongRecChannelListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetWarSongRecChannelListReq proto.InternalMessageInfo

func (m *GetWarSongRecChannelListReq) GetIsNewVer() bool {
	if m != nil && m.IsNewVer != nil {
		return *m.IsNewVer
	}
	return false
}

type GetWarSongRecChannelListResp struct {
	ChannelList          []*WarSongRecChannelInfo `protobuf:"bytes,1,rep,name=channel_list,json=channelList" json:"channel_list,omitempty"`
	IsEnd                *bool                    `protobuf:"varint,2,req,name=is_end,json=isEnd" json:"is_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetWarSongRecChannelListResp) Reset()         { *m = GetWarSongRecChannelListResp{} }
func (m *GetWarSongRecChannelListResp) String() string { return proto.CompactTextString(m) }
func (*GetWarSongRecChannelListResp) ProtoMessage()    {}
func (*GetWarSongRecChannelListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{98}
}
func (m *GetWarSongRecChannelListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWarSongRecChannelListResp.Unmarshal(m, b)
}
func (m *GetWarSongRecChannelListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWarSongRecChannelListResp.Marshal(b, m, deterministic)
}
func (dst *GetWarSongRecChannelListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWarSongRecChannelListResp.Merge(dst, src)
}
func (m *GetWarSongRecChannelListResp) XXX_Size() int {
	return xxx_messageInfo_GetWarSongRecChannelListResp.Size(m)
}
func (m *GetWarSongRecChannelListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWarSongRecChannelListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetWarSongRecChannelListResp proto.InternalMessageInfo

func (m *GetWarSongRecChannelListResp) GetChannelList() []*WarSongRecChannelInfo {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

func (m *GetWarSongRecChannelListResp) GetIsEnd() bool {
	if m != nil && m.IsEnd != nil {
		return *m.IsEnd
	}
	return false
}

// 房间推荐信息
type ChannelRecInfo struct {
	RecInfo              *ChannelRecommendSimpleInfo `protobuf:"bytes,1,opt,name=rec_info,json=recInfo" json:"rec_info,omitempty"`
	TagInfo              *ChannelTagConfigInfo       `protobuf:"bytes,2,opt,name=tag_info,json=tagInfo" json:"tag_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *ChannelRecInfo) Reset()         { *m = ChannelRecInfo{} }
func (m *ChannelRecInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelRecInfo) ProtoMessage()    {}
func (*ChannelRecInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{99}
}
func (m *ChannelRecInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelRecInfo.Unmarshal(m, b)
}
func (m *ChannelRecInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelRecInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelRecInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelRecInfo.Merge(dst, src)
}
func (m *ChannelRecInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelRecInfo.Size(m)
}
func (m *ChannelRecInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelRecInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelRecInfo proto.InternalMessageInfo

func (m *ChannelRecInfo) GetRecInfo() *ChannelRecommendSimpleInfo {
	if m != nil {
		return m.RecInfo
	}
	return nil
}

func (m *ChannelRecInfo) GetTagInfo() *ChannelTagConfigInfo {
	if m != nil {
		return m.TagInfo
	}
	return nil
}

// 批量获取房间的推荐信息
type BatGetChannelRecInfoReq struct {
	CidList              []uint32 `protobuf:"varint,1,rep,name=cid_list,json=cidList" json:"cid_list,omitempty"`
	Uid                  *uint32  `protobuf:"varint,2,opt,name=uid" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetChannelRecInfoReq) Reset()         { *m = BatGetChannelRecInfoReq{} }
func (m *BatGetChannelRecInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatGetChannelRecInfoReq) ProtoMessage()    {}
func (*BatGetChannelRecInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{100}
}
func (m *BatGetChannelRecInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetChannelRecInfoReq.Unmarshal(m, b)
}
func (m *BatGetChannelRecInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetChannelRecInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatGetChannelRecInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetChannelRecInfoReq.Merge(dst, src)
}
func (m *BatGetChannelRecInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatGetChannelRecInfoReq.Size(m)
}
func (m *BatGetChannelRecInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetChannelRecInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetChannelRecInfoReq proto.InternalMessageInfo

func (m *BatGetChannelRecInfoReq) GetCidList() []uint32 {
	if m != nil {
		return m.CidList
	}
	return nil
}

func (m *BatGetChannelRecInfoReq) GetUid() uint32 {
	if m != nil && m.Uid != nil {
		return *m.Uid
	}
	return 0
}

type BatGetChannelRecInfoResp struct {
	ChannelList          []*ChannelRecInfo `protobuf:"bytes,1,rep,name=channel_list,json=channelList" json:"channel_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatGetChannelRecInfoResp) Reset()         { *m = BatGetChannelRecInfoResp{} }
func (m *BatGetChannelRecInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatGetChannelRecInfoResp) ProtoMessage()    {}
func (*BatGetChannelRecInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entertainmentrecommendback_7b671d4fe7793222, []int{101}
}
func (m *BatGetChannelRecInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetChannelRecInfoResp.Unmarshal(m, b)
}
func (m *BatGetChannelRecInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetChannelRecInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatGetChannelRecInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetChannelRecInfoResp.Merge(dst, src)
}
func (m *BatGetChannelRecInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatGetChannelRecInfoResp.Size(m)
}
func (m *BatGetChannelRecInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetChannelRecInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetChannelRecInfoResp proto.InternalMessageInfo

func (m *BatGetChannelRecInfoResp) GetChannelList() []*ChannelRecInfo {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

func init() {
	proto.RegisterType((*TimeSection)(nil), "entertainmentRecommendBack.TimeSection")
	proto.RegisterType((*ChannelCommonInfo)(nil), "entertainmentRecommendBack.ChannelCommonInfo")
	proto.RegisterType((*ChannelCacheInfo)(nil), "entertainmentRecommendBack.ChannelCacheInfo")
	proto.RegisterType((*PrepareChannelInfo)(nil), "entertainmentRecommendBack.PrepareChannelInfo")
	proto.RegisterType((*McSimpleInfo)(nil), "entertainmentRecommendBack.McSimpleInfo")
	proto.RegisterType((*ChannelRecommendSimpleInfo)(nil), "entertainmentRecommendBack.ChannelRecommendSimpleInfo")
	proto.RegisterType((*ActivityChannelInfo)(nil), "entertainmentRecommendBack.ActivityChannelInfo")
	proto.RegisterType((*AddPrepareChannelReq)(nil), "entertainmentRecommendBack.AddPrepareChannelReq")
	proto.RegisterType((*AddPrepareChannelResp)(nil), "entertainmentRecommendBack.AddPrepareChannelResp")
	proto.RegisterType((*GetPrepareChannelByChannelIDReq)(nil), "entertainmentRecommendBack.GetPrepareChannelByChannelIDReq")
	proto.RegisterType((*GetPrepareChannelByChannelIDResp)(nil), "entertainmentRecommendBack.GetPrepareChannelByChannelIDResp")
	proto.RegisterType((*GetPrepareChannelByParaReq)(nil), "entertainmentRecommendBack.GetPrepareChannelByParaReq")
	proto.RegisterType((*GetPrepareChannelByParaResp)(nil), "entertainmentRecommendBack.GetPrepareChannelByParaResp")
	proto.RegisterType((*GetPrepareIDListByChannelTypeReq)(nil), "entertainmentRecommendBack.GetPrepareIDListByChannelTypeReq")
	proto.RegisterType((*GetPrepareIDListByChannelTypeResp)(nil), "entertainmentRecommendBack.GetPrepareIDListByChannelTypeResp")
	proto.RegisterType((*GetPrepareChannelListV2Req)(nil), "entertainmentRecommendBack.GetPrepareChannelListV2Req")
	proto.RegisterType((*GetPrepareChannelListV2Resp)(nil), "entertainmentRecommendBack.GetPrepareChannelListV2Resp")
	proto.RegisterType((*AddActivityChannelReq)(nil), "entertainmentRecommendBack.AddActivityChannelReq")
	proto.RegisterType((*AddActivityChannelResp)(nil), "entertainmentRecommendBack.AddActivityChannelResp")
	proto.RegisterType((*GetActivityChannelListReq)(nil), "entertainmentRecommendBack.GetActivityChannelListReq")
	proto.RegisterType((*GetActivityChannelListResp)(nil), "entertainmentRecommendBack.GetActivityChannelListResp")
	proto.RegisterType((*GetActivityChannelListV2Req)(nil), "entertainmentRecommendBack.GetActivityChannelListV2Req")
	proto.RegisterType((*GetActivityChannelListV2Resp)(nil), "entertainmentRecommendBack.GetActivityChannelListV2Resp")
	proto.RegisterType((*UpdateActivityChannelReq)(nil), "entertainmentRecommendBack.UpdateActivityChannelReq")
	proto.RegisterType((*UpdateActivityChannelResp)(nil), "entertainmentRecommendBack.UpdateActivityChannelResp")
	proto.RegisterType((*ChannelTagConfigInfo)(nil), "entertainmentRecommendBack.ChannelTagConfigInfo")
	proto.RegisterType((*ChannelMultiTagConfigInfo)(nil), "entertainmentRecommendBack.ChannelMultiTagConfigInfo")
	proto.RegisterType((*GetChannelTagConfigInfoReq)(nil), "entertainmentRecommendBack.GetChannelTagConfigInfoReq")
	proto.RegisterType((*GetChannelTagConfigInfoResp)(nil), "entertainmentRecommendBack.GetChannelTagConfigInfoResp")
	proto.RegisterType((*GetChannelTagReq)(nil), "entertainmentRecommendBack.GetChannelTagReq")
	proto.RegisterType((*GetChannelTagResp)(nil), "entertainmentRecommendBack.GetChannelTagResp")
	proto.RegisterType((*BatchGetChannelTagReq)(nil), "entertainmentRecommendBack.BatchGetChannelTagReq")
	proto.RegisterType((*BatchGetChannelTagResp)(nil), "entertainmentRecommendBack.BatchGetChannelTagResp")
	proto.RegisterType((*DeletePrepareChannelReq)(nil), "entertainmentRecommendBack.DeletePrepareChannelReq")
	proto.RegisterType((*DeletePrepareChannelResp)(nil), "entertainmentRecommendBack.DeletePrepareChannelResp")
	proto.RegisterType((*DeleteActivityChannelReq)(nil), "entertainmentRecommendBack.DeleteActivityChannelReq")
	proto.RegisterType((*DeleteActivityChannelResp)(nil), "entertainmentRecommendBack.DeleteActivityChannelResp")
	proto.RegisterType((*SetChannelCommonReq)(nil), "entertainmentRecommendBack.SetChannelCommonReq")
	proto.RegisterType((*SetChannelCommonResp)(nil), "entertainmentRecommendBack.SetChannelCommonResp")
	proto.RegisterType((*GetRecommendChannelReq)(nil), "entertainmentRecommendBack.GetRecommendChannelReq")
	proto.RegisterType((*GetRecommendChannelResp)(nil), "entertainmentRecommendBack.GetRecommendChannelResp")
	proto.RegisterType((*GetChannelByTagIdReq)(nil), "entertainmentRecommendBack.GetChannelByTagIdReq")
	proto.RegisterType((*GetChannelByTagIdResp)(nil), "entertainmentRecommendBack.GetChannelByTagIdResp")
	proto.RegisterType((*HotChannelInfo)(nil), "entertainmentRecommendBack.HotChannelInfo")
	proto.RegisterType((*GetHotChannelReq)(nil), "entertainmentRecommendBack.GetHotChannelReq")
	proto.RegisterType((*GetHotChannelResp)(nil), "entertainmentRecommendBack.GetHotChannelResp")
	proto.RegisterType((*ChannelTagAdv)(nil), "entertainmentRecommendBack.ChannelTagAdv")
	proto.RegisterType((*GetChannelTagAdvReq)(nil), "entertainmentRecommendBack.GetChannelTagAdvReq")
	proto.RegisterType((*GetChannelTagAdvResp)(nil), "entertainmentRecommendBack.GetChannelTagAdvResp")
	proto.RegisterType((*AddChannelTagAdvReq)(nil), "entertainmentRecommendBack.AddChannelTagAdvReq")
	proto.RegisterType((*AddChannelTagAdvResp)(nil), "entertainmentRecommendBack.AddChannelTagAdvResp")
	proto.RegisterType((*DelChannelTagAdvReq)(nil), "entertainmentRecommendBack.DelChannelTagAdvReq")
	proto.RegisterType((*DelChannelTagAdvResp)(nil), "entertainmentRecommendBack.DelChannelTagAdvResp")
	proto.RegisterType((*AddChannelGiftReq)(nil), "entertainmentRecommendBack.AddChannelGiftReq")
	proto.RegisterType((*AddChannelGiftResp)(nil), "entertainmentRecommendBack.AddChannelGiftResp")
	proto.RegisterType((*GetRankChannelsReq)(nil), "entertainmentRecommendBack.GetRankChannelsReq")
	proto.RegisterType((*GetRankChannelsResp)(nil), "entertainmentRecommendBack.GetRankChannelsResp")
	proto.RegisterType((*ChannelTagRankItem)(nil), "entertainmentRecommendBack.ChannelTagRankItem")
	proto.RegisterType((*GetChannelTagRankReq)(nil), "entertainmentRecommendBack.GetChannelTagRankReq")
	proto.RegisterType((*GetChannelTagRankResp)(nil), "entertainmentRecommendBack.GetChannelTagRankResp")
	proto.RegisterType((*BatchGetChannelRecommendLevelReq)(nil), "entertainmentRecommendBack.BatchGetChannelRecommendLevelReq")
	proto.RegisterType((*ChannelRecommendLevel)(nil), "entertainmentRecommendBack.ChannelRecommendLevel")
	proto.RegisterType((*BatchGetChannelRecommendLevelResp)(nil), "entertainmentRecommendBack.BatchGetChannelRecommendLevelResp")
	proto.RegisterType((*GetQuickRecommendChannelByTagIdReq)(nil), "entertainmentRecommendBack.GetQuickRecommendChannelByTagIdReq")
	proto.RegisterType((*GetQuickRecommendChannelByTagIdResp)(nil), "entertainmentRecommendBack.GetQuickRecommendChannelByTagIdResp")
	proto.RegisterType((*DayTimeSection)(nil), "entertainmentRecommendBack.DayTimeSection")
	proto.RegisterType((*DayTimeSectionList)(nil), "entertainmentRecommendBack.DayTimeSectionList")
	proto.RegisterType((*LiveQuickEnterPrepareInfo)(nil), "entertainmentRecommendBack.LiveQuickEnterPrepareInfo")
	proto.RegisterType((*LivePrepareChannelInfo)(nil), "entertainmentRecommendBack.LivePrepareChannelInfo")
	proto.RegisterType((*AddLivePrepareChannelReq)(nil), "entertainmentRecommendBack.AddLivePrepareChannelReq")
	proto.RegisterType((*AddLivePrepareChannelResp)(nil), "entertainmentRecommendBack.AddLivePrepareChannelResp")
	proto.RegisterType((*GetLivePrepareChannelListReq)(nil), "entertainmentRecommendBack.GetLivePrepareChannelListReq")
	proto.RegisterType((*GetLivePrepareChannelListResp)(nil), "entertainmentRecommendBack.GetLivePrepareChannelListResp")
	proto.RegisterType((*UpdateLivePrepareChannelReq)(nil), "entertainmentRecommendBack.UpdateLivePrepareChannelReq")
	proto.RegisterType((*UpdateLivePrepareChannelResp)(nil), "entertainmentRecommendBack.UpdateLivePrepareChannelResp")
	proto.RegisterType((*DelLivePrepareChannelReq)(nil), "entertainmentRecommendBack.DelLivePrepareChannelReq")
	proto.RegisterType((*DelLivePrepareChannelResp)(nil), "entertainmentRecommendBack.DelLivePrepareChannelResp")
	proto.RegisterType((*ClearLivePrepareChannelReq)(nil), "entertainmentRecommendBack.ClearLivePrepareChannelReq")
	proto.RegisterType((*ClearLivePrepareChannelResp)(nil), "entertainmentRecommendBack.ClearLivePrepareChannelResp")
	proto.RegisterType((*AutoAddLivePrepareChannelReq)(nil), "entertainmentRecommendBack.AutoAddLivePrepareChannelReq")
	proto.RegisterType((*AutoAddLivePrepareChannelResp)(nil), "entertainmentRecommendBack.AutoAddLivePrepareChannelResp")
	proto.RegisterType((*GetLivePrepareChannelByParaReq)(nil), "entertainmentRecommendBack.GetLivePrepareChannelByParaReq")
	proto.RegisterType((*GetLivePrepareChannelByParaResp)(nil), "entertainmentRecommendBack.GetLivePrepareChannelByParaResp")
	proto.RegisterType((*AutoGenReq)(nil), "entertainmentRecommendBack.AutoGenReq")
	proto.RegisterType((*AutoGenResp)(nil), "entertainmentRecommendBack.AutoGenResp")
	proto.RegisterType((*WarSongRecommendInfo)(nil), "entertainmentRecommendBack.WarSongRecommendInfo")
	proto.RegisterType((*AddWarSongRecommendReq)(nil), "entertainmentRecommendBack.AddWarSongRecommendReq")
	proto.RegisterType((*AddWarSongRecommendResp)(nil), "entertainmentRecommendBack.AddWarSongRecommendResp")
	proto.RegisterType((*GetWarSongRecommendListReq)(nil), "entertainmentRecommendBack.GetWarSongRecommendListReq")
	proto.RegisterType((*GetWarSongRecommendListResp)(nil), "entertainmentRecommendBack.GetWarSongRecommendListResp")
	proto.RegisterType((*UpdateWarSongRecommendReq)(nil), "entertainmentRecommendBack.UpdateWarSongRecommendReq")
	proto.RegisterType((*UpdateWarSongRecommendResp)(nil), "entertainmentRecommendBack.UpdateWarSongRecommendResp")
	proto.RegisterType((*DelWarSongRecommendReq)(nil), "entertainmentRecommendBack.DelWarSongRecommendReq")
	proto.RegisterType((*DelWarSongRecommendResp)(nil), "entertainmentRecommendBack.DelWarSongRecommendResp")
	proto.RegisterType((*ClearWarSongRecommendReq)(nil), "entertainmentRecommendBack.ClearWarSongRecommendReq")
	proto.RegisterType((*ClearWarSongRecommendResp)(nil), "entertainmentRecommendBack.ClearWarSongRecommendResp")
	proto.RegisterType((*WarSongRecChannelInfo)(nil), "entertainmentRecommendBack.WarSongRecChannelInfo")
	proto.RegisterType((*GetWarSongRecChannelListReq)(nil), "entertainmentRecommendBack.GetWarSongRecChannelListReq")
	proto.RegisterType((*GetWarSongRecChannelListResp)(nil), "entertainmentRecommendBack.GetWarSongRecChannelListResp")
	proto.RegisterType((*ChannelRecInfo)(nil), "entertainmentRecommendBack.ChannelRecInfo")
	proto.RegisterType((*BatGetChannelRecInfoReq)(nil), "entertainmentRecommendBack.BatGetChannelRecInfoReq")
	proto.RegisterType((*BatGetChannelRecInfoResp)(nil), "entertainmentRecommendBack.BatGetChannelRecInfoResp")
	proto.RegisterEnum("entertainmentRecommendBack.ADV_TYPE", ADV_TYPE_name, ADV_TYPE_value)
	proto.RegisterEnum("entertainmentRecommendBack.UserCategory", UserCategory_name, UserCategory_value)
	proto.RegisterEnum("entertainmentRecommendBack.ChannelCategory", ChannelCategory_name, ChannelCategory_value)
	proto.RegisterEnum("entertainmentRecommendBack.ActivityChStatus", ActivityChStatus_name, ActivityChStatus_value)
	proto.RegisterEnum("entertainmentRecommendBack.ChannelLevel", ChannelLevel_name, ChannelLevel_value)
	proto.RegisterEnum("entertainmentRecommendBack.ELivePrepareConfType", ELivePrepareConfType_name, ELivePrepareConfType_value)
	proto.RegisterEnum("entertainmentRecommendBack.SubTagQueryType", SubTagQueryType_name, SubTagQueryType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// EntertainmentRecommendBackClient is the client API for EntertainmentRecommendBack service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type EntertainmentRecommendBackClient interface {
	AddPrepareChannel(ctx context.Context, in *AddPrepareChannelReq, opts ...grpc.CallOption) (*AddPrepareChannelResp, error)
	// 根据房间ID 获取指定的推荐库的房间信息 列表
	GetPrepareChannelByChannelID(ctx context.Context, in *GetPrepareChannelByChannelIDReq, opts ...grpc.CallOption) (*GetPrepareChannelByChannelIDResp, error)
	// 获取全量推荐库房间信息 列表
	// 不建议使用该接口，由于数据量巨大导致性能太低
	// 如果仅是一般查询建议改用 GetPrepareIDListByChannelType 或者 GetPrepareChannelListV2
	GetPrepareChannelByPara(ctx context.Context, in *GetPrepareChannelByParaReq, opts ...grpc.CallOption) (*GetPrepareChannelByParaResp, error)
	// 添加活动库房间
	AddActivityChannel(ctx context.Context, in *AddActivityChannelReq, opts ...grpc.CallOption) (*AddActivityChannelResp, error)
	// 获取活动库房间信息 列表
	GetActivityChannelList(ctx context.Context, in *GetActivityChannelListReq, opts ...grpc.CallOption) (*GetActivityChannelListResp, error)
	GetChannelTagConfigInfo(ctx context.Context, in *GetChannelTagConfigInfoReq, opts ...grpc.CallOption) (*GetChannelTagConfigInfoResp, error)
	// 删除推荐库房间
	DeletePrepareChannel(ctx context.Context, in *DeletePrepareChannelReq, opts ...grpc.CallOption) (*DeletePrepareChannelResp, error)
	// 删除活动库房间
	DeleteActivityChannel(ctx context.Context, in *DeleteActivityChannelReq, opts ...grpc.CallOption) (*DeleteActivityChannelResp, error)
	SetChannelCommon(ctx context.Context, in *SetChannelCommonReq, opts ...grpc.CallOption) (*SetChannelCommonResp, error)
	GetRecommendChannel(ctx context.Context, in *GetRecommendChannelReq, opts ...grpc.CallOption) (*GetRecommendChannelResp, error)
	GetChannelByTagId(ctx context.Context, in *GetChannelByTagIdReq, opts ...grpc.CallOption) (*GetChannelByTagIdResp, error)
	GetChannelTag(ctx context.Context, in *GetChannelTagReq, opts ...grpc.CallOption) (*GetChannelTagResp, error)
	BatchGetChannelTag(ctx context.Context, in *BatchGetChannelTagReq, opts ...grpc.CallOption) (*BatchGetChannelTagResp, error)
	GetHotChannel(ctx context.Context, in *GetHotChannelReq, opts ...grpc.CallOption) (*GetHotChannelResp, error)
	GetChannelTagAdv(ctx context.Context, in *GetChannelTagAdvReq, opts ...grpc.CallOption) (*GetChannelTagAdvResp, error)
	AddChannelGift(ctx context.Context, in *AddChannelGiftReq, opts ...grpc.CallOption) (*AddChannelGiftResp, error)
	AddChannelTagAdv(ctx context.Context, in *AddChannelTagAdvReq, opts ...grpc.CallOption) (*AddChannelTagAdvResp, error)
	DelChannelTagAdv(ctx context.Context, in *DelChannelTagAdvReq, opts ...grpc.CallOption) (*DelChannelTagAdvResp, error)
	GetRankChannels(ctx context.Context, in *GetRankChannelsReq, opts ...grpc.CallOption) (*GetRankChannelsResp, error)
	GetChannelTagRanks(ctx context.Context, in *GetChannelTagRankReq, opts ...grpc.CallOption) (*GetChannelTagRankResp, error)
	BatchGetChannelRecommendLevel(ctx context.Context, in *BatchGetChannelRecommendLevelReq, opts ...grpc.CallOption) (*BatchGetChannelRecommendLevelResp, error)
	GetQuickRecChannelByTagId(ctx context.Context, in *GetQuickRecommendChannelByTagIdReq, opts ...grpc.CallOption) (*GetQuickRecommendChannelByTagIdResp, error)
	AddLivePrepareChannel(ctx context.Context, in *AddLivePrepareChannelReq, opts ...grpc.CallOption) (*AddLivePrepareChannelResp, error)
	GetLivePrepareChannelList(ctx context.Context, in *GetLivePrepareChannelListReq, opts ...grpc.CallOption) (*GetLivePrepareChannelListResp, error)
	DelLivePrepareChannel(ctx context.Context, in *DelLivePrepareChannelReq, opts ...grpc.CallOption) (*DelLivePrepareChannelResp, error)
	AutoAddLivePrepareChannel(ctx context.Context, in *AutoAddLivePrepareChannelReq, opts ...grpc.CallOption) (*AutoAddLivePrepareChannelResp, error)
	GetLivePrepareChannelByPara(ctx context.Context, in *GetLivePrepareChannelByParaReq, opts ...grpc.CallOption) (*GetLivePrepareChannelByParaResp, error)
	UpdateLivePrepareChannel(ctx context.Context, in *UpdateLivePrepareChannelReq, opts ...grpc.CallOption) (*UpdateLivePrepareChannelResp, error)
	AutoGen(ctx context.Context, in *AutoGenReq, opts ...grpc.CallOption) (*AutoGenResp, error)
	AddWarSongRecommend(ctx context.Context, in *AddWarSongRecommendReq, opts ...grpc.CallOption) (*AddWarSongRecommendResp, error)
	GetWarSongRecommendList(ctx context.Context, in *GetWarSongRecommendListReq, opts ...grpc.CallOption) (*GetWarSongRecommendListResp, error)
	UpdateWarSongRecommend(ctx context.Context, in *UpdateWarSongRecommendReq, opts ...grpc.CallOption) (*UpdateWarSongRecommendResp, error)
	DelWarSongRecommend(ctx context.Context, in *DelWarSongRecommendReq, opts ...grpc.CallOption) (*DelWarSongRecommendResp, error)
	GetWarSongRecChannelList(ctx context.Context, in *GetWarSongRecChannelListReq, opts ...grpc.CallOption) (*GetWarSongRecChannelListResp, error)
	GetActivityChannelListV2(ctx context.Context, in *GetActivityChannelListV2Req, opts ...grpc.CallOption) (*GetActivityChannelListV2Resp, error)
	UpdateActivityChannel(ctx context.Context, in *UpdateActivityChannelReq, opts ...grpc.CallOption) (*UpdateActivityChannelResp, error)
	// 根据房间类型 获取推荐库的房间ID 列表
	// 但是直播间类型的房间太多，不建议一次获取
	GetPrepareIDListByChannelType(ctx context.Context, in *GetPrepareIDListByChannelTypeReq, opts ...grpc.CallOption) (*GetPrepareIDListByChannelTypeResp, error)
	// 根据房间类型 获取推荐库的房间 列表
	// 但是直播间类型的房间太多，不建议一次获取
	GetPrepareChannelListV2(ctx context.Context, in *GetPrepareChannelListV2Req, opts ...grpc.CallOption) (*GetPrepareChannelListV2Resp, error)
	ClearLivePrepareChannel(ctx context.Context, in *ClearLivePrepareChannelReq, opts ...grpc.CallOption) (*ClearLivePrepareChannelResp, error)
	ClearWarSongRecommend(ctx context.Context, in *ClearWarSongRecommendReq, opts ...grpc.CallOption) (*ClearWarSongRecommendResp, error)
	BatGetChannelRecInfo(ctx context.Context, in *BatGetChannelRecInfoReq, opts ...grpc.CallOption) (*BatGetChannelRecInfoResp, error)
}

type entertainmentRecommendBackClient struct {
	cc *grpc.ClientConn
}

func NewEntertainmentRecommendBackClient(cc *grpc.ClientConn) EntertainmentRecommendBackClient {
	return &entertainmentRecommendBackClient{cc}
}

func (c *entertainmentRecommendBackClient) AddPrepareChannel(ctx context.Context, in *AddPrepareChannelReq, opts ...grpc.CallOption) (*AddPrepareChannelResp, error) {
	out := new(AddPrepareChannelResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/AddPrepareChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) GetPrepareChannelByChannelID(ctx context.Context, in *GetPrepareChannelByChannelIDReq, opts ...grpc.CallOption) (*GetPrepareChannelByChannelIDResp, error) {
	out := new(GetPrepareChannelByChannelIDResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/GetPrepareChannelByChannelID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) GetPrepareChannelByPara(ctx context.Context, in *GetPrepareChannelByParaReq, opts ...grpc.CallOption) (*GetPrepareChannelByParaResp, error) {
	out := new(GetPrepareChannelByParaResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/GetPrepareChannelByPara", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) AddActivityChannel(ctx context.Context, in *AddActivityChannelReq, opts ...grpc.CallOption) (*AddActivityChannelResp, error) {
	out := new(AddActivityChannelResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/AddActivityChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) GetActivityChannelList(ctx context.Context, in *GetActivityChannelListReq, opts ...grpc.CallOption) (*GetActivityChannelListResp, error) {
	out := new(GetActivityChannelListResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/GetActivityChannelList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) GetChannelTagConfigInfo(ctx context.Context, in *GetChannelTagConfigInfoReq, opts ...grpc.CallOption) (*GetChannelTagConfigInfoResp, error) {
	out := new(GetChannelTagConfigInfoResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/GetChannelTagConfigInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) DeletePrepareChannel(ctx context.Context, in *DeletePrepareChannelReq, opts ...grpc.CallOption) (*DeletePrepareChannelResp, error) {
	out := new(DeletePrepareChannelResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/DeletePrepareChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) DeleteActivityChannel(ctx context.Context, in *DeleteActivityChannelReq, opts ...grpc.CallOption) (*DeleteActivityChannelResp, error) {
	out := new(DeleteActivityChannelResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/DeleteActivityChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) SetChannelCommon(ctx context.Context, in *SetChannelCommonReq, opts ...grpc.CallOption) (*SetChannelCommonResp, error) {
	out := new(SetChannelCommonResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/SetChannelCommon", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) GetRecommendChannel(ctx context.Context, in *GetRecommendChannelReq, opts ...grpc.CallOption) (*GetRecommendChannelResp, error) {
	out := new(GetRecommendChannelResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/GetRecommendChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) GetChannelByTagId(ctx context.Context, in *GetChannelByTagIdReq, opts ...grpc.CallOption) (*GetChannelByTagIdResp, error) {
	out := new(GetChannelByTagIdResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/GetChannelByTagId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) GetChannelTag(ctx context.Context, in *GetChannelTagReq, opts ...grpc.CallOption) (*GetChannelTagResp, error) {
	out := new(GetChannelTagResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/GetChannelTag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) BatchGetChannelTag(ctx context.Context, in *BatchGetChannelTagReq, opts ...grpc.CallOption) (*BatchGetChannelTagResp, error) {
	out := new(BatchGetChannelTagResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/BatchGetChannelTag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) GetHotChannel(ctx context.Context, in *GetHotChannelReq, opts ...grpc.CallOption) (*GetHotChannelResp, error) {
	out := new(GetHotChannelResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/GetHotChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) GetChannelTagAdv(ctx context.Context, in *GetChannelTagAdvReq, opts ...grpc.CallOption) (*GetChannelTagAdvResp, error) {
	out := new(GetChannelTagAdvResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/GetChannelTagAdv", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) AddChannelGift(ctx context.Context, in *AddChannelGiftReq, opts ...grpc.CallOption) (*AddChannelGiftResp, error) {
	out := new(AddChannelGiftResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/AddChannelGift", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) AddChannelTagAdv(ctx context.Context, in *AddChannelTagAdvReq, opts ...grpc.CallOption) (*AddChannelTagAdvResp, error) {
	out := new(AddChannelTagAdvResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/AddChannelTagAdv", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) DelChannelTagAdv(ctx context.Context, in *DelChannelTagAdvReq, opts ...grpc.CallOption) (*DelChannelTagAdvResp, error) {
	out := new(DelChannelTagAdvResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/DelChannelTagAdv", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) GetRankChannels(ctx context.Context, in *GetRankChannelsReq, opts ...grpc.CallOption) (*GetRankChannelsResp, error) {
	out := new(GetRankChannelsResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/GetRankChannels", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) GetChannelTagRanks(ctx context.Context, in *GetChannelTagRankReq, opts ...grpc.CallOption) (*GetChannelTagRankResp, error) {
	out := new(GetChannelTagRankResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/GetChannelTagRanks", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) BatchGetChannelRecommendLevel(ctx context.Context, in *BatchGetChannelRecommendLevelReq, opts ...grpc.CallOption) (*BatchGetChannelRecommendLevelResp, error) {
	out := new(BatchGetChannelRecommendLevelResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/BatchGetChannelRecommendLevel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) GetQuickRecChannelByTagId(ctx context.Context, in *GetQuickRecommendChannelByTagIdReq, opts ...grpc.CallOption) (*GetQuickRecommendChannelByTagIdResp, error) {
	out := new(GetQuickRecommendChannelByTagIdResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/GetQuickRecChannelByTagId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) AddLivePrepareChannel(ctx context.Context, in *AddLivePrepareChannelReq, opts ...grpc.CallOption) (*AddLivePrepareChannelResp, error) {
	out := new(AddLivePrepareChannelResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/AddLivePrepareChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) GetLivePrepareChannelList(ctx context.Context, in *GetLivePrepareChannelListReq, opts ...grpc.CallOption) (*GetLivePrepareChannelListResp, error) {
	out := new(GetLivePrepareChannelListResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/GetLivePrepareChannelList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) DelLivePrepareChannel(ctx context.Context, in *DelLivePrepareChannelReq, opts ...grpc.CallOption) (*DelLivePrepareChannelResp, error) {
	out := new(DelLivePrepareChannelResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/DelLivePrepareChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) AutoAddLivePrepareChannel(ctx context.Context, in *AutoAddLivePrepareChannelReq, opts ...grpc.CallOption) (*AutoAddLivePrepareChannelResp, error) {
	out := new(AutoAddLivePrepareChannelResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/AutoAddLivePrepareChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) GetLivePrepareChannelByPara(ctx context.Context, in *GetLivePrepareChannelByParaReq, opts ...grpc.CallOption) (*GetLivePrepareChannelByParaResp, error) {
	out := new(GetLivePrepareChannelByParaResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/GetLivePrepareChannelByPara", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) UpdateLivePrepareChannel(ctx context.Context, in *UpdateLivePrepareChannelReq, opts ...grpc.CallOption) (*UpdateLivePrepareChannelResp, error) {
	out := new(UpdateLivePrepareChannelResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/UpdateLivePrepareChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) AutoGen(ctx context.Context, in *AutoGenReq, opts ...grpc.CallOption) (*AutoGenResp, error) {
	out := new(AutoGenResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/AutoGen", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) AddWarSongRecommend(ctx context.Context, in *AddWarSongRecommendReq, opts ...grpc.CallOption) (*AddWarSongRecommendResp, error) {
	out := new(AddWarSongRecommendResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/AddWarSongRecommend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) GetWarSongRecommendList(ctx context.Context, in *GetWarSongRecommendListReq, opts ...grpc.CallOption) (*GetWarSongRecommendListResp, error) {
	out := new(GetWarSongRecommendListResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/GetWarSongRecommendList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) UpdateWarSongRecommend(ctx context.Context, in *UpdateWarSongRecommendReq, opts ...grpc.CallOption) (*UpdateWarSongRecommendResp, error) {
	out := new(UpdateWarSongRecommendResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/UpdateWarSongRecommend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) DelWarSongRecommend(ctx context.Context, in *DelWarSongRecommendReq, opts ...grpc.CallOption) (*DelWarSongRecommendResp, error) {
	out := new(DelWarSongRecommendResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/DelWarSongRecommend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) GetWarSongRecChannelList(ctx context.Context, in *GetWarSongRecChannelListReq, opts ...grpc.CallOption) (*GetWarSongRecChannelListResp, error) {
	out := new(GetWarSongRecChannelListResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/GetWarSongRecChannelList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) GetActivityChannelListV2(ctx context.Context, in *GetActivityChannelListV2Req, opts ...grpc.CallOption) (*GetActivityChannelListV2Resp, error) {
	out := new(GetActivityChannelListV2Resp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/GetActivityChannelListV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) UpdateActivityChannel(ctx context.Context, in *UpdateActivityChannelReq, opts ...grpc.CallOption) (*UpdateActivityChannelResp, error) {
	out := new(UpdateActivityChannelResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/UpdateActivityChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) GetPrepareIDListByChannelType(ctx context.Context, in *GetPrepareIDListByChannelTypeReq, opts ...grpc.CallOption) (*GetPrepareIDListByChannelTypeResp, error) {
	out := new(GetPrepareIDListByChannelTypeResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/GetPrepareIDListByChannelType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) GetPrepareChannelListV2(ctx context.Context, in *GetPrepareChannelListV2Req, opts ...grpc.CallOption) (*GetPrepareChannelListV2Resp, error) {
	out := new(GetPrepareChannelListV2Resp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/GetPrepareChannelListV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) ClearLivePrepareChannel(ctx context.Context, in *ClearLivePrepareChannelReq, opts ...grpc.CallOption) (*ClearLivePrepareChannelResp, error) {
	out := new(ClearLivePrepareChannelResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/ClearLivePrepareChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) ClearWarSongRecommend(ctx context.Context, in *ClearWarSongRecommendReq, opts ...grpc.CallOption) (*ClearWarSongRecommendResp, error) {
	out := new(ClearWarSongRecommendResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/ClearWarSongRecommend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entertainmentRecommendBackClient) BatGetChannelRecInfo(ctx context.Context, in *BatGetChannelRecInfoReq, opts ...grpc.CallOption) (*BatGetChannelRecInfoResp, error) {
	out := new(BatGetChannelRecInfoResp)
	err := c.cc.Invoke(ctx, "/entertainmentRecommendBack.EntertainmentRecommendBack/BatGetChannelRecInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EntertainmentRecommendBackServer is the server API for EntertainmentRecommendBack service.
type EntertainmentRecommendBackServer interface {
	AddPrepareChannel(context.Context, *AddPrepareChannelReq) (*AddPrepareChannelResp, error)
	// 根据房间ID 获取指定的推荐库的房间信息 列表
	GetPrepareChannelByChannelID(context.Context, *GetPrepareChannelByChannelIDReq) (*GetPrepareChannelByChannelIDResp, error)
	// 获取全量推荐库房间信息 列表
	// 不建议使用该接口，由于数据量巨大导致性能太低
	// 如果仅是一般查询建议改用 GetPrepareIDListByChannelType 或者 GetPrepareChannelListV2
	GetPrepareChannelByPara(context.Context, *GetPrepareChannelByParaReq) (*GetPrepareChannelByParaResp, error)
	// 添加活动库房间
	AddActivityChannel(context.Context, *AddActivityChannelReq) (*AddActivityChannelResp, error)
	// 获取活动库房间信息 列表
	GetActivityChannelList(context.Context, *GetActivityChannelListReq) (*GetActivityChannelListResp, error)
	GetChannelTagConfigInfo(context.Context, *GetChannelTagConfigInfoReq) (*GetChannelTagConfigInfoResp, error)
	// 删除推荐库房间
	DeletePrepareChannel(context.Context, *DeletePrepareChannelReq) (*DeletePrepareChannelResp, error)
	// 删除活动库房间
	DeleteActivityChannel(context.Context, *DeleteActivityChannelReq) (*DeleteActivityChannelResp, error)
	SetChannelCommon(context.Context, *SetChannelCommonReq) (*SetChannelCommonResp, error)
	GetRecommendChannel(context.Context, *GetRecommendChannelReq) (*GetRecommendChannelResp, error)
	GetChannelByTagId(context.Context, *GetChannelByTagIdReq) (*GetChannelByTagIdResp, error)
	GetChannelTag(context.Context, *GetChannelTagReq) (*GetChannelTagResp, error)
	BatchGetChannelTag(context.Context, *BatchGetChannelTagReq) (*BatchGetChannelTagResp, error)
	GetHotChannel(context.Context, *GetHotChannelReq) (*GetHotChannelResp, error)
	GetChannelTagAdv(context.Context, *GetChannelTagAdvReq) (*GetChannelTagAdvResp, error)
	AddChannelGift(context.Context, *AddChannelGiftReq) (*AddChannelGiftResp, error)
	AddChannelTagAdv(context.Context, *AddChannelTagAdvReq) (*AddChannelTagAdvResp, error)
	DelChannelTagAdv(context.Context, *DelChannelTagAdvReq) (*DelChannelTagAdvResp, error)
	GetRankChannels(context.Context, *GetRankChannelsReq) (*GetRankChannelsResp, error)
	GetChannelTagRanks(context.Context, *GetChannelTagRankReq) (*GetChannelTagRankResp, error)
	BatchGetChannelRecommendLevel(context.Context, *BatchGetChannelRecommendLevelReq) (*BatchGetChannelRecommendLevelResp, error)
	GetQuickRecChannelByTagId(context.Context, *GetQuickRecommendChannelByTagIdReq) (*GetQuickRecommendChannelByTagIdResp, error)
	AddLivePrepareChannel(context.Context, *AddLivePrepareChannelReq) (*AddLivePrepareChannelResp, error)
	GetLivePrepareChannelList(context.Context, *GetLivePrepareChannelListReq) (*GetLivePrepareChannelListResp, error)
	DelLivePrepareChannel(context.Context, *DelLivePrepareChannelReq) (*DelLivePrepareChannelResp, error)
	AutoAddLivePrepareChannel(context.Context, *AutoAddLivePrepareChannelReq) (*AutoAddLivePrepareChannelResp, error)
	GetLivePrepareChannelByPara(context.Context, *GetLivePrepareChannelByParaReq) (*GetLivePrepareChannelByParaResp, error)
	UpdateLivePrepareChannel(context.Context, *UpdateLivePrepareChannelReq) (*UpdateLivePrepareChannelResp, error)
	AutoGen(context.Context, *AutoGenReq) (*AutoGenResp, error)
	AddWarSongRecommend(context.Context, *AddWarSongRecommendReq) (*AddWarSongRecommendResp, error)
	GetWarSongRecommendList(context.Context, *GetWarSongRecommendListReq) (*GetWarSongRecommendListResp, error)
	UpdateWarSongRecommend(context.Context, *UpdateWarSongRecommendReq) (*UpdateWarSongRecommendResp, error)
	DelWarSongRecommend(context.Context, *DelWarSongRecommendReq) (*DelWarSongRecommendResp, error)
	GetWarSongRecChannelList(context.Context, *GetWarSongRecChannelListReq) (*GetWarSongRecChannelListResp, error)
	GetActivityChannelListV2(context.Context, *GetActivityChannelListV2Req) (*GetActivityChannelListV2Resp, error)
	UpdateActivityChannel(context.Context, *UpdateActivityChannelReq) (*UpdateActivityChannelResp, error)
	// 根据房间类型 获取推荐库的房间ID 列表
	// 但是直播间类型的房间太多，不建议一次获取
	GetPrepareIDListByChannelType(context.Context, *GetPrepareIDListByChannelTypeReq) (*GetPrepareIDListByChannelTypeResp, error)
	// 根据房间类型 获取推荐库的房间 列表
	// 但是直播间类型的房间太多，不建议一次获取
	GetPrepareChannelListV2(context.Context, *GetPrepareChannelListV2Req) (*GetPrepareChannelListV2Resp, error)
	ClearLivePrepareChannel(context.Context, *ClearLivePrepareChannelReq) (*ClearLivePrepareChannelResp, error)
	ClearWarSongRecommend(context.Context, *ClearWarSongRecommendReq) (*ClearWarSongRecommendResp, error)
	BatGetChannelRecInfo(context.Context, *BatGetChannelRecInfoReq) (*BatGetChannelRecInfoResp, error)
}

func RegisterEntertainmentRecommendBackServer(s *grpc.Server, srv EntertainmentRecommendBackServer) {
	s.RegisterService(&_EntertainmentRecommendBack_serviceDesc, srv)
}

func _EntertainmentRecommendBack_AddPrepareChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPrepareChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).AddPrepareChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/AddPrepareChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).AddPrepareChannel(ctx, req.(*AddPrepareChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_GetPrepareChannelByChannelID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPrepareChannelByChannelIDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).GetPrepareChannelByChannelID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/GetPrepareChannelByChannelID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).GetPrepareChannelByChannelID(ctx, req.(*GetPrepareChannelByChannelIDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_GetPrepareChannelByPara_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPrepareChannelByParaReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).GetPrepareChannelByPara(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/GetPrepareChannelByPara",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).GetPrepareChannelByPara(ctx, req.(*GetPrepareChannelByParaReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_AddActivityChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddActivityChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).AddActivityChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/AddActivityChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).AddActivityChannel(ctx, req.(*AddActivityChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_GetActivityChannelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActivityChannelListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).GetActivityChannelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/GetActivityChannelList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).GetActivityChannelList(ctx, req.(*GetActivityChannelListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_GetChannelTagConfigInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelTagConfigInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).GetChannelTagConfigInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/GetChannelTagConfigInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).GetChannelTagConfigInfo(ctx, req.(*GetChannelTagConfigInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_DeletePrepareChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePrepareChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).DeletePrepareChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/DeletePrepareChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).DeletePrepareChannel(ctx, req.(*DeletePrepareChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_DeleteActivityChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteActivityChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).DeleteActivityChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/DeleteActivityChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).DeleteActivityChannel(ctx, req.(*DeleteActivityChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_SetChannelCommon_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelCommonReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).SetChannelCommon(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/SetChannelCommon",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).SetChannelCommon(ctx, req.(*SetChannelCommonReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_GetRecommendChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecommendChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).GetRecommendChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/GetRecommendChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).GetRecommendChannel(ctx, req.(*GetRecommendChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_GetChannelByTagId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelByTagIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).GetChannelByTagId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/GetChannelByTagId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).GetChannelByTagId(ctx, req.(*GetChannelByTagIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_GetChannelTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).GetChannelTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/GetChannelTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).GetChannelTag(ctx, req.(*GetChannelTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_BatchGetChannelTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetChannelTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).BatchGetChannelTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/BatchGetChannelTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).BatchGetChannelTag(ctx, req.(*BatchGetChannelTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_GetHotChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHotChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).GetHotChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/GetHotChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).GetHotChannel(ctx, req.(*GetHotChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_GetChannelTagAdv_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelTagAdvReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).GetChannelTagAdv(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/GetChannelTagAdv",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).GetChannelTagAdv(ctx, req.(*GetChannelTagAdvReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_AddChannelGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddChannelGiftReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).AddChannelGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/AddChannelGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).AddChannelGift(ctx, req.(*AddChannelGiftReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_AddChannelTagAdv_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddChannelTagAdvReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).AddChannelTagAdv(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/AddChannelTagAdv",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).AddChannelTagAdv(ctx, req.(*AddChannelTagAdvReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_DelChannelTagAdv_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelChannelTagAdvReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).DelChannelTagAdv(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/DelChannelTagAdv",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).DelChannelTagAdv(ctx, req.(*DelChannelTagAdvReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_GetRankChannels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRankChannelsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).GetRankChannels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/GetRankChannels",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).GetRankChannels(ctx, req.(*GetRankChannelsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_GetChannelTagRanks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelTagRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).GetChannelTagRanks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/GetChannelTagRanks",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).GetChannelTagRanks(ctx, req.(*GetChannelTagRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_BatchGetChannelRecommendLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetChannelRecommendLevelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).BatchGetChannelRecommendLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/BatchGetChannelRecommendLevel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).BatchGetChannelRecommendLevel(ctx, req.(*BatchGetChannelRecommendLevelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_GetQuickRecChannelByTagId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetQuickRecommendChannelByTagIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).GetQuickRecChannelByTagId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/GetQuickRecChannelByTagId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).GetQuickRecChannelByTagId(ctx, req.(*GetQuickRecommendChannelByTagIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_AddLivePrepareChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddLivePrepareChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).AddLivePrepareChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/AddLivePrepareChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).AddLivePrepareChannel(ctx, req.(*AddLivePrepareChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_GetLivePrepareChannelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLivePrepareChannelListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).GetLivePrepareChannelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/GetLivePrepareChannelList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).GetLivePrepareChannelList(ctx, req.(*GetLivePrepareChannelListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_DelLivePrepareChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelLivePrepareChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).DelLivePrepareChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/DelLivePrepareChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).DelLivePrepareChannel(ctx, req.(*DelLivePrepareChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_AutoAddLivePrepareChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AutoAddLivePrepareChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).AutoAddLivePrepareChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/AutoAddLivePrepareChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).AutoAddLivePrepareChannel(ctx, req.(*AutoAddLivePrepareChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_GetLivePrepareChannelByPara_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLivePrepareChannelByParaReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).GetLivePrepareChannelByPara(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/GetLivePrepareChannelByPara",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).GetLivePrepareChannelByPara(ctx, req.(*GetLivePrepareChannelByParaReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_UpdateLivePrepareChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateLivePrepareChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).UpdateLivePrepareChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/UpdateLivePrepareChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).UpdateLivePrepareChannel(ctx, req.(*UpdateLivePrepareChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_AutoGen_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AutoGenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).AutoGen(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/AutoGen",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).AutoGen(ctx, req.(*AutoGenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_AddWarSongRecommend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddWarSongRecommendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).AddWarSongRecommend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/AddWarSongRecommend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).AddWarSongRecommend(ctx, req.(*AddWarSongRecommendReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_GetWarSongRecommendList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWarSongRecommendListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).GetWarSongRecommendList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/GetWarSongRecommendList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).GetWarSongRecommendList(ctx, req.(*GetWarSongRecommendListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_UpdateWarSongRecommend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateWarSongRecommendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).UpdateWarSongRecommend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/UpdateWarSongRecommend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).UpdateWarSongRecommend(ctx, req.(*UpdateWarSongRecommendReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_DelWarSongRecommend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelWarSongRecommendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).DelWarSongRecommend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/DelWarSongRecommend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).DelWarSongRecommend(ctx, req.(*DelWarSongRecommendReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_GetWarSongRecChannelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWarSongRecChannelListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).GetWarSongRecChannelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/GetWarSongRecChannelList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).GetWarSongRecChannelList(ctx, req.(*GetWarSongRecChannelListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_GetActivityChannelListV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActivityChannelListV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).GetActivityChannelListV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/GetActivityChannelListV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).GetActivityChannelListV2(ctx, req.(*GetActivityChannelListV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_UpdateActivityChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateActivityChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).UpdateActivityChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/UpdateActivityChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).UpdateActivityChannel(ctx, req.(*UpdateActivityChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_GetPrepareIDListByChannelType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPrepareIDListByChannelTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).GetPrepareIDListByChannelType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/GetPrepareIDListByChannelType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).GetPrepareIDListByChannelType(ctx, req.(*GetPrepareIDListByChannelTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_GetPrepareChannelListV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPrepareChannelListV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).GetPrepareChannelListV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/GetPrepareChannelListV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).GetPrepareChannelListV2(ctx, req.(*GetPrepareChannelListV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_ClearLivePrepareChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearLivePrepareChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).ClearLivePrepareChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/ClearLivePrepareChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).ClearLivePrepareChannel(ctx, req.(*ClearLivePrepareChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_ClearWarSongRecommend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearWarSongRecommendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).ClearWarSongRecommend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/ClearWarSongRecommend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).ClearWarSongRecommend(ctx, req.(*ClearWarSongRecommendReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntertainmentRecommendBack_BatGetChannelRecInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetChannelRecInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntertainmentRecommendBackServer).BatGetChannelRecInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entertainmentRecommendBack.EntertainmentRecommendBack/BatGetChannelRecInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntertainmentRecommendBackServer).BatGetChannelRecInfo(ctx, req.(*BatGetChannelRecInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _EntertainmentRecommendBack_serviceDesc = grpc.ServiceDesc{
	ServiceName: "entertainmentRecommendBack.EntertainmentRecommendBack",
	HandlerType: (*EntertainmentRecommendBackServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddPrepareChannel",
			Handler:    _EntertainmentRecommendBack_AddPrepareChannel_Handler,
		},
		{
			MethodName: "GetPrepareChannelByChannelID",
			Handler:    _EntertainmentRecommendBack_GetPrepareChannelByChannelID_Handler,
		},
		{
			MethodName: "GetPrepareChannelByPara",
			Handler:    _EntertainmentRecommendBack_GetPrepareChannelByPara_Handler,
		},
		{
			MethodName: "AddActivityChannel",
			Handler:    _EntertainmentRecommendBack_AddActivityChannel_Handler,
		},
		{
			MethodName: "GetActivityChannelList",
			Handler:    _EntertainmentRecommendBack_GetActivityChannelList_Handler,
		},
		{
			MethodName: "GetChannelTagConfigInfo",
			Handler:    _EntertainmentRecommendBack_GetChannelTagConfigInfo_Handler,
		},
		{
			MethodName: "DeletePrepareChannel",
			Handler:    _EntertainmentRecommendBack_DeletePrepareChannel_Handler,
		},
		{
			MethodName: "DeleteActivityChannel",
			Handler:    _EntertainmentRecommendBack_DeleteActivityChannel_Handler,
		},
		{
			MethodName: "SetChannelCommon",
			Handler:    _EntertainmentRecommendBack_SetChannelCommon_Handler,
		},
		{
			MethodName: "GetRecommendChannel",
			Handler:    _EntertainmentRecommendBack_GetRecommendChannel_Handler,
		},
		{
			MethodName: "GetChannelByTagId",
			Handler:    _EntertainmentRecommendBack_GetChannelByTagId_Handler,
		},
		{
			MethodName: "GetChannelTag",
			Handler:    _EntertainmentRecommendBack_GetChannelTag_Handler,
		},
		{
			MethodName: "BatchGetChannelTag",
			Handler:    _EntertainmentRecommendBack_BatchGetChannelTag_Handler,
		},
		{
			MethodName: "GetHotChannel",
			Handler:    _EntertainmentRecommendBack_GetHotChannel_Handler,
		},
		{
			MethodName: "GetChannelTagAdv",
			Handler:    _EntertainmentRecommendBack_GetChannelTagAdv_Handler,
		},
		{
			MethodName: "AddChannelGift",
			Handler:    _EntertainmentRecommendBack_AddChannelGift_Handler,
		},
		{
			MethodName: "AddChannelTagAdv",
			Handler:    _EntertainmentRecommendBack_AddChannelTagAdv_Handler,
		},
		{
			MethodName: "DelChannelTagAdv",
			Handler:    _EntertainmentRecommendBack_DelChannelTagAdv_Handler,
		},
		{
			MethodName: "GetRankChannels",
			Handler:    _EntertainmentRecommendBack_GetRankChannels_Handler,
		},
		{
			MethodName: "GetChannelTagRanks",
			Handler:    _EntertainmentRecommendBack_GetChannelTagRanks_Handler,
		},
		{
			MethodName: "BatchGetChannelRecommendLevel",
			Handler:    _EntertainmentRecommendBack_BatchGetChannelRecommendLevel_Handler,
		},
		{
			MethodName: "GetQuickRecChannelByTagId",
			Handler:    _EntertainmentRecommendBack_GetQuickRecChannelByTagId_Handler,
		},
		{
			MethodName: "AddLivePrepareChannel",
			Handler:    _EntertainmentRecommendBack_AddLivePrepareChannel_Handler,
		},
		{
			MethodName: "GetLivePrepareChannelList",
			Handler:    _EntertainmentRecommendBack_GetLivePrepareChannelList_Handler,
		},
		{
			MethodName: "DelLivePrepareChannel",
			Handler:    _EntertainmentRecommendBack_DelLivePrepareChannel_Handler,
		},
		{
			MethodName: "AutoAddLivePrepareChannel",
			Handler:    _EntertainmentRecommendBack_AutoAddLivePrepareChannel_Handler,
		},
		{
			MethodName: "GetLivePrepareChannelByPara",
			Handler:    _EntertainmentRecommendBack_GetLivePrepareChannelByPara_Handler,
		},
		{
			MethodName: "UpdateLivePrepareChannel",
			Handler:    _EntertainmentRecommendBack_UpdateLivePrepareChannel_Handler,
		},
		{
			MethodName: "AutoGen",
			Handler:    _EntertainmentRecommendBack_AutoGen_Handler,
		},
		{
			MethodName: "AddWarSongRecommend",
			Handler:    _EntertainmentRecommendBack_AddWarSongRecommend_Handler,
		},
		{
			MethodName: "GetWarSongRecommendList",
			Handler:    _EntertainmentRecommendBack_GetWarSongRecommendList_Handler,
		},
		{
			MethodName: "UpdateWarSongRecommend",
			Handler:    _EntertainmentRecommendBack_UpdateWarSongRecommend_Handler,
		},
		{
			MethodName: "DelWarSongRecommend",
			Handler:    _EntertainmentRecommendBack_DelWarSongRecommend_Handler,
		},
		{
			MethodName: "GetWarSongRecChannelList",
			Handler:    _EntertainmentRecommendBack_GetWarSongRecChannelList_Handler,
		},
		{
			MethodName: "GetActivityChannelListV2",
			Handler:    _EntertainmentRecommendBack_GetActivityChannelListV2_Handler,
		},
		{
			MethodName: "UpdateActivityChannel",
			Handler:    _EntertainmentRecommendBack_UpdateActivityChannel_Handler,
		},
		{
			MethodName: "GetPrepareIDListByChannelType",
			Handler:    _EntertainmentRecommendBack_GetPrepareIDListByChannelType_Handler,
		},
		{
			MethodName: "GetPrepareChannelListV2",
			Handler:    _EntertainmentRecommendBack_GetPrepareChannelListV2_Handler,
		},
		{
			MethodName: "ClearLivePrepareChannel",
			Handler:    _EntertainmentRecommendBack_ClearLivePrepareChannel_Handler,
		},
		{
			MethodName: "ClearWarSongRecommend",
			Handler:    _EntertainmentRecommendBack_ClearWarSongRecommend_Handler,
		},
		{
			MethodName: "BatGetChannelRecInfo",
			Handler:    _EntertainmentRecommendBack_BatGetChannelRecInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/entertainmentRecommendBackSvr/entertainmentrecommendback.proto",
}

func init() {
	proto.RegisterFile("src/entertainmentRecommendBackSvr/entertainmentrecommendback.proto", fileDescriptor_entertainmentrecommendback_7b671d4fe7793222)
}

var fileDescriptor_entertainmentrecommendback_7b671d4fe7793222 = []byte{
	// 5182 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x7c, 0x5b, 0x8c, 0x1c, 0x57,
	0x5a, 0xb0, 0xab, 0xbb, 0x67, 0xa6, 0xe7, 0x9b, 0x5b, 0xfb, 0xcc, 0xad, 0xa7, 0x7d, 0x6b, 0x57,
	0xfc, 0x3b, 0x8e, 0xb3, 0xe3, 0x24, 0xce, 0xef, 0x5c, 0x3a, 0x9d, 0x59, 0x7a, 0x2e, 0x9e, 0x8c,
	0x77, 0x3c, 0x76, 0x7a, 0xc6, 0xc9, 0x66, 0xd1, 0x52, 0xa9, 0xa9, 0xaa, 0x69, 0xd7, 0x4e, 0x75,
	0x75, 0xa5, 0x4f, 0xf5, 0x78, 0x06, 0xad, 0xb8, 0x29, 0xa0, 0x85, 0x95, 0x40, 0x42, 0xbc, 0x20,
	0xa1, 0xa0, 0x45, 0x81, 0x5d, 0x81, 0x14, 0xa1, 0x45, 0x48, 0x48, 0x81, 0x37, 0x56, 0x2b, 0xb4,
	0xe2, 0x09, 0xf1, 0xce, 0x0b, 0xda, 0x20, 0x84, 0x10, 0xe2, 0x01, 0x1e, 0x78, 0x40, 0xe7, 0x3b,
	0x55, 0xd5, 0x55, 0x5d, 0xa7, 0xaa, 0x2f, 0xf6, 0x86, 0x27, 0x77, 0x7d, 0xe7, 0xf6, 0xdd, 0xcf,
	0x77, 0xbe, 0xef, 0x1b, 0xc3, 0x3a, 0x6d, 0x6b, 0x2f, 0x19, 0xb6, 0x6b, 0xb4, 0x5d, 0xd5, 0xb4,
	0x9b, 0x86, 0xed, 0xd6, 0x0d, 0xad, 0xd5, 0x6c, 0x1a, 0xb6, 0xbe, 0xae, 0x6a, 0xc7, 0xfb, 0x27,
	0xed, 0xe8, 0x68, 0xdb, 0x1f, 0x3d, 0x54, 0xb5, 0xe3, 0x5b, 0x4e, 0xbb, 0xe5, 0xb6, 0x48, 0x29,
	0x79, 0x7d, 0xe9, 0x1a, 0xfb, 0x68, 0xd9, 0x2f, 0xb9, 0xd6, 0x89, 0x63, 0x6a, 0xc7, 0x96, 0xf1,
	0x12, 0x3d, 0x3e, 0xec, 0x98, 0x96, 0x6b, 0xda, 0xee, 0x99, 0x63, 0xf0, 0x1d, 0xe4, 0x6d, 0x98,
	0x3a, 0x30, 0x9b, 0xc6, 0xbe, 0xa1, 0xb9, 0x66, 0xcb, 0x26, 0x97, 0x00, 0x0e, 0x8d, 0x86, 0x69,
	0x2b, 0xae, 0xd9, 0x34, 0x8a, 0x52, 0x39, 0x73, 0x63, 0xa6, 0x3e, 0x89, 0x10, 0x36, 0x8b, 0xac,
	0x40, 0xde, 0xb0, 0x75, 0x3e, 0x98, 0xc1, 0xc1, 0x09, 0xc3, 0xd6, 0xd9, 0x90, 0xfc, 0x7d, 0x09,
	0xce, 0x6f, 0x3c, 0x56, 0x6d, 0xdb, 0xb0, 0x36, 0xf0, 0xe0, 0x1d, 0xfb, 0xa8, 0xc5, 0xf6, 0xd3,
	0x38, 0x50, 0x31, 0x75, 0x7f, 0x3f, 0x0f, 0xb2, 0xa3, 0x93, 0x45, 0x18, 0x77, 0xd5, 0x06, 0x1b,
	0xe2, 0xbb, 0x8d, 0xb9, 0x6a, 0x63, 0x47, 0x27, 0xcb, 0x30, 0x41, 0x3b, 0x87, 0x8a, 0xab, 0x36,
	0x8a, 0xd9, 0xb2, 0x74, 0x63, 0xb2, 0x3e, 0x4e, 0x3b, 0x87, 0x07, 0x6a, 0x83, 0x6c, 0x40, 0x9e,
	0x72, 0x4c, 0x69, 0x31, 0x57, 0xce, 0xde, 0x98, 0xba, 0xfd, 0xfc, 0xad, 0x64, 0x16, 0xdc, 0x0a,
	0x51, 0x56, 0x0f, 0x16, 0xca, 0x4d, 0x28, 0xf8, 0x88, 0xaa, 0xda, 0x63, 0x03, 0xf1, 0xbc, 0x00,
	0x93, 0xb6, 0xf1, 0x44, 0xb1, 0x8c, 0x13, 0xc3, 0xf2, 0xd0, 0xcc, 0xdb, 0xc6, 0x93, 0x5d, 0xf6,
	0xcd, 0x06, 0x5b, 0x96, 0xee, 0x0d, 0x72, 0x44, 0xf3, 0x2d, 0x4b, 0xe7, 0x83, 0x57, 0x60, 0xea,
	0xa3, 0x8e, 0xa9, 0x1d, 0x7b, 0xc3, 0x0c, 0xdf, 0x99, 0x3a, 0x20, 0x08, 0x27, 0xc8, 0x7f, 0x93,
	0x05, 0xf2, 0xb0, 0x6d, 0x38, 0x6a, 0xdb, 0xf0, 0x8e, 0x7d, 0x0a, 0xce, 0x44, 0xf0, 0xcc, 0xa6,
	0xe1, 0x99, 0xeb, 0xc1, 0xf3, 0x1a, 0xcc, 0xb2, 0x95, 0xd4, 0x55, 0xdb, 0x2e, 0x17, 0xe0, 0x18,
	0xce, 0x98, 0xb6, 0x8d, 0x27, 0xfb, 0x0c, 0x88, 0x02, 0xbe, 0x06, 0xb3, 0x6c, 0x8b, 0xd0, 0xac,
	0x71, 0x3e, 0xab, 0x65, 0xe9, 0xdd, 0x59, 0x21, 0xf9, 0x4c, 0x44, 0xe4, 0x73, 0x05, 0xa6, 0x9a,
	0x2d, 0xdd, 0x3c, 0x3a, 0xe3, 0x6b, 0xf3, 0x9c, 0x19, 0x1c, 0x84, 0x2b, 0xc3, 0x02, 0x9c, 0x1c,
	0x51, 0x80, 0xbd, 0x2c, 0x87, 0x5e, 0x96, 0x93, 0x1b, 0x50, 0xe0, 0x13, 0x42, 0x74, 0x4c, 0xe1,
	0xac, 0x59, 0x84, 0x77, 0x29, 0xb9, 0x0a, 0xd3, 0xbe, 0x14, 0x98, 0x51, 0x14, 0xa7, 0x71, 0xd6,
	0x94, 0x07, 0x3b, 0x38, 0x73, 0x0c, 0xb9, 0x0d, 0xd3, 0xf7, 0xb5, 0x7d, 0xb3, 0xe9, 0x58, 0x5c,
	0x55, 0x0a, 0x90, 0xed, 0x04, 0x12, 0x63, 0x3f, 0x49, 0x11, 0x26, 0x54, 0x4d, 0x6b, 0x75, 0x6c,
	0xb7, 0x98, 0x41, 0x76, 0xf8, 0x9f, 0x28, 0x2e, 0x86, 0x87, 0xad, 0x36, 0x0d, 0x4f, 0x95, 0xf3,
	0x0c, 0xb0, 0xa7, 0x72, 0x63, 0x7a, 0x6c, 0xa8, 0xba, 0x62, 0x36, 0x1b, 0xc5, 0x1c, 0x5f, 0xc7,
	0xbe, 0x77, 0x9a, 0x0d, 0xf9, 0x3f, 0x25, 0x28, 0x79, 0xca, 0x12, 0x30, 0x24, 0x84, 0x42, 0x1f,
	0xdd, 0x29, 0x41, 0x5e, 0x53, 0x5d, 0xa3, 0xd1, 0x6a, 0x9f, 0xf9, 0xea, 0xea, 0x7f, 0x87, 0xf4,
	0x8a, 0x6b, 0x6a, 0xdc, 0xe2, 0x72, 0x11, 0x89, 0x3e, 0x07, 0x33, 0xfe, 0x51, 0x9c, 0xdb, 0x63,
	0xb8, 0xcc, 0xe7, 0x1a, 0xe7, 0xf7, 0x02, 0x8c, 0x51, 0xad, 0xd5, 0x66, 0xca, 0x82, 0x7b, 0xe2,
	0x07, 0xa3, 0x8f, 0x1d, 0x85, 0xb4, 0x73, 0x35, 0x99, 0x70, 0xd5, 0x06, 0x92, 0xbe, 0x00, 0x63,
	0x96, 0x7a, 0x68, 0x58, 0xa8, 0x21, 0x93, 0x75, 0xfe, 0x21, 0xff, 0x8b, 0x04, 0xf3, 0x35, 0xcd,
	0x35, 0x4f, 0x4c, 0xf7, 0x6c, 0x08, 0x53, 0x89, 0xfa, 0xac, 0x4c, 0x9a, 0xcf, 0xca, 0x46, 0x7c,
	0x56, 0x84, 0x51, 0xb9, 0x44, 0x46, 0x8d, 0x25, 0x30, 0x6a, 0x3c, 0xc2, 0xa8, 0x59, 0xc8, 0x98,
	0x3a, 0xd2, 0x39, 0x53, 0xcf, 0x98, 0x88, 0x95, 0xaa, 0xb9, 0x4c, 0x03, 0xdd, 0x0e, 0xf5, 0x2c,
	0x61, 0x52, 0xd5, 0xdc, 0x7d, 0x04, 0xc8, 0xa7, 0xb0, 0x50, 0xd3, 0xf5, 0xa8, 0x5f, 0xa8, 0x1b,
	0x1f, 0x91, 0x0f, 0x61, 0xc1, 0xe1, 0x40, 0x25, 0xe0, 0xbb, 0x49, 0xdd, 0xa2, 0x84, 0xc6, 0x72,
	0x2b, 0xcd, 0x58, 0xe2, 0x4e, 0xa6, 0x4e, 0x9c, 0x08, 0x6c, 0xd7, 0xa4, 0xae, 0xbc, 0x0c, 0x8b,
	0x82, 0x93, 0xa9, 0x23, 0x6f, 0xc2, 0x95, 0x6d, 0xc3, 0x8d, 0x0e, 0xac, 0x07, 0x82, 0xd8, 0x64,
	0xd8, 0x85, 0xcc, 0x25, 0xc0, 0xaa, 0x6b, 0x2e, 0xb8, 0xfd, 0xc7, 0x12, 0x94, 0xd3, 0xb7, 0xa1,
	0xce, 0x97, 0x40, 0xe5, 0x3f, 0x49, 0x50, 0x12, 0xa0, 0xf1, 0x50, 0x6d, 0xab, 0x8c, 0x90, 0xae,
	0x74, 0xa5, 0xb0, 0x74, 0x23, 0xee, 0x35, 0x83, 0x23, 0x09, 0xee, 0x95, 0x5b, 0x4f, 0xd7, 0xbd,
	0xae, 0x40, 0xde, 0xa4, 0x0a, 0x6a, 0x1d, 0x5a, 0x50, 0xbe, 0x3e, 0x61, 0xd2, 0x75, 0xf6, 0x49,
	0x5e, 0x04, 0x62, 0x52, 0xe5, 0xc8, 0xb4, 0x5c, 0xa3, 0xad, 0x58, 0xe6, 0x89, 0xa1, 0xa8, 0x16,
	0xb7, 0xa3, 0x7c, 0x7d, 0xce, 0xa4, 0x77, 0x71, 0x60, 0xd7, 0x3c, 0x31, 0x6a, 0x96, 0x45, 0x5e,
	0x80, 0xf3, 0xe1, 0x99, 0x87, 0x67, 0x8a, 0x4b, 0x3d, 0xb3, 0x9a, 0x3d, 0x0a, 0x66, 0xae, 0x9f,
	0x1d, 0x50, 0xf9, 0x97, 0xe1, 0x42, 0x22, 0x85, 0x5f, 0x0a, 0x8f, 0xbf, 0x1d, 0x96, 0xf4, 0xce,
	0x26, 0x83, 0x05, 0x82, 0x66, 0xae, 0x93, 0x31, 0xfa, 0x26, 0x9c, 0x0f, 0x3b, 0xd8, 0xb0, 0xda,
	0xcc, 0x85, 0xbc, 0x2c, 0x5b, 0x4b, 0x96, 0x60, 0xbc, 0x75, 0x74, 0x44, 0x0d, 0xd7, 0x63, 0xbd,
	0xf7, 0x85, 0xde, 0xc2, 0x6c, 0x9a, 0xae, 0xef, 0xb2, 0xf0, 0x43, 0xfe, 0x1a, 0x5c, 0xed, 0x73,
	0x3a, 0x75, 0xc8, 0x75, 0x98, 0xeb, 0xba, 0x8e, 0xf0, 0xe1, 0x33, 0x81, 0xff, 0x40, 0x52, 0x3e,
	0x11, 0xa9, 0x0b, 0x1b, 0x79, 0xef, 0xf6, 0xb0, 0x54, 0x5c, 0x87, 0x39, 0x93, 0x2a, 0x86, 0xad,
	0x1e, 0x5a, 0x86, 0xa2, 0xb1, 0x08, 0x03, 0xc9, 0xc9, 0xd7, 0x67, 0x4c, 0xba, 0x85, 0x50, 0x0c,
	0x3b, 0x42, 0xd4, 0x66, 0xc5, 0xd4, 0xe6, 0xc2, 0xd4, 0x8a, 0x84, 0xed, 0xe3, 0xf7, 0x25, 0x09,
	0x9b, 0xb9, 0x8d, 0x1e, 0xf7, 0xcc, 0x78, 0xa3, 0xc1, 0xa2, 0xea, 0x41, 0x45, 0x67, 0xbf, 0x94,
	0x76, 0xb6, 0xc0, 0xdb, 0xd7, 0xe7, 0xd5, 0x28, 0x10, 0x4f, 0x2f, 0xc2, 0x92, 0xe8, 0x74, 0xea,
	0xc8, 0x75, 0x58, 0xd9, 0x36, 0xdc, 0x5a, 0x7c, 0x0d, 0xc3, 0x2d, 0x6c, 0x95, 0x52, 0xd4, 0x2a,
	0xa3, 0x97, 0xa4, 0x14, 0xf6, 0xfd, 0xf2, 0xaf, 0x72, 0x6d, 0x10, 0x6e, 0x4a, 0x9d, 0x2f, 0x87,
	0xe2, 0x5f, 0xcf, 0xa0, 0xc4, 0x05, 0x38, 0x70, 0x95, 0xec, 0xaa, 0x0f, 0xbf, 0x10, 0x63, 0xea,
	0xe3, 0xc5, 0x8d, 0xf8, 0x91, 0x74, 0xed, 0x47, 0x6f, 0x56, 0xae, 0x70, 0x09, 0x81, 0xc4, 0x58,
	0x94, 0x47, 0x8c, 0xb5, 0x9a, 0x6f, 0x52, 0xe3, 0x68, 0x09, 0x13, 0x9a, 0x89, 0xc6, 0xc4, 0x0e,
	0x6b, 0xb2, 0xeb, 0x98, 0x7a, 0xd7, 0xe1, 0x58, 0xd3, 0xb4, 0x0f, 0x28, 0x82, 0xd5, 0x53, 0x06,
	0xce, 0x7b, 0x60, 0xf5, 0xf4, 0x80, 0xf6, 0x5c, 0x94, 0x93, 0xbd, 0x17, 0xe5, 0x67, 0x12, 0x5c,
	0x4c, 0xe6, 0x03, 0x75, 0xc8, 0x2e, 0x4c, 0x9a, 0xf6, 0x51, 0xeb, 0xa9, 0x24, 0x90, 0x67, 0x3b,
	0x20, 0xee, 0x57, 0x60, 0xca, 0x36, 0x4e, 0x5d, 0x25, 0x70, 0x44, 0x8c, 0x89, 0xc0, 0x40, 0x0f,
	0x38, 0x7f, 0xaf, 0xc0, 0x94, 0xdb, 0x72, 0x55, 0x4b, 0xe1, 0x01, 0x1f, 0x8f, 0x28, 0x00, 0x41,
	0x1b, 0x0c, 0x22, 0x2b, 0x50, 0x7c, 0xe4, 0xe8, 0xaa, 0x6b, 0x08, 0x6c, 0x65, 0x03, 0x72, 0xec,
	0x24, 0xd4, 0xc5, 0x11, 0xd0, 0xc4, 0xc5, 0xf2, 0x05, 0x58, 0x49, 0x38, 0x80, 0x3a, 0xf2, 0xe7,
	0x19, 0x58, 0xf0, 0x9d, 0xa0, 0xda, 0xd8, 0x68, 0xd9, 0x47, 0x66, 0x03, 0x83, 0xa8, 0xf0, 0x8d,
	0x17, 0x7a, 0x50, 0x10, 0xc8, 0x61, 0x80, 0xc6, 0x08, 0x9d, 0xac, 0xe3, 0x6f, 0x26, 0xda, 0xc3,
	0x63, 0x45, 0x6b, 0x59, 0xad, 0xb6, 0x17, 0xb4, 0x4e, 0x1c, 0x1e, 0x6f, 0xb0, 0x4f, 0x36, 0xdd,
	0xd4, 0x5a, 0xb6, 0x17, 0x24, 0xe2, 0x6f, 0x3f, 0xce, 0xc3, 0xf8, 0x99, 0x6b, 0x09, 0x8b, 0xf3,
	0x98, 0x3f, 0x4c, 0x8e, 0x96, 0x96, 0x81, 0xcd, 0x51, 0x3a, 0x6d, 0xcb, 0x7f, 0x41, 0xb8, 0x6a,
	0xe3, 0x51, 0xdb, 0x62, 0x68, 0x1e, 0x72, 0xb8, 0x17, 0x1a, 0x1e, 0x22, 0xf8, 0x2a, 0x4c, 0x3f,
	0x31, 0x2c, 0xad, 0xd5, 0x34, 0x14, 0xd7, 0x38, 0x75, 0x51, 0x4d, 0x26, 0xeb, 0x53, 0x1e, 0xec,
	0xc0, 0x38, 0x75, 0xd9, 0x94, 0xc3, 0x8e, 0xeb, 0xb6, 0x6c, 0x0f, 0x73, 0xe0, 0x53, 0x38, 0x8c,
	0x63, 0xcf, 0x9e, 0x27, 0x1d, 0xcb, 0x35, 0xbd, 0x19, 0x53, 0xe5, 0xec, 0x8d, 0xc9, 0x3a, 0x20,
	0x08, 0x27, 0xc8, 0x7f, 0x21, 0xc1, 0x8a, 0xc7, 0xbd, 0xfb, 0x0c, 0x1a, 0x65, 0xa1, 0x0c, 0x33,
	0xed, 0x56, 0xcb, 0x55, 0x82, 0xa8, 0x56, 0x42, 0xa6, 0x4d, 0x31, 0xe0, 0x81, 0x17, 0xd9, 0x5e,
	0x86, 0xa9, 0x60, 0x4e, 0xf0, 0x78, 0x9b, 0xf4, 0x66, 0xec, 0xe8, 0xa4, 0x0e, 0xd3, 0x1e, 0x47,
	0xb8, 0xc2, 0x66, 0x51, 0x61, 0x5f, 0x4e, 0xd3, 0x04, 0x91, 0x38, 0xeb, 0xc0, 0x19, 0x89, 0xae,
	0xe2, 0x6d, 0xf4, 0x56, 0xc2, 0x69, 0xc6, 0x47, 0x8c, 0xe8, 0xb6, 0xd1, 0x30, 0x29, 0x8b, 0x29,
	0x54, 0xd7, 0x8b, 0x77, 0xc0, 0x07, 0xd5, 0x5c, 0xf9, 0x9f, 0x25, 0xf4, 0x34, 0xe2, 0xf5, 0xd4,
	0x21, 0xdf, 0x80, 0x42, 0x70, 0xf9, 0xf9, 0x68, 0x4b, 0x23, 0xa2, 0x3d, 0xab, 0x05, 0x50, 0x34,
	0xb7, 0x6f, 0xc1, 0x92, 0xbf, 0x37, 0x97, 0x4c, 0x70, 0x42, 0x06, 0x4f, 0xb8, 0x33, 0xc0, 0x09,
	0x71, 0x49, 0xd5, 0xe7, 0xb5, 0xe8, 0x10, 0xb2, 0xe9, 0x15, 0x28, 0x44, 0xc8, 0x64, 0xcc, 0x49,
	0x7f, 0x5a, 0xc8, 0x1f, 0xc2, 0xf9, 0x9e, 0x25, 0xd4, 0x21, 0x5f, 0xe3, 0xfa, 0xee, 0x19, 0x72,
	0x66, 0x24, 0x3e, 0x30, 0xed, 0x67, 0x3f, 0xe4, 0xaf, 0xc2, 0xe2, 0xba, 0xea, 0x6a, 0x8f, 0x63,
	0x98, 0x0d, 0x1a, 0xb9, 0xb8, 0xb0, 0x24, 0xda, 0xe0, 0x67, 0x2b, 0x37, 0xb9, 0x0a, 0xcb, 0x9b,
	0x86, 0x65, 0xb8, 0x46, 0xfc, 0x05, 0x33, 0xc0, 0x1b, 0xa1, 0x04, 0x45, 0xf1, 0x6a, 0xea, 0xc8,
	0xef, 0xf9, 0x63, 0x02, 0xf7, 0xd9, 0x7f, 0x6b, 0xe6, 0x58, 0x7c, 0x76, 0x65, 0x70, 0x74, 0x9c,
	0x5f, 0x4a, 0xcc, 0x6b, 0x26, 0xec, 0x4b, 0x1d, 0xf9, 0x1f, 0x24, 0x98, 0xdf, 0x0f, 0x18, 0xc8,
	0xf3, 0x57, 0xfd, 0xd5, 0x63, 0xe8, 0xf4, 0x55, 0x6f, 0xb6, 0x21, 0x17, 0xcb, 0x36, 0x90, 0x7b,
	0x30, 0x6d, 0x1c, 0x1d, 0x19, 0x1a, 0xcf, 0x5a, 0xd0, 0xe2, 0xd8, 0x70, 0x49, 0x92, 0x29, 0xbe,
	0x98, 0x81, 0xa8, 0xbc, 0x04, 0x0b, 0x71, 0xa2, 0xa8, 0x23, 0x9b, 0xb0, 0xb4, 0x6d, 0x74, 0x37,
	0x09, 0x31, 0xf8, 0x39, 0x98, 0xe9, 0x50, 0xa3, 0xad, 0x04, 0xb7, 0x3e, 0x27, 0x79, 0x9a, 0x01,
	0x37, 0xfc, 0x9b, 0x9f, 0xbd, 0xf6, 0x5d, 0xb5, 0x1d, 0x44, 0x18, 0xf8, 0xc1, 0xa0, 0xe1, 0x1b,
	0x91, 0x7f, 0xc8, 0xdf, 0x95, 0x60, 0x59, 0x78, 0x16, 0x75, 0xc8, 0x07, 0x02, 0x69, 0x4e, 0xdd,
	0x7e, 0x6d, 0x00, 0xdd, 0x14, 0xe4, 0x44, 0xa2, 0x5a, 0xb0, 0x08, 0xe3, 0x18, 0x83, 0x73, 0xc1,
	0xe4, 0xeb, 0x63, 0x2c, 0xf4, 0xd6, 0xe5, 0x5f, 0x82, 0x85, 0xae, 0x99, 0xac, 0x9f, 0xa1, 0x47,
	0xee, 0x7d, 0x0d, 0x86, 0xe4, 0x38, 0x04, 0xa1, 0x71, 0xce, 0xe5, 0xe2, 0x9c, 0x93, 0x7f, 0x53,
	0x82, 0x45, 0x01, 0x02, 0xff, 0x27, 0xbc, 0xa8, 0xc3, 0xec, 0x3b, 0x2d, 0x77, 0x88, 0x34, 0x4b,
	0xaf, 0xf2, 0x72, 0xa6, 0x44, 0x52, 0x65, 0x1f, 0xa2, 0x87, 0xed, 0x6e, 0xcb, 0x78, 0x1b, 0x30,
	0x51, 0x12, 0x32, 0x31, 0x13, 0x66, 0xe2, 0x2c, 0x64, 0x5c, 0xea, 0x45, 0xa8, 0x19, 0x97, 0xfa,
	0xa9, 0x36, 0x6e, 0x26, 0xec, 0xa7, 0xfc, 0x6b, 0x12, 0x7a, 0xe4, 0xf0, 0x11, 0x83, 0xbf, 0xf2,
	0xd8, 0x0b, 0xbe, 0x6d, 0xa8, 0xda, 0xe3, 0x10, 0x37, 0xf2, 0x08, 0xd8, 0xb2, 0x75, 0x72, 0x03,
	0x0a, 0x41, 0x54, 0xef, 0xef, 0x92, 0xc5, 0x5d, 0x66, 0x7d, 0xb8, 0xe7, 0x72, 0xff, 0x4d, 0x82,
	0x99, 0xae, 0x97, 0xac, 0xe9, 0x27, 0xcc, 0xe2, 0x1d, 0x53, 0xc3, 0xb0, 0x85, 0xc7, 0x04, 0xe3,
	0x8e, 0xa9, 0xb1, 0xb8, 0x65, 0x19, 0x26, 0x54, 0xfd, 0x04, 0x07, 0x78, 0x84, 0x35, 0xae, 0xea,
	0x27, 0x6c, 0xe0, 0x02, 0x4c, 0x62, 0x96, 0x4c, 0x31, 0xf5, 0x53, 0x3f, 0x99, 0x80, 0x80, 0x1d,
	0xfd, 0x94, 0x89, 0x4c, 0x75, 0x9c, 0x6e, 0x48, 0x3e, 0xa6, 0x3a, 0x0e, 0xcf, 0x4e, 0x34, 0xd5,
	0xf6, 0xb1, 0xe1, 0x76, 0xb3, 0x52, 0x79, 0x0e, 0xe0, 0xa1, 0x7c, 0x24, 0x6b, 0x8b, 0x61, 0x34,
	0x0d, 0x12, 0x9d, 0xe1, 0x2c, 0x18, 0x8f, 0xca, 0x83, 0x2c, 0xd8, 0x15, 0x98, 0xd2, 0x2c, 0xd3,
	0xb0, 0x5d, 0x2e, 0x57, 0x2f, 0x69, 0xcb, 0x41, 0x28, 0xd6, 0x3f, 0x91, 0x60, 0x3e, 0x72, 0xbd,
	0xd4, 0xf4, 0x13, 0xef, 0x75, 0xc5, 0x88, 0xc3, 0x55, 0x3c, 0xac, 0x60, 0xc4, 0xa2, 0x1b, 0xeb,
	0x52, 0x90, 0x49, 0xa4, 0x20, 0xdb, 0x43, 0xc1, 0x15, 0x98, 0x32, 0xa9, 0x62, 0x32, 0x8d, 0x37,
	0x5b, 0x6d, 0x2f, 0x8b, 0x02, 0x26, 0xdd, 0xf1, 0x20, 0xbd, 0x88, 0x8e, 0xc5, 0x10, 0xd5, 0xc2,
	0xf6, 0xed, 0xe3, 0x89, 0x37, 0xf6, 0x34, 0xb3, 0x6f, 0x86, 0x6c, 0xc8, 0xba, 0x5e, 0x18, 0xec,
	0x16, 0x64, 0x9b, 0x80, 0x8b, 0xff, 0xa2, 0xf4, 0x7f, 0x3b, 0x03, 0xf3, 0x35, 0x5d, 0x8f, 0x71,
	0x63, 0x78, 0x1d, 0x08, 0xf3, 0x2f, 0x1b, 0xe5, 0x5f, 0x44, 0x3d, 0x72, 0x3d, 0xea, 0x91, 0xaa,
	0x07, 0x5d, 0xce, 0x8f, 0x87, 0x39, 0x1f, 0x55, 0x0f, 0x1e, 0x73, 0x27, 0xa8, 0x07, 0x0f, 0xbc,
	0x93, 0xd4, 0x63, 0x32, 0xc6, 0xf5, 0x25, 0x4c, 0x65, 0xc6, 0xb8, 0x2e, 0x7f, 0x22, 0xc1, 0xfc,
	0xa6, 0x61, 0x0d, 0xce, 0xa8, 0x08, 0x5d, 0x99, 0x44, 0xba, 0xb2, 0x61, 0xba, 0xc2, 0x3c, 0xcc,
	0x45, 0x79, 0xd8, 0x57, 0x5d, 0x96, 0x60, 0x21, 0x8e, 0x1f, 0x75, 0xe4, 0xbb, 0x70, 0xbe, 0x4b,
	0xd0, 0xb6, 0x79, 0xe4, 0x0e, 0x10, 0x0a, 0x10, 0xc8, 0x35, 0xcc, 0x23, 0xdf, 0x9f, 0xe1, 0x6f,
	0x79, 0x01, 0x48, 0xef, 0x3e, 0xd4, 0x91, 0x7f, 0x1e, 0x08, 0xbb, 0x11, 0x55, 0xfb, 0xd8, 0x1b,
	0xa1, 0x6c, 0x7b, 0xe6, 0x9a, 0x54, 0xfb, 0xd8, 0x37, 0x26, 0xcc, 0x45, 0x33, 0x00, 0x52, 0x42,
	0x20, 0xe7, 0xa8, 0x0d, 0xdf, 0xe5, 0xe2, 0xef, 0x84, 0xfb, 0xf6, 0x1b, 0x68, 0xa9, 0xd1, 0xcd,
	0x9f, 0x91, 0x83, 0x94, 0xff, 0x52, 0x02, 0x12, 0x0a, 0x31, 0x55, 0xfb, 0x78, 0xc7, 0x35, 0x9a,
	0x23, 0xc6, 0x48, 0x0b, 0x30, 0x76, 0xa2, 0x5a, 0x1d, 0x3f, 0x25, 0xcf, 0x3f, 0x18, 0xa1, 0x8c,
	0x68, 0xef, 0xf2, 0xc4, 0xdf, 0xe4, 0x1a, 0xcc, 0x5a, 0x2a, 0x75, 0x95, 0x27, 0x86, 0x71, 0xac,
	0xe0, 0xa8, 0x57, 0xb8, 0x62, 0xd0, 0xf7, 0x0d, 0xe3, 0x98, 0x61, 0x12, 0x29, 0x36, 0x8c, 0xa3,
	0x56, 0xf9, 0xc5, 0x06, 0x79, 0xb7, 0xc7, 0x2b, 0xb0, 0xf9, 0x43, 0x06, 0x77, 0xdd, 0x94, 0x89,
	0xfc, 0xcd, 0xf0, 0x15, 0x1e, 0xec, 0x46, 0x1d, 0xb2, 0x09, 0x63, 0x0c, 0x3b, 0x3a, 0x48, 0xce,
	0x2d, 0xce, 0xc6, 0x3a, 0x5f, 0x2c, 0xbf, 0x0d, 0xe5, 0x9e, 0x70, 0x3e, 0x58, 0x89, 0x89, 0x66,
	0xcf, 0xef, 0x6a, 0x51, 0x31, 0xfa, 0xa9, 0x17, 0xb9, 0x0e, 0x8b, 0xc2, 0x65, 0xec, 0x2a, 0xd5,
	0xba, 0x55, 0x2b, 0xcd, 0xd4, 0xc9, 0xf3, 0x30, 0x17, 0x94, 0x94, 0x23, 0xb5, 0xcd, 0xd9, 0x76,
	0x64, 0xa9, 0xfc, 0x1d, 0x09, 0xae, 0xf6, 0xc1, 0x09, 0x93, 0x62, 0x0b, 0x3d, 0xdb, 0x85, 0x7d,
	0xed, 0x2b, 0xc3, 0x44, 0x32, 0x7c, 0x53, 0x12, 0x45, 0x03, 0xc9, 0x7b, 0x0b, 0xe4, 0x6d, 0xc3,
	0x7d, 0xb7, 0x63, 0x6a, 0xc7, 0xbd, 0x21, 0x65, 0xdf, 0x70, 0x4e, 0xde, 0x84, 0xe7, 0xfa, 0x2e,
	0xa6, 0x4e, 0x4c, 0x2d, 0xa2, 0x39, 0x31, 0x79, 0x1d, 0x66, 0x37, 0xd5, 0xb3, 0x70, 0xcd, 0x7c,
	0x05, 0xf2, 0x5e, 0xfd, 0x89, 0xfa, 0xd7, 0x20, 0xaf, 0x3e, 0x61, 0xca, 0x0b, 0xdd, 0x2a, 0xf5,
	0x75, 0x88, 0x39, 0x55, 0x2a, 0x7f, 0x13, 0x48, 0x74, 0x0f, 0x34, 0xbe, 0x6d, 0x98, 0xa4, 0x2c,
	0xf0, 0x0f, 0xb1, 0xed, 0x66, 0x1a, 0xdb, 0xa2, 0x5b, 0xf0, 0xfa, 0x28, 0x72, 0xe9, 0xdf, 0x25,
	0x58, 0xd9, 0x35, 0x4f, 0x0c, 0x24, 0x75, 0x8b, 0x6d, 0xe0, 0x67, 0xc9, 0x59, 0x98, 0x47, 0x20,
	0x17, 0xba, 0xb1, 0xf1, 0x77, 0x6f, 0x45, 0x35, 0x13, 0xab, 0xa8, 0x86, 0xeb, 0xb6, 0x59, 0x4c,
	0x5e, 0x8d, 0x50, 0xb7, 0xfd, 0x3a, 0x14, 0x74, 0xf5, 0x4c, 0xf1, 0xbe, 0x39, 0x9d, 0x39, 0xdc,
	0xec, 0xd6, 0xe0, 0x74, 0x62, 0x16, 0x76, 0x56, 0x57, 0xcf, 0x42, 0xdf, 0xf2, 0x9f, 0xe6, 0x60,
	0x89, 0x51, 0x2c, 0xa8, 0xb3, 0xf3, 0xba, 0x1c, 0x57, 0x84, 0x8c, 0xa9, 0xfb, 0x31, 0x65, 0xa6,
	0x5b, 0xbe, 0x8d, 0x0a, 0x3c, 0xdb, 0xeb, 0x07, 0x56, 0x20, 0xdf, 0xe8, 0x98, 0x96, 0xce, 0xc3,
	0x31, 0xac, 0x1f, 0xe2, 0x77, 0xc4, 0x45, 0x8c, 0xf5, 0xf8, 0x36, 0xce, 0xc7, 0x71, 0x2f, 0x05,
	0x8b, 0x2c, 0xbc, 0xc7, 0x83, 0x90, 0x80, 0x8d, 0x13, 0x98, 0x3a, 0x18, 0xfc, 0x65, 0xe7, 0xaa,
	0x8d, 0x7d, 0x9f, 0x93, 0xa1, 0x17, 0x66, 0x3e, 0xf2, 0xc2, 0xbc, 0xc7, 0xd3, 0x4b, 0xa1, 0x1a,
	0xfb, 0x50, 0xb2, 0x9a, 0xa2, 0x9d, 0xc3, 0xfd, 0x90, 0xb8, 0x42, 0x08, 0x73, 0x71, 0xc1, 0x68,
	0xe2, 0xea, 0xe2, 0xee, 0x5f, 0x33, 0x5a, 0xcb, 0x3e, 0xe2, 0x97, 0xdd, 0x94, 0x97, 0x58, 0x6e,
	0xd9, 0x47, 0x78, 0xd9, 0x29, 0x7e, 0xf1, 0x1e, 0xcf, 0xe0, 0x69, 0x96, 0x69, 0x3c, 0x36, 0x35,
	0x19, 0x94, 0xa8, 0xf0, 0x5e, 0xcd, 0x1f, 0xc1, 0x98, 0x72, 0xf9, 0x08, 0x8a, 0x35, 0x5d, 0x8f,
	0xab, 0x0b, 0x73, 0x1d, 0x8f, 0x84, 0xef, 0xb0, 0xdb, 0xfd, 0x0e, 0x16, 0xd4, 0x50, 0x22, 0x09,
	0x8f, 0x0b, 0xb0, 0x92, 0x70, 0x24, 0x75, 0xe4, 0x5d, 0x4c, 0x70, 0xc7, 0x07, 0xfd, 0x22, 0xc6,
	0x50, 0x99, 0x7e, 0xf9, 0x87, 0x12, 0x5c, 0x4a, 0xd9, 0x8e, 0x3a, 0xe4, 0x41, 0x3c, 0x61, 0x3e,
	0x0a, 0x81, 0xcf, 0x32, 0x67, 0xfe, 0x93, 0x2c, 0x5c, 0xe0, 0x39, 0x6d, 0xb1, 0x58, 0x7a, 0x8d,
	0x38, 0xb0, 0x30, 0xcf, 0xad, 0x8a, 0x2d, 0x6c, 0x48, 0x47, 0x95, 0x64, 0x61, 0xb9, 0x54, 0x0b,
	0x1b, 0x7b, 0x0a, 0x0b, 0x7b, 0x1f, 0xe6, 0x18, 0xc2, 0xba, 0x7a, 0x16, 0x6c, 0x37, 0x3e, 0xb2,
	0x81, 0x6d, 0x76, 0x77, 0x89, 0x1a, 0xd8, 0xc4, 0x00, 0x06, 0x96, 0x7f, 0x96, 0x06, 0x76, 0x19,
	0x2e, 0x26, 0x0b, 0x93, 0x3a, 0xf2, 0xab, 0x98, 0xe2, 0x13, 0x4b, 0x3a, 0x94, 0xbf, 0x93, 0x04,
	0xf9, 0xbb, 0x84, 0x1d, 0x2f, 0x42, 0x69, 0xc3, 0x32, 0xd4, 0xb6, 0x70, 0x4f, 0xf9, 0x12, 0x5c,
	0x48, 0x1c, 0xa5, 0x8e, 0xfc, 0x6d, 0xb8, 0x58, 0xeb, 0xb8, 0xad, 0x44, 0x9f, 0x30, 0x72, 0x80,
	0x1b, 0xee, 0xd2, 0xf2, 0x54, 0x34, 0xc8, 0x68, 0xf8, 0xb7, 0x8f, 0x7c, 0x05, 0x2e, 0xa5, 0x9c,
	0x4e, 0x1d, 0xf9, 0xbf, 0x24, 0xb8, 0x2c, 0x34, 0xe8, 0x6e, 0x37, 0x03, 0x81, 0x5c, 0xc7, 0xd4,
	0xa9, 0xc7, 0x31, 0xfc, 0x1d, 0xb9, 0xb6, 0xb8, 0x95, 0x08, 0xae, 0xad, 0x48, 0x31, 0x30, 0xc0,
	0x38, 0x17, 0x36, 0xaa, 0x6b, 0x30, 0xcb, 0xf4, 0xfd, 0xa3, 0x8e, 0xd1, 0x3e, 0x0b, 0x3f, 0xa4,
	0x98, 0x15, 0xbc, 0xcb, 0x80, 0xa8, 0x53, 0xdc, 0x40, 0xc7, 0x83, 0xee, 0x97, 0x54, 0x05, 0xbc,
	0x0e, 0x73, 0x5c, 0x01, 0xbb, 0x53, 0x78, 0xd2, 0x61, 0x06, 0xc1, 0x1b, 0xde, 0x3c, 0xb9, 0x8d,
	0x0d, 0x29, 0xc9, 0x84, 0xff, 0x0c, 0x7c, 0x99, 0x3c, 0x0d, 0xc0, 0xc4, 0xb1, 0x6d, 0xd8, 0x4c,
	0x73, 0x66, 0x60, 0x2a, 0xf8, 0xa2, 0x8e, 0xfc, 0x53, 0x09, 0x16, 0xde, 0x57, 0xdb, 0xfb, 0x2d,
	0xbb, 0x11, 0x6c, 0x3b, 0x48, 0xea, 0x2c, 0x1e, 0x73, 0xac, 0x40, 0x1e, 0x7b, 0x9e, 0xfc, 0x88,
	0x63, 0xb2, 0x3e, 0x81, 0xdf, 0x5c, 0x0c, 0xa6, 0xed, 0xb6, 0x5b, 0xa8, 0x24, 0x93, 0x75, 0xfe,
	0xc1, 0x2e, 0x81, 0x27, 0x86, 0xd9, 0x78, 0xec, 0x7a, 0xa1, 0x86, 0xf7, 0xc5, 0x18, 0xdd, 0x41,
	0x5b, 0xe3, 0x7d, 0x22, 0xf8, 0x6e, 0xe4, 0x00, 0x1e, 0x7e, 0xb6, 0x1c, 0xa5, 0x83, 0x7d, 0x49,
	0xa8, 0x84, 0x2d, 0xe7, 0x91, 0x19, 0xcf, 0xe4, 0xe5, 0xe3, 0x4d, 0x6f, 0x0d, 0xac, 0xb7, 0xf7,
	0xd2, 0xca, 0x74, 0xed, 0x7e, 0x9c, 0xe3, 0xa9, 0xe5, 0x04, 0x11, 0xbf, 0x42, 0xfc, 0x5e, 0x81,
	0x65, 0xe1, 0x41, 0xd4, 0x91, 0xef, 0x61, 0x59, 0xab, 0x77, 0x68, 0xb4, 0x5b, 0xf1, 0x33, 0x5e,
	0xe3, 0x12, 0x6f, 0x46, 0x9d, 0x67, 0x4c, 0xd5, 0x33, 0xb8, 0x11, 0x55, 0xbf, 0xc8, 0x2b, 0x92,
	0xc1, 0x66, 0x50, 0x46, 0xce, 0x8c, 0x84, 0x28, 0xaf, 0x23, 0x5f, 0x84, 0x52, 0xd2, 0x11, 0xd4,
	0x91, 0x5f, 0x87, 0xa5, 0x4d, 0xc3, 0x12, 0x9d, 0xde, 0xa7, 0x66, 0xb6, 0x82, 0xa5, 0x21, 0xe1,
	0x9e, 0x25, 0x28, 0xa2, 0x23, 0x16, 0xec, 0xca, 0xfc, 0x7b, 0xc2, 0x18, 0x75, 0xe4, 0x7f, 0x94,
	0x60, 0xb1, 0x3b, 0x30, 0x44, 0xd2, 0x3a, 0x6e, 0x79, 0x81, 0x79, 0x65, 0xc3, 0xe6, 0xc5, 0xde,
	0x70, 0x0d, 0xaf, 0x2c, 0xcc, 0xed, 0x6e, 0xe2, 0xb0, 0xc1, 0x8b, 0xc6, 0x97, 0x00, 0x5c, 0x26,
	0x4b, 0x3e, 0x38, 0x86, 0x83, 0x93, 0x0c, 0xc2, 0x87, 0x8b, 0x30, 0xa1, 0xb5, 0x6c, 0xd7, 0xb0,
	0x5d, 0x3f, 0xef, 0xe0, 0x7d, 0xc6, 0xcc, 0x6c, 0x22, 0x6e, 0x66, 0x6f, 0xf5, 0x68, 0x65, 0x4f,
	0xe4, 0x77, 0x11, 0xc0, 0xa4, 0x8a, 0x6d, 0x3c, 0x51, 0x4e, 0x8c, 0xb6, 0xd7, 0xc0, 0x92, 0x37,
	0xe9, 0x9e, 0xf1, 0xe4, 0x3d, 0xa3, 0x2d, 0x7f, 0x97, 0x77, 0x46, 0x24, 0xac, 0xa6, 0x0e, 0x39,
	0x10, 0x06, 0xb3, 0xaf, 0x0c, 0xa6, 0x2e, 0x49, 0xb1, 0x6c, 0x52, 0x3d, 0xe1, 0xcf, 0x25, 0x98,
	0xed, 0x3e, 0xe4, 0x51, 0x36, 0xef, 0x42, 0xbe, 0x6d, 0x68, 0x4a, 0xa8, 0xe3, 0x61, 0xd4, 0x82,
	0xc6, 0x44, 0xdb, 0xdb, 0x32, 0x5c, 0x7b, 0xcd, 0xe0, 0x96, 0x4f, 0x51, 0x7b, 0xbd, 0x0b, 0xcb,
	0xeb, 0xaa, 0x1b, 0xc9, 0x6a, 0xf8, 0x45, 0xf3, 0xe4, 0x14, 0x4b, 0x57, 0xa5, 0x82, 0xa2, 0x84,
	0x09, 0x45, 0xf1, 0x3e, 0xe8, 0x58, 0x44, 0x32, 0xb8, 0x39, 0x18, 0x1f, 0x62, 0xcc, 0xbf, 0xf9,
	0x26, 0xe4, 0x6b, 0x9b, 0xef, 0x29, 0x07, 0x1f, 0x3c, 0xdc, 0x22, 0x0b, 0x50, 0xa8, 0x1d, 0x28,
	0x1b, 0xef, 0xd4, 0xf6, 0xf6, 0xb6, 0x76, 0x95, 0xbb, 0x8f, 0xf6, 0xf6, 0x3e, 0x28, 0x9c, 0x23,
	0xcb, 0x30, 0x1f, 0x82, 0xbe, 0xf3, 0xe0, 0xfe, 0xd6, 0xc3, 0xda, 0xf6, 0x56, 0x41, 0xba, 0xf9,
	0x0b, 0x30, 0xfd, 0x28, 0x5c, 0xc6, 0x2b, 0xc0, 0xf4, 0x9e, 0xf1, 0x84, 0x81, 0x14, 0xa6, 0x8b,
	0x85, 0x73, 0x0c, 0xf2, 0xc0, 0xd2, 0xbb, 0x10, 0x89, 0x9c, 0x87, 0x99, 0xf5, 0x96, 0xfb, 0xb8,
	0x0b, 0xca, 0xb0, 0xfd, 0xbb, 0x51, 0x60, 0x77, 0x20, 0x7b, 0xf3, 0x5b, 0x30, 0x17, 0xb4, 0xd5,
	0x77, 0x8f, 0xf0, 0xab, 0xad, 0xca, 0xfa, 0xce, 0x76, 0xe1, 0x1c, 0x21, 0x30, 0x1b, 0x40, 0xf6,
	0x6b, 0xf7, 0x77, 0x77, 0x0b, 0x12, 0x99, 0x05, 0x78, 0x44, 0x0d, 0x5d, 0xc1, 0x1d, 0x0b, 0x19,
	0x44, 0xac, 0xd5, 0x6e, 0xaa, 0x96, 0x82, 0xb9, 0x87, 0x42, 0x96, 0xcc, 0xc1, 0xd4, 0x3b, 0x2d,
	0x57, 0xf1, 0xb6, 0x2f, 0xe4, 0x6e, 0x9e, 0x40, 0xa1, 0x5b, 0xc6, 0xe5, 0x8d, 0x42, 0x64, 0x09,
	0x48, 0x6d, 0xe3, 0x40, 0xd9, 0x3f, 0xa8, 0x1d, 0x3c, 0xda, 0x57, 0x36, 0xb7, 0xee, 0xd6, 0x1e,
	0xed, 0x1e, 0x14, 0xce, 0x91, 0x22, 0x2c, 0x84, 0xe0, 0x7b, 0x0f, 0xde, 0x57, 0xde, 0xab, 0xed,
	0xee, 0x6c, 0x16, 0x24, 0xb2, 0x02, 0x8b, 0xa1, 0x91, 0xfd, 0x07, 0x0f, 0xf6, 0xbc, 0xa1, 0x0c,
	0x59, 0x84, 0xf3, 0xa1, 0xa1, 0xad, 0xaf, 0x3f, 0xdc, 0xa9, 0x6f, 0x15, 0xb2, 0x37, 0x7f, 0x22,
	0xc1, 0xf4, 0x46, 0xb8, 0xf3, 0x79, 0x3e, 0x20, 0x5a, 0xd9, 0xb1, 0x4f, 0x54, 0xcb, 0xd4, 0x0b,
	0xe7, 0xc2, 0x40, 0x9c, 0xa5, 0xec, 0x17, 0xa4, 0x38, 0xb0, 0x56, 0xc8, 0xc4, 0x81, 0xeb, 0x85,
	0x6c, 0x1c, 0xb8, 0x51, 0xc8, 0xc5, 0x81, 0x9b, 0x85, 0xb1, 0x38, 0x70, 0xab, 0x30, 0x1e, 0x07,
	0xde, 0x2d, 0x4c, 0xc4, 0x81, 0xdb, 0x85, 0xfc, 0xcd, 0x07, 0xb0, 0xb0, 0x15, 0x0e, 0x88, 0xfc,
	0xf8, 0xec, 0x02, 0x2c, 0x6f, 0x3d, 0xac, 0x6f, 0x3d, 0xac, 0xd5, 0xb7, 0x94, 0x8d, 0x07, 0x7b,
	0x77, 0x95, 0x87, 0x5b, 0xf5, 0xfb, 0xb5, 0xbd, 0xad, 0x3d, 0xc6, 0xce, 0x12, 0x2c, 0x45, 0x07,
	0x37, 0x1e, 0xec, 0xed, 0x1f, 0xd4, 0xf6, 0x0e, 0x0a, 0xd2, 0xcd, 0x7b, 0x30, 0xb7, 0x8f, 0xaf,
	0xa4, 0x6e, 0x60, 0x78, 0x1e, 0x66, 0xf6, 0x3b, 0x87, 0x0a, 0x02, 0x94, 0x9a, 0x65, 0x71, 0x35,
	0xeb, 0x82, 0xf6, 0x5a, 0x05, 0x89, 0x69, 0x45, 0x17, 0xf2, 0x8e, 0x7a, 0x62, 0x14, 0x32, 0xb7,
	0xbf, 0x57, 0x81, 0xd2, 0x56, 0xa2, 0x91, 0x90, 0x1f, 0x48, 0x98, 0xa4, 0x8f, 0xc6, 0x72, 0x24,
	0xd5, 0x19, 0x88, 0xfa, 0xad, 0x4b, 0xaf, 0x0c, 0xb9, 0x82, 0x3a, 0xf2, 0xad, 0x5f, 0xf9, 0xf4,
	0x8b, 0xac, 0xf4, 0x5b, 0x9f, 0x7e, 0x91, 0xcd, 0xb9, 0x15, 0xab, 0xf2, 0xbb, 0x9f, 0x7e, 0x91,
	0xbd, 0xb0, 0xea, 0x56, 0x31, 0x88, 0x5e, 0x2b, 0xaf, 0x5a, 0x55, 0xcf, 0x60, 0x4d, 0x9d, 0x59,
	0xfc, 0x1a, 0xf9, 0x11, 0xf7, 0xd3, 0x89, 0x1d, 0xd1, 0xe4, 0xad, 0x34, 0x1c, 0xfa, 0xb4, 0x64,
	0x97, 0xaa, 0xa3, 0x2f, 0xa6, 0x8e, 0x7c, 0x93, 0xd1, 0x92, 0x41, 0x5a, 0x3a, 0x1e, 0x2d, 0xcb,
	0xab, 0x9d, 0x6a, 0xc7, 0x8c, 0x50, 0xc2, 0xe9, 0xf8, 0x6f, 0x5e, 0xcb, 0x17, 0xc5, 0xe2, 0xe4,
	0xb5, 0x21, 0xb1, 0xf0, 0x5e, 0x2e, 0xa5, 0xd7, 0x47, 0x5a, 0x47, 0x1d, 0xd9, 0x62, 0x88, 0x67,
	0x19, 0xe2, 0xe0, 0x56, 0xec, 0x4a, 0xab, 0xa2, 0x56, 0x0e, 0x11, 0xfd, 0x7d, 0x2e, 0x0a, 0x05,
	0x29, 0xb0, 0xab, 0x41, 0x3b, 0xf7, 0x5a, 0x79, 0xb5, 0x55, 0x0d, 0xfa, 0xb7, 0xd7, 0xca, 0xab,
	0x6a, 0x35, 0xde, 0x95, 0xbd, 0x56, 0x5e, 0x3d, 0xac, 0xc6, 0xfa, 0xaf, 0xd7, 0xc8, 0xef, 0x49,
	0x58, 0xca, 0xe9, 0x69, 0x1d, 0x21, 0xfd, 0x94, 0x27, 0xde, 0xc2, 0x52, 0xba, 0x3d, 0xec, 0x12,
	0xea, 0xc8, 0xcb, 0x8c, 0xd6, 0x1c, 0xa3, 0x35, 0x7f, 0x5a, 0x39, 0xab, 0xb8, 0x15, 0x8a, 0x94,
	0x9e, 0x23, 0xdf, 0x93, 0xb0, 0x93, 0x43, 0xd0, 0x1b, 0x49, 0xee, 0xf4, 0x61, 0xac, 0xb8, 0x61,
	0xb6, 0xf4, 0xda, 0x28, 0xcb, 0xa8, 0x23, 0x17, 0x19, 0x8a, 0x63, 0x0c, 0xc5, 0x4c, 0x07, 0x91,
	0x9b, 0xf0, 0xb4, 0x88, 0x7c, 0xca, 0xb5, 0x46, 0xd8, 0x93, 0xd8, 0xef, 0xb4, 0x84, 0x96, 0xb6,
	0xbe, 0x5a, 0x93, 0xd4, 0xca, 0xc6, 0xd1, 0x1c, 0x17, 0xa1, 0xf9, 0x67, 0x12, 0x16, 0x03, 0x63,
	0x3d, 0x49, 0xe4, 0xd5, 0xd4, 0x54, 0x8c, 0xb8, 0x07, 0xaa, 0xf4, 0xff, 0x87, 0x5f, 0x44, 0x1d,
	0xf9, 0x06, 0xc3, 0x6e, 0xc2, 0x33, 0xc6, 0x53, 0xc4, 0x6f, 0x31, 0x30, 0xc6, 0xd3, 0x6a, 0x10,
	0xd5, 0xae, 0x91, 0x4f, 0x24, 0x58, 0x14, 0x76, 0x33, 0x91, 0x01, 0x4e, 0x16, 0x68, 0xe5, 0x9d,
	0x11, 0x56, 0xf9, 0xec, 0xcc, 0x8b, 0xd8, 0xf9, 0x3b, 0x12, 0x14, 0x7a, 0x7b, 0x8f, 0x48, 0x6a,
	0xbf, 0xab, 0xa0, 0xfd, 0xaa, 0xf4, 0xf2, 0x70, 0x0b, 0x7c, 0x8c, 0x40, 0x84, 0xd1, 0xe7, 0xbc,
	0x89, 0xa1, 0xb7, 0xf2, 0x43, 0x6e, 0xf7, 0xd1, 0x25, 0x41, 0x9b, 0x54, 0xe9, 0xd5, 0xa1, 0xd7,
	0x50, 0x47, 0x7e, 0x8b, 0xa1, 0x36, 0x85, 0x56, 0xdc, 0x41, 0x1b, 0xb6, 0x11, 0xc1, 0x6b, 0x81,
	0x84, 0xdd, 0x2a, 0x7b, 0x26, 0xac, 0x95, 0x57, 0x69, 0x15, 0x0b, 0xf3, 0xe8, 0xbf, 0xf0, 0x7d,
	0xb8, 0x46, 0xfe, 0x56, 0x0a, 0x77, 0x22, 0x7a, 0xe5, 0xaa, 0xf4, 0xeb, 0x4e, 0xd4, 0xe9, 0x94,
	0x7e, 0xdd, 0x09, 0x5b, 0x93, 0xe4, 0x7b, 0x0c, 0xef, 0x69, 0x86, 0xf7, 0xa4, 0x8f, 0x77, 0x83,
	0x21, 0x7e, 0x3b, 0x8c, 0xb8, 0xda, 0xc0, 0x5f, 0x71, 0xcc, 0xcb, 0xab, 0x8d, 0xaa, 0xdf, 0x13,
	0xb5, 0x46, 0xfe, 0x4a, 0x82, 0x99, 0x88, 0x7d, 0x92, 0xaf, 0x0c, 0x6c, 0xca, 0x0c, 0xfd, 0xd5,
	0x21, 0x66, 0xfb, 0xa8, 0xcf, 0x3c, 0x1b, 0xd4, 0x7f, 0x2c, 0x01, 0x89, 0x37, 0x5a, 0xa6, 0x5f,
	0x01, 0xc2, 0xce, 0xce, 0xf4, 0x2b, 0x40, 0xdc, 0xcb, 0xc9, 0x29, 0x99, 0x7d, 0x36, 0x94, 0xfc,
	0x06, 0x17, 0x42, 0xb7, 0x87, 0xaa, 0xaf, 0x10, 0x22, 0x1d, 0x5d, 0x7d, 0x85, 0x10, 0x6d, 0xce,
	0xe2, 0x26, 0x39, 0x27, 0x32, 0xc9, 0x1f, 0x4b, 0x3d, 0x1d, 0xb9, 0x35, 0xfd, 0x24, 0xdd, 0x49,
	0x08, 0xba, 0x90, 0x4a, 0x2f, 0x0f, 0xb7, 0x80, 0x3a, 0xf2, 0x43, 0x86, 0x51, 0x01, 0x2d, 0x51,
	0xad, 0x98, 0x95, 0x66, 0xc5, 0x45, 0xbc, 0xde, 0x5c, 0x55, 0xcb, 0x55, 0xbf, 0x99, 0x64, 0xad,
	0xbc, 0x6a, 0x96, 0xab, 0xbc, 0xe3, 0x64, 0xad, 0xbc, 0xda, 0x2c, 0x57, 0x83, 0xd6, 0x14, 0xc6,
	0xec, 0x72, 0x35, 0xd4, 0x5a, 0xb2, 0x46, 0x3e, 0x96, 0x60, 0x36, 0xda, 0xea, 0x41, 0x56, 0xfb,
	0x5c, 0xf4, 0xd1, 0xf6, 0x92, 0xd2, 0xad, 0x61, 0xa6, 0x53, 0x47, 0x9e, 0x67, 0x34, 0x9c, 0xc7,
	0xbb, 0xe2, 0xb4, 0xd2, 0xf0, 0xe2, 0x81, 0xff, 0x91, 0xa0, 0xd0, 0xdb, 0x8a, 0x93, 0xce, 0x50,
	0x41, 0x23, 0x53, 0xe9, 0xe5, 0xe1, 0x16, 0x50, 0x47, 0xfe, 0x58, 0x62, 0xd8, 0x10, 0x86, 0x0d,
	0x71, 0x2a, 0x7a, 0xc5, 0x45, 0xae, 0xd2, 0xca, 0x61, 0xc5, 0xf0, 0xbc, 0x9c, 0xb6, 0xea, 0x94,
	0xab, 0x8e, 0xa9, 0xb1, 0x08, 0x4b, 0x67, 0x4c, 0x0e, 0xfb, 0x3b, 0x93, 0xf1, 0x99, 0x31, 0xd9,
	0xe3, 0x31, 0xd7, 0x61, 0xad, 0xd5, 0x36, 0x30, 0x1e, 0xeb, 0xf6, 0x2d, 0xad, 0x95, 0x57, 0x8d,
	0xaa, 0xdf, 0xa7, 0xc4, 0xf5, 0x3b, 0x2c, 0x85, 0x1f, 0x49, 0x50, 0xe8, 0x6d, 0xe8, 0x49, 0x27,
	0x5f, 0xd0, 0x9e, 0x94, 0x4e, 0xbe, 0xb0, 0x5f, 0x68, 0x8f, 0x51, 0x3f, 0x8f, 0xb1, 0xa8, 0xe3,
	0xe9, 0x13, 0xa7, 0xfa, 0x4e, 0x88, 0x6a, 0x01, 0x91, 0x01, 0x03, 0x7a, 0xe8, 0xf8, 0xbe, 0x04,
	0x73, 0x3d, 0x5d, 0x3c, 0xe4, 0x56, 0xbf, 0x2b, 0x27, 0xda, 0x4f, 0x54, 0x7a, 0x69, 0xa8, 0xf9,
	0xd4, 0x91, 0x5f, 0x65, 0x44, 0x2c, 0x30, 0x22, 0xc6, 0xdd, 0x8a, 0xe3, 0x11, 0x70, 0x79, 0xd5,
	0xad, 0x06, 0x6d, 0x49, 0x6b, 0xe5, 0x55, 0xa7, 0xea, 0xa8, 0x0d, 0x8e, 0xb1, 0xdd, 0x69, 0xae,
	0x91, 0xdf, 0x97, 0xb0, 0x99, 0x29, 0xda, 0xce, 0x42, 0xc9, 0xe0, 0x26, 0xe9, 0xf5, 0xe2, 0x0c,
	0x7a, 0x2f, 0x85, 0xfa, 0x6d, 0xe4, 0xcb, 0x0c, 0xe1, 0x45, 0xf4, 0x2b, 0x3c, 0x56, 0x9a, 0x89,
	0xc6, 0x48, 0x7f, 0x2d, 0xc1, 0xa5, 0xd4, 0xb6, 0x15, 0x52, 0x1d, 0xc2, 0x11, 0xc7, 0xba, 0x70,
	0x4a, 0x6f, 0x3f, 0xc5, 0x6a, 0x1f, 0xfd, 0xa5, 0x64, 0xf4, 0xff, 0x4e, 0xc2, 0x3f, 0x6c, 0xf3,
	0xdb, 0x55, 0x7a, 0x6e, 0xfe, 0xb5, 0x3e, 0xfc, 0xea, 0xd3, 0x22, 0x53, 0xfa, 0xea, 0x53, 0xad,
	0xa7, 0x8e, 0xfc, 0x22, 0x43, 0x7f, 0xd9, 0x7b, 0x04, 0x73, 0x65, 0x29, 0x86, 0x1e, 0xc1, 0x76,
	0xd5, 0x35, 0xa8, 0xab, 0x78, 0xd1, 0xcb, 0xe7, 0x12, 0xfe, 0xf1, 0x60, 0xbc, 0xf8, 0x92, 0x1e,
	0xae, 0x26, 0x55, 0xe4, 0xd2, 0xc3, 0xd5, 0xe4, 0x4a, 0xda, 0x1d, 0x86, 0x73, 0x91, 0xe1, 0x3c,
	0x66, 0x32, 0x2f, 0xc5, 0x90, 0xbe, 0xca, 0xdc, 0x3c, 0xfa, 0x74, 0xbd, 0x1c, 0x62, 0x3f, 0xba,
	0x78, 0x4e, 0x0c, 0xc3, 0x7e, 0x25, 0xb1, 0xa2, 0x4e, 0xde, 0xe8, 0xc3, 0xc9, 0xc4, 0xba, 0x7e,
	0xe9, 0xcd, 0x11, 0x57, 0x52, 0x47, 0x7e, 0x81, 0x51, 0xb2, 0x82, 0xdc, 0x67, 0x94, 0x30, 0x42,
	0x96, 0x18, 0x21, 0xbc, 0xde, 0xc0, 0x89, 0xc1, 0xc2, 0xc7, 0x1a, 0xf9, 0x8c, 0x3f, 0x15, 0x86,
	0xe5, 0x7d, 0x52, 0x81, 0xb6, 0xef, 0x53, 0x21, 0x81, 0xf7, 0xf8, 0xb6, 0x29, 0xa1, 0xba, 0x9b,
	0x5e, 0x9a, 0x01, 0x19, 0x8f, 0x59, 0xd1, 0x32, 0x75, 0x2c, 0xd3, 0x2d, 0x1f, 0x9e, 0x95, 0xbf,
	0xb2, 0x46, 0xfe, 0x5e, 0x82, 0x95, 0xc4, 0x8a, 0x68, 0x3a, 0xbb, 0xd3, 0xca, 0xb8, 0xe9, 0xec,
	0x4e, 0x2f, 0xc1, 0x62, 0xe8, 0x7e, 0x01, 0x7d, 0xa3, 0x59, 0xf1, 0x73, 0x3e, 0xd7, 0x19, 0x01,
	0xdd, 0xda, 0x43, 0xa0, 0x32, 0xfc, 0xb7, 0x55, 0xae, 0xf2, 0x44, 0x03, 0xf9, 0x0f, 0x5e, 0x7a,
	0x4a, 0x2a, 0x63, 0x92, 0xca, 0xd0, 0x6a, 0xd0, 0x4d, 0x9f, 0xbc, 0x35, 0xf2, 0x5a, 0xea, 0xc8,
	0x1f, 0x30, 0xaa, 0x2e, 0xe2, 0xb5, 0xd5, 0xa9, 0x34, 0x90, 0xae, 0xc7, 0x48, 0xd9, 0xcf, 0xad,
	0x76, 0xca, 0x5e, 0x54, 0xd9, 0x28, 0x57, 0xfd, 0x2a, 0x72, 0x12, 0x81, 0xe5, 0xd5, 0xc7, 0xe5,
	0x6a, 0xa8, 0x44, 0xac, 0x36, 0xf0, 0x06, 0x2b, 0x26, 0xb5, 0x00, 0x90, 0xd4, 0xd7, 0x7b, 0x4a,
	0x17, 0x48, 0xe9, 0x8d, 0xd1, 0x16, 0xfa, 0x31, 0xe8, 0xa5, 0x90, 0xf6, 0x4d, 0x78, 0x66, 0x4f,
	0x3a, 0x30, 0xe1, 0x55, 0x78, 0xc9, 0xf5, 0x7e, 0x0a, 0xc2, 0x8b, 0xc2, 0xa5, 0xe7, 0x07, 0x9a,
	0x47, 0x1d, 0x79, 0x85, 0x9d, 0x7a, 0x39, 0x14, 0xf9, 0xe6, 0x7d, 0xc6, 0xb2, 0x8b, 0x73, 0x5e,
	0x50, 0xf7, 0x24, 0xfd, 0xd2, 0x43, 0x82, 0xca, 0x59, 0xfa, 0x6b, 0x34, 0xa9, 0xb8, 0x8a, 0xb8,
	0x5d, 0x11, 0xe2, 0xf6, 0xc7, 0x3c, 0x63, 0x23, 0xaa, 0x95, 0xf6, 0xcd, 0xd8, 0x24, 0x54, 0x6b,
	0xfb, 0x66, 0x6c, 0x92, 0x0a, 0xb3, 0x1c, 0xcf, 0xb2, 0x10, 0xcf, 0x3f, 0x92, 0x60, 0x49, 0x5c,
	0xc0, 0x4c, 0xcf, 0x7e, 0x25, 0xd6, 0x55, 0xd3, 0xb3, 0x5f, 0x29, 0xb5, 0x52, 0x44, 0xf2, 0x6a,
	0xa2, 0xa0, 0x05, 0xe5, 0xd0, 0x74, 0x41, 0x8b, 0x0b, 0xaf, 0xa5, 0x57, 0x87, 0x5e, 0xe3, 0xe3,
	0x26, 0x0b, 0x71, 0xfb, 0x81, 0x04, 0xc5, 0xa4, 0x02, 0x22, 0x19, 0x5c, 0x62, 0x3d, 0xd7, 0xda,
	0x1b, 0xa3, 0x2d, 0xf4, 0x51, 0x7d, 0x2e, 0x0d, 0x55, 0xe1, 0x5f, 0x81, 0xf7, 0x45, 0x35, 0xe9,
	0x6f, 0xe8, 0xfb, 0xa2, 0x9a, 0xf8, 0x47, 0xe7, 0x1c, 0xd5, 0x6b, 0x42, 0x54, 0xff, 0x50, 0x82,
	0x45, 0xe1, 0xdf, 0x67, 0xa7, 0x5f, 0xb8, 0x49, 0x7f, 0x33, 0x5e, 0xba, 0x33, 0xc2, 0x2a, 0x1f,
	0xc3, 0xff, 0x27, 0xc4, 0xf0, 0xa7, 0xbc, 0x45, 0x30, 0xf9, 0xbf, 0xce, 0x20, 0x03, 0x16, 0x15,
	0xc4, 0xff, 0xe7, 0x47, 0x7a, 0x64, 0xdc, 0xf7, 0xff, 0xec, 0x90, 0x77, 0x18, 0xe6, 0xd7, 0xbd,
	0x44, 0xd9, 0x69, 0x85, 0x7a, 0xf7, 0xed, 0xed, 0xee, 0xad, 0x74, 0x1a, 0x5c, 0xbc, 0x65, 0x3f,
	0x65, 0x16, 0x8a, 0x7d, 0x2c, 0x3f, 0xf6, 0x29, 0x93, 0x7f, 0x15, 0x95, 0x2c, 0x3c, 0xad, 0x19,
	0xae, 0x64, 0xd1, 0x55, 0x9a, 0xd7, 0x47, 0x5a, 0x47, 0x1d, 0x59, 0x61, 0x74, 0x3d, 0xef, 0xdd,
	0xb7, 0x9c, 0x2e, 0x5e, 0xb2, 0xb8, 0x3b, 0x3c, 0x65, 0xab, 0x87, 0xe5, 0xaa, 0x49, 0xcb, 0xfc,
	0x7f, 0x19, 0x29, 0xe3, 0xff, 0x32, 0xc2, 0x1d, 0x77, 0x42, 0xa3, 0x5b, 0x3a, 0xb5, 0xc9, 0xbd,
	0x73, 0xe9, 0xd4, 0xa6, 0x75, 0xd5, 0xa1, 0xfe, 0xdd, 0x48, 0xb4, 0x10, 0x61, 0xaf, 0x47, 0xba,
	0x85, 0x24, 0xb5, 0x8e, 0xa4, 0x5b, 0x48, 0x72, 0x53, 0x09, 0x62, 0xf8, 0x82, 0x10, 0xc3, 0x3f,
	0x90, 0x60, 0x41, 0x54, 0xd2, 0x4f, 0xaf, 0x06, 0x24, 0x34, 0x13, 0xa4, 0x57, 0x03, 0x92, 0x3a,
	0x07, 0x38, 0x7a, 0x2f, 0x8a, 0xd0, 0x2b, 0x8d, 0x7f, 0xe7, 0xd3, 0x2f, 0xb2, 0x3f, 0xfc, 0xc5,
	0xff, 0x0d, 0x00, 0x00, 0xff, 0xff, 0xe7, 0x44, 0x92, 0xeb, 0x43, 0x50, 0x00, 0x00,
}
