// Code generated by protoc-gen-gogo.
// source: services/goddess/goddess.proto
// DO NOT EDIT!

/*
	Package goddess is a generated protocol buffer package.

	It is generated from these files:
		services/goddess/goddess.proto

	It has these top-level messages:
		GoddessBaseInfo
		GoddessExtendInfo
		GoddessSignupAuditInfo
		GoddessDetailInfo
		SignupReq
		ModifySignupInfoReq
		DelSignupInfoReq
		DelSignupInfoResp
		AuditSignupInfoReq
		GetUserBaseInfoReq
		GetUserBaseInfoResp
		GetUserExtendInfoReq
		GetUserExtendInfoResp
		GetUserDetailInfoReq
		GetUserDetailInfoResp
		GoddessClassifyInfo
		GetAllGoddessClassifyInfoReq
		GetAllGoddessClassifyInfoResp
		GetUserDetailListBySignupStatusReq
		GetUserDetailListBySignupStatusResp
		GetUserBaseListByGoddessClassifyReq
		GetUserBaseListByGoddessClassifyResp
		BatGetGoddessDetailListReq
		BatGetGoddessDetailListResp
		ModifyGoddessSignupGuildInfoReq
		GoddessTrend
		GetGoddessTrendsByGoddessClassifyReq
		GetGoddessTrendsByGoddessUidReq
		GoddessTrendsResp
		TeaseGoddessReq
		TeaseGoddessResp
		BrushVoteCheckOpt
		GoddessVote
		VoteGoddessReq
		VoteGoddessResp
		GetGoddessRankingReq
		GetGoddessRankingResp
		GetGoddessFinalRankingReq
		GetGoddessRankInRankingReq
		GetGoddessRankInRankingResp
		GetGoddessVotesReq
		GetGoddessVotesResp
		GetUserVoteChancesReq
		GetUserVoteChancesResp
		GetUserToGoddessVotesReq
		GetUserToGoddessVotesResp
		AddExtraVotesReq
		AddExtraVotesResp
		ThirdPartyVoteGoddessReq
		ThirdPartyVoteGoddessResp
		ThirdPartyCheckVoteChancesReq
		ThirdPartyCheckVoteChancesResp
		VoteID
		ShareID
		CreateVoteIDReq
		CreateVoteIDResp
		CreateShareIDReq
		CreateShareIDResp
		GetCurrentStageReq
		GetCurrentStageResp
		NotifyRewardUserVoteChancesReq
		NotifyRewardUserVoteChancesResp
		VoteStatisticsAsyncData
		RewardStatisticsAsyncData
		TeaseStatisticsAsyncData
*/
package goddess

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type SIGNUP_STATUS_TYPE int32

const (
	SIGNUP_STATUS_TYPE_SIGNUP_STAT_NOT              SIGNUP_STATUS_TYPE = 1
	SIGNUP_STATUS_TYPE_SIGNUP_STAT_WAIT_AUDIT       SIGNUP_STATUS_TYPE = 2
	SIGNUP_STATUS_TYPE_SIGNUP_STAT_AUDIT_WAITMODIFY SIGNUP_STATUS_TYPE = 3
	SIGNUP_STATUS_TYPE_SIGNUP_STAT_AUDIT_NOTPASS    SIGNUP_STATUS_TYPE = 4
	SIGNUP_STATUS_TYPE_SIGNUP_STAT_OK               SIGNUP_STATUS_TYPE = 5
)

var SIGNUP_STATUS_TYPE_name = map[int32]string{
	1: "SIGNUP_STAT_NOT",
	2: "SIGNUP_STAT_WAIT_AUDIT",
	3: "SIGNUP_STAT_AUDIT_WAITMODIFY",
	4: "SIGNUP_STAT_AUDIT_NOTPASS",
	5: "SIGNUP_STAT_OK",
}
var SIGNUP_STATUS_TYPE_value = map[string]int32{
	"SIGNUP_STAT_NOT":              1,
	"SIGNUP_STAT_WAIT_AUDIT":       2,
	"SIGNUP_STAT_AUDIT_WAITMODIFY": 3,
	"SIGNUP_STAT_AUDIT_NOTPASS":    4,
	"SIGNUP_STAT_OK":               5,
}

func (x SIGNUP_STATUS_TYPE) Enum() *SIGNUP_STATUS_TYPE {
	p := new(SIGNUP_STATUS_TYPE)
	*p = x
	return p
}
func (x SIGNUP_STATUS_TYPE) String() string {
	return proto.EnumName(SIGNUP_STATUS_TYPE_name, int32(x))
}
func (x *SIGNUP_STATUS_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(SIGNUP_STATUS_TYPE_value, data, "SIGNUP_STATUS_TYPE")
	if err != nil {
		return err
	}
	*x = SIGNUP_STATUS_TYPE(value)
	return nil
}
func (SIGNUP_STATUS_TYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{0} }

type RankingType int32

const (
	RankingType_HOURLY  RankingType = 1
	RankingType_DAILY   RankingType = 2
	RankingType_FINALLY RankingType = 3
	RankingType_VOTED   RankingType = 4
)

var RankingType_name = map[int32]string{
	1: "HOURLY",
	2: "DAILY",
	3: "FINALLY",
	4: "VOTED",
}
var RankingType_value = map[string]int32{
	"HOURLY":  1,
	"DAILY":   2,
	"FINALLY": 3,
	"VOTED":   4,
}

func (x RankingType) Enum() *RankingType {
	p := new(RankingType)
	*p = x
	return p
}
func (x RankingType) String() string {
	return proto.EnumName(RankingType_name, int32(x))
}
func (x *RankingType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(RankingType_value, data, "RankingType")
	if err != nil {
		return err
	}
	*x = RankingType(value)
	return nil
}
func (RankingType) EnumDescriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{1} }

type Stage int32

const (
	Stage_None    Stage = 0
	Stage_Signup  Stage = 1
	Stage_Vote    Stage = 2
	Stage_Finally Stage = 3
	Stage_End     Stage = 4
)

var Stage_name = map[int32]string{
	0: "None",
	1: "Signup",
	2: "Vote",
	3: "Finally",
	4: "End",
}
var Stage_value = map[string]int32{
	"None":    0,
	"Signup":  1,
	"Vote":    2,
	"Finally": 3,
	"End":     4,
}

func (x Stage) Enum() *Stage {
	p := new(Stage)
	*p = x
	return p
}
func (x Stage) String() string {
	return proto.EnumName(Stage_name, int32(x))
}
func (x *Stage) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Stage_value, data, "Stage")
	if err != nil {
		return err
	}
	*x = Stage(value)
	return nil
}
func (Stage) EnumDescriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{2} }

// 触发 给目标用户 奖励
type RewardType int32

const (
	RewardType_REWARD_TYPE_DAILY RewardType = 1
	RewardType_REWARD_TYPE_SHARE RewardType = 2
)

var RewardType_name = map[int32]string{
	1: "REWARD_TYPE_DAILY",
	2: "REWARD_TYPE_SHARE",
}
var RewardType_value = map[string]int32{
	"REWARD_TYPE_DAILY": 1,
	"REWARD_TYPE_SHARE": 2,
}

func (x RewardType) Enum() *RewardType {
	p := new(RewardType)
	*p = x
	return p
}
func (x RewardType) String() string {
	return proto.EnumName(RewardType_name, int32(x))
}
func (x *RewardType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(RewardType_value, data, "RewardType")
	if err != nil {
		return err
	}
	*x = RewardType(value)
	return nil
}
func (RewardType) EnumDescriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{3} }

// 女神基本信息
type GoddessBaseInfo struct {
	Uid            uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GuildId        uint32 `protobuf:"varint,2,opt,name=guild_id,json=guildId" json:"guild_id"`
	ClassifyType   uint32 `protobuf:"varint,3,opt,name=classify_type,json=classifyType" json:"classify_type"`
	SignupStat     uint32 `protobuf:"varint,4,opt,name=signup_stat,json=signupStat" json:"signup_stat"`
	SignupTs       uint32 `protobuf:"varint,5,opt,name=signup_ts,json=signupTs" json:"signup_ts"`
	SignupUpdateTs uint32 `protobuf:"varint,6,opt,name=signup_update_ts,json=signupUpdateTs" json:"signup_update_ts"`
}

func (m *GoddessBaseInfo) Reset()                    { *m = GoddessBaseInfo{} }
func (m *GoddessBaseInfo) String() string            { return proto.CompactTextString(m) }
func (*GoddessBaseInfo) ProtoMessage()               {}
func (*GoddessBaseInfo) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{0} }

func (m *GoddessBaseInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GoddessBaseInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GoddessBaseInfo) GetClassifyType() uint32 {
	if m != nil {
		return m.ClassifyType
	}
	return 0
}

func (m *GoddessBaseInfo) GetSignupStat() uint32 {
	if m != nil {
		return m.SignupStat
	}
	return 0
}

func (m *GoddessBaseInfo) GetSignupTs() uint32 {
	if m != nil {
		return m.SignupTs
	}
	return 0
}

func (m *GoddessBaseInfo) GetSignupUpdateTs() uint32 {
	if m != nil {
		return m.SignupUpdateTs
	}
	return 0
}

// 报名扩展信息
type GoddessExtendInfo struct {
	IntroInfo  string   `protobuf:"bytes,1,opt,name=intro_info,json=introInfo" json:"intro_info"`
	PhotoList  []string `protobuf:"bytes,2,rep,name=photo_list,json=photoList" json:"photo_list,omitempty"`
	GameList   []string `protobuf:"bytes,3,rep,name=game_list,json=gameList" json:"game_list,omitempty"`
	Job        string   `protobuf:"bytes,4,opt,name=job" json:"job"`
	ZodiacSign string   `protobuf:"bytes,5,opt,name=zodiac_sign,json=zodiacSign" json:"zodiac_sign"`
	Height     uint32   `protobuf:"varint,6,opt,name=height" json:"height"`
	Bust       string   `protobuf:"bytes,7,opt,name=bust" json:"bust"`
}

func (m *GoddessExtendInfo) Reset()                    { *m = GoddessExtendInfo{} }
func (m *GoddessExtendInfo) String() string            { return proto.CompactTextString(m) }
func (*GoddessExtendInfo) ProtoMessage()               {}
func (*GoddessExtendInfo) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{1} }

func (m *GoddessExtendInfo) GetIntroInfo() string {
	if m != nil {
		return m.IntroInfo
	}
	return ""
}

func (m *GoddessExtendInfo) GetPhotoList() []string {
	if m != nil {
		return m.PhotoList
	}
	return nil
}

func (m *GoddessExtendInfo) GetGameList() []string {
	if m != nil {
		return m.GameList
	}
	return nil
}

func (m *GoddessExtendInfo) GetJob() string {
	if m != nil {
		return m.Job
	}
	return ""
}

func (m *GoddessExtendInfo) GetZodiacSign() string {
	if m != nil {
		return m.ZodiacSign
	}
	return ""
}

func (m *GoddessExtendInfo) GetHeight() uint32 {
	if m != nil {
		return m.Height
	}
	return 0
}

func (m *GoddessExtendInfo) GetBust() string {
	if m != nil {
		return m.Bust
	}
	return ""
}

// 审核信息
type GoddessSignupAuditInfo struct {
	QqCode    string `protobuf:"bytes,1,opt,name=qq_code,json=qqCode" json:"qq_code"`
	Phone     string `protobuf:"bytes,2,opt,name=phone" json:"phone"`
	AuditInfo string `protobuf:"bytes,3,opt,name=audit_info,json=auditInfo" json:"audit_info"`
}

func (m *GoddessSignupAuditInfo) Reset()                    { *m = GoddessSignupAuditInfo{} }
func (m *GoddessSignupAuditInfo) String() string            { return proto.CompactTextString(m) }
func (*GoddessSignupAuditInfo) ProtoMessage()               {}
func (*GoddessSignupAuditInfo) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{2} }

func (m *GoddessSignupAuditInfo) GetQqCode() string {
	if m != nil {
		return m.QqCode
	}
	return ""
}

func (m *GoddessSignupAuditInfo) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *GoddessSignupAuditInfo) GetAuditInfo() string {
	if m != nil {
		return m.AuditInfo
	}
	return ""
}

type GoddessDetailInfo struct {
	BaseInfo   *GoddessBaseInfo        `protobuf:"bytes,1,req,name=base_info,json=baseInfo" json:"base_info,omitempty"`
	ExtendInfo *GoddessExtendInfo      `protobuf:"bytes,2,opt,name=extend_info,json=extendInfo" json:"extend_info,omitempty"`
	AuditInfo  *GoddessSignupAuditInfo `protobuf:"bytes,3,opt,name=audit_info,json=auditInfo" json:"audit_info,omitempty"`
}

func (m *GoddessDetailInfo) Reset()                    { *m = GoddessDetailInfo{} }
func (m *GoddessDetailInfo) String() string            { return proto.CompactTextString(m) }
func (*GoddessDetailInfo) ProtoMessage()               {}
func (*GoddessDetailInfo) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{3} }

func (m *GoddessDetailInfo) GetBaseInfo() *GoddessBaseInfo {
	if m != nil {
		return m.BaseInfo
	}
	return nil
}

func (m *GoddessDetailInfo) GetExtendInfo() *GoddessExtendInfo {
	if m != nil {
		return m.ExtendInfo
	}
	return nil
}

func (m *GoddessDetailInfo) GetAuditInfo() *GoddessSignupAuditInfo {
	if m != nil {
		return m.AuditInfo
	}
	return nil
}

// 报名
type SignupReq struct {
	Info *GoddessDetailInfo `protobuf:"bytes,1,req,name=info" json:"info,omitempty"`
}

func (m *SignupReq) Reset()                    { *m = SignupReq{} }
func (m *SignupReq) String() string            { return proto.CompactTextString(m) }
func (*SignupReq) ProtoMessage()               {}
func (*SignupReq) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{4} }

func (m *SignupReq) GetInfo() *GoddessDetailInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

// 修改报名数据
type ModifySignupInfoReq struct {
	Info   *GoddessDetailInfo `protobuf:"bytes,1,req,name=info" json:"info,omitempty"`
	OpUser string             `protobuf:"bytes,2,req,name=op_user,json=opUser" json:"op_user"`
}

func (m *ModifySignupInfoReq) Reset()                    { *m = ModifySignupInfoReq{} }
func (m *ModifySignupInfoReq) String() string            { return proto.CompactTextString(m) }
func (*ModifySignupInfoReq) ProtoMessage()               {}
func (*ModifySignupInfoReq) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{5} }

func (m *ModifySignupInfoReq) GetInfo() *GoddessDetailInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *ModifySignupInfoReq) GetOpUser() string {
	if m != nil {
		return m.OpUser
	}
	return ""
}

// 删除报名(可以对所有状态的报名信息操作)
type DelSignupInfoReq struct {
	SignupUid uint32 `protobuf:"varint,1,req,name=signup_uid,json=signupUid" json:"signup_uid"`
	OpUser    string `protobuf:"bytes,2,req,name=op_user,json=opUser" json:"op_user"`
	OpReason  string `protobuf:"bytes,3,req,name=op_reason,json=opReason" json:"op_reason"`
}

func (m *DelSignupInfoReq) Reset()                    { *m = DelSignupInfoReq{} }
func (m *DelSignupInfoReq) String() string            { return proto.CompactTextString(m) }
func (*DelSignupInfoReq) ProtoMessage()               {}
func (*DelSignupInfoReq) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{6} }

func (m *DelSignupInfoReq) GetSignupUid() uint32 {
	if m != nil {
		return m.SignupUid
	}
	return 0
}

func (m *DelSignupInfoReq) GetOpUser() string {
	if m != nil {
		return m.OpUser
	}
	return ""
}

func (m *DelSignupInfoReq) GetOpReason() string {
	if m != nil {
		return m.OpReason
	}
	return ""
}

type DelSignupInfoResp struct {
	BeforeDelSignupstate uint32 `protobuf:"varint,1,req,name=before_del_signupstate,json=beforeDelSignupstate" json:"before_del_signupstate"`
}

func (m *DelSignupInfoResp) Reset()                    { *m = DelSignupInfoResp{} }
func (m *DelSignupInfoResp) String() string            { return proto.CompactTextString(m) }
func (*DelSignupInfoResp) ProtoMessage()               {}
func (*DelSignupInfoResp) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{7} }

func (m *DelSignupInfoResp) GetBeforeDelSignupstate() uint32 {
	if m != nil {
		return m.BeforeDelSignupstate
	}
	return 0
}

// 审核报名(不能对审核通过的用户进行操作)
type AuditSignupInfoReq struct {
	SignupUid  uint32 `protobuf:"varint,1,req,name=signup_uid,json=signupUid" json:"signup_uid"`
	OpUser     string `protobuf:"bytes,2,req,name=op_user,json=opUser" json:"op_user"`
	SignupStat uint32 `protobuf:"varint,3,req,name=signup_stat,json=signupStat" json:"signup_stat"`
	AuditMsg   string `protobuf:"bytes,4,opt,name=audit_msg,json=auditMsg" json:"audit_msg"`
}

func (m *AuditSignupInfoReq) Reset()                    { *m = AuditSignupInfoReq{} }
func (m *AuditSignupInfoReq) String() string            { return proto.CompactTextString(m) }
func (*AuditSignupInfoReq) ProtoMessage()               {}
func (*AuditSignupInfoReq) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{8} }

func (m *AuditSignupInfoReq) GetSignupUid() uint32 {
	if m != nil {
		return m.SignupUid
	}
	return 0
}

func (m *AuditSignupInfoReq) GetOpUser() string {
	if m != nil {
		return m.OpUser
	}
	return ""
}

func (m *AuditSignupInfoReq) GetSignupStat() uint32 {
	if m != nil {
		return m.SignupStat
	}
	return 0
}

func (m *AuditSignupInfoReq) GetAuditMsg() string {
	if m != nil {
		return m.AuditMsg
	}
	return ""
}

// 获取用户的基本信息
type GetUserBaseInfoReq struct {
	OpUid     uint32 `protobuf:"varint,1,req,name=op_uid,json=opUid" json:"op_uid"`
	TargetUid uint32 `protobuf:"varint,2,req,name=target_uid,json=targetUid" json:"target_uid"`
}

func (m *GetUserBaseInfoReq) Reset()                    { *m = GetUserBaseInfoReq{} }
func (m *GetUserBaseInfoReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserBaseInfoReq) ProtoMessage()               {}
func (*GetUserBaseInfoReq) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{9} }

func (m *GetUserBaseInfoReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *GetUserBaseInfoReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type GetUserBaseInfoResp struct {
	BaseInfo *GoddessBaseInfo `protobuf:"bytes,1,req,name=base_info,json=baseInfo" json:"base_info,omitempty"`
}

func (m *GetUserBaseInfoResp) Reset()                    { *m = GetUserBaseInfoResp{} }
func (m *GetUserBaseInfoResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserBaseInfoResp) ProtoMessage()               {}
func (*GetUserBaseInfoResp) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{10} }

func (m *GetUserBaseInfoResp) GetBaseInfo() *GoddessBaseInfo {
	if m != nil {
		return m.BaseInfo
	}
	return nil
}

// 获取用户的公开的报名信息
type GetUserExtendInfoReq struct {
	OpUid     uint32 `protobuf:"varint,1,req,name=op_uid,json=opUid" json:"op_uid"`
	TargetUid uint32 `protobuf:"varint,2,req,name=target_uid,json=targetUid" json:"target_uid"`
}

func (m *GetUserExtendInfoReq) Reset()                    { *m = GetUserExtendInfoReq{} }
func (m *GetUserExtendInfoReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserExtendInfoReq) ProtoMessage()               {}
func (*GetUserExtendInfoReq) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{11} }

func (m *GetUserExtendInfoReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *GetUserExtendInfoReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type GetUserExtendInfoResp struct {
	SignupInfo *GoddessExtendInfo `protobuf:"bytes,1,req,name=signup_info,json=signupInfo" json:"signup_info,omitempty"`
}

func (m *GetUserExtendInfoResp) Reset()                    { *m = GetUserExtendInfoResp{} }
func (m *GetUserExtendInfoResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserExtendInfoResp) ProtoMessage()               {}
func (*GetUserExtendInfoResp) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{12} }

func (m *GetUserExtendInfoResp) GetSignupInfo() *GoddessExtendInfo {
	if m != nil {
		return m.SignupInfo
	}
	return nil
}

// 获取用户的详细信息
type GetUserDetailInfoReq struct {
	OpUid     uint32 `protobuf:"varint,1,req,name=op_uid,json=opUid" json:"op_uid"`
	TargetUid uint32 `protobuf:"varint,2,req,name=target_uid,json=targetUid" json:"target_uid"`
}

func (m *GetUserDetailInfoReq) Reset()                    { *m = GetUserDetailInfoReq{} }
func (m *GetUserDetailInfoReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserDetailInfoReq) ProtoMessage()               {}
func (*GetUserDetailInfoReq) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{13} }

func (m *GetUserDetailInfoReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *GetUserDetailInfoReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type GetUserDetailInfoResp struct {
	DetailInfo *GoddessDetailInfo `protobuf:"bytes,1,req,name=detail_info,json=detailInfo" json:"detail_info,omitempty"`
}

func (m *GetUserDetailInfoResp) Reset()                    { *m = GetUserDetailInfoResp{} }
func (m *GetUserDetailInfoResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserDetailInfoResp) ProtoMessage()               {}
func (*GetUserDetailInfoResp) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{14} }

func (m *GetUserDetailInfoResp) GetDetailInfo() *GoddessDetailInfo {
	if m != nil {
		return m.DetailInfo
	}
	return nil
}

// 女神类型
type GoddessClassifyInfo struct {
	ClassifyId    uint32 `protobuf:"varint,1,req,name=classify_id,json=classifyId" json:"classify_id"`
	ClassifyName  string `protobuf:"bytes,2,req,name=classify_name,json=classifyName" json:"classify_name"`
	IsVaildSignup bool   `protobuf:"varint,3,req,name=is_vaild_signup,json=isVaildSignup" json:"is_vaild_signup"`
	SignupCnt     uint32 `protobuf:"varint,4,opt,name=signup_cnt,json=signupCnt" json:"signup_cnt"`
}

func (m *GoddessClassifyInfo) Reset()                    { *m = GoddessClassifyInfo{} }
func (m *GoddessClassifyInfo) String() string            { return proto.CompactTextString(m) }
func (*GoddessClassifyInfo) ProtoMessage()               {}
func (*GoddessClassifyInfo) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{15} }

func (m *GoddessClassifyInfo) GetClassifyId() uint32 {
	if m != nil {
		return m.ClassifyId
	}
	return 0
}

func (m *GoddessClassifyInfo) GetClassifyName() string {
	if m != nil {
		return m.ClassifyName
	}
	return ""
}

func (m *GoddessClassifyInfo) GetIsVaildSignup() bool {
	if m != nil {
		return m.IsVaildSignup
	}
	return false
}

func (m *GoddessClassifyInfo) GetSignupCnt() uint32 {
	if m != nil {
		return m.SignupCnt
	}
	return 0
}

type GetAllGoddessClassifyInfoReq struct {
	IsNeedSignupCnt bool `protobuf:"varint,1,req,name=is_need_signup_cnt,json=isNeedSignupCnt" json:"is_need_signup_cnt"`
}

func (m *GetAllGoddessClassifyInfoReq) Reset()         { *m = GetAllGoddessClassifyInfoReq{} }
func (m *GetAllGoddessClassifyInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetAllGoddessClassifyInfoReq) ProtoMessage()    {}
func (*GetAllGoddessClassifyInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGoddess, []int{16}
}

func (m *GetAllGoddessClassifyInfoReq) GetIsNeedSignupCnt() bool {
	if m != nil {
		return m.IsNeedSignupCnt
	}
	return false
}

type GetAllGoddessClassifyInfoResp struct {
	ClassifyList []*GoddessClassifyInfo `protobuf:"bytes,1,rep,name=classify_list,json=classifyList" json:"classify_list,omitempty"`
	AllSignupCnt uint32                 `protobuf:"varint,2,opt,name=all_signup_cnt,json=allSignupCnt" json:"all_signup_cnt"`
}

func (m *GetAllGoddessClassifyInfoResp) Reset()         { *m = GetAllGoddessClassifyInfoResp{} }
func (m *GetAllGoddessClassifyInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetAllGoddessClassifyInfoResp) ProtoMessage()    {}
func (*GetAllGoddessClassifyInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGoddess, []int{17}
}

func (m *GetAllGoddessClassifyInfoResp) GetClassifyList() []*GoddessClassifyInfo {
	if m != nil {
		return m.ClassifyList
	}
	return nil
}

func (m *GetAllGoddessClassifyInfoResp) GetAllSignupCnt() uint32 {
	if m != nil {
		return m.AllSignupCnt
	}
	return 0
}

// 根据报名状态 获取报名列表(用于后台审核)
type GetUserDetailListBySignupStatusReq struct {
	SignupStatus     uint32 `protobuf:"varint,1,req,name=signup_status,json=signupStatus" json:"signup_status"`
	IsNeedSignupInfo bool   `protobuf:"varint,2,req,name=is_need_signup_info,json=isNeedSignupInfo" json:"is_need_signup_info"`
}

func (m *GetUserDetailListBySignupStatusReq) Reset()         { *m = GetUserDetailListBySignupStatusReq{} }
func (m *GetUserDetailListBySignupStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetUserDetailListBySignupStatusReq) ProtoMessage()    {}
func (*GetUserDetailListBySignupStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGoddess, []int{18}
}

func (m *GetUserDetailListBySignupStatusReq) GetSignupStatus() uint32 {
	if m != nil {
		return m.SignupStatus
	}
	return 0
}

func (m *GetUserDetailListBySignupStatusReq) GetIsNeedSignupInfo() bool {
	if m != nil {
		return m.IsNeedSignupInfo
	}
	return false
}

type GetUserDetailListBySignupStatusResp struct {
	SignupStatus uint32               `protobuf:"varint,1,req,name=signup_status,json=signupStatus" json:"signup_status"`
	DetailList   []*GoddessDetailInfo `protobuf:"bytes,2,rep,name=detail_list,json=detailList" json:"detail_list,omitempty"`
}

func (m *GetUserDetailListBySignupStatusResp) Reset()         { *m = GetUserDetailListBySignupStatusResp{} }
func (m *GetUserDetailListBySignupStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetUserDetailListBySignupStatusResp) ProtoMessage()    {}
func (*GetUserDetailListBySignupStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGoddess, []int{19}
}

func (m *GetUserDetailListBySignupStatusResp) GetSignupStatus() uint32 {
	if m != nil {
		return m.SignupStatus
	}
	return 0
}

func (m *GetUserDetailListBySignupStatusResp) GetDetailList() []*GoddessDetailInfo {
	if m != nil {
		return m.DetailList
	}
	return nil
}

// 根据女神类型 获取报名列表(用于动态展示)
type GetUserBaseListByGoddessClassifyReq struct {
	GoddessClassifyId uint32 `protobuf:"varint,1,req,name=goddess_classify_id,json=goddessClassifyId" json:"goddess_classify_id"`
}

func (m *GetUserBaseListByGoddessClassifyReq) Reset()         { *m = GetUserBaseListByGoddessClassifyReq{} }
func (m *GetUserBaseListByGoddessClassifyReq) String() string { return proto.CompactTextString(m) }
func (*GetUserBaseListByGoddessClassifyReq) ProtoMessage()    {}
func (*GetUserBaseListByGoddessClassifyReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGoddess, []int{20}
}

func (m *GetUserBaseListByGoddessClassifyReq) GetGoddessClassifyId() uint32 {
	if m != nil {
		return m.GoddessClassifyId
	}
	return 0
}

type GetUserBaseListByGoddessClassifyResp struct {
	GoddessClassifyId uint32             `protobuf:"varint,1,req,name=goddess_classify_id,json=goddessClassifyId" json:"goddess_classify_id"`
	BaseList          []*GoddessBaseInfo `protobuf:"bytes,2,rep,name=base_list,json=baseList" json:"base_list,omitempty"`
}

func (m *GetUserBaseListByGoddessClassifyResp) Reset()         { *m = GetUserBaseListByGoddessClassifyResp{} }
func (m *GetUserBaseListByGoddessClassifyResp) String() string { return proto.CompactTextString(m) }
func (*GetUserBaseListByGoddessClassifyResp) ProtoMessage()    {}
func (*GetUserBaseListByGoddessClassifyResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGoddess, []int{21}
}

func (m *GetUserBaseListByGoddessClassifyResp) GetGoddessClassifyId() uint32 {
	if m != nil {
		return m.GoddessClassifyId
	}
	return 0
}

func (m *GetUserBaseListByGoddessClassifyResp) GetBaseList() []*GoddessBaseInfo {
	if m != nil {
		return m.BaseList
	}
	return nil
}

// 根据UID批量获取女神详情
type BatGetGoddessDetailListReq struct {
	UidList         []uint32 `protobuf:"varint,1,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
	IsNeedAuditinfo bool     `protobuf:"varint,2,opt,name=is_need_auditinfo,json=isNeedAuditinfo" json:"is_need_auditinfo"`
}

func (m *BatGetGoddessDetailListReq) Reset()         { *m = BatGetGoddessDetailListReq{} }
func (m *BatGetGoddessDetailListReq) String() string { return proto.CompactTextString(m) }
func (*BatGetGoddessDetailListReq) ProtoMessage()    {}
func (*BatGetGoddessDetailListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGoddess, []int{22}
}

func (m *BatGetGoddessDetailListReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatGetGoddessDetailListReq) GetIsNeedAuditinfo() bool {
	if m != nil {
		return m.IsNeedAuditinfo
	}
	return false
}

type BatGetGoddessDetailListResp struct {
	DetailList []*GoddessDetailInfo `protobuf:"bytes,2,rep,name=detail_list,json=detailList" json:"detail_list,omitempty"`
}

func (m *BatGetGoddessDetailListResp) Reset()         { *m = BatGetGoddessDetailListResp{} }
func (m *BatGetGoddessDetailListResp) String() string { return proto.CompactTextString(m) }
func (*BatGetGoddessDetailListResp) ProtoMessage()    {}
func (*BatGetGoddessDetailListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGoddess, []int{23}
}

func (m *BatGetGoddessDetailListResp) GetDetailList() []*GoddessDetailInfo {
	if m != nil {
		return m.DetailList
	}
	return nil
}

// 修改女神的公会ID 只有在女神报名时没有填写公会信息时有效
type ModifyGoddessSignupGuildInfoReq struct {
	GoddessUid uint32 `protobuf:"varint,1,req,name=goddess_uid,json=goddessUid" json:"goddess_uid"`
	Guildid    uint32 `protobuf:"varint,2,req,name=guildid" json:"guildid"`
}

func (m *ModifyGoddessSignupGuildInfoReq) Reset()         { *m = ModifyGoddessSignupGuildInfoReq{} }
func (m *ModifyGoddessSignupGuildInfoReq) String() string { return proto.CompactTextString(m) }
func (*ModifyGoddessSignupGuildInfoReq) ProtoMessage()    {}
func (*ModifyGoddessSignupGuildInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGoddess, []int{24}
}

func (m *ModifyGoddessSignupGuildInfoReq) GetGoddessUid() uint32 {
	if m != nil {
		return m.GoddessUid
	}
	return 0
}

func (m *ModifyGoddessSignupGuildInfoReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

// 女神动态, 实际上就是游戏圈的数据
type GoddessTrend struct {
	TopicId         uint32           `protobuf:"varint,1,req,name=topic_id,json=topicId" json:"topic_id"`
	Title           string           `protobuf:"bytes,2,req,name=title" json:"title"`
	Content         string           `protobuf:"bytes,3,req,name=content" json:"content"`
	Creator         uint32           `protobuf:"varint,4,req,name=creator" json:"creator"`
	CreateTime      uint32           `protobuf:"varint,5,req,name=create_time,json=createTime" json:"create_time"`
	LikeCount       uint32           `protobuf:"varint,6,req,name=like_count,json=likeCount" json:"like_count"`
	CommentCount    uint32           `protobuf:"varint,7,req,name=comment_count,json=commentCount" json:"comment_count"`
	ImageList       []string         `protobuf:"bytes,8,rep,name=image_list,json=imageList" json:"image_list,omitempty"`
	Tag             uint32           `protobuf:"varint,9,req,name=tag" json:"tag"`
	LastCommentTime uint32           `protobuf:"varint,10,req,name=last_comment_time,json=lastCommentTime" json:"last_comment_time"`
	GoddessBaseInfo *GoddessBaseInfo `protobuf:"bytes,11,opt,name=goddess_base_info,json=goddessBaseInfo" json:"goddess_base_info,omitempty"`
	TeaseCount      uint32           `protobuf:"varint,12,opt,name=tease_count,json=teaseCount" json:"tease_count"`
}

func (m *GoddessTrend) Reset()                    { *m = GoddessTrend{} }
func (m *GoddessTrend) String() string            { return proto.CompactTextString(m) }
func (*GoddessTrend) ProtoMessage()               {}
func (*GoddessTrend) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{25} }

func (m *GoddessTrend) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *GoddessTrend) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GoddessTrend) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *GoddessTrend) GetCreator() uint32 {
	if m != nil {
		return m.Creator
	}
	return 0
}

func (m *GoddessTrend) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *GoddessTrend) GetLikeCount() uint32 {
	if m != nil {
		return m.LikeCount
	}
	return 0
}

func (m *GoddessTrend) GetCommentCount() uint32 {
	if m != nil {
		return m.CommentCount
	}
	return 0
}

func (m *GoddessTrend) GetImageList() []string {
	if m != nil {
		return m.ImageList
	}
	return nil
}

func (m *GoddessTrend) GetTag() uint32 {
	if m != nil {
		return m.Tag
	}
	return 0
}

func (m *GoddessTrend) GetLastCommentTime() uint32 {
	if m != nil {
		return m.LastCommentTime
	}
	return 0
}

func (m *GoddessTrend) GetGoddessBaseInfo() *GoddessBaseInfo {
	if m != nil {
		return m.GoddessBaseInfo
	}
	return nil
}

func (m *GoddessTrend) GetTeaseCount() uint32 {
	if m != nil {
		return m.TeaseCount
	}
	return 0
}

// 根据女神分类获取动态列表
type GetGoddessTrendsByGoddessClassifyReq struct {
	ClassifyId   uint32 `protobuf:"varint,1,opt,name=classify_id,json=classifyId" json:"classify_id"`
	StartTopicId uint32 `protobuf:"varint,2,req,name=start_topic_id,json=startTopicId" json:"start_topic_id"`
	TrendsCount  uint32 `protobuf:"varint,3,req,name=trends_count,json=trendsCount" json:"trends_count"`
}

func (m *GetGoddessTrendsByGoddessClassifyReq) Reset()         { *m = GetGoddessTrendsByGoddessClassifyReq{} }
func (m *GetGoddessTrendsByGoddessClassifyReq) String() string { return proto.CompactTextString(m) }
func (*GetGoddessTrendsByGoddessClassifyReq) ProtoMessage()    {}
func (*GetGoddessTrendsByGoddessClassifyReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGoddess, []int{26}
}

func (m *GetGoddessTrendsByGoddessClassifyReq) GetClassifyId() uint32 {
	if m != nil {
		return m.ClassifyId
	}
	return 0
}

func (m *GetGoddessTrendsByGoddessClassifyReq) GetStartTopicId() uint32 {
	if m != nil {
		return m.StartTopicId
	}
	return 0
}

func (m *GetGoddessTrendsByGoddessClassifyReq) GetTrendsCount() uint32 {
	if m != nil {
		return m.TrendsCount
	}
	return 0
}

type GetGoddessTrendsByGoddessUidReq struct {
	GoddessUidList []uint32 `protobuf:"varint,1,rep,name=goddess_uid_list,json=goddessUidList" json:"goddess_uid_list,omitempty"`
	StartTopicId   uint32   `protobuf:"varint,2,req,name=start_topic_id,json=startTopicId" json:"start_topic_id"`
	TrendsCount    uint32   `protobuf:"varint,3,req,name=trends_count,json=trendsCount" json:"trends_count"`
}

func (m *GetGoddessTrendsByGoddessUidReq) Reset()         { *m = GetGoddessTrendsByGoddessUidReq{} }
func (m *GetGoddessTrendsByGoddessUidReq) String() string { return proto.CompactTextString(m) }
func (*GetGoddessTrendsByGoddessUidReq) ProtoMessage()    {}
func (*GetGoddessTrendsByGoddessUidReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGoddess, []int{27}
}

func (m *GetGoddessTrendsByGoddessUidReq) GetGoddessUidList() []uint32 {
	if m != nil {
		return m.GoddessUidList
	}
	return nil
}

func (m *GetGoddessTrendsByGoddessUidReq) GetStartTopicId() uint32 {
	if m != nil {
		return m.StartTopicId
	}
	return 0
}

func (m *GetGoddessTrendsByGoddessUidReq) GetTrendsCount() uint32 {
	if m != nil {
		return m.TrendsCount
	}
	return 0
}

type GoddessTrendsResp struct {
	TrendList   []*GoddessTrend `protobuf:"bytes,1,rep,name=trend_list,json=trendList" json:"trend_list,omitempty"`
	TrendsTotal uint32          `protobuf:"varint,2,req,name=trends_total,json=trendsTotal" json:"trends_total"`
	HasMore     bool            `protobuf:"varint,3,req,name=has_more,json=hasMore" json:"has_more"`
}

func (m *GoddessTrendsResp) Reset()                    { *m = GoddessTrendsResp{} }
func (m *GoddessTrendsResp) String() string            { return proto.CompactTextString(m) }
func (*GoddessTrendsResp) ProtoMessage()               {}
func (*GoddessTrendsResp) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{28} }

func (m *GoddessTrendsResp) GetTrendList() []*GoddessTrend {
	if m != nil {
		return m.TrendList
	}
	return nil
}

func (m *GoddessTrendsResp) GetTrendsTotal() uint32 {
	if m != nil {
		return m.TrendsTotal
	}
	return 0
}

func (m *GoddessTrendsResp) GetHasMore() bool {
	if m != nil {
		return m.HasMore
	}
	return false
}

// 撩妹 对帖子撩一下
type TeaseGoddessReq struct {
	OpUid      uint32 `protobuf:"varint,1,req,name=op_uid,json=opUid" json:"op_uid"`
	GoddessUid uint32 `protobuf:"varint,2,req,name=goddess_uid,json=goddessUid" json:"goddess_uid"`
	TopicId    uint32 `protobuf:"varint,3,opt,name=topic_id,json=topicId" json:"topic_id"`
}

func (m *TeaseGoddessReq) Reset()                    { *m = TeaseGoddessReq{} }
func (m *TeaseGoddessReq) String() string            { return proto.CompactTextString(m) }
func (*TeaseGoddessReq) ProtoMessage()               {}
func (*TeaseGoddessReq) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{29} }

func (m *TeaseGoddessReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *TeaseGoddessReq) GetGoddessUid() uint32 {
	if m != nil {
		return m.GoddessUid
	}
	return 0
}

func (m *TeaseGoddessReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

type TeaseGoddessResp struct {
}

func (m *TeaseGoddessResp) Reset()                    { *m = TeaseGoddessResp{} }
func (m *TeaseGoddessResp) String() string            { return proto.CompactTextString(m) }
func (*TeaseGoddessResp) ProtoMessage()               {}
func (*TeaseGoddessResp) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{30} }

type BrushVoteCheckOpt struct {
	IpInfo     string `protobuf:"bytes,1,opt,name=ip_info,json=ipInfo" json:"ip_info"`
	DeviceInfo string `protobuf:"bytes,2,opt,name=device_info,json=deviceInfo" json:"device_info"`
	UserLevel  uint32 `protobuf:"varint,3,opt,name=user_level,json=userLevel" json:"user_level"`
}

func (m *BrushVoteCheckOpt) Reset()                    { *m = BrushVoteCheckOpt{} }
func (m *BrushVoteCheckOpt) String() string            { return proto.CompactTextString(m) }
func (*BrushVoteCheckOpt) ProtoMessage()               {}
func (*BrushVoteCheckOpt) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{31} }

func (m *BrushVoteCheckOpt) GetIpInfo() string {
	if m != nil {
		return m.IpInfo
	}
	return ""
}

func (m *BrushVoteCheckOpt) GetDeviceInfo() string {
	if m != nil {
		return m.DeviceInfo
	}
	return ""
}

func (m *BrushVoteCheckOpt) GetUserLevel() uint32 {
	if m != nil {
		return m.UserLevel
	}
	return 0
}

type GoddessVote struct {
	Uid        uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Votes      uint32 `protobuf:"varint,2,req,name=votes" json:"votes"`
	ExtraVotes int32  `protobuf:"varint,3,req,name=extra_votes,json=extraVotes" json:"extra_votes"`
}

func (m *GoddessVote) Reset()                    { *m = GoddessVote{} }
func (m *GoddessVote) String() string            { return proto.CompactTextString(m) }
func (*GoddessVote) ProtoMessage()               {}
func (*GoddessVote) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{32} }

func (m *GoddessVote) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GoddessVote) GetVotes() uint32 {
	if m != nil {
		return m.Votes
	}
	return 0
}

func (m *GoddessVote) GetExtraVotes() int32 {
	if m != nil {
		return m.ExtraVotes
	}
	return 0
}

type VoteGoddessReq struct {
	Uid            uint32             `protobuf:"varint,1,req,name=uid" json:"uid"`
	GoddessUid     uint32             `protobuf:"varint,2,req,name=goddess_uid,json=goddessUid" json:"goddess_uid"`
	Votes          uint32             `protobuf:"varint,3,req,name=votes" json:"votes"`
	PageSourceType uint32             `protobuf:"varint,4,opt,name=page_source_type,json=pageSourceType" json:"page_source_type"`
	CheckOpt       *BrushVoteCheckOpt `protobuf:"bytes,5,opt,name=check_opt,json=checkOpt" json:"check_opt,omitempty"`
}

func (m *VoteGoddessReq) Reset()                    { *m = VoteGoddessReq{} }
func (m *VoteGoddessReq) String() string            { return proto.CompactTextString(m) }
func (*VoteGoddessReq) ProtoMessage()               {}
func (*VoteGoddessReq) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{33} }

func (m *VoteGoddessReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *VoteGoddessReq) GetGoddessUid() uint32 {
	if m != nil {
		return m.GoddessUid
	}
	return 0
}

func (m *VoteGoddessReq) GetVotes() uint32 {
	if m != nil {
		return m.Votes
	}
	return 0
}

func (m *VoteGoddessReq) GetPageSourceType() uint32 {
	if m != nil {
		return m.PageSourceType
	}
	return 0
}

func (m *VoteGoddessReq) GetCheckOpt() *BrushVoteCheckOpt {
	if m != nil {
		return m.CheckOpt
	}
	return nil
}

type VoteGoddessResp struct {
	RemainVoteChances uint32 `protobuf:"varint,1,req,name=remain_vote_chances,json=remainVoteChances" json:"remain_vote_chances"`
}

func (m *VoteGoddessResp) Reset()                    { *m = VoteGoddessResp{} }
func (m *VoteGoddessResp) String() string            { return proto.CompactTextString(m) }
func (*VoteGoddessResp) ProtoMessage()               {}
func (*VoteGoddessResp) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{34} }

func (m *VoteGoddessResp) GetRemainVoteChances() uint32 {
	if m != nil {
		return m.RemainVoteChances
	}
	return 0
}

type GetGoddessRankingReq struct {
	RankingType uint32 `protobuf:"varint,1,req,name=ranking_type,json=rankingType" json:"ranking_type"`
	Uid         uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
	ClassifyId  uint32 `protobuf:"varint,3,req,name=classify_id,json=classifyId" json:"classify_id"`
	StartIndex  uint32 `protobuf:"varint,4,opt,name=start_index,json=startIndex" json:"start_index"`
	Limit       uint32 `protobuf:"varint,5,opt,name=limit" json:"limit"`
}

func (m *GetGoddessRankingReq) Reset()                    { *m = GetGoddessRankingReq{} }
func (m *GetGoddessRankingReq) String() string            { return proto.CompactTextString(m) }
func (*GetGoddessRankingReq) ProtoMessage()               {}
func (*GetGoddessRankingReq) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{35} }

func (m *GetGoddessRankingReq) GetRankingType() uint32 {
	if m != nil {
		return m.RankingType
	}
	return 0
}

func (m *GetGoddessRankingReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetGoddessRankingReq) GetClassifyId() uint32 {
	if m != nil {
		return m.ClassifyId
	}
	return 0
}

func (m *GetGoddessRankingReq) GetStartIndex() uint32 {
	if m != nil {
		return m.StartIndex
	}
	return 0
}

func (m *GetGoddessRankingReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetGoddessRankingResp struct {
	GoddessVoteList []*GoddessVote `protobuf:"bytes,1,rep,name=goddess_vote_list,json=goddessVoteList" json:"goddess_vote_list,omitempty"`
}

func (m *GetGoddessRankingResp) Reset()                    { *m = GetGoddessRankingResp{} }
func (m *GetGoddessRankingResp) String() string            { return proto.CompactTextString(m) }
func (*GetGoddessRankingResp) ProtoMessage()               {}
func (*GetGoddessRankingResp) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{36} }

func (m *GetGoddessRankingResp) GetGoddessVoteList() []*GoddessVote {
	if m != nil {
		return m.GoddessVoteList
	}
	return nil
}

type GetGoddessFinalRankingReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetGoddessFinalRankingReq) Reset()         { *m = GetGoddessFinalRankingReq{} }
func (m *GetGoddessFinalRankingReq) String() string { return proto.CompactTextString(m) }
func (*GetGoddessFinalRankingReq) ProtoMessage()    {}
func (*GetGoddessFinalRankingReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGoddess, []int{37}
}

func (m *GetGoddessFinalRankingReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetGoddessRankInRankingReq struct {
	GoddessUid  uint32 `protobuf:"varint,1,req,name=goddess_uid,json=goddessUid" json:"goddess_uid"`
	RankingType uint32 `protobuf:"varint,2,req,name=ranking_type,json=rankingType" json:"ranking_type"`
}

func (m *GetGoddessRankInRankingReq) Reset()         { *m = GetGoddessRankInRankingReq{} }
func (m *GetGoddessRankInRankingReq) String() string { return proto.CompactTextString(m) }
func (*GetGoddessRankInRankingReq) ProtoMessage()    {}
func (*GetGoddessRankInRankingReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGoddess, []int{38}
}

func (m *GetGoddessRankInRankingReq) GetGoddessUid() uint32 {
	if m != nil {
		return m.GoddessUid
	}
	return 0
}

func (m *GetGoddessRankInRankingReq) GetRankingType() uint32 {
	if m != nil {
		return m.RankingType
	}
	return 0
}

type GetGoddessRankInRankingResp struct {
	Rank uint32 `protobuf:"varint,2,req,name=rank" json:"rank"`
}

func (m *GetGoddessRankInRankingResp) Reset()         { *m = GetGoddessRankInRankingResp{} }
func (m *GetGoddessRankInRankingResp) String() string { return proto.CompactTextString(m) }
func (*GetGoddessRankInRankingResp) ProtoMessage()    {}
func (*GetGoddessRankInRankingResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGoddess, []int{39}
}

func (m *GetGoddessRankInRankingResp) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

type GetGoddessVotesReq struct {
	GoddessUidList []uint32 `protobuf:"varint,1,rep,name=goddess_uid_list,json=goddessUidList" json:"goddess_uid_list,omitempty"`
}

func (m *GetGoddessVotesReq) Reset()                    { *m = GetGoddessVotesReq{} }
func (m *GetGoddessVotesReq) String() string            { return proto.CompactTextString(m) }
func (*GetGoddessVotesReq) ProtoMessage()               {}
func (*GetGoddessVotesReq) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{40} }

func (m *GetGoddessVotesReq) GetGoddessUidList() []uint32 {
	if m != nil {
		return m.GoddessUidList
	}
	return nil
}

type GetGoddessVotesResp struct {
	GoddessVoteList []*GoddessVote `protobuf:"bytes,1,rep,name=goddess_vote_list,json=goddessVoteList" json:"goddess_vote_list,omitempty"`
}

func (m *GetGoddessVotesResp) Reset()                    { *m = GetGoddessVotesResp{} }
func (m *GetGoddessVotesResp) String() string            { return proto.CompactTextString(m) }
func (*GetGoddessVotesResp) ProtoMessage()               {}
func (*GetGoddessVotesResp) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{41} }

func (m *GetGoddessVotesResp) GetGoddessVoteList() []*GoddessVote {
	if m != nil {
		return m.GoddessVoteList
	}
	return nil
}

type GetUserVoteChancesReq struct {
	Uid      uint32             `protobuf:"varint,1,req,name=uid" json:"uid"`
	CheckOpt *BrushVoteCheckOpt `protobuf:"bytes,2,opt,name=check_opt,json=checkOpt" json:"check_opt,omitempty"`
}

func (m *GetUserVoteChancesReq) Reset()                    { *m = GetUserVoteChancesReq{} }
func (m *GetUserVoteChancesReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserVoteChancesReq) ProtoMessage()               {}
func (*GetUserVoteChancesReq) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{42} }

func (m *GetUserVoteChancesReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserVoteChancesReq) GetCheckOpt() *BrushVoteCheckOpt {
	if m != nil {
		return m.CheckOpt
	}
	return nil
}

type GetUserVoteChancesResp struct {
	Chances uint32 `protobuf:"varint,1,req,name=chances" json:"chances"`
}

func (m *GetUserVoteChancesResp) Reset()                    { *m = GetUserVoteChancesResp{} }
func (m *GetUserVoteChancesResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserVoteChancesResp) ProtoMessage()               {}
func (*GetUserVoteChancesResp) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{43} }

func (m *GetUserVoteChancesResp) GetChances() uint32 {
	if m != nil {
		return m.Chances
	}
	return 0
}

type GetUserToGoddessVotesReq struct {
	Uid        uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GoddessUid uint32 `protobuf:"varint,2,req,name=goddess_uid,json=goddessUid" json:"goddess_uid"`
}

func (m *GetUserToGoddessVotesReq) Reset()                    { *m = GetUserToGoddessVotesReq{} }
func (m *GetUserToGoddessVotesReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserToGoddessVotesReq) ProtoMessage()               {}
func (*GetUserToGoddessVotesReq) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{44} }

func (m *GetUserToGoddessVotesReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserToGoddessVotesReq) GetGoddessUid() uint32 {
	if m != nil {
		return m.GoddessUid
	}
	return 0
}

type GetUserToGoddessVotesResp struct {
	Votes uint32 `protobuf:"varint,1,req,name=votes" json:"votes"`
}

func (m *GetUserToGoddessVotesResp) Reset()         { *m = GetUserToGoddessVotesResp{} }
func (m *GetUserToGoddessVotesResp) String() string { return proto.CompactTextString(m) }
func (*GetUserToGoddessVotesResp) ProtoMessage()    {}
func (*GetUserToGoddessVotesResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGoddess, []int{45}
}

func (m *GetUserToGoddessVotesResp) GetVotes() uint32 {
	if m != nil {
		return m.Votes
	}
	return 0
}

// 官方后台加票器
type AddExtraVotesReq struct {
	OpUser     string `protobuf:"bytes,1,req,name=op_user,json=opUser" json:"op_user"`
	GoddessUid uint32 `protobuf:"varint,2,req,name=goddess_uid,json=goddessUid" json:"goddess_uid"`
	ExtraVotes int32  `protobuf:"varint,3,req,name=extra_votes,json=extraVotes" json:"extra_votes"`
}

func (m *AddExtraVotesReq) Reset()                    { *m = AddExtraVotesReq{} }
func (m *AddExtraVotesReq) String() string            { return proto.CompactTextString(m) }
func (*AddExtraVotesReq) ProtoMessage()               {}
func (*AddExtraVotesReq) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{46} }

func (m *AddExtraVotesReq) GetOpUser() string {
	if m != nil {
		return m.OpUser
	}
	return ""
}

func (m *AddExtraVotesReq) GetGoddessUid() uint32 {
	if m != nil {
		return m.GoddessUid
	}
	return 0
}

func (m *AddExtraVotesReq) GetExtraVotes() int32 {
	if m != nil {
		return m.ExtraVotes
	}
	return 0
}

type AddExtraVotesResp struct {
	CurrentVote *GoddessVote `protobuf:"bytes,1,req,name=current_vote,json=currentVote" json:"current_vote,omitempty"`
}

func (m *AddExtraVotesResp) Reset()                    { *m = AddExtraVotesResp{} }
func (m *AddExtraVotesResp) String() string            { return proto.CompactTextString(m) }
func (*AddExtraVotesResp) ProtoMessage()               {}
func (*AddExtraVotesResp) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{47} }

func (m *AddExtraVotesResp) GetCurrentVote() *GoddessVote {
	if m != nil {
		return m.CurrentVote
	}
	return nil
}

// 非TT第三方(联运SDK,微信,QQ等) 投票接口
type ThirdPartyVoteGoddessReq struct {
	ThirdPartyType uint32 `protobuf:"varint,1,req,name=third_party_type,json=thirdPartyType" json:"third_party_type"`
	Identifier     string `protobuf:"bytes,2,req,name=identifier" json:"identifier"`
	GoddessUid     uint32 `protobuf:"varint,3,req,name=goddess_uid,json=goddessUid" json:"goddess_uid"`
	VoteCnt        uint32 `protobuf:"varint,4,req,name=vote_cnt,json=voteCnt" json:"vote_cnt"`
	PageSourceType uint32 `protobuf:"varint,5,opt,name=page_source_type,json=pageSourceType" json:"page_source_type"`
}

func (m *ThirdPartyVoteGoddessReq) Reset()                    { *m = ThirdPartyVoteGoddessReq{} }
func (m *ThirdPartyVoteGoddessReq) String() string            { return proto.CompactTextString(m) }
func (*ThirdPartyVoteGoddessReq) ProtoMessage()               {}
func (*ThirdPartyVoteGoddessReq) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{48} }

func (m *ThirdPartyVoteGoddessReq) GetThirdPartyType() uint32 {
	if m != nil {
		return m.ThirdPartyType
	}
	return 0
}

func (m *ThirdPartyVoteGoddessReq) GetIdentifier() string {
	if m != nil {
		return m.Identifier
	}
	return ""
}

func (m *ThirdPartyVoteGoddessReq) GetGoddessUid() uint32 {
	if m != nil {
		return m.GoddessUid
	}
	return 0
}

func (m *ThirdPartyVoteGoddessReq) GetVoteCnt() uint32 {
	if m != nil {
		return m.VoteCnt
	}
	return 0
}

func (m *ThirdPartyVoteGoddessReq) GetPageSourceType() uint32 {
	if m != nil {
		return m.PageSourceType
	}
	return 0
}

type ThirdPartyVoteGoddessResp struct {
	IsVote bool `protobuf:"varint,1,req,name=is_vote,json=isVote" json:"is_vote"`
}

func (m *ThirdPartyVoteGoddessResp) Reset()         { *m = ThirdPartyVoteGoddessResp{} }
func (m *ThirdPartyVoteGoddessResp) String() string { return proto.CompactTextString(m) }
func (*ThirdPartyVoteGoddessResp) ProtoMessage()    {}
func (*ThirdPartyVoteGoddessResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGoddess, []int{49}
}

func (m *ThirdPartyVoteGoddessResp) GetIsVote() bool {
	if m != nil {
		return m.IsVote
	}
	return false
}

type ThirdPartyCheckVoteChancesReq struct {
	ThirdPartyType uint32 `protobuf:"varint,1,req,name=third_party_type,json=thirdPartyType" json:"third_party_type"`
	Identifier     string `protobuf:"bytes,2,req,name=identifier" json:"identifier"`
}

func (m *ThirdPartyCheckVoteChancesReq) Reset()         { *m = ThirdPartyCheckVoteChancesReq{} }
func (m *ThirdPartyCheckVoteChancesReq) String() string { return proto.CompactTextString(m) }
func (*ThirdPartyCheckVoteChancesReq) ProtoMessage()    {}
func (*ThirdPartyCheckVoteChancesReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGoddess, []int{50}
}

func (m *ThirdPartyCheckVoteChancesReq) GetThirdPartyType() uint32 {
	if m != nil {
		return m.ThirdPartyType
	}
	return 0
}

func (m *ThirdPartyCheckVoteChancesReq) GetIdentifier() string {
	if m != nil {
		return m.Identifier
	}
	return ""
}

type ThirdPartyCheckVoteChancesResp struct {
	RemainChances uint32 `protobuf:"varint,1,req,name=remain_chances,json=remainChances" json:"remain_chances"`
}

func (m *ThirdPartyCheckVoteChancesResp) Reset()         { *m = ThirdPartyCheckVoteChancesResp{} }
func (m *ThirdPartyCheckVoteChancesResp) String() string { return proto.CompactTextString(m) }
func (*ThirdPartyCheckVoteChancesResp) ProtoMessage()    {}
func (*ThirdPartyCheckVoteChancesResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGoddess, []int{51}
}

func (m *ThirdPartyCheckVoteChancesResp) GetRemainChances() uint32 {
	if m != nil {
		return m.RemainChances
	}
	return 0
}

//
type VoteID struct {
	IsInTt     bool   `protobuf:"varint,1,req,name=is_in_tt,json=isInTt" json:"is_in_tt"`
	Identifier string `protobuf:"bytes,2,req,name=identifier" json:"identifier"`
	GoddessUid uint32 `protobuf:"varint,3,req,name=goddess_uid,json=goddessUid" json:"goddess_uid"`
	Timestamp  uint32 `protobuf:"varint,4,req,name=timestamp" json:"timestamp"`
	Salt       uint32 `protobuf:"varint,10,req,name=salt" json:"salt"`
}

func (m *VoteID) Reset()                    { *m = VoteID{} }
func (m *VoteID) String() string            { return proto.CompactTextString(m) }
func (*VoteID) ProtoMessage()               {}
func (*VoteID) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{52} }

func (m *VoteID) GetIsInTt() bool {
	if m != nil {
		return m.IsInTt
	}
	return false
}

func (m *VoteID) GetIdentifier() string {
	if m != nil {
		return m.Identifier
	}
	return ""
}

func (m *VoteID) GetGoddessUid() uint32 {
	if m != nil {
		return m.GoddessUid
	}
	return 0
}

func (m *VoteID) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *VoteID) GetSalt() uint32 {
	if m != nil {
		return m.Salt
	}
	return 0
}

type ShareID struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Timestamp uint32 `protobuf:"varint,2,req,name=timestamp" json:"timestamp"`
	Salt      uint32 `protobuf:"varint,10,req,name=salt" json:"salt"`
}

func (m *ShareID) Reset()                    { *m = ShareID{} }
func (m *ShareID) String() string            { return proto.CompactTextString(m) }
func (*ShareID) ProtoMessage()               {}
func (*ShareID) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{53} }

func (m *ShareID) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ShareID) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *ShareID) GetSalt() uint32 {
	if m != nil {
		return m.Salt
	}
	return 0
}

type CreateVoteIDReq struct {
	IsInTt         bool     `protobuf:"varint,1,req,name=is_in_tt,json=isInTt" json:"is_in_tt"`
	Identifier     string   `protobuf:"bytes,2,req,name=identifier" json:"identifier"`
	GoddessUidList []uint32 `protobuf:"varint,3,rep,name=goddess_uid_list,json=goddessUidList" json:"goddess_uid_list,omitempty"`
}

func (m *CreateVoteIDReq) Reset()                    { *m = CreateVoteIDReq{} }
func (m *CreateVoteIDReq) String() string            { return proto.CompactTextString(m) }
func (*CreateVoteIDReq) ProtoMessage()               {}
func (*CreateVoteIDReq) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{54} }

func (m *CreateVoteIDReq) GetIsInTt() bool {
	if m != nil {
		return m.IsInTt
	}
	return false
}

func (m *CreateVoteIDReq) GetIdentifier() string {
	if m != nil {
		return m.Identifier
	}
	return ""
}

func (m *CreateVoteIDReq) GetGoddessUidList() []uint32 {
	if m != nil {
		return m.GoddessUidList
	}
	return nil
}

type CreateVoteIDResp struct {
	VoteIdList []string `protobuf:"bytes,1,rep,name=vote_id_list,json=voteIdList" json:"vote_id_list,omitempty"`
}

func (m *CreateVoteIDResp) Reset()                    { *m = CreateVoteIDResp{} }
func (m *CreateVoteIDResp) String() string            { return proto.CompactTextString(m) }
func (*CreateVoteIDResp) ProtoMessage()               {}
func (*CreateVoteIDResp) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{55} }

func (m *CreateVoteIDResp) GetVoteIdList() []string {
	if m != nil {
		return m.VoteIdList
	}
	return nil
}

type CreateShareIDReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Timestamp uint32 `protobuf:"varint,2,req,name=timestamp" json:"timestamp"`
}

func (m *CreateShareIDReq) Reset()                    { *m = CreateShareIDReq{} }
func (m *CreateShareIDReq) String() string            { return proto.CompactTextString(m) }
func (*CreateShareIDReq) ProtoMessage()               {}
func (*CreateShareIDReq) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{56} }

func (m *CreateShareIDReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CreateShareIDReq) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

type CreateShareIDResp struct {
	ShareId string `protobuf:"bytes,1,req,name=share_id,json=shareId" json:"share_id"`
}

func (m *CreateShareIDResp) Reset()                    { *m = CreateShareIDResp{} }
func (m *CreateShareIDResp) String() string            { return proto.CompactTextString(m) }
func (*CreateShareIDResp) ProtoMessage()               {}
func (*CreateShareIDResp) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{57} }

func (m *CreateShareIDResp) GetShareId() string {
	if m != nil {
		return m.ShareId
	}
	return ""
}

type GetCurrentStageReq struct {
}

func (m *GetCurrentStageReq) Reset()                    { *m = GetCurrentStageReq{} }
func (m *GetCurrentStageReq) String() string            { return proto.CompactTextString(m) }
func (*GetCurrentStageReq) ProtoMessage()               {}
func (*GetCurrentStageReq) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{58} }

type GetCurrentStageResp struct {
	CurrentStage uint32 `protobuf:"varint,1,req,name=current_stage,json=currentStage" json:"current_stage"`
}

func (m *GetCurrentStageResp) Reset()                    { *m = GetCurrentStageResp{} }
func (m *GetCurrentStageResp) String() string            { return proto.CompactTextString(m) }
func (*GetCurrentStageResp) ProtoMessage()               {}
func (*GetCurrentStageResp) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{59} }

func (m *GetCurrentStageResp) GetCurrentStage() uint32 {
	if m != nil {
		return m.CurrentStage
	}
	return 0
}

type NotifyRewardUserVoteChancesReq struct {
	TargetUid   uint32             `protobuf:"varint,1,req,name=target_uid,json=targetUid" json:"target_uid"`
	OriTs       uint32             `protobuf:"varint,2,req,name=ori_ts,json=oriTs" json:"ori_ts"`
	Type        uint32             `protobuf:"varint,3,req,name=type" json:"type"`
	TriggerInfo string             `protobuf:"bytes,4,opt,name=trigger_info,json=triggerInfo" json:"trigger_info"`
	RewardNum   uint32             `protobuf:"varint,5,opt,name=reward_num,json=rewardNum" json:"reward_num"`
	CheckOpt    *BrushVoteCheckOpt `protobuf:"bytes,6,opt,name=check_opt,json=checkOpt" json:"check_opt,omitempty"`
}

func (m *NotifyRewardUserVoteChancesReq) Reset()         { *m = NotifyRewardUserVoteChancesReq{} }
func (m *NotifyRewardUserVoteChancesReq) String() string { return proto.CompactTextString(m) }
func (*NotifyRewardUserVoteChancesReq) ProtoMessage()    {}
func (*NotifyRewardUserVoteChancesReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGoddess, []int{60}
}

func (m *NotifyRewardUserVoteChancesReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *NotifyRewardUserVoteChancesReq) GetOriTs() uint32 {
	if m != nil {
		return m.OriTs
	}
	return 0
}

func (m *NotifyRewardUserVoteChancesReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *NotifyRewardUserVoteChancesReq) GetTriggerInfo() string {
	if m != nil {
		return m.TriggerInfo
	}
	return ""
}

func (m *NotifyRewardUserVoteChancesReq) GetRewardNum() uint32 {
	if m != nil {
		return m.RewardNum
	}
	return 0
}

func (m *NotifyRewardUserVoteChancesReq) GetCheckOpt() *BrushVoteCheckOpt {
	if m != nil {
		return m.CheckOpt
	}
	return nil
}

type NotifyRewardUserVoteChancesResp struct {
	IsReward bool `protobuf:"varint,1,req,name=is_reward,json=isReward" json:"is_reward"`
}

func (m *NotifyRewardUserVoteChancesResp) Reset()         { *m = NotifyRewardUserVoteChancesResp{} }
func (m *NotifyRewardUserVoteChancesResp) String() string { return proto.CompactTextString(m) }
func (*NotifyRewardUserVoteChancesResp) ProtoMessage()    {}
func (*NotifyRewardUserVoteChancesResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGoddess, []int{61}
}

func (m *NotifyRewardUserVoteChancesResp) GetIsReward() bool {
	if m != nil {
		return m.IsReward
	}
	return false
}

// //////////////////////////////////////////////////////////////
// massage for statistic
// //////////////////////////////////////////////////////////////
type VoteStatisticsAsyncData struct {
	TargetGoddess uint32 `protobuf:"varint,1,req,name=target_goddess,json=targetGoddess" json:"target_goddess"`
	OriUid        uint32 `protobuf:"varint,2,req,name=ori_uid,json=oriUid" json:"ori_uid"`
	SourceType    uint32 `protobuf:"varint,3,req,name=source_type,json=sourceType" json:"source_type"`
	ExUserInfo    string `protobuf:"bytes,4,opt,name=ex_user_info,json=exUserInfo" json:"ex_user_info"`
	VoteCnt       uint32 `protobuf:"varint,5,opt,name=vote_cnt,json=voteCnt" json:"vote_cnt"`
}

func (m *VoteStatisticsAsyncData) Reset()                    { *m = VoteStatisticsAsyncData{} }
func (m *VoteStatisticsAsyncData) String() string            { return proto.CompactTextString(m) }
func (*VoteStatisticsAsyncData) ProtoMessage()               {}
func (*VoteStatisticsAsyncData) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{62} }

func (m *VoteStatisticsAsyncData) GetTargetGoddess() uint32 {
	if m != nil {
		return m.TargetGoddess
	}
	return 0
}

func (m *VoteStatisticsAsyncData) GetOriUid() uint32 {
	if m != nil {
		return m.OriUid
	}
	return 0
}

func (m *VoteStatisticsAsyncData) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

func (m *VoteStatisticsAsyncData) GetExUserInfo() string {
	if m != nil {
		return m.ExUserInfo
	}
	return ""
}

func (m *VoteStatisticsAsyncData) GetVoteCnt() uint32 {
	if m != nil {
		return m.VoteCnt
	}
	return 0
}

type RewardStatisticsAsyncData struct {
	TargetUid  uint32 `protobuf:"varint,1,req,name=target_uid,json=targetUid" json:"target_uid"`
	RewardType uint32 `protobuf:"varint,2,req,name=reward_type,json=rewardType" json:"reward_type"`
	RewardNum  uint32 `protobuf:"varint,3,req,name=reward_num,json=rewardNum" json:"reward_num"`
}

func (m *RewardStatisticsAsyncData) Reset()         { *m = RewardStatisticsAsyncData{} }
func (m *RewardStatisticsAsyncData) String() string { return proto.CompactTextString(m) }
func (*RewardStatisticsAsyncData) ProtoMessage()    {}
func (*RewardStatisticsAsyncData) Descriptor() ([]byte, []int) {
	return fileDescriptorGoddess, []int{63}
}

func (m *RewardStatisticsAsyncData) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *RewardStatisticsAsyncData) GetRewardType() uint32 {
	if m != nil {
		return m.RewardType
	}
	return 0
}

func (m *RewardStatisticsAsyncData) GetRewardNum() uint32 {
	if m != nil {
		return m.RewardNum
	}
	return 0
}

type TeaseStatisticsAsyncData struct {
	OpUid     uint32 `protobuf:"varint,1,req,name=op_uid,json=opUid" json:"op_uid"`
	GodessUid uint32 `protobuf:"varint,2,req,name=godess_uid,json=godessUid" json:"godess_uid"`
	TopicId   uint32 `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
}

func (m *TeaseStatisticsAsyncData) Reset()                    { *m = TeaseStatisticsAsyncData{} }
func (m *TeaseStatisticsAsyncData) String() string            { return proto.CompactTextString(m) }
func (*TeaseStatisticsAsyncData) ProtoMessage()               {}
func (*TeaseStatisticsAsyncData) Descriptor() ([]byte, []int) { return fileDescriptorGoddess, []int{64} }

func (m *TeaseStatisticsAsyncData) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *TeaseStatisticsAsyncData) GetGodessUid() uint32 {
	if m != nil {
		return m.GodessUid
	}
	return 0
}

func (m *TeaseStatisticsAsyncData) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func init() {
	proto.RegisterType((*GoddessBaseInfo)(nil), "goddess.GoddessBaseInfo")
	proto.RegisterType((*GoddessExtendInfo)(nil), "goddess.GoddessExtendInfo")
	proto.RegisterType((*GoddessSignupAuditInfo)(nil), "goddess.GoddessSignupAuditInfo")
	proto.RegisterType((*GoddessDetailInfo)(nil), "goddess.GoddessDetailInfo")
	proto.RegisterType((*SignupReq)(nil), "goddess.SignupReq")
	proto.RegisterType((*ModifySignupInfoReq)(nil), "goddess.ModifySignupInfoReq")
	proto.RegisterType((*DelSignupInfoReq)(nil), "goddess.DelSignupInfoReq")
	proto.RegisterType((*DelSignupInfoResp)(nil), "goddess.DelSignupInfoResp")
	proto.RegisterType((*AuditSignupInfoReq)(nil), "goddess.AuditSignupInfoReq")
	proto.RegisterType((*GetUserBaseInfoReq)(nil), "goddess.GetUserBaseInfoReq")
	proto.RegisterType((*GetUserBaseInfoResp)(nil), "goddess.GetUserBaseInfoResp")
	proto.RegisterType((*GetUserExtendInfoReq)(nil), "goddess.GetUserExtendInfoReq")
	proto.RegisterType((*GetUserExtendInfoResp)(nil), "goddess.GetUserExtendInfoResp")
	proto.RegisterType((*GetUserDetailInfoReq)(nil), "goddess.GetUserDetailInfoReq")
	proto.RegisterType((*GetUserDetailInfoResp)(nil), "goddess.GetUserDetailInfoResp")
	proto.RegisterType((*GoddessClassifyInfo)(nil), "goddess.GoddessClassifyInfo")
	proto.RegisterType((*GetAllGoddessClassifyInfoReq)(nil), "goddess.GetAllGoddessClassifyInfoReq")
	proto.RegisterType((*GetAllGoddessClassifyInfoResp)(nil), "goddess.GetAllGoddessClassifyInfoResp")
	proto.RegisterType((*GetUserDetailListBySignupStatusReq)(nil), "goddess.GetUserDetailListBySignupStatusReq")
	proto.RegisterType((*GetUserDetailListBySignupStatusResp)(nil), "goddess.GetUserDetailListBySignupStatusResp")
	proto.RegisterType((*GetUserBaseListByGoddessClassifyReq)(nil), "goddess.GetUserBaseListByGoddessClassifyReq")
	proto.RegisterType((*GetUserBaseListByGoddessClassifyResp)(nil), "goddess.GetUserBaseListByGoddessClassifyResp")
	proto.RegisterType((*BatGetGoddessDetailListReq)(nil), "goddess.BatGetGoddessDetailListReq")
	proto.RegisterType((*BatGetGoddessDetailListResp)(nil), "goddess.BatGetGoddessDetailListResp")
	proto.RegisterType((*ModifyGoddessSignupGuildInfoReq)(nil), "goddess.ModifyGoddessSignupGuildInfoReq")
	proto.RegisterType((*GoddessTrend)(nil), "goddess.GoddessTrend")
	proto.RegisterType((*GetGoddessTrendsByGoddessClassifyReq)(nil), "goddess.GetGoddessTrendsByGoddessClassifyReq")
	proto.RegisterType((*GetGoddessTrendsByGoddessUidReq)(nil), "goddess.GetGoddessTrendsByGoddessUidReq")
	proto.RegisterType((*GoddessTrendsResp)(nil), "goddess.GoddessTrendsResp")
	proto.RegisterType((*TeaseGoddessReq)(nil), "goddess.TeaseGoddessReq")
	proto.RegisterType((*TeaseGoddessResp)(nil), "goddess.TeaseGoddessResp")
	proto.RegisterType((*BrushVoteCheckOpt)(nil), "goddess.BrushVoteCheckOpt")
	proto.RegisterType((*GoddessVote)(nil), "goddess.GoddessVote")
	proto.RegisterType((*VoteGoddessReq)(nil), "goddess.VoteGoddessReq")
	proto.RegisterType((*VoteGoddessResp)(nil), "goddess.VoteGoddessResp")
	proto.RegisterType((*GetGoddessRankingReq)(nil), "goddess.GetGoddessRankingReq")
	proto.RegisterType((*GetGoddessRankingResp)(nil), "goddess.GetGoddessRankingResp")
	proto.RegisterType((*GetGoddessFinalRankingReq)(nil), "goddess.GetGoddessFinalRankingReq")
	proto.RegisterType((*GetGoddessRankInRankingReq)(nil), "goddess.GetGoddessRankInRankingReq")
	proto.RegisterType((*GetGoddessRankInRankingResp)(nil), "goddess.GetGoddessRankInRankingResp")
	proto.RegisterType((*GetGoddessVotesReq)(nil), "goddess.GetGoddessVotesReq")
	proto.RegisterType((*GetGoddessVotesResp)(nil), "goddess.GetGoddessVotesResp")
	proto.RegisterType((*GetUserVoteChancesReq)(nil), "goddess.GetUserVoteChancesReq")
	proto.RegisterType((*GetUserVoteChancesResp)(nil), "goddess.GetUserVoteChancesResp")
	proto.RegisterType((*GetUserToGoddessVotesReq)(nil), "goddess.GetUserToGoddessVotesReq")
	proto.RegisterType((*GetUserToGoddessVotesResp)(nil), "goddess.GetUserToGoddessVotesResp")
	proto.RegisterType((*AddExtraVotesReq)(nil), "goddess.AddExtraVotesReq")
	proto.RegisterType((*AddExtraVotesResp)(nil), "goddess.AddExtraVotesResp")
	proto.RegisterType((*ThirdPartyVoteGoddessReq)(nil), "goddess.ThirdPartyVoteGoddessReq")
	proto.RegisterType((*ThirdPartyVoteGoddessResp)(nil), "goddess.ThirdPartyVoteGoddessResp")
	proto.RegisterType((*ThirdPartyCheckVoteChancesReq)(nil), "goddess.ThirdPartyCheckVoteChancesReq")
	proto.RegisterType((*ThirdPartyCheckVoteChancesResp)(nil), "goddess.ThirdPartyCheckVoteChancesResp")
	proto.RegisterType((*VoteID)(nil), "goddess.VoteID")
	proto.RegisterType((*ShareID)(nil), "goddess.ShareID")
	proto.RegisterType((*CreateVoteIDReq)(nil), "goddess.CreateVoteIDReq")
	proto.RegisterType((*CreateVoteIDResp)(nil), "goddess.CreateVoteIDResp")
	proto.RegisterType((*CreateShareIDReq)(nil), "goddess.CreateShareIDReq")
	proto.RegisterType((*CreateShareIDResp)(nil), "goddess.CreateShareIDResp")
	proto.RegisterType((*GetCurrentStageReq)(nil), "goddess.GetCurrentStageReq")
	proto.RegisterType((*GetCurrentStageResp)(nil), "goddess.GetCurrentStageResp")
	proto.RegisterType((*NotifyRewardUserVoteChancesReq)(nil), "goddess.NotifyRewardUserVoteChancesReq")
	proto.RegisterType((*NotifyRewardUserVoteChancesResp)(nil), "goddess.NotifyRewardUserVoteChancesResp")
	proto.RegisterType((*VoteStatisticsAsyncData)(nil), "goddess.VoteStatisticsAsyncData")
	proto.RegisterType((*RewardStatisticsAsyncData)(nil), "goddess.RewardStatisticsAsyncData")
	proto.RegisterType((*TeaseStatisticsAsyncData)(nil), "goddess.TeaseStatisticsAsyncData")
	proto.RegisterEnum("goddess.SIGNUP_STATUS_TYPE", SIGNUP_STATUS_TYPE_name, SIGNUP_STATUS_TYPE_value)
	proto.RegisterEnum("goddess.RankingType", RankingType_name, RankingType_value)
	proto.RegisterEnum("goddess.Stage", Stage_name, Stage_value)
	proto.RegisterEnum("goddess.RewardType", RewardType_name, RewardType_value)
}
func (m *GoddessBaseInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GoddessBaseInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.ClassifyType))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.SignupStat))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.SignupTs))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.SignupUpdateTs))
	return i, nil
}

func (m *GoddessExtendInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GoddessExtendInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(len(m.IntroInfo)))
	i += copy(dAtA[i:], m.IntroInfo)
	if len(m.PhotoList) > 0 {
		for _, s := range m.PhotoList {
			dAtA[i] = 0x12
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if len(m.GameList) > 0 {
		for _, s := range m.GameList {
			dAtA[i] = 0x1a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x22
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(len(m.Job)))
	i += copy(dAtA[i:], m.Job)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(len(m.ZodiacSign)))
	i += copy(dAtA[i:], m.ZodiacSign)
	dAtA[i] = 0x30
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.Height))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(len(m.Bust)))
	i += copy(dAtA[i:], m.Bust)
	return i, nil
}

func (m *GoddessSignupAuditInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GoddessSignupAuditInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(len(m.QqCode)))
	i += copy(dAtA[i:], m.QqCode)
	dAtA[i] = 0x12
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(len(m.AuditInfo)))
	i += copy(dAtA[i:], m.AuditInfo)
	return i, nil
}

func (m *GoddessDetailInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GoddessDetailInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("base_info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGoddess(dAtA, i, uint64(m.BaseInfo.Size()))
		n1, err := m.BaseInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	if m.ExtendInfo != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintGoddess(dAtA, i, uint64(m.ExtendInfo.Size()))
		n2, err := m.ExtendInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	if m.AuditInfo != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintGoddess(dAtA, i, uint64(m.AuditInfo.Size()))
		n3, err := m.AuditInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	return i, nil
}

func (m *SignupReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SignupReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Info == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGoddess(dAtA, i, uint64(m.Info.Size()))
		n4, err := m.Info.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	return i, nil
}

func (m *ModifySignupInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifySignupInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Info == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGoddess(dAtA, i, uint64(m.Info.Size()))
		n5, err := m.Info.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(len(m.OpUser)))
	i += copy(dAtA[i:], m.OpUser)
	return i, nil
}

func (m *DelSignupInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelSignupInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.SignupUid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(len(m.OpUser)))
	i += copy(dAtA[i:], m.OpUser)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(len(m.OpReason)))
	i += copy(dAtA[i:], m.OpReason)
	return i, nil
}

func (m *DelSignupInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelSignupInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.BeforeDelSignupstate))
	return i, nil
}

func (m *AuditSignupInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AuditSignupInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.SignupUid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(len(m.OpUser)))
	i += copy(dAtA[i:], m.OpUser)
	dAtA[i] = 0x18
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.SignupStat))
	dAtA[i] = 0x22
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(len(m.AuditMsg)))
	i += copy(dAtA[i:], m.AuditMsg)
	return i, nil
}

func (m *GetUserBaseInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserBaseInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.OpUid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.TargetUid))
	return i, nil
}

func (m *GetUserBaseInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserBaseInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("base_info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGoddess(dAtA, i, uint64(m.BaseInfo.Size()))
		n6, err := m.BaseInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	return i, nil
}

func (m *GetUserExtendInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserExtendInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.OpUid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.TargetUid))
	return i, nil
}

func (m *GetUserExtendInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserExtendInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.SignupInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("signup_info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGoddess(dAtA, i, uint64(m.SignupInfo.Size()))
		n7, err := m.SignupInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	return i, nil
}

func (m *GetUserDetailInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserDetailInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.OpUid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.TargetUid))
	return i, nil
}

func (m *GetUserDetailInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserDetailInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.DetailInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("detail_info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGoddess(dAtA, i, uint64(m.DetailInfo.Size()))
		n8, err := m.DetailInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	return i, nil
}

func (m *GoddessClassifyInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GoddessClassifyInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.ClassifyId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(len(m.ClassifyName)))
	i += copy(dAtA[i:], m.ClassifyName)
	dAtA[i] = 0x18
	i++
	if m.IsVaildSignup {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x20
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.SignupCnt))
	return i, nil
}

func (m *GetAllGoddessClassifyInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllGoddessClassifyInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.IsNeedSignupCnt {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetAllGoddessClassifyInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllGoddessClassifyInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ClassifyList) > 0 {
		for _, msg := range m.ClassifyList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGoddess(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.AllSignupCnt))
	return i, nil
}

func (m *GetUserDetailListBySignupStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserDetailListBySignupStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.SignupStatus))
	dAtA[i] = 0x10
	i++
	if m.IsNeedSignupInfo {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetUserDetailListBySignupStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserDetailListBySignupStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.SignupStatus))
	if len(m.DetailList) > 0 {
		for _, msg := range m.DetailList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintGoddess(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetUserBaseListByGoddessClassifyReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserBaseListByGoddessClassifyReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.GoddessClassifyId))
	return i, nil
}

func (m *GetUserBaseListByGoddessClassifyResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserBaseListByGoddessClassifyResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.GoddessClassifyId))
	if len(m.BaseList) > 0 {
		for _, msg := range m.BaseList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintGoddess(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *BatGetGoddessDetailListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatGetGoddessDetailListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintGoddess(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x10
	i++
	if m.IsNeedAuditinfo {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *BatGetGoddessDetailListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatGetGoddessDetailListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.DetailList) > 0 {
		for _, msg := range m.DetailList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintGoddess(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ModifyGoddessSignupGuildInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyGoddessSignupGuildInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.GoddessUid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.Guildid))
	return i, nil
}

func (m *GoddessTrend) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GoddessTrend) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(len(m.Title)))
	i += copy(dAtA[i:], m.Title)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	dAtA[i] = 0x20
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.Creator))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.CreateTime))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.LikeCount))
	dAtA[i] = 0x38
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.CommentCount))
	if len(m.ImageList) > 0 {
		for _, s := range m.ImageList {
			dAtA[i] = 0x42
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x48
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.Tag))
	dAtA[i] = 0x50
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.LastCommentTime))
	if m.GoddessBaseInfo != nil {
		dAtA[i] = 0x5a
		i++
		i = encodeVarintGoddess(dAtA, i, uint64(m.GoddessBaseInfo.Size()))
		n9, err := m.GoddessBaseInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	dAtA[i] = 0x60
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.TeaseCount))
	return i, nil
}

func (m *GetGoddessTrendsByGoddessClassifyReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGoddessTrendsByGoddessClassifyReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.ClassifyId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.StartTopicId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.TrendsCount))
	return i, nil
}

func (m *GetGoddessTrendsByGoddessUidReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGoddessTrendsByGoddessUidReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GoddessUidList) > 0 {
		for _, num := range m.GoddessUidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintGoddess(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.StartTopicId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.TrendsCount))
	return i, nil
}

func (m *GoddessTrendsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GoddessTrendsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.TrendList) > 0 {
		for _, msg := range m.TrendList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGoddess(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.TrendsTotal))
	dAtA[i] = 0x18
	i++
	if m.HasMore {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *TeaseGoddessReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TeaseGoddessReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.OpUid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.GoddessUid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.TopicId))
	return i, nil
}

func (m *TeaseGoddessResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TeaseGoddessResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *BrushVoteCheckOpt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BrushVoteCheckOpt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(len(m.IpInfo)))
	i += copy(dAtA[i:], m.IpInfo)
	dAtA[i] = 0x12
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(len(m.DeviceInfo)))
	i += copy(dAtA[i:], m.DeviceInfo)
	dAtA[i] = 0x18
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.UserLevel))
	return i, nil
}

func (m *GoddessVote) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GoddessVote) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.Votes))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.ExtraVotes))
	return i, nil
}

func (m *VoteGoddessReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VoteGoddessReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.GoddessUid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.Votes))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.PageSourceType))
	if m.CheckOpt != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintGoddess(dAtA, i, uint64(m.CheckOpt.Size()))
		n10, err := m.CheckOpt.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	return i, nil
}

func (m *VoteGoddessResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VoteGoddessResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.RemainVoteChances))
	return i, nil
}

func (m *GetGoddessRankingReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGoddessRankingReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.RankingType))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.ClassifyId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.StartIndex))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.Limit))
	return i, nil
}

func (m *GetGoddessRankingResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGoddessRankingResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GoddessVoteList) > 0 {
		for _, msg := range m.GoddessVoteList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGoddess(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetGoddessFinalRankingReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGoddessFinalRankingReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetGoddessRankInRankingReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGoddessRankInRankingReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.GoddessUid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.RankingType))
	return i, nil
}

func (m *GetGoddessRankInRankingResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGoddessRankInRankingResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x10
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.Rank))
	return i, nil
}

func (m *GetGoddessVotesReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGoddessVotesReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GoddessUidList) > 0 {
		for _, num := range m.GoddessUidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintGoddess(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GetGoddessVotesResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGoddessVotesResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GoddessVoteList) > 0 {
		for _, msg := range m.GoddessVoteList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGoddess(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetUserVoteChancesReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserVoteChancesReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.Uid))
	if m.CheckOpt != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintGoddess(dAtA, i, uint64(m.CheckOpt.Size()))
		n11, err := m.CheckOpt.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	return i, nil
}

func (m *GetUserVoteChancesResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserVoteChancesResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.Chances))
	return i, nil
}

func (m *GetUserToGoddessVotesReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserToGoddessVotesReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.GoddessUid))
	return i, nil
}

func (m *GetUserToGoddessVotesResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserToGoddessVotesResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.Votes))
	return i, nil
}

func (m *AddExtraVotesReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddExtraVotesReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(len(m.OpUser)))
	i += copy(dAtA[i:], m.OpUser)
	dAtA[i] = 0x10
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.GoddessUid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.ExtraVotes))
	return i, nil
}

func (m *AddExtraVotesResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddExtraVotesResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.CurrentVote == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("current_vote")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGoddess(dAtA, i, uint64(m.CurrentVote.Size()))
		n12, err := m.CurrentVote.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n12
	}
	return i, nil
}

func (m *ThirdPartyVoteGoddessReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ThirdPartyVoteGoddessReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.ThirdPartyType))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(len(m.Identifier)))
	i += copy(dAtA[i:], m.Identifier)
	dAtA[i] = 0x18
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.GoddessUid))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.VoteCnt))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.PageSourceType))
	return i, nil
}

func (m *ThirdPartyVoteGoddessResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ThirdPartyVoteGoddessResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.IsVote {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *ThirdPartyCheckVoteChancesReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ThirdPartyCheckVoteChancesReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.ThirdPartyType))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(len(m.Identifier)))
	i += copy(dAtA[i:], m.Identifier)
	return i, nil
}

func (m *ThirdPartyCheckVoteChancesResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ThirdPartyCheckVoteChancesResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.RemainChances))
	return i, nil
}

func (m *VoteID) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VoteID) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.IsInTt {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x12
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(len(m.Identifier)))
	i += copy(dAtA[i:], m.Identifier)
	dAtA[i] = 0x18
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.GoddessUid))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.Timestamp))
	dAtA[i] = 0x50
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.Salt))
	return i, nil
}

func (m *ShareID) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ShareID) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.Timestamp))
	dAtA[i] = 0x50
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.Salt))
	return i, nil
}

func (m *CreateVoteIDReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateVoteIDReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.IsInTt {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x12
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(len(m.Identifier)))
	i += copy(dAtA[i:], m.Identifier)
	if len(m.GoddessUidList) > 0 {
		for _, num := range m.GoddessUidList {
			dAtA[i] = 0x18
			i++
			i = encodeVarintGoddess(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *CreateVoteIDResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateVoteIDResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.VoteIdList) > 0 {
		for _, s := range m.VoteIdList {
			dAtA[i] = 0xa
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *CreateShareIDReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateShareIDReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.Timestamp))
	return i, nil
}

func (m *CreateShareIDResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateShareIDResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(len(m.ShareId)))
	i += copy(dAtA[i:], m.ShareId)
	return i, nil
}

func (m *GetCurrentStageReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCurrentStageReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetCurrentStageResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCurrentStageResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.CurrentStage))
	return i, nil
}

func (m *NotifyRewardUserVoteChancesReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NotifyRewardUserVoteChancesReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.TargetUid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.OriTs))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x22
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(len(m.TriggerInfo)))
	i += copy(dAtA[i:], m.TriggerInfo)
	dAtA[i] = 0x28
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.RewardNum))
	if m.CheckOpt != nil {
		dAtA[i] = 0x32
		i++
		i = encodeVarintGoddess(dAtA, i, uint64(m.CheckOpt.Size()))
		n13, err := m.CheckOpt.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n13
	}
	return i, nil
}

func (m *NotifyRewardUserVoteChancesResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NotifyRewardUserVoteChancesResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.IsReward {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *VoteStatisticsAsyncData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VoteStatisticsAsyncData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.TargetGoddess))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.OriUid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.SourceType))
	dAtA[i] = 0x22
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(len(m.ExUserInfo)))
	i += copy(dAtA[i:], m.ExUserInfo)
	dAtA[i] = 0x28
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.VoteCnt))
	return i, nil
}

func (m *RewardStatisticsAsyncData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RewardStatisticsAsyncData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.TargetUid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.RewardType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.RewardNum))
	return i, nil
}

func (m *TeaseStatisticsAsyncData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TeaseStatisticsAsyncData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.OpUid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.GodessUid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGoddess(dAtA, i, uint64(m.TopicId))
	return i, nil
}

func encodeFixed64Goddess(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Goddess(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintGoddess(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *GoddessBaseInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.Uid))
	n += 1 + sovGoddess(uint64(m.GuildId))
	n += 1 + sovGoddess(uint64(m.ClassifyType))
	n += 1 + sovGoddess(uint64(m.SignupStat))
	n += 1 + sovGoddess(uint64(m.SignupTs))
	n += 1 + sovGoddess(uint64(m.SignupUpdateTs))
	return n
}

func (m *GoddessExtendInfo) Size() (n int) {
	var l int
	_ = l
	l = len(m.IntroInfo)
	n += 1 + l + sovGoddess(uint64(l))
	if len(m.PhotoList) > 0 {
		for _, s := range m.PhotoList {
			l = len(s)
			n += 1 + l + sovGoddess(uint64(l))
		}
	}
	if len(m.GameList) > 0 {
		for _, s := range m.GameList {
			l = len(s)
			n += 1 + l + sovGoddess(uint64(l))
		}
	}
	l = len(m.Job)
	n += 1 + l + sovGoddess(uint64(l))
	l = len(m.ZodiacSign)
	n += 1 + l + sovGoddess(uint64(l))
	n += 1 + sovGoddess(uint64(m.Height))
	l = len(m.Bust)
	n += 1 + l + sovGoddess(uint64(l))
	return n
}

func (m *GoddessSignupAuditInfo) Size() (n int) {
	var l int
	_ = l
	l = len(m.QqCode)
	n += 1 + l + sovGoddess(uint64(l))
	l = len(m.Phone)
	n += 1 + l + sovGoddess(uint64(l))
	l = len(m.AuditInfo)
	n += 1 + l + sovGoddess(uint64(l))
	return n
}

func (m *GoddessDetailInfo) Size() (n int) {
	var l int
	_ = l
	if m.BaseInfo != nil {
		l = m.BaseInfo.Size()
		n += 1 + l + sovGoddess(uint64(l))
	}
	if m.ExtendInfo != nil {
		l = m.ExtendInfo.Size()
		n += 1 + l + sovGoddess(uint64(l))
	}
	if m.AuditInfo != nil {
		l = m.AuditInfo.Size()
		n += 1 + l + sovGoddess(uint64(l))
	}
	return n
}

func (m *SignupReq) Size() (n int) {
	var l int
	_ = l
	if m.Info != nil {
		l = m.Info.Size()
		n += 1 + l + sovGoddess(uint64(l))
	}
	return n
}

func (m *ModifySignupInfoReq) Size() (n int) {
	var l int
	_ = l
	if m.Info != nil {
		l = m.Info.Size()
		n += 1 + l + sovGoddess(uint64(l))
	}
	l = len(m.OpUser)
	n += 1 + l + sovGoddess(uint64(l))
	return n
}

func (m *DelSignupInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.SignupUid))
	l = len(m.OpUser)
	n += 1 + l + sovGoddess(uint64(l))
	l = len(m.OpReason)
	n += 1 + l + sovGoddess(uint64(l))
	return n
}

func (m *DelSignupInfoResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.BeforeDelSignupstate))
	return n
}

func (m *AuditSignupInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.SignupUid))
	l = len(m.OpUser)
	n += 1 + l + sovGoddess(uint64(l))
	n += 1 + sovGoddess(uint64(m.SignupStat))
	l = len(m.AuditMsg)
	n += 1 + l + sovGoddess(uint64(l))
	return n
}

func (m *GetUserBaseInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.OpUid))
	n += 1 + sovGoddess(uint64(m.TargetUid))
	return n
}

func (m *GetUserBaseInfoResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseInfo != nil {
		l = m.BaseInfo.Size()
		n += 1 + l + sovGoddess(uint64(l))
	}
	return n
}

func (m *GetUserExtendInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.OpUid))
	n += 1 + sovGoddess(uint64(m.TargetUid))
	return n
}

func (m *GetUserExtendInfoResp) Size() (n int) {
	var l int
	_ = l
	if m.SignupInfo != nil {
		l = m.SignupInfo.Size()
		n += 1 + l + sovGoddess(uint64(l))
	}
	return n
}

func (m *GetUserDetailInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.OpUid))
	n += 1 + sovGoddess(uint64(m.TargetUid))
	return n
}

func (m *GetUserDetailInfoResp) Size() (n int) {
	var l int
	_ = l
	if m.DetailInfo != nil {
		l = m.DetailInfo.Size()
		n += 1 + l + sovGoddess(uint64(l))
	}
	return n
}

func (m *GoddessClassifyInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.ClassifyId))
	l = len(m.ClassifyName)
	n += 1 + l + sovGoddess(uint64(l))
	n += 2
	n += 1 + sovGoddess(uint64(m.SignupCnt))
	return n
}

func (m *GetAllGoddessClassifyInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 2
	return n
}

func (m *GetAllGoddessClassifyInfoResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ClassifyList) > 0 {
		for _, e := range m.ClassifyList {
			l = e.Size()
			n += 1 + l + sovGoddess(uint64(l))
		}
	}
	n += 1 + sovGoddess(uint64(m.AllSignupCnt))
	return n
}

func (m *GetUserDetailListBySignupStatusReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.SignupStatus))
	n += 2
	return n
}

func (m *GetUserDetailListBySignupStatusResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.SignupStatus))
	if len(m.DetailList) > 0 {
		for _, e := range m.DetailList {
			l = e.Size()
			n += 1 + l + sovGoddess(uint64(l))
		}
	}
	return n
}

func (m *GetUserBaseListByGoddessClassifyReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.GoddessClassifyId))
	return n
}

func (m *GetUserBaseListByGoddessClassifyResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.GoddessClassifyId))
	if len(m.BaseList) > 0 {
		for _, e := range m.BaseList {
			l = e.Size()
			n += 1 + l + sovGoddess(uint64(l))
		}
	}
	return n
}

func (m *BatGetGoddessDetailListReq) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovGoddess(uint64(e))
		}
	}
	n += 2
	return n
}

func (m *BatGetGoddessDetailListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.DetailList) > 0 {
		for _, e := range m.DetailList {
			l = e.Size()
			n += 1 + l + sovGoddess(uint64(l))
		}
	}
	return n
}

func (m *ModifyGoddessSignupGuildInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.GoddessUid))
	n += 1 + sovGoddess(uint64(m.Guildid))
	return n
}

func (m *GoddessTrend) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.TopicId))
	l = len(m.Title)
	n += 1 + l + sovGoddess(uint64(l))
	l = len(m.Content)
	n += 1 + l + sovGoddess(uint64(l))
	n += 1 + sovGoddess(uint64(m.Creator))
	n += 1 + sovGoddess(uint64(m.CreateTime))
	n += 1 + sovGoddess(uint64(m.LikeCount))
	n += 1 + sovGoddess(uint64(m.CommentCount))
	if len(m.ImageList) > 0 {
		for _, s := range m.ImageList {
			l = len(s)
			n += 1 + l + sovGoddess(uint64(l))
		}
	}
	n += 1 + sovGoddess(uint64(m.Tag))
	n += 1 + sovGoddess(uint64(m.LastCommentTime))
	if m.GoddessBaseInfo != nil {
		l = m.GoddessBaseInfo.Size()
		n += 1 + l + sovGoddess(uint64(l))
	}
	n += 1 + sovGoddess(uint64(m.TeaseCount))
	return n
}

func (m *GetGoddessTrendsByGoddessClassifyReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.ClassifyId))
	n += 1 + sovGoddess(uint64(m.StartTopicId))
	n += 1 + sovGoddess(uint64(m.TrendsCount))
	return n
}

func (m *GetGoddessTrendsByGoddessUidReq) Size() (n int) {
	var l int
	_ = l
	if len(m.GoddessUidList) > 0 {
		for _, e := range m.GoddessUidList {
			n += 1 + sovGoddess(uint64(e))
		}
	}
	n += 1 + sovGoddess(uint64(m.StartTopicId))
	n += 1 + sovGoddess(uint64(m.TrendsCount))
	return n
}

func (m *GoddessTrendsResp) Size() (n int) {
	var l int
	_ = l
	if len(m.TrendList) > 0 {
		for _, e := range m.TrendList {
			l = e.Size()
			n += 1 + l + sovGoddess(uint64(l))
		}
	}
	n += 1 + sovGoddess(uint64(m.TrendsTotal))
	n += 2
	return n
}

func (m *TeaseGoddessReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.OpUid))
	n += 1 + sovGoddess(uint64(m.GoddessUid))
	n += 1 + sovGoddess(uint64(m.TopicId))
	return n
}

func (m *TeaseGoddessResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *BrushVoteCheckOpt) Size() (n int) {
	var l int
	_ = l
	l = len(m.IpInfo)
	n += 1 + l + sovGoddess(uint64(l))
	l = len(m.DeviceInfo)
	n += 1 + l + sovGoddess(uint64(l))
	n += 1 + sovGoddess(uint64(m.UserLevel))
	return n
}

func (m *GoddessVote) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.Uid))
	n += 1 + sovGoddess(uint64(m.Votes))
	n += 1 + sovGoddess(uint64(m.ExtraVotes))
	return n
}

func (m *VoteGoddessReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.Uid))
	n += 1 + sovGoddess(uint64(m.GoddessUid))
	n += 1 + sovGoddess(uint64(m.Votes))
	n += 1 + sovGoddess(uint64(m.PageSourceType))
	if m.CheckOpt != nil {
		l = m.CheckOpt.Size()
		n += 1 + l + sovGoddess(uint64(l))
	}
	return n
}

func (m *VoteGoddessResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.RemainVoteChances))
	return n
}

func (m *GetGoddessRankingReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.RankingType))
	n += 1 + sovGoddess(uint64(m.Uid))
	n += 1 + sovGoddess(uint64(m.ClassifyId))
	n += 1 + sovGoddess(uint64(m.StartIndex))
	n += 1 + sovGoddess(uint64(m.Limit))
	return n
}

func (m *GetGoddessRankingResp) Size() (n int) {
	var l int
	_ = l
	if len(m.GoddessVoteList) > 0 {
		for _, e := range m.GoddessVoteList {
			l = e.Size()
			n += 1 + l + sovGoddess(uint64(l))
		}
	}
	return n
}

func (m *GetGoddessFinalRankingReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.Uid))
	return n
}

func (m *GetGoddessRankInRankingReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.GoddessUid))
	n += 1 + sovGoddess(uint64(m.RankingType))
	return n
}

func (m *GetGoddessRankInRankingResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.Rank))
	return n
}

func (m *GetGoddessVotesReq) Size() (n int) {
	var l int
	_ = l
	if len(m.GoddessUidList) > 0 {
		for _, e := range m.GoddessUidList {
			n += 1 + sovGoddess(uint64(e))
		}
	}
	return n
}

func (m *GetGoddessVotesResp) Size() (n int) {
	var l int
	_ = l
	if len(m.GoddessVoteList) > 0 {
		for _, e := range m.GoddessVoteList {
			l = e.Size()
			n += 1 + l + sovGoddess(uint64(l))
		}
	}
	return n
}

func (m *GetUserVoteChancesReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.Uid))
	if m.CheckOpt != nil {
		l = m.CheckOpt.Size()
		n += 1 + l + sovGoddess(uint64(l))
	}
	return n
}

func (m *GetUserVoteChancesResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.Chances))
	return n
}

func (m *GetUserToGoddessVotesReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.Uid))
	n += 1 + sovGoddess(uint64(m.GoddessUid))
	return n
}

func (m *GetUserToGoddessVotesResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.Votes))
	return n
}

func (m *AddExtraVotesReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.OpUser)
	n += 1 + l + sovGoddess(uint64(l))
	n += 1 + sovGoddess(uint64(m.GoddessUid))
	n += 1 + sovGoddess(uint64(m.ExtraVotes))
	return n
}

func (m *AddExtraVotesResp) Size() (n int) {
	var l int
	_ = l
	if m.CurrentVote != nil {
		l = m.CurrentVote.Size()
		n += 1 + l + sovGoddess(uint64(l))
	}
	return n
}

func (m *ThirdPartyVoteGoddessReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.ThirdPartyType))
	l = len(m.Identifier)
	n += 1 + l + sovGoddess(uint64(l))
	n += 1 + sovGoddess(uint64(m.GoddessUid))
	n += 1 + sovGoddess(uint64(m.VoteCnt))
	n += 1 + sovGoddess(uint64(m.PageSourceType))
	return n
}

func (m *ThirdPartyVoteGoddessResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	return n
}

func (m *ThirdPartyCheckVoteChancesReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.ThirdPartyType))
	l = len(m.Identifier)
	n += 1 + l + sovGoddess(uint64(l))
	return n
}

func (m *ThirdPartyCheckVoteChancesResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.RemainChances))
	return n
}

func (m *VoteID) Size() (n int) {
	var l int
	_ = l
	n += 2
	l = len(m.Identifier)
	n += 1 + l + sovGoddess(uint64(l))
	n += 1 + sovGoddess(uint64(m.GoddessUid))
	n += 1 + sovGoddess(uint64(m.Timestamp))
	n += 1 + sovGoddess(uint64(m.Salt))
	return n
}

func (m *ShareID) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.Uid))
	n += 1 + sovGoddess(uint64(m.Timestamp))
	n += 1 + sovGoddess(uint64(m.Salt))
	return n
}

func (m *CreateVoteIDReq) Size() (n int) {
	var l int
	_ = l
	n += 2
	l = len(m.Identifier)
	n += 1 + l + sovGoddess(uint64(l))
	if len(m.GoddessUidList) > 0 {
		for _, e := range m.GoddessUidList {
			n += 1 + sovGoddess(uint64(e))
		}
	}
	return n
}

func (m *CreateVoteIDResp) Size() (n int) {
	var l int
	_ = l
	if len(m.VoteIdList) > 0 {
		for _, s := range m.VoteIdList {
			l = len(s)
			n += 1 + l + sovGoddess(uint64(l))
		}
	}
	return n
}

func (m *CreateShareIDReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.Uid))
	n += 1 + sovGoddess(uint64(m.Timestamp))
	return n
}

func (m *CreateShareIDResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.ShareId)
	n += 1 + l + sovGoddess(uint64(l))
	return n
}

func (m *GetCurrentStageReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetCurrentStageResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.CurrentStage))
	return n
}

func (m *NotifyRewardUserVoteChancesReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.TargetUid))
	n += 1 + sovGoddess(uint64(m.OriTs))
	n += 1 + sovGoddess(uint64(m.Type))
	l = len(m.TriggerInfo)
	n += 1 + l + sovGoddess(uint64(l))
	n += 1 + sovGoddess(uint64(m.RewardNum))
	if m.CheckOpt != nil {
		l = m.CheckOpt.Size()
		n += 1 + l + sovGoddess(uint64(l))
	}
	return n
}

func (m *NotifyRewardUserVoteChancesResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	return n
}

func (m *VoteStatisticsAsyncData) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.TargetGoddess))
	n += 1 + sovGoddess(uint64(m.OriUid))
	n += 1 + sovGoddess(uint64(m.SourceType))
	l = len(m.ExUserInfo)
	n += 1 + l + sovGoddess(uint64(l))
	n += 1 + sovGoddess(uint64(m.VoteCnt))
	return n
}

func (m *RewardStatisticsAsyncData) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.TargetUid))
	n += 1 + sovGoddess(uint64(m.RewardType))
	n += 1 + sovGoddess(uint64(m.RewardNum))
	return n
}

func (m *TeaseStatisticsAsyncData) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGoddess(uint64(m.OpUid))
	n += 1 + sovGoddess(uint64(m.GodessUid))
	n += 1 + sovGoddess(uint64(m.TopicId))
	return n
}

func sovGoddess(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozGoddess(x uint64) (n int) {
	return sovGoddess(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *GoddessBaseInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GoddessBaseInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GoddessBaseInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClassifyType", wireType)
			}
			m.ClassifyType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClassifyType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SignupStat", wireType)
			}
			m.SignupStat = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SignupStat |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SignupTs", wireType)
			}
			m.SignupTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SignupTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SignupUpdateTs", wireType)
			}
			m.SignupUpdateTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SignupUpdateTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GoddessExtendInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GoddessExtendInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GoddessExtendInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IntroInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IntroInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PhotoList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PhotoList = append(m.PhotoList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameList = append(m.GameList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Job", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Job = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ZodiacSign", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ZodiacSign = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Height", wireType)
			}
			m.Height = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Height |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Bust", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Bust = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GoddessSignupAuditInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GoddessSignupAuditInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GoddessSignupAuditInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field QqCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.QqCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AuditInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AuditInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GoddessDetailInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GoddessDetailInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GoddessDetailInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseInfo == nil {
				m.BaseInfo = &GoddessBaseInfo{}
			}
			if err := m.BaseInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtendInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ExtendInfo == nil {
				m.ExtendInfo = &GoddessExtendInfo{}
			}
			if err := m.ExtendInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AuditInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.AuditInfo == nil {
				m.AuditInfo = &GoddessSignupAuditInfo{}
			}
			if err := m.AuditInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SignupReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SignupReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SignupReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Info", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Info == nil {
				m.Info = &GoddessDetailInfo{}
			}
			if err := m.Info.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifySignupInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifySignupInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifySignupInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Info", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Info == nil {
				m.Info = &GoddessDetailInfo{}
			}
			if err := m.Info.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUser", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OpUser = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("info")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_user")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelSignupInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelSignupInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelSignupInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SignupUid", wireType)
			}
			m.SignupUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SignupUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUser", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OpUser = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpReason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OpReason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("signup_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_user")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_reason")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelSignupInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelSignupInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelSignupInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeforeDelSignupstate", wireType)
			}
			m.BeforeDelSignupstate = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeforeDelSignupstate |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("before_del_signupstate")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AuditSignupInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AuditSignupInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AuditSignupInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SignupUid", wireType)
			}
			m.SignupUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SignupUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUser", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OpUser = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SignupStat", wireType)
			}
			m.SignupStat = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SignupStat |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AuditMsg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AuditMsg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("signup_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_user")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("signup_stat")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserBaseInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserBaseInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserBaseInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("target_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserBaseInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserBaseInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserBaseInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseInfo == nil {
				m.BaseInfo = &GoddessBaseInfo{}
			}
			if err := m.BaseInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserExtendInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserExtendInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserExtendInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("target_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserExtendInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserExtendInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserExtendInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SignupInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.SignupInfo == nil {
				m.SignupInfo = &GoddessExtendInfo{}
			}
			if err := m.SignupInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("signup_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserDetailInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserDetailInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserDetailInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("target_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserDetailInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserDetailInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserDetailInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DetailInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.DetailInfo == nil {
				m.DetailInfo = &GoddessDetailInfo{}
			}
			if err := m.DetailInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("detail_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GoddessClassifyInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GoddessClassifyInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GoddessClassifyInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClassifyId", wireType)
			}
			m.ClassifyId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClassifyId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClassifyName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ClassifyName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsVaildSignup", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsVaildSignup = bool(v != 0)
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SignupCnt", wireType)
			}
			m.SignupCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SignupCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("classify_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("classify_name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_vaild_signup")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllGoddessClassifyInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllGoddessClassifyInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllGoddessClassifyInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsNeedSignupCnt", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsNeedSignupCnt = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_need_signup_cnt")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllGoddessClassifyInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllGoddessClassifyInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllGoddessClassifyInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClassifyList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ClassifyList = append(m.ClassifyList, &GoddessClassifyInfo{})
			if err := m.ClassifyList[len(m.ClassifyList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AllSignupCnt", wireType)
			}
			m.AllSignupCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AllSignupCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserDetailListBySignupStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserDetailListBySignupStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserDetailListBySignupStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SignupStatus", wireType)
			}
			m.SignupStatus = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SignupStatus |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsNeedSignupInfo", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsNeedSignupInfo = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("signup_status")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_need_signup_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserDetailListBySignupStatusResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserDetailListBySignupStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserDetailListBySignupStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SignupStatus", wireType)
			}
			m.SignupStatus = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SignupStatus |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DetailList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DetailList = append(m.DetailList, &GoddessDetailInfo{})
			if err := m.DetailList[len(m.DetailList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("signup_status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserBaseListByGoddessClassifyReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserBaseListByGoddessClassifyReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserBaseListByGoddessClassifyReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GoddessClassifyId", wireType)
			}
			m.GoddessClassifyId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GoddessClassifyId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("goddess_classify_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserBaseListByGoddessClassifyResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserBaseListByGoddessClassifyResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserBaseListByGoddessClassifyResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GoddessClassifyId", wireType)
			}
			m.GoddessClassifyId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GoddessClassifyId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BaseList = append(m.BaseList, &GoddessBaseInfo{})
			if err := m.BaseList[len(m.BaseList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("goddess_classify_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatGetGoddessDetailListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatGetGoddessDetailListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatGetGoddessDetailListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGoddess
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGoddess
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGoddess
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGoddess
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsNeedAuditinfo", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsNeedAuditinfo = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatGetGoddessDetailListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatGetGoddessDetailListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatGetGoddessDetailListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DetailList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DetailList = append(m.DetailList, &GoddessDetailInfo{})
			if err := m.DetailList[len(m.DetailList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyGoddessSignupGuildInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifyGoddessSignupGuildInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifyGoddessSignupGuildInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GoddessUid", wireType)
			}
			m.GoddessUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GoddessUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Guildid", wireType)
			}
			m.Guildid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Guildid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("goddess_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guildid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GoddessTrend) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GoddessTrend: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GoddessTrend: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Creator", wireType)
			}
			m.Creator = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Creator |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateTime", wireType)
			}
			m.CreateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LikeCount", wireType)
			}
			m.LikeCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LikeCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentCount", wireType)
			}
			m.CommentCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommentCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImageList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImageList = append(m.ImageList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Tag", wireType)
			}
			m.Tag = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Tag |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastCommentTime", wireType)
			}
			m.LastCommentTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastCommentTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000100)
		case 11:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GoddessBaseInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GoddessBaseInfo == nil {
				m.GoddessBaseInfo = &GoddessBaseInfo{}
			}
			if err := m.GoddessBaseInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TeaseCount", wireType)
			}
			m.TeaseCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TeaseCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("title")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("content")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("creator")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("create_time")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("like_count")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("comment_count")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("tag")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("last_comment_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGoddessTrendsByGoddessClassifyReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGoddessTrendsByGoddessClassifyReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGoddessTrendsByGoddessClassifyReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClassifyId", wireType)
			}
			m.ClassifyId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClassifyId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartTopicId", wireType)
			}
			m.StartTopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TrendsCount", wireType)
			}
			m.TrendsCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TrendsCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start_topic_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("trends_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGoddessTrendsByGoddessUidReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGoddessTrendsByGoddessUidReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGoddessTrendsByGoddessUidReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGoddess
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.GoddessUidList = append(m.GoddessUidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGoddess
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGoddess
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGoddess
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.GoddessUidList = append(m.GoddessUidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field GoddessUidList", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartTopicId", wireType)
			}
			m.StartTopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TrendsCount", wireType)
			}
			m.TrendsCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TrendsCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start_topic_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("trends_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GoddessTrendsResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GoddessTrendsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GoddessTrendsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TrendList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TrendList = append(m.TrendList, &GoddessTrend{})
			if err := m.TrendList[len(m.TrendList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TrendsTotal", wireType)
			}
			m.TrendsTotal = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TrendsTotal |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HasMore", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.HasMore = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("trends_total")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("has_more")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TeaseGoddessReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TeaseGoddessReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TeaseGoddessReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GoddessUid", wireType)
			}
			m.GoddessUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GoddessUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("goddess_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TeaseGoddessResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TeaseGoddessResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TeaseGoddessResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BrushVoteCheckOpt) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BrushVoteCheckOpt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BrushVoteCheckOpt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IpInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IpInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserLevel", wireType)
			}
			m.UserLevel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UserLevel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GoddessVote) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GoddessVote: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GoddessVote: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Votes", wireType)
			}
			m.Votes = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Votes |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtraVotes", wireType)
			}
			m.ExtraVotes = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExtraVotes |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("votes")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("extra_votes")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VoteGoddessReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: VoteGoddessReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: VoteGoddessReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GoddessUid", wireType)
			}
			m.GoddessUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GoddessUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Votes", wireType)
			}
			m.Votes = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Votes |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PageSourceType", wireType)
			}
			m.PageSourceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PageSourceType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CheckOpt", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.CheckOpt == nil {
				m.CheckOpt = &BrushVoteCheckOpt{}
			}
			if err := m.CheckOpt.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("goddess_uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("votes")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VoteGoddessResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: VoteGoddessResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: VoteGoddessResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RemainVoteChances", wireType)
			}
			m.RemainVoteChances = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RemainVoteChances |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("remain_vote_chances")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGoddessRankingReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGoddessRankingReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGoddessRankingReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankingType", wireType)
			}
			m.RankingType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RankingType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClassifyId", wireType)
			}
			m.ClassifyId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClassifyId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartIndex", wireType)
			}
			m.StartIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartIndex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ranking_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("classify_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGoddessRankingResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGoddessRankingResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGoddessRankingResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GoddessVoteList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GoddessVoteList = append(m.GoddessVoteList, &GoddessVote{})
			if err := m.GoddessVoteList[len(m.GoddessVoteList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGoddessFinalRankingReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGoddessFinalRankingReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGoddessFinalRankingReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGoddessRankInRankingReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGoddessRankInRankingReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGoddessRankInRankingReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GoddessUid", wireType)
			}
			m.GoddessUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GoddessUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankingType", wireType)
			}
			m.RankingType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RankingType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("goddess_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ranking_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGoddessRankInRankingResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGoddessRankInRankingResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGoddessRankInRankingResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Rank", wireType)
			}
			m.Rank = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Rank |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("rank")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGoddessVotesReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGoddessVotesReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGoddessVotesReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGoddess
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.GoddessUidList = append(m.GoddessUidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGoddess
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGoddess
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGoddess
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.GoddessUidList = append(m.GoddessUidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field GoddessUidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGoddessVotesResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGoddessVotesResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGoddessVotesResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GoddessVoteList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GoddessVoteList = append(m.GoddessVoteList, &GoddessVote{})
			if err := m.GoddessVoteList[len(m.GoddessVoteList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserVoteChancesReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserVoteChancesReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserVoteChancesReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CheckOpt", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.CheckOpt == nil {
				m.CheckOpt = &BrushVoteCheckOpt{}
			}
			if err := m.CheckOpt.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserVoteChancesResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserVoteChancesResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserVoteChancesResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Chances", wireType)
			}
			m.Chances = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Chances |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("chances")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserToGoddessVotesReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserToGoddessVotesReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserToGoddessVotesReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GoddessUid", wireType)
			}
			m.GoddessUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GoddessUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("goddess_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserToGoddessVotesResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserToGoddessVotesResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserToGoddessVotesResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Votes", wireType)
			}
			m.Votes = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Votes |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("votes")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddExtraVotesReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddExtraVotesReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddExtraVotesReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUser", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OpUser = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GoddessUid", wireType)
			}
			m.GoddessUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GoddessUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtraVotes", wireType)
			}
			m.ExtraVotes = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExtraVotes |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_user")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("goddess_uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("extra_votes")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddExtraVotesResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddExtraVotesResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddExtraVotesResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CurrentVote", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.CurrentVote == nil {
				m.CurrentVote = &GoddessVote{}
			}
			if err := m.CurrentVote.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("current_vote")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ThirdPartyVoteGoddessReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ThirdPartyVoteGoddessReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ThirdPartyVoteGoddessReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ThirdPartyType", wireType)
			}
			m.ThirdPartyType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ThirdPartyType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Identifier", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Identifier = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GoddessUid", wireType)
			}
			m.GoddessUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GoddessUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VoteCnt", wireType)
			}
			m.VoteCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VoteCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PageSourceType", wireType)
			}
			m.PageSourceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PageSourceType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("third_party_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("identifier")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("goddess_uid")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("vote_cnt")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ThirdPartyVoteGoddessResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ThirdPartyVoteGoddessResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ThirdPartyVoteGoddessResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsVote", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsVote = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_vote")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ThirdPartyCheckVoteChancesReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ThirdPartyCheckVoteChancesReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ThirdPartyCheckVoteChancesReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ThirdPartyType", wireType)
			}
			m.ThirdPartyType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ThirdPartyType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Identifier", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Identifier = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("third_party_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("identifier")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ThirdPartyCheckVoteChancesResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ThirdPartyCheckVoteChancesResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ThirdPartyCheckVoteChancesResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RemainChances", wireType)
			}
			m.RemainChances = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RemainChances |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("remain_chances")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VoteID) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: VoteID: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: VoteID: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsInTt", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsInTt = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Identifier", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Identifier = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GoddessUid", wireType)
			}
			m.GoddessUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GoddessUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Salt", wireType)
			}
			m.Salt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Salt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_in_tt")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("identifier")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("goddess_uid")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("timestamp")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("salt")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ShareID) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ShareID: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ShareID: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Salt", wireType)
			}
			m.Salt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Salt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("timestamp")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("salt")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateVoteIDReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateVoteIDReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateVoteIDReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsInTt", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsInTt = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Identifier", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Identifier = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGoddess
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.GoddessUidList = append(m.GoddessUidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGoddess
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGoddess
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGoddess
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.GoddessUidList = append(m.GoddessUidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field GoddessUidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_in_tt")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("identifier")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateVoteIDResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateVoteIDResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateVoteIDResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VoteIdList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.VoteIdList = append(m.VoteIdList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateShareIDReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateShareIDReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateShareIDReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("timestamp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateShareIDResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateShareIDResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateShareIDResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ShareId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ShareId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("share_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCurrentStageReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetCurrentStageReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetCurrentStageReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCurrentStageResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetCurrentStageResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetCurrentStageResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CurrentStage", wireType)
			}
			m.CurrentStage = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CurrentStage |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("current_stage")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *NotifyRewardUserVoteChancesReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NotifyRewardUserVoteChancesReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NotifyRewardUserVoteChancesReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OriTs", wireType)
			}
			m.OriTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OriTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TriggerInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TriggerInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RewardNum", wireType)
			}
			m.RewardNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RewardNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CheckOpt", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.CheckOpt == nil {
				m.CheckOpt = &BrushVoteCheckOpt{}
			}
			if err := m.CheckOpt.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("target_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ori_ts")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *NotifyRewardUserVoteChancesResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NotifyRewardUserVoteChancesResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NotifyRewardUserVoteChancesResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsReward", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsReward = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_reward")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VoteStatisticsAsyncData) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: VoteStatisticsAsyncData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: VoteStatisticsAsyncData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetGoddess", wireType)
			}
			m.TargetGoddess = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetGoddess |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OriUid", wireType)
			}
			m.OriUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OriUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceType", wireType)
			}
			m.SourceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExUserInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGoddess
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ExUserInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VoteCnt", wireType)
			}
			m.VoteCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VoteCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("target_goddess")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ori_uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("source_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RewardStatisticsAsyncData) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RewardStatisticsAsyncData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RewardStatisticsAsyncData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RewardType", wireType)
			}
			m.RewardType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RewardType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RewardNum", wireType)
			}
			m.RewardNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RewardNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("target_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("reward_type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("reward_num")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TeaseStatisticsAsyncData) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TeaseStatisticsAsyncData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TeaseStatisticsAsyncData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GodessUid", wireType)
			}
			m.GodessUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GodessUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGoddess(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGoddess
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("godess_uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("topic_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipGoddess(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowGoddess
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGoddess
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthGoddess
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowGoddess
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipGoddess(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthGoddess = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowGoddess   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("services/goddess/goddess.proto", fileDescriptorGoddess) }

var fileDescriptorGoddess = []byte{
	// 3592 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x5a, 0xdd, 0x6f, 0x1c, 0x4b,
	0x56, 0x4f, 0xcf, 0xf8, 0x63, 0xe6, 0x8c, 0x3f, 0xc6, 0x95, 0x7b, 0x73, 0xc7, 0x93, 0xc4, 0xee,
	0x5b, 0xf9, 0xf2, 0xcd, 0x8d, 0x9d, 0xdd, 0xdc, 0x40, 0x90, 0xaf, 0x77, 0x74, 0xfd, 0x75, 0xb3,
	0xa3, 0x75, 0xec, 0x30, 0x1e, 0xe7, 0x12, 0x90, 0x68, 0x75, 0xa6, 0xcb, 0xe3, 0xc6, 0x33, 0xdd,
	0xed, 0xae, 0x9a, 0xc4, 0x46, 0xb0, 0x80, 0x96, 0x07, 0xb4, 0xb0, 0x08, 0xb1, 0xe2, 0x05, 0xf1,
	0x80, 0x56, 0x79, 0x04, 0xa1, 0xd5, 0x3e, 0xac, 0xe0, 0x8d, 0x27, 0x2e, 0xdf, 0x8b, 0x04, 0x12,
	0x4f, 0x08, 0x5d, 0x5e, 0x22, 0xf1, 0xc0, 0x03, 0xff, 0x00, 0xaa, 0xaa, 0xfe, 0xa8, 0xee, 0xe9,
	0xf6, 0x47, 0xc8, 0x3e, 0x25, 0x3e, 0xa7, 0xba, 0xce, 0xef, 0x9c, 0x3a, 0x1f, 0x75, 0x4e, 0x0d,
	0xcc, 0x51, 0xe2, 0xbf, 0xb4, 0x3b, 0x84, 0xde, 0xef, 0xba, 0x96, 0x45, 0x68, 0xf4, 0xef, 0x92,
	0xe7, 0xbb, 0xcc, 0x45, 0xe3, 0xc1, 0x9f, 0xf5, 0x9b, 0x1d, 0xb7, 0xdf, 0x77, 0x9d, 0xfb, 0xac,
	0xf7, 0xd2, 0xb3, 0x3b, 0x87, 0x3d, 0x72, 0x9f, 0x1e, 0xbe, 0x18, 0xd8, 0x3d, 0x66, 0x3b, 0xec,
	0xc4, 0x23, 0x72, 0x39, 0xfe, 0x1f, 0x0d, 0xa6, 0x1f, 0xcb, 0x2f, 0xd6, 0x4c, 0x4a, 0x9a, 0xce,
	0xbe, 0x8b, 0xae, 0x40, 0x71, 0x60, 0x5b, 0x35, 0x4d, 0x2f, 0x2c, 0x4c, 0xae, 0x8d, 0x7c, 0xf9,
	0x1f, 0xf3, 0x97, 0x5a, 0x9c, 0x80, 0xe6, 0xa1, 0xd4, 0x1d, 0xd8, 0x3d, 0xcb, 0xb0, 0xad, 0x5a,
	0x41, 0xd7, 0x22, 0xe6, 0xb8, 0xa0, 0x36, 0x2d, 0xf4, 0x11, 0x4c, 0x76, 0x7a, 0x26, 0xa5, 0xf6,
	0xfe, 0x89, 0xc1, 0x65, 0xd4, 0x8a, 0xca, 0xaa, 0x89, 0x90, 0xd5, 0x3e, 0xf1, 0x08, 0xba, 0x05,
	0x15, 0x6a, 0x77, 0x9d, 0x81, 0x67, 0x50, 0x66, 0xb2, 0xda, 0x88, 0xb2, 0x10, 0x24, 0x63, 0x97,
	0x99, 0x0c, 0x7d, 0x08, 0xe5, 0x60, 0x19, 0xa3, 0xb5, 0x51, 0x65, 0x51, 0x49, 0x92, 0xdb, 0x14,
	0x2d, 0x41, 0x35, 0x58, 0x32, 0xf0, 0x2c, 0x93, 0x11, 0xbe, 0x72, 0x4c, 0x59, 0x39, 0x25, 0xb9,
	0x7b, 0x82, 0xd9, 0xa6, 0xf8, 0xbf, 0x35, 0x98, 0x09, 0x34, 0xde, 0x3c, 0x66, 0xc4, 0xb1, 0x84,
	0xce, 0x37, 0x00, 0x6c, 0x87, 0xf9, 0xae, 0x61, 0x3b, 0xfb, 0x6e, 0x4d, 0xd3, 0xb5, 0x85, 0x72,
	0xf0, 0x7d, 0x59, 0xd0, 0xc5, 0xa2, 0xeb, 0x00, 0xde, 0x81, 0xcb, 0x5c, 0xa3, 0x67, 0x53, 0x56,
	0x2b, 0xe8, 0xc5, 0x85, 0x72, 0xab, 0x2c, 0x28, 0x5b, 0x36, 0x65, 0xe8, 0x2a, 0x94, 0xbb, 0x66,
	0x9f, 0x48, 0x6e, 0x51, 0x70, 0x4b, 0x9c, 0x20, 0x98, 0x57, 0xa0, 0xf8, 0x2b, 0xee, 0x0b, 0xa1,
	0x68, 0xb8, 0x33, 0x27, 0x70, 0x43, 0xfc, 0xaa, 0x6b, 0xd9, 0x66, 0xc7, 0xe0, 0x38, 0x85, 0x8e,
	0x21, 0x1f, 0x24, 0x63, 0xd7, 0xee, 0x3a, 0xe8, 0x1a, 0x8c, 0x1d, 0x10, 0xbb, 0x7b, 0xc0, 0x12,
	0xba, 0x05, 0x34, 0x54, 0x83, 0x91, 0x17, 0x03, 0xca, 0x6a, 0xe3, 0xca, 0xd7, 0x82, 0x82, 0x8f,
	0xe1, 0x4a, 0xa0, 0xec, 0xae, 0x30, 0xc3, 0xea, 0xc0, 0xb2, 0x59, 0xa0, 0xcc, 0xf8, 0xd1, 0x91,
	0xd1, 0x71, 0x2d, 0x92, 0x50, 0x77, 0xec, 0xe8, 0x68, 0xdd, 0xb5, 0x08, 0xaa, 0xc3, 0xa8, 0x77,
	0xe0, 0x3a, 0x44, 0x9c, 0x74, 0xc8, 0x94, 0x24, 0x6e, 0x2c, 0x93, 0xef, 0x23, 0x8d, 0x55, 0x54,
	0x8d, 0x65, 0x86, 0xfb, 0xe3, 0xbf, 0x89, 0xed, 0xbc, 0x41, 0x98, 0x69, 0xf7, 0x84, 0xd4, 0x9f,
	0x81, 0xf2, 0x0b, 0x93, 0x92, 0xd0, 0xcc, 0x85, 0x85, 0xca, 0x83, 0xda, 0x52, 0xe8, 0xc1, 0x29,
	0x47, 0x6c, 0x95, 0x5e, 0x84, 0x2e, 0xf9, 0x29, 0x54, 0x88, 0x38, 0x2c, 0xf9, 0x21, 0xc7, 0x54,
	0x79, 0x50, 0x4f, 0x7f, 0x18, 0x9f, 0x67, 0x0b, 0x48, 0x7c, 0xb6, 0x8d, 0x21, 0xb8, 0x95, 0x07,
	0xf3, 0xe9, 0x6f, 0x53, 0xe6, 0x51, 0x35, 0xf9, 0x14, 0xca, 0x92, 0xdb, 0x22, 0x47, 0x68, 0x09,
	0x46, 0x14, 0xec, 0x43, 0x10, 0x62, 0x55, 0x5b, 0x62, 0x1d, 0xb6, 0xe0, 0xf2, 0x13, 0xd7, 0xb2,
	0xf7, 0x4f, 0xe4, 0x16, 0x82, 0x73, 0xf1, 0x6d, 0xf8, 0x69, 0xb9, 0x9e, 0x31, 0xa0, 0xc4, 0xaf,
	0x15, 0xf4, 0x42, 0x7c, 0x5a, 0xae, 0xb7, 0x47, 0x89, 0x8f, 0x4f, 0xa0, 0xba, 0x41, 0x7a, 0x49,
	0x11, 0x37, 0x00, 0xc2, 0xc0, 0x48, 0x45, 0x73, 0x10, 0x53, 0x7b, 0xb6, 0x75, 0xc6, 0xbe, 0x3c,
	0xfe, 0x5c, 0xcf, 0xf0, 0x89, 0x49, 0x5d, 0xa7, 0x56, 0x54, 0x16, 0x94, 0x5c, 0xaf, 0x25, 0xa8,
	0x78, 0x07, 0x66, 0x52, 0xa2, 0xa9, 0x87, 0x96, 0xe1, 0xca, 0x0b, 0xb2, 0xef, 0xfa, 0xc4, 0xb0,
	0x48, 0xcf, 0x90, 0xe2, 0x78, 0xa0, 0x93, 0x04, 0x8e, 0xf7, 0xe4, 0x9a, 0xe8, 0x73, 0xb1, 0x02,
	0xff, 0x40, 0x03, 0x24, 0xce, 0xe1, 0xdd, 0xab, 0x93, 0xca, 0x3a, 0x45, 0x65, 0x93, 0x54, 0xd6,
	0x91, 0x0e, 0xd3, 0xa7, 0xdd, 0x44, 0xc4, 0x96, 0x04, 0xf9, 0x09, 0xed, 0xe2, 0x67, 0x80, 0x1e,
	0x13, 0xc6, 0x37, 0x8d, 0xbc, 0x95, 0x1c, 0xa1, 0xab, 0x30, 0xe6, 0x0e, 0xe3, 0x1b, 0x75, 0x05,
	0xb6, 0x1b, 0x00, 0xcc, 0xf4, 0xbb, 0x84, 0x89, 0x05, 0x05, 0x55, 0x01, 0x49, 0xdf, 0xb3, 0x2d,
	0xbc, 0x05, 0x97, 0x87, 0xf6, 0xa5, 0xde, 0x5b, 0x86, 0x0d, 0xfe, 0x05, 0x78, 0x2f, 0xd8, 0x4d,
	0x09, 0x8d, 0x77, 0x82, 0xb3, 0x0d, 0xef, 0x67, 0xec, 0x4c, 0x3d, 0x1e, 0xa9, 0x81, 0x89, 0x4f,
	0xf3, 0x6f, 0x35, 0x52, 0x69, 0x74, 0xcc, 0x0a, 0x5e, 0x25, 0x00, 0xde, 0x31, 0x5e, 0x75, 0x67,
	0x89, 0xd7, 0x12, 0x14, 0xe3, 0x9c, 0xf1, 0x08, 0x56, 0xf4, 0x7f, 0xfc, 0x57, 0x1a, 0x5c, 0x0e,
	0x56, 0xac, 0x07, 0xd5, 0x4d, 0x64, 0x9c, 0x5b, 0x50, 0x89, 0x0a, 0x61, 0x0a, 0x34, 0x84, 0x8c,
	0x54, 0xbd, 0x74, 0xcc, 0x3e, 0x49, 0xf8, 0x6c, 0x54, 0x2f, 0xb7, 0xcd, 0x3e, 0x41, 0xf7, 0x60,
	0xda, 0xa6, 0xc6, 0x4b, 0x93, 0x97, 0x5f, 0x69, 0x30, 0xe1, 0xbd, 0xa5, 0x60, 0xf1, 0xa4, 0x4d,
	0x9f, 0x71, 0x9e, 0x0c, 0x19, 0x25, 0x56, 0x3a, 0x4e, 0xb2, 0xb8, 0x06, 0xb1, 0xb2, 0xee, 0x30,
	0xfc, 0xf3, 0x70, 0xed, 0x31, 0x61, 0xab, 0xbd, 0x5e, 0x86, 0x06, 0xdc, 0xe8, 0x5f, 0x07, 0x64,
	0x53, 0xc3, 0x21, 0x24, 0x94, 0x28, 0x36, 0xd3, 0x14, 0xa9, 0xd3, 0x36, 0xdd, 0x26, 0x24, 0x10,
	0xca, 0xb7, 0xfc, 0x7d, 0x0d, 0xae, 0x9f, 0xb2, 0x27, 0xf5, 0xd0, 0xaa, 0xa2, 0xb2, 0xa8, 0x93,
	0x9a, 0x5e, 0x5c, 0xa8, 0x3c, 0xb8, 0x96, 0x36, 0x78, 0xe2, 0xc3, 0xc8, 0x14, 0xa2, 0x92, 0xde,
	0x85, 0x29, 0xb3, 0xd7, 0x53, 0x31, 0xa9, 0x97, 0x91, 0x09, 0xb3, 0xd7, 0x8b, 0x01, 0xfd, 0xb6,
	0x06, 0x38, 0x71, 0xee, 0x7c, 0x87, 0xb5, 0x20, 0x17, 0xf3, 0x58, 0x1f, 0x50, 0xae, 0xea, 0x47,
	0x30, 0xa9, 0xe4, 0x85, 0x01, 0x4d, 0x9c, 0xd8, 0x04, 0x55, 0x56, 0xa3, 0x4f, 0xe0, 0x72, 0xca,
	0x2a, 0x41, 0x45, 0x8a, 0xcd, 0x52, 0x55, 0xcd, 0x22, 0xfc, 0xe4, 0x7b, 0x1a, 0xdc, 0x38, 0x13,
	0x06, 0xf5, 0x2e, 0x82, 0x23, 0xf6, 0xdb, 0xe8, 0x32, 0x72, 0x2e, 0xbf, 0xe5, 0x92, 0xf1, 0x2f,
	0x45, 0x70, 0x78, 0xd2, 0x90, 0x60, 0x52, 0x86, 0xe7, 0x66, 0x79, 0x08, 0x97, 0x83, 0xfd, 0x8c,
	0x3c, 0x77, 0x9e, 0xe9, 0xa6, 0xce, 0xcb, 0xc2, 0xdf, 0xd7, 0xe0, 0xe6, 0xd9, 0xbb, 0x53, 0xef,
	0xed, 0xb6, 0x8f, 0x52, 0xa1, 0xa2, 0xf6, 0x19, 0xa9, 0x50, 0xa8, 0x6c, 0x43, 0x7d, 0xcd, 0x64,
	0x8f, 0x09, 0x4b, 0x58, 0x86, 0xb3, 0xb8, 0xa6, 0xb3, 0x50, 0x1a, 0xd8, 0x56, 0xec, 0x91, 0x93,
	0xad, 0xf1, 0x81, 0x6d, 0x09, 0x77, 0xfb, 0x1a, 0xcc, 0x84, 0x07, 0x2e, 0xb2, 0x7f, 0x74, 0x01,
	0x49, 0x45, 0xc1, 0x6a, 0xc8, 0xc4, 0xbf, 0x08, 0x57, 0x73, 0x45, 0x25, 0x32, 0xce, 0x5b, 0x9c,
	0xdc, 0x01, 0xcc, 0xcb, 0xeb, 0x44, 0xe2, 0xda, 0xf2, 0x58, 0x5c, 0xc0, 0x83, 0xb8, 0xbd, 0x05,
	0x95, 0xd0, 0xac, 0xe9, 0x8c, 0x09, 0x01, 0x83, 0xa7, 0xcd, 0x39, 0x90, 0xf7, 0xf6, 0x54, 0xce,
	0x0c, 0x89, 0xf8, 0xdf, 0x8b, 0x30, 0x11, 0x08, 0x69, 0xfb, 0xc4, 0x11, 0xd7, 0x7f, 0xe6, 0x7a,
	0x76, 0x27, 0x7d, 0x46, 0xe3, 0x82, 0xda, 0xb4, 0xf8, 0x95, 0x91, 0xd9, 0xac, 0x97, 0x4c, 0x63,
	0x92, 0xc4, 0xa5, 0x75, 0x5c, 0x87, 0x11, 0x87, 0x25, 0xae, 0x11, 0x21, 0x51, 0xf0, 0x7d, 0x62,
	0x32, 0xd7, 0xaf, 0x8d, 0xa8, 0x7b, 0x07, 0x44, 0x91, 0x51, 0xf9, 0x7f, 0x89, 0xc1, 0xec, 0x3e,
	0xa9, 0x8d, 0x26, 0x32, 0xaa, 0x60, 0xb4, 0xed, 0xbe, 0xb8, 0x99, 0xf6, 0xec, 0x43, 0x62, 0x74,
	0xdc, 0x81, 0xc3, 0xaf, 0xca, 0x4a, 0x2d, 0xe0, 0xf4, 0x75, 0x4e, 0x16, 0x69, 0xd7, 0xed, 0xf7,
	0x89, 0xc3, 0x82, 0x75, 0xe3, 0x6a, 0x94, 0x05, 0x2c, 0xb9, 0xf4, 0x3a, 0x80, 0xdd, 0x37, 0xbb,
	0x81, 0xb7, 0x95, 0xe4, 0x8d, 0x5f, 0x50, 0xc2, 0x4b, 0x3d, 0x33, 0xbb, 0xb5, 0xb2, 0xda, 0x29,
	0x31, 0xb3, 0xcb, 0x7d, 0xa6, 0x67, 0x52, 0xbe, 0xbd, 0x14, 0x23, 0x30, 0x83, 0xb2, 0x6a, 0x9a,
	0xb3, 0xd7, 0x25, 0x57, 0x00, 0xdf, 0x80, 0xd0, 0xd5, 0x8d, 0xb8, 0xd0, 0x57, 0xc4, 0x55, 0x35,
	0xdf, 0xbb, 0xa7, 0xbb, 0xa9, 0xce, 0xed, 0x16, 0x54, 0x18, 0xe1, 0x9f, 0x4b, 0xbd, 0x26, 0xd4,
	0xae, 0x4a, 0x30, 0x84, 0x56, 0xfc, 0x86, 0x75, 0x33, 0x76, 0x4f, 0x71, 0xba, 0x34, 0x33, 0x01,
	0x0c, 0xd5, 0x31, 0x2d, 0xb3, 0x8e, 0xdd, 0x85, 0x29, 0xca, 0x4c, 0x9f, 0x19, 0x91, 0x7f, 0x14,
	0x12, 0x79, 0x8b, 0xf3, 0xda, 0x81, 0x93, 0xdc, 0x81, 0x09, 0x26, 0x04, 0x06, 0x18, 0xd5, 0x3b,
	0x58, 0x45, 0x72, 0x24, 0xc8, 0x3f, 0xd5, 0x60, 0x3e, 0x17, 0xe4, 0x9e, 0x6d, 0x71, 0x7c, 0x0b,
	0x50, 0x55, 0x5c, 0x5d, 0x0d, 0xdf, 0xa9, 0xd8, 0xd3, 0xc3, 0xa2, 0xf1, 0xee, 0x21, 0x7e, 0x3f,
	0x6e, 0x71, 0x24, 0xbe, 0x20, 0xad, 0x81, 0x58, 0xa4, 0xd6, 0xb7, 0xf7, 0xd3, 0x67, 0x28, 0xd6,
	0xb7, 0xca, 0x62, 0xa1, 0x00, 0x18, 0x0b, 0x65, 0x2e, 0x33, 0x7b, 0x09, 0x78, 0x81, 0xd0, 0x36,
	0x67, 0xf0, 0x30, 0x3c, 0x30, 0xa9, 0xd1, 0x77, 0x7d, 0x92, 0xb8, 0x02, 0x8c, 0x1f, 0x98, 0xf4,
	0x89, 0xeb, 0x13, 0xfc, 0x12, 0xa6, 0xdb, 0xfc, 0xac, 0x03, 0x49, 0x67, 0xde, 0x9f, 0x52, 0xf9,
	0xa2, 0x90, 0x93, 0x2f, 0xd4, 0xf0, 0x57, 0xfb, 0xfa, 0x30, 0xfc, 0x31, 0x82, 0x6a, 0x52, 0x2e,
	0xf5, 0xf0, 0xb7, 0x61, 0x66, 0xcd, 0x1f, 0xd0, 0x83, 0x67, 0x2e, 0x23, 0xeb, 0x07, 0xa4, 0x73,
	0xb8, 0xe3, 0xf1, 0xa0, 0x1a, 0xb7, 0xbd, 0xe1, 0x46, 0x7b, 0xcc, 0xf6, 0x42, 0x27, 0xb6, 0xc8,
	0x4b, 0xbb, 0x43, 0xe2, 0x5e, 0x2f, 0xea, 0x88, 0x25, 0x23, 0xec, 0xd8, 0xf9, 0x3d, 0xdf, 0xe8,
	0x91, 0x97, 0xa4, 0x97, 0x40, 0x54, 0xe6, 0xf4, 0x2d, 0x4e, 0xc6, 0x07, 0x50, 0x09, 0xe0, 0x70,
	0x04, 0xb9, 0x93, 0x8d, 0x3a, 0x8c, 0xbe, 0x74, 0x19, 0xa1, 0x09, 0xe5, 0x25, 0x89, 0xc3, 0x21,
	0xc7, 0xcc, 0x37, 0x0d, 0xb9, 0x82, 0x9b, 0x7c, 0x34, 0x84, 0x23, 0x18, 0x7c, 0x67, 0x8a, 0xff,
	0x55, 0x83, 0x29, 0xfe, 0x3f, 0xc5, 0xea, 0x79, 0xd2, 0xce, 0x69, 0xf0, 0x08, 0x54, 0x71, 0x18,
	0xd4, 0x12, 0x54, 0x3d, 0x9e, 0x96, 0xa8, 0x3b, 0xf0, 0x3b, 0x44, 0x0e, 0x5b, 0xd4, 0x6b, 0xde,
	0x14, 0xe7, 0xee, 0x0a, 0xa6, 0x18, 0xb7, 0x3c, 0x82, 0x72, 0x87, 0x9b, 0xdf, 0x70, 0x3d, 0x26,
	0x66, 0x0c, 0x6a, 0xc5, 0x19, 0x3a, 0xa1, 0x56, 0xa9, 0x13, 0xfc, 0x0f, 0x3f, 0x86, 0xe9, 0x84,
	0x56, 0xb2, 0x6c, 0xfb, 0xa4, 0x6f, 0xda, 0x8e, 0xb0, 0x88, 0xd1, 0x39, 0x30, 0x9d, 0x0e, 0x49,
	0x5e, 0x55, 0x66, 0xe4, 0x02, 0xb9, 0xa7, 0x60, 0xe3, 0xbf, 0xd6, 0xc4, 0xdd, 0x3e, 0xdc, 0xc8,
	0x74, 0x0e, 0x6d, 0xa7, 0xcb, 0xad, 0x74, 0x07, 0x26, 0x7c, 0xf9, 0x97, 0x54, 0x43, 0xdd, 0xa7,
	0x12, 0x70, 0x84, 0x0e, 0x81, 0x39, 0x0b, 0x19, 0xe6, 0x54, 0x93, 0x54, 0x31, 0xe7, 0xb2, 0xcd,
	0x7b, 0x3f, 0x91, 0x01, 0x6c, 0xc7, 0x22, 0xc7, 0xa9, 0x89, 0x13, 0x67, 0x34, 0x39, 0x9d, 0x5b,
	0xbd, 0x67, 0xf7, 0x6d, 0x96, 0x98, 0x36, 0x49, 0x12, 0x7e, 0x2e, 0x9a, 0x88, 0xb4, 0x0a, 0xd4,
	0x43, 0x9f, 0xc5, 0xd9, 0x5b, 0xd8, 0x44, 0x89, 0xfc, 0xf7, 0xd2, 0x91, 0xcf, 0x8d, 0x12, 0x65,
	0x6e, 0xfe, 0x87, 0xa8, 0xeb, 0x9f, 0xc0, 0x6c, 0xbc, 0xf5, 0xe7, 0xb6, 0x63, 0xf6, 0x14, 0x13,
	0xe5, 0x38, 0x12, 0xee, 0x41, 0x3d, 0x89, 0xa7, 0xe9, 0x28, 0x5f, 0x9d, 0xf3, 0x1e, 0x90, 0xb6,
	0x7f, 0x21, 0xc7, 0xfe, 0xf8, 0x11, 0x5c, 0xcd, 0x95, 0x46, 0x3d, 0x54, 0x83, 0x11, 0xbe, 0x3a,
	0xf1, 0xbd, 0xa0, 0xe0, 0x86, 0xe8, 0x95, 0x15, 0xf5, 0xe9, 0x85, 0x72, 0x37, 0xfe, 0x42, 0xf4,
	0xc4, 0xc9, 0xef, 0xdf, 0x89, 0xd1, 0x0f, 0xa2, 0xa6, 0x50, 0xf1, 0xd4, 0xd3, 0x22, 0x37, 0x11,
	0x46, 0x85, 0x0b, 0x84, 0xd1, 0xcf, 0xc1, 0x95, 0x2c, 0x49, 0xd4, 0x13, 0x17, 0x9f, 0x8c, 0x08,
	0x0a, 0x89, 0xf8, 0x39, 0xd4, 0x82, 0x2f, 0xdb, 0x6e, 0xda, 0x84, 0xff, 0xbf, 0x04, 0x83, 0x1f,
	0x09, 0x9f, 0xcb, 0xda, 0x9a, 0x7a, 0x71, 0xf6, 0xd1, 0x86, 0xb2, 0x0f, 0xfe, 0x0d, 0xa8, 0xae,
	0x5a, 0xd6, 0x66, 0x94, 0xfc, 0x38, 0x16, 0x65, 0xf2, 0xa2, 0x65, 0x4f, 0x5e, 0xce, 0x93, 0xf3,
	0xce, 0x99, 0x6c, 0xb7, 0x60, 0x26, 0x05, 0x80, 0x7a, 0xe8, 0x11, 0x4c, 0x74, 0x06, 0xbe, 0xcf,
	0xef, 0x5b, 0xfc, 0xeb, 0xa0, 0x95, 0xcf, 0x76, 0x85, 0x4a, 0xb0, 0x92, 0xff, 0x81, 0xdf, 0x68,
	0x50, 0x6b, 0x1f, 0xd8, 0xbe, 0xf5, 0xd4, 0xf4, 0xd9, 0x49, 0x2a, 0x89, 0x2f, 0x41, 0x95, 0x71,
	0x9e, 0xe1, 0x71, 0xe6, 0x70, 0x8a, 0x9a, 0x62, 0xd1, 0x97, 0x22, 0x4b, 0xdd, 0x04, 0xb0, 0x2d,
	0xe2, 0x30, 0x7b, 0xdf, 0x4e, 0x0d, 0xa1, 0x14, 0x7a, 0xda, 0x1c, 0xc5, 0xfc, 0x9a, 0x2b, 0x73,
	0xac, 0xe8, 0xe2, 0x15, 0xef, 0xe0, 0xd4, 0x75, 0x87, 0x65, 0xd6, 0x81, 0xd1, 0xfc, 0x3a, 0x80,
	0x97, 0x61, 0x36, 0x47, 0x53, 0xea, 0x89, 0xba, 0x4c, 0x63, 0xdb, 0x95, 0xa2, 0xba, 0x2c, 0x6c,
	0x86, 0x07, 0x70, 0x3d, 0xfe, 0x56, 0xf8, 0x78, 0x2a, 0x6a, 0x7e, 0x2a, 0xa6, 0xc2, 0x4f, 0x60,
	0xee, 0x34, 0xb1, 0xd4, 0x43, 0x1f, 0xc3, 0x54, 0x50, 0x90, 0xb2, 0x22, 0x69, 0x52, 0xf2, 0xc2,
	0x3a, 0xf4, 0x23, 0x0d, 0xc6, 0xf8, 0x06, 0xcd, 0x0d, 0x34, 0x07, 0x25, 0x9b, 0x1a, 0xb6, 0x63,
	0x30, 0x96, 0x56, 0xb8, 0xe9, 0xb4, 0xd9, 0xbb, 0x3d, 0x4a, 0x0c, 0x65, 0xde, 0x05, 0x50, 0x66,
	0xf6, 0xbd, 0xc4, 0x59, 0xc6, 0x64, 0x9e, 0x42, 0xa9, 0xd9, 0x63, 0x89, 0x4e, 0x41, 0x50, 0xb0,
	0x01, 0xe3, 0xbb, 0x07, 0xa6, 0xcf, 0x51, 0xe7, 0x05, 0x7d, 0x42, 0x40, 0xe1, 0xa2, 0x02, 0x7e,
	0x4b, 0x83, 0xe9, 0x75, 0xd1, 0x47, 0x49, 0xe3, 0xf0, 0xf3, 0x7c, 0x37, 0xf6, 0xc9, 0xca, 0xf3,
	0xc5, 0xcc, 0x3c, 0xff, 0x10, 0xaa, 0x49, 0x08, 0xd4, 0x43, 0x3a, 0x4c, 0x88, 0x08, 0x50, 0x2b,
	0x44, 0xb9, 0x05, 0x9c, 0xd6, 0x94, 0x5f, 0x6d, 0x87, 0x5f, 0x05, 0x06, 0x3a, 0x2d, 0x31, 0x9e,
	0xc3, 0x46, 0xf8, 0x21, 0xcc, 0xa4, 0xf6, 0xa3, 0x1e, 0x0f, 0x44, 0xca, 0xff, 0x0c, 0x7b, 0xdf,
	0xa8, 0x7f, 0x15, 0xd4, 0xa6, 0x85, 0xdf, 0x13, 0x35, 0x6e, 0x5d, 0x66, 0x95, 0x5d, 0x66, 0x76,
	0x49, 0x8b, 0x1c, 0xe1, 0xcf, 0x44, 0xe5, 0x4a, 0x52, 0xe5, 0x98, 0x27, 0xcc, 0x54, 0x94, 0x13,
	0x93, 0x63, 0x9e, 0x8e, 0xb2, 0x1c, 0x7f, 0xa7, 0x00, 0x73, 0xdb, 0x2e, 0x13, 0xfd, 0xd8, 0x2b,
	0xd3, 0xb7, 0x32, 0x8a, 0x55, 0x72, 0xfe, 0xa9, 0x65, 0xce, 0x3f, 0x45, 0x07, 0xe0, 0xdb, 0x06,
	0x4b, 0x5d, 0x71, 0x5d, 0xdf, 0x6e, 0x53, 0xee, 0x16, 0xc1, 0x73, 0x9d, 0xe2, 0x16, 0x9c, 0x22,
	0xbb, 0x12, 0xbb, 0xdb, 0x25, 0xbe, 0xbc, 0x8c, 0xab, 0xc3, 0xf0, 0x4a, 0xc0, 0x09, 0x6f, 0xe3,
	0xbe, 0x00, 0x68, 0x38, 0x83, 0x7e, 0x22, 0x05, 0x95, 0x25, 0x7d, 0x7b, 0xd0, 0x4f, 0x96, 0xcf,
	0xb1, 0x0b, 0x94, 0xcf, 0x0d, 0x98, 0x3f, 0xd5, 0x08, 0xd4, 0x43, 0x1f, 0x42, 0xd9, 0xa6, 0x86,
	0x94, 0x95, 0xf0, 0xd6, 0x92, 0x4d, 0xe5, 0x57, 0xf8, 0xdf, 0x34, 0xf8, 0x80, 0x7f, 0xb6, 0xcb,
	0x4c, 0x66, 0x53, 0x66, 0x77, 0xe8, 0x2a, 0x3d, 0x71, 0x3a, 0x1b, 0x26, 0x33, 0x79, 0x0e, 0x09,
	0x8c, 0x18, 0xe0, 0x49, 0xe6, 0x10, 0xc9, 0x0b, 0x92, 0xa5, 0xa8, 0x75, 0xbe, 0x3d, 0x54, 0xc8,
	0xb8, 0x85, 0x83, 0x22, 0xa6, 0xe6, 0xe3, 0xe4, 0x2b, 0x43, 0x7c, 0x27, 0xbf, 0x0d, 0x13, 0xe4,
	0x58, 0x54, 0xcc, 0x61, 0xdb, 0x02, 0x39, 0xe6, 0x4a, 0x0a, 0xd3, 0xaa, 0x45, 0x40, 0x35, 0x6c,
	0x58, 0x04, 0xf0, 0xef, 0x69, 0x30, 0x2b, 0x55, 0xcc, 0xd2, 0xec, 0x5c, 0xee, 0x71, 0x0b, 0x2a,
	0xc1, 0xf1, 0x0d, 0xdd, 0x01, 0x83, 0x73, 0x15, 0x90, 0x93, 0xa7, 0xac, 0x2a, 0x16, 0x9f, 0x32,
	0xfe, 0x75, 0xa8, 0x89, 0x3e, 0x30, 0x0b, 0xcc, 0x59, 0x83, 0xfc, 0xae, 0x9b, 0x79, 0x45, 0x28,
	0x4b, 0xfa, 0x70, 0x1b, 0x3a, 0x3c, 0x85, 0xba, 0xfb, 0xc7, 0x1a, 0xa0, 0xdd, 0xe6, 0xe3, 0xed,
	0xbd, 0xa7, 0xc6, 0x6e, 0x7b, 0xb5, 0xbd, 0xb7, 0x6b, 0xb4, 0x9f, 0x3f, 0xdd, 0x44, 0x97, 0x61,
	0x5a, 0xa1, 0x1a, 0xdb, 0x3b, 0xed, 0xaa, 0x86, 0xea, 0x70, 0x45, 0x25, 0x7e, 0xb1, 0xda, 0x6c,
	0x1b, 0xab, 0x7b, 0x1b, 0xcd, 0x76, 0xb5, 0x80, 0x74, 0xb8, 0xa6, 0xf2, 0x04, 0x59, 0xac, 0x78,
	0xb2, 0xb3, 0xd1, 0xfc, 0xfc, 0x79, 0xb5, 0x88, 0xae, 0xc3, 0xec, 0xf0, 0x8a, 0xed, 0x9d, 0xf6,
	0xd3, 0xd5, 0xdd, 0xdd, 0xea, 0x08, 0x42, 0x30, 0xa5, 0xb2, 0x77, 0xbe, 0x55, 0x1d, 0xbd, 0xbb,
	0x02, 0x95, 0x96, 0xd2, 0xd2, 0x00, 0x8c, 0x7d, 0x73, 0x67, 0xaf, 0xb5, 0xf5, 0xbc, 0xaa, 0xa1,
	0x32, 0x8c, 0x6e, 0xac, 0x36, 0xb7, 0x9e, 0x57, 0x0b, 0xa8, 0x02, 0xe3, 0x9f, 0x37, 0xb7, 0x57,
	0xb7, 0xb6, 0xb8, 0x94, 0x32, 0x8c, 0x3e, 0xdb, 0x69, 0x6f, 0x6e, 0x54, 0x47, 0xee, 0x7e, 0x03,
	0x46, 0x45, 0x56, 0x40, 0x25, 0x18, 0xd9, 0x76, 0x1d, 0x52, 0xbd, 0xc4, 0x77, 0x90, 0x23, 0xc0,
	0xaa, 0xc6, 0xa9, 0xdc, 0xbd, 0x83, 0x0d, 0x78, 0x0f, 0xd1, 0x3b, 0xa9, 0x16, 0xd1, 0x38, 0x14,
	0x37, 0x1d, 0xab, 0x3a, 0x72, 0x77, 0x19, 0xa0, 0x15, 0x9f, 0xe5, 0xfb, 0x30, 0xd3, 0xda, 0xfc,
	0x62, 0xb5, 0xb5, 0x21, 0xec, 0x63, 0x48, 0xd9, 0x5a, 0x9a, 0xbc, 0xfb, 0xcd, 0xd5, 0xd6, 0x66,
	0xb5, 0xf0, 0xe0, 0x7f, 0xaf, 0x41, 0xf8, 0xcb, 0x02, 0xf4, 0x2a, 0x94, 0x89, 0x50, 0x14, 0xbd,
	0xd1, 0x03, 0x69, 0xfd, 0xda, 0x52, 0xf4, 0x8b, 0x83, 0xa5, 0xdd, 0x6f, 0xad, 0xc9, 0x5f, 0x1c,
	0x6c, 0xf6, 0x3d, 0x76, 0x62, 0x3c, 0x5d, 0xc3, 0x9f, 0xfe, 0xe6, 0xeb, 0x37, 0x45, 0xed, 0xbb,
	0xaf, 0xdf, 0x14, 0xc7, 0x06, 0xcb, 0xdd, 0xe5, 0xce, 0xf2, 0x1f, 0xbe, 0x7e, 0x53, 0xbc, 0xbd,
	0x38, 0xd0, 0x57, 0xa8, 0xdd, 0x75, 0xf4, 0x81, 0x6d, 0x35, 0xf4, 0xc5, 0xae, 0xbe, 0x22, 0xa6,
	0x90, 0x0d, 0x7d, 0xb1, 0xa3, 0xaf, 0x84, 0x3d, 0x5c, 0x03, 0xfd, 0xae, 0x06, 0xd5, 0xf4, 0x63,
	0x2a, 0x8a, 0x9f, 0x0e, 0x32, 0xde, 0x59, 0xcf, 0x83, 0xa6, 0xf0, 0x96, 0x68, 0xf6, 0x61, 0x32,
	0xf1, 0xf0, 0x89, 0x66, 0x23, 0x24, 0xe9, 0xb7, 0xd8, 0x7a, 0x3d, 0x8f, 0x45, 0x3d, 0x3c, 0xc7,
	0x41, 0x14, 0x39, 0x88, 0xc2, 0x40, 0x00, 0x98, 0x4c, 0x00, 0x40, 0xdf, 0x86, 0xe9, 0xd4, 0x73,
	0x28, 0xba, 0x1a, 0x6d, 0x37, 0xfc, 0x50, 0x7a, 0x86, 0xca, 0x5f, 0xe3, 0xd2, 0x46, 0xb8, 0xb4,
	0x91, 0xc1, 0x32, 0x15, 0xf2, 0xae, 0xa7, 0x14, 0xa6, 0xfa, 0x8a, 0x98, 0x74, 0xeb, 0x94, 0x99,
	0xac, 0x81, 0x7a, 0x30, 0x9d, 0x7a, 0x92, 0x54, 0xe4, 0x0f, 0x3f, 0x82, 0xd6, 0xaf, 0xe5, 0x33,
	0x43, 0x6d, 0x47, 0xf3, 0xb5, 0xf5, 0x61, 0x66, 0xe8, 0x61, 0x11, 0x5d, 0x4f, 0x6f, 0x99, 0x78,
	0xce, 0xac, 0xcf, 0x9d, 0xc6, 0x0e, 0x65, 0x8e, 0x9d, 0x47, 0xa6, 0xf2, 0x4b, 0x85, 0x21, 0x99,
	0x89, 0x27, 0xc9, 0x61, 0x99, 0xc9, 0x77, 0x45, 0x29, 0x73, 0x3c, 0x5f, 0xe6, 0x77, 0x35, 0xd1,
	0x7d, 0x65, 0x3f, 0x95, 0xa1, 0x5b, 0xea, 0xee, 0xb9, 0x4f, 0x74, 0xf5, 0xdb, 0xe7, 0x59, 0x16,
	0x82, 0x29, 0xe5, 0x83, 0xf9, 0xa1, 0x9c, 0xb5, 0x9e, 0xf6, 0x3e, 0x85, 0x3e, 0xce, 0x56, 0x38,
	0xf3, 0x41, 0xad, 0x7e, 0xef, 0xfc, 0x8b, 0xa9, 0x87, 0x3f, 0xe1, 0xf0, 0xca, 0x09, 0x9f, 0xd4,
	0x87, 0x7d, 0x52, 0x3e, 0x7f, 0xe9, 0xf2, 0x65, 0xac, 0x81, 0x7e, 0xac, 0x81, 0x7e, 0xd6, 0x33,
	0x13, 0xba, 0x97, 0xe5, 0x8b, 0x79, 0xef, 0x5d, 0xf5, 0xc5, 0x0b, 0xac, 0xa6, 0x1e, 0xfe, 0x59,
	0x0e, 0x1b, 0x02, 0xd8, 0x32, 0x77, 0xdc, 0x48, 0xc1, 0xee, 0xe8, 0x2b, 0xc1, 0x96, 0x7a, 0x9c,
	0x38, 0xfe, 0x44, 0x83, 0x0f, 0x72, 0x1e, 0x88, 0xd0, 0x8d, 0xf8, 0x3e, 0x94, 0xfb, 0x5a, 0x55,
	0xbf, 0x79, 0xf6, 0xa2, 0x10, 0x5e, 0x25, 0x61, 0x55, 0x01, 0x2f, 0x04, 0x34, 0xb0, 0xad, 0xaf,
	0x4b, 0xcb, 0x2a, 0x94, 0x07, 0x0d, 0xf4, 0x97, 0x1a, 0x7c, 0x78, 0xe6, 0xeb, 0x00, 0x4a, 0xd8,
	0xea, 0xcc, 0x97, 0x84, 0x7a, 0x3d, 0x73, 0x00, 0x2e, 0x8f, 0xbf, 0xc9, 0x81, 0x5a, 0x22, 0x0b,
	0x1f, 0x2e, 0xd3, 0x65, 0x47, 0x40, 0x7d, 0xb8, 0x78, 0x98, 0x61, 0x3b, 0xe9, 0x08, 0x89, 0x41,
	0x7e, 0x43, 0x5f, 0x74, 0xf4, 0x15, 0x31, 0xa9, 0x6f, 0xa0, 0x1f, 0x68, 0xe2, 0x51, 0x3b, 0xf7,
	0xd1, 0x00, 0x2d, 0x9c, 0x0d, 0x5b, 0xbe, 0x2d, 0x9c, 0x8a, 0x78, 0x85, 0x23, 0x26, 0x41, 0xdd,
	0x08, 0x11, 0xdf, 0xe1, 0xc6, 0x8d, 0xbd, 0x35, 0x1f, 0xe4, 0x77, 0x34, 0x98, 0x50, 0x27, 0xe5,
	0x28, 0x7e, 0xe1, 0x49, 0x0d, 0xee, 0xeb, 0xb3, 0x39, 0x1c, 0xea, 0xe1, 0x06, 0xc7, 0xb0, 0x1f,
	0x60, 0x60, 0xcb, 0x5d, 0x81, 0xe1, 0x23, 0x8e, 0xc1, 0xf5, 0x24, 0x0a, 0xa6, 0xaf, 0x28, 0xf2,
	0xbb, 0x91, 0x39, 0x0d, 0xdb, 0xd2, 0x1b, 0xe8, 0xd7, 0xa0, 0xa2, 0x0c, 0x00, 0xd0, 0x07, 0x91,
	0xa4, 0xe4, 0x00, 0xa4, 0x5e, 0xcb, 0x66, 0x50, 0x0f, 0x2f, 0x73, 0x04, 0x5f, 0xc6, 0xc5, 0x5c,
	0x9a, 0xe1, 0x66, 0x6c, 0x06, 0x45, 0xe4, 0x20, 0xb4, 0x81, 0x18, 0xec, 0x34, 0xd0, 0x1f, 0x69,
	0x22, 0xe7, 0x26, 0x67, 0xa9, 0xc9, 0x9c, 0x3b, 0x34, 0x2a, 0x4e, 0xe6, 0xdc, 0xe1, 0x31, 0x2c,
	0x5e, 0xe5, 0x80, 0xfe, 0x56, 0x8b, 0x6c, 0x72, 0x28, 0x00, 0x7d, 0x1c, 0x03, 0x62, 0xfa, 0x8a,
	0x3a, 0xeb, 0x6c, 0xe8, 0xdc, 0xc9, 0x94, 0xe1, 0x71, 0x03, 0xf5, 0x45, 0xb1, 0x53, 0xa7, 0x61,
	0xc9, 0x62, 0x97, 0x1a, 0xc1, 0x25, 0x8b, 0x5d, 0x7a, 0x88, 0x86, 0xe7, 0x39, 0xa0, 0xbf, 0xd3,
	0xd2, 0x89, 0x37, 0xec, 0x83, 0x1b, 0xe8, 0x28, 0xfa, 0x19, 0x91, 0xd2, 0xcf, 0xa0, 0xa1, 0xda,
	0x92, 0xec, 0xf8, 0xea, 0xf3, 0xa7, 0xf2, 0xa9, 0x87, 0xeb, 0x5c, 0xee, 0xdf, 0xab, 0x72, 0x4b,
	0xa1, 0x11, 0xd0, 0x2b, 0x98, 0x4c, 0xcc, 0xce, 0x94, 0x6b, 0x4b, 0x7a, 0xa8, 0xa7, 0xc4, 0xc0,
	0xd0, 0xb8, 0x0d, 0xdf, 0xe7, 0x32, 0xfe, 0x41, 0xc8, 0x18, 0xe9, 0x2e, 0x33, 0x21, 0xe5, 0xea,
	0xf0, 0x91, 0xb3, 0xe8, 0xc8, 0x7f, 0xac, 0xc1, 0xd5, 0x53, 0xba, 0x38, 0x74, 0x27, 0x12, 0x76,
	0x7a, 0xc3, 0x5b, 0x5f, 0x38, 0xdf, 0x42, 0xea, 0xe1, 0x4d, 0x8e, 0xf1, 0x1f, 0x63, 0x87, 0x38,
	0x16, 0x28, 0xef, 0x73, 0x5b, 0xc4, 0x3d, 0x51, 0x10, 0x29, 0xe1, 0x00, 0xa0, 0xa1, 0x2f, 0x1e,
	0xeb, 0x2b, 0xb2, 0x9d, 0xd1, 0x85, 0x97, 0xa0, 0x1f, 0x69, 0xf0, 0x7e, 0xe6, 0xd8, 0x0c, 0x7d,
	0x18, 0xc7, 0x67, 0xce, 0x00, 0xb1, 0x8e, 0xcf, 0x5a, 0x42, 0x3d, 0xbc, 0xcb, 0x71, 0xfe, 0x93,
	0xc4, 0xc9, 0x51, 0xca, 0x60, 0xfe, 0x06, 0xc7, 0x15, 0x7f, 0xa4, 0xb7, 0xa5, 0xcb, 0x1e, 0x27,
	0x88, 0xbc, 0x7f, 0xd4, 0x53, 0xd1, 0x26, 0x8b, 0xfa, 0x5f, 0x68, 0x50, 0xcf, 0x9f, 0x9c, 0xa1,
	0xdb, 0x19, 0xb8, 0x32, 0xa6, 0x7a, 0xf5, 0x3b, 0xe7, 0x5a, 0x47, 0x3d, 0xfc, 0x19, 0x57, 0xe2,
	0x9f, 0xa5, 0x43, 0x84, 0xa6, 0xbe, 0x77, 0x11, 0x15, 0xd0, 0xf7, 0xb4, 0x68, 0x20, 0x9f, 0x9c,
	0x48, 0x2b, 0x76, 0xce, 0x1b, 0x86, 0x2b, 0x76, 0xce, 0x1d, 0x6a, 0xe3, 0x45, 0x0e, 0xf1, 0x27,
	0x5a, 0x50, 0x13, 0xa5, 0x95, 0x67, 0x73, 0xf3, 0x95, 0x28, 0xd4, 0x39, 0x4f, 0x1e, 0x4a, 0xa1,
	0xce, 0x7f, 0x82, 0x51, 0x0a, 0xf5, 0x29, 0x2f, 0x27, 0xb2, 0x50, 0xff, 0x4b, 0x18, 0x49, 0xbe,
	0x40, 0x85, 0x87, 0x23, 0xc9, 0x4f, 0x25, 0x2f, 0xf4, 0xcb, 0x22, 0x57, 0xa9, 0xd3, 0xa5, 0x64,
	0xae, 0x4a, 0x4d, 0xa3, 0x92, 0xb9, 0x2a, 0x3d, 0x94, 0xc2, 0x55, 0x8e, 0xe2, 0xcf, 0x44, 0x33,
	0x74, 0x89, 0x23, 0xb8, 0x84, 0x8e, 0xc5, 0xa3, 0x45, 0xc6, 0x9b, 0x14, 0xc2, 0x19, 0x7a, 0xa5,
	0x1e, 0xad, 0xce, 0x4c, 0xd6, 0x22, 0x47, 0xfd, 0x79, 0x21, 0x2b, 0x47, 0xd5, 0xc7, 0x7e, 0xe7,
	0xf5, 0x9b, 0xe2, 0x0f, 0x5f, 0xad, 0x55, 0xbf, 0xfc, 0x6a, 0x4e, 0xfb, 0xc9, 0x57, 0x73, 0xda,
	0x7f, 0x7e, 0x35, 0xa7, 0xfd, 0xc1, 0x7f, 0xcd, 0x5d, 0xfa, 0xbf, 0x00, 0x00, 0x00, 0xff, 0xff,
	0x92, 0x31, 0xc7, 0x4b, 0xff, 0x2e, 0x00, 0x00,
}
