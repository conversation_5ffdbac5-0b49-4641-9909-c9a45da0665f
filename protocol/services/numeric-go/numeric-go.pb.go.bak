// Code generated by protoc-gen-gogo.
// source: numeric-go/numeric-go.proto
// DO NOT EDIT!

/*
	Package numeric_go is a generated protocol buffer package.

	It is generated from these files:
		numeric-go/numeric-go.proto

	It has these top-level messages:
		GetGuildNumericListReq
		GuildMemerNumeric
		GetGuildNumericListResp
		GetPersonalNumericReq
		GetPersonalNumericResp
		BatchGetPersonalNumericReq
		PersonalNumeric
		BatchGetPersonalNumericResp
		RecordSendGiftEventReq
		RecordSendGiftEventResp
		UserGiftEventInfo
		BatchRecordSendGiftEventReq
		BatchRecordSendGiftEventResp
		RecordConsumeEventReq
		RecordConsumeEventResp
		NotifyGuildChangeReq
		GetGuildGiftTotalValueReq
		GetGuildGiftTotalValueResp
		GetPersonalRankingReq
		GetPersonalRankingResp
		GetRankListReq
		GetRankListResp
		AddUserNumericReq
		AddUserNumericResp
		GetUserRichSwitchReq
		GetUserRichSwitchResp
		SetUserRichSwitchReq
		SetUserRichSwitchResp
*/
package numeric_go

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import tlvpickle "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// 记录 用户消费行为
// 导致更新Numeric 只可能引起公会土豪魅力值变化
type CONSUME_TYPE int32

const (
	CONSUME_TYPE_ENUM_CONSUME_TT_GAME   CONSUME_TYPE = 1
	CONSUME_TYPE_ENUM_CONSUME_HAPPYCITY CONSUME_TYPE = 2
)

var CONSUME_TYPE_name = map[int32]string{
	1: "ENUM_CONSUME_TT_GAME",
	2: "ENUM_CONSUME_HAPPYCITY",
}
var CONSUME_TYPE_value = map[string]int32{
	"ENUM_CONSUME_TT_GAME":   1,
	"ENUM_CONSUME_HAPPYCITY": 2,
}

func (x CONSUME_TYPE) Enum() *CONSUME_TYPE {
	p := new(CONSUME_TYPE)
	*p = x
	return p
}
func (x CONSUME_TYPE) String() string {
	return proto.EnumName(CONSUME_TYPE_name, int32(x))
}
func (x *CONSUME_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(CONSUME_TYPE_value, data, "CONSUME_TYPE")
	if err != nil {
		return err
	}
	*x = CONSUME_TYPE(value)
	return nil
}
func (CONSUME_TYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorNumericGo, []int{0} }

type AddUserNumericType int32

const (
	AddUserNumericType_ENUM_ADD_TYPE_INVALID    AddUserNumericType = 0
	AddUserNumericType_ENUM_ADD_TYPE_LIVE_TO_TT AddUserNumericType = 1
)

var AddUserNumericType_name = map[int32]string{
	0: "ENUM_ADD_TYPE_INVALID",
	1: "ENUM_ADD_TYPE_LIVE_TO_TT",
}
var AddUserNumericType_value = map[string]int32{
	"ENUM_ADD_TYPE_INVALID":    0,
	"ENUM_ADD_TYPE_LIVE_TO_TT": 1,
}

func (x AddUserNumericType) Enum() *AddUserNumericType {
	p := new(AddUserNumericType)
	*p = x
	return p
}
func (x AddUserNumericType) String() string {
	return proto.EnumName(AddUserNumericType_name, int32(x))
}
func (x *AddUserNumericType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(AddUserNumericType_value, data, "AddUserNumericType")
	if err != nil {
		return err
	}
	*x = AddUserNumericType(value)
	return nil
}
func (AddUserNumericType) EnumDescriptor() ([]byte, []int) { return fileDescriptorNumericGo, []int{1} }

type GetRankListReq_RANK_TYPE int32

const (
	GetRankListReq_WEEK_RICH   GetRankListReq_RANK_TYPE = 1
	GetRankListReq_DAY_RICH    GetRankListReq_RANK_TYPE = 2
	GetRankListReq_WEEK_CHARM  GetRankListReq_RANK_TYPE = 3
	GetRankListReq_DAY_CHARM   GetRankListReq_RANK_TYPE = 4
	GetRankListReq_MONTH_RICH  GetRankListReq_RANK_TYPE = 5
	GetRankListReq_MONTH_CHARM GetRankListReq_RANK_TYPE = 6
)

var GetRankListReq_RANK_TYPE_name = map[int32]string{
	1: "WEEK_RICH",
	2: "DAY_RICH",
	3: "WEEK_CHARM",
	4: "DAY_CHARM",
	5: "MONTH_RICH",
	6: "MONTH_CHARM",
}
var GetRankListReq_RANK_TYPE_value = map[string]int32{
	"WEEK_RICH":   1,
	"DAY_RICH":    2,
	"WEEK_CHARM":  3,
	"DAY_CHARM":   4,
	"MONTH_RICH":  5,
	"MONTH_CHARM": 6,
}

func (x GetRankListReq_RANK_TYPE) Enum() *GetRankListReq_RANK_TYPE {
	p := new(GetRankListReq_RANK_TYPE)
	*p = x
	return p
}
func (x GetRankListReq_RANK_TYPE) String() string {
	return proto.EnumName(GetRankListReq_RANK_TYPE_name, int32(x))
}
func (x *GetRankListReq_RANK_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(GetRankListReq_RANK_TYPE_value, data, "GetRankListReq_RANK_TYPE")
	if err != nil {
		return err
	}
	*x = GetRankListReq_RANK_TYPE(value)
	return nil
}
func (GetRankListReq_RANK_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorNumericGo, []int{20, 0}
}

type GetGuildNumericListReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	Index   uint32 `protobuf:"varint,2,req,name=index" json:"index"`
	Count   uint32 `protobuf:"varint,3,req,name=count" json:"count"`
}

func (m *GetGuildNumericListReq) Reset()                    { *m = GetGuildNumericListReq{} }
func (m *GetGuildNumericListReq) String() string            { return proto.CompactTextString(m) }
func (*GetGuildNumericListReq) ProtoMessage()               {}
func (*GetGuildNumericListReq) Descriptor() ([]byte, []int) { return fileDescriptorNumericGo, []int{0} }

func (m *GetGuildNumericListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildNumericListReq) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *GetGuildNumericListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GuildMemerNumeric struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Numeric   uint32 `protobuf:"varint,2,req,name=numeric" json:"numeric"`
	Numeric64 uint64 `protobuf:"varint,3,opt,name=numeric64" json:"numeric64"`
}

func (m *GuildMemerNumeric) Reset()                    { *m = GuildMemerNumeric{} }
func (m *GuildMemerNumeric) String() string            { return proto.CompactTextString(m) }
func (*GuildMemerNumeric) ProtoMessage()               {}
func (*GuildMemerNumeric) Descriptor() ([]byte, []int) { return fileDescriptorNumericGo, []int{1} }

func (m *GuildMemerNumeric) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GuildMemerNumeric) GetNumeric() uint32 {
	if m != nil {
		return m.Numeric
	}
	return 0
}

func (m *GuildMemerNumeric) GetNumeric64() uint64 {
	if m != nil {
		return m.Numeric64
	}
	return 0
}

type GetGuildNumericListResp struct {
	NumericList []*GuildMemerNumeric `protobuf:"bytes,1,rep,name=numeric_list,json=numericList" json:"numeric_list,omitempty"`
}

func (m *GetGuildNumericListResp) Reset()                    { *m = GetGuildNumericListResp{} }
func (m *GetGuildNumericListResp) String() string            { return proto.CompactTextString(m) }
func (*GetGuildNumericListResp) ProtoMessage()               {}
func (*GetGuildNumericListResp) Descriptor() ([]byte, []int) { return fileDescriptorNumericGo, []int{2} }

func (m *GetGuildNumericListResp) GetNumericList() []*GuildMemerNumeric {
	if m != nil {
		return m.NumericList
	}
	return nil
}

type GetPersonalNumericReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetPersonalNumericReq) Reset()                    { *m = GetPersonalNumericReq{} }
func (m *GetPersonalNumericReq) String() string            { return proto.CompactTextString(m) }
func (*GetPersonalNumericReq) ProtoMessage()               {}
func (*GetPersonalNumericReq) Descriptor() ([]byte, []int) { return fileDescriptorNumericGo, []int{3} }

func (m *GetPersonalNumericReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetPersonalNumericResp struct {
	ConsumeNumeric   uint32 `protobuf:"varint,1,req,name=consume_numeric,json=consumeNumeric" json:"consume_numeric"`
	CharmNumeric     uint32 `protobuf:"varint,2,req,name=charm_numeric,json=charmNumeric" json:"charm_numeric"`
	ConsumeNumeric64 uint64 `protobuf:"varint,3,opt,name=consume_numeric64,json=consumeNumeric64" json:"consume_numeric64"`
	CharmNumeric64   uint64 `protobuf:"varint,4,opt,name=charm_numeric64,json=charmNumeric64" json:"charm_numeric64"`
}

func (m *GetPersonalNumericResp) Reset()                    { *m = GetPersonalNumericResp{} }
func (m *GetPersonalNumericResp) String() string            { return proto.CompactTextString(m) }
func (*GetPersonalNumericResp) ProtoMessage()               {}
func (*GetPersonalNumericResp) Descriptor() ([]byte, []int) { return fileDescriptorNumericGo, []int{4} }

func (m *GetPersonalNumericResp) GetConsumeNumeric() uint32 {
	if m != nil {
		return m.ConsumeNumeric
	}
	return 0
}

func (m *GetPersonalNumericResp) GetCharmNumeric() uint32 {
	if m != nil {
		return m.CharmNumeric
	}
	return 0
}

func (m *GetPersonalNumericResp) GetConsumeNumeric64() uint64 {
	if m != nil {
		return m.ConsumeNumeric64
	}
	return 0
}

func (m *GetPersonalNumericResp) GetCharmNumeric64() uint64 {
	if m != nil {
		return m.CharmNumeric64
	}
	return 0
}

type BatchGetPersonalNumericReq struct {
	UidList []uint32 `protobuf:"varint,1,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *BatchGetPersonalNumericReq) Reset()         { *m = BatchGetPersonalNumericReq{} }
func (m *BatchGetPersonalNumericReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetPersonalNumericReq) ProtoMessage()    {}
func (*BatchGetPersonalNumericReq) Descriptor() ([]byte, []int) {
	return fileDescriptorNumericGo, []int{5}
}

func (m *BatchGetPersonalNumericReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type PersonalNumeric struct {
	Uid     uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Charm   uint32 `protobuf:"varint,2,req,name=charm" json:"charm"`
	Rich    uint32 `protobuf:"varint,3,req,name=rich" json:"rich"`
	Charm64 uint64 `protobuf:"varint,4,opt,name=charm64" json:"charm64"`
	Rich64  uint64 `protobuf:"varint,5,opt,name=rich64" json:"rich64"`
}

func (m *PersonalNumeric) Reset()                    { *m = PersonalNumeric{} }
func (m *PersonalNumeric) String() string            { return proto.CompactTextString(m) }
func (*PersonalNumeric) ProtoMessage()               {}
func (*PersonalNumeric) Descriptor() ([]byte, []int) { return fileDescriptorNumericGo, []int{6} }

func (m *PersonalNumeric) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PersonalNumeric) GetCharm() uint32 {
	if m != nil {
		return m.Charm
	}
	return 0
}

func (m *PersonalNumeric) GetRich() uint32 {
	if m != nil {
		return m.Rich
	}
	return 0
}

func (m *PersonalNumeric) GetCharm64() uint64 {
	if m != nil {
		return m.Charm64
	}
	return 0
}

func (m *PersonalNumeric) GetRich64() uint64 {
	if m != nil {
		return m.Rich64
	}
	return 0
}

type BatchGetPersonalNumericResp struct {
	NumericList []*PersonalNumeric `protobuf:"bytes,1,rep,name=numeric_list,json=numericList" json:"numeric_list,omitempty"`
}

func (m *BatchGetPersonalNumericResp) Reset()         { *m = BatchGetPersonalNumericResp{} }
func (m *BatchGetPersonalNumericResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetPersonalNumericResp) ProtoMessage()    {}
func (*BatchGetPersonalNumericResp) Descriptor() ([]byte, []int) {
	return fileDescriptorNumericGo, []int{7}
}

func (m *BatchGetPersonalNumericResp) GetNumericList() []*PersonalNumeric {
	if m != nil {
		return m.NumericList
	}
	return nil
}

// 记录 用户送礼物的行为
// 仅记录红钻类型的行为 T豆行为通过MQ消费记录
// 导致更新Numeric 可能引起公会土豪魅力值变化 也可能引起用户的土豪魅力值变化
type RecordSendGiftEventReq struct {
	GiverUid      uint32 `protobuf:"varint,1,req,name=giver_uid,json=giverUid" json:"giver_uid"`
	ReceiverUid   uint32 `protobuf:"varint,2,req,name=receiver_uid,json=receiverUid" json:"receiver_uid"`
	RichValue     uint32 `protobuf:"varint,3,req,name=rich_value,json=richValue" json:"rich_value"`
	GiverGuild    uint32 `protobuf:"varint,4,req,name=giver_guild,json=giverGuild" json:"giver_guild"`
	ReceiverGuild uint32 `protobuf:"varint,5,req,name=receiver_guild,json=receiverGuild" json:"receiver_guild"`
	CharmValue    uint32 `protobuf:"varint,6,req,name=charm_value,json=charmValue" json:"charm_value"`
	OrderId       string `protobuf:"bytes,7,req,name=order_id,json=orderId" json:"order_id"`
	ChannelId     uint32 `protobuf:"varint,8,opt,name=channel_id,json=channelId" json:"channel_id"`
	ChannelGuild  uint32 `protobuf:"varint,9,opt,name=channel_guild,json=channelGuild" json:"channel_guild"`
	PriceType     uint32 `protobuf:"varint,10,opt,name=price_type,json=priceType" json:"price_type"`
	GiftId        uint32 `protobuf:"varint,11,opt,name=gift_id,json=giftId" json:"gift_id"`
}

func (m *RecordSendGiftEventReq) Reset()                    { *m = RecordSendGiftEventReq{} }
func (m *RecordSendGiftEventReq) String() string            { return proto.CompactTextString(m) }
func (*RecordSendGiftEventReq) ProtoMessage()               {}
func (*RecordSendGiftEventReq) Descriptor() ([]byte, []int) { return fileDescriptorNumericGo, []int{8} }

func (m *RecordSendGiftEventReq) GetGiverUid() uint32 {
	if m != nil {
		return m.GiverUid
	}
	return 0
}

func (m *RecordSendGiftEventReq) GetReceiverUid() uint32 {
	if m != nil {
		return m.ReceiverUid
	}
	return 0
}

func (m *RecordSendGiftEventReq) GetRichValue() uint32 {
	if m != nil {
		return m.RichValue
	}
	return 0
}

func (m *RecordSendGiftEventReq) GetGiverGuild() uint32 {
	if m != nil {
		return m.GiverGuild
	}
	return 0
}

func (m *RecordSendGiftEventReq) GetReceiverGuild() uint32 {
	if m != nil {
		return m.ReceiverGuild
	}
	return 0
}

func (m *RecordSendGiftEventReq) GetCharmValue() uint32 {
	if m != nil {
		return m.CharmValue
	}
	return 0
}

func (m *RecordSendGiftEventReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *RecordSendGiftEventReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RecordSendGiftEventReq) GetChannelGuild() uint32 {
	if m != nil {
		return m.ChannelGuild
	}
	return 0
}

func (m *RecordSendGiftEventReq) GetPriceType() uint32 {
	if m != nil {
		return m.PriceType
	}
	return 0
}

func (m *RecordSendGiftEventReq) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

type RecordSendGiftEventResp struct {
	LevelChanageUidList    []uint32 `protobuf:"varint,1,rep,name=level_chanage_uid_list,json=levelChanageUidList" json:"level_chanage_uid_list,omitempty"`
	RealCharm              uint32   `protobuf:"varint,2,opt,name=real_charm,json=realCharm" json:"real_charm"`
	GiverCurrAllRich       uint32   `protobuf:"varint,3,opt,name=giver_curr_all_rich,json=giverCurrAllRich" json:"giver_curr_all_rich"`
	ReceiverCurrAllCharm   uint32   `protobuf:"varint,4,opt,name=receiver_curr_all_charm,json=receiverCurrAllCharm" json:"receiver_curr_all_charm"`
	RealRich               uint32   `protobuf:"varint,5,opt,name=real_rich,json=realRich" json:"real_rich"`
	BeforeRich             uint32   `protobuf:"varint,6,opt,name=before_rich,json=beforeRich" json:"before_rich"`
	BeforeRich64           uint64   `protobuf:"varint,7,opt,name=before_rich64,json=beforeRich64" json:"before_rich64"`
	GiverCurrAllRich64     uint64   `protobuf:"varint,8,opt,name=giver_curr_all_rich64,json=giverCurrAllRich64" json:"giver_curr_all_rich64"`
	ReceiverCurrAllCharm64 uint64   `protobuf:"varint,9,opt,name=receiver_curr_all_charm64,json=receiverCurrAllCharm64" json:"receiver_curr_all_charm64"`
}

func (m *RecordSendGiftEventResp) Reset()                    { *m = RecordSendGiftEventResp{} }
func (m *RecordSendGiftEventResp) String() string            { return proto.CompactTextString(m) }
func (*RecordSendGiftEventResp) ProtoMessage()               {}
func (*RecordSendGiftEventResp) Descriptor() ([]byte, []int) { return fileDescriptorNumericGo, []int{9} }

func (m *RecordSendGiftEventResp) GetLevelChanageUidList() []uint32 {
	if m != nil {
		return m.LevelChanageUidList
	}
	return nil
}

func (m *RecordSendGiftEventResp) GetRealCharm() uint32 {
	if m != nil {
		return m.RealCharm
	}
	return 0
}

func (m *RecordSendGiftEventResp) GetGiverCurrAllRich() uint32 {
	if m != nil {
		return m.GiverCurrAllRich
	}
	return 0
}

func (m *RecordSendGiftEventResp) GetReceiverCurrAllCharm() uint32 {
	if m != nil {
		return m.ReceiverCurrAllCharm
	}
	return 0
}

func (m *RecordSendGiftEventResp) GetRealRich() uint32 {
	if m != nil {
		return m.RealRich
	}
	return 0
}

func (m *RecordSendGiftEventResp) GetBeforeRich() uint32 {
	if m != nil {
		return m.BeforeRich
	}
	return 0
}

func (m *RecordSendGiftEventResp) GetBeforeRich64() uint64 {
	if m != nil {
		return m.BeforeRich64
	}
	return 0
}

func (m *RecordSendGiftEventResp) GetGiverCurrAllRich64() uint64 {
	if m != nil {
		return m.GiverCurrAllRich64
	}
	return 0
}

func (m *RecordSendGiftEventResp) GetReceiverCurrAllCharm64() uint64 {
	if m != nil {
		return m.ReceiverCurrAllCharm64
	}
	return 0
}

type UserGiftEventInfo struct {
	Uid          uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GuildId      uint32 `protobuf:"varint,2,opt,name=guild_id,json=guildId" json:"guild_id"`
	AddValue     uint32 `protobuf:"varint,3,opt,name=add_value,json=addValue" json:"add_value"`
	FinalValue   uint32 `protobuf:"varint,4,opt,name=final_value,json=finalValue" json:"final_value"`
	LevelChange  bool   `protobuf:"varint,5,opt,name=level_change,json=levelChange" json:"level_change"`
	FinalValue64 uint64 `protobuf:"varint,6,opt,name=final_value64,json=finalValue64" json:"final_value64"`
}

func (m *UserGiftEventInfo) Reset()                    { *m = UserGiftEventInfo{} }
func (m *UserGiftEventInfo) String() string            { return proto.CompactTextString(m) }
func (*UserGiftEventInfo) ProtoMessage()               {}
func (*UserGiftEventInfo) Descriptor() ([]byte, []int) { return fileDescriptorNumericGo, []int{10} }

func (m *UserGiftEventInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserGiftEventInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *UserGiftEventInfo) GetAddValue() uint32 {
	if m != nil {
		return m.AddValue
	}
	return 0
}

func (m *UserGiftEventInfo) GetFinalValue() uint32 {
	if m != nil {
		return m.FinalValue
	}
	return 0
}

func (m *UserGiftEventInfo) GetLevelChange() bool {
	if m != nil {
		return m.LevelChange
	}
	return false
}

func (m *UserGiftEventInfo) GetFinalValue64() uint64 {
	if m != nil {
		return m.FinalValue64
	}
	return 0
}

// 记录 用户批量送礼物的行为
// 仅记录红钻类型的行为 T豆行为通过MQ消费记录
// 导致更新Numeric 可能引起公会土豪魅力值变化 也可能引起用户的土豪魅力值变化
type BatchRecordSendGiftEventReq struct {
	GiverUserInfo        *UserGiftEventInfo   `protobuf:"bytes,1,req,name=giver_user_info,json=giverUserInfo" json:"giver_user_info,omitempty"`
	ReceiverUserInfoList []*UserGiftEventInfo `protobuf:"bytes,2,rep,name=receiver_user_info_list,json=receiverUserInfoList" json:"receiver_user_info_list,omitempty"`
	OrderId              string               `protobuf:"bytes,3,req,name=order_id,json=orderId" json:"order_id"`
	ChannelId            uint32               `protobuf:"varint,4,opt,name=channel_id,json=channelId" json:"channel_id"`
	ChannelGuild         uint32               `protobuf:"varint,5,opt,name=channel_guild,json=channelGuild" json:"channel_guild"`
	PriceType            uint32               `protobuf:"varint,6,opt,name=price_type,json=priceType" json:"price_type"`
	GiftId               uint32               `protobuf:"varint,7,opt,name=gift_id,json=giftId" json:"gift_id"`
}

func (m *BatchRecordSendGiftEventReq) Reset()         { *m = BatchRecordSendGiftEventReq{} }
func (m *BatchRecordSendGiftEventReq) String() string { return proto.CompactTextString(m) }
func (*BatchRecordSendGiftEventReq) ProtoMessage()    {}
func (*BatchRecordSendGiftEventReq) Descriptor() ([]byte, []int) {
	return fileDescriptorNumericGo, []int{11}
}

func (m *BatchRecordSendGiftEventReq) GetGiverUserInfo() *UserGiftEventInfo {
	if m != nil {
		return m.GiverUserInfo
	}
	return nil
}

func (m *BatchRecordSendGiftEventReq) GetReceiverUserInfoList() []*UserGiftEventInfo {
	if m != nil {
		return m.ReceiverUserInfoList
	}
	return nil
}

func (m *BatchRecordSendGiftEventReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *BatchRecordSendGiftEventReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BatchRecordSendGiftEventReq) GetChannelGuild() uint32 {
	if m != nil {
		return m.ChannelGuild
	}
	return 0
}

func (m *BatchRecordSendGiftEventReq) GetPriceType() uint32 {
	if m != nil {
		return m.PriceType
	}
	return 0
}

func (m *BatchRecordSendGiftEventReq) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

type BatchRecordSendGiftEventResp struct {
	GiverUserInfo        *UserGiftEventInfo   `protobuf:"bytes,1,opt,name=giver_user_info,json=giverUserInfo" json:"giver_user_info,omitempty"`
	ReceiverUserInfoList []*UserGiftEventInfo `protobuf:"bytes,2,rep,name=receiver_user_info_list,json=receiverUserInfoList" json:"receiver_user_info_list,omitempty"`
}

func (m *BatchRecordSendGiftEventResp) Reset()         { *m = BatchRecordSendGiftEventResp{} }
func (m *BatchRecordSendGiftEventResp) String() string { return proto.CompactTextString(m) }
func (*BatchRecordSendGiftEventResp) ProtoMessage()    {}
func (*BatchRecordSendGiftEventResp) Descriptor() ([]byte, []int) {
	return fileDescriptorNumericGo, []int{12}
}

func (m *BatchRecordSendGiftEventResp) GetGiverUserInfo() *UserGiftEventInfo {
	if m != nil {
		return m.GiverUserInfo
	}
	return nil
}

func (m *BatchRecordSendGiftEventResp) GetReceiverUserInfoList() []*UserGiftEventInfo {
	if m != nil {
		return m.ReceiverUserInfoList
	}
	return nil
}

type RecordConsumeEventReq struct {
	Uid         uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GuildId     uint32 `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	RichValue   uint32 `protobuf:"varint,3,req,name=rich_value,json=richValue" json:"rich_value"`
	ConsumeType uint32 `protobuf:"varint,4,req,name=consume_type,json=consumeType" json:"consume_type"`
	OrderInfo   string `protobuf:"bytes,5,req,name=order_info,json=orderInfo" json:"order_info"`
}

func (m *RecordConsumeEventReq) Reset()                    { *m = RecordConsumeEventReq{} }
func (m *RecordConsumeEventReq) String() string            { return proto.CompactTextString(m) }
func (*RecordConsumeEventReq) ProtoMessage()               {}
func (*RecordConsumeEventReq) Descriptor() ([]byte, []int) { return fileDescriptorNumericGo, []int{13} }

func (m *RecordConsumeEventReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RecordConsumeEventReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *RecordConsumeEventReq) GetRichValue() uint32 {
	if m != nil {
		return m.RichValue
	}
	return 0
}

func (m *RecordConsumeEventReq) GetConsumeType() uint32 {
	if m != nil {
		return m.ConsumeType
	}
	return 0
}

func (m *RecordConsumeEventReq) GetOrderInfo() string {
	if m != nil {
		return m.OrderInfo
	}
	return ""
}

type RecordConsumeEventResp struct {
}

func (m *RecordConsumeEventResp) Reset()                    { *m = RecordConsumeEventResp{} }
func (m *RecordConsumeEventResp) String() string            { return proto.CompactTextString(m) }
func (*RecordConsumeEventResp) ProtoMessage()               {}
func (*RecordConsumeEventResp) Descriptor() ([]byte, []int) { return fileDescriptorNumericGo, []int{14} }

type NotifyGuildChangeReq struct {
	Uid     uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GuildId uint32 `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
}

func (m *NotifyGuildChangeReq) Reset()                    { *m = NotifyGuildChangeReq{} }
func (m *NotifyGuildChangeReq) String() string            { return proto.CompactTextString(m) }
func (*NotifyGuildChangeReq) ProtoMessage()               {}
func (*NotifyGuildChangeReq) Descriptor() ([]byte, []int) { return fileDescriptorNumericGo, []int{15} }

func (m *NotifyGuildChangeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *NotifyGuildChangeReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetGuildGiftTotalValueReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
}

func (m *GetGuildGiftTotalValueReq) Reset()         { *m = GetGuildGiftTotalValueReq{} }
func (m *GetGuildGiftTotalValueReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildGiftTotalValueReq) ProtoMessage()    {}
func (*GetGuildGiftTotalValueReq) Descriptor() ([]byte, []int) {
	return fileDescriptorNumericGo, []int{16}
}

func (m *GetGuildGiftTotalValueReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetGuildGiftTotalValueResp struct {
	GiftValue   uint32 `protobuf:"varint,1,req,name=gift_value,json=giftValue" json:"gift_value"`
	RichValue64 int64  `protobuf:"varint,2,opt,name=rich_value64,json=richValue64" json:"rich_value64"`
	GiftValue64 int64  `protobuf:"varint,3,opt,name=gift_value64,json=giftValue64" json:"gift_value64"`
}

func (m *GetGuildGiftTotalValueResp) Reset()         { *m = GetGuildGiftTotalValueResp{} }
func (m *GetGuildGiftTotalValueResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildGiftTotalValueResp) ProtoMessage()    {}
func (*GetGuildGiftTotalValueResp) Descriptor() ([]byte, []int) {
	return fileDescriptorNumericGo, []int{17}
}

func (m *GetGuildGiftTotalValueResp) GetGiftValue() uint32 {
	if m != nil {
		return m.GiftValue
	}
	return 0
}

func (m *GetGuildGiftTotalValueResp) GetRichValue64() int64 {
	if m != nil {
		return m.RichValue64
	}
	return 0
}

func (m *GetGuildGiftTotalValueResp) GetGiftValue64() int64 {
	if m != nil {
		return m.GiftValue64
	}
	return 0
}

type GetPersonalRankingReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetPersonalRankingReq) Reset()                    { *m = GetPersonalRankingReq{} }
func (m *GetPersonalRankingReq) String() string            { return proto.CompactTextString(m) }
func (*GetPersonalRankingReq) ProtoMessage()               {}
func (*GetPersonalRankingReq) Descriptor() ([]byte, []int) { return fileDescriptorNumericGo, []int{18} }

func (m *GetPersonalRankingReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetPersonalRankingResp struct {
	WeekCharmRanking  uint32 `protobuf:"varint,1,req,name=week_charm_ranking,json=weekCharmRanking" json:"week_charm_ranking"`
	DayCharmRanking   uint32 `protobuf:"varint,2,req,name=day_charm_ranking,json=dayCharmRanking" json:"day_charm_ranking"`
	WeekRichRanking   uint32 `protobuf:"varint,3,req,name=week_rich_ranking,json=weekRichRanking" json:"week_rich_ranking"`
	DayRichRanking    uint32 `protobuf:"varint,4,req,name=day_rich_ranking,json=dayRichRanking" json:"day_rich_ranking"`
	MonthCharmRanking uint32 `protobuf:"varint,5,opt,name=month_charm_ranking,json=monthCharmRanking" json:"month_charm_ranking"`
	MonthRichRanking  uint32 `protobuf:"varint,6,opt,name=month_rich_ranking,json=monthRichRanking" json:"month_rich_ranking"`
}

func (m *GetPersonalRankingResp) Reset()                    { *m = GetPersonalRankingResp{} }
func (m *GetPersonalRankingResp) String() string            { return proto.CompactTextString(m) }
func (*GetPersonalRankingResp) ProtoMessage()               {}
func (*GetPersonalRankingResp) Descriptor() ([]byte, []int) { return fileDescriptorNumericGo, []int{19} }

func (m *GetPersonalRankingResp) GetWeekCharmRanking() uint32 {
	if m != nil {
		return m.WeekCharmRanking
	}
	return 0
}

func (m *GetPersonalRankingResp) GetDayCharmRanking() uint32 {
	if m != nil {
		return m.DayCharmRanking
	}
	return 0
}

func (m *GetPersonalRankingResp) GetWeekRichRanking() uint32 {
	if m != nil {
		return m.WeekRichRanking
	}
	return 0
}

func (m *GetPersonalRankingResp) GetDayRichRanking() uint32 {
	if m != nil {
		return m.DayRichRanking
	}
	return 0
}

func (m *GetPersonalRankingResp) GetMonthCharmRanking() uint32 {
	if m != nil {
		return m.MonthCharmRanking
	}
	return 0
}

func (m *GetPersonalRankingResp) GetMonthRichRanking() uint32 {
	if m != nil {
		return m.MonthRichRanking
	}
	return 0
}

type GetRankListReq struct {
	Index    uint32 `protobuf:"varint,1,req,name=index" json:"index"`
	Count    uint32 `protobuf:"varint,2,req,name=count" json:"count"`
	RankType uint32 `protobuf:"varint,3,req,name=rank_type,json=rankType" json:"rank_type"`
	Uid      uint32 `protobuf:"varint,4,opt,name=uid" json:"uid"`
}

func (m *GetRankListReq) Reset()                    { *m = GetRankListReq{} }
func (m *GetRankListReq) String() string            { return proto.CompactTextString(m) }
func (*GetRankListReq) ProtoMessage()               {}
func (*GetRankListReq) Descriptor() ([]byte, []int) { return fileDescriptorNumericGo, []int{20} }

func (m *GetRankListReq) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *GetRankListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetRankListReq) GetRankType() uint32 {
	if m != nil {
		return m.RankType
	}
	return 0
}

func (m *GetRankListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetRankListResp struct {
	RankList      []uint32 `protobuf:"varint,1,rep,name=rank_list,json=rankList" json:"rank_list,omitempty"`
	RankLast      uint32   `protobuf:"varint,2,opt,name=rank_last,json=rankLast" json:"rank_last"`
	RankNow       uint32   `protobuf:"varint,3,opt,name=rank_now,json=rankNow" json:"rank_now"`
	GapToRiseRank uint32   `protobuf:"varint,4,opt,name=gap_to_rise_rank,json=gapToRiseRank" json:"gap_to_rise_rank"`
}

func (m *GetRankListResp) Reset()                    { *m = GetRankListResp{} }
func (m *GetRankListResp) String() string            { return proto.CompactTextString(m) }
func (*GetRankListResp) ProtoMessage()               {}
func (*GetRankListResp) Descriptor() ([]byte, []int) { return fileDescriptorNumericGo, []int{21} }

func (m *GetRankListResp) GetRankList() []uint32 {
	if m != nil {
		return m.RankList
	}
	return nil
}

func (m *GetRankListResp) GetRankLast() uint32 {
	if m != nil {
		return m.RankLast
	}
	return 0
}

func (m *GetRankListResp) GetRankNow() uint32 {
	if m != nil {
		return m.RankNow
	}
	return 0
}

func (m *GetRankListResp) GetGapToRiseRank() uint32 {
	if m != nil {
		return m.GapToRiseRank
	}
	return 0
}

type AddUserNumericReq struct {
	Uid        uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	AddType    uint32 `protobuf:"varint,2,req,name=add_type,json=addType" json:"add_type"`
	RichValue  uint64 `protobuf:"varint,3,opt,name=rich_value,json=richValue" json:"rich_value"`
	CharmValue uint64 `protobuf:"varint,4,opt,name=charm_value,json=charmValue" json:"charm_value"`
}

func (m *AddUserNumericReq) Reset()                    { *m = AddUserNumericReq{} }
func (m *AddUserNumericReq) String() string            { return proto.CompactTextString(m) }
func (*AddUserNumericReq) ProtoMessage()               {}
func (*AddUserNumericReq) Descriptor() ([]byte, []int) { return fileDescriptorNumericGo, []int{22} }

func (m *AddUserNumericReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddUserNumericReq) GetAddType() uint32 {
	if m != nil {
		return m.AddType
	}
	return 0
}

func (m *AddUserNumericReq) GetRichValue() uint64 {
	if m != nil {
		return m.RichValue
	}
	return 0
}

func (m *AddUserNumericReq) GetCharmValue() uint64 {
	if m != nil {
		return m.CharmValue
	}
	return 0
}

type AddUserNumericResp struct {
	FinalRichValue  uint64 `protobuf:"varint,1,req,name=final_rich_value,json=finalRichValue" json:"final_rich_value"`
	FinalCharmValue uint64 `protobuf:"varint,2,req,name=final_charm_value,json=finalCharmValue" json:"final_charm_value"`
}

func (m *AddUserNumericResp) Reset()                    { *m = AddUserNumericResp{} }
func (m *AddUserNumericResp) String() string            { return proto.CompactTextString(m) }
func (*AddUserNumericResp) ProtoMessage()               {}
func (*AddUserNumericResp) Descriptor() ([]byte, []int) { return fileDescriptorNumericGo, []int{23} }

func (m *AddUserNumericResp) GetFinalRichValue() uint64 {
	if m != nil {
		return m.FinalRichValue
	}
	return 0
}

func (m *AddUserNumericResp) GetFinalCharmValue() uint64 {
	if m != nil {
		return m.FinalCharmValue
	}
	return 0
}

type GetUserRichSwitchReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetUserRichSwitchReq) Reset()                    { *m = GetUserRichSwitchReq{} }
func (m *GetUserRichSwitchReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserRichSwitchReq) ProtoMessage()               {}
func (*GetUserRichSwitchReq) Descriptor() ([]byte, []int) { return fileDescriptorNumericGo, []int{24} }

func (m *GetUserRichSwitchReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserRichSwitchResp struct {
	Enable bool `protobuf:"varint,1,req,name=enable" json:"enable"`
}

func (m *GetUserRichSwitchResp) Reset()                    { *m = GetUserRichSwitchResp{} }
func (m *GetUserRichSwitchResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserRichSwitchResp) ProtoMessage()               {}
func (*GetUserRichSwitchResp) Descriptor() ([]byte, []int) { return fileDescriptorNumericGo, []int{25} }

func (m *GetUserRichSwitchResp) GetEnable() bool {
	if m != nil {
		return m.Enable
	}
	return false
}

type SetUserRichSwitchReq struct {
	Uid    uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Enable bool   `protobuf:"varint,2,req,name=enable" json:"enable"`
}

func (m *SetUserRichSwitchReq) Reset()                    { *m = SetUserRichSwitchReq{} }
func (m *SetUserRichSwitchReq) String() string            { return proto.CompactTextString(m) }
func (*SetUserRichSwitchReq) ProtoMessage()               {}
func (*SetUserRichSwitchReq) Descriptor() ([]byte, []int) { return fileDescriptorNumericGo, []int{26} }

func (m *SetUserRichSwitchReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserRichSwitchReq) GetEnable() bool {
	if m != nil {
		return m.Enable
	}
	return false
}

type SetUserRichSwitchResp struct {
}

func (m *SetUserRichSwitchResp) Reset()                    { *m = SetUserRichSwitchResp{} }
func (m *SetUserRichSwitchResp) String() string            { return proto.CompactTextString(m) }
func (*SetUserRichSwitchResp) ProtoMessage()               {}
func (*SetUserRichSwitchResp) Descriptor() ([]byte, []int) { return fileDescriptorNumericGo, []int{27} }

func init() {
	proto.RegisterType((*GetGuildNumericListReq)(nil), "numeric_go.GetGuildNumericListReq")
	proto.RegisterType((*GuildMemerNumeric)(nil), "numeric_go.GuildMemerNumeric")
	proto.RegisterType((*GetGuildNumericListResp)(nil), "numeric_go.GetGuildNumericListResp")
	proto.RegisterType((*GetPersonalNumericReq)(nil), "numeric_go.GetPersonalNumericReq")
	proto.RegisterType((*GetPersonalNumericResp)(nil), "numeric_go.GetPersonalNumericResp")
	proto.RegisterType((*BatchGetPersonalNumericReq)(nil), "numeric_go.BatchGetPersonalNumericReq")
	proto.RegisterType((*PersonalNumeric)(nil), "numeric_go.PersonalNumeric")
	proto.RegisterType((*BatchGetPersonalNumericResp)(nil), "numeric_go.BatchGetPersonalNumericResp")
	proto.RegisterType((*RecordSendGiftEventReq)(nil), "numeric_go.RecordSendGiftEventReq")
	proto.RegisterType((*RecordSendGiftEventResp)(nil), "numeric_go.RecordSendGiftEventResp")
	proto.RegisterType((*UserGiftEventInfo)(nil), "numeric_go.UserGiftEventInfo")
	proto.RegisterType((*BatchRecordSendGiftEventReq)(nil), "numeric_go.BatchRecordSendGiftEventReq")
	proto.RegisterType((*BatchRecordSendGiftEventResp)(nil), "numeric_go.BatchRecordSendGiftEventResp")
	proto.RegisterType((*RecordConsumeEventReq)(nil), "numeric_go.RecordConsumeEventReq")
	proto.RegisterType((*RecordConsumeEventResp)(nil), "numeric_go.RecordConsumeEventResp")
	proto.RegisterType((*NotifyGuildChangeReq)(nil), "numeric_go.NotifyGuildChangeReq")
	proto.RegisterType((*GetGuildGiftTotalValueReq)(nil), "numeric_go.GetGuildGiftTotalValueReq")
	proto.RegisterType((*GetGuildGiftTotalValueResp)(nil), "numeric_go.GetGuildGiftTotalValueResp")
	proto.RegisterType((*GetPersonalRankingReq)(nil), "numeric_go.GetPersonalRankingReq")
	proto.RegisterType((*GetPersonalRankingResp)(nil), "numeric_go.GetPersonalRankingResp")
	proto.RegisterType((*GetRankListReq)(nil), "numeric_go.GetRankListReq")
	proto.RegisterType((*GetRankListResp)(nil), "numeric_go.GetRankListResp")
	proto.RegisterType((*AddUserNumericReq)(nil), "numeric_go.AddUserNumericReq")
	proto.RegisterType((*AddUserNumericResp)(nil), "numeric_go.AddUserNumericResp")
	proto.RegisterType((*GetUserRichSwitchReq)(nil), "numeric_go.GetUserRichSwitchReq")
	proto.RegisterType((*GetUserRichSwitchResp)(nil), "numeric_go.GetUserRichSwitchResp")
	proto.RegisterType((*SetUserRichSwitchReq)(nil), "numeric_go.SetUserRichSwitchReq")
	proto.RegisterType((*SetUserRichSwitchResp)(nil), "numeric_go.SetUserRichSwitchResp")
	proto.RegisterEnum("numeric_go.CONSUME_TYPE", CONSUME_TYPE_name, CONSUME_TYPE_value)
	proto.RegisterEnum("numeric_go.AddUserNumericType", AddUserNumericType_name, AddUserNumericType_value)
	proto.RegisterEnum("numeric_go.GetRankListReq_RANK_TYPE", GetRankListReq_RANK_TYPE_name, GetRankListReq_RANK_TYPE_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for NumericGo service

type NumericGoClient interface {
	GetGuildConsumeList(ctx context.Context, in *GetGuildNumericListReq, opts ...grpc.CallOption) (*GetGuildNumericListResp, error)
	GetGuildCharmList(ctx context.Context, in *GetGuildNumericListReq, opts ...grpc.CallOption) (*GetGuildNumericListResp, error)
	GetPersonalNumeric(ctx context.Context, in *GetPersonalNumericReq, opts ...grpc.CallOption) (*GetPersonalNumericResp, error)
	// 由送礼物 导致更新Numeric
	RecordSendGiftEvent(ctx context.Context, in *RecordSendGiftEventReq, opts ...grpc.CallOption) (*RecordSendGiftEventResp, error)
	NotifyGuildQuit(ctx context.Context, in *NotifyGuildChangeReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	NotifyGuildJoin(ctx context.Context, in *NotifyGuildChangeReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGuildGiftTotalValue(ctx context.Context, in *GetGuildGiftTotalValueReq, opts ...grpc.CallOption) (*GetGuildGiftTotalValueResp, error)
	// 由现金消费 导致更新Numeric
	RecordConsumeEvent(ctx context.Context, in *RecordConsumeEventReq, opts ...grpc.CallOption) (*RecordConsumeEventResp, error)
	GetPersonalRanking(ctx context.Context, in *GetPersonalRankingReq, opts ...grpc.CallOption) (*GetPersonalRankingResp, error)
	GetRankList(ctx context.Context, in *GetRankListReq, opts ...grpc.CallOption) (*GetRankListResp, error)
	BatchGetPersonalNumeric(ctx context.Context, in *BatchGetPersonalNumericReq, opts ...grpc.CallOption) (*BatchGetPersonalNumericResp, error)
	// 由批量送礼物 导致更新Numeric
	BatchRecordSendGiftEvent(ctx context.Context, in *BatchRecordSendGiftEventReq, opts ...grpc.CallOption) (*BatchRecordSendGiftEventResp, error)
	AddUserNumeric(ctx context.Context, in *AddUserNumericReq, opts ...grpc.CallOption) (*AddUserNumericResp, error)
	GetPersonalRankingFromLocalCache(ctx context.Context, in *GetPersonalRankingReq, opts ...grpc.CallOption) (*GetPersonalRankingResp, error)
	GetUserRichSwitch(ctx context.Context, in *GetUserRichSwitchReq, opts ...grpc.CallOption) (*GetUserRichSwitchResp, error)
	SetUserRichSwitch(ctx context.Context, in *SetUserRichSwitchReq, opts ...grpc.CallOption) (*SetUserRichSwitchResp, error)
}

type numericGoClient struct {
	cc *grpc.ClientConn
}

func NewNumericGoClient(cc *grpc.ClientConn) NumericGoClient {
	return &numericGoClient{cc}
}

func (c *numericGoClient) GetGuildConsumeList(ctx context.Context, in *GetGuildNumericListReq, opts ...grpc.CallOption) (*GetGuildNumericListResp, error) {
	out := new(GetGuildNumericListResp)
	err := grpc.Invoke(ctx, "/numeric_go.NumericGo/GetGuildConsumeList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericGoClient) GetGuildCharmList(ctx context.Context, in *GetGuildNumericListReq, opts ...grpc.CallOption) (*GetGuildNumericListResp, error) {
	out := new(GetGuildNumericListResp)
	err := grpc.Invoke(ctx, "/numeric_go.NumericGo/GetGuildCharmList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericGoClient) GetPersonalNumeric(ctx context.Context, in *GetPersonalNumericReq, opts ...grpc.CallOption) (*GetPersonalNumericResp, error) {
	out := new(GetPersonalNumericResp)
	err := grpc.Invoke(ctx, "/numeric_go.NumericGo/GetPersonalNumeric", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericGoClient) RecordSendGiftEvent(ctx context.Context, in *RecordSendGiftEventReq, opts ...grpc.CallOption) (*RecordSendGiftEventResp, error) {
	out := new(RecordSendGiftEventResp)
	err := grpc.Invoke(ctx, "/numeric_go.NumericGo/RecordSendGiftEvent", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericGoClient) NotifyGuildQuit(ctx context.Context, in *NotifyGuildChangeReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/numeric_go.NumericGo/NotifyGuildQuit", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericGoClient) NotifyGuildJoin(ctx context.Context, in *NotifyGuildChangeReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/numeric_go.NumericGo/NotifyGuildJoin", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericGoClient) GetGuildGiftTotalValue(ctx context.Context, in *GetGuildGiftTotalValueReq, opts ...grpc.CallOption) (*GetGuildGiftTotalValueResp, error) {
	out := new(GetGuildGiftTotalValueResp)
	err := grpc.Invoke(ctx, "/numeric_go.NumericGo/GetGuildGiftTotalValue", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericGoClient) RecordConsumeEvent(ctx context.Context, in *RecordConsumeEventReq, opts ...grpc.CallOption) (*RecordConsumeEventResp, error) {
	out := new(RecordConsumeEventResp)
	err := grpc.Invoke(ctx, "/numeric_go.NumericGo/RecordConsumeEvent", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericGoClient) GetPersonalRanking(ctx context.Context, in *GetPersonalRankingReq, opts ...grpc.CallOption) (*GetPersonalRankingResp, error) {
	out := new(GetPersonalRankingResp)
	err := grpc.Invoke(ctx, "/numeric_go.NumericGo/GetPersonalRanking", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericGoClient) GetRankList(ctx context.Context, in *GetRankListReq, opts ...grpc.CallOption) (*GetRankListResp, error) {
	out := new(GetRankListResp)
	err := grpc.Invoke(ctx, "/numeric_go.NumericGo/GetRankList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericGoClient) BatchGetPersonalNumeric(ctx context.Context, in *BatchGetPersonalNumericReq, opts ...grpc.CallOption) (*BatchGetPersonalNumericResp, error) {
	out := new(BatchGetPersonalNumericResp)
	err := grpc.Invoke(ctx, "/numeric_go.NumericGo/BatchGetPersonalNumeric", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericGoClient) BatchRecordSendGiftEvent(ctx context.Context, in *BatchRecordSendGiftEventReq, opts ...grpc.CallOption) (*BatchRecordSendGiftEventResp, error) {
	out := new(BatchRecordSendGiftEventResp)
	err := grpc.Invoke(ctx, "/numeric_go.NumericGo/BatchRecordSendGiftEvent", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericGoClient) AddUserNumeric(ctx context.Context, in *AddUserNumericReq, opts ...grpc.CallOption) (*AddUserNumericResp, error) {
	out := new(AddUserNumericResp)
	err := grpc.Invoke(ctx, "/numeric_go.NumericGo/AddUserNumeric", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericGoClient) GetPersonalRankingFromLocalCache(ctx context.Context, in *GetPersonalRankingReq, opts ...grpc.CallOption) (*GetPersonalRankingResp, error) {
	out := new(GetPersonalRankingResp)
	err := grpc.Invoke(ctx, "/numeric_go.NumericGo/GetPersonalRankingFromLocalCache", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericGoClient) GetUserRichSwitch(ctx context.Context, in *GetUserRichSwitchReq, opts ...grpc.CallOption) (*GetUserRichSwitchResp, error) {
	out := new(GetUserRichSwitchResp)
	err := grpc.Invoke(ctx, "/numeric_go.NumericGo/GetUserRichSwitch", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericGoClient) SetUserRichSwitch(ctx context.Context, in *SetUserRichSwitchReq, opts ...grpc.CallOption) (*SetUserRichSwitchResp, error) {
	out := new(SetUserRichSwitchResp)
	err := grpc.Invoke(ctx, "/numeric_go.NumericGo/SetUserRichSwitch", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for NumericGo service

type NumericGoServer interface {
	GetGuildConsumeList(context.Context, *GetGuildNumericListReq) (*GetGuildNumericListResp, error)
	GetGuildCharmList(context.Context, *GetGuildNumericListReq) (*GetGuildNumericListResp, error)
	GetPersonalNumeric(context.Context, *GetPersonalNumericReq) (*GetPersonalNumericResp, error)
	// 由送礼物 导致更新Numeric
	RecordSendGiftEvent(context.Context, *RecordSendGiftEventReq) (*RecordSendGiftEventResp, error)
	NotifyGuildQuit(context.Context, *NotifyGuildChangeReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	NotifyGuildJoin(context.Context, *NotifyGuildChangeReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGuildGiftTotalValue(context.Context, *GetGuildGiftTotalValueReq) (*GetGuildGiftTotalValueResp, error)
	// 由现金消费 导致更新Numeric
	RecordConsumeEvent(context.Context, *RecordConsumeEventReq) (*RecordConsumeEventResp, error)
	GetPersonalRanking(context.Context, *GetPersonalRankingReq) (*GetPersonalRankingResp, error)
	GetRankList(context.Context, *GetRankListReq) (*GetRankListResp, error)
	BatchGetPersonalNumeric(context.Context, *BatchGetPersonalNumericReq) (*BatchGetPersonalNumericResp, error)
	// 由批量送礼物 导致更新Numeric
	BatchRecordSendGiftEvent(context.Context, *BatchRecordSendGiftEventReq) (*BatchRecordSendGiftEventResp, error)
	AddUserNumeric(context.Context, *AddUserNumericReq) (*AddUserNumericResp, error)
	GetPersonalRankingFromLocalCache(context.Context, *GetPersonalRankingReq) (*GetPersonalRankingResp, error)
	GetUserRichSwitch(context.Context, *GetUserRichSwitchReq) (*GetUserRichSwitchResp, error)
	SetUserRichSwitch(context.Context, *SetUserRichSwitchReq) (*SetUserRichSwitchResp, error)
}

func RegisterNumericGoServer(s *grpc.Server, srv NumericGoServer) {
	s.RegisterService(&_NumericGo_serviceDesc, srv)
}

func _NumericGo_GetGuildConsumeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildNumericListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).GetGuildConsumeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/GetGuildConsumeList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).GetGuildConsumeList(ctx, req.(*GetGuildNumericListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericGo_GetGuildCharmList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildNumericListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).GetGuildCharmList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/GetGuildCharmList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).GetGuildCharmList(ctx, req.(*GetGuildNumericListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericGo_GetPersonalNumeric_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPersonalNumericReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).GetPersonalNumeric(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/GetPersonalNumeric",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).GetPersonalNumeric(ctx, req.(*GetPersonalNumericReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericGo_RecordSendGiftEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordSendGiftEventReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).RecordSendGiftEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/RecordSendGiftEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).RecordSendGiftEvent(ctx, req.(*RecordSendGiftEventReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericGo_NotifyGuildQuit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotifyGuildChangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).NotifyGuildQuit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/NotifyGuildQuit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).NotifyGuildQuit(ctx, req.(*NotifyGuildChangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericGo_NotifyGuildJoin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotifyGuildChangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).NotifyGuildJoin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/NotifyGuildJoin",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).NotifyGuildJoin(ctx, req.(*NotifyGuildChangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericGo_GetGuildGiftTotalValue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildGiftTotalValueReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).GetGuildGiftTotalValue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/GetGuildGiftTotalValue",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).GetGuildGiftTotalValue(ctx, req.(*GetGuildGiftTotalValueReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericGo_RecordConsumeEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordConsumeEventReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).RecordConsumeEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/RecordConsumeEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).RecordConsumeEvent(ctx, req.(*RecordConsumeEventReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericGo_GetPersonalRanking_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPersonalRankingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).GetPersonalRanking(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/GetPersonalRanking",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).GetPersonalRanking(ctx, req.(*GetPersonalRankingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericGo_GetRankList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRankListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).GetRankList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/GetRankList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).GetRankList(ctx, req.(*GetRankListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericGo_BatchGetPersonalNumeric_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetPersonalNumericReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).BatchGetPersonalNumeric(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/BatchGetPersonalNumeric",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).BatchGetPersonalNumeric(ctx, req.(*BatchGetPersonalNumericReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericGo_BatchRecordSendGiftEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchRecordSendGiftEventReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).BatchRecordSendGiftEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/BatchRecordSendGiftEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).BatchRecordSendGiftEvent(ctx, req.(*BatchRecordSendGiftEventReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericGo_AddUserNumeric_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserNumericReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).AddUserNumeric(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/AddUserNumeric",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).AddUserNumeric(ctx, req.(*AddUserNumericReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericGo_GetPersonalRankingFromLocalCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPersonalRankingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).GetPersonalRankingFromLocalCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/GetPersonalRankingFromLocalCache",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).GetPersonalRankingFromLocalCache(ctx, req.(*GetPersonalRankingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericGo_GetUserRichSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserRichSwitchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).GetUserRichSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/GetUserRichSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).GetUserRichSwitch(ctx, req.(*GetUserRichSwitchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericGo_SetUserRichSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserRichSwitchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).SetUserRichSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/SetUserRichSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).SetUserRichSwitch(ctx, req.(*SetUserRichSwitchReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _NumericGo_serviceDesc = grpc.ServiceDesc{
	ServiceName: "numeric_go.NumericGo",
	HandlerType: (*NumericGoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetGuildConsumeList",
			Handler:    _NumericGo_GetGuildConsumeList_Handler,
		},
		{
			MethodName: "GetGuildCharmList",
			Handler:    _NumericGo_GetGuildCharmList_Handler,
		},
		{
			MethodName: "GetPersonalNumeric",
			Handler:    _NumericGo_GetPersonalNumeric_Handler,
		},
		{
			MethodName: "RecordSendGiftEvent",
			Handler:    _NumericGo_RecordSendGiftEvent_Handler,
		},
		{
			MethodName: "NotifyGuildQuit",
			Handler:    _NumericGo_NotifyGuildQuit_Handler,
		},
		{
			MethodName: "NotifyGuildJoin",
			Handler:    _NumericGo_NotifyGuildJoin_Handler,
		},
		{
			MethodName: "GetGuildGiftTotalValue",
			Handler:    _NumericGo_GetGuildGiftTotalValue_Handler,
		},
		{
			MethodName: "RecordConsumeEvent",
			Handler:    _NumericGo_RecordConsumeEvent_Handler,
		},
		{
			MethodName: "GetPersonalRanking",
			Handler:    _NumericGo_GetPersonalRanking_Handler,
		},
		{
			MethodName: "GetRankList",
			Handler:    _NumericGo_GetRankList_Handler,
		},
		{
			MethodName: "BatchGetPersonalNumeric",
			Handler:    _NumericGo_BatchGetPersonalNumeric_Handler,
		},
		{
			MethodName: "BatchRecordSendGiftEvent",
			Handler:    _NumericGo_BatchRecordSendGiftEvent_Handler,
		},
		{
			MethodName: "AddUserNumeric",
			Handler:    _NumericGo_AddUserNumeric_Handler,
		},
		{
			MethodName: "GetPersonalRankingFromLocalCache",
			Handler:    _NumericGo_GetPersonalRankingFromLocalCache_Handler,
		},
		{
			MethodName: "GetUserRichSwitch",
			Handler:    _NumericGo_GetUserRichSwitch_Handler,
		},
		{
			MethodName: "SetUserRichSwitch",
			Handler:    _NumericGo_SetUserRichSwitch_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "numeric-go/numeric-go.proto",
}

func (m *GetGuildNumericListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildNumericListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.Index))
	dAtA[i] = 0x18
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.Count))
	return i, nil
}

func (m *GuildMemerNumeric) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildMemerNumeric) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.Numeric))
	dAtA[i] = 0x18
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.Numeric64))
	return i, nil
}

func (m *GetGuildNumericListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildNumericListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.NumericList) > 0 {
		for _, msg := range m.NumericList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintNumericGo(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetPersonalNumericReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPersonalNumericReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetPersonalNumericResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPersonalNumericResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.ConsumeNumeric))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.CharmNumeric))
	dAtA[i] = 0x18
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.ConsumeNumeric64))
	dAtA[i] = 0x20
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.CharmNumeric64))
	return i, nil
}

func (m *BatchGetPersonalNumericReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetPersonalNumericReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintNumericGo(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *PersonalNumeric) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PersonalNumeric) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.Charm))
	dAtA[i] = 0x18
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.Rich))
	dAtA[i] = 0x20
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.Charm64))
	dAtA[i] = 0x28
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.Rich64))
	return i, nil
}

func (m *BatchGetPersonalNumericResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetPersonalNumericResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.NumericList) > 0 {
		for _, msg := range m.NumericList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintNumericGo(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *RecordSendGiftEventReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecordSendGiftEventReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.GiverUid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.ReceiverUid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.RichValue))
	dAtA[i] = 0x20
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.GiverGuild))
	dAtA[i] = 0x28
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.ReceiverGuild))
	dAtA[i] = 0x30
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.CharmValue))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(len(m.OrderId)))
	i += copy(dAtA[i:], m.OrderId)
	dAtA[i] = 0x40
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x48
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.ChannelGuild))
	dAtA[i] = 0x50
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.PriceType))
	dAtA[i] = 0x58
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.GiftId))
	return i, nil
}

func (m *RecordSendGiftEventResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecordSendGiftEventResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.LevelChanageUidList) > 0 {
		for _, num := range m.LevelChanageUidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintNumericGo(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.RealCharm))
	dAtA[i] = 0x18
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.GiverCurrAllRich))
	dAtA[i] = 0x20
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.ReceiverCurrAllCharm))
	dAtA[i] = 0x28
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.RealRich))
	dAtA[i] = 0x30
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.BeforeRich))
	dAtA[i] = 0x38
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.BeforeRich64))
	dAtA[i] = 0x40
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.GiverCurrAllRich64))
	dAtA[i] = 0x48
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.ReceiverCurrAllCharm64))
	return i, nil
}

func (m *UserGiftEventInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserGiftEventInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.AddValue))
	dAtA[i] = 0x20
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.FinalValue))
	dAtA[i] = 0x28
	i++
	if m.LevelChange {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x30
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.FinalValue64))
	return i, nil
}

func (m *BatchRecordSendGiftEventReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchRecordSendGiftEventReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.GiverUserInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("giver_user_info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintNumericGo(dAtA, i, uint64(m.GiverUserInfo.Size()))
		n1, err := m.GiverUserInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	if len(m.ReceiverUserInfoList) > 0 {
		for _, msg := range m.ReceiverUserInfoList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintNumericGo(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x1a
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(len(m.OrderId)))
	i += copy(dAtA[i:], m.OrderId)
	dAtA[i] = 0x20
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.ChannelGuild))
	dAtA[i] = 0x30
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.PriceType))
	dAtA[i] = 0x38
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.GiftId))
	return i, nil
}

func (m *BatchRecordSendGiftEventResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchRecordSendGiftEventResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.GiverUserInfo != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintNumericGo(dAtA, i, uint64(m.GiverUserInfo.Size()))
		n2, err := m.GiverUserInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	if len(m.ReceiverUserInfoList) > 0 {
		for _, msg := range m.ReceiverUserInfoList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintNumericGo(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *RecordConsumeEventReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecordConsumeEventReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.RichValue))
	dAtA[i] = 0x20
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.ConsumeType))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(len(m.OrderInfo)))
	i += copy(dAtA[i:], m.OrderInfo)
	return i, nil
}

func (m *RecordConsumeEventResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecordConsumeEventResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *NotifyGuildChangeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NotifyGuildChangeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.GuildId))
	return i, nil
}

func (m *GetGuildGiftTotalValueReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildGiftTotalValueReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.GuildId))
	return i, nil
}

func (m *GetGuildGiftTotalValueResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildGiftTotalValueResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.GiftValue))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.RichValue64))
	dAtA[i] = 0x18
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.GiftValue64))
	return i, nil
}

func (m *GetPersonalRankingReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPersonalRankingReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetPersonalRankingResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPersonalRankingResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.WeekCharmRanking))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.DayCharmRanking))
	dAtA[i] = 0x18
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.WeekRichRanking))
	dAtA[i] = 0x20
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.DayRichRanking))
	dAtA[i] = 0x28
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.MonthCharmRanking))
	dAtA[i] = 0x30
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.MonthRichRanking))
	return i, nil
}

func (m *GetRankListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRankListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.Index))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.Count))
	dAtA[i] = 0x18
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.RankType))
	dAtA[i] = 0x20
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetRankListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRankListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.RankList) > 0 {
		for _, num := range m.RankList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintNumericGo(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.RankLast))
	dAtA[i] = 0x18
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.RankNow))
	dAtA[i] = 0x20
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.GapToRiseRank))
	return i, nil
}

func (m *AddUserNumericReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddUserNumericReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.AddType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.RichValue))
	dAtA[i] = 0x20
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.CharmValue))
	return i, nil
}

func (m *AddUserNumericResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddUserNumericResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.FinalRichValue))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.FinalCharmValue))
	return i, nil
}

func (m *GetUserRichSwitchReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserRichSwitchReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetUserRichSwitchResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserRichSwitchResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.Enable {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *SetUserRichSwitchReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetUserRichSwitchReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNumericGo(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	if m.Enable {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *SetUserRichSwitchResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetUserRichSwitchResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func encodeFixed64NumericGo(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32NumericGo(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintNumericGo(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *GetGuildNumericListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNumericGo(uint64(m.GuildId))
	n += 1 + sovNumericGo(uint64(m.Index))
	n += 1 + sovNumericGo(uint64(m.Count))
	return n
}

func (m *GuildMemerNumeric) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNumericGo(uint64(m.Uid))
	n += 1 + sovNumericGo(uint64(m.Numeric))
	n += 1 + sovNumericGo(uint64(m.Numeric64))
	return n
}

func (m *GetGuildNumericListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.NumericList) > 0 {
		for _, e := range m.NumericList {
			l = e.Size()
			n += 1 + l + sovNumericGo(uint64(l))
		}
	}
	return n
}

func (m *GetPersonalNumericReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNumericGo(uint64(m.Uid))
	return n
}

func (m *GetPersonalNumericResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNumericGo(uint64(m.ConsumeNumeric))
	n += 1 + sovNumericGo(uint64(m.CharmNumeric))
	n += 1 + sovNumericGo(uint64(m.ConsumeNumeric64))
	n += 1 + sovNumericGo(uint64(m.CharmNumeric64))
	return n
}

func (m *BatchGetPersonalNumericReq) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovNumericGo(uint64(e))
		}
	}
	return n
}

func (m *PersonalNumeric) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNumericGo(uint64(m.Uid))
	n += 1 + sovNumericGo(uint64(m.Charm))
	n += 1 + sovNumericGo(uint64(m.Rich))
	n += 1 + sovNumericGo(uint64(m.Charm64))
	n += 1 + sovNumericGo(uint64(m.Rich64))
	return n
}

func (m *BatchGetPersonalNumericResp) Size() (n int) {
	var l int
	_ = l
	if len(m.NumericList) > 0 {
		for _, e := range m.NumericList {
			l = e.Size()
			n += 1 + l + sovNumericGo(uint64(l))
		}
	}
	return n
}

func (m *RecordSendGiftEventReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNumericGo(uint64(m.GiverUid))
	n += 1 + sovNumericGo(uint64(m.ReceiverUid))
	n += 1 + sovNumericGo(uint64(m.RichValue))
	n += 1 + sovNumericGo(uint64(m.GiverGuild))
	n += 1 + sovNumericGo(uint64(m.ReceiverGuild))
	n += 1 + sovNumericGo(uint64(m.CharmValue))
	l = len(m.OrderId)
	n += 1 + l + sovNumericGo(uint64(l))
	n += 1 + sovNumericGo(uint64(m.ChannelId))
	n += 1 + sovNumericGo(uint64(m.ChannelGuild))
	n += 1 + sovNumericGo(uint64(m.PriceType))
	n += 1 + sovNumericGo(uint64(m.GiftId))
	return n
}

func (m *RecordSendGiftEventResp) Size() (n int) {
	var l int
	_ = l
	if len(m.LevelChanageUidList) > 0 {
		for _, e := range m.LevelChanageUidList {
			n += 1 + sovNumericGo(uint64(e))
		}
	}
	n += 1 + sovNumericGo(uint64(m.RealCharm))
	n += 1 + sovNumericGo(uint64(m.GiverCurrAllRich))
	n += 1 + sovNumericGo(uint64(m.ReceiverCurrAllCharm))
	n += 1 + sovNumericGo(uint64(m.RealRich))
	n += 1 + sovNumericGo(uint64(m.BeforeRich))
	n += 1 + sovNumericGo(uint64(m.BeforeRich64))
	n += 1 + sovNumericGo(uint64(m.GiverCurrAllRich64))
	n += 1 + sovNumericGo(uint64(m.ReceiverCurrAllCharm64))
	return n
}

func (m *UserGiftEventInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNumericGo(uint64(m.Uid))
	n += 1 + sovNumericGo(uint64(m.GuildId))
	n += 1 + sovNumericGo(uint64(m.AddValue))
	n += 1 + sovNumericGo(uint64(m.FinalValue))
	n += 2
	n += 1 + sovNumericGo(uint64(m.FinalValue64))
	return n
}

func (m *BatchRecordSendGiftEventReq) Size() (n int) {
	var l int
	_ = l
	if m.GiverUserInfo != nil {
		l = m.GiverUserInfo.Size()
		n += 1 + l + sovNumericGo(uint64(l))
	}
	if len(m.ReceiverUserInfoList) > 0 {
		for _, e := range m.ReceiverUserInfoList {
			l = e.Size()
			n += 1 + l + sovNumericGo(uint64(l))
		}
	}
	l = len(m.OrderId)
	n += 1 + l + sovNumericGo(uint64(l))
	n += 1 + sovNumericGo(uint64(m.ChannelId))
	n += 1 + sovNumericGo(uint64(m.ChannelGuild))
	n += 1 + sovNumericGo(uint64(m.PriceType))
	n += 1 + sovNumericGo(uint64(m.GiftId))
	return n
}

func (m *BatchRecordSendGiftEventResp) Size() (n int) {
	var l int
	_ = l
	if m.GiverUserInfo != nil {
		l = m.GiverUserInfo.Size()
		n += 1 + l + sovNumericGo(uint64(l))
	}
	if len(m.ReceiverUserInfoList) > 0 {
		for _, e := range m.ReceiverUserInfoList {
			l = e.Size()
			n += 1 + l + sovNumericGo(uint64(l))
		}
	}
	return n
}

func (m *RecordConsumeEventReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNumericGo(uint64(m.Uid))
	n += 1 + sovNumericGo(uint64(m.GuildId))
	n += 1 + sovNumericGo(uint64(m.RichValue))
	n += 1 + sovNumericGo(uint64(m.ConsumeType))
	l = len(m.OrderInfo)
	n += 1 + l + sovNumericGo(uint64(l))
	return n
}

func (m *RecordConsumeEventResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *NotifyGuildChangeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNumericGo(uint64(m.Uid))
	n += 1 + sovNumericGo(uint64(m.GuildId))
	return n
}

func (m *GetGuildGiftTotalValueReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNumericGo(uint64(m.GuildId))
	return n
}

func (m *GetGuildGiftTotalValueResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNumericGo(uint64(m.GiftValue))
	n += 1 + sovNumericGo(uint64(m.RichValue64))
	n += 1 + sovNumericGo(uint64(m.GiftValue64))
	return n
}

func (m *GetPersonalRankingReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNumericGo(uint64(m.Uid))
	return n
}

func (m *GetPersonalRankingResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNumericGo(uint64(m.WeekCharmRanking))
	n += 1 + sovNumericGo(uint64(m.DayCharmRanking))
	n += 1 + sovNumericGo(uint64(m.WeekRichRanking))
	n += 1 + sovNumericGo(uint64(m.DayRichRanking))
	n += 1 + sovNumericGo(uint64(m.MonthCharmRanking))
	n += 1 + sovNumericGo(uint64(m.MonthRichRanking))
	return n
}

func (m *GetRankListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNumericGo(uint64(m.Index))
	n += 1 + sovNumericGo(uint64(m.Count))
	n += 1 + sovNumericGo(uint64(m.RankType))
	n += 1 + sovNumericGo(uint64(m.Uid))
	return n
}

func (m *GetRankListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.RankList) > 0 {
		for _, e := range m.RankList {
			n += 1 + sovNumericGo(uint64(e))
		}
	}
	n += 1 + sovNumericGo(uint64(m.RankLast))
	n += 1 + sovNumericGo(uint64(m.RankNow))
	n += 1 + sovNumericGo(uint64(m.GapToRiseRank))
	return n
}

func (m *AddUserNumericReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNumericGo(uint64(m.Uid))
	n += 1 + sovNumericGo(uint64(m.AddType))
	n += 1 + sovNumericGo(uint64(m.RichValue))
	n += 1 + sovNumericGo(uint64(m.CharmValue))
	return n
}

func (m *AddUserNumericResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNumericGo(uint64(m.FinalRichValue))
	n += 1 + sovNumericGo(uint64(m.FinalCharmValue))
	return n
}

func (m *GetUserRichSwitchReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNumericGo(uint64(m.Uid))
	return n
}

func (m *GetUserRichSwitchResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	return n
}

func (m *SetUserRichSwitchReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNumericGo(uint64(m.Uid))
	n += 2
	return n
}

func (m *SetUserRichSwitchResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func sovNumericGo(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozNumericGo(x uint64) (n int) {
	return sovNumericGo(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *GetGuildNumericListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildNumericListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildNumericListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Index", wireType)
			}
			m.Index = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Index |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipNumericGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("index")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildMemerNumeric) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildMemerNumeric: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildMemerNumeric: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Numeric", wireType)
			}
			m.Numeric = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Numeric |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Numeric64", wireType)
			}
			m.Numeric64 = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Numeric64 |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNumericGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("numeric")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildNumericListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildNumericListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildNumericListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NumericList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthNumericGo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.NumericList = append(m.NumericList, &GuildMemerNumeric{})
			if err := m.NumericList[len(m.NumericList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipNumericGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPersonalNumericReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPersonalNumericReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPersonalNumericReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipNumericGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPersonalNumericResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPersonalNumericResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPersonalNumericResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConsumeNumeric", wireType)
			}
			m.ConsumeNumeric = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConsumeNumeric |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CharmNumeric", wireType)
			}
			m.CharmNumeric = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CharmNumeric |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConsumeNumeric64", wireType)
			}
			m.ConsumeNumeric64 = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConsumeNumeric64 |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CharmNumeric64", wireType)
			}
			m.CharmNumeric64 = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CharmNumeric64 |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNumericGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("consume_numeric")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("charm_numeric")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetPersonalNumericReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetPersonalNumericReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetPersonalNumericReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowNumericGo
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowNumericGo
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthNumericGo
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowNumericGo
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNumericGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PersonalNumeric) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PersonalNumeric: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PersonalNumeric: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Charm", wireType)
			}
			m.Charm = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Charm |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Rich", wireType)
			}
			m.Rich = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Rich |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Charm64", wireType)
			}
			m.Charm64 = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Charm64 |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Rich64", wireType)
			}
			m.Rich64 = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Rich64 |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNumericGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("charm")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("rich")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetPersonalNumericResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetPersonalNumericResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetPersonalNumericResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NumericList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthNumericGo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.NumericList = append(m.NumericList, &PersonalNumeric{})
			if err := m.NumericList[len(m.NumericList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipNumericGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecordSendGiftEventReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecordSendGiftEventReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecordSendGiftEventReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiverUid", wireType)
			}
			m.GiverUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiverUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReceiverUid", wireType)
			}
			m.ReceiverUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReceiverUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RichValue", wireType)
			}
			m.RichValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RichValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiverGuild", wireType)
			}
			m.GiverGuild = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiverGuild |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReceiverGuild", wireType)
			}
			m.ReceiverGuild = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReceiverGuild |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CharmValue", wireType)
			}
			m.CharmValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CharmValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthNumericGo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelGuild", wireType)
			}
			m.ChannelGuild = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelGuild |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PriceType", wireType)
			}
			m.PriceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PriceType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftId", wireType)
			}
			m.GiftId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNumericGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("giver_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("receiver_uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("rich_value")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("giver_guild")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("receiver_guild")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("charm_value")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("order_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecordSendGiftEventResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecordSendGiftEventResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecordSendGiftEventResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowNumericGo
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.LevelChanageUidList = append(m.LevelChanageUidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowNumericGo
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthNumericGo
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowNumericGo
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.LevelChanageUidList = append(m.LevelChanageUidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field LevelChanageUidList", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RealCharm", wireType)
			}
			m.RealCharm = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RealCharm |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiverCurrAllRich", wireType)
			}
			m.GiverCurrAllRich = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiverCurrAllRich |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReceiverCurrAllCharm", wireType)
			}
			m.ReceiverCurrAllCharm = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReceiverCurrAllCharm |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RealRich", wireType)
			}
			m.RealRich = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RealRich |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeforeRich", wireType)
			}
			m.BeforeRich = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeforeRich |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeforeRich64", wireType)
			}
			m.BeforeRich64 = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeforeRich64 |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiverCurrAllRich64", wireType)
			}
			m.GiverCurrAllRich64 = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiverCurrAllRich64 |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReceiverCurrAllCharm64", wireType)
			}
			m.ReceiverCurrAllCharm64 = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReceiverCurrAllCharm64 |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNumericGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserGiftEventInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserGiftEventInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserGiftEventInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AddValue", wireType)
			}
			m.AddValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AddValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FinalValue", wireType)
			}
			m.FinalValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FinalValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LevelChange", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.LevelChange = bool(v != 0)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FinalValue64", wireType)
			}
			m.FinalValue64 = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FinalValue64 |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNumericGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchRecordSendGiftEventReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchRecordSendGiftEventReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchRecordSendGiftEventReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiverUserInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthNumericGo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GiverUserInfo == nil {
				m.GiverUserInfo = &UserGiftEventInfo{}
			}
			if err := m.GiverUserInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReceiverUserInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthNumericGo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ReceiverUserInfoList = append(m.ReceiverUserInfoList, &UserGiftEventInfo{})
			if err := m.ReceiverUserInfoList[len(m.ReceiverUserInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthNumericGo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelGuild", wireType)
			}
			m.ChannelGuild = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelGuild |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PriceType", wireType)
			}
			m.PriceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PriceType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftId", wireType)
			}
			m.GiftId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNumericGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("giver_user_info")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("order_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchRecordSendGiftEventResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchRecordSendGiftEventResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchRecordSendGiftEventResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiverUserInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthNumericGo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GiverUserInfo == nil {
				m.GiverUserInfo = &UserGiftEventInfo{}
			}
			if err := m.GiverUserInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReceiverUserInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthNumericGo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ReceiverUserInfoList = append(m.ReceiverUserInfoList, &UserGiftEventInfo{})
			if err := m.ReceiverUserInfoList[len(m.ReceiverUserInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipNumericGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecordConsumeEventReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecordConsumeEventReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecordConsumeEventReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RichValue", wireType)
			}
			m.RichValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RichValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConsumeType", wireType)
			}
			m.ConsumeType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConsumeType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthNumericGo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipNumericGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("rich_value")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("consume_type")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("order_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecordConsumeEventResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecordConsumeEventResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecordConsumeEventResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipNumericGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *NotifyGuildChangeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NotifyGuildChangeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NotifyGuildChangeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipNumericGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildGiftTotalValueReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildGiftTotalValueReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildGiftTotalValueReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipNumericGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildGiftTotalValueResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildGiftTotalValueResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildGiftTotalValueResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftValue", wireType)
			}
			m.GiftValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RichValue64", wireType)
			}
			m.RichValue64 = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RichValue64 |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftValue64", wireType)
			}
			m.GiftValue64 = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftValue64 |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNumericGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_value")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPersonalRankingReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPersonalRankingReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPersonalRankingReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipNumericGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPersonalRankingResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPersonalRankingResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPersonalRankingResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field WeekCharmRanking", wireType)
			}
			m.WeekCharmRanking = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.WeekCharmRanking |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DayCharmRanking", wireType)
			}
			m.DayCharmRanking = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DayCharmRanking |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field WeekRichRanking", wireType)
			}
			m.WeekRichRanking = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.WeekRichRanking |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DayRichRanking", wireType)
			}
			m.DayRichRanking = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DayRichRanking |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MonthCharmRanking", wireType)
			}
			m.MonthCharmRanking = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MonthCharmRanking |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MonthRichRanking", wireType)
			}
			m.MonthRichRanking = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MonthRichRanking |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNumericGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("week_charm_ranking")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("day_charm_ranking")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("week_rich_ranking")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("day_rich_ranking")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRankListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRankListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRankListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Index", wireType)
			}
			m.Index = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Index |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankType", wireType)
			}
			m.RankType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RankType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNumericGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("index")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("count")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("rank_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRankListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRankListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRankListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowNumericGo
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.RankList = append(m.RankList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowNumericGo
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthNumericGo
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowNumericGo
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.RankList = append(m.RankList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankList", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankLast", wireType)
			}
			m.RankLast = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RankLast |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankNow", wireType)
			}
			m.RankNow = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RankNow |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GapToRiseRank", wireType)
			}
			m.GapToRiseRank = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GapToRiseRank |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNumericGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddUserNumericReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddUserNumericReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddUserNumericReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AddType", wireType)
			}
			m.AddType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AddType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RichValue", wireType)
			}
			m.RichValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RichValue |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CharmValue", wireType)
			}
			m.CharmValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CharmValue |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNumericGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("add_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddUserNumericResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddUserNumericResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddUserNumericResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FinalRichValue", wireType)
			}
			m.FinalRichValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FinalRichValue |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FinalCharmValue", wireType)
			}
			m.FinalCharmValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FinalCharmValue |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipNumericGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("final_rich_value")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("final_charm_value")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserRichSwitchReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserRichSwitchReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserRichSwitchReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipNumericGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserRichSwitchResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserRichSwitchResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserRichSwitchResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Enable", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Enable = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipNumericGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("enable")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetUserRichSwitchReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetUserRichSwitchReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetUserRichSwitchReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Enable", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Enable = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipNumericGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("enable")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetUserRichSwitchResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNumericGo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetUserRichSwitchResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetUserRichSwitchResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipNumericGo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNumericGo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipNumericGo(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowNumericGo
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowNumericGo
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthNumericGo
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowNumericGo
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipNumericGo(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthNumericGo = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowNumericGo   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("numeric-go/numeric-go.proto", fileDescriptorNumericGo) }

var fileDescriptorNumericGo = []byte{
	// 2225 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x58, 0xcd, 0x6f, 0x1b, 0x5b,
	0x15, 0xef, 0xd8, 0x4e, 0x62, 0x1f, 0xdb, 0xb1, 0x7d, 0xdb, 0xa6, 0xae, 0xdb, 0x97, 0x4e, 0xa6,
	0x94, 0xa6, 0x0f, 0x39, 0x7d, 0xe4, 0x19, 0x3f, 0xc9, 0xcf, 0x32, 0x24, 0x8e, 0x49, 0x4d, 0x93,
	0x34, 0x38, 0x4e, 0x51, 0x41, 0x68, 0x34, 0xf5, 0xdc, 0x38, 0x43, 0xec, 0x99, 0xe9, 0xcc, 0x38,
	0x69, 0x24, 0xf4, 0xc4, 0x02, 0x89, 0x8f, 0x05, 0x42, 0xb0, 0x79, 0x1b, 0x84, 0x40, 0xfd, 0x27,
	0x10, 0x4b, 0x36, 0x4f, 0xac, 0x10, 0x7a, 0x12, 0x3b, 0x84, 0xca, 0xa6, 0x42, 0x42, 0xac, 0xd9,
	0xa1, 0x7b, 0xef, 0xcc, 0xf8, 0xce, 0x78, 0xa6, 0x09, 0xe2, 0x3d, 0x89, 0x45, 0xa4, 0xf8, 0x7c,
	0xfd, 0xce, 0x39, 0xf7, 0x9c, 0x73, 0xcf, 0x1d, 0xb8, 0xa5, 0x4f, 0xc6, 0xd8, 0xd2, 0x06, 0xd5,
	0xa1, 0xf1, 0x70, 0xfa, 0xef, 0x9a, 0x69, 0x19, 0x8e, 0x81, 0xc0, 0xa5, 0xc8, 0x43, 0xa3, 0xf2,
	0x85, 0x81, 0x31, 0x1e, 0x1b, 0xfa, 0x43, 0x67, 0x74, 0x6a, 0x6a, 0x83, 0x93, 0x11, 0x7e, 0x68,
	0x9f, 0x3c, 0x9f, 0x68, 0x23, 0x47, 0xd3, 0x9d, 0x73, 0x13, 0x33, 0x0d, 0xe9, 0x05, 0x2c, 0x6d,
	0x63, 0x67, 0x7b, 0xa2, 0x8d, 0xd4, 0x3d, 0xa6, 0xbb, 0xa3, 0xd9, 0x4e, 0x0f, 0xbf, 0x40, 0x77,
	0x20, 0x3d, 0x24, 0x64, 0x59, 0x53, 0xcb, 0x82, 0x98, 0x58, 0xcd, 0x6f, 0xa6, 0x3e, 0xf9, 0xeb,
	0x9d, 0x2b, 0xbd, 0x05, 0x4a, 0xed, 0xaa, 0xa8, 0x02, 0x73, 0x9a, 0xae, 0xe2, 0x97, 0xe5, 0x04,
	0xc7, 0x65, 0x24, 0xc2, 0x1b, 0x18, 0x13, 0xdd, 0x29, 0x27, 0x79, 0x1e, 0x25, 0x49, 0x06, 0x94,
	0x28, 0xde, 0x2e, 0x1e, 0x63, 0xcb, 0x05, 0x45, 0x4b, 0x90, 0x9c, 0x84, 0x80, 0x08, 0x01, 0x2d,
	0xc3, 0x82, 0x1b, 0x53, 0x00, 0xc6, 0x23, 0x22, 0x09, 0x32, 0xee, 0xbf, 0xf5, 0x5a, 0x39, 0x29,
	0x0a, 0xab, 0x29, 0x57, 0x62, 0x4a, 0x96, 0xbe, 0x03, 0x37, 0x22, 0x63, 0xb4, 0x4d, 0xf4, 0x35,
	0xc8, 0x79, 0x29, 0x1b, 0x69, 0xb6, 0x53, 0x16, 0xc4, 0xe4, 0x6a, 0x76, 0xfd, 0x9d, 0xb5, 0x69,
	0x1e, 0xd7, 0x66, 0x7c, 0xed, 0x65, 0xf5, 0xa9, 0x15, 0xe9, 0x21, 0x5c, 0xdf, 0xc6, 0xce, 0x3e,
	0xb6, 0x6c, 0x43, 0x57, 0x46, 0x9e, 0x08, 0x7e, 0x11, 0x17, 0x91, 0xf4, 0xa9, 0x40, 0x53, 0x3e,
	0xa3, 0x61, 0x9b, 0xa8, 0x0a, 0x85, 0x81, 0xa1, 0xdb, 0x93, 0x31, 0x96, 0xbd, 0xa0, 0x79, 0xf5,
	0x45, 0x97, 0xe9, 0xe5, 0xec, 0x01, 0xe4, 0x07, 0xc7, 0x8a, 0x35, 0x96, 0xa3, 0x32, 0x94, 0xa3,
	0x2c, 0x4f, 0xf4, 0xcb, 0x50, 0x0a, 0x59, 0x0e, 0xa5, 0xab, 0x18, 0xb4, 0x5d, 0xaf, 0x51, 0x67,
	0x78, 0xeb, 0xf5, 0x5a, 0x39, 0xc5, 0x29, 0x2c, 0xf2, 0xf6, 0xeb, 0x35, 0xe9, 0x03, 0xa8, 0x6c,
	0x2a, 0xce, 0xe0, 0x38, 0x3a, 0x19, 0x37, 0x21, 0x3d, 0xd1, 0xd4, 0x69, 0x8e, 0xf3, 0xbd, 0x85,
	0x89, 0xa6, 0xd2, 0x04, 0xfe, 0x4a, 0x80, 0x42, 0x48, 0x23, 0xb6, 0x1a, 0x48, 0x59, 0x11, 0xd8,
	0x60, 0xc9, 0x51, 0x12, 0x2a, 0x43, 0xca, 0xd2, 0x06, 0xc7, 0x81, 0x8a, 0xa3, 0x14, 0x52, 0x43,
	0x54, 0x24, 0x14, 0x81, 0x47, 0x44, 0xb7, 0x61, 0x9e, 0xc8, 0xd5, 0x6b, 0xe5, 0x39, 0x8e, 0xed,
	0xd2, 0xa4, 0xef, 0xc2, 0xad, 0xd8, 0xc0, 0x6c, 0x13, 0xb5, 0x22, 0x2b, 0xe8, 0x16, 0x5f, 0x41,
	0x61, 0xb5, 0x40, 0xfd, 0xfc, 0x2e, 0x09, 0x4b, 0x3d, 0x3c, 0x30, 0x2c, 0xf5, 0x00, 0xeb, 0xea,
	0xb6, 0x76, 0xe4, 0x74, 0x4e, 0xb1, 0x4e, 0x3b, 0x70, 0x05, 0x32, 0x43, 0xed, 0x14, 0x5b, 0x72,
	0x38, 0x17, 0x69, 0x4a, 0x3e, 0xd4, 0x54, 0x74, 0x1f, 0x72, 0x16, 0x1e, 0x60, 0x5f, 0x8a, 0xcf,
	0x4b, 0xd6, 0xe3, 0x10, 0xc1, 0xbb, 0x00, 0x24, 0x1e, 0xf9, 0x54, 0x19, 0x4d, 0x70, 0x20, 0x47,
	0x19, 0x42, 0x7f, 0x4a, 0xc8, 0xe8, 0x1e, 0x64, 0x19, 0x20, 0x6d, 0xf1, 0x72, 0x8a, 0x93, 0x02,
	0xca, 0xa0, 0xbd, 0x80, 0xbe, 0x04, 0x8b, 0x3e, 0x28, 0x93, 0x9c, 0xe3, 0x24, 0xf3, 0x1e, 0x8f,
	0x09, 0xdf, 0x83, 0x2c, 0x2b, 0x23, 0x86, 0x3c, 0xcf, 0xdb, 0xa4, 0x0c, 0x06, 0x7d, 0x07, 0xd2,
	0x86, 0xa5, 0x62, 0x8b, 0x4c, 0x9b, 0x05, 0x31, 0xb1, 0x9a, 0xf1, 0x0e, 0x89, 0x52, 0xbb, 0x34,
	0x80, 0xc1, 0xb1, 0xa2, 0xeb, 0x78, 0x44, 0x44, 0xd2, 0xa2, 0x30, 0x0d, 0xc0, 0xa5, 0x77, 0x55,
	0xb7, 0x23, 0xa8, 0x10, 0x73, 0x2c, 0xc3, 0xc9, 0xe5, 0x5c, 0x16, 0xf3, 0xeb, 0x2e, 0x80, 0x69,
	0x69, 0x03, 0x2c, 0x93, 0x61, 0x58, 0x06, 0xde, 0x1e, 0xa5, 0xf7, 0xcf, 0x4d, 0x8c, 0xde, 0x81,
	0x85, 0xa1, 0x76, 0xe4, 0x10, 0xc4, 0x2c, 0x27, 0x31, 0x4f, 0x88, 0x5d, 0x55, 0xfa, 0x34, 0x09,
	0x37, 0x22, 0xcf, 0xce, 0x36, 0xd1, 0xfb, 0xb0, 0x34, 0xc2, 0xa7, 0x78, 0x24, 0x13, 0x54, 0x65,
	0x88, 0xe5, 0x50, 0xfd, 0x5f, 0xa5, 0xdc, 0x36, 0x63, 0x1e, 0xb2, 0x5e, 0xa0, 0xa7, 0x84, 0x15,
	0xaa, 0x43, 0x8b, 0x9c, 0x73, 0x8a, 0xd0, 0xdb, 0xb4, 0xd0, 0xdf, 0x87, 0xab, 0xec, 0x94, 0x06,
	0x13, 0xcb, 0x92, 0x95, 0xd1, 0x48, 0x76, 0xeb, 0x7e, 0x2a, 0x5d, 0xa4, 0x02, 0xed, 0x89, 0x65,
	0x6d, 0x8c, 0x46, 0x3d, 0xd2, 0x03, 0x1f, 0xc2, 0x0d, 0xff, 0xcc, 0x7c, 0x3d, 0x06, 0x93, 0xe2,
	0x14, 0xaf, 0x79, 0x42, 0xae, 0x2e, 0x43, 0x5c, 0x01, 0x0a, 0xcf, 0x70, 0xe6, 0x38, 0xf1, 0x34,
	0x21, 0x53, 0xfb, 0xf7, 0x20, 0xfb, 0x1c, 0x1f, 0x19, 0x16, 0x66, 0x42, 0xf3, 0x9c, 0x10, 0x30,
	0x06, 0x15, 0x7b, 0x00, 0x79, 0x4e, 0xac, 0x5e, 0x2b, 0x2f, 0x70, 0x1d, 0x97, 0x9b, 0x0a, 0xd6,
	0x6b, 0xe8, 0x03, 0xb8, 0x1e, 0x11, 0x66, 0xbd, 0x46, 0xcf, 0xde, 0x53, 0x41, 0xe1, 0x40, 0xeb,
	0x35, 0xf4, 0x55, 0xb8, 0x19, 0x13, 0x6a, 0xbd, 0x46, 0x0b, 0xc2, 0x53, 0x5e, 0x8a, 0x0a, 0xb6,
	0x5e, 0x93, 0xfe, 0x21, 0x40, 0xe9, 0xd0, 0xc6, 0x96, 0x7f, 0xa0, 0x5d, 0xfd, 0xc8, 0x88, 0x9d,
	0x49, 0xfc, 0x3d, 0xc9, 0x9f, 0x98, 0x7f, 0x4f, 0xae, 0x40, 0x46, 0x51, 0x55, 0xbf, 0xf3, 0xb8,
	0xec, 0x29, 0xaa, 0xea, 0x37, 0xde, 0x91, 0xa6, 0x2b, 0x23, 0x57, 0x88, 0x3f, 0x11, 0xa0, 0x0c,
	0x26, 0x76, 0x1f, 0x72, 0xd3, 0x9a, 0x1a, 0x62, 0x7a, 0x14, 0x69, 0xaf, 0xdb, 0xfd, 0x7a, 0x1a,
	0x62, 0x92, 0x66, 0xce, 0x5e, 0xbd, 0x46, 0xcf, 0xc3, 0x4f, 0xf3, 0xd4, 0x62, 0xbd, 0x26, 0xfd,
	0x2b, 0xe1, 0xce, 0xb7, 0x98, 0x21, 0xd4, 0x81, 0x82, 0x3b, 0x84, 0x6c, 0xd2, 0x9d, 0xfa, 0x91,
	0x41, 0x53, 0x10, 0xba, 0x24, 0x67, 0xd2, 0xd5, 0xcb, 0xb3, 0x19, 0x65, 0x63, 0x8b, 0x66, 0xaf,
	0xcf, 0xd5, 0x9f, 0x6f, 0x89, 0xf5, 0x43, 0x62, 0xf6, 0xce, 0x9d, 0x35, 0xe7, 0x17, 0xa6, 0x67,
	0x91, 0xf6, 0x0b, 0x3f, 0x35, 0x92, 0x17, 0x4f, 0x8d, 0xd4, 0x25, 0xa7, 0xc6, 0xdc, 0x25, 0xa7,
	0xc6, 0xfc, 0x85, 0x53, 0x63, 0x21, 0x62, 0x6a, 0xfc, 0x5e, 0x80, 0xdb, 0xf1, 0x19, 0xb7, 0xcd,
	0xe8, 0x94, 0x0b, 0xff, 0x1f, 0x29, 0x97, 0xfe, 0x20, 0xc0, 0x75, 0xe6, 0x78, 0x9b, 0x6d, 0x0c,
	0x7e, 0xa5, 0x5c, 0xae, 0x41, 0x22, 0x16, 0xc9, 0x4b, 0xdd, 0x4d, 0xf7, 0x21, 0xe7, 0x6d, 0x30,
	0x34, 0xf7, 0xfc, 0xe5, 0x94, 0x75, 0x39, 0x34, 0xfb, 0x77, 0x01, 0xdc, 0x9a, 0x20, 0x89, 0x9b,
	0xe3, 0xaa, 0x22, 0xc3, 0xaa, 0x42, 0x3f, 0x32, 0xa4, 0xb2, 0x77, 0xe9, 0x06, 0x83, 0xb0, 0x4d,
	0xe9, 0x09, 0x5c, 0xdb, 0x33, 0x1c, 0xed, 0xe8, 0x9c, 0x1e, 0x38, 0xeb, 0xa7, 0xff, 0x25, 0x3a,
	0xa9, 0x09, 0x37, 0xbd, 0xed, 0x93, 0xe4, 0xb7, 0x6f, 0x38, 0x6e, 0xf3, 0x5d, 0x66, 0xc9, 0x96,
	0x7e, 0x29, 0x40, 0x25, 0x4e, 0xdd, 0x36, 0x49, 0xb0, 0xb4, 0xd4, 0x58, 0xea, 0x78, 0x0b, 0x19,
	0x42, 0xf7, 0x53, 0x37, 0xcd, 0x6f, 0xbd, 0x46, 0xa7, 0x54, 0xd2, 0x5f, 0x12, 0xbc, 0x0c, 0xd7,
	0x6b, 0x44, 0x70, 0x6a, 0xcd, 0x5d, 0x10, 0x7d, 0x41, 0xdf, 0x5e, 0xbd, 0x16, 0x5a, 0x7a, 0x7b,
	0x8a, 0x7e, 0xa2, 0xe9, 0xc3, 0xb7, 0x2d, 0xbd, 0x7f, 0x4c, 0x04, 0x96, 0x5e, 0x5f, 0xc3, 0x36,
	0xd1, 0x3a, 0xa0, 0x33, 0x8c, 0x4f, 0xd8, 0x84, 0x96, 0x2d, 0xc6, 0x09, 0x58, 0x28, 0x12, 0x3e,
	0x1d, 0xce, 0xae, 0x1e, 0x7a, 0x0f, 0x4a, 0xaa, 0x72, 0x1e, 0x52, 0xe1, 0xb3, 0x5f, 0x50, 0x95,
	0xf3, 0xb0, 0x06, 0x45, 0xa1, 0x89, 0xf0, 0x34, 0xf8, 0x52, 0x2b, 0x10, 0x36, 0xb9, 0x41, 0x3c,
	0x8d, 0x35, 0x28, 0x12, 0x8c, 0x80, 0x02, 0x5f, 0x74, 0x8b, 0xaa, 0x72, 0xce, 0xcb, 0xd7, 0xe0,
	0xea, 0xd8, 0xd0, 0x9d, 0xe3, 0x90, 0x57, 0xfc, 0x2c, 0x29, 0x51, 0x81, 0x80, 0x5f, 0xeb, 0x80,
	0x98, 0x56, 0x00, 0x87, 0x1f, 0x2c, 0x45, 0xca, 0xe7, 0x90, 0xa4, 0x7f, 0x0a, 0xb0, 0xb8, 0x8d,
	0x1d, 0xf2, 0xd3, 0x7b, 0xac, 0xf9, 0x6f, 0x31, 0xe1, 0x2d, 0x6f, 0xb1, 0xc4, 0xcc, 0x5b, 0x8c,
	0xde, 0xec, 0x8a, 0x7e, 0xc2, 0x5a, 0x8a, 0x4f, 0x47, 0x9a, 0x90, 0x69, 0x3f, 0xb9, 0x47, 0xca,
	0xcf, 0x4e, 0x7a, 0xa4, 0xc7, 0x90, 0xe9, 0x6d, 0xec, 0x3d, 0x96, 0xfb, 0xcf, 0xf6, 0x3b, 0x28,
	0x0f, 0x99, 0x6f, 0x75, 0x3a, 0x8f, 0xe5, 0x5e, 0xb7, 0xfd, 0xa8, 0x28, 0xa0, 0x1c, 0xa4, 0xb7,
	0x36, 0x9e, 0xb1, 0x5f, 0x09, 0xb4, 0x08, 0x40, 0x99, 0xed, 0x47, 0x1b, 0xbd, 0xdd, 0x62, 0x92,
	0x08, 0x13, 0x2e, 0xfb, 0x99, 0x22, 0xec, 0xdd, 0x27, 0x7b, 0xfd, 0x47, 0x4c, 0x7c, 0x0e, 0x15,
	0x20, 0xcb, 0x7e, 0x33, 0x81, 0x79, 0xe9, 0x37, 0x02, 0x14, 0x02, 0xf1, 0xda, 0x26, 0xba, 0xe5,
	0x3a, 0xce, 0x6d, 0x54, 0xd4, 0x65, 0x7a, 0x2d, 0x78, 0x51, 0x8d, 0x14, 0x3a, 0xeb, 0x84, 0x60,
	0x54, 0x3b, 0x0a, 0xbb, 0x39, 0xa8, 0x88, 0x6e, 0x9c, 0x05, 0xee, 0xe4, 0x05, 0x42, 0xdd, 0x33,
	0xce, 0x50, 0x15, 0x8a, 0x43, 0xc5, 0x94, 0x1d, 0x43, 0xb6, 0x34, 0x1b, 0xd3, 0x93, 0x09, 0xe4,
	0x20, 0x3f, 0x54, 0xcc, 0xbe, 0xd1, 0xd3, 0x6c, 0x4c, 0xfc, 0x92, 0x3e, 0x16, 0xa0, 0xb4, 0xa1,
	0xaa, 0x64, 0x54, 0x5e, 0xfc, 0x06, 0x24, 0xe8, 0x64, 0x25, 0xa0, 0x59, 0x0f, 0x0c, 0x0d, 0x45,
	0x55, 0xbd, 0x21, 0x16, 0x18, 0x89, 0xdc, 0xbb, 0x36, 0xb0, 0xae, 0xf3, 0xab, 0x35, 0xff, 0xb6,
	0xe1, 0x56, 0x6b, 0xe9, 0x14, 0x50, 0xd8, 0x33, 0xdb, 0x24, 0xe5, 0xcd, 0x56, 0x04, 0x0e, 0x87,
	0xf8, 0xe9, 0xbf, 0xef, 0x28, 0xb7, 0xe7, 0x83, 0xbd, 0x07, 0x25, 0x26, 0xcf, 0x43, 0x26, 0x38,
	0x85, 0x02, 0x65, 0xb7, 0xa7, 0xb8, 0x6b, 0x70, 0x6d, 0x1b, 0x3b, 0x04, 0x97, 0x58, 0x39, 0x38,
	0xd3, 0xe8, 0x95, 0x17, 0x3f, 0x23, 0xbe, 0x42, 0x87, 0x4a, 0x58, 0xde, 0x36, 0xc9, 0xfb, 0x0c,
	0xeb, 0xca, 0xf3, 0x11, 0x73, 0xd0, 0x5b, 0x78, 0x5c, 0x9a, 0xb4, 0x03, 0xd7, 0x0e, 0xfe, 0x0b,
	0x18, 0xce, 0x5a, 0x22, 0xc2, 0xda, 0x0d, 0xb8, 0x7e, 0x10, 0xe5, 0xc4, 0xbb, 0x5b, 0x90, 0x6b,
	0x3f, 0xd9, 0x3b, 0x38, 0xdc, 0xed, 0xb0, 0x8a, 0x2f, 0xc3, 0xb5, 0xce, 0xde, 0xe1, 0xae, 0xec,
	0x13, 0xfb, 0xf2, 0xf6, 0xc6, 0x6e, 0xa7, 0x28, 0xa0, 0x0a, 0x2c, 0x05, 0x38, 0x8f, 0x36, 0xf6,
	0xf7, 0x9f, 0xb5, 0xbb, 0xfd, 0x67, 0xc5, 0xc4, 0xbb, 0xbb, 0xe1, 0xb3, 0xa0, 0xa7, 0x7d, 0x13,
	0xae, 0x53, 0x8d, 0x8d, 0xad, 0x2d, 0x6a, 0x5c, 0xee, 0xee, 0x3d, 0xdd, 0xd8, 0xe9, 0x6e, 0x15,
	0xaf, 0xa0, 0xdb, 0x50, 0x0e, 0xb2, 0x76, 0xba, 0x4f, 0x3b, 0x72, 0xff, 0x89, 0xdc, 0xef, 0x17,
	0x85, 0xf5, 0x5f, 0x23, 0xc8, 0xb8, 0x86, 0xb6, 0x0d, 0xf4, 0x11, 0x5c, 0xf5, 0xae, 0x0a, 0xf7,
	0x5a, 0xa3, 0xdd, 0x20, 0x05, 0xbe, 0x66, 0x44, 0x7e, 0xec, 0xa9, 0xdc, 0xbd, 0x50, 0xc6, 0x36,
	0xa5, 0xe5, 0x1f, 0xbc, 0x7a, 0x93, 0x14, 0x7e, 0xfa, 0xea, 0x4d, 0x32, 0x31, 0x6c, 0xfc, 0xe2,
	0xd5, 0x9b, 0x64, 0xbe, 0x3a, 0x14, 0x9b, 0xde, 0xfd, 0xd5, 0x42, 0xdf, 0x87, 0x92, 0x8f, 0x4f,
	0xca, 0xe0, 0x73, 0x40, 0x4f, 0xc4, 0xa3, 0x9f, 0x02, 0x9a, 0x7d, 0xa2, 0xa3, 0x95, 0x90, 0xe9,
	0xd9, 0x6f, 0x13, 0x15, 0xe9, 0x22, 0x11, 0xdb, 0x94, 0x6e, 0x12, 0xf0, 0x24, 0x05, 0x9f, 0x50,
	0xf0, 0x74, 0x75, 0x22, 0x36, 0x27, 0x04, 0xf7, 0x2f, 0x02, 0x5c, 0x8d, 0xd8, 0xe4, 0x82, 0x81,
	0x47, 0x2f, 0xd7, 0xc1, 0xc0, 0x63, 0xd6, 0x41, 0xe9, 0x8c, 0x60, 0xa7, 0x08, 0xf6, 0xe2, 0xa4,
	0x31, 0x6c, 0x58, 0x0d, 0xa7, 0x31, 0x6e, 0x28, 0x0d, 0x83, 0xfa, 0xf1, 0x6d, 0xcf, 0x0f, 0x31,
	0x90, 0x0d, 0xb1, 0x6a, 0x89, 0x4d, 0xd2, 0xd9, 0x2d, 0xb1, 0xea, 0x88, 0x4d, 0x47, 0xb1, 0x86,
	0xd8, 0x61, 0x8c, 0xb1, 0xff, 0x93, 0x4a, 0xb7, 0xc4, 0xaa, 0x22, 0x36, 0x69, 0x53, 0xb7, 0xc4,
	0xaa, 0x21, 0x36, 0xbd, 0x85, 0x9a, 0x9c, 0x67, 0x81, 0x5b, 0x85, 0xbe, 0x39, 0xd1, 0x1c, 0x24,
	0xf2, 0x0e, 0x47, 0xed, 0x49, 0x95, 0xdb, 0x6b, 0xfe, 0x07, 0xc7, 0xb5, 0x83, 0xc7, 0x9b, 0xec,
	0x83, 0x63, 0x67, 0x6c, 0x3a, 0xe7, 0xf2, 0xfe, 0xa6, 0xf4, 0x80, 0xc4, 0x32, 0x4f, 0x62, 0x49,
	0x0d, 0x1b, 0x2c, 0x93, 0x4b, 0x21, 0xc7, 0xbd, 0xbc, 0x06, 0xd1, 0xbf, 0x61, 0x68, 0xfa, 0x67,
	0x83, 0xbe, 0x70, 0x29, 0xf4, 0x1f, 0x09, 0xd3, 0x0f, 0xa3, 0xc1, 0xbd, 0x0b, 0xdd, 0x8b, 0xaa,
	0xd6, 0x99, 0xd5, 0xae, 0xf2, 0xc5, 0xcb, 0x88, 0x79, 0x75, 0x9d, 0x8e, 0xaf, 0xeb, 0xdf, 0x0a,
	0x80, 0x66, 0x77, 0xd5, 0x60, 0x61, 0x47, 0x2e, 0xe4, 0x15, 0xe9, 0x22, 0x11, 0xdb, 0x94, 0x3a,
	0x04, 0x3d, 0x43, 0xd0, 0xd3, 0xac, 0xb8, 0x58, 0x59, 0xad, 0x5d, 0x5c, 0x56, 0x81, 0x52, 0x09,
	0x36, 0x9f, 0xb7, 0xdc, 0xc4, 0x35, 0xdf, 0x74, 0x61, 0x8c, 0x6d, 0x3e, 0x6e, 0x43, 0x64, 0xcd,
	0x07, 0x91, 0xcd, 0xf7, 0x11, 0x64, 0xb9, 0xcd, 0x00, 0x55, 0x42, 0xd6, 0xb8, 0x15, 0xa9, 0x72,
	0x2b, 0x96, 0x67, 0x9b, 0x52, 0x9d, 0x40, 0x64, 0x09, 0xc4, 0xfc, 0xa4, 0xa1, 0x37, 0x1c, 0x0a,
	0xb3, 0x32, 0x4d, 0x82, 0x2e, 0x36, 0x1d, 0xc3, 0xdc, 0x63, 0xdd, 0xe4, 0x6f, 0x4c, 0x2d, 0xf4,
	0x33, 0x01, 0x6e, 0xc4, 0x7c, 0x1d, 0x44, 0x81, 0x02, 0x88, 0xff, 0x36, 0x5a, 0xb9, 0x7f, 0x29,
	0x39, 0xdb, 0x94, 0x24, 0xe2, 0x64, 0x8e, 0xcb, 0x43, 0xc9, 0x75, 0xf0, 0x43, 0xef, 0xaf, 0x85,
	0xfe, 0x2d, 0x40, 0x39, 0xee, 0x71, 0x89, 0x66, 0x91, 0x62, 0xe6, 0xd2, 0xea, 0xe5, 0x04, 0x6d,
	0x53, 0xfa, 0x89, 0x40, 0x9c, 0xca, 0x13, 0xa7, 0x8a, 0xc1, 0xe9, 0x64, 0x52, 0x17, 0xbf, 0xf7,
	0xf9, 0xcd, 0x27, 0xb1, 0x6a, 0x8a, 0xcd, 0xe9, 0x6b, 0xbc, 0x85, 0x3e, 0x16, 0x60, 0x31, 0x78,
	0xbb, 0xa2, 0xc0, 0x13, 0x77, 0x66, 0x3f, 0xab, 0x2c, 0xbf, 0x8d, 0x6d, 0x9b, 0xd2, 0x36, 0x09,
	0x6e, 0xd1, 0x2d, 0x0b, 0xab, 0xf1, 0x92, 0x86, 0xb4, 0x3e, 0x0d, 0xc9, 0x12, 0x9b, 0x8a, 0xaa,
	0x8a, 0xd3, 0x15, 0xaa, 0x25, 0x56, 0x5f, 0x32, 0x1a, 0xb7, 0x26, 0xb5, 0x44, 0xf4, 0x43, 0x01,
	0xc4, 0xd9, 0xea, 0xfe, 0xba, 0x65, 0x8c, 0x77, 0x8c, 0x81, 0x32, 0x6a, 0x2b, 0x83, 0x63, 0xfc,
	0x99, 0xb6, 0x4b, 0x21, 0xb2, 0x5d, 0x6c, 0x7a, 0x43, 0x07, 0xb7, 0x9b, 0xe0, 0x54, 0x8d, 0xda,
	0xd8, 0x2a, 0x2b, 0x17, 0x48, 0x78, 0xa0, 0xc5, 0x38, 0xd0, 0x83, 0xb7, 0x83, 0x1e, 0x5c, 0x08,
	0x7a, 0x10, 0x0f, 0x5a, 0x8a, 0x02, 0xad, 0xcc, 0xff, 0xf8, 0xd5, 0x9b, 0xe4, 0x9f, 0xcf, 0x37,
	0x8b, 0x9f, 0xbc, 0x5e, 0x16, 0xfe, 0xf4, 0x7a, 0x59, 0xf8, 0xdb, 0xeb, 0x65, 0xe1, 0xe7, 0x7f,
	0x5f, 0xbe, 0xf2, 0x9f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x37, 0x6c, 0x35, 0xdf, 0x41, 0x1b, 0x00,
	0x00,
}
