// Code generated by protoc-gen-go. DO NOT EDIT.
// source: muse-social-rank/muse-social-rank.proto

package muse_social_rank // import "golang.52tt.com/protocol/services/muse-social-rank"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/extension/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type SocialCommunityRankType int32

const (
	SocialCommunityRankType_SOCIAL_COMMUNITY_RANK_TYPE_UNSPECIFIED SocialCommunityRankType = 0
	SocialCommunityRankType_SOCIAL_COMMUNITY_RANK_TYPE_WEEK        SocialCommunityRankType = 1
	SocialCommunityRankType_SOCIAL_COMMUNITY_RANK_TYPE_HOUR        SocialCommunityRankType = 2
)

var SocialCommunityRankType_name = map[int32]string{
	0: "SOCIAL_COMMUNITY_RANK_TYPE_UNSPECIFIED",
	1: "SOCIAL_COMMUNITY_RANK_TYPE_WEEK",
	2: "SOCIAL_COMMUNITY_RANK_TYPE_HOUR",
}
var SocialCommunityRankType_value = map[string]int32{
	"SOCIAL_COMMUNITY_RANK_TYPE_UNSPECIFIED": 0,
	"SOCIAL_COMMUNITY_RANK_TYPE_WEEK":        1,
	"SOCIAL_COMMUNITY_RANK_TYPE_HOUR":        2,
}

func (x SocialCommunityRankType) String() string {
	return proto.EnumName(SocialCommunityRankType_name, int32(x))
}
func (SocialCommunityRankType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_rank_a75277c75ea4c412, []int{0}
}

// 在房状态
type SocialCommunityRankBrandChannelStatus int32

const (
	SocialCommunityRankBrandChannelStatus_SOCIAL_COMMUNITY_RANK_BRAND_CHANNEL_STATUS_UNSPECIFIED SocialCommunityRankBrandChannelStatus = 0
	SocialCommunityRankBrandChannelStatus_SOCIAL_COMMUNITY_RANK_BRAND_CHANNEL_STATUS_CHAT        SocialCommunityRankBrandChannelStatus = 1
	SocialCommunityRankBrandChannelStatus_SOCIAL_COMMUNITY_RANK_BRAND_CHANNEL_STATUS_SHOW        SocialCommunityRankBrandChannelStatus = 2
)

var SocialCommunityRankBrandChannelStatus_name = map[int32]string{
	0: "SOCIAL_COMMUNITY_RANK_BRAND_CHANNEL_STATUS_UNSPECIFIED",
	1: "SOCIAL_COMMUNITY_RANK_BRAND_CHANNEL_STATUS_CHAT",
	2: "SOCIAL_COMMUNITY_RANK_BRAND_CHANNEL_STATUS_SHOW",
}
var SocialCommunityRankBrandChannelStatus_value = map[string]int32{
	"SOCIAL_COMMUNITY_RANK_BRAND_CHANNEL_STATUS_UNSPECIFIED": 0,
	"SOCIAL_COMMUNITY_RANK_BRAND_CHANNEL_STATUS_CHAT":        1,
	"SOCIAL_COMMUNITY_RANK_BRAND_CHANNEL_STATUS_SHOW":        2,
}

func (x SocialCommunityRankBrandChannelStatus) String() string {
	return proto.EnumName(SocialCommunityRankBrandChannelStatus_name, int32(x))
}
func (SocialCommunityRankBrandChannelStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_rank_a75277c75ea4c412, []int{1}
}

// 榜单顶部品类tab列表
type GetSocialCommunityRankTopTabListRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSocialCommunityRankTopTabListRequest) Reset() {
	*m = GetSocialCommunityRankTopTabListRequest{}
}
func (m *GetSocialCommunityRankTopTabListRequest) String() string { return proto.CompactTextString(m) }
func (*GetSocialCommunityRankTopTabListRequest) ProtoMessage()    {}
func (*GetSocialCommunityRankTopTabListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_rank_a75277c75ea4c412, []int{0}
}
func (m *GetSocialCommunityRankTopTabListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocialCommunityRankTopTabListRequest.Unmarshal(m, b)
}
func (m *GetSocialCommunityRankTopTabListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocialCommunityRankTopTabListRequest.Marshal(b, m, deterministic)
}
func (dst *GetSocialCommunityRankTopTabListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocialCommunityRankTopTabListRequest.Merge(dst, src)
}
func (m *GetSocialCommunityRankTopTabListRequest) XXX_Size() int {
	return xxx_messageInfo_GetSocialCommunityRankTopTabListRequest.Size(m)
}
func (m *GetSocialCommunityRankTopTabListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocialCommunityRankTopTabListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocialCommunityRankTopTabListRequest proto.InternalMessageInfo

func (m *GetSocialCommunityRankTopTabListRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type TopTabInfo struct {
	TabName              string   `protobuf:"bytes,1,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	TabCategoryId        string   `protobuf:"bytes,2,opt,name=tab_category_id,json=tabCategoryId,proto3" json:"tab_category_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TopTabInfo) Reset()         { *m = TopTabInfo{} }
func (m *TopTabInfo) String() string { return proto.CompactTextString(m) }
func (*TopTabInfo) ProtoMessage()    {}
func (*TopTabInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_rank_a75277c75ea4c412, []int{1}
}
func (m *TopTabInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopTabInfo.Unmarshal(m, b)
}
func (m *TopTabInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopTabInfo.Marshal(b, m, deterministic)
}
func (dst *TopTabInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopTabInfo.Merge(dst, src)
}
func (m *TopTabInfo) XXX_Size() int {
	return xxx_messageInfo_TopTabInfo.Size(m)
}
func (m *TopTabInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TopTabInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TopTabInfo proto.InternalMessageInfo

func (m *TopTabInfo) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *TopTabInfo) GetTabCategoryId() string {
	if m != nil {
		return m.TabCategoryId
	}
	return ""
}

type GetSocialCommunityRankTopTabListResponse struct {
	TabList              []*TopTabInfo `protobuf:"bytes,1,rep,name=tab_list,json=tabList,proto3" json:"tab_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetSocialCommunityRankTopTabListResponse) Reset() {
	*m = GetSocialCommunityRankTopTabListResponse{}
}
func (m *GetSocialCommunityRankTopTabListResponse) String() string { return proto.CompactTextString(m) }
func (*GetSocialCommunityRankTopTabListResponse) ProtoMessage()    {}
func (*GetSocialCommunityRankTopTabListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_rank_a75277c75ea4c412, []int{2}
}
func (m *GetSocialCommunityRankTopTabListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocialCommunityRankTopTabListResponse.Unmarshal(m, b)
}
func (m *GetSocialCommunityRankTopTabListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocialCommunityRankTopTabListResponse.Marshal(b, m, deterministic)
}
func (dst *GetSocialCommunityRankTopTabListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocialCommunityRankTopTabListResponse.Merge(dst, src)
}
func (m *GetSocialCommunityRankTopTabListResponse) XXX_Size() int {
	return xxx_messageInfo_GetSocialCommunityRankTopTabListResponse.Size(m)
}
func (m *GetSocialCommunityRankTopTabListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocialCommunityRankTopTabListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocialCommunityRankTopTabListResponse proto.InternalMessageInfo

func (m *GetSocialCommunityRankTopTabListResponse) GetTabList() []*TopTabInfo {
	if m != nil {
		return m.TabList
	}
	return nil
}

// 榜单
type GetSocialCommunityRankRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TabCategoryId        string   `protobuf:"bytes,2,opt,name=tab_category_id,json=tabCategoryId,proto3" json:"tab_category_id,omitempty"`
	RankType             uint32   `protobuf:"varint,3,opt,name=rank_type,json=rankType,proto3" json:"rank_type,omitempty"`
	IsNextWeekRankData   bool     `protobuf:"varint,4,opt,name=is_next_week_rank_data,json=isNextWeekRankData,proto3" json:"is_next_week_rank_data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSocialCommunityRankRequest) Reset()         { *m = GetSocialCommunityRankRequest{} }
func (m *GetSocialCommunityRankRequest) String() string { return proto.CompactTextString(m) }
func (*GetSocialCommunityRankRequest) ProtoMessage()    {}
func (*GetSocialCommunityRankRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_rank_a75277c75ea4c412, []int{3}
}
func (m *GetSocialCommunityRankRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocialCommunityRankRequest.Unmarshal(m, b)
}
func (m *GetSocialCommunityRankRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocialCommunityRankRequest.Marshal(b, m, deterministic)
}
func (dst *GetSocialCommunityRankRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocialCommunityRankRequest.Merge(dst, src)
}
func (m *GetSocialCommunityRankRequest) XXX_Size() int {
	return xxx_messageInfo_GetSocialCommunityRankRequest.Size(m)
}
func (m *GetSocialCommunityRankRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocialCommunityRankRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocialCommunityRankRequest proto.InternalMessageInfo

func (m *GetSocialCommunityRankRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetSocialCommunityRankRequest) GetTabCategoryId() string {
	if m != nil {
		return m.TabCategoryId
	}
	return ""
}

func (m *GetSocialCommunityRankRequest) GetRankType() uint32 {
	if m != nil {
		return m.RankType
	}
	return 0
}

func (m *GetSocialCommunityRankRequest) GetIsNextWeekRankData() bool {
	if m != nil {
		return m.IsNextWeekRankData
	}
	return false
}

type SocialCommunityRankBrandInfo struct {
	BrandId              string   `protobuf:"bytes,1,opt,name=brand_id,json=brandId,proto3" json:"brand_id,omitempty"`
	BrandLogo            string   `protobuf:"bytes,2,opt,name=brand_logo,json=brandLogo,proto3" json:"brand_logo,omitempty"`
	BrandName            string   `protobuf:"bytes,3,opt,name=brand_name,json=brandName,proto3" json:"brand_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SocialCommunityRankBrandInfo) Reset()         { *m = SocialCommunityRankBrandInfo{} }
func (m *SocialCommunityRankBrandInfo) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityRankBrandInfo) ProtoMessage()    {}
func (*SocialCommunityRankBrandInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_rank_a75277c75ea4c412, []int{4}
}
func (m *SocialCommunityRankBrandInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityRankBrandInfo.Unmarshal(m, b)
}
func (m *SocialCommunityRankBrandInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityRankBrandInfo.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityRankBrandInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityRankBrandInfo.Merge(dst, src)
}
func (m *SocialCommunityRankBrandInfo) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityRankBrandInfo.Size(m)
}
func (m *SocialCommunityRankBrandInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityRankBrandInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityRankBrandInfo proto.InternalMessageInfo

func (m *SocialCommunityRankBrandInfo) GetBrandId() string {
	if m != nil {
		return m.BrandId
	}
	return ""
}

func (m *SocialCommunityRankBrandInfo) GetBrandLogo() string {
	if m != nil {
		return m.BrandLogo
	}
	return ""
}

func (m *SocialCommunityRankBrandInfo) GetBrandName() string {
	if m != nil {
		return m.BrandName
	}
	return ""
}

type SocialCommunityRankElementInfo struct {
	BrandRankInfo        *SocialCommunityRankBrandInfo `protobuf:"bytes,1,opt,name=brand_rank_info,json=brandRankInfo,proto3" json:"brand_rank_info,omitempty"`
	Impact               uint32                        `protobuf:"varint,2,opt,name=impact,proto3" json:"impact,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *SocialCommunityRankElementInfo) Reset()         { *m = SocialCommunityRankElementInfo{} }
func (m *SocialCommunityRankElementInfo) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityRankElementInfo) ProtoMessage()    {}
func (*SocialCommunityRankElementInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_rank_a75277c75ea4c412, []int{5}
}
func (m *SocialCommunityRankElementInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityRankElementInfo.Unmarshal(m, b)
}
func (m *SocialCommunityRankElementInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityRankElementInfo.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityRankElementInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityRankElementInfo.Merge(dst, src)
}
func (m *SocialCommunityRankElementInfo) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityRankElementInfo.Size(m)
}
func (m *SocialCommunityRankElementInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityRankElementInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityRankElementInfo proto.InternalMessageInfo

func (m *SocialCommunityRankElementInfo) GetBrandRankInfo() *SocialCommunityRankBrandInfo {
	if m != nil {
		return m.BrandRankInfo
	}
	return nil
}

func (m *SocialCommunityRankElementInfo) GetImpact() uint32 {
	if m != nil {
		return m.Impact
	}
	return 0
}

type SocialCommunityRankMeSortInfo struct {
	BrandRankInfo        *SocialCommunityRankElementInfo `protobuf:"bytes,1,opt,name=brand_rank_info,json=brandRankInfo,proto3" json:"brand_rank_info,omitempty"`
	ThisWeekRankIndex    uint32                          `protobuf:"varint,2,opt,name=this_week_rank_index,json=thisWeekRankIndex,proto3" json:"this_week_rank_index,omitempty"`
	NextWeekRankIndex    uint32                          `protobuf:"varint,3,opt,name=next_week_rank_index,json=nextWeekRankIndex,proto3" json:"next_week_rank_index,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *SocialCommunityRankMeSortInfo) Reset()         { *m = SocialCommunityRankMeSortInfo{} }
func (m *SocialCommunityRankMeSortInfo) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityRankMeSortInfo) ProtoMessage()    {}
func (*SocialCommunityRankMeSortInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_rank_a75277c75ea4c412, []int{6}
}
func (m *SocialCommunityRankMeSortInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityRankMeSortInfo.Unmarshal(m, b)
}
func (m *SocialCommunityRankMeSortInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityRankMeSortInfo.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityRankMeSortInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityRankMeSortInfo.Merge(dst, src)
}
func (m *SocialCommunityRankMeSortInfo) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityRankMeSortInfo.Size(m)
}
func (m *SocialCommunityRankMeSortInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityRankMeSortInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityRankMeSortInfo proto.InternalMessageInfo

func (m *SocialCommunityRankMeSortInfo) GetBrandRankInfo() *SocialCommunityRankElementInfo {
	if m != nil {
		return m.BrandRankInfo
	}
	return nil
}

func (m *SocialCommunityRankMeSortInfo) GetThisWeekRankIndex() uint32 {
	if m != nil {
		return m.ThisWeekRankIndex
	}
	return 0
}

func (m *SocialCommunityRankMeSortInfo) GetNextWeekRankIndex() uint32 {
	if m != nil {
		return m.NextWeekRankIndex
	}
	return 0
}

type GetSocialCommunityRankResponse struct {
	RankElementList      []*SocialCommunityRankElementInfo `protobuf:"bytes,1,rep,name=rank_element_list,json=rankElementList,proto3" json:"rank_element_list,omitempty"`
	MeRankInfo           *SocialCommunityRankMeSortInfo    `protobuf:"bytes,2,opt,name=me_rank_info,json=meRankInfo,proto3" json:"me_rank_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *GetSocialCommunityRankResponse) Reset()         { *m = GetSocialCommunityRankResponse{} }
func (m *GetSocialCommunityRankResponse) String() string { return proto.CompactTextString(m) }
func (*GetSocialCommunityRankResponse) ProtoMessage()    {}
func (*GetSocialCommunityRankResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_rank_a75277c75ea4c412, []int{7}
}
func (m *GetSocialCommunityRankResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocialCommunityRankResponse.Unmarshal(m, b)
}
func (m *GetSocialCommunityRankResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocialCommunityRankResponse.Marshal(b, m, deterministic)
}
func (dst *GetSocialCommunityRankResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocialCommunityRankResponse.Merge(dst, src)
}
func (m *GetSocialCommunityRankResponse) XXX_Size() int {
	return xxx_messageInfo_GetSocialCommunityRankResponse.Size(m)
}
func (m *GetSocialCommunityRankResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocialCommunityRankResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocialCommunityRankResponse proto.InternalMessageInfo

func (m *GetSocialCommunityRankResponse) GetRankElementList() []*SocialCommunityRankElementInfo {
	if m != nil {
		return m.RankElementList
	}
	return nil
}

func (m *GetSocialCommunityRankResponse) GetMeRankInfo() *SocialCommunityRankMeSortInfo {
	if m != nil {
		return m.MeRankInfo
	}
	return nil
}

// 榜单社群状态
type GetSocialCommunityRankBrandChannelStatusRequest struct {
	TabCategoryId        string   `protobuf:"bytes,1,opt,name=tab_category_id,json=tabCategoryId,proto3" json:"tab_category_id,omitempty"`
	RankType             uint32   `protobuf:"varint,2,opt,name=rank_type,json=rankType,proto3" json:"rank_type,omitempty"`
	BrandIdList          []string `protobuf:"bytes,3,rep,name=brand_id_list,json=brandIdList,proto3" json:"brand_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSocialCommunityRankBrandChannelStatusRequest) Reset() {
	*m = GetSocialCommunityRankBrandChannelStatusRequest{}
}
func (m *GetSocialCommunityRankBrandChannelStatusRequest) String() string {
	return proto.CompactTextString(m)
}
func (*GetSocialCommunityRankBrandChannelStatusRequest) ProtoMessage() {}
func (*GetSocialCommunityRankBrandChannelStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_rank_a75277c75ea4c412, []int{8}
}
func (m *GetSocialCommunityRankBrandChannelStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocialCommunityRankBrandChannelStatusRequest.Unmarshal(m, b)
}
func (m *GetSocialCommunityRankBrandChannelStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocialCommunityRankBrandChannelStatusRequest.Marshal(b, m, deterministic)
}
func (dst *GetSocialCommunityRankBrandChannelStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocialCommunityRankBrandChannelStatusRequest.Merge(dst, src)
}
func (m *GetSocialCommunityRankBrandChannelStatusRequest) XXX_Size() int {
	return xxx_messageInfo_GetSocialCommunityRankBrandChannelStatusRequest.Size(m)
}
func (m *GetSocialCommunityRankBrandChannelStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocialCommunityRankBrandChannelStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocialCommunityRankBrandChannelStatusRequest proto.InternalMessageInfo

func (m *GetSocialCommunityRankBrandChannelStatusRequest) GetTabCategoryId() string {
	if m != nil {
		return m.TabCategoryId
	}
	return ""
}

func (m *GetSocialCommunityRankBrandChannelStatusRequest) GetRankType() uint32 {
	if m != nil {
		return m.RankType
	}
	return 0
}

func (m *GetSocialCommunityRankBrandChannelStatusRequest) GetBrandIdList() []string {
	if m != nil {
		return m.BrandIdList
	}
	return nil
}

type SocialCommunityRankBrandChannelStatusInfo struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelStatus        uint32   `protobuf:"varint,2,opt,name=channel_status,json=channelStatus,proto3" json:"channel_status,omitempty"`
	UserCount            uint32   `protobuf:"varint,3,opt,name=user_count,json=userCount,proto3" json:"user_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SocialCommunityRankBrandChannelStatusInfo) Reset() {
	*m = SocialCommunityRankBrandChannelStatusInfo{}
}
func (m *SocialCommunityRankBrandChannelStatusInfo) String() string {
	return proto.CompactTextString(m)
}
func (*SocialCommunityRankBrandChannelStatusInfo) ProtoMessage() {}
func (*SocialCommunityRankBrandChannelStatusInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_rank_a75277c75ea4c412, []int{9}
}
func (m *SocialCommunityRankBrandChannelStatusInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityRankBrandChannelStatusInfo.Unmarshal(m, b)
}
func (m *SocialCommunityRankBrandChannelStatusInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityRankBrandChannelStatusInfo.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityRankBrandChannelStatusInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityRankBrandChannelStatusInfo.Merge(dst, src)
}
func (m *SocialCommunityRankBrandChannelStatusInfo) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityRankBrandChannelStatusInfo.Size(m)
}
func (m *SocialCommunityRankBrandChannelStatusInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityRankBrandChannelStatusInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityRankBrandChannelStatusInfo proto.InternalMessageInfo

func (m *SocialCommunityRankBrandChannelStatusInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SocialCommunityRankBrandChannelStatusInfo) GetChannelStatus() uint32 {
	if m != nil {
		return m.ChannelStatus
	}
	return 0
}

func (m *SocialCommunityRankBrandChannelStatusInfo) GetUserCount() uint32 {
	if m != nil {
		return m.UserCount
	}
	return 0
}

type GetSocialCommunityRankBrandChannelStatusResponse struct {
	ChannelStatusList    []*SocialCommunityRankBrandChannelStatusInfo `protobuf:"bytes,1,rep,name=channel_status_list,json=channelStatusList,proto3" json:"channel_status_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                     `json:"-"`
	XXX_unrecognized     []byte                                       `json:"-"`
	XXX_sizecache        int32                                        `json:"-"`
}

func (m *GetSocialCommunityRankBrandChannelStatusResponse) Reset() {
	*m = GetSocialCommunityRankBrandChannelStatusResponse{}
}
func (m *GetSocialCommunityRankBrandChannelStatusResponse) String() string {
	return proto.CompactTextString(m)
}
func (*GetSocialCommunityRankBrandChannelStatusResponse) ProtoMessage() {}
func (*GetSocialCommunityRankBrandChannelStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_rank_a75277c75ea4c412, []int{10}
}
func (m *GetSocialCommunityRankBrandChannelStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocialCommunityRankBrandChannelStatusResponse.Unmarshal(m, b)
}
func (m *GetSocialCommunityRankBrandChannelStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocialCommunityRankBrandChannelStatusResponse.Marshal(b, m, deterministic)
}
func (dst *GetSocialCommunityRankBrandChannelStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocialCommunityRankBrandChannelStatusResponse.Merge(dst, src)
}
func (m *GetSocialCommunityRankBrandChannelStatusResponse) XXX_Size() int {
	return xxx_messageInfo_GetSocialCommunityRankBrandChannelStatusResponse.Size(m)
}
func (m *GetSocialCommunityRankBrandChannelStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocialCommunityRankBrandChannelStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocialCommunityRankBrandChannelStatusResponse proto.InternalMessageInfo

func (m *GetSocialCommunityRankBrandChannelStatusResponse) GetChannelStatusList() []*SocialCommunityRankBrandChannelStatusInfo {
	if m != nil {
		return m.ChannelStatusList
	}
	return nil
}

type OnRankCheckRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OnRankCheckRequest) Reset()         { *m = OnRankCheckRequest{} }
func (m *OnRankCheckRequest) String() string { return proto.CompactTextString(m) }
func (*OnRankCheckRequest) ProtoMessage()    {}
func (*OnRankCheckRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_rank_a75277c75ea4c412, []int{11}
}
func (m *OnRankCheckRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OnRankCheckRequest.Unmarshal(m, b)
}
func (m *OnRankCheckRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OnRankCheckRequest.Marshal(b, m, deterministic)
}
func (dst *OnRankCheckRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OnRankCheckRequest.Merge(dst, src)
}
func (m *OnRankCheckRequest) XXX_Size() int {
	return xxx_messageInfo_OnRankCheckRequest.Size(m)
}
func (m *OnRankCheckRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OnRankCheckRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OnRankCheckRequest proto.InternalMessageInfo

func (m *OnRankCheckRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type OnRankCheckResponse struct {
	IsOnRank               bool                          `protobuf:"varint,1,opt,name=is_on_rank,json=isOnRank,proto3" json:"is_on_rank,omitempty"`
	BrandRankInfo          *SocialCommunityRankBrandInfo `protobuf:"bytes,2,opt,name=brand_rank_info,json=brandRankInfo,proto3" json:"brand_rank_info,omitempty"`
	CategoryTypeSimpleDesc string                        `protobuf:"bytes,3,opt,name=category_type_simple_desc,json=categoryTypeSimpleDesc,proto3" json:"category_type_simple_desc,omitempty"`
	CategoryName           string                        `protobuf:"bytes,4,opt,name=category_name,json=categoryName,proto3" json:"category_name,omitempty"`
	RankNo                 string                        `protobuf:"bytes,5,opt,name=rank_no,json=rankNo,proto3" json:"rank_no,omitempty"`
	CategoryId             string                        `protobuf:"bytes,6,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}                      `json:"-"`
	XXX_unrecognized       []byte                        `json:"-"`
	XXX_sizecache          int32                         `json:"-"`
}

func (m *OnRankCheckResponse) Reset()         { *m = OnRankCheckResponse{} }
func (m *OnRankCheckResponse) String() string { return proto.CompactTextString(m) }
func (*OnRankCheckResponse) ProtoMessage()    {}
func (*OnRankCheckResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_rank_a75277c75ea4c412, []int{12}
}
func (m *OnRankCheckResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OnRankCheckResponse.Unmarshal(m, b)
}
func (m *OnRankCheckResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OnRankCheckResponse.Marshal(b, m, deterministic)
}
func (dst *OnRankCheckResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OnRankCheckResponse.Merge(dst, src)
}
func (m *OnRankCheckResponse) XXX_Size() int {
	return xxx_messageInfo_OnRankCheckResponse.Size(m)
}
func (m *OnRankCheckResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OnRankCheckResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OnRankCheckResponse proto.InternalMessageInfo

func (m *OnRankCheckResponse) GetIsOnRank() bool {
	if m != nil {
		return m.IsOnRank
	}
	return false
}

func (m *OnRankCheckResponse) GetBrandRankInfo() *SocialCommunityRankBrandInfo {
	if m != nil {
		return m.BrandRankInfo
	}
	return nil
}

func (m *OnRankCheckResponse) GetCategoryTypeSimpleDesc() string {
	if m != nil {
		return m.CategoryTypeSimpleDesc
	}
	return ""
}

func (m *OnRankCheckResponse) GetCategoryName() string {
	if m != nil {
		return m.CategoryName
	}
	return ""
}

func (m *OnRankCheckResponse) GetRankNo() string {
	if m != nil {
		return m.RankNo
	}
	return ""
}

func (m *OnRankCheckResponse) GetCategoryId() string {
	if m != nil {
		return m.CategoryId
	}
	return ""
}

// 根据房间（或者社群ID）查榜单（周榜）的荣誉标识
type GetRankHonorSignByChannelIdsRequest struct {
	ChannelIdList        []uint32 `protobuf:"varint,1,rep,packed,name=channel_id_list,json=channelIdList,proto3" json:"channel_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRankHonorSignByChannelIdsRequest) Reset()         { *m = GetRankHonorSignByChannelIdsRequest{} }
func (m *GetRankHonorSignByChannelIdsRequest) String() string { return proto.CompactTextString(m) }
func (*GetRankHonorSignByChannelIdsRequest) ProtoMessage()    {}
func (*GetRankHonorSignByChannelIdsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_rank_a75277c75ea4c412, []int{13}
}
func (m *GetRankHonorSignByChannelIdsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRankHonorSignByChannelIdsRequest.Unmarshal(m, b)
}
func (m *GetRankHonorSignByChannelIdsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRankHonorSignByChannelIdsRequest.Marshal(b, m, deterministic)
}
func (dst *GetRankHonorSignByChannelIdsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRankHonorSignByChannelIdsRequest.Merge(dst, src)
}
func (m *GetRankHonorSignByChannelIdsRequest) XXX_Size() int {
	return xxx_messageInfo_GetRankHonorSignByChannelIdsRequest.Size(m)
}
func (m *GetRankHonorSignByChannelIdsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRankHonorSignByChannelIdsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRankHonorSignByChannelIdsRequest proto.InternalMessageInfo

func (m *GetRankHonorSignByChannelIdsRequest) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type SocialRankHonorSignInfo struct {
	Icon                 string   `protobuf:"bytes,1,opt,name=icon,proto3" json:"icon,omitempty"`
	StyleColorList       []string `protobuf:"bytes,2,rep,name=style_color_list,json=styleColorList,proto3" json:"style_color_list,omitempty"`
	Text                 string   `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	CategoryId           string   `protobuf:"bytes,4,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	RankType             uint32   `protobuf:"varint,5,opt,name=rank_type,json=rankType,proto3" json:"rank_type,omitempty"`
	SocialCommunityId    string   `protobuf:"bytes,6,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	RankInChannelText    string   `protobuf:"bytes,7,opt,name=RankInChannelText,proto3" json:"RankInChannelText,omitempty"`
	JumpRankUrl          string   `protobuf:"bytes,8,opt,name=jump_rank_url,json=jumpRankUrl,proto3" json:"jump_rank_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SocialRankHonorSignInfo) Reset()         { *m = SocialRankHonorSignInfo{} }
func (m *SocialRankHonorSignInfo) String() string { return proto.CompactTextString(m) }
func (*SocialRankHonorSignInfo) ProtoMessage()    {}
func (*SocialRankHonorSignInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_rank_a75277c75ea4c412, []int{14}
}
func (m *SocialRankHonorSignInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialRankHonorSignInfo.Unmarshal(m, b)
}
func (m *SocialRankHonorSignInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialRankHonorSignInfo.Marshal(b, m, deterministic)
}
func (dst *SocialRankHonorSignInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialRankHonorSignInfo.Merge(dst, src)
}
func (m *SocialRankHonorSignInfo) XXX_Size() int {
	return xxx_messageInfo_SocialRankHonorSignInfo.Size(m)
}
func (m *SocialRankHonorSignInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialRankHonorSignInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SocialRankHonorSignInfo proto.InternalMessageInfo

func (m *SocialRankHonorSignInfo) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *SocialRankHonorSignInfo) GetStyleColorList() []string {
	if m != nil {
		return m.StyleColorList
	}
	return nil
}

func (m *SocialRankHonorSignInfo) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *SocialRankHonorSignInfo) GetCategoryId() string {
	if m != nil {
		return m.CategoryId
	}
	return ""
}

func (m *SocialRankHonorSignInfo) GetRankType() uint32 {
	if m != nil {
		return m.RankType
	}
	return 0
}

func (m *SocialRankHonorSignInfo) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

func (m *SocialRankHonorSignInfo) GetRankInChannelText() string {
	if m != nil {
		return m.RankInChannelText
	}
	return ""
}

func (m *SocialRankHonorSignInfo) GetJumpRankUrl() string {
	if m != nil {
		return m.JumpRankUrl
	}
	return ""
}

type GetRankHonorSignByChannelIdsResponse struct {
	ChannelHonorSignMap  map[uint32]*SocialRankHonorSignInfo `protobuf:"bytes,1,rep,name=channel_honor_sign_map,json=channelHonorSignMap,proto3" json:"channel_honor_sign_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *GetRankHonorSignByChannelIdsResponse) Reset()         { *m = GetRankHonorSignByChannelIdsResponse{} }
func (m *GetRankHonorSignByChannelIdsResponse) String() string { return proto.CompactTextString(m) }
func (*GetRankHonorSignByChannelIdsResponse) ProtoMessage()    {}
func (*GetRankHonorSignByChannelIdsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_rank_a75277c75ea4c412, []int{15}
}
func (m *GetRankHonorSignByChannelIdsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRankHonorSignByChannelIdsResponse.Unmarshal(m, b)
}
func (m *GetRankHonorSignByChannelIdsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRankHonorSignByChannelIdsResponse.Marshal(b, m, deterministic)
}
func (dst *GetRankHonorSignByChannelIdsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRankHonorSignByChannelIdsResponse.Merge(dst, src)
}
func (m *GetRankHonorSignByChannelIdsResponse) XXX_Size() int {
	return xxx_messageInfo_GetRankHonorSignByChannelIdsResponse.Size(m)
}
func (m *GetRankHonorSignByChannelIdsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRankHonorSignByChannelIdsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRankHonorSignByChannelIdsResponse proto.InternalMessageInfo

func (m *GetRankHonorSignByChannelIdsResponse) GetChannelHonorSignMap() map[uint32]*SocialRankHonorSignInfo {
	if m != nil {
		return m.ChannelHonorSignMap
	}
	return nil
}

// 根据社群ID 查榜单（周榜）的荣誉标识
type GetRankHonorSignBySocialCommunityIdRequest struct {
	SocialCommunityId    string   `protobuf:"bytes,1,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRankHonorSignBySocialCommunityIdRequest) Reset() {
	*m = GetRankHonorSignBySocialCommunityIdRequest{}
}
func (m *GetRankHonorSignBySocialCommunityIdRequest) String() string {
	return proto.CompactTextString(m)
}
func (*GetRankHonorSignBySocialCommunityIdRequest) ProtoMessage() {}
func (*GetRankHonorSignBySocialCommunityIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_rank_a75277c75ea4c412, []int{16}
}
func (m *GetRankHonorSignBySocialCommunityIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRankHonorSignBySocialCommunityIdRequest.Unmarshal(m, b)
}
func (m *GetRankHonorSignBySocialCommunityIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRankHonorSignBySocialCommunityIdRequest.Marshal(b, m, deterministic)
}
func (dst *GetRankHonorSignBySocialCommunityIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRankHonorSignBySocialCommunityIdRequest.Merge(dst, src)
}
func (m *GetRankHonorSignBySocialCommunityIdRequest) XXX_Size() int {
	return xxx_messageInfo_GetRankHonorSignBySocialCommunityIdRequest.Size(m)
}
func (m *GetRankHonorSignBySocialCommunityIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRankHonorSignBySocialCommunityIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRankHonorSignBySocialCommunityIdRequest proto.InternalMessageInfo

func (m *GetRankHonorSignBySocialCommunityIdRequest) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

type GetRankHonorSignBySocialCommunityIdResponse struct {
	RankHonorSign        *SocialRankHonorSignInfo `protobuf:"bytes,1,opt,name=rank_honor_sign,json=rankHonorSign,proto3" json:"rank_honor_sign,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetRankHonorSignBySocialCommunityIdResponse) Reset() {
	*m = GetRankHonorSignBySocialCommunityIdResponse{}
}
func (m *GetRankHonorSignBySocialCommunityIdResponse) String() string {
	return proto.CompactTextString(m)
}
func (*GetRankHonorSignBySocialCommunityIdResponse) ProtoMessage() {}
func (*GetRankHonorSignBySocialCommunityIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_rank_a75277c75ea4c412, []int{17}
}
func (m *GetRankHonorSignBySocialCommunityIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRankHonorSignBySocialCommunityIdResponse.Unmarshal(m, b)
}
func (m *GetRankHonorSignBySocialCommunityIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRankHonorSignBySocialCommunityIdResponse.Marshal(b, m, deterministic)
}
func (dst *GetRankHonorSignBySocialCommunityIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRankHonorSignBySocialCommunityIdResponse.Merge(dst, src)
}
func (m *GetRankHonorSignBySocialCommunityIdResponse) XXX_Size() int {
	return xxx_messageInfo_GetRankHonorSignBySocialCommunityIdResponse.Size(m)
}
func (m *GetRankHonorSignBySocialCommunityIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRankHonorSignBySocialCommunityIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRankHonorSignBySocialCommunityIdResponse proto.InternalMessageInfo

func (m *GetRankHonorSignBySocialCommunityIdResponse) GetRankHonorSign() *SocialRankHonorSignInfo {
	if m != nil {
		return m.RankHonorSign
	}
	return nil
}

// 根据社群ID查榜单（周榜）的荣誉标识
type GetRankHonorSignBySocialCommunityIdsRequest struct {
	SocialCommunityIdList []string `protobuf:"bytes,1,rep,name=social_community_id_list,json=socialCommunityIdList,proto3" json:"social_community_id_list,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *GetRankHonorSignBySocialCommunityIdsRequest) Reset() {
	*m = GetRankHonorSignBySocialCommunityIdsRequest{}
}
func (m *GetRankHonorSignBySocialCommunityIdsRequest) String() string {
	return proto.CompactTextString(m)
}
func (*GetRankHonorSignBySocialCommunityIdsRequest) ProtoMessage() {}
func (*GetRankHonorSignBySocialCommunityIdsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_rank_a75277c75ea4c412, []int{18}
}
func (m *GetRankHonorSignBySocialCommunityIdsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRankHonorSignBySocialCommunityIdsRequest.Unmarshal(m, b)
}
func (m *GetRankHonorSignBySocialCommunityIdsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRankHonorSignBySocialCommunityIdsRequest.Marshal(b, m, deterministic)
}
func (dst *GetRankHonorSignBySocialCommunityIdsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRankHonorSignBySocialCommunityIdsRequest.Merge(dst, src)
}
func (m *GetRankHonorSignBySocialCommunityIdsRequest) XXX_Size() int {
	return xxx_messageInfo_GetRankHonorSignBySocialCommunityIdsRequest.Size(m)
}
func (m *GetRankHonorSignBySocialCommunityIdsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRankHonorSignBySocialCommunityIdsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRankHonorSignBySocialCommunityIdsRequest proto.InternalMessageInfo

func (m *GetRankHonorSignBySocialCommunityIdsRequest) GetSocialCommunityIdList() []string {
	if m != nil {
		return m.SocialCommunityIdList
	}
	return nil
}

type GetRankInfoBySocialCommunityIdResponse struct {
	ChannelHonorSignMap  map[string]*SocialRankHonorSignInfo `protobuf:"bytes,1,rep,name=channel_honor_sign_map,json=channelHonorSignMap,proto3" json:"channel_honor_sign_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *GetRankInfoBySocialCommunityIdResponse) Reset() {
	*m = GetRankInfoBySocialCommunityIdResponse{}
}
func (m *GetRankInfoBySocialCommunityIdResponse) String() string { return proto.CompactTextString(m) }
func (*GetRankInfoBySocialCommunityIdResponse) ProtoMessage()    {}
func (*GetRankInfoBySocialCommunityIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_rank_a75277c75ea4c412, []int{19}
}
func (m *GetRankInfoBySocialCommunityIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRankInfoBySocialCommunityIdResponse.Unmarshal(m, b)
}
func (m *GetRankInfoBySocialCommunityIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRankInfoBySocialCommunityIdResponse.Marshal(b, m, deterministic)
}
func (dst *GetRankInfoBySocialCommunityIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRankInfoBySocialCommunityIdResponse.Merge(dst, src)
}
func (m *GetRankInfoBySocialCommunityIdResponse) XXX_Size() int {
	return xxx_messageInfo_GetRankInfoBySocialCommunityIdResponse.Size(m)
}
func (m *GetRankInfoBySocialCommunityIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRankInfoBySocialCommunityIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRankInfoBySocialCommunityIdResponse proto.InternalMessageInfo

func (m *GetRankInfoBySocialCommunityIdResponse) GetChannelHonorSignMap() map[string]*SocialRankHonorSignInfo {
	if m != nil {
		return m.ChannelHonorSignMap
	}
	return nil
}

// 上榜社群的公演房增加榜单入口
type BatGetRankInChannelRequest struct {
	ChannelIdList        []uint32 `protobuf:"varint,1,rep,packed,name=channel_id_list,json=channelIdList,proto3" json:"channel_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetRankInChannelRequest) Reset()         { *m = BatGetRankInChannelRequest{} }
func (m *BatGetRankInChannelRequest) String() string { return proto.CompactTextString(m) }
func (*BatGetRankInChannelRequest) ProtoMessage()    {}
func (*BatGetRankInChannelRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_rank_a75277c75ea4c412, []int{20}
}
func (m *BatGetRankInChannelRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetRankInChannelRequest.Unmarshal(m, b)
}
func (m *BatGetRankInChannelRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetRankInChannelRequest.Marshal(b, m, deterministic)
}
func (dst *BatGetRankInChannelRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetRankInChannelRequest.Merge(dst, src)
}
func (m *BatGetRankInChannelRequest) XXX_Size() int {
	return xxx_messageInfo_BatGetRankInChannelRequest.Size(m)
}
func (m *BatGetRankInChannelRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetRankInChannelRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetRankInChannelRequest proto.InternalMessageInfo

func (m *BatGetRankInChannelRequest) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type RankInChannelInfo struct {
	CategoryId           string   `protobuf:"bytes,1,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	RankType             uint32   `protobuf:"varint,2,opt,name=rank_type,json=rankType,proto3" json:"rank_type,omitempty"`
	Text                 string   `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	SocialCommunityId    string   `protobuf:"bytes,4,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	JumpRankUrl          string   `protobuf:"bytes,5,opt,name=jump_rank_url,json=jumpRankUrl,proto3" json:"jump_rank_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RankInChannelInfo) Reset()         { *m = RankInChannelInfo{} }
func (m *RankInChannelInfo) String() string { return proto.CompactTextString(m) }
func (*RankInChannelInfo) ProtoMessage()    {}
func (*RankInChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_rank_a75277c75ea4c412, []int{21}
}
func (m *RankInChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RankInChannelInfo.Unmarshal(m, b)
}
func (m *RankInChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RankInChannelInfo.Marshal(b, m, deterministic)
}
func (dst *RankInChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RankInChannelInfo.Merge(dst, src)
}
func (m *RankInChannelInfo) XXX_Size() int {
	return xxx_messageInfo_RankInChannelInfo.Size(m)
}
func (m *RankInChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RankInChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RankInChannelInfo proto.InternalMessageInfo

func (m *RankInChannelInfo) GetCategoryId() string {
	if m != nil {
		return m.CategoryId
	}
	return ""
}

func (m *RankInChannelInfo) GetRankType() uint32 {
	if m != nil {
		return m.RankType
	}
	return 0
}

func (m *RankInChannelInfo) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *RankInChannelInfo) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

func (m *RankInChannelInfo) GetJumpRankUrl() string {
	if m != nil {
		return m.JumpRankUrl
	}
	return ""
}

type BatGetRankInChannelResponse struct {
	RankInChannelInfoMap map[uint32]*RankInChannelInfo `protobuf:"bytes,1,rep,name=rank_in_channel_info_map,json=rankInChannelInfoMap,proto3" json:"rank_in_channel_info_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *BatGetRankInChannelResponse) Reset()         { *m = BatGetRankInChannelResponse{} }
func (m *BatGetRankInChannelResponse) String() string { return proto.CompactTextString(m) }
func (*BatGetRankInChannelResponse) ProtoMessage()    {}
func (*BatGetRankInChannelResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_rank_a75277c75ea4c412, []int{22}
}
func (m *BatGetRankInChannelResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetRankInChannelResponse.Unmarshal(m, b)
}
func (m *BatGetRankInChannelResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetRankInChannelResponse.Marshal(b, m, deterministic)
}
func (dst *BatGetRankInChannelResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetRankInChannelResponse.Merge(dst, src)
}
func (m *BatGetRankInChannelResponse) XXX_Size() int {
	return xxx_messageInfo_BatGetRankInChannelResponse.Size(m)
}
func (m *BatGetRankInChannelResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetRankInChannelResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetRankInChannelResponse proto.InternalMessageInfo

func (m *BatGetRankInChannelResponse) GetRankInChannelInfoMap() map[uint32]*RankInChannelInfo {
	if m != nil {
		return m.RankInChannelInfoMap
	}
	return nil
}

type SendWeeklyDataRequest struct {
	BrandId              string   `protobuf:"bytes,1,opt,name=brand_id,json=brandId,proto3" json:"brand_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendWeeklyDataRequest) Reset()         { *m = SendWeeklyDataRequest{} }
func (m *SendWeeklyDataRequest) String() string { return proto.CompactTextString(m) }
func (*SendWeeklyDataRequest) ProtoMessage()    {}
func (*SendWeeklyDataRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_rank_a75277c75ea4c412, []int{23}
}
func (m *SendWeeklyDataRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendWeeklyDataRequest.Unmarshal(m, b)
}
func (m *SendWeeklyDataRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendWeeklyDataRequest.Marshal(b, m, deterministic)
}
func (dst *SendWeeklyDataRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendWeeklyDataRequest.Merge(dst, src)
}
func (m *SendWeeklyDataRequest) XXX_Size() int {
	return xxx_messageInfo_SendWeeklyDataRequest.Size(m)
}
func (m *SendWeeklyDataRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SendWeeklyDataRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SendWeeklyDataRequest proto.InternalMessageInfo

func (m *SendWeeklyDataRequest) GetBrandId() string {
	if m != nil {
		return m.BrandId
	}
	return ""
}

type SendWeeklyDataResponse struct {
	MsgList              []string `protobuf:"bytes,1,rep,name=msgList,proto3" json:"msgList,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendWeeklyDataResponse) Reset()         { *m = SendWeeklyDataResponse{} }
func (m *SendWeeklyDataResponse) String() string { return proto.CompactTextString(m) }
func (*SendWeeklyDataResponse) ProtoMessage()    {}
func (*SendWeeklyDataResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_rank_a75277c75ea4c412, []int{24}
}
func (m *SendWeeklyDataResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendWeeklyDataResponse.Unmarshal(m, b)
}
func (m *SendWeeklyDataResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendWeeklyDataResponse.Marshal(b, m, deterministic)
}
func (dst *SendWeeklyDataResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendWeeklyDataResponse.Merge(dst, src)
}
func (m *SendWeeklyDataResponse) XXX_Size() int {
	return xxx_messageInfo_SendWeeklyDataResponse.Size(m)
}
func (m *SendWeeklyDataResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SendWeeklyDataResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SendWeeklyDataResponse proto.InternalMessageInfo

func (m *SendWeeklyDataResponse) GetMsgList() []string {
	if m != nil {
		return m.MsgList
	}
	return nil
}

func init() {
	proto.RegisterType((*GetSocialCommunityRankTopTabListRequest)(nil), "muse_social_rank.GetSocialCommunityRankTopTabListRequest")
	proto.RegisterType((*TopTabInfo)(nil), "muse_social_rank.TopTabInfo")
	proto.RegisterType((*GetSocialCommunityRankTopTabListResponse)(nil), "muse_social_rank.GetSocialCommunityRankTopTabListResponse")
	proto.RegisterType((*GetSocialCommunityRankRequest)(nil), "muse_social_rank.GetSocialCommunityRankRequest")
	proto.RegisterType((*SocialCommunityRankBrandInfo)(nil), "muse_social_rank.SocialCommunityRankBrandInfo")
	proto.RegisterType((*SocialCommunityRankElementInfo)(nil), "muse_social_rank.SocialCommunityRankElementInfo")
	proto.RegisterType((*SocialCommunityRankMeSortInfo)(nil), "muse_social_rank.SocialCommunityRankMeSortInfo")
	proto.RegisterType((*GetSocialCommunityRankResponse)(nil), "muse_social_rank.GetSocialCommunityRankResponse")
	proto.RegisterType((*GetSocialCommunityRankBrandChannelStatusRequest)(nil), "muse_social_rank.GetSocialCommunityRankBrandChannelStatusRequest")
	proto.RegisterType((*SocialCommunityRankBrandChannelStatusInfo)(nil), "muse_social_rank.SocialCommunityRankBrandChannelStatusInfo")
	proto.RegisterType((*GetSocialCommunityRankBrandChannelStatusResponse)(nil), "muse_social_rank.GetSocialCommunityRankBrandChannelStatusResponse")
	proto.RegisterType((*OnRankCheckRequest)(nil), "muse_social_rank.OnRankCheckRequest")
	proto.RegisterType((*OnRankCheckResponse)(nil), "muse_social_rank.OnRankCheckResponse")
	proto.RegisterType((*GetRankHonorSignByChannelIdsRequest)(nil), "muse_social_rank.GetRankHonorSignByChannelIdsRequest")
	proto.RegisterType((*SocialRankHonorSignInfo)(nil), "muse_social_rank.SocialRankHonorSignInfo")
	proto.RegisterType((*GetRankHonorSignByChannelIdsResponse)(nil), "muse_social_rank.GetRankHonorSignByChannelIdsResponse")
	proto.RegisterMapType((map[uint32]*SocialRankHonorSignInfo)(nil), "muse_social_rank.GetRankHonorSignByChannelIdsResponse.ChannelHonorSignMapEntry")
	proto.RegisterType((*GetRankHonorSignBySocialCommunityIdRequest)(nil), "muse_social_rank.GetRankHonorSignBySocialCommunityIdRequest")
	proto.RegisterType((*GetRankHonorSignBySocialCommunityIdResponse)(nil), "muse_social_rank.GetRankHonorSignBySocialCommunityIdResponse")
	proto.RegisterType((*GetRankHonorSignBySocialCommunityIdsRequest)(nil), "muse_social_rank.GetRankHonorSignBySocialCommunityIdsRequest")
	proto.RegisterType((*GetRankInfoBySocialCommunityIdResponse)(nil), "muse_social_rank.GetRankInfoBySocialCommunityIdResponse")
	proto.RegisterMapType((map[string]*SocialRankHonorSignInfo)(nil), "muse_social_rank.GetRankInfoBySocialCommunityIdResponse.ChannelHonorSignMapEntry")
	proto.RegisterType((*BatGetRankInChannelRequest)(nil), "muse_social_rank.BatGetRankInChannelRequest")
	proto.RegisterType((*RankInChannelInfo)(nil), "muse_social_rank.RankInChannelInfo")
	proto.RegisterType((*BatGetRankInChannelResponse)(nil), "muse_social_rank.BatGetRankInChannelResponse")
	proto.RegisterMapType((map[uint32]*RankInChannelInfo)(nil), "muse_social_rank.BatGetRankInChannelResponse.RankInChannelInfoMapEntry")
	proto.RegisterType((*SendWeeklyDataRequest)(nil), "muse_social_rank.SendWeeklyDataRequest")
	proto.RegisterType((*SendWeeklyDataResponse)(nil), "muse_social_rank.SendWeeklyDataResponse")
	proto.RegisterEnum("muse_social_rank.SocialCommunityRankType", SocialCommunityRankType_name, SocialCommunityRankType_value)
	proto.RegisterEnum("muse_social_rank.SocialCommunityRankBrandChannelStatus", SocialCommunityRankBrandChannelStatus_name, SocialCommunityRankBrandChannelStatus_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MuseSocialRankClient is the client API for MuseSocialRank service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MuseSocialRankClient interface {
	// 榜单顶部品类tab列表
	GetSocialCommunityRankTopTabList(ctx context.Context, in *GetSocialCommunityRankTopTabListRequest, opts ...grpc.CallOption) (*GetSocialCommunityRankTopTabListResponse, error)
	// 榜单
	GetSocialCommunityRank(ctx context.Context, in *GetSocialCommunityRankRequest, opts ...grpc.CallOption) (*GetSocialCommunityRankResponse, error)
	// 榜单社群状态
	GetSocialCommunityRankBrandChannelStatus(ctx context.Context, in *GetSocialCommunityRankBrandChannelStatusRequest, opts ...grpc.CallOption) (*GetSocialCommunityRankBrandChannelStatusResponse, error)
	// 上榜社群的公演房增加榜单入口
	OnRankCheck(ctx context.Context, in *OnRankCheckRequest, opts ...grpc.CallOption) (*OnRankCheckResponse, error)
	SendWeeklyData(ctx context.Context, in *SendWeeklyDataRequest, opts ...grpc.CallOption) (*SendWeeklyDataResponse, error)
	// 根据房间（或者社群ID）查榜单（周榜）的荣誉标识
	GetRankHonorSignByChannelIds(ctx context.Context, in *GetRankHonorSignByChannelIdsRequest, opts ...grpc.CallOption) (*GetRankHonorSignByChannelIdsResponse, error)
	// 上榜社群的公演房增加榜单入口
	BatGetRankInChannel(ctx context.Context, in *BatGetRankInChannelRequest, opts ...grpc.CallOption) (*BatGetRankInChannelResponse, error)
	// 根据社群ID 查榜单（周榜）的荣誉标识
	GetRankHonorSignBySocialCommunityId(ctx context.Context, in *GetRankHonorSignBySocialCommunityIdRequest, opts ...grpc.CallOption) (*GetRankHonorSignBySocialCommunityIdResponse, error)
}

type museSocialRankClient struct {
	cc *grpc.ClientConn
}

func NewMuseSocialRankClient(cc *grpc.ClientConn) MuseSocialRankClient {
	return &museSocialRankClient{cc}
}

func (c *museSocialRankClient) GetSocialCommunityRankTopTabList(ctx context.Context, in *GetSocialCommunityRankTopTabListRequest, opts ...grpc.CallOption) (*GetSocialCommunityRankTopTabListResponse, error) {
	out := new(GetSocialCommunityRankTopTabListResponse)
	err := c.cc.Invoke(ctx, "/muse_social_rank.MuseSocialRank/GetSocialCommunityRankTopTabList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museSocialRankClient) GetSocialCommunityRank(ctx context.Context, in *GetSocialCommunityRankRequest, opts ...grpc.CallOption) (*GetSocialCommunityRankResponse, error) {
	out := new(GetSocialCommunityRankResponse)
	err := c.cc.Invoke(ctx, "/muse_social_rank.MuseSocialRank/GetSocialCommunityRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museSocialRankClient) GetSocialCommunityRankBrandChannelStatus(ctx context.Context, in *GetSocialCommunityRankBrandChannelStatusRequest, opts ...grpc.CallOption) (*GetSocialCommunityRankBrandChannelStatusResponse, error) {
	out := new(GetSocialCommunityRankBrandChannelStatusResponse)
	err := c.cc.Invoke(ctx, "/muse_social_rank.MuseSocialRank/GetSocialCommunityRankBrandChannelStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museSocialRankClient) OnRankCheck(ctx context.Context, in *OnRankCheckRequest, opts ...grpc.CallOption) (*OnRankCheckResponse, error) {
	out := new(OnRankCheckResponse)
	err := c.cc.Invoke(ctx, "/muse_social_rank.MuseSocialRank/OnRankCheck", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museSocialRankClient) SendWeeklyData(ctx context.Context, in *SendWeeklyDataRequest, opts ...grpc.CallOption) (*SendWeeklyDataResponse, error) {
	out := new(SendWeeklyDataResponse)
	err := c.cc.Invoke(ctx, "/muse_social_rank.MuseSocialRank/SendWeeklyData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museSocialRankClient) GetRankHonorSignByChannelIds(ctx context.Context, in *GetRankHonorSignByChannelIdsRequest, opts ...grpc.CallOption) (*GetRankHonorSignByChannelIdsResponse, error) {
	out := new(GetRankHonorSignByChannelIdsResponse)
	err := c.cc.Invoke(ctx, "/muse_social_rank.MuseSocialRank/GetRankHonorSignByChannelIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museSocialRankClient) BatGetRankInChannel(ctx context.Context, in *BatGetRankInChannelRequest, opts ...grpc.CallOption) (*BatGetRankInChannelResponse, error) {
	out := new(BatGetRankInChannelResponse)
	err := c.cc.Invoke(ctx, "/muse_social_rank.MuseSocialRank/BatGetRankInChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museSocialRankClient) GetRankHonorSignBySocialCommunityId(ctx context.Context, in *GetRankHonorSignBySocialCommunityIdRequest, opts ...grpc.CallOption) (*GetRankHonorSignBySocialCommunityIdResponse, error) {
	out := new(GetRankHonorSignBySocialCommunityIdResponse)
	err := c.cc.Invoke(ctx, "/muse_social_rank.MuseSocialRank/GetRankHonorSignBySocialCommunityId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MuseSocialRankServer is the server API for MuseSocialRank service.
type MuseSocialRankServer interface {
	// 榜单顶部品类tab列表
	GetSocialCommunityRankTopTabList(context.Context, *GetSocialCommunityRankTopTabListRequest) (*GetSocialCommunityRankTopTabListResponse, error)
	// 榜单
	GetSocialCommunityRank(context.Context, *GetSocialCommunityRankRequest) (*GetSocialCommunityRankResponse, error)
	// 榜单社群状态
	GetSocialCommunityRankBrandChannelStatus(context.Context, *GetSocialCommunityRankBrandChannelStatusRequest) (*GetSocialCommunityRankBrandChannelStatusResponse, error)
	// 上榜社群的公演房增加榜单入口
	OnRankCheck(context.Context, *OnRankCheckRequest) (*OnRankCheckResponse, error)
	SendWeeklyData(context.Context, *SendWeeklyDataRequest) (*SendWeeklyDataResponse, error)
	// 根据房间（或者社群ID）查榜单（周榜）的荣誉标识
	GetRankHonorSignByChannelIds(context.Context, *GetRankHonorSignByChannelIdsRequest) (*GetRankHonorSignByChannelIdsResponse, error)
	// 上榜社群的公演房增加榜单入口
	BatGetRankInChannel(context.Context, *BatGetRankInChannelRequest) (*BatGetRankInChannelResponse, error)
	// 根据社群ID 查榜单（周榜）的荣誉标识
	GetRankHonorSignBySocialCommunityId(context.Context, *GetRankHonorSignBySocialCommunityIdRequest) (*GetRankHonorSignBySocialCommunityIdResponse, error)
}

func RegisterMuseSocialRankServer(s *grpc.Server, srv MuseSocialRankServer) {
	s.RegisterService(&_MuseSocialRank_serviceDesc, srv)
}

func _MuseSocialRank_GetSocialCommunityRankTopTabList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSocialCommunityRankTopTabListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseSocialRankServer).GetSocialCommunityRankTopTabList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_social_rank.MuseSocialRank/GetSocialCommunityRankTopTabList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseSocialRankServer).GetSocialCommunityRankTopTabList(ctx, req.(*GetSocialCommunityRankTopTabListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseSocialRank_GetSocialCommunityRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSocialCommunityRankRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseSocialRankServer).GetSocialCommunityRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_social_rank.MuseSocialRank/GetSocialCommunityRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseSocialRankServer).GetSocialCommunityRank(ctx, req.(*GetSocialCommunityRankRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseSocialRank_GetSocialCommunityRankBrandChannelStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSocialCommunityRankBrandChannelStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseSocialRankServer).GetSocialCommunityRankBrandChannelStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_social_rank.MuseSocialRank/GetSocialCommunityRankBrandChannelStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseSocialRankServer).GetSocialCommunityRankBrandChannelStatus(ctx, req.(*GetSocialCommunityRankBrandChannelStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseSocialRank_OnRankCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OnRankCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseSocialRankServer).OnRankCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_social_rank.MuseSocialRank/OnRankCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseSocialRankServer).OnRankCheck(ctx, req.(*OnRankCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseSocialRank_SendWeeklyData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendWeeklyDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseSocialRankServer).SendWeeklyData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_social_rank.MuseSocialRank/SendWeeklyData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseSocialRankServer).SendWeeklyData(ctx, req.(*SendWeeklyDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseSocialRank_GetRankHonorSignByChannelIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRankHonorSignByChannelIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseSocialRankServer).GetRankHonorSignByChannelIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_social_rank.MuseSocialRank/GetRankHonorSignByChannelIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseSocialRankServer).GetRankHonorSignByChannelIds(ctx, req.(*GetRankHonorSignByChannelIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseSocialRank_BatGetRankInChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetRankInChannelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseSocialRankServer).BatGetRankInChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_social_rank.MuseSocialRank/BatGetRankInChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseSocialRankServer).BatGetRankInChannel(ctx, req.(*BatGetRankInChannelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseSocialRank_GetRankHonorSignBySocialCommunityId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRankHonorSignBySocialCommunityIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseSocialRankServer).GetRankHonorSignBySocialCommunityId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_social_rank.MuseSocialRank/GetRankHonorSignBySocialCommunityId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseSocialRankServer).GetRankHonorSignBySocialCommunityId(ctx, req.(*GetRankHonorSignBySocialCommunityIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _MuseSocialRank_serviceDesc = grpc.ServiceDesc{
	ServiceName: "muse_social_rank.MuseSocialRank",
	HandlerType: (*MuseSocialRankServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetSocialCommunityRankTopTabList",
			Handler:    _MuseSocialRank_GetSocialCommunityRankTopTabList_Handler,
		},
		{
			MethodName: "GetSocialCommunityRank",
			Handler:    _MuseSocialRank_GetSocialCommunityRank_Handler,
		},
		{
			MethodName: "GetSocialCommunityRankBrandChannelStatus",
			Handler:    _MuseSocialRank_GetSocialCommunityRankBrandChannelStatus_Handler,
		},
		{
			MethodName: "OnRankCheck",
			Handler:    _MuseSocialRank_OnRankCheck_Handler,
		},
		{
			MethodName: "SendWeeklyData",
			Handler:    _MuseSocialRank_SendWeeklyData_Handler,
		},
		{
			MethodName: "GetRankHonorSignByChannelIds",
			Handler:    _MuseSocialRank_GetRankHonorSignByChannelIds_Handler,
		},
		{
			MethodName: "BatGetRankInChannel",
			Handler:    _MuseSocialRank_BatGetRankInChannel_Handler,
		},
		{
			MethodName: "GetRankHonorSignBySocialCommunityId",
			Handler:    _MuseSocialRank_GetRankHonorSignBySocialCommunityId_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "muse-social-rank/muse-social-rank.proto",
}

func init() {
	proto.RegisterFile("muse-social-rank/muse-social-rank.proto", fileDescriptor_muse_social_rank_a75277c75ea4c412)
}

var fileDescriptor_muse_social_rank_a75277c75ea4c412 = []byte{
	// 1581 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x58, 0x4f, 0x73, 0xd3, 0x46,
	0x14, 0x8f, 0x9c, 0xbf, 0x7e, 0xc1, 0xc1, 0xd9, 0x40, 0x30, 0x26, 0x81, 0x8c, 0x02, 0x21, 0xa4,
	0x60, 0x33, 0xa6, 0x40, 0x81, 0x76, 0x3a, 0x89, 0xe3, 0x12, 0x0f, 0x89, 0x03, 0xb2, 0x53, 0x4a,
	0x87, 0x19, 0x8d, 0x22, 0x6f, 0x1c, 0x35, 0xb2, 0xd6, 0x48, 0x6b, 0x88, 0x0f, 0x9d, 0xf6, 0xd0,
	0xe9, 0xa9, 0x33, 0x70, 0x6e, 0x0f, 0xed, 0x81, 0x5e, 0xda, 0x63, 0x0f, 0x9d, 0x7e, 0x87, 0xf6,
	0xd0, 0x73, 0xbf, 0x44, 0xcf, 0x3d, 0x75, 0x76, 0x25, 0xd9, 0xb2, 0xfe, 0xc4, 0x0a, 0xa5, 0x27,
	0x5b, 0x6f, 0xdf, 0x7b, 0xfb, 0x7b, 0xef, 0xfd, 0xde, 0xdb, 0x95, 0xe0, 0x72, 0xb3, 0x6d, 0xe1,
	0x6b, 0x16, 0x51, 0x35, 0x45, 0xbf, 0x66, 0x2a, 0xc6, 0x41, 0xde, 0x2f, 0xc8, 0xb5, 0x4c, 0x42,
	0x09, 0x4a, 0x33, 0xb9, 0x6c, 0xcb, 0x65, 0x26, 0xcf, 0x5e, 0xc0, 0x87, 0x14, 0x1b, 0x96, 0x46,
	0x8c, 0x3c, 0x69, 0x51, 0x8d, 0x18, 0x96, 0xfb, 0x6b, 0x9b, 0x88, 0xf7, 0xe0, 0xf2, 0x7d, 0x4c,
	0xab, 0xdc, 0xa4, 0x48, 0x9a, 0xcd, 0xb6, 0xa1, 0xd1, 0x8e, 0xa4, 0x18, 0x07, 0x35, 0xd2, 0xaa,
	0x29, 0xbb, 0x9b, 0x9a, 0x45, 0x25, 0xfc, 0xac, 0x8d, 0x2d, 0x8a, 0xd2, 0x30, 0xdc, 0xd6, 0xea,
	0x19, 0x61, 0x41, 0x58, 0x4e, 0x49, 0xec, 0xaf, 0xb8, 0x0d, 0x60, 0xab, 0x95, 0x8d, 0x3d, 0x82,
	0xce, 0xc2, 0x04, 0x55, 0x76, 0x65, 0x43, 0x69, 0x62, 0xae, 0x94, 0x94, 0xc6, 0xa9, 0xb2, 0x5b,
	0x51, 0x9a, 0x18, 0x2d, 0xc1, 0x49, 0xb6, 0xa4, 0x2a, 0x14, 0x37, 0x88, 0xd9, 0x91, 0xb5, 0x7a,
	0x26, 0xc1, 0x35, 0x52, 0x54, 0xd9, 0x2d, 0x3a, 0xd2, 0x72, 0x5d, 0x54, 0x61, 0x79, 0x30, 0x1a,
	0xab, 0x45, 0x0c, 0x0b, 0xa3, 0xdb, 0xf6, 0x76, 0xba, 0x66, 0xd1, 0x8c, 0xb0, 0x30, 0xbc, 0x3c,
	0x59, 0x98, 0xcb, 0xf9, 0xe3, 0xcf, 0xf5, 0xe0, 0x71, 0x30, 0xcc, 0x81, 0xf8, 0x93, 0x00, 0xf3,
	0xe1, 0xbb, 0x44, 0x46, 0x1a, 0x37, 0x00, 0x74, 0x0e, 0x92, 0x6c, 0x5f, 0x99, 0x76, 0x5a, 0x38,
	0x33, 0xcc, 0xed, 0x27, 0x98, 0xa0, 0xd6, 0x69, 0x61, 0x54, 0x80, 0x59, 0xcd, 0x92, 0x0d, 0x7c,
	0x48, 0xe5, 0x17, 0x18, 0x1f, 0x70, 0x84, 0x72, 0x5d, 0xa1, 0x4a, 0x66, 0x64, 0x41, 0x58, 0x9e,
	0x90, 0x90, 0x66, 0x55, 0xf0, 0x21, 0x7d, 0x8c, 0xf1, 0x01, 0x43, 0xb3, 0xae, 0x50, 0x45, 0x7c,
	0x01, 0x73, 0x21, 0x40, 0xd7, 0x4c, 0xc5, 0xa8, 0xbb, 0x49, 0xdf, 0x65, 0x0f, 0xb2, 0x83, 0x37,
	0x29, 0x8d, 0xf3, 0xe7, 0x72, 0x1d, 0xcd, 0x03, 0xd8, 0x4b, 0x3a, 0x69, 0x10, 0x07, 0x6e, 0x92,
	0x4b, 0x36, 0x49, 0x83, 0xf4, 0x96, 0x79, 0xc1, 0x86, 0x3d, 0xcb, 0xac, 0x64, 0xe2, 0x2b, 0x01,
	0xce, 0x87, 0xec, 0x5c, 0xd2, 0x71, 0x13, 0x1b, 0x94, 0xef, 0xfd, 0x31, 0x9c, 0xb4, 0x3d, 0xf0,
	0x40, 0x34, 0x63, 0x8f, 0x70, 0x08, 0x93, 0x85, 0x5c, 0xb0, 0x10, 0x47, 0x05, 0x21, 0xa5, 0xb8,
	0x1b, 0x26, 0xe3, 0x7e, 0x67, 0x61, 0x4c, 0x6b, 0xb6, 0x14, 0x95, 0x72, 0xd0, 0x29, 0xc9, 0x79,
	0x12, 0xff, 0x12, 0x60, 0x3e, 0xc4, 0xcf, 0x16, 0xae, 0x12, 0xd3, 0x46, 0xf4, 0x49, 0x14, 0xa2,
	0xeb, 0xb1, 0x10, 0x79, 0x82, 0xf3, 0x63, 0xca, 0xc3, 0x29, 0xba, 0xaf, 0x59, 0x9e, 0xc2, 0x69,
	0x46, 0x1d, 0x1f, 0x3a, 0x08, 0xa7, 0xd9, 0x9a, 0x5b, 0xb7, 0x32, 0x5b, 0x60, 0x06, 0xbe, 0x4a,
	0xdb, 0x06, 0x36, 0x29, 0xa6, 0x0d, 0x4f, 0xa1, 0xb9, 0x81, 0xf8, 0xa7, 0x00, 0xe7, 0xa3, 0x68,
	0xe9, 0x50, 0xfe, 0x29, 0x4c, 0x73, 0x4f, 0xd8, 0xc6, 0xe9, 0xe5, 0xfe, 0xf1, 0x03, 0x3c, 0x69,
	0xf6, 0x04, 0xac, 0x2f, 0xd0, 0x23, 0x38, 0xd1, 0xc4, 0x9e, 0xcc, 0x25, 0x78, 0xe6, 0xf2, 0xb1,
	0x1c, 0xf7, 0x6a, 0x20, 0x41, 0x13, 0xbb, 0x59, 0x13, 0xbf, 0x15, 0x20, 0x1f, 0x1e, 0x13, 0x2f,
	0x7e, 0x71, 0x5f, 0x31, 0x0c, 0xac, 0x57, 0xa9, 0x42, 0xdb, 0x96, 0xdb, 0x7c, 0x21, 0xad, 0x26,
	0x0c, 0x6c, 0xb5, 0x84, 0xaf, 0xd5, 0x44, 0x48, 0xb9, 0x6d, 0x61, 0x67, 0x69, 0x78, 0x61, 0x78,
	0x39, 0x29, 0x4d, 0x3a, 0xbd, 0xc1, 0xe7, 0xc0, 0x2b, 0x01, 0xae, 0xc4, 0x42, 0xc6, 0x09, 0x30,
	0x0f, 0xa0, 0xda, 0x42, 0xb9, 0x3b, 0x1a, 0x92, 0x8e, 0xa4, 0x5c, 0x47, 0x97, 0x60, 0xca, 0x5d,
	0xb6, 0xb8, 0x91, 0x03, 0x29, 0xa5, 0x7a, 0x3d, 0x31, 0x2f, 0x6d, 0x0b, 0x9b, 0xb2, 0x4a, 0xda,
	0x06, 0x75, 0xb8, 0x90, 0x64, 0x92, 0x22, 0x13, 0x88, 0xdf, 0x0b, 0x70, 0x3d, 0x7e, 0xbe, 0x1c,
	0x56, 0x1c, 0xc0, 0x4c, 0xff, 0xd6, 0x5e, 0x5e, 0xdc, 0x8b, 0xdf, 0x8a, 0x81, 0x98, 0xa5, 0xe9,
	0x3e, 0xf0, 0x3c, 0x69, 0x4b, 0x80, 0xb6, 0x0d, 0x66, 0x52, 0xdc, 0xc7, 0x6a, 0xf4, 0xc0, 0x14,
	0x7f, 0x4c, 0xc0, 0x4c, 0x9f, 0xa2, 0x03, 0x76, 0x0e, 0x40, 0xb3, 0x64, 0x62, 0x70, 0x28, 0xdc,
	0x60, 0x42, 0x9a, 0xd0, 0x2c, 0x5b, 0x35, 0x6c, 0xa2, 0x24, 0xde, 0xc6, 0x44, 0xb9, 0x03, 0x67,
	0xbb, 0x7c, 0x62, 0x7c, 0x91, 0x2d, 0xad, 0xd9, 0xd2, 0xb1, 0x5c, 0xc7, 0x96, 0xea, 0x8c, 0xbe,
	0x59, 0x57, 0x81, 0xf1, 0xa7, 0xca, 0x97, 0xd7, 0xb1, 0xa5, 0xa2, 0x45, 0x48, 0x75, 0x4d, 0xf9,
	0xa4, 0x1c, 0xe1, 0xea, 0x27, 0x5c, 0x21, 0x3f, 0xdf, 0xce, 0xc0, 0x38, 0x47, 0x6c, 0x90, 0xcc,
	0x28, 0x5f, 0x1e, 0x63, 0x8f, 0x15, 0x82, 0x2e, 0xc0, 0xa4, 0x97, 0xc8, 0x63, 0x7c, 0x11, 0xd4,
	0xde, 0x89, 0xb7, 0x05, 0x8b, 0xf7, 0x31, 0x65, 0x40, 0x37, 0x88, 0x41, 0xcc, 0xaa, 0xd6, 0x30,
	0xd6, 0x3a, 0x45, 0x97, 0x56, 0xde, 0xa6, 0xe8, 0xb1, 0xaf, 0x57, 0xdf, 0x1e, 0xbf, 0x1c, 0x4e,
	0xff, 0x9c, 0x80, 0x33, 0x76, 0x62, 0xfa, 0x5c, 0xf2, 0x24, 0x20, 0x18, 0xd1, 0x54, 0x62, 0x38,
	0xdd, 0xc4, 0xff, 0xa3, 0x65, 0x48, 0x5b, 0xb4, 0xa3, 0x63, 0x59, 0x25, 0x3a, 0x31, 0x6d, 0xc7,
	0x09, 0xde, 0x2a, 0x53, 0x5c, 0x5e, 0x64, 0x62, 0x3e, 0x1d, 0x10, 0x8c, 0x50, 0x7c, 0x48, 0x9d,
	0x6c, 0xf1, 0xff, 0xfe, 0xe8, 0x46, 0xfc, 0xd1, 0xf5, 0xf7, 0xe8, 0xa8, 0xaf, 0x47, 0x73, 0x30,
	0xe3, 0xd4, 0x53, 0x75, 0x8b, 0xd8, 0xcb, 0xd1, 0xb4, 0xd5, 0x5f, 0xde, 0x72, 0x1d, 0x5d, 0x85,
	0x69, 0xbb, 0xa0, 0x4e, 0x7a, 0x6a, 0x0c, 0xce, 0xb8, 0xad, 0x1d, 0x58, 0x60, 0x13, 0xe0, 0xb3,
	0x76, 0xb3, 0x65, 0x33, 0xa9, 0x6d, 0xea, 0x99, 0x09, 0xae, 0x39, 0xc9, 0x84, 0x4c, 0x7b, 0xc7,
	0xd4, 0xc5, 0x1f, 0x12, 0x70, 0xf1, 0xe8, 0xec, 0x3b, 0xac, 0xfd, 0x4a, 0x80, 0x59, 0x37, 0xff,
	0xfb, 0x4c, 0x53, 0xb6, 0xb4, 0x86, 0x21, 0x37, 0x95, 0x96, 0xd3, 0x66, 0xdb, 0x41, 0x7e, 0xc6,
	0x71, 0x9c, 0x73, 0x44, 0x5d, 0xa5, 0x2d, 0xa5, 0x55, 0x32, 0xa8, 0xd9, 0x91, 0xdc, 0x96, 0xf6,
	0xae, 0x64, 0x9f, 0x41, 0x26, 0xca, 0x80, 0xb5, 0xe0, 0x01, 0xee, 0xb8, 0x2d, 0x78, 0x80, 0x3b,
	0xe8, 0x43, 0x18, 0x7d, 0xae, 0xe8, 0x6d, 0xec, 0xb4, 0xd0, 0x95, 0xa8, 0x16, 0x0a, 0x30, 0x45,
	0xb2, 0xed, 0xee, 0x26, 0xde, 0x13, 0xc4, 0xa7, 0xb0, 0x12, 0x0c, 0xa4, 0xea, 0xaf, 0x8d, 0x4b,
	0xd3, 0x88, 0x92, 0x0a, 0x11, 0x25, 0x15, 0xbf, 0x14, 0xe0, 0x9d, 0x58, 0xee, 0x9d, 0x3a, 0x3c,
	0x02, 0x7e, 0x6a, 0x79, 0x6a, 0xe0, 0x9c, 0xef, 0xc7, 0x08, 0x2e, 0x65, 0x7a, 0x45, 0xe2, 0x5e,
	0x2c, 0x04, 0xdd, 0x46, 0xbc, 0x0d, 0x99, 0x90, 0x08, 0x7b, 0x1d, 0x99, 0x94, 0x4e, 0x07, 0xc2,
	0xe4, 0x9d, 0xf9, 0x3a, 0x01, 0x4b, 0xce, 0x46, 0x0c, 0xc6, 0x51, 0x51, 0x7e, 0x3d, 0x88, 0x6d,
	0x8f, 0x22, 0xd9, 0x36, 0xc0, 0xf5, 0xff, 0xc3, 0xb7, 0xe4, 0x5b, 0xe4, 0xdb, 0x3a, 0x64, 0xd7,
	0x14, 0xda, 0x8d, 0xc6, 0xd9, 0xfe, 0xb8, 0x63, 0xf0, 0x37, 0xc1, 0x37, 0x2b, 0xf8, 0x00, 0xf4,
	0x8d, 0x2b, 0xe1, 0xe8, 0x71, 0xe5, 0xbf, 0x52, 0x84, 0x0d, 0xc0, 0x08, 0xbe, 0x8f, 0x44, 0x8d,
	0xb0, 0xc0, 0x50, 0x1a, 0x0d, 0x0e, 0xa5, 0x6f, 0x12, 0x70, 0x2e, 0x34, 0x05, 0x0e, 0x3b, 0xbe,
	0x80, 0x8c, 0x73, 0x3a, 0xca, 0xdd, 0x5c, 0x18, 0x7b, 0xc4, 0x43, 0x8f, 0xfb, 0xc1, 0xcc, 0x1f,
	0xe1, 0x30, 0x17, 0x48, 0x54, 0x97, 0x14, 0xa7, 0xcc, 0x90, 0xa5, 0xac, 0x0e, 0x67, 0x23, 0x4d,
	0x42, 0xc6, 0xd0, 0x9d, 0x7e, 0x5a, 0x2c, 0x06, 0xc1, 0x05, 0xbc, 0x79, 0x09, 0x51, 0x80, 0xd3,
	0x55, 0x6c, 0xd4, 0xd9, 0x5d, 0x59, 0xef, 0xb0, 0x57, 0x22, 0x97, 0x0b, 0xd1, 0x6f, 0x3e, 0x62,
	0x01, 0x66, 0xfd, 0x36, 0x4e, 0xf2, 0x32, 0x30, 0xde, 0xb4, 0x1a, 0x9b, 0xbd, 0x6e, 0x75, 0x1f,
	0x57, 0x5e, 0x0a, 0xee, 0xc9, 0xd9, 0xff, 0xe2, 0xc9, 0x4a, 0xbf, 0x02, 0x4b, 0xd5, 0xed, 0x62,
	0x79, 0x75, 0x53, 0x2e, 0x6e, 0x6f, 0x6d, 0xed, 0x54, 0xca, 0xb5, 0x27, 0xb2, 0xb4, 0x5a, 0x79,
	0x20, 0xd7, 0x9e, 0x3c, 0x2c, 0xc9, 0x3b, 0x95, 0xea, 0xc3, 0x52, 0xb1, 0xfc, 0x51, 0xb9, 0xb4,
	0x9e, 0x1e, 0x42, 0x8b, 0x70, 0xe1, 0x08, 0xdd, 0xc7, 0xa5, 0xd2, 0x83, 0xb4, 0x30, 0x40, 0x69,
	0x63, 0x7b, 0x47, 0x4a, 0x27, 0x56, 0xfe, 0x10, 0xe0, 0x52, 0xac, 0xbb, 0x1a, 0xba, 0x0b, 0xb7,
	0xc2, 0xdd, 0xad, 0x49, 0xab, 0x95, 0x75, 0xb9, 0xb8, 0xb1, 0x5a, 0xa9, 0x94, 0x36, 0xe5, 0x6a,
	0x6d, 0xb5, 0xb6, 0x53, 0xf5, 0xe1, 0xbd, 0x01, 0xf9, 0x63, 0xd8, 0x16, 0x37, 0x56, 0x6b, 0x69,
	0xe1, 0x98, 0x46, 0xd5, 0x8d, 0xed, 0xc7, 0xe9, 0x44, 0xe1, 0xef, 0x09, 0x98, 0xda, 0x6a, 0x5b,
	0xb8, 0x37, 0x05, 0xd0, 0x77, 0x02, 0x2c, 0x0c, 0x7a, 0xe1, 0x47, 0x77, 0x42, 0xa7, 0x5d, 0x9c,
	0x4f, 0x16, 0xd9, 0xbb, 0x6f, 0x62, 0x6a, 0x53, 0x45, 0x1c, 0x42, 0x9f, 0xc3, 0x6c, 0xb8, 0x36,
	0xca, 0xc7, 0xf5, 0xeb, 0x02, 0xb9, 0x1e, 0xdf, 0xa0, 0xbb, 0xfd, 0x2f, 0x42, 0xd4, 0xd7, 0x90,
	0x10, 0x0a, 0xac, 0xc6, 0xdd, 0x20, 0xf2, 0xc5, 0x2b, 0xbb, 0xf6, 0x5f, 0x5c, 0x74, 0x51, 0x3f,
	0x85, 0x49, 0xcf, 0xbd, 0x1f, 0x5d, 0x0c, 0x3a, 0x0d, 0xbe, 0x3f, 0x64, 0x2f, 0x0d, 0xd0, 0xea,
	0x7a, 0xc7, 0x30, 0xd5, 0xdf, 0xd9, 0xe8, 0x72, 0xc8, 0x31, 0x13, 0x36, 0x2f, 0xb2, 0xcb, 0x83,
	0x15, 0xbb, 0xdb, 0xbc, 0x14, 0x60, 0xee, 0xa8, 0xfb, 0x1b, 0xba, 0x79, 0xdc, 0xfb, 0x9e, 0x8d,
	0xe1, 0xd6, 0x9b, 0x5d, 0x13, 0xc5, 0x21, 0x44, 0x61, 0x26, 0x64, 0x86, 0xa3, 0xab, 0x31, 0x47,
	0xbd, 0xbd, 0xfd, 0xb5, 0x63, 0x1d, 0x0c, 0xe2, 0x10, 0x7a, 0x2d, 0x84, 0xbd, 0x9e, 0x04, 0xae,
	0x17, 0xe8, 0xfd, 0x38, 0x71, 0x45, 0xdd, 0x1a, 0xb3, 0x1f, 0xbc, 0xa1, 0xb5, 0x0b, 0x33, 0x7b,
	0xe6, 0x9f, 0x5f, 0x7f, 0xaf, 0x21, 0x48, 0xfb, 0x3f, 0x8b, 0xae, 0xbd, 0xfb, 0x69, 0xa1, 0x41,
	0x74, 0xc5, 0x68, 0xe4, 0x6e, 0x16, 0x28, 0xcd, 0xa9, 0xa4, 0x99, 0xe7, 0x9f, 0x3d, 0x55, 0xa2,
	0xe7, 0x2d, 0x6c, 0x3e, 0xd7, 0x54, 0x6c, 0x05, 0x3e, 0xa6, 0xee, 0x8e, 0x71, 0x9d, 0x1b, 0xff,
	0x06, 0x00, 0x00, 0xff, 0xff, 0xc3, 0x6b, 0xc1, 0xa1, 0x78, 0x15, 0x00, 0x00,
}
