// Code generated by protoc-gen-gogo.
// source: src/lbssvr/lbs.proto
// DO NOT EDIT!

/*
	Package lbs is a generated protocol buffer package.

	It is generated from these files:
		src/lbssvr/lbs.proto

	It has these top-level messages:
		AdDetail
		LocationInfo
		GetLocationByIpReq
		GetLocationByIpResp
		DelLocationByIpReq
		SetLocationByIpReq
		GetAdCodeByIpReq
		GetAdCodeByIpResp
		GetAdCodeByLocationReq
		GetAdCodeByLocationResp
		GetLocationWebApiJsonReq
		GetLocationWebApiJsonResp
*/
package lbs

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import math3 "math"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import math4 "math"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type AdDetail struct {
	Province string `protobuf:"bytes,1,req,name=province" json:"province"`
	City     string `protobuf:"bytes,2,req,name=city" json:"city"`
	District string `protobuf:"bytes,3,opt,name=district" json:"district"`
	Nation   string `protobuf:"bytes,4,opt,name=nation" json:"nation"`
}

func (m *AdDetail) Reset()                    { *m = AdDetail{} }
func (m *AdDetail) String() string            { return proto.CompactTextString(m) }
func (*AdDetail) ProtoMessage()               {}
func (*AdDetail) Descriptor() ([]byte, []int) { return fileDescriptorLbs, []int{0} }

func (m *AdDetail) GetProvince() string {
	if m != nil {
		return m.Province
	}
	return ""
}

func (m *AdDetail) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *AdDetail) GetDistrict() string {
	if m != nil {
		return m.District
	}
	return ""
}

func (m *AdDetail) GetNation() string {
	if m != nil {
		return m.Nation
	}
	return ""
}

type LocationInfo struct {
	Lat      float32   `protobuf:"fixed32,1,req,name=lat" json:"lat"`
	Lng      float32   `protobuf:"fixed32,2,req,name=lng" json:"lng"`
	OpTime   uint64    `protobuf:"varint,3,opt,name=op_time,json=opTime" json:"op_time"`
	AdDetail *AdDetail `protobuf:"bytes,4,opt,name=ad_detail,json=adDetail" json:"ad_detail,omitempty"`
}

func (m *LocationInfo) Reset()                    { *m = LocationInfo{} }
func (m *LocationInfo) String() string            { return proto.CompactTextString(m) }
func (*LocationInfo) ProtoMessage()               {}
func (*LocationInfo) Descriptor() ([]byte, []int) { return fileDescriptorLbs, []int{1} }

func (m *LocationInfo) GetLat() float32 {
	if m != nil {
		return m.Lat
	}
	return 0
}

func (m *LocationInfo) GetLng() float32 {
	if m != nil {
		return m.Lng
	}
	return 0
}

func (m *LocationInfo) GetOpTime() uint64 {
	if m != nil {
		return m.OpTime
	}
	return 0
}

func (m *LocationInfo) GetAdDetail() *AdDetail {
	if m != nil {
		return m.AdDetail
	}
	return nil
}

type GetLocationByIpReq struct {
	Ip           string `protobuf:"bytes,1,req,name=ip" json:"ip"`
	IsNeedAdinfo bool   `protobuf:"varint,2,opt,name=is_need_adinfo,json=isNeedAdinfo" json:"is_need_adinfo"`
}

func (m *GetLocationByIpReq) Reset()                    { *m = GetLocationByIpReq{} }
func (m *GetLocationByIpReq) String() string            { return proto.CompactTextString(m) }
func (*GetLocationByIpReq) ProtoMessage()               {}
func (*GetLocationByIpReq) Descriptor() ([]byte, []int) { return fileDescriptorLbs, []int{2} }

func (m *GetLocationByIpReq) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *GetLocationByIpReq) GetIsNeedAdinfo() bool {
	if m != nil {
		return m.IsNeedAdinfo
	}
	return false
}

type GetLocationByIpResp struct {
	Data *LocationInfo `protobuf:"bytes,1,req,name=data" json:"data,omitempty"`
}

func (m *GetLocationByIpResp) Reset()                    { *m = GetLocationByIpResp{} }
func (m *GetLocationByIpResp) String() string            { return proto.CompactTextString(m) }
func (*GetLocationByIpResp) ProtoMessage()               {}
func (*GetLocationByIpResp) Descriptor() ([]byte, []int) { return fileDescriptorLbs, []int{3} }

func (m *GetLocationByIpResp) GetData() *LocationInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

type DelLocationByIpReq struct {
	Ip string `protobuf:"bytes,1,req,name=ip" json:"ip"`
}

func (m *DelLocationByIpReq) Reset()                    { *m = DelLocationByIpReq{} }
func (m *DelLocationByIpReq) String() string            { return proto.CompactTextString(m) }
func (*DelLocationByIpReq) ProtoMessage()               {}
func (*DelLocationByIpReq) Descriptor() ([]byte, []int) { return fileDescriptorLbs, []int{4} }

func (m *DelLocationByIpReq) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

type SetLocationByIpReq struct {
	Ip   string        `protobuf:"bytes,1,req,name=ip" json:"ip"`
	Data *LocationInfo `protobuf:"bytes,2,req,name=data" json:"data,omitempty"`
}

func (m *SetLocationByIpReq) Reset()                    { *m = SetLocationByIpReq{} }
func (m *SetLocationByIpReq) String() string            { return proto.CompactTextString(m) }
func (*SetLocationByIpReq) ProtoMessage()               {}
func (*SetLocationByIpReq) Descriptor() ([]byte, []int) { return fileDescriptorLbs, []int{5} }

func (m *SetLocationByIpReq) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *SetLocationByIpReq) GetData() *LocationInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

type GetAdCodeByIpReq struct {
	Ip string `protobuf:"bytes,1,req,name=ip" json:"ip"`
}

func (m *GetAdCodeByIpReq) Reset()                    { *m = GetAdCodeByIpReq{} }
func (m *GetAdCodeByIpReq) String() string            { return proto.CompactTextString(m) }
func (*GetAdCodeByIpReq) ProtoMessage()               {}
func (*GetAdCodeByIpReq) Descriptor() ([]byte, []int) { return fileDescriptorLbs, []int{6} }

func (m *GetAdCodeByIpReq) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

type GetAdCodeByIpResp struct {
	AdCode   uint32    `protobuf:"varint,1,req,name=ad_code,json=adCode" json:"ad_code"`
	AdInfo   string    `protobuf:"bytes,2,req,name=ad_info,json=adInfo" json:"ad_info"`
	AdDetail *AdDetail `protobuf:"bytes,3,opt,name=ad_detail,json=adDetail" json:"ad_detail,omitempty"`
}

func (m *GetAdCodeByIpResp) Reset()                    { *m = GetAdCodeByIpResp{} }
func (m *GetAdCodeByIpResp) String() string            { return proto.CompactTextString(m) }
func (*GetAdCodeByIpResp) ProtoMessage()               {}
func (*GetAdCodeByIpResp) Descriptor() ([]byte, []int) { return fileDescriptorLbs, []int{7} }

func (m *GetAdCodeByIpResp) GetAdCode() uint32 {
	if m != nil {
		return m.AdCode
	}
	return 0
}

func (m *GetAdCodeByIpResp) GetAdInfo() string {
	if m != nil {
		return m.AdInfo
	}
	return ""
}

func (m *GetAdCodeByIpResp) GetAdDetail() *AdDetail {
	if m != nil {
		return m.AdDetail
	}
	return nil
}

type GetAdCodeByLocationReq struct {
	Data *LocationInfo `protobuf:"bytes,1,req,name=data" json:"data,omitempty"`
}

func (m *GetAdCodeByLocationReq) Reset()                    { *m = GetAdCodeByLocationReq{} }
func (m *GetAdCodeByLocationReq) String() string            { return proto.CompactTextString(m) }
func (*GetAdCodeByLocationReq) ProtoMessage()               {}
func (*GetAdCodeByLocationReq) Descriptor() ([]byte, []int) { return fileDescriptorLbs, []int{8} }

func (m *GetAdCodeByLocationReq) GetData() *LocationInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

type GetAdCodeByLocationResp struct {
	AdCode   uint32    `protobuf:"varint,1,req,name=ad_code,json=adCode" json:"ad_code"`
	AdInfo   string    `protobuf:"bytes,2,req,name=ad_info,json=adInfo" json:"ad_info"`
	AdDetail *AdDetail `protobuf:"bytes,3,opt,name=ad_detail,json=adDetail" json:"ad_detail,omitempty"`
}

func (m *GetAdCodeByLocationResp) Reset()                    { *m = GetAdCodeByLocationResp{} }
func (m *GetAdCodeByLocationResp) String() string            { return proto.CompactTextString(m) }
func (*GetAdCodeByLocationResp) ProtoMessage()               {}
func (*GetAdCodeByLocationResp) Descriptor() ([]byte, []int) { return fileDescriptorLbs, []int{9} }

func (m *GetAdCodeByLocationResp) GetAdCode() uint32 {
	if m != nil {
		return m.AdCode
	}
	return 0
}

func (m *GetAdCodeByLocationResp) GetAdInfo() string {
	if m != nil {
		return m.AdInfo
	}
	return ""
}

func (m *GetAdCodeByLocationResp) GetAdDetail() *AdDetail {
	if m != nil {
		return m.AdDetail
	}
	return nil
}

// 获取调用外部接口（百度/腾讯）获得的原始JSON数据
type GetLocationWebApiJsonReq struct {
	Data *LocationInfo `protobuf:"bytes,2,req,name=data" json:"data,omitempty"`
}

func (m *GetLocationWebApiJsonReq) Reset()                    { *m = GetLocationWebApiJsonReq{} }
func (m *GetLocationWebApiJsonReq) String() string            { return proto.CompactTextString(m) }
func (*GetLocationWebApiJsonReq) ProtoMessage()               {}
func (*GetLocationWebApiJsonReq) Descriptor() ([]byte, []int) { return fileDescriptorLbs, []int{10} }

func (m *GetLocationWebApiJsonReq) GetData() *LocationInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

type GetLocationWebApiJsonResp struct {
	JsonInfo string `protobuf:"bytes,1,req,name=json_info,json=jsonInfo" json:"json_info"`
}

func (m *GetLocationWebApiJsonResp) Reset()                    { *m = GetLocationWebApiJsonResp{} }
func (m *GetLocationWebApiJsonResp) String() string            { return proto.CompactTextString(m) }
func (*GetLocationWebApiJsonResp) ProtoMessage()               {}
func (*GetLocationWebApiJsonResp) Descriptor() ([]byte, []int) { return fileDescriptorLbs, []int{11} }

func (m *GetLocationWebApiJsonResp) GetJsonInfo() string {
	if m != nil {
		return m.JsonInfo
	}
	return ""
}

func init() {
	proto.RegisterType((*AdDetail)(nil), "lbs.AdDetail")
	proto.RegisterType((*LocationInfo)(nil), "lbs.LocationInfo")
	proto.RegisterType((*GetLocationByIpReq)(nil), "lbs.GetLocationByIpReq")
	proto.RegisterType((*GetLocationByIpResp)(nil), "lbs.GetLocationByIpResp")
	proto.RegisterType((*DelLocationByIpReq)(nil), "lbs.DelLocationByIpReq")
	proto.RegisterType((*SetLocationByIpReq)(nil), "lbs.SetLocationByIpReq")
	proto.RegisterType((*GetAdCodeByIpReq)(nil), "lbs.GetAdCodeByIpReq")
	proto.RegisterType((*GetAdCodeByIpResp)(nil), "lbs.GetAdCodeByIpResp")
	proto.RegisterType((*GetAdCodeByLocationReq)(nil), "lbs.GetAdCodeByLocationReq")
	proto.RegisterType((*GetAdCodeByLocationResp)(nil), "lbs.GetAdCodeByLocationResp")
	proto.RegisterType((*GetLocationWebApiJsonReq)(nil), "lbs.GetLocationWebApiJsonReq")
	proto.RegisterType((*GetLocationWebApiJsonResp)(nil), "lbs.GetLocationWebApiJsonResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for Lbs service

type LbsClient interface {
	GetLocationByIp(ctx context.Context, in *GetLocationByIpReq, opts ...grpc.CallOption) (*GetLocationByIpResp, error)
	SetLocationByIp(ctx context.Context, in *SetLocationByIpReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetAdCodeByIp(ctx context.Context, in *GetAdCodeByIpReq, opts ...grpc.CallOption) (*GetAdCodeByIpResp, error)
	// 获取调用外部接口（百度/腾讯）获得的原始JSON数据
	GetLocationWebApiJson(ctx context.Context, in *GetLocationWebApiJsonReq, opts ...grpc.CallOption) (*GetLocationWebApiJsonResp, error)
	DelLocationByIp(ctx context.Context, in *DelLocationByIpReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetAdCodeByLocation(ctx context.Context, in *GetAdCodeByLocationReq, opts ...grpc.CallOption) (*GetAdCodeByLocationResp, error)
}

type lbsClient struct {
	cc *grpc.ClientConn
}

func NewLbsClient(cc *grpc.ClientConn) LbsClient {
	return &lbsClient{cc}
}

func (c *lbsClient) GetLocationByIp(ctx context.Context, in *GetLocationByIpReq, opts ...grpc.CallOption) (*GetLocationByIpResp, error) {
	out := new(GetLocationByIpResp)
	err := grpc.Invoke(ctx, "/lbs.lbs/GetLocationByIp", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lbsClient) SetLocationByIp(ctx context.Context, in *SetLocationByIpReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/lbs.lbs/SetLocationByIp", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lbsClient) GetAdCodeByIp(ctx context.Context, in *GetAdCodeByIpReq, opts ...grpc.CallOption) (*GetAdCodeByIpResp, error) {
	out := new(GetAdCodeByIpResp)
	err := grpc.Invoke(ctx, "/lbs.lbs/GetAdCodeByIp", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lbsClient) GetLocationWebApiJson(ctx context.Context, in *GetLocationWebApiJsonReq, opts ...grpc.CallOption) (*GetLocationWebApiJsonResp, error) {
	out := new(GetLocationWebApiJsonResp)
	err := grpc.Invoke(ctx, "/lbs.lbs/GetLocationWebApiJson", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lbsClient) DelLocationByIp(ctx context.Context, in *DelLocationByIpReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/lbs.lbs/DelLocationByIp", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lbsClient) GetAdCodeByLocation(ctx context.Context, in *GetAdCodeByLocationReq, opts ...grpc.CallOption) (*GetAdCodeByLocationResp, error) {
	out := new(GetAdCodeByLocationResp)
	err := grpc.Invoke(ctx, "/lbs.lbs/GetAdCodeByLocation", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for Lbs service

type LbsServer interface {
	GetLocationByIp(context.Context, *GetLocationByIpReq) (*GetLocationByIpResp, error)
	SetLocationByIp(context.Context, *SetLocationByIpReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetAdCodeByIp(context.Context, *GetAdCodeByIpReq) (*GetAdCodeByIpResp, error)
	// 获取调用外部接口（百度/腾讯）获得的原始JSON数据
	GetLocationWebApiJson(context.Context, *GetLocationWebApiJsonReq) (*GetLocationWebApiJsonResp, error)
	DelLocationByIp(context.Context, *DelLocationByIpReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetAdCodeByLocation(context.Context, *GetAdCodeByLocationReq) (*GetAdCodeByLocationResp, error)
}

func RegisterLbsServer(s *grpc.Server, srv LbsServer) {
	s.RegisterService(&_Lbs_serviceDesc, srv)
}

func _Lbs_GetLocationByIp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLocationByIpReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LbsServer).GetLocationByIp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lbs.lbs/GetLocationByIp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LbsServer).GetLocationByIp(ctx, req.(*GetLocationByIpReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lbs_SetLocationByIp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetLocationByIpReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LbsServer).SetLocationByIp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lbs.lbs/SetLocationByIp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LbsServer).SetLocationByIp(ctx, req.(*SetLocationByIpReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lbs_GetAdCodeByIp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAdCodeByIpReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LbsServer).GetAdCodeByIp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lbs.lbs/GetAdCodeByIp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LbsServer).GetAdCodeByIp(ctx, req.(*GetAdCodeByIpReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lbs_GetLocationWebApiJson_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLocationWebApiJsonReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LbsServer).GetLocationWebApiJson(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lbs.lbs/GetLocationWebApiJson",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LbsServer).GetLocationWebApiJson(ctx, req.(*GetLocationWebApiJsonReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lbs_DelLocationByIp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelLocationByIpReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LbsServer).DelLocationByIp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lbs.lbs/DelLocationByIp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LbsServer).DelLocationByIp(ctx, req.(*DelLocationByIpReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lbs_GetAdCodeByLocation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAdCodeByLocationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LbsServer).GetAdCodeByLocation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lbs.lbs/GetAdCodeByLocation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LbsServer).GetAdCodeByLocation(ctx, req.(*GetAdCodeByLocationReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Lbs_serviceDesc = grpc.ServiceDesc{
	ServiceName: "lbs.lbs",
	HandlerType: (*LbsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetLocationByIp",
			Handler:    _Lbs_GetLocationByIp_Handler,
		},
		{
			MethodName: "SetLocationByIp",
			Handler:    _Lbs_SetLocationByIp_Handler,
		},
		{
			MethodName: "GetAdCodeByIp",
			Handler:    _Lbs_GetAdCodeByIp_Handler,
		},
		{
			MethodName: "GetLocationWebApiJson",
			Handler:    _Lbs_GetLocationWebApiJson_Handler,
		},
		{
			MethodName: "DelLocationByIp",
			Handler:    _Lbs_DelLocationByIp_Handler,
		},
		{
			MethodName: "GetAdCodeByLocation",
			Handler:    _Lbs_GetAdCodeByLocation_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/lbssvr/lbs.proto",
}

func (m *AdDetail) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AdDetail) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintLbs(dAtA, i, uint64(len(m.Province)))
	i += copy(dAtA[i:], m.Province)
	dAtA[i] = 0x12
	i++
	i = encodeVarintLbs(dAtA, i, uint64(len(m.City)))
	i += copy(dAtA[i:], m.City)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintLbs(dAtA, i, uint64(len(m.District)))
	i += copy(dAtA[i:], m.District)
	dAtA[i] = 0x22
	i++
	i = encodeVarintLbs(dAtA, i, uint64(len(m.Nation)))
	i += copy(dAtA[i:], m.Nation)
	return i, nil
}

func (m *LocationInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LocationInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xd
	i++
	i = encodeFixed32Lbs(dAtA, i, uint32(math3.Float32bits(float32(m.Lat))))
	dAtA[i] = 0x15
	i++
	i = encodeFixed32Lbs(dAtA, i, uint32(math3.Float32bits(float32(m.Lng))))
	dAtA[i] = 0x18
	i++
	i = encodeVarintLbs(dAtA, i, uint64(m.OpTime))
	if m.AdDetail != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintLbs(dAtA, i, uint64(m.AdDetail.Size()))
		n1, err := m.AdDetail.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *GetLocationByIpReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLocationByIpReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintLbs(dAtA, i, uint64(len(m.Ip)))
	i += copy(dAtA[i:], m.Ip)
	dAtA[i] = 0x10
	i++
	if m.IsNeedAdinfo {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetLocationByIpResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLocationByIpResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Data == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("data")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintLbs(dAtA, i, uint64(m.Data.Size()))
		n2, err := m.Data.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func (m *DelLocationByIpReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelLocationByIpReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintLbs(dAtA, i, uint64(len(m.Ip)))
	i += copy(dAtA[i:], m.Ip)
	return i, nil
}

func (m *SetLocationByIpReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetLocationByIpReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintLbs(dAtA, i, uint64(len(m.Ip)))
	i += copy(dAtA[i:], m.Ip)
	if m.Data == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("data")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintLbs(dAtA, i, uint64(m.Data.Size()))
		n3, err := m.Data.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	return i, nil
}

func (m *GetAdCodeByIpReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAdCodeByIpReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintLbs(dAtA, i, uint64(len(m.Ip)))
	i += copy(dAtA[i:], m.Ip)
	return i, nil
}

func (m *GetAdCodeByIpResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAdCodeByIpResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLbs(dAtA, i, uint64(m.AdCode))
	dAtA[i] = 0x12
	i++
	i = encodeVarintLbs(dAtA, i, uint64(len(m.AdInfo)))
	i += copy(dAtA[i:], m.AdInfo)
	if m.AdDetail != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintLbs(dAtA, i, uint64(m.AdDetail.Size()))
		n4, err := m.AdDetail.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	return i, nil
}

func (m *GetAdCodeByLocationReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAdCodeByLocationReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Data == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("data")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintLbs(dAtA, i, uint64(m.Data.Size()))
		n5, err := m.Data.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	return i, nil
}

func (m *GetAdCodeByLocationResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAdCodeByLocationResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLbs(dAtA, i, uint64(m.AdCode))
	dAtA[i] = 0x12
	i++
	i = encodeVarintLbs(dAtA, i, uint64(len(m.AdInfo)))
	i += copy(dAtA[i:], m.AdInfo)
	if m.AdDetail != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintLbs(dAtA, i, uint64(m.AdDetail.Size()))
		n6, err := m.AdDetail.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	return i, nil
}

func (m *GetLocationWebApiJsonReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLocationWebApiJsonReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Data == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("data")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintLbs(dAtA, i, uint64(m.Data.Size()))
		n7, err := m.Data.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	return i, nil
}

func (m *GetLocationWebApiJsonResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLocationWebApiJsonResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintLbs(dAtA, i, uint64(len(m.JsonInfo)))
	i += copy(dAtA[i:], m.JsonInfo)
	return i, nil
}

func encodeFixed64Lbs(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Lbs(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintLbs(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *AdDetail) Size() (n int) {
	var l int
	_ = l
	l = len(m.Province)
	n += 1 + l + sovLbs(uint64(l))
	l = len(m.City)
	n += 1 + l + sovLbs(uint64(l))
	l = len(m.District)
	n += 1 + l + sovLbs(uint64(l))
	l = len(m.Nation)
	n += 1 + l + sovLbs(uint64(l))
	return n
}

func (m *LocationInfo) Size() (n int) {
	var l int
	_ = l
	n += 5
	n += 5
	n += 1 + sovLbs(uint64(m.OpTime))
	if m.AdDetail != nil {
		l = m.AdDetail.Size()
		n += 1 + l + sovLbs(uint64(l))
	}
	return n
}

func (m *GetLocationByIpReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Ip)
	n += 1 + l + sovLbs(uint64(l))
	n += 2
	return n
}

func (m *GetLocationByIpResp) Size() (n int) {
	var l int
	_ = l
	if m.Data != nil {
		l = m.Data.Size()
		n += 1 + l + sovLbs(uint64(l))
	}
	return n
}

func (m *DelLocationByIpReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Ip)
	n += 1 + l + sovLbs(uint64(l))
	return n
}

func (m *SetLocationByIpReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Ip)
	n += 1 + l + sovLbs(uint64(l))
	if m.Data != nil {
		l = m.Data.Size()
		n += 1 + l + sovLbs(uint64(l))
	}
	return n
}

func (m *GetAdCodeByIpReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Ip)
	n += 1 + l + sovLbs(uint64(l))
	return n
}

func (m *GetAdCodeByIpResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLbs(uint64(m.AdCode))
	l = len(m.AdInfo)
	n += 1 + l + sovLbs(uint64(l))
	if m.AdDetail != nil {
		l = m.AdDetail.Size()
		n += 1 + l + sovLbs(uint64(l))
	}
	return n
}

func (m *GetAdCodeByLocationReq) Size() (n int) {
	var l int
	_ = l
	if m.Data != nil {
		l = m.Data.Size()
		n += 1 + l + sovLbs(uint64(l))
	}
	return n
}

func (m *GetAdCodeByLocationResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLbs(uint64(m.AdCode))
	l = len(m.AdInfo)
	n += 1 + l + sovLbs(uint64(l))
	if m.AdDetail != nil {
		l = m.AdDetail.Size()
		n += 1 + l + sovLbs(uint64(l))
	}
	return n
}

func (m *GetLocationWebApiJsonReq) Size() (n int) {
	var l int
	_ = l
	if m.Data != nil {
		l = m.Data.Size()
		n += 1 + l + sovLbs(uint64(l))
	}
	return n
}

func (m *GetLocationWebApiJsonResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.JsonInfo)
	n += 1 + l + sovLbs(uint64(l))
	return n
}

func sovLbs(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozLbs(x uint64) (n int) {
	return sovLbs(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *AdDetail) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLbs
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AdDetail: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AdDetail: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Province", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLbs
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Province = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field City", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLbs
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.City = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field District", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLbs
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.District = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Nation", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLbs
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Nation = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLbs(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLbs
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("province")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("city")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LocationInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLbs
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LocationInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LocationInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 5 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Lat", wireType)
			}
			var v uint32
			if (iNdEx + 4) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 4
			v = uint32(dAtA[iNdEx-4])
			v |= uint32(dAtA[iNdEx-3]) << 8
			v |= uint32(dAtA[iNdEx-2]) << 16
			v |= uint32(dAtA[iNdEx-1]) << 24
			m.Lat = float32(math4.Float32frombits(v))
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 5 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Lng", wireType)
			}
			var v uint32
			if (iNdEx + 4) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 4
			v = uint32(dAtA[iNdEx-4])
			v |= uint32(dAtA[iNdEx-3]) << 8
			v |= uint32(dAtA[iNdEx-2]) << 16
			v |= uint32(dAtA[iNdEx-1]) << 24
			m.Lng = float32(math4.Float32frombits(v))
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpTime", wireType)
			}
			m.OpTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AdDetail", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLbs
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.AdDetail == nil {
				m.AdDetail = &AdDetail{}
			}
			if err := m.AdDetail.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLbs(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLbs
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("lat")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("lng")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLocationByIpReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLbs
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLocationByIpReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLocationByIpReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ip", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLbs
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Ip = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsNeedAdinfo", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsNeedAdinfo = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipLbs(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLbs
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ip")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLocationByIpResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLbs
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLocationByIpResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLocationByIpResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLbs
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Data == nil {
				m.Data = &LocationInfo{}
			}
			if err := m.Data.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipLbs(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLbs
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("data")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelLocationByIpReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLbs
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelLocationByIpReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelLocationByIpReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ip", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLbs
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Ip = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipLbs(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLbs
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ip")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetLocationByIpReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLbs
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetLocationByIpReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetLocationByIpReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ip", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLbs
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Ip = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLbs
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Data == nil {
				m.Data = &LocationInfo{}
			}
			if err := m.Data.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipLbs(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLbs
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ip")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("data")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAdCodeByIpReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLbs
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAdCodeByIpReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAdCodeByIpReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ip", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLbs
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Ip = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipLbs(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLbs
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ip")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAdCodeByIpResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLbs
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAdCodeByIpResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAdCodeByIpResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AdCode", wireType)
			}
			m.AdCode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AdCode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AdInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLbs
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AdInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AdDetail", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLbs
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.AdDetail == nil {
				m.AdDetail = &AdDetail{}
			}
			if err := m.AdDetail.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLbs(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLbs
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ad_code")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ad_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAdCodeByLocationReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLbs
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAdCodeByLocationReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAdCodeByLocationReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLbs
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Data == nil {
				m.Data = &LocationInfo{}
			}
			if err := m.Data.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipLbs(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLbs
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("data")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAdCodeByLocationResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLbs
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAdCodeByLocationResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAdCodeByLocationResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AdCode", wireType)
			}
			m.AdCode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AdCode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AdInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLbs
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AdInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AdDetail", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLbs
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.AdDetail == nil {
				m.AdDetail = &AdDetail{}
			}
			if err := m.AdDetail.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLbs(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLbs
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ad_code")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ad_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLocationWebApiJsonReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLbs
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLocationWebApiJsonReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLocationWebApiJsonReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLbs
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Data == nil {
				m.Data = &LocationInfo{}
			}
			if err := m.Data.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipLbs(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLbs
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("data")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLocationWebApiJsonResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLbs
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLocationWebApiJsonResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLocationWebApiJsonResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field JsonInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLbs
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.JsonInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipLbs(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLbs
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("json_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipLbs(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowLbs
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowLbs
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowLbs
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthLbs
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowLbs
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipLbs(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthLbs = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowLbs   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/lbssvr/lbs.proto", fileDescriptorLbs) }

var fileDescriptorLbs = []byte{
	// 707 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x54, 0x5d, 0x4f, 0x13, 0x4d,
	0x14, 0x66, 0x77, 0x4b, 0x29, 0x07, 0x78, 0x81, 0x79, 0xf9, 0xd8, 0xb7, 0x2f, 0xd4, 0x75, 0x83,
	0x09, 0x21, 0xa1, 0x24, 0x78, 0x47, 0x9a, 0x9a, 0x56, 0x0c, 0x41, 0x8d, 0xd1, 0x62, 0xf4, 0xca,
	0x34, 0xdb, 0x9d, 0xa1, 0x19, 0xd9, 0xee, 0x8c, 0x9d, 0xb1, 0xb1, 0x89, 0x24, 0x26, 0x26, 0x46,
	0xbd, 0x30, 0xc6, 0xdf, 0xd0, 0x7b, 0xff, 0x06, 0x97, 0xfe, 0x02, 0x63, 0xf0, 0xa6, 0x3f, 0xc3,
	0xec, 0x74, 0x17, 0x76, 0xfb, 0x05, 0x57, 0x5e, 0x41, 0x9f, 0x73, 0xf6, 0x3c, 0xe7, 0x79, 0xce,
	0x99, 0x03, 0x4b, 0xa2, 0xe9, 0xee, 0x78, 0x35, 0x21, 0x5a, 0xcd, 0xe0, 0x4f, 0x9e, 0x37, 0x99,
	0x64, 0xc8, 0xf0, 0x6a, 0x22, 0xbb, 0xe1, 0xb2, 0x46, 0x83, 0xf9, 0x3b, 0xd2, 0x6b, 0x71, 0xea,
	0x9e, 0x78, 0x64, 0x47, 0x9c, 0xd4, 0x5e, 0x53, 0x4f, 0x52, 0x5f, 0xb6, 0x39, 0xe9, 0xa5, 0xda,
	0x1f, 0x34, 0xc8, 0x94, 0xf0, 0x3e, 0x91, 0x0e, 0xf5, 0x90, 0x05, 0x19, 0xde, 0x64, 0x2d, 0xea,
	0xbb, 0xc4, 0xd4, 0x2c, 0x7d, 0x73, 0xba, 0x9c, 0x3a, 0xfb, 0x79, 0x63, 0xa2, 0x72, 0x81, 0x22,
	0x13, 0x52, 0x2e, 0x95, 0x6d, 0x53, 0x8f, 0x45, 0x15, 0x12, 0x7c, 0x8b, 0xa9, 0x90, 0x4d, 0xea,
	0x4a, 0xd3, 0xb0, 0xb4, 0xcb, 0x6f, 0x23, 0x14, 0xad, 0x41, 0xda, 0x77, 0x24, 0x65, 0xbe, 0x99,
	0x8a, 0xc5, 0x43, 0xcc, 0xfe, 0xa4, 0xc1, 0xec, 0x43, 0xe6, 0xaa, 0x1f, 0x87, 0xfe, 0x31, 0x43,
	0x2b, 0x60, 0x78, 0x8e, 0x54, 0x7d, 0xe8, 0x61, 0x6e, 0x00, 0x28, 0xdc, 0xaf, 0xab, 0x0e, 0x2e,
	0x71, 0xbf, 0x8e, 0xd6, 0x61, 0x8a, 0xf1, 0xaa, 0xa4, 0x0d, 0xa2, 0xf8, 0x53, 0x51, 0x7d, 0xc6,
	0x9f, 0xd2, 0x06, 0x41, 0x5b, 0x30, 0xed, 0xe0, 0x2a, 0x56, 0x42, 0x55, 0x03, 0x33, 0xbb, 0x73,
	0xf9, 0xc0, 0xb2, 0x48, 0x7d, 0x25, 0xe3, 0x84, 0xff, 0xd9, 0xcf, 0x00, 0x1d, 0x10, 0x19, 0x75,
	0x53, 0x6e, 0x1f, 0xf2, 0x0a, 0x79, 0x85, 0x96, 0x40, 0xa7, 0x3c, 0xe1, 0x8b, 0x4e, 0x39, 0xda,
	0x82, 0x7f, 0xa8, 0xa8, 0xfa, 0x84, 0xe0, 0xaa, 0x83, 0xa9, 0x7f, 0xcc, 0x4c, 0xdd, 0xd2, 0x36,
	0x33, 0x61, 0xc6, 0x2c, 0x15, 0x8f, 0x08, 0xc1, 0x25, 0x15, 0xb1, 0x0b, 0xf0, 0xef, 0x40, 0x5d,
	0xc1, 0xd1, 0x2d, 0x48, 0x61, 0x47, 0x3a, 0xaa, 0xf4, 0xcc, 0xee, 0xa2, 0xea, 0x2a, 0x6e, 0x45,
	0x45, 0x85, 0xed, 0x2d, 0x40, 0xfb, 0xc4, 0xbb, 0x56, 0x57, 0xf6, 0x13, 0x40, 0x47, 0xd7, 0x55,
	0x10, 0xd1, 0xeb, 0xe3, 0xe9, 0x37, 0x61, 0xe1, 0x80, 0xc8, 0x12, 0xbe, 0xcb, 0x30, 0x19, 0x4f,
	0x7e, 0x0a, 0x8b, 0x7d, 0x99, 0x82, 0x07, 0xe3, 0x71, 0x70, 0xd5, 0x65, 0xb8, 0xb7, 0x5a, 0x73,
	0xd1, 0x78, 0x1c, 0x95, 0x16, 0x86, 0x43, 0xff, 0x2e, 0xcb, 0xa5, 0x1d, 0xac, 0x96, 0x21, 0x31,
	0x3d, 0x63, 0xfc, 0xf4, 0xee, 0xc0, 0x4a, 0x8c, 0x3e, 0x52, 0x12, 0xb4, 0x7b, 0x4d, 0xa3, 0xdf,
	0x6b, 0xb0, 0x3a, 0xb4, 0xc2, 0x5f, 0x95, 0x51, 0x02, 0x33, 0xb6, 0x2c, 0xcf, 0x49, 0xad, 0xc4,
	0xe9, 0x7d, 0x91, 0x14, 0x72, 0xc5, 0xc8, 0x8a, 0xf0, 0xdf, 0x88, 0x12, 0x82, 0xa3, 0x9b, 0x30,
	0xfd, 0x52, 0x30, 0xbf, 0xd7, 0x6c, 0xe2, 0xb5, 0x07, 0x70, 0x50, 0x65, 0xf7, 0xfb, 0x24, 0x04,
	0xa7, 0x04, 0xb9, 0x30, 0xdf, 0xb7, 0xb7, 0x68, 0x55, 0x71, 0x0e, 0xbe, 0x92, 0xac, 0x39, 0x3c,
	0x20, 0xb8, 0xbd, 0xfe, 0xae, 0xd3, 0x35, 0xb4, 0xcf, 0x9d, 0xae, 0xa1, 0xd3, 0xbd, 0x6f, 0x9d,
	0xae, 0x31, 0xbb, 0x4d, 0xad, 0x02, 0xe5, 0x55, 0x07, 0xe3, 0x66, 0x11, 0x9d, 0xc2, 0xfc, 0xd1,
	0x50, 0x92, 0xc1, 0x45, 0xce, 0xae, 0xe5, 0x2f, 0xae, 0x5a, 0xfe, 0xe8, 0x41, 0xb9, 0x77, 0xd5,
	0xee, 0x35, 0xb8, 0x6c, 0x57, 0x1f, 0x97, 0xed, 0xdb, 0x01, 0x91, 0x1e, 0x10, 0xa5, 0xe9, 0x9e,
	0xdc, 0xab, 0x2b, 0xb2, 0x5c, 0x9c, 0xcc, 0xda, 0x96, 0x56, 0xc1, 0x73, 0x64, 0xd1, 0xda, 0xae,
	0x5b, 0x05, 0xcf, 0xaf, 0x17, 0xd1, 0x0b, 0x98, 0x4b, 0x2c, 0x2d, 0x5a, 0x8e, 0x84, 0x24, 0x56,
	0x3e, 0xbb, 0x32, 0x0c, 0x8e, 0xd4, 0x19, 0x23, 0xd5, 0xbd, 0x85, 0xe5, 0xa1, 0xa3, 0x40, 0xeb,
	0xfd, 0x7e, 0x25, 0x26, 0x9d, 0xcd, 0x8d, 0x0b, 0x0b, 0x6e, 0x6f, 0x04, 0xb4, 0x93, 0x01, 0x6d,
	0x2a, 0x52, 0xba, 0x38, 0x28, 0xae, 0x0e, 0xf3, 0x7d, 0xa7, 0x23, 0xf4, 0x76, 0xf0, 0xa0, 0x5c,
	0xe1, 0xad, 0x92, 0x99, 0x1e, 0x29, 0xb3, 0xa5, 0x2e, 0x5c, 0xff, 0xcb, 0x41, 0xff, 0xf7, 0x9b,
	0x16, 0x7b, 0x95, 0xd9, 0xb5, 0xd1, 0xc1, 0x48, 0xe0, 0xd4, 0x15, 0x02, 0xb3, 0xe9, 0x8f, 0x9d,
	0xae, 0xf1, 0xe5, 0x4d, 0x79, 0xe1, 0xec, 0x3c, 0xa7, 0xfd, 0x38, 0xcf, 0x69, 0xbf, 0xce, 0x73,
	0xda, 0xd7, 0xdf, 0xb9, 0x89, 0x3f, 0x01, 0x00, 0x00, 0xff, 0xff, 0x40, 0x25, 0xc6, 0xcf, 0x22,
	0x07, 0x00, 0x00,
}
