// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-mic-middle/channel-mic-middle.proto

package channel_mic_middle // import "golang.52tt.com/protocol/services/channel-mic-middle"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/extension/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 麦位操作的权限
type MicPermission int32

const (
	MicPermission_Invalid         MicPermission = 0
	MicPermission_ModifyMicStatus MicPermission = 1
	MicPermission_KickOnMicUser   MicPermission = 2
)

var MicPermission_name = map[int32]string{
	0: "Invalid",
	1: "ModifyMicStatus",
	2: "KickOnMicUser",
}
var MicPermission_value = map[string]int32{
	"Invalid":         0,
	"ModifyMicStatus": 1,
	"KickOnMicUser":   2,
}

func (x MicPermission) String() string {
	return proto.EnumName(MicPermission_name, int32(x))
}
func (MicPermission) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_mic_middle_494d55eb4a4a4a49, []int{0}
}

// 麦位信息
type MicSpaceInfo struct {
	MicId                uint32   `protobuf:"varint,1,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	MicState             uint32   `protobuf:"varint,2,opt,name=mic_state,json=micState,proto3" json:"mic_state,omitempty"`
	MicUid               uint32   `protobuf:"varint,3,opt,name=mic_uid,json=micUid,proto3" json:"mic_uid,omitempty"`
	MicTs                uint32   `protobuf:"varint,4,opt,name=mic_ts,json=micTs,proto3" json:"mic_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MicSpaceInfo) Reset()         { *m = MicSpaceInfo{} }
func (m *MicSpaceInfo) String() string { return proto.CompactTextString(m) }
func (*MicSpaceInfo) ProtoMessage()    {}
func (*MicSpaceInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_mic_middle_494d55eb4a4a4a49, []int{0}
}
func (m *MicSpaceInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MicSpaceInfo.Unmarshal(m, b)
}
func (m *MicSpaceInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MicSpaceInfo.Marshal(b, m, deterministic)
}
func (dst *MicSpaceInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MicSpaceInfo.Merge(dst, src)
}
func (m *MicSpaceInfo) XXX_Size() int {
	return xxx_messageInfo_MicSpaceInfo.Size(m)
}
func (m *MicSpaceInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MicSpaceInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MicSpaceInfo proto.InternalMessageInfo

func (m *MicSpaceInfo) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *MicSpaceInfo) GetMicState() uint32 {
	if m != nil {
		return m.MicState
	}
	return 0
}

func (m *MicSpaceInfo) GetMicUid() uint32 {
	if m != nil {
		return m.MicUid
	}
	return 0
}

func (m *MicSpaceInfo) GetMicTs() uint32 {
	if m != nil {
		return m.MicTs
	}
	return 0
}

type HoldMicReq struct {
	Source               string          `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	Uid                  uint32          `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Cid                  uint32          `protobuf:"varint,3,opt,name=cid,proto3" json:"cid,omitempty"`
	MicId                uint32          `protobuf:"varint,4,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	Permissions          []MicPermission `protobuf:"varint,5,rep,packed,name=permissions,proto3,enum=channel_mic_middle.MicPermission" json:"permissions,omitempty"`
	AppId                uint32          `protobuf:"varint,6,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	MarketId             uint32          `protobuf:"varint,7,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *HoldMicReq) Reset()         { *m = HoldMicReq{} }
func (m *HoldMicReq) String() string { return proto.CompactTextString(m) }
func (*HoldMicReq) ProtoMessage()    {}
func (*HoldMicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_mic_middle_494d55eb4a4a4a49, []int{1}
}
func (m *HoldMicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HoldMicReq.Unmarshal(m, b)
}
func (m *HoldMicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HoldMicReq.Marshal(b, m, deterministic)
}
func (dst *HoldMicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HoldMicReq.Merge(dst, src)
}
func (m *HoldMicReq) XXX_Size() int {
	return xxx_messageInfo_HoldMicReq.Size(m)
}
func (m *HoldMicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_HoldMicReq.DiscardUnknown(m)
}

var xxx_messageInfo_HoldMicReq proto.InternalMessageInfo

func (m *HoldMicReq) GetSource() string {
	if m != nil {
		return m.Source
	}
	return ""
}

func (m *HoldMicReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *HoldMicReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *HoldMicReq) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *HoldMicReq) GetPermissions() []MicPermission {
	if m != nil {
		return m.Permissions
	}
	return nil
}

func (m *HoldMicReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *HoldMicReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

type HoldMicResp struct {
	HoldMicInfo          *MicSpaceInfo   `protobuf:"bytes,1,opt,name=hold_mic_info,json=holdMicInfo,proto3" json:"hold_mic_info,omitempty"`
	KickedUid            uint32          `protobuf:"varint,2,opt,name=kicked_uid,json=kickedUid,proto3" json:"kicked_uid,omitempty"`
	AllMicList           []*MicSpaceInfo `protobuf:"bytes,3,rep,name=all_mic_list,json=allMicList,proto3" json:"all_mic_list,omitempty"`
	ServerTimeMs         uint64          `protobuf:"varint,4,opt,name=server_time_ms,json=serverTimeMs,proto3" json:"server_time_ms,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *HoldMicResp) Reset()         { *m = HoldMicResp{} }
func (m *HoldMicResp) String() string { return proto.CompactTextString(m) }
func (*HoldMicResp) ProtoMessage()    {}
func (*HoldMicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_mic_middle_494d55eb4a4a4a49, []int{2}
}
func (m *HoldMicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HoldMicResp.Unmarshal(m, b)
}
func (m *HoldMicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HoldMicResp.Marshal(b, m, deterministic)
}
func (dst *HoldMicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HoldMicResp.Merge(dst, src)
}
func (m *HoldMicResp) XXX_Size() int {
	return xxx_messageInfo_HoldMicResp.Size(m)
}
func (m *HoldMicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_HoldMicResp.DiscardUnknown(m)
}

var xxx_messageInfo_HoldMicResp proto.InternalMessageInfo

func (m *HoldMicResp) GetHoldMicInfo() *MicSpaceInfo {
	if m != nil {
		return m.HoldMicInfo
	}
	return nil
}

func (m *HoldMicResp) GetKickedUid() uint32 {
	if m != nil {
		return m.KickedUid
	}
	return 0
}

func (m *HoldMicResp) GetAllMicList() []*MicSpaceInfo {
	if m != nil {
		return m.AllMicList
	}
	return nil
}

func (m *HoldMicResp) GetServerTimeMs() uint64 {
	if m != nil {
		return m.ServerTimeMs
	}
	return 0
}

type TakeHoldMicReq struct {
	Source               string          `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	OpUid                uint32          `protobuf:"varint,2,opt,name=op_uid,json=opUid,proto3" json:"op_uid,omitempty"`
	TargetUid            uint32          `protobuf:"varint,3,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	Cid                  uint32          `protobuf:"varint,4,opt,name=cid,proto3" json:"cid,omitempty"`
	MicId                uint32          `protobuf:"varint,5,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	Permissions          []MicPermission `protobuf:"varint,6,rep,packed,name=permissions,proto3,enum=channel_mic_middle.MicPermission" json:"permissions,omitempty"`
	AppId                uint32          `protobuf:"varint,7,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	MarketId             uint32          `protobuf:"varint,8,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *TakeHoldMicReq) Reset()         { *m = TakeHoldMicReq{} }
func (m *TakeHoldMicReq) String() string { return proto.CompactTextString(m) }
func (*TakeHoldMicReq) ProtoMessage()    {}
func (*TakeHoldMicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_mic_middle_494d55eb4a4a4a49, []int{3}
}
func (m *TakeHoldMicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TakeHoldMicReq.Unmarshal(m, b)
}
func (m *TakeHoldMicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TakeHoldMicReq.Marshal(b, m, deterministic)
}
func (dst *TakeHoldMicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TakeHoldMicReq.Merge(dst, src)
}
func (m *TakeHoldMicReq) XXX_Size() int {
	return xxx_messageInfo_TakeHoldMicReq.Size(m)
}
func (m *TakeHoldMicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TakeHoldMicReq.DiscardUnknown(m)
}

var xxx_messageInfo_TakeHoldMicReq proto.InternalMessageInfo

func (m *TakeHoldMicReq) GetSource() string {
	if m != nil {
		return m.Source
	}
	return ""
}

func (m *TakeHoldMicReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *TakeHoldMicReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *TakeHoldMicReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *TakeHoldMicReq) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *TakeHoldMicReq) GetPermissions() []MicPermission {
	if m != nil {
		return m.Permissions
	}
	return nil
}

func (m *TakeHoldMicReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *TakeHoldMicReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

type TakeHoldMicResp struct {
	HoldMicInfo          *MicSpaceInfo   `protobuf:"bytes,1,opt,name=hold_mic_info,json=holdMicInfo,proto3" json:"hold_mic_info,omitempty"`
	KickedUid            uint32          `protobuf:"varint,2,opt,name=kicked_uid,json=kickedUid,proto3" json:"kicked_uid,omitempty"`
	AllMicList           []*MicSpaceInfo `protobuf:"bytes,3,rep,name=all_mic_list,json=allMicList,proto3" json:"all_mic_list,omitempty"`
	ServerTimeMs         uint64          `protobuf:"varint,4,opt,name=server_time_ms,json=serverTimeMs,proto3" json:"server_time_ms,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *TakeHoldMicResp) Reset()         { *m = TakeHoldMicResp{} }
func (m *TakeHoldMicResp) String() string { return proto.CompactTextString(m) }
func (*TakeHoldMicResp) ProtoMessage()    {}
func (*TakeHoldMicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_mic_middle_494d55eb4a4a4a49, []int{4}
}
func (m *TakeHoldMicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TakeHoldMicResp.Unmarshal(m, b)
}
func (m *TakeHoldMicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TakeHoldMicResp.Marshal(b, m, deterministic)
}
func (dst *TakeHoldMicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TakeHoldMicResp.Merge(dst, src)
}
func (m *TakeHoldMicResp) XXX_Size() int {
	return xxx_messageInfo_TakeHoldMicResp.Size(m)
}
func (m *TakeHoldMicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TakeHoldMicResp.DiscardUnknown(m)
}

var xxx_messageInfo_TakeHoldMicResp proto.InternalMessageInfo

func (m *TakeHoldMicResp) GetHoldMicInfo() *MicSpaceInfo {
	if m != nil {
		return m.HoldMicInfo
	}
	return nil
}

func (m *TakeHoldMicResp) GetKickedUid() uint32 {
	if m != nil {
		return m.KickedUid
	}
	return 0
}

func (m *TakeHoldMicResp) GetAllMicList() []*MicSpaceInfo {
	if m != nil {
		return m.AllMicList
	}
	return nil
}

func (m *TakeHoldMicResp) GetServerTimeMs() uint64 {
	if m != nil {
		return m.ServerTimeMs
	}
	return 0
}

type ChangeMicReq struct {
	Source               string   `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Cid                  uint32   `protobuf:"varint,3,opt,name=cid,proto3" json:"cid,omitempty"`
	MicId                uint32   `protobuf:"varint,4,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeMicReq) Reset()         { *m = ChangeMicReq{} }
func (m *ChangeMicReq) String() string { return proto.CompactTextString(m) }
func (*ChangeMicReq) ProtoMessage()    {}
func (*ChangeMicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_mic_middle_494d55eb4a4a4a49, []int{5}
}
func (m *ChangeMicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeMicReq.Unmarshal(m, b)
}
func (m *ChangeMicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeMicReq.Marshal(b, m, deterministic)
}
func (dst *ChangeMicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeMicReq.Merge(dst, src)
}
func (m *ChangeMicReq) XXX_Size() int {
	return xxx_messageInfo_ChangeMicReq.Size(m)
}
func (m *ChangeMicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeMicReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeMicReq proto.InternalMessageInfo

func (m *ChangeMicReq) GetSource() string {
	if m != nil {
		return m.Source
	}
	return ""
}

func (m *ChangeMicReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChangeMicReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *ChangeMicReq) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

type ChangeMicResp struct {
	FromMicInfo          *MicSpaceInfo   `protobuf:"bytes,1,opt,name=from_mic_info,json=fromMicInfo,proto3" json:"from_mic_info,omitempty"`
	ToMicInfo            *MicSpaceInfo   `protobuf:"bytes,2,opt,name=to_mic_info,json=toMicInfo,proto3" json:"to_mic_info,omitempty"`
	ServerTimeMs         uint64          `protobuf:"varint,3,opt,name=server_time_ms,json=serverTimeMs,proto3" json:"server_time_ms,omitempty"`
	AllMicList           []*MicSpaceInfo `protobuf:"bytes,4,rep,name=all_mic_list,json=allMicList,proto3" json:"all_mic_list,omitempty"`
	MicMode              uint32          `protobuf:"varint,5,opt,name=mic_mode,json=micMode,proto3" json:"mic_mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ChangeMicResp) Reset()         { *m = ChangeMicResp{} }
func (m *ChangeMicResp) String() string { return proto.CompactTextString(m) }
func (*ChangeMicResp) ProtoMessage()    {}
func (*ChangeMicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_mic_middle_494d55eb4a4a4a49, []int{6}
}
func (m *ChangeMicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeMicResp.Unmarshal(m, b)
}
func (m *ChangeMicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeMicResp.Marshal(b, m, deterministic)
}
func (dst *ChangeMicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeMicResp.Merge(dst, src)
}
func (m *ChangeMicResp) XXX_Size() int {
	return xxx_messageInfo_ChangeMicResp.Size(m)
}
func (m *ChangeMicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeMicResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeMicResp proto.InternalMessageInfo

func (m *ChangeMicResp) GetFromMicInfo() *MicSpaceInfo {
	if m != nil {
		return m.FromMicInfo
	}
	return nil
}

func (m *ChangeMicResp) GetToMicInfo() *MicSpaceInfo {
	if m != nil {
		return m.ToMicInfo
	}
	return nil
}

func (m *ChangeMicResp) GetServerTimeMs() uint64 {
	if m != nil {
		return m.ServerTimeMs
	}
	return 0
}

func (m *ChangeMicResp) GetAllMicList() []*MicSpaceInfo {
	if m != nil {
		return m.AllMicList
	}
	return nil
}

func (m *ChangeMicResp) GetMicMode() uint32 {
	if m != nil {
		return m.MicMode
	}
	return 0
}

type ReleaseMicReq struct {
	Source               string   `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Cid                  uint32   `protobuf:"varint,3,opt,name=cid,proto3" json:"cid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReleaseMicReq) Reset()         { *m = ReleaseMicReq{} }
func (m *ReleaseMicReq) String() string { return proto.CompactTextString(m) }
func (*ReleaseMicReq) ProtoMessage()    {}
func (*ReleaseMicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_mic_middle_494d55eb4a4a4a49, []int{7}
}
func (m *ReleaseMicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReleaseMicReq.Unmarshal(m, b)
}
func (m *ReleaseMicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReleaseMicReq.Marshal(b, m, deterministic)
}
func (dst *ReleaseMicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReleaseMicReq.Merge(dst, src)
}
func (m *ReleaseMicReq) XXX_Size() int {
	return xxx_messageInfo_ReleaseMicReq.Size(m)
}
func (m *ReleaseMicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReleaseMicReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReleaseMicReq proto.InternalMessageInfo

func (m *ReleaseMicReq) GetSource() string {
	if m != nil {
		return m.Source
	}
	return ""
}

func (m *ReleaseMicReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReleaseMicReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

type ReleaseMicResp struct {
	CloseMicInfo         *MicSpaceInfo   `protobuf:"bytes,1,opt,name=close_mic_info,json=closeMicInfo,proto3" json:"close_mic_info,omitempty"`
	AllMicList           []*MicSpaceInfo `protobuf:"bytes,2,rep,name=all_mic_list,json=allMicList,proto3" json:"all_mic_list,omitempty"`
	ServerTimeMs         uint64          `protobuf:"varint,3,opt,name=server_time_ms,json=serverTimeMs,proto3" json:"server_time_ms,omitempty"`
	IsAutoDisableMic     bool            `protobuf:"varint,4,opt,name=is_auto_disable_mic,json=isAutoDisableMic,proto3" json:"is_auto_disable_mic,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ReleaseMicResp) Reset()         { *m = ReleaseMicResp{} }
func (m *ReleaseMicResp) String() string { return proto.CompactTextString(m) }
func (*ReleaseMicResp) ProtoMessage()    {}
func (*ReleaseMicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_mic_middle_494d55eb4a4a4a49, []int{8}
}
func (m *ReleaseMicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReleaseMicResp.Unmarshal(m, b)
}
func (m *ReleaseMicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReleaseMicResp.Marshal(b, m, deterministic)
}
func (dst *ReleaseMicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReleaseMicResp.Merge(dst, src)
}
func (m *ReleaseMicResp) XXX_Size() int {
	return xxx_messageInfo_ReleaseMicResp.Size(m)
}
func (m *ReleaseMicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReleaseMicResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReleaseMicResp proto.InternalMessageInfo

func (m *ReleaseMicResp) GetCloseMicInfo() *MicSpaceInfo {
	if m != nil {
		return m.CloseMicInfo
	}
	return nil
}

func (m *ReleaseMicResp) GetAllMicList() []*MicSpaceInfo {
	if m != nil {
		return m.AllMicList
	}
	return nil
}

func (m *ReleaseMicResp) GetServerTimeMs() uint64 {
	if m != nil {
		return m.ServerTimeMs
	}
	return 0
}

func (m *ReleaseMicResp) GetIsAutoDisableMic() bool {
	if m != nil {
		return m.IsAutoDisableMic
	}
	return false
}

type SetMicStatusReq struct {
	Source               string   `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	OpUid                uint32   `protobuf:"varint,2,opt,name=op_uid,json=opUid,proto3" json:"op_uid,omitempty"`
	Cid                  uint32   `protobuf:"varint,3,opt,name=cid,proto3" json:"cid,omitempty"`
	MicIdList            []uint32 `protobuf:"varint,4,rep,packed,name=mic_id_list,json=micIdList,proto3" json:"mic_id_list,omitempty"`
	MicState             uint32   `protobuf:"varint,5,opt,name=mic_state,json=micState,proto3" json:"mic_state,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetMicStatusReq) Reset()         { *m = SetMicStatusReq{} }
func (m *SetMicStatusReq) String() string { return proto.CompactTextString(m) }
func (*SetMicStatusReq) ProtoMessage()    {}
func (*SetMicStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_mic_middle_494d55eb4a4a4a49, []int{9}
}
func (m *SetMicStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMicStatusReq.Unmarshal(m, b)
}
func (m *SetMicStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMicStatusReq.Marshal(b, m, deterministic)
}
func (dst *SetMicStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMicStatusReq.Merge(dst, src)
}
func (m *SetMicStatusReq) XXX_Size() int {
	return xxx_messageInfo_SetMicStatusReq.Size(m)
}
func (m *SetMicStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMicStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetMicStatusReq proto.InternalMessageInfo

func (m *SetMicStatusReq) GetSource() string {
	if m != nil {
		return m.Source
	}
	return ""
}

func (m *SetMicStatusReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *SetMicStatusReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *SetMicStatusReq) GetMicIdList() []uint32 {
	if m != nil {
		return m.MicIdList
	}
	return nil
}

func (m *SetMicStatusReq) GetMicState() uint32 {
	if m != nil {
		return m.MicState
	}
	return 0
}

type SetMicStatusResp struct {
	AllMicList           []*MicSpaceInfo `protobuf:"bytes,1,rep,name=all_mic_list,json=allMicList,proto3" json:"all_mic_list,omitempty"`
	KickedMicList        []*MicSpaceInfo `protobuf:"bytes,2,rep,name=kicked_mic_list,json=kickedMicList,proto3" json:"kicked_mic_list,omitempty"`
	ServerTimeMs         uint64          `protobuf:"varint,3,opt,name=server_time_ms,json=serverTimeMs,proto3" json:"server_time_ms,omitempty"`
	MicMode              uint32          `protobuf:"varint,4,opt,name=mic_mode,json=micMode,proto3" json:"mic_mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *SetMicStatusResp) Reset()         { *m = SetMicStatusResp{} }
func (m *SetMicStatusResp) String() string { return proto.CompactTextString(m) }
func (*SetMicStatusResp) ProtoMessage()    {}
func (*SetMicStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_mic_middle_494d55eb4a4a4a49, []int{10}
}
func (m *SetMicStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMicStatusResp.Unmarshal(m, b)
}
func (m *SetMicStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMicStatusResp.Marshal(b, m, deterministic)
}
func (dst *SetMicStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMicStatusResp.Merge(dst, src)
}
func (m *SetMicStatusResp) XXX_Size() int {
	return xxx_messageInfo_SetMicStatusResp.Size(m)
}
func (m *SetMicStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMicStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetMicStatusResp proto.InternalMessageInfo

func (m *SetMicStatusResp) GetAllMicList() []*MicSpaceInfo {
	if m != nil {
		return m.AllMicList
	}
	return nil
}

func (m *SetMicStatusResp) GetKickedMicList() []*MicSpaceInfo {
	if m != nil {
		return m.KickedMicList
	}
	return nil
}

func (m *SetMicStatusResp) GetServerTimeMs() uint64 {
	if m != nil {
		return m.ServerTimeMs
	}
	return 0
}

func (m *SetMicStatusResp) GetMicMode() uint32 {
	if m != nil {
		return m.MicMode
	}
	return 0
}

type KickOutMicReq struct {
	Source               string   `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	OpUid                uint32   `protobuf:"varint,2,opt,name=op_uid,json=opUid,proto3" json:"op_uid,omitempty"`
	Cid                  uint32   `protobuf:"varint,3,opt,name=cid,proto3" json:"cid,omitempty"`
	TargetUidList        []uint32 `protobuf:"varint,4,rep,packed,name=target_uid_list,json=targetUidList,proto3" json:"target_uid_list,omitempty"`
	BanSecond            uint32   `protobuf:"varint,5,opt,name=ban_second,json=banSecond,proto3" json:"ban_second,omitempty"`
	Toasts               string   `protobuf:"bytes,6,opt,name=toasts,proto3" json:"toasts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KickOutMicReq) Reset()         { *m = KickOutMicReq{} }
func (m *KickOutMicReq) String() string { return proto.CompactTextString(m) }
func (*KickOutMicReq) ProtoMessage()    {}
func (*KickOutMicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_mic_middle_494d55eb4a4a4a49, []int{11}
}
func (m *KickOutMicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KickOutMicReq.Unmarshal(m, b)
}
func (m *KickOutMicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KickOutMicReq.Marshal(b, m, deterministic)
}
func (dst *KickOutMicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KickOutMicReq.Merge(dst, src)
}
func (m *KickOutMicReq) XXX_Size() int {
	return xxx_messageInfo_KickOutMicReq.Size(m)
}
func (m *KickOutMicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_KickOutMicReq.DiscardUnknown(m)
}

var xxx_messageInfo_KickOutMicReq proto.InternalMessageInfo

func (m *KickOutMicReq) GetSource() string {
	if m != nil {
		return m.Source
	}
	return ""
}

func (m *KickOutMicReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *KickOutMicReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *KickOutMicReq) GetTargetUidList() []uint32 {
	if m != nil {
		return m.TargetUidList
	}
	return nil
}

func (m *KickOutMicReq) GetBanSecond() uint32 {
	if m != nil {
		return m.BanSecond
	}
	return 0
}

func (m *KickOutMicReq) GetToasts() string {
	if m != nil {
		return m.Toasts
	}
	return ""
}

type KickOutMicResp struct {
	DisableMicIdList     []uint32        `protobuf:"varint,1,rep,packed,name=disable_mic_id_list,json=disableMicIdList,proto3" json:"disable_mic_id_list,omitempty"`
	KickedMicList        []*MicSpaceInfo `protobuf:"bytes,2,rep,name=kicked_mic_list,json=kickedMicList,proto3" json:"kicked_mic_list,omitempty"`
	AllMicList           []*MicSpaceInfo `protobuf:"bytes,3,rep,name=all_mic_list,json=allMicList,proto3" json:"all_mic_list,omitempty"`
	ServerTimeMs         uint64          `protobuf:"varint,4,opt,name=server_time_ms,json=serverTimeMs,proto3" json:"server_time_ms,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *KickOutMicResp) Reset()         { *m = KickOutMicResp{} }
func (m *KickOutMicResp) String() string { return proto.CompactTextString(m) }
func (*KickOutMicResp) ProtoMessage()    {}
func (*KickOutMicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_mic_middle_494d55eb4a4a4a49, []int{12}
}
func (m *KickOutMicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KickOutMicResp.Unmarshal(m, b)
}
func (m *KickOutMicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KickOutMicResp.Marshal(b, m, deterministic)
}
func (dst *KickOutMicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KickOutMicResp.Merge(dst, src)
}
func (m *KickOutMicResp) XXX_Size() int {
	return xxx_messageInfo_KickOutMicResp.Size(m)
}
func (m *KickOutMicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_KickOutMicResp.DiscardUnknown(m)
}

var xxx_messageInfo_KickOutMicResp proto.InternalMessageInfo

func (m *KickOutMicResp) GetDisableMicIdList() []uint32 {
	if m != nil {
		return m.DisableMicIdList
	}
	return nil
}

func (m *KickOutMicResp) GetKickedMicList() []*MicSpaceInfo {
	if m != nil {
		return m.KickedMicList
	}
	return nil
}

func (m *KickOutMicResp) GetAllMicList() []*MicSpaceInfo {
	if m != nil {
		return m.AllMicList
	}
	return nil
}

func (m *KickOutMicResp) GetServerTimeMs() uint64 {
	if m != nil {
		return m.ServerTimeMs
	}
	return 0
}

type SetAutoDisableMicSwitchReq struct {
	Source               string   `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	OpUid                uint32   `protobuf:"varint,2,opt,name=op_uid,json=opUid,proto3" json:"op_uid,omitempty"`
	Cid                  uint32   `protobuf:"varint,3,opt,name=cid,proto3" json:"cid,omitempty"`
	SwitchOn             bool     `protobuf:"varint,4,opt,name=switch_on,json=switchOn,proto3" json:"switch_on,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetAutoDisableMicSwitchReq) Reset()         { *m = SetAutoDisableMicSwitchReq{} }
func (m *SetAutoDisableMicSwitchReq) String() string { return proto.CompactTextString(m) }
func (*SetAutoDisableMicSwitchReq) ProtoMessage()    {}
func (*SetAutoDisableMicSwitchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_mic_middle_494d55eb4a4a4a49, []int{13}
}
func (m *SetAutoDisableMicSwitchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetAutoDisableMicSwitchReq.Unmarshal(m, b)
}
func (m *SetAutoDisableMicSwitchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetAutoDisableMicSwitchReq.Marshal(b, m, deterministic)
}
func (dst *SetAutoDisableMicSwitchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetAutoDisableMicSwitchReq.Merge(dst, src)
}
func (m *SetAutoDisableMicSwitchReq) XXX_Size() int {
	return xxx_messageInfo_SetAutoDisableMicSwitchReq.Size(m)
}
func (m *SetAutoDisableMicSwitchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetAutoDisableMicSwitchReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetAutoDisableMicSwitchReq proto.InternalMessageInfo

func (m *SetAutoDisableMicSwitchReq) GetSource() string {
	if m != nil {
		return m.Source
	}
	return ""
}

func (m *SetAutoDisableMicSwitchReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *SetAutoDisableMicSwitchReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *SetAutoDisableMicSwitchReq) GetSwitchOn() bool {
	if m != nil {
		return m.SwitchOn
	}
	return false
}

type SetAutoDisableMicSwitchResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetAutoDisableMicSwitchResp) Reset()         { *m = SetAutoDisableMicSwitchResp{} }
func (m *SetAutoDisableMicSwitchResp) String() string { return proto.CompactTextString(m) }
func (*SetAutoDisableMicSwitchResp) ProtoMessage()    {}
func (*SetAutoDisableMicSwitchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_mic_middle_494d55eb4a4a4a49, []int{14}
}
func (m *SetAutoDisableMicSwitchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetAutoDisableMicSwitchResp.Unmarshal(m, b)
}
func (m *SetAutoDisableMicSwitchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetAutoDisableMicSwitchResp.Marshal(b, m, deterministic)
}
func (dst *SetAutoDisableMicSwitchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetAutoDisableMicSwitchResp.Merge(dst, src)
}
func (m *SetAutoDisableMicSwitchResp) XXX_Size() int {
	return xxx_messageInfo_SetAutoDisableMicSwitchResp.Size(m)
}
func (m *SetAutoDisableMicSwitchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetAutoDisableMicSwitchResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetAutoDisableMicSwitchResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*MicSpaceInfo)(nil), "channel_mic_middle.MicSpaceInfo")
	proto.RegisterType((*HoldMicReq)(nil), "channel_mic_middle.HoldMicReq")
	proto.RegisterType((*HoldMicResp)(nil), "channel_mic_middle.HoldMicResp")
	proto.RegisterType((*TakeHoldMicReq)(nil), "channel_mic_middle.TakeHoldMicReq")
	proto.RegisterType((*TakeHoldMicResp)(nil), "channel_mic_middle.TakeHoldMicResp")
	proto.RegisterType((*ChangeMicReq)(nil), "channel_mic_middle.ChangeMicReq")
	proto.RegisterType((*ChangeMicResp)(nil), "channel_mic_middle.ChangeMicResp")
	proto.RegisterType((*ReleaseMicReq)(nil), "channel_mic_middle.ReleaseMicReq")
	proto.RegisterType((*ReleaseMicResp)(nil), "channel_mic_middle.ReleaseMicResp")
	proto.RegisterType((*SetMicStatusReq)(nil), "channel_mic_middle.SetMicStatusReq")
	proto.RegisterType((*SetMicStatusResp)(nil), "channel_mic_middle.SetMicStatusResp")
	proto.RegisterType((*KickOutMicReq)(nil), "channel_mic_middle.KickOutMicReq")
	proto.RegisterType((*KickOutMicResp)(nil), "channel_mic_middle.KickOutMicResp")
	proto.RegisterType((*SetAutoDisableMicSwitchReq)(nil), "channel_mic_middle.SetAutoDisableMicSwitchReq")
	proto.RegisterType((*SetAutoDisableMicSwitchResp)(nil), "channel_mic_middle.SetAutoDisableMicSwitchResp")
	proto.RegisterEnum("channel_mic_middle.MicPermission", MicPermission_name, MicPermission_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelMicMiddleClient is the client API for ChannelMicMiddle service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelMicMiddleClient interface {
	// 上麦
	HoldMic(ctx context.Context, in *HoldMicReq, opts ...grpc.CallOption) (*HoldMicResp, error)
	// 抱上麦
	TakeHoldMic(ctx context.Context, in *TakeHoldMicReq, opts ...grpc.CallOption) (*TakeHoldMicResp, error)
	// 换麦
	ChangeMic(ctx context.Context, in *ChangeMicReq, opts ...grpc.CallOption) (*ChangeMicResp, error)
	// 下麦
	ReleaseMic(ctx context.Context, in *ReleaseMicReq, opts ...grpc.CallOption) (*ReleaseMicResp, error)
	// 设置麦位状态
	SetMicStatus(ctx context.Context, in *SetMicStatusReq, opts ...grpc.CallOption) (*SetMicStatusResp, error)
	// 踢下麦
	KickOutMic(ctx context.Context, in *KickOutMicReq, opts ...grpc.CallOption) (*KickOutMicResp, error)
	// 设置自动锁麦开关
	SetAutoDisableMicSwitch(ctx context.Context, in *SetAutoDisableMicSwitchReq, opts ...grpc.CallOption) (*SetAutoDisableMicSwitchResp, error)
}

type channelMicMiddleClient struct {
	cc *grpc.ClientConn
}

func NewChannelMicMiddleClient(cc *grpc.ClientConn) ChannelMicMiddleClient {
	return &channelMicMiddleClient{cc}
}

func (c *channelMicMiddleClient) HoldMic(ctx context.Context, in *HoldMicReq, opts ...grpc.CallOption) (*HoldMicResp, error) {
	out := new(HoldMicResp)
	err := c.cc.Invoke(ctx, "/channel_mic_middle.ChannelMicMiddle/HoldMic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMicMiddleClient) TakeHoldMic(ctx context.Context, in *TakeHoldMicReq, opts ...grpc.CallOption) (*TakeHoldMicResp, error) {
	out := new(TakeHoldMicResp)
	err := c.cc.Invoke(ctx, "/channel_mic_middle.ChannelMicMiddle/TakeHoldMic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMicMiddleClient) ChangeMic(ctx context.Context, in *ChangeMicReq, opts ...grpc.CallOption) (*ChangeMicResp, error) {
	out := new(ChangeMicResp)
	err := c.cc.Invoke(ctx, "/channel_mic_middle.ChannelMicMiddle/ChangeMic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMicMiddleClient) ReleaseMic(ctx context.Context, in *ReleaseMicReq, opts ...grpc.CallOption) (*ReleaseMicResp, error) {
	out := new(ReleaseMicResp)
	err := c.cc.Invoke(ctx, "/channel_mic_middle.ChannelMicMiddle/ReleaseMic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMicMiddleClient) SetMicStatus(ctx context.Context, in *SetMicStatusReq, opts ...grpc.CallOption) (*SetMicStatusResp, error) {
	out := new(SetMicStatusResp)
	err := c.cc.Invoke(ctx, "/channel_mic_middle.ChannelMicMiddle/SetMicStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMicMiddleClient) KickOutMic(ctx context.Context, in *KickOutMicReq, opts ...grpc.CallOption) (*KickOutMicResp, error) {
	out := new(KickOutMicResp)
	err := c.cc.Invoke(ctx, "/channel_mic_middle.ChannelMicMiddle/KickOutMic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMicMiddleClient) SetAutoDisableMicSwitch(ctx context.Context, in *SetAutoDisableMicSwitchReq, opts ...grpc.CallOption) (*SetAutoDisableMicSwitchResp, error) {
	out := new(SetAutoDisableMicSwitchResp)
	err := c.cc.Invoke(ctx, "/channel_mic_middle.ChannelMicMiddle/SetAutoDisableMicSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelMicMiddleServer is the server API for ChannelMicMiddle service.
type ChannelMicMiddleServer interface {
	// 上麦
	HoldMic(context.Context, *HoldMicReq) (*HoldMicResp, error)
	// 抱上麦
	TakeHoldMic(context.Context, *TakeHoldMicReq) (*TakeHoldMicResp, error)
	// 换麦
	ChangeMic(context.Context, *ChangeMicReq) (*ChangeMicResp, error)
	// 下麦
	ReleaseMic(context.Context, *ReleaseMicReq) (*ReleaseMicResp, error)
	// 设置麦位状态
	SetMicStatus(context.Context, *SetMicStatusReq) (*SetMicStatusResp, error)
	// 踢下麦
	KickOutMic(context.Context, *KickOutMicReq) (*KickOutMicResp, error)
	// 设置自动锁麦开关
	SetAutoDisableMicSwitch(context.Context, *SetAutoDisableMicSwitchReq) (*SetAutoDisableMicSwitchResp, error)
}

func RegisterChannelMicMiddleServer(s *grpc.Server, srv ChannelMicMiddleServer) {
	s.RegisterService(&_ChannelMicMiddle_serviceDesc, srv)
}

func _ChannelMicMiddle_HoldMic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HoldMicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMicMiddleServer).HoldMic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_mic_middle.ChannelMicMiddle/HoldMic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMicMiddleServer).HoldMic(ctx, req.(*HoldMicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMicMiddle_TakeHoldMic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TakeHoldMicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMicMiddleServer).TakeHoldMic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_mic_middle.ChannelMicMiddle/TakeHoldMic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMicMiddleServer).TakeHoldMic(ctx, req.(*TakeHoldMicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMicMiddle_ChangeMic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeMicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMicMiddleServer).ChangeMic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_mic_middle.ChannelMicMiddle/ChangeMic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMicMiddleServer).ChangeMic(ctx, req.(*ChangeMicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMicMiddle_ReleaseMic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReleaseMicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMicMiddleServer).ReleaseMic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_mic_middle.ChannelMicMiddle/ReleaseMic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMicMiddleServer).ReleaseMic(ctx, req.(*ReleaseMicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMicMiddle_SetMicStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetMicStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMicMiddleServer).SetMicStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_mic_middle.ChannelMicMiddle/SetMicStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMicMiddleServer).SetMicStatus(ctx, req.(*SetMicStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMicMiddle_KickOutMic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(KickOutMicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMicMiddleServer).KickOutMic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_mic_middle.ChannelMicMiddle/KickOutMic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMicMiddleServer).KickOutMic(ctx, req.(*KickOutMicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMicMiddle_SetAutoDisableMicSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetAutoDisableMicSwitchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMicMiddleServer).SetAutoDisableMicSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_mic_middle.ChannelMicMiddle/SetAutoDisableMicSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMicMiddleServer).SetAutoDisableMicSwitch(ctx, req.(*SetAutoDisableMicSwitchReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelMicMiddle_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_mic_middle.ChannelMicMiddle",
	HandlerType: (*ChannelMicMiddleServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "HoldMic",
			Handler:    _ChannelMicMiddle_HoldMic_Handler,
		},
		{
			MethodName: "TakeHoldMic",
			Handler:    _ChannelMicMiddle_TakeHoldMic_Handler,
		},
		{
			MethodName: "ChangeMic",
			Handler:    _ChannelMicMiddle_ChangeMic_Handler,
		},
		{
			MethodName: "ReleaseMic",
			Handler:    _ChannelMicMiddle_ReleaseMic_Handler,
		},
		{
			MethodName: "SetMicStatus",
			Handler:    _ChannelMicMiddle_SetMicStatus_Handler,
		},
		{
			MethodName: "KickOutMic",
			Handler:    _ChannelMicMiddle_KickOutMic_Handler,
		},
		{
			MethodName: "SetAutoDisableMicSwitch",
			Handler:    _ChannelMicMiddle_SetAutoDisableMicSwitch_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "channel-mic-middle/channel-mic-middle.proto",
}

func init() {
	proto.RegisterFile("channel-mic-middle/channel-mic-middle.proto", fileDescriptor_channel_mic_middle_494d55eb4a4a4a49)
}

var fileDescriptor_channel_mic_middle_494d55eb4a4a4a49 = []byte{
	// 1015 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x57, 0xcd, 0x8e, 0xe3, 0x44,
	0x10, 0x5e, 0xe7, 0xdf, 0x95, 0x5f, 0x7a, 0x59, 0x36, 0x9b, 0xd1, 0xec, 0xce, 0x9a, 0x15, 0x5a,
	0x81, 0x26, 0x91, 0x86, 0x9f, 0x33, 0xec, 0x8c, 0xd0, 0x8e, 0x76, 0xad, 0x45, 0xce, 0x8c, 0x84,
	0xe0, 0x10, 0xf5, 0xb4, 0x7b, 0x26, 0xad, 0xd8, 0x6e, 0xe3, 0xee, 0x2c, 0x3f, 0xe2, 0xc0, 0x1b,
	0xf0, 0x02, 0xbc, 0x03, 0xdc, 0x78, 0x09, 0x1e, 0x80, 0x03, 0x07, 0x78, 0x04, 0xc4, 0x8d, 0x13,
	0xea, 0xb6, 0xe3, 0xd8, 0x13, 0x67, 0x7e, 0xc2, 0xc0, 0x61, 0x4f, 0x89, 0xab, 0xbb, 0xbe, 0xaa,
	0xfe, 0xbe, 0xaa, 0x2e, 0x1b, 0xde, 0x21, 0x53, 0x1c, 0x04, 0xd4, 0xdb, 0xf5, 0x19, 0xd9, 0xf5,
	0x99, 0xeb, 0x7a, 0x74, 0xb4, 0x6a, 0x1a, 0x86, 0x11, 0x97, 0x1c, 0xa1, 0x64, 0x65, 0xe2, 0x33,
	0x32, 0x89, 0x57, 0x06, 0x0f, 0xe8, 0x57, 0x92, 0x06, 0x82, 0xf1, 0x60, 0xc4, 0x43, 0xc9, 0x78,
	0x20, 0x16, 0xbf, 0xb1, 0x93, 0x15, 0x41, 0xcb, 0x66, 0x64, 0x1c, 0x62, 0x42, 0x0f, 0x83, 0x53,
	0x8e, 0xee, 0x40, 0x4d, 0xb9, 0x33, 0xb7, 0x6f, 0xec, 0x18, 0x8f, 0xdb, 0x4e, 0xd5, 0x67, 0xe4,
	0xd0, 0x45, 0x5b, 0x60, 0x2a, 0xb3, 0x90, 0x58, 0xd2, 0x7e, 0x49, 0xaf, 0x34, 0x7c, 0x46, 0xc6,
	0xea, 0x19, 0xdd, 0x85, 0xba, 0x5a, 0x9c, 0x33, 0xb7, 0x5f, 0xd6, 0x4b, 0x0a, 0xe2, 0x98, 0xb9,
	0x0b, 0x30, 0x29, 0xfa, 0x95, 0x14, 0xec, 0x48, 0x58, 0xbf, 0x1a, 0x00, 0x4f, 0xb9, 0xe7, 0xda,
	0x8c, 0x38, 0xf4, 0x0b, 0xf4, 0x06, 0xd4, 0x04, 0x9f, 0x47, 0x84, 0xea, 0x90, 0xa6, 0x93, 0x3c,
	0xa1, 0x1e, 0x94, 0x15, 0x64, 0x1c, 0x4d, 0xfd, 0x55, 0x16, 0x92, 0x06, 0x51, 0x7f, 0x33, 0xe9,
	0x56, 0xb2, 0xe9, 0xee, 0x43, 0x33, 0xa4, 0x91, 0xcf, 0x84, 0x3a, 0xb9, 0xe8, 0x57, 0x77, 0xca,
	0x8f, 0x3b, 0x7b, 0x0f, 0x87, 0xab, 0x04, 0x0d, 0x6d, 0x46, 0x3e, 0x49, 0x77, 0x3a, 0x59, 0x2f,
	0x85, 0x8d, 0xc3, 0x50, 0x61, 0xd7, 0x62, 0x6c, 0x1c, 0x86, 0x09, 0x15, 0x38, 0x9a, 0x51, 0xa9,
	0x56, 0xea, 0x09, 0x15, 0xda, 0x70, 0xe8, 0x5a, 0xbf, 0x19, 0xd0, 0x4c, 0x8f, 0x26, 0x42, 0x74,
	0x00, 0xed, 0x29, 0xf7, 0x5c, 0x1d, 0x91, 0x05, 0xa7, 0x5c, 0x1f, 0xb1, 0xb9, 0xb7, 0xb3, 0x26,
	0x95, 0x54, 0x07, 0xa7, 0x39, 0x8d, 0x51, 0xb4, 0x28, 0xdb, 0x00, 0x33, 0x46, 0x66, 0xd4, 0x9d,
	0x2c, 0x09, 0x31, 0x63, 0x8b, 0xa2, 0xf9, 0x09, 0xb4, 0xb0, 0x17, 0x43, 0x79, 0x4c, 0xc8, 0x7e,
	0x79, 0xa7, 0x7c, 0xa5, 0x18, 0x80, 0x3d, 0xcf, 0x66, 0xe4, 0x39, 0x13, 0x12, 0x3d, 0x82, 0x8e,
	0xa0, 0xd1, 0x4b, 0x1a, 0x4d, 0x24, 0xf3, 0xe9, 0xc4, 0x8f, 0x25, 0xab, 0x38, 0xad, 0xd8, 0x7a,
	0xc4, 0x7c, 0x6a, 0x0b, 0xeb, 0xbb, 0x12, 0x74, 0x8e, 0xf0, 0x8c, 0x5e, 0x41, 0xbd, 0x3b, 0x50,
	0xe3, 0x61, 0x26, 0xdf, 0x2a, 0x0f, 0x55, 0xae, 0xdb, 0x00, 0x12, 0x47, 0x67, 0x54, 0x66, 0xca,
	0xc5, 0x8c, 0x2d, 0xc7, 0x4b, 0x85, 0x2b, 0x45, 0x0a, 0x57, 0x2f, 0x50, 0xb8, 0xf6, 0x2f, 0x15,
	0xae, 0xaf, 0x55, 0xb8, 0x71, 0x4e, 0xe1, 0xdf, 0x0d, 0xe8, 0xe6, 0x28, 0x78, 0xf5, 0x54, 0xc6,
	0xd0, 0xda, 0x9f, 0xe2, 0xe0, 0x8c, 0xfe, 0x67, 0x0d, 0x6a, 0xfd, 0x50, 0x82, 0x76, 0x26, 0x46,
	0xcc, 0xe1, 0x69, 0xc4, 0xfd, 0x0d, 0x38, 0x54, 0x6e, 0x0b, 0x0e, 0x3f, 0x84, 0xa6, 0xe4, 0x4b,
	0x8c, 0xd2, 0x15, 0x31, 0x4c, 0xc9, 0x17, 0x08, 0xab, 0x14, 0x95, 0x57, 0x29, 0x5a, 0x11, 0xa3,
	0xb2, 0x81, 0x18, 0xf7, 0xa0, 0xa1, 0xb7, 0x71, 0x97, 0x26, 0xb5, 0xad, 0xae, 0x51, 0x9b, 0xbb,
	0xd4, 0x7a, 0x06, 0x6d, 0x87, 0x7a, 0x14, 0x8b, 0x1b, 0x90, 0xc0, 0xfa, 0xd3, 0x80, 0x4e, 0x16,
	0x4d, 0x84, 0xe8, 0x63, 0xe8, 0x10, 0x8f, 0x0b, 0x7a, 0x7d, 0xb6, 0x5b, 0xda, 0x6f, 0x41, 0xd6,
	0x79, 0x1a, 0x4a, 0x37, 0x52, 0x93, 0x45, 0x84, 0xef, 0xc2, 0x6d, 0x26, 0x26, 0x78, 0x2e, 0xf9,
	0xc4, 0x65, 0x02, 0x9f, 0x78, 0x3a, 0x77, 0x5d, 0x54, 0x0d, 0xa7, 0xc7, 0xc4, 0x47, 0x73, 0xc9,
	0x0f, 0xe2, 0x05, 0x9b, 0x11, 0xeb, 0x7b, 0x03, 0xba, 0x63, 0x2a, 0xed, 0x78, 0x44, 0xcd, 0xc5,
	0x06, 0x37, 0xd5, 0x6a, 0x2d, 0xdf, 0x87, 0x66, 0x5c, 0xcb, 0x4b, 0xcd, 0xdb, 0x8e, 0xa9, 0x0b,
	0x5a, 0x9f, 0x24, 0x37, 0x24, 0xab, 0xf9, 0x21, 0x69, 0xfd, 0x61, 0x40, 0x2f, 0x9f, 0x91, 0x08,
	0x57, 0xf8, 0x33, 0x36, 0xe0, 0xef, 0x29, 0x74, 0x93, 0x6b, 0xe3, 0xda, 0x32, 0xb4, 0x63, 0xc7,
	0xeb, 0x29, 0x91, 0x2d, 0xdb, 0x4a, 0xbe, 0x6c, 0x7f, 0x34, 0xa0, 0xfd, 0x8c, 0x91, 0xd9, 0x8b,
	0xb9, 0xdc, 0x6c, 0x3a, 0xac, 0x72, 0xfe, 0x16, 0x74, 0x97, 0xf3, 0x22, 0xcb, 0x7b, 0x3b, 0x1d,
	0x1a, 0x3a, 0xf7, 0x6d, 0x80, 0x13, 0x1c, 0x4c, 0x04, 0x25, 0x3c, 0x58, 0x8c, 0x0a, 0xf3, 0x04,
	0x07, 0x63, 0x6d, 0x50, 0x79, 0x48, 0x8e, 0x85, 0x14, 0x7a, 0x96, 0x9b, 0x4e, 0xf2, 0x64, 0xfd,
	0x65, 0x40, 0x27, 0x9b, 0xb1, 0x08, 0x55, 0xa5, 0x65, 0x2a, 0x2c, 0x55, 0xdb, 0xd0, 0x51, 0x7b,
	0x6e, 0x5a, 0x63, 0x89, 0xe8, 0x37, 0x47, 0xff, 0xff, 0x77, 0xc1, 0x7f, 0x03, 0x83, 0x31, 0x95,
	0xf9, 0x96, 0x19, 0x7f, 0xc9, 0x24, 0x99, 0xde, 0x88, 0x66, 0x5b, 0x60, 0x0a, 0x8d, 0x36, 0xe1,
	0x41, 0xd2, 0xa1, 0x8d, 0xd8, 0xf0, 0x22, 0xb0, 0xb6, 0x61, 0x6b, 0x6d, 0x6c, 0x11, 0xbe, 0x7d,
	0x00, 0xed, 0xdc, 0xc0, 0x46, 0x4d, 0xa8, 0x1f, 0x06, 0x2f, 0xb1, 0xc7, 0xdc, 0xde, 0x2d, 0x74,
	0x1b, 0xba, 0x36, 0x77, 0xd9, 0xe9, 0xd7, 0x69, 0x1b, 0xf5, 0x0c, 0xf4, 0x5a, 0x52, 0x74, 0x81,
	0xcd, 0xc8, 0xb1, 0xa0, 0x51, 0xaf, 0xb4, 0xf7, 0x53, 0x15, 0x7a, 0xfb, 0x31, 0x6d, 0x36, 0x23,
	0xb6, 0x26, 0x0d, 0x3d, 0x87, 0x7a, 0x32, 0xb4, 0xd1, 0xfd, 0x22, 0x52, 0x97, 0x2f, 0x35, 0x83,
	0x07, 0x17, 0xae, 0x8b, 0xd0, 0xba, 0x85, 0x3e, 0x85, 0x66, 0xe6, 0x35, 0x00, 0x59, 0x45, 0x1e,
	0xf9, 0x57, 0xa5, 0xc1, 0x9b, 0x97, 0xee, 0xd1, 0xc8, 0x0e, 0x98, 0xe9, 0x68, 0x44, 0x85, 0xf2,
	0x67, 0xa7, 0xf3, 0xe0, 0xe1, 0x25, 0x3b, 0x34, 0xe6, 0x31, 0xc0, 0x72, 0x04, 0xa0, 0x42, 0x97,
	0xdc, 0xc0, 0x19, 0x58, 0x97, 0x6d, 0xd1, 0xb0, 0x9f, 0x43, 0x2b, 0x7b, 0xa7, 0xa1, 0xc2, 0x13,
	0x9e, 0xbb, 0x87, 0x07, 0x8f, 0x2e, 0xdf, 0xa4, 0xc1, 0xc7, 0x00, 0xcb, 0xd6, 0x2c, 0xce, 0x39,
	0x77, 0xd9, 0x14, 0xe7, 0x7c, 0xae, 0xbb, 0xbf, 0x85, 0xbb, 0x6b, 0xca, 0x0f, 0x0d, 0xd7, 0xe4,
	0xb5, 0xa6, 0x4f, 0x06, 0xa3, 0x6b, 0xed, 0x57, 0x47, 0x1a, 0xdc, 0xfb, 0xfb, 0xe7, 0x5f, 0x8e,
	0x5e, 0x07, 0xb4, 0xfa, 0x0d, 0xf7, 0xe4, 0x83, 0xcf, 0xde, 0x3b, 0xe3, 0x1e, 0x0e, 0xce, 0x86,
	0xef, 0xef, 0x49, 0x39, 0x24, 0xdc, 0x1f, 0xe9, 0x2f, 0x34, 0xc2, 0xbd, 0x91, 0x6a, 0x5e, 0x46,
	0xa8, 0x28, 0xf8, 0xf6, 0x3b, 0xa9, 0xe9, 0x5d, 0xef, 0xfe, 0x13, 0x00, 0x00, 0xff, 0xff, 0x03,
	0x8f, 0xe6, 0x98, 0x2b, 0x0e, 0x00, 0x00,
}
