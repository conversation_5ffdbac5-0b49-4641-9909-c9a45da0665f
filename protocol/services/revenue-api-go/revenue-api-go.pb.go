// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/revenue-api-go/revenue-api-go.proto

package revenue_api_go // import "golang.52tt.com/protocol/services/revenue-api-go"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/extension/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type RevenueSceneType int32

const (
	RevenueSceneType_RevenueSceneTypeNone                 RevenueSceneType = 0
	RevenueSceneType_RevenueSceneTypeEnterChannel         RevenueSceneType = 1
	RevenueSceneType_RevenueSceneTypeOnMic                RevenueSceneType = 2
	RevenueSceneType_RevenueSceneTypeOffMic               RevenueSceneType = 3
	RevenueSceneType_RevenueSceneTypeKickMic              RevenueSceneType = 4
	RevenueSceneType_RevenueSceneTypePullMic              RevenueSceneType = 5
	RevenueSceneType_RevenueSceneTypeNormalChannelMessage RevenueSceneType = 6
	RevenueSceneType_RevenueSceneTypeUKWExpose            RevenueSceneType = 7
)

var RevenueSceneType_name = map[int32]string{
	0: "RevenueSceneTypeNone",
	1: "RevenueSceneTypeEnterChannel",
	2: "RevenueSceneTypeOnMic",
	3: "RevenueSceneTypeOffMic",
	4: "RevenueSceneTypeKickMic",
	5: "RevenueSceneTypePullMic",
	6: "RevenueSceneTypeNormalChannelMessage",
	7: "RevenueSceneTypeUKWExpose",
}
var RevenueSceneType_value = map[string]int32{
	"RevenueSceneTypeNone":                 0,
	"RevenueSceneTypeEnterChannel":         1,
	"RevenueSceneTypeOnMic":                2,
	"RevenueSceneTypeOffMic":               3,
	"RevenueSceneTypeKickMic":              4,
	"RevenueSceneTypePullMic":              5,
	"RevenueSceneTypeNormalChannelMessage": 6,
	"RevenueSceneTypeUKWExpose":            7,
}

func (x RevenueSceneType) String() string {
	return proto.EnumName(RevenueSceneType_name, int32(x))
}
func (RevenueSceneType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_revenue_api_go_16cfb9db57041c54, []int{0}
}

type RevenueAwardType int32

const (
	RevenueAwardType_RevenueAwardTypeNone            RevenueAwardType = 0
	RevenueAwardType_RevenueAwardTypePackage         RevenueAwardType = 1
	RevenueAwardType_RevenueAwardTypeOfficialCert    RevenueAwardType = 2
	RevenueAwardType_RevenueAwardTypeNameplate       RevenueAwardType = 3
	RevenueAwardType_RevenueAwardTypeHeadWear        RevenueAwardType = 4
	RevenueAwardType_RevenueAwardTypeHorse           RevenueAwardType = 5
	RevenueAwardType_RevenueAwardTypeFloat           RevenueAwardType = 6
	RevenueAwardType_RevenueAwardTypeChannelInfoCard RevenueAwardType = 7
)

var RevenueAwardType_name = map[int32]string{
	0: "RevenueAwardTypeNone",
	1: "RevenueAwardTypePackage",
	2: "RevenueAwardTypeOfficialCert",
	3: "RevenueAwardTypeNameplate",
	4: "RevenueAwardTypeHeadWear",
	5: "RevenueAwardTypeHorse",
	6: "RevenueAwardTypeFloat",
	7: "RevenueAwardTypeChannelInfoCard",
}
var RevenueAwardType_value = map[string]int32{
	"RevenueAwardTypeNone":            0,
	"RevenueAwardTypePackage":         1,
	"RevenueAwardTypeOfficialCert":    2,
	"RevenueAwardTypeNameplate":       3,
	"RevenueAwardTypeHeadWear":        4,
	"RevenueAwardTypeHorse":           5,
	"RevenueAwardTypeFloat":           6,
	"RevenueAwardTypeChannelInfoCard": 7,
}

func (x RevenueAwardType) String() string {
	return proto.EnumName(RevenueAwardType_name, int32(x))
}
func (RevenueAwardType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_revenue_api_go_16cfb9db57041c54, []int{1}
}

type GetRevenueMicInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Scene                uint32   `protobuf:"varint,3,opt,name=scene,proto3" json:"scene,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRevenueMicInfoReq) Reset()         { *m = GetRevenueMicInfoReq{} }
func (m *GetRevenueMicInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetRevenueMicInfoReq) ProtoMessage()    {}
func (*GetRevenueMicInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_api_go_16cfb9db57041c54, []int{0}
}
func (m *GetRevenueMicInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRevenueMicInfoReq.Unmarshal(m, b)
}
func (m *GetRevenueMicInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRevenueMicInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetRevenueMicInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRevenueMicInfoReq.Merge(dst, src)
}
func (m *GetRevenueMicInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetRevenueMicInfoReq.Size(m)
}
func (m *GetRevenueMicInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRevenueMicInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRevenueMicInfoReq proto.InternalMessageInfo

func (m *GetRevenueMicInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRevenueMicInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetRevenueMicInfoReq) GetScene() uint32 {
	if m != nil {
		return m.Scene
	}
	return 0
}

type GetRevenueMicInfoResp struct {
	RevenueInfo          []byte   `protobuf:"bytes,1,opt,name=revenue_info,json=revenueInfo,proto3" json:"revenue_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRevenueMicInfoResp) Reset()         { *m = GetRevenueMicInfoResp{} }
func (m *GetRevenueMicInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetRevenueMicInfoResp) ProtoMessage()    {}
func (*GetRevenueMicInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_api_go_16cfb9db57041c54, []int{1}
}
func (m *GetRevenueMicInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRevenueMicInfoResp.Unmarshal(m, b)
}
func (m *GetRevenueMicInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRevenueMicInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetRevenueMicInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRevenueMicInfoResp.Merge(dst, src)
}
func (m *GetRevenueMicInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetRevenueMicInfoResp.Size(m)
}
func (m *GetRevenueMicInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRevenueMicInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRevenueMicInfoResp proto.InternalMessageInfo

func (m *GetRevenueMicInfoResp) GetRevenueInfo() []byte {
	if m != nil {
		return m.RevenueInfo
	}
	return nil
}

type BatchGetRevenueMicInfoReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Scene                uint32   `protobuf:"varint,3,opt,name=scene,proto3" json:"scene,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetRevenueMicInfoReq) Reset()         { *m = BatchGetRevenueMicInfoReq{} }
func (m *BatchGetRevenueMicInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetRevenueMicInfoReq) ProtoMessage()    {}
func (*BatchGetRevenueMicInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_api_go_16cfb9db57041c54, []int{2}
}
func (m *BatchGetRevenueMicInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetRevenueMicInfoReq.Unmarshal(m, b)
}
func (m *BatchGetRevenueMicInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetRevenueMicInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetRevenueMicInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetRevenueMicInfoReq.Merge(dst, src)
}
func (m *BatchGetRevenueMicInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetRevenueMicInfoReq.Size(m)
}
func (m *BatchGetRevenueMicInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetRevenueMicInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetRevenueMicInfoReq proto.InternalMessageInfo

func (m *BatchGetRevenueMicInfoReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatchGetRevenueMicInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BatchGetRevenueMicInfoReq) GetScene() uint32 {
	if m != nil {
		return m.Scene
	}
	return 0
}

type BatchGetRevenueMicInfoResp struct {
	RevenueInfo          [][]byte `protobuf:"bytes,1,rep,name=revenue_info,json=revenueInfo,proto3" json:"revenue_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetRevenueMicInfoResp) Reset()         { *m = BatchGetRevenueMicInfoResp{} }
func (m *BatchGetRevenueMicInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetRevenueMicInfoResp) ProtoMessage()    {}
func (*BatchGetRevenueMicInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_api_go_16cfb9db57041c54, []int{3}
}
func (m *BatchGetRevenueMicInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetRevenueMicInfoResp.Unmarshal(m, b)
}
func (m *BatchGetRevenueMicInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetRevenueMicInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetRevenueMicInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetRevenueMicInfoResp.Merge(dst, src)
}
func (m *BatchGetRevenueMicInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetRevenueMicInfoResp.Size(m)
}
func (m *BatchGetRevenueMicInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetRevenueMicInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetRevenueMicInfoResp proto.InternalMessageInfo

func (m *BatchGetRevenueMicInfoResp) GetRevenueInfo() [][]byte {
	if m != nil {
		return m.RevenueInfo
	}
	return nil
}

type GetRevenueEnterChannelInfoReq struct {
	Uid                  uint32           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32           `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Scene                RevenueSceneType `protobuf:"varint,3,opt,name=scene,proto3,enum=revenue_api_go.RevenueSceneType" json:"scene,omitempty"`
	ChannelType          uint32           `protobuf:"varint,4,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	FollowedUid          uint32           `protobuf:"varint,5,opt,name=followed_uid,json=followedUid,proto3" json:"followed_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetRevenueEnterChannelInfoReq) Reset()         { *m = GetRevenueEnterChannelInfoReq{} }
func (m *GetRevenueEnterChannelInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetRevenueEnterChannelInfoReq) ProtoMessage()    {}
func (*GetRevenueEnterChannelInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_api_go_16cfb9db57041c54, []int{4}
}
func (m *GetRevenueEnterChannelInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRevenueEnterChannelInfoReq.Unmarshal(m, b)
}
func (m *GetRevenueEnterChannelInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRevenueEnterChannelInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetRevenueEnterChannelInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRevenueEnterChannelInfoReq.Merge(dst, src)
}
func (m *GetRevenueEnterChannelInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetRevenueEnterChannelInfoReq.Size(m)
}
func (m *GetRevenueEnterChannelInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRevenueEnterChannelInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRevenueEnterChannelInfoReq proto.InternalMessageInfo

func (m *GetRevenueEnterChannelInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRevenueEnterChannelInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetRevenueEnterChannelInfoReq) GetScene() RevenueSceneType {
	if m != nil {
		return m.Scene
	}
	return RevenueSceneType_RevenueSceneTypeNone
}

func (m *GetRevenueEnterChannelInfoReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *GetRevenueEnterChannelInfoReq) GetFollowedUid() uint32 {
	if m != nil {
		return m.FollowedUid
	}
	return 0
}

type GetRevenueEnterChannelInfoResp struct {
	RevenueInfo          []byte   `protobuf:"bytes,1,opt,name=revenue_info,json=revenueInfo,proto3" json:"revenue_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRevenueEnterChannelInfoResp) Reset()         { *m = GetRevenueEnterChannelInfoResp{} }
func (m *GetRevenueEnterChannelInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetRevenueEnterChannelInfoResp) ProtoMessage()    {}
func (*GetRevenueEnterChannelInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_api_go_16cfb9db57041c54, []int{5}
}
func (m *GetRevenueEnterChannelInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRevenueEnterChannelInfoResp.Unmarshal(m, b)
}
func (m *GetRevenueEnterChannelInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRevenueEnterChannelInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetRevenueEnterChannelInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRevenueEnterChannelInfoResp.Merge(dst, src)
}
func (m *GetRevenueEnterChannelInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetRevenueEnterChannelInfoResp.Size(m)
}
func (m *GetRevenueEnterChannelInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRevenueEnterChannelInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRevenueEnterChannelInfoResp proto.InternalMessageInfo

func (m *GetRevenueEnterChannelInfoResp) GetRevenueInfo() []byte {
	if m != nil {
		return m.RevenueInfo
	}
	return nil
}

// 互踩互赞
type GetRevenueUserVisitorRecordReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRevenueUserVisitorRecordReq) Reset()         { *m = GetRevenueUserVisitorRecordReq{} }
func (m *GetRevenueUserVisitorRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetRevenueUserVisitorRecordReq) ProtoMessage()    {}
func (*GetRevenueUserVisitorRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_api_go_16cfb9db57041c54, []int{6}
}
func (m *GetRevenueUserVisitorRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRevenueUserVisitorRecordReq.Unmarshal(m, b)
}
func (m *GetRevenueUserVisitorRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRevenueUserVisitorRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetRevenueUserVisitorRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRevenueUserVisitorRecordReq.Merge(dst, src)
}
func (m *GetRevenueUserVisitorRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetRevenueUserVisitorRecordReq.Size(m)
}
func (m *GetRevenueUserVisitorRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRevenueUserVisitorRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRevenueUserVisitorRecordReq proto.InternalMessageInfo

func (m *GetRevenueUserVisitorRecordReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type GetRevenueUserVisitorRecordResp struct {
	RevenueInfoMap       map[uint32][]byte `protobuf:"bytes,1,rep,name=revenue_info_map,json=revenueInfoMap,proto3" json:"revenue_info_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetRevenueUserVisitorRecordResp) Reset()         { *m = GetRevenueUserVisitorRecordResp{} }
func (m *GetRevenueUserVisitorRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetRevenueUserVisitorRecordResp) ProtoMessage()    {}
func (*GetRevenueUserVisitorRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_api_go_16cfb9db57041c54, []int{7}
}
func (m *GetRevenueUserVisitorRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRevenueUserVisitorRecordResp.Unmarshal(m, b)
}
func (m *GetRevenueUserVisitorRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRevenueUserVisitorRecordResp.Marshal(b, m, deterministic)
}
func (dst *GetRevenueUserVisitorRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRevenueUserVisitorRecordResp.Merge(dst, src)
}
func (m *GetRevenueUserVisitorRecordResp) XXX_Size() int {
	return xxx_messageInfo_GetRevenueUserVisitorRecordResp.Size(m)
}
func (m *GetRevenueUserVisitorRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRevenueUserVisitorRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRevenueUserVisitorRecordResp proto.InternalMessageInfo

func (m *GetRevenueUserVisitorRecordResp) GetRevenueInfoMap() map[uint32][]byte {
	if m != nil {
		return m.RevenueInfoMap
	}
	return nil
}

// RevenueAwardInfo 奖励信息
type RevenueAwardInfo struct {
	AwardId              string           `protobuf:"bytes,1,opt,name=award_id,json=awardId,proto3" json:"award_id,omitempty"`
	AwardName            string           `protobuf:"bytes,2,opt,name=award_name,json=awardName,proto3" json:"award_name,omitempty"`
	AwardType            RevenueAwardType `protobuf:"varint,3,opt,name=award_type,json=awardType,proto3,enum=revenue_api_go.RevenueAwardType" json:"award_type,omitempty"`
	DetailInfo           []byte           `protobuf:"bytes,4,opt,name=detail_info,json=detailInfo,proto3" json:"detail_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *RevenueAwardInfo) Reset()         { *m = RevenueAwardInfo{} }
func (m *RevenueAwardInfo) String() string { return proto.CompactTextString(m) }
func (*RevenueAwardInfo) ProtoMessage()    {}
func (*RevenueAwardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_api_go_16cfb9db57041c54, []int{8}
}
func (m *RevenueAwardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RevenueAwardInfo.Unmarshal(m, b)
}
func (m *RevenueAwardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RevenueAwardInfo.Marshal(b, m, deterministic)
}
func (dst *RevenueAwardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RevenueAwardInfo.Merge(dst, src)
}
func (m *RevenueAwardInfo) XXX_Size() int {
	return xxx_messageInfo_RevenueAwardInfo.Size(m)
}
func (m *RevenueAwardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RevenueAwardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RevenueAwardInfo proto.InternalMessageInfo

func (m *RevenueAwardInfo) GetAwardId() string {
	if m != nil {
		return m.AwardId
	}
	return ""
}

func (m *RevenueAwardInfo) GetAwardName() string {
	if m != nil {
		return m.AwardName
	}
	return ""
}

func (m *RevenueAwardInfo) GetAwardType() RevenueAwardType {
	if m != nil {
		return m.AwardType
	}
	return RevenueAwardType_RevenueAwardTypeNone
}

func (m *RevenueAwardInfo) GetDetailInfo() []byte {
	if m != nil {
		return m.DetailInfo
	}
	return nil
}

type GetRevenueAwardInfosByTypeReq struct {
	AwardType            RevenueAwardType `protobuf:"varint,1,opt,name=award_type,json=awardType,proto3,enum=revenue_api_go.RevenueAwardType" json:"award_type,omitempty"`
	AwardId              string           `protobuf:"bytes,2,opt,name=award_id,json=awardId,proto3" json:"award_id,omitempty"`
	AwardName            string           `protobuf:"bytes,3,opt,name=award_name,json=awardName,proto3" json:"award_name,omitempty"`
	BlurredSearch        bool             `protobuf:"varint,4,opt,name=blurred_search,json=blurredSearch,proto3" json:"blurred_search,omitempty"`
	WithDetailInfo       bool             `protobuf:"varint,5,opt,name=with_detail_info,json=withDetailInfo,proto3" json:"with_detail_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetRevenueAwardInfosByTypeReq) Reset()         { *m = GetRevenueAwardInfosByTypeReq{} }
func (m *GetRevenueAwardInfosByTypeReq) String() string { return proto.CompactTextString(m) }
func (*GetRevenueAwardInfosByTypeReq) ProtoMessage()    {}
func (*GetRevenueAwardInfosByTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_api_go_16cfb9db57041c54, []int{9}
}
func (m *GetRevenueAwardInfosByTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRevenueAwardInfosByTypeReq.Unmarshal(m, b)
}
func (m *GetRevenueAwardInfosByTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRevenueAwardInfosByTypeReq.Marshal(b, m, deterministic)
}
func (dst *GetRevenueAwardInfosByTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRevenueAwardInfosByTypeReq.Merge(dst, src)
}
func (m *GetRevenueAwardInfosByTypeReq) XXX_Size() int {
	return xxx_messageInfo_GetRevenueAwardInfosByTypeReq.Size(m)
}
func (m *GetRevenueAwardInfosByTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRevenueAwardInfosByTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRevenueAwardInfosByTypeReq proto.InternalMessageInfo

func (m *GetRevenueAwardInfosByTypeReq) GetAwardType() RevenueAwardType {
	if m != nil {
		return m.AwardType
	}
	return RevenueAwardType_RevenueAwardTypeNone
}

func (m *GetRevenueAwardInfosByTypeReq) GetAwardId() string {
	if m != nil {
		return m.AwardId
	}
	return ""
}

func (m *GetRevenueAwardInfosByTypeReq) GetAwardName() string {
	if m != nil {
		return m.AwardName
	}
	return ""
}

func (m *GetRevenueAwardInfosByTypeReq) GetBlurredSearch() bool {
	if m != nil {
		return m.BlurredSearch
	}
	return false
}

func (m *GetRevenueAwardInfosByTypeReq) GetWithDetailInfo() bool {
	if m != nil {
		return m.WithDetailInfo
	}
	return false
}

type GetRevenueAwardInfosByTypeResp struct {
	AwardInfos           []*RevenueAwardInfo `protobuf:"bytes,1,rep,name=award_infos,json=awardInfos,proto3" json:"award_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetRevenueAwardInfosByTypeResp) Reset()         { *m = GetRevenueAwardInfosByTypeResp{} }
func (m *GetRevenueAwardInfosByTypeResp) String() string { return proto.CompactTextString(m) }
func (*GetRevenueAwardInfosByTypeResp) ProtoMessage()    {}
func (*GetRevenueAwardInfosByTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_api_go_16cfb9db57041c54, []int{10}
}
func (m *GetRevenueAwardInfosByTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRevenueAwardInfosByTypeResp.Unmarshal(m, b)
}
func (m *GetRevenueAwardInfosByTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRevenueAwardInfosByTypeResp.Marshal(b, m, deterministic)
}
func (dst *GetRevenueAwardInfosByTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRevenueAwardInfosByTypeResp.Merge(dst, src)
}
func (m *GetRevenueAwardInfosByTypeResp) XXX_Size() int {
	return xxx_messageInfo_GetRevenueAwardInfosByTypeResp.Size(m)
}
func (m *GetRevenueAwardInfosByTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRevenueAwardInfosByTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRevenueAwardInfosByTypeResp proto.InternalMessageInfo

func (m *GetRevenueAwardInfosByTypeResp) GetAwardInfos() []*RevenueAwardInfo {
	if m != nil {
		return m.AwardInfos
	}
	return nil
}

type SearchAwardInfo struct {
	AwardType            RevenueAwardType `protobuf:"varint,1,opt,name=award_type,json=awardType,proto3,enum=revenue_api_go.RevenueAwardType" json:"award_type,omitempty"`
	AwardId              string           `protobuf:"bytes,2,opt,name=award_id,json=awardId,proto3" json:"award_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *SearchAwardInfo) Reset()         { *m = SearchAwardInfo{} }
func (m *SearchAwardInfo) String() string { return proto.CompactTextString(m) }
func (*SearchAwardInfo) ProtoMessage()    {}
func (*SearchAwardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_api_go_16cfb9db57041c54, []int{11}
}
func (m *SearchAwardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchAwardInfo.Unmarshal(m, b)
}
func (m *SearchAwardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchAwardInfo.Marshal(b, m, deterministic)
}
func (dst *SearchAwardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchAwardInfo.Merge(dst, src)
}
func (m *SearchAwardInfo) XXX_Size() int {
	return xxx_messageInfo_SearchAwardInfo.Size(m)
}
func (m *SearchAwardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchAwardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SearchAwardInfo proto.InternalMessageInfo

func (m *SearchAwardInfo) GetAwardType() RevenueAwardType {
	if m != nil {
		return m.AwardType
	}
	return RevenueAwardType_RevenueAwardTypeNone
}

func (m *SearchAwardInfo) GetAwardId() string {
	if m != nil {
		return m.AwardId
	}
	return ""
}

type BatchGetRevenueAwardInfosByIdsReq struct {
	AwardTypeList        []*SearchAwardInfo `protobuf:"bytes,1,rep,name=award_type_list,json=awardTypeList,proto3" json:"award_type_list,omitempty"`
	NoDetail             bool               `protobuf:"varint,2,opt,name=no_detail,json=noDetail,proto3" json:"no_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *BatchGetRevenueAwardInfosByIdsReq) Reset()         { *m = BatchGetRevenueAwardInfosByIdsReq{} }
func (m *BatchGetRevenueAwardInfosByIdsReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetRevenueAwardInfosByIdsReq) ProtoMessage()    {}
func (*BatchGetRevenueAwardInfosByIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_api_go_16cfb9db57041c54, []int{12}
}
func (m *BatchGetRevenueAwardInfosByIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetRevenueAwardInfosByIdsReq.Unmarshal(m, b)
}
func (m *BatchGetRevenueAwardInfosByIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetRevenueAwardInfosByIdsReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetRevenueAwardInfosByIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetRevenueAwardInfosByIdsReq.Merge(dst, src)
}
func (m *BatchGetRevenueAwardInfosByIdsReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetRevenueAwardInfosByIdsReq.Size(m)
}
func (m *BatchGetRevenueAwardInfosByIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetRevenueAwardInfosByIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetRevenueAwardInfosByIdsReq proto.InternalMessageInfo

func (m *BatchGetRevenueAwardInfosByIdsReq) GetAwardTypeList() []*SearchAwardInfo {
	if m != nil {
		return m.AwardTypeList
	}
	return nil
}

func (m *BatchGetRevenueAwardInfosByIdsReq) GetNoDetail() bool {
	if m != nil {
		return m.NoDetail
	}
	return false
}

type BatchGetRevenueAwardInfosByIdsResp struct {
	AwardInfos           []*RevenueAwardInfo `protobuf:"bytes,1,rep,name=award_infos,json=awardInfos,proto3" json:"award_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *BatchGetRevenueAwardInfosByIdsResp) Reset()         { *m = BatchGetRevenueAwardInfosByIdsResp{} }
func (m *BatchGetRevenueAwardInfosByIdsResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetRevenueAwardInfosByIdsResp) ProtoMessage()    {}
func (*BatchGetRevenueAwardInfosByIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_api_go_16cfb9db57041c54, []int{13}
}
func (m *BatchGetRevenueAwardInfosByIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetRevenueAwardInfosByIdsResp.Unmarshal(m, b)
}
func (m *BatchGetRevenueAwardInfosByIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetRevenueAwardInfosByIdsResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetRevenueAwardInfosByIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetRevenueAwardInfosByIdsResp.Merge(dst, src)
}
func (m *BatchGetRevenueAwardInfosByIdsResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetRevenueAwardInfosByIdsResp.Size(m)
}
func (m *BatchGetRevenueAwardInfosByIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetRevenueAwardInfosByIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetRevenueAwardInfosByIdsResp proto.InternalMessageInfo

func (m *BatchGetRevenueAwardInfosByIdsResp) GetAwardInfos() []*RevenueAwardInfo {
	if m != nil {
		return m.AwardInfos
	}
	return nil
}

// 检查是否可以修改性别
type CheckCanModifySexReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckCanModifySexReq) Reset()         { *m = CheckCanModifySexReq{} }
func (m *CheckCanModifySexReq) String() string { return proto.CompactTextString(m) }
func (*CheckCanModifySexReq) ProtoMessage()    {}
func (*CheckCanModifySexReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_api_go_16cfb9db57041c54, []int{14}
}
func (m *CheckCanModifySexReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckCanModifySexReq.Unmarshal(m, b)
}
func (m *CheckCanModifySexReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckCanModifySexReq.Marshal(b, m, deterministic)
}
func (dst *CheckCanModifySexReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckCanModifySexReq.Merge(dst, src)
}
func (m *CheckCanModifySexReq) XXX_Size() int {
	return xxx_messageInfo_CheckCanModifySexReq.Size(m)
}
func (m *CheckCanModifySexReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckCanModifySexReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckCanModifySexReq proto.InternalMessageInfo

func (m *CheckCanModifySexReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CheckCanModifySexResp struct {
	ErrCode              int32    `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	ErrMsg               string   `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckCanModifySexResp) Reset()         { *m = CheckCanModifySexResp{} }
func (m *CheckCanModifySexResp) String() string { return proto.CompactTextString(m) }
func (*CheckCanModifySexResp) ProtoMessage()    {}
func (*CheckCanModifySexResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_api_go_16cfb9db57041c54, []int{15}
}
func (m *CheckCanModifySexResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckCanModifySexResp.Unmarshal(m, b)
}
func (m *CheckCanModifySexResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckCanModifySexResp.Marshal(b, m, deterministic)
}
func (dst *CheckCanModifySexResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckCanModifySexResp.Merge(dst, src)
}
func (m *CheckCanModifySexResp) XXX_Size() int {
	return xxx_messageInfo_CheckCanModifySexResp.Size(m)
}
func (m *CheckCanModifySexResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckCanModifySexResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckCanModifySexResp proto.InternalMessageInfo

func (m *CheckCanModifySexResp) GetErrCode() int32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *CheckCanModifySexResp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

func init() {
	proto.RegisterType((*GetRevenueMicInfoReq)(nil), "revenue_api_go.GetRevenueMicInfoReq")
	proto.RegisterType((*GetRevenueMicInfoResp)(nil), "revenue_api_go.GetRevenueMicInfoResp")
	proto.RegisterType((*BatchGetRevenueMicInfoReq)(nil), "revenue_api_go.BatchGetRevenueMicInfoReq")
	proto.RegisterType((*BatchGetRevenueMicInfoResp)(nil), "revenue_api_go.BatchGetRevenueMicInfoResp")
	proto.RegisterType((*GetRevenueEnterChannelInfoReq)(nil), "revenue_api_go.GetRevenueEnterChannelInfoReq")
	proto.RegisterType((*GetRevenueEnterChannelInfoResp)(nil), "revenue_api_go.GetRevenueEnterChannelInfoResp")
	proto.RegisterType((*GetRevenueUserVisitorRecordReq)(nil), "revenue_api_go.GetRevenueUserVisitorRecordReq")
	proto.RegisterType((*GetRevenueUserVisitorRecordResp)(nil), "revenue_api_go.GetRevenueUserVisitorRecordResp")
	proto.RegisterMapType((map[uint32][]byte)(nil), "revenue_api_go.GetRevenueUserVisitorRecordResp.RevenueInfoMapEntry")
	proto.RegisterType((*RevenueAwardInfo)(nil), "revenue_api_go.RevenueAwardInfo")
	proto.RegisterType((*GetRevenueAwardInfosByTypeReq)(nil), "revenue_api_go.GetRevenueAwardInfosByTypeReq")
	proto.RegisterType((*GetRevenueAwardInfosByTypeResp)(nil), "revenue_api_go.GetRevenueAwardInfosByTypeResp")
	proto.RegisterType((*SearchAwardInfo)(nil), "revenue_api_go.SearchAwardInfo")
	proto.RegisterType((*BatchGetRevenueAwardInfosByIdsReq)(nil), "revenue_api_go.BatchGetRevenueAwardInfosByIdsReq")
	proto.RegisterType((*BatchGetRevenueAwardInfosByIdsResp)(nil), "revenue_api_go.BatchGetRevenueAwardInfosByIdsResp")
	proto.RegisterType((*CheckCanModifySexReq)(nil), "revenue_api_go.CheckCanModifySexReq")
	proto.RegisterType((*CheckCanModifySexResp)(nil), "revenue_api_go.CheckCanModifySexResp")
	proto.RegisterEnum("revenue_api_go.RevenueSceneType", RevenueSceneType_name, RevenueSceneType_value)
	proto.RegisterEnum("revenue_api_go.RevenueAwardType", RevenueAwardType_name, RevenueAwardType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RevenueApiGoClient is the client API for RevenueApiGo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RevenueApiGoClient interface {
	// 获取麦位相关的营收扩展信息
	GetRevenueMicInfo(ctx context.Context, in *GetRevenueMicInfoReq, opts ...grpc.CallOption) (*GetRevenueMicInfoResp, error)
	// 批量获取麦位相关的营收扩展信息
	BatchGetRevenueMicInfo(ctx context.Context, in *BatchGetRevenueMicInfoReq, opts ...grpc.CallOption) (*BatchGetRevenueMicInfoResp, error)
	// 获取进房相关的营收扩展信息
	GetRevenueEnterChannelInfo(ctx context.Context, in *GetRevenueEnterChannelInfoReq, opts ...grpc.CallOption) (*GetRevenueEnterChannelInfoResp, error)
	// 互踩互赞相关的营收扩展信息
	GetRevenueUserVisitorRecord(ctx context.Context, in *GetRevenueUserVisitorRecordReq, opts ...grpc.CallOption) (*GetRevenueUserVisitorRecordResp, error)
	// 根据奖励类型查询营收奖励信息
	GetRevenueAwardInfosByType(ctx context.Context, in *GetRevenueAwardInfosByTypeReq, opts ...grpc.CallOption) (*GetRevenueAwardInfosByTypeResp, error)
	// 根据奖励信息类型批量查询营收奖励信息
	BatchGetRevenueAwardInfosByIds(ctx context.Context, in *BatchGetRevenueAwardInfosByIdsReq, opts ...grpc.CallOption) (*BatchGetRevenueAwardInfosByIdsResp, error)
	// 检查是否可以修改性别
	CheckCanModifySex(ctx context.Context, in *CheckCanModifySexReq, opts ...grpc.CallOption) (*CheckCanModifySexResp, error)
}

type revenueApiGoClient struct {
	cc *grpc.ClientConn
}

func NewRevenueApiGoClient(cc *grpc.ClientConn) RevenueApiGoClient {
	return &revenueApiGoClient{cc}
}

func (c *revenueApiGoClient) GetRevenueMicInfo(ctx context.Context, in *GetRevenueMicInfoReq, opts ...grpc.CallOption) (*GetRevenueMicInfoResp, error) {
	out := new(GetRevenueMicInfoResp)
	err := c.cc.Invoke(ctx, "/revenue_api_go.RevenueApiGo/GetRevenueMicInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueApiGoClient) BatchGetRevenueMicInfo(ctx context.Context, in *BatchGetRevenueMicInfoReq, opts ...grpc.CallOption) (*BatchGetRevenueMicInfoResp, error) {
	out := new(BatchGetRevenueMicInfoResp)
	err := c.cc.Invoke(ctx, "/revenue_api_go.RevenueApiGo/BatchGetRevenueMicInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueApiGoClient) GetRevenueEnterChannelInfo(ctx context.Context, in *GetRevenueEnterChannelInfoReq, opts ...grpc.CallOption) (*GetRevenueEnterChannelInfoResp, error) {
	out := new(GetRevenueEnterChannelInfoResp)
	err := c.cc.Invoke(ctx, "/revenue_api_go.RevenueApiGo/GetRevenueEnterChannelInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueApiGoClient) GetRevenueUserVisitorRecord(ctx context.Context, in *GetRevenueUserVisitorRecordReq, opts ...grpc.CallOption) (*GetRevenueUserVisitorRecordResp, error) {
	out := new(GetRevenueUserVisitorRecordResp)
	err := c.cc.Invoke(ctx, "/revenue_api_go.RevenueApiGo/GetRevenueUserVisitorRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueApiGoClient) GetRevenueAwardInfosByType(ctx context.Context, in *GetRevenueAwardInfosByTypeReq, opts ...grpc.CallOption) (*GetRevenueAwardInfosByTypeResp, error) {
	out := new(GetRevenueAwardInfosByTypeResp)
	err := c.cc.Invoke(ctx, "/revenue_api_go.RevenueApiGo/GetRevenueAwardInfosByType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueApiGoClient) BatchGetRevenueAwardInfosByIds(ctx context.Context, in *BatchGetRevenueAwardInfosByIdsReq, opts ...grpc.CallOption) (*BatchGetRevenueAwardInfosByIdsResp, error) {
	out := new(BatchGetRevenueAwardInfosByIdsResp)
	err := c.cc.Invoke(ctx, "/revenue_api_go.RevenueApiGo/BatchGetRevenueAwardInfosByIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueApiGoClient) CheckCanModifySex(ctx context.Context, in *CheckCanModifySexReq, opts ...grpc.CallOption) (*CheckCanModifySexResp, error) {
	out := new(CheckCanModifySexResp)
	err := c.cc.Invoke(ctx, "/revenue_api_go.RevenueApiGo/CheckCanModifySex", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RevenueApiGoServer is the server API for RevenueApiGo service.
type RevenueApiGoServer interface {
	// 获取麦位相关的营收扩展信息
	GetRevenueMicInfo(context.Context, *GetRevenueMicInfoReq) (*GetRevenueMicInfoResp, error)
	// 批量获取麦位相关的营收扩展信息
	BatchGetRevenueMicInfo(context.Context, *BatchGetRevenueMicInfoReq) (*BatchGetRevenueMicInfoResp, error)
	// 获取进房相关的营收扩展信息
	GetRevenueEnterChannelInfo(context.Context, *GetRevenueEnterChannelInfoReq) (*GetRevenueEnterChannelInfoResp, error)
	// 互踩互赞相关的营收扩展信息
	GetRevenueUserVisitorRecord(context.Context, *GetRevenueUserVisitorRecordReq) (*GetRevenueUserVisitorRecordResp, error)
	// 根据奖励类型查询营收奖励信息
	GetRevenueAwardInfosByType(context.Context, *GetRevenueAwardInfosByTypeReq) (*GetRevenueAwardInfosByTypeResp, error)
	// 根据奖励信息类型批量查询营收奖励信息
	BatchGetRevenueAwardInfosByIds(context.Context, *BatchGetRevenueAwardInfosByIdsReq) (*BatchGetRevenueAwardInfosByIdsResp, error)
	// 检查是否可以修改性别
	CheckCanModifySex(context.Context, *CheckCanModifySexReq) (*CheckCanModifySexResp, error)
}

func RegisterRevenueApiGoServer(s *grpc.Server, srv RevenueApiGoServer) {
	s.RegisterService(&_RevenueApiGo_serviceDesc, srv)
}

func _RevenueApiGo_GetRevenueMicInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRevenueMicInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueApiGoServer).GetRevenueMicInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_api_go.RevenueApiGo/GetRevenueMicInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueApiGoServer).GetRevenueMicInfo(ctx, req.(*GetRevenueMicInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueApiGo_BatchGetRevenueMicInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetRevenueMicInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueApiGoServer).BatchGetRevenueMicInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_api_go.RevenueApiGo/BatchGetRevenueMicInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueApiGoServer).BatchGetRevenueMicInfo(ctx, req.(*BatchGetRevenueMicInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueApiGo_GetRevenueEnterChannelInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRevenueEnterChannelInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueApiGoServer).GetRevenueEnterChannelInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_api_go.RevenueApiGo/GetRevenueEnterChannelInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueApiGoServer).GetRevenueEnterChannelInfo(ctx, req.(*GetRevenueEnterChannelInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueApiGo_GetRevenueUserVisitorRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRevenueUserVisitorRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueApiGoServer).GetRevenueUserVisitorRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_api_go.RevenueApiGo/GetRevenueUserVisitorRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueApiGoServer).GetRevenueUserVisitorRecord(ctx, req.(*GetRevenueUserVisitorRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueApiGo_GetRevenueAwardInfosByType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRevenueAwardInfosByTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueApiGoServer).GetRevenueAwardInfosByType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_api_go.RevenueApiGo/GetRevenueAwardInfosByType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueApiGoServer).GetRevenueAwardInfosByType(ctx, req.(*GetRevenueAwardInfosByTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueApiGo_BatchGetRevenueAwardInfosByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetRevenueAwardInfosByIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueApiGoServer).BatchGetRevenueAwardInfosByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_api_go.RevenueApiGo/BatchGetRevenueAwardInfosByIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueApiGoServer).BatchGetRevenueAwardInfosByIds(ctx, req.(*BatchGetRevenueAwardInfosByIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueApiGo_CheckCanModifySex_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckCanModifySexReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueApiGoServer).CheckCanModifySex(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_api_go.RevenueApiGo/CheckCanModifySex",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueApiGoServer).CheckCanModifySex(ctx, req.(*CheckCanModifySexReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _RevenueApiGo_serviceDesc = grpc.ServiceDesc{
	ServiceName: "revenue_api_go.RevenueApiGo",
	HandlerType: (*RevenueApiGoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetRevenueMicInfo",
			Handler:    _RevenueApiGo_GetRevenueMicInfo_Handler,
		},
		{
			MethodName: "BatchGetRevenueMicInfo",
			Handler:    _RevenueApiGo_BatchGetRevenueMicInfo_Handler,
		},
		{
			MethodName: "GetRevenueEnterChannelInfo",
			Handler:    _RevenueApiGo_GetRevenueEnterChannelInfo_Handler,
		},
		{
			MethodName: "GetRevenueUserVisitorRecord",
			Handler:    _RevenueApiGo_GetRevenueUserVisitorRecord_Handler,
		},
		{
			MethodName: "GetRevenueAwardInfosByType",
			Handler:    _RevenueApiGo_GetRevenueAwardInfosByType_Handler,
		},
		{
			MethodName: "BatchGetRevenueAwardInfosByIds",
			Handler:    _RevenueApiGo_BatchGetRevenueAwardInfosByIds_Handler,
		},
		{
			MethodName: "CheckCanModifySex",
			Handler:    _RevenueApiGo_CheckCanModifySex_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/revenue-api-go/revenue-api-go.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/revenue-api-go/revenue-api-go.proto", fileDescriptor_revenue_api_go_16cfb9db57041c54)
}

var fileDescriptor_revenue_api_go_16cfb9db57041c54 = []byte{
	// 1104 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x57, 0x5f, 0x4f, 0x1b, 0x47,
	0x10, 0xe7, 0x6c, 0xcc, 0x9f, 0x31, 0x90, 0xcb, 0x96, 0x24, 0xc6, 0x09, 0x81, 0x5c, 0x89, 0x44,
	0x91, 0xb0, 0x5b, 0x57, 0xad, 0xaa, 0xf4, 0x21, 0x02, 0x97, 0x52, 0x44, 0x9d, 0x44, 0x47, 0x69,
	0xa4, 0x4a, 0x95, 0xb5, 0xdc, 0x8d, 0xcd, 0xca, 0xe7, 0xdb, 0x63, 0xf7, 0x0c, 0xf8, 0xa1, 0x52,
	0xa5, 0xbe, 0x34, 0x5f, 0xa6, 0x1f, 0xa5, 0x0f, 0xed, 0xc7, 0xe8, 0x4b, 0x5f, 0xfa, 0x5e, 0xed,
	0xde, 0xd9, 0x3e, 0xce, 0x87, 0x81, 0xa8, 0x7d, 0xb2, 0x77, 0x7e, 0x73, 0xb3, 0xbf, 0xf9, 0xcd,
	0xdc, 0xec, 0x1e, 0xd4, 0xc2, 0xb0, 0x7a, 0xd6, 0x63, 0x4e, 0x47, 0x32, 0xef, 0x1c, 0x45, 0x55,
	0xe0, 0x39, 0xfa, 0x3d, 0xdc, 0xa6, 0x01, 0xdb, 0x6e, 0xf3, 0xd4, 0xb2, 0x12, 0x08, 0x1e, 0x72,
	0xb2, 0x14, 0x5b, 0x9b, 0x34, 0x60, 0xcd, 0x36, 0x2f, 0x57, 0x52, 0x31, 0xf0, 0x32, 0x44, 0x5f,
	0x32, 0xee, 0x57, 0x79, 0x10, 0x32, 0xee, 0xcb, 0xc1, 0x6f, 0xf4, 0xbc, 0xf5, 0x23, 0x2c, 0xef,
	0x63, 0x68, 0x47, 0x41, 0x1a, 0xcc, 0x39, 0xf0, 0x5b, 0xdc, 0xc6, 0x33, 0x62, 0x42, 0xbe, 0xc7,
	0xdc, 0x92, 0xb1, 0x6e, 0x6c, 0x2e, 0xda, 0xea, 0x2f, 0x59, 0x05, 0x70, 0x4e, 0xa9, 0xef, 0xa3,
	0xd7, 0x64, 0x6e, 0x29, 0xa7, 0x81, 0xf9, 0xd8, 0x72, 0xe0, 0x92, 0x65, 0x28, 0x48, 0x07, 0x7d,
	0x2c, 0xe5, 0x35, 0x12, 0x2d, 0xac, 0x17, 0xf0, 0x20, 0x23, 0xbc, 0x0c, 0xc8, 0x33, 0x58, 0x18,
	0x30, 0x67, 0x7e, 0x8b, 0xeb, 0x8d, 0x16, 0xec, 0x62, 0x6c, 0x53, 0x6e, 0x56, 0x07, 0x56, 0x76,
	0x69, 0xe8, 0x9c, 0x66, 0xf2, 0x5b, 0x81, 0xb9, 0x1e, 0x73, 0x9b, 0x1e, 0x93, 0x61, 0xc9, 0x58,
	0xcf, 0x6f, 0x2e, 0xda, 0xb3, 0x3d, 0xe6, 0x7e, 0xcb, 0x64, 0xf8, 0x7e, 0x44, 0x5f, 0x42, 0xf9,
	0xba, 0xcd, 0x32, 0xd9, 0xe6, 0xd3, 0x6c, 0x7f, 0x37, 0x60, 0x75, 0xf4, 0xf0, 0x9e, 0x1f, 0xa2,
	0xa8, 0xc7, 0x7b, 0xbe, 0xaf, 0xa4, 0x9f, 0x27, 0x99, 0x2e, 0xd5, 0xd6, 0x2b, 0x57, 0x6b, 0x5d,
	0x89, 0xf7, 0x3a, 0x52, 0x3e, 0xdf, 0xf5, 0x03, 0x8c, 0x73, 0x51, 0x6c, 0x07, 0x61, 0xc3, 0x7e,
	0x80, 0xa5, 0x69, 0x1d, 0xb8, 0x18, 0xdb, 0x94, 0xa7, 0x72, 0x69, 0x71, 0xcf, 0xe3, 0x17, 0xe8,
	0x36, 0x15, 0xa9, 0x42, 0xe4, 0x32, 0xb0, 0x1d, 0x33, 0xd7, 0xaa, 0xc3, 0xd3, 0x49, 0xf9, 0xdc,
	0xae, 0x86, 0x5f, 0x26, 0x83, 0x1c, 0x4b, 0x14, 0xdf, 0x33, 0xc9, 0x42, 0x2e, 0x6c, 0x74, 0xb8,
	0x70, 0x27, 0x17, 0xd2, 0xfa, 0xd3, 0x80, 0xb5, 0x89, 0x4f, 0xcb, 0x80, 0x74, 0xc1, 0x4c, 0x72,
	0x68, 0x76, 0x69, 0xa0, 0xc3, 0x14, 0x6b, 0xf5, 0xb4, 0x5c, 0x37, 0x84, 0x1a, 0xc8, 0xa9, 0x78,
	0x37, 0x68, 0xb0, 0xe7, 0x87, 0xa2, 0x6f, 0x2f, 0x89, 0x2b, 0xc6, 0xf2, 0x0e, 0x7c, 0x90, 0xe1,
	0xa6, 0x4a, 0xdb, 0xc1, 0xfe, 0xa0, 0xb4, 0x1d, 0xec, 0xab, 0x2e, 0x3b, 0xa7, 0x5e, 0x0f, 0x75,
	0x55, 0x17, 0xec, 0x68, 0xf1, 0x22, 0xf7, 0x85, 0x61, 0xfd, 0x66, 0x80, 0x19, 0xc7, 0xd8, 0xb9,
	0xa0, 0xc2, 0x55, 0x81, 0x94, 0x0a, 0x54, 0x2d, 0x9a, 0x71, 0x83, 0xcc, 0xdb, 0xb3, 0x7a, 0x7d,
	0xa0, 0x9b, 0x24, 0x82, 0x7c, 0xda, 0x8d, 0xc2, 0xcd, 0xdb, 0xf3, 0xda, 0xf2, 0x8a, 0x76, 0x91,
	0xbc, 0x1c, 0xc0, 0xba, 0xd4, 0x93, 0x3b, 0x45, 0xef, 0xa7, 0x3b, 0x25, 0x0a, 0xa0, 0x5b, 0x61,
	0x0d, 0x8a, 0x2e, 0x86, 0x94, 0x79, 0x51, 0x11, 0xa7, 0x35, 0x5f, 0x88, 0x4c, 0xba, 0x86, 0x7f,
	0x5d, 0xe9, 0xec, 0x21, 0x67, 0xb9, 0xdb, 0xd7, 0xa1, 0xf0, 0x2c, 0xc5, 0xc1, 0xb8, 0x3b, 0x87,
	0x64, 0xfa, 0xb9, 0x49, 0xe9, 0xe7, 0xd3, 0xe9, 0x3f, 0x87, 0xa5, 0x13, 0xaf, 0x27, 0x04, 0xba,
	0x4d, 0x89, 0x54, 0x38, 0xa7, 0x3a, 0x81, 0x39, 0x7b, 0x31, 0xb6, 0x1e, 0x69, 0x23, 0xd9, 0x04,
	0xf3, 0x82, 0x85, 0xa7, 0xcd, 0x64, 0xa6, 0x05, 0xed, 0xb8, 0xa4, 0xec, 0x5f, 0x8d, 0xb2, 0x75,
	0x92, 0x1d, 0x3b, 0x9e, 0xac, 0x0c, 0xc8, 0x0e, 0x14, 0x63, 0xb2, 0x0a, 0x88, 0xbb, 0x6d, 0x62,
	0xba, 0xfa, 0x8d, 0x89, 0xd2, 0xd0, 0xc1, 0xac, 0x2e, 0xdc, 0x8b, 0x88, 0x8d, 0x3a, 0xe0, 0x7f,
	0xd4, 0xd0, 0x7a, 0x67, 0xc0, 0xb3, 0xd4, 0x74, 0x4b, 0x66, 0x76, 0xe0, 0x4a, 0x55, 0xc5, 0x7d,
	0xb8, 0x37, 0x62, 0x30, 0x7a, 0x21, 0x8b, 0xb5, 0xb5, 0x34, 0x8d, 0x14, 0x77, 0x7b, 0x71, 0xc8,
	0x42, 0x0f, 0xe0, 0xc7, 0x30, 0xef, 0xf3, 0x58, 0x6a, 0x4d, 0x65, 0xce, 0x9e, 0xf3, 0x79, 0xa4,
	0xb1, 0xd5, 0x06, 0xeb, 0x26, 0x2a, 0xff, 0x8d, 0xc6, 0x9b, 0xb0, 0x5c, 0x3f, 0x45, 0xa7, 0x53,
	0xa7, 0x7e, 0x83, 0xbb, 0xac, 0xd5, 0x3f, 0xc2, 0xcb, 0xcc, 0x31, 0x6c, 0x1d, 0xc2, 0x83, 0x0c,
	0x4f, 0x19, 0x28, 0x49, 0x51, 0x88, 0xa6, 0xc3, 0xdd, 0xa8, 0x22, 0x05, 0x7b, 0x16, 0x85, 0xa8,
	0x73, 0x17, 0xc9, 0x23, 0x50, 0x7f, 0x9b, 0x5d, 0xd9, 0x8e, 0xc5, 0x9e, 0x41, 0x21, 0x1a, 0xb2,
	0xbd, 0xf5, 0x6b, 0x6e, 0xf8, 0x7a, 0x0f, 0x07, 0x33, 0x29, 0xc1, 0x72, 0xda, 0xf6, 0x8a, 0xfb,
	0x68, 0x4e, 0x91, 0x75, 0x78, 0x92, 0x46, 0x92, 0xb3, 0xd6, 0x34, 0xc8, 0x0a, 0x3c, 0x48, 0x7b,
	0xbc, 0xf6, 0x1b, 0xcc, 0x31, 0x73, 0xa4, 0x0c, 0x0f, 0xc7, 0xa0, 0x56, 0x4b, 0x61, 0x79, 0xf2,
	0x18, 0x1e, 0xa5, 0xb1, 0x43, 0xe6, 0x74, 0x14, 0x38, 0x9d, 0x05, 0xbe, 0xe9, 0x79, 0x9e, 0x02,
	0x0b, 0x64, 0x13, 0x36, 0xc6, 0xc9, 0x8a, 0x2e, 0xf5, 0x62, 0x4e, 0x0d, 0x94, 0x92, 0xb6, 0xd1,
	0x9c, 0x21, 0xab, 0xb0, 0x92, 0xf6, 0x3c, 0x3e, 0x7c, 0xbb, 0x77, 0x19, 0x70, 0x89, 0xe6, 0xec,
	0xd6, 0x2f, 0xb9, 0xab, 0x93, 0x2e, 0x25, 0xc5, 0xd0, 0x16, 0x4b, 0x31, 0x22, 0x35, 0x44, 0xde,
	0x50, 0xa7, 0xa3, 0xb6, 0x32, 0x12, 0x3a, 0x0d, 0xc1, 0xd7, 0xad, 0x16, 0x73, 0x18, 0xf5, 0xea,
	0x28, 0x42, 0x33, 0x97, 0x20, 0x33, 0x0a, 0x4c, 0xbb, 0x18, 0x78, 0x34, 0x44, 0x33, 0x4f, 0x9e,
	0x40, 0x29, 0x0d, 0x7f, 0x83, 0xd4, 0x7d, 0x8b, 0x54, 0x98, 0xd3, 0x09, 0x91, 0x47, 0x28, 0x17,
	0x12, 0xcd, 0x42, 0x16, 0xf4, 0xb5, 0xc7, 0x69, 0x68, 0xce, 0x90, 0x0f, 0x61, 0x2d, 0x0d, 0x25,
	0xce, 0xc8, 0x3a, 0x15, 0xae, 0x39, 0x5b, 0xfb, 0x67, 0x06, 0x16, 0x06, 0x5e, 0x01, 0xdb, 0xe7,
	0xe4, 0x04, 0xee, 0x8f, 0xdd, 0x32, 0xc8, 0xc6, 0xf5, 0xa7, 0xd5, 0xe8, 0xd6, 0x53, 0x7e, 0x7e,
	0x0b, 0x2f, 0x19, 0x58, 0x53, 0xe4, 0x0c, 0x1e, 0x66, 0x5f, 0x67, 0xc8, 0x47, 0xe9, 0x10, 0xd7,
	0xde, 0xb1, 0xca, 0x5b, 0xb7, 0x75, 0xd5, 0x5b, 0xfe, 0x04, 0xe5, 0xeb, 0xef, 0x0b, 0x64, 0xfb,
	0x7a, 0xe6, 0x19, 0x77, 0xa5, 0x72, 0xe5, 0x2e, 0xee, 0x7a, 0xfb, 0x9f, 0x0d, 0x78, 0x3c, 0xe1,
	0x84, 0x27, 0x95, 0x3b, 0x5d, 0x07, 0xce, 0xca, 0xd5, 0x3b, 0x5e, 0x1f, 0xd2, 0x0a, 0xa4, 0x8f,
	0x8e, 0x49, 0x0a, 0x64, 0x9c, 0xa9, 0x93, 0x14, 0xc8, 0x3a, 0x95, 0xac, 0x29, 0xf2, 0xce, 0x80,
	0xa7, 0x93, 0x47, 0x2b, 0xf9, 0xe4, 0x86, 0x8a, 0x8e, 0x9f, 0x0a, 0xe5, 0xda, 0x5d, 0x1f, 0xd1,
	0x5c, 0x4e, 0xe0, 0xfe, 0xd8, 0x48, 0x1d, 0xef, 0xf1, 0xac, 0xf9, 0x3c, 0xde, 0xe3, 0x99, 0xb3,
	0xd9, 0x9a, 0x2a, 0xaf, 0xfe, 0xf1, 0xf7, 0xc6, 0x4a, 0xf2, 0xcd, 0xaa, 0x24, 0x17, 0xbb, 0xb5,
	0x1f, 0x3e, 0x6e, 0x73, 0x8f, 0xfa, 0xed, 0xca, 0x67, 0xb5, 0x30, 0xac, 0x38, 0xbc, 0x5b, 0xd5,
	0x9f, 0x3c, 0x0e, 0xf7, 0xaa, 0x12, 0xc5, 0x39, 0x73, 0x50, 0xa6, 0xbe, 0xa9, 0x4e, 0x66, 0xb4,
	0xc7, 0xa7, 0xff, 0x06, 0x00, 0x00, 0xff, 0xff, 0xe6, 0x69, 0x1e, 0xc7, 0x8a, 0x0d, 0x00, 0x00,
}
