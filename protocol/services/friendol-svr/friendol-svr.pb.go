// Code generated by protoc-gen-gogo.
// source: friendol-svr/friendol-svr.proto
// DO NOT EDIT!

/*
	Package friendol_svr is a generated protocol buffer package.

	It is generated from these files:
		friendol-svr/friendol-svr.proto

	It has these top-level messages:
		OnlineInfo
		FriendsDetail
		GetOnlineFriendsReq
		GetOnlineFriendsResp
		GetOfflineFriendsReq
		GetOfflineFriendsResp
		UpdateOnlineStatusReq
		UpdateUserPlayingGameReq
		UpdateUserRoomIdReq
		UpdateFollowChannelAuthReq
		GetFollowChannelAuthReq
		GetFollowChannelAuthResp
		BatGetFollowChannelAuthReq
		BatGetFollowChannelAuthResp
		GetFirendCacheCountReq
		GetFirendCacheCountResp
*/
package friendol_svr

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type OnlineStatus int32

const (
	OnlineStatus_OFFLINE OnlineStatus = 0
	OnlineStatus_ONLINE  OnlineStatus = 1
)

var OnlineStatus_name = map[int32]string{
	0: "OFFLINE",
	1: "ONLINE",
}
var OnlineStatus_value = map[string]int32{
	"OFFLINE": 0,
	"ONLINE":  1,
}

func (x OnlineStatus) Enum() *OnlineStatus {
	p := new(OnlineStatus)
	*p = x
	return p
}
func (x OnlineStatus) String() string {
	return proto.EnumName(OnlineStatus_name, int32(x))
}
func (x *OnlineStatus) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(OnlineStatus_value, data, "OnlineStatus")
	if err != nil {
		return err
	}
	*x = OnlineStatus(value)
	return nil
}
func (OnlineStatus) EnumDescriptor() ([]byte, []int) { return fileDescriptorFriendolSvr, []int{0} }

type OnlineInfo struct {
	RoomId   uint32 `protobuf:"varint,1,req,name=room_id,json=roomId" json:"room_id"`
	GameName string `protobuf:"bytes,2,req,name=game_name,json=gameName" json:"game_name"`
}

func (m *OnlineInfo) Reset()                    { *m = OnlineInfo{} }
func (m *OnlineInfo) String() string            { return proto.CompactTextString(m) }
func (*OnlineInfo) ProtoMessage()               {}
func (*OnlineInfo) Descriptor() ([]byte, []int) { return fileDescriptorFriendolSvr, []int{0} }

func (m *OnlineInfo) GetRoomId() uint32 {
	if m != nil {
		return m.RoomId
	}
	return 0
}

func (m *OnlineInfo) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

type FriendsDetail struct {
	Uid          uint32       `protobuf:"varint,1,req,name=uid" json:"uid"`
	OlStatus     OnlineStatus `protobuf:"varint,2,req,name=ol_status,json=olStatus,enum=friendol_svr.OnlineStatus" json:"ol_status"`
	LastOlTime   uint32       `protobuf:"varint,3,req,name=last_ol_time,json=lastOlTime" json:"last_ol_time"`
	RoomId       uint32       `protobuf:"varint,4,opt,name=room_id,json=roomId" json:"room_id"`
	GameName     string       `protobuf:"bytes,5,opt,name=game_name,json=gameName" json:"game_name"`
	ChannelType  uint32       `protobuf:"varint,6,opt,name=channel_type,json=channelType" json:"channel_type"`
	ChannelIsPwd bool         `protobuf:"varint,7,opt,name=channel_is_pwd,json=channelIsPwd" json:"channel_is_pwd"`
	Invisible    bool         `protobuf:"varint,8,opt,name=invisible" json:"invisible"`
}

func (m *FriendsDetail) Reset()                    { *m = FriendsDetail{} }
func (m *FriendsDetail) String() string            { return proto.CompactTextString(m) }
func (*FriendsDetail) ProtoMessage()               {}
func (*FriendsDetail) Descriptor() ([]byte, []int) { return fileDescriptorFriendolSvr, []int{1} }

func (m *FriendsDetail) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FriendsDetail) GetOlStatus() OnlineStatus {
	if m != nil {
		return m.OlStatus
	}
	return OnlineStatus_OFFLINE
}

func (m *FriendsDetail) GetLastOlTime() uint32 {
	if m != nil {
		return m.LastOlTime
	}
	return 0
}

func (m *FriendsDetail) GetRoomId() uint32 {
	if m != nil {
		return m.RoomId
	}
	return 0
}

func (m *FriendsDetail) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *FriendsDetail) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *FriendsDetail) GetChannelIsPwd() bool {
	if m != nil {
		return m.ChannelIsPwd
	}
	return false
}

func (m *FriendsDetail) GetInvisible() bool {
	if m != nil {
		return m.Invisible
	}
	return false
}

type GetOnlineFriendsReq struct {
	Uid                   uint32   `protobuf:"varint,1,req,name=uid" json:"uid"`
	ExtCheckOnlineUidlist []uint32 `protobuf:"varint,2,rep,name=ext_check_online_uidlist,json=extCheckOnlineUidlist" json:"ext_check_online_uidlist,omitempty"`
}

func (m *GetOnlineFriendsReq) Reset()                    { *m = GetOnlineFriendsReq{} }
func (m *GetOnlineFriendsReq) String() string            { return proto.CompactTextString(m) }
func (*GetOnlineFriendsReq) ProtoMessage()               {}
func (*GetOnlineFriendsReq) Descriptor() ([]byte, []int) { return fileDescriptorFriendolSvr, []int{2} }

func (m *GetOnlineFriendsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetOnlineFriendsReq) GetExtCheckOnlineUidlist() []uint32 {
	if m != nil {
		return m.ExtCheckOnlineUidlist
	}
	return nil
}

type GetOnlineFriendsResp struct {
	DetailList          []*FriendsDetail `protobuf:"bytes,1,rep,name=detail_list,json=detailList" json:"detail_list,omitempty"`
	ExtCheckOfflineList []*FriendsDetail `protobuf:"bytes,2,rep,name=ext_check_offline_list,json=extCheckOfflineList" json:"ext_check_offline_list,omitempty"`
}

func (m *GetOnlineFriendsResp) Reset()                    { *m = GetOnlineFriendsResp{} }
func (m *GetOnlineFriendsResp) String() string            { return proto.CompactTextString(m) }
func (*GetOnlineFriendsResp) ProtoMessage()               {}
func (*GetOnlineFriendsResp) Descriptor() ([]byte, []int) { return fileDescriptorFriendolSvr, []int{3} }

func (m *GetOnlineFriendsResp) GetDetailList() []*FriendsDetail {
	if m != nil {
		return m.DetailList
	}
	return nil
}

func (m *GetOnlineFriendsResp) GetExtCheckOfflineList() []*FriendsDetail {
	if m != nil {
		return m.ExtCheckOfflineList
	}
	return nil
}

type GetOfflineFriendsReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetOfflineFriendsReq) Reset()                    { *m = GetOfflineFriendsReq{} }
func (m *GetOfflineFriendsReq) String() string            { return proto.CompactTextString(m) }
func (*GetOfflineFriendsReq) ProtoMessage()               {}
func (*GetOfflineFriendsReq) Descriptor() ([]byte, []int) { return fileDescriptorFriendolSvr, []int{4} }

func (m *GetOfflineFriendsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetOfflineFriendsResp struct {
	DetailList []*FriendsDetail `protobuf:"bytes,1,rep,name=detail_list,json=detailList" json:"detail_list,omitempty"`
}

func (m *GetOfflineFriendsResp) Reset()                    { *m = GetOfflineFriendsResp{} }
func (m *GetOfflineFriendsResp) String() string            { return proto.CompactTextString(m) }
func (*GetOfflineFriendsResp) ProtoMessage()               {}
func (*GetOfflineFriendsResp) Descriptor() ([]byte, []int) { return fileDescriptorFriendolSvr, []int{5} }

func (m *GetOfflineFriendsResp) GetDetailList() []*FriendsDetail {
	if m != nil {
		return m.DetailList
	}
	return nil
}

type UpdateOnlineStatusReq struct {
	Uid      uint32       `protobuf:"varint,1,req,name=uid" json:"uid"`
	OlStatus OnlineStatus `protobuf:"varint,2,req,name=ol_status,json=olStatus,enum=friendol_svr.OnlineStatus" json:"ol_status"`
}

func (m *UpdateOnlineStatusReq) Reset()                    { *m = UpdateOnlineStatusReq{} }
func (m *UpdateOnlineStatusReq) String() string            { return proto.CompactTextString(m) }
func (*UpdateOnlineStatusReq) ProtoMessage()               {}
func (*UpdateOnlineStatusReq) Descriptor() ([]byte, []int) { return fileDescriptorFriendolSvr, []int{6} }

func (m *UpdateOnlineStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateOnlineStatusReq) GetOlStatus() OnlineStatus {
	if m != nil {
		return m.OlStatus
	}
	return OnlineStatus_OFFLINE
}

type UpdateUserPlayingGameReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	IsPlaying bool   `protobuf:"varint,2,req,name=is_playing,json=isPlaying" json:"is_playing"`
	GameName  string `protobuf:"bytes,3,req,name=game_name,json=gameName" json:"game_name"`
}

func (m *UpdateUserPlayingGameReq) Reset()         { *m = UpdateUserPlayingGameReq{} }
func (m *UpdateUserPlayingGameReq) String() string { return proto.CompactTextString(m) }
func (*UpdateUserPlayingGameReq) ProtoMessage()    {}
func (*UpdateUserPlayingGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptorFriendolSvr, []int{7}
}

func (m *UpdateUserPlayingGameReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateUserPlayingGameReq) GetIsPlaying() bool {
	if m != nil {
		return m.IsPlaying
	}
	return false
}

func (m *UpdateUserPlayingGameReq) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

type UpdateUserRoomIdReq struct {
	Uid         uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ChannelId   uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	IsEnter     bool   `protobuf:"varint,3,req,name=is_enter,json=isEnter" json:"is_enter"`
	ChannelType uint32 `protobuf:"varint,4,opt,name=channel_type,json=channelType" json:"channel_type"`
	IsPwd       bool   `protobuf:"varint,5,opt,name=is_pwd,json=isPwd" json:"is_pwd"`
}

func (m *UpdateUserRoomIdReq) Reset()                    { *m = UpdateUserRoomIdReq{} }
func (m *UpdateUserRoomIdReq) String() string            { return proto.CompactTextString(m) }
func (*UpdateUserRoomIdReq) ProtoMessage()               {}
func (*UpdateUserRoomIdReq) Descriptor() ([]byte, []int) { return fileDescriptorFriendolSvr, []int{8} }

func (m *UpdateUserRoomIdReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateUserRoomIdReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UpdateUserRoomIdReq) GetIsEnter() bool {
	if m != nil {
		return m.IsEnter
	}
	return false
}

func (m *UpdateUserRoomIdReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *UpdateUserRoomIdReq) GetIsPwd() bool {
	if m != nil {
		return m.IsPwd
	}
	return false
}

type UpdateFollowChannelAuthReq struct {
	Uid              uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	FollowAuth       bool   `protobuf:"varint,2,req,name=follow_auth,json=followAuth" json:"follow_auth"`
	FollowAuthSwitch uint32 `protobuf:"varint,3,opt,name=follow_auth_switch,json=followAuthSwitch" json:"follow_auth_switch"`
}

func (m *UpdateFollowChannelAuthReq) Reset()         { *m = UpdateFollowChannelAuthReq{} }
func (m *UpdateFollowChannelAuthReq) String() string { return proto.CompactTextString(m) }
func (*UpdateFollowChannelAuthReq) ProtoMessage()    {}
func (*UpdateFollowChannelAuthReq) Descriptor() ([]byte, []int) {
	return fileDescriptorFriendolSvr, []int{9}
}

func (m *UpdateFollowChannelAuthReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateFollowChannelAuthReq) GetFollowAuth() bool {
	if m != nil {
		return m.FollowAuth
	}
	return false
}

func (m *UpdateFollowChannelAuthReq) GetFollowAuthSwitch() uint32 {
	if m != nil {
		return m.FollowAuthSwitch
	}
	return 0
}

type GetFollowChannelAuthReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetFollowChannelAuthReq) Reset()         { *m = GetFollowChannelAuthReq{} }
func (m *GetFollowChannelAuthReq) String() string { return proto.CompactTextString(m) }
func (*GetFollowChannelAuthReq) ProtoMessage()    {}
func (*GetFollowChannelAuthReq) Descriptor() ([]byte, []int) {
	return fileDescriptorFriendolSvr, []int{10}
}

func (m *GetFollowChannelAuthReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetFollowChannelAuthResp struct {
	FollowAuth       bool   `protobuf:"varint,1,req,name=follow_auth,json=followAuth" json:"follow_auth"`
	FollowAuthSwitch uint32 `protobuf:"varint,2,opt,name=follow_auth_switch,json=followAuthSwitch" json:"follow_auth_switch"`
}

func (m *GetFollowChannelAuthResp) Reset()         { *m = GetFollowChannelAuthResp{} }
func (m *GetFollowChannelAuthResp) String() string { return proto.CompactTextString(m) }
func (*GetFollowChannelAuthResp) ProtoMessage()    {}
func (*GetFollowChannelAuthResp) Descriptor() ([]byte, []int) {
	return fileDescriptorFriendolSvr, []int{11}
}

func (m *GetFollowChannelAuthResp) GetFollowAuth() bool {
	if m != nil {
		return m.FollowAuth
	}
	return false
}

func (m *GetFollowChannelAuthResp) GetFollowAuthSwitch() uint32 {
	if m != nil {
		return m.FollowAuthSwitch
	}
	return 0
}

// 批量获取指定用户的跟随开关设置
type BatGetFollowChannelAuthReq struct {
	UidList []uint32 `protobuf:"varint,1,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *BatGetFollowChannelAuthReq) Reset()         { *m = BatGetFollowChannelAuthReq{} }
func (m *BatGetFollowChannelAuthReq) String() string { return proto.CompactTextString(m) }
func (*BatGetFollowChannelAuthReq) ProtoMessage()    {}
func (*BatGetFollowChannelAuthReq) Descriptor() ([]byte, []int) {
	return fileDescriptorFriendolSvr, []int{12}
}

func (m *BatGetFollowChannelAuthReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatGetFollowChannelAuthResp struct {
	FollowAuthList       []bool   `protobuf:"varint,1,rep,name=follow_auth_list,json=followAuthList" json:"follow_auth_list,omitempty"`
	FollowAuthSwitchList []uint32 `protobuf:"varint,2,rep,name=follow_auth_switch_list,json=followAuthSwitchList" json:"follow_auth_switch_list,omitempty"`
}

func (m *BatGetFollowChannelAuthResp) Reset()         { *m = BatGetFollowChannelAuthResp{} }
func (m *BatGetFollowChannelAuthResp) String() string { return proto.CompactTextString(m) }
func (*BatGetFollowChannelAuthResp) ProtoMessage()    {}
func (*BatGetFollowChannelAuthResp) Descriptor() ([]byte, []int) {
	return fileDescriptorFriendolSvr, []int{13}
}

func (m *BatGetFollowChannelAuthResp) GetFollowAuthList() []bool {
	if m != nil {
		return m.FollowAuthList
	}
	return nil
}

func (m *BatGetFollowChannelAuthResp) GetFollowAuthSwitchList() []uint32 {
	if m != nil {
		return m.FollowAuthSwitchList
	}
	return nil
}

// 获取好友数量
type GetFirendCacheCountReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetFirendCacheCountReq) Reset()         { *m = GetFirendCacheCountReq{} }
func (m *GetFirendCacheCountReq) String() string { return proto.CompactTextString(m) }
func (*GetFirendCacheCountReq) ProtoMessage()    {}
func (*GetFirendCacheCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptorFriendolSvr, []int{14}
}

func (m *GetFirendCacheCountReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetFirendCacheCountResp struct {
	Count uint32 `protobuf:"varint,1,req,name=count" json:"count"`
}

func (m *GetFirendCacheCountResp) Reset()         { *m = GetFirendCacheCountResp{} }
func (m *GetFirendCacheCountResp) String() string { return proto.CompactTextString(m) }
func (*GetFirendCacheCountResp) ProtoMessage()    {}
func (*GetFirendCacheCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptorFriendolSvr, []int{15}
}

func (m *GetFirendCacheCountResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func init() {
	proto.RegisterType((*OnlineInfo)(nil), "friendol_svr.OnlineInfo")
	proto.RegisterType((*FriendsDetail)(nil), "friendol_svr.FriendsDetail")
	proto.RegisterType((*GetOnlineFriendsReq)(nil), "friendol_svr.GetOnlineFriendsReq")
	proto.RegisterType((*GetOnlineFriendsResp)(nil), "friendol_svr.GetOnlineFriendsResp")
	proto.RegisterType((*GetOfflineFriendsReq)(nil), "friendol_svr.GetOfflineFriendsReq")
	proto.RegisterType((*GetOfflineFriendsResp)(nil), "friendol_svr.GetOfflineFriendsResp")
	proto.RegisterType((*UpdateOnlineStatusReq)(nil), "friendol_svr.UpdateOnlineStatusReq")
	proto.RegisterType((*UpdateUserPlayingGameReq)(nil), "friendol_svr.UpdateUserPlayingGameReq")
	proto.RegisterType((*UpdateUserRoomIdReq)(nil), "friendol_svr.UpdateUserRoomIdReq")
	proto.RegisterType((*UpdateFollowChannelAuthReq)(nil), "friendol_svr.UpdateFollowChannelAuthReq")
	proto.RegisterType((*GetFollowChannelAuthReq)(nil), "friendol_svr.GetFollowChannelAuthReq")
	proto.RegisterType((*GetFollowChannelAuthResp)(nil), "friendol_svr.GetFollowChannelAuthResp")
	proto.RegisterType((*BatGetFollowChannelAuthReq)(nil), "friendol_svr.BatGetFollowChannelAuthReq")
	proto.RegisterType((*BatGetFollowChannelAuthResp)(nil), "friendol_svr.BatGetFollowChannelAuthResp")
	proto.RegisterType((*GetFirendCacheCountReq)(nil), "friendol_svr.GetFirendCacheCountReq")
	proto.RegisterType((*GetFirendCacheCountResp)(nil), "friendol_svr.GetFirendCacheCountResp")
	proto.RegisterEnum("friendol_svr.OnlineStatus", OnlineStatus_name, OnlineStatus_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for FriendOLSvr service

type FriendOLSvrClient interface {
	GetOnlineFriends(ctx context.Context, in *GetOnlineFriendsReq, opts ...grpc.CallOption) (*GetOnlineFriendsResp, error)
	GetOfflineFriends(ctx context.Context, in *GetOfflineFriendsReq, opts ...grpc.CallOption) (*GetOfflineFriendsResp, error)
	UpdateOnlineStatus(ctx context.Context, in *UpdateOnlineStatusReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	UpdateUserPlayingGame(ctx context.Context, in *UpdateUserPlayingGameReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	UpdateUserRoomId(ctx context.Context, in *UpdateUserRoomIdReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 更新用户的跟随开关设置
	UpdateFollowChannelAuth(ctx context.Context, in *UpdateFollowChannelAuthReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 获取指定用户的跟随开关设置
	GetFollowChannelAuth(ctx context.Context, in *GetFollowChannelAuthReq, opts ...grpc.CallOption) (*GetFollowChannelAuthResp, error)
	// 批量获取指定用户的跟随开关设置
	BatGetFollowChannelAuth(ctx context.Context, in *BatGetFollowChannelAuthReq, opts ...grpc.CallOption) (*BatGetFollowChannelAuthResp, error)
	// 从cache中获取用户好友数量
	GetFirendCacheCount(ctx context.Context, in *GetFirendCacheCountReq, opts ...grpc.CallOption) (*GetFirendCacheCountResp, error)
}

type friendOLSvrClient struct {
	cc *grpc.ClientConn
}

func NewFriendOLSvrClient(cc *grpc.ClientConn) FriendOLSvrClient {
	return &friendOLSvrClient{cc}
}

func (c *friendOLSvrClient) GetOnlineFriends(ctx context.Context, in *GetOnlineFriendsReq, opts ...grpc.CallOption) (*GetOnlineFriendsResp, error) {
	out := new(GetOnlineFriendsResp)
	err := grpc.Invoke(ctx, "/friendol_svr.FriendOLSvr/GetOnlineFriends", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendOLSvrClient) GetOfflineFriends(ctx context.Context, in *GetOfflineFriendsReq, opts ...grpc.CallOption) (*GetOfflineFriendsResp, error) {
	out := new(GetOfflineFriendsResp)
	err := grpc.Invoke(ctx, "/friendol_svr.FriendOLSvr/GetOfflineFriends", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendOLSvrClient) UpdateOnlineStatus(ctx context.Context, in *UpdateOnlineStatusReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/friendol_svr.FriendOLSvr/UpdateOnlineStatus", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendOLSvrClient) UpdateUserPlayingGame(ctx context.Context, in *UpdateUserPlayingGameReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/friendol_svr.FriendOLSvr/UpdateUserPlayingGame", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendOLSvrClient) UpdateUserRoomId(ctx context.Context, in *UpdateUserRoomIdReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/friendol_svr.FriendOLSvr/UpdateUserRoomId", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendOLSvrClient) UpdateFollowChannelAuth(ctx context.Context, in *UpdateFollowChannelAuthReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/friendol_svr.FriendOLSvr/UpdateFollowChannelAuth", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendOLSvrClient) GetFollowChannelAuth(ctx context.Context, in *GetFollowChannelAuthReq, opts ...grpc.CallOption) (*GetFollowChannelAuthResp, error) {
	out := new(GetFollowChannelAuthResp)
	err := grpc.Invoke(ctx, "/friendol_svr.FriendOLSvr/GetFollowChannelAuth", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendOLSvrClient) BatGetFollowChannelAuth(ctx context.Context, in *BatGetFollowChannelAuthReq, opts ...grpc.CallOption) (*BatGetFollowChannelAuthResp, error) {
	out := new(BatGetFollowChannelAuthResp)
	err := grpc.Invoke(ctx, "/friendol_svr.FriendOLSvr/BatGetFollowChannelAuth", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendOLSvrClient) GetFirendCacheCount(ctx context.Context, in *GetFirendCacheCountReq, opts ...grpc.CallOption) (*GetFirendCacheCountResp, error) {
	out := new(GetFirendCacheCountResp)
	err := grpc.Invoke(ctx, "/friendol_svr.FriendOLSvr/GetFirendCacheCount", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for FriendOLSvr service

type FriendOLSvrServer interface {
	GetOnlineFriends(context.Context, *GetOnlineFriendsReq) (*GetOnlineFriendsResp, error)
	GetOfflineFriends(context.Context, *GetOfflineFriendsReq) (*GetOfflineFriendsResp, error)
	UpdateOnlineStatus(context.Context, *UpdateOnlineStatusReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	UpdateUserPlayingGame(context.Context, *UpdateUserPlayingGameReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	UpdateUserRoomId(context.Context, *UpdateUserRoomIdReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 更新用户的跟随开关设置
	UpdateFollowChannelAuth(context.Context, *UpdateFollowChannelAuthReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 获取指定用户的跟随开关设置
	GetFollowChannelAuth(context.Context, *GetFollowChannelAuthReq) (*GetFollowChannelAuthResp, error)
	// 批量获取指定用户的跟随开关设置
	BatGetFollowChannelAuth(context.Context, *BatGetFollowChannelAuthReq) (*BatGetFollowChannelAuthResp, error)
	// 从cache中获取用户好友数量
	GetFirendCacheCount(context.Context, *GetFirendCacheCountReq) (*GetFirendCacheCountResp, error)
}

func RegisterFriendOLSvrServer(s *grpc.Server, srv FriendOLSvrServer) {
	s.RegisterService(&_FriendOLSvr_serviceDesc, srv)
}

func _FriendOLSvr_GetOnlineFriends_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOnlineFriendsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendOLSvrServer).GetOnlineFriends(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/friendol_svr.FriendOLSvr/GetOnlineFriends",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendOLSvrServer).GetOnlineFriends(ctx, req.(*GetOnlineFriendsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FriendOLSvr_GetOfflineFriends_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOfflineFriendsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendOLSvrServer).GetOfflineFriends(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/friendol_svr.FriendOLSvr/GetOfflineFriends",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendOLSvrServer).GetOfflineFriends(ctx, req.(*GetOfflineFriendsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FriendOLSvr_UpdateOnlineStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateOnlineStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendOLSvrServer).UpdateOnlineStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/friendol_svr.FriendOLSvr/UpdateOnlineStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendOLSvrServer).UpdateOnlineStatus(ctx, req.(*UpdateOnlineStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FriendOLSvr_UpdateUserPlayingGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserPlayingGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendOLSvrServer).UpdateUserPlayingGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/friendol_svr.FriendOLSvr/UpdateUserPlayingGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendOLSvrServer).UpdateUserPlayingGame(ctx, req.(*UpdateUserPlayingGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FriendOLSvr_UpdateUserRoomId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserRoomIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendOLSvrServer).UpdateUserRoomId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/friendol_svr.FriendOLSvr/UpdateUserRoomId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendOLSvrServer).UpdateUserRoomId(ctx, req.(*UpdateUserRoomIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FriendOLSvr_UpdateFollowChannelAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateFollowChannelAuthReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendOLSvrServer).UpdateFollowChannelAuth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/friendol_svr.FriendOLSvr/UpdateFollowChannelAuth",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendOLSvrServer).UpdateFollowChannelAuth(ctx, req.(*UpdateFollowChannelAuthReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FriendOLSvr_GetFollowChannelAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFollowChannelAuthReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendOLSvrServer).GetFollowChannelAuth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/friendol_svr.FriendOLSvr/GetFollowChannelAuth",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendOLSvrServer).GetFollowChannelAuth(ctx, req.(*GetFollowChannelAuthReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FriendOLSvr_BatGetFollowChannelAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetFollowChannelAuthReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendOLSvrServer).BatGetFollowChannelAuth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/friendol_svr.FriendOLSvr/BatGetFollowChannelAuth",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendOLSvrServer).BatGetFollowChannelAuth(ctx, req.(*BatGetFollowChannelAuthReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FriendOLSvr_GetFirendCacheCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFirendCacheCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendOLSvrServer).GetFirendCacheCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/friendol_svr.FriendOLSvr/GetFirendCacheCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendOLSvrServer).GetFirendCacheCount(ctx, req.(*GetFirendCacheCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _FriendOLSvr_serviceDesc = grpc.ServiceDesc{
	ServiceName: "friendol_svr.FriendOLSvr",
	HandlerType: (*FriendOLSvrServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetOnlineFriends",
			Handler:    _FriendOLSvr_GetOnlineFriends_Handler,
		},
		{
			MethodName: "GetOfflineFriends",
			Handler:    _FriendOLSvr_GetOfflineFriends_Handler,
		},
		{
			MethodName: "UpdateOnlineStatus",
			Handler:    _FriendOLSvr_UpdateOnlineStatus_Handler,
		},
		{
			MethodName: "UpdateUserPlayingGame",
			Handler:    _FriendOLSvr_UpdateUserPlayingGame_Handler,
		},
		{
			MethodName: "UpdateUserRoomId",
			Handler:    _FriendOLSvr_UpdateUserRoomId_Handler,
		},
		{
			MethodName: "UpdateFollowChannelAuth",
			Handler:    _FriendOLSvr_UpdateFollowChannelAuth_Handler,
		},
		{
			MethodName: "GetFollowChannelAuth",
			Handler:    _FriendOLSvr_GetFollowChannelAuth_Handler,
		},
		{
			MethodName: "BatGetFollowChannelAuth",
			Handler:    _FriendOLSvr_BatGetFollowChannelAuth_Handler,
		},
		{
			MethodName: "GetFirendCacheCount",
			Handler:    _FriendOLSvr_GetFirendCacheCount_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "friendol-svr/friendol-svr.proto",
}

func (m *OnlineInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OnlineInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriendolSvr(dAtA, i, uint64(m.RoomId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintFriendolSvr(dAtA, i, uint64(len(m.GameName)))
	i += copy(dAtA[i:], m.GameName)
	return i, nil
}

func (m *FriendsDetail) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FriendsDetail) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriendolSvr(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintFriendolSvr(dAtA, i, uint64(m.OlStatus))
	dAtA[i] = 0x18
	i++
	i = encodeVarintFriendolSvr(dAtA, i, uint64(m.LastOlTime))
	dAtA[i] = 0x20
	i++
	i = encodeVarintFriendolSvr(dAtA, i, uint64(m.RoomId))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintFriendolSvr(dAtA, i, uint64(len(m.GameName)))
	i += copy(dAtA[i:], m.GameName)
	dAtA[i] = 0x30
	i++
	i = encodeVarintFriendolSvr(dAtA, i, uint64(m.ChannelType))
	dAtA[i] = 0x38
	i++
	if m.ChannelIsPwd {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x40
	i++
	if m.Invisible {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetOnlineFriendsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOnlineFriendsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriendolSvr(dAtA, i, uint64(m.Uid))
	if len(m.ExtCheckOnlineUidlist) > 0 {
		for _, num := range m.ExtCheckOnlineUidlist {
			dAtA[i] = 0x10
			i++
			i = encodeVarintFriendolSvr(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GetOnlineFriendsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOnlineFriendsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.DetailList) > 0 {
		for _, msg := range m.DetailList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintFriendolSvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.ExtCheckOfflineList) > 0 {
		for _, msg := range m.ExtCheckOfflineList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintFriendolSvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetOfflineFriendsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOfflineFriendsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriendolSvr(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetOfflineFriendsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOfflineFriendsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.DetailList) > 0 {
		for _, msg := range m.DetailList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintFriendolSvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *UpdateOnlineStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateOnlineStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriendolSvr(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintFriendolSvr(dAtA, i, uint64(m.OlStatus))
	return i, nil
}

func (m *UpdateUserPlayingGameReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateUserPlayingGameReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriendolSvr(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	if m.IsPlaying {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x1a
	i++
	i = encodeVarintFriendolSvr(dAtA, i, uint64(len(m.GameName)))
	i += copy(dAtA[i:], m.GameName)
	return i, nil
}

func (m *UpdateUserRoomIdReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateUserRoomIdReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriendolSvr(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintFriendolSvr(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	if m.IsEnter {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x20
	i++
	i = encodeVarintFriendolSvr(dAtA, i, uint64(m.ChannelType))
	dAtA[i] = 0x28
	i++
	if m.IsPwd {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *UpdateFollowChannelAuthReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateFollowChannelAuthReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriendolSvr(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	if m.FollowAuth {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x18
	i++
	i = encodeVarintFriendolSvr(dAtA, i, uint64(m.FollowAuthSwitch))
	return i, nil
}

func (m *GetFollowChannelAuthReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFollowChannelAuthReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriendolSvr(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetFollowChannelAuthResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFollowChannelAuthResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.FollowAuth {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x10
	i++
	i = encodeVarintFriendolSvr(dAtA, i, uint64(m.FollowAuthSwitch))
	return i, nil
}

func (m *BatGetFollowChannelAuthReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatGetFollowChannelAuthReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintFriendolSvr(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *BatGetFollowChannelAuthResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatGetFollowChannelAuthResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.FollowAuthList) > 0 {
		for _, b := range m.FollowAuthList {
			dAtA[i] = 0x8
			i++
			if b {
				dAtA[i] = 1
			} else {
				dAtA[i] = 0
			}
			i++
		}
	}
	if len(m.FollowAuthSwitchList) > 0 {
		for _, num := range m.FollowAuthSwitchList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintFriendolSvr(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GetFirendCacheCountReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFirendCacheCountReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriendolSvr(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetFirendCacheCountResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFirendCacheCountResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriendolSvr(dAtA, i, uint64(m.Count))
	return i, nil
}

func encodeFixed64FriendolSvr(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32FriendolSvr(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintFriendolSvr(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *OnlineInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriendolSvr(uint64(m.RoomId))
	l = len(m.GameName)
	n += 1 + l + sovFriendolSvr(uint64(l))
	return n
}

func (m *FriendsDetail) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriendolSvr(uint64(m.Uid))
	n += 1 + sovFriendolSvr(uint64(m.OlStatus))
	n += 1 + sovFriendolSvr(uint64(m.LastOlTime))
	n += 1 + sovFriendolSvr(uint64(m.RoomId))
	l = len(m.GameName)
	n += 1 + l + sovFriendolSvr(uint64(l))
	n += 1 + sovFriendolSvr(uint64(m.ChannelType))
	n += 2
	n += 2
	return n
}

func (m *GetOnlineFriendsReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriendolSvr(uint64(m.Uid))
	if len(m.ExtCheckOnlineUidlist) > 0 {
		for _, e := range m.ExtCheckOnlineUidlist {
			n += 1 + sovFriendolSvr(uint64(e))
		}
	}
	return n
}

func (m *GetOnlineFriendsResp) Size() (n int) {
	var l int
	_ = l
	if len(m.DetailList) > 0 {
		for _, e := range m.DetailList {
			l = e.Size()
			n += 1 + l + sovFriendolSvr(uint64(l))
		}
	}
	if len(m.ExtCheckOfflineList) > 0 {
		for _, e := range m.ExtCheckOfflineList {
			l = e.Size()
			n += 1 + l + sovFriendolSvr(uint64(l))
		}
	}
	return n
}

func (m *GetOfflineFriendsReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriendolSvr(uint64(m.Uid))
	return n
}

func (m *GetOfflineFriendsResp) Size() (n int) {
	var l int
	_ = l
	if len(m.DetailList) > 0 {
		for _, e := range m.DetailList {
			l = e.Size()
			n += 1 + l + sovFriendolSvr(uint64(l))
		}
	}
	return n
}

func (m *UpdateOnlineStatusReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriendolSvr(uint64(m.Uid))
	n += 1 + sovFriendolSvr(uint64(m.OlStatus))
	return n
}

func (m *UpdateUserPlayingGameReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriendolSvr(uint64(m.Uid))
	n += 2
	l = len(m.GameName)
	n += 1 + l + sovFriendolSvr(uint64(l))
	return n
}

func (m *UpdateUserRoomIdReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriendolSvr(uint64(m.Uid))
	n += 1 + sovFriendolSvr(uint64(m.ChannelId))
	n += 2
	n += 1 + sovFriendolSvr(uint64(m.ChannelType))
	n += 2
	return n
}

func (m *UpdateFollowChannelAuthReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriendolSvr(uint64(m.Uid))
	n += 2
	n += 1 + sovFriendolSvr(uint64(m.FollowAuthSwitch))
	return n
}

func (m *GetFollowChannelAuthReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriendolSvr(uint64(m.Uid))
	return n
}

func (m *GetFollowChannelAuthResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	n += 1 + sovFriendolSvr(uint64(m.FollowAuthSwitch))
	return n
}

func (m *BatGetFollowChannelAuthReq) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovFriendolSvr(uint64(e))
		}
	}
	return n
}

func (m *BatGetFollowChannelAuthResp) Size() (n int) {
	var l int
	_ = l
	if len(m.FollowAuthList) > 0 {
		n += 2 * len(m.FollowAuthList)
	}
	if len(m.FollowAuthSwitchList) > 0 {
		for _, e := range m.FollowAuthSwitchList {
			n += 1 + sovFriendolSvr(uint64(e))
		}
	}
	return n
}

func (m *GetFirendCacheCountReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriendolSvr(uint64(m.Uid))
	return n
}

func (m *GetFirendCacheCountResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriendolSvr(uint64(m.Count))
	return n
}

func sovFriendolSvr(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozFriendolSvr(x uint64) (n int) {
	return sovFriendolSvr(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *OnlineInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendolSvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: OnlineInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: OnlineInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RoomId", wireType)
			}
			m.RoomId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendolSvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RoomId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendolSvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendolSvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipFriendolSvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendolSvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("room_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("game_name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FriendsDetail) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendolSvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FriendsDetail: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FriendsDetail: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendolSvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OlStatus", wireType)
			}
			m.OlStatus = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendolSvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OlStatus |= (OnlineStatus(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastOlTime", wireType)
			}
			m.LastOlTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendolSvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastOlTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RoomId", wireType)
			}
			m.RoomId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendolSvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RoomId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendolSvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendolSvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendolSvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIsPwd", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendolSvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ChannelIsPwd = bool(v != 0)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Invisible", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendolSvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Invisible = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipFriendolSvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendolSvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("ol_status")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("last_ol_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOnlineFriendsReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendolSvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOnlineFriendsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOnlineFriendsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendolSvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFriendolSvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ExtCheckOnlineUidlist = append(m.ExtCheckOnlineUidlist, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFriendolSvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthFriendolSvr
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowFriendolSvr
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ExtCheckOnlineUidlist = append(m.ExtCheckOnlineUidlist, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtCheckOnlineUidlist", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFriendolSvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendolSvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOnlineFriendsResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendolSvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOnlineFriendsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOnlineFriendsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DetailList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendolSvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriendolSvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DetailList = append(m.DetailList, &FriendsDetail{})
			if err := m.DetailList[len(m.DetailList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtCheckOfflineList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendolSvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriendolSvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ExtCheckOfflineList = append(m.ExtCheckOfflineList, &FriendsDetail{})
			if err := m.ExtCheckOfflineList[len(m.ExtCheckOfflineList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFriendolSvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendolSvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOfflineFriendsReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendolSvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOfflineFriendsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOfflineFriendsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendolSvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipFriendolSvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendolSvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOfflineFriendsResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendolSvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOfflineFriendsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOfflineFriendsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DetailList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendolSvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriendolSvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DetailList = append(m.DetailList, &FriendsDetail{})
			if err := m.DetailList[len(m.DetailList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFriendolSvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendolSvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateOnlineStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendolSvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateOnlineStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateOnlineStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendolSvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OlStatus", wireType)
			}
			m.OlStatus = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendolSvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OlStatus |= (OnlineStatus(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipFriendolSvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendolSvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("ol_status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateUserPlayingGameReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendolSvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateUserPlayingGameReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateUserPlayingGameReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendolSvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsPlaying", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendolSvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsPlaying = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendolSvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendolSvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipFriendolSvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendolSvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("is_playing")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("game_name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateUserRoomIdReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendolSvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateUserRoomIdReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateUserRoomIdReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendolSvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendolSvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsEnter", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendolSvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsEnter = bool(v != 0)
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendolSvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsPwd", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendolSvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsPwd = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipFriendolSvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendolSvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("is_enter")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateFollowChannelAuthReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendolSvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateFollowChannelAuthReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateFollowChannelAuthReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendolSvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FollowAuth", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendolSvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.FollowAuth = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FollowAuthSwitch", wireType)
			}
			m.FollowAuthSwitch = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendolSvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FollowAuthSwitch |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFriendolSvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendolSvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("follow_auth")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFollowChannelAuthReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendolSvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFollowChannelAuthReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFollowChannelAuthReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendolSvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipFriendolSvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendolSvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFollowChannelAuthResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendolSvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFollowChannelAuthResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFollowChannelAuthResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FollowAuth", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendolSvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.FollowAuth = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FollowAuthSwitch", wireType)
			}
			m.FollowAuthSwitch = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendolSvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FollowAuthSwitch |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFriendolSvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendolSvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("follow_auth")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatGetFollowChannelAuthReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendolSvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatGetFollowChannelAuthReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatGetFollowChannelAuthReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFriendolSvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFriendolSvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthFriendolSvr
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowFriendolSvr
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFriendolSvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendolSvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatGetFollowChannelAuthResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendolSvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatGetFollowChannelAuthResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatGetFollowChannelAuthResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFriendolSvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.FollowAuthList = append(m.FollowAuthList, bool(v != 0))
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFriendolSvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthFriendolSvr
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v int
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowFriendolSvr
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (int(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.FollowAuthList = append(m.FollowAuthList, bool(v != 0))
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field FollowAuthList", wireType)
			}
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFriendolSvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.FollowAuthSwitchList = append(m.FollowAuthSwitchList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFriendolSvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthFriendolSvr
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowFriendolSvr
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.FollowAuthSwitchList = append(m.FollowAuthSwitchList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field FollowAuthSwitchList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFriendolSvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendolSvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFirendCacheCountReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendolSvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFirendCacheCountReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFirendCacheCountReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendolSvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipFriendolSvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendolSvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFirendCacheCountResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendolSvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFirendCacheCountResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFirendCacheCountResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendolSvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipFriendolSvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendolSvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipFriendolSvr(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowFriendolSvr
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowFriendolSvr
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowFriendolSvr
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthFriendolSvr
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowFriendolSvr
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipFriendolSvr(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthFriendolSvr = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowFriendolSvr   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("friendol-svr/friendol-svr.proto", fileDescriptorFriendolSvr) }

var fileDescriptorFriendolSvr = []byte{
	// 1054 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x56, 0xdd, 0x4e, 0x1b, 0x47,
	0x14, 0x66, 0xbd, 0xe0, 0x9f, 0x63, 0x48, 0xdd, 0x21, 0xc0, 0x66, 0x69, 0xc1, 0xd9, 0x04, 0xe2,
	0x46, 0xc2, 0x34, 0x48, 0x51, 0xd4, 0x88, 0x46, 0x2a, 0x14, 0x10, 0x2a, 0x02, 0x64, 0xc2, 0xf5,
	0x6a, 0xe3, 0x1d, 0xc7, 0x23, 0xf6, 0xaf, 0x3b, 0xb3, 0xfc, 0xf4, 0x22, 0xca, 0x65, 0x95, 0x9b,
	0x56, 0x55, 0x1f, 0x01, 0xf5, 0x1d, 0x7a, 0xd3, 0xeb, 0xf4, 0xae, 0x4f, 0x50, 0x55, 0xf4, 0x86,
	0xbe, 0x45, 0x35, 0x3b, 0x5e, 0x7b, 0xbd, 0xbb, 0xfe, 0x89, 0x72, 0xe7, 0x9d, 0xf9, 0xce, 0xf9,
	0xbe, 0x33, 0x73, 0xce, 0xe7, 0x81, 0xe5, 0x96, 0x4f, 0xb0, 0x63, 0xba, 0xd6, 0x1a, 0x3d, 0xf7,
	0xd7, 0xe3, 0x1f, 0x75, 0xcf, 0x77, 0x99, 0x8b, 0xa6, 0xa3, 0x35, 0x9d, 0x9e, 0xfb, 0xea, 0xc3,
	0xa6, 0x6b, 0xdb, 0xae, 0xb3, 0xce, 0xac, 0x73, 0x8f, 0x34, 0xcf, 0x2c, 0xbc, 0x4e, 0xcf, 0x5e,
	0x05, 0xc4, 0x62, 0xc4, 0x61, 0x57, 0x1e, 0x16, 0x31, 0xda, 0x21, 0xc0, 0x91, 0x63, 0x11, 0x07,
	0xef, 0x3b, 0x2d, 0x17, 0x7d, 0x0e, 0x05, 0xdf, 0x75, 0x6d, 0x9d, 0x98, 0x8a, 0x54, 0xcd, 0xd5,
	0x66, 0xb6, 0x26, 0xdf, 0xff, 0xbd, 0x3c, 0xd1, 0xc8, 0xf3, 0xc5, 0x7d, 0x13, 0xdd, 0x87, 0xd2,
	0x6b, 0xc3, 0xc6, 0xba, 0x63, 0xd8, 0x58, 0xc9, 0x55, 0x73, 0xb5, 0x52, 0x07, 0x50, 0xe4, 0xcb,
	0x87, 0x86, 0x8d, 0xb5, 0x3f, 0x73, 0x30, 0xb3, 0x1b, 0xca, 0xa0, 0xdf, 0x62, 0x66, 0x10, 0x0b,
	0xcd, 0x83, 0x1c, 0x24, 0xf2, 0xf1, 0x05, 0xf4, 0x35, 0x94, 0xb8, 0x52, 0x66, 0xb0, 0x80, 0x86,
	0xc9, 0xee, 0x6c, 0xa8, 0xf5, 0x78, 0x05, 0x75, 0x21, 0xec, 0x24, 0x44, 0x44, 0x44, 0xae, 0x25,
	0xbe, 0xd1, 0x2a, 0x4c, 0x5b, 0x06, 0x65, 0xba, 0x6b, 0xe9, 0x8c, 0xd8, 0x58, 0x91, 0x63, 0xf9,
	0x81, 0xef, 0x1c, 0x59, 0x2f, 0x89, 0x8d, 0xe3, 0x25, 0x4d, 0x56, 0xa5, 0xe1, 0x25, 0x4d, 0x55,
	0xa5, 0x74, 0x49, 0xe8, 0x11, 0x4c, 0x37, 0xdb, 0x86, 0xe3, 0x60, 0x4b, 0xe7, 0x07, 0xa7, 0xe4,
	0x63, 0x69, 0xca, 0x9d, 0x9d, 0x97, 0x57, 0x1e, 0x46, 0x8f, 0xe1, 0x4e, 0x04, 0x24, 0x54, 0xf7,
	0x2e, 0x4c, 0xa5, 0x50, 0x95, 0x6a, 0xc5, 0x0e, 0x34, 0x4a, 0xb2, 0x4f, 0x8f, 0x2f, 0x4c, 0xa4,
	0x41, 0x89, 0x38, 0xe7, 0x84, 0x92, 0x57, 0x16, 0x56, 0x8a, 0x31, 0x58, 0x6f, 0x59, 0x6b, 0xc1,
	0xec, 0x1e, 0x66, 0xe2, 0x14, 0x3a, 0x67, 0xda, 0xc0, 0xdf, 0x0f, 0x3c, 0xd0, 0x67, 0xa0, 0xe0,
	0x4b, 0xa6, 0x37, 0xdb, 0xb8, 0x79, 0xa6, 0xbb, 0x61, 0x94, 0x1e, 0x10, 0xd3, 0x22, 0x94, 0x29,
	0xb9, 0xaa, 0x5c, 0x9b, 0x69, 0xcc, 0xe1, 0x4b, 0xb6, 0xcd, 0xb7, 0x45, 0xce, 0x53, 0xb1, 0xa9,
	0xfd, 0x26, 0xc1, 0xdd, 0x34, 0x11, 0xf5, 0xd0, 0x26, 0x94, 0xcd, 0xf0, 0x12, 0xf5, 0x30, 0x89,
	0x54, 0x95, 0x6b, 0xe5, 0x8d, 0xc5, 0xfe, 0x4b, 0xea, 0xbb, 0xec, 0x06, 0x08, 0xfc, 0x01, 0xa1,
	0x0c, 0x1d, 0xc3, 0x7c, 0x4c, 0x4f, 0xab, 0x15, 0x0a, 0xea, 0xaa, 0x19, 0x91, 0x68, 0xb6, 0x2b,
	0x55, 0x04, 0xf2, 0x8c, 0x5a, 0x5d, 0xe8, 0x14, 0x2b, 0xa3, 0x4f, 0x44, 0x3b, 0x85, 0xb9, 0x0c,
	0xfc, 0xc7, 0x16, 0xa6, 0x39, 0x30, 0x77, 0xea, 0x99, 0x06, 0xc3, 0xf1, 0x06, 0x1d, 0x76, 0x33,
	0x1f, 0xd7, 0xea, 0xda, 0x0f, 0xa0, 0x08, 0xbe, 0x53, 0x8a, 0xfd, 0x63, 0xcb, 0xb8, 0x22, 0xce,
	0xeb, 0x3d, 0xc3, 0xc6, 0xc3, 0x28, 0x1f, 0x00, 0xf0, 0x1e, 0x14, 0xe0, 0x90, 0xb3, 0xd7, 0x60,
	0xb4, 0x93, 0xa3, 0xbf, 0xf9, 0xe5, 0xcc, 0x79, 0xfe, 0x5d, 0x82, 0xd9, 0x1e, 0x79, 0x23, 0x1c,
	0x9a, 0x11, 0xbc, 0xdd, 0x19, 0x30, 0x43, 0xde, 0x68, 0xbb, 0x14, 0xf5, 0xbf, 0x89, 0x96, 0xa1,
	0x48, 0xa8, 0x8e, 0x1d, 0x86, 0xfd, 0x90, 0x36, 0x92, 0x56, 0x20, 0x74, 0x87, 0x2f, 0xa6, 0x46,
	0x6e, 0x72, 0xd0, 0xc8, 0x2d, 0x42, 0xbe, 0x33, 0x6a, 0x53, 0xb1, 0x19, 0x9a, 0x22, 0x7c, 0xc6,
	0xb4, 0x9f, 0x24, 0x50, 0x85, 0xf6, 0x5d, 0xd7, 0xb2, 0xdc, 0x8b, 0x6d, 0x11, 0xf8, 0x4d, 0xc0,
	0xda, 0xc3, 0x4a, 0x58, 0x81, 0x72, 0x2b, 0xc4, 0xeb, 0x46, 0xc0, 0xda, 0x7d, 0x67, 0x07, 0x62,
	0x83, 0x67, 0x40, 0x1b, 0x80, 0x62, 0x30, 0x9d, 0x5e, 0x10, 0xd6, 0x6c, 0x2b, 0x72, 0x4c, 0x69,
	0xa5, 0x87, 0x3e, 0x09, 0x77, 0xb5, 0x27, 0xb0, 0xb0, 0x87, 0xd9, 0x87, 0xa8, 0xd1, 0x02, 0x50,
	0xb2, 0x43, 0xa8, 0x97, 0x54, 0x2a, 0x7d, 0x90, 0xd2, 0xdc, 0x50, 0xa5, 0xcf, 0x40, 0xdd, 0x32,
	0xd8, 0x20, 0xb1, 0xf7, 0xa0, 0x18, 0x10, 0xb3, 0x37, 0x3c, 0x33, 0x8d, 0x42, 0x40, 0xcc, 0x70,
	0x38, 0xde, 0xc0, 0xe2, 0xc0, 0x40, 0xea, 0xa1, 0x1a, 0x54, 0xe2, 0x5a, 0xba, 0x19, 0x8a, 0x8d,
	0x3b, 0x3d, 0x0d, 0xa1, 0x7d, 0x3c, 0x85, 0x85, 0xb4, 0x6a, 0x3d, 0xe6, 0x66, 0x77, 0x93, 0xa2,
	0x43, 0xfe, 0x2f, 0x61, 0x9e, 0x93, 0x13, 0x1f, 0x3b, 0xe6, 0xb6, 0xd1, 0x6c, 0xe3, 0x6d, 0x37,
	0x70, 0xd8, 0xb0, 0x13, 0x7e, 0x2a, 0x2e, 0x25, 0x15, 0x41, 0x3d, 0xa4, 0xc2, 0x54, 0x93, 0x7f,
	0xf4, 0x05, 0x89, 0xa5, 0xc7, 0x8f, 0x60, 0x3a, 0x3e, 0xb5, 0xa8, 0x0c, 0x85, 0xa3, 0xdd, 0xdd,
	0x83, 0xfd, 0xc3, 0x9d, 0xca, 0x04, 0x02, 0xc8, 0x1f, 0x1d, 0x86, 0xbf, 0xa5, 0x8d, 0xff, 0x8a,
	0x50, 0x16, 0x66, 0x72, 0x74, 0x70, 0x72, 0xee, 0xa3, 0x5f, 0x25, 0xa8, 0x24, 0xed, 0x16, 0xdd,
	0xef, 0xf7, 0x83, 0x0c, 0xdf, 0x57, 0xb5, 0x51, 0x10, 0xea, 0x69, 0x5f, 0xbd, 0xbd, 0xbe, 0x95,
	0xa5, 0x77, 0xd7, 0xb7, 0xf2, 0x64, 0xf0, 0xfc, 0xf2, 0xf9, 0x2f, 0xd7, 0xb7, 0xf2, 0xea, 0x5a,
	0x50, 0xdd, 0x0c, 0x88, 0xf9, 0xa2, 0xba, 0x76, 0x59, 0xdd, 0x1c, 0xf4, 0x0f, 0xf1, 0x02, 0x5d,
	0xc0, 0xa7, 0x29, 0xb3, 0x44, 0x19, 0x9c, 0x49, 0xf7, 0x55, 0x1f, 0x8c, 0xc4, 0x50, 0x4f, 0xbb,
	0xc7, 0x85, 0xe5, 0xb8, 0xb0, 0x5c, 0x10, 0xca, 0x2a, 0x46, 0xb2, 0x50, 0x1b, 0x50, 0xda, 0x4e,
	0x51, 0x22, 0x6b, 0xa6, 0xe1, 0xaa, 0x9f, 0xd5, 0xbb, 0xaf, 0x9b, 0xfa, 0xc9, 0x77, 0x5b, 0xe2,
	0x75, 0xb3, 0x63, 0x7b, 0xec, 0x4a, 0x3f, 0xde, 0xd2, 0x3e, 0xe1, 0x9c, 0x32, 0xe7, 0x9c, 0xe0,
	0x8c, 0x13, 0xa8, 0x6b, 0xdc, 0x09, 0x23, 0x45, 0xab, 0x59, 0x64, 0x69, 0xb7, 0x1d, 0x87, 0x6f,
	0x32, 0xc6, 0x67, 0x42, 0x25, 0xe9, 0x9d, 0xc9, 0x8b, 0xce, 0xf0, 0xd6, 0x71, 0x58, 0xa6, 0x62,
	0x2c, 0x3e, 0x2c, 0x0c, 0x70, 0x39, 0x54, 0xcb, 0x22, 0xcb, 0x9a, 0xe8, 0x71, 0x38, 0xf3, 0x31,
	0xce, 0xb7, 0xe2, 0xc9, 0x90, 0x66, 0x5c, 0x49, 0x35, 0x43, 0x26, 0xdd, 0xea, 0x38, 0xb0, 0xa8,
	0x6d, 0x0a, 0x99, 0x6d, 0xf3, 0x4e, 0x82, 0x85, 0x01, 0x4e, 0x93, 0xac, 0x7b, 0xb0, 0x93, 0xa9,
	0x5f, 0x8c, 0x89, 0xa4, 0x9e, 0xa6, 0x72, 0x2d, 0xc5, 0x98, 0x96, 0xd2, 0x5a, 0xf0, 0xa4, 0x23,
	0xe6, 0x4d, 0xf8, 0x54, 0x4b, 0x7a, 0x08, 0x7a, 0x98, 0x2e, 0x33, 0x6d, 0x4c, 0xea, 0xca, 0x18,
	0xa8, 0x88, 0xbf, 0x94, 0xc9, 0xaf, 0xe6, 0x7f, 0xbc, 0xbe, 0x95, 0xff, 0xb8, 0xda, 0xaa, 0xbc,
	0xbf, 0x59, 0x92, 0xfe, 0xba, 0x59, 0x92, 0xfe, 0xb9, 0x59, 0x92, 0x7e, 0xfe, 0x77, 0x69, 0xe2,
	0xff, 0x00, 0x00, 0x00, 0xff, 0xff, 0x5f, 0x67, 0x51, 0xe4, 0x36, 0x0c, 0x00, 0x00,
}
