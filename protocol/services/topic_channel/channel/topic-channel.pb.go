// Code generated by protoc-gen-go. DO NOT EDIT.
// source: topic_channel/topic-channel.proto

package channel // import "golang.52tt.com/protocol/services/topic_channel/channel"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ChannelDisplayType int32

const (
	ChannelDisplayType_DISPLAY_AT_MAIN_PAGE   ChannelDisplayType = 0
	ChannelDisplayType_DISMISSED              ChannelDisplayType = 1
	ChannelDisplayType_DISPLAY_AT_FIND_FRIEND ChannelDisplayType = 2
	ChannelDisplayType_TEMPORARY              ChannelDisplayType = 3
)

var ChannelDisplayType_name = map[int32]string{
	0: "DISPLAY_AT_MAIN_PAGE",
	1: "DISMISSED",
	2: "DISPLAY_AT_FIND_FRIEND",
	3: "TEMPORARY",
}
var ChannelDisplayType_value = map[string]int32{
	"DISPLAY_AT_MAIN_PAGE":   0,
	"DISMISSED":              1,
	"DISPLAY_AT_FIND_FRIEND": 2,
	"TEMPORARY":              3,
}

func (x ChannelDisplayType) String() string {
	return proto.EnumName(ChannelDisplayType_name, int32(x))
}
func (ChannelDisplayType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{0}
}

// 显示首页类别，临时解决方案，之后迁移到hobby channel中区分
type HomePageType int32

const (
	HomePageType_Default HomePageType = 0
	HomePageType_Game    HomePageType = 1
	HomePageType_Music   HomePageType = 2
)

var HomePageType_name = map[int32]string{
	0: "Default",
	1: "Game",
	2: "Music",
}
var HomePageType_value = map[string]int32{
	"Default": 0,
	"Game":    1,
	"Music":   2,
}

func (x HomePageType) String() string {
	return proto.EnumName(HomePageType_name, int32(x))
}
func (HomePageType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{1}
}

type DismissType int32

const (
	DismissType_Unknown   DismissType = 0
	DismissType_Sever     DismissType = 1
	DismissType_Cancel    DismissType = 2
	DismissType_SwitchTab DismissType = 3
	DismissType_Quit      DismissType = 4
)

var DismissType_name = map[int32]string{
	0: "Unknown",
	1: "Sever",
	2: "Cancel",
	3: "SwitchTab",
	4: "Quit",
}
var DismissType_value = map[string]int32{
	"Unknown":   0,
	"Sever":     1,
	"Cancel":    2,
	"SwitchTab": 3,
	"Quit":      4,
}

func (x DismissType) String() string {
	return proto.EnumName(DismissType_name, int32(x))
}
func (DismissType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{2}
}

type KeepAliveStatus int32

const (
	KeepAliveStatus_ALIVE        KeepAliveStatus = 0
	KeepAliveStatus_DISCONNECTED KeepAliveStatus = 1
)

var KeepAliveStatus_name = map[int32]string{
	0: "ALIVE",
	1: "DISCONNECTED",
}
var KeepAliveStatus_value = map[string]int32{
	"ALIVE":        0,
	"DISCONNECTED": 1,
}

func (x KeepAliveStatus) String() string {
	return proto.EnumName(KeepAliveStatus_name, int32(x))
}
func (KeepAliveStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{3}
}

type Source int32

const (
	Source_INVAIAL Source = 0
	Source_CREATE  Source = 1
	Source_SWITCH  Source = 2
	Source_PUBLISH Source = 3
)

var Source_name = map[int32]string{
	0: "INVAIAL",
	1: "CREATE",
	2: "SWITCH",
	3: "PUBLISH",
}
var Source_value = map[string]int32{
	"INVAIAL": 0,
	"CREATE":  1,
	"SWITCH":  2,
	"PUBLISH": 3,
}

func (x Source) String() string {
	return proto.EnumName(Source_name, int32(x))
}
func (Source) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{4}
}

type BlockOptionList struct {
	BlockOptions         []*BlockOption `protobuf:"bytes,1,rep,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *BlockOptionList) Reset()         { *m = BlockOptionList{} }
func (m *BlockOptionList) String() string { return proto.CompactTextString(m) }
func (*BlockOptionList) ProtoMessage()    {}
func (*BlockOptionList) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{0}
}
func (m *BlockOptionList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BlockOptionList.Unmarshal(m, b)
}
func (m *BlockOptionList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BlockOptionList.Marshal(b, m, deterministic)
}
func (dst *BlockOptionList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BlockOptionList.Merge(dst, src)
}
func (m *BlockOptionList) XXX_Size() int {
	return xxx_messageInfo_BlockOptionList.Size(m)
}
func (m *BlockOptionList) XXX_DiscardUnknown() {
	xxx_messageInfo_BlockOptionList.DiscardUnknown(m)
}

var xxx_messageInfo_BlockOptionList proto.InternalMessageInfo

func (m *BlockOptionList) GetBlockOptions() []*BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

type BlockOption struct {
	BlockId              uint32   `protobuf:"varint,1,opt,name=block_id,json=blockId,proto3" json:"block_id,omitempty"`
	ElemId               uint32   `protobuf:"varint,2,opt,name=elem_id,json=elemId,proto3" json:"elem_id,omitempty"`
	ElemVal              string   `protobuf:"bytes,3,opt,name=elem_val,json=elemVal,proto3" json:"elem_val,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BlockOption) Reset()         { *m = BlockOption{} }
func (m *BlockOption) String() string { return proto.CompactTextString(m) }
func (*BlockOption) ProtoMessage()    {}
func (*BlockOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{1}
}
func (m *BlockOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BlockOption.Unmarshal(m, b)
}
func (m *BlockOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BlockOption.Marshal(b, m, deterministic)
}
func (dst *BlockOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BlockOption.Merge(dst, src)
}
func (m *BlockOption) XXX_Size() int {
	return xxx_messageInfo_BlockOption.Size(m)
}
func (m *BlockOption) XXX_DiscardUnknown() {
	xxx_messageInfo_BlockOption.DiscardUnknown(m)
}

var xxx_messageInfo_BlockOption proto.InternalMessageInfo

func (m *BlockOption) GetBlockId() uint32 {
	if m != nil {
		return m.BlockId
	}
	return 0
}

func (m *BlockOption) GetElemId() uint32 {
	if m != nil {
		return m.ElemId
	}
	return 0
}

func (m *BlockOption) GetElemVal() string {
	if m != nil {
		return m.ElemVal
	}
	return ""
}

type ChannelInfo struct {
	Id                   uint32               `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	TabId                uint32               `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	CreateTime           int64                `protobuf:"varint,3,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	MaleCount            uint32               `protobuf:"varint,4,opt,name=male_count,json=maleCount,proto3" json:"male_count,omitempty"`
	FemaleCount          uint32               `protobuf:"varint,5,opt,name=female_count,json=femaleCount,proto3" json:"female_count,omitempty"`
	IsRecommendChannel   bool                 `protobuf:"varint,6,opt,name=is_recommend_channel,json=isRecommendChannel,proto3" json:"is_recommend_channel,omitempty"`
	Creator              uint32               `protobuf:"varint,7,opt,name=creator,proto3" json:"creator,omitempty"`
	TotalCount           uint32               `protobuf:"varint,8,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	ScanParam            string               `protobuf:"bytes,9,opt,name=scan_param,json=scanParam,proto3" json:"scan_param,omitempty"`
	BlockOptions         []*BlockOption       `protobuf:"bytes,10,rep,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"`
	IsPrivate            bool                 `protobuf:"varint,11,opt,name=is_private,json=isPrivate,proto3" json:"is_private,omitempty"`
	OnMicCount           uint32               `protobuf:"varint,12,opt,name=on_mic_count,json=onMicCount,proto3" json:"on_mic_count,omitempty"`
	Name                 string               `protobuf:"bytes,13,opt,name=name,proto3" json:"name,omitempty"`
	DisplayType          []ChannelDisplayType `protobuf:"varint,14,rep,packed,name=display_type,json=displayType,proto3,enum=topic_channel.channel.ChannelDisplayType" json:"display_type,omitempty"`
	WantFresh            bool                 `protobuf:"varint,15,opt,name=want_fresh,json=wantFresh,proto3" json:"want_fresh,omitempty"`
	ReleaseIp            string               `protobuf:"bytes,16,opt,name=release_ip,json=releaseIp,proto3" json:"release_ip,omitempty"`
	ShowGeoInfo          bool                 `protobuf:"varint,17,opt,name=show_geo_info,json=showGeoInfo,proto3" json:"show_geo_info,omitempty"`
	MarketId             uint32               `protobuf:"varint,18,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	TerminalType         uint32               `protobuf:"varint,19,opt,name=terminal_type,json=terminalType,proto3" json:"terminal_type,omitempty"`
	LastDismissTime      int64                `protobuf:"varint,20,opt,name=last_dismiss_time,json=lastDismissTime,proto3" json:"last_dismiss_time,omitempty"`
	SwitchTime           int64                `protobuf:"varint,21,opt,name=switch_time,json=switchTime,proto3" json:"switch_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *ChannelInfo) Reset()         { *m = ChannelInfo{} }
func (m *ChannelInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelInfo) ProtoMessage()    {}
func (*ChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{2}
}
func (m *ChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelInfo.Unmarshal(m, b)
}
func (m *ChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelInfo.Merge(dst, src)
}
func (m *ChannelInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelInfo.Size(m)
}
func (m *ChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelInfo proto.InternalMessageInfo

func (m *ChannelInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ChannelInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *ChannelInfo) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *ChannelInfo) GetMaleCount() uint32 {
	if m != nil {
		return m.MaleCount
	}
	return 0
}

func (m *ChannelInfo) GetFemaleCount() uint32 {
	if m != nil {
		return m.FemaleCount
	}
	return 0
}

func (m *ChannelInfo) GetIsRecommendChannel() bool {
	if m != nil {
		return m.IsRecommendChannel
	}
	return false
}

func (m *ChannelInfo) GetCreator() uint32 {
	if m != nil {
		return m.Creator
	}
	return 0
}

func (m *ChannelInfo) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *ChannelInfo) GetScanParam() string {
	if m != nil {
		return m.ScanParam
	}
	return ""
}

func (m *ChannelInfo) GetBlockOptions() []*BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

func (m *ChannelInfo) GetIsPrivate() bool {
	if m != nil {
		return m.IsPrivate
	}
	return false
}

func (m *ChannelInfo) GetOnMicCount() uint32 {
	if m != nil {
		return m.OnMicCount
	}
	return 0
}

func (m *ChannelInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ChannelInfo) GetDisplayType() []ChannelDisplayType {
	if m != nil {
		return m.DisplayType
	}
	return nil
}

func (m *ChannelInfo) GetWantFresh() bool {
	if m != nil {
		return m.WantFresh
	}
	return false
}

func (m *ChannelInfo) GetReleaseIp() string {
	if m != nil {
		return m.ReleaseIp
	}
	return ""
}

func (m *ChannelInfo) GetShowGeoInfo() bool {
	if m != nil {
		return m.ShowGeoInfo
	}
	return false
}

func (m *ChannelInfo) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *ChannelInfo) GetTerminalType() uint32 {
	if m != nil {
		return m.TerminalType
	}
	return 0
}

func (m *ChannelInfo) GetLastDismissTime() int64 {
	if m != nil {
		return m.LastDismissTime
	}
	return 0
}

func (m *ChannelInfo) GetSwitchTime() int64 {
	if m != nil {
		return m.SwitchTime
	}
	return 0
}

type AddChannelReq struct {
	Channel              *ChannelInfo `protobuf:"bytes,1,opt,name=channel,proto3" json:"channel,omitempty"`
	IsChange             bool         `protobuf:"varint,2,opt,name=is_change,json=isChange,proto3" json:"is_change,omitempty"`
	HomePageType         HomePageType `protobuf:"varint,3,opt,name=home_page_type,json=homePageType,proto3,enum=topic_channel.channel.HomePageType" json:"home_page_type,omitempty"`
	AllSelectedBids      []uint32     `protobuf:"varint,4,rep,packed,name=all_selected_bids,json=allSelectedBids,proto3" json:"all_selected_bids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AddChannelReq) Reset()         { *m = AddChannelReq{} }
func (m *AddChannelReq) String() string { return proto.CompactTextString(m) }
func (*AddChannelReq) ProtoMessage()    {}
func (*AddChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{3}
}
func (m *AddChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddChannelReq.Unmarshal(m, b)
}
func (m *AddChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddChannelReq.Marshal(b, m, deterministic)
}
func (dst *AddChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddChannelReq.Merge(dst, src)
}
func (m *AddChannelReq) XXX_Size() int {
	return xxx_messageInfo_AddChannelReq.Size(m)
}
func (m *AddChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddChannelReq proto.InternalMessageInfo

func (m *AddChannelReq) GetChannel() *ChannelInfo {
	if m != nil {
		return m.Channel
	}
	return nil
}

func (m *AddChannelReq) GetIsChange() bool {
	if m != nil {
		return m.IsChange
	}
	return false
}

func (m *AddChannelReq) GetHomePageType() HomePageType {
	if m != nil {
		return m.HomePageType
	}
	return HomePageType_Default
}

func (m *AddChannelReq) GetAllSelectedBids() []uint32 {
	if m != nil {
		return m.AllSelectedBids
	}
	return nil
}

type AddChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddChannelResp) Reset()         { *m = AddChannelResp{} }
func (m *AddChannelResp) String() string { return proto.CompactTextString(m) }
func (*AddChannelResp) ProtoMessage()    {}
func (*AddChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{4}
}
func (m *AddChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddChannelResp.Unmarshal(m, b)
}
func (m *AddChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddChannelResp.Marshal(b, m, deterministic)
}
func (dst *AddChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddChannelResp.Merge(dst, src)
}
func (m *AddChannelResp) XXX_Size() int {
	return xxx_messageInfo_AddChannelResp.Size(m)
}
func (m *AddChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddChannelResp proto.InternalMessageInfo

type UpdateChannelInfoReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	MaleCount            uint32   `protobuf:"varint,4,opt,name=male_count,json=maleCount,proto3" json:"male_count,omitempty"`
	FemaleCount          uint32   `protobuf:"varint,5,opt,name=female_count,json=femaleCount,proto3" json:"female_count,omitempty"`
	TotalCount           uint32   `protobuf:"varint,6,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	OnMicCount           uint32   `protobuf:"varint,7,opt,name=on_mic_count,json=onMicCount,proto3" json:"on_mic_count,omitempty"`
	Creator              uint32   `protobuf:"varint,8,opt,name=creator,proto3" json:"creator,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateChannelInfoReq) Reset()         { *m = UpdateChannelInfoReq{} }
func (m *UpdateChannelInfoReq) String() string { return proto.CompactTextString(m) }
func (*UpdateChannelInfoReq) ProtoMessage()    {}
func (*UpdateChannelInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{5}
}
func (m *UpdateChannelInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateChannelInfoReq.Unmarshal(m, b)
}
func (m *UpdateChannelInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateChannelInfoReq.Marshal(b, m, deterministic)
}
func (dst *UpdateChannelInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateChannelInfoReq.Merge(dst, src)
}
func (m *UpdateChannelInfoReq) XXX_Size() int {
	return xxx_messageInfo_UpdateChannelInfoReq.Size(m)
}
func (m *UpdateChannelInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateChannelInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateChannelInfoReq proto.InternalMessageInfo

func (m *UpdateChannelInfoReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UpdateChannelInfoReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *UpdateChannelInfoReq) GetMaleCount() uint32 {
	if m != nil {
		return m.MaleCount
	}
	return 0
}

func (m *UpdateChannelInfoReq) GetFemaleCount() uint32 {
	if m != nil {
		return m.FemaleCount
	}
	return 0
}

func (m *UpdateChannelInfoReq) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *UpdateChannelInfoReq) GetOnMicCount() uint32 {
	if m != nil {
		return m.OnMicCount
	}
	return 0
}

func (m *UpdateChannelInfoReq) GetCreator() uint32 {
	if m != nil {
		return m.Creator
	}
	return 0
}

type UpdateChannelInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateChannelInfoResp) Reset()         { *m = UpdateChannelInfoResp{} }
func (m *UpdateChannelInfoResp) String() string { return proto.CompactTextString(m) }
func (*UpdateChannelInfoResp) ProtoMessage()    {}
func (*UpdateChannelInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{6}
}
func (m *UpdateChannelInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateChannelInfoResp.Unmarshal(m, b)
}
func (m *UpdateChannelInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateChannelInfoResp.Marshal(b, m, deterministic)
}
func (dst *UpdateChannelInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateChannelInfoResp.Merge(dst, src)
}
func (m *UpdateChannelInfoResp) XXX_Size() int {
	return xxx_messageInfo_UpdateChannelInfoResp.Size(m)
}
func (m *UpdateChannelInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateChannelInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateChannelInfoResp proto.InternalMessageInfo

type DismissChannelReq struct {
	ChannelId            uint32      `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Type                 DismissType `protobuf:"varint,2,opt,name=type,proto3,enum=topic_channel.channel.DismissType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *DismissChannelReq) Reset()         { *m = DismissChannelReq{} }
func (m *DismissChannelReq) String() string { return proto.CompactTextString(m) }
func (*DismissChannelReq) ProtoMessage()    {}
func (*DismissChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{7}
}
func (m *DismissChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DismissChannelReq.Unmarshal(m, b)
}
func (m *DismissChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DismissChannelReq.Marshal(b, m, deterministic)
}
func (dst *DismissChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DismissChannelReq.Merge(dst, src)
}
func (m *DismissChannelReq) XXX_Size() int {
	return xxx_messageInfo_DismissChannelReq.Size(m)
}
func (m *DismissChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DismissChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_DismissChannelReq proto.InternalMessageInfo

func (m *DismissChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *DismissChannelReq) GetType() DismissType {
	if m != nil {
		return m.Type
	}
	return DismissType_Unknown
}

type DismissChannelResp struct {
	Dismiss              bool     `protobuf:"varint,1,opt,name=dismiss,proto3" json:"dismiss,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DismissChannelResp) Reset()         { *m = DismissChannelResp{} }
func (m *DismissChannelResp) String() string { return proto.CompactTextString(m) }
func (*DismissChannelResp) ProtoMessage()    {}
func (*DismissChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{8}
}
func (m *DismissChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DismissChannelResp.Unmarshal(m, b)
}
func (m *DismissChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DismissChannelResp.Marshal(b, m, deterministic)
}
func (dst *DismissChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DismissChannelResp.Merge(dst, src)
}
func (m *DismissChannelResp) XXX_Size() int {
	return xxx_messageInfo_DismissChannelResp.Size(m)
}
func (m *DismissChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DismissChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_DismissChannelResp proto.InternalMessageInfo

func (m *DismissChannelResp) GetDismiss() bool {
	if m != nil {
		return m.Dismiss
	}
	return false
}

type GetRecommendChannelListLoadMore struct {
	Num                  uint32   `protobuf:"varint,1,opt,name=num,proto3" json:"num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecommendChannelListLoadMore) Reset()         { *m = GetRecommendChannelListLoadMore{} }
func (m *GetRecommendChannelListLoadMore) String() string { return proto.CompactTextString(m) }
func (*GetRecommendChannelListLoadMore) ProtoMessage()    {}
func (*GetRecommendChannelListLoadMore) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{9}
}
func (m *GetRecommendChannelListLoadMore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendChannelListLoadMore.Unmarshal(m, b)
}
func (m *GetRecommendChannelListLoadMore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendChannelListLoadMore.Marshal(b, m, deterministic)
}
func (dst *GetRecommendChannelListLoadMore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendChannelListLoadMore.Merge(dst, src)
}
func (m *GetRecommendChannelListLoadMore) XXX_Size() int {
	return xxx_messageInfo_GetRecommendChannelListLoadMore.Size(m)
}
func (m *GetRecommendChannelListLoadMore) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendChannelListLoadMore.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendChannelListLoadMore proto.InternalMessageInfo

func (m *GetRecommendChannelListLoadMore) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

type GetRecommendChannelListReq struct {
	Uid                  uint32                           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Limit                uint32                           `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	LoadMore             *GetRecommendChannelListLoadMore `protobuf:"bytes,3,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	TabIdList            []uint32                         `protobuf:"varint,4,rep,packed,name=tab_id_list,json=tabIdList,proto3" json:"tab_id_list,omitempty"`
	NotClearHistory      bool                             `protobuf:"varint,5,opt,name=not_clear_history,json=notClearHistory,proto3" json:"not_clear_history,omitempty"`
	NotCheckHistory      bool                             `protobuf:"varint,6,opt,name=not_check_history,json=notCheckHistory,proto3" json:"not_check_history,omitempty"`
	NotSaveHistory       bool                             `protobuf:"varint,7,opt,name=not_save_history,json=notSaveHistory,proto3" json:"not_save_history,omitempty"`
	ReturnPgc            uint32                           `protobuf:"varint,8,opt,name=return_pgc,json=returnPgc,proto3" json:"return_pgc,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *GetRecommendChannelListReq) Reset()         { *m = GetRecommendChannelListReq{} }
func (m *GetRecommendChannelListReq) String() string { return proto.CompactTextString(m) }
func (*GetRecommendChannelListReq) ProtoMessage()    {}
func (*GetRecommendChannelListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{10}
}
func (m *GetRecommendChannelListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendChannelListReq.Unmarshal(m, b)
}
func (m *GetRecommendChannelListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendChannelListReq.Marshal(b, m, deterministic)
}
func (dst *GetRecommendChannelListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendChannelListReq.Merge(dst, src)
}
func (m *GetRecommendChannelListReq) XXX_Size() int {
	return xxx_messageInfo_GetRecommendChannelListReq.Size(m)
}
func (m *GetRecommendChannelListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendChannelListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendChannelListReq proto.InternalMessageInfo

func (m *GetRecommendChannelListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRecommendChannelListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetRecommendChannelListReq) GetLoadMore() *GetRecommendChannelListLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

func (m *GetRecommendChannelListReq) GetTabIdList() []uint32 {
	if m != nil {
		return m.TabIdList
	}
	return nil
}

func (m *GetRecommendChannelListReq) GetNotClearHistory() bool {
	if m != nil {
		return m.NotClearHistory
	}
	return false
}

func (m *GetRecommendChannelListReq) GetNotCheckHistory() bool {
	if m != nil {
		return m.NotCheckHistory
	}
	return false
}

func (m *GetRecommendChannelListReq) GetNotSaveHistory() bool {
	if m != nil {
		return m.NotSaveHistory
	}
	return false
}

func (m *GetRecommendChannelListReq) GetReturnPgc() uint32 {
	if m != nil {
		return m.ReturnPgc
	}
	return 0
}

type GetRecommendChannelListResp struct {
	ChannelList          []*ChannelInfo                   `protobuf:"bytes,1,rep,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
	LoadMore             *GetRecommendChannelListLoadMore `protobuf:"bytes,2,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *GetRecommendChannelListResp) Reset()         { *m = GetRecommendChannelListResp{} }
func (m *GetRecommendChannelListResp) String() string { return proto.CompactTextString(m) }
func (*GetRecommendChannelListResp) ProtoMessage()    {}
func (*GetRecommendChannelListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{11}
}
func (m *GetRecommendChannelListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendChannelListResp.Unmarshal(m, b)
}
func (m *GetRecommendChannelListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendChannelListResp.Marshal(b, m, deterministic)
}
func (dst *GetRecommendChannelListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendChannelListResp.Merge(dst, src)
}
func (m *GetRecommendChannelListResp) XXX_Size() int {
	return xxx_messageInfo_GetRecommendChannelListResp.Size(m)
}
func (m *GetRecommendChannelListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendChannelListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendChannelListResp proto.InternalMessageInfo

func (m *GetRecommendChannelListResp) GetChannelList() []*ChannelInfo {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

func (m *GetRecommendChannelListResp) GetLoadMore() *GetRecommendChannelListLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type GetListByTabLoadMore struct {
	Newborn              *GetListByTabLoadMoreItem `protobuf:"bytes,1,opt,name=newborn,proto3" json:"newborn,omitempty"`
	Sink                 *GetListByTabLoadMoreItem `protobuf:"bytes,2,opt,name=sink,proto3" json:"sink,omitempty"`
	Big                  *GetListByTabLoadMoreItem `protobuf:"bytes,3,opt,name=big,proto3" json:"big,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetListByTabLoadMore) Reset()         { *m = GetListByTabLoadMore{} }
func (m *GetListByTabLoadMore) String() string { return proto.CompactTextString(m) }
func (*GetListByTabLoadMore) ProtoMessage()    {}
func (*GetListByTabLoadMore) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{12}
}
func (m *GetListByTabLoadMore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetListByTabLoadMore.Unmarshal(m, b)
}
func (m *GetListByTabLoadMore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetListByTabLoadMore.Marshal(b, m, deterministic)
}
func (dst *GetListByTabLoadMore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetListByTabLoadMore.Merge(dst, src)
}
func (m *GetListByTabLoadMore) XXX_Size() int {
	return xxx_messageInfo_GetListByTabLoadMore.Size(m)
}
func (m *GetListByTabLoadMore) XXX_DiscardUnknown() {
	xxx_messageInfo_GetListByTabLoadMore.DiscardUnknown(m)
}

var xxx_messageInfo_GetListByTabLoadMore proto.InternalMessageInfo

func (m *GetListByTabLoadMore) GetNewborn() *GetListByTabLoadMoreItem {
	if m != nil {
		return m.Newborn
	}
	return nil
}

func (m *GetListByTabLoadMore) GetSink() *GetListByTabLoadMoreItem {
	if m != nil {
		return m.Sink
	}
	return nil
}

func (m *GetListByTabLoadMore) GetBig() *GetListByTabLoadMoreItem {
	if m != nil {
		return m.Big
	}
	return nil
}

type GetListByTabLoadMoreItem struct {
	Cursor               uint64   `protobuf:"varint,1,opt,name=cursor,proto3" json:"cursor,omitempty"`
	LastValue            uint32   `protobuf:"varint,2,opt,name=last_value,json=lastValue,proto3" json:"last_value,omitempty"`
	LastIndex            uint32   `protobuf:"varint,3,opt,name=last_index,json=lastIndex,proto3" json:"last_index,omitempty"`
	LastCount            int64    `protobuf:"varint,4,opt,name=last_count,json=lastCount,proto3" json:"last_count,omitempty"`
	TheEnd               bool     `protobuf:"varint,5,opt,name=the_end,json=theEnd,proto3" json:"the_end,omitempty"`
	LastMatch            string   `protobuf:"bytes,6,opt,name=last_match,json=lastMatch,proto3" json:"last_match,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetListByTabLoadMoreItem) Reset()         { *m = GetListByTabLoadMoreItem{} }
func (m *GetListByTabLoadMoreItem) String() string { return proto.CompactTextString(m) }
func (*GetListByTabLoadMoreItem) ProtoMessage()    {}
func (*GetListByTabLoadMoreItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{13}
}
func (m *GetListByTabLoadMoreItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetListByTabLoadMoreItem.Unmarshal(m, b)
}
func (m *GetListByTabLoadMoreItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetListByTabLoadMoreItem.Marshal(b, m, deterministic)
}
func (dst *GetListByTabLoadMoreItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetListByTabLoadMoreItem.Merge(dst, src)
}
func (m *GetListByTabLoadMoreItem) XXX_Size() int {
	return xxx_messageInfo_GetListByTabLoadMoreItem.Size(m)
}
func (m *GetListByTabLoadMoreItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GetListByTabLoadMoreItem.DiscardUnknown(m)
}

var xxx_messageInfo_GetListByTabLoadMoreItem proto.InternalMessageInfo

func (m *GetListByTabLoadMoreItem) GetCursor() uint64 {
	if m != nil {
		return m.Cursor
	}
	return 0
}

func (m *GetListByTabLoadMoreItem) GetLastValue() uint32 {
	if m != nil {
		return m.LastValue
	}
	return 0
}

func (m *GetListByTabLoadMoreItem) GetLastIndex() uint32 {
	if m != nil {
		return m.LastIndex
	}
	return 0
}

func (m *GetListByTabLoadMoreItem) GetLastCount() int64 {
	if m != nil {
		return m.LastCount
	}
	return 0
}

func (m *GetListByTabLoadMoreItem) GetTheEnd() bool {
	if m != nil {
		return m.TheEnd
	}
	return false
}

func (m *GetListByTabLoadMoreItem) GetLastMatch() string {
	if m != nil {
		return m.LastMatch
	}
	return ""
}

type GetRecommendChannelListByTabReq struct {
	Uid                  uint32                `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Limit                uint32                `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	TabId                uint32                `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	LoadMore             *GetListByTabLoadMore `protobuf:"bytes,4,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	ExceptChannelId      []uint32              `protobuf:"varint,5,rep,packed,name=except_channel_id,json=exceptChannelId,proto3" json:"except_channel_id,omitempty"`
	ScanParam            string                `protobuf:"bytes,6,opt,name=scan_param,json=scanParam,proto3" json:"scan_param,omitempty"`
	BlockOptions         []*BlockOption        `protobuf:"bytes,7,rep,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"`
	NotClearHistory      bool                  `protobuf:"varint,8,opt,name=not_clear_history,json=notClearHistory,proto3" json:"not_clear_history,omitempty"`
	NotCheckHistory      bool                  `protobuf:"varint,9,opt,name=not_check_history,json=notCheckHistory,proto3" json:"not_check_history,omitempty"`
	NotSaveHistory       bool                  `protobuf:"varint,10,opt,name=not_save_history,json=notSaveHistory,proto3" json:"not_save_history,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetRecommendChannelListByTabReq) Reset()         { *m = GetRecommendChannelListByTabReq{} }
func (m *GetRecommendChannelListByTabReq) String() string { return proto.CompactTextString(m) }
func (*GetRecommendChannelListByTabReq) ProtoMessage()    {}
func (*GetRecommendChannelListByTabReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{14}
}
func (m *GetRecommendChannelListByTabReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendChannelListByTabReq.Unmarshal(m, b)
}
func (m *GetRecommendChannelListByTabReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendChannelListByTabReq.Marshal(b, m, deterministic)
}
func (dst *GetRecommendChannelListByTabReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendChannelListByTabReq.Merge(dst, src)
}
func (m *GetRecommendChannelListByTabReq) XXX_Size() int {
	return xxx_messageInfo_GetRecommendChannelListByTabReq.Size(m)
}
func (m *GetRecommendChannelListByTabReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendChannelListByTabReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendChannelListByTabReq proto.InternalMessageInfo

func (m *GetRecommendChannelListByTabReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRecommendChannelListByTabReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetRecommendChannelListByTabReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetRecommendChannelListByTabReq) GetLoadMore() *GetListByTabLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

func (m *GetRecommendChannelListByTabReq) GetExceptChannelId() []uint32 {
	if m != nil {
		return m.ExceptChannelId
	}
	return nil
}

func (m *GetRecommendChannelListByTabReq) GetScanParam() string {
	if m != nil {
		return m.ScanParam
	}
	return ""
}

func (m *GetRecommendChannelListByTabReq) GetBlockOptions() []*BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

func (m *GetRecommendChannelListByTabReq) GetNotClearHistory() bool {
	if m != nil {
		return m.NotClearHistory
	}
	return false
}

func (m *GetRecommendChannelListByTabReq) GetNotCheckHistory() bool {
	if m != nil {
		return m.NotCheckHistory
	}
	return false
}

func (m *GetRecommendChannelListByTabReq) GetNotSaveHistory() bool {
	if m != nil {
		return m.NotSaveHistory
	}
	return false
}

type GetRecommendChannelListByTabResp struct {
	ChannelList          []*ChannelInfo        `protobuf:"bytes,1,rep,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
	LoadMore             *GetListByTabLoadMore `protobuf:"bytes,2,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetRecommendChannelListByTabResp) Reset()         { *m = GetRecommendChannelListByTabResp{} }
func (m *GetRecommendChannelListByTabResp) String() string { return proto.CompactTextString(m) }
func (*GetRecommendChannelListByTabResp) ProtoMessage()    {}
func (*GetRecommendChannelListByTabResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{15}
}
func (m *GetRecommendChannelListByTabResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendChannelListByTabResp.Unmarshal(m, b)
}
func (m *GetRecommendChannelListByTabResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendChannelListByTabResp.Marshal(b, m, deterministic)
}
func (dst *GetRecommendChannelListByTabResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendChannelListByTabResp.Merge(dst, src)
}
func (m *GetRecommendChannelListByTabResp) XXX_Size() int {
	return xxx_messageInfo_GetRecommendChannelListByTabResp.Size(m)
}
func (m *GetRecommendChannelListByTabResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendChannelListByTabResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendChannelListByTabResp proto.InternalMessageInfo

func (m *GetRecommendChannelListByTabResp) GetChannelList() []*ChannelInfo {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

func (m *GetRecommendChannelListByTabResp) GetLoadMore() *GetListByTabLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type GetChannelByIdsReq struct {
	Ids                  []uint32             `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	Types                []ChannelDisplayType `protobuf:"varint,2,rep,packed,name=types,proto3,enum=topic_channel.channel.ChannelDisplayType" json:"types,omitempty"`
	ReturnAll            bool                 `protobuf:"varint,3,opt,name=return_all,json=returnAll,proto3" json:"return_all,omitempty"`
	Source               string               `protobuf:"bytes,4,opt,name=source,proto3" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetChannelByIdsReq) Reset()         { *m = GetChannelByIdsReq{} }
func (m *GetChannelByIdsReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelByIdsReq) ProtoMessage()    {}
func (*GetChannelByIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{16}
}
func (m *GetChannelByIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelByIdsReq.Unmarshal(m, b)
}
func (m *GetChannelByIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelByIdsReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelByIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelByIdsReq.Merge(dst, src)
}
func (m *GetChannelByIdsReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelByIdsReq.Size(m)
}
func (m *GetChannelByIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelByIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelByIdsReq proto.InternalMessageInfo

func (m *GetChannelByIdsReq) GetIds() []uint32 {
	if m != nil {
		return m.Ids
	}
	return nil
}

func (m *GetChannelByIdsReq) GetTypes() []ChannelDisplayType {
	if m != nil {
		return m.Types
	}
	return nil
}

func (m *GetChannelByIdsReq) GetReturnAll() bool {
	if m != nil {
		return m.ReturnAll
	}
	return false
}

func (m *GetChannelByIdsReq) GetSource() string {
	if m != nil {
		return m.Source
	}
	return ""
}

type GetChannelByIdsResp struct {
	Info                 []*ChannelInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetChannelByIdsResp) Reset()         { *m = GetChannelByIdsResp{} }
func (m *GetChannelByIdsResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelByIdsResp) ProtoMessage()    {}
func (*GetChannelByIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{17}
}
func (m *GetChannelByIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelByIdsResp.Unmarshal(m, b)
}
func (m *GetChannelByIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelByIdsResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelByIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelByIdsResp.Merge(dst, src)
}
func (m *GetChannelByIdsResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelByIdsResp.Size(m)
}
func (m *GetChannelByIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelByIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelByIdsResp proto.InternalMessageInfo

func (m *GetChannelByIdsResp) GetInfo() []*ChannelInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type DismissTabReq struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DismissTabReq) Reset()         { *m = DismissTabReq{} }
func (m *DismissTabReq) String() string { return proto.CompactTextString(m) }
func (*DismissTabReq) ProtoMessage()    {}
func (*DismissTabReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{18}
}
func (m *DismissTabReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DismissTabReq.Unmarshal(m, b)
}
func (m *DismissTabReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DismissTabReq.Marshal(b, m, deterministic)
}
func (dst *DismissTabReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DismissTabReq.Merge(dst, src)
}
func (m *DismissTabReq) XXX_Size() int {
	return xxx_messageInfo_DismissTabReq.Size(m)
}
func (m *DismissTabReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DismissTabReq.DiscardUnknown(m)
}

var xxx_messageInfo_DismissTabReq proto.InternalMessageInfo

func (m *DismissTabReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type DismissTabResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DismissTabResp) Reset()         { *m = DismissTabResp{} }
func (m *DismissTabResp) String() string { return proto.CompactTextString(m) }
func (*DismissTabResp) ProtoMessage()    {}
func (*DismissTabResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{19}
}
func (m *DismissTabResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DismissTabResp.Unmarshal(m, b)
}
func (m *DismissTabResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DismissTabResp.Marshal(b, m, deterministic)
}
func (dst *DismissTabResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DismissTabResp.Merge(dst, src)
}
func (m *DismissTabResp) XXX_Size() int {
	return xxx_messageInfo_DismissTabResp.Size(m)
}
func (m *DismissTabResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DismissTabResp.DiscardUnknown(m)
}

var xxx_messageInfo_DismissTabResp proto.InternalMessageInfo

type KeepChannelAliveReq struct {
	ChannelId            uint32          `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Status               KeepAliveStatus `protobuf:"varint,2,opt,name=status,proto3,enum=topic_channel.channel.KeepAliveStatus" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *KeepChannelAliveReq) Reset()         { *m = KeepChannelAliveReq{} }
func (m *KeepChannelAliveReq) String() string { return proto.CompactTextString(m) }
func (*KeepChannelAliveReq) ProtoMessage()    {}
func (*KeepChannelAliveReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{20}
}
func (m *KeepChannelAliveReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KeepChannelAliveReq.Unmarshal(m, b)
}
func (m *KeepChannelAliveReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KeepChannelAliveReq.Marshal(b, m, deterministic)
}
func (dst *KeepChannelAliveReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KeepChannelAliveReq.Merge(dst, src)
}
func (m *KeepChannelAliveReq) XXX_Size() int {
	return xxx_messageInfo_KeepChannelAliveReq.Size(m)
}
func (m *KeepChannelAliveReq) XXX_DiscardUnknown() {
	xxx_messageInfo_KeepChannelAliveReq.DiscardUnknown(m)
}

var xxx_messageInfo_KeepChannelAliveReq proto.InternalMessageInfo

func (m *KeepChannelAliveReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *KeepChannelAliveReq) GetStatus() KeepAliveStatus {
	if m != nil {
		return m.Status
	}
	return KeepAliveStatus_ALIVE
}

type KeepChannelAliveResp struct {
	IsAlive              bool     `protobuf:"varint,1,opt,name=is_alive,json=isAlive,proto3" json:"is_alive,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KeepChannelAliveResp) Reset()         { *m = KeepChannelAliveResp{} }
func (m *KeepChannelAliveResp) String() string { return proto.CompactTextString(m) }
func (*KeepChannelAliveResp) ProtoMessage()    {}
func (*KeepChannelAliveResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{21}
}
func (m *KeepChannelAliveResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KeepChannelAliveResp.Unmarshal(m, b)
}
func (m *KeepChannelAliveResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KeepChannelAliveResp.Marshal(b, m, deterministic)
}
func (dst *KeepChannelAliveResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KeepChannelAliveResp.Merge(dst, src)
}
func (m *KeepChannelAliveResp) XXX_Size() int {
	return xxx_messageInfo_KeepChannelAliveResp.Size(m)
}
func (m *KeepChannelAliveResp) XXX_DiscardUnknown() {
	xxx_messageInfo_KeepChannelAliveResp.DiscardUnknown(m)
}

var xxx_messageInfo_KeepChannelAliveResp proto.InternalMessageInfo

func (m *KeepChannelAliveResp) GetIsAlive() bool {
	if m != nil {
		return m.IsAlive
	}
	return false
}

type DisappearChannelReq struct {
	ClientId             string                              `protobuf:"bytes,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	AcquireDuration      uint64                              `protobuf:"varint,2,opt,name=acquire_duration,json=acquireDuration,proto3" json:"acquire_duration,omitempty"`
	TimeoutEvent         *DisappearChannelReq_Timeout        `protobuf:"bytes,10,opt,name=timeout_event,json=timeoutEvent,proto3" json:"timeout_event,omitempty"`
	KeepaliveEvent       *DisappearChannelReq_Keepalive      `protobuf:"bytes,11,opt,name=keepalive_event,json=keepaliveEvent,proto3" json:"keepalive_event,omitempty"`
	ReleaseTimeoutEvent  *DisappearChannelReq_ReleaseTimeout `protobuf:"bytes,12,opt,name=release_timeout_event,json=releaseTimeoutEvent,proto3" json:"release_timeout_event,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *DisappearChannelReq) Reset()         { *m = DisappearChannelReq{} }
func (m *DisappearChannelReq) String() string { return proto.CompactTextString(m) }
func (*DisappearChannelReq) ProtoMessage()    {}
func (*DisappearChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{22}
}
func (m *DisappearChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisappearChannelReq.Unmarshal(m, b)
}
func (m *DisappearChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisappearChannelReq.Marshal(b, m, deterministic)
}
func (dst *DisappearChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisappearChannelReq.Merge(dst, src)
}
func (m *DisappearChannelReq) XXX_Size() int {
	return xxx_messageInfo_DisappearChannelReq.Size(m)
}
func (m *DisappearChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DisappearChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_DisappearChannelReq proto.InternalMessageInfo

func (m *DisappearChannelReq) GetClientId() string {
	if m != nil {
		return m.ClientId
	}
	return ""
}

func (m *DisappearChannelReq) GetAcquireDuration() uint64 {
	if m != nil {
		return m.AcquireDuration
	}
	return 0
}

func (m *DisappearChannelReq) GetTimeoutEvent() *DisappearChannelReq_Timeout {
	if m != nil {
		return m.TimeoutEvent
	}
	return nil
}

func (m *DisappearChannelReq) GetKeepaliveEvent() *DisappearChannelReq_Keepalive {
	if m != nil {
		return m.KeepaliveEvent
	}
	return nil
}

func (m *DisappearChannelReq) GetReleaseTimeoutEvent() *DisappearChannelReq_ReleaseTimeout {
	if m != nil {
		return m.ReleaseTimeoutEvent
	}
	return nil
}

type DisappearChannelReq_Timeout struct {
	TimeoutDuration      uint64   `protobuf:"varint,1,opt,name=timeout_duration,json=timeoutDuration,proto3" json:"timeout_duration,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisappearChannelReq_Timeout) Reset()         { *m = DisappearChannelReq_Timeout{} }
func (m *DisappearChannelReq_Timeout) String() string { return proto.CompactTextString(m) }
func (*DisappearChannelReq_Timeout) ProtoMessage()    {}
func (*DisappearChannelReq_Timeout) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{22, 0}
}
func (m *DisappearChannelReq_Timeout) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisappearChannelReq_Timeout.Unmarshal(m, b)
}
func (m *DisappearChannelReq_Timeout) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisappearChannelReq_Timeout.Marshal(b, m, deterministic)
}
func (dst *DisappearChannelReq_Timeout) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisappearChannelReq_Timeout.Merge(dst, src)
}
func (m *DisappearChannelReq_Timeout) XXX_Size() int {
	return xxx_messageInfo_DisappearChannelReq_Timeout.Size(m)
}
func (m *DisappearChannelReq_Timeout) XXX_DiscardUnknown() {
	xxx_messageInfo_DisappearChannelReq_Timeout.DiscardUnknown(m)
}

var xxx_messageInfo_DisappearChannelReq_Timeout proto.InternalMessageInfo

func (m *DisappearChannelReq_Timeout) GetTimeoutDuration() uint64 {
	if m != nil {
		return m.TimeoutDuration
	}
	return 0
}

type DisappearChannelReq_Keepalive struct {
	KeepaliveDuration    uint64   `protobuf:"varint,1,opt,name=Keepalive_duration,json=KeepaliveDuration,proto3" json:"Keepalive_duration,omitempty"`
	MemberCount          uint32   `protobuf:"varint,2,opt,name=member_count,json=memberCount,proto3" json:"member_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisappearChannelReq_Keepalive) Reset()         { *m = DisappearChannelReq_Keepalive{} }
func (m *DisappearChannelReq_Keepalive) String() string { return proto.CompactTextString(m) }
func (*DisappearChannelReq_Keepalive) ProtoMessage()    {}
func (*DisappearChannelReq_Keepalive) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{22, 1}
}
func (m *DisappearChannelReq_Keepalive) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisappearChannelReq_Keepalive.Unmarshal(m, b)
}
func (m *DisappearChannelReq_Keepalive) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisappearChannelReq_Keepalive.Marshal(b, m, deterministic)
}
func (dst *DisappearChannelReq_Keepalive) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisappearChannelReq_Keepalive.Merge(dst, src)
}
func (m *DisappearChannelReq_Keepalive) XXX_Size() int {
	return xxx_messageInfo_DisappearChannelReq_Keepalive.Size(m)
}
func (m *DisappearChannelReq_Keepalive) XXX_DiscardUnknown() {
	xxx_messageInfo_DisappearChannelReq_Keepalive.DiscardUnknown(m)
}

var xxx_messageInfo_DisappearChannelReq_Keepalive proto.InternalMessageInfo

func (m *DisappearChannelReq_Keepalive) GetKeepaliveDuration() uint64 {
	if m != nil {
		return m.KeepaliveDuration
	}
	return 0
}

func (m *DisappearChannelReq_Keepalive) GetMemberCount() uint32 {
	if m != nil {
		return m.MemberCount
	}
	return 0
}

type DisappearChannelReq_ReleaseTimeout struct {
	TimeoutDuration      uint64   `protobuf:"varint,1,opt,name=timeout_duration,json=timeoutDuration,proto3" json:"timeout_duration,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisappearChannelReq_ReleaseTimeout) Reset()         { *m = DisappearChannelReq_ReleaseTimeout{} }
func (m *DisappearChannelReq_ReleaseTimeout) String() string { return proto.CompactTextString(m) }
func (*DisappearChannelReq_ReleaseTimeout) ProtoMessage()    {}
func (*DisappearChannelReq_ReleaseTimeout) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{22, 2}
}
func (m *DisappearChannelReq_ReleaseTimeout) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisappearChannelReq_ReleaseTimeout.Unmarshal(m, b)
}
func (m *DisappearChannelReq_ReleaseTimeout) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisappearChannelReq_ReleaseTimeout.Marshal(b, m, deterministic)
}
func (dst *DisappearChannelReq_ReleaseTimeout) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisappearChannelReq_ReleaseTimeout.Merge(dst, src)
}
func (m *DisappearChannelReq_ReleaseTimeout) XXX_Size() int {
	return xxx_messageInfo_DisappearChannelReq_ReleaseTimeout.Size(m)
}
func (m *DisappearChannelReq_ReleaseTimeout) XXX_DiscardUnknown() {
	xxx_messageInfo_DisappearChannelReq_ReleaseTimeout.DiscardUnknown(m)
}

var xxx_messageInfo_DisappearChannelReq_ReleaseTimeout proto.InternalMessageInfo

func (m *DisappearChannelReq_ReleaseTimeout) GetTimeoutDuration() uint64 {
	if m != nil {
		return m.TimeoutDuration
	}
	return 0
}

type DisappearChannelResp struct {
	ChannelIds           []uint32 `protobuf:"varint,1,rep,packed,name=channel_ids,json=channelIds,proto3" json:"channel_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisappearChannelResp) Reset()         { *m = DisappearChannelResp{} }
func (m *DisappearChannelResp) String() string { return proto.CompactTextString(m) }
func (*DisappearChannelResp) ProtoMessage()    {}
func (*DisappearChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{23}
}
func (m *DisappearChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisappearChannelResp.Unmarshal(m, b)
}
func (m *DisappearChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisappearChannelResp.Marshal(b, m, deterministic)
}
func (dst *DisappearChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisappearChannelResp.Merge(dst, src)
}
func (m *DisappearChannelResp) XXX_Size() int {
	return xxx_messageInfo_DisappearChannelResp.Size(m)
}
func (m *DisappearChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DisappearChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_DisappearChannelResp proto.InternalMessageInfo

func (m *DisappearChannelResp) GetChannelIds() []uint32 {
	if m != nil {
		return m.ChannelIds
	}
	return nil
}

type TabConfigure struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	ScanRule             []string `protobuf:"bytes,2,rep,name=scan_rule,json=scanRule,proto3" json:"scan_rule,omitempty"`
	SinkDuration         uint64   `protobuf:"varint,3,opt,name=sink_duration,json=sinkDuration,proto3" json:"sink_duration,omitempty"`
	DisappearDuration    uint64   `protobuf:"varint,4,opt,name=disappear_duration,json=disappearDuration,proto3" json:"disappear_duration,omitempty"`
	BigPoolBoundaryValue uint32   `protobuf:"varint,5,opt,name=big_pool_boundary_value,json=bigPoolBoundaryValue,proto3" json:"big_pool_boundary_value,omitempty"`
	RandSetBoundaryValue uint32   `protobuf:"varint,6,opt,name=rand_set_boundary_value,json=randSetBoundaryValue,proto3" json:"rand_set_boundary_value,omitempty"`
	ModifyAt             int64    `protobuf:"varint,7,opt,name=modify_at,json=modifyAt,proto3" json:"modify_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TabConfigure) Reset()         { *m = TabConfigure{} }
func (m *TabConfigure) String() string { return proto.CompactTextString(m) }
func (*TabConfigure) ProtoMessage()    {}
func (*TabConfigure) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{24}
}
func (m *TabConfigure) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TabConfigure.Unmarshal(m, b)
}
func (m *TabConfigure) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TabConfigure.Marshal(b, m, deterministic)
}
func (dst *TabConfigure) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TabConfigure.Merge(dst, src)
}
func (m *TabConfigure) XXX_Size() int {
	return xxx_messageInfo_TabConfigure.Size(m)
}
func (m *TabConfigure) XXX_DiscardUnknown() {
	xxx_messageInfo_TabConfigure.DiscardUnknown(m)
}

var xxx_messageInfo_TabConfigure proto.InternalMessageInfo

func (m *TabConfigure) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *TabConfigure) GetScanRule() []string {
	if m != nil {
		return m.ScanRule
	}
	return nil
}

func (m *TabConfigure) GetSinkDuration() uint64 {
	if m != nil {
		return m.SinkDuration
	}
	return 0
}

func (m *TabConfigure) GetDisappearDuration() uint64 {
	if m != nil {
		return m.DisappearDuration
	}
	return 0
}

func (m *TabConfigure) GetBigPoolBoundaryValue() uint32 {
	if m != nil {
		return m.BigPoolBoundaryValue
	}
	return 0
}

func (m *TabConfigure) GetRandSetBoundaryValue() uint32 {
	if m != nil {
		return m.RandSetBoundaryValue
	}
	return 0
}

func (m *TabConfigure) GetModifyAt() int64 {
	if m != nil {
		return m.ModifyAt
	}
	return 0
}

type SetTabConfigureReq struct {
	Config               *TabConfigure `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetTabConfigureReq) Reset()         { *m = SetTabConfigureReq{} }
func (m *SetTabConfigureReq) String() string { return proto.CompactTextString(m) }
func (*SetTabConfigureReq) ProtoMessage()    {}
func (*SetTabConfigureReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{25}
}
func (m *SetTabConfigureReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetTabConfigureReq.Unmarshal(m, b)
}
func (m *SetTabConfigureReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetTabConfigureReq.Marshal(b, m, deterministic)
}
func (dst *SetTabConfigureReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetTabConfigureReq.Merge(dst, src)
}
func (m *SetTabConfigureReq) XXX_Size() int {
	return xxx_messageInfo_SetTabConfigureReq.Size(m)
}
func (m *SetTabConfigureReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetTabConfigureReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetTabConfigureReq proto.InternalMessageInfo

func (m *SetTabConfigureReq) GetConfig() *TabConfigure {
	if m != nil {
		return m.Config
	}
	return nil
}

type SetTabConfigureResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetTabConfigureResp) Reset()         { *m = SetTabConfigureResp{} }
func (m *SetTabConfigureResp) String() string { return proto.CompactTextString(m) }
func (*SetTabConfigureResp) ProtoMessage()    {}
func (*SetTabConfigureResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{26}
}
func (m *SetTabConfigureResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetTabConfigureResp.Unmarshal(m, b)
}
func (m *SetTabConfigureResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetTabConfigureResp.Marshal(b, m, deterministic)
}
func (dst *SetTabConfigureResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetTabConfigureResp.Merge(dst, src)
}
func (m *SetTabConfigureResp) XXX_Size() int {
	return xxx_messageInfo_SetTabConfigureResp.Size(m)
}
func (m *SetTabConfigureResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetTabConfigureResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetTabConfigureResp proto.InternalMessageInfo

type GetOnlineInfoReq struct {
	OnlineUserCount      uint32   `protobuf:"varint,1,opt,name=online_user_count,json=onlineUserCount,proto3" json:"online_user_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOnlineInfoReq) Reset()         { *m = GetOnlineInfoReq{} }
func (m *GetOnlineInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetOnlineInfoReq) ProtoMessage()    {}
func (*GetOnlineInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{27}
}
func (m *GetOnlineInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOnlineInfoReq.Unmarshal(m, b)
}
func (m *GetOnlineInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOnlineInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetOnlineInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOnlineInfoReq.Merge(dst, src)
}
func (m *GetOnlineInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetOnlineInfoReq.Size(m)
}
func (m *GetOnlineInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOnlineInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOnlineInfoReq proto.InternalMessageInfo

func (m *GetOnlineInfoReq) GetOnlineUserCount() uint32 {
	if m != nil {
		return m.OnlineUserCount
	}
	return 0
}

type GetOnlineInfoResp struct {
	RoomCount            uint32   `protobuf:"varint,1,opt,name=room_count,json=roomCount,proto3" json:"room_count,omitempty"`
	OnlineUserList       []uint32 `protobuf:"varint,2,rep,packed,name=online_user_list,json=onlineUserList,proto3" json:"online_user_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOnlineInfoResp) Reset()         { *m = GetOnlineInfoResp{} }
func (m *GetOnlineInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetOnlineInfoResp) ProtoMessage()    {}
func (*GetOnlineInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{28}
}
func (m *GetOnlineInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOnlineInfoResp.Unmarshal(m, b)
}
func (m *GetOnlineInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOnlineInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetOnlineInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOnlineInfoResp.Merge(dst, src)
}
func (m *GetOnlineInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetOnlineInfoResp.Size(m)
}
func (m *GetOnlineInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOnlineInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOnlineInfoResp proto.InternalMessageInfo

func (m *GetOnlineInfoResp) GetRoomCount() uint32 {
	if m != nil {
		return m.RoomCount
	}
	return 0
}

func (m *GetOnlineInfoResp) GetOnlineUserList() []uint32 {
	if m != nil {
		return m.OnlineUserList
	}
	return nil
}

type FreezeChannelReq struct {
	ChannelIdList        []uint32 `protobuf:"varint,1,rep,packed,name=channel_id_list,json=channelIdList,proto3" json:"channel_id_list,omitempty"`
	FreezeTime           int64    `protobuf:"varint,2,opt,name=freeze_time,json=freezeTime,proto3" json:"freeze_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FreezeChannelReq) Reset()         { *m = FreezeChannelReq{} }
func (m *FreezeChannelReq) String() string { return proto.CompactTextString(m) }
func (*FreezeChannelReq) ProtoMessage()    {}
func (*FreezeChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{29}
}
func (m *FreezeChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FreezeChannelReq.Unmarshal(m, b)
}
func (m *FreezeChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FreezeChannelReq.Marshal(b, m, deterministic)
}
func (dst *FreezeChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FreezeChannelReq.Merge(dst, src)
}
func (m *FreezeChannelReq) XXX_Size() int {
	return xxx_messageInfo_FreezeChannelReq.Size(m)
}
func (m *FreezeChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_FreezeChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_FreezeChannelReq proto.InternalMessageInfo

func (m *FreezeChannelReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

func (m *FreezeChannelReq) GetFreezeTime() int64 {
	if m != nil {
		return m.FreezeTime
	}
	return 0
}

type FreezeChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FreezeChannelResp) Reset()         { *m = FreezeChannelResp{} }
func (m *FreezeChannelResp) String() string { return proto.CompactTextString(m) }
func (*FreezeChannelResp) ProtoMessage()    {}
func (*FreezeChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{30}
}
func (m *FreezeChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FreezeChannelResp.Unmarshal(m, b)
}
func (m *FreezeChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FreezeChannelResp.Marshal(b, m, deterministic)
}
func (dst *FreezeChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FreezeChannelResp.Merge(dst, src)
}
func (m *FreezeChannelResp) XXX_Size() int {
	return xxx_messageInfo_FreezeChannelResp.Size(m)
}
func (m *FreezeChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_FreezeChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_FreezeChannelResp proto.InternalMessageInfo

type UnfreezeChannelReq struct {
	ChannelIdList        []uint32 `protobuf:"varint,1,rep,packed,name=channel_id_list,json=channelIdList,proto3" json:"channel_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnfreezeChannelReq) Reset()         { *m = UnfreezeChannelReq{} }
func (m *UnfreezeChannelReq) String() string { return proto.CompactTextString(m) }
func (*UnfreezeChannelReq) ProtoMessage()    {}
func (*UnfreezeChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{31}
}
func (m *UnfreezeChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnfreezeChannelReq.Unmarshal(m, b)
}
func (m *UnfreezeChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnfreezeChannelReq.Marshal(b, m, deterministic)
}
func (dst *UnfreezeChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnfreezeChannelReq.Merge(dst, src)
}
func (m *UnfreezeChannelReq) XXX_Size() int {
	return xxx_messageInfo_UnfreezeChannelReq.Size(m)
}
func (m *UnfreezeChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UnfreezeChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_UnfreezeChannelReq proto.InternalMessageInfo

func (m *UnfreezeChannelReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type UnfreezeChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnfreezeChannelResp) Reset()         { *m = UnfreezeChannelResp{} }
func (m *UnfreezeChannelResp) String() string { return proto.CompactTextString(m) }
func (*UnfreezeChannelResp) ProtoMessage()    {}
func (*UnfreezeChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{32}
}
func (m *UnfreezeChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnfreezeChannelResp.Unmarshal(m, b)
}
func (m *UnfreezeChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnfreezeChannelResp.Marshal(b, m, deterministic)
}
func (dst *UnfreezeChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnfreezeChannelResp.Merge(dst, src)
}
func (m *UnfreezeChannelResp) XXX_Size() int {
	return xxx_messageInfo_UnfreezeChannelResp.Size(m)
}
func (m *UnfreezeChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UnfreezeChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_UnfreezeChannelResp proto.InternalMessageInfo

type GetChannelFreezeInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelFreezeInfoReq) Reset()         { *m = GetChannelFreezeInfoReq{} }
func (m *GetChannelFreezeInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelFreezeInfoReq) ProtoMessage()    {}
func (*GetChannelFreezeInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{33}
}
func (m *GetChannelFreezeInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelFreezeInfoReq.Unmarshal(m, b)
}
func (m *GetChannelFreezeInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelFreezeInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelFreezeInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelFreezeInfoReq.Merge(dst, src)
}
func (m *GetChannelFreezeInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelFreezeInfoReq.Size(m)
}
func (m *GetChannelFreezeInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelFreezeInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelFreezeInfoReq proto.InternalMessageInfo

func (m *GetChannelFreezeInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelFreezeInfoResp struct {
	FreezeTime           int64    `protobuf:"varint,1,opt,name=freeze_time,json=freezeTime,proto3" json:"freeze_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelFreezeInfoResp) Reset()         { *m = GetChannelFreezeInfoResp{} }
func (m *GetChannelFreezeInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelFreezeInfoResp) ProtoMessage()    {}
func (*GetChannelFreezeInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{34}
}
func (m *GetChannelFreezeInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelFreezeInfoResp.Unmarshal(m, b)
}
func (m *GetChannelFreezeInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelFreezeInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelFreezeInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelFreezeInfoResp.Merge(dst, src)
}
func (m *GetChannelFreezeInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelFreezeInfoResp.Size(m)
}
func (m *GetChannelFreezeInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelFreezeInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelFreezeInfoResp proto.InternalMessageInfo

func (m *GetChannelFreezeInfoResp) GetFreezeTime() int64 {
	if m != nil {
		return m.FreezeTime
	}
	return 0
}

type SetExtraHistoryReq struct {
	Key                  string   `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value                string   `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	ExpireAfter          int64    `protobuf:"varint,3,opt,name=expire_after,json=expireAfter,proto3" json:"expire_after,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetExtraHistoryReq) Reset()         { *m = SetExtraHistoryReq{} }
func (m *SetExtraHistoryReq) String() string { return proto.CompactTextString(m) }
func (*SetExtraHistoryReq) ProtoMessage()    {}
func (*SetExtraHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{35}
}
func (m *SetExtraHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetExtraHistoryReq.Unmarshal(m, b)
}
func (m *SetExtraHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetExtraHistoryReq.Marshal(b, m, deterministic)
}
func (dst *SetExtraHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetExtraHistoryReq.Merge(dst, src)
}
func (m *SetExtraHistoryReq) XXX_Size() int {
	return xxx_messageInfo_SetExtraHistoryReq.Size(m)
}
func (m *SetExtraHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetExtraHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetExtraHistoryReq proto.InternalMessageInfo

func (m *SetExtraHistoryReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *SetExtraHistoryReq) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

func (m *SetExtraHistoryReq) GetExpireAfter() int64 {
	if m != nil {
		return m.ExpireAfter
	}
	return 0
}

type SetExtraHistoryResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetExtraHistoryResp) Reset()         { *m = SetExtraHistoryResp{} }
func (m *SetExtraHistoryResp) String() string { return proto.CompactTextString(m) }
func (*SetExtraHistoryResp) ProtoMessage()    {}
func (*SetExtraHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{36}
}
func (m *SetExtraHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetExtraHistoryResp.Unmarshal(m, b)
}
func (m *SetExtraHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetExtraHistoryResp.Marshal(b, m, deterministic)
}
func (dst *SetExtraHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetExtraHistoryResp.Merge(dst, src)
}
func (m *SetExtraHistoryResp) XXX_Size() int {
	return xxx_messageInfo_SetExtraHistoryResp.Size(m)
}
func (m *SetExtraHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetExtraHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetExtraHistoryResp proto.InternalMessageInfo

type GetExtraHistoryReq struct {
	Key                  string   `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetExtraHistoryReq) Reset()         { *m = GetExtraHistoryReq{} }
func (m *GetExtraHistoryReq) String() string { return proto.CompactTextString(m) }
func (*GetExtraHistoryReq) ProtoMessage()    {}
func (*GetExtraHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{37}
}
func (m *GetExtraHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExtraHistoryReq.Unmarshal(m, b)
}
func (m *GetExtraHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExtraHistoryReq.Marshal(b, m, deterministic)
}
func (dst *GetExtraHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExtraHistoryReq.Merge(dst, src)
}
func (m *GetExtraHistoryReq) XXX_Size() int {
	return xxx_messageInfo_GetExtraHistoryReq.Size(m)
}
func (m *GetExtraHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExtraHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetExtraHistoryReq proto.InternalMessageInfo

func (m *GetExtraHistoryReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

type GetExtraHistoryResp struct {
	Value                string   `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetExtraHistoryResp) Reset()         { *m = GetExtraHistoryResp{} }
func (m *GetExtraHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetExtraHistoryResp) ProtoMessage()    {}
func (*GetExtraHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{38}
}
func (m *GetExtraHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExtraHistoryResp.Unmarshal(m, b)
}
func (m *GetExtraHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExtraHistoryResp.Marshal(b, m, deterministic)
}
func (dst *GetExtraHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExtraHistoryResp.Merge(dst, src)
}
func (m *GetExtraHistoryResp) XXX_Size() int {
	return xxx_messageInfo_GetExtraHistoryResp.Size(m)
}
func (m *GetExtraHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExtraHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetExtraHistoryResp proto.InternalMessageInfo

func (m *GetExtraHistoryResp) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

type GetChannelPlayModelReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelPlayModelReq) Reset()         { *m = GetChannelPlayModelReq{} }
func (m *GetChannelPlayModelReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelPlayModelReq) ProtoMessage()    {}
func (*GetChannelPlayModelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{39}
}
func (m *GetChannelPlayModelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelPlayModelReq.Unmarshal(m, b)
}
func (m *GetChannelPlayModelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelPlayModelReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelPlayModelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelPlayModelReq.Merge(dst, src)
}
func (m *GetChannelPlayModelReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelPlayModelReq.Size(m)
}
func (m *GetChannelPlayModelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelPlayModelReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelPlayModelReq proto.InternalMessageInfo

func (m *GetChannelPlayModelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelPlayModelResp struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelPlayModelResp) Reset()         { *m = GetChannelPlayModelResp{} }
func (m *GetChannelPlayModelResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelPlayModelResp) ProtoMessage()    {}
func (*GetChannelPlayModelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{40}
}
func (m *GetChannelPlayModelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelPlayModelResp.Unmarshal(m, b)
}
func (m *GetChannelPlayModelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelPlayModelResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelPlayModelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelPlayModelResp.Merge(dst, src)
}
func (m *GetChannelPlayModelResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelPlayModelResp.Size(m)
}
func (m *GetChannelPlayModelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelPlayModelResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelPlayModelResp proto.InternalMessageInfo

func (m *GetChannelPlayModelResp) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type SwitchChannelTabMqReq struct {
	TabId                uint32       `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TabName              string       `protobuf:"bytes,3,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	Source               Source       `protobuf:"varint,4,opt,name=source,proto3,enum=topic_channel.channel.Source" json:"source,omitempty"`
	HomePageType         HomePageType `protobuf:"varint,5,opt,name=home_page_type,json=homePageType,proto3,enum=topic_channel.channel.HomePageType" json:"home_page_type,omitempty"`
	Uid                  uint32       `protobuf:"varint,6,opt,name=uid,proto3" json:"uid,omitempty"`
	OldTabId             uint32       `protobuf:"varint,7,opt,name=old_tab_id,json=oldTabId,proto3" json:"old_tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SwitchChannelTabMqReq) Reset()         { *m = SwitchChannelTabMqReq{} }
func (m *SwitchChannelTabMqReq) String() string { return proto.CompactTextString(m) }
func (*SwitchChannelTabMqReq) ProtoMessage()    {}
func (*SwitchChannelTabMqReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{41}
}
func (m *SwitchChannelTabMqReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchChannelTabMqReq.Unmarshal(m, b)
}
func (m *SwitchChannelTabMqReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchChannelTabMqReq.Marshal(b, m, deterministic)
}
func (dst *SwitchChannelTabMqReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchChannelTabMqReq.Merge(dst, src)
}
func (m *SwitchChannelTabMqReq) XXX_Size() int {
	return xxx_messageInfo_SwitchChannelTabMqReq.Size(m)
}
func (m *SwitchChannelTabMqReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchChannelTabMqReq.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchChannelTabMqReq proto.InternalMessageInfo

func (m *SwitchChannelTabMqReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *SwitchChannelTabMqReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SwitchChannelTabMqReq) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *SwitchChannelTabMqReq) GetSource() Source {
	if m != nil {
		return m.Source
	}
	return Source_INVAIAL
}

func (m *SwitchChannelTabMqReq) GetHomePageType() HomePageType {
	if m != nil {
		return m.HomePageType
	}
	return HomePageType_Default
}

func (m *SwitchChannelTabMqReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SwitchChannelTabMqReq) GetOldTabId() uint32 {
	if m != nil {
		return m.OldTabId
	}
	return 0
}

type SwitchChannelTabMqResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SwitchChannelTabMqResp) Reset()         { *m = SwitchChannelTabMqResp{} }
func (m *SwitchChannelTabMqResp) String() string { return proto.CompactTextString(m) }
func (*SwitchChannelTabMqResp) ProtoMessage()    {}
func (*SwitchChannelTabMqResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{42}
}
func (m *SwitchChannelTabMqResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchChannelTabMqResp.Unmarshal(m, b)
}
func (m *SwitchChannelTabMqResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchChannelTabMqResp.Marshal(b, m, deterministic)
}
func (dst *SwitchChannelTabMqResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchChannelTabMqResp.Merge(dst, src)
}
func (m *SwitchChannelTabMqResp) XXX_Size() int {
	return xxx_messageInfo_SwitchChannelTabMqResp.Size(m)
}
func (m *SwitchChannelTabMqResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchChannelTabMqResp.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchChannelTabMqResp proto.InternalMessageInfo

type GetChannelRoomUserNumberReq struct {
	TabId                []uint32 `protobuf:"varint,1,rep,packed,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelRoomUserNumberReq) Reset()         { *m = GetChannelRoomUserNumberReq{} }
func (m *GetChannelRoomUserNumberReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelRoomUserNumberReq) ProtoMessage()    {}
func (*GetChannelRoomUserNumberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{43}
}
func (m *GetChannelRoomUserNumberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelRoomUserNumberReq.Unmarshal(m, b)
}
func (m *GetChannelRoomUserNumberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelRoomUserNumberReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelRoomUserNumberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelRoomUserNumberReq.Merge(dst, src)
}
func (m *GetChannelRoomUserNumberReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelRoomUserNumberReq.Size(m)
}
func (m *GetChannelRoomUserNumberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelRoomUserNumberReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelRoomUserNumberReq proto.InternalMessageInfo

func (m *GetChannelRoomUserNumberReq) GetTabId() []uint32 {
	if m != nil {
		return m.TabId
	}
	return nil
}

type GetChannelRoomUserNumberResp struct {
	RoomUserInfo         []*GetChannelRoomUserNumberResp_RoomUserInfo `protobuf:"bytes,1,rep,name=room_user_info,json=roomUserInfo,proto3" json:"room_user_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                     `json:"-"`
	XXX_unrecognized     []byte                                       `json:"-"`
	XXX_sizecache        int32                                        `json:"-"`
}

func (m *GetChannelRoomUserNumberResp) Reset()         { *m = GetChannelRoomUserNumberResp{} }
func (m *GetChannelRoomUserNumberResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelRoomUserNumberResp) ProtoMessage()    {}
func (*GetChannelRoomUserNumberResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{44}
}
func (m *GetChannelRoomUserNumberResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelRoomUserNumberResp.Unmarshal(m, b)
}
func (m *GetChannelRoomUserNumberResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelRoomUserNumberResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelRoomUserNumberResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelRoomUserNumberResp.Merge(dst, src)
}
func (m *GetChannelRoomUserNumberResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelRoomUserNumberResp.Size(m)
}
func (m *GetChannelRoomUserNumberResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelRoomUserNumberResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelRoomUserNumberResp proto.InternalMessageInfo

func (m *GetChannelRoomUserNumberResp) GetRoomUserInfo() []*GetChannelRoomUserNumberResp_RoomUserInfo {
	if m != nil {
		return m.RoomUserInfo
	}
	return nil
}

type GetChannelRoomUserNumberResp_RoomUserInfo struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TotalUserNumber      int64    `protobuf:"varint,2,opt,name=total_user_number,json=totalUserNumber,proto3" json:"total_user_number,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelRoomUserNumberResp_RoomUserInfo) Reset() {
	*m = GetChannelRoomUserNumberResp_RoomUserInfo{}
}
func (m *GetChannelRoomUserNumberResp_RoomUserInfo) String() string { return proto.CompactTextString(m) }
func (*GetChannelRoomUserNumberResp_RoomUserInfo) ProtoMessage()    {}
func (*GetChannelRoomUserNumberResp_RoomUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{44, 0}
}
func (m *GetChannelRoomUserNumberResp_RoomUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelRoomUserNumberResp_RoomUserInfo.Unmarshal(m, b)
}
func (m *GetChannelRoomUserNumberResp_RoomUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelRoomUserNumberResp_RoomUserInfo.Marshal(b, m, deterministic)
}
func (dst *GetChannelRoomUserNumberResp_RoomUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelRoomUserNumberResp_RoomUserInfo.Merge(dst, src)
}
func (m *GetChannelRoomUserNumberResp_RoomUserInfo) XXX_Size() int {
	return xxx_messageInfo_GetChannelRoomUserNumberResp_RoomUserInfo.Size(m)
}
func (m *GetChannelRoomUserNumberResp_RoomUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelRoomUserNumberResp_RoomUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelRoomUserNumberResp_RoomUserInfo proto.InternalMessageInfo

func (m *GetChannelRoomUserNumberResp_RoomUserInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetChannelRoomUserNumberResp_RoomUserInfo) GetTotalUserNumber() int64 {
	if m != nil {
		return m.TotalUserNumber
	}
	return 0
}

type AddTemporaryChannelReq struct {
	Channel              *ChannelInfo `protobuf:"bytes,1,opt,name=channel,proto3" json:"channel,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AddTemporaryChannelReq) Reset()         { *m = AddTemporaryChannelReq{} }
func (m *AddTemporaryChannelReq) String() string { return proto.CompactTextString(m) }
func (*AddTemporaryChannelReq) ProtoMessage()    {}
func (*AddTemporaryChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{45}
}
func (m *AddTemporaryChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddTemporaryChannelReq.Unmarshal(m, b)
}
func (m *AddTemporaryChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddTemporaryChannelReq.Marshal(b, m, deterministic)
}
func (dst *AddTemporaryChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddTemporaryChannelReq.Merge(dst, src)
}
func (m *AddTemporaryChannelReq) XXX_Size() int {
	return xxx_messageInfo_AddTemporaryChannelReq.Size(m)
}
func (m *AddTemporaryChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddTemporaryChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddTemporaryChannelReq proto.InternalMessageInfo

func (m *AddTemporaryChannelReq) GetChannel() *ChannelInfo {
	if m != nil {
		return m.Channel
	}
	return nil
}

type AddTemporaryChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddTemporaryChannelResp) Reset()         { *m = AddTemporaryChannelResp{} }
func (m *AddTemporaryChannelResp) String() string { return proto.CompactTextString(m) }
func (*AddTemporaryChannelResp) ProtoMessage()    {}
func (*AddTemporaryChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{46}
}
func (m *AddTemporaryChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddTemporaryChannelResp.Unmarshal(m, b)
}
func (m *AddTemporaryChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddTemporaryChannelResp.Marshal(b, m, deterministic)
}
func (dst *AddTemporaryChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddTemporaryChannelResp.Merge(dst, src)
}
func (m *AddTemporaryChannelResp) XXX_Size() int {
	return xxx_messageInfo_AddTemporaryChannelResp.Size(m)
}
func (m *AddTemporaryChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddTemporaryChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddTemporaryChannelResp proto.InternalMessageInfo

type SwitchChannelTabReq struct {
	Uid                  uint32       `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Creator              uint32       `protobuf:"varint,2,opt,name=creator,proto3" json:"creator,omitempty"`
	TabId                uint32       `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	ChannelId            uint32       `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Source               Source       `protobuf:"varint,5,opt,name=source,proto3,enum=topic_channel.channel.Source" json:"source,omitempty"`
	AppId                uint32       `protobuf:"varint,6,opt,name=appId,proto3" json:"appId,omitempty"`
	MarketId             uint32       `protobuf:"varint,7,opt,name=marketId,proto3" json:"marketId,omitempty"`
	HomePageType         HomePageType `protobuf:"varint,8,opt,name=home_page_type,json=homePageType,proto3,enum=topic_channel.channel.HomePageType" json:"home_page_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SwitchChannelTabReq) Reset()         { *m = SwitchChannelTabReq{} }
func (m *SwitchChannelTabReq) String() string { return proto.CompactTextString(m) }
func (*SwitchChannelTabReq) ProtoMessage()    {}
func (*SwitchChannelTabReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{47}
}
func (m *SwitchChannelTabReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchChannelTabReq.Unmarshal(m, b)
}
func (m *SwitchChannelTabReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchChannelTabReq.Marshal(b, m, deterministic)
}
func (dst *SwitchChannelTabReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchChannelTabReq.Merge(dst, src)
}
func (m *SwitchChannelTabReq) XXX_Size() int {
	return xxx_messageInfo_SwitchChannelTabReq.Size(m)
}
func (m *SwitchChannelTabReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchChannelTabReq.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchChannelTabReq proto.InternalMessageInfo

func (m *SwitchChannelTabReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SwitchChannelTabReq) GetCreator() uint32 {
	if m != nil {
		return m.Creator
	}
	return 0
}

func (m *SwitchChannelTabReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *SwitchChannelTabReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SwitchChannelTabReq) GetSource() Source {
	if m != nil {
		return m.Source
	}
	return Source_INVAIAL
}

func (m *SwitchChannelTabReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *SwitchChannelTabReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *SwitchChannelTabReq) GetHomePageType() HomePageType {
	if m != nil {
		return m.HomePageType
	}
	return HomePageType_Default
}

type SwitchChannelTabResp struct {
	TabName              string   `protobuf:"bytes,1,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	WelcomeTxtList       []string `protobuf:"bytes,2,rep,name=welcome_txt_list,json=welcomeTxtList,proto3" json:"welcome_txt_list,omitempty"`
	MicMod               uint32   `protobuf:"varint,3,opt,name=mic_mod,json=micMod,proto3" json:"mic_mod,omitempty"`
	TabType              uint32   `protobuf:"varint,4,opt,name=tab_type,json=tabType,proto3" json:"tab_type,omitempty"`
	TagId                uint32   `protobuf:"varint,5,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SwitchChannelTabResp) Reset()         { *m = SwitchChannelTabResp{} }
func (m *SwitchChannelTabResp) String() string { return proto.CompactTextString(m) }
func (*SwitchChannelTabResp) ProtoMessage()    {}
func (*SwitchChannelTabResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{48}
}
func (m *SwitchChannelTabResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchChannelTabResp.Unmarshal(m, b)
}
func (m *SwitchChannelTabResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchChannelTabResp.Marshal(b, m, deterministic)
}
func (dst *SwitchChannelTabResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchChannelTabResp.Merge(dst, src)
}
func (m *SwitchChannelTabResp) XXX_Size() int {
	return xxx_messageInfo_SwitchChannelTabResp.Size(m)
}
func (m *SwitchChannelTabResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchChannelTabResp.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchChannelTabResp proto.InternalMessageInfo

func (m *SwitchChannelTabResp) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *SwitchChannelTabResp) GetWelcomeTxtList() []string {
	if m != nil {
		return m.WelcomeTxtList
	}
	return nil
}

func (m *SwitchChannelTabResp) GetMicMod() uint32 {
	if m != nil {
		return m.MicMod
	}
	return 0
}

func (m *SwitchChannelTabResp) GetTabType() uint32 {
	if m != nil {
		return m.TabType
	}
	return 0
}

func (m *SwitchChannelTabResp) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

type UpdateTopicChannelInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateTopicChannelInfoReq) Reset()         { *m = UpdateTopicChannelInfoReq{} }
func (m *UpdateTopicChannelInfoReq) String() string { return proto.CompactTextString(m) }
func (*UpdateTopicChannelInfoReq) ProtoMessage()    {}
func (*UpdateTopicChannelInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{49}
}
func (m *UpdateTopicChannelInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateTopicChannelInfoReq.Unmarshal(m, b)
}
func (m *UpdateTopicChannelInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateTopicChannelInfoReq.Marshal(b, m, deterministic)
}
func (dst *UpdateTopicChannelInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTopicChannelInfoReq.Merge(dst, src)
}
func (m *UpdateTopicChannelInfoReq) XXX_Size() int {
	return xxx_messageInfo_UpdateTopicChannelInfoReq.Size(m)
}
func (m *UpdateTopicChannelInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTopicChannelInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTopicChannelInfoReq proto.InternalMessageInfo

func (m *UpdateTopicChannelInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UpdateTopicChannelInfoReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type UpdateTopicChannelInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateTopicChannelInfoResp) Reset()         { *m = UpdateTopicChannelInfoResp{} }
func (m *UpdateTopicChannelInfoResp) String() string { return proto.CompactTextString(m) }
func (*UpdateTopicChannelInfoResp) ProtoMessage()    {}
func (*UpdateTopicChannelInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{50}
}
func (m *UpdateTopicChannelInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateTopicChannelInfoResp.Unmarshal(m, b)
}
func (m *UpdateTopicChannelInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateTopicChannelInfoResp.Marshal(b, m, deterministic)
}
func (dst *UpdateTopicChannelInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTopicChannelInfoResp.Merge(dst, src)
}
func (m *UpdateTopicChannelInfoResp) XXX_Size() int {
	return xxx_messageInfo_UpdateTopicChannelInfoResp.Size(m)
}
func (m *UpdateTopicChannelInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTopicChannelInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTopicChannelInfoResp proto.InternalMessageInfo

// 更新用户上次进房的时间
type UpdateLastEnterRoomTimeByUidReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	EnterRoomTime        int64    `protobuf:"varint,2,opt,name=enter_room_time,json=enterRoomTime,proto3" json:"enter_room_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateLastEnterRoomTimeByUidReq) Reset()         { *m = UpdateLastEnterRoomTimeByUidReq{} }
func (m *UpdateLastEnterRoomTimeByUidReq) String() string { return proto.CompactTextString(m) }
func (*UpdateLastEnterRoomTimeByUidReq) ProtoMessage()    {}
func (*UpdateLastEnterRoomTimeByUidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{51}
}
func (m *UpdateLastEnterRoomTimeByUidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateLastEnterRoomTimeByUidReq.Unmarshal(m, b)
}
func (m *UpdateLastEnterRoomTimeByUidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateLastEnterRoomTimeByUidReq.Marshal(b, m, deterministic)
}
func (dst *UpdateLastEnterRoomTimeByUidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateLastEnterRoomTimeByUidReq.Merge(dst, src)
}
func (m *UpdateLastEnterRoomTimeByUidReq) XXX_Size() int {
	return xxx_messageInfo_UpdateLastEnterRoomTimeByUidReq.Size(m)
}
func (m *UpdateLastEnterRoomTimeByUidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateLastEnterRoomTimeByUidReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateLastEnterRoomTimeByUidReq proto.InternalMessageInfo

func (m *UpdateLastEnterRoomTimeByUidReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateLastEnterRoomTimeByUidReq) GetEnterRoomTime() int64 {
	if m != nil {
		return m.EnterRoomTime
	}
	return 0
}

type UpdateLastEnterRoomTimeByUidResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateLastEnterRoomTimeByUidResp) Reset()         { *m = UpdateLastEnterRoomTimeByUidResp{} }
func (m *UpdateLastEnterRoomTimeByUidResp) String() string { return proto.CompactTextString(m) }
func (*UpdateLastEnterRoomTimeByUidResp) ProtoMessage()    {}
func (*UpdateLastEnterRoomTimeByUidResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{52}
}
func (m *UpdateLastEnterRoomTimeByUidResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateLastEnterRoomTimeByUidResp.Unmarshal(m, b)
}
func (m *UpdateLastEnterRoomTimeByUidResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateLastEnterRoomTimeByUidResp.Marshal(b, m, deterministic)
}
func (dst *UpdateLastEnterRoomTimeByUidResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateLastEnterRoomTimeByUidResp.Merge(dst, src)
}
func (m *UpdateLastEnterRoomTimeByUidResp) XXX_Size() int {
	return xxx_messageInfo_UpdateLastEnterRoomTimeByUidResp.Size(m)
}
func (m *UpdateLastEnterRoomTimeByUidResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateLastEnterRoomTimeByUidResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateLastEnterRoomTimeByUidResp proto.InternalMessageInfo

// 获取用户上次进房的时间
type GetLastEnterRoomTimeByUidReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLastEnterRoomTimeByUidReq) Reset()         { *m = GetLastEnterRoomTimeByUidReq{} }
func (m *GetLastEnterRoomTimeByUidReq) String() string { return proto.CompactTextString(m) }
func (*GetLastEnterRoomTimeByUidReq) ProtoMessage()    {}
func (*GetLastEnterRoomTimeByUidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{53}
}
func (m *GetLastEnterRoomTimeByUidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLastEnterRoomTimeByUidReq.Unmarshal(m, b)
}
func (m *GetLastEnterRoomTimeByUidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLastEnterRoomTimeByUidReq.Marshal(b, m, deterministic)
}
func (dst *GetLastEnterRoomTimeByUidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLastEnterRoomTimeByUidReq.Merge(dst, src)
}
func (m *GetLastEnterRoomTimeByUidReq) XXX_Size() int {
	return xxx_messageInfo_GetLastEnterRoomTimeByUidReq.Size(m)
}
func (m *GetLastEnterRoomTimeByUidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLastEnterRoomTimeByUidReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLastEnterRoomTimeByUidReq proto.InternalMessageInfo

func (m *GetLastEnterRoomTimeByUidReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetLastEnterRoomTimeByUidResp struct {
	EnterRoomTime        int64    `protobuf:"varint,1,opt,name=enter_room_time,json=enterRoomTime,proto3" json:"enter_room_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLastEnterRoomTimeByUidResp) Reset()         { *m = GetLastEnterRoomTimeByUidResp{} }
func (m *GetLastEnterRoomTimeByUidResp) String() string { return proto.CompactTextString(m) }
func (*GetLastEnterRoomTimeByUidResp) ProtoMessage()    {}
func (*GetLastEnterRoomTimeByUidResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{54}
}
func (m *GetLastEnterRoomTimeByUidResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLastEnterRoomTimeByUidResp.Unmarshal(m, b)
}
func (m *GetLastEnterRoomTimeByUidResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLastEnterRoomTimeByUidResp.Marshal(b, m, deterministic)
}
func (dst *GetLastEnterRoomTimeByUidResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLastEnterRoomTimeByUidResp.Merge(dst, src)
}
func (m *GetLastEnterRoomTimeByUidResp) XXX_Size() int {
	return xxx_messageInfo_GetLastEnterRoomTimeByUidResp.Size(m)
}
func (m *GetLastEnterRoomTimeByUidResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLastEnterRoomTimeByUidResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLastEnterRoomTimeByUidResp proto.InternalMessageInfo

func (m *GetLastEnterRoomTimeByUidResp) GetEnterRoomTime() int64 {
	if m != nil {
		return m.EnterRoomTime
	}
	return 0
}

// 更新用户上次进房大于10分钟的玩法id
type UpdateLastEnterRoomTabIdByUidReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	QuitRoomTime         int64    `protobuf:"varint,3,opt,name=quitRoomTime,proto3" json:"quitRoomTime,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateLastEnterRoomTabIdByUidReq) Reset()         { *m = UpdateLastEnterRoomTabIdByUidReq{} }
func (m *UpdateLastEnterRoomTabIdByUidReq) String() string { return proto.CompactTextString(m) }
func (*UpdateLastEnterRoomTabIdByUidReq) ProtoMessage()    {}
func (*UpdateLastEnterRoomTabIdByUidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{55}
}
func (m *UpdateLastEnterRoomTabIdByUidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateLastEnterRoomTabIdByUidReq.Unmarshal(m, b)
}
func (m *UpdateLastEnterRoomTabIdByUidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateLastEnterRoomTabIdByUidReq.Marshal(b, m, deterministic)
}
func (dst *UpdateLastEnterRoomTabIdByUidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateLastEnterRoomTabIdByUidReq.Merge(dst, src)
}
func (m *UpdateLastEnterRoomTabIdByUidReq) XXX_Size() int {
	return xxx_messageInfo_UpdateLastEnterRoomTabIdByUidReq.Size(m)
}
func (m *UpdateLastEnterRoomTabIdByUidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateLastEnterRoomTabIdByUidReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateLastEnterRoomTabIdByUidReq proto.InternalMessageInfo

func (m *UpdateLastEnterRoomTabIdByUidReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateLastEnterRoomTabIdByUidReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *UpdateLastEnterRoomTabIdByUidReq) GetQuitRoomTime() int64 {
	if m != nil {
		return m.QuitRoomTime
	}
	return 0
}

type UpdateLastEnterRoomTabIdByUidResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateLastEnterRoomTabIdByUidResp) Reset()         { *m = UpdateLastEnterRoomTabIdByUidResp{} }
func (m *UpdateLastEnterRoomTabIdByUidResp) String() string { return proto.CompactTextString(m) }
func (*UpdateLastEnterRoomTabIdByUidResp) ProtoMessage()    {}
func (*UpdateLastEnterRoomTabIdByUidResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{56}
}
func (m *UpdateLastEnterRoomTabIdByUidResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateLastEnterRoomTabIdByUidResp.Unmarshal(m, b)
}
func (m *UpdateLastEnterRoomTabIdByUidResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateLastEnterRoomTabIdByUidResp.Marshal(b, m, deterministic)
}
func (dst *UpdateLastEnterRoomTabIdByUidResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateLastEnterRoomTabIdByUidResp.Merge(dst, src)
}
func (m *UpdateLastEnterRoomTabIdByUidResp) XXX_Size() int {
	return xxx_messageInfo_UpdateLastEnterRoomTabIdByUidResp.Size(m)
}
func (m *UpdateLastEnterRoomTabIdByUidResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateLastEnterRoomTabIdByUidResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateLastEnterRoomTabIdByUidResp proto.InternalMessageInfo

// 获取用户上次进房大于10分钟的玩法id
type GetLastEnterRoomTabIdByUidReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLastEnterRoomTabIdByUidReq) Reset()         { *m = GetLastEnterRoomTabIdByUidReq{} }
func (m *GetLastEnterRoomTabIdByUidReq) String() string { return proto.CompactTextString(m) }
func (*GetLastEnterRoomTabIdByUidReq) ProtoMessage()    {}
func (*GetLastEnterRoomTabIdByUidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{57}
}
func (m *GetLastEnterRoomTabIdByUidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLastEnterRoomTabIdByUidReq.Unmarshal(m, b)
}
func (m *GetLastEnterRoomTabIdByUidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLastEnterRoomTabIdByUidReq.Marshal(b, m, deterministic)
}
func (dst *GetLastEnterRoomTabIdByUidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLastEnterRoomTabIdByUidReq.Merge(dst, src)
}
func (m *GetLastEnterRoomTabIdByUidReq) XXX_Size() int {
	return xxx_messageInfo_GetLastEnterRoomTabIdByUidReq.Size(m)
}
func (m *GetLastEnterRoomTabIdByUidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLastEnterRoomTabIdByUidReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLastEnterRoomTabIdByUidReq proto.InternalMessageInfo

func (m *GetLastEnterRoomTabIdByUidReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetLastEnterRoomTabIdByUidResp struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLastEnterRoomTabIdByUidResp) Reset()         { *m = GetLastEnterRoomTabIdByUidResp{} }
func (m *GetLastEnterRoomTabIdByUidResp) String() string { return proto.CompactTextString(m) }
func (*GetLastEnterRoomTabIdByUidResp) ProtoMessage()    {}
func (*GetLastEnterRoomTabIdByUidResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_e065d1c544bfe311, []int{58}
}
func (m *GetLastEnterRoomTabIdByUidResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLastEnterRoomTabIdByUidResp.Unmarshal(m, b)
}
func (m *GetLastEnterRoomTabIdByUidResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLastEnterRoomTabIdByUidResp.Marshal(b, m, deterministic)
}
func (dst *GetLastEnterRoomTabIdByUidResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLastEnterRoomTabIdByUidResp.Merge(dst, src)
}
func (m *GetLastEnterRoomTabIdByUidResp) XXX_Size() int {
	return xxx_messageInfo_GetLastEnterRoomTabIdByUidResp.Size(m)
}
func (m *GetLastEnterRoomTabIdByUidResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLastEnterRoomTabIdByUidResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLastEnterRoomTabIdByUidResp proto.InternalMessageInfo

func (m *GetLastEnterRoomTabIdByUidResp) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func init() {
	proto.RegisterType((*BlockOptionList)(nil), "topic_channel.channel.BlockOptionList")
	proto.RegisterType((*BlockOption)(nil), "topic_channel.channel.BlockOption")
	proto.RegisterType((*ChannelInfo)(nil), "topic_channel.channel.ChannelInfo")
	proto.RegisterType((*AddChannelReq)(nil), "topic_channel.channel.AddChannelReq")
	proto.RegisterType((*AddChannelResp)(nil), "topic_channel.channel.AddChannelResp")
	proto.RegisterType((*UpdateChannelInfoReq)(nil), "topic_channel.channel.UpdateChannelInfoReq")
	proto.RegisterType((*UpdateChannelInfoResp)(nil), "topic_channel.channel.UpdateChannelInfoResp")
	proto.RegisterType((*DismissChannelReq)(nil), "topic_channel.channel.DismissChannelReq")
	proto.RegisterType((*DismissChannelResp)(nil), "topic_channel.channel.DismissChannelResp")
	proto.RegisterType((*GetRecommendChannelListLoadMore)(nil), "topic_channel.channel.GetRecommendChannelListLoadMore")
	proto.RegisterType((*GetRecommendChannelListReq)(nil), "topic_channel.channel.GetRecommendChannelListReq")
	proto.RegisterType((*GetRecommendChannelListResp)(nil), "topic_channel.channel.GetRecommendChannelListResp")
	proto.RegisterType((*GetListByTabLoadMore)(nil), "topic_channel.channel.GetListByTabLoadMore")
	proto.RegisterType((*GetListByTabLoadMoreItem)(nil), "topic_channel.channel.GetListByTabLoadMoreItem")
	proto.RegisterType((*GetRecommendChannelListByTabReq)(nil), "topic_channel.channel.GetRecommendChannelListByTabReq")
	proto.RegisterType((*GetRecommendChannelListByTabResp)(nil), "topic_channel.channel.GetRecommendChannelListByTabResp")
	proto.RegisterType((*GetChannelByIdsReq)(nil), "topic_channel.channel.GetChannelByIdsReq")
	proto.RegisterType((*GetChannelByIdsResp)(nil), "topic_channel.channel.GetChannelByIdsResp")
	proto.RegisterType((*DismissTabReq)(nil), "topic_channel.channel.DismissTabReq")
	proto.RegisterType((*DismissTabResp)(nil), "topic_channel.channel.DismissTabResp")
	proto.RegisterType((*KeepChannelAliveReq)(nil), "topic_channel.channel.KeepChannelAliveReq")
	proto.RegisterType((*KeepChannelAliveResp)(nil), "topic_channel.channel.KeepChannelAliveResp")
	proto.RegisterType((*DisappearChannelReq)(nil), "topic_channel.channel.DisappearChannelReq")
	proto.RegisterType((*DisappearChannelReq_Timeout)(nil), "topic_channel.channel.DisappearChannelReq.Timeout")
	proto.RegisterType((*DisappearChannelReq_Keepalive)(nil), "topic_channel.channel.DisappearChannelReq.Keepalive")
	proto.RegisterType((*DisappearChannelReq_ReleaseTimeout)(nil), "topic_channel.channel.DisappearChannelReq.ReleaseTimeout")
	proto.RegisterType((*DisappearChannelResp)(nil), "topic_channel.channel.DisappearChannelResp")
	proto.RegisterType((*TabConfigure)(nil), "topic_channel.channel.TabConfigure")
	proto.RegisterType((*SetTabConfigureReq)(nil), "topic_channel.channel.SetTabConfigureReq")
	proto.RegisterType((*SetTabConfigureResp)(nil), "topic_channel.channel.SetTabConfigureResp")
	proto.RegisterType((*GetOnlineInfoReq)(nil), "topic_channel.channel.GetOnlineInfoReq")
	proto.RegisterType((*GetOnlineInfoResp)(nil), "topic_channel.channel.GetOnlineInfoResp")
	proto.RegisterType((*FreezeChannelReq)(nil), "topic_channel.channel.FreezeChannelReq")
	proto.RegisterType((*FreezeChannelResp)(nil), "topic_channel.channel.FreezeChannelResp")
	proto.RegisterType((*UnfreezeChannelReq)(nil), "topic_channel.channel.UnfreezeChannelReq")
	proto.RegisterType((*UnfreezeChannelResp)(nil), "topic_channel.channel.UnfreezeChannelResp")
	proto.RegisterType((*GetChannelFreezeInfoReq)(nil), "topic_channel.channel.GetChannelFreezeInfoReq")
	proto.RegisterType((*GetChannelFreezeInfoResp)(nil), "topic_channel.channel.GetChannelFreezeInfoResp")
	proto.RegisterType((*SetExtraHistoryReq)(nil), "topic_channel.channel.SetExtraHistoryReq")
	proto.RegisterType((*SetExtraHistoryResp)(nil), "topic_channel.channel.SetExtraHistoryResp")
	proto.RegisterType((*GetExtraHistoryReq)(nil), "topic_channel.channel.GetExtraHistoryReq")
	proto.RegisterType((*GetExtraHistoryResp)(nil), "topic_channel.channel.GetExtraHistoryResp")
	proto.RegisterType((*GetChannelPlayModelReq)(nil), "topic_channel.channel.GetChannelPlayModelReq")
	proto.RegisterType((*GetChannelPlayModelResp)(nil), "topic_channel.channel.GetChannelPlayModelResp")
	proto.RegisterType((*SwitchChannelTabMqReq)(nil), "topic_channel.channel.SwitchChannelTabMqReq")
	proto.RegisterType((*SwitchChannelTabMqResp)(nil), "topic_channel.channel.SwitchChannelTabMqResp")
	proto.RegisterType((*GetChannelRoomUserNumberReq)(nil), "topic_channel.channel.GetChannelRoomUserNumberReq")
	proto.RegisterType((*GetChannelRoomUserNumberResp)(nil), "topic_channel.channel.GetChannelRoomUserNumberResp")
	proto.RegisterType((*GetChannelRoomUserNumberResp_RoomUserInfo)(nil), "topic_channel.channel.GetChannelRoomUserNumberResp.RoomUserInfo")
	proto.RegisterType((*AddTemporaryChannelReq)(nil), "topic_channel.channel.AddTemporaryChannelReq")
	proto.RegisterType((*AddTemporaryChannelResp)(nil), "topic_channel.channel.AddTemporaryChannelResp")
	proto.RegisterType((*SwitchChannelTabReq)(nil), "topic_channel.channel.SwitchChannelTabReq")
	proto.RegisterType((*SwitchChannelTabResp)(nil), "topic_channel.channel.SwitchChannelTabResp")
	proto.RegisterType((*UpdateTopicChannelInfoReq)(nil), "topic_channel.channel.UpdateTopicChannelInfoReq")
	proto.RegisterType((*UpdateTopicChannelInfoResp)(nil), "topic_channel.channel.UpdateTopicChannelInfoResp")
	proto.RegisterType((*UpdateLastEnterRoomTimeByUidReq)(nil), "topic_channel.channel.UpdateLastEnterRoomTimeByUidReq")
	proto.RegisterType((*UpdateLastEnterRoomTimeByUidResp)(nil), "topic_channel.channel.UpdateLastEnterRoomTimeByUidResp")
	proto.RegisterType((*GetLastEnterRoomTimeByUidReq)(nil), "topic_channel.channel.GetLastEnterRoomTimeByUidReq")
	proto.RegisterType((*GetLastEnterRoomTimeByUidResp)(nil), "topic_channel.channel.GetLastEnterRoomTimeByUidResp")
	proto.RegisterType((*UpdateLastEnterRoomTabIdByUidReq)(nil), "topic_channel.channel.UpdateLastEnterRoomTabIdByUidReq")
	proto.RegisterType((*UpdateLastEnterRoomTabIdByUidResp)(nil), "topic_channel.channel.UpdateLastEnterRoomTabIdByUidResp")
	proto.RegisterType((*GetLastEnterRoomTabIdByUidReq)(nil), "topic_channel.channel.GetLastEnterRoomTabIdByUidReq")
	proto.RegisterType((*GetLastEnterRoomTabIdByUidResp)(nil), "topic_channel.channel.GetLastEnterRoomTabIdByUidResp")
	proto.RegisterEnum("topic_channel.channel.ChannelDisplayType", ChannelDisplayType_name, ChannelDisplayType_value)
	proto.RegisterEnum("topic_channel.channel.HomePageType", HomePageType_name, HomePageType_value)
	proto.RegisterEnum("topic_channel.channel.DismissType", DismissType_name, DismissType_value)
	proto.RegisterEnum("topic_channel.channel.KeepAliveStatus", KeepAliveStatus_name, KeepAliveStatus_value)
	proto.RegisterEnum("topic_channel.channel.Source", Source_name, Source_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelClient is the client API for Channel service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelClient interface {
	// 创建主题房
	AddChannel(ctx context.Context, in *AddChannelReq, opts ...grpc.CallOption) (*AddChannelResp, error)
	// 修改主题房字段
	UpdateChannelInfo(ctx context.Context, in *UpdateChannelInfoReq, opts ...grpc.CallOption) (*UpdateChannelInfoResp, error)
	// 解散主题房
	DismissChannel(ctx context.Context, in *DismissChannelReq, opts ...grpc.CallOption) (*DismissChannelResp, error)
	// 不限分类获取列表
	GetRecommendChannelList(ctx context.Context, in *GetRecommendChannelListReq, opts ...grpc.CallOption) (*GetRecommendChannelListResp, error)
	// 指定分类获取列表
	GetRecommendChannelListByTab(ctx context.Context, in *GetRecommendChannelListByTabReq, opts ...grpc.CallOption) (*GetRecommendChannelListByTabResp, error)
	// 获取主题房信息
	GetChannelByIds(ctx context.Context, in *GetChannelByIdsReq, opts ...grpc.CallOption) (*GetChannelByIdsResp, error)
	// 解散某个分类
	DismissTab(ctx context.Context, in *DismissTabReq, opts ...grpc.CallOption) (*DismissTabResp, error)
	// 房间保持心跳
	KeepChannelAlive(ctx context.Context, in *KeepChannelAliveReq, opts ...grpc.CallOption) (*KeepChannelAliveResp, error)
	// 清除房间展示在tab
	DisappearChannel(ctx context.Context, in *DisappearChannelReq, opts ...grpc.CallOption) (*DisappearChannelResp, error)
	// 设置对应tab下对应的获取房间配置
	SetTabConfigure(ctx context.Context, in *SetTabConfigureReq, opts ...grpc.CallOption) (*SetTabConfigureResp, error)
	// 获取主题房数，以及示例用户
	GetOnlineInfo(ctx context.Context, in *GetOnlineInfoReq, opts ...grpc.CallOption) (*GetOnlineInfoResp, error)
	// 冻结主题房
	FreezeChannel(ctx context.Context, in *FreezeChannelReq, opts ...grpc.CallOption) (*FreezeChannelResp, error)
	// 解除冻结主题房
	UnfreezeChannel(ctx context.Context, in *UnfreezeChannelReq, opts ...grpc.CallOption) (*UnfreezeChannelResp, error)
	GetChannelFreezeInfo(ctx context.Context, in *GetChannelFreezeInfoReq, opts ...grpc.CallOption) (*GetChannelFreezeInfoResp, error)
	// 获取房间玩法和模式列
	GetChannelPlayModel(ctx context.Context, in *GetChannelPlayModelReq, opts ...grpc.CallOption) (*GetChannelPlayModelResp, error)
	// 切换玩法上报tab事件
	SwitchChannelTabMq(ctx context.Context, in *SwitchChannelTabMqReq, opts ...grpc.CallOption) (*SwitchChannelTabMqResp, error)
	// 获取指定房间人数
	GetChannelRoomUserNumber(ctx context.Context, in *GetChannelRoomUserNumberReq, opts ...grpc.CallOption) (*GetChannelRoomUserNumberResp, error)
	// 创建临时房
	AddTemporaryChannel(ctx context.Context, in *AddTemporaryChannelReq, opts ...grpc.CallOption) (*AddTemporaryChannelResp, error)
	// 历史记录保存
	SetExtraHistory(ctx context.Context, in *SetExtraHistoryReq, opts ...grpc.CallOption) (*SetExtraHistoryResp, error)
	GetExtraHistory(ctx context.Context, in *GetExtraHistoryReq, opts ...grpc.CallOption) (*GetExtraHistoryResp, error)
	// 切换玩法
	SwitchChannelTab(ctx context.Context, in *SwitchChannelTabReq, opts ...grpc.CallOption) (*SwitchChannelTabResp, error)
	SwitchChannelTabAndBC(ctx context.Context, in *SwitchChannelTabReq, opts ...grpc.CallOption) (*SwitchChannelTabResp, error)
	// 添加房间玩法数据，过渡用
	UpdateTopicChannelInfo(ctx context.Context, in *UpdateTopicChannelInfoReq, opts ...grpc.CallOption) (*UpdateTopicChannelInfoResp, error)
	// 更新用户进房时间记录
	UpdateLastEnterRoomTimeByUid(ctx context.Context, in *UpdateLastEnterRoomTimeByUidReq, opts ...grpc.CallOption) (*UpdateLastEnterRoomTimeByUidResp, error)
	// 获取用户进房时间记录
	GetLastEnterRoomTimeByUid(ctx context.Context, in *GetLastEnterRoomTimeByUidReq, opts ...grpc.CallOption) (*GetLastEnterRoomTimeByUidResp, error)
	// 更新用户进房tab记录
	UpdateLastEnterRoomTabIdByUid(ctx context.Context, in *UpdateLastEnterRoomTabIdByUidReq, opts ...grpc.CallOption) (*UpdateLastEnterRoomTabIdByUidResp, error)
	// 获取用户进房tab记录
	GetLastEnterRoomTabIdByUid(ctx context.Context, in *GetLastEnterRoomTabIdByUidReq, opts ...grpc.CallOption) (*GetLastEnterRoomTabIdByUidResp, error)
}

type channelClient struct {
	cc *grpc.ClientConn
}

func NewChannelClient(cc *grpc.ClientConn) ChannelClient {
	return &channelClient{cc}
}

func (c *channelClient) AddChannel(ctx context.Context, in *AddChannelReq, opts ...grpc.CallOption) (*AddChannelResp, error) {
	out := new(AddChannelResp)
	err := c.cc.Invoke(ctx, "/topic_channel.channel.Channel/AddChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) UpdateChannelInfo(ctx context.Context, in *UpdateChannelInfoReq, opts ...grpc.CallOption) (*UpdateChannelInfoResp, error) {
	out := new(UpdateChannelInfoResp)
	err := c.cc.Invoke(ctx, "/topic_channel.channel.Channel/UpdateChannelInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) DismissChannel(ctx context.Context, in *DismissChannelReq, opts ...grpc.CallOption) (*DismissChannelResp, error) {
	out := new(DismissChannelResp)
	err := c.cc.Invoke(ctx, "/topic_channel.channel.Channel/DismissChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) GetRecommendChannelList(ctx context.Context, in *GetRecommendChannelListReq, opts ...grpc.CallOption) (*GetRecommendChannelListResp, error) {
	out := new(GetRecommendChannelListResp)
	err := c.cc.Invoke(ctx, "/topic_channel.channel.Channel/GetRecommendChannelList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) GetRecommendChannelListByTab(ctx context.Context, in *GetRecommendChannelListByTabReq, opts ...grpc.CallOption) (*GetRecommendChannelListByTabResp, error) {
	out := new(GetRecommendChannelListByTabResp)
	err := c.cc.Invoke(ctx, "/topic_channel.channel.Channel/GetRecommendChannelListByTab", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) GetChannelByIds(ctx context.Context, in *GetChannelByIdsReq, opts ...grpc.CallOption) (*GetChannelByIdsResp, error) {
	out := new(GetChannelByIdsResp)
	err := c.cc.Invoke(ctx, "/topic_channel.channel.Channel/GetChannelByIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) DismissTab(ctx context.Context, in *DismissTabReq, opts ...grpc.CallOption) (*DismissTabResp, error) {
	out := new(DismissTabResp)
	err := c.cc.Invoke(ctx, "/topic_channel.channel.Channel/DismissTab", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) KeepChannelAlive(ctx context.Context, in *KeepChannelAliveReq, opts ...grpc.CallOption) (*KeepChannelAliveResp, error) {
	out := new(KeepChannelAliveResp)
	err := c.cc.Invoke(ctx, "/topic_channel.channel.Channel/KeepChannelAlive", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) DisappearChannel(ctx context.Context, in *DisappearChannelReq, opts ...grpc.CallOption) (*DisappearChannelResp, error) {
	out := new(DisappearChannelResp)
	err := c.cc.Invoke(ctx, "/topic_channel.channel.Channel/DisappearChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) SetTabConfigure(ctx context.Context, in *SetTabConfigureReq, opts ...grpc.CallOption) (*SetTabConfigureResp, error) {
	out := new(SetTabConfigureResp)
	err := c.cc.Invoke(ctx, "/topic_channel.channel.Channel/SetTabConfigure", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) GetOnlineInfo(ctx context.Context, in *GetOnlineInfoReq, opts ...grpc.CallOption) (*GetOnlineInfoResp, error) {
	out := new(GetOnlineInfoResp)
	err := c.cc.Invoke(ctx, "/topic_channel.channel.Channel/GetOnlineInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) FreezeChannel(ctx context.Context, in *FreezeChannelReq, opts ...grpc.CallOption) (*FreezeChannelResp, error) {
	out := new(FreezeChannelResp)
	err := c.cc.Invoke(ctx, "/topic_channel.channel.Channel/FreezeChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) UnfreezeChannel(ctx context.Context, in *UnfreezeChannelReq, opts ...grpc.CallOption) (*UnfreezeChannelResp, error) {
	out := new(UnfreezeChannelResp)
	err := c.cc.Invoke(ctx, "/topic_channel.channel.Channel/UnfreezeChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) GetChannelFreezeInfo(ctx context.Context, in *GetChannelFreezeInfoReq, opts ...grpc.CallOption) (*GetChannelFreezeInfoResp, error) {
	out := new(GetChannelFreezeInfoResp)
	err := c.cc.Invoke(ctx, "/topic_channel.channel.Channel/GetChannelFreezeInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) GetChannelPlayModel(ctx context.Context, in *GetChannelPlayModelReq, opts ...grpc.CallOption) (*GetChannelPlayModelResp, error) {
	out := new(GetChannelPlayModelResp)
	err := c.cc.Invoke(ctx, "/topic_channel.channel.Channel/GetChannelPlayModel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) SwitchChannelTabMq(ctx context.Context, in *SwitchChannelTabMqReq, opts ...grpc.CallOption) (*SwitchChannelTabMqResp, error) {
	out := new(SwitchChannelTabMqResp)
	err := c.cc.Invoke(ctx, "/topic_channel.channel.Channel/SwitchChannelTabMq", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) GetChannelRoomUserNumber(ctx context.Context, in *GetChannelRoomUserNumberReq, opts ...grpc.CallOption) (*GetChannelRoomUserNumberResp, error) {
	out := new(GetChannelRoomUserNumberResp)
	err := c.cc.Invoke(ctx, "/topic_channel.channel.Channel/GetChannelRoomUserNumber", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) AddTemporaryChannel(ctx context.Context, in *AddTemporaryChannelReq, opts ...grpc.CallOption) (*AddTemporaryChannelResp, error) {
	out := new(AddTemporaryChannelResp)
	err := c.cc.Invoke(ctx, "/topic_channel.channel.Channel/AddTemporaryChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) SetExtraHistory(ctx context.Context, in *SetExtraHistoryReq, opts ...grpc.CallOption) (*SetExtraHistoryResp, error) {
	out := new(SetExtraHistoryResp)
	err := c.cc.Invoke(ctx, "/topic_channel.channel.Channel/SetExtraHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) GetExtraHistory(ctx context.Context, in *GetExtraHistoryReq, opts ...grpc.CallOption) (*GetExtraHistoryResp, error) {
	out := new(GetExtraHistoryResp)
	err := c.cc.Invoke(ctx, "/topic_channel.channel.Channel/GetExtraHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) SwitchChannelTab(ctx context.Context, in *SwitchChannelTabReq, opts ...grpc.CallOption) (*SwitchChannelTabResp, error) {
	out := new(SwitchChannelTabResp)
	err := c.cc.Invoke(ctx, "/topic_channel.channel.Channel/SwitchChannelTab", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) SwitchChannelTabAndBC(ctx context.Context, in *SwitchChannelTabReq, opts ...grpc.CallOption) (*SwitchChannelTabResp, error) {
	out := new(SwitchChannelTabResp)
	err := c.cc.Invoke(ctx, "/topic_channel.channel.Channel/SwitchChannelTabAndBC", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) UpdateTopicChannelInfo(ctx context.Context, in *UpdateTopicChannelInfoReq, opts ...grpc.CallOption) (*UpdateTopicChannelInfoResp, error) {
	out := new(UpdateTopicChannelInfoResp)
	err := c.cc.Invoke(ctx, "/topic_channel.channel.Channel/UpdateTopicChannelInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) UpdateLastEnterRoomTimeByUid(ctx context.Context, in *UpdateLastEnterRoomTimeByUidReq, opts ...grpc.CallOption) (*UpdateLastEnterRoomTimeByUidResp, error) {
	out := new(UpdateLastEnterRoomTimeByUidResp)
	err := c.cc.Invoke(ctx, "/topic_channel.channel.Channel/UpdateLastEnterRoomTimeByUid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) GetLastEnterRoomTimeByUid(ctx context.Context, in *GetLastEnterRoomTimeByUidReq, opts ...grpc.CallOption) (*GetLastEnterRoomTimeByUidResp, error) {
	out := new(GetLastEnterRoomTimeByUidResp)
	err := c.cc.Invoke(ctx, "/topic_channel.channel.Channel/GetLastEnterRoomTimeByUid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) UpdateLastEnterRoomTabIdByUid(ctx context.Context, in *UpdateLastEnterRoomTabIdByUidReq, opts ...grpc.CallOption) (*UpdateLastEnterRoomTabIdByUidResp, error) {
	out := new(UpdateLastEnterRoomTabIdByUidResp)
	err := c.cc.Invoke(ctx, "/topic_channel.channel.Channel/UpdateLastEnterRoomTabIdByUid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelClient) GetLastEnterRoomTabIdByUid(ctx context.Context, in *GetLastEnterRoomTabIdByUidReq, opts ...grpc.CallOption) (*GetLastEnterRoomTabIdByUidResp, error) {
	out := new(GetLastEnterRoomTabIdByUidResp)
	err := c.cc.Invoke(ctx, "/topic_channel.channel.Channel/GetLastEnterRoomTabIdByUid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelServer is the server API for Channel service.
type ChannelServer interface {
	// 创建主题房
	AddChannel(context.Context, *AddChannelReq) (*AddChannelResp, error)
	// 修改主题房字段
	UpdateChannelInfo(context.Context, *UpdateChannelInfoReq) (*UpdateChannelInfoResp, error)
	// 解散主题房
	DismissChannel(context.Context, *DismissChannelReq) (*DismissChannelResp, error)
	// 不限分类获取列表
	GetRecommendChannelList(context.Context, *GetRecommendChannelListReq) (*GetRecommendChannelListResp, error)
	// 指定分类获取列表
	GetRecommendChannelListByTab(context.Context, *GetRecommendChannelListByTabReq) (*GetRecommendChannelListByTabResp, error)
	// 获取主题房信息
	GetChannelByIds(context.Context, *GetChannelByIdsReq) (*GetChannelByIdsResp, error)
	// 解散某个分类
	DismissTab(context.Context, *DismissTabReq) (*DismissTabResp, error)
	// 房间保持心跳
	KeepChannelAlive(context.Context, *KeepChannelAliveReq) (*KeepChannelAliveResp, error)
	// 清除房间展示在tab
	DisappearChannel(context.Context, *DisappearChannelReq) (*DisappearChannelResp, error)
	// 设置对应tab下对应的获取房间配置
	SetTabConfigure(context.Context, *SetTabConfigureReq) (*SetTabConfigureResp, error)
	// 获取主题房数，以及示例用户
	GetOnlineInfo(context.Context, *GetOnlineInfoReq) (*GetOnlineInfoResp, error)
	// 冻结主题房
	FreezeChannel(context.Context, *FreezeChannelReq) (*FreezeChannelResp, error)
	// 解除冻结主题房
	UnfreezeChannel(context.Context, *UnfreezeChannelReq) (*UnfreezeChannelResp, error)
	GetChannelFreezeInfo(context.Context, *GetChannelFreezeInfoReq) (*GetChannelFreezeInfoResp, error)
	// 获取房间玩法和模式列
	GetChannelPlayModel(context.Context, *GetChannelPlayModelReq) (*GetChannelPlayModelResp, error)
	// 切换玩法上报tab事件
	SwitchChannelTabMq(context.Context, *SwitchChannelTabMqReq) (*SwitchChannelTabMqResp, error)
	// 获取指定房间人数
	GetChannelRoomUserNumber(context.Context, *GetChannelRoomUserNumberReq) (*GetChannelRoomUserNumberResp, error)
	// 创建临时房
	AddTemporaryChannel(context.Context, *AddTemporaryChannelReq) (*AddTemporaryChannelResp, error)
	// 历史记录保存
	SetExtraHistory(context.Context, *SetExtraHistoryReq) (*SetExtraHistoryResp, error)
	GetExtraHistory(context.Context, *GetExtraHistoryReq) (*GetExtraHistoryResp, error)
	// 切换玩法
	SwitchChannelTab(context.Context, *SwitchChannelTabReq) (*SwitchChannelTabResp, error)
	SwitchChannelTabAndBC(context.Context, *SwitchChannelTabReq) (*SwitchChannelTabResp, error)
	// 添加房间玩法数据，过渡用
	UpdateTopicChannelInfo(context.Context, *UpdateTopicChannelInfoReq) (*UpdateTopicChannelInfoResp, error)
	// 更新用户进房时间记录
	UpdateLastEnterRoomTimeByUid(context.Context, *UpdateLastEnterRoomTimeByUidReq) (*UpdateLastEnterRoomTimeByUidResp, error)
	// 获取用户进房时间记录
	GetLastEnterRoomTimeByUid(context.Context, *GetLastEnterRoomTimeByUidReq) (*GetLastEnterRoomTimeByUidResp, error)
	// 更新用户进房tab记录
	UpdateLastEnterRoomTabIdByUid(context.Context, *UpdateLastEnterRoomTabIdByUidReq) (*UpdateLastEnterRoomTabIdByUidResp, error)
	// 获取用户进房tab记录
	GetLastEnterRoomTabIdByUid(context.Context, *GetLastEnterRoomTabIdByUidReq) (*GetLastEnterRoomTabIdByUidResp, error)
}

func RegisterChannelServer(s *grpc.Server, srv ChannelServer) {
	s.RegisterService(&_Channel_serviceDesc, srv)
}

func _Channel_AddChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).AddChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.channel.Channel/AddChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).AddChannel(ctx, req.(*AddChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_UpdateChannelInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateChannelInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).UpdateChannelInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.channel.Channel/UpdateChannelInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).UpdateChannelInfo(ctx, req.(*UpdateChannelInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_DismissChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DismissChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).DismissChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.channel.Channel/DismissChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).DismissChannel(ctx, req.(*DismissChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_GetRecommendChannelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecommendChannelListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).GetRecommendChannelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.channel.Channel/GetRecommendChannelList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).GetRecommendChannelList(ctx, req.(*GetRecommendChannelListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_GetRecommendChannelListByTab_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecommendChannelListByTabReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).GetRecommendChannelListByTab(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.channel.Channel/GetRecommendChannelListByTab",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).GetRecommendChannelListByTab(ctx, req.(*GetRecommendChannelListByTabReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_GetChannelByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelByIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).GetChannelByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.channel.Channel/GetChannelByIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).GetChannelByIds(ctx, req.(*GetChannelByIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_DismissTab_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DismissTabReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).DismissTab(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.channel.Channel/DismissTab",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).DismissTab(ctx, req.(*DismissTabReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_KeepChannelAlive_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(KeepChannelAliveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).KeepChannelAlive(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.channel.Channel/KeepChannelAlive",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).KeepChannelAlive(ctx, req.(*KeepChannelAliveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_DisappearChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisappearChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).DisappearChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.channel.Channel/DisappearChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).DisappearChannel(ctx, req.(*DisappearChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_SetTabConfigure_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetTabConfigureReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).SetTabConfigure(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.channel.Channel/SetTabConfigure",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).SetTabConfigure(ctx, req.(*SetTabConfigureReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_GetOnlineInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOnlineInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).GetOnlineInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.channel.Channel/GetOnlineInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).GetOnlineInfo(ctx, req.(*GetOnlineInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_FreezeChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FreezeChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).FreezeChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.channel.Channel/FreezeChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).FreezeChannel(ctx, req.(*FreezeChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_UnfreezeChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnfreezeChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).UnfreezeChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.channel.Channel/UnfreezeChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).UnfreezeChannel(ctx, req.(*UnfreezeChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_GetChannelFreezeInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelFreezeInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).GetChannelFreezeInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.channel.Channel/GetChannelFreezeInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).GetChannelFreezeInfo(ctx, req.(*GetChannelFreezeInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_GetChannelPlayModel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelPlayModelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).GetChannelPlayModel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.channel.Channel/GetChannelPlayModel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).GetChannelPlayModel(ctx, req.(*GetChannelPlayModelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_SwitchChannelTabMq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SwitchChannelTabMqReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).SwitchChannelTabMq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.channel.Channel/SwitchChannelTabMq",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).SwitchChannelTabMq(ctx, req.(*SwitchChannelTabMqReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_GetChannelRoomUserNumber_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelRoomUserNumberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).GetChannelRoomUserNumber(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.channel.Channel/GetChannelRoomUserNumber",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).GetChannelRoomUserNumber(ctx, req.(*GetChannelRoomUserNumberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_AddTemporaryChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddTemporaryChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).AddTemporaryChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.channel.Channel/AddTemporaryChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).AddTemporaryChannel(ctx, req.(*AddTemporaryChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_SetExtraHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetExtraHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).SetExtraHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.channel.Channel/SetExtraHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).SetExtraHistory(ctx, req.(*SetExtraHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_GetExtraHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExtraHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).GetExtraHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.channel.Channel/GetExtraHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).GetExtraHistory(ctx, req.(*GetExtraHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_SwitchChannelTab_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SwitchChannelTabReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).SwitchChannelTab(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.channel.Channel/SwitchChannelTab",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).SwitchChannelTab(ctx, req.(*SwitchChannelTabReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_SwitchChannelTabAndBC_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SwitchChannelTabReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).SwitchChannelTabAndBC(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.channel.Channel/SwitchChannelTabAndBC",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).SwitchChannelTabAndBC(ctx, req.(*SwitchChannelTabReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_UpdateTopicChannelInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTopicChannelInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).UpdateTopicChannelInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.channel.Channel/UpdateTopicChannelInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).UpdateTopicChannelInfo(ctx, req.(*UpdateTopicChannelInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_UpdateLastEnterRoomTimeByUid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateLastEnterRoomTimeByUidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).UpdateLastEnterRoomTimeByUid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.channel.Channel/UpdateLastEnterRoomTimeByUid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).UpdateLastEnterRoomTimeByUid(ctx, req.(*UpdateLastEnterRoomTimeByUidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_GetLastEnterRoomTimeByUid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLastEnterRoomTimeByUidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).GetLastEnterRoomTimeByUid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.channel.Channel/GetLastEnterRoomTimeByUid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).GetLastEnterRoomTimeByUid(ctx, req.(*GetLastEnterRoomTimeByUidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_UpdateLastEnterRoomTabIdByUid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateLastEnterRoomTabIdByUidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).UpdateLastEnterRoomTabIdByUid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.channel.Channel/UpdateLastEnterRoomTabIdByUid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).UpdateLastEnterRoomTabIdByUid(ctx, req.(*UpdateLastEnterRoomTabIdByUidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channel_GetLastEnterRoomTabIdByUid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLastEnterRoomTabIdByUidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelServer).GetLastEnterRoomTabIdByUid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.channel.Channel/GetLastEnterRoomTabIdByUid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelServer).GetLastEnterRoomTabIdByUid(ctx, req.(*GetLastEnterRoomTabIdByUidReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Channel_serviceDesc = grpc.ServiceDesc{
	ServiceName: "topic_channel.channel.Channel",
	HandlerType: (*ChannelServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddChannel",
			Handler:    _Channel_AddChannel_Handler,
		},
		{
			MethodName: "UpdateChannelInfo",
			Handler:    _Channel_UpdateChannelInfo_Handler,
		},
		{
			MethodName: "DismissChannel",
			Handler:    _Channel_DismissChannel_Handler,
		},
		{
			MethodName: "GetRecommendChannelList",
			Handler:    _Channel_GetRecommendChannelList_Handler,
		},
		{
			MethodName: "GetRecommendChannelListByTab",
			Handler:    _Channel_GetRecommendChannelListByTab_Handler,
		},
		{
			MethodName: "GetChannelByIds",
			Handler:    _Channel_GetChannelByIds_Handler,
		},
		{
			MethodName: "DismissTab",
			Handler:    _Channel_DismissTab_Handler,
		},
		{
			MethodName: "KeepChannelAlive",
			Handler:    _Channel_KeepChannelAlive_Handler,
		},
		{
			MethodName: "DisappearChannel",
			Handler:    _Channel_DisappearChannel_Handler,
		},
		{
			MethodName: "SetTabConfigure",
			Handler:    _Channel_SetTabConfigure_Handler,
		},
		{
			MethodName: "GetOnlineInfo",
			Handler:    _Channel_GetOnlineInfo_Handler,
		},
		{
			MethodName: "FreezeChannel",
			Handler:    _Channel_FreezeChannel_Handler,
		},
		{
			MethodName: "UnfreezeChannel",
			Handler:    _Channel_UnfreezeChannel_Handler,
		},
		{
			MethodName: "GetChannelFreezeInfo",
			Handler:    _Channel_GetChannelFreezeInfo_Handler,
		},
		{
			MethodName: "GetChannelPlayModel",
			Handler:    _Channel_GetChannelPlayModel_Handler,
		},
		{
			MethodName: "SwitchChannelTabMq",
			Handler:    _Channel_SwitchChannelTabMq_Handler,
		},
		{
			MethodName: "GetChannelRoomUserNumber",
			Handler:    _Channel_GetChannelRoomUserNumber_Handler,
		},
		{
			MethodName: "AddTemporaryChannel",
			Handler:    _Channel_AddTemporaryChannel_Handler,
		},
		{
			MethodName: "SetExtraHistory",
			Handler:    _Channel_SetExtraHistory_Handler,
		},
		{
			MethodName: "GetExtraHistory",
			Handler:    _Channel_GetExtraHistory_Handler,
		},
		{
			MethodName: "SwitchChannelTab",
			Handler:    _Channel_SwitchChannelTab_Handler,
		},
		{
			MethodName: "SwitchChannelTabAndBC",
			Handler:    _Channel_SwitchChannelTabAndBC_Handler,
		},
		{
			MethodName: "UpdateTopicChannelInfo",
			Handler:    _Channel_UpdateTopicChannelInfo_Handler,
		},
		{
			MethodName: "UpdateLastEnterRoomTimeByUid",
			Handler:    _Channel_UpdateLastEnterRoomTimeByUid_Handler,
		},
		{
			MethodName: "GetLastEnterRoomTimeByUid",
			Handler:    _Channel_GetLastEnterRoomTimeByUid_Handler,
		},
		{
			MethodName: "UpdateLastEnterRoomTabIdByUid",
			Handler:    _Channel_UpdateLastEnterRoomTabIdByUid_Handler,
		},
		{
			MethodName: "GetLastEnterRoomTabIdByUid",
			Handler:    _Channel_GetLastEnterRoomTabIdByUid_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "topic_channel/topic-channel.proto",
}

func init() {
	proto.RegisterFile("topic_channel/topic-channel.proto", fileDescriptor_topic_channel_e065d1c544bfe311)
}

var fileDescriptor_topic_channel_e065d1c544bfe311 = []byte{
	// 3179 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x3a, 0x4b, 0x6f, 0x1b, 0xc9,
	0xd1, 0x26, 0x29, 0x51, 0x64, 0x91, 0x12, 0xa9, 0x96, 0x6c, 0xd3, 0xdc, 0xf5, 0x5a, 0x9e, 0xfd,
	0x3e, 0x47, 0xd6, 0xc6, 0xb2, 0xad, 0xb5, 0xd7, 0xbb, 0xd9, 0xc5, 0x26, 0xd4, 0xc3, 0x32, 0xb3,
	0x92, 0x2c, 0x0f, 0x69, 0x2f, 0xf6, 0x85, 0x41, 0x73, 0xa6, 0x25, 0x4d, 0x34, 0x8f, 0xf6, 0x74,
	0x53, 0xb2, 0x02, 0x04, 0x58, 0x20, 0x40, 0x80, 0x9c, 0x02, 0xe4, 0x98, 0x43, 0x80, 0x5c, 0x72,
	0xc9, 0x31, 0xa7, 0xdc, 0x72, 0xcb, 0x3f, 0x48, 0x90, 0x20, 0x97, 0x1c, 0xf3, 0x2f, 0x82, 0xee,
	0x9e, 0x21, 0x87, 0xe4, 0x0c, 0x25, 0x2a, 0x9b, 0x93, 0xd8, 0xd5, 0xf5, 0xea, 0xea, 0xaa, 0xea,
	0xaa, 0x1a, 0xc1, 0x6d, 0xee, 0x53, 0xdb, 0x34, 0xcc, 0x23, 0xec, 0x79, 0xc4, 0xb9, 0x2f, 0x57,
	0xf7, 0xc2, 0xd5, 0x2a, 0x0d, 0x7c, 0xee, 0xa3, 0xab, 0x03, 0x28, 0xab, 0xe1, 0x5f, 0xed, 0x4b,
	0xa8, 0xac, 0x3b, 0xbe, 0x79, 0xfc, 0x9c, 0x72, 0xdb, 0xf7, 0x76, 0x6c, 0xc6, 0xd1, 0x36, 0xcc,
	0x76, 0x04, 0xc8, 0xf0, 0x25, 0x8c, 0xd5, 0x32, 0x4b, 0xb9, 0xe5, 0xd2, 0x9a, 0xb6, 0x9a, 0xc8,
	0x61, 0x35, 0x46, 0xae, 0x97, 0x3b, 0xfd, 0x05, 0xd3, 0xbe, 0x86, 0x52, 0x6c, 0x13, 0xdd, 0x80,
	0x82, 0xe2, 0x6b, 0x5b, 0xb5, 0xcc, 0x52, 0x66, 0x79, 0x56, 0x9f, 0x91, 0xeb, 0xa6, 0x85, 0xae,
	0xc3, 0x0c, 0x71, 0x88, 0x2b, 0x76, 0xb2, 0x72, 0x27, 0x2f, 0x96, 0x4d, 0x4b, 0xd0, 0xc8, 0x8d,
	0x13, 0xec, 0xd4, 0x72, 0x4b, 0x99, 0xe5, 0xa2, 0x2e, 0x11, 0x5f, 0x61, 0x47, 0xfb, 0x36, 0x0f,
	0xa5, 0x0d, 0xa5, 0x43, 0xd3, 0x3b, 0xf0, 0xd1, 0x1c, 0x64, 0x7b, 0x8c, 0xb3, 0xb6, 0x85, 0xae,
	0x42, 0x9e, 0xe3, 0x4e, 0x9f, 0xe5, 0x34, 0xc7, 0x9d, 0xa6, 0x85, 0x6e, 0x41, 0xc9, 0x0c, 0x08,
	0xe6, 0xc4, 0xe0, 0xb6, 0x4b, 0x24, 0xd3, 0x9c, 0x0e, 0x0a, 0xd4, 0xb6, 0x5d, 0x82, 0x6e, 0x02,
	0xb8, 0xd8, 0x21, 0x86, 0xe9, 0x77, 0x3d, 0x5e, 0x9b, 0x92, 0xb4, 0x45, 0x01, 0xd9, 0x10, 0x00,
	0x74, 0x1b, 0xca, 0x07, 0x24, 0x86, 0x30, 0x2d, 0x11, 0x4a, 0x0a, 0xa6, 0x50, 0x1e, 0xc0, 0xa2,
	0xcd, 0x8c, 0x80, 0x98, 0xbe, 0xeb, 0x12, 0xcf, 0x8a, 0x2c, 0x56, 0xcb, 0x2f, 0x65, 0x96, 0x0b,
	0x3a, 0xb2, 0x99, 0x1e, 0x6d, 0x85, 0xfa, 0xa3, 0x1a, 0xcc, 0x48, 0x0d, 0xfc, 0xa0, 0x36, 0xa3,
	0x2c, 0x13, 0x2e, 0x85, 0xba, 0xdc, 0xe7, 0xd8, 0x09, 0xa5, 0x15, 0xe4, 0x2e, 0x48, 0x90, 0x12,
	0x76, 0x13, 0x80, 0x99, 0xd8, 0x33, 0x28, 0x0e, 0xb0, 0x5b, 0x2b, 0x4a, 0x1b, 0x15, 0x05, 0x64,
	0x5f, 0x00, 0x46, 0x2f, 0x13, 0x2e, 0x77, 0x99, 0x42, 0x8e, 0xcd, 0x0c, 0x1a, 0xd8, 0x27, 0x98,
	0x93, 0x5a, 0x49, 0x1e, 0xa5, 0x68, 0xb3, 0x7d, 0x05, 0x40, 0x4b, 0x50, 0xf6, 0x3d, 0xc3, 0x15,
	0x2c, 0xa5, 0xa2, 0x65, 0xa5, 0xa8, 0xef, 0xed, 0xda, 0xa6, 0x52, 0x14, 0xc1, 0x94, 0x87, 0x5d,
	0x52, 0x9b, 0x95, 0x2a, 0xca, 0xdf, 0x68, 0x07, 0xca, 0x96, 0xcd, 0xa8, 0x83, 0xcf, 0x0c, 0x7e,
	0x46, 0x49, 0x6d, 0x6e, 0x29, 0xb7, 0x3c, 0xb7, 0x76, 0x37, 0x45, 0xb9, 0xd0, 0x5a, 0x9b, 0x8a,
	0xa2, 0x7d, 0x46, 0x89, 0x5e, 0xb2, 0xfa, 0x0b, 0xa1, 0xe2, 0x29, 0xf6, 0xb8, 0x71, 0x10, 0x10,
	0x76, 0x54, 0xab, 0x28, 0x15, 0x05, 0xe4, 0xa9, 0x00, 0x88, 0xed, 0x80, 0x38, 0x04, 0x33, 0x62,
	0xd8, 0xb4, 0x56, 0x55, 0x96, 0x0a, 0x21, 0x4d, 0x8a, 0x34, 0x98, 0x65, 0x47, 0xfe, 0xa9, 0x71,
	0x48, 0x7c, 0xc3, 0xf6, 0x0e, 0xfc, 0xda, 0xbc, 0x64, 0x50, 0x12, 0xc0, 0x6d, 0xe2, 0x4b, 0x1f,
	0x7b, 0x0b, 0x8a, 0x2e, 0x0e, 0x8e, 0x09, 0x17, 0x6e, 0x85, 0xe4, 0x11, 0x0b, 0x0a, 0xd0, 0xb4,
	0xd0, 0xbb, 0x30, 0xcb, 0x49, 0xe0, 0xda, 0x1e, 0x76, 0xd4, 0x69, 0x16, 0x24, 0x42, 0x39, 0x02,
	0x4a, 0x1d, 0x57, 0x60, 0xde, 0xc1, 0x8c, 0x1b, 0x96, 0xcd, 0x5c, 0x9b, 0x31, 0xe5, 0x84, 0x8b,
	0xd2, 0x09, 0x2b, 0x62, 0x63, 0x53, 0xc1, 0xa5, 0x27, 0xde, 0x82, 0x12, 0x3b, 0xb5, 0xb9, 0x79,
	0xa4, 0xb0, 0xae, 0x2a, 0x57, 0x55, 0x20, 0x81, 0xa0, 0xfd, 0x2b, 0x03, 0xb3, 0x0d, 0x2b, 0xf2,
	0x22, 0x9d, 0xbc, 0x46, 0x9f, 0xc0, 0x4c, 0xe4, 0x6d, 0x22, 0x12, 0xd2, 0x2f, 0x3a, 0x16, 0x39,
	0x7a, 0x44, 0x22, 0x8e, 0x67, 0x33, 0x89, 0x7a, 0x48, 0x64, 0xd4, 0x14, 0xf4, 0x82, 0xcd, 0x36,
	0xe4, 0x1a, 0x35, 0x61, 0xee, 0xc8, 0x77, 0x89, 0x41, 0xf1, 0x21, 0x51, 0xe7, 0x13, 0xb1, 0x33,
	0xb7, 0xf6, 0x6e, 0x8a, 0x84, 0x67, 0xbe, 0x4b, 0xf6, 0xf1, 0x21, 0x91, 0xf7, 0x54, 0x3e, 0x8a,
	0xad, 0x84, 0x11, 0xb0, 0xe3, 0x18, 0x8c, 0x38, 0xc4, 0xe4, 0xc4, 0x32, 0x3a, 0xb6, 0xc5, 0x6a,
	0x53, 0x4b, 0xb9, 0xe5, 0x59, 0xbd, 0x82, 0x1d, 0xa7, 0x15, 0xc2, 0xd7, 0x6d, 0x8b, 0x69, 0x55,
	0x98, 0x8b, 0x1f, 0x91, 0x51, 0xed, 0x9f, 0x19, 0x58, 0x7c, 0x49, 0x2d, 0xcc, 0x49, 0xfc, 0x10,
	0xe4, 0xf5, 0x45, 0x33, 0xc0, 0x7f, 0x1f, 0xe0, 0x43, 0x41, 0x99, 0x1f, 0x09, 0xca, 0xe1, 0x68,
	0x98, 0x19, 0x89, 0x86, 0x58, 0xc4, 0x17, 0x06, 0x22, 0x5e, 0xbb, 0x0e, 0x57, 0x13, 0x4e, 0xc7,
	0xa8, 0xf6, 0x13, 0x98, 0x0f, 0xbd, 0x23, 0x76, 0xe1, 0x37, 0x01, 0x42, 0x83, 0xf7, 0xd3, 0x6a,
	0x31, 0x84, 0x34, 0x2d, 0xf4, 0x01, 0x4c, 0xc9, 0xab, 0xca, 0xca, 0xab, 0x4a, 0x73, 0x86, 0xc8,
	0xe9, 0xc4, 0x4d, 0x49, 0x7c, 0x6d, 0x15, 0xd0, 0xb0, 0x2c, 0x46, 0x85, 0xd2, 0xa1, 0xdf, 0x4a,
	0x49, 0x05, 0x3d, 0x5a, 0x6a, 0xef, 0xc3, 0xad, 0x6d, 0xc2, 0x87, 0xf3, 0x9a, 0x78, 0x4e, 0x76,
	0x7c, 0x6c, 0xed, 0xfa, 0x01, 0x41, 0x55, 0xc8, 0x79, 0x5d, 0x37, 0x54, 0x51, 0xfc, 0xd4, 0xfe,
	0x91, 0x85, 0x7a, 0x0a, 0x95, 0x38, 0x5a, 0x15, 0x72, 0xdd, 0xde, 0x99, 0xc4, 0x4f, 0xb4, 0x08,
	0xd3, 0x8e, 0xed, 0xda, 0x3c, 0xba, 0x4f, 0xb9, 0x40, 0x2d, 0x28, 0x3a, 0x3e, 0xb6, 0x0c, 0xd7,
	0x0f, 0x94, 0x4f, 0x96, 0xd6, 0x3e, 0x48, 0x39, 0xe8, 0x39, 0x3a, 0xea, 0x05, 0x27, 0xd2, 0xf6,
	0x1d, 0x28, 0x29, 0xdf, 0x31, 0x1c, 0x9b, 0xf1, 0xd0, 0x39, 0x8b, 0xd2, 0x81, 0xe4, 0x23, 0xb9,
	0x02, 0xf3, 0x9e, 0xcf, 0x0d, 0xd3, 0x21, 0x38, 0x30, 0x8e, 0x6c, 0xc6, 0xfd, 0xe0, 0x4c, 0xba,
	0x4a, 0x41, 0xaf, 0x78, 0x3e, 0xdf, 0x10, 0xf0, 0x67, 0x0a, 0xdc, 0xc3, 0x3d, 0x22, 0xe6, 0x71,
	0x0f, 0x37, 0xdf, 0xc7, 0x15, 0xf0, 0x08, 0x77, 0x19, 0xaa, 0x02, 0x97, 0xe1, 0x13, 0xd2, 0x43,
	0x9d, 0x91, 0xa8, 0x73, 0x9e, 0xcf, 0x5b, 0xf8, 0x84, 0x44, 0x98, 0x32, 0x9d, 0xf1, 0x6e, 0xe0,
	0x19, 0xf4, 0xd0, 0x0c, 0x9d, 0xa8, 0xa8, 0x20, 0xfb, 0x87, 0xa6, 0xf6, 0xa7, 0x0c, 0xbc, 0x95,
	0x6a, 0x5c, 0x46, 0xd1, 0x16, 0x94, 0x23, 0xc7, 0x91, 0x27, 0x1c, 0xff, 0xc8, 0xc7, 0x7d, 0xb1,
	0x64, 0xf6, 0x59, 0x0d, 0x1a, 0x3f, 0xfb, 0xdd, 0x18, 0x5f, 0xfb, 0x77, 0x06, 0x16, 0xb7, 0x09,
	0x17, 0xbb, 0xeb, 0x67, 0x6d, 0xdc, 0xe9, 0xf9, 0x50, 0x13, 0x66, 0x3c, 0x72, 0xda, 0xf1, 0x03,
	0x2f, 0x4c, 0x6f, 0xf7, 0xd3, 0x65, 0x8d, 0x50, 0x37, 0x39, 0x71, 0xf5, 0x88, 0x1e, 0x6d, 0xc0,
	0x14, 0xb3, 0xbd, 0xe3, 0x50, 0xe7, 0x89, 0xf9, 0x48, 0x62, 0xd4, 0x80, 0x5c, 0xc7, 0x3e, 0x0c,
	0x9d, 0x6e, 0x62, 0x1e, 0x82, 0x56, 0xfb, 0x4b, 0x06, 0x6a, 0x69, 0x18, 0xe8, 0x1a, 0xe4, 0xcd,
	0x6e, 0xc0, 0xfc, 0x40, 0x1e, 0x77, 0x4a, 0x0f, 0x57, 0xe2, 0xee, 0xe5, 0x2b, 0x72, 0x82, 0x9d,
	0x2e, 0x09, 0xa3, 0xa1, 0x28, 0x20, 0xaf, 0x04, 0xa0, 0xb7, 0x6d, 0x7b, 0x16, 0x79, 0x23, 0xb5,
	0x0b, 0xb7, 0x9b, 0x02, 0xd0, 0xdb, 0xee, 0x27, 0xc0, 0x9c, 0xda, 0x56, 0xa9, 0xe9, 0x3a, 0xcc,
	0xf0, 0x23, 0x62, 0x10, 0xcf, 0x0a, 0x1d, 0x3a, 0xcf, 0x8f, 0xc8, 0x96, 0x67, 0xf5, 0xe8, 0x5c,
	0xcc, 0xcd, 0x23, 0xe9, 0xc0, 0x45, 0x45, 0xb7, 0x2b, 0x00, 0xda, 0x9f, 0x73, 0xa9, 0x49, 0x40,
	0x9e, 0x6c, 0x92, 0x98, 0xee, 0xa7, 0xee, 0x5c, 0x3c, 0x75, 0x3f, 0x8b, 0x7b, 0xdb, 0x94, 0xb4,
	0xfa, 0x7b, 0x13, 0x58, 0x3d, 0x16, 0xdf, 0x2b, 0x30, 0x4f, 0xde, 0x98, 0x84, 0x72, 0x23, 0x96,
	0x3e, 0xa7, 0xd5, 0x13, 0xa4, 0x36, 0x36, 0x7a, 0x49, 0x74, 0xb0, 0xc4, 0xca, 0x9f, 0x5b, 0x62,
	0xcd, 0x5c, 0xb2, 0xc4, 0x4a, 0xcc, 0x29, 0x85, 0x09, 0x72, 0x4a, 0xf1, 0xe2, 0x39, 0x05, 0x92,
	0x72, 0x8a, 0xf6, 0xc7, 0x0c, 0x2c, 0x8d, 0xbf, 0xc2, 0xef, 0x2e, 0x73, 0x3c, 0x1b, 0xcd, 0x1c,
	0x97, 0xbb, 0x4b, 0xed, 0xb7, 0x19, 0x40, 0xdb, 0x24, 0xba, 0xb0, 0xf5, 0xb3, 0xa6, 0xc5, 0x42,
	0x5f, 0x13, 0x75, 0x45, 0x46, 0x5e, 0xaa, 0xf8, 0x89, 0x7e, 0x08, 0xd3, 0xe2, 0x75, 0x63, 0xb5,
	0xec, 0xa4, 0x75, 0xa6, 0xa2, 0x8b, 0xe5, 0x5c, 0xec, 0xa8, 0x86, 0xa4, 0x10, 0xe5, 0xdc, 0x86,
	0xe3, 0x88, 0x70, 0x65, 0x7e, 0x37, 0x30, 0x95, 0x6f, 0x16, 0xf5, 0x70, 0xa5, 0xed, 0xc2, 0xc2,
	0x88, 0x7e, 0x8c, 0x8a, 0xc7, 0x59, 0x16, 0x9a, 0x17, 0x37, 0xa0, 0xc4, 0xd7, 0xee, 0xc0, 0x6c,
	0xf4, 0x62, 0xab, 0xa8, 0xea, 0x47, 0x4b, 0x26, 0x16, 0x2d, 0xa2, 0x74, 0x8a, 0xe3, 0x31, 0xaa,
	0x71, 0x58, 0xf8, 0x8c, 0x10, 0x1a, 0xb2, 0x6c, 0x38, 0xf6, 0x09, 0xb9, 0x40, 0x11, 0xf1, 0x29,
	0xe4, 0x19, 0xc7, 0xbc, 0xcb, 0xc2, 0x32, 0xe2, 0x4e, 0x8a, 0xa6, 0x82, 0xb5, 0xe4, 0xd9, 0x92,
	0xd8, 0x7a, 0x48, 0xa5, 0x3d, 0x84, 0xc5, 0x51, 0xa9, 0x8c, 0x8a, 0xe6, 0xce, 0x66, 0x06, 0x16,
	0xeb, 0xa8, 0x9e, 0xb0, 0x99, 0xdc, 0xd6, 0xfe, 0x3a, 0x05, 0x0b, 0x9b, 0x36, 0xc3, 0x94, 0x12,
	0x1c, 0xc4, 0xca, 0x9d, 0xb7, 0xa0, 0x68, 0x3a, 0x36, 0xf1, 0x78, 0xa4, 0x68, 0x51, 0x2f, 0x28,
	0x40, 0xd3, 0x42, 0x77, 0xa1, 0x8a, 0xcd, 0xd7, 0x5d, 0x3b, 0x20, 0x86, 0xd5, 0x0d, 0xb0, 0x08,
	0x2a, 0xa9, 0xf1, 0x94, 0x5e, 0x09, 0xe1, 0x9b, 0x21, 0x18, 0x7d, 0x0e, 0xb3, 0xa2, 0xa6, 0xf6,
	0xbb, 0xdc, 0x20, 0x27, 0xc4, 0xe3, 0x32, 0x1e, 0x4a, 0x6b, 0x6b, 0xe9, 0x05, 0xd2, 0xb0, 0x2a,
	0xab, 0x6d, 0x45, 0xaf, 0x97, 0x43, 0x46, 0x5b, 0x82, 0x0f, 0xfa, 0x06, 0x2a, 0xc7, 0x84, 0x50,
	0x79, 0xa8, 0x90, 0x75, 0x49, 0xb2, 0x7e, 0x34, 0x01, 0xeb, 0xcf, 0x22, 0x0e, 0xfa, 0x5c, 0x8f,
	0x99, 0x62, 0xef, 0xc2, 0xd5, 0xa8, 0x87, 0x19, 0xd4, 0xbf, 0x2c, 0x85, 0x7c, 0x34, 0x81, 0x10,
	0x5d, 0xf1, 0x89, 0x8e, 0xb1, 0x10, 0x0c, 0xac, 0xa5, 0xb8, 0xfa, 0x23, 0x98, 0x09, 0xd7, 0xc2,
	0xb8, 0x91, 0xc4, 0x9e, 0x71, 0xd5, 0xa3, 0x54, 0x09, 0xe1, 0x91, 0x71, 0xeb, 0xdf, 0x40, 0xb1,
	0x77, 0x02, 0x74, 0x0f, 0x50, 0x6f, 0x31, 0x4c, 0x39, 0xdf, 0xdb, 0xe9, 0x5d, 0xcc, 0x6d, 0x28,
	0xbb, 0xc4, 0xed, 0x90, 0x20, 0x7c, 0x9d, 0xd4, 0xab, 0x50, 0x52, 0x30, 0xf9, 0x3e, 0xd5, 0x3f,
	0x86, 0xb9, 0x41, 0xdd, 0x27, 0xd0, 0x4d, 0x7b, 0x02, 0x8b, 0xa3, 0xc6, 0x60, 0x54, 0x8e, 0x05,
	0x7a, 0x21, 0x10, 0x25, 0x0d, 0xe8, 0xc5, 0x00, 0xd3, 0x7e, 0x97, 0x85, 0x72, 0x1b, 0x77, 0x36,
	0x7c, 0xef, 0xc0, 0x3e, 0xec, 0x06, 0x24, 0x25, 0xe8, 0x84, 0x87, 0xca, 0xc7, 0x22, 0xe8, 0x3a,
	0x44, 0xe6, 0x99, 0xa2, 0x5e, 0x10, 0x00, 0xbd, 0xeb, 0x10, 0xd1, 0x22, 0x8a, 0xba, 0xa1, 0xaf,
	0x65, 0x4e, 0x6a, 0x59, 0x16, 0xc0, 0x9e, 0x09, 0xee, 0x01, 0xb2, 0x22, 0x15, 0xfb, 0x98, 0x53,
	0xca, 0x62, 0xbd, 0x9d, 0x1e, 0xfa, 0x63, 0xb8, 0xde, 0xb1, 0x0f, 0x0d, 0xea, 0xfb, 0x8e, 0xd1,
	0xf1, 0xbb, 0x9e, 0x85, 0x83, 0xb3, 0xb0, 0x30, 0x50, 0xad, 0xcb, 0x62, 0xc7, 0x3e, 0xdc, 0xf7,
	0x7d, 0x67, 0x3d, 0xdc, 0x54, 0x35, 0xc2, 0x63, 0xb8, 0x1e, 0x60, 0xcf, 0x32, 0x18, 0xe1, 0xc3,
	0x64, 0xaa, 0x9f, 0x59, 0x14, 0xdb, 0x2d, 0xc2, 0x07, 0xc9, 0x44, 0x07, 0xec, 0x5b, 0xf6, 0xc1,
	0x99, 0x81, 0x55, 0x5b, 0x93, 0xd3, 0x0b, 0x0a, 0xd0, 0xe0, 0xda, 0x0b, 0x40, 0x2d, 0xc2, 0xe3,
	0x56, 0x12, 0x31, 0xfb, 0x31, 0xe4, 0x4d, 0xb9, 0x0e, 0x6b, 0xb6, 0xb4, 0x86, 0x71, 0x80, 0x2e,
	0x24, 0xd1, 0xae, 0xc2, 0xc2, 0x08, 0x4b, 0x46, 0xb5, 0x4f, 0xa1, 0xba, 0x4d, 0xf8, 0x73, 0xcf,
	0xb1, 0x3d, 0x12, 0xb5, 0x7f, 0x2b, 0x30, 0xef, 0x4b, 0x80, 0xd1, 0x65, 0x3d, 0xff, 0x51, 0x77,
	0x53, 0x51, 0x1b, 0x2f, 0x59, 0xe8, 0x43, 0xda, 0xd7, 0x30, 0x3f, 0x44, 0xcf, 0xa8, 0xcc, 0xee,
	0xbe, 0xef, 0x0e, 0x50, 0x16, 0x05, 0x44, 0xd5, 0x45, 0xcb, 0x50, 0x8d, 0xf3, 0x97, 0x6f, 0x5f,
	0x56, 0xfa, 0xc9, 0x5c, 0x9f, 0xbd, 0x78, 0xa7, 0xb4, 0xaf, 0xa0, 0xfa, 0x34, 0x20, 0xe4, 0xa7,
	0x24, 0x96, 0xb9, 0xee, 0x40, 0xa5, 0xef, 0x60, 0xfd, 0x87, 0x73, 0x56, 0x9f, 0xed, 0x39, 0x99,
	0x7c, 0x16, 0x6f, 0x41, 0xe9, 0x40, 0xd2, 0xaa, 0xa6, 0x3f, 0xab, 0x9a, 0x7e, 0x05, 0x92, 0x4d,
	0xff, 0x02, 0xcc, 0x0f, 0x31, 0x67, 0x54, 0xfb, 0x04, 0xd0, 0x4b, 0xef, 0xe0, 0x92, 0x32, 0x85,
	0x91, 0x47, 0xa8, 0x19, 0xd5, 0x3e, 0x84, 0xeb, 0xfd, 0x67, 0x4b, 0xc9, 0x8c, 0x6c, 0x3d, 0xfe,
	0xc5, 0xd0, 0x3e, 0x96, 0x35, 0x6d, 0x02, 0xa5, 0x8a, 0xb4, 0xf8, 0x01, 0x33, 0x23, 0x07, 0x34,
	0xa4, 0x17, 0x6d, 0xbd, 0xe1, 0x01, 0x0e, 0xeb, 0x92, 0xf0, 0x35, 0x3f, 0x26, 0x67, 0x61, 0xce,
	0x17, 0x3f, 0x45, 0xe5, 0xd8, 0xaf, 0x7f, 0x8b, 0xba, 0x5a, 0x88, 0x04, 0x42, 0xde, 0x50, 0xf1,
	0x06, 0xe0, 0x03, 0x4e, 0x82, 0x70, 0xc0, 0x57, 0x52, 0xb0, 0x86, 0x00, 0x85, 0x3e, 0x35, 0x28,
	0x80, 0x51, 0xed, 0x8e, 0xac, 0x22, 0xce, 0x95, 0xab, 0xbd, 0x27, 0x5f, 0xf3, 0x61, 0xf2, 0xbe,
	0x3a, 0x99, 0x98, 0x3a, 0xda, 0x13, 0xb8, 0xd6, 0xb7, 0xc4, 0xbe, 0x83, 0xcf, 0x76, 0x7d, 0xeb,
	0x22, 0x9d, 0xbb, 0xf6, 0x20, 0x6e, 0xfc, 0x18, 0x21, 0xa3, 0x69, 0xcf, 0xfd, 0x6f, 0xb2, 0x70,
	0xb5, 0x25, 0x87, 0x43, 0x21, 0x55, 0x1b, 0x77, 0x76, 0x5f, 0xa7, 0xd7, 0x07, 0x43, 0x1a, 0x64,
	0x87, 0x9f, 0xfd, 0x1b, 0x50, 0x10, 0x54, 0x72, 0x68, 0x17, 0xce, 0x5e, 0x39, 0xee, 0xec, 0x61,
	0x57, 0x24, 0x8f, 0x78, 0xa1, 0x33, 0xb7, 0x76, 0x33, 0x25, 0xa4, 0x5b, 0x12, 0x29, 0xaa, 0x83,
	0x12, 0x46, 0x48, 0xd3, 0x97, 0x1d, 0x21, 0x85, 0x8d, 0x44, 0xbe, 0xdf, 0x48, 0xbc, 0x0d, 0xe0,
	0x3b, 0x96, 0x11, 0x1e, 0x54, 0x4d, 0x5c, 0x0a, 0xbe, 0x63, 0xb5, 0xa5, 0x71, 0x6a, 0x70, 0x2d,
	0xc9, 0x36, 0x8c, 0x6a, 0x8f, 0x64, 0x9f, 0x1c, 0xf9, 0xbd, 0xef, 0xbb, 0x22, 0x8c, 0xf7, 0xba,
	0xe2, 0xbd, 0x19, 0xb6, 0x5d, 0xae, 0x6f, 0xec, 0xbf, 0x67, 0xe0, 0xed, 0x74, 0x32, 0x46, 0xd1,
	0x01, 0xcc, 0xc9, 0x64, 0x22, 0x73, 0x45, 0xac, 0xcc, 0xfb, 0x51, 0x7a, 0x8d, 0x9b, 0xca, 0x6c,
	0x35, 0x02, 0xc9, 0x20, 0x2a, 0x07, 0xb1, 0x55, 0xfd, 0x05, 0x94, 0xe3, 0xbb, 0x69, 0x77, 0xbd,
	0x02, 0xf3, 0x6a, 0x64, 0x25, 0xf5, 0xf1, 0x24, 0xeb, 0x30, 0xb9, 0x54, 0xe4, 0x46, 0x5f, 0xa2,
	0xf6, 0x0a, 0xae, 0x35, 0x2c, 0xab, 0x4d, 0x5c, 0xea, 0x07, 0x38, 0x38, 0xfb, 0xae, 0xc6, 0x8b,
	0xda, 0x0d, 0xb8, 0x9e, 0xc8, 0x97, 0x51, 0xed, 0x0f, 0x59, 0x58, 0x18, 0xbe, 0x9f, 0xe4, 0x7e,
	0x31, 0x36, 0x38, 0xcb, 0x0e, 0x8e, 0xca, 0x53, 0x7a, 0xc6, 0x41, 0x2f, 0x9f, 0x1a, 0xf6, 0xf2,
	0xbe, 0x2b, 0x4f, 0x4f, 0xe2, 0xca, 0x8b, 0x30, 0x8d, 0x29, 0x6d, 0x46, 0x1e, 0xa8, 0x16, 0xa8,
	0x0e, 0xbd, 0x71, 0x70, 0xe4, 0x81, 0xbd, 0xf1, 0xf0, 0xa8, 0xf3, 0x17, 0x2e, 0xe9, 0xfc, 0xda,
	0xef, 0x33, 0xb0, 0x38, 0x6a, 0x2d, 0x55, 0x51, 0xf7, 0x42, 0x36, 0x33, 0x18, 0xb2, 0xcb, 0x50,
	0x3d, 0x25, 0x8e, 0x29, 0x34, 0xe0, 0x6f, 0x78, 0xff, 0xf5, 0x2a, 0xea, 0x73, 0x21, 0xbc, 0xfd,
	0x46, 0x76, 0x59, 0xa2, 0xff, 0x77, 0x6d, 0xd3, 0x70, 0xfd, 0xc8, 0x90, 0x79, 0xd7, 0x36, 0x77,
	0xfd, 0x5e, 0x42, 0x90, 0xba, 0x2b, 0x3b, 0x0a, 0xee, 0x32, 0x1c, 0xa5, 0xed, 0x0f, 0x55, 0x0f,
	0x1d, 0xda, 0xfe, 0xb0, 0x69, 0x69, 0x2f, 0xe0, 0x86, 0x9a, 0x65, 0xb6, 0xc5, 0x11, 0x87, 0xc6,
	0xb5, 0xe7, 0x74, 0x1d, 0xc9, 0xd3, 0x5b, 0xed, 0x6d, 0xa8, 0xa7, 0xb1, 0x64, 0x54, 0xfb, 0x0a,
	0x6e, 0xa9, 0xdd, 0x1d, 0xcc, 0xf8, 0x96, 0xc7, 0x49, 0x20, 0x82, 0x43, 0x3c, 0x2b, 0xeb, 0x67,
	0x2f, 0x6d, 0x2b, 0xd9, 0xa5, 0xee, 0x40, 0x85, 0x08, 0x54, 0x43, 0x06, 0x6c, 0xec, 0xd9, 0x9d,
	0x25, 0x71, 0x0e, 0x9a, 0x06, 0x4b, 0xe3, 0x99, 0x33, 0xaa, 0x3d, 0x90, 0x69, 0x61, 0x02, 0xe9,
	0xda, 0x36, 0xdc, 0x1c, 0x43, 0xc1, 0x68, 0x92, 0x7a, 0x99, 0x24, 0xf5, 0xfc, 0x64, 0xf5, 0x84,
	0xd5, 0xc6, 0x1c, 0x3e, 0x65, 0x48, 0xae, 0x41, 0xf9, 0x75, 0xd7, 0xe6, 0x11, 0xf3, 0xf0, 0x19,
	0x1d, 0x80, 0x69, 0xef, 0xc2, 0xed, 0x73, 0x04, 0x32, 0xaa, 0x3d, 0x4c, 0x38, 0xde, 0x78, 0x95,
	0xb4, 0x27, 0xf0, 0xce, 0x38, 0x92, 0xd4, 0x17, 0x70, 0xe5, 0x08, 0xd0, 0x68, 0xef, 0x8e, 0x6a,
	0xb0, 0xb8, 0xd9, 0x6c, 0xed, 0xef, 0x34, 0xbe, 0x30, 0x1a, 0x6d, 0x63, 0xb7, 0xd1, 0xdc, 0x33,
	0xf6, 0x1b, 0xdb, 0x5b, 0xd5, 0x2b, 0x68, 0x16, 0x8a, 0x9b, 0xcd, 0xd6, 0x6e, 0xb3, 0xd5, 0xda,
	0xda, 0xac, 0x66, 0x50, 0x1d, 0xae, 0xc5, 0x10, 0x9f, 0x36, 0xf7, 0x36, 0x8d, 0xa7, 0x7a, 0x73,
	0x6b, 0x6f, 0xb3, 0x9a, 0x15, 0xa8, 0xed, 0xad, 0xdd, 0xfd, 0xe7, 0x7a, 0x43, 0xff, 0xa2, 0x9a,
	0x5b, 0x79, 0x00, 0xe5, 0x78, 0x7c, 0xa2, 0x12, 0xcc, 0x6c, 0x92, 0x03, 0xdc, 0x75, 0x78, 0xf5,
	0x0a, 0x2a, 0xc0, 0xd4, 0x36, 0x76, 0x49, 0x35, 0x83, 0x8a, 0x30, 0xbd, 0xdb, 0x65, 0xb6, 0x59,
	0xcd, 0xae, 0xfc, 0x18, 0x4a, 0xb1, 0x31, 0xbb, 0x20, 0x78, 0xe9, 0x1d, 0x7b, 0xfe, 0xa9, 0x57,
	0xbd, 0x22, 0xd0, 0x5a, 0xe4, 0x84, 0x04, 0xd5, 0x0c, 0x02, 0xc8, 0x6f, 0x60, 0xcf, 0x24, 0x8e,
	0x92, 0xa9, 0xa2, 0xbc, 0x8d, 0x3b, 0xd5, 0x9c, 0x60, 0xfb, 0xa2, 0x6b, 0xf3, 0xea, 0xd4, 0xca,
	0x2a, 0x54, 0x86, 0x7a, 0x6d, 0xc1, 0xa2, 0xb1, 0xd3, 0x7c, 0x25, 0x4e, 0x55, 0x85, 0xf2, 0x66,
	0xb3, 0xb5, 0xf1, 0x7c, 0x6f, 0x6f, 0x6b, 0xa3, 0x2d, 0x0e, 0xb6, 0xf2, 0x03, 0xc8, 0xab, 0xf4,
	0x25, 0xc4, 0x36, 0xf7, 0x5e, 0x35, 0x9a, 0x8d, 0x9d, 0xea, 0x15, 0x29, 0x4b, 0xdf, 0x6a, 0xb4,
	0xb7, 0x94, 0xdc, 0xd6, 0xe7, 0xcd, 0xf6, 0xc6, 0xb3, 0x6a, 0x56, 0x20, 0xed, 0xbf, 0x5c, 0xdf,
	0x69, 0xb6, 0x9e, 0x55, 0x73, 0x6b, 0x7f, 0xab, 0xc1, 0x4c, 0xf4, 0x99, 0xf2, 0x0b, 0x80, 0xfe,
	0xb7, 0x18, 0xf4, 0x7f, 0x29, 0x89, 0x6b, 0xe0, 0x8b, 0x54, 0xfd, 0xff, 0x2f, 0x80, 0xc5, 0x28,
	0xf2, 0x60, 0x7e, 0xe4, 0xab, 0x07, 0x4a, 0x9b, 0x07, 0x25, 0x7d, 0xfd, 0xa9, 0x7f, 0xff, 0xe2,
	0xc8, 0x8c, 0x22, 0xd2, 0x9b, 0x8d, 0x44, 0xc7, 0x59, 0x1e, 0xff, 0x71, 0x24, 0x76, 0xa4, 0xbb,
	0x17, 0xc4, 0x64, 0x14, 0x7d, 0x9b, 0x91, 0x65, 0x5c, 0xd2, 0x40, 0x0d, 0x3d, 0x9c, 0x6c, 0x4e,
	0x2e, 0x24, 0xaf, 0x4d, 0x4a, 0xc2, 0x28, 0xfa, 0x95, 0xaa, 0x54, 0x52, 0x67, 0x7a, 0x68, 0xc2,
	0x79, 0x7d, 0x34, 0xcb, 0xad, 0x3f, 0xb9, 0x14, 0x1d, 0xa3, 0xe8, 0x08, 0x2a, 0x43, 0xe3, 0x30,
	0x74, 0xf7, 0xdc, 0xaa, 0x28, 0x1a, 0xeb, 0xd5, 0x57, 0x2e, 0x8a, 0xca, 0xa8, 0x70, 0xd8, 0xfe,
	0x04, 0x2c, 0xd5, 0x61, 0x07, 0x86, 0x69, 0xa9, 0x0e, 0x3b, 0x38, 0x4a, 0x43, 0xc7, 0x50, 0x1d,
	0x1e, 0x6a, 0xa1, 0x95, 0x31, 0x83, 0xb1, 0xa1, 0x99, 0x5b, 0xfd, 0xbd, 0x0b, 0xe3, 0x2a, 0x61,
	0xc3, 0x53, 0x8b, 0x54, 0x61, 0x09, 0xb3, 0x9e, 0x54, 0x61, 0x89, 0xa3, 0x90, 0x23, 0xa8, 0x0c,
	0xb5, 0xdc, 0xa9, 0xd7, 0x33, 0xda, 0xed, 0xa7, 0x5e, 0x4f, 0x42, 0x17, 0x8f, 0x3a, 0x30, 0x3b,
	0xd0, 0x85, 0xa3, 0xef, 0xa5, 0xdf, 0xed, 0x40, 0xaf, 0x5f, 0x5f, 0xbe, 0x18, 0xa2, 0x92, 0x31,
	0xd0, 0x2e, 0xa7, 0xca, 0x18, 0xee, 0xd8, 0x53, 0x65, 0x8c, 0x74, 0xdf, 0xc2, 0x62, 0x43, 0xfd,
	0x73, 0xaa, 0xc5, 0x46, 0xbb, 0xf4, 0x54, 0x8b, 0x25, 0xb4, 0xe4, 0xe8, 0x54, 0x7e, 0x18, 0x1b,
	0x69, 0xac, 0xd1, 0xea, 0xb9, 0x41, 0x31, 0xd0, 0xbf, 0xd7, 0xef, 0x4f, 0x84, 0xcf, 0x28, 0xe2,
	0xf1, 0x11, 0x76, 0xaf, 0x1d, 0x45, 0xf7, 0xce, 0xe5, 0x13, 0xef, 0x79, 0xeb, 0xab, 0x93, 0xa0,
	0x33, 0x8a, 0x5e, 0x03, 0x1a, 0xed, 0xda, 0x50, 0x5a, 0xa6, 0x4f, 0x6c, 0x7e, 0xeb, 0xf7, 0x26,
	0xc0, 0x66, 0x14, 0xfd, 0x3c, 0x13, 0x9f, 0x5d, 0x0c, 0xf6, 0x62, 0x68, 0x6d, 0xe2, 0xe6, 0xed,
	0x75, 0xfd, 0xfd, 0x4b, 0x34, 0x7c, 0xc2, 0xdc, 0x09, 0xad, 0x52, 0xaa, 0xb9, 0x93, 0xdb, 0xb5,
	0x54, 0x73, 0xa7, 0x74, 0x61, 0x61, 0xe4, 0xc7, 0x27, 0x1b, 0xe3, 0x22, 0x7f, 0x68, 0x52, 0x32,
	0x2e, 0xf2, 0x47, 0x86, 0x25, 0xea, 0x09, 0xb8, 0x90, 0xa4, 0xed, 0x8b, 0x4b, 0x4a, 0x1a, 0xcb,
	0x1c, 0x43, 0x75, 0xf8, 0xa6, 0x53, 0x53, 0x67, 0x42, 0x07, 0x9a, 0x9a, 0x3a, 0x13, 0xfb, 0x2f,
	0x3a, 0x3a, 0x81, 0x69, 0x78, 0xd6, 0xfa, 0xc6, 0xff, 0x4e, 0xe2, 0xcf, 0xe0, 0x5a, 0x72, 0x3b,
	0x84, 0x1e, 0x8c, 0xad, 0x87, 0x12, 0x1a, 0xb2, 0xfa, 0xc3, 0x09, 0x29, 0xc2, 0xe2, 0x62, 0x5c,
	0x4f, 0x94, 0x5a, 0x5c, 0x9c, 0xd3, 0xa5, 0xa5, 0x16, 0x17, 0xe7, 0x35, 0x60, 0xe8, 0x17, 0x19,
	0xb8, 0x91, 0xda, 0x4f, 0xa1, 0x31, 0xc1, 0x98, 0xae, 0xcb, 0xa3, 0xc9, 0x89, 0x18, 0x45, 0xbf,
	0xce, 0xc0, 0xcd, 0xb1, 0xed, 0x11, 0x9a, 0xe4, 0x8c, 0xf1, 0x96, 0xa9, 0xfe, 0xe1, 0xe5, 0x08,
	0x19, 0x45, 0xbf, 0xcc, 0xc8, 0x7f, 0xb9, 0x49, 0xd3, 0xe8, 0xc2, 0x27, 0x1d, 0x50, 0xe7, 0xf1,
	0x25, 0xa8, 0x18, 0x5d, 0xff, 0xe8, 0xcb, 0x27, 0x87, 0xbe, 0x83, 0xbd, 0xc3, 0xd5, 0xc7, 0x6b,
	0x9c, 0xaf, 0x9a, 0xbe, 0x7b, 0x5f, 0xfe, 0xab, 0xaa, 0xe9, 0x3b, 0xf7, 0x19, 0x09, 0x4e, 0x6c,
	0x93, 0xb0, 0xfb, 0x83, 0xff, 0xd8, 0x1a, 0xfe, 0xed, 0xe4, 0x25, 0xe2, 0xfb, 0xff, 0x09, 0x00,
	0x00, 0xff, 0xff, 0x47, 0xbf, 0xde, 0x20, 0xf8, 0x2a, 0x00, 0x00,
}
