// Code generated by protoc-gen-gogo.
// source: src/channelmicsvr/channelmic.proto
// DO NOT EDIT!

/*
	Package ChannelMic is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/channelmicsvr/channelmic.proto

	It has these top-level messages:
		MicrSpaceInfo
		SimpleHoldMicrSpaceReq
		SimpleHoldMicrSpaceResp
		FakeHoldMicrSpaceReq
		FakeHoldMicrSpaceResp
		SimpleReleaseMicrSpaceReq
		SimpleReleaseMicrSpaceResp
		SimpleRandomHoldMicrSpaceReq
		SimpleRandomHoldMicrSpaceResp
		GetMicrListReq
		GetMicrListResp
		ChangeMicrophoneReq
		ChangeMicrophoneResp
		ResetMicSpaceListReq
		ResetMicSpaceListResp
		DisableChannelMicEntryReq
		DisableChannelMicEntryResp
		EnableChannelMicEntryReq
		EnableChannelMicEntryResp
		DisableAllEmptyMicrSpaceReq
		DisableAllEmptyMicrSpaceResp
		SetChannelMicSpaceStatusReq
		SetChannelMicSpaceStatusResp
		BatchSetChannelMicSpaceStatusReq
		BatchSetChannelMicSpaceStatusResp
		KickoutChannelMicReq
		KickoutChannelMicResp
		SetChannelMicModeReq
		SetChannelMicModeResp
		BatchGetChannelMicModeReq
		BatchGetChannelMicModeResp
		GetChannelMicModeReq
		GetChannelMicModeResp
		InitCreateMicrSpaceReq
		InitCreateMicrSpaceResp
		BatGetMicrListReq
		MicrData
		BatGetMicrListResp
		ReInitChannelMicDataReq
		ReInitChannelMicDataResp
*/
package ChannelMic

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// 麦位信息
type MicrSpaceInfo struct {
	MicId    uint32 `protobuf:"varint,1,req,name=mic_id,json=micId" json:"mic_id"`
	MicState uint32 `protobuf:"varint,2,opt,name=mic_state,json=micState" json:"mic_state"`
	MicUid   uint32 `protobuf:"varint,3,opt,name=mic_uid,json=micUid" json:"mic_uid"`
	MicTs    uint32 `protobuf:"varint,4,opt,name=mic_ts,json=micTs" json:"mic_ts"`
}

func (m *MicrSpaceInfo) Reset()                    { *m = MicrSpaceInfo{} }
func (m *MicrSpaceInfo) String() string            { return proto.CompactTextString(m) }
func (*MicrSpaceInfo) ProtoMessage()               {}
func (*MicrSpaceInfo) Descriptor() ([]byte, []int) { return fileDescriptorChannelmic, []int{0} }

func (m *MicrSpaceInfo) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *MicrSpaceInfo) GetMicState() uint32 {
	if m != nil {
		return m.MicState
	}
	return 0
}

func (m *MicrSpaceInfo) GetMicUid() uint32 {
	if m != nil {
		return m.MicUid
	}
	return 0
}

func (m *MicrSpaceInfo) GetMicTs() uint32 {
	if m != nil {
		return m.MicTs
	}
	return 0
}

// 上麦
type SimpleHoldMicrSpaceReq struct {
	ChannelId        uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	Uid              uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
	MicPosId         uint32 `protobuf:"varint,3,opt,name=mic_pos_id,json=micPosId" json:"mic_pos_id"`
	IsForce          bool   `protobuf:"varint,4,opt,name=is_force,json=isForce" json:"is_force"`
	ChannelDisplayId uint32 `protobuf:"varint,5,opt,name=channel_display_id,json=channelDisplayId" json:"channel_display_id"`
	ChannelType      uint32 `protobuf:"varint,6,opt,name=channel_type,json=channelType" json:"channel_type"`
	UserSex          uint32 `protobuf:"varint,7,opt,name=user_sex,json=userSex" json:"user_sex"`
	OpUid            uint32 `protobuf:"varint,8,opt,name=op_uid,json=opUid" json:"op_uid"`
}

func (m *SimpleHoldMicrSpaceReq) Reset()                    { *m = SimpleHoldMicrSpaceReq{} }
func (m *SimpleHoldMicrSpaceReq) String() string            { return proto.CompactTextString(m) }
func (*SimpleHoldMicrSpaceReq) ProtoMessage()               {}
func (*SimpleHoldMicrSpaceReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelmic, []int{1} }

func (m *SimpleHoldMicrSpaceReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SimpleHoldMicrSpaceReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SimpleHoldMicrSpaceReq) GetMicPosId() uint32 {
	if m != nil {
		return m.MicPosId
	}
	return 0
}

func (m *SimpleHoldMicrSpaceReq) GetIsForce() bool {
	if m != nil {
		return m.IsForce
	}
	return false
}

func (m *SimpleHoldMicrSpaceReq) GetChannelDisplayId() uint32 {
	if m != nil {
		return m.ChannelDisplayId
	}
	return 0
}

func (m *SimpleHoldMicrSpaceReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *SimpleHoldMicrSpaceReq) GetUserSex() uint32 {
	if m != nil {
		return m.UserSex
	}
	return 0
}

func (m *SimpleHoldMicrSpaceReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

type SimpleHoldMicrSpaceResp struct {
	OpenMicInfo  *MicrSpaceInfo   `protobuf:"bytes,1,opt,name=open_mic_info,json=openMicInfo" json:"open_mic_info,omitempty"`
	KickOutUid   uint32           `protobuf:"varint,2,opt,name=kick_out_uid,json=kickOutUid" json:"kick_out_uid"`
	AllMicList   []*MicrSpaceInfo `protobuf:"bytes,3,rep,name=all_mic_list,json=allMicList" json:"all_mic_list,omitempty"`
	ServerTimeMs uint64           `protobuf:"varint,4,opt,name=server_time_ms,json=serverTimeMs" json:"server_time_ms"`
}

func (m *SimpleHoldMicrSpaceResp) Reset()         { *m = SimpleHoldMicrSpaceResp{} }
func (m *SimpleHoldMicrSpaceResp) String() string { return proto.CompactTextString(m) }
func (*SimpleHoldMicrSpaceResp) ProtoMessage()    {}
func (*SimpleHoldMicrSpaceResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmic, []int{2}
}

func (m *SimpleHoldMicrSpaceResp) GetOpenMicInfo() *MicrSpaceInfo {
	if m != nil {
		return m.OpenMicInfo
	}
	return nil
}

func (m *SimpleHoldMicrSpaceResp) GetKickOutUid() uint32 {
	if m != nil {
		return m.KickOutUid
	}
	return 0
}

func (m *SimpleHoldMicrSpaceResp) GetAllMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.AllMicList
	}
	return nil
}

func (m *SimpleHoldMicrSpaceResp) GetServerTimeMs() uint64 {
	if m != nil {
		return m.ServerTimeMs
	}
	return 0
}

// 假上麦，触发麦位时间更新和麦位kafka
type FakeHoldMicrSpaceReq struct {
	ChannelId        uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	Uid              uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
	MicPosId         uint32 `protobuf:"varint,3,opt,name=mic_pos_id,json=micPosId" json:"mic_pos_id"`
	IsForce          bool   `protobuf:"varint,4,opt,name=is_force,json=isForce" json:"is_force"`
	ChannelDisplayId uint32 `protobuf:"varint,5,opt,name=channel_display_id,json=channelDisplayId" json:"channel_display_id"`
	ChannelType      uint32 `protobuf:"varint,6,opt,name=channel_type,json=channelType" json:"channel_type"`
	UserSex          uint32 `protobuf:"varint,7,opt,name=user_sex,json=userSex" json:"user_sex"`
	OpUid            uint32 `protobuf:"varint,8,opt,name=op_uid,json=opUid" json:"op_uid"`
}

func (m *FakeHoldMicrSpaceReq) Reset()                    { *m = FakeHoldMicrSpaceReq{} }
func (m *FakeHoldMicrSpaceReq) String() string            { return proto.CompactTextString(m) }
func (*FakeHoldMicrSpaceReq) ProtoMessage()               {}
func (*FakeHoldMicrSpaceReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelmic, []int{3} }

func (m *FakeHoldMicrSpaceReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *FakeHoldMicrSpaceReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FakeHoldMicrSpaceReq) GetMicPosId() uint32 {
	if m != nil {
		return m.MicPosId
	}
	return 0
}

func (m *FakeHoldMicrSpaceReq) GetIsForce() bool {
	if m != nil {
		return m.IsForce
	}
	return false
}

func (m *FakeHoldMicrSpaceReq) GetChannelDisplayId() uint32 {
	if m != nil {
		return m.ChannelDisplayId
	}
	return 0
}

func (m *FakeHoldMicrSpaceReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *FakeHoldMicrSpaceReq) GetUserSex() uint32 {
	if m != nil {
		return m.UserSex
	}
	return 0
}

func (m *FakeHoldMicrSpaceReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

type FakeHoldMicrSpaceResp struct {
	OpenMicInfo  *MicrSpaceInfo   `protobuf:"bytes,1,opt,name=open_mic_info,json=openMicInfo" json:"open_mic_info,omitempty"`
	KickOutUid   uint32           `protobuf:"varint,2,opt,name=kick_out_uid,json=kickOutUid" json:"kick_out_uid"`
	AllMicList   []*MicrSpaceInfo `protobuf:"bytes,3,rep,name=all_mic_list,json=allMicList" json:"all_mic_list,omitempty"`
	ServerTimeMs uint64           `protobuf:"varint,4,opt,name=server_time_ms,json=serverTimeMs" json:"server_time_ms"`
}

func (m *FakeHoldMicrSpaceResp) Reset()                    { *m = FakeHoldMicrSpaceResp{} }
func (m *FakeHoldMicrSpaceResp) String() string            { return proto.CompactTextString(m) }
func (*FakeHoldMicrSpaceResp) ProtoMessage()               {}
func (*FakeHoldMicrSpaceResp) Descriptor() ([]byte, []int) { return fileDescriptorChannelmic, []int{4} }

func (m *FakeHoldMicrSpaceResp) GetOpenMicInfo() *MicrSpaceInfo {
	if m != nil {
		return m.OpenMicInfo
	}
	return nil
}

func (m *FakeHoldMicrSpaceResp) GetKickOutUid() uint32 {
	if m != nil {
		return m.KickOutUid
	}
	return 0
}

func (m *FakeHoldMicrSpaceResp) GetAllMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.AllMicList
	}
	return nil
}

func (m *FakeHoldMicrSpaceResp) GetServerTimeMs() uint64 {
	if m != nil {
		return m.ServerTimeMs
	}
	return 0
}

// 下麦
type SimpleReleaseMicrSpaceReq struct {
	ChannelId        uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	Uid              uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
	SwitchFlag       uint32 `protobuf:"varint,3,req,name=switch_flag,json=switchFlag" json:"switch_flag"`
	ChannelDisplayId uint32 `protobuf:"varint,4,opt,name=channel_display_id,json=channelDisplayId" json:"channel_display_id"`
	ChannelType      uint32 `protobuf:"varint,5,opt,name=channel_type,json=channelType" json:"channel_type"`
	UserSex          int32  `protobuf:"varint,6,opt,name=user_sex,json=userSex" json:"user_sex"`
	OpUid            uint32 `protobuf:"varint,7,opt,name=op_uid,json=opUid" json:"op_uid"`
}

func (m *SimpleReleaseMicrSpaceReq) Reset()         { *m = SimpleReleaseMicrSpaceReq{} }
func (m *SimpleReleaseMicrSpaceReq) String() string { return proto.CompactTextString(m) }
func (*SimpleReleaseMicrSpaceReq) ProtoMessage()    {}
func (*SimpleReleaseMicrSpaceReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmic, []int{5}
}

func (m *SimpleReleaseMicrSpaceReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SimpleReleaseMicrSpaceReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SimpleReleaseMicrSpaceReq) GetSwitchFlag() uint32 {
	if m != nil {
		return m.SwitchFlag
	}
	return 0
}

func (m *SimpleReleaseMicrSpaceReq) GetChannelDisplayId() uint32 {
	if m != nil {
		return m.ChannelDisplayId
	}
	return 0
}

func (m *SimpleReleaseMicrSpaceReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *SimpleReleaseMicrSpaceReq) GetUserSex() int32 {
	if m != nil {
		return m.UserSex
	}
	return 0
}

func (m *SimpleReleaseMicrSpaceReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

type SimpleReleaseMicrSpaceResp struct {
	CloseMicInfo     *MicrSpaceInfo   `protobuf:"bytes,1,opt,name=close_mic_info,json=closeMicInfo" json:"close_mic_info,omitempty"`
	AllMicList       []*MicrSpaceInfo `protobuf:"bytes,2,rep,name=all_mic_list,json=allMicList" json:"all_mic_list,omitempty"`
	ServerTimeMs     uint64           `protobuf:"varint,3,opt,name=server_time_ms,json=serverTimeMs" json:"server_time_ms"`
	IsAutoDisableMic bool             `protobuf:"varint,4,opt,name=is_auto_disable_mic,json=isAutoDisableMic" json:"is_auto_disable_mic"`
}

func (m *SimpleReleaseMicrSpaceResp) Reset()         { *m = SimpleReleaseMicrSpaceResp{} }
func (m *SimpleReleaseMicrSpaceResp) String() string { return proto.CompactTextString(m) }
func (*SimpleReleaseMicrSpaceResp) ProtoMessage()    {}
func (*SimpleReleaseMicrSpaceResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmic, []int{6}
}

func (m *SimpleReleaseMicrSpaceResp) GetCloseMicInfo() *MicrSpaceInfo {
	if m != nil {
		return m.CloseMicInfo
	}
	return nil
}

func (m *SimpleReleaseMicrSpaceResp) GetAllMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.AllMicList
	}
	return nil
}

func (m *SimpleReleaseMicrSpaceResp) GetServerTimeMs() uint64 {
	if m != nil {
		return m.ServerTimeMs
	}
	return 0
}

func (m *SimpleReleaseMicrSpaceResp) GetIsAutoDisableMic() bool {
	if m != nil {
		return m.IsAutoDisableMic
	}
	return false
}

// 随机上空余的某个麦位
type SimpleRandomHoldMicrSpaceReq struct {
	ChannelId    uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	Uid          uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
	OptMinMicrId uint32 `protobuf:"varint,3,opt,name=opt_min_micr_id,json=optMinMicrId" json:"opt_min_micr_id"`
	OptMaxMicrId uint32 `protobuf:"varint,4,opt,name=opt_max_micr_id,json=optMaxMicrId" json:"opt_max_micr_id"`
}

func (m *SimpleRandomHoldMicrSpaceReq) Reset()         { *m = SimpleRandomHoldMicrSpaceReq{} }
func (m *SimpleRandomHoldMicrSpaceReq) String() string { return proto.CompactTextString(m) }
func (*SimpleRandomHoldMicrSpaceReq) ProtoMessage()    {}
func (*SimpleRandomHoldMicrSpaceReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmic, []int{7}
}

func (m *SimpleRandomHoldMicrSpaceReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SimpleRandomHoldMicrSpaceReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SimpleRandomHoldMicrSpaceReq) GetOptMinMicrId() uint32 {
	if m != nil {
		return m.OptMinMicrId
	}
	return 0
}

func (m *SimpleRandomHoldMicrSpaceReq) GetOptMaxMicrId() uint32 {
	if m != nil {
		return m.OptMaxMicrId
	}
	return 0
}

type SimpleRandomHoldMicrSpaceResp struct {
	OpenMicInfo  *MicrSpaceInfo   `protobuf:"bytes,1,opt,name=open_mic_info,json=openMicInfo" json:"open_mic_info,omitempty"`
	AllMicList   []*MicrSpaceInfo `protobuf:"bytes,2,rep,name=all_mic_list,json=allMicList" json:"all_mic_list,omitempty"`
	ServerTimeMs uint64           `protobuf:"varint,3,opt,name=server_time_ms,json=serverTimeMs" json:"server_time_ms"`
}

func (m *SimpleRandomHoldMicrSpaceResp) Reset()         { *m = SimpleRandomHoldMicrSpaceResp{} }
func (m *SimpleRandomHoldMicrSpaceResp) String() string { return proto.CompactTextString(m) }
func (*SimpleRandomHoldMicrSpaceResp) ProtoMessage()    {}
func (*SimpleRandomHoldMicrSpaceResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmic, []int{8}
}

func (m *SimpleRandomHoldMicrSpaceResp) GetOpenMicInfo() *MicrSpaceInfo {
	if m != nil {
		return m.OpenMicInfo
	}
	return nil
}

func (m *SimpleRandomHoldMicrSpaceResp) GetAllMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.AllMicList
	}
	return nil
}

func (m *SimpleRandomHoldMicrSpaceResp) GetServerTimeMs() uint64 {
	if m != nil {
		return m.ServerTimeMs
	}
	return 0
}

type GetMicrListReq struct {
	ChannelId      uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	OpUid          uint32 `protobuf:"varint,2,req,name=op_uid,json=opUid" json:"op_uid"`
	ForceLocalTime bool   `protobuf:"varint,3,opt,name=force_local_time,json=forceLocalTime" json:"force_local_time"`
}

func (m *GetMicrListReq) Reset()                    { *m = GetMicrListReq{} }
func (m *GetMicrListReq) String() string            { return proto.CompactTextString(m) }
func (*GetMicrListReq) ProtoMessage()               {}
func (*GetMicrListReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelmic, []int{9} }

func (m *GetMicrListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetMicrListReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *GetMicrListReq) GetForceLocalTime() bool {
	if m != nil {
		return m.ForceLocalTime
	}
	return false
}

type GetMicrListResp struct {
	ChannelId    uint32           `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	AllMicList   []*MicrSpaceInfo `protobuf:"bytes,2,rep,name=all_mic_list,json=allMicList" json:"all_mic_list,omitempty"`
	MicrMode     uint32           `protobuf:"varint,3,opt,name=micr_mode,json=micrMode" json:"micr_mode"`
	ServerTimeMs uint64           `protobuf:"varint,4,opt,name=server_time_ms,json=serverTimeMs" json:"server_time_ms"`
}

func (m *GetMicrListResp) Reset()                    { *m = GetMicrListResp{} }
func (m *GetMicrListResp) String() string            { return proto.CompactTextString(m) }
func (*GetMicrListResp) ProtoMessage()               {}
func (*GetMicrListResp) Descriptor() ([]byte, []int) { return fileDescriptorChannelmic, []int{10} }

func (m *GetMicrListResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetMicrListResp) GetAllMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.AllMicList
	}
	return nil
}

func (m *GetMicrListResp) GetMicrMode() uint32 {
	if m != nil {
		return m.MicrMode
	}
	return 0
}

func (m *GetMicrListResp) GetServerTimeMs() uint64 {
	if m != nil {
		return m.ServerTimeMs
	}
	return 0
}

// 换自己的麦的位置
type ChangeMicrophoneReq struct {
	OpUid            uint32         `protobuf:"varint,1,req,name=op_uid,json=opUid" json:"op_uid"`
	ChannelId        uint32         `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	ToMicInfo        *MicrSpaceInfo `protobuf:"bytes,3,req,name=to_mic_info,json=toMicInfo" json:"to_mic_info,omitempty"`
	SwitchFlag       uint32         `protobuf:"varint,4,req,name=switch_flag,json=switchFlag" json:"switch_flag"`
	ChannelDisplayId uint32         `protobuf:"varint,5,opt,name=channel_display_id,json=channelDisplayId" json:"channel_display_id"`
	ChannelType      uint32         `protobuf:"varint,6,opt,name=channel_type,json=channelType" json:"channel_type"`
}

func (m *ChangeMicrophoneReq) Reset()                    { *m = ChangeMicrophoneReq{} }
func (m *ChangeMicrophoneReq) String() string            { return proto.CompactTextString(m) }
func (*ChangeMicrophoneReq) ProtoMessage()               {}
func (*ChangeMicrophoneReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelmic, []int{11} }

func (m *ChangeMicrophoneReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *ChangeMicrophoneReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChangeMicrophoneReq) GetToMicInfo() *MicrSpaceInfo {
	if m != nil {
		return m.ToMicInfo
	}
	return nil
}

func (m *ChangeMicrophoneReq) GetSwitchFlag() uint32 {
	if m != nil {
		return m.SwitchFlag
	}
	return 0
}

func (m *ChangeMicrophoneReq) GetChannelDisplayId() uint32 {
	if m != nil {
		return m.ChannelDisplayId
	}
	return 0
}

func (m *ChangeMicrophoneReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

type ChangeMicrophoneResp struct {
	FromMicInfo  *MicrSpaceInfo   `protobuf:"bytes,1,req,name=from_mic_info,json=fromMicInfo" json:"from_mic_info,omitempty"`
	ToMicInfo    *MicrSpaceInfo   `protobuf:"bytes,2,req,name=to_mic_info,json=toMicInfo" json:"to_mic_info,omitempty"`
	ServerTimeMs uint64           `protobuf:"varint,3,opt,name=server_time_ms,json=serverTimeMs" json:"server_time_ms"`
	AllMicList   []*MicrSpaceInfo `protobuf:"bytes,4,rep,name=all_mic_list,json=allMicList" json:"all_mic_list,omitempty"`
	MicMode      uint32           `protobuf:"varint,5,opt,name=mic_mode,json=micMode" json:"mic_mode"`
}

func (m *ChangeMicrophoneResp) Reset()                    { *m = ChangeMicrophoneResp{} }
func (m *ChangeMicrophoneResp) String() string            { return proto.CompactTextString(m) }
func (*ChangeMicrophoneResp) ProtoMessage()               {}
func (*ChangeMicrophoneResp) Descriptor() ([]byte, []int) { return fileDescriptorChannelmic, []int{12} }

func (m *ChangeMicrophoneResp) GetFromMicInfo() *MicrSpaceInfo {
	if m != nil {
		return m.FromMicInfo
	}
	return nil
}

func (m *ChangeMicrophoneResp) GetToMicInfo() *MicrSpaceInfo {
	if m != nil {
		return m.ToMicInfo
	}
	return nil
}

func (m *ChangeMicrophoneResp) GetServerTimeMs() uint64 {
	if m != nil {
		return m.ServerTimeMs
	}
	return 0
}

func (m *ChangeMicrophoneResp) GetAllMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.AllMicList
	}
	return nil
}

func (m *ChangeMicrophoneResp) GetMicMode() uint32 {
	if m != nil {
		return m.MicMode
	}
	return 0
}

type ResetMicSpaceListReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *ResetMicSpaceListReq) Reset()                    { *m = ResetMicSpaceListReq{} }
func (m *ResetMicSpaceListReq) String() string            { return proto.CompactTextString(m) }
func (*ResetMicSpaceListReq) ProtoMessage()               {}
func (*ResetMicSpaceListReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelmic, []int{13} }

func (m *ResetMicSpaceListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ResetMicSpaceListResp struct {
	SuccessMicSpaceList []*MicrSpaceInfo `protobuf:"bytes,1,rep,name=success_mic_space_list,json=successMicSpaceList" json:"success_mic_space_list,omitempty"`
	AfterMicSpaceList   []*MicrSpaceInfo `protobuf:"bytes,2,rep,name=after_mic_space_list,json=afterMicSpaceList" json:"after_mic_space_list,omitempty"`
	AfterMicMode        uint32           `protobuf:"varint,3,req,name=after_mic_mode,json=afterMicMode" json:"after_mic_mode"`
	AfterServerTimeMs   uint64           `protobuf:"varint,4,req,name=after_server_time_ms,json=afterServerTimeMs" json:"after_server_time_ms"`
}

func (m *ResetMicSpaceListResp) Reset()                    { *m = ResetMicSpaceListResp{} }
func (m *ResetMicSpaceListResp) String() string            { return proto.CompactTextString(m) }
func (*ResetMicSpaceListResp) ProtoMessage()               {}
func (*ResetMicSpaceListResp) Descriptor() ([]byte, []int) { return fileDescriptorChannelmic, []int{14} }

func (m *ResetMicSpaceListResp) GetSuccessMicSpaceList() []*MicrSpaceInfo {
	if m != nil {
		return m.SuccessMicSpaceList
	}
	return nil
}

func (m *ResetMicSpaceListResp) GetAfterMicSpaceList() []*MicrSpaceInfo {
	if m != nil {
		return m.AfterMicSpaceList
	}
	return nil
}

func (m *ResetMicSpaceListResp) GetAfterMicMode() uint32 {
	if m != nil {
		return m.AfterMicMode
	}
	return 0
}

func (m *ResetMicSpaceListResp) GetAfterServerTimeMs() uint64 {
	if m != nil {
		return m.AfterServerTimeMs
	}
	return 0
}

// 关闭麦位入口
type DisableChannelMicEntryReq struct {
	OpUid          uint32 `protobuf:"varint,1,req,name=op_uid,json=opUid" json:"op_uid"`
	ChannelId      uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	DisableMicSize uint32 `protobuf:"varint,3,opt,name=disable_mic_size,json=disableMicSize" json:"disable_mic_size"`
	KickUid        uint32 `protobuf:"varint,4,opt,name=kick_uid,json=kickUid" json:"kick_uid"`
	MicPosId       uint32 `protobuf:"varint,5,opt,name=mic_pos_id,json=micPosId" json:"mic_pos_id"`
}

func (m *DisableChannelMicEntryReq) Reset()         { *m = DisableChannelMicEntryReq{} }
func (m *DisableChannelMicEntryReq) String() string { return proto.CompactTextString(m) }
func (*DisableChannelMicEntryReq) ProtoMessage()    {}
func (*DisableChannelMicEntryReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmic, []int{15}
}

func (m *DisableChannelMicEntryReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *DisableChannelMicEntryReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *DisableChannelMicEntryReq) GetDisableMicSize() uint32 {
	if m != nil {
		return m.DisableMicSize
	}
	return 0
}

func (m *DisableChannelMicEntryReq) GetKickUid() uint32 {
	if m != nil {
		return m.KickUid
	}
	return 0
}

func (m *DisableChannelMicEntryReq) GetMicPosId() uint32 {
	if m != nil {
		return m.MicPosId
	}
	return 0
}

type DisableChannelMicEntryResp struct {
	ChannelId    uint32           `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	MicPosId     uint32           `protobuf:"varint,2,opt,name=mic_pos_id,json=micPosId" json:"mic_pos_id"`
	AllMicList   []*MicrSpaceInfo `protobuf:"bytes,3,rep,name=all_mic_list,json=allMicList" json:"all_mic_list,omitempty"`
	ServerTimeMs uint64           `protobuf:"varint,4,opt,name=server_time_ms,json=serverTimeMs" json:"server_time_ms"`
}

func (m *DisableChannelMicEntryResp) Reset()         { *m = DisableChannelMicEntryResp{} }
func (m *DisableChannelMicEntryResp) String() string { return proto.CompactTextString(m) }
func (*DisableChannelMicEntryResp) ProtoMessage()    {}
func (*DisableChannelMicEntryResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmic, []int{16}
}

func (m *DisableChannelMicEntryResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *DisableChannelMicEntryResp) GetMicPosId() uint32 {
	if m != nil {
		return m.MicPosId
	}
	return 0
}

func (m *DisableChannelMicEntryResp) GetAllMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.AllMicList
	}
	return nil
}

func (m *DisableChannelMicEntryResp) GetServerTimeMs() uint64 {
	if m != nil {
		return m.ServerTimeMs
	}
	return 0
}

// 开启麦位入口
type EnableChannelMicEntryReq struct {
	OpUid     uint32 `protobuf:"varint,1,req,name=op_uid,json=opUid" json:"op_uid"`
	ChannelId uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	MicPosId  uint32 `protobuf:"varint,3,opt,name=mic_pos_id,json=micPosId" json:"mic_pos_id"`
	HoldMic   bool   `protobuf:"varint,4,opt,name=hold_mic,json=holdMic" json:"hold_mic"`
}

func (m *EnableChannelMicEntryReq) Reset()         { *m = EnableChannelMicEntryReq{} }
func (m *EnableChannelMicEntryReq) String() string { return proto.CompactTextString(m) }
func (*EnableChannelMicEntryReq) ProtoMessage()    {}
func (*EnableChannelMicEntryReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmic, []int{17}
}

func (m *EnableChannelMicEntryReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *EnableChannelMicEntryReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *EnableChannelMicEntryReq) GetMicPosId() uint32 {
	if m != nil {
		return m.MicPosId
	}
	return 0
}

func (m *EnableChannelMicEntryReq) GetHoldMic() bool {
	if m != nil {
		return m.HoldMic
	}
	return false
}

type EnableChannelMicEntryResp struct {
	ChannelId    uint32           `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	MicPosId     uint32           `protobuf:"varint,2,opt,name=mic_pos_id,json=micPosId" json:"mic_pos_id"`
	AllMicList   []*MicrSpaceInfo `protobuf:"bytes,3,rep,name=all_mic_list,json=allMicList" json:"all_mic_list,omitempty"`
	ServerTimeMs uint64           `protobuf:"varint,4,opt,name=server_time_ms,json=serverTimeMs" json:"server_time_ms"`
}

func (m *EnableChannelMicEntryResp) Reset()         { *m = EnableChannelMicEntryResp{} }
func (m *EnableChannelMicEntryResp) String() string { return proto.CompactTextString(m) }
func (*EnableChannelMicEntryResp) ProtoMessage()    {}
func (*EnableChannelMicEntryResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmic, []int{18}
}

func (m *EnableChannelMicEntryResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *EnableChannelMicEntryResp) GetMicPosId() uint32 {
	if m != nil {
		return m.MicPosId
	}
	return 0
}

func (m *EnableChannelMicEntryResp) GetAllMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.AllMicList
	}
	return nil
}

func (m *EnableChannelMicEntryResp) GetServerTimeMs() uint64 {
	if m != nil {
		return m.ServerTimeMs
	}
	return 0
}

// 对所有空麦位锁麦
type DisableAllEmptyMicrSpaceReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	OpUid     uint32 `protobuf:"varint,2,req,name=op_uid,json=opUid" json:"op_uid"`
}

func (m *DisableAllEmptyMicrSpaceReq) Reset()         { *m = DisableAllEmptyMicrSpaceReq{} }
func (m *DisableAllEmptyMicrSpaceReq) String() string { return proto.CompactTextString(m) }
func (*DisableAllEmptyMicrSpaceReq) ProtoMessage()    {}
func (*DisableAllEmptyMicrSpaceReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmic, []int{19}
}

func (m *DisableAllEmptyMicrSpaceReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *DisableAllEmptyMicrSpaceReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

type DisableAllEmptyMicrSpaceResp struct {
	DisableMicidList []uint32         `protobuf:"varint,1,rep,name=disable_micid_list,json=disableMicidList" json:"disable_micid_list,omitempty"`
	AllMicList       []*MicrSpaceInfo `protobuf:"bytes,2,rep,name=all_mic_list,json=allMicList" json:"all_mic_list,omitempty"`
	MicMode          uint32           `protobuf:"varint,3,req,name=mic_mode,json=micMode" json:"mic_mode"`
	ServerTimeMs     uint64           `protobuf:"varint,4,req,name=server_time_ms,json=serverTimeMs" json:"server_time_ms"`
}

func (m *DisableAllEmptyMicrSpaceResp) Reset()         { *m = DisableAllEmptyMicrSpaceResp{} }
func (m *DisableAllEmptyMicrSpaceResp) String() string { return proto.CompactTextString(m) }
func (*DisableAllEmptyMicrSpaceResp) ProtoMessage()    {}
func (*DisableAllEmptyMicrSpaceResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmic, []int{20}
}

func (m *DisableAllEmptyMicrSpaceResp) GetDisableMicidList() []uint32 {
	if m != nil {
		return m.DisableMicidList
	}
	return nil
}

func (m *DisableAllEmptyMicrSpaceResp) GetAllMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.AllMicList
	}
	return nil
}

func (m *DisableAllEmptyMicrSpaceResp) GetMicMode() uint32 {
	if m != nil {
		return m.MicMode
	}
	return 0
}

func (m *DisableAllEmptyMicrSpaceResp) GetServerTimeMs() uint64 {
	if m != nil {
		return m.ServerTimeMs
	}
	return 0
}

// 设置麦位状态 比如打开/关闭/禁言 麦位 (可以取代之前的麦位开启和麦位关闭命令)
type SetChannelMicSpaceStatusReq struct {
	ChannelId uint32         `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	OpUid     uint32         `protobuf:"varint,2,req,name=op_uid,json=opUid" json:"op_uid"`
	MicInfo   *MicrSpaceInfo `protobuf:"bytes,3,req,name=mic_info,json=micInfo" json:"mic_info,omitempty"`
}

func (m *SetChannelMicSpaceStatusReq) Reset()         { *m = SetChannelMicSpaceStatusReq{} }
func (m *SetChannelMicSpaceStatusReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelMicSpaceStatusReq) ProtoMessage()    {}
func (*SetChannelMicSpaceStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmic, []int{21}
}

func (m *SetChannelMicSpaceStatusReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelMicSpaceStatusReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *SetChannelMicSpaceStatusReq) GetMicInfo() *MicrSpaceInfo {
	if m != nil {
		return m.MicInfo
	}
	return nil
}

type SetChannelMicSpaceStatusResp struct {
	AllMicList   []*MicrSpaceInfo `protobuf:"bytes,1,rep,name=all_mic_list,json=allMicList" json:"all_mic_list,omitempty"`
	KickedUid    uint32           `protobuf:"varint,2,opt,name=kicked_uid,json=kickedUid" json:"kicked_uid"`
	ServerTimeMs uint64           `protobuf:"varint,3,opt,name=server_time_ms,json=serverTimeMs" json:"server_time_ms"`
	MicMode      uint32           `protobuf:"varint,4,opt,name=mic_mode,json=micMode" json:"mic_mode"`
}

func (m *SetChannelMicSpaceStatusResp) Reset()         { *m = SetChannelMicSpaceStatusResp{} }
func (m *SetChannelMicSpaceStatusResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelMicSpaceStatusResp) ProtoMessage()    {}
func (*SetChannelMicSpaceStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmic, []int{22}
}

func (m *SetChannelMicSpaceStatusResp) GetAllMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.AllMicList
	}
	return nil
}

func (m *SetChannelMicSpaceStatusResp) GetKickedUid() uint32 {
	if m != nil {
		return m.KickedUid
	}
	return 0
}

func (m *SetChannelMicSpaceStatusResp) GetServerTimeMs() uint64 {
	if m != nil {
		return m.ServerTimeMs
	}
	return 0
}

func (m *SetChannelMicSpaceStatusResp) GetMicMode() uint32 {
	if m != nil {
		return m.MicMode
	}
	return 0
}

type BatchSetChannelMicSpaceStatusReq struct {
	ChannelId uint32   `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	OpUid     uint32   `protobuf:"varint,2,req,name=op_uid,json=opUid" json:"op_uid"`
	MicIdList []uint32 `protobuf:"varint,3,rep,name=mic_id_list,json=micIdList" json:"mic_id_list,omitempty"`
	MicStatus uint32   `protobuf:"varint,4,req,name=mic_status,json=micStatus" json:"mic_status"`
}

func (m *BatchSetChannelMicSpaceStatusReq) Reset()         { *m = BatchSetChannelMicSpaceStatusReq{} }
func (m *BatchSetChannelMicSpaceStatusReq) String() string { return proto.CompactTextString(m) }
func (*BatchSetChannelMicSpaceStatusReq) ProtoMessage()    {}
func (*BatchSetChannelMicSpaceStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmic, []int{23}
}

func (m *BatchSetChannelMicSpaceStatusReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BatchSetChannelMicSpaceStatusReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *BatchSetChannelMicSpaceStatusReq) GetMicIdList() []uint32 {
	if m != nil {
		return m.MicIdList
	}
	return nil
}

func (m *BatchSetChannelMicSpaceStatusReq) GetMicStatus() uint32 {
	if m != nil {
		return m.MicStatus
	}
	return 0
}

type BatchSetChannelMicSpaceStatusResp struct {
	AllMicList     []*MicrSpaceInfo `protobuf:"bytes,1,rep,name=all_mic_list,json=allMicList" json:"all_mic_list,omitempty"`
	KickedMicrList []*MicrSpaceInfo `protobuf:"bytes,2,rep,name=kicked_micr_list,json=kickedMicrList" json:"kicked_micr_list,omitempty"`
	ServerTimeMs   uint64           `protobuf:"varint,3,opt,name=server_time_ms,json=serverTimeMs" json:"server_time_ms"`
	MicMode        uint32           `protobuf:"varint,4,opt,name=mic_mode,json=micMode" json:"mic_mode"`
}

func (m *BatchSetChannelMicSpaceStatusResp) Reset()         { *m = BatchSetChannelMicSpaceStatusResp{} }
func (m *BatchSetChannelMicSpaceStatusResp) String() string { return proto.CompactTextString(m) }
func (*BatchSetChannelMicSpaceStatusResp) ProtoMessage()    {}
func (*BatchSetChannelMicSpaceStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmic, []int{24}
}

func (m *BatchSetChannelMicSpaceStatusResp) GetAllMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.AllMicList
	}
	return nil
}

func (m *BatchSetChannelMicSpaceStatusResp) GetKickedMicrList() []*MicrSpaceInfo {
	if m != nil {
		return m.KickedMicrList
	}
	return nil
}

func (m *BatchSetChannelMicSpaceStatusResp) GetServerTimeMs() uint64 {
	if m != nil {
		return m.ServerTimeMs
	}
	return 0
}

func (m *BatchSetChannelMicSpaceStatusResp) GetMicMode() uint32 {
	if m != nil {
		return m.MicMode
	}
	return 0
}

// 踢下麦
type KickoutChannelMicReq struct {
	OpUid         uint32   `protobuf:"varint,1,req,name=op_uid,json=opUid" json:"op_uid"`
	ChannelId     uint32   `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	TargetUidList []uint32 `protobuf:"varint,3,rep,name=target_uid_list,json=targetUidList" json:"target_uid_list,omitempty"`
	BanSecond     uint32   `protobuf:"varint,4,opt,name=ban_second,json=banSecond" json:"ban_second"`
	SwitchFlag    uint32   `protobuf:"varint,5,req,name=switch_flag,json=switchFlag" json:"switch_flag"`
}

func (m *KickoutChannelMicReq) Reset()                    { *m = KickoutChannelMicReq{} }
func (m *KickoutChannelMicReq) String() string            { return proto.CompactTextString(m) }
func (*KickoutChannelMicReq) ProtoMessage()               {}
func (*KickoutChannelMicReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelmic, []int{25} }

func (m *KickoutChannelMicReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *KickoutChannelMicReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *KickoutChannelMicReq) GetTargetUidList() []uint32 {
	if m != nil {
		return m.TargetUidList
	}
	return nil
}

func (m *KickoutChannelMicReq) GetBanSecond() uint32 {
	if m != nil {
		return m.BanSecond
	}
	return 0
}

func (m *KickoutChannelMicReq) GetSwitchFlag() uint32 {
	if m != nil {
		return m.SwitchFlag
	}
	return 0
}

type KickoutChannelMicResp struct {
	ChannelId        uint32           `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	DisableMicIdList []uint32         `protobuf:"varint,2,rep,name=disable_mic_id_list,json=disableMicIdList" json:"disable_mic_id_list,omitempty"`
	KickoutMicList   []*MicrSpaceInfo `protobuf:"bytes,3,rep,name=kickout_mic_list,json=kickoutMicList" json:"kickout_mic_list,omitempty"`
	AllMicList       []*MicrSpaceInfo `protobuf:"bytes,4,rep,name=all_mic_list,json=allMicList" json:"all_mic_list,omitempty"`
	ServerTimeMs     uint64           `protobuf:"varint,5,opt,name=server_time_ms,json=serverTimeMs" json:"server_time_ms"`
}

func (m *KickoutChannelMicResp) Reset()                    { *m = KickoutChannelMicResp{} }
func (m *KickoutChannelMicResp) String() string            { return proto.CompactTextString(m) }
func (*KickoutChannelMicResp) ProtoMessage()               {}
func (*KickoutChannelMicResp) Descriptor() ([]byte, []int) { return fileDescriptorChannelmic, []int{26} }

func (m *KickoutChannelMicResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *KickoutChannelMicResp) GetDisableMicIdList() []uint32 {
	if m != nil {
		return m.DisableMicIdList
	}
	return nil
}

func (m *KickoutChannelMicResp) GetKickoutMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.KickoutMicList
	}
	return nil
}

func (m *KickoutChannelMicResp) GetAllMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.AllMicList
	}
	return nil
}

func (m *KickoutChannelMicResp) GetServerTimeMs() uint64 {
	if m != nil {
		return m.ServerTimeMs
	}
	return 0
}

// 修改mic模式
type SetChannelMicModeReq struct {
	Uid             uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ChannelId       uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	MicMode         uint32 `protobuf:"varint,3,req,name=mic_mode,json=micMode" json:"mic_mode"`
	IsDisableAllMic bool   `protobuf:"varint,4,opt,name=is_disable_all_mic,json=isDisableAllMic" json:"is_disable_all_mic"`
	IsNeedHoldMic   bool   `protobuf:"varint,5,opt,name=is_need_hold_mic,json=isNeedHoldMic" json:"is_need_hold_mic"`
}

func (m *SetChannelMicModeReq) Reset()                    { *m = SetChannelMicModeReq{} }
func (m *SetChannelMicModeReq) String() string            { return proto.CompactTextString(m) }
func (*SetChannelMicModeReq) ProtoMessage()               {}
func (*SetChannelMicModeReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelmic, []int{27} }

func (m *SetChannelMicModeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetChannelMicModeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelMicModeReq) GetMicMode() uint32 {
	if m != nil {
		return m.MicMode
	}
	return 0
}

func (m *SetChannelMicModeReq) GetIsDisableAllMic() bool {
	if m != nil {
		return m.IsDisableAllMic
	}
	return false
}

func (m *SetChannelMicModeReq) GetIsNeedHoldMic() bool {
	if m != nil {
		return m.IsNeedHoldMic
	}
	return false
}

type SetChannelMicModeResp struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	MicMode   uint32 `protobuf:"varint,2,req,name=mic_mode,json=micMode" json:"mic_mode"`
	// 因为本次模式切换 而变化的麦位信息
	DisableMicList []*MicrSpaceInfo `protobuf:"bytes,3,rep,name=disable_mic_list,json=disableMicList" json:"disable_mic_list,omitempty"`
	EnableMicList  []*MicrSpaceInfo `protobuf:"bytes,4,rep,name=enable_mic_list,json=enableMicList" json:"enable_mic_list,omitempty"`
	KickoutMicList []*MicrSpaceInfo `protobuf:"bytes,5,rep,name=kickout_mic_list,json=kickoutMicList" json:"kickout_mic_list,omitempty"`
	HoldMicInfo    *MicrSpaceInfo   `protobuf:"bytes,6,opt,name=hold_mic_info,json=holdMicInfo" json:"hold_mic_info,omitempty"`
	// 模式设置之后 当前的麦位信息
	AllMicList   []*MicrSpaceInfo `protobuf:"bytes,7,rep,name=all_mic_list,json=allMicList" json:"all_mic_list,omitempty"`
	ServerTimeMs uint64           `protobuf:"varint,8,opt,name=server_time_ms,json=serverTimeMs" json:"server_time_ms"`
	FromMicMode  uint32           `protobuf:"varint,9,opt,name=from_mic_mode,json=fromMicMode" json:"from_mic_mode"`
}

func (m *SetChannelMicModeResp) Reset()                    { *m = SetChannelMicModeResp{} }
func (m *SetChannelMicModeResp) String() string            { return proto.CompactTextString(m) }
func (*SetChannelMicModeResp) ProtoMessage()               {}
func (*SetChannelMicModeResp) Descriptor() ([]byte, []int) { return fileDescriptorChannelmic, []int{28} }

func (m *SetChannelMicModeResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelMicModeResp) GetMicMode() uint32 {
	if m != nil {
		return m.MicMode
	}
	return 0
}

func (m *SetChannelMicModeResp) GetDisableMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.DisableMicList
	}
	return nil
}

func (m *SetChannelMicModeResp) GetEnableMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.EnableMicList
	}
	return nil
}

func (m *SetChannelMicModeResp) GetKickoutMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.KickoutMicList
	}
	return nil
}

func (m *SetChannelMicModeResp) GetHoldMicInfo() *MicrSpaceInfo {
	if m != nil {
		return m.HoldMicInfo
	}
	return nil
}

func (m *SetChannelMicModeResp) GetAllMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.AllMicList
	}
	return nil
}

func (m *SetChannelMicModeResp) GetServerTimeMs() uint64 {
	if m != nil {
		return m.ServerTimeMs
	}
	return 0
}

func (m *SetChannelMicModeResp) GetFromMicMode() uint32 {
	if m != nil {
		return m.FromMicMode
	}
	return 0
}

// 批量获取mic模式
type BatchGetChannelMicModeReq struct {
	ChannelIdList []uint32 `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
}

func (m *BatchGetChannelMicModeReq) Reset()         { *m = BatchGetChannelMicModeReq{} }
func (m *BatchGetChannelMicModeReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelMicModeReq) ProtoMessage()    {}
func (*BatchGetChannelMicModeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmic, []int{29}
}

func (m *BatchGetChannelMicModeReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type BatchGetChannelMicModeResp struct {
	MicModeList []uint32 `protobuf:"varint,1,rep,name=mic_mode_list,json=micModeList" json:"mic_mode_list,omitempty"`
}

func (m *BatchGetChannelMicModeResp) Reset()         { *m = BatchGetChannelMicModeResp{} }
func (m *BatchGetChannelMicModeResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelMicModeResp) ProtoMessage()    {}
func (*BatchGetChannelMicModeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmic, []int{30}
}

func (m *BatchGetChannelMicModeResp) GetMicModeList() []uint32 {
	if m != nil {
		return m.MicModeList
	}
	return nil
}

type GetChannelMicModeReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *GetChannelMicModeReq) Reset()                    { *m = GetChannelMicModeReq{} }
func (m *GetChannelMicModeReq) String() string            { return proto.CompactTextString(m) }
func (*GetChannelMicModeReq) ProtoMessage()               {}
func (*GetChannelMicModeReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelmic, []int{31} }

func (m *GetChannelMicModeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelMicModeResp struct {
	MicMode uint32 `protobuf:"varint,1,req,name=mic_mode,json=micMode" json:"mic_mode"`
}

func (m *GetChannelMicModeResp) Reset()                    { *m = GetChannelMicModeResp{} }
func (m *GetChannelMicModeResp) String() string            { return proto.CompactTextString(m) }
func (*GetChannelMicModeResp) ProtoMessage()               {}
func (*GetChannelMicModeResp) Descriptor() ([]byte, []int) { return fileDescriptorChannelmic, []int{32} }

func (m *GetChannelMicModeResp) GetMicMode() uint32 {
	if m != nil {
		return m.MicMode
	}
	return 0
}

// 创建房间时初始化麦位
type InitCreateMicrSpaceReq struct {
	Uid              uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ChannelId        uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	MicMode          uint32 `protobuf:"varint,3,req,name=mic_mode,json=micMode" json:"mic_mode"`
	ChannelDisplayId uint32 `protobuf:"varint,4,opt,name=channel_display_id,json=channelDisplayId" json:"channel_display_id"`
	ChannelType      uint32 `protobuf:"varint,5,opt,name=channel_type,json=channelType" json:"channel_type"`
}

func (m *InitCreateMicrSpaceReq) Reset()         { *m = InitCreateMicrSpaceReq{} }
func (m *InitCreateMicrSpaceReq) String() string { return proto.CompactTextString(m) }
func (*InitCreateMicrSpaceReq) ProtoMessage()    {}
func (*InitCreateMicrSpaceReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmic, []int{33}
}

func (m *InitCreateMicrSpaceReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *InitCreateMicrSpaceReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *InitCreateMicrSpaceReq) GetMicMode() uint32 {
	if m != nil {
		return m.MicMode
	}
	return 0
}

func (m *InitCreateMicrSpaceReq) GetChannelDisplayId() uint32 {
	if m != nil {
		return m.ChannelDisplayId
	}
	return 0
}

func (m *InitCreateMicrSpaceReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

type InitCreateMicrSpaceResp struct {
}

func (m *InitCreateMicrSpaceResp) Reset()         { *m = InitCreateMicrSpaceResp{} }
func (m *InitCreateMicrSpaceResp) String() string { return proto.CompactTextString(m) }
func (*InitCreateMicrSpaceResp) ProtoMessage()    {}
func (*InitCreateMicrSpaceResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmic, []int{34}
}

// 一次最多请求20个房间，否则会被截断
type BatGetMicrListReq struct {
	ChannelIdList []uint32 `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
}

func (m *BatGetMicrListReq) Reset()                    { *m = BatGetMicrListReq{} }
func (m *BatGetMicrListReq) String() string            { return proto.CompactTextString(m) }
func (*BatGetMicrListReq) ProtoMessage()               {}
func (*BatGetMicrListReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelmic, []int{35} }

func (m *BatGetMicrListReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type MicrData struct {
	ChannelId    uint32           `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	AllMicList   []*MicrSpaceInfo `protobuf:"bytes,2,rep,name=all_mic_list,json=allMicList" json:"all_mic_list,omitempty"`
	MicrMode     uint32           `protobuf:"varint,3,opt,name=micr_mode,json=micrMode" json:"micr_mode"`
	ServerTimeMs uint64           `protobuf:"varint,4,opt,name=server_time_ms,json=serverTimeMs" json:"server_time_ms"`
}

func (m *MicrData) Reset()                    { *m = MicrData{} }
func (m *MicrData) String() string            { return proto.CompactTextString(m) }
func (*MicrData) ProtoMessage()               {}
func (*MicrData) Descriptor() ([]byte, []int) { return fileDescriptorChannelmic, []int{36} }

func (m *MicrData) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MicrData) GetAllMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.AllMicList
	}
	return nil
}

func (m *MicrData) GetMicrMode() uint32 {
	if m != nil {
		return m.MicrMode
	}
	return 0
}

func (m *MicrData) GetServerTimeMs() uint64 {
	if m != nil {
		return m.ServerTimeMs
	}
	return 0
}

type BatGetMicrListResp struct {
	MicDataList []*MicrData `protobuf:"bytes,1,rep,name=mic_data_list,json=micDataList" json:"mic_data_list,omitempty"`
}

func (m *BatGetMicrListResp) Reset()                    { *m = BatGetMicrListResp{} }
func (m *BatGetMicrListResp) String() string            { return proto.CompactTextString(m) }
func (*BatGetMicrListResp) ProtoMessage()               {}
func (*BatGetMicrListResp) Descriptor() ([]byte, []int) { return fileDescriptorChannelmic, []int{37} }

func (m *BatGetMicrListResp) GetMicDataList() []*MicrData {
	if m != nil {
		return m.MicDataList
	}
	return nil
}

// 切换玩法时，重新初始化麦位数据，包括麦位数，麦位状态等(之前是SetChannelMicMode，但是麦位模式逐渐淘汰，需要换成这个新的接口)
type ReInitChannelMicDataReq struct {
	Uid              uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Cid              uint32 `protobuf:"varint,2,req,name=cid" json:"cid"`
	MicNum           uint32 `protobuf:"varint,3,req,name=mic_num,json=micNum" json:"mic_num"`
	MicMode          uint32 `protobuf:"varint,4,req,name=mic_mode,json=micMode" json:"mic_mode"`
	SchemeId         uint32 `protobuf:"varint,5,req,name=scheme_id,json=schemeId" json:"scheme_id"`
	NotKickOutMic    bool   `protobuf:"varint,6,opt,name=not_kick_out_mic,json=notKickOutMic" json:"not_kick_out_mic"`
	NotUnlockMic     bool   `protobuf:"varint,7,opt,name=not_unlock_mic,json=notUnlockMic" json:"not_unlock_mic"`
	UnmuteMic        bool   `protobuf:"varint,8,opt,name=unmute_mic,json=unmuteMic" json:"unmute_mic"`
	ChannelDisplayId uint32 `protobuf:"varint,9,opt,name=channel_display_id,json=channelDisplayId" json:"channel_display_id"`
	ChannelType      uint32 `protobuf:"varint,10,opt,name=channel_type,json=channelType" json:"channel_type"`
	UseNewControlMic bool   `protobuf:"varint,11,opt,name=use_new_control_mic,json=useNewControlMic" json:"use_new_control_mic"`
}

func (m *ReInitChannelMicDataReq) Reset()         { *m = ReInitChannelMicDataReq{} }
func (m *ReInitChannelMicDataReq) String() string { return proto.CompactTextString(m) }
func (*ReInitChannelMicDataReq) ProtoMessage()    {}
func (*ReInitChannelMicDataReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmic, []int{38}
}

func (m *ReInitChannelMicDataReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReInitChannelMicDataReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *ReInitChannelMicDataReq) GetMicNum() uint32 {
	if m != nil {
		return m.MicNum
	}
	return 0
}

func (m *ReInitChannelMicDataReq) GetMicMode() uint32 {
	if m != nil {
		return m.MicMode
	}
	return 0
}

func (m *ReInitChannelMicDataReq) GetSchemeId() uint32 {
	if m != nil {
		return m.SchemeId
	}
	return 0
}

func (m *ReInitChannelMicDataReq) GetNotKickOutMic() bool {
	if m != nil {
		return m.NotKickOutMic
	}
	return false
}

func (m *ReInitChannelMicDataReq) GetNotUnlockMic() bool {
	if m != nil {
		return m.NotUnlockMic
	}
	return false
}

func (m *ReInitChannelMicDataReq) GetUnmuteMic() bool {
	if m != nil {
		return m.UnmuteMic
	}
	return false
}

func (m *ReInitChannelMicDataReq) GetChannelDisplayId() uint32 {
	if m != nil {
		return m.ChannelDisplayId
	}
	return 0
}

func (m *ReInitChannelMicDataReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *ReInitChannelMicDataReq) GetUseNewControlMic() bool {
	if m != nil {
		return m.UseNewControlMic
	}
	return false
}

type ReInitChannelMicDataResp struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	// 因为本次模式切换 而变化的麦位信息
	EnableMicList  []*MicrSpaceInfo `protobuf:"bytes,2,rep,name=enable_mic_list,json=enableMicList" json:"enable_mic_list,omitempty"`
	KickoutMicList []*MicrSpaceInfo `protobuf:"bytes,3,rep,name=kickout_mic_list,json=kickoutMicList" json:"kickout_mic_list,omitempty"`
	HoldMicInfo    *MicrSpaceInfo   `protobuf:"bytes,4,opt,name=hold_mic_info,json=holdMicInfo" json:"hold_mic_info,omitempty"`
	// 模式设置之后 当前的麦位信息
	AllMicList   []*MicrSpaceInfo `protobuf:"bytes,5,rep,name=all_mic_list,json=allMicList" json:"all_mic_list,omitempty"`
	ServerTimeMs uint64           `protobuf:"varint,6,opt,name=server_time_ms,json=serverTimeMs" json:"server_time_ms"`
	// 兼容之前的接口
	MicMode     uint32 `protobuf:"varint,7,req,name=mic_mode,json=micMode" json:"mic_mode"`
	FromMicMode uint32 `protobuf:"varint,8,opt,name=from_mic_mode,json=fromMicMode" json:"from_mic_mode"`
}

func (m *ReInitChannelMicDataResp) Reset()         { *m = ReInitChannelMicDataResp{} }
func (m *ReInitChannelMicDataResp) String() string { return proto.CompactTextString(m) }
func (*ReInitChannelMicDataResp) ProtoMessage()    {}
func (*ReInitChannelMicDataResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmic, []int{39}
}

func (m *ReInitChannelMicDataResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ReInitChannelMicDataResp) GetEnableMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.EnableMicList
	}
	return nil
}

func (m *ReInitChannelMicDataResp) GetKickoutMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.KickoutMicList
	}
	return nil
}

func (m *ReInitChannelMicDataResp) GetHoldMicInfo() *MicrSpaceInfo {
	if m != nil {
		return m.HoldMicInfo
	}
	return nil
}

func (m *ReInitChannelMicDataResp) GetAllMicList() []*MicrSpaceInfo {
	if m != nil {
		return m.AllMicList
	}
	return nil
}

func (m *ReInitChannelMicDataResp) GetServerTimeMs() uint64 {
	if m != nil {
		return m.ServerTimeMs
	}
	return 0
}

func (m *ReInitChannelMicDataResp) GetMicMode() uint32 {
	if m != nil {
		return m.MicMode
	}
	return 0
}

func (m *ReInitChannelMicDataResp) GetFromMicMode() uint32 {
	if m != nil {
		return m.FromMicMode
	}
	return 0
}

func init() {
	proto.RegisterType((*MicrSpaceInfo)(nil), "ChannelMic.MicrSpaceInfo")
	proto.RegisterType((*SimpleHoldMicrSpaceReq)(nil), "ChannelMic.SimpleHoldMicrSpaceReq")
	proto.RegisterType((*SimpleHoldMicrSpaceResp)(nil), "ChannelMic.SimpleHoldMicrSpaceResp")
	proto.RegisterType((*FakeHoldMicrSpaceReq)(nil), "ChannelMic.FakeHoldMicrSpaceReq")
	proto.RegisterType((*FakeHoldMicrSpaceResp)(nil), "ChannelMic.FakeHoldMicrSpaceResp")
	proto.RegisterType((*SimpleReleaseMicrSpaceReq)(nil), "ChannelMic.SimpleReleaseMicrSpaceReq")
	proto.RegisterType((*SimpleReleaseMicrSpaceResp)(nil), "ChannelMic.SimpleReleaseMicrSpaceResp")
	proto.RegisterType((*SimpleRandomHoldMicrSpaceReq)(nil), "ChannelMic.SimpleRandomHoldMicrSpaceReq")
	proto.RegisterType((*SimpleRandomHoldMicrSpaceResp)(nil), "ChannelMic.SimpleRandomHoldMicrSpaceResp")
	proto.RegisterType((*GetMicrListReq)(nil), "ChannelMic.GetMicrListReq")
	proto.RegisterType((*GetMicrListResp)(nil), "ChannelMic.GetMicrListResp")
	proto.RegisterType((*ChangeMicrophoneReq)(nil), "ChannelMic.ChangeMicrophoneReq")
	proto.RegisterType((*ChangeMicrophoneResp)(nil), "ChannelMic.ChangeMicrophoneResp")
	proto.RegisterType((*ResetMicSpaceListReq)(nil), "ChannelMic.ResetMicSpaceListReq")
	proto.RegisterType((*ResetMicSpaceListResp)(nil), "ChannelMic.ResetMicSpaceListResp")
	proto.RegisterType((*DisableChannelMicEntryReq)(nil), "ChannelMic.DisableChannelMicEntryReq")
	proto.RegisterType((*DisableChannelMicEntryResp)(nil), "ChannelMic.DisableChannelMicEntryResp")
	proto.RegisterType((*EnableChannelMicEntryReq)(nil), "ChannelMic.EnableChannelMicEntryReq")
	proto.RegisterType((*EnableChannelMicEntryResp)(nil), "ChannelMic.EnableChannelMicEntryResp")
	proto.RegisterType((*DisableAllEmptyMicrSpaceReq)(nil), "ChannelMic.DisableAllEmptyMicrSpaceReq")
	proto.RegisterType((*DisableAllEmptyMicrSpaceResp)(nil), "ChannelMic.DisableAllEmptyMicrSpaceResp")
	proto.RegisterType((*SetChannelMicSpaceStatusReq)(nil), "ChannelMic.SetChannelMicSpaceStatusReq")
	proto.RegisterType((*SetChannelMicSpaceStatusResp)(nil), "ChannelMic.SetChannelMicSpaceStatusResp")
	proto.RegisterType((*BatchSetChannelMicSpaceStatusReq)(nil), "ChannelMic.BatchSetChannelMicSpaceStatusReq")
	proto.RegisterType((*BatchSetChannelMicSpaceStatusResp)(nil), "ChannelMic.BatchSetChannelMicSpaceStatusResp")
	proto.RegisterType((*KickoutChannelMicReq)(nil), "ChannelMic.KickoutChannelMicReq")
	proto.RegisterType((*KickoutChannelMicResp)(nil), "ChannelMic.KickoutChannelMicResp")
	proto.RegisterType((*SetChannelMicModeReq)(nil), "ChannelMic.SetChannelMicModeReq")
	proto.RegisterType((*SetChannelMicModeResp)(nil), "ChannelMic.SetChannelMicModeResp")
	proto.RegisterType((*BatchGetChannelMicModeReq)(nil), "ChannelMic.BatchGetChannelMicModeReq")
	proto.RegisterType((*BatchGetChannelMicModeResp)(nil), "ChannelMic.BatchGetChannelMicModeResp")
	proto.RegisterType((*GetChannelMicModeReq)(nil), "ChannelMic.GetChannelMicModeReq")
	proto.RegisterType((*GetChannelMicModeResp)(nil), "ChannelMic.GetChannelMicModeResp")
	proto.RegisterType((*InitCreateMicrSpaceReq)(nil), "ChannelMic.InitCreateMicrSpaceReq")
	proto.RegisterType((*InitCreateMicrSpaceResp)(nil), "ChannelMic.InitCreateMicrSpaceResp")
	proto.RegisterType((*BatGetMicrListReq)(nil), "ChannelMic.BatGetMicrListReq")
	proto.RegisterType((*MicrData)(nil), "ChannelMic.MicrData")
	proto.RegisterType((*BatGetMicrListResp)(nil), "ChannelMic.BatGetMicrListResp")
	proto.RegisterType((*ReInitChannelMicDataReq)(nil), "ChannelMic.ReInitChannelMicDataReq")
	proto.RegisterType((*ReInitChannelMicDataResp)(nil), "ChannelMic.ReInitChannelMicDataResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for ChannelMic service

type ChannelMicClient interface {
	// 开麦(上麦)
	SimpleHoldMicrSpace(ctx context.Context, in *SimpleHoldMicrSpaceReq, opts ...grpc.CallOption) (*SimpleHoldMicrSpaceResp, error)
	// 关麦(下麦)
	SimpleReleaseMicrSpace(ctx context.Context, in *SimpleReleaseMicrSpaceReq, opts ...grpc.CallOption) (*SimpleReleaseMicrSpaceResp, error)
	// 获取麦位列表
	GetMicrList(ctx context.Context, in *GetMicrListReq, opts ...grpc.CallOption) (*GetMicrListResp, error)
	// 换麦位
	ChangeMicrophone(ctx context.Context, in *ChangeMicrophoneReq, opts ...grpc.CallOption) (*ChangeMicrophoneResp, error)
	// 重置麦位
	ResetMicSpaceList(ctx context.Context, in *ResetMicSpaceListReq, opts ...grpc.CallOption) (*ResetMicSpaceListResp, error)
	// 关闭麦位入口
	DisableChannelMicEntry(ctx context.Context, in *DisableChannelMicEntryReq, opts ...grpc.CallOption) (*DisableChannelMicEntryResp, error)
	// 开启麦位入口
	EnableChannelMicEntry(ctx context.Context, in *EnableChannelMicEntryReq, opts ...grpc.CallOption) (*EnableChannelMicEntryResp, error)
	// 对所有空麦位锁麦
	DisableAllEmptyMicrSpace(ctx context.Context, in *DisableAllEmptyMicrSpaceReq, opts ...grpc.CallOption) (*DisableAllEmptyMicrSpaceResp, error)
	// 设置麦位状态 比如打开/关闭/禁言 麦位 (可以取代之前的麦位开启和麦位关闭命令)
	SetChannelMicSpaceStatus(ctx context.Context, in *SetChannelMicSpaceStatusReq, opts ...grpc.CallOption) (*SetChannelMicSpaceStatusResp, error)
	// 设置麦位状态 比如打开/关闭/禁言 麦位 (可以取代之前的麦位开启和麦位关闭命令)
	KickoutChannelMic(ctx context.Context, in *KickoutChannelMicReq, opts ...grpc.CallOption) (*KickoutChannelMicResp, error)
	// 逐渐淘汰，需要换成ReInitChannelMicData这个新的接口
	SetChannelMicMode(ctx context.Context, in *SetChannelMicModeReq, opts ...grpc.CallOption) (*SetChannelMicModeResp, error)
	BatchGetChannelMicMode(ctx context.Context, in *BatchGetChannelMicModeReq, opts ...grpc.CallOption) (*BatchGetChannelMicModeResp, error)
	// 批量修改麦位的状态
	BatchSetChannelMicSpaceStatus(ctx context.Context, in *BatchSetChannelMicSpaceStatusReq, opts ...grpc.CallOption) (*BatchSetChannelMicSpaceStatusResp, error)
	// 获取麦模式
	GetChannelMicMode(ctx context.Context, in *GetChannelMicModeReq, opts ...grpc.CallOption) (*GetChannelMicModeResp, error)
	// 上随机的空麦位
	SimpleRandomHoldMicrSpace(ctx context.Context, in *SimpleRandomHoldMicrSpaceReq, opts ...grpc.CallOption) (*SimpleRandomHoldMicrSpaceResp, error)
	// 创建初始的麦位信息
	InitCreateMicrSpace(ctx context.Context, in *InitCreateMicrSpaceReq, opts ...grpc.CallOption) (*InitCreateMicrSpaceResp, error)
	// 假上麦，用于神秘人现身场景,请求上麦的人原来已经在麦位上了，触发更新kafka
	FakeHoldMicrSpace(ctx context.Context, in *FakeHoldMicrSpaceReq, opts ...grpc.CallOption) (*FakeHoldMicrSpaceResp, error)
	// 批量获取麦位列表,一次最多获取20个房间
	BatGetMicrList(ctx context.Context, in *BatGetMicrListReq, opts ...grpc.CallOption) (*BatGetMicrListResp, error)
	ReInitChannelMicData(ctx context.Context, in *ReInitChannelMicDataReq, opts ...grpc.CallOption) (*ReInitChannelMicDataResp, error)
}

type channelMicClient struct {
	cc *grpc.ClientConn
}

func NewChannelMicClient(cc *grpc.ClientConn) ChannelMicClient {
	return &channelMicClient{cc}
}

func (c *channelMicClient) SimpleHoldMicrSpace(ctx context.Context, in *SimpleHoldMicrSpaceReq, opts ...grpc.CallOption) (*SimpleHoldMicrSpaceResp, error) {
	out := new(SimpleHoldMicrSpaceResp)
	err := grpc.Invoke(ctx, "/ChannelMic.ChannelMic/SimpleHoldMicrSpace", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMicClient) SimpleReleaseMicrSpace(ctx context.Context, in *SimpleReleaseMicrSpaceReq, opts ...grpc.CallOption) (*SimpleReleaseMicrSpaceResp, error) {
	out := new(SimpleReleaseMicrSpaceResp)
	err := grpc.Invoke(ctx, "/ChannelMic.ChannelMic/SimpleReleaseMicrSpace", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMicClient) GetMicrList(ctx context.Context, in *GetMicrListReq, opts ...grpc.CallOption) (*GetMicrListResp, error) {
	out := new(GetMicrListResp)
	err := grpc.Invoke(ctx, "/ChannelMic.ChannelMic/GetMicrList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMicClient) ChangeMicrophone(ctx context.Context, in *ChangeMicrophoneReq, opts ...grpc.CallOption) (*ChangeMicrophoneResp, error) {
	out := new(ChangeMicrophoneResp)
	err := grpc.Invoke(ctx, "/ChannelMic.ChannelMic/ChangeMicrophone", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMicClient) ResetMicSpaceList(ctx context.Context, in *ResetMicSpaceListReq, opts ...grpc.CallOption) (*ResetMicSpaceListResp, error) {
	out := new(ResetMicSpaceListResp)
	err := grpc.Invoke(ctx, "/ChannelMic.ChannelMic/ResetMicSpaceList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMicClient) DisableChannelMicEntry(ctx context.Context, in *DisableChannelMicEntryReq, opts ...grpc.CallOption) (*DisableChannelMicEntryResp, error) {
	out := new(DisableChannelMicEntryResp)
	err := grpc.Invoke(ctx, "/ChannelMic.ChannelMic/DisableChannelMicEntry", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMicClient) EnableChannelMicEntry(ctx context.Context, in *EnableChannelMicEntryReq, opts ...grpc.CallOption) (*EnableChannelMicEntryResp, error) {
	out := new(EnableChannelMicEntryResp)
	err := grpc.Invoke(ctx, "/ChannelMic.ChannelMic/EnableChannelMicEntry", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMicClient) DisableAllEmptyMicrSpace(ctx context.Context, in *DisableAllEmptyMicrSpaceReq, opts ...grpc.CallOption) (*DisableAllEmptyMicrSpaceResp, error) {
	out := new(DisableAllEmptyMicrSpaceResp)
	err := grpc.Invoke(ctx, "/ChannelMic.ChannelMic/DisableAllEmptyMicrSpace", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMicClient) SetChannelMicSpaceStatus(ctx context.Context, in *SetChannelMicSpaceStatusReq, opts ...grpc.CallOption) (*SetChannelMicSpaceStatusResp, error) {
	out := new(SetChannelMicSpaceStatusResp)
	err := grpc.Invoke(ctx, "/ChannelMic.ChannelMic/SetChannelMicSpaceStatus", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMicClient) KickoutChannelMic(ctx context.Context, in *KickoutChannelMicReq, opts ...grpc.CallOption) (*KickoutChannelMicResp, error) {
	out := new(KickoutChannelMicResp)
	err := grpc.Invoke(ctx, "/ChannelMic.ChannelMic/KickoutChannelMic", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMicClient) SetChannelMicMode(ctx context.Context, in *SetChannelMicModeReq, opts ...grpc.CallOption) (*SetChannelMicModeResp, error) {
	out := new(SetChannelMicModeResp)
	err := grpc.Invoke(ctx, "/ChannelMic.ChannelMic/SetChannelMicMode", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMicClient) BatchGetChannelMicMode(ctx context.Context, in *BatchGetChannelMicModeReq, opts ...grpc.CallOption) (*BatchGetChannelMicModeResp, error) {
	out := new(BatchGetChannelMicModeResp)
	err := grpc.Invoke(ctx, "/ChannelMic.ChannelMic/BatchGetChannelMicMode", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMicClient) BatchSetChannelMicSpaceStatus(ctx context.Context, in *BatchSetChannelMicSpaceStatusReq, opts ...grpc.CallOption) (*BatchSetChannelMicSpaceStatusResp, error) {
	out := new(BatchSetChannelMicSpaceStatusResp)
	err := grpc.Invoke(ctx, "/ChannelMic.ChannelMic/BatchSetChannelMicSpaceStatus", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMicClient) GetChannelMicMode(ctx context.Context, in *GetChannelMicModeReq, opts ...grpc.CallOption) (*GetChannelMicModeResp, error) {
	out := new(GetChannelMicModeResp)
	err := grpc.Invoke(ctx, "/ChannelMic.ChannelMic/GetChannelMicMode", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMicClient) SimpleRandomHoldMicrSpace(ctx context.Context, in *SimpleRandomHoldMicrSpaceReq, opts ...grpc.CallOption) (*SimpleRandomHoldMicrSpaceResp, error) {
	out := new(SimpleRandomHoldMicrSpaceResp)
	err := grpc.Invoke(ctx, "/ChannelMic.ChannelMic/SimpleRandomHoldMicrSpace", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMicClient) InitCreateMicrSpace(ctx context.Context, in *InitCreateMicrSpaceReq, opts ...grpc.CallOption) (*InitCreateMicrSpaceResp, error) {
	out := new(InitCreateMicrSpaceResp)
	err := grpc.Invoke(ctx, "/ChannelMic.ChannelMic/InitCreateMicrSpace", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMicClient) FakeHoldMicrSpace(ctx context.Context, in *FakeHoldMicrSpaceReq, opts ...grpc.CallOption) (*FakeHoldMicrSpaceResp, error) {
	out := new(FakeHoldMicrSpaceResp)
	err := grpc.Invoke(ctx, "/ChannelMic.ChannelMic/FakeHoldMicrSpace", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMicClient) BatGetMicrList(ctx context.Context, in *BatGetMicrListReq, opts ...grpc.CallOption) (*BatGetMicrListResp, error) {
	out := new(BatGetMicrListResp)
	err := grpc.Invoke(ctx, "/ChannelMic.ChannelMic/BatGetMicrList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMicClient) ReInitChannelMicData(ctx context.Context, in *ReInitChannelMicDataReq, opts ...grpc.CallOption) (*ReInitChannelMicDataResp, error) {
	out := new(ReInitChannelMicDataResp)
	err := grpc.Invoke(ctx, "/ChannelMic.ChannelMic/ReInitChannelMicData", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for ChannelMic service

type ChannelMicServer interface {
	// 开麦(上麦)
	SimpleHoldMicrSpace(context.Context, *SimpleHoldMicrSpaceReq) (*SimpleHoldMicrSpaceResp, error)
	// 关麦(下麦)
	SimpleReleaseMicrSpace(context.Context, *SimpleReleaseMicrSpaceReq) (*SimpleReleaseMicrSpaceResp, error)
	// 获取麦位列表
	GetMicrList(context.Context, *GetMicrListReq) (*GetMicrListResp, error)
	// 换麦位
	ChangeMicrophone(context.Context, *ChangeMicrophoneReq) (*ChangeMicrophoneResp, error)
	// 重置麦位
	ResetMicSpaceList(context.Context, *ResetMicSpaceListReq) (*ResetMicSpaceListResp, error)
	// 关闭麦位入口
	DisableChannelMicEntry(context.Context, *DisableChannelMicEntryReq) (*DisableChannelMicEntryResp, error)
	// 开启麦位入口
	EnableChannelMicEntry(context.Context, *EnableChannelMicEntryReq) (*EnableChannelMicEntryResp, error)
	// 对所有空麦位锁麦
	DisableAllEmptyMicrSpace(context.Context, *DisableAllEmptyMicrSpaceReq) (*DisableAllEmptyMicrSpaceResp, error)
	// 设置麦位状态 比如打开/关闭/禁言 麦位 (可以取代之前的麦位开启和麦位关闭命令)
	SetChannelMicSpaceStatus(context.Context, *SetChannelMicSpaceStatusReq) (*SetChannelMicSpaceStatusResp, error)
	// 设置麦位状态 比如打开/关闭/禁言 麦位 (可以取代之前的麦位开启和麦位关闭命令)
	KickoutChannelMic(context.Context, *KickoutChannelMicReq) (*KickoutChannelMicResp, error)
	// 逐渐淘汰，需要换成ReInitChannelMicData这个新的接口
	SetChannelMicMode(context.Context, *SetChannelMicModeReq) (*SetChannelMicModeResp, error)
	BatchGetChannelMicMode(context.Context, *BatchGetChannelMicModeReq) (*BatchGetChannelMicModeResp, error)
	// 批量修改麦位的状态
	BatchSetChannelMicSpaceStatus(context.Context, *BatchSetChannelMicSpaceStatusReq) (*BatchSetChannelMicSpaceStatusResp, error)
	// 获取麦模式
	GetChannelMicMode(context.Context, *GetChannelMicModeReq) (*GetChannelMicModeResp, error)
	// 上随机的空麦位
	SimpleRandomHoldMicrSpace(context.Context, *SimpleRandomHoldMicrSpaceReq) (*SimpleRandomHoldMicrSpaceResp, error)
	// 创建初始的麦位信息
	InitCreateMicrSpace(context.Context, *InitCreateMicrSpaceReq) (*InitCreateMicrSpaceResp, error)
	// 假上麦，用于神秘人现身场景,请求上麦的人原来已经在麦位上了，触发更新kafka
	FakeHoldMicrSpace(context.Context, *FakeHoldMicrSpaceReq) (*FakeHoldMicrSpaceResp, error)
	// 批量获取麦位列表,一次最多获取20个房间
	BatGetMicrList(context.Context, *BatGetMicrListReq) (*BatGetMicrListResp, error)
	ReInitChannelMicData(context.Context, *ReInitChannelMicDataReq) (*ReInitChannelMicDataResp, error)
}

func RegisterChannelMicServer(s *grpc.Server, srv ChannelMicServer) {
	s.RegisterService(&_ChannelMic_serviceDesc, srv)
}

func _ChannelMic_SimpleHoldMicrSpace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SimpleHoldMicrSpaceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMicServer).SimpleHoldMicrSpace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ChannelMic.ChannelMic/SimpleHoldMicrSpace",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMicServer).SimpleHoldMicrSpace(ctx, req.(*SimpleHoldMicrSpaceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMic_SimpleReleaseMicrSpace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SimpleReleaseMicrSpaceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMicServer).SimpleReleaseMicrSpace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ChannelMic.ChannelMic/SimpleReleaseMicrSpace",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMicServer).SimpleReleaseMicrSpace(ctx, req.(*SimpleReleaseMicrSpaceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMic_GetMicrList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMicrListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMicServer).GetMicrList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ChannelMic.ChannelMic/GetMicrList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMicServer).GetMicrList(ctx, req.(*GetMicrListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMic_ChangeMicrophone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeMicrophoneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMicServer).ChangeMicrophone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ChannelMic.ChannelMic/ChangeMicrophone",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMicServer).ChangeMicrophone(ctx, req.(*ChangeMicrophoneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMic_ResetMicSpaceList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetMicSpaceListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMicServer).ResetMicSpaceList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ChannelMic.ChannelMic/ResetMicSpaceList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMicServer).ResetMicSpaceList(ctx, req.(*ResetMicSpaceListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMic_DisableChannelMicEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisableChannelMicEntryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMicServer).DisableChannelMicEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ChannelMic.ChannelMic/DisableChannelMicEntry",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMicServer).DisableChannelMicEntry(ctx, req.(*DisableChannelMicEntryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMic_EnableChannelMicEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EnableChannelMicEntryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMicServer).EnableChannelMicEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ChannelMic.ChannelMic/EnableChannelMicEntry",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMicServer).EnableChannelMicEntry(ctx, req.(*EnableChannelMicEntryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMic_DisableAllEmptyMicrSpace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisableAllEmptyMicrSpaceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMicServer).DisableAllEmptyMicrSpace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ChannelMic.ChannelMic/DisableAllEmptyMicrSpace",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMicServer).DisableAllEmptyMicrSpace(ctx, req.(*DisableAllEmptyMicrSpaceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMic_SetChannelMicSpaceStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelMicSpaceStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMicServer).SetChannelMicSpaceStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ChannelMic.ChannelMic/SetChannelMicSpaceStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMicServer).SetChannelMicSpaceStatus(ctx, req.(*SetChannelMicSpaceStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMic_KickoutChannelMic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(KickoutChannelMicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMicServer).KickoutChannelMic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ChannelMic.ChannelMic/KickoutChannelMic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMicServer).KickoutChannelMic(ctx, req.(*KickoutChannelMicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMic_SetChannelMicMode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelMicModeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMicServer).SetChannelMicMode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ChannelMic.ChannelMic/SetChannelMicMode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMicServer).SetChannelMicMode(ctx, req.(*SetChannelMicModeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMic_BatchGetChannelMicMode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetChannelMicModeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMicServer).BatchGetChannelMicMode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ChannelMic.ChannelMic/BatchGetChannelMicMode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMicServer).BatchGetChannelMicMode(ctx, req.(*BatchGetChannelMicModeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMic_BatchSetChannelMicSpaceStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchSetChannelMicSpaceStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMicServer).BatchSetChannelMicSpaceStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ChannelMic.ChannelMic/BatchSetChannelMicSpaceStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMicServer).BatchSetChannelMicSpaceStatus(ctx, req.(*BatchSetChannelMicSpaceStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMic_GetChannelMicMode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelMicModeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMicServer).GetChannelMicMode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ChannelMic.ChannelMic/GetChannelMicMode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMicServer).GetChannelMicMode(ctx, req.(*GetChannelMicModeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMic_SimpleRandomHoldMicrSpace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SimpleRandomHoldMicrSpaceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMicServer).SimpleRandomHoldMicrSpace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ChannelMic.ChannelMic/SimpleRandomHoldMicrSpace",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMicServer).SimpleRandomHoldMicrSpace(ctx, req.(*SimpleRandomHoldMicrSpaceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMic_InitCreateMicrSpace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitCreateMicrSpaceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMicServer).InitCreateMicrSpace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ChannelMic.ChannelMic/InitCreateMicrSpace",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMicServer).InitCreateMicrSpace(ctx, req.(*InitCreateMicrSpaceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMic_FakeHoldMicrSpace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FakeHoldMicrSpaceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMicServer).FakeHoldMicrSpace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ChannelMic.ChannelMic/FakeHoldMicrSpace",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMicServer).FakeHoldMicrSpace(ctx, req.(*FakeHoldMicrSpaceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMic_BatGetMicrList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetMicrListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMicServer).BatGetMicrList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ChannelMic.ChannelMic/BatGetMicrList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMicServer).BatGetMicrList(ctx, req.(*BatGetMicrListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMic_ReInitChannelMicData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReInitChannelMicDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMicServer).ReInitChannelMicData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ChannelMic.ChannelMic/ReInitChannelMicData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMicServer).ReInitChannelMicData(ctx, req.(*ReInitChannelMicDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelMic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ChannelMic.ChannelMic",
	HandlerType: (*ChannelMicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SimpleHoldMicrSpace",
			Handler:    _ChannelMic_SimpleHoldMicrSpace_Handler,
		},
		{
			MethodName: "SimpleReleaseMicrSpace",
			Handler:    _ChannelMic_SimpleReleaseMicrSpace_Handler,
		},
		{
			MethodName: "GetMicrList",
			Handler:    _ChannelMic_GetMicrList_Handler,
		},
		{
			MethodName: "ChangeMicrophone",
			Handler:    _ChannelMic_ChangeMicrophone_Handler,
		},
		{
			MethodName: "ResetMicSpaceList",
			Handler:    _ChannelMic_ResetMicSpaceList_Handler,
		},
		{
			MethodName: "DisableChannelMicEntry",
			Handler:    _ChannelMic_DisableChannelMicEntry_Handler,
		},
		{
			MethodName: "EnableChannelMicEntry",
			Handler:    _ChannelMic_EnableChannelMicEntry_Handler,
		},
		{
			MethodName: "DisableAllEmptyMicrSpace",
			Handler:    _ChannelMic_DisableAllEmptyMicrSpace_Handler,
		},
		{
			MethodName: "SetChannelMicSpaceStatus",
			Handler:    _ChannelMic_SetChannelMicSpaceStatus_Handler,
		},
		{
			MethodName: "KickoutChannelMic",
			Handler:    _ChannelMic_KickoutChannelMic_Handler,
		},
		{
			MethodName: "SetChannelMicMode",
			Handler:    _ChannelMic_SetChannelMicMode_Handler,
		},
		{
			MethodName: "BatchGetChannelMicMode",
			Handler:    _ChannelMic_BatchGetChannelMicMode_Handler,
		},
		{
			MethodName: "BatchSetChannelMicSpaceStatus",
			Handler:    _ChannelMic_BatchSetChannelMicSpaceStatus_Handler,
		},
		{
			MethodName: "GetChannelMicMode",
			Handler:    _ChannelMic_GetChannelMicMode_Handler,
		},
		{
			MethodName: "SimpleRandomHoldMicrSpace",
			Handler:    _ChannelMic_SimpleRandomHoldMicrSpace_Handler,
		},
		{
			MethodName: "InitCreateMicrSpace",
			Handler:    _ChannelMic_InitCreateMicrSpace_Handler,
		},
		{
			MethodName: "FakeHoldMicrSpace",
			Handler:    _ChannelMic_FakeHoldMicrSpace_Handler,
		},
		{
			MethodName: "BatGetMicrList",
			Handler:    _ChannelMic_BatGetMicrList_Handler,
		},
		{
			MethodName: "ReInitChannelMicData",
			Handler:    _ChannelMic_ReInitChannelMicData_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/channelmicsvr/channelmic.proto",
}

func (m *MicrSpaceInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MicrSpaceInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.MicId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.MicState))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.MicUid))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.MicTs))
	return i, nil
}

func (m *SimpleHoldMicrSpaceReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SimpleHoldMicrSpaceReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.MicPosId))
	dAtA[i] = 0x20
	i++
	if m.IsForce {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ChannelDisplayId))
	dAtA[i] = 0x30
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ChannelType))
	dAtA[i] = 0x38
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.UserSex))
	dAtA[i] = 0x40
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.OpUid))
	return i, nil
}

func (m *SimpleHoldMicrSpaceResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SimpleHoldMicrSpaceResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.OpenMicInfo != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelmic(dAtA, i, uint64(m.OpenMicInfo.Size()))
		n1, err := m.OpenMicInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.KickOutUid))
	if len(m.AllMicList) > 0 {
		for _, msg := range m.AllMicList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintChannelmic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ServerTimeMs))
	return i, nil
}

func (m *FakeHoldMicrSpaceReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FakeHoldMicrSpaceReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.MicPosId))
	dAtA[i] = 0x20
	i++
	if m.IsForce {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ChannelDisplayId))
	dAtA[i] = 0x30
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ChannelType))
	dAtA[i] = 0x38
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.UserSex))
	dAtA[i] = 0x40
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.OpUid))
	return i, nil
}

func (m *FakeHoldMicrSpaceResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FakeHoldMicrSpaceResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.OpenMicInfo != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelmic(dAtA, i, uint64(m.OpenMicInfo.Size()))
		n2, err := m.OpenMicInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.KickOutUid))
	if len(m.AllMicList) > 0 {
		for _, msg := range m.AllMicList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintChannelmic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ServerTimeMs))
	return i, nil
}

func (m *SimpleReleaseMicrSpaceReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SimpleReleaseMicrSpaceReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.SwitchFlag))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ChannelDisplayId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ChannelType))
	dAtA[i] = 0x30
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.UserSex))
	dAtA[i] = 0x38
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.OpUid))
	return i, nil
}

func (m *SimpleReleaseMicrSpaceResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SimpleReleaseMicrSpaceResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.CloseMicInfo != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelmic(dAtA, i, uint64(m.CloseMicInfo.Size()))
		n3, err := m.CloseMicInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	if len(m.AllMicList) > 0 {
		for _, msg := range m.AllMicList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintChannelmic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ServerTimeMs))
	dAtA[i] = 0x20
	i++
	if m.IsAutoDisableMic {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *SimpleRandomHoldMicrSpaceReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SimpleRandomHoldMicrSpaceReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.OptMinMicrId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.OptMaxMicrId))
	return i, nil
}

func (m *SimpleRandomHoldMicrSpaceResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SimpleRandomHoldMicrSpaceResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.OpenMicInfo != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelmic(dAtA, i, uint64(m.OpenMicInfo.Size()))
		n4, err := m.OpenMicInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	if len(m.AllMicList) > 0 {
		for _, msg := range m.AllMicList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintChannelmic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ServerTimeMs))
	return i, nil
}

func (m *GetMicrListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMicrListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.OpUid))
	dAtA[i] = 0x18
	i++
	if m.ForceLocalTime {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetMicrListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMicrListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ChannelId))
	if len(m.AllMicList) > 0 {
		for _, msg := range m.AllMicList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintChannelmic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.MicrMode))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ServerTimeMs))
	return i, nil
}

func (m *ChangeMicrophoneReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChangeMicrophoneReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.OpUid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ChannelId))
	if m.ToMicInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("to_mic_info")
	} else {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintChannelmic(dAtA, i, uint64(m.ToMicInfo.Size()))
		n5, err := m.ToMicInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.SwitchFlag))
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ChannelDisplayId))
	dAtA[i] = 0x30
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ChannelType))
	return i, nil
}

func (m *ChangeMicrophoneResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChangeMicrophoneResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.FromMicInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("from_mic_info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelmic(dAtA, i, uint64(m.FromMicInfo.Size()))
		n6, err := m.FromMicInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	if m.ToMicInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("to_mic_info")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintChannelmic(dAtA, i, uint64(m.ToMicInfo.Size()))
		n7, err := m.ToMicInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ServerTimeMs))
	if len(m.AllMicList) > 0 {
		for _, msg := range m.AllMicList {
			dAtA[i] = 0x22
			i++
			i = encodeVarintChannelmic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.MicMode))
	return i, nil
}

func (m *ResetMicSpaceListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ResetMicSpaceListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *ResetMicSpaceListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ResetMicSpaceListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.SuccessMicSpaceList) > 0 {
		for _, msg := range m.SuccessMicSpaceList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelmic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.AfterMicSpaceList) > 0 {
		for _, msg := range m.AfterMicSpaceList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintChannelmic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.AfterMicMode))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.AfterServerTimeMs))
	return i, nil
}

func (m *DisableChannelMicEntryReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DisableChannelMicEntryReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.OpUid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.DisableMicSize))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.KickUid))
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.MicPosId))
	return i, nil
}

func (m *DisableChannelMicEntryResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DisableChannelMicEntryResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.MicPosId))
	if len(m.AllMicList) > 0 {
		for _, msg := range m.AllMicList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintChannelmic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ServerTimeMs))
	return i, nil
}

func (m *EnableChannelMicEntryReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *EnableChannelMicEntryReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.OpUid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.MicPosId))
	dAtA[i] = 0x20
	i++
	if m.HoldMic {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *EnableChannelMicEntryResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *EnableChannelMicEntryResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.MicPosId))
	if len(m.AllMicList) > 0 {
		for _, msg := range m.AllMicList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintChannelmic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ServerTimeMs))
	return i, nil
}

func (m *DisableAllEmptyMicrSpaceReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DisableAllEmptyMicrSpaceReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.OpUid))
	return i, nil
}

func (m *DisableAllEmptyMicrSpaceResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DisableAllEmptyMicrSpaceResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.DisableMicidList) > 0 {
		for _, num := range m.DisableMicidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelmic(dAtA, i, uint64(num))
		}
	}
	if len(m.AllMicList) > 0 {
		for _, msg := range m.AllMicList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintChannelmic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.MicMode))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ServerTimeMs))
	return i, nil
}

func (m *SetChannelMicSpaceStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetChannelMicSpaceStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.OpUid))
	if m.MicInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("mic_info")
	} else {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintChannelmic(dAtA, i, uint64(m.MicInfo.Size()))
		n8, err := m.MicInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	return i, nil
}

func (m *SetChannelMicSpaceStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetChannelMicSpaceStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.AllMicList) > 0 {
		for _, msg := range m.AllMicList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelmic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.KickedUid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ServerTimeMs))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.MicMode))
	return i, nil
}

func (m *BatchSetChannelMicSpaceStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchSetChannelMicSpaceStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.OpUid))
	if len(m.MicIdList) > 0 {
		for _, num := range m.MicIdList {
			dAtA[i] = 0x18
			i++
			i = encodeVarintChannelmic(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.MicStatus))
	return i, nil
}

func (m *BatchSetChannelMicSpaceStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchSetChannelMicSpaceStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.AllMicList) > 0 {
		for _, msg := range m.AllMicList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelmic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.KickedMicrList) > 0 {
		for _, msg := range m.KickedMicrList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintChannelmic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ServerTimeMs))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.MicMode))
	return i, nil
}

func (m *KickoutChannelMicReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *KickoutChannelMicReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.OpUid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ChannelId))
	if len(m.TargetUidList) > 0 {
		for _, num := range m.TargetUidList {
			dAtA[i] = 0x18
			i++
			i = encodeVarintChannelmic(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.BanSecond))
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.SwitchFlag))
	return i, nil
}

func (m *KickoutChannelMicResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *KickoutChannelMicResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ChannelId))
	if len(m.DisableMicIdList) > 0 {
		for _, num := range m.DisableMicIdList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintChannelmic(dAtA, i, uint64(num))
		}
	}
	if len(m.KickoutMicList) > 0 {
		for _, msg := range m.KickoutMicList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintChannelmic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.AllMicList) > 0 {
		for _, msg := range m.AllMicList {
			dAtA[i] = 0x22
			i++
			i = encodeVarintChannelmic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ServerTimeMs))
	return i, nil
}

func (m *SetChannelMicModeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetChannelMicModeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.MicMode))
	dAtA[i] = 0x20
	i++
	if m.IsDisableAllMic {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x28
	i++
	if m.IsNeedHoldMic {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *SetChannelMicModeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetChannelMicModeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.MicMode))
	if len(m.DisableMicList) > 0 {
		for _, msg := range m.DisableMicList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintChannelmic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.EnableMicList) > 0 {
		for _, msg := range m.EnableMicList {
			dAtA[i] = 0x22
			i++
			i = encodeVarintChannelmic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.KickoutMicList) > 0 {
		for _, msg := range m.KickoutMicList {
			dAtA[i] = 0x2a
			i++
			i = encodeVarintChannelmic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.HoldMicInfo != nil {
		dAtA[i] = 0x32
		i++
		i = encodeVarintChannelmic(dAtA, i, uint64(m.HoldMicInfo.Size()))
		n9, err := m.HoldMicInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	if len(m.AllMicList) > 0 {
		for _, msg := range m.AllMicList {
			dAtA[i] = 0x3a
			i++
			i = encodeVarintChannelmic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x40
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ServerTimeMs))
	dAtA[i] = 0x48
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.FromMicMode))
	return i, nil
}

func (m *BatchGetChannelMicModeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetChannelMicModeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, num := range m.ChannelIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelmic(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *BatchGetChannelMicModeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetChannelMicModeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.MicModeList) > 0 {
		for _, num := range m.MicModeList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelmic(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GetChannelMicModeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelMicModeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *GetChannelMicModeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelMicModeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.MicMode))
	return i, nil
}

func (m *InitCreateMicrSpaceReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *InitCreateMicrSpaceReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.MicMode))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ChannelDisplayId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ChannelType))
	return i, nil
}

func (m *InitCreateMicrSpaceResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *InitCreateMicrSpaceResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *BatGetMicrListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatGetMicrListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, num := range m.ChannelIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelmic(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *MicrData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MicrData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ChannelId))
	if len(m.AllMicList) > 0 {
		for _, msg := range m.AllMicList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintChannelmic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.MicrMode))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ServerTimeMs))
	return i, nil
}

func (m *BatGetMicrListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatGetMicrListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.MicDataList) > 0 {
		for _, msg := range m.MicDataList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelmic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ReInitChannelMicDataReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReInitChannelMicDataReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.Cid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.MicNum))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.MicMode))
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.SchemeId))
	dAtA[i] = 0x30
	i++
	if m.NotKickOutMic {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x38
	i++
	if m.NotUnlockMic {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x40
	i++
	if m.UnmuteMic {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x48
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ChannelDisplayId))
	dAtA[i] = 0x50
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ChannelType))
	dAtA[i] = 0x58
	i++
	if m.UseNewControlMic {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *ReInitChannelMicDataResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReInitChannelMicDataResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ChannelId))
	if len(m.EnableMicList) > 0 {
		for _, msg := range m.EnableMicList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintChannelmic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.KickoutMicList) > 0 {
		for _, msg := range m.KickoutMicList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintChannelmic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.HoldMicInfo != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintChannelmic(dAtA, i, uint64(m.HoldMicInfo.Size()))
		n10, err := m.HoldMicInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	if len(m.AllMicList) > 0 {
		for _, msg := range m.AllMicList {
			dAtA[i] = 0x2a
			i++
			i = encodeVarintChannelmic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x30
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.ServerTimeMs))
	dAtA[i] = 0x38
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.MicMode))
	dAtA[i] = 0x40
	i++
	i = encodeVarintChannelmic(dAtA, i, uint64(m.FromMicMode))
	return i, nil
}

func encodeFixed64Channelmic(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Channelmic(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintChannelmic(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *MicrSpaceInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmic(uint64(m.MicId))
	n += 1 + sovChannelmic(uint64(m.MicState))
	n += 1 + sovChannelmic(uint64(m.MicUid))
	n += 1 + sovChannelmic(uint64(m.MicTs))
	return n
}

func (m *SimpleHoldMicrSpaceReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmic(uint64(m.ChannelId))
	n += 1 + sovChannelmic(uint64(m.Uid))
	n += 1 + sovChannelmic(uint64(m.MicPosId))
	n += 2
	n += 1 + sovChannelmic(uint64(m.ChannelDisplayId))
	n += 1 + sovChannelmic(uint64(m.ChannelType))
	n += 1 + sovChannelmic(uint64(m.UserSex))
	n += 1 + sovChannelmic(uint64(m.OpUid))
	return n
}

func (m *SimpleHoldMicrSpaceResp) Size() (n int) {
	var l int
	_ = l
	if m.OpenMicInfo != nil {
		l = m.OpenMicInfo.Size()
		n += 1 + l + sovChannelmic(uint64(l))
	}
	n += 1 + sovChannelmic(uint64(m.KickOutUid))
	if len(m.AllMicList) > 0 {
		for _, e := range m.AllMicList {
			l = e.Size()
			n += 1 + l + sovChannelmic(uint64(l))
		}
	}
	n += 1 + sovChannelmic(uint64(m.ServerTimeMs))
	return n
}

func (m *FakeHoldMicrSpaceReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmic(uint64(m.ChannelId))
	n += 1 + sovChannelmic(uint64(m.Uid))
	n += 1 + sovChannelmic(uint64(m.MicPosId))
	n += 2
	n += 1 + sovChannelmic(uint64(m.ChannelDisplayId))
	n += 1 + sovChannelmic(uint64(m.ChannelType))
	n += 1 + sovChannelmic(uint64(m.UserSex))
	n += 1 + sovChannelmic(uint64(m.OpUid))
	return n
}

func (m *FakeHoldMicrSpaceResp) Size() (n int) {
	var l int
	_ = l
	if m.OpenMicInfo != nil {
		l = m.OpenMicInfo.Size()
		n += 1 + l + sovChannelmic(uint64(l))
	}
	n += 1 + sovChannelmic(uint64(m.KickOutUid))
	if len(m.AllMicList) > 0 {
		for _, e := range m.AllMicList {
			l = e.Size()
			n += 1 + l + sovChannelmic(uint64(l))
		}
	}
	n += 1 + sovChannelmic(uint64(m.ServerTimeMs))
	return n
}

func (m *SimpleReleaseMicrSpaceReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmic(uint64(m.ChannelId))
	n += 1 + sovChannelmic(uint64(m.Uid))
	n += 1 + sovChannelmic(uint64(m.SwitchFlag))
	n += 1 + sovChannelmic(uint64(m.ChannelDisplayId))
	n += 1 + sovChannelmic(uint64(m.ChannelType))
	n += 1 + sovChannelmic(uint64(m.UserSex))
	n += 1 + sovChannelmic(uint64(m.OpUid))
	return n
}

func (m *SimpleReleaseMicrSpaceResp) Size() (n int) {
	var l int
	_ = l
	if m.CloseMicInfo != nil {
		l = m.CloseMicInfo.Size()
		n += 1 + l + sovChannelmic(uint64(l))
	}
	if len(m.AllMicList) > 0 {
		for _, e := range m.AllMicList {
			l = e.Size()
			n += 1 + l + sovChannelmic(uint64(l))
		}
	}
	n += 1 + sovChannelmic(uint64(m.ServerTimeMs))
	n += 2
	return n
}

func (m *SimpleRandomHoldMicrSpaceReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmic(uint64(m.ChannelId))
	n += 1 + sovChannelmic(uint64(m.Uid))
	n += 1 + sovChannelmic(uint64(m.OptMinMicrId))
	n += 1 + sovChannelmic(uint64(m.OptMaxMicrId))
	return n
}

func (m *SimpleRandomHoldMicrSpaceResp) Size() (n int) {
	var l int
	_ = l
	if m.OpenMicInfo != nil {
		l = m.OpenMicInfo.Size()
		n += 1 + l + sovChannelmic(uint64(l))
	}
	if len(m.AllMicList) > 0 {
		for _, e := range m.AllMicList {
			l = e.Size()
			n += 1 + l + sovChannelmic(uint64(l))
		}
	}
	n += 1 + sovChannelmic(uint64(m.ServerTimeMs))
	return n
}

func (m *GetMicrListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmic(uint64(m.ChannelId))
	n += 1 + sovChannelmic(uint64(m.OpUid))
	n += 2
	return n
}

func (m *GetMicrListResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmic(uint64(m.ChannelId))
	if len(m.AllMicList) > 0 {
		for _, e := range m.AllMicList {
			l = e.Size()
			n += 1 + l + sovChannelmic(uint64(l))
		}
	}
	n += 1 + sovChannelmic(uint64(m.MicrMode))
	n += 1 + sovChannelmic(uint64(m.ServerTimeMs))
	return n
}

func (m *ChangeMicrophoneReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmic(uint64(m.OpUid))
	n += 1 + sovChannelmic(uint64(m.ChannelId))
	if m.ToMicInfo != nil {
		l = m.ToMicInfo.Size()
		n += 1 + l + sovChannelmic(uint64(l))
	}
	n += 1 + sovChannelmic(uint64(m.SwitchFlag))
	n += 1 + sovChannelmic(uint64(m.ChannelDisplayId))
	n += 1 + sovChannelmic(uint64(m.ChannelType))
	return n
}

func (m *ChangeMicrophoneResp) Size() (n int) {
	var l int
	_ = l
	if m.FromMicInfo != nil {
		l = m.FromMicInfo.Size()
		n += 1 + l + sovChannelmic(uint64(l))
	}
	if m.ToMicInfo != nil {
		l = m.ToMicInfo.Size()
		n += 1 + l + sovChannelmic(uint64(l))
	}
	n += 1 + sovChannelmic(uint64(m.ServerTimeMs))
	if len(m.AllMicList) > 0 {
		for _, e := range m.AllMicList {
			l = e.Size()
			n += 1 + l + sovChannelmic(uint64(l))
		}
	}
	n += 1 + sovChannelmic(uint64(m.MicMode))
	return n
}

func (m *ResetMicSpaceListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmic(uint64(m.ChannelId))
	return n
}

func (m *ResetMicSpaceListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.SuccessMicSpaceList) > 0 {
		for _, e := range m.SuccessMicSpaceList {
			l = e.Size()
			n += 1 + l + sovChannelmic(uint64(l))
		}
	}
	if len(m.AfterMicSpaceList) > 0 {
		for _, e := range m.AfterMicSpaceList {
			l = e.Size()
			n += 1 + l + sovChannelmic(uint64(l))
		}
	}
	n += 1 + sovChannelmic(uint64(m.AfterMicMode))
	n += 1 + sovChannelmic(uint64(m.AfterServerTimeMs))
	return n
}

func (m *DisableChannelMicEntryReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmic(uint64(m.OpUid))
	n += 1 + sovChannelmic(uint64(m.ChannelId))
	n += 1 + sovChannelmic(uint64(m.DisableMicSize))
	n += 1 + sovChannelmic(uint64(m.KickUid))
	n += 1 + sovChannelmic(uint64(m.MicPosId))
	return n
}

func (m *DisableChannelMicEntryResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmic(uint64(m.ChannelId))
	n += 1 + sovChannelmic(uint64(m.MicPosId))
	if len(m.AllMicList) > 0 {
		for _, e := range m.AllMicList {
			l = e.Size()
			n += 1 + l + sovChannelmic(uint64(l))
		}
	}
	n += 1 + sovChannelmic(uint64(m.ServerTimeMs))
	return n
}

func (m *EnableChannelMicEntryReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmic(uint64(m.OpUid))
	n += 1 + sovChannelmic(uint64(m.ChannelId))
	n += 1 + sovChannelmic(uint64(m.MicPosId))
	n += 2
	return n
}

func (m *EnableChannelMicEntryResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmic(uint64(m.ChannelId))
	n += 1 + sovChannelmic(uint64(m.MicPosId))
	if len(m.AllMicList) > 0 {
		for _, e := range m.AllMicList {
			l = e.Size()
			n += 1 + l + sovChannelmic(uint64(l))
		}
	}
	n += 1 + sovChannelmic(uint64(m.ServerTimeMs))
	return n
}

func (m *DisableAllEmptyMicrSpaceReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmic(uint64(m.ChannelId))
	n += 1 + sovChannelmic(uint64(m.OpUid))
	return n
}

func (m *DisableAllEmptyMicrSpaceResp) Size() (n int) {
	var l int
	_ = l
	if len(m.DisableMicidList) > 0 {
		for _, e := range m.DisableMicidList {
			n += 1 + sovChannelmic(uint64(e))
		}
	}
	if len(m.AllMicList) > 0 {
		for _, e := range m.AllMicList {
			l = e.Size()
			n += 1 + l + sovChannelmic(uint64(l))
		}
	}
	n += 1 + sovChannelmic(uint64(m.MicMode))
	n += 1 + sovChannelmic(uint64(m.ServerTimeMs))
	return n
}

func (m *SetChannelMicSpaceStatusReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmic(uint64(m.ChannelId))
	n += 1 + sovChannelmic(uint64(m.OpUid))
	if m.MicInfo != nil {
		l = m.MicInfo.Size()
		n += 1 + l + sovChannelmic(uint64(l))
	}
	return n
}

func (m *SetChannelMicSpaceStatusResp) Size() (n int) {
	var l int
	_ = l
	if len(m.AllMicList) > 0 {
		for _, e := range m.AllMicList {
			l = e.Size()
			n += 1 + l + sovChannelmic(uint64(l))
		}
	}
	n += 1 + sovChannelmic(uint64(m.KickedUid))
	n += 1 + sovChannelmic(uint64(m.ServerTimeMs))
	n += 1 + sovChannelmic(uint64(m.MicMode))
	return n
}

func (m *BatchSetChannelMicSpaceStatusReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmic(uint64(m.ChannelId))
	n += 1 + sovChannelmic(uint64(m.OpUid))
	if len(m.MicIdList) > 0 {
		for _, e := range m.MicIdList {
			n += 1 + sovChannelmic(uint64(e))
		}
	}
	n += 1 + sovChannelmic(uint64(m.MicStatus))
	return n
}

func (m *BatchSetChannelMicSpaceStatusResp) Size() (n int) {
	var l int
	_ = l
	if len(m.AllMicList) > 0 {
		for _, e := range m.AllMicList {
			l = e.Size()
			n += 1 + l + sovChannelmic(uint64(l))
		}
	}
	if len(m.KickedMicrList) > 0 {
		for _, e := range m.KickedMicrList {
			l = e.Size()
			n += 1 + l + sovChannelmic(uint64(l))
		}
	}
	n += 1 + sovChannelmic(uint64(m.ServerTimeMs))
	n += 1 + sovChannelmic(uint64(m.MicMode))
	return n
}

func (m *KickoutChannelMicReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmic(uint64(m.OpUid))
	n += 1 + sovChannelmic(uint64(m.ChannelId))
	if len(m.TargetUidList) > 0 {
		for _, e := range m.TargetUidList {
			n += 1 + sovChannelmic(uint64(e))
		}
	}
	n += 1 + sovChannelmic(uint64(m.BanSecond))
	n += 1 + sovChannelmic(uint64(m.SwitchFlag))
	return n
}

func (m *KickoutChannelMicResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmic(uint64(m.ChannelId))
	if len(m.DisableMicIdList) > 0 {
		for _, e := range m.DisableMicIdList {
			n += 1 + sovChannelmic(uint64(e))
		}
	}
	if len(m.KickoutMicList) > 0 {
		for _, e := range m.KickoutMicList {
			l = e.Size()
			n += 1 + l + sovChannelmic(uint64(l))
		}
	}
	if len(m.AllMicList) > 0 {
		for _, e := range m.AllMicList {
			l = e.Size()
			n += 1 + l + sovChannelmic(uint64(l))
		}
	}
	n += 1 + sovChannelmic(uint64(m.ServerTimeMs))
	return n
}

func (m *SetChannelMicModeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmic(uint64(m.Uid))
	n += 1 + sovChannelmic(uint64(m.ChannelId))
	n += 1 + sovChannelmic(uint64(m.MicMode))
	n += 2
	n += 2
	return n
}

func (m *SetChannelMicModeResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmic(uint64(m.ChannelId))
	n += 1 + sovChannelmic(uint64(m.MicMode))
	if len(m.DisableMicList) > 0 {
		for _, e := range m.DisableMicList {
			l = e.Size()
			n += 1 + l + sovChannelmic(uint64(l))
		}
	}
	if len(m.EnableMicList) > 0 {
		for _, e := range m.EnableMicList {
			l = e.Size()
			n += 1 + l + sovChannelmic(uint64(l))
		}
	}
	if len(m.KickoutMicList) > 0 {
		for _, e := range m.KickoutMicList {
			l = e.Size()
			n += 1 + l + sovChannelmic(uint64(l))
		}
	}
	if m.HoldMicInfo != nil {
		l = m.HoldMicInfo.Size()
		n += 1 + l + sovChannelmic(uint64(l))
	}
	if len(m.AllMicList) > 0 {
		for _, e := range m.AllMicList {
			l = e.Size()
			n += 1 + l + sovChannelmic(uint64(l))
		}
	}
	n += 1 + sovChannelmic(uint64(m.ServerTimeMs))
	n += 1 + sovChannelmic(uint64(m.FromMicMode))
	return n
}

func (m *BatchGetChannelMicModeReq) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, e := range m.ChannelIdList {
			n += 1 + sovChannelmic(uint64(e))
		}
	}
	return n
}

func (m *BatchGetChannelMicModeResp) Size() (n int) {
	var l int
	_ = l
	if len(m.MicModeList) > 0 {
		for _, e := range m.MicModeList {
			n += 1 + sovChannelmic(uint64(e))
		}
	}
	return n
}

func (m *GetChannelMicModeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmic(uint64(m.ChannelId))
	return n
}

func (m *GetChannelMicModeResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmic(uint64(m.MicMode))
	return n
}

func (m *InitCreateMicrSpaceReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmic(uint64(m.Uid))
	n += 1 + sovChannelmic(uint64(m.ChannelId))
	n += 1 + sovChannelmic(uint64(m.MicMode))
	n += 1 + sovChannelmic(uint64(m.ChannelDisplayId))
	n += 1 + sovChannelmic(uint64(m.ChannelType))
	return n
}

func (m *InitCreateMicrSpaceResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *BatGetMicrListReq) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, e := range m.ChannelIdList {
			n += 1 + sovChannelmic(uint64(e))
		}
	}
	return n
}

func (m *MicrData) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmic(uint64(m.ChannelId))
	if len(m.AllMicList) > 0 {
		for _, e := range m.AllMicList {
			l = e.Size()
			n += 1 + l + sovChannelmic(uint64(l))
		}
	}
	n += 1 + sovChannelmic(uint64(m.MicrMode))
	n += 1 + sovChannelmic(uint64(m.ServerTimeMs))
	return n
}

func (m *BatGetMicrListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.MicDataList) > 0 {
		for _, e := range m.MicDataList {
			l = e.Size()
			n += 1 + l + sovChannelmic(uint64(l))
		}
	}
	return n
}

func (m *ReInitChannelMicDataReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmic(uint64(m.Uid))
	n += 1 + sovChannelmic(uint64(m.Cid))
	n += 1 + sovChannelmic(uint64(m.MicNum))
	n += 1 + sovChannelmic(uint64(m.MicMode))
	n += 1 + sovChannelmic(uint64(m.SchemeId))
	n += 2
	n += 2
	n += 2
	n += 1 + sovChannelmic(uint64(m.ChannelDisplayId))
	n += 1 + sovChannelmic(uint64(m.ChannelType))
	n += 2
	return n
}

func (m *ReInitChannelMicDataResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmic(uint64(m.ChannelId))
	if len(m.EnableMicList) > 0 {
		for _, e := range m.EnableMicList {
			l = e.Size()
			n += 1 + l + sovChannelmic(uint64(l))
		}
	}
	if len(m.KickoutMicList) > 0 {
		for _, e := range m.KickoutMicList {
			l = e.Size()
			n += 1 + l + sovChannelmic(uint64(l))
		}
	}
	if m.HoldMicInfo != nil {
		l = m.HoldMicInfo.Size()
		n += 1 + l + sovChannelmic(uint64(l))
	}
	if len(m.AllMicList) > 0 {
		for _, e := range m.AllMicList {
			l = e.Size()
			n += 1 + l + sovChannelmic(uint64(l))
		}
	}
	n += 1 + sovChannelmic(uint64(m.ServerTimeMs))
	n += 1 + sovChannelmic(uint64(m.MicMode))
	n += 1 + sovChannelmic(uint64(m.FromMicMode))
	return n
}

func sovChannelmic(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozChannelmic(x uint64) (n int) {
	return sovChannelmic(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *MicrSpaceInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MicrSpaceInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MicrSpaceInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MicId", wireType)
			}
			m.MicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MicState", wireType)
			}
			m.MicState = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MicState |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MicUid", wireType)
			}
			m.MicUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MicUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MicTs", wireType)
			}
			m.MicTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MicTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("mic_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SimpleHoldMicrSpaceReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SimpleHoldMicrSpaceReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SimpleHoldMicrSpaceReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MicPosId", wireType)
			}
			m.MicPosId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MicPosId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsForce", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsForce = bool(v != 0)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelDisplayId", wireType)
			}
			m.ChannelDisplayId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelDisplayId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserSex", wireType)
			}
			m.UserSex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UserSex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SimpleHoldMicrSpaceResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SimpleHoldMicrSpaceResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SimpleHoldMicrSpaceResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpenMicInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.OpenMicInfo == nil {
				m.OpenMicInfo = &MicrSpaceInfo{}
			}
			if err := m.OpenMicInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field KickOutUid", wireType)
			}
			m.KickOutUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.KickOutUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AllMicList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AllMicList = append(m.AllMicList, &MicrSpaceInfo{})
			if err := m.AllMicList[len(m.AllMicList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ServerTimeMs", wireType)
			}
			m.ServerTimeMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ServerTimeMs |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FakeHoldMicrSpaceReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FakeHoldMicrSpaceReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FakeHoldMicrSpaceReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MicPosId", wireType)
			}
			m.MicPosId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MicPosId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsForce", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsForce = bool(v != 0)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelDisplayId", wireType)
			}
			m.ChannelDisplayId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelDisplayId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserSex", wireType)
			}
			m.UserSex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UserSex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FakeHoldMicrSpaceResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FakeHoldMicrSpaceResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FakeHoldMicrSpaceResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpenMicInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.OpenMicInfo == nil {
				m.OpenMicInfo = &MicrSpaceInfo{}
			}
			if err := m.OpenMicInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field KickOutUid", wireType)
			}
			m.KickOutUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.KickOutUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AllMicList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AllMicList = append(m.AllMicList, &MicrSpaceInfo{})
			if err := m.AllMicList[len(m.AllMicList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ServerTimeMs", wireType)
			}
			m.ServerTimeMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ServerTimeMs |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SimpleReleaseMicrSpaceReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SimpleReleaseMicrSpaceReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SimpleReleaseMicrSpaceReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SwitchFlag", wireType)
			}
			m.SwitchFlag = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SwitchFlag |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelDisplayId", wireType)
			}
			m.ChannelDisplayId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelDisplayId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserSex", wireType)
			}
			m.UserSex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UserSex |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("switch_flag")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SimpleReleaseMicrSpaceResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SimpleReleaseMicrSpaceResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SimpleReleaseMicrSpaceResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CloseMicInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.CloseMicInfo == nil {
				m.CloseMicInfo = &MicrSpaceInfo{}
			}
			if err := m.CloseMicInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AllMicList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AllMicList = append(m.AllMicList, &MicrSpaceInfo{})
			if err := m.AllMicList[len(m.AllMicList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ServerTimeMs", wireType)
			}
			m.ServerTimeMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ServerTimeMs |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsAutoDisableMic", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsAutoDisableMic = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SimpleRandomHoldMicrSpaceReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SimpleRandomHoldMicrSpaceReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SimpleRandomHoldMicrSpaceReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OptMinMicrId", wireType)
			}
			m.OptMinMicrId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OptMinMicrId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OptMaxMicrId", wireType)
			}
			m.OptMaxMicrId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OptMaxMicrId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SimpleRandomHoldMicrSpaceResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SimpleRandomHoldMicrSpaceResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SimpleRandomHoldMicrSpaceResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpenMicInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.OpenMicInfo == nil {
				m.OpenMicInfo = &MicrSpaceInfo{}
			}
			if err := m.OpenMicInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AllMicList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AllMicList = append(m.AllMicList, &MicrSpaceInfo{})
			if err := m.AllMicList[len(m.AllMicList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ServerTimeMs", wireType)
			}
			m.ServerTimeMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ServerTimeMs |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMicrListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMicrListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMicrListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ForceLocalTime", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ForceLocalTime = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMicrListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMicrListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMicrListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AllMicList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AllMicList = append(m.AllMicList, &MicrSpaceInfo{})
			if err := m.AllMicList[len(m.AllMicList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MicrMode", wireType)
			}
			m.MicrMode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MicrMode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ServerTimeMs", wireType)
			}
			m.ServerTimeMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ServerTimeMs |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChangeMicrophoneReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChangeMicrophoneReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChangeMicrophoneReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToMicInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ToMicInfo == nil {
				m.ToMicInfo = &MicrSpaceInfo{}
			}
			if err := m.ToMicInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SwitchFlag", wireType)
			}
			m.SwitchFlag = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SwitchFlag |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelDisplayId", wireType)
			}
			m.ChannelDisplayId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelDisplayId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("to_mic_info")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("switch_flag")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChangeMicrophoneResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChangeMicrophoneResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChangeMicrophoneResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromMicInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.FromMicInfo == nil {
				m.FromMicInfo = &MicrSpaceInfo{}
			}
			if err := m.FromMicInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToMicInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ToMicInfo == nil {
				m.ToMicInfo = &MicrSpaceInfo{}
			}
			if err := m.ToMicInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ServerTimeMs", wireType)
			}
			m.ServerTimeMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ServerTimeMs |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AllMicList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AllMicList = append(m.AllMicList, &MicrSpaceInfo{})
			if err := m.AllMicList[len(m.AllMicList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MicMode", wireType)
			}
			m.MicMode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MicMode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("from_mic_info")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("to_mic_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ResetMicSpaceListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ResetMicSpaceListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ResetMicSpaceListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ResetMicSpaceListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ResetMicSpaceListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ResetMicSpaceListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SuccessMicSpaceList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SuccessMicSpaceList = append(m.SuccessMicSpaceList, &MicrSpaceInfo{})
			if err := m.SuccessMicSpaceList[len(m.SuccessMicSpaceList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AfterMicSpaceList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AfterMicSpaceList = append(m.AfterMicSpaceList, &MicrSpaceInfo{})
			if err := m.AfterMicSpaceList[len(m.AfterMicSpaceList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AfterMicMode", wireType)
			}
			m.AfterMicMode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AfterMicMode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AfterServerTimeMs", wireType)
			}
			m.AfterServerTimeMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AfterServerTimeMs |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("after_mic_mode")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("after_server_time_ms")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DisableChannelMicEntryReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DisableChannelMicEntryReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DisableChannelMicEntryReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DisableMicSize", wireType)
			}
			m.DisableMicSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DisableMicSize |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field KickUid", wireType)
			}
			m.KickUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.KickUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MicPosId", wireType)
			}
			m.MicPosId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MicPosId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DisableChannelMicEntryResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DisableChannelMicEntryResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DisableChannelMicEntryResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MicPosId", wireType)
			}
			m.MicPosId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MicPosId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AllMicList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AllMicList = append(m.AllMicList, &MicrSpaceInfo{})
			if err := m.AllMicList[len(m.AllMicList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ServerTimeMs", wireType)
			}
			m.ServerTimeMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ServerTimeMs |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *EnableChannelMicEntryReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: EnableChannelMicEntryReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: EnableChannelMicEntryReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MicPosId", wireType)
			}
			m.MicPosId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MicPosId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HoldMic", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.HoldMic = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *EnableChannelMicEntryResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: EnableChannelMicEntryResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: EnableChannelMicEntryResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MicPosId", wireType)
			}
			m.MicPosId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MicPosId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AllMicList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AllMicList = append(m.AllMicList, &MicrSpaceInfo{})
			if err := m.AllMicList[len(m.AllMicList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ServerTimeMs", wireType)
			}
			m.ServerTimeMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ServerTimeMs |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DisableAllEmptyMicrSpaceReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DisableAllEmptyMicrSpaceReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DisableAllEmptyMicrSpaceReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DisableAllEmptyMicrSpaceResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DisableAllEmptyMicrSpaceResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DisableAllEmptyMicrSpaceResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelmic
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.DisableMicidList = append(m.DisableMicidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelmic
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelmic
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelmic
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.DisableMicidList = append(m.DisableMicidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field DisableMicidList", wireType)
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AllMicList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AllMicList = append(m.AllMicList, &MicrSpaceInfo{})
			if err := m.AllMicList[len(m.AllMicList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MicMode", wireType)
			}
			m.MicMode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MicMode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ServerTimeMs", wireType)
			}
			m.ServerTimeMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ServerTimeMs |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("mic_mode")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("server_time_ms")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetChannelMicSpaceStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetChannelMicSpaceStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetChannelMicSpaceStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MicInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.MicInfo == nil {
				m.MicInfo = &MicrSpaceInfo{}
			}
			if err := m.MicInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("mic_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetChannelMicSpaceStatusResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetChannelMicSpaceStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetChannelMicSpaceStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AllMicList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AllMicList = append(m.AllMicList, &MicrSpaceInfo{})
			if err := m.AllMicList[len(m.AllMicList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field KickedUid", wireType)
			}
			m.KickedUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.KickedUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ServerTimeMs", wireType)
			}
			m.ServerTimeMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ServerTimeMs |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MicMode", wireType)
			}
			m.MicMode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MicMode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchSetChannelMicSpaceStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchSetChannelMicSpaceStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchSetChannelMicSpaceStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelmic
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.MicIdList = append(m.MicIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelmic
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelmic
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelmic
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.MicIdList = append(m.MicIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field MicIdList", wireType)
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MicStatus", wireType)
			}
			m.MicStatus = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MicStatus |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("mic_status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchSetChannelMicSpaceStatusResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchSetChannelMicSpaceStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchSetChannelMicSpaceStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AllMicList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AllMicList = append(m.AllMicList, &MicrSpaceInfo{})
			if err := m.AllMicList[len(m.AllMicList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field KickedMicrList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.KickedMicrList = append(m.KickedMicrList, &MicrSpaceInfo{})
			if err := m.KickedMicrList[len(m.KickedMicrList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ServerTimeMs", wireType)
			}
			m.ServerTimeMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ServerTimeMs |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MicMode", wireType)
			}
			m.MicMode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MicMode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *KickoutChannelMicReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: KickoutChannelMicReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: KickoutChannelMicReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelmic
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.TargetUidList = append(m.TargetUidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelmic
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelmic
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelmic
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.TargetUidList = append(m.TargetUidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUidList", wireType)
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BanSecond", wireType)
			}
			m.BanSecond = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BanSecond |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SwitchFlag", wireType)
			}
			m.SwitchFlag = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SwitchFlag |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("switch_flag")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *KickoutChannelMicResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: KickoutChannelMicResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: KickoutChannelMicResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelmic
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.DisableMicIdList = append(m.DisableMicIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelmic
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelmic
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelmic
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.DisableMicIdList = append(m.DisableMicIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field DisableMicIdList", wireType)
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field KickoutMicList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.KickoutMicList = append(m.KickoutMicList, &MicrSpaceInfo{})
			if err := m.KickoutMicList[len(m.KickoutMicList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AllMicList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AllMicList = append(m.AllMicList, &MicrSpaceInfo{})
			if err := m.AllMicList[len(m.AllMicList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ServerTimeMs", wireType)
			}
			m.ServerTimeMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ServerTimeMs |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetChannelMicModeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetChannelMicModeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetChannelMicModeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MicMode", wireType)
			}
			m.MicMode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MicMode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsDisableAllMic", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsDisableAllMic = bool(v != 0)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsNeedHoldMic", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsNeedHoldMic = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("mic_mode")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetChannelMicModeResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetChannelMicModeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetChannelMicModeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MicMode", wireType)
			}
			m.MicMode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MicMode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DisableMicList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DisableMicList = append(m.DisableMicList, &MicrSpaceInfo{})
			if err := m.DisableMicList[len(m.DisableMicList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EnableMicList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EnableMicList = append(m.EnableMicList, &MicrSpaceInfo{})
			if err := m.EnableMicList[len(m.EnableMicList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field KickoutMicList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.KickoutMicList = append(m.KickoutMicList, &MicrSpaceInfo{})
			if err := m.KickoutMicList[len(m.KickoutMicList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HoldMicInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.HoldMicInfo == nil {
				m.HoldMicInfo = &MicrSpaceInfo{}
			}
			if err := m.HoldMicInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AllMicList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AllMicList = append(m.AllMicList, &MicrSpaceInfo{})
			if err := m.AllMicList[len(m.AllMicList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ServerTimeMs", wireType)
			}
			m.ServerTimeMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ServerTimeMs |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromMicMode", wireType)
			}
			m.FromMicMode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FromMicMode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("mic_mode")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetChannelMicModeReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetChannelMicModeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetChannelMicModeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelmic
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ChannelIdList = append(m.ChannelIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelmic
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelmic
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelmic
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ChannelIdList = append(m.ChannelIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetChannelMicModeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetChannelMicModeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetChannelMicModeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelmic
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.MicModeList = append(m.MicModeList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelmic
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelmic
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelmic
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.MicModeList = append(m.MicModeList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field MicModeList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelMicModeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelMicModeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelMicModeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelMicModeResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelMicModeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelMicModeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MicMode", wireType)
			}
			m.MicMode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MicMode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("mic_mode")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *InitCreateMicrSpaceReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: InitCreateMicrSpaceReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: InitCreateMicrSpaceReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MicMode", wireType)
			}
			m.MicMode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MicMode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelDisplayId", wireType)
			}
			m.ChannelDisplayId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelDisplayId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("mic_mode")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *InitCreateMicrSpaceResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: InitCreateMicrSpaceResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: InitCreateMicrSpaceResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatGetMicrListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatGetMicrListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatGetMicrListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelmic
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ChannelIdList = append(m.ChannelIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelmic
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelmic
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelmic
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ChannelIdList = append(m.ChannelIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MicrData) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MicrData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MicrData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AllMicList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AllMicList = append(m.AllMicList, &MicrSpaceInfo{})
			if err := m.AllMicList[len(m.AllMicList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MicrMode", wireType)
			}
			m.MicrMode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MicrMode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ServerTimeMs", wireType)
			}
			m.ServerTimeMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ServerTimeMs |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatGetMicrListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatGetMicrListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatGetMicrListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MicDataList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MicDataList = append(m.MicDataList, &MicrData{})
			if err := m.MicDataList[len(m.MicDataList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReInitChannelMicDataReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReInitChannelMicDataReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReInitChannelMicDataReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Cid", wireType)
			}
			m.Cid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Cid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MicNum", wireType)
			}
			m.MicNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MicNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MicMode", wireType)
			}
			m.MicMode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MicMode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SchemeId", wireType)
			}
			m.SchemeId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SchemeId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NotKickOutMic", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.NotKickOutMic = bool(v != 0)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NotUnlockMic", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.NotUnlockMic = bool(v != 0)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UnmuteMic", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.UnmuteMic = bool(v != 0)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelDisplayId", wireType)
			}
			m.ChannelDisplayId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelDisplayId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UseNewControlMic", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.UseNewControlMic = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("cid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("mic_num")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("mic_mode")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("scheme_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReInitChannelMicDataResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReInitChannelMicDataResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReInitChannelMicDataResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EnableMicList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EnableMicList = append(m.EnableMicList, &MicrSpaceInfo{})
			if err := m.EnableMicList[len(m.EnableMicList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field KickoutMicList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.KickoutMicList = append(m.KickoutMicList, &MicrSpaceInfo{})
			if err := m.KickoutMicList[len(m.KickoutMicList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HoldMicInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.HoldMicInfo == nil {
				m.HoldMicInfo = &MicrSpaceInfo{}
			}
			if err := m.HoldMicInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AllMicList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AllMicList = append(m.AllMicList, &MicrSpaceInfo{})
			if err := m.AllMicList[len(m.AllMicList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ServerTimeMs", wireType)
			}
			m.ServerTimeMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ServerTimeMs |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MicMode", wireType)
			}
			m.MicMode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MicMode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromMicMode", wireType)
			}
			m.FromMicMode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FromMicMode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("mic_mode")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipChannelmic(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowChannelmic
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChannelmic
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthChannelmic
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowChannelmic
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipChannelmic(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthChannelmic = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowChannelmic   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/channelmicsvr/channelmic.proto", fileDescriptorChannelmic) }

var fileDescriptorChannelmic = []byte{
	// 2381 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x5a, 0x4d, 0x6c, 0x1c, 0x49,
	0x15, 0x4e, 0xcd, 0x78, 0x3c, 0x33, 0x6f, 0x3c, 0xe3, 0x71, 0xf9, 0x27, 0xe3, 0xc9, 0xc6, 0x99,
	0xb4, 0x93, 0x6c, 0x08, 0xeb, 0x44, 0x64, 0x41, 0x0a, 0x49, 0x36, 0x90, 0x38, 0x3f, 0x98, 0x5d,
	0x67, 0x91, 0x9d, 0x9c, 0x5b, 0x9d, 0xee, 0x72, 0x5c, 0x72, 0xff, 0x31, 0xd5, 0x9d, 0xd8, 0x7b,
	0x42, 0x48, 0x48, 0xab, 0x45, 0x20, 0x04, 0x02, 0x89, 0x9f, 0x0b, 0x52, 0x2e, 0x7b, 0xe2, 0x80,
	0xf6, 0x02, 0x12, 0x42, 0x5c, 0xd8, 0x03, 0x20, 0x04, 0xd2, 0x1e, 0x11, 0x0a, 0x07, 0xc2, 0x95,
	0x0b, 0x07, 0x38, 0xa0, 0xaa, 0xee, 0x9e, 0xa9, 0xee, 0xa9, 0x9e, 0x1f, 0xd9, 0x59, 0x69, 0x11,
	0x37, 0xbb, 0xea, 0x55, 0xbd, 0xaf, 0xde, 0xfb, 0xea, 0xbd, 0x57, 0xaf, 0x07, 0x34, 0xd6, 0x35,
	0x2f, 0x99, 0xbb, 0x86, 0xeb, 0x12, 0xdb, 0xa1, 0x26, 0x7b, 0xd2, 0x95, 0xfe, 0xbb, 0xe8, 0x77,
	0xbd, 0xc0, 0xc3, 0xb0, 0x1e, 0x8d, 0x6c, 0x52, 0xb3, 0x7d, 0xc6, 0xf4, 0x1c, 0xc7, 0x73, 0x2f,
	0x05, 0xf6, 0x13, 0x9f, 0x9a, 0x7b, 0x36, 0xb9, 0xc4, 0xf6, 0x1e, 0x85, 0xd4, 0x0e, 0xa8, 0x1b,
	0x1c, 0xf8, 0x24, 0x5a, 0xa1, 0x7d, 0x13, 0x41, 0x7d, 0x93, 0x9a, 0xdd, 0x6d, 0xdf, 0x30, 0xc9,
	0x86, 0xbb, 0xe3, 0xe1, 0x13, 0x30, 0xed, 0x50, 0x53, 0xa7, 0x56, 0x0b, 0x75, 0x0a, 0xe7, 0xeb,
	0xb7, 0xa6, 0x3e, 0xfc, 0xcb, 0xa9, 0x63, 0x5b, 0x25, 0x87, 0x9a, 0x1b, 0x16, 0x3e, 0x0d, 0x55,
	0x3e, 0xc9, 0x02, 0x23, 0x20, 0xad, 0x42, 0x07, 0xf5, 0xe6, 0x2b, 0x0e, 0x35, 0xb7, 0xf9, 0x28,
	0x3e, 0x09, 0x65, 0x2e, 0x12, 0x52, 0xab, 0x55, 0x94, 0x04, 0xf8, 0xa6, 0x0f, 0xa9, 0x95, 0x6c,
	0x1f, 0xb0, 0xd6, 0x94, 0x34, 0xcb, 0xb7, 0x7f, 0xc0, 0xb4, 0x0f, 0x0a, 0xb0, 0xb4, 0x4d, 0x1d,
	0xdf, 0x26, 0x5f, 0xf2, 0x6c, 0xab, 0x87, 0x6b, 0x8b, 0x7c, 0x15, 0xaf, 0x02, 0xc4, 0xc7, 0xcd,
	0x42, 0xab, 0xc6, 0xe3, 0x1b, 0x16, 0x5e, 0x82, 0x22, 0xd7, 0x5b, 0x90, 0x66, 0xf9, 0x00, 0xd6,
	0x00, 0xb8, 0x52, 0xdf, 0x63, 0x7a, 0x06, 0x16, 0xc7, 0xfd, 0x15, 0x8f, 0x6d, 0x58, 0xf8, 0x14,
	0x54, 0x28, 0xd3, 0x77, 0xbc, 0xae, 0x49, 0x04, 0xb4, 0x4a, 0x2c, 0x51, 0xa6, 0xec, 0x2e, 0x1f,
	0xc4, 0x97, 0x01, 0x27, 0x08, 0x2c, 0xca, 0x7c, 0xdb, 0x38, 0xe0, 0x9b, 0x95, 0xa4, 0xcd, 0x9a,
	0xf1, 0xfc, 0xed, 0x68, 0x7a, 0xc3, 0xc2, 0xaf, 0xc2, 0x4c, 0xb2, 0x86, 0x1b, 0xbd, 0x35, 0x2d,
	0x49, 0xd7, 0xe2, 0x99, 0x07, 0x07, 0x3e, 0xe1, 0xda, 0x43, 0x46, 0xba, 0x3a, 0x23, 0xfb, 0xad,
	0xb2, 0x24, 0x54, 0xe6, 0xa3, 0xdb, 0x64, 0x9f, 0xdb, 0xcd, 0xf3, 0x85, 0x55, 0x2b, 0xb2, 0xdd,
	0x3c, 0xff, 0x21, 0xb5, 0xb4, 0x7f, 0x20, 0x38, 0xae, 0xb4, 0x1b, 0xf3, 0xf1, 0x1b, 0x50, 0xf7,
	0x7c, 0xe2, 0xea, 0xc2, 0xa9, 0xee, 0x8e, 0xd7, 0x42, 0x1d, 0x74, 0xbe, 0x76, 0x79, 0xf9, 0x62,
	0x9f, 0x2b, 0x17, 0x53, 0x0c, 0xd8, 0xaa, 0x71, 0xf9, 0x4d, 0x6a, 0x0a, 0x3a, 0x9c, 0x83, 0x99,
	0x3d, 0x6a, 0xee, 0xe9, 0x5e, 0x18, 0xe8, 0x91, 0x6d, 0xfb, 0xda, 0x81, 0xcf, 0xbc, 0x1d, 0x06,
	0xdc, 0xaf, 0xd7, 0x60, 0xc6, 0xb0, 0x6d, 0xa1, 0xc5, 0xa6, 0x2c, 0x68, 0x15, 0x3b, 0xc5, 0xe1,
	0x5a, 0xc0, 0xb0, 0xf9, 0xe8, 0x5b, 0x94, 0x05, 0xf8, 0x02, 0x34, 0x18, 0xe9, 0x3e, 0x21, 0x5d,
	0x3d, 0xa0, 0x0e, 0xd1, 0x9d, 0x88, 0x1c, 0x53, 0xb1, 0x9a, 0x99, 0x68, 0xee, 0x01, 0x75, 0xc8,
	0x26, 0xd3, 0x7e, 0x5e, 0x80, 0x85, 0xbb, 0xc6, 0xde, 0xff, 0x19, 0x32, 0x11, 0x43, 0xfe, 0x8e,
	0x60, 0x51, 0x61, 0xb5, 0xff, 0x41, 0x7e, 0xfc, 0xa8, 0x00, 0xcb, 0xd1, 0x5d, 0xd8, 0x22, 0x36,
	0x31, 0x18, 0x39, 0x3a, 0x92, 0x9c, 0x85, 0x1a, 0x7b, 0x4a, 0x03, 0x73, 0x57, 0xdf, 0xb1, 0x8d,
	0xc7, 0xad, 0xa2, 0x34, 0x0f, 0xd1, 0xc4, 0x5d, 0xdb, 0x78, 0x9c, 0x43, 0x83, 0xa9, 0x89, 0x68,
	0x50, 0x1a, 0x87, 0x06, 0x9c, 0x2b, 0xa5, 0x7c, 0x1a, 0x94, 0x07, 0x69, 0xf0, 0x6f, 0x04, 0xed,
	0x3c, 0xe3, 0x30, 0x1f, 0x7f, 0x01, 0x1a, 0xa6, 0xed, 0x31, 0x32, 0x01, 0x19, 0x66, 0xc4, 0x82,
	0x84, 0x0d, 0x59, 0x2f, 0x17, 0x0e, 0xe7, 0xe5, 0x62, 0x9e, 0x97, 0xf1, 0xeb, 0x30, 0x4f, 0x99,
	0x6e, 0x84, 0x81, 0xc7, 0x6d, 0x6c, 0x3c, 0xb2, 0x05, 0xe6, 0xd4, 0xb5, 0x6c, 0x52, 0x76, 0x33,
	0x0c, 0xbc, 0xdb, 0xd1, 0xf4, 0x26, 0x35, 0xb5, 0x0f, 0x10, 0xbc, 0x12, 0x9f, 0xde, 0x70, 0x2d,
	0xcf, 0x39, 0xda, 0x10, 0xf2, 0x69, 0x98, 0xf5, 0xfc, 0x40, 0x77, 0xa8, 0xb8, 0x4b, 0xdd, 0x6c,
	0x1c, 0x99, 0xf1, 0xfc, 0x60, 0x93, 0xf2, 0x7b, 0xd3, 0xdd, 0xe8, 0x0b, 0x1b, 0xfb, 0x3d, 0xe1,
	0xa9, 0xac, 0xb0, 0xb1, 0x1f, 0x09, 0x6b, 0xbf, 0x45, 0x70, 0x72, 0x08, 0xee, 0xc3, 0x5f, 0xe2,
	0x8f, 0xcb, 0x6d, 0xda, 0xd7, 0x11, 0x34, 0xee, 0x91, 0x80, 0x6f, 0xc6, 0xd7, 0x8e, 0x6d, 0xf3,
	0x3e, 0xa9, 0x65, 0xb3, 0x47, 0xa4, 0xc6, 0x17, 0xa1, 0x29, 0x82, 0xb2, 0x6e, 0x7b, 0xa6, 0x61,
	0x0b, 0x14, 0x02, 0x42, 0x42, 0x84, 0x86, 0x98, 0x7d, 0x8b, 0x4f, 0x72, 0x18, 0xda, 0x6f, 0x10,
	0xcc, 0xa6, 0x40, 0x30, 0x7f, 0x3c, 0x14, 0x87, 0x32, 0x53, 0x54, 0x3a, 0x75, 0x75, 0xc7, 0xb3,
	0xc8, 0x40, 0x82, 0xe9, 0x6e, 0x7a, 0x16, 0x99, 0x28, 0xcc, 0xfd, 0xa0, 0x00, 0xf3, 0x5c, 0xef,
	0x63, 0x71, 0x85, 0x3d, 0x7f, 0xd7, 0x73, 0x05, 0x85, 0xfb, 0x96, 0x42, 0x83, 0x96, 0x4a, 0x9f,
	0xb2, 0xa0, 0x3e, 0xe5, 0xe7, 0xa1, 0x16, 0x78, 0x7d, 0x26, 0xf1, 0x28, 0x37, 0xf4, 0x90, 0xd5,
	0xc0, 0x4b, 0x78, 0x94, 0x09, 0x90, 0x53, 0x13, 0x05, 0xc8, 0xa3, 0xc9, 0x93, 0x3c, 0xfe, 0x2f,
	0x0c, 0x1a, 0x26, 0xba, 0x23, 0x3b, 0x5d, 0xcf, 0x91, 0xef, 0xc8, 0x88, 0x93, 0xd5, 0xb8, 0x7c,
	0x72, 0xb6, 0x8c, 0x59, 0x0a, 0x13, 0x98, 0x65, 0x92, 0xc0, 0x96, 0xe5, 0xd8, 0xd4, 0x24, 0x1c,
	0x3b, 0x05, 0x9c, 0x4c, 0x11, 0xc5, 0x64, 0x73, 0xf2, 0x8a, 0x9c, 0x33, 0x4c, 0xbb, 0x06, 0x0b,
	0x5b, 0x84, 0x09, 0xee, 0x8b, 0x1d, 0x26, 0xb9, 0x84, 0x9c, 0x72, 0x8b, 0x8a, 0xd5, 0xcc, 0xc7,
	0xf7, 0x61, 0x89, 0x85, 0xa6, 0x49, 0x18, 0x13, 0xc0, 0x19, 0x9f, 0x8c, 0xe0, 0xa3, 0x51, 0xf0,
	0xe7, 0xe3, 0x85, 0xf2, 0x9e, 0xf8, 0xcb, 0xb0, 0x60, 0xec, 0x04, 0xa4, 0x9b, 0xdd, 0x6d, 0xe4,
	0x85, 0x9b, 0x13, 0xcb, 0x52, 0x7b, 0x5d, 0x80, 0x46, 0x7f, 0xaf, 0xf8, 0xf2, 0xf5, 0x8f, 0x37,
	0x93, 0x2c, 0x10, 0x17, 0xf0, 0x73, 0x89, 0xde, 0x81, 0x6b, 0x58, 0xe8, 0xb9, 0x2b, 0x52, 0xb1,
	0x2d, 0xdf, 0xc5, 0x3f, 0x21, 0x58, 0x8e, 0xd3, 0x4c, 0x1f, 0xd9, 0x1d, 0x37, 0xe8, 0x1e, 0x1c,
	0xcd, 0x8d, 0xbc, 0x08, 0x4d, 0x29, 0xc9, 0xe9, 0x8c, 0xbe, 0x93, 0x8e, 0x20, 0x0d, 0xab, 0x97,
	0xe3, 0xb6, 0xe9, 0x3b, 0xa2, 0x46, 0x10, 0x35, 0x59, 0x98, 0xc9, 0x2a, 0x65, 0x3e, 0xfa, 0x70,
	0xa0, 0xda, 0x2d, 0xa9, 0xaa, 0x5d, 0xed, 0x0f, 0x08, 0xda, 0x79, 0x87, 0x1a, 0x37, 0x60, 0xa6,
	0xf5, 0x14, 0x94, 0x55, 0xf5, 0xc7, 0x56, 0x18, 0xfe, 0x14, 0x41, 0xeb, 0x8e, 0xfb, 0xd2, 0x9c,
	0x34, 0xe6, 0x0b, 0x62, 0xd7, 0xb3, 0xad, 0x81, 0x52, 0xa5, 0xbc, 0x1b, 0x65, 0x74, 0xed, 0xf7,
	0x08, 0x96, 0x73, 0x30, 0x7e, 0x22, 0x6d, 0xae, 0xc3, 0x89, 0x98, 0x43, 0x37, 0x6d, 0xfb, 0x8e,
	0xe3, 0x07, 0x07, 0x93, 0xd7, 0x5b, 0xc3, 0x72, 0xbf, 0xf6, 0x11, 0x82, 0x57, 0xf2, 0x35, 0x30,
	0x1f, 0xbf, 0x06, 0x58, 0xba, 0x3b, 0xd4, 0xea, 0x87, 0xa5, 0xfa, 0x56, 0xb3, 0x7f, 0x6f, 0xa8,
	0x25, 0xce, 0x76, 0xa8, 0x0c, 0x2f, 0x47, 0x5f, 0x39, 0xc6, 0x24, 0xd1, 0x57, 0x69, 0xb9, 0x42,
	0x8e, 0xe5, 0xbe, 0x8f, 0xe0, 0xc4, 0x36, 0x09, 0xfa, 0x8a, 0x85, 0xce, 0xed, 0xc0, 0x08, 0x42,
	0x76, 0x34, 0x65, 0xd3, 0x67, 0x23, 0xb8, 0xe3, 0x25, 0x79, 0x7e, 0x06, 0xfe, 0x07, 0x67, 0xe8,
	0x2b, 0xf9, 0xb8, 0x98, 0x3f, 0x60, 0x42, 0x34, 0x89, 0x09, 0x57, 0x41, 0xbc, 0x19, 0x89, 0x35,
	0xf0, 0x96, 0xac, 0x46, 0xe3, 0x1c, 0xf8, 0x24, 0xe9, 0x54, 0xf6, 0xc9, 0x94, 0x2a, 0x23, 0xbe,
	0x8f, 0xa0, 0x73, 0xcb, 0x08, 0xcc, 0xdd, 0x97, 0x6b, 0xec, 0x15, 0xa8, 0x45, 0x5d, 0xb5, 0xfe,
	0x85, 0xab, 0x6f, 0x55, 0x45, 0x53, 0x2d, 0x39, 0x78, 0xd2, 0x58, 0x0b, 0x59, 0xaa, 0x70, 0xaa,
	0xc6, 0x9d, 0xb5, 0x90, 0x69, 0xff, 0x44, 0x70, 0x7a, 0x04, 0xd6, 0xc3, 0x3a, 0x60, 0x1d, 0x9a,
	0xb1, 0x03, 0x44, 0xb1, 0x3a, 0xde, 0x25, 0x68, 0x44, 0x4b, 0x92, 0x82, 0xfa, 0x68, 0x1d, 0xf4,
	0x3b, 0x04, 0x0b, 0x6f, 0x52, 0x73, 0xcf, 0x0b, 0xa5, 0x33, 0x1f, 0x4d, 0xc8, 0x3e, 0x07, 0xb3,
	0x81, 0xd1, 0x7d, 0x4c, 0x44, 0xe7, 0x42, 0x76, 0x4c, 0x3d, 0x1a, 0x7e, 0x48, 0x7b, 0xce, 0x79,
	0x64, 0xb8, 0x3a, 0x23, 0xa6, 0xe7, 0xa6, 0x33, 0x6a, 0xf5, 0x91, 0xe1, 0x6e, 0x8b, 0xe1, 0x6c,
	0xed, 0x5b, 0x52, 0xd7, 0xbe, 0xda, 0x0f, 0x0b, 0xb0, 0xa8, 0x38, 0xce, 0xb8, 0xd1, 0x7d, 0x0d,
	0xe6, 0xe5, 0x52, 0x20, 0x81, 0x5d, 0xc8, 0xc6, 0xb3, 0x98, 0x56, 0xb1, 0x3b, 0xbd, 0x30, 0x98,
	0x20, 0xd8, 0x37, 0xe2, 0x25, 0x09, 0x27, 0x0e, 0x55, 0x92, 0x0e, 0x72, 0xa1, 0x94, 0x9b, 0x2d,
	0x3e, 0x42, 0xb0, 0x90, 0xa2, 0x36, 0x27, 0x00, 0x77, 0x75, 0xfc, 0xe4, 0x46, 0xd9, 0x27, 0xf7,
	0x58, 0x5e, 0x1e, 0x19, 0x96, 0x3f, 0x03, 0x98, 0xb2, 0x5e, 0x1b, 0x21, 0x3e, 0x6a, 0x2a, 0x3f,
	0xcf, 0x52, 0xd6, 0x4f, 0x32, 0x9b, 0xd4, 0xc4, 0x6b, 0xd0, 0xa4, 0x4c, 0x77, 0x09, 0xb1, 0xf4,
	0x5e, 0x42, 0x2f, 0x49, 0x0b, 0xea, 0x94, 0xdd, 0x27, 0xc4, 0x8a, 0x1f, 0xea, 0xda, 0x7f, 0x8a,
	0xb0, 0xa8, 0x38, 0xd8, 0xb8, 0x4e, 0x97, 0x4f, 0x50, 0x50, 0x9d, 0x60, 0x3d, 0x5d, 0x20, 0x8e,
	0xe9, 0xe6, 0x3e, 0x5b, 0x84, 0xa7, 0x6e, 0xc2, 0x2c, 0x71, 0xd3, 0x7b, 0x8c, 0xf4, 0x74, 0x3d,
	0x5a, 0x91, 0x89, 0x1e, 0x29, 0xba, 0x95, 0x26, 0xa5, 0xdb, 0x1b, 0x50, 0x4f, 0x6c, 0x1a, 0x25,
	0xa7, 0xe9, 0x91, 0xbd, 0x8c, 0xb8, 0x7c, 0x52, 0xf6, 0x32, 0xca, 0x87, 0x63, 0x6b, 0x25, 0x37,
	0x72, 0x9d, 0x97, 0xde, 0x93, 0xc2, 0x35, 0x55, 0xf9, 0x49, 0x1a, 0x3f, 0x1d, 0x45, 0x08, 0x5b,
	0x87, 0x65, 0x11, 0xb6, 0xef, 0xa9, 0xb8, 0x7d, 0x0e, 0x66, 0xfb, 0x0c, 0x90, 0xab, 0x93, 0x7a,
	0x8f, 0x00, 0x1c, 0x9a, 0xf6, 0x45, 0x68, 0xe7, 0x6d, 0xc2, 0x7c, 0xac, 0x41, 0x3d, 0xc1, 0x21,
	0xef, 0x51, 0x8b, 0x19, 0x22, 0x76, 0xb8, 0x06, 0x0b, 0x4a, 0x04, 0x63, 0x3d, 0xfe, 0xae, 0xc0,
	0xa2, 0x5a, 0xb3, 0x4c, 0x4e, 0xa4, 0x20, 0xa7, 0xf6, 0x67, 0x04, 0x4b, 0x1b, 0x2e, 0x0d, 0xd6,
	0xbb, 0xc4, 0x08, 0xd2, 0xdd, 0xd8, 0x97, 0x7b, 0xaf, 0x5f, 0x66, 0x1f, 0x56, 0x5b, 0x86, 0xe3,
	0xca, 0x43, 0x31, 0x5f, 0xbb, 0x06, 0x73, 0xb7, 0x8c, 0x20, 0xd3, 0xe6, 0x1a, 0xd7, 0xcd, 0xbf,
	0x44, 0x50, 0xe1, 0xeb, 0x6e, 0x1b, 0x81, 0xf1, 0xc9, 0xeb, 0x4a, 0xdd, 0x07, 0x9c, 0x3d, 0x3a,
	0xf3, 0xf1, 0x95, 0x88, 0x9c, 0x96, 0x11, 0x18, 0x72, 0x49, 0xb2, 0x90, 0x85, 0xc8, 0xcf, 0x2c,
	0x28, 0xcb, 0xff, 0x10, 0xd6, 0xf8, 0x55, 0x11, 0x8e, 0x6f, 0x11, 0x61, 0xe8, 0x9e, 0xa8, 0x90,
	0x1a, 0x42, 0x9e, 0x25, 0x28, 0x9a, 0xd9, 0xfe, 0xac, 0x49, 0xad, 0xe4, 0xc3, 0xa4, 0x1b, 0x3a,
	0x29, 0xba, 0x4c, 0x3b, 0xd4, 0xbc, 0x1f, 0x3a, 0x99, 0x42, 0x44, 0x41, 0xa7, 0xd3, 0x50, 0x65,
	0xe6, 0x2e, 0x71, 0x48, 0xf4, 0x66, 0xee, 0x4b, 0x54, 0xa2, 0x61, 0x91, 0x9d, 0x9b, 0xae, 0x17,
	0xe8, 0xbd, 0x0f, 0x22, 0x3c, 0x2d, 0x4c, 0xcb, 0x69, 0xc1, 0xf5, 0x82, 0x37, 0xa3, 0x6f, 0x22,
	0x3c, 0x8b, 0x5c, 0x80, 0x06, 0x17, 0x0f, 0x5d, 0xdb, 0x33, 0xf7, 0x84, 0x70, 0x59, 0x12, 0x9e,
	0x71, 0xbd, 0xe0, 0xa1, 0x98, 0xe2, 0xb2, 0xab, 0x00, 0xa1, 0xeb, 0x84, 0x41, 0xd4, 0xe7, 0xae,
	0x48, 0x72, 0xd5, 0x68, 0x9c, 0x0b, 0xa9, 0x19, 0x5f, 0x9d, 0x88, 0xf1, 0x90, 0xf7, 0xe5, 0xe1,
	0x75, 0x98, 0x0f, 0x19, 0xd1, 0x5d, 0xf2, 0x54, 0x37, 0x3d, 0x37, 0xe8, 0x7a, 0x51, 0x9e, 0xac,
	0xc9, 0x2d, 0xf7, 0x90, 0x91, 0xfb, 0xe4, 0xe9, 0x7a, 0x34, 0xcd, 0x33, 0xdf, 0x2f, 0x8a, 0xd0,
	0x52, 0x3b, 0x70, 0xdc, 0xe4, 0xa7, 0x48, 0x4b, 0x85, 0x23, 0x48, 0x4b, 0xc5, 0x43, 0xa7, 0xa5,
	0xa9, 0x43, 0xa5, 0xa5, 0xd2, 0xe1, 0xd2, 0xd2, 0xf4, 0x58, 0x05, 0x75, 0x59, 0xc5, 0xe3, 0x81,
	0xbc, 0x55, 0xc9, 0xc9, 0x5b, 0x97, 0x7f, 0xbd, 0x00, 0xd2, 0x2f, 0x0a, 0xf0, 0xcf, 0x10, 0xcc,
	0x2b, 0xbe, 0x32, 0x63, 0x4d, 0x3e, 0x84, 0xfa, 0xf3, 0x7d, 0x7b, 0x75, 0xa4, 0x0c, 0xf3, 0xb5,
	0xb7, 0xbf, 0xf6, 0xec, 0x45, 0x11, 0xbd, 0xf7, 0xec, 0x45, 0xb1, 0xb2, 0x7f, 0x35, 0xbc, 0x1a,
	0x5c, 0xdd, 0xb9, 0xfa, 0xdd, 0x67, 0x2f, 0x8a, 0x57, 0xd6, 0xf6, 0x3b, 0xd7, 0x13, 0x42, 0xdc,
	0xbe, 0xd1, 0x59, 0x0b, 0x3b, 0xd7, 0x43, 0x6a, 0xdd, 0xe8, 0xac, 0x05, 0x9d, 0xeb, 0x51, 0xa5,
	0xde, 0x71, 0xa8, 0xd9, 0x11, 0x73, 0x3b, 0x9d, 0xeb, 0x94, 0x75, 0x44, 0xc7, 0xff, 0x06, 0xfe,
	0x1e, 0x4a, 0x7e, 0x4f, 0x90, 0xfd, 0xdc, 0x85, 0xcf, 0x0e, 0x02, 0x52, 0x7c, 0x2f, 0x6c, 0x9f,
	0x1b, 0x47, 0x8c, 0xf9, 0xda, 0x05, 0x0e, 0xbd, 0xc0, 0xa1, 0x4f, 0x71, 0xe8, 0x1c, 0xf6, 0xf1,
	0x1c, 0xd8, 0xf8, 0x31, 0xd4, 0xa4, 0x08, 0x89, 0xdb, 0xb2, 0x8a, 0x74, 0xd6, 0x68, 0x9f, 0xc8,
	0x9d, 0x63, 0xbe, 0x76, 0x8a, 0xeb, 0x2c, 0x72, 0x9d, 0x85, 0x7d, 0xa1, 0xb1, 0x91, 0xd6, 0x88,
	0x9f, 0x40, 0x33, 0xdb, 0x09, 0xc7, 0xa7, 0xe4, 0x1d, 0x15, 0x1f, 0x10, 0xda, 0x9d, 0xe1, 0x02,
	0x89, 0xde, 0xa9, 0x21, 0x7a, 0x0f, 0x60, 0x6e, 0xa0, 0x4f, 0x8c, 0x53, 0xfb, 0xaa, 0x9a, 0xd0,
	0xed, 0xd3, 0x23, 0x24, 0x12, 0xd5, 0xa5, 0x21, 0xaa, 0xdf, 0x45, 0xb0, 0xa4, 0xee, 0x5a, 0xa6,
	0x3d, 0x9e, 0xdb, 0xae, 0x4d, 0x7b, 0x3c, 0xbf, 0x01, 0x1a, 0x41, 0x99, 0x1e, 0x02, 0xe5, 0x1b,
	0x08, 0x16, 0x95, 0xbd, 0x3c, 0x7c, 0x46, 0x56, 0x91, 0xd7, 0x92, 0x6c, 0x9f, 0x1d, 0x43, 0x2a,
	0xc1, 0x51, 0x1e, 0x82, 0xe3, 0xdb, 0x08, 0x5a, 0x79, 0x2d, 0x32, 0xfc, 0xaa, 0xe2, 0xb4, 0xaa,
	0x56, 0x5d, 0xfb, 0xfc, 0x78, 0x82, 0x09, 0xa0, 0xca, 0x08, 0x40, 0x79, 0x1d, 0x8c, 0x34, 0xa0,
	0x21, 0x3d, 0x99, 0x34, 0xa0, 0x61, 0x0d, 0x91, 0x08, 0x50, 0x75, 0x38, 0x5f, 0x07, 0x9e, 0xe4,
	0x69, 0xbe, 0xaa, 0x1a, 0x10, 0x69, 0xbe, 0x2a, 0xdf, 0xf4, 0x91, 0x6a, 0x18, 0xae, 0x7a, 0xe0,
	0x61, 0x98, 0x56, 0xad, 0x7a, 0x10, 0xa7, 0x55, 0x2b, 0x5f, 0x96, 0x91, 0xea, 0xda, 0x10, 0xd5,
	0xfb, 0xb0, 0xa4, 0x7e, 0x50, 0xa4, 0x6f, 0x4a, 0xee, 0xcb, 0x25, 0x7d, 0x53, 0xf2, 0xdf, 0x26,
	0xda, 0x2c, 0x47, 0x32, 0xc3, 0x91, 0x1c, 0xe3, 0x38, 0x8e, 0xe1, 0x6f, 0x21, 0x38, 0x39, 0xb4,
	0x8f, 0x85, 0x5f, 0x1b, 0xd8, 0x7a, 0x18, 0x15, 0xd6, 0x26, 0x90, 0x4e, 0xf0, 0xd4, 0x25, 0x3c,
	0x14, 0xe6, 0xee, 0x0d, 0x77, 0xc2, 0xbd, 0x91, 0x4e, 0x18, 0x72, 0xf4, 0x86, 0xa4, 0xea, 0x27,
	0xa8, 0xf7, 0xeb, 0x94, 0xc1, 0x4f, 0xf9, 0xf8, 0xbc, 0x22, 0xdb, 0x28, 0x7f, 0xa9, 0xd0, 0xfe,
	0xd4, 0x98, 0x92, 0x49, 0x6a, 0x9a, 0x15, 0xa9, 0x29, 0xbc, 0xba, 0x1f, 0xa7, 0xa6, 0x5e, 0x0a,
	0xed, 0x73, 0x82, 0xa7, 0xa6, 0xf7, 0x10, 0xcc, 0x2b, 0x9e, 0x35, 0xe9, 0x14, 0xaf, 0x7e, 0xcc,
	0xa5, 0x53, 0x7c, 0xde, 0xdb, 0x48, 0x80, 0x69, 0x8e, 0x07, 0xe6, 0x7d, 0x04, 0x73, 0x03, 0xbf,
	0x59, 0x4a, 0xfb, 0x45, 0xf5, 0x43, 0xb0, 0xb4, 0x5f, 0x94, 0x3f, 0x7a, 0x8a, 0x2a, 0x8d, 0xb9,
	0x23, 0xac, 0x34, 0x7c, 0x68, 0xa4, 0x1f, 0x3e, 0xf8, 0x64, 0x86, 0x94, 0x99, 0xcc, 0xbe, 0x32,
	0x6c, 0x3a, 0xb9, 0xbe, 0x78, 0xc8, 0xf5, 0xfd, 0x31, 0x82, 0x05, 0x55, 0x65, 0x8d, 0x57, 0xd3,
	0x69, 0x54, 0xf9, 0x78, 0x6a, 0x9f, 0x19, 0x2d, 0xc4, 0x7c, 0xed, 0x0a, 0x07, 0x31, 0xcf, 0x41,
	0x4c, 0x73, 0x6f, 0xb9, 0x02, 0xc8, 0xaa, 0xca, 0x5f, 0xc2, 0x22, 0x6e, 0xe7, 0xba, 0x43, 0x4d,
	0x37, 0x74, 0x6e, 0xb4, 0xa7, 0xdf, 0x7d, 0xf6, 0xa2, 0xf8, 0xaf, 0x27, 0xb7, 0x9a, 0x1f, 0x3e,
	0x5f, 0x41, 0x7f, 0x7c, 0xbe, 0x82, 0xfe, 0xfa, 0x7c, 0x05, 0x7d, 0xe7, 0x6f, 0x2b, 0xc7, 0xfe,
	0x1b, 0x00, 0x00, 0xff, 0xff, 0xca, 0x06, 0xc2, 0xef, 0xc8, 0x2a, 0x00, 0x00,
}
