// Code generated by protoc-gen-go. DO NOT EDIT.
// source: autoaward/autoaward.proto

package autoaward

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type SettingStatus int32

const (
	SettingStatus_All    SettingStatus = 0
	SettingStatus_Normal SettingStatus = 1
	SettingStatus_Ban    SettingStatus = 2
)

var SettingStatus_name = map[int32]string{
	0: "All",
	1: "Normal",
	2: "Ban",
}
var SettingStatus_value = map[string]int32{
	"All":    0,
	"Normal": 1,
	"Ban":    2,
}

func (x SettingStatus) String() string {
	return proto.EnumName(SettingStatus_name, int32(x))
}
func (SettingStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_autoaward_f5a67070e9f9a2ef, []int{0}
}

type UserType int32

const (
	UserType_NONE    UserType = 0
	UserType_NewUser UserType = 1
	UserType_AllUser UserType = 2
)

var UserType_name = map[int32]string{
	0: "NONE",
	1: "NewUser",
	2: "AllUser",
}
var UserType_value = map[string]int32{
	"NONE":    0,
	"NewUser": 1,
	"AllUser": 2,
}

func (x UserType) String() string {
	return proto.EnumName(UserType_name, int32(x))
}
func (UserType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_autoaward_f5a67070e9f9a2ef, []int{1}
}

type StrategyIDType int32

const (
	StrategyIDType_Nothing    StrategyIDType = 0
	StrategyIDType_Meiridaka  StrategyIDType = 1
	StrategyIDType_Lianxudaka StrategyIDType = 2
	StrategyIDType_Shoucidaka StrategyIDType = 3
)

var StrategyIDType_name = map[int32]string{
	0: "Nothing",
	1: "Meiridaka",
	2: "Lianxudaka",
	3: "Shoucidaka",
}
var StrategyIDType_value = map[string]int32{
	"Nothing":    0,
	"Meiridaka":  1,
	"Lianxudaka": 2,
	"Shoucidaka": 3,
}

func (x StrategyIDType) String() string {
	return proto.EnumName(StrategyIDType_name, int32(x))
}
func (StrategyIDType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_autoaward_f5a67070e9f9a2ef, []int{2}
}

type DecoractionInfo struct {
	Dur                  uint32   `protobuf:"varint,1,opt,name=Dur,proto3" json:"Dur,omitempty"`
	ID                   string   `protobuf:"bytes,2,opt,name=ID,proto3" json:"ID,omitempty"`
	Version              string   `protobuf:"bytes,3,opt,name=Version,proto3" json:"Version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DecoractionInfo) Reset()         { *m = DecoractionInfo{} }
func (m *DecoractionInfo) String() string { return proto.CompactTextString(m) }
func (*DecoractionInfo) ProtoMessage()    {}
func (*DecoractionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_autoaward_f5a67070e9f9a2ef, []int{0}
}
func (m *DecoractionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DecoractionInfo.Unmarshal(m, b)
}
func (m *DecoractionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DecoractionInfo.Marshal(b, m, deterministic)
}
func (dst *DecoractionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DecoractionInfo.Merge(dst, src)
}
func (m *DecoractionInfo) XXX_Size() int {
	return xxx_messageInfo_DecoractionInfo.Size(m)
}
func (m *DecoractionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DecoractionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DecoractionInfo proto.InternalMessageInfo

func (m *DecoractionInfo) GetDur() uint32 {
	if m != nil {
		return m.Dur
	}
	return 0
}

func (m *DecoractionInfo) GetID() string {
	if m != nil {
		return m.ID
	}
	return ""
}

func (m *DecoractionInfo) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

type MedalInfo struct {
	MedalID              uint32   `protobuf:"varint,1,opt,name=MedalID,proto3" json:"MedalID,omitempty"`
	ExTime               uint32   `protobuf:"varint,2,opt,name=ExTime,proto3" json:"ExTime,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MedalInfo) Reset()         { *m = MedalInfo{} }
func (m *MedalInfo) String() string { return proto.CompactTextString(m) }
func (*MedalInfo) ProtoMessage()    {}
func (*MedalInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_autoaward_f5a67070e9f9a2ef, []int{1}
}
func (m *MedalInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MedalInfo.Unmarshal(m, b)
}
func (m *MedalInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MedalInfo.Marshal(b, m, deterministic)
}
func (dst *MedalInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MedalInfo.Merge(dst, src)
}
func (m *MedalInfo) XXX_Size() int {
	return xxx_messageInfo_MedalInfo.Size(m)
}
func (m *MedalInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MedalInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MedalInfo proto.InternalMessageInfo

func (m *MedalInfo) GetMedalID() uint32 {
	if m != nil {
		return m.MedalID
	}
	return 0
}

func (m *MedalInfo) GetExTime() uint32 {
	if m != nil {
		return m.ExTime
	}
	return 0
}

type AwardInfo struct {
	// UserType 发放的用户类型 1 新用户 2 全部用户 这里是或关系
	UserType UserType `protobuf:"varint,1,opt,name=UserType,proto3,enum=autoaward.UserType" json:"UserType,omitempty"`
	// AwardAmount 奖励数量
	AwardAmount uint32 `protobuf:"varint,2,opt,name=AwardAmount,proto3" json:"AwardAmount,omitempty"`
	// AwardType 奖励类型 1红钻 2飘窗 3勋章 4 经验
	AwardType uint32 `protobuf:"varint,3,opt,name=AwardType,proto3" json:"AwardType,omitempty"`
	// AwardID 奖励类型后面是有id的 具体的某类型的 id 预留项目
	AwardID string `protobuf:"bytes,4,opt,name=AwardID,proto3" json:"AwardID,omitempty"`
	// AwardName 奖品名字 红钻 。。。
	AwardName string `protobuf:"bytes,5,opt,name=AwardName,proto3" json:"AwardName,omitempty"`
	// 主页飘
	DecoractionInfo *DecoractionInfo `protobuf:"bytes,6,opt,name=DecoractionInfo,proto3" json:"DecoractionInfo,omitempty"`
	// 勋章
	MedalInfo *MedalInfo `protobuf:"bytes,7,opt,name=MedalInfo,proto3" json:"MedalInfo,omitempty"`
	// 定义的新用户时间 单位为天
	NewUserDay int64 `protobuf:"varint,8,opt,name=NewUserDay,proto3" json:"NewUserDay,omitempty"`
	// 连续打卡天数
	Count                uint32   `protobuf:"varint,9,opt,name=Count,proto3" json:"Count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AwardInfo) Reset()         { *m = AwardInfo{} }
func (m *AwardInfo) String() string { return proto.CompactTextString(m) }
func (*AwardInfo) ProtoMessage()    {}
func (*AwardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_autoaward_f5a67070e9f9a2ef, []int{2}
}
func (m *AwardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardInfo.Unmarshal(m, b)
}
func (m *AwardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardInfo.Marshal(b, m, deterministic)
}
func (dst *AwardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardInfo.Merge(dst, src)
}
func (m *AwardInfo) XXX_Size() int {
	return xxx_messageInfo_AwardInfo.Size(m)
}
func (m *AwardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AwardInfo proto.InternalMessageInfo

func (m *AwardInfo) GetUserType() UserType {
	if m != nil {
		return m.UserType
	}
	return UserType_NONE
}

func (m *AwardInfo) GetAwardAmount() uint32 {
	if m != nil {
		return m.AwardAmount
	}
	return 0
}

func (m *AwardInfo) GetAwardType() uint32 {
	if m != nil {
		return m.AwardType
	}
	return 0
}

func (m *AwardInfo) GetAwardID() string {
	if m != nil {
		return m.AwardID
	}
	return ""
}

func (m *AwardInfo) GetAwardName() string {
	if m != nil {
		return m.AwardName
	}
	return ""
}

func (m *AwardInfo) GetDecoractionInfo() *DecoractionInfo {
	if m != nil {
		return m.DecoractionInfo
	}
	return nil
}

func (m *AwardInfo) GetMedalInfo() *MedalInfo {
	if m != nil {
		return m.MedalInfo
	}
	return nil
}

func (m *AwardInfo) GetNewUserDay() int64 {
	if m != nil {
		return m.NewUserDay
	}
	return 0
}

func (m *AwardInfo) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type SettingItem struct {
	StrategyID           StrategyIDType `protobuf:"varint,1,opt,name=StrategyID,proto3,enum=autoaward.StrategyIDType" json:"StrategyID,omitempty"`
	Describe             string         `protobuf:"bytes,2,opt,name=Describe,proto3" json:"Describe,omitempty"`
	AwardInfoList        []*AwardInfo   `protobuf:"bytes,3,rep,name=AwardInfoList,proto3" json:"AwardInfoList,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *SettingItem) Reset()         { *m = SettingItem{} }
func (m *SettingItem) String() string { return proto.CompactTextString(m) }
func (*SettingItem) ProtoMessage()    {}
func (*SettingItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_autoaward_f5a67070e9f9a2ef, []int{3}
}
func (m *SettingItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SettingItem.Unmarshal(m, b)
}
func (m *SettingItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SettingItem.Marshal(b, m, deterministic)
}
func (dst *SettingItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SettingItem.Merge(dst, src)
}
func (m *SettingItem) XXX_Size() int {
	return xxx_messageInfo_SettingItem.Size(m)
}
func (m *SettingItem) XXX_DiscardUnknown() {
	xxx_messageInfo_SettingItem.DiscardUnknown(m)
}

var xxx_messageInfo_SettingItem proto.InternalMessageInfo

func (m *SettingItem) GetStrategyID() StrategyIDType {
	if m != nil {
		return m.StrategyID
	}
	return StrategyIDType_Nothing
}

func (m *SettingItem) GetDescribe() string {
	if m != nil {
		return m.Describe
	}
	return ""
}

func (m *SettingItem) GetAwardInfoList() []*AwardInfo {
	if m != nil {
		return m.AwardInfoList
	}
	return nil
}

type SettingInfo struct {
	Type                 uint32         `protobuf:"varint,2,opt,name=Type,proto3" json:"Type,omitempty"`
	MatchName            string         `protobuf:"bytes,3,opt,name=MatchName,proto3" json:"MatchName,omitempty"`
	MatchID              string         `protobuf:"bytes,4,opt,name=MatchID,proto3" json:"MatchID,omitempty"`
	BeginTime            uint64         `protobuf:"varint,5,opt,name=BeginTime,proto3" json:"BeginTime,omitempty"`
	EndTime              uint64         `protobuf:"varint,6,opt,name=EndTime,proto3" json:"EndTime,omitempty"`
	SettingItems         []*SettingItem `protobuf:"bytes,7,rep,name=SettingItems,proto3" json:"SettingItems,omitempty"`
	Status               SettingStatus  `protobuf:"varint,8,opt,name=Status,proto3,enum=autoaward.SettingStatus" json:"Status,omitempty"`
	ID                   string         `protobuf:"bytes,9,opt,name=ID,proto3" json:"ID,omitempty"`
	ChineseLimit         uint32         `protobuf:"varint,10,opt,name=ChineseLimit,proto3" json:"ChineseLimit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *SettingInfo) Reset()         { *m = SettingInfo{} }
func (m *SettingInfo) String() string { return proto.CompactTextString(m) }
func (*SettingInfo) ProtoMessage()    {}
func (*SettingInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_autoaward_f5a67070e9f9a2ef, []int{4}
}
func (m *SettingInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SettingInfo.Unmarshal(m, b)
}
func (m *SettingInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SettingInfo.Marshal(b, m, deterministic)
}
func (dst *SettingInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SettingInfo.Merge(dst, src)
}
func (m *SettingInfo) XXX_Size() int {
	return xxx_messageInfo_SettingInfo.Size(m)
}
func (m *SettingInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SettingInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SettingInfo proto.InternalMessageInfo

func (m *SettingInfo) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *SettingInfo) GetMatchName() string {
	if m != nil {
		return m.MatchName
	}
	return ""
}

func (m *SettingInfo) GetMatchID() string {
	if m != nil {
		return m.MatchID
	}
	return ""
}

func (m *SettingInfo) GetBeginTime() uint64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *SettingInfo) GetEndTime() uint64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *SettingInfo) GetSettingItems() []*SettingItem {
	if m != nil {
		return m.SettingItems
	}
	return nil
}

func (m *SettingInfo) GetStatus() SettingStatus {
	if m != nil {
		return m.Status
	}
	return SettingStatus_All
}

func (m *SettingInfo) GetID() string {
	if m != nil {
		return m.ID
	}
	return ""
}

func (m *SettingInfo) GetChineseLimit() uint32 {
	if m != nil {
		return m.ChineseLimit
	}
	return 0
}

type GetSettingReq struct {
	Status               SettingStatus `protobuf:"varint,1,opt,name=status,proto3,enum=autoaward.SettingStatus" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetSettingReq) Reset()         { *m = GetSettingReq{} }
func (m *GetSettingReq) String() string { return proto.CompactTextString(m) }
func (*GetSettingReq) ProtoMessage()    {}
func (*GetSettingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_autoaward_f5a67070e9f9a2ef, []int{5}
}
func (m *GetSettingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSettingReq.Unmarshal(m, b)
}
func (m *GetSettingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSettingReq.Marshal(b, m, deterministic)
}
func (dst *GetSettingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSettingReq.Merge(dst, src)
}
func (m *GetSettingReq) XXX_Size() int {
	return xxx_messageInfo_GetSettingReq.Size(m)
}
func (m *GetSettingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSettingReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSettingReq proto.InternalMessageInfo

func (m *GetSettingReq) GetStatus() SettingStatus {
	if m != nil {
		return m.Status
	}
	return SettingStatus_All
}

type GetSettingResp struct {
	SettingInfoList      []*SettingInfo `protobuf:"bytes,1,rep,name=SettingInfoList,proto3" json:"SettingInfoList,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetSettingResp) Reset()         { *m = GetSettingResp{} }
func (m *GetSettingResp) String() string { return proto.CompactTextString(m) }
func (*GetSettingResp) ProtoMessage()    {}
func (*GetSettingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_autoaward_f5a67070e9f9a2ef, []int{6}
}
func (m *GetSettingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSettingResp.Unmarshal(m, b)
}
func (m *GetSettingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSettingResp.Marshal(b, m, deterministic)
}
func (dst *GetSettingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSettingResp.Merge(dst, src)
}
func (m *GetSettingResp) XXX_Size() int {
	return xxx_messageInfo_GetSettingResp.Size(m)
}
func (m *GetSettingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSettingResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSettingResp proto.InternalMessageInfo

func (m *GetSettingResp) GetSettingInfoList() []*SettingInfo {
	if m != nil {
		return m.SettingInfoList
	}
	return nil
}

type UpdateOrInsertSettingReq struct {
	ID                   string       `protobuf:"bytes,1,opt,name=ID,proto3" json:"ID,omitempty"`
	SettingInfo          *SettingInfo `protobuf:"bytes,2,opt,name=SettingInfo,proto3" json:"SettingInfo,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UpdateOrInsertSettingReq) Reset()         { *m = UpdateOrInsertSettingReq{} }
func (m *UpdateOrInsertSettingReq) String() string { return proto.CompactTextString(m) }
func (*UpdateOrInsertSettingReq) ProtoMessage()    {}
func (*UpdateOrInsertSettingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_autoaward_f5a67070e9f9a2ef, []int{7}
}
func (m *UpdateOrInsertSettingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateOrInsertSettingReq.Unmarshal(m, b)
}
func (m *UpdateOrInsertSettingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateOrInsertSettingReq.Marshal(b, m, deterministic)
}
func (dst *UpdateOrInsertSettingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateOrInsertSettingReq.Merge(dst, src)
}
func (m *UpdateOrInsertSettingReq) XXX_Size() int {
	return xxx_messageInfo_UpdateOrInsertSettingReq.Size(m)
}
func (m *UpdateOrInsertSettingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateOrInsertSettingReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateOrInsertSettingReq proto.InternalMessageInfo

func (m *UpdateOrInsertSettingReq) GetID() string {
	if m != nil {
		return m.ID
	}
	return ""
}

func (m *UpdateOrInsertSettingReq) GetSettingInfo() *SettingInfo {
	if m != nil {
		return m.SettingInfo
	}
	return nil
}

type UpdateOrInsertSettingResp struct {
	ErrMsg               string   `protobuf:"bytes,1,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateOrInsertSettingResp) Reset()         { *m = UpdateOrInsertSettingResp{} }
func (m *UpdateOrInsertSettingResp) String() string { return proto.CompactTextString(m) }
func (*UpdateOrInsertSettingResp) ProtoMessage()    {}
func (*UpdateOrInsertSettingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_autoaward_f5a67070e9f9a2ef, []int{8}
}
func (m *UpdateOrInsertSettingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateOrInsertSettingResp.Unmarshal(m, b)
}
func (m *UpdateOrInsertSettingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateOrInsertSettingResp.Marshal(b, m, deterministic)
}
func (dst *UpdateOrInsertSettingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateOrInsertSettingResp.Merge(dst, src)
}
func (m *UpdateOrInsertSettingResp) XXX_Size() int {
	return xxx_messageInfo_UpdateOrInsertSettingResp.Size(m)
}
func (m *UpdateOrInsertSettingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateOrInsertSettingResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateOrInsertSettingResp proto.InternalMessageInfo

func (m *UpdateOrInsertSettingResp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

type ChangeSettingStatusReq struct {
	ID                   string        `protobuf:"bytes,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Status               SettingStatus `protobuf:"varint,2,opt,name=status,proto3,enum=autoaward.SettingStatus" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ChangeSettingStatusReq) Reset()         { *m = ChangeSettingStatusReq{} }
func (m *ChangeSettingStatusReq) String() string { return proto.CompactTextString(m) }
func (*ChangeSettingStatusReq) ProtoMessage()    {}
func (*ChangeSettingStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_autoaward_f5a67070e9f9a2ef, []int{9}
}
func (m *ChangeSettingStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeSettingStatusReq.Unmarshal(m, b)
}
func (m *ChangeSettingStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeSettingStatusReq.Marshal(b, m, deterministic)
}
func (dst *ChangeSettingStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeSettingStatusReq.Merge(dst, src)
}
func (m *ChangeSettingStatusReq) XXX_Size() int {
	return xxx_messageInfo_ChangeSettingStatusReq.Size(m)
}
func (m *ChangeSettingStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeSettingStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeSettingStatusReq proto.InternalMessageInfo

func (m *ChangeSettingStatusReq) GetID() string {
	if m != nil {
		return m.ID
	}
	return ""
}

func (m *ChangeSettingStatusReq) GetStatus() SettingStatus {
	if m != nil {
		return m.Status
	}
	return SettingStatus_All
}

type ChangeSettingStatusResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeSettingStatusResp) Reset()         { *m = ChangeSettingStatusResp{} }
func (m *ChangeSettingStatusResp) String() string { return proto.CompactTextString(m) }
func (*ChangeSettingStatusResp) ProtoMessage()    {}
func (*ChangeSettingStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_autoaward_f5a67070e9f9a2ef, []int{10}
}
func (m *ChangeSettingStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeSettingStatusResp.Unmarshal(m, b)
}
func (m *ChangeSettingStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeSettingStatusResp.Marshal(b, m, deterministic)
}
func (dst *ChangeSettingStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeSettingStatusResp.Merge(dst, src)
}
func (m *ChangeSettingStatusResp) XXX_Size() int {
	return xxx_messageInfo_ChangeSettingStatusResp.Size(m)
}
func (m *ChangeSettingStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeSettingStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeSettingStatusResp proto.InternalMessageInfo

// 发送选项
type ImSendOption struct {
	// 马甲包，默认全部都收到
	// market_id -> app_name 转换关系:
	// 0->tt, 2->huanyou, 5->maike, 6->mijing
	MarketIdList []uint32 `protobuf:"varint,1,rep,packed,name=market_id_list,json=marketIdList,proto3" json:"market_id_list,omitempty"`
	// 马甲包，默认全部都收到
	// 0->tt, 2->huanyou, 5->maike, 6->mijing
	AppNameList []string `protobuf:"bytes,2,rep,name=app_name_list,json=appNameList,proto3" json:"app_name_list,omitempty"`
	// 平台类型，默认全平台都收到
	// client_type -> app_platform 转换关系:
	// 0->android, 1->ios, 5->pc
	ClientTypeList []uint32 `protobuf:"varint,3,rep,packed,name=client_type_list,json=clientTypeList,proto3" json:"client_type_list,omitempty"`
	// 平台类型，默认全平台都收到
	// android, ios, pc
	AppPlatformList []string `protobuf:"bytes,4,rep,name=app_platform_list,json=appPlatformList,proto3" json:"app_platform_list,omitempty"`
	// 是否属于附件发送
	HasAttachment bool `protobuf:"varint,5,opt,name=has_attachment,json=hasAttachment,proto3" json:"has_attachment,omitempty"`
	// 附件有效时间 秒
	AttachmentTtl uint32 `protobuf:"varint,6,opt,name=attachment_ttl,json=attachmentTtl,proto3" json:"attachment_ttl,omitempty"`
	// 私聊：不推消息给发送方
	IgnoreFrom bool `protobuf:"varint,7,opt,name=ignore_from,json=ignoreFrom,proto3" json:"ignore_from,omitempty"`
	// 私聊：不推消息给接收方
	IgnoreTo bool `protobuf:"varint,8,opt,name=ignore_to,json=ignoreTo,proto3" json:"ignore_to,omitempty"`
	// 推送给所有订阅用户
	SendAllFollowers bool `protobuf:"varint,9,opt,name=send_all_followers,json=sendAllFollowers,proto3" json:"send_all_followers,omitempty"`
	// 是否1v1私聊离线推送
	WithOfflinePush      bool     `protobuf:"varint,10,opt,name=with_offline_push,json=withOfflinePush,proto3" json:"with_offline_push,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ImSendOption) Reset()         { *m = ImSendOption{} }
func (m *ImSendOption) String() string { return proto.CompactTextString(m) }
func (*ImSendOption) ProtoMessage()    {}
func (*ImSendOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_autoaward_f5a67070e9f9a2ef, []int{11}
}
func (m *ImSendOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImSendOption.Unmarshal(m, b)
}
func (m *ImSendOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImSendOption.Marshal(b, m, deterministic)
}
func (dst *ImSendOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImSendOption.Merge(dst, src)
}
func (m *ImSendOption) XXX_Size() int {
	return xxx_messageInfo_ImSendOption.Size(m)
}
func (m *ImSendOption) XXX_DiscardUnknown() {
	xxx_messageInfo_ImSendOption.DiscardUnknown(m)
}

var xxx_messageInfo_ImSendOption proto.InternalMessageInfo

func (m *ImSendOption) GetMarketIdList() []uint32 {
	if m != nil {
		return m.MarketIdList
	}
	return nil
}

func (m *ImSendOption) GetAppNameList() []string {
	if m != nil {
		return m.AppNameList
	}
	return nil
}

func (m *ImSendOption) GetClientTypeList() []uint32 {
	if m != nil {
		return m.ClientTypeList
	}
	return nil
}

func (m *ImSendOption) GetAppPlatformList() []string {
	if m != nil {
		return m.AppPlatformList
	}
	return nil
}

func (m *ImSendOption) GetHasAttachment() bool {
	if m != nil {
		return m.HasAttachment
	}
	return false
}

func (m *ImSendOption) GetAttachmentTtl() uint32 {
	if m != nil {
		return m.AttachmentTtl
	}
	return 0
}

func (m *ImSendOption) GetIgnoreFrom() bool {
	if m != nil {
		return m.IgnoreFrom
	}
	return false
}

func (m *ImSendOption) GetIgnoreTo() bool {
	if m != nil {
		return m.IgnoreTo
	}
	return false
}

func (m *ImSendOption) GetSendAllFollowers() bool {
	if m != nil {
		return m.SendAllFollowers
	}
	return false
}

func (m *ImSendOption) GetWithOfflinePush() bool {
	if m != nil {
		return m.WithOfflinePush
	}
	return false
}

// 文本消息
type ImText struct {
	// 文本内容
	Content string `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	// 高亮文字
	Highlight string `protobuf:"bytes,2,opt,name=highlight,proto3" json:"highlight,omitempty"`
	// 高亮文字对应的链接
	Url                  string   `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ImText) Reset()         { *m = ImText{} }
func (m *ImText) String() string { return proto.CompactTextString(m) }
func (*ImText) ProtoMessage()    {}
func (*ImText) Descriptor() ([]byte, []int) {
	return fileDescriptor_autoaward_f5a67070e9f9a2ef, []int{12}
}
func (m *ImText) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImText.Unmarshal(m, b)
}
func (m *ImText) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImText.Marshal(b, m, deterministic)
}
func (dst *ImText) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImText.Merge(dst, src)
}
func (m *ImText) XXX_Size() int {
	return xxx_messageInfo_ImText.Size(m)
}
func (m *ImText) XXX_DiscardUnknown() {
	xxx_messageInfo_ImText.DiscardUnknown(m)
}

var xxx_messageInfo_ImText proto.InternalMessageInfo

func (m *ImText) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *ImText) GetHighlight() string {
	if m != nil {
		return m.Highlight
	}
	return ""
}

func (m *ImText) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

type PushPlazaAssistantMessageReq struct {
	ToUid                uint32        `protobuf:"varint,1,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	Text                 *ImText       `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	Opt                  *ImSendOption `protobuf:"bytes,3,opt,name=opt,proto3" json:"opt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PushPlazaAssistantMessageReq) Reset()         { *m = PushPlazaAssistantMessageReq{} }
func (m *PushPlazaAssistantMessageReq) String() string { return proto.CompactTextString(m) }
func (*PushPlazaAssistantMessageReq) ProtoMessage()    {}
func (*PushPlazaAssistantMessageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_autoaward_f5a67070e9f9a2ef, []int{13}
}
func (m *PushPlazaAssistantMessageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushPlazaAssistantMessageReq.Unmarshal(m, b)
}
func (m *PushPlazaAssistantMessageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushPlazaAssistantMessageReq.Marshal(b, m, deterministic)
}
func (dst *PushPlazaAssistantMessageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushPlazaAssistantMessageReq.Merge(dst, src)
}
func (m *PushPlazaAssistantMessageReq) XXX_Size() int {
	return xxx_messageInfo_PushPlazaAssistantMessageReq.Size(m)
}
func (m *PushPlazaAssistantMessageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PushPlazaAssistantMessageReq.DiscardUnknown(m)
}

var xxx_messageInfo_PushPlazaAssistantMessageReq proto.InternalMessageInfo

func (m *PushPlazaAssistantMessageReq) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *PushPlazaAssistantMessageReq) GetText() *ImText {
	if m != nil {
		return m.Text
	}
	return nil
}

func (m *PushPlazaAssistantMessageReq) GetOpt() *ImSendOption {
	if m != nil {
		return m.Opt
	}
	return nil
}

type PushPlazaAssistantMessageResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushPlazaAssistantMessageResp) Reset()         { *m = PushPlazaAssistantMessageResp{} }
func (m *PushPlazaAssistantMessageResp) String() string { return proto.CompactTextString(m) }
func (*PushPlazaAssistantMessageResp) ProtoMessage()    {}
func (*PushPlazaAssistantMessageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_autoaward_f5a67070e9f9a2ef, []int{14}
}
func (m *PushPlazaAssistantMessageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushPlazaAssistantMessageResp.Unmarshal(m, b)
}
func (m *PushPlazaAssistantMessageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushPlazaAssistantMessageResp.Marshal(b, m, deterministic)
}
func (dst *PushPlazaAssistantMessageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushPlazaAssistantMessageResp.Merge(dst, src)
}
func (m *PushPlazaAssistantMessageResp) XXX_Size() int {
	return xxx_messageInfo_PushPlazaAssistantMessageResp.Size(m)
}
func (m *PushPlazaAssistantMessageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PushPlazaAssistantMessageResp.DiscardUnknown(m)
}

var xxx_messageInfo_PushPlazaAssistantMessageResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*DecoractionInfo)(nil), "autoaward.DecoractionInfo")
	proto.RegisterType((*MedalInfo)(nil), "autoaward.MedalInfo")
	proto.RegisterType((*AwardInfo)(nil), "autoaward.AwardInfo")
	proto.RegisterType((*SettingItem)(nil), "autoaward.SettingItem")
	proto.RegisterType((*SettingInfo)(nil), "autoaward.SettingInfo")
	proto.RegisterType((*GetSettingReq)(nil), "autoaward.GetSettingReq")
	proto.RegisterType((*GetSettingResp)(nil), "autoaward.GetSettingResp")
	proto.RegisterType((*UpdateOrInsertSettingReq)(nil), "autoaward.UpdateOrInsertSettingReq")
	proto.RegisterType((*UpdateOrInsertSettingResp)(nil), "autoaward.UpdateOrInsertSettingResp")
	proto.RegisterType((*ChangeSettingStatusReq)(nil), "autoaward.ChangeSettingStatusReq")
	proto.RegisterType((*ChangeSettingStatusResp)(nil), "autoaward.ChangeSettingStatusResp")
	proto.RegisterType((*ImSendOption)(nil), "autoaward.ImSendOption")
	proto.RegisterType((*ImText)(nil), "autoaward.ImText")
	proto.RegisterType((*PushPlazaAssistantMessageReq)(nil), "autoaward.PushPlazaAssistantMessageReq")
	proto.RegisterType((*PushPlazaAssistantMessageResp)(nil), "autoaward.PushPlazaAssistantMessageResp")
	proto.RegisterEnum("autoaward.SettingStatus", SettingStatus_name, SettingStatus_value)
	proto.RegisterEnum("autoaward.UserType", UserType_name, UserType_value)
	proto.RegisterEnum("autoaward.StrategyIDType", StrategyIDType_name, StrategyIDType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AutoawardClient is the client API for Autoaward service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AutoawardClient interface {
	// 获取设置
	GetSetting(ctx context.Context, in *GetSettingReq, opts ...grpc.CallOption) (*GetSettingResp, error)
	// 更新配置
	UpdateOrInsertSetting(ctx context.Context, in *UpdateOrInsertSettingReq, opts ...grpc.CallOption) (*UpdateOrInsertSettingResp, error)
	// 修改状态
	ChangeSettingStatus(ctx context.Context, in *ChangeSettingStatusReq, opts ...grpc.CallOption) (*ChangeSettingStatusResp, error)
	// 兼容新旧版本推送
	PushPlazaAssistantMessage(ctx context.Context, in *PushPlazaAssistantMessageReq, opts ...grpc.CallOption) (*PushPlazaAssistantMessageResp, error)
}

type autoawardClient struct {
	cc *grpc.ClientConn
}

func NewAutoawardClient(cc *grpc.ClientConn) AutoawardClient {
	return &autoawardClient{cc}
}

func (c *autoawardClient) GetSetting(ctx context.Context, in *GetSettingReq, opts ...grpc.CallOption) (*GetSettingResp, error) {
	out := new(GetSettingResp)
	err := c.cc.Invoke(ctx, "/autoaward.Autoaward/GetSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *autoawardClient) UpdateOrInsertSetting(ctx context.Context, in *UpdateOrInsertSettingReq, opts ...grpc.CallOption) (*UpdateOrInsertSettingResp, error) {
	out := new(UpdateOrInsertSettingResp)
	err := c.cc.Invoke(ctx, "/autoaward.Autoaward/UpdateOrInsertSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *autoawardClient) ChangeSettingStatus(ctx context.Context, in *ChangeSettingStatusReq, opts ...grpc.CallOption) (*ChangeSettingStatusResp, error) {
	out := new(ChangeSettingStatusResp)
	err := c.cc.Invoke(ctx, "/autoaward.Autoaward/ChangeSettingStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *autoawardClient) PushPlazaAssistantMessage(ctx context.Context, in *PushPlazaAssistantMessageReq, opts ...grpc.CallOption) (*PushPlazaAssistantMessageResp, error) {
	out := new(PushPlazaAssistantMessageResp)
	err := c.cc.Invoke(ctx, "/autoaward.Autoaward/PushPlazaAssistantMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AutoawardServer is the server API for Autoaward service.
type AutoawardServer interface {
	// 获取设置
	GetSetting(context.Context, *GetSettingReq) (*GetSettingResp, error)
	// 更新配置
	UpdateOrInsertSetting(context.Context, *UpdateOrInsertSettingReq) (*UpdateOrInsertSettingResp, error)
	// 修改状态
	ChangeSettingStatus(context.Context, *ChangeSettingStatusReq) (*ChangeSettingStatusResp, error)
	// 兼容新旧版本推送
	PushPlazaAssistantMessage(context.Context, *PushPlazaAssistantMessageReq) (*PushPlazaAssistantMessageResp, error)
}

func RegisterAutoawardServer(s *grpc.Server, srv AutoawardServer) {
	s.RegisterService(&_Autoaward_serviceDesc, srv)
}

func _Autoaward_GetSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSettingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AutoawardServer).GetSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/autoaward.Autoaward/GetSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AutoawardServer).GetSetting(ctx, req.(*GetSettingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Autoaward_UpdateOrInsertSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateOrInsertSettingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AutoawardServer).UpdateOrInsertSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/autoaward.Autoaward/UpdateOrInsertSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AutoawardServer).UpdateOrInsertSetting(ctx, req.(*UpdateOrInsertSettingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Autoaward_ChangeSettingStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeSettingStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AutoawardServer).ChangeSettingStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/autoaward.Autoaward/ChangeSettingStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AutoawardServer).ChangeSettingStatus(ctx, req.(*ChangeSettingStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Autoaward_PushPlazaAssistantMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushPlazaAssistantMessageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AutoawardServer).PushPlazaAssistantMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/autoaward.Autoaward/PushPlazaAssistantMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AutoawardServer).PushPlazaAssistantMessage(ctx, req.(*PushPlazaAssistantMessageReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Autoaward_serviceDesc = grpc.ServiceDesc{
	ServiceName: "autoaward.Autoaward",
	HandlerType: (*AutoawardServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetSetting",
			Handler:    _Autoaward_GetSetting_Handler,
		},
		{
			MethodName: "UpdateOrInsertSetting",
			Handler:    _Autoaward_UpdateOrInsertSetting_Handler,
		},
		{
			MethodName: "ChangeSettingStatus",
			Handler:    _Autoaward_ChangeSettingStatus_Handler,
		},
		{
			MethodName: "PushPlazaAssistantMessage",
			Handler:    _Autoaward_PushPlazaAssistantMessage_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "autoaward/autoaward.proto",
}

func init() {
	proto.RegisterFile("autoaward/autoaward.proto", fileDescriptor_autoaward_f5a67070e9f9a2ef)
}

var fileDescriptor_autoaward_f5a67070e9f9a2ef = []byte{
	// 1144 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x56, 0xdd, 0x6e, 0xdb, 0xb6,
	0x17, 0x8f, 0x2c, 0xd7, 0x1f, 0xc7, 0xb1, 0xe3, 0xb2, 0x6d, 0x22, 0xfb, 0xdf, 0xff, 0xea, 0x69,
	0x2d, 0xe6, 0x05, 0x5b, 0x3a, 0x78, 0xbb, 0xd8, 0x0a, 0x0c, 0x98, 0x1b, 0xa7, 0x83, 0x81, 0x7c,
	0x81, 0x49, 0x76, 0x31, 0x0c, 0x10, 0x18, 0x8b, 0xb6, 0x88, 0x4a, 0xa2, 0x46, 0xd2, 0x48, 0xb2,
	0xcb, 0xdd, 0xec, 0x05, 0xf6, 0x00, 0x7b, 0x87, 0xbd, 0xd2, 0x1e, 0x64, 0x20, 0x25, 0x5b, 0x72,
	0x12, 0x37, 0xbd, 0xd3, 0xf9, 0x9d, 0x1f, 0xcf, 0xe1, 0xf9, 0xa4, 0xa0, 0x43, 0xe6, 0x8a, 0x93,
	0x2b, 0x22, 0xfc, 0xd7, 0xcb, 0xaf, 0xbd, 0x44, 0x70, 0xc5, 0x51, 0x7d, 0x09, 0xb8, 0x47, 0xb0,
	0x35, 0xa2, 0x13, 0x2e, 0xc8, 0x44, 0x31, 0x1e, 0x8f, 0xe3, 0x29, 0x47, 0x6d, 0xb0, 0x47, 0x73,
	0xe1, 0x58, 0x3d, 0xab, 0xdf, 0xc4, 0xfa, 0x13, 0xb5, 0xa0, 0x34, 0x1e, 0x39, 0xa5, 0x9e, 0xd5,
	0xaf, 0xe3, 0xd2, 0x78, 0x84, 0x1c, 0xa8, 0xfe, 0x4c, 0x85, 0x64, 0x3c, 0x76, 0x6c, 0x03, 0x2e,
	0x44, 0xf7, 0x07, 0xa8, 0x1f, 0x51, 0x9f, 0x84, 0xc6, 0x90, 0x03, 0xd5, 0x54, 0x18, 0x65, 0xc6,
	0x16, 0x22, 0xda, 0x86, 0xca, 0xc1, 0xf5, 0x39, 0x8b, 0xa8, 0x31, 0xda, 0xc4, 0x99, 0xe4, 0xfe,
	0x5b, 0x82, 0xfa, 0x50, 0xdf, 0xcb, 0x9c, 0x7f, 0x0d, 0xb5, 0x0b, 0x49, 0xc5, 0xf9, 0x4d, 0x42,
	0x8d, 0x81, 0xd6, 0xe0, 0xc9, 0x5e, 0x1e, 0xca, 0x42, 0x85, 0x97, 0x24, 0xd4, 0x83, 0x86, 0x39,
	0x3d, 0x8c, 0xf8, 0x3c, 0x56, 0x99, 0xed, 0x22, 0x84, 0x9e, 0x67, 0xf6, 0x8d, 0x4d, 0xdb, 0xe8,
	0x73, 0x40, 0x5f, 0x38, 0xf5, 0x3e, 0x72, 0xca, 0x69, 0x5c, 0x99, 0xb8, 0x3c, 0x77, 0x4c, 0x22,
	0xea, 0x3c, 0x32, 0xba, 0x1c, 0x40, 0xa3, 0x3b, 0x49, 0x74, 0x2a, 0x3d, 0xab, 0xdf, 0x18, 0x74,
	0x0b, 0xf7, 0xbd, 0xc5, 0xc0, 0x77, 0xf2, 0x3e, 0x28, 0xe4, 0xce, 0xa9, 0x9a, 0xf3, 0x4f, 0x0b,
	0xe7, 0x97, 0x3a, 0x5c, 0x48, 0xf1, 0x27, 0x00, 0xc7, 0xf4, 0x4a, 0x27, 0x60, 0x44, 0x6e, 0x9c,
	0x5a, 0xcf, 0xea, 0xdb, 0xb8, 0x80, 0xa0, 0xa7, 0xf0, 0x68, 0xdf, 0xe4, 0xa2, 0x6e, 0x62, 0x4d,
	0x05, 0xf7, 0x6f, 0x0b, 0x1a, 0x67, 0x54, 0x29, 0x16, 0xcf, 0xc6, 0x8a, 0x46, 0xe8, 0x7b, 0x80,
	0x33, 0x25, 0x88, 0xa2, 0xb3, 0x9b, 0xac, 0x56, 0xad, 0x41, 0xa7, 0xe0, 0x3a, 0x57, 0x9a, 0x84,
	0x17, 0xc8, 0xa8, 0x0b, 0xb5, 0x11, 0x95, 0x13, 0xc1, 0x2e, 0x69, 0xd6, 0x20, 0x4b, 0x19, 0xbd,
	0x81, 0xe6, 0xb2, 0x98, 0x87, 0x4c, 0x2a, 0xc7, 0xee, 0xd9, 0xb7, 0x82, 0x5a, 0xea, 0xf1, 0x2a,
	0xd5, 0xfd, 0xa7, 0x94, 0x5f, 0x51, 0x07, 0x8a, 0xa0, 0x6c, 0x6a, 0x96, 0xd6, 0xd4, 0x7c, 0xeb,
	0xa2, 0x1c, 0x11, 0x35, 0x09, 0x4c, 0x51, 0xd2, 0x46, 0xcc, 0x01, 0xd3, 0x7d, 0x5a, 0xc8, 0x8b,
	0x99, 0x89, 0xfa, 0xdc, 0x5b, 0x3a, 0x63, 0xb1, 0x69, 0x40, 0x5d, 0xcc, 0x32, 0xce, 0x01, 0x7d,
	0xee, 0x20, 0xf6, 0x8d, 0xae, 0x62, 0x74, 0x0b, 0x11, 0xbd, 0x81, 0xcd, 0x42, 0xd6, 0xa4, 0x53,
	0x35, 0xe1, 0x6c, 0x17, 0x13, 0x95, 0xab, 0xf1, 0x0a, 0x17, 0x7d, 0x0d, 0x95, 0x33, 0x45, 0xd4,
	0x5c, 0x9a, 0x22, 0xb5, 0x06, 0xce, 0xdd, 0x53, 0xa9, 0x1e, 0x67, 0xbc, 0x6c, 0xe8, 0xea, 0xcb,
	0xa1, 0x73, 0x61, 0x73, 0x3f, 0x60, 0x31, 0x95, 0xf4, 0x90, 0x45, 0x4c, 0x39, 0x60, 0x32, 0xb1,
	0x82, 0xb9, 0x43, 0x68, 0xfe, 0x44, 0x55, 0x66, 0x0f, 0xd3, 0xdf, 0xb4, 0x5b, 0x99, 0xba, 0xb5,
	0x1e, 0x72, 0x9b, 0xf2, 0x5c, 0x0c, 0xad, 0xa2, 0x09, 0x99, 0xa0, 0x1f, 0x61, 0xab, 0x50, 0x09,
	0x53, 0x48, 0x6b, 0x6d, 0xe4, 0xa6, 0xb3, 0x6f, 0xd1, 0x5d, 0x1f, 0x9c, 0x8b, 0xc4, 0x27, 0x8a,
	0x9e, 0x88, 0x71, 0x2c, 0xa9, 0x28, 0xde, 0x30, 0x0d, 0xd3, 0x5a, 0x86, 0xf9, 0xdd, 0x4a, 0xdd,
	0x4d, 0xbd, 0xd7, 0x7b, 0x2a, 0x52, 0xdd, 0x6f, 0xa1, 0xb3, 0xc6, 0x8b, 0x4c, 0xd0, 0x0e, 0x54,
	0xa9, 0x10, 0x5e, 0x24, 0x67, 0x99, 0xaf, 0x0a, 0x15, 0xe2, 0x48, 0xce, 0xdc, 0x5f, 0x60, 0x7b,
	0x3f, 0x20, 0xf1, 0x8c, 0xae, 0xa6, 0xe3, 0x9e, 0x9b, 0xe5, 0xb9, 0x2c, 0x7d, 0x64, 0x2e, 0x3b,
	0xb0, 0x73, 0xaf, 0x6d, 0x99, 0xb8, 0x7f, 0xd9, 0xb0, 0x39, 0x8e, 0xce, 0x68, 0xec, 0x9f, 0x24,
	0x7a, 0x03, 0xa0, 0x97, 0xd0, 0x8a, 0x88, 0x78, 0x4f, 0x95, 0xc7, 0x7c, 0x2f, 0x5c, 0x24, 0xb9,
	0x89, 0x37, 0x53, 0x74, 0xec, 0xeb, 0x4c, 0x22, 0x17, 0x9a, 0x24, 0x49, 0xbc, 0x98, 0x44, 0x34,
	0x25, 0x95, 0x7a, 0x76, 0xbf, 0x8e, 0x1b, 0x24, 0x49, 0x74, 0xd3, 0x1b, 0x4e, 0x1f, 0xda, 0x93,
	0x90, 0xd1, 0x58, 0x79, 0xea, 0x26, 0xc9, 0x68, 0xb6, 0xb1, 0xd5, 0x4a, 0x71, 0x3d, 0x3c, 0x86,
	0xb9, 0x0b, 0x8f, 0xb5, 0xb5, 0x24, 0x24, 0x6a, 0xca, 0x45, 0x94, 0x52, 0xcb, 0xc6, 0xe2, 0x16,
	0x49, 0x92, 0xd3, 0x0c, 0x37, 0xdc, 0x57, 0xd0, 0x0a, 0x88, 0xf4, 0x88, 0x52, 0x64, 0x12, 0x44,
	0x34, 0x56, 0x66, 0x72, 0x6a, 0xb8, 0x19, 0x10, 0x39, 0x5c, 0x82, 0x9a, 0x96, 0x53, 0x3c, 0xa5,
	0x42, 0x33, 0x44, 0x4d, 0xdc, 0xcc, 0xd1, 0x73, 0x15, 0xa2, 0x17, 0xd0, 0x60, 0xb3, 0x98, 0x0b,
	0xea, 0x4d, 0x05, 0x8f, 0xcc, 0xb6, 0xab, 0x61, 0x48, 0xa1, 0x77, 0x82, 0x47, 0xe8, 0x7f, 0x50,
	0xcf, 0x08, 0x8a, 0x9b, 0x91, 0xa9, 0xe1, 0x5a, 0x0a, 0x9c, 0x73, 0xf4, 0x25, 0x20, 0x49, 0x63,
	0xdf, 0x23, 0x61, 0xe8, 0x4d, 0x79, 0x18, 0xf2, 0x2b, 0x2a, 0xa4, 0x19, 0x95, 0x1a, 0x6e, 0x6b,
	0xcd, 0x30, 0x0c, 0xdf, 0x2d, 0x70, 0x1d, 0xe5, 0x15, 0x53, 0x81, 0xc7, 0xa7, 0xd3, 0x90, 0xc5,
	0xd4, 0x4b, 0xe6, 0x32, 0x30, 0xd3, 0x53, 0xc3, 0x5b, 0x5a, 0x71, 0x92, 0xe2, 0xa7, 0x73, 0x19,
	0xb8, 0x18, 0x2a, 0xe3, 0xe8, 0x9c, 0x5e, 0x2b, 0xbd, 0x06, 0x26, 0x3c, 0x56, 0x3a, 0xd0, 0xb4,
	0x05, 0x16, 0xa2, 0x5e, 0x1f, 0x01, 0x9b, 0x05, 0x21, 0x9b, 0x05, 0x2a, 0xdb, 0x79, 0x39, 0xa0,
	0x5f, 0xcf, 0xb9, 0x08, 0xb3, 0x75, 0xa4, 0x3f, 0xdd, 0x3f, 0x2d, 0x78, 0xae, 0x8d, 0x9f, 0x86,
	0xe4, 0x77, 0x32, 0x94, 0x92, 0x49, 0x45, 0x62, 0x75, 0x44, 0xa5, 0x24, 0x33, 0xaa, 0x1b, 0xed,
	0x19, 0x54, 0x14, 0xf7, 0xe6, 0xcc, 0xcf, 0x9e, 0xc9, 0x47, 0x8a, 0x5f, 0x30, 0x1f, 0xbd, 0x82,
	0xb2, 0xa2, 0xd7, 0x2a, 0x1b, 0x81, 0xc7, 0x85, 0x6e, 0x4b, 0xaf, 0x88, 0x8d, 0x1a, 0x7d, 0x01,
	0x36, 0x4f, 0x94, 0x71, 0xd8, 0x18, 0xec, 0xac, 0xb0, 0xf2, 0xf6, 0xc2, 0x9a, 0xe3, 0xbe, 0x80,
	0xff, 0x7f, 0xe0, 0x22, 0x32, 0xd9, 0xfd, 0x0a, 0x9a, 0x2b, 0xad, 0x8a, 0xaa, 0x60, 0x0f, 0xc3,
	0xb0, 0xbd, 0x81, 0x00, 0x2a, 0xc7, 0x5c, 0x44, 0x24, 0x6c, 0x5b, 0x1a, 0x7c, 0x4b, 0xe2, 0x76,
	0x69, 0x77, 0x2f, 0x7f, 0xa0, 0x51, 0x0d, 0xca, 0xc7, 0x27, 0xc7, 0x07, 0xed, 0x0d, 0xd4, 0x80,
	0x6a, 0xf6, 0x02, 0xb5, 0x2d, 0x2d, 0x0c, 0xc3, 0xd0, 0x08, 0xa5, 0xdd, 0x43, 0x68, 0xad, 0x3e,
	0x25, 0x86, 0xcb, 0x55, 0xc0, 0xe2, 0x59, 0x7b, 0x03, 0x35, 0xf5, 0x03, 0xc8, 0x04, 0xf3, 0xc9,
	0x7b, 0xd2, 0xb6, 0x50, 0x0b, 0xe0, 0x90, 0x91, 0xf8, 0x7a, 0x6e, 0xe4, 0x92, 0x96, 0xcf, 0x02,
	0x3e, 0x9f, 0xa4, 0x7a, 0x7b, 0xf0, 0x87, 0x0d, 0xf5, 0xe1, 0x22, 0x5a, 0x34, 0x04, 0xc8, 0xf7,
	0x16, 0x2a, 0xce, 0xe6, 0xca, 0x46, 0xec, 0x76, 0xd6, 0x68, 0x64, 0x82, 0x2e, 0xe1, 0xd9, 0xbd,
	0x0b, 0x04, 0x7d, 0x56, 0xfc, 0xed, 0x58, 0xb3, 0xc8, 0xba, 0x2f, 0x1f, 0x26, 0xc9, 0x04, 0xfd,
	0x0a, 0x4f, 0xee, 0x59, 0x09, 0xe8, 0xd3, 0xc2, 0xe1, 0xfb, 0xd7, 0x51, 0xd7, 0x7d, 0x88, 0x22,
	0x13, 0x94, 0x40, 0x67, 0x6d, 0x81, 0xd1, 0xe7, 0x05, 0x03, 0x1f, 0xea, 0xc7, 0x6e, 0xff, 0xe3,
	0x88, 0x32, 0x71, 0x37, 0x2e, 0x2b, 0xe6, 0x8f, 0xf2, 0x9b, 0xff, 0x02, 0x00, 0x00, 0xff, 0xff,
	0x0b, 0xb7, 0xd1, 0xe2, 0x6e, 0x0a, 0x00, 0x00,
}
