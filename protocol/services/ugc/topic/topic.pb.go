// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ugc/topic.proto

package topic // import "golang.52tt.com/protocol/services/ugc/topic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type TopicQueryOption int32

const (
	TopicQueryOption_QueryAll     TopicQueryOption = 0
	TopicQueryOption_QueryEnable  TopicQueryOption = 1
	TopicQueryOption_QueryDisable TopicQueryOption = 2
)

var TopicQueryOption_name = map[int32]string{
	0: "QueryAll",
	1: "QueryEnable",
	2: "QueryDisable",
}
var TopicQueryOption_value = map[string]int32{
	"QueryAll":     0,
	"QueryEnable":  1,
	"QueryDisable": 2,
}

func (x TopicQueryOption) String() string {
	return proto.EnumName(TopicQueryOption_name, int32(x))
}
func (TopicQueryOption) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{0}
}

// 兼容旧版本,GetTopics用，其它勿用
type TopicQueryOptionEx int32

const (
	TopicQueryOptionEx_ExQueryEnable  TopicQueryOptionEx = 0
	TopicQueryOptionEx_ExQueryAll     TopicQueryOptionEx = 1
	TopicQueryOptionEx_ExQueryDisable TopicQueryOptionEx = 2
)

var TopicQueryOptionEx_name = map[int32]string{
	0: "ExQueryEnable",
	1: "ExQueryAll",
	2: "ExQueryDisable",
}
var TopicQueryOptionEx_value = map[string]int32{
	"ExQueryEnable":  0,
	"ExQueryAll":     1,
	"ExQueryDisable": 2,
}

func (x TopicQueryOptionEx) String() string {
	return proto.EnumName(TopicQueryOptionEx_name, int32(x))
}
func (TopicQueryOptionEx) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{1}
}

type TopicType int32

const (
	TopicType_TypeCollection   TopicType = 0
	TopicType_TypeTopic        TopicType = 1
	TopicType_TypeGeo          TopicType = 2
	TopicType_TypeDIY          TopicType = 3
	TopicType_TypeMood         TopicType = 4
	TopicType_TypeTalk         TopicType = 5
	TopicType_TypeGameDistrict TopicType = 6
)

var TopicType_name = map[int32]string{
	0: "TypeCollection",
	1: "TypeTopic",
	2: "TypeGeo",
	3: "TypeDIY",
	4: "TypeMood",
	5: "TypeTalk",
	6: "TypeGameDistrict",
}
var TopicType_value = map[string]int32{
	"TypeCollection":   0,
	"TypeTopic":        1,
	"TypeGeo":          2,
	"TypeDIY":          3,
	"TypeMood":         4,
	"TypeTalk":         5,
	"TypeGameDistrict": 6,
}

func (x TopicType) String() string {
	return proto.EnumName(TopicType_name, int32(x))
}
func (TopicType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{2}
}

type TopicSubscript int32

const (
	TopicSubscript_NoneSub   TopicSubscript = 0
	TopicSubscript_New       TopicSubscript = 1
	TopicSubscript_Recommend TopicSubscript = 2
	TopicSubscript_Hot       TopicSubscript = 3
	TopicSubscript_SecondNew TopicSubscript = 4
	TopicSubscript_Up        TopicSubscript = 5
	TopicSubscript_Activity  TopicSubscript = 6
)

var TopicSubscript_name = map[int32]string{
	0: "NoneSub",
	1: "New",
	2: "Recommend",
	3: "Hot",
	4: "SecondNew",
	5: "Up",
	6: "Activity",
}
var TopicSubscript_value = map[string]int32{
	"NoneSub":   0,
	"New":       1,
	"Recommend": 2,
	"Hot":       3,
	"SecondNew": 4,
	"Up":        5,
	"Activity":  6,
}

func (x TopicSubscript) String() string {
	return proto.EnumName(TopicSubscript_name, int32(x))
}
func (TopicSubscript) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{3}
}

type TopicPlatform int32

const (
	TopicPlatform_Android      TopicPlatform = 0
	TopicPlatform_iOS          TopicPlatform = 1
	TopicPlatform_All_Platform TopicPlatform = 255
)

var TopicPlatform_name = map[int32]string{
	0:   "Android",
	1:   "iOS",
	255: "All_Platform",
}
var TopicPlatform_value = map[string]int32{
	"Android":      0,
	"iOS":          1,
	"All_Platform": 255,
}

func (x TopicPlatform) String() string {
	return proto.EnumName(TopicPlatform_name, int32(x))
}
func (TopicPlatform) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{4}
}

type PersonAttr int32

const (
	PersonAttr_AllAttr PersonAttr = 0
	PersonAttr_Newbie  PersonAttr = 1
	PersonAttr_Gender  PersonAttr = 2
)

var PersonAttr_name = map[int32]string{
	0: "AllAttr",
	1: "Newbie",
	2: "Gender",
}
var PersonAttr_value = map[string]int32{
	"AllAttr": 0,
	"Newbie":  1,
	"Gender":  2,
}

func (x PersonAttr) String() string {
	return proto.EnumName(PersonAttr_name, int32(x))
}
func (PersonAttr) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{5}
}

type TopicGender int32

const (
	TopicGender_Female TopicGender = 0
	TopicGender_Male   TopicGender = 1
)

var TopicGender_name = map[int32]string{
	0: "Female",
	1: "Male",
}
var TopicGender_value = map[string]int32{
	"Female": 0,
	"Male":   1,
}

func (x TopicGender) String() string {
	return proto.EnumName(TopicGender_name, int32(x))
}
func (TopicGender) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{6}
}

type TopicInRecommendType int32

const (
	TopicInRecommendType_OLD_VERSION                      TopicInRecommendType = 0
	TopicInRecommendType_NEW_VERSION_TOP_POSITION_NORMAL  TopicInRecommendType = 1
	TopicInRecommendType_NEW_VERSION_TOP_POSITION_CATALOG TopicInRecommendType = 2
)

var TopicInRecommendType_name = map[int32]string{
	0: "OLD_VERSION",
	1: "NEW_VERSION_TOP_POSITION_NORMAL",
	2: "NEW_VERSION_TOP_POSITION_CATALOG",
}
var TopicInRecommendType_value = map[string]int32{
	"OLD_VERSION":                      0,
	"NEW_VERSION_TOP_POSITION_NORMAL":  1,
	"NEW_VERSION_TOP_POSITION_CATALOG": 2,
}

func (x TopicInRecommendType) String() string {
	return proto.EnumName(TopicInRecommendType_name, int32(x))
}
func (TopicInRecommendType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{7}
}

type WeightStatus int32

const (
	WeightStatus_WeiStDefault WeightStatus = 0
	WeightStatus_WeiStValid   WeightStatus = 1
	WeightStatus_WeiStExpired WeightStatus = 2
	WeightStatus_WeiStFuture  WeightStatus = 3
	WeightStatus_WeiNoConfig  WeightStatus = 4
)

var WeightStatus_name = map[int32]string{
	0: "WeiStDefault",
	1: "WeiStValid",
	2: "WeiStExpired",
	3: "WeiStFuture",
	4: "WeiNoConfig",
}
var WeightStatus_value = map[string]int32{
	"WeiStDefault": 0,
	"WeiStValid":   1,
	"WeiStExpired": 2,
	"WeiStFuture":  3,
	"WeiNoConfig":  4,
}

func (x WeightStatus) String() string {
	return proto.EnumName(WeightStatus_name, int32(x))
}
func (WeightStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{8}
}

type DIYSortNDays int32

const (
	DIYSortNDays_DefaultSort DIYSortNDays = 0
	DIYSortNDays_OneDay      DIYSortNDays = 1
	DIYSortNDays_ThreeDays   DIYSortNDays = 3
	DIYSortNDays_SevenDays   DIYSortNDays = 7
)

var DIYSortNDays_name = map[int32]string{
	0: "DefaultSort",
	1: "OneDay",
	3: "ThreeDays",
	7: "SevenDays",
}
var DIYSortNDays_value = map[string]int32{
	"DefaultSort": 0,
	"OneDay":      1,
	"ThreeDays":   3,
	"SevenDays":   7,
}

func (x DIYSortNDays) String() string {
	return proto.EnumName(DIYSortNDays_name, int32(x))
}
func (DIYSortNDays) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{9}
}

// 发布器 话题配置接口
type PublisherTopicAppid int32

const (
	PublisherTopicAppid_Appid_Default    PublisherTopicAppid = 0
	PublisherTopicAppid_Appid_TT         PublisherTopicAppid = 1
	PublisherTopicAppid_Appid_Huanyou    PublisherTopicAppid = 2
	PublisherTopicAppid_Appid_TT_Huanyou PublisherTopicAppid = 3
)

var PublisherTopicAppid_name = map[int32]string{
	0: "Appid_Default",
	1: "Appid_TT",
	2: "Appid_Huanyou",
	3: "Appid_TT_Huanyou",
}
var PublisherTopicAppid_value = map[string]int32{
	"Appid_Default":    0,
	"Appid_TT":         1,
	"Appid_Huanyou":    2,
	"Appid_TT_Huanyou": 3,
}

func (x PublisherTopicAppid) String() string {
	return proto.EnumName(PublisherTopicAppid_name, int32(x))
}
func (PublisherTopicAppid) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{10}
}

type PublisherTopicPlatform int32

const (
	PublisherTopicPlatform_Platform_Default     PublisherTopicPlatform = 0
	PublisherTopicPlatform_Platform_Android     PublisherTopicPlatform = 1
	PublisherTopicPlatform_Platform_IOS         PublisherTopicPlatform = 2
	PublisherTopicPlatform_Platform_Android_IOS PublisherTopicPlatform = 3
)

var PublisherTopicPlatform_name = map[int32]string{
	0: "Platform_Default",
	1: "Platform_Android",
	2: "Platform_IOS",
	3: "Platform_Android_IOS",
}
var PublisherTopicPlatform_value = map[string]int32{
	"Platform_Default":     0,
	"Platform_Android":     1,
	"Platform_IOS":         2,
	"Platform_Android_IOS": 3,
}

func (x PublisherTopicPlatform) String() string {
	return proto.EnumName(PublisherTopicPlatform_name, int32(x))
}
func (PublisherTopicPlatform) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{11}
}

type UserSex int32

const (
	UserSex_User_Female UserSex = 0
	UserSex_User_Male   UserSex = 1
)

var UserSex_name = map[int32]string{
	0: "User_Female",
	1: "User_Male",
}
var UserSex_value = map[string]int32{
	"User_Female": 0,
	"User_Male":   1,
}

func (x UserSex) String() string {
	return proto.EnumName(UserSex_name, int32(x))
}
func (UserSex) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{12}
}

type PublisherTopicShowUser int32

const (
	PublisherTopicShowUser_To_Default PublisherTopicShowUser = 0
	PublisherTopicShowUser_To_Total   PublisherTopicShowUser = 1
	PublisherTopicShowUser_To_Part    PublisherTopicShowUser = 2
	PublisherTopicShowUser_To_Newbie  PublisherTopicShowUser = 3
	PublisherTopicShowUser_To_SexAge  PublisherTopicShowUser = 4
)

var PublisherTopicShowUser_name = map[int32]string{
	0: "To_Default",
	1: "To_Total",
	2: "To_Part",
	3: "To_Newbie",
	4: "To_SexAge",
}
var PublisherTopicShowUser_value = map[string]int32{
	"To_Default": 0,
	"To_Total":   1,
	"To_Part":    2,
	"To_Newbie":  3,
	"To_SexAge":  4,
}

func (x PublisherTopicShowUser) String() string {
	return proto.EnumName(PublisherTopicShowUser_name, int32(x))
}
func (PublisherTopicShowUser) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{13}
}

type PublisherTopicNewInsert int32

const (
	PublisherTopicNewInsert_Is_Default  PublisherTopicNewInsert = 0
	PublisherTopicNewInsert_Is_New      PublisherTopicNewInsert = 1
	PublisherTopicNewInsert_Is_Continue PublisherTopicNewInsert = 2
)

var PublisherTopicNewInsert_name = map[int32]string{
	0: "Is_Default",
	1: "Is_New",
	2: "Is_Continue",
}
var PublisherTopicNewInsert_value = map[string]int32{
	"Is_Default":  0,
	"Is_New":      1,
	"Is_Continue": 2,
}

func (x PublisherTopicNewInsert) String() string {
	return proto.EnumName(PublisherTopicNewInsert_name, int32(x))
}
func (PublisherTopicNewInsert) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{14}
}

// 话题详情页广告位 && 心情详情页广告位
type AdType int32

const (
	AdType_TotalAd AdType = 0
	AdType_TopicAd AdType = 1
	AdType_MoodAd  AdType = 2
)

var AdType_name = map[int32]string{
	0: "TotalAd",
	1: "TopicAd",
	2: "MoodAd",
}
var AdType_value = map[string]int32{
	"TotalAd": 0,
	"TopicAd": 1,
	"MoodAd":  2,
}

func (x AdType) String() string {
	return proto.EnumName(AdType_name, int32(x))
}
func (AdType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{15}
}

type TopicConfigCommonStatus int32

const (
	TopicConfigCommonStatus_Total    TopicConfigCommonStatus = 0
	TopicConfigCommonStatus_NotBegin TopicConfigCommonStatus = 1
	TopicConfigCommonStatus_InConfig TopicConfigCommonStatus = 2
	TopicConfigCommonStatus_Expired  TopicConfigCommonStatus = 3
)

var TopicConfigCommonStatus_name = map[int32]string{
	0: "Total",
	1: "NotBegin",
	2: "InConfig",
	3: "Expired",
}
var TopicConfigCommonStatus_value = map[string]int32{
	"Total":    0,
	"NotBegin": 1,
	"InConfig": 2,
	"Expired":  3,
}

func (x TopicConfigCommonStatus) String() string {
	return proto.EnumName(TopicConfigCommonStatus_name, int32(x))
}
func (TopicConfigCommonStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{16}
}

// 话题贴子推荐流强插配置的状态
type TopicStreamForceConfStatus int32

const (
	TopicStreamForceConfStatus_TopicStreamForceConfStatusAll      TopicStreamForceConfStatus = 0
	TopicStreamForceConfStatus_TopicStreamForceConfStatusInactive TopicStreamForceConfStatus = 1
	TopicStreamForceConfStatus_TopicStreamForceConfStatusActive   TopicStreamForceConfStatus = 2
	TopicStreamForceConfStatus_TopicStreamForceConfStatusExpired  TopicStreamForceConfStatus = 3
)

var TopicStreamForceConfStatus_name = map[int32]string{
	0: "TopicStreamForceConfStatusAll",
	1: "TopicStreamForceConfStatusInactive",
	2: "TopicStreamForceConfStatusActive",
	3: "TopicStreamForceConfStatusExpired",
}
var TopicStreamForceConfStatus_value = map[string]int32{
	"TopicStreamForceConfStatusAll":      0,
	"TopicStreamForceConfStatusInactive": 1,
	"TopicStreamForceConfStatusActive":   2,
	"TopicStreamForceConfStatusExpired":  3,
}

func (x TopicStreamForceConfStatus) String() string {
	return proto.EnumName(TopicStreamForceConfStatus_name, int32(x))
}
func (TopicStreamForceConfStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{17}
}

type PostButtonConfigStatus int32

const (
	PostButtonConfigStatus_Post_Button_Config_None    PostButtonConfigStatus = 0
	PostButtonConfigStatus_Post_Button_Config_Valid   PostButtonConfigStatus = 1
	PostButtonConfigStatus_Post_Button_Config_Expired PostButtonConfigStatus = 2
	PostButtonConfigStatus_Post_Button_Config_NotYet  PostButtonConfigStatus = 3
)

var PostButtonConfigStatus_name = map[int32]string{
	0: "Post_Button_Config_None",
	1: "Post_Button_Config_Valid",
	2: "Post_Button_Config_Expired",
	3: "Post_Button_Config_NotYet",
}
var PostButtonConfigStatus_value = map[string]int32{
	"Post_Button_Config_None":    0,
	"Post_Button_Config_Valid":   1,
	"Post_Button_Config_Expired": 2,
	"Post_Button_Config_NotYet":  3,
}

func (x PostButtonConfigStatus) String() string {
	return proto.EnumName(PostButtonConfigStatus_name, int32(x))
}
func (PostButtonConfigStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{18}
}

type ReportTopicFeedbackResp_ReportResult int32

const (
	ReportTopicFeedbackResp_Success   ReportTopicFeedbackResp_ReportResult = 0
	ReportTopicFeedbackResp_Duplicate ReportTopicFeedbackResp_ReportResult = 1
)

var ReportTopicFeedbackResp_ReportResult_name = map[int32]string{
	0: "Success",
	1: "Duplicate",
}
var ReportTopicFeedbackResp_ReportResult_value = map[string]int32{
	"Success":   0,
	"Duplicate": 1,
}

func (x ReportTopicFeedbackResp_ReportResult) String() string {
	return proto.EnumName(ReportTopicFeedbackResp_ReportResult_name, int32(x))
}
func (ReportTopicFeedbackResp_ReportResult) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{137, 0}
}

type TopicBindGameInfo struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GameName             string   `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TopicBindGameInfo) Reset()         { *m = TopicBindGameInfo{} }
func (m *TopicBindGameInfo) String() string { return proto.CompactTextString(m) }
func (*TopicBindGameInfo) ProtoMessage()    {}
func (*TopicBindGameInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{0}
}
func (m *TopicBindGameInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicBindGameInfo.Unmarshal(m, b)
}
func (m *TopicBindGameInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicBindGameInfo.Marshal(b, m, deterministic)
}
func (dst *TopicBindGameInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicBindGameInfo.Merge(dst, src)
}
func (m *TopicBindGameInfo) XXX_Size() int {
	return xxx_messageInfo_TopicBindGameInfo.Size(m)
}
func (m *TopicBindGameInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicBindGameInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TopicBindGameInfo proto.InternalMessageInfo

func (m *TopicBindGameInfo) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *TopicBindGameInfo) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

type CreateTopicReq struct {
	Name                 string             `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Desc                 string             `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	IconUrl              string             `protobuf:"bytes,3,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	BindUid              uint32             `protobuf:"varint,4,opt,name=bind_uid,json=bindUid,proto3" json:"bind_uid,omitempty"` // Deprecated: Do not use.
	TopicType            TopicType          `protobuf:"varint,5,opt,name=topic_type,json=topicType,proto3,enum=ugc.topic.TopicType" json:"topic_type,omitempty"`
	ParentTopicId        string             `protobuf:"bytes,6,opt,name=parent_topic_id,json=parentTopicId,proto3" json:"parent_topic_id,omitempty"`
	WeightInfo           *TopicWeightInfo   `protobuf:"bytes,7,opt,name=weight_info,json=weightInfo,proto3" json:"weight_info,omitempty"`
	Game                 *TopicBindGameInfo `protobuf:"bytes,8,opt,name=game,proto3" json:"game,omitempty"`
	RelatedTopicList     []*RelatedTopic    `protobuf:"bytes,9,rep,name=related_topic_list,json=relatedTopicList,proto3" json:"related_topic_list,omitempty"`
	Enable               bool               `protobuf:"varint,10,opt,name=enable,proto3" json:"enable,omitempty"`
	MultiPicUrlList      []string           `protobuf:"bytes,11,rep,name=multi_pic_url_list,json=multiPicUrlList,proto3" json:"multi_pic_url_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *CreateTopicReq) Reset()         { *m = CreateTopicReq{} }
func (m *CreateTopicReq) String() string { return proto.CompactTextString(m) }
func (*CreateTopicReq) ProtoMessage()    {}
func (*CreateTopicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{1}
}
func (m *CreateTopicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateTopicReq.Unmarshal(m, b)
}
func (m *CreateTopicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateTopicReq.Marshal(b, m, deterministic)
}
func (dst *CreateTopicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateTopicReq.Merge(dst, src)
}
func (m *CreateTopicReq) XXX_Size() int {
	return xxx_messageInfo_CreateTopicReq.Size(m)
}
func (m *CreateTopicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateTopicReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateTopicReq proto.InternalMessageInfo

func (m *CreateTopicReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *CreateTopicReq) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *CreateTopicReq) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

// Deprecated: Do not use.
func (m *CreateTopicReq) GetBindUid() uint32 {
	if m != nil {
		return m.BindUid
	}
	return 0
}

func (m *CreateTopicReq) GetTopicType() TopicType {
	if m != nil {
		return m.TopicType
	}
	return TopicType_TypeCollection
}

func (m *CreateTopicReq) GetParentTopicId() string {
	if m != nil {
		return m.ParentTopicId
	}
	return ""
}

func (m *CreateTopicReq) GetWeightInfo() *TopicWeightInfo {
	if m != nil {
		return m.WeightInfo
	}
	return nil
}

func (m *CreateTopicReq) GetGame() *TopicBindGameInfo {
	if m != nil {
		return m.Game
	}
	return nil
}

func (m *CreateTopicReq) GetRelatedTopicList() []*RelatedTopic {
	if m != nil {
		return m.RelatedTopicList
	}
	return nil
}

func (m *CreateTopicReq) GetEnable() bool {
	if m != nil {
		return m.Enable
	}
	return false
}

func (m *CreateTopicReq) GetMultiPicUrlList() []string {
	if m != nil {
		return m.MultiPicUrlList
	}
	return nil
}

type CreateTopicResp struct {
	TopicId              string   `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	IsNew                bool     `protobuf:"varint,2,opt,name=is_new,json=isNew,proto3" json:"is_new,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateTopicResp) Reset()         { *m = CreateTopicResp{} }
func (m *CreateTopicResp) String() string { return proto.CompactTextString(m) }
func (*CreateTopicResp) ProtoMessage()    {}
func (*CreateTopicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{2}
}
func (m *CreateTopicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateTopicResp.Unmarshal(m, b)
}
func (m *CreateTopicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateTopicResp.Marshal(b, m, deterministic)
}
func (dst *CreateTopicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateTopicResp.Merge(dst, src)
}
func (m *CreateTopicResp) XXX_Size() int {
	return xxx_messageInfo_CreateTopicResp.Size(m)
}
func (m *CreateTopicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateTopicResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateTopicResp proto.InternalMessageInfo

func (m *CreateTopicResp) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *CreateTopicResp) GetIsNew() bool {
	if m != nil {
		return m.IsNew
	}
	return false
}

type EnableTopicReq struct {
	TopicId              string   `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	Enable               bool     `protobuf:"varint,2,opt,name=enable,proto3" json:"enable,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EnableTopicReq) Reset()         { *m = EnableTopicReq{} }
func (m *EnableTopicReq) String() string { return proto.CompactTextString(m) }
func (*EnableTopicReq) ProtoMessage()    {}
func (*EnableTopicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{3}
}
func (m *EnableTopicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EnableTopicReq.Unmarshal(m, b)
}
func (m *EnableTopicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EnableTopicReq.Marshal(b, m, deterministic)
}
func (dst *EnableTopicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EnableTopicReq.Merge(dst, src)
}
func (m *EnableTopicReq) XXX_Size() int {
	return xxx_messageInfo_EnableTopicReq.Size(m)
}
func (m *EnableTopicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_EnableTopicReq.DiscardUnknown(m)
}

var xxx_messageInfo_EnableTopicReq proto.InternalMessageInfo

func (m *EnableTopicReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *EnableTopicReq) GetEnable() bool {
	if m != nil {
		return m.Enable
	}
	return false
}

type EnableTopicResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EnableTopicResp) Reset()         { *m = EnableTopicResp{} }
func (m *EnableTopicResp) String() string { return proto.CompactTextString(m) }
func (*EnableTopicResp) ProtoMessage()    {}
func (*EnableTopicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{4}
}
func (m *EnableTopicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EnableTopicResp.Unmarshal(m, b)
}
func (m *EnableTopicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EnableTopicResp.Marshal(b, m, deterministic)
}
func (dst *EnableTopicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EnableTopicResp.Merge(dst, src)
}
func (m *EnableTopicResp) XXX_Size() int {
	return xxx_messageInfo_EnableTopicResp.Size(m)
}
func (m *EnableTopicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_EnableTopicResp.DiscardUnknown(m)
}

var xxx_messageInfo_EnableTopicResp proto.InternalMessageInfo

type UpdateTopicInfoReq struct {
	TopicId              string             `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	Name                 string             `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Desc                 string             `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	IconUrl              string             `protobuf:"bytes,4,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	BindUid              uint32             `protobuf:"varint,5,opt,name=bind_uid,json=bindUid,proto3" json:"bind_uid,omitempty"` // Deprecated: Do not use.
	WeightInfo           *TopicWeightInfo   `protobuf:"bytes,6,opt,name=weight_info,json=weightInfo,proto3" json:"weight_info,omitempty"`
	Game                 *TopicBindGameInfo `protobuf:"bytes,7,opt,name=game,proto3" json:"game,omitempty"`
	RelatedTopicList     []*RelatedTopic    `protobuf:"bytes,8,rep,name=related_topic_list,json=relatedTopicList,proto3" json:"related_topic_list,omitempty"`
	MultiPicUrlList      []string           `protobuf:"bytes,9,rep,name=multi_pic_url_list,json=multiPicUrlList,proto3" json:"multi_pic_url_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *UpdateTopicInfoReq) Reset()         { *m = UpdateTopicInfoReq{} }
func (m *UpdateTopicInfoReq) String() string { return proto.CompactTextString(m) }
func (*UpdateTopicInfoReq) ProtoMessage()    {}
func (*UpdateTopicInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{5}
}
func (m *UpdateTopicInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateTopicInfoReq.Unmarshal(m, b)
}
func (m *UpdateTopicInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateTopicInfoReq.Marshal(b, m, deterministic)
}
func (dst *UpdateTopicInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTopicInfoReq.Merge(dst, src)
}
func (m *UpdateTopicInfoReq) XXX_Size() int {
	return xxx_messageInfo_UpdateTopicInfoReq.Size(m)
}
func (m *UpdateTopicInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTopicInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTopicInfoReq proto.InternalMessageInfo

func (m *UpdateTopicInfoReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *UpdateTopicInfoReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *UpdateTopicInfoReq) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *UpdateTopicInfoReq) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

// Deprecated: Do not use.
func (m *UpdateTopicInfoReq) GetBindUid() uint32 {
	if m != nil {
		return m.BindUid
	}
	return 0
}

func (m *UpdateTopicInfoReq) GetWeightInfo() *TopicWeightInfo {
	if m != nil {
		return m.WeightInfo
	}
	return nil
}

func (m *UpdateTopicInfoReq) GetGame() *TopicBindGameInfo {
	if m != nil {
		return m.Game
	}
	return nil
}

func (m *UpdateTopicInfoReq) GetRelatedTopicList() []*RelatedTopic {
	if m != nil {
		return m.RelatedTopicList
	}
	return nil
}

func (m *UpdateTopicInfoReq) GetMultiPicUrlList() []string {
	if m != nil {
		return m.MultiPicUrlList
	}
	return nil
}

type UpdateTopicInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateTopicInfoResp) Reset()         { *m = UpdateTopicInfoResp{} }
func (m *UpdateTopicInfoResp) String() string { return proto.CompactTextString(m) }
func (*UpdateTopicInfoResp) ProtoMessage()    {}
func (*UpdateTopicInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{6}
}
func (m *UpdateTopicInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateTopicInfoResp.Unmarshal(m, b)
}
func (m *UpdateTopicInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateTopicInfoResp.Marshal(b, m, deterministic)
}
func (dst *UpdateTopicInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTopicInfoResp.Merge(dst, src)
}
func (m *UpdateTopicInfoResp) XXX_Size() int {
	return xxx_messageInfo_UpdateTopicInfoResp.Size(m)
}
func (m *UpdateTopicInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTopicInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTopicInfoResp proto.InternalMessageInfo

// 从自定义修改为官方话题
type DIY2OfficialTopicReq struct {
	TopicId              string             `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	Desc                 string             `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	IconUrl              string             `protobuf:"bytes,3,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	TopicType            TopicType          `protobuf:"varint,4,opt,name=topic_type,json=topicType,proto3,enum=ugc.topic.TopicType" json:"topic_type,omitempty"`
	ParentTopicId        string             `protobuf:"bytes,5,opt,name=parent_topic_id,json=parentTopicId,proto3" json:"parent_topic_id,omitempty"`
	WeightInfo           *TopicWeightInfo   `protobuf:"bytes,6,opt,name=weight_info,json=weightInfo,proto3" json:"weight_info,omitempty"`
	Game                 *TopicBindGameInfo `protobuf:"bytes,7,opt,name=game,proto3" json:"game,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *DIY2OfficialTopicReq) Reset()         { *m = DIY2OfficialTopicReq{} }
func (m *DIY2OfficialTopicReq) String() string { return proto.CompactTextString(m) }
func (*DIY2OfficialTopicReq) ProtoMessage()    {}
func (*DIY2OfficialTopicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{7}
}
func (m *DIY2OfficialTopicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DIY2OfficialTopicReq.Unmarshal(m, b)
}
func (m *DIY2OfficialTopicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DIY2OfficialTopicReq.Marshal(b, m, deterministic)
}
func (dst *DIY2OfficialTopicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DIY2OfficialTopicReq.Merge(dst, src)
}
func (m *DIY2OfficialTopicReq) XXX_Size() int {
	return xxx_messageInfo_DIY2OfficialTopicReq.Size(m)
}
func (m *DIY2OfficialTopicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DIY2OfficialTopicReq.DiscardUnknown(m)
}

var xxx_messageInfo_DIY2OfficialTopicReq proto.InternalMessageInfo

func (m *DIY2OfficialTopicReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *DIY2OfficialTopicReq) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *DIY2OfficialTopicReq) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *DIY2OfficialTopicReq) GetTopicType() TopicType {
	if m != nil {
		return m.TopicType
	}
	return TopicType_TypeCollection
}

func (m *DIY2OfficialTopicReq) GetParentTopicId() string {
	if m != nil {
		return m.ParentTopicId
	}
	return ""
}

func (m *DIY2OfficialTopicReq) GetWeightInfo() *TopicWeightInfo {
	if m != nil {
		return m.WeightInfo
	}
	return nil
}

func (m *DIY2OfficialTopicReq) GetGame() *TopicBindGameInfo {
	if m != nil {
		return m.Game
	}
	return nil
}

type DIY2OfficialTopicResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DIY2OfficialTopicResp) Reset()         { *m = DIY2OfficialTopicResp{} }
func (m *DIY2OfficialTopicResp) String() string { return proto.CompactTextString(m) }
func (*DIY2OfficialTopicResp) ProtoMessage()    {}
func (*DIY2OfficialTopicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{8}
}
func (m *DIY2OfficialTopicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DIY2OfficialTopicResp.Unmarshal(m, b)
}
func (m *DIY2OfficialTopicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DIY2OfficialTopicResp.Marshal(b, m, deterministic)
}
func (dst *DIY2OfficialTopicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DIY2OfficialTopicResp.Merge(dst, src)
}
func (m *DIY2OfficialTopicResp) XXX_Size() int {
	return xxx_messageInfo_DIY2OfficialTopicResp.Size(m)
}
func (m *DIY2OfficialTopicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DIY2OfficialTopicResp.DiscardUnknown(m)
}

var xxx_messageInfo_DIY2OfficialTopicResp proto.InternalMessageInfo

type BatUpdateWeightReq struct {
	TopicIdList          []string         `protobuf:"bytes,1,rep,name=topic_id_list,json=topicIdList,proto3" json:"topic_id_list,omitempty"`
	WeightInfo           *TopicWeightInfo `protobuf:"bytes,7,opt,name=weight_info,json=weightInfo,proto3" json:"weight_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *BatUpdateWeightReq) Reset()         { *m = BatUpdateWeightReq{} }
func (m *BatUpdateWeightReq) String() string { return proto.CompactTextString(m) }
func (*BatUpdateWeightReq) ProtoMessage()    {}
func (*BatUpdateWeightReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{9}
}
func (m *BatUpdateWeightReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatUpdateWeightReq.Unmarshal(m, b)
}
func (m *BatUpdateWeightReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatUpdateWeightReq.Marshal(b, m, deterministic)
}
func (dst *BatUpdateWeightReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatUpdateWeightReq.Merge(dst, src)
}
func (m *BatUpdateWeightReq) XXX_Size() int {
	return xxx_messageInfo_BatUpdateWeightReq.Size(m)
}
func (m *BatUpdateWeightReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatUpdateWeightReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatUpdateWeightReq proto.InternalMessageInfo

func (m *BatUpdateWeightReq) GetTopicIdList() []string {
	if m != nil {
		return m.TopicIdList
	}
	return nil
}

func (m *BatUpdateWeightReq) GetWeightInfo() *TopicWeightInfo {
	if m != nil {
		return m.WeightInfo
	}
	return nil
}

type BatUpdateWeightResp struct {
	UpdateCnt            uint32   `protobuf:"varint,1,opt,name=update_cnt,json=updateCnt,proto3" json:"update_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatUpdateWeightResp) Reset()         { *m = BatUpdateWeightResp{} }
func (m *BatUpdateWeightResp) String() string { return proto.CompactTextString(m) }
func (*BatUpdateWeightResp) ProtoMessage()    {}
func (*BatUpdateWeightResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{10}
}
func (m *BatUpdateWeightResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatUpdateWeightResp.Unmarshal(m, b)
}
func (m *BatUpdateWeightResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatUpdateWeightResp.Marshal(b, m, deterministic)
}
func (dst *BatUpdateWeightResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatUpdateWeightResp.Merge(dst, src)
}
func (m *BatUpdateWeightResp) XXX_Size() int {
	return xxx_messageInfo_BatUpdateWeightResp.Size(m)
}
func (m *BatUpdateWeightResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatUpdateWeightResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatUpdateWeightResp proto.InternalMessageInfo

func (m *BatUpdateWeightResp) GetUpdateCnt() uint32 {
	if m != nil {
		return m.UpdateCnt
	}
	return 0
}

// 批量获取所有提权话题
type BatGetAllWeightTopicReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetAllWeightTopicReq) Reset()         { *m = BatGetAllWeightTopicReq{} }
func (m *BatGetAllWeightTopicReq) String() string { return proto.CompactTextString(m) }
func (*BatGetAllWeightTopicReq) ProtoMessage()    {}
func (*BatGetAllWeightTopicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{11}
}
func (m *BatGetAllWeightTopicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetAllWeightTopicReq.Unmarshal(m, b)
}
func (m *BatGetAllWeightTopicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetAllWeightTopicReq.Marshal(b, m, deterministic)
}
func (dst *BatGetAllWeightTopicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetAllWeightTopicReq.Merge(dst, src)
}
func (m *BatGetAllWeightTopicReq) XXX_Size() int {
	return xxx_messageInfo_BatGetAllWeightTopicReq.Size(m)
}
func (m *BatGetAllWeightTopicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetAllWeightTopicReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetAllWeightTopicReq proto.InternalMessageInfo

func (m *BatGetAllWeightTopicReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *BatGetAllWeightTopicReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type TopicWithWeight struct {
	TopicId              string   `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	Weight               string   `protobuf:"bytes,2,opt,name=weight,proto3" json:"weight,omitempty"`
	StartTs              uint32   `protobuf:"varint,3,opt,name=start_ts,json=startTs,proto3" json:"start_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,4,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TopicWithWeight) Reset()         { *m = TopicWithWeight{} }
func (m *TopicWithWeight) String() string { return proto.CompactTextString(m) }
func (*TopicWithWeight) ProtoMessage()    {}
func (*TopicWithWeight) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{12}
}
func (m *TopicWithWeight) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicWithWeight.Unmarshal(m, b)
}
func (m *TopicWithWeight) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicWithWeight.Marshal(b, m, deterministic)
}
func (dst *TopicWithWeight) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicWithWeight.Merge(dst, src)
}
func (m *TopicWithWeight) XXX_Size() int {
	return xxx_messageInfo_TopicWithWeight.Size(m)
}
func (m *TopicWithWeight) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicWithWeight.DiscardUnknown(m)
}

var xxx_messageInfo_TopicWithWeight proto.InternalMessageInfo

func (m *TopicWithWeight) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *TopicWithWeight) GetWeight() string {
	if m != nil {
		return m.Weight
	}
	return ""
}

func (m *TopicWithWeight) GetStartTs() uint32 {
	if m != nil {
		return m.StartTs
	}
	return 0
}

func (m *TopicWithWeight) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

type BatGetAllWeightTopicResp struct {
	WeightList           []*TopicWithWeight `protobuf:"bytes,1,rep,name=weight_list,json=weightList,proto3" json:"weight_list,omitempty"`
	TotalCnt             uint32             `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *BatGetAllWeightTopicResp) Reset()         { *m = BatGetAllWeightTopicResp{} }
func (m *BatGetAllWeightTopicResp) String() string { return proto.CompactTextString(m) }
func (*BatGetAllWeightTopicResp) ProtoMessage()    {}
func (*BatGetAllWeightTopicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{13}
}
func (m *BatGetAllWeightTopicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetAllWeightTopicResp.Unmarshal(m, b)
}
func (m *BatGetAllWeightTopicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetAllWeightTopicResp.Marshal(b, m, deterministic)
}
func (dst *BatGetAllWeightTopicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetAllWeightTopicResp.Merge(dst, src)
}
func (m *BatGetAllWeightTopicResp) XXX_Size() int {
	return xxx_messageInfo_BatGetAllWeightTopicResp.Size(m)
}
func (m *BatGetAllWeightTopicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetAllWeightTopicResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetAllWeightTopicResp proto.InternalMessageInfo

func (m *BatGetAllWeightTopicResp) GetWeightList() []*TopicWithWeight {
	if m != nil {
		return m.WeightList
	}
	return nil
}

func (m *BatGetAllWeightTopicResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

type TopicRecommendInfo struct {
	ConfigId             string   `protobuf:"bytes,1,opt,name=config_id,json=configId,proto3" json:"config_id,omitempty"`
	Index                uint32   `protobuf:"varint,2,opt,name=index,proto3" json:"index,omitempty"`
	TopicId              string   `protobuf:"bytes,3,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	Name                 string   `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	PicUrl               string   `protobuf:"bytes,5,opt,name=pic_url,json=picUrl,proto3" json:"pic_url,omitempty"`
	Subscript            uint32   `protobuf:"varint,6,opt,name=subscript,proto3" json:"subscript,omitempty"`
	Platform             uint32   `protobuf:"varint,7,opt,name=platform,proto3" json:"platform,omitempty"`
	PersonAttr           uint32   `protobuf:"varint,8,opt,name=person_attr,json=personAttr,proto3" json:"person_attr,omitempty"`
	StartTs              uint32   `protobuf:"varint,9,opt,name=start_ts,json=startTs,proto3" json:"start_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,10,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	Status               uint32   `protobuf:"varint,11,opt,name=status,proto3" json:"status,omitempty"`
	RegDays              uint32   `protobuf:"varint,12,opt,name=RegDays,proto3" json:"RegDays,omitempty"`
	Gender               uint32   `protobuf:"varint,13,opt,name=gender,proto3" json:"gender,omitempty"`
	TopicType            uint32   `protobuf:"varint,14,opt,name=topic_type,json=topicType,proto3" json:"topic_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TopicRecommendInfo) Reset()         { *m = TopicRecommendInfo{} }
func (m *TopicRecommendInfo) String() string { return proto.CompactTextString(m) }
func (*TopicRecommendInfo) ProtoMessage()    {}
func (*TopicRecommendInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{14}
}
func (m *TopicRecommendInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicRecommendInfo.Unmarshal(m, b)
}
func (m *TopicRecommendInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicRecommendInfo.Marshal(b, m, deterministic)
}
func (dst *TopicRecommendInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicRecommendInfo.Merge(dst, src)
}
func (m *TopicRecommendInfo) XXX_Size() int {
	return xxx_messageInfo_TopicRecommendInfo.Size(m)
}
func (m *TopicRecommendInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicRecommendInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TopicRecommendInfo proto.InternalMessageInfo

func (m *TopicRecommendInfo) GetConfigId() string {
	if m != nil {
		return m.ConfigId
	}
	return ""
}

func (m *TopicRecommendInfo) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *TopicRecommendInfo) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *TopicRecommendInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *TopicRecommendInfo) GetPicUrl() string {
	if m != nil {
		return m.PicUrl
	}
	return ""
}

func (m *TopicRecommendInfo) GetSubscript() uint32 {
	if m != nil {
		return m.Subscript
	}
	return 0
}

func (m *TopicRecommendInfo) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

func (m *TopicRecommendInfo) GetPersonAttr() uint32 {
	if m != nil {
		return m.PersonAttr
	}
	return 0
}

func (m *TopicRecommendInfo) GetStartTs() uint32 {
	if m != nil {
		return m.StartTs
	}
	return 0
}

func (m *TopicRecommendInfo) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *TopicRecommendInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *TopicRecommendInfo) GetRegDays() uint32 {
	if m != nil {
		return m.RegDays
	}
	return 0
}

func (m *TopicRecommendInfo) GetGender() uint32 {
	if m != nil {
		return m.Gender
	}
	return 0
}

func (m *TopicRecommendInfo) GetTopicType() uint32 {
	if m != nil {
		return m.TopicType
	}
	return 0
}

// 推荐流插入话题设置
type UpdatePutTopicInRecommendConfReq struct {
	Info                 *TopicRecommendInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	VersionType          uint32              `protobuf:"varint,2,opt,name=version_type,json=versionType,proto3" json:"version_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *UpdatePutTopicInRecommendConfReq) Reset()         { *m = UpdatePutTopicInRecommendConfReq{} }
func (m *UpdatePutTopicInRecommendConfReq) String() string { return proto.CompactTextString(m) }
func (*UpdatePutTopicInRecommendConfReq) ProtoMessage()    {}
func (*UpdatePutTopicInRecommendConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{15}
}
func (m *UpdatePutTopicInRecommendConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePutTopicInRecommendConfReq.Unmarshal(m, b)
}
func (m *UpdatePutTopicInRecommendConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePutTopicInRecommendConfReq.Marshal(b, m, deterministic)
}
func (dst *UpdatePutTopicInRecommendConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePutTopicInRecommendConfReq.Merge(dst, src)
}
func (m *UpdatePutTopicInRecommendConfReq) XXX_Size() int {
	return xxx_messageInfo_UpdatePutTopicInRecommendConfReq.Size(m)
}
func (m *UpdatePutTopicInRecommendConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePutTopicInRecommendConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePutTopicInRecommendConfReq proto.InternalMessageInfo

func (m *UpdatePutTopicInRecommendConfReq) GetInfo() *TopicRecommendInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *UpdatePutTopicInRecommendConfReq) GetVersionType() uint32 {
	if m != nil {
		return m.VersionType
	}
	return 0
}

type UpdatePutTopicInRecommendConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePutTopicInRecommendConfResp) Reset()         { *m = UpdatePutTopicInRecommendConfResp{} }
func (m *UpdatePutTopicInRecommendConfResp) String() string { return proto.CompactTextString(m) }
func (*UpdatePutTopicInRecommendConfResp) ProtoMessage()    {}
func (*UpdatePutTopicInRecommendConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{16}
}
func (m *UpdatePutTopicInRecommendConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePutTopicInRecommendConfResp.Unmarshal(m, b)
}
func (m *UpdatePutTopicInRecommendConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePutTopicInRecommendConfResp.Marshal(b, m, deterministic)
}
func (dst *UpdatePutTopicInRecommendConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePutTopicInRecommendConfResp.Merge(dst, src)
}
func (m *UpdatePutTopicInRecommendConfResp) XXX_Size() int {
	return xxx_messageInfo_UpdatePutTopicInRecommendConfResp.Size(m)
}
func (m *UpdatePutTopicInRecommendConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePutTopicInRecommendConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePutTopicInRecommendConfResp proto.InternalMessageInfo

type DelPutTopicInRecommendConfReq struct {
	ConfigIdList         []string `protobuf:"bytes,1,rep,name=config_id_list,json=configIdList,proto3" json:"config_id_list,omitempty"`
	VersionType          uint32   `protobuf:"varint,2,opt,name=version_type,json=versionType,proto3" json:"version_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPutTopicInRecommendConfReq) Reset()         { *m = DelPutTopicInRecommendConfReq{} }
func (m *DelPutTopicInRecommendConfReq) String() string { return proto.CompactTextString(m) }
func (*DelPutTopicInRecommendConfReq) ProtoMessage()    {}
func (*DelPutTopicInRecommendConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{17}
}
func (m *DelPutTopicInRecommendConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPutTopicInRecommendConfReq.Unmarshal(m, b)
}
func (m *DelPutTopicInRecommendConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPutTopicInRecommendConfReq.Marshal(b, m, deterministic)
}
func (dst *DelPutTopicInRecommendConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPutTopicInRecommendConfReq.Merge(dst, src)
}
func (m *DelPutTopicInRecommendConfReq) XXX_Size() int {
	return xxx_messageInfo_DelPutTopicInRecommendConfReq.Size(m)
}
func (m *DelPutTopicInRecommendConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPutTopicInRecommendConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelPutTopicInRecommendConfReq proto.InternalMessageInfo

func (m *DelPutTopicInRecommendConfReq) GetConfigIdList() []string {
	if m != nil {
		return m.ConfigIdList
	}
	return nil
}

func (m *DelPutTopicInRecommendConfReq) GetVersionType() uint32 {
	if m != nil {
		return m.VersionType
	}
	return 0
}

type DelPutTopicInRecommendConfResp struct {
	DelCnt               uint32   `protobuf:"varint,1,opt,name=del_cnt,json=delCnt,proto3" json:"del_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPutTopicInRecommendConfResp) Reset()         { *m = DelPutTopicInRecommendConfResp{} }
func (m *DelPutTopicInRecommendConfResp) String() string { return proto.CompactTextString(m) }
func (*DelPutTopicInRecommendConfResp) ProtoMessage()    {}
func (*DelPutTopicInRecommendConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{18}
}
func (m *DelPutTopicInRecommendConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPutTopicInRecommendConfResp.Unmarshal(m, b)
}
func (m *DelPutTopicInRecommendConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPutTopicInRecommendConfResp.Marshal(b, m, deterministic)
}
func (dst *DelPutTopicInRecommendConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPutTopicInRecommendConfResp.Merge(dst, src)
}
func (m *DelPutTopicInRecommendConfResp) XXX_Size() int {
	return xxx_messageInfo_DelPutTopicInRecommendConfResp.Size(m)
}
func (m *DelPutTopicInRecommendConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPutTopicInRecommendConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelPutTopicInRecommendConfResp proto.InternalMessageInfo

func (m *DelPutTopicInRecommendConfResp) GetDelCnt() uint32 {
	if m != nil {
		return m.DelCnt
	}
	return 0
}

// 获取推荐流插入话题配置
type GetPutTopicInRecommendConfReq struct {
	Index                uint32   `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`
	TopicId              string   `protobuf:"bytes,2,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Offset               uint32   `protobuf:"varint,4,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	VersionType          uint32   `protobuf:"varint,6,opt,name=version_type,json=versionType,proto3" json:"version_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPutTopicInRecommendConfReq) Reset()         { *m = GetPutTopicInRecommendConfReq{} }
func (m *GetPutTopicInRecommendConfReq) String() string { return proto.CompactTextString(m) }
func (*GetPutTopicInRecommendConfReq) ProtoMessage()    {}
func (*GetPutTopicInRecommendConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{19}
}
func (m *GetPutTopicInRecommendConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPutTopicInRecommendConfReq.Unmarshal(m, b)
}
func (m *GetPutTopicInRecommendConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPutTopicInRecommendConfReq.Marshal(b, m, deterministic)
}
func (dst *GetPutTopicInRecommendConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPutTopicInRecommendConfReq.Merge(dst, src)
}
func (m *GetPutTopicInRecommendConfReq) XXX_Size() int {
	return xxx_messageInfo_GetPutTopicInRecommendConfReq.Size(m)
}
func (m *GetPutTopicInRecommendConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPutTopicInRecommendConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPutTopicInRecommendConfReq proto.InternalMessageInfo

func (m *GetPutTopicInRecommendConfReq) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *GetPutTopicInRecommendConfReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *GetPutTopicInRecommendConfReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetPutTopicInRecommendConfReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetPutTopicInRecommendConfReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetPutTopicInRecommendConfReq) GetVersionType() uint32 {
	if m != nil {
		return m.VersionType
	}
	return 0
}

type GetPutTopicInRecommendConfResp struct {
	InfoList             []*TopicRecommendInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	TotalCnt             uint32                `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetPutTopicInRecommendConfResp) Reset()         { *m = GetPutTopicInRecommendConfResp{} }
func (m *GetPutTopicInRecommendConfResp) String() string { return proto.CompactTextString(m) }
func (*GetPutTopicInRecommendConfResp) ProtoMessage()    {}
func (*GetPutTopicInRecommendConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{20}
}
func (m *GetPutTopicInRecommendConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPutTopicInRecommendConfResp.Unmarshal(m, b)
}
func (m *GetPutTopicInRecommendConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPutTopicInRecommendConfResp.Marshal(b, m, deterministic)
}
func (dst *GetPutTopicInRecommendConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPutTopicInRecommendConfResp.Merge(dst, src)
}
func (m *GetPutTopicInRecommendConfResp) XXX_Size() int {
	return xxx_messageInfo_GetPutTopicInRecommendConfResp.Size(m)
}
func (m *GetPutTopicInRecommendConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPutTopicInRecommendConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPutTopicInRecommendConfResp proto.InternalMessageInfo

func (m *GetPutTopicInRecommendConfResp) GetInfoList() []*TopicRecommendInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetPutTopicInRecommendConfResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

// 新运营配置推荐流话题类
type TopicAndSubscript struct {
	TopicId              string   `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Subscript            uint32   `protobuf:"varint,3,opt,name=subscript,proto3" json:"subscript,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TopicAndSubscript) Reset()         { *m = TopicAndSubscript{} }
func (m *TopicAndSubscript) String() string { return proto.CompactTextString(m) }
func (*TopicAndSubscript) ProtoMessage()    {}
func (*TopicAndSubscript) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{21}
}
func (m *TopicAndSubscript) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicAndSubscript.Unmarshal(m, b)
}
func (m *TopicAndSubscript) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicAndSubscript.Marshal(b, m, deterministic)
}
func (dst *TopicAndSubscript) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicAndSubscript.Merge(dst, src)
}
func (m *TopicAndSubscript) XXX_Size() int {
	return xxx_messageInfo_TopicAndSubscript.Size(m)
}
func (m *TopicAndSubscript) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicAndSubscript.DiscardUnknown(m)
}

var xxx_messageInfo_TopicAndSubscript proto.InternalMessageInfo

func (m *TopicAndSubscript) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *TopicAndSubscript) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *TopicAndSubscript) GetSubscript() uint32 {
	if m != nil {
		return m.Subscript
	}
	return 0
}

type CatalogTopicInRcmdFeed struct {
	CatalogId            string               `protobuf:"bytes,1,opt,name=catalog_id,json=catalogId,proto3" json:"catalog_id,omitempty"`
	CatalogName          string               `protobuf:"bytes,2,opt,name=catalog_name,json=catalogName,proto3" json:"catalog_name,omitempty"`
	Index                uint32               `protobuf:"varint,3,opt,name=index,proto3" json:"index,omitempty"`
	TopicList            []*TopicAndSubscript `protobuf:"bytes,4,rep,name=topic_list,json=topicList,proto3" json:"topic_list,omitempty"`
	Platform             uint32               `protobuf:"varint,5,opt,name=platform,proto3" json:"platform,omitempty"`
	PersonAttr           uint32               `protobuf:"varint,6,opt,name=person_attr,json=personAttr,proto3" json:"person_attr,omitempty"`
	StartTs              uint32               `protobuf:"varint,7,opt,name=start_ts,json=startTs,proto3" json:"start_ts,omitempty"`
	EndTs                uint32               `protobuf:"varint,8,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	Status               uint32               `protobuf:"varint,9,opt,name=status,proto3" json:"status,omitempty"`
	RegDays              uint32               `protobuf:"varint,10,opt,name=RegDays,proto3" json:"RegDays,omitempty"`
	Gender               uint32               `protobuf:"varint,11,opt,name=gender,proto3" json:"gender,omitempty"`
	UpdateTime           uint32               `protobuf:"varint,12,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *CatalogTopicInRcmdFeed) Reset()         { *m = CatalogTopicInRcmdFeed{} }
func (m *CatalogTopicInRcmdFeed) String() string { return proto.CompactTextString(m) }
func (*CatalogTopicInRcmdFeed) ProtoMessage()    {}
func (*CatalogTopicInRcmdFeed) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{22}
}
func (m *CatalogTopicInRcmdFeed) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CatalogTopicInRcmdFeed.Unmarshal(m, b)
}
func (m *CatalogTopicInRcmdFeed) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CatalogTopicInRcmdFeed.Marshal(b, m, deterministic)
}
func (dst *CatalogTopicInRcmdFeed) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CatalogTopicInRcmdFeed.Merge(dst, src)
}
func (m *CatalogTopicInRcmdFeed) XXX_Size() int {
	return xxx_messageInfo_CatalogTopicInRcmdFeed.Size(m)
}
func (m *CatalogTopicInRcmdFeed) XXX_DiscardUnknown() {
	xxx_messageInfo_CatalogTopicInRcmdFeed.DiscardUnknown(m)
}

var xxx_messageInfo_CatalogTopicInRcmdFeed proto.InternalMessageInfo

func (m *CatalogTopicInRcmdFeed) GetCatalogId() string {
	if m != nil {
		return m.CatalogId
	}
	return ""
}

func (m *CatalogTopicInRcmdFeed) GetCatalogName() string {
	if m != nil {
		return m.CatalogName
	}
	return ""
}

func (m *CatalogTopicInRcmdFeed) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *CatalogTopicInRcmdFeed) GetTopicList() []*TopicAndSubscript {
	if m != nil {
		return m.TopicList
	}
	return nil
}

func (m *CatalogTopicInRcmdFeed) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

func (m *CatalogTopicInRcmdFeed) GetPersonAttr() uint32 {
	if m != nil {
		return m.PersonAttr
	}
	return 0
}

func (m *CatalogTopicInRcmdFeed) GetStartTs() uint32 {
	if m != nil {
		return m.StartTs
	}
	return 0
}

func (m *CatalogTopicInRcmdFeed) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *CatalogTopicInRcmdFeed) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *CatalogTopicInRcmdFeed) GetRegDays() uint32 {
	if m != nil {
		return m.RegDays
	}
	return 0
}

func (m *CatalogTopicInRcmdFeed) GetGender() uint32 {
	if m != nil {
		return m.Gender
	}
	return 0
}

func (m *CatalogTopicInRcmdFeed) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

// 分类的话题 推荐流顶部 运营后台新增、修改
type UpdateCatalogTopicInRcmdFeedReq struct {
	Catalogs             []*CatalogTopicInRcmdFeed `protobuf:"bytes,1,rep,name=catalogs,proto3" json:"catalogs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *UpdateCatalogTopicInRcmdFeedReq) Reset()         { *m = UpdateCatalogTopicInRcmdFeedReq{} }
func (m *UpdateCatalogTopicInRcmdFeedReq) String() string { return proto.CompactTextString(m) }
func (*UpdateCatalogTopicInRcmdFeedReq) ProtoMessage()    {}
func (*UpdateCatalogTopicInRcmdFeedReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{23}
}
func (m *UpdateCatalogTopicInRcmdFeedReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCatalogTopicInRcmdFeedReq.Unmarshal(m, b)
}
func (m *UpdateCatalogTopicInRcmdFeedReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCatalogTopicInRcmdFeedReq.Marshal(b, m, deterministic)
}
func (dst *UpdateCatalogTopicInRcmdFeedReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCatalogTopicInRcmdFeedReq.Merge(dst, src)
}
func (m *UpdateCatalogTopicInRcmdFeedReq) XXX_Size() int {
	return xxx_messageInfo_UpdateCatalogTopicInRcmdFeedReq.Size(m)
}
func (m *UpdateCatalogTopicInRcmdFeedReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCatalogTopicInRcmdFeedReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCatalogTopicInRcmdFeedReq proto.InternalMessageInfo

func (m *UpdateCatalogTopicInRcmdFeedReq) GetCatalogs() []*CatalogTopicInRcmdFeed {
	if m != nil {
		return m.Catalogs
	}
	return nil
}

type UpdateCatalogTopicInRcmdFeedResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateCatalogTopicInRcmdFeedResp) Reset()         { *m = UpdateCatalogTopicInRcmdFeedResp{} }
func (m *UpdateCatalogTopicInRcmdFeedResp) String() string { return proto.CompactTextString(m) }
func (*UpdateCatalogTopicInRcmdFeedResp) ProtoMessage()    {}
func (*UpdateCatalogTopicInRcmdFeedResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{24}
}
func (m *UpdateCatalogTopicInRcmdFeedResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCatalogTopicInRcmdFeedResp.Unmarshal(m, b)
}
func (m *UpdateCatalogTopicInRcmdFeedResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCatalogTopicInRcmdFeedResp.Marshal(b, m, deterministic)
}
func (dst *UpdateCatalogTopicInRcmdFeedResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCatalogTopicInRcmdFeedResp.Merge(dst, src)
}
func (m *UpdateCatalogTopicInRcmdFeedResp) XXX_Size() int {
	return xxx_messageInfo_UpdateCatalogTopicInRcmdFeedResp.Size(m)
}
func (m *UpdateCatalogTopicInRcmdFeedResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCatalogTopicInRcmdFeedResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCatalogTopicInRcmdFeedResp proto.InternalMessageInfo

type DelCatalogTopicInRcmdFeedReq struct {
	ConfigIdList         []string `protobuf:"bytes,1,rep,name=config_id_list,json=configIdList,proto3" json:"config_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelCatalogTopicInRcmdFeedReq) Reset()         { *m = DelCatalogTopicInRcmdFeedReq{} }
func (m *DelCatalogTopicInRcmdFeedReq) String() string { return proto.CompactTextString(m) }
func (*DelCatalogTopicInRcmdFeedReq) ProtoMessage()    {}
func (*DelCatalogTopicInRcmdFeedReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{25}
}
func (m *DelCatalogTopicInRcmdFeedReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelCatalogTopicInRcmdFeedReq.Unmarshal(m, b)
}
func (m *DelCatalogTopicInRcmdFeedReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelCatalogTopicInRcmdFeedReq.Marshal(b, m, deterministic)
}
func (dst *DelCatalogTopicInRcmdFeedReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelCatalogTopicInRcmdFeedReq.Merge(dst, src)
}
func (m *DelCatalogTopicInRcmdFeedReq) XXX_Size() int {
	return xxx_messageInfo_DelCatalogTopicInRcmdFeedReq.Size(m)
}
func (m *DelCatalogTopicInRcmdFeedReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelCatalogTopicInRcmdFeedReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelCatalogTopicInRcmdFeedReq proto.InternalMessageInfo

func (m *DelCatalogTopicInRcmdFeedReq) GetConfigIdList() []string {
	if m != nil {
		return m.ConfigIdList
	}
	return nil
}

type DelCatalogTopicInRcmdFeedResp struct {
	DelCnt               uint32   `protobuf:"varint,1,opt,name=del_cnt,json=delCnt,proto3" json:"del_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelCatalogTopicInRcmdFeedResp) Reset()         { *m = DelCatalogTopicInRcmdFeedResp{} }
func (m *DelCatalogTopicInRcmdFeedResp) String() string { return proto.CompactTextString(m) }
func (*DelCatalogTopicInRcmdFeedResp) ProtoMessage()    {}
func (*DelCatalogTopicInRcmdFeedResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{26}
}
func (m *DelCatalogTopicInRcmdFeedResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelCatalogTopicInRcmdFeedResp.Unmarshal(m, b)
}
func (m *DelCatalogTopicInRcmdFeedResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelCatalogTopicInRcmdFeedResp.Marshal(b, m, deterministic)
}
func (dst *DelCatalogTopicInRcmdFeedResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelCatalogTopicInRcmdFeedResp.Merge(dst, src)
}
func (m *DelCatalogTopicInRcmdFeedResp) XXX_Size() int {
	return xxx_messageInfo_DelCatalogTopicInRcmdFeedResp.Size(m)
}
func (m *DelCatalogTopicInRcmdFeedResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelCatalogTopicInRcmdFeedResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelCatalogTopicInRcmdFeedResp proto.InternalMessageInfo

func (m *DelCatalogTopicInRcmdFeedResp) GetDelCnt() uint32 {
	if m != nil {
		return m.DelCnt
	}
	return 0
}

type GetCatalogTopicInRcmdFeedReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Valid                bool     `protobuf:"varint,3,opt,name=valid,proto3" json:"valid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCatalogTopicInRcmdFeedReq) Reset()         { *m = GetCatalogTopicInRcmdFeedReq{} }
func (m *GetCatalogTopicInRcmdFeedReq) String() string { return proto.CompactTextString(m) }
func (*GetCatalogTopicInRcmdFeedReq) ProtoMessage()    {}
func (*GetCatalogTopicInRcmdFeedReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{27}
}
func (m *GetCatalogTopicInRcmdFeedReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCatalogTopicInRcmdFeedReq.Unmarshal(m, b)
}
func (m *GetCatalogTopicInRcmdFeedReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCatalogTopicInRcmdFeedReq.Marshal(b, m, deterministic)
}
func (dst *GetCatalogTopicInRcmdFeedReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCatalogTopicInRcmdFeedReq.Merge(dst, src)
}
func (m *GetCatalogTopicInRcmdFeedReq) XXX_Size() int {
	return xxx_messageInfo_GetCatalogTopicInRcmdFeedReq.Size(m)
}
func (m *GetCatalogTopicInRcmdFeedReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCatalogTopicInRcmdFeedReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCatalogTopicInRcmdFeedReq proto.InternalMessageInfo

func (m *GetCatalogTopicInRcmdFeedReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetCatalogTopicInRcmdFeedReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetCatalogTopicInRcmdFeedReq) GetValid() bool {
	if m != nil {
		return m.Valid
	}
	return false
}

type GetCatalogTopicInRcmdFeedResp struct {
	Catalogs             []*CatalogTopicInRcmdFeed `protobuf:"bytes,1,rep,name=catalogs,proto3" json:"catalogs,omitempty"`
	TotalCnt             uint32                    `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetCatalogTopicInRcmdFeedResp) Reset()         { *m = GetCatalogTopicInRcmdFeedResp{} }
func (m *GetCatalogTopicInRcmdFeedResp) String() string { return proto.CompactTextString(m) }
func (*GetCatalogTopicInRcmdFeedResp) ProtoMessage()    {}
func (*GetCatalogTopicInRcmdFeedResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{28}
}
func (m *GetCatalogTopicInRcmdFeedResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCatalogTopicInRcmdFeedResp.Unmarshal(m, b)
}
func (m *GetCatalogTopicInRcmdFeedResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCatalogTopicInRcmdFeedResp.Marshal(b, m, deterministic)
}
func (dst *GetCatalogTopicInRcmdFeedResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCatalogTopicInRcmdFeedResp.Merge(dst, src)
}
func (m *GetCatalogTopicInRcmdFeedResp) XXX_Size() int {
	return xxx_messageInfo_GetCatalogTopicInRcmdFeedResp.Size(m)
}
func (m *GetCatalogTopicInRcmdFeedResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCatalogTopicInRcmdFeedResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCatalogTopicInRcmdFeedResp proto.InternalMessageInfo

func (m *GetCatalogTopicInRcmdFeedResp) GetCatalogs() []*CatalogTopicInRcmdFeed {
	if m != nil {
		return m.Catalogs
	}
	return nil
}

func (m *GetCatalogTopicInRcmdFeedResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

// 获取推荐流插入话题
type GetTopicsInRecommendReq struct {
	IndexMax             uint32   `protobuf:"varint,1,opt,name=index_max,json=indexMax,proto3" json:"index_max,omitempty"`
	VersionType          uint32   `protobuf:"varint,2,opt,name=version_type,json=versionType,proto3" json:"version_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTopicsInRecommendReq) Reset()         { *m = GetTopicsInRecommendReq{} }
func (m *GetTopicsInRecommendReq) String() string { return proto.CompactTextString(m) }
func (*GetTopicsInRecommendReq) ProtoMessage()    {}
func (*GetTopicsInRecommendReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{29}
}
func (m *GetTopicsInRecommendReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicsInRecommendReq.Unmarshal(m, b)
}
func (m *GetTopicsInRecommendReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicsInRecommendReq.Marshal(b, m, deterministic)
}
func (dst *GetTopicsInRecommendReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicsInRecommendReq.Merge(dst, src)
}
func (m *GetTopicsInRecommendReq) XXX_Size() int {
	return xxx_messageInfo_GetTopicsInRecommendReq.Size(m)
}
func (m *GetTopicsInRecommendReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicsInRecommendReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicsInRecommendReq proto.InternalMessageInfo

func (m *GetTopicsInRecommendReq) GetIndexMax() uint32 {
	if m != nil {
		return m.IndexMax
	}
	return 0
}

func (m *GetTopicsInRecommendReq) GetVersionType() uint32 {
	if m != nil {
		return m.VersionType
	}
	return 0
}

type IndexTopicConfs struct {
	Info                 []*TopicRecommendInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *IndexTopicConfs) Reset()         { *m = IndexTopicConfs{} }
func (m *IndexTopicConfs) String() string { return proto.CompactTextString(m) }
func (*IndexTopicConfs) ProtoMessage()    {}
func (*IndexTopicConfs) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{30}
}
func (m *IndexTopicConfs) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IndexTopicConfs.Unmarshal(m, b)
}
func (m *IndexTopicConfs) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IndexTopicConfs.Marshal(b, m, deterministic)
}
func (dst *IndexTopicConfs) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndexTopicConfs.Merge(dst, src)
}
func (m *IndexTopicConfs) XXX_Size() int {
	return xxx_messageInfo_IndexTopicConfs.Size(m)
}
func (m *IndexTopicConfs) XXX_DiscardUnknown() {
	xxx_messageInfo_IndexTopicConfs.DiscardUnknown(m)
}

var xxx_messageInfo_IndexTopicConfs proto.InternalMessageInfo

func (m *IndexTopicConfs) GetInfo() []*TopicRecommendInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type GetTopicsInRecommendResp struct {
	ConfMap              map[uint32]*IndexTopicConfs `protobuf:"bytes,1,rep,name=conf_map,json=confMap,proto3" json:"conf_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	WeightList           []*TopicInfo                `protobuf:"bytes,2,rep,name=weight_list,json=weightList,proto3" json:"weight_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetTopicsInRecommendResp) Reset()         { *m = GetTopicsInRecommendResp{} }
func (m *GetTopicsInRecommendResp) String() string { return proto.CompactTextString(m) }
func (*GetTopicsInRecommendResp) ProtoMessage()    {}
func (*GetTopicsInRecommendResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{31}
}
func (m *GetTopicsInRecommendResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicsInRecommendResp.Unmarshal(m, b)
}
func (m *GetTopicsInRecommendResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicsInRecommendResp.Marshal(b, m, deterministic)
}
func (dst *GetTopicsInRecommendResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicsInRecommendResp.Merge(dst, src)
}
func (m *GetTopicsInRecommendResp) XXX_Size() int {
	return xxx_messageInfo_GetTopicsInRecommendResp.Size(m)
}
func (m *GetTopicsInRecommendResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicsInRecommendResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicsInRecommendResp proto.InternalMessageInfo

func (m *GetTopicsInRecommendResp) GetConfMap() map[uint32]*IndexTopicConfs {
	if m != nil {
		return m.ConfMap
	}
	return nil
}

func (m *GetTopicsInRecommendResp) GetWeightList() []*TopicInfo {
	if m != nil {
		return m.WeightList
	}
	return nil
}

// 获取24小时 3天 7天内 动态量最多的话题
type GetTopicRankReq struct {
	Ndays                uint32   `protobuf:"varint,1,opt,name=ndays,proto3" json:"ndays,omitempty"`
	Cnt                  uint32   `protobuf:"varint,2,opt,name=cnt,proto3" json:"cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTopicRankReq) Reset()         { *m = GetTopicRankReq{} }
func (m *GetTopicRankReq) String() string { return proto.CompactTextString(m) }
func (*GetTopicRankReq) ProtoMessage()    {}
func (*GetTopicRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{32}
}
func (m *GetTopicRankReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicRankReq.Unmarshal(m, b)
}
func (m *GetTopicRankReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicRankReq.Marshal(b, m, deterministic)
}
func (dst *GetTopicRankReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicRankReq.Merge(dst, src)
}
func (m *GetTopicRankReq) XXX_Size() int {
	return xxx_messageInfo_GetTopicRankReq.Size(m)
}
func (m *GetTopicRankReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicRankReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicRankReq proto.InternalMessageInfo

func (m *GetTopicRankReq) GetNdays() uint32 {
	if m != nil {
		return m.Ndays
	}
	return 0
}

func (m *GetTopicRankReq) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

type GetTopicRankResp struct {
	TopicIdList          []string `protobuf:"bytes,1,rep,name=topic_id_list,json=topicIdList,proto3" json:"topic_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTopicRankResp) Reset()         { *m = GetTopicRankResp{} }
func (m *GetTopicRankResp) String() string { return proto.CompactTextString(m) }
func (*GetTopicRankResp) ProtoMessage()    {}
func (*GetTopicRankResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{33}
}
func (m *GetTopicRankResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicRankResp.Unmarshal(m, b)
}
func (m *GetTopicRankResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicRankResp.Marshal(b, m, deterministic)
}
func (dst *GetTopicRankResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicRankResp.Merge(dst, src)
}
func (m *GetTopicRankResp) XXX_Size() int {
	return xxx_messageInfo_GetTopicRankResp.Size(m)
}
func (m *GetTopicRankResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicRankResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicRankResp proto.InternalMessageInfo

func (m *GetTopicRankResp) GetTopicIdList() []string {
	if m != nil {
		return m.TopicIdList
	}
	return nil
}

// 获取从自定义转官方近3天动态数多的话题
type GetTopicByConvert2OfficialReq struct {
	Cnt                  uint32   `protobuf:"varint,1,opt,name=cnt,proto3" json:"cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTopicByConvert2OfficialReq) Reset()         { *m = GetTopicByConvert2OfficialReq{} }
func (m *GetTopicByConvert2OfficialReq) String() string { return proto.CompactTextString(m) }
func (*GetTopicByConvert2OfficialReq) ProtoMessage()    {}
func (*GetTopicByConvert2OfficialReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{34}
}
func (m *GetTopicByConvert2OfficialReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicByConvert2OfficialReq.Unmarshal(m, b)
}
func (m *GetTopicByConvert2OfficialReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicByConvert2OfficialReq.Marshal(b, m, deterministic)
}
func (dst *GetTopicByConvert2OfficialReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicByConvert2OfficialReq.Merge(dst, src)
}
func (m *GetTopicByConvert2OfficialReq) XXX_Size() int {
	return xxx_messageInfo_GetTopicByConvert2OfficialReq.Size(m)
}
func (m *GetTopicByConvert2OfficialReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicByConvert2OfficialReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicByConvert2OfficialReq proto.InternalMessageInfo

func (m *GetTopicByConvert2OfficialReq) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

type GetTopicByConvert2OfficialResp struct {
	TopicIdList          []string `protobuf:"bytes,1,rep,name=topic_id_list,json=topicIdList,proto3" json:"topic_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTopicByConvert2OfficialResp) Reset()         { *m = GetTopicByConvert2OfficialResp{} }
func (m *GetTopicByConvert2OfficialResp) String() string { return proto.CompactTextString(m) }
func (*GetTopicByConvert2OfficialResp) ProtoMessage()    {}
func (*GetTopicByConvert2OfficialResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{35}
}
func (m *GetTopicByConvert2OfficialResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicByConvert2OfficialResp.Unmarshal(m, b)
}
func (m *GetTopicByConvert2OfficialResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicByConvert2OfficialResp.Marshal(b, m, deterministic)
}
func (dst *GetTopicByConvert2OfficialResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicByConvert2OfficialResp.Merge(dst, src)
}
func (m *GetTopicByConvert2OfficialResp) XXX_Size() int {
	return xxx_messageInfo_GetTopicByConvert2OfficialResp.Size(m)
}
func (m *GetTopicByConvert2OfficialResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicByConvert2OfficialResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicByConvert2OfficialResp proto.InternalMessageInfo

func (m *GetTopicByConvert2OfficialResp) GetTopicIdList() []string {
	if m != nil {
		return m.TopicIdList
	}
	return nil
}

// 记录每个话题发动态的用户uid
type AddUserIDByTopicReq struct {
	TopicId              []string `protobuf:"bytes,1,rep,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	IsDelete             bool     `protobuf:"varint,3,opt,name=is_delete,json=isDelete,proto3" json:"is_delete,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUserIDByTopicReq) Reset()         { *m = AddUserIDByTopicReq{} }
func (m *AddUserIDByTopicReq) String() string { return proto.CompactTextString(m) }
func (*AddUserIDByTopicReq) ProtoMessage()    {}
func (*AddUserIDByTopicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{36}
}
func (m *AddUserIDByTopicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserIDByTopicReq.Unmarshal(m, b)
}
func (m *AddUserIDByTopicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserIDByTopicReq.Marshal(b, m, deterministic)
}
func (dst *AddUserIDByTopicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserIDByTopicReq.Merge(dst, src)
}
func (m *AddUserIDByTopicReq) XXX_Size() int {
	return xxx_messageInfo_AddUserIDByTopicReq.Size(m)
}
func (m *AddUserIDByTopicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserIDByTopicReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserIDByTopicReq proto.InternalMessageInfo

func (m *AddUserIDByTopicReq) GetTopicId() []string {
	if m != nil {
		return m.TopicId
	}
	return nil
}

func (m *AddUserIDByTopicReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddUserIDByTopicReq) GetIsDelete() bool {
	if m != nil {
		return m.IsDelete
	}
	return false
}

type AddUserIDByTopicResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUserIDByTopicResp) Reset()         { *m = AddUserIDByTopicResp{} }
func (m *AddUserIDByTopicResp) String() string { return proto.CompactTextString(m) }
func (*AddUserIDByTopicResp) ProtoMessage()    {}
func (*AddUserIDByTopicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{37}
}
func (m *AddUserIDByTopicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserIDByTopicResp.Unmarshal(m, b)
}
func (m *AddUserIDByTopicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserIDByTopicResp.Marshal(b, m, deterministic)
}
func (dst *AddUserIDByTopicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserIDByTopicResp.Merge(dst, src)
}
func (m *AddUserIDByTopicResp) XXX_Size() int {
	return xxx_messageInfo_AddUserIDByTopicResp.Size(m)
}
func (m *AddUserIDByTopicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserIDByTopicResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserIDByTopicResp proto.InternalMessageInfo

// 获取话题发动态的用户uid列表
type GetUserIDByTopicReq struct {
	TopicId              string   `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	Cnt                  uint32   `protobuf:"varint,2,opt,name=cnt,proto3" json:"cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserIDByTopicReq) Reset()         { *m = GetUserIDByTopicReq{} }
func (m *GetUserIDByTopicReq) String() string { return proto.CompactTextString(m) }
func (*GetUserIDByTopicReq) ProtoMessage()    {}
func (*GetUserIDByTopicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{38}
}
func (m *GetUserIDByTopicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserIDByTopicReq.Unmarshal(m, b)
}
func (m *GetUserIDByTopicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserIDByTopicReq.Marshal(b, m, deterministic)
}
func (dst *GetUserIDByTopicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserIDByTopicReq.Merge(dst, src)
}
func (m *GetUserIDByTopicReq) XXX_Size() int {
	return xxx_messageInfo_GetUserIDByTopicReq.Size(m)
}
func (m *GetUserIDByTopicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserIDByTopicReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserIDByTopicReq proto.InternalMessageInfo

func (m *GetUserIDByTopicReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *GetUserIDByTopicReq) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

type GetUserIDByTopicResp struct {
	UidList              []string `protobuf:"bytes,1,rep,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserIDByTopicResp) Reset()         { *m = GetUserIDByTopicResp{} }
func (m *GetUserIDByTopicResp) String() string { return proto.CompactTextString(m) }
func (*GetUserIDByTopicResp) ProtoMessage()    {}
func (*GetUserIDByTopicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{39}
}
func (m *GetUserIDByTopicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserIDByTopicResp.Unmarshal(m, b)
}
func (m *GetUserIDByTopicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserIDByTopicResp.Marshal(b, m, deterministic)
}
func (dst *GetUserIDByTopicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserIDByTopicResp.Merge(dst, src)
}
func (m *GetUserIDByTopicResp) XXX_Size() int {
	return xxx_messageInfo_GetUserIDByTopicResp.Size(m)
}
func (m *GetUserIDByTopicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserIDByTopicResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserIDByTopicResp proto.InternalMessageInfo

func (m *GetUserIDByTopicResp) GetUidList() []string {
	if m != nil {
		return m.UidList
	}
	return nil
}

type GetTopicInfoReq struct {
	TopicId string           `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	Option  TopicQueryOption `protobuf:"varint,2,opt,name=option,proto3,enum=ugc.topic.TopicQueryOption" json:"option,omitempty"`
	Name    string           `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 传入uid
	Uid                  uint32    `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	TopicType            TopicType `protobuf:"varint,5,opt,name=topic_type,json=topicType,proto3,enum=ugc.topic.TopicType" json:"topic_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetTopicInfoReq) Reset()         { *m = GetTopicInfoReq{} }
func (m *GetTopicInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetTopicInfoReq) ProtoMessage()    {}
func (*GetTopicInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{40}
}
func (m *GetTopicInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicInfoReq.Unmarshal(m, b)
}
func (m *GetTopicInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetTopicInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicInfoReq.Merge(dst, src)
}
func (m *GetTopicInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetTopicInfoReq.Size(m)
}
func (m *GetTopicInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicInfoReq proto.InternalMessageInfo

func (m *GetTopicInfoReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *GetTopicInfoReq) GetOption() TopicQueryOption {
	if m != nil {
		return m.Option
	}
	return TopicQueryOption_QueryAll
}

func (m *GetTopicInfoReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetTopicInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetTopicInfoReq) GetTopicType() TopicType {
	if m != nil {
		return m.TopicType
	}
	return TopicType_TypeCollection
}

type GetTopicInfoResp struct {
	Info                 *TopicInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetTopicInfoResp) Reset()         { *m = GetTopicInfoResp{} }
func (m *GetTopicInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetTopicInfoResp) ProtoMessage()    {}
func (*GetTopicInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{41}
}
func (m *GetTopicInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicInfoResp.Unmarshal(m, b)
}
func (m *GetTopicInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetTopicInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicInfoResp.Merge(dst, src)
}
func (m *GetTopicInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetTopicInfoResp.Size(m)
}
func (m *GetTopicInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicInfoResp proto.InternalMessageInfo

func (m *GetTopicInfoResp) GetInfo() *TopicInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type GetTopicListReq struct {
	Option               TopicQueryOption `protobuf:"varint,1,opt,name=option,proto3,enum=ugc.topic.TopicQueryOption" json:"option,omitempty"` // Deprecated: Do not use.
	Offset               uint32           `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32           `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	TopicType            TopicType        `protobuf:"varint,4,opt,name=topic_type,json=topicType,proto3,enum=ugc.topic.TopicType" json:"topic_type,omitempty"`
	QueryParentInfo      bool             `protobuf:"varint,5,opt,name=query_parent_info,json=queryParentInfo,proto3" json:"query_parent_info,omitempty"` // Deprecated: Do not use.
	ParentTopicId        string           `protobuf:"bytes,6,opt,name=parent_topic_id,json=parentTopicId,proto3" json:"parent_topic_id,omitempty"`
	Uid                  uint32           `protobuf:"varint,7,opt,name=uid,proto3" json:"uid,omitempty"`
	WeightStatus         uint32           `protobuf:"varint,8,opt,name=weight_status,json=weightStatus,proto3" json:"weight_status,omitempty"`
	Weight               string           `protobuf:"bytes,9,opt,name=weight,proto3" json:"weight,omitempty"`
	Ndays                uint32           `protobuf:"varint,10,opt,name=ndays,proto3" json:"ndays,omitempty"`
	Name                 string           `protobuf:"bytes,11,opt,name=name,proto3" json:"name,omitempty"`
	TopicId              string           `protobuf:"bytes,12,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	GameName             string           `protobuf:"bytes,13,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetTopicListReq) Reset()         { *m = GetTopicListReq{} }
func (m *GetTopicListReq) String() string { return proto.CompactTextString(m) }
func (*GetTopicListReq) ProtoMessage()    {}
func (*GetTopicListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{42}
}
func (m *GetTopicListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicListReq.Unmarshal(m, b)
}
func (m *GetTopicListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicListReq.Marshal(b, m, deterministic)
}
func (dst *GetTopicListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicListReq.Merge(dst, src)
}
func (m *GetTopicListReq) XXX_Size() int {
	return xxx_messageInfo_GetTopicListReq.Size(m)
}
func (m *GetTopicListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicListReq proto.InternalMessageInfo

// Deprecated: Do not use.
func (m *GetTopicListReq) GetOption() TopicQueryOption {
	if m != nil {
		return m.Option
	}
	return TopicQueryOption_QueryAll
}

func (m *GetTopicListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetTopicListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetTopicListReq) GetTopicType() TopicType {
	if m != nil {
		return m.TopicType
	}
	return TopicType_TypeCollection
}

// Deprecated: Do not use.
func (m *GetTopicListReq) GetQueryParentInfo() bool {
	if m != nil {
		return m.QueryParentInfo
	}
	return false
}

func (m *GetTopicListReq) GetParentTopicId() string {
	if m != nil {
		return m.ParentTopicId
	}
	return ""
}

func (m *GetTopicListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetTopicListReq) GetWeightStatus() uint32 {
	if m != nil {
		return m.WeightStatus
	}
	return 0
}

func (m *GetTopicListReq) GetWeight() string {
	if m != nil {
		return m.Weight
	}
	return ""
}

func (m *GetTopicListReq) GetNdays() uint32 {
	if m != nil {
		return m.Ndays
	}
	return 0
}

func (m *GetTopicListReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetTopicListReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *GetTopicListReq) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

type GeTopicListRelationByTopicReq struct {
	TopicId              string           `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	Offset               uint32           `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32           `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	Option               TopicQueryOption `protobuf:"varint,4,opt,name=option,proto3,enum=ugc.topic.TopicQueryOption" json:"option,omitempty"`
	QueryParentTopic     bool             `protobuf:"varint,5,opt,name=query_parent_topic,json=queryParentTopic,proto3" json:"query_parent_topic,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GeTopicListRelationByTopicReq) Reset()         { *m = GeTopicListRelationByTopicReq{} }
func (m *GeTopicListRelationByTopicReq) String() string { return proto.CompactTextString(m) }
func (*GeTopicListRelationByTopicReq) ProtoMessage()    {}
func (*GeTopicListRelationByTopicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{43}
}
func (m *GeTopicListRelationByTopicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GeTopicListRelationByTopicReq.Unmarshal(m, b)
}
func (m *GeTopicListRelationByTopicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GeTopicListRelationByTopicReq.Marshal(b, m, deterministic)
}
func (dst *GeTopicListRelationByTopicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GeTopicListRelationByTopicReq.Merge(dst, src)
}
func (m *GeTopicListRelationByTopicReq) XXX_Size() int {
	return xxx_messageInfo_GeTopicListRelationByTopicReq.Size(m)
}
func (m *GeTopicListRelationByTopicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GeTopicListRelationByTopicReq.DiscardUnknown(m)
}

var xxx_messageInfo_GeTopicListRelationByTopicReq proto.InternalMessageInfo

func (m *GeTopicListRelationByTopicReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *GeTopicListRelationByTopicReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GeTopicListRelationByTopicReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GeTopicListRelationByTopicReq) GetOption() TopicQueryOption {
	if m != nil {
		return m.Option
	}
	return TopicQueryOption_QueryAll
}

func (m *GeTopicListRelationByTopicReq) GetQueryParentTopic() bool {
	if m != nil {
		return m.QueryParentTopic
	}
	return false
}

type GetTopicListResp struct {
	TopicInfos           []*TopicInfo `protobuf:"bytes,1,rep,name=topicInfos,proto3" json:"topicInfos,omitempty"`
	Total                uint32       `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetTopicListResp) Reset()         { *m = GetTopicListResp{} }
func (m *GetTopicListResp) String() string { return proto.CompactTextString(m) }
func (*GetTopicListResp) ProtoMessage()    {}
func (*GetTopicListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{44}
}
func (m *GetTopicListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicListResp.Unmarshal(m, b)
}
func (m *GetTopicListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicListResp.Marshal(b, m, deterministic)
}
func (dst *GetTopicListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicListResp.Merge(dst, src)
}
func (m *GetTopicListResp) XXX_Size() int {
	return xxx_messageInfo_GetTopicListResp.Size(m)
}
func (m *GetTopicListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicListResp proto.InternalMessageInfo

func (m *GetTopicListResp) GetTopicInfos() []*TopicInfo {
	if m != nil {
		return m.TopicInfos
	}
	return nil
}

func (m *GetTopicListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type SortTopicReq struct {
	Sort                 map[string]string `protobuf:"bytes,1,rep,name=sort,proto3" json:"sort,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SortTopicReq) Reset()         { *m = SortTopicReq{} }
func (m *SortTopicReq) String() string { return proto.CompactTextString(m) }
func (*SortTopicReq) ProtoMessage()    {}
func (*SortTopicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{45}
}
func (m *SortTopicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SortTopicReq.Unmarshal(m, b)
}
func (m *SortTopicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SortTopicReq.Marshal(b, m, deterministic)
}
func (dst *SortTopicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SortTopicReq.Merge(dst, src)
}
func (m *SortTopicReq) XXX_Size() int {
	return xxx_messageInfo_SortTopicReq.Size(m)
}
func (m *SortTopicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SortTopicReq.DiscardUnknown(m)
}

var xxx_messageInfo_SortTopicReq proto.InternalMessageInfo

func (m *SortTopicReq) GetSort() map[string]string {
	if m != nil {
		return m.Sort
	}
	return nil
}

type SortTopicResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SortTopicResp) Reset()         { *m = SortTopicResp{} }
func (m *SortTopicResp) String() string { return proto.CompactTextString(m) }
func (*SortTopicResp) ProtoMessage()    {}
func (*SortTopicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{46}
}
func (m *SortTopicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SortTopicResp.Unmarshal(m, b)
}
func (m *SortTopicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SortTopicResp.Marshal(b, m, deterministic)
}
func (dst *SortTopicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SortTopicResp.Merge(dst, src)
}
func (m *SortTopicResp) XXX_Size() int {
	return xxx_messageInfo_SortTopicResp.Size(m)
}
func (m *SortTopicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SortTopicResp.DiscardUnknown(m)
}

var xxx_messageInfo_SortTopicResp proto.InternalMessageInfo

type DeleteTopicReq struct {
	TopicId              string   `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteTopicReq) Reset()         { *m = DeleteTopicReq{} }
func (m *DeleteTopicReq) String() string { return proto.CompactTextString(m) }
func (*DeleteTopicReq) ProtoMessage()    {}
func (*DeleteTopicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{47}
}
func (m *DeleteTopicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteTopicReq.Unmarshal(m, b)
}
func (m *DeleteTopicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteTopicReq.Marshal(b, m, deterministic)
}
func (dst *DeleteTopicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteTopicReq.Merge(dst, src)
}
func (m *DeleteTopicReq) XXX_Size() int {
	return xxx_messageInfo_DeleteTopicReq.Size(m)
}
func (m *DeleteTopicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteTopicReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteTopicReq proto.InternalMessageInfo

func (m *DeleteTopicReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

type DeleteTopicResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteTopicResp) Reset()         { *m = DeleteTopicResp{} }
func (m *DeleteTopicResp) String() string { return proto.CompactTextString(m) }
func (*DeleteTopicResp) ProtoMessage()    {}
func (*DeleteTopicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{48}
}
func (m *DeleteTopicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteTopicResp.Unmarshal(m, b)
}
func (m *DeleteTopicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteTopicResp.Marshal(b, m, deterministic)
}
func (dst *DeleteTopicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteTopicResp.Merge(dst, src)
}
func (m *DeleteTopicResp) XXX_Size() int {
	return xxx_messageInfo_DeleteTopicResp.Size(m)
}
func (m *DeleteTopicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteTopicResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteTopicResp proto.InternalMessageInfo

// 批量删除
type BatDeleteTopicReq struct {
	TopicIdList          []string `protobuf:"bytes,1,rep,name=topic_id_list,json=topicIdList,proto3" json:"topic_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatDeleteTopicReq) Reset()         { *m = BatDeleteTopicReq{} }
func (m *BatDeleteTopicReq) String() string { return proto.CompactTextString(m) }
func (*BatDeleteTopicReq) ProtoMessage()    {}
func (*BatDeleteTopicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{49}
}
func (m *BatDeleteTopicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatDeleteTopicReq.Unmarshal(m, b)
}
func (m *BatDeleteTopicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatDeleteTopicReq.Marshal(b, m, deterministic)
}
func (dst *BatDeleteTopicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatDeleteTopicReq.Merge(dst, src)
}
func (m *BatDeleteTopicReq) XXX_Size() int {
	return xxx_messageInfo_BatDeleteTopicReq.Size(m)
}
func (m *BatDeleteTopicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatDeleteTopicReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatDeleteTopicReq proto.InternalMessageInfo

func (m *BatDeleteTopicReq) GetTopicIdList() []string {
	if m != nil {
		return m.TopicIdList
	}
	return nil
}

// message BatDelResult{
//    string topic_id = 1;
//    bool is_deleted = 2;
// }
type BatDeleteTopicResp struct {
	DeletedCnt           uint32   `protobuf:"varint,1,opt,name=deleted_cnt,json=deletedCnt,proto3" json:"deleted_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatDeleteTopicResp) Reset()         { *m = BatDeleteTopicResp{} }
func (m *BatDeleteTopicResp) String() string { return proto.CompactTextString(m) }
func (*BatDeleteTopicResp) ProtoMessage()    {}
func (*BatDeleteTopicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{50}
}
func (m *BatDeleteTopicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatDeleteTopicResp.Unmarshal(m, b)
}
func (m *BatDeleteTopicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatDeleteTopicResp.Marshal(b, m, deterministic)
}
func (dst *BatDeleteTopicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatDeleteTopicResp.Merge(dst, src)
}
func (m *BatDeleteTopicResp) XXX_Size() int {
	return xxx_messageInfo_BatDeleteTopicResp.Size(m)
}
func (m *BatDeleteTopicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatDeleteTopicResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatDeleteTopicResp proto.InternalMessageInfo

func (m *BatDeleteTopicResp) GetDeletedCnt() uint32 {
	if m != nil {
		return m.DeletedCnt
	}
	return 0
}

type SubscribeTopicReq struct {
	Uid                  uint32    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TopicId              string    `protobuf:"bytes,2,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	TopicType            TopicType `protobuf:"varint,3,opt,name=topic_type,json=topicType,proto3,enum=ugc.topic.TopicType" json:"topic_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *SubscribeTopicReq) Reset()         { *m = SubscribeTopicReq{} }
func (m *SubscribeTopicReq) String() string { return proto.CompactTextString(m) }
func (*SubscribeTopicReq) ProtoMessage()    {}
func (*SubscribeTopicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{51}
}
func (m *SubscribeTopicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubscribeTopicReq.Unmarshal(m, b)
}
func (m *SubscribeTopicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubscribeTopicReq.Marshal(b, m, deterministic)
}
func (dst *SubscribeTopicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubscribeTopicReq.Merge(dst, src)
}
func (m *SubscribeTopicReq) XXX_Size() int {
	return xxx_messageInfo_SubscribeTopicReq.Size(m)
}
func (m *SubscribeTopicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SubscribeTopicReq.DiscardUnknown(m)
}

var xxx_messageInfo_SubscribeTopicReq proto.InternalMessageInfo

func (m *SubscribeTopicReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SubscribeTopicReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *SubscribeTopicReq) GetTopicType() TopicType {
	if m != nil {
		return m.TopicType
	}
	return TopicType_TypeCollection
}

type SubscribeTopicResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubscribeTopicResp) Reset()         { *m = SubscribeTopicResp{} }
func (m *SubscribeTopicResp) String() string { return proto.CompactTextString(m) }
func (*SubscribeTopicResp) ProtoMessage()    {}
func (*SubscribeTopicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{52}
}
func (m *SubscribeTopicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubscribeTopicResp.Unmarshal(m, b)
}
func (m *SubscribeTopicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubscribeTopicResp.Marshal(b, m, deterministic)
}
func (dst *SubscribeTopicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubscribeTopicResp.Merge(dst, src)
}
func (m *SubscribeTopicResp) XXX_Size() int {
	return xxx_messageInfo_SubscribeTopicResp.Size(m)
}
func (m *SubscribeTopicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SubscribeTopicResp.DiscardUnknown(m)
}

var xxx_messageInfo_SubscribeTopicResp proto.InternalMessageInfo

type UnsubscribeTopicReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TopicId              string   `protobuf:"bytes,2,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnsubscribeTopicReq) Reset()         { *m = UnsubscribeTopicReq{} }
func (m *UnsubscribeTopicReq) String() string { return proto.CompactTextString(m) }
func (*UnsubscribeTopicReq) ProtoMessage()    {}
func (*UnsubscribeTopicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{53}
}
func (m *UnsubscribeTopicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnsubscribeTopicReq.Unmarshal(m, b)
}
func (m *UnsubscribeTopicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnsubscribeTopicReq.Marshal(b, m, deterministic)
}
func (dst *UnsubscribeTopicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnsubscribeTopicReq.Merge(dst, src)
}
func (m *UnsubscribeTopicReq) XXX_Size() int {
	return xxx_messageInfo_UnsubscribeTopicReq.Size(m)
}
func (m *UnsubscribeTopicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UnsubscribeTopicReq.DiscardUnknown(m)
}

var xxx_messageInfo_UnsubscribeTopicReq proto.InternalMessageInfo

func (m *UnsubscribeTopicReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UnsubscribeTopicReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

type UnsubscribeTopicResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnsubscribeTopicResp) Reset()         { *m = UnsubscribeTopicResp{} }
func (m *UnsubscribeTopicResp) String() string { return proto.CompactTextString(m) }
func (*UnsubscribeTopicResp) ProtoMessage()    {}
func (*UnsubscribeTopicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{54}
}
func (m *UnsubscribeTopicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnsubscribeTopicResp.Unmarshal(m, b)
}
func (m *UnsubscribeTopicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnsubscribeTopicResp.Marshal(b, m, deterministic)
}
func (dst *UnsubscribeTopicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnsubscribeTopicResp.Merge(dst, src)
}
func (m *UnsubscribeTopicResp) XXX_Size() int {
	return xxx_messageInfo_UnsubscribeTopicResp.Size(m)
}
func (m *UnsubscribeTopicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UnsubscribeTopicResp.DiscardUnknown(m)
}

var xxx_messageInfo_UnsubscribeTopicResp proto.InternalMessageInfo

type GetSubscribersReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSubscribersReq) Reset()         { *m = GetSubscribersReq{} }
func (m *GetSubscribersReq) String() string { return proto.CompactTextString(m) }
func (*GetSubscribersReq) ProtoMessage()    {}
func (*GetSubscribersReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{55}
}
func (m *GetSubscribersReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSubscribersReq.Unmarshal(m, b)
}
func (m *GetSubscribersReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSubscribersReq.Marshal(b, m, deterministic)
}
func (dst *GetSubscribersReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSubscribersReq.Merge(dst, src)
}
func (m *GetSubscribersReq) XXX_Size() int {
	return xxx_messageInfo_GetSubscribersReq.Size(m)
}
func (m *GetSubscribersReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSubscribersReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSubscribersReq proto.InternalMessageInfo

type GetSubscribersResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSubscribersResp) Reset()         { *m = GetSubscribersResp{} }
func (m *GetSubscribersResp) String() string { return proto.CompactTextString(m) }
func (*GetSubscribersResp) ProtoMessage()    {}
func (*GetSubscribersResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{56}
}
func (m *GetSubscribersResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSubscribersResp.Unmarshal(m, b)
}
func (m *GetSubscribersResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSubscribersResp.Marshal(b, m, deterministic)
}
func (dst *GetSubscribersResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSubscribersResp.Merge(dst, src)
}
func (m *GetSubscribersResp) XXX_Size() int {
	return xxx_messageInfo_GetSubscribersResp.Size(m)
}
func (m *GetSubscribersResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSubscribersResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSubscribersResp proto.InternalMessageInfo

type GetTopicsReq struct {
	TopicIds             []string           `protobuf:"bytes,1,rep,name=topic_ids,json=topicIds,proto3" json:"topic_ids,omitempty"`
	Option               TopicQueryOptionEx `protobuf:"varint,2,opt,name=option,proto3,enum=ugc.topic.TopicQueryOptionEx" json:"option,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetTopicsReq) Reset()         { *m = GetTopicsReq{} }
func (m *GetTopicsReq) String() string { return proto.CompactTextString(m) }
func (*GetTopicsReq) ProtoMessage()    {}
func (*GetTopicsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{57}
}
func (m *GetTopicsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicsReq.Unmarshal(m, b)
}
func (m *GetTopicsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicsReq.Marshal(b, m, deterministic)
}
func (dst *GetTopicsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicsReq.Merge(dst, src)
}
func (m *GetTopicsReq) XXX_Size() int {
	return xxx_messageInfo_GetTopicsReq.Size(m)
}
func (m *GetTopicsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicsReq proto.InternalMessageInfo

func (m *GetTopicsReq) GetTopicIds() []string {
	if m != nil {
		return m.TopicIds
	}
	return nil
}

func (m *GetTopicsReq) GetOption() TopicQueryOptionEx {
	if m != nil {
		return m.Option
	}
	return TopicQueryOptionEx_ExQueryEnable
}

type GetTopicsResp struct {
	TopicInfos           []*TopicInfo `protobuf:"bytes,1,rep,name=topicInfos,proto3" json:"topicInfos,omitempty"`
	Total                uint32       `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetTopicsResp) Reset()         { *m = GetTopicsResp{} }
func (m *GetTopicsResp) String() string { return proto.CompactTextString(m) }
func (*GetTopicsResp) ProtoMessage()    {}
func (*GetTopicsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{58}
}
func (m *GetTopicsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicsResp.Unmarshal(m, b)
}
func (m *GetTopicsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicsResp.Marshal(b, m, deterministic)
}
func (dst *GetTopicsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicsResp.Merge(dst, src)
}
func (m *GetTopicsResp) XXX_Size() int {
	return xxx_messageInfo_GetTopicsResp.Size(m)
}
func (m *GetTopicsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicsResp proto.InternalMessageInfo

func (m *GetTopicsResp) GetTopicInfos() []*TopicInfo {
	if m != nil {
		return m.TopicInfos
	}
	return nil
}

func (m *GetTopicsResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type TopicWeightInfo struct {
	HasWeight            bool     `protobuf:"varint,1,opt,name=has_weight,json=hasWeight,proto3" json:"has_weight,omitempty"`
	Weight               string   `protobuf:"bytes,2,opt,name=weight,proto3" json:"weight,omitempty"`
	WeightStartTs        uint32   `protobuf:"varint,3,opt,name=weight_start_ts,json=weightStartTs,proto3" json:"weight_start_ts,omitempty"`
	WeightEndTs          uint32   `protobuf:"varint,4,opt,name=weight_end_ts,json=weightEndTs,proto3" json:"weight_end_ts,omitempty"`
	WeightStatus         uint32   `protobuf:"varint,5,opt,name=weight_status,json=weightStatus,proto3" json:"weight_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TopicWeightInfo) Reset()         { *m = TopicWeightInfo{} }
func (m *TopicWeightInfo) String() string { return proto.CompactTextString(m) }
func (*TopicWeightInfo) ProtoMessage()    {}
func (*TopicWeightInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{59}
}
func (m *TopicWeightInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicWeightInfo.Unmarshal(m, b)
}
func (m *TopicWeightInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicWeightInfo.Marshal(b, m, deterministic)
}
func (dst *TopicWeightInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicWeightInfo.Merge(dst, src)
}
func (m *TopicWeightInfo) XXX_Size() int {
	return xxx_messageInfo_TopicWeightInfo.Size(m)
}
func (m *TopicWeightInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicWeightInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TopicWeightInfo proto.InternalMessageInfo

func (m *TopicWeightInfo) GetHasWeight() bool {
	if m != nil {
		return m.HasWeight
	}
	return false
}

func (m *TopicWeightInfo) GetWeight() string {
	if m != nil {
		return m.Weight
	}
	return ""
}

func (m *TopicWeightInfo) GetWeightStartTs() uint32 {
	if m != nil {
		return m.WeightStartTs
	}
	return 0
}

func (m *TopicWeightInfo) GetWeightEndTs() uint32 {
	if m != nil {
		return m.WeightEndTs
	}
	return 0
}

func (m *TopicWeightInfo) GetWeightStatus() uint32 {
	if m != nil {
		return m.WeightStatus
	}
	return 0
}

type TopicInfo struct {
	TopicId string `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	Name    string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Desc    string `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	IconUrl string `protobuf:"bytes,4,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	// 查询详细内容则包括一下字段
	MemberCount uint32       `protobuf:"varint,5,opt,name=member_count,json=memberCount,proto3" json:"member_count,omitempty"` // Deprecated: Do not use.
	PostCount   uint32       `protobuf:"varint,6,opt,name=post_count,json=postCount,proto3" json:"post_count,omitempty"`
	UpdateAt    uint32       `protobuf:"varint,7,opt,name=update_at,json=updateAt,proto3" json:"update_at,omitempty"`
	BindUid     uint32       `protobuf:"varint,8,opt,name=bind_uid,json=bindUid,proto3" json:"bind_uid,omitempty"` // Deprecated: Do not use.
	CreateAt    uint32       `protobuf:"varint,9,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	TopicType   TopicType    `protobuf:"varint,10,opt,name=topic_type,json=topicType,proto3,enum=ugc.topic.TopicType" json:"topic_type,omitempty"`
	Order       string       `protobuf:"bytes,11,opt,name=order,proto3" json:"order,omitempty"`
	ParentInfos []*TopicInfo `protobuf:"bytes,12,rep,name=parent_infos,json=parentInfos,proto3" json:"parent_infos,omitempty"` // Deprecated: Do not use.
	// 观看数量
	ViewCount            uint32             `protobuf:"varint,13,opt,name=view_count,json=viewCount,proto3" json:"view_count,omitempty"`
	WeightInfo           *TopicWeightInfo   `protobuf:"bytes,14,opt,name=weight_info,json=weightInfo,proto3" json:"weight_info,omitempty"`
	PostCntInDays        uint32             `protobuf:"varint,15,opt,name=PostCntInDays,proto3" json:"PostCntInDays,omitempty"`
	IsValid              bool               `protobuf:"varint,16,opt,name=is_valid,json=isValid,proto3" json:"is_valid,omitempty"`
	Game                 *TopicBindGameInfo `protobuf:"bytes,17,opt,name=game,proto3" json:"game,omitempty"`
	MoodId               string             `protobuf:"bytes,18,opt,name=mood_id,json=moodId,proto3" json:"mood_id,omitempty"`
	RelatedTopicList     []*RelatedTopic    `protobuf:"bytes,19,rep,name=related_topic_list,json=relatedTopicList,proto3" json:"related_topic_list,omitempty"`
	Enable               bool               `protobuf:"varint,20,opt,name=enable,proto3" json:"enable,omitempty"`
	MultiPicUrlList      []string           `protobuf:"bytes,21,rep,name=multi_pic_url_list,json=multiPicUrlList,proto3" json:"multi_pic_url_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *TopicInfo) Reset()         { *m = TopicInfo{} }
func (m *TopicInfo) String() string { return proto.CompactTextString(m) }
func (*TopicInfo) ProtoMessage()    {}
func (*TopicInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{60}
}
func (m *TopicInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicInfo.Unmarshal(m, b)
}
func (m *TopicInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicInfo.Marshal(b, m, deterministic)
}
func (dst *TopicInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicInfo.Merge(dst, src)
}
func (m *TopicInfo) XXX_Size() int {
	return xxx_messageInfo_TopicInfo.Size(m)
}
func (m *TopicInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TopicInfo proto.InternalMessageInfo

func (m *TopicInfo) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *TopicInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *TopicInfo) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *TopicInfo) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

// Deprecated: Do not use.
func (m *TopicInfo) GetMemberCount() uint32 {
	if m != nil {
		return m.MemberCount
	}
	return 0
}

func (m *TopicInfo) GetPostCount() uint32 {
	if m != nil {
		return m.PostCount
	}
	return 0
}

func (m *TopicInfo) GetUpdateAt() uint32 {
	if m != nil {
		return m.UpdateAt
	}
	return 0
}

// Deprecated: Do not use.
func (m *TopicInfo) GetBindUid() uint32 {
	if m != nil {
		return m.BindUid
	}
	return 0
}

func (m *TopicInfo) GetCreateAt() uint32 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *TopicInfo) GetTopicType() TopicType {
	if m != nil {
		return m.TopicType
	}
	return TopicType_TypeCollection
}

func (m *TopicInfo) GetOrder() string {
	if m != nil {
		return m.Order
	}
	return ""
}

// Deprecated: Do not use.
func (m *TopicInfo) GetParentInfos() []*TopicInfo {
	if m != nil {
		return m.ParentInfos
	}
	return nil
}

func (m *TopicInfo) GetViewCount() uint32 {
	if m != nil {
		return m.ViewCount
	}
	return 0
}

func (m *TopicInfo) GetWeightInfo() *TopicWeightInfo {
	if m != nil {
		return m.WeightInfo
	}
	return nil
}

func (m *TopicInfo) GetPostCntInDays() uint32 {
	if m != nil {
		return m.PostCntInDays
	}
	return 0
}

func (m *TopicInfo) GetIsValid() bool {
	if m != nil {
		return m.IsValid
	}
	return false
}

func (m *TopicInfo) GetGame() *TopicBindGameInfo {
	if m != nil {
		return m.Game
	}
	return nil
}

func (m *TopicInfo) GetMoodId() string {
	if m != nil {
		return m.MoodId
	}
	return ""
}

func (m *TopicInfo) GetRelatedTopicList() []*RelatedTopic {
	if m != nil {
		return m.RelatedTopicList
	}
	return nil
}

func (m *TopicInfo) GetEnable() bool {
	if m != nil {
		return m.Enable
	}
	return false
}

func (m *TopicInfo) GetMultiPicUrlList() []string {
	if m != nil {
		return m.MultiPicUrlList
	}
	return nil
}

type RelatedTopic struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RelatedTopic) Reset()         { *m = RelatedTopic{} }
func (m *RelatedTopic) String() string { return proto.CompactTextString(m) }
func (*RelatedTopic) ProtoMessage()    {}
func (*RelatedTopic) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{61}
}
func (m *RelatedTopic) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RelatedTopic.Unmarshal(m, b)
}
func (m *RelatedTopic) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RelatedTopic.Marshal(b, m, deterministic)
}
func (dst *RelatedTopic) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RelatedTopic.Merge(dst, src)
}
func (m *RelatedTopic) XXX_Size() int {
	return xxx_messageInfo_RelatedTopic.Size(m)
}
func (m *RelatedTopic) XXX_DiscardUnknown() {
	xxx_messageInfo_RelatedTopic.DiscardUnknown(m)
}

var xxx_messageInfo_RelatedTopic proto.InternalMessageInfo

func (m *RelatedTopic) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *RelatedTopic) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type CheckTopicBindUidExistsReq struct {
	BindUid              uint32   `protobuf:"varint,1,opt,name=bind_uid,json=bindUid,proto3" json:"bind_uid,omitempty"`
	TopicId              string   `protobuf:"bytes,2,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckTopicBindUidExistsReq) Reset()         { *m = CheckTopicBindUidExistsReq{} }
func (m *CheckTopicBindUidExistsReq) String() string { return proto.CompactTextString(m) }
func (*CheckTopicBindUidExistsReq) ProtoMessage()    {}
func (*CheckTopicBindUidExistsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{62}
}
func (m *CheckTopicBindUidExistsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckTopicBindUidExistsReq.Unmarshal(m, b)
}
func (m *CheckTopicBindUidExistsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckTopicBindUidExistsReq.Marshal(b, m, deterministic)
}
func (dst *CheckTopicBindUidExistsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckTopicBindUidExistsReq.Merge(dst, src)
}
func (m *CheckTopicBindUidExistsReq) XXX_Size() int {
	return xxx_messageInfo_CheckTopicBindUidExistsReq.Size(m)
}
func (m *CheckTopicBindUidExistsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckTopicBindUidExistsReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckTopicBindUidExistsReq proto.InternalMessageInfo

func (m *CheckTopicBindUidExistsReq) GetBindUid() uint32 {
	if m != nil {
		return m.BindUid
	}
	return 0
}

func (m *CheckTopicBindUidExistsReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

type CheckTopicBindUidExistsResp struct {
	Exists               bool     `protobuf:"varint,1,opt,name=exists,proto3" json:"exists,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckTopicBindUidExistsResp) Reset()         { *m = CheckTopicBindUidExistsResp{} }
func (m *CheckTopicBindUidExistsResp) String() string { return proto.CompactTextString(m) }
func (*CheckTopicBindUidExistsResp) ProtoMessage()    {}
func (*CheckTopicBindUidExistsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{63}
}
func (m *CheckTopicBindUidExistsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckTopicBindUidExistsResp.Unmarshal(m, b)
}
func (m *CheckTopicBindUidExistsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckTopicBindUidExistsResp.Marshal(b, m, deterministic)
}
func (dst *CheckTopicBindUidExistsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckTopicBindUidExistsResp.Merge(dst, src)
}
func (m *CheckTopicBindUidExistsResp) XXX_Size() int {
	return xxx_messageInfo_CheckTopicBindUidExistsResp.Size(m)
}
func (m *CheckTopicBindUidExistsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckTopicBindUidExistsResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckTopicBindUidExistsResp proto.InternalMessageInfo

func (m *CheckTopicBindUidExistsResp) GetExists() bool {
	if m != nil {
		return m.Exists
	}
	return false
}

type LoadMore struct {
	LastPage             uint32   `protobuf:"varint,1,opt,name=last_page,json=lastPage,proto3" json:"last_page,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LoadMore) Reset()         { *m = LoadMore{} }
func (m *LoadMore) String() string { return proto.CompactTextString(m) }
func (*LoadMore) ProtoMessage()    {}
func (*LoadMore) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{64}
}
func (m *LoadMore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LoadMore.Unmarshal(m, b)
}
func (m *LoadMore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LoadMore.Marshal(b, m, deterministic)
}
func (dst *LoadMore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LoadMore.Merge(dst, src)
}
func (m *LoadMore) XXX_Size() int {
	return xxx_messageInfo_LoadMore.Size(m)
}
func (m *LoadMore) XXX_DiscardUnknown() {
	xxx_messageInfo_LoadMore.DiscardUnknown(m)
}

var xxx_messageInfo_LoadMore proto.InternalMessageInfo

func (m *LoadMore) GetLastPage() uint32 {
	if m != nil {
		return m.LastPage
	}
	return 0
}

type GetTopicListExcludeSubscribeReq struct {
	LoadMore             *LoadMore `protobuf:"bytes,1,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	Count                uint32    `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	SubscriberIds        []string  `protobuf:"bytes,3,rep,name=subscriber_ids,json=subscriberIds,proto3" json:"subscriber_ids,omitempty"`
	TopicType            TopicType `protobuf:"varint,4,opt,name=topic_type,json=topicType,proto3,enum=ugc.topic.TopicType" json:"topic_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetTopicListExcludeSubscribeReq) Reset()         { *m = GetTopicListExcludeSubscribeReq{} }
func (m *GetTopicListExcludeSubscribeReq) String() string { return proto.CompactTextString(m) }
func (*GetTopicListExcludeSubscribeReq) ProtoMessage()    {}
func (*GetTopicListExcludeSubscribeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{65}
}
func (m *GetTopicListExcludeSubscribeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicListExcludeSubscribeReq.Unmarshal(m, b)
}
func (m *GetTopicListExcludeSubscribeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicListExcludeSubscribeReq.Marshal(b, m, deterministic)
}
func (dst *GetTopicListExcludeSubscribeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicListExcludeSubscribeReq.Merge(dst, src)
}
func (m *GetTopicListExcludeSubscribeReq) XXX_Size() int {
	return xxx_messageInfo_GetTopicListExcludeSubscribeReq.Size(m)
}
func (m *GetTopicListExcludeSubscribeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicListExcludeSubscribeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicListExcludeSubscribeReq proto.InternalMessageInfo

func (m *GetTopicListExcludeSubscribeReq) GetLoadMore() *LoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

func (m *GetTopicListExcludeSubscribeReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetTopicListExcludeSubscribeReq) GetSubscriberIds() []string {
	if m != nil {
		return m.SubscriberIds
	}
	return nil
}

func (m *GetTopicListExcludeSubscribeReq) GetTopicType() TopicType {
	if m != nil {
		return m.TopicType
	}
	return TopicType_TypeCollection
}

type GetTopicListExcludeSubscribeResp struct {
	TopicInfos           []*TopicInfo `protobuf:"bytes,1,rep,name=topicInfos,proto3" json:"topicInfos,omitempty"`
	LoadMore             *LoadMore    `protobuf:"bytes,2,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetTopicListExcludeSubscribeResp) Reset()         { *m = GetTopicListExcludeSubscribeResp{} }
func (m *GetTopicListExcludeSubscribeResp) String() string { return proto.CompactTextString(m) }
func (*GetTopicListExcludeSubscribeResp) ProtoMessage()    {}
func (*GetTopicListExcludeSubscribeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{66}
}
func (m *GetTopicListExcludeSubscribeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicListExcludeSubscribeResp.Unmarshal(m, b)
}
func (m *GetTopicListExcludeSubscribeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicListExcludeSubscribeResp.Marshal(b, m, deterministic)
}
func (dst *GetTopicListExcludeSubscribeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicListExcludeSubscribeResp.Merge(dst, src)
}
func (m *GetTopicListExcludeSubscribeResp) XXX_Size() int {
	return xxx_messageInfo_GetTopicListExcludeSubscribeResp.Size(m)
}
func (m *GetTopicListExcludeSubscribeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicListExcludeSubscribeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicListExcludeSubscribeResp proto.InternalMessageInfo

func (m *GetTopicListExcludeSubscribeResp) GetTopicInfos() []*TopicInfo {
	if m != nil {
		return m.TopicInfos
	}
	return nil
}

func (m *GetTopicListExcludeSubscribeResp) GetLoadMore() *LoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type BatchCheckTopicBindUidReq struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchCheckTopicBindUidReq) Reset()         { *m = BatchCheckTopicBindUidReq{} }
func (m *BatchCheckTopicBindUidReq) String() string { return proto.CompactTextString(m) }
func (*BatchCheckTopicBindUidReq) ProtoMessage()    {}
func (*BatchCheckTopicBindUidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{67}
}
func (m *BatchCheckTopicBindUidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckTopicBindUidReq.Unmarshal(m, b)
}
func (m *BatchCheckTopicBindUidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckTopicBindUidReq.Marshal(b, m, deterministic)
}
func (dst *BatchCheckTopicBindUidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckTopicBindUidReq.Merge(dst, src)
}
func (m *BatchCheckTopicBindUidReq) XXX_Size() int {
	return xxx_messageInfo_BatchCheckTopicBindUidReq.Size(m)
}
func (m *BatchCheckTopicBindUidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckTopicBindUidReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckTopicBindUidReq proto.InternalMessageInfo

func (m *BatchCheckTopicBindUidReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type BatchCheckTopicBindUidResp struct {
	Result               map[uint32]bool `protobuf:"bytes,1,rep,name=result,proto3" json:"result,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatchCheckTopicBindUidResp) Reset()         { *m = BatchCheckTopicBindUidResp{} }
func (m *BatchCheckTopicBindUidResp) String() string { return proto.CompactTextString(m) }
func (*BatchCheckTopicBindUidResp) ProtoMessage()    {}
func (*BatchCheckTopicBindUidResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{68}
}
func (m *BatchCheckTopicBindUidResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckTopicBindUidResp.Unmarshal(m, b)
}
func (m *BatchCheckTopicBindUidResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckTopicBindUidResp.Marshal(b, m, deterministic)
}
func (dst *BatchCheckTopicBindUidResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckTopicBindUidResp.Merge(dst, src)
}
func (m *BatchCheckTopicBindUidResp) XXX_Size() int {
	return xxx_messageInfo_BatchCheckTopicBindUidResp.Size(m)
}
func (m *BatchCheckTopicBindUidResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckTopicBindUidResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckTopicBindUidResp proto.InternalMessageInfo

func (m *BatchCheckTopicBindUidResp) GetResult() map[uint32]bool {
	if m != nil {
		return m.Result
	}
	return nil
}

type UpdateTopicPostCountReq struct {
	TopicId              string   `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	PostId               string   `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Delete               bool     `protobuf:"varint,3,opt,name=delete,proto3" json:"delete,omitempty"`
	CreateAt             uint32   `protobuf:"varint,4,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	SubTopicId           string   `protobuf:"bytes,5,opt,name=sub_topic_id,json=subTopicId,proto3" json:"sub_topic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateTopicPostCountReq) Reset()         { *m = UpdateTopicPostCountReq{} }
func (m *UpdateTopicPostCountReq) String() string { return proto.CompactTextString(m) }
func (*UpdateTopicPostCountReq) ProtoMessage()    {}
func (*UpdateTopicPostCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{69}
}
func (m *UpdateTopicPostCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateTopicPostCountReq.Unmarshal(m, b)
}
func (m *UpdateTopicPostCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateTopicPostCountReq.Marshal(b, m, deterministic)
}
func (dst *UpdateTopicPostCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTopicPostCountReq.Merge(dst, src)
}
func (m *UpdateTopicPostCountReq) XXX_Size() int {
	return xxx_messageInfo_UpdateTopicPostCountReq.Size(m)
}
func (m *UpdateTopicPostCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTopicPostCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTopicPostCountReq proto.InternalMessageInfo

func (m *UpdateTopicPostCountReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *UpdateTopicPostCountReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *UpdateTopicPostCountReq) GetDelete() bool {
	if m != nil {
		return m.Delete
	}
	return false
}

func (m *UpdateTopicPostCountReq) GetCreateAt() uint32 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *UpdateTopicPostCountReq) GetSubTopicId() string {
	if m != nil {
		return m.SubTopicId
	}
	return ""
}

type UpdateTopicPostCountResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateTopicPostCountResp) Reset()         { *m = UpdateTopicPostCountResp{} }
func (m *UpdateTopicPostCountResp) String() string { return proto.CompactTextString(m) }
func (*UpdateTopicPostCountResp) ProtoMessage()    {}
func (*UpdateTopicPostCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{70}
}
func (m *UpdateTopicPostCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateTopicPostCountResp.Unmarshal(m, b)
}
func (m *UpdateTopicPostCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateTopicPostCountResp.Marshal(b, m, deterministic)
}
func (dst *UpdateTopicPostCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTopicPostCountResp.Merge(dst, src)
}
func (m *UpdateTopicPostCountResp) XXX_Size() int {
	return xxx_messageInfo_UpdateTopicPostCountResp.Size(m)
}
func (m *UpdateTopicPostCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTopicPostCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTopicPostCountResp proto.InternalMessageInfo

type PostInfo struct {
	TopicId              string   `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	PostId               string   `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Delete               bool     `protobuf:"varint,3,opt,name=delete,proto3" json:"delete,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PostInfo) Reset()         { *m = PostInfo{} }
func (m *PostInfo) String() string { return proto.CompactTextString(m) }
func (*PostInfo) ProtoMessage()    {}
func (*PostInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{71}
}
func (m *PostInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostInfo.Unmarshal(m, b)
}
func (m *PostInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostInfo.Marshal(b, m, deterministic)
}
func (dst *PostInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostInfo.Merge(dst, src)
}
func (m *PostInfo) XXX_Size() int {
	return xxx_messageInfo_PostInfo.Size(m)
}
func (m *PostInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PostInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PostInfo proto.InternalMessageInfo

func (m *PostInfo) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *PostInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *PostInfo) GetDelete() bool {
	if m != nil {
		return m.Delete
	}
	return false
}

type BatchUpdateTopicPostCountReq struct {
	PostInfos            []*PostInfo `protobuf:"bytes,1,rep,name=post_infos,json=postInfos,proto3" json:"post_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *BatchUpdateTopicPostCountReq) Reset()         { *m = BatchUpdateTopicPostCountReq{} }
func (m *BatchUpdateTopicPostCountReq) String() string { return proto.CompactTextString(m) }
func (*BatchUpdateTopicPostCountReq) ProtoMessage()    {}
func (*BatchUpdateTopicPostCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{72}
}
func (m *BatchUpdateTopicPostCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUpdateTopicPostCountReq.Unmarshal(m, b)
}
func (m *BatchUpdateTopicPostCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUpdateTopicPostCountReq.Marshal(b, m, deterministic)
}
func (dst *BatchUpdateTopicPostCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUpdateTopicPostCountReq.Merge(dst, src)
}
func (m *BatchUpdateTopicPostCountReq) XXX_Size() int {
	return xxx_messageInfo_BatchUpdateTopicPostCountReq.Size(m)
}
func (m *BatchUpdateTopicPostCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUpdateTopicPostCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUpdateTopicPostCountReq proto.InternalMessageInfo

func (m *BatchUpdateTopicPostCountReq) GetPostInfos() []*PostInfo {
	if m != nil {
		return m.PostInfos
	}
	return nil
}

type BatchUpdateTopicPostCountResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchUpdateTopicPostCountResp) Reset()         { *m = BatchUpdateTopicPostCountResp{} }
func (m *BatchUpdateTopicPostCountResp) String() string { return proto.CompactTextString(m) }
func (*BatchUpdateTopicPostCountResp) ProtoMessage()    {}
func (*BatchUpdateTopicPostCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{73}
}
func (m *BatchUpdateTopicPostCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUpdateTopicPostCountResp.Unmarshal(m, b)
}
func (m *BatchUpdateTopicPostCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUpdateTopicPostCountResp.Marshal(b, m, deterministic)
}
func (dst *BatchUpdateTopicPostCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUpdateTopicPostCountResp.Merge(dst, src)
}
func (m *BatchUpdateTopicPostCountResp) XXX_Size() int {
	return xxx_messageInfo_BatchUpdateTopicPostCountResp.Size(m)
}
func (m *BatchUpdateTopicPostCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUpdateTopicPostCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUpdateTopicPostCountResp proto.InternalMessageInfo

type IsSubscribeAllTopicReq struct {
	TopicIds             []string `protobuf:"bytes,1,rep,name=topic_ids,json=topicIds,proto3" json:"topic_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsSubscribeAllTopicReq) Reset()         { *m = IsSubscribeAllTopicReq{} }
func (m *IsSubscribeAllTopicReq) String() string { return proto.CompactTextString(m) }
func (*IsSubscribeAllTopicReq) ProtoMessage()    {}
func (*IsSubscribeAllTopicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{74}
}
func (m *IsSubscribeAllTopicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsSubscribeAllTopicReq.Unmarshal(m, b)
}
func (m *IsSubscribeAllTopicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsSubscribeAllTopicReq.Marshal(b, m, deterministic)
}
func (dst *IsSubscribeAllTopicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsSubscribeAllTopicReq.Merge(dst, src)
}
func (m *IsSubscribeAllTopicReq) XXX_Size() int {
	return xxx_messageInfo_IsSubscribeAllTopicReq.Size(m)
}
func (m *IsSubscribeAllTopicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IsSubscribeAllTopicReq.DiscardUnknown(m)
}

var xxx_messageInfo_IsSubscribeAllTopicReq proto.InternalMessageInfo

func (m *IsSubscribeAllTopicReq) GetTopicIds() []string {
	if m != nil {
		return m.TopicIds
	}
	return nil
}

type IsSubscribeAllTopicResp struct {
	Result               bool     `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsSubscribeAllTopicResp) Reset()         { *m = IsSubscribeAllTopicResp{} }
func (m *IsSubscribeAllTopicResp) String() string { return proto.CompactTextString(m) }
func (*IsSubscribeAllTopicResp) ProtoMessage()    {}
func (*IsSubscribeAllTopicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{75}
}
func (m *IsSubscribeAllTopicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsSubscribeAllTopicResp.Unmarshal(m, b)
}
func (m *IsSubscribeAllTopicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsSubscribeAllTopicResp.Marshal(b, m, deterministic)
}
func (dst *IsSubscribeAllTopicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsSubscribeAllTopicResp.Merge(dst, src)
}
func (m *IsSubscribeAllTopicResp) XXX_Size() int {
	return xxx_messageInfo_IsSubscribeAllTopicResp.Size(m)
}
func (m *IsSubscribeAllTopicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IsSubscribeAllTopicResp.DiscardUnknown(m)
}

var xxx_messageInfo_IsSubscribeAllTopicResp proto.InternalMessageInfo

func (m *IsSubscribeAllTopicResp) GetResult() bool {
	if m != nil {
		return m.Result
	}
	return false
}

type AdjustTopicBelongReq struct {
	TopicId              string   `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	ParentTopicId        string   `protobuf:"bytes,2,opt,name=parent_topic_id,json=parentTopicId,proto3" json:"parent_topic_id,omitempty"`
	IsDelete             bool     `protobuf:"varint,3,opt,name=is_delete,json=isDelete,proto3" json:"is_delete,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AdjustTopicBelongReq) Reset()         { *m = AdjustTopicBelongReq{} }
func (m *AdjustTopicBelongReq) String() string { return proto.CompactTextString(m) }
func (*AdjustTopicBelongReq) ProtoMessage()    {}
func (*AdjustTopicBelongReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{76}
}
func (m *AdjustTopicBelongReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AdjustTopicBelongReq.Unmarshal(m, b)
}
func (m *AdjustTopicBelongReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AdjustTopicBelongReq.Marshal(b, m, deterministic)
}
func (dst *AdjustTopicBelongReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AdjustTopicBelongReq.Merge(dst, src)
}
func (m *AdjustTopicBelongReq) XXX_Size() int {
	return xxx_messageInfo_AdjustTopicBelongReq.Size(m)
}
func (m *AdjustTopicBelongReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AdjustTopicBelongReq.DiscardUnknown(m)
}

var xxx_messageInfo_AdjustTopicBelongReq proto.InternalMessageInfo

func (m *AdjustTopicBelongReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *AdjustTopicBelongReq) GetParentTopicId() string {
	if m != nil {
		return m.ParentTopicId
	}
	return ""
}

func (m *AdjustTopicBelongReq) GetIsDelete() bool {
	if m != nil {
		return m.IsDelete
	}
	return false
}

type AdjustTopicBelongResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AdjustTopicBelongResp) Reset()         { *m = AdjustTopicBelongResp{} }
func (m *AdjustTopicBelongResp) String() string { return proto.CompactTextString(m) }
func (*AdjustTopicBelongResp) ProtoMessage()    {}
func (*AdjustTopicBelongResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{77}
}
func (m *AdjustTopicBelongResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AdjustTopicBelongResp.Unmarshal(m, b)
}
func (m *AdjustTopicBelongResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AdjustTopicBelongResp.Marshal(b, m, deterministic)
}
func (dst *AdjustTopicBelongResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AdjustTopicBelongResp.Merge(dst, src)
}
func (m *AdjustTopicBelongResp) XXX_Size() int {
	return xxx_messageInfo_AdjustTopicBelongResp.Size(m)
}
func (m *AdjustTopicBelongResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AdjustTopicBelongResp.DiscardUnknown(m)
}

var xxx_messageInfo_AdjustTopicBelongResp proto.InternalMessageInfo

type BatchGetParentTopicInfoReq struct {
	TopicIds             []string `protobuf:"bytes,1,rep,name=topic_ids,json=topicIds,proto3" json:"topic_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetParentTopicInfoReq) Reset()         { *m = BatchGetParentTopicInfoReq{} }
func (m *BatchGetParentTopicInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetParentTopicInfoReq) ProtoMessage()    {}
func (*BatchGetParentTopicInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{78}
}
func (m *BatchGetParentTopicInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetParentTopicInfoReq.Unmarshal(m, b)
}
func (m *BatchGetParentTopicInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetParentTopicInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetParentTopicInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetParentTopicInfoReq.Merge(dst, src)
}
func (m *BatchGetParentTopicInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetParentTopicInfoReq.Size(m)
}
func (m *BatchGetParentTopicInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetParentTopicInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetParentTopicInfoReq proto.InternalMessageInfo

func (m *BatchGetParentTopicInfoReq) GetTopicIds() []string {
	if m != nil {
		return m.TopicIds
	}
	return nil
}

type BatchGetParentTopicInfoResp struct {
	Results              map[string]*TopicInfoes `protobuf:"bytes,1,rep,name=results,proto3" json:"results,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *BatchGetParentTopicInfoResp) Reset()         { *m = BatchGetParentTopicInfoResp{} }
func (m *BatchGetParentTopicInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetParentTopicInfoResp) ProtoMessage()    {}
func (*BatchGetParentTopicInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{79}
}
func (m *BatchGetParentTopicInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetParentTopicInfoResp.Unmarshal(m, b)
}
func (m *BatchGetParentTopicInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetParentTopicInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetParentTopicInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetParentTopicInfoResp.Merge(dst, src)
}
func (m *BatchGetParentTopicInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetParentTopicInfoResp.Size(m)
}
func (m *BatchGetParentTopicInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetParentTopicInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetParentTopicInfoResp proto.InternalMessageInfo

func (m *BatchGetParentTopicInfoResp) GetResults() map[string]*TopicInfoes {
	if m != nil {
		return m.Results
	}
	return nil
}

type TopicInfoes struct {
	Infoes               []*TopicInfo `protobuf:"bytes,1,rep,name=infoes,proto3" json:"infoes,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *TopicInfoes) Reset()         { *m = TopicInfoes{} }
func (m *TopicInfoes) String() string { return proto.CompactTextString(m) }
func (*TopicInfoes) ProtoMessage()    {}
func (*TopicInfoes) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{80}
}
func (m *TopicInfoes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicInfoes.Unmarshal(m, b)
}
func (m *TopicInfoes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicInfoes.Marshal(b, m, deterministic)
}
func (dst *TopicInfoes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicInfoes.Merge(dst, src)
}
func (m *TopicInfoes) XXX_Size() int {
	return xxx_messageInfo_TopicInfoes.Size(m)
}
func (m *TopicInfoes) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicInfoes.DiscardUnknown(m)
}

var xxx_messageInfo_TopicInfoes proto.InternalMessageInfo

func (m *TopicInfoes) GetInfoes() []*TopicInfo {
	if m != nil {
		return m.Infoes
	}
	return nil
}

type SexAgeInfo struct {
	Sex                  []UserSex `protobuf:"varint,1,rep,packed,name=sex,proto3,enum=ugc.topic.UserSex" json:"sex,omitempty"`
	IsAgeUnlimited       bool      `protobuf:"varint,2,opt,name=is_age_unlimited,json=isAgeUnlimited,proto3" json:"is_age_unlimited,omitempty"`
	AgeYearMin           uint32    `protobuf:"varint,3,opt,name=age_year_min,json=ageYearMin,proto3" json:"age_year_min,omitempty"`
	AgeYearMax           uint32    `protobuf:"varint,4,opt,name=age_year_max,json=ageYearMax,proto3" json:"age_year_max,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *SexAgeInfo) Reset()         { *m = SexAgeInfo{} }
func (m *SexAgeInfo) String() string { return proto.CompactTextString(m) }
func (*SexAgeInfo) ProtoMessage()    {}
func (*SexAgeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{81}
}
func (m *SexAgeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SexAgeInfo.Unmarshal(m, b)
}
func (m *SexAgeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SexAgeInfo.Marshal(b, m, deterministic)
}
func (dst *SexAgeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SexAgeInfo.Merge(dst, src)
}
func (m *SexAgeInfo) XXX_Size() int {
	return xxx_messageInfo_SexAgeInfo.Size(m)
}
func (m *SexAgeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SexAgeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SexAgeInfo proto.InternalMessageInfo

func (m *SexAgeInfo) GetSex() []UserSex {
	if m != nil {
		return m.Sex
	}
	return nil
}

func (m *SexAgeInfo) GetIsAgeUnlimited() bool {
	if m != nil {
		return m.IsAgeUnlimited
	}
	return false
}

func (m *SexAgeInfo) GetAgeYearMin() uint32 {
	if m != nil {
		return m.AgeYearMin
	}
	return 0
}

func (m *SexAgeInfo) GetAgeYearMax() uint32 {
	if m != nil {
		return m.AgeYearMax
	}
	return 0
}

type PublisherTopicInfo struct {
	TopicId              string                 `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	AppId                PublisherTopicAppid    `protobuf:"varint,2,opt,name=app_id,json=appId,proto3,enum=ugc.topic.PublisherTopicAppid" json:"app_id,omitempty"`
	Platform             PublisherTopicPlatform `protobuf:"varint,3,opt,name=platform,proto3,enum=ugc.topic.PublisherTopicPlatform" json:"platform,omitempty"`
	BeginTime            int64                  `protobuf:"varint,4,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64                  `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Show                 PublisherTopicShowUser `protobuf:"varint,6,opt,name=show,proto3,enum=ugc.topic.PublisherTopicShowUser" json:"show,omitempty"`
	DaysReg              uint32                 `protobuf:"varint,7,opt,name=days_reg,json=daysReg,proto3" json:"days_reg,omitempty"`
	Age                  *SexAgeInfo            `protobuf:"bytes,8,opt,name=age,proto3" json:"age,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *PublisherTopicInfo) Reset()         { *m = PublisherTopicInfo{} }
func (m *PublisherTopicInfo) String() string { return proto.CompactTextString(m) }
func (*PublisherTopicInfo) ProtoMessage()    {}
func (*PublisherTopicInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{82}
}
func (m *PublisherTopicInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PublisherTopicInfo.Unmarshal(m, b)
}
func (m *PublisherTopicInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PublisherTopicInfo.Marshal(b, m, deterministic)
}
func (dst *PublisherTopicInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PublisherTopicInfo.Merge(dst, src)
}
func (m *PublisherTopicInfo) XXX_Size() int {
	return xxx_messageInfo_PublisherTopicInfo.Size(m)
}
func (m *PublisherTopicInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PublisherTopicInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PublisherTopicInfo proto.InternalMessageInfo

func (m *PublisherTopicInfo) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *PublisherTopicInfo) GetAppId() PublisherTopicAppid {
	if m != nil {
		return m.AppId
	}
	return PublisherTopicAppid_Appid_Default
}

func (m *PublisherTopicInfo) GetPlatform() PublisherTopicPlatform {
	if m != nil {
		return m.Platform
	}
	return PublisherTopicPlatform_Platform_Default
}

func (m *PublisherTopicInfo) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *PublisherTopicInfo) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *PublisherTopicInfo) GetShow() PublisherTopicShowUser {
	if m != nil {
		return m.Show
	}
	return PublisherTopicShowUser_To_Default
}

func (m *PublisherTopicInfo) GetDaysReg() uint32 {
	if m != nil {
		return m.DaysReg
	}
	return 0
}

func (m *PublisherTopicInfo) GetAge() *SexAgeInfo {
	if m != nil {
		return m.Age
	}
	return nil
}

// 新增接口：uids 对全局用户生效时，为空
type InsertPublisherTopicReq struct {
	Info                 *PublisherTopicInfo     `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	Uids                 []uint32                `protobuf:"varint,2,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	NewInsert            PublisherTopicNewInsert `protobuf:"varint,3,opt,name=new_insert,json=newInsert,proto3,enum=ugc.topic.PublisherTopicNewInsert" json:"new_insert,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *InsertPublisherTopicReq) Reset()         { *m = InsertPublisherTopicReq{} }
func (m *InsertPublisherTopicReq) String() string { return proto.CompactTextString(m) }
func (*InsertPublisherTopicReq) ProtoMessage()    {}
func (*InsertPublisherTopicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{83}
}
func (m *InsertPublisherTopicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsertPublisherTopicReq.Unmarshal(m, b)
}
func (m *InsertPublisherTopicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsertPublisherTopicReq.Marshal(b, m, deterministic)
}
func (dst *InsertPublisherTopicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsertPublisherTopicReq.Merge(dst, src)
}
func (m *InsertPublisherTopicReq) XXX_Size() int {
	return xxx_messageInfo_InsertPublisherTopicReq.Size(m)
}
func (m *InsertPublisherTopicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_InsertPublisherTopicReq.DiscardUnknown(m)
}

var xxx_messageInfo_InsertPublisherTopicReq proto.InternalMessageInfo

func (m *InsertPublisherTopicReq) GetInfo() *PublisherTopicInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *InsertPublisherTopicReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

func (m *InsertPublisherTopicReq) GetNewInsert() PublisherTopicNewInsert {
	if m != nil {
		return m.NewInsert
	}
	return PublisherTopicNewInsert_Is_Default
}

type InsertPublisherTopicResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InsertPublisherTopicResp) Reset()         { *m = InsertPublisherTopicResp{} }
func (m *InsertPublisherTopicResp) String() string { return proto.CompactTextString(m) }
func (*InsertPublisherTopicResp) ProtoMessage()    {}
func (*InsertPublisherTopicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{84}
}
func (m *InsertPublisherTopicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsertPublisherTopicResp.Unmarshal(m, b)
}
func (m *InsertPublisherTopicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsertPublisherTopicResp.Marshal(b, m, deterministic)
}
func (dst *InsertPublisherTopicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsertPublisherTopicResp.Merge(dst, src)
}
func (m *InsertPublisherTopicResp) XXX_Size() int {
	return xxx_messageInfo_InsertPublisherTopicResp.Size(m)
}
func (m *InsertPublisherTopicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_InsertPublisherTopicResp.DiscardUnknown(m)
}

var xxx_messageInfo_InsertPublisherTopicResp proto.InternalMessageInfo

// 获取topic配置（不包括uid）
type GetPublisherTopicInfoReq struct {
	TopicId              string   `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPublisherTopicInfoReq) Reset()         { *m = GetPublisherTopicInfoReq{} }
func (m *GetPublisherTopicInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetPublisherTopicInfoReq) ProtoMessage()    {}
func (*GetPublisherTopicInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{85}
}
func (m *GetPublisherTopicInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPublisherTopicInfoReq.Unmarshal(m, b)
}
func (m *GetPublisherTopicInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPublisherTopicInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetPublisherTopicInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPublisherTopicInfoReq.Merge(dst, src)
}
func (m *GetPublisherTopicInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetPublisherTopicInfoReq.Size(m)
}
func (m *GetPublisherTopicInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPublisherTopicInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPublisherTopicInfoReq proto.InternalMessageInfo

func (m *GetPublisherTopicInfoReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

type GetPublisherTopicInfoResp struct {
	Info                 *PublisherTopicInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetPublisherTopicInfoResp) Reset()         { *m = GetPublisherTopicInfoResp{} }
func (m *GetPublisherTopicInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetPublisherTopicInfoResp) ProtoMessage()    {}
func (*GetPublisherTopicInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{86}
}
func (m *GetPublisherTopicInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPublisherTopicInfoResp.Unmarshal(m, b)
}
func (m *GetPublisherTopicInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPublisherTopicInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetPublisherTopicInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPublisherTopicInfoResp.Merge(dst, src)
}
func (m *GetPublisherTopicInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetPublisherTopicInfoResp.Size(m)
}
func (m *GetPublisherTopicInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPublisherTopicInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPublisherTopicInfoResp proto.InternalMessageInfo

func (m *GetPublisherTopicInfoResp) GetInfo() *PublisherTopicInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

// 获取topic配置（包含uid）
type GetPublisherTopicUidsReq struct {
	TopicId              string   `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPublisherTopicUidsReq) Reset()         { *m = GetPublisherTopicUidsReq{} }
func (m *GetPublisherTopicUidsReq) String() string { return proto.CompactTextString(m) }
func (*GetPublisherTopicUidsReq) ProtoMessage()    {}
func (*GetPublisherTopicUidsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{87}
}
func (m *GetPublisherTopicUidsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPublisherTopicUidsReq.Unmarshal(m, b)
}
func (m *GetPublisherTopicUidsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPublisherTopicUidsReq.Marshal(b, m, deterministic)
}
func (dst *GetPublisherTopicUidsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPublisherTopicUidsReq.Merge(dst, src)
}
func (m *GetPublisherTopicUidsReq) XXX_Size() int {
	return xxx_messageInfo_GetPublisherTopicUidsReq.Size(m)
}
func (m *GetPublisherTopicUidsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPublisherTopicUidsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPublisherTopicUidsReq proto.InternalMessageInfo

func (m *GetPublisherTopicUidsReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *GetPublisherTopicUidsReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetPublisherTopicUidsReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetPublisherTopicUidsResp struct {
	Info                 *PublisherTopicInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	Uids                 []uint32            `protobuf:"varint,2,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetPublisherTopicUidsResp) Reset()         { *m = GetPublisherTopicUidsResp{} }
func (m *GetPublisherTopicUidsResp) String() string { return proto.CompactTextString(m) }
func (*GetPublisherTopicUidsResp) ProtoMessage()    {}
func (*GetPublisherTopicUidsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{88}
}
func (m *GetPublisherTopicUidsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPublisherTopicUidsResp.Unmarshal(m, b)
}
func (m *GetPublisherTopicUidsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPublisherTopicUidsResp.Marshal(b, m, deterministic)
}
func (dst *GetPublisherTopicUidsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPublisherTopicUidsResp.Merge(dst, src)
}
func (m *GetPublisherTopicUidsResp) XXX_Size() int {
	return xxx_messageInfo_GetPublisherTopicUidsResp.Size(m)
}
func (m *GetPublisherTopicUidsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPublisherTopicUidsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPublisherTopicUidsResp proto.InternalMessageInfo

func (m *GetPublisherTopicUidsResp) GetInfo() *PublisherTopicInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *GetPublisherTopicUidsResp) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

// 删除
type DeletePublisherTopicReq struct {
	TopicId              string   `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeletePublisherTopicReq) Reset()         { *m = DeletePublisherTopicReq{} }
func (m *DeletePublisherTopicReq) String() string { return proto.CompactTextString(m) }
func (*DeletePublisherTopicReq) ProtoMessage()    {}
func (*DeletePublisherTopicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{89}
}
func (m *DeletePublisherTopicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeletePublisherTopicReq.Unmarshal(m, b)
}
func (m *DeletePublisherTopicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeletePublisherTopicReq.Marshal(b, m, deterministic)
}
func (dst *DeletePublisherTopicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeletePublisherTopicReq.Merge(dst, src)
}
func (m *DeletePublisherTopicReq) XXX_Size() int {
	return xxx_messageInfo_DeletePublisherTopicReq.Size(m)
}
func (m *DeletePublisherTopicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeletePublisherTopicReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeletePublisherTopicReq proto.InternalMessageInfo

func (m *DeletePublisherTopicReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

type DeletePublisherTopicResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeletePublisherTopicResp) Reset()         { *m = DeletePublisherTopicResp{} }
func (m *DeletePublisherTopicResp) String() string { return proto.CompactTextString(m) }
func (*DeletePublisherTopicResp) ProtoMessage()    {}
func (*DeletePublisherTopicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{90}
}
func (m *DeletePublisherTopicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeletePublisherTopicResp.Unmarshal(m, b)
}
func (m *DeletePublisherTopicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeletePublisherTopicResp.Marshal(b, m, deterministic)
}
func (dst *DeletePublisherTopicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeletePublisherTopicResp.Merge(dst, src)
}
func (m *DeletePublisherTopicResp) XXX_Size() int {
	return xxx_messageInfo_DeletePublisherTopicResp.Size(m)
}
func (m *DeletePublisherTopicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeletePublisherTopicResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeletePublisherTopicResp proto.InternalMessageInfo

// 客户端获取
type GetPublisherTopicByUidReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPublisherTopicByUidReq) Reset()         { *m = GetPublisherTopicByUidReq{} }
func (m *GetPublisherTopicByUidReq) String() string { return proto.CompactTextString(m) }
func (*GetPublisherTopicByUidReq) ProtoMessage()    {}
func (*GetPublisherTopicByUidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{91}
}
func (m *GetPublisherTopicByUidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPublisherTopicByUidReq.Unmarshal(m, b)
}
func (m *GetPublisherTopicByUidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPublisherTopicByUidReq.Marshal(b, m, deterministic)
}
func (dst *GetPublisherTopicByUidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPublisherTopicByUidReq.Merge(dst, src)
}
func (m *GetPublisherTopicByUidReq) XXX_Size() int {
	return xxx_messageInfo_GetPublisherTopicByUidReq.Size(m)
}
func (m *GetPublisherTopicByUidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPublisherTopicByUidReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPublisherTopicByUidReq proto.InternalMessageInfo

func (m *GetPublisherTopicByUidReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetPublisherTopicByUidResp struct {
	Attr                 []*PublisherTopicInfo `protobuf:"bytes,1,rep,name=attr,proto3" json:"attr,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetPublisherTopicByUidResp) Reset()         { *m = GetPublisherTopicByUidResp{} }
func (m *GetPublisherTopicByUidResp) String() string { return proto.CompactTextString(m) }
func (*GetPublisherTopicByUidResp) ProtoMessage()    {}
func (*GetPublisherTopicByUidResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{92}
}
func (m *GetPublisherTopicByUidResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPublisherTopicByUidResp.Unmarshal(m, b)
}
func (m *GetPublisherTopicByUidResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPublisherTopicByUidResp.Marshal(b, m, deterministic)
}
func (dst *GetPublisherTopicByUidResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPublisherTopicByUidResp.Merge(dst, src)
}
func (m *GetPublisherTopicByUidResp) XXX_Size() int {
	return xxx_messageInfo_GetPublisherTopicByUidResp.Size(m)
}
func (m *GetPublisherTopicByUidResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPublisherTopicByUidResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPublisherTopicByUidResp proto.InternalMessageInfo

func (m *GetPublisherTopicByUidResp) GetAttr() []*PublisherTopicInfo {
	if m != nil {
		return m.Attr
	}
	return nil
}

// 获取发布器话题列表
type GetPublisherTopicListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPublisherTopicListReq) Reset()         { *m = GetPublisherTopicListReq{} }
func (m *GetPublisherTopicListReq) String() string { return proto.CompactTextString(m) }
func (*GetPublisherTopicListReq) ProtoMessage()    {}
func (*GetPublisherTopicListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{93}
}
func (m *GetPublisherTopicListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPublisherTopicListReq.Unmarshal(m, b)
}
func (m *GetPublisherTopicListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPublisherTopicListReq.Marshal(b, m, deterministic)
}
func (dst *GetPublisherTopicListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPublisherTopicListReq.Merge(dst, src)
}
func (m *GetPublisherTopicListReq) XXX_Size() int {
	return xxx_messageInfo_GetPublisherTopicListReq.Size(m)
}
func (m *GetPublisherTopicListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPublisherTopicListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPublisherTopicListReq proto.InternalMessageInfo

type GetPublisherTopicListResp struct {
	TopicIdList          []string `protobuf:"bytes,1,rep,name=topic_id_list,json=topicIdList,proto3" json:"topic_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPublisherTopicListResp) Reset()         { *m = GetPublisherTopicListResp{} }
func (m *GetPublisherTopicListResp) String() string { return proto.CompactTextString(m) }
func (*GetPublisherTopicListResp) ProtoMessage()    {}
func (*GetPublisherTopicListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{94}
}
func (m *GetPublisherTopicListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPublisherTopicListResp.Unmarshal(m, b)
}
func (m *GetPublisherTopicListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPublisherTopicListResp.Marshal(b, m, deterministic)
}
func (dst *GetPublisherTopicListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPublisherTopicListResp.Merge(dst, src)
}
func (m *GetPublisherTopicListResp) XXX_Size() int {
	return xxx_messageInfo_GetPublisherTopicListResp.Size(m)
}
func (m *GetPublisherTopicListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPublisherTopicListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPublisherTopicListResp proto.InternalMessageInfo

func (m *GetPublisherTopicListResp) GetTopicIdList() []string {
	if m != nil {
		return m.TopicIdList
	}
	return nil
}

// 增加话题下动态发布记录
type AddTopicStatisticReq struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	IsDelPost            bool     `protobuf:"varint,2,opt,name=is_del_post,json=isDelPost,proto3" json:"is_del_post,omitempty"`
	DiyTopicId           []string `protobuf:"bytes,3,rep,name=diy_topic_id,json=diyTopicId,proto3" json:"diy_topic_id,omitempty"`
	MoonTopicId          string   `protobuf:"bytes,4,opt,name=moon_topic_id,json=moonTopicId,proto3" json:"moon_topic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddTopicStatisticReq) Reset()         { *m = AddTopicStatisticReq{} }
func (m *AddTopicStatisticReq) String() string { return proto.CompactTextString(m) }
func (*AddTopicStatisticReq) ProtoMessage()    {}
func (*AddTopicStatisticReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{95}
}
func (m *AddTopicStatisticReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddTopicStatisticReq.Unmarshal(m, b)
}
func (m *AddTopicStatisticReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddTopicStatisticReq.Marshal(b, m, deterministic)
}
func (dst *AddTopicStatisticReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddTopicStatisticReq.Merge(dst, src)
}
func (m *AddTopicStatisticReq) XXX_Size() int {
	return xxx_messageInfo_AddTopicStatisticReq.Size(m)
}
func (m *AddTopicStatisticReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddTopicStatisticReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddTopicStatisticReq proto.InternalMessageInfo

func (m *AddTopicStatisticReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *AddTopicStatisticReq) GetIsDelPost() bool {
	if m != nil {
		return m.IsDelPost
	}
	return false
}

func (m *AddTopicStatisticReq) GetDiyTopicId() []string {
	if m != nil {
		return m.DiyTopicId
	}
	return nil
}

func (m *AddTopicStatisticReq) GetMoonTopicId() string {
	if m != nil {
		return m.MoonTopicId
	}
	return ""
}

type AddTopicStatisticResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddTopicStatisticResp) Reset()         { *m = AddTopicStatisticResp{} }
func (m *AddTopicStatisticResp) String() string { return proto.CompactTextString(m) }
func (*AddTopicStatisticResp) ProtoMessage()    {}
func (*AddTopicStatisticResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{96}
}
func (m *AddTopicStatisticResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddTopicStatisticResp.Unmarshal(m, b)
}
func (m *AddTopicStatisticResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddTopicStatisticResp.Marshal(b, m, deterministic)
}
func (dst *AddTopicStatisticResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddTopicStatisticResp.Merge(dst, src)
}
func (m *AddTopicStatisticResp) XXX_Size() int {
	return xxx_messageInfo_AddTopicStatisticResp.Size(m)
}
func (m *AddTopicStatisticResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddTopicStatisticResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddTopicStatisticResp proto.InternalMessageInfo

// 同时创建多个话题
type CreateTopicV2Req struct {
	TopicList            []*CreateTopicV2Req_TopicInfo `protobuf:"bytes,1,rep,name=topic_list,json=topicList,proto3" json:"topic_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *CreateTopicV2Req) Reset()         { *m = CreateTopicV2Req{} }
func (m *CreateTopicV2Req) String() string { return proto.CompactTextString(m) }
func (*CreateTopicV2Req) ProtoMessage()    {}
func (*CreateTopicV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{97}
}
func (m *CreateTopicV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateTopicV2Req.Unmarshal(m, b)
}
func (m *CreateTopicV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateTopicV2Req.Marshal(b, m, deterministic)
}
func (dst *CreateTopicV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateTopicV2Req.Merge(dst, src)
}
func (m *CreateTopicV2Req) XXX_Size() int {
	return xxx_messageInfo_CreateTopicV2Req.Size(m)
}
func (m *CreateTopicV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateTopicV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_CreateTopicV2Req proto.InternalMessageInfo

func (m *CreateTopicV2Req) GetTopicList() []*CreateTopicV2Req_TopicInfo {
	if m != nil {
		return m.TopicList
	}
	return nil
}

type CreateTopicV2Req_TopicInfo struct {
	Name                 string    `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Desc                 string    `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	IconUrl              string    `protobuf:"bytes,3,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	BindUid              uint32    `protobuf:"varint,4,opt,name=bind_uid,json=bindUid,proto3" json:"bind_uid,omitempty"` // Deprecated: Do not use.
	TopicType            TopicType `protobuf:"varint,5,opt,name=topic_type,json=topicType,proto3,enum=ugc.topic.TopicType" json:"topic_type,omitempty"`
	ParentTopicId        string    `protobuf:"bytes,6,opt,name=parent_topic_id,json=parentTopicId,proto3" json:"parent_topic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *CreateTopicV2Req_TopicInfo) Reset()         { *m = CreateTopicV2Req_TopicInfo{} }
func (m *CreateTopicV2Req_TopicInfo) String() string { return proto.CompactTextString(m) }
func (*CreateTopicV2Req_TopicInfo) ProtoMessage()    {}
func (*CreateTopicV2Req_TopicInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{97, 0}
}
func (m *CreateTopicV2Req_TopicInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateTopicV2Req_TopicInfo.Unmarshal(m, b)
}
func (m *CreateTopicV2Req_TopicInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateTopicV2Req_TopicInfo.Marshal(b, m, deterministic)
}
func (dst *CreateTopicV2Req_TopicInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateTopicV2Req_TopicInfo.Merge(dst, src)
}
func (m *CreateTopicV2Req_TopicInfo) XXX_Size() int {
	return xxx_messageInfo_CreateTopicV2Req_TopicInfo.Size(m)
}
func (m *CreateTopicV2Req_TopicInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateTopicV2Req_TopicInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CreateTopicV2Req_TopicInfo proto.InternalMessageInfo

func (m *CreateTopicV2Req_TopicInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *CreateTopicV2Req_TopicInfo) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *CreateTopicV2Req_TopicInfo) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

// Deprecated: Do not use.
func (m *CreateTopicV2Req_TopicInfo) GetBindUid() uint32 {
	if m != nil {
		return m.BindUid
	}
	return 0
}

func (m *CreateTopicV2Req_TopicInfo) GetTopicType() TopicType {
	if m != nil {
		return m.TopicType
	}
	return TopicType_TypeCollection
}

func (m *CreateTopicV2Req_TopicInfo) GetParentTopicId() string {
	if m != nil {
		return m.ParentTopicId
	}
	return ""
}

type CreateTopicV2Resp struct {
	TopicIdList          []string `protobuf:"bytes,1,rep,name=topic_id_list,json=topicIdList,proto3" json:"topic_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateTopicV2Resp) Reset()         { *m = CreateTopicV2Resp{} }
func (m *CreateTopicV2Resp) String() string { return proto.CompactTextString(m) }
func (*CreateTopicV2Resp) ProtoMessage()    {}
func (*CreateTopicV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{98}
}
func (m *CreateTopicV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateTopicV2Resp.Unmarshal(m, b)
}
func (m *CreateTopicV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateTopicV2Resp.Marshal(b, m, deterministic)
}
func (dst *CreateTopicV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateTopicV2Resp.Merge(dst, src)
}
func (m *CreateTopicV2Resp) XXX_Size() int {
	return xxx_messageInfo_CreateTopicV2Resp.Size(m)
}
func (m *CreateTopicV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateTopicV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateTopicV2Resp proto.InternalMessageInfo

func (m *CreateTopicV2Resp) GetTopicIdList() []string {
	if m != nil {
		return m.TopicIdList
	}
	return nil
}

type SearchTopicReq struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	LastCount            int32    `protobuf:"varint,2,opt,name=last_count,json=lastCount,proto3" json:"last_count,omitempty"`
	LastId               string   `protobuf:"bytes,3,opt,name=last_id,json=lastId,proto3" json:"last_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchTopicReq) Reset()         { *m = SearchTopicReq{} }
func (m *SearchTopicReq) String() string { return proto.CompactTextString(m) }
func (*SearchTopicReq) ProtoMessage()    {}
func (*SearchTopicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{99}
}
func (m *SearchTopicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchTopicReq.Unmarshal(m, b)
}
func (m *SearchTopicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchTopicReq.Marshal(b, m, deterministic)
}
func (dst *SearchTopicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchTopicReq.Merge(dst, src)
}
func (m *SearchTopicReq) XXX_Size() int {
	return xxx_messageInfo_SearchTopicReq.Size(m)
}
func (m *SearchTopicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchTopicReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchTopicReq proto.InternalMessageInfo

func (m *SearchTopicReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SearchTopicReq) GetLastCount() int32 {
	if m != nil {
		return m.LastCount
	}
	return 0
}

func (m *SearchTopicReq) GetLastId() string {
	if m != nil {
		return m.LastId
	}
	return ""
}

type SearchTopicResp struct {
	TopicList            []*SearchTopicResp_TopicInfo `protobuf:"bytes,1,rep,name=topic_list,json=topicList,proto3" json:"topic_list,omitempty"`
	LastCount            int32                        `protobuf:"varint,2,opt,name=last_count,json=lastCount,proto3" json:"last_count,omitempty"`
	LastId               string                       `protobuf:"bytes,3,opt,name=last_id,json=lastId,proto3" json:"last_id,omitempty"`
	NeedCreate           bool                         `protobuf:"varint,4,opt,name=need_create,json=needCreate,proto3" json:"need_create,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *SearchTopicResp) Reset()         { *m = SearchTopicResp{} }
func (m *SearchTopicResp) String() string { return proto.CompactTextString(m) }
func (*SearchTopicResp) ProtoMessage()    {}
func (*SearchTopicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{100}
}
func (m *SearchTopicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchTopicResp.Unmarshal(m, b)
}
func (m *SearchTopicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchTopicResp.Marshal(b, m, deterministic)
}
func (dst *SearchTopicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchTopicResp.Merge(dst, src)
}
func (m *SearchTopicResp) XXX_Size() int {
	return xxx_messageInfo_SearchTopicResp.Size(m)
}
func (m *SearchTopicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchTopicResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchTopicResp proto.InternalMessageInfo

func (m *SearchTopicResp) GetTopicList() []*SearchTopicResp_TopicInfo {
	if m != nil {
		return m.TopicList
	}
	return nil
}

func (m *SearchTopicResp) GetLastCount() int32 {
	if m != nil {
		return m.LastCount
	}
	return 0
}

func (m *SearchTopicResp) GetLastId() string {
	if m != nil {
		return m.LastId
	}
	return ""
}

func (m *SearchTopicResp) GetNeedCreate() bool {
	if m != nil {
		return m.NeedCreate
	}
	return false
}

type SearchTopicResp_TopicInfo struct {
	Name                 string    `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	TopicId              string    `protobuf:"bytes,2,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	Count                int32     `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	IsNew                bool      `protobuf:"varint,4,opt,name=is_new,json=isNew,proto3" json:"is_new,omitempty"`
	TopicType            TopicType `protobuf:"varint,5,opt,name=topic_type,json=topicType,proto3,enum=ugc.topic.TopicType" json:"topic_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *SearchTopicResp_TopicInfo) Reset()         { *m = SearchTopicResp_TopicInfo{} }
func (m *SearchTopicResp_TopicInfo) String() string { return proto.CompactTextString(m) }
func (*SearchTopicResp_TopicInfo) ProtoMessage()    {}
func (*SearchTopicResp_TopicInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{100, 0}
}
func (m *SearchTopicResp_TopicInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchTopicResp_TopicInfo.Unmarshal(m, b)
}
func (m *SearchTopicResp_TopicInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchTopicResp_TopicInfo.Marshal(b, m, deterministic)
}
func (dst *SearchTopicResp_TopicInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchTopicResp_TopicInfo.Merge(dst, src)
}
func (m *SearchTopicResp_TopicInfo) XXX_Size() int {
	return xxx_messageInfo_SearchTopicResp_TopicInfo.Size(m)
}
func (m *SearchTopicResp_TopicInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchTopicResp_TopicInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SearchTopicResp_TopicInfo proto.InternalMessageInfo

func (m *SearchTopicResp_TopicInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SearchTopicResp_TopicInfo) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *SearchTopicResp_TopicInfo) GetCount() int32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *SearchTopicResp_TopicInfo) GetIsNew() bool {
	if m != nil {
		return m.IsNew
	}
	return false
}

func (m *SearchTopicResp_TopicInfo) GetTopicType() TopicType {
	if m != nil {
		return m.TopicType
	}
	return TopicType_TypeCollection
}

type CreateEsTopicReq struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	TopicId              string   `protobuf:"bytes,2,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateEsTopicReq) Reset()         { *m = CreateEsTopicReq{} }
func (m *CreateEsTopicReq) String() string { return proto.CompactTextString(m) }
func (*CreateEsTopicReq) ProtoMessage()    {}
func (*CreateEsTopicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{101}
}
func (m *CreateEsTopicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateEsTopicReq.Unmarshal(m, b)
}
func (m *CreateEsTopicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateEsTopicReq.Marshal(b, m, deterministic)
}
func (dst *CreateEsTopicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateEsTopicReq.Merge(dst, src)
}
func (m *CreateEsTopicReq) XXX_Size() int {
	return xxx_messageInfo_CreateEsTopicReq.Size(m)
}
func (m *CreateEsTopicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateEsTopicReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateEsTopicReq proto.InternalMessageInfo

func (m *CreateEsTopicReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *CreateEsTopicReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

type CreateEsTopicResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateEsTopicResp) Reset()         { *m = CreateEsTopicResp{} }
func (m *CreateEsTopicResp) String() string { return proto.CompactTextString(m) }
func (*CreateEsTopicResp) ProtoMessage()    {}
func (*CreateEsTopicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{102}
}
func (m *CreateEsTopicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateEsTopicResp.Unmarshal(m, b)
}
func (m *CreateEsTopicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateEsTopicResp.Marshal(b, m, deterministic)
}
func (dst *CreateEsTopicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateEsTopicResp.Merge(dst, src)
}
func (m *CreateEsTopicResp) XXX_Size() int {
	return xxx_messageInfo_CreateEsTopicResp.Size(m)
}
func (m *CreateEsTopicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateEsTopicResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateEsTopicResp proto.InternalMessageInfo

type ReportTopicViewCountReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TopicIds             []string `protobuf:"bytes,2,rep,name=topic_ids,json=topicIds,proto3" json:"topic_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportTopicViewCountReq) Reset()         { *m = ReportTopicViewCountReq{} }
func (m *ReportTopicViewCountReq) String() string { return proto.CompactTextString(m) }
func (*ReportTopicViewCountReq) ProtoMessage()    {}
func (*ReportTopicViewCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{103}
}
func (m *ReportTopicViewCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportTopicViewCountReq.Unmarshal(m, b)
}
func (m *ReportTopicViewCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportTopicViewCountReq.Marshal(b, m, deterministic)
}
func (dst *ReportTopicViewCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportTopicViewCountReq.Merge(dst, src)
}
func (m *ReportTopicViewCountReq) XXX_Size() int {
	return xxx_messageInfo_ReportTopicViewCountReq.Size(m)
}
func (m *ReportTopicViewCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportTopicViewCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportTopicViewCountReq proto.InternalMessageInfo

func (m *ReportTopicViewCountReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReportTopicViewCountReq) GetTopicIds() []string {
	if m != nil {
		return m.TopicIds
	}
	return nil
}

type ReportTopicViewCountRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportTopicViewCountRsp) Reset()         { *m = ReportTopicViewCountRsp{} }
func (m *ReportTopicViewCountRsp) String() string { return proto.CompactTextString(m) }
func (*ReportTopicViewCountRsp) ProtoMessage()    {}
func (*ReportTopicViewCountRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{104}
}
func (m *ReportTopicViewCountRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportTopicViewCountRsp.Unmarshal(m, b)
}
func (m *ReportTopicViewCountRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportTopicViewCountRsp.Marshal(b, m, deterministic)
}
func (dst *ReportTopicViewCountRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportTopicViewCountRsp.Merge(dst, src)
}
func (m *ReportTopicViewCountRsp) XXX_Size() int {
	return xxx_messageInfo_ReportTopicViewCountRsp.Size(m)
}
func (m *ReportTopicViewCountRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportTopicViewCountRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportTopicViewCountRsp proto.InternalMessageInfo

type TopicRankWeightInfo struct {
	HasWeight            bool         `protobuf:"varint,1,opt,name=has_weight,json=hasWeight,proto3" json:"has_weight,omitempty"`
	Weight               string       `protobuf:"bytes,2,opt,name=weight,proto3" json:"weight,omitempty"`
	WeightStartTs        uint32       `protobuf:"varint,3,opt,name=weight_start_ts,json=weightStartTs,proto3" json:"weight_start_ts,omitempty"`
	WeightEndTs          uint32       `protobuf:"varint,4,opt,name=weight_end_ts,json=weightEndTs,proto3" json:"weight_end_ts,omitempty"`
	WeightStatus         WeightStatus `protobuf:"varint,5,opt,name=weight_status,json=weightStatus,proto3,enum=ugc.topic.WeightStatus" json:"weight_status,omitempty"`
	TopicId              string       `protobuf:"bytes,6,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *TopicRankWeightInfo) Reset()         { *m = TopicRankWeightInfo{} }
func (m *TopicRankWeightInfo) String() string { return proto.CompactTextString(m) }
func (*TopicRankWeightInfo) ProtoMessage()    {}
func (*TopicRankWeightInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{105}
}
func (m *TopicRankWeightInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicRankWeightInfo.Unmarshal(m, b)
}
func (m *TopicRankWeightInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicRankWeightInfo.Marshal(b, m, deterministic)
}
func (dst *TopicRankWeightInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicRankWeightInfo.Merge(dst, src)
}
func (m *TopicRankWeightInfo) XXX_Size() int {
	return xxx_messageInfo_TopicRankWeightInfo.Size(m)
}
func (m *TopicRankWeightInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicRankWeightInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TopicRankWeightInfo proto.InternalMessageInfo

func (m *TopicRankWeightInfo) GetHasWeight() bool {
	if m != nil {
		return m.HasWeight
	}
	return false
}

func (m *TopicRankWeightInfo) GetWeight() string {
	if m != nil {
		return m.Weight
	}
	return ""
}

func (m *TopicRankWeightInfo) GetWeightStartTs() uint32 {
	if m != nil {
		return m.WeightStartTs
	}
	return 0
}

func (m *TopicRankWeightInfo) GetWeightEndTs() uint32 {
	if m != nil {
		return m.WeightEndTs
	}
	return 0
}

func (m *TopicRankWeightInfo) GetWeightStatus() WeightStatus {
	if m != nil {
		return m.WeightStatus
	}
	return WeightStatus_WeiStDefault
}

func (m *TopicRankWeightInfo) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

type UpdateTopicRankWeightReq struct {
	WeightInfo           *TopicRankWeightInfo `protobuf:"bytes,1,opt,name=weight_info,json=weightInfo,proto3" json:"weight_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *UpdateTopicRankWeightReq) Reset()         { *m = UpdateTopicRankWeightReq{} }
func (m *UpdateTopicRankWeightReq) String() string { return proto.CompactTextString(m) }
func (*UpdateTopicRankWeightReq) ProtoMessage()    {}
func (*UpdateTopicRankWeightReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{106}
}
func (m *UpdateTopicRankWeightReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateTopicRankWeightReq.Unmarshal(m, b)
}
func (m *UpdateTopicRankWeightReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateTopicRankWeightReq.Marshal(b, m, deterministic)
}
func (dst *UpdateTopicRankWeightReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTopicRankWeightReq.Merge(dst, src)
}
func (m *UpdateTopicRankWeightReq) XXX_Size() int {
	return xxx_messageInfo_UpdateTopicRankWeightReq.Size(m)
}
func (m *UpdateTopicRankWeightReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTopicRankWeightReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTopicRankWeightReq proto.InternalMessageInfo

func (m *UpdateTopicRankWeightReq) GetWeightInfo() *TopicRankWeightInfo {
	if m != nil {
		return m.WeightInfo
	}
	return nil
}

type UpdateTopicRankWeightResp struct {
	UpdateCnt            uint32   `protobuf:"varint,1,opt,name=update_cnt,json=updateCnt,proto3" json:"update_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateTopicRankWeightResp) Reset()         { *m = UpdateTopicRankWeightResp{} }
func (m *UpdateTopicRankWeightResp) String() string { return proto.CompactTextString(m) }
func (*UpdateTopicRankWeightResp) ProtoMessage()    {}
func (*UpdateTopicRankWeightResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{107}
}
func (m *UpdateTopicRankWeightResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateTopicRankWeightResp.Unmarshal(m, b)
}
func (m *UpdateTopicRankWeightResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateTopicRankWeightResp.Marshal(b, m, deterministic)
}
func (dst *UpdateTopicRankWeightResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTopicRankWeightResp.Merge(dst, src)
}
func (m *UpdateTopicRankWeightResp) XXX_Size() int {
	return xxx_messageInfo_UpdateTopicRankWeightResp.Size(m)
}
func (m *UpdateTopicRankWeightResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTopicRankWeightResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTopicRankWeightResp proto.InternalMessageInfo

func (m *UpdateTopicRankWeightResp) GetUpdateCnt() uint32 {
	if m != nil {
		return m.UpdateCnt
	}
	return 0
}

type GetTopicRankWeightReq struct {
	TopicId              string   `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTopicRankWeightReq) Reset()         { *m = GetTopicRankWeightReq{} }
func (m *GetTopicRankWeightReq) String() string { return proto.CompactTextString(m) }
func (*GetTopicRankWeightReq) ProtoMessage()    {}
func (*GetTopicRankWeightReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{108}
}
func (m *GetTopicRankWeightReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicRankWeightReq.Unmarshal(m, b)
}
func (m *GetTopicRankWeightReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicRankWeightReq.Marshal(b, m, deterministic)
}
func (dst *GetTopicRankWeightReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicRankWeightReq.Merge(dst, src)
}
func (m *GetTopicRankWeightReq) XXX_Size() int {
	return xxx_messageInfo_GetTopicRankWeightReq.Size(m)
}
func (m *GetTopicRankWeightReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicRankWeightReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicRankWeightReq proto.InternalMessageInfo

func (m *GetTopicRankWeightReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

type GetTopicRankWeightResp struct {
	WeightInfo           *TopicAllRankWeightInfo `protobuf:"bytes,1,opt,name=weight_info,json=weightInfo,proto3" json:"weight_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetTopicRankWeightResp) Reset()         { *m = GetTopicRankWeightResp{} }
func (m *GetTopicRankWeightResp) String() string { return proto.CompactTextString(m) }
func (*GetTopicRankWeightResp) ProtoMessage()    {}
func (*GetTopicRankWeightResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{109}
}
func (m *GetTopicRankWeightResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicRankWeightResp.Unmarshal(m, b)
}
func (m *GetTopicRankWeightResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicRankWeightResp.Marshal(b, m, deterministic)
}
func (dst *GetTopicRankWeightResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicRankWeightResp.Merge(dst, src)
}
func (m *GetTopicRankWeightResp) XXX_Size() int {
	return xxx_messageInfo_GetTopicRankWeightResp.Size(m)
}
func (m *GetTopicRankWeightResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicRankWeightResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicRankWeightResp proto.InternalMessageInfo

func (m *GetTopicRankWeightResp) GetWeightInfo() *TopicAllRankWeightInfo {
	if m != nil {
		return m.WeightInfo
	}
	return nil
}

type BatGetTopicRankWeightReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetTopicRankWeightReq) Reset()         { *m = BatGetTopicRankWeightReq{} }
func (m *BatGetTopicRankWeightReq) String() string { return proto.CompactTextString(m) }
func (*BatGetTopicRankWeightReq) ProtoMessage()    {}
func (*BatGetTopicRankWeightReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{110}
}
func (m *BatGetTopicRankWeightReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetTopicRankWeightReq.Unmarshal(m, b)
}
func (m *BatGetTopicRankWeightReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetTopicRankWeightReq.Marshal(b, m, deterministic)
}
func (dst *BatGetTopicRankWeightReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetTopicRankWeightReq.Merge(dst, src)
}
func (m *BatGetTopicRankWeightReq) XXX_Size() int {
	return xxx_messageInfo_BatGetTopicRankWeightReq.Size(m)
}
func (m *BatGetTopicRankWeightReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetTopicRankWeightReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetTopicRankWeightReq proto.InternalMessageInfo

func (m *BatGetTopicRankWeightReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *BatGetTopicRankWeightReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type TopicAllRankWeightInfo struct {
	WeightInfo           *TopicRankWeightInfo `protobuf:"bytes,1,opt,name=weight_info,json=weightInfo,proto3" json:"weight_info,omitempty"`
	Realscore            float64              `protobuf:"fixed64,2,opt,name=realscore,proto3" json:"realscore,omitempty"`
	Totalscore           float64              `protobuf:"fixed64,3,opt,name=totalscore,proto3" json:"totalscore,omitempty"`
	ViewCnt              int64                `protobuf:"varint,4,opt,name=view_cnt,json=viewCnt,proto3" json:"view_cnt,omitempty"`
	PostCnt              int64                `protobuf:"varint,5,opt,name=post_cnt,json=postCnt,proto3" json:"post_cnt,omitempty"`
	TopicName            string               `protobuf:"bytes,6,opt,name=topic_name,json=topicName,proto3" json:"topic_name,omitempty"`
	CreateTime           int64                `protobuf:"varint,7,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Downscore            float64              `protobuf:"fixed64,8,opt,name=downscore,proto3" json:"downscore,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *TopicAllRankWeightInfo) Reset()         { *m = TopicAllRankWeightInfo{} }
func (m *TopicAllRankWeightInfo) String() string { return proto.CompactTextString(m) }
func (*TopicAllRankWeightInfo) ProtoMessage()    {}
func (*TopicAllRankWeightInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{111}
}
func (m *TopicAllRankWeightInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicAllRankWeightInfo.Unmarshal(m, b)
}
func (m *TopicAllRankWeightInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicAllRankWeightInfo.Marshal(b, m, deterministic)
}
func (dst *TopicAllRankWeightInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicAllRankWeightInfo.Merge(dst, src)
}
func (m *TopicAllRankWeightInfo) XXX_Size() int {
	return xxx_messageInfo_TopicAllRankWeightInfo.Size(m)
}
func (m *TopicAllRankWeightInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicAllRankWeightInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TopicAllRankWeightInfo proto.InternalMessageInfo

func (m *TopicAllRankWeightInfo) GetWeightInfo() *TopicRankWeightInfo {
	if m != nil {
		return m.WeightInfo
	}
	return nil
}

func (m *TopicAllRankWeightInfo) GetRealscore() float64 {
	if m != nil {
		return m.Realscore
	}
	return 0
}

func (m *TopicAllRankWeightInfo) GetTotalscore() float64 {
	if m != nil {
		return m.Totalscore
	}
	return 0
}

func (m *TopicAllRankWeightInfo) GetViewCnt() int64 {
	if m != nil {
		return m.ViewCnt
	}
	return 0
}

func (m *TopicAllRankWeightInfo) GetPostCnt() int64 {
	if m != nil {
		return m.PostCnt
	}
	return 0
}

func (m *TopicAllRankWeightInfo) GetTopicName() string {
	if m != nil {
		return m.TopicName
	}
	return ""
}

func (m *TopicAllRankWeightInfo) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *TopicAllRankWeightInfo) GetDownscore() float64 {
	if m != nil {
		return m.Downscore
	}
	return 0
}

type BatGetTopicRankWeightResp struct {
	TopicList            []*TopicAllRankWeightInfo `protobuf:"bytes,1,rep,name=topic_list,json=topicList,proto3" json:"topic_list,omitempty"`
	TotalCnt             uint32                    `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *BatGetTopicRankWeightResp) Reset()         { *m = BatGetTopicRankWeightResp{} }
func (m *BatGetTopicRankWeightResp) String() string { return proto.CompactTextString(m) }
func (*BatGetTopicRankWeightResp) ProtoMessage()    {}
func (*BatGetTopicRankWeightResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{112}
}
func (m *BatGetTopicRankWeightResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetTopicRankWeightResp.Unmarshal(m, b)
}
func (m *BatGetTopicRankWeightResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetTopicRankWeightResp.Marshal(b, m, deterministic)
}
func (dst *BatGetTopicRankWeightResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetTopicRankWeightResp.Merge(dst, src)
}
func (m *BatGetTopicRankWeightResp) XXX_Size() int {
	return xxx_messageInfo_BatGetTopicRankWeightResp.Size(m)
}
func (m *BatGetTopicRankWeightResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetTopicRankWeightResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetTopicRankWeightResp proto.InternalMessageInfo

func (m *BatGetTopicRankWeightResp) GetTopicList() []*TopicAllRankWeightInfo {
	if m != nil {
		return m.TopicList
	}
	return nil
}

func (m *BatGetTopicRankWeightResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

type TopicAdInfo struct {
	ConfigId             string   `protobuf:"bytes,1,opt,name=config_id,json=configId,proto3" json:"config_id,omitempty"`
	TopicId              string   `protobuf:"bytes,2,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	Index                uint32   `protobuf:"varint,3,opt,name=index,proto3" json:"index,omitempty"`
	Platform             uint32   `protobuf:"varint,4,opt,name=platform,proto3" json:"platform,omitempty"`
	StartTs              uint32   `protobuf:"varint,5,opt,name=start_ts,json=startTs,proto3" json:"start_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,6,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	PicUrl               string   `protobuf:"bytes,7,opt,name=pic_url,json=picUrl,proto3" json:"pic_url,omitempty"`
	Link                 string   `protobuf:"bytes,8,opt,name=link,proto3" json:"link,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,9,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	AdType               uint32   `protobuf:"varint,10,opt,name=ad_type,json=adType,proto3" json:"ad_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TopicAdInfo) Reset()         { *m = TopicAdInfo{} }
func (m *TopicAdInfo) String() string { return proto.CompactTextString(m) }
func (*TopicAdInfo) ProtoMessage()    {}
func (*TopicAdInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{113}
}
func (m *TopicAdInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicAdInfo.Unmarshal(m, b)
}
func (m *TopicAdInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicAdInfo.Marshal(b, m, deterministic)
}
func (dst *TopicAdInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicAdInfo.Merge(dst, src)
}
func (m *TopicAdInfo) XXX_Size() int {
	return xxx_messageInfo_TopicAdInfo.Size(m)
}
func (m *TopicAdInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicAdInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TopicAdInfo proto.InternalMessageInfo

func (m *TopicAdInfo) GetConfigId() string {
	if m != nil {
		return m.ConfigId
	}
	return ""
}

func (m *TopicAdInfo) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *TopicAdInfo) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *TopicAdInfo) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

func (m *TopicAdInfo) GetStartTs() uint32 {
	if m != nil {
		return m.StartTs
	}
	return 0
}

func (m *TopicAdInfo) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *TopicAdInfo) GetPicUrl() string {
	if m != nil {
		return m.PicUrl
	}
	return ""
}

func (m *TopicAdInfo) GetLink() string {
	if m != nil {
		return m.Link
	}
	return ""
}

func (m *TopicAdInfo) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *TopicAdInfo) GetAdType() uint32 {
	if m != nil {
		return m.AdType
	}
	return 0
}

type SetTopicAdReq struct {
	Info                 *TopicAdInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetTopicAdReq) Reset()         { *m = SetTopicAdReq{} }
func (m *SetTopicAdReq) String() string { return proto.CompactTextString(m) }
func (*SetTopicAdReq) ProtoMessage()    {}
func (*SetTopicAdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{114}
}
func (m *SetTopicAdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetTopicAdReq.Unmarshal(m, b)
}
func (m *SetTopicAdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetTopicAdReq.Marshal(b, m, deterministic)
}
func (dst *SetTopicAdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetTopicAdReq.Merge(dst, src)
}
func (m *SetTopicAdReq) XXX_Size() int {
	return xxx_messageInfo_SetTopicAdReq.Size(m)
}
func (m *SetTopicAdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetTopicAdReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetTopicAdReq proto.InternalMessageInfo

func (m *SetTopicAdReq) GetInfo() *TopicAdInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type SetTopicAdResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetTopicAdResp) Reset()         { *m = SetTopicAdResp{} }
func (m *SetTopicAdResp) String() string { return proto.CompactTextString(m) }
func (*SetTopicAdResp) ProtoMessage()    {}
func (*SetTopicAdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{115}
}
func (m *SetTopicAdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetTopicAdResp.Unmarshal(m, b)
}
func (m *SetTopicAdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetTopicAdResp.Marshal(b, m, deterministic)
}
func (dst *SetTopicAdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetTopicAdResp.Merge(dst, src)
}
func (m *SetTopicAdResp) XXX_Size() int {
	return xxx_messageInfo_SetTopicAdResp.Size(m)
}
func (m *SetTopicAdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetTopicAdResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetTopicAdResp proto.InternalMessageInfo

type GetTopicAdReq struct {
	TopicName            string   `protobuf:"bytes,1,opt,name=topic_name,json=topicName,proto3" json:"topic_name,omitempty"`
	TopicId              string   `protobuf:"bytes,2,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	Status               uint32   `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	Offset               uint32   `protobuf:"varint,5,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,6,opt,name=limit,proto3" json:"limit,omitempty"`
	Platform             uint32   `protobuf:"varint,7,opt,name=platform,proto3" json:"platform,omitempty"`
	ForApp               bool     `protobuf:"varint,8,opt,name=for_app,json=forApp,proto3" json:"for_app,omitempty"`
	AdType               uint32   `protobuf:"varint,9,opt,name=ad_type,json=adType,proto3" json:"ad_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTopicAdReq) Reset()         { *m = GetTopicAdReq{} }
func (m *GetTopicAdReq) String() string { return proto.CompactTextString(m) }
func (*GetTopicAdReq) ProtoMessage()    {}
func (*GetTopicAdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{116}
}
func (m *GetTopicAdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicAdReq.Unmarshal(m, b)
}
func (m *GetTopicAdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicAdReq.Marshal(b, m, deterministic)
}
func (dst *GetTopicAdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicAdReq.Merge(dst, src)
}
func (m *GetTopicAdReq) XXX_Size() int {
	return xxx_messageInfo_GetTopicAdReq.Size(m)
}
func (m *GetTopicAdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicAdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicAdReq proto.InternalMessageInfo

func (m *GetTopicAdReq) GetTopicName() string {
	if m != nil {
		return m.TopicName
	}
	return ""
}

func (m *GetTopicAdReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *GetTopicAdReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetTopicAdReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetTopicAdReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetTopicAdReq) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

func (m *GetTopicAdReq) GetForApp() bool {
	if m != nil {
		return m.ForApp
	}
	return false
}

func (m *GetTopicAdReq) GetAdType() uint32 {
	if m != nil {
		return m.AdType
	}
	return 0
}

type GetTopicAdResp struct {
	Infos                []*TopicAdInfo `protobuf:"bytes,1,rep,name=infos,proto3" json:"infos,omitempty"`
	TotalCnt             uint32         `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetTopicAdResp) Reset()         { *m = GetTopicAdResp{} }
func (m *GetTopicAdResp) String() string { return proto.CompactTextString(m) }
func (*GetTopicAdResp) ProtoMessage()    {}
func (*GetTopicAdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{117}
}
func (m *GetTopicAdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicAdResp.Unmarshal(m, b)
}
func (m *GetTopicAdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicAdResp.Marshal(b, m, deterministic)
}
func (dst *GetTopicAdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicAdResp.Merge(dst, src)
}
func (m *GetTopicAdResp) XXX_Size() int {
	return xxx_messageInfo_GetTopicAdResp.Size(m)
}
func (m *GetTopicAdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicAdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicAdResp proto.InternalMessageInfo

func (m *GetTopicAdResp) GetInfos() []*TopicAdInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

func (m *GetTopicAdResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

type DelTopicAdReq struct {
	ConfigIds            []string `protobuf:"bytes,1,rep,name=config_ids,json=configIds,proto3" json:"config_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelTopicAdReq) Reset()         { *m = DelTopicAdReq{} }
func (m *DelTopicAdReq) String() string { return proto.CompactTextString(m) }
func (*DelTopicAdReq) ProtoMessage()    {}
func (*DelTopicAdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{118}
}
func (m *DelTopicAdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelTopicAdReq.Unmarshal(m, b)
}
func (m *DelTopicAdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelTopicAdReq.Marshal(b, m, deterministic)
}
func (dst *DelTopicAdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelTopicAdReq.Merge(dst, src)
}
func (m *DelTopicAdReq) XXX_Size() int {
	return xxx_messageInfo_DelTopicAdReq.Size(m)
}
func (m *DelTopicAdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelTopicAdReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelTopicAdReq proto.InternalMessageInfo

func (m *DelTopicAdReq) GetConfigIds() []string {
	if m != nil {
		return m.ConfigIds
	}
	return nil
}

type DelTopicAdResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelTopicAdResp) Reset()         { *m = DelTopicAdResp{} }
func (m *DelTopicAdResp) String() string { return proto.CompactTextString(m) }
func (*DelTopicAdResp) ProtoMessage()    {}
func (*DelTopicAdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{119}
}
func (m *DelTopicAdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelTopicAdResp.Unmarshal(m, b)
}
func (m *DelTopicAdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelTopicAdResp.Marshal(b, m, deterministic)
}
func (dst *DelTopicAdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelTopicAdResp.Merge(dst, src)
}
func (m *DelTopicAdResp) XXX_Size() int {
	return xxx_messageInfo_DelTopicAdResp.Size(m)
}
func (m *DelTopicAdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelTopicAdResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelTopicAdResp proto.InternalMessageInfo

// 话题绑定的游戏id
type GetTopicBindGameIDReq struct {
	GameId               []uint32 `protobuf:"varint,1,rep,packed,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTopicBindGameIDReq) Reset()         { *m = GetTopicBindGameIDReq{} }
func (m *GetTopicBindGameIDReq) String() string { return proto.CompactTextString(m) }
func (*GetTopicBindGameIDReq) ProtoMessage()    {}
func (*GetTopicBindGameIDReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{120}
}
func (m *GetTopicBindGameIDReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicBindGameIDReq.Unmarshal(m, b)
}
func (m *GetTopicBindGameIDReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicBindGameIDReq.Marshal(b, m, deterministic)
}
func (dst *GetTopicBindGameIDReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicBindGameIDReq.Merge(dst, src)
}
func (m *GetTopicBindGameIDReq) XXX_Size() int {
	return xxx_messageInfo_GetTopicBindGameIDReq.Size(m)
}
func (m *GetTopicBindGameIDReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicBindGameIDReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicBindGameIDReq proto.InternalMessageInfo

func (m *GetTopicBindGameIDReq) GetGameId() []uint32 {
	if m != nil {
		return m.GameId
	}
	return nil
}

type TopicInfoBindGame struct {
	TopicId              string   `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	TopicName            string   `protobuf:"bytes,2,opt,name=topic_name,json=topicName,proto3" json:"topic_name,omitempty"`
	GameId               uint32   `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TopicInfoBindGame) Reset()         { *m = TopicInfoBindGame{} }
func (m *TopicInfoBindGame) String() string { return proto.CompactTextString(m) }
func (*TopicInfoBindGame) ProtoMessage()    {}
func (*TopicInfoBindGame) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{121}
}
func (m *TopicInfoBindGame) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicInfoBindGame.Unmarshal(m, b)
}
func (m *TopicInfoBindGame) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicInfoBindGame.Marshal(b, m, deterministic)
}
func (dst *TopicInfoBindGame) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicInfoBindGame.Merge(dst, src)
}
func (m *TopicInfoBindGame) XXX_Size() int {
	return xxx_messageInfo_TopicInfoBindGame.Size(m)
}
func (m *TopicInfoBindGame) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicInfoBindGame.DiscardUnknown(m)
}

var xxx_messageInfo_TopicInfoBindGame proto.InternalMessageInfo

func (m *TopicInfoBindGame) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *TopicInfoBindGame) GetTopicName() string {
	if m != nil {
		return m.TopicName
	}
	return ""
}

func (m *TopicInfoBindGame) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetTopicBindGameIDResp struct {
	Topics               []*TopicInfoBindGame `protobuf:"bytes,1,rep,name=topics,proto3" json:"topics,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetTopicBindGameIDResp) Reset()         { *m = GetTopicBindGameIDResp{} }
func (m *GetTopicBindGameIDResp) String() string { return proto.CompactTextString(m) }
func (*GetTopicBindGameIDResp) ProtoMessage()    {}
func (*GetTopicBindGameIDResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{122}
}
func (m *GetTopicBindGameIDResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicBindGameIDResp.Unmarshal(m, b)
}
func (m *GetTopicBindGameIDResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicBindGameIDResp.Marshal(b, m, deterministic)
}
func (dst *GetTopicBindGameIDResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicBindGameIDResp.Merge(dst, src)
}
func (m *GetTopicBindGameIDResp) XXX_Size() int {
	return xxx_messageInfo_GetTopicBindGameIDResp.Size(m)
}
func (m *GetTopicBindGameIDResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicBindGameIDResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicBindGameIDResp proto.InternalMessageInfo

func (m *GetTopicBindGameIDResp) GetTopics() []*TopicInfoBindGame {
	if m != nil {
		return m.Topics
	}
	return nil
}

// 新建 一个心情id对应的话题id
type CreateTopicIDForMoodReq struct {
	MoodId               string   `protobuf:"bytes,1,opt,name=mood_id,json=moodId,proto3" json:"mood_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateTopicIDForMoodReq) Reset()         { *m = CreateTopicIDForMoodReq{} }
func (m *CreateTopicIDForMoodReq) String() string { return proto.CompactTextString(m) }
func (*CreateTopicIDForMoodReq) ProtoMessage()    {}
func (*CreateTopicIDForMoodReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{123}
}
func (m *CreateTopicIDForMoodReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateTopicIDForMoodReq.Unmarshal(m, b)
}
func (m *CreateTopicIDForMoodReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateTopicIDForMoodReq.Marshal(b, m, deterministic)
}
func (dst *CreateTopicIDForMoodReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateTopicIDForMoodReq.Merge(dst, src)
}
func (m *CreateTopicIDForMoodReq) XXX_Size() int {
	return xxx_messageInfo_CreateTopicIDForMoodReq.Size(m)
}
func (m *CreateTopicIDForMoodReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateTopicIDForMoodReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateTopicIDForMoodReq proto.InternalMessageInfo

func (m *CreateTopicIDForMoodReq) GetMoodId() string {
	if m != nil {
		return m.MoodId
	}
	return ""
}

type CreateTopicIDForMoodResp struct {
	TopicId              string   `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateTopicIDForMoodResp) Reset()         { *m = CreateTopicIDForMoodResp{} }
func (m *CreateTopicIDForMoodResp) String() string { return proto.CompactTextString(m) }
func (*CreateTopicIDForMoodResp) ProtoMessage()    {}
func (*CreateTopicIDForMoodResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{124}
}
func (m *CreateTopicIDForMoodResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateTopicIDForMoodResp.Unmarshal(m, b)
}
func (m *CreateTopicIDForMoodResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateTopicIDForMoodResp.Marshal(b, m, deterministic)
}
func (dst *CreateTopicIDForMoodResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateTopicIDForMoodResp.Merge(dst, src)
}
func (m *CreateTopicIDForMoodResp) XXX_Size() int {
	return xxx_messageInfo_CreateTopicIDForMoodResp.Size(m)
}
func (m *CreateTopicIDForMoodResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateTopicIDForMoodResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateTopicIDForMoodResp proto.InternalMessageInfo

func (m *CreateTopicIDForMoodResp) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

type GetTopicsCacheReq struct {
	TopicIds             []string `protobuf:"bytes,1,rep,name=topic_ids,json=topicIds,proto3" json:"topic_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTopicsCacheReq) Reset()         { *m = GetTopicsCacheReq{} }
func (m *GetTopicsCacheReq) String() string { return proto.CompactTextString(m) }
func (*GetTopicsCacheReq) ProtoMessage()    {}
func (*GetTopicsCacheReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{125}
}
func (m *GetTopicsCacheReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicsCacheReq.Unmarshal(m, b)
}
func (m *GetTopicsCacheReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicsCacheReq.Marshal(b, m, deterministic)
}
func (dst *GetTopicsCacheReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicsCacheReq.Merge(dst, src)
}
func (m *GetTopicsCacheReq) XXX_Size() int {
	return xxx_messageInfo_GetTopicsCacheReq.Size(m)
}
func (m *GetTopicsCacheReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicsCacheReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicsCacheReq proto.InternalMessageInfo

func (m *GetTopicsCacheReq) GetTopicIds() []string {
	if m != nil {
		return m.TopicIds
	}
	return nil
}

// 帖子缓存，帖子，用户数目没用到，暂时不准
type GetTopicsCacheResp struct {
	TopicInfos           []*TopicInfo `protobuf:"bytes,1,rep,name=topicInfos,proto3" json:"topicInfos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetTopicsCacheResp) Reset()         { *m = GetTopicsCacheResp{} }
func (m *GetTopicsCacheResp) String() string { return proto.CompactTextString(m) }
func (*GetTopicsCacheResp) ProtoMessage()    {}
func (*GetTopicsCacheResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{126}
}
func (m *GetTopicsCacheResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicsCacheResp.Unmarshal(m, b)
}
func (m *GetTopicsCacheResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicsCacheResp.Marshal(b, m, deterministic)
}
func (dst *GetTopicsCacheResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicsCacheResp.Merge(dst, src)
}
func (m *GetTopicsCacheResp) XXX_Size() int {
	return xxx_messageInfo_GetTopicsCacheResp.Size(m)
}
func (m *GetTopicsCacheResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicsCacheResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicsCacheResp proto.InternalMessageInfo

func (m *GetTopicsCacheResp) GetTopicInfos() []*TopicInfo {
	if m != nil {
		return m.TopicInfos
	}
	return nil
}

type TopicStreamForceConf struct {
	ConfId               string   `protobuf:"bytes,1,opt,name=conf_id,json=confId,proto3" json:"conf_id,omitempty"`
	TopicId              string   `protobuf:"bytes,2,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	PostId               string   `protobuf:"bytes,3,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Position             uint32   `protobuf:"varint,4,opt,name=position,proto3" json:"position,omitempty"`
	BeginAt              uint32   `protobuf:"varint,5,opt,name=begin_at,json=beginAt,proto3" json:"begin_at,omitempty"`
	EndAt                uint32   `protobuf:"varint,6,opt,name=end_at,json=endAt,proto3" json:"end_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TopicStreamForceConf) Reset()         { *m = TopicStreamForceConf{} }
func (m *TopicStreamForceConf) String() string { return proto.CompactTextString(m) }
func (*TopicStreamForceConf) ProtoMessage()    {}
func (*TopicStreamForceConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{127}
}
func (m *TopicStreamForceConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicStreamForceConf.Unmarshal(m, b)
}
func (m *TopicStreamForceConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicStreamForceConf.Marshal(b, m, deterministic)
}
func (dst *TopicStreamForceConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicStreamForceConf.Merge(dst, src)
}
func (m *TopicStreamForceConf) XXX_Size() int {
	return xxx_messageInfo_TopicStreamForceConf.Size(m)
}
func (m *TopicStreamForceConf) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicStreamForceConf.DiscardUnknown(m)
}

var xxx_messageInfo_TopicStreamForceConf proto.InternalMessageInfo

func (m *TopicStreamForceConf) GetConfId() string {
	if m != nil {
		return m.ConfId
	}
	return ""
}

func (m *TopicStreamForceConf) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *TopicStreamForceConf) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *TopicStreamForceConf) GetPosition() uint32 {
	if m != nil {
		return m.Position
	}
	return 0
}

func (m *TopicStreamForceConf) GetBeginAt() uint32 {
	if m != nil {
		return m.BeginAt
	}
	return 0
}

func (m *TopicStreamForceConf) GetEndAt() uint32 {
	if m != nil {
		return m.EndAt
	}
	return 0
}

type DelTopicStreamForceConfReq struct {
	ConfId               string   `protobuf:"bytes,1,opt,name=conf_id,json=confId,proto3" json:"conf_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelTopicStreamForceConfReq) Reset()         { *m = DelTopicStreamForceConfReq{} }
func (m *DelTopicStreamForceConfReq) String() string { return proto.CompactTextString(m) }
func (*DelTopicStreamForceConfReq) ProtoMessage()    {}
func (*DelTopicStreamForceConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{128}
}
func (m *DelTopicStreamForceConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelTopicStreamForceConfReq.Unmarshal(m, b)
}
func (m *DelTopicStreamForceConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelTopicStreamForceConfReq.Marshal(b, m, deterministic)
}
func (dst *DelTopicStreamForceConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelTopicStreamForceConfReq.Merge(dst, src)
}
func (m *DelTopicStreamForceConfReq) XXX_Size() int {
	return xxx_messageInfo_DelTopicStreamForceConfReq.Size(m)
}
func (m *DelTopicStreamForceConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelTopicStreamForceConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelTopicStreamForceConfReq proto.InternalMessageInfo

func (m *DelTopicStreamForceConfReq) GetConfId() string {
	if m != nil {
		return m.ConfId
	}
	return ""
}

type DelTopicStreamForceConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelTopicStreamForceConfResp) Reset()         { *m = DelTopicStreamForceConfResp{} }
func (m *DelTopicStreamForceConfResp) String() string { return proto.CompactTextString(m) }
func (*DelTopicStreamForceConfResp) ProtoMessage()    {}
func (*DelTopicStreamForceConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{129}
}
func (m *DelTopicStreamForceConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelTopicStreamForceConfResp.Unmarshal(m, b)
}
func (m *DelTopicStreamForceConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelTopicStreamForceConfResp.Marshal(b, m, deterministic)
}
func (dst *DelTopicStreamForceConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelTopicStreamForceConfResp.Merge(dst, src)
}
func (m *DelTopicStreamForceConfResp) XXX_Size() int {
	return xxx_messageInfo_DelTopicStreamForceConfResp.Size(m)
}
func (m *DelTopicStreamForceConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelTopicStreamForceConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelTopicStreamForceConfResp proto.InternalMessageInfo

type SetTopicStreamForceConfReq struct {
	Conf                 *TopicStreamForceConf `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *SetTopicStreamForceConfReq) Reset()         { *m = SetTopicStreamForceConfReq{} }
func (m *SetTopicStreamForceConfReq) String() string { return proto.CompactTextString(m) }
func (*SetTopicStreamForceConfReq) ProtoMessage()    {}
func (*SetTopicStreamForceConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{130}
}
func (m *SetTopicStreamForceConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetTopicStreamForceConfReq.Unmarshal(m, b)
}
func (m *SetTopicStreamForceConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetTopicStreamForceConfReq.Marshal(b, m, deterministic)
}
func (dst *SetTopicStreamForceConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetTopicStreamForceConfReq.Merge(dst, src)
}
func (m *SetTopicStreamForceConfReq) XXX_Size() int {
	return xxx_messageInfo_SetTopicStreamForceConfReq.Size(m)
}
func (m *SetTopicStreamForceConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetTopicStreamForceConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetTopicStreamForceConfReq proto.InternalMessageInfo

func (m *SetTopicStreamForceConfReq) GetConf() *TopicStreamForceConf {
	if m != nil {
		return m.Conf
	}
	return nil
}

type SetTopicStreamForceConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetTopicStreamForceConfResp) Reset()         { *m = SetTopicStreamForceConfResp{} }
func (m *SetTopicStreamForceConfResp) String() string { return proto.CompactTextString(m) }
func (*SetTopicStreamForceConfResp) ProtoMessage()    {}
func (*SetTopicStreamForceConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{131}
}
func (m *SetTopicStreamForceConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetTopicStreamForceConfResp.Unmarshal(m, b)
}
func (m *SetTopicStreamForceConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetTopicStreamForceConfResp.Marshal(b, m, deterministic)
}
func (dst *SetTopicStreamForceConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetTopicStreamForceConfResp.Merge(dst, src)
}
func (m *SetTopicStreamForceConfResp) XXX_Size() int {
	return xxx_messageInfo_SetTopicStreamForceConfResp.Size(m)
}
func (m *SetTopicStreamForceConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetTopicStreamForceConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetTopicStreamForceConfResp proto.InternalMessageInfo

type GetTopicStreamForceConfListReq struct {
	Offset               uint32                     `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32                     `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	TopicId              string                     `protobuf:"bytes,3,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	PostId               string                     `protobuf:"bytes,4,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Status               TopicStreamForceConfStatus `protobuf:"varint,5,opt,name=status,proto3,enum=ugc.topic.TopicStreamForceConfStatus" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetTopicStreamForceConfListReq) Reset()         { *m = GetTopicStreamForceConfListReq{} }
func (m *GetTopicStreamForceConfListReq) String() string { return proto.CompactTextString(m) }
func (*GetTopicStreamForceConfListReq) ProtoMessage()    {}
func (*GetTopicStreamForceConfListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{132}
}
func (m *GetTopicStreamForceConfListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicStreamForceConfListReq.Unmarshal(m, b)
}
func (m *GetTopicStreamForceConfListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicStreamForceConfListReq.Marshal(b, m, deterministic)
}
func (dst *GetTopicStreamForceConfListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicStreamForceConfListReq.Merge(dst, src)
}
func (m *GetTopicStreamForceConfListReq) XXX_Size() int {
	return xxx_messageInfo_GetTopicStreamForceConfListReq.Size(m)
}
func (m *GetTopicStreamForceConfListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicStreamForceConfListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicStreamForceConfListReq proto.InternalMessageInfo

func (m *GetTopicStreamForceConfListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetTopicStreamForceConfListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetTopicStreamForceConfListReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *GetTopicStreamForceConfListReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *GetTopicStreamForceConfListReq) GetStatus() TopicStreamForceConfStatus {
	if m != nil {
		return m.Status
	}
	return TopicStreamForceConfStatus_TopicStreamForceConfStatusAll
}

type GetTopicStreamForceConfListResp struct {
	ConfList             []*TopicStreamForceConf `protobuf:"bytes,1,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	Total                uint32                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetTopicStreamForceConfListResp) Reset()         { *m = GetTopicStreamForceConfListResp{} }
func (m *GetTopicStreamForceConfListResp) String() string { return proto.CompactTextString(m) }
func (*GetTopicStreamForceConfListResp) ProtoMessage()    {}
func (*GetTopicStreamForceConfListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{133}
}
func (m *GetTopicStreamForceConfListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicStreamForceConfListResp.Unmarshal(m, b)
}
func (m *GetTopicStreamForceConfListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicStreamForceConfListResp.Marshal(b, m, deterministic)
}
func (dst *GetTopicStreamForceConfListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicStreamForceConfListResp.Merge(dst, src)
}
func (m *GetTopicStreamForceConfListResp) XXX_Size() int {
	return xxx_messageInfo_GetTopicStreamForceConfListResp.Size(m)
}
func (m *GetTopicStreamForceConfListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicStreamForceConfListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicStreamForceConfListResp proto.InternalMessageInfo

func (m *GetTopicStreamForceConfListResp) GetConfList() []*TopicStreamForceConf {
	if m != nil {
		return m.ConfList
	}
	return nil
}

func (m *GetTopicStreamForceConfListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetTopicStreamForceActivePostListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TopicId              string   `protobuf:"bytes,2,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTopicStreamForceActivePostListReq) Reset()         { *m = GetTopicStreamForceActivePostListReq{} }
func (m *GetTopicStreamForceActivePostListReq) String() string { return proto.CompactTextString(m) }
func (*GetTopicStreamForceActivePostListReq) ProtoMessage()    {}
func (*GetTopicStreamForceActivePostListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{134}
}
func (m *GetTopicStreamForceActivePostListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicStreamForceActivePostListReq.Unmarshal(m, b)
}
func (m *GetTopicStreamForceActivePostListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicStreamForceActivePostListReq.Marshal(b, m, deterministic)
}
func (dst *GetTopicStreamForceActivePostListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicStreamForceActivePostListReq.Merge(dst, src)
}
func (m *GetTopicStreamForceActivePostListReq) XXX_Size() int {
	return xxx_messageInfo_GetTopicStreamForceActivePostListReq.Size(m)
}
func (m *GetTopicStreamForceActivePostListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicStreamForceActivePostListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicStreamForceActivePostListReq proto.InternalMessageInfo

func (m *GetTopicStreamForceActivePostListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetTopicStreamForceActivePostListReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

type GetTopicStreamForceActivePostListResp struct {
	PostList             []*GetTopicStreamForceActivePostListResp_Post `protobuf:"bytes,1,rep,name=post_list,json=postList,proto3" json:"post_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                      `json:"-"`
	XXX_unrecognized     []byte                                        `json:"-"`
	XXX_sizecache        int32                                         `json:"-"`
}

func (m *GetTopicStreamForceActivePostListResp) Reset()         { *m = GetTopicStreamForceActivePostListResp{} }
func (m *GetTopicStreamForceActivePostListResp) String() string { return proto.CompactTextString(m) }
func (*GetTopicStreamForceActivePostListResp) ProtoMessage()    {}
func (*GetTopicStreamForceActivePostListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{135}
}
func (m *GetTopicStreamForceActivePostListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicStreamForceActivePostListResp.Unmarshal(m, b)
}
func (m *GetTopicStreamForceActivePostListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicStreamForceActivePostListResp.Marshal(b, m, deterministic)
}
func (dst *GetTopicStreamForceActivePostListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicStreamForceActivePostListResp.Merge(dst, src)
}
func (m *GetTopicStreamForceActivePostListResp) XXX_Size() int {
	return xxx_messageInfo_GetTopicStreamForceActivePostListResp.Size(m)
}
func (m *GetTopicStreamForceActivePostListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicStreamForceActivePostListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicStreamForceActivePostListResp proto.InternalMessageInfo

func (m *GetTopicStreamForceActivePostListResp) GetPostList() []*GetTopicStreamForceActivePostListResp_Post {
	if m != nil {
		return m.PostList
	}
	return nil
}

type GetTopicStreamForceActivePostListResp_Post struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Pos                  uint32   `protobuf:"varint,2,opt,name=pos,proto3" json:"pos,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTopicStreamForceActivePostListResp_Post) Reset() {
	*m = GetTopicStreamForceActivePostListResp_Post{}
}
func (m *GetTopicStreamForceActivePostListResp_Post) String() string {
	return proto.CompactTextString(m)
}
func (*GetTopicStreamForceActivePostListResp_Post) ProtoMessage() {}
func (*GetTopicStreamForceActivePostListResp_Post) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{135, 0}
}
func (m *GetTopicStreamForceActivePostListResp_Post) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicStreamForceActivePostListResp_Post.Unmarshal(m, b)
}
func (m *GetTopicStreamForceActivePostListResp_Post) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicStreamForceActivePostListResp_Post.Marshal(b, m, deterministic)
}
func (dst *GetTopicStreamForceActivePostListResp_Post) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicStreamForceActivePostListResp_Post.Merge(dst, src)
}
func (m *GetTopicStreamForceActivePostListResp_Post) XXX_Size() int {
	return xxx_messageInfo_GetTopicStreamForceActivePostListResp_Post.Size(m)
}
func (m *GetTopicStreamForceActivePostListResp_Post) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicStreamForceActivePostListResp_Post.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicStreamForceActivePostListResp_Post proto.InternalMessageInfo

func (m *GetTopicStreamForceActivePostListResp_Post) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *GetTopicStreamForceActivePostListResp_Post) GetPos() uint32 {
	if m != nil {
		return m.Pos
	}
	return 0
}

type ReportTopicFeedbackReq struct {
	ReportUid            uint32   `protobuf:"varint,1,opt,name=report_uid,json=reportUid,proto3" json:"report_uid,omitempty"`
	PostId               string   `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	TopicId              string   `protobuf:"bytes,3,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportTopicFeedbackReq) Reset()         { *m = ReportTopicFeedbackReq{} }
func (m *ReportTopicFeedbackReq) String() string { return proto.CompactTextString(m) }
func (*ReportTopicFeedbackReq) ProtoMessage()    {}
func (*ReportTopicFeedbackReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{136}
}
func (m *ReportTopicFeedbackReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportTopicFeedbackReq.Unmarshal(m, b)
}
func (m *ReportTopicFeedbackReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportTopicFeedbackReq.Marshal(b, m, deterministic)
}
func (dst *ReportTopicFeedbackReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportTopicFeedbackReq.Merge(dst, src)
}
func (m *ReportTopicFeedbackReq) XXX_Size() int {
	return xxx_messageInfo_ReportTopicFeedbackReq.Size(m)
}
func (m *ReportTopicFeedbackReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportTopicFeedbackReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportTopicFeedbackReq proto.InternalMessageInfo

func (m *ReportTopicFeedbackReq) GetReportUid() uint32 {
	if m != nil {
		return m.ReportUid
	}
	return 0
}

func (m *ReportTopicFeedbackReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *ReportTopicFeedbackReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

type ReportTopicFeedbackResp struct {
	ReportResult         ReportTopicFeedbackResp_ReportResult `protobuf:"varint,1,opt,name=report_result,json=reportResult,proto3,enum=ugc.topic.ReportTopicFeedbackResp_ReportResult" json:"report_result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                             `json:"-"`
	XXX_unrecognized     []byte                               `json:"-"`
	XXX_sizecache        int32                                `json:"-"`
}

func (m *ReportTopicFeedbackResp) Reset()         { *m = ReportTopicFeedbackResp{} }
func (m *ReportTopicFeedbackResp) String() string { return proto.CompactTextString(m) }
func (*ReportTopicFeedbackResp) ProtoMessage()    {}
func (*ReportTopicFeedbackResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{137}
}
func (m *ReportTopicFeedbackResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportTopicFeedbackResp.Unmarshal(m, b)
}
func (m *ReportTopicFeedbackResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportTopicFeedbackResp.Marshal(b, m, deterministic)
}
func (dst *ReportTopicFeedbackResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportTopicFeedbackResp.Merge(dst, src)
}
func (m *ReportTopicFeedbackResp) XXX_Size() int {
	return xxx_messageInfo_ReportTopicFeedbackResp.Size(m)
}
func (m *ReportTopicFeedbackResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportTopicFeedbackResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportTopicFeedbackResp proto.InternalMessageInfo

func (m *ReportTopicFeedbackResp) GetReportResult() ReportTopicFeedbackResp_ReportResult {
	if m != nil {
		return m.ReportResult
	}
	return ReportTopicFeedbackResp_Success
}

type GetTopicFeedbacksReq struct {
	ReportUid            uint32   `protobuf:"varint,1,opt,name=report_uid,json=reportUid,proto3" json:"report_uid,omitempty"`
	TopicId              string   `protobuf:"bytes,2,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTopicFeedbacksReq) Reset()         { *m = GetTopicFeedbacksReq{} }
func (m *GetTopicFeedbacksReq) String() string { return proto.CompactTextString(m) }
func (*GetTopicFeedbacksReq) ProtoMessage()    {}
func (*GetTopicFeedbacksReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{138}
}
func (m *GetTopicFeedbacksReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicFeedbacksReq.Unmarshal(m, b)
}
func (m *GetTopicFeedbacksReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicFeedbacksReq.Marshal(b, m, deterministic)
}
func (dst *GetTopicFeedbacksReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicFeedbacksReq.Merge(dst, src)
}
func (m *GetTopicFeedbacksReq) XXX_Size() int {
	return xxx_messageInfo_GetTopicFeedbacksReq.Size(m)
}
func (m *GetTopicFeedbacksReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicFeedbacksReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicFeedbacksReq proto.InternalMessageInfo

func (m *GetTopicFeedbacksReq) GetReportUid() uint32 {
	if m != nil {
		return m.ReportUid
	}
	return 0
}

func (m *GetTopicFeedbacksReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

type GetTopicFeedbacksResp struct {
	FeedbackList         []*GetTopicFeedbacksResp_FeedbackInfo `protobuf:"bytes,1,rep,name=feedback_list,json=feedbackList,proto3" json:"feedback_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                              `json:"-"`
	XXX_unrecognized     []byte                                `json:"-"`
	XXX_sizecache        int32                                 `json:"-"`
}

func (m *GetTopicFeedbacksResp) Reset()         { *m = GetTopicFeedbacksResp{} }
func (m *GetTopicFeedbacksResp) String() string { return proto.CompactTextString(m) }
func (*GetTopicFeedbacksResp) ProtoMessage()    {}
func (*GetTopicFeedbacksResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{139}
}
func (m *GetTopicFeedbacksResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicFeedbacksResp.Unmarshal(m, b)
}
func (m *GetTopicFeedbacksResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicFeedbacksResp.Marshal(b, m, deterministic)
}
func (dst *GetTopicFeedbacksResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicFeedbacksResp.Merge(dst, src)
}
func (m *GetTopicFeedbacksResp) XXX_Size() int {
	return xxx_messageInfo_GetTopicFeedbacksResp.Size(m)
}
func (m *GetTopicFeedbacksResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicFeedbacksResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicFeedbacksResp proto.InternalMessageInfo

func (m *GetTopicFeedbacksResp) GetFeedbackList() []*GetTopicFeedbacksResp_FeedbackInfo {
	if m != nil {
		return m.FeedbackList
	}
	return nil
}

type GetTopicFeedbacksResp_FeedbackInfo struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTopicFeedbacksResp_FeedbackInfo) Reset()         { *m = GetTopicFeedbacksResp_FeedbackInfo{} }
func (m *GetTopicFeedbacksResp_FeedbackInfo) String() string { return proto.CompactTextString(m) }
func (*GetTopicFeedbacksResp_FeedbackInfo) ProtoMessage()    {}
func (*GetTopicFeedbacksResp_FeedbackInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{139, 0}
}
func (m *GetTopicFeedbacksResp_FeedbackInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicFeedbacksResp_FeedbackInfo.Unmarshal(m, b)
}
func (m *GetTopicFeedbacksResp_FeedbackInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicFeedbacksResp_FeedbackInfo.Marshal(b, m, deterministic)
}
func (dst *GetTopicFeedbacksResp_FeedbackInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicFeedbacksResp_FeedbackInfo.Merge(dst, src)
}
func (m *GetTopicFeedbacksResp_FeedbackInfo) XXX_Size() int {
	return xxx_messageInfo_GetTopicFeedbacksResp_FeedbackInfo.Size(m)
}
func (m *GetTopicFeedbacksResp_FeedbackInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicFeedbacksResp_FeedbackInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicFeedbacksResp_FeedbackInfo proto.InternalMessageInfo

func (m *GetTopicFeedbacksResp_FeedbackInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

type GetStepOnTopicIdsReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStepOnTopicIdsReq) Reset()         { *m = GetStepOnTopicIdsReq{} }
func (m *GetStepOnTopicIdsReq) String() string { return proto.CompactTextString(m) }
func (*GetStepOnTopicIdsReq) ProtoMessage()    {}
func (*GetStepOnTopicIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{140}
}
func (m *GetStepOnTopicIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStepOnTopicIdsReq.Unmarshal(m, b)
}
func (m *GetStepOnTopicIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStepOnTopicIdsReq.Marshal(b, m, deterministic)
}
func (dst *GetStepOnTopicIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStepOnTopicIdsReq.Merge(dst, src)
}
func (m *GetStepOnTopicIdsReq) XXX_Size() int {
	return xxx_messageInfo_GetStepOnTopicIdsReq.Size(m)
}
func (m *GetStepOnTopicIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStepOnTopicIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStepOnTopicIdsReq proto.InternalMessageInfo

type GetStepOnTopicIdsResp struct {
	StepOnTopicIds       []string `protobuf:"bytes,1,rep,name=step_on_topic_ids,json=stepOnTopicIds,proto3" json:"step_on_topic_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStepOnTopicIdsResp) Reset()         { *m = GetStepOnTopicIdsResp{} }
func (m *GetStepOnTopicIdsResp) String() string { return proto.CompactTextString(m) }
func (*GetStepOnTopicIdsResp) ProtoMessage()    {}
func (*GetStepOnTopicIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{141}
}
func (m *GetStepOnTopicIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStepOnTopicIdsResp.Unmarshal(m, b)
}
func (m *GetStepOnTopicIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStepOnTopicIdsResp.Marshal(b, m, deterministic)
}
func (dst *GetStepOnTopicIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStepOnTopicIdsResp.Merge(dst, src)
}
func (m *GetStepOnTopicIdsResp) XXX_Size() int {
	return xxx_messageInfo_GetStepOnTopicIdsResp.Size(m)
}
func (m *GetStepOnTopicIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStepOnTopicIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStepOnTopicIdsResp proto.InternalMessageInfo

func (m *GetStepOnTopicIdsResp) GetStepOnTopicIds() []string {
	if m != nil {
		return m.StepOnTopicIds
	}
	return nil
}

type BatchGetTopicFollowCountReq struct {
	TopicId              []string `protobuf:"bytes,1,rep,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetTopicFollowCountReq) Reset()         { *m = BatchGetTopicFollowCountReq{} }
func (m *BatchGetTopicFollowCountReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetTopicFollowCountReq) ProtoMessage()    {}
func (*BatchGetTopicFollowCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{142}
}
func (m *BatchGetTopicFollowCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetTopicFollowCountReq.Unmarshal(m, b)
}
func (m *BatchGetTopicFollowCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetTopicFollowCountReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetTopicFollowCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetTopicFollowCountReq.Merge(dst, src)
}
func (m *BatchGetTopicFollowCountReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetTopicFollowCountReq.Size(m)
}
func (m *BatchGetTopicFollowCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetTopicFollowCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetTopicFollowCountReq proto.InternalMessageInfo

func (m *BatchGetTopicFollowCountReq) GetTopicId() []string {
	if m != nil {
		return m.TopicId
	}
	return nil
}

type BatchGetTopicFollowCountResp struct {
	MapList              map[string]uint32 `protobuf:"bytes,1,rep,name=map_list,json=mapList,proto3" json:"map_list,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchGetTopicFollowCountResp) Reset()         { *m = BatchGetTopicFollowCountResp{} }
func (m *BatchGetTopicFollowCountResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetTopicFollowCountResp) ProtoMessage()    {}
func (*BatchGetTopicFollowCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{143}
}
func (m *BatchGetTopicFollowCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetTopicFollowCountResp.Unmarshal(m, b)
}
func (m *BatchGetTopicFollowCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetTopicFollowCountResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetTopicFollowCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetTopicFollowCountResp.Merge(dst, src)
}
func (m *BatchGetTopicFollowCountResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetTopicFollowCountResp.Size(m)
}
func (m *BatchGetTopicFollowCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetTopicFollowCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetTopicFollowCountResp proto.InternalMessageInfo

func (m *BatchGetTopicFollowCountResp) GetMapList() map[string]uint32 {
	if m != nil {
		return m.MapList
	}
	return nil
}

// 发帖按钮引导配置 运营后台
type PostButtonInfo struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Url                  string   `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	TopicText            string   `protobuf:"bytes,3,opt,name=topic_text,json=topicText,proto3" json:"topic_text,omitempty"`
	RewardText           string   `protobuf:"bytes,4,opt,name=reward_text,json=rewardText,proto3" json:"reward_text,omitempty"`
	RelationTopic        string   `protobuf:"bytes,5,opt,name=relation_topic,json=relationTopic,proto3" json:"relation_topic,omitempty"`
	StartTime            uint32   `protobuf:"varint,6,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,7,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Status               uint32   `protobuf:"varint,8,opt,name=status,proto3" json:"status,omitempty"`
	OperateTime          uint32   `protobuf:"varint,9,opt,name=operate_time,json=operateTime,proto3" json:"operate_time,omitempty"`
	Operator             string   `protobuf:"bytes,10,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PostButtonInfo) Reset()         { *m = PostButtonInfo{} }
func (m *PostButtonInfo) String() string { return proto.CompactTextString(m) }
func (*PostButtonInfo) ProtoMessage()    {}
func (*PostButtonInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{144}
}
func (m *PostButtonInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostButtonInfo.Unmarshal(m, b)
}
func (m *PostButtonInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostButtonInfo.Marshal(b, m, deterministic)
}
func (dst *PostButtonInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostButtonInfo.Merge(dst, src)
}
func (m *PostButtonInfo) XXX_Size() int {
	return xxx_messageInfo_PostButtonInfo.Size(m)
}
func (m *PostButtonInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PostButtonInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PostButtonInfo proto.InternalMessageInfo

func (m *PostButtonInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *PostButtonInfo) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *PostButtonInfo) GetTopicText() string {
	if m != nil {
		return m.TopicText
	}
	return ""
}

func (m *PostButtonInfo) GetRewardText() string {
	if m != nil {
		return m.RewardText
	}
	return ""
}

func (m *PostButtonInfo) GetRelationTopic() string {
	if m != nil {
		return m.RelationTopic
	}
	return ""
}

func (m *PostButtonInfo) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *PostButtonInfo) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *PostButtonInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *PostButtonInfo) GetOperateTime() uint32 {
	if m != nil {
		return m.OperateTime
	}
	return 0
}

func (m *PostButtonInfo) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type SetPostButtonConfigReq struct {
	Info                 *PostButtonInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *SetPostButtonConfigReq) Reset()         { *m = SetPostButtonConfigReq{} }
func (m *SetPostButtonConfigReq) String() string { return proto.CompactTextString(m) }
func (*SetPostButtonConfigReq) ProtoMessage()    {}
func (*SetPostButtonConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{145}
}
func (m *SetPostButtonConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPostButtonConfigReq.Unmarshal(m, b)
}
func (m *SetPostButtonConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPostButtonConfigReq.Marshal(b, m, deterministic)
}
func (dst *SetPostButtonConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPostButtonConfigReq.Merge(dst, src)
}
func (m *SetPostButtonConfigReq) XXX_Size() int {
	return xxx_messageInfo_SetPostButtonConfigReq.Size(m)
}
func (m *SetPostButtonConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPostButtonConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetPostButtonConfigReq proto.InternalMessageInfo

func (m *SetPostButtonConfigReq) GetInfo() *PostButtonInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type SetPostButtonConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPostButtonConfigResp) Reset()         { *m = SetPostButtonConfigResp{} }
func (m *SetPostButtonConfigResp) String() string { return proto.CompactTextString(m) }
func (*SetPostButtonConfigResp) ProtoMessage()    {}
func (*SetPostButtonConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{146}
}
func (m *SetPostButtonConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPostButtonConfigResp.Unmarshal(m, b)
}
func (m *SetPostButtonConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPostButtonConfigResp.Marshal(b, m, deterministic)
}
func (dst *SetPostButtonConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPostButtonConfigResp.Merge(dst, src)
}
func (m *SetPostButtonConfigResp) XXX_Size() int {
	return xxx_messageInfo_SetPostButtonConfigResp.Size(m)
}
func (m *SetPostButtonConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPostButtonConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetPostButtonConfigResp proto.InternalMessageInfo

type GetPostButtonConfigsReq struct {
	Status               PostButtonConfigStatus `protobuf:"varint,1,opt,name=status,proto3,enum=ugc.topic.PostButtonConfigStatus" json:"status,omitempty"`
	RelationTopic        string                 `protobuf:"bytes,2,opt,name=relation_topic,json=relationTopic,proto3" json:"relation_topic,omitempty"`
	Limit                uint32                 `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	Offset               uint32                 `protobuf:"varint,4,opt,name=offset,proto3" json:"offset,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetPostButtonConfigsReq) Reset()         { *m = GetPostButtonConfigsReq{} }
func (m *GetPostButtonConfigsReq) String() string { return proto.CompactTextString(m) }
func (*GetPostButtonConfigsReq) ProtoMessage()    {}
func (*GetPostButtonConfigsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{147}
}
func (m *GetPostButtonConfigsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPostButtonConfigsReq.Unmarshal(m, b)
}
func (m *GetPostButtonConfigsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPostButtonConfigsReq.Marshal(b, m, deterministic)
}
func (dst *GetPostButtonConfigsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPostButtonConfigsReq.Merge(dst, src)
}
func (m *GetPostButtonConfigsReq) XXX_Size() int {
	return xxx_messageInfo_GetPostButtonConfigsReq.Size(m)
}
func (m *GetPostButtonConfigsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPostButtonConfigsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPostButtonConfigsReq proto.InternalMessageInfo

func (m *GetPostButtonConfigsReq) GetStatus() PostButtonConfigStatus {
	if m != nil {
		return m.Status
	}
	return PostButtonConfigStatus_Post_Button_Config_None
}

func (m *GetPostButtonConfigsReq) GetRelationTopic() string {
	if m != nil {
		return m.RelationTopic
	}
	return ""
}

func (m *GetPostButtonConfigsReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetPostButtonConfigsReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

type GetPostButtonConfigsResp struct {
	Info                 []*PostButtonInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	Total                uint32            `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetPostButtonConfigsResp) Reset()         { *m = GetPostButtonConfigsResp{} }
func (m *GetPostButtonConfigsResp) String() string { return proto.CompactTextString(m) }
func (*GetPostButtonConfigsResp) ProtoMessage()    {}
func (*GetPostButtonConfigsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{148}
}
func (m *GetPostButtonConfigsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPostButtonConfigsResp.Unmarshal(m, b)
}
func (m *GetPostButtonConfigsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPostButtonConfigsResp.Marshal(b, m, deterministic)
}
func (dst *GetPostButtonConfigsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPostButtonConfigsResp.Merge(dst, src)
}
func (m *GetPostButtonConfigsResp) XXX_Size() int {
	return xxx_messageInfo_GetPostButtonConfigsResp.Size(m)
}
func (m *GetPostButtonConfigsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPostButtonConfigsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPostButtonConfigsResp proto.InternalMessageInfo

func (m *GetPostButtonConfigsResp) GetInfo() []*PostButtonInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *GetPostButtonConfigsResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type OfflinePostButtonConfigReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OfflinePostButtonConfigReq) Reset()         { *m = OfflinePostButtonConfigReq{} }
func (m *OfflinePostButtonConfigReq) String() string { return proto.CompactTextString(m) }
func (*OfflinePostButtonConfigReq) ProtoMessage()    {}
func (*OfflinePostButtonConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{149}
}
func (m *OfflinePostButtonConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfflinePostButtonConfigReq.Unmarshal(m, b)
}
func (m *OfflinePostButtonConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfflinePostButtonConfigReq.Marshal(b, m, deterministic)
}
func (dst *OfflinePostButtonConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfflinePostButtonConfigReq.Merge(dst, src)
}
func (m *OfflinePostButtonConfigReq) XXX_Size() int {
	return xxx_messageInfo_OfflinePostButtonConfigReq.Size(m)
}
func (m *OfflinePostButtonConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_OfflinePostButtonConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_OfflinePostButtonConfigReq proto.InternalMessageInfo

func (m *OfflinePostButtonConfigReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type OfflinePostButtonConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OfflinePostButtonConfigResp) Reset()         { *m = OfflinePostButtonConfigResp{} }
func (m *OfflinePostButtonConfigResp) String() string { return proto.CompactTextString(m) }
func (*OfflinePostButtonConfigResp) ProtoMessage()    {}
func (*OfflinePostButtonConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{150}
}
func (m *OfflinePostButtonConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfflinePostButtonConfigResp.Unmarshal(m, b)
}
func (m *OfflinePostButtonConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfflinePostButtonConfigResp.Marshal(b, m, deterministic)
}
func (dst *OfflinePostButtonConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfflinePostButtonConfigResp.Merge(dst, src)
}
func (m *OfflinePostButtonConfigResp) XXX_Size() int {
	return xxx_messageInfo_OfflinePostButtonConfigResp.Size(m)
}
func (m *OfflinePostButtonConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_OfflinePostButtonConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_OfflinePostButtonConfigResp proto.InternalMessageInfo

type GetLastedPostButtonConfigReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLastedPostButtonConfigReq) Reset()         { *m = GetLastedPostButtonConfigReq{} }
func (m *GetLastedPostButtonConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetLastedPostButtonConfigReq) ProtoMessage()    {}
func (*GetLastedPostButtonConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{151}
}
func (m *GetLastedPostButtonConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLastedPostButtonConfigReq.Unmarshal(m, b)
}
func (m *GetLastedPostButtonConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLastedPostButtonConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetLastedPostButtonConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLastedPostButtonConfigReq.Merge(dst, src)
}
func (m *GetLastedPostButtonConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetLastedPostButtonConfigReq.Size(m)
}
func (m *GetLastedPostButtonConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLastedPostButtonConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLastedPostButtonConfigReq proto.InternalMessageInfo

type GetLastedPostButtonConfigResp struct {
	TopicName            string   `protobuf:"bytes,1,opt,name=topic_name,json=topicName,proto3" json:"topic_name,omitempty"`
	Url                  string   `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	TopicText            string   `protobuf:"bytes,3,opt,name=topic_text,json=topicText,proto3" json:"topic_text,omitempty"`
	RewardText           string   `protobuf:"bytes,4,opt,name=reward_text,json=rewardText,proto3" json:"reward_text,omitempty"`
	RelationTopic        string   `protobuf:"bytes,5,opt,name=relation_topic,json=relationTopic,proto3" json:"relation_topic,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLastedPostButtonConfigResp) Reset()         { *m = GetLastedPostButtonConfigResp{} }
func (m *GetLastedPostButtonConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetLastedPostButtonConfigResp) ProtoMessage()    {}
func (*GetLastedPostButtonConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_58f2997e06b41df1, []int{152}
}
func (m *GetLastedPostButtonConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLastedPostButtonConfigResp.Unmarshal(m, b)
}
func (m *GetLastedPostButtonConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLastedPostButtonConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetLastedPostButtonConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLastedPostButtonConfigResp.Merge(dst, src)
}
func (m *GetLastedPostButtonConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetLastedPostButtonConfigResp.Size(m)
}
func (m *GetLastedPostButtonConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLastedPostButtonConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLastedPostButtonConfigResp proto.InternalMessageInfo

func (m *GetLastedPostButtonConfigResp) GetTopicName() string {
	if m != nil {
		return m.TopicName
	}
	return ""
}

func (m *GetLastedPostButtonConfigResp) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *GetLastedPostButtonConfigResp) GetTopicText() string {
	if m != nil {
		return m.TopicText
	}
	return ""
}

func (m *GetLastedPostButtonConfigResp) GetRewardText() string {
	if m != nil {
		return m.RewardText
	}
	return ""
}

func (m *GetLastedPostButtonConfigResp) GetRelationTopic() string {
	if m != nil {
		return m.RelationTopic
	}
	return ""
}

func init() {
	proto.RegisterType((*TopicBindGameInfo)(nil), "ugc.topic.TopicBindGameInfo")
	proto.RegisterType((*CreateTopicReq)(nil), "ugc.topic.CreateTopicReq")
	proto.RegisterType((*CreateTopicResp)(nil), "ugc.topic.CreateTopicResp")
	proto.RegisterType((*EnableTopicReq)(nil), "ugc.topic.EnableTopicReq")
	proto.RegisterType((*EnableTopicResp)(nil), "ugc.topic.EnableTopicResp")
	proto.RegisterType((*UpdateTopicInfoReq)(nil), "ugc.topic.UpdateTopicInfoReq")
	proto.RegisterType((*UpdateTopicInfoResp)(nil), "ugc.topic.UpdateTopicInfoResp")
	proto.RegisterType((*DIY2OfficialTopicReq)(nil), "ugc.topic.DIY2OfficialTopicReq")
	proto.RegisterType((*DIY2OfficialTopicResp)(nil), "ugc.topic.DIY2OfficialTopicResp")
	proto.RegisterType((*BatUpdateWeightReq)(nil), "ugc.topic.BatUpdateWeightReq")
	proto.RegisterType((*BatUpdateWeightResp)(nil), "ugc.topic.BatUpdateWeightResp")
	proto.RegisterType((*BatGetAllWeightTopicReq)(nil), "ugc.topic.BatGetAllWeightTopicReq")
	proto.RegisterType((*TopicWithWeight)(nil), "ugc.topic.TopicWithWeight")
	proto.RegisterType((*BatGetAllWeightTopicResp)(nil), "ugc.topic.BatGetAllWeightTopicResp")
	proto.RegisterType((*TopicRecommendInfo)(nil), "ugc.topic.TopicRecommendInfo")
	proto.RegisterType((*UpdatePutTopicInRecommendConfReq)(nil), "ugc.topic.UpdatePutTopicInRecommendConfReq")
	proto.RegisterType((*UpdatePutTopicInRecommendConfResp)(nil), "ugc.topic.UpdatePutTopicInRecommendConfResp")
	proto.RegisterType((*DelPutTopicInRecommendConfReq)(nil), "ugc.topic.DelPutTopicInRecommendConfReq")
	proto.RegisterType((*DelPutTopicInRecommendConfResp)(nil), "ugc.topic.DelPutTopicInRecommendConfResp")
	proto.RegisterType((*GetPutTopicInRecommendConfReq)(nil), "ugc.topic.GetPutTopicInRecommendConfReq")
	proto.RegisterType((*GetPutTopicInRecommendConfResp)(nil), "ugc.topic.GetPutTopicInRecommendConfResp")
	proto.RegisterType((*TopicAndSubscript)(nil), "ugc.topic.TopicAndSubscript")
	proto.RegisterType((*CatalogTopicInRcmdFeed)(nil), "ugc.topic.CatalogTopicInRcmdFeed")
	proto.RegisterType((*UpdateCatalogTopicInRcmdFeedReq)(nil), "ugc.topic.UpdateCatalogTopicInRcmdFeedReq")
	proto.RegisterType((*UpdateCatalogTopicInRcmdFeedResp)(nil), "ugc.topic.UpdateCatalogTopicInRcmdFeedResp")
	proto.RegisterType((*DelCatalogTopicInRcmdFeedReq)(nil), "ugc.topic.DelCatalogTopicInRcmdFeedReq")
	proto.RegisterType((*DelCatalogTopicInRcmdFeedResp)(nil), "ugc.topic.DelCatalogTopicInRcmdFeedResp")
	proto.RegisterType((*GetCatalogTopicInRcmdFeedReq)(nil), "ugc.topic.GetCatalogTopicInRcmdFeedReq")
	proto.RegisterType((*GetCatalogTopicInRcmdFeedResp)(nil), "ugc.topic.GetCatalogTopicInRcmdFeedResp")
	proto.RegisterType((*GetTopicsInRecommendReq)(nil), "ugc.topic.GetTopicsInRecommendReq")
	proto.RegisterType((*IndexTopicConfs)(nil), "ugc.topic.IndexTopicConfs")
	proto.RegisterType((*GetTopicsInRecommendResp)(nil), "ugc.topic.GetTopicsInRecommendResp")
	proto.RegisterMapType((map[uint32]*IndexTopicConfs)(nil), "ugc.topic.GetTopicsInRecommendResp.ConfMapEntry")
	proto.RegisterType((*GetTopicRankReq)(nil), "ugc.topic.GetTopicRankReq")
	proto.RegisterType((*GetTopicRankResp)(nil), "ugc.topic.GetTopicRankResp")
	proto.RegisterType((*GetTopicByConvert2OfficialReq)(nil), "ugc.topic.GetTopicByConvert2OfficialReq")
	proto.RegisterType((*GetTopicByConvert2OfficialResp)(nil), "ugc.topic.GetTopicByConvert2OfficialResp")
	proto.RegisterType((*AddUserIDByTopicReq)(nil), "ugc.topic.AddUserIDByTopicReq")
	proto.RegisterType((*AddUserIDByTopicResp)(nil), "ugc.topic.AddUserIDByTopicResp")
	proto.RegisterType((*GetUserIDByTopicReq)(nil), "ugc.topic.GetUserIDByTopicReq")
	proto.RegisterType((*GetUserIDByTopicResp)(nil), "ugc.topic.GetUserIDByTopicResp")
	proto.RegisterType((*GetTopicInfoReq)(nil), "ugc.topic.GetTopicInfoReq")
	proto.RegisterType((*GetTopicInfoResp)(nil), "ugc.topic.GetTopicInfoResp")
	proto.RegisterType((*GetTopicListReq)(nil), "ugc.topic.GetTopicListReq")
	proto.RegisterType((*GeTopicListRelationByTopicReq)(nil), "ugc.topic.GeTopicListRelationByTopicReq")
	proto.RegisterType((*GetTopicListResp)(nil), "ugc.topic.GetTopicListResp")
	proto.RegisterType((*SortTopicReq)(nil), "ugc.topic.SortTopicReq")
	proto.RegisterMapType((map[string]string)(nil), "ugc.topic.SortTopicReq.SortEntry")
	proto.RegisterType((*SortTopicResp)(nil), "ugc.topic.SortTopicResp")
	proto.RegisterType((*DeleteTopicReq)(nil), "ugc.topic.DeleteTopicReq")
	proto.RegisterType((*DeleteTopicResp)(nil), "ugc.topic.DeleteTopicResp")
	proto.RegisterType((*BatDeleteTopicReq)(nil), "ugc.topic.BatDeleteTopicReq")
	proto.RegisterType((*BatDeleteTopicResp)(nil), "ugc.topic.BatDeleteTopicResp")
	proto.RegisterType((*SubscribeTopicReq)(nil), "ugc.topic.SubscribeTopicReq")
	proto.RegisterType((*SubscribeTopicResp)(nil), "ugc.topic.SubscribeTopicResp")
	proto.RegisterType((*UnsubscribeTopicReq)(nil), "ugc.topic.UnsubscribeTopicReq")
	proto.RegisterType((*UnsubscribeTopicResp)(nil), "ugc.topic.UnsubscribeTopicResp")
	proto.RegisterType((*GetSubscribersReq)(nil), "ugc.topic.GetSubscribersReq")
	proto.RegisterType((*GetSubscribersResp)(nil), "ugc.topic.GetSubscribersResp")
	proto.RegisterType((*GetTopicsReq)(nil), "ugc.topic.GetTopicsReq")
	proto.RegisterType((*GetTopicsResp)(nil), "ugc.topic.GetTopicsResp")
	proto.RegisterType((*TopicWeightInfo)(nil), "ugc.topic.TopicWeightInfo")
	proto.RegisterType((*TopicInfo)(nil), "ugc.topic.TopicInfo")
	proto.RegisterType((*RelatedTopic)(nil), "ugc.topic.RelatedTopic")
	proto.RegisterType((*CheckTopicBindUidExistsReq)(nil), "ugc.topic.CheckTopicBindUidExistsReq")
	proto.RegisterType((*CheckTopicBindUidExistsResp)(nil), "ugc.topic.CheckTopicBindUidExistsResp")
	proto.RegisterType((*LoadMore)(nil), "ugc.topic.LoadMore")
	proto.RegisterType((*GetTopicListExcludeSubscribeReq)(nil), "ugc.topic.GetTopicListExcludeSubscribeReq")
	proto.RegisterType((*GetTopicListExcludeSubscribeResp)(nil), "ugc.topic.GetTopicListExcludeSubscribeResp")
	proto.RegisterType((*BatchCheckTopicBindUidReq)(nil), "ugc.topic.BatchCheckTopicBindUidReq")
	proto.RegisterType((*BatchCheckTopicBindUidResp)(nil), "ugc.topic.BatchCheckTopicBindUidResp")
	proto.RegisterMapType((map[uint32]bool)(nil), "ugc.topic.BatchCheckTopicBindUidResp.ResultEntry")
	proto.RegisterType((*UpdateTopicPostCountReq)(nil), "ugc.topic.UpdateTopicPostCountReq")
	proto.RegisterType((*UpdateTopicPostCountResp)(nil), "ugc.topic.UpdateTopicPostCountResp")
	proto.RegisterType((*PostInfo)(nil), "ugc.topic.PostInfo")
	proto.RegisterType((*BatchUpdateTopicPostCountReq)(nil), "ugc.topic.BatchUpdateTopicPostCountReq")
	proto.RegisterType((*BatchUpdateTopicPostCountResp)(nil), "ugc.topic.BatchUpdateTopicPostCountResp")
	proto.RegisterType((*IsSubscribeAllTopicReq)(nil), "ugc.topic.IsSubscribeAllTopicReq")
	proto.RegisterType((*IsSubscribeAllTopicResp)(nil), "ugc.topic.IsSubscribeAllTopicResp")
	proto.RegisterType((*AdjustTopicBelongReq)(nil), "ugc.topic.AdjustTopicBelongReq")
	proto.RegisterType((*AdjustTopicBelongResp)(nil), "ugc.topic.AdjustTopicBelongResp")
	proto.RegisterType((*BatchGetParentTopicInfoReq)(nil), "ugc.topic.BatchGetParentTopicInfoReq")
	proto.RegisterType((*BatchGetParentTopicInfoResp)(nil), "ugc.topic.BatchGetParentTopicInfoResp")
	proto.RegisterMapType((map[string]*TopicInfoes)(nil), "ugc.topic.BatchGetParentTopicInfoResp.ResultsEntry")
	proto.RegisterType((*TopicInfoes)(nil), "ugc.topic.TopicInfoes")
	proto.RegisterType((*SexAgeInfo)(nil), "ugc.topic.SexAgeInfo")
	proto.RegisterType((*PublisherTopicInfo)(nil), "ugc.topic.PublisherTopicInfo")
	proto.RegisterType((*InsertPublisherTopicReq)(nil), "ugc.topic.InsertPublisherTopicReq")
	proto.RegisterType((*InsertPublisherTopicResp)(nil), "ugc.topic.InsertPublisherTopicResp")
	proto.RegisterType((*GetPublisherTopicInfoReq)(nil), "ugc.topic.GetPublisherTopicInfoReq")
	proto.RegisterType((*GetPublisherTopicInfoResp)(nil), "ugc.topic.GetPublisherTopicInfoResp")
	proto.RegisterType((*GetPublisherTopicUidsReq)(nil), "ugc.topic.GetPublisherTopicUidsReq")
	proto.RegisterType((*GetPublisherTopicUidsResp)(nil), "ugc.topic.GetPublisherTopicUidsResp")
	proto.RegisterType((*DeletePublisherTopicReq)(nil), "ugc.topic.DeletePublisherTopicReq")
	proto.RegisterType((*DeletePublisherTopicResp)(nil), "ugc.topic.DeletePublisherTopicResp")
	proto.RegisterType((*GetPublisherTopicByUidReq)(nil), "ugc.topic.GetPublisherTopicByUidReq")
	proto.RegisterType((*GetPublisherTopicByUidResp)(nil), "ugc.topic.GetPublisherTopicByUidResp")
	proto.RegisterType((*GetPublisherTopicListReq)(nil), "ugc.topic.GetPublisherTopicListReq")
	proto.RegisterType((*GetPublisherTopicListResp)(nil), "ugc.topic.GetPublisherTopicListResp")
	proto.RegisterType((*AddTopicStatisticReq)(nil), "ugc.topic.AddTopicStatisticReq")
	proto.RegisterType((*AddTopicStatisticResp)(nil), "ugc.topic.AddTopicStatisticResp")
	proto.RegisterType((*CreateTopicV2Req)(nil), "ugc.topic.CreateTopicV2Req")
	proto.RegisterType((*CreateTopicV2Req_TopicInfo)(nil), "ugc.topic.CreateTopicV2Req.TopicInfo")
	proto.RegisterType((*CreateTopicV2Resp)(nil), "ugc.topic.CreateTopicV2Resp")
	proto.RegisterType((*SearchTopicReq)(nil), "ugc.topic.SearchTopicReq")
	proto.RegisterType((*SearchTopicResp)(nil), "ugc.topic.SearchTopicResp")
	proto.RegisterType((*SearchTopicResp_TopicInfo)(nil), "ugc.topic.SearchTopicResp.TopicInfo")
	proto.RegisterType((*CreateEsTopicReq)(nil), "ugc.topic.CreateEsTopicReq")
	proto.RegisterType((*CreateEsTopicResp)(nil), "ugc.topic.CreateEsTopicResp")
	proto.RegisterType((*ReportTopicViewCountReq)(nil), "ugc.topic.ReportTopicViewCountReq")
	proto.RegisterType((*ReportTopicViewCountRsp)(nil), "ugc.topic.ReportTopicViewCountRsp")
	proto.RegisterType((*TopicRankWeightInfo)(nil), "ugc.topic.TopicRankWeightInfo")
	proto.RegisterType((*UpdateTopicRankWeightReq)(nil), "ugc.topic.UpdateTopicRankWeightReq")
	proto.RegisterType((*UpdateTopicRankWeightResp)(nil), "ugc.topic.UpdateTopicRankWeightResp")
	proto.RegisterType((*GetTopicRankWeightReq)(nil), "ugc.topic.GetTopicRankWeightReq")
	proto.RegisterType((*GetTopicRankWeightResp)(nil), "ugc.topic.GetTopicRankWeightResp")
	proto.RegisterType((*BatGetTopicRankWeightReq)(nil), "ugc.topic.BatGetTopicRankWeightReq")
	proto.RegisterType((*TopicAllRankWeightInfo)(nil), "ugc.topic.TopicAllRankWeightInfo")
	proto.RegisterType((*BatGetTopicRankWeightResp)(nil), "ugc.topic.BatGetTopicRankWeightResp")
	proto.RegisterType((*TopicAdInfo)(nil), "ugc.topic.TopicAdInfo")
	proto.RegisterType((*SetTopicAdReq)(nil), "ugc.topic.SetTopicAdReq")
	proto.RegisterType((*SetTopicAdResp)(nil), "ugc.topic.SetTopicAdResp")
	proto.RegisterType((*GetTopicAdReq)(nil), "ugc.topic.GetTopicAdReq")
	proto.RegisterType((*GetTopicAdResp)(nil), "ugc.topic.GetTopicAdResp")
	proto.RegisterType((*DelTopicAdReq)(nil), "ugc.topic.DelTopicAdReq")
	proto.RegisterType((*DelTopicAdResp)(nil), "ugc.topic.DelTopicAdResp")
	proto.RegisterType((*GetTopicBindGameIDReq)(nil), "ugc.topic.GetTopicBindGameIDReq")
	proto.RegisterType((*TopicInfoBindGame)(nil), "ugc.topic.TopicInfoBindGame")
	proto.RegisterType((*GetTopicBindGameIDResp)(nil), "ugc.topic.GetTopicBindGameIDResp")
	proto.RegisterType((*CreateTopicIDForMoodReq)(nil), "ugc.topic.CreateTopicIDForMoodReq")
	proto.RegisterType((*CreateTopicIDForMoodResp)(nil), "ugc.topic.CreateTopicIDForMoodResp")
	proto.RegisterType((*GetTopicsCacheReq)(nil), "ugc.topic.GetTopicsCacheReq")
	proto.RegisterType((*GetTopicsCacheResp)(nil), "ugc.topic.GetTopicsCacheResp")
	proto.RegisterType((*TopicStreamForceConf)(nil), "ugc.topic.TopicStreamForceConf")
	proto.RegisterType((*DelTopicStreamForceConfReq)(nil), "ugc.topic.DelTopicStreamForceConfReq")
	proto.RegisterType((*DelTopicStreamForceConfResp)(nil), "ugc.topic.DelTopicStreamForceConfResp")
	proto.RegisterType((*SetTopicStreamForceConfReq)(nil), "ugc.topic.SetTopicStreamForceConfReq")
	proto.RegisterType((*SetTopicStreamForceConfResp)(nil), "ugc.topic.SetTopicStreamForceConfResp")
	proto.RegisterType((*GetTopicStreamForceConfListReq)(nil), "ugc.topic.GetTopicStreamForceConfListReq")
	proto.RegisterType((*GetTopicStreamForceConfListResp)(nil), "ugc.topic.GetTopicStreamForceConfListResp")
	proto.RegisterType((*GetTopicStreamForceActivePostListReq)(nil), "ugc.topic.GetTopicStreamForceActivePostListReq")
	proto.RegisterType((*GetTopicStreamForceActivePostListResp)(nil), "ugc.topic.GetTopicStreamForceActivePostListResp")
	proto.RegisterType((*GetTopicStreamForceActivePostListResp_Post)(nil), "ugc.topic.GetTopicStreamForceActivePostListResp.Post")
	proto.RegisterType((*ReportTopicFeedbackReq)(nil), "ugc.topic.ReportTopicFeedbackReq")
	proto.RegisterType((*ReportTopicFeedbackResp)(nil), "ugc.topic.ReportTopicFeedbackResp")
	proto.RegisterType((*GetTopicFeedbacksReq)(nil), "ugc.topic.GetTopicFeedbacksReq")
	proto.RegisterType((*GetTopicFeedbacksResp)(nil), "ugc.topic.GetTopicFeedbacksResp")
	proto.RegisterType((*GetTopicFeedbacksResp_FeedbackInfo)(nil), "ugc.topic.GetTopicFeedbacksResp.FeedbackInfo")
	proto.RegisterType((*GetStepOnTopicIdsReq)(nil), "ugc.topic.GetStepOnTopicIdsReq")
	proto.RegisterType((*GetStepOnTopicIdsResp)(nil), "ugc.topic.GetStepOnTopicIdsResp")
	proto.RegisterType((*BatchGetTopicFollowCountReq)(nil), "ugc.topic.BatchGetTopicFollowCountReq")
	proto.RegisterType((*BatchGetTopicFollowCountResp)(nil), "ugc.topic.BatchGetTopicFollowCountResp")
	proto.RegisterMapType((map[string]uint32)(nil), "ugc.topic.BatchGetTopicFollowCountResp.MapListEntry")
	proto.RegisterType((*PostButtonInfo)(nil), "ugc.topic.PostButtonInfo")
	proto.RegisterType((*SetPostButtonConfigReq)(nil), "ugc.topic.SetPostButtonConfigReq")
	proto.RegisterType((*SetPostButtonConfigResp)(nil), "ugc.topic.SetPostButtonConfigResp")
	proto.RegisterType((*GetPostButtonConfigsReq)(nil), "ugc.topic.GetPostButtonConfigsReq")
	proto.RegisterType((*GetPostButtonConfigsResp)(nil), "ugc.topic.GetPostButtonConfigsResp")
	proto.RegisterType((*OfflinePostButtonConfigReq)(nil), "ugc.topic.OfflinePostButtonConfigReq")
	proto.RegisterType((*OfflinePostButtonConfigResp)(nil), "ugc.topic.OfflinePostButtonConfigResp")
	proto.RegisterType((*GetLastedPostButtonConfigReq)(nil), "ugc.topic.GetLastedPostButtonConfigReq")
	proto.RegisterType((*GetLastedPostButtonConfigResp)(nil), "ugc.topic.GetLastedPostButtonConfigResp")
	proto.RegisterEnum("ugc.topic.TopicQueryOption", TopicQueryOption_name, TopicQueryOption_value)
	proto.RegisterEnum("ugc.topic.TopicQueryOptionEx", TopicQueryOptionEx_name, TopicQueryOptionEx_value)
	proto.RegisterEnum("ugc.topic.TopicType", TopicType_name, TopicType_value)
	proto.RegisterEnum("ugc.topic.TopicSubscript", TopicSubscript_name, TopicSubscript_value)
	proto.RegisterEnum("ugc.topic.TopicPlatform", TopicPlatform_name, TopicPlatform_value)
	proto.RegisterEnum("ugc.topic.PersonAttr", PersonAttr_name, PersonAttr_value)
	proto.RegisterEnum("ugc.topic.TopicGender", TopicGender_name, TopicGender_value)
	proto.RegisterEnum("ugc.topic.TopicInRecommendType", TopicInRecommendType_name, TopicInRecommendType_value)
	proto.RegisterEnum("ugc.topic.WeightStatus", WeightStatus_name, WeightStatus_value)
	proto.RegisterEnum("ugc.topic.DIYSortNDays", DIYSortNDays_name, DIYSortNDays_value)
	proto.RegisterEnum("ugc.topic.PublisherTopicAppid", PublisherTopicAppid_name, PublisherTopicAppid_value)
	proto.RegisterEnum("ugc.topic.PublisherTopicPlatform", PublisherTopicPlatform_name, PublisherTopicPlatform_value)
	proto.RegisterEnum("ugc.topic.UserSex", UserSex_name, UserSex_value)
	proto.RegisterEnum("ugc.topic.PublisherTopicShowUser", PublisherTopicShowUser_name, PublisherTopicShowUser_value)
	proto.RegisterEnum("ugc.topic.PublisherTopicNewInsert", PublisherTopicNewInsert_name, PublisherTopicNewInsert_value)
	proto.RegisterEnum("ugc.topic.AdType", AdType_name, AdType_value)
	proto.RegisterEnum("ugc.topic.TopicConfigCommonStatus", TopicConfigCommonStatus_name, TopicConfigCommonStatus_value)
	proto.RegisterEnum("ugc.topic.TopicStreamForceConfStatus", TopicStreamForceConfStatus_name, TopicStreamForceConfStatus_value)
	proto.RegisterEnum("ugc.topic.PostButtonConfigStatus", PostButtonConfigStatus_name, PostButtonConfigStatus_value)
	proto.RegisterEnum("ugc.topic.ReportTopicFeedbackResp_ReportResult", ReportTopicFeedbackResp_ReportResult_name, ReportTopicFeedbackResp_ReportResult_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// TopicClient is the client API for Topic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type TopicClient interface {
	CreateTopic(ctx context.Context, in *CreateTopicReq, opts ...grpc.CallOption) (*CreateTopicResp, error)
	// 删除
	DeleteTopic(ctx context.Context, in *DeleteTopicReq, opts ...grpc.CallOption) (*DeleteTopicResp, error)
	BatDeleteTopic(ctx context.Context, in *BatDeleteTopicReq, opts ...grpc.CallOption) (*BatDeleteTopicResp, error)
	// 上下线主题
	EnableTopic(ctx context.Context, in *EnableTopicReq, opts ...grpc.CallOption) (*EnableTopicResp, error)
	// 更新主题资料
	UpdateTopicInfo(ctx context.Context, in *UpdateTopicInfoReq, opts ...grpc.CallOption) (*UpdateTopicInfoResp, error)
	// 从自定义修改为官方话题
	DIY2OfficialTopic(ctx context.Context, in *DIY2OfficialTopicReq, opts ...grpc.CallOption) (*DIY2OfficialTopicResp, error)
	// 批量修改提权
	BatUpdateWeight(ctx context.Context, in *BatUpdateWeightReq, opts ...grpc.CallOption) (*BatUpdateWeightResp, error)
	// 批量获取所有提权话题
	BatGetAllWeightTopic(ctx context.Context, in *BatGetAllWeightTopicReq, opts ...grpc.CallOption) (*BatGetAllWeightTopicResp, error)
	// 推荐流插入话题设置
	UpdatePutTopicInRecommendConf(ctx context.Context, in *UpdatePutTopicInRecommendConfReq, opts ...grpc.CallOption) (*UpdatePutTopicInRecommendConfResp, error)
	// 删除推荐流插入话题配置
	DelPutTopicInRecommendConf(ctx context.Context, in *DelPutTopicInRecommendConfReq, opts ...grpc.CallOption) (*DelPutTopicInRecommendConfResp, error)
	// 获取推荐流插入话题配置
	GetPutTopicInRecommendConf(ctx context.Context, in *GetPutTopicInRecommendConfReq, opts ...grpc.CallOption) (*GetPutTopicInRecommendConfResp, error)
	// 更新推荐流插入其他分类话题配置
	UpdateCatalogTopicInRcmdFeed(ctx context.Context, in *UpdateCatalogTopicInRcmdFeedReq, opts ...grpc.CallOption) (*UpdateCatalogTopicInRcmdFeedResp, error)
	// 删除推荐流插入其他分类话题配置
	DelCatalogTopicInRcmdFeed(ctx context.Context, in *DelCatalogTopicInRcmdFeedReq, opts ...grpc.CallOption) (*DelCatalogTopicInRcmdFeedResp, error)
	// 获取推荐流插入其他分类话题配置
	GetCatalogTopicInRcmdFeed(ctx context.Context, in *GetCatalogTopicInRcmdFeedReq, opts ...grpc.CallOption) (*GetCatalogTopicInRcmdFeedResp, error)
	// 获取24小时 3天 7天内 动态量最多的话题
	GetTopicRank(ctx context.Context, in *GetTopicRankReq, opts ...grpc.CallOption) (*GetTopicRankResp, error)
	// 获取推荐流插入话题
	GetTopicsInRecommend(ctx context.Context, in *GetTopicsInRecommendReq, opts ...grpc.CallOption) (*GetTopicsInRecommendResp, error)
	// 返回指定主题详细资料
	GetTopicInfo(ctx context.Context, in *GetTopicInfoReq, opts ...grpc.CallOption) (*GetTopicInfoResp, error)
	// 获取从自定义转官方近3天动态数多的话题
	GetTopicByConvert2Official(ctx context.Context, in *GetTopicByConvert2OfficialReq, opts ...grpc.CallOption) (*GetTopicByConvert2OfficialResp, error)
	// 记录每个话题发动态的用户uid
	AddUserIDByTopic(ctx context.Context, in *AddUserIDByTopicReq, opts ...grpc.CallOption) (*AddUserIDByTopicResp, error)
	// 获取话题发动态的用户uid列表
	GetUserIDByTopic(ctx context.Context, in *GetUserIDByTopicReq, opts ...grpc.CallOption) (*GetUserIDByTopicResp, error)
	// 返回所有主题
	GetTopicList(ctx context.Context, in *GetTopicListReq, opts ...grpc.CallOption) (*GetTopicListResp, error)
	// 根据指定的topicIds获取主题列表
	GetTopics(ctx context.Context, in *GetTopicsReq, opts ...grpc.CallOption) (*GetTopicsResp, error)
	GetTopicsCache(ctx context.Context, in *GetTopicsCacheReq, opts ...grpc.CallOption) (*GetTopicsCacheResp, error)
	// 排序圈子
	SortTopic(ctx context.Context, in *SortTopicReq, opts ...grpc.CallOption) (*SortTopicResp, error)
	// 更新主题帖子数量
	UpdateTopicPostCount(ctx context.Context, in *UpdateTopicPostCountReq, opts ...grpc.CallOption) (*UpdateTopicPostCountResp, error)
	// 调整话题归属
	AdjustTopicBelong(ctx context.Context, in *AdjustTopicBelongReq, opts ...grpc.CallOption) (*AdjustTopicBelongResp, error)
	// 订阅
	SubscribeTopic(ctx context.Context, in *SubscribeTopicReq, opts ...grpc.CallOption) (*SubscribeTopicResp, error)
	// 取消订阅
	UnsubscribeTopic(ctx context.Context, in *UnsubscribeTopicReq, opts ...grpc.CallOption) (*UnsubscribeTopicResp, error)
	// 发布器 新增
	InsertPublisherTopic(ctx context.Context, in *InsertPublisherTopicReq, opts ...grpc.CallOption) (*InsertPublisherTopicResp, error)
	// 发布器 获取配置
	GetPublisherTopicInfo(ctx context.Context, in *GetPublisherTopicInfoReq, opts ...grpc.CallOption) (*GetPublisherTopicInfoResp, error)
	// 发布器 获取uids
	GetPublisherTopicUids(ctx context.Context, in *GetPublisherTopicUidsReq, opts ...grpc.CallOption) (*GetPublisherTopicUidsResp, error)
	// 发布器 删除
	DeletePublisherTopic(ctx context.Context, in *DeletePublisherTopicReq, opts ...grpc.CallOption) (*DeletePublisherTopicResp, error)
	GetPublisherTopicByUid(ctx context.Context, in *GetPublisherTopicByUidReq, opts ...grpc.CallOption) (*GetPublisherTopicByUidResp, error)
	GetPublisherTopicList(ctx context.Context, in *GetPublisherTopicListReq, opts ...grpc.CallOption) (*GetPublisherTopicListResp, error)
	// 增加话题下动态发布记录
	AddTopicStatistic(ctx context.Context, in *AddTopicStatisticReq, opts ...grpc.CallOption) (*AddTopicStatisticResp, error)
	// 创建多个话题
	// 搜索话题 支持模糊搜索
	SearchTopic(ctx context.Context, in *SearchTopicReq, opts ...grpc.CallOption) (*SearchTopicResp, error)
	BatchUpdateTopicPostCount(ctx context.Context, in *BatchUpdateTopicPostCountReq, opts ...grpc.CallOption) (*BatchUpdateTopicPostCountResp, error)
	CreateEsTopic(ctx context.Context, in *CreateEsTopicReq, opts ...grpc.CallOption) (*CreateEsTopicResp, error)
	DeleteEsTopic(ctx context.Context, in *DeleteTopicReq, opts ...grpc.CallOption) (*DeleteTopicResp, error)
	ReportTopicViewCount(ctx context.Context, in *ReportTopicViewCountReq, opts ...grpc.CallOption) (*ReportTopicViewCountRsp, error)
	UpdateTopicRankWeight(ctx context.Context, in *UpdateTopicRankWeightReq, opts ...grpc.CallOption) (*UpdateTopicRankWeightResp, error)
	GetTopicRankWeight(ctx context.Context, in *GetTopicRankWeightReq, opts ...grpc.CallOption) (*GetTopicRankWeightResp, error)
	BatGetTopicRankWeight(ctx context.Context, in *BatGetTopicRankWeightReq, opts ...grpc.CallOption) (*BatGetTopicRankWeightResp, error)
	// 话题详情页广告位
	SetTopicAd(ctx context.Context, in *SetTopicAdReq, opts ...grpc.CallOption) (*SetTopicAdResp, error)
	GetTopicAd(ctx context.Context, in *GetTopicAdReq, opts ...grpc.CallOption) (*GetTopicAdResp, error)
	DelTopicAd(ctx context.Context, in *DelTopicAdReq, opts ...grpc.CallOption) (*DelTopicAdResp, error)
	// 话题绑定的游戏id
	GetTopicBindGameID(ctx context.Context, in *GetTopicBindGameIDReq, opts ...grpc.CallOption) (*GetTopicBindGameIDResp, error)
	// 新建 一个心情id对应的话题id
	CreateTopicIDForMood(ctx context.Context, in *CreateTopicIDForMoodReq, opts ...grpc.CallOption) (*CreateTopicIDForMoodResp, error)
	// 删除话题流强插贴子配置
	DelTopicStreamForceConf(ctx context.Context, in *DelTopicStreamForceConfReq, opts ...grpc.CallOption) (*DelTopicStreamForceConfResp, error)
	// 新建或更新话题流强插贴子配置
	SetTopicStreamForceConf(ctx context.Context, in *SetTopicStreamForceConfReq, opts ...grpc.CallOption) (*SetTopicStreamForceConfResp, error)
	// 根据筛选条件获取话题流强插贴子配置列表
	GetTopicStreamForceConfList(ctx context.Context, in *GetTopicStreamForceConfListReq, opts ...grpc.CallOption) (*GetTopicStreamForceConfListResp, error)
	// 获取话题流指定强插位置范围并且生效中的贴子
	GetTopicStreamForceActivePostList(ctx context.Context, in *GetTopicStreamForceActivePostListReq, opts ...grpc.CallOption) (*GetTopicStreamForceActivePostListResp, error)
	// 反馈话题下的帖子无关
	ReportTopicFeedback(ctx context.Context, in *ReportTopicFeedbackReq, opts ...grpc.CallOption) (*ReportTopicFeedbackResp, error)
	// 获取用户在话题下反馈过的帖子
	GetTopicFeedbacks(ctx context.Context, in *GetTopicFeedbacksReq, opts ...grpc.CallOption) (*GetTopicFeedbacksResp, error)
	GetStepOnTopicIds(ctx context.Context, in *GetStepOnTopicIdsReq, opts ...grpc.CallOption) (*GetStepOnTopicIdsResp, error)
	BatchGetTopicFollowCount(ctx context.Context, in *BatchGetTopicFollowCountReq, opts ...grpc.CallOption) (*BatchGetTopicFollowCountResp, error)
	// 发帖按钮引导配置 运营后台
	SetPostButtonConfig(ctx context.Context, in *SetPostButtonConfigReq, opts ...grpc.CallOption) (*SetPostButtonConfigResp, error)
	GetPostButtonConfigs(ctx context.Context, in *GetPostButtonConfigsReq, opts ...grpc.CallOption) (*GetPostButtonConfigsResp, error)
	OfflinePostButtonConfig(ctx context.Context, in *OfflinePostButtonConfigReq, opts ...grpc.CallOption) (*OfflinePostButtonConfigResp, error)
	GetLastedPostButtonConfig(ctx context.Context, in *GetLastedPostButtonConfigReq, opts ...grpc.CallOption) (*GetLastedPostButtonConfigResp, error)
}

type topicClient struct {
	cc *grpc.ClientConn
}

func NewTopicClient(cc *grpc.ClientConn) TopicClient {
	return &topicClient{cc}
}

func (c *topicClient) CreateTopic(ctx context.Context, in *CreateTopicReq, opts ...grpc.CallOption) (*CreateTopicResp, error) {
	out := new(CreateTopicResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/CreateTopic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) DeleteTopic(ctx context.Context, in *DeleteTopicReq, opts ...grpc.CallOption) (*DeleteTopicResp, error) {
	out := new(DeleteTopicResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/DeleteTopic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) BatDeleteTopic(ctx context.Context, in *BatDeleteTopicReq, opts ...grpc.CallOption) (*BatDeleteTopicResp, error) {
	out := new(BatDeleteTopicResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/BatDeleteTopic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) EnableTopic(ctx context.Context, in *EnableTopicReq, opts ...grpc.CallOption) (*EnableTopicResp, error) {
	out := new(EnableTopicResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/EnableTopic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) UpdateTopicInfo(ctx context.Context, in *UpdateTopicInfoReq, opts ...grpc.CallOption) (*UpdateTopicInfoResp, error) {
	out := new(UpdateTopicInfoResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/UpdateTopicInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) DIY2OfficialTopic(ctx context.Context, in *DIY2OfficialTopicReq, opts ...grpc.CallOption) (*DIY2OfficialTopicResp, error) {
	out := new(DIY2OfficialTopicResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/DIY2OfficialTopic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) BatUpdateWeight(ctx context.Context, in *BatUpdateWeightReq, opts ...grpc.CallOption) (*BatUpdateWeightResp, error) {
	out := new(BatUpdateWeightResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/BatUpdateWeight", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) BatGetAllWeightTopic(ctx context.Context, in *BatGetAllWeightTopicReq, opts ...grpc.CallOption) (*BatGetAllWeightTopicResp, error) {
	out := new(BatGetAllWeightTopicResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/BatGetAllWeightTopic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) UpdatePutTopicInRecommendConf(ctx context.Context, in *UpdatePutTopicInRecommendConfReq, opts ...grpc.CallOption) (*UpdatePutTopicInRecommendConfResp, error) {
	out := new(UpdatePutTopicInRecommendConfResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/UpdatePutTopicInRecommendConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) DelPutTopicInRecommendConf(ctx context.Context, in *DelPutTopicInRecommendConfReq, opts ...grpc.CallOption) (*DelPutTopicInRecommendConfResp, error) {
	out := new(DelPutTopicInRecommendConfResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/DelPutTopicInRecommendConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) GetPutTopicInRecommendConf(ctx context.Context, in *GetPutTopicInRecommendConfReq, opts ...grpc.CallOption) (*GetPutTopicInRecommendConfResp, error) {
	out := new(GetPutTopicInRecommendConfResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/GetPutTopicInRecommendConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) UpdateCatalogTopicInRcmdFeed(ctx context.Context, in *UpdateCatalogTopicInRcmdFeedReq, opts ...grpc.CallOption) (*UpdateCatalogTopicInRcmdFeedResp, error) {
	out := new(UpdateCatalogTopicInRcmdFeedResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/UpdateCatalogTopicInRcmdFeed", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) DelCatalogTopicInRcmdFeed(ctx context.Context, in *DelCatalogTopicInRcmdFeedReq, opts ...grpc.CallOption) (*DelCatalogTopicInRcmdFeedResp, error) {
	out := new(DelCatalogTopicInRcmdFeedResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/DelCatalogTopicInRcmdFeed", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) GetCatalogTopicInRcmdFeed(ctx context.Context, in *GetCatalogTopicInRcmdFeedReq, opts ...grpc.CallOption) (*GetCatalogTopicInRcmdFeedResp, error) {
	out := new(GetCatalogTopicInRcmdFeedResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/GetCatalogTopicInRcmdFeed", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) GetTopicRank(ctx context.Context, in *GetTopicRankReq, opts ...grpc.CallOption) (*GetTopicRankResp, error) {
	out := new(GetTopicRankResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/GetTopicRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) GetTopicsInRecommend(ctx context.Context, in *GetTopicsInRecommendReq, opts ...grpc.CallOption) (*GetTopicsInRecommendResp, error) {
	out := new(GetTopicsInRecommendResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/GetTopicsInRecommend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) GetTopicInfo(ctx context.Context, in *GetTopicInfoReq, opts ...grpc.CallOption) (*GetTopicInfoResp, error) {
	out := new(GetTopicInfoResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/GetTopicInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) GetTopicByConvert2Official(ctx context.Context, in *GetTopicByConvert2OfficialReq, opts ...grpc.CallOption) (*GetTopicByConvert2OfficialResp, error) {
	out := new(GetTopicByConvert2OfficialResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/GetTopicByConvert2Official", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) AddUserIDByTopic(ctx context.Context, in *AddUserIDByTopicReq, opts ...grpc.CallOption) (*AddUserIDByTopicResp, error) {
	out := new(AddUserIDByTopicResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/AddUserIDByTopic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) GetUserIDByTopic(ctx context.Context, in *GetUserIDByTopicReq, opts ...grpc.CallOption) (*GetUserIDByTopicResp, error) {
	out := new(GetUserIDByTopicResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/GetUserIDByTopic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) GetTopicList(ctx context.Context, in *GetTopicListReq, opts ...grpc.CallOption) (*GetTopicListResp, error) {
	out := new(GetTopicListResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/GetTopicList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) GetTopics(ctx context.Context, in *GetTopicsReq, opts ...grpc.CallOption) (*GetTopicsResp, error) {
	out := new(GetTopicsResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/GetTopics", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) GetTopicsCache(ctx context.Context, in *GetTopicsCacheReq, opts ...grpc.CallOption) (*GetTopicsCacheResp, error) {
	out := new(GetTopicsCacheResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/GetTopicsCache", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) SortTopic(ctx context.Context, in *SortTopicReq, opts ...grpc.CallOption) (*SortTopicResp, error) {
	out := new(SortTopicResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/SortTopic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) UpdateTopicPostCount(ctx context.Context, in *UpdateTopicPostCountReq, opts ...grpc.CallOption) (*UpdateTopicPostCountResp, error) {
	out := new(UpdateTopicPostCountResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/UpdateTopicPostCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) AdjustTopicBelong(ctx context.Context, in *AdjustTopicBelongReq, opts ...grpc.CallOption) (*AdjustTopicBelongResp, error) {
	out := new(AdjustTopicBelongResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/AdjustTopicBelong", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) SubscribeTopic(ctx context.Context, in *SubscribeTopicReq, opts ...grpc.CallOption) (*SubscribeTopicResp, error) {
	out := new(SubscribeTopicResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/SubscribeTopic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) UnsubscribeTopic(ctx context.Context, in *UnsubscribeTopicReq, opts ...grpc.CallOption) (*UnsubscribeTopicResp, error) {
	out := new(UnsubscribeTopicResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/UnsubscribeTopic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) InsertPublisherTopic(ctx context.Context, in *InsertPublisherTopicReq, opts ...grpc.CallOption) (*InsertPublisherTopicResp, error) {
	out := new(InsertPublisherTopicResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/InsertPublisherTopic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) GetPublisherTopicInfo(ctx context.Context, in *GetPublisherTopicInfoReq, opts ...grpc.CallOption) (*GetPublisherTopicInfoResp, error) {
	out := new(GetPublisherTopicInfoResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/GetPublisherTopicInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) GetPublisherTopicUids(ctx context.Context, in *GetPublisherTopicUidsReq, opts ...grpc.CallOption) (*GetPublisherTopicUidsResp, error) {
	out := new(GetPublisherTopicUidsResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/GetPublisherTopicUids", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) DeletePublisherTopic(ctx context.Context, in *DeletePublisherTopicReq, opts ...grpc.CallOption) (*DeletePublisherTopicResp, error) {
	out := new(DeletePublisherTopicResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/DeletePublisherTopic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) GetPublisherTopicByUid(ctx context.Context, in *GetPublisherTopicByUidReq, opts ...grpc.CallOption) (*GetPublisherTopicByUidResp, error) {
	out := new(GetPublisherTopicByUidResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/GetPublisherTopicByUid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) GetPublisherTopicList(ctx context.Context, in *GetPublisherTopicListReq, opts ...grpc.CallOption) (*GetPublisherTopicListResp, error) {
	out := new(GetPublisherTopicListResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/GetPublisherTopicList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) AddTopicStatistic(ctx context.Context, in *AddTopicStatisticReq, opts ...grpc.CallOption) (*AddTopicStatisticResp, error) {
	out := new(AddTopicStatisticResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/AddTopicStatistic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) SearchTopic(ctx context.Context, in *SearchTopicReq, opts ...grpc.CallOption) (*SearchTopicResp, error) {
	out := new(SearchTopicResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/SearchTopic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) BatchUpdateTopicPostCount(ctx context.Context, in *BatchUpdateTopicPostCountReq, opts ...grpc.CallOption) (*BatchUpdateTopicPostCountResp, error) {
	out := new(BatchUpdateTopicPostCountResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/BatchUpdateTopicPostCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) CreateEsTopic(ctx context.Context, in *CreateEsTopicReq, opts ...grpc.CallOption) (*CreateEsTopicResp, error) {
	out := new(CreateEsTopicResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/CreateEsTopic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) DeleteEsTopic(ctx context.Context, in *DeleteTopicReq, opts ...grpc.CallOption) (*DeleteTopicResp, error) {
	out := new(DeleteTopicResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/DeleteEsTopic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) ReportTopicViewCount(ctx context.Context, in *ReportTopicViewCountReq, opts ...grpc.CallOption) (*ReportTopicViewCountRsp, error) {
	out := new(ReportTopicViewCountRsp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/ReportTopicViewCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) UpdateTopicRankWeight(ctx context.Context, in *UpdateTopicRankWeightReq, opts ...grpc.CallOption) (*UpdateTopicRankWeightResp, error) {
	out := new(UpdateTopicRankWeightResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/UpdateTopicRankWeight", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) GetTopicRankWeight(ctx context.Context, in *GetTopicRankWeightReq, opts ...grpc.CallOption) (*GetTopicRankWeightResp, error) {
	out := new(GetTopicRankWeightResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/GetTopicRankWeight", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) BatGetTopicRankWeight(ctx context.Context, in *BatGetTopicRankWeightReq, opts ...grpc.CallOption) (*BatGetTopicRankWeightResp, error) {
	out := new(BatGetTopicRankWeightResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/BatGetTopicRankWeight", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) SetTopicAd(ctx context.Context, in *SetTopicAdReq, opts ...grpc.CallOption) (*SetTopicAdResp, error) {
	out := new(SetTopicAdResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/SetTopicAd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) GetTopicAd(ctx context.Context, in *GetTopicAdReq, opts ...grpc.CallOption) (*GetTopicAdResp, error) {
	out := new(GetTopicAdResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/GetTopicAd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) DelTopicAd(ctx context.Context, in *DelTopicAdReq, opts ...grpc.CallOption) (*DelTopicAdResp, error) {
	out := new(DelTopicAdResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/DelTopicAd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) GetTopicBindGameID(ctx context.Context, in *GetTopicBindGameIDReq, opts ...grpc.CallOption) (*GetTopicBindGameIDResp, error) {
	out := new(GetTopicBindGameIDResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/GetTopicBindGameID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) CreateTopicIDForMood(ctx context.Context, in *CreateTopicIDForMoodReq, opts ...grpc.CallOption) (*CreateTopicIDForMoodResp, error) {
	out := new(CreateTopicIDForMoodResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/CreateTopicIDForMood", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) DelTopicStreamForceConf(ctx context.Context, in *DelTopicStreamForceConfReq, opts ...grpc.CallOption) (*DelTopicStreamForceConfResp, error) {
	out := new(DelTopicStreamForceConfResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/DelTopicStreamForceConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) SetTopicStreamForceConf(ctx context.Context, in *SetTopicStreamForceConfReq, opts ...grpc.CallOption) (*SetTopicStreamForceConfResp, error) {
	out := new(SetTopicStreamForceConfResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/SetTopicStreamForceConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) GetTopicStreamForceConfList(ctx context.Context, in *GetTopicStreamForceConfListReq, opts ...grpc.CallOption) (*GetTopicStreamForceConfListResp, error) {
	out := new(GetTopicStreamForceConfListResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/GetTopicStreamForceConfList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) GetTopicStreamForceActivePostList(ctx context.Context, in *GetTopicStreamForceActivePostListReq, opts ...grpc.CallOption) (*GetTopicStreamForceActivePostListResp, error) {
	out := new(GetTopicStreamForceActivePostListResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/GetTopicStreamForceActivePostList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) ReportTopicFeedback(ctx context.Context, in *ReportTopicFeedbackReq, opts ...grpc.CallOption) (*ReportTopicFeedbackResp, error) {
	out := new(ReportTopicFeedbackResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/ReportTopicFeedback", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) GetTopicFeedbacks(ctx context.Context, in *GetTopicFeedbacksReq, opts ...grpc.CallOption) (*GetTopicFeedbacksResp, error) {
	out := new(GetTopicFeedbacksResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/GetTopicFeedbacks", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) GetStepOnTopicIds(ctx context.Context, in *GetStepOnTopicIdsReq, opts ...grpc.CallOption) (*GetStepOnTopicIdsResp, error) {
	out := new(GetStepOnTopicIdsResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/GetStepOnTopicIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) BatchGetTopicFollowCount(ctx context.Context, in *BatchGetTopicFollowCountReq, opts ...grpc.CallOption) (*BatchGetTopicFollowCountResp, error) {
	out := new(BatchGetTopicFollowCountResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/BatchGetTopicFollowCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) SetPostButtonConfig(ctx context.Context, in *SetPostButtonConfigReq, opts ...grpc.CallOption) (*SetPostButtonConfigResp, error) {
	out := new(SetPostButtonConfigResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/SetPostButtonConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) GetPostButtonConfigs(ctx context.Context, in *GetPostButtonConfigsReq, opts ...grpc.CallOption) (*GetPostButtonConfigsResp, error) {
	out := new(GetPostButtonConfigsResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/GetPostButtonConfigs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) OfflinePostButtonConfig(ctx context.Context, in *OfflinePostButtonConfigReq, opts ...grpc.CallOption) (*OfflinePostButtonConfigResp, error) {
	out := new(OfflinePostButtonConfigResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/OfflinePostButtonConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *topicClient) GetLastedPostButtonConfig(ctx context.Context, in *GetLastedPostButtonConfigReq, opts ...grpc.CallOption) (*GetLastedPostButtonConfigResp, error) {
	out := new(GetLastedPostButtonConfigResp)
	err := c.cc.Invoke(ctx, "/ugc.topic.Topic/GetLastedPostButtonConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TopicServer is the server API for Topic service.
type TopicServer interface {
	CreateTopic(context.Context, *CreateTopicReq) (*CreateTopicResp, error)
	// 删除
	DeleteTopic(context.Context, *DeleteTopicReq) (*DeleteTopicResp, error)
	BatDeleteTopic(context.Context, *BatDeleteTopicReq) (*BatDeleteTopicResp, error)
	// 上下线主题
	EnableTopic(context.Context, *EnableTopicReq) (*EnableTopicResp, error)
	// 更新主题资料
	UpdateTopicInfo(context.Context, *UpdateTopicInfoReq) (*UpdateTopicInfoResp, error)
	// 从自定义修改为官方话题
	DIY2OfficialTopic(context.Context, *DIY2OfficialTopicReq) (*DIY2OfficialTopicResp, error)
	// 批量修改提权
	BatUpdateWeight(context.Context, *BatUpdateWeightReq) (*BatUpdateWeightResp, error)
	// 批量获取所有提权话题
	BatGetAllWeightTopic(context.Context, *BatGetAllWeightTopicReq) (*BatGetAllWeightTopicResp, error)
	// 推荐流插入话题设置
	UpdatePutTopicInRecommendConf(context.Context, *UpdatePutTopicInRecommendConfReq) (*UpdatePutTopicInRecommendConfResp, error)
	// 删除推荐流插入话题配置
	DelPutTopicInRecommendConf(context.Context, *DelPutTopicInRecommendConfReq) (*DelPutTopicInRecommendConfResp, error)
	// 获取推荐流插入话题配置
	GetPutTopicInRecommendConf(context.Context, *GetPutTopicInRecommendConfReq) (*GetPutTopicInRecommendConfResp, error)
	// 更新推荐流插入其他分类话题配置
	UpdateCatalogTopicInRcmdFeed(context.Context, *UpdateCatalogTopicInRcmdFeedReq) (*UpdateCatalogTopicInRcmdFeedResp, error)
	// 删除推荐流插入其他分类话题配置
	DelCatalogTopicInRcmdFeed(context.Context, *DelCatalogTopicInRcmdFeedReq) (*DelCatalogTopicInRcmdFeedResp, error)
	// 获取推荐流插入其他分类话题配置
	GetCatalogTopicInRcmdFeed(context.Context, *GetCatalogTopicInRcmdFeedReq) (*GetCatalogTopicInRcmdFeedResp, error)
	// 获取24小时 3天 7天内 动态量最多的话题
	GetTopicRank(context.Context, *GetTopicRankReq) (*GetTopicRankResp, error)
	// 获取推荐流插入话题
	GetTopicsInRecommend(context.Context, *GetTopicsInRecommendReq) (*GetTopicsInRecommendResp, error)
	// 返回指定主题详细资料
	GetTopicInfo(context.Context, *GetTopicInfoReq) (*GetTopicInfoResp, error)
	// 获取从自定义转官方近3天动态数多的话题
	GetTopicByConvert2Official(context.Context, *GetTopicByConvert2OfficialReq) (*GetTopicByConvert2OfficialResp, error)
	// 记录每个话题发动态的用户uid
	AddUserIDByTopic(context.Context, *AddUserIDByTopicReq) (*AddUserIDByTopicResp, error)
	// 获取话题发动态的用户uid列表
	GetUserIDByTopic(context.Context, *GetUserIDByTopicReq) (*GetUserIDByTopicResp, error)
	// 返回所有主题
	GetTopicList(context.Context, *GetTopicListReq) (*GetTopicListResp, error)
	// 根据指定的topicIds获取主题列表
	GetTopics(context.Context, *GetTopicsReq) (*GetTopicsResp, error)
	GetTopicsCache(context.Context, *GetTopicsCacheReq) (*GetTopicsCacheResp, error)
	// 排序圈子
	SortTopic(context.Context, *SortTopicReq) (*SortTopicResp, error)
	// 更新主题帖子数量
	UpdateTopicPostCount(context.Context, *UpdateTopicPostCountReq) (*UpdateTopicPostCountResp, error)
	// 调整话题归属
	AdjustTopicBelong(context.Context, *AdjustTopicBelongReq) (*AdjustTopicBelongResp, error)
	// 订阅
	SubscribeTopic(context.Context, *SubscribeTopicReq) (*SubscribeTopicResp, error)
	// 取消订阅
	UnsubscribeTopic(context.Context, *UnsubscribeTopicReq) (*UnsubscribeTopicResp, error)
	// 发布器 新增
	InsertPublisherTopic(context.Context, *InsertPublisherTopicReq) (*InsertPublisherTopicResp, error)
	// 发布器 获取配置
	GetPublisherTopicInfo(context.Context, *GetPublisherTopicInfoReq) (*GetPublisherTopicInfoResp, error)
	// 发布器 获取uids
	GetPublisherTopicUids(context.Context, *GetPublisherTopicUidsReq) (*GetPublisherTopicUidsResp, error)
	// 发布器 删除
	DeletePublisherTopic(context.Context, *DeletePublisherTopicReq) (*DeletePublisherTopicResp, error)
	GetPublisherTopicByUid(context.Context, *GetPublisherTopicByUidReq) (*GetPublisherTopicByUidResp, error)
	GetPublisherTopicList(context.Context, *GetPublisherTopicListReq) (*GetPublisherTopicListResp, error)
	// 增加话题下动态发布记录
	AddTopicStatistic(context.Context, *AddTopicStatisticReq) (*AddTopicStatisticResp, error)
	// 创建多个话题
	// 搜索话题 支持模糊搜索
	SearchTopic(context.Context, *SearchTopicReq) (*SearchTopicResp, error)
	BatchUpdateTopicPostCount(context.Context, *BatchUpdateTopicPostCountReq) (*BatchUpdateTopicPostCountResp, error)
	CreateEsTopic(context.Context, *CreateEsTopicReq) (*CreateEsTopicResp, error)
	DeleteEsTopic(context.Context, *DeleteTopicReq) (*DeleteTopicResp, error)
	ReportTopicViewCount(context.Context, *ReportTopicViewCountReq) (*ReportTopicViewCountRsp, error)
	UpdateTopicRankWeight(context.Context, *UpdateTopicRankWeightReq) (*UpdateTopicRankWeightResp, error)
	GetTopicRankWeight(context.Context, *GetTopicRankWeightReq) (*GetTopicRankWeightResp, error)
	BatGetTopicRankWeight(context.Context, *BatGetTopicRankWeightReq) (*BatGetTopicRankWeightResp, error)
	// 话题详情页广告位
	SetTopicAd(context.Context, *SetTopicAdReq) (*SetTopicAdResp, error)
	GetTopicAd(context.Context, *GetTopicAdReq) (*GetTopicAdResp, error)
	DelTopicAd(context.Context, *DelTopicAdReq) (*DelTopicAdResp, error)
	// 话题绑定的游戏id
	GetTopicBindGameID(context.Context, *GetTopicBindGameIDReq) (*GetTopicBindGameIDResp, error)
	// 新建 一个心情id对应的话题id
	CreateTopicIDForMood(context.Context, *CreateTopicIDForMoodReq) (*CreateTopicIDForMoodResp, error)
	// 删除话题流强插贴子配置
	DelTopicStreamForceConf(context.Context, *DelTopicStreamForceConfReq) (*DelTopicStreamForceConfResp, error)
	// 新建或更新话题流强插贴子配置
	SetTopicStreamForceConf(context.Context, *SetTopicStreamForceConfReq) (*SetTopicStreamForceConfResp, error)
	// 根据筛选条件获取话题流强插贴子配置列表
	GetTopicStreamForceConfList(context.Context, *GetTopicStreamForceConfListReq) (*GetTopicStreamForceConfListResp, error)
	// 获取话题流指定强插位置范围并且生效中的贴子
	GetTopicStreamForceActivePostList(context.Context, *GetTopicStreamForceActivePostListReq) (*GetTopicStreamForceActivePostListResp, error)
	// 反馈话题下的帖子无关
	ReportTopicFeedback(context.Context, *ReportTopicFeedbackReq) (*ReportTopicFeedbackResp, error)
	// 获取用户在话题下反馈过的帖子
	GetTopicFeedbacks(context.Context, *GetTopicFeedbacksReq) (*GetTopicFeedbacksResp, error)
	GetStepOnTopicIds(context.Context, *GetStepOnTopicIdsReq) (*GetStepOnTopicIdsResp, error)
	BatchGetTopicFollowCount(context.Context, *BatchGetTopicFollowCountReq) (*BatchGetTopicFollowCountResp, error)
	// 发帖按钮引导配置 运营后台
	SetPostButtonConfig(context.Context, *SetPostButtonConfigReq) (*SetPostButtonConfigResp, error)
	GetPostButtonConfigs(context.Context, *GetPostButtonConfigsReq) (*GetPostButtonConfigsResp, error)
	OfflinePostButtonConfig(context.Context, *OfflinePostButtonConfigReq) (*OfflinePostButtonConfigResp, error)
	GetLastedPostButtonConfig(context.Context, *GetLastedPostButtonConfigReq) (*GetLastedPostButtonConfigResp, error)
}

func RegisterTopicServer(s *grpc.Server, srv TopicServer) {
	s.RegisterService(&_Topic_serviceDesc, srv)
}

func _Topic_CreateTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTopicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).CreateTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/CreateTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).CreateTopic(ctx, req.(*CreateTopicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_DeleteTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTopicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).DeleteTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/DeleteTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).DeleteTopic(ctx, req.(*DeleteTopicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_BatDeleteTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatDeleteTopicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).BatDeleteTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/BatDeleteTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).BatDeleteTopic(ctx, req.(*BatDeleteTopicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_EnableTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EnableTopicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).EnableTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/EnableTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).EnableTopic(ctx, req.(*EnableTopicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_UpdateTopicInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTopicInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).UpdateTopicInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/UpdateTopicInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).UpdateTopicInfo(ctx, req.(*UpdateTopicInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_DIY2OfficialTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DIY2OfficialTopicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).DIY2OfficialTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/DIY2OfficialTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).DIY2OfficialTopic(ctx, req.(*DIY2OfficialTopicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_BatUpdateWeight_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatUpdateWeightReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).BatUpdateWeight(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/BatUpdateWeight",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).BatUpdateWeight(ctx, req.(*BatUpdateWeightReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_BatGetAllWeightTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetAllWeightTopicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).BatGetAllWeightTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/BatGetAllWeightTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).BatGetAllWeightTopic(ctx, req.(*BatGetAllWeightTopicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_UpdatePutTopicInRecommendConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePutTopicInRecommendConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).UpdatePutTopicInRecommendConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/UpdatePutTopicInRecommendConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).UpdatePutTopicInRecommendConf(ctx, req.(*UpdatePutTopicInRecommendConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_DelPutTopicInRecommendConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelPutTopicInRecommendConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).DelPutTopicInRecommendConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/DelPutTopicInRecommendConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).DelPutTopicInRecommendConf(ctx, req.(*DelPutTopicInRecommendConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_GetPutTopicInRecommendConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPutTopicInRecommendConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).GetPutTopicInRecommendConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/GetPutTopicInRecommendConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).GetPutTopicInRecommendConf(ctx, req.(*GetPutTopicInRecommendConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_UpdateCatalogTopicInRcmdFeed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCatalogTopicInRcmdFeedReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).UpdateCatalogTopicInRcmdFeed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/UpdateCatalogTopicInRcmdFeed",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).UpdateCatalogTopicInRcmdFeed(ctx, req.(*UpdateCatalogTopicInRcmdFeedReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_DelCatalogTopicInRcmdFeed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelCatalogTopicInRcmdFeedReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).DelCatalogTopicInRcmdFeed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/DelCatalogTopicInRcmdFeed",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).DelCatalogTopicInRcmdFeed(ctx, req.(*DelCatalogTopicInRcmdFeedReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_GetCatalogTopicInRcmdFeed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCatalogTopicInRcmdFeedReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).GetCatalogTopicInRcmdFeed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/GetCatalogTopicInRcmdFeed",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).GetCatalogTopicInRcmdFeed(ctx, req.(*GetCatalogTopicInRcmdFeedReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_GetTopicRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopicRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).GetTopicRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/GetTopicRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).GetTopicRank(ctx, req.(*GetTopicRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_GetTopicsInRecommend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopicsInRecommendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).GetTopicsInRecommend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/GetTopicsInRecommend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).GetTopicsInRecommend(ctx, req.(*GetTopicsInRecommendReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_GetTopicInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopicInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).GetTopicInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/GetTopicInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).GetTopicInfo(ctx, req.(*GetTopicInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_GetTopicByConvert2Official_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopicByConvert2OfficialReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).GetTopicByConvert2Official(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/GetTopicByConvert2Official",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).GetTopicByConvert2Official(ctx, req.(*GetTopicByConvert2OfficialReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_AddUserIDByTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserIDByTopicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).AddUserIDByTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/AddUserIDByTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).AddUserIDByTopic(ctx, req.(*AddUserIDByTopicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_GetUserIDByTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserIDByTopicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).GetUserIDByTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/GetUserIDByTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).GetUserIDByTopic(ctx, req.(*GetUserIDByTopicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_GetTopicList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopicListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).GetTopicList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/GetTopicList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).GetTopicList(ctx, req.(*GetTopicListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_GetTopics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopicsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).GetTopics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/GetTopics",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).GetTopics(ctx, req.(*GetTopicsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_GetTopicsCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopicsCacheReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).GetTopicsCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/GetTopicsCache",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).GetTopicsCache(ctx, req.(*GetTopicsCacheReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_SortTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortTopicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).SortTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/SortTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).SortTopic(ctx, req.(*SortTopicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_UpdateTopicPostCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTopicPostCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).UpdateTopicPostCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/UpdateTopicPostCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).UpdateTopicPostCount(ctx, req.(*UpdateTopicPostCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_AdjustTopicBelong_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdjustTopicBelongReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).AdjustTopicBelong(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/AdjustTopicBelong",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).AdjustTopicBelong(ctx, req.(*AdjustTopicBelongReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_SubscribeTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubscribeTopicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).SubscribeTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/SubscribeTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).SubscribeTopic(ctx, req.(*SubscribeTopicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_UnsubscribeTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnsubscribeTopicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).UnsubscribeTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/UnsubscribeTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).UnsubscribeTopic(ctx, req.(*UnsubscribeTopicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_InsertPublisherTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InsertPublisherTopicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).InsertPublisherTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/InsertPublisherTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).InsertPublisherTopic(ctx, req.(*InsertPublisherTopicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_GetPublisherTopicInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPublisherTopicInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).GetPublisherTopicInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/GetPublisherTopicInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).GetPublisherTopicInfo(ctx, req.(*GetPublisherTopicInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_GetPublisherTopicUids_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPublisherTopicUidsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).GetPublisherTopicUids(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/GetPublisherTopicUids",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).GetPublisherTopicUids(ctx, req.(*GetPublisherTopicUidsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_DeletePublisherTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePublisherTopicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).DeletePublisherTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/DeletePublisherTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).DeletePublisherTopic(ctx, req.(*DeletePublisherTopicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_GetPublisherTopicByUid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPublisherTopicByUidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).GetPublisherTopicByUid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/GetPublisherTopicByUid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).GetPublisherTopicByUid(ctx, req.(*GetPublisherTopicByUidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_GetPublisherTopicList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPublisherTopicListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).GetPublisherTopicList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/GetPublisherTopicList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).GetPublisherTopicList(ctx, req.(*GetPublisherTopicListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_AddTopicStatistic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddTopicStatisticReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).AddTopicStatistic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/AddTopicStatistic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).AddTopicStatistic(ctx, req.(*AddTopicStatisticReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_SearchTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchTopicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).SearchTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/SearchTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).SearchTopic(ctx, req.(*SearchTopicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_BatchUpdateTopicPostCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUpdateTopicPostCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).BatchUpdateTopicPostCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/BatchUpdateTopicPostCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).BatchUpdateTopicPostCount(ctx, req.(*BatchUpdateTopicPostCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_CreateEsTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateEsTopicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).CreateEsTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/CreateEsTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).CreateEsTopic(ctx, req.(*CreateEsTopicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_DeleteEsTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTopicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).DeleteEsTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/DeleteEsTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).DeleteEsTopic(ctx, req.(*DeleteTopicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_ReportTopicViewCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportTopicViewCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).ReportTopicViewCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/ReportTopicViewCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).ReportTopicViewCount(ctx, req.(*ReportTopicViewCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_UpdateTopicRankWeight_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTopicRankWeightReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).UpdateTopicRankWeight(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/UpdateTopicRankWeight",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).UpdateTopicRankWeight(ctx, req.(*UpdateTopicRankWeightReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_GetTopicRankWeight_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopicRankWeightReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).GetTopicRankWeight(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/GetTopicRankWeight",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).GetTopicRankWeight(ctx, req.(*GetTopicRankWeightReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_BatGetTopicRankWeight_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetTopicRankWeightReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).BatGetTopicRankWeight(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/BatGetTopicRankWeight",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).BatGetTopicRankWeight(ctx, req.(*BatGetTopicRankWeightReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_SetTopicAd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetTopicAdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).SetTopicAd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/SetTopicAd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).SetTopicAd(ctx, req.(*SetTopicAdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_GetTopicAd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopicAdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).GetTopicAd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/GetTopicAd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).GetTopicAd(ctx, req.(*GetTopicAdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_DelTopicAd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelTopicAdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).DelTopicAd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/DelTopicAd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).DelTopicAd(ctx, req.(*DelTopicAdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_GetTopicBindGameID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopicBindGameIDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).GetTopicBindGameID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/GetTopicBindGameID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).GetTopicBindGameID(ctx, req.(*GetTopicBindGameIDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_CreateTopicIDForMood_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTopicIDForMoodReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).CreateTopicIDForMood(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/CreateTopicIDForMood",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).CreateTopicIDForMood(ctx, req.(*CreateTopicIDForMoodReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_DelTopicStreamForceConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelTopicStreamForceConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).DelTopicStreamForceConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/DelTopicStreamForceConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).DelTopicStreamForceConf(ctx, req.(*DelTopicStreamForceConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_SetTopicStreamForceConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetTopicStreamForceConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).SetTopicStreamForceConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/SetTopicStreamForceConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).SetTopicStreamForceConf(ctx, req.(*SetTopicStreamForceConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_GetTopicStreamForceConfList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopicStreamForceConfListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).GetTopicStreamForceConfList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/GetTopicStreamForceConfList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).GetTopicStreamForceConfList(ctx, req.(*GetTopicStreamForceConfListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_GetTopicStreamForceActivePostList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopicStreamForceActivePostListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).GetTopicStreamForceActivePostList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/GetTopicStreamForceActivePostList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).GetTopicStreamForceActivePostList(ctx, req.(*GetTopicStreamForceActivePostListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_ReportTopicFeedback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportTopicFeedbackReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).ReportTopicFeedback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/ReportTopicFeedback",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).ReportTopicFeedback(ctx, req.(*ReportTopicFeedbackReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_GetTopicFeedbacks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopicFeedbacksReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).GetTopicFeedbacks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/GetTopicFeedbacks",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).GetTopicFeedbacks(ctx, req.(*GetTopicFeedbacksReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_GetStepOnTopicIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStepOnTopicIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).GetStepOnTopicIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/GetStepOnTopicIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).GetStepOnTopicIds(ctx, req.(*GetStepOnTopicIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_BatchGetTopicFollowCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetTopicFollowCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).BatchGetTopicFollowCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/BatchGetTopicFollowCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).BatchGetTopicFollowCount(ctx, req.(*BatchGetTopicFollowCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_SetPostButtonConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetPostButtonConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).SetPostButtonConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/SetPostButtonConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).SetPostButtonConfig(ctx, req.(*SetPostButtonConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_GetPostButtonConfigs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPostButtonConfigsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).GetPostButtonConfigs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/GetPostButtonConfigs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).GetPostButtonConfigs(ctx, req.(*GetPostButtonConfigsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_OfflinePostButtonConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OfflinePostButtonConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).OfflinePostButtonConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/OfflinePostButtonConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).OfflinePostButtonConfig(ctx, req.(*OfflinePostButtonConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Topic_GetLastedPostButtonConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLastedPostButtonConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TopicServer).GetLastedPostButtonConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.topic.Topic/GetLastedPostButtonConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TopicServer).GetLastedPostButtonConfig(ctx, req.(*GetLastedPostButtonConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Topic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ugc.topic.Topic",
	HandlerType: (*TopicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateTopic",
			Handler:    _Topic_CreateTopic_Handler,
		},
		{
			MethodName: "DeleteTopic",
			Handler:    _Topic_DeleteTopic_Handler,
		},
		{
			MethodName: "BatDeleteTopic",
			Handler:    _Topic_BatDeleteTopic_Handler,
		},
		{
			MethodName: "EnableTopic",
			Handler:    _Topic_EnableTopic_Handler,
		},
		{
			MethodName: "UpdateTopicInfo",
			Handler:    _Topic_UpdateTopicInfo_Handler,
		},
		{
			MethodName: "DIY2OfficialTopic",
			Handler:    _Topic_DIY2OfficialTopic_Handler,
		},
		{
			MethodName: "BatUpdateWeight",
			Handler:    _Topic_BatUpdateWeight_Handler,
		},
		{
			MethodName: "BatGetAllWeightTopic",
			Handler:    _Topic_BatGetAllWeightTopic_Handler,
		},
		{
			MethodName: "UpdatePutTopicInRecommendConf",
			Handler:    _Topic_UpdatePutTopicInRecommendConf_Handler,
		},
		{
			MethodName: "DelPutTopicInRecommendConf",
			Handler:    _Topic_DelPutTopicInRecommendConf_Handler,
		},
		{
			MethodName: "GetPutTopicInRecommendConf",
			Handler:    _Topic_GetPutTopicInRecommendConf_Handler,
		},
		{
			MethodName: "UpdateCatalogTopicInRcmdFeed",
			Handler:    _Topic_UpdateCatalogTopicInRcmdFeed_Handler,
		},
		{
			MethodName: "DelCatalogTopicInRcmdFeed",
			Handler:    _Topic_DelCatalogTopicInRcmdFeed_Handler,
		},
		{
			MethodName: "GetCatalogTopicInRcmdFeed",
			Handler:    _Topic_GetCatalogTopicInRcmdFeed_Handler,
		},
		{
			MethodName: "GetTopicRank",
			Handler:    _Topic_GetTopicRank_Handler,
		},
		{
			MethodName: "GetTopicsInRecommend",
			Handler:    _Topic_GetTopicsInRecommend_Handler,
		},
		{
			MethodName: "GetTopicInfo",
			Handler:    _Topic_GetTopicInfo_Handler,
		},
		{
			MethodName: "GetTopicByConvert2Official",
			Handler:    _Topic_GetTopicByConvert2Official_Handler,
		},
		{
			MethodName: "AddUserIDByTopic",
			Handler:    _Topic_AddUserIDByTopic_Handler,
		},
		{
			MethodName: "GetUserIDByTopic",
			Handler:    _Topic_GetUserIDByTopic_Handler,
		},
		{
			MethodName: "GetTopicList",
			Handler:    _Topic_GetTopicList_Handler,
		},
		{
			MethodName: "GetTopics",
			Handler:    _Topic_GetTopics_Handler,
		},
		{
			MethodName: "GetTopicsCache",
			Handler:    _Topic_GetTopicsCache_Handler,
		},
		{
			MethodName: "SortTopic",
			Handler:    _Topic_SortTopic_Handler,
		},
		{
			MethodName: "UpdateTopicPostCount",
			Handler:    _Topic_UpdateTopicPostCount_Handler,
		},
		{
			MethodName: "AdjustTopicBelong",
			Handler:    _Topic_AdjustTopicBelong_Handler,
		},
		{
			MethodName: "SubscribeTopic",
			Handler:    _Topic_SubscribeTopic_Handler,
		},
		{
			MethodName: "UnsubscribeTopic",
			Handler:    _Topic_UnsubscribeTopic_Handler,
		},
		{
			MethodName: "InsertPublisherTopic",
			Handler:    _Topic_InsertPublisherTopic_Handler,
		},
		{
			MethodName: "GetPublisherTopicInfo",
			Handler:    _Topic_GetPublisherTopicInfo_Handler,
		},
		{
			MethodName: "GetPublisherTopicUids",
			Handler:    _Topic_GetPublisherTopicUids_Handler,
		},
		{
			MethodName: "DeletePublisherTopic",
			Handler:    _Topic_DeletePublisherTopic_Handler,
		},
		{
			MethodName: "GetPublisherTopicByUid",
			Handler:    _Topic_GetPublisherTopicByUid_Handler,
		},
		{
			MethodName: "GetPublisherTopicList",
			Handler:    _Topic_GetPublisherTopicList_Handler,
		},
		{
			MethodName: "AddTopicStatistic",
			Handler:    _Topic_AddTopicStatistic_Handler,
		},
		{
			MethodName: "SearchTopic",
			Handler:    _Topic_SearchTopic_Handler,
		},
		{
			MethodName: "BatchUpdateTopicPostCount",
			Handler:    _Topic_BatchUpdateTopicPostCount_Handler,
		},
		{
			MethodName: "CreateEsTopic",
			Handler:    _Topic_CreateEsTopic_Handler,
		},
		{
			MethodName: "DeleteEsTopic",
			Handler:    _Topic_DeleteEsTopic_Handler,
		},
		{
			MethodName: "ReportTopicViewCount",
			Handler:    _Topic_ReportTopicViewCount_Handler,
		},
		{
			MethodName: "UpdateTopicRankWeight",
			Handler:    _Topic_UpdateTopicRankWeight_Handler,
		},
		{
			MethodName: "GetTopicRankWeight",
			Handler:    _Topic_GetTopicRankWeight_Handler,
		},
		{
			MethodName: "BatGetTopicRankWeight",
			Handler:    _Topic_BatGetTopicRankWeight_Handler,
		},
		{
			MethodName: "SetTopicAd",
			Handler:    _Topic_SetTopicAd_Handler,
		},
		{
			MethodName: "GetTopicAd",
			Handler:    _Topic_GetTopicAd_Handler,
		},
		{
			MethodName: "DelTopicAd",
			Handler:    _Topic_DelTopicAd_Handler,
		},
		{
			MethodName: "GetTopicBindGameID",
			Handler:    _Topic_GetTopicBindGameID_Handler,
		},
		{
			MethodName: "CreateTopicIDForMood",
			Handler:    _Topic_CreateTopicIDForMood_Handler,
		},
		{
			MethodName: "DelTopicStreamForceConf",
			Handler:    _Topic_DelTopicStreamForceConf_Handler,
		},
		{
			MethodName: "SetTopicStreamForceConf",
			Handler:    _Topic_SetTopicStreamForceConf_Handler,
		},
		{
			MethodName: "GetTopicStreamForceConfList",
			Handler:    _Topic_GetTopicStreamForceConfList_Handler,
		},
		{
			MethodName: "GetTopicStreamForceActivePostList",
			Handler:    _Topic_GetTopicStreamForceActivePostList_Handler,
		},
		{
			MethodName: "ReportTopicFeedback",
			Handler:    _Topic_ReportTopicFeedback_Handler,
		},
		{
			MethodName: "GetTopicFeedbacks",
			Handler:    _Topic_GetTopicFeedbacks_Handler,
		},
		{
			MethodName: "GetStepOnTopicIds",
			Handler:    _Topic_GetStepOnTopicIds_Handler,
		},
		{
			MethodName: "BatchGetTopicFollowCount",
			Handler:    _Topic_BatchGetTopicFollowCount_Handler,
		},
		{
			MethodName: "SetPostButtonConfig",
			Handler:    _Topic_SetPostButtonConfig_Handler,
		},
		{
			MethodName: "GetPostButtonConfigs",
			Handler:    _Topic_GetPostButtonConfigs_Handler,
		},
		{
			MethodName: "OfflinePostButtonConfig",
			Handler:    _Topic_OfflinePostButtonConfig_Handler,
		},
		{
			MethodName: "GetLastedPostButtonConfig",
			Handler:    _Topic_GetLastedPostButtonConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ugc/topic.proto",
}

func init() { proto.RegisterFile("ugc/topic.proto", fileDescriptor_topic_58f2997e06b41df1) }

var fileDescriptor_topic_58f2997e06b41df1 = []byte{
	// 6347 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x3c, 0x5d, 0x6f, 0x1c, 0x59,
	0x56, 0xae, 0xee, 0x76, 0x7f, 0x9c, 0x76, 0xdb, 0x9d, 0x1b, 0x27, 0x76, 0xda, 0x71, 0x3e, 0x6a,
	0x92, 0x99, 0x8c, 0x27, 0x93, 0x0f, 0xcf, 0x84, 0xf9, 0xda, 0xd5, 0x6e, 0xdb, 0x4e, 0x3c, 0xcd,
	0x26, 0x76, 0xb6, 0xec, 0xcc, 0x30, 0xcc, 0x6a, 0x7b, 0xcb, 0x5d, 0xd7, 0x4e, 0x6d, 0xba, 0xab,
	0x6a, 0xea, 0x56, 0xc7, 0xf6, 0x82, 0x78, 0x02, 0x81, 0x78, 0x42, 0x08, 0x9e, 0xe0, 0x01, 0xb1,
	0xf0, 0x00, 0x8b, 0x90, 0x90, 0xf8, 0x10, 0x12, 0x0f, 0xf0, 0xc0, 0xcb, 0x22, 0xf1, 0x03, 0x90,
	0xf8, 0x11, 0x48, 0x88, 0x57, 0xd0, 0xfd, 0xa8, 0xaa, 0x7b, 0xab, 0x6f, 0x95, 0x3b, 0x99, 0x5d,
	0x01, 0x4f, 0xdd, 0xf7, 0xf3, 0xdc, 0x7b, 0xce, 0xb9, 0xe7, 0x9c, 0x7b, 0xce, 0xb9, 0x05, 0x0b,
	0xe3, 0xa3, 0xc1, 0xdd, 0xc8, 0x0f, 0xdc, 0xc1, 0x9d, 0x20, 0xf4, 0x23, 0x1f, 0x35, 0xc6, 0x47,
	0x83, 0x3b, 0xac, 0xc2, 0xec, 0xc1, 0xb9, 0x7d, 0xfa, 0x67, 0xc3, 0xf5, 0x9c, 0x6d, 0x7b, 0x84,
	0x7b, 0xde, 0xa1, 0x8f, 0x96, 0xa0, 0x76, 0x64, 0x8f, 0x70, 0xdf, 0x75, 0x96, 0x8d, 0x6b, 0xc6,
	0xad, 0x96, 0x55, 0xa5, 0xc5, 0x9e, 0x83, 0x56, 0xa0, 0xc1, 0x1a, 0x3c, 0x7b, 0x84, 0x97, 0x4b,
	0xd7, 0x8c, 0x5b, 0x0d, 0xab, 0x4e, 0x2b, 0x76, 0xec, 0x11, 0x36, 0x7f, 0x5a, 0x86, 0xf9, 0xcd,
	0x10, 0xdb, 0x11, 0x66, 0x33, 0x5a, 0xf8, 0x2b, 0x84, 0xa0, 0xc2, 0xba, 0x1a, 0xac, 0x2b, 0xfb,
	0x4f, 0xeb, 0x1c, 0x4c, 0x06, 0x62, 0x38, 0xfb, 0x8f, 0x2e, 0x41, 0xdd, 0x1d, 0xf8, 0x5e, 0x7f,
	0x1c, 0x0e, 0x97, 0xcb, 0xac, 0xbe, 0x46, 0xcb, 0xcf, 0xc2, 0x21, 0x5a, 0x85, 0xfa, 0x81, 0xeb,
	0x39, 0xfd, 0xb1, 0xeb, 0x2c, 0x57, 0xe8, 0x62, 0x36, 0x4a, 0xcb, 0x86, 0x55, 0xa3, 0x75, 0xcf,
	0x5c, 0x07, 0xbd, 0x07, 0xc0, 0x36, 0xd2, 0x8f, 0x4e, 0x03, 0xbc, 0x3c, 0x7b, 0xcd, 0xb8, 0x35,
	0xbf, 0xbe, 0x78, 0x27, 0xd9, 0xdf, 0x1d, 0xb6, 0x94, 0xfd, 0xd3, 0x00, 0x5b, 0x8d, 0x28, 0xfe,
	0x8b, 0xde, 0x84, 0x85, 0xc0, 0x0e, 0xb1, 0x17, 0xf5, 0xf9, 0x58, 0xd7, 0x59, 0xae, 0x32, 0xa8,
	0x2d, 0x5e, 0xcd, 0x06, 0xf5, 0x1c, 0xf4, 0x09, 0x34, 0x8f, 0xb1, 0x7b, 0xf4, 0x3c, 0xea, 0xbb,
	0xde, 0xa1, 0xbf, 0x5c, 0xbb, 0x66, 0xdc, 0x6a, 0xae, 0x77, 0xb2, 0xb3, 0x7f, 0xce, 0xba, 0x50,
	0xc4, 0x59, 0x70, 0x9c, 0xfc, 0x47, 0xf7, 0xa0, 0x42, 0x51, 0xb3, 0x5c, 0x67, 0xa3, 0x2e, 0x67,
	0x47, 0xc9, 0x08, 0xb7, 0x58, 0x4f, 0xf4, 0x10, 0x50, 0x88, 0x87, 0x76, 0x84, 0x1d, 0xb1, 0xae,
	0xa1, 0x4b, 0xa2, 0xe5, 0xc6, 0xb5, 0xf2, 0xad, 0xe6, 0xfa, 0x92, 0x34, 0xde, 0xe2, 0x9d, 0x38,
	0x96, 0xdb, 0xa1, 0x54, 0x7a, 0xec, 0x92, 0x08, 0x5d, 0x84, 0x2a, 0xf6, 0xec, 0x83, 0x21, 0x5e,
	0x86, 0x6b, 0xc6, 0xad, 0xba, 0x25, 0x4a, 0xe8, 0x1d, 0x40, 0xa3, 0xf1, 0x30, 0x72, 0xfb, 0x74,
	0xea, 0x71, 0x38, 0xe4, 0xd3, 0x37, 0xaf, 0x95, 0x6f, 0x35, 0xac, 0x05, 0xd6, 0xf2, 0xd4, 0x1d,
	0x3c, 0x0b, 0x87, 0x74, 0x12, 0x73, 0x13, 0x16, 0x14, 0x5a, 0x92, 0x80, 0x12, 0x29, 0x41, 0x17,
	0x27, 0x68, 0x2d, 0x12, 0x88, 0xba, 0x00, 0x55, 0x97, 0xf4, 0x3d, 0x7c, 0xcc, 0xa8, 0x5a, 0xb7,
	0x66, 0x5d, 0xb2, 0x83, 0x8f, 0xcd, 0x4d, 0x98, 0x7f, 0xc8, 0x60, 0x27, 0x0c, 0x51, 0x30, 0x47,
	0xba, 0xec, 0x92, 0xbc, 0x6c, 0xf3, 0x1c, 0x2c, 0x28, 0x93, 0x90, 0xc0, 0xfc, 0xcf, 0x12, 0xa0,
	0x67, 0x81, 0x13, 0xaf, 0x8e, 0xa1, 0xb0, 0x78, 0xf2, 0x98, 0x11, 0x4b, 0x1a, 0x46, 0x2c, 0xe7,
	0x30, 0x62, 0x25, 0x9f, 0x11, 0x67, 0x27, 0x19, 0x31, 0xc3, 0x2b, 0xd5, 0xd7, 0xe2, 0x95, 0xda,
	0xd7, 0xe4, 0x95, 0xfa, 0xab, 0xf2, 0x8a, 0x9e, 0x27, 0x1a, 0x7a, 0x9e, 0xb8, 0x00, 0xe7, 0x27,
	0xb0, 0x4e, 0x02, 0xf3, 0x2f, 0x4a, 0xb0, 0xb8, 0xd5, 0xfb, 0x62, 0x7d, 0xf7, 0xf0, 0xd0, 0x1d,
	0xb8, 0xf6, 0x70, 0x1a, 0x62, 0xbf, 0xa2, 0x10, 0x50, 0x4f, 0x79, 0xe5, 0xb5, 0x4f, 0xf9, 0xec,
	0x14, 0xa7, 0xfc, 0xe7, 0x4c, 0x39, 0x73, 0x09, 0x2e, 0x68, 0xb0, 0x45, 0x02, 0x73, 0x0c, 0x68,
	0xc3, 0x8e, 0x38, 0x86, 0x39, 0x34, 0x8a, 0x44, 0x13, 0x5a, 0xf1, 0xf2, 0x39, 0x71, 0x0c, 0x46,
	0x9c, 0xa6, 0xc0, 0x24, 0xa3, 0xe2, 0xd7, 0x91, 0x53, 0xe6, 0xfb, 0x70, 0x7e, 0x02, 0x2c, 0x09,
	0xd0, 0x2a, 0xc0, 0x98, 0xd5, 0xf5, 0x07, 0x5e, 0x24, 0xd4, 0x40, 0x83, 0xd7, 0x6c, 0x7a, 0x91,
	0xb9, 0x0d, 0x4b, 0x1b, 0x76, 0xb4, 0x8d, 0xa3, 0xee, 0x70, 0xc8, 0x47, 0x25, 0x64, 0xbf, 0x08,
	0x55, 0xff, 0xf0, 0x90, 0xe0, 0x78, 0x94, 0x28, 0xa1, 0x45, 0x98, 0x1d, 0xba, 0x23, 0x37, 0x62,
	0x44, 0x6f, 0x59, 0xbc, 0x60, 0xbe, 0x84, 0x05, 0xbe, 0x3a, 0x37, 0x7a, 0xce, 0x27, 0x3a, 0x43,
	0x48, 0xf0, 0xa5, 0x0b, 0xce, 0x11, 0x25, 0x3a, 0x84, 0x44, 0x76, 0x18, 0xf5, 0x23, 0xc2, 0x78,
	0xa7, 0x65, 0xd5, 0x58, 0x79, 0x9f, 0x50, 0xd9, 0x84, 0x3d, 0x87, 0x36, 0x54, 0x38, 0x5c, 0xec,
	0x39, 0xfb, 0xc4, 0x8c, 0x60, 0x59, 0xbf, 0x01, 0x12, 0x48, 0xf8, 0x4c, 0x30, 0xae, 0xc3, 0x67,
	0xb2, 0xe2, 0x18, 0x9f, 0x8c, 0x18, 0x2b, 0xd0, 0x88, 0xfc, 0xc8, 0x1e, 0x32, 0xbc, 0xf1, 0xad,
	0xd6, 0x59, 0x05, 0x45, 0xdb, 0x6f, 0x94, 0x01, 0x09, 0x38, 0x03, 0x7f, 0x34, 0xc2, 0x9e, 0xc3,
	0xb8, 0x68, 0x05, 0x1a, 0x03, 0xdf, 0x3b, 0x74, 0x8f, 0xd2, 0x2d, 0xd7, 0x79, 0x45, 0xcf, 0xa1,
	0x78, 0x73, 0x3d, 0x07, 0x9f, 0xc4, 0x78, 0x63, 0x05, 0x05, 0x49, 0x65, 0xbd, 0xb0, 0xab, 0x48,
	0xc2, 0x6e, 0x09, 0x6a, 0xe2, 0x88, 0x8b, 0x43, 0x50, 0x0d, 0xd8, 0xc1, 0x46, 0x97, 0xa1, 0x41,
	0xc6, 0x07, 0x64, 0x10, 0xba, 0x41, 0xc4, 0x78, 0xbf, 0x65, 0xa5, 0x15, 0xa8, 0x03, 0xf5, 0x60,
	0x68, 0x47, 0x87, 0x7e, 0x38, 0x62, 0x6c, 0xd5, 0xb2, 0x92, 0x32, 0xba, 0x0a, 0xcd, 0x00, 0x87,
	0xc4, 0xf7, 0xfa, 0x76, 0x14, 0x85, 0x4c, 0xcf, 0xb5, 0x2c, 0xe0, 0x55, 0xdd, 0x28, 0x0a, 0x15,
	0xa2, 0x34, 0xf2, 0x88, 0x02, 0x12, 0x51, 0x28, 0x79, 0x49, 0x64, 0x47, 0x63, 0xb2, 0xdc, 0xe4,
	0xac, 0xc3, 0x4b, 0x68, 0x19, 0x6a, 0x16, 0x3e, 0xda, 0xb2, 0x4f, 0xc9, 0xf2, 0x1c, 0x9f, 0x48,
	0x14, 0xe9, 0x88, 0x23, 0xec, 0x39, 0x38, 0x5c, 0x6e, 0x09, 0x4b, 0x85, 0x95, 0x28, 0xfb, 0x4a,
	0x12, 0x63, 0x9e, 0xef, 0x2b, 0x91, 0x0d, 0xe6, 0x09, 0x5c, 0xe3, 0x1c, 0xff, 0x74, 0x2c, 0xe4,
	0x80, 0x97, 0x50, 0x64, 0xd3, 0xf7, 0x0e, 0x29, 0x1f, 0xdf, 0x87, 0x0a, 0x3b, 0x4e, 0x06, 0x3b,
	0x4e, 0xab, 0x59, 0xf2, 0x2b, 0x14, 0xb4, 0x58, 0x57, 0x74, 0x1d, 0xe6, 0x5e, 0xe2, 0x90, 0xb8,
	0xbe, 0xc7, 0xe1, 0x72, 0x8a, 0x35, 0x45, 0x1d, 0x83, 0xfc, 0x06, 0x5c, 0x3f, 0x03, 0x32, 0x09,
	0xcc, 0xe7, 0xb0, 0xba, 0x85, 0x87, 0x05, 0x6b, 0xbb, 0x01, 0xf3, 0x09, 0xc3, 0xc8, 0x62, 0x61,
	0x2e, 0xe6, 0x1a, 0xc6, 0x8a, 0x53, 0x2c, 0xe7, 0x23, 0xb8, 0x52, 0x04, 0x89, 0x04, 0x94, 0x73,
	0x1c, 0x3c, 0x94, 0xa4, 0x40, 0xd5, 0xc1, 0x8c, 0x97, 0xff, 0xc6, 0x80, 0xd5, 0x6d, 0x1c, 0x15,
	0xac, 0x32, 0xe1, 0x5c, 0x23, 0x8f, 0x73, 0x4b, 0x7a, 0xce, 0x2d, 0x4b, 0x9c, 0x9b, 0x8a, 0x93,
	0x8a, 0x5e, 0x9c, 0xcc, 0x4a, 0xe2, 0x64, 0x62, 0xcb, 0xd5, 0xc9, 0x2d, 0x9f, 0xc2, 0x95, 0xa2,
	0x65, 0x93, 0x00, 0x7d, 0x0c, 0x0d, 0x4a, 0x4e, 0xf9, 0xf4, 0x9f, 0x41, 0xfe, 0x3a, 0xed, 0x7f,
	0xf6, 0xf1, 0xff, 0x81, 0xb0, 0xb6, 0xbb, 0x9e, 0xb3, 0x97, 0x9c, 0xb1, 0x57, 0x34, 0x5b, 0x94,
	0x03, 0x5b, 0xce, 0x1c, 0x58, 0xf3, 0x37, 0xcb, 0x70, 0x71, 0xd3, 0x8e, 0xec, 0xa1, 0x7f, 0x14,
	0x6f, 0x6f, 0x30, 0x72, 0x1e, 0x61, 0xec, 0xd0, 0x23, 0x31, 0xe0, 0x2d, 0x29, 0xa4, 0x86, 0xa8,
	0xe9, 0x39, 0x14, 0x73, 0x71, 0xb3, 0x04, 0xb3, 0x29, 0xea, 0xa8, 0x85, 0x9f, 0xd2, 0xb3, 0x2c,
	0xd3, 0xf3, 0x93, 0xf8, 0xa8, 0x31, 0x74, 0x55, 0x18, 0xba, 0x26, 0x14, 0xa1, 0xbc, 0x63, 0x71,
	0x10, 0x19, 0xba, 0x64, 0x01, 0x33, 0x5b, 0x2c, 0x60, 0xaa, 0x85, 0x02, 0xa6, 0x96, 0x27, 0x60,
	0xea, 0x7a, 0x01, 0xd3, 0xc8, 0x13, 0x30, 0x90, 0x27, 0x60, 0x9a, 0x8a, 0x80, 0xb9, 0x0a, 0x4d,
	0xa1, 0x1f, 0x23, 0x77, 0x84, 0x85, 0x58, 0x12, 0x2a, 0x73, 0xdf, 0x1d, 0x61, 0xf3, 0x07, 0x70,
	0x95, 0x1f, 0x74, 0x3d, 0x39, 0xe8, 0xf9, 0xf8, 0x26, 0xd4, 0x05, 0x7a, 0x89, 0x60, 0xb3, 0xeb,
	0x12, 0xde, 0x72, 0xc6, 0x25, 0x43, 0x4c, 0x33, 0x16, 0x62, 0x79, 0x10, 0x48, 0x60, 0x6e, 0xc1,
	0xe5, 0x2d, 0x3c, 0xcc, 0x5f, 0xc2, 0x54, 0x82, 0xc4, 0xfc, 0x90, 0xc9, 0xa3, 0x7c, 0x30, 0xf9,
	0x42, 0xe2, 0x00, 0x2e, 0x6f, 0xe3, 0x28, 0x1f, 0xfe, 0x2b, 0x19, 0x0b, 0xb4, 0xf6, 0xa5, 0x3d,
	0x14, 0x1a, 0xaf, 0x6e, 0xf1, 0x82, 0xf9, 0x2b, 0x4c, 0x0e, 0x15, 0xac, 0xee, 0xeb, 0xe1, 0xb9,
	0xf8, 0x48, 0x7f, 0x01, 0x4b, 0xdb, 0x98, 0x8b, 0x12, 0x22, 0xc9, 0x12, 0xba, 0xb7, 0x15, 0x2a,
	0x46, 0x1c, 0x7c, 0xd2, 0x1f, 0xd9, 0xb1, 0x08, 0xac, 0xb3, 0x8a, 0x27, 0xf6, 0xc9, 0x34, 0xb2,
	0x79, 0x0b, 0x16, 0x7a, 0xb4, 0x3b, 0x9b, 0x9c, 0x0a, 0x27, 0x22, 0xe9, 0xa4, 0xf2, 0x94, 0x3a,
	0xc9, 0xfc, 0x2f, 0x03, 0x96, 0xf5, 0x2b, 0x24, 0x01, 0xfa, 0x0e, 0x30, 0x3b, 0xa3, 0x3f, 0xb2,
	0x03, 0x31, 0xe7, 0x3d, 0x69, 0xce, 0xbc, 0x61, 0x77, 0xe8, 0x62, 0x9e, 0xd8, 0xc1, 0x43, 0x2f,
	0x0a, 0x4f, 0xad, 0xda, 0x80, 0x97, 0xd0, 0x03, 0xd5, 0x6c, 0x2a, 0xb1, 0xf9, 0x26, 0xcc, 0x74,
	0xd9, 0x00, 0xa5, 0xcc, 0xd5, 0xf9, 0x0c, 0xe6, 0xe4, 0xf9, 0x50, 0x1b, 0xca, 0x2f, 0xf0, 0xa9,
	0x40, 0x18, 0xfd, 0x8b, 0xee, 0x31, 0xb2, 0x8f, 0x39, 0x92, 0x54, 0x4b, 0x2c, 0x83, 0x20, 0x8b,
	0x77, 0xfc, 0xb8, 0xf4, 0xa1, 0x61, 0x7e, 0x04, 0x0b, 0xf1, 0x06, 0x2c, 0xdb, 0x7b, 0x21, 0x14,
	0x92, 0xe7, 0xd0, 0x43, 0x2e, 0x14, 0x12, 0x2b, 0x50, 0x80, 0x29, 0x65, 0xe9, 0x5f, 0xf3, 0x17,
	0xa0, 0xad, 0x0e, 0x25, 0xc1, 0x34, 0x86, 0xb8, 0x79, 0x9f, 0x71, 0x22, 0xb7, 0xfc, 0x4f, 0x37,
	0x7d, 0xef, 0x25, 0x0e, 0xa3, 0xc4, 0xd2, 0xa7, 0x0b, 0x10, 0xa0, 0x8c, 0x14, 0xd4, 0x16, 0xd3,
	0x46, 0xb9, 0x43, 0xa6, 0x04, 0xdc, 0x87, 0xf3, 0x5d, 0xc7, 0x79, 0x46, 0x70, 0xd8, 0xdb, 0xda,
	0x38, 0xcd, 0xb9, 0x81, 0x95, 0x65, 0xd5, 0xd2, 0x86, 0xf2, 0x58, 0x28, 0xe0, 0x96, 0x45, 0xff,
	0x32, 0x76, 0x25, 0x7d, 0x07, 0x0f, 0x71, 0x84, 0xc5, 0x01, 0xab, 0xbb, 0x64, 0x8b, 0x95, 0xcd,
	0x8b, 0xb0, 0x38, 0x09, 0x80, 0x04, 0xe6, 0x06, 0x9c, 0xdf, 0xc6, 0xd1, 0x19, 0x80, 0x8d, 0x0c,
	0xe0, 0x0c, 0xb6, 0xef, 0xc3, 0xe2, 0xe4, 0x1c, 0xdc, 0xe1, 0x30, 0x56, 0xf7, 0x5c, 0x1b, 0xbb,
	0x7c, 0xbf, 0xff, 0x60, 0xa4, 0xc4, 0x9d, 0xe2, 0xfa, 0xff, 0x1e, 0x54, 0xfd, 0x20, 0x72, 0x7d,
	0x8f, 0x81, 0x9d, 0x5f, 0x5f, 0xc9, 0x32, 0xe5, 0x77, 0xc7, 0x38, 0x3c, 0xdd, 0x65, 0x5d, 0x2c,
	0xd1, 0x55, 0x6b, 0x8c, 0x08, 0xac, 0x55, 0x52, 0xac, 0xbd, 0x8e, 0x03, 0xca, 0xfc, 0x46, 0xca,
	0x5f, 0xf1, 0x35, 0x1a, 0xdd, 0x52, 0xcc, 0x4d, 0xfd, 0xb1, 0xe1, 0x27, 0xfa, 0x6f, 0xcb, 0xe9,
	0xe6, 0x29, 0x36, 0xe8, 0xe6, 0x3f, 0x48, 0x76, 0x68, 0x9c, 0xb9, 0x43, 0xe6, 0xb8, 0x88, 0x77,
	0x99, 0x0a, 0xe0, 0x92, 0x5e, 0x00, 0x97, 0x65, 0x01, 0xfc, 0x5a, 0x17, 0xf1, 0x3b, 0x70, 0xee,
	0x2b, 0x0a, 0xbd, 0x2f, 0xae, 0xe3, 0x6c, 0x9b, 0x14, 0x53, 0x75, 0xb6, 0x92, 0x05, 0xd6, 0xf8,
	0x94, 0xb5, 0xb1, 0xdb, 0xd0, 0xb4, 0xee, 0x39, 0x41, 0x8c, 0x5a, 0x4a, 0x8c, 0x37, 0xa0, 0x25,
	0x24, 0x90, 0xd0, 0xf2, 0x5c, 0xf9, 0xcf, 0xf1, 0xca, 0x3d, 0xae, 0xeb, 0xd3, 0x3b, 0x64, 0x43,
	0xb9, 0x43, 0x26, 0xc2, 0x01, 0x64, 0xe1, 0x10, 0x73, 0x41, 0x53, 0xe2, 0x02, 0x99, 0xd3, 0xe6,
	0x54, 0x4e, 0x53, 0x3c, 0xa4, 0xad, 0x8c, 0x87, 0xf4, 0x5f, 0x98, 0xc5, 0x2c, 0xd1, 0x6d, 0x68,
	0x33, 0x7a, 0x9c, 0x4e, 0xe9, 0x1f, 0x7b, 0x25, 0x42, 0xc5, 0xfc, 0x50, 0x99, 0x9e, 0xe3, 0x6f,
	0x03, 0x52, 0x08, 0xc5, 0xba, 0x73, 0x4a, 0x59, 0x6d, 0x89, 0x4a, 0x6c, 0x02, 0xf3, 0xfb, 0x29,
	0x13, 0xf3, 0xdd, 0x90, 0x00, 0xbd, 0x2f, 0xf8, 0x83, 0xd2, 0x31, 0xd6, 0xb5, 0x39, 0x1a, 0x20,
	0xed, 0x47, 0xb7, 0xc0, 0xf4, 0x69, 0xac, 0xec, 0x59, 0xc1, 0xfc, 0x35, 0x98, 0xdb, 0xf3, 0xc3,
	0xd4, 0xaf, 0xf0, 0x00, 0x2a, 0xc4, 0x0f, 0x23, 0x8d, 0x06, 0x97, 0xbb, 0xb1, 0x02, 0x57, 0x4c,
	0xac, 0x7b, 0xe7, 0x03, 0x68, 0x24, 0x55, 0xb2, 0x6e, 0x69, 0x70, 0xdd, 0xb2, 0x28, 0xeb, 0x96,
	0x86, 0xac, 0x3f, 0x16, 0xa0, 0x25, 0x4d, 0x4c, 0x02, 0xf3, 0x1d, 0x98, 0xe7, 0xd2, 0x70, 0x0a,
	0x72, 0x99, 0xe7, 0x60, 0x41, 0xe9, 0x4c, 0x02, 0xf3, 0x03, 0x38, 0xb7, 0x61, 0x47, 0x99, 0x29,
	0xa6, 0x91, 0xee, 0x0f, 0x98, 0x67, 0x28, 0x33, 0x1d, 0xb5, 0x40, 0xb9, 0xb0, 0x76, 0x24, 0xbb,
	0x0b, 0x44, 0x15, 0x35, 0x4d, 0xc6, 0x70, 0x4e, 0xd8, 0xdc, 0x07, 0x29, 0x3c, 0x71, 0x68, 0x8c,
	0xf4, 0xd0, 0x14, 0xdc, 0xc7, 0xd4, 0xe3, 0x5e, 0x9e, 0x4e, 0xb8, 0x2d, 0x02, 0xca, 0x82, 0xe5,
	0x8a, 0xe2, 0x99, 0x47, 0xbe, 0xd6, 0x72, 0xa8, 0x12, 0x9a, 0x9c, 0x83, 0x04, 0xe6, 0x79, 0x38,
	0xb7, 0x8d, 0xa3, 0x04, 0x68, 0x48, 0x2c, 0xfc, 0x15, 0x5d, 0x46, 0xb6, 0x92, 0x04, 0xe6, 0x01,
	0xcc, 0x25, 0x56, 0x8d, 0xb0, 0xd1, 0x62, 0x68, 0x44, 0xa0, 0xbe, 0x2e, 0xc0, 0x11, 0xf4, 0x20,
	0xa3, 0x36, 0x56, 0x0b, 0x0e, 0xd1, 0xc3, 0x93, 0xf8, 0x18, 0x99, 0x5f, 0x42, 0x4b, 0x82, 0xf1,
	0x33, 0x3e, 0x15, 0x7f, 0x6f, 0xc4, 0x0e, 0xb3, 0xd4, 0x09, 0xb9, 0x0a, 0xf0, 0xdc, 0x26, 0x7d,
	0x21, 0xd5, 0x0c, 0x76, 0x5e, 0x1b, 0xcf, 0x6d, 0x22, 0xfc, 0x69, 0x79, 0x4e, 0xb3, 0x37, 0x61,
	0x21, 0x95, 0x96, 0xb2, 0xef, 0xac, 0x95, 0xc8, 0x4b, 0x76, 0x97, 0x32, 0x13, 0xa9, 0xaa, 0x38,
	0xd2, 0x84, 0xb1, 0xf7, 0x90, 0x5d, 0xac, 0x26, 0x24, 0xef, 0xec, 0xa4, 0xe4, 0x35, 0x7f, 0xbd,
	0x0a, 0x8d, 0x64, 0xaf, 0x3f, 0x67, 0x77, 0xfd, 0x4d, 0x98, 0x1b, 0xe1, 0xd1, 0x01, 0x0e, 0xfb,
	0x03, 0x7f, 0xec, 0x45, 0x92, 0xcb, 0xbe, 0xc9, 0xeb, 0x37, 0x69, 0x35, 0x45, 0x5d, 0xe0, 0x93,
	0x48, 0x74, 0x12, 0xfe, 0x2f, 0x5a, 0xc3, 0x9b, 0x57, 0x40, 0xf8, 0x3c, 0xfb, 0x76, 0x14, 0x3b,
	0xc0, 0x78, 0x45, 0x37, 0x52, 0x22, 0x02, 0xf5, 0xc9, 0x88, 0xc0, 0x0a, 0x34, 0x06, 0x2c, 0x84,
	0x42, 0xc7, 0xf2, 0xeb, 0x66, 0x9d, 0x57, 0x74, 0xb3, 0x8a, 0x14, 0xa6, 0x53, 0xa4, 0x8b, 0x30,
	0xeb, 0x87, 0xf1, 0x55, 0xb4, 0x61, 0xf1, 0x02, 0xfa, 0x04, 0xe6, 0x24, 0xc5, 0x4a, 0x96, 0xe7,
	0xf2, 0xf9, 0x8b, 0xef, 0x3f, 0x48, 0x54, 0x2d, 0xa1, 0xfb, 0x7f, 0xe9, 0xe2, 0x63, 0xb1, 0x7f,
	0xee, 0x43, 0x6b, 0xd0, 0x1a, 0xbe, 0xff, 0x8c, 0x67, 0x79, 0xfe, 0x95, 0x7c, 0xe3, 0x37, 0xa0,
	0xf5, 0x94, 0x62, 0x92, 0xc2, 0x62, 0x57, 0xeb, 0x05, 0xce, 0x5d, 0x4a, 0x25, 0xa3, 0x21, 0xe9,
	0xf3, 0x6b, 0x5d, 0x9b, 0xb1, 0x6e, 0xcd, 0x25, 0x9f, 0xd1, 0x62, 0xe2, 0x5c, 0x3f, 0x37, 0x75,
	0x58, 0x64, 0x09, 0x6a, 0x23, 0xdf, 0x77, 0x28, 0x4b, 0x21, 0xce, 0xeb, 0xb4, 0xd8, 0x73, 0x72,
	0xe2, 0x25, 0xe7, 0x5f, 0x3f, 0xb6, 0xb6, 0x38, 0x45, 0x6c, 0xed, 0x82, 0x3e, 0x8e, 0xb2, 0x0e,
	0x73, 0x32, 0x18, 0x34, 0x0f, 0xa5, 0xe4, 0x08, 0x94, 0x5c, 0x2d, 0xf7, 0x9b, 0x16, 0x74, 0x36,
	0x9f, 0xe3, 0xc1, 0x8b, 0x64, 0xe3, 0xcf, 0x5c, 0xe7, 0xe1, 0x89, 0x4b, 0x22, 0x22, 0xf4, 0x50,
	0xc2, 0x89, 0x5c, 0x94, 0x26, 0x5c, 0x58, 0x20, 0x4e, 0x1f, 0xc0, 0x4a, 0xee, 0x9c, 0x24, 0x60,
	0x7b, 0x65, 0x25, 0x21, 0x51, 0x44, 0xc9, 0x7c, 0x0b, 0xea, 0x8f, 0x7d, 0xdb, 0x79, 0xe2, 0x87,
	0x98, 0xf2, 0xf8, 0xd0, 0x26, 0x51, 0x3f, 0xb0, 0x8f, 0x70, 0x7c, 0xc5, 0xa5, 0x15, 0x4f, 0xed,
	0x23, 0x6c, 0xfe, 0xb3, 0x01, 0x57, 0x65, 0x0b, 0xe1, 0xe1, 0xc9, 0x60, 0x38, 0x76, 0x70, 0x22,
	0x92, 0xe9, 0xca, 0xef, 0x41, 0x63, 0xe8, 0xdb, 0x4e, 0x7f, 0xe4, 0x87, 0x58, 0x98, 0xbe, 0xe7,
	0x25, 0x72, 0xc4, 0x80, 0xac, 0xfa, 0x30, 0x06, 0xb9, 0x08, 0xb3, 0x9c, 0x59, 0x85, 0x58, 0x64,
	0x05, 0x74, 0x13, 0xe6, 0x13, 0xc5, 0x10, 0x32, 0x61, 0x5e, 0x66, 0xc8, 0x6f, 0xa5, 0xb5, 0x54,
	0xa2, 0xbf, 0x8e, 0xfd, 0x6a, 0xfe, 0xb6, 0x01, 0xd7, 0x8a, 0xf7, 0xf1, 0xda, 0x32, 0x5e, 0xd9,
	0x7e, 0x69, 0x8a, 0xed, 0x9b, 0x77, 0xe1, 0xd2, 0x86, 0x1d, 0x0d, 0x9e, 0x4f, 0x50, 0x4e, 0xc4,
	0xdb, 0xc7, 0xb1, 0x22, 0x6b, 0x59, 0xec, 0xbf, 0xf9, 0xc7, 0x06, 0x74, 0xf2, 0x46, 0x90, 0x00,
	0xf5, 0xa0, 0x1a, 0x62, 0x32, 0x1e, 0xc6, 0x76, 0xd5, 0x7d, 0x09, 0x7c, 0xfe, 0xb0, 0x3b, 0x16,
	0x1b, 0xc3, 0xed, 0x2c, 0x31, 0x41, 0xe7, 0x23, 0x68, 0x4a, 0xd5, 0x9a, 0x7b, 0xbc, 0x62, 0x6b,
	0xd5, 0x65, 0x5b, 0xeb, 0x4f, 0x0c, 0x58, 0x92, 0x62, 0x8b, 0x4f, 0x63, 0x01, 0x7c, 0x86, 0x4d,
	0xbc, 0x04, 0x35, 0x26, 0xbd, 0x13, 0xde, 0xae, 0xd2, 0x22, 0x37, 0x96, 0x95, 0x8b, 0xac, 0x28,
	0xa9, 0x32, 0xb9, 0x92, 0x91, 0xc9, 0xd7, 0x60, 0x8e, 0x8c, 0x0f, 0xb2, 0xd1, 0x42, 0x20, 0xe3,
	0x03, 0x71, 0xe3, 0x30, 0x3b, 0xb0, 0xac, 0x5f, 0x25, 0x09, 0xcc, 0xcf, 0xa0, 0x4e, 0x2b, 0xce,
	0x52, 0x6d, 0xaf, 0xba, 0x64, 0xd3, 0x82, 0xcb, 0x8c, 0x0e, 0x79, 0xe8, 0x59, 0x17, 0x1a, 0xcc,
	0x95, 0x18, 0x4f, 0xe6, 0xa1, 0x78, 0x51, 0x5c, 0xad, 0x31, 0xb6, 0x33, 0xaf, 0xc2, 0x6a, 0xc1,
	0x9c, 0x24, 0x30, 0x1f, 0xc0, 0xc5, 0x1e, 0x49, 0x18, 0xbc, 0x3b, 0x4c, 0x83, 0xba, 0x45, 0x06,
	0x93, 0x79, 0x1f, 0x96, 0xb4, 0xc3, 0xb8, 0x34, 0x49, 0xf8, 0x8c, 0x6d, 0x8f, 0x97, 0xcc, 0x97,
	0xb0, 0xd8, 0x75, 0x7e, 0x38, 0x26, 0xc2, 0x05, 0x82, 0x87, 0xbe, 0x77, 0x74, 0x06, 0xd5, 0x35,
	0xf7, 0xc3, 0x92, 0xee, 0x7e, 0x58, 0xe8, 0xd0, 0x58, 0x82, 0x0b, 0x1a, 0xb8, 0x24, 0x30, 0x3f,
	0x12, 0xc7, 0x65, 0x1b, 0x47, 0xd2, 0x6d, 0x27, 0x76, 0x32, 0x14, 0x6e, 0xff, 0x1f, 0x0d, 0x58,
	0xc9, 0x1d, 0x4b, 0x02, 0xf4, 0x04, 0x6a, 0x7c, 0xd7, 0x31, 0x9d, 0xde, 0xcb, 0x1e, 0x36, 0xfd,
	0x40, 0x71, 0xda, 0x88, 0xf0, 0xb7, 0x89, 0x39, 0x3a, 0x16, 0xd5, 0x23, 0x69, 0x83, 0xe6, 0x72,
	0x73, 0x5b, 0x75, 0x9c, 0x5d, 0xd4, 0xc9, 0x23, 0xac, 0x38, 0xcd, 0x3e, 0x81, 0xa6, 0xd4, 0x82,
	0x6e, 0x43, 0xd5, 0x65, 0xff, 0x0a, 0x25, 0x9a, 0xe8, 0x63, 0xfe, 0x91, 0x01, 0xb0, 0x87, 0x4f,
	0xba, 0x47, 0x58, 0xe8, 0xff, 0x32, 0x61, 0xc1, 0x9f, 0xf2, 0xad, 0xf9, 0x75, 0x24, 0x8d, 0x7c,
	0x46, 0x70, 0xb8, 0x87, 0x4f, 0x2c, 0xda, 0x8c, 0x6e, 0x41, 0xdb, 0x25, 0x7d, 0xfb, 0x08, 0xf7,
	0xc7, 0x1e, 0xbb, 0xbb, 0x62, 0x47, 0xc8, 0x87, 0x79, 0x97, 0x74, 0x8f, 0xf0, 0xb3, 0xb8, 0x96,
	0x9e, 0x4f, 0xda, 0xed, 0x14, 0xdb, 0x61, 0x7f, 0xe4, 0x7a, 0xc2, 0x58, 0x05, 0xfb, 0x08, 0x7f,
	0x81, 0xed, 0xf0, 0x89, 0xeb, 0xa9, 0x3d, 0xec, 0x13, 0x71, 0xc2, 0x93, 0x1e, 0xf6, 0x89, 0xf9,
	0x6f, 0x25, 0x40, 0x4f, 0xc7, 0x07, 0x43, 0x97, 0x3c, 0xc7, 0xe1, 0x54, 0xb6, 0xe8, 0x03, 0xa8,
	0xda, 0x41, 0x10, 0x33, 0xd9, 0xfc, 0xfa, 0x15, 0xf9, 0x6c, 0x29, 0x33, 0x75, 0x83, 0xc0, 0x75,
	0xac, 0x59, 0x3b, 0x08, 0x7a, 0x0e, 0xfa, 0xa6, 0x14, 0xd8, 0xe0, 0x17, 0xa7, 0xeb, 0xb9, 0x03,
	0x9f, 0x8a, 0x8e, 0x52, 0xec, 0x63, 0x15, 0xe0, 0x00, 0x1f, 0xb9, 0x1e, 0x8f, 0x2e, 0xd0, 0x7d,
	0x94, 0xad, 0x06, 0xab, 0xd9, 0x77, 0xb9, 0x07, 0x82, 0xd9, 0xe2, 0xb4, 0x71, 0x96, 0x35, 0xd6,
	0xb0, 0xe7, 0xb0, 0x26, 0x7a, 0x4d, 0x7e, 0xee, 0x1f, 0x33, 0x5b, 0xb6, 0x08, 0xe8, 0xde, 0x73,
	0xff, 0x98, 0x12, 0xc2, 0x62, 0xdd, 0xe9, 0x8c, 0x8e, 0x7d, 0x4a, 0xfa, 0x21, 0x3e, 0x8a, 0x63,
	0x29, 0xb4, 0x6c, 0xe1, 0x23, 0xf4, 0x16, 0x94, 0xa9, 0x7a, 0xe7, 0x89, 0x4c, 0x17, 0xe4, 0x7b,
	0x77, 0x42, 0x6b, 0x8b, 0xf6, 0x30, 0x7f, 0x6c, 0xc0, 0x52, 0xcf, 0x23, 0x38, 0x8c, 0x54, 0x50,
	0xc5, 0xd1, 0xd4, 0x49, 0x72, 0x88, 0x68, 0x6a, 0xac, 0xcd, 0x4a, 0xa9, 0x36, 0x43, 0x5d, 0x00,
	0x0f, 0x1f, 0xf7, 0x5d, 0x06, 0x45, 0x20, 0xd6, 0xcc, 0x9d, 0x6c, 0x07, 0x1f, 0xf3, 0xf5, 0x58,
	0x0d, 0x2f, 0xfe, 0x4b, 0x85, 0xb8, 0x7e, 0x91, 0x4c, 0xee, 0x2d, 0xb3, 0xd8, 0xe0, 0xc4, 0x8a,
	0x8a, 0x2f, 0xfb, 0x3b, 0x70, 0x29, 0x67, 0x18, 0x09, 0x5e, 0x63, 0xe7, 0xe6, 0x40, 0xb3, 0x8c,
	0x67, 0xae, 0x43, 0x7e, 0x96, 0x2e, 0x22, 0xf3, 0x40, 0xb3, 0x68, 0x0e, 0xe4, 0xb5, 0x16, 0xad,
	0x23, 0x97, 0xf9, 0x3e, 0x2c, 0x71, 0x79, 0x3b, 0xc9, 0x10, 0x05, 0xe8, 0xec, 0xc0, 0xb2, 0x7e,
	0x14, 0x09, 0xcc, 0x77, 0x35, 0xab, 0xde, 0x38, 0x15, 0xf6, 0xcf, 0x84, 0x37, 0xc1, 0xdc, 0x85,
	0x4e, 0x5e, 0x77, 0xbe, 0x4b, 0x16, 0x5a, 0x9c, 0x0c, 0xa7, 0xe8, 0x76, 0x49, 0xbb, 0xd2, 0xb5,
	0x4d, 0x4c, 0x28, 0x9c, 0xb0, 0xe6, 0xb7, 0x34, 0x6b, 0x4b, 0x5c, 0x63, 0xd3, 0x38, 0x7a, 0x7e,
	0xdf, 0x60, 0x6e, 0x76, 0x7e, 0x3e, 0x23, 0x3b, 0x72, 0x49, 0xc4, 0x91, 0x25, 0x59, 0x0d, 0x86,
	0x62, 0x35, 0x5c, 0x81, 0x26, 0xd7, 0x71, 0x7d, 0x5a, 0x21, 0x04, 0x67, 0x83, 0x69, 0x39, 0xaa,
	0xd1, 0xa9, 0x44, 0x74, 0xdc, 0xd3, 0xbe, 0x94, 0x2a, 0x42, 0x81, 0x82, 0xe3, 0x9e, 0xc6, 0x5a,
	0xd2, 0x84, 0xd6, 0xc8, 0xf7, 0xbd, 0xb4, 0x0b, 0xbf, 0x48, 0x37, 0x69, 0x65, 0x6c, 0xf7, 0x30,
	0x65, 0x39, 0xb1, 0x2c, 0x12, 0x98, 0x7f, 0x5a, 0x82, 0xb6, 0x94, 0x27, 0xf8, 0xd9, 0x3a, 0x5d,
	0xec, 0x96, 0x12, 0x10, 0xe6, 0xb8, 0xbd, 0x29, 0x07, 0xdc, 0x32, 0x03, 0x24, 0x4d, 0x92, 0x46,
	0x86, 0x3b, 0x3f, 0x35, 0x64, 0x67, 0xc1, 0xff, 0xef, 0x4c, 0x52, 0xf3, 0x03, 0x38, 0x97, 0xd9,
	0xf5, 0x94, 0x1c, 0xf1, 0x3d, 0x98, 0xdf, 0xc3, 0x76, 0x38, 0x78, 0x5e, 0x98, 0x53, 0xbb, 0x0a,
	0xc0, 0xae, 0x61, 0xe9, 0xc5, 0x68, 0xd6, 0x62, 0x17, 0x33, 0x7e, 0x8b, 0x5f, 0x82, 0x1a, 0x6b,
	0x4e, 0x52, 0x85, 0xaa, 0xb4, 0xd8, 0x73, 0xcc, 0x7f, 0x2d, 0xc1, 0x82, 0x32, 0x3d, 0x09, 0xd0,
	0xa6, 0x86, 0x7a, 0x37, 0x14, 0xa1, 0xaf, 0xf4, 0xd7, 0x12, 0xef, 0x75, 0x17, 0x84, 0xae, 0x42,
	0xd3, 0xc3, 0xd8, 0xe9, 0x73, 0x9b, 0x9c, 0x91, 0xa9, 0x6e, 0x01, 0xad, 0xe2, 0xe8, 0xeb, 0xfc,
	0xe1, 0x99, 0x5c, 0x51, 0xe0, 0xce, 0x4c, 0xae, 0x8e, 0x65, 0xb6, 0x20, 0x71, 0x75, 0x4c, 0x93,
	0x57, 0x2b, 0x52, 0xf2, 0xea, 0xeb, 0x05, 0x76, 0xba, 0xf1, 0x71, 0x78, 0x48, 0x0a, 0x09, 0x56,
	0x70, 0x2b, 0x3f, 0x1f, 0xb3, 0x4a, 0x32, 0x05, 0x09, 0xcc, 0x4f, 0x61, 0xc9, 0xc2, 0x41, 0xec,
	0x8d, 0xfe, 0x2c, 0xf6, 0xcf, 0xe8, 0x3d, 0xa8, 0x8a, 0x8d, 0x5a, 0xca, 0xd8, 0xa8, 0x97, 0x72,
	0x66, 0x22, 0x81, 0xf9, 0x1f, 0x06, 0x9c, 0x4f, 0x62, 0x9e, 0xff, 0xb7, 0xdc, 0x8b, 0xdf, 0xd0,
	0xb9, 0x17, 0xe7, 0x15, 0xcf, 0xcd, 0xe7, 0x92, 0xa7, 0x31, 0x13, 0xf1, 0x91, 0xb1, 0x5d, 0x55,
	0xb1, 0xfd, 0xa5, 0x72, 0xa3, 0x4b, 0x37, 0x4e, 0x31, 0xfb, 0x2d, 0xd5, 0xf9, 0xc5, 0x55, 0xe1,
	0x95, 0x89, 0x98, 0xbb, 0x82, 0x2c, 0x25, 0xb5, 0xf2, 0x63, 0xb8, 0x94, 0x33, 0xf9, 0xd9, 0x09,
	0x96, 0xeb, 0x70, 0x41, 0x0e, 0x41, 0xa7, 0xab, 0x2a, 0xd0, 0x9b, 0xdf, 0x83, 0x8b, 0xba, 0x31,
	0x24, 0x40, 0x1b, 0xba, 0xad, 0x5c, 0x9f, 0x48, 0xd2, 0x19, 0x0e, 0x0b, 0x76, 0xf3, 0x69, 0x9c,
	0x31, 0xa9, 0x59, 0xd4, 0xab, 0xe5, 0x7c, 0xfe, 0xa4, 0x04, 0x17, 0xf5, 0x00, 0xbf, 0x36, 0xce,
	0xd1, 0x65, 0x68, 0x84, 0xd8, 0x1e, 0x92, 0x41, 0xec, 0x51, 0x31, 0xac, 0xb4, 0x02, 0x5d, 0xa1,
	0x87, 0x3a, 0x8a, 0x9b, 0xcb, 0xac, 0x59, 0xaa, 0xa1, 0xc8, 0xe5, 0xee, 0x50, 0x2f, 0x12, 0x46,
	0x77, 0x8d, 0x39, 0x43, 0x3d, 0x96, 0xa6, 0xc5, 0x3d, 0xc5, 0xc2, 0x99, 0x5c, 0xb6, 0x98, 0x4a,
	0xde, 0xe4, 0x4e, 0x64, 0x4e, 0x12, 0x76, 0xce, 0x39, 0x87, 0xf1, 0x23, 0xc8, 0xd2, 0xa6, 0xae,
	0x42, 0x53, 0x38, 0x1d, 0x98, 0xbd, 0x5e, 0x63, 0x83, 0x81, 0x57, 0x31, 0x93, 0xfd, 0x32, 0x34,
	0x1c, 0xff, 0xd8, 0xe3, 0x8b, 0xaa, 0xf3, 0x35, 0x27, 0x15, 0xe6, 0x8f, 0x98, 0xc7, 0x27, 0x87,
	0xb0, 0xdf, 0xd6, 0x48, 0xeb, 0x29, 0xe8, 0x2a, 0x89, 0xea, 0xc2, 0xec, 0x96, 0xdf, 0x29, 0x89,
	0xfb, 0x60, 0x77, 0x8a, 0x44, 0xd5, 0x62, 0xc9, 0xab, 0xc9, 0x1c, 0x93, 0x93, 0xbf, 0x2a, 0x99,
	0xe4, 0x2f, 0x39, 0xb7, 0x6b, 0x36, 0x2f, 0xb7, 0xab, 0x2a, 0xe7, 0x76, 0x49, 0x29, 0xae, 0x35,
	0x25, 0xc5, 0x15, 0x41, 0x65, 0xe8, 0x7a, 0x2f, 0x18, 0x66, 0x1b, 0x16, 0xfb, 0x9f, 0x4d, 0xdf,
	0x6a, 0x64, 0xd3, 0xb7, 0xe8, 0x6c, 0xb6, 0x93, 0x7a, 0xe7, 0x5b, 0x56, 0xd5, 0x76, 0x98, 0x88,
	0xff, 0x04, 0x5a, 0x7b, 0x82, 0x16, 0x5d, 0x66, 0x74, 0xae, 0x29, 0xa6, 0xf2, 0xc4, 0x1d, 0xbb,
	0x2b, 0x27, 0xe3, 0xb4, 0xa9, 0x3a, 0x4f, 0x07, 0x93, 0xc0, 0xfc, 0x77, 0x23, 0x8d, 0x16, 0xf1,
	0xf9, 0x54, 0x6e, 0x32, 0xb2, 0xdc, 0x54, 0x80, 0xe5, 0x34, 0xbb, 0xad, 0xa2, 0x64, 0xb7, 0xa5,
	0xa7, 0x73, 0x56, 0x7f, 0x3a, 0xab, 0x72, 0xe8, 0xb8, 0x28, 0xe7, 0x77, 0x09, 0x6a, 0x87, 0x7e,
	0xd8, 0xb7, 0x83, 0x80, 0x61, 0xb3, 0x6e, 0x55, 0x0f, 0xfd, 0xb0, 0x1b, 0x04, 0x32, 0xba, 0x1a,
	0x0a, 0xba, 0xbe, 0x84, 0xf9, 0x6d, 0x65, 0xc7, 0xe8, 0x36, 0xe5, 0x85, 0xd4, 0x57, 0x95, 0x87,
	0x30, 0xde, 0xa9, 0x98, 0x3d, 0xef, 0x40, 0x6b, 0x0b, 0x0f, 0x55, 0xdc, 0x25, 0xfc, 0x19, 0xfb,
	0x67, 0x1a, 0x31, 0x83, 0x12, 0x8a, 0x7e, 0xb9, 0x3f, 0x09, 0xcc, 0x7b, 0xa9, 0x98, 0x4d, 0xc2,
	0x09, 0x5b, 0xc2, 0xe2, 0x4e, 0xdf, 0x40, 0x95, 0xd3, 0x37, 0x50, 0xe6, 0xa1, 0xc8, 0xe1, 0x64,
	0xf1, 0x14, 0x31, 0xa4, 0xe8, 0x52, 0xa6, 0x92, 0xb3, 0x94, 0x25, 0xa7, 0x04, 0xa7, 0x2c, 0xbf,
	0xb5, 0x32, 0x77, 0x52, 0x61, 0x2e, 0xaf, 0x8c, 0xb9, 0x9a, 0xab, 0x6c, 0x7c, 0x8c, 0xc1, 0xcb,
	0xda, 0x50, 0x8f, 0x18, 0x63, 0x89, 0xbe, 0xe6, 0x3a, 0x2c, 0x49, 0x26, 0x68, 0x6f, 0xeb, 0x91,
	0x1f, 0x3e, 0xf1, 0x7d, 0x47, 0xec, 0x35, 0x8e, 0x9a, 0x18, 0x72, 0xd4, 0x84, 0x5e, 0x87, 0xf5,
	0x63, 0x0a, 0x9f, 0x03, 0x99, 0xf7, 0x58, 0x3c, 0x96, 0x07, 0x40, 0x37, 0xed, 0xc1, 0x73, 0x7c,
	0xa6, 0xe7, 0xec, 0x17, 0x59, 0xb0, 0x56, 0x19, 0xf1, 0xba, 0x3e, 0x75, 0xf3, 0xaf, 0x0c, 0x58,
	0x14, 0x57, 0x95, 0x10, 0xdb, 0xa3, 0x47, 0x7e, 0x38, 0xc0, 0x9b, 0xbe, 0x77, 0x48, 0xb7, 0xc9,
	0x92, 0xdd, 0xd2, 0x6d, 0xd2, 0x62, 0xb1, 0xe0, 0x92, 0x2e, 0x5e, 0x65, 0xe5, 0xe2, 0xd5, 0x61,
	0xea, 0xc0, 0x4d, 0x52, 0x2c, 0xe8, 0x29, 0x11, 0x65, 0x16, 0x8e, 0x61, 0xce, 0x1b, 0x3b, 0x3e,
	0x71, 0x35, 0x56, 0xee, 0x46, 0xb1, 0xec, 0xb2, 0x23, 0x49, 0x76, 0x75, 0x23, 0xf3, 0x01, 0x74,
	0x62, 0xc6, 0xcc, 0xac, 0x5a, 0xd0, 0x47, 0xbb, 0x70, 0x73, 0x15, 0x56, 0x72, 0x87, 0x91, 0xc0,
	0xfc, 0x2e, 0x74, 0x62, 0x69, 0xa3, 0x99, 0xf5, 0x3d, 0xa8, 0xd0, 0x69, 0x84, 0xdc, 0xba, 0x9a,
	0xc5, 0x6b, 0x76, 0x04, 0xeb, 0x4c, 0x21, 0xe6, 0x4e, 0x49, 0x02, 0xf3, 0x9f, 0x8c, 0x34, 0x9d,
	0x2d, 0xd3, 0x1e, 0x67, 0x2a, 0xbd, 0x5a, 0xc6, 0x67, 0xc1, 0x33, 0x07, 0x89, 0x34, 0x15, 0x85,
	0x34, 0xdf, 0x4c, 0xc4, 0x20, 0xb7, 0x12, 0x6f, 0x9e, 0xb1, 0x35, 0x61, 0x33, 0x8a, 0x41, 0xe6,
	0x38, 0x8d, 0x5a, 0x69, 0xb7, 0x40, 0x02, 0xf4, 0x0d, 0xae, 0x06, 0x65, 0xa5, 0x7b, 0x26, 0xfe,
	0x98, 0x9e, 0x64, 0x1a, 0x57, 0x1f, 0xd8, 0xdf, 0x83, 0x1b, 0x1a, 0xb0, 0xdd, 0x41, 0xe4, 0xbe,
	0xc4, 0xf4, 0x2a, 0x1f, 0xe3, 0xef, 0x95, 0x32, 0x26, 0xfe, 0xd2, 0x80, 0x9b, 0x53, 0xcc, 0x4a,
	0x02, 0x64, 0x01, 0x8b, 0x0f, 0xc8, 0x5b, 0x7a, 0xa0, 0x49, 0x05, 0x2d, 0x9c, 0x84, 0xc5, 0x1a,
	0xd8, 0x39, 0xe0, 0x99, 0x9d, 0xf7, 0xa1, 0xc2, 0x9c, 0x10, 0xb9, 0xde, 0x8b, 0x36, 0x94, 0x03,
	0x9f, 0xc4, 0xb9, 0x80, 0x81, 0x4f, 0xcc, 0x17, 0x70, 0x51, 0xba, 0x9e, 0x3c, 0xc2, 0xd8, 0x39,
	0xb0, 0x07, 0x2f, 0x84, 0x68, 0x0f, 0x59, 0x8b, 0x14, 0xe5, 0x6c, 0xf0, 0x1a, 0x7a, 0x7d, 0xcf,
	0x8d, 0xab, 0xe4, 0x73, 0x90, 0xf9, 0x07, 0x86, 0x72, 0x19, 0x4a, 0xa1, 0x91, 0x00, 0xed, 0x43,
	0x4b, 0x80, 0x93, 0xc2, 0x16, 0xf3, 0xeb, 0x77, 0x95, 0x58, 0xb1, 0x76, 0xa8, 0xa8, 0xe7, 0xae,
	0x79, 0x6b, 0x2e, 0x94, 0x4a, 0xe6, 0x1a, 0xcc, 0xc9, 0xad, 0xa8, 0x09, 0xb5, 0xbd, 0xf1, 0x60,
	0x80, 0x09, 0x69, 0xcf, 0xa0, 0x16, 0x34, 0xb6, 0xc6, 0xc1, 0xd0, 0x1d, 0xd8, 0x11, 0x6e, 0x1b,
	0xe6, 0x53, 0x96, 0x16, 0xa9, 0x4c, 0x4f, 0xa6, 0x40, 0x44, 0x01, 0x37, 0xfc, 0x9e, 0x91, 0x6a,
	0x3b, 0x69, 0x4a, 0x46, 0xfd, 0xd6, 0xa1, 0xa8, 0x90, 0x39, 0xe0, 0x5d, 0x0d, 0x07, 0x28, 0x03,
	0xef, 0xc4, 0x25, 0x26, 0x85, 0xe7, 0xe2, 0x39, 0x18, 0xf5, 0xdf, 0x82, 0x39, 0xb9, 0x35, 0x97,
	0x0b, 0xcc, 0x8b, 0x6c, 0xa3, 0x7b, 0x11, 0x0e, 0x76, 0x63, 0x8f, 0x13, 0xcb, 0xe0, 0xd9, 0x60,
	0xab, 0xcd, 0xd6, 0x93, 0x00, 0xbd, 0x0d, 0xe7, 0x48, 0x84, 0x83, 0xbe, 0xe4, 0xb5, 0x8a, 0x55,
	0xca, 0x3c, 0x51, 0xba, 0x9b, 0x1f, 0xa6, 0x11, 0x19, 0xbe, 0x7a, 0x7f, 0x38, 0xf4, 0x8f, 0x73,
	0x62, 0x8b, 0x72, 0x82, 0xac, 0xf9, 0x13, 0x43, 0x04, 0xde, 0xb4, 0x43, 0x49, 0x80, 0x76, 0xa1,
	0x3e, 0xb2, 0x03, 0x19, 0x5d, 0xef, 0x6b, 0xc2, 0x39, 0xba, 0xa1, 0x77, 0x9e, 0xd8, 0x01, 0x0b,
	0x24, 0xf3, 0x78, 0xce, 0x88, 0x97, 0x3a, 0x1f, 0xc3, 0x9c, 0xdc, 0x70, 0x56, 0xb2, 0x5a, 0x4b,
	0x8e, 0xdb, 0xfc, 0x79, 0x09, 0xe6, 0xe9, 0x59, 0xdb, 0x18, 0x47, 0x91, 0xef, 0x31, 0x7c, 0x67,
	0xd3, 0x0a, 0xa8, 0xe0, 0x08, 0x87, 0x82, 0x27, 0xe8, 0x5f, 0xe9, 0x91, 0x14, 0x3e, 0x89, 0xc4,
	0xe1, 0x10, 0xce, 0x0c, 0x7c, 0x12, 0x51, 0x1b, 0x39, 0xc4, 0xc7, 0x76, 0xe8, 0xf0, 0x76, 0x2e,
	0x64, 0x81, 0x57, 0xb1, 0x0e, 0x37, 0x61, 0x3e, 0x14, 0x39, 0x8c, 0x52, 0xae, 0x60, 0xc3, 0x6a,
	0xc5, 0xb5, 0x3c, 0xbf, 0x61, 0x15, 0x40, 0x98, 0xf2, 0xee, 0x08, 0x27, 0x6f, 0xcc, 0x98, 0x31,
	0x9f, 0x8d, 0x65, 0x88, 0xc8, 0x43, 0x1c, 0xcb, 0x48, 0x0d, 0xda, 0xba, 0x62, 0xd0, 0x5e, 0x87,
	0x39, 0x3f, 0xc0, 0x61, 0xc6, 0x7c, 0x6f, 0x8a, 0x3a, 0x36, 0xb4, 0x03, 0x75, 0x5e, 0xf4, 0x43,
	0x66, 0xc0, 0x37, 0xac, 0xa4, 0x6c, 0x6e, 0xc3, 0xc5, 0x3d, 0x1c, 0xa5, 0xe8, 0xda, 0x64, 0x06,
	0x22, 0xe5, 0x87, 0x77, 0x15, 0x5b, 0xfe, 0x52, 0x26, 0x8c, 0x9a, 0x22, 0x57, 0x98, 0xf3, 0x97,
	0x60, 0x49, 0x3b, 0x11, 0x09, 0xcc, 0x3f, 0x33, 0xd8, 0xc3, 0x80, 0x6c, 0x1b, 0x3b, 0xc1, 0x1f,
	0x25, 0xdb, 0x32, 0x26, 0x83, 0x34, 0x99, 0x01, 0xaa, 0x72, 0xd2, 0xa0, 0xbc, 0xa4, 0x43, 0xb9,
	0x3e, 0x29, 0x34, 0xe7, 0x29, 0x95, 0xd9, 0xe7, 0x3e, 0xed, 0xc9, 0xa5, 0x92, 0x40, 0xc2, 0x48,
	0x79, 0x0a, 0x8c, 0xe4, 0xe8, 0xb6, 0xdb, 0xd0, 0xd9, 0x3d, 0x3c, 0x1c, 0xba, 0x1e, 0xd6, 0x21,
	0x3d, 0xc3, 0xa8, 0xd4, 0xc6, 0xc8, 0xed, 0x4d, 0x02, 0xf3, 0x0a, 0x7b, 0x52, 0xf2, 0xd8, 0x26,
	0x11, 0x76, 0x34, 0xd3, 0x99, 0x7f, 0xc7, 0xdf, 0xa5, 0xe5, 0x75, 0xe0, 0xae, 0x97, 0xa2, 0x1b,
	0xd6, 0xff, 0xd6, 0x41, 0x59, 0xdb, 0x84, 0x76, 0x36, 0xad, 0x10, 0xcd, 0x41, 0x9d, 0x15, 0xbb,
	0xc3, 0x61, 0x7b, 0x06, 0x2d, 0x40, 0x93, 0x95, 0xf8, 0x8b, 0xf8, 0xb6, 0x81, 0xda, 0x30, 0xc7,
	0x2a, 0xb6, 0x5c, 0xc2, 0x6a, 0x4a, 0x6b, 0xdf, 0x11, 0x2f, 0x4c, 0x95, 0xdc, 0x44, 0x74, 0x0e,
	0x5a, 0x0f, 0x4f, 0xe4, 0xa1, 0x33, 0x68, 0x1e, 0x40, 0x54, 0xd1, 0xb9, 0x0d, 0x84, 0x60, 0x5e,
	0x94, 0xd3, 0xc9, 0x7e, 0x24, 0xbc, 0xad, 0xcc, 0xd9, 0x8d, 0x60, 0x9e, 0xfe, 0x6e, 0xfa, 0xc3,
	0x21, 0x1e, 0xd0, 0x79, 0xb9, 0xce, 0xa2, 0x75, 0xac, 0x53, 0xdb, 0xa0, 0xfa, 0x8c, 0x16, 0xb7,
	0xb1, 0xdf, 0x2e, 0xc5, 0x85, 0xad, 0xde, 0x17, 0xed, 0x32, 0xdd, 0x07, 0x2d, 0xd0, 0xeb, 0x43,
	0xbb, 0x12, 0x97, 0xf6, 0xed, 0xe1, 0x8b, 0xf6, 0x2c, 0x5a, 0x84, 0x36, 0x1b, 0x65, 0x8f, 0xf0,
	0x96, 0x4b, 0xa2, 0xd0, 0x1d, 0x44, 0xed, 0xea, 0xda, 0x00, 0xe6, 0xb9, 0xc9, 0x91, 0xbc, 0x94,
	0x6b, 0x42, 0x6d, 0xc7, 0xf7, 0xf0, 0xde, 0xf8, 0xa0, 0x3d, 0x83, 0x6a, 0x50, 0xde, 0xc1, 0xc7,
	0x6d, 0x83, 0x2e, 0x21, 0x79, 0x9d, 0xd2, 0x2e, 0xd1, 0xfa, 0x4f, 0xfd, 0xa8, 0x5d, 0xa6, 0xf5,
	0x7b, 0x78, 0xe0, 0x7b, 0x0e, 0xed, 0x56, 0x41, 0x55, 0x28, 0x3d, 0x0b, 0xda, 0xb3, 0x14, 0x34,
	0xb3, 0x5c, 0xdc, 0xe8, 0xb4, 0x5d, 0x5d, 0xfb, 0x10, 0x5a, 0x4a, 0x08, 0x96, 0xc2, 0xe8, 0x7a,
	0x4e, 0xe8, 0xbb, 0x0e, 0x87, 0xe1, 0xee, 0xee, 0xb5, 0x0d, 0x74, 0x0e, 0xe6, 0xba, 0xc3, 0x61,
	0x3f, 0xee, 0xd5, 0xfe, 0x6f, 0x63, 0xed, 0x3e, 0xc0, 0xd3, 0xf4, 0x29, 0x1a, 0x1d, 0x36, 0x1c,
	0xd2, 0xbf, 0xed, 0x19, 0x04, 0x50, 0xdd, 0xc1, 0xc7, 0x07, 0x2e, 0x25, 0x10, 0x40, 0x75, 0x9b,
	0xbd, 0x18, 0x6b, 0x97, 0xd6, 0xde, 0x10, 0xce, 0x14, 0x5e, 0x41, 0x9b, 0x1e, 0xe1, 0x91, 0xcd,
	0x88, 0x51, 0x87, 0xca, 0x13, 0xfa, 0xcf, 0x58, 0x0b, 0xc5, 0xed, 0x45, 0x7a, 0x73, 0xc3, 0xb0,
	0xbf, 0x00, 0xcd, 0xdd, 0xc7, 0x5b, 0xfd, 0xcf, 0x1e, 0x5a, 0x7b, 0xbd, 0xdd, 0x9d, 0xf6, 0x0c,
	0x7a, 0x03, 0xae, 0xee, 0x3c, 0xfc, 0x3c, 0xae, 0xe8, 0xef, 0xef, 0x3e, 0xed, 0x3f, 0xdd, 0xdd,
	0xeb, 0xed, 0xd3, 0xc2, 0xce, 0xae, 0xf5, 0xa4, 0xfb, 0xb8, 0x6d, 0xa0, 0x1b, 0x70, 0x2d, 0xb7,
	0xd3, 0x66, 0x77, 0xbf, 0xfb, 0x78, 0x77, 0xbb, 0x5d, 0x5a, 0x73, 0x60, 0x4e, 0x76, 0x9f, 0x52,
	0xae, 0xfa, 0x1c, 0xbb, 0x7b, 0xd1, 0x16, 0x3e, 0xb4, 0xc7, 0xc3, 0x88, 0x33, 0x0b, 0xab, 0x61,
	0x89, 0x79, 0x9c, 0xef, 0x58, 0xf9, 0xe1, 0x49, 0xe0, 0x86, 0x98, 0xe2, 0x7d, 0x01, 0x9a, 0xac,
	0xe6, 0xd1, 0x38, 0x1a, 0x87, 0xb8, 0x5d, 0x16, 0x15, 0x3b, 0x3e, 0x3f, 0x78, 0xed, 0xca, 0x5a,
	0x0f, 0xe6, 0xb6, 0x7a, 0x5f, 0xec, 0xf9, 0x61, 0xb4, 0xc3, 0x32, 0xff, 0x16, 0xa0, 0x29, 0x00,
	0xd0, 0x3a, 0x8e, 0xb7, 0x5d, 0x0f, 0x6f, 0xd9, 0xa7, 0x9c, 0xaa, 0xfb, 0xcf, 0x43, 0x4c, 0x4b,
	0x24, 0x26, 0xe6, 0x4b, 0xcc, 0x52, 0x06, 0xdb, 0xb5, 0xb5, 0x3e, 0x9c, 0xd7, 0xc4, 0xde, 0x29,
	0x97, 0xb3, 0x3f, 0xfd, 0x74, 0xe1, 0x94, 0xdc, 0xac, 0x6a, 0x7f, 0x9f, 0xd1, 0x51, 0x74, 0xf8,
	0x74, 0x6c, 0x7b, 0xa7, 0xfe, 0xb8, 0x5d, 0xa2, 0xcc, 0x17, 0x77, 0x48, 0x6a, 0xcb, 0x6b, 0x01,
	0x5c, 0xd4, 0xc7, 0xe8, 0x69, 0xff, 0xf8, 0xbf, 0x04, 0x46, 0xae, 0x8d, 0xf9, 0x87, 0x61, 0x29,
	0xa9, 0xed, 0xed, 0xee, 0xb5, 0x4b, 0x68, 0x19, 0x16, 0xb3, 0xfd, 0x58, 0x4b, 0x79, 0xed, 0x6d,
	0xa8, 0x89, 0xbc, 0x08, 0x8a, 0x18, 0xfa, 0xb7, 0x9f, 0x70, 0x47, 0x0b, 0x1a, 0xac, 0x42, 0xb0,
	0x88, 0x9d, 0x5d, 0x5c, 0x1c, 0xcb, 0xa7, 0x64, 0xda, 0xf7, 0xd5, 0xdd, 0xef, 0xfb, 0xfd, 0x7d,
	0x2a, 0x84, 0xc5, 0xe9, 0xf4, 0xfb, 0x4f, 0xed, 0x30, 0x6a, 0x97, 0x18, 0x82, 0xfd, 0xbe, 0xe0,
	0xd3, 0xb2, 0x28, 0xf2, 0x88, 0x7e, 0xbb, 0xb2, 0xf6, 0x08, 0x96, 0x72, 0x42, 0xe9, 0x14, 0x46,
	0x8f, 0x48, 0x30, 0x00, 0xaa, 0x3d, 0xd2, 0xe7, 0x67, 0x71, 0x01, 0x9a, 0x3d, 0xd2, 0xdf, 0xf4,
	0xbd, 0xc8, 0xf5, 0xc6, 0x54, 0x80, 0xdc, 0x81, 0x6a, 0x97, 0xf3, 0x2f, 0x03, 0x1e, 0xd9, 0xc3,
	0x2e, 0x3d, 0x58, 0xac, 0xc0, 0xbc, 0x30, 0xfc, 0x88, 0x50, 0xb1, 0xd0, 0x75, 0xda, 0xa5, 0xb5,
	0x27, 0xb0, 0x94, 0xbc, 0xe6, 0x72, 0x8f, 0x36, 0xfd, 0xd1, 0xc8, 0xf7, 0x04, 0x53, 0x36, 0x60,
	0x96, 0x6f, 0x84, 0x6d, 0x6b, 0xc7, 0x8f, 0x36, 0xe8, 0x9d, 0xba, 0x6d, 0xd0, 0x52, 0x4f, 0x88,
	0x77, 0x2e, 0x75, 0x62, 0xa6, 0x2c, 0xaf, 0xfd, 0xb5, 0x01, 0x9d, 0xfc, 0x2b, 0x1f, 0xba, 0x0e,
	0xab, 0xf9, 0xad, 0x5c, 0xe2, 0xbe, 0x09, 0x66, 0x7e, 0x97, 0x9e, 0x67, 0xb3, 0x4b, 0x10, 0x3f,
	0x68, 0x05, 0x53, 0xf1, 0x5e, 0x25, 0x74, 0x13, 0xae, 0xe7, 0xf7, 0x4a, 0x97, 0xfd, 0xbb, 0x06,
	0x5c, 0xd4, 0x1b, 0x02, 0x68, 0x05, 0x96, 0x68, 0x4b, 0x9f, 0x37, 0xf5, 0x79, 0x5b, 0x9f, 0x8a,
	0xc5, 0xf6, 0x0c, 0xba, 0x0c, 0xcb, 0x9a, 0xc6, 0xf8, 0xcc, 0x5e, 0x81, 0x8e, 0xa6, 0x35, 0x3d,
	0xc1, 0xab, 0x70, 0x49, 0x3b, 0x75, 0xf4, 0x05, 0x8e, 0xda, 0xe5, 0xf5, 0x1f, 0xdf, 0xa6, 0x04,
	0xa0, 0xd6, 0xc5, 0x23, 0x68, 0x4a, 0x6e, 0x21, 0x74, 0x49, 0x1f, 0xdb, 0xb5, 0xf0, 0x57, 0x9d,
	0x4e, 0x5e, 0x13, 0x09, 0xcc, 0x19, 0x3a, 0x8f, 0xf4, 0xa8, 0x41, 0x99, 0x47, 0x7d, 0x25, 0xa1,
	0xcc, 0x93, 0x7d, 0x56, 0x31, 0x83, 0x76, 0x61, 0x5e, 0x7d, 0x1f, 0x81, 0x2e, 0xab, 0x96, 0x78,
	0x66, 0xb6, 0xd5, 0x82, 0xd6, 0x78, 0x61, 0xd2, 0x37, 0x67, 0x94, 0x85, 0xa9, 0x1f, 0xb4, 0x51,
	0x16, 0x96, 0xfd, 0x4c, 0xcd, 0x0c, 0xb2, 0x60, 0x21, 0xf3, 0xc5, 0x14, 0x24, 0xc3, 0x9e, 0xfc,
	0x86, 0x4d, 0xe7, 0x4a, 0x51, 0x33, 0x9b, 0xf3, 0x97, 0xe0, 0xdc, 0xc4, 0xf7, 0x43, 0x90, 0xec,
	0x7d, 0xd0, 0x7d, 0x8b, 0xa5, 0x73, 0xad, 0xb8, 0x43, 0xbc, 0xda, 0xcc, 0x97, 0x40, 0x50, 0x06,
	0x53, 0x99, 0x8f, 0x93, 0x28, 0xab, 0xd5, 0x7c, 0x44, 0xc4, 0x9c, 0x41, 0x36, 0x2c, 0xea, 0x3e,
	0xb3, 0x81, 0x4c, 0x75, 0xa4, 0xee, 0x43, 0x22, 0x9d, 0x37, 0xce, 0xec, 0xc3, 0x40, 0xfc, 0x2a,
	0xac, 0x16, 0x7e, 0x51, 0x01, 0xbd, 0x33, 0x81, 0xd3, 0xfc, 0x6f, 0x16, 0x74, 0x6e, 0x4f, 0xdf,
	0x99, 0x41, 0x27, 0xcc, 0x73, 0x97, 0x07, 0xfa, 0x96, 0xca, 0xb7, 0x05, 0x70, 0xdf, 0x9e, 0xb2,
	0x67, 0x0c, 0x34, 0xff, 0x13, 0x06, 0x0a, 0xd0, 0xc2, 0x0f, 0x34, 0x28, 0x40, 0x8b, 0xbf, 0x89,
	0x60, 0xce, 0xa0, 0x53, 0xb8, 0x5c, 0xf4, 0xdc, 0x1c, 0xad, 0x4d, 0x60, 0x2e, 0xf7, 0xd9, 0x77,
	0xe7, 0x9d, 0xa9, 0xfb, 0x32, 0xd0, 0x01, 0x5c, 0xca, 0x7d, 0x7f, 0x8e, 0xde, 0x52, 0x31, 0x97,
	0x0f, 0xf4, 0xd6, 0x74, 0x1d, 0x63, 0x88, 0xb9, 0x6f, 0xca, 0x15, 0x88, 0x45, 0xaf, 0xdb, 0x3b,
	0xb7, 0xa6, 0xeb, 0xc8, 0x20, 0xf6, 0xd2, 0x97, 0x49, 0x96, 0xed, 0xbd, 0x40, 0x1d, 0x8d, 0xef,
	0x45, 0xbc, 0x63, 0xee, 0xac, 0xe4, 0xb6, 0xc5, 0x87, 0x4e, 0xf7, 0x74, 0x5b, 0x39, 0x74, 0x39,
	0x8f, 0xd6, 0x95, 0x43, 0x97, 0xf7, 0xfe, 0x5b, 0x5d, 0x2d, 0x13, 0x6b, 0xba, 0xd5, 0xc6, 0x32,
	0x6d, 0x25, 0xb7, 0x4d, 0x62, 0xe6, 0x9c, 0x17, 0xd0, 0x59, 0x66, 0xce, 0x7f, 0x5b, 0x9d, 0x65,
	0xe6, 0x82, 0x27, 0xd5, 0xe6, 0x0c, 0x7a, 0x06, 0xed, 0xec, 0x7b, 0x66, 0x24, 0x4b, 0x33, 0xcd,
	0x6b, 0xea, 0xce, 0xd5, 0xc2, 0xf6, 0x78, 0xda, 0xec, 0x53, 0x66, 0x65, 0x5a, 0xcd, 0x5b, 0x69,
	0x65, 0x5a, 0xdd, 0x3b, 0x68, 0x15, 0xdb, 0xfc, 0xab, 0x19, 0x9a, 0xad, 0x0a, 0xff, 0xb0, 0x16,
	0xdb, 0xb1, 0x83, 0xd6, 0x9c, 0x41, 0xdf, 0x86, 0x46, 0x42, 0x56, 0xb4, 0xa4, 0x23, 0x36, 0x9d,
	0x64, 0x59, 0xdf, 0x10, 0x6b, 0x5b, 0x35, 0x56, 0xa3, 0x68, 0xdb, 0x89, 0xc0, 0x8f, 0xa2, 0x6d,
	0x27, 0x83, 0x3c, 0x7c, 0x49, 0xc9, 0x43, 0x4b, 0x65, 0x49, 0xf2, 0xbb, 0x4e, 0x65, 0x49, 0xea,
	0xbb, 0x4c, 0xc6, 0xf0, 0xba, 0x54, 0x76, 0x85, 0xe1, 0x73, 0xf2, 0xe7, 0x15, 0x86, 0xcf, 0xcd,
	0x87, 0x67, 0x6a, 0x77, 0x22, 0x5f, 0x1c, 0xa9, 0x1c, 0x31, 0x99, 0xc5, 0xae, 0xa8, 0x5d, 0x7d,
	0xba, 0x39, 0xc3, 0xa7, 0xfa, 0x5e, 0x52, 0xc1, 0xe7, 0xc4, 0x0b, 0x4e, 0x05, 0x9f, 0x9a, 0x87,
	0x96, 0x8c, 0x09, 0xb3, 0xcf, 0x24, 0x15, 0x26, 0xd4, 0xbc, 0xc3, 0x54, 0x98, 0x50, 0xfb, 0xc6,
	0x92, 0x21, 0x59, 0x97, 0x37, 0xab, 0x20, 0x39, 0x27, 0xfb, 0x57, 0x41, 0x72, 0x6e, 0xf2, 0xed,
	0x0c, 0x72, 0x98, 0xc7, 0x57, 0x93, 0x9f, 0xfd, 0x46, 0x56, 0x51, 0x69, 0x12, 0x74, 0x3b, 0x37,
	0xce, 0xee, 0x94, 0x0b, 0xe5, 0x99, 0xeb, 0x90, 0x62, 0x28, 0x22, 0xff, 0xb6, 0x18, 0x4a, 0x9c,
	0x3f, 0xcb, 0xd1, 0xa5, 0x4b, 0x62, 0x55, 0xd0, 0x95, 0x93, 0x1b, 0xab, 0xa0, 0x2b, 0x37, 0x13,
	0x76, 0x06, 0x1d, 0xb1, 0x10, 0xb1, 0x26, 0xb9, 0x15, 0x15, 0x2e, 0x32, 0x4e, 0x97, 0xed, 0xdc,
	0x9c, 0xa2, 0x57, 0x2e, 0xc6, 0x98, 0x20, 0x2a, 0xc4, 0x58, 0x2c, 0x91, 0x6e, 0x9c, 0xdd, 0x29,
	0x3d, 0x62, 0x99, 0x2c, 0xd3, 0xcc, 0x11, 0x9b, 0x4c, 0x8d, 0xcd, 0x1c, 0x31, 0x5d, 0x92, 0x2a,
	0xb3, 0xe7, 0xa5, 0xb4, 0x45, 0xc5, 0x9e, 0x57, 0xb3, 0x2b, 0x15, 0x7b, 0x3e, 0x93, 0xe9, 0xc8,
	0xad, 0x82, 0xdc, 0x77, 0x33, 0x8a, 0x55, 0x50, 0xf4, 0x62, 0x47, 0xb1, 0x0a, 0x8a, 0x9f, 0xe1,
	0xcc, 0xa0, 0xc7, 0xd0, 0x52, 0xb2, 0x01, 0xd1, 0xca, 0xc4, 0x8d, 0x2a, 0x4d, 0x35, 0xec, 0x5c,
	0xce, 0x6f, 0x64, 0xb3, 0x7d, 0xca, 0xf2, 0x25, 0x70, 0x3a, 0xdb, 0x6b, 0x5f, 0xb9, 0x7e, 0x00,
	0x8b, 0xba, 0x34, 0x42, 0x85, 0xbb, 0x73, 0x32, 0x16, 0x3b, 0x67, 0xf6, 0x89, 0x79, 0x4e, 0x9b,
	0x3c, 0x87, 0x72, 0x04, 0xb6, 0x92, 0x90, 0xa6, 0xf0, 0x5c, 0x6e, 0x0e, 0x9e, 0x39, 0x83, 0xbe,
	0x4c, 0x13, 0x0f, 0x24, 0x10, 0xd7, 0x72, 0xec, 0xab, 0x74, 0xfe, 0xeb, 0x67, 0xf4, 0x88, 0xb7,
	0xa0, 0xcd, 0xdc, 0x42, 0x93, 0x37, 0x9b, 0x33, 0xb6, 0x90, 0x9b, 0x00, 0x66, 0xce, 0xa0, 0x4d,
	0x80, 0x34, 0xa7, 0x08, 0x29, 0x6a, 0x52, 0xce, 0x2b, 0xea, 0x5c, 0xca, 0x69, 0x89, 0x27, 0xd9,
	0xd6, 0x4f, 0xb2, 0x9d, 0x3b, 0xc9, 0xb6, 0x66, 0x92, 0x34, 0xbd, 0x46, 0x99, 0x44, 0xc9, 0xd2,
	0xe9, 0x5c, 0xca, 0x69, 0xc9, 0x52, 0x24, 0xcd, 0x7b, 0xd1, 0x52, 0x44, 0x49, 0xd8, 0xd1, 0x52,
	0x44, 0x4d, 0x9c, 0xe1, 0x42, 0x59, 0x97, 0xd0, 0xa2, 0xb0, 0x6d, 0x4e, 0x96, 0x8c, 0x22, 0x94,
	0xf3, 0xb2, 0x62, 0xcc, 0x19, 0xf4, 0x43, 0xf6, 0xe4, 0x41, 0x9b, 0x80, 0x72, 0x53, 0xb3, 0xef,
	0xc9, 0xc4, 0x8c, 0xce, 0x9b, 0xd3, 0x74, 0x8b, 0x61, 0xe5, 0x64, 0x63, 0x28, 0xb0, 0xf2, 0x93,
	0x40, 0x14, 0x58, 0x45, 0x89, 0x1d, 0x33, 0xe8, 0x25, 0xac, 0x14, 0xa4, 0x45, 0xa0, 0xb7, 0x8b,
	0x93, 0x05, 0xa4, 0x0c, 0x90, 0xce, 0xda, 0xb4, 0x5d, 0x19, 0xdc, 0xdf, 0x32, 0xe0, 0xfa, 0x99,
	0xd9, 0x07, 0xe8, 0xee, 0xab, 0xe5, 0x2a, 0x7c, 0xd5, 0xb9, 0xf7, 0xaa, 0xc9, 0x0d, 0xe6, 0x0c,
	0xfa, 0x3e, 0x9c, 0xd7, 0xc4, 0xfc, 0xd1, 0xf5, 0xb3, 0x72, 0x02, 0x72, 0x45, 0x9e, 0x9c, 0x36,
	0xc0, 0x15, 0xe0, 0x44, 0x94, 0x1d, 0x5d, 0x2d, 0x8e, 0xc1, 0xab, 0x0a, 0x50, 0x1b, 0xa4, 0x4f,
	0x66, 0x56, 0x43, 0xe9, 0xd9, 0x99, 0x27, 0x02, 0xf0, 0xd9, 0x99, 0x27, 0x23, 0xf1, 0xe6, 0x0c,
	0x1a, 0xb1, 0xac, 0x60, 0x6d, 0xa8, 0x1b, 0xbd, 0x39, 0x55, 0x3c, 0xfc, 0xab, 0xce, 0x5b, 0x53,
	0xc6, 0xcd, 0x39, 0x09, 0x34, 0x11, 0x57, 0x85, 0x04, 0xfa, 0xd0, 0xae, 0x42, 0x82, 0xbc, 0xa0,
	0x6d, 0x7c, 0x75, 0x9e, 0x08, 0x85, 0x66, 0xaf, 0xce, 0xba, 0xb0, 0x6e, 0xf6, 0xea, 0xac, 0x8d,
	0xa7, 0xf2, 0x43, 0x9b, 0x13, 0xde, 0x54, 0x0e, 0x6d, 0x7e, 0xc0, 0x54, 0x39, 0xb4, 0x45, 0x91,
	0xd2, 0xd8, 0x8d, 0xa1, 0x0f, 0x85, 0x66, 0xdd, 0x18, 0xb9, 0x11, 0xd5, 0xac, 0x1b, 0x23, 0x3f,
	0xb2, 0x6a, 0xce, 0x6c, 0xbc, 0xfb, 0xcb, 0xef, 0x1c, 0xf9, 0x43, 0xdb, 0x3b, 0xba, 0xf3, 0x60,
	0x3d, 0x8a, 0xee, 0x0c, 0xfc, 0xd1, 0x5d, 0xf6, 0xd1, 0xf9, 0x81, 0x3f, 0xbc, 0x4b, 0x70, 0xf8,
	0xd2, 0x1d, 0x60, 0x72, 0x37, 0xf9, 0x20, 0xfd, 0x41, 0x95, 0x35, 0xbe, 0xf7, 0x3f, 0x01, 0x00,
	0x00, 0xff, 0xff, 0xfd, 0x75, 0xc6, 0xb2, 0xa4, 0x5e, 0x00, 0x00,
}
