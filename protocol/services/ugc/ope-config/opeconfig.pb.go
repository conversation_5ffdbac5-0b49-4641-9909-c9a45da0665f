// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ugc/opeconfig.proto

package ope_config // import "golang.52tt.com/protocol/services/ugc/ope-config"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type FeedType int32

const (
	FeedType_RecommnendType FeedType = 0
	FeedType_TopicType      FeedType = 1
	FeedType_NormalType     FeedType = 2
	FeedType_GameDistrict   FeedType = 3
)

var FeedType_name = map[int32]string{
	0: "RecommnendType",
	1: "TopicType",
	2: "NormalType",
	3: "GameDistrict",
}
var FeedType_value = map[string]int32{
	"RecommnendType": 0,
	"TopicType":      1,
	"NormalType":     2,
	"GameDistrict":   3,
}

func (x FeedType) String() string {
	return proto.EnumName(FeedType_name, int32(x))
}
func (FeedType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{0}
}

type GetGameEntranceResp_PlayStatus int32

const (
	GetGameEntranceResp_UNKNOWN    GetGameEntranceResp_PlayStatus = 0
	GetGameEntranceResp_PLAYED     GetGameEntranceResp_PlayStatus = 1
	GetGameEntranceResp_NOT_PLAYED GetGameEntranceResp_PlayStatus = 2
)

var GetGameEntranceResp_PlayStatus_name = map[int32]string{
	0: "UNKNOWN",
	1: "PLAYED",
	2: "NOT_PLAYED",
}
var GetGameEntranceResp_PlayStatus_value = map[string]int32{
	"UNKNOWN":    0,
	"PLAYED":     1,
	"NOT_PLAYED": 2,
}

func (x GetGameEntranceResp_PlayStatus) String() string {
	return proto.EnumName(GetGameEntranceResp_PlayStatus_name, int32(x))
}
func (GetGameEntranceResp_PlayStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{29, 0}
}

type GetGameEntranceResp_OnlineStatus int32

const (
	GetGameEntranceResp_UNDEFINE GetGameEntranceResp_OnlineStatus = 0
	GetGameEntranceResp_ONLINE   GetGameEntranceResp_OnlineStatus = 1
	GetGameEntranceResp_OFFLINE  GetGameEntranceResp_OnlineStatus = 2
)

var GetGameEntranceResp_OnlineStatus_name = map[int32]string{
	0: "UNDEFINE",
	1: "ONLINE",
	2: "OFFLINE",
}
var GetGameEntranceResp_OnlineStatus_value = map[string]int32{
	"UNDEFINE": 0,
	"ONLINE":   1,
	"OFFLINE":  2,
}

func (x GetGameEntranceResp_OnlineStatus) String() string {
	return proto.EnumName(GetGameEntranceResp_OnlineStatus_name, int32(x))
}
func (GetGameEntranceResp_OnlineStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{29, 1}
}

type PartyGoInfo struct {
	TopicIds             []string   `protobuf:"bytes,1,rep,name=topic_ids,json=topicIds,proto3" json:"topic_ids,omitempty"`
	FeedTypes            []FeedType `protobuf:"varint,2,rep,packed,name=feed_types,json=feedTypes,proto3,enum=ugc.opeconfig.FeedType" json:"feed_types,omitempty"`
	Text                 string     `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	Url                  string     `protobuf:"bytes,4,opt,name=url,proto3" json:"url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *PartyGoInfo) Reset()         { *m = PartyGoInfo{} }
func (m *PartyGoInfo) String() string { return proto.CompactTextString(m) }
func (*PartyGoInfo) ProtoMessage()    {}
func (*PartyGoInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{0}
}
func (m *PartyGoInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PartyGoInfo.Unmarshal(m, b)
}
func (m *PartyGoInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PartyGoInfo.Marshal(b, m, deterministic)
}
func (dst *PartyGoInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PartyGoInfo.Merge(dst, src)
}
func (m *PartyGoInfo) XXX_Size() int {
	return xxx_messageInfo_PartyGoInfo.Size(m)
}
func (m *PartyGoInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PartyGoInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PartyGoInfo proto.InternalMessageInfo

func (m *PartyGoInfo) GetTopicIds() []string {
	if m != nil {
		return m.TopicIds
	}
	return nil
}

func (m *PartyGoInfo) GetFeedTypes() []FeedType {
	if m != nil {
		return m.FeedTypes
	}
	return nil
}

func (m *PartyGoInfo) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *PartyGoInfo) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

type AddPartyGoReq struct {
	PostIds              []string     `protobuf:"bytes,1,rep,name=post_ids,json=postIds,proto3" json:"post_ids,omitempty"`
	Info                 *PartyGoInfo `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	IsPartygoUser        bool         `protobuf:"varint,3,opt,name=is_partygo_user,json=isPartygoUser,proto3" json:"is_partygo_user,omitempty"`
	Gameid               uint32       `protobuf:"varint,4,opt,name=gameid,proto3" json:"gameid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AddPartyGoReq) Reset()         { *m = AddPartyGoReq{} }
func (m *AddPartyGoReq) String() string { return proto.CompactTextString(m) }
func (*AddPartyGoReq) ProtoMessage()    {}
func (*AddPartyGoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{1}
}
func (m *AddPartyGoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPartyGoReq.Unmarshal(m, b)
}
func (m *AddPartyGoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPartyGoReq.Marshal(b, m, deterministic)
}
func (dst *AddPartyGoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPartyGoReq.Merge(dst, src)
}
func (m *AddPartyGoReq) XXX_Size() int {
	return xxx_messageInfo_AddPartyGoReq.Size(m)
}
func (m *AddPartyGoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPartyGoReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddPartyGoReq proto.InternalMessageInfo

func (m *AddPartyGoReq) GetPostIds() []string {
	if m != nil {
		return m.PostIds
	}
	return nil
}

func (m *AddPartyGoReq) GetInfo() *PartyGoInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *AddPartyGoReq) GetIsPartygoUser() bool {
	if m != nil {
		return m.IsPartygoUser
	}
	return false
}

func (m *AddPartyGoReq) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

type AddPartyGoRsp struct {
	NoPermission         bool     `protobuf:"varint,1,opt,name=no_permission,json=noPermission,proto3" json:"no_permission,omitempty"`
	ErrPostId            []string `protobuf:"bytes,2,rep,name=err_post_id,json=errPostId,proto3" json:"err_post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddPartyGoRsp) Reset()         { *m = AddPartyGoRsp{} }
func (m *AddPartyGoRsp) String() string { return proto.CompactTextString(m) }
func (*AddPartyGoRsp) ProtoMessage()    {}
func (*AddPartyGoRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{2}
}
func (m *AddPartyGoRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPartyGoRsp.Unmarshal(m, b)
}
func (m *AddPartyGoRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPartyGoRsp.Marshal(b, m, deterministic)
}
func (dst *AddPartyGoRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPartyGoRsp.Merge(dst, src)
}
func (m *AddPartyGoRsp) XXX_Size() int {
	return xxx_messageInfo_AddPartyGoRsp.Size(m)
}
func (m *AddPartyGoRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPartyGoRsp.DiscardUnknown(m)
}

var xxx_messageInfo_AddPartyGoRsp proto.InternalMessageInfo

func (m *AddPartyGoRsp) GetNoPermission() bool {
	if m != nil {
		return m.NoPermission
	}
	return false
}

func (m *AddPartyGoRsp) GetErrPostId() []string {
	if m != nil {
		return m.ErrPostId
	}
	return nil
}

type GetPartyGosReq struct {
	PostIds              []string `protobuf:"bytes,1,rep,name=post_ids,json=postIds,proto3" json:"post_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPartyGosReq) Reset()         { *m = GetPartyGosReq{} }
func (m *GetPartyGosReq) String() string { return proto.CompactTextString(m) }
func (*GetPartyGosReq) ProtoMessage()    {}
func (*GetPartyGosReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{3}
}
func (m *GetPartyGosReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPartyGosReq.Unmarshal(m, b)
}
func (m *GetPartyGosReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPartyGosReq.Marshal(b, m, deterministic)
}
func (dst *GetPartyGosReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPartyGosReq.Merge(dst, src)
}
func (m *GetPartyGosReq) XXX_Size() int {
	return xxx_messageInfo_GetPartyGosReq.Size(m)
}
func (m *GetPartyGosReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPartyGosReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPartyGosReq proto.InternalMessageInfo

func (m *GetPartyGosReq) GetPostIds() []string {
	if m != nil {
		return m.PostIds
	}
	return nil
}

type GetPartyGosRsp struct {
	Infos                []*AllPartyGoInfo `protobuf:"bytes,1,rep,name=infos,proto3" json:"infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetPartyGosRsp) Reset()         { *m = GetPartyGosRsp{} }
func (m *GetPartyGosRsp) String() string { return proto.CompactTextString(m) }
func (*GetPartyGosRsp) ProtoMessage()    {}
func (*GetPartyGosRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{4}
}
func (m *GetPartyGosRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPartyGosRsp.Unmarshal(m, b)
}
func (m *GetPartyGosRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPartyGosRsp.Marshal(b, m, deterministic)
}
func (dst *GetPartyGosRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPartyGosRsp.Merge(dst, src)
}
func (m *GetPartyGosRsp) XXX_Size() int {
	return xxx_messageInfo_GetPartyGosRsp.Size(m)
}
func (m *GetPartyGosRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPartyGosRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPartyGosRsp proto.InternalMessageInfo

func (m *GetPartyGosRsp) GetInfos() []*AllPartyGoInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

type GetPartyGosOffsetReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Postid               string   `protobuf:"bytes,3,opt,name=postid,proto3" json:"postid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPartyGosOffsetReq) Reset()         { *m = GetPartyGosOffsetReq{} }
func (m *GetPartyGosOffsetReq) String() string { return proto.CompactTextString(m) }
func (*GetPartyGosOffsetReq) ProtoMessage()    {}
func (*GetPartyGosOffsetReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{5}
}
func (m *GetPartyGosOffsetReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPartyGosOffsetReq.Unmarshal(m, b)
}
func (m *GetPartyGosOffsetReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPartyGosOffsetReq.Marshal(b, m, deterministic)
}
func (dst *GetPartyGosOffsetReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPartyGosOffsetReq.Merge(dst, src)
}
func (m *GetPartyGosOffsetReq) XXX_Size() int {
	return xxx_messageInfo_GetPartyGosOffsetReq.Size(m)
}
func (m *GetPartyGosOffsetReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPartyGosOffsetReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPartyGosOffsetReq proto.InternalMessageInfo

func (m *GetPartyGosOffsetReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetPartyGosOffsetReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetPartyGosOffsetReq) GetPostid() string {
	if m != nil {
		return m.Postid
	}
	return ""
}

type AllPartyGoInfo struct {
	PostId               string       `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Info                 *PartyGoInfo `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	CreateTime           int64        `protobuf:"varint,3,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Gameid               uint32       `protobuf:"varint,4,opt,name=gameid,proto3" json:"gameid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AllPartyGoInfo) Reset()         { *m = AllPartyGoInfo{} }
func (m *AllPartyGoInfo) String() string { return proto.CompactTextString(m) }
func (*AllPartyGoInfo) ProtoMessage()    {}
func (*AllPartyGoInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{6}
}
func (m *AllPartyGoInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AllPartyGoInfo.Unmarshal(m, b)
}
func (m *AllPartyGoInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AllPartyGoInfo.Marshal(b, m, deterministic)
}
func (dst *AllPartyGoInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AllPartyGoInfo.Merge(dst, src)
}
func (m *AllPartyGoInfo) XXX_Size() int {
	return xxx_messageInfo_AllPartyGoInfo.Size(m)
}
func (m *AllPartyGoInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AllPartyGoInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AllPartyGoInfo proto.InternalMessageInfo

func (m *AllPartyGoInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *AllPartyGoInfo) GetInfo() *PartyGoInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *AllPartyGoInfo) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *AllPartyGoInfo) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

type GetPartyGosOffsetRsp struct {
	Infos                []*AllPartyGoInfo `protobuf:"bytes,1,rep,name=infos,proto3" json:"infos,omitempty"`
	TotalCnt             uint32            `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetPartyGosOffsetRsp) Reset()         { *m = GetPartyGosOffsetRsp{} }
func (m *GetPartyGosOffsetRsp) String() string { return proto.CompactTextString(m) }
func (*GetPartyGosOffsetRsp) ProtoMessage()    {}
func (*GetPartyGosOffsetRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{7}
}
func (m *GetPartyGosOffsetRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPartyGosOffsetRsp.Unmarshal(m, b)
}
func (m *GetPartyGosOffsetRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPartyGosOffsetRsp.Marshal(b, m, deterministic)
}
func (dst *GetPartyGosOffsetRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPartyGosOffsetRsp.Merge(dst, src)
}
func (m *GetPartyGosOffsetRsp) XXX_Size() int {
	return xxx_messageInfo_GetPartyGosOffsetRsp.Size(m)
}
func (m *GetPartyGosOffsetRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPartyGosOffsetRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPartyGosOffsetRsp proto.InternalMessageInfo

func (m *GetPartyGosOffsetRsp) GetInfos() []*AllPartyGoInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

func (m *GetPartyGosOffsetRsp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

type UpdatePartyGoReq struct {
	PostId               string       `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Info                 *PartyGoInfo `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	IsPartygoUser        bool         `protobuf:"varint,3,opt,name=is_partygo_user,json=isPartygoUser,proto3" json:"is_partygo_user,omitempty"`
	Gameid               uint32       `protobuf:"varint,4,opt,name=gameid,proto3" json:"gameid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UpdatePartyGoReq) Reset()         { *m = UpdatePartyGoReq{} }
func (m *UpdatePartyGoReq) String() string { return proto.CompactTextString(m) }
func (*UpdatePartyGoReq) ProtoMessage()    {}
func (*UpdatePartyGoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{8}
}
func (m *UpdatePartyGoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePartyGoReq.Unmarshal(m, b)
}
func (m *UpdatePartyGoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePartyGoReq.Marshal(b, m, deterministic)
}
func (dst *UpdatePartyGoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePartyGoReq.Merge(dst, src)
}
func (m *UpdatePartyGoReq) XXX_Size() int {
	return xxx_messageInfo_UpdatePartyGoReq.Size(m)
}
func (m *UpdatePartyGoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePartyGoReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePartyGoReq proto.InternalMessageInfo

func (m *UpdatePartyGoReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *UpdatePartyGoReq) GetInfo() *PartyGoInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *UpdatePartyGoReq) GetIsPartygoUser() bool {
	if m != nil {
		return m.IsPartygoUser
	}
	return false
}

func (m *UpdatePartyGoReq) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

type UpdatePartyGoRsp struct {
	NoPermission         bool     `protobuf:"varint,1,opt,name=no_permission,json=noPermission,proto3" json:"no_permission,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePartyGoRsp) Reset()         { *m = UpdatePartyGoRsp{} }
func (m *UpdatePartyGoRsp) String() string { return proto.CompactTextString(m) }
func (*UpdatePartyGoRsp) ProtoMessage()    {}
func (*UpdatePartyGoRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{9}
}
func (m *UpdatePartyGoRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePartyGoRsp.Unmarshal(m, b)
}
func (m *UpdatePartyGoRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePartyGoRsp.Marshal(b, m, deterministic)
}
func (dst *UpdatePartyGoRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePartyGoRsp.Merge(dst, src)
}
func (m *UpdatePartyGoRsp) XXX_Size() int {
	return xxx_messageInfo_UpdatePartyGoRsp.Size(m)
}
func (m *UpdatePartyGoRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePartyGoRsp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePartyGoRsp proto.InternalMessageInfo

func (m *UpdatePartyGoRsp) GetNoPermission() bool {
	if m != nil {
		return m.NoPermission
	}
	return false
}

type DelInfo struct {
	PostId               string     `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	FeedTypes            []FeedType `protobuf:"varint,2,rep,packed,name=feed_types,json=feedTypes,proto3,enum=ugc.opeconfig.FeedType" json:"feed_types,omitempty"`
	TopicIds             []string   `protobuf:"bytes,3,rep,name=topic_ids,json=topicIds,proto3" json:"topic_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *DelInfo) Reset()         { *m = DelInfo{} }
func (m *DelInfo) String() string { return proto.CompactTextString(m) }
func (*DelInfo) ProtoMessage()    {}
func (*DelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{10}
}
func (m *DelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelInfo.Unmarshal(m, b)
}
func (m *DelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelInfo.Marshal(b, m, deterministic)
}
func (dst *DelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelInfo.Merge(dst, src)
}
func (m *DelInfo) XXX_Size() int {
	return xxx_messageInfo_DelInfo.Size(m)
}
func (m *DelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DelInfo proto.InternalMessageInfo

func (m *DelInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *DelInfo) GetFeedTypes() []FeedType {
	if m != nil {
		return m.FeedTypes
	}
	return nil
}

func (m *DelInfo) GetTopicIds() []string {
	if m != nil {
		return m.TopicIds
	}
	return nil
}

type DelPartyGoReq struct {
	Infos                []*DelInfo `protobuf:"bytes,1,rep,name=infos,proto3" json:"infos,omitempty"`
	IsPartygoUser        bool       `protobuf:"varint,2,opt,name=is_partygo_user,json=isPartygoUser,proto3" json:"is_partygo_user,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *DelPartyGoReq) Reset()         { *m = DelPartyGoReq{} }
func (m *DelPartyGoReq) String() string { return proto.CompactTextString(m) }
func (*DelPartyGoReq) ProtoMessage()    {}
func (*DelPartyGoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{11}
}
func (m *DelPartyGoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPartyGoReq.Unmarshal(m, b)
}
func (m *DelPartyGoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPartyGoReq.Marshal(b, m, deterministic)
}
func (dst *DelPartyGoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPartyGoReq.Merge(dst, src)
}
func (m *DelPartyGoReq) XXX_Size() int {
	return xxx_messageInfo_DelPartyGoReq.Size(m)
}
func (m *DelPartyGoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPartyGoReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelPartyGoReq proto.InternalMessageInfo

func (m *DelPartyGoReq) GetInfos() []*DelInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

func (m *DelPartyGoReq) GetIsPartygoUser() bool {
	if m != nil {
		return m.IsPartygoUser
	}
	return false
}

type DelPartyGoRsp struct {
	NoPermission         bool     `protobuf:"varint,1,opt,name=no_permission,json=noPermission,proto3" json:"no_permission,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPartyGoRsp) Reset()         { *m = DelPartyGoRsp{} }
func (m *DelPartyGoRsp) String() string { return proto.CompactTextString(m) }
func (*DelPartyGoRsp) ProtoMessage()    {}
func (*DelPartyGoRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{12}
}
func (m *DelPartyGoRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPartyGoRsp.Unmarshal(m, b)
}
func (m *DelPartyGoRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPartyGoRsp.Marshal(b, m, deterministic)
}
func (dst *DelPartyGoRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPartyGoRsp.Merge(dst, src)
}
func (m *DelPartyGoRsp) XXX_Size() int {
	return xxx_messageInfo_DelPartyGoRsp.Size(m)
}
func (m *DelPartyGoRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPartyGoRsp.DiscardUnknown(m)
}

var xxx_messageInfo_DelPartyGoRsp proto.InternalMessageInfo

func (m *DelPartyGoRsp) GetNoPermission() bool {
	if m != nil {
		return m.NoPermission
	}
	return false
}

type AddPartyGoOriginReq struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Url                  string   `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	Text                 string   `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	Gameid               uint32   `protobuf:"varint,4,opt,name=gameid,proto3" json:"gameid,omitempty"`
	TopicIds             []string `protobuf:"bytes,5,rep,name=topic_ids,json=topicIds,proto3" json:"topic_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddPartyGoOriginReq) Reset()         { *m = AddPartyGoOriginReq{} }
func (m *AddPartyGoOriginReq) String() string { return proto.CompactTextString(m) }
func (*AddPartyGoOriginReq) ProtoMessage()    {}
func (*AddPartyGoOriginReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{13}
}
func (m *AddPartyGoOriginReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPartyGoOriginReq.Unmarshal(m, b)
}
func (m *AddPartyGoOriginReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPartyGoOriginReq.Marshal(b, m, deterministic)
}
func (dst *AddPartyGoOriginReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPartyGoOriginReq.Merge(dst, src)
}
func (m *AddPartyGoOriginReq) XXX_Size() int {
	return xxx_messageInfo_AddPartyGoOriginReq.Size(m)
}
func (m *AddPartyGoOriginReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPartyGoOriginReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddPartyGoOriginReq proto.InternalMessageInfo

func (m *AddPartyGoOriginReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *AddPartyGoOriginReq) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *AddPartyGoOriginReq) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *AddPartyGoOriginReq) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *AddPartyGoOriginReq) GetTopicIds() []string {
	if m != nil {
		return m.TopicIds
	}
	return nil
}

type AddPartyGoOriginRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddPartyGoOriginRsp) Reset()         { *m = AddPartyGoOriginRsp{} }
func (m *AddPartyGoOriginRsp) String() string { return proto.CompactTextString(m) }
func (*AddPartyGoOriginRsp) ProtoMessage()    {}
func (*AddPartyGoOriginRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{14}
}
func (m *AddPartyGoOriginRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPartyGoOriginRsp.Unmarshal(m, b)
}
func (m *AddPartyGoOriginRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPartyGoOriginRsp.Marshal(b, m, deterministic)
}
func (dst *AddPartyGoOriginRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPartyGoOriginRsp.Merge(dst, src)
}
func (m *AddPartyGoOriginRsp) XXX_Size() int {
	return xxx_messageInfo_AddPartyGoOriginRsp.Size(m)
}
func (m *AddPartyGoOriginRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPartyGoOriginRsp.DiscardUnknown(m)
}

var xxx_messageInfo_AddPartyGoOriginRsp proto.InternalMessageInfo

type TopicItem struct {
	TopicId              string   `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	TopicName            string   `protobuf:"bytes,2,opt,name=topic_name,json=topicName,proto3" json:"topic_name,omitempty"`
	TopicType            uint32   `protobuf:"varint,3,opt,name=topic_type,json=topicType,proto3" json:"topic_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TopicItem) Reset()         { *m = TopicItem{} }
func (m *TopicItem) String() string { return proto.CompactTextString(m) }
func (*TopicItem) ProtoMessage()    {}
func (*TopicItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{15}
}
func (m *TopicItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicItem.Unmarshal(m, b)
}
func (m *TopicItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicItem.Marshal(b, m, deterministic)
}
func (dst *TopicItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicItem.Merge(dst, src)
}
func (m *TopicItem) XXX_Size() int {
	return xxx_messageInfo_TopicItem.Size(m)
}
func (m *TopicItem) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicItem.DiscardUnknown(m)
}

var xxx_messageInfo_TopicItem proto.InternalMessageInfo

func (m *TopicItem) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *TopicItem) GetTopicName() string {
	if m != nil {
		return m.TopicName
	}
	return ""
}

func (m *TopicItem) GetTopicType() uint32 {
	if m != nil {
		return m.TopicType
	}
	return 0
}

type TabTopicInfo struct {
	TabId                string       `protobuf:"bytes,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabName              string       `protobuf:"bytes,2,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	TopicItems           []*TopicItem `protobuf:"bytes,3,rep,name=topic_items,json=topicItems,proto3" json:"topic_items,omitempty"`
	CreateTime           int64        `protobuf:"varint,4,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UpdateTime           int64        `protobuf:"varint,5,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	Index                uint32       `protobuf:"varint,6,opt,name=index,proto3" json:"index,omitempty"`
	MarketIds            []uint32     `protobuf:"varint,7,rep,packed,name=market_ids,json=marketIds,proto3" json:"market_ids,omitempty"`
	TabType              uint32       `protobuf:"varint,8,opt,name=tab_type,json=tabType,proto3" json:"tab_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *TabTopicInfo) Reset()         { *m = TabTopicInfo{} }
func (m *TabTopicInfo) String() string { return proto.CompactTextString(m) }
func (*TabTopicInfo) ProtoMessage()    {}
func (*TabTopicInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{16}
}
func (m *TabTopicInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TabTopicInfo.Unmarshal(m, b)
}
func (m *TabTopicInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TabTopicInfo.Marshal(b, m, deterministic)
}
func (dst *TabTopicInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TabTopicInfo.Merge(dst, src)
}
func (m *TabTopicInfo) XXX_Size() int {
	return xxx_messageInfo_TabTopicInfo.Size(m)
}
func (m *TabTopicInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TabTopicInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TabTopicInfo proto.InternalMessageInfo

func (m *TabTopicInfo) GetTabId() string {
	if m != nil {
		return m.TabId
	}
	return ""
}

func (m *TabTopicInfo) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *TabTopicInfo) GetTopicItems() []*TopicItem {
	if m != nil {
		return m.TopicItems
	}
	return nil
}

func (m *TabTopicInfo) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *TabTopicInfo) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *TabTopicInfo) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *TabTopicInfo) GetMarketIds() []uint32 {
	if m != nil {
		return m.MarketIds
	}
	return nil
}

func (m *TabTopicInfo) GetTabType() uint32 {
	if m != nil {
		return m.TabType
	}
	return 0
}

type GetTabTopicInfoReq struct {
	IsWeb                bool     `protobuf:"varint,1,opt,name=is_web,json=isWeb,proto3" json:"is_web,omitempty"`
	MarketId             uint32   `protobuf:"varint,2,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTabTopicInfoReq) Reset()         { *m = GetTabTopicInfoReq{} }
func (m *GetTabTopicInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetTabTopicInfoReq) ProtoMessage()    {}
func (*GetTabTopicInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{17}
}
func (m *GetTabTopicInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTabTopicInfoReq.Unmarshal(m, b)
}
func (m *GetTabTopicInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTabTopicInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetTabTopicInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTabTopicInfoReq.Merge(dst, src)
}
func (m *GetTabTopicInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetTabTopicInfoReq.Size(m)
}
func (m *GetTabTopicInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTabTopicInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTabTopicInfoReq proto.InternalMessageInfo

func (m *GetTabTopicInfoReq) GetIsWeb() bool {
	if m != nil {
		return m.IsWeb
	}
	return false
}

func (m *GetTabTopicInfoReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

type GetTabTopicInfoRsp struct {
	Infos                []*TabTopicInfo `protobuf:"bytes,1,rep,name=infos,proto3" json:"infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetTabTopicInfoRsp) Reset()         { *m = GetTabTopicInfoRsp{} }
func (m *GetTabTopicInfoRsp) String() string { return proto.CompactTextString(m) }
func (*GetTabTopicInfoRsp) ProtoMessage()    {}
func (*GetTabTopicInfoRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{18}
}
func (m *GetTabTopicInfoRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTabTopicInfoRsp.Unmarshal(m, b)
}
func (m *GetTabTopicInfoRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTabTopicInfoRsp.Marshal(b, m, deterministic)
}
func (dst *GetTabTopicInfoRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTabTopicInfoRsp.Merge(dst, src)
}
func (m *GetTabTopicInfoRsp) XXX_Size() int {
	return xxx_messageInfo_GetTabTopicInfoRsp.Size(m)
}
func (m *GetTabTopicInfoRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTabTopicInfoRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTabTopicInfoRsp proto.InternalMessageInfo

func (m *GetTabTopicInfoRsp) GetInfos() []*TabTopicInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

type AddTabTopicInfoReq struct {
	TabId                string   `protobuf:"bytes,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabName              string   `protobuf:"bytes,2,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	TopicIds             []string `protobuf:"bytes,3,rep,name=topic_ids,json=topicIds,proto3" json:"topic_ids,omitempty"`
	Index                uint32   `protobuf:"varint,4,opt,name=index,proto3" json:"index,omitempty"`
	MarketIds            []uint32 `protobuf:"varint,5,rep,packed,name=market_ids,json=marketIds,proto3" json:"market_ids,omitempty"`
	TabType              uint32   `protobuf:"varint,6,opt,name=tab_type,json=tabType,proto3" json:"tab_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddTabTopicInfoReq) Reset()         { *m = AddTabTopicInfoReq{} }
func (m *AddTabTopicInfoReq) String() string { return proto.CompactTextString(m) }
func (*AddTabTopicInfoReq) ProtoMessage()    {}
func (*AddTabTopicInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{19}
}
func (m *AddTabTopicInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddTabTopicInfoReq.Unmarshal(m, b)
}
func (m *AddTabTopicInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddTabTopicInfoReq.Marshal(b, m, deterministic)
}
func (dst *AddTabTopicInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddTabTopicInfoReq.Merge(dst, src)
}
func (m *AddTabTopicInfoReq) XXX_Size() int {
	return xxx_messageInfo_AddTabTopicInfoReq.Size(m)
}
func (m *AddTabTopicInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddTabTopicInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddTabTopicInfoReq proto.InternalMessageInfo

func (m *AddTabTopicInfoReq) GetTabId() string {
	if m != nil {
		return m.TabId
	}
	return ""
}

func (m *AddTabTopicInfoReq) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *AddTabTopicInfoReq) GetTopicIds() []string {
	if m != nil {
		return m.TopicIds
	}
	return nil
}

func (m *AddTabTopicInfoReq) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *AddTabTopicInfoReq) GetMarketIds() []uint32 {
	if m != nil {
		return m.MarketIds
	}
	return nil
}

func (m *AddTabTopicInfoReq) GetTabType() uint32 {
	if m != nil {
		return m.TabType
	}
	return 0
}

type AddTabTopicInfoRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddTabTopicInfoRsp) Reset()         { *m = AddTabTopicInfoRsp{} }
func (m *AddTabTopicInfoRsp) String() string { return proto.CompactTextString(m) }
func (*AddTabTopicInfoRsp) ProtoMessage()    {}
func (*AddTabTopicInfoRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{20}
}
func (m *AddTabTopicInfoRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddTabTopicInfoRsp.Unmarshal(m, b)
}
func (m *AddTabTopicInfoRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddTabTopicInfoRsp.Marshal(b, m, deterministic)
}
func (dst *AddTabTopicInfoRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddTabTopicInfoRsp.Merge(dst, src)
}
func (m *AddTabTopicInfoRsp) XXX_Size() int {
	return xxx_messageInfo_AddTabTopicInfoRsp.Size(m)
}
func (m *AddTabTopicInfoRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddTabTopicInfoRsp.DiscardUnknown(m)
}

var xxx_messageInfo_AddTabTopicInfoRsp proto.InternalMessageInfo

type DelTabTopicInfoReq struct {
	TabId                string   `protobuf:"bytes,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabName              string   `protobuf:"bytes,2,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelTabTopicInfoReq) Reset()         { *m = DelTabTopicInfoReq{} }
func (m *DelTabTopicInfoReq) String() string { return proto.CompactTextString(m) }
func (*DelTabTopicInfoReq) ProtoMessage()    {}
func (*DelTabTopicInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{21}
}
func (m *DelTabTopicInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelTabTopicInfoReq.Unmarshal(m, b)
}
func (m *DelTabTopicInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelTabTopicInfoReq.Marshal(b, m, deterministic)
}
func (dst *DelTabTopicInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelTabTopicInfoReq.Merge(dst, src)
}
func (m *DelTabTopicInfoReq) XXX_Size() int {
	return xxx_messageInfo_DelTabTopicInfoReq.Size(m)
}
func (m *DelTabTopicInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelTabTopicInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelTabTopicInfoReq proto.InternalMessageInfo

func (m *DelTabTopicInfoReq) GetTabId() string {
	if m != nil {
		return m.TabId
	}
	return ""
}

func (m *DelTabTopicInfoReq) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

type DelTabTopicInfoRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelTabTopicInfoRsp) Reset()         { *m = DelTabTopicInfoRsp{} }
func (m *DelTabTopicInfoRsp) String() string { return proto.CompactTextString(m) }
func (*DelTabTopicInfoRsp) ProtoMessage()    {}
func (*DelTabTopicInfoRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{22}
}
func (m *DelTabTopicInfoRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelTabTopicInfoRsp.Unmarshal(m, b)
}
func (m *DelTabTopicInfoRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelTabTopicInfoRsp.Marshal(b, m, deterministic)
}
func (dst *DelTabTopicInfoRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelTabTopicInfoRsp.Merge(dst, src)
}
func (m *DelTabTopicInfoRsp) XXX_Size() int {
	return xxx_messageInfo_DelTabTopicInfoRsp.Size(m)
}
func (m *DelTabTopicInfoRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelTabTopicInfoRsp.DiscardUnknown(m)
}

var xxx_messageInfo_DelTabTopicInfoRsp proto.InternalMessageInfo

type TabTopicSort struct {
	TabId                string   `protobuf:"bytes,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Index                uint32   `protobuf:"varint,2,opt,name=index,proto3" json:"index,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TabTopicSort) Reset()         { *m = TabTopicSort{} }
func (m *TabTopicSort) String() string { return proto.CompactTextString(m) }
func (*TabTopicSort) ProtoMessage()    {}
func (*TabTopicSort) Descriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{23}
}
func (m *TabTopicSort) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TabTopicSort.Unmarshal(m, b)
}
func (m *TabTopicSort) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TabTopicSort.Marshal(b, m, deterministic)
}
func (dst *TabTopicSort) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TabTopicSort.Merge(dst, src)
}
func (m *TabTopicSort) XXX_Size() int {
	return xxx_messageInfo_TabTopicSort.Size(m)
}
func (m *TabTopicSort) XXX_DiscardUnknown() {
	xxx_messageInfo_TabTopicSort.DiscardUnknown(m)
}

var xxx_messageInfo_TabTopicSort proto.InternalMessageInfo

func (m *TabTopicSort) GetTabId() string {
	if m != nil {
		return m.TabId
	}
	return ""
}

func (m *TabTopicSort) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

type SetTabTopicInfoReq struct {
	TabSort              []*TabTopicSort `protobuf:"bytes,1,rep,name=tab_sort,json=tabSort,proto3" json:"tab_sort,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *SetTabTopicInfoReq) Reset()         { *m = SetTabTopicInfoReq{} }
func (m *SetTabTopicInfoReq) String() string { return proto.CompactTextString(m) }
func (*SetTabTopicInfoReq) ProtoMessage()    {}
func (*SetTabTopicInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{24}
}
func (m *SetTabTopicInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetTabTopicInfoReq.Unmarshal(m, b)
}
func (m *SetTabTopicInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetTabTopicInfoReq.Marshal(b, m, deterministic)
}
func (dst *SetTabTopicInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetTabTopicInfoReq.Merge(dst, src)
}
func (m *SetTabTopicInfoReq) XXX_Size() int {
	return xxx_messageInfo_SetTabTopicInfoReq.Size(m)
}
func (m *SetTabTopicInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetTabTopicInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetTabTopicInfoReq proto.InternalMessageInfo

func (m *SetTabTopicInfoReq) GetTabSort() []*TabTopicSort {
	if m != nil {
		return m.TabSort
	}
	return nil
}

type SetTabTopicInfoRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetTabTopicInfoRsp) Reset()         { *m = SetTabTopicInfoRsp{} }
func (m *SetTabTopicInfoRsp) String() string { return proto.CompactTextString(m) }
func (*SetTabTopicInfoRsp) ProtoMessage()    {}
func (*SetTabTopicInfoRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{25}
}
func (m *SetTabTopicInfoRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetTabTopicInfoRsp.Unmarshal(m, b)
}
func (m *SetTabTopicInfoRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetTabTopicInfoRsp.Marshal(b, m, deterministic)
}
func (dst *SetTabTopicInfoRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetTabTopicInfoRsp.Merge(dst, src)
}
func (m *SetTabTopicInfoRsp) XXX_Size() int {
	return xxx_messageInfo_SetTabTopicInfoRsp.Size(m)
}
func (m *SetTabTopicInfoRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetTabTopicInfoRsp.DiscardUnknown(m)
}

var xxx_messageInfo_SetTabTopicInfoRsp proto.InternalMessageInfo

type SendRcmdNotifyReq struct {
	PostIds              []string `protobuf:"bytes,1,rep,name=post_ids,json=postIds,proto3" json:"post_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendRcmdNotifyReq) Reset()         { *m = SendRcmdNotifyReq{} }
func (m *SendRcmdNotifyReq) String() string { return proto.CompactTextString(m) }
func (*SendRcmdNotifyReq) ProtoMessage()    {}
func (*SendRcmdNotifyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{26}
}
func (m *SendRcmdNotifyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendRcmdNotifyReq.Unmarshal(m, b)
}
func (m *SendRcmdNotifyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendRcmdNotifyReq.Marshal(b, m, deterministic)
}
func (dst *SendRcmdNotifyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendRcmdNotifyReq.Merge(dst, src)
}
func (m *SendRcmdNotifyReq) XXX_Size() int {
	return xxx_messageInfo_SendRcmdNotifyReq.Size(m)
}
func (m *SendRcmdNotifyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendRcmdNotifyReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendRcmdNotifyReq proto.InternalMessageInfo

func (m *SendRcmdNotifyReq) GetPostIds() []string {
	if m != nil {
		return m.PostIds
	}
	return nil
}

type SendRcmdNotifyRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendRcmdNotifyRsp) Reset()         { *m = SendRcmdNotifyRsp{} }
func (m *SendRcmdNotifyRsp) String() string { return proto.CompactTextString(m) }
func (*SendRcmdNotifyRsp) ProtoMessage()    {}
func (*SendRcmdNotifyRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{27}
}
func (m *SendRcmdNotifyRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendRcmdNotifyRsp.Unmarshal(m, b)
}
func (m *SendRcmdNotifyRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendRcmdNotifyRsp.Marshal(b, m, deterministic)
}
func (dst *SendRcmdNotifyRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendRcmdNotifyRsp.Merge(dst, src)
}
func (m *SendRcmdNotifyRsp) XXX_Size() int {
	return xxx_messageInfo_SendRcmdNotifyRsp.Size(m)
}
func (m *SendRcmdNotifyRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendRcmdNotifyRsp.DiscardUnknown(m)
}

var xxx_messageInfo_SendRcmdNotifyRsp proto.InternalMessageInfo

type GetGameEntranceReq struct {
	MarketId             uint32   `protobuf:"varint,1,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ReqUid               uint32   `protobuf:"varint,2,opt,name=req_uid,json=reqUid,proto3" json:"req_uid,omitempty"`
	PageOwnerUid         uint32   `protobuf:"varint,3,opt,name=page_owner_uid,json=pageOwnerUid,proto3" json:"page_owner_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameEntranceReq) Reset()         { *m = GetGameEntranceReq{} }
func (m *GetGameEntranceReq) String() string { return proto.CompactTextString(m) }
func (*GetGameEntranceReq) ProtoMessage()    {}
func (*GetGameEntranceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{28}
}
func (m *GetGameEntranceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameEntranceReq.Unmarshal(m, b)
}
func (m *GetGameEntranceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameEntranceReq.Marshal(b, m, deterministic)
}
func (dst *GetGameEntranceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameEntranceReq.Merge(dst, src)
}
func (m *GetGameEntranceReq) XXX_Size() int {
	return xxx_messageInfo_GetGameEntranceReq.Size(m)
}
func (m *GetGameEntranceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameEntranceReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameEntranceReq proto.InternalMessageInfo

func (m *GetGameEntranceReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *GetGameEntranceReq) GetReqUid() uint32 {
	if m != nil {
		return m.ReqUid
	}
	return 0
}

func (m *GetGameEntranceReq) GetPageOwnerUid() uint32 {
	if m != nil {
		return m.PageOwnerUid
	}
	return 0
}

type GetGameEntranceResp struct {
	GameEntrance         *GetGameEntranceResp_GameEntrance `protobuf:"bytes,1,opt,name=game_entrance,json=gameEntrance,proto3" json:"game_entrance,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *GetGameEntranceResp) Reset()         { *m = GetGameEntranceResp{} }
func (m *GetGameEntranceResp) String() string { return proto.CompactTextString(m) }
func (*GetGameEntranceResp) ProtoMessage()    {}
func (*GetGameEntranceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{29}
}
func (m *GetGameEntranceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameEntranceResp.Unmarshal(m, b)
}
func (m *GetGameEntranceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameEntranceResp.Marshal(b, m, deterministic)
}
func (dst *GetGameEntranceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameEntranceResp.Merge(dst, src)
}
func (m *GetGameEntranceResp) XXX_Size() int {
	return xxx_messageInfo_GetGameEntranceResp.Size(m)
}
func (m *GetGameEntranceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameEntranceResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameEntranceResp proto.InternalMessageInfo

func (m *GetGameEntranceResp) GetGameEntrance() *GetGameEntranceResp_GameEntrance {
	if m != nil {
		return m.GameEntrance
	}
	return nil
}

type GetGameEntranceResp_GameEntrance struct {
	GameId     uint32 `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GameName   string `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	IconImgUrl string `protobuf:"bytes,3,opt,name=icon_img_url,json=iconImgUrl,proto3" json:"icon_img_url,omitempty"`
	HintImgUrl string `protobuf:"bytes,4,opt,name=hint_img_url,json=hintImgUrl,proto3" json:"hint_img_url,omitempty"`
	JumpUrl    string `protobuf:"bytes,5,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	// 以下字段在客态才用到
	PageOwnerPlayStatus   GetGameEntranceResp_PlayStatus   `protobuf:"varint,6,opt,name=page_owner_play_status,json=pageOwnerPlayStatus,proto3,enum=ugc.opeconfig.GetGameEntranceResp_PlayStatus" json:"page_owner_play_status,omitempty"`
	PageOwnerOnlineStatus GetGameEntranceResp_OnlineStatus `protobuf:"varint,7,opt,name=page_owner_online_status,json=pageOwnerOnlineStatus,proto3,enum=ugc.opeconfig.GetGameEntranceResp_OnlineStatus" json:"page_owner_online_status,omitempty"`
	BackgroundImgUrl      string                           `protobuf:"bytes,8,opt,name=background_img_url,json=backgroundImgUrl,proto3" json:"background_img_url,omitempty"`
	ImImgUrl              string                           `protobuf:"bytes,9,opt,name=im_img_url,json=imImgUrl,proto3" json:"im_img_url,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}                         `json:"-"`
	XXX_unrecognized      []byte                           `json:"-"`
	XXX_sizecache         int32                            `json:"-"`
}

func (m *GetGameEntranceResp_GameEntrance) Reset()         { *m = GetGameEntranceResp_GameEntrance{} }
func (m *GetGameEntranceResp_GameEntrance) String() string { return proto.CompactTextString(m) }
func (*GetGameEntranceResp_GameEntrance) ProtoMessage()    {}
func (*GetGameEntranceResp_GameEntrance) Descriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{29, 0}
}
func (m *GetGameEntranceResp_GameEntrance) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameEntranceResp_GameEntrance.Unmarshal(m, b)
}
func (m *GetGameEntranceResp_GameEntrance) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameEntranceResp_GameEntrance.Marshal(b, m, deterministic)
}
func (dst *GetGameEntranceResp_GameEntrance) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameEntranceResp_GameEntrance.Merge(dst, src)
}
func (m *GetGameEntranceResp_GameEntrance) XXX_Size() int {
	return xxx_messageInfo_GetGameEntranceResp_GameEntrance.Size(m)
}
func (m *GetGameEntranceResp_GameEntrance) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameEntranceResp_GameEntrance.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameEntranceResp_GameEntrance proto.InternalMessageInfo

func (m *GetGameEntranceResp_GameEntrance) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetGameEntranceResp_GameEntrance) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *GetGameEntranceResp_GameEntrance) GetIconImgUrl() string {
	if m != nil {
		return m.IconImgUrl
	}
	return ""
}

func (m *GetGameEntranceResp_GameEntrance) GetHintImgUrl() string {
	if m != nil {
		return m.HintImgUrl
	}
	return ""
}

func (m *GetGameEntranceResp_GameEntrance) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *GetGameEntranceResp_GameEntrance) GetPageOwnerPlayStatus() GetGameEntranceResp_PlayStatus {
	if m != nil {
		return m.PageOwnerPlayStatus
	}
	return GetGameEntranceResp_UNKNOWN
}

func (m *GetGameEntranceResp_GameEntrance) GetPageOwnerOnlineStatus() GetGameEntranceResp_OnlineStatus {
	if m != nil {
		return m.PageOwnerOnlineStatus
	}
	return GetGameEntranceResp_UNDEFINE
}

func (m *GetGameEntranceResp_GameEntrance) GetBackgroundImgUrl() string {
	if m != nil {
		return m.BackgroundImgUrl
	}
	return ""
}

func (m *GetGameEntranceResp_GameEntrance) GetImImgUrl() string {
	if m != nil {
		return m.ImImgUrl
	}
	return ""
}

type PostTeleport struct {
	PostId               string     `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Text                 string     `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	Url                  string     `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`
	FeedTypes            []FeedType `protobuf:"varint,4,rep,packed,name=feed_types,json=feedTypes,proto3,enum=ugc.opeconfig.FeedType" json:"feed_types,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *PostTeleport) Reset()         { *m = PostTeleport{} }
func (m *PostTeleport) String() string { return proto.CompactTextString(m) }
func (*PostTeleport) ProtoMessage()    {}
func (*PostTeleport) Descriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{30}
}
func (m *PostTeleport) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostTeleport.Unmarshal(m, b)
}
func (m *PostTeleport) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostTeleport.Marshal(b, m, deterministic)
}
func (dst *PostTeleport) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostTeleport.Merge(dst, src)
}
func (m *PostTeleport) XXX_Size() int {
	return xxx_messageInfo_PostTeleport.Size(m)
}
func (m *PostTeleport) XXX_DiscardUnknown() {
	xxx_messageInfo_PostTeleport.DiscardUnknown(m)
}

var xxx_messageInfo_PostTeleport proto.InternalMessageInfo

func (m *PostTeleport) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *PostTeleport) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *PostTeleport) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *PostTeleport) GetFeedTypes() []FeedType {
	if m != nil {
		return m.FeedTypes
	}
	return nil
}

type AddPostTeleportReq struct {
	Teleport             *PostTeleport `protobuf:"bytes,1,opt,name=teleport,proto3" json:"teleport,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *AddPostTeleportReq) Reset()         { *m = AddPostTeleportReq{} }
func (m *AddPostTeleportReq) String() string { return proto.CompactTextString(m) }
func (*AddPostTeleportReq) ProtoMessage()    {}
func (*AddPostTeleportReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{31}
}
func (m *AddPostTeleportReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPostTeleportReq.Unmarshal(m, b)
}
func (m *AddPostTeleportReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPostTeleportReq.Marshal(b, m, deterministic)
}
func (dst *AddPostTeleportReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPostTeleportReq.Merge(dst, src)
}
func (m *AddPostTeleportReq) XXX_Size() int {
	return xxx_messageInfo_AddPostTeleportReq.Size(m)
}
func (m *AddPostTeleportReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPostTeleportReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddPostTeleportReq proto.InternalMessageInfo

func (m *AddPostTeleportReq) GetTeleport() *PostTeleport {
	if m != nil {
		return m.Teleport
	}
	return nil
}

type AddPostTeleportResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddPostTeleportResp) Reset()         { *m = AddPostTeleportResp{} }
func (m *AddPostTeleportResp) String() string { return proto.CompactTextString(m) }
func (*AddPostTeleportResp) ProtoMessage()    {}
func (*AddPostTeleportResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_opeconfig_d1e993cc7a6d6ca7, []int{32}
}
func (m *AddPostTeleportResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPostTeleportResp.Unmarshal(m, b)
}
func (m *AddPostTeleportResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPostTeleportResp.Marshal(b, m, deterministic)
}
func (dst *AddPostTeleportResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPostTeleportResp.Merge(dst, src)
}
func (m *AddPostTeleportResp) XXX_Size() int {
	return xxx_messageInfo_AddPostTeleportResp.Size(m)
}
func (m *AddPostTeleportResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPostTeleportResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddPostTeleportResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*PartyGoInfo)(nil), "ugc.opeconfig.PartyGoInfo")
	proto.RegisterType((*AddPartyGoReq)(nil), "ugc.opeconfig.AddPartyGoReq")
	proto.RegisterType((*AddPartyGoRsp)(nil), "ugc.opeconfig.AddPartyGoRsp")
	proto.RegisterType((*GetPartyGosReq)(nil), "ugc.opeconfig.GetPartyGosReq")
	proto.RegisterType((*GetPartyGosRsp)(nil), "ugc.opeconfig.GetPartyGosRsp")
	proto.RegisterType((*GetPartyGosOffsetReq)(nil), "ugc.opeconfig.GetPartyGosOffsetReq")
	proto.RegisterType((*AllPartyGoInfo)(nil), "ugc.opeconfig.AllPartyGoInfo")
	proto.RegisterType((*GetPartyGosOffsetRsp)(nil), "ugc.opeconfig.GetPartyGosOffsetRsp")
	proto.RegisterType((*UpdatePartyGoReq)(nil), "ugc.opeconfig.UpdatePartyGoReq")
	proto.RegisterType((*UpdatePartyGoRsp)(nil), "ugc.opeconfig.UpdatePartyGoRsp")
	proto.RegisterType((*DelInfo)(nil), "ugc.opeconfig.DelInfo")
	proto.RegisterType((*DelPartyGoReq)(nil), "ugc.opeconfig.DelPartyGoReq")
	proto.RegisterType((*DelPartyGoRsp)(nil), "ugc.opeconfig.DelPartyGoRsp")
	proto.RegisterType((*AddPartyGoOriginReq)(nil), "ugc.opeconfig.AddPartyGoOriginReq")
	proto.RegisterType((*AddPartyGoOriginRsp)(nil), "ugc.opeconfig.AddPartyGoOriginRsp")
	proto.RegisterType((*TopicItem)(nil), "ugc.opeconfig.TopicItem")
	proto.RegisterType((*TabTopicInfo)(nil), "ugc.opeconfig.TabTopicInfo")
	proto.RegisterType((*GetTabTopicInfoReq)(nil), "ugc.opeconfig.GetTabTopicInfoReq")
	proto.RegisterType((*GetTabTopicInfoRsp)(nil), "ugc.opeconfig.GetTabTopicInfoRsp")
	proto.RegisterType((*AddTabTopicInfoReq)(nil), "ugc.opeconfig.AddTabTopicInfoReq")
	proto.RegisterType((*AddTabTopicInfoRsp)(nil), "ugc.opeconfig.AddTabTopicInfoRsp")
	proto.RegisterType((*DelTabTopicInfoReq)(nil), "ugc.opeconfig.DelTabTopicInfoReq")
	proto.RegisterType((*DelTabTopicInfoRsp)(nil), "ugc.opeconfig.DelTabTopicInfoRsp")
	proto.RegisterType((*TabTopicSort)(nil), "ugc.opeconfig.TabTopicSort")
	proto.RegisterType((*SetTabTopicInfoReq)(nil), "ugc.opeconfig.SetTabTopicInfoReq")
	proto.RegisterType((*SetTabTopicInfoRsp)(nil), "ugc.opeconfig.SetTabTopicInfoRsp")
	proto.RegisterType((*SendRcmdNotifyReq)(nil), "ugc.opeconfig.SendRcmdNotifyReq")
	proto.RegisterType((*SendRcmdNotifyRsp)(nil), "ugc.opeconfig.SendRcmdNotifyRsp")
	proto.RegisterType((*GetGameEntranceReq)(nil), "ugc.opeconfig.GetGameEntranceReq")
	proto.RegisterType((*GetGameEntranceResp)(nil), "ugc.opeconfig.GetGameEntranceResp")
	proto.RegisterType((*GetGameEntranceResp_GameEntrance)(nil), "ugc.opeconfig.GetGameEntranceResp.GameEntrance")
	proto.RegisterType((*PostTeleport)(nil), "ugc.opeconfig.PostTeleport")
	proto.RegisterType((*AddPostTeleportReq)(nil), "ugc.opeconfig.AddPostTeleportReq")
	proto.RegisterType((*AddPostTeleportResp)(nil), "ugc.opeconfig.AddPostTeleportResp")
	proto.RegisterEnum("ugc.opeconfig.FeedType", FeedType_name, FeedType_value)
	proto.RegisterEnum("ugc.opeconfig.GetGameEntranceResp_PlayStatus", GetGameEntranceResp_PlayStatus_name, GetGameEntranceResp_PlayStatus_value)
	proto.RegisterEnum("ugc.opeconfig.GetGameEntranceResp_OnlineStatus", GetGameEntranceResp_OnlineStatus_name, GetGameEntranceResp_OnlineStatus_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// UgcOpeConfigClient is the client API for UgcOpeConfig service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UgcOpeConfigClient interface {
	AddPartyGo(ctx context.Context, in *AddPartyGoReq, opts ...grpc.CallOption) (*AddPartyGoRsp, error)
	AddPartyGoOrigin(ctx context.Context, in *AddPartyGoOriginReq, opts ...grpc.CallOption) (*AddPartyGoOriginRsp, error)
	GetPartyGos(ctx context.Context, in *GetPartyGosReq, opts ...grpc.CallOption) (*GetPartyGosRsp, error)
	GetPartyGosOffset(ctx context.Context, in *GetPartyGosOffsetReq, opts ...grpc.CallOption) (*GetPartyGosOffsetRsp, error)
	UpdatePartyGo(ctx context.Context, in *UpdatePartyGoReq, opts ...grpc.CallOption) (*UpdatePartyGoRsp, error)
	DelPartyGo(ctx context.Context, in *DelPartyGoReq, opts ...grpc.CallOption) (*DelPartyGoRsp, error)
	GetTabTopicInfo(ctx context.Context, in *GetTabTopicInfoReq, opts ...grpc.CallOption) (*GetTabTopicInfoRsp, error)
	AddTabTopicInfo(ctx context.Context, in *AddTabTopicInfoReq, opts ...grpc.CallOption) (*AddTabTopicInfoRsp, error)
	DelTabTopicInfo(ctx context.Context, in *DelTabTopicInfoReq, opts ...grpc.CallOption) (*DelTabTopicInfoRsp, error)
	SetTabTopicInfo(ctx context.Context, in *SetTabTopicInfoReq, opts ...grpc.CallOption) (*SetTabTopicInfoRsp, error)
	// 帖子被推荐到声控板块时，向用户发送通知
	SendRcmdNotify(ctx context.Context, in *SendRcmdNotifyReq, opts ...grpc.CallOption) (*SendRcmdNotifyRsp, error)
	// 获取个人主页游戏入口
	GetGameEntrance(ctx context.Context, in *GetGameEntranceReq, opts ...grpc.CallOption) (*GetGameEntranceResp, error)
	// 帖子跳转短链配置
	AddPostTeleport(ctx context.Context, in *AddPostTeleportReq, opts ...grpc.CallOption) (*AddPostTeleportResp, error)
}

type ugcOpeConfigClient struct {
	cc *grpc.ClientConn
}

func NewUgcOpeConfigClient(cc *grpc.ClientConn) UgcOpeConfigClient {
	return &ugcOpeConfigClient{cc}
}

func (c *ugcOpeConfigClient) AddPartyGo(ctx context.Context, in *AddPartyGoReq, opts ...grpc.CallOption) (*AddPartyGoRsp, error) {
	out := new(AddPartyGoRsp)
	err := c.cc.Invoke(ctx, "/ugc.opeconfig.UgcOpeConfig/AddPartyGo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcOpeConfigClient) AddPartyGoOrigin(ctx context.Context, in *AddPartyGoOriginReq, opts ...grpc.CallOption) (*AddPartyGoOriginRsp, error) {
	out := new(AddPartyGoOriginRsp)
	err := c.cc.Invoke(ctx, "/ugc.opeconfig.UgcOpeConfig/AddPartyGoOrigin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcOpeConfigClient) GetPartyGos(ctx context.Context, in *GetPartyGosReq, opts ...grpc.CallOption) (*GetPartyGosRsp, error) {
	out := new(GetPartyGosRsp)
	err := c.cc.Invoke(ctx, "/ugc.opeconfig.UgcOpeConfig/GetPartyGos", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcOpeConfigClient) GetPartyGosOffset(ctx context.Context, in *GetPartyGosOffsetReq, opts ...grpc.CallOption) (*GetPartyGosOffsetRsp, error) {
	out := new(GetPartyGosOffsetRsp)
	err := c.cc.Invoke(ctx, "/ugc.opeconfig.UgcOpeConfig/GetPartyGosOffset", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcOpeConfigClient) UpdatePartyGo(ctx context.Context, in *UpdatePartyGoReq, opts ...grpc.CallOption) (*UpdatePartyGoRsp, error) {
	out := new(UpdatePartyGoRsp)
	err := c.cc.Invoke(ctx, "/ugc.opeconfig.UgcOpeConfig/UpdatePartyGo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcOpeConfigClient) DelPartyGo(ctx context.Context, in *DelPartyGoReq, opts ...grpc.CallOption) (*DelPartyGoRsp, error) {
	out := new(DelPartyGoRsp)
	err := c.cc.Invoke(ctx, "/ugc.opeconfig.UgcOpeConfig/DelPartyGo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcOpeConfigClient) GetTabTopicInfo(ctx context.Context, in *GetTabTopicInfoReq, opts ...grpc.CallOption) (*GetTabTopicInfoRsp, error) {
	out := new(GetTabTopicInfoRsp)
	err := c.cc.Invoke(ctx, "/ugc.opeconfig.UgcOpeConfig/GetTabTopicInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcOpeConfigClient) AddTabTopicInfo(ctx context.Context, in *AddTabTopicInfoReq, opts ...grpc.CallOption) (*AddTabTopicInfoRsp, error) {
	out := new(AddTabTopicInfoRsp)
	err := c.cc.Invoke(ctx, "/ugc.opeconfig.UgcOpeConfig/AddTabTopicInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcOpeConfigClient) DelTabTopicInfo(ctx context.Context, in *DelTabTopicInfoReq, opts ...grpc.CallOption) (*DelTabTopicInfoRsp, error) {
	out := new(DelTabTopicInfoRsp)
	err := c.cc.Invoke(ctx, "/ugc.opeconfig.UgcOpeConfig/DelTabTopicInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcOpeConfigClient) SetTabTopicInfo(ctx context.Context, in *SetTabTopicInfoReq, opts ...grpc.CallOption) (*SetTabTopicInfoRsp, error) {
	out := new(SetTabTopicInfoRsp)
	err := c.cc.Invoke(ctx, "/ugc.opeconfig.UgcOpeConfig/SetTabTopicInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcOpeConfigClient) SendRcmdNotify(ctx context.Context, in *SendRcmdNotifyReq, opts ...grpc.CallOption) (*SendRcmdNotifyRsp, error) {
	out := new(SendRcmdNotifyRsp)
	err := c.cc.Invoke(ctx, "/ugc.opeconfig.UgcOpeConfig/SendRcmdNotify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcOpeConfigClient) GetGameEntrance(ctx context.Context, in *GetGameEntranceReq, opts ...grpc.CallOption) (*GetGameEntranceResp, error) {
	out := new(GetGameEntranceResp)
	err := c.cc.Invoke(ctx, "/ugc.opeconfig.UgcOpeConfig/GetGameEntrance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcOpeConfigClient) AddPostTeleport(ctx context.Context, in *AddPostTeleportReq, opts ...grpc.CallOption) (*AddPostTeleportResp, error) {
	out := new(AddPostTeleportResp)
	err := c.cc.Invoke(ctx, "/ugc.opeconfig.UgcOpeConfig/AddPostTeleport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UgcOpeConfigServer is the server API for UgcOpeConfig service.
type UgcOpeConfigServer interface {
	AddPartyGo(context.Context, *AddPartyGoReq) (*AddPartyGoRsp, error)
	AddPartyGoOrigin(context.Context, *AddPartyGoOriginReq) (*AddPartyGoOriginRsp, error)
	GetPartyGos(context.Context, *GetPartyGosReq) (*GetPartyGosRsp, error)
	GetPartyGosOffset(context.Context, *GetPartyGosOffsetReq) (*GetPartyGosOffsetRsp, error)
	UpdatePartyGo(context.Context, *UpdatePartyGoReq) (*UpdatePartyGoRsp, error)
	DelPartyGo(context.Context, *DelPartyGoReq) (*DelPartyGoRsp, error)
	GetTabTopicInfo(context.Context, *GetTabTopicInfoReq) (*GetTabTopicInfoRsp, error)
	AddTabTopicInfo(context.Context, *AddTabTopicInfoReq) (*AddTabTopicInfoRsp, error)
	DelTabTopicInfo(context.Context, *DelTabTopicInfoReq) (*DelTabTopicInfoRsp, error)
	SetTabTopicInfo(context.Context, *SetTabTopicInfoReq) (*SetTabTopicInfoRsp, error)
	// 帖子被推荐到声控板块时，向用户发送通知
	SendRcmdNotify(context.Context, *SendRcmdNotifyReq) (*SendRcmdNotifyRsp, error)
	// 获取个人主页游戏入口
	GetGameEntrance(context.Context, *GetGameEntranceReq) (*GetGameEntranceResp, error)
	// 帖子跳转短链配置
	AddPostTeleport(context.Context, *AddPostTeleportReq) (*AddPostTeleportResp, error)
}

func RegisterUgcOpeConfigServer(s *grpc.Server, srv UgcOpeConfigServer) {
	s.RegisterService(&_UgcOpeConfig_serviceDesc, srv)
}

func _UgcOpeConfig_AddPartyGo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPartyGoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcOpeConfigServer).AddPartyGo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.opeconfig.UgcOpeConfig/AddPartyGo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcOpeConfigServer).AddPartyGo(ctx, req.(*AddPartyGoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcOpeConfig_AddPartyGoOrigin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPartyGoOriginReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcOpeConfigServer).AddPartyGoOrigin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.opeconfig.UgcOpeConfig/AddPartyGoOrigin",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcOpeConfigServer).AddPartyGoOrigin(ctx, req.(*AddPartyGoOriginReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcOpeConfig_GetPartyGos_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPartyGosReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcOpeConfigServer).GetPartyGos(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.opeconfig.UgcOpeConfig/GetPartyGos",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcOpeConfigServer).GetPartyGos(ctx, req.(*GetPartyGosReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcOpeConfig_GetPartyGosOffset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPartyGosOffsetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcOpeConfigServer).GetPartyGosOffset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.opeconfig.UgcOpeConfig/GetPartyGosOffset",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcOpeConfigServer).GetPartyGosOffset(ctx, req.(*GetPartyGosOffsetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcOpeConfig_UpdatePartyGo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePartyGoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcOpeConfigServer).UpdatePartyGo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.opeconfig.UgcOpeConfig/UpdatePartyGo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcOpeConfigServer).UpdatePartyGo(ctx, req.(*UpdatePartyGoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcOpeConfig_DelPartyGo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelPartyGoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcOpeConfigServer).DelPartyGo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.opeconfig.UgcOpeConfig/DelPartyGo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcOpeConfigServer).DelPartyGo(ctx, req.(*DelPartyGoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcOpeConfig_GetTabTopicInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTabTopicInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcOpeConfigServer).GetTabTopicInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.opeconfig.UgcOpeConfig/GetTabTopicInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcOpeConfigServer).GetTabTopicInfo(ctx, req.(*GetTabTopicInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcOpeConfig_AddTabTopicInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddTabTopicInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcOpeConfigServer).AddTabTopicInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.opeconfig.UgcOpeConfig/AddTabTopicInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcOpeConfigServer).AddTabTopicInfo(ctx, req.(*AddTabTopicInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcOpeConfig_DelTabTopicInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelTabTopicInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcOpeConfigServer).DelTabTopicInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.opeconfig.UgcOpeConfig/DelTabTopicInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcOpeConfigServer).DelTabTopicInfo(ctx, req.(*DelTabTopicInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcOpeConfig_SetTabTopicInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetTabTopicInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcOpeConfigServer).SetTabTopicInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.opeconfig.UgcOpeConfig/SetTabTopicInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcOpeConfigServer).SetTabTopicInfo(ctx, req.(*SetTabTopicInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcOpeConfig_SendRcmdNotify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendRcmdNotifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcOpeConfigServer).SendRcmdNotify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.opeconfig.UgcOpeConfig/SendRcmdNotify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcOpeConfigServer).SendRcmdNotify(ctx, req.(*SendRcmdNotifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcOpeConfig_GetGameEntrance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameEntranceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcOpeConfigServer).GetGameEntrance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.opeconfig.UgcOpeConfig/GetGameEntrance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcOpeConfigServer).GetGameEntrance(ctx, req.(*GetGameEntranceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcOpeConfig_AddPostTeleport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPostTeleportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcOpeConfigServer).AddPostTeleport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.opeconfig.UgcOpeConfig/AddPostTeleport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcOpeConfigServer).AddPostTeleport(ctx, req.(*AddPostTeleportReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _UgcOpeConfig_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ugc.opeconfig.UgcOpeConfig",
	HandlerType: (*UgcOpeConfigServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddPartyGo",
			Handler:    _UgcOpeConfig_AddPartyGo_Handler,
		},
		{
			MethodName: "AddPartyGoOrigin",
			Handler:    _UgcOpeConfig_AddPartyGoOrigin_Handler,
		},
		{
			MethodName: "GetPartyGos",
			Handler:    _UgcOpeConfig_GetPartyGos_Handler,
		},
		{
			MethodName: "GetPartyGosOffset",
			Handler:    _UgcOpeConfig_GetPartyGosOffset_Handler,
		},
		{
			MethodName: "UpdatePartyGo",
			Handler:    _UgcOpeConfig_UpdatePartyGo_Handler,
		},
		{
			MethodName: "DelPartyGo",
			Handler:    _UgcOpeConfig_DelPartyGo_Handler,
		},
		{
			MethodName: "GetTabTopicInfo",
			Handler:    _UgcOpeConfig_GetTabTopicInfo_Handler,
		},
		{
			MethodName: "AddTabTopicInfo",
			Handler:    _UgcOpeConfig_AddTabTopicInfo_Handler,
		},
		{
			MethodName: "DelTabTopicInfo",
			Handler:    _UgcOpeConfig_DelTabTopicInfo_Handler,
		},
		{
			MethodName: "SetTabTopicInfo",
			Handler:    _UgcOpeConfig_SetTabTopicInfo_Handler,
		},
		{
			MethodName: "SendRcmdNotify",
			Handler:    _UgcOpeConfig_SendRcmdNotify_Handler,
		},
		{
			MethodName: "GetGameEntrance",
			Handler:    _UgcOpeConfig_GetGameEntrance_Handler,
		},
		{
			MethodName: "AddPostTeleport",
			Handler:    _UgcOpeConfig_AddPostTeleport_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ugc/opeconfig.proto",
}

func init() { proto.RegisterFile("ugc/opeconfig.proto", fileDescriptor_opeconfig_d1e993cc7a6d6ca7) }

var fileDescriptor_opeconfig_d1e993cc7a6d6ca7 = []byte{
	// 1548 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x58, 0x59, 0x8f, 0x13, 0xc7,
	0x13, 0x5f, 0xdb, 0xeb, 0xab, 0x7c, 0x60, 0x7a, 0x0f, 0x8c, 0x81, 0x3f, 0xcb, 0xf0, 0x57, 0xb4,
	0x4a, 0xc0, 0x9b, 0x98, 0x00, 0x8a, 0xf2, 0xb4, 0x61, 0x8f, 0x2c, 0x01, 0x7b, 0x35, 0xb6, 0x83,
	0x40, 0x48, 0xa3, 0xf1, 0x4c, 0xdb, 0x74, 0x98, 0x8b, 0xe9, 0x76, 0x60, 0x3f, 0x00, 0x52, 0xf2,
	0x90, 0x97, 0xbc, 0x25, 0xf9, 0x18, 0x79, 0xce, 0x77, 0x8b, 0xba, 0x7b, 0x3c, 0x9e, 0xc3, 0x6b,
	0x2f, 0x90, 0xb7, 0xa9, 0xea, 0xea, 0x3a, 0x7e, 0x55, 0x5d, 0x5d, 0x3d, 0xb0, 0x31, 0x9d, 0x18,
	0x7b, 0xae, 0x87, 0x0d, 0xd7, 0x19, 0x93, 0x49, 0xdb, 0xf3, 0x5d, 0xe6, 0xa2, 0xda, 0x74, 0x62,
	0xb4, 0x43, 0xa6, 0xf2, 0x4b, 0x06, 0x2a, 0xa7, 0xba, 0xcf, 0xce, 0x8e, 0xdd, 0x13, 0x67, 0xec,
	0xa2, 0x6b, 0x50, 0x66, 0xae, 0x47, 0x0c, 0x8d, 0x98, 0xb4, 0x99, 0xd9, 0xc9, 0xed, 0x96, 0xd5,
	0x92, 0x60, 0x9c, 0x98, 0x14, 0x3d, 0x00, 0x18, 0x63, 0x6c, 0x6a, 0xec, 0xcc, 0xc3, 0xb4, 0x99,
	0xdd, 0xc9, 0xed, 0xd6, 0x3b, 0x57, 0xda, 0x31, 0x85, 0xed, 0x23, 0x8c, 0xcd, 0xc1, 0x99, 0x87,
	0xd5, 0xf2, 0x38, 0xf8, 0xa2, 0x08, 0xc1, 0x3a, 0xc3, 0xef, 0x58, 0x33, 0xb7, 0x93, 0xd9, 0x2d,
	0xab, 0xe2, 0x1b, 0x35, 0x20, 0x37, 0xf5, 0xad, 0xe6, 0xba, 0x60, 0xf1, 0x4f, 0xe5, 0xcf, 0x0c,
	0xd4, 0xf6, 0x4d, 0x33, 0xf0, 0x46, 0xc5, 0x6f, 0xd0, 0x55, 0x28, 0x79, 0x2e, 0x65, 0x11, 0x5f,
	0x8a, 0x9c, 0xe6, 0xae, 0xb4, 0x61, 0x9d, 0x38, 0x63, 0xb7, 0x99, 0xdd, 0xc9, 0xec, 0x56, 0x3a,
	0xad, 0x84, 0x13, 0x91, 0x88, 0x54, 0x21, 0x87, 0x3e, 0x83, 0x4b, 0x84, 0x6a, 0x1e, 0xe7, 0x4f,
	0x5c, 0x6d, 0x4a, 0xb1, 0x2f, 0xbc, 0x29, 0xa9, 0x35, 0x42, 0x4f, 0x25, 0x77, 0x48, 0xb1, 0x8f,
	0xb6, 0xa1, 0x30, 0xd1, 0x6d, 0x4c, 0x4c, 0xe1, 0x59, 0x4d, 0x0d, 0x28, 0x65, 0x10, 0xf3, 0x8d,
	0x7a, 0xe8, 0x36, 0xd4, 0x1c, 0x57, 0xf3, 0xb0, 0x6f, 0x13, 0x4a, 0x89, 0xeb, 0x34, 0x33, 0x42,
	0x5d, 0xd5, 0x71, 0x4f, 0x43, 0x1e, 0xfa, 0x1f, 0x54, 0xb0, 0xef, 0x6b, 0x41, 0x10, 0x02, 0xb1,
	0xb2, 0x5a, 0xc6, 0xbe, 0x7f, 0x2a, 0xc2, 0x50, 0xbe, 0x80, 0xfa, 0x31, 0x66, 0x81, 0x56, 0xba,
	0x3c, 0x64, 0xe5, 0x30, 0x2e, 0x4c, 0x3d, 0x74, 0x0f, 0xf2, 0x3c, 0x38, 0x29, 0x59, 0xe9, 0xdc,
	0x48, 0xa0, 0xb0, 0x6f, 0x59, 0x51, 0x20, 0xa4, 0xac, 0xf2, 0x12, 0x36, 0x23, 0x6a, 0x7a, 0xe3,
	0x31, 0xc5, 0x8c, 0x5b, 0xde, 0x86, 0x82, 0x2b, 0x08, 0x11, 0x49, 0x4d, 0x0d, 0x28, 0xb4, 0x09,
	0x79, 0x8b, 0xd8, 0x84, 0x09, 0xa8, 0x6b, 0xaa, 0x24, 0xb8, 0x34, 0xf7, 0x8b, 0x98, 0x41, 0x52,
	0x03, 0x4a, 0xf9, 0x3d, 0x03, 0xf5, 0xb8, 0x5d, 0x74, 0x05, 0x8a, 0x33, 0x00, 0x32, 0x73, 0xd9,
	0x13, 0xf3, 0x83, 0x73, 0x78, 0x13, 0x2a, 0x86, 0x8f, 0x75, 0x86, 0x35, 0x46, 0x6c, 0x2c, 0x0c,
	0xe7, 0x54, 0x90, 0xac, 0x01, 0xb1, 0xf1, 0xb9, 0xc9, 0x7b, 0xb5, 0x28, 0xe4, 0x8f, 0xc4, 0x4f,
	0x9e, 0x10, 0xa6, 0x5b, 0x9a, 0xe1, 0xcc, 0x30, 0x29, 0x09, 0xc6, 0x23, 0x87, 0x29, 0x7f, 0x65,
	0xa0, 0x31, 0xf4, 0x4c, 0x9d, 0xe1, 0x48, 0x19, 0xff, 0x67, 0x00, 0x7c, 0x6a, 0x11, 0x3f, 0x4c,
	0x3a, 0x77, 0xc1, 0x3a, 0x56, 0xde, 0x42, 0xf1, 0x00, 0x5b, 0xcb, 0xb3, 0xf9, 0xb1, 0xcd, 0x21,
	0xd6, 0x71, 0x72, 0xf1, 0x8e, 0xa3, 0x60, 0xa8, 0x1d, 0x60, 0x2b, 0x82, 0xe5, 0x9d, 0x78, 0xca,
	0xb6, 0x13, 0x06, 0x02, 0x2f, 0x67, 0xb9, 0x5a, 0x00, 0x58, 0x76, 0x01, 0x60, 0xca, 0xd7, 0x31,
	0x33, 0x17, 0x45, 0xe5, 0xd7, 0x0c, 0x6c, 0xcc, 0x9b, 0x42, 0xcf, 0x27, 0x13, 0xe2, 0x2c, 0xcd,
	0x77, 0xd0, 0xf3, 0xb2, 0x61, 0xcf, 0x5b, 0xd8, 0x19, 0xcf, 0xc9, 0x5e, 0x1c, 0xa8, 0x7c, 0x02,
	0xa8, 0xad, 0x05, 0xae, 0x50, 0x4f, 0x31, 0xa1, 0x3c, 0x10, 0x22, 0x0c, 0xdb, 0xbc, 0xb7, 0xcc,
	0x14, 0x04, 0x8e, 0x15, 0x83, 0xfd, 0xe8, 0x06, 0x80, 0x5c, 0x72, 0x74, 0x1b, 0x07, 0x0e, 0x4a,
	0x6b, 0x5d, 0xdd, 0xc6, 0xf3, 0x65, 0x9e, 0x5c, 0xe1, 0x6c, 0x2d, 0x58, 0xe6, 0x39, 0x54, 0x7e,
	0xcb, 0x42, 0x75, 0xa0, 0x8f, 0xa4, 0x25, 0x5e, 0x24, 0x5b, 0x50, 0x60, 0xfa, 0x68, 0x6e, 0x27,
	0xcf, 0xf4, 0xd1, 0x89, 0x29, 0x1c, 0xd0, 0x47, 0x51, 0x1b, 0x45, 0xa6, 0x8f, 0x84, 0x85, 0x6f,
	0xa0, 0x12, 0xf8, 0xc6, 0xb0, 0x2d, 0xeb, 0xa0, 0xd2, 0x69, 0x26, 0xb2, 0x1b, 0x86, 0xa2, 0x4a,
	0x77, 0xf8, 0x27, 0x4d, 0xb6, 0x85, 0xf5, 0x54, 0x5b, 0xb8, 0x09, 0x95, 0xa9, 0x28, 0x7b, 0x29,
	0x90, 0x97, 0x02, 0x92, 0x25, 0x04, 0x36, 0x79, 0x51, 0x99, 0xf8, 0x5d, 0xb3, 0x20, 0x5b, 0x9c,
	0x20, 0x78, 0xd0, 0xb6, 0xee, 0xbf, 0xc6, 0xb2, 0x19, 0x17, 0x77, 0x72, 0x3c, 0x68, 0xc9, 0xe1,
	0x37, 0x50, 0x10, 0x8c, 0x40, 0xa4, 0x24, 0xf6, 0xf1, 0x60, 0x04, 0x1e, 0xdf, 0x03, 0x3a, 0xc6,
	0x2c, 0x8a, 0x08, 0x2f, 0x8b, 0x2d, 0x28, 0x10, 0xaa, 0xbd, 0xc5, 0xa3, 0xa0, 0x98, 0xf2, 0x84,
	0x3e, 0xc3, 0x23, 0x9e, 0xd6, 0xd0, 0xcc, 0xac, 0x9f, 0xcc, 0xac, 0x28, 0xc7, 0x69, 0x4d, 0xd4,
	0x43, 0x5f, 0xc5, 0x0f, 0xc1, 0xb5, 0x24, 0x4c, 0x51, 0xf1, 0xa0, 0xeb, 0xff, 0x9d, 0x01, 0xb4,
	0x6f, 0x9a, 0x0b, 0x7c, 0xfa, 0xc0, 0x44, 0x2d, 0x3b, 0xae, 0x73, 0x20, 0xd7, 0xcf, 0x07, 0x32,
	0xbf, 0x0c, 0xc8, 0x42, 0x1c, 0xc8, 0xcd, 0xb4, 0xd3, 0xd4, 0x53, 0x8e, 0x00, 0x1d, 0x60, 0xeb,
	0x93, 0x43, 0xe1, 0xda, 0x93, 0x7a, 0xa8, 0xa7, 0x7c, 0x3b, 0xaf, 0xe5, 0xbe, 0xeb, 0xb3, 0xf3,
	0xf4, 0x86, 0xa1, 0x66, 0x23, 0xa1, 0x2a, 0x4f, 0x00, 0xf5, 0xd3, 0x99, 0x7f, 0x20, 0x7d, 0xa0,
	0xae, 0xcf, 0x56, 0xa4, 0x8c, 0x5b, 0x14, 0x0e, 0xf2, 0x0f, 0xee, 0x60, 0x3f, 0x95, 0x7d, 0xa5,
	0x0d, 0x97, 0xfb, 0xd8, 0x31, 0x55, 0xc3, 0x36, 0xbb, 0x2e, 0x23, 0xe3, 0xb3, 0x15, 0x73, 0xc3,
	0x46, 0x4a, 0x9e, 0x7a, 0x8a, 0x27, 0x0a, 0xeb, 0x58, 0xb7, 0xf1, 0xa1, 0xc3, 0x7c, 0xdd, 0x31,
	0x30, 0xd7, 0x12, 0xab, 0xc5, 0x4c, 0xbc, 0x16, 0x79, 0x5b, 0xf3, 0xf1, 0x1b, 0x6d, 0x1a, 0x96,
	0x69, 0xc1, 0xc7, 0x6f, 0x86, 0xc4, 0x44, 0xff, 0x87, 0xba, 0xa7, 0x4f, 0xb0, 0xe6, 0xbe, 0x75,
	0xb0, 0x2f, 0xd6, 0x65, 0x87, 0xa8, 0x72, 0x6e, 0x8f, 0x33, 0x87, 0xc4, 0x54, 0xfe, 0xc8, 0xc3,
	0x46, 0xca, 0x24, 0xf5, 0xd0, 0x00, 0x6a, 0xbc, 0xc1, 0x69, 0x38, 0x60, 0x0a, 0xbb, 0x95, 0xce,
	0x5e, 0x02, 0xa1, 0x05, 0x5b, 0xdb, 0x31, 0x46, 0x75, 0x12, 0xa1, 0x5a, 0xff, 0xe4, 0xa0, 0x1a,
	0x5d, 0xe6, 0xde, 0x0b, 0x33, 0x61, 0x60, 0xa2, 0xad, 0x9e, 0x88, 0xb6, 0x2a, 0x16, 0x22, 0x15,
	0x52, 0xe2, 0x0c, 0x51, 0xed, 0x3b, 0x50, 0x25, 0x86, 0xeb, 0x68, 0xc4, 0x9e, 0x68, 0xbc, 0x75,
	0xcb, 0x3e, 0x0d, 0x9c, 0x77, 0x62, 0x4f, 0x86, 0xbe, 0xc5, 0x25, 0x5e, 0x11, 0x87, 0x85, 0x12,
	0x72, 0xa0, 0x05, 0xce, 0x0b, 0x24, 0xae, 0x42, 0xe9, 0xa7, 0xa9, 0xed, 0x89, 0xd5, 0xbc, 0xac,
	0x40, 0x4e, 0xf3, 0xa5, 0x11, 0x6c, 0x47, 0x90, 0xf3, 0x2c, 0xfd, 0x4c, 0xa3, 0x4c, 0x67, 0x53,
	0x2a, 0x0e, 0x42, 0xbd, 0x73, 0xf7, 0x02, 0x20, 0x9c, 0x5a, 0xfa, 0x59, 0x5f, 0x6c, 0x52, 0x37,
	0x42, 0xc0, 0xe7, 0x4c, 0xf4, 0x0a, 0x9a, 0x11, 0x1b, 0xae, 0x63, 0x11, 0x07, 0xcf, 0xac, 0x14,
	0x85, 0x95, 0x8b, 0x40, 0xdd, 0x13, 0xfb, 0x02, 0x3b, 0x5b, 0xa1, 0x9d, 0x28, 0x1b, 0xdd, 0x01,
	0x34, 0xd2, 0x8d, 0xd7, 0x13, 0xdf, 0x9d, 0x3a, 0x66, 0x08, 0x48, 0x49, 0x84, 0xdc, 0x98, 0xaf,
	0x04, 0xb0, 0x5c, 0x07, 0x20, 0x76, 0x28, 0x55, 0x96, 0xc0, 0x13, 0x5b, 0xae, 0x2a, 0xf7, 0x01,
	0x22, 0x31, 0x54, 0xa0, 0x38, 0xec, 0xfe, 0xd0, 0xed, 0x3d, 0xeb, 0x36, 0xd6, 0x10, 0x40, 0xe1,
	0xf4, 0xc9, 0xfe, 0xf3, 0xc3, 0x83, 0x46, 0x06, 0xd5, 0x01, 0xba, 0xbd, 0x81, 0x16, 0xd0, 0x59,
	0xe5, 0x3e, 0x54, 0x63, 0x2e, 0x55, 0xa1, 0x34, 0xec, 0x1e, 0x1c, 0x1e, 0x9d, 0x74, 0x0f, 0xe5,
	0xce, 0x5e, 0xf7, 0x09, 0xff, 0xce, 0x70, 0x95, 0xbd, 0xa3, 0x23, 0x41, 0x64, 0x95, 0xf7, 0x19,
	0xa8, 0xf2, 0x91, 0x7c, 0x80, 0x2d, 0xec, 0xf1, 0x43, 0x7f, 0xee, 0x15, 0x3e, 0xbb, 0xb0, 0xb3,
	0xe9, 0xa7, 0x4c, 0x6e, 0x7e, 0xad, 0xc7, 0x67, 0xa1, 0xf5, 0x8b, 0xce, 0x42, 0xca, 0x53, 0xd1,
	0xef, 0xa2, 0x9e, 0xf0, 0x53, 0xf9, 0x10, 0x4a, 0x2c, 0x20, 0x83, 0xc3, 0x91, 0x6c, 0x1f, 0xb1,
	0x1d, 0xa1, 0xf0, 0x6c, 0x28, 0x88, 0xa9, 0xa3, 0xde, 0xe7, 0x3d, 0x28, 0xcd, 0x8c, 0x23, 0x04,
	0x75, 0x15, 0x1b, 0xae, 0x6d, 0x3b, 0xd8, 0x11, 0x9c, 0xc6, 0x1a, 0xaa, 0x05, 0x43, 0x83, 0x20,
	0x25, 0xc6, 0xae, 0x6f, 0xeb, 0x96, 0xa0, 0xb3, 0xa8, 0x21, 0x4f, 0xd6, 0x01, 0xa1, 0xcc, 0x27,
	0x06, 0x6b, 0xe4, 0x3a, 0xef, 0xcb, 0x50, 0x1d, 0x4e, 0x8c, 0x9e, 0x87, 0x1f, 0x09, 0x7f, 0xd0,
	0x63, 0x80, 0xf9, 0x34, 0x82, 0xae, 0x27, 0xe7, 0xea, 0xe8, 0x23, 0xaf, 0xb5, 0x64, 0x95, 0x7a,
	0xca, 0x1a, 0x7a, 0x09, 0x8d, 0xe4, 0x64, 0x83, 0x94, 0x73, 0xf7, 0x84, 0x53, 0x58, 0x6b, 0xa5,
	0x8c, 0xd0, 0xfe, 0x14, 0x2a, 0x91, 0xa7, 0x01, 0xba, 0x91, 0x3e, 0x0a, 0x91, 0xd7, 0x59, 0x6b,
	0xd9, 0xb2, 0x50, 0xa7, 0xc1, 0xe5, 0xd4, 0x4b, 0x03, 0xdd, 0x3e, 0x7f, 0x57, 0xf8, 0xfc, 0x6a,
	0xad, 0x16, 0x12, 0x06, 0xfa, 0x50, 0x8b, 0x8d, 0xf0, 0xe8, 0x66, 0x62, 0x5f, 0xf2, 0xf5, 0xd1,
	0x5a, 0x2e, 0x20, 0x94, 0x3e, 0x06, 0x98, 0x8f, 0xbf, 0xa9, 0x74, 0xc5, 0x06, 0xf0, 0xd6, 0x92,
	0x55, 0xa1, 0xeb, 0x39, 0x5c, 0x4a, 0x4c, 0x2c, 0xe8, 0x56, 0x3a, 0xb4, 0xc4, 0x0d, 0xd9, 0x5a,
	0x25, 0x32, 0x53, 0x9d, 0x98, 0x06, 0x52, 0xaa, 0xd3, 0x23, 0x4e, 0x6b, 0x95, 0xc8, 0x4c, 0x75,
	0x62, 0x14, 0x48, 0xa9, 0x4e, 0x8f, 0x1c, 0xad, 0x55, 0x22, 0x33, 0xd5, 0xfd, 0x15, 0x80, 0xf4,
	0x57, 0x03, 0xd2, 0x5f, 0x04, 0xc8, 0x8f, 0x50, 0x8f, 0xdf, 0xec, 0x68, 0x27, 0xb5, 0x2d, 0x31,
	0x28, 0xb4, 0x56, 0x48, 0x08, 0xbd, 0x2f, 0x44, 0x0e, 0x63, 0xd7, 0xe7, 0xad, 0x55, 0x77, 0x44,
	0xfa, 0xc0, 0x2d, 0xb8, 0x46, 0xa4, 0xee, 0x44, 0x4f, 0x5a, 0x94, 0xc4, 0x44, 0x0b, 0x5c, 0x78,
	0x98, 0x13, 0x6d, 0x4d, 0x59, 0xfb, 0xae, 0xf3, 0xe2, 0xcb, 0x89, 0x6b, 0xe9, 0xce, 0xa4, 0x7d,
	0xbf, 0xc3, 0x58, 0xdb, 0x70, 0xed, 0x3d, 0xf1, 0xd3, 0xcb, 0x70, 0xad, 0x3d, 0x8a, 0xfd, 0x9f,
	0x89, 0x81, 0xe9, 0x5e, 0xf0, 0x53, 0xec, 0xae, 0xd4, 0x34, 0x2a, 0x08, 0x89, 0x7b, 0xff, 0x06,
	0x00, 0x00, 0xff, 0xff, 0x32, 0x55, 0x36, 0x95, 0x2d, 0x13, 0x00, 0x00,
}
