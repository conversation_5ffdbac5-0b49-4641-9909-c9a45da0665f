// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ugc/mood.proto

package mood // import "golang.52tt.com/protocol/services/ugc/mood"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type MoodType int32

const (
	MoodType_AllMoodType MoodType = 0
	MoodType_Positive    MoodType = 1
	MoodType_Negative    MoodType = 2
)

var MoodType_name = map[int32]string{
	0: "AllMoodType",
	1: "Positive",
	2: "Negative",
}
var MoodType_value = map[string]int32{
	"AllMoodType": 0,
	"Positive":    1,
	"Negative":    2,
}

func (x MoodType) String() string {
	return proto.EnumName(MoodType_name, int32(x))
}
func (MoodType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mood_e951fb0984221d46, []int{0}
}

type SortNDays int32

const (
	SortNDays_DefaultSort SortNDays = 0
	SortNDays_OneDay      SortNDays = 1
	SortNDays_ThreeDays   SortNDays = 3
	SortNDays_SevenDays   SortNDays = 7
)

var SortNDays_name = map[int32]string{
	0: "DefaultSort",
	1: "OneDay",
	3: "ThreeDays",
	7: "SevenDays",
}
var SortNDays_value = map[string]int32{
	"DefaultSort": 0,
	"OneDay":      1,
	"ThreeDays":   3,
	"SevenDays":   7,
}

func (x SortNDays) String() string {
	return proto.EnumName(SortNDays_name, int32(x))
}
func (SortNDays) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mood_e951fb0984221d46, []int{1}
}

type MoodBaseInfo struct {
	MoodId   string   `protobuf:"bytes,1,opt,name=mood_id,json=moodId,proto3" json:"mood_id,omitempty"`
	Name     string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	SmallUrl string   `protobuf:"bytes,3,opt,name=small_url,json=smallUrl,proto3" json:"small_url,omitempty"`
	BgUrl    string   `protobuf:"bytes,4,opt,name=bg_url,json=bgUrl,proto3" json:"bg_url,omitempty"`
	TextPost []string `protobuf:"bytes,5,rep,name=text_post,json=textPost,proto3" json:"text_post,omitempty"`
	MoodType MoodType `protobuf:"varint,6,opt,name=mood_type,json=moodType,proto3,enum=ugc.mood.MoodType" json:"mood_type,omitempty"`
	// Types that are valid to be assigned to MoodT:
	//	*MoodBaseInfo_PositiveConf
	//	*MoodBaseInfo_NegativeConf
	MoodT                isMoodBaseInfo_MoodT `protobuf_oneof:"MoodT"`
	Index                uint32               `protobuf:"varint,9,opt,name=index,proto3" json:"index,omitempty"`
	BindTopicId          string               `protobuf:"bytes,10,opt,name=bind_topic_id,json=bindTopicId,proto3" json:"bind_topic_id,omitempty"`
	CreateTs             uint32               `protobuf:"varint,11,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *MoodBaseInfo) Reset()         { *m = MoodBaseInfo{} }
func (m *MoodBaseInfo) String() string { return proto.CompactTextString(m) }
func (*MoodBaseInfo) ProtoMessage()    {}
func (*MoodBaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_mood_e951fb0984221d46, []int{0}
}
func (m *MoodBaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MoodBaseInfo.Unmarshal(m, b)
}
func (m *MoodBaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MoodBaseInfo.Marshal(b, m, deterministic)
}
func (dst *MoodBaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MoodBaseInfo.Merge(dst, src)
}
func (m *MoodBaseInfo) XXX_Size() int {
	return xxx_messageInfo_MoodBaseInfo.Size(m)
}
func (m *MoodBaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MoodBaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MoodBaseInfo proto.InternalMessageInfo

func (m *MoodBaseInfo) GetMoodId() string {
	if m != nil {
		return m.MoodId
	}
	return ""
}

func (m *MoodBaseInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MoodBaseInfo) GetSmallUrl() string {
	if m != nil {
		return m.SmallUrl
	}
	return ""
}

func (m *MoodBaseInfo) GetBgUrl() string {
	if m != nil {
		return m.BgUrl
	}
	return ""
}

func (m *MoodBaseInfo) GetTextPost() []string {
	if m != nil {
		return m.TextPost
	}
	return nil
}

func (m *MoodBaseInfo) GetMoodType() MoodType {
	if m != nil {
		return m.MoodType
	}
	return MoodType_AllMoodType
}

type isMoodBaseInfo_MoodT interface {
	isMoodBaseInfo_MoodT()
}

type MoodBaseInfo_PositiveConf struct {
	PositiveConf *MoodBaseInfo_PositiveMoodConf `protobuf:"bytes,7,opt,name=positive_conf,json=positiveConf,proto3,oneof"`
}

type MoodBaseInfo_NegativeConf struct {
	NegativeConf *MoodBaseInfo_NegativeMoodConf `protobuf:"bytes,8,opt,name=negative_conf,json=negativeConf,proto3,oneof"`
}

func (*MoodBaseInfo_PositiveConf) isMoodBaseInfo_MoodT() {}

func (*MoodBaseInfo_NegativeConf) isMoodBaseInfo_MoodT() {}

func (m *MoodBaseInfo) GetMoodT() isMoodBaseInfo_MoodT {
	if m != nil {
		return m.MoodT
	}
	return nil
}

func (m *MoodBaseInfo) GetPositiveConf() *MoodBaseInfo_PositiveMoodConf {
	if x, ok := m.GetMoodT().(*MoodBaseInfo_PositiveConf); ok {
		return x.PositiveConf
	}
	return nil
}

func (m *MoodBaseInfo) GetNegativeConf() *MoodBaseInfo_NegativeMoodConf {
	if x, ok := m.GetMoodT().(*MoodBaseInfo_NegativeConf); ok {
		return x.NegativeConf
	}
	return nil
}

func (m *MoodBaseInfo) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *MoodBaseInfo) GetBindTopicId() string {
	if m != nil {
		return m.BindTopicId
	}
	return ""
}

func (m *MoodBaseInfo) GetCreateTs() uint32 {
	if m != nil {
		return m.CreateTs
	}
	return 0
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*MoodBaseInfo) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _MoodBaseInfo_OneofMarshaler, _MoodBaseInfo_OneofUnmarshaler, _MoodBaseInfo_OneofSizer, []interface{}{
		(*MoodBaseInfo_PositiveConf)(nil),
		(*MoodBaseInfo_NegativeConf)(nil),
	}
}

func _MoodBaseInfo_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*MoodBaseInfo)
	// MoodT
	switch x := m.MoodT.(type) {
	case *MoodBaseInfo_PositiveConf:
		b.EncodeVarint(7<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.PositiveConf); err != nil {
			return err
		}
	case *MoodBaseInfo_NegativeConf:
		b.EncodeVarint(8<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.NegativeConf); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("MoodBaseInfo.MoodT has unexpected type %T", x)
	}
	return nil
}

func _MoodBaseInfo_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*MoodBaseInfo)
	switch tag {
	case 7: // MoodT.positive_conf
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(MoodBaseInfo_PositiveMoodConf)
		err := b.DecodeMessage(msg)
		m.MoodT = &MoodBaseInfo_PositiveConf{msg}
		return true, err
	case 8: // MoodT.negative_conf
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(MoodBaseInfo_NegativeMoodConf)
		err := b.DecodeMessage(msg)
		m.MoodT = &MoodBaseInfo_NegativeConf{msg}
		return true, err
	default:
		return false, nil
	}
}

func _MoodBaseInfo_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*MoodBaseInfo)
	// MoodT
	switch x := m.MoodT.(type) {
	case *MoodBaseInfo_PositiveConf:
		s := proto.Size(x.PositiveConf)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *MoodBaseInfo_NegativeConf:
		s := proto.Size(x.NegativeConf)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type MoodBaseInfo_PositiveMoodConf struct {
	Introduction         string   `protobuf:"bytes,1,opt,name=introduction,proto3" json:"introduction,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MoodBaseInfo_PositiveMoodConf) Reset()         { *m = MoodBaseInfo_PositiveMoodConf{} }
func (m *MoodBaseInfo_PositiveMoodConf) String() string { return proto.CompactTextString(m) }
func (*MoodBaseInfo_PositiveMoodConf) ProtoMessage()    {}
func (*MoodBaseInfo_PositiveMoodConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_mood_e951fb0984221d46, []int{0, 0}
}
func (m *MoodBaseInfo_PositiveMoodConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MoodBaseInfo_PositiveMoodConf.Unmarshal(m, b)
}
func (m *MoodBaseInfo_PositiveMoodConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MoodBaseInfo_PositiveMoodConf.Marshal(b, m, deterministic)
}
func (dst *MoodBaseInfo_PositiveMoodConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MoodBaseInfo_PositiveMoodConf.Merge(dst, src)
}
func (m *MoodBaseInfo_PositiveMoodConf) XXX_Size() int {
	return xxx_messageInfo_MoodBaseInfo_PositiveMoodConf.Size(m)
}
func (m *MoodBaseInfo_PositiveMoodConf) XXX_DiscardUnknown() {
	xxx_messageInfo_MoodBaseInfo_PositiveMoodConf.DiscardUnknown(m)
}

var xxx_messageInfo_MoodBaseInfo_PositiveMoodConf proto.InternalMessageInfo

func (m *MoodBaseInfo_PositiveMoodConf) GetIntroduction() string {
	if m != nil {
		return m.Introduction
	}
	return ""
}

type MoodBaseInfo_NegativeMoodConf struct {
	TextMe               []string `protobuf:"bytes,1,rep,name=text_me,json=textMe,proto3" json:"text_me,omitempty"`
	TextOthers           string   `protobuf:"bytes,2,opt,name=text_others,json=textOthers,proto3" json:"text_others,omitempty"`
	TextComfort          []string `protobuf:"bytes,3,rep,name=text_comfort,json=textComfort,proto3" json:"text_comfort,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MoodBaseInfo_NegativeMoodConf) Reset()         { *m = MoodBaseInfo_NegativeMoodConf{} }
func (m *MoodBaseInfo_NegativeMoodConf) String() string { return proto.CompactTextString(m) }
func (*MoodBaseInfo_NegativeMoodConf) ProtoMessage()    {}
func (*MoodBaseInfo_NegativeMoodConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_mood_e951fb0984221d46, []int{0, 1}
}
func (m *MoodBaseInfo_NegativeMoodConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MoodBaseInfo_NegativeMoodConf.Unmarshal(m, b)
}
func (m *MoodBaseInfo_NegativeMoodConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MoodBaseInfo_NegativeMoodConf.Marshal(b, m, deterministic)
}
func (dst *MoodBaseInfo_NegativeMoodConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MoodBaseInfo_NegativeMoodConf.Merge(dst, src)
}
func (m *MoodBaseInfo_NegativeMoodConf) XXX_Size() int {
	return xxx_messageInfo_MoodBaseInfo_NegativeMoodConf.Size(m)
}
func (m *MoodBaseInfo_NegativeMoodConf) XXX_DiscardUnknown() {
	xxx_messageInfo_MoodBaseInfo_NegativeMoodConf.DiscardUnknown(m)
}

var xxx_messageInfo_MoodBaseInfo_NegativeMoodConf proto.InternalMessageInfo

func (m *MoodBaseInfo_NegativeMoodConf) GetTextMe() []string {
	if m != nil {
		return m.TextMe
	}
	return nil
}

func (m *MoodBaseInfo_NegativeMoodConf) GetTextOthers() string {
	if m != nil {
		return m.TextOthers
	}
	return ""
}

func (m *MoodBaseInfo_NegativeMoodConf) GetTextComfort() []string {
	if m != nil {
		return m.TextComfort
	}
	return nil
}

type MoodInfo struct {
	BaseInfo             *MoodBaseInfo `protobuf:"bytes,1,opt,name=base_info,json=baseInfo,proto3" json:"base_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *MoodInfo) Reset()         { *m = MoodInfo{} }
func (m *MoodInfo) String() string { return proto.CompactTextString(m) }
func (*MoodInfo) ProtoMessage()    {}
func (*MoodInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_mood_e951fb0984221d46, []int{1}
}
func (m *MoodInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MoodInfo.Unmarshal(m, b)
}
func (m *MoodInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MoodInfo.Marshal(b, m, deterministic)
}
func (dst *MoodInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MoodInfo.Merge(dst, src)
}
func (m *MoodInfo) XXX_Size() int {
	return xxx_messageInfo_MoodInfo.Size(m)
}
func (m *MoodInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MoodInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MoodInfo proto.InternalMessageInfo

func (m *MoodInfo) GetBaseInfo() *MoodBaseInfo {
	if m != nil {
		return m.BaseInfo
	}
	return nil
}

type UpdateMoodInfoReq struct {
	BaseInfo             *MoodBaseInfo `protobuf:"bytes,1,opt,name=base_info,json=baseInfo,proto3" json:"base_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UpdateMoodInfoReq) Reset()         { *m = UpdateMoodInfoReq{} }
func (m *UpdateMoodInfoReq) String() string { return proto.CompactTextString(m) }
func (*UpdateMoodInfoReq) ProtoMessage()    {}
func (*UpdateMoodInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mood_e951fb0984221d46, []int{2}
}
func (m *UpdateMoodInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateMoodInfoReq.Unmarshal(m, b)
}
func (m *UpdateMoodInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateMoodInfoReq.Marshal(b, m, deterministic)
}
func (dst *UpdateMoodInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateMoodInfoReq.Merge(dst, src)
}
func (m *UpdateMoodInfoReq) XXX_Size() int {
	return xxx_messageInfo_UpdateMoodInfoReq.Size(m)
}
func (m *UpdateMoodInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateMoodInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateMoodInfoReq proto.InternalMessageInfo

func (m *UpdateMoodInfoReq) GetBaseInfo() *MoodBaseInfo {
	if m != nil {
		return m.BaseInfo
	}
	return nil
}

type UpdateMoodInfoResp struct {
	MoodId               string   `protobuf:"bytes,1,opt,name=mood_id,json=moodId,proto3" json:"mood_id,omitempty"`
	TopicId              string   `protobuf:"bytes,2,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateMoodInfoResp) Reset()         { *m = UpdateMoodInfoResp{} }
func (m *UpdateMoodInfoResp) String() string { return proto.CompactTextString(m) }
func (*UpdateMoodInfoResp) ProtoMessage()    {}
func (*UpdateMoodInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mood_e951fb0984221d46, []int{3}
}
func (m *UpdateMoodInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateMoodInfoResp.Unmarshal(m, b)
}
func (m *UpdateMoodInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateMoodInfoResp.Marshal(b, m, deterministic)
}
func (dst *UpdateMoodInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateMoodInfoResp.Merge(dst, src)
}
func (m *UpdateMoodInfoResp) XXX_Size() int {
	return xxx_messageInfo_UpdateMoodInfoResp.Size(m)
}
func (m *UpdateMoodInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateMoodInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateMoodInfoResp proto.InternalMessageInfo

func (m *UpdateMoodInfoResp) GetMoodId() string {
	if m != nil {
		return m.MoodId
	}
	return ""
}

func (m *UpdateMoodInfoResp) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

type GetMoodsReq struct {
	MoodIds              []string `protobuf:"bytes,1,rep,name=mood_ids,json=moodIds,proto3" json:"mood_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMoodsReq) Reset()         { *m = GetMoodsReq{} }
func (m *GetMoodsReq) String() string { return proto.CompactTextString(m) }
func (*GetMoodsReq) ProtoMessage()    {}
func (*GetMoodsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mood_e951fb0984221d46, []int{4}
}
func (m *GetMoodsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMoodsReq.Unmarshal(m, b)
}
func (m *GetMoodsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMoodsReq.Marshal(b, m, deterministic)
}
func (dst *GetMoodsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMoodsReq.Merge(dst, src)
}
func (m *GetMoodsReq) XXX_Size() int {
	return xxx_messageInfo_GetMoodsReq.Size(m)
}
func (m *GetMoodsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMoodsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMoodsReq proto.InternalMessageInfo

func (m *GetMoodsReq) GetMoodIds() []string {
	if m != nil {
		return m.MoodIds
	}
	return nil
}

type GetMoodsResp struct {
	Moods                []*MoodInfo `protobuf:"bytes,1,rep,name=moods,proto3" json:"moods,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetMoodsResp) Reset()         { *m = GetMoodsResp{} }
func (m *GetMoodsResp) String() string { return proto.CompactTextString(m) }
func (*GetMoodsResp) ProtoMessage()    {}
func (*GetMoodsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mood_e951fb0984221d46, []int{5}
}
func (m *GetMoodsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMoodsResp.Unmarshal(m, b)
}
func (m *GetMoodsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMoodsResp.Marshal(b, m, deterministic)
}
func (dst *GetMoodsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMoodsResp.Merge(dst, src)
}
func (m *GetMoodsResp) XXX_Size() int {
	return xxx_messageInfo_GetMoodsResp.Size(m)
}
func (m *GetMoodsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMoodsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMoodsResp proto.InternalMessageInfo

func (m *GetMoodsResp) GetMoods() []*MoodInfo {
	if m != nil {
		return m.Moods
	}
	return nil
}

type GetMoodListReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	MoodType             uint32   `protobuf:"varint,4,opt,name=mood_type,json=moodType,proto3" json:"mood_type,omitempty"`
	Ndays                uint32   `protobuf:"varint,10,opt,name=ndays,proto3" json:"ndays,omitempty"`
	Name                 string   `protobuf:"bytes,11,opt,name=name,proto3" json:"name,omitempty"`
	MoodId               string   `protobuf:"bytes,12,opt,name=mood_id,json=moodId,proto3" json:"mood_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMoodListReq) Reset()         { *m = GetMoodListReq{} }
func (m *GetMoodListReq) String() string { return proto.CompactTextString(m) }
func (*GetMoodListReq) ProtoMessage()    {}
func (*GetMoodListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mood_e951fb0984221d46, []int{6}
}
func (m *GetMoodListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMoodListReq.Unmarshal(m, b)
}
func (m *GetMoodListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMoodListReq.Marshal(b, m, deterministic)
}
func (dst *GetMoodListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMoodListReq.Merge(dst, src)
}
func (m *GetMoodListReq) XXX_Size() int {
	return xxx_messageInfo_GetMoodListReq.Size(m)
}
func (m *GetMoodListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMoodListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMoodListReq proto.InternalMessageInfo

func (m *GetMoodListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetMoodListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetMoodListReq) GetMoodType() uint32 {
	if m != nil {
		return m.MoodType
	}
	return 0
}

func (m *GetMoodListReq) GetNdays() uint32 {
	if m != nil {
		return m.Ndays
	}
	return 0
}

func (m *GetMoodListReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetMoodListReq) GetMoodId() string {
	if m != nil {
		return m.MoodId
	}
	return ""
}

type GetMoodListResp struct {
	Moods                []*MoodInfo `protobuf:"bytes,1,rep,name=moods,proto3" json:"moods,omitempty"`
	Total                uint32      `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetMoodListResp) Reset()         { *m = GetMoodListResp{} }
func (m *GetMoodListResp) String() string { return proto.CompactTextString(m) }
func (*GetMoodListResp) ProtoMessage()    {}
func (*GetMoodListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mood_e951fb0984221d46, []int{7}
}
func (m *GetMoodListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMoodListResp.Unmarshal(m, b)
}
func (m *GetMoodListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMoodListResp.Marshal(b, m, deterministic)
}
func (dst *GetMoodListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMoodListResp.Merge(dst, src)
}
func (m *GetMoodListResp) XXX_Size() int {
	return xxx_messageInfo_GetMoodListResp.Size(m)
}
func (m *GetMoodListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMoodListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMoodListResp proto.InternalMessageInfo

func (m *GetMoodListResp) GetMoods() []*MoodInfo {
	if m != nil {
		return m.Moods
	}
	return nil
}

func (m *GetMoodListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 心情展示排序
type MoodSort struct {
	MoodId               string   `protobuf:"bytes,1,opt,name=mood_id,json=moodId,proto3" json:"mood_id,omitempty"`
	Index                uint32   `protobuf:"varint,2,opt,name=index,proto3" json:"index,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MoodSort) Reset()         { *m = MoodSort{} }
func (m *MoodSort) String() string { return proto.CompactTextString(m) }
func (*MoodSort) ProtoMessage()    {}
func (*MoodSort) Descriptor() ([]byte, []int) {
	return fileDescriptor_mood_e951fb0984221d46, []int{8}
}
func (m *MoodSort) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MoodSort.Unmarshal(m, b)
}
func (m *MoodSort) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MoodSort.Marshal(b, m, deterministic)
}
func (dst *MoodSort) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MoodSort.Merge(dst, src)
}
func (m *MoodSort) XXX_Size() int {
	return xxx_messageInfo_MoodSort.Size(m)
}
func (m *MoodSort) XXX_DiscardUnknown() {
	xxx_messageInfo_MoodSort.DiscardUnknown(m)
}

var xxx_messageInfo_MoodSort proto.InternalMessageInfo

func (m *MoodSort) GetMoodId() string {
	if m != nil {
		return m.MoodId
	}
	return ""
}

func (m *MoodSort) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

type SetMoodSortReq struct {
	Moods                []*MoodSort `protobuf:"bytes,1,rep,name=moods,proto3" json:"moods,omitempty"`
	NoUniqueCheck        bool        `protobuf:"varint,2,opt,name=no_unique_check,json=noUniqueCheck,proto3" json:"no_unique_check,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *SetMoodSortReq) Reset()         { *m = SetMoodSortReq{} }
func (m *SetMoodSortReq) String() string { return proto.CompactTextString(m) }
func (*SetMoodSortReq) ProtoMessage()    {}
func (*SetMoodSortReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mood_e951fb0984221d46, []int{9}
}
func (m *SetMoodSortReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMoodSortReq.Unmarshal(m, b)
}
func (m *SetMoodSortReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMoodSortReq.Marshal(b, m, deterministic)
}
func (dst *SetMoodSortReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMoodSortReq.Merge(dst, src)
}
func (m *SetMoodSortReq) XXX_Size() int {
	return xxx_messageInfo_SetMoodSortReq.Size(m)
}
func (m *SetMoodSortReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMoodSortReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetMoodSortReq proto.InternalMessageInfo

func (m *SetMoodSortReq) GetMoods() []*MoodSort {
	if m != nil {
		return m.Moods
	}
	return nil
}

func (m *SetMoodSortReq) GetNoUniqueCheck() bool {
	if m != nil {
		return m.NoUniqueCheck
	}
	return false
}

type SetMoodSortResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetMoodSortResp) Reset()         { *m = SetMoodSortResp{} }
func (m *SetMoodSortResp) String() string { return proto.CompactTextString(m) }
func (*SetMoodSortResp) ProtoMessage()    {}
func (*SetMoodSortResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mood_e951fb0984221d46, []int{10}
}
func (m *SetMoodSortResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMoodSortResp.Unmarshal(m, b)
}
func (m *SetMoodSortResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMoodSortResp.Marshal(b, m, deterministic)
}
func (dst *SetMoodSortResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMoodSortResp.Merge(dst, src)
}
func (m *SetMoodSortResp) XXX_Size() int {
	return xxx_messageInfo_SetMoodSortResp.Size(m)
}
func (m *SetMoodSortResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMoodSortResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetMoodSortResp proto.InternalMessageInfo

type GetMoodSortReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMoodSortReq) Reset()         { *m = GetMoodSortReq{} }
func (m *GetMoodSortReq) String() string { return proto.CompactTextString(m) }
func (*GetMoodSortReq) ProtoMessage()    {}
func (*GetMoodSortReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mood_e951fb0984221d46, []int{11}
}
func (m *GetMoodSortReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMoodSortReq.Unmarshal(m, b)
}
func (m *GetMoodSortReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMoodSortReq.Marshal(b, m, deterministic)
}
func (dst *GetMoodSortReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMoodSortReq.Merge(dst, src)
}
func (m *GetMoodSortReq) XXX_Size() int {
	return xxx_messageInfo_GetMoodSortReq.Size(m)
}
func (m *GetMoodSortReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMoodSortReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMoodSortReq proto.InternalMessageInfo

func (m *GetMoodSortReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetMoodSortReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetMoodSortResp struct {
	Moods                []*MoodSort `protobuf:"bytes,1,rep,name=moods,proto3" json:"moods,omitempty"`
	Total                uint32      `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetMoodSortResp) Reset()         { *m = GetMoodSortResp{} }
func (m *GetMoodSortResp) String() string { return proto.CompactTextString(m) }
func (*GetMoodSortResp) ProtoMessage()    {}
func (*GetMoodSortResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mood_e951fb0984221d46, []int{12}
}
func (m *GetMoodSortResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMoodSortResp.Unmarshal(m, b)
}
func (m *GetMoodSortResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMoodSortResp.Marshal(b, m, deterministic)
}
func (dst *GetMoodSortResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMoodSortResp.Merge(dst, src)
}
func (m *GetMoodSortResp) XXX_Size() int {
	return xxx_messageInfo_GetMoodSortResp.Size(m)
}
func (m *GetMoodSortResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMoodSortResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMoodSortResp proto.InternalMessageInfo

func (m *GetMoodSortResp) GetMoods() []*MoodSort {
	if m != nil {
		return m.Moods
	}
	return nil
}

func (m *GetMoodSortResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type BatDelMoodSortReq struct {
	MoodIdList           []string `protobuf:"bytes,1,rep,name=mood_id_list,json=moodIdList,proto3" json:"mood_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatDelMoodSortReq) Reset()         { *m = BatDelMoodSortReq{} }
func (m *BatDelMoodSortReq) String() string { return proto.CompactTextString(m) }
func (*BatDelMoodSortReq) ProtoMessage()    {}
func (*BatDelMoodSortReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mood_e951fb0984221d46, []int{13}
}
func (m *BatDelMoodSortReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatDelMoodSortReq.Unmarshal(m, b)
}
func (m *BatDelMoodSortReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatDelMoodSortReq.Marshal(b, m, deterministic)
}
func (dst *BatDelMoodSortReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatDelMoodSortReq.Merge(dst, src)
}
func (m *BatDelMoodSortReq) XXX_Size() int {
	return xxx_messageInfo_BatDelMoodSortReq.Size(m)
}
func (m *BatDelMoodSortReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatDelMoodSortReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatDelMoodSortReq proto.InternalMessageInfo

func (m *BatDelMoodSortReq) GetMoodIdList() []string {
	if m != nil {
		return m.MoodIdList
	}
	return nil
}

type BatDelMoodSortResp struct {
	DeletedCnt           uint32   `protobuf:"varint,1,opt,name=deleted_cnt,json=deletedCnt,proto3" json:"deleted_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatDelMoodSortResp) Reset()         { *m = BatDelMoodSortResp{} }
func (m *BatDelMoodSortResp) String() string { return proto.CompactTextString(m) }
func (*BatDelMoodSortResp) ProtoMessage()    {}
func (*BatDelMoodSortResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mood_e951fb0984221d46, []int{14}
}
func (m *BatDelMoodSortResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatDelMoodSortResp.Unmarshal(m, b)
}
func (m *BatDelMoodSortResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatDelMoodSortResp.Marshal(b, m, deterministic)
}
func (dst *BatDelMoodSortResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatDelMoodSortResp.Merge(dst, src)
}
func (m *BatDelMoodSortResp) XXX_Size() int {
	return xxx_messageInfo_BatDelMoodSortResp.Size(m)
}
func (m *BatDelMoodSortResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatDelMoodSortResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatDelMoodSortResp proto.InternalMessageInfo

func (m *BatDelMoodSortResp) GetDeletedCnt() uint32 {
	if m != nil {
		return m.DeletedCnt
	}
	return 0
}

// 批量删除
type BatDeleteMoodReq struct {
	MoodIdList           []string `protobuf:"bytes,1,rep,name=mood_id_list,json=moodIdList,proto3" json:"mood_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatDeleteMoodReq) Reset()         { *m = BatDeleteMoodReq{} }
func (m *BatDeleteMoodReq) String() string { return proto.CompactTextString(m) }
func (*BatDeleteMoodReq) ProtoMessage()    {}
func (*BatDeleteMoodReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mood_e951fb0984221d46, []int{15}
}
func (m *BatDeleteMoodReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatDeleteMoodReq.Unmarshal(m, b)
}
func (m *BatDeleteMoodReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatDeleteMoodReq.Marshal(b, m, deterministic)
}
func (dst *BatDeleteMoodReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatDeleteMoodReq.Merge(dst, src)
}
func (m *BatDeleteMoodReq) XXX_Size() int {
	return xxx_messageInfo_BatDeleteMoodReq.Size(m)
}
func (m *BatDeleteMoodReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatDeleteMoodReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatDeleteMoodReq proto.InternalMessageInfo

func (m *BatDeleteMoodReq) GetMoodIdList() []string {
	if m != nil {
		return m.MoodIdList
	}
	return nil
}

type BatDeleteMoodResp struct {
	DeletedCnt           uint32   `protobuf:"varint,1,opt,name=deleted_cnt,json=deletedCnt,proto3" json:"deleted_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatDeleteMoodResp) Reset()         { *m = BatDeleteMoodResp{} }
func (m *BatDeleteMoodResp) String() string { return proto.CompactTextString(m) }
func (*BatDeleteMoodResp) ProtoMessage()    {}
func (*BatDeleteMoodResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mood_e951fb0984221d46, []int{16}
}
func (m *BatDeleteMoodResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatDeleteMoodResp.Unmarshal(m, b)
}
func (m *BatDeleteMoodResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatDeleteMoodResp.Marshal(b, m, deterministic)
}
func (dst *BatDeleteMoodResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatDeleteMoodResp.Merge(dst, src)
}
func (m *BatDeleteMoodResp) XXX_Size() int {
	return xxx_messageInfo_BatDeleteMoodResp.Size(m)
}
func (m *BatDeleteMoodResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatDeleteMoodResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatDeleteMoodResp proto.InternalMessageInfo

func (m *BatDeleteMoodResp) GetDeletedCnt() uint32 {
	if m != nil {
		return m.DeletedCnt
	}
	return 0
}

type GetAllValidMoodReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllValidMoodReq) Reset()         { *m = GetAllValidMoodReq{} }
func (m *GetAllValidMoodReq) String() string { return proto.CompactTextString(m) }
func (*GetAllValidMoodReq) ProtoMessage()    {}
func (*GetAllValidMoodReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mood_e951fb0984221d46, []int{17}
}
func (m *GetAllValidMoodReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllValidMoodReq.Unmarshal(m, b)
}
func (m *GetAllValidMoodReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllValidMoodReq.Marshal(b, m, deterministic)
}
func (dst *GetAllValidMoodReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllValidMoodReq.Merge(dst, src)
}
func (m *GetAllValidMoodReq) XXX_Size() int {
	return xxx_messageInfo_GetAllValidMoodReq.Size(m)
}
func (m *GetAllValidMoodReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllValidMoodReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllValidMoodReq proto.InternalMessageInfo

type GetAllValidMoodResp struct {
	Moods                []*MoodInfo `protobuf:"bytes,1,rep,name=moods,proto3" json:"moods,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetAllValidMoodResp) Reset()         { *m = GetAllValidMoodResp{} }
func (m *GetAllValidMoodResp) String() string { return proto.CompactTextString(m) }
func (*GetAllValidMoodResp) ProtoMessage()    {}
func (*GetAllValidMoodResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mood_e951fb0984221d46, []int{18}
}
func (m *GetAllValidMoodResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllValidMoodResp.Unmarshal(m, b)
}
func (m *GetAllValidMoodResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllValidMoodResp.Marshal(b, m, deterministic)
}
func (dst *GetAllValidMoodResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllValidMoodResp.Merge(dst, src)
}
func (m *GetAllValidMoodResp) XXX_Size() int {
	return xxx_messageInfo_GetAllValidMoodResp.Size(m)
}
func (m *GetAllValidMoodResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllValidMoodResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllValidMoodResp proto.InternalMessageInfo

func (m *GetAllValidMoodResp) GetMoods() []*MoodInfo {
	if m != nil {
		return m.Moods
	}
	return nil
}

func init() {
	proto.RegisterType((*MoodBaseInfo)(nil), "ugc.mood.MoodBaseInfo")
	proto.RegisterType((*MoodBaseInfo_PositiveMoodConf)(nil), "ugc.mood.MoodBaseInfo.PositiveMoodConf")
	proto.RegisterType((*MoodBaseInfo_NegativeMoodConf)(nil), "ugc.mood.MoodBaseInfo.NegativeMoodConf")
	proto.RegisterType((*MoodInfo)(nil), "ugc.mood.MoodInfo")
	proto.RegisterType((*UpdateMoodInfoReq)(nil), "ugc.mood.UpdateMoodInfoReq")
	proto.RegisterType((*UpdateMoodInfoResp)(nil), "ugc.mood.UpdateMoodInfoResp")
	proto.RegisterType((*GetMoodsReq)(nil), "ugc.mood.GetMoodsReq")
	proto.RegisterType((*GetMoodsResp)(nil), "ugc.mood.GetMoodsResp")
	proto.RegisterType((*GetMoodListReq)(nil), "ugc.mood.GetMoodListReq")
	proto.RegisterType((*GetMoodListResp)(nil), "ugc.mood.GetMoodListResp")
	proto.RegisterType((*MoodSort)(nil), "ugc.mood.MoodSort")
	proto.RegisterType((*SetMoodSortReq)(nil), "ugc.mood.SetMoodSortReq")
	proto.RegisterType((*SetMoodSortResp)(nil), "ugc.mood.SetMoodSortResp")
	proto.RegisterType((*GetMoodSortReq)(nil), "ugc.mood.GetMoodSortReq")
	proto.RegisterType((*GetMoodSortResp)(nil), "ugc.mood.GetMoodSortResp")
	proto.RegisterType((*BatDelMoodSortReq)(nil), "ugc.mood.BatDelMoodSortReq")
	proto.RegisterType((*BatDelMoodSortResp)(nil), "ugc.mood.BatDelMoodSortResp")
	proto.RegisterType((*BatDeleteMoodReq)(nil), "ugc.mood.BatDeleteMoodReq")
	proto.RegisterType((*BatDeleteMoodResp)(nil), "ugc.mood.BatDeleteMoodResp")
	proto.RegisterType((*GetAllValidMoodReq)(nil), "ugc.mood.GetAllValidMoodReq")
	proto.RegisterType((*GetAllValidMoodResp)(nil), "ugc.mood.GetAllValidMoodResp")
	proto.RegisterEnum("ugc.mood.MoodType", MoodType_name, MoodType_value)
	proto.RegisterEnum("ugc.mood.SortNDays", SortNDays_name, SortNDays_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MoodClient is the client API for Mood service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MoodClient interface {
	// 更新心情资料
	UpdateMoodInfo(ctx context.Context, in *UpdateMoodInfoReq, opts ...grpc.CallOption) (*UpdateMoodInfoResp, error)
	// 删除
	BatDeleteMood(ctx context.Context, in *BatDeleteMoodReq, opts ...grpc.CallOption) (*BatDeleteMoodResp, error)
	// 返回指定心情详细资料
	GetMoods(ctx context.Context, in *GetMoodsReq, opts ...grpc.CallOption) (*GetMoodsResp, error)
	// 返回所有心情
	GetMoodList(ctx context.Context, in *GetMoodListReq, opts ...grpc.CallOption) (*GetMoodListResp, error)
	// 心情展示排序
	SetMoodSort(ctx context.Context, in *SetMoodSortReq, opts ...grpc.CallOption) (*SetMoodSortResp, error)
	GetMoodSort(ctx context.Context, in *GetMoodSortReq, opts ...grpc.CallOption) (*GetMoodSortResp, error)
	BatDelMoodSort(ctx context.Context, in *BatDelMoodSortReq, opts ...grpc.CallOption) (*BatDelMoodSortResp, error)
	GetAllValidMood(ctx context.Context, in *GetAllValidMoodReq, opts ...grpc.CallOption) (*GetAllValidMoodResp, error)
}

type moodClient struct {
	cc *grpc.ClientConn
}

func NewMoodClient(cc *grpc.ClientConn) MoodClient {
	return &moodClient{cc}
}

func (c *moodClient) UpdateMoodInfo(ctx context.Context, in *UpdateMoodInfoReq, opts ...grpc.CallOption) (*UpdateMoodInfoResp, error) {
	out := new(UpdateMoodInfoResp)
	err := c.cc.Invoke(ctx, "/ugc.mood.Mood/UpdateMoodInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moodClient) BatDeleteMood(ctx context.Context, in *BatDeleteMoodReq, opts ...grpc.CallOption) (*BatDeleteMoodResp, error) {
	out := new(BatDeleteMoodResp)
	err := c.cc.Invoke(ctx, "/ugc.mood.Mood/BatDeleteMood", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moodClient) GetMoods(ctx context.Context, in *GetMoodsReq, opts ...grpc.CallOption) (*GetMoodsResp, error) {
	out := new(GetMoodsResp)
	err := c.cc.Invoke(ctx, "/ugc.mood.Mood/GetMoods", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moodClient) GetMoodList(ctx context.Context, in *GetMoodListReq, opts ...grpc.CallOption) (*GetMoodListResp, error) {
	out := new(GetMoodListResp)
	err := c.cc.Invoke(ctx, "/ugc.mood.Mood/GetMoodList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moodClient) SetMoodSort(ctx context.Context, in *SetMoodSortReq, opts ...grpc.CallOption) (*SetMoodSortResp, error) {
	out := new(SetMoodSortResp)
	err := c.cc.Invoke(ctx, "/ugc.mood.Mood/SetMoodSort", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moodClient) GetMoodSort(ctx context.Context, in *GetMoodSortReq, opts ...grpc.CallOption) (*GetMoodSortResp, error) {
	out := new(GetMoodSortResp)
	err := c.cc.Invoke(ctx, "/ugc.mood.Mood/GetMoodSort", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moodClient) BatDelMoodSort(ctx context.Context, in *BatDelMoodSortReq, opts ...grpc.CallOption) (*BatDelMoodSortResp, error) {
	out := new(BatDelMoodSortResp)
	err := c.cc.Invoke(ctx, "/ugc.mood.Mood/BatDelMoodSort", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moodClient) GetAllValidMood(ctx context.Context, in *GetAllValidMoodReq, opts ...grpc.CallOption) (*GetAllValidMoodResp, error) {
	out := new(GetAllValidMoodResp)
	err := c.cc.Invoke(ctx, "/ugc.mood.Mood/GetAllValidMood", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MoodServer is the server API for Mood service.
type MoodServer interface {
	// 更新心情资料
	UpdateMoodInfo(context.Context, *UpdateMoodInfoReq) (*UpdateMoodInfoResp, error)
	// 删除
	BatDeleteMood(context.Context, *BatDeleteMoodReq) (*BatDeleteMoodResp, error)
	// 返回指定心情详细资料
	GetMoods(context.Context, *GetMoodsReq) (*GetMoodsResp, error)
	// 返回所有心情
	GetMoodList(context.Context, *GetMoodListReq) (*GetMoodListResp, error)
	// 心情展示排序
	SetMoodSort(context.Context, *SetMoodSortReq) (*SetMoodSortResp, error)
	GetMoodSort(context.Context, *GetMoodSortReq) (*GetMoodSortResp, error)
	BatDelMoodSort(context.Context, *BatDelMoodSortReq) (*BatDelMoodSortResp, error)
	GetAllValidMood(context.Context, *GetAllValidMoodReq) (*GetAllValidMoodResp, error)
}

func RegisterMoodServer(s *grpc.Server, srv MoodServer) {
	s.RegisterService(&_Mood_serviceDesc, srv)
}

func _Mood_UpdateMoodInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateMoodInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoodServer).UpdateMoodInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.mood.Mood/UpdateMoodInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoodServer).UpdateMoodInfo(ctx, req.(*UpdateMoodInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Mood_BatDeleteMood_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatDeleteMoodReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoodServer).BatDeleteMood(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.mood.Mood/BatDeleteMood",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoodServer).BatDeleteMood(ctx, req.(*BatDeleteMoodReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Mood_GetMoods_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMoodsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoodServer).GetMoods(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.mood.Mood/GetMoods",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoodServer).GetMoods(ctx, req.(*GetMoodsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Mood_GetMoodList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMoodListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoodServer).GetMoodList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.mood.Mood/GetMoodList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoodServer).GetMoodList(ctx, req.(*GetMoodListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Mood_SetMoodSort_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetMoodSortReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoodServer).SetMoodSort(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.mood.Mood/SetMoodSort",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoodServer).SetMoodSort(ctx, req.(*SetMoodSortReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Mood_GetMoodSort_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMoodSortReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoodServer).GetMoodSort(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.mood.Mood/GetMoodSort",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoodServer).GetMoodSort(ctx, req.(*GetMoodSortReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Mood_BatDelMoodSort_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatDelMoodSortReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoodServer).BatDelMoodSort(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.mood.Mood/BatDelMoodSort",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoodServer).BatDelMoodSort(ctx, req.(*BatDelMoodSortReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Mood_GetAllValidMood_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllValidMoodReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoodServer).GetAllValidMood(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.mood.Mood/GetAllValidMood",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoodServer).GetAllValidMood(ctx, req.(*GetAllValidMoodReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Mood_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ugc.mood.Mood",
	HandlerType: (*MoodServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpdateMoodInfo",
			Handler:    _Mood_UpdateMoodInfo_Handler,
		},
		{
			MethodName: "BatDeleteMood",
			Handler:    _Mood_BatDeleteMood_Handler,
		},
		{
			MethodName: "GetMoods",
			Handler:    _Mood_GetMoods_Handler,
		},
		{
			MethodName: "GetMoodList",
			Handler:    _Mood_GetMoodList_Handler,
		},
		{
			MethodName: "SetMoodSort",
			Handler:    _Mood_SetMoodSort_Handler,
		},
		{
			MethodName: "GetMoodSort",
			Handler:    _Mood_GetMoodSort_Handler,
		},
		{
			MethodName: "BatDelMoodSort",
			Handler:    _Mood_BatDelMoodSort_Handler,
		},
		{
			MethodName: "GetAllValidMood",
			Handler:    _Mood_GetAllValidMood_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ugc/mood.proto",
}

func init() { proto.RegisterFile("ugc/mood.proto", fileDescriptor_mood_e951fb0984221d46) }

var fileDescriptor_mood_e951fb0984221d46 = []byte{
	// 994 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x56, 0xeb, 0x4e, 0xe3, 0x46,
	0x14, 0x26, 0x4b, 0x2e, 0xce, 0x71, 0x6e, 0x4c, 0x59, 0x6a, 0x02, 0x55, 0x53, 0xff, 0x68, 0x23,
	0x54, 0x25, 0x52, 0x76, 0xe9, 0x45, 0x95, 0xba, 0x5a, 0x88, 0xca, 0x52, 0x15, 0x96, 0x1a, 0xe8,
	0x8f, 0xfe, 0xb1, 0x1c, 0x7b, 0x12, 0xac, 0x3a, 0x33, 0x5e, 0xcf, 0x04, 0x2d, 0x2f, 0xd3, 0x77,
	0xea, 0x4b, 0xf4, 0x39, 0xaa, 0x33, 0xb6, 0x13, 0xdb, 0x21, 0x68, 0xe9, 0x3f, 0x9f, 0xdb, 0x77,
	0xbe, 0x39, 0x37, 0x19, 0x5a, 0x8b, 0x99, 0x3b, 0x9c, 0x73, 0xee, 0x0d, 0xc2, 0x88, 0x4b, 0x4e,
	0xb4, 0xc5, 0xcc, 0x1d, 0xa0, 0x6c, 0xfe, 0x5b, 0x86, 0xc6, 0x05, 0xe7, 0xde, 0x89, 0x23, 0xe8,
	0x39, 0x9b, 0x72, 0xf2, 0x39, 0xd4, 0xd0, 0x60, 0xfb, 0x9e, 0x51, 0xea, 0x95, 0xfa, 0x75, 0xab,
	0x8a, 0xe2, 0xb9, 0x47, 0x08, 0x94, 0x99, 0x33, 0xa7, 0xc6, 0x0b, 0xa5, 0x55, 0xdf, 0xe4, 0x00,
	0xea, 0x62, 0xee, 0x04, 0x81, 0xbd, 0x88, 0x02, 0x63, 0x5b, 0x19, 0x34, 0xa5, 0xb8, 0x8d, 0x02,
	0xf2, 0x12, 0xaa, 0x93, 0x99, 0xb2, 0x94, 0x95, 0xa5, 0x32, 0x99, 0xa1, 0xfa, 0x00, 0xea, 0x92,
	0x7e, 0x94, 0x76, 0xc8, 0x85, 0x34, 0x2a, 0xbd, 0x6d, 0x8c, 0x41, 0xc5, 0x15, 0x17, 0x92, 0x0c,
	0xa1, 0xae, 0xb2, 0xcb, 0x87, 0x90, 0x1a, 0xd5, 0x5e, 0xa9, 0xdf, 0x1a, 0x91, 0x41, 0x4a, 0x76,
	0x80, 0x44, 0x6f, 0x1e, 0x42, 0x6a, 0x69, 0xf3, 0xe4, 0x8b, 0x5c, 0x42, 0x33, 0xe4, 0xc2, 0x97,
	0xfe, 0x3d, 0xb5, 0x5d, 0xce, 0xa6, 0x46, 0xad, 0x57, 0xea, 0xeb, 0xa3, 0x6f, 0xf2, 0x41, 0xe9,
	0xeb, 0x06, 0x57, 0x89, 0x2f, 0x2a, 0x4f, 0x39, 0x9b, 0xbe, 0xdb, 0xb2, 0x1a, 0x69, 0x3c, 0xca,
	0x88, 0xc7, 0xe8, 0xcc, 0x59, 0xe1, 0x69, 0x4f, 0xe2, 0x5d, 0x26, 0xbe, 0x59, 0xbc, 0x34, 0x5e,
	0xe1, 0xed, 0x42, 0xc5, 0x67, 0x1e, 0xfd, 0x68, 0xd4, 0x7b, 0xa5, 0x7e, 0xd3, 0x8a, 0x05, 0x62,
	0x42, 0x73, 0xe2, 0x33, 0xcf, 0x96, 0x3c, 0xf4, 0x5d, 0x2c, 0x35, 0xa8, 0x0a, 0xe9, 0xa8, 0xbc,
	0x41, 0xdd, 0xb9, 0x87, 0x75, 0x72, 0x23, 0xea, 0x48, 0x6a, 0x4b, 0x61, 0xe8, 0x2a, 0x5a, 0x8b,
	0x15, 0x37, 0xa2, 0xfb, 0x1d, 0x74, 0x8a, 0x4f, 0x21, 0x26, 0x34, 0x7c, 0x26, 0x23, 0xee, 0x2d,
	0x5c, 0xe9, 0x73, 0x96, 0xb4, 0x2f, 0xa7, 0xeb, 0x72, 0xe8, 0x14, 0x29, 0x63, 0xc7, 0x55, 0x43,
	0xe6, 0xd4, 0x28, 0xa9, 0x76, 0x54, 0x51, 0xbc, 0xa0, 0xe4, 0x4b, 0xd0, 0x95, 0x81, 0xcb, 0x3b,
	0x1a, 0x89, 0xa4, 0xf1, 0x80, 0xaa, 0xf7, 0x4a, 0x43, 0xbe, 0x82, 0x86, 0x72, 0x70, 0xf9, 0x7c,
	0xca, 0x23, 0x69, 0x6c, 0xab, 0x70, 0x15, 0x74, 0x1a, 0xab, 0x4e, 0x6a, 0x50, 0x51, 0x5d, 0x33,
	0xdf, 0x80, 0x86, 0x1f, 0x6a, 0xc6, 0x5e, 0x41, 0x7d, 0xe2, 0x08, 0x6a, 0xfb, 0x6c, 0xca, 0x15,
	0x4d, 0x7d, 0xb4, 0xf7, 0x78, 0x81, 0x2d, 0x6d, 0x92, 0x7c, 0x99, 0xef, 0x60, 0xe7, 0x36, 0xf4,
	0x1c, 0x49, 0x53, 0x18, 0x8b, 0x7e, 0xf8, 0xbf, 0x48, 0xa4, 0x88, 0x24, 0xc2, 0xcd, 0x83, 0xbf,
	0x0f, 0xda, 0xb2, 0x4f, 0x71, 0x0d, 0x6a, 0x32, 0xee, 0x91, 0xd9, 0x07, 0xfd, 0x8c, 0x4a, 0x84,
	0x11, 0xc8, 0x66, 0x1f, 0xb4, 0x04, 0x42, 0x24, 0xa5, 0xac, 0xc5, 0x18, 0xc2, 0xfc, 0x01, 0x1a,
	0x2b, 0x4f, 0x11, 0x92, 0x3e, 0x54, 0xd0, 0x14, 0xfb, 0xe9, 0xc5, 0x21, 0x57, 0xa4, 0x62, 0x07,
	0xf3, 0xef, 0x12, 0xb4, 0x92, 0xd0, 0xdf, 0x7c, 0x21, 0x31, 0xcf, 0x1e, 0x54, 0xf9, 0x74, 0x2a,
	0xa8, 0x54, 0x4c, 0x9b, 0x56, 0x22, 0xe1, 0xb0, 0x05, 0xfe, 0xdc, 0x97, 0x8a, 0x66, 0xd3, 0x8a,
	0x05, 0x1c, 0xa4, 0xd5, 0x4e, 0x95, 0xe3, 0x41, 0x5a, 0xee, 0xcf, 0x2e, 0x54, 0x98, 0xe7, 0x3c,
	0x08, 0x35, 0x81, 0x4d, 0x2b, 0x16, 0x96, 0xbb, 0xae, 0x67, 0x76, 0x3d, 0x53, 0x9f, 0x46, 0xb6,
	0x3e, 0xe6, 0xef, 0xd0, 0xce, 0xf1, 0x7b, 0xce, 0xeb, 0x30, 0xbf, 0xe4, 0xd2, 0x09, 0x52, 0xca,
	0x4a, 0x30, 0x7f, 0x8c, 0x87, 0xe5, 0x9a, 0x47, 0x72, 0x73, 0x5f, 0x96, 0xab, 0xf5, 0x22, 0xb3,
	0x5a, 0xe6, 0x04, 0x5a, 0xd7, 0x31, 0x1b, 0x8c, 0xc6, 0x6a, 0x3d, 0x4d, 0x46, 0x79, 0x25, 0x64,
	0xbe, 0x86, 0x36, 0xe3, 0xf6, 0x82, 0xf9, 0x1f, 0x16, 0xd4, 0x76, 0xef, 0xa8, 0xfb, 0x97, 0xc2,
	0xd6, 0xac, 0x26, 0xe3, 0xb7, 0x4a, 0x7b, 0x8a, 0x4a, 0x73, 0x07, 0xda, 0xb9, 0x1c, 0x22, 0x34,
	0x7f, 0x5e, 0x36, 0x29, 0x4d, 0xfb, 0xac, 0x26, 0x65, 0x8a, 0x98, 0x42, 0x3e, 0x83, 0xf7, 0xe3,
	0x45, 0x3c, 0x86, 0x9d, 0x13, 0x47, 0x8e, 0x69, 0x90, 0x65, 0xd5, 0x83, 0x46, 0x52, 0x4d, 0x3b,
	0xf0, 0x85, 0x4c, 0xc6, 0x14, 0xe2, 0x92, 0x62, 0xff, 0xcc, 0x63, 0x20, 0xc5, 0x30, 0x11, 0xe2,
	0x2d, 0xf0, 0x68, 0x40, 0x25, 0xf5, 0x6c, 0x97, 0xa5, 0x4f, 0x82, 0x44, 0x75, 0xca, 0xa4, 0xf9,
	0x1a, 0x3a, 0x71, 0x18, 0x8d, 0xf7, 0xea, 0xd3, 0x92, 0xbd, 0x4e, 0x39, 0x2e, 0xa3, 0x3e, 0x25,
	0xd7, 0x2e, 0x90, 0x33, 0x2a, 0xdf, 0x06, 0xc1, 0x1f, 0x4e, 0xe0, 0x7b, 0x49, 0x36, 0xf3, 0x0d,
	0x7c, 0xb6, 0xa6, 0x7d, 0xce, 0x2c, 0x1e, 0x7d, 0x1f, 0x4f, 0x9d, 0xda, 0x8b, 0x36, 0xe8, 0x6f,
	0x83, 0x20, 0x15, 0x3b, 0x5b, 0xa4, 0x01, 0x5a, 0x7a, 0x71, 0x3b, 0x25, 0x94, 0xd2, 0x3b, 0xda,
	0x79, 0x71, 0xf4, 0x0b, 0xd4, 0xb1, 0x50, 0x97, 0x63, 0xdc, 0x9d, 0x36, 0xe8, 0x63, 0x3a, 0x75,
	0x16, 0x81, 0x44, 0x5d, 0x67, 0x8b, 0x00, 0x54, 0xdf, 0x33, 0x3a, 0x76, 0x1e, 0x3a, 0x25, 0xd2,
	0x84, 0xfa, 0xcd, 0x5d, 0x44, 0x51, 0x12, 0x9d, 0x6d, 0x14, 0xaf, 0xe9, 0x3d, 0x65, 0x4a, 0xac,
	0x8d, 0xfe, 0x29, 0x43, 0x19, 0x53, 0x92, 0x0b, 0x68, 0xe5, 0x2f, 0x14, 0x39, 0x58, 0xd1, 0x5e,
	0xbb, 0x82, 0xdd, 0xc3, 0xcd, 0x46, 0x11, 0x9a, 0x5b, 0xe4, 0x57, 0x68, 0xe6, 0xaa, 0x4c, 0xba,
	0xab, 0x80, 0x62, 0xd3, 0xba, 0x07, 0x1b, 0x6d, 0x0a, 0xeb, 0x27, 0xd0, 0xd2, 0x43, 0x46, 0x5e,
	0xae, 0x5c, 0x33, 0x67, 0xb0, 0xbb, 0xf7, 0x98, 0x5a, 0x05, 0x8f, 0x97, 0xf7, 0x12, 0xbb, 0x4f,
	0x8c, 0x35, 0xc7, 0xe4, 0xc2, 0x75, 0xf7, 0x37, 0x58, 0x52, 0x94, 0xcc, 0xfa, 0x65, 0x51, 0xf2,
	0x9b, 0x9f, 0x45, 0x29, 0xee, 0x6b, 0x96, 0x4b, 0x11, 0xe5, 0x6c, 0x23, 0xca, 0xd9, 0x1a, 0xca,
	0x05, 0xb4, 0xf2, 0xdb, 0x42, 0xd6, 0xea, 0x97, 0xc5, 0x3a, 0xdc, 0x6c, 0x54, 0x70, 0x57, 0xea,
	0x0c, 0x64, 0x67, 0x98, 0x1c, 0xe6, 0xd2, 0x17, 0x86, 0xbe, 0xfb, 0xc5, 0x13, 0x56, 0x44, 0x3c,
	0xf9, 0xf6, 0xcf, 0xa3, 0x19, 0x0f, 0x1c, 0x36, 0x1b, 0x1c, 0x8f, 0xa4, 0x1c, 0xb8, 0x7c, 0x3e,
	0x54, 0xff, 0x80, 0x2e, 0x0f, 0x86, 0x82, 0x46, 0xf7, 0xbe, 0x4b, 0xc5, 0x30, 0xfd, 0x3d, 0x9c,
	0x54, 0x95, 0xed, 0xd5, 0x7f, 0x01, 0x00, 0x00, 0xff, 0xff, 0x44, 0xaa, 0x8d, 0x9d, 0x31, 0x0a,
	0x00, 0x00,
}
