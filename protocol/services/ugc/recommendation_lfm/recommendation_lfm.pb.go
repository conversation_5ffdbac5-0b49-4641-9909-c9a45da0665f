// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ugc/recommendation_lfm.proto

package recommendation_lfm // import "golang.52tt.com/protocol/services/ugc/recommendation_lfm"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type Filter_Method int32

const (
	Filter_RANDOM Filter_Method = 0
	Filter_TIME   Filter_Method = 1
	Filter_TAGS   Filter_Method = 2
	Filter_FORCE  Filter_Method = 3
)

var Filter_Method_name = map[int32]string{
	0: "RANDOM",
	1: "TIME",
	2: "TAGS",
	3: "FORCE",
}
var Filter_Method_value = map[string]int32{
	"RANDOM": 0,
	"TIME":   1,
	"TAGS":   2,
	"FORCE":  3,
}

func (x Filter_Method) String() string {
	return proto.EnumName(Filter_Method_name, int32(x))
}
func (Filter_Method) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_lfm_a1476ef0757e6028, []int{7, 0}
}

type AddUserReadHistoryReq struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	PostIdList           []string `protobuf:"bytes,2,rep,name=post_id_list,json=postIdList,proto3" json:"post_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUserReadHistoryReq) Reset()         { *m = AddUserReadHistoryReq{} }
func (m *AddUserReadHistoryReq) String() string { return proto.CompactTextString(m) }
func (*AddUserReadHistoryReq) ProtoMessage()    {}
func (*AddUserReadHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_lfm_a1476ef0757e6028, []int{0}
}
func (m *AddUserReadHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserReadHistoryReq.Unmarshal(m, b)
}
func (m *AddUserReadHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserReadHistoryReq.Marshal(b, m, deterministic)
}
func (dst *AddUserReadHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserReadHistoryReq.Merge(dst, src)
}
func (m *AddUserReadHistoryReq) XXX_Size() int {
	return xxx_messageInfo_AddUserReadHistoryReq.Size(m)
}
func (m *AddUserReadHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserReadHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserReadHistoryReq proto.InternalMessageInfo

func (m *AddUserReadHistoryReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *AddUserReadHistoryReq) GetPostIdList() []string {
	if m != nil {
		return m.PostIdList
	}
	return nil
}

type AddUserReadHistoryResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUserReadHistoryResp) Reset()         { *m = AddUserReadHistoryResp{} }
func (m *AddUserReadHistoryResp) String() string { return proto.CompactTextString(m) }
func (*AddUserReadHistoryResp) ProtoMessage()    {}
func (*AddUserReadHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_lfm_a1476ef0757e6028, []int{1}
}
func (m *AddUserReadHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserReadHistoryResp.Unmarshal(m, b)
}
func (m *AddUserReadHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserReadHistoryResp.Marshal(b, m, deterministic)
}
func (dst *AddUserReadHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserReadHistoryResp.Merge(dst, src)
}
func (m *AddUserReadHistoryResp) XXX_Size() int {
	return xxx_messageInfo_AddUserReadHistoryResp.Size(m)
}
func (m *AddUserReadHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserReadHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserReadHistoryResp proto.InternalMessageInfo

type RecommendationPostInfo struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	TopicId              string   `protobuf:"bytes,2,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	Score                uint32   `protobuf:"varint,3,opt,name=score,proto3" json:"score,omitempty"`
	Hint                 string   `protobuf:"bytes,4,opt,name=hint,proto3" json:"hint,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecommendationPostInfo) Reset()         { *m = RecommendationPostInfo{} }
func (m *RecommendationPostInfo) String() string { return proto.CompactTextString(m) }
func (*RecommendationPostInfo) ProtoMessage()    {}
func (*RecommendationPostInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_lfm_a1476ef0757e6028, []int{2}
}
func (m *RecommendationPostInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendationPostInfo.Unmarshal(m, b)
}
func (m *RecommendationPostInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendationPostInfo.Marshal(b, m, deterministic)
}
func (dst *RecommendationPostInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendationPostInfo.Merge(dst, src)
}
func (m *RecommendationPostInfo) XXX_Size() int {
	return xxx_messageInfo_RecommendationPostInfo.Size(m)
}
func (m *RecommendationPostInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendationPostInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendationPostInfo proto.InternalMessageInfo

func (m *RecommendationPostInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *RecommendationPostInfo) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *RecommendationPostInfo) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *RecommendationPostInfo) GetHint() string {
	if m != nil {
		return m.Hint
	}
	return ""
}

type AddPostsIntoReq struct {
	PostInfoList         []*RecommendationPostInfo `protobuf:"bytes,1,rep,name=post_info_list,json=postInfoList,proto3" json:"post_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *AddPostsIntoReq) Reset()         { *m = AddPostsIntoReq{} }
func (m *AddPostsIntoReq) String() string { return proto.CompactTextString(m) }
func (*AddPostsIntoReq) ProtoMessage()    {}
func (*AddPostsIntoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_lfm_a1476ef0757e6028, []int{3}
}
func (m *AddPostsIntoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPostsIntoReq.Unmarshal(m, b)
}
func (m *AddPostsIntoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPostsIntoReq.Marshal(b, m, deterministic)
}
func (dst *AddPostsIntoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPostsIntoReq.Merge(dst, src)
}
func (m *AddPostsIntoReq) XXX_Size() int {
	return xxx_messageInfo_AddPostsIntoReq.Size(m)
}
func (m *AddPostsIntoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPostsIntoReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddPostsIntoReq proto.InternalMessageInfo

func (m *AddPostsIntoReq) GetPostInfoList() []*RecommendationPostInfo {
	if m != nil {
		return m.PostInfoList
	}
	return nil
}

type AddPostsIntoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddPostsIntoResp) Reset()         { *m = AddPostsIntoResp{} }
func (m *AddPostsIntoResp) String() string { return proto.CompactTextString(m) }
func (*AddPostsIntoResp) ProtoMessage()    {}
func (*AddPostsIntoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_lfm_a1476ef0757e6028, []int{4}
}
func (m *AddPostsIntoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPostsIntoResp.Unmarshal(m, b)
}
func (m *AddPostsIntoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPostsIntoResp.Marshal(b, m, deterministic)
}
func (dst *AddPostsIntoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPostsIntoResp.Merge(dst, src)
}
func (m *AddPostsIntoResp) XXX_Size() int {
	return xxx_messageInfo_AddPostsIntoResp.Size(m)
}
func (m *AddPostsIntoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPostsIntoResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddPostsIntoResp proto.InternalMessageInfo

type RemovePostsReq struct {
	PostIdList           []string `protobuf:"bytes,1,rep,name=post_id_list,json=postIdList,proto3" json:"post_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemovePostsReq) Reset()         { *m = RemovePostsReq{} }
func (m *RemovePostsReq) String() string { return proto.CompactTextString(m) }
func (*RemovePostsReq) ProtoMessage()    {}
func (*RemovePostsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_lfm_a1476ef0757e6028, []int{5}
}
func (m *RemovePostsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemovePostsReq.Unmarshal(m, b)
}
func (m *RemovePostsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemovePostsReq.Marshal(b, m, deterministic)
}
func (dst *RemovePostsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemovePostsReq.Merge(dst, src)
}
func (m *RemovePostsReq) XXX_Size() int {
	return xxx_messageInfo_RemovePostsReq.Size(m)
}
func (m *RemovePostsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RemovePostsReq.DiscardUnknown(m)
}

var xxx_messageInfo_RemovePostsReq proto.InternalMessageInfo

func (m *RemovePostsReq) GetPostIdList() []string {
	if m != nil {
		return m.PostIdList
	}
	return nil
}

type RemovePostsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemovePostsResp) Reset()         { *m = RemovePostsResp{} }
func (m *RemovePostsResp) String() string { return proto.CompactTextString(m) }
func (*RemovePostsResp) ProtoMessage()    {}
func (*RemovePostsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_lfm_a1476ef0757e6028, []int{6}
}
func (m *RemovePostsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemovePostsResp.Unmarshal(m, b)
}
func (m *RemovePostsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemovePostsResp.Marshal(b, m, deterministic)
}
func (dst *RemovePostsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemovePostsResp.Merge(dst, src)
}
func (m *RemovePostsResp) XXX_Size() int {
	return xxx_messageInfo_RemovePostsResp.Size(m)
}
func (m *RemovePostsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RemovePostsResp.DiscardUnknown(m)
}

var xxx_messageInfo_RemovePostsResp proto.InternalMessageInfo

type Filter struct {
	Method               Filter_Method `protobuf:"varint,1,opt,name=method,proto3,enum=ugc.recommendation_lfm.Filter_Method" json:"method,omitempty"`
	ExceptionPostIdList  []string      `protobuf:"bytes,2,rep,name=exception_post_id_list,json=exceptionPostIdList,proto3" json:"exception_post_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *Filter) Reset()         { *m = Filter{} }
func (m *Filter) String() string { return proto.CompactTextString(m) }
func (*Filter) ProtoMessage()    {}
func (*Filter) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_lfm_a1476ef0757e6028, []int{7}
}
func (m *Filter) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Filter.Unmarshal(m, b)
}
func (m *Filter) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Filter.Marshal(b, m, deterministic)
}
func (dst *Filter) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Filter.Merge(dst, src)
}
func (m *Filter) XXX_Size() int {
	return xxx_messageInfo_Filter.Size(m)
}
func (m *Filter) XXX_DiscardUnknown() {
	xxx_messageInfo_Filter.DiscardUnknown(m)
}

var xxx_messageInfo_Filter proto.InternalMessageInfo

func (m *Filter) GetMethod() Filter_Method {
	if m != nil {
		return m.Method
	}
	return Filter_RANDOM
}

func (m *Filter) GetExceptionPostIdList() []string {
	if m != nil {
		return m.ExceptionPostIdList
	}
	return nil
}

type GetPostsWithFilterReq struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Filter               *Filter  `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
	Count                uint32   `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPostsWithFilterReq) Reset()         { *m = GetPostsWithFilterReq{} }
func (m *GetPostsWithFilterReq) String() string { return proto.CompactTextString(m) }
func (*GetPostsWithFilterReq) ProtoMessage()    {}
func (*GetPostsWithFilterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_lfm_a1476ef0757e6028, []int{8}
}
func (m *GetPostsWithFilterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPostsWithFilterReq.Unmarshal(m, b)
}
func (m *GetPostsWithFilterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPostsWithFilterReq.Marshal(b, m, deterministic)
}
func (dst *GetPostsWithFilterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPostsWithFilterReq.Merge(dst, src)
}
func (m *GetPostsWithFilterReq) XXX_Size() int {
	return xxx_messageInfo_GetPostsWithFilterReq.Size(m)
}
func (m *GetPostsWithFilterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPostsWithFilterReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPostsWithFilterReq proto.InternalMessageInfo

func (m *GetPostsWithFilterReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *GetPostsWithFilterReq) GetFilter() *Filter {
	if m != nil {
		return m.Filter
	}
	return nil
}

func (m *GetPostsWithFilterReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetPostsWithFilterResp struct {
	PostList             []*RecommendationPostInfo `protobuf:"bytes,1,rep,name=post_list,json=postList,proto3" json:"post_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetPostsWithFilterResp) Reset()         { *m = GetPostsWithFilterResp{} }
func (m *GetPostsWithFilterResp) String() string { return proto.CompactTextString(m) }
func (*GetPostsWithFilterResp) ProtoMessage()    {}
func (*GetPostsWithFilterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_lfm_a1476ef0757e6028, []int{9}
}
func (m *GetPostsWithFilterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPostsWithFilterResp.Unmarshal(m, b)
}
func (m *GetPostsWithFilterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPostsWithFilterResp.Marshal(b, m, deterministic)
}
func (dst *GetPostsWithFilterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPostsWithFilterResp.Merge(dst, src)
}
func (m *GetPostsWithFilterResp) XXX_Size() int {
	return xxx_messageInfo_GetPostsWithFilterResp.Size(m)
}
func (m *GetPostsWithFilterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPostsWithFilterResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPostsWithFilterResp proto.InternalMessageInfo

func (m *GetPostsWithFilterResp) GetPostList() []*RecommendationPostInfo {
	if m != nil {
		return m.PostList
	}
	return nil
}

func init() {
	proto.RegisterType((*AddUserReadHistoryReq)(nil), "ugc.recommendation_lfm.AddUserReadHistoryReq")
	proto.RegisterType((*AddUserReadHistoryResp)(nil), "ugc.recommendation_lfm.AddUserReadHistoryResp")
	proto.RegisterType((*RecommendationPostInfo)(nil), "ugc.recommendation_lfm.RecommendationPostInfo")
	proto.RegisterType((*AddPostsIntoReq)(nil), "ugc.recommendation_lfm.AddPostsIntoReq")
	proto.RegisterType((*AddPostsIntoResp)(nil), "ugc.recommendation_lfm.AddPostsIntoResp")
	proto.RegisterType((*RemovePostsReq)(nil), "ugc.recommendation_lfm.RemovePostsReq")
	proto.RegisterType((*RemovePostsResp)(nil), "ugc.recommendation_lfm.RemovePostsResp")
	proto.RegisterType((*Filter)(nil), "ugc.recommendation_lfm.Filter")
	proto.RegisterType((*GetPostsWithFilterReq)(nil), "ugc.recommendation_lfm.GetPostsWithFilterReq")
	proto.RegisterType((*GetPostsWithFilterResp)(nil), "ugc.recommendation_lfm.GetPostsWithFilterResp")
	proto.RegisterEnum("ugc.recommendation_lfm.Filter_Method", Filter_Method_name, Filter_Method_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// UgcLfmRecommendationClient is the client API for UgcLfmRecommendation service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UgcLfmRecommendationClient interface {
	// 用户浏览记录
	AddUserReadHistory(ctx context.Context, in *AddUserReadHistoryReq, opts ...grpc.CallOption) (*AddUserReadHistoryResp, error)
	// 加入推荐池
	AddPostsInto(ctx context.Context, in *AddPostsIntoReq, opts ...grpc.CallOption) (*AddPostsIntoResp, error)
	// 从推荐池移除
	RemovePosts(ctx context.Context, in *RemovePostsReq, opts ...grpc.CallOption) (*RemovePostsResp, error)
	// 从推荐池里面取数据
	GetPostsWithFilter(ctx context.Context, in *GetPostsWithFilterReq, opts ...grpc.CallOption) (*GetPostsWithFilterResp, error)
}

type ugcLfmRecommendationClient struct {
	cc *grpc.ClientConn
}

func NewUgcLfmRecommendationClient(cc *grpc.ClientConn) UgcLfmRecommendationClient {
	return &ugcLfmRecommendationClient{cc}
}

func (c *ugcLfmRecommendationClient) AddUserReadHistory(ctx context.Context, in *AddUserReadHistoryReq, opts ...grpc.CallOption) (*AddUserReadHistoryResp, error) {
	out := new(AddUserReadHistoryResp)
	err := c.cc.Invoke(ctx, "/ugc.recommendation_lfm.UgcLfmRecommendation/AddUserReadHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcLfmRecommendationClient) AddPostsInto(ctx context.Context, in *AddPostsIntoReq, opts ...grpc.CallOption) (*AddPostsIntoResp, error) {
	out := new(AddPostsIntoResp)
	err := c.cc.Invoke(ctx, "/ugc.recommendation_lfm.UgcLfmRecommendation/AddPostsInto", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcLfmRecommendationClient) RemovePosts(ctx context.Context, in *RemovePostsReq, opts ...grpc.CallOption) (*RemovePostsResp, error) {
	out := new(RemovePostsResp)
	err := c.cc.Invoke(ctx, "/ugc.recommendation_lfm.UgcLfmRecommendation/RemovePosts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcLfmRecommendationClient) GetPostsWithFilter(ctx context.Context, in *GetPostsWithFilterReq, opts ...grpc.CallOption) (*GetPostsWithFilterResp, error) {
	out := new(GetPostsWithFilterResp)
	err := c.cc.Invoke(ctx, "/ugc.recommendation_lfm.UgcLfmRecommendation/GetPostsWithFilter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UgcLfmRecommendationServer is the server API for UgcLfmRecommendation service.
type UgcLfmRecommendationServer interface {
	// 用户浏览记录
	AddUserReadHistory(context.Context, *AddUserReadHistoryReq) (*AddUserReadHistoryResp, error)
	// 加入推荐池
	AddPostsInto(context.Context, *AddPostsIntoReq) (*AddPostsIntoResp, error)
	// 从推荐池移除
	RemovePosts(context.Context, *RemovePostsReq) (*RemovePostsResp, error)
	// 从推荐池里面取数据
	GetPostsWithFilter(context.Context, *GetPostsWithFilterReq) (*GetPostsWithFilterResp, error)
}

func RegisterUgcLfmRecommendationServer(s *grpc.Server, srv UgcLfmRecommendationServer) {
	s.RegisterService(&_UgcLfmRecommendation_serviceDesc, srv)
}

func _UgcLfmRecommendation_AddUserReadHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserReadHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcLfmRecommendationServer).AddUserReadHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.recommendation_lfm.UgcLfmRecommendation/AddUserReadHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcLfmRecommendationServer).AddUserReadHistory(ctx, req.(*AddUserReadHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcLfmRecommendation_AddPostsInto_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPostsIntoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcLfmRecommendationServer).AddPostsInto(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.recommendation_lfm.UgcLfmRecommendation/AddPostsInto",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcLfmRecommendationServer).AddPostsInto(ctx, req.(*AddPostsIntoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcLfmRecommendation_RemovePosts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemovePostsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcLfmRecommendationServer).RemovePosts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.recommendation_lfm.UgcLfmRecommendation/RemovePosts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcLfmRecommendationServer).RemovePosts(ctx, req.(*RemovePostsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcLfmRecommendation_GetPostsWithFilter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPostsWithFilterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcLfmRecommendationServer).GetPostsWithFilter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.recommendation_lfm.UgcLfmRecommendation/GetPostsWithFilter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcLfmRecommendationServer).GetPostsWithFilter(ctx, req.(*GetPostsWithFilterReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _UgcLfmRecommendation_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ugc.recommendation_lfm.UgcLfmRecommendation",
	HandlerType: (*UgcLfmRecommendationServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddUserReadHistory",
			Handler:    _UgcLfmRecommendation_AddUserReadHistory_Handler,
		},
		{
			MethodName: "AddPostsInto",
			Handler:    _UgcLfmRecommendation_AddPostsInto_Handler,
		},
		{
			MethodName: "RemovePosts",
			Handler:    _UgcLfmRecommendation_RemovePosts_Handler,
		},
		{
			MethodName: "GetPostsWithFilter",
			Handler:    _UgcLfmRecommendation_GetPostsWithFilter_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ugc/recommendation_lfm.proto",
}

func init() {
	proto.RegisterFile("ugc/recommendation_lfm.proto", fileDescriptor_recommendation_lfm_a1476ef0757e6028)
}

var fileDescriptor_recommendation_lfm_a1476ef0757e6028 = []byte{
	// 563 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x54, 0x51, 0x6f, 0x12, 0x41,
	0x10, 0xf6, 0x80, 0x5e, 0x61, 0xa8, 0x14, 0xd7, 0x16, 0x91, 0x18, 0x43, 0x2e, 0xd1, 0xf2, 0xe2,
	0x91, 0x1c, 0xd1, 0x18, 0x13, 0x1f, 0x50, 0xdb, 0x4a, 0x2c, 0xb6, 0x59, 0xdb, 0x98, 0xf8, 0x20,
	0xc1, 0xdd, 0xe5, 0xd8, 0x84, 0xbb, 0x5d, 0x6f, 0x17, 0xa2, 0x2f, 0xfe, 0x27, 0x7f, 0x83, 0x7f,
	0xcc, 0xec, 0x1e, 0x2a, 0xc8, 0x1d, 0xa9, 0xf1, 0x6d, 0xf7, 0xe6, 0x9b, 0xf9, 0x66, 0xbe, 0xf9,
	0xf6, 0xe0, 0xde, 0x3c, 0x24, 0xdd, 0x84, 0x11, 0x11, 0x45, 0x2c, 0xa6, 0x63, 0xcd, 0x45, 0x3c,
	0x9a, 0x4d, 0x22, 0x5f, 0x26, 0x42, 0x0b, 0xd4, 0x98, 0x87, 0xc4, 0xdf, 0x8c, 0x7a, 0x18, 0x0e,
	0xfb, 0x94, 0x5e, 0x29, 0x96, 0x60, 0x36, 0xa6, 0xaf, 0xb9, 0xd2, 0x22, 0xf9, 0x8a, 0xd9, 0x67,
	0x74, 0x07, 0x76, 0xe7, 0x8a, 0x25, 0x23, 0x4e, 0x9b, 0x4e, 0xdb, 0xe9, 0xdc, 0xc4, 0xae, 0xb9,
	0x0e, 0x28, 0x6a, 0xc3, 0x9e, 0x14, 0x4a, 0x8f, 0x38, 0x1d, 0xcd, 0xb8, 0xd2, 0xcd, 0x42, 0xbb,
	0xd8, 0xa9, 0x60, 0x30, 0xdf, 0x06, 0xf4, 0x8c, 0x2b, 0xed, 0x35, 0xa1, 0x91, 0x55, 0x53, 0x49,
	0x6f, 0x01, 0x0d, 0xbc, 0xd6, 0xc3, 0x85, 0xc9, 0x8a, 0x27, 0xc2, 0xd0, 0x2d, 0xab, 0x5a, 0xba,
	0x0a, 0x76, 0xd3, 0x82, 0xe8, 0x2e, 0x94, 0xb5, 0x90, 0x9c, 0x98, 0x48, 0xc1, 0x46, 0x76, 0xed,
	0x7d, 0x40, 0xd1, 0x01, 0xec, 0x28, 0x22, 0x12, 0xd6, 0x2c, 0xda, 0x06, 0xd3, 0x0b, 0x42, 0x50,
	0x9a, 0xf2, 0x58, 0x37, 0x4b, 0x16, 0x6c, 0xcf, 0x5e, 0x08, 0xfb, 0x7d, 0x4a, 0x0d, 0x99, 0x1a,
	0xc4, 0x5a, 0x98, 0xf9, 0x2e, 0xa1, 0x96, 0x12, 0xc6, 0x13, 0x91, 0x0e, 0xe2, 0xb4, 0x8b, 0x9d,
	0x6a, 0xe0, 0xfb, 0xd9, 0x4a, 0xf9, 0xd9, 0x8d, 0x63, 0x2b, 0x86, 0x39, 0xd9, 0xd1, 0x11, 0xd4,
	0xd7, 0x89, 0x94, 0xf4, 0x02, 0xa8, 0x61, 0x16, 0x89, 0x05, 0xb3, 0x9f, 0x0d, 0xf7, 0xdf, 0x12,
	0x3a, 0x1b, 0x12, 0xde, 0x82, 0xfd, 0xb5, 0x1c, 0x25, 0xbd, 0xef, 0x0e, 0xb8, 0x27, 0x7c, 0xa6,
	0x59, 0x82, 0x9e, 0x83, 0x1b, 0x31, 0x3d, 0x15, 0xa9, 0x56, 0xb5, 0xe0, 0x41, 0x5e, 0xcf, 0x29,
	0xde, 0x1f, 0x5a, 0x30, 0x5e, 0x26, 0xa1, 0x1e, 0x34, 0xd8, 0x17, 0xc2, 0xa4, 0x85, 0x65, 0xec,
	0xf2, 0xf6, 0xef, 0xe8, 0xc5, 0x9f, 0x8e, 0x7a, 0xe0, 0xa6, 0x65, 0x10, 0x80, 0x8b, 0xfb, 0x6f,
	0x5f, 0x9d, 0x0f, 0xeb, 0x37, 0x50, 0x19, 0x4a, 0x97, 0x83, 0xe1, 0x71, 0xdd, 0xb1, 0xa7, 0xfe,
	0xe9, 0xbb, 0x7a, 0x01, 0x55, 0x60, 0xe7, 0xe4, 0x1c, 0xbf, 0x3c, 0xae, 0x17, 0xbd, 0x6f, 0x70,
	0x78, 0xca, 0xb4, 0x9d, 0xe1, 0x3d, 0xd7, 0xd3, 0xb4, 0x9d, 0xad, 0xee, 0x7a, 0x02, 0xee, 0xc4,
	0xa2, 0xec, 0xb2, 0xab, 0xc1, 0xfd, 0xed, 0xa3, 0xe1, 0x25, 0xda, 0x78, 0x81, 0x88, 0x79, 0xac,
	0x7f, 0x79, 0xc1, 0x5e, 0x3c, 0x06, 0x8d, 0x2c, 0x7e, 0x25, 0xd1, 0x1b, 0xa8, 0xd8, 0xc9, 0xff,
	0x63, 0xf3, 0x65, 0x53, 0xc0, 0x68, 0x13, 0xfc, 0x28, 0xc2, 0xc1, 0x55, 0x48, 0xce, 0x26, 0xd1,
	0x3a, 0x14, 0x29, 0x40, 0x9b, 0x2f, 0x01, 0x3d, 0xca, 0x23, 0xca, 0x7c, 0x89, 0x2d, 0xff, 0x5f,
	0xe0, 0x4a, 0xa2, 0x31, 0xec, 0xad, 0x7a, 0x10, 0x1d, 0x6d, 0xc9, 0x5f, 0x7d, 0x12, 0xad, 0xce,
	0xf5, 0x80, 0x4a, 0xa2, 0x8f, 0x50, 0x5d, 0xb1, 0x27, 0x7a, 0x98, 0xaf, 0xdc, 0xaa, 0xef, 0x5b,
	0x47, 0xd7, 0xc2, 0x29, 0x69, 0x74, 0xdb, 0xdc, 0x5b, 0xbe, 0x6e, 0x99, 0x1e, 0xcb, 0xd7, 0x2d,
	0xdb, 0x12, 0x2f, 0x9e, 0x7d, 0x78, 0x1a, 0x8a, 0xd9, 0x38, 0x0e, 0xfd, 0xc7, 0x81, 0xd6, 0x3e,
	0x11, 0x51, 0xd7, 0xfe, 0x3b, 0x89, 0x98, 0x75, 0x15, 0x4b, 0x16, 0x9c, 0x30, 0xd5, 0xcd, 0xfe,
	0xc9, 0x7e, 0x72, 0x2d, 0xb2, 0xf7, 0x33, 0x00, 0x00, 0xff, 0xff, 0xa7, 0x25, 0xd8, 0xe4, 0x85,
	0x05, 0x00, 0x00,
}
