// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ugc/non_public_content.proto

package non_public_content // import "golang.52tt.com/protocol/services/ugc/non_public_content"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import content "golang.52tt.com/protocol/services/ugc/content"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type DistributePostFilterType int32

const (
	DistributePostFilterType_FILTER_NONE        DistributePostFilterType = 0
	DistributePostFilterType_FILTER_HOT_POST    DistributePostFilterType = 1
	DistributePostFilterType_FILTER_INSERT_POST DistributePostFilterType = 2
)

var DistributePostFilterType_name = map[int32]string{
	0: "FILTER_NONE",
	1: "FILTER_HOT_POST",
	2: "FILTER_INSERT_POST",
}
var DistributePostFilterType_value = map[string]int32{
	"FILTER_NONE":        0,
	"FILTER_HOT_POST":    1,
	"FILTER_INSERT_POST": 2,
}

func (x DistributePostFilterType) String() string {
	return proto.EnumName(DistributePostFilterType_name, int32(x))
}
func (DistributePostFilterType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{0}
}

type NonPublicPostHideStatus int32

const (
	NonPublicPostHideStatus_HIDE_STATUS_NONE NonPublicPostHideStatus = 0
	NonPublicPostHideStatus_NOT_HIDE         NonPublicPostHideStatus = 1
	NonPublicPostHideStatus_POST_HIDE        NonPublicPostHideStatus = 2
)

var NonPublicPostHideStatus_name = map[int32]string{
	0: "HIDE_STATUS_NONE",
	1: "NOT_HIDE",
	2: "POST_HIDE",
}
var NonPublicPostHideStatus_value = map[string]int32{
	"HIDE_STATUS_NONE": 0,
	"NOT_HIDE":         1,
	"POST_HIDE":        2,
}

func (x NonPublicPostHideStatus) String() string {
	return proto.EnumName(NonPublicPostHideStatus_name, int32(x))
}
func (NonPublicPostHideStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{1}
}

type ForceInsertStatus int32

const (
	ForceInsertStatus_FORCE_INSERT_NONE    ForceInsertStatus = 0
	ForceInsertStatus_FORCE_INSERT_NOT_YET ForceInsertStatus = 1
	ForceInsertStatus_FORCE_INSERT_VALID   ForceInsertStatus = 2
	ForceInsertStatus_FORCE_INSERT_EXPIRED ForceInsertStatus = 3
)

var ForceInsertStatus_name = map[int32]string{
	0: "FORCE_INSERT_NONE",
	1: "FORCE_INSERT_NOT_YET",
	2: "FORCE_INSERT_VALID",
	3: "FORCE_INSERT_EXPIRED",
}
var ForceInsertStatus_value = map[string]int32{
	"FORCE_INSERT_NONE":    0,
	"FORCE_INSERT_NOT_YET": 1,
	"FORCE_INSERT_VALID":   2,
	"FORCE_INSERT_EXPIRED": 3,
}

func (x ForceInsertStatus) String() string {
	return proto.EnumName(ForceInsertStatus_name, int32(x))
}
func (ForceInsertStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{2}
}

type AttachmentInfo_AttachmentType int32

const (
	AttachmentInfo_NONE  AttachmentInfo_AttachmentType = 0
	AttachmentInfo_IMAGE AttachmentInfo_AttachmentType = 1
	AttachmentInfo_GIF   AttachmentInfo_AttachmentType = 2
	AttachmentInfo_VIDEO AttachmentInfo_AttachmentType = 3
	AttachmentInfo_CMS   AttachmentInfo_AttachmentType = 4
	AttachmentInfo_AUDIO AttachmentInfo_AttachmentType = 5
	AttachmentInfo_TEXT  AttachmentInfo_AttachmentType = 6
)

var AttachmentInfo_AttachmentType_name = map[int32]string{
	0: "NONE",
	1: "IMAGE",
	2: "GIF",
	3: "VIDEO",
	4: "CMS",
	5: "AUDIO",
	6: "TEXT",
}
var AttachmentInfo_AttachmentType_value = map[string]int32{
	"NONE":  0,
	"IMAGE": 1,
	"GIF":   2,
	"VIDEO": 3,
	"CMS":   4,
	"AUDIO": 5,
	"TEXT":  6,
}

func (x AttachmentInfo_AttachmentType) String() string {
	return proto.EnumName(AttachmentInfo_AttachmentType_name, int32(x))
}
func (AttachmentInfo_AttachmentType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{40, 0}
}

type UpsertNonPublicHotCommentReq struct {
	SceneId              string   `protobuf:"bytes,1,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	PostId               string   `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CommentId            string   `protobuf:"bytes,3,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	Score                int64    `protobuf:"varint,4,opt,name=score,proto3" json:"score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertNonPublicHotCommentReq) Reset()         { *m = UpsertNonPublicHotCommentReq{} }
func (m *UpsertNonPublicHotCommentReq) String() string { return proto.CompactTextString(m) }
func (*UpsertNonPublicHotCommentReq) ProtoMessage()    {}
func (*UpsertNonPublicHotCommentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{0}
}
func (m *UpsertNonPublicHotCommentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertNonPublicHotCommentReq.Unmarshal(m, b)
}
func (m *UpsertNonPublicHotCommentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertNonPublicHotCommentReq.Marshal(b, m, deterministic)
}
func (dst *UpsertNonPublicHotCommentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertNonPublicHotCommentReq.Merge(dst, src)
}
func (m *UpsertNonPublicHotCommentReq) XXX_Size() int {
	return xxx_messageInfo_UpsertNonPublicHotCommentReq.Size(m)
}
func (m *UpsertNonPublicHotCommentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertNonPublicHotCommentReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertNonPublicHotCommentReq proto.InternalMessageInfo

func (m *UpsertNonPublicHotCommentReq) GetSceneId() string {
	if m != nil {
		return m.SceneId
	}
	return ""
}

func (m *UpsertNonPublicHotCommentReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *UpsertNonPublicHotCommentReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *UpsertNonPublicHotCommentReq) GetScore() int64 {
	if m != nil {
		return m.Score
	}
	return 0
}

type UpsertNonPublicHotCommentResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertNonPublicHotCommentResp) Reset()         { *m = UpsertNonPublicHotCommentResp{} }
func (m *UpsertNonPublicHotCommentResp) String() string { return proto.CompactTextString(m) }
func (*UpsertNonPublicHotCommentResp) ProtoMessage()    {}
func (*UpsertNonPublicHotCommentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{1}
}
func (m *UpsertNonPublicHotCommentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertNonPublicHotCommentResp.Unmarshal(m, b)
}
func (m *UpsertNonPublicHotCommentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertNonPublicHotCommentResp.Marshal(b, m, deterministic)
}
func (dst *UpsertNonPublicHotCommentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertNonPublicHotCommentResp.Merge(dst, src)
}
func (m *UpsertNonPublicHotCommentResp) XXX_Size() int {
	return xxx_messageInfo_UpsertNonPublicHotCommentResp.Size(m)
}
func (m *UpsertNonPublicHotCommentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertNonPublicHotCommentResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertNonPublicHotCommentResp proto.InternalMessageInfo

type AddNonPublicAttitudeReq struct {
	OriginSceneStream     *SceneStream `protobuf:"bytes,1,opt,name=origin_scene_stream,json=originSceneStream,proto3" json:"origin_scene_stream,omitempty"`
	PostId                string       `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CommentId             string       `protobuf:"bytes,3,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	UserId                uint32       `protobuf:"varint,4,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	AttitudeType          uint32       `protobuf:"varint,5,opt,name=attitude_type,json=attitudeType,proto3" json:"attitude_type,omitempty"`
	IsFirstTime           bool         `protobuf:"varint,6,opt,name=is_first_time,json=isFirstTime,proto3" json:"is_first_time,omitempty"`
	TargetUserId          uint32       `protobuf:"varint,7,opt,name=target_user_id,json=targetUserId,proto3" json:"target_user_id,omitempty"`
	Source                uint32       `protobuf:"varint,8,opt,name=source,proto3" json:"source,omitempty"`
	StepType              uint32       `protobuf:"varint,9,opt,name=step_type,json=stepType,proto3" json:"step_type,omitempty"`
	SceneId               string       `protobuf:"bytes,10,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	UserBelongIdForReport string       `protobuf:"bytes,11,opt,name=user_belong_id_for_report,json=userBelongIdForReport,proto3" json:"user_belong_id_for_report,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}     `json:"-"`
	XXX_unrecognized      []byte       `json:"-"`
	XXX_sizecache         int32        `json:"-"`
}

func (m *AddNonPublicAttitudeReq) Reset()         { *m = AddNonPublicAttitudeReq{} }
func (m *AddNonPublicAttitudeReq) String() string { return proto.CompactTextString(m) }
func (*AddNonPublicAttitudeReq) ProtoMessage()    {}
func (*AddNonPublicAttitudeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{2}
}
func (m *AddNonPublicAttitudeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddNonPublicAttitudeReq.Unmarshal(m, b)
}
func (m *AddNonPublicAttitudeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddNonPublicAttitudeReq.Marshal(b, m, deterministic)
}
func (dst *AddNonPublicAttitudeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddNonPublicAttitudeReq.Merge(dst, src)
}
func (m *AddNonPublicAttitudeReq) XXX_Size() int {
	return xxx_messageInfo_AddNonPublicAttitudeReq.Size(m)
}
func (m *AddNonPublicAttitudeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddNonPublicAttitudeReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddNonPublicAttitudeReq proto.InternalMessageInfo

func (m *AddNonPublicAttitudeReq) GetOriginSceneStream() *SceneStream {
	if m != nil {
		return m.OriginSceneStream
	}
	return nil
}

func (m *AddNonPublicAttitudeReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *AddNonPublicAttitudeReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *AddNonPublicAttitudeReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *AddNonPublicAttitudeReq) GetAttitudeType() uint32 {
	if m != nil {
		return m.AttitudeType
	}
	return 0
}

func (m *AddNonPublicAttitudeReq) GetIsFirstTime() bool {
	if m != nil {
		return m.IsFirstTime
	}
	return false
}

func (m *AddNonPublicAttitudeReq) GetTargetUserId() uint32 {
	if m != nil {
		return m.TargetUserId
	}
	return 0
}

func (m *AddNonPublicAttitudeReq) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *AddNonPublicAttitudeReq) GetStepType() uint32 {
	if m != nil {
		return m.StepType
	}
	return 0
}

func (m *AddNonPublicAttitudeReq) GetSceneId() string {
	if m != nil {
		return m.SceneId
	}
	return ""
}

func (m *AddNonPublicAttitudeReq) GetUserBelongIdForReport() string {
	if m != nil {
		return m.UserBelongIdForReport
	}
	return ""
}

type AddNonPublicAttitudeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddNonPublicAttitudeResp) Reset()         { *m = AddNonPublicAttitudeResp{} }
func (m *AddNonPublicAttitudeResp) String() string { return proto.CompactTextString(m) }
func (*AddNonPublicAttitudeResp) ProtoMessage()    {}
func (*AddNonPublicAttitudeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{3}
}
func (m *AddNonPublicAttitudeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddNonPublicAttitudeResp.Unmarshal(m, b)
}
func (m *AddNonPublicAttitudeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddNonPublicAttitudeResp.Marshal(b, m, deterministic)
}
func (dst *AddNonPublicAttitudeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddNonPublicAttitudeResp.Merge(dst, src)
}
func (m *AddNonPublicAttitudeResp) XXX_Size() int {
	return xxx_messageInfo_AddNonPublicAttitudeResp.Size(m)
}
func (m *AddNonPublicAttitudeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddNonPublicAttitudeResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddNonPublicAttitudeResp proto.InternalMessageInfo

type DeleteNonPublicHotCommentReq struct {
	SceneId              string   `protobuf:"bytes,1,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	PostId               string   `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CommentId            string   `protobuf:"bytes,3,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteNonPublicHotCommentReq) Reset()         { *m = DeleteNonPublicHotCommentReq{} }
func (m *DeleteNonPublicHotCommentReq) String() string { return proto.CompactTextString(m) }
func (*DeleteNonPublicHotCommentReq) ProtoMessage()    {}
func (*DeleteNonPublicHotCommentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{4}
}
func (m *DeleteNonPublicHotCommentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteNonPublicHotCommentReq.Unmarshal(m, b)
}
func (m *DeleteNonPublicHotCommentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteNonPublicHotCommentReq.Marshal(b, m, deterministic)
}
func (dst *DeleteNonPublicHotCommentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteNonPublicHotCommentReq.Merge(dst, src)
}
func (m *DeleteNonPublicHotCommentReq) XXX_Size() int {
	return xxx_messageInfo_DeleteNonPublicHotCommentReq.Size(m)
}
func (m *DeleteNonPublicHotCommentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteNonPublicHotCommentReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteNonPublicHotCommentReq proto.InternalMessageInfo

func (m *DeleteNonPublicHotCommentReq) GetSceneId() string {
	if m != nil {
		return m.SceneId
	}
	return ""
}

func (m *DeleteNonPublicHotCommentReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *DeleteNonPublicHotCommentReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

type DeleteNonPublicHotCommentResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteNonPublicHotCommentResp) Reset()         { *m = DeleteNonPublicHotCommentResp{} }
func (m *DeleteNonPublicHotCommentResp) String() string { return proto.CompactTextString(m) }
func (*DeleteNonPublicHotCommentResp) ProtoMessage()    {}
func (*DeleteNonPublicHotCommentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{5}
}
func (m *DeleteNonPublicHotCommentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteNonPublicHotCommentResp.Unmarshal(m, b)
}
func (m *DeleteNonPublicHotCommentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteNonPublicHotCommentResp.Marshal(b, m, deterministic)
}
func (dst *DeleteNonPublicHotCommentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteNonPublicHotCommentResp.Merge(dst, src)
}
func (m *DeleteNonPublicHotCommentResp) XXX_Size() int {
	return xxx_messageInfo_DeleteNonPublicHotCommentResp.Size(m)
}
func (m *DeleteNonPublicHotCommentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteNonPublicHotCommentResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteNonPublicHotCommentResp proto.InternalMessageInfo

type GetAndUpdateInsertPostsReq struct {
	PostIds              []string `protobuf:"bytes,1,rep,name=postIds,proto3" json:"postIds,omitempty"`
	CategoryId           string   `protobuf:"bytes,2,opt,name=categoryId,proto3" json:"categoryId,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAndUpdateInsertPostsReq) Reset()         { *m = GetAndUpdateInsertPostsReq{} }
func (m *GetAndUpdateInsertPostsReq) String() string { return proto.CompactTextString(m) }
func (*GetAndUpdateInsertPostsReq) ProtoMessage()    {}
func (*GetAndUpdateInsertPostsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{6}
}
func (m *GetAndUpdateInsertPostsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAndUpdateInsertPostsReq.Unmarshal(m, b)
}
func (m *GetAndUpdateInsertPostsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAndUpdateInsertPostsReq.Marshal(b, m, deterministic)
}
func (dst *GetAndUpdateInsertPostsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAndUpdateInsertPostsReq.Merge(dst, src)
}
func (m *GetAndUpdateInsertPostsReq) XXX_Size() int {
	return xxx_messageInfo_GetAndUpdateInsertPostsReq.Size(m)
}
func (m *GetAndUpdateInsertPostsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAndUpdateInsertPostsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAndUpdateInsertPostsReq proto.InternalMessageInfo

func (m *GetAndUpdateInsertPostsReq) GetPostIds() []string {
	if m != nil {
		return m.PostIds
	}
	return nil
}

func (m *GetAndUpdateInsertPostsReq) GetCategoryId() string {
	if m != nil {
		return m.CategoryId
	}
	return ""
}

type GetAndUpdateInsertPostsResp struct {
	PostIds              []string `protobuf:"bytes,1,rep,name=postIds,proto3" json:"postIds,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAndUpdateInsertPostsResp) Reset()         { *m = GetAndUpdateInsertPostsResp{} }
func (m *GetAndUpdateInsertPostsResp) String() string { return proto.CompactTextString(m) }
func (*GetAndUpdateInsertPostsResp) ProtoMessage()    {}
func (*GetAndUpdateInsertPostsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{7}
}
func (m *GetAndUpdateInsertPostsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAndUpdateInsertPostsResp.Unmarshal(m, b)
}
func (m *GetAndUpdateInsertPostsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAndUpdateInsertPostsResp.Marshal(b, m, deterministic)
}
func (dst *GetAndUpdateInsertPostsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAndUpdateInsertPostsResp.Merge(dst, src)
}
func (m *GetAndUpdateInsertPostsResp) XXX_Size() int {
	return xxx_messageInfo_GetAndUpdateInsertPostsResp.Size(m)
}
func (m *GetAndUpdateInsertPostsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAndUpdateInsertPostsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAndUpdateInsertPostsResp proto.InternalMessageInfo

func (m *GetAndUpdateInsertPostsResp) GetPostIds() []string {
	if m != nil {
		return m.PostIds
	}
	return nil
}

type NonPublicAttachment struct {
	Key       string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Type      uint32 `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	Content   string `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	Extra     string `protobuf:"bytes,4,opt,name=extra,proto3" json:"extra,omitempty"`
	Status    uint32 `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	VmContent string `protobuf:"bytes,6,opt,name=vm_content,json=vmContent,proto3" json:"vm_content,omitempty"`
	// 给type == video用的, 转码参数
	Param string `protobuf:"bytes,10,opt,name=param,proto3" json:"param,omitempty"`
	// 原始视频封面url
	OriginVideoCover     string   `protobuf:"bytes,11,opt,name=origin_video_cover,json=originVideoCover,proto3" json:"origin_video_cover,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NonPublicAttachment) Reset()         { *m = NonPublicAttachment{} }
func (m *NonPublicAttachment) String() string { return proto.CompactTextString(m) }
func (*NonPublicAttachment) ProtoMessage()    {}
func (*NonPublicAttachment) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{8}
}
func (m *NonPublicAttachment) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NonPublicAttachment.Unmarshal(m, b)
}
func (m *NonPublicAttachment) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NonPublicAttachment.Marshal(b, m, deterministic)
}
func (dst *NonPublicAttachment) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NonPublicAttachment.Merge(dst, src)
}
func (m *NonPublicAttachment) XXX_Size() int {
	return xxx_messageInfo_NonPublicAttachment.Size(m)
}
func (m *NonPublicAttachment) XXX_DiscardUnknown() {
	xxx_messageInfo_NonPublicAttachment.DiscardUnknown(m)
}

var xxx_messageInfo_NonPublicAttachment proto.InternalMessageInfo

func (m *NonPublicAttachment) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *NonPublicAttachment) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *NonPublicAttachment) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *NonPublicAttachment) GetExtra() string {
	if m != nil {
		return m.Extra
	}
	return ""
}

func (m *NonPublicAttachment) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *NonPublicAttachment) GetVmContent() string {
	if m != nil {
		return m.VmContent
	}
	return ""
}

func (m *NonPublicAttachment) GetParam() string {
	if m != nil {
		return m.Param
	}
	return ""
}

func (m *NonPublicAttachment) GetOriginVideoCover() string {
	if m != nil {
		return m.OriginVideoCover
	}
	return ""
}

type NonPublicRichTextWords struct {
	Text                 string   `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NonPublicRichTextWords) Reset()         { *m = NonPublicRichTextWords{} }
func (m *NonPublicRichTextWords) String() string { return proto.CompactTextString(m) }
func (*NonPublicRichTextWords) ProtoMessage()    {}
func (*NonPublicRichTextWords) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{9}
}
func (m *NonPublicRichTextWords) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NonPublicRichTextWords.Unmarshal(m, b)
}
func (m *NonPublicRichTextWords) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NonPublicRichTextWords.Marshal(b, m, deterministic)
}
func (dst *NonPublicRichTextWords) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NonPublicRichTextWords.Merge(dst, src)
}
func (m *NonPublicRichTextWords) XXX_Size() int {
	return xxx_messageInfo_NonPublicRichTextWords.Size(m)
}
func (m *NonPublicRichTextWords) XXX_DiscardUnknown() {
	xxx_messageInfo_NonPublicRichTextWords.DiscardUnknown(m)
}

var xxx_messageInfo_NonPublicRichTextWords proto.InternalMessageInfo

func (m *NonPublicRichTextWords) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

type NonPublicURLCard struct {
	ShowText             string   `protobuf:"bytes,1,opt,name=show_text,json=showText,proto3" json:"show_text,omitempty"`
	ShowIconUrl          string   `protobuf:"bytes,2,opt,name=show_icon_url,json=showIconUrl,proto3" json:"show_icon_url,omitempty"`
	ShowPlatformName     string   `protobuf:"bytes,3,opt,name=show_platform_name,json=showPlatformName,proto3" json:"show_platform_name,omitempty"`
	ShowPlatformIcon     string   `protobuf:"bytes,4,opt,name=show_platform_icon,json=showPlatformIcon,proto3" json:"show_platform_icon,omitempty"`
	RealUrlAddr          string   `protobuf:"bytes,5,opt,name=real_url_addr,json=realUrlAddr,proto3" json:"real_url_addr,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NonPublicURLCard) Reset()         { *m = NonPublicURLCard{} }
func (m *NonPublicURLCard) String() string { return proto.CompactTextString(m) }
func (*NonPublicURLCard) ProtoMessage()    {}
func (*NonPublicURLCard) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{10}
}
func (m *NonPublicURLCard) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NonPublicURLCard.Unmarshal(m, b)
}
func (m *NonPublicURLCard) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NonPublicURLCard.Marshal(b, m, deterministic)
}
func (dst *NonPublicURLCard) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NonPublicURLCard.Merge(dst, src)
}
func (m *NonPublicURLCard) XXX_Size() int {
	return xxx_messageInfo_NonPublicURLCard.Size(m)
}
func (m *NonPublicURLCard) XXX_DiscardUnknown() {
	xxx_messageInfo_NonPublicURLCard.DiscardUnknown(m)
}

var xxx_messageInfo_NonPublicURLCard proto.InternalMessageInfo

func (m *NonPublicURLCard) GetShowText() string {
	if m != nil {
		return m.ShowText
	}
	return ""
}

func (m *NonPublicURLCard) GetShowIconUrl() string {
	if m != nil {
		return m.ShowIconUrl
	}
	return ""
}

func (m *NonPublicURLCard) GetShowPlatformName() string {
	if m != nil {
		return m.ShowPlatformName
	}
	return ""
}

func (m *NonPublicURLCard) GetShowPlatformIcon() string {
	if m != nil {
		return m.ShowPlatformIcon
	}
	return ""
}

func (m *NonPublicURLCard) GetRealUrlAddr() string {
	if m != nil {
		return m.RealUrlAddr
	}
	return ""
}

type NonPublicRichTextElement struct {
	// Types that are valid to be assigned to Content:
	//	*NonPublicRichTextElement_Words
	//	*NonPublicRichTextElement_MultiMedia
	//	*NonPublicRichTextElement_UrlCard
	Content              isNonPublicRichTextElement_Content `protobuf_oneof:"content"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *NonPublicRichTextElement) Reset()         { *m = NonPublicRichTextElement{} }
func (m *NonPublicRichTextElement) String() string { return proto.CompactTextString(m) }
func (*NonPublicRichTextElement) ProtoMessage()    {}
func (*NonPublicRichTextElement) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{11}
}
func (m *NonPublicRichTextElement) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NonPublicRichTextElement.Unmarshal(m, b)
}
func (m *NonPublicRichTextElement) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NonPublicRichTextElement.Marshal(b, m, deterministic)
}
func (dst *NonPublicRichTextElement) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NonPublicRichTextElement.Merge(dst, src)
}
func (m *NonPublicRichTextElement) XXX_Size() int {
	return xxx_messageInfo_NonPublicRichTextElement.Size(m)
}
func (m *NonPublicRichTextElement) XXX_DiscardUnknown() {
	xxx_messageInfo_NonPublicRichTextElement.DiscardUnknown(m)
}

var xxx_messageInfo_NonPublicRichTextElement proto.InternalMessageInfo

type isNonPublicRichTextElement_Content interface {
	isNonPublicRichTextElement_Content()
}

type NonPublicRichTextElement_Words struct {
	Words *NonPublicRichTextWords `protobuf:"bytes,1,opt,name=words,proto3,oneof"`
}

type NonPublicRichTextElement_MultiMedia struct {
	MultiMedia *NonPublicAttachment `protobuf:"bytes,2,opt,name=multi_media,json=multiMedia,proto3,oneof"`
}

type NonPublicRichTextElement_UrlCard struct {
	UrlCard *NonPublicURLCard `protobuf:"bytes,3,opt,name=url_card,json=urlCard,proto3,oneof"`
}

func (*NonPublicRichTextElement_Words) isNonPublicRichTextElement_Content() {}

func (*NonPublicRichTextElement_MultiMedia) isNonPublicRichTextElement_Content() {}

func (*NonPublicRichTextElement_UrlCard) isNonPublicRichTextElement_Content() {}

func (m *NonPublicRichTextElement) GetContent() isNonPublicRichTextElement_Content {
	if m != nil {
		return m.Content
	}
	return nil
}

func (m *NonPublicRichTextElement) GetWords() *NonPublicRichTextWords {
	if x, ok := m.GetContent().(*NonPublicRichTextElement_Words); ok {
		return x.Words
	}
	return nil
}

func (m *NonPublicRichTextElement) GetMultiMedia() *NonPublicAttachment {
	if x, ok := m.GetContent().(*NonPublicRichTextElement_MultiMedia); ok {
		return x.MultiMedia
	}
	return nil
}

func (m *NonPublicRichTextElement) GetUrlCard() *NonPublicURLCard {
	if x, ok := m.GetContent().(*NonPublicRichTextElement_UrlCard); ok {
		return x.UrlCard
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*NonPublicRichTextElement) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _NonPublicRichTextElement_OneofMarshaler, _NonPublicRichTextElement_OneofUnmarshaler, _NonPublicRichTextElement_OneofSizer, []interface{}{
		(*NonPublicRichTextElement_Words)(nil),
		(*NonPublicRichTextElement_MultiMedia)(nil),
		(*NonPublicRichTextElement_UrlCard)(nil),
	}
}

func _NonPublicRichTextElement_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*NonPublicRichTextElement)
	// content
	switch x := m.Content.(type) {
	case *NonPublicRichTextElement_Words:
		b.EncodeVarint(1<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Words); err != nil {
			return err
		}
	case *NonPublicRichTextElement_MultiMedia:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.MultiMedia); err != nil {
			return err
		}
	case *NonPublicRichTextElement_UrlCard:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.UrlCard); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("NonPublicRichTextElement.Content has unexpected type %T", x)
	}
	return nil
}

func _NonPublicRichTextElement_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*NonPublicRichTextElement)
	switch tag {
	case 1: // content.words
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(NonPublicRichTextWords)
		err := b.DecodeMessage(msg)
		m.Content = &NonPublicRichTextElement_Words{msg}
		return true, err
	case 2: // content.multi_media
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(NonPublicAttachment)
		err := b.DecodeMessage(msg)
		m.Content = &NonPublicRichTextElement_MultiMedia{msg}
		return true, err
	case 3: // content.url_card
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(NonPublicURLCard)
		err := b.DecodeMessage(msg)
		m.Content = &NonPublicRichTextElement_UrlCard{msg}
		return true, err
	default:
		return false, nil
	}
}

func _NonPublicRichTextElement_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*NonPublicRichTextElement)
	// content
	switch x := m.Content.(type) {
	case *NonPublicRichTextElement_Words:
		s := proto.Size(x.Words)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *NonPublicRichTextElement_MultiMedia:
		s := proto.Size(x.MultiMedia)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *NonPublicRichTextElement_UrlCard:
		s := proto.Size(x.UrlCard)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type SceneStream struct {
	StreamId             string   `protobuf:"bytes,1,opt,name=stream_id,json=streamId,proto3" json:"stream_id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	StreamType           uint32   `protobuf:"varint,3,opt,name=stream_type,json=streamType,proto3" json:"stream_type,omitempty"`
	Opt                  uint32   `protobuf:"varint,4,opt,name=opt,proto3" json:"opt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SceneStream) Reset()         { *m = SceneStream{} }
func (m *SceneStream) String() string { return proto.CompactTextString(m) }
func (*SceneStream) ProtoMessage()    {}
func (*SceneStream) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{12}
}
func (m *SceneStream) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SceneStream.Unmarshal(m, b)
}
func (m *SceneStream) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SceneStream.Marshal(b, m, deterministic)
}
func (dst *SceneStream) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SceneStream.Merge(dst, src)
}
func (m *SceneStream) XXX_Size() int {
	return xxx_messageInfo_SceneStream.Size(m)
}
func (m *SceneStream) XXX_DiscardUnknown() {
	xxx_messageInfo_SceneStream.DiscardUnknown(m)
}

var xxx_messageInfo_SceneStream proto.InternalMessageInfo

func (m *SceneStream) GetStreamId() string {
	if m != nil {
		return m.StreamId
	}
	return ""
}

func (m *SceneStream) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SceneStream) GetStreamType() uint32 {
	if m != nil {
		return m.StreamType
	}
	return 0
}

func (m *SceneStream) GetOpt() uint32 {
	if m != nil {
		return m.Opt
	}
	return 0
}

// 发帖
type AddNonPublicPostReq struct {
	UserId            uint32                      `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	SceneId           string                      `protobuf:"bytes,2,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	SceneStreamList   []*SceneStream              `protobuf:"bytes,3,rep,name=scene_stream_list,json=sceneStreamList,proto3" json:"scene_stream_list,omitempty"`
	OriginSceneStream *SceneStream                `protobuf:"bytes,4,opt,name=origin_scene_stream,json=originSceneStream,proto3" json:"origin_scene_stream,omitempty"`
	Title             string                      `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`
	ContentList       []*NonPublicRichTextElement `protobuf:"bytes,6,rep,name=content_list,json=contentList,proto3" json:"content_list,omitempty"`
	Status            uint32                      `protobuf:"varint,7,opt,name=status,proto3" json:"status,omitempty"`
	PostOrigin        uint32                      `protobuf:"varint,8,opt,name=post_origin,json=postOrigin,proto3" json:"post_origin,omitempty"`
	Ttid              string                      `protobuf:"bytes,9,opt,name=ttid,proto3" json:"ttid,omitempty"`
	// 给数据中心用的
	DeviceId string `protobuf:"bytes,10,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	Platform uint32 `protobuf:"varint,11,opt,name=platform,proto3" json:"platform,omitempty"`
	MarketId uint32 `protobuf:"varint,12,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ClientIp uint32 `protobuf:"varint,13,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	// 发帖人的IP归属地
	IpLoc                *content.Location         `protobuf:"bytes,14,opt,name=ip_loc,json=ipLoc,proto3" json:"ip_loc,omitempty"`
	GeneralContents      []*content.GeneralContent `protobuf:"bytes,15,rep,name=general_contents,json=generalContents,proto3" json:"general_contents,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *AddNonPublicPostReq) Reset()         { *m = AddNonPublicPostReq{} }
func (m *AddNonPublicPostReq) String() string { return proto.CompactTextString(m) }
func (*AddNonPublicPostReq) ProtoMessage()    {}
func (*AddNonPublicPostReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{13}
}
func (m *AddNonPublicPostReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddNonPublicPostReq.Unmarshal(m, b)
}
func (m *AddNonPublicPostReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddNonPublicPostReq.Marshal(b, m, deterministic)
}
func (dst *AddNonPublicPostReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddNonPublicPostReq.Merge(dst, src)
}
func (m *AddNonPublicPostReq) XXX_Size() int {
	return xxx_messageInfo_AddNonPublicPostReq.Size(m)
}
func (m *AddNonPublicPostReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddNonPublicPostReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddNonPublicPostReq proto.InternalMessageInfo

func (m *AddNonPublicPostReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *AddNonPublicPostReq) GetSceneId() string {
	if m != nil {
		return m.SceneId
	}
	return ""
}

func (m *AddNonPublicPostReq) GetSceneStreamList() []*SceneStream {
	if m != nil {
		return m.SceneStreamList
	}
	return nil
}

func (m *AddNonPublicPostReq) GetOriginSceneStream() *SceneStream {
	if m != nil {
		return m.OriginSceneStream
	}
	return nil
}

func (m *AddNonPublicPostReq) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *AddNonPublicPostReq) GetContentList() []*NonPublicRichTextElement {
	if m != nil {
		return m.ContentList
	}
	return nil
}

func (m *AddNonPublicPostReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *AddNonPublicPostReq) GetPostOrigin() uint32 {
	if m != nil {
		return m.PostOrigin
	}
	return 0
}

func (m *AddNonPublicPostReq) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *AddNonPublicPostReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *AddNonPublicPostReq) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

func (m *AddNonPublicPostReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *AddNonPublicPostReq) GetClientIp() uint32 {
	if m != nil {
		return m.ClientIp
	}
	return 0
}

func (m *AddNonPublicPostReq) GetIpLoc() *content.Location {
	if m != nil {
		return m.IpLoc
	}
	return nil
}

func (m *AddNonPublicPostReq) GetGeneralContents() []*content.GeneralContent {
	if m != nil {
		return m.GeneralContents
	}
	return nil
}

type AddNonPublicPostResp struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Status               uint32   `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	PostCreateAt         uint64   `protobuf:"varint,3,opt,name=post_create_at,json=postCreateAt,proto3" json:"post_create_at,omitempty"`
	AttachmentImageKeys  []string `protobuf:"bytes,10,rep,name=attachment_image_keys,json=attachmentImageKeys,proto3" json:"attachment_image_keys,omitempty"`
	AttachmentVideoKeys  []string `protobuf:"bytes,11,rep,name=attachment_video_keys,json=attachmentVideoKeys,proto3" json:"attachment_video_keys,omitempty"`
	ImageUploadToken     string   `protobuf:"bytes,12,opt,name=image_upload_token,json=imageUploadToken,proto3" json:"image_upload_token,omitempty"`
	VideoUploadToken     string   `protobuf:"bytes,13,opt,name=video_upload_token,json=videoUploadToken,proto3" json:"video_upload_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddNonPublicPostResp) Reset()         { *m = AddNonPublicPostResp{} }
func (m *AddNonPublicPostResp) String() string { return proto.CompactTextString(m) }
func (*AddNonPublicPostResp) ProtoMessage()    {}
func (*AddNonPublicPostResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{14}
}
func (m *AddNonPublicPostResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddNonPublicPostResp.Unmarshal(m, b)
}
func (m *AddNonPublicPostResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddNonPublicPostResp.Marshal(b, m, deterministic)
}
func (dst *AddNonPublicPostResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddNonPublicPostResp.Merge(dst, src)
}
func (m *AddNonPublicPostResp) XXX_Size() int {
	return xxx_messageInfo_AddNonPublicPostResp.Size(m)
}
func (m *AddNonPublicPostResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddNonPublicPostResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddNonPublicPostResp proto.InternalMessageInfo

func (m *AddNonPublicPostResp) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *AddNonPublicPostResp) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *AddNonPublicPostResp) GetPostCreateAt() uint64 {
	if m != nil {
		return m.PostCreateAt
	}
	return 0
}

func (m *AddNonPublicPostResp) GetAttachmentImageKeys() []string {
	if m != nil {
		return m.AttachmentImageKeys
	}
	return nil
}

func (m *AddNonPublicPostResp) GetAttachmentVideoKeys() []string {
	if m != nil {
		return m.AttachmentVideoKeys
	}
	return nil
}

func (m *AddNonPublicPostResp) GetImageUploadToken() string {
	if m != nil {
		return m.ImageUploadToken
	}
	return ""
}

func (m *AddNonPublicPostResp) GetVideoUploadToken() string {
	if m != nil {
		return m.VideoUploadToken
	}
	return ""
}

// 发帖
type AddNonPublicPostDirectlyReq struct {
	UserId            uint32                      `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	SceneId           string                      `protobuf:"bytes,2,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	SceneStreamList   []*SceneStream              `protobuf:"bytes,3,rep,name=scene_stream_list,json=sceneStreamList,proto3" json:"scene_stream_list,omitempty"`
	OriginSceneStream *SceneStream                `protobuf:"bytes,4,opt,name=origin_scene_stream,json=originSceneStream,proto3" json:"origin_scene_stream,omitempty"`
	Title             string                      `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`
	ContentList       []*NonPublicRichTextElement `protobuf:"bytes,6,rep,name=content_list,json=contentList,proto3" json:"content_list,omitempty"`
	Status            uint32                      `protobuf:"varint,7,opt,name=status,proto3" json:"status,omitempty"`
	PostOrigin        uint32                      `protobuf:"varint,8,opt,name=post_origin,json=postOrigin,proto3" json:"post_origin,omitempty"`
	Ttid              string                      `protobuf:"bytes,9,opt,name=ttid,proto3" json:"ttid,omitempty"`
	// 给数据中心用的
	Platform uint32 `protobuf:"varint,10,opt,name=platform,proto3" json:"platform,omitempty"`
	MarketId uint32 `protobuf:"varint,11,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ClientIp uint32 `protobuf:"varint,12,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	// 发帖人的IP归属地
	IpLoc                *content.Location         `protobuf:"bytes,13,opt,name=ip_loc,json=ipLoc,proto3" json:"ip_loc,omitempty"`
	GeneralContents      []*content.GeneralContent `protobuf:"bytes,14,rep,name=general_contents,json=generalContents,proto3" json:"general_contents,omitempty"`
	IsPostOnce           bool                      `protobuf:"varint,15,opt,name=is_post_once,json=isPostOnce,proto3" json:"is_post_once,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *AddNonPublicPostDirectlyReq) Reset()         { *m = AddNonPublicPostDirectlyReq{} }
func (m *AddNonPublicPostDirectlyReq) String() string { return proto.CompactTextString(m) }
func (*AddNonPublicPostDirectlyReq) ProtoMessage()    {}
func (*AddNonPublicPostDirectlyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{15}
}
func (m *AddNonPublicPostDirectlyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddNonPublicPostDirectlyReq.Unmarshal(m, b)
}
func (m *AddNonPublicPostDirectlyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddNonPublicPostDirectlyReq.Marshal(b, m, deterministic)
}
func (dst *AddNonPublicPostDirectlyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddNonPublicPostDirectlyReq.Merge(dst, src)
}
func (m *AddNonPublicPostDirectlyReq) XXX_Size() int {
	return xxx_messageInfo_AddNonPublicPostDirectlyReq.Size(m)
}
func (m *AddNonPublicPostDirectlyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddNonPublicPostDirectlyReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddNonPublicPostDirectlyReq proto.InternalMessageInfo

func (m *AddNonPublicPostDirectlyReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *AddNonPublicPostDirectlyReq) GetSceneId() string {
	if m != nil {
		return m.SceneId
	}
	return ""
}

func (m *AddNonPublicPostDirectlyReq) GetSceneStreamList() []*SceneStream {
	if m != nil {
		return m.SceneStreamList
	}
	return nil
}

func (m *AddNonPublicPostDirectlyReq) GetOriginSceneStream() *SceneStream {
	if m != nil {
		return m.OriginSceneStream
	}
	return nil
}

func (m *AddNonPublicPostDirectlyReq) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *AddNonPublicPostDirectlyReq) GetContentList() []*NonPublicRichTextElement {
	if m != nil {
		return m.ContentList
	}
	return nil
}

func (m *AddNonPublicPostDirectlyReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *AddNonPublicPostDirectlyReq) GetPostOrigin() uint32 {
	if m != nil {
		return m.PostOrigin
	}
	return 0
}

func (m *AddNonPublicPostDirectlyReq) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *AddNonPublicPostDirectlyReq) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

func (m *AddNonPublicPostDirectlyReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *AddNonPublicPostDirectlyReq) GetClientIp() uint32 {
	if m != nil {
		return m.ClientIp
	}
	return 0
}

func (m *AddNonPublicPostDirectlyReq) GetIpLoc() *content.Location {
	if m != nil {
		return m.IpLoc
	}
	return nil
}

func (m *AddNonPublicPostDirectlyReq) GetGeneralContents() []*content.GeneralContent {
	if m != nil {
		return m.GeneralContents
	}
	return nil
}

func (m *AddNonPublicPostDirectlyReq) GetIsPostOnce() bool {
	if m != nil {
		return m.IsPostOnce
	}
	return false
}

type AddNonPublicPostDirectlyResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddNonPublicPostDirectlyResp) Reset()         { *m = AddNonPublicPostDirectlyResp{} }
func (m *AddNonPublicPostDirectlyResp) String() string { return proto.CompactTextString(m) }
func (*AddNonPublicPostDirectlyResp) ProtoMessage()    {}
func (*AddNonPublicPostDirectlyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{16}
}
func (m *AddNonPublicPostDirectlyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddNonPublicPostDirectlyResp.Unmarshal(m, b)
}
func (m *AddNonPublicPostDirectlyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddNonPublicPostDirectlyResp.Marshal(b, m, deterministic)
}
func (dst *AddNonPublicPostDirectlyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddNonPublicPostDirectlyResp.Merge(dst, src)
}
func (m *AddNonPublicPostDirectlyResp) XXX_Size() int {
	return xxx_messageInfo_AddNonPublicPostDirectlyResp.Size(m)
}
func (m *AddNonPublicPostDirectlyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddNonPublicPostDirectlyResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddNonPublicPostDirectlyResp proto.InternalMessageInfo

// 更新帖子的update time
type UpdateNonPublicPostUpdateTimeReq struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,2,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateNonPublicPostUpdateTimeReq) Reset()         { *m = UpdateNonPublicPostUpdateTimeReq{} }
func (m *UpdateNonPublicPostUpdateTimeReq) String() string { return proto.CompactTextString(m) }
func (*UpdateNonPublicPostUpdateTimeReq) ProtoMessage()    {}
func (*UpdateNonPublicPostUpdateTimeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{17}
}
func (m *UpdateNonPublicPostUpdateTimeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateNonPublicPostUpdateTimeReq.Unmarshal(m, b)
}
func (m *UpdateNonPublicPostUpdateTimeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateNonPublicPostUpdateTimeReq.Marshal(b, m, deterministic)
}
func (dst *UpdateNonPublicPostUpdateTimeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateNonPublicPostUpdateTimeReq.Merge(dst, src)
}
func (m *UpdateNonPublicPostUpdateTimeReq) XXX_Size() int {
	return xxx_messageInfo_UpdateNonPublicPostUpdateTimeReq.Size(m)
}
func (m *UpdateNonPublicPostUpdateTimeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateNonPublicPostUpdateTimeReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateNonPublicPostUpdateTimeReq proto.InternalMessageInfo

func (m *UpdateNonPublicPostUpdateTimeReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *UpdateNonPublicPostUpdateTimeReq) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

type UpdateNonPublicPostUpdateTimeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateNonPublicPostUpdateTimeResp) Reset()         { *m = UpdateNonPublicPostUpdateTimeResp{} }
func (m *UpdateNonPublicPostUpdateTimeResp) String() string { return proto.CompactTextString(m) }
func (*UpdateNonPublicPostUpdateTimeResp) ProtoMessage()    {}
func (*UpdateNonPublicPostUpdateTimeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{18}
}
func (m *UpdateNonPublicPostUpdateTimeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateNonPublicPostUpdateTimeResp.Unmarshal(m, b)
}
func (m *UpdateNonPublicPostUpdateTimeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateNonPublicPostUpdateTimeResp.Marshal(b, m, deterministic)
}
func (dst *UpdateNonPublicPostUpdateTimeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateNonPublicPostUpdateTimeResp.Merge(dst, src)
}
func (m *UpdateNonPublicPostUpdateTimeResp) XXX_Size() int {
	return xxx_messageInfo_UpdateNonPublicPostUpdateTimeResp.Size(m)
}
func (m *UpdateNonPublicPostUpdateTimeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateNonPublicPostUpdateTimeResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateNonPublicPostUpdateTimeResp proto.InternalMessageInfo

type GetSceneStreamPostRecordReq struct {
	Scene                string   `protobuf:"bytes,1,opt,name=scene,proto3" json:"scene,omitempty"`
	StreamId             string   `protobuf:"bytes,2,opt,name=stream_id,json=streamId,proto3" json:"stream_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSceneStreamPostRecordReq) Reset()         { *m = GetSceneStreamPostRecordReq{} }
func (m *GetSceneStreamPostRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetSceneStreamPostRecordReq) ProtoMessage()    {}
func (*GetSceneStreamPostRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{19}
}
func (m *GetSceneStreamPostRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSceneStreamPostRecordReq.Unmarshal(m, b)
}
func (m *GetSceneStreamPostRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSceneStreamPostRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetSceneStreamPostRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSceneStreamPostRecordReq.Merge(dst, src)
}
func (m *GetSceneStreamPostRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetSceneStreamPostRecordReq.Size(m)
}
func (m *GetSceneStreamPostRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSceneStreamPostRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSceneStreamPostRecordReq proto.InternalMessageInfo

func (m *GetSceneStreamPostRecordReq) GetScene() string {
	if m != nil {
		return m.Scene
	}
	return ""
}

func (m *GetSceneStreamPostRecordReq) GetStreamId() string {
	if m != nil {
		return m.StreamId
	}
	return ""
}

type GetSceneStreamPostRecordResp struct {
	HasRecord            bool     `protobuf:"varint,1,opt,name=has_record,json=hasRecord,proto3" json:"has_record,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSceneStreamPostRecordResp) Reset()         { *m = GetSceneStreamPostRecordResp{} }
func (m *GetSceneStreamPostRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetSceneStreamPostRecordResp) ProtoMessage()    {}
func (*GetSceneStreamPostRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{20}
}
func (m *GetSceneStreamPostRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSceneStreamPostRecordResp.Unmarshal(m, b)
}
func (m *GetSceneStreamPostRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSceneStreamPostRecordResp.Marshal(b, m, deterministic)
}
func (dst *GetSceneStreamPostRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSceneStreamPostRecordResp.Merge(dst, src)
}
func (m *GetSceneStreamPostRecordResp) XXX_Size() int {
	return xxx_messageInfo_GetSceneStreamPostRecordResp.Size(m)
}
func (m *GetSceneStreamPostRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSceneStreamPostRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSceneStreamPostRecordResp proto.InternalMessageInfo

func (m *GetSceneStreamPostRecordResp) GetHasRecord() bool {
	if m != nil {
		return m.HasRecord
	}
	return false
}

// 更新帖子/评论 的附件的状态
type NonPublicMarkAttachmentUploadedReq struct {
	PostId               string                 `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CommentId            string                 `protobuf:"bytes,2,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	AttachmentInfoList   []*NonPublicAttachment `protobuf:"bytes,3,rep,name=attachment_info_list,json=attachmentInfoList,proto3" json:"attachment_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *NonPublicMarkAttachmentUploadedReq) Reset()         { *m = NonPublicMarkAttachmentUploadedReq{} }
func (m *NonPublicMarkAttachmentUploadedReq) String() string { return proto.CompactTextString(m) }
func (*NonPublicMarkAttachmentUploadedReq) ProtoMessage()    {}
func (*NonPublicMarkAttachmentUploadedReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{21}
}
func (m *NonPublicMarkAttachmentUploadedReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NonPublicMarkAttachmentUploadedReq.Unmarshal(m, b)
}
func (m *NonPublicMarkAttachmentUploadedReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NonPublicMarkAttachmentUploadedReq.Marshal(b, m, deterministic)
}
func (dst *NonPublicMarkAttachmentUploadedReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NonPublicMarkAttachmentUploadedReq.Merge(dst, src)
}
func (m *NonPublicMarkAttachmentUploadedReq) XXX_Size() int {
	return xxx_messageInfo_NonPublicMarkAttachmentUploadedReq.Size(m)
}
func (m *NonPublicMarkAttachmentUploadedReq) XXX_DiscardUnknown() {
	xxx_messageInfo_NonPublicMarkAttachmentUploadedReq.DiscardUnknown(m)
}

var xxx_messageInfo_NonPublicMarkAttachmentUploadedReq proto.InternalMessageInfo

func (m *NonPublicMarkAttachmentUploadedReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *NonPublicMarkAttachmentUploadedReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *NonPublicMarkAttachmentUploadedReq) GetAttachmentInfoList() []*NonPublicAttachment {
	if m != nil {
		return m.AttachmentInfoList
	}
	return nil
}

type NonPublicMarkAttachmentUploadedResp struct {
	PostCreateAt         uint64   `protobuf:"varint,1,opt,name=post_create_at,json=postCreateAt,proto3" json:"post_create_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NonPublicMarkAttachmentUploadedResp) Reset()         { *m = NonPublicMarkAttachmentUploadedResp{} }
func (m *NonPublicMarkAttachmentUploadedResp) String() string { return proto.CompactTextString(m) }
func (*NonPublicMarkAttachmentUploadedResp) ProtoMessage()    {}
func (*NonPublicMarkAttachmentUploadedResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{22}
}
func (m *NonPublicMarkAttachmentUploadedResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NonPublicMarkAttachmentUploadedResp.Unmarshal(m, b)
}
func (m *NonPublicMarkAttachmentUploadedResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NonPublicMarkAttachmentUploadedResp.Marshal(b, m, deterministic)
}
func (dst *NonPublicMarkAttachmentUploadedResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NonPublicMarkAttachmentUploadedResp.Merge(dst, src)
}
func (m *NonPublicMarkAttachmentUploadedResp) XXX_Size() int {
	return xxx_messageInfo_NonPublicMarkAttachmentUploadedResp.Size(m)
}
func (m *NonPublicMarkAttachmentUploadedResp) XXX_DiscardUnknown() {
	xxx_messageInfo_NonPublicMarkAttachmentUploadedResp.DiscardUnknown(m)
}

var xxx_messageInfo_NonPublicMarkAttachmentUploadedResp proto.InternalMessageInfo

func (m *NonPublicMarkAttachmentUploadedResp) GetPostCreateAt() uint64 {
	if m != nil {
		return m.PostCreateAt
	}
	return 0
}

// 帖子详情
type GetNonPublicPostByIdReq struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	MarketId             uint32   `protobuf:"varint,2,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ClientType           uint32   `protobuf:"varint,3,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNonPublicPostByIdReq) Reset()         { *m = GetNonPublicPostByIdReq{} }
func (m *GetNonPublicPostByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetNonPublicPostByIdReq) ProtoMessage()    {}
func (*GetNonPublicPostByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{23}
}
func (m *GetNonPublicPostByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNonPublicPostByIdReq.Unmarshal(m, b)
}
func (m *GetNonPublicPostByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNonPublicPostByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetNonPublicPostByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNonPublicPostByIdReq.Merge(dst, src)
}
func (m *GetNonPublicPostByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetNonPublicPostByIdReq.Size(m)
}
func (m *GetNonPublicPostByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNonPublicPostByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNonPublicPostByIdReq proto.InternalMessageInfo

func (m *GetNonPublicPostByIdReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *GetNonPublicPostByIdReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *GetNonPublicPostByIdReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

type GetNonPublicPostByIdResp struct {
	Post                 *NonPublicPostInfo `protobuf:"bytes,1,opt,name=post,proto3" json:"post,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetNonPublicPostByIdResp) Reset()         { *m = GetNonPublicPostByIdResp{} }
func (m *GetNonPublicPostByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetNonPublicPostByIdResp) ProtoMessage()    {}
func (*GetNonPublicPostByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{24}
}
func (m *GetNonPublicPostByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNonPublicPostByIdResp.Unmarshal(m, b)
}
func (m *GetNonPublicPostByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNonPublicPostByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetNonPublicPostByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNonPublicPostByIdResp.Merge(dst, src)
}
func (m *GetNonPublicPostByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetNonPublicPostByIdResp.Size(m)
}
func (m *GetNonPublicPostByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNonPublicPostByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNonPublicPostByIdResp proto.InternalMessageInfo

func (m *GetNonPublicPostByIdResp) GetPost() *NonPublicPostInfo {
	if m != nil {
		return m.Post
	}
	return nil
}

type BatGetNonPublicPostListByIdReq struct {
	PostIdList           []string `protobuf:"bytes,1,rep,name=post_id_list,json=postIdList,proto3" json:"post_id_list,omitempty"`
	MarketId             uint32   `protobuf:"varint,2,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ClientType           uint32   `protobuf:"varint,3,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetNonPublicPostListByIdReq) Reset()         { *m = BatGetNonPublicPostListByIdReq{} }
func (m *BatGetNonPublicPostListByIdReq) String() string { return proto.CompactTextString(m) }
func (*BatGetNonPublicPostListByIdReq) ProtoMessage()    {}
func (*BatGetNonPublicPostListByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{25}
}
func (m *BatGetNonPublicPostListByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetNonPublicPostListByIdReq.Unmarshal(m, b)
}
func (m *BatGetNonPublicPostListByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetNonPublicPostListByIdReq.Marshal(b, m, deterministic)
}
func (dst *BatGetNonPublicPostListByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetNonPublicPostListByIdReq.Merge(dst, src)
}
func (m *BatGetNonPublicPostListByIdReq) XXX_Size() int {
	return xxx_messageInfo_BatGetNonPublicPostListByIdReq.Size(m)
}
func (m *BatGetNonPublicPostListByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetNonPublicPostListByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetNonPublicPostListByIdReq proto.InternalMessageInfo

func (m *BatGetNonPublicPostListByIdReq) GetPostIdList() []string {
	if m != nil {
		return m.PostIdList
	}
	return nil
}

func (m *BatGetNonPublicPostListByIdReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *BatGetNonPublicPostListByIdReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

type BatGetNonPublicPostListByIdResp struct {
	PostList             []*NonPublicPostInfo `protobuf:"bytes,1,rep,name=post_list,json=postList,proto3" json:"post_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *BatGetNonPublicPostListByIdResp) Reset()         { *m = BatGetNonPublicPostListByIdResp{} }
func (m *BatGetNonPublicPostListByIdResp) String() string { return proto.CompactTextString(m) }
func (*BatGetNonPublicPostListByIdResp) ProtoMessage()    {}
func (*BatGetNonPublicPostListByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{26}
}
func (m *BatGetNonPublicPostListByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetNonPublicPostListByIdResp.Unmarshal(m, b)
}
func (m *BatGetNonPublicPostListByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetNonPublicPostListByIdResp.Marshal(b, m, deterministic)
}
func (dst *BatGetNonPublicPostListByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetNonPublicPostListByIdResp.Merge(dst, src)
}
func (m *BatGetNonPublicPostListByIdResp) XXX_Size() int {
	return xxx_messageInfo_BatGetNonPublicPostListByIdResp.Size(m)
}
func (m *BatGetNonPublicPostListByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetNonPublicPostListByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetNonPublicPostListByIdResp proto.InternalMessageInfo

func (m *BatGetNonPublicPostListByIdResp) GetPostList() []*NonPublicPostInfo {
	if m != nil {
		return m.PostList
	}
	return nil
}

type NonPublicBanPostByIdReq struct {
	SceneId              string   `protobuf:"bytes,1,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	PostId               string   `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	IsBan                bool     `protobuf:"varint,3,opt,name=is_ban,json=isBan,proto3" json:"is_ban,omitempty"`
	SecondLabelId        int32    `protobuf:"varint,4,opt,name=second_label_id,json=secondLabelId,proto3" json:"second_label_id,omitempty"`
	SecondLabelLevel     string   `protobuf:"bytes,5,opt,name=second_label_level,json=secondLabelLevel,proto3" json:"second_label_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NonPublicBanPostByIdReq) Reset()         { *m = NonPublicBanPostByIdReq{} }
func (m *NonPublicBanPostByIdReq) String() string { return proto.CompactTextString(m) }
func (*NonPublicBanPostByIdReq) ProtoMessage()    {}
func (*NonPublicBanPostByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{27}
}
func (m *NonPublicBanPostByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NonPublicBanPostByIdReq.Unmarshal(m, b)
}
func (m *NonPublicBanPostByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NonPublicBanPostByIdReq.Marshal(b, m, deterministic)
}
func (dst *NonPublicBanPostByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NonPublicBanPostByIdReq.Merge(dst, src)
}
func (m *NonPublicBanPostByIdReq) XXX_Size() int {
	return xxx_messageInfo_NonPublicBanPostByIdReq.Size(m)
}
func (m *NonPublicBanPostByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_NonPublicBanPostByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_NonPublicBanPostByIdReq proto.InternalMessageInfo

func (m *NonPublicBanPostByIdReq) GetSceneId() string {
	if m != nil {
		return m.SceneId
	}
	return ""
}

func (m *NonPublicBanPostByIdReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *NonPublicBanPostByIdReq) GetIsBan() bool {
	if m != nil {
		return m.IsBan
	}
	return false
}

func (m *NonPublicBanPostByIdReq) GetSecondLabelId() int32 {
	if m != nil {
		return m.SecondLabelId
	}
	return 0
}

func (m *NonPublicBanPostByIdReq) GetSecondLabelLevel() string {
	if m != nil {
		return m.SecondLabelLevel
	}
	return ""
}

type NonPublicBanPostByIdResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NonPublicBanPostByIdResp) Reset()         { *m = NonPublicBanPostByIdResp{} }
func (m *NonPublicBanPostByIdResp) String() string { return proto.CompactTextString(m) }
func (*NonPublicBanPostByIdResp) ProtoMessage()    {}
func (*NonPublicBanPostByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{28}
}
func (m *NonPublicBanPostByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NonPublicBanPostByIdResp.Unmarshal(m, b)
}
func (m *NonPublicBanPostByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NonPublicBanPostByIdResp.Marshal(b, m, deterministic)
}
func (dst *NonPublicBanPostByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NonPublicBanPostByIdResp.Merge(dst, src)
}
func (m *NonPublicBanPostByIdResp) XXX_Size() int {
	return xxx_messageInfo_NonPublicBanPostByIdResp.Size(m)
}
func (m *NonPublicBanPostByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_NonPublicBanPostByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_NonPublicBanPostByIdResp proto.InternalMessageInfo

// 置顶
type AddNonPublishStickyContentReq struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ParentId             string   `protobuf:"bytes,2,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	TargetId             string   `protobuf:"bytes,3,opt,name=target_id,json=targetId,proto3" json:"target_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddNonPublishStickyContentReq) Reset()         { *m = AddNonPublishStickyContentReq{} }
func (m *AddNonPublishStickyContentReq) String() string { return proto.CompactTextString(m) }
func (*AddNonPublishStickyContentReq) ProtoMessage()    {}
func (*AddNonPublishStickyContentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{29}
}
func (m *AddNonPublishStickyContentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddNonPublishStickyContentReq.Unmarshal(m, b)
}
func (m *AddNonPublishStickyContentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddNonPublishStickyContentReq.Marshal(b, m, deterministic)
}
func (dst *AddNonPublishStickyContentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddNonPublishStickyContentReq.Merge(dst, src)
}
func (m *AddNonPublishStickyContentReq) XXX_Size() int {
	return xxx_messageInfo_AddNonPublishStickyContentReq.Size(m)
}
func (m *AddNonPublishStickyContentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddNonPublishStickyContentReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddNonPublishStickyContentReq proto.InternalMessageInfo

func (m *AddNonPublishStickyContentReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *AddNonPublishStickyContentReq) GetParentId() string {
	if m != nil {
		return m.ParentId
	}
	return ""
}

func (m *AddNonPublishStickyContentReq) GetTargetId() string {
	if m != nil {
		return m.TargetId
	}
	return ""
}

type AddNonPublishStickyContentResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddNonPublishStickyContentResp) Reset()         { *m = AddNonPublishStickyContentResp{} }
func (m *AddNonPublishStickyContentResp) String() string { return proto.CompactTextString(m) }
func (*AddNonPublishStickyContentResp) ProtoMessage()    {}
func (*AddNonPublishStickyContentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{30}
}
func (m *AddNonPublishStickyContentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddNonPublishStickyContentResp.Unmarshal(m, b)
}
func (m *AddNonPublishStickyContentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddNonPublishStickyContentResp.Marshal(b, m, deterministic)
}
func (dst *AddNonPublishStickyContentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddNonPublishStickyContentResp.Merge(dst, src)
}
func (m *AddNonPublishStickyContentResp) XXX_Size() int {
	return xxx_messageInfo_AddNonPublishStickyContentResp.Size(m)
}
func (m *AddNonPublishStickyContentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddNonPublishStickyContentResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddNonPublishStickyContentResp proto.InternalMessageInfo

type RemoveNonPublishStickyContentReq struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ParentId             string   `protobuf:"bytes,2,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	TargetId             string   `protobuf:"bytes,3,opt,name=target_id,json=targetId,proto3" json:"target_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveNonPublishStickyContentReq) Reset()         { *m = RemoveNonPublishStickyContentReq{} }
func (m *RemoveNonPublishStickyContentReq) String() string { return proto.CompactTextString(m) }
func (*RemoveNonPublishStickyContentReq) ProtoMessage()    {}
func (*RemoveNonPublishStickyContentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{31}
}
func (m *RemoveNonPublishStickyContentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveNonPublishStickyContentReq.Unmarshal(m, b)
}
func (m *RemoveNonPublishStickyContentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveNonPublishStickyContentReq.Marshal(b, m, deterministic)
}
func (dst *RemoveNonPublishStickyContentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveNonPublishStickyContentReq.Merge(dst, src)
}
func (m *RemoveNonPublishStickyContentReq) XXX_Size() int {
	return xxx_messageInfo_RemoveNonPublishStickyContentReq.Size(m)
}
func (m *RemoveNonPublishStickyContentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveNonPublishStickyContentReq.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveNonPublishStickyContentReq proto.InternalMessageInfo

func (m *RemoveNonPublishStickyContentReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *RemoveNonPublishStickyContentReq) GetParentId() string {
	if m != nil {
		return m.ParentId
	}
	return ""
}

func (m *RemoveNonPublishStickyContentReq) GetTargetId() string {
	if m != nil {
		return m.TargetId
	}
	return ""
}

type RemoveNonPublishStickyContentResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveNonPublishStickyContentResp) Reset()         { *m = RemoveNonPublishStickyContentResp{} }
func (m *RemoveNonPublishStickyContentResp) String() string { return proto.CompactTextString(m) }
func (*RemoveNonPublishStickyContentResp) ProtoMessage()    {}
func (*RemoveNonPublishStickyContentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{32}
}
func (m *RemoveNonPublishStickyContentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveNonPublishStickyContentResp.Unmarshal(m, b)
}
func (m *RemoveNonPublishStickyContentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveNonPublishStickyContentResp.Marshal(b, m, deterministic)
}
func (dst *RemoveNonPublishStickyContentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveNonPublishStickyContentResp.Merge(dst, src)
}
func (m *RemoveNonPublishStickyContentResp) XXX_Size() int {
	return xxx_messageInfo_RemoveNonPublishStickyContentResp.Size(m)
}
func (m *RemoveNonPublishStickyContentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveNonPublishStickyContentResp.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveNonPublishStickyContentResp proto.InternalMessageInfo

type GetNonPublishStickyContentReq struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ParentId             string   `protobuf:"bytes,2,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNonPublishStickyContentReq) Reset()         { *m = GetNonPublishStickyContentReq{} }
func (m *GetNonPublishStickyContentReq) String() string { return proto.CompactTextString(m) }
func (*GetNonPublishStickyContentReq) ProtoMessage()    {}
func (*GetNonPublishStickyContentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{33}
}
func (m *GetNonPublishStickyContentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNonPublishStickyContentReq.Unmarshal(m, b)
}
func (m *GetNonPublishStickyContentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNonPublishStickyContentReq.Marshal(b, m, deterministic)
}
func (dst *GetNonPublishStickyContentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNonPublishStickyContentReq.Merge(dst, src)
}
func (m *GetNonPublishStickyContentReq) XXX_Size() int {
	return xxx_messageInfo_GetNonPublishStickyContentReq.Size(m)
}
func (m *GetNonPublishStickyContentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNonPublishStickyContentReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNonPublishStickyContentReq proto.InternalMessageInfo

func (m *GetNonPublishStickyContentReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *GetNonPublishStickyContentReq) GetParentId() string {
	if m != nil {
		return m.ParentId
	}
	return ""
}

type GetNonPublishStickyContentResp struct {
	ParentId             string   `protobuf:"bytes,1,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	TargetIds            []string `protobuf:"bytes,2,rep,name=target_ids,json=targetIds,proto3" json:"target_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNonPublishStickyContentResp) Reset()         { *m = GetNonPublishStickyContentResp{} }
func (m *GetNonPublishStickyContentResp) String() string { return proto.CompactTextString(m) }
func (*GetNonPublishStickyContentResp) ProtoMessage()    {}
func (*GetNonPublishStickyContentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{34}
}
func (m *GetNonPublishStickyContentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNonPublishStickyContentResp.Unmarshal(m, b)
}
func (m *GetNonPublishStickyContentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNonPublishStickyContentResp.Marshal(b, m, deterministic)
}
func (dst *GetNonPublishStickyContentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNonPublishStickyContentResp.Merge(dst, src)
}
func (m *GetNonPublishStickyContentResp) XXX_Size() int {
	return xxx_messageInfo_GetNonPublishStickyContentResp.Size(m)
}
func (m *GetNonPublishStickyContentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNonPublishStickyContentResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNonPublishStickyContentResp proto.InternalMessageInfo

func (m *GetNonPublishStickyContentResp) GetParentId() string {
	if m != nil {
		return m.ParentId
	}
	return ""
}

func (m *GetNonPublishStickyContentResp) GetTargetIds() []string {
	if m != nil {
		return m.TargetIds
	}
	return nil
}

type UpdateNonPublicPostMachineAuditByIdReq struct {
	SceneId              string   `protobuf:"bytes,1,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	PostId               string   `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Status               uint32   `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	Reason               string   `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateNonPublicPostMachineAuditByIdReq) Reset() {
	*m = UpdateNonPublicPostMachineAuditByIdReq{}
}
func (m *UpdateNonPublicPostMachineAuditByIdReq) String() string { return proto.CompactTextString(m) }
func (*UpdateNonPublicPostMachineAuditByIdReq) ProtoMessage()    {}
func (*UpdateNonPublicPostMachineAuditByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{35}
}
func (m *UpdateNonPublicPostMachineAuditByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateNonPublicPostMachineAuditByIdReq.Unmarshal(m, b)
}
func (m *UpdateNonPublicPostMachineAuditByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateNonPublicPostMachineAuditByIdReq.Marshal(b, m, deterministic)
}
func (dst *UpdateNonPublicPostMachineAuditByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateNonPublicPostMachineAuditByIdReq.Merge(dst, src)
}
func (m *UpdateNonPublicPostMachineAuditByIdReq) XXX_Size() int {
	return xxx_messageInfo_UpdateNonPublicPostMachineAuditByIdReq.Size(m)
}
func (m *UpdateNonPublicPostMachineAuditByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateNonPublicPostMachineAuditByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateNonPublicPostMachineAuditByIdReq proto.InternalMessageInfo

func (m *UpdateNonPublicPostMachineAuditByIdReq) GetSceneId() string {
	if m != nil {
		return m.SceneId
	}
	return ""
}

func (m *UpdateNonPublicPostMachineAuditByIdReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *UpdateNonPublicPostMachineAuditByIdReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *UpdateNonPublicPostMachineAuditByIdReq) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

type UpdateNonPublicPostMachineAuditByIdResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateNonPublicPostMachineAuditByIdResp) Reset() {
	*m = UpdateNonPublicPostMachineAuditByIdResp{}
}
func (m *UpdateNonPublicPostMachineAuditByIdResp) String() string { return proto.CompactTextString(m) }
func (*UpdateNonPublicPostMachineAuditByIdResp) ProtoMessage()    {}
func (*UpdateNonPublicPostMachineAuditByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{36}
}
func (m *UpdateNonPublicPostMachineAuditByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateNonPublicPostMachineAuditByIdResp.Unmarshal(m, b)
}
func (m *UpdateNonPublicPostMachineAuditByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateNonPublicPostMachineAuditByIdResp.Marshal(b, m, deterministic)
}
func (dst *UpdateNonPublicPostMachineAuditByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateNonPublicPostMachineAuditByIdResp.Merge(dst, src)
}
func (m *UpdateNonPublicPostMachineAuditByIdResp) XXX_Size() int {
	return xxx_messageInfo_UpdateNonPublicPostMachineAuditByIdResp.Size(m)
}
func (m *UpdateNonPublicPostMachineAuditByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateNonPublicPostMachineAuditByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateNonPublicPostMachineAuditByIdResp proto.InternalMessageInfo

// 帖子详情
type NonPublicPostInfo struct {
	SceneId         string                      `protobuf:"bytes,1,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	SceneStreamList []*SceneStream              `protobuf:"bytes,2,rep,name=scene_stream_list,json=sceneStreamList,proto3" json:"scene_stream_list,omitempty"`
	PostId          string                      `protobuf:"bytes,3,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Title           string                      `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	ContentList     []*NonPublicRichTextElement `protobuf:"bytes,5,rep,name=content_list,json=contentList,proto3" json:"content_list,omitempty"`
	UserId          uint32                      `protobuf:"varint,6,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	CreateAt        uint64                      `protobuf:"varint,7,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	PostUpdateTime  uint64                      `protobuf:"varint,8,opt,name=post_update_time,json=postUpdateTime,proto3" json:"post_update_time,omitempty"`
	CommentCount    uint32                      `protobuf:"varint,9,opt,name=comment_count,json=commentCount,proto3" json:"comment_count,omitempty"`
	AttitudeCount   uint32                      `protobuf:"varint,10,opt,name=attitude_count,json=attitudeCount,proto3" json:"attitude_count,omitempty"`
	ViewCount       uint32                      `protobuf:"varint,11,opt,name=view_count,json=viewCount,proto3" json:"view_count,omitempty"`
	MyAttitude      uint32                      `protobuf:"varint,12,opt,name=my_attitude,json=myAttitude,proto3" json:"my_attitude,omitempty"`
	ShareCount      uint32                      `protobuf:"varint,13,opt,name=share_count,json=shareCount,proto3" json:"share_count,omitempty"`
	HadFavoured     bool                        `protobuf:"varint,14,opt,name=had_favoured,json=hadFavoured,proto3" json:"had_favoured,omitempty"`
	IsSticky        bool                        `protobuf:"varint,15,opt,name=is_sticky,json=isSticky,proto3" json:"is_sticky,omitempty"`
	AbnormalState   uint32                      `protobuf:"varint,16,opt,name=abnormal_state,json=abnormalState,proto3" json:"abnormal_state,omitempty"`
	PostSource      uint32                      `protobuf:"varint,17,opt,name=post_source,json=postSource,proto3" json:"post_source,omitempty"`
	Privacy         uint32                      `protobuf:"varint,18,opt,name=privacy,proto3" json:"privacy,omitempty"`
	// TT5.4.2
	PostPrivacyPolicy uint32 `protobuf:"varint,19,opt,name=post_privacy_policy,json=postPrivacyPolicy,proto3" json:"post_privacy_policy,omitempty"`
	// TT5.4.3 发帖来源
	Origin            uint32                    `protobuf:"varint,20,opt,name=origin,proto3" json:"origin,omitempty"`
	PostStatus        uint32                    `protobuf:"varint,21,opt,name=post_status,json=postStatus,proto3" json:"post_status,omitempty"`
	PostMachineStatus uint32                    `protobuf:"varint,22,opt,name=post_machine_status,json=postMachineStatus,proto3" json:"post_machine_status,omitempty"`
	LabelRemind       string                    `protobuf:"bytes,23,opt,name=label_remind,json=labelRemind,proto3" json:"label_remind,omitempty"`
	SecondLabelId     int32                     `protobuf:"varint,24,opt,name=second_label_id,json=secondLabelId,proto3" json:"second_label_id,omitempty"`
	LabelLevel        string                    `protobuf:"bytes,25,opt,name=label_level,json=labelLevel,proto3" json:"label_level,omitempty"`
	GeneralContents   []*content.GeneralContent `protobuf:"bytes,26,rep,name=general_contents,json=generalContents,proto3" json:"general_contents,omitempty"`
	// 发帖人IP归属地
	IpLoc                *content.Location `protobuf:"bytes,27,opt,name=ip_loc,json=ipLoc,proto3" json:"ip_loc,omitempty"`
	OriginSceneStream    *SceneStream      `protobuf:"bytes,28,opt,name=origin_scene_stream,json=originSceneStream,proto3" json:"origin_scene_stream,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *NonPublicPostInfo) Reset()         { *m = NonPublicPostInfo{} }
func (m *NonPublicPostInfo) String() string { return proto.CompactTextString(m) }
func (*NonPublicPostInfo) ProtoMessage()    {}
func (*NonPublicPostInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{37}
}
func (m *NonPublicPostInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NonPublicPostInfo.Unmarshal(m, b)
}
func (m *NonPublicPostInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NonPublicPostInfo.Marshal(b, m, deterministic)
}
func (dst *NonPublicPostInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NonPublicPostInfo.Merge(dst, src)
}
func (m *NonPublicPostInfo) XXX_Size() int {
	return xxx_messageInfo_NonPublicPostInfo.Size(m)
}
func (m *NonPublicPostInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_NonPublicPostInfo.DiscardUnknown(m)
}

var xxx_messageInfo_NonPublicPostInfo proto.InternalMessageInfo

func (m *NonPublicPostInfo) GetSceneId() string {
	if m != nil {
		return m.SceneId
	}
	return ""
}

func (m *NonPublicPostInfo) GetSceneStreamList() []*SceneStream {
	if m != nil {
		return m.SceneStreamList
	}
	return nil
}

func (m *NonPublicPostInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *NonPublicPostInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *NonPublicPostInfo) GetContentList() []*NonPublicRichTextElement {
	if m != nil {
		return m.ContentList
	}
	return nil
}

func (m *NonPublicPostInfo) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *NonPublicPostInfo) GetCreateAt() uint64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *NonPublicPostInfo) GetPostUpdateTime() uint64 {
	if m != nil {
		return m.PostUpdateTime
	}
	return 0
}

func (m *NonPublicPostInfo) GetCommentCount() uint32 {
	if m != nil {
		return m.CommentCount
	}
	return 0
}

func (m *NonPublicPostInfo) GetAttitudeCount() uint32 {
	if m != nil {
		return m.AttitudeCount
	}
	return 0
}

func (m *NonPublicPostInfo) GetViewCount() uint32 {
	if m != nil {
		return m.ViewCount
	}
	return 0
}

func (m *NonPublicPostInfo) GetMyAttitude() uint32 {
	if m != nil {
		return m.MyAttitude
	}
	return 0
}

func (m *NonPublicPostInfo) GetShareCount() uint32 {
	if m != nil {
		return m.ShareCount
	}
	return 0
}

func (m *NonPublicPostInfo) GetHadFavoured() bool {
	if m != nil {
		return m.HadFavoured
	}
	return false
}

func (m *NonPublicPostInfo) GetIsSticky() bool {
	if m != nil {
		return m.IsSticky
	}
	return false
}

func (m *NonPublicPostInfo) GetAbnormalState() uint32 {
	if m != nil {
		return m.AbnormalState
	}
	return 0
}

func (m *NonPublicPostInfo) GetPostSource() uint32 {
	if m != nil {
		return m.PostSource
	}
	return 0
}

func (m *NonPublicPostInfo) GetPrivacy() uint32 {
	if m != nil {
		return m.Privacy
	}
	return 0
}

func (m *NonPublicPostInfo) GetPostPrivacyPolicy() uint32 {
	if m != nil {
		return m.PostPrivacyPolicy
	}
	return 0
}

func (m *NonPublicPostInfo) GetOrigin() uint32 {
	if m != nil {
		return m.Origin
	}
	return 0
}

func (m *NonPublicPostInfo) GetPostStatus() uint32 {
	if m != nil {
		return m.PostStatus
	}
	return 0
}

func (m *NonPublicPostInfo) GetPostMachineStatus() uint32 {
	if m != nil {
		return m.PostMachineStatus
	}
	return 0
}

func (m *NonPublicPostInfo) GetLabelRemind() string {
	if m != nil {
		return m.LabelRemind
	}
	return ""
}

func (m *NonPublicPostInfo) GetSecondLabelId() int32 {
	if m != nil {
		return m.SecondLabelId
	}
	return 0
}

func (m *NonPublicPostInfo) GetLabelLevel() string {
	if m != nil {
		return m.LabelLevel
	}
	return ""
}

func (m *NonPublicPostInfo) GetGeneralContents() []*content.GeneralContent {
	if m != nil {
		return m.GeneralContents
	}
	return nil
}

func (m *NonPublicPostInfo) GetIpLoc() *content.Location {
	if m != nil {
		return m.IpLoc
	}
	return nil
}

func (m *NonPublicPostInfo) GetOriginSceneStream() *SceneStream {
	if m != nil {
		return m.OriginSceneStream
	}
	return nil
}

// -------- 评论 --------
type AddNonPublicCommentReq struct {
	SceneId           string                      `protobuf:"bytes,1,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	OriginSceneStream *SceneStream                `protobuf:"bytes,2,opt,name=origin_scene_stream,json=originSceneStream,proto3" json:"origin_scene_stream,omitempty"`
	UserId            uint32                      `protobuf:"varint,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	PostId            string                      `protobuf:"bytes,4,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	ReplyToCommentId  string                      `protobuf:"bytes,5,opt,name=reply_to_comment_id,json=replyToCommentId,proto3" json:"reply_to_comment_id,omitempty"`
	ConversationId    string                      `protobuf:"bytes,6,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id,omitempty"`
	Status            uint32                      `protobuf:"varint,7,opt,name=status,proto3" json:"status,omitempty"`
	ContentList       []*NonPublicRichTextElement `protobuf:"bytes,8,rep,name=content_list,json=contentList,proto3" json:"content_list,omitempty"`
	Source            uint32                      `protobuf:"varint,9,opt,name=source,proto3" json:"source,omitempty"`
	// 评论人的IP归属地
	IpLoc                *content.Location         `protobuf:"bytes,10,opt,name=ip_loc,json=ipLoc,proto3" json:"ip_loc,omitempty"`
	ClientIp             uint32                    `protobuf:"varint,11,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	GeneralContents      []*content.GeneralContent `protobuf:"bytes,12,rep,name=general_contents,json=generalContents,proto3" json:"general_contents,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *AddNonPublicCommentReq) Reset()         { *m = AddNonPublicCommentReq{} }
func (m *AddNonPublicCommentReq) String() string { return proto.CompactTextString(m) }
func (*AddNonPublicCommentReq) ProtoMessage()    {}
func (*AddNonPublicCommentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{38}
}
func (m *AddNonPublicCommentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddNonPublicCommentReq.Unmarshal(m, b)
}
func (m *AddNonPublicCommentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddNonPublicCommentReq.Marshal(b, m, deterministic)
}
func (dst *AddNonPublicCommentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddNonPublicCommentReq.Merge(dst, src)
}
func (m *AddNonPublicCommentReq) XXX_Size() int {
	return xxx_messageInfo_AddNonPublicCommentReq.Size(m)
}
func (m *AddNonPublicCommentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddNonPublicCommentReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddNonPublicCommentReq proto.InternalMessageInfo

func (m *AddNonPublicCommentReq) GetSceneId() string {
	if m != nil {
		return m.SceneId
	}
	return ""
}

func (m *AddNonPublicCommentReq) GetOriginSceneStream() *SceneStream {
	if m != nil {
		return m.OriginSceneStream
	}
	return nil
}

func (m *AddNonPublicCommentReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *AddNonPublicCommentReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *AddNonPublicCommentReq) GetReplyToCommentId() string {
	if m != nil {
		return m.ReplyToCommentId
	}
	return ""
}

func (m *AddNonPublicCommentReq) GetConversationId() string {
	if m != nil {
		return m.ConversationId
	}
	return ""
}

func (m *AddNonPublicCommentReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *AddNonPublicCommentReq) GetContentList() []*NonPublicRichTextElement {
	if m != nil {
		return m.ContentList
	}
	return nil
}

func (m *AddNonPublicCommentReq) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *AddNonPublicCommentReq) GetIpLoc() *content.Location {
	if m != nil {
		return m.IpLoc
	}
	return nil
}

func (m *AddNonPublicCommentReq) GetClientIp() uint32 {
	if m != nil {
		return m.ClientIp
	}
	return 0
}

func (m *AddNonPublicCommentReq) GetGeneralContents() []*content.GeneralContent {
	if m != nil {
		return m.GeneralContents
	}
	return nil
}

type AddNonPublicCommentResp struct {
	CommentId            string       `protobuf:"bytes,1,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	Comment              *CommentInfo `protobuf:"bytes,2,opt,name=comment,proto3" json:"comment,omitempty"`
	AttachmentImageKeys  []string     `protobuf:"bytes,3,rep,name=attachment_image_keys,json=attachmentImageKeys,proto3" json:"attachment_image_keys,omitempty"`
	ImageUploadToken     string       `protobuf:"bytes,4,opt,name=image_upload_token,json=imageUploadToken,proto3" json:"image_upload_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AddNonPublicCommentResp) Reset()         { *m = AddNonPublicCommentResp{} }
func (m *AddNonPublicCommentResp) String() string { return proto.CompactTextString(m) }
func (*AddNonPublicCommentResp) ProtoMessage()    {}
func (*AddNonPublicCommentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{39}
}
func (m *AddNonPublicCommentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddNonPublicCommentResp.Unmarshal(m, b)
}
func (m *AddNonPublicCommentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddNonPublicCommentResp.Marshal(b, m, deterministic)
}
func (dst *AddNonPublicCommentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddNonPublicCommentResp.Merge(dst, src)
}
func (m *AddNonPublicCommentResp) XXX_Size() int {
	return xxx_messageInfo_AddNonPublicCommentResp.Size(m)
}
func (m *AddNonPublicCommentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddNonPublicCommentResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddNonPublicCommentResp proto.InternalMessageInfo

func (m *AddNonPublicCommentResp) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *AddNonPublicCommentResp) GetComment() *CommentInfo {
	if m != nil {
		return m.Comment
	}
	return nil
}

func (m *AddNonPublicCommentResp) GetAttachmentImageKeys() []string {
	if m != nil {
		return m.AttachmentImageKeys
	}
	return nil
}

func (m *AddNonPublicCommentResp) GetImageUploadToken() string {
	if m != nil {
		return m.ImageUploadToken
	}
	return ""
}

type AttachmentInfo struct {
	Key       string                        `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Type      AttachmentInfo_AttachmentType `protobuf:"varint,2,opt,name=type,proto3,enum=ugc.non_public_content.AttachmentInfo_AttachmentType" json:"type,omitempty"`
	Content   string                        `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	Extra     string                        `protobuf:"bytes,4,opt,name=extra,proto3" json:"extra,omitempty"`
	Status    int32                         `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	VmContent string                        `protobuf:"bytes,6,opt,name=vm_content,json=vmContent,proto3" json:"vm_content,omitempty"`
	// 给type == video用的, 转码参数
	Param string `protobuf:"bytes,10,opt,name=param,proto3" json:"param,omitempty"`
	// 原始视频封面url
	OriginVideoCover     string   `protobuf:"bytes,11,opt,name=origin_video_cover,json=originVideoCover,proto3" json:"origin_video_cover,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AttachmentInfo) Reset()         { *m = AttachmentInfo{} }
func (m *AttachmentInfo) String() string { return proto.CompactTextString(m) }
func (*AttachmentInfo) ProtoMessage()    {}
func (*AttachmentInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{40}
}
func (m *AttachmentInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AttachmentInfo.Unmarshal(m, b)
}
func (m *AttachmentInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AttachmentInfo.Marshal(b, m, deterministic)
}
func (dst *AttachmentInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AttachmentInfo.Merge(dst, src)
}
func (m *AttachmentInfo) XXX_Size() int {
	return xxx_messageInfo_AttachmentInfo.Size(m)
}
func (m *AttachmentInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AttachmentInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AttachmentInfo proto.InternalMessageInfo

func (m *AttachmentInfo) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *AttachmentInfo) GetType() AttachmentInfo_AttachmentType {
	if m != nil {
		return m.Type
	}
	return AttachmentInfo_NONE
}

func (m *AttachmentInfo) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *AttachmentInfo) GetExtra() string {
	if m != nil {
		return m.Extra
	}
	return ""
}

func (m *AttachmentInfo) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *AttachmentInfo) GetVmContent() string {
	if m != nil {
		return m.VmContent
	}
	return ""
}

func (m *AttachmentInfo) GetParam() string {
	if m != nil {
		return m.Param
	}
	return ""
}

func (m *AttachmentInfo) GetOriginVideoCover() string {
	if m != nil {
		return m.OriginVideoCover
	}
	return ""
}

type CommentInfo struct {
	CommentId            string                      `protobuf:"bytes,1,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	PostId               string                      `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	ConversationId       string                      `protobuf:"bytes,3,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id,omitempty"`
	UserId               uint32                      `protobuf:"varint,4,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ReplyToUserId        uint32                      `protobuf:"varint,5,opt,name=reply_to_user_id,json=replyToUserId,proto3" json:"reply_to_user_id,omitempty"`
	ContentList          []*NonPublicRichTextElement `protobuf:"bytes,6,rep,name=content_list,json=contentList,proto3" json:"content_list,omitempty"`
	CommentCount         uint32                      `protobuf:"varint,7,opt,name=comment_count,json=commentCount,proto3" json:"comment_count,omitempty"`
	AttitudeCount        uint32                      `protobuf:"varint,8,opt,name=attitude_count,json=attitudeCount,proto3" json:"attitude_count,omitempty"`
	Status               uint32                      `protobuf:"varint,9,opt,name=status,proto3" json:"status,omitempty"`
	SubComments          []*CommentInfo              `protobuf:"bytes,10,rep,name=sub_comments,json=subComments,proto3" json:"sub_comments,omitempty"`
	CreateAt             uint64                      `protobuf:"varint,11,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	ReplyToCommentId     string                      `protobuf:"bytes,12,opt,name=reply_to_comment_id,json=replyToCommentId,proto3" json:"reply_to_comment_id,omitempty"`
	StepCount            uint32                      `protobuf:"varint,13,opt,name=step_count,json=stepCount,proto3" json:"step_count,omitempty"`
	IpLoc                *content.Location           `protobuf:"bytes,14,opt,name=ip_loc,json=ipLoc,proto3" json:"ip_loc,omitempty"`
	GeneralContents      []*content.GeneralContent   `protobuf:"bytes,15,rep,name=general_contents,json=generalContents,proto3" json:"general_contents,omitempty"`
	OriginSceneStream    *SceneStream                `protobuf:"bytes,16,opt,name=origin_scene_stream,json=originSceneStream,proto3" json:"origin_scene_stream,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *CommentInfo) Reset()         { *m = CommentInfo{} }
func (m *CommentInfo) String() string { return proto.CompactTextString(m) }
func (*CommentInfo) ProtoMessage()    {}
func (*CommentInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{41}
}
func (m *CommentInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentInfo.Unmarshal(m, b)
}
func (m *CommentInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentInfo.Marshal(b, m, deterministic)
}
func (dst *CommentInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentInfo.Merge(dst, src)
}
func (m *CommentInfo) XXX_Size() int {
	return xxx_messageInfo_CommentInfo.Size(m)
}
func (m *CommentInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CommentInfo proto.InternalMessageInfo

func (m *CommentInfo) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *CommentInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *CommentInfo) GetConversationId() string {
	if m != nil {
		return m.ConversationId
	}
	return ""
}

func (m *CommentInfo) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *CommentInfo) GetReplyToUserId() uint32 {
	if m != nil {
		return m.ReplyToUserId
	}
	return 0
}

func (m *CommentInfo) GetContentList() []*NonPublicRichTextElement {
	if m != nil {
		return m.ContentList
	}
	return nil
}

func (m *CommentInfo) GetCommentCount() uint32 {
	if m != nil {
		return m.CommentCount
	}
	return 0
}

func (m *CommentInfo) GetAttitudeCount() uint32 {
	if m != nil {
		return m.AttitudeCount
	}
	return 0
}

func (m *CommentInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *CommentInfo) GetSubComments() []*CommentInfo {
	if m != nil {
		return m.SubComments
	}
	return nil
}

func (m *CommentInfo) GetCreateAt() uint64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *CommentInfo) GetReplyToCommentId() string {
	if m != nil {
		return m.ReplyToCommentId
	}
	return ""
}

func (m *CommentInfo) GetStepCount() uint32 {
	if m != nil {
		return m.StepCount
	}
	return 0
}

func (m *CommentInfo) GetIpLoc() *content.Location {
	if m != nil {
		return m.IpLoc
	}
	return nil
}

func (m *CommentInfo) GetGeneralContents() []*content.GeneralContent {
	if m != nil {
		return m.GeneralContents
	}
	return nil
}

func (m *CommentInfo) GetOriginSceneStream() *SceneStream {
	if m != nil {
		return m.OriginSceneStream
	}
	return nil
}

type GetNonPublicCommentListReq struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	ConversationId       string   `protobuf:"bytes,2,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id,omitempty"`
	LoadMore             string   `protobuf:"bytes,3,opt,name=loadMore,proto3" json:"loadMore,omitempty"`
	Count                uint32   `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	UserId               uint32   `protobuf:"varint,5,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	MarketId             uint32   `protobuf:"varint,6,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ClientType           uint32   `protobuf:"varint,7,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNonPublicCommentListReq) Reset()         { *m = GetNonPublicCommentListReq{} }
func (m *GetNonPublicCommentListReq) String() string { return proto.CompactTextString(m) }
func (*GetNonPublicCommentListReq) ProtoMessage()    {}
func (*GetNonPublicCommentListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{42}
}
func (m *GetNonPublicCommentListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNonPublicCommentListReq.Unmarshal(m, b)
}
func (m *GetNonPublicCommentListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNonPublicCommentListReq.Marshal(b, m, deterministic)
}
func (dst *GetNonPublicCommentListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNonPublicCommentListReq.Merge(dst, src)
}
func (m *GetNonPublicCommentListReq) XXX_Size() int {
	return xxx_messageInfo_GetNonPublicCommentListReq.Size(m)
}
func (m *GetNonPublicCommentListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNonPublicCommentListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNonPublicCommentListReq proto.InternalMessageInfo

func (m *GetNonPublicCommentListReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *GetNonPublicCommentListReq) GetConversationId() string {
	if m != nil {
		return m.ConversationId
	}
	return ""
}

func (m *GetNonPublicCommentListReq) GetLoadMore() string {
	if m != nil {
		return m.LoadMore
	}
	return ""
}

func (m *GetNonPublicCommentListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetNonPublicCommentListReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *GetNonPublicCommentListReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *GetNonPublicCommentListReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

type GetNonPublicCommentListResp struct {
	CommentList          []*CommentInfo `protobuf:"bytes,1,rep,name=comment_list,json=commentList,proto3" json:"comment_list,omitempty"`
	LoadMore             string         `protobuf:"bytes,2,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetNonPublicCommentListResp) Reset()         { *m = GetNonPublicCommentListResp{} }
func (m *GetNonPublicCommentListResp) String() string { return proto.CompactTextString(m) }
func (*GetNonPublicCommentListResp) ProtoMessage()    {}
func (*GetNonPublicCommentListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{43}
}
func (m *GetNonPublicCommentListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNonPublicCommentListResp.Unmarshal(m, b)
}
func (m *GetNonPublicCommentListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNonPublicCommentListResp.Marshal(b, m, deterministic)
}
func (dst *GetNonPublicCommentListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNonPublicCommentListResp.Merge(dst, src)
}
func (m *GetNonPublicCommentListResp) XXX_Size() int {
	return xxx_messageInfo_GetNonPublicCommentListResp.Size(m)
}
func (m *GetNonPublicCommentListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNonPublicCommentListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNonPublicCommentListResp proto.InternalMessageInfo

func (m *GetNonPublicCommentListResp) GetCommentList() []*CommentInfo {
	if m != nil {
		return m.CommentList
	}
	return nil
}

func (m *GetNonPublicCommentListResp) GetLoadMore() string {
	if m != nil {
		return m.LoadMore
	}
	return ""
}

type BatchGetNonPublicCommentByIdsReq struct {
	CommentId            []string `protobuf:"bytes,1,rep,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	ContentType          int32    `protobuf:"varint,2,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`
	MarketId             uint32   `protobuf:"varint,3,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ClientType           uint32   `protobuf:"varint,4,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetNonPublicCommentByIdsReq) Reset()         { *m = BatchGetNonPublicCommentByIdsReq{} }
func (m *BatchGetNonPublicCommentByIdsReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetNonPublicCommentByIdsReq) ProtoMessage()    {}
func (*BatchGetNonPublicCommentByIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{44}
}
func (m *BatchGetNonPublicCommentByIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetNonPublicCommentByIdsReq.Unmarshal(m, b)
}
func (m *BatchGetNonPublicCommentByIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetNonPublicCommentByIdsReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetNonPublicCommentByIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetNonPublicCommentByIdsReq.Merge(dst, src)
}
func (m *BatchGetNonPublicCommentByIdsReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetNonPublicCommentByIdsReq.Size(m)
}
func (m *BatchGetNonPublicCommentByIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetNonPublicCommentByIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetNonPublicCommentByIdsReq proto.InternalMessageInfo

func (m *BatchGetNonPublicCommentByIdsReq) GetCommentId() []string {
	if m != nil {
		return m.CommentId
	}
	return nil
}

func (m *BatchGetNonPublicCommentByIdsReq) GetContentType() int32 {
	if m != nil {
		return m.ContentType
	}
	return 0
}

func (m *BatchGetNonPublicCommentByIdsReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *BatchGetNonPublicCommentByIdsReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

type BatchGetNonPublicCommentByIdsResp struct {
	CommentInfoMap       map[string]*CommentInfo `protobuf:"bytes,1,rep,name=comment_info_map,json=commentInfoMap,proto3" json:"comment_info_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *BatchGetNonPublicCommentByIdsResp) Reset()         { *m = BatchGetNonPublicCommentByIdsResp{} }
func (m *BatchGetNonPublicCommentByIdsResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetNonPublicCommentByIdsResp) ProtoMessage()    {}
func (*BatchGetNonPublicCommentByIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{45}
}
func (m *BatchGetNonPublicCommentByIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetNonPublicCommentByIdsResp.Unmarshal(m, b)
}
func (m *BatchGetNonPublicCommentByIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetNonPublicCommentByIdsResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetNonPublicCommentByIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetNonPublicCommentByIdsResp.Merge(dst, src)
}
func (m *BatchGetNonPublicCommentByIdsResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetNonPublicCommentByIdsResp.Size(m)
}
func (m *BatchGetNonPublicCommentByIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetNonPublicCommentByIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetNonPublicCommentByIdsResp proto.InternalMessageInfo

func (m *BatchGetNonPublicCommentByIdsResp) GetCommentInfoMap() map[string]*CommentInfo {
	if m != nil {
		return m.CommentInfoMap
	}
	return nil
}

type GetNonPublicHotCommentReq struct {
	SceneId              string   `protobuf:"bytes,1,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	PostId               string   `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Count                int32    `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNonPublicHotCommentReq) Reset()         { *m = GetNonPublicHotCommentReq{} }
func (m *GetNonPublicHotCommentReq) String() string { return proto.CompactTextString(m) }
func (*GetNonPublicHotCommentReq) ProtoMessage()    {}
func (*GetNonPublicHotCommentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{46}
}
func (m *GetNonPublicHotCommentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNonPublicHotCommentReq.Unmarshal(m, b)
}
func (m *GetNonPublicHotCommentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNonPublicHotCommentReq.Marshal(b, m, deterministic)
}
func (dst *GetNonPublicHotCommentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNonPublicHotCommentReq.Merge(dst, src)
}
func (m *GetNonPublicHotCommentReq) XXX_Size() int {
	return xxx_messageInfo_GetNonPublicHotCommentReq.Size(m)
}
func (m *GetNonPublicHotCommentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNonPublicHotCommentReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNonPublicHotCommentReq proto.InternalMessageInfo

func (m *GetNonPublicHotCommentReq) GetSceneId() string {
	if m != nil {
		return m.SceneId
	}
	return ""
}

func (m *GetNonPublicHotCommentReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *GetNonPublicHotCommentReq) GetCount() int32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetNonPublicHotCommentResp struct {
	CommentIds           []string `protobuf:"bytes,1,rep,name=comment_ids,json=commentIds,proto3" json:"comment_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNonPublicHotCommentResp) Reset()         { *m = GetNonPublicHotCommentResp{} }
func (m *GetNonPublicHotCommentResp) String() string { return proto.CompactTextString(m) }
func (*GetNonPublicHotCommentResp) ProtoMessage()    {}
func (*GetNonPublicHotCommentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{47}
}
func (m *GetNonPublicHotCommentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNonPublicHotCommentResp.Unmarshal(m, b)
}
func (m *GetNonPublicHotCommentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNonPublicHotCommentResp.Marshal(b, m, deterministic)
}
func (dst *GetNonPublicHotCommentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNonPublicHotCommentResp.Merge(dst, src)
}
func (m *GetNonPublicHotCommentResp) XXX_Size() int {
	return xxx_messageInfo_GetNonPublicHotCommentResp.Size(m)
}
func (m *GetNonPublicHotCommentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNonPublicHotCommentResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNonPublicHotCommentResp proto.InternalMessageInfo

func (m *GetNonPublicHotCommentResp) GetCommentIds() []string {
	if m != nil {
		return m.CommentIds
	}
	return nil
}

type BatchNonPublicHotCommentReq struct {
	SceneId              string   `protobuf:"bytes,1,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	PostId               []string `protobuf:"bytes,2,rep,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Count                int32    `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchNonPublicHotCommentReq) Reset()         { *m = BatchNonPublicHotCommentReq{} }
func (m *BatchNonPublicHotCommentReq) String() string { return proto.CompactTextString(m) }
func (*BatchNonPublicHotCommentReq) ProtoMessage()    {}
func (*BatchNonPublicHotCommentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{48}
}
func (m *BatchNonPublicHotCommentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchNonPublicHotCommentReq.Unmarshal(m, b)
}
func (m *BatchNonPublicHotCommentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchNonPublicHotCommentReq.Marshal(b, m, deterministic)
}
func (dst *BatchNonPublicHotCommentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchNonPublicHotCommentReq.Merge(dst, src)
}
func (m *BatchNonPublicHotCommentReq) XXX_Size() int {
	return xxx_messageInfo_BatchNonPublicHotCommentReq.Size(m)
}
func (m *BatchNonPublicHotCommentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchNonPublicHotCommentReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchNonPublicHotCommentReq proto.InternalMessageInfo

func (m *BatchNonPublicHotCommentReq) GetSceneId() string {
	if m != nil {
		return m.SceneId
	}
	return ""
}

func (m *BatchNonPublicHotCommentReq) GetPostId() []string {
	if m != nil {
		return m.PostId
	}
	return nil
}

func (m *BatchNonPublicHotCommentReq) GetCount() int32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type CommentIds struct {
	CommentIds           []string `protobuf:"bytes,1,rep,name=comment_ids,json=commentIds,proto3" json:"comment_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommentIds) Reset()         { *m = CommentIds{} }
func (m *CommentIds) String() string { return proto.CompactTextString(m) }
func (*CommentIds) ProtoMessage()    {}
func (*CommentIds) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{49}
}
func (m *CommentIds) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentIds.Unmarshal(m, b)
}
func (m *CommentIds) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentIds.Marshal(b, m, deterministic)
}
func (dst *CommentIds) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentIds.Merge(dst, src)
}
func (m *CommentIds) XXX_Size() int {
	return xxx_messageInfo_CommentIds.Size(m)
}
func (m *CommentIds) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentIds.DiscardUnknown(m)
}

var xxx_messageInfo_CommentIds proto.InternalMessageInfo

func (m *CommentIds) GetCommentIds() []string {
	if m != nil {
		return m.CommentIds
	}
	return nil
}

type BatchNonPublicHotCommentResp struct {
	CommentMap           map[string]*CommentIds `protobuf:"bytes,1,rep,name=comment_map,json=commentMap,proto3" json:"comment_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *BatchNonPublicHotCommentResp) Reset()         { *m = BatchNonPublicHotCommentResp{} }
func (m *BatchNonPublicHotCommentResp) String() string { return proto.CompactTextString(m) }
func (*BatchNonPublicHotCommentResp) ProtoMessage()    {}
func (*BatchNonPublicHotCommentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{50}
}
func (m *BatchNonPublicHotCommentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchNonPublicHotCommentResp.Unmarshal(m, b)
}
func (m *BatchNonPublicHotCommentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchNonPublicHotCommentResp.Marshal(b, m, deterministic)
}
func (dst *BatchNonPublicHotCommentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchNonPublicHotCommentResp.Merge(dst, src)
}
func (m *BatchNonPublicHotCommentResp) XXX_Size() int {
	return xxx_messageInfo_BatchNonPublicHotCommentResp.Size(m)
}
func (m *BatchNonPublicHotCommentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchNonPublicHotCommentResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchNonPublicHotCommentResp proto.InternalMessageInfo

func (m *BatchNonPublicHotCommentResp) GetCommentMap() map[string]*CommentIds {
	if m != nil {
		return m.CommentMap
	}
	return nil
}

type BanNonPublicCommentByIdReq struct {
	CommentId            string   `protobuf:"bytes,1,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	IsBan                bool     `protobuf:"varint,2,opt,name=is_ban,json=isBan,proto3" json:"is_ban,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BanNonPublicCommentByIdReq) Reset()         { *m = BanNonPublicCommentByIdReq{} }
func (m *BanNonPublicCommentByIdReq) String() string { return proto.CompactTextString(m) }
func (*BanNonPublicCommentByIdReq) ProtoMessage()    {}
func (*BanNonPublicCommentByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{51}
}
func (m *BanNonPublicCommentByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BanNonPublicCommentByIdReq.Unmarshal(m, b)
}
func (m *BanNonPublicCommentByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BanNonPublicCommentByIdReq.Marshal(b, m, deterministic)
}
func (dst *BanNonPublicCommentByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BanNonPublicCommentByIdReq.Merge(dst, src)
}
func (m *BanNonPublicCommentByIdReq) XXX_Size() int {
	return xxx_messageInfo_BanNonPublicCommentByIdReq.Size(m)
}
func (m *BanNonPublicCommentByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BanNonPublicCommentByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_BanNonPublicCommentByIdReq proto.InternalMessageInfo

func (m *BanNonPublicCommentByIdReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *BanNonPublicCommentByIdReq) GetIsBan() bool {
	if m != nil {
		return m.IsBan
	}
	return false
}

type BanNonPublicCommentByIdResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BanNonPublicCommentByIdResp) Reset()         { *m = BanNonPublicCommentByIdResp{} }
func (m *BanNonPublicCommentByIdResp) String() string { return proto.CompactTextString(m) }
func (*BanNonPublicCommentByIdResp) ProtoMessage()    {}
func (*BanNonPublicCommentByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{52}
}
func (m *BanNonPublicCommentByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BanNonPublicCommentByIdResp.Unmarshal(m, b)
}
func (m *BanNonPublicCommentByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BanNonPublicCommentByIdResp.Marshal(b, m, deterministic)
}
func (dst *BanNonPublicCommentByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BanNonPublicCommentByIdResp.Merge(dst, src)
}
func (m *BanNonPublicCommentByIdResp) XXX_Size() int {
	return xxx_messageInfo_BanNonPublicCommentByIdResp.Size(m)
}
func (m *BanNonPublicCommentByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BanNonPublicCommentByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_BanNonPublicCommentByIdResp proto.InternalMessageInfo

// 帖子下发过滤(请求的全部标记过滤，并且返回过滤信息)
type DistributePostFilterReq struct {
	FilterType           uint32   `protobuf:"varint,1,opt,name=filter_type,json=filterType,proto3" json:"filter_type,omitempty"`
	PostIdList           []string `protobuf:"bytes,2,rep,name=post_id_list,json=postIdList,proto3" json:"post_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DistributePostFilterReq) Reset()         { *m = DistributePostFilterReq{} }
func (m *DistributePostFilterReq) String() string { return proto.CompactTextString(m) }
func (*DistributePostFilterReq) ProtoMessage()    {}
func (*DistributePostFilterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{53}
}
func (m *DistributePostFilterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DistributePostFilterReq.Unmarshal(m, b)
}
func (m *DistributePostFilterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DistributePostFilterReq.Marshal(b, m, deterministic)
}
func (dst *DistributePostFilterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DistributePostFilterReq.Merge(dst, src)
}
func (m *DistributePostFilterReq) XXX_Size() int {
	return xxx_messageInfo_DistributePostFilterReq.Size(m)
}
func (m *DistributePostFilterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DistributePostFilterReq.DiscardUnknown(m)
}

var xxx_messageInfo_DistributePostFilterReq proto.InternalMessageInfo

func (m *DistributePostFilterReq) GetFilterType() uint32 {
	if m != nil {
		return m.FilterType
	}
	return 0
}

func (m *DistributePostFilterReq) GetPostIdList() []string {
	if m != nil {
		return m.PostIdList
	}
	return nil
}

type DistributePostFilterResp struct {
	PostFilterMap        map[string]bool `protobuf:"bytes,1,rep,name=post_filter_map,json=postFilterMap,proto3" json:"post_filter_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *DistributePostFilterResp) Reset()         { *m = DistributePostFilterResp{} }
func (m *DistributePostFilterResp) String() string { return proto.CompactTextString(m) }
func (*DistributePostFilterResp) ProtoMessage()    {}
func (*DistributePostFilterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{54}
}
func (m *DistributePostFilterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DistributePostFilterResp.Unmarshal(m, b)
}
func (m *DistributePostFilterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DistributePostFilterResp.Marshal(b, m, deterministic)
}
func (dst *DistributePostFilterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DistributePostFilterResp.Merge(dst, src)
}
func (m *DistributePostFilterResp) XXX_Size() int {
	return xxx_messageInfo_DistributePostFilterResp.Size(m)
}
func (m *DistributePostFilterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DistributePostFilterResp.DiscardUnknown(m)
}

var xxx_messageInfo_DistributePostFilterResp proto.InternalMessageInfo

func (m *DistributePostFilterResp) GetPostFilterMap() map[string]bool {
	if m != nil {
		return m.PostFilterMap
	}
	return nil
}

type BatchGetHotCommentSubCommentsReq struct {
	CommentIds           []string `protobuf:"bytes,1,rep,name=comment_ids,json=commentIds,proto3" json:"comment_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetHotCommentSubCommentsReq) Reset()         { *m = BatchGetHotCommentSubCommentsReq{} }
func (m *BatchGetHotCommentSubCommentsReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetHotCommentSubCommentsReq) ProtoMessage()    {}
func (*BatchGetHotCommentSubCommentsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{55}
}
func (m *BatchGetHotCommentSubCommentsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetHotCommentSubCommentsReq.Unmarshal(m, b)
}
func (m *BatchGetHotCommentSubCommentsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetHotCommentSubCommentsReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetHotCommentSubCommentsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetHotCommentSubCommentsReq.Merge(dst, src)
}
func (m *BatchGetHotCommentSubCommentsReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetHotCommentSubCommentsReq.Size(m)
}
func (m *BatchGetHotCommentSubCommentsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetHotCommentSubCommentsReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetHotCommentSubCommentsReq proto.InternalMessageInfo

func (m *BatchGetHotCommentSubCommentsReq) GetCommentIds() []string {
	if m != nil {
		return m.CommentIds
	}
	return nil
}

type SubComments struct {
	SubComment           []*CommentInfo `protobuf:"bytes,1,rep,name=sub_comment,json=subComment,proto3" json:"sub_comment,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *SubComments) Reset()         { *m = SubComments{} }
func (m *SubComments) String() string { return proto.CompactTextString(m) }
func (*SubComments) ProtoMessage()    {}
func (*SubComments) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{56}
}
func (m *SubComments) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubComments.Unmarshal(m, b)
}
func (m *SubComments) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubComments.Marshal(b, m, deterministic)
}
func (dst *SubComments) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubComments.Merge(dst, src)
}
func (m *SubComments) XXX_Size() int {
	return xxx_messageInfo_SubComments.Size(m)
}
func (m *SubComments) XXX_DiscardUnknown() {
	xxx_messageInfo_SubComments.DiscardUnknown(m)
}

var xxx_messageInfo_SubComments proto.InternalMessageInfo

func (m *SubComments) GetSubComment() []*CommentInfo {
	if m != nil {
		return m.SubComment
	}
	return nil
}

type BatchGetHotCommentSubCommentsResp struct {
	SubComments          map[string]*SubComments `protobuf:"bytes,1,rep,name=sub_comments,json=subComments,proto3" json:"sub_comments,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *BatchGetHotCommentSubCommentsResp) Reset()         { *m = BatchGetHotCommentSubCommentsResp{} }
func (m *BatchGetHotCommentSubCommentsResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetHotCommentSubCommentsResp) ProtoMessage()    {}
func (*BatchGetHotCommentSubCommentsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{57}
}
func (m *BatchGetHotCommentSubCommentsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetHotCommentSubCommentsResp.Unmarshal(m, b)
}
func (m *BatchGetHotCommentSubCommentsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetHotCommentSubCommentsResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetHotCommentSubCommentsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetHotCommentSubCommentsResp.Merge(dst, src)
}
func (m *BatchGetHotCommentSubCommentsResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetHotCommentSubCommentsResp.Size(m)
}
func (m *BatchGetHotCommentSubCommentsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetHotCommentSubCommentsResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetHotCommentSubCommentsResp proto.InternalMessageInfo

func (m *BatchGetHotCommentSubCommentsResp) GetSubComments() map[string]*SubComments {
	if m != nil {
		return m.SubComments
	}
	return nil
}

type NonPublicIndexPostInfo struct {
	Post                 *NonPublicPostInfo `protobuf:"bytes,1,opt,name=post,proto3" json:"post,omitempty"`
	HideStatus           uint32             `protobuf:"varint,2,opt,name=hide_status,json=hideStatus,proto3" json:"hide_status,omitempty"`
	OffsetId             string             `protobuf:"bytes,3,opt,name=offset_id,json=offsetId,proto3" json:"offset_id,omitempty"`
	Operator             string             `protobuf:"bytes,4,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *NonPublicIndexPostInfo) Reset()         { *m = NonPublicIndexPostInfo{} }
func (m *NonPublicIndexPostInfo) String() string { return proto.CompactTextString(m) }
func (*NonPublicIndexPostInfo) ProtoMessage()    {}
func (*NonPublicIndexPostInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{58}
}
func (m *NonPublicIndexPostInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NonPublicIndexPostInfo.Unmarshal(m, b)
}
func (m *NonPublicIndexPostInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NonPublicIndexPostInfo.Marshal(b, m, deterministic)
}
func (dst *NonPublicIndexPostInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NonPublicIndexPostInfo.Merge(dst, src)
}
func (m *NonPublicIndexPostInfo) XXX_Size() int {
	return xxx_messageInfo_NonPublicIndexPostInfo.Size(m)
}
func (m *NonPublicIndexPostInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_NonPublicIndexPostInfo.DiscardUnknown(m)
}

var xxx_messageInfo_NonPublicIndexPostInfo proto.InternalMessageInfo

func (m *NonPublicIndexPostInfo) GetPost() *NonPublicPostInfo {
	if m != nil {
		return m.Post
	}
	return nil
}

func (m *NonPublicIndexPostInfo) GetHideStatus() uint32 {
	if m != nil {
		return m.HideStatus
	}
	return 0
}

func (m *NonPublicIndexPostInfo) GetOffsetId() string {
	if m != nil {
		return m.OffsetId
	}
	return ""
}

func (m *NonPublicIndexPostInfo) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

// 运营后台 获取流帖子列表
type ListNonPublicPostIndexReq struct {
	SceneId               string       `protobuf:"bytes,1,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	SceneStream           *SceneStream `protobuf:"bytes,2,opt,name=scene_stream,json=sceneStream,proto3" json:"scene_stream,omitempty"`
	PostId                string       `protobuf:"bytes,3,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	BusinessCommonIndexId string       `protobuf:"bytes,4,opt,name=business_common_index_id,json=businessCommonIndexId,proto3" json:"business_common_index_id,omitempty"`
	Uid                   uint32       `protobuf:"varint,5,opt,name=uid,proto3" json:"uid,omitempty"`
	BeginTime             uint32       `protobuf:"varint,6,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime               uint32       `protobuf:"varint,7,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	OffsetId              string       `protobuf:"bytes,8,opt,name=offset_id,json=offsetId,proto3" json:"offset_id,omitempty"`
	Count                 uint32       `protobuf:"varint,9,opt,name=count,proto3" json:"count,omitempty"`
	HideStatus            uint32       `protobuf:"varint,10,opt,name=hide_status,json=hideStatus,proto3" json:"hide_status,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}     `json:"-"`
	XXX_unrecognized      []byte       `json:"-"`
	XXX_sizecache         int32        `json:"-"`
}

func (m *ListNonPublicPostIndexReq) Reset()         { *m = ListNonPublicPostIndexReq{} }
func (m *ListNonPublicPostIndexReq) String() string { return proto.CompactTextString(m) }
func (*ListNonPublicPostIndexReq) ProtoMessage()    {}
func (*ListNonPublicPostIndexReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{59}
}
func (m *ListNonPublicPostIndexReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListNonPublicPostIndexReq.Unmarshal(m, b)
}
func (m *ListNonPublicPostIndexReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListNonPublicPostIndexReq.Marshal(b, m, deterministic)
}
func (dst *ListNonPublicPostIndexReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListNonPublicPostIndexReq.Merge(dst, src)
}
func (m *ListNonPublicPostIndexReq) XXX_Size() int {
	return xxx_messageInfo_ListNonPublicPostIndexReq.Size(m)
}
func (m *ListNonPublicPostIndexReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListNonPublicPostIndexReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListNonPublicPostIndexReq proto.InternalMessageInfo

func (m *ListNonPublicPostIndexReq) GetSceneId() string {
	if m != nil {
		return m.SceneId
	}
	return ""
}

func (m *ListNonPublicPostIndexReq) GetSceneStream() *SceneStream {
	if m != nil {
		return m.SceneStream
	}
	return nil
}

func (m *ListNonPublicPostIndexReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *ListNonPublicPostIndexReq) GetBusinessCommonIndexId() string {
	if m != nil {
		return m.BusinessCommonIndexId
	}
	return ""
}

func (m *ListNonPublicPostIndexReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ListNonPublicPostIndexReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *ListNonPublicPostIndexReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *ListNonPublicPostIndexReq) GetOffsetId() string {
	if m != nil {
		return m.OffsetId
	}
	return ""
}

func (m *ListNonPublicPostIndexReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *ListNonPublicPostIndexReq) GetHideStatus() uint32 {
	if m != nil {
		return m.HideStatus
	}
	return 0
}

type ListNonPublicPostIndexResp struct {
	PostList             []*NonPublicIndexPostInfo `protobuf:"bytes,1,rep,name=post_list,json=postList,proto3" json:"post_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *ListNonPublicPostIndexResp) Reset()         { *m = ListNonPublicPostIndexResp{} }
func (m *ListNonPublicPostIndexResp) String() string { return proto.CompactTextString(m) }
func (*ListNonPublicPostIndexResp) ProtoMessage()    {}
func (*ListNonPublicPostIndexResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{60}
}
func (m *ListNonPublicPostIndexResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListNonPublicPostIndexResp.Unmarshal(m, b)
}
func (m *ListNonPublicPostIndexResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListNonPublicPostIndexResp.Marshal(b, m, deterministic)
}
func (dst *ListNonPublicPostIndexResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListNonPublicPostIndexResp.Merge(dst, src)
}
func (m *ListNonPublicPostIndexResp) XXX_Size() int {
	return xxx_messageInfo_ListNonPublicPostIndexResp.Size(m)
}
func (m *ListNonPublicPostIndexResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListNonPublicPostIndexResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListNonPublicPostIndexResp proto.InternalMessageInfo

func (m *ListNonPublicPostIndexResp) GetPostList() []*NonPublicIndexPostInfo {
	if m != nil {
		return m.PostList
	}
	return nil
}

type InsertNonPublicPostIndexReq struct {
	SceneId               string       `protobuf:"bytes,1,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	SceneStream           *SceneStream `protobuf:"bytes,2,opt,name=scene_stream,json=sceneStream,proto3" json:"scene_stream,omitempty"`
	PostId                string       `protobuf:"bytes,3,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CreateTime            uint32       `protobuf:"varint,4,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Uid                   uint32       `protobuf:"varint,5,opt,name=uid,proto3" json:"uid,omitempty"`
	BusinessCommonIndexId string       `protobuf:"bytes,6,opt,name=business_common_index_id,json=businessCommonIndexId,proto3" json:"business_common_index_id,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}     `json:"-"`
	XXX_unrecognized      []byte       `json:"-"`
	XXX_sizecache         int32        `json:"-"`
}

func (m *InsertNonPublicPostIndexReq) Reset()         { *m = InsertNonPublicPostIndexReq{} }
func (m *InsertNonPublicPostIndexReq) String() string { return proto.CompactTextString(m) }
func (*InsertNonPublicPostIndexReq) ProtoMessage()    {}
func (*InsertNonPublicPostIndexReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{61}
}
func (m *InsertNonPublicPostIndexReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsertNonPublicPostIndexReq.Unmarshal(m, b)
}
func (m *InsertNonPublicPostIndexReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsertNonPublicPostIndexReq.Marshal(b, m, deterministic)
}
func (dst *InsertNonPublicPostIndexReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsertNonPublicPostIndexReq.Merge(dst, src)
}
func (m *InsertNonPublicPostIndexReq) XXX_Size() int {
	return xxx_messageInfo_InsertNonPublicPostIndexReq.Size(m)
}
func (m *InsertNonPublicPostIndexReq) XXX_DiscardUnknown() {
	xxx_messageInfo_InsertNonPublicPostIndexReq.DiscardUnknown(m)
}

var xxx_messageInfo_InsertNonPublicPostIndexReq proto.InternalMessageInfo

func (m *InsertNonPublicPostIndexReq) GetSceneId() string {
	if m != nil {
		return m.SceneId
	}
	return ""
}

func (m *InsertNonPublicPostIndexReq) GetSceneStream() *SceneStream {
	if m != nil {
		return m.SceneStream
	}
	return nil
}

func (m *InsertNonPublicPostIndexReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *InsertNonPublicPostIndexReq) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *InsertNonPublicPostIndexReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *InsertNonPublicPostIndexReq) GetBusinessCommonIndexId() string {
	if m != nil {
		return m.BusinessCommonIndexId
	}
	return ""
}

type InsertNonPublicPostIndexResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InsertNonPublicPostIndexResp) Reset()         { *m = InsertNonPublicPostIndexResp{} }
func (m *InsertNonPublicPostIndexResp) String() string { return proto.CompactTextString(m) }
func (*InsertNonPublicPostIndexResp) ProtoMessage()    {}
func (*InsertNonPublicPostIndexResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{62}
}
func (m *InsertNonPublicPostIndexResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsertNonPublicPostIndexResp.Unmarshal(m, b)
}
func (m *InsertNonPublicPostIndexResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsertNonPublicPostIndexResp.Marshal(b, m, deterministic)
}
func (dst *InsertNonPublicPostIndexResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsertNonPublicPostIndexResp.Merge(dst, src)
}
func (m *InsertNonPublicPostIndexResp) XXX_Size() int {
	return xxx_messageInfo_InsertNonPublicPostIndexResp.Size(m)
}
func (m *InsertNonPublicPostIndexResp) XXX_DiscardUnknown() {
	xxx_messageInfo_InsertNonPublicPostIndexResp.DiscardUnknown(m)
}

var xxx_messageInfo_InsertNonPublicPostIndexResp proto.InternalMessageInfo

// 运营后台 在流中 隐藏帖子
type HideNonPublicPostInStreamReq struct {
	SceneId              string       `protobuf:"bytes,1,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	SceneStream          *SceneStream `protobuf:"bytes,2,opt,name=scene_stream,json=sceneStream,proto3" json:"scene_stream,omitempty"`
	PostId               string       `protobuf:"bytes,3,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	HideStatus           uint32       `protobuf:"varint,4,opt,name=hide_status,json=hideStatus,proto3" json:"hide_status,omitempty"`
	Operator             string       `protobuf:"bytes,5,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *HideNonPublicPostInStreamReq) Reset()         { *m = HideNonPublicPostInStreamReq{} }
func (m *HideNonPublicPostInStreamReq) String() string { return proto.CompactTextString(m) }
func (*HideNonPublicPostInStreamReq) ProtoMessage()    {}
func (*HideNonPublicPostInStreamReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{63}
}
func (m *HideNonPublicPostInStreamReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HideNonPublicPostInStreamReq.Unmarshal(m, b)
}
func (m *HideNonPublicPostInStreamReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HideNonPublicPostInStreamReq.Marshal(b, m, deterministic)
}
func (dst *HideNonPublicPostInStreamReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HideNonPublicPostInStreamReq.Merge(dst, src)
}
func (m *HideNonPublicPostInStreamReq) XXX_Size() int {
	return xxx_messageInfo_HideNonPublicPostInStreamReq.Size(m)
}
func (m *HideNonPublicPostInStreamReq) XXX_DiscardUnknown() {
	xxx_messageInfo_HideNonPublicPostInStreamReq.DiscardUnknown(m)
}

var xxx_messageInfo_HideNonPublicPostInStreamReq proto.InternalMessageInfo

func (m *HideNonPublicPostInStreamReq) GetSceneId() string {
	if m != nil {
		return m.SceneId
	}
	return ""
}

func (m *HideNonPublicPostInStreamReq) GetSceneStream() *SceneStream {
	if m != nil {
		return m.SceneStream
	}
	return nil
}

func (m *HideNonPublicPostInStreamReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *HideNonPublicPostInStreamReq) GetHideStatus() uint32 {
	if m != nil {
		return m.HideStatus
	}
	return 0
}

func (m *HideNonPublicPostInStreamReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type HideNonPublicPostInStreamResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HideNonPublicPostInStreamResp) Reset()         { *m = HideNonPublicPostInStreamResp{} }
func (m *HideNonPublicPostInStreamResp) String() string { return proto.CompactTextString(m) }
func (*HideNonPublicPostInStreamResp) ProtoMessage()    {}
func (*HideNonPublicPostInStreamResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{64}
}
func (m *HideNonPublicPostInStreamResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HideNonPublicPostInStreamResp.Unmarshal(m, b)
}
func (m *HideNonPublicPostInStreamResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HideNonPublicPostInStreamResp.Marshal(b, m, deterministic)
}
func (dst *HideNonPublicPostInStreamResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HideNonPublicPostInStreamResp.Merge(dst, src)
}
func (m *HideNonPublicPostInStreamResp) XXX_Size() int {
	return xxx_messageInfo_HideNonPublicPostInStreamResp.Size(m)
}
func (m *HideNonPublicPostInStreamResp) XXX_DiscardUnknown() {
	xxx_messageInfo_HideNonPublicPostInStreamResp.DiscardUnknown(m)
}

var xxx_messageInfo_HideNonPublicPostInStreamResp proto.InternalMessageInfo

type BatGetNonPostHideStatusReq struct {
	PostIdList           []string `protobuf:"bytes,1,rep,name=post_id_list,json=postIdList,proto3" json:"post_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetNonPostHideStatusReq) Reset()         { *m = BatGetNonPostHideStatusReq{} }
func (m *BatGetNonPostHideStatusReq) String() string { return proto.CompactTextString(m) }
func (*BatGetNonPostHideStatusReq) ProtoMessage()    {}
func (*BatGetNonPostHideStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{65}
}
func (m *BatGetNonPostHideStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetNonPostHideStatusReq.Unmarshal(m, b)
}
func (m *BatGetNonPostHideStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetNonPostHideStatusReq.Marshal(b, m, deterministic)
}
func (dst *BatGetNonPostHideStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetNonPostHideStatusReq.Merge(dst, src)
}
func (m *BatGetNonPostHideStatusReq) XXX_Size() int {
	return xxx_messageInfo_BatGetNonPostHideStatusReq.Size(m)
}
func (m *BatGetNonPostHideStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetNonPostHideStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetNonPostHideStatusReq proto.InternalMessageInfo

func (m *BatGetNonPostHideStatusReq) GetPostIdList() []string {
	if m != nil {
		return m.PostIdList
	}
	return nil
}

type BatGetNonPostHideStatusResp struct {
	PostHideStatusMap    map[string]uint32 `protobuf:"bytes,1,rep,name=post_hide_status_map,json=postHideStatusMap,proto3" json:"post_hide_status_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatGetNonPostHideStatusResp) Reset()         { *m = BatGetNonPostHideStatusResp{} }
func (m *BatGetNonPostHideStatusResp) String() string { return proto.CompactTextString(m) }
func (*BatGetNonPostHideStatusResp) ProtoMessage()    {}
func (*BatGetNonPostHideStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{66}
}
func (m *BatGetNonPostHideStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetNonPostHideStatusResp.Unmarshal(m, b)
}
func (m *BatGetNonPostHideStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetNonPostHideStatusResp.Marshal(b, m, deterministic)
}
func (dst *BatGetNonPostHideStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetNonPostHideStatusResp.Merge(dst, src)
}
func (m *BatGetNonPostHideStatusResp) XXX_Size() int {
	return xxx_messageInfo_BatGetNonPostHideStatusResp.Size(m)
}
func (m *BatGetNonPostHideStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetNonPostHideStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetNonPostHideStatusResp proto.InternalMessageInfo

func (m *BatGetNonPostHideStatusResp) GetPostHideStatusMap() map[string]uint32 {
	if m != nil {
		return m.PostHideStatusMap
	}
	return nil
}

// 运营后台 在流中 强插帖子
type ForceInsertNonPublicPostInStreamReq struct {
	SceneId              string       `protobuf:"bytes,1,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	SceneStream          *SceneStream `protobuf:"bytes,2,opt,name=scene_stream,json=sceneStream,proto3" json:"scene_stream,omitempty"`
	ID                   string       `protobuf:"bytes,3,opt,name=ID,proto3" json:"ID,omitempty"`
	PostId               string       `protobuf:"bytes,4,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Position             uint32       `protobuf:"varint,5,opt,name=position,proto3" json:"position,omitempty"`
	BeginTime            uint32       `protobuf:"varint,6,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32       `protobuf:"varint,7,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	IsDel                bool         `protobuf:"varint,8,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
	Operator             string       `protobuf:"bytes,9,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ForceInsertNonPublicPostInStreamReq) Reset()         { *m = ForceInsertNonPublicPostInStreamReq{} }
func (m *ForceInsertNonPublicPostInStreamReq) String() string { return proto.CompactTextString(m) }
func (*ForceInsertNonPublicPostInStreamReq) ProtoMessage()    {}
func (*ForceInsertNonPublicPostInStreamReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{67}
}
func (m *ForceInsertNonPublicPostInStreamReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ForceInsertNonPublicPostInStreamReq.Unmarshal(m, b)
}
func (m *ForceInsertNonPublicPostInStreamReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ForceInsertNonPublicPostInStreamReq.Marshal(b, m, deterministic)
}
func (dst *ForceInsertNonPublicPostInStreamReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ForceInsertNonPublicPostInStreamReq.Merge(dst, src)
}
func (m *ForceInsertNonPublicPostInStreamReq) XXX_Size() int {
	return xxx_messageInfo_ForceInsertNonPublicPostInStreamReq.Size(m)
}
func (m *ForceInsertNonPublicPostInStreamReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ForceInsertNonPublicPostInStreamReq.DiscardUnknown(m)
}

var xxx_messageInfo_ForceInsertNonPublicPostInStreamReq proto.InternalMessageInfo

func (m *ForceInsertNonPublicPostInStreamReq) GetSceneId() string {
	if m != nil {
		return m.SceneId
	}
	return ""
}

func (m *ForceInsertNonPublicPostInStreamReq) GetSceneStream() *SceneStream {
	if m != nil {
		return m.SceneStream
	}
	return nil
}

func (m *ForceInsertNonPublicPostInStreamReq) GetID() string {
	if m != nil {
		return m.ID
	}
	return ""
}

func (m *ForceInsertNonPublicPostInStreamReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *ForceInsertNonPublicPostInStreamReq) GetPosition() uint32 {
	if m != nil {
		return m.Position
	}
	return 0
}

func (m *ForceInsertNonPublicPostInStreamReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *ForceInsertNonPublicPostInStreamReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *ForceInsertNonPublicPostInStreamReq) GetIsDel() bool {
	if m != nil {
		return m.IsDel
	}
	return false
}

func (m *ForceInsertNonPublicPostInStreamReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type ForceInsertNonPublicPostInStreamResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ForceInsertNonPublicPostInStreamResp) Reset()         { *m = ForceInsertNonPublicPostInStreamResp{} }
func (m *ForceInsertNonPublicPostInStreamResp) String() string { return proto.CompactTextString(m) }
func (*ForceInsertNonPublicPostInStreamResp) ProtoMessage()    {}
func (*ForceInsertNonPublicPostInStreamResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{68}
}
func (m *ForceInsertNonPublicPostInStreamResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ForceInsertNonPublicPostInStreamResp.Unmarshal(m, b)
}
func (m *ForceInsertNonPublicPostInStreamResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ForceInsertNonPublicPostInStreamResp.Marshal(b, m, deterministic)
}
func (dst *ForceInsertNonPublicPostInStreamResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ForceInsertNonPublicPostInStreamResp.Merge(dst, src)
}
func (m *ForceInsertNonPublicPostInStreamResp) XXX_Size() int {
	return xxx_messageInfo_ForceInsertNonPublicPostInStreamResp.Size(m)
}
func (m *ForceInsertNonPublicPostInStreamResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ForceInsertNonPublicPostInStreamResp.DiscardUnknown(m)
}

var xxx_messageInfo_ForceInsertNonPublicPostInStreamResp proto.InternalMessageInfo

type ListNonPublicPostForceInsertInStreamReq struct {
	SceneId              string       `protobuf:"bytes,1,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	SceneStream          *SceneStream `protobuf:"bytes,2,opt,name=scene_stream,json=sceneStream,proto3" json:"scene_stream,omitempty"`
	PostId               string       `protobuf:"bytes,3,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	BeginTime            uint32       `protobuf:"varint,4,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32       `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	OffsetId             string       `protobuf:"bytes,6,opt,name=offset_id,json=offsetId,proto3" json:"offset_id,omitempty"`
	Count                uint32       `protobuf:"varint,7,opt,name=count,proto3" json:"count,omitempty"`
	InsertStatus         uint32       `protobuf:"varint,8,opt,name=insert_status,json=insertStatus,proto3" json:"insert_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ListNonPublicPostForceInsertInStreamReq) Reset() {
	*m = ListNonPublicPostForceInsertInStreamReq{}
}
func (m *ListNonPublicPostForceInsertInStreamReq) String() string { return proto.CompactTextString(m) }
func (*ListNonPublicPostForceInsertInStreamReq) ProtoMessage()    {}
func (*ListNonPublicPostForceInsertInStreamReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{69}
}
func (m *ListNonPublicPostForceInsertInStreamReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListNonPublicPostForceInsertInStreamReq.Unmarshal(m, b)
}
func (m *ListNonPublicPostForceInsertInStreamReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListNonPublicPostForceInsertInStreamReq.Marshal(b, m, deterministic)
}
func (dst *ListNonPublicPostForceInsertInStreamReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListNonPublicPostForceInsertInStreamReq.Merge(dst, src)
}
func (m *ListNonPublicPostForceInsertInStreamReq) XXX_Size() int {
	return xxx_messageInfo_ListNonPublicPostForceInsertInStreamReq.Size(m)
}
func (m *ListNonPublicPostForceInsertInStreamReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListNonPublicPostForceInsertInStreamReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListNonPublicPostForceInsertInStreamReq proto.InternalMessageInfo

func (m *ListNonPublicPostForceInsertInStreamReq) GetSceneId() string {
	if m != nil {
		return m.SceneId
	}
	return ""
}

func (m *ListNonPublicPostForceInsertInStreamReq) GetSceneStream() *SceneStream {
	if m != nil {
		return m.SceneStream
	}
	return nil
}

func (m *ListNonPublicPostForceInsertInStreamReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *ListNonPublicPostForceInsertInStreamReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *ListNonPublicPostForceInsertInStreamReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *ListNonPublicPostForceInsertInStreamReq) GetOffsetId() string {
	if m != nil {
		return m.OffsetId
	}
	return ""
}

func (m *ListNonPublicPostForceInsertInStreamReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *ListNonPublicPostForceInsertInStreamReq) GetInsertStatus() uint32 {
	if m != nil {
		return m.InsertStatus
	}
	return 0
}

type NonPublicForceInsertPostInfo struct {
	Post                 *NonPublicPostInfo `protobuf:"bytes,1,opt,name=post,proto3" json:"post,omitempty"`
	ID                   string             `protobuf:"bytes,2,opt,name=ID,proto3" json:"ID,omitempty"`
	InsertStatus         uint32             `protobuf:"varint,3,opt,name=insert_status,json=insertStatus,proto3" json:"insert_status,omitempty"`
	OffsetId             string             `protobuf:"bytes,4,opt,name=offset_id,json=offsetId,proto3" json:"offset_id,omitempty"`
	Position             uint32             `protobuf:"varint,5,opt,name=position,proto3" json:"position,omitempty"`
	BeginTime            uint32             `protobuf:"varint,6,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32             `protobuf:"varint,7,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Operator             string             `protobuf:"bytes,8,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *NonPublicForceInsertPostInfo) Reset()         { *m = NonPublicForceInsertPostInfo{} }
func (m *NonPublicForceInsertPostInfo) String() string { return proto.CompactTextString(m) }
func (*NonPublicForceInsertPostInfo) ProtoMessage()    {}
func (*NonPublicForceInsertPostInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{70}
}
func (m *NonPublicForceInsertPostInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NonPublicForceInsertPostInfo.Unmarshal(m, b)
}
func (m *NonPublicForceInsertPostInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NonPublicForceInsertPostInfo.Marshal(b, m, deterministic)
}
func (dst *NonPublicForceInsertPostInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NonPublicForceInsertPostInfo.Merge(dst, src)
}
func (m *NonPublicForceInsertPostInfo) XXX_Size() int {
	return xxx_messageInfo_NonPublicForceInsertPostInfo.Size(m)
}
func (m *NonPublicForceInsertPostInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_NonPublicForceInsertPostInfo.DiscardUnknown(m)
}

var xxx_messageInfo_NonPublicForceInsertPostInfo proto.InternalMessageInfo

func (m *NonPublicForceInsertPostInfo) GetPost() *NonPublicPostInfo {
	if m != nil {
		return m.Post
	}
	return nil
}

func (m *NonPublicForceInsertPostInfo) GetID() string {
	if m != nil {
		return m.ID
	}
	return ""
}

func (m *NonPublicForceInsertPostInfo) GetInsertStatus() uint32 {
	if m != nil {
		return m.InsertStatus
	}
	return 0
}

func (m *NonPublicForceInsertPostInfo) GetOffsetId() string {
	if m != nil {
		return m.OffsetId
	}
	return ""
}

func (m *NonPublicForceInsertPostInfo) GetPosition() uint32 {
	if m != nil {
		return m.Position
	}
	return 0
}

func (m *NonPublicForceInsertPostInfo) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *NonPublicForceInsertPostInfo) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *NonPublicForceInsertPostInfo) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type ListNonPublicPostForceInsertInStreamResp struct {
	PostList             []*NonPublicForceInsertPostInfo `protobuf:"bytes,1,rep,name=post_list,json=postList,proto3" json:"post_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *ListNonPublicPostForceInsertInStreamResp) Reset() {
	*m = ListNonPublicPostForceInsertInStreamResp{}
}
func (m *ListNonPublicPostForceInsertInStreamResp) String() string { return proto.CompactTextString(m) }
func (*ListNonPublicPostForceInsertInStreamResp) ProtoMessage()    {}
func (*ListNonPublicPostForceInsertInStreamResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_non_public_content_ebea57b8c4eccb67, []int{71}
}
func (m *ListNonPublicPostForceInsertInStreamResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListNonPublicPostForceInsertInStreamResp.Unmarshal(m, b)
}
func (m *ListNonPublicPostForceInsertInStreamResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListNonPublicPostForceInsertInStreamResp.Marshal(b, m, deterministic)
}
func (dst *ListNonPublicPostForceInsertInStreamResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListNonPublicPostForceInsertInStreamResp.Merge(dst, src)
}
func (m *ListNonPublicPostForceInsertInStreamResp) XXX_Size() int {
	return xxx_messageInfo_ListNonPublicPostForceInsertInStreamResp.Size(m)
}
func (m *ListNonPublicPostForceInsertInStreamResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListNonPublicPostForceInsertInStreamResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListNonPublicPostForceInsertInStreamResp proto.InternalMessageInfo

func (m *ListNonPublicPostForceInsertInStreamResp) GetPostList() []*NonPublicForceInsertPostInfo {
	if m != nil {
		return m.PostList
	}
	return nil
}

func init() {
	proto.RegisterType((*UpsertNonPublicHotCommentReq)(nil), "ugc.non_public_content.UpsertNonPublicHotCommentReq")
	proto.RegisterType((*UpsertNonPublicHotCommentResp)(nil), "ugc.non_public_content.UpsertNonPublicHotCommentResp")
	proto.RegisterType((*AddNonPublicAttitudeReq)(nil), "ugc.non_public_content.AddNonPublicAttitudeReq")
	proto.RegisterType((*AddNonPublicAttitudeResp)(nil), "ugc.non_public_content.AddNonPublicAttitudeResp")
	proto.RegisterType((*DeleteNonPublicHotCommentReq)(nil), "ugc.non_public_content.DeleteNonPublicHotCommentReq")
	proto.RegisterType((*DeleteNonPublicHotCommentResp)(nil), "ugc.non_public_content.DeleteNonPublicHotCommentResp")
	proto.RegisterType((*GetAndUpdateInsertPostsReq)(nil), "ugc.non_public_content.GetAndUpdateInsertPostsReq")
	proto.RegisterType((*GetAndUpdateInsertPostsResp)(nil), "ugc.non_public_content.GetAndUpdateInsertPostsResp")
	proto.RegisterType((*NonPublicAttachment)(nil), "ugc.non_public_content.NonPublicAttachment")
	proto.RegisterType((*NonPublicRichTextWords)(nil), "ugc.non_public_content.NonPublicRichTextWords")
	proto.RegisterType((*NonPublicURLCard)(nil), "ugc.non_public_content.NonPublicURLCard")
	proto.RegisterType((*NonPublicRichTextElement)(nil), "ugc.non_public_content.NonPublicRichTextElement")
	proto.RegisterType((*SceneStream)(nil), "ugc.non_public_content.SceneStream")
	proto.RegisterType((*AddNonPublicPostReq)(nil), "ugc.non_public_content.AddNonPublicPostReq")
	proto.RegisterType((*AddNonPublicPostResp)(nil), "ugc.non_public_content.AddNonPublicPostResp")
	proto.RegisterType((*AddNonPublicPostDirectlyReq)(nil), "ugc.non_public_content.AddNonPublicPostDirectlyReq")
	proto.RegisterType((*AddNonPublicPostDirectlyResp)(nil), "ugc.non_public_content.AddNonPublicPostDirectlyResp")
	proto.RegisterType((*UpdateNonPublicPostUpdateTimeReq)(nil), "ugc.non_public_content.UpdateNonPublicPostUpdateTimeReq")
	proto.RegisterType((*UpdateNonPublicPostUpdateTimeResp)(nil), "ugc.non_public_content.UpdateNonPublicPostUpdateTimeResp")
	proto.RegisterType((*GetSceneStreamPostRecordReq)(nil), "ugc.non_public_content.GetSceneStreamPostRecordReq")
	proto.RegisterType((*GetSceneStreamPostRecordResp)(nil), "ugc.non_public_content.GetSceneStreamPostRecordResp")
	proto.RegisterType((*NonPublicMarkAttachmentUploadedReq)(nil), "ugc.non_public_content.NonPublicMarkAttachmentUploadedReq")
	proto.RegisterType((*NonPublicMarkAttachmentUploadedResp)(nil), "ugc.non_public_content.NonPublicMarkAttachmentUploadedResp")
	proto.RegisterType((*GetNonPublicPostByIdReq)(nil), "ugc.non_public_content.GetNonPublicPostByIdReq")
	proto.RegisterType((*GetNonPublicPostByIdResp)(nil), "ugc.non_public_content.GetNonPublicPostByIdResp")
	proto.RegisterType((*BatGetNonPublicPostListByIdReq)(nil), "ugc.non_public_content.BatGetNonPublicPostListByIdReq")
	proto.RegisterType((*BatGetNonPublicPostListByIdResp)(nil), "ugc.non_public_content.BatGetNonPublicPostListByIdResp")
	proto.RegisterType((*NonPublicBanPostByIdReq)(nil), "ugc.non_public_content.NonPublicBanPostByIdReq")
	proto.RegisterType((*NonPublicBanPostByIdResp)(nil), "ugc.non_public_content.NonPublicBanPostByIdResp")
	proto.RegisterType((*AddNonPublishStickyContentReq)(nil), "ugc.non_public_content.AddNonPublishStickyContentReq")
	proto.RegisterType((*AddNonPublishStickyContentResp)(nil), "ugc.non_public_content.AddNonPublishStickyContentResp")
	proto.RegisterType((*RemoveNonPublishStickyContentReq)(nil), "ugc.non_public_content.RemoveNonPublishStickyContentReq")
	proto.RegisterType((*RemoveNonPublishStickyContentResp)(nil), "ugc.non_public_content.RemoveNonPublishStickyContentResp")
	proto.RegisterType((*GetNonPublishStickyContentReq)(nil), "ugc.non_public_content.GetNonPublishStickyContentReq")
	proto.RegisterType((*GetNonPublishStickyContentResp)(nil), "ugc.non_public_content.GetNonPublishStickyContentResp")
	proto.RegisterType((*UpdateNonPublicPostMachineAuditByIdReq)(nil), "ugc.non_public_content.UpdateNonPublicPostMachineAuditByIdReq")
	proto.RegisterType((*UpdateNonPublicPostMachineAuditByIdResp)(nil), "ugc.non_public_content.UpdateNonPublicPostMachineAuditByIdResp")
	proto.RegisterType((*NonPublicPostInfo)(nil), "ugc.non_public_content.NonPublicPostInfo")
	proto.RegisterType((*AddNonPublicCommentReq)(nil), "ugc.non_public_content.AddNonPublicCommentReq")
	proto.RegisterType((*AddNonPublicCommentResp)(nil), "ugc.non_public_content.AddNonPublicCommentResp")
	proto.RegisterType((*AttachmentInfo)(nil), "ugc.non_public_content.AttachmentInfo")
	proto.RegisterType((*CommentInfo)(nil), "ugc.non_public_content.CommentInfo")
	proto.RegisterType((*GetNonPublicCommentListReq)(nil), "ugc.non_public_content.GetNonPublicCommentListReq")
	proto.RegisterType((*GetNonPublicCommentListResp)(nil), "ugc.non_public_content.GetNonPublicCommentListResp")
	proto.RegisterType((*BatchGetNonPublicCommentByIdsReq)(nil), "ugc.non_public_content.BatchGetNonPublicCommentByIdsReq")
	proto.RegisterType((*BatchGetNonPublicCommentByIdsResp)(nil), "ugc.non_public_content.BatchGetNonPublicCommentByIdsResp")
	proto.RegisterMapType((map[string]*CommentInfo)(nil), "ugc.non_public_content.BatchGetNonPublicCommentByIdsResp.CommentInfoMapEntry")
	proto.RegisterType((*GetNonPublicHotCommentReq)(nil), "ugc.non_public_content.GetNonPublicHotCommentReq")
	proto.RegisterType((*GetNonPublicHotCommentResp)(nil), "ugc.non_public_content.GetNonPublicHotCommentResp")
	proto.RegisterType((*BatchNonPublicHotCommentReq)(nil), "ugc.non_public_content.BatchNonPublicHotCommentReq")
	proto.RegisterType((*CommentIds)(nil), "ugc.non_public_content.CommentIds")
	proto.RegisterType((*BatchNonPublicHotCommentResp)(nil), "ugc.non_public_content.BatchNonPublicHotCommentResp")
	proto.RegisterMapType((map[string]*CommentIds)(nil), "ugc.non_public_content.BatchNonPublicHotCommentResp.CommentMapEntry")
	proto.RegisterType((*BanNonPublicCommentByIdReq)(nil), "ugc.non_public_content.BanNonPublicCommentByIdReq")
	proto.RegisterType((*BanNonPublicCommentByIdResp)(nil), "ugc.non_public_content.BanNonPublicCommentByIdResp")
	proto.RegisterType((*DistributePostFilterReq)(nil), "ugc.non_public_content.DistributePostFilterReq")
	proto.RegisterType((*DistributePostFilterResp)(nil), "ugc.non_public_content.DistributePostFilterResp")
	proto.RegisterMapType((map[string]bool)(nil), "ugc.non_public_content.DistributePostFilterResp.PostFilterMapEntry")
	proto.RegisterType((*BatchGetHotCommentSubCommentsReq)(nil), "ugc.non_public_content.BatchGetHotCommentSubCommentsReq")
	proto.RegisterType((*SubComments)(nil), "ugc.non_public_content.SubComments")
	proto.RegisterType((*BatchGetHotCommentSubCommentsResp)(nil), "ugc.non_public_content.BatchGetHotCommentSubCommentsResp")
	proto.RegisterMapType((map[string]*SubComments)(nil), "ugc.non_public_content.BatchGetHotCommentSubCommentsResp.SubCommentsEntry")
	proto.RegisterType((*NonPublicIndexPostInfo)(nil), "ugc.non_public_content.NonPublicIndexPostInfo")
	proto.RegisterType((*ListNonPublicPostIndexReq)(nil), "ugc.non_public_content.ListNonPublicPostIndexReq")
	proto.RegisterType((*ListNonPublicPostIndexResp)(nil), "ugc.non_public_content.ListNonPublicPostIndexResp")
	proto.RegisterType((*InsertNonPublicPostIndexReq)(nil), "ugc.non_public_content.InsertNonPublicPostIndexReq")
	proto.RegisterType((*InsertNonPublicPostIndexResp)(nil), "ugc.non_public_content.InsertNonPublicPostIndexResp")
	proto.RegisterType((*HideNonPublicPostInStreamReq)(nil), "ugc.non_public_content.HideNonPublicPostInStreamReq")
	proto.RegisterType((*HideNonPublicPostInStreamResp)(nil), "ugc.non_public_content.HideNonPublicPostInStreamResp")
	proto.RegisterType((*BatGetNonPostHideStatusReq)(nil), "ugc.non_public_content.BatGetNonPostHideStatusReq")
	proto.RegisterType((*BatGetNonPostHideStatusResp)(nil), "ugc.non_public_content.BatGetNonPostHideStatusResp")
	proto.RegisterMapType((map[string]uint32)(nil), "ugc.non_public_content.BatGetNonPostHideStatusResp.PostHideStatusMapEntry")
	proto.RegisterType((*ForceInsertNonPublicPostInStreamReq)(nil), "ugc.non_public_content.ForceInsertNonPublicPostInStreamReq")
	proto.RegisterType((*ForceInsertNonPublicPostInStreamResp)(nil), "ugc.non_public_content.ForceInsertNonPublicPostInStreamResp")
	proto.RegisterType((*ListNonPublicPostForceInsertInStreamReq)(nil), "ugc.non_public_content.ListNonPublicPostForceInsertInStreamReq")
	proto.RegisterType((*NonPublicForceInsertPostInfo)(nil), "ugc.non_public_content.NonPublicForceInsertPostInfo")
	proto.RegisterType((*ListNonPublicPostForceInsertInStreamResp)(nil), "ugc.non_public_content.ListNonPublicPostForceInsertInStreamResp")
	proto.RegisterEnum("ugc.non_public_content.DistributePostFilterType", DistributePostFilterType_name, DistributePostFilterType_value)
	proto.RegisterEnum("ugc.non_public_content.NonPublicPostHideStatus", NonPublicPostHideStatus_name, NonPublicPostHideStatus_value)
	proto.RegisterEnum("ugc.non_public_content.ForceInsertStatus", ForceInsertStatus_name, ForceInsertStatus_value)
	proto.RegisterEnum("ugc.non_public_content.AttachmentInfo_AttachmentType", AttachmentInfo_AttachmentType_name, AttachmentInfo_AttachmentType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// UgcNonPublicContentClient is the client API for UgcNonPublicContent service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UgcNonPublicContentClient interface {
	// 帖子
	AddNonPublicPost(ctx context.Context, in *AddNonPublicPostReq, opts ...grpc.CallOption) (*AddNonPublicPostResp, error)
	AddNonPublicPostDirectly(ctx context.Context, in *AddNonPublicPostDirectlyReq, opts ...grpc.CallOption) (*AddNonPublicPostDirectlyResp, error)
	UpdateNonPublicPostUpdateTime(ctx context.Context, in *UpdateNonPublicPostUpdateTimeReq, opts ...grpc.CallOption) (*UpdateNonPublicPostUpdateTimeResp, error)
	GetSceneStreamPostRecord(ctx context.Context, in *GetSceneStreamPostRecordReq, opts ...grpc.CallOption) (*GetSceneStreamPostRecordResp, error)
	NonPublicMarkAttachmentUploaded(ctx context.Context, in *NonPublicMarkAttachmentUploadedReq, opts ...grpc.CallOption) (*NonPublicMarkAttachmentUploadedResp, error)
	GetNonPublicPostById(ctx context.Context, in *GetNonPublicPostByIdReq, opts ...grpc.CallOption) (*GetNonPublicPostByIdResp, error)
	BatGetNonPublicPostListById(ctx context.Context, in *BatGetNonPublicPostListByIdReq, opts ...grpc.CallOption) (*BatGetNonPublicPostListByIdResp, error)
	NonPublicBanPostById(ctx context.Context, in *NonPublicBanPostByIdReq, opts ...grpc.CallOption) (*NonPublicBanPostByIdResp, error)
	AddNonPublicStickyContent(ctx context.Context, in *AddNonPublishStickyContentReq, opts ...grpc.CallOption) (*AddNonPublishStickyContentResp, error)
	RemoveNonPublishStickyContent(ctx context.Context, in *RemoveNonPublishStickyContentReq, opts ...grpc.CallOption) (*RemoveNonPublishStickyContentResp, error)
	GetNonPublishStickyContent(ctx context.Context, in *GetNonPublishStickyContentReq, opts ...grpc.CallOption) (*GetNonPublishStickyContentResp, error)
	UpdateNonPublicPostMachineAuditById(ctx context.Context, in *UpdateNonPublicPostMachineAuditByIdReq, opts ...grpc.CallOption) (*UpdateNonPublicPostMachineAuditByIdResp, error)
	GetAndUpdateInsertPosts(ctx context.Context, in *GetAndUpdateInsertPostsReq, opts ...grpc.CallOption) (*GetAndUpdateInsertPostsResp, error)
	DistributePostFilter(ctx context.Context, in *DistributePostFilterReq, opts ...grpc.CallOption) (*DistributePostFilterResp, error)
	// 评论
	AddNonPublicComment(ctx context.Context, in *AddNonPublicCommentReq, opts ...grpc.CallOption) (*AddNonPublicCommentResp, error)
	BanNonPublicCommentById(ctx context.Context, in *BanNonPublicCommentByIdReq, opts ...grpc.CallOption) (*BanNonPublicCommentByIdResp, error)
	GetNonPublicCommentList(ctx context.Context, in *GetNonPublicCommentListReq, opts ...grpc.CallOption) (*GetNonPublicCommentListResp, error)
	BatchGetNonPublicCommentByIds(ctx context.Context, in *BatchGetNonPublicCommentByIdsReq, opts ...grpc.CallOption) (*BatchGetNonPublicCommentByIdsResp, error)
	UpsertNonPublicHotComment(ctx context.Context, in *UpsertNonPublicHotCommentReq, opts ...grpc.CallOption) (*UpsertNonPublicHotCommentResp, error)
	DeleteNonPublicHotComment(ctx context.Context, in *DeleteNonPublicHotCommentReq, opts ...grpc.CallOption) (*DeleteNonPublicHotCommentResp, error)
	GetNonPublicHotComment(ctx context.Context, in *GetNonPublicHotCommentReq, opts ...grpc.CallOption) (*GetNonPublicHotCommentResp, error)
	BatchNonPublicHotComment(ctx context.Context, in *BatchNonPublicHotCommentReq, opts ...grpc.CallOption) (*BatchNonPublicHotCommentResp, error)
	// 点赞
	AddNonPublicAttitude(ctx context.Context, in *AddNonPublicAttitudeReq, opts ...grpc.CallOption) (*AddNonPublicAttitudeResp, error)
	BatchGetHotCommentSubComments(ctx context.Context, in *BatchGetHotCommentSubCommentsReq, opts ...grpc.CallOption) (*BatchGetHotCommentSubCommentsResp, error)
	// -----------运营后台帖子列表-----------
	ListNonPublicPostForceInsertInStream(ctx context.Context, in *ListNonPublicPostForceInsertInStreamReq, opts ...grpc.CallOption) (*ListNonPublicPostForceInsertInStreamResp, error)
	ForceInsertNonPublicPostInStream(ctx context.Context, in *ForceInsertNonPublicPostInStreamReq, opts ...grpc.CallOption) (*ForceInsertNonPublicPostInStreamResp, error)
	HideNonPublicPostInStream(ctx context.Context, in *HideNonPublicPostInStreamReq, opts ...grpc.CallOption) (*HideNonPublicPostInStreamResp, error)
	BatGetNonPostHideStatus(ctx context.Context, in *BatGetNonPostHideStatusReq, opts ...grpc.CallOption) (*BatGetNonPostHideStatusResp, error)
	ListNonPublicPostIndex(ctx context.Context, in *ListNonPublicPostIndexReq, opts ...grpc.CallOption) (*ListNonPublicPostIndexResp, error)
	InsertNonPublicPostIndex(ctx context.Context, in *InsertNonPublicPostIndexReq, opts ...grpc.CallOption) (*InsertNonPublicPostIndexResp, error)
}

type ugcNonPublicContentClient struct {
	cc *grpc.ClientConn
}

func NewUgcNonPublicContentClient(cc *grpc.ClientConn) UgcNonPublicContentClient {
	return &ugcNonPublicContentClient{cc}
}

func (c *ugcNonPublicContentClient) AddNonPublicPost(ctx context.Context, in *AddNonPublicPostReq, opts ...grpc.CallOption) (*AddNonPublicPostResp, error) {
	out := new(AddNonPublicPostResp)
	err := c.cc.Invoke(ctx, "/ugc.non_public_content.UgcNonPublicContent/AddNonPublicPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcNonPublicContentClient) AddNonPublicPostDirectly(ctx context.Context, in *AddNonPublicPostDirectlyReq, opts ...grpc.CallOption) (*AddNonPublicPostDirectlyResp, error) {
	out := new(AddNonPublicPostDirectlyResp)
	err := c.cc.Invoke(ctx, "/ugc.non_public_content.UgcNonPublicContent/AddNonPublicPostDirectly", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcNonPublicContentClient) UpdateNonPublicPostUpdateTime(ctx context.Context, in *UpdateNonPublicPostUpdateTimeReq, opts ...grpc.CallOption) (*UpdateNonPublicPostUpdateTimeResp, error) {
	out := new(UpdateNonPublicPostUpdateTimeResp)
	err := c.cc.Invoke(ctx, "/ugc.non_public_content.UgcNonPublicContent/UpdateNonPublicPostUpdateTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcNonPublicContentClient) GetSceneStreamPostRecord(ctx context.Context, in *GetSceneStreamPostRecordReq, opts ...grpc.CallOption) (*GetSceneStreamPostRecordResp, error) {
	out := new(GetSceneStreamPostRecordResp)
	err := c.cc.Invoke(ctx, "/ugc.non_public_content.UgcNonPublicContent/GetSceneStreamPostRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcNonPublicContentClient) NonPublicMarkAttachmentUploaded(ctx context.Context, in *NonPublicMarkAttachmentUploadedReq, opts ...grpc.CallOption) (*NonPublicMarkAttachmentUploadedResp, error) {
	out := new(NonPublicMarkAttachmentUploadedResp)
	err := c.cc.Invoke(ctx, "/ugc.non_public_content.UgcNonPublicContent/NonPublicMarkAttachmentUploaded", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcNonPublicContentClient) GetNonPublicPostById(ctx context.Context, in *GetNonPublicPostByIdReq, opts ...grpc.CallOption) (*GetNonPublicPostByIdResp, error) {
	out := new(GetNonPublicPostByIdResp)
	err := c.cc.Invoke(ctx, "/ugc.non_public_content.UgcNonPublicContent/GetNonPublicPostById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcNonPublicContentClient) BatGetNonPublicPostListById(ctx context.Context, in *BatGetNonPublicPostListByIdReq, opts ...grpc.CallOption) (*BatGetNonPublicPostListByIdResp, error) {
	out := new(BatGetNonPublicPostListByIdResp)
	err := c.cc.Invoke(ctx, "/ugc.non_public_content.UgcNonPublicContent/BatGetNonPublicPostListById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcNonPublicContentClient) NonPublicBanPostById(ctx context.Context, in *NonPublicBanPostByIdReq, opts ...grpc.CallOption) (*NonPublicBanPostByIdResp, error) {
	out := new(NonPublicBanPostByIdResp)
	err := c.cc.Invoke(ctx, "/ugc.non_public_content.UgcNonPublicContent/NonPublicBanPostById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcNonPublicContentClient) AddNonPublicStickyContent(ctx context.Context, in *AddNonPublishStickyContentReq, opts ...grpc.CallOption) (*AddNonPublishStickyContentResp, error) {
	out := new(AddNonPublishStickyContentResp)
	err := c.cc.Invoke(ctx, "/ugc.non_public_content.UgcNonPublicContent/AddNonPublicStickyContent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcNonPublicContentClient) RemoveNonPublishStickyContent(ctx context.Context, in *RemoveNonPublishStickyContentReq, opts ...grpc.CallOption) (*RemoveNonPublishStickyContentResp, error) {
	out := new(RemoveNonPublishStickyContentResp)
	err := c.cc.Invoke(ctx, "/ugc.non_public_content.UgcNonPublicContent/RemoveNonPublishStickyContent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcNonPublicContentClient) GetNonPublishStickyContent(ctx context.Context, in *GetNonPublishStickyContentReq, opts ...grpc.CallOption) (*GetNonPublishStickyContentResp, error) {
	out := new(GetNonPublishStickyContentResp)
	err := c.cc.Invoke(ctx, "/ugc.non_public_content.UgcNonPublicContent/GetNonPublishStickyContent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcNonPublicContentClient) UpdateNonPublicPostMachineAuditById(ctx context.Context, in *UpdateNonPublicPostMachineAuditByIdReq, opts ...grpc.CallOption) (*UpdateNonPublicPostMachineAuditByIdResp, error) {
	out := new(UpdateNonPublicPostMachineAuditByIdResp)
	err := c.cc.Invoke(ctx, "/ugc.non_public_content.UgcNonPublicContent/UpdateNonPublicPostMachineAuditById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcNonPublicContentClient) GetAndUpdateInsertPosts(ctx context.Context, in *GetAndUpdateInsertPostsReq, opts ...grpc.CallOption) (*GetAndUpdateInsertPostsResp, error) {
	out := new(GetAndUpdateInsertPostsResp)
	err := c.cc.Invoke(ctx, "/ugc.non_public_content.UgcNonPublicContent/GetAndUpdateInsertPosts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcNonPublicContentClient) DistributePostFilter(ctx context.Context, in *DistributePostFilterReq, opts ...grpc.CallOption) (*DistributePostFilterResp, error) {
	out := new(DistributePostFilterResp)
	err := c.cc.Invoke(ctx, "/ugc.non_public_content.UgcNonPublicContent/DistributePostFilter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcNonPublicContentClient) AddNonPublicComment(ctx context.Context, in *AddNonPublicCommentReq, opts ...grpc.CallOption) (*AddNonPublicCommentResp, error) {
	out := new(AddNonPublicCommentResp)
	err := c.cc.Invoke(ctx, "/ugc.non_public_content.UgcNonPublicContent/AddNonPublicComment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcNonPublicContentClient) BanNonPublicCommentById(ctx context.Context, in *BanNonPublicCommentByIdReq, opts ...grpc.CallOption) (*BanNonPublicCommentByIdResp, error) {
	out := new(BanNonPublicCommentByIdResp)
	err := c.cc.Invoke(ctx, "/ugc.non_public_content.UgcNonPublicContent/BanNonPublicCommentById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcNonPublicContentClient) GetNonPublicCommentList(ctx context.Context, in *GetNonPublicCommentListReq, opts ...grpc.CallOption) (*GetNonPublicCommentListResp, error) {
	out := new(GetNonPublicCommentListResp)
	err := c.cc.Invoke(ctx, "/ugc.non_public_content.UgcNonPublicContent/GetNonPublicCommentList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcNonPublicContentClient) BatchGetNonPublicCommentByIds(ctx context.Context, in *BatchGetNonPublicCommentByIdsReq, opts ...grpc.CallOption) (*BatchGetNonPublicCommentByIdsResp, error) {
	out := new(BatchGetNonPublicCommentByIdsResp)
	err := c.cc.Invoke(ctx, "/ugc.non_public_content.UgcNonPublicContent/BatchGetNonPublicCommentByIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcNonPublicContentClient) UpsertNonPublicHotComment(ctx context.Context, in *UpsertNonPublicHotCommentReq, opts ...grpc.CallOption) (*UpsertNonPublicHotCommentResp, error) {
	out := new(UpsertNonPublicHotCommentResp)
	err := c.cc.Invoke(ctx, "/ugc.non_public_content.UgcNonPublicContent/UpsertNonPublicHotComment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcNonPublicContentClient) DeleteNonPublicHotComment(ctx context.Context, in *DeleteNonPublicHotCommentReq, opts ...grpc.CallOption) (*DeleteNonPublicHotCommentResp, error) {
	out := new(DeleteNonPublicHotCommentResp)
	err := c.cc.Invoke(ctx, "/ugc.non_public_content.UgcNonPublicContent/DeleteNonPublicHotComment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcNonPublicContentClient) GetNonPublicHotComment(ctx context.Context, in *GetNonPublicHotCommentReq, opts ...grpc.CallOption) (*GetNonPublicHotCommentResp, error) {
	out := new(GetNonPublicHotCommentResp)
	err := c.cc.Invoke(ctx, "/ugc.non_public_content.UgcNonPublicContent/GetNonPublicHotComment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcNonPublicContentClient) BatchNonPublicHotComment(ctx context.Context, in *BatchNonPublicHotCommentReq, opts ...grpc.CallOption) (*BatchNonPublicHotCommentResp, error) {
	out := new(BatchNonPublicHotCommentResp)
	err := c.cc.Invoke(ctx, "/ugc.non_public_content.UgcNonPublicContent/BatchNonPublicHotComment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcNonPublicContentClient) AddNonPublicAttitude(ctx context.Context, in *AddNonPublicAttitudeReq, opts ...grpc.CallOption) (*AddNonPublicAttitudeResp, error) {
	out := new(AddNonPublicAttitudeResp)
	err := c.cc.Invoke(ctx, "/ugc.non_public_content.UgcNonPublicContent/AddNonPublicAttitude", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcNonPublicContentClient) BatchGetHotCommentSubComments(ctx context.Context, in *BatchGetHotCommentSubCommentsReq, opts ...grpc.CallOption) (*BatchGetHotCommentSubCommentsResp, error) {
	out := new(BatchGetHotCommentSubCommentsResp)
	err := c.cc.Invoke(ctx, "/ugc.non_public_content.UgcNonPublicContent/BatchGetHotCommentSubComments", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcNonPublicContentClient) ListNonPublicPostForceInsertInStream(ctx context.Context, in *ListNonPublicPostForceInsertInStreamReq, opts ...grpc.CallOption) (*ListNonPublicPostForceInsertInStreamResp, error) {
	out := new(ListNonPublicPostForceInsertInStreamResp)
	err := c.cc.Invoke(ctx, "/ugc.non_public_content.UgcNonPublicContent/ListNonPublicPostForceInsertInStream", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcNonPublicContentClient) ForceInsertNonPublicPostInStream(ctx context.Context, in *ForceInsertNonPublicPostInStreamReq, opts ...grpc.CallOption) (*ForceInsertNonPublicPostInStreamResp, error) {
	out := new(ForceInsertNonPublicPostInStreamResp)
	err := c.cc.Invoke(ctx, "/ugc.non_public_content.UgcNonPublicContent/ForceInsertNonPublicPostInStream", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcNonPublicContentClient) HideNonPublicPostInStream(ctx context.Context, in *HideNonPublicPostInStreamReq, opts ...grpc.CallOption) (*HideNonPublicPostInStreamResp, error) {
	out := new(HideNonPublicPostInStreamResp)
	err := c.cc.Invoke(ctx, "/ugc.non_public_content.UgcNonPublicContent/HideNonPublicPostInStream", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcNonPublicContentClient) BatGetNonPostHideStatus(ctx context.Context, in *BatGetNonPostHideStatusReq, opts ...grpc.CallOption) (*BatGetNonPostHideStatusResp, error) {
	out := new(BatGetNonPostHideStatusResp)
	err := c.cc.Invoke(ctx, "/ugc.non_public_content.UgcNonPublicContent/BatGetNonPostHideStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcNonPublicContentClient) ListNonPublicPostIndex(ctx context.Context, in *ListNonPublicPostIndexReq, opts ...grpc.CallOption) (*ListNonPublicPostIndexResp, error) {
	out := new(ListNonPublicPostIndexResp)
	err := c.cc.Invoke(ctx, "/ugc.non_public_content.UgcNonPublicContent/ListNonPublicPostIndex", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcNonPublicContentClient) InsertNonPublicPostIndex(ctx context.Context, in *InsertNonPublicPostIndexReq, opts ...grpc.CallOption) (*InsertNonPublicPostIndexResp, error) {
	out := new(InsertNonPublicPostIndexResp)
	err := c.cc.Invoke(ctx, "/ugc.non_public_content.UgcNonPublicContent/InsertNonPublicPostIndex", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UgcNonPublicContentServer is the server API for UgcNonPublicContent service.
type UgcNonPublicContentServer interface {
	// 帖子
	AddNonPublicPost(context.Context, *AddNonPublicPostReq) (*AddNonPublicPostResp, error)
	AddNonPublicPostDirectly(context.Context, *AddNonPublicPostDirectlyReq) (*AddNonPublicPostDirectlyResp, error)
	UpdateNonPublicPostUpdateTime(context.Context, *UpdateNonPublicPostUpdateTimeReq) (*UpdateNonPublicPostUpdateTimeResp, error)
	GetSceneStreamPostRecord(context.Context, *GetSceneStreamPostRecordReq) (*GetSceneStreamPostRecordResp, error)
	NonPublicMarkAttachmentUploaded(context.Context, *NonPublicMarkAttachmentUploadedReq) (*NonPublicMarkAttachmentUploadedResp, error)
	GetNonPublicPostById(context.Context, *GetNonPublicPostByIdReq) (*GetNonPublicPostByIdResp, error)
	BatGetNonPublicPostListById(context.Context, *BatGetNonPublicPostListByIdReq) (*BatGetNonPublicPostListByIdResp, error)
	NonPublicBanPostById(context.Context, *NonPublicBanPostByIdReq) (*NonPublicBanPostByIdResp, error)
	AddNonPublicStickyContent(context.Context, *AddNonPublishStickyContentReq) (*AddNonPublishStickyContentResp, error)
	RemoveNonPublishStickyContent(context.Context, *RemoveNonPublishStickyContentReq) (*RemoveNonPublishStickyContentResp, error)
	GetNonPublishStickyContent(context.Context, *GetNonPublishStickyContentReq) (*GetNonPublishStickyContentResp, error)
	UpdateNonPublicPostMachineAuditById(context.Context, *UpdateNonPublicPostMachineAuditByIdReq) (*UpdateNonPublicPostMachineAuditByIdResp, error)
	GetAndUpdateInsertPosts(context.Context, *GetAndUpdateInsertPostsReq) (*GetAndUpdateInsertPostsResp, error)
	DistributePostFilter(context.Context, *DistributePostFilterReq) (*DistributePostFilterResp, error)
	// 评论
	AddNonPublicComment(context.Context, *AddNonPublicCommentReq) (*AddNonPublicCommentResp, error)
	BanNonPublicCommentById(context.Context, *BanNonPublicCommentByIdReq) (*BanNonPublicCommentByIdResp, error)
	GetNonPublicCommentList(context.Context, *GetNonPublicCommentListReq) (*GetNonPublicCommentListResp, error)
	BatchGetNonPublicCommentByIds(context.Context, *BatchGetNonPublicCommentByIdsReq) (*BatchGetNonPublicCommentByIdsResp, error)
	UpsertNonPublicHotComment(context.Context, *UpsertNonPublicHotCommentReq) (*UpsertNonPublicHotCommentResp, error)
	DeleteNonPublicHotComment(context.Context, *DeleteNonPublicHotCommentReq) (*DeleteNonPublicHotCommentResp, error)
	GetNonPublicHotComment(context.Context, *GetNonPublicHotCommentReq) (*GetNonPublicHotCommentResp, error)
	BatchNonPublicHotComment(context.Context, *BatchNonPublicHotCommentReq) (*BatchNonPublicHotCommentResp, error)
	// 点赞
	AddNonPublicAttitude(context.Context, *AddNonPublicAttitudeReq) (*AddNonPublicAttitudeResp, error)
	BatchGetHotCommentSubComments(context.Context, *BatchGetHotCommentSubCommentsReq) (*BatchGetHotCommentSubCommentsResp, error)
	// -----------运营后台帖子列表-----------
	ListNonPublicPostForceInsertInStream(context.Context, *ListNonPublicPostForceInsertInStreamReq) (*ListNonPublicPostForceInsertInStreamResp, error)
	ForceInsertNonPublicPostInStream(context.Context, *ForceInsertNonPublicPostInStreamReq) (*ForceInsertNonPublicPostInStreamResp, error)
	HideNonPublicPostInStream(context.Context, *HideNonPublicPostInStreamReq) (*HideNonPublicPostInStreamResp, error)
	BatGetNonPostHideStatus(context.Context, *BatGetNonPostHideStatusReq) (*BatGetNonPostHideStatusResp, error)
	ListNonPublicPostIndex(context.Context, *ListNonPublicPostIndexReq) (*ListNonPublicPostIndexResp, error)
	InsertNonPublicPostIndex(context.Context, *InsertNonPublicPostIndexReq) (*InsertNonPublicPostIndexResp, error)
}

func RegisterUgcNonPublicContentServer(s *grpc.Server, srv UgcNonPublicContentServer) {
	s.RegisterService(&_UgcNonPublicContent_serviceDesc, srv)
}

func _UgcNonPublicContent_AddNonPublicPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddNonPublicPostReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcNonPublicContentServer).AddNonPublicPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.non_public_content.UgcNonPublicContent/AddNonPublicPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcNonPublicContentServer).AddNonPublicPost(ctx, req.(*AddNonPublicPostReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcNonPublicContent_AddNonPublicPostDirectly_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddNonPublicPostDirectlyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcNonPublicContentServer).AddNonPublicPostDirectly(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.non_public_content.UgcNonPublicContent/AddNonPublicPostDirectly",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcNonPublicContentServer).AddNonPublicPostDirectly(ctx, req.(*AddNonPublicPostDirectlyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcNonPublicContent_UpdateNonPublicPostUpdateTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNonPublicPostUpdateTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcNonPublicContentServer).UpdateNonPublicPostUpdateTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.non_public_content.UgcNonPublicContent/UpdateNonPublicPostUpdateTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcNonPublicContentServer).UpdateNonPublicPostUpdateTime(ctx, req.(*UpdateNonPublicPostUpdateTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcNonPublicContent_GetSceneStreamPostRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSceneStreamPostRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcNonPublicContentServer).GetSceneStreamPostRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.non_public_content.UgcNonPublicContent/GetSceneStreamPostRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcNonPublicContentServer).GetSceneStreamPostRecord(ctx, req.(*GetSceneStreamPostRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcNonPublicContent_NonPublicMarkAttachmentUploaded_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NonPublicMarkAttachmentUploadedReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcNonPublicContentServer).NonPublicMarkAttachmentUploaded(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.non_public_content.UgcNonPublicContent/NonPublicMarkAttachmentUploaded",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcNonPublicContentServer).NonPublicMarkAttachmentUploaded(ctx, req.(*NonPublicMarkAttachmentUploadedReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcNonPublicContent_GetNonPublicPostById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNonPublicPostByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcNonPublicContentServer).GetNonPublicPostById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.non_public_content.UgcNonPublicContent/GetNonPublicPostById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcNonPublicContentServer).GetNonPublicPostById(ctx, req.(*GetNonPublicPostByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcNonPublicContent_BatGetNonPublicPostListById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetNonPublicPostListByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcNonPublicContentServer).BatGetNonPublicPostListById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.non_public_content.UgcNonPublicContent/BatGetNonPublicPostListById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcNonPublicContentServer).BatGetNonPublicPostListById(ctx, req.(*BatGetNonPublicPostListByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcNonPublicContent_NonPublicBanPostById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NonPublicBanPostByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcNonPublicContentServer).NonPublicBanPostById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.non_public_content.UgcNonPublicContent/NonPublicBanPostById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcNonPublicContentServer).NonPublicBanPostById(ctx, req.(*NonPublicBanPostByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcNonPublicContent_AddNonPublicStickyContent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddNonPublishStickyContentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcNonPublicContentServer).AddNonPublicStickyContent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.non_public_content.UgcNonPublicContent/AddNonPublicStickyContent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcNonPublicContentServer).AddNonPublicStickyContent(ctx, req.(*AddNonPublishStickyContentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcNonPublicContent_RemoveNonPublishStickyContent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveNonPublishStickyContentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcNonPublicContentServer).RemoveNonPublishStickyContent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.non_public_content.UgcNonPublicContent/RemoveNonPublishStickyContent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcNonPublicContentServer).RemoveNonPublishStickyContent(ctx, req.(*RemoveNonPublishStickyContentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcNonPublicContent_GetNonPublishStickyContent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNonPublishStickyContentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcNonPublicContentServer).GetNonPublishStickyContent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.non_public_content.UgcNonPublicContent/GetNonPublishStickyContent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcNonPublicContentServer).GetNonPublishStickyContent(ctx, req.(*GetNonPublishStickyContentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcNonPublicContent_UpdateNonPublicPostMachineAuditById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNonPublicPostMachineAuditByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcNonPublicContentServer).UpdateNonPublicPostMachineAuditById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.non_public_content.UgcNonPublicContent/UpdateNonPublicPostMachineAuditById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcNonPublicContentServer).UpdateNonPublicPostMachineAuditById(ctx, req.(*UpdateNonPublicPostMachineAuditByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcNonPublicContent_GetAndUpdateInsertPosts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAndUpdateInsertPostsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcNonPublicContentServer).GetAndUpdateInsertPosts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.non_public_content.UgcNonPublicContent/GetAndUpdateInsertPosts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcNonPublicContentServer).GetAndUpdateInsertPosts(ctx, req.(*GetAndUpdateInsertPostsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcNonPublicContent_DistributePostFilter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DistributePostFilterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcNonPublicContentServer).DistributePostFilter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.non_public_content.UgcNonPublicContent/DistributePostFilter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcNonPublicContentServer).DistributePostFilter(ctx, req.(*DistributePostFilterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcNonPublicContent_AddNonPublicComment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddNonPublicCommentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcNonPublicContentServer).AddNonPublicComment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.non_public_content.UgcNonPublicContent/AddNonPublicComment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcNonPublicContentServer).AddNonPublicComment(ctx, req.(*AddNonPublicCommentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcNonPublicContent_BanNonPublicCommentById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BanNonPublicCommentByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcNonPublicContentServer).BanNonPublicCommentById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.non_public_content.UgcNonPublicContent/BanNonPublicCommentById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcNonPublicContentServer).BanNonPublicCommentById(ctx, req.(*BanNonPublicCommentByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcNonPublicContent_GetNonPublicCommentList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNonPublicCommentListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcNonPublicContentServer).GetNonPublicCommentList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.non_public_content.UgcNonPublicContent/GetNonPublicCommentList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcNonPublicContentServer).GetNonPublicCommentList(ctx, req.(*GetNonPublicCommentListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcNonPublicContent_BatchGetNonPublicCommentByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetNonPublicCommentByIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcNonPublicContentServer).BatchGetNonPublicCommentByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.non_public_content.UgcNonPublicContent/BatchGetNonPublicCommentByIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcNonPublicContentServer).BatchGetNonPublicCommentByIds(ctx, req.(*BatchGetNonPublicCommentByIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcNonPublicContent_UpsertNonPublicHotComment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertNonPublicHotCommentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcNonPublicContentServer).UpsertNonPublicHotComment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.non_public_content.UgcNonPublicContent/UpsertNonPublicHotComment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcNonPublicContentServer).UpsertNonPublicHotComment(ctx, req.(*UpsertNonPublicHotCommentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcNonPublicContent_DeleteNonPublicHotComment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteNonPublicHotCommentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcNonPublicContentServer).DeleteNonPublicHotComment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.non_public_content.UgcNonPublicContent/DeleteNonPublicHotComment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcNonPublicContentServer).DeleteNonPublicHotComment(ctx, req.(*DeleteNonPublicHotCommentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcNonPublicContent_GetNonPublicHotComment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNonPublicHotCommentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcNonPublicContentServer).GetNonPublicHotComment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.non_public_content.UgcNonPublicContent/GetNonPublicHotComment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcNonPublicContentServer).GetNonPublicHotComment(ctx, req.(*GetNonPublicHotCommentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcNonPublicContent_BatchNonPublicHotComment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchNonPublicHotCommentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcNonPublicContentServer).BatchNonPublicHotComment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.non_public_content.UgcNonPublicContent/BatchNonPublicHotComment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcNonPublicContentServer).BatchNonPublicHotComment(ctx, req.(*BatchNonPublicHotCommentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcNonPublicContent_AddNonPublicAttitude_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddNonPublicAttitudeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcNonPublicContentServer).AddNonPublicAttitude(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.non_public_content.UgcNonPublicContent/AddNonPublicAttitude",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcNonPublicContentServer).AddNonPublicAttitude(ctx, req.(*AddNonPublicAttitudeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcNonPublicContent_BatchGetHotCommentSubComments_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetHotCommentSubCommentsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcNonPublicContentServer).BatchGetHotCommentSubComments(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.non_public_content.UgcNonPublicContent/BatchGetHotCommentSubComments",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcNonPublicContentServer).BatchGetHotCommentSubComments(ctx, req.(*BatchGetHotCommentSubCommentsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcNonPublicContent_ListNonPublicPostForceInsertInStream_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListNonPublicPostForceInsertInStreamReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcNonPublicContentServer).ListNonPublicPostForceInsertInStream(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.non_public_content.UgcNonPublicContent/ListNonPublicPostForceInsertInStream",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcNonPublicContentServer).ListNonPublicPostForceInsertInStream(ctx, req.(*ListNonPublicPostForceInsertInStreamReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcNonPublicContent_ForceInsertNonPublicPostInStream_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ForceInsertNonPublicPostInStreamReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcNonPublicContentServer).ForceInsertNonPublicPostInStream(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.non_public_content.UgcNonPublicContent/ForceInsertNonPublicPostInStream",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcNonPublicContentServer).ForceInsertNonPublicPostInStream(ctx, req.(*ForceInsertNonPublicPostInStreamReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcNonPublicContent_HideNonPublicPostInStream_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HideNonPublicPostInStreamReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcNonPublicContentServer).HideNonPublicPostInStream(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.non_public_content.UgcNonPublicContent/HideNonPublicPostInStream",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcNonPublicContentServer).HideNonPublicPostInStream(ctx, req.(*HideNonPublicPostInStreamReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcNonPublicContent_BatGetNonPostHideStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetNonPostHideStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcNonPublicContentServer).BatGetNonPostHideStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.non_public_content.UgcNonPublicContent/BatGetNonPostHideStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcNonPublicContentServer).BatGetNonPostHideStatus(ctx, req.(*BatGetNonPostHideStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcNonPublicContent_ListNonPublicPostIndex_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListNonPublicPostIndexReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcNonPublicContentServer).ListNonPublicPostIndex(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.non_public_content.UgcNonPublicContent/ListNonPublicPostIndex",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcNonPublicContentServer).ListNonPublicPostIndex(ctx, req.(*ListNonPublicPostIndexReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcNonPublicContent_InsertNonPublicPostIndex_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InsertNonPublicPostIndexReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcNonPublicContentServer).InsertNonPublicPostIndex(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.non_public_content.UgcNonPublicContent/InsertNonPublicPostIndex",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcNonPublicContentServer).InsertNonPublicPostIndex(ctx, req.(*InsertNonPublicPostIndexReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _UgcNonPublicContent_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ugc.non_public_content.UgcNonPublicContent",
	HandlerType: (*UgcNonPublicContentServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddNonPublicPost",
			Handler:    _UgcNonPublicContent_AddNonPublicPost_Handler,
		},
		{
			MethodName: "AddNonPublicPostDirectly",
			Handler:    _UgcNonPublicContent_AddNonPublicPostDirectly_Handler,
		},
		{
			MethodName: "UpdateNonPublicPostUpdateTime",
			Handler:    _UgcNonPublicContent_UpdateNonPublicPostUpdateTime_Handler,
		},
		{
			MethodName: "GetSceneStreamPostRecord",
			Handler:    _UgcNonPublicContent_GetSceneStreamPostRecord_Handler,
		},
		{
			MethodName: "NonPublicMarkAttachmentUploaded",
			Handler:    _UgcNonPublicContent_NonPublicMarkAttachmentUploaded_Handler,
		},
		{
			MethodName: "GetNonPublicPostById",
			Handler:    _UgcNonPublicContent_GetNonPublicPostById_Handler,
		},
		{
			MethodName: "BatGetNonPublicPostListById",
			Handler:    _UgcNonPublicContent_BatGetNonPublicPostListById_Handler,
		},
		{
			MethodName: "NonPublicBanPostById",
			Handler:    _UgcNonPublicContent_NonPublicBanPostById_Handler,
		},
		{
			MethodName: "AddNonPublicStickyContent",
			Handler:    _UgcNonPublicContent_AddNonPublicStickyContent_Handler,
		},
		{
			MethodName: "RemoveNonPublishStickyContent",
			Handler:    _UgcNonPublicContent_RemoveNonPublishStickyContent_Handler,
		},
		{
			MethodName: "GetNonPublishStickyContent",
			Handler:    _UgcNonPublicContent_GetNonPublishStickyContent_Handler,
		},
		{
			MethodName: "UpdateNonPublicPostMachineAuditById",
			Handler:    _UgcNonPublicContent_UpdateNonPublicPostMachineAuditById_Handler,
		},
		{
			MethodName: "GetAndUpdateInsertPosts",
			Handler:    _UgcNonPublicContent_GetAndUpdateInsertPosts_Handler,
		},
		{
			MethodName: "DistributePostFilter",
			Handler:    _UgcNonPublicContent_DistributePostFilter_Handler,
		},
		{
			MethodName: "AddNonPublicComment",
			Handler:    _UgcNonPublicContent_AddNonPublicComment_Handler,
		},
		{
			MethodName: "BanNonPublicCommentById",
			Handler:    _UgcNonPublicContent_BanNonPublicCommentById_Handler,
		},
		{
			MethodName: "GetNonPublicCommentList",
			Handler:    _UgcNonPublicContent_GetNonPublicCommentList_Handler,
		},
		{
			MethodName: "BatchGetNonPublicCommentByIds",
			Handler:    _UgcNonPublicContent_BatchGetNonPublicCommentByIds_Handler,
		},
		{
			MethodName: "UpsertNonPublicHotComment",
			Handler:    _UgcNonPublicContent_UpsertNonPublicHotComment_Handler,
		},
		{
			MethodName: "DeleteNonPublicHotComment",
			Handler:    _UgcNonPublicContent_DeleteNonPublicHotComment_Handler,
		},
		{
			MethodName: "GetNonPublicHotComment",
			Handler:    _UgcNonPublicContent_GetNonPublicHotComment_Handler,
		},
		{
			MethodName: "BatchNonPublicHotComment",
			Handler:    _UgcNonPublicContent_BatchNonPublicHotComment_Handler,
		},
		{
			MethodName: "AddNonPublicAttitude",
			Handler:    _UgcNonPublicContent_AddNonPublicAttitude_Handler,
		},
		{
			MethodName: "BatchGetHotCommentSubComments",
			Handler:    _UgcNonPublicContent_BatchGetHotCommentSubComments_Handler,
		},
		{
			MethodName: "ListNonPublicPostForceInsertInStream",
			Handler:    _UgcNonPublicContent_ListNonPublicPostForceInsertInStream_Handler,
		},
		{
			MethodName: "ForceInsertNonPublicPostInStream",
			Handler:    _UgcNonPublicContent_ForceInsertNonPublicPostInStream_Handler,
		},
		{
			MethodName: "HideNonPublicPostInStream",
			Handler:    _UgcNonPublicContent_HideNonPublicPostInStream_Handler,
		},
		{
			MethodName: "BatGetNonPostHideStatus",
			Handler:    _UgcNonPublicContent_BatGetNonPostHideStatus_Handler,
		},
		{
			MethodName: "ListNonPublicPostIndex",
			Handler:    _UgcNonPublicContent_ListNonPublicPostIndex_Handler,
		},
		{
			MethodName: "InsertNonPublicPostIndex",
			Handler:    _UgcNonPublicContent_InsertNonPublicPostIndex_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ugc/non_public_content.proto",
}

func init() {
	proto.RegisterFile("ugc/non_public_content.proto", fileDescriptor_non_public_content_ebea57b8c4eccb67)
}

var fileDescriptor_non_public_content_ebea57b8c4eccb67 = []byte{
	// 4133 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x3c, 0x4d, 0x73, 0x1b, 0xc9,
	0x75, 0x1c, 0x80, 0x20, 0x81, 0x07, 0x7e, 0x40, 0x4d, 0x8a, 0x82, 0x40, 0x52, 0xa4, 0x86, 0x9b,
	0x5d, 0x79, 0x23, 0x53, 0x1b, 0xee, 0xca, 0x2b, 0x6f, 0xb2, 0x8e, 0xf9, 0x29, 0x21, 0x4b, 0x89,
	0xcc, 0x90, 0x5c, 0xaf, 0x53, 0x4e, 0x4d, 0x0d, 0x67, 0x9a, 0xe4, 0x94, 0x80, 0x99, 0xd9, 0xe9,
	0x01, 0x57, 0x28, 0x57, 0xca, 0xa9, 0xad, 0x75, 0x39, 0x07, 0xa7, 0xca, 0x87, 0x54, 0x4e, 0xf1,
	0x21, 0x39, 0x25, 0xb9, 0xf9, 0x94, 0x94, 0xf3, 0x0b, 0x72, 0xce, 0x21, 0x95, 0x5b, 0x4e, 0x71,
	0x8e, 0xc9, 0x29, 0xa7, 0x54, 0xa5, 0xfa, 0x63, 0x06, 0x3d, 0xc0, 0xf4, 0x00, 0x24, 0x65, 0x79,
	0x0f, 0xb9, 0xa1, 0x5f, 0x77, 0xbf, 0x7e, 0xfd, 0xfa, 0x7d, 0xf5, 0x7b, 0x3d, 0x80, 0xa5, 0xce,
	0xb9, 0xfd, 0xc8, 0xf3, 0x3d, 0x33, 0xe8, 0x9c, 0xb6, 0x5c, 0xdb, 0xb4, 0x7d, 0x2f, 0xc2, 0x5e,
	0xb4, 0x1e, 0x84, 0x7e, 0xe4, 0xa3, 0x85, 0xce, 0xb9, 0xbd, 0x3e, 0xd8, 0xdb, 0xb8, 0x45, 0x67,
	0xa5, 0x86, 0xea, 0x3f, 0xd1, 0x60, 0xe9, 0x24, 0x20, 0x38, 0x8c, 0x5e, 0xf8, 0xde, 0x21, 0x1b,
	0xfe, 0xcc, 0x8f, 0xb6, 0xfd, 0x76, 0x1b, 0x7b, 0x91, 0x81, 0x3f, 0x47, 0x77, 0xa1, 0x4c, 0x6c,
	0xec, 0x61, 0xd3, 0x75, 0xea, 0xda, 0xaa, 0xf6, 0xa0, 0x62, 0x4c, 0xb2, 0x76, 0xd3, 0x41, 0x77,
	0x60, 0x32, 0xf0, 0x49, 0x44, 0x7b, 0x0a, 0xac, 0x67, 0x82, 0x36, 0x9b, 0x0e, 0x5a, 0x06, 0xb0,
	0x39, 0x06, 0xda, 0x57, 0x64, 0x7d, 0x15, 0x01, 0x69, 0x3a, 0x68, 0x1e, 0x4a, 0xc4, 0xf6, 0x43,
	0x5c, 0x1f, 0x5f, 0xd5, 0x1e, 0x14, 0x0d, 0xde, 0xd0, 0x57, 0x60, 0x39, 0x87, 0x10, 0x12, 0xe8,
	0xff, 0x58, 0x84, 0x3b, 0x9b, 0x8e, 0x93, 0x74, 0x6f, 0x46, 0x91, 0x1b, 0x75, 0x1c, 0x4c, 0xa9,
	0x3c, 0x82, 0x39, 0x3f, 0x74, 0xcf, 0x5d, 0xcf, 0xe4, 0xc4, 0x92, 0x28, 0xc4, 0x56, 0x9b, 0x11,
	0x5c, 0xdd, 0x58, 0x5b, 0xcf, 0xe6, 0xc7, 0xfa, 0x11, 0x1d, 0x7b, 0xc4, 0x86, 0x1a, 0xb7, 0xf8,
	0x7c, 0x09, 0x74, 0xed, 0xfd, 0xdd, 0x81, 0xc9, 0x0e, 0xc1, 0x21, 0xed, 0xa3, 0x3b, 0x9c, 0x36,
	0x26, 0x68, 0xb3, 0xe9, 0xa0, 0x35, 0x98, 0xb6, 0x04, 0xd1, 0x66, 0xd4, 0x0d, 0x70, 0xbd, 0xc4,
	0xba, 0xa7, 0x62, 0xe0, 0x71, 0x37, 0xc0, 0x48, 0x87, 0x69, 0x97, 0x98, 0x67, 0x6e, 0x48, 0x22,
	0x33, 0x72, 0xdb, 0xb8, 0x3e, 0xb1, 0xaa, 0x3d, 0x28, 0x1b, 0x55, 0x97, 0xec, 0x51, 0xd8, 0xb1,
	0xdb, 0xc6, 0xe8, 0x2d, 0x98, 0x89, 0xac, 0xf0, 0x1c, 0x47, 0x66, 0xbc, 0xd0, 0x24, 0xc7, 0xc4,
	0xa1, 0x27, 0x7c, 0xb9, 0x05, 0x98, 0x20, 0x7e, 0x27, 0xb4, 0x71, 0xbd, 0xcc, 0xc9, 0xe0, 0x2d,
	0xb4, 0x08, 0x15, 0x12, 0xe1, 0x80, 0x93, 0x50, 0x61, 0x5d, 0x65, 0x0a, 0x60, 0xcb, 0xcb, 0xe7,
	0x0d, 0xe9, 0xf3, 0x7e, 0x02, 0x77, 0xd9, 0x72, 0xa7, 0xb8, 0xe5, 0x7b, 0xe7, 0xa6, 0xeb, 0x98,
	0x67, 0x7e, 0x68, 0x86, 0x38, 0xf0, 0xc3, 0xa8, 0x5e, 0x65, 0x63, 0x6f, 0xd3, 0x01, 0x5b, 0xac,
	0xbf, 0xe9, 0xec, 0xf9, 0xa1, 0xc1, 0x3a, 0xf5, 0x06, 0xd4, 0xb3, 0x4f, 0x8e, 0x04, 0xfa, 0xe7,
	0xb0, 0xb4, 0x83, 0x5b, 0x38, 0xc2, 0x6f, 0x4c, 0x00, 0xa9, 0xa8, 0xe5, 0x2c, 0x49, 0x02, 0xfd,
	0x53, 0x68, 0x3c, 0xc5, 0xd1, 0xa6, 0xe7, 0x9c, 0x04, 0x8e, 0x15, 0xe1, 0xa6, 0x47, 0xe5, 0xf2,
	0xd0, 0x27, 0x11, 0xa1, 0x14, 0xd5, 0xf9, 0xb2, 0x4d, 0x87, 0xd4, 0xb5, 0xd5, 0x22, 0x25, 0x48,
	0x34, 0xd1, 0x3d, 0x00, 0xdb, 0x8a, 0xf0, 0xb9, 0x1f, 0x76, 0x9b, 0x31, 0x4d, 0x12, 0x44, 0xff,
	0x10, 0x16, 0x95, 0x78, 0x49, 0xa0, 0x46, 0xac, 0xff, 0x87, 0x06, 0x73, 0x32, 0xfb, 0x2c, 0xfb,
	0x82, 0x12, 0x8b, 0x6a, 0x50, 0x7c, 0x89, 0xbb, 0x82, 0x2f, 0xf4, 0x27, 0x42, 0x30, 0xce, 0xce,
	0xb5, 0xc0, 0xce, 0x95, 0xfd, 0xa6, 0x78, 0x85, 0xc8, 0x0b, 0x5e, 0xc4, 0x4d, 0xaa, 0x8a, 0xf8,
	0x55, 0x14, 0x5a, 0x4c, 0x50, 0x2b, 0x06, 0x6f, 0x30, 0xc1, 0x89, 0xac, 0xa8, 0x43, 0x84, 0x80,
	0x8a, 0x16, 0x65, 0xeb, 0x65, 0x3b, 0xd6, 0x1e, 0x26, 0x97, 0x15, 0xa3, 0x72, 0xd9, 0xde, 0xee,
	0x21, 0x0b, 0xac, 0xd0, 0x6a, 0x0b, 0xb9, 0xe1, 0x0d, 0xf4, 0x10, 0x90, 0x50, 0xcd, 0x4b, 0xd7,
	0xc1, 0xbe, 0x69, 0xfb, 0x97, 0x38, 0x14, 0xe2, 0x52, 0xe3, 0x3d, 0x9f, 0xd2, 0x8e, 0x6d, 0x0a,
	0xd7, 0x1f, 0xc2, 0x42, 0xb2, 0x4f, 0xc3, 0xb5, 0x2f, 0x8e, 0xf1, 0xab, 0xe8, 0x7b, 0x7e, 0xe8,
	0x10, 0xb6, 0x31, 0xfc, 0x2a, 0x12, 0x7b, 0x65, 0xbf, 0xf5, 0x7f, 0xd1, 0xa0, 0x96, 0x0c, 0x3f,
	0x31, 0xf6, 0xb7, 0xad, 0xd0, 0x61, 0xe2, 0x7d, 0xe1, 0x7f, 0x61, 0x4a, 0xa3, 0xcb, 0x14, 0x40,
	0x51, 0x51, 0xed, 0x62, 0x9d, 0xae, 0xed, 0x7b, 0x66, 0x27, 0x6c, 0x89, 0x43, 0xaa, 0x52, 0x60,
	0xd3, 0xf6, 0xbd, 0x93, 0xb0, 0x45, 0x29, 0x66, 0x63, 0x82, 0x96, 0x15, 0x9d, 0xf9, 0x61, 0xdb,
	0xf4, 0xac, 0x36, 0x16, 0x9c, 0xab, 0xd1, 0x9e, 0x43, 0xd1, 0xf1, 0xc2, 0x6a, 0xe3, 0xc1, 0xd1,
	0x14, 0xb5, 0xe0, 0x67, 0x6a, 0x34, 0x45, 0x4f, 0xd7, 0x0f, 0xb1, 0xd5, 0xa2, 0x4b, 0x9b, 0x96,
	0xe3, 0x84, 0x8c, 0xc3, 0x15, 0xa3, 0x4a, 0x81, 0x27, 0x61, 0x6b, 0xd3, 0x71, 0x42, 0xfd, 0xab,
	0x02, 0xd4, 0x07, 0x98, 0xb0, 0xdb, 0xc2, 0xec, 0xc4, 0xf7, 0xa0, 0xf4, 0x05, 0xe5, 0x87, 0xb0,
	0x6d, 0xeb, 0x2a, 0xdb, 0x96, 0xcd, 0xc5, 0x67, 0x63, 0x06, 0x9f, 0x8e, 0x5e, 0x40, 0xb5, 0xdd,
	0x69, 0x45, 0xae, 0xd9, 0xc6, 0x8e, 0x6b, 0x31, 0x36, 0x54, 0x37, 0x7e, 0x7b, 0x28, 0xb6, 0x9e,
	0xec, 0x3d, 0x1b, 0x33, 0x80, 0x61, 0x78, 0x4e, 0x11, 0xa0, 0x5d, 0x28, 0xd3, 0x3d, 0xd9, 0x56,
	0xc8, 0x15, 0xae, 0xba, 0xf1, 0x60, 0x28, 0x32, 0x71, 0x62, 0xcf, 0xc6, 0x8c, 0xc9, 0x4e, 0xd8,
	0xa2, 0x3f, 0xb7, 0x2a, 0x89, 0xa8, 0xea, 0x9f, 0x43, 0x55, 0xb6, 0xc6, 0xcc, 0x6a, 0xd1, 0x5f,
	0x3d, 0x43, 0x50, 0xe6, 0x80, 0xa6, 0x43, 0x85, 0x83, 0x1d, 0x12, 0x3f, 0x4d, 0xf6, 0x1b, 0xad,
	0x40, 0x55, 0x4c, 0x60, 0x0a, 0x51, 0x64, 0xa2, 0x0c, 0x1c, 0xc4, 0x4c, 0x5d, 0x0d, 0x8a, 0x7e,
	0x10, 0x09, 0x1b, 0x4d, 0x7f, 0xea, 0x3f, 0x2b, 0xc1, 0x9c, 0x6c, 0xa8, 0xa8, 0x6a, 0x52, 0x8d,
	0x97, 0x2c, 0xba, 0x96, 0xb2, 0xe8, 0xb2, 0x71, 0x2a, 0xa4, 0x8d, 0xd3, 0x01, 0xdc, 0x92, 0x7d,
	0x91, 0xd9, 0x72, 0x09, 0x55, 0xbf, 0xe2, 0xa8, 0x0e, 0x69, 0x96, 0xf4, 0x1a, 0xfb, 0x2e, 0x89,
	0x54, 0x3e, 0x6e, 0xfc, 0x46, 0x3e, 0x6e, 0x1e, 0x4a, 0x91, 0x1b, 0xb5, 0xb0, 0x90, 0x43, 0xde,
	0x40, 0x47, 0x30, 0x25, 0xe6, 0x73, 0xb2, 0x27, 0x18, 0xd9, 0xef, 0x8d, 0x2c, 0x6b, 0x42, 0x58,
	0x8d, 0xaa, 0x18, 0xc1, 0xe8, 0xef, 0x59, 0x95, 0xc9, 0x94, 0x55, 0x59, 0x81, 0x2a, 0xb3, 0xe2,
	0x9c, 0x38, 0xe1, 0xab, 0x80, 0x82, 0x0e, 0x18, 0x84, 0x69, 0x7e, 0xe4, 0x3a, 0xcc, 0x55, 0x51,
	0xcd, 0x8f, 0x5c, 0xa6, 0xe4, 0x0e, 0xbe, 0x74, 0x6d, 0xc9, 0x4f, 0x95, 0x39, 0xa0, 0xe9, 0xa0,
	0x06, 0x94, 0x63, 0x6d, 0x64, 0x86, 0x66, 0xda, 0x48, 0xda, 0x74, 0x62, 0xdb, 0x0a, 0x5f, 0x62,
	0xe6, 0x19, 0xa6, 0x78, 0x27, 0x07, 0x34, 0x19, 0x56, 0xbb, 0xe5, 0x32, 0xb7, 0x11, 0xd4, 0xa7,
	0x79, 0x27, 0x07, 0x34, 0x03, 0xf4, 0x10, 0x26, 0xdc, 0xc0, 0x6c, 0xf9, 0x76, 0x7d, 0x86, 0xb1,
	0xfc, 0x36, 0x63, 0x47, 0xcc, 0x83, 0x7d, 0xdf, 0xb6, 0x22, 0xd7, 0xf7, 0x8c, 0x92, 0x1b, 0xec,
	0xfb, 0x36, 0xda, 0x83, 0xda, 0x39, 0xf6, 0x70, 0x68, 0xb5, 0x62, 0x36, 0x91, 0xfa, 0x2c, 0x63,
	0xe3, 0x62, 0x6a, 0xde, 0x53, 0x3e, 0x48, 0xd8, 0x50, 0x63, 0xf6, 0x3c, 0xd5, 0x26, 0xfa, 0x2f,
	0x0a, 0x30, 0x3f, 0x28, 0x92, 0x24, 0x90, 0x9d, 0x9f, 0x96, 0x72, 0x7e, 0x3d, 0x3e, 0x17, 0x52,
	0x7c, 0x7e, 0x0b, 0x66, 0xd8, 0x04, 0x3b, 0xc4, 0x56, 0x84, 0x4d, 0x8b, 0x3b, 0x83, 0x71, 0x63,
	0x8a, 0x42, 0xb7, 0x19, 0x70, 0x33, 0x42, 0x1b, 0x70, 0xdb, 0x4a, 0x74, 0xdc, 0x74, 0xdb, 0xd6,
	0x39, 0x36, 0x5f, 0xe2, 0x2e, 0xa9, 0x03, 0xf3, 0x48, 0x73, 0xbd, 0xce, 0x26, 0xed, 0xfb, 0x04,
	0x77, 0x49, 0xdf, 0x1c, 0x6e, 0xe6, 0xd9, 0x9c, 0x6a, 0xff, 0x1c, 0x66, 0xe9, 0xd9, 0x9c, 0x87,
	0x80, 0x38, 0xf2, 0x4e, 0xd0, 0xf2, 0x2d, 0xc7, 0x8c, 0xfc, 0x97, 0xd8, 0x63, 0x07, 0x52, 0x31,
	0x6a, 0xac, 0xe7, 0x84, 0x75, 0x1c, 0x53, 0x38, 0x1d, 0xcd, 0xd1, 0xa6, 0x46, 0x4f, 0xf3, 0xd1,
	0xac, 0x47, 0x1a, 0xad, 0xff, 0xbc, 0x04, 0x8b, 0xfd, 0x3c, 0xdb, 0x71, 0x43, 0x6c, 0x47, 0xad,
	0xee, 0xff, 0xab, 0xf3, 0xd7, 0x51, 0x9d, 0x65, 0x8d, 0x85, 0x3c, 0x8d, 0xad, 0xe6, 0x69, 0xec,
	0x94, 0x52, 0x63, 0xa7, 0xaf, 0xa9, 0xb1, 0x33, 0x57, 0xd7, 0x58, 0xb4, 0x0a, 0x53, 0x2e, 0x31,
	0x39, 0x0f, 0x3c, 0x1b, 0xd7, 0x67, 0x59, 0xfc, 0x0e, 0x2e, 0xa1, 0x62, 0x78, 0xe0, 0xd9, 0x58,
	0xbf, 0x07, 0x4b, 0x6a, 0xf1, 0x24, 0x81, 0xfe, 0x03, 0x58, 0xe5, 0x01, 0x62, 0x6a, 0x08, 0x07,
	0xd1, 0xf8, 0x5f, 0xc8, 0x70, 0xb6, 0xfa, 0xaf, 0x40, 0xb5, 0xc3, 0x46, 0xf2, 0xdb, 0x03, 0xb7,
	0x01, 0xd0, 0x49, 0x26, 0xeb, 0x6b, 0x70, 0x7f, 0x08, 0x76, 0x12, 0xe8, 0x87, 0x2c, 0x52, 0x95,
	0x44, 0x8b, 0xdb, 0x1d, 0xdb, 0x0f, 0x1d, 0xba, 0x3a, 0xbb, 0xc2, 0x61, 0x0f, 0x8b, 0xb5, 0x79,
	0x23, 0xed, 0xa2, 0x0b, 0x69, 0x17, 0xad, 0x7f, 0x0c, 0x4b, 0x6a, 0x8c, 0x24, 0xa0, 0xc1, 0xe5,
	0x85, 0x45, 0xcc, 0x90, 0x41, 0x18, 0xde, 0xb2, 0x51, 0xb9, 0xb0, 0x08, 0x1f, 0xa2, 0xff, 0x93,
	0x06, 0x7a, 0x42, 0xf0, 0x73, 0x2b, 0x7c, 0xd9, 0x8b, 0x44, 0xb8, 0xea, 0x63, 0x27, 0x97, 0x2d,
	0xe9, 0x2b, 0x41, 0xa1, 0xff, 0xce, 0xf6, 0xc7, 0x30, 0x2f, 0x9b, 0x3d, 0xef, 0xcc, 0x97, 0x35,
	0xfc, 0x2a, 0x71, 0x91, 0x81, 0x24, 0x13, 0xe9, 0x9d, 0xf9, 0x54, 0x59, 0xf4, 0x4f, 0x60, 0x6d,
	0x28, 0xf1, 0x24, 0xc8, 0x30, 0xd1, 0xda, 0xa0, 0x89, 0xd6, 0x03, 0xb8, 0xf3, 0x14, 0x47, 0xa9,
	0xd3, 0xdb, 0xea, 0x36, 0xf3, 0xb7, 0x9f, 0x52, 0xa2, 0x42, 0x9f, 0x12, 0xad, 0x40, 0x55, 0x28,
	0x91, 0x1c, 0x29, 0x71, 0x10, 0x8d, 0x94, 0xf4, 0xef, 0x43, 0x3d, 0x7b, 0x45, 0x12, 0xa0, 0x8f,
	0x61, 0x9c, 0xae, 0x21, 0xe2, 0xd1, 0x6f, 0x0c, 0xe5, 0x14, 0x9d, 0x4c, 0xf9, 0x62, 0xb0, 0x69,
	0xfa, 0x9f, 0x6a, 0x70, 0x6f, 0xcb, 0x8a, 0xfa, 0xd1, 0x53, 0xae, 0xc5, 0x9b, 0x5a, 0x85, 0x29,
	0xb1, 0x29, 0x7e, 0x26, 0xfc, 0x6e, 0x04, 0x7c, 0x67, 0xcc, 0x16, 0xdd, 0x6c, 0x77, 0x2e, 0xac,
	0xe4, 0x52, 0x40, 0x02, 0xb4, 0x07, 0x15, 0x46, 0x42, 0xb2, 0xfe, 0x95, 0x76, 0x5a, 0x0e, 0x04,
	0x3e, 0xfd, 0x97, 0x1a, 0xdc, 0x49, 0xfa, 0xb7, 0x2c, 0x4f, 0x3e, 0xbb, 0xeb, 0x5c, 0x74, 0x6f,
	0xc3, 0x84, 0x4b, 0xcc, 0x53, 0xcb, 0x63, 0xdb, 0x2a, 0x1b, 0x25, 0x97, 0x6c, 0x59, 0x1e, 0x7a,
	0x1b, 0x66, 0x09, 0xb6, 0x7d, 0xcf, 0x31, 0x5b, 0xd6, 0x29, 0x6e, 0xc5, 0x99, 0x88, 0x92, 0x31,
	0xcd, 0xc1, 0xfb, 0x14, 0xda, 0x74, 0xd8, 0xdd, 0x45, 0x1e, 0xd7, 0xc2, 0x97, 0xb8, 0x25, 0x7c,
	0x47, 0x4d, 0x1a, 0xba, 0x4f, 0xe1, 0xf4, 0x16, 0x9f, 0x4d, 0x3b, 0x09, 0xf4, 0x10, 0x96, 0x25,
	0x93, 0x46, 0x2e, 0x8e, 0x22, 0xd7, 0x7e, 0xd9, 0x8d, 0x6d, 0x64, 0x9e, 0xcf, 0x5d, 0x84, 0x4a,
	0x60, 0x85, 0x29, 0xbd, 0x2c, 0x73, 0x00, 0xef, 0x14, 0x89, 0x8e, 0xe4, 0x1e, 0x5f, 0xe6, 0x80,
	0xa6, 0xa3, 0xaf, 0xc2, 0xbd, 0xbc, 0x35, 0x49, 0xa0, 0x77, 0x60, 0xd5, 0xc0, 0x6d, 0xff, 0x12,
	0xbf, 0x59, 0xc2, 0xd6, 0xe0, 0xfe, 0x90, 0x65, 0x49, 0xa0, 0x9f, 0xc0, 0xb2, 0x24, 0x72, 0xaf,
	0x8b, 0x30, 0xfd, 0x07, 0x70, 0x2f, 0x0f, 0x2d, 0x09, 0xd2, 0xd3, 0xb5, 0xbe, 0x7d, 0x2d, 0x03,
	0x24, 0xfb, 0xa2, 0x01, 0x24, 0xd5, 0xb4, 0x4a, 0xbc, 0x31, 0xa2, 0xff, 0x54, 0x83, 0xb7, 0x33,
	0x9c, 0xc7, 0x73, 0xcb, 0xbe, 0x70, 0x3d, 0xbc, 0xd9, 0x71, 0xdc, 0x1b, 0x89, 0x73, 0x2f, 0xa6,
	0x28, 0xa6, 0x62, 0x8a, 0x05, 0x98, 0x08, 0xb1, 0x45, 0x92, 0x7b, 0xb5, 0x68, 0xe9, 0xdf, 0x80,
	0x77, 0x46, 0xa2, 0x86, 0x04, 0xfa, 0x97, 0x15, 0xb8, 0x35, 0xa0, 0x99, 0x79, 0x44, 0x66, 0x06,
	0x7c, 0x85, 0x1b, 0x04, 0x7c, 0xd2, 0xae, 0x8b, 0xa9, 0x5d, 0x27, 0x41, 0xdb, 0x78, 0x5e, 0xd0,
	0x56, 0x7a, 0x1d, 0x41, 0x9b, 0x24, 0x53, 0x13, 0xfd, 0x32, 0xd5, 0x73, 0x3a, 0x93, 0xcc, 0xe9,
	0x94, 0xed, 0xf8, 0x4e, 0xf0, 0x00, 0x6a, 0x8c, 0x72, 0x39, 0xae, 0x28, 0xb3, 0x31, 0xcc, 0x5d,
	0xf5, 0x42, 0x07, 0xb4, 0x06, 0xd3, 0xb1, 0x97, 0xb5, 0xfd, 0x8e, 0x17, 0x89, 0xf4, 0xe2, 0x94,
	0x00, 0x6e, 0x53, 0x18, 0xfa, 0x2d, 0x98, 0x49, 0xd2, 0xa0, 0x7c, 0x14, 0x0f, 0xf9, 0x92, 0xe4,
	0x28, 0x1f, 0xb6, 0x0c, 0x70, 0xe9, 0xe2, 0x2f, 0xc4, 0x10, 0x1e, 0xf8, 0x55, 0x28, 0x84, 0x77,
	0xaf, 0x40, 0xb5, 0xdd, 0x35, 0xe3, 0x29, 0x22, 0xf6, 0x83, 0x76, 0x37, 0x4e, 0x2e, 0xb2, 0xfb,
	0xff, 0x85, 0x15, 0xc6, 0x6b, 0x4c, 0x8b, 0xfb, 0x3f, 0x05, 0x71, 0x0c, 0xf7, 0x61, 0xea, 0xc2,
	0x72, 0xcc, 0x33, 0xeb, 0xd2, 0xef, 0x84, 0xd8, 0x61, 0xd7, 0xba, 0xb2, 0x51, 0xbd, 0xb0, 0x9c,
	0x3d, 0x01, 0xa2, 0x6c, 0x71, 0x89, 0x49, 0x98, 0x0e, 0x89, 0x40, 0xae, 0xec, 0x12, 0xae, 0x53,
	0x6c, 0x1f, 0xa7, 0x9e, 0x1f, 0xb6, 0xad, 0x96, 0x49, 0x05, 0x15, 0xd7, 0x6b, 0x62, 0x1f, 0x02,
	0x7a, 0x44, 0x81, 0x49, 0x40, 0x2c, 0x72, 0xb1, 0xb7, 0x7a, 0x01, 0xf1, 0x11, 0xcf, 0xc7, 0xd6,
	0x61, 0x32, 0x08, 0xdd, 0x4b, 0xcb, 0xee, 0xd6, 0x11, 0xeb, 0x8c, 0x9b, 0x68, 0x1d, 0xe6, 0xd8,
	0x54, 0xd1, 0x36, 0x03, 0xbf, 0xe5, 0xda, 0xdd, 0xfa, 0x1c, 0x1b, 0x75, 0x8b, 0x76, 0x1d, 0xf2,
	0x9e, 0x43, 0xd6, 0x41, 0xf5, 0x44, 0x84, 0xdd, 0xf3, 0xfc, 0x74, 0x79, 0xab, 0x47, 0x02, 0x57,
	0xae, 0xdb, 0x12, 0x09, 0x5c, 0xc1, 0xe2, 0x85, 0xda, 0x5c, 0x75, 0xe2, 0x81, 0x0b, 0xbd, 0x85,
	0x84, 0x52, 0x89, 0xf1, 0xf7, 0x61, 0x8a, 0x7b, 0x8c, 0x10, 0xb7, 0x5d, 0xcf, 0xa9, 0xdf, 0xe1,
	0x59, 0x2c, 0x06, 0x33, 0x18, 0x28, 0xcb, 0x07, 0xd5, 0xb3, 0x7c, 0xd0, 0x0a, 0x54, 0x65, 0xe7,
	0x73, 0x97, 0x27, 0x4d, 0x5b, 0x89, 0xdb, 0xc9, 0x8c, 0xcb, 0x1b, 0xd7, 0x88, 0xcb, 0x7b, 0xb7,
	0x81, 0xc5, 0x11, 0x6e, 0x03, 0x8a, 0xeb, 0xd9, 0xd2, 0x4d, 0xae, 0x67, 0xfa, 0xdf, 0x8f, 0xc3,
	0x82, 0x1c, 0xf9, 0x8f, 0x96, 0xe6, 0x56, 0x90, 0x52, 0xb8, 0x69, 0x71, 0x23, 0xb6, 0x04, 0xc5,
	0x94, 0x25, 0x90, 0xcc, 0xd4, 0x78, 0xca, 0x4c, 0x7d, 0x13, 0xe6, 0x42, 0x1c, 0xb4, 0xba, 0x66,
	0xe4, 0x9b, 0x52, 0x28, 0x2d, 0xa2, 0x05, 0xd6, 0x75, 0xec, 0x6f, 0x27, 0x11, 0xf5, 0x3b, 0x30,
	0x6b, 0xfb, 0xde, 0x25, 0x0e, 0x09, 0xe3, 0x6b, 0x6c, 0x72, 0x2a, 0xc6, 0x8c, 0x0c, 0x4e, 0x19,
	0xfd, 0xf4, 0x45, 0xb2, 0xdf, 0x00, 0x96, 0x5f, 0xd7, 0xad, 0x95, 0xeb, 0x61, 0x25, 0x55, 0x13,
	0xe9, 0x09, 0x07, 0x8c, 0x20, 0x1c, 0xa9, 0x5b, 0x67, 0xb5, 0xef, 0xd6, 0x99, 0x25, 0xaf, 0x53,
	0xd7, 0xc8, 0xfc, 0xfc, 0x9b, 0x96, 0xae, 0x77, 0x49, 0x05, 0x8a, 0xbe, 0xdb, 0x8c, 0xd6, 0x7f,
	0x9b, 0xf9, 0x18, 0x26, 0x45, 0x63, 0x98, 0x94, 0xc4, 0xe7, 0x45, 0xc3, 0xd4, 0x78, 0x8e, 0x3a,
	0x07, 0x54, 0x54, 0xe7, 0x80, 0xb2, 0xf3, 0x39, 0xe3, 0xd9, 0xf9, 0x1c, 0xfd, 0xbf, 0x0b, 0x30,
	0xb3, 0x99, 0xba, 0x26, 0x65, 0x94, 0x32, 0x9a, 0x52, 0x29, 0x63, 0x66, 0xe3, 0xb1, 0x6a, 0x0b,
	0x69, 0x3c, 0x52, 0x93, 0x06, 0xf7, 0xaf, 0xa5, 0x02, 0x52, 0x7a, 0x93, 0x15, 0x90, 0xef, 0xc9,
	0x9c, 0x61, 0x79, 0xea, 0x32, 0x8c, 0xbf, 0x38, 0x78, 0xb1, 0x5b, 0x1b, 0x43, 0x15, 0x28, 0x35,
	0x9f, 0x6f, 0x3e, 0xdd, 0xad, 0x69, 0x68, 0x12, 0x8a, 0x4f, 0x9b, 0x7b, 0xb5, 0x02, 0x85, 0x7d,
	0xda, 0xdc, 0xd9, 0x3d, 0xa8, 0x15, 0x29, 0x6c, 0xfb, 0xf9, 0x51, 0x6d, 0x9c, 0xc2, 0x36, 0x4f,
	0x76, 0x9a, 0x07, 0xb5, 0x12, 0x9d, 0x7c, 0xbc, 0xfb, 0xd9, 0x71, 0x6d, 0x42, 0xff, 0x55, 0x09,
	0xaa, 0xd2, 0x71, 0x0f, 0x93, 0x21, 0x65, 0x90, 0x96, 0xa1, 0xd8, 0xc5, 0x4c, 0xc5, 0x56, 0xd6,
	0x41, 0xdf, 0x81, 0x5a, 0x62, 0x49, 0xe2, 0x11, 0xbc, 0xd2, 0x34, 0x2d, 0xcc, 0x88, 0xa8, 0x60,
	0xfe, 0x5a, 0x12, 0x57, 0x03, 0x31, 0xca, 0xe4, 0x48, 0x31, 0x4a, 0x39, 0x2b, 0x46, 0xe9, 0xc9,
	0x49, 0x25, 0x65, 0xbb, 0xf6, 0x60, 0x8a, 0x74, 0x4e, 0x63, 0x33, 0xc9, 0x93, 0xa7, 0x23, 0x6a,
	0x61, 0x95, 0x74, 0x4e, 0x45, 0x9b, 0xa4, 0xc3, 0xb2, 0x6a, 0x5f, 0x58, 0xa6, 0x30, 0xc8, 0x53,
	0x0a, 0x83, 0xbc, 0x0c, 0xc0, 0xca, 0xbe, 0x72, 0x38, 0xc4, 0x0a, 0xc1, 0x7c, 0x2b, 0xbf, 0x91,
	0xf4, 0xb6, 0xca, 0xb7, 0xd5, 0x6e, 0xe4, 0x66, 0x7f, 0xa5, 0xb1, 0xfa, 0x6d, 0xbf, 0xe5, 0xa4,
	0xa7, 0x9f, 0x9b, 0x24, 0xc9, 0x90, 0xec, 0x42, 0xa6, 0x64, 0x37, 0xa0, 0x4c, 0x6d, 0xd9, 0x73,
	0x3f, 0x8c, 0xeb, 0x82, 0x49, 0x9b, 0xda, 0x00, 0xce, 0x61, 0x2e, 0xf3, 0xbc, 0x21, 0xeb, 0x42,
	0xa9, 0x3f, 0xf0, 0xee, 0xa5, 0x2e, 0x26, 0xf2, 0x53, 0x17, 0x93, 0x03, 0xa9, 0x8b, 0x2f, 0x35,
	0x96, 0xa7, 0xcb, 0xde, 0x29, 0xcb, 0x5b, 0xc4, 0x62, 0x2d, 0xa7, 0x2e, 0x46, 0x93, 0x43, 0xbb,
	0x87, 0x8b, 0x52, 0xc9, 0xac, 0x7a, 0x9b, 0xee, 0xb8, 0x90, 0xde, 0xb1, 0xfe, 0x37, 0x1a, 0xac,
	0x6e, 0x59, 0x91, 0x7d, 0x91, 0x41, 0x09, 0xbd, 0x7f, 0xb1, 0xa2, 0x79, 0xbf, 0xb5, 0x29, 0xa6,
	0xad, 0xcd, 0xfd, 0x9e, 0xa6, 0x27, 0x36, 0xbf, 0x94, 0xe8, 0x2d, 0x33, 0x83, 0x29, 0x4e, 0x15,
	0xf3, 0x39, 0x35, 0x3e, 0xc0, 0xa9, 0xaf, 0x0a, 0x70, 0x7f, 0x08, 0x91, 0x24, 0x40, 0x5f, 0x40,
	0x2d, 0xa1, 0xd2, 0x3b, 0xf3, 0xcd, 0xb6, 0x15, 0x08, 0x9e, 0x3d, 0x57, 0xf1, 0x6c, 0x28, 0x52,
	0x99, 0xab, 0xcf, 0xad, 0x60, 0xd7, 0x8b, 0xc2, 0x2e, 0x95, 0x28, 0x19, 0xd8, 0x38, 0x83, 0xb9,
	0x8c, 0x61, 0x19, 0x4e, 0xf1, 0xdb, 0x50, 0xba, 0xb4, 0x5a, 0x1d, 0x7c, 0x15, 0xc7, 0xce, 0x67,
	0x7c, 0x54, 0x78, 0xa2, 0xe9, 0x18, 0xee, 0xca, 0xb4, 0xde, 0xfc, 0xa9, 0x45, 0x22, 0xee, 0x45,
	0x76, 0x62, 0xbc, 0xa1, 0x7f, 0x9c, 0x56, 0xc0, 0xf4, 0xf3, 0x0a, 0x76, 0x58, 0x89, 0x2c, 0xc4,
	0x6f, 0x1d, 0x20, 0x11, 0x06, 0xa2, 0x9f, 0xc3, 0x22, 0x63, 0xeb, 0x0d, 0xe9, 0x2c, 0x0e, 0xa5,
	0xf3, 0x9b, 0x00, 0x89, 0x81, 0x24, 0xc3, 0xe9, 0xfa, 0x4f, 0x0d, 0x96, 0xd4, 0x84, 0x91, 0x00,
	0xe1, 0x1e, 0x86, 0x9e, 0xe8, 0xec, 0xe4, 0x8a, 0x8e, 0x02, 0x55, 0x7c, 0x80, 0x89, 0xc4, 0xc4,
	0x74, 0x50, 0x69, 0xb1, 0x60, 0xb6, 0xaf, 0x3b, 0x43, 0x52, 0x9e, 0xa4, 0x25, 0x45, 0x1f, 0x26,
	0x29, 0x0e, 0x91, 0x05, 0xc5, 0x80, 0xc6, 0x96, 0xe5, 0x65, 0x09, 0x75, 0x96, 0x36, 0xf7, 0xc5,
	0x0e, 0xbd, 0xb4, 0x64, 0x41, 0x4a, 0x4b, 0xea, 0xcb, 0xf4, 0x58, 0x15, 0x38, 0x59, 0xd9, 0xe3,
	0xce, 0x8e, 0x4b, 0xa2, 0xd0, 0x3d, 0xed, 0x44, 0xf8, 0xd0, 0x27, 0xd1, 0x9e, 0xdb, 0x8a, 0x70,
	0x48, 0xd7, 0x5b, 0x81, 0xea, 0x19, 0x6b, 0x70, 0xf5, 0xe6, 0xf9, 0x30, 0xe0, 0x20, 0x66, 0x1c,
	0xfa, 0x73, 0xc4, 0x85, 0xfe, 0x1c, 0xb1, 0xfe, 0xcf, 0x1a, 0xd4, 0xb3, 0xd1, 0x93, 0x00, 0xbd,
	0x84, 0x59, 0x36, 0x5d, 0x2c, 0xd2, 0x3b, 0xbb, 0x6d, 0x15, 0xd7, 0x54, 0xa8, 0xd6, 0x7b, 0xcd,
	0xe4, 0xe8, 0xa6, 0x03, 0x19, 0xd6, 0xf8, 0x2e, 0xa0, 0xc1, 0x41, 0x19, 0x07, 0x38, 0x2f, 0x1f,
	0x60, 0x59, 0x3e, 0x9c, 0xed, 0x9e, 0xc1, 0xed, 0x49, 0xcd, 0x51, 0x2f, 0x6e, 0x10, 0x2c, 0xcb,
	0x17, 0xe6, 0x23, 0xa8, 0x4a, 0x53, 0xd0, 0x0e, 0x54, 0xa5, 0x90, 0xe5, 0x2a, 0x9e, 0x02, 0x7a,
	0x11, 0x8b, 0xfe, 0x3f, 0x5a, 0xcf, 0xcc, 0x2a, 0x48, 0x23, 0x01, 0x6a, 0xf7, 0x85, 0x47, 0x7c,
	0xb1, 0x3f, 0x18, 0x66, 0x62, 0x95, 0x08, 0xd7, 0xa5, 0x36, 0x67, 0xb9, 0x1c, 0x45, 0x35, 0x6c,
	0xa8, 0xf5, 0x0f, 0xb8, 0x81, 0x65, 0x95, 0xd7, 0x96, 0xce, 0xe4, 0x17, 0x9a, 0xf4, 0x74, 0xa9,
	0xe9, 0x39, 0xf8, 0x55, 0x92, 0x65, 0xbc, 0x59, 0x89, 0x84, 0x9e, 0xe4, 0x85, 0xeb, 0x24, 0x49,
	0x19, 0x51, 0xd1, 0xa3, 0x20, 0x91, 0x8d, 0x59, 0x84, 0x8a, 0x7f, 0x76, 0x46, 0x52, 0xc9, 0x68,
	0x0e, 0xe0, 0xb1, 0x8a, 0x1f, 0xe0, 0xd0, 0x8a, 0xfc, 0x50, 0xdc, 0x71, 0x92, 0xb6, 0xfe, 0x5f,
	0x05, 0xb8, 0x4b, 0x95, 0xa3, 0x6f, 0x65, 0x07, 0xbf, 0x1a, 0x62, 0x66, 0x69, 0x7c, 0x7b, 0xcd,
	0x5c, 0x44, 0x95, 0x64, 0x3f, 0xb1, 0x4c, 0xe7, 0x44, 0x3f, 0x84, 0xfa, 0x69, 0x87, 0xb8, 0x1e,
	0x26, 0x84, 0x89, 0x09, 0x8d, 0xc6, 0x28, 0x5d, 0xbd, 0xb4, 0xc4, 0xed, 0xb8, 0x7f, 0x9b, 0x75,
	0x33, 0xaa, 0x9b, 0x0e, 0x3d, 0xd7, 0x4e, 0x12, 0x64, 0xd1, 0x9f, 0xd4, 0x56, 0x9d, 0x62, 0x1a,
	0x61, 0x26, 0xaf, 0x29, 0xa7, 0x8d, 0x0a, 0x83, 0xb0, 0x94, 0xe5, 0x5d, 0x28, 0x63, 0xcf, 0xe1,
	0x9d, 0x3c, 0xc0, 0x9a, 0xc4, 0x9e, 0xc3, 0xba, 0x52, 0x7c, 0x2d, 0xf7, 0xf1, 0x35, 0x71, 0x28,
	0x15, 0x39, 0xce, 0xeb, 0x3b, 0x2b, 0xe8, 0x3f, 0x2b, 0xdd, 0x85, 0x86, 0x8a, 0xe3, 0x24, 0x40,
	0x9f, 0x0c, 0xd6, 0x99, 0x86, 0xbf, 0xf0, 0x4a, 0x09, 0x9b, 0x54, 0x6c, 0xfa, 0x71, 0x01, 0x16,
	0xf9, 0x13, 0xc3, 0xaf, 0xdf, 0xf9, 0x52, 0xeb, 0xc4, 0x2f, 0x36, 0x8c, 0xf1, 0x71, 0xbc, 0xc6,
	0x40, 0x8c, 0xf7, 0x83, 0xe7, 0x98, 0x27, 0x12, 0x13, 0x39, 0x22, 0xa1, 0xdf, 0x83, 0x25, 0x35,
	0x1b, 0x48, 0xa0, 0xff, 0xab, 0x06, 0x4b, 0xcf, 0x5c, 0x07, 0xf7, 0x75, 0x8b, 0xfd, 0xfc, 0xe6,
	0x19, 0x25, 0x0b, 0xd4, 0xf8, 0x80, 0xf2, 0xcb, 0xfa, 0x5d, 0xea, 0xd3, 0xef, 0x15, 0x58, 0xce,
	0xd9, 0x18, 0x09, 0xf4, 0xef, 0x50, 0x2f, 0x1f, 0x97, 0x3e, 0x7d, 0x12, 0x3d, 0x4b, 0xf0, 0x8e,
	0x54, 0x78, 0xd5, 0xff, 0x5d, 0x63, 0x91, 0x5a, 0x36, 0x02, 0x12, 0xa0, 0x1f, 0xc2, 0x3c, 0xc3,
	0x20, 0x6d, 0x41, 0x72, 0xae, 0x79, 0x06, 0x5f, 0x85, 0x72, 0x3d, 0x0d, 0x4a, 0x7c, 0x2c, 0x4b,
	0x52, 0xa7, 0xe0, 0x8d, 0x1d, 0x58, 0xc8, 0x1e, 0x3c, 0xcc, 0xd7, 0x4e, 0xcb, 0x76, 0xfd, 0x97,
	0x05, 0x58, 0xdb, 0xf3, 0x43, 0x1b, 0x67, 0xca, 0xd0, 0x1b, 0x15, 0x92, 0x19, 0x28, 0x34, 0x77,
	0x84, 0x7c, 0x14, 0x9a, 0x3b, 0xea, 0x54, 0x6d, 0x03, 0xa8, 0x15, 0x70, 0xe9, 0x6d, 0x55, 0x68,
	0x50, 0xd2, 0xbe, 0x81, 0x39, 0xe4, 0x51, 0x9d, 0x83, 0x5b, 0xcc, 0x16, 0xb2, 0xa8, 0x6e, 0x07,
	0xb7, 0x52, 0x02, 0x58, 0xe9, 0x13, 0xc0, 0xb7, 0xe1, 0xad, 0xe1, 0xbc, 0x23, 0x81, 0xfe, 0x0f,
	0x05, 0x78, 0x67, 0xc0, 0x2c, 0x4a, 0x33, 0xbf, 0x2e, 0xda, 0x98, 0x66, 0xde, 0x78, 0x1e, 0xf3,
	0x4a, 0x39, 0xbe, 0x64, 0x42, 0xe5, 0x4b, 0x26, 0x65, 0x5f, 0xb2, 0x06, 0xd3, 0x2e, 0xdb, 0x7d,
	0xac, 0xfc, 0x3c, 0x05, 0x35, 0xc5, 0x81, 0xc2, 0x9f, 0xfc, 0x75, 0x01, 0x96, 0x12, 0xae, 0x49,
	0x1c, 0x7b, 0x5d, 0xc1, 0x07, 0x97, 0xb9, 0x42, 0x22, 0x73, 0x03, 0x44, 0x15, 0x07, 0x89, 0x4a,
	0x6f, 0x76, 0x7c, 0x30, 0x20, 0xf9, 0x35, 0x08, 0xa7, 0x2c, 0x85, 0xe5, 0x3e, 0x29, 0xfc, 0x13,
	0x78, 0x30, 0x9a, 0x70, 0x91, 0x00, 0xfd, 0xe1, 0xa0, 0x07, 0xfe, 0x60, 0x28, 0xcf, 0x32, 0xf8,
	0xde, 0xf3, 0xc3, 0xef, 0x7e, 0x96, 0x7d, 0xf1, 0x60, 0xf7, 0x96, 0x59, 0xa8, 0xee, 0x35, 0xf7,
	0x8f, 0x77, 0x0d, 0x53, 0xa4, 0x78, 0xe7, 0x60, 0x56, 0x00, 0x9e, 0x1d, 0x1c, 0x9b, 0x87, 0x07,
	0x47, 0xc7, 0x35, 0x0d, 0x2d, 0x00, 0x12, 0xc0, 0xe6, 0x8b, 0xa3, 0x5d, 0x43, 0xc0, 0x0b, 0xef,
	0xee, 0x4b, 0xaf, 0x49, 0xd2, 0xa6, 0x0e, 0xcd, 0x43, 0xed, 0x59, 0x73, 0x67, 0xd7, 0x3c, 0x3a,
	0xde, 0x3c, 0x3e, 0x39, 0x8a, 0xb1, 0x4f, 0x41, 0xf9, 0xc5, 0xc1, 0xb1, 0x49, 0x7b, 0x6a, 0x1a,
	0x9a, 0x86, 0x0a, 0x45, 0xc4, 0x9b, 0x85, 0x77, 0x2f, 0xe1, 0x96, 0xb4, 0x11, 0x81, 0xe7, 0x36,
	0xdc, 0xda, 0x3b, 0x30, 0xb6, 0x77, 0xe3, 0x95, 0x05, 0xa2, 0x3a, 0xcc, 0xf7, 0x81, 0x8f, 0xcd,
	0xef, 0xef, 0xc6, 0xb4, 0xca, 0x3d, 0x9f, 0x6e, 0xee, 0x37, 0x77, 0x6a, 0x85, 0x81, 0x19, 0xbb,
	0x9f, 0x1d, 0x36, 0x8d, 0xdd, 0x9d, 0x5a, 0x71, 0xe3, 0x7f, 0x57, 0x61, 0xee, 0xe4, 0xdc, 0x96,
	0xee, 0x85, 0x3c, 0x9b, 0xee, 0x43, 0xad, 0xff, 0x99, 0x1c, 0x52, 0xbe, 0xc4, 0xca, 0x78, 0xb6,
	0xdd, 0x78, 0x38, 0xfa, 0x60, 0x12, 0xe8, 0x63, 0xe8, 0x27, 0x5a, 0xfa, 0x3b, 0x15, 0xf9, 0x61,
	0x1e, 0x7a, 0x7f, 0x54, 0x64, 0xd2, 0x4b, 0xd3, 0xc6, 0x07, 0x57, 0x9f, 0xc4, 0x28, 0xf9, 0x4b,
	0x0d, 0x96, 0x73, 0x1f, 0xe9, 0xa1, 0x27, 0x2a, 0xcc, 0xc3, 0x5e, 0x0e, 0x36, 0xbe, 0x7d, 0xcd,
	0x99, 0x09, 0x8b, 0x54, 0xcf, 0xf8, 0xd4, 0x2c, 0xca, 0x79, 0x4a, 0xa8, 0x66, 0x51, 0xde, 0x6b,
	0x41, 0x7d, 0x0c, 0xfd, 0x95, 0x06, 0x2b, 0x43, 0xde, 0xd4, 0xa1, 0x8f, 0x86, 0x6a, 0xae, 0xf2,
	0x25, 0x61, 0xe3, 0x77, 0xaf, 0x3d, 0x97, 0x91, 0xf7, 0x43, 0x98, 0xcf, 0x7a, 0x32, 0x87, 0x1e,
	0xe5, 0x6c, 0x37, 0xeb, 0x49, 0x5f, 0xe3, 0xbd, 0xab, 0x4d, 0x60, 0x8b, 0xff, 0x2c, 0x15, 0x96,
	0x0d, 0x3c, 0x69, 0x43, 0xdf, 0x1a, 0x1e, 0x78, 0x65, 0xbd, 0xc4, 0x6b, 0x7c, 0x78, 0xad, 0x79,
	0x31, 0x3f, 0xb2, 0x1e, 0x8f, 0xa9, 0xf9, 0xa1, 0x78, 0x26, 0xd7, 0x78, 0xef, 0x6a, 0x13, 0xd8,
	0xe2, 0x3f, 0xd5, 0xe0, 0xae, 0xac, 0x71, 0xa9, 0x47, 0x51, 0xe8, 0xf1, 0x08, 0x4a, 0x3a, 0xf8,
	0x3e, 0xab, 0xf1, 0xad, 0xeb, 0x4c, 0x4b, 0xb4, 0x3b, 0xf7, 0x81, 0x98, 0x5a, 0xbb, 0x87, 0x3d,
	0x67, 0x53, 0x6b, 0xf7, 0xf0, 0x17, 0x69, 0x63, 0xe8, 0xcf, 0xd3, 0x85, 0x93, 0x7e, 0xaa, 0x1e,
	0x8f, 0x20, 0x8a, 0x57, 0x61, 0x54, 0xfe, 0x43, 0x35, 0x7d, 0x0c, 0xfd, 0xad, 0x06, 0x6b, 0x23,
	0x3c, 0xf0, 0x42, 0xdf, 0xb9, 0x82, 0x49, 0xcb, 0x78, 0xab, 0xd6, 0xf8, 0xfd, 0x1b, 0xcd, 0x67,
	0xa4, 0x7e, 0xa5, 0xb1, 0x57, 0xb9, 0x59, 0xdf, 0xf6, 0xa1, 0x8d, 0x1c, 0x06, 0x28, 0x3e, 0x32,
	0x6c, 0xbc, 0x7f, 0xe5, 0x39, 0xb1, 0x9a, 0x65, 0xc5, 0x1a, 0x6a, 0x35, 0x53, 0x64, 0x5c, 0xd5,
	0x6a, 0xa6, 0x4a, 0x7c, 0xea, 0x63, 0xe8, 0x55, 0xfa, 0xeb, 0x29, 0x91, 0x25, 0x43, 0xeb, 0xa3,
	0x38, 0xc1, 0x5e, 0x7a, 0xbf, 0xf1, 0xe8, 0x4a, 0xe3, 0x13, 0xee, 0x2b, 0x52, 0xcb, 0x6a, 0xee,
	0xab, 0xf3, 0xdb, 0x6a, 0xee, 0xe7, 0xe5, 0xaf, 0x13, 0x21, 0xc8, 0x2a, 0xc7, 0xe5, 0x0a, 0x81,
	0xa2, 0x52, 0x99, 0x2b, 0x04, 0xaa, 0x9a, 0x9f, 0xb0, 0x2f, 0xb9, 0x65, 0x29, 0xb5, 0x7d, 0x19,
	0x56, 0xc7, 0x53, 0xdb, 0x97, 0xa1, 0x75, 0x30, 0x7d, 0x0c, 0xfd, 0x99, 0x06, 0x77, 0x95, 0x1f,
	0x79, 0xa3, 0x0f, 0xd4, 0x5a, 0xa8, 0xfe, 0x40, 0xbd, 0xf1, 0xf8, 0x1a, 0xb3, 0x48, 0xc0, 0x48,
	0x51, 0x7e, 0x04, 0xac, 0x26, 0x25, 0xef, 0x53, 0x65, 0x35, 0x29, 0xb9, 0x5f, 0x1b, 0xa3, 0x1f,
	0xc1, 0x42, 0x76, 0xb1, 0x0c, 0xfd, 0xce, 0x28, 0xe7, 0x9f, 0xa6, 0x61, 0xe3, 0xaa, 0x53, 0x48,
	0x80, 0x7e, 0xac, 0x41, 0x5d, 0x55, 0x8b, 0x42, 0xef, 0x5f, 0xbd, 0x7a, 0x95, 0x13, 0xd4, 0xe5,
	0x56, 0xcf, 0xba, 0xe9, 0x4f, 0xdd, 0x92, 0x97, 0x9c, 0x23, 0x19, 0x04, 0xe9, 0xef, 0x00, 0xd4,
	0xc6, 0x4b, 0xf5, 0x15, 0x3a, 0xfa, 0x0b, 0x49, 0x65, 0x32, 0xcb, 0x0c, 0xc3, 0x55, 0x46, 0x55,
	0x89, 0x19, 0xae, 0x32, 0xea, 0x42, 0xc9, 0xdf, 0x69, 0xf0, 0xd6, 0x28, 0x57, 0x57, 0xa4, 0xf4,
	0x60, 0x23, 0x66, 0x55, 0x1a, 0xdf, 0xbd, 0x19, 0x02, 0x12, 0xa0, 0x9f, 0x6b, 0xb0, 0x3a, 0x2c,
	0xd9, 0x83, 0x94, 0x51, 0xf5, 0x08, 0x29, 0xb6, 0xc6, 0xef, 0x5d, 0x7f, 0xb2, 0xd0, 0x78, 0x65,
	0x36, 0x54, 0xad, 0xf1, 0x79, 0x99, 0x61, 0xb5, 0xc6, 0xe7, 0xa6, 0x5d, 0xd1, 0x97, 0xcc, 0x5d,
	0x65, 0xe6, 0x38, 0xf3, 0xdc, 0x95, 0x2a, 0x51, 0x9b, 0xe7, 0xae, 0xd4, 0xb9, 0xd9, 0x1f, 0xc1,
	0x42, 0x76, 0x25, 0x42, 0x6d, 0x76, 0x94, 0xb5, 0x22, 0xb5, 0xd9, 0xc9, 0x29, 0x76, 0x50, 0xb3,
	0xa3, 0x4a, 0xcc, 0xab, 0xcd, 0x4e, 0x4e, 0x45, 0x43, 0x6d, 0x76, 0xf2, 0xf2, 0xff, 0x5b, 0x1f,
	0xfd, 0xd1, 0x93, 0x73, 0xbf, 0x65, 0x79, 0xe7, 0xeb, 0x8f, 0x37, 0xa2, 0x68, 0xdd, 0xf6, 0xdb,
	0x8f, 0xd8, 0x9f, 0xa3, 0xd8, 0x7e, 0xeb, 0x11, 0xc1, 0xe1, 0xa5, 0x6b, 0x63, 0xf2, 0x28, 0xfb,
	0x0f, 0x57, 0x4e, 0x27, 0xd8, 0xc8, 0xf7, 0xff, 0x2f, 0x00, 0x00, 0xff, 0xff, 0xe7, 0x75, 0xb9,
	0x08, 0x91, 0x45, 0x00, 0x00,
}
