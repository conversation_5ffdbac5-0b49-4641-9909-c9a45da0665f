// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ugc/interactive.proto

package interactive // import "golang.52tt.com/protocol/services/ugc/interactive"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type MsgType int32

const (
	MsgType_NEW_FOLLOW   MsgType = 0
	MsgType_NEW_ATTITUDE MsgType = 1
	MsgType_NEW_COMMENT  MsgType = 2
	MsgType_NEW_AT       MsgType = 3
	MsgType_NEW_CONCERN  MsgType = 4
)

var MsgType_name = map[int32]string{
	0: "NEW_FOLLOW",
	1: "NEW_ATTITUDE",
	2: "NEW_COMMENT",
	3: "NEW_AT",
	4: "NEW_CONCERN",
}
var MsgType_value = map[string]int32{
	"NEW_FOLLOW":   0,
	"NEW_ATTITUDE": 1,
	"NEW_COMMENT":  2,
	"NEW_AT":       3,
	"NEW_CONCERN":  4,
}

func (x MsgType) String() string {
	return proto.EnumName(MsgType_name, int32(x))
}
func (MsgType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_interactive_4b873c933a5dec8e, []int{0}
}

type CommentMsg struct {
	PostObjectId         string   `protobuf:"bytes,1,opt,name=post_object_id,json=postObjectId,proto3" json:"post_object_id,omitempty"`
	CommentObjectId      string   `protobuf:"bytes,2,opt,name=comment_object_id,json=commentObjectId,proto3" json:"comment_object_id,omitempty"`
	CommentId            string   `protobuf:"bytes,3,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommentMsg) Reset()         { *m = CommentMsg{} }
func (m *CommentMsg) String() string { return proto.CompactTextString(m) }
func (*CommentMsg) ProtoMessage()    {}
func (*CommentMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_interactive_4b873c933a5dec8e, []int{0}
}
func (m *CommentMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentMsg.Unmarshal(m, b)
}
func (m *CommentMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentMsg.Marshal(b, m, deterministic)
}
func (dst *CommentMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentMsg.Merge(dst, src)
}
func (m *CommentMsg) XXX_Size() int {
	return xxx_messageInfo_CommentMsg.Size(m)
}
func (m *CommentMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentMsg.DiscardUnknown(m)
}

var xxx_messageInfo_CommentMsg proto.InternalMessageInfo

func (m *CommentMsg) GetPostObjectId() string {
	if m != nil {
		return m.PostObjectId
	}
	return ""
}

func (m *CommentMsg) GetCommentObjectId() string {
	if m != nil {
		return m.CommentObjectId
	}
	return ""
}

func (m *CommentMsg) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

type AttitudeMsg struct {
	PostObjectId         string   `protobuf:"bytes,1,opt,name=post_object_id,json=postObjectId,proto3" json:"post_object_id,omitempty"`
	CommentObjectId      string   `protobuf:"bytes,2,opt,name=comment_object_id,json=commentObjectId,proto3" json:"comment_object_id,omitempty"`
	AttitudeType         int32    `protobuf:"varint,3,opt,name=attitude_type,json=attitudeType,proto3" json:"attitude_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AttitudeMsg) Reset()         { *m = AttitudeMsg{} }
func (m *AttitudeMsg) String() string { return proto.CompactTextString(m) }
func (*AttitudeMsg) ProtoMessage()    {}
func (*AttitudeMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_interactive_4b873c933a5dec8e, []int{1}
}
func (m *AttitudeMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AttitudeMsg.Unmarshal(m, b)
}
func (m *AttitudeMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AttitudeMsg.Marshal(b, m, deterministic)
}
func (dst *AttitudeMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AttitudeMsg.Merge(dst, src)
}
func (m *AttitudeMsg) XXX_Size() int {
	return xxx_messageInfo_AttitudeMsg.Size(m)
}
func (m *AttitudeMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_AttitudeMsg.DiscardUnknown(m)
}

var xxx_messageInfo_AttitudeMsg proto.InternalMessageInfo

func (m *AttitudeMsg) GetPostObjectId() string {
	if m != nil {
		return m.PostObjectId
	}
	return ""
}

func (m *AttitudeMsg) GetCommentObjectId() string {
	if m != nil {
		return m.CommentObjectId
	}
	return ""
}

func (m *AttitudeMsg) GetAttitudeType() int32 {
	if m != nil {
		return m.AttitudeType
	}
	return 0
}

type AtMsg struct {
	PostObjectId         string   `protobuf:"bytes,1,opt,name=post_object_id,json=postObjectId,proto3" json:"post_object_id,omitempty"`
	CommentObjectId      string   `protobuf:"bytes,2,opt,name=comment_object_id,json=commentObjectId,proto3" json:"comment_object_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AtMsg) Reset()         { *m = AtMsg{} }
func (m *AtMsg) String() string { return proto.CompactTextString(m) }
func (*AtMsg) ProtoMessage()    {}
func (*AtMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_interactive_4b873c933a5dec8e, []int{2}
}
func (m *AtMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AtMsg.Unmarshal(m, b)
}
func (m *AtMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AtMsg.Marshal(b, m, deterministic)
}
func (dst *AtMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AtMsg.Merge(dst, src)
}
func (m *AtMsg) XXX_Size() int {
	return xxx_messageInfo_AtMsg.Size(m)
}
func (m *AtMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_AtMsg.DiscardUnknown(m)
}

var xxx_messageInfo_AtMsg proto.InternalMessageInfo

func (m *AtMsg) GetPostObjectId() string {
	if m != nil {
		return m.PostObjectId
	}
	return ""
}

func (m *AtMsg) GetCommentObjectId() string {
	if m != nil {
		return m.CommentObjectId
	}
	return ""
}

type ConcernMsg struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConcernMsg) Reset()         { *m = ConcernMsg{} }
func (m *ConcernMsg) String() string { return proto.CompactTextString(m) }
func (*ConcernMsg) ProtoMessage()    {}
func (*ConcernMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_interactive_4b873c933a5dec8e, []int{3}
}
func (m *ConcernMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConcernMsg.Unmarshal(m, b)
}
func (m *ConcernMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConcernMsg.Marshal(b, m, deterministic)
}
func (dst *ConcernMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConcernMsg.Merge(dst, src)
}
func (m *ConcernMsg) XXX_Size() int {
	return xxx_messageInfo_ConcernMsg.Size(m)
}
func (m *ConcernMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_ConcernMsg.DiscardUnknown(m)
}

var xxx_messageInfo_ConcernMsg proto.InternalMessageInfo

type InteractiveMsg struct {
	Id                   string       `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Time                 int64        `protobuf:"varint,2,opt,name=time,proto3" json:"time,omitempty"`
	UserId               uint32       `protobuf:"varint,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	FromUserId           uint32       `protobuf:"varint,4,opt,name=from_user_id,json=fromUserId,proto3" json:"from_user_id,omitempty"`
	Type                 MsgType      `protobuf:"varint,5,opt,name=type,proto3,enum=ugc.interactive.MsgType" json:"type,omitempty"`
	CommentMsg           *CommentMsg  `protobuf:"bytes,6,opt,name=comment_msg,json=commentMsg,proto3" json:"comment_msg,omitempty"`
	AttitudeMsg          *AttitudeMsg `protobuf:"bytes,7,opt,name=attitude_msg,json=attitudeMsg,proto3" json:"attitude_msg,omitempty"`
	AtMsg                *AtMsg       `protobuf:"bytes,8,opt,name=at_msg,json=atMsg,proto3" json:"at_msg,omitempty"`
	ConcernMsg           *ConcernMsg  `protobuf:"bytes,9,opt,name=concern_msg,json=concernMsg,proto3" json:"concern_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *InteractiveMsg) Reset()         { *m = InteractiveMsg{} }
func (m *InteractiveMsg) String() string { return proto.CompactTextString(m) }
func (*InteractiveMsg) ProtoMessage()    {}
func (*InteractiveMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_interactive_4b873c933a5dec8e, []int{4}
}
func (m *InteractiveMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InteractiveMsg.Unmarshal(m, b)
}
func (m *InteractiveMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InteractiveMsg.Marshal(b, m, deterministic)
}
func (dst *InteractiveMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InteractiveMsg.Merge(dst, src)
}
func (m *InteractiveMsg) XXX_Size() int {
	return xxx_messageInfo_InteractiveMsg.Size(m)
}
func (m *InteractiveMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_InteractiveMsg.DiscardUnknown(m)
}

var xxx_messageInfo_InteractiveMsg proto.InternalMessageInfo

func (m *InteractiveMsg) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *InteractiveMsg) GetTime() int64 {
	if m != nil {
		return m.Time
	}
	return 0
}

func (m *InteractiveMsg) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *InteractiveMsg) GetFromUserId() uint32 {
	if m != nil {
		return m.FromUserId
	}
	return 0
}

func (m *InteractiveMsg) GetType() MsgType {
	if m != nil {
		return m.Type
	}
	return MsgType_NEW_FOLLOW
}

func (m *InteractiveMsg) GetCommentMsg() *CommentMsg {
	if m != nil {
		return m.CommentMsg
	}
	return nil
}

func (m *InteractiveMsg) GetAttitudeMsg() *AttitudeMsg {
	if m != nil {
		return m.AttitudeMsg
	}
	return nil
}

func (m *InteractiveMsg) GetAtMsg() *AtMsg {
	if m != nil {
		return m.AtMsg
	}
	return nil
}

func (m *InteractiveMsg) GetConcernMsg() *ConcernMsg {
	if m != nil {
		return m.ConcernMsg
	}
	return nil
}

type FollowingStreamUpdateInfo struct {
	UpdateAt             uint64   `protobuf:"varint,1,opt,name=update_at,json=updateAt,proto3" json:"update_at,omitempty"`
	LatestActors         []uint32 `protobuf:"varint,2,rep,packed,name=latest_actors,json=latestActors,proto3" json:"latest_actors,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FollowingStreamUpdateInfo) Reset()         { *m = FollowingStreamUpdateInfo{} }
func (m *FollowingStreamUpdateInfo) String() string { return proto.CompactTextString(m) }
func (*FollowingStreamUpdateInfo) ProtoMessage()    {}
func (*FollowingStreamUpdateInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_interactive_4b873c933a5dec8e, []int{5}
}
func (m *FollowingStreamUpdateInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FollowingStreamUpdateInfo.Unmarshal(m, b)
}
func (m *FollowingStreamUpdateInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FollowingStreamUpdateInfo.Marshal(b, m, deterministic)
}
func (dst *FollowingStreamUpdateInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FollowingStreamUpdateInfo.Merge(dst, src)
}
func (m *FollowingStreamUpdateInfo) XXX_Size() int {
	return xxx_messageInfo_FollowingStreamUpdateInfo.Size(m)
}
func (m *FollowingStreamUpdateInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FollowingStreamUpdateInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FollowingStreamUpdateInfo proto.InternalMessageInfo

func (m *FollowingStreamUpdateInfo) GetUpdateAt() uint64 {
	if m != nil {
		return m.UpdateAt
	}
	return 0
}

func (m *FollowingStreamUpdateInfo) GetLatestActors() []uint32 {
	if m != nil {
		return m.LatestActors
	}
	return nil
}

type Uinfo struct {
	UserId                    uint32                     `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	LastReadId                string                     `protobuf:"bytes,2,opt,name=last_read_id,json=lastReadId,proto3" json:"last_read_id,omitempty"`
	NewAttitudeCount          int32                      `protobuf:"varint,3,opt,name=new_attitude_count,json=newAttitudeCount,proto3" json:"new_attitude_count,omitempty"`
	NewCommentCount           int32                      `protobuf:"varint,4,opt,name=new_comment_count,json=newCommentCount,proto3" json:"new_comment_count,omitempty"`
	NewFollowCount            int32                      `protobuf:"varint,5,opt,name=new_follow_count,json=newFollowCount,proto3" json:"new_follow_count,omitempty"`
	UnreadSequence            uint32                     `protobuf:"varint,6,opt,name=unread_sequence,json=unreadSequence,proto3" json:"unread_sequence,omitempty"`
	FollowingStreamUpdateInfo *FollowingStreamUpdateInfo `protobuf:"bytes,7,opt,name=following_stream_update_info,json=followingStreamUpdateInfo,proto3" json:"following_stream_update_info,omitempty"`
	NewAtCount                int32                      `protobuf:"varint,8,opt,name=new_at_count,json=newAtCount,proto3" json:"new_at_count,omitempty"`
	NewConcernCount           int32                      `protobuf:"varint,9,opt,name=new_concern_count,json=newConcernCount,proto3" json:"new_concern_count,omitempty"`
	LastReadAttitudeId        string                     `protobuf:"bytes,11,opt,name=last_read_attitude_id,json=lastReadAttitudeId,proto3" json:"last_read_attitude_id,omitempty"`
	LastReadAttitudeTime      int64                      `protobuf:"varint,12,opt,name=last_read_attitude_time,json=lastReadAttitudeTime,proto3" json:"last_read_attitude_time,omitempty"`
	LastReadCommentId         string                     `protobuf:"bytes,13,opt,name=last_read_comment_id,json=lastReadCommentId,proto3" json:"last_read_comment_id,omitempty"`
	LastReadCommentTime       int64                      `protobuf:"varint,14,opt,name=last_read_comment_time,json=lastReadCommentTime,proto3" json:"last_read_comment_time,omitempty"`
	LastReadFollowId          string                     `protobuf:"bytes,15,opt,name=last_read_follow_id,json=lastReadFollowId,proto3" json:"last_read_follow_id,omitempty"`
	LastReadFollowTime        int64                      `protobuf:"varint,16,opt,name=last_read_follow_time,json=lastReadFollowTime,proto3" json:"last_read_follow_time,omitempty"`
	LastReadAtId              string                     `protobuf:"bytes,17,opt,name=last_read_at_id,json=lastReadAtId,proto3" json:"last_read_at_id,omitempty"`
	LastReadAtTime            int64                      `protobuf:"varint,18,opt,name=last_read_at_time,json=lastReadAtTime,proto3" json:"last_read_at_time,omitempty"`
	LastReadConcernId         string                     `protobuf:"bytes,19,opt,name=last_read_concern_id,json=lastReadConcernId,proto3" json:"last_read_concern_id,omitempty"`
	LastReadConcernTime       int64                      `protobuf:"varint,20,opt,name=last_read_concern_time,json=lastReadConcernTime,proto3" json:"last_read_concern_time,omitempty"`
	XXX_NoUnkeyedLiteral      struct{}                   `json:"-"`
	XXX_unrecognized          []byte                     `json:"-"`
	XXX_sizecache             int32                      `json:"-"`
}

func (m *Uinfo) Reset()         { *m = Uinfo{} }
func (m *Uinfo) String() string { return proto.CompactTextString(m) }
func (*Uinfo) ProtoMessage()    {}
func (*Uinfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_interactive_4b873c933a5dec8e, []int{6}
}
func (m *Uinfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Uinfo.Unmarshal(m, b)
}
func (m *Uinfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Uinfo.Marshal(b, m, deterministic)
}
func (dst *Uinfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Uinfo.Merge(dst, src)
}
func (m *Uinfo) XXX_Size() int {
	return xxx_messageInfo_Uinfo.Size(m)
}
func (m *Uinfo) XXX_DiscardUnknown() {
	xxx_messageInfo_Uinfo.DiscardUnknown(m)
}

var xxx_messageInfo_Uinfo proto.InternalMessageInfo

func (m *Uinfo) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *Uinfo) GetLastReadId() string {
	if m != nil {
		return m.LastReadId
	}
	return ""
}

func (m *Uinfo) GetNewAttitudeCount() int32 {
	if m != nil {
		return m.NewAttitudeCount
	}
	return 0
}

func (m *Uinfo) GetNewCommentCount() int32 {
	if m != nil {
		return m.NewCommentCount
	}
	return 0
}

func (m *Uinfo) GetNewFollowCount() int32 {
	if m != nil {
		return m.NewFollowCount
	}
	return 0
}

func (m *Uinfo) GetUnreadSequence() uint32 {
	if m != nil {
		return m.UnreadSequence
	}
	return 0
}

func (m *Uinfo) GetFollowingStreamUpdateInfo() *FollowingStreamUpdateInfo {
	if m != nil {
		return m.FollowingStreamUpdateInfo
	}
	return nil
}

func (m *Uinfo) GetNewAtCount() int32 {
	if m != nil {
		return m.NewAtCount
	}
	return 0
}

func (m *Uinfo) GetNewConcernCount() int32 {
	if m != nil {
		return m.NewConcernCount
	}
	return 0
}

func (m *Uinfo) GetLastReadAttitudeId() string {
	if m != nil {
		return m.LastReadAttitudeId
	}
	return ""
}

func (m *Uinfo) GetLastReadAttitudeTime() int64 {
	if m != nil {
		return m.LastReadAttitudeTime
	}
	return 0
}

func (m *Uinfo) GetLastReadCommentId() string {
	if m != nil {
		return m.LastReadCommentId
	}
	return ""
}

func (m *Uinfo) GetLastReadCommentTime() int64 {
	if m != nil {
		return m.LastReadCommentTime
	}
	return 0
}

func (m *Uinfo) GetLastReadFollowId() string {
	if m != nil {
		return m.LastReadFollowId
	}
	return ""
}

func (m *Uinfo) GetLastReadFollowTime() int64 {
	if m != nil {
		return m.LastReadFollowTime
	}
	return 0
}

func (m *Uinfo) GetLastReadAtId() string {
	if m != nil {
		return m.LastReadAtId
	}
	return ""
}

func (m *Uinfo) GetLastReadAtTime() int64 {
	if m != nil {
		return m.LastReadAtTime
	}
	return 0
}

func (m *Uinfo) GetLastReadConcernId() string {
	if m != nil {
		return m.LastReadConcernId
	}
	return ""
}

func (m *Uinfo) GetLastReadConcernTime() int64 {
	if m != nil {
		return m.LastReadConcernTime
	}
	return 0
}

type MarkReadReq struct {
	UserId               uint32    `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Id                   string    `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	Types                []MsgType `protobuf:"varint,3,rep,packed,name=types,proto3,enum=ugc.interactive.MsgType" json:"types,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *MarkReadReq) Reset()         { *m = MarkReadReq{} }
func (m *MarkReadReq) String() string { return proto.CompactTextString(m) }
func (*MarkReadReq) ProtoMessage()    {}
func (*MarkReadReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_interactive_4b873c933a5dec8e, []int{7}
}
func (m *MarkReadReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkReadReq.Unmarshal(m, b)
}
func (m *MarkReadReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkReadReq.Marshal(b, m, deterministic)
}
func (dst *MarkReadReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkReadReq.Merge(dst, src)
}
func (m *MarkReadReq) XXX_Size() int {
	return xxx_messageInfo_MarkReadReq.Size(m)
}
func (m *MarkReadReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkReadReq.DiscardUnknown(m)
}

var xxx_messageInfo_MarkReadReq proto.InternalMessageInfo

func (m *MarkReadReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *MarkReadReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *MarkReadReq) GetTypes() []MsgType {
	if m != nil {
		return m.Types
	}
	return nil
}

type MarkReadResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarkReadResp) Reset()         { *m = MarkReadResp{} }
func (m *MarkReadResp) String() string { return proto.CompactTextString(m) }
func (*MarkReadResp) ProtoMessage()    {}
func (*MarkReadResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_interactive_4b873c933a5dec8e, []int{8}
}
func (m *MarkReadResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkReadResp.Unmarshal(m, b)
}
func (m *MarkReadResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkReadResp.Marshal(b, m, deterministic)
}
func (dst *MarkReadResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkReadResp.Merge(dst, src)
}
func (m *MarkReadResp) XXX_Size() int {
	return xxx_messageInfo_MarkReadResp.Size(m)
}
func (m *MarkReadResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkReadResp.DiscardUnknown(m)
}

var xxx_messageInfo_MarkReadResp proto.InternalMessageInfo

type GetUinfoReq struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUinfoReq) Reset()         { *m = GetUinfoReq{} }
func (m *GetUinfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUinfoReq) ProtoMessage()    {}
func (*GetUinfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_interactive_4b873c933a5dec8e, []int{9}
}
func (m *GetUinfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUinfoReq.Unmarshal(m, b)
}
func (m *GetUinfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUinfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUinfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUinfoReq.Merge(dst, src)
}
func (m *GetUinfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUinfoReq.Size(m)
}
func (m *GetUinfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUinfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUinfoReq proto.InternalMessageInfo

func (m *GetUinfoReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

type GetUinfoResp struct {
	Uinfo                *Uinfo   `protobuf:"bytes,1,opt,name=uinfo,proto3" json:"uinfo,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUinfoResp) Reset()         { *m = GetUinfoResp{} }
func (m *GetUinfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUinfoResp) ProtoMessage()    {}
func (*GetUinfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_interactive_4b873c933a5dec8e, []int{10}
}
func (m *GetUinfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUinfoResp.Unmarshal(m, b)
}
func (m *GetUinfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUinfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUinfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUinfoResp.Merge(dst, src)
}
func (m *GetUinfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUinfoResp.Size(m)
}
func (m *GetUinfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUinfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUinfoResp proto.InternalMessageInfo

func (m *GetUinfoResp) GetUinfo() *Uinfo {
	if m != nil {
		return m.Uinfo
	}
	return nil
}

type ClearNewFollowCountReq struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearNewFollowCountReq) Reset()         { *m = ClearNewFollowCountReq{} }
func (m *ClearNewFollowCountReq) String() string { return proto.CompactTextString(m) }
func (*ClearNewFollowCountReq) ProtoMessage()    {}
func (*ClearNewFollowCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_interactive_4b873c933a5dec8e, []int{11}
}
func (m *ClearNewFollowCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearNewFollowCountReq.Unmarshal(m, b)
}
func (m *ClearNewFollowCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearNewFollowCountReq.Marshal(b, m, deterministic)
}
func (dst *ClearNewFollowCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearNewFollowCountReq.Merge(dst, src)
}
func (m *ClearNewFollowCountReq) XXX_Size() int {
	return xxx_messageInfo_ClearNewFollowCountReq.Size(m)
}
func (m *ClearNewFollowCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearNewFollowCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_ClearNewFollowCountReq proto.InternalMessageInfo

func (m *ClearNewFollowCountReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

type ClearNewFollowCountResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearNewFollowCountResp) Reset()         { *m = ClearNewFollowCountResp{} }
func (m *ClearNewFollowCountResp) String() string { return proto.CompactTextString(m) }
func (*ClearNewFollowCountResp) ProtoMessage()    {}
func (*ClearNewFollowCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_interactive_4b873c933a5dec8e, []int{12}
}
func (m *ClearNewFollowCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearNewFollowCountResp.Unmarshal(m, b)
}
func (m *ClearNewFollowCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearNewFollowCountResp.Marshal(b, m, deterministic)
}
func (dst *ClearNewFollowCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearNewFollowCountResp.Merge(dst, src)
}
func (m *ClearNewFollowCountResp) XXX_Size() int {
	return xxx_messageInfo_ClearNewFollowCountResp.Size(m)
}
func (m *ClearNewFollowCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearNewFollowCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_ClearNewFollowCountResp proto.InternalMessageInfo

type AddFollowCountReq struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Count                uint32   `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddFollowCountReq) Reset()         { *m = AddFollowCountReq{} }
func (m *AddFollowCountReq) String() string { return proto.CompactTextString(m) }
func (*AddFollowCountReq) ProtoMessage()    {}
func (*AddFollowCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_interactive_4b873c933a5dec8e, []int{13}
}
func (m *AddFollowCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddFollowCountReq.Unmarshal(m, b)
}
func (m *AddFollowCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddFollowCountReq.Marshal(b, m, deterministic)
}
func (dst *AddFollowCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddFollowCountReq.Merge(dst, src)
}
func (m *AddFollowCountReq) XXX_Size() int {
	return xxx_messageInfo_AddFollowCountReq.Size(m)
}
func (m *AddFollowCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddFollowCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddFollowCountReq proto.InternalMessageInfo

func (m *AddFollowCountReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *AddFollowCountReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type AddFollowCountResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddFollowCountResp) Reset()         { *m = AddFollowCountResp{} }
func (m *AddFollowCountResp) String() string { return proto.CompactTextString(m) }
func (*AddFollowCountResp) ProtoMessage()    {}
func (*AddFollowCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_interactive_4b873c933a5dec8e, []int{14}
}
func (m *AddFollowCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddFollowCountResp.Unmarshal(m, b)
}
func (m *AddFollowCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddFollowCountResp.Marshal(b, m, deterministic)
}
func (dst *AddFollowCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddFollowCountResp.Merge(dst, src)
}
func (m *AddFollowCountResp) XXX_Size() int {
	return xxx_messageInfo_AddFollowCountResp.Size(m)
}
func (m *AddFollowCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddFollowCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddFollowCountResp proto.InternalMessageInfo

type UpdateFollowingStreamReq struct {
	UidList              []uint32                   `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	UpdateInfo           *FollowingStreamUpdateInfo `protobuf:"bytes,2,opt,name=update_info,json=updateInfo,proto3" json:"update_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *UpdateFollowingStreamReq) Reset()         { *m = UpdateFollowingStreamReq{} }
func (m *UpdateFollowingStreamReq) String() string { return proto.CompactTextString(m) }
func (*UpdateFollowingStreamReq) ProtoMessage()    {}
func (*UpdateFollowingStreamReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_interactive_4b873c933a5dec8e, []int{15}
}
func (m *UpdateFollowingStreamReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateFollowingStreamReq.Unmarshal(m, b)
}
func (m *UpdateFollowingStreamReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateFollowingStreamReq.Marshal(b, m, deterministic)
}
func (dst *UpdateFollowingStreamReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateFollowingStreamReq.Merge(dst, src)
}
func (m *UpdateFollowingStreamReq) XXX_Size() int {
	return xxx_messageInfo_UpdateFollowingStreamReq.Size(m)
}
func (m *UpdateFollowingStreamReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateFollowingStreamReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateFollowingStreamReq proto.InternalMessageInfo

func (m *UpdateFollowingStreamReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *UpdateFollowingStreamReq) GetUpdateInfo() *FollowingStreamUpdateInfo {
	if m != nil {
		return m.UpdateInfo
	}
	return nil
}

type UpdateFollowingStreamResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateFollowingStreamResp) Reset()         { *m = UpdateFollowingStreamResp{} }
func (m *UpdateFollowingStreamResp) String() string { return proto.CompactTextString(m) }
func (*UpdateFollowingStreamResp) ProtoMessage()    {}
func (*UpdateFollowingStreamResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_interactive_4b873c933a5dec8e, []int{16}
}
func (m *UpdateFollowingStreamResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateFollowingStreamResp.Unmarshal(m, b)
}
func (m *UpdateFollowingStreamResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateFollowingStreamResp.Marshal(b, m, deterministic)
}
func (dst *UpdateFollowingStreamResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateFollowingStreamResp.Merge(dst, src)
}
func (m *UpdateFollowingStreamResp) XXX_Size() int {
	return xxx_messageInfo_UpdateFollowingStreamResp.Size(m)
}
func (m *UpdateFollowingStreamResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateFollowingStreamResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateFollowingStreamResp proto.InternalMessageInfo

type AddUnreadCountsReq struct {
	UserId uint32 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	//
	// count更新逻辑：
	// 1. > 0: 增加
	// 2. < 0: 清0
	// 3. = 0: 不变
	NewCommentCount      int32    `protobuf:"varint,2,opt,name=new_comment_count,json=newCommentCount,proto3" json:"new_comment_count,omitempty"`
	NewAttitudeCount     int32    `protobuf:"varint,3,opt,name=new_attitude_count,json=newAttitudeCount,proto3" json:"new_attitude_count,omitempty"`
	NewFollowerCount     int32    `protobuf:"varint,4,opt,name=new_follower_count,json=newFollowerCount,proto3" json:"new_follower_count,omitempty"`
	NewAtCount           int32    `protobuf:"varint,5,opt,name=new_at_count,json=newAtCount,proto3" json:"new_at_count,omitempty"`
	NewConcernCount      int32    `protobuf:"varint,6,opt,name=new_concern_count,json=newConcernCount,proto3" json:"new_concern_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUnreadCountsReq) Reset()         { *m = AddUnreadCountsReq{} }
func (m *AddUnreadCountsReq) String() string { return proto.CompactTextString(m) }
func (*AddUnreadCountsReq) ProtoMessage()    {}
func (*AddUnreadCountsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_interactive_4b873c933a5dec8e, []int{17}
}
func (m *AddUnreadCountsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUnreadCountsReq.Unmarshal(m, b)
}
func (m *AddUnreadCountsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUnreadCountsReq.Marshal(b, m, deterministic)
}
func (dst *AddUnreadCountsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUnreadCountsReq.Merge(dst, src)
}
func (m *AddUnreadCountsReq) XXX_Size() int {
	return xxx_messageInfo_AddUnreadCountsReq.Size(m)
}
func (m *AddUnreadCountsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUnreadCountsReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddUnreadCountsReq proto.InternalMessageInfo

func (m *AddUnreadCountsReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *AddUnreadCountsReq) GetNewCommentCount() int32 {
	if m != nil {
		return m.NewCommentCount
	}
	return 0
}

func (m *AddUnreadCountsReq) GetNewAttitudeCount() int32 {
	if m != nil {
		return m.NewAttitudeCount
	}
	return 0
}

func (m *AddUnreadCountsReq) GetNewFollowerCount() int32 {
	if m != nil {
		return m.NewFollowerCount
	}
	return 0
}

func (m *AddUnreadCountsReq) GetNewAtCount() int32 {
	if m != nil {
		return m.NewAtCount
	}
	return 0
}

func (m *AddUnreadCountsReq) GetNewConcernCount() int32 {
	if m != nil {
		return m.NewConcernCount
	}
	return 0
}

type AddUnreadCountsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUnreadCountsResp) Reset()         { *m = AddUnreadCountsResp{} }
func (m *AddUnreadCountsResp) String() string { return proto.CompactTextString(m) }
func (*AddUnreadCountsResp) ProtoMessage()    {}
func (*AddUnreadCountsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_interactive_4b873c933a5dec8e, []int{18}
}
func (m *AddUnreadCountsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUnreadCountsResp.Unmarshal(m, b)
}
func (m *AddUnreadCountsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUnreadCountsResp.Marshal(b, m, deterministic)
}
func (dst *AddUnreadCountsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUnreadCountsResp.Merge(dst, src)
}
func (m *AddUnreadCountsResp) XXX_Size() int {
	return xxx_messageInfo_AddUnreadCountsResp.Size(m)
}
func (m *AddUnreadCountsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUnreadCountsResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddUnreadCountsResp proto.InternalMessageInfo

type AddNonPublicUnreadCountsReq struct {
	UserId uint32 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	//
	// count更新逻辑：
	// 1. > 0: 增加
	// 2. < 0: 清0
	// 3. = 0: 不变
	NewCommentCount      int32    `protobuf:"varint,2,opt,name=new_comment_count,json=newCommentCount,proto3" json:"new_comment_count,omitempty"`
	NewAttitudeCount     int32    `protobuf:"varint,3,opt,name=new_attitude_count,json=newAttitudeCount,proto3" json:"new_attitude_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddNonPublicUnreadCountsReq) Reset()         { *m = AddNonPublicUnreadCountsReq{} }
func (m *AddNonPublicUnreadCountsReq) String() string { return proto.CompactTextString(m) }
func (*AddNonPublicUnreadCountsReq) ProtoMessage()    {}
func (*AddNonPublicUnreadCountsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_interactive_4b873c933a5dec8e, []int{19}
}
func (m *AddNonPublicUnreadCountsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddNonPublicUnreadCountsReq.Unmarshal(m, b)
}
func (m *AddNonPublicUnreadCountsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddNonPublicUnreadCountsReq.Marshal(b, m, deterministic)
}
func (dst *AddNonPublicUnreadCountsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddNonPublicUnreadCountsReq.Merge(dst, src)
}
func (m *AddNonPublicUnreadCountsReq) XXX_Size() int {
	return xxx_messageInfo_AddNonPublicUnreadCountsReq.Size(m)
}
func (m *AddNonPublicUnreadCountsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddNonPublicUnreadCountsReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddNonPublicUnreadCountsReq proto.InternalMessageInfo

func (m *AddNonPublicUnreadCountsReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *AddNonPublicUnreadCountsReq) GetNewCommentCount() int32 {
	if m != nil {
		return m.NewCommentCount
	}
	return 0
}

func (m *AddNonPublicUnreadCountsReq) GetNewAttitudeCount() int32 {
	if m != nil {
		return m.NewAttitudeCount
	}
	return 0
}

type AddNonPublicUnreadCountsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddNonPublicUnreadCountsResp) Reset()         { *m = AddNonPublicUnreadCountsResp{} }
func (m *AddNonPublicUnreadCountsResp) String() string { return proto.CompactTextString(m) }
func (*AddNonPublicUnreadCountsResp) ProtoMessage()    {}
func (*AddNonPublicUnreadCountsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_interactive_4b873c933a5dec8e, []int{20}
}
func (m *AddNonPublicUnreadCountsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddNonPublicUnreadCountsResp.Unmarshal(m, b)
}
func (m *AddNonPublicUnreadCountsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddNonPublicUnreadCountsResp.Marshal(b, m, deterministic)
}
func (dst *AddNonPublicUnreadCountsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddNonPublicUnreadCountsResp.Merge(dst, src)
}
func (m *AddNonPublicUnreadCountsResp) XXX_Size() int {
	return xxx_messageInfo_AddNonPublicUnreadCountsResp.Size(m)
}
func (m *AddNonPublicUnreadCountsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddNonPublicUnreadCountsResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddNonPublicUnreadCountsResp proto.InternalMessageInfo

type UpsertNonPublicHotCommentReq struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CommentId            string   `protobuf:"bytes,2,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	Score                int64    `protobuf:"varint,3,opt,name=score,proto3" json:"score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertNonPublicHotCommentReq) Reset()         { *m = UpsertNonPublicHotCommentReq{} }
func (m *UpsertNonPublicHotCommentReq) String() string { return proto.CompactTextString(m) }
func (*UpsertNonPublicHotCommentReq) ProtoMessage()    {}
func (*UpsertNonPublicHotCommentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_interactive_4b873c933a5dec8e, []int{21}
}
func (m *UpsertNonPublicHotCommentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertNonPublicHotCommentReq.Unmarshal(m, b)
}
func (m *UpsertNonPublicHotCommentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertNonPublicHotCommentReq.Marshal(b, m, deterministic)
}
func (dst *UpsertNonPublicHotCommentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertNonPublicHotCommentReq.Merge(dst, src)
}
func (m *UpsertNonPublicHotCommentReq) XXX_Size() int {
	return xxx_messageInfo_UpsertNonPublicHotCommentReq.Size(m)
}
func (m *UpsertNonPublicHotCommentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertNonPublicHotCommentReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertNonPublicHotCommentReq proto.InternalMessageInfo

func (m *UpsertNonPublicHotCommentReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *UpsertNonPublicHotCommentReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *UpsertNonPublicHotCommentReq) GetScore() int64 {
	if m != nil {
		return m.Score
	}
	return 0
}

type UpsertNonPublicHotCommentResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertNonPublicHotCommentResp) Reset()         { *m = UpsertNonPublicHotCommentResp{} }
func (m *UpsertNonPublicHotCommentResp) String() string { return proto.CompactTextString(m) }
func (*UpsertNonPublicHotCommentResp) ProtoMessage()    {}
func (*UpsertNonPublicHotCommentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_interactive_4b873c933a5dec8e, []int{22}
}
func (m *UpsertNonPublicHotCommentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertNonPublicHotCommentResp.Unmarshal(m, b)
}
func (m *UpsertNonPublicHotCommentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertNonPublicHotCommentResp.Marshal(b, m, deterministic)
}
func (dst *UpsertNonPublicHotCommentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertNonPublicHotCommentResp.Merge(dst, src)
}
func (m *UpsertNonPublicHotCommentResp) XXX_Size() int {
	return xxx_messageInfo_UpsertNonPublicHotCommentResp.Size(m)
}
func (m *UpsertNonPublicHotCommentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertNonPublicHotCommentResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertNonPublicHotCommentResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*CommentMsg)(nil), "ugc.interactive.CommentMsg")
	proto.RegisterType((*AttitudeMsg)(nil), "ugc.interactive.AttitudeMsg")
	proto.RegisterType((*AtMsg)(nil), "ugc.interactive.AtMsg")
	proto.RegisterType((*ConcernMsg)(nil), "ugc.interactive.ConcernMsg")
	proto.RegisterType((*InteractiveMsg)(nil), "ugc.interactive.InteractiveMsg")
	proto.RegisterType((*FollowingStreamUpdateInfo)(nil), "ugc.interactive.FollowingStreamUpdateInfo")
	proto.RegisterType((*Uinfo)(nil), "ugc.interactive.Uinfo")
	proto.RegisterType((*MarkReadReq)(nil), "ugc.interactive.MarkReadReq")
	proto.RegisterType((*MarkReadResp)(nil), "ugc.interactive.MarkReadResp")
	proto.RegisterType((*GetUinfoReq)(nil), "ugc.interactive.GetUinfoReq")
	proto.RegisterType((*GetUinfoResp)(nil), "ugc.interactive.GetUinfoResp")
	proto.RegisterType((*ClearNewFollowCountReq)(nil), "ugc.interactive.ClearNewFollowCountReq")
	proto.RegisterType((*ClearNewFollowCountResp)(nil), "ugc.interactive.ClearNewFollowCountResp")
	proto.RegisterType((*AddFollowCountReq)(nil), "ugc.interactive.AddFollowCountReq")
	proto.RegisterType((*AddFollowCountResp)(nil), "ugc.interactive.AddFollowCountResp")
	proto.RegisterType((*UpdateFollowingStreamReq)(nil), "ugc.interactive.UpdateFollowingStreamReq")
	proto.RegisterType((*UpdateFollowingStreamResp)(nil), "ugc.interactive.UpdateFollowingStreamResp")
	proto.RegisterType((*AddUnreadCountsReq)(nil), "ugc.interactive.AddUnreadCountsReq")
	proto.RegisterType((*AddUnreadCountsResp)(nil), "ugc.interactive.AddUnreadCountsResp")
	proto.RegisterType((*AddNonPublicUnreadCountsReq)(nil), "ugc.interactive.AddNonPublicUnreadCountsReq")
	proto.RegisterType((*AddNonPublicUnreadCountsResp)(nil), "ugc.interactive.AddNonPublicUnreadCountsResp")
	proto.RegisterType((*UpsertNonPublicHotCommentReq)(nil), "ugc.interactive.UpsertNonPublicHotCommentReq")
	proto.RegisterType((*UpsertNonPublicHotCommentResp)(nil), "ugc.interactive.UpsertNonPublicHotCommentResp")
	proto.RegisterEnum("ugc.interactive.MsgType", MsgType_name, MsgType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// InteractiveClient is the client API for Interactive service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type InteractiveClient interface {
	MarkRead(ctx context.Context, in *MarkReadReq, opts ...grpc.CallOption) (*MarkReadResp, error)
	GetInteractiveUinfo(ctx context.Context, in *GetUinfoReq, opts ...grpc.CallOption) (*GetUinfoResp, error)
	ClearNewFollowCount(ctx context.Context, in *ClearNewFollowCountReq, opts ...grpc.CallOption) (*ClearNewFollowCountResp, error)
	AddFollowCount(ctx context.Context, in *AddFollowCountReq, opts ...grpc.CallOption) (*AddFollowCountResp, error)
	AddUnreadCounts(ctx context.Context, in *AddUnreadCountsReq, opts ...grpc.CallOption) (*AddUnreadCountsResp, error)
	UpdateFollowingStream(ctx context.Context, in *UpdateFollowingStreamReq, opts ...grpc.CallOption) (*UpdateFollowingStreamResp, error)
	AddNonPublicUnreadCounts(ctx context.Context, in *AddNonPublicUnreadCountsReq, opts ...grpc.CallOption) (*AddNonPublicUnreadCountsResp, error)
	UpsertNonPublicHotComment(ctx context.Context, in *UpsertNonPublicHotCommentReq, opts ...grpc.CallOption) (*UpsertNonPublicHotCommentResp, error)
}

type interactiveClient struct {
	cc *grpc.ClientConn
}

func NewInteractiveClient(cc *grpc.ClientConn) InteractiveClient {
	return &interactiveClient{cc}
}

func (c *interactiveClient) MarkRead(ctx context.Context, in *MarkReadReq, opts ...grpc.CallOption) (*MarkReadResp, error) {
	out := new(MarkReadResp)
	err := c.cc.Invoke(ctx, "/ugc.interactive.Interactive/MarkRead", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interactiveClient) GetInteractiveUinfo(ctx context.Context, in *GetUinfoReq, opts ...grpc.CallOption) (*GetUinfoResp, error) {
	out := new(GetUinfoResp)
	err := c.cc.Invoke(ctx, "/ugc.interactive.Interactive/GetInteractiveUinfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interactiveClient) ClearNewFollowCount(ctx context.Context, in *ClearNewFollowCountReq, opts ...grpc.CallOption) (*ClearNewFollowCountResp, error) {
	out := new(ClearNewFollowCountResp)
	err := c.cc.Invoke(ctx, "/ugc.interactive.Interactive/ClearNewFollowCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interactiveClient) AddFollowCount(ctx context.Context, in *AddFollowCountReq, opts ...grpc.CallOption) (*AddFollowCountResp, error) {
	out := new(AddFollowCountResp)
	err := c.cc.Invoke(ctx, "/ugc.interactive.Interactive/AddFollowCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interactiveClient) AddUnreadCounts(ctx context.Context, in *AddUnreadCountsReq, opts ...grpc.CallOption) (*AddUnreadCountsResp, error) {
	out := new(AddUnreadCountsResp)
	err := c.cc.Invoke(ctx, "/ugc.interactive.Interactive/AddUnreadCounts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interactiveClient) UpdateFollowingStream(ctx context.Context, in *UpdateFollowingStreamReq, opts ...grpc.CallOption) (*UpdateFollowingStreamResp, error) {
	out := new(UpdateFollowingStreamResp)
	err := c.cc.Invoke(ctx, "/ugc.interactive.Interactive/UpdateFollowingStream", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interactiveClient) AddNonPublicUnreadCounts(ctx context.Context, in *AddNonPublicUnreadCountsReq, opts ...grpc.CallOption) (*AddNonPublicUnreadCountsResp, error) {
	out := new(AddNonPublicUnreadCountsResp)
	err := c.cc.Invoke(ctx, "/ugc.interactive.Interactive/AddNonPublicUnreadCounts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *interactiveClient) UpsertNonPublicHotComment(ctx context.Context, in *UpsertNonPublicHotCommentReq, opts ...grpc.CallOption) (*UpsertNonPublicHotCommentResp, error) {
	out := new(UpsertNonPublicHotCommentResp)
	err := c.cc.Invoke(ctx, "/ugc.interactive.Interactive/UpsertNonPublicHotComment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// InteractiveServer is the server API for Interactive service.
type InteractiveServer interface {
	MarkRead(context.Context, *MarkReadReq) (*MarkReadResp, error)
	GetInteractiveUinfo(context.Context, *GetUinfoReq) (*GetUinfoResp, error)
	ClearNewFollowCount(context.Context, *ClearNewFollowCountReq) (*ClearNewFollowCountResp, error)
	AddFollowCount(context.Context, *AddFollowCountReq) (*AddFollowCountResp, error)
	AddUnreadCounts(context.Context, *AddUnreadCountsReq) (*AddUnreadCountsResp, error)
	UpdateFollowingStream(context.Context, *UpdateFollowingStreamReq) (*UpdateFollowingStreamResp, error)
	AddNonPublicUnreadCounts(context.Context, *AddNonPublicUnreadCountsReq) (*AddNonPublicUnreadCountsResp, error)
	UpsertNonPublicHotComment(context.Context, *UpsertNonPublicHotCommentReq) (*UpsertNonPublicHotCommentResp, error)
}

func RegisterInteractiveServer(s *grpc.Server, srv InteractiveServer) {
	s.RegisterService(&_Interactive_serviceDesc, srv)
}

func _Interactive_MarkRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkReadReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InteractiveServer).MarkRead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.interactive.Interactive/MarkRead",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InteractiveServer).MarkRead(ctx, req.(*MarkReadReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Interactive_GetInteractiveUinfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUinfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InteractiveServer).GetInteractiveUinfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.interactive.Interactive/GetInteractiveUinfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InteractiveServer).GetInteractiveUinfo(ctx, req.(*GetUinfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Interactive_ClearNewFollowCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearNewFollowCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InteractiveServer).ClearNewFollowCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.interactive.Interactive/ClearNewFollowCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InteractiveServer).ClearNewFollowCount(ctx, req.(*ClearNewFollowCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Interactive_AddFollowCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddFollowCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InteractiveServer).AddFollowCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.interactive.Interactive/AddFollowCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InteractiveServer).AddFollowCount(ctx, req.(*AddFollowCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Interactive_AddUnreadCounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUnreadCountsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InteractiveServer).AddUnreadCounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.interactive.Interactive/AddUnreadCounts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InteractiveServer).AddUnreadCounts(ctx, req.(*AddUnreadCountsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Interactive_UpdateFollowingStream_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateFollowingStreamReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InteractiveServer).UpdateFollowingStream(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.interactive.Interactive/UpdateFollowingStream",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InteractiveServer).UpdateFollowingStream(ctx, req.(*UpdateFollowingStreamReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Interactive_AddNonPublicUnreadCounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddNonPublicUnreadCountsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InteractiveServer).AddNonPublicUnreadCounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.interactive.Interactive/AddNonPublicUnreadCounts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InteractiveServer).AddNonPublicUnreadCounts(ctx, req.(*AddNonPublicUnreadCountsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Interactive_UpsertNonPublicHotComment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertNonPublicHotCommentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InteractiveServer).UpsertNonPublicHotComment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.interactive.Interactive/UpsertNonPublicHotComment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InteractiveServer).UpsertNonPublicHotComment(ctx, req.(*UpsertNonPublicHotCommentReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Interactive_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ugc.interactive.Interactive",
	HandlerType: (*InteractiveServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "MarkRead",
			Handler:    _Interactive_MarkRead_Handler,
		},
		{
			MethodName: "GetInteractiveUinfo",
			Handler:    _Interactive_GetInteractiveUinfo_Handler,
		},
		{
			MethodName: "ClearNewFollowCount",
			Handler:    _Interactive_ClearNewFollowCount_Handler,
		},
		{
			MethodName: "AddFollowCount",
			Handler:    _Interactive_AddFollowCount_Handler,
		},
		{
			MethodName: "AddUnreadCounts",
			Handler:    _Interactive_AddUnreadCounts_Handler,
		},
		{
			MethodName: "UpdateFollowingStream",
			Handler:    _Interactive_UpdateFollowingStream_Handler,
		},
		{
			MethodName: "AddNonPublicUnreadCounts",
			Handler:    _Interactive_AddNonPublicUnreadCounts_Handler,
		},
		{
			MethodName: "UpsertNonPublicHotComment",
			Handler:    _Interactive_UpsertNonPublicHotComment_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ugc/interactive.proto",
}

func init() { proto.RegisterFile("ugc/interactive.proto", fileDescriptor_interactive_4b873c933a5dec8e) }

var fileDescriptor_interactive_4b873c933a5dec8e = []byte{
	// 1272 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x57, 0x4d, 0x6f, 0xdb, 0x46,
	0x10, 0xad, 0x24, 0x4b, 0xb6, 0x47, 0xb2, 0x24, 0xaf, 0xed, 0x44, 0x96, 0x9d, 0x56, 0x60, 0xd2,
	0xc6, 0x31, 0x62, 0x19, 0x71, 0x90, 0x5b, 0x80, 0x42, 0x71, 0x9d, 0x54, 0x68, 0x24, 0x17, 0x8c,
	0x84, 0x20, 0x45, 0x0b, 0x82, 0x26, 0x57, 0x2a, 0x1b, 0x89, 0x64, 0xb8, 0xcb, 0x08, 0x29, 0xd0,
	0x4b, 0xdb, 0x4b, 0x6f, 0x3d, 0xf4, 0x97, 0xf4, 0x17, 0x16, 0x3b, 0x4b, 0x4a, 0x14, 0x3f, 0x14,
	0xfb, 0x10, 0xf4, 0x66, 0xce, 0xbe, 0xb7, 0x33, 0xfb, 0x66, 0xe7, 0x79, 0x05, 0x7b, 0xfe, 0xd8,
	0x38, 0xb5, 0x6c, 0x4e, 0x3d, 0xdd, 0xe0, 0xd6, 0x7b, 0xda, 0x76, 0x3d, 0x87, 0x3b, 0xa4, 0xe6,
	0x8f, 0x8d, 0x76, 0x24, 0xac, 0xfc, 0x06, 0x70, 0xee, 0x4c, 0xa7, 0xd4, 0xe6, 0x3d, 0x36, 0x26,
	0xf7, 0xa0, 0xea, 0x3a, 0x8c, 0x6b, 0xce, 0xd5, 0x2f, 0xd4, 0xe0, 0x9a, 0x65, 0x36, 0x72, 0xad,
	0xdc, 0xd1, 0xa6, 0x5a, 0x11, 0xd1, 0x4b, 0x0c, 0x76, 0x4d, 0x72, 0x0c, 0xdb, 0x86, 0xe4, 0x44,
	0x80, 0x79, 0x04, 0xd6, 0x82, 0x85, 0x39, 0xf6, 0x0e, 0x40, 0x88, 0xb5, 0xcc, 0x46, 0x01, 0x41,
	0x9b, 0x41, 0xa4, 0x6b, 0x2a, 0x7f, 0xe6, 0xa0, 0xdc, 0xe1, 0xdc, 0xe2, 0xbe, 0x49, 0x3f, 0x4d,
	0x01, 0x77, 0x61, 0x4b, 0x0f, 0x12, 0x68, 0xfc, 0x83, 0x4b, 0xb1, 0x86, 0xa2, 0x5a, 0x09, 0x83,
	0x83, 0x0f, 0x2e, 0x55, 0xde, 0x40, 0xb1, 0xf3, 0x69, 0x04, 0x50, 0x2a, 0x42, 0x60, 0xdb, 0xa0,
	0x9e, 0xdd, 0x63, 0x63, 0xe5, 0xaf, 0x02, 0x54, 0xbb, 0x0b, 0xf9, 0x45, 0xca, 0x2a, 0xe4, 0xe7,
	0x69, 0xf2, 0x96, 0x49, 0x08, 0xac, 0x71, 0x6b, 0x4a, 0x71, 0xbf, 0x82, 0x8a, 0x7f, 0x93, 0xdb,
	0xb0, 0xee, 0x33, 0xea, 0x85, 0x12, 0x6e, 0xa9, 0x25, 0xf1, 0xd9, 0x35, 0x49, 0x0b, 0x2a, 0x23,
	0xcf, 0x99, 0x6a, 0xe1, 0xea, 0x1a, 0xae, 0x82, 0x88, 0x0d, 0x25, 0xe2, 0x21, 0xac, 0xe1, 0xb1,
	0x8b, 0xad, 0xdc, 0x51, 0xf5, 0xac, 0xd1, 0x8e, 0x5d, 0x80, 0x76, 0x8f, 0x8d, 0x85, 0x04, 0x2a,
	0xa2, 0xc8, 0x53, 0x28, 0x87, 0x27, 0x9b, 0xb2, 0x71, 0xa3, 0xd4, 0xca, 0x1d, 0x95, 0xcf, 0x0e,
	0x12, 0xa4, 0xc5, 0x95, 0x51, 0xc3, 0xf6, 0x8a, 0xa3, 0x7c, 0x0d, 0x73, 0x59, 0x91, 0xbe, 0x8e,
	0xf4, 0xc3, 0x04, 0x3d, 0xd2, 0x71, 0xb5, 0xac, 0x47, 0xda, 0x7f, 0x02, 0x25, 0x5d, 0x66, 0xde,
	0x40, 0xea, 0xad, 0x14, 0xaa, 0x20, 0x15, 0x75, 0xcc, 0x87, 0xd5, 0xa2, 0xb6, 0xc8, 0xd9, 0xcc,
	0xac, 0x36, 0xd4, 0x5f, 0x54, 0x3b, 0xef, 0xc5, 0x4f, 0xb0, 0xff, 0xdc, 0x99, 0x4c, 0x9c, 0x99,
	0x65, 0x8f, 0x5f, 0x71, 0x8f, 0xea, 0xd3, 0xa1, 0x6b, 0xea, 0x9c, 0x76, 0xed, 0x91, 0x43, 0x0e,
	0x60, 0xd3, 0xc7, 0x2f, 0x4d, 0xe7, 0xd8, 0x9c, 0x35, 0x75, 0x43, 0x06, 0x3a, 0x5c, 0xdc, 0xa9,
	0x89, 0xce, 0x29, 0xe3, 0x9a, 0x6e, 0x70, 0xc7, 0x63, 0x8d, 0x7c, 0xab, 0x70, 0xb4, 0xa5, 0x56,
	0x64, 0xb0, 0x83, 0x31, 0xe5, 0x9f, 0x75, 0x28, 0x0e, 0x2d, 0xb1, 0x57, 0xa4, 0x7b, 0xb9, 0x78,
	0xf7, 0x26, 0x3a, 0xe3, 0x9a, 0x47, 0x75, 0x73, 0x71, 0x85, 0x40, 0xc4, 0x54, 0xaa, 0x9b, 0xd8,
	0x3d, 0x62, 0xd3, 0x99, 0x36, 0x57, 0xd5, 0x70, 0x7c, 0x9b, 0x07, 0x57, 0xb8, 0x6e, 0xd3, 0x59,
	0xa8, 0xe4, 0xb9, 0x88, 0x8b, 0x7b, 0x29, 0xd0, 0x61, 0x07, 0x25, 0x78, 0x0d, 0xc1, 0x35, 0x9b,
	0xce, 0x82, 0xae, 0x49, 0xec, 0x11, 0x08, 0xbe, 0x36, 0x42, 0x05, 0x02, 0x68, 0x11, 0xa1, 0x55,
	0x9b, 0xce, 0xa4, 0x30, 0x12, 0x79, 0x1f, 0x6a, 0xbe, 0x8d, 0x25, 0x32, 0xfa, 0xce, 0xa7, 0xb6,
	0x41, 0xf1, 0x5e, 0x6c, 0xa9, 0x55, 0x19, 0x7e, 0x15, 0x44, 0xc9, 0x5b, 0x38, 0x1c, 0x85, 0x82,
	0x6a, 0x0c, 0x15, 0xd5, 0x02, 0x11, 0x85, 0x0e, 0xc1, 0x75, 0x38, 0x4e, 0xf4, 0x27, 0xb3, 0x0b,
	0xea, 0xfe, 0x28, 0xb3, 0x41, 0x2d, 0xa8, 0x48, 0x65, 0x82, 0xda, 0x37, 0xb0, 0x76, 0x40, 0x4d,
	0x62, 0x6a, 0xc8, 0x1b, 0x22, 0x61, 0x9b, 0x11, 0x35, 0x30, 0x2e, 0xb1, 0x8f, 0x60, 0x6f, 0xd1,
	0x89, 0xb9, 0xda, 0x96, 0xd9, 0x28, 0x63, 0x4b, 0x48, 0xd8, 0x92, 0x50, 0xef, 0xae, 0x49, 0x9e,
	0xc0, 0xed, 0x14, 0x0a, 0x8e, 0x6e, 0x05, 0x47, 0x77, 0x37, 0x4e, 0x1a, 0x88, 0x51, 0x3e, 0x85,
	0xdd, 0x05, 0x2d, 0x62, 0x8d, 0x5b, 0x98, 0x68, 0x3b, 0xe4, 0x9c, 0x87, 0x16, 0x49, 0x1e, 0xc3,
	0xad, 0x24, 0x01, 0xd3, 0x54, 0x31, 0xcd, 0x4e, 0x8c, 0x82, 0x59, 0x4e, 0x60, 0x67, 0x41, 0x0a,
	0x7a, 0x6c, 0x99, 0x8d, 0x1a, 0x26, 0xa9, 0x87, 0x0c, 0x29, 0x7c, 0xd7, 0x5c, 0x3e, 0x7e, 0x00,
	0xc7, 0x14, 0x75, 0x4c, 0x41, 0x96, 0x09, 0x98, 0xe1, 0x4b, 0xa8, 0x45, 0x8f, 0x2f, 0x76, 0xdf,
	0x96, 0x56, 0xb9, 0x38, 0x76, 0xd7, 0x24, 0x0f, 0x60, 0x7b, 0x09, 0x86, 0xbb, 0x12, 0xdc, 0xb5,
	0xba, 0x00, 0xa6, 0x29, 0x23, 0xbb, 0x66, 0x99, 0x8d, 0x9d, 0xb8, 0x32, 0xb8, 0x92, 0x54, 0x46,
	0x12, 0x30, 0xc1, 0x6e, 0x5c, 0x19, 0x5c, 0x13, 0x59, 0x94, 0x11, 0x94, 0x7b, 0xba, 0xf7, 0x56,
	0x84, 0x55, 0xfa, 0x2e, 0x7b, 0x36, 0xa5, 0x2d, 0xe7, 0xe7, 0xb6, 0xdc, 0x86, 0xa2, 0x70, 0x48,
	0xd6, 0x28, 0xb4, 0x0a, 0x2b, 0x8d, 0x54, 0xc2, 0x94, 0x2a, 0x54, 0x16, 0x79, 0x98, 0xab, 0x7c,
	0x05, 0xe5, 0x17, 0x94, 0xa3, 0x21, 0xac, 0xca, 0xab, 0x3c, 0x85, 0xca, 0x02, 0xc7, 0x5c, 0xf2,
	0x10, 0x8a, 0x3e, 0x4e, 0x4f, 0x2e, 0xc3, 0x11, 0x25, 0x54, 0x82, 0x94, 0x47, 0x70, 0xeb, 0x7c,
	0x42, 0x75, 0xaf, 0xbf, 0x34, 0xc2, 0x2b, 0x13, 0xee, 0xc3, 0xed, 0x54, 0x0a, 0x73, 0x95, 0x67,
	0xb0, 0xdd, 0x31, 0xcd, 0x6b, 0x6e, 0x44, 0x76, 0xa1, 0x28, 0x67, 0x2c, 0x8f, 0x61, 0xf9, 0xa1,
	0xec, 0x02, 0x89, 0xef, 0xc1, 0x5c, 0xe5, 0xf7, 0x1c, 0x34, 0xe4, 0x30, 0xc7, 0x86, 0x5f, 0x64,
	0xd8, 0x87, 0x0d, 0xdf, 0x32, 0xb5, 0x89, 0xc5, 0x84, 0xf5, 0x0a, 0x67, 0x5d, 0xf7, 0x2d, 0xf3,
	0xa5, 0xc5, 0x38, 0xf9, 0x0e, 0xca, 0x51, 0x47, 0xc9, 0xdf, 0xd8, 0x51, 0xc0, 0x9f, 0xff, 0xad,
	0x1c, 0xc0, 0x7e, 0x46, 0x0d, 0xcc, 0x55, 0xfe, 0xc8, 0x63, 0xe1, 0x43, 0xb4, 0x38, 0x2c, 0x9c,
	0xad, 0x3c, 0x7d, 0xaa, 0xf7, 0xe6, 0xd3, 0xbd, 0xf7, 0x66, 0xae, 0x1e, 0xa0, 0xe5, 0x58, 0x52,
	0x6f, 0xc9, 0xd6, 0xeb, 0x73, 0xaf, 0xa6, 0x9e, 0x44, 0xc7, 0x7d, 0xb1, 0x78, 0x3d, 0x5f, 0x2c,
	0xa5, 0xfa, 0xa2, 0xb2, 0x07, 0x3b, 0x09, 0x11, 0x98, 0xab, 0xfc, 0x9d, 0x83, 0x83, 0x8e, 0x69,
	0xf6, 0x1d, 0xfb, 0x7b, 0xff, 0x6a, 0x62, 0x19, 0xff, 0xbf, 0x4a, 0xca, 0xe7, 0x70, 0x98, 0x5d,
	0x11, 0x73, 0x95, 0x09, 0x1c, 0x0e, 0x5d, 0x46, 0x3d, 0x3e, 0x87, 0x7c, 0xeb, 0xf0, 0x20, 0x5f,
	0x50, 0x32, 0xbe, 0xfc, 0xe6, 0x6f, 0xb1, 0x92, 0xf8, 0x4c, 0xbc, 0x60, 0xf3, 0xb1, 0x17, 0xac,
	0xb8, 0xf5, 0xcc, 0x70, 0x3c, 0xf9, 0xae, 0x2c, 0xa8, 0xf2, 0x43, 0xf9, 0x02, 0xee, 0xac, 0xc8,
	0xc6, 0xdc, 0xe3, 0x37, 0xb0, 0x1e, 0x18, 0x06, 0xa9, 0x02, 0xf4, 0x2f, 0x5e, 0x6b, 0xcf, 0x2f,
	0x5f, 0xbe, 0xbc, 0x7c, 0x5d, 0xff, 0x8c, 0xd4, 0xa1, 0x22, 0xbe, 0x3b, 0x83, 0x41, 0x77, 0x30,
	0xfc, 0xe6, 0xa2, 0x9e, 0x23, 0x35, 0x28, 0x8b, 0xc8, 0xf9, 0x65, 0xaf, 0x77, 0xd1, 0x1f, 0xd4,
	0xf3, 0x04, 0xa0, 0x24, 0x21, 0xf5, 0xc2, 0x62, 0xb1, 0x7f, 0x7e, 0xa1, 0xf6, 0xeb, 0x6b, 0x67,
	0xff, 0x96, 0xa0, 0x1c, 0x79, 0x63, 0x92, 0x17, 0xb0, 0x11, 0x3a, 0x11, 0x49, 0xbe, 0xc5, 0x22,
	0x66, 0xd8, 0xbc, 0xb3, 0x62, 0x95, 0xb9, 0x44, 0x85, 0x9d, 0x17, 0x94, 0x47, 0xb6, 0x96, 0xcf,
	0x9b, 0xe4, 0x9e, 0x11, 0xa3, 0x4b, 0xd9, 0x73, 0xc9, 0xde, 0x7e, 0x86, 0x9d, 0x14, 0xf7, 0x21,
	0xf7, 0x93, 0x8f, 0xb8, 0x54, 0x5b, 0x6b, 0x1e, 0x5d, 0x0f, 0xc8, 0x5c, 0xf2, 0x06, 0xaa, 0xcb,
	0x46, 0x44, 0x94, 0xe4, 0xeb, 0x32, 0xee, 0x76, 0xcd, 0xbb, 0x1f, 0xc5, 0x30, 0x97, 0xfc, 0x08,
	0xb5, 0xd8, 0x94, 0x90, 0x54, 0x5e, 0x6c, 0x4c, 0x9a, 0xf7, 0x3e, 0x0e, 0x62, 0x2e, 0xb1, 0x61,
	0x2f, 0xd5, 0xa6, 0xc8, 0x83, 0xe4, 0xff, 0x82, 0x0c, 0x4b, 0x6d, 0x1e, 0x5f, 0x17, 0xca, 0x5c,
	0x32, 0x83, 0x46, 0xd6, 0x24, 0x91, 0x87, 0x69, 0x15, 0x67, 0xd9, 0x40, 0xf3, 0xe4, 0x06, 0x68,
	0xe6, 0x92, 0x5f, 0x85, 0x1f, 0x67, 0x0c, 0x0d, 0x39, 0x49, 0x39, 0x41, 0xf6, 0x38, 0x37, 0xdb,
	0x37, 0x81, 0x33, 0xf7, 0xd9, 0xe3, 0x1f, 0x1e, 0x8d, 0x9d, 0x89, 0x6e, 0x8f, 0xdb, 0x4f, 0xce,
	0x38, 0x6f, 0x1b, 0xce, 0xf4, 0x14, 0x7f, 0x31, 0x1b, 0xce, 0xe4, 0x94, 0x51, 0xef, 0xbd, 0x65,
	0x50, 0x76, 0x1a, 0xfb, 0x4d, 0x7d, 0x55, 0x42, 0xc8, 0xe3, 0xff, 0x02, 0x00, 0x00, 0xff, 0xff,
	0x15, 0xba, 0x9c, 0x93, 0x6d, 0x0f, 0x00, 0x00,
}
