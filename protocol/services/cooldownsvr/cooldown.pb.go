// Code generated by protoc-gen-gogo.
// source: src/cooldownsvr/cooldown.proto
// DO NOT EDIT!

/*
	Package cooldown is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/cooldownsvr/cooldown.proto

	It has these top-level messages:
		FetchVisitReq
		FetchVisitRsp
		FetchAndAddGeneralVisitReq
		FetchAndAddGeneralVisitResp
		FetchAndAddPeriodVisitReq
		FetchAndAddPeriodVisitResp
		FetchAndSubGeneralVisitReq
		FetchAndSubGeneralVisitResp
		FetchAndSubPeriodVisitReq
		FetchAndSubPeriodVisitResp
		FetchGeneralVisitReq
		FetchGeneralVisitResp
		ClearGeneralVisitReq
		ClearGeneralVisitResp
		FetchComplexVisit
		FetchAndIncrGeneralComplexVisitReq
		FetchAndIncrGeneralComplexVisitResp
		FetchGeneralComplexVisitReq
		FetchGeneralComplexVisitResp
		CheckNxReq
		CheckNxResp
		DelTryPwdDevLimitReq
		DelTryPwdDevLimitResp
*/
package cooldown

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type FetchVisitReq struct {
	Key       string `protobuf:"bytes,1,req,name=key" json:"key"`
	Type      uint32 `protobuf:"varint,2,req,name=type" json:"type"`
	Val       int64  `protobuf:"varint,3,req,name=val" json:"val"`
	Ttl       uint32 `protobuf:"varint,4,opt,name=ttl" json:"ttl"`
	UpdateTtl uint32 `protobuf:"varint,5,opt,name=update_ttl,json=updateTtl" json:"update_ttl"`
}

func (m *FetchVisitReq) Reset()                    { *m = FetchVisitReq{} }
func (m *FetchVisitReq) String() string            { return proto.CompactTextString(m) }
func (*FetchVisitReq) ProtoMessage()               {}
func (*FetchVisitReq) Descriptor() ([]byte, []int) { return fileDescriptorCooldown, []int{0} }

func (m *FetchVisitReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *FetchVisitReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *FetchVisitReq) GetVal() int64 {
	if m != nil {
		return m.Val
	}
	return 0
}

func (m *FetchVisitReq) GetTtl() uint32 {
	if m != nil {
		return m.Ttl
	}
	return 0
}

func (m *FetchVisitReq) GetUpdateTtl() uint32 {
	if m != nil {
		return m.UpdateTtl
	}
	return 0
}

type FetchVisitRsp struct {
	OldVal int64  `protobuf:"varint,1,req,name=old_val,json=oldVal" json:"old_val"`
	Key    string `protobuf:"bytes,2,opt,name=key" json:"key"`
	Type   uint32 `protobuf:"varint,3,opt,name=type" json:"type"`
}

func (m *FetchVisitRsp) Reset()                    { *m = FetchVisitRsp{} }
func (m *FetchVisitRsp) String() string            { return proto.CompactTextString(m) }
func (*FetchVisitRsp) ProtoMessage()               {}
func (*FetchVisitRsp) Descriptor() ([]byte, []int) { return fileDescriptorCooldown, []int{1} }

func (m *FetchVisitRsp) GetOldVal() int64 {
	if m != nil {
		return m.OldVal
	}
	return 0
}

func (m *FetchVisitRsp) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *FetchVisitRsp) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type FetchAndAddGeneralVisitReq struct {
	VisitReq *FetchVisitReq `protobuf:"bytes,1,req,name=visit_req,json=visitReq" json:"visit_req,omitempty"`
}

func (m *FetchAndAddGeneralVisitReq) Reset()         { *m = FetchAndAddGeneralVisitReq{} }
func (m *FetchAndAddGeneralVisitReq) String() string { return proto.CompactTextString(m) }
func (*FetchAndAddGeneralVisitReq) ProtoMessage()    {}
func (*FetchAndAddGeneralVisitReq) Descriptor() ([]byte, []int) {
	return fileDescriptorCooldown, []int{2}
}

func (m *FetchAndAddGeneralVisitReq) GetVisitReq() *FetchVisitReq {
	if m != nil {
		return m.VisitReq
	}
	return nil
}

type FetchAndAddGeneralVisitResp struct {
	VisitResp *FetchVisitRsp `protobuf:"bytes,1,opt,name=visit_resp,json=visitResp" json:"visit_resp,omitempty"`
}

func (m *FetchAndAddGeneralVisitResp) Reset()         { *m = FetchAndAddGeneralVisitResp{} }
func (m *FetchAndAddGeneralVisitResp) String() string { return proto.CompactTextString(m) }
func (*FetchAndAddGeneralVisitResp) ProtoMessage()    {}
func (*FetchAndAddGeneralVisitResp) Descriptor() ([]byte, []int) {
	return fileDescriptorCooldown, []int{3}
}

func (m *FetchAndAddGeneralVisitResp) GetVisitResp() *FetchVisitRsp {
	if m != nil {
		return m.VisitResp
	}
	return nil
}

type FetchAndAddPeriodVisitReq struct {
	VisitReq *FetchVisitReq `protobuf:"bytes,1,req,name=visit_req,json=visitReq" json:"visit_req,omitempty"`
}

func (m *FetchAndAddPeriodVisitReq) Reset()         { *m = FetchAndAddPeriodVisitReq{} }
func (m *FetchAndAddPeriodVisitReq) String() string { return proto.CompactTextString(m) }
func (*FetchAndAddPeriodVisitReq) ProtoMessage()    {}
func (*FetchAndAddPeriodVisitReq) Descriptor() ([]byte, []int) {
	return fileDescriptorCooldown, []int{4}
}

func (m *FetchAndAddPeriodVisitReq) GetVisitReq() *FetchVisitReq {
	if m != nil {
		return m.VisitReq
	}
	return nil
}

type FetchAndAddPeriodVisitResp struct {
	VisitResp *FetchVisitRsp `protobuf:"bytes,1,opt,name=visit_resp,json=visitResp" json:"visit_resp,omitempty"`
}

func (m *FetchAndAddPeriodVisitResp) Reset()         { *m = FetchAndAddPeriodVisitResp{} }
func (m *FetchAndAddPeriodVisitResp) String() string { return proto.CompactTextString(m) }
func (*FetchAndAddPeriodVisitResp) ProtoMessage()    {}
func (*FetchAndAddPeriodVisitResp) Descriptor() ([]byte, []int) {
	return fileDescriptorCooldown, []int{5}
}

func (m *FetchAndAddPeriodVisitResp) GetVisitResp() *FetchVisitRsp {
	if m != nil {
		return m.VisitResp
	}
	return nil
}

type FetchAndSubGeneralVisitReq struct {
	VisitReq *FetchVisitReq `protobuf:"bytes,1,req,name=visit_req,json=visitReq" json:"visit_req,omitempty"`
}

func (m *FetchAndSubGeneralVisitReq) Reset()         { *m = FetchAndSubGeneralVisitReq{} }
func (m *FetchAndSubGeneralVisitReq) String() string { return proto.CompactTextString(m) }
func (*FetchAndSubGeneralVisitReq) ProtoMessage()    {}
func (*FetchAndSubGeneralVisitReq) Descriptor() ([]byte, []int) {
	return fileDescriptorCooldown, []int{6}
}

func (m *FetchAndSubGeneralVisitReq) GetVisitReq() *FetchVisitReq {
	if m != nil {
		return m.VisitReq
	}
	return nil
}

type FetchAndSubGeneralVisitResp struct {
	VisitResp *FetchVisitRsp `protobuf:"bytes,1,opt,name=visit_resp,json=visitResp" json:"visit_resp,omitempty"`
}

func (m *FetchAndSubGeneralVisitResp) Reset()         { *m = FetchAndSubGeneralVisitResp{} }
func (m *FetchAndSubGeneralVisitResp) String() string { return proto.CompactTextString(m) }
func (*FetchAndSubGeneralVisitResp) ProtoMessage()    {}
func (*FetchAndSubGeneralVisitResp) Descriptor() ([]byte, []int) {
	return fileDescriptorCooldown, []int{7}
}

func (m *FetchAndSubGeneralVisitResp) GetVisitResp() *FetchVisitRsp {
	if m != nil {
		return m.VisitResp
	}
	return nil
}

type FetchAndSubPeriodVisitReq struct {
	VisitReq *FetchVisitReq `protobuf:"bytes,1,req,name=visit_req,json=visitReq" json:"visit_req,omitempty"`
}

func (m *FetchAndSubPeriodVisitReq) Reset()         { *m = FetchAndSubPeriodVisitReq{} }
func (m *FetchAndSubPeriodVisitReq) String() string { return proto.CompactTextString(m) }
func (*FetchAndSubPeriodVisitReq) ProtoMessage()    {}
func (*FetchAndSubPeriodVisitReq) Descriptor() ([]byte, []int) {
	return fileDescriptorCooldown, []int{8}
}

func (m *FetchAndSubPeriodVisitReq) GetVisitReq() *FetchVisitReq {
	if m != nil {
		return m.VisitReq
	}
	return nil
}

type FetchAndSubPeriodVisitResp struct {
	VisitResp *FetchVisitRsp `protobuf:"bytes,1,opt,name=visit_resp,json=visitResp" json:"visit_resp,omitempty"`
}

func (m *FetchAndSubPeriodVisitResp) Reset()         { *m = FetchAndSubPeriodVisitResp{} }
func (m *FetchAndSubPeriodVisitResp) String() string { return proto.CompactTextString(m) }
func (*FetchAndSubPeriodVisitResp) ProtoMessage()    {}
func (*FetchAndSubPeriodVisitResp) Descriptor() ([]byte, []int) {
	return fileDescriptorCooldown, []int{9}
}

func (m *FetchAndSubPeriodVisitResp) GetVisitResp() *FetchVisitRsp {
	if m != nil {
		return m.VisitResp
	}
	return nil
}

type FetchGeneralVisitReq struct {
	VisitReq *FetchVisitReq `protobuf:"bytes,1,req,name=visit_req,json=visitReq" json:"visit_req,omitempty"`
	RetTtl   bool           `protobuf:"varint,2,opt,name=ret_ttl,json=retTtl" json:"ret_ttl"`
}

func (m *FetchGeneralVisitReq) Reset()                    { *m = FetchGeneralVisitReq{} }
func (m *FetchGeneralVisitReq) String() string            { return proto.CompactTextString(m) }
func (*FetchGeneralVisitReq) ProtoMessage()               {}
func (*FetchGeneralVisitReq) Descriptor() ([]byte, []int) { return fileDescriptorCooldown, []int{10} }

func (m *FetchGeneralVisitReq) GetVisitReq() *FetchVisitReq {
	if m != nil {
		return m.VisitReq
	}
	return nil
}

func (m *FetchGeneralVisitReq) GetRetTtl() bool {
	if m != nil {
		return m.RetTtl
	}
	return false
}

type FetchGeneralVisitResp struct {
	VisitResp *FetchVisitRsp `protobuf:"bytes,1,opt,name=visit_resp,json=visitResp" json:"visit_resp,omitempty"`
	Ttl       uint32         `protobuf:"varint,2,opt,name=ttl" json:"ttl"`
}

func (m *FetchGeneralVisitResp) Reset()                    { *m = FetchGeneralVisitResp{} }
func (m *FetchGeneralVisitResp) String() string            { return proto.CompactTextString(m) }
func (*FetchGeneralVisitResp) ProtoMessage()               {}
func (*FetchGeneralVisitResp) Descriptor() ([]byte, []int) { return fileDescriptorCooldown, []int{11} }

func (m *FetchGeneralVisitResp) GetVisitResp() *FetchVisitRsp {
	if m != nil {
		return m.VisitResp
	}
	return nil
}

func (m *FetchGeneralVisitResp) GetTtl() uint32 {
	if m != nil {
		return m.Ttl
	}
	return 0
}

type ClearGeneralVisitReq struct {
	Key  string `protobuf:"bytes,1,req,name=key" json:"key"`
	Type uint32 `protobuf:"varint,2,req,name=type" json:"type"`
}

func (m *ClearGeneralVisitReq) Reset()                    { *m = ClearGeneralVisitReq{} }
func (m *ClearGeneralVisitReq) String() string            { return proto.CompactTextString(m) }
func (*ClearGeneralVisitReq) ProtoMessage()               {}
func (*ClearGeneralVisitReq) Descriptor() ([]byte, []int) { return fileDescriptorCooldown, []int{12} }

func (m *ClearGeneralVisitReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *ClearGeneralVisitReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type ClearGeneralVisitResp struct {
}

func (m *ClearGeneralVisitResp) Reset()                    { *m = ClearGeneralVisitResp{} }
func (m *ClearGeneralVisitResp) String() string            { return proto.CompactTextString(m) }
func (*ClearGeneralVisitResp) ProtoMessage()               {}
func (*ClearGeneralVisitResp) Descriptor() ([]byte, []int) { return fileDescriptorCooldown, []int{13} }

// 复杂结构(HMAP)
type FetchComplexVisit struct {
	SubType string `protobuf:"bytes,1,req,name=sub_type,json=subType" json:"sub_type"`
	OldVal  int64  `protobuf:"varint,2,req,name=old_val,json=oldVal" json:"old_val"`
}

func (m *FetchComplexVisit) Reset()                    { *m = FetchComplexVisit{} }
func (m *FetchComplexVisit) String() string            { return proto.CompactTextString(m) }
func (*FetchComplexVisit) ProtoMessage()               {}
func (*FetchComplexVisit) Descriptor() ([]byte, []int) { return fileDescriptorCooldown, []int{14} }

func (m *FetchComplexVisit) GetSubType() string {
	if m != nil {
		return m.SubType
	}
	return ""
}

func (m *FetchComplexVisit) GetOldVal() int64 {
	if m != nil {
		return m.OldVal
	}
	return 0
}

type FetchAndIncrGeneralComplexVisitReq struct {
	Key     string `protobuf:"bytes,1,req,name=key" json:"key"`
	SubType string `protobuf:"bytes,2,opt,name=sub_type,json=subType" json:"sub_type"`
	Val     int64  `protobuf:"varint,3,opt,name=val" json:"val"`
	Ttl     int32  `protobuf:"varint,4,opt,name=ttl" json:"ttl"`
}

func (m *FetchAndIncrGeneralComplexVisitReq) Reset()         { *m = FetchAndIncrGeneralComplexVisitReq{} }
func (m *FetchAndIncrGeneralComplexVisitReq) String() string { return proto.CompactTextString(m) }
func (*FetchAndIncrGeneralComplexVisitReq) ProtoMessage()    {}
func (*FetchAndIncrGeneralComplexVisitReq) Descriptor() ([]byte, []int) {
	return fileDescriptorCooldown, []int{15}
}

func (m *FetchAndIncrGeneralComplexVisitReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *FetchAndIncrGeneralComplexVisitReq) GetSubType() string {
	if m != nil {
		return m.SubType
	}
	return ""
}

func (m *FetchAndIncrGeneralComplexVisitReq) GetVal() int64 {
	if m != nil {
		return m.Val
	}
	return 0
}

func (m *FetchAndIncrGeneralComplexVisitReq) GetTtl() int32 {
	if m != nil {
		return m.Ttl
	}
	return 0
}

type FetchAndIncrGeneralComplexVisitResp struct {
	ComplexVisit *FetchComplexVisit `protobuf:"bytes,1,opt,name=complex_visit,json=complexVisit" json:"complex_visit,omitempty"`
}

func (m *FetchAndIncrGeneralComplexVisitResp) Reset()         { *m = FetchAndIncrGeneralComplexVisitResp{} }
func (m *FetchAndIncrGeneralComplexVisitResp) String() string { return proto.CompactTextString(m) }
func (*FetchAndIncrGeneralComplexVisitResp) ProtoMessage()    {}
func (*FetchAndIncrGeneralComplexVisitResp) Descriptor() ([]byte, []int) {
	return fileDescriptorCooldown, []int{16}
}

func (m *FetchAndIncrGeneralComplexVisitResp) GetComplexVisit() *FetchComplexVisit {
	if m != nil {
		return m.ComplexVisit
	}
	return nil
}

type FetchGeneralComplexVisitReq struct {
	Key string `protobuf:"bytes,1,req,name=key" json:"key"`
}

func (m *FetchGeneralComplexVisitReq) Reset()         { *m = FetchGeneralComplexVisitReq{} }
func (m *FetchGeneralComplexVisitReq) String() string { return proto.CompactTextString(m) }
func (*FetchGeneralComplexVisitReq) ProtoMessage()    {}
func (*FetchGeneralComplexVisitReq) Descriptor() ([]byte, []int) {
	return fileDescriptorCooldown, []int{17}
}

func (m *FetchGeneralComplexVisitReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

type FetchGeneralComplexVisitResp struct {
	ComplexVisitList []*FetchComplexVisit `protobuf:"bytes,1,rep,name=complex_visit_list,json=complexVisitList" json:"complex_visit_list,omitempty"`
}

func (m *FetchGeneralComplexVisitResp) Reset()         { *m = FetchGeneralComplexVisitResp{} }
func (m *FetchGeneralComplexVisitResp) String() string { return proto.CompactTextString(m) }
func (*FetchGeneralComplexVisitResp) ProtoMessage()    {}
func (*FetchGeneralComplexVisitResp) Descriptor() ([]byte, []int) {
	return fileDescriptorCooldown, []int{18}
}

func (m *FetchGeneralComplexVisitResp) GetComplexVisitList() []*FetchComplexVisit {
	if m != nil {
		return m.ComplexVisitList
	}
	return nil
}

type CheckNxReq struct {
	Key          string `protobuf:"bytes,1,req,name=key" json:"key"`
	Ttl          int32  `protobuf:"varint,2,req,name=ttl" json:"ttl"`
	Uid          uint32 `protobuf:"varint,3,req,name=uid" json:"uid"`
	Val          string `protobuf:"bytes,4,opt,name=val" json:"val"`
	RetBeforeVal bool   `protobuf:"varint,5,opt,name=ret_before_val,json=retBeforeVal" json:"ret_before_val"`
}

func (m *CheckNxReq) Reset()                    { *m = CheckNxReq{} }
func (m *CheckNxReq) String() string            { return proto.CompactTextString(m) }
func (*CheckNxReq) ProtoMessage()               {}
func (*CheckNxReq) Descriptor() ([]byte, []int) { return fileDescriptorCooldown, []int{19} }

func (m *CheckNxReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *CheckNxReq) GetTtl() int32 {
	if m != nil {
		return m.Ttl
	}
	return 0
}

func (m *CheckNxReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckNxReq) GetVal() string {
	if m != nil {
		return m.Val
	}
	return ""
}

func (m *CheckNxReq) GetRetBeforeVal() bool {
	if m != nil {
		return m.RetBeforeVal
	}
	return false
}

type CheckNxResp struct {
	IsExistBefore  bool   `protobuf:"varint,1,req,name=is_exist_before,json=isExistBefore" json:"is_exist_before"`
	BeforeExistVal string `protobuf:"bytes,2,opt,name=before_exist_val,json=beforeExistVal" json:"before_exist_val"`
}

func (m *CheckNxResp) Reset()                    { *m = CheckNxResp{} }
func (m *CheckNxResp) String() string            { return proto.CompactTextString(m) }
func (*CheckNxResp) ProtoMessage()               {}
func (*CheckNxResp) Descriptor() ([]byte, []int) { return fileDescriptorCooldown, []int{20} }

func (m *CheckNxResp) GetIsExistBefore() bool {
	if m != nil {
		return m.IsExistBefore
	}
	return false
}

func (m *CheckNxResp) GetBeforeExistVal() string {
	if m != nil {
		return m.BeforeExistVal
	}
	return ""
}

// 清除登录密码错误限制
type DelTryPwdDevLimitReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *DelTryPwdDevLimitReq) Reset()                    { *m = DelTryPwdDevLimitReq{} }
func (m *DelTryPwdDevLimitReq) String() string            { return proto.CompactTextString(m) }
func (*DelTryPwdDevLimitReq) ProtoMessage()               {}
func (*DelTryPwdDevLimitReq) Descriptor() ([]byte, []int) { return fileDescriptorCooldown, []int{21} }

func (m *DelTryPwdDevLimitReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type DelTryPwdDevLimitResp struct {
}

func (m *DelTryPwdDevLimitResp) Reset()                    { *m = DelTryPwdDevLimitResp{} }
func (m *DelTryPwdDevLimitResp) String() string            { return proto.CompactTextString(m) }
func (*DelTryPwdDevLimitResp) ProtoMessage()               {}
func (*DelTryPwdDevLimitResp) Descriptor() ([]byte, []int) { return fileDescriptorCooldown, []int{22} }

func init() {
	proto.RegisterType((*FetchVisitReq)(nil), "cooldown.FetchVisitReq")
	proto.RegisterType((*FetchVisitRsp)(nil), "cooldown.FetchVisitRsp")
	proto.RegisterType((*FetchAndAddGeneralVisitReq)(nil), "cooldown.FetchAndAddGeneralVisitReq")
	proto.RegisterType((*FetchAndAddGeneralVisitResp)(nil), "cooldown.FetchAndAddGeneralVisitResp")
	proto.RegisterType((*FetchAndAddPeriodVisitReq)(nil), "cooldown.FetchAndAddPeriodVisitReq")
	proto.RegisterType((*FetchAndAddPeriodVisitResp)(nil), "cooldown.FetchAndAddPeriodVisitResp")
	proto.RegisterType((*FetchAndSubGeneralVisitReq)(nil), "cooldown.FetchAndSubGeneralVisitReq")
	proto.RegisterType((*FetchAndSubGeneralVisitResp)(nil), "cooldown.FetchAndSubGeneralVisitResp")
	proto.RegisterType((*FetchAndSubPeriodVisitReq)(nil), "cooldown.FetchAndSubPeriodVisitReq")
	proto.RegisterType((*FetchAndSubPeriodVisitResp)(nil), "cooldown.FetchAndSubPeriodVisitResp")
	proto.RegisterType((*FetchGeneralVisitReq)(nil), "cooldown.FetchGeneralVisitReq")
	proto.RegisterType((*FetchGeneralVisitResp)(nil), "cooldown.FetchGeneralVisitResp")
	proto.RegisterType((*ClearGeneralVisitReq)(nil), "cooldown.ClearGeneralVisitReq")
	proto.RegisterType((*ClearGeneralVisitResp)(nil), "cooldown.ClearGeneralVisitResp")
	proto.RegisterType((*FetchComplexVisit)(nil), "cooldown.FetchComplexVisit")
	proto.RegisterType((*FetchAndIncrGeneralComplexVisitReq)(nil), "cooldown.FetchAndIncrGeneralComplexVisitReq")
	proto.RegisterType((*FetchAndIncrGeneralComplexVisitResp)(nil), "cooldown.FetchAndIncrGeneralComplexVisitResp")
	proto.RegisterType((*FetchGeneralComplexVisitReq)(nil), "cooldown.FetchGeneralComplexVisitReq")
	proto.RegisterType((*FetchGeneralComplexVisitResp)(nil), "cooldown.FetchGeneralComplexVisitResp")
	proto.RegisterType((*CheckNxReq)(nil), "cooldown.CheckNxReq")
	proto.RegisterType((*CheckNxResp)(nil), "cooldown.CheckNxResp")
	proto.RegisterType((*DelTryPwdDevLimitReq)(nil), "cooldown.DelTryPwdDevLimitReq")
	proto.RegisterType((*DelTryPwdDevLimitResp)(nil), "cooldown.DelTryPwdDevLimitResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for Cooldown service

type CooldownClient interface {
	// 通用的
	FetchAndAddGeneralVisit(ctx context.Context, in *FetchAndAddGeneralVisitReq, opts ...grpc.CallOption) (*FetchAndAddGeneralVisitResp, error)
	FetchAndSubGeneralVisit(ctx context.Context, in *FetchAndSubGeneralVisitReq, opts ...grpc.CallOption) (*FetchAndSubGeneralVisitResp, error)
	// 周期 : 每日/每小时/每分钟等
	FetchAndAddPeriodVisit(ctx context.Context, in *FetchAndAddPeriodVisitReq, opts ...grpc.CallOption) (*FetchAndAddPeriodVisitResp, error)
	FetchAndSubPeriodVisit(ctx context.Context, in *FetchAndSubPeriodVisitReq, opts ...grpc.CallOption) (*FetchAndSubPeriodVisitResp, error)
	// 通用的获取
	FetchGeneralVisit(ctx context.Context, in *FetchGeneralVisitReq, opts ...grpc.CallOption) (*FetchGeneralVisitResp, error)
	// 复杂通用结构的设置/获取
	FetchAndIncrGeneralComplexVisit(ctx context.Context, in *FetchAndIncrGeneralComplexVisitReq, opts ...grpc.CallOption) (*FetchAndIncrGeneralComplexVisitResp, error)
	FetchGeneralComplexVisit(ctx context.Context, in *FetchGeneralComplexVisitReq, opts ...grpc.CallOption) (*FetchGeneralComplexVisitResp, error)
	//
	CheckNx(ctx context.Context, in *CheckNxReq, opts ...grpc.CallOption) (*CheckNxResp, error)
	ClearGeneralVisit(ctx context.Context, in *ClearGeneralVisitReq, opts ...grpc.CallOption) (*ClearGeneralVisitResp, error)
	DelTryPwdDevLimit(ctx context.Context, in *DelTryPwdDevLimitReq, opts ...grpc.CallOption) (*DelTryPwdDevLimitResp, error)
}

type cooldownClient struct {
	cc *grpc.ClientConn
}

func NewCooldownClient(cc *grpc.ClientConn) CooldownClient {
	return &cooldownClient{cc}
}

func (c *cooldownClient) FetchAndAddGeneralVisit(ctx context.Context, in *FetchAndAddGeneralVisitReq, opts ...grpc.CallOption) (*FetchAndAddGeneralVisitResp, error) {
	out := new(FetchAndAddGeneralVisitResp)
	err := grpc.Invoke(ctx, "/cooldown.Cooldown/FetchAndAddGeneralVisit", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cooldownClient) FetchAndSubGeneralVisit(ctx context.Context, in *FetchAndSubGeneralVisitReq, opts ...grpc.CallOption) (*FetchAndSubGeneralVisitResp, error) {
	out := new(FetchAndSubGeneralVisitResp)
	err := grpc.Invoke(ctx, "/cooldown.Cooldown/FetchAndSubGeneralVisit", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cooldownClient) FetchAndAddPeriodVisit(ctx context.Context, in *FetchAndAddPeriodVisitReq, opts ...grpc.CallOption) (*FetchAndAddPeriodVisitResp, error) {
	out := new(FetchAndAddPeriodVisitResp)
	err := grpc.Invoke(ctx, "/cooldown.Cooldown/FetchAndAddPeriodVisit", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cooldownClient) FetchAndSubPeriodVisit(ctx context.Context, in *FetchAndSubPeriodVisitReq, opts ...grpc.CallOption) (*FetchAndSubPeriodVisitResp, error) {
	out := new(FetchAndSubPeriodVisitResp)
	err := grpc.Invoke(ctx, "/cooldown.Cooldown/FetchAndSubPeriodVisit", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cooldownClient) FetchGeneralVisit(ctx context.Context, in *FetchGeneralVisitReq, opts ...grpc.CallOption) (*FetchGeneralVisitResp, error) {
	out := new(FetchGeneralVisitResp)
	err := grpc.Invoke(ctx, "/cooldown.Cooldown/FetchGeneralVisit", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cooldownClient) FetchAndIncrGeneralComplexVisit(ctx context.Context, in *FetchAndIncrGeneralComplexVisitReq, opts ...grpc.CallOption) (*FetchAndIncrGeneralComplexVisitResp, error) {
	out := new(FetchAndIncrGeneralComplexVisitResp)
	err := grpc.Invoke(ctx, "/cooldown.Cooldown/FetchAndIncrGeneralComplexVisit", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cooldownClient) FetchGeneralComplexVisit(ctx context.Context, in *FetchGeneralComplexVisitReq, opts ...grpc.CallOption) (*FetchGeneralComplexVisitResp, error) {
	out := new(FetchGeneralComplexVisitResp)
	err := grpc.Invoke(ctx, "/cooldown.Cooldown/FetchGeneralComplexVisit", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cooldownClient) CheckNx(ctx context.Context, in *CheckNxReq, opts ...grpc.CallOption) (*CheckNxResp, error) {
	out := new(CheckNxResp)
	err := grpc.Invoke(ctx, "/cooldown.Cooldown/CheckNx", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cooldownClient) ClearGeneralVisit(ctx context.Context, in *ClearGeneralVisitReq, opts ...grpc.CallOption) (*ClearGeneralVisitResp, error) {
	out := new(ClearGeneralVisitResp)
	err := grpc.Invoke(ctx, "/cooldown.Cooldown/ClearGeneralVisit", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cooldownClient) DelTryPwdDevLimit(ctx context.Context, in *DelTryPwdDevLimitReq, opts ...grpc.CallOption) (*DelTryPwdDevLimitResp, error) {
	out := new(DelTryPwdDevLimitResp)
	err := grpc.Invoke(ctx, "/cooldown.Cooldown/DelTryPwdDevLimit", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for Cooldown service

type CooldownServer interface {
	// 通用的
	FetchAndAddGeneralVisit(context.Context, *FetchAndAddGeneralVisitReq) (*FetchAndAddGeneralVisitResp, error)
	FetchAndSubGeneralVisit(context.Context, *FetchAndSubGeneralVisitReq) (*FetchAndSubGeneralVisitResp, error)
	// 周期 : 每日/每小时/每分钟等
	FetchAndAddPeriodVisit(context.Context, *FetchAndAddPeriodVisitReq) (*FetchAndAddPeriodVisitResp, error)
	FetchAndSubPeriodVisit(context.Context, *FetchAndSubPeriodVisitReq) (*FetchAndSubPeriodVisitResp, error)
	// 通用的获取
	FetchGeneralVisit(context.Context, *FetchGeneralVisitReq) (*FetchGeneralVisitResp, error)
	// 复杂通用结构的设置/获取
	FetchAndIncrGeneralComplexVisit(context.Context, *FetchAndIncrGeneralComplexVisitReq) (*FetchAndIncrGeneralComplexVisitResp, error)
	FetchGeneralComplexVisit(context.Context, *FetchGeneralComplexVisitReq) (*FetchGeneralComplexVisitResp, error)
	//
	CheckNx(context.Context, *CheckNxReq) (*CheckNxResp, error)
	ClearGeneralVisit(context.Context, *ClearGeneralVisitReq) (*ClearGeneralVisitResp, error)
	DelTryPwdDevLimit(context.Context, *DelTryPwdDevLimitReq) (*DelTryPwdDevLimitResp, error)
}

func RegisterCooldownServer(s *grpc.Server, srv CooldownServer) {
	s.RegisterService(&_Cooldown_serviceDesc, srv)
}

func _Cooldown_FetchAndAddGeneralVisit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchAndAddGeneralVisitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CooldownServer).FetchAndAddGeneralVisit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cooldown.Cooldown/FetchAndAddGeneralVisit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CooldownServer).FetchAndAddGeneralVisit(ctx, req.(*FetchAndAddGeneralVisitReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cooldown_FetchAndSubGeneralVisit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchAndSubGeneralVisitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CooldownServer).FetchAndSubGeneralVisit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cooldown.Cooldown/FetchAndSubGeneralVisit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CooldownServer).FetchAndSubGeneralVisit(ctx, req.(*FetchAndSubGeneralVisitReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cooldown_FetchAndAddPeriodVisit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchAndAddPeriodVisitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CooldownServer).FetchAndAddPeriodVisit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cooldown.Cooldown/FetchAndAddPeriodVisit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CooldownServer).FetchAndAddPeriodVisit(ctx, req.(*FetchAndAddPeriodVisitReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cooldown_FetchAndSubPeriodVisit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchAndSubPeriodVisitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CooldownServer).FetchAndSubPeriodVisit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cooldown.Cooldown/FetchAndSubPeriodVisit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CooldownServer).FetchAndSubPeriodVisit(ctx, req.(*FetchAndSubPeriodVisitReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cooldown_FetchGeneralVisit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchGeneralVisitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CooldownServer).FetchGeneralVisit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cooldown.Cooldown/FetchGeneralVisit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CooldownServer).FetchGeneralVisit(ctx, req.(*FetchGeneralVisitReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cooldown_FetchAndIncrGeneralComplexVisit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchAndIncrGeneralComplexVisitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CooldownServer).FetchAndIncrGeneralComplexVisit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cooldown.Cooldown/FetchAndIncrGeneralComplexVisit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CooldownServer).FetchAndIncrGeneralComplexVisit(ctx, req.(*FetchAndIncrGeneralComplexVisitReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cooldown_FetchGeneralComplexVisit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchGeneralComplexVisitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CooldownServer).FetchGeneralComplexVisit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cooldown.Cooldown/FetchGeneralComplexVisit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CooldownServer).FetchGeneralComplexVisit(ctx, req.(*FetchGeneralComplexVisitReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cooldown_CheckNx_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckNxReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CooldownServer).CheckNx(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cooldown.Cooldown/CheckNx",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CooldownServer).CheckNx(ctx, req.(*CheckNxReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cooldown_ClearGeneralVisit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearGeneralVisitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CooldownServer).ClearGeneralVisit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cooldown.Cooldown/ClearGeneralVisit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CooldownServer).ClearGeneralVisit(ctx, req.(*ClearGeneralVisitReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cooldown_DelTryPwdDevLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelTryPwdDevLimitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CooldownServer).DelTryPwdDevLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cooldown.Cooldown/DelTryPwdDevLimit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CooldownServer).DelTryPwdDevLimit(ctx, req.(*DelTryPwdDevLimitReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Cooldown_serviceDesc = grpc.ServiceDesc{
	ServiceName: "cooldown.Cooldown",
	HandlerType: (*CooldownServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "FetchAndAddGeneralVisit",
			Handler:    _Cooldown_FetchAndAddGeneralVisit_Handler,
		},
		{
			MethodName: "FetchAndSubGeneralVisit",
			Handler:    _Cooldown_FetchAndSubGeneralVisit_Handler,
		},
		{
			MethodName: "FetchAndAddPeriodVisit",
			Handler:    _Cooldown_FetchAndAddPeriodVisit_Handler,
		},
		{
			MethodName: "FetchAndSubPeriodVisit",
			Handler:    _Cooldown_FetchAndSubPeriodVisit_Handler,
		},
		{
			MethodName: "FetchGeneralVisit",
			Handler:    _Cooldown_FetchGeneralVisit_Handler,
		},
		{
			MethodName: "FetchAndIncrGeneralComplexVisit",
			Handler:    _Cooldown_FetchAndIncrGeneralComplexVisit_Handler,
		},
		{
			MethodName: "FetchGeneralComplexVisit",
			Handler:    _Cooldown_FetchGeneralComplexVisit_Handler,
		},
		{
			MethodName: "CheckNx",
			Handler:    _Cooldown_CheckNx_Handler,
		},
		{
			MethodName: "ClearGeneralVisit",
			Handler:    _Cooldown_ClearGeneralVisit_Handler,
		},
		{
			MethodName: "DelTryPwdDevLimit",
			Handler:    _Cooldown_DelTryPwdDevLimit_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/cooldownsvr/cooldown.proto",
}

func (m *FetchVisitReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FetchVisitReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintCooldown(dAtA, i, uint64(len(m.Key)))
	i += copy(dAtA[i:], m.Key)
	dAtA[i] = 0x10
	i++
	i = encodeVarintCooldown(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCooldown(dAtA, i, uint64(m.Val))
	dAtA[i] = 0x20
	i++
	i = encodeVarintCooldown(dAtA, i, uint64(m.Ttl))
	dAtA[i] = 0x28
	i++
	i = encodeVarintCooldown(dAtA, i, uint64(m.UpdateTtl))
	return i, nil
}

func (m *FetchVisitRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FetchVisitRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCooldown(dAtA, i, uint64(m.OldVal))
	dAtA[i] = 0x12
	i++
	i = encodeVarintCooldown(dAtA, i, uint64(len(m.Key)))
	i += copy(dAtA[i:], m.Key)
	dAtA[i] = 0x18
	i++
	i = encodeVarintCooldown(dAtA, i, uint64(m.Type))
	return i, nil
}

func (m *FetchAndAddGeneralVisitReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FetchAndAddGeneralVisitReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.VisitReq == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("visit_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCooldown(dAtA, i, uint64(m.VisitReq.Size()))
		n1, err := m.VisitReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *FetchAndAddGeneralVisitResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FetchAndAddGeneralVisitResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.VisitResp != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCooldown(dAtA, i, uint64(m.VisitResp.Size()))
		n2, err := m.VisitResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func (m *FetchAndAddPeriodVisitReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FetchAndAddPeriodVisitReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.VisitReq == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("visit_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCooldown(dAtA, i, uint64(m.VisitReq.Size()))
		n3, err := m.VisitReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	return i, nil
}

func (m *FetchAndAddPeriodVisitResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FetchAndAddPeriodVisitResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.VisitResp != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCooldown(dAtA, i, uint64(m.VisitResp.Size()))
		n4, err := m.VisitResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	return i, nil
}

func (m *FetchAndSubGeneralVisitReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FetchAndSubGeneralVisitReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.VisitReq == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("visit_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCooldown(dAtA, i, uint64(m.VisitReq.Size()))
		n5, err := m.VisitReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	return i, nil
}

func (m *FetchAndSubGeneralVisitResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FetchAndSubGeneralVisitResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.VisitResp != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCooldown(dAtA, i, uint64(m.VisitResp.Size()))
		n6, err := m.VisitResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	return i, nil
}

func (m *FetchAndSubPeriodVisitReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FetchAndSubPeriodVisitReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.VisitReq == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("visit_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCooldown(dAtA, i, uint64(m.VisitReq.Size()))
		n7, err := m.VisitReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	return i, nil
}

func (m *FetchAndSubPeriodVisitResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FetchAndSubPeriodVisitResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.VisitResp != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCooldown(dAtA, i, uint64(m.VisitResp.Size()))
		n8, err := m.VisitResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	return i, nil
}

func (m *FetchGeneralVisitReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FetchGeneralVisitReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.VisitReq == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("visit_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCooldown(dAtA, i, uint64(m.VisitReq.Size()))
		n9, err := m.VisitReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	dAtA[i] = 0x10
	i++
	if m.RetTtl {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *FetchGeneralVisitResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FetchGeneralVisitResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.VisitResp != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCooldown(dAtA, i, uint64(m.VisitResp.Size()))
		n10, err := m.VisitResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCooldown(dAtA, i, uint64(m.Ttl))
	return i, nil
}

func (m *ClearGeneralVisitReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ClearGeneralVisitReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintCooldown(dAtA, i, uint64(len(m.Key)))
	i += copy(dAtA[i:], m.Key)
	dAtA[i] = 0x10
	i++
	i = encodeVarintCooldown(dAtA, i, uint64(m.Type))
	return i, nil
}

func (m *ClearGeneralVisitResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ClearGeneralVisitResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *FetchComplexVisit) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FetchComplexVisit) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintCooldown(dAtA, i, uint64(len(m.SubType)))
	i += copy(dAtA[i:], m.SubType)
	dAtA[i] = 0x10
	i++
	i = encodeVarintCooldown(dAtA, i, uint64(m.OldVal))
	return i, nil
}

func (m *FetchAndIncrGeneralComplexVisitReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FetchAndIncrGeneralComplexVisitReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintCooldown(dAtA, i, uint64(len(m.Key)))
	i += copy(dAtA[i:], m.Key)
	dAtA[i] = 0x12
	i++
	i = encodeVarintCooldown(dAtA, i, uint64(len(m.SubType)))
	i += copy(dAtA[i:], m.SubType)
	dAtA[i] = 0x18
	i++
	i = encodeVarintCooldown(dAtA, i, uint64(m.Val))
	dAtA[i] = 0x20
	i++
	i = encodeVarintCooldown(dAtA, i, uint64(m.Ttl))
	return i, nil
}

func (m *FetchAndIncrGeneralComplexVisitResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FetchAndIncrGeneralComplexVisitResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ComplexVisit != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCooldown(dAtA, i, uint64(m.ComplexVisit.Size()))
		n11, err := m.ComplexVisit.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	return i, nil
}

func (m *FetchGeneralComplexVisitReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FetchGeneralComplexVisitReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintCooldown(dAtA, i, uint64(len(m.Key)))
	i += copy(dAtA[i:], m.Key)
	return i, nil
}

func (m *FetchGeneralComplexVisitResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FetchGeneralComplexVisitResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ComplexVisitList) > 0 {
		for _, msg := range m.ComplexVisitList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintCooldown(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *CheckNxReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckNxReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintCooldown(dAtA, i, uint64(len(m.Key)))
	i += copy(dAtA[i:], m.Key)
	dAtA[i] = 0x10
	i++
	i = encodeVarintCooldown(dAtA, i, uint64(m.Ttl))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCooldown(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x22
	i++
	i = encodeVarintCooldown(dAtA, i, uint64(len(m.Val)))
	i += copy(dAtA[i:], m.Val)
	dAtA[i] = 0x28
	i++
	if m.RetBeforeVal {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *CheckNxResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckNxResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.IsExistBefore {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x12
	i++
	i = encodeVarintCooldown(dAtA, i, uint64(len(m.BeforeExistVal)))
	i += copy(dAtA[i:], m.BeforeExistVal)
	return i, nil
}

func (m *DelTryPwdDevLimitReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelTryPwdDevLimitReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCooldown(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *DelTryPwdDevLimitResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelTryPwdDevLimitResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func encodeFixed64Cooldown(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Cooldown(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintCooldown(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *FetchVisitReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Key)
	n += 1 + l + sovCooldown(uint64(l))
	n += 1 + sovCooldown(uint64(m.Type))
	n += 1 + sovCooldown(uint64(m.Val))
	n += 1 + sovCooldown(uint64(m.Ttl))
	n += 1 + sovCooldown(uint64(m.UpdateTtl))
	return n
}

func (m *FetchVisitRsp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCooldown(uint64(m.OldVal))
	l = len(m.Key)
	n += 1 + l + sovCooldown(uint64(l))
	n += 1 + sovCooldown(uint64(m.Type))
	return n
}

func (m *FetchAndAddGeneralVisitReq) Size() (n int) {
	var l int
	_ = l
	if m.VisitReq != nil {
		l = m.VisitReq.Size()
		n += 1 + l + sovCooldown(uint64(l))
	}
	return n
}

func (m *FetchAndAddGeneralVisitResp) Size() (n int) {
	var l int
	_ = l
	if m.VisitResp != nil {
		l = m.VisitResp.Size()
		n += 1 + l + sovCooldown(uint64(l))
	}
	return n
}

func (m *FetchAndAddPeriodVisitReq) Size() (n int) {
	var l int
	_ = l
	if m.VisitReq != nil {
		l = m.VisitReq.Size()
		n += 1 + l + sovCooldown(uint64(l))
	}
	return n
}

func (m *FetchAndAddPeriodVisitResp) Size() (n int) {
	var l int
	_ = l
	if m.VisitResp != nil {
		l = m.VisitResp.Size()
		n += 1 + l + sovCooldown(uint64(l))
	}
	return n
}

func (m *FetchAndSubGeneralVisitReq) Size() (n int) {
	var l int
	_ = l
	if m.VisitReq != nil {
		l = m.VisitReq.Size()
		n += 1 + l + sovCooldown(uint64(l))
	}
	return n
}

func (m *FetchAndSubGeneralVisitResp) Size() (n int) {
	var l int
	_ = l
	if m.VisitResp != nil {
		l = m.VisitResp.Size()
		n += 1 + l + sovCooldown(uint64(l))
	}
	return n
}

func (m *FetchAndSubPeriodVisitReq) Size() (n int) {
	var l int
	_ = l
	if m.VisitReq != nil {
		l = m.VisitReq.Size()
		n += 1 + l + sovCooldown(uint64(l))
	}
	return n
}

func (m *FetchAndSubPeriodVisitResp) Size() (n int) {
	var l int
	_ = l
	if m.VisitResp != nil {
		l = m.VisitResp.Size()
		n += 1 + l + sovCooldown(uint64(l))
	}
	return n
}

func (m *FetchGeneralVisitReq) Size() (n int) {
	var l int
	_ = l
	if m.VisitReq != nil {
		l = m.VisitReq.Size()
		n += 1 + l + sovCooldown(uint64(l))
	}
	n += 2
	return n
}

func (m *FetchGeneralVisitResp) Size() (n int) {
	var l int
	_ = l
	if m.VisitResp != nil {
		l = m.VisitResp.Size()
		n += 1 + l + sovCooldown(uint64(l))
	}
	n += 1 + sovCooldown(uint64(m.Ttl))
	return n
}

func (m *ClearGeneralVisitReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Key)
	n += 1 + l + sovCooldown(uint64(l))
	n += 1 + sovCooldown(uint64(m.Type))
	return n
}

func (m *ClearGeneralVisitResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *FetchComplexVisit) Size() (n int) {
	var l int
	_ = l
	l = len(m.SubType)
	n += 1 + l + sovCooldown(uint64(l))
	n += 1 + sovCooldown(uint64(m.OldVal))
	return n
}

func (m *FetchAndIncrGeneralComplexVisitReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Key)
	n += 1 + l + sovCooldown(uint64(l))
	l = len(m.SubType)
	n += 1 + l + sovCooldown(uint64(l))
	n += 1 + sovCooldown(uint64(m.Val))
	n += 1 + sovCooldown(uint64(m.Ttl))
	return n
}

func (m *FetchAndIncrGeneralComplexVisitResp) Size() (n int) {
	var l int
	_ = l
	if m.ComplexVisit != nil {
		l = m.ComplexVisit.Size()
		n += 1 + l + sovCooldown(uint64(l))
	}
	return n
}

func (m *FetchGeneralComplexVisitReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Key)
	n += 1 + l + sovCooldown(uint64(l))
	return n
}

func (m *FetchGeneralComplexVisitResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ComplexVisitList) > 0 {
		for _, e := range m.ComplexVisitList {
			l = e.Size()
			n += 1 + l + sovCooldown(uint64(l))
		}
	}
	return n
}

func (m *CheckNxReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Key)
	n += 1 + l + sovCooldown(uint64(l))
	n += 1 + sovCooldown(uint64(m.Ttl))
	n += 1 + sovCooldown(uint64(m.Uid))
	l = len(m.Val)
	n += 1 + l + sovCooldown(uint64(l))
	n += 2
	return n
}

func (m *CheckNxResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	l = len(m.BeforeExistVal)
	n += 1 + l + sovCooldown(uint64(l))
	return n
}

func (m *DelTryPwdDevLimitReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCooldown(uint64(m.Uid))
	return n
}

func (m *DelTryPwdDevLimitResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func sovCooldown(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozCooldown(x uint64) (n int) {
	return sovCooldown(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *FetchVisitReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCooldown
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FetchVisitReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FetchVisitReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCooldown
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Val", wireType)
			}
			m.Val = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Val |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ttl", wireType)
			}
			m.Ttl = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ttl |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UpdateTtl", wireType)
			}
			m.UpdateTtl = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UpdateTtl |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCooldown(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCooldown
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("key")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("val")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FetchVisitRsp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCooldown
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FetchVisitRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FetchVisitRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OldVal", wireType)
			}
			m.OldVal = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OldVal |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCooldown
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCooldown(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCooldown
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("old_val")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FetchAndAddGeneralVisitReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCooldown
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FetchAndAddGeneralVisitReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FetchAndAddGeneralVisitReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VisitReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCooldown
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.VisitReq == nil {
				m.VisitReq = &FetchVisitReq{}
			}
			if err := m.VisitReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipCooldown(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCooldown
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("visit_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FetchAndAddGeneralVisitResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCooldown
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FetchAndAddGeneralVisitResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FetchAndAddGeneralVisitResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VisitResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCooldown
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.VisitResp == nil {
				m.VisitResp = &FetchVisitRsp{}
			}
			if err := m.VisitResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCooldown(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCooldown
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FetchAndAddPeriodVisitReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCooldown
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FetchAndAddPeriodVisitReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FetchAndAddPeriodVisitReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VisitReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCooldown
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.VisitReq == nil {
				m.VisitReq = &FetchVisitReq{}
			}
			if err := m.VisitReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipCooldown(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCooldown
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("visit_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FetchAndAddPeriodVisitResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCooldown
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FetchAndAddPeriodVisitResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FetchAndAddPeriodVisitResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VisitResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCooldown
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.VisitResp == nil {
				m.VisitResp = &FetchVisitRsp{}
			}
			if err := m.VisitResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCooldown(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCooldown
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FetchAndSubGeneralVisitReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCooldown
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FetchAndSubGeneralVisitReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FetchAndSubGeneralVisitReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VisitReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCooldown
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.VisitReq == nil {
				m.VisitReq = &FetchVisitReq{}
			}
			if err := m.VisitReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipCooldown(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCooldown
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("visit_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FetchAndSubGeneralVisitResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCooldown
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FetchAndSubGeneralVisitResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FetchAndSubGeneralVisitResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VisitResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCooldown
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.VisitResp == nil {
				m.VisitResp = &FetchVisitRsp{}
			}
			if err := m.VisitResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCooldown(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCooldown
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FetchAndSubPeriodVisitReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCooldown
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FetchAndSubPeriodVisitReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FetchAndSubPeriodVisitReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VisitReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCooldown
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.VisitReq == nil {
				m.VisitReq = &FetchVisitReq{}
			}
			if err := m.VisitReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipCooldown(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCooldown
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("visit_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FetchAndSubPeriodVisitResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCooldown
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FetchAndSubPeriodVisitResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FetchAndSubPeriodVisitResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VisitResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCooldown
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.VisitResp == nil {
				m.VisitResp = &FetchVisitRsp{}
			}
			if err := m.VisitResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCooldown(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCooldown
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FetchGeneralVisitReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCooldown
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FetchGeneralVisitReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FetchGeneralVisitReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VisitReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCooldown
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.VisitReq == nil {
				m.VisitReq = &FetchVisitReq{}
			}
			if err := m.VisitReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RetTtl", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.RetTtl = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipCooldown(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCooldown
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("visit_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FetchGeneralVisitResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCooldown
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FetchGeneralVisitResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FetchGeneralVisitResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VisitResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCooldown
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.VisitResp == nil {
				m.VisitResp = &FetchVisitRsp{}
			}
			if err := m.VisitResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ttl", wireType)
			}
			m.Ttl = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ttl |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCooldown(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCooldown
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ClearGeneralVisitReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCooldown
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ClearGeneralVisitReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ClearGeneralVisitReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCooldown
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipCooldown(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCooldown
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("key")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ClearGeneralVisitResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCooldown
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ClearGeneralVisitResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ClearGeneralVisitResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipCooldown(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCooldown
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FetchComplexVisit) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCooldown
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FetchComplexVisit: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FetchComplexVisit: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SubType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCooldown
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SubType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OldVal", wireType)
			}
			m.OldVal = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OldVal |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipCooldown(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCooldown
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("sub_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("old_val")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FetchAndIncrGeneralComplexVisitReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCooldown
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FetchAndIncrGeneralComplexVisitReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FetchAndIncrGeneralComplexVisitReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCooldown
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SubType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCooldown
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SubType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Val", wireType)
			}
			m.Val = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Val |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ttl", wireType)
			}
			m.Ttl = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ttl |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCooldown(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCooldown
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("key")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FetchAndIncrGeneralComplexVisitResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCooldown
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FetchAndIncrGeneralComplexVisitResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FetchAndIncrGeneralComplexVisitResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ComplexVisit", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCooldown
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ComplexVisit == nil {
				m.ComplexVisit = &FetchComplexVisit{}
			}
			if err := m.ComplexVisit.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCooldown(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCooldown
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FetchGeneralComplexVisitReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCooldown
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FetchGeneralComplexVisitReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FetchGeneralComplexVisitReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCooldown
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipCooldown(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCooldown
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("key")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FetchGeneralComplexVisitResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCooldown
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FetchGeneralComplexVisitResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FetchGeneralComplexVisitResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ComplexVisitList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCooldown
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ComplexVisitList = append(m.ComplexVisitList, &FetchComplexVisit{})
			if err := m.ComplexVisitList[len(m.ComplexVisitList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCooldown(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCooldown
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckNxReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCooldown
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckNxReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckNxReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCooldown
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ttl", wireType)
			}
			m.Ttl = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ttl |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Val", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCooldown
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Val = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RetBeforeVal", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.RetBeforeVal = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipCooldown(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCooldown
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("key")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ttl")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckNxResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCooldown
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckNxResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckNxResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsExistBefore", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsExistBefore = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeforeExistVal", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCooldown
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BeforeExistVal = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCooldown(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCooldown
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_exist_before")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelTryPwdDevLimitReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCooldown
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelTryPwdDevLimitReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelTryPwdDevLimitReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipCooldown(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCooldown
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelTryPwdDevLimitResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCooldown
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelTryPwdDevLimitResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelTryPwdDevLimitResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipCooldown(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCooldown
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipCooldown(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowCooldown
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowCooldown
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthCooldown
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowCooldown
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipCooldown(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthCooldown = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowCooldown   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/cooldownsvr/cooldown.proto", fileDescriptorCooldown) }

var fileDescriptorCooldown = []byte{
	// 1090 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x96, 0x41, 0x6f, 0x1b, 0x45,
	0x14, 0xc7, 0xb3, 0xbb, 0x4e, 0x62, 0xbf, 0x34, 0x6d, 0x32, 0x24, 0x8d, 0xeb, 0x52, 0x67, 0x35,
	0x49, 0xab, 0x08, 0xd5, 0x89, 0x88, 0x80, 0x43, 0xe4, 0x06, 0x9a, 0x94, 0x42, 0xa5, 0x0a, 0x15,
	0x27, 0xf4, 0x02, 0xc8, 0xd8, 0xbb, 0x43, 0xb3, 0xda, 0x8d, 0x77, 0xba, 0xb3, 0xeb, 0xc6, 0x12,
	0x07, 0x2e, 0x48, 0x08, 0x09, 0x09, 0x71, 0x40, 0x88, 0x73, 0x24, 0x0e, 0x7c, 0x03, 0x3e, 0x00,
	0xea, 0x91, 0x1b, 0x37, 0x84, 0xc2, 0x25, 0x1f, 0x03, 0xbd, 0x5d, 0xef, 0x66, 0x76, 0xbd, 0xb6,
	0x1b, 0x92, 0x9b, 0x3d, 0x33, 0xef, 0xbd, 0xdf, 0xfc, 0xdf, 0xbc, 0xf7, 0x16, 0xaa, 0xc2, 0x33,
	0x36, 0x0c, 0xd7, 0x75, 0x4c, 0xf7, 0x45, 0x47, 0x74, 0xbd, 0xe4, 0xf7, 0x3a, 0xf7, 0x5c, 0xdf,
	0x25, 0xc5, 0xf8, 0x7f, 0x65, 0xd5, 0x70, 0x0f, 0x0f, 0xdd, 0xce, 0x86, 0xef, 0x74, 0xb9, 0x65,
	0xd8, 0x0e, 0xdb, 0x10, 0x76, 0x3b, 0xb0, 0x1c, 0xdf, 0xea, 0xf8, 0x3d, 0xce, 0xa2, 0xf3, 0xf4,
	0x27, 0x05, 0x66, 0x1f, 0x32, 0xdf, 0x38, 0x78, 0x6a, 0x09, 0xcb, 0x6f, 0xb0, 0xe7, 0xe4, 0x3a,
	0x68, 0x36, 0xeb, 0x95, 0x15, 0x5d, 0x5d, 0x2b, 0xed, 0x14, 0x5e, 0xfe, 0xbd, 0x3c, 0xd1, 0xc0,
	0x05, 0x52, 0x86, 0x02, 0xda, 0x95, 0x55, 0x5d, 0x5d, 0x9b, 0xed, 0x6f, 0x84, 0x2b, 0x68, 0xd1,
	0x6d, 0x39, 0x65, 0x4d, 0x57, 0xd7, 0xb4, 0xd8, 0xa2, 0xdb, 0x72, 0x70, 0xdd, 0xf7, 0x9d, 0x72,
	0x41, 0x57, 0x12, 0x03, 0x5c, 0x20, 0x2b, 0x00, 0x01, 0x37, 0x5b, 0x3e, 0x6b, 0xe2, 0xf6, 0xa4,
	0xb4, 0x5d, 0x8a, 0xd6, 0xf7, 0x7d, 0x87, 0x7e, 0x91, 0xe2, 0x12, 0x9c, 0xdc, 0x82, 0x69, 0xd7,
	0x31, 0x9b, 0x18, 0x49, 0x91, 0x22, 0x4d, 0xb9, 0x8e, 0xf9, 0x34, 0x0a, 0x86, 0xd8, 0xaa, 0xae,
	0xe4, 0x63, 0x6b, 0x52, 0x98, 0x70, 0x85, 0x36, 0xa0, 0x12, 0x46, 0xb8, 0xdf, 0x31, 0xef, 0x9b,
	0xe6, 0x07, 0xac, 0xc3, 0xbc, 0x96, 0x93, 0xc8, 0xf0, 0x16, 0x94, 0xba, 0xf8, 0xbb, 0xe9, 0xb1,
	0xe7, 0x61, 0xc0, 0x99, 0xcd, 0xa5, 0xf5, 0x44, 0xec, 0x94, 0x64, 0x8d, 0x62, 0xb7, 0xff, 0x8b,
	0x7e, 0x02, 0x37, 0x87, 0xfa, 0x14, 0x9c, 0xbc, 0x03, 0x10, 0x3b, 0x15, 0xbc, 0xac, 0xe8, 0xca,
	0x50, 0xaf, 0x82, 0x37, 0x4a, 0xdd, 0xd8, 0x8e, 0x7e, 0x0c, 0x37, 0x24, 0xb7, 0x4f, 0x98, 0x67,
	0xb9, 0xe6, 0x05, 0x49, 0xf7, 0x53, 0xb7, 0x4f, 0xb9, 0xbc, 0x00, 0xa8, 0xa4, 0xe9, 0x5e, 0xd0,
	0xbe, 0x74, 0x4d, 0x07, 0x7c, 0x5e, 0x8e, 0xa6, 0x7b, 0x41, 0xfb, 0xb2, 0x35, 0xcd, 0xba, 0xbc,
	0x00, 0xa8, 0x0d, 0x0b, 0xe1, 0xde, 0xa5, 0xa8, 0x89, 0x65, 0xe4, 0x31, 0x3f, 0xac, 0x3c, 0xac,
	0x95, 0x62, 0x5c, 0x46, 0x1e, 0xf3, 0xb1, 0xec, 0x9e, 0xc1, 0x62, 0x4e, 0xb0, 0xff, 0x4f, 0x1f,
	0x37, 0x01, 0x35, 0xd3, 0x04, 0xe8, 0x87, 0xb0, 0xb0, 0xeb, 0xb0, 0x96, 0x97, 0xbd, 0xd5, 0xb9,
	0xdb, 0x0f, 0x5d, 0x82, 0xc5, 0x1c, 0x4f, 0x82, 0xd3, 0x3d, 0x98, 0x0f, 0xb1, 0x76, 0xdd, 0x43,
	0xee, 0xb0, 0xa3, 0x70, 0x83, 0x2c, 0x43, 0x51, 0x04, 0xed, 0x66, 0xe8, 0x4b, 0x0e, 0x32, 0x2d,
	0x82, 0xf6, 0x3e, 0x76, 0x33, 0xa9, 0xcf, 0xa8, 0x83, 0x7d, 0x86, 0x7e, 0xaf, 0x00, 0x8d, 0x93,
	0xfc, 0xa8, 0x63, 0xc4, 0x51, 0xe5, 0x18, 0xa3, 0xae, 0x21, 0x87, 0x97, 0x7b, 0x55, 0x12, 0x3e,
	0x69, 0xa6, 0xca, 0xd0, 0x66, 0x3a, 0x29, 0xeb, 0xf8, 0x0c, 0x56, 0xc6, 0xe2, 0x08, 0x4e, 0xde,
	0x83, 0x59, 0x23, 0x5a, 0x6b, 0x86, 0xb9, 0xe9, 0x67, 0xf0, 0x66, 0x26, 0x83, 0x29, 0xbb, 0x2b,
	0x86, 0xf4, 0x8f, 0xbe, 0xdd, 0x2f, 0xc3, 0xf3, 0x5d, 0x98, 0x5a, 0xf0, 0xfa, 0x70, 0x33, 0xc1,
	0xc9, 0x23, 0x20, 0x29, 0xb0, 0xa6, 0x63, 0x09, 0xa4, 0xd3, 0xc6, 0xd1, 0xcd, 0xc9, 0x74, 0x8f,
	0x2d, 0xe1, 0xd3, 0x9f, 0x15, 0x80, 0xdd, 0x03, 0x66, 0xd8, 0x1f, 0x1d, 0x8d, 0x4a, 0x41, 0xf2,
	0x22, 0xd5, 0x94, 0x92, 0xb8, 0x1e, 0x58, 0x66, 0x38, 0xc6, 0x92, 0x97, 0x1a, 0x58, 0x66, 0x9c,
	0x91, 0x82, 0x3c, 0x59, 0x30, 0x23, 0x6f, 0xc0, 0x55, 0xac, 0xa4, 0x36, 0xfb, 0xd2, 0xf5, 0x58,
	0xf8, 0x5e, 0x26, 0xa5, 0x82, 0xba, 0xe2, 0x31, 0x7f, 0x27, 0xdc, 0xc2, 0x57, 0x63, 0xc3, 0x4c,
	0x42, 0x26, 0x38, 0xb9, 0x0b, 0xd7, 0x2c, 0xd1, 0x64, 0x47, 0x96, 0x88, 0xed, 0x43, 0xcc, 0xd8,
	0x76, 0xd6, 0x12, 0xef, 0xe3, 0x5e, 0x64, 0x4f, 0xd6, 0x61, 0xae, 0x1f, 0x24, 0xb2, 0x88, 0x9e,
	0xe6, 0x19, 0xcd, 0xd5, 0x68, 0x37, 0x34, 0xc1, 0x60, 0xeb, 0xb0, 0xf0, 0x80, 0x39, 0xfb, 0x5e,
	0xef, 0xc9, 0x0b, 0xf3, 0x01, 0xeb, 0x3e, 0xb6, 0x0e, 0x93, 0x14, 0xe1, 0x05, 0x95, 0xcc, 0x05,
	0xb1, 0x80, 0x72, 0xce, 0x0b, 0xbe, 0xf9, 0xfb, 0x0c, 0x14, 0x77, 0xfb, 0x19, 0x20, 0xbf, 0x2a,
	0xb0, 0x34, 0x64, 0xb6, 0x91, 0xd5, 0x4c, 0xa2, 0x72, 0x47, 0x6a, 0xe5, 0xf6, 0x2b, 0x9c, 0x12,
	0x9c, 0xbe, 0xfb, 0xf5, 0xf1, 0xa9, 0xa6, 0x7c, 0x77, 0x7c, 0xaa, 0x15, 0xed, 0x2d, 0x7f, 0xab,
	0xb5, 0xe5, 0x6c, 0xfd, 0x78, 0x7c, 0xaa, 0xad, 0xd5, 0x6c, 0xbd, 0x6e, 0xb3, 0xde, 0xb6, 0x5e,
	0xf3, 0xf5, 0x3a, 0x16, 0xd0, 0xb6, 0x5e, 0x6b, 0xe9, 0xf5, 0x6e, 0xcb, 0x09, 0xd8, 0xb6, 0xfe,
	0x69, 0xcd, 0xd1, 0xeb, 0xbe, 0xef, 0x6c, 0x7f, 0x4e, 0x7e, 0x91, 0x48, 0x33, 0x13, 0x23, 0x8f,
	0x74, 0x70, 0x50, 0xe5, 0x91, 0xe6, 0x8c, 0x1e, 0xba, 0x89, 0xa4, 0x2a, 0x92, 0x4e, 0x45, 0xa4,
	0xc8, 0x79, 0x6b, 0x24, 0x27, 0xf9, 0x4d, 0x81, 0xeb, 0xf9, 0x83, 0x97, 0xac, 0xe4, 0xea, 0x93,
	0x9e, 0x4c, 0x95, 0xd5, 0xf1, 0x87, 0x04, 0xa7, 0x0f, 0x91, 0x4c, 0x93, 0x34, 0xe4, 0x21, 0xdb,
	0xc6, 0x68, 0x0d, 0x6b, 0x5c, 0xaf, 0xbf, 0x79, 0xcf, 0x6c, 0xf5, 0x36, 0x36, 0xef, 0x1d, 0xb8,
	0x81, 0x97, 0xa6, 0x4d, 0x8f, 0xb4, 0x3c, 0xda, 0x81, 0x39, 0x5a, 0x59, 0x1d, 0x7f, 0x28, 0xa6,
	0x2d, 0x5c, 0x9c, 0xf6, 0xab, 0x7e, 0xc3, 0x4f, 0x65, 0xbc, 0x9a, 0x41, 0xc8, 0xe6, 0x7a, 0x79,
	0xe4, 0xbe, 0xe0, 0xf4, 0x0e, 0xd2, 0x4d, 0x22, 0x5d, 0x01, 0xe9, 0x90, 0xec, 0xb5, 0x1c, 0x32,
	0xf2, 0x87, 0x02, 0xcb, 0x63, 0x5a, 0x31, 0xb9, 0x3b, 0xa8, 0xc7, 0xf0, 0x21, 0x52, 0xa9, 0x9d,
	0xe3, 0x74, 0x2c, 0xe3, 0x94, 0x24, 0x23, 0xcb, 0x91, 0x31, 0x9e, 0x3e, 0x91, 0x94, 0x56, 0xc7,
	0xf0, 0xf4, 0x58, 0x4f, 0x16, 0x15, 0x10, 0xf9, 0x46, 0x81, 0xf2, 0xb0, 0x9e, 0x4d, 0x6e, 0xe7,
	0xcb, 0x95, 0x45, 0xbf, 0xf3, 0x2a, 0xc7, 0x04, 0xa7, 0x15, 0x64, 0x9e, 0x46, 0x66, 0xd5, 0x0e,
	0x69, 0x4b, 0x09, 0x2d, 0xf9, 0x0c, 0xa6, 0xfb, 0x4d, 0x93, 0x2c, 0x9c, 0xb9, 0x3b, 0xeb, 0xf0,
	0x95, 0xc5, 0x9c, 0x55, 0xc1, 0xe9, 0x2a, 0xfa, 0x2c, 0xf6, 0x13, 0x16, 0x69, 0x30, 0x7f, 0xa6,
	0x41, 0x7c, 0x4b, 0x06, 0xf3, 0x03, 0x9f, 0x0d, 0xf2, 0x63, 0xc9, 0xfb, 0x3a, 0x91, 0x1f, 0x4b,
	0xfe, 0x37, 0xc7, 0x35, 0x8c, 0x5d, 0xc2, 0xd8, 0x13, 0x18, 0x77, 0x82, 0xb8, 0x30, 0x3f, 0xd0,
	0x5c, 0xe5, 0x30, 0x79, 0x9d, 0x5a, 0x0e, 0x93, 0xdb, 0x99, 0xe9, 0x0d, 0x0c, 0x03, 0xa1, 0x6c,
	0x41, 0x78, 0xc1, 0x62, 0x2d, 0xd0, 0xeb, 0x81, 0x65, 0x6e, 0x57, 0xa6, 0xbe, 0x3d, 0x3e, 0xd5,
	0xfe, 0x0a, 0x76, 0xe6, 0x5e, 0x9e, 0x54, 0x95, 0x3f, 0x4f, 0xaa, 0xca, 0x3f, 0x27, 0x55, 0xe5,
	0x87, 0x7f, 0xab, 0x13, 0xff, 0x05, 0x00, 0x00, 0xff, 0xff, 0xcf, 0xd9, 0x77, 0xdd, 0x3c, 0x0e,
	0x00, 0x00,
}
