// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-listening-auto-play/channel-listening-auto-play.proto

package channellisteningautoplay // import "golang.52tt.com/protocol/services/channellisteningautoplay"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ChannelPlayMode int32

const (
	ChannelPlayMode_chooseMode ChannelPlayMode = 0
	ChannelPlayMode_AutoMode   ChannelPlayMode = 1
	ChannelPlayMode_OrderMode  ChannelPlayMode = 2
)

var ChannelPlayMode_name = map[int32]string{
	0: "chooseMode",
	1: "AutoMode",
	2: "OrderMode",
}
var ChannelPlayMode_value = map[string]int32{
	"chooseMode": 0,
	"AutoMode":   1,
	"OrderMode":  2,
}

func (x ChannelPlayMode) String() string {
	return proto.EnumName(ChannelPlayMode_name, int32(x))
}
func (ChannelPlayMode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{0}
}

type AutoPlaySwitch int32

const (
	AutoPlaySwitch_Close AutoPlaySwitch = 0
	AutoPlaySwitch_Open  AutoPlaySwitch = 1
)

var AutoPlaySwitch_name = map[int32]string{
	0: "Close",
	1: "Open",
}
var AutoPlaySwitch_value = map[string]int32{
	"Close": 0,
	"Open":  1,
}

func (x AutoPlaySwitch) String() string {
	return proto.EnumName(AutoPlaySwitch_name, int32(x))
}
func (AutoPlaySwitch) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{1}
}

type PlayStatus int32

const (
	PlayStatus_play  PlayStatus = 0
	PlayStatus_pause PlayStatus = 1
)

var PlayStatus_name = map[int32]string{
	0: "play",
	1: "pause",
}
var PlayStatus_value = map[string]int32{
	"play":  0,
	"pause": 1,
}

func (x PlayStatus) String() string {
	return proto.EnumName(PlayStatus_name, int32(x))
}
func (PlayStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{2}
}

type WYYVipOpenDetailDto int32

const (
	WYYVipOpenDetailDto_ENUM_VIP_TYPE_NORMAL     WYYVipOpenDetailDto = 0
	WYYVipOpenDetailDto_ENUM_VIP_TYPE_BLACK_VIP  WYYVipOpenDetailDto = 6
	WYYVipOpenDetailDto_ENUM_VIP_TYPE_BLACK_SVIP WYYVipOpenDetailDto = 15
	WYYVipOpenDetailDto_ENUM_VIP_TYPE_CAR        WYYVipOpenDetailDto = 13
	WYYVipOpenDetailDto_ENUM_VIP_TYPE_WATCH      WYYVipOpenDetailDto = 16
	WYYVipOpenDetailDto_ENUM_VIP_TYPE_TV         WYYVipOpenDetailDto = 17
	WYYVipOpenDetailDto_ENUM_VIP_TYPE_RADIO      WYYVipOpenDetailDto = 18
)

var WYYVipOpenDetailDto_name = map[int32]string{
	0:  "ENUM_VIP_TYPE_NORMAL",
	6:  "ENUM_VIP_TYPE_BLACK_VIP",
	15: "ENUM_VIP_TYPE_BLACK_SVIP",
	13: "ENUM_VIP_TYPE_CAR",
	16: "ENUM_VIP_TYPE_WATCH",
	17: "ENUM_VIP_TYPE_TV",
	18: "ENUM_VIP_TYPE_RADIO",
}
var WYYVipOpenDetailDto_value = map[string]int32{
	"ENUM_VIP_TYPE_NORMAL":     0,
	"ENUM_VIP_TYPE_BLACK_VIP":  6,
	"ENUM_VIP_TYPE_BLACK_SVIP": 15,
	"ENUM_VIP_TYPE_CAR":        13,
	"ENUM_VIP_TYPE_WATCH":      16,
	"ENUM_VIP_TYPE_TV":         17,
	"ENUM_VIP_TYPE_RADIO":      18,
}

func (x WYYVipOpenDetailDto) String() string {
	return proto.EnumName(WYYVipOpenDetailDto_name, int32(x))
}
func (WYYVipOpenDetailDto) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{3}
}

// 战歌列表类型
type MusicListType int32

const (
	MusicListType_MusicListType_Default MusicListType = 0
	MusicListType_MusicListType_NC      MusicListType = 1
)

var MusicListType_name = map[int32]string{
	0: "MusicListType_Default",
	1: "MusicListType_NC",
}
var MusicListType_value = map[string]int32{
	"MusicListType_Default": 0,
	"MusicListType_NC":      1,
}

func (x MusicListType) String() string {
	return proto.EnumName(MusicListType_name, int32(x))
}
func (MusicListType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{4}
}

type SongListType int32

const (
	SongListType_ENUM_SONG_LIST_TYPE_LIKE SongListType = 0
	SongListType_ENUM_SONG_LIST_TYPE_RCMD SongListType = 1
	SongListType_ENUM_SONG_LIST_TYPE_TOP  SongListType = 2
	SongListType_ENUM_SONG_LIST_TYPE_FM   SongListType = 3
)

var SongListType_name = map[int32]string{
	0: "ENUM_SONG_LIST_TYPE_LIKE",
	1: "ENUM_SONG_LIST_TYPE_RCMD",
	2: "ENUM_SONG_LIST_TYPE_TOP",
	3: "ENUM_SONG_LIST_TYPE_FM",
}
var SongListType_value = map[string]int32{
	"ENUM_SONG_LIST_TYPE_LIKE": 0,
	"ENUM_SONG_LIST_TYPE_RCMD": 1,
	"ENUM_SONG_LIST_TYPE_TOP":  2,
	"ENUM_SONG_LIST_TYPE_FM":   3,
}

func (x SongListType) String() string {
	return proto.EnumName(SongListType_name, int32(x))
}
func (SongListType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{5}
}

type ReportChannelAutoSongProgressReq_ProgressType int32

const (
	ReportChannelAutoSongProgressReq_start ReportChannelAutoSongProgressReq_ProgressType = 0
	ReportChannelAutoSongProgressReq_end   ReportChannelAutoSongProgressReq_ProgressType = 1
)

var ReportChannelAutoSongProgressReq_ProgressType_name = map[int32]string{
	0: "start",
	1: "end",
}
var ReportChannelAutoSongProgressReq_ProgressType_value = map[string]int32{
	"start": 0,
	"end":   1,
}

func (x ReportChannelAutoSongProgressReq_ProgressType) String() string {
	return proto.EnumName(ReportChannelAutoSongProgressReq_ProgressType_name, int32(x))
}
func (ReportChannelAutoSongProgressReq_ProgressType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{20, 0}
}

type RecycleMenuReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecycleMenuReq) Reset()         { *m = RecycleMenuReq{} }
func (m *RecycleMenuReq) String() string { return proto.CompactTextString(m) }
func (*RecycleMenuReq) ProtoMessage()    {}
func (*RecycleMenuReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{0}
}
func (m *RecycleMenuReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecycleMenuReq.Unmarshal(m, b)
}
func (m *RecycleMenuReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecycleMenuReq.Marshal(b, m, deterministic)
}
func (dst *RecycleMenuReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecycleMenuReq.Merge(dst, src)
}
func (m *RecycleMenuReq) XXX_Size() int {
	return xxx_messageInfo_RecycleMenuReq.Size(m)
}
func (m *RecycleMenuReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RecycleMenuReq.DiscardUnknown(m)
}

var xxx_messageInfo_RecycleMenuReq proto.InternalMessageInfo

type RecycleMenuResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecycleMenuResp) Reset()         { *m = RecycleMenuResp{} }
func (m *RecycleMenuResp) String() string { return proto.CompactTextString(m) }
func (*RecycleMenuResp) ProtoMessage()    {}
func (*RecycleMenuResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{1}
}
func (m *RecycleMenuResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecycleMenuResp.Unmarshal(m, b)
}
func (m *RecycleMenuResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecycleMenuResp.Marshal(b, m, deterministic)
}
func (dst *RecycleMenuResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecycleMenuResp.Merge(dst, src)
}
func (m *RecycleMenuResp) XXX_Size() int {
	return xxx_messageInfo_RecycleMenuResp.Size(m)
}
func (m *RecycleMenuResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RecycleMenuResp.DiscardUnknown(m)
}

var xxx_messageInfo_RecycleMenuResp proto.InternalMessageInfo

type RecyclePlayInfoReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecyclePlayInfoReq) Reset()         { *m = RecyclePlayInfoReq{} }
func (m *RecyclePlayInfoReq) String() string { return proto.CompactTextString(m) }
func (*RecyclePlayInfoReq) ProtoMessage()    {}
func (*RecyclePlayInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{2}
}
func (m *RecyclePlayInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecyclePlayInfoReq.Unmarshal(m, b)
}
func (m *RecyclePlayInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecyclePlayInfoReq.Marshal(b, m, deterministic)
}
func (dst *RecyclePlayInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecyclePlayInfoReq.Merge(dst, src)
}
func (m *RecyclePlayInfoReq) XXX_Size() int {
	return xxx_messageInfo_RecyclePlayInfoReq.Size(m)
}
func (m *RecyclePlayInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RecyclePlayInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_RecyclePlayInfoReq proto.InternalMessageInfo

type RecyclePlayInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecyclePlayInfoResp) Reset()         { *m = RecyclePlayInfoResp{} }
func (m *RecyclePlayInfoResp) String() string { return proto.CompactTextString(m) }
func (*RecyclePlayInfoResp) ProtoMessage()    {}
func (*RecyclePlayInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{3}
}
func (m *RecyclePlayInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecyclePlayInfoResp.Unmarshal(m, b)
}
func (m *RecyclePlayInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecyclePlayInfoResp.Marshal(b, m, deterministic)
}
func (dst *RecyclePlayInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecyclePlayInfoResp.Merge(dst, src)
}
func (m *RecyclePlayInfoResp) XXX_Size() int {
	return xxx_messageInfo_RecyclePlayInfoResp.Size(m)
}
func (m *RecyclePlayInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RecyclePlayInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_RecyclePlayInfoResp proto.InternalMessageInfo

type BatchChannelPlayingMusicReq struct {
	ChannelIds           []uint32 `protobuf:"varint,1,rep,packed,name=channel_ids,json=channelIds,proto3" json:"channel_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchChannelPlayingMusicReq) Reset()         { *m = BatchChannelPlayingMusicReq{} }
func (m *BatchChannelPlayingMusicReq) String() string { return proto.CompactTextString(m) }
func (*BatchChannelPlayingMusicReq) ProtoMessage()    {}
func (*BatchChannelPlayingMusicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{4}
}
func (m *BatchChannelPlayingMusicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchChannelPlayingMusicReq.Unmarshal(m, b)
}
func (m *BatchChannelPlayingMusicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchChannelPlayingMusicReq.Marshal(b, m, deterministic)
}
func (dst *BatchChannelPlayingMusicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchChannelPlayingMusicReq.Merge(dst, src)
}
func (m *BatchChannelPlayingMusicReq) XXX_Size() int {
	return xxx_messageInfo_BatchChannelPlayingMusicReq.Size(m)
}
func (m *BatchChannelPlayingMusicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchChannelPlayingMusicReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchChannelPlayingMusicReq proto.InternalMessageInfo

func (m *BatchChannelPlayingMusicReq) GetChannelIds() []uint32 {
	if m != nil {
		return m.ChannelIds
	}
	return nil
}

type BatchChannelPlayingMusicResp struct {
	ChannelMusics        map[uint32]*ChannelPlayingMusic `protobuf:"bytes,1,rep,name=channel_musics,json=channelMusics,proto3" json:"channel_musics,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *BatchChannelPlayingMusicResp) Reset()         { *m = BatchChannelPlayingMusicResp{} }
func (m *BatchChannelPlayingMusicResp) String() string { return proto.CompactTextString(m) }
func (*BatchChannelPlayingMusicResp) ProtoMessage()    {}
func (*BatchChannelPlayingMusicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{5}
}
func (m *BatchChannelPlayingMusicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchChannelPlayingMusicResp.Unmarshal(m, b)
}
func (m *BatchChannelPlayingMusicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchChannelPlayingMusicResp.Marshal(b, m, deterministic)
}
func (dst *BatchChannelPlayingMusicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchChannelPlayingMusicResp.Merge(dst, src)
}
func (m *BatchChannelPlayingMusicResp) XXX_Size() int {
	return xxx_messageInfo_BatchChannelPlayingMusicResp.Size(m)
}
func (m *BatchChannelPlayingMusicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchChannelPlayingMusicResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchChannelPlayingMusicResp proto.InternalMessageInfo

func (m *BatchChannelPlayingMusicResp) GetChannelMusics() map[uint32]*ChannelPlayingMusic {
	if m != nil {
		return m.ChannelMusics
	}
	return nil
}

type ChannelPlayingMusic struct {
	Name                 string     `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Author               string     `protobuf:"bytes,2,opt,name=author,proto3" json:"author,omitempty"`
	Status               PlayStatus `protobuf:"varint,3,opt,name=status,proto3,enum=channellisteningautoplay.PlayStatus" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *ChannelPlayingMusic) Reset()         { *m = ChannelPlayingMusic{} }
func (m *ChannelPlayingMusic) String() string { return proto.CompactTextString(m) }
func (*ChannelPlayingMusic) ProtoMessage()    {}
func (*ChannelPlayingMusic) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{6}
}
func (m *ChannelPlayingMusic) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelPlayingMusic.Unmarshal(m, b)
}
func (m *ChannelPlayingMusic) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelPlayingMusic.Marshal(b, m, deterministic)
}
func (dst *ChannelPlayingMusic) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelPlayingMusic.Merge(dst, src)
}
func (m *ChannelPlayingMusic) XXX_Size() int {
	return xxx_messageInfo_ChannelPlayingMusic.Size(m)
}
func (m *ChannelPlayingMusic) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelPlayingMusic.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelPlayingMusic proto.InternalMessageInfo

func (m *ChannelPlayingMusic) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ChannelPlayingMusic) GetAuthor() string {
	if m != nil {
		return m.Author
	}
	return ""
}

func (m *ChannelPlayingMusic) GetStatus() PlayStatus {
	if m != nil {
		return m.Status
	}
	return PlayStatus_play
}

// 用户进房获取播放状态
type ChannelPlayStatusReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelPlayStatusReq) Reset()         { *m = ChannelPlayStatusReq{} }
func (m *ChannelPlayStatusReq) String() string { return proto.CompactTextString(m) }
func (*ChannelPlayStatusReq) ProtoMessage()    {}
func (*ChannelPlayStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{7}
}
func (m *ChannelPlayStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelPlayStatusReq.Unmarshal(m, b)
}
func (m *ChannelPlayStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelPlayStatusReq.Marshal(b, m, deterministic)
}
func (dst *ChannelPlayStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelPlayStatusReq.Merge(dst, src)
}
func (m *ChannelPlayStatusReq) XXX_Size() int {
	return xxx_messageInfo_ChannelPlayStatusReq.Size(m)
}
func (m *ChannelPlayStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelPlayStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelPlayStatusReq proto.InternalMessageInfo

func (m *ChannelPlayStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelPlayStatusReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ChannelPlayStatusResp struct {
	Mode                 ChannelPlayMode `protobuf:"varint,1,opt,name=mode,proto3,enum=channellisteningautoplay.ChannelPlayMode" json:"mode,omitempty"`
	Switch               AutoPlaySwitch  `protobuf:"varint,2,opt,name=switch,proto3,enum=channellisteningautoplay.AutoPlaySwitch" json:"switch,omitempty"`
	Info                 *AutoPlayInfo   `protobuf:"bytes,3,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ChannelPlayStatusResp) Reset()         { *m = ChannelPlayStatusResp{} }
func (m *ChannelPlayStatusResp) String() string { return proto.CompactTextString(m) }
func (*ChannelPlayStatusResp) ProtoMessage()    {}
func (*ChannelPlayStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{8}
}
func (m *ChannelPlayStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelPlayStatusResp.Unmarshal(m, b)
}
func (m *ChannelPlayStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelPlayStatusResp.Marshal(b, m, deterministic)
}
func (dst *ChannelPlayStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelPlayStatusResp.Merge(dst, src)
}
func (m *ChannelPlayStatusResp) XXX_Size() int {
	return xxx_messageInfo_ChannelPlayStatusResp.Size(m)
}
func (m *ChannelPlayStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelPlayStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelPlayStatusResp proto.InternalMessageInfo

func (m *ChannelPlayStatusResp) GetMode() ChannelPlayMode {
	if m != nil {
		return m.Mode
	}
	return ChannelPlayMode_chooseMode
}

func (m *ChannelPlayStatusResp) GetSwitch() AutoPlaySwitch {
	if m != nil {
		return m.Switch
	}
	return AutoPlaySwitch_Close
}

func (m *ChannelPlayStatusResp) GetInfo() *AutoPlayInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type AutoPlayInfo struct {
	Menu                 *ListeningAutoPlayRcmdMenu `protobuf:"bytes,1,opt,name=menu,proto3" json:"menu,omitempty"`
	Key                  int64                      `protobuf:"varint,2,opt,name=key,proto3" json:"key,omitempty"`
	PlayTime             int64                      `protobuf:"varint,3,opt,name=play_time,json=playTime,proto3" json:"play_time,omitempty"`
	Status               PlayStatus                 `protobuf:"varint,4,opt,name=status,proto3,enum=channellisteningautoplay.PlayStatus" json:"status,omitempty"`
	Volume               uint32                     `protobuf:"varint,5,opt,name=Volume,proto3" json:"Volume,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *AutoPlayInfo) Reset()         { *m = AutoPlayInfo{} }
func (m *AutoPlayInfo) String() string { return proto.CompactTextString(m) }
func (*AutoPlayInfo) ProtoMessage()    {}
func (*AutoPlayInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{9}
}
func (m *AutoPlayInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AutoPlayInfo.Unmarshal(m, b)
}
func (m *AutoPlayInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AutoPlayInfo.Marshal(b, m, deterministic)
}
func (dst *AutoPlayInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AutoPlayInfo.Merge(dst, src)
}
func (m *AutoPlayInfo) XXX_Size() int {
	return xxx_messageInfo_AutoPlayInfo.Size(m)
}
func (m *AutoPlayInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AutoPlayInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AutoPlayInfo proto.InternalMessageInfo

func (m *AutoPlayInfo) GetMenu() *ListeningAutoPlayRcmdMenu {
	if m != nil {
		return m.Menu
	}
	return nil
}

func (m *AutoPlayInfo) GetKey() int64 {
	if m != nil {
		return m.Key
	}
	return 0
}

func (m *AutoPlayInfo) GetPlayTime() int64 {
	if m != nil {
		return m.PlayTime
	}
	return 0
}

func (m *AutoPlayInfo) GetStatus() PlayStatus {
	if m != nil {
		return m.Status
	}
	return PlayStatus_play
}

func (m *AutoPlayInfo) GetVolume() uint32 {
	if m != nil {
		return m.Volume
	}
	return 0
}

// 与2060接口协议一致
type MusicInfo struct {
	ClientKey            string   `protobuf:"bytes,1,opt,name=client_key,json=clientKey,proto3" json:"client_key,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Author               string   `protobuf:"bytes,3,opt,name=author,proto3" json:"author,omitempty"`
	Uid                  uint32   `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	Volume               uint32   `protobuf:"varint,5,opt,name=volume,proto3" json:"volume,omitempty"`
	Key                  int64    `protobuf:"varint,6,opt,name=key,proto3" json:"key,omitempty"`
	Account              string   `protobuf:"bytes,7,opt,name=account,proto3" json:"account,omitempty"`
	IsLocal              uint32   `protobuf:"varint,8,opt,name=is_local,json=isLocal,proto3" json:"is_local,omitempty"`
	Status               uint32   `protobuf:"varint,9,opt,name=status,proto3" json:"status,omitempty"`
	Nickname             string   `protobuf:"bytes,10,opt,name=nickname,proto3" json:"nickname,omitempty"`
	MusicType            uint32   `protobuf:"varint,11,opt,name=music_type,json=musicType,proto3" json:"music_type,omitempty"`
	MusicId              string   `protobuf:"bytes,12,opt,name=music_id,json=musicId,proto3" json:"music_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MusicInfo) Reset()         { *m = MusicInfo{} }
func (m *MusicInfo) String() string { return proto.CompactTextString(m) }
func (*MusicInfo) ProtoMessage()    {}
func (*MusicInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{10}
}
func (m *MusicInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicInfo.Unmarshal(m, b)
}
func (m *MusicInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicInfo.Marshal(b, m, deterministic)
}
func (dst *MusicInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicInfo.Merge(dst, src)
}
func (m *MusicInfo) XXX_Size() int {
	return xxx_messageInfo_MusicInfo.Size(m)
}
func (m *MusicInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MusicInfo proto.InternalMessageInfo

func (m *MusicInfo) GetClientKey() string {
	if m != nil {
		return m.ClientKey
	}
	return ""
}

func (m *MusicInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MusicInfo) GetAuthor() string {
	if m != nil {
		return m.Author
	}
	return ""
}

func (m *MusicInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MusicInfo) GetVolume() uint32 {
	if m != nil {
		return m.Volume
	}
	return 0
}

func (m *MusicInfo) GetKey() int64 {
	if m != nil {
		return m.Key
	}
	return 0
}

func (m *MusicInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *MusicInfo) GetIsLocal() uint32 {
	if m != nil {
		return m.IsLocal
	}
	return 0
}

func (m *MusicInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *MusicInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *MusicInfo) GetMusicType() uint32 {
	if m != nil {
		return m.MusicType
	}
	return 0
}

func (m *MusicInfo) GetMusicId() string {
	if m != nil {
		return m.MusicId
	}
	return ""
}

// 切换自动播放/点歌模式开关
type SwitchChannelPlayReq struct {
	Uid                  uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32         `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Switch               AutoPlaySwitch `protobuf:"varint,3,opt,name=switch,proto3,enum=channellisteningautoplay.AutoPlaySwitch" json:"switch,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *SwitchChannelPlayReq) Reset()         { *m = SwitchChannelPlayReq{} }
func (m *SwitchChannelPlayReq) String() string { return proto.CompactTextString(m) }
func (*SwitchChannelPlayReq) ProtoMessage()    {}
func (*SwitchChannelPlayReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{11}
}
func (m *SwitchChannelPlayReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchChannelPlayReq.Unmarshal(m, b)
}
func (m *SwitchChannelPlayReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchChannelPlayReq.Marshal(b, m, deterministic)
}
func (dst *SwitchChannelPlayReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchChannelPlayReq.Merge(dst, src)
}
func (m *SwitchChannelPlayReq) XXX_Size() int {
	return xxx_messageInfo_SwitchChannelPlayReq.Size(m)
}
func (m *SwitchChannelPlayReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchChannelPlayReq.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchChannelPlayReq proto.InternalMessageInfo

func (m *SwitchChannelPlayReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SwitchChannelPlayReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SwitchChannelPlayReq) GetSwitch() AutoPlaySwitch {
	if m != nil {
		return m.Switch
	}
	return AutoPlaySwitch_Close
}

type SwitchChannelPlayResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SwitchChannelPlayResp) Reset()         { *m = SwitchChannelPlayResp{} }
func (m *SwitchChannelPlayResp) String() string { return proto.CompactTextString(m) }
func (*SwitchChannelPlayResp) ProtoMessage()    {}
func (*SwitchChannelPlayResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{12}
}
func (m *SwitchChannelPlayResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchChannelPlayResp.Unmarshal(m, b)
}
func (m *SwitchChannelPlayResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchChannelPlayResp.Marshal(b, m, deterministic)
}
func (dst *SwitchChannelPlayResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchChannelPlayResp.Merge(dst, src)
}
func (m *SwitchChannelPlayResp) XXX_Size() int {
	return xxx_messageInfo_SwitchChannelPlayResp.Size(m)
}
func (m *SwitchChannelPlayResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchChannelPlayResp.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchChannelPlayResp proto.InternalMessageInfo

// 获取推荐歌曲歌单
type ChannelRcmdMusicMenuReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelRcmdMusicMenuReq) Reset()         { *m = ChannelRcmdMusicMenuReq{} }
func (m *ChannelRcmdMusicMenuReq) String() string { return proto.CompactTextString(m) }
func (*ChannelRcmdMusicMenuReq) ProtoMessage()    {}
func (*ChannelRcmdMusicMenuReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{13}
}
func (m *ChannelRcmdMusicMenuReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelRcmdMusicMenuReq.Unmarshal(m, b)
}
func (m *ChannelRcmdMusicMenuReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelRcmdMusicMenuReq.Marshal(b, m, deterministic)
}
func (dst *ChannelRcmdMusicMenuReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelRcmdMusicMenuReq.Merge(dst, src)
}
func (m *ChannelRcmdMusicMenuReq) XXX_Size() int {
	return xxx_messageInfo_ChannelRcmdMusicMenuReq.Size(m)
}
func (m *ChannelRcmdMusicMenuReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelRcmdMusicMenuReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelRcmdMusicMenuReq proto.InternalMessageInfo

func (m *ChannelRcmdMusicMenuReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ChannelRcmdMusicMenuResp struct {
	Menu                 *ListeningAutoPlayRcmdMenu `protobuf:"bytes,1,opt,name=menu,proto3" json:"menu,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *ChannelRcmdMusicMenuResp) Reset()         { *m = ChannelRcmdMusicMenuResp{} }
func (m *ChannelRcmdMusicMenuResp) String() string { return proto.CompactTextString(m) }
func (*ChannelRcmdMusicMenuResp) ProtoMessage()    {}
func (*ChannelRcmdMusicMenuResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{14}
}
func (m *ChannelRcmdMusicMenuResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelRcmdMusicMenuResp.Unmarshal(m, b)
}
func (m *ChannelRcmdMusicMenuResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelRcmdMusicMenuResp.Marshal(b, m, deterministic)
}
func (dst *ChannelRcmdMusicMenuResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelRcmdMusicMenuResp.Merge(dst, src)
}
func (m *ChannelRcmdMusicMenuResp) XXX_Size() int {
	return xxx_messageInfo_ChannelRcmdMusicMenuResp.Size(m)
}
func (m *ChannelRcmdMusicMenuResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelRcmdMusicMenuResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelRcmdMusicMenuResp proto.InternalMessageInfo

func (m *ChannelRcmdMusicMenuResp) GetMenu() *ListeningAutoPlayRcmdMenu {
	if m != nil {
		return m.Menu
	}
	return nil
}

type ListeningAutoPlayRcmdMenu struct {
	Id                   string       `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string       `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Bg                   string       `protobuf:"bytes,3,opt,name=bg,proto3" json:"bg,omitempty"`
	Musics               []*MusicInfo `protobuf:"bytes,4,rep,name=musics,proto3" json:"musics,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ListeningAutoPlayRcmdMenu) Reset()         { *m = ListeningAutoPlayRcmdMenu{} }
func (m *ListeningAutoPlayRcmdMenu) String() string { return proto.CompactTextString(m) }
func (*ListeningAutoPlayRcmdMenu) ProtoMessage()    {}
func (*ListeningAutoPlayRcmdMenu) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{15}
}
func (m *ListeningAutoPlayRcmdMenu) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningAutoPlayRcmdMenu.Unmarshal(m, b)
}
func (m *ListeningAutoPlayRcmdMenu) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningAutoPlayRcmdMenu.Marshal(b, m, deterministic)
}
func (dst *ListeningAutoPlayRcmdMenu) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningAutoPlayRcmdMenu.Merge(dst, src)
}
func (m *ListeningAutoPlayRcmdMenu) XXX_Size() int {
	return xxx_messageInfo_ListeningAutoPlayRcmdMenu.Size(m)
}
func (m *ListeningAutoPlayRcmdMenu) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningAutoPlayRcmdMenu.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningAutoPlayRcmdMenu proto.InternalMessageInfo

func (m *ListeningAutoPlayRcmdMenu) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ListeningAutoPlayRcmdMenu) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ListeningAutoPlayRcmdMenu) GetBg() string {
	if m != nil {
		return m.Bg
	}
	return ""
}

func (m *ListeningAutoPlayRcmdMenu) GetMusics() []*MusicInfo {
	if m != nil {
		return m.Musics
	}
	return nil
}

// 设置音量
type SetVolumeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Volume               uint32   `protobuf:"varint,3,opt,name=volume,proto3" json:"volume,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetVolumeReq) Reset()         { *m = SetVolumeReq{} }
func (m *SetVolumeReq) String() string { return proto.CompactTextString(m) }
func (*SetVolumeReq) ProtoMessage()    {}
func (*SetVolumeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{16}
}
func (m *SetVolumeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetVolumeReq.Unmarshal(m, b)
}
func (m *SetVolumeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetVolumeReq.Marshal(b, m, deterministic)
}
func (dst *SetVolumeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetVolumeReq.Merge(dst, src)
}
func (m *SetVolumeReq) XXX_Size() int {
	return xxx_messageInfo_SetVolumeReq.Size(m)
}
func (m *SetVolumeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetVolumeReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetVolumeReq proto.InternalMessageInfo

func (m *SetVolumeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetVolumeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetVolumeReq) GetVolume() uint32 {
	if m != nil {
		return m.Volume
	}
	return 0
}

type SetVolumeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetVolumeResp) Reset()         { *m = SetVolumeResp{} }
func (m *SetVolumeResp) String() string { return proto.CompactTextString(m) }
func (*SetVolumeResp) ProtoMessage()    {}
func (*SetVolumeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{17}
}
func (m *SetVolumeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetVolumeResp.Unmarshal(m, b)
}
func (m *SetVolumeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetVolumeResp.Marshal(b, m, deterministic)
}
func (dst *SetVolumeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetVolumeResp.Merge(dst, src)
}
func (m *SetVolumeResp) XXX_Size() int {
	return xxx_messageInfo_SetVolumeResp.Size(m)
}
func (m *SetVolumeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetVolumeResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetVolumeResp proto.InternalMessageInfo

// 自动模式切歌
type CutAutoModeSongReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MusicMenuId          string   `protobuf:"bytes,3,opt,name=music_menu_id,json=musicMenuId,proto3" json:"music_menu_id,omitempty"`
	Key                  int64    `protobuf:"varint,4,opt,name=key,proto3" json:"key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CutAutoModeSongReq) Reset()         { *m = CutAutoModeSongReq{} }
func (m *CutAutoModeSongReq) String() string { return proto.CompactTextString(m) }
func (*CutAutoModeSongReq) ProtoMessage()    {}
func (*CutAutoModeSongReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{18}
}
func (m *CutAutoModeSongReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CutAutoModeSongReq.Unmarshal(m, b)
}
func (m *CutAutoModeSongReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CutAutoModeSongReq.Marshal(b, m, deterministic)
}
func (dst *CutAutoModeSongReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CutAutoModeSongReq.Merge(dst, src)
}
func (m *CutAutoModeSongReq) XXX_Size() int {
	return xxx_messageInfo_CutAutoModeSongReq.Size(m)
}
func (m *CutAutoModeSongReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CutAutoModeSongReq.DiscardUnknown(m)
}

var xxx_messageInfo_CutAutoModeSongReq proto.InternalMessageInfo

func (m *CutAutoModeSongReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CutAutoModeSongReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CutAutoModeSongReq) GetMusicMenuId() string {
	if m != nil {
		return m.MusicMenuId
	}
	return ""
}

func (m *CutAutoModeSongReq) GetKey() int64 {
	if m != nil {
		return m.Key
	}
	return 0
}

type CutAutoModeSongResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CutAutoModeSongResp) Reset()         { *m = CutAutoModeSongResp{} }
func (m *CutAutoModeSongResp) String() string { return proto.CompactTextString(m) }
func (*CutAutoModeSongResp) ProtoMessage()    {}
func (*CutAutoModeSongResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{19}
}
func (m *CutAutoModeSongResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CutAutoModeSongResp.Unmarshal(m, b)
}
func (m *CutAutoModeSongResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CutAutoModeSongResp.Marshal(b, m, deterministic)
}
func (dst *CutAutoModeSongResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CutAutoModeSongResp.Merge(dst, src)
}
func (m *CutAutoModeSongResp) XXX_Size() int {
	return xxx_messageInfo_CutAutoModeSongResp.Size(m)
}
func (m *CutAutoModeSongResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CutAutoModeSongResp.DiscardUnknown(m)
}

var xxx_messageInfo_CutAutoModeSongResp proto.InternalMessageInfo

// 歌曲进度上报
type ReportChannelAutoSongProgressReq struct {
	Uid                  uint32                                        `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32                                        `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MusicMenuId          string                                        `protobuf:"bytes,3,opt,name=music_menu_id,json=musicMenuId,proto3" json:"music_menu_id,omitempty"`
	Key                  int64                                         `protobuf:"varint,4,opt,name=key,proto3" json:"key,omitempty"`
	Progress             ReportChannelAutoSongProgressReq_ProgressType `protobuf:"varint,5,opt,name=progress,proto3,enum=channellisteningautoplay.ReportChannelAutoSongProgressReq_ProgressType" json:"progress,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                      `json:"-"`
	XXX_unrecognized     []byte                                        `json:"-"`
	XXX_sizecache        int32                                         `json:"-"`
}

func (m *ReportChannelAutoSongProgressReq) Reset()         { *m = ReportChannelAutoSongProgressReq{} }
func (m *ReportChannelAutoSongProgressReq) String() string { return proto.CompactTextString(m) }
func (*ReportChannelAutoSongProgressReq) ProtoMessage()    {}
func (*ReportChannelAutoSongProgressReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{20}
}
func (m *ReportChannelAutoSongProgressReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportChannelAutoSongProgressReq.Unmarshal(m, b)
}
func (m *ReportChannelAutoSongProgressReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportChannelAutoSongProgressReq.Marshal(b, m, deterministic)
}
func (dst *ReportChannelAutoSongProgressReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportChannelAutoSongProgressReq.Merge(dst, src)
}
func (m *ReportChannelAutoSongProgressReq) XXX_Size() int {
	return xxx_messageInfo_ReportChannelAutoSongProgressReq.Size(m)
}
func (m *ReportChannelAutoSongProgressReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportChannelAutoSongProgressReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportChannelAutoSongProgressReq proto.InternalMessageInfo

func (m *ReportChannelAutoSongProgressReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReportChannelAutoSongProgressReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ReportChannelAutoSongProgressReq) GetMusicMenuId() string {
	if m != nil {
		return m.MusicMenuId
	}
	return ""
}

func (m *ReportChannelAutoSongProgressReq) GetKey() int64 {
	if m != nil {
		return m.Key
	}
	return 0
}

func (m *ReportChannelAutoSongProgressReq) GetProgress() ReportChannelAutoSongProgressReq_ProgressType {
	if m != nil {
		return m.Progress
	}
	return ReportChannelAutoSongProgressReq_start
}

type ReportChannelAutoSongProgressResp struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportChannelAutoSongProgressResp) Reset()         { *m = ReportChannelAutoSongProgressResp{} }
func (m *ReportChannelAutoSongProgressResp) String() string { return proto.CompactTextString(m) }
func (*ReportChannelAutoSongProgressResp) ProtoMessage()    {}
func (*ReportChannelAutoSongProgressResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{21}
}
func (m *ReportChannelAutoSongProgressResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportChannelAutoSongProgressResp.Unmarshal(m, b)
}
func (m *ReportChannelAutoSongProgressResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportChannelAutoSongProgressResp.Marshal(b, m, deterministic)
}
func (dst *ReportChannelAutoSongProgressResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportChannelAutoSongProgressResp.Merge(dst, src)
}
func (m *ReportChannelAutoSongProgressResp) XXX_Size() int {
	return xxx_messageInfo_ReportChannelAutoSongProgressResp.Size(m)
}
func (m *ReportChannelAutoSongProgressResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportChannelAutoSongProgressResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportChannelAutoSongProgressResp proto.InternalMessageInfo

func (m *ReportChannelAutoSongProgressResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

// 切换自动播放/点歌模式
type SetChannelPlayModeReq struct {
	Uid                  uint32          `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32          `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Mode                 ChannelPlayMode `protobuf:"varint,3,opt,name=mode,proto3,enum=channellisteningautoplay.ChannelPlayMode" json:"mode,omitempty"`
	MusicMenuId          string          `protobuf:"bytes,4,opt,name=music_menu_id,json=musicMenuId,proto3" json:"music_menu_id,omitempty"`
	Key                  int64           `protobuf:"varint,5,opt,name=key,proto3" json:"key,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *SetChannelPlayModeReq) Reset()         { *m = SetChannelPlayModeReq{} }
func (m *SetChannelPlayModeReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelPlayModeReq) ProtoMessage()    {}
func (*SetChannelPlayModeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{22}
}
func (m *SetChannelPlayModeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelPlayModeReq.Unmarshal(m, b)
}
func (m *SetChannelPlayModeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelPlayModeReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelPlayModeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelPlayModeReq.Merge(dst, src)
}
func (m *SetChannelPlayModeReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelPlayModeReq.Size(m)
}
func (m *SetChannelPlayModeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelPlayModeReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelPlayModeReq proto.InternalMessageInfo

func (m *SetChannelPlayModeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetChannelPlayModeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelPlayModeReq) GetMode() ChannelPlayMode {
	if m != nil {
		return m.Mode
	}
	return ChannelPlayMode_chooseMode
}

func (m *SetChannelPlayModeReq) GetMusicMenuId() string {
	if m != nil {
		return m.MusicMenuId
	}
	return ""
}

func (m *SetChannelPlayModeReq) GetKey() int64 {
	if m != nil {
		return m.Key
	}
	return 0
}

type SetChannelPlayModeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelPlayModeResp) Reset()         { *m = SetChannelPlayModeResp{} }
func (m *SetChannelPlayModeResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelPlayModeResp) ProtoMessage()    {}
func (*SetChannelPlayModeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{23}
}
func (m *SetChannelPlayModeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelPlayModeResp.Unmarshal(m, b)
}
func (m *SetChannelPlayModeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelPlayModeResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelPlayModeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelPlayModeResp.Merge(dst, src)
}
func (m *SetChannelPlayModeResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelPlayModeResp.Size(m)
}
func (m *SetChannelPlayModeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelPlayModeResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelPlayModeResp proto.InternalMessageInfo

// 播放/暂停切换
type SetChannelPlayStatusReq struct {
	Uid                  uint32     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32     `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MusicMenuId          string     `protobuf:"bytes,3,opt,name=music_menu_id,json=musicMenuId,proto3" json:"music_menu_id,omitempty"`
	Key                  int64      `protobuf:"varint,4,opt,name=key,proto3" json:"key,omitempty"`
	Status               PlayStatus `protobuf:"varint,5,opt,name=status,proto3,enum=channellisteningautoplay.PlayStatus" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *SetChannelPlayStatusReq) Reset()         { *m = SetChannelPlayStatusReq{} }
func (m *SetChannelPlayStatusReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelPlayStatusReq) ProtoMessage()    {}
func (*SetChannelPlayStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{24}
}
func (m *SetChannelPlayStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelPlayStatusReq.Unmarshal(m, b)
}
func (m *SetChannelPlayStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelPlayStatusReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelPlayStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelPlayStatusReq.Merge(dst, src)
}
func (m *SetChannelPlayStatusReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelPlayStatusReq.Size(m)
}
func (m *SetChannelPlayStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelPlayStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelPlayStatusReq proto.InternalMessageInfo

func (m *SetChannelPlayStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetChannelPlayStatusReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelPlayStatusReq) GetMusicMenuId() string {
	if m != nil {
		return m.MusicMenuId
	}
	return ""
}

func (m *SetChannelPlayStatusReq) GetKey() int64 {
	if m != nil {
		return m.Key
	}
	return 0
}

func (m *SetChannelPlayStatusReq) GetStatus() PlayStatus {
	if m != nil {
		return m.Status
	}
	return PlayStatus_play
}

type SetChannelPlayStatusResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelPlayStatusResp) Reset()         { *m = SetChannelPlayStatusResp{} }
func (m *SetChannelPlayStatusResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelPlayStatusResp) ProtoMessage()    {}
func (*SetChannelPlayStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{25}
}
func (m *SetChannelPlayStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelPlayStatusResp.Unmarshal(m, b)
}
func (m *SetChannelPlayStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelPlayStatusResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelPlayStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelPlayStatusResp.Merge(dst, src)
}
func (m *SetChannelPlayStatusResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelPlayStatusResp.Size(m)
}
func (m *SetChannelPlayStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelPlayStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelPlayStatusResp proto.InternalMessageInfo

// 自动播放歌曲暂停/播放状态通知
type AutoSongStatusNotify struct {
	ChannelId            uint32        `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Info                 *AutoPlayInfo `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *AutoSongStatusNotify) Reset()         { *m = AutoSongStatusNotify{} }
func (m *AutoSongStatusNotify) String() string { return proto.CompactTextString(m) }
func (*AutoSongStatusNotify) ProtoMessage()    {}
func (*AutoSongStatusNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{26}
}
func (m *AutoSongStatusNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AutoSongStatusNotify.Unmarshal(m, b)
}
func (m *AutoSongStatusNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AutoSongStatusNotify.Marshal(b, m, deterministic)
}
func (dst *AutoSongStatusNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AutoSongStatusNotify.Merge(dst, src)
}
func (m *AutoSongStatusNotify) XXX_Size() int {
	return xxx_messageInfo_AutoSongStatusNotify.Size(m)
}
func (m *AutoSongStatusNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_AutoSongStatusNotify.DiscardUnknown(m)
}

var xxx_messageInfo_AutoSongStatusNotify proto.InternalMessageInfo

func (m *AutoSongStatusNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AutoSongStatusNotify) GetInfo() *AutoPlayInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

// 切换自动播放/点歌模式通知
type ChannelPlayModeNotify struct {
	ChannelId            uint32          `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Mode                 ChannelPlayMode `protobuf:"varint,2,opt,name=mode,proto3,enum=channellisteningautoplay.ChannelPlayMode" json:"mode,omitempty"`
	Info                 *AutoPlayInfo   `protobuf:"bytes,3,opt,name=info,proto3" json:"info,omitempty"`
	MusicListMode        uint32          `protobuf:"varint,4,opt,name=music_list_mode,json=musicListMode,proto3" json:"music_list_mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ChannelPlayModeNotify) Reset()         { *m = ChannelPlayModeNotify{} }
func (m *ChannelPlayModeNotify) String() string { return proto.CompactTextString(m) }
func (*ChannelPlayModeNotify) ProtoMessage()    {}
func (*ChannelPlayModeNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{27}
}
func (m *ChannelPlayModeNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelPlayModeNotify.Unmarshal(m, b)
}
func (m *ChannelPlayModeNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelPlayModeNotify.Marshal(b, m, deterministic)
}
func (dst *ChannelPlayModeNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelPlayModeNotify.Merge(dst, src)
}
func (m *ChannelPlayModeNotify) XXX_Size() int {
	return xxx_messageInfo_ChannelPlayModeNotify.Size(m)
}
func (m *ChannelPlayModeNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelPlayModeNotify.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelPlayModeNotify proto.InternalMessageInfo

func (m *ChannelPlayModeNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelPlayModeNotify) GetMode() ChannelPlayMode {
	if m != nil {
		return m.Mode
	}
	return ChannelPlayMode_chooseMode
}

func (m *ChannelPlayModeNotify) GetInfo() *AutoPlayInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *ChannelPlayModeNotify) GetMusicListMode() uint32 {
	if m != nil {
		return m.MusicListMode
	}
	return 0
}

// 音量改变通知
type ChannelVolumeNotify struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Volume               uint32   `protobuf:"varint,2,opt,name=volume,proto3" json:"volume,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelVolumeNotify) Reset()         { *m = ChannelVolumeNotify{} }
func (m *ChannelVolumeNotify) String() string { return proto.CompactTextString(m) }
func (*ChannelVolumeNotify) ProtoMessage()    {}
func (*ChannelVolumeNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{28}
}
func (m *ChannelVolumeNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelVolumeNotify.Unmarshal(m, b)
}
func (m *ChannelVolumeNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelVolumeNotify.Marshal(b, m, deterministic)
}
func (dst *ChannelVolumeNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelVolumeNotify.Merge(dst, src)
}
func (m *ChannelVolumeNotify) XXX_Size() int {
	return xxx_messageInfo_ChannelVolumeNotify.Size(m)
}
func (m *ChannelVolumeNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelVolumeNotify.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelVolumeNotify proto.InternalMessageInfo

func (m *ChannelVolumeNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelVolumeNotify) GetVolume() uint32 {
	if m != nil {
		return m.Volume
	}
	return 0
}

// 音乐V2 同channel_.proto
type MusicInfoV2 struct {
	ClientKey            string   `protobuf:"bytes,1,opt,name=client_key,json=clientKey,proto3" json:"client_key,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Author               string   `protobuf:"bytes,3,opt,name=author,proto3" json:"author,omitempty"`
	Uid                  uint32   `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	Volume               uint32   `protobuf:"varint,5,opt,name=volume,proto3" json:"volume,omitempty"`
	Key                  int64    `protobuf:"varint,6,opt,name=key,proto3" json:"key,omitempty"`
	Account              string   `protobuf:"bytes,7,opt,name=account,proto3" json:"account,omitempty"`
	IsLocal              uint32   `protobuf:"varint,8,opt,name=is_local,json=isLocal,proto3" json:"is_local,omitempty"`
	Status               uint32   `protobuf:"varint,9,opt,name=status,proto3" json:"status,omitempty"`
	Nickname             string   `protobuf:"bytes,10,opt,name=nickname,proto3" json:"nickname,omitempty"`
	MusicType            uint32   `protobuf:"varint,11,opt,name=music_type,json=musicType,proto3" json:"music_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MusicInfoV2) Reset()         { *m = MusicInfoV2{} }
func (m *MusicInfoV2) String() string { return proto.CompactTextString(m) }
func (*MusicInfoV2) ProtoMessage()    {}
func (*MusicInfoV2) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{29}
}
func (m *MusicInfoV2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicInfoV2.Unmarshal(m, b)
}
func (m *MusicInfoV2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicInfoV2.Marshal(b, m, deterministic)
}
func (dst *MusicInfoV2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicInfoV2.Merge(dst, src)
}
func (m *MusicInfoV2) XXX_Size() int {
	return xxx_messageInfo_MusicInfoV2.Size(m)
}
func (m *MusicInfoV2) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicInfoV2.DiscardUnknown(m)
}

var xxx_messageInfo_MusicInfoV2 proto.InternalMessageInfo

func (m *MusicInfoV2) GetClientKey() string {
	if m != nil {
		return m.ClientKey
	}
	return ""
}

func (m *MusicInfoV2) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MusicInfoV2) GetAuthor() string {
	if m != nil {
		return m.Author
	}
	return ""
}

func (m *MusicInfoV2) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MusicInfoV2) GetVolume() uint32 {
	if m != nil {
		return m.Volume
	}
	return 0
}

func (m *MusicInfoV2) GetKey() int64 {
	if m != nil {
		return m.Key
	}
	return 0
}

func (m *MusicInfoV2) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *MusicInfoV2) GetIsLocal() uint32 {
	if m != nil {
		return m.IsLocal
	}
	return 0
}

func (m *MusicInfoV2) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *MusicInfoV2) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *MusicInfoV2) GetMusicType() uint32 {
	if m != nil {
		return m.MusicType
	}
	return 0
}

type WYYUserInfo struct {
	Id                   string        `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Nickname             string        `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	AvatarUrl            string        `protobuf:"bytes,3,opt,name=avatar_url,json=avatarUrl,proto3" json:"avatar_url,omitempty"`
	Gender               uint32        `protobuf:"varint,4,opt,name=gender,proto3" json:"gender,omitempty"`
	Signature            string        `protobuf:"bytes,5,opt,name=signature,proto3" json:"signature,omitempty"`
	VipDetail            []*WYYVipInfo `protobuf:"bytes,6,rep,name=vip_detail,json=vipDetail,proto3" json:"vip_detail,omitempty"`
	RedVipLevel          uint32        `protobuf:"varint,7,opt,name=red_vip_level,json=redVipLevel,proto3" json:"red_vip_level,omitempty"`
	RedVipLevelImg       string        `protobuf:"bytes,8,opt,name=red_vip_level_img,json=redVipLevelImg,proto3" json:"red_vip_level_img,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *WYYUserInfo) Reset()         { *m = WYYUserInfo{} }
func (m *WYYUserInfo) String() string { return proto.CompactTextString(m) }
func (*WYYUserInfo) ProtoMessage()    {}
func (*WYYUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{30}
}
func (m *WYYUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WYYUserInfo.Unmarshal(m, b)
}
func (m *WYYUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WYYUserInfo.Marshal(b, m, deterministic)
}
func (dst *WYYUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WYYUserInfo.Merge(dst, src)
}
func (m *WYYUserInfo) XXX_Size() int {
	return xxx_messageInfo_WYYUserInfo.Size(m)
}
func (m *WYYUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WYYUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WYYUserInfo proto.InternalMessageInfo

func (m *WYYUserInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *WYYUserInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *WYYUserInfo) GetAvatarUrl() string {
	if m != nil {
		return m.AvatarUrl
	}
	return ""
}

func (m *WYYUserInfo) GetGender() uint32 {
	if m != nil {
		return m.Gender
	}
	return 0
}

func (m *WYYUserInfo) GetSignature() string {
	if m != nil {
		return m.Signature
	}
	return ""
}

func (m *WYYUserInfo) GetVipDetail() []*WYYVipInfo {
	if m != nil {
		return m.VipDetail
	}
	return nil
}

func (m *WYYUserInfo) GetRedVipLevel() uint32 {
	if m != nil {
		return m.RedVipLevel
	}
	return 0
}

func (m *WYYUserInfo) GetRedVipLevelImg() string {
	if m != nil {
		return m.RedVipLevelImg
	}
	return ""
}

type WYYVipInfo struct {
	Type                 uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	ExpireTime           int64    `protobuf:"varint,2,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WYYVipInfo) Reset()         { *m = WYYVipInfo{} }
func (m *WYYVipInfo) String() string { return proto.CompactTextString(m) }
func (*WYYVipInfo) ProtoMessage()    {}
func (*WYYVipInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{31}
}
func (m *WYYVipInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WYYVipInfo.Unmarshal(m, b)
}
func (m *WYYVipInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WYYVipInfo.Marshal(b, m, deterministic)
}
func (dst *WYYVipInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WYYVipInfo.Merge(dst, src)
}
func (m *WYYVipInfo) XXX_Size() int {
	return xxx_messageInfo_WYYVipInfo.Size(m)
}
func (m *WYYVipInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WYYVipInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WYYVipInfo proto.InternalMessageInfo

func (m *WYYVipInfo) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *WYYVipInfo) GetExpireTime() int64 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

type WYYSongArtist struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WYYSongArtist) Reset()         { *m = WYYSongArtist{} }
func (m *WYYSongArtist) String() string { return proto.CompactTextString(m) }
func (*WYYSongArtist) ProtoMessage()    {}
func (*WYYSongArtist) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{32}
}
func (m *WYYSongArtist) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WYYSongArtist.Unmarshal(m, b)
}
func (m *WYYSongArtist) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WYYSongArtist.Marshal(b, m, deterministic)
}
func (dst *WYYSongArtist) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WYYSongArtist.Merge(dst, src)
}
func (m *WYYSongArtist) XXX_Size() int {
	return xxx_messageInfo_WYYSongArtist.Size(m)
}
func (m *WYYSongArtist) XXX_DiscardUnknown() {
	xxx_messageInfo_WYYSongArtist.DiscardUnknown(m)
}

var xxx_messageInfo_WYYSongArtist proto.InternalMessageInfo

func (m *WYYSongArtist) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *WYYSongArtist) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type WYYSongAlbum struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WYYSongAlbum) Reset()         { *m = WYYSongAlbum{} }
func (m *WYYSongAlbum) String() string { return proto.CompactTextString(m) }
func (*WYYSongAlbum) ProtoMessage()    {}
func (*WYYSongAlbum) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{33}
}
func (m *WYYSongAlbum) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WYYSongAlbum.Unmarshal(m, b)
}
func (m *WYYSongAlbum) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WYYSongAlbum.Marshal(b, m, deterministic)
}
func (dst *WYYSongAlbum) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WYYSongAlbum.Merge(dst, src)
}
func (m *WYYSongAlbum) XXX_Size() int {
	return xxx_messageInfo_WYYSongAlbum.Size(m)
}
func (m *WYYSongAlbum) XXX_DiscardUnknown() {
	xxx_messageInfo_WYYSongAlbum.DiscardUnknown(m)
}

var xxx_messageInfo_WYYSongAlbum proto.InternalMessageInfo

func (m *WYYSongAlbum) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *WYYSongAlbum) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type WYYSongResource struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Url                  string   `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	Size                 uint32   `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	Md5                  string   `protobuf:"bytes,4,opt,name=md5,proto3" json:"md5,omitempty"`
	Level                string   `protobuf:"bytes,5,opt,name=level,proto3" json:"level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WYYSongResource) Reset()         { *m = WYYSongResource{} }
func (m *WYYSongResource) String() string { return proto.CompactTextString(m) }
func (*WYYSongResource) ProtoMessage()    {}
func (*WYYSongResource) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{34}
}
func (m *WYYSongResource) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WYYSongResource.Unmarshal(m, b)
}
func (m *WYYSongResource) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WYYSongResource.Marshal(b, m, deterministic)
}
func (dst *WYYSongResource) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WYYSongResource.Merge(dst, src)
}
func (m *WYYSongResource) XXX_Size() int {
	return xxx_messageInfo_WYYSongResource.Size(m)
}
func (m *WYYSongResource) XXX_DiscardUnknown() {
	xxx_messageInfo_WYYSongResource.DiscardUnknown(m)
}

var xxx_messageInfo_WYYSongResource proto.InternalMessageInfo

func (m *WYYSongResource) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *WYYSongResource) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *WYYSongResource) GetSize() uint32 {
	if m != nil {
		return m.Size
	}
	return 0
}

func (m *WYYSongResource) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

func (m *WYYSongResource) GetLevel() string {
	if m != nil {
		return m.Level
	}
	return ""
}

type ListeningChangePlayerReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MusicListMode        uint32   `protobuf:"varint,2,opt,name=music_list_mode,json=musicListMode,proto3" json:"music_list_mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListeningChangePlayerReq) Reset()         { *m = ListeningChangePlayerReq{} }
func (m *ListeningChangePlayerReq) String() string { return proto.CompactTextString(m) }
func (*ListeningChangePlayerReq) ProtoMessage()    {}
func (*ListeningChangePlayerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{35}
}
func (m *ListeningChangePlayerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningChangePlayerReq.Unmarshal(m, b)
}
func (m *ListeningChangePlayerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningChangePlayerReq.Marshal(b, m, deterministic)
}
func (dst *ListeningChangePlayerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningChangePlayerReq.Merge(dst, src)
}
func (m *ListeningChangePlayerReq) XXX_Size() int {
	return xxx_messageInfo_ListeningChangePlayerReq.Size(m)
}
func (m *ListeningChangePlayerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningChangePlayerReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningChangePlayerReq proto.InternalMessageInfo

func (m *ListeningChangePlayerReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ListeningChangePlayerReq) GetMusicListMode() uint32 {
	if m != nil {
		return m.MusicListMode
	}
	return 0
}

type ListeningChangePlayerResp struct {
	MusicListMode        uint32   `protobuf:"varint,1,opt,name=music_list_mode,json=musicListMode,proto3" json:"music_list_mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListeningChangePlayerResp) Reset()         { *m = ListeningChangePlayerResp{} }
func (m *ListeningChangePlayerResp) String() string { return proto.CompactTextString(m) }
func (*ListeningChangePlayerResp) ProtoMessage()    {}
func (*ListeningChangePlayerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{36}
}
func (m *ListeningChangePlayerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningChangePlayerResp.Unmarshal(m, b)
}
func (m *ListeningChangePlayerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningChangePlayerResp.Marshal(b, m, deterministic)
}
func (dst *ListeningChangePlayerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningChangePlayerResp.Merge(dst, src)
}
func (m *ListeningChangePlayerResp) XXX_Size() int {
	return xxx_messageInfo_ListeningChangePlayerResp.Size(m)
}
func (m *ListeningChangePlayerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningChangePlayerResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningChangePlayerResp proto.InternalMessageInfo

func (m *ListeningChangePlayerResp) GetMusicListMode() uint32 {
	if m != nil {
		return m.MusicListMode
	}
	return 0
}

type ListeningGetPlayerStatusReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListeningGetPlayerStatusReq) Reset()         { *m = ListeningGetPlayerStatusReq{} }
func (m *ListeningGetPlayerStatusReq) String() string { return proto.CompactTextString(m) }
func (*ListeningGetPlayerStatusReq) ProtoMessage()    {}
func (*ListeningGetPlayerStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{37}
}
func (m *ListeningGetPlayerStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningGetPlayerStatusReq.Unmarshal(m, b)
}
func (m *ListeningGetPlayerStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningGetPlayerStatusReq.Marshal(b, m, deterministic)
}
func (dst *ListeningGetPlayerStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningGetPlayerStatusReq.Merge(dst, src)
}
func (m *ListeningGetPlayerStatusReq) XXX_Size() int {
	return xxx_messageInfo_ListeningGetPlayerStatusReq.Size(m)
}
func (m *ListeningGetPlayerStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningGetPlayerStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningGetPlayerStatusReq proto.InternalMessageInfo

func (m *ListeningGetPlayerStatusReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ListeningGetPlayerStatusResp struct {
	MusicListMode        uint32   `protobuf:"varint,1,opt,name=music_list_mode,json=musicListMode,proto3" json:"music_list_mode,omitempty"`
	Close                bool     `protobuf:"varint,2,opt,name=close,proto3" json:"close,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListeningGetPlayerStatusResp) Reset()         { *m = ListeningGetPlayerStatusResp{} }
func (m *ListeningGetPlayerStatusResp) String() string { return proto.CompactTextString(m) }
func (*ListeningGetPlayerStatusResp) ProtoMessage()    {}
func (*ListeningGetPlayerStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{38}
}
func (m *ListeningGetPlayerStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningGetPlayerStatusResp.Unmarshal(m, b)
}
func (m *ListeningGetPlayerStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningGetPlayerStatusResp.Marshal(b, m, deterministic)
}
func (dst *ListeningGetPlayerStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningGetPlayerStatusResp.Merge(dst, src)
}
func (m *ListeningGetPlayerStatusResp) XXX_Size() int {
	return xxx_messageInfo_ListeningGetPlayerStatusResp.Size(m)
}
func (m *ListeningGetPlayerStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningGetPlayerStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningGetPlayerStatusResp proto.InternalMessageInfo

func (m *ListeningGetPlayerStatusResp) GetMusicListMode() uint32 {
	if m != nil {
		return m.MusicListMode
	}
	return 0
}

func (m *ListeningGetPlayerStatusResp) GetClose() bool {
	if m != nil {
		return m.Close
	}
	return false
}

type ListeningSearchSongByKeyReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Keyword              string   `protobuf:"bytes,2,opt,name=keyword,proto3" json:"keyword,omitempty"`
	Page                 uint32   `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListeningSearchSongByKeyReq) Reset()         { *m = ListeningSearchSongByKeyReq{} }
func (m *ListeningSearchSongByKeyReq) String() string { return proto.CompactTextString(m) }
func (*ListeningSearchSongByKeyReq) ProtoMessage()    {}
func (*ListeningSearchSongByKeyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{39}
}
func (m *ListeningSearchSongByKeyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningSearchSongByKeyReq.Unmarshal(m, b)
}
func (m *ListeningSearchSongByKeyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningSearchSongByKeyReq.Marshal(b, m, deterministic)
}
func (dst *ListeningSearchSongByKeyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningSearchSongByKeyReq.Merge(dst, src)
}
func (m *ListeningSearchSongByKeyReq) XXX_Size() int {
	return xxx_messageInfo_ListeningSearchSongByKeyReq.Size(m)
}
func (m *ListeningSearchSongByKeyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningSearchSongByKeyReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningSearchSongByKeyReq proto.InternalMessageInfo

func (m *ListeningSearchSongByKeyReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ListeningSearchSongByKeyReq) GetKeyword() string {
	if m != nil {
		return m.Keyword
	}
	return ""
}

func (m *ListeningSearchSongByKeyReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

type ListeningSearchSongByKeyResp struct {
	MusicInfoList        []*WYYMusicInfo `protobuf:"bytes,1,rep,name=music_info_list,json=musicInfoList,proto3" json:"music_info_list,omitempty"`
	HasMore              bool            `protobuf:"varint,2,opt,name=has_more,json=hasMore,proto3" json:"has_more,omitempty"`
	Count                uint32          `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ListeningSearchSongByKeyResp) Reset()         { *m = ListeningSearchSongByKeyResp{} }
func (m *ListeningSearchSongByKeyResp) String() string { return proto.CompactTextString(m) }
func (*ListeningSearchSongByKeyResp) ProtoMessage()    {}
func (*ListeningSearchSongByKeyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{40}
}
func (m *ListeningSearchSongByKeyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningSearchSongByKeyResp.Unmarshal(m, b)
}
func (m *ListeningSearchSongByKeyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningSearchSongByKeyResp.Marshal(b, m, deterministic)
}
func (dst *ListeningSearchSongByKeyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningSearchSongByKeyResp.Merge(dst, src)
}
func (m *ListeningSearchSongByKeyResp) XXX_Size() int {
	return xxx_messageInfo_ListeningSearchSongByKeyResp.Size(m)
}
func (m *ListeningSearchSongByKeyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningSearchSongByKeyResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningSearchSongByKeyResp proto.InternalMessageInfo

func (m *ListeningSearchSongByKeyResp) GetMusicInfoList() []*WYYMusicInfo {
	if m != nil {
		return m.MusicInfoList
	}
	return nil
}

func (m *ListeningSearchSongByKeyResp) GetHasMore() bool {
	if m != nil {
		return m.HasMore
	}
	return false
}

func (m *ListeningSearchSongByKeyResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type ListeningGetSongListByTypeReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Type                 uint32   `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	Page                 uint32   `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListeningGetSongListByTypeReq) Reset()         { *m = ListeningGetSongListByTypeReq{} }
func (m *ListeningGetSongListByTypeReq) String() string { return proto.CompactTextString(m) }
func (*ListeningGetSongListByTypeReq) ProtoMessage()    {}
func (*ListeningGetSongListByTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{41}
}
func (m *ListeningGetSongListByTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningGetSongListByTypeReq.Unmarshal(m, b)
}
func (m *ListeningGetSongListByTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningGetSongListByTypeReq.Marshal(b, m, deterministic)
}
func (dst *ListeningGetSongListByTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningGetSongListByTypeReq.Merge(dst, src)
}
func (m *ListeningGetSongListByTypeReq) XXX_Size() int {
	return xxx_messageInfo_ListeningGetSongListByTypeReq.Size(m)
}
func (m *ListeningGetSongListByTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningGetSongListByTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningGetSongListByTypeReq proto.InternalMessageInfo

func (m *ListeningGetSongListByTypeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ListeningGetSongListByTypeReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *ListeningGetSongListByTypeReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

type ListeningGetSongListByTypeResp struct {
	MusicInfoList        []*WYYMusicInfo `protobuf:"bytes,1,rep,name=music_info_list,json=musicInfoList,proto3" json:"music_info_list,omitempty"`
	Count                uint32          `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	HasMore              bool            `protobuf:"varint,3,opt,name=has_more,json=hasMore,proto3" json:"has_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ListeningGetSongListByTypeResp) Reset()         { *m = ListeningGetSongListByTypeResp{} }
func (m *ListeningGetSongListByTypeResp) String() string { return proto.CompactTextString(m) }
func (*ListeningGetSongListByTypeResp) ProtoMessage()    {}
func (*ListeningGetSongListByTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{42}
}
func (m *ListeningGetSongListByTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningGetSongListByTypeResp.Unmarshal(m, b)
}
func (m *ListeningGetSongListByTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningGetSongListByTypeResp.Marshal(b, m, deterministic)
}
func (dst *ListeningGetSongListByTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningGetSongListByTypeResp.Merge(dst, src)
}
func (m *ListeningGetSongListByTypeResp) XXX_Size() int {
	return xxx_messageInfo_ListeningGetSongListByTypeResp.Size(m)
}
func (m *ListeningGetSongListByTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningGetSongListByTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningGetSongListByTypeResp proto.InternalMessageInfo

func (m *ListeningGetSongListByTypeResp) GetMusicInfoList() []*WYYMusicInfo {
	if m != nil {
		return m.MusicInfoList
	}
	return nil
}

func (m *ListeningGetSongListByTypeResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *ListeningGetSongListByTypeResp) GetHasMore() bool {
	if m != nil {
		return m.HasMore
	}
	return false
}

type ListeningGetSongListListReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListeningGetSongListListReq) Reset()         { *m = ListeningGetSongListListReq{} }
func (m *ListeningGetSongListListReq) String() string { return proto.CompactTextString(m) }
func (*ListeningGetSongListListReq) ProtoMessage()    {}
func (*ListeningGetSongListListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{43}
}
func (m *ListeningGetSongListListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningGetSongListListReq.Unmarshal(m, b)
}
func (m *ListeningGetSongListListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningGetSongListListReq.Marshal(b, m, deterministic)
}
func (dst *ListeningGetSongListListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningGetSongListListReq.Merge(dst, src)
}
func (m *ListeningGetSongListListReq) XXX_Size() int {
	return xxx_messageInfo_ListeningGetSongListListReq.Size(m)
}
func (m *ListeningGetSongListListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningGetSongListListReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningGetSongListListReq proto.InternalMessageInfo

func (m *ListeningGetSongListListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ListeningGetSongListListResp struct {
	List                 []*SongListList `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ListeningGetSongListListResp) Reset()         { *m = ListeningGetSongListListResp{} }
func (m *ListeningGetSongListListResp) String() string { return proto.CompactTextString(m) }
func (*ListeningGetSongListListResp) ProtoMessage()    {}
func (*ListeningGetSongListListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{44}
}
func (m *ListeningGetSongListListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningGetSongListListResp.Unmarshal(m, b)
}
func (m *ListeningGetSongListListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningGetSongListListResp.Marshal(b, m, deterministic)
}
func (dst *ListeningGetSongListListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningGetSongListListResp.Merge(dst, src)
}
func (m *ListeningGetSongListListResp) XXX_Size() int {
	return xxx_messageInfo_ListeningGetSongListListResp.Size(m)
}
func (m *ListeningGetSongListListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningGetSongListListResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningGetSongListListResp proto.InternalMessageInfo

func (m *ListeningGetSongListListResp) GetList() []*SongListList {
	if m != nil {
		return m.List
	}
	return nil
}

type SongListList struct {
	CoverImgUrl          string   `protobuf:"bytes,1,opt,name=cover_img_url,json=coverImgUrl,proto3" json:"cover_img_url,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Type                 uint32   `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	LightCoverImgUrl     string   `protobuf:"bytes,4,opt,name=light_cover_img_url,json=lightCoverImgUrl,proto3" json:"light_cover_img_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SongListList) Reset()         { *m = SongListList{} }
func (m *SongListList) String() string { return proto.CompactTextString(m) }
func (*SongListList) ProtoMessage()    {}
func (*SongListList) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{45}
}
func (m *SongListList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SongListList.Unmarshal(m, b)
}
func (m *SongListList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SongListList.Marshal(b, m, deterministic)
}
func (dst *SongListList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SongListList.Merge(dst, src)
}
func (m *SongListList) XXX_Size() int {
	return xxx_messageInfo_SongListList.Size(m)
}
func (m *SongListList) XXX_DiscardUnknown() {
	xxx_messageInfo_SongListList.DiscardUnknown(m)
}

var xxx_messageInfo_SongListList proto.InternalMessageInfo

func (m *SongListList) GetCoverImgUrl() string {
	if m != nil {
		return m.CoverImgUrl
	}
	return ""
}

func (m *SongListList) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SongListList) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *SongListList) GetLightCoverImgUrl() string {
	if m != nil {
		return m.LightCoverImgUrl
	}
	return ""
}

type WYYMusicInfo struct {
	BaseInfo             *MusicInfoV2 `protobuf:"bytes,1,opt,name=base_info,json=baseInfo,proto3" json:"base_info,omitempty"`
	AlbumName            string       `protobuf:"bytes,2,opt,name=album_name,json=albumName,proto3" json:"album_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *WYYMusicInfo) Reset()         { *m = WYYMusicInfo{} }
func (m *WYYMusicInfo) String() string { return proto.CompactTextString(m) }
func (*WYYMusicInfo) ProtoMessage()    {}
func (*WYYMusicInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{46}
}
func (m *WYYMusicInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WYYMusicInfo.Unmarshal(m, b)
}
func (m *WYYMusicInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WYYMusicInfo.Marshal(b, m, deterministic)
}
func (dst *WYYMusicInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WYYMusicInfo.Merge(dst, src)
}
func (m *WYYMusicInfo) XXX_Size() int {
	return xxx_messageInfo_WYYMusicInfo.Size(m)
}
func (m *WYYMusicInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WYYMusicInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WYYMusicInfo proto.InternalMessageInfo

func (m *WYYMusicInfo) GetBaseInfo() *MusicInfoV2 {
	if m != nil {
		return m.BaseInfo
	}
	return nil
}

func (m *WYYMusicInfo) GetAlbumName() string {
	if m != nil {
		return m.AlbumName
	}
	return ""
}

type ListeningOrderSongReq struct {
	ChannelId            uint32          `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MusicInfoList        []*WYYMusicInfo `protobuf:"bytes,2,rep,name=music_info_list,json=musicInfoList,proto3" json:"music_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ListeningOrderSongReq) Reset()         { *m = ListeningOrderSongReq{} }
func (m *ListeningOrderSongReq) String() string { return proto.CompactTextString(m) }
func (*ListeningOrderSongReq) ProtoMessage()    {}
func (*ListeningOrderSongReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{47}
}
func (m *ListeningOrderSongReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningOrderSongReq.Unmarshal(m, b)
}
func (m *ListeningOrderSongReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningOrderSongReq.Marshal(b, m, deterministic)
}
func (dst *ListeningOrderSongReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningOrderSongReq.Merge(dst, src)
}
func (m *ListeningOrderSongReq) XXX_Size() int {
	return xxx_messageInfo_ListeningOrderSongReq.Size(m)
}
func (m *ListeningOrderSongReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningOrderSongReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningOrderSongReq proto.InternalMessageInfo

func (m *ListeningOrderSongReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ListeningOrderSongReq) GetMusicInfoList() []*WYYMusicInfo {
	if m != nil {
		return m.MusicInfoList
	}
	return nil
}

type ListeningOrderSongResp struct {
	MusicAddedList       []*WYYMusicInfo `protobuf:"bytes,1,rep,name=music_added_list,json=musicAddedList,proto3" json:"music_added_list,omitempty"`
	MaxMusicCount        uint32          `protobuf:"varint,2,opt,name=max_music_count,json=maxMusicCount,proto3" json:"max_music_count,omitempty"`
	CurrentMusicCount    uint32          `protobuf:"varint,3,opt,name=current_music_count,json=currentMusicCount,proto3" json:"current_music_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ListeningOrderSongResp) Reset()         { *m = ListeningOrderSongResp{} }
func (m *ListeningOrderSongResp) String() string { return proto.CompactTextString(m) }
func (*ListeningOrderSongResp) ProtoMessage()    {}
func (*ListeningOrderSongResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{48}
}
func (m *ListeningOrderSongResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningOrderSongResp.Unmarshal(m, b)
}
func (m *ListeningOrderSongResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningOrderSongResp.Marshal(b, m, deterministic)
}
func (dst *ListeningOrderSongResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningOrderSongResp.Merge(dst, src)
}
func (m *ListeningOrderSongResp) XXX_Size() int {
	return xxx_messageInfo_ListeningOrderSongResp.Size(m)
}
func (m *ListeningOrderSongResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningOrderSongResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningOrderSongResp proto.InternalMessageInfo

func (m *ListeningOrderSongResp) GetMusicAddedList() []*WYYMusicInfo {
	if m != nil {
		return m.MusicAddedList
	}
	return nil
}

func (m *ListeningOrderSongResp) GetMaxMusicCount() uint32 {
	if m != nil {
		return m.MaxMusicCount
	}
	return 0
}

func (m *ListeningOrderSongResp) GetCurrentMusicCount() uint32 {
	if m != nil {
		return m.CurrentMusicCount
	}
	return 0
}

type ListeningGetPlayListReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListeningGetPlayListReq) Reset()         { *m = ListeningGetPlayListReq{} }
func (m *ListeningGetPlayListReq) String() string { return proto.CompactTextString(m) }
func (*ListeningGetPlayListReq) ProtoMessage()    {}
func (*ListeningGetPlayListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{49}
}
func (m *ListeningGetPlayListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningGetPlayListReq.Unmarshal(m, b)
}
func (m *ListeningGetPlayListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningGetPlayListReq.Marshal(b, m, deterministic)
}
func (dst *ListeningGetPlayListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningGetPlayListReq.Merge(dst, src)
}
func (m *ListeningGetPlayListReq) XXX_Size() int {
	return xxx_messageInfo_ListeningGetPlayListReq.Size(m)
}
func (m *ListeningGetPlayListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningGetPlayListReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningGetPlayListReq proto.InternalMessageInfo

func (m *ListeningGetPlayListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ListeningGetPlayListResp struct {
	MusicList            []*WYYMusicInfo `protobuf:"bytes,1,rep,name=music_list,json=musicList,proto3" json:"music_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ListeningGetPlayListResp) Reset()         { *m = ListeningGetPlayListResp{} }
func (m *ListeningGetPlayListResp) String() string { return proto.CompactTextString(m) }
func (*ListeningGetPlayListResp) ProtoMessage()    {}
func (*ListeningGetPlayListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{50}
}
func (m *ListeningGetPlayListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningGetPlayListResp.Unmarshal(m, b)
}
func (m *ListeningGetPlayListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningGetPlayListResp.Marshal(b, m, deterministic)
}
func (dst *ListeningGetPlayListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningGetPlayListResp.Merge(dst, src)
}
func (m *ListeningGetPlayListResp) XXX_Size() int {
	return xxx_messageInfo_ListeningGetPlayListResp.Size(m)
}
func (m *ListeningGetPlayListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningGetPlayListResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningGetPlayListResp proto.InternalMessageInfo

func (m *ListeningGetPlayListResp) GetMusicList() []*WYYMusicInfo {
	if m != nil {
		return m.MusicList
	}
	return nil
}

type ListeningGetPlayerUserInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListeningGetPlayerUserInfoReq) Reset()         { *m = ListeningGetPlayerUserInfoReq{} }
func (m *ListeningGetPlayerUserInfoReq) String() string { return proto.CompactTextString(m) }
func (*ListeningGetPlayerUserInfoReq) ProtoMessage()    {}
func (*ListeningGetPlayerUserInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{51}
}
func (m *ListeningGetPlayerUserInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningGetPlayerUserInfoReq.Unmarshal(m, b)
}
func (m *ListeningGetPlayerUserInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningGetPlayerUserInfoReq.Marshal(b, m, deterministic)
}
func (dst *ListeningGetPlayerUserInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningGetPlayerUserInfoReq.Merge(dst, src)
}
func (m *ListeningGetPlayerUserInfoReq) XXX_Size() int {
	return xxx_messageInfo_ListeningGetPlayerUserInfoReq.Size(m)
}
func (m *ListeningGetPlayerUserInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningGetPlayerUserInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningGetPlayerUserInfoReq proto.InternalMessageInfo

func (m *ListeningGetPlayerUserInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ListeningGetPlayerUserInfoResp struct {
	UserInfo             *WYYUserInfo `protobuf:"bytes,1,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ListeningGetPlayerUserInfoResp) Reset()         { *m = ListeningGetPlayerUserInfoResp{} }
func (m *ListeningGetPlayerUserInfoResp) String() string { return proto.CompactTextString(m) }
func (*ListeningGetPlayerUserInfoResp) ProtoMessage()    {}
func (*ListeningGetPlayerUserInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{52}
}
func (m *ListeningGetPlayerUserInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningGetPlayerUserInfoResp.Unmarshal(m, b)
}
func (m *ListeningGetPlayerUserInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningGetPlayerUserInfoResp.Marshal(b, m, deterministic)
}
func (dst *ListeningGetPlayerUserInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningGetPlayerUserInfoResp.Merge(dst, src)
}
func (m *ListeningGetPlayerUserInfoResp) XXX_Size() int {
	return xxx_messageInfo_ListeningGetPlayerUserInfoResp.Size(m)
}
func (m *ListeningGetPlayerUserInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningGetPlayerUserInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningGetPlayerUserInfoResp proto.InternalMessageInfo

func (m *ListeningGetPlayerUserInfoResp) GetUserInfo() *WYYUserInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

type ListeningGetSongResourceReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SongId               string   `protobuf:"bytes,2,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListeningGetSongResourceReq) Reset()         { *m = ListeningGetSongResourceReq{} }
func (m *ListeningGetSongResourceReq) String() string { return proto.CompactTextString(m) }
func (*ListeningGetSongResourceReq) ProtoMessage()    {}
func (*ListeningGetSongResourceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{53}
}
func (m *ListeningGetSongResourceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningGetSongResourceReq.Unmarshal(m, b)
}
func (m *ListeningGetSongResourceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningGetSongResourceReq.Marshal(b, m, deterministic)
}
func (dst *ListeningGetSongResourceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningGetSongResourceReq.Merge(dst, src)
}
func (m *ListeningGetSongResourceReq) XXX_Size() int {
	return xxx_messageInfo_ListeningGetSongResourceReq.Size(m)
}
func (m *ListeningGetSongResourceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningGetSongResourceReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningGetSongResourceReq proto.InternalMessageInfo

func (m *ListeningGetSongResourceReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ListeningGetSongResourceReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

type ListeningGetSongResourceResp struct {
	SongResource         *WYYSongResource `protobuf:"bytes,1,opt,name=song_resource,json=songResource,proto3" json:"song_resource,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *ListeningGetSongResourceResp) Reset()         { *m = ListeningGetSongResourceResp{} }
func (m *ListeningGetSongResourceResp) String() string { return proto.CompactTextString(m) }
func (*ListeningGetSongResourceResp) ProtoMessage()    {}
func (*ListeningGetSongResourceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{54}
}
func (m *ListeningGetSongResourceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningGetSongResourceResp.Unmarshal(m, b)
}
func (m *ListeningGetSongResourceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningGetSongResourceResp.Marshal(b, m, deterministic)
}
func (dst *ListeningGetSongResourceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningGetSongResourceResp.Merge(dst, src)
}
func (m *ListeningGetSongResourceResp) XXX_Size() int {
	return xxx_messageInfo_ListeningGetSongResourceResp.Size(m)
}
func (m *ListeningGetSongResourceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningGetSongResourceResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningGetSongResourceResp proto.InternalMessageInfo

func (m *ListeningGetSongResourceResp) GetSongResource() *WYYSongResource {
	if m != nil {
		return m.SongResource
	}
	return nil
}

type WYYRedirectApiReq struct {
	Code                 string   `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	State                string   `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WYYRedirectApiReq) Reset()         { *m = WYYRedirectApiReq{} }
func (m *WYYRedirectApiReq) String() string { return proto.CompactTextString(m) }
func (*WYYRedirectApiReq) ProtoMessage()    {}
func (*WYYRedirectApiReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{55}
}
func (m *WYYRedirectApiReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WYYRedirectApiReq.Unmarshal(m, b)
}
func (m *WYYRedirectApiReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WYYRedirectApiReq.Marshal(b, m, deterministic)
}
func (dst *WYYRedirectApiReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WYYRedirectApiReq.Merge(dst, src)
}
func (m *WYYRedirectApiReq) XXX_Size() int {
	return xxx_messageInfo_WYYRedirectApiReq.Size(m)
}
func (m *WYYRedirectApiReq) XXX_DiscardUnknown() {
	xxx_messageInfo_WYYRedirectApiReq.DiscardUnknown(m)
}

var xxx_messageInfo_WYYRedirectApiReq proto.InternalMessageInfo

func (m *WYYRedirectApiReq) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *WYYRedirectApiReq) GetState() string {
	if m != nil {
		return m.State
	}
	return ""
}

type WYYRedirectApiResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WYYRedirectApiResp) Reset()         { *m = WYYRedirectApiResp{} }
func (m *WYYRedirectApiResp) String() string { return proto.CompactTextString(m) }
func (*WYYRedirectApiResp) ProtoMessage()    {}
func (*WYYRedirectApiResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{56}
}
func (m *WYYRedirectApiResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WYYRedirectApiResp.Unmarshal(m, b)
}
func (m *WYYRedirectApiResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WYYRedirectApiResp.Marshal(b, m, deterministic)
}
func (dst *WYYRedirectApiResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WYYRedirectApiResp.Merge(dst, src)
}
func (m *WYYRedirectApiResp) XXX_Size() int {
	return xxx_messageInfo_WYYRedirectApiResp.Size(m)
}
func (m *WYYRedirectApiResp) XXX_DiscardUnknown() {
	xxx_messageInfo_WYYRedirectApiResp.DiscardUnknown(m)
}

var xxx_messageInfo_WYYRedirectApiResp proto.InternalMessageInfo

type ListeningReportRecordReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Action               string   `protobuf:"bytes,2,opt,name=action,proto3" json:"action,omitempty"`
	StartLogTime         uint64   `protobuf:"varint,3,opt,name=start_log_time,json=startLogTime,proto3" json:"start_log_time,omitempty"`
	SongId               string   `protobuf:"bytes,4,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	Alg                  string   `protobuf:"bytes,5,opt,name=alg,proto3" json:"alg,omitempty"`
	Time                 uint64   `protobuf:"varint,6,opt,name=time,proto3" json:"time,omitempty"`
	End                  string   `protobuf:"bytes,7,opt,name=end,proto3" json:"end,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListeningReportRecordReq) Reset()         { *m = ListeningReportRecordReq{} }
func (m *ListeningReportRecordReq) String() string { return proto.CompactTextString(m) }
func (*ListeningReportRecordReq) ProtoMessage()    {}
func (*ListeningReportRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{57}
}
func (m *ListeningReportRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningReportRecordReq.Unmarshal(m, b)
}
func (m *ListeningReportRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningReportRecordReq.Marshal(b, m, deterministic)
}
func (dst *ListeningReportRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningReportRecordReq.Merge(dst, src)
}
func (m *ListeningReportRecordReq) XXX_Size() int {
	return xxx_messageInfo_ListeningReportRecordReq.Size(m)
}
func (m *ListeningReportRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningReportRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningReportRecordReq proto.InternalMessageInfo

func (m *ListeningReportRecordReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ListeningReportRecordReq) GetAction() string {
	if m != nil {
		return m.Action
	}
	return ""
}

func (m *ListeningReportRecordReq) GetStartLogTime() uint64 {
	if m != nil {
		return m.StartLogTime
	}
	return 0
}

func (m *ListeningReportRecordReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *ListeningReportRecordReq) GetAlg() string {
	if m != nil {
		return m.Alg
	}
	return ""
}

func (m *ListeningReportRecordReq) GetTime() uint64 {
	if m != nil {
		return m.Time
	}
	return 0
}

func (m *ListeningReportRecordReq) GetEnd() string {
	if m != nil {
		return m.End
	}
	return ""
}

type ListeningReportRecordResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListeningReportRecordResp) Reset()         { *m = ListeningReportRecordResp{} }
func (m *ListeningReportRecordResp) String() string { return proto.CompactTextString(m) }
func (*ListeningReportRecordResp) ProtoMessage()    {}
func (*ListeningReportRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_022263866ee59b01, []int{58}
}
func (m *ListeningReportRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningReportRecordResp.Unmarshal(m, b)
}
func (m *ListeningReportRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningReportRecordResp.Marshal(b, m, deterministic)
}
func (dst *ListeningReportRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningReportRecordResp.Merge(dst, src)
}
func (m *ListeningReportRecordResp) XXX_Size() int {
	return xxx_messageInfo_ListeningReportRecordResp.Size(m)
}
func (m *ListeningReportRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningReportRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningReportRecordResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*RecycleMenuReq)(nil), "channellisteningautoplay.RecycleMenuReq")
	proto.RegisterType((*RecycleMenuResp)(nil), "channellisteningautoplay.RecycleMenuResp")
	proto.RegisterType((*RecyclePlayInfoReq)(nil), "channellisteningautoplay.RecyclePlayInfoReq")
	proto.RegisterType((*RecyclePlayInfoResp)(nil), "channellisteningautoplay.RecyclePlayInfoResp")
	proto.RegisterType((*BatchChannelPlayingMusicReq)(nil), "channellisteningautoplay.BatchChannelPlayingMusicReq")
	proto.RegisterType((*BatchChannelPlayingMusicResp)(nil), "channellisteningautoplay.BatchChannelPlayingMusicResp")
	proto.RegisterMapType((map[uint32]*ChannelPlayingMusic)(nil), "channellisteningautoplay.BatchChannelPlayingMusicResp.ChannelMusicsEntry")
	proto.RegisterType((*ChannelPlayingMusic)(nil), "channellisteningautoplay.ChannelPlayingMusic")
	proto.RegisterType((*ChannelPlayStatusReq)(nil), "channellisteningautoplay.ChannelPlayStatusReq")
	proto.RegisterType((*ChannelPlayStatusResp)(nil), "channellisteningautoplay.ChannelPlayStatusResp")
	proto.RegisterType((*AutoPlayInfo)(nil), "channellisteningautoplay.AutoPlayInfo")
	proto.RegisterType((*MusicInfo)(nil), "channellisteningautoplay.MusicInfo")
	proto.RegisterType((*SwitchChannelPlayReq)(nil), "channellisteningautoplay.SwitchChannelPlayReq")
	proto.RegisterType((*SwitchChannelPlayResp)(nil), "channellisteningautoplay.SwitchChannelPlayResp")
	proto.RegisterType((*ChannelRcmdMusicMenuReq)(nil), "channellisteningautoplay.ChannelRcmdMusicMenuReq")
	proto.RegisterType((*ChannelRcmdMusicMenuResp)(nil), "channellisteningautoplay.ChannelRcmdMusicMenuResp")
	proto.RegisterType((*ListeningAutoPlayRcmdMenu)(nil), "channellisteningautoplay.ListeningAutoPlayRcmdMenu")
	proto.RegisterType((*SetVolumeReq)(nil), "channellisteningautoplay.SetVolumeReq")
	proto.RegisterType((*SetVolumeResp)(nil), "channellisteningautoplay.SetVolumeResp")
	proto.RegisterType((*CutAutoModeSongReq)(nil), "channellisteningautoplay.CutAutoModeSongReq")
	proto.RegisterType((*CutAutoModeSongResp)(nil), "channellisteningautoplay.CutAutoModeSongResp")
	proto.RegisterType((*ReportChannelAutoSongProgressReq)(nil), "channellisteningautoplay.ReportChannelAutoSongProgressReq")
	proto.RegisterType((*ReportChannelAutoSongProgressResp)(nil), "channellisteningautoplay.ReportChannelAutoSongProgressResp")
	proto.RegisterType((*SetChannelPlayModeReq)(nil), "channellisteningautoplay.SetChannelPlayModeReq")
	proto.RegisterType((*SetChannelPlayModeResp)(nil), "channellisteningautoplay.SetChannelPlayModeResp")
	proto.RegisterType((*SetChannelPlayStatusReq)(nil), "channellisteningautoplay.SetChannelPlayStatusReq")
	proto.RegisterType((*SetChannelPlayStatusResp)(nil), "channellisteningautoplay.SetChannelPlayStatusResp")
	proto.RegisterType((*AutoSongStatusNotify)(nil), "channellisteningautoplay.AutoSongStatusNotify")
	proto.RegisterType((*ChannelPlayModeNotify)(nil), "channellisteningautoplay.ChannelPlayModeNotify")
	proto.RegisterType((*ChannelVolumeNotify)(nil), "channellisteningautoplay.ChannelVolumeNotify")
	proto.RegisterType((*MusicInfoV2)(nil), "channellisteningautoplay.MusicInfoV2")
	proto.RegisterType((*WYYUserInfo)(nil), "channellisteningautoplay.WYYUserInfo")
	proto.RegisterType((*WYYVipInfo)(nil), "channellisteningautoplay.WYYVipInfo")
	proto.RegisterType((*WYYSongArtist)(nil), "channellisteningautoplay.WYYSongArtist")
	proto.RegisterType((*WYYSongAlbum)(nil), "channellisteningautoplay.WYYSongAlbum")
	proto.RegisterType((*WYYSongResource)(nil), "channellisteningautoplay.WYYSongResource")
	proto.RegisterType((*ListeningChangePlayerReq)(nil), "channellisteningautoplay.ListeningChangePlayerReq")
	proto.RegisterType((*ListeningChangePlayerResp)(nil), "channellisteningautoplay.ListeningChangePlayerResp")
	proto.RegisterType((*ListeningGetPlayerStatusReq)(nil), "channellisteningautoplay.ListeningGetPlayerStatusReq")
	proto.RegisterType((*ListeningGetPlayerStatusResp)(nil), "channellisteningautoplay.ListeningGetPlayerStatusResp")
	proto.RegisterType((*ListeningSearchSongByKeyReq)(nil), "channellisteningautoplay.ListeningSearchSongByKeyReq")
	proto.RegisterType((*ListeningSearchSongByKeyResp)(nil), "channellisteningautoplay.ListeningSearchSongByKeyResp")
	proto.RegisterType((*ListeningGetSongListByTypeReq)(nil), "channellisteningautoplay.ListeningGetSongListByTypeReq")
	proto.RegisterType((*ListeningGetSongListByTypeResp)(nil), "channellisteningautoplay.ListeningGetSongListByTypeResp")
	proto.RegisterType((*ListeningGetSongListListReq)(nil), "channellisteningautoplay.ListeningGetSongListListReq")
	proto.RegisterType((*ListeningGetSongListListResp)(nil), "channellisteningautoplay.ListeningGetSongListListResp")
	proto.RegisterType((*SongListList)(nil), "channellisteningautoplay.SongListList")
	proto.RegisterType((*WYYMusicInfo)(nil), "channellisteningautoplay.WYYMusicInfo")
	proto.RegisterType((*ListeningOrderSongReq)(nil), "channellisteningautoplay.ListeningOrderSongReq")
	proto.RegisterType((*ListeningOrderSongResp)(nil), "channellisteningautoplay.ListeningOrderSongResp")
	proto.RegisterType((*ListeningGetPlayListReq)(nil), "channellisteningautoplay.ListeningGetPlayListReq")
	proto.RegisterType((*ListeningGetPlayListResp)(nil), "channellisteningautoplay.ListeningGetPlayListResp")
	proto.RegisterType((*ListeningGetPlayerUserInfoReq)(nil), "channellisteningautoplay.ListeningGetPlayerUserInfoReq")
	proto.RegisterType((*ListeningGetPlayerUserInfoResp)(nil), "channellisteningautoplay.ListeningGetPlayerUserInfoResp")
	proto.RegisterType((*ListeningGetSongResourceReq)(nil), "channellisteningautoplay.ListeningGetSongResourceReq")
	proto.RegisterType((*ListeningGetSongResourceResp)(nil), "channellisteningautoplay.ListeningGetSongResourceResp")
	proto.RegisterType((*WYYRedirectApiReq)(nil), "channellisteningautoplay.WYYRedirectApiReq")
	proto.RegisterType((*WYYRedirectApiResp)(nil), "channellisteningautoplay.WYYRedirectApiResp")
	proto.RegisterType((*ListeningReportRecordReq)(nil), "channellisteningautoplay.ListeningReportRecordReq")
	proto.RegisterType((*ListeningReportRecordResp)(nil), "channellisteningautoplay.ListeningReportRecordResp")
	proto.RegisterEnum("channellisteningautoplay.ChannelPlayMode", ChannelPlayMode_name, ChannelPlayMode_value)
	proto.RegisterEnum("channellisteningautoplay.AutoPlaySwitch", AutoPlaySwitch_name, AutoPlaySwitch_value)
	proto.RegisterEnum("channellisteningautoplay.PlayStatus", PlayStatus_name, PlayStatus_value)
	proto.RegisterEnum("channellisteningautoplay.WYYVipOpenDetailDto", WYYVipOpenDetailDto_name, WYYVipOpenDetailDto_value)
	proto.RegisterEnum("channellisteningautoplay.MusicListType", MusicListType_name, MusicListType_value)
	proto.RegisterEnum("channellisteningautoplay.SongListType", SongListType_name, SongListType_value)
	proto.RegisterEnum("channellisteningautoplay.ReportChannelAutoSongProgressReq_ProgressType", ReportChannelAutoSongProgressReq_ProgressType_name, ReportChannelAutoSongProgressReq_ProgressType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelListeningAutoPlayClient is the client API for ChannelListeningAutoPlay service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelListeningAutoPlayClient interface {
	ChannelPlayStatus(ctx context.Context, in *ChannelPlayStatusReq, opts ...grpc.CallOption) (*ChannelPlayStatusResp, error)
	BatchChannelPlayingMusic(ctx context.Context, in *BatchChannelPlayingMusicReq, opts ...grpc.CallOption) (*BatchChannelPlayingMusicResp, error)
	SwitchChannelPlay(ctx context.Context, in *SwitchChannelPlayReq, opts ...grpc.CallOption) (*SwitchChannelPlayResp, error)
	CutAutoModeSong(ctx context.Context, in *CutAutoModeSongReq, opts ...grpc.CallOption) (*CutAutoModeSongResp, error)
	ReportChannelAutoSongProgress(ctx context.Context, in *ReportChannelAutoSongProgressReq, opts ...grpc.CallOption) (*ReportChannelAutoSongProgressResp, error)
	ChannelRcmdMusicMenu(ctx context.Context, in *ChannelRcmdMusicMenuReq, opts ...grpc.CallOption) (*ChannelRcmdMusicMenuResp, error)
	SetVolume(ctx context.Context, in *SetVolumeReq, opts ...grpc.CallOption) (*SetVolumeResp, error)
	SetChannelPlayMode(ctx context.Context, in *SetChannelPlayModeReq, opts ...grpc.CallOption) (*SetChannelPlayModeResp, error)
	SetChannelPlayStatus(ctx context.Context, in *SetChannelPlayStatusReq, opts ...grpc.CallOption) (*SetChannelPlayStatusResp, error)
	RecyclePlayInfo(ctx context.Context, in *RecyclePlayInfoReq, opts ...grpc.CallOption) (*RecyclePlayInfoResp, error)
	RecycleMenu(ctx context.Context, in *RecycleMenuReq, opts ...grpc.CallOption) (*RecycleMenuResp, error)
	ListeningChangePlayer(ctx context.Context, in *ListeningChangePlayerReq, opts ...grpc.CallOption) (*ListeningChangePlayerResp, error)
	ListeningGetPlayerStatus(ctx context.Context, in *ListeningGetPlayerStatusReq, opts ...grpc.CallOption) (*ListeningGetPlayerStatusResp, error)
	ListeningSearchSongByKey(ctx context.Context, in *ListeningSearchSongByKeyReq, opts ...grpc.CallOption) (*ListeningSearchSongByKeyResp, error)
	ListeningGetSongListByType(ctx context.Context, in *ListeningGetSongListByTypeReq, opts ...grpc.CallOption) (*ListeningGetSongListByTypeResp, error)
	ListeningGetSongListList(ctx context.Context, in *ListeningGetSongListListReq, opts ...grpc.CallOption) (*ListeningGetSongListListResp, error)
	ListeningGetPlayerUserInfo(ctx context.Context, in *ListeningGetPlayerUserInfoReq, opts ...grpc.CallOption) (*ListeningGetPlayerUserInfoResp, error)
	ListeningOrderSong(ctx context.Context, in *ListeningOrderSongReq, opts ...grpc.CallOption) (*ListeningOrderSongResp, error)
	ListeningGetPlayList(ctx context.Context, in *ListeningGetPlayListReq, opts ...grpc.CallOption) (*ListeningGetPlayListResp, error)
	ListeningGetSongResource(ctx context.Context, in *ListeningGetSongResourceReq, opts ...grpc.CallOption) (*ListeningGetSongResourceResp, error)
	WYYRedirectApi(ctx context.Context, in *WYYRedirectApiReq, opts ...grpc.CallOption) (*WYYRedirectApiResp, error)
	ListeningReportRecord(ctx context.Context, in *ListeningReportRecordReq, opts ...grpc.CallOption) (*ListeningReportRecordResp, error)
}

type channelListeningAutoPlayClient struct {
	cc *grpc.ClientConn
}

func NewChannelListeningAutoPlayClient(cc *grpc.ClientConn) ChannelListeningAutoPlayClient {
	return &channelListeningAutoPlayClient{cc}
}

func (c *channelListeningAutoPlayClient) ChannelPlayStatus(ctx context.Context, in *ChannelPlayStatusReq, opts ...grpc.CallOption) (*ChannelPlayStatusResp, error) {
	out := new(ChannelPlayStatusResp)
	err := c.cc.Invoke(ctx, "/channellisteningautoplay.ChannelListeningAutoPlay/ChannelPlayStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningAutoPlayClient) BatchChannelPlayingMusic(ctx context.Context, in *BatchChannelPlayingMusicReq, opts ...grpc.CallOption) (*BatchChannelPlayingMusicResp, error) {
	out := new(BatchChannelPlayingMusicResp)
	err := c.cc.Invoke(ctx, "/channellisteningautoplay.ChannelListeningAutoPlay/BatchChannelPlayingMusic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningAutoPlayClient) SwitchChannelPlay(ctx context.Context, in *SwitchChannelPlayReq, opts ...grpc.CallOption) (*SwitchChannelPlayResp, error) {
	out := new(SwitchChannelPlayResp)
	err := c.cc.Invoke(ctx, "/channellisteningautoplay.ChannelListeningAutoPlay/SwitchChannelPlay", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningAutoPlayClient) CutAutoModeSong(ctx context.Context, in *CutAutoModeSongReq, opts ...grpc.CallOption) (*CutAutoModeSongResp, error) {
	out := new(CutAutoModeSongResp)
	err := c.cc.Invoke(ctx, "/channellisteningautoplay.ChannelListeningAutoPlay/CutAutoModeSong", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningAutoPlayClient) ReportChannelAutoSongProgress(ctx context.Context, in *ReportChannelAutoSongProgressReq, opts ...grpc.CallOption) (*ReportChannelAutoSongProgressResp, error) {
	out := new(ReportChannelAutoSongProgressResp)
	err := c.cc.Invoke(ctx, "/channellisteningautoplay.ChannelListeningAutoPlay/ReportChannelAutoSongProgress", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningAutoPlayClient) ChannelRcmdMusicMenu(ctx context.Context, in *ChannelRcmdMusicMenuReq, opts ...grpc.CallOption) (*ChannelRcmdMusicMenuResp, error) {
	out := new(ChannelRcmdMusicMenuResp)
	err := c.cc.Invoke(ctx, "/channellisteningautoplay.ChannelListeningAutoPlay/ChannelRcmdMusicMenu", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningAutoPlayClient) SetVolume(ctx context.Context, in *SetVolumeReq, opts ...grpc.CallOption) (*SetVolumeResp, error) {
	out := new(SetVolumeResp)
	err := c.cc.Invoke(ctx, "/channellisteningautoplay.ChannelListeningAutoPlay/SetVolume", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningAutoPlayClient) SetChannelPlayMode(ctx context.Context, in *SetChannelPlayModeReq, opts ...grpc.CallOption) (*SetChannelPlayModeResp, error) {
	out := new(SetChannelPlayModeResp)
	err := c.cc.Invoke(ctx, "/channellisteningautoplay.ChannelListeningAutoPlay/SetChannelPlayMode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningAutoPlayClient) SetChannelPlayStatus(ctx context.Context, in *SetChannelPlayStatusReq, opts ...grpc.CallOption) (*SetChannelPlayStatusResp, error) {
	out := new(SetChannelPlayStatusResp)
	err := c.cc.Invoke(ctx, "/channellisteningautoplay.ChannelListeningAutoPlay/SetChannelPlayStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningAutoPlayClient) RecyclePlayInfo(ctx context.Context, in *RecyclePlayInfoReq, opts ...grpc.CallOption) (*RecyclePlayInfoResp, error) {
	out := new(RecyclePlayInfoResp)
	err := c.cc.Invoke(ctx, "/channellisteningautoplay.ChannelListeningAutoPlay/RecyclePlayInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningAutoPlayClient) RecycleMenu(ctx context.Context, in *RecycleMenuReq, opts ...grpc.CallOption) (*RecycleMenuResp, error) {
	out := new(RecycleMenuResp)
	err := c.cc.Invoke(ctx, "/channellisteningautoplay.ChannelListeningAutoPlay/RecycleMenu", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningAutoPlayClient) ListeningChangePlayer(ctx context.Context, in *ListeningChangePlayerReq, opts ...grpc.CallOption) (*ListeningChangePlayerResp, error) {
	out := new(ListeningChangePlayerResp)
	err := c.cc.Invoke(ctx, "/channellisteningautoplay.ChannelListeningAutoPlay/ListeningChangePlayer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningAutoPlayClient) ListeningGetPlayerStatus(ctx context.Context, in *ListeningGetPlayerStatusReq, opts ...grpc.CallOption) (*ListeningGetPlayerStatusResp, error) {
	out := new(ListeningGetPlayerStatusResp)
	err := c.cc.Invoke(ctx, "/channellisteningautoplay.ChannelListeningAutoPlay/ListeningGetPlayerStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningAutoPlayClient) ListeningSearchSongByKey(ctx context.Context, in *ListeningSearchSongByKeyReq, opts ...grpc.CallOption) (*ListeningSearchSongByKeyResp, error) {
	out := new(ListeningSearchSongByKeyResp)
	err := c.cc.Invoke(ctx, "/channellisteningautoplay.ChannelListeningAutoPlay/ListeningSearchSongByKey", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningAutoPlayClient) ListeningGetSongListByType(ctx context.Context, in *ListeningGetSongListByTypeReq, opts ...grpc.CallOption) (*ListeningGetSongListByTypeResp, error) {
	out := new(ListeningGetSongListByTypeResp)
	err := c.cc.Invoke(ctx, "/channellisteningautoplay.ChannelListeningAutoPlay/ListeningGetSongListByType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningAutoPlayClient) ListeningGetSongListList(ctx context.Context, in *ListeningGetSongListListReq, opts ...grpc.CallOption) (*ListeningGetSongListListResp, error) {
	out := new(ListeningGetSongListListResp)
	err := c.cc.Invoke(ctx, "/channellisteningautoplay.ChannelListeningAutoPlay/ListeningGetSongListList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningAutoPlayClient) ListeningGetPlayerUserInfo(ctx context.Context, in *ListeningGetPlayerUserInfoReq, opts ...grpc.CallOption) (*ListeningGetPlayerUserInfoResp, error) {
	out := new(ListeningGetPlayerUserInfoResp)
	err := c.cc.Invoke(ctx, "/channellisteningautoplay.ChannelListeningAutoPlay/ListeningGetPlayerUserInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningAutoPlayClient) ListeningOrderSong(ctx context.Context, in *ListeningOrderSongReq, opts ...grpc.CallOption) (*ListeningOrderSongResp, error) {
	out := new(ListeningOrderSongResp)
	err := c.cc.Invoke(ctx, "/channellisteningautoplay.ChannelListeningAutoPlay/ListeningOrderSong", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningAutoPlayClient) ListeningGetPlayList(ctx context.Context, in *ListeningGetPlayListReq, opts ...grpc.CallOption) (*ListeningGetPlayListResp, error) {
	out := new(ListeningGetPlayListResp)
	err := c.cc.Invoke(ctx, "/channellisteningautoplay.ChannelListeningAutoPlay/ListeningGetPlayList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningAutoPlayClient) ListeningGetSongResource(ctx context.Context, in *ListeningGetSongResourceReq, opts ...grpc.CallOption) (*ListeningGetSongResourceResp, error) {
	out := new(ListeningGetSongResourceResp)
	err := c.cc.Invoke(ctx, "/channellisteningautoplay.ChannelListeningAutoPlay/ListeningGetSongResource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningAutoPlayClient) WYYRedirectApi(ctx context.Context, in *WYYRedirectApiReq, opts ...grpc.CallOption) (*WYYRedirectApiResp, error) {
	out := new(WYYRedirectApiResp)
	err := c.cc.Invoke(ctx, "/channellisteningautoplay.ChannelListeningAutoPlay/WYYRedirectApi", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningAutoPlayClient) ListeningReportRecord(ctx context.Context, in *ListeningReportRecordReq, opts ...grpc.CallOption) (*ListeningReportRecordResp, error) {
	out := new(ListeningReportRecordResp)
	err := c.cc.Invoke(ctx, "/channellisteningautoplay.ChannelListeningAutoPlay/ListeningReportRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelListeningAutoPlayServer is the server API for ChannelListeningAutoPlay service.
type ChannelListeningAutoPlayServer interface {
	ChannelPlayStatus(context.Context, *ChannelPlayStatusReq) (*ChannelPlayStatusResp, error)
	BatchChannelPlayingMusic(context.Context, *BatchChannelPlayingMusicReq) (*BatchChannelPlayingMusicResp, error)
	SwitchChannelPlay(context.Context, *SwitchChannelPlayReq) (*SwitchChannelPlayResp, error)
	CutAutoModeSong(context.Context, *CutAutoModeSongReq) (*CutAutoModeSongResp, error)
	ReportChannelAutoSongProgress(context.Context, *ReportChannelAutoSongProgressReq) (*ReportChannelAutoSongProgressResp, error)
	ChannelRcmdMusicMenu(context.Context, *ChannelRcmdMusicMenuReq) (*ChannelRcmdMusicMenuResp, error)
	SetVolume(context.Context, *SetVolumeReq) (*SetVolumeResp, error)
	SetChannelPlayMode(context.Context, *SetChannelPlayModeReq) (*SetChannelPlayModeResp, error)
	SetChannelPlayStatus(context.Context, *SetChannelPlayStatusReq) (*SetChannelPlayStatusResp, error)
	RecyclePlayInfo(context.Context, *RecyclePlayInfoReq) (*RecyclePlayInfoResp, error)
	RecycleMenu(context.Context, *RecycleMenuReq) (*RecycleMenuResp, error)
	ListeningChangePlayer(context.Context, *ListeningChangePlayerReq) (*ListeningChangePlayerResp, error)
	ListeningGetPlayerStatus(context.Context, *ListeningGetPlayerStatusReq) (*ListeningGetPlayerStatusResp, error)
	ListeningSearchSongByKey(context.Context, *ListeningSearchSongByKeyReq) (*ListeningSearchSongByKeyResp, error)
	ListeningGetSongListByType(context.Context, *ListeningGetSongListByTypeReq) (*ListeningGetSongListByTypeResp, error)
	ListeningGetSongListList(context.Context, *ListeningGetSongListListReq) (*ListeningGetSongListListResp, error)
	ListeningGetPlayerUserInfo(context.Context, *ListeningGetPlayerUserInfoReq) (*ListeningGetPlayerUserInfoResp, error)
	ListeningOrderSong(context.Context, *ListeningOrderSongReq) (*ListeningOrderSongResp, error)
	ListeningGetPlayList(context.Context, *ListeningGetPlayListReq) (*ListeningGetPlayListResp, error)
	ListeningGetSongResource(context.Context, *ListeningGetSongResourceReq) (*ListeningGetSongResourceResp, error)
	WYYRedirectApi(context.Context, *WYYRedirectApiReq) (*WYYRedirectApiResp, error)
	ListeningReportRecord(context.Context, *ListeningReportRecordReq) (*ListeningReportRecordResp, error)
}

func RegisterChannelListeningAutoPlayServer(s *grpc.Server, srv ChannelListeningAutoPlayServer) {
	s.RegisterService(&_ChannelListeningAutoPlay_serviceDesc, srv)
}

func _ChannelListeningAutoPlay_ChannelPlayStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelPlayStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningAutoPlayServer).ChannelPlayStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellisteningautoplay.ChannelListeningAutoPlay/ChannelPlayStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningAutoPlayServer).ChannelPlayStatus(ctx, req.(*ChannelPlayStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningAutoPlay_BatchChannelPlayingMusic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchChannelPlayingMusicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningAutoPlayServer).BatchChannelPlayingMusic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellisteningautoplay.ChannelListeningAutoPlay/BatchChannelPlayingMusic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningAutoPlayServer).BatchChannelPlayingMusic(ctx, req.(*BatchChannelPlayingMusicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningAutoPlay_SwitchChannelPlay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SwitchChannelPlayReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningAutoPlayServer).SwitchChannelPlay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellisteningautoplay.ChannelListeningAutoPlay/SwitchChannelPlay",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningAutoPlayServer).SwitchChannelPlay(ctx, req.(*SwitchChannelPlayReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningAutoPlay_CutAutoModeSong_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CutAutoModeSongReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningAutoPlayServer).CutAutoModeSong(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellisteningautoplay.ChannelListeningAutoPlay/CutAutoModeSong",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningAutoPlayServer).CutAutoModeSong(ctx, req.(*CutAutoModeSongReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningAutoPlay_ReportChannelAutoSongProgress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportChannelAutoSongProgressReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningAutoPlayServer).ReportChannelAutoSongProgress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellisteningautoplay.ChannelListeningAutoPlay/ReportChannelAutoSongProgress",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningAutoPlayServer).ReportChannelAutoSongProgress(ctx, req.(*ReportChannelAutoSongProgressReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningAutoPlay_ChannelRcmdMusicMenu_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelRcmdMusicMenuReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningAutoPlayServer).ChannelRcmdMusicMenu(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellisteningautoplay.ChannelListeningAutoPlay/ChannelRcmdMusicMenu",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningAutoPlayServer).ChannelRcmdMusicMenu(ctx, req.(*ChannelRcmdMusicMenuReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningAutoPlay_SetVolume_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetVolumeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningAutoPlayServer).SetVolume(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellisteningautoplay.ChannelListeningAutoPlay/SetVolume",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningAutoPlayServer).SetVolume(ctx, req.(*SetVolumeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningAutoPlay_SetChannelPlayMode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelPlayModeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningAutoPlayServer).SetChannelPlayMode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellisteningautoplay.ChannelListeningAutoPlay/SetChannelPlayMode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningAutoPlayServer).SetChannelPlayMode(ctx, req.(*SetChannelPlayModeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningAutoPlay_SetChannelPlayStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelPlayStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningAutoPlayServer).SetChannelPlayStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellisteningautoplay.ChannelListeningAutoPlay/SetChannelPlayStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningAutoPlayServer).SetChannelPlayStatus(ctx, req.(*SetChannelPlayStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningAutoPlay_RecyclePlayInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecyclePlayInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningAutoPlayServer).RecyclePlayInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellisteningautoplay.ChannelListeningAutoPlay/RecyclePlayInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningAutoPlayServer).RecyclePlayInfo(ctx, req.(*RecyclePlayInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningAutoPlay_RecycleMenu_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecycleMenuReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningAutoPlayServer).RecycleMenu(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellisteningautoplay.ChannelListeningAutoPlay/RecycleMenu",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningAutoPlayServer).RecycleMenu(ctx, req.(*RecycleMenuReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningAutoPlay_ListeningChangePlayer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListeningChangePlayerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningAutoPlayServer).ListeningChangePlayer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellisteningautoplay.ChannelListeningAutoPlay/ListeningChangePlayer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningAutoPlayServer).ListeningChangePlayer(ctx, req.(*ListeningChangePlayerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningAutoPlay_ListeningGetPlayerStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListeningGetPlayerStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningAutoPlayServer).ListeningGetPlayerStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellisteningautoplay.ChannelListeningAutoPlay/ListeningGetPlayerStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningAutoPlayServer).ListeningGetPlayerStatus(ctx, req.(*ListeningGetPlayerStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningAutoPlay_ListeningSearchSongByKey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListeningSearchSongByKeyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningAutoPlayServer).ListeningSearchSongByKey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellisteningautoplay.ChannelListeningAutoPlay/ListeningSearchSongByKey",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningAutoPlayServer).ListeningSearchSongByKey(ctx, req.(*ListeningSearchSongByKeyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningAutoPlay_ListeningGetSongListByType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListeningGetSongListByTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningAutoPlayServer).ListeningGetSongListByType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellisteningautoplay.ChannelListeningAutoPlay/ListeningGetSongListByType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningAutoPlayServer).ListeningGetSongListByType(ctx, req.(*ListeningGetSongListByTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningAutoPlay_ListeningGetSongListList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListeningGetSongListListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningAutoPlayServer).ListeningGetSongListList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellisteningautoplay.ChannelListeningAutoPlay/ListeningGetSongListList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningAutoPlayServer).ListeningGetSongListList(ctx, req.(*ListeningGetSongListListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningAutoPlay_ListeningGetPlayerUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListeningGetPlayerUserInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningAutoPlayServer).ListeningGetPlayerUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellisteningautoplay.ChannelListeningAutoPlay/ListeningGetPlayerUserInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningAutoPlayServer).ListeningGetPlayerUserInfo(ctx, req.(*ListeningGetPlayerUserInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningAutoPlay_ListeningOrderSong_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListeningOrderSongReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningAutoPlayServer).ListeningOrderSong(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellisteningautoplay.ChannelListeningAutoPlay/ListeningOrderSong",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningAutoPlayServer).ListeningOrderSong(ctx, req.(*ListeningOrderSongReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningAutoPlay_ListeningGetPlayList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListeningGetPlayListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningAutoPlayServer).ListeningGetPlayList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellisteningautoplay.ChannelListeningAutoPlay/ListeningGetPlayList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningAutoPlayServer).ListeningGetPlayList(ctx, req.(*ListeningGetPlayListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningAutoPlay_ListeningGetSongResource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListeningGetSongResourceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningAutoPlayServer).ListeningGetSongResource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellisteningautoplay.ChannelListeningAutoPlay/ListeningGetSongResource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningAutoPlayServer).ListeningGetSongResource(ctx, req.(*ListeningGetSongResourceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningAutoPlay_WYYRedirectApi_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WYYRedirectApiReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningAutoPlayServer).WYYRedirectApi(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellisteningautoplay.ChannelListeningAutoPlay/WYYRedirectApi",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningAutoPlayServer).WYYRedirectApi(ctx, req.(*WYYRedirectApiReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningAutoPlay_ListeningReportRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListeningReportRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningAutoPlayServer).ListeningReportRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellisteningautoplay.ChannelListeningAutoPlay/ListeningReportRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningAutoPlayServer).ListeningReportRecord(ctx, req.(*ListeningReportRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelListeningAutoPlay_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channellisteningautoplay.ChannelListeningAutoPlay",
	HandlerType: (*ChannelListeningAutoPlayServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ChannelPlayStatus",
			Handler:    _ChannelListeningAutoPlay_ChannelPlayStatus_Handler,
		},
		{
			MethodName: "BatchChannelPlayingMusic",
			Handler:    _ChannelListeningAutoPlay_BatchChannelPlayingMusic_Handler,
		},
		{
			MethodName: "SwitchChannelPlay",
			Handler:    _ChannelListeningAutoPlay_SwitchChannelPlay_Handler,
		},
		{
			MethodName: "CutAutoModeSong",
			Handler:    _ChannelListeningAutoPlay_CutAutoModeSong_Handler,
		},
		{
			MethodName: "ReportChannelAutoSongProgress",
			Handler:    _ChannelListeningAutoPlay_ReportChannelAutoSongProgress_Handler,
		},
		{
			MethodName: "ChannelRcmdMusicMenu",
			Handler:    _ChannelListeningAutoPlay_ChannelRcmdMusicMenu_Handler,
		},
		{
			MethodName: "SetVolume",
			Handler:    _ChannelListeningAutoPlay_SetVolume_Handler,
		},
		{
			MethodName: "SetChannelPlayMode",
			Handler:    _ChannelListeningAutoPlay_SetChannelPlayMode_Handler,
		},
		{
			MethodName: "SetChannelPlayStatus",
			Handler:    _ChannelListeningAutoPlay_SetChannelPlayStatus_Handler,
		},
		{
			MethodName: "RecyclePlayInfo",
			Handler:    _ChannelListeningAutoPlay_RecyclePlayInfo_Handler,
		},
		{
			MethodName: "RecycleMenu",
			Handler:    _ChannelListeningAutoPlay_RecycleMenu_Handler,
		},
		{
			MethodName: "ListeningChangePlayer",
			Handler:    _ChannelListeningAutoPlay_ListeningChangePlayer_Handler,
		},
		{
			MethodName: "ListeningGetPlayerStatus",
			Handler:    _ChannelListeningAutoPlay_ListeningGetPlayerStatus_Handler,
		},
		{
			MethodName: "ListeningSearchSongByKey",
			Handler:    _ChannelListeningAutoPlay_ListeningSearchSongByKey_Handler,
		},
		{
			MethodName: "ListeningGetSongListByType",
			Handler:    _ChannelListeningAutoPlay_ListeningGetSongListByType_Handler,
		},
		{
			MethodName: "ListeningGetSongListList",
			Handler:    _ChannelListeningAutoPlay_ListeningGetSongListList_Handler,
		},
		{
			MethodName: "ListeningGetPlayerUserInfo",
			Handler:    _ChannelListeningAutoPlay_ListeningGetPlayerUserInfo_Handler,
		},
		{
			MethodName: "ListeningOrderSong",
			Handler:    _ChannelListeningAutoPlay_ListeningOrderSong_Handler,
		},
		{
			MethodName: "ListeningGetPlayList",
			Handler:    _ChannelListeningAutoPlay_ListeningGetPlayList_Handler,
		},
		{
			MethodName: "ListeningGetSongResource",
			Handler:    _ChannelListeningAutoPlay_ListeningGetSongResource_Handler,
		},
		{
			MethodName: "WYYRedirectApi",
			Handler:    _ChannelListeningAutoPlay_WYYRedirectApi_Handler,
		},
		{
			MethodName: "ListeningReportRecord",
			Handler:    _ChannelListeningAutoPlay_ListeningReportRecord_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "channel-listening-auto-play/channel-listening-auto-play.proto",
}

func init() {
	proto.RegisterFile("channel-listening-auto-play/channel-listening-auto-play.proto", fileDescriptor_channel_listening_auto_play_022263866ee59b01)
}

var fileDescriptor_channel_listening_auto_play_022263866ee59b01 = []byte{
	// 2597 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x5a, 0xcf, 0x6f, 0x23, 0x49,
	0xf5, 0x77, 0xdb, 0xce, 0x0f, 0x3f, 0xc7, 0x4e, 0xa7, 0xf2, 0xcb, 0xeb, 0x99, 0xfd, 0x7e, 0x67,
	0x8b, 0xdd, 0x25, 0x13, 0x36, 0x09, 0x78, 0x34, 0x30, 0x9a, 0xfd, 0xa1, 0x4d, 0x3c, 0xc3, 0x60,
	0x4d, 0x7e, 0xa9, 0x93, 0x49, 0x94, 0x15, 0xc2, 0xea, 0xe9, 0xae, 0x71, 0x9a, 0x69, 0x77, 0x77,
	0xba, 0xda, 0xd9, 0x31, 0x1c, 0x56, 0x80, 0x84, 0x84, 0xe0, 0x02, 0x47, 0x84, 0x84, 0xe0, 0x3f,
	0xe0, 0xc0, 0x11, 0x0e, 0x9c, 0xe0, 0xce, 0x05, 0x89, 0x3f, 0x06, 0x55, 0x55, 0x77, 0xbb, 0xdb,
	0xdd, 0xee, 0x74, 0xbc, 0xc0, 0x89, 0x83, 0xa5, 0xaa, 0x57, 0xf5, 0x5e, 0xbd, 0x7a, 0xf5, 0xa9,
	0x57, 0xef, 0xbd, 0x36, 0x7c, 0xac, 0x5d, 0xaa, 0x96, 0x45, 0xcc, 0x2d, 0xd3, 0xa0, 0x1e, 0xb1,
	0x0c, 0xab, 0xb7, 0xa5, 0x0e, 0x3c, 0x7b, 0xcb, 0x31, 0xd5, 0xe1, 0x4e, 0xc6, 0xd8, 0xb6, 0xe3,
	0xda, 0x9e, 0x8d, 0x1a, 0xfe, 0x94, 0x70, 0x06, 0x9b, 0xc0, 0xc6, 0xb1, 0x0c, 0x75, 0x85, 0x68,
	0x43, 0xcd, 0x24, 0x07, 0xc4, 0x1a, 0x28, 0xe4, 0x0a, 0x2f, 0xc1, 0x62, 0x8c, 0x42, 0x1d, 0xbc,
	0x02, 0xc8, 0x27, 0x1d, 0x9b, 0xea, 0xb0, 0x63, 0xbd, 0xb2, 0xd9, 0xc4, 0x55, 0x58, 0x4e, 0x50,
	0xa9, 0x83, 0x3f, 0x81, 0x3b, 0x7b, 0xaa, 0xa7, 0x5d, 0xb6, 0xc5, 0x92, 0x6c, 0xcc, 0xb0, 0x7a,
	0x07, 0x03, 0x6a, 0x68, 0x0a, 0xb9, 0x42, 0xff, 0x0f, 0x55, 0x5f, 0x99, 0xae, 0xa1, 0xd3, 0x86,
	0x74, 0xaf, 0xb4, 0x51, 0x53, 0xc0, 0x27, 0x75, 0x74, 0x8a, 0x7f, 0x54, 0x84, 0xbb, 0x93, 0x05,
	0x50, 0x07, 0x39, 0x50, 0x0f, 0x24, 0xf4, 0x19, 0x51, 0x08, 0xa9, 0xb6, 0x3a, 0xdb, 0x93, 0x76,
	0xb9, 0x9d, 0x25, 0x6f, 0xdb, 0xa7, 0x73, 0x02, 0x7d, 0x6a, 0x79, 0xee, 0x50, 0xa9, 0x69, 0x51,
	0x5a, 0xd3, 0x06, 0x94, 0x9c, 0x84, 0x64, 0x28, 0xbd, 0x26, 0xc3, 0x86, 0x74, 0x4f, 0xda, 0xa8,
	0x29, 0xac, 0x89, 0xda, 0x30, 0x73, 0xad, 0x9a, 0x03, 0xd2, 0x28, 0xde, 0x93, 0x36, 0xaa, 0xad,
	0xad, 0xc9, 0x0a, 0xa5, 0xe9, 0x22, 0x78, 0x1f, 0x17, 0x1f, 0x49, 0xf8, 0x0b, 0x58, 0x4e, 0x99,
	0x81, 0x10, 0x94, 0x2d, 0xb5, 0x4f, 0xf8, 0x92, 0x15, 0x85, 0xb7, 0xd1, 0x1a, 0xcc, 0xaa, 0x03,
	0xef, 0xd2, 0x76, 0xf9, 0xa2, 0x15, 0xc5, 0xef, 0xa1, 0x8f, 0x60, 0x96, 0x7a, 0xaa, 0x37, 0xa0,
	0x8d, 0xd2, 0x3d, 0x69, 0xa3, 0xde, 0x7a, 0x77, 0xb2, 0x32, 0x6c, 0x8d, 0x13, 0x3e, 0x57, 0xf1,
	0x79, 0xf0, 0x33, 0x58, 0x89, 0x28, 0xe0, 0x0f, 0x92, 0x2b, 0xb6, 0xe7, 0x81, 0xa1, 0x07, 0x7b,
	0x1e, 0x18, 0x3a, 0x7a, 0x1b, 0x60, 0x74, 0x9e, 0x5c, 0x87, 0x9a, 0x52, 0x09, 0x8f, 0x13, 0xff,
	0x5d, 0x82, 0xd5, 0x14, 0x49, 0xd4, 0x41, 0x1f, 0x43, 0xb9, 0x6f, 0xeb, 0x62, 0x33, 0xf5, 0xd6,
	0xfd, 0x5c, 0xb6, 0x3a, 0xb0, 0x75, 0xa2, 0x70, 0x36, 0xf4, 0x29, 0xcc, 0xd2, 0xcf, 0x0d, 0x4f,
	0xbb, 0xe4, 0x6b, 0xd6, 0x5b, 0x1b, 0x93, 0x05, 0xec, 0x0e, 0x3c, 0x9b, 0x2f, 0xce, 0xe7, 0x2b,
	0x3e, 0x1f, 0x7a, 0x0c, 0x65, 0xc3, 0x7a, 0x65, 0x73, 0xfb, 0x54, 0x5b, 0xef, 0xdf, 0xcc, 0xcf,
	0x21, 0xce, 0x79, 0xf0, 0x3f, 0x25, 0x58, 0x88, 0x92, 0xd1, 0x33, 0x28, 0xf7, 0x89, 0x35, 0xe0,
	0xbb, 0xa9, 0xb6, 0x1e, 0x4c, 0x16, 0xb6, 0x1f, 0x50, 0x02, 0x76, 0x45, 0xeb, 0xeb, 0xfc, 0xa6,
	0x71, 0x01, 0x01, 0xaa, 0xd8, 0xa6, 0x4a, 0x02, 0x55, 0x77, 0xa0, 0xc2, 0x38, 0xbb, 0x9e, 0xd1,
	0x27, 0x5c, 0xd9, 0x92, 0x32, 0xcf, 0x08, 0xa7, 0x46, 0x9f, 0x44, 0x8e, 0xb9, 0x7c, 0xfb, 0x63,
	0x66, 0xe0, 0x39, 0xb3, 0xcd, 0x41, 0x9f, 0x34, 0x66, 0xf8, 0xc1, 0xf9, 0x3d, 0xfc, 0xc7, 0x22,
	0x54, 0x38, 0xe4, 0xf8, 0xde, 0xd8, 0x11, 0x9b, 0x06, 0xb1, 0xbc, 0x6e, 0x80, 0xf7, 0x8a, 0x52,
	0x11, 0x94, 0xe7, 0x64, 0x18, 0xa2, 0xb2, 0x98, 0x8a, 0xca, 0x52, 0x0c, 0x95, 0x3e, 0x7e, 0xca,
	0x23, 0xfc, 0xac, 0xc1, 0xec, 0x75, 0x4c, 0x05, 0xd1, 0x0b, 0xec, 0x30, 0x3b, 0xb2, 0x43, 0x03,
	0xe6, 0x54, 0x4d, 0xb3, 0x07, 0x96, 0xd7, 0x98, 0xe3, 0x42, 0x83, 0x2e, 0x7a, 0x0b, 0xe6, 0x0d,
	0xda, 0x35, 0x6d, 0x4d, 0x35, 0x1b, 0xf3, 0x5c, 0xca, 0x9c, 0x41, 0xf7, 0x59, 0x97, 0x89, 0xf7,
	0xed, 0x53, 0x11, 0xe2, 0xfd, 0x9d, 0x37, 0x61, 0xde, 0x32, 0xb4, 0xd7, 0x5c, 0x71, 0xe0, 0xd2,
	0xc2, 0x3e, 0xdb, 0x2f, 0x77, 0x2c, 0x5d, 0x6f, 0xe8, 0x90, 0x46, 0x55, 0x40, 0x9a, 0x53, 0x4e,
	0x87, 0x0e, 0x61, 0xab, 0x89, 0x61, 0x43, 0x6f, 0x2c, 0x08, 0x45, 0x78, 0xbf, 0xa3, 0xe3, 0x9f,
	0x49, 0xb0, 0x22, 0x50, 0x16, 0x01, 0xed, 0x34, 0xf7, 0x26, 0x02, 0xef, 0xd2, 0x74, 0xf0, 0xc6,
	0xeb, 0xb0, 0x9a, 0xa2, 0x0a, 0x75, 0xf0, 0x23, 0x58, 0xf7, 0x49, 0x1c, 0x7a, 0x4c, 0x75, 0xdf,
	0xf7, 0x8f, 0x29, 0x25, 0x8d, 0x5f, 0x66, 0x0d, 0x1a, 0xe9, 0x9c, 0xd4, 0xf9, 0xb7, 0x5d, 0x00,
	0xfc, 0x0b, 0x09, 0xde, 0x9a, 0x38, 0x07, 0xd5, 0xa1, 0xe8, 0x6b, 0x56, 0x51, 0x8a, 0x86, 0x9e,
	0x0a, 0xbe, 0x3a, 0x14, 0x5f, 0xf6, 0x7c, 0xe0, 0x15, 0x5f, 0xf6, 0xd0, 0x87, 0x30, 0xeb, 0x3f,
	0x14, 0x65, 0xfe, 0x50, 0x7c, 0x65, 0xb2, 0x72, 0x21, 0xe8, 0x15, 0x9f, 0x05, 0x9f, 0xc3, 0xc2,
	0x09, 0xf1, 0xc4, 0xbd, 0x98, 0xea, 0x24, 0x47, 0x00, 0x2f, 0x45, 0x01, 0x8e, 0x17, 0xa1, 0x16,
	0x11, 0x4c, 0x1d, 0xfc, 0x43, 0x40, 0xed, 0x81, 0xc7, 0x76, 0xcc, 0xdc, 0xdc, 0x89, 0x6d, 0xf5,
	0xa6, 0x5a, 0x0f, 0x43, 0x4d, 0xc0, 0x93, 0x59, 0x93, 0xcd, 0x10, 0x86, 0xa8, 0xf6, 0x83, 0xe3,
	0xea, 0xe8, 0xc1, 0xe5, 0x2a, 0x87, 0x97, 0x8b, 0x3d, 0xe6, 0x89, 0xc5, 0xa9, 0x83, 0x7f, 0x55,
	0x84, 0x7b, 0x0a, 0x71, 0x6c, 0xd7, 0xf3, 0x0f, 0x9e, 0xcd, 0x60, 0xa3, 0xc7, 0xae, 0xdd, 0x73,
	0x09, 0xa5, 0xff, 0x3d, 0x15, 0x91, 0x06, 0xf3, 0x8e, 0xbf, 0x2a, 0xf7, 0x15, 0xf5, 0xd6, 0xb3,
	0xc9, 0x07, 0x79, 0x93, 0xd2, 0xdb, 0x41, 0x9b, 0x5d, 0x69, 0x25, 0x14, 0x8c, 0x31, 0x2c, 0x44,
	0x47, 0x50, 0x05, 0x66, 0xa8, 0xa7, 0xba, 0x9e, 0x5c, 0x40, 0x73, 0x50, 0x22, 0x96, 0x2e, 0x4b,
	0xf8, 0x21, 0xbc, 0x73, 0x83, 0x78, 0xea, 0x24, 0x8d, 0x82, 0xff, 0x24, 0xc1, 0xea, 0x09, 0xf1,
	0xc6, 0x9f, 0xb3, 0x69, 0x0c, 0x18, 0xbc, 0x9d, 0xa5, 0xe9, 0xde, 0xce, 0x84, 0xfd, 0xcb, 0x13,
	0xed, 0x3f, 0x33, 0x82, 0x48, 0x03, 0xd6, 0xd2, 0xd4, 0xa7, 0x0e, 0xfe, 0xb3, 0x04, 0xeb, 0xf1,
	0xa1, 0xe9, 0x23, 0x86, 0x29, 0xc1, 0x31, 0x7a, 0x07, 0x67, 0xa6, 0x08, 0x77, 0x9a, 0xd0, 0x48,
	0xd7, 0x9f, 0x3a, 0xf8, 0x0a, 0x56, 0x82, 0x03, 0x16, 0xd4, 0x43, 0xdb, 0x33, 0x5e, 0x0d, 0x6f,
	0xf0, 0x95, 0x61, 0x74, 0x51, 0x9c, 0x22, 0xba, 0xf8, 0x47, 0x3c, 0x68, 0x62, 0x76, 0xce, 0xb7,
	0x68, 0x80, 0x8b, 0xe2, 0x74, 0xb8, 0xf8, 0x12, 0x11, 0x11, 0x7a, 0x1f, 0x16, 0xc5, 0xb1, 0xb1,
	0xd9, 0x5d, 0xae, 0x85, 0x78, 0xe5, 0xc5, 0x69, 0x32, 0x8f, 0xce, 0x56, 0xc2, 0xfb, 0x61, 0x68,
	0x2b, 0x5c, 0x5f, 0xbe, 0x8d, 0x8d, 0x9c, 0x68, 0x31, 0xe6, 0x44, 0x7f, 0x53, 0x84, 0x6a, 0xe8,
	0xb3, 0xcf, 0x5a, 0xff, 0x0b, 0x55, 0x62, 0xa1, 0x0a, 0xfe, 0x7d, 0x11, 0xaa, 0xe7, 0x17, 0x17,
	0x2f, 0x28, 0x71, 0x79, 0x24, 0x37, 0xfe, 0x7a, 0x46, 0x45, 0x17, 0x93, 0xa2, 0xd5, 0x6b, 0xd5,
	0x53, 0xdd, 0xee, 0xc0, 0x35, 0x7d, 0xdb, 0x54, 0x04, 0xe5, 0x85, 0xcb, 0xb5, 0xed, 0x11, 0x4b,
	0x27, 0xae, 0x6f, 0x21, 0xbf, 0x87, 0xee, 0x42, 0x85, 0x1a, 0x3d, 0x4b, 0xf5, 0x06, 0xae, 0xb0,
	0x53, 0x45, 0x19, 0x11, 0x50, 0x1b, 0xe0, 0xda, 0x70, 0xba, 0x3a, 0xf1, 0x54, 0xc3, 0x6c, 0xcc,
	0xf2, 0xe7, 0x38, 0xe3, 0xaa, 0x9e, 0x5f, 0x5c, 0x9c, 0x19, 0x0e, 0x47, 0x59, 0xe5, 0xda, 0x70,
	0x9e, 0x70, 0x36, 0xe6, 0x21, 0x5c, 0xa2, 0x77, 0x99, 0x20, 0x93, 0x5c, 0x13, 0x93, 0xdb, 0xb8,
	0xa6, 0x54, 0x5d, 0xa2, 0x9f, 0x19, 0xce, 0x3e, 0x23, 0xa1, 0xfb, 0xb0, 0x14, 0x9b, 0xd3, 0x35,
	0xfa, 0x3d, 0x6e, 0xf0, 0x8a, 0x52, 0x8f, 0xcc, 0xeb, 0xf4, 0x7b, 0x78, 0x17, 0x60, 0xb4, 0x0e,
	0x83, 0x08, 0xb7, 0xa5, 0x80, 0x20, 0x6f, 0xb3, 0x9c, 0x95, 0xbc, 0x71, 0x0c, 0x97, 0x88, 0x18,
	0x5c, 0xc4, 0xe6, 0x20, 0x48, 0x2c, 0x0a, 0xc7, 0x0f, 0xa0, 0x76, 0x7e, 0x71, 0xc1, 0x5c, 0xc4,
	0xae, 0xeb, 0x19, 0xd4, 0xcb, 0x13, 0xa6, 0xe0, 0x16, 0x2c, 0x04, 0x4c, 0xe6, 0xcb, 0x41, 0x3f,
	0x17, 0x8f, 0x0d, 0x8b, 0x3e, 0x8f, 0x42, 0xa8, 0x3d, 0x70, 0x35, 0x92, 0x60, 0x63, 0xb8, 0x75,
	0x4d, 0x9f, 0x8b, 0x35, 0x99, 0x20, 0x6a, 0xfc, 0x20, 0x88, 0x3f, 0x78, 0x9b, 0xcd, 0xea, 0xeb,
	0x0f, 0x7d, 0xc7, 0xcf, 0x9a, 0x68, 0x05, 0x66, 0x84, 0x35, 0xc5, 0xa1, 0x89, 0x0e, 0x56, 0xa1,
	0x11, 0x06, 0x63, 0xec, 0xde, 0xf6, 0x78, 0xb2, 0x4f, 0xdc, 0x9b, 0xa3, 0xc5, 0x34, 0x8f, 0x50,
	0x4c, 0xf3, 0x08, 0xed, 0x48, 0xbc, 0x17, 0x5f, 0x82, 0x3a, 0x69, 0x42, 0xa4, 0x34, 0x21, 0x1f,
	0xc1, 0x9d, 0x50, 0xc8, 0x33, 0xe2, 0x09, 0x09, 0xa3, 0x57, 0xe8, 0x86, 0xc0, 0xf6, 0xbb, 0x70,
	0x77, 0x32, 0x77, 0x7e, 0x2d, 0x98, 0x0d, 0x35, 0xd3, 0xa6, 0x62, 0xa3, 0xf3, 0x8a, 0xe8, 0xe0,
	0xef, 0x47, 0x74, 0x3b, 0x21, 0xaa, 0xab, 0x5d, 0xb2, 0x03, 0xdc, 0x1b, 0x3e, 0x27, 0xc3, 0x1c,
	0x66, 0x6c, 0xc0, 0xdc, 0x6b, 0x32, 0xfc, 0xdc, 0x76, 0x75, 0xff, 0x4c, 0x83, 0x2e, 0x3b, 0x57,
	0x47, 0xed, 0x85, 0xe7, 0xca, 0xda, 0xf8, 0xb7, 0x52, 0x64, 0x2b, 0x89, 0xc5, 0xa8, 0x83, 0x0e,
	0x83, 0xad, 0x30, 0xaf, 0xcd, 0xf7, 0xe3, 0x97, 0x4f, 0xde, 0xcf, 0xbc, 0x86, 0xa3, 0xc0, 0x58,
	0x6c, 0x99, 0x35, 0xd9, 0x3a, 0xcc, 0xa1, 0x5d, 0xaa, 0xb4, 0xdb, 0xb7, 0xdd, 0x60, 0xd7, 0x73,
	0x97, 0x2a, 0x3d, 0xb0, 0x5d, 0x61, 0x0d, 0xee, 0x03, 0x85, 0x82, 0xa2, 0x83, 0x5f, 0xc1, 0xdb,
	0x51, 0x5b, 0x33, 0xed, 0x58, 0x7f, 0x6f, 0xc8, 0x23, 0xb1, 0x9b, 0xed, 0x11, 0x5c, 0xd0, 0x62,
	0xe4, 0x82, 0xa6, 0x59, 0xe2, 0x77, 0x12, 0xfc, 0x5f, 0xd6, 0x42, 0xff, 0x01, 0x5b, 0x84, 0x1b,
	0x2e, 0x46, 0x36, 0x1c, 0xb3, 0x50, 0x29, 0x66, 0xa1, 0x71, 0xd4, 0x06, 0x2a, 0xb2, 0x5f, 0x0e,
	0xd4, 0x7e, 0x16, 0x47, 0x6d, 0x9c, 0x9b, 0x3a, 0xec, 0x39, 0xcf, 0xb7, 0xa7, 0x18, 0x27, 0xe7,
	0x61, 0x99, 0xec, 0x42, 0x94, 0xcc, 0x9c, 0xae, 0x66, 0x5f, 0x13, 0x97, 0x39, 0x52, 0xfe, 0x22,
	0x08, 0x8f, 0x53, 0xe5, 0xc4, 0x4e, 0xbf, 0xf7, 0x42, 0x38, 0x9a, 0xc4, 0xf3, 0x1a, 0x1c, 0x57,
	0x29, 0x72, 0x5c, 0x5b, 0xb0, 0x6c, 0x1a, 0xbd, 0x4b, 0xaf, 0x1b, 0x97, 0x28, 0x9c, 0x91, 0xcc,
	0x87, 0xda, 0x23, 0xb1, 0xf8, 0x8a, 0x3b, 0xca, 0x51, 0x3d, 0x62, 0x0f, 0x2a, 0x2f, 0x55, 0x4a,
	0xf8, 0xa9, 0xf9, 0xf9, 0xe6, 0x7b, 0x39, 0x52, 0xba, 0xb3, 0x96, 0x32, 0xcf, 0xf8, 0x82, 0x9a,
	0x86, 0xca, 0xbc, 0x6e, 0x37, 0xa2, 0x70, 0x85, 0x53, 0x0e, 0x99, 0x9f, 0xfd, 0xa9, 0x04, 0xab,
	0xa1, 0x6d, 0x8f, 0x5c, 0x9d, 0xb8, 0x41, 0x3e, 0x76, 0x03, 0x3a, 0x53, 0x20, 0x55, 0xfc, 0x12,
	0x90, 0xc2, 0x7f, 0x91, 0x60, 0x2d, 0x4d, 0x11, 0xea, 0xa0, 0x63, 0x90, 0xc5, 0x52, 0xaa, 0xae,
	0x13, 0x7d, 0x1a, 0xf8, 0xd6, 0x39, 0xff, 0x2e, 0x63, 0xe7, 0x67, 0xcc, 0xdc, 0x9c, 0xfa, 0x46,
	0x54, 0x55, 0xbb, 0x51, 0x24, 0xd7, 0xfa, 0xea, 0x1b, 0xce, 0xd6, 0xe6, 0x88, 0xde, 0x86, 0x65,
	0x6d, 0xe0, 0xba, 0x2c, 0xcc, 0x8a, 0xce, 0x15, 0x47, 0xbc, 0xe4, 0x0f, 0x8d, 0xe6, 0xe3, 0x47,
	0xb0, 0x3e, 0xee, 0x5e, 0x73, 0x42, 0x3c, 0xfa, 0xfc, 0xc4, 0x38, 0xa9, 0x83, 0x9e, 0x06, 0xb1,
	0xcf, 0x14, 0x3b, 0xaf, 0x84, 0x7e, 0x1b, 0x7f, 0x12, 0xf7, 0x47, 0xc2, 0xf7, 0x07, 0x11, 0x53,
	0x0e, 0x15, 0xf5, 0xb8, 0x9b, 0x19, 0xe7, 0xa7, 0x0e, 0xc3, 0xeb, 0x80, 0x32, 0x9c, 0xe7, 0xc2,
	0x6b, 0x24, 0x5e, 0x53, 0xe6, 0x07, 0x7e, 0x0b, 0xbf, 0x48, 0x7a, 0x8a, 0x20, 0x02, 0xc8, 0x81,
	0xca, 0x75, 0x98, 0xa3, 0xb6, 0xd5, 0x0b, 0xf2, 0xad, 0x8a, 0x32, 0xcb, 0xba, 0x1d, 0x1d, 0x5b,
	0x49, 0x17, 0x32, 0x12, 0xcb, 0x3d, 0x64, 0x8d, 0x33, 0xba, 0x3e, 0xd1, 0x57, 0xff, 0x7e, 0xa6,
	0xfa, 0x31, 0x29, 0x0b, 0x34, 0xd2, 0xc3, 0x1f, 0xc3, 0xd2, 0xf9, 0xc5, 0x85, 0x42, 0x74, 0xc3,
	0x25, 0x9a, 0xb7, 0xeb, 0x18, 0x4c, 0x79, 0x04, 0x65, 0x2d, 0x78, 0x52, 0x2b, 0x0a, 0x6f, 0x33,
	0x57, 0xca, 0xc2, 0xdf, 0xe0, 0x6a, 0x8a, 0x0e, 0x5e, 0x01, 0x34, 0xce, 0x4e, 0x1d, 0xfc, 0x57,
	0x29, 0x82, 0x12, 0x91, 0x99, 0x2b, 0x44, 0xb3, 0x5d, 0x3d, 0x87, 0x65, 0x58, 0xf4, 0xaf, 0x79,
	0x86, 0x6d, 0x85, 0xe5, 0x73, 0xde, 0x43, 0xef, 0x42, 0x9d, 0xe7, 0xfd, 0x5d, 0xd3, 0xee, 0x8d,
	0x2a, 0xaf, 0x65, 0x65, 0x81, 0x53, 0xf7, 0xed, 0x1e, 0xaf, 0xbe, 0x46, 0xec, 0x5a, 0x8e, 0xda,
	0x95, 0x85, 0x57, 0xaa, 0xd9, 0xf3, 0x43, 0x29, 0xd6, 0xe4, 0x7e, 0x90, 0x89, 0x99, 0xe5, 0x62,
	0x78, 0x9b, 0xcd, 0x22, 0x96, 0xee, 0xa7, 0x08, 0xac, 0x89, 0xef, 0x44, 0x62, 0xa1, 0xf8, 0x4e,
	0xa8, 0xb3, 0xf9, 0x09, 0x2c, 0x8e, 0xe5, 0x6d, 0xa8, 0xce, 0x76, 0x67, 0xdb, 0x94, 0xb0, 0x9e,
	0x5c, 0x40, 0x0b, 0x30, 0x1f, 0xd4, 0x70, 0x64, 0x09, 0xd5, 0xa0, 0xc2, 0x5d, 0x06, 0xef, 0x16,
	0x37, 0xdf, 0x83, 0x7a, 0xbc, 0x56, 0x88, 0x2a, 0x30, 0xd3, 0x66, 0x21, 0x8a, 0x5c, 0x40, 0xf3,
	0x50, 0x3e, 0x72, 0x88, 0x25, 0x4b, 0x9b, 0xef, 0x00, 0x8c, 0x52, 0x60, 0x46, 0x67, 0xe7, 0x2a,
	0x17, 0xd8, 0x64, 0x47, 0x1d, 0x50, 0x22, 0x4b, 0x9b, 0x7f, 0x93, 0x60, 0x59, 0xc4, 0xcc, 0x8c,
	0x47, 0x84, 0xe5, 0x4f, 0x3c, 0x1b, 0x35, 0x60, 0xe5, 0xe9, 0xe1, 0x8b, 0x83, 0xee, 0x59, 0xe7,
	0xb8, 0x7b, 0x7a, 0x71, 0xfc, 0xb4, 0x7b, 0x78, 0xa4, 0x1c, 0xec, 0xee, 0xcb, 0x05, 0x74, 0x07,
	0xd6, 0xe3, 0x23, 0x7b, 0xfb, 0xbb, 0xed, 0xe7, 0xac, 0x2b, 0xcf, 0xa2, 0xbb, 0xd0, 0x48, 0x1b,
	0x3c, 0x61, 0xa3, 0x8b, 0x68, 0x15, 0x96, 0xe2, 0xa3, 0xed, 0x5d, 0x45, 0xae, 0xa1, 0x75, 0x58,
	0x8e, 0x93, 0xcf, 0x77, 0x4f, 0xdb, 0xdf, 0x91, 0x65, 0xb4, 0x02, 0x72, 0x7c, 0xe0, 0xf4, 0x4c,
	0x5e, 0x4a, 0x4e, 0x57, 0x76, 0x9f, 0x74, 0x8e, 0x64, 0xb4, 0xf9, 0x29, 0xd4, 0x0e, 0x02, 0x67,
	0xe0, 0xd7, 0x77, 0x57, 0x63, 0x84, 0xee, 0x13, 0xf2, 0x4a, 0x1d, 0x98, 0x9e, 0x5c, 0x60, 0xa2,
	0xe3, 0x43, 0x87, 0x6d, 0x59, 0xda, 0xfc, 0x49, 0xe4, 0xad, 0xe4, 0x12, 0x82, 0xfd, 0x9c, 0x1c,
	0x1d, 0x3e, 0xeb, 0xee, 0x77, 0x4e, 0x4e, 0xc5, 0x8a, 0xfb, 0x9d, 0xe7, 0x4f, 0xe5, 0xc2, 0xa4,
	0x51, 0xa5, 0x7d, 0xf0, 0x44, 0x96, 0x42, 0x43, 0x8d, 0x8d, 0x9e, 0x1e, 0x1d, 0xcb, 0x45, 0xd4,
	0x84, 0xb5, 0xb4, 0xc1, 0x6f, 0x1f, 0xc8, 0xa5, 0xd6, 0x1f, 0xd6, 0xc2, 0xea, 0x6c, 0xa2, 0x7c,
	0x8a, 0xae, 0x61, 0x29, 0x51, 0xdd, 0x40, 0xdb, 0xb9, 0xea, 0x03, 0x61, 0x10, 0xdd, 0xdc, 0xb9,
	0xd5, 0x7c, 0xea, 0xe0, 0x02, 0xfa, 0xb9, 0x04, 0x8d, 0x49, 0x1f, 0xdf, 0xd0, 0xc3, 0x69, 0x3e,
	0xd8, 0x5d, 0x35, 0xbf, 0x39, 0xdd, 0x77, 0x3e, 0x5c, 0x60, 0x56, 0x48, 0x94, 0xc4, 0xb3, 0xac,
	0x90, 0x56, 0xca, 0xcf, 0xb2, 0x42, 0x7a, 0xbd, 0xbd, 0x80, 0x1c, 0x58, 0x1c, 0x2b, 0xae, 0xa2,
	0x0f, 0x32, 0x6c, 0x99, 0x28, 0x02, 0x37, 0xb7, 0x6e, 0x31, 0x9b, 0xaf, 0xf8, 0x6b, 0x09, 0xde,
	0xce, 0xac, 0x51, 0xa2, 0xc7, 0xd3, 0xd7, 0x4e, 0x9b, 0x1f, 0x4e, 0xcd, 0xcb, 0x95, 0xfb, 0x22,
	0xfc, 0xb8, 0x18, 0xfb, 0x8c, 0x80, 0xbe, 0x71, 0x23, 0xbe, 0xc6, 0x3f, 0x58, 0x34, 0x5b, 0xb7,
	0x65, 0xe1, 0x0a, 0x7c, 0x0f, 0x2a, 0x61, 0xe9, 0x1d, 0x65, 0xc5, 0xc5, 0x91, 0xc2, 0x7f, 0xf3,
	0xab, 0xb9, 0xe6, 0x71, 0xf9, 0x43, 0x40, 0xc9, 0x4a, 0x29, 0xda, 0xc9, 0x14, 0x90, 0x2c, 0x0b,
	0x37, 0xbf, 0x7e, 0x3b, 0x86, 0xc0, 0xb6, 0x69, 0x95, 0xcc, 0x2c, 0xdb, 0x4e, 0xa8, 0xdc, 0x66,
	0xd9, 0x76, 0x62, 0xb1, 0x94, 0x63, 0x7d, 0xec, 0x5f, 0x01, 0x59, 0x58, 0x4f, 0xfe, 0xad, 0x20,
	0x0b, 0xeb, 0x69, 0x7f, 0x37, 0x28, 0x20, 0x1d, 0xaa, 0x91, 0x3f, 0x2c, 0xa0, 0x8d, 0x1b, 0xf9,
	0x03, 0xf0, 0xdc, 0xcf, 0x39, 0x93, 0xaf, 0xf2, 0xe3, 0x68, 0x46, 0x10, 0x2d, 0x53, 0xa0, 0x56,
	0x8e, 0x6f, 0x5d, 0x63, 0xa5, 0x93, 0xe6, 0x83, 0x5b, 0xf3, 0x84, 0xee, 0x74, 0x52, 0xa1, 0x22,
	0xcb, 0x9d, 0x66, 0x94, 0x46, 0xb2, 0xdc, 0x69, 0x56, 0x4d, 0x64, 0x5c, 0x9b, 0xb1, 0x5a, 0x43,
	0x2e, 0x6d, 0x92, 0xc5, 0x90, 0x5c, 0xda, 0xa4, 0x94, 0x35, 0x70, 0x01, 0xfd, 0x52, 0x82, 0xe6,
	0xe4, 0x7c, 0x1f, 0x7d, 0x2b, 0xdf, 0x36, 0x13, 0xe5, 0x88, 0xe6, 0xa3, 0xe9, 0x18, 0x53, 0xcf,
	0x2b, 0x96, 0x51, 0x3f, 0xbc, 0x9d, 0x60, 0x3f, 0x63, 0xca, 0x7b, 0x5e, 0xe3, 0xd5, 0x80, 0x14,
	0x0b, 0xc5, 0x53, 0x95, 0xbc, 0x16, 0x4a, 0x24, 0x48, 0x79, 0x2d, 0x94, 0xcc, 0x8c, 0x84, 0xab,
	0x4c, 0xa6, 0xb7, 0x59, 0xae, 0x32, 0x35, 0x2b, 0xcf, 0x72, 0x95, 0xe9, 0xd9, 0xb3, 0x70, 0x95,
	0x69, 0xb9, 0x65, 0x96, 0xab, 0x9c, 0x90, 0xc5, 0x36, 0x5b, 0xb7, 0x65, 0x99, 0x88, 0x8e, 0xb0,
	0xac, 0x7b, 0x0b, 0x74, 0x44, 0x12, 0xc1, 0xdb, 0xa0, 0x23, 0x9a, 0xe8, 0xe1, 0x02, 0xea, 0x43,
	0x3d, 0x9e, 0x5b, 0xa1, 0xaf, 0x65, 0x66, 0x79, 0xf1, 0x24, 0xae, 0xf9, 0x41, 0xfe, 0xc9, 0x49,
	0x7f, 0x1a, 0x4d, 0x75, 0x72, 0xf9, 0xd3, 0xb1, 0x2c, 0x2f, 0x97, 0x3f, 0x1d, 0xcf, 0xa7, 0x70,
	0x61, 0xef, 0xa3, 0xcf, 0x1e, 0xf7, 0x6c, 0x53, 0xb5, 0x7a, 0xdb, 0x0f, 0x5b, 0x9e, 0xb7, 0xad,
	0xd9, 0xfd, 0x1d, 0xfe, 0x87, 0x39, 0xcd, 0x36, 0x77, 0x28, 0x71, 0xaf, 0x0d, 0x8d, 0xd0, 0x9d,
	0x49, 0x92, 0x5f, 0xce, 0xf2, 0xb9, 0x0f, 0xfe, 0x15, 0x00, 0x00, 0xff, 0xff, 0xc0, 0xb7, 0xf1,
	0x5c, 0x9d, 0x27, 0x00, 0x00,
}
