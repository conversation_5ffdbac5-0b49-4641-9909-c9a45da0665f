// Code generated by protoc-gen-go. DO NOT EDIT.
// source: game-ticket-mgr/game-ticket-mgr.proto

package game_ticket_mgr // import "golang.52tt.com/protocol/services/game-ticket-mgr"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type GainSourceType int32

const (
	GainSourceType_UnknownGainSourceType GainSourceType = 0
	GainSourceType_GameTicketRecharge    GainSourceType = 1
	GainSourceType_InviteNewUser         GainSourceType = 2
	GainSourceType_LoginTask             GainSourceType = 3
	GainSourceType_GameAward             GainSourceType = 4
	GainSourceType_OperatorSend          GainSourceType = 5
	GainSourceType_OperatorSendTT        GainSourceType = 6
	GainSourceType_ActivitySendTT        GainSourceType = 7
)

var GainSourceType_name = map[int32]string{
	0: "UnknownGainSourceType",
	1: "GameTicketRecharge",
	2: "InviteNewUser",
	3: "LoginTask",
	4: "GameAward",
	5: "OperatorSend",
	6: "OperatorSendTT",
	7: "ActivitySendTT",
}
var GainSourceType_value = map[string]int32{
	"UnknownGainSourceType": 0,
	"GameTicketRecharge":    1,
	"InviteNewUser":         2,
	"LoginTask":             3,
	"GameAward":             4,
	"OperatorSend":          5,
	"OperatorSendTT":        6,
	"ActivitySendTT":        7,
}

func (x GainSourceType) String() string {
	return proto.EnumName(GainSourceType_name, int32(x))
}
func (GainSourceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{0}
}

type CostDestType int32

const (
	CostDestType_UnknownCostDestType CostDestType = 0
)

var CostDestType_name = map[int32]string{
	0: "UnknownCostDestType",
}
var CostDestType_value = map[string]int32{
	"UnknownCostDestType": 0,
}

func (x CostDestType) String() string {
	return proto.EnumName(CostDestType_name, int32(x))
}
func (CostDestType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{1}
}

type ExpireType int32

const (
	ExpireType_Forever           ExpireType = 0
	ExpireType_AbsoluteTimeSec   ExpireType = 1
	ExpireType_RelativeTimeSec   ExpireType = 2
	ExpireType_RelativeTimeMonth ExpireType = 3
)

var ExpireType_name = map[int32]string{
	0: "Forever",
	1: "AbsoluteTimeSec",
	2: "RelativeTimeSec",
	3: "RelativeTimeMonth",
}
var ExpireType_value = map[string]int32{
	"Forever":           0,
	"AbsoluteTimeSec":   1,
	"RelativeTimeSec":   2,
	"RelativeTimeMonth": 3,
}

func (x ExpireType) String() string {
	return proto.EnumName(ExpireType_name, int32(x))
}
func (ExpireType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{2}
}

type CostPriorityType int32

const (
	CostPriorityType_Free CostPriorityType = 0
	CostPriorityType_Paid CostPriorityType = 1
)

var CostPriorityType_name = map[int32]string{
	0: "Free",
	1: "Paid",
}
var CostPriorityType_value = map[string]int32{
	"Free": 0,
	"Paid": 1,
}

func (x CostPriorityType) String() string {
	return proto.EnumName(CostPriorityType_name, int32(x))
}
func (CostPriorityType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{3}
}

type FreezeStatus int32

const (
	FreezeStatus_Freeze   FreezeStatus = 0
	FreezeStatus_Commit   FreezeStatus = 1
	FreezeStatus_Rollback FreezeStatus = 2
)

var FreezeStatus_name = map[int32]string{
	0: "Freeze",
	1: "Commit",
	2: "Rollback",
}
var FreezeStatus_value = map[string]int32{
	"Freeze":   0,
	"Commit":   1,
	"Rollback": 2,
}

func (x FreezeStatus) String() string {
	return proto.EnumName(FreezeStatus_name, int32(x))
}
func (FreezeStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{4}
}

type CouponType int32

const (
	CouponType_COUPON_TYPE_UNSPECIFIED  CouponType = 0
	CouponType_COUPON_TYPE_DISCOUNT     CouponType = 1
	CouponType_COUPON_TYPE_REDUCTION    CouponType = 2
	CouponType_COUPON_TYPE_SINGLE_TRIAL CouponType = 3
	CouponType_COUPON_TYPE_FREE         CouponType = 4
)

var CouponType_name = map[int32]string{
	0: "COUPON_TYPE_UNSPECIFIED",
	1: "COUPON_TYPE_DISCOUNT",
	2: "COUPON_TYPE_REDUCTION",
	3: "COUPON_TYPE_SINGLE_TRIAL",
	4: "COUPON_TYPE_FREE",
}
var CouponType_value = map[string]int32{
	"COUPON_TYPE_UNSPECIFIED":  0,
	"COUPON_TYPE_DISCOUNT":     1,
	"COUPON_TYPE_REDUCTION":    2,
	"COUPON_TYPE_SINGLE_TRIAL": 3,
	"COUPON_TYPE_FREE":         4,
}

func (x CouponType) String() string {
	return proto.EnumName(CouponType_name, int32(x))
}
func (CouponType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{5}
}

type CouponGrantSource int32

const (
	CouponGrantSource_COUPON_GRANT_SOURCE_NONE        CouponGrantSource = 0
	CouponGrantSource_COUPON_GRANT_SOURCE_TT_ACTIVITY CouponGrantSource = 1
	CouponGrantSource_COUPON_GRANT_SOURCE_TT_ADMIN    CouponGrantSource = 2
	CouponGrantSource_COUPON_GRANT_SOURCE_MJ_ACTIVITY CouponGrantSource = 3
	CouponGrantSource_COUPON_GRANT_SOURCE_MJ_ADMIN    CouponGrantSource = 4
)

var CouponGrantSource_name = map[int32]string{
	0: "COUPON_GRANT_SOURCE_NONE",
	1: "COUPON_GRANT_SOURCE_TT_ACTIVITY",
	2: "COUPON_GRANT_SOURCE_TT_ADMIN",
	3: "COUPON_GRANT_SOURCE_MJ_ACTIVITY",
	4: "COUPON_GRANT_SOURCE_MJ_ADMIN",
}
var CouponGrantSource_value = map[string]int32{
	"COUPON_GRANT_SOURCE_NONE":        0,
	"COUPON_GRANT_SOURCE_TT_ACTIVITY": 1,
	"COUPON_GRANT_SOURCE_TT_ADMIN":    2,
	"COUPON_GRANT_SOURCE_MJ_ACTIVITY": 3,
	"COUPON_GRANT_SOURCE_MJ_ADMIN":    4,
}

func (x CouponGrantSource) String() string {
	return proto.EnumName(CouponGrantSource_name, int32(x))
}
func (CouponGrantSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{6}
}

type CouponStatus int32

const (
	CouponStatus_COUPON_STATUS_UNDEFINED CouponStatus = 0
	CouponStatus_COUPON_STATUS_UNUSED    CouponStatus = 1
	CouponStatus_COUPON_STATUS_USED      CouponStatus = 2
)

var CouponStatus_name = map[int32]string{
	0: "COUPON_STATUS_UNDEFINED",
	1: "COUPON_STATUS_UNUSED",
	2: "COUPON_STATUS_USED",
}
var CouponStatus_value = map[string]int32{
	"COUPON_STATUS_UNDEFINED": 0,
	"COUPON_STATUS_UNUSED":    1,
	"COUPON_STATUS_USED":      2,
}

func (x CouponStatus) String() string {
	return proto.EnumName(CouponStatus_name, int32(x))
}
func (CouponStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{7}
}

type CouponQueryType int32

const (
	CouponQueryType_COUPON_QUERY_TYPE_UNSPECIFIED CouponQueryType = 0
	CouponQueryType_COUPON_QUERY_TYPE_VALID       CouponQueryType = 1
	CouponQueryType_COUPON_QUERY_TYPE_INVALID     CouponQueryType = 2
)

var CouponQueryType_name = map[int32]string{
	0: "COUPON_QUERY_TYPE_UNSPECIFIED",
	1: "COUPON_QUERY_TYPE_VALID",
	2: "COUPON_QUERY_TYPE_INVALID",
}
var CouponQueryType_value = map[string]int32{
	"COUPON_QUERY_TYPE_UNSPECIFIED": 0,
	"COUPON_QUERY_TYPE_VALID":       1,
	"COUPON_QUERY_TYPE_INVALID":     2,
}

func (x CouponQueryType) String() string {
	return proto.EnumName(CouponQueryType_name, int32(x))
}
func (CouponQueryType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{8}
}

type GameTicketOrderQueryType int32

const (
	GameTicketOrderQueryType_GAME_TICKET_ORDER_QUERY_TYPE_UNSPECIFIED GameTicketOrderQueryType = 0
	GameTicketOrderQueryType_GAME_TICKET_ORDER_QUERY_TYPE_ALL         GameTicketOrderQueryType = 1
	GameTicketOrderQueryType_GAME_TICKET_ORDER_QUERY_TYPE_RECHARGE    GameTicketOrderQueryType = 2
	GameTicketOrderQueryType_GAME_TICKET_ORDER_QUERY_TYPE_CONSUME     GameTicketOrderQueryType = 3
)

var GameTicketOrderQueryType_name = map[int32]string{
	0: "GAME_TICKET_ORDER_QUERY_TYPE_UNSPECIFIED",
	1: "GAME_TICKET_ORDER_QUERY_TYPE_ALL",
	2: "GAME_TICKET_ORDER_QUERY_TYPE_RECHARGE",
	3: "GAME_TICKET_ORDER_QUERY_TYPE_CONSUME",
}
var GameTicketOrderQueryType_value = map[string]int32{
	"GAME_TICKET_ORDER_QUERY_TYPE_UNSPECIFIED": 0,
	"GAME_TICKET_ORDER_QUERY_TYPE_ALL":         1,
	"GAME_TICKET_ORDER_QUERY_TYPE_RECHARGE":    2,
	"GAME_TICKET_ORDER_QUERY_TYPE_CONSUME":     3,
}

func (x GameTicketOrderQueryType) String() string {
	return proto.EnumName(GameTicketOrderQueryType_name, int32(x))
}
func (GameTicketOrderQueryType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{9}
}

type GameTicketOrderStatus int32

const (
	GameTicketOrderStatus_GAME_TICKET_ORDER_STATUS_UNSPECIFIED GameTicketOrderStatus = 0
	GameTicketOrderStatus_GAME_TICKET_ORDER_STATUS_GAINED      GameTicketOrderStatus = 1
	GameTicketOrderStatus_GAME_TICKET_ORDER_STATUS_CONSUMED    GameTicketOrderStatus = 2
	GameTicketOrderStatus_GAME_TICKET_ORDER_STATUS_FREEZED     GameTicketOrderStatus = 3
	GameTicketOrderStatus_GAME_TICKET_ORDER_STATUS_ROLLBACK    GameTicketOrderStatus = 4
)

var GameTicketOrderStatus_name = map[int32]string{
	0: "GAME_TICKET_ORDER_STATUS_UNSPECIFIED",
	1: "GAME_TICKET_ORDER_STATUS_GAINED",
	2: "GAME_TICKET_ORDER_STATUS_CONSUMED",
	3: "GAME_TICKET_ORDER_STATUS_FREEZED",
	4: "GAME_TICKET_ORDER_STATUS_ROLLBACK",
}
var GameTicketOrderStatus_value = map[string]int32{
	"GAME_TICKET_ORDER_STATUS_UNSPECIFIED": 0,
	"GAME_TICKET_ORDER_STATUS_GAINED":      1,
	"GAME_TICKET_ORDER_STATUS_CONSUMED":    2,
	"GAME_TICKET_ORDER_STATUS_FREEZED":     3,
	"GAME_TICKET_ORDER_STATUS_ROLLBACK":    4,
}

func (x GameTicketOrderStatus) String() string {
	return proto.EnumName(GameTicketOrderStatus_name, int32(x))
}
func (GameTicketOrderStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{10}
}

type GameTicketCfg struct {
	TicketId             uint32     `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id,omitempty"`
	Name                 string     `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Desc                 string     `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	ExpireType           ExpireType `protobuf:"varint,4,opt,name=expire_type,json=expireType,proto3,enum=game_ticket_mgr.ExpireType" json:"expire_type,omitempty"`
	ExpireDuration       int64      `protobuf:"varint,5,opt,name=expire_duration,json=expireDuration,proto3" json:"expire_duration,omitempty"`
	Price                uint32     `protobuf:"varint,6,opt,name=price,proto3" json:"price,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GameTicketCfg) Reset()         { *m = GameTicketCfg{} }
func (m *GameTicketCfg) String() string { return proto.CompactTextString(m) }
func (*GameTicketCfg) ProtoMessage()    {}
func (*GameTicketCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{0}
}
func (m *GameTicketCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameTicketCfg.Unmarshal(m, b)
}
func (m *GameTicketCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameTicketCfg.Marshal(b, m, deterministic)
}
func (dst *GameTicketCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameTicketCfg.Merge(dst, src)
}
func (m *GameTicketCfg) XXX_Size() int {
	return xxx_messageInfo_GameTicketCfg.Size(m)
}
func (m *GameTicketCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_GameTicketCfg.DiscardUnknown(m)
}

var xxx_messageInfo_GameTicketCfg proto.InternalMessageInfo

func (m *GameTicketCfg) GetTicketId() uint32 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *GameTicketCfg) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GameTicketCfg) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *GameTicketCfg) GetExpireType() ExpireType {
	if m != nil {
		return m.ExpireType
	}
	return ExpireType_Forever
}

func (m *GameTicketCfg) GetExpireDuration() int64 {
	if m != nil {
		return m.ExpireDuration
	}
	return 0
}

func (m *GameTicketCfg) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

type AddGameTicketCfgResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddGameTicketCfgResp) Reset()         { *m = AddGameTicketCfgResp{} }
func (m *AddGameTicketCfgResp) String() string { return proto.CompactTextString(m) }
func (*AddGameTicketCfgResp) ProtoMessage()    {}
func (*AddGameTicketCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{1}
}
func (m *AddGameTicketCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddGameTicketCfgResp.Unmarshal(m, b)
}
func (m *AddGameTicketCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddGameTicketCfgResp.Marshal(b, m, deterministic)
}
func (dst *AddGameTicketCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddGameTicketCfgResp.Merge(dst, src)
}
func (m *AddGameTicketCfgResp) XXX_Size() int {
	return xxx_messageInfo_AddGameTicketCfgResp.Size(m)
}
func (m *AddGameTicketCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddGameTicketCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddGameTicketCfgResp proto.InternalMessageInfo

type DelGameTicketCfgReq struct {
	TicketId             uint32   `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelGameTicketCfgReq) Reset()         { *m = DelGameTicketCfgReq{} }
func (m *DelGameTicketCfgReq) String() string { return proto.CompactTextString(m) }
func (*DelGameTicketCfgReq) ProtoMessage()    {}
func (*DelGameTicketCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{2}
}
func (m *DelGameTicketCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelGameTicketCfgReq.Unmarshal(m, b)
}
func (m *DelGameTicketCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelGameTicketCfgReq.Marshal(b, m, deterministic)
}
func (dst *DelGameTicketCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelGameTicketCfgReq.Merge(dst, src)
}
func (m *DelGameTicketCfgReq) XXX_Size() int {
	return xxx_messageInfo_DelGameTicketCfgReq.Size(m)
}
func (m *DelGameTicketCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelGameTicketCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelGameTicketCfgReq proto.InternalMessageInfo

func (m *DelGameTicketCfgReq) GetTicketId() uint32 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

type DelGameTicketCfgResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelGameTicketCfgResp) Reset()         { *m = DelGameTicketCfgResp{} }
func (m *DelGameTicketCfgResp) String() string { return proto.CompactTextString(m) }
func (*DelGameTicketCfgResp) ProtoMessage()    {}
func (*DelGameTicketCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{3}
}
func (m *DelGameTicketCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelGameTicketCfgResp.Unmarshal(m, b)
}
func (m *DelGameTicketCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelGameTicketCfgResp.Marshal(b, m, deterministic)
}
func (dst *DelGameTicketCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelGameTicketCfgResp.Merge(dst, src)
}
func (m *DelGameTicketCfgResp) XXX_Size() int {
	return xxx_messageInfo_DelGameTicketCfgResp.Size(m)
}
func (m *DelGameTicketCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelGameTicketCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelGameTicketCfgResp proto.InternalMessageInfo

type GetAllGameTicketCfgReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllGameTicketCfgReq) Reset()         { *m = GetAllGameTicketCfgReq{} }
func (m *GetAllGameTicketCfgReq) String() string { return proto.CompactTextString(m) }
func (*GetAllGameTicketCfgReq) ProtoMessage()    {}
func (*GetAllGameTicketCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{4}
}
func (m *GetAllGameTicketCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllGameTicketCfgReq.Unmarshal(m, b)
}
func (m *GetAllGameTicketCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllGameTicketCfgReq.Marshal(b, m, deterministic)
}
func (dst *GetAllGameTicketCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllGameTicketCfgReq.Merge(dst, src)
}
func (m *GetAllGameTicketCfgReq) XXX_Size() int {
	return xxx_messageInfo_GetAllGameTicketCfgReq.Size(m)
}
func (m *GetAllGameTicketCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllGameTicketCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllGameTicketCfgReq proto.InternalMessageInfo

type GameTicketCfgList struct {
	CfgList              []*GameTicketCfg `protobuf:"bytes,1,rep,name=cfg_list,json=cfgList,proto3" json:"cfg_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GameTicketCfgList) Reset()         { *m = GameTicketCfgList{} }
func (m *GameTicketCfgList) String() string { return proto.CompactTextString(m) }
func (*GameTicketCfgList) ProtoMessage()    {}
func (*GameTicketCfgList) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{5}
}
func (m *GameTicketCfgList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameTicketCfgList.Unmarshal(m, b)
}
func (m *GameTicketCfgList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameTicketCfgList.Marshal(b, m, deterministic)
}
func (dst *GameTicketCfgList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameTicketCfgList.Merge(dst, src)
}
func (m *GameTicketCfgList) XXX_Size() int {
	return xxx_messageInfo_GameTicketCfgList.Size(m)
}
func (m *GameTicketCfgList) XXX_DiscardUnknown() {
	xxx_messageInfo_GameTicketCfgList.DiscardUnknown(m)
}

var xxx_messageInfo_GameTicketCfgList proto.InternalMessageInfo

func (m *GameTicketCfgList) GetCfgList() []*GameTicketCfg {
	if m != nil {
		return m.CfgList
	}
	return nil
}

type GetGameTicketCfgReq struct {
	TicketId             uint32   `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameTicketCfgReq) Reset()         { *m = GetGameTicketCfgReq{} }
func (m *GetGameTicketCfgReq) String() string { return proto.CompactTextString(m) }
func (*GetGameTicketCfgReq) ProtoMessage()    {}
func (*GetGameTicketCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{6}
}
func (m *GetGameTicketCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameTicketCfgReq.Unmarshal(m, b)
}
func (m *GetGameTicketCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameTicketCfgReq.Marshal(b, m, deterministic)
}
func (dst *GetGameTicketCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameTicketCfgReq.Merge(dst, src)
}
func (m *GetGameTicketCfgReq) XXX_Size() int {
	return xxx_messageInfo_GetGameTicketCfgReq.Size(m)
}
func (m *GetGameTicketCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameTicketCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameTicketCfgReq proto.InternalMessageInfo

func (m *GetGameTicketCfgReq) GetTicketId() uint32 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

type TicketRemainDetail struct {
	TicketId             uint32         `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id,omitempty"`
	Name                 string         `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	ExpireTime           int64          `protobuf:"varint,3,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	Source               GainSourceType `protobuf:"varint,4,opt,name=source,proto3,enum=game_ticket_mgr.GainSourceType" json:"source,omitempty"`
	Num                  uint32         `protobuf:"varint,5,opt,name=num,proto3" json:"num,omitempty"`
	Price                uint32         `protobuf:"varint,6,opt,name=price,proto3" json:"price,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *TicketRemainDetail) Reset()         { *m = TicketRemainDetail{} }
func (m *TicketRemainDetail) String() string { return proto.CompactTextString(m) }
func (*TicketRemainDetail) ProtoMessage()    {}
func (*TicketRemainDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{7}
}
func (m *TicketRemainDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketRemainDetail.Unmarshal(m, b)
}
func (m *TicketRemainDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketRemainDetail.Marshal(b, m, deterministic)
}
func (dst *TicketRemainDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketRemainDetail.Merge(dst, src)
}
func (m *TicketRemainDetail) XXX_Size() int {
	return xxx_messageInfo_TicketRemainDetail.Size(m)
}
func (m *TicketRemainDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketRemainDetail.DiscardUnknown(m)
}

var xxx_messageInfo_TicketRemainDetail proto.InternalMessageInfo

func (m *TicketRemainDetail) GetTicketId() uint32 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *TicketRemainDetail) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *TicketRemainDetail) GetExpireTime() int64 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *TicketRemainDetail) GetSource() GainSourceType {
	if m != nil {
		return m.Source
	}
	return GainSourceType_UnknownGainSourceType
}

func (m *TicketRemainDetail) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *TicketRemainDetail) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

// 详细信息，即按不同券、来源、过期时间分栏
type GetUserRemainDetailReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TicketId             uint32   `protobuf:"varint,2,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserRemainDetailReq) Reset()         { *m = GetUserRemainDetailReq{} }
func (m *GetUserRemainDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetUserRemainDetailReq) ProtoMessage()    {}
func (*GetUserRemainDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{8}
}
func (m *GetUserRemainDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRemainDetailReq.Unmarshal(m, b)
}
func (m *GetUserRemainDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRemainDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetUserRemainDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRemainDetailReq.Merge(dst, src)
}
func (m *GetUserRemainDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetUserRemainDetailReq.Size(m)
}
func (m *GetUserRemainDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRemainDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRemainDetailReq proto.InternalMessageInfo

func (m *GetUserRemainDetailReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserRemainDetailReq) GetTicketId() uint32 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

type UserRemainDetail struct {
	Uid                  uint32                `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	List                 []*TicketRemainDetail `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *UserRemainDetail) Reset()         { *m = UserRemainDetail{} }
func (m *UserRemainDetail) String() string { return proto.CompactTextString(m) }
func (*UserRemainDetail) ProtoMessage()    {}
func (*UserRemainDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{9}
}
func (m *UserRemainDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserRemainDetail.Unmarshal(m, b)
}
func (m *UserRemainDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserRemainDetail.Marshal(b, m, deterministic)
}
func (dst *UserRemainDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserRemainDetail.Merge(dst, src)
}
func (m *UserRemainDetail) XXX_Size() int {
	return xxx_messageInfo_UserRemainDetail.Size(m)
}
func (m *UserRemainDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_UserRemainDetail.DiscardUnknown(m)
}

var xxx_messageInfo_UserRemainDetail proto.InternalMessageInfo

func (m *UserRemainDetail) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserRemainDetail) GetList() []*TicketRemainDetail {
	if m != nil {
		return m.List
	}
	return nil
}

type RemainSummary struct {
	TicketId             uint32   `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Num                  uint32   `protobuf:"varint,3,opt,name=num,proto3" json:"num,omitempty"`
	Price                uint32   `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemainSummary) Reset()         { *m = RemainSummary{} }
func (m *RemainSummary) String() string { return proto.CompactTextString(m) }
func (*RemainSummary) ProtoMessage()    {}
func (*RemainSummary) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{10}
}
func (m *RemainSummary) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemainSummary.Unmarshal(m, b)
}
func (m *RemainSummary) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemainSummary.Marshal(b, m, deterministic)
}
func (dst *RemainSummary) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemainSummary.Merge(dst, src)
}
func (m *RemainSummary) XXX_Size() int {
	return xxx_messageInfo_RemainSummary.Size(m)
}
func (m *RemainSummary) XXX_DiscardUnknown() {
	xxx_messageInfo_RemainSummary.DiscardUnknown(m)
}

var xxx_messageInfo_RemainSummary proto.InternalMessageInfo

func (m *RemainSummary) GetTicketId() uint32 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *RemainSummary) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *RemainSummary) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *RemainSummary) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

// 按券汇总数据，相同券id为一栏
type GetUserRemainSummaryReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TicketId             uint32   `protobuf:"varint,2,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserRemainSummaryReq) Reset()         { *m = GetUserRemainSummaryReq{} }
func (m *GetUserRemainSummaryReq) String() string { return proto.CompactTextString(m) }
func (*GetUserRemainSummaryReq) ProtoMessage()    {}
func (*GetUserRemainSummaryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{11}
}
func (m *GetUserRemainSummaryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRemainSummaryReq.Unmarshal(m, b)
}
func (m *GetUserRemainSummaryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRemainSummaryReq.Marshal(b, m, deterministic)
}
func (dst *GetUserRemainSummaryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRemainSummaryReq.Merge(dst, src)
}
func (m *GetUserRemainSummaryReq) XXX_Size() int {
	return xxx_messageInfo_GetUserRemainSummaryReq.Size(m)
}
func (m *GetUserRemainSummaryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRemainSummaryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRemainSummaryReq proto.InternalMessageInfo

func (m *GetUserRemainSummaryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserRemainSummaryReq) GetTicketId() uint32 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

type UserRemainSummary struct {
	Uid                  uint32           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	List                 []*RemainSummary `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *UserRemainSummary) Reset()         { *m = UserRemainSummary{} }
func (m *UserRemainSummary) String() string { return proto.CompactTextString(m) }
func (*UserRemainSummary) ProtoMessage()    {}
func (*UserRemainSummary) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{12}
}
func (m *UserRemainSummary) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserRemainSummary.Unmarshal(m, b)
}
func (m *UserRemainSummary) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserRemainSummary.Marshal(b, m, deterministic)
}
func (dst *UserRemainSummary) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserRemainSummary.Merge(dst, src)
}
func (m *UserRemainSummary) XXX_Size() int {
	return xxx_messageInfo_UserRemainSummary.Size(m)
}
func (m *UserRemainSummary) XXX_DiscardUnknown() {
	xxx_messageInfo_UserRemainSummary.DiscardUnknown(m)
}

var xxx_messageInfo_UserRemainSummary proto.InternalMessageInfo

func (m *UserRemainSummary) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserRemainSummary) GetList() []*RemainSummary {
	if m != nil {
		return m.List
	}
	return nil
}

// 发放游戏券
type SendUserGameTicketReq struct {
	Uid                  uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OrderId              string         `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	TicketId             uint32         `protobuf:"varint,3,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id,omitempty"`
	Num                  uint32         `protobuf:"varint,4,opt,name=num,proto3" json:"num,omitempty"`
	Source               GainSourceType `protobuf:"varint,5,opt,name=source,proto3,enum=game_ticket_mgr.GainSourceType" json:"source,omitempty"`
	OutsideTime          int64          `protobuf:"varint,6,opt,name=outside_time,json=outsideTime,proto3" json:"outside_time,omitempty"`
	ActivityName         string         `protobuf:"bytes,7,opt,name=activity_name,json=activityName,proto3" json:"activity_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *SendUserGameTicketReq) Reset()         { *m = SendUserGameTicketReq{} }
func (m *SendUserGameTicketReq) String() string { return proto.CompactTextString(m) }
func (*SendUserGameTicketReq) ProtoMessage()    {}
func (*SendUserGameTicketReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{13}
}
func (m *SendUserGameTicketReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendUserGameTicketReq.Unmarshal(m, b)
}
func (m *SendUserGameTicketReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendUserGameTicketReq.Marshal(b, m, deterministic)
}
func (dst *SendUserGameTicketReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendUserGameTicketReq.Merge(dst, src)
}
func (m *SendUserGameTicketReq) XXX_Size() int {
	return xxx_messageInfo_SendUserGameTicketReq.Size(m)
}
func (m *SendUserGameTicketReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendUserGameTicketReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendUserGameTicketReq proto.InternalMessageInfo

func (m *SendUserGameTicketReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SendUserGameTicketReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *SendUserGameTicketReq) GetTicketId() uint32 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *SendUserGameTicketReq) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *SendUserGameTicketReq) GetSource() GainSourceType {
	if m != nil {
		return m.Source
	}
	return GainSourceType_UnknownGainSourceType
}

func (m *SendUserGameTicketReq) GetOutsideTime() int64 {
	if m != nil {
		return m.OutsideTime
	}
	return 0
}

func (m *SendUserGameTicketReq) GetActivityName() string {
	if m != nil {
		return m.ActivityName
	}
	return ""
}

type SendUserGameTicketResp struct {
	FinalNum             uint32   `protobuf:"varint,1,opt,name=final_num,json=finalNum,proto3" json:"final_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendUserGameTicketResp) Reset()         { *m = SendUserGameTicketResp{} }
func (m *SendUserGameTicketResp) String() string { return proto.CompactTextString(m) }
func (*SendUserGameTicketResp) ProtoMessage()    {}
func (*SendUserGameTicketResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{14}
}
func (m *SendUserGameTicketResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendUserGameTicketResp.Unmarshal(m, b)
}
func (m *SendUserGameTicketResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendUserGameTicketResp.Marshal(b, m, deterministic)
}
func (dst *SendUserGameTicketResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendUserGameTicketResp.Merge(dst, src)
}
func (m *SendUserGameTicketResp) XXX_Size() int {
	return xxx_messageInfo_SendUserGameTicketResp.Size(m)
}
func (m *SendUserGameTicketResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendUserGameTicketResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendUserGameTicketResp proto.InternalMessageInfo

func (m *SendUserGameTicketResp) GetFinalNum() uint32 {
	if m != nil {
		return m.FinalNum
	}
	return 0
}

type BatchSendUserGameTicketReq struct {
	List                 []*SendUserGameTicketReq `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *BatchSendUserGameTicketReq) Reset()         { *m = BatchSendUserGameTicketReq{} }
func (m *BatchSendUserGameTicketReq) String() string { return proto.CompactTextString(m) }
func (*BatchSendUserGameTicketReq) ProtoMessage()    {}
func (*BatchSendUserGameTicketReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{15}
}
func (m *BatchSendUserGameTicketReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSendUserGameTicketReq.Unmarshal(m, b)
}
func (m *BatchSendUserGameTicketReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSendUserGameTicketReq.Marshal(b, m, deterministic)
}
func (dst *BatchSendUserGameTicketReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSendUserGameTicketReq.Merge(dst, src)
}
func (m *BatchSendUserGameTicketReq) XXX_Size() int {
	return xxx_messageInfo_BatchSendUserGameTicketReq.Size(m)
}
func (m *BatchSendUserGameTicketReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSendUserGameTicketReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSendUserGameTicketReq proto.InternalMessageInfo

func (m *BatchSendUserGameTicketReq) GetList() []*SendUserGameTicketReq {
	if m != nil {
		return m.List
	}
	return nil
}

type Empty struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Empty) Reset()         { *m = Empty{} }
func (m *Empty) String() string { return proto.CompactTextString(m) }
func (*Empty) ProtoMessage()    {}
func (*Empty) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{16}
}
func (m *Empty) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Empty.Unmarshal(m, b)
}
func (m *Empty) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Empty.Marshal(b, m, deterministic)
}
func (dst *Empty) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Empty.Merge(dst, src)
}
func (m *Empty) XXX_Size() int {
	return xxx_messageInfo_Empty.Size(m)
}
func (m *Empty) XXX_DiscardUnknown() {
	xxx_messageInfo_Empty.DiscardUnknown(m)
}

var xxx_messageInfo_Empty proto.InternalMessageInfo

// 冻结游戏券
type FreezeUserGameTicketReq struct {
	Uid                  uint32           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OrderId              string           `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	TicketId             uint32           `protobuf:"varint,3,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id,omitempty"`
	Num                  uint32           `protobuf:"varint,4,opt,name=num,proto3" json:"num,omitempty"`
	CostType             CostDestType     `protobuf:"varint,5,opt,name=cost_type,json=costType,proto3,enum=game_ticket_mgr.CostDestType" json:"cost_type,omitempty"`
	CostPriority         CostPriorityType `protobuf:"varint,6,opt,name=cost_priority,json=costPriority,proto3,enum=game_ticket_mgr.CostPriorityType" json:"cost_priority,omitempty"`
	CouponId             string           `protobuf:"bytes,7,opt,name=coupon_id,json=couponId,proto3" json:"coupon_id,omitempty"`
	CouponReduceNum      uint32           `protobuf:"varint,8,opt,name=coupon_reduce_num,json=couponReduceNum,proto3" json:"coupon_reduce_num,omitempty"`
	UsageType            uint32           `protobuf:"varint,9,opt,name=usage_type,json=usageType,proto3" json:"usage_type,omitempty"`
	UsageContext         *UsageContext    `protobuf:"bytes,10,opt,name=usage_context,json=usageContext,proto3" json:"usage_context,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *FreezeUserGameTicketReq) Reset()         { *m = FreezeUserGameTicketReq{} }
func (m *FreezeUserGameTicketReq) String() string { return proto.CompactTextString(m) }
func (*FreezeUserGameTicketReq) ProtoMessage()    {}
func (*FreezeUserGameTicketReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{17}
}
func (m *FreezeUserGameTicketReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FreezeUserGameTicketReq.Unmarshal(m, b)
}
func (m *FreezeUserGameTicketReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FreezeUserGameTicketReq.Marshal(b, m, deterministic)
}
func (dst *FreezeUserGameTicketReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FreezeUserGameTicketReq.Merge(dst, src)
}
func (m *FreezeUserGameTicketReq) XXX_Size() int {
	return xxx_messageInfo_FreezeUserGameTicketReq.Size(m)
}
func (m *FreezeUserGameTicketReq) XXX_DiscardUnknown() {
	xxx_messageInfo_FreezeUserGameTicketReq.DiscardUnknown(m)
}

var xxx_messageInfo_FreezeUserGameTicketReq proto.InternalMessageInfo

func (m *FreezeUserGameTicketReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FreezeUserGameTicketReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *FreezeUserGameTicketReq) GetTicketId() uint32 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *FreezeUserGameTicketReq) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *FreezeUserGameTicketReq) GetCostType() CostDestType {
	if m != nil {
		return m.CostType
	}
	return CostDestType_UnknownCostDestType
}

func (m *FreezeUserGameTicketReq) GetCostPriority() CostPriorityType {
	if m != nil {
		return m.CostPriority
	}
	return CostPriorityType_Free
}

func (m *FreezeUserGameTicketReq) GetCouponId() string {
	if m != nil {
		return m.CouponId
	}
	return ""
}

func (m *FreezeUserGameTicketReq) GetCouponReduceNum() uint32 {
	if m != nil {
		return m.CouponReduceNum
	}
	return 0
}

func (m *FreezeUserGameTicketReq) GetUsageType() uint32 {
	if m != nil {
		return m.UsageType
	}
	return 0
}

func (m *FreezeUserGameTicketReq) GetUsageContext() *UsageContext {
	if m != nil {
		return m.UsageContext
	}
	return nil
}

type UsageContext struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Chapters             []uint32 `protobuf:"varint,2,rep,packed,name=chapters,proto3" json:"chapters,omitempty"`
	PlaymateUid          uint32   `protobuf:"varint,3,opt,name=playmate_uid,json=playmateUid,proto3" json:"playmate_uid,omitempty"`
	PlayerUids           []uint32 `protobuf:"varint,4,rep,packed,name=player_uids,json=playerUids,proto3" json:"player_uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UsageContext) Reset()         { *m = UsageContext{} }
func (m *UsageContext) String() string { return proto.CompactTextString(m) }
func (*UsageContext) ProtoMessage()    {}
func (*UsageContext) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{18}
}
func (m *UsageContext) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UsageContext.Unmarshal(m, b)
}
func (m *UsageContext) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UsageContext.Marshal(b, m, deterministic)
}
func (dst *UsageContext) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UsageContext.Merge(dst, src)
}
func (m *UsageContext) XXX_Size() int {
	return xxx_messageInfo_UsageContext.Size(m)
}
func (m *UsageContext) XXX_DiscardUnknown() {
	xxx_messageInfo_UsageContext.DiscardUnknown(m)
}

var xxx_messageInfo_UsageContext proto.InternalMessageInfo

func (m *UsageContext) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *UsageContext) GetChapters() []uint32 {
	if m != nil {
		return m.Chapters
	}
	return nil
}

func (m *UsageContext) GetPlaymateUid() uint32 {
	if m != nil {
		return m.PlaymateUid
	}
	return 0
}

func (m *UsageContext) GetPlayerUids() []uint32 {
	if m != nil {
		return m.PlayerUids
	}
	return nil
}

type FreezeUserGameTicketResp struct {
	FreezeTime           int64                 `protobuf:"varint,1,opt,name=freeze_time,json=freezeTime,proto3" json:"freeze_time,omitempty"`
	FreezeList           []*TicketRemainDetail `protobuf:"bytes,2,rep,name=freeze_list,json=freezeList,proto3" json:"freeze_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *FreezeUserGameTicketResp) Reset()         { *m = FreezeUserGameTicketResp{} }
func (m *FreezeUserGameTicketResp) String() string { return proto.CompactTextString(m) }
func (*FreezeUserGameTicketResp) ProtoMessage()    {}
func (*FreezeUserGameTicketResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{19}
}
func (m *FreezeUserGameTicketResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FreezeUserGameTicketResp.Unmarshal(m, b)
}
func (m *FreezeUserGameTicketResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FreezeUserGameTicketResp.Marshal(b, m, deterministic)
}
func (dst *FreezeUserGameTicketResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FreezeUserGameTicketResp.Merge(dst, src)
}
func (m *FreezeUserGameTicketResp) XXX_Size() int {
	return xxx_messageInfo_FreezeUserGameTicketResp.Size(m)
}
func (m *FreezeUserGameTicketResp) XXX_DiscardUnknown() {
	xxx_messageInfo_FreezeUserGameTicketResp.DiscardUnknown(m)
}

var xxx_messageInfo_FreezeUserGameTicketResp proto.InternalMessageInfo

func (m *FreezeUserGameTicketResp) GetFreezeTime() int64 {
	if m != nil {
		return m.FreezeTime
	}
	return 0
}

func (m *FreezeUserGameTicketResp) GetFreezeList() []*TicketRemainDetail {
	if m != nil {
		return m.FreezeList
	}
	return nil
}

// 确认冻结的订单
type ConfirmFreezeOrderReq struct {
	Uid                  uint32       `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OrderId              string       `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	ConfirmStatus        FreezeStatus `protobuf:"varint,3,opt,name=confirm_status,json=confirmStatus,proto3,enum=game_ticket_mgr.FreezeStatus" json:"confirm_status,omitempty"`
	FreezeTime           int64        `protobuf:"varint,4,opt,name=freeze_time,json=freezeTime,proto3" json:"freeze_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ConfirmFreezeOrderReq) Reset()         { *m = ConfirmFreezeOrderReq{} }
func (m *ConfirmFreezeOrderReq) String() string { return proto.CompactTextString(m) }
func (*ConfirmFreezeOrderReq) ProtoMessage()    {}
func (*ConfirmFreezeOrderReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{20}
}
func (m *ConfirmFreezeOrderReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfirmFreezeOrderReq.Unmarshal(m, b)
}
func (m *ConfirmFreezeOrderReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfirmFreezeOrderReq.Marshal(b, m, deterministic)
}
func (dst *ConfirmFreezeOrderReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfirmFreezeOrderReq.Merge(dst, src)
}
func (m *ConfirmFreezeOrderReq) XXX_Size() int {
	return xxx_messageInfo_ConfirmFreezeOrderReq.Size(m)
}
func (m *ConfirmFreezeOrderReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfirmFreezeOrderReq.DiscardUnknown(m)
}

var xxx_messageInfo_ConfirmFreezeOrderReq proto.InternalMessageInfo

func (m *ConfirmFreezeOrderReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ConfirmFreezeOrderReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *ConfirmFreezeOrderReq) GetConfirmStatus() FreezeStatus {
	if m != nil {
		return m.ConfirmStatus
	}
	return FreezeStatus_Freeze
}

func (m *ConfirmFreezeOrderReq) GetFreezeTime() int64 {
	if m != nil {
		return m.FreezeTime
	}
	return 0
}

type ConfirmFreezeOrderResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConfirmFreezeOrderResp) Reset()         { *m = ConfirmFreezeOrderResp{} }
func (m *ConfirmFreezeOrderResp) String() string { return proto.CompactTextString(m) }
func (*ConfirmFreezeOrderResp) ProtoMessage()    {}
func (*ConfirmFreezeOrderResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{21}
}
func (m *ConfirmFreezeOrderResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfirmFreezeOrderResp.Unmarshal(m, b)
}
func (m *ConfirmFreezeOrderResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfirmFreezeOrderResp.Marshal(b, m, deterministic)
}
func (dst *ConfirmFreezeOrderResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfirmFreezeOrderResp.Merge(dst, src)
}
func (m *ConfirmFreezeOrderResp) XXX_Size() int {
	return xxx_messageInfo_ConfirmFreezeOrderResp.Size(m)
}
func (m *ConfirmFreezeOrderResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfirmFreezeOrderResp.DiscardUnknown(m)
}

var xxx_messageInfo_ConfirmFreezeOrderResp proto.InternalMessageInfo

type CouponContentLimit struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Chapters             []uint32 `protobuf:"varint,2,rep,packed,name=chapters,proto3" json:"chapters,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CouponContentLimit) Reset()         { *m = CouponContentLimit{} }
func (m *CouponContentLimit) String() string { return proto.CompactTextString(m) }
func (*CouponContentLimit) ProtoMessage()    {}
func (*CouponContentLimit) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{22}
}
func (m *CouponContentLimit) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CouponContentLimit.Unmarshal(m, b)
}
func (m *CouponContentLimit) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CouponContentLimit.Marshal(b, m, deterministic)
}
func (dst *CouponContentLimit) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CouponContentLimit.Merge(dst, src)
}
func (m *CouponContentLimit) XXX_Size() int {
	return xxx_messageInfo_CouponContentLimit.Size(m)
}
func (m *CouponContentLimit) XXX_DiscardUnknown() {
	xxx_messageInfo_CouponContentLimit.DiscardUnknown(m)
}

var xxx_messageInfo_CouponContentLimit proto.InternalMessageInfo

func (m *CouponContentLimit) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *CouponContentLimit) GetChapters() []uint32 {
	if m != nil {
		return m.Chapters
	}
	return nil
}

type CouponConfig struct {
	CouponConfId         uint32                `protobuf:"varint,1,opt,name=coupon_conf_id,json=couponConfId,proto3" json:"coupon_conf_id,omitempty"`
	CouponName           string                `protobuf:"bytes,2,opt,name=coupon_name,json=couponName,proto3" json:"coupon_name,omitempty"`
	CouponGrantSource    CouponGrantSource     `protobuf:"varint,3,opt,name=coupon_grant_source,json=couponGrantSource,proto3,enum=game_ticket_mgr.CouponGrantSource" json:"coupon_grant_source,omitempty"`
	CouponType           CouponType            `protobuf:"varint,4,opt,name=coupon_type,json=couponType,proto3,enum=game_ticket_mgr.CouponType" json:"coupon_type,omitempty"`
	CouponReduction      uint32                `protobuf:"varint,5,opt,name=coupon_reduction,json=couponReduction,proto3" json:"coupon_reduction,omitempty"`
	CouponDiscount       uint32                `protobuf:"varint,6,opt,name=coupon_discount,json=couponDiscount,proto3" json:"coupon_discount,omitempty"`
	ContentLimits        []*CouponContentLimit `protobuf:"bytes,7,rep,name=content_limits,json=contentLimits,proto3" json:"content_limits,omitempty"`
	Operator             string                `protobuf:"bytes,8,opt,name=operator,proto3" json:"operator,omitempty"`
	CreateTime           int64                 `protobuf:"varint,9,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	ModifyTime           int64                 `protobuf:"varint,10,opt,name=modify_time,json=modifyTime,proto3" json:"modify_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *CouponConfig) Reset()         { *m = CouponConfig{} }
func (m *CouponConfig) String() string { return proto.CompactTextString(m) }
func (*CouponConfig) ProtoMessage()    {}
func (*CouponConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{23}
}
func (m *CouponConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CouponConfig.Unmarshal(m, b)
}
func (m *CouponConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CouponConfig.Marshal(b, m, deterministic)
}
func (dst *CouponConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CouponConfig.Merge(dst, src)
}
func (m *CouponConfig) XXX_Size() int {
	return xxx_messageInfo_CouponConfig.Size(m)
}
func (m *CouponConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_CouponConfig.DiscardUnknown(m)
}

var xxx_messageInfo_CouponConfig proto.InternalMessageInfo

func (m *CouponConfig) GetCouponConfId() uint32 {
	if m != nil {
		return m.CouponConfId
	}
	return 0
}

func (m *CouponConfig) GetCouponName() string {
	if m != nil {
		return m.CouponName
	}
	return ""
}

func (m *CouponConfig) GetCouponGrantSource() CouponGrantSource {
	if m != nil {
		return m.CouponGrantSource
	}
	return CouponGrantSource_COUPON_GRANT_SOURCE_NONE
}

func (m *CouponConfig) GetCouponType() CouponType {
	if m != nil {
		return m.CouponType
	}
	return CouponType_COUPON_TYPE_UNSPECIFIED
}

func (m *CouponConfig) GetCouponReduction() uint32 {
	if m != nil {
		return m.CouponReduction
	}
	return 0
}

func (m *CouponConfig) GetCouponDiscount() uint32 {
	if m != nil {
		return m.CouponDiscount
	}
	return 0
}

func (m *CouponConfig) GetContentLimits() []*CouponContentLimit {
	if m != nil {
		return m.ContentLimits
	}
	return nil
}

func (m *CouponConfig) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *CouponConfig) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *CouponConfig) GetModifyTime() int64 {
	if m != nil {
		return m.ModifyTime
	}
	return 0
}

type AddCouponConfigReq struct {
	CouponConfig         *CouponConfig `protobuf:"bytes,1,opt,name=coupon_config,json=couponConfig,proto3" json:"coupon_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *AddCouponConfigReq) Reset()         { *m = AddCouponConfigReq{} }
func (m *AddCouponConfigReq) String() string { return proto.CompactTextString(m) }
func (*AddCouponConfigReq) ProtoMessage()    {}
func (*AddCouponConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{24}
}
func (m *AddCouponConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddCouponConfigReq.Unmarshal(m, b)
}
func (m *AddCouponConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddCouponConfigReq.Marshal(b, m, deterministic)
}
func (dst *AddCouponConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddCouponConfigReq.Merge(dst, src)
}
func (m *AddCouponConfigReq) XXX_Size() int {
	return xxx_messageInfo_AddCouponConfigReq.Size(m)
}
func (m *AddCouponConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddCouponConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddCouponConfigReq proto.InternalMessageInfo

func (m *AddCouponConfigReq) GetCouponConfig() *CouponConfig {
	if m != nil {
		return m.CouponConfig
	}
	return nil
}

type AddCouponConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddCouponConfigResp) Reset()         { *m = AddCouponConfigResp{} }
func (m *AddCouponConfigResp) String() string { return proto.CompactTextString(m) }
func (*AddCouponConfigResp) ProtoMessage()    {}
func (*AddCouponConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{25}
}
func (m *AddCouponConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddCouponConfigResp.Unmarshal(m, b)
}
func (m *AddCouponConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddCouponConfigResp.Marshal(b, m, deterministic)
}
func (dst *AddCouponConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddCouponConfigResp.Merge(dst, src)
}
func (m *AddCouponConfigResp) XXX_Size() int {
	return xxx_messageInfo_AddCouponConfigResp.Size(m)
}
func (m *AddCouponConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddCouponConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddCouponConfigResp proto.InternalMessageInfo

type ModifyCouponConfigReq struct {
	CouponConfig         *CouponConfig `protobuf:"bytes,1,opt,name=coupon_config,json=couponConfig,proto3" json:"coupon_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ModifyCouponConfigReq) Reset()         { *m = ModifyCouponConfigReq{} }
func (m *ModifyCouponConfigReq) String() string { return proto.CompactTextString(m) }
func (*ModifyCouponConfigReq) ProtoMessage()    {}
func (*ModifyCouponConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{26}
}
func (m *ModifyCouponConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyCouponConfigReq.Unmarshal(m, b)
}
func (m *ModifyCouponConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyCouponConfigReq.Marshal(b, m, deterministic)
}
func (dst *ModifyCouponConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyCouponConfigReq.Merge(dst, src)
}
func (m *ModifyCouponConfigReq) XXX_Size() int {
	return xxx_messageInfo_ModifyCouponConfigReq.Size(m)
}
func (m *ModifyCouponConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyCouponConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyCouponConfigReq proto.InternalMessageInfo

func (m *ModifyCouponConfigReq) GetCouponConfig() *CouponConfig {
	if m != nil {
		return m.CouponConfig
	}
	return nil
}

type ModifyCouponConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyCouponConfigResp) Reset()         { *m = ModifyCouponConfigResp{} }
func (m *ModifyCouponConfigResp) String() string { return proto.CompactTextString(m) }
func (*ModifyCouponConfigResp) ProtoMessage()    {}
func (*ModifyCouponConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{27}
}
func (m *ModifyCouponConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyCouponConfigResp.Unmarshal(m, b)
}
func (m *ModifyCouponConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyCouponConfigResp.Marshal(b, m, deterministic)
}
func (dst *ModifyCouponConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyCouponConfigResp.Merge(dst, src)
}
func (m *ModifyCouponConfigResp) XXX_Size() int {
	return xxx_messageInfo_ModifyCouponConfigResp.Size(m)
}
func (m *ModifyCouponConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyCouponConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyCouponConfigResp proto.InternalMessageInfo

type ListCouponConfigReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListCouponConfigReq) Reset()         { *m = ListCouponConfigReq{} }
func (m *ListCouponConfigReq) String() string { return proto.CompactTextString(m) }
func (*ListCouponConfigReq) ProtoMessage()    {}
func (*ListCouponConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{28}
}
func (m *ListCouponConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListCouponConfigReq.Unmarshal(m, b)
}
func (m *ListCouponConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListCouponConfigReq.Marshal(b, m, deterministic)
}
func (dst *ListCouponConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListCouponConfigReq.Merge(dst, src)
}
func (m *ListCouponConfigReq) XXX_Size() int {
	return xxx_messageInfo_ListCouponConfigReq.Size(m)
}
func (m *ListCouponConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListCouponConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListCouponConfigReq proto.InternalMessageInfo

type ListCouponConfigResp struct {
	CouponConfigs        []*CouponConfig `protobuf:"bytes,1,rep,name=coupon_configs,json=couponConfigs,proto3" json:"coupon_configs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ListCouponConfigResp) Reset()         { *m = ListCouponConfigResp{} }
func (m *ListCouponConfigResp) String() string { return proto.CompactTextString(m) }
func (*ListCouponConfigResp) ProtoMessage()    {}
func (*ListCouponConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{29}
}
func (m *ListCouponConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListCouponConfigResp.Unmarshal(m, b)
}
func (m *ListCouponConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListCouponConfigResp.Marshal(b, m, deterministic)
}
func (dst *ListCouponConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListCouponConfigResp.Merge(dst, src)
}
func (m *ListCouponConfigResp) XXX_Size() int {
	return xxx_messageInfo_ListCouponConfigResp.Size(m)
}
func (m *ListCouponConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListCouponConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListCouponConfigResp proto.InternalMessageInfo

func (m *ListCouponConfigResp) GetCouponConfigs() []*CouponConfig {
	if m != nil {
		return m.CouponConfigs
	}
	return nil
}

type CouponGrantUser struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PlaymateUidLimits    []uint32 `protobuf:"varint,2,rep,packed,name=playmate_uid_limits,json=playmateUidLimits,proto3" json:"playmate_uid_limits,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CouponGrantUser) Reset()         { *m = CouponGrantUser{} }
func (m *CouponGrantUser) String() string { return proto.CompactTextString(m) }
func (*CouponGrantUser) ProtoMessage()    {}
func (*CouponGrantUser) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{30}
}
func (m *CouponGrantUser) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CouponGrantUser.Unmarshal(m, b)
}
func (m *CouponGrantUser) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CouponGrantUser.Marshal(b, m, deterministic)
}
func (dst *CouponGrantUser) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CouponGrantUser.Merge(dst, src)
}
func (m *CouponGrantUser) XXX_Size() int {
	return xxx_messageInfo_CouponGrantUser.Size(m)
}
func (m *CouponGrantUser) XXX_DiscardUnknown() {
	xxx_messageInfo_CouponGrantUser.DiscardUnknown(m)
}

var xxx_messageInfo_CouponGrantUser proto.InternalMessageInfo

func (m *CouponGrantUser) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CouponGrantUser) GetPlaymateUidLimits() []uint32 {
	if m != nil {
		return m.PlaymateUidLimits
	}
	return nil
}

// 批量发放优惠券，多用于管理后台发放
type BatGrantCouponReq struct {
	Records []*GrantCouponRecord `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
	// 批量接口下列参数将会覆盖records内同名参数
	SourceDetail string `protobuf:"bytes,3,opt,name=source_detail,json=sourceDetail,proto3" json:"source_detail,omitempty"`
	GrantNote    string `protobuf:"bytes,4,opt,name=grant_note,json=grantNote,proto3" json:"grant_note,omitempty"`
	Operator     string `protobuf:"bytes,5,opt,name=operator,proto3" json:"operator,omitempty"`
	// 下列参数不为0时会覆盖records内同名参数
	StartTs              int64    `protobuf:"varint,6,opt,name=start_ts,json=startTs,proto3" json:"start_ts,omitempty"`
	StopTs               int64    `protobuf:"varint,7,opt,name=stop_ts,json=stopTs,proto3" json:"stop_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGrantCouponReq) Reset()         { *m = BatGrantCouponReq{} }
func (m *BatGrantCouponReq) String() string { return proto.CompactTextString(m) }
func (*BatGrantCouponReq) ProtoMessage()    {}
func (*BatGrantCouponReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{31}
}
func (m *BatGrantCouponReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGrantCouponReq.Unmarshal(m, b)
}
func (m *BatGrantCouponReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGrantCouponReq.Marshal(b, m, deterministic)
}
func (dst *BatGrantCouponReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGrantCouponReq.Merge(dst, src)
}
func (m *BatGrantCouponReq) XXX_Size() int {
	return xxx_messageInfo_BatGrantCouponReq.Size(m)
}
func (m *BatGrantCouponReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGrantCouponReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatGrantCouponReq proto.InternalMessageInfo

func (m *BatGrantCouponReq) GetRecords() []*GrantCouponRecord {
	if m != nil {
		return m.Records
	}
	return nil
}

func (m *BatGrantCouponReq) GetSourceDetail() string {
	if m != nil {
		return m.SourceDetail
	}
	return ""
}

func (m *BatGrantCouponReq) GetGrantNote() string {
	if m != nil {
		return m.GrantNote
	}
	return ""
}

func (m *BatGrantCouponReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *BatGrantCouponReq) GetStartTs() int64 {
	if m != nil {
		return m.StartTs
	}
	return 0
}

func (m *BatGrantCouponReq) GetStopTs() int64 {
	if m != nil {
		return m.StopTs
	}
	return 0
}

type BatGrantCouponResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGrantCouponResp) Reset()         { *m = BatGrantCouponResp{} }
func (m *BatGrantCouponResp) String() string { return proto.CompactTextString(m) }
func (*BatGrantCouponResp) ProtoMessage()    {}
func (*BatGrantCouponResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{32}
}
func (m *BatGrantCouponResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGrantCouponResp.Unmarshal(m, b)
}
func (m *BatGrantCouponResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGrantCouponResp.Marshal(b, m, deterministic)
}
func (dst *BatGrantCouponResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGrantCouponResp.Merge(dst, src)
}
func (m *BatGrantCouponResp) XXX_Size() int {
	return xxx_messageInfo_BatGrantCouponResp.Size(m)
}
func (m *BatGrantCouponResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGrantCouponResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatGrantCouponResp proto.InternalMessageInfo

type GrantCouponRecord struct {
	Users        *CouponGrantUser `protobuf:"bytes,1,opt,name=users,proto3" json:"users,omitempty"`
	CouponConfId uint32           `protobuf:"varint,2,opt,name=coupon_conf_id,json=couponConfId,proto3" json:"coupon_conf_id,omitempty"`
	// 以下参数批量发放在外部传入
	SourceDetail         string   `protobuf:"bytes,3,opt,name=source_detail,json=sourceDetail,proto3" json:"source_detail,omitempty"`
	GrantNote            string   `protobuf:"bytes,4,opt,name=grant_note,json=grantNote,proto3" json:"grant_note,omitempty"`
	Operator             string   `protobuf:"bytes,5,opt,name=operator,proto3" json:"operator,omitempty"`
	StartTs              int64    `protobuf:"varint,6,opt,name=start_ts,json=startTs,proto3" json:"start_ts,omitempty"`
	StopTs               int64    `protobuf:"varint,7,opt,name=stop_ts,json=stopTs,proto3" json:"stop_ts,omitempty"`
	OutOrderId           string   `protobuf:"bytes,8,opt,name=out_order_id,json=outOrderId,proto3" json:"out_order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GrantCouponRecord) Reset()         { *m = GrantCouponRecord{} }
func (m *GrantCouponRecord) String() string { return proto.CompactTextString(m) }
func (*GrantCouponRecord) ProtoMessage()    {}
func (*GrantCouponRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{33}
}
func (m *GrantCouponRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GrantCouponRecord.Unmarshal(m, b)
}
func (m *GrantCouponRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GrantCouponRecord.Marshal(b, m, deterministic)
}
func (dst *GrantCouponRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GrantCouponRecord.Merge(dst, src)
}
func (m *GrantCouponRecord) XXX_Size() int {
	return xxx_messageInfo_GrantCouponRecord.Size(m)
}
func (m *GrantCouponRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_GrantCouponRecord.DiscardUnknown(m)
}

var xxx_messageInfo_GrantCouponRecord proto.InternalMessageInfo

func (m *GrantCouponRecord) GetUsers() *CouponGrantUser {
	if m != nil {
		return m.Users
	}
	return nil
}

func (m *GrantCouponRecord) GetCouponConfId() uint32 {
	if m != nil {
		return m.CouponConfId
	}
	return 0
}

func (m *GrantCouponRecord) GetSourceDetail() string {
	if m != nil {
		return m.SourceDetail
	}
	return ""
}

func (m *GrantCouponRecord) GetGrantNote() string {
	if m != nil {
		return m.GrantNote
	}
	return ""
}

func (m *GrantCouponRecord) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *GrantCouponRecord) GetStartTs() int64 {
	if m != nil {
		return m.StartTs
	}
	return 0
}

func (m *GrantCouponRecord) GetStopTs() int64 {
	if m != nil {
		return m.StopTs
	}
	return 0
}

func (m *GrantCouponRecord) GetOutOrderId() string {
	if m != nil {
		return m.OutOrderId
	}
	return ""
}

// 发放优惠券，多用于程序发放
type GrantCouponReq struct {
	Record               *GrantCouponRecord `protobuf:"bytes,1,opt,name=record,proto3" json:"record,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GrantCouponReq) Reset()         { *m = GrantCouponReq{} }
func (m *GrantCouponReq) String() string { return proto.CompactTextString(m) }
func (*GrantCouponReq) ProtoMessage()    {}
func (*GrantCouponReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{34}
}
func (m *GrantCouponReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GrantCouponReq.Unmarshal(m, b)
}
func (m *GrantCouponReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GrantCouponReq.Marshal(b, m, deterministic)
}
func (dst *GrantCouponReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GrantCouponReq.Merge(dst, src)
}
func (m *GrantCouponReq) XXX_Size() int {
	return xxx_messageInfo_GrantCouponReq.Size(m)
}
func (m *GrantCouponReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GrantCouponReq.DiscardUnknown(m)
}

var xxx_messageInfo_GrantCouponReq proto.InternalMessageInfo

func (m *GrantCouponReq) GetRecord() *GrantCouponRecord {
	if m != nil {
		return m.Record
	}
	return nil
}

type GrantCouponResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GrantCouponResp) Reset()         { *m = GrantCouponResp{} }
func (m *GrantCouponResp) String() string { return proto.CompactTextString(m) }
func (*GrantCouponResp) ProtoMessage()    {}
func (*GrantCouponResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{35}
}
func (m *GrantCouponResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GrantCouponResp.Unmarshal(m, b)
}
func (m *GrantCouponResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GrantCouponResp.Marshal(b, m, deterministic)
}
func (dst *GrantCouponResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GrantCouponResp.Merge(dst, src)
}
func (m *GrantCouponResp) XXX_Size() int {
	return xxx_messageInfo_GrantCouponResp.Size(m)
}
func (m *GrantCouponResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GrantCouponResp.DiscardUnknown(m)
}

var xxx_messageInfo_GrantCouponResp proto.InternalMessageInfo

type GetUserCouponCountReq struct {
	Uid                  uint32          `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	QueryType            CouponQueryType `protobuf:"varint,2,opt,name=query_type,json=queryType,proto3,enum=game_ticket_mgr.CouponQueryType" json:"query_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetUserCouponCountReq) Reset()         { *m = GetUserCouponCountReq{} }
func (m *GetUserCouponCountReq) String() string { return proto.CompactTextString(m) }
func (*GetUserCouponCountReq) ProtoMessage()    {}
func (*GetUserCouponCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{36}
}
func (m *GetUserCouponCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCouponCountReq.Unmarshal(m, b)
}
func (m *GetUserCouponCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCouponCountReq.Marshal(b, m, deterministic)
}
func (dst *GetUserCouponCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCouponCountReq.Merge(dst, src)
}
func (m *GetUserCouponCountReq) XXX_Size() int {
	return xxx_messageInfo_GetUserCouponCountReq.Size(m)
}
func (m *GetUserCouponCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCouponCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCouponCountReq proto.InternalMessageInfo

func (m *GetUserCouponCountReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserCouponCountReq) GetQueryType() CouponQueryType {
	if m != nil {
		return m.QueryType
	}
	return CouponQueryType_COUPON_QUERY_TYPE_UNSPECIFIED
}

type GetUserCouponCountResp struct {
	Count                uint32   `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserCouponCountResp) Reset()         { *m = GetUserCouponCountResp{} }
func (m *GetUserCouponCountResp) String() string { return proto.CompactTextString(m) }
func (*GetUserCouponCountResp) ProtoMessage()    {}
func (*GetUserCouponCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{37}
}
func (m *GetUserCouponCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCouponCountResp.Unmarshal(m, b)
}
func (m *GetUserCouponCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCouponCountResp.Marshal(b, m, deterministic)
}
func (dst *GetUserCouponCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCouponCountResp.Merge(dst, src)
}
func (m *GetUserCouponCountResp) XXX_Size() int {
	return xxx_messageInfo_GetUserCouponCountResp.Size(m)
}
func (m *GetUserCouponCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCouponCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCouponCountResp proto.InternalMessageInfo

func (m *GetUserCouponCountResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetUserCouponListReq struct {
	Uid                  uint32          `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	QueryType            CouponQueryType `protobuf:"varint,2,opt,name=query_type,json=queryType,proto3,enum=game_ticket_mgr.CouponQueryType" json:"query_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetUserCouponListReq) Reset()         { *m = GetUserCouponListReq{} }
func (m *GetUserCouponListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserCouponListReq) ProtoMessage()    {}
func (*GetUserCouponListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{38}
}
func (m *GetUserCouponListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCouponListReq.Unmarshal(m, b)
}
func (m *GetUserCouponListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCouponListReq.Marshal(b, m, deterministic)
}
func (dst *GetUserCouponListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCouponListReq.Merge(dst, src)
}
func (m *GetUserCouponListReq) XXX_Size() int {
	return xxx_messageInfo_GetUserCouponListReq.Size(m)
}
func (m *GetUserCouponListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCouponListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCouponListReq proto.InternalMessageInfo

func (m *GetUserCouponListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserCouponListReq) GetQueryType() CouponQueryType {
	if m != nil {
		return m.QueryType
	}
	return CouponQueryType_COUPON_QUERY_TYPE_UNSPECIFIED
}

type GetUserCouponListResp struct {
	Coupons              []*UserCouponDetail `protobuf:"bytes,1,rep,name=coupons,proto3" json:"coupons,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetUserCouponListResp) Reset()         { *m = GetUserCouponListResp{} }
func (m *GetUserCouponListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserCouponListResp) ProtoMessage()    {}
func (*GetUserCouponListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{39}
}
func (m *GetUserCouponListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCouponListResp.Unmarshal(m, b)
}
func (m *GetUserCouponListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCouponListResp.Marshal(b, m, deterministic)
}
func (dst *GetUserCouponListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCouponListResp.Merge(dst, src)
}
func (m *GetUserCouponListResp) XXX_Size() int {
	return xxx_messageInfo_GetUserCouponListResp.Size(m)
}
func (m *GetUserCouponListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCouponListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCouponListResp proto.InternalMessageInfo

func (m *GetUserCouponListResp) GetCoupons() []*UserCouponDetail {
	if m != nil {
		return m.Coupons
	}
	return nil
}

type UserCouponDetail struct {
	CouponId             string        `protobuf:"bytes,1,opt,name=coupon_id,json=couponId,proto3" json:"coupon_id,omitempty"`
	CouponStatus         CouponStatus  `protobuf:"varint,2,opt,name=coupon_status,json=couponStatus,proto3,enum=game_ticket_mgr.CouponStatus" json:"coupon_status,omitempty"`
	CouponStartTs        int64         `protobuf:"varint,3,opt,name=coupon_start_ts,json=couponStartTs,proto3" json:"coupon_start_ts,omitempty"`
	CouponStopTs         int64         `protobuf:"varint,4,opt,name=coupon_stop_ts,json=couponStopTs,proto3" json:"coupon_stop_ts,omitempty"`
	PlaymateUidLimits    []uint32      `protobuf:"varint,5,rep,packed,name=playmate_uid_limits,json=playmateUidLimits,proto3" json:"playmate_uid_limits,omitempty"`
	CouponConfig         *CouponConfig `protobuf:"bytes,10,opt,name=coupon_config,json=couponConfig,proto3" json:"coupon_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UserCouponDetail) Reset()         { *m = UserCouponDetail{} }
func (m *UserCouponDetail) String() string { return proto.CompactTextString(m) }
func (*UserCouponDetail) ProtoMessage()    {}
func (*UserCouponDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{40}
}
func (m *UserCouponDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserCouponDetail.Unmarshal(m, b)
}
func (m *UserCouponDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserCouponDetail.Marshal(b, m, deterministic)
}
func (dst *UserCouponDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserCouponDetail.Merge(dst, src)
}
func (m *UserCouponDetail) XXX_Size() int {
	return xxx_messageInfo_UserCouponDetail.Size(m)
}
func (m *UserCouponDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_UserCouponDetail.DiscardUnknown(m)
}

var xxx_messageInfo_UserCouponDetail proto.InternalMessageInfo

func (m *UserCouponDetail) GetCouponId() string {
	if m != nil {
		return m.CouponId
	}
	return ""
}

func (m *UserCouponDetail) GetCouponStatus() CouponStatus {
	if m != nil {
		return m.CouponStatus
	}
	return CouponStatus_COUPON_STATUS_UNDEFINED
}

func (m *UserCouponDetail) GetCouponStartTs() int64 {
	if m != nil {
		return m.CouponStartTs
	}
	return 0
}

func (m *UserCouponDetail) GetCouponStopTs() int64 {
	if m != nil {
		return m.CouponStopTs
	}
	return 0
}

func (m *UserCouponDetail) GetPlaymateUidLimits() []uint32 {
	if m != nil {
		return m.PlaymateUidLimits
	}
	return nil
}

func (m *UserCouponDetail) GetCouponConfig() *CouponConfig {
	if m != nil {
		return m.CouponConfig
	}
	return nil
}

type GetUserValidCouponReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserValidCouponReq) Reset()         { *m = GetUserValidCouponReq{} }
func (m *GetUserValidCouponReq) String() string { return proto.CompactTextString(m) }
func (*GetUserValidCouponReq) ProtoMessage()    {}
func (*GetUserValidCouponReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{41}
}
func (m *GetUserValidCouponReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserValidCouponReq.Unmarshal(m, b)
}
func (m *GetUserValidCouponReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserValidCouponReq.Marshal(b, m, deterministic)
}
func (dst *GetUserValidCouponReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserValidCouponReq.Merge(dst, src)
}
func (m *GetUserValidCouponReq) XXX_Size() int {
	return xxx_messageInfo_GetUserValidCouponReq.Size(m)
}
func (m *GetUserValidCouponReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserValidCouponReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserValidCouponReq proto.InternalMessageInfo

func (m *GetUserValidCouponReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserValidCouponResp struct {
	Coupons              []*UserValidCoupon `protobuf:"bytes,1,rep,name=coupons,proto3" json:"coupons,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetUserValidCouponResp) Reset()         { *m = GetUserValidCouponResp{} }
func (m *GetUserValidCouponResp) String() string { return proto.CompactTextString(m) }
func (*GetUserValidCouponResp) ProtoMessage()    {}
func (*GetUserValidCouponResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{42}
}
func (m *GetUserValidCouponResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserValidCouponResp.Unmarshal(m, b)
}
func (m *GetUserValidCouponResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserValidCouponResp.Marshal(b, m, deterministic)
}
func (dst *GetUserValidCouponResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserValidCouponResp.Merge(dst, src)
}
func (m *GetUserValidCouponResp) XXX_Size() int {
	return xxx_messageInfo_GetUserValidCouponResp.Size(m)
}
func (m *GetUserValidCouponResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserValidCouponResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserValidCouponResp proto.InternalMessageInfo

func (m *GetUserValidCouponResp) GetCoupons() []*UserValidCoupon {
	if m != nil {
		return m.Coupons
	}
	return nil
}

type UserValidCoupon struct {
	CouponId             string              `protobuf:"bytes,1,opt,name=coupon_id,json=couponId,proto3" json:"coupon_id,omitempty"`
	CouponType           CouponType          `protobuf:"varint,2,opt,name=coupon_type,json=couponType,proto3,enum=game_ticket_mgr.CouponType" json:"coupon_type,omitempty"`
	CouponReduction      uint32              `protobuf:"varint,3,opt,name=coupon_reduction,json=couponReduction,proto3" json:"coupon_reduction,omitempty"`
	CouponDiscount       uint32              `protobuf:"varint,4,opt,name=coupon_discount,json=couponDiscount,proto3" json:"coupon_discount,omitempty"`
	CouponStartTs        int64               `protobuf:"varint,5,opt,name=coupon_start_ts,json=couponStartTs,proto3" json:"coupon_start_ts,omitempty"`
	CouponStopTs         int64               `protobuf:"varint,6,opt,name=coupon_stop_ts,json=couponStopTs,proto3" json:"coupon_stop_ts,omitempty"`
	ContentLimits        []*GameContentLimit `protobuf:"bytes,7,rep,name=content_limits,json=contentLimits,proto3" json:"content_limits,omitempty"`
	PlaymateUidLimits    []uint32            `protobuf:"varint,8,rep,packed,name=playmate_uid_limits,json=playmateUidLimits,proto3" json:"playmate_uid_limits,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *UserValidCoupon) Reset()         { *m = UserValidCoupon{} }
func (m *UserValidCoupon) String() string { return proto.CompactTextString(m) }
func (*UserValidCoupon) ProtoMessage()    {}
func (*UserValidCoupon) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{43}
}
func (m *UserValidCoupon) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserValidCoupon.Unmarshal(m, b)
}
func (m *UserValidCoupon) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserValidCoupon.Marshal(b, m, deterministic)
}
func (dst *UserValidCoupon) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserValidCoupon.Merge(dst, src)
}
func (m *UserValidCoupon) XXX_Size() int {
	return xxx_messageInfo_UserValidCoupon.Size(m)
}
func (m *UserValidCoupon) XXX_DiscardUnknown() {
	xxx_messageInfo_UserValidCoupon.DiscardUnknown(m)
}

var xxx_messageInfo_UserValidCoupon proto.InternalMessageInfo

func (m *UserValidCoupon) GetCouponId() string {
	if m != nil {
		return m.CouponId
	}
	return ""
}

func (m *UserValidCoupon) GetCouponType() CouponType {
	if m != nil {
		return m.CouponType
	}
	return CouponType_COUPON_TYPE_UNSPECIFIED
}

func (m *UserValidCoupon) GetCouponReduction() uint32 {
	if m != nil {
		return m.CouponReduction
	}
	return 0
}

func (m *UserValidCoupon) GetCouponDiscount() uint32 {
	if m != nil {
		return m.CouponDiscount
	}
	return 0
}

func (m *UserValidCoupon) GetCouponStartTs() int64 {
	if m != nil {
		return m.CouponStartTs
	}
	return 0
}

func (m *UserValidCoupon) GetCouponStopTs() int64 {
	if m != nil {
		return m.CouponStopTs
	}
	return 0
}

func (m *UserValidCoupon) GetContentLimits() []*GameContentLimit {
	if m != nil {
		return m.ContentLimits
	}
	return nil
}

func (m *UserValidCoupon) GetPlaymateUidLimits() []uint32 {
	if m != nil {
		return m.PlaymateUidLimits
	}
	return nil
}

type GameContentLimit struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Chapters             []uint32 `protobuf:"varint,2,rep,packed,name=chapters,proto3" json:"chapters,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameContentLimit) Reset()         { *m = GameContentLimit{} }
func (m *GameContentLimit) String() string { return proto.CompactTextString(m) }
func (*GameContentLimit) ProtoMessage()    {}
func (*GameContentLimit) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{44}
}
func (m *GameContentLimit) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameContentLimit.Unmarshal(m, b)
}
func (m *GameContentLimit) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameContentLimit.Marshal(b, m, deterministic)
}
func (dst *GameContentLimit) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameContentLimit.Merge(dst, src)
}
func (m *GameContentLimit) XXX_Size() int {
	return xxx_messageInfo_GameContentLimit.Size(m)
}
func (m *GameContentLimit) XXX_DiscardUnknown() {
	xxx_messageInfo_GameContentLimit.DiscardUnknown(m)
}

var xxx_messageInfo_GameContentLimit proto.InternalMessageInfo

func (m *GameContentLimit) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GameContentLimit) GetChapters() []uint32 {
	if m != nil {
		return m.Chapters
	}
	return nil
}

type GetGameTicketOrderQueryOptionsRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameTicketOrderQueryOptionsRequest) Reset()         { *m = GetGameTicketOrderQueryOptionsRequest{} }
func (m *GetGameTicketOrderQueryOptionsRequest) String() string { return proto.CompactTextString(m) }
func (*GetGameTicketOrderQueryOptionsRequest) ProtoMessage()    {}
func (*GetGameTicketOrderQueryOptionsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{45}
}
func (m *GetGameTicketOrderQueryOptionsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameTicketOrderQueryOptionsRequest.Unmarshal(m, b)
}
func (m *GetGameTicketOrderQueryOptionsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameTicketOrderQueryOptionsRequest.Marshal(b, m, deterministic)
}
func (dst *GetGameTicketOrderQueryOptionsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameTicketOrderQueryOptionsRequest.Merge(dst, src)
}
func (m *GetGameTicketOrderQueryOptionsRequest) XXX_Size() int {
	return xxx_messageInfo_GetGameTicketOrderQueryOptionsRequest.Size(m)
}
func (m *GetGameTicketOrderQueryOptionsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameTicketOrderQueryOptionsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameTicketOrderQueryOptionsRequest proto.InternalMessageInfo

type GetGameTicketOrderQueryOptionsResponse struct {
	Opts                 []*GameTicketOrderQueryOption `protobuf:"bytes,1,rep,name=opts,proto3" json:"opts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetGameTicketOrderQueryOptionsResponse) Reset() {
	*m = GetGameTicketOrderQueryOptionsResponse{}
}
func (m *GetGameTicketOrderQueryOptionsResponse) String() string { return proto.CompactTextString(m) }
func (*GetGameTicketOrderQueryOptionsResponse) ProtoMessage()    {}
func (*GetGameTicketOrderQueryOptionsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{46}
}
func (m *GetGameTicketOrderQueryOptionsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameTicketOrderQueryOptionsResponse.Unmarshal(m, b)
}
func (m *GetGameTicketOrderQueryOptionsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameTicketOrderQueryOptionsResponse.Marshal(b, m, deterministic)
}
func (dst *GetGameTicketOrderQueryOptionsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameTicketOrderQueryOptionsResponse.Merge(dst, src)
}
func (m *GetGameTicketOrderQueryOptionsResponse) XXX_Size() int {
	return xxx_messageInfo_GetGameTicketOrderQueryOptionsResponse.Size(m)
}
func (m *GetGameTicketOrderQueryOptionsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameTicketOrderQueryOptionsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameTicketOrderQueryOptionsResponse proto.InternalMessageInfo

func (m *GetGameTicketOrderQueryOptionsResponse) GetOpts() []*GameTicketOrderQueryOption {
	if m != nil {
		return m.Opts
	}
	return nil
}

type GameTicketOrderQueryOption struct {
	QueryType            GameTicketOrderQueryType        `protobuf:"varint,1,opt,name=query_type,json=queryType,proto3,enum=game_ticket_mgr.GameTicketOrderQueryType" json:"query_type,omitempty"`
	SubType              []string                        `protobuf:"bytes,2,rep,name=sub_type,json=subType,proto3" json:"sub_type,omitempty"`
	Status               []*GameTicketOrderStatusWrapper `protobuf:"bytes,3,rep,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *GameTicketOrderQueryOption) Reset()         { *m = GameTicketOrderQueryOption{} }
func (m *GameTicketOrderQueryOption) String() string { return proto.CompactTextString(m) }
func (*GameTicketOrderQueryOption) ProtoMessage()    {}
func (*GameTicketOrderQueryOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{47}
}
func (m *GameTicketOrderQueryOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameTicketOrderQueryOption.Unmarshal(m, b)
}
func (m *GameTicketOrderQueryOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameTicketOrderQueryOption.Marshal(b, m, deterministic)
}
func (dst *GameTicketOrderQueryOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameTicketOrderQueryOption.Merge(dst, src)
}
func (m *GameTicketOrderQueryOption) XXX_Size() int {
	return xxx_messageInfo_GameTicketOrderQueryOption.Size(m)
}
func (m *GameTicketOrderQueryOption) XXX_DiscardUnknown() {
	xxx_messageInfo_GameTicketOrderQueryOption.DiscardUnknown(m)
}

var xxx_messageInfo_GameTicketOrderQueryOption proto.InternalMessageInfo

func (m *GameTicketOrderQueryOption) GetQueryType() GameTicketOrderQueryType {
	if m != nil {
		return m.QueryType
	}
	return GameTicketOrderQueryType_GAME_TICKET_ORDER_QUERY_TYPE_UNSPECIFIED
}

func (m *GameTicketOrderQueryOption) GetSubType() []string {
	if m != nil {
		return m.SubType
	}
	return nil
}

func (m *GameTicketOrderQueryOption) GetStatus() []*GameTicketOrderStatusWrapper {
	if m != nil {
		return m.Status
	}
	return nil
}

type GetGameTicketOrderListRequest struct {
	Uid                  uint32                       `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	QueryMonth           uint32                       `protobuf:"varint,2,opt,name=query_month,json=queryMonth,proto3" json:"query_month,omitempty"`
	Opt                  *GameTicketOrderQueryOption  `protobuf:"bytes,3,opt,name=opt,proto3" json:"opt,omitempty"`
	Count                uint32                       `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	LoadMore             *GameTicketOrderListLoadMore `protobuf:"bytes,5,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *GetGameTicketOrderListRequest) Reset()         { *m = GetGameTicketOrderListRequest{} }
func (m *GetGameTicketOrderListRequest) String() string { return proto.CompactTextString(m) }
func (*GetGameTicketOrderListRequest) ProtoMessage()    {}
func (*GetGameTicketOrderListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{48}
}
func (m *GetGameTicketOrderListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameTicketOrderListRequest.Unmarshal(m, b)
}
func (m *GetGameTicketOrderListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameTicketOrderListRequest.Marshal(b, m, deterministic)
}
func (dst *GetGameTicketOrderListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameTicketOrderListRequest.Merge(dst, src)
}
func (m *GetGameTicketOrderListRequest) XXX_Size() int {
	return xxx_messageInfo_GetGameTicketOrderListRequest.Size(m)
}
func (m *GetGameTicketOrderListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameTicketOrderListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameTicketOrderListRequest proto.InternalMessageInfo

func (m *GetGameTicketOrderListRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetGameTicketOrderListRequest) GetQueryMonth() uint32 {
	if m != nil {
		return m.QueryMonth
	}
	return 0
}

func (m *GetGameTicketOrderListRequest) GetOpt() *GameTicketOrderQueryOption {
	if m != nil {
		return m.Opt
	}
	return nil
}

func (m *GetGameTicketOrderListRequest) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetGameTicketOrderListRequest) GetLoadMore() *GameTicketOrderListLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type GetGameTicketOrderListResponse struct {
	OrderList            []*GameTicketOrder           `protobuf:"bytes,1,rep,name=order_list,json=orderList,proto3" json:"order_list,omitempty"`
	LoadMore             *GameTicketOrderListLoadMore `protobuf:"bytes,2,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *GetGameTicketOrderListResponse) Reset()         { *m = GetGameTicketOrderListResponse{} }
func (m *GetGameTicketOrderListResponse) String() string { return proto.CompactTextString(m) }
func (*GetGameTicketOrderListResponse) ProtoMessage()    {}
func (*GetGameTicketOrderListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{49}
}
func (m *GetGameTicketOrderListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameTicketOrderListResponse.Unmarshal(m, b)
}
func (m *GetGameTicketOrderListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameTicketOrderListResponse.Marshal(b, m, deterministic)
}
func (dst *GetGameTicketOrderListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameTicketOrderListResponse.Merge(dst, src)
}
func (m *GetGameTicketOrderListResponse) XXX_Size() int {
	return xxx_messageInfo_GetGameTicketOrderListResponse.Size(m)
}
func (m *GetGameTicketOrderListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameTicketOrderListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameTicketOrderListResponse proto.InternalMessageInfo

func (m *GetGameTicketOrderListResponse) GetOrderList() []*GameTicketOrder {
	if m != nil {
		return m.OrderList
	}
	return nil
}

func (m *GetGameTicketOrderListResponse) GetLoadMore() *GameTicketOrderListLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type GameTicketOrderListLoadMore struct {
	LastPage             uint32   `protobuf:"varint,1,opt,name=last_page,json=lastPage,proto3" json:"last_page,omitempty"`
	LastCount            uint32   `protobuf:"varint,2,opt,name=last_count,json=lastCount,proto3" json:"last_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameTicketOrderListLoadMore) Reset()         { *m = GameTicketOrderListLoadMore{} }
func (m *GameTicketOrderListLoadMore) String() string { return proto.CompactTextString(m) }
func (*GameTicketOrderListLoadMore) ProtoMessage()    {}
func (*GameTicketOrderListLoadMore) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{50}
}
func (m *GameTicketOrderListLoadMore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameTicketOrderListLoadMore.Unmarshal(m, b)
}
func (m *GameTicketOrderListLoadMore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameTicketOrderListLoadMore.Marshal(b, m, deterministic)
}
func (dst *GameTicketOrderListLoadMore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameTicketOrderListLoadMore.Merge(dst, src)
}
func (m *GameTicketOrderListLoadMore) XXX_Size() int {
	return xxx_messageInfo_GameTicketOrderListLoadMore.Size(m)
}
func (m *GameTicketOrderListLoadMore) XXX_DiscardUnknown() {
	xxx_messageInfo_GameTicketOrderListLoadMore.DiscardUnknown(m)
}

var xxx_messageInfo_GameTicketOrderListLoadMore proto.InternalMessageInfo

func (m *GameTicketOrderListLoadMore) GetLastPage() uint32 {
	if m != nil {
		return m.LastPage
	}
	return 0
}

func (m *GameTicketOrderListLoadMore) GetLastCount() uint32 {
	if m != nil {
		return m.LastCount
	}
	return 0
}

type GameTicketOrderStatusWrapper struct {
	Status               GameTicketOrderStatus `protobuf:"varint,1,opt,name=status,proto3,enum=game_ticket_mgr.GameTicketOrderStatus" json:"status,omitempty"`
	Text                 string                `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GameTicketOrderStatusWrapper) Reset()         { *m = GameTicketOrderStatusWrapper{} }
func (m *GameTicketOrderStatusWrapper) String() string { return proto.CompactTextString(m) }
func (*GameTicketOrderStatusWrapper) ProtoMessage()    {}
func (*GameTicketOrderStatusWrapper) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{51}
}
func (m *GameTicketOrderStatusWrapper) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameTicketOrderStatusWrapper.Unmarshal(m, b)
}
func (m *GameTicketOrderStatusWrapper) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameTicketOrderStatusWrapper.Marshal(b, m, deterministic)
}
func (dst *GameTicketOrderStatusWrapper) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameTicketOrderStatusWrapper.Merge(dst, src)
}
func (m *GameTicketOrderStatusWrapper) XXX_Size() int {
	return xxx_messageInfo_GameTicketOrderStatusWrapper.Size(m)
}
func (m *GameTicketOrderStatusWrapper) XXX_DiscardUnknown() {
	xxx_messageInfo_GameTicketOrderStatusWrapper.DiscardUnknown(m)
}

var xxx_messageInfo_GameTicketOrderStatusWrapper proto.InternalMessageInfo

func (m *GameTicketOrderStatusWrapper) GetStatus() GameTicketOrderStatus {
	if m != nil {
		return m.Status
	}
	return GameTicketOrderStatus_GAME_TICKET_ORDER_STATUS_UNSPECIFIED
}

func (m *GameTicketOrderStatusWrapper) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

type GameTicketOrder struct {
	OrderDesc            string                        `protobuf:"bytes,1,opt,name=order_desc,json=orderDesc,proto3" json:"order_desc,omitempty"`
	Status               *GameTicketOrderStatusWrapper `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	OrderId              string                        `protobuf:"bytes,3,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	UpdatedAt            uint32                        `protobuf:"varint,4,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	Balance              uint32                        `protobuf:"varint,5,opt,name=balance,proto3" json:"balance,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GameTicketOrder) Reset()         { *m = GameTicketOrder{} }
func (m *GameTicketOrder) String() string { return proto.CompactTextString(m) }
func (*GameTicketOrder) ProtoMessage()    {}
func (*GameTicketOrder) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ticket_mgr_387d25772a98ec31, []int{52}
}
func (m *GameTicketOrder) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameTicketOrder.Unmarshal(m, b)
}
func (m *GameTicketOrder) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameTicketOrder.Marshal(b, m, deterministic)
}
func (dst *GameTicketOrder) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameTicketOrder.Merge(dst, src)
}
func (m *GameTicketOrder) XXX_Size() int {
	return xxx_messageInfo_GameTicketOrder.Size(m)
}
func (m *GameTicketOrder) XXX_DiscardUnknown() {
	xxx_messageInfo_GameTicketOrder.DiscardUnknown(m)
}

var xxx_messageInfo_GameTicketOrder proto.InternalMessageInfo

func (m *GameTicketOrder) GetOrderDesc() string {
	if m != nil {
		return m.OrderDesc
	}
	return ""
}

func (m *GameTicketOrder) GetStatus() *GameTicketOrderStatusWrapper {
	if m != nil {
		return m.Status
	}
	return nil
}

func (m *GameTicketOrder) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *GameTicketOrder) GetUpdatedAt() uint32 {
	if m != nil {
		return m.UpdatedAt
	}
	return 0
}

func (m *GameTicketOrder) GetBalance() uint32 {
	if m != nil {
		return m.Balance
	}
	return 0
}

func init() {
	proto.RegisterType((*GameTicketCfg)(nil), "game_ticket_mgr.GameTicketCfg")
	proto.RegisterType((*AddGameTicketCfgResp)(nil), "game_ticket_mgr.AddGameTicketCfgResp")
	proto.RegisterType((*DelGameTicketCfgReq)(nil), "game_ticket_mgr.DelGameTicketCfgReq")
	proto.RegisterType((*DelGameTicketCfgResp)(nil), "game_ticket_mgr.DelGameTicketCfgResp")
	proto.RegisterType((*GetAllGameTicketCfgReq)(nil), "game_ticket_mgr.GetAllGameTicketCfgReq")
	proto.RegisterType((*GameTicketCfgList)(nil), "game_ticket_mgr.GameTicketCfgList")
	proto.RegisterType((*GetGameTicketCfgReq)(nil), "game_ticket_mgr.GetGameTicketCfgReq")
	proto.RegisterType((*TicketRemainDetail)(nil), "game_ticket_mgr.TicketRemainDetail")
	proto.RegisterType((*GetUserRemainDetailReq)(nil), "game_ticket_mgr.GetUserRemainDetailReq")
	proto.RegisterType((*UserRemainDetail)(nil), "game_ticket_mgr.UserRemainDetail")
	proto.RegisterType((*RemainSummary)(nil), "game_ticket_mgr.RemainSummary")
	proto.RegisterType((*GetUserRemainSummaryReq)(nil), "game_ticket_mgr.GetUserRemainSummaryReq")
	proto.RegisterType((*UserRemainSummary)(nil), "game_ticket_mgr.UserRemainSummary")
	proto.RegisterType((*SendUserGameTicketReq)(nil), "game_ticket_mgr.SendUserGameTicketReq")
	proto.RegisterType((*SendUserGameTicketResp)(nil), "game_ticket_mgr.SendUserGameTicketResp")
	proto.RegisterType((*BatchSendUserGameTicketReq)(nil), "game_ticket_mgr.BatchSendUserGameTicketReq")
	proto.RegisterType((*Empty)(nil), "game_ticket_mgr.Empty")
	proto.RegisterType((*FreezeUserGameTicketReq)(nil), "game_ticket_mgr.FreezeUserGameTicketReq")
	proto.RegisterType((*UsageContext)(nil), "game_ticket_mgr.UsageContext")
	proto.RegisterType((*FreezeUserGameTicketResp)(nil), "game_ticket_mgr.FreezeUserGameTicketResp")
	proto.RegisterType((*ConfirmFreezeOrderReq)(nil), "game_ticket_mgr.ConfirmFreezeOrderReq")
	proto.RegisterType((*ConfirmFreezeOrderResp)(nil), "game_ticket_mgr.ConfirmFreezeOrderResp")
	proto.RegisterType((*CouponContentLimit)(nil), "game_ticket_mgr.CouponContentLimit")
	proto.RegisterType((*CouponConfig)(nil), "game_ticket_mgr.CouponConfig")
	proto.RegisterType((*AddCouponConfigReq)(nil), "game_ticket_mgr.AddCouponConfigReq")
	proto.RegisterType((*AddCouponConfigResp)(nil), "game_ticket_mgr.AddCouponConfigResp")
	proto.RegisterType((*ModifyCouponConfigReq)(nil), "game_ticket_mgr.ModifyCouponConfigReq")
	proto.RegisterType((*ModifyCouponConfigResp)(nil), "game_ticket_mgr.ModifyCouponConfigResp")
	proto.RegisterType((*ListCouponConfigReq)(nil), "game_ticket_mgr.ListCouponConfigReq")
	proto.RegisterType((*ListCouponConfigResp)(nil), "game_ticket_mgr.ListCouponConfigResp")
	proto.RegisterType((*CouponGrantUser)(nil), "game_ticket_mgr.CouponGrantUser")
	proto.RegisterType((*BatGrantCouponReq)(nil), "game_ticket_mgr.BatGrantCouponReq")
	proto.RegisterType((*BatGrantCouponResp)(nil), "game_ticket_mgr.BatGrantCouponResp")
	proto.RegisterType((*GrantCouponRecord)(nil), "game_ticket_mgr.GrantCouponRecord")
	proto.RegisterType((*GrantCouponReq)(nil), "game_ticket_mgr.GrantCouponReq")
	proto.RegisterType((*GrantCouponResp)(nil), "game_ticket_mgr.GrantCouponResp")
	proto.RegisterType((*GetUserCouponCountReq)(nil), "game_ticket_mgr.GetUserCouponCountReq")
	proto.RegisterType((*GetUserCouponCountResp)(nil), "game_ticket_mgr.GetUserCouponCountResp")
	proto.RegisterType((*GetUserCouponListReq)(nil), "game_ticket_mgr.GetUserCouponListReq")
	proto.RegisterType((*GetUserCouponListResp)(nil), "game_ticket_mgr.GetUserCouponListResp")
	proto.RegisterType((*UserCouponDetail)(nil), "game_ticket_mgr.UserCouponDetail")
	proto.RegisterType((*GetUserValidCouponReq)(nil), "game_ticket_mgr.GetUserValidCouponReq")
	proto.RegisterType((*GetUserValidCouponResp)(nil), "game_ticket_mgr.GetUserValidCouponResp")
	proto.RegisterType((*UserValidCoupon)(nil), "game_ticket_mgr.UserValidCoupon")
	proto.RegisterType((*GameContentLimit)(nil), "game_ticket_mgr.GameContentLimit")
	proto.RegisterType((*GetGameTicketOrderQueryOptionsRequest)(nil), "game_ticket_mgr.GetGameTicketOrderQueryOptionsRequest")
	proto.RegisterType((*GetGameTicketOrderQueryOptionsResponse)(nil), "game_ticket_mgr.GetGameTicketOrderQueryOptionsResponse")
	proto.RegisterType((*GameTicketOrderQueryOption)(nil), "game_ticket_mgr.GameTicketOrderQueryOption")
	proto.RegisterType((*GetGameTicketOrderListRequest)(nil), "game_ticket_mgr.GetGameTicketOrderListRequest")
	proto.RegisterType((*GetGameTicketOrderListResponse)(nil), "game_ticket_mgr.GetGameTicketOrderListResponse")
	proto.RegisterType((*GameTicketOrderListLoadMore)(nil), "game_ticket_mgr.GameTicketOrderListLoadMore")
	proto.RegisterType((*GameTicketOrderStatusWrapper)(nil), "game_ticket_mgr.GameTicketOrderStatusWrapper")
	proto.RegisterType((*GameTicketOrder)(nil), "game_ticket_mgr.GameTicketOrder")
	proto.RegisterEnum("game_ticket_mgr.GainSourceType", GainSourceType_name, GainSourceType_value)
	proto.RegisterEnum("game_ticket_mgr.CostDestType", CostDestType_name, CostDestType_value)
	proto.RegisterEnum("game_ticket_mgr.ExpireType", ExpireType_name, ExpireType_value)
	proto.RegisterEnum("game_ticket_mgr.CostPriorityType", CostPriorityType_name, CostPriorityType_value)
	proto.RegisterEnum("game_ticket_mgr.FreezeStatus", FreezeStatus_name, FreezeStatus_value)
	proto.RegisterEnum("game_ticket_mgr.CouponType", CouponType_name, CouponType_value)
	proto.RegisterEnum("game_ticket_mgr.CouponGrantSource", CouponGrantSource_name, CouponGrantSource_value)
	proto.RegisterEnum("game_ticket_mgr.CouponStatus", CouponStatus_name, CouponStatus_value)
	proto.RegisterEnum("game_ticket_mgr.CouponQueryType", CouponQueryType_name, CouponQueryType_value)
	proto.RegisterEnum("game_ticket_mgr.GameTicketOrderQueryType", GameTicketOrderQueryType_name, GameTicketOrderQueryType_value)
	proto.RegisterEnum("game_ticket_mgr.GameTicketOrderStatus", GameTicketOrderStatus_name, GameTicketOrderStatus_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GameTicketMgrClient is the client API for GameTicketMgr service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GameTicketMgrClient interface {
	// 新增游戏券配置
	AddGameTicketCfg(ctx context.Context, in *GameTicketCfg, opts ...grpc.CallOption) (*AddGameTicketCfgResp, error)
	// 删除游戏券配置
	DelGameTicketCfg(ctx context.Context, in *DelGameTicketCfgReq, opts ...grpc.CallOption) (*DelGameTicketCfgResp, error)
	// 获取所有游戏券配置
	GetAllGameTicketCfg(ctx context.Context, in *GetAllGameTicketCfgReq, opts ...grpc.CallOption) (*GameTicketCfgList, error)
	// 获取游戏券配置
	GetGameTicketCfg(ctx context.Context, in *GetGameTicketCfgReq, opts ...grpc.CallOption) (*GameTicketCfg, error)
	// 获取用户游戏券余额明细
	GetUserRemainDetail(ctx context.Context, in *GetUserRemainDetailReq, opts ...grpc.CallOption) (*UserRemainDetail, error)
	// 获取用户游戏券余额汇总
	GetUserRemainSummary(ctx context.Context, in *GetUserRemainSummaryReq, opts ...grpc.CallOption) (*UserRemainSummary, error)
	// 发放游戏券
	SendUserGameTicket(ctx context.Context, in *SendUserGameTicketReq, opts ...grpc.CallOption) (*SendUserGameTicketResp, error)
	// 批量发放游戏券
	BatchSendUserGameTicket(ctx context.Context, in *BatchSendUserGameTicketReq, opts ...grpc.CallOption) (*Empty, error)
	// 冻结游戏券
	FreezeUserGameTicket(ctx context.Context, in *FreezeUserGameTicketReq, opts ...grpc.CallOption) (*FreezeUserGameTicketResp, error)
	// 确认冻结游戏券
	ConfirmFreezeOrder(ctx context.Context, in *ConfirmFreezeOrderReq, opts ...grpc.CallOption) (*ConfirmFreezeOrderResp, error)
	// 发放数据对账
	GetGainTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetGainOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// 使用数据对账
	GetCostTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetCostOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// 新增优惠券配置
	AddCouponConfig(ctx context.Context, in *AddCouponConfigReq, opts ...grpc.CallOption) (*AddCouponConfigResp, error)
	// 修改优惠券配置
	ModifyCouponConfig(ctx context.Context, in *ModifyCouponConfigReq, opts ...grpc.CallOption) (*ModifyCouponConfigResp, error)
	// 获取优惠券配置列表
	ListCouponConfig(ctx context.Context, in *ListCouponConfigReq, opts ...grpc.CallOption) (*ListCouponConfigResp, error)
	// 批量发放优惠券，多用于管理后台发放
	BatGrantCoupon(ctx context.Context, in *BatGrantCouponReq, opts ...grpc.CallOption) (*BatGrantCouponResp, error)
	// 发放优惠券，多用于程序发放
	GrantCoupon(ctx context.Context, in *GrantCouponReq, opts ...grpc.CallOption) (*GrantCouponResp, error)
	// 查询用户优惠券数量
	GetUserCouponCount(ctx context.Context, in *GetUserCouponCountReq, opts ...grpc.CallOption) (*GetUserCouponCountResp, error)
	// 查询用户优惠券列表
	GetUserCouponList(ctx context.Context, in *GetUserCouponListReq, opts ...grpc.CallOption) (*GetUserCouponListResp, error)
	// 查询用户生效中的优惠券，成都密境专用
	GetUserValidCoupon(ctx context.Context, in *GetUserValidCouponReq, opts ...grpc.CallOption) (*GetUserValidCouponResp, error)
	// 获取密室券订单查询选项
	GetGameTicketOrderQueryOptions(ctx context.Context, in *GetGameTicketOrderQueryOptionsRequest, opts ...grpc.CallOption) (*GetGameTicketOrderQueryOptionsResponse, error)
	// 获取密室券订单列表
	GetGameTicketOrderList(ctx context.Context, in *GetGameTicketOrderListRequest, opts ...grpc.CallOption) (*GetGameTicketOrderListResponse, error)
}

type gameTicketMgrClient struct {
	cc *grpc.ClientConn
}

func NewGameTicketMgrClient(cc *grpc.ClientConn) GameTicketMgrClient {
	return &gameTicketMgrClient{cc}
}

func (c *gameTicketMgrClient) AddGameTicketCfg(ctx context.Context, in *GameTicketCfg, opts ...grpc.CallOption) (*AddGameTicketCfgResp, error) {
	out := new(AddGameTicketCfgResp)
	err := c.cc.Invoke(ctx, "/game_ticket_mgr.GameTicketMgr/AddGameTicketCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketMgrClient) DelGameTicketCfg(ctx context.Context, in *DelGameTicketCfgReq, opts ...grpc.CallOption) (*DelGameTicketCfgResp, error) {
	out := new(DelGameTicketCfgResp)
	err := c.cc.Invoke(ctx, "/game_ticket_mgr.GameTicketMgr/DelGameTicketCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketMgrClient) GetAllGameTicketCfg(ctx context.Context, in *GetAllGameTicketCfgReq, opts ...grpc.CallOption) (*GameTicketCfgList, error) {
	out := new(GameTicketCfgList)
	err := c.cc.Invoke(ctx, "/game_ticket_mgr.GameTicketMgr/GetAllGameTicketCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketMgrClient) GetGameTicketCfg(ctx context.Context, in *GetGameTicketCfgReq, opts ...grpc.CallOption) (*GameTicketCfg, error) {
	out := new(GameTicketCfg)
	err := c.cc.Invoke(ctx, "/game_ticket_mgr.GameTicketMgr/GetGameTicketCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketMgrClient) GetUserRemainDetail(ctx context.Context, in *GetUserRemainDetailReq, opts ...grpc.CallOption) (*UserRemainDetail, error) {
	out := new(UserRemainDetail)
	err := c.cc.Invoke(ctx, "/game_ticket_mgr.GameTicketMgr/GetUserRemainDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketMgrClient) GetUserRemainSummary(ctx context.Context, in *GetUserRemainSummaryReq, opts ...grpc.CallOption) (*UserRemainSummary, error) {
	out := new(UserRemainSummary)
	err := c.cc.Invoke(ctx, "/game_ticket_mgr.GameTicketMgr/GetUserRemainSummary", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketMgrClient) SendUserGameTicket(ctx context.Context, in *SendUserGameTicketReq, opts ...grpc.CallOption) (*SendUserGameTicketResp, error) {
	out := new(SendUserGameTicketResp)
	err := c.cc.Invoke(ctx, "/game_ticket_mgr.GameTicketMgr/SendUserGameTicket", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketMgrClient) BatchSendUserGameTicket(ctx context.Context, in *BatchSendUserGameTicketReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, "/game_ticket_mgr.GameTicketMgr/BatchSendUserGameTicket", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketMgrClient) FreezeUserGameTicket(ctx context.Context, in *FreezeUserGameTicketReq, opts ...grpc.CallOption) (*FreezeUserGameTicketResp, error) {
	out := new(FreezeUserGameTicketResp)
	err := c.cc.Invoke(ctx, "/game_ticket_mgr.GameTicketMgr/FreezeUserGameTicket", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketMgrClient) ConfirmFreezeOrder(ctx context.Context, in *ConfirmFreezeOrderReq, opts ...grpc.CallOption) (*ConfirmFreezeOrderResp, error) {
	out := new(ConfirmFreezeOrderResp)
	err := c.cc.Invoke(ctx, "/game_ticket_mgr.GameTicketMgr/ConfirmFreezeOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketMgrClient) GetGainTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/game_ticket_mgr.GameTicketMgr/GetGainTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketMgrClient) GetGainOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/game_ticket_mgr.GameTicketMgr/GetGainOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketMgrClient) GetCostTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/game_ticket_mgr.GameTicketMgr/GetCostTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketMgrClient) GetCostOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/game_ticket_mgr.GameTicketMgr/GetCostOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketMgrClient) AddCouponConfig(ctx context.Context, in *AddCouponConfigReq, opts ...grpc.CallOption) (*AddCouponConfigResp, error) {
	out := new(AddCouponConfigResp)
	err := c.cc.Invoke(ctx, "/game_ticket_mgr.GameTicketMgr/AddCouponConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketMgrClient) ModifyCouponConfig(ctx context.Context, in *ModifyCouponConfigReq, opts ...grpc.CallOption) (*ModifyCouponConfigResp, error) {
	out := new(ModifyCouponConfigResp)
	err := c.cc.Invoke(ctx, "/game_ticket_mgr.GameTicketMgr/ModifyCouponConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketMgrClient) ListCouponConfig(ctx context.Context, in *ListCouponConfigReq, opts ...grpc.CallOption) (*ListCouponConfigResp, error) {
	out := new(ListCouponConfigResp)
	err := c.cc.Invoke(ctx, "/game_ticket_mgr.GameTicketMgr/ListCouponConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketMgrClient) BatGrantCoupon(ctx context.Context, in *BatGrantCouponReq, opts ...grpc.CallOption) (*BatGrantCouponResp, error) {
	out := new(BatGrantCouponResp)
	err := c.cc.Invoke(ctx, "/game_ticket_mgr.GameTicketMgr/BatGrantCoupon", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketMgrClient) GrantCoupon(ctx context.Context, in *GrantCouponReq, opts ...grpc.CallOption) (*GrantCouponResp, error) {
	out := new(GrantCouponResp)
	err := c.cc.Invoke(ctx, "/game_ticket_mgr.GameTicketMgr/GrantCoupon", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketMgrClient) GetUserCouponCount(ctx context.Context, in *GetUserCouponCountReq, opts ...grpc.CallOption) (*GetUserCouponCountResp, error) {
	out := new(GetUserCouponCountResp)
	err := c.cc.Invoke(ctx, "/game_ticket_mgr.GameTicketMgr/GetUserCouponCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketMgrClient) GetUserCouponList(ctx context.Context, in *GetUserCouponListReq, opts ...grpc.CallOption) (*GetUserCouponListResp, error) {
	out := new(GetUserCouponListResp)
	err := c.cc.Invoke(ctx, "/game_ticket_mgr.GameTicketMgr/GetUserCouponList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketMgrClient) GetUserValidCoupon(ctx context.Context, in *GetUserValidCouponReq, opts ...grpc.CallOption) (*GetUserValidCouponResp, error) {
	out := new(GetUserValidCouponResp)
	err := c.cc.Invoke(ctx, "/game_ticket_mgr.GameTicketMgr/GetUserValidCoupon", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketMgrClient) GetGameTicketOrderQueryOptions(ctx context.Context, in *GetGameTicketOrderQueryOptionsRequest, opts ...grpc.CallOption) (*GetGameTicketOrderQueryOptionsResponse, error) {
	out := new(GetGameTicketOrderQueryOptionsResponse)
	err := c.cc.Invoke(ctx, "/game_ticket_mgr.GameTicketMgr/GetGameTicketOrderQueryOptions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameTicketMgrClient) GetGameTicketOrderList(ctx context.Context, in *GetGameTicketOrderListRequest, opts ...grpc.CallOption) (*GetGameTicketOrderListResponse, error) {
	out := new(GetGameTicketOrderListResponse)
	err := c.cc.Invoke(ctx, "/game_ticket_mgr.GameTicketMgr/GetGameTicketOrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GameTicketMgrServer is the server API for GameTicketMgr service.
type GameTicketMgrServer interface {
	// 新增游戏券配置
	AddGameTicketCfg(context.Context, *GameTicketCfg) (*AddGameTicketCfgResp, error)
	// 删除游戏券配置
	DelGameTicketCfg(context.Context, *DelGameTicketCfgReq) (*DelGameTicketCfgResp, error)
	// 获取所有游戏券配置
	GetAllGameTicketCfg(context.Context, *GetAllGameTicketCfgReq) (*GameTicketCfgList, error)
	// 获取游戏券配置
	GetGameTicketCfg(context.Context, *GetGameTicketCfgReq) (*GameTicketCfg, error)
	// 获取用户游戏券余额明细
	GetUserRemainDetail(context.Context, *GetUserRemainDetailReq) (*UserRemainDetail, error)
	// 获取用户游戏券余额汇总
	GetUserRemainSummary(context.Context, *GetUserRemainSummaryReq) (*UserRemainSummary, error)
	// 发放游戏券
	SendUserGameTicket(context.Context, *SendUserGameTicketReq) (*SendUserGameTicketResp, error)
	// 批量发放游戏券
	BatchSendUserGameTicket(context.Context, *BatchSendUserGameTicketReq) (*Empty, error)
	// 冻结游戏券
	FreezeUserGameTicket(context.Context, *FreezeUserGameTicketReq) (*FreezeUserGameTicketResp, error)
	// 确认冻结游戏券
	ConfirmFreezeOrder(context.Context, *ConfirmFreezeOrderReq) (*ConfirmFreezeOrderResp, error)
	// 发放数据对账
	GetGainTotalCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetGainOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// 使用数据对账
	GetCostTotalCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetCostOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// 新增优惠券配置
	AddCouponConfig(context.Context, *AddCouponConfigReq) (*AddCouponConfigResp, error)
	// 修改优惠券配置
	ModifyCouponConfig(context.Context, *ModifyCouponConfigReq) (*ModifyCouponConfigResp, error)
	// 获取优惠券配置列表
	ListCouponConfig(context.Context, *ListCouponConfigReq) (*ListCouponConfigResp, error)
	// 批量发放优惠券，多用于管理后台发放
	BatGrantCoupon(context.Context, *BatGrantCouponReq) (*BatGrantCouponResp, error)
	// 发放优惠券，多用于程序发放
	GrantCoupon(context.Context, *GrantCouponReq) (*GrantCouponResp, error)
	// 查询用户优惠券数量
	GetUserCouponCount(context.Context, *GetUserCouponCountReq) (*GetUserCouponCountResp, error)
	// 查询用户优惠券列表
	GetUserCouponList(context.Context, *GetUserCouponListReq) (*GetUserCouponListResp, error)
	// 查询用户生效中的优惠券，成都密境专用
	GetUserValidCoupon(context.Context, *GetUserValidCouponReq) (*GetUserValidCouponResp, error)
	// 获取密室券订单查询选项
	GetGameTicketOrderQueryOptions(context.Context, *GetGameTicketOrderQueryOptionsRequest) (*GetGameTicketOrderQueryOptionsResponse, error)
	// 获取密室券订单列表
	GetGameTicketOrderList(context.Context, *GetGameTicketOrderListRequest) (*GetGameTicketOrderListResponse, error)
}

func RegisterGameTicketMgrServer(s *grpc.Server, srv GameTicketMgrServer) {
	s.RegisterService(&_GameTicketMgr_serviceDesc, srv)
}

func _GameTicketMgr_AddGameTicketCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GameTicketCfg)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketMgrServer).AddGameTicketCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_mgr.GameTicketMgr/AddGameTicketCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketMgrServer).AddGameTicketCfg(ctx, req.(*GameTicketCfg))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketMgr_DelGameTicketCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelGameTicketCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketMgrServer).DelGameTicketCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_mgr.GameTicketMgr/DelGameTicketCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketMgrServer).DelGameTicketCfg(ctx, req.(*DelGameTicketCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketMgr_GetAllGameTicketCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllGameTicketCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketMgrServer).GetAllGameTicketCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_mgr.GameTicketMgr/GetAllGameTicketCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketMgrServer).GetAllGameTicketCfg(ctx, req.(*GetAllGameTicketCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketMgr_GetGameTicketCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameTicketCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketMgrServer).GetGameTicketCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_mgr.GameTicketMgr/GetGameTicketCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketMgrServer).GetGameTicketCfg(ctx, req.(*GetGameTicketCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketMgr_GetUserRemainDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserRemainDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketMgrServer).GetUserRemainDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_mgr.GameTicketMgr/GetUserRemainDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketMgrServer).GetUserRemainDetail(ctx, req.(*GetUserRemainDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketMgr_GetUserRemainSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserRemainSummaryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketMgrServer).GetUserRemainSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_mgr.GameTicketMgr/GetUserRemainSummary",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketMgrServer).GetUserRemainSummary(ctx, req.(*GetUserRemainSummaryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketMgr_SendUserGameTicket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendUserGameTicketReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketMgrServer).SendUserGameTicket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_mgr.GameTicketMgr/SendUserGameTicket",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketMgrServer).SendUserGameTicket(ctx, req.(*SendUserGameTicketReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketMgr_BatchSendUserGameTicket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchSendUserGameTicketReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketMgrServer).BatchSendUserGameTicket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_mgr.GameTicketMgr/BatchSendUserGameTicket",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketMgrServer).BatchSendUserGameTicket(ctx, req.(*BatchSendUserGameTicketReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketMgr_FreezeUserGameTicket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FreezeUserGameTicketReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketMgrServer).FreezeUserGameTicket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_mgr.GameTicketMgr/FreezeUserGameTicket",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketMgrServer).FreezeUserGameTicket(ctx, req.(*FreezeUserGameTicketReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketMgr_ConfirmFreezeOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfirmFreezeOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketMgrServer).ConfirmFreezeOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_mgr.GameTicketMgr/ConfirmFreezeOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketMgrServer).ConfirmFreezeOrder(ctx, req.(*ConfirmFreezeOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketMgr_GetGainTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketMgrServer).GetGainTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_mgr.GameTicketMgr/GetGainTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketMgrServer).GetGainTotalCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketMgr_GetGainOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketMgrServer).GetGainOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_mgr.GameTicketMgr/GetGainOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketMgrServer).GetGainOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketMgr_GetCostTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketMgrServer).GetCostTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_mgr.GameTicketMgr/GetCostTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketMgrServer).GetCostTotalCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketMgr_GetCostOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketMgrServer).GetCostOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_mgr.GameTicketMgr/GetCostOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketMgrServer).GetCostOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketMgr_AddCouponConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddCouponConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketMgrServer).AddCouponConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_mgr.GameTicketMgr/AddCouponConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketMgrServer).AddCouponConfig(ctx, req.(*AddCouponConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketMgr_ModifyCouponConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyCouponConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketMgrServer).ModifyCouponConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_mgr.GameTicketMgr/ModifyCouponConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketMgrServer).ModifyCouponConfig(ctx, req.(*ModifyCouponConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketMgr_ListCouponConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCouponConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketMgrServer).ListCouponConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_mgr.GameTicketMgr/ListCouponConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketMgrServer).ListCouponConfig(ctx, req.(*ListCouponConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketMgr_BatGrantCoupon_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGrantCouponReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketMgrServer).BatGrantCoupon(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_mgr.GameTicketMgr/BatGrantCoupon",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketMgrServer).BatGrantCoupon(ctx, req.(*BatGrantCouponReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketMgr_GrantCoupon_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GrantCouponReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketMgrServer).GrantCoupon(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_mgr.GameTicketMgr/GrantCoupon",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketMgrServer).GrantCoupon(ctx, req.(*GrantCouponReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketMgr_GetUserCouponCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserCouponCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketMgrServer).GetUserCouponCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_mgr.GameTicketMgr/GetUserCouponCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketMgrServer).GetUserCouponCount(ctx, req.(*GetUserCouponCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketMgr_GetUserCouponList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserCouponListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketMgrServer).GetUserCouponList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_mgr.GameTicketMgr/GetUserCouponList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketMgrServer).GetUserCouponList(ctx, req.(*GetUserCouponListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketMgr_GetUserValidCoupon_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserValidCouponReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketMgrServer).GetUserValidCoupon(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_mgr.GameTicketMgr/GetUserValidCoupon",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketMgrServer).GetUserValidCoupon(ctx, req.(*GetUserValidCouponReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketMgr_GetGameTicketOrderQueryOptions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameTicketOrderQueryOptionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketMgrServer).GetGameTicketOrderQueryOptions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_mgr.GameTicketMgr/GetGameTicketOrderQueryOptions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketMgrServer).GetGameTicketOrderQueryOptions(ctx, req.(*GetGameTicketOrderQueryOptionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameTicketMgr_GetGameTicketOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameTicketOrderListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameTicketMgrServer).GetGameTicketOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_ticket_mgr.GameTicketMgr/GetGameTicketOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameTicketMgrServer).GetGameTicketOrderList(ctx, req.(*GetGameTicketOrderListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _GameTicketMgr_serviceDesc = grpc.ServiceDesc{
	ServiceName: "game_ticket_mgr.GameTicketMgr",
	HandlerType: (*GameTicketMgrServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddGameTicketCfg",
			Handler:    _GameTicketMgr_AddGameTicketCfg_Handler,
		},
		{
			MethodName: "DelGameTicketCfg",
			Handler:    _GameTicketMgr_DelGameTicketCfg_Handler,
		},
		{
			MethodName: "GetAllGameTicketCfg",
			Handler:    _GameTicketMgr_GetAllGameTicketCfg_Handler,
		},
		{
			MethodName: "GetGameTicketCfg",
			Handler:    _GameTicketMgr_GetGameTicketCfg_Handler,
		},
		{
			MethodName: "GetUserRemainDetail",
			Handler:    _GameTicketMgr_GetUserRemainDetail_Handler,
		},
		{
			MethodName: "GetUserRemainSummary",
			Handler:    _GameTicketMgr_GetUserRemainSummary_Handler,
		},
		{
			MethodName: "SendUserGameTicket",
			Handler:    _GameTicketMgr_SendUserGameTicket_Handler,
		},
		{
			MethodName: "BatchSendUserGameTicket",
			Handler:    _GameTicketMgr_BatchSendUserGameTicket_Handler,
		},
		{
			MethodName: "FreezeUserGameTicket",
			Handler:    _GameTicketMgr_FreezeUserGameTicket_Handler,
		},
		{
			MethodName: "ConfirmFreezeOrder",
			Handler:    _GameTicketMgr_ConfirmFreezeOrder_Handler,
		},
		{
			MethodName: "GetGainTotalCount",
			Handler:    _GameTicketMgr_GetGainTotalCount_Handler,
		},
		{
			MethodName: "GetGainOrderIds",
			Handler:    _GameTicketMgr_GetGainOrderIds_Handler,
		},
		{
			MethodName: "GetCostTotalCount",
			Handler:    _GameTicketMgr_GetCostTotalCount_Handler,
		},
		{
			MethodName: "GetCostOrderIds",
			Handler:    _GameTicketMgr_GetCostOrderIds_Handler,
		},
		{
			MethodName: "AddCouponConfig",
			Handler:    _GameTicketMgr_AddCouponConfig_Handler,
		},
		{
			MethodName: "ModifyCouponConfig",
			Handler:    _GameTicketMgr_ModifyCouponConfig_Handler,
		},
		{
			MethodName: "ListCouponConfig",
			Handler:    _GameTicketMgr_ListCouponConfig_Handler,
		},
		{
			MethodName: "BatGrantCoupon",
			Handler:    _GameTicketMgr_BatGrantCoupon_Handler,
		},
		{
			MethodName: "GrantCoupon",
			Handler:    _GameTicketMgr_GrantCoupon_Handler,
		},
		{
			MethodName: "GetUserCouponCount",
			Handler:    _GameTicketMgr_GetUserCouponCount_Handler,
		},
		{
			MethodName: "GetUserCouponList",
			Handler:    _GameTicketMgr_GetUserCouponList_Handler,
		},
		{
			MethodName: "GetUserValidCoupon",
			Handler:    _GameTicketMgr_GetUserValidCoupon_Handler,
		},
		{
			MethodName: "GetGameTicketOrderQueryOptions",
			Handler:    _GameTicketMgr_GetGameTicketOrderQueryOptions_Handler,
		},
		{
			MethodName: "GetGameTicketOrderList",
			Handler:    _GameTicketMgr_GetGameTicketOrderList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "game-ticket-mgr/game-ticket-mgr.proto",
}

func init() {
	proto.RegisterFile("game-ticket-mgr/game-ticket-mgr.proto", fileDescriptor_game_ticket_mgr_387d25772a98ec31)
}

var fileDescriptor_game_ticket_mgr_387d25772a98ec31 = []byte{
	// 3010 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x5a, 0xcd, 0x73, 0x1b, 0xc7,
	0xb1, 0xe7, 0x02, 0xe0, 0x07, 0x9a, 0x04, 0xb9, 0x1c, 0x91, 0x12, 0x04, 0x59, 0x26, 0xb5, 0xfa,
	0x20, 0xc5, 0x67, 0x51, 0xf5, 0xe8, 0x67, 0xbb, 0x9e, 0x9e, 0x5f, 0x5c, 0x10, 0x00, 0x52, 0xb0,
	0x49, 0x50, 0x5e, 0x00, 0x8a, 0x65, 0xc7, 0xde, 0x5a, 0xee, 0x0e, 0xa1, 0x8d, 0x80, 0xdd, 0xd5,
	0xee, 0x40, 0x36, 0x93, 0x53, 0x4e, 0xc9, 0x39, 0xf9, 0x1b, 0x52, 0x39, 0x24, 0x95, 0x5b, 0xce,
	0x3e, 0xe4, 0x90, 0x54, 0xe5, 0x94, 0x6b, 0xee, 0xfe, 0x07, 0x52, 0xf9, 0x03, 0x52, 0xf3, 0x81,
	0xc5, 0x7e, 0x0c, 0x40, 0xd2, 0x8e, 0x53, 0xb9, 0x61, 0x7a, 0x7a, 0x7a, 0x7b, 0xba, 0x7f, 0xd3,
	0xd3, 0xd3, 0x0d, 0xb8, 0xdb, 0x33, 0x07, 0xf8, 0x01, 0x71, 0xac, 0x97, 0x98, 0x3c, 0x18, 0xf4,
	0x82, 0x87, 0xa9, 0xf1, 0xae, 0x1f, 0x78, 0xc4, 0x43, 0x2b, 0x94, 0x6c, 0x70, 0xb2, 0x31, 0xe8,
	0x05, 0x95, 0x8d, 0x00, 0x5b, 0x9e, 0x6b, 0x39, 0x7d, 0xfc, 0xe0, 0xf5, 0xde, 0xc3, 0xf8, 0x80,
	0xaf, 0xd0, 0xfe, 0xaa, 0x40, 0xe9, 0xc0, 0x1c, 0xe0, 0x0e, 0x5b, 0x53, 0x3b, 0xed, 0xa1, 0x1b,
	0x50, 0x14, 0x02, 0x1c, 0xbb, 0xac, 0x6c, 0x2a, 0xdb, 0x25, 0x7d, 0x81, 0x13, 0x9a, 0x36, 0x42,
	0x50, 0x70, 0xcd, 0x01, 0x2e, 0xe7, 0x36, 0x95, 0xed, 0xa2, 0xce, 0x7e, 0x53, 0x9a, 0x8d, 0x43,
	0xab, 0x9c, 0xe7, 0x34, 0xfa, 0x1b, 0xbd, 0x0f, 0x8b, 0xf8, 0x2b, 0xdf, 0x09, 0xb0, 0x41, 0xce,
	0x7c, 0x5c, 0x2e, 0x6c, 0x2a, 0xdb, 0xcb, 0x7b, 0x37, 0x76, 0x53, 0xea, 0xed, 0x36, 0x18, 0x4f,
	0xe7, 0xcc, 0xc7, 0x3a, 0xe0, 0xe8, 0x37, 0xda, 0x82, 0x15, 0xb1, 0xda, 0x1e, 0x06, 0x26, 0x71,
	0x3c, 0xb7, 0x3c, 0xbb, 0xa9, 0x6c, 0xe7, 0xf5, 0x65, 0x4e, 0xae, 0x0b, 0x2a, 0x5a, 0x83, 0x59,
	0x3f, 0x70, 0x2c, 0x5c, 0x9e, 0x63, 0x7a, 0xf2, 0x81, 0x76, 0x15, 0xd6, 0xaa, 0xb6, 0x9d, 0xd8,
	0x95, 0x8e, 0x43, 0x5f, 0xdb, 0x83, 0x2b, 0x75, 0xdc, 0x4f, 0xd1, 0x5f, 0x4d, 0xdd, 0x30, 0x95,
	0x95, 0x5d, 0x13, 0xfa, 0x5a, 0x19, 0xae, 0x1e, 0x60, 0x52, 0xed, 0x67, 0xc4, 0x69, 0x2d, 0x58,
	0x4d, 0xd0, 0x0e, 0x9d, 0x90, 0xa0, 0xff, 0x85, 0x05, 0xeb, 0xb4, 0x67, 0xf4, 0x9d, 0x90, 0x94,
	0x95, 0xcd, 0xfc, 0xf6, 0xe2, 0xde, 0x9b, 0x19, 0x63, 0x24, 0x25, 0xcd, 0x5b, 0x7c, 0x29, 0xd5,
	0xfa, 0x00, 0x93, 0xcb, 0x69, 0xfd, 0x27, 0x05, 0x10, 0xe7, 0xd6, 0xf1, 0xc0, 0x74, 0xdc, 0x3a,
	0x26, 0xa6, 0xd3, 0xbf, 0xbc, 0x6b, 0x37, 0xc6, 0x6e, 0x74, 0x06, 0x98, 0x79, 0x38, 0x1f, 0x79,
	0xca, 0x19, 0x60, 0xf4, 0x1e, 0xcc, 0x85, 0xde, 0x30, 0xb0, 0x46, 0x2e, 0xde, 0x90, 0xec, 0xca,
	0x71, 0xdb, 0x8c, 0x85, 0xb9, 0x59, 0xb0, 0x23, 0x15, 0xf2, 0xee, 0x70, 0xc0, 0xdc, 0x5a, 0xd2,
	0xe9, 0xcf, 0x09, 0xbe, 0x3c, 0x60, 0x76, 0xee, 0x86, 0x38, 0x88, 0xef, 0x84, 0x1a, 0x40, 0x85,
	0xfc, 0x30, 0xda, 0x06, 0xfd, 0x99, 0xdc, 0x5e, 0x2e, 0x65, 0x92, 0xcf, 0x41, 0x4d, 0x4b, 0x91,
	0x88, 0x78, 0x0f, 0x0a, 0xcc, 0x47, 0x39, 0xe6, 0xa3, 0xdb, 0x99, 0xdd, 0x64, 0x8d, 0xaa, 0xb3,
	0x05, 0xda, 0x0b, 0x28, 0x71, 0x6a, 0x7b, 0x38, 0x18, 0x98, 0xc1, 0xd9, 0xe5, 0x6d, 0x2d, 0x2c,
	0x92, 0x97, 0x58, 0xa4, 0x10, 0xb7, 0xc8, 0x13, 0xb8, 0x96, 0xb0, 0x88, 0xf8, 0xe0, 0xb7, 0x30,
	0xc9, 0x73, 0x58, 0xcd, 0x88, 0x91, 0xc8, 0xd8, 0x4b, 0xd8, 0x24, 0x8b, 0xdb, 0xa4, 0x1a, 0xdc,
	0x1c, 0xff, 0x50, 0x60, 0xbd, 0x8d, 0x5d, 0x9b, 0xca, 0x1f, 0x43, 0x57, 0xae, 0xe3, 0x75, 0x58,
	0xf0, 0x02, 0x1b, 0x07, 0x23, 0x15, 0x8b, 0xfa, 0x3c, 0x1b, 0x37, 0x53, 0xea, 0xe7, 0x53, 0x46,
	0x14, 0x06, 0x2b, 0x8c, 0x0d, 0x36, 0x46, 0xe3, 0xec, 0xe5, 0xd0, 0x78, 0x0b, 0x96, 0xbc, 0x21,
	0x09, 0x1d, 0x5b, 0x00, 0x7d, 0x8e, 0x01, 0x7d, 0x51, 0xd0, 0x18, 0xd2, 0x6f, 0x43, 0xc9, 0xb4,
	0x88, 0xf3, 0xda, 0x21, 0x67, 0x06, 0xf3, 0xdd, 0x3c, 0x53, 0x75, 0x69, 0x44, 0x6c, 0x99, 0x03,
	0xac, 0xbd, 0x03, 0x57, 0x65, 0xbb, 0x0e, 0x7d, 0xba, 0x93, 0x53, 0xc7, 0x35, 0xfb, 0x06, 0x55,
	0x59, 0xc0, 0x81, 0x11, 0x5a, 0xc3, 0x81, 0xf6, 0x09, 0x54, 0x1e, 0x9b, 0xc4, 0x7a, 0x21, 0xb7,
	0xd8, 0x23, 0x61, 0x7f, 0x1e, 0x37, 0xee, 0x65, 0xf6, 0x24, 0x5d, 0x25, 0xfc, 0x30, 0x0f, 0xb3,
	0x8d, 0x81, 0x4f, 0xce, 0xb4, 0x5f, 0xe7, 0xe1, 0xda, 0x7e, 0x80, 0xf1, 0x4f, 0xf0, 0xbf, 0xd7,
	0x25, 0x8f, 0xa0, 0x68, 0x79, 0x21, 0xe1, 0xd7, 0x00, 0xf7, 0xca, 0xcd, 0xcc, 0x0e, 0x6a, 0x5e,
	0x48, 0xea, 0x38, 0x24, 0xcc, 0x27, 0x0b, 0x94, 0x9f, 0x5d, 0x03, 0xfb, 0x50, 0x62, 0x6b, 0xfd,
	0xc0, 0xf1, 0x02, 0x87, 0x9c, 0x31, 0xb7, 0x2c, 0xef, 0xdd, 0x92, 0xae, 0x7f, 0x2a, 0x98, 0x98,
	0x8c, 0x25, 0x2b, 0x46, 0xa1, 0x2a, 0x5b, 0xde, 0xd0, 0xf7, 0x5c, 0xaa, 0x32, 0x77, 0xdb, 0x02,
	0x27, 0x34, 0x6d, 0xb4, 0x03, 0xab, 0x62, 0x32, 0xc0, 0xf6, 0xd0, 0xc2, 0xcc, 0x41, 0x0b, 0x6c,
	0x03, 0x2b, 0x7c, 0x42, 0x67, 0xf4, 0xd6, 0x70, 0x80, 0x6e, 0x02, 0x0c, 0x43, 0xb3, 0x27, 0x2e,
	0xb5, 0x22, 0x63, 0x2a, 0x32, 0x0a, 0xd3, 0xf7, 0x31, 0x94, 0xf8, 0xb4, 0xe5, 0xb9, 0x04, 0x7f,
	0x45, 0xca, 0xb0, 0xa9, 0x6c, 0x2f, 0x4a, 0xf6, 0xdb, 0xa5, 0x5c, 0x35, 0xce, 0xa4, 0x2f, 0x0d,
	0x63, 0x23, 0xed, 0xe7, 0x0a, 0x2c, 0xc5, 0xa7, 0xd1, 0x35, 0x98, 0x67, 0xcb, 0x23, 0x07, 0xcd,
	0xd1, 0x61, 0xd3, 0x46, 0x15, 0x58, 0xb0, 0x5e, 0x98, 0x3e, 0xc1, 0x41, 0xc8, 0x8e, 0x66, 0x49,
	0x8f, 0xc6, 0x14, 0xcf, 0x7e, 0xdf, 0x3c, 0x1b, 0x98, 0x04, 0x1b, 0xc3, 0xc8, 0x4f, 0x8b, 0x23,
	0x5a, 0xd7, 0xb1, 0x69, 0x68, 0xa7, 0x43, 0x1c, 0x50, 0x86, 0xb0, 0x5c, 0x60, 0x12, 0x80, 0x93,
	0xba, 0x8e, 0x1d, 0x6a, 0x3f, 0x53, 0xa0, 0x2c, 0x47, 0x4c, 0xe8, 0xd3, 0xd5, 0xa7, 0x6c, 0x8e,
	0x9f, 0x17, 0x85, 0x5f, 0x0c, 0x9c, 0xc4, 0x8e, 0x4b, 0x3d, 0x62, 0xb8, 0x6c, 0x3c, 0x15, 0x52,
	0xd8, 0xdd, 0xf7, 0x5b, 0x05, 0xd6, 0x6b, 0x9e, 0x7b, 0xea, 0x04, 0x03, 0xae, 0xca, 0x31, 0x45,
	0xe1, 0xa5, 0x31, 0x5b, 0x87, 0x65, 0x8b, 0x4b, 0x31, 0x42, 0x62, 0x92, 0x61, 0xc8, 0x0c, 0x22,
	0x43, 0x22, 0xff, 0x4a, 0x9b, 0x31, 0xe9, 0x25, 0xb1, 0x88, 0x0f, 0xd3, 0x7b, 0x2e, 0xa4, 0xf7,
	0x4c, 0x73, 0x02, 0x99, 0xb2, 0xa1, 0xaf, 0x1d, 0x00, 0xaa, 0x31, 0x2c, 0x31, 0xaf, 0xba, 0xe4,
	0xd0, 0x19, 0x38, 0x04, 0xad, 0xc3, 0x1c, 0x31, 0x4f, 0xc6, 0x9e, 0x9d, 0x25, 0xe6, 0xc9, 0x74,
	0xc7, 0x6a, 0xdf, 0xe4, 0x61, 0x29, 0x92, 0x74, 0xea, 0xf4, 0xd0, 0x1d, 0xba, 0x35, 0x06, 0x5f,
	0xaa, 0xec, 0x58, 0xd6, 0x92, 0x15, 0x71, 0x35, 0x99, 0xb3, 0x05, 0x57, 0xec, 0xda, 0x01, 0x4e,
	0xa2, 0x81, 0x0b, 0xe9, 0x70, 0x45, 0x30, 0xf4, 0x02, 0xd3, 0x25, 0x86, 0x08, 0xa3, 0xdc, 0x4c,
	0x9a, 0xe4, 0xc0, 0x51, 0xde, 0x03, 0xca, 0xca, 0xa3, 0xa9, 0x2e, 0x0e, 0x51, 0x8c, 0x44, 0x73,
	0x40, 0x21, 0x73, 0x6a, 0x0e, 0xc8, 0x65, 0xf1, 0x1c, 0xd0, 0x8a, 0x7e, 0xa3, 0xfb, 0xa0, 0xc6,
	0xcf, 0x65, 0x94, 0x04, 0x26, 0x8f, 0x25, 0xcb, 0x02, 0xb7, 0x40, 0x90, 0x0c, 0xdb, 0x09, 0x2d,
	0x6f, 0xe8, 0x12, 0x91, 0x43, 0x08, 0xd3, 0xd4, 0x05, 0x15, 0x7d, 0xc8, 0x70, 0x40, 0x1d, 0x60,
	0xf4, 0xa9, 0x07, 0xc2, 0xf2, 0xfc, 0x04, 0x5c, 0x66, 0xbd, 0xc5, 0xd0, 0x10, 0x8d, 0x42, 0xea,
	0x25, 0xcf, 0xc7, 0x81, 0x49, 0xbc, 0x80, 0x85, 0x8b, 0xa2, 0x1e, 0x8d, 0x99, 0xb9, 0x03, 0x4c,
	0x0f, 0x1f, 0x43, 0x4a, 0x91, 0x23, 0x85, 0x93, 0xd8, 0xe9, 0xd8, 0x80, 0xc5, 0x81, 0x67, 0x3b,
	0xa7, 0x67, 0x9c, 0x01, 0x38, 0x03, 0x27, 0x31, 0x28, 0x7d, 0x02, 0xa8, 0x6a, 0xdb, 0x71, 0x4f,
	0x53, 0xd0, 0x3f, 0xa6, 0x01, 0x31, 0x72, 0xb6, 0xd3, 0x63, 0xbe, 0x5e, 0x94, 0x06, 0xd4, 0xd8,
	0xc2, 0x18, 0x14, 0x9c, 0x9e, 0xb6, 0x0e, 0x57, 0x32, 0x92, 0x43, 0x5f, 0xfb, 0x0c, 0xd6, 0x8f,
	0xd8, 0xe7, 0xbf, 0x8f, 0x6f, 0x96, 0xe1, 0xaa, 0x4c, 0x78, 0xe8, 0x53, 0x6d, 0xe8, 0x41, 0x4f,
	0x7d, 0x54, 0xfb, 0x11, 0xac, 0x65, 0xc9, 0xa1, 0xcf, 0x0f, 0x72, 0x4c, 0x99, 0x50, 0x5c, 0x8a,
	0xe7, 0x68, 0x53, 0x8a, 0x6b, 0x13, 0x6a, 0x6d, 0x58, 0x89, 0x01, 0x98, 0x46, 0x37, 0x49, 0x38,
	0xd9, 0x85, 0x2b, 0xf1, 0x10, 0x3a, 0x02, 0x0c, 0x3f, 0x90, 0xab, 0xb1, 0x48, 0xca, 0xf1, 0xa0,
	0x7d, 0xa3, 0xc0, 0xea, 0x63, 0x93, 0x30, 0x91, 0x35, 0x01, 0xd0, 0x57, 0xe8, 0x7d, 0x98, 0xa7,
	0x8f, 0xae, 0xc0, 0x1e, 0x69, 0x9a, 0x3d, 0x4b, 0x89, 0x15, 0x94, 0x55, 0x1f, 0x2d, 0xa1, 0x39,
	0x07, 0x3f, 0x88, 0x86, 0xcd, 0x62, 0xa3, 0x78, 0x62, 0x2d, 0x71, 0xa2, 0x48, 0x62, 0x6f, 0x02,
	0xf0, 0x33, 0xeb, 0x7a, 0x84, 0x9f, 0xb2, 0xa2, 0x5e, 0x64, 0x94, 0x96, 0x47, 0x70, 0x02, 0xa7,
	0xb3, 0x29, 0x9c, 0x5e, 0x87, 0x85, 0x90, 0x98, 0x01, 0x31, 0x48, 0x28, 0x52, 0x9e, 0x79, 0x36,
	0xee, 0x84, 0xf4, 0xda, 0x09, 0x89, 0xe7, 0x1b, 0xec, 0x8c, 0xd0, 0x99, 0x39, 0x3a, 0xec, 0x84,
	0xda, 0x1a, 0xa0, 0xf4, 0x36, 0x43, 0x5f, 0xfb, 0x4d, 0x0e, 0x56, 0x33, 0x1b, 0x41, 0xef, 0xc2,
	0xec, 0x30, 0xa4, 0x61, 0x8c, 0x63, 0x66, 0x73, 0x5a, 0x1c, 0x61, 0x99, 0x28, 0x67, 0x97, 0x04,
	0xb5, 0x9c, 0x24, 0xa8, 0xfd, 0x07, 0x5a, 0x07, 0x6d, 0xb2, 0x44, 0xd2, 0x88, 0x2e, 0x22, 0x1e,
	0x19, 0xc0, 0x1b, 0x92, 0x63, 0x7e, 0x17, 0x69, 0x87, 0xb0, 0x9c, 0xc2, 0xc8, 0x23, 0x98, 0xe3,
	0x0e, 0x17, 0x66, 0xba, 0x08, 0x44, 0xc4, 0x0a, 0x6d, 0x15, 0x56, 0xd2, 0xae, 0xf8, 0x31, 0xac,
	0x8b, 0xf7, 0xc1, 0xe8, 0x0c, 0x0c, 0xdd, 0x09, 0x69, 0xde, 0x07, 0x00, 0xaf, 0x86, 0x38, 0x38,
	0xe3, 0x01, 0x3a, 0xc7, 0x02, 0xf4, 0x24, 0x27, 0x7d, 0x4c, 0x19, 0x59, 0x94, 0x2e, 0xbe, 0x1a,
	0xfd, 0xd4, 0x76, 0xa3, 0xd7, 0x59, 0xe2, 0x5b, 0xa1, 0x4f, 0xdf, 0x2e, 0x3c, 0x12, 0x8b, 0xab,
	0x8d, 0x0d, 0x34, 0x07, 0xd6, 0x12, 0xfc, 0xf4, 0x90, 0x7f, 0x4f, 0xaa, 0x75, 0x52, 0x66, 0xe0,
	0x9f, 0x0a, 0x7d, 0xf4, 0x7f, 0x30, 0xcf, 0x61, 0x34, 0x3a, 0x92, 0xb7, 0x24, 0xf9, 0xd9, 0x68,
	0x95, 0xc8, 0x49, 0x46, 0x2b, 0xb4, 0x3f, 0xe4, 0xf8, 0x33, 0x32, 0x3e, 0x9b, 0xcc, 0x2f, 0x95,
	0x54, 0x7e, 0x39, 0x8e, 0x9f, 0x22, 0xf5, 0xc8, 0x4d, 0x4c, 0x82, 0x29, 0x97, 0x48, 0x3d, 0x04,
	0xd2, 0x45, 0xe6, 0x71, 0x2f, 0xba, 0xe0, 0x22, 0x40, 0xf2, 0xa7, 0x78, 0x29, 0x62, 0x63, 0xb0,
	0x1c, 0x9f, 0x9b, 0x11, 0x3a, 0x79, 0x92, 0x12, 0x49, 0x63, 0x18, 0x9d, 0x10, 0xd9, 0x66, 0x27,
	0x44, 0xb6, 0xec, 0x0d, 0x00, 0x97, 0xbf, 0x01, 0xee, 0x47, 0xde, 0x78, 0x66, 0xf6, 0x1d, 0x7b,
	0x0c, 0xfe, 0x8c, 0xe7, 0xb5, 0x4e, 0x84, 0xa9, 0x04, 0x6b, 0xe8, 0xa3, 0x47, 0x69, 0xcf, 0x6d,
	0x4a, 0x3d, 0x17, 0x5f, 0x16, 0x39, 0xee, 0x17, 0x79, 0x58, 0x49, 0x4d, 0x4e, 0xf7, 0x5b, 0x2a,
	0x7b, 0xc9, 0x7d, 0xf7, 0xec, 0x25, 0x7f, 0xe1, 0xec, 0xa5, 0x20, 0xcd, 0x5e, 0x24, 0x28, 0x98,
	0xbd, 0x18, 0x0a, 0xe6, 0x24, 0x28, 0x78, 0x32, 0x21, 0x17, 0xba, 0x25, 0xad, 0x4b, 0x4d, 0xcb,
	0x84, 0x26, 0xe0, 0x69, 0x61, 0xd2, 0x4d, 0x79, 0x00, 0x6a, 0x5a, 0xe4, 0xb7, 0x7a, 0xe5, 0x68,
	0x5b, 0x70, 0x37, 0x51, 0x19, 0x63, 0x21, 0x96, 0x05, 0x83, 0x63, 0x9f, 0x9a, 0x36, 0xd4, 0xf1,
	0xab, 0x21, 0x0e, 0x69, 0xd8, 0xb9, 0x77, 0x1e, 0x63, 0x48, 0x51, 0x82, 0xd1, 0x07, 0x50, 0xf0,
	0x7c, 0x32, 0xc2, 0xd7, 0x7f, 0x4d, 0xa9, 0xd1, 0xa5, 0x65, 0xe8, 0x6c, 0xa1, 0xf6, 0x67, 0x05,
	0x2a, 0x93, 0x99, 0xd0, 0x93, 0x44, 0x58, 0x53, 0x18, 0xa8, 0xee, 0x5f, 0xe8, 0x2b, 0xa9, 0xf8,
	0xc6, 0x6e, 0xa7, 0xe1, 0xc9, 0x08, 0x9c, 0x79, 0xfa, 0xdc, 0x09, 0x87, 0x27, 0x6c, 0xaa, 0x01,
	0x73, 0xd1, 0x33, 0x87, 0x6e, 0xe3, 0xc1, 0x79, 0x1f, 0xe0, 0x61, 0xe6, 0x87, 0x81, 0xe9, 0xfb,
	0x38, 0xd0, 0xc5, 0x62, 0xed, 0xef, 0x0a, 0xdc, 0xcc, 0x9a, 0x4d, 0x84, 0x6c, 0x6a, 0x57, 0x49,
	0xd8, 0xde, 0x80, 0x45, 0xbe, 0xbf, 0x81, 0xe7, 0x92, 0x17, 0xe2, 0xda, 0xe6, 0x5b, 0x3e, 0xa2,
	0x14, 0xf4, 0xff, 0x90, 0xf7, 0x7c, 0xc2, 0xce, 0xc2, 0x25, 0xed, 0x4b, 0xd7, 0x8d, 0xaf, 0x95,
	0x42, 0xec, 0x5a, 0x41, 0x4d, 0x28, 0xf6, 0x3d, 0xd3, 0x36, 0x06, 0x5e, 0xc0, 0x8b, 0x0c, 0x8b,
	0x7b, 0x6f, 0x9d, 0x27, 0x9a, 0xee, 0xe3, 0xd0, 0x33, 0xed, 0x23, 0x2f, 0xc0, 0xfa, 0x42, 0x5f,
	0xfc, 0xd2, 0x7e, 0xa7, 0xc0, 0x9b, 0x93, 0x36, 0x1d, 0x61, 0x04, 0xf8, 0xfd, 0x1e, 0xab, 0xca,
	0x6c, 0x9e, 0xf7, 0x39, 0xbd, 0xe8, 0x8d, 0x04, 0x25, 0xd5, 0xcd, 0x7d, 0x27, 0x75, 0x9f, 0xc3,
	0x8d, 0x29, 0x8c, 0x34, 0xc2, 0xf5, 0xcd, 0x90, 0x18, 0xbe, 0xd9, 0xc3, 0xa3, 0xaa, 0x13, 0x25,
	0x3c, 0x35, 0x7b, 0x98, 0xa6, 0x46, 0x6c, 0x92, 0x1b, 0x94, 0xbb, 0x8a, 0xb1, 0xb3, 0x5b, 0x5c,
	0x0b, 0xe0, 0x8d, 0x69, 0x30, 0x41, 0x3f, 0x88, 0x50, 0xc6, 0x61, 0x7c, 0xef, 0x62, 0x28, 0x1b,
	0xc1, 0x0b, 0x21, 0x28, 0xb0, 0x22, 0x89, 0xa8, 0x81, 0xb2, 0xea, 0xc7, 0x5f, 0x14, 0x58, 0x49,
	0xad, 0xa2, 0x6a, 0x72, 0x73, 0xb3, 0x26, 0x03, 0x0f, 0xd3, 0xdc, 0x98, 0x75, 0x1c, 0x5a, 0x31,
	0xb0, 0x73, 0x4b, 0x7e, 0x3b, 0xb0, 0x27, 0xaa, 0x07, 0xf9, 0x64, 0xf5, 0xe0, 0x26, 0xc0, 0xd0,
	0xb7, 0x4d, 0x82, 0x6d, 0xc3, 0x1c, 0x01, 0xaf, 0x28, 0x28, 0x55, 0x82, 0xca, 0x30, 0x7f, 0x62,
	0xf6, 0x4d, 0x57, 0x54, 0x1d, 0x4b, 0xfa, 0x68, 0xb8, 0xf3, 0x7b, 0x05, 0x96, 0x93, 0x05, 0x47,
	0x74, 0x1d, 0xd6, 0xbb, 0xee, 0x4b, 0xd7, 0xfb, 0xd2, 0x4d, 0x4e, 0xa8, 0x33, 0xe8, 0x2a, 0xa0,
	0x78, 0x91, 0xc5, 0x7a, 0x61, 0x06, 0x3d, 0xac, 0x2a, 0x68, 0x15, 0x4a, 0x4d, 0xf7, 0xb5, 0x43,
	0x70, 0x0b, 0x7f, 0x49, 0x6f, 0x30, 0x35, 0x87, 0x4a, 0x50, 0x3c, 0xf4, 0x7a, 0x8e, 0xdb, 0x31,
	0xc3, 0x97, 0x6a, 0x9e, 0x0e, 0xe9, 0xca, 0xea, 0x97, 0x66, 0x60, 0xab, 0x05, 0xa4, 0xc2, 0xd2,
	0xb1, 0xc8, 0x61, 0xdb, 0xd8, 0xb5, 0xd5, 0x59, 0x84, 0x60, 0x39, 0x4e, 0xe9, 0x74, 0xd4, 0x39,
	0x4a, 0xab, 0x8a, 0xd2, 0xa5, 0xa0, 0xcd, 0xef, 0x6c, 0xc1, 0x52, 0xbc, 0x14, 0x87, 0xae, 0xc1,
	0x15, 0xa1, 0x6d, 0x9c, 0xac, 0xce, 0xec, 0x7c, 0x0a, 0x30, 0x6e, 0xdd, 0xa0, 0x45, 0x98, 0xdf,
	0xf7, 0x02, 0xfc, 0x1a, 0x07, 0xea, 0x0c, 0xba, 0x02, 0x2b, 0xd5, 0x93, 0xd0, 0xeb, 0x0f, 0xf9,
	0x53, 0xb7, 0x8d, 0x2d, 0x55, 0xa1, 0x44, 0x1d, 0xf7, 0x4d, 0xe2, 0xbc, 0x8e, 0x88, 0x39, 0xb4,
	0x0e, 0xab, 0x71, 0x22, 0x8b, 0x0f, 0x6a, 0x7e, 0xe7, 0x1e, 0xa8, 0xe9, 0x7a, 0x1e, 0x5a, 0x80,
	0xc2, 0x7e, 0x80, 0xa9, 0x95, 0x16, 0xa0, 0xf0, 0xd4, 0x74, 0x6c, 0x55, 0xd9, 0xf9, 0x1f, 0x58,
	0x8a, 0x57, 0x6b, 0x10, 0xc0, 0x1c, 0x1f, 0xab, 0x33, 0xf4, 0x77, 0xcd, 0x1b, 0x0c, 0x1c, 0xa2,
	0x2a, 0x68, 0x09, 0x16, 0x74, 0xaf, 0xdf, 0x3f, 0x31, 0xad, 0x97, 0x6a, 0x6e, 0xe7, 0x97, 0x0a,
	0xc0, 0xf8, 0xce, 0x46, 0x37, 0xe0, 0x5a, 0xed, 0xb8, 0xfb, 0xf4, 0xb8, 0x65, 0x74, 0x9e, 0x3f,
	0x6d, 0x18, 0xdd, 0x56, 0xfb, 0x69, 0xa3, 0xd6, 0xdc, 0x6f, 0x36, 0xea, 0xea, 0x0c, 0x2a, 0xc3,
	0x5a, 0x7c, 0xb2, 0xde, 0x6c, 0xd7, 0x8e, 0xbb, 0xad, 0x8e, 0xaa, 0x50, 0x37, 0xc6, 0x67, 0xf4,
	0x46, 0xbd, 0x5b, 0xeb, 0x34, 0x8f, 0x5b, 0x6a, 0x0e, 0xbd, 0x01, 0xe5, 0xf8, 0x54, 0xbb, 0xd9,
	0x3a, 0x38, 0x6c, 0x18, 0x1d, 0xbd, 0x59, 0x3d, 0x54, 0xf3, 0x68, 0x0d, 0xd4, 0xf8, 0xec, 0xbe,
	0xde, 0x68, 0xa8, 0x85, 0x9d, 0xaf, 0x15, 0x58, 0xcd, 0x94, 0x54, 0x62, 0x92, 0x0e, 0xf4, 0x6a,
	0xab, 0x63, 0xb4, 0x8f, 0xbb, 0x7a, 0xad, 0x61, 0xb4, 0x8e, 0x5b, 0x0d, 0x75, 0x06, 0xdd, 0x86,
	0x0d, 0xd9, 0x6c, 0xa7, 0x63, 0x54, 0x6b, 0x9d, 0xe6, 0xb3, 0x66, 0xe7, 0xb9, 0xaa, 0xa0, 0x4d,
	0x78, 0x63, 0x12, 0x53, 0xfd, 0xa8, 0x49, 0xd5, 0x9d, 0x20, 0xe6, 0xe8, 0xc3, 0xb1, 0x98, 0xfc,
	0x24, 0x31, 0x94, 0x89, 0x89, 0x29, 0xec, 0x7c, 0x3e, 0x2a, 0x4b, 0x09, 0x67, 0x8c, 0xed, 0xda,
	0xee, 0x54, 0x3b, 0xdd, 0xb6, 0xd1, 0x6d, 0xd5, 0x1b, 0xfb, 0xcd, 0x56, 0xca, 0xae, 0xd1, 0x64,
	0xb7, 0xdd, 0xa8, 0xab, 0x0a, 0x3d, 0x03, 0xa9, 0x19, 0x4a, 0xcf, 0xed, 0xf8, 0xa3, 0x17, 0x7b,
	0x74, 0x15, 0xa2, 0x5b, 0x70, 0x53, 0xb0, 0x7e, 0xdc, 0x6d, 0xe8, 0xcf, 0x65, 0xfe, 0x1b, 0x2b,
	0x11, 0x63, 0x79, 0x56, 0x3d, 0x6c, 0xd2, 0x4f, 0xdd, 0x84, 0xeb, 0xd9, 0xc9, 0x66, 0x8b, 0x4f,
	0xe7, 0x76, 0xfe, 0xa8, 0x40, 0x79, 0xd2, 0x35, 0x8c, 0xde, 0x82, 0xed, 0x83, 0xea, 0x51, 0xc3,
	0xe8, 0x34, 0x6b, 0x1f, 0x35, 0x3a, 0xc6, 0xb1, 0x5e, 0x6f, 0xe8, 0x93, 0xd5, 0xb8, 0x03, 0x9b,
	0x53, 0xb9, 0xab, 0x87, 0x87, 0xaa, 0x82, 0xee, 0xc3, 0xdd, 0xa9, 0x5c, 0x7a, 0xa3, 0xf6, 0xa4,
	0xaa, 0x1f, 0x34, 0xd4, 0x1c, 0xda, 0x86, 0x3b, 0x53, 0x59, 0x6b, 0xc7, 0xad, 0x76, 0xf7, 0xa8,
	0xa1, 0xe6, 0x77, 0xfe, 0xa6, 0xc0, 0xba, 0x34, 0xfc, 0xc9, 0x65, 0x44, 0xee, 0x88, 0xab, 0x7f,
	0x1b, 0x36, 0x26, 0x72, 0x1e, 0x54, 0x99, 0x4b, 0x15, 0x74, 0x17, 0x6e, 0x4d, 0x64, 0x12, 0xea,
	0xd4, 0xd5, 0x9c, 0xdc, 0x14, 0x82, 0x8d, 0x9e, 0x85, 0x4f, 0x1b, 0x75, 0x35, 0x3f, 0x55, 0x98,
	0x7e, 0x7c, 0x78, 0xf8, 0xb8, 0x5a, 0xfb, 0x48, 0x2d, 0xec, 0x7d, 0x8d, 0xe2, 0xad, 0xeb, 0xa3,
	0x5e, 0x80, 0x9e, 0x83, 0x9a, 0x6e, 0xfc, 0xa2, 0x73, 0xfa, 0xac, 0x95, 0xbb, 0x99, 0x79, 0x59,
	0xef, 0x18, 0x19, 0xa0, 0xa6, 0xfb, 0xc0, 0xe8, 0x4e, 0x66, 0xa9, 0xa4, 0xbd, 0x2c, 0xf9, 0x80,
	0xac, 0xa1, 0x8c, 0x4e, 0x58, 0x9b, 0x37, 0xdd, 0x50, 0x46, 0x5b, 0x59, 0xf5, 0xa5, 0x6d, 0xe7,
	0x8a, 0x36, 0x7d, 0x9f, 0x2c, 0xf1, 0xf8, 0x04, 0xd4, 0x74, 0x2b, 0x59, 0xb2, 0x09, 0x49, 0xb7,
	0xb9, 0x72, 0x8e, 0x15, 0x91, 0xc9, 0xb4, 0xcf, 0x34, 0x58, 0xa5, 0xda, 0x4b, 0x9a, 0xb9, 0x15,
	0xf9, 0x1b, 0x3c, 0x21, 0xcb, 0x8e, 0x6a, 0x07, 0xc9, 0x86, 0xe5, 0xf6, 0xf4, 0x6f, 0x8c, 0xdb,
	0xa3, 0x12, 0x13, 0x65, 0xa5, 0x61, 0x40, 0xd9, 0x7e, 0x1a, 0xba, 0x60, 0xd3, 0xad, 0xb2, 0x75,
	0x21, 0xbe, 0xd0, 0x47, 0x5f, 0xc0, 0xb5, 0x09, 0x1d, 0x3f, 0x94, 0x4d, 0x8a, 0x27, 0xf7, 0x06,
	0x2b, 0x57, 0xb3, 0x7f, 0xa9, 0x18, 0xf8, 0xe4, 0x0c, 0xbd, 0x84, 0x35, 0x59, 0xef, 0x46, 0x62,
	0xac, 0x09, 0x4d, 0xc1, 0xca, 0xfd, 0x0b, 0x72, 0x86, 0x3e, 0xb5, 0x59, 0xb6, 0xef, 0x21, 0xb1,
	0x99, 0xb4, 0x93, 0x23, 0xb1, 0x99, 0xbc, 0x89, 0x82, 0x9e, 0xc0, 0x2a, 0x83, 0xa6, 0xe3, 0x76,
	0x3c, 0x62, 0xf6, 0x59, 0x96, 0x8a, 0xae, 0xef, 0xea, 0xa3, 0x3f, 0xb5, 0x3c, 0xdb, 0xdb, 0xa5,
	0x79, 0x84, 0x6e, 0xba, 0x3d, 0xcc, 0x6d, 0x13, 0x9f, 0x8a, 0x4a, 0x53, 0xda, 0x0c, 0x6a, 0xc2,
	0x8a, 0x90, 0x24, 0xaa, 0x72, 0xe1, 0x34, 0x39, 0xc9, 0xa9, 0xd1, 0x0a, 0x21, 0x8a, 0x2b, 0x45,
	0x13, 0x96, 0x7f, 0x8d, 0x52, 0x54, 0xd2, 0x77, 0x56, 0xea, 0x0b, 0x58, 0x49, 0xd5, 0xf8, 0xd1,
	0x6d, 0x59, 0x98, 0x4b, 0x95, 0xdd, 0x2b, 0x77, 0xce, 0x67, 0x62, 0xf2, 0x7b, 0x80, 0xb2, 0xf5,
	0x7c, 0x89, 0xc3, 0xa5, 0x1d, 0x05, 0x89, 0xc3, 0x27, 0x34, 0x07, 0x66, 0x90, 0x09, 0x6a, 0xba,
	0x0f, 0x20, 0x09, 0x58, 0x92, 0x0e, 0x82, 0x24, 0xea, 0xca, 0x1a, 0x0a, 0xda, 0x0c, 0xfa, 0x0c,
	0x96, 0x93, 0xf5, 0x6c, 0xa4, 0xc9, 0x0e, 0x60, 0xb2, 0x66, 0x5b, 0xb9, 0x7d, 0x2e, 0x0f, 0x13,
	0xae, 0xc3, 0x62, 0x5c, 0xf2, 0xc6, 0xf4, 0xca, 0xee, 0xab, 0xca, 0xe6, 0x74, 0x86, 0x91, 0xf1,
	0xb3, 0x35, 0x57, 0x89, 0xf1, 0xa5, 0x45, 0xe0, 0xca, 0xd6, 0x85, 0xf8, 0xd8, 0x87, 0x6c, 0x06,
	0xed, 0x64, 0x05, 0x15, 0xdd, 0x9d, 0xbe, 0x5e, 0x54, 0x07, 0x2a, 0xf7, 0x2e, 0xc2, 0x96, 0xda,
	0x4e, 0xbc, 0x34, 0x37, 0x71, 0x7d, 0xb2, 0x7c, 0x38, 0x79, 0x3b, 0xa9, 0xda, 0xa1, 0x36, 0x83,
	0x7e, 0x25, 0x7d, 0xd9, 0xc7, 0xab, 0x40, 0xe8, 0xdd, 0xe9, 0x77, 0xe1, 0xa4, 0xfa, 0x52, 0xe5,
	0xbd, 0x4b, 0xaf, 0xe3, 0xa5, 0x04, 0x6d, 0x06, 0xfd, 0x94, 0x55, 0x3b, 0x25, 0x6f, 0x78, 0xb4,
	0x7b, 0x01, 0xa1, 0xb1, 0x62, 0x4c, 0xe5, 0xe1, 0x85, 0xf9, 0x47, 0x1f, 0x7f, 0xfc, 0xf6, 0xa7,
	0xff, 0xdd, 0xf3, 0xfa, 0xa6, 0xdb, 0xdb, 0x7d, 0x67, 0x8f, 0x90, 0x5d, 0xcb, 0x1b, 0x3c, 0x64,
	0xff, 0x0a, 0xb4, 0xbc, 0xfe, 0xc3, 0x10, 0x07, 0xaf, 0x1d, 0x0b, 0x87, 0xe9, 0x7f, 0x1a, 0x9e,
	0xcc, 0x31, 0x96, 0xb7, 0xff, 0x19, 0x00, 0x00, 0xff, 0xff, 0xa4, 0x9e, 0xc9, 0x71, 0x93, 0x28,
	0x00, 0x00,
}
