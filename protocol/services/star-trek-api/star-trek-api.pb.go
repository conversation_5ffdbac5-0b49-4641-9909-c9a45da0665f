// Code generated by protoc-gen-go. DO NOT EDIT.
// source: apicenter-go/star-trek-api.proto

package star_trek_api // import "golang.52tt.com/protocol/services/star-trek-api"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import star_trek "golang.52tt.com/protocol/services/star-trek"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// StarTrekApiClient is the client API for StarTrekApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type StarTrekApiClient interface {
	// 活动配置（包括时间、奖池）
	SetStarTrekConf(ctx context.Context, in *star_trek.SetStarTrekConfReq, opts ...grpc.CallOption) (*star_trek.SetStarTrekConfResp, error)
	// 获取活动配置
	GetStarTrekConf(ctx context.Context, in *star_trek.GetStarTrekConfReq, opts ...grpc.CallOption) (*star_trek.GetStarTrekConfResp, error)
	GetStarTrekConfById(ctx context.Context, in *star_trek.GetStarTrekConfByIdReq, opts ...grpc.CallOption) (*star_trek.GetStarTrekConfByIdResp, error)
	// 修改活动配置
	UpdateStarTrekConf(ctx context.Context, in *star_trek.UpdateStarTrekConfReq, opts ...grpc.CallOption) (*star_trek.UpdateStarTrekConfResp, error)
	// 删除活动配置
	DelStarTrekConf(ctx context.Context, in *star_trek.DelStarTrekConfReq, opts ...grpc.CallOption) (*star_trek.DelStarTrekConfResp, error)
	// 补给配置
	SetStarTrekSupply(ctx context.Context, in *star_trek.SetStarTrekSupplyReq, opts ...grpc.CallOption) (*star_trek.SetStarTrekSupplyResp, error)
	// 获取补给配置
	GetStarTrekSupply(ctx context.Context, in *star_trek.GetStarTrekSupplyReq, opts ...grpc.CallOption) (*star_trek.GetStarTrekSupplyResp, error)
	// 删除补给配置
	DelStarTrekSupply(ctx context.Context, in *star_trek.DelStarTrekSupplyReq, opts ...grpc.CallOption) (*star_trek.DelStarTrekSupplyResp, error)
}

type starTrekApiClient struct {
	cc *grpc.ClientConn
}

func NewStarTrekApiClient(cc *grpc.ClientConn) StarTrekApiClient {
	return &starTrekApiClient{cc}
}

func (c *starTrekApiClient) SetStarTrekConf(ctx context.Context, in *star_trek.SetStarTrekConfReq, opts ...grpc.CallOption) (*star_trek.SetStarTrekConfResp, error) {
	out := new(star_trek.SetStarTrekConfResp)
	err := c.cc.Invoke(ctx, "/star_trek_api.StarTrekApi/SetStarTrekConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrekApiClient) GetStarTrekConf(ctx context.Context, in *star_trek.GetStarTrekConfReq, opts ...grpc.CallOption) (*star_trek.GetStarTrekConfResp, error) {
	out := new(star_trek.GetStarTrekConfResp)
	err := c.cc.Invoke(ctx, "/star_trek_api.StarTrekApi/GetStarTrekConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrekApiClient) GetStarTrekConfById(ctx context.Context, in *star_trek.GetStarTrekConfByIdReq, opts ...grpc.CallOption) (*star_trek.GetStarTrekConfByIdResp, error) {
	out := new(star_trek.GetStarTrekConfByIdResp)
	err := c.cc.Invoke(ctx, "/star_trek_api.StarTrekApi/GetStarTrekConfById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrekApiClient) UpdateStarTrekConf(ctx context.Context, in *star_trek.UpdateStarTrekConfReq, opts ...grpc.CallOption) (*star_trek.UpdateStarTrekConfResp, error) {
	out := new(star_trek.UpdateStarTrekConfResp)
	err := c.cc.Invoke(ctx, "/star_trek_api.StarTrekApi/UpdateStarTrekConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrekApiClient) DelStarTrekConf(ctx context.Context, in *star_trek.DelStarTrekConfReq, opts ...grpc.CallOption) (*star_trek.DelStarTrekConfResp, error) {
	out := new(star_trek.DelStarTrekConfResp)
	err := c.cc.Invoke(ctx, "/star_trek_api.StarTrekApi/DelStarTrekConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrekApiClient) SetStarTrekSupply(ctx context.Context, in *star_trek.SetStarTrekSupplyReq, opts ...grpc.CallOption) (*star_trek.SetStarTrekSupplyResp, error) {
	out := new(star_trek.SetStarTrekSupplyResp)
	err := c.cc.Invoke(ctx, "/star_trek_api.StarTrekApi/SetStarTrekSupply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrekApiClient) GetStarTrekSupply(ctx context.Context, in *star_trek.GetStarTrekSupplyReq, opts ...grpc.CallOption) (*star_trek.GetStarTrekSupplyResp, error) {
	out := new(star_trek.GetStarTrekSupplyResp)
	err := c.cc.Invoke(ctx, "/star_trek_api.StarTrekApi/GetStarTrekSupply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrekApiClient) DelStarTrekSupply(ctx context.Context, in *star_trek.DelStarTrekSupplyReq, opts ...grpc.CallOption) (*star_trek.DelStarTrekSupplyResp, error) {
	out := new(star_trek.DelStarTrekSupplyResp)
	err := c.cc.Invoke(ctx, "/star_trek_api.StarTrekApi/DelStarTrekSupply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// StarTrekApiServer is the server API for StarTrekApi service.
type StarTrekApiServer interface {
	// 活动配置（包括时间、奖池）
	SetStarTrekConf(context.Context, *star_trek.SetStarTrekConfReq) (*star_trek.SetStarTrekConfResp, error)
	// 获取活动配置
	GetStarTrekConf(context.Context, *star_trek.GetStarTrekConfReq) (*star_trek.GetStarTrekConfResp, error)
	GetStarTrekConfById(context.Context, *star_trek.GetStarTrekConfByIdReq) (*star_trek.GetStarTrekConfByIdResp, error)
	// 修改活动配置
	UpdateStarTrekConf(context.Context, *star_trek.UpdateStarTrekConfReq) (*star_trek.UpdateStarTrekConfResp, error)
	// 删除活动配置
	DelStarTrekConf(context.Context, *star_trek.DelStarTrekConfReq) (*star_trek.DelStarTrekConfResp, error)
	// 补给配置
	SetStarTrekSupply(context.Context, *star_trek.SetStarTrekSupplyReq) (*star_trek.SetStarTrekSupplyResp, error)
	// 获取补给配置
	GetStarTrekSupply(context.Context, *star_trek.GetStarTrekSupplyReq) (*star_trek.GetStarTrekSupplyResp, error)
	// 删除补给配置
	DelStarTrekSupply(context.Context, *star_trek.DelStarTrekSupplyReq) (*star_trek.DelStarTrekSupplyResp, error)
}

func RegisterStarTrekApiServer(s *grpc.Server, srv StarTrekApiServer) {
	s.RegisterService(&_StarTrekApi_serviceDesc, srv)
}

func _StarTrekApi_SetStarTrekConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(star_trek.SetStarTrekConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrekApiServer).SetStarTrekConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_trek_api.StarTrekApi/SetStarTrekConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrekApiServer).SetStarTrekConf(ctx, req.(*star_trek.SetStarTrekConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrekApi_GetStarTrekConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(star_trek.GetStarTrekConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrekApiServer).GetStarTrekConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_trek_api.StarTrekApi/GetStarTrekConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrekApiServer).GetStarTrekConf(ctx, req.(*star_trek.GetStarTrekConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrekApi_GetStarTrekConfById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(star_trek.GetStarTrekConfByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrekApiServer).GetStarTrekConfById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_trek_api.StarTrekApi/GetStarTrekConfById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrekApiServer).GetStarTrekConfById(ctx, req.(*star_trek.GetStarTrekConfByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrekApi_UpdateStarTrekConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(star_trek.UpdateStarTrekConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrekApiServer).UpdateStarTrekConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_trek_api.StarTrekApi/UpdateStarTrekConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrekApiServer).UpdateStarTrekConf(ctx, req.(*star_trek.UpdateStarTrekConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrekApi_DelStarTrekConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(star_trek.DelStarTrekConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrekApiServer).DelStarTrekConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_trek_api.StarTrekApi/DelStarTrekConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrekApiServer).DelStarTrekConf(ctx, req.(*star_trek.DelStarTrekConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrekApi_SetStarTrekSupply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(star_trek.SetStarTrekSupplyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrekApiServer).SetStarTrekSupply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_trek_api.StarTrekApi/SetStarTrekSupply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrekApiServer).SetStarTrekSupply(ctx, req.(*star_trek.SetStarTrekSupplyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrekApi_GetStarTrekSupply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(star_trek.GetStarTrekSupplyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrekApiServer).GetStarTrekSupply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_trek_api.StarTrekApi/GetStarTrekSupply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrekApiServer).GetStarTrekSupply(ctx, req.(*star_trek.GetStarTrekSupplyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrekApi_DelStarTrekSupply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(star_trek.DelStarTrekSupplyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrekApiServer).DelStarTrekSupply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_trek_api.StarTrekApi/DelStarTrekSupply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrekApiServer).DelStarTrekSupply(ctx, req.(*star_trek.DelStarTrekSupplyReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _StarTrekApi_serviceDesc = grpc.ServiceDesc{
	ServiceName: "star_trek_api.StarTrekApi",
	HandlerType: (*StarTrekApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetStarTrekConf",
			Handler:    _StarTrekApi_SetStarTrekConf_Handler,
		},
		{
			MethodName: "GetStarTrekConf",
			Handler:    _StarTrekApi_GetStarTrekConf_Handler,
		},
		{
			MethodName: "GetStarTrekConfById",
			Handler:    _StarTrekApi_GetStarTrekConfById_Handler,
		},
		{
			MethodName: "UpdateStarTrekConf",
			Handler:    _StarTrekApi_UpdateStarTrekConf_Handler,
		},
		{
			MethodName: "DelStarTrekConf",
			Handler:    _StarTrekApi_DelStarTrekConf_Handler,
		},
		{
			MethodName: "SetStarTrekSupply",
			Handler:    _StarTrekApi_SetStarTrekSupply_Handler,
		},
		{
			MethodName: "GetStarTrekSupply",
			Handler:    _StarTrekApi_GetStarTrekSupply_Handler,
		},
		{
			MethodName: "DelStarTrekSupply",
			Handler:    _StarTrekApi_DelStarTrekSupply_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "apicenter-go/star-trek-api.proto",
}

func init() {
	proto.RegisterFile("apicenter-go/star-trek-api.proto", fileDescriptor_star_trek_api_583359928e4a723f)
}

var fileDescriptor_star_trek_api_583359928e4a723f = []byte{
	// 283 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x84, 0xd3, 0x41, 0x4b, 0xc3, 0x30,
	0x14, 0x07, 0x70, 0x2f, 0x7a, 0x88, 0x88, 0x18, 0x4f, 0x0e, 0xd4, 0x6e, 0xf7, 0xb6, 0x38, 0xf1,
	0x03, 0x38, 0x85, 0xe0, 0x75, 0x55, 0x10, 0x05, 0x47, 0xec, 0x9e, 0x25, 0xac, 0x36, 0xcf, 0xf4,
	0x29, 0xec, 0x7b, 0xfa, 0x81, 0x24, 0x1d, 0x9d, 0x5d, 0xda, 0xa6, 0xd7, 0xf7, 0xff, 0xe7, 0x47,
	0xfb, 0xda, 0xb0, 0x40, 0xa2, 0x4a, 0xa1, 0x20, 0x30, 0x61, 0xa6, 0xe3, 0x92, 0xa4, 0x09, 0xc9,
	0xc0, 0x2a, 0x94, 0xa8, 0x22, 0x34, 0x9a, 0x34, 0x3f, 0xb2, 0xc3, 0x85, 0x1d, 0x2e, 0x24, 0xaa,
	0xd1, 0xd9, 0xb6, 0xf3, 0xdf, 0xde, 0x34, 0xa7, 0xbf, 0xfb, 0xec, 0x30, 0x21, 0x69, 0x1e, 0x0d,
	0xac, 0x6e, 0x51, 0xf1, 0x39, 0x3b, 0x4e, 0x80, 0xea, 0xc9, 0x9d, 0x2e, 0x3e, 0xf8, 0x79, 0xb4,
	0xd5, 0x22, 0x27, 0x9b, 0xc3, 0xd7, 0xe8, 0xc2, 0x17, 0x97, 0x38, 0xd9, 0xb3, 0xa6, 0xf0, 0x98,
	0xc2, 0x6f, 0x8a, 0x4e, 0xf3, 0x8d, 0x9d, 0x3a, 0xc1, 0x6c, 0xfd, 0xb0, 0xe4, 0xe3, 0xfe, 0x83,
	0x36, 0xb7, 0xf6, 0x64, 0xa8, 0x52, 0xf9, 0xaf, 0x8c, 0x3f, 0xe1, 0x52, 0x12, 0xec, 0x3c, 0x76,
	0xd0, 0x38, 0xdb, 0x8e, 0xad, 0x3e, 0x1e, 0x68, 0xd4, 0x0b, 0xb9, 0x87, 0xbc, 0x77, 0x21, 0x4e,
	0xe6, 0x2e, 0xa4, 0x15, 0x57, 0xe6, 0x33, 0x3b, 0x69, 0x6c, 0x3f, 0xf9, 0x46, 0xcc, 0xd7, 0xfc,
	0xb2, 0xfb, 0xdb, 0x6c, 0x52, 0xeb, 0x06, 0xfe, 0x42, 0x2d, 0x0b, 0xaf, 0x2c, 0x86, 0x64, 0xd1,
	0x2f, 0x37, 0x5e, 0xa6, 0x43, 0x6e, 0xa5, 0xae, 0xdc, 0x51, 0xb0, 0xf2, 0xec, 0xea, 0x25, 0xce,
	0x74, 0x2e, 0x8b, 0x2c, 0xba, 0x99, 0x12, 0x45, 0xa9, 0xfe, 0x8c, 0xab, 0xff, 0x3d, 0xd5, 0x79,
	0x5c, 0x82, 0xf9, 0x51, 0x29, 0x94, 0xbb, 0x37, 0xe7, 0xfd, 0xa0, 0x2a, 0x5c, 0xff, 0x05, 0x00,
	0x00, 0xff, 0xff, 0x59, 0xbd, 0x5a, 0xcb, 0x5e, 0x03, 0x00, 0x00,
}
