// Code generated by protoc-gen-gogo.
// source: src/banusersvr/banuser.proto
// DO NOT EDIT!

/*
	Package banuser is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/banusersvr/banuser.proto

	It has these top-level messages:
		UpdateBanStatusReq
		UpdateBanStatusResp
		GetBannedStatusReq
		BannedStatus
		GetBannedStatusResp
		BatchGetUserBannedStatusReq
		BatchGetUserBannedStatusResp
		GetBannedHistoryReq
		BannedRecord
		GetBannedHistoryResp
		DeviceBannedStatus
		BatchGetDeviceBannedStatusReq
		BatchGetDeviceBannedStatusResp
		BannedAppealRecord
		GetBannedAppealRecordReq
		GetBannedAppealRecordResp
		SetBannedAppealRecordReq
		SetBannedAppealRecordResp
		UpdateBannedAppealRecordReq
		UpdateBannedAppealRecordResp
		GetBannedOperatorReq
		BannedOperator
		GetBannedOperatorResp
		UpdateBanedStatus
		BatchUpdateBanedStatusReq
		BatchUpdateBanedStatusResp
		BannedCacheData
*/
package banuser

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type BAN_OP_TYPE int32

const (
	// 封 操作
	BAN_OP_TYPE_BAN_OP_BAN_USER      BAN_OP_TYPE = 1
	BAN_OP_TYPE_BAN_OP_BAN_DEVICE    BAN_OP_TYPE = 2
	BAN_OP_TYPE_BAN_OP_BAN_CLIENT_IP BAN_OP_TYPE = 4
	BAN_OP_TYPE_BAN_OP_BAN_PHONE     BAN_OP_TYPE = 8
	// 解封 操作
	BAN_OP_TYPE_BAN_OP_UNBAN_USER      BAN_OP_TYPE = 256
	BAN_OP_TYPE_BAN_OP_UNBAN_DEVICE    BAN_OP_TYPE = 512
	BAN_OP_TYPE_BAN_OP_UNBAN_CLIENT_IP BAN_OP_TYPE = 1024
	BAN_OP_TYPE_BAN_OP_UNBAN_PHONE     BAN_OP_TYPE = 2048
)

var BAN_OP_TYPE_name = map[int32]string{
	1:    "BAN_OP_BAN_USER",
	2:    "BAN_OP_BAN_DEVICE",
	4:    "BAN_OP_BAN_CLIENT_IP",
	8:    "BAN_OP_BAN_PHONE",
	256:  "BAN_OP_UNBAN_USER",
	512:  "BAN_OP_UNBAN_DEVICE",
	1024: "BAN_OP_UNBAN_CLIENT_IP",
	2048: "BAN_OP_UNBAN_PHONE",
}
var BAN_OP_TYPE_value = map[string]int32{
	"BAN_OP_BAN_USER":        1,
	"BAN_OP_BAN_DEVICE":      2,
	"BAN_OP_BAN_CLIENT_IP":   4,
	"BAN_OP_BAN_PHONE":       8,
	"BAN_OP_UNBAN_USER":      256,
	"BAN_OP_UNBAN_DEVICE":    512,
	"BAN_OP_UNBAN_CLIENT_IP": 1024,
	"BAN_OP_UNBAN_PHONE":     2048,
}

func (x BAN_OP_TYPE) Enum() *BAN_OP_TYPE {
	p := new(BAN_OP_TYPE)
	*p = x
	return p
}
func (x BAN_OP_TYPE) String() string {
	return proto.EnumName(BAN_OP_TYPE_name, int32(x))
}
func (x *BAN_OP_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(BAN_OP_TYPE_value, data, "BAN_OP_TYPE")
	if err != nil {
		return err
	}
	*x = BAN_OP_TYPE(value)
	return nil
}
func (BAN_OP_TYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorBanuser, []int{0} }

type BANNED_TYPE int32

const (
	BANNED_TYPE_BAN_NOBANNED     BANNED_TYPE = 0
	BANNED_TYPE_BAN_BY_USER      BANNED_TYPE = 1
	BANNED_TYPE_BAN_BY_DEVICE    BANNED_TYPE = 2
	BANNED_TYPE_BAN_BY_CLIENT_IP BANNED_TYPE = 3
	BANNED_TYPE_BAN_BY_PHONE     BANNED_TYPE = 4
)

var BANNED_TYPE_name = map[int32]string{
	0: "BAN_NOBANNED",
	1: "BAN_BY_USER",
	2: "BAN_BY_DEVICE",
	3: "BAN_BY_CLIENT_IP",
	4: "BAN_BY_PHONE",
}
var BANNED_TYPE_value = map[string]int32{
	"BAN_NOBANNED":     0,
	"BAN_BY_USER":      1,
	"BAN_BY_DEVICE":    2,
	"BAN_BY_CLIENT_IP": 3,
	"BAN_BY_PHONE":     4,
}

func (x BANNED_TYPE) Enum() *BANNED_TYPE {
	p := new(BANNED_TYPE)
	*p = x
	return p
}
func (x BANNED_TYPE) String() string {
	return proto.EnumName(BANNED_TYPE_name, int32(x))
}
func (x *BANNED_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(BANNED_TYPE_value, data, "BANNED_TYPE")
	if err != nil {
		return err
	}
	*x = BANNED_TYPE(value)
	return nil
}
func (BANNED_TYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorBanuser, []int{1} }

type BAN_STATUS int32

const (
	BAN_STATUS_BAN_ST_NORMAL BAN_STATUS = 0
	BAN_STATUS_BAN_ST_BANNED BAN_STATUS = 1
)

var BAN_STATUS_name = map[int32]string{
	0: "BAN_ST_NORMAL",
	1: "BAN_ST_BANNED",
}
var BAN_STATUS_value = map[string]int32{
	"BAN_ST_NORMAL": 0,
	"BAN_ST_BANNED": 1,
}

func (x BAN_STATUS) Enum() *BAN_STATUS {
	p := new(BAN_STATUS)
	*p = x
	return p
}
func (x BAN_STATUS) String() string {
	return proto.EnumName(BAN_STATUS_name, int32(x))
}
func (x *BAN_STATUS) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(BAN_STATUS_value, data, "BAN_STATUS")
	if err != nil {
		return err
	}
	*x = BAN_STATUS(value)
	return nil
}
func (BAN_STATUS) EnumDescriptor() ([]byte, []int) { return fileDescriptorBanuser, []int{2} }

type REASON_CODE int32

const (
	REASON_CODE_REASON_CODE_NORMAL          REASON_CODE = 0
	REASON_CODE_REASON_CODE_BAN             REASON_CODE = 1
	REASON_CODE_REASON_CODE_ACCOUNT_RECYCLE REASON_CODE = 2
	REASON_CODE_REASON_CODE_ACCOUNT_BLACK   REASON_CODE = 4
)

var REASON_CODE_name = map[int32]string{
	0: "REASON_CODE_NORMAL",
	1: "REASON_CODE_BAN",
	2: "REASON_CODE_ACCOUNT_RECYCLE",
	4: "REASON_CODE_ACCOUNT_BLACK",
}
var REASON_CODE_value = map[string]int32{
	"REASON_CODE_NORMAL":          0,
	"REASON_CODE_BAN":             1,
	"REASON_CODE_ACCOUNT_RECYCLE": 2,
	"REASON_CODE_ACCOUNT_BLACK":   4,
}

func (x REASON_CODE) Enum() *REASON_CODE {
	p := new(REASON_CODE)
	*p = x
	return p
}
func (x REASON_CODE) String() string {
	return proto.EnumName(REASON_CODE_name, int32(x))
}
func (x *REASON_CODE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(REASON_CODE_value, data, "REASON_CODE")
	if err != nil {
		return err
	}
	*x = REASON_CODE(value)
	return nil
}
func (REASON_CODE) EnumDescriptor() ([]byte, []int) { return fileDescriptorBanuser, []int{3} }

// 封禁申诉
type BannedAppealState int32

const (
	BannedAppealState_BANNED_APPEAL_STATE_NONE  BannedAppealState = 0
	BannedAppealState_BANNED_APPEAL_STATE_PEND  BannedAppealState = 1
	BannedAppealState_BANNED_APPEAL_STATE_PASS  BannedAppealState = 2
	BannedAppealState_BANNED_APPEAL_STATE_DENY  BannedAppealState = 4
	BannedAppealState_BANNED_APPEAL_STATE_BLACK BannedAppealState = 5
)

var BannedAppealState_name = map[int32]string{
	0: "BANNED_APPEAL_STATE_NONE",
	1: "BANNED_APPEAL_STATE_PEND",
	2: "BANNED_APPEAL_STATE_PASS",
	4: "BANNED_APPEAL_STATE_DENY",
	5: "BANNED_APPEAL_STATE_BLACK",
}
var BannedAppealState_value = map[string]int32{
	"BANNED_APPEAL_STATE_NONE":  0,
	"BANNED_APPEAL_STATE_PEND":  1,
	"BANNED_APPEAL_STATE_PASS":  2,
	"BANNED_APPEAL_STATE_DENY":  4,
	"BANNED_APPEAL_STATE_BLACK": 5,
}

func (x BannedAppealState) Enum() *BannedAppealState {
	p := new(BannedAppealState)
	*p = x
	return p
}
func (x BannedAppealState) String() string {
	return proto.EnumName(BannedAppealState_name, int32(x))
}
func (x *BannedAppealState) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(BannedAppealState_value, data, "BannedAppealState")
	if err != nil {
		return err
	}
	*x = BannedAppealState(value)
	return nil
}
func (BannedAppealState) EnumDescriptor() ([]byte, []int) { return fileDescriptorBanuser, []int{4} }

type UpdateBanStatusReq struct {
	OpType       uint32 `protobuf:"varint,1,req,name=op_type,json=opType" json:"op_type"`
	Uid          uint32 `protobuf:"varint,2,opt,name=uid" json:"uid"`
	DeviceId     string `protobuf:"bytes,3,opt,name=device_id,json=deviceId" json:"device_id"`
	ClientIp     string `protobuf:"bytes,4,opt,name=client_ip,json=clientIp" json:"client_ip"`
	At           uint32 `protobuf:"varint,5,opt,name=at" json:"at"`
	RecoveryAt   uint32 `protobuf:"varint,6,opt,name=recovery_at,json=recoveryAt" json:"recovery_at"`
	Reason       string `protobuf:"bytes,7,opt,name=reason" json:"reason"`
	OperatorId   string `protobuf:"bytes,8,opt,name=operator_id,json=operatorId" json:"operator_id"`
	ProofPic     string `protobuf:"bytes,9,opt,name=proof_pic,json=proofPic" json:"proof_pic"`
	ExtInfo      string `protobuf:"bytes,10,opt,name=ext_info,json=extInfo" json:"ext_info"`
	NoLog        bool   `protobuf:"varint,11,opt,name=no_log,json=noLog" json:"no_log"`
	ReasonDetail string `protobuf:"bytes,12,opt,name=reason_detail,json=reasonDetail" json:"reason_detail"`
}

func (m *UpdateBanStatusReq) Reset()                    { *m = UpdateBanStatusReq{} }
func (m *UpdateBanStatusReq) String() string            { return proto.CompactTextString(m) }
func (*UpdateBanStatusReq) ProtoMessage()               {}
func (*UpdateBanStatusReq) Descriptor() ([]byte, []int) { return fileDescriptorBanuser, []int{0} }

func (m *UpdateBanStatusReq) GetOpType() uint32 {
	if m != nil {
		return m.OpType
	}
	return 0
}

func (m *UpdateBanStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateBanStatusReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *UpdateBanStatusReq) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

func (m *UpdateBanStatusReq) GetAt() uint32 {
	if m != nil {
		return m.At
	}
	return 0
}

func (m *UpdateBanStatusReq) GetRecoveryAt() uint32 {
	if m != nil {
		return m.RecoveryAt
	}
	return 0
}

func (m *UpdateBanStatusReq) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *UpdateBanStatusReq) GetOperatorId() string {
	if m != nil {
		return m.OperatorId
	}
	return ""
}

func (m *UpdateBanStatusReq) GetProofPic() string {
	if m != nil {
		return m.ProofPic
	}
	return ""
}

func (m *UpdateBanStatusReq) GetExtInfo() string {
	if m != nil {
		return m.ExtInfo
	}
	return ""
}

func (m *UpdateBanStatusReq) GetNoLog() bool {
	if m != nil {
		return m.NoLog
	}
	return false
}

func (m *UpdateBanStatusReq) GetReasonDetail() string {
	if m != nil {
		return m.ReasonDetail
	}
	return ""
}

type UpdateBanStatusResp struct {
}

func (m *UpdateBanStatusResp) Reset()                    { *m = UpdateBanStatusResp{} }
func (m *UpdateBanStatusResp) String() string            { return proto.CompactTextString(m) }
func (*UpdateBanStatusResp) ProtoMessage()               {}
func (*UpdateBanStatusResp) Descriptor() ([]byte, []int) { return fileDescriptorBanuser, []int{1} }

type GetBannedStatusReq struct {
	Uid      uint32 `protobuf:"varint,1,opt,name=uid" json:"uid"`
	DeviceId string `protobuf:"bytes,2,opt,name=device_id,json=deviceId" json:"device_id"`
	ClientIp string `protobuf:"bytes,3,opt,name=client_ip,json=clientIp" json:"client_ip"`
	Phone    string `protobuf:"bytes,4,opt,name=phone" json:"phone"`
}

func (m *GetBannedStatusReq) Reset()                    { *m = GetBannedStatusReq{} }
func (m *GetBannedStatusReq) String() string            { return proto.CompactTextString(m) }
func (*GetBannedStatusReq) ProtoMessage()               {}
func (*GetBannedStatusReq) Descriptor() ([]byte, []int) { return fileDescriptorBanuser, []int{2} }

func (m *GetBannedStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetBannedStatusReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *GetBannedStatusReq) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

func (m *GetBannedStatusReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

type BannedStatus struct {
	Status       uint32 `protobuf:"varint,1,req,name=status" json:"status"`
	BannedType   uint32 `protobuf:"varint,2,opt,name=banned_type,json=bannedType" json:"banned_type"`
	Uid          uint32 `protobuf:"varint,3,opt,name=uid" json:"uid"`
	BannedAt     uint32 `protobuf:"varint,4,opt,name=banned_at,json=bannedAt" json:"banned_at"`
	RecoveryAt   uint32 `protobuf:"varint,5,opt,name=recovery_at,json=recoveryAt" json:"recovery_at"`
	Reason       string `protobuf:"bytes,6,opt,name=reason" json:"reason"`
	ReasonCode   uint32 `protobuf:"varint,7,opt,name=reason_code,json=reasonCode" json:"reason_code"`
	ReasonDetail string `protobuf:"bytes,8,opt,name=reason_detail,json=reasonDetail" json:"reason_detail"`
}

func (m *BannedStatus) Reset()                    { *m = BannedStatus{} }
func (m *BannedStatus) String() string            { return proto.CompactTextString(m) }
func (*BannedStatus) ProtoMessage()               {}
func (*BannedStatus) Descriptor() ([]byte, []int) { return fileDescriptorBanuser, []int{3} }

func (m *BannedStatus) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *BannedStatus) GetBannedType() uint32 {
	if m != nil {
		return m.BannedType
	}
	return 0
}

func (m *BannedStatus) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BannedStatus) GetBannedAt() uint32 {
	if m != nil {
		return m.BannedAt
	}
	return 0
}

func (m *BannedStatus) GetRecoveryAt() uint32 {
	if m != nil {
		return m.RecoveryAt
	}
	return 0
}

func (m *BannedStatus) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *BannedStatus) GetReasonCode() uint32 {
	if m != nil {
		return m.ReasonCode
	}
	return 0
}

func (m *BannedStatus) GetReasonDetail() string {
	if m != nil {
		return m.ReasonDetail
	}
	return ""
}

type GetBannedStatusResp struct {
	BannedStatus *BannedStatus `protobuf:"bytes,1,req,name=banned_status,json=bannedStatus" json:"banned_status,omitempty"`
}

func (m *GetBannedStatusResp) Reset()                    { *m = GetBannedStatusResp{} }
func (m *GetBannedStatusResp) String() string            { return proto.CompactTextString(m) }
func (*GetBannedStatusResp) ProtoMessage()               {}
func (*GetBannedStatusResp) Descriptor() ([]byte, []int) { return fileDescriptorBanuser, []int{4} }

func (m *GetBannedStatusResp) GetBannedStatus() *BannedStatus {
	if m != nil {
		return m.BannedStatus
	}
	return nil
}

type BatchGetUserBannedStatusReq struct {
	UidList []uint32 `protobuf:"varint,1,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *BatchGetUserBannedStatusReq) Reset()         { *m = BatchGetUserBannedStatusReq{} }
func (m *BatchGetUserBannedStatusReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserBannedStatusReq) ProtoMessage()    {}
func (*BatchGetUserBannedStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptorBanuser, []int{5}
}

func (m *BatchGetUserBannedStatusReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchGetUserBannedStatusResp struct {
	StatusList []*BannedStatus `protobuf:"bytes,1,rep,name=status_list,json=statusList" json:"status_list,omitempty"`
}

func (m *BatchGetUserBannedStatusResp) Reset()         { *m = BatchGetUserBannedStatusResp{} }
func (m *BatchGetUserBannedStatusResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserBannedStatusResp) ProtoMessage()    {}
func (*BatchGetUserBannedStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptorBanuser, []int{6}
}

func (m *BatchGetUserBannedStatusResp) GetStatusList() []*BannedStatus {
	if m != nil {
		return m.StatusList
	}
	return nil
}

type GetBannedHistoryReq struct {
	Uid        uint32 `protobuf:"varint,1,opt,name=uid" json:"uid"`
	DeviceId   string `protobuf:"bytes,2,opt,name=device_id,json=deviceId" json:"device_id"`
	ClientIp   string `protobuf:"bytes,3,opt,name=client_ip,json=clientIp" json:"client_ip"`
	OpType     uint32 `protobuf:"varint,4,opt,name=op_type,json=opType" json:"op_type"`
	Offset     uint32 `protobuf:"varint,5,opt,name=offset" json:"offset"`
	Limit      uint32 `protobuf:"varint,6,opt,name=limit" json:"limit"`
	BeginTime  uint32 `protobuf:"varint,7,opt,name=begin_time,json=beginTime" json:"begin_time"`
	EndTime    uint32 `protobuf:"varint,8,opt,name=end_time,json=endTime" json:"end_time"`
	OperatorId string `protobuf:"bytes,9,opt,name=operator_id,json=operatorId" json:"operator_id"`
	Reason     string `protobuf:"bytes,10,opt,name=reason" json:"reason"`
}

func (m *GetBannedHistoryReq) Reset()                    { *m = GetBannedHistoryReq{} }
func (m *GetBannedHistoryReq) String() string            { return proto.CompactTextString(m) }
func (*GetBannedHistoryReq) ProtoMessage()               {}
func (*GetBannedHistoryReq) Descriptor() ([]byte, []int) { return fileDescriptorBanuser, []int{7} }

func (m *GetBannedHistoryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetBannedHistoryReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *GetBannedHistoryReq) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

func (m *GetBannedHistoryReq) GetOpType() uint32 {
	if m != nil {
		return m.OpType
	}
	return 0
}

func (m *GetBannedHistoryReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetBannedHistoryReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetBannedHistoryReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetBannedHistoryReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetBannedHistoryReq) GetOperatorId() string {
	if m != nil {
		return m.OperatorId
	}
	return ""
}

func (m *GetBannedHistoryReq) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

type BannedRecord struct {
	Op           uint32 `protobuf:"varint,1,req,name=op" json:"op"`
	At           uint32 `protobuf:"varint,2,req,name=at" json:"at"`
	RecoveryAt   uint32 `protobuf:"varint,3,req,name=recovery_at,json=recoveryAt" json:"recovery_at"`
	Reason       string `protobuf:"bytes,4,opt,name=reason" json:"reason"`
	OperatorId   string `protobuf:"bytes,5,opt,name=operator_id,json=operatorId" json:"operator_id"`
	Uid          uint32 `protobuf:"varint,6,opt,name=uid" json:"uid"`
	DeviceId     string `protobuf:"bytes,7,opt,name=device_id,json=deviceId" json:"device_id"`
	ClientIp     string `protobuf:"bytes,8,opt,name=client_ip,json=clientIp" json:"client_ip"`
	ProofPic     string `protobuf:"bytes,9,opt,name=proof_pic,json=proofPic" json:"proof_pic"`
	ExtInfo      string `protobuf:"bytes,10,opt,name=ext_info,json=extInfo" json:"ext_info"`
	ReasonDetail string `protobuf:"bytes,11,opt,name=reason_detail,json=reasonDetail" json:"reason_detail"`
}

func (m *BannedRecord) Reset()                    { *m = BannedRecord{} }
func (m *BannedRecord) String() string            { return proto.CompactTextString(m) }
func (*BannedRecord) ProtoMessage()               {}
func (*BannedRecord) Descriptor() ([]byte, []int) { return fileDescriptorBanuser, []int{8} }

func (m *BannedRecord) GetOp() uint32 {
	if m != nil {
		return m.Op
	}
	return 0
}

func (m *BannedRecord) GetAt() uint32 {
	if m != nil {
		return m.At
	}
	return 0
}

func (m *BannedRecord) GetRecoveryAt() uint32 {
	if m != nil {
		return m.RecoveryAt
	}
	return 0
}

func (m *BannedRecord) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *BannedRecord) GetOperatorId() string {
	if m != nil {
		return m.OperatorId
	}
	return ""
}

func (m *BannedRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BannedRecord) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *BannedRecord) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

func (m *BannedRecord) GetProofPic() string {
	if m != nil {
		return m.ProofPic
	}
	return ""
}

func (m *BannedRecord) GetExtInfo() string {
	if m != nil {
		return m.ExtInfo
	}
	return ""
}

func (m *BannedRecord) GetReasonDetail() string {
	if m != nil {
		return m.ReasonDetail
	}
	return ""
}

type GetBannedHistoryResp struct {
	Records []*BannedRecord `protobuf:"bytes,1,rep,name=records" json:"records,omitempty"`
	Total   uint32          `protobuf:"varint,2,req,name=total" json:"total"`
}

func (m *GetBannedHistoryResp) Reset()                    { *m = GetBannedHistoryResp{} }
func (m *GetBannedHistoryResp) String() string            { return proto.CompactTextString(m) }
func (*GetBannedHistoryResp) ProtoMessage()               {}
func (*GetBannedHistoryResp) Descriptor() ([]byte, []int) { return fileDescriptorBanuser, []int{9} }

func (m *GetBannedHistoryResp) GetRecords() []*BannedRecord {
	if m != nil {
		return m.Records
	}
	return nil
}

func (m *GetBannedHistoryResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type DeviceBannedStatus struct {
	Status       uint32 `protobuf:"varint,1,req,name=status" json:"status"`
	BannedType   uint32 `protobuf:"varint,2,opt,name=banned_type,json=bannedType" json:"banned_type"`
	DeviceId     string `protobuf:"bytes,3,opt,name=device_id,json=deviceId" json:"device_id"`
	BannedAt     uint32 `protobuf:"varint,4,opt,name=banned_at,json=bannedAt" json:"banned_at"`
	RecoveryAt   uint32 `protobuf:"varint,5,opt,name=recovery_at,json=recoveryAt" json:"recovery_at"`
	Reason       string `protobuf:"bytes,6,opt,name=reason" json:"reason"`
	ReasonDetail string `protobuf:"bytes,7,opt,name=reason_detail,json=reasonDetail" json:"reason_detail"`
}

func (m *DeviceBannedStatus) Reset()                    { *m = DeviceBannedStatus{} }
func (m *DeviceBannedStatus) String() string            { return proto.CompactTextString(m) }
func (*DeviceBannedStatus) ProtoMessage()               {}
func (*DeviceBannedStatus) Descriptor() ([]byte, []int) { return fileDescriptorBanuser, []int{10} }

func (m *DeviceBannedStatus) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *DeviceBannedStatus) GetBannedType() uint32 {
	if m != nil {
		return m.BannedType
	}
	return 0
}

func (m *DeviceBannedStatus) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *DeviceBannedStatus) GetBannedAt() uint32 {
	if m != nil {
		return m.BannedAt
	}
	return 0
}

func (m *DeviceBannedStatus) GetRecoveryAt() uint32 {
	if m != nil {
		return m.RecoveryAt
	}
	return 0
}

func (m *DeviceBannedStatus) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *DeviceBannedStatus) GetReasonDetail() string {
	if m != nil {
		return m.ReasonDetail
	}
	return ""
}

type BatchGetDeviceBannedStatusReq struct {
	DeviceIds []string `protobuf:"bytes,1,rep,name=device_ids,json=deviceIds" json:"device_ids,omitempty"`
}

func (m *BatchGetDeviceBannedStatusReq) Reset()         { *m = BatchGetDeviceBannedStatusReq{} }
func (m *BatchGetDeviceBannedStatusReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetDeviceBannedStatusReq) ProtoMessage()    {}
func (*BatchGetDeviceBannedStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptorBanuser, []int{11}
}

func (m *BatchGetDeviceBannedStatusReq) GetDeviceIds() []string {
	if m != nil {
		return m.DeviceIds
	}
	return nil
}

type BatchGetDeviceBannedStatusResp struct {
	StatusList []*DeviceBannedStatus `protobuf:"bytes,1,rep,name=status_list,json=statusList" json:"status_list,omitempty"`
}

func (m *BatchGetDeviceBannedStatusResp) Reset()         { *m = BatchGetDeviceBannedStatusResp{} }
func (m *BatchGetDeviceBannedStatusResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetDeviceBannedStatusResp) ProtoMessage()    {}
func (*BatchGetDeviceBannedStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptorBanuser, []int{12}
}

func (m *BatchGetDeviceBannedStatusResp) GetStatusList() []*DeviceBannedStatus {
	if m != nil {
		return m.StatusList
	}
	return nil
}

type BannedAppealRecord struct {
	Id            uint32 `protobuf:"varint,1,opt,name=id" json:"id"`
	Uid           uint32 `protobuf:"varint,2,opt,name=uid" json:"uid"`
	State         uint32 `protobuf:"varint,3,opt,name=state" json:"state"`
	ReasonCode    uint32 `protobuf:"varint,4,opt,name=reason_code,json=reasonCode" json:"reason_code"`
	BannedBeginAt int64  `protobuf:"varint,5,opt,name=banned_begin_at,json=bannedBeginAt" json:"banned_begin_at"`
	BannedEndAt   int64  `protobuf:"varint,6,opt,name=banned_end_at,json=bannedEndAt" json:"banned_end_at"`
	CreateAt      int64  `protobuf:"varint,7,opt,name=create_at,json=createAt" json:"create_at"`
	UpdateAt      int64  `protobuf:"varint,8,opt,name=update_at,json=updateAt" json:"update_at"`
	CreateDesc    string `protobuf:"bytes,9,opt,name=create_desc,json=createDesc" json:"create_desc"`
	UpdateDesc    string `protobuf:"bytes,10,opt,name=update_desc,json=updateDesc" json:"update_desc"`
	Operator      string `protobuf:"bytes,11,opt,name=operator" json:"operator"`
	MarketId      uint32 `protobuf:"varint,12,opt,name=market_id,json=marketId" json:"market_id"`
}

func (m *BannedAppealRecord) Reset()                    { *m = BannedAppealRecord{} }
func (m *BannedAppealRecord) String() string            { return proto.CompactTextString(m) }
func (*BannedAppealRecord) ProtoMessage()               {}
func (*BannedAppealRecord) Descriptor() ([]byte, []int) { return fileDescriptorBanuser, []int{13} }

func (m *BannedAppealRecord) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *BannedAppealRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BannedAppealRecord) GetState() uint32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *BannedAppealRecord) GetReasonCode() uint32 {
	if m != nil {
		return m.ReasonCode
	}
	return 0
}

func (m *BannedAppealRecord) GetBannedBeginAt() int64 {
	if m != nil {
		return m.BannedBeginAt
	}
	return 0
}

func (m *BannedAppealRecord) GetBannedEndAt() int64 {
	if m != nil {
		return m.BannedEndAt
	}
	return 0
}

func (m *BannedAppealRecord) GetCreateAt() int64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *BannedAppealRecord) GetUpdateAt() int64 {
	if m != nil {
		return m.UpdateAt
	}
	return 0
}

func (m *BannedAppealRecord) GetCreateDesc() string {
	if m != nil {
		return m.CreateDesc
	}
	return ""
}

func (m *BannedAppealRecord) GetUpdateDesc() string {
	if m != nil {
		return m.UpdateDesc
	}
	return ""
}

func (m *BannedAppealRecord) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *BannedAppealRecord) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

type GetBannedAppealRecordReq struct {
	UidList        []uint32 `protobuf:"varint,1,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
	StateList      []uint32 `protobuf:"varint,2,rep,name=state_list,json=stateList" json:"state_list,omitempty"`
	ReasonCodeList []uint32 `protobuf:"varint,3,rep,name=reason_code_list,json=reasonCodeList" json:"reason_code_list,omitempty"`
	CreateBeginAt  int64    `protobuf:"varint,4,opt,name=create_begin_at,json=createBeginAt" json:"create_begin_at"`
	CreateEndAt    int64    `protobuf:"varint,5,opt,name=create_end_at,json=createEndAt" json:"create_end_at"`
	Offset         uint32   `protobuf:"varint,6,opt,name=offset" json:"offset"`
	Limit          uint32   `protobuf:"varint,7,opt,name=limit" json:"limit"`
	RespTotal      bool     `protobuf:"varint,8,opt,name=resp_total,json=respTotal" json:"resp_total"`
}

func (m *GetBannedAppealRecordReq) Reset()                    { *m = GetBannedAppealRecordReq{} }
func (m *GetBannedAppealRecordReq) String() string            { return proto.CompactTextString(m) }
func (*GetBannedAppealRecordReq) ProtoMessage()               {}
func (*GetBannedAppealRecordReq) Descriptor() ([]byte, []int) { return fileDescriptorBanuser, []int{14} }

func (m *GetBannedAppealRecordReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetBannedAppealRecordReq) GetStateList() []uint32 {
	if m != nil {
		return m.StateList
	}
	return nil
}

func (m *GetBannedAppealRecordReq) GetReasonCodeList() []uint32 {
	if m != nil {
		return m.ReasonCodeList
	}
	return nil
}

func (m *GetBannedAppealRecordReq) GetCreateBeginAt() int64 {
	if m != nil {
		return m.CreateBeginAt
	}
	return 0
}

func (m *GetBannedAppealRecordReq) GetCreateEndAt() int64 {
	if m != nil {
		return m.CreateEndAt
	}
	return 0
}

func (m *GetBannedAppealRecordReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetBannedAppealRecordReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetBannedAppealRecordReq) GetRespTotal() bool {
	if m != nil {
		return m.RespTotal
	}
	return false
}

type GetBannedAppealRecordResp struct {
	RecordList []*BannedAppealRecord `protobuf:"bytes,1,rep,name=record_list,json=recordList" json:"record_list,omitempty"`
	Total      uint32                `protobuf:"varint,2,opt,name=total" json:"total"`
}

func (m *GetBannedAppealRecordResp) Reset()         { *m = GetBannedAppealRecordResp{} }
func (m *GetBannedAppealRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetBannedAppealRecordResp) ProtoMessage()    {}
func (*GetBannedAppealRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptorBanuser, []int{15}
}

func (m *GetBannedAppealRecordResp) GetRecordList() []*BannedAppealRecord {
	if m != nil {
		return m.RecordList
	}
	return nil
}

func (m *GetBannedAppealRecordResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type SetBannedAppealRecordReq struct {
	RecordList []*BannedAppealRecord `protobuf:"bytes,1,rep,name=record_list,json=recordList" json:"record_list,omitempty"`
}

func (m *SetBannedAppealRecordReq) Reset()                    { *m = SetBannedAppealRecordReq{} }
func (m *SetBannedAppealRecordReq) String() string            { return proto.CompactTextString(m) }
func (*SetBannedAppealRecordReq) ProtoMessage()               {}
func (*SetBannedAppealRecordReq) Descriptor() ([]byte, []int) { return fileDescriptorBanuser, []int{16} }

func (m *SetBannedAppealRecordReq) GetRecordList() []*BannedAppealRecord {
	if m != nil {
		return m.RecordList
	}
	return nil
}

type SetBannedAppealRecordResp struct {
}

func (m *SetBannedAppealRecordResp) Reset()         { *m = SetBannedAppealRecordResp{} }
func (m *SetBannedAppealRecordResp) String() string { return proto.CompactTextString(m) }
func (*SetBannedAppealRecordResp) ProtoMessage()    {}
func (*SetBannedAppealRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptorBanuser, []int{17}
}

type UpdateBannedAppealRecordReq struct {
	Id         uint32 `protobuf:"varint,1,opt,name=id" json:"id"`
	Uid        uint32 `protobuf:"varint,2,opt,name=uid" json:"uid"`
	State      uint32 `protobuf:"varint,3,opt,name=state" json:"state"`
	UpdateDesc string `protobuf:"bytes,4,opt,name=update_desc,json=updateDesc" json:"update_desc"`
	Operator   string `protobuf:"bytes,5,opt,name=operator" json:"operator"`
}

func (m *UpdateBannedAppealRecordReq) Reset()         { *m = UpdateBannedAppealRecordReq{} }
func (m *UpdateBannedAppealRecordReq) String() string { return proto.CompactTextString(m) }
func (*UpdateBannedAppealRecordReq) ProtoMessage()    {}
func (*UpdateBannedAppealRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptorBanuser, []int{18}
}

func (m *UpdateBannedAppealRecordReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UpdateBannedAppealRecordReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateBannedAppealRecordReq) GetState() uint32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *UpdateBannedAppealRecordReq) GetUpdateDesc() string {
	if m != nil {
		return m.UpdateDesc
	}
	return ""
}

func (m *UpdateBannedAppealRecordReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type UpdateBannedAppealRecordResp struct {
	Updated bool `protobuf:"varint,1,opt,name=updated" json:"updated"`
}

func (m *UpdateBannedAppealRecordResp) Reset()         { *m = UpdateBannedAppealRecordResp{} }
func (m *UpdateBannedAppealRecordResp) String() string { return proto.CompactTextString(m) }
func (*UpdateBannedAppealRecordResp) ProtoMessage()    {}
func (*UpdateBannedAppealRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptorBanuser, []int{19}
}

func (m *UpdateBannedAppealRecordResp) GetUpdated() bool {
	if m != nil {
		return m.Updated
	}
	return false
}

// 获取封禁操作人
type GetBannedOperatorReq struct {
	OperatorName string `protobuf:"bytes,1,opt,name=operator_name,json=operatorName" json:"operator_name"`
	Limit        uint32 `protobuf:"varint,2,opt,name=limit" json:"limit"`
}

func (m *GetBannedOperatorReq) Reset()                    { *m = GetBannedOperatorReq{} }
func (m *GetBannedOperatorReq) String() string            { return proto.CompactTextString(m) }
func (*GetBannedOperatorReq) ProtoMessage()               {}
func (*GetBannedOperatorReq) Descriptor() ([]byte, []int) { return fileDescriptorBanuser, []int{20} }

func (m *GetBannedOperatorReq) GetOperatorName() string {
	if m != nil {
		return m.OperatorName
	}
	return ""
}

func (m *GetBannedOperatorReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type BannedOperator struct {
	OperatorName string `protobuf:"bytes,1,opt,name=operator_name,json=operatorName" json:"operator_name"`
	LastOpAt     uint32 `protobuf:"varint,2,opt,name=last_op_at,json=lastOpAt" json:"last_op_at"`
	LastOpType   uint32 `protobuf:"varint,3,opt,name=last_op_type,json=lastOpType" json:"last_op_type"`
}

func (m *BannedOperator) Reset()                    { *m = BannedOperator{} }
func (m *BannedOperator) String() string            { return proto.CompactTextString(m) }
func (*BannedOperator) ProtoMessage()               {}
func (*BannedOperator) Descriptor() ([]byte, []int) { return fileDescriptorBanuser, []int{21} }

func (m *BannedOperator) GetOperatorName() string {
	if m != nil {
		return m.OperatorName
	}
	return ""
}

func (m *BannedOperator) GetLastOpAt() uint32 {
	if m != nil {
		return m.LastOpAt
	}
	return 0
}

func (m *BannedOperator) GetLastOpType() uint32 {
	if m != nil {
		return m.LastOpType
	}
	return 0
}

type GetBannedOperatorResp struct {
	OperatorList []*BannedOperator `protobuf:"bytes,1,rep,name=operator_list,json=operatorList" json:"operator_list,omitempty"`
}

func (m *GetBannedOperatorResp) Reset()                    { *m = GetBannedOperatorResp{} }
func (m *GetBannedOperatorResp) String() string            { return proto.CompactTextString(m) }
func (*GetBannedOperatorResp) ProtoMessage()               {}
func (*GetBannedOperatorResp) Descriptor() ([]byte, []int) { return fileDescriptorBanuser, []int{22} }

func (m *GetBannedOperatorResp) GetOperatorList() []*BannedOperator {
	if m != nil {
		return m.OperatorList
	}
	return nil
}

type UpdateBanedStatus struct {
	OpType       uint32 `protobuf:"varint,1,req,name=op_type,json=opType" json:"op_type"`
	Uid          uint32 `protobuf:"varint,2,opt,name=uid" json:"uid"`
	DeviceId     string `protobuf:"bytes,3,opt,name=device_id,json=deviceId" json:"device_id"`
	ClientIp     string `protobuf:"bytes,4,opt,name=client_ip,json=clientIp" json:"client_ip"`
	At           uint32 `protobuf:"varint,5,opt,name=at" json:"at"`
	RecoveryAt   uint32 `protobuf:"varint,6,opt,name=recovery_at,json=recoveryAt" json:"recovery_at"`
	Reason       string `protobuf:"bytes,7,opt,name=reason" json:"reason"`
	OperatorId   string `protobuf:"bytes,8,opt,name=operator_id,json=operatorId" json:"operator_id"`
	ProofPic     string `protobuf:"bytes,9,opt,name=proof_pic,json=proofPic" json:"proof_pic"`
	ExtInfo      string `protobuf:"bytes,10,opt,name=ext_info,json=extInfo" json:"ext_info"`
	NoLog        bool   `protobuf:"varint,11,opt,name=no_log,json=noLog" json:"no_log"`
	ReasonDetail string `protobuf:"bytes,12,opt,name=reason_detail,json=reasonDetail" json:"reason_detail"`
}

func (m *UpdateBanedStatus) Reset()                    { *m = UpdateBanedStatus{} }
func (m *UpdateBanedStatus) String() string            { return proto.CompactTextString(m) }
func (*UpdateBanedStatus) ProtoMessage()               {}
func (*UpdateBanedStatus) Descriptor() ([]byte, []int) { return fileDescriptorBanuser, []int{23} }

func (m *UpdateBanedStatus) GetOpType() uint32 {
	if m != nil {
		return m.OpType
	}
	return 0
}

func (m *UpdateBanedStatus) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateBanedStatus) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *UpdateBanedStatus) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

func (m *UpdateBanedStatus) GetAt() uint32 {
	if m != nil {
		return m.At
	}
	return 0
}

func (m *UpdateBanedStatus) GetRecoveryAt() uint32 {
	if m != nil {
		return m.RecoveryAt
	}
	return 0
}

func (m *UpdateBanedStatus) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *UpdateBanedStatus) GetOperatorId() string {
	if m != nil {
		return m.OperatorId
	}
	return ""
}

func (m *UpdateBanedStatus) GetProofPic() string {
	if m != nil {
		return m.ProofPic
	}
	return ""
}

func (m *UpdateBanedStatus) GetExtInfo() string {
	if m != nil {
		return m.ExtInfo
	}
	return ""
}

func (m *UpdateBanedStatus) GetNoLog() bool {
	if m != nil {
		return m.NoLog
	}
	return false
}

func (m *UpdateBanedStatus) GetReasonDetail() string {
	if m != nil {
		return m.ReasonDetail
	}
	return ""
}

type BatchUpdateBanedStatusReq struct {
	List []*UpdateBanedStatus `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
}

func (m *BatchUpdateBanedStatusReq) Reset()         { *m = BatchUpdateBanedStatusReq{} }
func (m *BatchUpdateBanedStatusReq) String() string { return proto.CompactTextString(m) }
func (*BatchUpdateBanedStatusReq) ProtoMessage()    {}
func (*BatchUpdateBanedStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptorBanuser, []int{24}
}

func (m *BatchUpdateBanedStatusReq) GetList() []*UpdateBanedStatus {
	if m != nil {
		return m.List
	}
	return nil
}

type BatchUpdateBanedStatusResp struct {
	SuccessSize int32 `protobuf:"varint,1,opt,name=success_size,json=successSize" json:"success_size"`
}

func (m *BatchUpdateBanedStatusResp) Reset()         { *m = BatchUpdateBanedStatusResp{} }
func (m *BatchUpdateBanedStatusResp) String() string { return proto.CompactTextString(m) }
func (*BatchUpdateBanedStatusResp) ProtoMessage()    {}
func (*BatchUpdateBanedStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptorBanuser, []int{25}
}

func (m *BatchUpdateBanedStatusResp) GetSuccessSize() int32 {
	if m != nil {
		return m.SuccessSize
	}
	return 0
}

// cache 对应 ban_log 表
type BannedCacheData struct {
	Status       uint32 `protobuf:"varint,1,req,name=status" json:"status"`
	BannedAt     uint32 `protobuf:"varint,2,opt,name=banned_at,json=bannedAt" json:"banned_at"`
	RecoveryAt   uint32 `protobuf:"varint,3,opt,name=recovery_at,json=recoveryAt" json:"recovery_at"`
	Reason       string `protobuf:"bytes,4,opt,name=reason" json:"reason"`
	RefUid       uint32 `protobuf:"varint,5,opt,name=ref_uid,json=refUid" json:"ref_uid"`
	RefDevice    string `protobuf:"bytes,6,opt,name=ref_device,json=refDevice" json:"ref_device"`
	RefPhone     string `protobuf:"bytes,7,opt,name=ref_phone,json=refPhone" json:"ref_phone"`
	ReasonCode   uint32 `protobuf:"varint,8,opt,name=reason_code,json=reasonCode" json:"reason_code"`
	ReasonDetail string `protobuf:"bytes,9,opt,name=reason_detail,json=reasonDetail" json:"reason_detail"`
}

func (m *BannedCacheData) Reset()                    { *m = BannedCacheData{} }
func (m *BannedCacheData) String() string            { return proto.CompactTextString(m) }
func (*BannedCacheData) ProtoMessage()               {}
func (*BannedCacheData) Descriptor() ([]byte, []int) { return fileDescriptorBanuser, []int{26} }

func (m *BannedCacheData) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *BannedCacheData) GetBannedAt() uint32 {
	if m != nil {
		return m.BannedAt
	}
	return 0
}

func (m *BannedCacheData) GetRecoveryAt() uint32 {
	if m != nil {
		return m.RecoveryAt
	}
	return 0
}

func (m *BannedCacheData) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *BannedCacheData) GetRefUid() uint32 {
	if m != nil {
		return m.RefUid
	}
	return 0
}

func (m *BannedCacheData) GetRefDevice() string {
	if m != nil {
		return m.RefDevice
	}
	return ""
}

func (m *BannedCacheData) GetRefPhone() string {
	if m != nil {
		return m.RefPhone
	}
	return ""
}

func (m *BannedCacheData) GetReasonCode() uint32 {
	if m != nil {
		return m.ReasonCode
	}
	return 0
}

func (m *BannedCacheData) GetReasonDetail() string {
	if m != nil {
		return m.ReasonDetail
	}
	return ""
}

func init() {
	proto.RegisterType((*UpdateBanStatusReq)(nil), "banuser.UpdateBanStatusReq")
	proto.RegisterType((*UpdateBanStatusResp)(nil), "banuser.UpdateBanStatusResp")
	proto.RegisterType((*GetBannedStatusReq)(nil), "banuser.GetBannedStatusReq")
	proto.RegisterType((*BannedStatus)(nil), "banuser.BannedStatus")
	proto.RegisterType((*GetBannedStatusResp)(nil), "banuser.GetBannedStatusResp")
	proto.RegisterType((*BatchGetUserBannedStatusReq)(nil), "banuser.BatchGetUserBannedStatusReq")
	proto.RegisterType((*BatchGetUserBannedStatusResp)(nil), "banuser.BatchGetUserBannedStatusResp")
	proto.RegisterType((*GetBannedHistoryReq)(nil), "banuser.GetBannedHistoryReq")
	proto.RegisterType((*BannedRecord)(nil), "banuser.BannedRecord")
	proto.RegisterType((*GetBannedHistoryResp)(nil), "banuser.GetBannedHistoryResp")
	proto.RegisterType((*DeviceBannedStatus)(nil), "banuser.DeviceBannedStatus")
	proto.RegisterType((*BatchGetDeviceBannedStatusReq)(nil), "banuser.BatchGetDeviceBannedStatusReq")
	proto.RegisterType((*BatchGetDeviceBannedStatusResp)(nil), "banuser.BatchGetDeviceBannedStatusResp")
	proto.RegisterType((*BannedAppealRecord)(nil), "banuser.BannedAppealRecord")
	proto.RegisterType((*GetBannedAppealRecordReq)(nil), "banuser.GetBannedAppealRecordReq")
	proto.RegisterType((*GetBannedAppealRecordResp)(nil), "banuser.GetBannedAppealRecordResp")
	proto.RegisterType((*SetBannedAppealRecordReq)(nil), "banuser.SetBannedAppealRecordReq")
	proto.RegisterType((*SetBannedAppealRecordResp)(nil), "banuser.SetBannedAppealRecordResp")
	proto.RegisterType((*UpdateBannedAppealRecordReq)(nil), "banuser.UpdateBannedAppealRecordReq")
	proto.RegisterType((*UpdateBannedAppealRecordResp)(nil), "banuser.UpdateBannedAppealRecordResp")
	proto.RegisterType((*GetBannedOperatorReq)(nil), "banuser.GetBannedOperatorReq")
	proto.RegisterType((*BannedOperator)(nil), "banuser.BannedOperator")
	proto.RegisterType((*GetBannedOperatorResp)(nil), "banuser.GetBannedOperatorResp")
	proto.RegisterType((*UpdateBanedStatus)(nil), "banuser.UpdateBanedStatus")
	proto.RegisterType((*BatchUpdateBanedStatusReq)(nil), "banuser.BatchUpdateBanedStatusReq")
	proto.RegisterType((*BatchUpdateBanedStatusResp)(nil), "banuser.BatchUpdateBanedStatusResp")
	proto.RegisterType((*BannedCacheData)(nil), "banuser.BannedCacheData")
	proto.RegisterEnum("banuser.BAN_OP_TYPE", BAN_OP_TYPE_name, BAN_OP_TYPE_value)
	proto.RegisterEnum("banuser.BANNED_TYPE", BANNED_TYPE_name, BANNED_TYPE_value)
	proto.RegisterEnum("banuser.BAN_STATUS", BAN_STATUS_name, BAN_STATUS_value)
	proto.RegisterEnum("banuser.REASON_CODE", REASON_CODE_name, REASON_CODE_value)
	proto.RegisterEnum("banuser.BannedAppealState", BannedAppealState_name, BannedAppealState_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for BanUser service

type BanUserClient interface {
	UpdateBanedStatus(ctx context.Context, in *UpdateBanStatusReq, opts ...grpc.CallOption) (*UpdateBanStatusResp, error)
	GetBannedStatus(ctx context.Context, in *GetBannedStatusReq, opts ...grpc.CallOption) (*GetBannedStatusResp, error)
	BatchGetUserBannedStatus(ctx context.Context, in *BatchGetUserBannedStatusReq, opts ...grpc.CallOption) (*BatchGetUserBannedStatusResp, error)
	GetBannedHistory(ctx context.Context, in *GetBannedHistoryReq, opts ...grpc.CallOption) (*GetBannedHistoryResp, error)
	BatchGetDeviceBannedStatus(ctx context.Context, in *BatchGetDeviceBannedStatusReq, opts ...grpc.CallOption) (*BatchGetDeviceBannedStatusResp, error)
	GetBannedAppealRecord(ctx context.Context, in *GetBannedAppealRecordReq, opts ...grpc.CallOption) (*GetBannedAppealRecordResp, error)
	SetBannedAppealRecord(ctx context.Context, in *SetBannedAppealRecordReq, opts ...grpc.CallOption) (*SetBannedAppealRecordResp, error)
	UpdateBannedAppealRecord(ctx context.Context, in *UpdateBannedAppealRecordReq, opts ...grpc.CallOption) (*UpdateBannedAppealRecordResp, error)
	GetBannedOperator(ctx context.Context, in *GetBannedOperatorReq, opts ...grpc.CallOption) (*GetBannedOperatorResp, error)
	BatchUpdateBanedStatus(ctx context.Context, in *BatchUpdateBanedStatusReq, opts ...grpc.CallOption) (*BatchUpdateBanedStatusResp, error)
}

type banUserClient struct {
	cc *grpc.ClientConn
}

func NewBanUserClient(cc *grpc.ClientConn) BanUserClient {
	return &banUserClient{cc}
}

func (c *banUserClient) UpdateBanedStatus(ctx context.Context, in *UpdateBanStatusReq, opts ...grpc.CallOption) (*UpdateBanStatusResp, error) {
	out := new(UpdateBanStatusResp)
	err := grpc.Invoke(ctx, "/banuser.BanUser/UpdateBanedStatus", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *banUserClient) GetBannedStatus(ctx context.Context, in *GetBannedStatusReq, opts ...grpc.CallOption) (*GetBannedStatusResp, error) {
	out := new(GetBannedStatusResp)
	err := grpc.Invoke(ctx, "/banuser.BanUser/GetBannedStatus", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *banUserClient) BatchGetUserBannedStatus(ctx context.Context, in *BatchGetUserBannedStatusReq, opts ...grpc.CallOption) (*BatchGetUserBannedStatusResp, error) {
	out := new(BatchGetUserBannedStatusResp)
	err := grpc.Invoke(ctx, "/banuser.BanUser/BatchGetUserBannedStatus", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *banUserClient) GetBannedHistory(ctx context.Context, in *GetBannedHistoryReq, opts ...grpc.CallOption) (*GetBannedHistoryResp, error) {
	out := new(GetBannedHistoryResp)
	err := grpc.Invoke(ctx, "/banuser.BanUser/GetBannedHistory", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *banUserClient) BatchGetDeviceBannedStatus(ctx context.Context, in *BatchGetDeviceBannedStatusReq, opts ...grpc.CallOption) (*BatchGetDeviceBannedStatusResp, error) {
	out := new(BatchGetDeviceBannedStatusResp)
	err := grpc.Invoke(ctx, "/banuser.BanUser/BatchGetDeviceBannedStatus", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *banUserClient) GetBannedAppealRecord(ctx context.Context, in *GetBannedAppealRecordReq, opts ...grpc.CallOption) (*GetBannedAppealRecordResp, error) {
	out := new(GetBannedAppealRecordResp)
	err := grpc.Invoke(ctx, "/banuser.BanUser/GetBannedAppealRecord", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *banUserClient) SetBannedAppealRecord(ctx context.Context, in *SetBannedAppealRecordReq, opts ...grpc.CallOption) (*SetBannedAppealRecordResp, error) {
	out := new(SetBannedAppealRecordResp)
	err := grpc.Invoke(ctx, "/banuser.BanUser/SetBannedAppealRecord", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *banUserClient) UpdateBannedAppealRecord(ctx context.Context, in *UpdateBannedAppealRecordReq, opts ...grpc.CallOption) (*UpdateBannedAppealRecordResp, error) {
	out := new(UpdateBannedAppealRecordResp)
	err := grpc.Invoke(ctx, "/banuser.BanUser/UpdateBannedAppealRecord", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *banUserClient) GetBannedOperator(ctx context.Context, in *GetBannedOperatorReq, opts ...grpc.CallOption) (*GetBannedOperatorResp, error) {
	out := new(GetBannedOperatorResp)
	err := grpc.Invoke(ctx, "/banuser.BanUser/GetBannedOperator", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *banUserClient) BatchUpdateBanedStatus(ctx context.Context, in *BatchUpdateBanedStatusReq, opts ...grpc.CallOption) (*BatchUpdateBanedStatusResp, error) {
	out := new(BatchUpdateBanedStatusResp)
	err := grpc.Invoke(ctx, "/banuser.BanUser/BatchUpdateBanedStatus", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for BanUser service

type BanUserServer interface {
	UpdateBanedStatus(context.Context, *UpdateBanStatusReq) (*UpdateBanStatusResp, error)
	GetBannedStatus(context.Context, *GetBannedStatusReq) (*GetBannedStatusResp, error)
	BatchGetUserBannedStatus(context.Context, *BatchGetUserBannedStatusReq) (*BatchGetUserBannedStatusResp, error)
	GetBannedHistory(context.Context, *GetBannedHistoryReq) (*GetBannedHistoryResp, error)
	BatchGetDeviceBannedStatus(context.Context, *BatchGetDeviceBannedStatusReq) (*BatchGetDeviceBannedStatusResp, error)
	GetBannedAppealRecord(context.Context, *GetBannedAppealRecordReq) (*GetBannedAppealRecordResp, error)
	SetBannedAppealRecord(context.Context, *SetBannedAppealRecordReq) (*SetBannedAppealRecordResp, error)
	UpdateBannedAppealRecord(context.Context, *UpdateBannedAppealRecordReq) (*UpdateBannedAppealRecordResp, error)
	GetBannedOperator(context.Context, *GetBannedOperatorReq) (*GetBannedOperatorResp, error)
	BatchUpdateBanedStatus(context.Context, *BatchUpdateBanedStatusReq) (*BatchUpdateBanedStatusResp, error)
}

func RegisterBanUserServer(s *grpc.Server, srv BanUserServer) {
	s.RegisterService(&_BanUser_serviceDesc, srv)
}

func _BanUser_UpdateBanedStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBanStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BanUserServer).UpdateBanedStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/banuser.BanUser/UpdateBanedStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BanUserServer).UpdateBanedStatus(ctx, req.(*UpdateBanStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BanUser_GetBannedStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBannedStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BanUserServer).GetBannedStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/banuser.BanUser/GetBannedStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BanUserServer).GetBannedStatus(ctx, req.(*GetBannedStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BanUser_BatchGetUserBannedStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserBannedStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BanUserServer).BatchGetUserBannedStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/banuser.BanUser/BatchGetUserBannedStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BanUserServer).BatchGetUserBannedStatus(ctx, req.(*BatchGetUserBannedStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BanUser_GetBannedHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBannedHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BanUserServer).GetBannedHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/banuser.BanUser/GetBannedHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BanUserServer).GetBannedHistory(ctx, req.(*GetBannedHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BanUser_BatchGetDeviceBannedStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetDeviceBannedStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BanUserServer).BatchGetDeviceBannedStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/banuser.BanUser/BatchGetDeviceBannedStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BanUserServer).BatchGetDeviceBannedStatus(ctx, req.(*BatchGetDeviceBannedStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BanUser_GetBannedAppealRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBannedAppealRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BanUserServer).GetBannedAppealRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/banuser.BanUser/GetBannedAppealRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BanUserServer).GetBannedAppealRecord(ctx, req.(*GetBannedAppealRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BanUser_SetBannedAppealRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetBannedAppealRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BanUserServer).SetBannedAppealRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/banuser.BanUser/SetBannedAppealRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BanUserServer).SetBannedAppealRecord(ctx, req.(*SetBannedAppealRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BanUser_UpdateBannedAppealRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBannedAppealRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BanUserServer).UpdateBannedAppealRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/banuser.BanUser/UpdateBannedAppealRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BanUserServer).UpdateBannedAppealRecord(ctx, req.(*UpdateBannedAppealRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BanUser_GetBannedOperator_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBannedOperatorReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BanUserServer).GetBannedOperator(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/banuser.BanUser/GetBannedOperator",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BanUserServer).GetBannedOperator(ctx, req.(*GetBannedOperatorReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BanUser_BatchUpdateBanedStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUpdateBanedStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BanUserServer).BatchUpdateBanedStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/banuser.BanUser/BatchUpdateBanedStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BanUserServer).BatchUpdateBanedStatus(ctx, req.(*BatchUpdateBanedStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _BanUser_serviceDesc = grpc.ServiceDesc{
	ServiceName: "banuser.BanUser",
	HandlerType: (*BanUserServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpdateBanedStatus",
			Handler:    _BanUser_UpdateBanedStatus_Handler,
		},
		{
			MethodName: "GetBannedStatus",
			Handler:    _BanUser_GetBannedStatus_Handler,
		},
		{
			MethodName: "BatchGetUserBannedStatus",
			Handler:    _BanUser_BatchGetUserBannedStatus_Handler,
		},
		{
			MethodName: "GetBannedHistory",
			Handler:    _BanUser_GetBannedHistory_Handler,
		},
		{
			MethodName: "BatchGetDeviceBannedStatus",
			Handler:    _BanUser_BatchGetDeviceBannedStatus_Handler,
		},
		{
			MethodName: "GetBannedAppealRecord",
			Handler:    _BanUser_GetBannedAppealRecord_Handler,
		},
		{
			MethodName: "SetBannedAppealRecord",
			Handler:    _BanUser_SetBannedAppealRecord_Handler,
		},
		{
			MethodName: "UpdateBannedAppealRecord",
			Handler:    _BanUser_UpdateBannedAppealRecord_Handler,
		},
		{
			MethodName: "GetBannedOperator",
			Handler:    _BanUser_GetBannedOperator_Handler,
		},
		{
			MethodName: "BatchUpdateBanedStatus",
			Handler:    _BanUser_BatchUpdateBanedStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/banusersvr/banuser.proto",
}

func (m *UpdateBanStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateBanStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.OpType))
	dAtA[i] = 0x10
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	dAtA[i] = 0x22
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.ClientIp)))
	i += copy(dAtA[i:], m.ClientIp)
	dAtA[i] = 0x28
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.At))
	dAtA[i] = 0x30
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.RecoveryAt))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.Reason)))
	i += copy(dAtA[i:], m.Reason)
	dAtA[i] = 0x42
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.OperatorId)))
	i += copy(dAtA[i:], m.OperatorId)
	dAtA[i] = 0x4a
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.ProofPic)))
	i += copy(dAtA[i:], m.ProofPic)
	dAtA[i] = 0x52
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.ExtInfo)))
	i += copy(dAtA[i:], m.ExtInfo)
	dAtA[i] = 0x58
	i++
	if m.NoLog {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x62
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.ReasonDetail)))
	i += copy(dAtA[i:], m.ReasonDetail)
	return i, nil
}

func (m *UpdateBanStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateBanStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetBannedStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetBannedStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.ClientIp)))
	i += copy(dAtA[i:], m.ClientIp)
	dAtA[i] = 0x22
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	return i, nil
}

func (m *BannedStatus) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BannedStatus) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x10
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.BannedType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x20
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.BannedAt))
	dAtA[i] = 0x28
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.RecoveryAt))
	dAtA[i] = 0x32
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.Reason)))
	i += copy(dAtA[i:], m.Reason)
	dAtA[i] = 0x38
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.ReasonCode))
	dAtA[i] = 0x42
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.ReasonDetail)))
	i += copy(dAtA[i:], m.ReasonDetail)
	return i, nil
}

func (m *GetBannedStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetBannedStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BannedStatus == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("banned_status")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintBanuser(dAtA, i, uint64(m.BannedStatus.Size()))
		n1, err := m.BannedStatus.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *BatchGetUserBannedStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetUserBannedStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintBanuser(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *BatchGetUserBannedStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetUserBannedStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.StatusList) > 0 {
		for _, msg := range m.StatusList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintBanuser(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetBannedHistoryReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetBannedHistoryReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.ClientIp)))
	i += copy(dAtA[i:], m.ClientIp)
	dAtA[i] = 0x20
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.OpType))
	dAtA[i] = 0x28
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.Offset))
	dAtA[i] = 0x30
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.Limit))
	dAtA[i] = 0x38
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.BeginTime))
	dAtA[i] = 0x40
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.EndTime))
	dAtA[i] = 0x4a
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.OperatorId)))
	i += copy(dAtA[i:], m.OperatorId)
	dAtA[i] = 0x52
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.Reason)))
	i += copy(dAtA[i:], m.Reason)
	return i, nil
}

func (m *BannedRecord) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BannedRecord) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.Op))
	dAtA[i] = 0x10
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.At))
	dAtA[i] = 0x18
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.RecoveryAt))
	dAtA[i] = 0x22
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.Reason)))
	i += copy(dAtA[i:], m.Reason)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.OperatorId)))
	i += copy(dAtA[i:], m.OperatorId)
	dAtA[i] = 0x30
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	dAtA[i] = 0x42
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.ClientIp)))
	i += copy(dAtA[i:], m.ClientIp)
	dAtA[i] = 0x4a
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.ProofPic)))
	i += copy(dAtA[i:], m.ProofPic)
	dAtA[i] = 0x52
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.ExtInfo)))
	i += copy(dAtA[i:], m.ExtInfo)
	dAtA[i] = 0x5a
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.ReasonDetail)))
	i += copy(dAtA[i:], m.ReasonDetail)
	return i, nil
}

func (m *GetBannedHistoryResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetBannedHistoryResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Records) > 0 {
		for _, msg := range m.Records {
			dAtA[i] = 0xa
			i++
			i = encodeVarintBanuser(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.Total))
	return i, nil
}

func (m *DeviceBannedStatus) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeviceBannedStatus) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x10
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.BannedType))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	dAtA[i] = 0x20
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.BannedAt))
	dAtA[i] = 0x28
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.RecoveryAt))
	dAtA[i] = 0x32
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.Reason)))
	i += copy(dAtA[i:], m.Reason)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.ReasonDetail)))
	i += copy(dAtA[i:], m.ReasonDetail)
	return i, nil
}

func (m *BatchGetDeviceBannedStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetDeviceBannedStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.DeviceIds) > 0 {
		for _, s := range m.DeviceIds {
			dAtA[i] = 0xa
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *BatchGetDeviceBannedStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetDeviceBannedStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.StatusList) > 0 {
		for _, msg := range m.StatusList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintBanuser(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *BannedAppealRecord) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BannedAppealRecord) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x10
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.State))
	dAtA[i] = 0x20
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.ReasonCode))
	dAtA[i] = 0x28
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.BannedBeginAt))
	dAtA[i] = 0x30
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.BannedEndAt))
	dAtA[i] = 0x38
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.CreateAt))
	dAtA[i] = 0x40
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.UpdateAt))
	dAtA[i] = 0x4a
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.CreateDesc)))
	i += copy(dAtA[i:], m.CreateDesc)
	dAtA[i] = 0x52
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.UpdateDesc)))
	i += copy(dAtA[i:], m.UpdateDesc)
	dAtA[i] = 0x5a
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.Operator)))
	i += copy(dAtA[i:], m.Operator)
	dAtA[i] = 0x60
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.MarketId))
	return i, nil
}

func (m *GetBannedAppealRecordReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetBannedAppealRecordReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintBanuser(dAtA, i, uint64(num))
		}
	}
	if len(m.StateList) > 0 {
		for _, num := range m.StateList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintBanuser(dAtA, i, uint64(num))
		}
	}
	if len(m.ReasonCodeList) > 0 {
		for _, num := range m.ReasonCodeList {
			dAtA[i] = 0x18
			i++
			i = encodeVarintBanuser(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.CreateBeginAt))
	dAtA[i] = 0x28
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.CreateEndAt))
	dAtA[i] = 0x30
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.Offset))
	dAtA[i] = 0x38
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.Limit))
	dAtA[i] = 0x40
	i++
	if m.RespTotal {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetBannedAppealRecordResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetBannedAppealRecordResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.RecordList) > 0 {
		for _, msg := range m.RecordList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintBanuser(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.Total))
	return i, nil
}

func (m *SetBannedAppealRecordReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetBannedAppealRecordReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.RecordList) > 0 {
		for _, msg := range m.RecordList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintBanuser(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *SetBannedAppealRecordResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetBannedAppealRecordResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *UpdateBannedAppealRecordReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateBannedAppealRecordReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x10
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.State))
	dAtA[i] = 0x22
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.UpdateDesc)))
	i += copy(dAtA[i:], m.UpdateDesc)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.Operator)))
	i += copy(dAtA[i:], m.Operator)
	return i, nil
}

func (m *UpdateBannedAppealRecordResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateBannedAppealRecordResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.Updated {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetBannedOperatorReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetBannedOperatorReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.OperatorName)))
	i += copy(dAtA[i:], m.OperatorName)
	dAtA[i] = 0x10
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.Limit))
	return i, nil
}

func (m *BannedOperator) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BannedOperator) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.OperatorName)))
	i += copy(dAtA[i:], m.OperatorName)
	dAtA[i] = 0x10
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.LastOpAt))
	dAtA[i] = 0x18
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.LastOpType))
	return i, nil
}

func (m *GetBannedOperatorResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetBannedOperatorResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.OperatorList) > 0 {
		for _, msg := range m.OperatorList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintBanuser(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *UpdateBanedStatus) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateBanedStatus) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.OpType))
	dAtA[i] = 0x10
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	dAtA[i] = 0x22
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.ClientIp)))
	i += copy(dAtA[i:], m.ClientIp)
	dAtA[i] = 0x28
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.At))
	dAtA[i] = 0x30
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.RecoveryAt))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.Reason)))
	i += copy(dAtA[i:], m.Reason)
	dAtA[i] = 0x42
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.OperatorId)))
	i += copy(dAtA[i:], m.OperatorId)
	dAtA[i] = 0x4a
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.ProofPic)))
	i += copy(dAtA[i:], m.ProofPic)
	dAtA[i] = 0x52
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.ExtInfo)))
	i += copy(dAtA[i:], m.ExtInfo)
	dAtA[i] = 0x58
	i++
	if m.NoLog {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x62
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.ReasonDetail)))
	i += copy(dAtA[i:], m.ReasonDetail)
	return i, nil
}

func (m *BatchUpdateBanedStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchUpdateBanedStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.List) > 0 {
		for _, msg := range m.List {
			dAtA[i] = 0xa
			i++
			i = encodeVarintBanuser(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *BatchUpdateBanedStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchUpdateBanedStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.SuccessSize))
	return i, nil
}

func (m *BannedCacheData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BannedCacheData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x10
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.BannedAt))
	dAtA[i] = 0x18
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.RecoveryAt))
	dAtA[i] = 0x22
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.Reason)))
	i += copy(dAtA[i:], m.Reason)
	dAtA[i] = 0x28
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.RefUid))
	dAtA[i] = 0x32
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.RefDevice)))
	i += copy(dAtA[i:], m.RefDevice)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.RefPhone)))
	i += copy(dAtA[i:], m.RefPhone)
	dAtA[i] = 0x40
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(m.ReasonCode))
	dAtA[i] = 0x4a
	i++
	i = encodeVarintBanuser(dAtA, i, uint64(len(m.ReasonDetail)))
	i += copy(dAtA[i:], m.ReasonDetail)
	return i, nil
}

func encodeFixed64Banuser(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Banuser(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintBanuser(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *UpdateBanStatusReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovBanuser(uint64(m.OpType))
	n += 1 + sovBanuser(uint64(m.Uid))
	l = len(m.DeviceId)
	n += 1 + l + sovBanuser(uint64(l))
	l = len(m.ClientIp)
	n += 1 + l + sovBanuser(uint64(l))
	n += 1 + sovBanuser(uint64(m.At))
	n += 1 + sovBanuser(uint64(m.RecoveryAt))
	l = len(m.Reason)
	n += 1 + l + sovBanuser(uint64(l))
	l = len(m.OperatorId)
	n += 1 + l + sovBanuser(uint64(l))
	l = len(m.ProofPic)
	n += 1 + l + sovBanuser(uint64(l))
	l = len(m.ExtInfo)
	n += 1 + l + sovBanuser(uint64(l))
	n += 2
	l = len(m.ReasonDetail)
	n += 1 + l + sovBanuser(uint64(l))
	return n
}

func (m *UpdateBanStatusResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetBannedStatusReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovBanuser(uint64(m.Uid))
	l = len(m.DeviceId)
	n += 1 + l + sovBanuser(uint64(l))
	l = len(m.ClientIp)
	n += 1 + l + sovBanuser(uint64(l))
	l = len(m.Phone)
	n += 1 + l + sovBanuser(uint64(l))
	return n
}

func (m *BannedStatus) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovBanuser(uint64(m.Status))
	n += 1 + sovBanuser(uint64(m.BannedType))
	n += 1 + sovBanuser(uint64(m.Uid))
	n += 1 + sovBanuser(uint64(m.BannedAt))
	n += 1 + sovBanuser(uint64(m.RecoveryAt))
	l = len(m.Reason)
	n += 1 + l + sovBanuser(uint64(l))
	n += 1 + sovBanuser(uint64(m.ReasonCode))
	l = len(m.ReasonDetail)
	n += 1 + l + sovBanuser(uint64(l))
	return n
}

func (m *GetBannedStatusResp) Size() (n int) {
	var l int
	_ = l
	if m.BannedStatus != nil {
		l = m.BannedStatus.Size()
		n += 1 + l + sovBanuser(uint64(l))
	}
	return n
}

func (m *BatchGetUserBannedStatusReq) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovBanuser(uint64(e))
		}
	}
	return n
}

func (m *BatchGetUserBannedStatusResp) Size() (n int) {
	var l int
	_ = l
	if len(m.StatusList) > 0 {
		for _, e := range m.StatusList {
			l = e.Size()
			n += 1 + l + sovBanuser(uint64(l))
		}
	}
	return n
}

func (m *GetBannedHistoryReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovBanuser(uint64(m.Uid))
	l = len(m.DeviceId)
	n += 1 + l + sovBanuser(uint64(l))
	l = len(m.ClientIp)
	n += 1 + l + sovBanuser(uint64(l))
	n += 1 + sovBanuser(uint64(m.OpType))
	n += 1 + sovBanuser(uint64(m.Offset))
	n += 1 + sovBanuser(uint64(m.Limit))
	n += 1 + sovBanuser(uint64(m.BeginTime))
	n += 1 + sovBanuser(uint64(m.EndTime))
	l = len(m.OperatorId)
	n += 1 + l + sovBanuser(uint64(l))
	l = len(m.Reason)
	n += 1 + l + sovBanuser(uint64(l))
	return n
}

func (m *BannedRecord) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovBanuser(uint64(m.Op))
	n += 1 + sovBanuser(uint64(m.At))
	n += 1 + sovBanuser(uint64(m.RecoveryAt))
	l = len(m.Reason)
	n += 1 + l + sovBanuser(uint64(l))
	l = len(m.OperatorId)
	n += 1 + l + sovBanuser(uint64(l))
	n += 1 + sovBanuser(uint64(m.Uid))
	l = len(m.DeviceId)
	n += 1 + l + sovBanuser(uint64(l))
	l = len(m.ClientIp)
	n += 1 + l + sovBanuser(uint64(l))
	l = len(m.ProofPic)
	n += 1 + l + sovBanuser(uint64(l))
	l = len(m.ExtInfo)
	n += 1 + l + sovBanuser(uint64(l))
	l = len(m.ReasonDetail)
	n += 1 + l + sovBanuser(uint64(l))
	return n
}

func (m *GetBannedHistoryResp) Size() (n int) {
	var l int
	_ = l
	if len(m.Records) > 0 {
		for _, e := range m.Records {
			l = e.Size()
			n += 1 + l + sovBanuser(uint64(l))
		}
	}
	n += 1 + sovBanuser(uint64(m.Total))
	return n
}

func (m *DeviceBannedStatus) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovBanuser(uint64(m.Status))
	n += 1 + sovBanuser(uint64(m.BannedType))
	l = len(m.DeviceId)
	n += 1 + l + sovBanuser(uint64(l))
	n += 1 + sovBanuser(uint64(m.BannedAt))
	n += 1 + sovBanuser(uint64(m.RecoveryAt))
	l = len(m.Reason)
	n += 1 + l + sovBanuser(uint64(l))
	l = len(m.ReasonDetail)
	n += 1 + l + sovBanuser(uint64(l))
	return n
}

func (m *BatchGetDeviceBannedStatusReq) Size() (n int) {
	var l int
	_ = l
	if len(m.DeviceIds) > 0 {
		for _, s := range m.DeviceIds {
			l = len(s)
			n += 1 + l + sovBanuser(uint64(l))
		}
	}
	return n
}

func (m *BatchGetDeviceBannedStatusResp) Size() (n int) {
	var l int
	_ = l
	if len(m.StatusList) > 0 {
		for _, e := range m.StatusList {
			l = e.Size()
			n += 1 + l + sovBanuser(uint64(l))
		}
	}
	return n
}

func (m *BannedAppealRecord) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovBanuser(uint64(m.Id))
	n += 1 + sovBanuser(uint64(m.Uid))
	n += 1 + sovBanuser(uint64(m.State))
	n += 1 + sovBanuser(uint64(m.ReasonCode))
	n += 1 + sovBanuser(uint64(m.BannedBeginAt))
	n += 1 + sovBanuser(uint64(m.BannedEndAt))
	n += 1 + sovBanuser(uint64(m.CreateAt))
	n += 1 + sovBanuser(uint64(m.UpdateAt))
	l = len(m.CreateDesc)
	n += 1 + l + sovBanuser(uint64(l))
	l = len(m.UpdateDesc)
	n += 1 + l + sovBanuser(uint64(l))
	l = len(m.Operator)
	n += 1 + l + sovBanuser(uint64(l))
	n += 1 + sovBanuser(uint64(m.MarketId))
	return n
}

func (m *GetBannedAppealRecordReq) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovBanuser(uint64(e))
		}
	}
	if len(m.StateList) > 0 {
		for _, e := range m.StateList {
			n += 1 + sovBanuser(uint64(e))
		}
	}
	if len(m.ReasonCodeList) > 0 {
		for _, e := range m.ReasonCodeList {
			n += 1 + sovBanuser(uint64(e))
		}
	}
	n += 1 + sovBanuser(uint64(m.CreateBeginAt))
	n += 1 + sovBanuser(uint64(m.CreateEndAt))
	n += 1 + sovBanuser(uint64(m.Offset))
	n += 1 + sovBanuser(uint64(m.Limit))
	n += 2
	return n
}

func (m *GetBannedAppealRecordResp) Size() (n int) {
	var l int
	_ = l
	if len(m.RecordList) > 0 {
		for _, e := range m.RecordList {
			l = e.Size()
			n += 1 + l + sovBanuser(uint64(l))
		}
	}
	n += 1 + sovBanuser(uint64(m.Total))
	return n
}

func (m *SetBannedAppealRecordReq) Size() (n int) {
	var l int
	_ = l
	if len(m.RecordList) > 0 {
		for _, e := range m.RecordList {
			l = e.Size()
			n += 1 + l + sovBanuser(uint64(l))
		}
	}
	return n
}

func (m *SetBannedAppealRecordResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *UpdateBannedAppealRecordReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovBanuser(uint64(m.Id))
	n += 1 + sovBanuser(uint64(m.Uid))
	n += 1 + sovBanuser(uint64(m.State))
	l = len(m.UpdateDesc)
	n += 1 + l + sovBanuser(uint64(l))
	l = len(m.Operator)
	n += 1 + l + sovBanuser(uint64(l))
	return n
}

func (m *UpdateBannedAppealRecordResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	return n
}

func (m *GetBannedOperatorReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.OperatorName)
	n += 1 + l + sovBanuser(uint64(l))
	n += 1 + sovBanuser(uint64(m.Limit))
	return n
}

func (m *BannedOperator) Size() (n int) {
	var l int
	_ = l
	l = len(m.OperatorName)
	n += 1 + l + sovBanuser(uint64(l))
	n += 1 + sovBanuser(uint64(m.LastOpAt))
	n += 1 + sovBanuser(uint64(m.LastOpType))
	return n
}

func (m *GetBannedOperatorResp) Size() (n int) {
	var l int
	_ = l
	if len(m.OperatorList) > 0 {
		for _, e := range m.OperatorList {
			l = e.Size()
			n += 1 + l + sovBanuser(uint64(l))
		}
	}
	return n
}

func (m *UpdateBanedStatus) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovBanuser(uint64(m.OpType))
	n += 1 + sovBanuser(uint64(m.Uid))
	l = len(m.DeviceId)
	n += 1 + l + sovBanuser(uint64(l))
	l = len(m.ClientIp)
	n += 1 + l + sovBanuser(uint64(l))
	n += 1 + sovBanuser(uint64(m.At))
	n += 1 + sovBanuser(uint64(m.RecoveryAt))
	l = len(m.Reason)
	n += 1 + l + sovBanuser(uint64(l))
	l = len(m.OperatorId)
	n += 1 + l + sovBanuser(uint64(l))
	l = len(m.ProofPic)
	n += 1 + l + sovBanuser(uint64(l))
	l = len(m.ExtInfo)
	n += 1 + l + sovBanuser(uint64(l))
	n += 2
	l = len(m.ReasonDetail)
	n += 1 + l + sovBanuser(uint64(l))
	return n
}

func (m *BatchUpdateBanedStatusReq) Size() (n int) {
	var l int
	_ = l
	if len(m.List) > 0 {
		for _, e := range m.List {
			l = e.Size()
			n += 1 + l + sovBanuser(uint64(l))
		}
	}
	return n
}

func (m *BatchUpdateBanedStatusResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovBanuser(uint64(m.SuccessSize))
	return n
}

func (m *BannedCacheData) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovBanuser(uint64(m.Status))
	n += 1 + sovBanuser(uint64(m.BannedAt))
	n += 1 + sovBanuser(uint64(m.RecoveryAt))
	l = len(m.Reason)
	n += 1 + l + sovBanuser(uint64(l))
	n += 1 + sovBanuser(uint64(m.RefUid))
	l = len(m.RefDevice)
	n += 1 + l + sovBanuser(uint64(l))
	l = len(m.RefPhone)
	n += 1 + l + sovBanuser(uint64(l))
	n += 1 + sovBanuser(uint64(m.ReasonCode))
	l = len(m.ReasonDetail)
	n += 1 + l + sovBanuser(uint64(l))
	return n
}

func sovBanuser(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozBanuser(x uint64) (n int) {
	return sovBanuser(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *UpdateBanStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBanuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateBanStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateBanStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpType", wireType)
			}
			m.OpType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientIp", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ClientIp = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field At", wireType)
			}
			m.At = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.At |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecoveryAt", wireType)
			}
			m.RecoveryAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RecoveryAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Reason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Reason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperatorId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OperatorId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProofPic", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ProofPic = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ExtInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NoLog", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.NoLog = bool(v != 0)
		case 12:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReasonDetail", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ReasonDetail = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBanuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBanuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateBanStatusResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBanuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateBanStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateBanStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipBanuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBanuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetBannedStatusReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBanuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetBannedStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetBannedStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientIp", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ClientIp = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBanuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBanuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BannedStatus) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBanuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BannedStatus: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BannedStatus: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BannedType", wireType)
			}
			m.BannedType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BannedType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BannedAt", wireType)
			}
			m.BannedAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BannedAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecoveryAt", wireType)
			}
			m.RecoveryAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RecoveryAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Reason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Reason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReasonCode", wireType)
			}
			m.ReasonCode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReasonCode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReasonDetail", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ReasonDetail = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBanuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBanuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetBannedStatusResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBanuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetBannedStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetBannedStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BannedStatus", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BannedStatus == nil {
				m.BannedStatus = &BannedStatus{}
			}
			if err := m.BannedStatus.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipBanuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBanuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("banned_status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetUserBannedStatusReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBanuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetUserBannedStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetUserBannedStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBanuser
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBanuser
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthBanuser
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowBanuser
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBanuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBanuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetUserBannedStatusResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBanuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetUserBannedStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetUserBannedStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StatusList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.StatusList = append(m.StatusList, &BannedStatus{})
			if err := m.StatusList[len(m.StatusList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBanuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBanuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetBannedHistoryReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBanuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetBannedHistoryReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetBannedHistoryReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientIp", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ClientIp = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpType", wireType)
			}
			m.OpType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperatorId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OperatorId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Reason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Reason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBanuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBanuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BannedRecord) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBanuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BannedRecord: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BannedRecord: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Op", wireType)
			}
			m.Op = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Op |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field At", wireType)
			}
			m.At = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.At |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecoveryAt", wireType)
			}
			m.RecoveryAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RecoveryAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Reason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Reason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperatorId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OperatorId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientIp", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ClientIp = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProofPic", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ProofPic = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ExtInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReasonDetail", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ReasonDetail = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBanuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBanuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("at")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("recovery_at")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetBannedHistoryResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBanuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetBannedHistoryResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetBannedHistoryResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Records", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Records = append(m.Records, &BannedRecord{})
			if err := m.Records[len(m.Records)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipBanuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBanuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeviceBannedStatus) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBanuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeviceBannedStatus: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeviceBannedStatus: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BannedType", wireType)
			}
			m.BannedType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BannedType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BannedAt", wireType)
			}
			m.BannedAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BannedAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecoveryAt", wireType)
			}
			m.RecoveryAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RecoveryAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Reason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Reason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReasonDetail", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ReasonDetail = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBanuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBanuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetDeviceBannedStatusReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBanuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetDeviceBannedStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetDeviceBannedStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceIds", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceIds = append(m.DeviceIds, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBanuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBanuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetDeviceBannedStatusResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBanuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetDeviceBannedStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetDeviceBannedStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StatusList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.StatusList = append(m.StatusList, &DeviceBannedStatus{})
			if err := m.StatusList[len(m.StatusList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBanuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBanuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BannedAppealRecord) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBanuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BannedAppealRecord: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BannedAppealRecord: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field State", wireType)
			}
			m.State = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.State |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReasonCode", wireType)
			}
			m.ReasonCode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReasonCode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BannedBeginAt", wireType)
			}
			m.BannedBeginAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BannedBeginAt |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BannedEndAt", wireType)
			}
			m.BannedEndAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BannedEndAt |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateAt", wireType)
			}
			m.CreateAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateAt |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UpdateAt", wireType)
			}
			m.UpdateAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UpdateAt |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CreateDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UpdateDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UpdateDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Operator", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Operator = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MarketId", wireType)
			}
			m.MarketId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MarketId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBanuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBanuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetBannedAppealRecordReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBanuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetBannedAppealRecordReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetBannedAppealRecordReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBanuser
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBanuser
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthBanuser
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowBanuser
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBanuser
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.StateList = append(m.StateList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBanuser
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthBanuser
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowBanuser
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.StateList = append(m.StateList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field StateList", wireType)
			}
		case 3:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBanuser
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ReasonCodeList = append(m.ReasonCodeList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBanuser
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthBanuser
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowBanuser
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ReasonCodeList = append(m.ReasonCodeList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReasonCodeList", wireType)
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateBeginAt", wireType)
			}
			m.CreateBeginAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateBeginAt |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateEndAt", wireType)
			}
			m.CreateEndAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateEndAt |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RespTotal", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.RespTotal = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipBanuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBanuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetBannedAppealRecordResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBanuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetBannedAppealRecordResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetBannedAppealRecordResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecordList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RecordList = append(m.RecordList, &BannedAppealRecord{})
			if err := m.RecordList[len(m.RecordList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBanuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBanuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetBannedAppealRecordReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBanuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetBannedAppealRecordReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetBannedAppealRecordReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecordList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RecordList = append(m.RecordList, &BannedAppealRecord{})
			if err := m.RecordList[len(m.RecordList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBanuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBanuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetBannedAppealRecordResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBanuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetBannedAppealRecordResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetBannedAppealRecordResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipBanuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBanuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateBannedAppealRecordReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBanuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateBannedAppealRecordReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateBannedAppealRecordReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field State", wireType)
			}
			m.State = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.State |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UpdateDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UpdateDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Operator", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Operator = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBanuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBanuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateBannedAppealRecordResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBanuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateBannedAppealRecordResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateBannedAppealRecordResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Updated", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Updated = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipBanuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBanuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetBannedOperatorReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBanuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetBannedOperatorReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetBannedOperatorReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperatorName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OperatorName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBanuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBanuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BannedOperator) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBanuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BannedOperator: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BannedOperator: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperatorName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OperatorName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastOpAt", wireType)
			}
			m.LastOpAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastOpAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastOpType", wireType)
			}
			m.LastOpType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastOpType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBanuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBanuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetBannedOperatorResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBanuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetBannedOperatorResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetBannedOperatorResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperatorList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OperatorList = append(m.OperatorList, &BannedOperator{})
			if err := m.OperatorList[len(m.OperatorList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBanuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBanuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateBanedStatus) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBanuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateBanedStatus: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateBanedStatus: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpType", wireType)
			}
			m.OpType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientIp", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ClientIp = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field At", wireType)
			}
			m.At = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.At |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecoveryAt", wireType)
			}
			m.RecoveryAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RecoveryAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Reason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Reason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperatorId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OperatorId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProofPic", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ProofPic = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ExtInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NoLog", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.NoLog = bool(v != 0)
		case 12:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReasonDetail", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ReasonDetail = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBanuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBanuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchUpdateBanedStatusReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBanuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchUpdateBanedStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchUpdateBanedStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field List", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.List = append(m.List, &UpdateBanedStatus{})
			if err := m.List[len(m.List)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBanuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBanuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchUpdateBanedStatusResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBanuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchUpdateBanedStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchUpdateBanedStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SuccessSize", wireType)
			}
			m.SuccessSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SuccessSize |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBanuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBanuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BannedCacheData) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBanuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BannedCacheData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BannedCacheData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BannedAt", wireType)
			}
			m.BannedAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BannedAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecoveryAt", wireType)
			}
			m.RecoveryAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RecoveryAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Reason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Reason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RefUid", wireType)
			}
			m.RefUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RefUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RefDevice", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RefDevice = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RefPhone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RefPhone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReasonCode", wireType)
			}
			m.ReasonCode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReasonCode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReasonDetail", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBanuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ReasonDetail = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBanuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBanuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipBanuser(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowBanuser
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowBanuser
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthBanuser
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowBanuser
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipBanuser(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthBanuser = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowBanuser   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/banusersvr/banuser.proto", fileDescriptorBanuser) }

var fileDescriptorBanuser = []byte{
	// 2248 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x58, 0xcd, 0x6f, 0xdb, 0xc8,
	0x15, 0x37, 0x29, 0xd9, 0x92, 0x9e, 0xed, 0x58, 0x9e, 0x7c, 0xac, 0x22, 0x3b, 0x0e, 0x97, 0xd9,
	0x0f, 0x6f, 0x60, 0x39, 0x48, 0x0e, 0x45, 0x4b, 0x08, 0x02, 0x24, 0x59, 0xd8, 0x18, 0xeb, 0xca,
	0x5e, 0xc9, 0x5e, 0xd4, 0x58, 0xa4, 0x04, 0x25, 0x8e, 0x12, 0x22, 0x12, 0xc9, 0x72, 0xa8, 0x20,
	0xc9, 0x29, 0x97, 0xa2, 0x45, 0xb1, 0x68, 0x8b, 0x1e, 0x7a, 0x2a, 0xda, 0x4b, 0x7a, 0x29, 0x50,
	0xa0, 0x97, 0xf6, 0xd2, 0x7f, 0x60, 0x0f, 0x05, 0xba, 0x3d, 0x6d, 0x4f, 0x45, 0x91, 0x5e, 0x02,
	0xf4, 0xd8, 0x7b, 0x51, 0xcc, 0x0c, 0x49, 0x0d, 0x25, 0x52, 0x72, 0xd1, 0xb4, 0xe8, 0x61, 0x4f,
	0x12, 0xdf, 0x7b, 0x7c, 0x7c, 0xef, 0xfd, 0xde, 0xd7, 0x0c, 0x6c, 0x13, 0xaf, 0x7f, 0xa7, 0x67,
	0xd8, 0x63, 0x82, 0x3d, 0xf2, 0xc4, 0x0b, 0xff, 0xee, 0xbb, 0x9e, 0xe3, 0x3b, 0x28, 0x17, 0x3c,
	0x96, 0xdf, 0xe9, 0x3b, 0xa3, 0x91, 0x63, 0xdf, 0xf1, 0x87, 0x4f, 0x5c, 0xab, 0xff, 0x78, 0x88,
	0xef, 0x90, 0xc7, 0xbd, 0xb1, 0x35, 0xf4, 0x2d, 0xdb, 0x7f, 0xe6, 0x62, 0x2e, 0xae, 0xfe, 0x22,
	0x03, 0xe8, 0xcc, 0x35, 0x0d, 0x1f, 0x37, 0x0c, 0xbb, 0xeb, 0x1b, 0xfe, 0x98, 0x74, 0xf0, 0x77,
	0xd0, 0x0d, 0xc8, 0x39, 0xae, 0x4e, 0xe5, 0x4a, 0x92, 0x22, 0xef, 0xae, 0x37, 0xb2, 0x9f, 0xff,
	0xe5, 0xe6, 0x52, 0x67, 0xc5, 0x71, 0x4f, 0x9f, 0xb9, 0x18, 0x5d, 0x83, 0xcc, 0xd8, 0x32, 0x4b,
	0xb2, 0x22, 0x45, 0x2c, 0x4a, 0x40, 0x6f, 0x43, 0xc1, 0xc4, 0x4f, 0xac, 0x3e, 0xd6, 0x2d, 0xb3,
	0x94, 0x51, 0xa4, 0xdd, 0x42, 0xc0, 0xcd, 0x73, 0xf2, 0x21, 0x13, 0xe9, 0x0f, 0x2d, 0x6c, 0xfb,
	0xba, 0xe5, 0x96, 0xb2, 0xa2, 0x08, 0x27, 0x1f, 0xba, 0xe8, 0x0a, 0xc8, 0x86, 0x5f, 0x5a, 0x16,
	0x94, 0xcb, 0x86, 0x8f, 0xde, 0x85, 0x55, 0x0f, 0xf7, 0x9d, 0x27, 0xd8, 0x7b, 0xa6, 0x1b, 0x7e,
	0x69, 0x45, 0x60, 0x43, 0xc8, 0xa8, 0xfb, 0x68, 0x1b, 0x56, 0x3c, 0x6c, 0x10, 0xc7, 0x2e, 0xe5,
	0x04, 0xe5, 0x01, 0x8d, 0x2a, 0x71, 0x5c, 0xec, 0x19, 0xbe, 0xe3, 0x51, 0x13, 0xf3, 0x82, 0x08,
	0x84, 0x0c, 0x6e, 0xa4, 0xeb, 0x39, 0xce, 0x40, 0x77, 0xad, 0x7e, 0xa9, 0x20, 0x1a, 0xc9, 0xc8,
	0x27, 0x56, 0x1f, 0xdd, 0x84, 0x3c, 0x7e, 0xea, 0xeb, 0x96, 0x3d, 0x70, 0x4a, 0x20, 0x48, 0xe4,
	0xf0, 0x53, 0xff, 0xd0, 0x1e, 0x38, 0x68, 0x0b, 0x56, 0x6c, 0x47, 0x1f, 0x3a, 0x0f, 0x4b, 0xab,
	0x8a, 0xb4, 0x9b, 0x0f, 0xd8, 0xcb, 0xb6, 0x73, 0xe4, 0x3c, 0x44, 0x1f, 0xc0, 0x3a, 0xb7, 0x48,
	0x37, 0xb1, 0x6f, 0x58, 0xc3, 0xd2, 0x9a, 0xa0, 0x62, 0x8d, 0xb3, 0x0e, 0x18, 0x47, 0xbd, 0x0a,
	0x97, 0x67, 0x00, 0x22, 0xae, 0xfa, 0x43, 0x09, 0xd0, 0x87, 0xd8, 0x6f, 0x18, 0xb6, 0x8d, 0xcd,
	0x09, 0x70, 0x01, 0x32, 0xd2, 0x5c, 0x64, 0xe4, 0xc5, 0xc8, 0x64, 0x12, 0x91, 0x29, 0xc3, 0xb2,
	0xfb, 0xc8, 0xb1, 0x71, 0x0c, 0x38, 0x4e, 0x52, 0x7f, 0x29, 0xc3, 0x9a, 0x68, 0x0d, 0x45, 0x82,
	0xb0, 0x7f, 0xf1, 0x14, 0xe2, 0x34, 0x8a, 0x44, 0x8f, 0x49, 0xf3, 0x2c, 0x13, 0x53, 0x09, 0x38,
	0x43, 0xcc, 0xb4, 0x4c, 0x82, 0x3f, 0xc1, 0xeb, 0x86, 0xcf, 0xac, 0x09, 0xb9, 0x79, 0x4e, 0xae,
	0xcf, 0x24, 0xcc, 0xf2, 0xc2, 0x84, 0x59, 0x49, 0x4e, 0x98, 0x00, 0xa8, 0xbe, 0x63, 0x62, 0x96,
	0x53, 0x82, 0x12, 0xca, 0x68, 0x3a, 0x26, 0x9e, 0xc5, 0x33, 0x9f, 0x8a, 0xe7, 0xc7, 0x70, 0x79,
	0x06, 0x37, 0xe2, 0x22, 0x0d, 0xd6, 0x03, 0x87, 0x84, 0xa0, 0xad, 0xde, 0xbb, 0xba, 0x1f, 0x96,
	0x77, 0xec, 0x8d, 0xb5, 0x9e, 0xf0, 0xa4, 0x7e, 0x1d, 0xb6, 0x1a, 0x86, 0xdf, 0x7f, 0xf4, 0x21,
	0xf6, 0xcf, 0x08, 0xf6, 0xa6, 0x73, 0xe2, 0x3a, 0xe4, 0xc7, 0x96, 0xa9, 0x0f, 0x2d, 0xe2, 0x97,
	0x24, 0x25, 0xb3, 0xbb, 0xde, 0xc9, 0x8d, 0x2d, 0xf3, 0xc8, 0x22, 0xbe, 0xfa, 0x09, 0x6c, 0xa7,
	0xbf, 0x49, 0x5c, 0xf4, 0x35, 0x58, 0xe5, 0xe6, 0x4c, 0xde, 0x4e, 0xb5, 0x09, 0xb8, 0x24, 0xd3,
	0xfb, 0xa5, 0x2c, 0x78, 0x79, 0xdf, 0x22, 0xbe, 0xe3, 0x3d, 0xfb, 0xef, 0xa7, 0xa7, 0xd0, 0xb5,
	0xc4, 0x94, 0x08, 0xbb, 0xd6, 0x36, 0xac, 0x38, 0x83, 0x01, 0xc1, 0xf1, 0x5c, 0x08, 0x68, 0x34,
	0xb7, 0x87, 0xd6, 0xc8, 0x8a, 0x77, 0x16, 0x4e, 0x42, 0xb7, 0x00, 0x7a, 0xf8, 0xa1, 0x65, 0xeb,
	0xbe, 0x35, 0x8a, 0x27, 0x41, 0x81, 0xd1, 0x4f, 0xad, 0x11, 0x66, 0x1d, 0xc1, 0x36, 0xb9, 0x48,
	0x5e, 0x10, 0xc9, 0x61, 0xdb, 0x64, 0x02, 0x53, 0xcd, 0xa7, 0x90, 0xd2, 0x7c, 0x26, 0x09, 0x09,
	0xb3, 0x09, 0xa9, 0xfe, 0x23, 0x2a, 0xb3, 0x0e, 0xee, 0x3b, 0x9e, 0x49, 0xbb, 0xa5, 0xe3, 0xc6,
	0x4a, 0x4c, 0x76, 0xc2, 0x1e, 0x2a, 0x8b, 0xd4, 0xd9, 0x1e, 0x9a, 0x11, 0xd8, 0xc9, 0x25, 0x91,
	0x5d, 0xdc, 0x43, 0x97, 0x53, 0xdc, 0x08, 0xa0, 0x5e, 0x99, 0x0b, 0x75, 0x6e, 0x31, 0xd4, 0xf9,
	0x44, 0xa8, 0xdf, 0x44, 0x87, 0x9e, 0x29, 0xda, 0xd5, 0xd4, 0xa2, 0xed, 0xc3, 0x95, 0xd9, 0x74,
	0x26, 0x2e, 0xba, 0x03, 0x39, 0x8f, 0xc1, 0x40, 0x52, 0x6a, 0x83, 0x83, 0xd4, 0x09, 0xa5, 0x68,
	0x96, 0xf9, 0x8e, 0x6f, 0x0c, 0x63, 0xd0, 0x70, 0x92, 0xfa, 0x23, 0x19, 0xd0, 0x01, 0x8b, 0xc1,
	0x9b, 0xef, 0xa3, 0x17, 0x9b, 0xcc, 0xff, 0x93, 0x96, 0x3a, 0x13, 0xf6, 0x5c, 0x6a, 0xd8, 0x6b,
	0x70, 0x23, 0x6c, 0x4f, 0xb3, 0x81, 0xe1, 0x7b, 0x0a, 0x44, 0x6e, 0x71, 0x08, 0x0a, 0x9d, 0x42,
	0xe8, 0x11, 0x51, 0xbf, 0x0d, 0x3b, 0xf3, 0xde, 0x27, 0x2e, 0xaa, 0x26, 0x35, 0xb8, 0xad, 0x08,
	0xc4, 0x84, 0xb7, 0xc4, 0x36, 0xf7, 0xbb, 0x0c, 0x20, 0xce, 0xac, 0xbb, 0x2e, 0x36, 0x86, 0x93,
	0x92, 0x9c, 0x6a, 0x72, 0xb2, 0x65, 0xa6, 0x2e, 0x4d, 0x65, 0x58, 0xa6, 0x2a, 0x71, 0x6c, 0xc8,
	0x71, 0xd2, 0xf4, 0xf8, 0xc9, 0xa6, 0x8c, 0x9f, 0x3d, 0xd8, 0x08, 0xa0, 0xe3, 0x6d, 0x2a, 0xc0,
	0x26, 0x13, 0x88, 0x06, 0x93, 0xa5, 0x41, 0x79, 0x75, 0x1f, 0xed, 0x46, 0xa3, 0x86, 0xf6, 0xab,
	0x60, 0x97, 0x0a, 0x65, 0x83, 0x6c, 0x6a, 0xd9, 0x14, 0x6f, 0x5a, 0x88, 0x1e, 0x36, 0x7c, 0x4c,
	0xa5, 0x72, 0x82, 0x54, 0x9e, 0x93, 0xb9, 0xc8, 0x98, 0xad, 0x27, 0x54, 0x24, 0x2f, 0x8a, 0x70,
	0x32, 0xcf, 0x9a, 0x40, 0x8b, 0x89, 0x49, 0xbc, 0x5a, 0x81, 0x33, 0x0e, 0x30, 0xe9, 0x53, 0xb1,
	0x40, 0x13, 0x13, 0x13, 0x4b, 0x16, 0x38, 0x83, 0x89, 0x29, 0x90, 0x0f, 0xbb, 0x4c, 0xac, 0x60,
	0x23, 0x2a, 0x35, 0x69, 0x64, 0x78, 0x8f, 0xb1, 0x4f, 0x73, 0x7d, 0x4d, 0x4c, 0x64, 0x4e, 0x3e,
	0x34, 0xd5, 0xdf, 0xca, 0x50, 0x8a, 0x0a, 0x5a, 0xc4, 0x6e, 0xfe, 0xbc, 0xa4, 0xf9, 0xc6, 0x80,
	0xe1, 0x4c, 0x99, 0x31, 0x0b, 0x8c, 0xc2, 0xd8, 0xbb, 0x50, 0x14, 0xe0, 0xe2, 0x42, 0x19, 0x26,
	0x74, 0x69, 0x82, 0x16, 0x93, 0xdc, 0x83, 0x8d, 0x20, 0x26, 0x11, 0x62, 0x59, 0x11, 0x31, 0xce,
	0x14, 0x10, 0x0b, 0xa4, 0x03, 0xc4, 0x44, 0x74, 0x83, 0xe0, 0x72, 0xc4, 0x26, 0x33, 0x6e, 0x65,
	0xde, 0x8c, 0xcb, 0x25, 0xce, 0x38, 0x0f, 0x13, 0x57, 0xe7, 0xed, 0x29, 0x2f, 0xec, 0xac, 0x05,
	0x4a, 0x3f, 0x65, 0x2d, 0x6a, 0x0c, 0xd7, 0x53, 0xc2, 0xc6, 0x6b, 0x89, 0xb7, 0xb9, 0xe4, 0x5a,
	0x4a, 0x78, 0x0b, 0xb8, 0x3c, 0x8b, 0x88, 0xd0, 0x19, 0xa5, 0xe9, 0xce, 0xf8, 0x2d, 0x28, 0x75,
	0xd3, 0xd0, 0xfa, 0x8f, 0xbe, 0xaa, 0x6e, 0xc1, 0xf5, 0x6e, 0x9a, 0x43, 0xea, 0x6f, 0x24, 0xd8,
	0x8a, 0x76, 0xef, 0x84, 0x4f, 0xbf, 0xd1, 0x3a, 0x17, 0x73, 0x3f, 0x7b, 0x81, 0xdc, 0x5f, 0x4e,
	0xca, 0x7d, 0xb5, 0x06, 0xdb, 0xe9, 0x16, 0x13, 0x17, 0xed, 0x40, 0x8e, 0xeb, 0xe3, 0x76, 0x87,
	0x10, 0x87, 0x44, 0xf5, 0x81, 0x30, 0xe8, 0x8e, 0x03, 0xa5, 0xd4, 0xd5, 0x0f, 0x60, 0x3d, 0x1a,
	0xfa, 0xb6, 0x31, 0xc2, 0xec, 0xed, 0xa8, 0x69, 0x87, 0xac, 0xb6, 0x31, 0xc2, 0x93, 0x24, 0x93,
	0x67, 0x92, 0x4c, 0xfd, 0x9e, 0x04, 0x97, 0xe2, 0xca, 0xff, 0x1d, 0xcd, 0x2a, 0xc0, 0xd0, 0x20,
	0xbe, 0xee, 0xb8, 0xba, 0x11, 0x57, 0x9f, 0xa7, 0xf4, 0x63, 0xb7, 0xee, 0xa3, 0xf7, 0x60, 0x2d,
	0x94, 0x61, 0x03, 0x51, 0x0c, 0x36, 0x70, 0x29, 0x3a, 0x10, 0xd5, 0x33, 0xb8, 0x9a, 0xe0, 0x28,
	0xcb, 0xe2, 0x89, 0x3d, 0x42, 0x46, 0xbd, 0x35, 0x95, 0x51, 0xd1, 0x3b, 0x91, 0x89, 0x2c, 0x9f,
	0x7e, 0x9e, 0x81, 0xcd, 0x08, 0x80, 0x68, 0x84, 0x7f, 0x75, 0x9c, 0xfe, 0xbf, 0x39, 0x4e, 0x7f,
	0x04, 0xd7, 0xd9, 0x4a, 0x30, 0x03, 0x12, 0xcd, 0xf2, 0x7d, 0xc8, 0x0a, 0x90, 0x97, 0x23, 0xc8,
	0x67, 0x85, 0x99, 0x9c, 0xda, 0x82, 0x72, 0x9a, 0x32, 0xe2, 0xa2, 0xf7, 0x61, 0x8d, 0x8c, 0xfb,
	0x7d, 0x4c, 0x88, 0x4e, 0xac, 0xe7, 0x3c, 0xb1, 0x97, 0xc3, 0xa6, 0x1d, 0x70, 0xba, 0xd6, 0x73,
	0xac, 0x7e, 0x21, 0xc3, 0x06, 0xcf, 0xaa, 0xa6, 0xd1, 0x7f, 0x84, 0x0f, 0x0c, 0xdf, 0x58, 0xb0,
	0xf5, 0xc5, 0x76, 0x35, 0xf9, 0x22, 0xbb, 0x5a, 0x66, 0x21, 0xc0, 0x49, 0xbb, 0xfe, 0x0d, 0xba,
	0xdf, 0x0e, 0xf4, 0x71, 0xb0, 0xe7, 0xaf, 0x4f, 0xd8, 0x83, 0x33, 0xcb, 0xe4, 0x33, 0x63, 0xa0,
	0xf3, 0x6c, 0x8c, 0x2d, 0x7b, 0x05, 0x0f, 0x0f, 0xf8, 0xf2, 0x44, 0x6d, 0xa5, 0x42, 0xfc, 0xe2,
	0x20, 0xb6, 0xf0, 0x7b, 0x78, 0x70, 0x42, 0xa9, 0xd3, 0x6b, 0x4e, 0xfe, 0xa2, 0xa7, 0xec, 0x42,
	0x1a, 0xcc, 0xb7, 0xff, 0x24, 0xc1, 0x6a, 0xa3, 0xde, 0xd6, 0x8f, 0x4f, 0xf4, 0xd3, 0xf3, 0x93,
	0x16, 0xba, 0x0c, 0x1b, 0xc1, 0x23, 0xfd, 0x39, 0xeb, 0xb6, 0x3a, 0x45, 0x09, 0x5d, 0x85, 0x4d,
	0x81, 0x78, 0xd0, 0xfa, 0xe4, 0xb0, 0xd9, 0x2a, 0xca, 0xa8, 0x04, 0x57, 0x04, 0x72, 0xf3, 0xe8,
	0xb0, 0xd5, 0x3e, 0xd5, 0x0f, 0x4f, 0x8a, 0x59, 0x74, 0x05, 0x8a, 0x02, 0xe7, 0xe4, 0xfe, 0x71,
	0xbb, 0x55, 0xcc, 0xa3, 0x6b, 0x91, 0x9a, 0xb3, 0x76, 0xa4, 0xfd, 0x05, 0xd5, 0x73, 0x39, 0x46,
	0x0f, 0x3e, 0xf0, 0x22, 0x8b, 0xb6, 0xe0, 0x5a, 0x8c, 0x33, 0xf9, 0xc6, 0x8b, 0x3c, 0x7a, 0x0b,
	0x50, 0x8c, 0xc9, 0x3f, 0xf3, 0xa2, 0x78, 0xfb, 0x31, 0x73, 0xa9, 0xdd, 0x3a, 0xe0, 0x2e, 0x15,
	0x61, 0x8d, 0xb2, 0xdb, 0xc7, 0x9c, 0x58, 0x5c, 0x42, 0x1b, 0xdc, 0xe7, 0xc6, 0x79, 0xe8, 0xe0,
	0x26, 0xac, 0x07, 0x84, 0xc8, 0xb9, 0xc0, 0x85, 0xc6, 0xb9, 0xf0, 0xd1, 0x4c, 0xa8, 0xab, 0x71,
	0x1e, 0x7c, 0x2d, 0x7b, 0xfb, 0x1e, 0x00, 0xa5, 0x74, 0x4f, 0xeb, 0xa7, 0x67, 0xdd, 0x50, 0x51,
	0xf7, 0x54, 0x6f, 0x1f, 0x77, 0xbe, 0x59, 0x3f, 0x2a, 0x2e, 0x09, 0xa4, 0xe0, 0xfb, 0xd2, 0xed,
	0xe7, 0xb0, 0xda, 0x69, 0xd5, 0xbb, 0xc7, 0x6d, 0xbd, 0x79, 0x7c, 0xd0, 0x42, 0xd7, 0x00, 0x09,
	0x8f, 0x93, 0x37, 0x2f, 0xc3, 0x86, 0x48, 0x6f, 0xd4, 0xdb, 0x45, 0x09, 0xdd, 0x84, 0x2d, 0x91,
	0x58, 0x6f, 0x36, 0x8f, 0xcf, 0xda, 0xa7, 0x7a, 0xa7, 0xd5, 0x3c, 0x6f, 0x1e, 0x51, 0xc3, 0x6f,
	0xc0, 0xf5, 0x24, 0x81, 0xc6, 0x51, 0xbd, 0xf9, 0x51, 0x31, 0x7b, 0xfb, 0x57, 0x12, 0x6c, 0x8a,
	0x33, 0xaf, 0xcb, 0xe6, 0xea, 0x36, 0x94, 0x82, 0x90, 0xd5, 0x4f, 0x4e, 0x5a, 0xf5, 0x23, 0xe6,
	0x0f, 0x35, 0xa5, 0xdd, 0x2a, 0x2e, 0xa5, 0x71, 0x4f, 0x5a, 0xed, 0x83, 0xa2, 0x94, 0xca, 0xad,
	0x77, 0xbb, 0x45, 0x39, 0x8d, 0x7b, 0xd0, 0x6a, 0x9f, 0x17, 0xb3, 0xd4, 0xd8, 0x24, 0x2e, 0x37,
	0x76, 0xf9, 0xde, 0xdf, 0xd7, 0x21, 0xd7, 0x30, 0xec, 0x33, 0x82, 0x3d, 0xf4, 0x4f, 0x29, 0x69,
	0x62, 0x6c, 0xcd, 0xf6, 0x9e, 0xa8, 0x4d, 0x95, 0xb7, 0xd3, 0x99, 0xc4, 0x55, 0x7f, 0x2d, 0xbd,
	0x78, 0xf9, 0x3a, 0x23, 0xfd, 0xe0, 0xe5, 0xeb, 0xcc, 0xa6, 0xa3, 0x8d, 0x35, 0x53, 0xf3, 0x35,
	0x4f, 0x33, 0xb4, 0xa7, 0xda, 0x51, 0x4f, 0xfb, 0xc9, 0xcb, 0xd7, 0x99, 0xcf, 0xa4, 0x8a, 0xa3,
	0x54, 0x1d, 0x77, 0xef, 0x6e, 0xa5, 0x67, 0xd8, 0x7b, 0xf7, 0x2a, 0x63, 0xbb, 0x67, 0xd8, 0x35,
	0xa5, 0x32, 0x56, 0xaa, 0x63, 0xcb, 0xac, 0x29, 0x9f, 0x56, 0x4c, 0xa5, 0x1a, 0x4d, 0x9e, 0xda,
	0x03, 0xe5, 0xd3, 0x8a, 0xaf, 0x54, 0xc3, 0xa6, 0xa1, 0xf8, 0xd6, 0x08, 0x33, 0xa2, 0x47, 0x89,
	0xb4, 0xe8, 0xd8, 0x93, 0x41, 0x75, 0xf2, 0x9e, 0xcf, 0x9e, 0x9f, 0x2a, 0xd5, 0xa8, 0xeb, 0x33,
	0xc2, 0xd1, 0x9e, 0x62, 0x3b, 0xca, 0xd0, 0x79, 0x48, 0x1f, 0x7a, 0x4a, 0xcf, 0xa0, 0x6b, 0xef,
	0x03, 0xf4, 0x53, 0x09, 0x36, 0xa6, 0x6e, 0xc4, 0x04, 0xf7, 0x67, 0xef, 0x38, 0x05, 0xf7, 0x13,
	0x2e, 0xd2, 0xd4, 0xfb, 0xd4, 0x7b, 0x99, 0x7a, 0x9f, 0xa7, 0xbe, 0x3f, 0xd2, 0x7c, 0xe6, 0xf4,
	0xdd, 0xc8, 0xbb, 0xb8, 0x73, 0x4a, 0xe5, 0x91, 0x52, 0xe5, 0xb3, 0x52, 0xb1, 0xdc, 0x9a, 0x42,
	0x3d, 0x65, 0xad, 0xaa, 0x86, 0x3e, 0x93, 0xa0, 0x94, 0x76, 0x3b, 0x86, 0xde, 0x11, 0xf6, 0x81,
	0xd4, 0xab, 0xb7, 0xf2, 0xbb, 0x17, 0x90, 0x22, 0xae, 0x7a, 0x8b, 0xda, 0x9c, 0xa1, 0x36, 0xcb,
	0x63, 0x66, 0x2d, 0x0a, 0xac, 0xbd, 0xbb, 0x37, 0xb6, 0xcc, 0x7b, 0x7b, 0xfb, 0xfb, 0xfb, 0x35,
	0xf4, 0xa5, 0x04, 0xc5, 0xe9, 0x4b, 0x08, 0x94, 0x10, 0x8b, 0xc9, 0x75, 0x5b, 0xf9, 0xc6, 0x1c,
	0x2e, 0x71, 0xd5, 0xef, 0xb2, 0x4c, 0x59, 0xa1, 0xdf, 0x2d, 0xd2, 0x58, 0x39, 0xda, 0x50, 0xeb,
	0x69, 0x58, 0x73, 0x35, 0x8f, 0x59, 0x31, 0x48, 0x8d, 0x19, 0xcd, 0x1f, 0x76, 0xa4, 0xa8, 0x29,
	0x95, 0xa1, 0x52, 0x65, 0xcb, 0x5d, 0x4d, 0xa9, 0xf4, 0x94, 0xea, 0xe4, 0x9e, 0xac, 0xa6, 0x54,
	0xb0, 0x52, 0x0d, 0xaf, 0xc4, 0x6a, 0x4a, 0xc5, 0x15, 0x12, 0x44, 0x11, 0x92, 0x07, 0xfd, 0x4c,
	0x0a, 0xe6, 0x68, 0xe2, 0x39, 0x1d, 0xbd, 0x37, 0x13, 0xc4, 0xc4, 0xcb, 0x80, 0xf2, 0xfb, 0x17,
	0x92, 0x23, 0xae, 0x5a, 0xa1, 0x6e, 0xe7, 0x58, 0xb8, 0x4d, 0xe6, 0xe8, 0x76, 0xcc, 0xbf, 0xbd,
	0xc9, 0x3f, 0x16, 0xf8, 0x3f, 0x4a, 0xc2, 0xae, 0x18, 0x3b, 0xe8, 0xbf, 0x3d, 0x1b, 0xdf, 0xa9,
	0x33, 0x42, 0x59, 0x5d, 0x24, 0x42, 0x5c, 0xd5, 0xa1, 0xf6, 0xe4, 0xa9, 0x3d, 0x97, 0xc6, 0x1a,
	0xd1, 0x3c, 0x06, 0x02, 0x05, 0x83, 0xda, 0xd6, 0x99, 0x80, 0x40, 0x94, 0x2a, 0x3b, 0x2e, 0x88,
	0xd1, 0xa3, 0x83, 0x51, 0x88, 0x7c, 0xdd, 0x8f, 0xc2, 0xce, 0xfe, 0x26, 0x02, 0x85, 0xfe, 0x20,
	0xc1, 0xd5, 0xee, 0x02, 0x8f, 0xba, 0x8b, 0x3d, 0x4a, 0x3f, 0x39, 0x0d, 0xa9, 0x47, 0x05, 0xea,
	0xd1, 0x1a, 0xf7, 0x68, 0xa4, 0xd9, 0x9a, 0xc3, 0xfc, 0xf9, 0xf8, 0x42, 0xfe, 0x8c, 0x94, 0xea,
	0xe4, 0x6a, 0xa0, 0xa6, 0x54, 0x6c, 0xa5, 0x1a, 0x9c, 0x83, 0x08, 0xee, 0x07, 0x7e, 0xb9, 0xd8,
	0xf0, 0x68, 0x2e, 0xa1, 0xdf, 0x4b, 0x50, 0x4a, 0x3b, 0xf5, 0x08, 0x85, 0x3a, 0xe7, 0x28, 0x27,
	0x14, 0xea, 0xbc, 0xe3, 0x93, 0xda, 0xa1, 0x7e, 0x01, 0xf5, 0x0b, 0x2c, 0x6d, 0xac, 0xd9, 0x1a,
	0x09, 0xbc, 0xfa, 0x46, 0xc5, 0x52, 0xaa, 0xcc, 0xa9, 0x24, 0xef, 0xe6, 0x5a, 0xdf, 0x87, 0xcd,
	0x99, 0x93, 0x08, 0x4a, 0xa8, 0x5c, 0xe1, 0x38, 0x56, 0xde, 0x99, 0xc7, 0x26, 0xae, 0xba, 0x41,
	0xed, 0x5c, 0xa5, 0x76, 0x2e, 0x51, 0xeb, 0x96, 0x90, 0x07, 0xd7, 0x92, 0x37, 0x55, 0xa4, 0xc6,
	0xab, 0x26, 0x69, 0x2f, 0x2e, 0xdf, 0x5a, 0x28, 0x13, 0x7e, 0x73, 0x6d, 0xf2, 0xcd, 0xf2, 0xca,
	0xf7, 0x5f, 0xbe, 0xce, 0xfc, 0x79, 0xdc, 0x28, 0x7e, 0xfe, 0x6a, 0x47, 0xfa, 0xe2, 0xd5, 0x8e,
	0xf4, 0xd7, 0x57, 0x3b, 0xd2, 0x8f, 0xff, 0xb6, 0xb3, 0xf4, 0xaf, 0x00, 0x00, 0x00, 0xff, 0xff,
	0xdf, 0x8b, 0x67, 0x3d, 0xc3, 0x1c, 0x00, 0x00,
}
