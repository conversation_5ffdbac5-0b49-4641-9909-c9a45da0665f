// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rhythm/ai-rapper.proto

package airapper // import "golang.52tt.com/protocol/services/rhythm/airapper"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// tab状态枚举
type TabStatusInfo int32

const (
	TabStatusInfo_Configuring TabStatusInfo = 0
	TabStatusInfo_Online      TabStatusInfo = 1
	TabStatusInfo_Offline     TabStatusInfo = 2
)

var TabStatusInfo_name = map[int32]string{
	0: "Configuring",
	1: "Online",
	2: "Offline",
}
var TabStatusInfo_value = map[string]int32{
	"Configuring": 0,
	"Online":      1,
	"Offline":     2,
}

func (x TabStatusInfo) String() string {
	return proto.EnumName(TabStatusInfo_name, int32(x))
}
func (TabStatusInfo) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{0}
}

// 文件是否存在枚举
type FileExist int32

const (
	FileExist_NoneSense        FileExist = 0
	FileExist_OnlyNotExistFile FileExist = 1
	FileExist_OnlyExistFile    FileExist = 2
)

var FileExist_name = map[int32]string{
	0: "NoneSense",
	1: "OnlyNotExistFile",
	2: "OnlyExistFile",
}
var FileExist_value = map[string]int32{
	"NoneSense":        0,
	"OnlyNotExistFile": 1,
	"OnlyExistFile":    2,
}

func (x FileExist) String() string {
	return proto.EnumName(FileExist_name, int32(x))
}
func (FileExist) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{1}
}

// 生效状态枚举
type StatusInfo int32

const (
	StatusInfo_NoneStatus       StatusInfo = 0
	StatusInfo_Effective        StatusInfo = 1
	StatusInfo_LoseEffective    StatusInfo = 2
	StatusInfo_WaitingEffective StatusInfo = 3
	StatusInfo_TrainOver        StatusInfo = 4
	StatusInfo_TrainFail        StatusInfo = 5
	StatusInfo_SongConfiguring  StatusInfo = 6
)

var StatusInfo_name = map[int32]string{
	0: "NoneStatus",
	1: "Effective",
	2: "LoseEffective",
	3: "WaitingEffective",
	4: "TrainOver",
	5: "TrainFail",
	6: "SongConfiguring",
}
var StatusInfo_value = map[string]int32{
	"NoneStatus":       0,
	"Effective":        1,
	"LoseEffective":    2,
	"WaitingEffective": 3,
	"TrainOver":        4,
	"TrainFail":        5,
	"SongConfiguring":  6,
}

func (x StatusInfo) String() string {
	return proto.EnumName(StatusInfo_name, int32(x))
}
func (StatusInfo) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{2}
}

// 前置检查歌词、模板文件状态
type CheckType int32

const (
	CheckType_UnknownCheckType       CheckType = 0
	CheckType_SongFileCheck          CheckType = 1
	CheckType_SongTemplateCheck      CheckType = 2
	CheckType_NewSongFileCheck       CheckType = 3
	CheckType_SongComposedVideoCheck CheckType = 4
)

var CheckType_name = map[int32]string{
	0: "UnknownCheckType",
	1: "SongFileCheck",
	2: "SongTemplateCheck",
	3: "NewSongFileCheck",
	4: "SongComposedVideoCheck",
}
var CheckType_value = map[string]int32{
	"UnknownCheckType":       0,
	"SongFileCheck":          1,
	"SongTemplateCheck":      2,
	"NewSongFileCheck":       3,
	"SongComposedVideoCheck": 4,
}

func (x CheckType) String() string {
	return proto.EnumName(CheckType_name, int32(x))
}
func (CheckType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{3}
}

// 3.复制、移动与强插的关系
// - 从推荐tab复制或移动到其他tab，默认排在其他Tab最后
// - 带强插字段的歌曲移到其他Tab,强插字段直接消失(若再移回也不恢复)
// - 从其他tab复制或移动到推荐tab，按排序分数参与排序
type CopyOrMoveType int32

const (
	CopyOrMoveType_UnknownCopyMoveType CopyOrMoveType = 0
	CopyOrMoveType_CopyType            CopyOrMoveType = 1
	CopyOrMoveType_MoveType            CopyOrMoveType = 2
)

var CopyOrMoveType_name = map[int32]string{
	0: "UnknownCopyMoveType",
	1: "CopyType",
	2: "MoveType",
}
var CopyOrMoveType_value = map[string]int32{
	"UnknownCopyMoveType": 0,
	"CopyType":            1,
	"MoveType":            2,
}

func (x CopyOrMoveType) String() string {
	return proto.EnumName(CopyOrMoveType_name, int32(x))
}
func (CopyOrMoveType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{4}
}

// 上报类型枚举
type AggregationChoose int32

const (
	AggregationChoose_EXposure AggregationChoose = 0
	AggregationChoose_Compose  AggregationChoose = 1
	AggregationChoose_Share    AggregationChoose = 2
)

var AggregationChoose_name = map[int32]string{
	0: "EXposure",
	1: "Compose",
	2: "Share",
}
var AggregationChoose_value = map[string]int32{
	"EXposure": 0,
	"Compose":  1,
	"Share":    2,
}

func (x AggregationChoose) String() string {
	return proto.EnumName(AggregationChoose_name, int32(x))
}
func (AggregationChoose) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{5}
}

// AI 外语
type AIType int32

const (
	AIType_AiRapperType          AIType = 0
	AIType_AiForeignLanguageType AIType = 1
)

var AIType_name = map[int32]string{
	0: "AiRapperType",
	1: "AiForeignLanguageType",
}
var AIType_value = map[string]int32{
	"AiRapperType":          0,
	"AiForeignLanguageType": 1,
}

func (x AIType) String() string {
	return proto.EnumName(AIType_name, int32(x))
}
func (AIType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{6}
}

// ------------------测试接口 ------------------------------------------------------------------
type GetBackgroundVideoUrlReq struct {
	VideoMd5             string   `protobuf:"bytes,1,opt,name=video_md5,json=videoMd5,proto3" json:"video_md5,omitempty"`
	SongId               string   `protobuf:"bytes,2,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBackgroundVideoUrlReq) Reset()         { *m = GetBackgroundVideoUrlReq{} }
func (m *GetBackgroundVideoUrlReq) String() string { return proto.CompactTextString(m) }
func (*GetBackgroundVideoUrlReq) ProtoMessage()    {}
func (*GetBackgroundVideoUrlReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{0}
}
func (m *GetBackgroundVideoUrlReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBackgroundVideoUrlReq.Unmarshal(m, b)
}
func (m *GetBackgroundVideoUrlReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBackgroundVideoUrlReq.Marshal(b, m, deterministic)
}
func (dst *GetBackgroundVideoUrlReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBackgroundVideoUrlReq.Merge(dst, src)
}
func (m *GetBackgroundVideoUrlReq) XXX_Size() int {
	return xxx_messageInfo_GetBackgroundVideoUrlReq.Size(m)
}
func (m *GetBackgroundVideoUrlReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBackgroundVideoUrlReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBackgroundVideoUrlReq proto.InternalMessageInfo

func (m *GetBackgroundVideoUrlReq) GetVideoMd5() string {
	if m != nil {
		return m.VideoMd5
	}
	return ""
}

func (m *GetBackgroundVideoUrlReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

type GetBackgroundVideoUrlResp struct {
	Info                 []*BackgroundVideoInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetBackgroundVideoUrlResp) Reset()         { *m = GetBackgroundVideoUrlResp{} }
func (m *GetBackgroundVideoUrlResp) String() string { return proto.CompactTextString(m) }
func (*GetBackgroundVideoUrlResp) ProtoMessage()    {}
func (*GetBackgroundVideoUrlResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{1}
}
func (m *GetBackgroundVideoUrlResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBackgroundVideoUrlResp.Unmarshal(m, b)
}
func (m *GetBackgroundVideoUrlResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBackgroundVideoUrlResp.Marshal(b, m, deterministic)
}
func (dst *GetBackgroundVideoUrlResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBackgroundVideoUrlResp.Merge(dst, src)
}
func (m *GetBackgroundVideoUrlResp) XXX_Size() int {
	return xxx_messageInfo_GetBackgroundVideoUrlResp.Size(m)
}
func (m *GetBackgroundVideoUrlResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBackgroundVideoUrlResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBackgroundVideoUrlResp proto.InternalMessageInfo

func (m *GetBackgroundVideoUrlResp) GetInfo() []*BackgroundVideoInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type BackgroundVideoInfo struct {
	VideoUrl             string   `protobuf:"bytes,1,opt,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty"`
	VideoMd5             string   `protobuf:"bytes,2,opt,name=video_md5,json=videoMd5,proto3" json:"video_md5,omitempty"`
	ShareCoverPicUrl     string   `protobuf:"bytes,3,opt,name=share_cover_pic_url,json=shareCoverPicUrl,proto3" json:"share_cover_pic_url,omitempty"`
	OriginVideoUrl       string   `protobuf:"bytes,4,opt,name=origin_video_url,json=originVideoUrl,proto3" json:"origin_video_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BackgroundVideoInfo) Reset()         { *m = BackgroundVideoInfo{} }
func (m *BackgroundVideoInfo) String() string { return proto.CompactTextString(m) }
func (*BackgroundVideoInfo) ProtoMessage()    {}
func (*BackgroundVideoInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{2}
}
func (m *BackgroundVideoInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BackgroundVideoInfo.Unmarshal(m, b)
}
func (m *BackgroundVideoInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BackgroundVideoInfo.Marshal(b, m, deterministic)
}
func (dst *BackgroundVideoInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BackgroundVideoInfo.Merge(dst, src)
}
func (m *BackgroundVideoInfo) XXX_Size() int {
	return xxx_messageInfo_BackgroundVideoInfo.Size(m)
}
func (m *BackgroundVideoInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BackgroundVideoInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BackgroundVideoInfo proto.InternalMessageInfo

func (m *BackgroundVideoInfo) GetVideoUrl() string {
	if m != nil {
		return m.VideoUrl
	}
	return ""
}

func (m *BackgroundVideoInfo) GetVideoMd5() string {
	if m != nil {
		return m.VideoMd5
	}
	return ""
}

func (m *BackgroundVideoInfo) GetShareCoverPicUrl() string {
	if m != nil {
		return m.ShareCoverPicUrl
	}
	return ""
}

func (m *BackgroundVideoInfo) GetOriginVideoUrl() string {
	if m != nil {
		return m.OriginVideoUrl
	}
	return ""
}

type GetSongAggScoreReq struct {
	SongName             string   `protobuf:"bytes,1,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	SongRapper           string   `protobuf:"bytes,2,opt,name=song_rapper,json=songRapper,proto3" json:"song_rapper,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSongAggScoreReq) Reset()         { *m = GetSongAggScoreReq{} }
func (m *GetSongAggScoreReq) String() string { return proto.CompactTextString(m) }
func (*GetSongAggScoreReq) ProtoMessage()    {}
func (*GetSongAggScoreReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{3}
}
func (m *GetSongAggScoreReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSongAggScoreReq.Unmarshal(m, b)
}
func (m *GetSongAggScoreReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSongAggScoreReq.Marshal(b, m, deterministic)
}
func (dst *GetSongAggScoreReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSongAggScoreReq.Merge(dst, src)
}
func (m *GetSongAggScoreReq) XXX_Size() int {
	return xxx_messageInfo_GetSongAggScoreReq.Size(m)
}
func (m *GetSongAggScoreReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSongAggScoreReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSongAggScoreReq proto.InternalMessageInfo

func (m *GetSongAggScoreReq) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

func (m *GetSongAggScoreReq) GetSongRapper() string {
	if m != nil {
		return m.SongRapper
	}
	return ""
}

type GetSongAggScoreInfo struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Score                float32  `protobuf:"fixed32,2,opt,name=score,proto3" json:"score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSongAggScoreInfo) Reset()         { *m = GetSongAggScoreInfo{} }
func (m *GetSongAggScoreInfo) String() string { return proto.CompactTextString(m) }
func (*GetSongAggScoreInfo) ProtoMessage()    {}
func (*GetSongAggScoreInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{4}
}
func (m *GetSongAggScoreInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSongAggScoreInfo.Unmarshal(m, b)
}
func (m *GetSongAggScoreInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSongAggScoreInfo.Marshal(b, m, deterministic)
}
func (dst *GetSongAggScoreInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSongAggScoreInfo.Merge(dst, src)
}
func (m *GetSongAggScoreInfo) XXX_Size() int {
	return xxx_messageInfo_GetSongAggScoreInfo.Size(m)
}
func (m *GetSongAggScoreInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSongAggScoreInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GetSongAggScoreInfo proto.InternalMessageInfo

func (m *GetSongAggScoreInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetSongAggScoreInfo) GetScore() float32 {
	if m != nil {
		return m.Score
	}
	return 0
}

type GetSongAggScoreResp struct {
	Info                 []*GetSongAggScoreInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetSongAggScoreResp) Reset()         { *m = GetSongAggScoreResp{} }
func (m *GetSongAggScoreResp) String() string { return proto.CompactTextString(m) }
func (*GetSongAggScoreResp) ProtoMessage()    {}
func (*GetSongAggScoreResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{5}
}
func (m *GetSongAggScoreResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSongAggScoreResp.Unmarshal(m, b)
}
func (m *GetSongAggScoreResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSongAggScoreResp.Marshal(b, m, deterministic)
}
func (dst *GetSongAggScoreResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSongAggScoreResp.Merge(dst, src)
}
func (m *GetSongAggScoreResp) XXX_Size() int {
	return xxx_messageInfo_GetSongAggScoreResp.Size(m)
}
func (m *GetSongAggScoreResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSongAggScoreResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSongAggScoreResp proto.InternalMessageInfo

func (m *GetSongAggScoreResp) GetInfo() []*GetSongAggScoreInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type GetRecommendTabAllNoInsertSongScoreListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecommendTabAllNoInsertSongScoreListReq) Reset() {
	*m = GetRecommendTabAllNoInsertSongScoreListReq{}
}
func (m *GetRecommendTabAllNoInsertSongScoreListReq) String() string {
	return proto.CompactTextString(m)
}
func (*GetRecommendTabAllNoInsertSongScoreListReq) ProtoMessage() {}
func (*GetRecommendTabAllNoInsertSongScoreListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{6}
}
func (m *GetRecommendTabAllNoInsertSongScoreListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendTabAllNoInsertSongScoreListReq.Unmarshal(m, b)
}
func (m *GetRecommendTabAllNoInsertSongScoreListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendTabAllNoInsertSongScoreListReq.Marshal(b, m, deterministic)
}
func (dst *GetRecommendTabAllNoInsertSongScoreListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendTabAllNoInsertSongScoreListReq.Merge(dst, src)
}
func (m *GetRecommendTabAllNoInsertSongScoreListReq) XXX_Size() int {
	return xxx_messageInfo_GetRecommendTabAllNoInsertSongScoreListReq.Size(m)
}
func (m *GetRecommendTabAllNoInsertSongScoreListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendTabAllNoInsertSongScoreListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendTabAllNoInsertSongScoreListReq proto.InternalMessageInfo

type GetRecommendTabAllNoInsertSongScoreListInfo struct {
	SongName             string   `protobuf:"bytes,1,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	SongRapper           string   `protobuf:"bytes,2,opt,name=song_rapper,json=songRapper,proto3" json:"song_rapper,omitempty"`
	Score                float32  `protobuf:"fixed32,3,opt,name=score,proto3" json:"score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecommendTabAllNoInsertSongScoreListInfo) Reset() {
	*m = GetRecommendTabAllNoInsertSongScoreListInfo{}
}
func (m *GetRecommendTabAllNoInsertSongScoreListInfo) String() string {
	return proto.CompactTextString(m)
}
func (*GetRecommendTabAllNoInsertSongScoreListInfo) ProtoMessage() {}
func (*GetRecommendTabAllNoInsertSongScoreListInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{7}
}
func (m *GetRecommendTabAllNoInsertSongScoreListInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendTabAllNoInsertSongScoreListInfo.Unmarshal(m, b)
}
func (m *GetRecommendTabAllNoInsertSongScoreListInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendTabAllNoInsertSongScoreListInfo.Marshal(b, m, deterministic)
}
func (dst *GetRecommendTabAllNoInsertSongScoreListInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendTabAllNoInsertSongScoreListInfo.Merge(dst, src)
}
func (m *GetRecommendTabAllNoInsertSongScoreListInfo) XXX_Size() int {
	return xxx_messageInfo_GetRecommendTabAllNoInsertSongScoreListInfo.Size(m)
}
func (m *GetRecommendTabAllNoInsertSongScoreListInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendTabAllNoInsertSongScoreListInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendTabAllNoInsertSongScoreListInfo proto.InternalMessageInfo

func (m *GetRecommendTabAllNoInsertSongScoreListInfo) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

func (m *GetRecommendTabAllNoInsertSongScoreListInfo) GetSongRapper() string {
	if m != nil {
		return m.SongRapper
	}
	return ""
}

func (m *GetRecommendTabAllNoInsertSongScoreListInfo) GetScore() float32 {
	if m != nil {
		return m.Score
	}
	return 0
}

type GetRecommendTabAllNoInsertSongScoreListResp struct {
	Info                 []*GetRecommendTabAllNoInsertSongScoreListInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                       `json:"-"`
	XXX_unrecognized     []byte                                         `json:"-"`
	XXX_sizecache        int32                                          `json:"-"`
}

func (m *GetRecommendTabAllNoInsertSongScoreListResp) Reset() {
	*m = GetRecommendTabAllNoInsertSongScoreListResp{}
}
func (m *GetRecommendTabAllNoInsertSongScoreListResp) String() string {
	return proto.CompactTextString(m)
}
func (*GetRecommendTabAllNoInsertSongScoreListResp) ProtoMessage() {}
func (*GetRecommendTabAllNoInsertSongScoreListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{8}
}
func (m *GetRecommendTabAllNoInsertSongScoreListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendTabAllNoInsertSongScoreListResp.Unmarshal(m, b)
}
func (m *GetRecommendTabAllNoInsertSongScoreListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendTabAllNoInsertSongScoreListResp.Marshal(b, m, deterministic)
}
func (dst *GetRecommendTabAllNoInsertSongScoreListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendTabAllNoInsertSongScoreListResp.Merge(dst, src)
}
func (m *GetRecommendTabAllNoInsertSongScoreListResp) XXX_Size() int {
	return xxx_messageInfo_GetRecommendTabAllNoInsertSongScoreListResp.Size(m)
}
func (m *GetRecommendTabAllNoInsertSongScoreListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendTabAllNoInsertSongScoreListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendTabAllNoInsertSongScoreListResp proto.InternalMessageInfo

func (m *GetRecommendTabAllNoInsertSongScoreListResp) GetInfo() []*GetRecommendTabAllNoInsertSongScoreListInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type GetUserComposedAllSongListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserComposedAllSongListReq) Reset()         { *m = GetUserComposedAllSongListReq{} }
func (m *GetUserComposedAllSongListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserComposedAllSongListReq) ProtoMessage()    {}
func (*GetUserComposedAllSongListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{9}
}
func (m *GetUserComposedAllSongListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserComposedAllSongListReq.Unmarshal(m, b)
}
func (m *GetUserComposedAllSongListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserComposedAllSongListReq.Marshal(b, m, deterministic)
}
func (dst *GetUserComposedAllSongListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserComposedAllSongListReq.Merge(dst, src)
}
func (m *GetUserComposedAllSongListReq) XXX_Size() int {
	return xxx_messageInfo_GetUserComposedAllSongListReq.Size(m)
}
func (m *GetUserComposedAllSongListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserComposedAllSongListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserComposedAllSongListReq proto.InternalMessageInfo

func (m *GetUserComposedAllSongListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserComposedAllSongListResp struct {
	SongRapper           []string `protobuf:"bytes,1,rep,name=song_rapper,json=songRapper,proto3" json:"song_rapper,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserComposedAllSongListResp) Reset()         { *m = GetUserComposedAllSongListResp{} }
func (m *GetUserComposedAllSongListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserComposedAllSongListResp) ProtoMessage()    {}
func (*GetUserComposedAllSongListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{10}
}
func (m *GetUserComposedAllSongListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserComposedAllSongListResp.Unmarshal(m, b)
}
func (m *GetUserComposedAllSongListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserComposedAllSongListResp.Marshal(b, m, deterministic)
}
func (dst *GetUserComposedAllSongListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserComposedAllSongListResp.Merge(dst, src)
}
func (m *GetUserComposedAllSongListResp) XXX_Size() int {
	return xxx_messageInfo_GetUserComposedAllSongListResp.Size(m)
}
func (m *GetUserComposedAllSongListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserComposedAllSongListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserComposedAllSongListResp proto.InternalMessageInfo

func (m *GetUserComposedAllSongListResp) GetSongRapper() []string {
	if m != nil {
		return m.SongRapper
	}
	return nil
}

// 设置声音水印
type SetVoiceWatermarkReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	StageName            string   `protobuf:"bytes,2,opt,name=stage_name,json=stageName,proto3" json:"stage_name,omitempty"`
	VoiceComposedKey     string   `protobuf:"bytes,3,opt,name=voice_composed_key,json=voiceComposedKey,proto3" json:"voice_composed_key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetVoiceWatermarkReq) Reset()         { *m = SetVoiceWatermarkReq{} }
func (m *SetVoiceWatermarkReq) String() string { return proto.CompactTextString(m) }
func (*SetVoiceWatermarkReq) ProtoMessage()    {}
func (*SetVoiceWatermarkReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{11}
}
func (m *SetVoiceWatermarkReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetVoiceWatermarkReq.Unmarshal(m, b)
}
func (m *SetVoiceWatermarkReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetVoiceWatermarkReq.Marshal(b, m, deterministic)
}
func (dst *SetVoiceWatermarkReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetVoiceWatermarkReq.Merge(dst, src)
}
func (m *SetVoiceWatermarkReq) XXX_Size() int {
	return xxx_messageInfo_SetVoiceWatermarkReq.Size(m)
}
func (m *SetVoiceWatermarkReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetVoiceWatermarkReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetVoiceWatermarkReq proto.InternalMessageInfo

func (m *SetVoiceWatermarkReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetVoiceWatermarkReq) GetStageName() string {
	if m != nil {
		return m.StageName
	}
	return ""
}

func (m *SetVoiceWatermarkReq) GetVoiceComposedKey() string {
	if m != nil {
		return m.VoiceComposedKey
	}
	return ""
}

type SetVoiceWatermarkResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetVoiceWatermarkResp) Reset()         { *m = SetVoiceWatermarkResp{} }
func (m *SetVoiceWatermarkResp) String() string { return proto.CompactTextString(m) }
func (*SetVoiceWatermarkResp) ProtoMessage()    {}
func (*SetVoiceWatermarkResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{12}
}
func (m *SetVoiceWatermarkResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetVoiceWatermarkResp.Unmarshal(m, b)
}
func (m *SetVoiceWatermarkResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetVoiceWatermarkResp.Marshal(b, m, deterministic)
}
func (dst *SetVoiceWatermarkResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetVoiceWatermarkResp.Merge(dst, src)
}
func (m *SetVoiceWatermarkResp) XXX_Size() int {
	return xxx_messageInfo_SetVoiceWatermarkResp.Size(m)
}
func (m *SetVoiceWatermarkResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetVoiceWatermarkResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetVoiceWatermarkResp proto.InternalMessageInfo

type GetVoiceWatermarkReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVoiceWatermarkReq) Reset()         { *m = GetVoiceWatermarkReq{} }
func (m *GetVoiceWatermarkReq) String() string { return proto.CompactTextString(m) }
func (*GetVoiceWatermarkReq) ProtoMessage()    {}
func (*GetVoiceWatermarkReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{13}
}
func (m *GetVoiceWatermarkReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVoiceWatermarkReq.Unmarshal(m, b)
}
func (m *GetVoiceWatermarkReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVoiceWatermarkReq.Marshal(b, m, deterministic)
}
func (dst *GetVoiceWatermarkReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVoiceWatermarkReq.Merge(dst, src)
}
func (m *GetVoiceWatermarkReq) XXX_Size() int {
	return xxx_messageInfo_GetVoiceWatermarkReq.Size(m)
}
func (m *GetVoiceWatermarkReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVoiceWatermarkReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetVoiceWatermarkReq proto.InternalMessageInfo

func (m *GetVoiceWatermarkReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetVoiceWatermarkResp struct {
	StageName            string   `protobuf:"bytes,1,opt,name=stage_name,json=stageName,proto3" json:"stage_name,omitempty"`
	VoiceUrl             string   `protobuf:"bytes,2,opt,name=voice_url,json=voiceUrl,proto3" json:"voice_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVoiceWatermarkResp) Reset()         { *m = GetVoiceWatermarkResp{} }
func (m *GetVoiceWatermarkResp) String() string { return proto.CompactTextString(m) }
func (*GetVoiceWatermarkResp) ProtoMessage()    {}
func (*GetVoiceWatermarkResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{14}
}
func (m *GetVoiceWatermarkResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVoiceWatermarkResp.Unmarshal(m, b)
}
func (m *GetVoiceWatermarkResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVoiceWatermarkResp.Marshal(b, m, deterministic)
}
func (dst *GetVoiceWatermarkResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVoiceWatermarkResp.Merge(dst, src)
}
func (m *GetVoiceWatermarkResp) XXX_Size() int {
	return xxx_messageInfo_GetVoiceWatermarkResp.Size(m)
}
func (m *GetVoiceWatermarkResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVoiceWatermarkResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetVoiceWatermarkResp proto.InternalMessageInfo

func (m *GetVoiceWatermarkResp) GetStageName() string {
	if m != nil {
		return m.StageName
	}
	return ""
}

func (m *GetVoiceWatermarkResp) GetVoiceUrl() string {
	if m != nil {
		return m.VoiceUrl
	}
	return ""
}

type DelVoiceWatermarkReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelVoiceWatermarkReq) Reset()         { *m = DelVoiceWatermarkReq{} }
func (m *DelVoiceWatermarkReq) String() string { return proto.CompactTextString(m) }
func (*DelVoiceWatermarkReq) ProtoMessage()    {}
func (*DelVoiceWatermarkReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{15}
}
func (m *DelVoiceWatermarkReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelVoiceWatermarkReq.Unmarshal(m, b)
}
func (m *DelVoiceWatermarkReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelVoiceWatermarkReq.Marshal(b, m, deterministic)
}
func (dst *DelVoiceWatermarkReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelVoiceWatermarkReq.Merge(dst, src)
}
func (m *DelVoiceWatermarkReq) XXX_Size() int {
	return xxx_messageInfo_DelVoiceWatermarkReq.Size(m)
}
func (m *DelVoiceWatermarkReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelVoiceWatermarkReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelVoiceWatermarkReq proto.InternalMessageInfo

func (m *DelVoiceWatermarkReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type DelVoiceWatermarkResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelVoiceWatermarkResp) Reset()         { *m = DelVoiceWatermarkResp{} }
func (m *DelVoiceWatermarkResp) String() string { return proto.CompactTextString(m) }
func (*DelVoiceWatermarkResp) ProtoMessage()    {}
func (*DelVoiceWatermarkResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{16}
}
func (m *DelVoiceWatermarkResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelVoiceWatermarkResp.Unmarshal(m, b)
}
func (m *DelVoiceWatermarkResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelVoiceWatermarkResp.Marshal(b, m, deterministic)
}
func (dst *DelVoiceWatermarkResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelVoiceWatermarkResp.Merge(dst, src)
}
func (m *DelVoiceWatermarkResp) XXX_Size() int {
	return xxx_messageInfo_DelVoiceWatermarkResp.Size(m)
}
func (m *DelVoiceWatermarkResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelVoiceWatermarkResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelVoiceWatermarkResp proto.InternalMessageInfo

// tab信息
type TabInfo struct {
	TabId                string        `protobuf:"bytes,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabName              string        `protobuf:"bytes,2,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	Status               TabStatusInfo `protobuf:"varint,3,opt,name=status,proto3,enum=rhythm.airapper.TabStatusInfo" json:"status,omitempty"`
	Content              string        `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	UpdateTime           uint32        `protobuf:"varint,5,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	Index                uint32        `protobuf:"varint,6,opt,name=index,proto3" json:"index,omitempty"`
	AiType               uint32        `protobuf:"varint,7,opt,name=ai_type,json=aiType,proto3" json:"ai_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *TabInfo) Reset()         { *m = TabInfo{} }
func (m *TabInfo) String() string { return proto.CompactTextString(m) }
func (*TabInfo) ProtoMessage()    {}
func (*TabInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{17}
}
func (m *TabInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TabInfo.Unmarshal(m, b)
}
func (m *TabInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TabInfo.Marshal(b, m, deterministic)
}
func (dst *TabInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TabInfo.Merge(dst, src)
}
func (m *TabInfo) XXX_Size() int {
	return xxx_messageInfo_TabInfo.Size(m)
}
func (m *TabInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TabInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TabInfo proto.InternalMessageInfo

func (m *TabInfo) GetTabId() string {
	if m != nil {
		return m.TabId
	}
	return ""
}

func (m *TabInfo) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *TabInfo) GetStatus() TabStatusInfo {
	if m != nil {
		return m.Status
	}
	return TabStatusInfo_Configuring
}

func (m *TabInfo) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *TabInfo) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *TabInfo) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *TabInfo) GetAiType() uint32 {
	if m != nil {
		return m.AiType
	}
	return 0
}

// 新增/编辑Tab
type CreateTabInfoReq struct {
	TabInfo              *TabInfo `protobuf:"bytes,1,opt,name=tab_info,json=tabInfo,proto3" json:"tab_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateTabInfoReq) Reset()         { *m = CreateTabInfoReq{} }
func (m *CreateTabInfoReq) String() string { return proto.CompactTextString(m) }
func (*CreateTabInfoReq) ProtoMessage()    {}
func (*CreateTabInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{18}
}
func (m *CreateTabInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateTabInfoReq.Unmarshal(m, b)
}
func (m *CreateTabInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateTabInfoReq.Marshal(b, m, deterministic)
}
func (dst *CreateTabInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateTabInfoReq.Merge(dst, src)
}
func (m *CreateTabInfoReq) XXX_Size() int {
	return xxx_messageInfo_CreateTabInfoReq.Size(m)
}
func (m *CreateTabInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateTabInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateTabInfoReq proto.InternalMessageInfo

func (m *CreateTabInfoReq) GetTabInfo() *TabInfo {
	if m != nil {
		return m.TabInfo
	}
	return nil
}

type CreateTabInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateTabInfoResp) Reset()         { *m = CreateTabInfoResp{} }
func (m *CreateTabInfoResp) String() string { return proto.CompactTextString(m) }
func (*CreateTabInfoResp) ProtoMessage()    {}
func (*CreateTabInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{19}
}
func (m *CreateTabInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateTabInfoResp.Unmarshal(m, b)
}
func (m *CreateTabInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateTabInfoResp.Marshal(b, m, deterministic)
}
func (dst *CreateTabInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateTabInfoResp.Merge(dst, src)
}
func (m *CreateTabInfoResp) XXX_Size() int {
	return xxx_messageInfo_CreateTabInfoResp.Size(m)
}
func (m *CreateTabInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateTabInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateTabInfoResp proto.InternalMessageInfo

// 获取tab信息
type GetTabInfoReq struct {
	AiType               uint32   `protobuf:"varint,1,opt,name=ai_type,json=aiType,proto3" json:"ai_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTabInfoReq) Reset()         { *m = GetTabInfoReq{} }
func (m *GetTabInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetTabInfoReq) ProtoMessage()    {}
func (*GetTabInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{20}
}
func (m *GetTabInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTabInfoReq.Unmarshal(m, b)
}
func (m *GetTabInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTabInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetTabInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTabInfoReq.Merge(dst, src)
}
func (m *GetTabInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetTabInfoReq.Size(m)
}
func (m *GetTabInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTabInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTabInfoReq proto.InternalMessageInfo

func (m *GetTabInfoReq) GetAiType() uint32 {
	if m != nil {
		return m.AiType
	}
	return 0
}

type GetTabInfoResp struct {
	TabInfo              []*TabInfo `protobuf:"bytes,1,rep,name=tab_info,json=tabInfo,proto3" json:"tab_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetTabInfoResp) Reset()         { *m = GetTabInfoResp{} }
func (m *GetTabInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetTabInfoResp) ProtoMessage()    {}
func (*GetTabInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{21}
}
func (m *GetTabInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTabInfoResp.Unmarshal(m, b)
}
func (m *GetTabInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTabInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetTabInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTabInfoResp.Merge(dst, src)
}
func (m *GetTabInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetTabInfoResp.Size(m)
}
func (m *GetTabInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTabInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTabInfoResp proto.InternalMessageInfo

func (m *GetTabInfoResp) GetTabInfo() []*TabInfo {
	if m != nil {
		return m.TabInfo
	}
	return nil
}

// 移动tab位置
type MoveTabCategoryReq struct {
	TabId                string   `protobuf:"bytes,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	CurrentIndex         uint32   `protobuf:"varint,2,opt,name=current_index,json=currentIndex,proto3" json:"current_index,omitempty"`
	TargetIndex          uint32   `protobuf:"varint,3,opt,name=target_index,json=targetIndex,proto3" json:"target_index,omitempty"`
	AiType               uint32   `protobuf:"varint,4,opt,name=ai_type,json=aiType,proto3" json:"ai_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MoveTabCategoryReq) Reset()         { *m = MoveTabCategoryReq{} }
func (m *MoveTabCategoryReq) String() string { return proto.CompactTextString(m) }
func (*MoveTabCategoryReq) ProtoMessage()    {}
func (*MoveTabCategoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{22}
}
func (m *MoveTabCategoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MoveTabCategoryReq.Unmarshal(m, b)
}
func (m *MoveTabCategoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MoveTabCategoryReq.Marshal(b, m, deterministic)
}
func (dst *MoveTabCategoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MoveTabCategoryReq.Merge(dst, src)
}
func (m *MoveTabCategoryReq) XXX_Size() int {
	return xxx_messageInfo_MoveTabCategoryReq.Size(m)
}
func (m *MoveTabCategoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MoveTabCategoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_MoveTabCategoryReq proto.InternalMessageInfo

func (m *MoveTabCategoryReq) GetTabId() string {
	if m != nil {
		return m.TabId
	}
	return ""
}

func (m *MoveTabCategoryReq) GetCurrentIndex() uint32 {
	if m != nil {
		return m.CurrentIndex
	}
	return 0
}

func (m *MoveTabCategoryReq) GetTargetIndex() uint32 {
	if m != nil {
		return m.TargetIndex
	}
	return 0
}

func (m *MoveTabCategoryReq) GetAiType() uint32 {
	if m != nil {
		return m.AiType
	}
	return 0
}

type MoveTabCategoryResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MoveTabCategoryResp) Reset()         { *m = MoveTabCategoryResp{} }
func (m *MoveTabCategoryResp) String() string { return proto.CompactTextString(m) }
func (*MoveTabCategoryResp) ProtoMessage()    {}
func (*MoveTabCategoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{23}
}
func (m *MoveTabCategoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MoveTabCategoryResp.Unmarshal(m, b)
}
func (m *MoveTabCategoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MoveTabCategoryResp.Marshal(b, m, deterministic)
}
func (dst *MoveTabCategoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MoveTabCategoryResp.Merge(dst, src)
}
func (m *MoveTabCategoryResp) XXX_Size() int {
	return xxx_messageInfo_MoveTabCategoryResp.Size(m)
}
func (m *MoveTabCategoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MoveTabCategoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_MoveTabCategoryResp proto.InternalMessageInfo

// 歌曲信息
type SongInfo struct {
	SongName             string   `protobuf:"bytes,1,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	SongRapper           string   `protobuf:"bytes,2,opt,name=song_rapper,json=songRapper,proto3" json:"song_rapper,omitempty"`
	Content              []string `protobuf:"bytes,3,rep,name=content,proto3" json:"content,omitempty"`
	LevelId              string   `protobuf:"bytes,4,opt,name=level_id,json=levelId,proto3" json:"level_id,omitempty"`
	MusicProducer        string   `protobuf:"bytes,5,opt,name=music_producer,json=musicProducer,proto3" json:"music_producer,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SongInfo) Reset()         { *m = SongInfo{} }
func (m *SongInfo) String() string { return proto.CompactTextString(m) }
func (*SongInfo) ProtoMessage()    {}
func (*SongInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{24}
}
func (m *SongInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SongInfo.Unmarshal(m, b)
}
func (m *SongInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SongInfo.Marshal(b, m, deterministic)
}
func (dst *SongInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SongInfo.Merge(dst, src)
}
func (m *SongInfo) XXX_Size() int {
	return xxx_messageInfo_SongInfo.Size(m)
}
func (m *SongInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SongInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SongInfo proto.InternalMessageInfo

func (m *SongInfo) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

func (m *SongInfo) GetSongRapper() string {
	if m != nil {
		return m.SongRapper
	}
	return ""
}

func (m *SongInfo) GetContent() []string {
	if m != nil {
		return m.Content
	}
	return nil
}

func (m *SongInfo) GetLevelId() string {
	if m != nil {
		return m.LevelId
	}
	return ""
}

func (m *SongInfo) GetMusicProducer() string {
	if m != nil {
		return m.MusicProducer
	}
	return ""
}

// 歌曲名+歌手名+url
type UrlInfo struct {
	SongName             string   `protobuf:"bytes,1,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	SongRapper           string   `protobuf:"bytes,2,opt,name=song_rapper,json=songRapper,proto3" json:"song_rapper,omitempty"`
	LyricUrl             string   `protobuf:"bytes,3,opt,name=lyric_url,json=lyricUrl,proto3" json:"lyric_url,omitempty"`
	FileMd5              string   `protobuf:"bytes,4,opt,name=file_md5,json=fileMd5,proto3" json:"file_md5,omitempty"`
	TemplateUrl          string   `protobuf:"bytes,5,opt,name=template_url,json=templateUrl,proto3" json:"template_url,omitempty"`
	TemplateFileMd5      string   `protobuf:"bytes,6,opt,name=template_file_md5,json=templateFileMd5,proto3" json:"template_file_md5,omitempty"`
	NewLyricAUrl         string   `protobuf:"bytes,7,opt,name=new_lyric_a_url,json=newLyricAUrl,proto3" json:"new_lyric_a_url,omitempty"`
	NewLyricAMd5         string   `protobuf:"bytes,8,opt,name=new_lyric_a_md5,json=newLyricAMd5,proto3" json:"new_lyric_a_md5,omitempty"`
	NewLyricBUrl         string   `protobuf:"bytes,9,opt,name=new_lyric_b_url,json=newLyricBUrl,proto3" json:"new_lyric_b_url,omitempty"`
	NewLyricBMd5         string   `protobuf:"bytes,10,opt,name=new_lyric_b_md5,json=newLyricBMd5,proto3" json:"new_lyric_b_md5,omitempty"`
	NewLyric_4Url        string   `protobuf:"bytes,11,opt,name=new_lyric_4_url,json=newLyric4Url,proto3" json:"new_lyric_4_url,omitempty"`
	NewLyric_4Md5        string   `protobuf:"bytes,12,opt,name=new_lyric_4_md5,json=newLyric4Md5,proto3" json:"new_lyric_4_md5,omitempty"`
	MusicProducer        string   `protobuf:"bytes,13,opt,name=music_producer,json=musicProducer,proto3" json:"music_producer,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UrlInfo) Reset()         { *m = UrlInfo{} }
func (m *UrlInfo) String() string { return proto.CompactTextString(m) }
func (*UrlInfo) ProtoMessage()    {}
func (*UrlInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{25}
}
func (m *UrlInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UrlInfo.Unmarshal(m, b)
}
func (m *UrlInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UrlInfo.Marshal(b, m, deterministic)
}
func (dst *UrlInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UrlInfo.Merge(dst, src)
}
func (m *UrlInfo) XXX_Size() int {
	return xxx_messageInfo_UrlInfo.Size(m)
}
func (m *UrlInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UrlInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UrlInfo proto.InternalMessageInfo

func (m *UrlInfo) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

func (m *UrlInfo) GetSongRapper() string {
	if m != nil {
		return m.SongRapper
	}
	return ""
}

func (m *UrlInfo) GetLyricUrl() string {
	if m != nil {
		return m.LyricUrl
	}
	return ""
}

func (m *UrlInfo) GetFileMd5() string {
	if m != nil {
		return m.FileMd5
	}
	return ""
}

func (m *UrlInfo) GetTemplateUrl() string {
	if m != nil {
		return m.TemplateUrl
	}
	return ""
}

func (m *UrlInfo) GetTemplateFileMd5() string {
	if m != nil {
		return m.TemplateFileMd5
	}
	return ""
}

func (m *UrlInfo) GetNewLyricAUrl() string {
	if m != nil {
		return m.NewLyricAUrl
	}
	return ""
}

func (m *UrlInfo) GetNewLyricAMd5() string {
	if m != nil {
		return m.NewLyricAMd5
	}
	return ""
}

func (m *UrlInfo) GetNewLyricBUrl() string {
	if m != nil {
		return m.NewLyricBUrl
	}
	return ""
}

func (m *UrlInfo) GetNewLyricBMd5() string {
	if m != nil {
		return m.NewLyricBMd5
	}
	return ""
}

func (m *UrlInfo) GetNewLyric_4Url() string {
	if m != nil {
		return m.NewLyric_4Url
	}
	return ""
}

func (m *UrlInfo) GetNewLyric_4Md5() string {
	if m != nil {
		return m.NewLyric_4Md5
	}
	return ""
}

func (m *UrlInfo) GetMusicProducer() string {
	if m != nil {
		return m.MusicProducer
	}
	return ""
}

// 新增歌曲信息
type AddSongInfoReq struct {
	TabId                string      `protobuf:"bytes,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	SongInfo             []*SongInfo `protobuf:"bytes,2,rep,name=song_info,json=songInfo,proto3" json:"song_info,omitempty"`
	AiType               uint32      `protobuf:"varint,3,opt,name=ai_type,json=aiType,proto3" json:"ai_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *AddSongInfoReq) Reset()         { *m = AddSongInfoReq{} }
func (m *AddSongInfoReq) String() string { return proto.CompactTextString(m) }
func (*AddSongInfoReq) ProtoMessage()    {}
func (*AddSongInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{26}
}
func (m *AddSongInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddSongInfoReq.Unmarshal(m, b)
}
func (m *AddSongInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddSongInfoReq.Marshal(b, m, deterministic)
}
func (dst *AddSongInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddSongInfoReq.Merge(dst, src)
}
func (m *AddSongInfoReq) XXX_Size() int {
	return xxx_messageInfo_AddSongInfoReq.Size(m)
}
func (m *AddSongInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddSongInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddSongInfoReq proto.InternalMessageInfo

func (m *AddSongInfoReq) GetTabId() string {
	if m != nil {
		return m.TabId
	}
	return ""
}

func (m *AddSongInfoReq) GetSongInfo() []*SongInfo {
	if m != nil {
		return m.SongInfo
	}
	return nil
}

func (m *AddSongInfoReq) GetAiType() uint32 {
	if m != nil {
		return m.AiType
	}
	return 0
}

type AddSongInfoResp struct {
	DuplicativeSong      []*ResInfo `protobuf:"bytes,1,rep,name=duplicative_song,json=duplicativeSong,proto3" json:"duplicative_song,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *AddSongInfoResp) Reset()         { *m = AddSongInfoResp{} }
func (m *AddSongInfoResp) String() string { return proto.CompactTextString(m) }
func (*AddSongInfoResp) ProtoMessage()    {}
func (*AddSongInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{27}
}
func (m *AddSongInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddSongInfoResp.Unmarshal(m, b)
}
func (m *AddSongInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddSongInfoResp.Marshal(b, m, deterministic)
}
func (dst *AddSongInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddSongInfoResp.Merge(dst, src)
}
func (m *AddSongInfoResp) XXX_Size() int {
	return xxx_messageInfo_AddSongInfoResp.Size(m)
}
func (m *AddSongInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddSongInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddSongInfoResp proto.InternalMessageInfo

func (m *AddSongInfoResp) GetDuplicativeSong() []*ResInfo {
	if m != nil {
		return m.DuplicativeSong
	}
	return nil
}

// 新增歌词信息
type UpdateLyricInfoReq struct {
	TabId                string     `protobuf:"bytes,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	UrlInfo              []*UrlInfo `protobuf:"bytes,2,rep,name=url_info,json=urlInfo,proto3" json:"url_info,omitempty"`
	CheckType            uint32     `protobuf:"varint,3,opt,name=check_type,json=checkType,proto3" json:"check_type,omitempty"`
	AiType               uint32     `protobuf:"varint,4,opt,name=ai_type,json=aiType,proto3" json:"ai_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *UpdateLyricInfoReq) Reset()         { *m = UpdateLyricInfoReq{} }
func (m *UpdateLyricInfoReq) String() string { return proto.CompactTextString(m) }
func (*UpdateLyricInfoReq) ProtoMessage()    {}
func (*UpdateLyricInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{28}
}
func (m *UpdateLyricInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateLyricInfoReq.Unmarshal(m, b)
}
func (m *UpdateLyricInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateLyricInfoReq.Marshal(b, m, deterministic)
}
func (dst *UpdateLyricInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateLyricInfoReq.Merge(dst, src)
}
func (m *UpdateLyricInfoReq) XXX_Size() int {
	return xxx_messageInfo_UpdateLyricInfoReq.Size(m)
}
func (m *UpdateLyricInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateLyricInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateLyricInfoReq proto.InternalMessageInfo

func (m *UpdateLyricInfoReq) GetTabId() string {
	if m != nil {
		return m.TabId
	}
	return ""
}

func (m *UpdateLyricInfoReq) GetUrlInfo() []*UrlInfo {
	if m != nil {
		return m.UrlInfo
	}
	return nil
}

func (m *UpdateLyricInfoReq) GetCheckType() uint32 {
	if m != nil {
		return m.CheckType
	}
	return 0
}

func (m *UpdateLyricInfoReq) GetAiType() uint32 {
	if m != nil {
		return m.AiType
	}
	return 0
}

type ResInfo struct {
	SongName             string   `protobuf:"bytes,1,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	SongRapper           string   `protobuf:"bytes,2,opt,name=song_rapper,json=songRapper,proto3" json:"song_rapper,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ResInfo) Reset()         { *m = ResInfo{} }
func (m *ResInfo) String() string { return proto.CompactTextString(m) }
func (*ResInfo) ProtoMessage()    {}
func (*ResInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{29}
}
func (m *ResInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ResInfo.Unmarshal(m, b)
}
func (m *ResInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ResInfo.Marshal(b, m, deterministic)
}
func (dst *ResInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResInfo.Merge(dst, src)
}
func (m *ResInfo) XXX_Size() int {
	return xxx_messageInfo_ResInfo.Size(m)
}
func (m *ResInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ResInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ResInfo proto.InternalMessageInfo

func (m *ResInfo) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

func (m *ResInfo) GetSongRapper() string {
	if m != nil {
		return m.SongRapper
	}
	return ""
}

type UpdateLyricInfoResp struct {
	DuplicativeSong      []*ResInfo `protobuf:"bytes,1,rep,name=duplicative_song,json=duplicativeSong,proto3" json:"duplicative_song,omitempty"`
	NoExistSong          []*ResInfo `protobuf:"bytes,2,rep,name=no_exist_song,json=noExistSong,proto3" json:"no_exist_song,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *UpdateLyricInfoResp) Reset()         { *m = UpdateLyricInfoResp{} }
func (m *UpdateLyricInfoResp) String() string { return proto.CompactTextString(m) }
func (*UpdateLyricInfoResp) ProtoMessage()    {}
func (*UpdateLyricInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{30}
}
func (m *UpdateLyricInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateLyricInfoResp.Unmarshal(m, b)
}
func (m *UpdateLyricInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateLyricInfoResp.Marshal(b, m, deterministic)
}
func (dst *UpdateLyricInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateLyricInfoResp.Merge(dst, src)
}
func (m *UpdateLyricInfoResp) XXX_Size() int {
	return xxx_messageInfo_UpdateLyricInfoResp.Size(m)
}
func (m *UpdateLyricInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateLyricInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateLyricInfoResp proto.InternalMessageInfo

func (m *UpdateLyricInfoResp) GetDuplicativeSong() []*ResInfo {
	if m != nil {
		return m.DuplicativeSong
	}
	return nil
}

func (m *UpdateLyricInfoResp) GetNoExistSong() []*ResInfo {
	if m != nil {
		return m.NoExistSong
	}
	return nil
}

type CheckSongFileReq struct {
	TabId                string     `protobuf:"bytes,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	SongList             []*ResInfo `protobuf:"bytes,2,rep,name=song_list,json=songList,proto3" json:"song_list,omitempty"`
	CheckType            uint32     `protobuf:"varint,3,opt,name=check_type,json=checkType,proto3" json:"check_type,omitempty"`
	AiType               uint32     `protobuf:"varint,4,opt,name=ai_type,json=aiType,proto3" json:"ai_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *CheckSongFileReq) Reset()         { *m = CheckSongFileReq{} }
func (m *CheckSongFileReq) String() string { return proto.CompactTextString(m) }
func (*CheckSongFileReq) ProtoMessage()    {}
func (*CheckSongFileReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{31}
}
func (m *CheckSongFileReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckSongFileReq.Unmarshal(m, b)
}
func (m *CheckSongFileReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckSongFileReq.Marshal(b, m, deterministic)
}
func (dst *CheckSongFileReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckSongFileReq.Merge(dst, src)
}
func (m *CheckSongFileReq) XXX_Size() int {
	return xxx_messageInfo_CheckSongFileReq.Size(m)
}
func (m *CheckSongFileReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckSongFileReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckSongFileReq proto.InternalMessageInfo

func (m *CheckSongFileReq) GetTabId() string {
	if m != nil {
		return m.TabId
	}
	return ""
}

func (m *CheckSongFileReq) GetSongList() []*ResInfo {
	if m != nil {
		return m.SongList
	}
	return nil
}

func (m *CheckSongFileReq) GetCheckType() uint32 {
	if m != nil {
		return m.CheckType
	}
	return 0
}

func (m *CheckSongFileReq) GetAiType() uint32 {
	if m != nil {
		return m.AiType
	}
	return 0
}

type CheckSongFileResp struct {
	DuplicativeSong      []*ResInfo        `protobuf:"bytes,1,rep,name=duplicative_song,json=duplicativeSong,proto3" json:"duplicative_song,omitempty"`
	NoExistSong          []*ResInfo        `protobuf:"bytes,2,rep,name=no_exist_song,json=noExistSong,proto3" json:"no_exist_song,omitempty"`
	ValidSong            map[string]uint32 `protobuf:"bytes,3,rep,name=valid_song,json=validSong,proto3" json:"valid_song,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *CheckSongFileResp) Reset()         { *m = CheckSongFileResp{} }
func (m *CheckSongFileResp) String() string { return proto.CompactTextString(m) }
func (*CheckSongFileResp) ProtoMessage()    {}
func (*CheckSongFileResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{32}
}
func (m *CheckSongFileResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckSongFileResp.Unmarshal(m, b)
}
func (m *CheckSongFileResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckSongFileResp.Marshal(b, m, deterministic)
}
func (dst *CheckSongFileResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckSongFileResp.Merge(dst, src)
}
func (m *CheckSongFileResp) XXX_Size() int {
	return xxx_messageInfo_CheckSongFileResp.Size(m)
}
func (m *CheckSongFileResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckSongFileResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckSongFileResp proto.InternalMessageInfo

func (m *CheckSongFileResp) GetDuplicativeSong() []*ResInfo {
	if m != nil {
		return m.DuplicativeSong
	}
	return nil
}

func (m *CheckSongFileResp) GetNoExistSong() []*ResInfo {
	if m != nil {
		return m.NoExistSong
	}
	return nil
}

func (m *CheckSongFileResp) GetValidSong() map[string]uint32 {
	if m != nil {
		return m.ValidSong
	}
	return nil
}

// 检查歌曲是否存在
type CheckSongExistReq struct {
	TabId                string     `protobuf:"bytes,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	SongList             []*ResInfo `protobuf:"bytes,2,rep,name=song_list,json=songList,proto3" json:"song_list,omitempty"`
	AiType               uint32     `protobuf:"varint,3,opt,name=ai_type,json=aiType,proto3" json:"ai_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *CheckSongExistReq) Reset()         { *m = CheckSongExistReq{} }
func (m *CheckSongExistReq) String() string { return proto.CompactTextString(m) }
func (*CheckSongExistReq) ProtoMessage()    {}
func (*CheckSongExistReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{33}
}
func (m *CheckSongExistReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckSongExistReq.Unmarshal(m, b)
}
func (m *CheckSongExistReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckSongExistReq.Marshal(b, m, deterministic)
}
func (dst *CheckSongExistReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckSongExistReq.Merge(dst, src)
}
func (m *CheckSongExistReq) XXX_Size() int {
	return xxx_messageInfo_CheckSongExistReq.Size(m)
}
func (m *CheckSongExistReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckSongExistReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckSongExistReq proto.InternalMessageInfo

func (m *CheckSongExistReq) GetTabId() string {
	if m != nil {
		return m.TabId
	}
	return ""
}

func (m *CheckSongExistReq) GetSongList() []*ResInfo {
	if m != nil {
		return m.SongList
	}
	return nil
}

func (m *CheckSongExistReq) GetAiType() uint32 {
	if m != nil {
		return m.AiType
	}
	return 0
}

type CheckSongExistResp struct {
	NoExistSong          []*ResInfo        `protobuf:"bytes,1,rep,name=no_exist_song,json=noExistSong,proto3" json:"no_exist_song,omitempty"`
	ValidSong            map[string]uint32 `protobuf:"bytes,2,rep,name=valid_song,json=validSong,proto3" json:"valid_song,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *CheckSongExistResp) Reset()         { *m = CheckSongExistResp{} }
func (m *CheckSongExistResp) String() string { return proto.CompactTextString(m) }
func (*CheckSongExistResp) ProtoMessage()    {}
func (*CheckSongExistResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{34}
}
func (m *CheckSongExistResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckSongExistResp.Unmarshal(m, b)
}
func (m *CheckSongExistResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckSongExistResp.Marshal(b, m, deterministic)
}
func (dst *CheckSongExistResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckSongExistResp.Merge(dst, src)
}
func (m *CheckSongExistResp) XXX_Size() int {
	return xxx_messageInfo_CheckSongExistResp.Size(m)
}
func (m *CheckSongExistResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckSongExistResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckSongExistResp proto.InternalMessageInfo

func (m *CheckSongExistResp) GetNoExistSong() []*ResInfo {
	if m != nil {
		return m.NoExistSong
	}
	return nil
}

func (m *CheckSongExistResp) GetValidSong() map[string]uint32 {
	if m != nil {
		return m.ValidSong
	}
	return nil
}

type VideoURL struct {
	BgVideoUrl           string   `protobuf:"bytes,1,opt,name=bg_video_url,json=bgVideoUrl,proto3" json:"bg_video_url,omitempty"`
	BgVideoMd5           string   `protobuf:"bytes,2,opt,name=bg_video_md5,json=bgVideoMd5,proto3" json:"bg_video_md5,omitempty"`
	BgOriginVideoUrl     string   `protobuf:"bytes,3,opt,name=bg_origin_video_url,json=bgOriginVideoUrl,proto3" json:"bg_origin_video_url,omitempty"`
	BgVideoFirstFrameUrl string   `protobuf:"bytes,4,opt,name=bg_video_first_frame_url,json=bgVideoFirstFrameUrl,proto3" json:"bg_video_first_frame_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VideoURL) Reset()         { *m = VideoURL{} }
func (m *VideoURL) String() string { return proto.CompactTextString(m) }
func (*VideoURL) ProtoMessage()    {}
func (*VideoURL) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{35}
}
func (m *VideoURL) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VideoURL.Unmarshal(m, b)
}
func (m *VideoURL) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VideoURL.Marshal(b, m, deterministic)
}
func (dst *VideoURL) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VideoURL.Merge(dst, src)
}
func (m *VideoURL) XXX_Size() int {
	return xxx_messageInfo_VideoURL.Size(m)
}
func (m *VideoURL) XXX_DiscardUnknown() {
	xxx_messageInfo_VideoURL.DiscardUnknown(m)
}

var xxx_messageInfo_VideoURL proto.InternalMessageInfo

func (m *VideoURL) GetBgVideoUrl() string {
	if m != nil {
		return m.BgVideoUrl
	}
	return ""
}

func (m *VideoURL) GetBgVideoMd5() string {
	if m != nil {
		return m.BgVideoMd5
	}
	return ""
}

func (m *VideoURL) GetBgOriginVideoUrl() string {
	if m != nil {
		return m.BgOriginVideoUrl
	}
	return ""
}

func (m *VideoURL) GetBgVideoFirstFrameUrl() string {
	if m != nil {
		return m.BgVideoFirstFrameUrl
	}
	return ""
}

// 编辑歌曲配置信息
type UpdateSongConfiguration struct {
	RapId                string      `protobuf:"bytes,1,opt,name=rap_id,json=rapId,proto3" json:"rap_id,omitempty"`
	SongName             string      `protobuf:"bytes,2,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	SongRapper           string      `protobuf:"bytes,3,opt,name=song_rapper,json=songRapper,proto3" json:"song_rapper,omitempty"`
	Content              []string    `protobuf:"bytes,4,rep,name=content,proto3" json:"content,omitempty"`
	TabId                string      `protobuf:"bytes,5,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	LyricUrl             string      `protobuf:"bytes,6,opt,name=lyric_url,json=lyricUrl,proto3" json:"lyric_url,omitempty"`
	FileMd5              string      `protobuf:"bytes,7,opt,name=file_md5,json=fileMd5,proto3" json:"file_md5,omitempty"`
	Status               StatusInfo  `protobuf:"varint,8,opt,name=status,proto3,enum=rhythm.airapper.StatusInfo" json:"status,omitempty"`
	LevelId              string      `protobuf:"bytes,9,opt,name=level_id,json=levelId,proto3" json:"level_id,omitempty"`
	TemplateUrl          string      `protobuf:"bytes,10,opt,name=template_url,json=templateUrl,proto3" json:"template_url,omitempty"`
	TemplateFileMd5      string      `protobuf:"bytes,11,opt,name=template_file_md5,json=templateFileMd5,proto3" json:"template_file_md5,omitempty"`
	SongPosition         uint32      `protobuf:"varint,12,opt,name=song_position,json=songPosition,proto3" json:"song_position,omitempty"`
	NewLyricAUrl         string      `protobuf:"bytes,13,opt,name=new_lyric_a_url,json=newLyricAUrl,proto3" json:"new_lyric_a_url,omitempty"`
	NewLyricAMd5         string      `protobuf:"bytes,14,opt,name=new_lyric_a_md5,json=newLyricAMd5,proto3" json:"new_lyric_a_md5,omitempty"`
	NewLyricBUrl         string      `protobuf:"bytes,15,opt,name=new_lyric_b_url,json=newLyricBUrl,proto3" json:"new_lyric_b_url,omitempty"`
	NewLyricBMd5         string      `protobuf:"bytes,16,opt,name=new_lyric_b_md5,json=newLyricBMd5,proto3" json:"new_lyric_b_md5,omitempty"`
	NewLyric_4Url        string      `protobuf:"bytes,17,opt,name=new_lyric_4_url,json=newLyric4Url,proto3" json:"new_lyric_4_url,omitempty"`
	NewLyric_4Md5        string      `protobuf:"bytes,18,opt,name=new_lyric_4_md5,json=newLyric4Md5,proto3" json:"new_lyric_4_md5,omitempty"`
	MusicProducer        string      `protobuf:"bytes,19,opt,name=music_producer,json=musicProducer,proto3" json:"music_producer,omitempty"`
	ComposedVideoUrlInfo []*VideoURL `protobuf:"bytes,20,rep,name=composed_video_url_info,json=composedVideoUrlInfo,proto3" json:"composed_video_url_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *UpdateSongConfiguration) Reset()         { *m = UpdateSongConfiguration{} }
func (m *UpdateSongConfiguration) String() string { return proto.CompactTextString(m) }
func (*UpdateSongConfiguration) ProtoMessage()    {}
func (*UpdateSongConfiguration) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{36}
}
func (m *UpdateSongConfiguration) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateSongConfiguration.Unmarshal(m, b)
}
func (m *UpdateSongConfiguration) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateSongConfiguration.Marshal(b, m, deterministic)
}
func (dst *UpdateSongConfiguration) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateSongConfiguration.Merge(dst, src)
}
func (m *UpdateSongConfiguration) XXX_Size() int {
	return xxx_messageInfo_UpdateSongConfiguration.Size(m)
}
func (m *UpdateSongConfiguration) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateSongConfiguration.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateSongConfiguration proto.InternalMessageInfo

func (m *UpdateSongConfiguration) GetRapId() string {
	if m != nil {
		return m.RapId
	}
	return ""
}

func (m *UpdateSongConfiguration) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

func (m *UpdateSongConfiguration) GetSongRapper() string {
	if m != nil {
		return m.SongRapper
	}
	return ""
}

func (m *UpdateSongConfiguration) GetContent() []string {
	if m != nil {
		return m.Content
	}
	return nil
}

func (m *UpdateSongConfiguration) GetTabId() string {
	if m != nil {
		return m.TabId
	}
	return ""
}

func (m *UpdateSongConfiguration) GetLyricUrl() string {
	if m != nil {
		return m.LyricUrl
	}
	return ""
}

func (m *UpdateSongConfiguration) GetFileMd5() string {
	if m != nil {
		return m.FileMd5
	}
	return ""
}

func (m *UpdateSongConfiguration) GetStatus() StatusInfo {
	if m != nil {
		return m.Status
	}
	return StatusInfo_NoneStatus
}

func (m *UpdateSongConfiguration) GetLevelId() string {
	if m != nil {
		return m.LevelId
	}
	return ""
}

func (m *UpdateSongConfiguration) GetTemplateUrl() string {
	if m != nil {
		return m.TemplateUrl
	}
	return ""
}

func (m *UpdateSongConfiguration) GetTemplateFileMd5() string {
	if m != nil {
		return m.TemplateFileMd5
	}
	return ""
}

func (m *UpdateSongConfiguration) GetSongPosition() uint32 {
	if m != nil {
		return m.SongPosition
	}
	return 0
}

func (m *UpdateSongConfiguration) GetNewLyricAUrl() string {
	if m != nil {
		return m.NewLyricAUrl
	}
	return ""
}

func (m *UpdateSongConfiguration) GetNewLyricAMd5() string {
	if m != nil {
		return m.NewLyricAMd5
	}
	return ""
}

func (m *UpdateSongConfiguration) GetNewLyricBUrl() string {
	if m != nil {
		return m.NewLyricBUrl
	}
	return ""
}

func (m *UpdateSongConfiguration) GetNewLyricBMd5() string {
	if m != nil {
		return m.NewLyricBMd5
	}
	return ""
}

func (m *UpdateSongConfiguration) GetNewLyric_4Url() string {
	if m != nil {
		return m.NewLyric_4Url
	}
	return ""
}

func (m *UpdateSongConfiguration) GetNewLyric_4Md5() string {
	if m != nil {
		return m.NewLyric_4Md5
	}
	return ""
}

func (m *UpdateSongConfiguration) GetMusicProducer() string {
	if m != nil {
		return m.MusicProducer
	}
	return ""
}

func (m *UpdateSongConfiguration) GetComposedVideoUrlInfo() []*VideoURL {
	if m != nil {
		return m.ComposedVideoUrlInfo
	}
	return nil
}

// 编辑歌曲配置信息
type UpdateSongConfigurationReq struct {
	SongConfiguration    *UpdateSongConfiguration `protobuf:"bytes,1,opt,name=song_configuration,json=songConfiguration,proto3" json:"song_configuration,omitempty"`
	AiType               uint32                   `protobuf:"varint,2,opt,name=ai_type,json=aiType,proto3" json:"ai_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *UpdateSongConfigurationReq) Reset()         { *m = UpdateSongConfigurationReq{} }
func (m *UpdateSongConfigurationReq) String() string { return proto.CompactTextString(m) }
func (*UpdateSongConfigurationReq) ProtoMessage()    {}
func (*UpdateSongConfigurationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{37}
}
func (m *UpdateSongConfigurationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateSongConfigurationReq.Unmarshal(m, b)
}
func (m *UpdateSongConfigurationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateSongConfigurationReq.Marshal(b, m, deterministic)
}
func (dst *UpdateSongConfigurationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateSongConfigurationReq.Merge(dst, src)
}
func (m *UpdateSongConfigurationReq) XXX_Size() int {
	return xxx_messageInfo_UpdateSongConfigurationReq.Size(m)
}
func (m *UpdateSongConfigurationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateSongConfigurationReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateSongConfigurationReq proto.InternalMessageInfo

func (m *UpdateSongConfigurationReq) GetSongConfiguration() *UpdateSongConfiguration {
	if m != nil {
		return m.SongConfiguration
	}
	return nil
}

func (m *UpdateSongConfigurationReq) GetAiType() uint32 {
	if m != nil {
		return m.AiType
	}
	return 0
}

type UpdateSongConfigurationResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateSongConfigurationResp) Reset()         { *m = UpdateSongConfigurationResp{} }
func (m *UpdateSongConfigurationResp) String() string { return proto.CompactTextString(m) }
func (*UpdateSongConfigurationResp) ProtoMessage()    {}
func (*UpdateSongConfigurationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{38}
}
func (m *UpdateSongConfigurationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateSongConfigurationResp.Unmarshal(m, b)
}
func (m *UpdateSongConfigurationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateSongConfigurationResp.Marshal(b, m, deterministic)
}
func (dst *UpdateSongConfigurationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateSongConfigurationResp.Merge(dst, src)
}
func (m *UpdateSongConfigurationResp) XXX_Size() int {
	return xxx_messageInfo_UpdateSongConfigurationResp.Size(m)
}
func (m *UpdateSongConfigurationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateSongConfigurationResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateSongConfigurationResp proto.InternalMessageInfo

// 编辑歌曲的合成视频资源 覆盖设置
type SongComposedVideo struct {
	TabId                string      `protobuf:"bytes,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	SongName             string      `protobuf:"bytes,2,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	SongRapper           string      `protobuf:"bytes,3,opt,name=song_rapper,json=songRapper,proto3" json:"song_rapper,omitempty"`
	ComposedVideoUrlInfo []*VideoURL `protobuf:"bytes,4,rep,name=composed_video_url_info,json=composedVideoUrlInfo,proto3" json:"composed_video_url_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *SongComposedVideo) Reset()         { *m = SongComposedVideo{} }
func (m *SongComposedVideo) String() string { return proto.CompactTextString(m) }
func (*SongComposedVideo) ProtoMessage()    {}
func (*SongComposedVideo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{39}
}
func (m *SongComposedVideo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SongComposedVideo.Unmarshal(m, b)
}
func (m *SongComposedVideo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SongComposedVideo.Marshal(b, m, deterministic)
}
func (dst *SongComposedVideo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SongComposedVideo.Merge(dst, src)
}
func (m *SongComposedVideo) XXX_Size() int {
	return xxx_messageInfo_SongComposedVideo.Size(m)
}
func (m *SongComposedVideo) XXX_DiscardUnknown() {
	xxx_messageInfo_SongComposedVideo.DiscardUnknown(m)
}

var xxx_messageInfo_SongComposedVideo proto.InternalMessageInfo

func (m *SongComposedVideo) GetTabId() string {
	if m != nil {
		return m.TabId
	}
	return ""
}

func (m *SongComposedVideo) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

func (m *SongComposedVideo) GetSongRapper() string {
	if m != nil {
		return m.SongRapper
	}
	return ""
}

func (m *SongComposedVideo) GetComposedVideoUrlInfo() []*VideoURL {
	if m != nil {
		return m.ComposedVideoUrlInfo
	}
	return nil
}

type BatUpdateComposedVideoUrlInfoReq struct {
	AiType               uint32               `protobuf:"varint,1,opt,name=ai_type,json=aiType,proto3" json:"ai_type,omitempty"`
	InfoList             []*SongComposedVideo `protobuf:"bytes,2,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *BatUpdateComposedVideoUrlInfoReq) Reset()         { *m = BatUpdateComposedVideoUrlInfoReq{} }
func (m *BatUpdateComposedVideoUrlInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatUpdateComposedVideoUrlInfoReq) ProtoMessage()    {}
func (*BatUpdateComposedVideoUrlInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{40}
}
func (m *BatUpdateComposedVideoUrlInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatUpdateComposedVideoUrlInfoReq.Unmarshal(m, b)
}
func (m *BatUpdateComposedVideoUrlInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatUpdateComposedVideoUrlInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatUpdateComposedVideoUrlInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatUpdateComposedVideoUrlInfoReq.Merge(dst, src)
}
func (m *BatUpdateComposedVideoUrlInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatUpdateComposedVideoUrlInfoReq.Size(m)
}
func (m *BatUpdateComposedVideoUrlInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatUpdateComposedVideoUrlInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatUpdateComposedVideoUrlInfoReq proto.InternalMessageInfo

func (m *BatUpdateComposedVideoUrlInfoReq) GetAiType() uint32 {
	if m != nil {
		return m.AiType
	}
	return 0
}

func (m *BatUpdateComposedVideoUrlInfoReq) GetInfoList() []*SongComposedVideo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type BatUpdateComposedVideoUrlInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatUpdateComposedVideoUrlInfoResp) Reset()         { *m = BatUpdateComposedVideoUrlInfoResp{} }
func (m *BatUpdateComposedVideoUrlInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatUpdateComposedVideoUrlInfoResp) ProtoMessage()    {}
func (*BatUpdateComposedVideoUrlInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{41}
}
func (m *BatUpdateComposedVideoUrlInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatUpdateComposedVideoUrlInfoResp.Unmarshal(m, b)
}
func (m *BatUpdateComposedVideoUrlInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatUpdateComposedVideoUrlInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatUpdateComposedVideoUrlInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatUpdateComposedVideoUrlInfoResp.Merge(dst, src)
}
func (m *BatUpdateComposedVideoUrlInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatUpdateComposedVideoUrlInfoResp.Size(m)
}
func (m *BatUpdateComposedVideoUrlInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatUpdateComposedVideoUrlInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatUpdateComposedVideoUrlInfoResp proto.InternalMessageInfo

// 歌曲配置信息
type SongConfigurationInformation struct {
	RapId                string      `protobuf:"bytes,1,opt,name=rap_id,json=rapId,proto3" json:"rap_id,omitempty"`
	SongName             string      `protobuf:"bytes,2,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	SongRapper           string      `protobuf:"bytes,3,opt,name=song_rapper,json=songRapper,proto3" json:"song_rapper,omitempty"`
	Content              []string    `protobuf:"bytes,4,rep,name=content,proto3" json:"content,omitempty"`
	TabId                string      `protobuf:"bytes,5,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	LevelId              string      `protobuf:"bytes,6,opt,name=level_id,json=levelId,proto3" json:"level_id,omitempty"`
	LyricUrl             string      `protobuf:"bytes,7,opt,name=lyric_url,json=lyricUrl,proto3" json:"lyric_url,omitempty"`
	FileMd5              string      `protobuf:"bytes,8,opt,name=file_md5,json=fileMd5,proto3" json:"file_md5,omitempty"`
	Status               StatusInfo  `protobuf:"varint,9,opt,name=status,proto3,enum=rhythm.airapper.StatusInfo" json:"status,omitempty"`
	UpdateTime           uint32      `protobuf:"varint,10,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	Index                uint32      `protobuf:"varint,11,opt,name=index,proto3" json:"index,omitempty"`
	TemplateUrl          string      `protobuf:"bytes,12,opt,name=template_url,json=templateUrl,proto3" json:"template_url,omitempty"`
	TemplateFileMd5      string      `protobuf:"bytes,13,opt,name=template_file_md5,json=templateFileMd5,proto3" json:"template_file_md5,omitempty"`
	SongPosition         uint32      `protobuf:"varint,14,opt,name=song_position,json=songPosition,proto3" json:"song_position,omitempty"`
	NewLyricAUrl         string      `protobuf:"bytes,15,opt,name=new_lyric_a_url,json=newLyricAUrl,proto3" json:"new_lyric_a_url,omitempty"`
	NewLyricAMd5         string      `protobuf:"bytes,16,opt,name=new_lyric_a_md5,json=newLyricAMd5,proto3" json:"new_lyric_a_md5,omitempty"`
	NewLyricBUrl         string      `protobuf:"bytes,17,opt,name=new_lyric_b_url,json=newLyricBUrl,proto3" json:"new_lyric_b_url,omitempty"`
	NewLyricBMd5         string      `protobuf:"bytes,18,opt,name=new_lyric_b_md5,json=newLyricBMd5,proto3" json:"new_lyric_b_md5,omitempty"`
	NewLyric_4Url        string      `protobuf:"bytes,19,opt,name=new_lyric_4_url,json=newLyric4Url,proto3" json:"new_lyric_4_url,omitempty"`
	NewLyric_4Md5        string      `protobuf:"bytes,20,opt,name=new_lyric_4_md5,json=newLyric4Md5,proto3" json:"new_lyric_4_md5,omitempty"`
	MusicProducer        string      `protobuf:"bytes,21,opt,name=music_producer,json=musicProducer,proto3" json:"music_producer,omitempty"`
	ComposedVideoUrlInfo []*VideoURL `protobuf:"bytes,22,rep,name=composed_video_url_info,json=composedVideoUrlInfo,proto3" json:"composed_video_url_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *SongConfigurationInformation) Reset()         { *m = SongConfigurationInformation{} }
func (m *SongConfigurationInformation) String() string { return proto.CompactTextString(m) }
func (*SongConfigurationInformation) ProtoMessage()    {}
func (*SongConfigurationInformation) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{42}
}
func (m *SongConfigurationInformation) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SongConfigurationInformation.Unmarshal(m, b)
}
func (m *SongConfigurationInformation) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SongConfigurationInformation.Marshal(b, m, deterministic)
}
func (dst *SongConfigurationInformation) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SongConfigurationInformation.Merge(dst, src)
}
func (m *SongConfigurationInformation) XXX_Size() int {
	return xxx_messageInfo_SongConfigurationInformation.Size(m)
}
func (m *SongConfigurationInformation) XXX_DiscardUnknown() {
	xxx_messageInfo_SongConfigurationInformation.DiscardUnknown(m)
}

var xxx_messageInfo_SongConfigurationInformation proto.InternalMessageInfo

func (m *SongConfigurationInformation) GetRapId() string {
	if m != nil {
		return m.RapId
	}
	return ""
}

func (m *SongConfigurationInformation) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

func (m *SongConfigurationInformation) GetSongRapper() string {
	if m != nil {
		return m.SongRapper
	}
	return ""
}

func (m *SongConfigurationInformation) GetContent() []string {
	if m != nil {
		return m.Content
	}
	return nil
}

func (m *SongConfigurationInformation) GetTabId() string {
	if m != nil {
		return m.TabId
	}
	return ""
}

func (m *SongConfigurationInformation) GetLevelId() string {
	if m != nil {
		return m.LevelId
	}
	return ""
}

func (m *SongConfigurationInformation) GetLyricUrl() string {
	if m != nil {
		return m.LyricUrl
	}
	return ""
}

func (m *SongConfigurationInformation) GetFileMd5() string {
	if m != nil {
		return m.FileMd5
	}
	return ""
}

func (m *SongConfigurationInformation) GetStatus() StatusInfo {
	if m != nil {
		return m.Status
	}
	return StatusInfo_NoneStatus
}

func (m *SongConfigurationInformation) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *SongConfigurationInformation) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *SongConfigurationInformation) GetTemplateUrl() string {
	if m != nil {
		return m.TemplateUrl
	}
	return ""
}

func (m *SongConfigurationInformation) GetTemplateFileMd5() string {
	if m != nil {
		return m.TemplateFileMd5
	}
	return ""
}

func (m *SongConfigurationInformation) GetSongPosition() uint32 {
	if m != nil {
		return m.SongPosition
	}
	return 0
}

func (m *SongConfigurationInformation) GetNewLyricAUrl() string {
	if m != nil {
		return m.NewLyricAUrl
	}
	return ""
}

func (m *SongConfigurationInformation) GetNewLyricAMd5() string {
	if m != nil {
		return m.NewLyricAMd5
	}
	return ""
}

func (m *SongConfigurationInformation) GetNewLyricBUrl() string {
	if m != nil {
		return m.NewLyricBUrl
	}
	return ""
}

func (m *SongConfigurationInformation) GetNewLyricBMd5() string {
	if m != nil {
		return m.NewLyricBMd5
	}
	return ""
}

func (m *SongConfigurationInformation) GetNewLyric_4Url() string {
	if m != nil {
		return m.NewLyric_4Url
	}
	return ""
}

func (m *SongConfigurationInformation) GetNewLyric_4Md5() string {
	if m != nil {
		return m.NewLyric_4Md5
	}
	return ""
}

func (m *SongConfigurationInformation) GetMusicProducer() string {
	if m != nil {
		return m.MusicProducer
	}
	return ""
}

func (m *SongConfigurationInformation) GetComposedVideoUrlInfo() []*VideoURL {
	if m != nil {
		return m.ComposedVideoUrlInfo
	}
	return nil
}

// 获取歌曲配置信息
type GetSongConfigurationInformationReq struct {
	RapId                string     `protobuf:"bytes,1,opt,name=rap_id,json=rapId,proto3" json:"rap_id,omitempty"`
	SongName             string     `protobuf:"bytes,2,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	SongRapper           string     `protobuf:"bytes,3,opt,name=song_rapper,json=songRapper,proto3" json:"song_rapper,omitempty"`
	Content              string     `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	TabId                string     `protobuf:"bytes,5,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	LevelId              string     `protobuf:"bytes,6,opt,name=level_id,json=levelId,proto3" json:"level_id,omitempty"`
	FileExist            FileExist  `protobuf:"varint,7,opt,name=file_exist,json=fileExist,proto3,enum=rhythm.airapper.FileExist" json:"file_exist,omitempty"`
	Status               StatusInfo `protobuf:"varint,8,opt,name=status,proto3,enum=rhythm.airapper.StatusInfo" json:"status,omitempty"`
	Offset               uint32     `protobuf:"varint,9,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32     `protobuf:"varint,10,opt,name=limit,proto3" json:"limit,omitempty"`
	AiType               uint32     `protobuf:"varint,11,opt,name=ai_type,json=aiType,proto3" json:"ai_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetSongConfigurationInformationReq) Reset()         { *m = GetSongConfigurationInformationReq{} }
func (m *GetSongConfigurationInformationReq) String() string { return proto.CompactTextString(m) }
func (*GetSongConfigurationInformationReq) ProtoMessage()    {}
func (*GetSongConfigurationInformationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{43}
}
func (m *GetSongConfigurationInformationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSongConfigurationInformationReq.Unmarshal(m, b)
}
func (m *GetSongConfigurationInformationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSongConfigurationInformationReq.Marshal(b, m, deterministic)
}
func (dst *GetSongConfigurationInformationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSongConfigurationInformationReq.Merge(dst, src)
}
func (m *GetSongConfigurationInformationReq) XXX_Size() int {
	return xxx_messageInfo_GetSongConfigurationInformationReq.Size(m)
}
func (m *GetSongConfigurationInformationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSongConfigurationInformationReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSongConfigurationInformationReq proto.InternalMessageInfo

func (m *GetSongConfigurationInformationReq) GetRapId() string {
	if m != nil {
		return m.RapId
	}
	return ""
}

func (m *GetSongConfigurationInformationReq) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

func (m *GetSongConfigurationInformationReq) GetSongRapper() string {
	if m != nil {
		return m.SongRapper
	}
	return ""
}

func (m *GetSongConfigurationInformationReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *GetSongConfigurationInformationReq) GetTabId() string {
	if m != nil {
		return m.TabId
	}
	return ""
}

func (m *GetSongConfigurationInformationReq) GetLevelId() string {
	if m != nil {
		return m.LevelId
	}
	return ""
}

func (m *GetSongConfigurationInformationReq) GetFileExist() FileExist {
	if m != nil {
		return m.FileExist
	}
	return FileExist_NoneSense
}

func (m *GetSongConfigurationInformationReq) GetStatus() StatusInfo {
	if m != nil {
		return m.Status
	}
	return StatusInfo_NoneStatus
}

func (m *GetSongConfigurationInformationReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetSongConfigurationInformationReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetSongConfigurationInformationReq) GetAiType() uint32 {
	if m != nil {
		return m.AiType
	}
	return 0
}

type GetSongConfigurationInformationResp struct {
	SongConfigurationInfo []*SongConfigurationInformation `protobuf:"bytes,1,rep,name=song_configuration_info,json=songConfigurationInfo,proto3" json:"song_configuration_info,omitempty"`
	Total                 uint32                          `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}                        `json:"-"`
	XXX_unrecognized      []byte                          `json:"-"`
	XXX_sizecache         int32                           `json:"-"`
}

func (m *GetSongConfigurationInformationResp) Reset()         { *m = GetSongConfigurationInformationResp{} }
func (m *GetSongConfigurationInformationResp) String() string { return proto.CompactTextString(m) }
func (*GetSongConfigurationInformationResp) ProtoMessage()    {}
func (*GetSongConfigurationInformationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{44}
}
func (m *GetSongConfigurationInformationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSongConfigurationInformationResp.Unmarshal(m, b)
}
func (m *GetSongConfigurationInformationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSongConfigurationInformationResp.Marshal(b, m, deterministic)
}
func (dst *GetSongConfigurationInformationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSongConfigurationInformationResp.Merge(dst, src)
}
func (m *GetSongConfigurationInformationResp) XXX_Size() int {
	return xxx_messageInfo_GetSongConfigurationInformationResp.Size(m)
}
func (m *GetSongConfigurationInformationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSongConfigurationInformationResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSongConfigurationInformationResp proto.InternalMessageInfo

func (m *GetSongConfigurationInformationResp) GetSongConfigurationInfo() []*SongConfigurationInformation {
	if m != nil {
		return m.SongConfigurationInfo
	}
	return nil
}

func (m *GetSongConfigurationInformationResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 删除歌曲
type DelSongConfigurationReq struct {
	RapId                string   `protobuf:"bytes,1,opt,name=rap_id,json=rapId,proto3" json:"rap_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelSongConfigurationReq) Reset()         { *m = DelSongConfigurationReq{} }
func (m *DelSongConfigurationReq) String() string { return proto.CompactTextString(m) }
func (*DelSongConfigurationReq) ProtoMessage()    {}
func (*DelSongConfigurationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{45}
}
func (m *DelSongConfigurationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelSongConfigurationReq.Unmarshal(m, b)
}
func (m *DelSongConfigurationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelSongConfigurationReq.Marshal(b, m, deterministic)
}
func (dst *DelSongConfigurationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelSongConfigurationReq.Merge(dst, src)
}
func (m *DelSongConfigurationReq) XXX_Size() int {
	return xxx_messageInfo_DelSongConfigurationReq.Size(m)
}
func (m *DelSongConfigurationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelSongConfigurationReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelSongConfigurationReq proto.InternalMessageInfo

func (m *DelSongConfigurationReq) GetRapId() string {
	if m != nil {
		return m.RapId
	}
	return ""
}

type DelSongConfigurationResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelSongConfigurationResp) Reset()         { *m = DelSongConfigurationResp{} }
func (m *DelSongConfigurationResp) String() string { return proto.CompactTextString(m) }
func (*DelSongConfigurationResp) ProtoMessage()    {}
func (*DelSongConfigurationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{46}
}
func (m *DelSongConfigurationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelSongConfigurationResp.Unmarshal(m, b)
}
func (m *DelSongConfigurationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelSongConfigurationResp.Marshal(b, m, deterministic)
}
func (dst *DelSongConfigurationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelSongConfigurationResp.Merge(dst, src)
}
func (m *DelSongConfigurationResp) XXX_Size() int {
	return xxx_messageInfo_DelSongConfigurationResp.Size(m)
}
func (m *DelSongConfigurationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelSongConfigurationResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelSongConfigurationResp proto.InternalMessageInfo

// 移动歌曲位置
type MoveSongCategoryReq struct {
	RapId                string   `protobuf:"bytes,1,opt,name=rap_id,json=rapId,proto3" json:"rap_id,omitempty"`
	CurrentIndex         uint32   `protobuf:"varint,2,opt,name=current_index,json=currentIndex,proto3" json:"current_index,omitempty"`
	TargetIndex          uint32   `protobuf:"varint,3,opt,name=target_index,json=targetIndex,proto3" json:"target_index,omitempty"`
	TabId                string   `protobuf:"bytes,4,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	AiType               uint32   `protobuf:"varint,5,opt,name=ai_type,json=aiType,proto3" json:"ai_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MoveSongCategoryReq) Reset()         { *m = MoveSongCategoryReq{} }
func (m *MoveSongCategoryReq) String() string { return proto.CompactTextString(m) }
func (*MoveSongCategoryReq) ProtoMessage()    {}
func (*MoveSongCategoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{47}
}
func (m *MoveSongCategoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MoveSongCategoryReq.Unmarshal(m, b)
}
func (m *MoveSongCategoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MoveSongCategoryReq.Marshal(b, m, deterministic)
}
func (dst *MoveSongCategoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MoveSongCategoryReq.Merge(dst, src)
}
func (m *MoveSongCategoryReq) XXX_Size() int {
	return xxx_messageInfo_MoveSongCategoryReq.Size(m)
}
func (m *MoveSongCategoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MoveSongCategoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_MoveSongCategoryReq proto.InternalMessageInfo

func (m *MoveSongCategoryReq) GetRapId() string {
	if m != nil {
		return m.RapId
	}
	return ""
}

func (m *MoveSongCategoryReq) GetCurrentIndex() uint32 {
	if m != nil {
		return m.CurrentIndex
	}
	return 0
}

func (m *MoveSongCategoryReq) GetTargetIndex() uint32 {
	if m != nil {
		return m.TargetIndex
	}
	return 0
}

func (m *MoveSongCategoryReq) GetTabId() string {
	if m != nil {
		return m.TabId
	}
	return ""
}

func (m *MoveSongCategoryReq) GetAiType() uint32 {
	if m != nil {
		return m.AiType
	}
	return 0
}

type MoveSongCategoryResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MoveSongCategoryResp) Reset()         { *m = MoveSongCategoryResp{} }
func (m *MoveSongCategoryResp) String() string { return proto.CompactTextString(m) }
func (*MoveSongCategoryResp) ProtoMessage()    {}
func (*MoveSongCategoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{48}
}
func (m *MoveSongCategoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MoveSongCategoryResp.Unmarshal(m, b)
}
func (m *MoveSongCategoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MoveSongCategoryResp.Marshal(b, m, deterministic)
}
func (dst *MoveSongCategoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MoveSongCategoryResp.Merge(dst, src)
}
func (m *MoveSongCategoryResp) XXX_Size() int {
	return xxx_messageInfo_MoveSongCategoryResp.Size(m)
}
func (m *MoveSongCategoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MoveSongCategoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_MoveSongCategoryResp proto.InternalMessageInfo

type CopyMoveSongsToOtherTabReq struct {
	CopyMoveType         uint32   `protobuf:"varint,1,opt,name=copy_move_type,json=copyMoveType,proto3" json:"copy_move_type,omitempty"`
	SongIdList           []string `protobuf:"bytes,2,rep,name=song_id_list,json=songIdList,proto3" json:"song_id_list,omitempty"`
	TargetTabId          string   `protobuf:"bytes,3,opt,name=target_tab_id,json=targetTabId,proto3" json:"target_tab_id,omitempty"`
	AiType               uint32   `protobuf:"varint,5,opt,name=ai_type,json=aiType,proto3" json:"ai_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CopyMoveSongsToOtherTabReq) Reset()         { *m = CopyMoveSongsToOtherTabReq{} }
func (m *CopyMoveSongsToOtherTabReq) String() string { return proto.CompactTextString(m) }
func (*CopyMoveSongsToOtherTabReq) ProtoMessage()    {}
func (*CopyMoveSongsToOtherTabReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{49}
}
func (m *CopyMoveSongsToOtherTabReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CopyMoveSongsToOtherTabReq.Unmarshal(m, b)
}
func (m *CopyMoveSongsToOtherTabReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CopyMoveSongsToOtherTabReq.Marshal(b, m, deterministic)
}
func (dst *CopyMoveSongsToOtherTabReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CopyMoveSongsToOtherTabReq.Merge(dst, src)
}
func (m *CopyMoveSongsToOtherTabReq) XXX_Size() int {
	return xxx_messageInfo_CopyMoveSongsToOtherTabReq.Size(m)
}
func (m *CopyMoveSongsToOtherTabReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CopyMoveSongsToOtherTabReq.DiscardUnknown(m)
}

var xxx_messageInfo_CopyMoveSongsToOtherTabReq proto.InternalMessageInfo

func (m *CopyMoveSongsToOtherTabReq) GetCopyMoveType() uint32 {
	if m != nil {
		return m.CopyMoveType
	}
	return 0
}

func (m *CopyMoveSongsToOtherTabReq) GetSongIdList() []string {
	if m != nil {
		return m.SongIdList
	}
	return nil
}

func (m *CopyMoveSongsToOtherTabReq) GetTargetTabId() string {
	if m != nil {
		return m.TargetTabId
	}
	return ""
}

func (m *CopyMoveSongsToOtherTabReq) GetAiType() uint32 {
	if m != nil {
		return m.AiType
	}
	return 0
}

type CopyMoveSongsToOtherTabResp struct {
	DuplicativeSong      []*ResInfo `protobuf:"bytes,1,rep,name=duplicative_song,json=duplicativeSong,proto3" json:"duplicative_song,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *CopyMoveSongsToOtherTabResp) Reset()         { *m = CopyMoveSongsToOtherTabResp{} }
func (m *CopyMoveSongsToOtherTabResp) String() string { return proto.CompactTextString(m) }
func (*CopyMoveSongsToOtherTabResp) ProtoMessage()    {}
func (*CopyMoveSongsToOtherTabResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{50}
}
func (m *CopyMoveSongsToOtherTabResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CopyMoveSongsToOtherTabResp.Unmarshal(m, b)
}
func (m *CopyMoveSongsToOtherTabResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CopyMoveSongsToOtherTabResp.Marshal(b, m, deterministic)
}
func (dst *CopyMoveSongsToOtherTabResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CopyMoveSongsToOtherTabResp.Merge(dst, src)
}
func (m *CopyMoveSongsToOtherTabResp) XXX_Size() int {
	return xxx_messageInfo_CopyMoveSongsToOtherTabResp.Size(m)
}
func (m *CopyMoveSongsToOtherTabResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CopyMoveSongsToOtherTabResp.DiscardUnknown(m)
}

var xxx_messageInfo_CopyMoveSongsToOtherTabResp proto.InternalMessageInfo

func (m *CopyMoveSongsToOtherTabResp) GetDuplicativeSong() []*ResInfo {
	if m != nil {
		return m.DuplicativeSong
	}
	return nil
}

// rapper首页歌词列表
type GetLyricInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TabId                string   `protobuf:"bytes,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	Offset               uint32   `protobuf:"varint,4,opt,name=offset,proto3" json:"offset,omitempty"`
	AiType               uint32   `protobuf:"varint,5,opt,name=ai_type,json=aiType,proto3" json:"ai_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLyricInfoReq) Reset()         { *m = GetLyricInfoReq{} }
func (m *GetLyricInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetLyricInfoReq) ProtoMessage()    {}
func (*GetLyricInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{51}
}
func (m *GetLyricInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLyricInfoReq.Unmarshal(m, b)
}
func (m *GetLyricInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLyricInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetLyricInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLyricInfoReq.Merge(dst, src)
}
func (m *GetLyricInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetLyricInfoReq.Size(m)
}
func (m *GetLyricInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLyricInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLyricInfoReq proto.InternalMessageInfo

func (m *GetLyricInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetLyricInfoReq) GetTabId() string {
	if m != nil {
		return m.TabId
	}
	return ""
}

func (m *GetLyricInfoReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetLyricInfoReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetLyricInfoReq) GetAiType() uint32 {
	if m != nil {
		return m.AiType
	}
	return 0
}

// ai外语首页歌曲卡片的视频+按钮链接
type CardBGVideo struct {
	VideoUrl             string   `protobuf:"bytes,1,opt,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty"`
	ButtonUrl            string   `protobuf:"bytes,2,opt,name=button_url,json=buttonUrl,proto3" json:"button_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CardBGVideo) Reset()         { *m = CardBGVideo{} }
func (m *CardBGVideo) String() string { return proto.CompactTextString(m) }
func (*CardBGVideo) ProtoMessage()    {}
func (*CardBGVideo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{52}
}
func (m *CardBGVideo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardBGVideo.Unmarshal(m, b)
}
func (m *CardBGVideo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardBGVideo.Marshal(b, m, deterministic)
}
func (dst *CardBGVideo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardBGVideo.Merge(dst, src)
}
func (m *CardBGVideo) XXX_Size() int {
	return xxx_messageInfo_CardBGVideo.Size(m)
}
func (m *CardBGVideo) XXX_DiscardUnknown() {
	xxx_messageInfo_CardBGVideo.DiscardUnknown(m)
}

var xxx_messageInfo_CardBGVideo proto.InternalMessageInfo

func (m *CardBGVideo) GetVideoUrl() string {
	if m != nil {
		return m.VideoUrl
	}
	return ""
}

func (m *CardBGVideo) GetButtonUrl() string {
	if m != nil {
		return m.ButtonUrl
	}
	return ""
}

type LyricInfo struct {
	SongId                    string       `protobuf:"bytes,1,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	SongName                  string       `protobuf:"bytes,2,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	RapName                   string       `protobuf:"bytes,3,opt,name=rap_name,json=rapName,proto3" json:"rap_name,omitempty"`
	LyricContent              []string     `protobuf:"bytes,4,rep,name=lyric_content,json=lyricContent,proto3" json:"lyric_content,omitempty"`
	LyricUrl                  string       `protobuf:"bytes,5,opt,name=lyric_url,json=lyricUrl,proto3" json:"lyric_url,omitempty"`
	FileMd5                   string       `protobuf:"bytes,6,opt,name=file_md5,json=fileMd5,proto3" json:"file_md5,omitempty"`
	LevelName                 string       `protobuf:"bytes,7,opt,name=level_name,json=levelName,proto3" json:"level_name,omitempty"`
	LevelScore                uint32       `protobuf:"varint,8,opt,name=level_score,json=levelScore,proto3" json:"level_score,omitempty"`
	IsLocked                  bool         `protobuf:"varint,9,opt,name=is_locked,json=isLocked,proto3" json:"is_locked,omitempty"`
	NewLyricAUrl              string       `protobuf:"bytes,10,opt,name=new_lyric_a_url,json=newLyricAUrl,proto3" json:"new_lyric_a_url,omitempty"`
	NewLyricAMd5              string       `protobuf:"bytes,11,opt,name=new_lyric_a_md5,json=newLyricAMd5,proto3" json:"new_lyric_a_md5,omitempty"`
	NewLyricBUrl              string       `protobuf:"bytes,12,opt,name=new_lyric_b_url,json=newLyricBUrl,proto3" json:"new_lyric_b_url,omitempty"`
	NewLyricBMd5              string       `protobuf:"bytes,13,opt,name=new_lyric_b_md5,json=newLyricBMd5,proto3" json:"new_lyric_b_md5,omitempty"`
	MusicProducer             string       `protobuf:"bytes,14,opt,name=music_producer,json=musicProducer,proto3" json:"music_producer,omitempty"`
	FriendCircleHeat          uint64       `protobuf:"varint,15,opt,name=friend_circle_heat,json=friendCircleHeat,proto3" json:"friend_circle_heat,omitempty"`
	FriendCircleHeatDatumLine uint64       `protobuf:"varint,16,opt,name=friend_circle_heat_datum_line,json=friendCircleHeatDatumLine,proto3" json:"friend_circle_heat_datum_line,omitempty"`
	CardBgVideoUrl            *CardBGVideo `protobuf:"bytes,17,opt,name=card_bg_video_url,json=cardBgVideoUrl,proto3" json:"card_bg_video_url,omitempty"`
	XXX_NoUnkeyedLiteral      struct{}     `json:"-"`
	XXX_unrecognized          []byte       `json:"-"`
	XXX_sizecache             int32        `json:"-"`
}

func (m *LyricInfo) Reset()         { *m = LyricInfo{} }
func (m *LyricInfo) String() string { return proto.CompactTextString(m) }
func (*LyricInfo) ProtoMessage()    {}
func (*LyricInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{53}
}
func (m *LyricInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LyricInfo.Unmarshal(m, b)
}
func (m *LyricInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LyricInfo.Marshal(b, m, deterministic)
}
func (dst *LyricInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LyricInfo.Merge(dst, src)
}
func (m *LyricInfo) XXX_Size() int {
	return xxx_messageInfo_LyricInfo.Size(m)
}
func (m *LyricInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_LyricInfo.DiscardUnknown(m)
}

var xxx_messageInfo_LyricInfo proto.InternalMessageInfo

func (m *LyricInfo) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *LyricInfo) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

func (m *LyricInfo) GetRapName() string {
	if m != nil {
		return m.RapName
	}
	return ""
}

func (m *LyricInfo) GetLyricContent() []string {
	if m != nil {
		return m.LyricContent
	}
	return nil
}

func (m *LyricInfo) GetLyricUrl() string {
	if m != nil {
		return m.LyricUrl
	}
	return ""
}

func (m *LyricInfo) GetFileMd5() string {
	if m != nil {
		return m.FileMd5
	}
	return ""
}

func (m *LyricInfo) GetLevelName() string {
	if m != nil {
		return m.LevelName
	}
	return ""
}

func (m *LyricInfo) GetLevelScore() uint32 {
	if m != nil {
		return m.LevelScore
	}
	return 0
}

func (m *LyricInfo) GetIsLocked() bool {
	if m != nil {
		return m.IsLocked
	}
	return false
}

func (m *LyricInfo) GetNewLyricAUrl() string {
	if m != nil {
		return m.NewLyricAUrl
	}
	return ""
}

func (m *LyricInfo) GetNewLyricAMd5() string {
	if m != nil {
		return m.NewLyricAMd5
	}
	return ""
}

func (m *LyricInfo) GetNewLyricBUrl() string {
	if m != nil {
		return m.NewLyricBUrl
	}
	return ""
}

func (m *LyricInfo) GetNewLyricBMd5() string {
	if m != nil {
		return m.NewLyricBMd5
	}
	return ""
}

func (m *LyricInfo) GetMusicProducer() string {
	if m != nil {
		return m.MusicProducer
	}
	return ""
}

func (m *LyricInfo) GetFriendCircleHeat() uint64 {
	if m != nil {
		return m.FriendCircleHeat
	}
	return 0
}

func (m *LyricInfo) GetFriendCircleHeatDatumLine() uint64 {
	if m != nil {
		return m.FriendCircleHeatDatumLine
	}
	return 0
}

func (m *LyricInfo) GetCardBgVideoUrl() *CardBGVideo {
	if m != nil {
		return m.CardBgVideoUrl
	}
	return nil
}

type GetLyricInfoResp struct {
	LyricInfo            []*LyricInfo `protobuf:"bytes,1,rep,name=lyric_info,json=lyricInfo,proto3" json:"lyric_info,omitempty"`
	HasMore              bool         `protobuf:"varint,2,opt,name=has_more,json=hasMore,proto3" json:"has_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetLyricInfoResp) Reset()         { *m = GetLyricInfoResp{} }
func (m *GetLyricInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetLyricInfoResp) ProtoMessage()    {}
func (*GetLyricInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{54}
}
func (m *GetLyricInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLyricInfoResp.Unmarshal(m, b)
}
func (m *GetLyricInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLyricInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetLyricInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLyricInfoResp.Merge(dst, src)
}
func (m *GetLyricInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetLyricInfoResp.Size(m)
}
func (m *GetLyricInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLyricInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLyricInfoResp proto.InternalMessageInfo

func (m *GetLyricInfoResp) GetLyricInfo() []*LyricInfo {
	if m != nil {
		return m.LyricInfo
	}
	return nil
}

func (m *GetLyricInfoResp) GetHasMore() bool {
	if m != nil {
		return m.HasMore
	}
	return false
}

// 等级管理
type UpdateLevelReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	LevelName            string   `protobuf:"bytes,2,opt,name=level_name,json=levelName,proto3" json:"level_name,omitempty"`
	LevelScore           uint32   `protobuf:"varint,3,opt,name=level_score,json=levelScore,proto3" json:"level_score,omitempty"`
	Content              string   `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateLevelReq) Reset()         { *m = UpdateLevelReq{} }
func (m *UpdateLevelReq) String() string { return proto.CompactTextString(m) }
func (*UpdateLevelReq) ProtoMessage()    {}
func (*UpdateLevelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{55}
}
func (m *UpdateLevelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateLevelReq.Unmarshal(m, b)
}
func (m *UpdateLevelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateLevelReq.Marshal(b, m, deterministic)
}
func (dst *UpdateLevelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateLevelReq.Merge(dst, src)
}
func (m *UpdateLevelReq) XXX_Size() int {
	return xxx_messageInfo_UpdateLevelReq.Size(m)
}
func (m *UpdateLevelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateLevelReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateLevelReq proto.InternalMessageInfo

func (m *UpdateLevelReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *UpdateLevelReq) GetLevelName() string {
	if m != nil {
		return m.LevelName
	}
	return ""
}

func (m *UpdateLevelReq) GetLevelScore() uint32 {
	if m != nil {
		return m.LevelScore
	}
	return 0
}

func (m *UpdateLevelReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type UpdateLevelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateLevelResp) Reset()         { *m = UpdateLevelResp{} }
func (m *UpdateLevelResp) String() string { return proto.CompactTextString(m) }
func (*UpdateLevelResp) ProtoMessage()    {}
func (*UpdateLevelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{56}
}
func (m *UpdateLevelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateLevelResp.Unmarshal(m, b)
}
func (m *UpdateLevelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateLevelResp.Marshal(b, m, deterministic)
}
func (dst *UpdateLevelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateLevelResp.Merge(dst, src)
}
func (m *UpdateLevelResp) XXX_Size() int {
	return xxx_messageInfo_UpdateLevelResp.Size(m)
}
func (m *UpdateLevelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateLevelResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateLevelResp proto.InternalMessageInfo

type GetLevelInfoReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLevelInfoReq) Reset()         { *m = GetLevelInfoReq{} }
func (m *GetLevelInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetLevelInfoReq) ProtoMessage()    {}
func (*GetLevelInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{57}
}
func (m *GetLevelInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLevelInfoReq.Unmarshal(m, b)
}
func (m *GetLevelInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLevelInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetLevelInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLevelInfoReq.Merge(dst, src)
}
func (m *GetLevelInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetLevelInfoReq.Size(m)
}
func (m *GetLevelInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLevelInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLevelInfoReq proto.InternalMessageInfo

type LevelInfo struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	LevelName            string   `protobuf:"bytes,2,opt,name=level_name,json=levelName,proto3" json:"level_name,omitempty"`
	LevelScore           uint32   `protobuf:"varint,3,opt,name=level_score,json=levelScore,proto3" json:"level_score,omitempty"`
	Content              string   `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,5,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	ReleationSong        uint32   `protobuf:"varint,6,opt,name=releation_song,json=releationSong,proto3" json:"releation_song,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LevelInfo) Reset()         { *m = LevelInfo{} }
func (m *LevelInfo) String() string { return proto.CompactTextString(m) }
func (*LevelInfo) ProtoMessage()    {}
func (*LevelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{58}
}
func (m *LevelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelInfo.Unmarshal(m, b)
}
func (m *LevelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelInfo.Marshal(b, m, deterministic)
}
func (dst *LevelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelInfo.Merge(dst, src)
}
func (m *LevelInfo) XXX_Size() int {
	return xxx_messageInfo_LevelInfo.Size(m)
}
func (m *LevelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_LevelInfo proto.InternalMessageInfo

func (m *LevelInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *LevelInfo) GetLevelName() string {
	if m != nil {
		return m.LevelName
	}
	return ""
}

func (m *LevelInfo) GetLevelScore() uint32 {
	if m != nil {
		return m.LevelScore
	}
	return 0
}

func (m *LevelInfo) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *LevelInfo) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *LevelInfo) GetReleationSong() uint32 {
	if m != nil {
		return m.ReleationSong
	}
	return 0
}

type GetLevelInfoResp struct {
	LevelInfo            []*LevelInfo `protobuf:"bytes,1,rep,name=level_info,json=levelInfo,proto3" json:"level_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetLevelInfoResp) Reset()         { *m = GetLevelInfoResp{} }
func (m *GetLevelInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetLevelInfoResp) ProtoMessage()    {}
func (*GetLevelInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{59}
}
func (m *GetLevelInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLevelInfoResp.Unmarshal(m, b)
}
func (m *GetLevelInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLevelInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetLevelInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLevelInfoResp.Merge(dst, src)
}
func (m *GetLevelInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetLevelInfoResp.Size(m)
}
func (m *GetLevelInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLevelInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLevelInfoResp proto.InternalMessageInfo

func (m *GetLevelInfoResp) GetLevelInfo() []*LevelInfo {
	if m != nil {
		return m.LevelInfo
	}
	return nil
}

type DelLevelInfoReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelLevelInfoReq) Reset()         { *m = DelLevelInfoReq{} }
func (m *DelLevelInfoReq) String() string { return proto.CompactTextString(m) }
func (*DelLevelInfoReq) ProtoMessage()    {}
func (*DelLevelInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{60}
}
func (m *DelLevelInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelLevelInfoReq.Unmarshal(m, b)
}
func (m *DelLevelInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelLevelInfoReq.Marshal(b, m, deterministic)
}
func (dst *DelLevelInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelLevelInfoReq.Merge(dst, src)
}
func (m *DelLevelInfoReq) XXX_Size() int {
	return xxx_messageInfo_DelLevelInfoReq.Size(m)
}
func (m *DelLevelInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelLevelInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelLevelInfoReq proto.InternalMessageInfo

func (m *DelLevelInfoReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type DelLevelInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelLevelInfoResp) Reset()         { *m = DelLevelInfoResp{} }
func (m *DelLevelInfoResp) String() string { return proto.CompactTextString(m) }
func (*DelLevelInfoResp) ProtoMessage()    {}
func (*DelLevelInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{61}
}
func (m *DelLevelInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelLevelInfoResp.Unmarshal(m, b)
}
func (m *DelLevelInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelLevelInfoResp.Marshal(b, m, deterministic)
}
func (dst *DelLevelInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelLevelInfoResp.Merge(dst, src)
}
func (m *DelLevelInfoResp) XXX_Size() int {
	return xxx_messageInfo_DelLevelInfoResp.Size(m)
}
func (m *DelLevelInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelLevelInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelLevelInfoResp proto.InternalMessageInfo

// 等级
type LevelConfig struct {
	LevelName            string   `protobuf:"bytes,1,opt,name=level_name,json=levelName,proto3" json:"level_name,omitempty"`
	MinNum               uint32   `protobuf:"varint,2,opt,name=min_num,json=minNum,proto3" json:"min_num,omitempty"`
	MaxNum               uint32   `protobuf:"varint,3,opt,name=max_num,json=maxNum,proto3" json:"max_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LevelConfig) Reset()         { *m = LevelConfig{} }
func (m *LevelConfig) String() string { return proto.CompactTextString(m) }
func (*LevelConfig) ProtoMessage()    {}
func (*LevelConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{62}
}
func (m *LevelConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelConfig.Unmarshal(m, b)
}
func (m *LevelConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelConfig.Marshal(b, m, deterministic)
}
func (dst *LevelConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelConfig.Merge(dst, src)
}
func (m *LevelConfig) XXX_Size() int {
	return xxx_messageInfo_LevelConfig.Size(m)
}
func (m *LevelConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelConfig.DiscardUnknown(m)
}

var xxx_messageInfo_LevelConfig proto.InternalMessageInfo

func (m *LevelConfig) GetLevelName() string {
	if m != nil {
		return m.LevelName
	}
	return ""
}

func (m *LevelConfig) GetMinNum() uint32 {
	if m != nil {
		return m.MinNum
	}
	return 0
}

func (m *LevelConfig) GetMaxNum() uint32 {
	if m != nil {
		return m.MaxNum
	}
	return 0
}

type UserLevel struct {
	Uid                  uint32       `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string       `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Level                *LevelConfig `protobuf:"bytes,3,opt,name=level,proto3" json:"level,omitempty"`
	Point                uint32       `protobuf:"varint,4,opt,name=point,proto3" json:"point,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UserLevel) Reset()         { *m = UserLevel{} }
func (m *UserLevel) String() string { return proto.CompactTextString(m) }
func (*UserLevel) ProtoMessage()    {}
func (*UserLevel) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{63}
}
func (m *UserLevel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserLevel.Unmarshal(m, b)
}
func (m *UserLevel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserLevel.Marshal(b, m, deterministic)
}
func (dst *UserLevel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserLevel.Merge(dst, src)
}
func (m *UserLevel) XXX_Size() int {
	return xxx_messageInfo_UserLevel.Size(m)
}
func (m *UserLevel) XXX_DiscardUnknown() {
	xxx_messageInfo_UserLevel.DiscardUnknown(m)
}

var xxx_messageInfo_UserLevel proto.InternalMessageInfo

func (m *UserLevel) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserLevel) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserLevel) GetLevel() *LevelConfig {
	if m != nil {
		return m.Level
	}
	return nil
}

func (m *UserLevel) GetPoint() uint32 {
	if m != nil {
		return m.Point
	}
	return 0
}

type GetUserAIRapperLevelReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserAIRapperLevelReq) Reset()         { *m = GetUserAIRapperLevelReq{} }
func (m *GetUserAIRapperLevelReq) String() string { return proto.CompactTextString(m) }
func (*GetUserAIRapperLevelReq) ProtoMessage()    {}
func (*GetUserAIRapperLevelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{64}
}
func (m *GetUserAIRapperLevelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAIRapperLevelReq.Unmarshal(m, b)
}
func (m *GetUserAIRapperLevelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAIRapperLevelReq.Marshal(b, m, deterministic)
}
func (dst *GetUserAIRapperLevelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAIRapperLevelReq.Merge(dst, src)
}
func (m *GetUserAIRapperLevelReq) XXX_Size() int {
	return xxx_messageInfo_GetUserAIRapperLevelReq.Size(m)
}
func (m *GetUserAIRapperLevelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAIRapperLevelReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAIRapperLevelReq proto.InternalMessageInfo

func (m *GetUserAIRapperLevelReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserAIRapperLevelResp struct {
	UserLevel            *UserLevel `protobuf:"bytes,1,opt,name=user_level,json=userLevel,proto3" json:"user_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetUserAIRapperLevelResp) Reset()         { *m = GetUserAIRapperLevelResp{} }
func (m *GetUserAIRapperLevelResp) String() string { return proto.CompactTextString(m) }
func (*GetUserAIRapperLevelResp) ProtoMessage()    {}
func (*GetUserAIRapperLevelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{65}
}
func (m *GetUserAIRapperLevelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAIRapperLevelResp.Unmarshal(m, b)
}
func (m *GetUserAIRapperLevelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAIRapperLevelResp.Marshal(b, m, deterministic)
}
func (dst *GetUserAIRapperLevelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAIRapperLevelResp.Merge(dst, src)
}
func (m *GetUserAIRapperLevelResp) XXX_Size() int {
	return xxx_messageInfo_GetUserAIRapperLevelResp.Size(m)
}
func (m *GetUserAIRapperLevelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAIRapperLevelResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAIRapperLevelResp proto.InternalMessageInfo

func (m *GetUserAIRapperLevelResp) GetUserLevel() *UserLevel {
	if m != nil {
		return m.UserLevel
	}
	return nil
}

// 模拟kafka
type PushPartialKafkaReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Timestamp            uint32   `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	VoiceStartSecond     uint32   `protobuf:"varint,3,opt,name=voice_start_second,json=voiceStartSecond,proto3" json:"voice_start_second,omitempty"`
	Score                uint32   `protobuf:"varint,4,opt,name=score,proto3" json:"score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushPartialKafkaReq) Reset()         { *m = PushPartialKafkaReq{} }
func (m *PushPartialKafkaReq) String() string { return proto.CompactTextString(m) }
func (*PushPartialKafkaReq) ProtoMessage()    {}
func (*PushPartialKafkaReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{66}
}
func (m *PushPartialKafkaReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushPartialKafkaReq.Unmarshal(m, b)
}
func (m *PushPartialKafkaReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushPartialKafkaReq.Marshal(b, m, deterministic)
}
func (dst *PushPartialKafkaReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushPartialKafkaReq.Merge(dst, src)
}
func (m *PushPartialKafkaReq) XXX_Size() int {
	return xxx_messageInfo_PushPartialKafkaReq.Size(m)
}
func (m *PushPartialKafkaReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PushPartialKafkaReq.DiscardUnknown(m)
}

var xxx_messageInfo_PushPartialKafkaReq proto.InternalMessageInfo

func (m *PushPartialKafkaReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PushPartialKafkaReq) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *PushPartialKafkaReq) GetVoiceStartSecond() uint32 {
	if m != nil {
		return m.VoiceStartSecond
	}
	return 0
}

func (m *PushPartialKafkaReq) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

type PushPartialKafkaResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushPartialKafkaResp) Reset()         { *m = PushPartialKafkaResp{} }
func (m *PushPartialKafkaResp) String() string { return proto.CompactTextString(m) }
func (*PushPartialKafkaResp) ProtoMessage()    {}
func (*PushPartialKafkaResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{67}
}
func (m *PushPartialKafkaResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushPartialKafkaResp.Unmarshal(m, b)
}
func (m *PushPartialKafkaResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushPartialKafkaResp.Marshal(b, m, deterministic)
}
func (dst *PushPartialKafkaResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushPartialKafkaResp.Merge(dst, src)
}
func (m *PushPartialKafkaResp) XXX_Size() int {
	return xxx_messageInfo_PushPartialKafkaResp.Size(m)
}
func (m *PushPartialKafkaResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PushPartialKafkaResp.DiscardUnknown(m)
}

var xxx_messageInfo_PushPartialKafkaResp proto.InternalMessageInfo

type PushComposeKafkaReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Timestamp            uint32   `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	VoiceUrl             string   `protobuf:"bytes,3,opt,name=voice_url,json=voiceUrl,proto3" json:"voice_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushComposeKafkaReq) Reset()         { *m = PushComposeKafkaReq{} }
func (m *PushComposeKafkaReq) String() string { return proto.CompactTextString(m) }
func (*PushComposeKafkaReq) ProtoMessage()    {}
func (*PushComposeKafkaReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{68}
}
func (m *PushComposeKafkaReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushComposeKafkaReq.Unmarshal(m, b)
}
func (m *PushComposeKafkaReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushComposeKafkaReq.Marshal(b, m, deterministic)
}
func (dst *PushComposeKafkaReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushComposeKafkaReq.Merge(dst, src)
}
func (m *PushComposeKafkaReq) XXX_Size() int {
	return xxx_messageInfo_PushComposeKafkaReq.Size(m)
}
func (m *PushComposeKafkaReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PushComposeKafkaReq.DiscardUnknown(m)
}

var xxx_messageInfo_PushComposeKafkaReq proto.InternalMessageInfo

func (m *PushComposeKafkaReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PushComposeKafkaReq) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *PushComposeKafkaReq) GetVoiceUrl() string {
	if m != nil {
		return m.VoiceUrl
	}
	return ""
}

type PushComposeKafkaResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushComposeKafkaResp) Reset()         { *m = PushComposeKafkaResp{} }
func (m *PushComposeKafkaResp) String() string { return proto.CompactTextString(m) }
func (*PushComposeKafkaResp) ProtoMessage()    {}
func (*PushComposeKafkaResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{69}
}
func (m *PushComposeKafkaResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushComposeKafkaResp.Unmarshal(m, b)
}
func (m *PushComposeKafkaResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushComposeKafkaResp.Marshal(b, m, deterministic)
}
func (dst *PushComposeKafkaResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushComposeKafkaResp.Merge(dst, src)
}
func (m *PushComposeKafkaResp) XXX_Size() int {
	return xxx_messageInfo_PushComposeKafkaResp.Size(m)
}
func (m *PushComposeKafkaResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PushComposeKafkaResp.DiscardUnknown(m)
}

var xxx_messageInfo_PushComposeKafkaResp proto.InternalMessageInfo

// 模拟增加点赞数
type AddUserLevelAttitudeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AttitudeCnt          int32    `protobuf:"varint,2,opt,name=attitude_cnt,json=attitudeCnt,proto3" json:"attitude_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUserLevelAttitudeReq) Reset()         { *m = AddUserLevelAttitudeReq{} }
func (m *AddUserLevelAttitudeReq) String() string { return proto.CompactTextString(m) }
func (*AddUserLevelAttitudeReq) ProtoMessage()    {}
func (*AddUserLevelAttitudeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{70}
}
func (m *AddUserLevelAttitudeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserLevelAttitudeReq.Unmarshal(m, b)
}
func (m *AddUserLevelAttitudeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserLevelAttitudeReq.Marshal(b, m, deterministic)
}
func (dst *AddUserLevelAttitudeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserLevelAttitudeReq.Merge(dst, src)
}
func (m *AddUserLevelAttitudeReq) XXX_Size() int {
	return xxx_messageInfo_AddUserLevelAttitudeReq.Size(m)
}
func (m *AddUserLevelAttitudeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserLevelAttitudeReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserLevelAttitudeReq proto.InternalMessageInfo

func (m *AddUserLevelAttitudeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddUserLevelAttitudeReq) GetAttitudeCnt() int32 {
	if m != nil {
		return m.AttitudeCnt
	}
	return 0
}

type AddUserLevelAttitudeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUserLevelAttitudeResp) Reset()         { *m = AddUserLevelAttitudeResp{} }
func (m *AddUserLevelAttitudeResp) String() string { return proto.CompactTextString(m) }
func (*AddUserLevelAttitudeResp) ProtoMessage()    {}
func (*AddUserLevelAttitudeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{71}
}
func (m *AddUserLevelAttitudeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserLevelAttitudeResp.Unmarshal(m, b)
}
func (m *AddUserLevelAttitudeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserLevelAttitudeResp.Marshal(b, m, deterministic)
}
func (dst *AddUserLevelAttitudeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserLevelAttitudeResp.Merge(dst, src)
}
func (m *AddUserLevelAttitudeResp) XXX_Size() int {
	return xxx_messageInfo_AddUserLevelAttitudeResp.Size(m)
}
func (m *AddUserLevelAttitudeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserLevelAttitudeResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserLevelAttitudeResp proto.InternalMessageInfo

// 用户发帖成功后，用户提高用户rapper等级
type PostAIRapperPostReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PostId               string   `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PostAIRapperPostReq) Reset()         { *m = PostAIRapperPostReq{} }
func (m *PostAIRapperPostReq) String() string { return proto.CompactTextString(m) }
func (*PostAIRapperPostReq) ProtoMessage()    {}
func (*PostAIRapperPostReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{72}
}
func (m *PostAIRapperPostReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostAIRapperPostReq.Unmarshal(m, b)
}
func (m *PostAIRapperPostReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostAIRapperPostReq.Marshal(b, m, deterministic)
}
func (dst *PostAIRapperPostReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostAIRapperPostReq.Merge(dst, src)
}
func (m *PostAIRapperPostReq) XXX_Size() int {
	return xxx_messageInfo_PostAIRapperPostReq.Size(m)
}
func (m *PostAIRapperPostReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PostAIRapperPostReq.DiscardUnknown(m)
}

var xxx_messageInfo_PostAIRapperPostReq proto.InternalMessageInfo

func (m *PostAIRapperPostReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PostAIRapperPostReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

type PostAIRapperPostResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PostAIRapperPostResp) Reset()         { *m = PostAIRapperPostResp{} }
func (m *PostAIRapperPostResp) String() string { return proto.CompactTextString(m) }
func (*PostAIRapperPostResp) ProtoMessage()    {}
func (*PostAIRapperPostResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{73}
}
func (m *PostAIRapperPostResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostAIRapperPostResp.Unmarshal(m, b)
}
func (m *PostAIRapperPostResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostAIRapperPostResp.Marshal(b, m, deterministic)
}
func (dst *PostAIRapperPostResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostAIRapperPostResp.Merge(dst, src)
}
func (m *PostAIRapperPostResp) XXX_Size() int {
	return xxx_messageInfo_PostAIRapperPostResp.Size(m)
}
func (m *PostAIRapperPostResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PostAIRapperPostResp.DiscardUnknown(m)
}

var xxx_messageInfo_PostAIRapperPostResp proto.InternalMessageInfo

type CheckPositionStatusReq struct {
	SongName             string   `protobuf:"bytes,1,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	SongRapper           string   `protobuf:"bytes,2,opt,name=song_rapper,json=songRapper,proto3" json:"song_rapper,omitempty"`
	SongPosition         uint32   `protobuf:"varint,3,opt,name=song_position,json=songPosition,proto3" json:"song_position,omitempty"`
	AiType               uint32   `protobuf:"varint,4,opt,name=ai_type,json=aiType,proto3" json:"ai_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckPositionStatusReq) Reset()         { *m = CheckPositionStatusReq{} }
func (m *CheckPositionStatusReq) String() string { return proto.CompactTextString(m) }
func (*CheckPositionStatusReq) ProtoMessage()    {}
func (*CheckPositionStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{74}
}
func (m *CheckPositionStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckPositionStatusReq.Unmarshal(m, b)
}
func (m *CheckPositionStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckPositionStatusReq.Marshal(b, m, deterministic)
}
func (dst *CheckPositionStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckPositionStatusReq.Merge(dst, src)
}
func (m *CheckPositionStatusReq) XXX_Size() int {
	return xxx_messageInfo_CheckPositionStatusReq.Size(m)
}
func (m *CheckPositionStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckPositionStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckPositionStatusReq proto.InternalMessageInfo

func (m *CheckPositionStatusReq) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

func (m *CheckPositionStatusReq) GetSongRapper() string {
	if m != nil {
		return m.SongRapper
	}
	return ""
}

func (m *CheckPositionStatusReq) GetSongPosition() uint32 {
	if m != nil {
		return m.SongPosition
	}
	return 0
}

func (m *CheckPositionStatusReq) GetAiType() uint32 {
	if m != nil {
		return m.AiType
	}
	return 0
}

type CheckPositionStatusResp struct {
	ResInfo              *ResInfo `protobuf:"bytes,1,opt,name=res_info,json=resInfo,proto3" json:"res_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckPositionStatusResp) Reset()         { *m = CheckPositionStatusResp{} }
func (m *CheckPositionStatusResp) String() string { return proto.CompactTextString(m) }
func (*CheckPositionStatusResp) ProtoMessage()    {}
func (*CheckPositionStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{75}
}
func (m *CheckPositionStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckPositionStatusResp.Unmarshal(m, b)
}
func (m *CheckPositionStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckPositionStatusResp.Marshal(b, m, deterministic)
}
func (dst *CheckPositionStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckPositionStatusResp.Merge(dst, src)
}
func (m *CheckPositionStatusResp) XXX_Size() int {
	return xxx_messageInfo_CheckPositionStatusResp.Size(m)
}
func (m *CheckPositionStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckPositionStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckPositionStatusResp proto.InternalMessageInfo

func (m *CheckPositionStatusResp) GetResInfo() *ResInfo {
	if m != nil {
		return m.ResInfo
	}
	return nil
}

type AggregationInfo struct {
	AggregationChoose    AggregationChoose `protobuf:"varint,1,opt,name=aggregation_choose,json=aggregationChoose,proto3,enum=rhythm.airapper.AggregationChoose" json:"aggregation_choose,omitempty"`
	SongId               string            `protobuf:"bytes,2,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	Number               uint32            `protobuf:"varint,3,opt,name=number,proto3" json:"number,omitempty"`
	Uid                  uint32            `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *AggregationInfo) Reset()         { *m = AggregationInfo{} }
func (m *AggregationInfo) String() string { return proto.CompactTextString(m) }
func (*AggregationInfo) ProtoMessage()    {}
func (*AggregationInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{76}
}
func (m *AggregationInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AggregationInfo.Unmarshal(m, b)
}
func (m *AggregationInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AggregationInfo.Marshal(b, m, deterministic)
}
func (dst *AggregationInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AggregationInfo.Merge(dst, src)
}
func (m *AggregationInfo) XXX_Size() int {
	return xxx_messageInfo_AggregationInfo.Size(m)
}
func (m *AggregationInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AggregationInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AggregationInfo proto.InternalMessageInfo

func (m *AggregationInfo) GetAggregationChoose() AggregationChoose {
	if m != nil {
		return m.AggregationChoose
	}
	return AggregationChoose_EXposure
}

func (m *AggregationInfo) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *AggregationInfo) GetNumber() uint32 {
	if m != nil {
		return m.Number
	}
	return 0
}

func (m *AggregationInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type AggregationInfoReq struct {
	AggregationInfo      []*AggregationInfo `protobuf:"bytes,1,rep,name=aggregation_info,json=aggregationInfo,proto3" json:"aggregation_info,omitempty"`
	AiType               uint32             `protobuf:"varint,2,opt,name=ai_type,json=aiType,proto3" json:"ai_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *AggregationInfoReq) Reset()         { *m = AggregationInfoReq{} }
func (m *AggregationInfoReq) String() string { return proto.CompactTextString(m) }
func (*AggregationInfoReq) ProtoMessage()    {}
func (*AggregationInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{77}
}
func (m *AggregationInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AggregationInfoReq.Unmarshal(m, b)
}
func (m *AggregationInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AggregationInfoReq.Marshal(b, m, deterministic)
}
func (dst *AggregationInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AggregationInfoReq.Merge(dst, src)
}
func (m *AggregationInfoReq) XXX_Size() int {
	return xxx_messageInfo_AggregationInfoReq.Size(m)
}
func (m *AggregationInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AggregationInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_AggregationInfoReq proto.InternalMessageInfo

func (m *AggregationInfoReq) GetAggregationInfo() []*AggregationInfo {
	if m != nil {
		return m.AggregationInfo
	}
	return nil
}

func (m *AggregationInfoReq) GetAiType() uint32 {
	if m != nil {
		return m.AiType
	}
	return 0
}

type AggregationInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AggregationInfoResp) Reset()         { *m = AggregationInfoResp{} }
func (m *AggregationInfoResp) String() string { return proto.CompactTextString(m) }
func (*AggregationInfoResp) ProtoMessage()    {}
func (*AggregationInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{78}
}
func (m *AggregationInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AggregationInfoResp.Unmarshal(m, b)
}
func (m *AggregationInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AggregationInfoResp.Marshal(b, m, deterministic)
}
func (dst *AggregationInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AggregationInfoResp.Merge(dst, src)
}
func (m *AggregationInfoResp) XXX_Size() int {
	return xxx_messageInfo_AggregationInfoResp.Size(m)
}
func (m *AggregationInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AggregationInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_AggregationInfoResp proto.InternalMessageInfo

// 移动推荐下的tab
type SongPositionInfo struct {
	SongId               string   `protobuf:"bytes,1,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	SongPosition         uint32   `protobuf:"varint,2,opt,name=song_position,json=songPosition,proto3" json:"song_position,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SongPositionInfo) Reset()         { *m = SongPositionInfo{} }
func (m *SongPositionInfo) String() string { return proto.CompactTextString(m) }
func (*SongPositionInfo) ProtoMessage()    {}
func (*SongPositionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{79}
}
func (m *SongPositionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SongPositionInfo.Unmarshal(m, b)
}
func (m *SongPositionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SongPositionInfo.Marshal(b, m, deterministic)
}
func (dst *SongPositionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SongPositionInfo.Merge(dst, src)
}
func (m *SongPositionInfo) XXX_Size() int {
	return xxx_messageInfo_SongPositionInfo.Size(m)
}
func (m *SongPositionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SongPositionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SongPositionInfo proto.InternalMessageInfo

func (m *SongPositionInfo) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *SongPositionInfo) GetSongPosition() uint32 {
	if m != nil {
		return m.SongPosition
	}
	return 0
}

type MoveSongPositionReq struct {
	SongPositionInfo     []*SongPositionInfo `protobuf:"bytes,1,rep,name=song_position_info,json=songPositionInfo,proto3" json:"song_position_info,omitempty"`
	AiType               uint32              `protobuf:"varint,2,opt,name=ai_type,json=aiType,proto3" json:"ai_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *MoveSongPositionReq) Reset()         { *m = MoveSongPositionReq{} }
func (m *MoveSongPositionReq) String() string { return proto.CompactTextString(m) }
func (*MoveSongPositionReq) ProtoMessage()    {}
func (*MoveSongPositionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{80}
}
func (m *MoveSongPositionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MoveSongPositionReq.Unmarshal(m, b)
}
func (m *MoveSongPositionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MoveSongPositionReq.Marshal(b, m, deterministic)
}
func (dst *MoveSongPositionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MoveSongPositionReq.Merge(dst, src)
}
func (m *MoveSongPositionReq) XXX_Size() int {
	return xxx_messageInfo_MoveSongPositionReq.Size(m)
}
func (m *MoveSongPositionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MoveSongPositionReq.DiscardUnknown(m)
}

var xxx_messageInfo_MoveSongPositionReq proto.InternalMessageInfo

func (m *MoveSongPositionReq) GetSongPositionInfo() []*SongPositionInfo {
	if m != nil {
		return m.SongPositionInfo
	}
	return nil
}

func (m *MoveSongPositionReq) GetAiType() uint32 {
	if m != nil {
		return m.AiType
	}
	return 0
}

type MoveSongPositionResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MoveSongPositionResp) Reset()         { *m = MoveSongPositionResp{} }
func (m *MoveSongPositionResp) String() string { return proto.CompactTextString(m) }
func (*MoveSongPositionResp) ProtoMessage()    {}
func (*MoveSongPositionResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{81}
}
func (m *MoveSongPositionResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MoveSongPositionResp.Unmarshal(m, b)
}
func (m *MoveSongPositionResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MoveSongPositionResp.Marshal(b, m, deterministic)
}
func (dst *MoveSongPositionResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MoveSongPositionResp.Merge(dst, src)
}
func (m *MoveSongPositionResp) XXX_Size() int {
	return xxx_messageInfo_MoveSongPositionResp.Size(m)
}
func (m *MoveSongPositionResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MoveSongPositionResp.DiscardUnknown(m)
}

var xxx_messageInfo_MoveSongPositionResp proto.InternalMessageInfo

// ai 说唱三期 分享朋友圈
type GetH5UrlWithAICtxReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BackgroundVideoUrl   string   `protobuf:"bytes,2,opt,name=background_video_url,json=backgroundVideoUrl,proto3" json:"background_video_url,omitempty"`
	VoiceUrl             string   `protobuf:"bytes,3,opt,name=voice_url,json=voiceUrl,proto3" json:"voice_url,omitempty"`
	SongName             string   `protobuf:"bytes,4,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	Lyrics               []string `protobuf:"bytes,5,rep,name=lyrics,proto3" json:"lyrics,omitempty"`
	SongId               string   `protobuf:"bytes,6,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	MarketId             uint32   `protobuf:"varint,7,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ClientType           uint32   `protobuf:"varint,8,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	AiType               uint32   `protobuf:"varint,9,opt,name=ai_type,json=aiType,proto3" json:"ai_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetH5UrlWithAICtxReq) Reset()         { *m = GetH5UrlWithAICtxReq{} }
func (m *GetH5UrlWithAICtxReq) String() string { return proto.CompactTextString(m) }
func (*GetH5UrlWithAICtxReq) ProtoMessage()    {}
func (*GetH5UrlWithAICtxReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{82}
}
func (m *GetH5UrlWithAICtxReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetH5UrlWithAICtxReq.Unmarshal(m, b)
}
func (m *GetH5UrlWithAICtxReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetH5UrlWithAICtxReq.Marshal(b, m, deterministic)
}
func (dst *GetH5UrlWithAICtxReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetH5UrlWithAICtxReq.Merge(dst, src)
}
func (m *GetH5UrlWithAICtxReq) XXX_Size() int {
	return xxx_messageInfo_GetH5UrlWithAICtxReq.Size(m)
}
func (m *GetH5UrlWithAICtxReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetH5UrlWithAICtxReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetH5UrlWithAICtxReq proto.InternalMessageInfo

func (m *GetH5UrlWithAICtxReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetH5UrlWithAICtxReq) GetBackgroundVideoUrl() string {
	if m != nil {
		return m.BackgroundVideoUrl
	}
	return ""
}

func (m *GetH5UrlWithAICtxReq) GetVoiceUrl() string {
	if m != nil {
		return m.VoiceUrl
	}
	return ""
}

func (m *GetH5UrlWithAICtxReq) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

func (m *GetH5UrlWithAICtxReq) GetLyrics() []string {
	if m != nil {
		return m.Lyrics
	}
	return nil
}

func (m *GetH5UrlWithAICtxReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *GetH5UrlWithAICtxReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *GetH5UrlWithAICtxReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *GetH5UrlWithAICtxReq) GetAiType() uint32 {
	if m != nil {
		return m.AiType
	}
	return 0
}

type GetH5UrlWithAICtxResp struct {
	H5Url                string   `protobuf:"bytes,1,opt,name=h5_url,json=h5Url,proto3" json:"h5_url,omitempty"`
	MainTitle            string   `protobuf:"bytes,2,opt,name=main_title,json=mainTitle,proto3" json:"main_title,omitempty"`
	SubTitle             string   `protobuf:"bytes,3,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetH5UrlWithAICtxResp) Reset()         { *m = GetH5UrlWithAICtxResp{} }
func (m *GetH5UrlWithAICtxResp) String() string { return proto.CompactTextString(m) }
func (*GetH5UrlWithAICtxResp) ProtoMessage()    {}
func (*GetH5UrlWithAICtxResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{83}
}
func (m *GetH5UrlWithAICtxResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetH5UrlWithAICtxResp.Unmarshal(m, b)
}
func (m *GetH5UrlWithAICtxResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetH5UrlWithAICtxResp.Marshal(b, m, deterministic)
}
func (dst *GetH5UrlWithAICtxResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetH5UrlWithAICtxResp.Merge(dst, src)
}
func (m *GetH5UrlWithAICtxResp) XXX_Size() int {
	return xxx_messageInfo_GetH5UrlWithAICtxResp.Size(m)
}
func (m *GetH5UrlWithAICtxResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetH5UrlWithAICtxResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetH5UrlWithAICtxResp proto.InternalMessageInfo

func (m *GetH5UrlWithAICtxResp) GetH5Url() string {
	if m != nil {
		return m.H5Url
	}
	return ""
}

func (m *GetH5UrlWithAICtxResp) GetMainTitle() string {
	if m != nil {
		return m.MainTitle
	}
	return ""
}

func (m *GetH5UrlWithAICtxResp) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

type GetAICtxByUUIDReq struct {
	Uuid                 string   `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAICtxByUUIDReq) Reset()         { *m = GetAICtxByUUIDReq{} }
func (m *GetAICtxByUUIDReq) String() string { return proto.CompactTextString(m) }
func (*GetAICtxByUUIDReq) ProtoMessage()    {}
func (*GetAICtxByUUIDReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{84}
}
func (m *GetAICtxByUUIDReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAICtxByUUIDReq.Unmarshal(m, b)
}
func (m *GetAICtxByUUIDReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAICtxByUUIDReq.Marshal(b, m, deterministic)
}
func (dst *GetAICtxByUUIDReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAICtxByUUIDReq.Merge(dst, src)
}
func (m *GetAICtxByUUIDReq) XXX_Size() int {
	return xxx_messageInfo_GetAICtxByUUIDReq.Size(m)
}
func (m *GetAICtxByUUIDReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAICtxByUUIDReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAICtxByUUIDReq proto.InternalMessageInfo

func (m *GetAICtxByUUIDReq) GetUuid() string {
	if m != nil {
		return m.Uuid
	}
	return ""
}

type GetAICtxByUUIDResp struct {
	NickName             string   `protobuf:"bytes,1,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	BackgroundVideoUrl   string   `protobuf:"bytes,2,opt,name=background_video_url,json=backgroundVideoUrl,proto3" json:"background_video_url,omitempty"`
	VoiceUrl             string   `protobuf:"bytes,3,opt,name=voice_url,json=voiceUrl,proto3" json:"voice_url,omitempty"`
	SongName             string   `protobuf:"bytes,4,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	Lyrics               []string `protobuf:"bytes,5,rep,name=lyrics,proto3" json:"lyrics,omitempty"`
	StageName            string   `protobuf:"bytes,6,opt,name=stage_name,json=stageName,proto3" json:"stage_name,omitempty"`
	UserName             string   `protobuf:"bytes,7,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	ShareUid             uint32   `protobuf:"varint,8,opt,name=share_uid,json=shareUid,proto3" json:"share_uid,omitempty"`
	AiType               uint32   `protobuf:"varint,9,opt,name=ai_type,json=aiType,proto3" json:"ai_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAICtxByUUIDResp) Reset()         { *m = GetAICtxByUUIDResp{} }
func (m *GetAICtxByUUIDResp) String() string { return proto.CompactTextString(m) }
func (*GetAICtxByUUIDResp) ProtoMessage()    {}
func (*GetAICtxByUUIDResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{85}
}
func (m *GetAICtxByUUIDResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAICtxByUUIDResp.Unmarshal(m, b)
}
func (m *GetAICtxByUUIDResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAICtxByUUIDResp.Marshal(b, m, deterministic)
}
func (dst *GetAICtxByUUIDResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAICtxByUUIDResp.Merge(dst, src)
}
func (m *GetAICtxByUUIDResp) XXX_Size() int {
	return xxx_messageInfo_GetAICtxByUUIDResp.Size(m)
}
func (m *GetAICtxByUUIDResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAICtxByUUIDResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAICtxByUUIDResp proto.InternalMessageInfo

func (m *GetAICtxByUUIDResp) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *GetAICtxByUUIDResp) GetBackgroundVideoUrl() string {
	if m != nil {
		return m.BackgroundVideoUrl
	}
	return ""
}

func (m *GetAICtxByUUIDResp) GetVoiceUrl() string {
	if m != nil {
		return m.VoiceUrl
	}
	return ""
}

func (m *GetAICtxByUUIDResp) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

func (m *GetAICtxByUUIDResp) GetLyrics() []string {
	if m != nil {
		return m.Lyrics
	}
	return nil
}

func (m *GetAICtxByUUIDResp) GetStageName() string {
	if m != nil {
		return m.StageName
	}
	return ""
}

func (m *GetAICtxByUUIDResp) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *GetAICtxByUUIDResp) GetShareUid() uint32 {
	if m != nil {
		return m.ShareUid
	}
	return 0
}

func (m *GetAICtxByUUIDResp) GetAiType() uint32 {
	if m != nil {
		return m.AiType
	}
	return 0
}

type GetSongHeatReq struct {
	SongName             string   `protobuf:"bytes,1,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	SongRapper           string   `protobuf:"bytes,2,opt,name=song_rapper,json=songRapper,proto3" json:"song_rapper,omitempty"`
	AiType               uint32   `protobuf:"varint,3,opt,name=ai_type,json=aiType,proto3" json:"ai_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSongHeatReq) Reset()         { *m = GetSongHeatReq{} }
func (m *GetSongHeatReq) String() string { return proto.CompactTextString(m) }
func (*GetSongHeatReq) ProtoMessage()    {}
func (*GetSongHeatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{86}
}
func (m *GetSongHeatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSongHeatReq.Unmarshal(m, b)
}
func (m *GetSongHeatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSongHeatReq.Marshal(b, m, deterministic)
}
func (dst *GetSongHeatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSongHeatReq.Merge(dst, src)
}
func (m *GetSongHeatReq) XXX_Size() int {
	return xxx_messageInfo_GetSongHeatReq.Size(m)
}
func (m *GetSongHeatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSongHeatReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSongHeatReq proto.InternalMessageInfo

func (m *GetSongHeatReq) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

func (m *GetSongHeatReq) GetSongRapper() string {
	if m != nil {
		return m.SongRapper
	}
	return ""
}

func (m *GetSongHeatReq) GetAiType() uint32 {
	if m != nil {
		return m.AiType
	}
	return 0
}

type GetSongHeatResp struct {
	Heat                 uint64   `protobuf:"varint,1,opt,name=heat,proto3" json:"heat,omitempty"`
	HeatLine             uint64   `protobuf:"varint,2,opt,name=heat_line,json=heatLine,proto3" json:"heat_line,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSongHeatResp) Reset()         { *m = GetSongHeatResp{} }
func (m *GetSongHeatResp) String() string { return proto.CompactTextString(m) }
func (*GetSongHeatResp) ProtoMessage()    {}
func (*GetSongHeatResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{87}
}
func (m *GetSongHeatResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSongHeatResp.Unmarshal(m, b)
}
func (m *GetSongHeatResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSongHeatResp.Marshal(b, m, deterministic)
}
func (dst *GetSongHeatResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSongHeatResp.Merge(dst, src)
}
func (m *GetSongHeatResp) XXX_Size() int {
	return xxx_messageInfo_GetSongHeatResp.Size(m)
}
func (m *GetSongHeatResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSongHeatResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSongHeatResp proto.InternalMessageInfo

func (m *GetSongHeatResp) GetHeat() uint64 {
	if m != nil {
		return m.Heat
	}
	return 0
}

func (m *GetSongHeatResp) GetHeatLine() uint64 {
	if m != nil {
		return m.HeatLine
	}
	return 0
}

type GetLyricInfoBySongIDReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SongId               string   `protobuf:"bytes,2,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLyricInfoBySongIDReq) Reset()         { *m = GetLyricInfoBySongIDReq{} }
func (m *GetLyricInfoBySongIDReq) String() string { return proto.CompactTextString(m) }
func (*GetLyricInfoBySongIDReq) ProtoMessage()    {}
func (*GetLyricInfoBySongIDReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{88}
}
func (m *GetLyricInfoBySongIDReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLyricInfoBySongIDReq.Unmarshal(m, b)
}
func (m *GetLyricInfoBySongIDReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLyricInfoBySongIDReq.Marshal(b, m, deterministic)
}
func (dst *GetLyricInfoBySongIDReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLyricInfoBySongIDReq.Merge(dst, src)
}
func (m *GetLyricInfoBySongIDReq) XXX_Size() int {
	return xxx_messageInfo_GetLyricInfoBySongIDReq.Size(m)
}
func (m *GetLyricInfoBySongIDReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLyricInfoBySongIDReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLyricInfoBySongIDReq proto.InternalMessageInfo

func (m *GetLyricInfoBySongIDReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetLyricInfoBySongIDReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

type GetLyricInfoBySongIDResp struct {
	LyricInfo            *LyricInfo `protobuf:"bytes,1,opt,name=lyric_info,json=lyricInfo,proto3" json:"lyric_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetLyricInfoBySongIDResp) Reset()         { *m = GetLyricInfoBySongIDResp{} }
func (m *GetLyricInfoBySongIDResp) String() string { return proto.CompactTextString(m) }
func (*GetLyricInfoBySongIDResp) ProtoMessage()    {}
func (*GetLyricInfoBySongIDResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{89}
}
func (m *GetLyricInfoBySongIDResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLyricInfoBySongIDResp.Unmarshal(m, b)
}
func (m *GetLyricInfoBySongIDResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLyricInfoBySongIDResp.Marshal(b, m, deterministic)
}
func (dst *GetLyricInfoBySongIDResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLyricInfoBySongIDResp.Merge(dst, src)
}
func (m *GetLyricInfoBySongIDResp) XXX_Size() int {
	return xxx_messageInfo_GetLyricInfoBySongIDResp.Size(m)
}
func (m *GetLyricInfoBySongIDResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLyricInfoBySongIDResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLyricInfoBySongIDResp proto.InternalMessageInfo

func (m *GetLyricInfoBySongIDResp) GetLyricInfo() *LyricInfo {
	if m != nil {
		return m.LyricInfo
	}
	return nil
}

type CheckRepeatedSongReq struct {
	TabId                string      `protobuf:"bytes,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	SongInfo             []*SongInfo `protobuf:"bytes,2,rep,name=song_info,json=songInfo,proto3" json:"song_info,omitempty"`
	AiType               uint32      `protobuf:"varint,3,opt,name=ai_type,json=aiType,proto3" json:"ai_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *CheckRepeatedSongReq) Reset()         { *m = CheckRepeatedSongReq{} }
func (m *CheckRepeatedSongReq) String() string { return proto.CompactTextString(m) }
func (*CheckRepeatedSongReq) ProtoMessage()    {}
func (*CheckRepeatedSongReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{90}
}
func (m *CheckRepeatedSongReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckRepeatedSongReq.Unmarshal(m, b)
}
func (m *CheckRepeatedSongReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckRepeatedSongReq.Marshal(b, m, deterministic)
}
func (dst *CheckRepeatedSongReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckRepeatedSongReq.Merge(dst, src)
}
func (m *CheckRepeatedSongReq) XXX_Size() int {
	return xxx_messageInfo_CheckRepeatedSongReq.Size(m)
}
func (m *CheckRepeatedSongReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckRepeatedSongReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckRepeatedSongReq proto.InternalMessageInfo

func (m *CheckRepeatedSongReq) GetTabId() string {
	if m != nil {
		return m.TabId
	}
	return ""
}

func (m *CheckRepeatedSongReq) GetSongInfo() []*SongInfo {
	if m != nil {
		return m.SongInfo
	}
	return nil
}

func (m *CheckRepeatedSongReq) GetAiType() uint32 {
	if m != nil {
		return m.AiType
	}
	return 0
}

type CheckRepeatedSongResp struct {
	DuplicativeSong      []*ResInfo `protobuf:"bytes,1,rep,name=duplicative_song,json=duplicativeSong,proto3" json:"duplicative_song,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *CheckRepeatedSongResp) Reset()         { *m = CheckRepeatedSongResp{} }
func (m *CheckRepeatedSongResp) String() string { return proto.CompactTextString(m) }
func (*CheckRepeatedSongResp) ProtoMessage()    {}
func (*CheckRepeatedSongResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{91}
}
func (m *CheckRepeatedSongResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckRepeatedSongResp.Unmarshal(m, b)
}
func (m *CheckRepeatedSongResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckRepeatedSongResp.Marshal(b, m, deterministic)
}
func (dst *CheckRepeatedSongResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckRepeatedSongResp.Merge(dst, src)
}
func (m *CheckRepeatedSongResp) XXX_Size() int {
	return xxx_messageInfo_CheckRepeatedSongResp.Size(m)
}
func (m *CheckRepeatedSongResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckRepeatedSongResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckRepeatedSongResp proto.InternalMessageInfo

func (m *CheckRepeatedSongResp) GetDuplicativeSong() []*ResInfo {
	if m != nil {
		return m.DuplicativeSong
	}
	return nil
}

type BatSetSongInfoReq struct {
	TabId                string      `protobuf:"bytes,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	SongInfo             []*SongInfo `protobuf:"bytes,2,rep,name=song_info,json=songInfo,proto3" json:"song_info,omitempty"`
	AiType               uint32      `protobuf:"varint,3,opt,name=ai_type,json=aiType,proto3" json:"ai_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *BatSetSongInfoReq) Reset()         { *m = BatSetSongInfoReq{} }
func (m *BatSetSongInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatSetSongInfoReq) ProtoMessage()    {}
func (*BatSetSongInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{92}
}
func (m *BatSetSongInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatSetSongInfoReq.Unmarshal(m, b)
}
func (m *BatSetSongInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatSetSongInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatSetSongInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatSetSongInfoReq.Merge(dst, src)
}
func (m *BatSetSongInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatSetSongInfoReq.Size(m)
}
func (m *BatSetSongInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatSetSongInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatSetSongInfoReq proto.InternalMessageInfo

func (m *BatSetSongInfoReq) GetTabId() string {
	if m != nil {
		return m.TabId
	}
	return ""
}

func (m *BatSetSongInfoReq) GetSongInfo() []*SongInfo {
	if m != nil {
		return m.SongInfo
	}
	return nil
}

func (m *BatSetSongInfoReq) GetAiType() uint32 {
	if m != nil {
		return m.AiType
	}
	return 0
}

type BatSetSongInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatSetSongInfoResp) Reset()         { *m = BatSetSongInfoResp{} }
func (m *BatSetSongInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatSetSongInfoResp) ProtoMessage()    {}
func (*BatSetSongInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ai_rapper_1e66311c5c2cf8e2, []int{93}
}
func (m *BatSetSongInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatSetSongInfoResp.Unmarshal(m, b)
}
func (m *BatSetSongInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatSetSongInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatSetSongInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatSetSongInfoResp.Merge(dst, src)
}
func (m *BatSetSongInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatSetSongInfoResp.Size(m)
}
func (m *BatSetSongInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatSetSongInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatSetSongInfoResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*GetBackgroundVideoUrlReq)(nil), "rhythm.airapper.GetBackgroundVideoUrlReq")
	proto.RegisterType((*GetBackgroundVideoUrlResp)(nil), "rhythm.airapper.GetBackgroundVideoUrlResp")
	proto.RegisterType((*BackgroundVideoInfo)(nil), "rhythm.airapper.BackgroundVideoInfo")
	proto.RegisterType((*GetSongAggScoreReq)(nil), "rhythm.airapper.GetSongAggScoreReq")
	proto.RegisterType((*GetSongAggScoreInfo)(nil), "rhythm.airapper.GetSongAggScoreInfo")
	proto.RegisterType((*GetSongAggScoreResp)(nil), "rhythm.airapper.GetSongAggScoreResp")
	proto.RegisterType((*GetRecommendTabAllNoInsertSongScoreListReq)(nil), "rhythm.airapper.GetRecommendTabAllNoInsertSongScoreListReq")
	proto.RegisterType((*GetRecommendTabAllNoInsertSongScoreListInfo)(nil), "rhythm.airapper.GetRecommendTabAllNoInsertSongScoreListInfo")
	proto.RegisterType((*GetRecommendTabAllNoInsertSongScoreListResp)(nil), "rhythm.airapper.GetRecommendTabAllNoInsertSongScoreListResp")
	proto.RegisterType((*GetUserComposedAllSongListReq)(nil), "rhythm.airapper.GetUserComposedAllSongListReq")
	proto.RegisterType((*GetUserComposedAllSongListResp)(nil), "rhythm.airapper.GetUserComposedAllSongListResp")
	proto.RegisterType((*SetVoiceWatermarkReq)(nil), "rhythm.airapper.SetVoiceWatermarkReq")
	proto.RegisterType((*SetVoiceWatermarkResp)(nil), "rhythm.airapper.SetVoiceWatermarkResp")
	proto.RegisterType((*GetVoiceWatermarkReq)(nil), "rhythm.airapper.GetVoiceWatermarkReq")
	proto.RegisterType((*GetVoiceWatermarkResp)(nil), "rhythm.airapper.GetVoiceWatermarkResp")
	proto.RegisterType((*DelVoiceWatermarkReq)(nil), "rhythm.airapper.DelVoiceWatermarkReq")
	proto.RegisterType((*DelVoiceWatermarkResp)(nil), "rhythm.airapper.DelVoiceWatermarkResp")
	proto.RegisterType((*TabInfo)(nil), "rhythm.airapper.TabInfo")
	proto.RegisterType((*CreateTabInfoReq)(nil), "rhythm.airapper.CreateTabInfoReq")
	proto.RegisterType((*CreateTabInfoResp)(nil), "rhythm.airapper.CreateTabInfoResp")
	proto.RegisterType((*GetTabInfoReq)(nil), "rhythm.airapper.GetTabInfoReq")
	proto.RegisterType((*GetTabInfoResp)(nil), "rhythm.airapper.GetTabInfoResp")
	proto.RegisterType((*MoveTabCategoryReq)(nil), "rhythm.airapper.MoveTabCategoryReq")
	proto.RegisterType((*MoveTabCategoryResp)(nil), "rhythm.airapper.MoveTabCategoryResp")
	proto.RegisterType((*SongInfo)(nil), "rhythm.airapper.songInfo")
	proto.RegisterType((*UrlInfo)(nil), "rhythm.airapper.urlInfo")
	proto.RegisterType((*AddSongInfoReq)(nil), "rhythm.airapper.AddSongInfoReq")
	proto.RegisterType((*AddSongInfoResp)(nil), "rhythm.airapper.AddSongInfoResp")
	proto.RegisterType((*UpdateLyricInfoReq)(nil), "rhythm.airapper.UpdateLyricInfoReq")
	proto.RegisterType((*ResInfo)(nil), "rhythm.airapper.ResInfo")
	proto.RegisterType((*UpdateLyricInfoResp)(nil), "rhythm.airapper.UpdateLyricInfoResp")
	proto.RegisterType((*CheckSongFileReq)(nil), "rhythm.airapper.CheckSongFileReq")
	proto.RegisterType((*CheckSongFileResp)(nil), "rhythm.airapper.CheckSongFileResp")
	proto.RegisterMapType((map[string]uint32)(nil), "rhythm.airapper.CheckSongFileResp.ValidSongEntry")
	proto.RegisterType((*CheckSongExistReq)(nil), "rhythm.airapper.CheckSongExistReq")
	proto.RegisterType((*CheckSongExistResp)(nil), "rhythm.airapper.CheckSongExistResp")
	proto.RegisterMapType((map[string]uint32)(nil), "rhythm.airapper.CheckSongExistResp.ValidSongEntry")
	proto.RegisterType((*VideoURL)(nil), "rhythm.airapper.VideoURL")
	proto.RegisterType((*UpdateSongConfiguration)(nil), "rhythm.airapper.UpdateSongConfiguration")
	proto.RegisterType((*UpdateSongConfigurationReq)(nil), "rhythm.airapper.UpdateSongConfigurationReq")
	proto.RegisterType((*UpdateSongConfigurationResp)(nil), "rhythm.airapper.UpdateSongConfigurationResp")
	proto.RegisterType((*SongComposedVideo)(nil), "rhythm.airapper.SongComposedVideo")
	proto.RegisterType((*BatUpdateComposedVideoUrlInfoReq)(nil), "rhythm.airapper.BatUpdateComposedVideoUrlInfoReq")
	proto.RegisterType((*BatUpdateComposedVideoUrlInfoResp)(nil), "rhythm.airapper.BatUpdateComposedVideoUrlInfoResp")
	proto.RegisterType((*SongConfigurationInformation)(nil), "rhythm.airapper.SongConfigurationInformation")
	proto.RegisterType((*GetSongConfigurationInformationReq)(nil), "rhythm.airapper.GetSongConfigurationInformationReq")
	proto.RegisterType((*GetSongConfigurationInformationResp)(nil), "rhythm.airapper.GetSongConfigurationInformationResp")
	proto.RegisterType((*DelSongConfigurationReq)(nil), "rhythm.airapper.DelSongConfigurationReq")
	proto.RegisterType((*DelSongConfigurationResp)(nil), "rhythm.airapper.DelSongConfigurationResp")
	proto.RegisterType((*MoveSongCategoryReq)(nil), "rhythm.airapper.MoveSongCategoryReq")
	proto.RegisterType((*MoveSongCategoryResp)(nil), "rhythm.airapper.MoveSongCategoryResp")
	proto.RegisterType((*CopyMoveSongsToOtherTabReq)(nil), "rhythm.airapper.CopyMoveSongsToOtherTabReq")
	proto.RegisterType((*CopyMoveSongsToOtherTabResp)(nil), "rhythm.airapper.CopyMoveSongsToOtherTabResp")
	proto.RegisterType((*GetLyricInfoReq)(nil), "rhythm.airapper.GetLyricInfoReq")
	proto.RegisterType((*CardBGVideo)(nil), "rhythm.airapper.CardBGVideo")
	proto.RegisterType((*LyricInfo)(nil), "rhythm.airapper.LyricInfo")
	proto.RegisterType((*GetLyricInfoResp)(nil), "rhythm.airapper.GetLyricInfoResp")
	proto.RegisterType((*UpdateLevelReq)(nil), "rhythm.airapper.UpdateLevelReq")
	proto.RegisterType((*UpdateLevelResp)(nil), "rhythm.airapper.UpdateLevelResp")
	proto.RegisterType((*GetLevelInfoReq)(nil), "rhythm.airapper.GetLevelInfoReq")
	proto.RegisterType((*LevelInfo)(nil), "rhythm.airapper.LevelInfo")
	proto.RegisterType((*GetLevelInfoResp)(nil), "rhythm.airapper.GetLevelInfoResp")
	proto.RegisterType((*DelLevelInfoReq)(nil), "rhythm.airapper.DelLevelInfoReq")
	proto.RegisterType((*DelLevelInfoResp)(nil), "rhythm.airapper.DelLevelInfoResp")
	proto.RegisterType((*LevelConfig)(nil), "rhythm.airapper.LevelConfig")
	proto.RegisterType((*UserLevel)(nil), "rhythm.airapper.UserLevel")
	proto.RegisterType((*GetUserAIRapperLevelReq)(nil), "rhythm.airapper.GetUserAIRapperLevelReq")
	proto.RegisterType((*GetUserAIRapperLevelResp)(nil), "rhythm.airapper.GetUserAIRapperLevelResp")
	proto.RegisterType((*PushPartialKafkaReq)(nil), "rhythm.airapper.PushPartialKafkaReq")
	proto.RegisterType((*PushPartialKafkaResp)(nil), "rhythm.airapper.PushPartialKafkaResp")
	proto.RegisterType((*PushComposeKafkaReq)(nil), "rhythm.airapper.PushComposeKafkaReq")
	proto.RegisterType((*PushComposeKafkaResp)(nil), "rhythm.airapper.PushComposeKafkaResp")
	proto.RegisterType((*AddUserLevelAttitudeReq)(nil), "rhythm.airapper.AddUserLevelAttitudeReq")
	proto.RegisterType((*AddUserLevelAttitudeResp)(nil), "rhythm.airapper.AddUserLevelAttitudeResp")
	proto.RegisterType((*PostAIRapperPostReq)(nil), "rhythm.airapper.PostAIRapperPostReq")
	proto.RegisterType((*PostAIRapperPostResp)(nil), "rhythm.airapper.PostAIRapperPostResp")
	proto.RegisterType((*CheckPositionStatusReq)(nil), "rhythm.airapper.CheckPositionStatusReq")
	proto.RegisterType((*CheckPositionStatusResp)(nil), "rhythm.airapper.CheckPositionStatusResp")
	proto.RegisterType((*AggregationInfo)(nil), "rhythm.airapper.AggregationInfo")
	proto.RegisterType((*AggregationInfoReq)(nil), "rhythm.airapper.AggregationInfoReq")
	proto.RegisterType((*AggregationInfoResp)(nil), "rhythm.airapper.AggregationInfoResp")
	proto.RegisterType((*SongPositionInfo)(nil), "rhythm.airapper.SongPositionInfo")
	proto.RegisterType((*MoveSongPositionReq)(nil), "rhythm.airapper.MoveSongPositionReq")
	proto.RegisterType((*MoveSongPositionResp)(nil), "rhythm.airapper.MoveSongPositionResp")
	proto.RegisterType((*GetH5UrlWithAICtxReq)(nil), "rhythm.airapper.GetH5UrlWithAICtxReq")
	proto.RegisterType((*GetH5UrlWithAICtxResp)(nil), "rhythm.airapper.GetH5UrlWithAICtxResp")
	proto.RegisterType((*GetAICtxByUUIDReq)(nil), "rhythm.airapper.GetAICtxByUUIDReq")
	proto.RegisterType((*GetAICtxByUUIDResp)(nil), "rhythm.airapper.GetAICtxByUUIDResp")
	proto.RegisterType((*GetSongHeatReq)(nil), "rhythm.airapper.GetSongHeatReq")
	proto.RegisterType((*GetSongHeatResp)(nil), "rhythm.airapper.GetSongHeatResp")
	proto.RegisterType((*GetLyricInfoBySongIDReq)(nil), "rhythm.airapper.GetLyricInfoBySongIDReq")
	proto.RegisterType((*GetLyricInfoBySongIDResp)(nil), "rhythm.airapper.GetLyricInfoBySongIDResp")
	proto.RegisterType((*CheckRepeatedSongReq)(nil), "rhythm.airapper.CheckRepeatedSongReq")
	proto.RegisterType((*CheckRepeatedSongResp)(nil), "rhythm.airapper.CheckRepeatedSongResp")
	proto.RegisterType((*BatSetSongInfoReq)(nil), "rhythm.airapper.BatSetSongInfoReq")
	proto.RegisterType((*BatSetSongInfoResp)(nil), "rhythm.airapper.BatSetSongInfoResp")
	proto.RegisterEnum("rhythm.airapper.TabStatusInfo", TabStatusInfo_name, TabStatusInfo_value)
	proto.RegisterEnum("rhythm.airapper.FileExist", FileExist_name, FileExist_value)
	proto.RegisterEnum("rhythm.airapper.StatusInfo", StatusInfo_name, StatusInfo_value)
	proto.RegisterEnum("rhythm.airapper.CheckType", CheckType_name, CheckType_value)
	proto.RegisterEnum("rhythm.airapper.CopyOrMoveType", CopyOrMoveType_name, CopyOrMoveType_value)
	proto.RegisterEnum("rhythm.airapper.AggregationChoose", AggregationChoose_name, AggregationChoose_value)
	proto.RegisterEnum("rhythm.airapper.AIType", AIType_name, AIType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AiRapperClient is the client API for AiRapper service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AiRapperClient interface {
	SetVoiceWatermark(ctx context.Context, in *SetVoiceWatermarkReq, opts ...grpc.CallOption) (*SetVoiceWatermarkResp, error)
	GetVoiceWatermark(ctx context.Context, in *GetVoiceWatermarkReq, opts ...grpc.CallOption) (*GetVoiceWatermarkResp, error)
	DelVoiceWatermark(ctx context.Context, in *DelVoiceWatermarkReq, opts ...grpc.CallOption) (*DelVoiceWatermarkResp, error)
	// 新增或编辑Tab
	CreateTabInfo(ctx context.Context, in *CreateTabInfoReq, opts ...grpc.CallOption) (*CreateTabInfoResp, error)
	// 获取Tab信息
	GetTabInfo(ctx context.Context, in *GetTabInfoReq, opts ...grpc.CallOption) (*GetTabInfoResp, error)
	// 移动tab位置
	MoveTabCategory(ctx context.Context, in *MoveTabCategoryReq, opts ...grpc.CallOption) (*MoveTabCategoryResp, error)
	// 新增歌曲信息
	AddSongInfo(ctx context.Context, in *AddSongInfoReq, opts ...grpc.CallOption) (*AddSongInfoResp, error)
	// 新增歌词信息
	UpdateLyricInfo(ctx context.Context, in *UpdateLyricInfoReq, opts ...grpc.CallOption) (*UpdateLyricInfoResp, error)
	// 编辑歌曲配置信息
	UpdateSongConfiguration(ctx context.Context, in *UpdateSongConfigurationReq, opts ...grpc.CallOption) (*UpdateSongConfigurationResp, error)
	// 获取歌曲配置信息
	GetSongConfigurationInformation(ctx context.Context, in *GetSongConfigurationInformationReq, opts ...grpc.CallOption) (*GetSongConfigurationInformationResp, error)
	// 删除歌曲
	DelSongConfiguration(ctx context.Context, in *DelSongConfigurationReq, opts ...grpc.CallOption) (*DelSongConfigurationResp, error)
	// 移动歌曲位置
	MoveSongCategory(ctx context.Context, in *MoveSongCategoryReq, opts ...grpc.CallOption) (*MoveSongCategoryResp, error)
	GetLyricInfo(ctx context.Context, in *GetLyricInfoReq, opts ...grpc.CallOption) (*GetLyricInfoResp, error)
	// 编辑歌曲的合成视频资源 覆盖设置
	BatUpdateComposedVideoUrlInfo(ctx context.Context, in *BatUpdateComposedVideoUrlInfoReq, opts ...grpc.CallOption) (*BatUpdateComposedVideoUrlInfoResp, error)
	// 添加/编辑等级
	UpdateLevel(ctx context.Context, in *UpdateLevelReq, opts ...grpc.CallOption) (*UpdateLevelResp, error)
	// 获取等级
	GetLevelInfo(ctx context.Context, in *GetLevelInfoReq, opts ...grpc.CallOption) (*GetLevelInfoResp, error)
	// 删除等级
	DelLevelInfo(ctx context.Context, in *DelLevelInfoReq, opts ...grpc.CallOption) (*DelLevelInfoResp, error)
	// ===========客户端交互先关==================
	GetUserAIRapperLevel(ctx context.Context, in *GetUserAIRapperLevelReq, opts ...grpc.CallOption) (*GetUserAIRapperLevelResp, error)
	PostAIRapperPost(ctx context.Context, in *PostAIRapperPostReq, opts ...grpc.CallOption) (*PostAIRapperPostResp, error)
	// 模拟kafka
	PushPartialKafka(ctx context.Context, in *PushPartialKafkaReq, opts ...grpc.CallOption) (*PushPartialKafkaResp, error)
	PushComposeKafka(ctx context.Context, in *PushComposeKafkaReq, opts ...grpc.CallOption) (*PushComposeKafkaResp, error)
	AddUserLevelAttitude(ctx context.Context, in *AddUserLevelAttitudeReq, opts ...grpc.CallOption) (*AddUserLevelAttitudeResp, error)
	// 前置检查歌词、模板文件状态
	CheckSongFile(ctx context.Context, in *CheckSongFileReq, opts ...grpc.CallOption) (*CheckSongFileResp, error)
	// 检查歌曲是否存在
	CheckSongExist(ctx context.Context, in *CheckSongExistReq, opts ...grpc.CallOption) (*CheckSongExistResp, error)
	// 校验强插位置是否被占用接口
	CheckPositionStatus(ctx context.Context, in *CheckPositionStatusReq, opts ...grpc.CallOption) (*CheckPositionStatusResp, error)
	// 曝光，合成，分享聚合接口
	Aggregation(ctx context.Context, in *AggregationInfoReq, opts ...grpc.CallOption) (*AggregationInfoResp, error)
	// 移动
	CopyMoveSongsToOtherTab(ctx context.Context, in *CopyMoveSongsToOtherTabReq, opts ...grpc.CallOption) (*CopyMoveSongsToOtherTabResp, error)
	// 移动推荐tab下的强插歌曲
	MoveSongPosition(ctx context.Context, in *MoveSongPositionReq, opts ...grpc.CallOption) (*MoveSongPositionResp, error)
	// 用户点击分享，记录上下文，并返回分享链接
	GetH5UrlWithAICtx(ctx context.Context, in *GetH5UrlWithAICtxReq, opts ...grpc.CallOption) (*GetH5UrlWithAICtxResp, error)
	GetAICtxByUUID(ctx context.Context, in *GetAICtxByUUIDReq, opts ...grpc.CallOption) (*GetAICtxByUUIDResp, error)
	GetSongHeat(ctx context.Context, in *GetSongHeatReq, opts ...grpc.CallOption) (*GetSongHeatResp, error)
	// 根据指定songId获取歌曲卡片信息
	GetLyricInfoBySongID(ctx context.Context, in *GetLyricInfoBySongIDReq, opts ...grpc.CallOption) (*GetLyricInfoBySongIDResp, error)
	// 检查重复歌曲信息接口
	CheckRepeatedSong(ctx context.Context, in *CheckRepeatedSongReq, opts ...grpc.CallOption) (*CheckRepeatedSongResp, error)
	// 上传歌曲
	BatSetSongInfo(ctx context.Context, in *BatSetSongInfoReq, opts ...grpc.CallOption) (*BatSetSongInfoResp, error)
	// 获取推荐tab下所有未强插歌曲分数接口
	GetRecommendTabAllNoInsertSongScoreList(ctx context.Context, in *GetRecommendTabAllNoInsertSongScoreListReq, opts ...grpc.CallOption) (*GetRecommendTabAllNoInsertSongScoreListResp, error)
	// 获取用户合成过的所有歌曲
	GetUserComposedAllSongList(ctx context.Context, in *GetUserComposedAllSongListReq, opts ...grpc.CallOption) (*GetUserComposedAllSongListResp, error)
	// 获取歌曲曝光分数接口
	GetSongAggScore(ctx context.Context, in *GetSongAggScoreReq, opts ...grpc.CallOption) (*GetSongAggScoreResp, error)
	// 获取背景视频列表
	GetBackgroundVideoUrl(ctx context.Context, in *GetBackgroundVideoUrlReq, opts ...grpc.CallOption) (*GetBackgroundVideoUrlResp, error)
}

type aiRapperClient struct {
	cc *grpc.ClientConn
}

func NewAiRapperClient(cc *grpc.ClientConn) AiRapperClient {
	return &aiRapperClient{cc}
}

func (c *aiRapperClient) SetVoiceWatermark(ctx context.Context, in *SetVoiceWatermarkReq, opts ...grpc.CallOption) (*SetVoiceWatermarkResp, error) {
	out := new(SetVoiceWatermarkResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/SetVoiceWatermark", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) GetVoiceWatermark(ctx context.Context, in *GetVoiceWatermarkReq, opts ...grpc.CallOption) (*GetVoiceWatermarkResp, error) {
	out := new(GetVoiceWatermarkResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/GetVoiceWatermark", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) DelVoiceWatermark(ctx context.Context, in *DelVoiceWatermarkReq, opts ...grpc.CallOption) (*DelVoiceWatermarkResp, error) {
	out := new(DelVoiceWatermarkResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/DelVoiceWatermark", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) CreateTabInfo(ctx context.Context, in *CreateTabInfoReq, opts ...grpc.CallOption) (*CreateTabInfoResp, error) {
	out := new(CreateTabInfoResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/CreateTabInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) GetTabInfo(ctx context.Context, in *GetTabInfoReq, opts ...grpc.CallOption) (*GetTabInfoResp, error) {
	out := new(GetTabInfoResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/GetTabInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) MoveTabCategory(ctx context.Context, in *MoveTabCategoryReq, opts ...grpc.CallOption) (*MoveTabCategoryResp, error) {
	out := new(MoveTabCategoryResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/MoveTabCategory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) AddSongInfo(ctx context.Context, in *AddSongInfoReq, opts ...grpc.CallOption) (*AddSongInfoResp, error) {
	out := new(AddSongInfoResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/AddSongInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) UpdateLyricInfo(ctx context.Context, in *UpdateLyricInfoReq, opts ...grpc.CallOption) (*UpdateLyricInfoResp, error) {
	out := new(UpdateLyricInfoResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/UpdateLyricInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) UpdateSongConfiguration(ctx context.Context, in *UpdateSongConfigurationReq, opts ...grpc.CallOption) (*UpdateSongConfigurationResp, error) {
	out := new(UpdateSongConfigurationResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/UpdateSongConfiguration", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) GetSongConfigurationInformation(ctx context.Context, in *GetSongConfigurationInformationReq, opts ...grpc.CallOption) (*GetSongConfigurationInformationResp, error) {
	out := new(GetSongConfigurationInformationResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/GetSongConfigurationInformation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) DelSongConfiguration(ctx context.Context, in *DelSongConfigurationReq, opts ...grpc.CallOption) (*DelSongConfigurationResp, error) {
	out := new(DelSongConfigurationResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/DelSongConfiguration", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) MoveSongCategory(ctx context.Context, in *MoveSongCategoryReq, opts ...grpc.CallOption) (*MoveSongCategoryResp, error) {
	out := new(MoveSongCategoryResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/MoveSongCategory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) GetLyricInfo(ctx context.Context, in *GetLyricInfoReq, opts ...grpc.CallOption) (*GetLyricInfoResp, error) {
	out := new(GetLyricInfoResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/GetLyricInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) BatUpdateComposedVideoUrlInfo(ctx context.Context, in *BatUpdateComposedVideoUrlInfoReq, opts ...grpc.CallOption) (*BatUpdateComposedVideoUrlInfoResp, error) {
	out := new(BatUpdateComposedVideoUrlInfoResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/BatUpdateComposedVideoUrlInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) UpdateLevel(ctx context.Context, in *UpdateLevelReq, opts ...grpc.CallOption) (*UpdateLevelResp, error) {
	out := new(UpdateLevelResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/UpdateLevel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) GetLevelInfo(ctx context.Context, in *GetLevelInfoReq, opts ...grpc.CallOption) (*GetLevelInfoResp, error) {
	out := new(GetLevelInfoResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/GetLevelInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) DelLevelInfo(ctx context.Context, in *DelLevelInfoReq, opts ...grpc.CallOption) (*DelLevelInfoResp, error) {
	out := new(DelLevelInfoResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/DelLevelInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) GetUserAIRapperLevel(ctx context.Context, in *GetUserAIRapperLevelReq, opts ...grpc.CallOption) (*GetUserAIRapperLevelResp, error) {
	out := new(GetUserAIRapperLevelResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/GetUserAIRapperLevel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) PostAIRapperPost(ctx context.Context, in *PostAIRapperPostReq, opts ...grpc.CallOption) (*PostAIRapperPostResp, error) {
	out := new(PostAIRapperPostResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/PostAIRapperPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) PushPartialKafka(ctx context.Context, in *PushPartialKafkaReq, opts ...grpc.CallOption) (*PushPartialKafkaResp, error) {
	out := new(PushPartialKafkaResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/PushPartialKafka", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) PushComposeKafka(ctx context.Context, in *PushComposeKafkaReq, opts ...grpc.CallOption) (*PushComposeKafkaResp, error) {
	out := new(PushComposeKafkaResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/PushComposeKafka", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) AddUserLevelAttitude(ctx context.Context, in *AddUserLevelAttitudeReq, opts ...grpc.CallOption) (*AddUserLevelAttitudeResp, error) {
	out := new(AddUserLevelAttitudeResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/AddUserLevelAttitude", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) CheckSongFile(ctx context.Context, in *CheckSongFileReq, opts ...grpc.CallOption) (*CheckSongFileResp, error) {
	out := new(CheckSongFileResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/CheckSongFile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) CheckSongExist(ctx context.Context, in *CheckSongExistReq, opts ...grpc.CallOption) (*CheckSongExistResp, error) {
	out := new(CheckSongExistResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/CheckSongExist", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) CheckPositionStatus(ctx context.Context, in *CheckPositionStatusReq, opts ...grpc.CallOption) (*CheckPositionStatusResp, error) {
	out := new(CheckPositionStatusResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/CheckPositionStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) Aggregation(ctx context.Context, in *AggregationInfoReq, opts ...grpc.CallOption) (*AggregationInfoResp, error) {
	out := new(AggregationInfoResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/Aggregation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) CopyMoveSongsToOtherTab(ctx context.Context, in *CopyMoveSongsToOtherTabReq, opts ...grpc.CallOption) (*CopyMoveSongsToOtherTabResp, error) {
	out := new(CopyMoveSongsToOtherTabResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/CopyMoveSongsToOtherTab", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) MoveSongPosition(ctx context.Context, in *MoveSongPositionReq, opts ...grpc.CallOption) (*MoveSongPositionResp, error) {
	out := new(MoveSongPositionResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/MoveSongPosition", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) GetH5UrlWithAICtx(ctx context.Context, in *GetH5UrlWithAICtxReq, opts ...grpc.CallOption) (*GetH5UrlWithAICtxResp, error) {
	out := new(GetH5UrlWithAICtxResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/GetH5UrlWithAICtx", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) GetAICtxByUUID(ctx context.Context, in *GetAICtxByUUIDReq, opts ...grpc.CallOption) (*GetAICtxByUUIDResp, error) {
	out := new(GetAICtxByUUIDResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/GetAICtxByUUID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) GetSongHeat(ctx context.Context, in *GetSongHeatReq, opts ...grpc.CallOption) (*GetSongHeatResp, error) {
	out := new(GetSongHeatResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/GetSongHeat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) GetLyricInfoBySongID(ctx context.Context, in *GetLyricInfoBySongIDReq, opts ...grpc.CallOption) (*GetLyricInfoBySongIDResp, error) {
	out := new(GetLyricInfoBySongIDResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/GetLyricInfoBySongID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) CheckRepeatedSong(ctx context.Context, in *CheckRepeatedSongReq, opts ...grpc.CallOption) (*CheckRepeatedSongResp, error) {
	out := new(CheckRepeatedSongResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/CheckRepeatedSong", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) BatSetSongInfo(ctx context.Context, in *BatSetSongInfoReq, opts ...grpc.CallOption) (*BatSetSongInfoResp, error) {
	out := new(BatSetSongInfoResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/BatSetSongInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) GetRecommendTabAllNoInsertSongScoreList(ctx context.Context, in *GetRecommendTabAllNoInsertSongScoreListReq, opts ...grpc.CallOption) (*GetRecommendTabAllNoInsertSongScoreListResp, error) {
	out := new(GetRecommendTabAllNoInsertSongScoreListResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/GetRecommendTabAllNoInsertSongScoreList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) GetUserComposedAllSongList(ctx context.Context, in *GetUserComposedAllSongListReq, opts ...grpc.CallOption) (*GetUserComposedAllSongListResp, error) {
	out := new(GetUserComposedAllSongListResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/GetUserComposedAllSongList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) GetSongAggScore(ctx context.Context, in *GetSongAggScoreReq, opts ...grpc.CallOption) (*GetSongAggScoreResp, error) {
	out := new(GetSongAggScoreResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/GetSongAggScore", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiRapperClient) GetBackgroundVideoUrl(ctx context.Context, in *GetBackgroundVideoUrlReq, opts ...grpc.CallOption) (*GetBackgroundVideoUrlResp, error) {
	out := new(GetBackgroundVideoUrlResp)
	err := c.cc.Invoke(ctx, "/rhythm.airapper.AiRapper/GetBackgroundVideoUrl", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AiRapperServer is the server API for AiRapper service.
type AiRapperServer interface {
	SetVoiceWatermark(context.Context, *SetVoiceWatermarkReq) (*SetVoiceWatermarkResp, error)
	GetVoiceWatermark(context.Context, *GetVoiceWatermarkReq) (*GetVoiceWatermarkResp, error)
	DelVoiceWatermark(context.Context, *DelVoiceWatermarkReq) (*DelVoiceWatermarkResp, error)
	// 新增或编辑Tab
	CreateTabInfo(context.Context, *CreateTabInfoReq) (*CreateTabInfoResp, error)
	// 获取Tab信息
	GetTabInfo(context.Context, *GetTabInfoReq) (*GetTabInfoResp, error)
	// 移动tab位置
	MoveTabCategory(context.Context, *MoveTabCategoryReq) (*MoveTabCategoryResp, error)
	// 新增歌曲信息
	AddSongInfo(context.Context, *AddSongInfoReq) (*AddSongInfoResp, error)
	// 新增歌词信息
	UpdateLyricInfo(context.Context, *UpdateLyricInfoReq) (*UpdateLyricInfoResp, error)
	// 编辑歌曲配置信息
	UpdateSongConfiguration(context.Context, *UpdateSongConfigurationReq) (*UpdateSongConfigurationResp, error)
	// 获取歌曲配置信息
	GetSongConfigurationInformation(context.Context, *GetSongConfigurationInformationReq) (*GetSongConfigurationInformationResp, error)
	// 删除歌曲
	DelSongConfiguration(context.Context, *DelSongConfigurationReq) (*DelSongConfigurationResp, error)
	// 移动歌曲位置
	MoveSongCategory(context.Context, *MoveSongCategoryReq) (*MoveSongCategoryResp, error)
	GetLyricInfo(context.Context, *GetLyricInfoReq) (*GetLyricInfoResp, error)
	// 编辑歌曲的合成视频资源 覆盖设置
	BatUpdateComposedVideoUrlInfo(context.Context, *BatUpdateComposedVideoUrlInfoReq) (*BatUpdateComposedVideoUrlInfoResp, error)
	// 添加/编辑等级
	UpdateLevel(context.Context, *UpdateLevelReq) (*UpdateLevelResp, error)
	// 获取等级
	GetLevelInfo(context.Context, *GetLevelInfoReq) (*GetLevelInfoResp, error)
	// 删除等级
	DelLevelInfo(context.Context, *DelLevelInfoReq) (*DelLevelInfoResp, error)
	// ===========客户端交互先关==================
	GetUserAIRapperLevel(context.Context, *GetUserAIRapperLevelReq) (*GetUserAIRapperLevelResp, error)
	PostAIRapperPost(context.Context, *PostAIRapperPostReq) (*PostAIRapperPostResp, error)
	// 模拟kafka
	PushPartialKafka(context.Context, *PushPartialKafkaReq) (*PushPartialKafkaResp, error)
	PushComposeKafka(context.Context, *PushComposeKafkaReq) (*PushComposeKafkaResp, error)
	AddUserLevelAttitude(context.Context, *AddUserLevelAttitudeReq) (*AddUserLevelAttitudeResp, error)
	// 前置检查歌词、模板文件状态
	CheckSongFile(context.Context, *CheckSongFileReq) (*CheckSongFileResp, error)
	// 检查歌曲是否存在
	CheckSongExist(context.Context, *CheckSongExistReq) (*CheckSongExistResp, error)
	// 校验强插位置是否被占用接口
	CheckPositionStatus(context.Context, *CheckPositionStatusReq) (*CheckPositionStatusResp, error)
	// 曝光，合成，分享聚合接口
	Aggregation(context.Context, *AggregationInfoReq) (*AggregationInfoResp, error)
	// 移动
	CopyMoveSongsToOtherTab(context.Context, *CopyMoveSongsToOtherTabReq) (*CopyMoveSongsToOtherTabResp, error)
	// 移动推荐tab下的强插歌曲
	MoveSongPosition(context.Context, *MoveSongPositionReq) (*MoveSongPositionResp, error)
	// 用户点击分享，记录上下文，并返回分享链接
	GetH5UrlWithAICtx(context.Context, *GetH5UrlWithAICtxReq) (*GetH5UrlWithAICtxResp, error)
	GetAICtxByUUID(context.Context, *GetAICtxByUUIDReq) (*GetAICtxByUUIDResp, error)
	GetSongHeat(context.Context, *GetSongHeatReq) (*GetSongHeatResp, error)
	// 根据指定songId获取歌曲卡片信息
	GetLyricInfoBySongID(context.Context, *GetLyricInfoBySongIDReq) (*GetLyricInfoBySongIDResp, error)
	// 检查重复歌曲信息接口
	CheckRepeatedSong(context.Context, *CheckRepeatedSongReq) (*CheckRepeatedSongResp, error)
	// 上传歌曲
	BatSetSongInfo(context.Context, *BatSetSongInfoReq) (*BatSetSongInfoResp, error)
	// 获取推荐tab下所有未强插歌曲分数接口
	GetRecommendTabAllNoInsertSongScoreList(context.Context, *GetRecommendTabAllNoInsertSongScoreListReq) (*GetRecommendTabAllNoInsertSongScoreListResp, error)
	// 获取用户合成过的所有歌曲
	GetUserComposedAllSongList(context.Context, *GetUserComposedAllSongListReq) (*GetUserComposedAllSongListResp, error)
	// 获取歌曲曝光分数接口
	GetSongAggScore(context.Context, *GetSongAggScoreReq) (*GetSongAggScoreResp, error)
	// 获取背景视频列表
	GetBackgroundVideoUrl(context.Context, *GetBackgroundVideoUrlReq) (*GetBackgroundVideoUrlResp, error)
}

func RegisterAiRapperServer(s *grpc.Server, srv AiRapperServer) {
	s.RegisterService(&_AiRapper_serviceDesc, srv)
}

func _AiRapper_SetVoiceWatermark_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetVoiceWatermarkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).SetVoiceWatermark(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/SetVoiceWatermark",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).SetVoiceWatermark(ctx, req.(*SetVoiceWatermarkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_GetVoiceWatermark_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVoiceWatermarkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).GetVoiceWatermark(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/GetVoiceWatermark",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).GetVoiceWatermark(ctx, req.(*GetVoiceWatermarkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_DelVoiceWatermark_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelVoiceWatermarkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).DelVoiceWatermark(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/DelVoiceWatermark",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).DelVoiceWatermark(ctx, req.(*DelVoiceWatermarkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_CreateTabInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTabInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).CreateTabInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/CreateTabInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).CreateTabInfo(ctx, req.(*CreateTabInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_GetTabInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTabInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).GetTabInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/GetTabInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).GetTabInfo(ctx, req.(*GetTabInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_MoveTabCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MoveTabCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).MoveTabCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/MoveTabCategory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).MoveTabCategory(ctx, req.(*MoveTabCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_AddSongInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddSongInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).AddSongInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/AddSongInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).AddSongInfo(ctx, req.(*AddSongInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_UpdateLyricInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateLyricInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).UpdateLyricInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/UpdateLyricInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).UpdateLyricInfo(ctx, req.(*UpdateLyricInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_UpdateSongConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSongConfigurationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).UpdateSongConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/UpdateSongConfiguration",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).UpdateSongConfiguration(ctx, req.(*UpdateSongConfigurationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_GetSongConfigurationInformation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSongConfigurationInformationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).GetSongConfigurationInformation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/GetSongConfigurationInformation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).GetSongConfigurationInformation(ctx, req.(*GetSongConfigurationInformationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_DelSongConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelSongConfigurationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).DelSongConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/DelSongConfiguration",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).DelSongConfiguration(ctx, req.(*DelSongConfigurationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_MoveSongCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MoveSongCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).MoveSongCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/MoveSongCategory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).MoveSongCategory(ctx, req.(*MoveSongCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_GetLyricInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLyricInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).GetLyricInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/GetLyricInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).GetLyricInfo(ctx, req.(*GetLyricInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_BatUpdateComposedVideoUrlInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatUpdateComposedVideoUrlInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).BatUpdateComposedVideoUrlInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/BatUpdateComposedVideoUrlInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).BatUpdateComposedVideoUrlInfo(ctx, req.(*BatUpdateComposedVideoUrlInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_UpdateLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateLevelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).UpdateLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/UpdateLevel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).UpdateLevel(ctx, req.(*UpdateLevelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_GetLevelInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLevelInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).GetLevelInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/GetLevelInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).GetLevelInfo(ctx, req.(*GetLevelInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_DelLevelInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelLevelInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).DelLevelInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/DelLevelInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).DelLevelInfo(ctx, req.(*DelLevelInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_GetUserAIRapperLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserAIRapperLevelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).GetUserAIRapperLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/GetUserAIRapperLevel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).GetUserAIRapperLevel(ctx, req.(*GetUserAIRapperLevelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_PostAIRapperPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PostAIRapperPostReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).PostAIRapperPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/PostAIRapperPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).PostAIRapperPost(ctx, req.(*PostAIRapperPostReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_PushPartialKafka_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushPartialKafkaReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).PushPartialKafka(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/PushPartialKafka",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).PushPartialKafka(ctx, req.(*PushPartialKafkaReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_PushComposeKafka_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushComposeKafkaReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).PushComposeKafka(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/PushComposeKafka",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).PushComposeKafka(ctx, req.(*PushComposeKafkaReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_AddUserLevelAttitude_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserLevelAttitudeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).AddUserLevelAttitude(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/AddUserLevelAttitude",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).AddUserLevelAttitude(ctx, req.(*AddUserLevelAttitudeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_CheckSongFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckSongFileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).CheckSongFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/CheckSongFile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).CheckSongFile(ctx, req.(*CheckSongFileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_CheckSongExist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckSongExistReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).CheckSongExist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/CheckSongExist",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).CheckSongExist(ctx, req.(*CheckSongExistReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_CheckPositionStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckPositionStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).CheckPositionStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/CheckPositionStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).CheckPositionStatus(ctx, req.(*CheckPositionStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_Aggregation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AggregationInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).Aggregation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/Aggregation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).Aggregation(ctx, req.(*AggregationInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_CopyMoveSongsToOtherTab_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CopyMoveSongsToOtherTabReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).CopyMoveSongsToOtherTab(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/CopyMoveSongsToOtherTab",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).CopyMoveSongsToOtherTab(ctx, req.(*CopyMoveSongsToOtherTabReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_MoveSongPosition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MoveSongPositionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).MoveSongPosition(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/MoveSongPosition",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).MoveSongPosition(ctx, req.(*MoveSongPositionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_GetH5UrlWithAICtx_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetH5UrlWithAICtxReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).GetH5UrlWithAICtx(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/GetH5UrlWithAICtx",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).GetH5UrlWithAICtx(ctx, req.(*GetH5UrlWithAICtxReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_GetAICtxByUUID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAICtxByUUIDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).GetAICtxByUUID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/GetAICtxByUUID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).GetAICtxByUUID(ctx, req.(*GetAICtxByUUIDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_GetSongHeat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSongHeatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).GetSongHeat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/GetSongHeat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).GetSongHeat(ctx, req.(*GetSongHeatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_GetLyricInfoBySongID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLyricInfoBySongIDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).GetLyricInfoBySongID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/GetLyricInfoBySongID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).GetLyricInfoBySongID(ctx, req.(*GetLyricInfoBySongIDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_CheckRepeatedSong_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckRepeatedSongReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).CheckRepeatedSong(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/CheckRepeatedSong",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).CheckRepeatedSong(ctx, req.(*CheckRepeatedSongReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_BatSetSongInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatSetSongInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).BatSetSongInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/BatSetSongInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).BatSetSongInfo(ctx, req.(*BatSetSongInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_GetRecommendTabAllNoInsertSongScoreList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecommendTabAllNoInsertSongScoreListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).GetRecommendTabAllNoInsertSongScoreList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/GetRecommendTabAllNoInsertSongScoreList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).GetRecommendTabAllNoInsertSongScoreList(ctx, req.(*GetRecommendTabAllNoInsertSongScoreListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_GetUserComposedAllSongList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserComposedAllSongListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).GetUserComposedAllSongList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/GetUserComposedAllSongList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).GetUserComposedAllSongList(ctx, req.(*GetUserComposedAllSongListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_GetSongAggScore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSongAggScoreReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).GetSongAggScore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/GetSongAggScore",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).GetSongAggScore(ctx, req.(*GetSongAggScoreReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AiRapper_GetBackgroundVideoUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBackgroundVideoUrlReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiRapperServer).GetBackgroundVideoUrl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.airapper.AiRapper/GetBackgroundVideoUrl",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiRapperServer).GetBackgroundVideoUrl(ctx, req.(*GetBackgroundVideoUrlReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _AiRapper_serviceDesc = grpc.ServiceDesc{
	ServiceName: "rhythm.airapper.AiRapper",
	HandlerType: (*AiRapperServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetVoiceWatermark",
			Handler:    _AiRapper_SetVoiceWatermark_Handler,
		},
		{
			MethodName: "GetVoiceWatermark",
			Handler:    _AiRapper_GetVoiceWatermark_Handler,
		},
		{
			MethodName: "DelVoiceWatermark",
			Handler:    _AiRapper_DelVoiceWatermark_Handler,
		},
		{
			MethodName: "CreateTabInfo",
			Handler:    _AiRapper_CreateTabInfo_Handler,
		},
		{
			MethodName: "GetTabInfo",
			Handler:    _AiRapper_GetTabInfo_Handler,
		},
		{
			MethodName: "MoveTabCategory",
			Handler:    _AiRapper_MoveTabCategory_Handler,
		},
		{
			MethodName: "AddSongInfo",
			Handler:    _AiRapper_AddSongInfo_Handler,
		},
		{
			MethodName: "UpdateLyricInfo",
			Handler:    _AiRapper_UpdateLyricInfo_Handler,
		},
		{
			MethodName: "UpdateSongConfiguration",
			Handler:    _AiRapper_UpdateSongConfiguration_Handler,
		},
		{
			MethodName: "GetSongConfigurationInformation",
			Handler:    _AiRapper_GetSongConfigurationInformation_Handler,
		},
		{
			MethodName: "DelSongConfiguration",
			Handler:    _AiRapper_DelSongConfiguration_Handler,
		},
		{
			MethodName: "MoveSongCategory",
			Handler:    _AiRapper_MoveSongCategory_Handler,
		},
		{
			MethodName: "GetLyricInfo",
			Handler:    _AiRapper_GetLyricInfo_Handler,
		},
		{
			MethodName: "BatUpdateComposedVideoUrlInfo",
			Handler:    _AiRapper_BatUpdateComposedVideoUrlInfo_Handler,
		},
		{
			MethodName: "UpdateLevel",
			Handler:    _AiRapper_UpdateLevel_Handler,
		},
		{
			MethodName: "GetLevelInfo",
			Handler:    _AiRapper_GetLevelInfo_Handler,
		},
		{
			MethodName: "DelLevelInfo",
			Handler:    _AiRapper_DelLevelInfo_Handler,
		},
		{
			MethodName: "GetUserAIRapperLevel",
			Handler:    _AiRapper_GetUserAIRapperLevel_Handler,
		},
		{
			MethodName: "PostAIRapperPost",
			Handler:    _AiRapper_PostAIRapperPost_Handler,
		},
		{
			MethodName: "PushPartialKafka",
			Handler:    _AiRapper_PushPartialKafka_Handler,
		},
		{
			MethodName: "PushComposeKafka",
			Handler:    _AiRapper_PushComposeKafka_Handler,
		},
		{
			MethodName: "AddUserLevelAttitude",
			Handler:    _AiRapper_AddUserLevelAttitude_Handler,
		},
		{
			MethodName: "CheckSongFile",
			Handler:    _AiRapper_CheckSongFile_Handler,
		},
		{
			MethodName: "CheckSongExist",
			Handler:    _AiRapper_CheckSongExist_Handler,
		},
		{
			MethodName: "CheckPositionStatus",
			Handler:    _AiRapper_CheckPositionStatus_Handler,
		},
		{
			MethodName: "Aggregation",
			Handler:    _AiRapper_Aggregation_Handler,
		},
		{
			MethodName: "CopyMoveSongsToOtherTab",
			Handler:    _AiRapper_CopyMoveSongsToOtherTab_Handler,
		},
		{
			MethodName: "MoveSongPosition",
			Handler:    _AiRapper_MoveSongPosition_Handler,
		},
		{
			MethodName: "GetH5UrlWithAICtx",
			Handler:    _AiRapper_GetH5UrlWithAICtx_Handler,
		},
		{
			MethodName: "GetAICtxByUUID",
			Handler:    _AiRapper_GetAICtxByUUID_Handler,
		},
		{
			MethodName: "GetSongHeat",
			Handler:    _AiRapper_GetSongHeat_Handler,
		},
		{
			MethodName: "GetLyricInfoBySongID",
			Handler:    _AiRapper_GetLyricInfoBySongID_Handler,
		},
		{
			MethodName: "CheckRepeatedSong",
			Handler:    _AiRapper_CheckRepeatedSong_Handler,
		},
		{
			MethodName: "BatSetSongInfo",
			Handler:    _AiRapper_BatSetSongInfo_Handler,
		},
		{
			MethodName: "GetRecommendTabAllNoInsertSongScoreList",
			Handler:    _AiRapper_GetRecommendTabAllNoInsertSongScoreList_Handler,
		},
		{
			MethodName: "GetUserComposedAllSongList",
			Handler:    _AiRapper_GetUserComposedAllSongList_Handler,
		},
		{
			MethodName: "GetSongAggScore",
			Handler:    _AiRapper_GetSongAggScore_Handler,
		},
		{
			MethodName: "GetBackgroundVideoUrl",
			Handler:    _AiRapper_GetBackgroundVideoUrl_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rhythm/ai-rapper.proto",
}

func init() { proto.RegisterFile("rhythm/ai-rapper.proto", fileDescriptor_ai_rapper_1e66311c5c2cf8e2) }

var fileDescriptor_ai_rapper_1e66311c5c2cf8e2 = []byte{
	// 4122 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x3c, 0x5d, 0x6f, 0x1c, 0x47,
	0x72, 0x9c, 0x5d, 0x72, 0x3f, 0x6a, 0xb9, 0xcb, 0x65, 0x93, 0x14, 0xc9, 0x95, 0x65, 0x53, 0x23,
	0x2b, 0x66, 0x68, 0x5b, 0x3a, 0x53, 0x96, 0x71, 0x4e, 0x0c, 0xdc, 0x91, 0x2b, 0x89, 0x47, 0x58,
	0x26, 0x79, 0x43, 0xd2, 0x3e, 0x5c, 0x82, 0x5b, 0xcc, 0xce, 0x36, 0x97, 0x73, 0x9c, 0x9d, 0x19,
	0x4f, 0xcf, 0x52, 0xa2, 0xf3, 0x61, 0xdc, 0x53, 0x12, 0x20, 0xb8, 0x87, 0x20, 0x0f, 0xf7, 0x90,
	0x97, 0x04, 0xc1, 0x5d, 0xfe, 0x40, 0x5e, 0x02, 0xe4, 0xa7, 0x04, 0x09, 0x90, 0xbf, 0x11, 0x04,
	0x87, 0xae, 0x9e, 0x99, 0x9d, 0x8f, 0x9e, 0xe5, 0x88, 0xd2, 0x19, 0x7e, 0xe3, 0x54, 0x57, 0x57,
	0x55, 0x57, 0x57, 0x57, 0x55, 0x57, 0xd7, 0x12, 0x6e, 0x79, 0xe7, 0x57, 0xfe, 0xf9, 0xe8, 0xa1,
	0x6e, 0x7e, 0xe8, 0xe9, 0xae, 0x4b, 0xbd, 0x07, 0xae, 0xe7, 0xf8, 0x0e, 0x59, 0x10, 0xf0, 0x07,
	0xba, 0x29, 0xc0, 0xea, 0x11, 0xac, 0xed, 0x51, 0x7f, 0x57, 0x37, 0x2e, 0x86, 0x9e, 0x33, 0xb6,
	0x07, 0x5f, 0x9a, 0x03, 0xea, 0x9c, 0x7a, 0x96, 0x46, 0xbf, 0x26, 0xb7, 0xa1, 0x7e, 0xc9, 0x3f,
	0x7b, 0xa3, 0xc1, 0xe3, 0x35, 0x65, 0x43, 0xd9, 0xac, 0x6b, 0x35, 0x04, 0x7c, 0x31, 0x78, 0x4c,
	0x56, 0xa1, 0xca, 0x1c, 0x7b, 0xd8, 0x33, 0x07, 0x6b, 0x25, 0x1c, 0xaa, 0xf0, 0xcf, 0xfd, 0x81,
	0x7a, 0x0a, 0xeb, 0x39, 0x14, 0x99, 0x4b, 0x7e, 0x08, 0xb3, 0xa6, 0x7d, 0xe6, 0xac, 0x29, 0x1b,
	0xe5, 0xcd, 0xc6, 0xf6, 0xbb, 0x0f, 0x52, 0xe2, 0x3c, 0x48, 0x4d, 0xdb, 0xb7, 0xcf, 0x1c, 0x0d,
	0x67, 0xa8, 0xff, 0xa6, 0xc0, 0x92, 0x64, 0x74, 0x22, 0xe4, 0xd8, 0xb3, 0x12, 0x42, 0x9e, 0x7a,
	0x56, 0x72, 0x05, 0xa5, 0xd4, 0x0a, 0x3e, 0x84, 0x25, 0x76, 0xae, 0x7b, 0xb4, 0x67, 0x38, 0x97,
	0xd4, 0xeb, 0xb9, 0xa6, 0x81, 0x34, 0xca, 0x88, 0xd6, 0xc6, 0xa1, 0x2e, 0x1f, 0x39, 0x32, 0x0d,
	0x4e, 0x6b, 0x13, 0xda, 0x8e, 0x67, 0x0e, 0x4d, 0xbb, 0x37, 0xe1, 0x37, 0x8b, 0xb8, 0x2d, 0x01,
	0x0f, 0x17, 0xaa, 0x6a, 0x40, 0xf6, 0xa8, 0x7f, 0xec, 0xd8, 0xc3, 0x9d, 0xe1, 0xf0, 0xd8, 0x70,
	0x3c, 0x1a, 0x68, 0x13, 0x15, 0x66, 0xeb, 0x23, 0x1a, 0x0a, 0xca, 0x01, 0x07, 0xfa, 0x88, 0x92,
	0x77, 0xa0, 0x81, 0x83, 0x42, 0x0d, 0x81, 0xa8, 0xc0, 0x41, 0x9a, 0xd8, 0xa7, 0x1f, 0xc1, 0x52,
	0x8a, 0x26, 0xae, 0x9e, 0xc0, 0x6c, 0x8c, 0x1e, 0xfe, 0x4d, 0x96, 0x61, 0x8e, 0x71, 0x04, 0xa4,
	0x52, 0xd2, 0xc4, 0x87, 0x7a, 0x98, 0x21, 0x50, 0x68, 0x43, 0x24, 0x4c, 0x83, 0x0d, 0xf9, 0x00,
	0xb6, 0xf6, 0xa8, 0xaf, 0x51, 0xc3, 0x19, 0x8d, 0xa8, 0x3d, 0x38, 0xd1, 0xfb, 0x3b, 0x96, 0x75,
	0xe0, 0xec, 0xdb, 0x8c, 0x7a, 0x38, 0x07, 0x27, 0x3c, 0x37, 0x99, 0xaf, 0xd1, 0xaf, 0xd5, 0x5f,
	0x29, 0xf0, 0x7e, 0x41, 0xf4, 0x70, 0x5b, 0x6f, 0xae, 0xad, 0x89, 0x0a, 0xca, 0x71, 0x15, 0x7c,
	0x5b, 0x58, 0x04, 0x54, 0xcd, 0x51, 0x42, 0x35, 0x9f, 0xc9, 0x54, 0x53, 0x74, 0x39, 0x81, 0xca,
	0x3e, 0x82, 0x3b, 0x7b, 0xd4, 0x3f, 0x65, 0xd4, 0xeb, 0x3a, 0x23, 0xd7, 0x61, 0x74, 0xb0, 0x63,
	0x59, 0x1c, 0x39, 0xd0, 0x12, 0x69, 0x43, 0x79, 0x6c, 0x0e, 0x70, 0xbd, 0x4d, 0x8d, 0xff, 0xa9,
	0xee, 0xc0, 0xdb, 0xd3, 0xa6, 0x30, 0x37, 0xad, 0x0c, 0x2e, 0x6d, 0xd2, 0x74, 0xc6, 0xb0, 0x7c,
	0x4c, 0xfd, 0x2f, 0x1d, 0xd3, 0xa0, 0x5f, 0xe9, 0x3e, 0xf5, 0x46, 0xba, 0x77, 0x21, 0x65, 0x46,
	0xee, 0x00, 0x30, 0x5f, 0x1f, 0x52, 0xa1, 0x75, 0xa1, 0xd6, 0x3a, 0x42, 0x50, 0xed, 0x1f, 0x00,
	0xb9, 0xe4, 0x54, 0x7a, 0x46, 0x20, 0x4a, 0xef, 0x82, 0x5e, 0x85, 0xe7, 0x05, 0x47, 0x42, 0x19,
	0x3f, 0xa7, 0x57, 0xea, 0x2a, 0xac, 0x48, 0xd8, 0x32, 0x57, 0xdd, 0x84, 0xe5, 0xbd, 0x42, 0xf2,
	0xa8, 0xc7, 0xb0, 0xb2, 0x27, 0x23, 0x91, 0x12, 0x54, 0x49, 0x0b, 0xca, 0x8f, 0x3d, 0x0a, 0xca,
	0xcf, 0x68, 0x78, 0xec, 0x39, 0x80, 0x9f, 0xce, 0x4d, 0x58, 0x7e, 0x42, 0xad, 0x22, 0xec, 0x57,
	0x61, 0x45, 0x82, 0xc9, 0x5c, 0xf5, 0xbf, 0x14, 0xa8, 0x9e, 0xe8, 0x7d, 0x34, 0xd4, 0x15, 0xa8,
	0xf8, 0x7a, 0xbf, 0x17, 0xcc, 0xac, 0x6b, 0x73, 0xbe, 0xde, 0xdf, 0x1f, 0x90, 0x75, 0xa8, 0x71,
	0x70, 0x4c, 0x91, 0x55, 0x5f, 0xef, 0xa3, 0x74, 0x9f, 0x40, 0x85, 0xf9, 0xba, 0x3f, 0x66, 0xa8,
	0xba, 0xd6, 0xf6, 0xdb, 0x19, 0xcb, 0x3a, 0xd1, 0xfb, 0xc7, 0x88, 0x81, 0xb6, 0x13, 0x60, 0x93,
	0x35, 0xa8, 0x1a, 0x8e, 0xed, 0x53, 0xdb, 0x0f, 0xfc, 0x4e, 0xf8, 0xc9, 0x4d, 0x60, 0xec, 0x0e,
	0x74, 0x9f, 0xf6, 0x7c, 0x73, 0x44, 0xd7, 0xe6, 0x70, 0x09, 0x20, 0x40, 0x27, 0xa6, 0x70, 0x09,
	0xa6, 0x3d, 0xa0, 0x2f, 0xd7, 0x2a, 0x38, 0x24, 0x3e, 0xb8, 0x0b, 0xd7, 0xcd, 0x9e, 0x7f, 0xe5,
	0xd2, 0xb5, 0x2a, 0xc2, 0x2b, 0xba, 0x79, 0x72, 0xe5, 0x52, 0x75, 0x0f, 0xda, 0x5d, 0x8f, 0xf2,
	0xc9, 0x62, 0x91, 0x5c, 0x3d, 0x8f, 0xc4, 0x82, 0x82, 0x13, 0xa1, 0x6c, 0x36, 0xb6, 0xd7, 0x64,
	0x72, 0x23, 0x3a, 0x5f, 0x2a, 0xff, 0x43, 0x5d, 0x82, 0xc5, 0x14, 0x21, 0xdc, 0xff, 0xe6, 0x1e,
	0xf5, 0x63, 0xa4, 0x63, 0x72, 0x28, 0x09, 0x39, 0x9e, 0x42, 0x2b, 0x8e, 0xc9, 0xdc, 0x94, 0x14,
	0xe5, 0x62, 0x52, 0xfc, 0xbd, 0x02, 0xe4, 0x0b, 0xe7, 0x92, 0x0b, 0xd1, 0xd5, 0x7d, 0x3a, 0x74,
	0xbc, 0x2b, 0xce, 0x36, 0x67, 0xe7, 0xee, 0x41, 0xd3, 0x18, 0x7b, 0x1e, 0xb5, 0xfd, 0x9e, 0xd0,
	0x59, 0x09, 0x65, 0x9a, 0x0f, 0x80, 0xfb, 0xa8, 0xba, 0xbb, 0x30, 0xef, 0xeb, 0xde, 0x90, 0x86,
	0x38, 0x65, 0xc4, 0x69, 0x08, 0xd8, 0x7e, 0x5a, 0xbb, 0xb3, 0x89, 0x55, 0xad, 0xc0, 0x52, 0x46,
	0x1a, 0xe6, 0xaa, 0xff, 0xaa, 0x00, 0x7a, 0xb8, 0x37, 0xe0, 0xfe, 0x62, 0x96, 0x52, 0x46, 0x77,
	0x10, 0x59, 0xca, 0x3a, 0xd4, 0x2c, 0x7a, 0x49, 0x2d, 0xbe, 0xea, 0xc0, 0x88, 0xf0, 0x7b, 0x7f,
	0x40, 0xee, 0x43, 0x6b, 0x34, 0x66, 0xa6, 0xd1, 0x73, 0x3d, 0x67, 0x30, 0x36, 0xa8, 0x87, 0x76,
	0x54, 0xd7, 0x9a, 0x08, 0x3d, 0x0a, 0x80, 0xea, 0x7f, 0x97, 0xa1, 0x3a, 0xf6, 0xac, 0x37, 0x20,
	0xe5, 0x6d, 0xa8, 0x5b, 0x57, 0x5e, 0x22, 0xea, 0xd6, 0x10, 0xc0, 0xa3, 0xed, 0x3a, 0xd4, 0xce,
	0x4c, 0x8b, 0x62, 0xe0, 0x0e, 0x04, 0xe5, 0xdf, 0x3c, 0x6e, 0x73, 0xdd, 0xd3, 0x91, 0x6b, 0x71,
	0x7b, 0xe7, 0x53, 0x85, 0x98, 0x8d, 0x10, 0xc6, 0x67, 0x6f, 0xc1, 0x62, 0x84, 0x12, 0x91, 0xa9,
	0x20, 0xde, 0x42, 0x38, 0xf0, 0x2c, 0x20, 0x77, 0x1f, 0x16, 0x6c, 0xfa, 0xa2, 0x27, 0x44, 0xd1,
	0x91, 0x62, 0x15, 0x31, 0xe7, 0x6d, 0xfa, 0xe2, 0x39, 0x87, 0xee, 0x70, 0x92, 0x29, 0x34, 0x4e,
	0xb0, 0x96, 0x42, 0xcb, 0x50, 0xeb, 0x23, 0xb5, 0x7a, 0x12, 0x6d, 0x37, 0x43, 0xad, 0x8f, 0xd4,
	0x20, 0x85, 0x96, 0xa1, 0xf6, 0x31, 0x52, 0x6b, 0x24, 0xd1, 0x3e, 0xce, 0x50, 0xfb, 0x18, 0xa9,
	0xcd, 0xa7, 0xd0, 0x04, 0xb5, 0xf4, 0x0e, 0x37, 0x65, 0x3b, 0xfc, 0x12, 0x5a, 0x3b, 0x83, 0xc1,
	0x71, 0x60, 0x8a, 0x53, 0x4e, 0xca, 0x27, 0xc1, 0xf6, 0xe3, 0x69, 0x2c, 0xe1, 0x69, 0x5c, 0xcf,
	0x9c, 0xc6, 0xd0, 0xa4, 0xb5, 0x89, 0x71, 0xc7, 0x4e, 0x46, 0x39, 0x71, 0x32, 0xbe, 0x84, 0x85,
	0x04, 0x67, 0xe6, 0x92, 0x2e, 0xb4, 0x07, 0x63, 0xd7, 0x32, 0x0d, 0xdd, 0x37, 0x2f, 0x69, 0x8f,
	0xd3, 0xc8, 0x3d, 0xf8, 0x1a, 0x15, 0x0e, 0x73, 0x21, 0x36, 0x83, 0x13, 0x53, 0x7f, 0xa3, 0x00,
	0x39, 0x45, 0x6f, 0x88, 0xca, 0xb8, 0x66, 0x59, 0x8f, 0xa0, 0x36, 0xf6, 0xac, 0xf8, 0xaa, 0xb2,
	0xac, 0x82, 0x13, 0xa0, 0x45, 0x47, 0xe1, 0x0e, 0x80, 0x71, 0x4e, 0x8d, 0x8b, 0xf8, 0xb2, 0xea,
	0x08, 0xe1, 0x2b, 0xcb, 0x77, 0x06, 0x7b, 0x50, 0x0d, 0xc4, 0x7e, 0xcd, 0x04, 0xf1, 0x37, 0x0a,
	0x2c, 0x65, 0xd6, 0xf8, 0x86, 0x14, 0x48, 0x3e, 0x83, 0xa6, 0xed, 0xf4, 0xe8, 0x4b, 0x93, 0xf9,
	0x82, 0x42, 0xe9, 0x1a, 0x0a, 0x0d, 0xdb, 0x79, 0xca, 0xb1, 0x43, 0xf5, 0xb7, 0xbb, 0x5c, 0x15,
	0xfc, 0x8b, 0x1f, 0xbb, 0x29, 0xca, 0x7f, 0x1c, 0x28, 0xc1, 0x32, 0x99, 0x7f, 0x2d, 0x17, 0x54,
	0x0f, 0x4f, 0x84, 0x6e, 0xac, 0xfe, 0xdf, 0x96, 0x60, 0x31, 0x25, 0xda, 0xf7, 0x42, 0x67, 0xe4,
	0x08, 0xe0, 0x52, 0xb7, 0xcc, 0x81, 0x98, 0x5a, 0xc6, 0xa9, 0x1f, 0x65, 0xa6, 0x66, 0x44, 0x7f,
	0xf0, 0x25, 0x9f, 0xc4, 0x21, 0x4f, 0x6d, 0xdf, 0xbb, 0xd2, 0xea, 0x97, 0xe1, 0x77, 0xe7, 0x33,
	0x68, 0x25, 0x07, 0x79, 0xc6, 0xc3, 0x13, 0x38, 0xa1, 0x7f, 0xfe, 0x27, 0xcf, 0x13, 0x2e, 0x75,
	0x6b, 0x4c, 0x83, 0x98, 0x27, 0x3e, 0xfe, 0xa4, 0xf4, 0x43, 0x45, 0xfd, 0x26, 0xa6, 0x27, 0x94,
	0xf2, 0xcd, 0xef, 0x61, 0xae, 0x5b, 0xf8, 0x5f, 0x05, 0x48, 0x9a, 0x39, 0x73, 0xb3, 0x0a, 0x56,
	0x5e, 0x45, 0xc1, 0x3f, 0x4d, 0x28, 0x58, 0x48, 0xb9, 0x9d, 0xaf, 0xe0, 0x88, 0xed, 0x1f, 0x4c,
	0xc3, 0xff, 0xae, 0x40, 0x4d, 0x5c, 0x21, 0xb5, 0xe7, 0x64, 0x03, 0xe6, 0xfb, 0xc3, 0x5e, 0xfa,
	0x62, 0x0b, 0xfd, 0x61, 0x78, 0xc9, 0x4c, 0x60, 0x4c, 0x6e, 0xb7, 0x21, 0x46, 0x70, 0xbf, 0xed,
	0x0f, 0x7b, 0x99, 0x3b, 0x6b, 0x90, 0xaf, 0xf7, 0x87, 0x87, 0x89, 0x5b, 0x2b, 0xf9, 0x04, 0xd6,
	0x22, 0x82, 0x67, 0xa6, 0xc7, 0xfc, 0xde, 0x99, 0xa7, 0x8f, 0x68, 0xec, 0x9e, 0xbb, 0x1c, 0x10,
	0x7f, 0xc6, 0x47, 0x9f, 0xf1, 0x41, 0x9e, 0x4f, 0xff, 0xff, 0x1c, 0xac, 0x0a, 0xc7, 0xc3, 0xd7,
	0xdd, 0x75, 0xec, 0x33, 0x73, 0x38, 0xf6, 0x74, 0xdf, 0x74, 0x6c, 0x6e, 0x20, 0x9e, 0xee, 0xc6,
	0x0c, 0xc4, 0xd3, 0xdd, 0xfd, 0x41, 0xd2, 0xd3, 0x95, 0xa6, 0x7b, 0xba, 0xf2, 0xb4, 0xec, 0x66,
	0x36, 0x99, 0xdd, 0x4c, 0xec, 0x71, 0x2e, 0x6e, 0x8f, 0x89, 0x44, 0xa3, 0x32, 0x25, 0xd1, 0xa8,
	0x26, 0x13, 0x8d, 0x47, 0x51, 0xa2, 0x5e, 0xc3, 0x44, 0xfd, 0x76, 0xc6, 0x3c, 0x24, 0x59, 0x7a,
	0x3c, 0xc3, 0xaa, 0x27, 0x33, 0xac, 0x74, 0xe2, 0x02, 0x05, 0x13, 0x97, 0x86, 0x3c, 0x71, 0xb9,
	0x07, 0x4d, 0x54, 0x94, 0xeb, 0x30, 0x93, 0x6b, 0x1b, 0x63, 0x7e, 0x53, 0x9b, 0xe7, 0xc0, 0xa3,
	0x00, 0x26, 0xcb, 0x6e, 0x9a, 0xc5, 0xb2, 0x9b, 0x56, 0xb1, 0xec, 0x66, 0xa1, 0x58, 0x76, 0xd3,
	0x2e, 0x96, 0xdd, 0x2c, 0x16, 0xcb, 0x6e, 0x48, 0xa1, 0xec, 0x66, 0x49, 0x92, 0xdd, 0x90, 0x23,
	0x58, 0x8d, 0xae, 0xaf, 0xd1, 0xa1, 0x10, 0xc1, 0x7e, 0x39, 0x27, 0x85, 0x09, 0x4f, 0xa5, 0xb6,
	0x1c, 0xce, 0x0c, 0x0f, 0x0d, 0x5e, 0x2f, 0x7e, 0xad, 0x40, 0x27, 0xe7, 0x00, 0x70, 0x27, 0xf9,
	0x15, 0x10, 0xdc, 0x26, 0x23, 0x3e, 0x10, 0x5c, 0xa1, 0x36, 0x33, 0xbc, 0xf2, 0x08, 0x2d, 0xb2,
	0xcc, 0xe1, 0x8a, 0xf9, 0xcb, 0x52, 0xc2, 0x5f, 0xde, 0x81, 0xdb, 0xb9, 0xf2, 0x30, 0x57, 0xfd,
	0x0f, 0x05, 0x16, 0xc5, 0x48, 0x6c, 0x31, 0x79, 0xbe, 0xfc, 0xf5, 0x8e, 0xea, 0x14, 0x65, 0xcf,
	0xde, 0x4c, 0xd9, 0x7f, 0x09, 0x1b, 0xbb, 0xba, 0x2f, 0x96, 0xd7, 0x95, 0x20, 0x4c, 0xbb, 0x4f,
	0x92, 0x1f, 0x41, 0x9d, 0xf3, 0x8e, 0x07, 0x26, 0x35, 0x7b, 0xa6, 0xd3, 0xaa, 0xd1, 0x6a, 0x7c,
	0x12, 0x0f, 0x51, 0xea, 0x3d, 0xb8, 0x7b, 0x0d, 0x77, 0xae, 0xdf, 0x0a, 0xbc, 0x95, 0xd1, 0x3c,
	0x1f, 0xf5, 0x46, 0xdf, 0x27, 0xaf, 0x18, 0x77, 0x54, 0x95, 0xa4, 0xa3, 0x4a, 0x38, 0xcc, 0xea,
	0x14, 0x87, 0x59, 0xcb, 0x73, 0x98, 0xf5, 0xe2, 0x0e, 0x33, 0x55, 0xbc, 0x80, 0xfc, 0xe2, 0x45,
	0x23, 0x5e, 0xbc, 0x48, 0x3b, 0xd3, 0xf9, 0x82, 0xce, 0xb4, 0x59, 0xd0, 0x99, 0xb6, 0x8a, 0x39,
	0xd3, 0x85, 0x62, 0xce, 0xb4, 0x5d, 0xcc, 0x99, 0x2e, 0x16, 0x73, 0xa6, 0xa4, 0x98, 0x33, 0x5d,
	0x2a, 0xe6, 0x4c, 0x97, 0x0b, 0x39, 0xd3, 0x95, 0x57, 0x74, 0xa6, 0xb7, 0x6e, 0x76, 0xbe, 0xff,
	0xaf, 0x04, 0x6a, 0x50, 0x73, 0xce, 0x3b, 0x3f, 0x41, 0xe6, 0xf9, 0x07, 0x3e, 0x42, 0xca, 0xeb,
	0x1d, 0xa1, 0x4f, 0x01, 0xd0, 0xe4, 0x30, 0x3d, 0xc5, 0x33, 0xd4, 0xda, 0xee, 0x64, 0x94, 0xc1,
	0xad, 0x4f, 0x64, 0x96, 0xf5, 0xb3, 0xf0, 0xcf, 0x9b, 0xa5, 0x1d, 0xb7, 0xa0, 0xe2, 0x9c, 0x9d,
	0x31, 0xea, 0xe3, 0xd1, 0x6b, 0x6a, 0xc1, 0x17, 0x3f, 0x3c, 0x96, 0x39, 0x32, 0xfd, 0xe0, 0x5c,
	0x89, 0x8f, 0xb8, 0x87, 0x6c, 0x24, 0x42, 0xc7, 0xbf, 0x28, 0x70, 0xef, 0x5a, 0xf5, 0x33, 0x97,
	0x50, 0x58, 0xcd, 0x06, 0xb5, 0x78, 0x59, 0xee, 0xc3, 0x1c, 0xbf, 0x9a, 0x43, 0x73, 0x85, 0xc9,
	0x46, 0xb9, 0xf4, 0xbe, 0xe3, 0xeb, 0x56, 0x98, 0x2d, 0xe3, 0x87, 0xfa, 0x03, 0x58, 0x7d, 0x42,
	0x2d, 0x69, 0xb0, 0x95, 0xdb, 0x85, 0xda, 0x81, 0x35, 0xf9, 0x0c, 0x51, 0x77, 0xc3, 0x7a, 0x1c,
	0x8e, 0x26, 0xcb, 0x83, 0x32, 0x13, 0x7b, 0x53, 0xe5, 0xc1, 0x89, 0x49, 0xcd, 0xc6, 0x4d, 0x2a,
	0xb6, 0x33, 0x73, 0x89, 0x9d, 0xb9, 0x05, 0xcb, 0x59, 0x29, 0x99, 0xab, 0xfe, 0xb3, 0x02, 0x9d,
	0xae, 0xe3, 0x5e, 0x85, 0x83, 0xec, 0xc4, 0x39, 0xf4, 0xcf, 0xa9, 0x77, 0xa2, 0xf7, 0xf9, 0x2a,
	0xde, 0x85, 0x96, 0xe1, 0xb8, 0x57, 0xbd, 0x91, 0x73, 0x49, 0xe3, 0x21, 0x71, 0xde, 0x08, 0xe6,
	0x60, 0x60, 0xdc, 0x80, 0xf9, 0xe0, 0x31, 0x6f, 0x12, 0x1b, 0x83, 0xb3, 0xb1, 0x3f, 0xc0, 0xcb,
	0x99, 0x0a, 0xcd, 0x60, 0x45, 0x81, 0xd4, 0xe5, 0xc0, 0xdf, 0x22, 0xf0, 0x64, 0xba, 0xec, 0x7d,
	0xb8, 0x9d, 0x2b, 0xe2, 0x9b, 0xaa, 0xf1, 0xfc, 0x4a, 0x81, 0x85, 0x3d, 0xea, 0x27, 0x0a, 0x3c,
	0xd9, 0x17, 0x8e, 0x89, 0xd6, 0x4b, 0x71, 0xad, 0x47, 0xa7, 0xa4, 0x1c, 0x3f, 0x25, 0x93, 0x33,
	0x35, 0x9b, 0x38, 0x53, 0xb9, 0xeb, 0xdc, 0x87, 0x46, 0x57, 0xf7, 0x06, 0xbb, 0x7b, 0x22, 0xa5,
	0x9a, 0xfa, 0x34, 0x79, 0x07, 0xa0, 0x3f, 0xf6, 0x7d, 0xc7, 0x8e, 0x3d, 0x52, 0xd4, 0x05, 0x84,
	0xdf, 0xaa, 0xfe, 0x61, 0x0e, 0xea, 0xd1, 0x5a, 0xe2, 0x8f, 0xad, 0x4a, 0xfc, 0xb1, 0x75, 0xba,
	0xc3, 0x5b, 0x87, 0x1a, 0xb7, 0x60, 0x1c, 0x13, 0xdb, 0x55, 0xf5, 0x74, 0x17, 0x87, 0xee, 0x41,
	0x53, 0x84, 0x80, 0x64, 0xce, 0x30, 0x8f, 0xc0, 0x6e, 0xe0, 0xf5, 0x12, 0x69, 0xc0, 0xdc, 0x94,
	0x34, 0xa0, 0x92, 0x4c, 0x03, 0xee, 0x00, 0x08, 0xb7, 0x88, 0x9c, 0x45, 0xfe, 0x50, 0x47, 0x48,
	0xe8, 0x87, 0xc5, 0xb0, 0x78, 0xa2, 0xab, 0x89, 0x80, 0x8f, 0x20, 0x7c, 0x34, 0xe3, 0x7c, 0x4d,
	0xd6, 0xb3, 0x1c, 0xe3, 0x82, 0x8a, 0x3b, 0x54, 0x4d, 0xab, 0x99, 0xec, 0x39, 0x7e, 0xcb, 0x62,
	0x30, 0x14, 0x8b, 0xc1, 0x8d, 0x62, 0x31, 0x78, 0xbe, 0x58, 0x0c, 0x6e, 0x4a, 0x63, 0x70, 0x3a,
	0x6a, 0xb6, 0x64, 0x51, 0xf3, 0x03, 0x20, 0x67, 0x9e, 0x49, 0xed, 0x41, 0xcf, 0x30, 0x3d, 0xc3,
	0xa2, 0xbd, 0x73, 0xaa, 0xfb, 0x98, 0x49, 0xcc, 0x6a, 0x6d, 0x31, 0xd2, 0xc5, 0x81, 0x9f, 0x50,
	0xdd, 0x27, 0x3f, 0x86, 0x3b, 0x59, 0xec, 0xde, 0x40, 0xf7, 0xc7, 0xa3, 0x9e, 0x65, 0xda, 0x14,
	0x73, 0x8b, 0x59, 0x6d, 0x3d, 0x3d, 0xf1, 0x09, 0xc7, 0x78, 0x6e, 0xda, 0x94, 0xec, 0xc1, 0xa2,
	0xa1, 0x7b, 0x83, 0x5e, 0xa2, 0xa2, 0xb0, 0x88, 0x17, 0x90, 0xb7, 0xb2, 0x15, 0x8f, 0x89, 0x01,
	0x6b, 0x2d, 0x3e, 0x6d, 0x37, 0xaa, 0x39, 0xa8, 0xe7, 0xd0, 0x4e, 0x1e, 0x31, 0xe6, 0xf2, 0x40,
	0x27, 0xd4, 0x12, 0x73, 0xfe, 0xd9, 0x40, 0x37, 0x99, 0x23, 0x4c, 0x0a, 0xad, 0x7a, 0x1d, 0x6a,
	0xe7, 0x3a, 0xeb, 0x8d, 0xc2, 0xb7, 0xea, 0x9a, 0x56, 0x3d, 0xd7, 0xd9, 0x17, 0x8e, 0x47, 0xd5,
	0x6f, 0xa0, 0x15, 0x14, 0x33, 0xb9, 0x59, 0xf0, 0xb3, 0xdc, 0x82, 0x52, 0x64, 0xfd, 0x25, 0xf1,
	0x56, 0x19, 0x33, 0xb2, 0xd2, 0x35, 0x46, 0x56, 0xce, 0x18, 0x59, 0x6e, 0xb0, 0x57, 0x17, 0x61,
	0x21, 0xc1, 0x9b, 0xb9, 0x1c, 0xc4, 0x17, 0x8e, 0xb1, 0x5d, 0xf8, 0x16, 0xf5, 0x3f, 0x15, 0xa8,
	0x47, 0x80, 0xef, 0x4e, 0xba, 0xeb, 0xdf, 0xfa, 0xee, 0x43, 0xcb, 0xa3, 0x16, 0x15, 0x11, 0x19,
	0x7d, 0xa9, 0x78, 0xf4, 0x6b, 0x46, 0x50, 0xf4, 0x97, 0x5f, 0x88, 0xbd, 0x9c, 0x2c, 0x29, 0xd8,
	0x4b, 0x91, 0xcf, 0x4c, 0xdd, 0xcb, 0x68, 0x8e, 0x58, 0x11, 0xe6, 0x6d, 0x77, 0x61, 0xe1, 0x09,
	0xb5, 0xe2, 0x1a, 0x4a, 0xeb, 0x44, 0x25, 0xd0, 0x4e, 0xa2, 0x30, 0x57, 0xfd, 0x05, 0x34, 0x10,
	0x20, 0xc2, 0x72, 0x4a, 0x6d, 0x4a, 0x5a, 0x6d, 0xab, 0x50, 0x1d, 0x99, 0x76, 0xcf, 0x1e, 0x8f,
	0xc2, 0x1b, 0xef, 0xc8, 0xb4, 0x0f, 0xc6, 0x23, 0x1c, 0xd0, 0x5f, 0xe2, 0x40, 0x50, 0x3a, 0x1c,
	0xe9, 0x2f, 0x0f, 0xc6, 0x23, 0x1e, 0x15, 0xea, 0xa7, 0x8c, 0x7a, 0xc8, 0x44, 0x12, 0x0f, 0xd6,
	0xa0, 0xaa, 0x1b, 0x86, 0x33, 0xb6, 0xfd, 0xf0, 0x95, 0x36, 0xf8, 0x24, 0xdb, 0x30, 0x87, 0x8c,
	0x91, 0xa0, 0xec, 0xa0, 0xc4, 0xe4, 0xd6, 0x04, 0x2a, 0x0f, 0x23, 0xae, 0x63, 0xda, 0x61, 0xbc,
	0x10, 0x1f, 0xea, 0xfb, 0xb0, 0x1a, 0x3c, 0xe1, 0xef, 0xec, 0x8b, 0x4c, 0x33, 0x32, 0xea, 0xec,
	0x9b, 0xf3, 0x29, 0xf6, 0xe3, 0x48, 0x90, 0xc5, 0xf6, 0x8c, 0x19, 0xf5, 0x7a, 0x42, 0x2e, 0x51,
	0x41, 0xc8, 0x6e, 0x4f, 0xb4, 0x5c, 0xad, 0x3e, 0x0e, 0xff, 0x54, 0xff, 0x4e, 0x81, 0xa5, 0xa3,
	0x31, 0x3b, 0x3f, 0xd2, 0x3d, 0xdf, 0xd4, 0xad, 0xcf, 0xf5, 0xb3, 0x0b, 0x5d, 0x1e, 0x21, 0xdf,
	0x82, 0x3a, 0x37, 0x2c, 0xe6, 0xeb, 0x23, 0x37, 0xd0, 0xf2, 0x04, 0x30, 0x69, 0x01, 0x60, 0xbe,
	0xee, 0xf9, 0x3d, 0x46, 0x0d, 0xc7, 0x1e, 0x04, 0x3a, 0x17, 0x2d, 0x00, 0xc7, 0x7c, 0xe0, 0x18,
	0xe1, 0x93, 0x36, 0x8c, 0x40, 0x1f, 0xa2, 0x0d, 0xe3, 0x16, 0x2c, 0x67, 0x45, 0x61, 0xae, 0xda,
	0x17, 0x22, 0x06, 0xf7, 0xea, 0x1b, 0x8b, 0x98, 0x78, 0xfc, 0x2f, 0xa7, 0x1e, 0xff, 0x03, 0xde,
	0x49, 0x1e, 0xcc, 0x55, 0x0f, 0x60, 0x75, 0x67, 0x30, 0x88, 0x54, 0xb7, 0xe3, 0xfb, 0xa6, 0x3f,
	0x1e, 0x50, 0x39, 0xff, 0xbb, 0x30, 0xaf, 0x07, 0x08, 0x3d, 0x23, 0xb0, 0x9c, 0x39, 0xad, 0x11,
	0xc2, 0xba, 0xb6, 0xcf, 0x13, 0x4e, 0x39, 0x3d, 0xe6, 0xaa, 0x3f, 0x86, 0xa5, 0x23, 0x87, 0xf9,
	0xe1, 0xfe, 0xf2, 0xbf, 0xe5, 0x7c, 0x56, 0xa1, 0xea, 0x3a, 0xcc, 0x8f, 0xb5, 0x58, 0xf1, 0xcf,
	0xfd, 0x01, 0xae, 0x22, 0x43, 0x81, 0xb9, 0xea, 0x3f, 0x2a, 0x70, 0x0b, 0x2b, 0xd6, 0xe1, 0x8d,
	0x55, 0x5c, 0x14, 0x5e, 0xbb, 0xfb, 0x28, 0x7b, 0x3b, 0x2e, 0x4b, 0x6e, 0xc7, 0xb9, 0x8f, 0x2c,
	0x07, 0xb0, 0x2a, 0x95, 0x4a, 0xbc, 0xe7, 0x7b, 0x94, 0x4d, 0xef, 0x2a, 0x08, 0x53, 0xbe, 0xaa,
	0x27, 0xfe, 0x50, 0x7f, 0xa7, 0xc0, 0xc2, 0xce, 0x70, 0xe8, 0xd1, 0xe1, 0xe4, 0xa6, 0xf0, 0x53,
	0x20, 0xfa, 0x04, 0xd4, 0x33, 0xce, 0x1d, 0x87, 0x89, 0x85, 0xb6, 0x24, 0x35, 0x9e, 0xd8, 0xec,
	0x2e, 0x62, 0x6a, 0x8b, 0x7a, 0x1a, 0x94, 0xdb, 0xe1, 0xc6, 0xf3, 0x42, 0x7b, 0x3c, 0xea, 0x07,
	0x77, 0xc8, 0xa6, 0x16, 0x7c, 0x85, 0x3b, 0x38, 0x3b, 0x39, 0xcd, 0xdf, 0x00, 0x49, 0x09, 0xca,
	0xf7, 0xe2, 0x73, 0x68, 0xc7, 0x65, 0x8d, 0x39, 0xdb, 0x8d, 0x69, 0x92, 0x8a, 0xbc, 0x57, 0x4f,
	0x2d, 0x3c, 0xb7, 0x0a, 0xb8, 0x02, 0x4b, 0x19, 0xde, 0xcc, 0x55, 0x8f, 0xa0, 0x7d, 0x1c, 0xdb,
	0xb5, 0xe9, 0xe9, 0x65, 0x66, 0xdf, 0x4b, 0xd9, 0x7d, 0x57, 0xbf, 0x9d, 0xdc, 0x9f, 0x42, 0x18,
	0x5f, 0xe5, 0x61, 0x50, 0xf7, 0x0c, 0xe7, 0xc6, 0xd7, 0x79, 0x57, 0x7a, 0x3b, 0x8c, 0xcb, 0xa4,
	0xb5, 0x99, 0x44, 0x4a, 0xf9, 0x4a, 0x63, 0x57, 0xa3, 0x89, 0x00, 0xcc, 0x55, 0xff, 0xa9, 0x84,
	0x9d, 0x46, 0x3f, 0x79, 0x7c, 0xea, 0x59, 0x5f, 0x99, 0xfe, 0xf9, 0xce, 0x7e, 0xd7, 0x7f, 0x29,
	0x3f, 0x6a, 0x3f, 0x80, 0xe5, 0x7e, 0xd4, 0x5c, 0x18, 0xcb, 0x92, 0xc4, 0xc6, 0x93, 0x7e, 0xa6,
	0x9b, 0x71, 0xaa, 0x9b, 0x49, 0x9e, 0xb6, 0xd9, 0xd4, 0x69, 0xbb, 0x05, 0x15, 0xcc, 0x81, 0xd8,
	0xda, 0x1c, 0x26, 0xdd, 0xc1, 0x57, 0x7c, 0x17, 0x2a, 0xe9, 0x24, 0x7f, 0xa4, 0x7b, 0x17, 0x14,
	0x3d, 0x81, 0xe8, 0xd4, 0xa9, 0x09, 0xc0, 0xfe, 0x80, 0x9f, 0x5d, 0xc3, 0x32, 0xf9, 0x75, 0x14,
	0x35, 0x13, 0x64, 0xd3, 0x02, 0x94, 0x7e, 0xfb, 0xac, 0x27, 0xd4, 0xf6, 0x4b, 0xec, 0xae, 0x4a,
	0x6b, 0x87, 0xb9, 0xfc, 0x92, 0x74, 0xfe, 0x38, 0x76, 0x69, 0x99, 0x3b, 0x7f, 0x1c, 0xdc, 0x58,
	0x46, 0xba, 0x69, 0xf7, 0x7c, 0xd3, 0xb7, 0xa2, 0x9c, 0x86, 0x43, 0x4e, 0x38, 0x00, 0xd7, 0x3c,
	0xee, 0x07, 0xa3, 0x81, 0x42, 0xd8, 0xb8, 0x8f, 0x83, 0xea, 0x7b, 0xb0, 0xb8, 0x47, 0x7d, 0x64,
	0xb1, 0x7b, 0x75, 0x7a, 0xba, 0xff, 0x84, 0x6f, 0x03, 0x81, 0xd9, 0xf1, 0x38, 0xb2, 0x39, 0xfc,
	0x5b, 0xfd, 0x5d, 0x09, 0x9b, 0x27, 0x13, 0x98, 0x0c, 0x9d, 0xba, 0x6d, 0x1a, 0x17, 0x09, 0xf7,
	0xc5, 0x01, 0xa8, 0xd0, 0xef, 0xc3, 0xe6, 0x25, 0x3b, 0xd2, 0x2a, 0x92, 0x8e, 0x34, 0x0c, 0xdd,
	0xb1, 0x1b, 0x51, 0x8d, 0x03, 0xc2, 0x41, 0xd1, 0x88, 0xca, 0x95, 0x21, 0x36, 0xb0, 0x86, 0x80,
	0x53, 0x73, 0x90, 0xbf, 0x7d, 0x43, 0x6c, 0x8e, 0xe2, 0x46, 0xcf, 0xb3, 0xfd, 0xd7, 0xf7, 0xf1,
	0xb9, 0xcf, 0xaf, 0xbb, 0x98, 0xfc, 0x4e, 0x18, 0x31, 0x97, 0xef, 0x1c, 0xde, 0x59, 0x14, 0xbc,
	0x7a, 0xe0, 0xdf, 0x9c, 0x3b, 0xde, 0x4c, 0xf0, 0x4e, 0x52, 0xc2, 0x81, 0x1a, 0x07, 0xf0, 0x2b,
	0x88, 0xfa, 0x04, 0x73, 0xa0, 0xe8, 0x16, 0xb0, 0x7b, 0x85, 0x4d, 0x1e, 0x4f, 0x72, 0xe3, 0x5e,
	0x5e, 0x6b, 0xf1, 0x9a, 0x9c, 0x8a, 0xe4, 0x1e, 0xa2, 0x14, 0xbe, 0x87, 0xa8, 0x7f, 0x0d, 0xcb,
	0x18, 0x9f, 0x34, 0xea, 0x52, 0xdd, 0xa7, 0xf8, 0x7e, 0xfb, 0x5d, 0xb6, 0xbd, 0xfc, 0x39, 0xac,
	0x48, 0xf8, 0xbf, 0xa9, 0xc2, 0xc8, 0x5f, 0xc0, 0xe2, 0xae, 0xee, 0x1f, 0x8b, 0x1d, 0xfc, 0xae,
	0x3b, 0x7a, 0x96, 0x81, 0xa4, 0x99, 0x33, 0x77, 0xeb, 0x53, 0x68, 0x26, 0x5a, 0x1c, 0xc9, 0x02,
	0x34, 0xc2, 0xc2, 0x9c, 0x69, 0x0f, 0xdb, 0x33, 0x04, 0xa0, 0x72, 0x68, 0x73, 0x4b, 0x6a, 0x2b,
	0xa4, 0x01, 0xd5, 0xc3, 0xb3, 0x33, 0xfc, 0x28, 0x6d, 0x75, 0xa1, 0x1e, 0x15, 0x4d, 0x49, 0x13,
	0xea, 0x07, 0x8e, 0x4d, 0x8f, 0xa9, 0xcd, 0x68, 0x7b, 0x86, 0x2c, 0x43, 0xfb, 0xd0, 0xb6, 0xae,
	0x0e, 0x1c, 0x1f, 0x87, 0x39, 0x5e, 0x5b, 0x21, 0x8b, 0xd0, 0xe4, 0xd0, 0x09, 0xa8, 0xb4, 0xf5,
	0x37, 0x0a, 0x40, 0x8c, 0x7b, 0x0b, 0x00, 0xc9, 0x20, 0xa4, 0x3d, 0xc3, 0xc9, 0x3e, 0x3d, 0x3b,
	0xa3, 0x06, 0x57, 0xa1, 0x20, 0xf0, 0xdc, 0x61, 0x74, 0x02, 0x2a, 0x71, 0x4e, 0x5f, 0xe9, 0xa6,
	0x6f, 0xda, 0xc3, 0x09, 0xb4, 0xcc, 0xe7, 0x9d, 0x78, 0xba, 0x69, 0x1f, 0x5e, 0x52, 0xaf, 0x3d,
	0x1b, 0x7d, 0x3e, 0xd3, 0x4d, 0xab, 0x3d, 0x47, 0x96, 0x60, 0x21, 0x5e, 0x80, 0xe4, 0xeb, 0xac,
	0x6c, 0xfd, 0x15, 0xd4, 0xbb, 0x51, 0x97, 0xca, 0x32, 0xb4, 0x4f, 0xed, 0x0b, 0xdb, 0x79, 0x61,
	0x47, 0xb0, 0xf6, 0x0c, 0x67, 0x1f, 0x76, 0x78, 0x20, 0xb8, 0xad, 0x90, 0x15, 0xf1, 0x80, 0x77,
	0x12, 0xbc, 0x61, 0x08, 0x30, 0x4a, 0x75, 0x40, 0x5f, 0x24, 0x91, 0xcb, 0xa4, 0x03, 0xb7, 0x32,
	0x4f, 0x5a, 0x62, 0x6c, 0x76, 0xeb, 0x29, 0xb4, 0xba, 0x8e, 0x7b, 0x75, 0xe8, 0x45, 0x95, 0xc0,
	0x55, 0x58, 0x0a, 0x65, 0x88, 0x15, 0x08, 0xdb, 0x33, 0x64, 0x1e, 0x6a, 0x1c, 0x82, 0x5f, 0x0a,
	0xff, 0x8a, 0xc6, 0x4a, 0x5b, 0x9f, 0xc2, 0x62, 0x26, 0xa3, 0xe2, 0x28, 0x4f, 0x7f, 0xe6, 0x3a,
	0x6c, 0xec, 0xf1, 0xe9, 0x0d, 0xa8, 0x06, 0x12, 0xb4, 0x15, 0x52, 0x87, 0xb9, 0x63, 0xee, 0xdf,
	0xda, 0xa5, 0xad, 0xc7, 0x50, 0xd9, 0xd9, 0x47, 0xce, 0x6d, 0x98, 0xdf, 0x31, 0x85, 0x2f, 0x0a,
	0x58, 0xae, 0xc3, 0xca, 0x8e, 0xf9, 0xcc, 0xf1, 0xa8, 0x39, 0xb4, 0x9f, 0xeb, 0xf6, 0x70, 0xac,
	0x0f, 0x05, 0x47, 0x65, 0xfb, 0x7f, 0xde, 0x86, 0x5a, 0x88, 0x4d, 0x06, 0xb0, 0x98, 0xe9, 0x34,
	0x26, 0xf7, 0xb3, 0x29, 0x86, 0xa4, 0xe9, 0xb8, 0xf3, 0x47, 0x45, 0xd0, 0x98, 0xab, 0xce, 0x70,
	0x2e, 0x7b, 0x05, 0xb8, 0xec, 0x15, 0xe3, 0xb2, 0x97, 0xcf, 0x25, 0xd3, 0x73, 0x2c, 0xe1, 0x22,
	0xeb, 0x60, 0x96, 0x70, 0x91, 0xb7, 0x2f, 0xcf, 0x90, 0x9f, 0x41, 0x33, 0xd1, 0x97, 0x4b, 0xb2,
	0x09, 0x59, 0xba, 0x01, 0xb8, 0xa3, 0x5e, 0x87, 0x82, 0x94, 0x0f, 0x01, 0x26, 0x2d, 0xbb, 0xe4,
	0x6d, 0xd9, 0xba, 0x63, 0x34, 0xdf, 0x99, 0x3a, 0x8e, 0x04, 0x7f, 0x01, 0x0b, 0xa9, 0x6e, 0x59,
	0x72, 0x2f, 0x33, 0x2b, 0xdb, 0xdd, 0xdb, 0x79, 0xf7, 0x7a, 0x24, 0xa4, 0xaf, 0x41, 0x23, 0xd6,
	0x73, 0x48, 0xb2, 0x12, 0x25, 0x7b, 0x21, 0x3b, 0x1b, 0xd3, 0x11, 0x42, 0x99, 0x53, 0xad, 0x78,
	0x12, 0x99, 0xb3, 0x0d, 0x89, 0x12, 0x99, 0x25, 0x1d, 0x7d, 0xea, 0x0c, 0xb9, 0xcc, 0xef, 0xb8,
	0x79, 0xbf, 0x70, 0x47, 0x01, 0xfd, 0xba, 0xf3, 0x41, 0x71, 0x64, 0xe4, 0xfb, 0x6b, 0x05, 0xde,
	0xb9, 0xe6, 0x75, 0x88, 0x3c, 0xca, 0xfb, 0x09, 0xc9, 0x94, 0xe7, 0xbc, 0xce, 0xc7, 0xaf, 0x3e,
	0x09, 0x05, 0x1a, 0x61, 0x2f, 0x7f, 0x56, 0x0b, 0x9b, 0xb2, 0x93, 0x20, 0x55, 0xc1, 0x1f, 0x17,
	0xc4, 0x44, 0x76, 0x3a, 0xb4, 0xd3, 0x6f, 0x30, 0x44, 0x6e, 0x67, 0xa9, 0xc7, 0xa4, 0xce, 0xfd,
	0x02, 0x58, 0xc8, 0xe2, 0x14, 0xe6, 0xe3, 0x29, 0x0e, 0xd9, 0x90, 0x69, 0x26, 0x61, 0x34, 0x77,
	0xaf, 0xc1, 0x40, 0xb2, 0x7f, 0xab, 0xc0, 0x9d, 0xa9, 0x9d, 0x0b, 0xe4, 0x23, 0xc9, 0x6f, 0xb1,
	0xa6, 0xf7, 0x59, 0x74, 0xb6, 0x5f, 0x75, 0x4a, 0x78, 0xe0, 0x62, 0xe5, 0x55, 0xc9, 0x81, 0x4b,
	0x16, 0x7e, 0x25, 0x07, 0x2e, 0x5d, 0x9d, 0x8d, 0xb4, 0x16, 0x95, 0x63, 0xe5, 0x5a, 0x8b, 0x15,
	0x27, 0x73, 0xb4, 0x96, 0xa8, 0x4d, 0x22, 0xd9, 0x78, 0xc5, 0x52, 0x42, 0x36, 0x55, 0xf3, 0x94,
	0x90, 0xcd, 0x94, 0x3c, 0xd1, 0x6a, 0x65, 0x35, 0x3e, 0x89, 0xd5, 0xe6, 0xd4, 0x0d, 0x25, 0x56,
	0x9b, 0x57, 0x34, 0x14, 0x56, 0x9b, 0xae, 0x16, 0x49, 0xac, 0x56, 0x52, 0x92, 0x92, 0x58, 0xad,
	0xb4, 0xec, 0x24, 0x58, 0xa4, 0x4a, 0x7a, 0x32, 0x16, 0xd9, 0x02, 0xa4, 0x8c, 0x85, 0xac, 0x36,
	0x18, 0xb1, 0x88, 0x57, 0xee, 0x72, 0x58, 0xa4, 0x0a, 0x88, 0x39, 0x2c, 0x32, 0x25, 0x40, 0xdc,
	0x17, 0x59, 0xd1, 0x4e, 0xb2, 0x2f, 0x39, 0xb5, 0x42, 0xc9, 0xbe, 0xe4, 0x56, 0x01, 0x45, 0x10,
	0x8e, 0xf7, 0xef, 0xca, 0x82, 0x70, 0xaa, 0x6b, 0x5a, 0x16, 0x84, 0xd3, 0x2d, 0xc0, 0xea, 0x0c,
	0xf9, 0x33, 0x68, 0x25, 0x1b, 0x57, 0x89, 0x7a, 0x6d, 0x67, 0xeb, 0xd7, 0x9d, 0x7b, 0x05, 0xba,
	0x5f, 0xd5, 0x19, 0xf2, 0x4b, 0x58, 0x92, 0x54, 0xf3, 0xc8, 0x7b, 0xf2, 0xd9, 0x99, 0x4a, 0x64,
	0x67, 0xb3, 0x18, 0x22, 0xf2, 0xfa, 0x39, 0x34, 0x62, 0x89, 0xa5, 0x24, 0x88, 0x66, 0xab, 0x6b,
	0x92, 0x20, 0x2a, 0x2b, 0x83, 0x61, 0x10, 0xcd, 0x79, 0x94, 0x96, 0x04, 0xd1, 0xfc, 0x17, 0x76,
	0x49, 0x10, 0x9d, 0xf2, 0xd6, 0x9d, 0x0c, 0x22, 0x51, 0xe9, 0x34, 0x3f, 0x88, 0xc4, 0x2a, 0x6a,
	0x53, 0x82, 0x48, 0xa2, 0xec, 0x15, 0xa6, 0xaa, 0xc9, 0xca, 0x8e, 0x3c, 0x55, 0xcd, 0xd4, 0xc6,
	0xe4, 0xa9, 0x6a, 0xb6, 0x48, 0x24, 0xac, 0x2c, 0x59, 0xa9, 0x91, 0x58, 0x59, 0xa6, 0xe8, 0x23,
	0xb1, 0xb2, 0x6c, 0xb9, 0x47, 0x44, 0x89, 0x58, 0xd1, 0x81, 0xbc, 0x93, 0x97, 0x20, 0x04, 0xb5,
	0x8f, 0xce, 0xc6, 0x74, 0x84, 0x98, 0xdf, 0xcd, 0x94, 0x0f, 0xe4, 0x7e, 0x57, 0x56, 0xab, 0x90,
	0xfb, 0x5d, 0x69, 0x3d, 0x42, 0xec, 0x42, 0xe6, 0x5a, 0x2f, 0xd9, 0x05, 0x59, 0xe9, 0x41, 0xb2,
	0x0b, 0xd2, 0x0a, 0x81, 0xd8, 0x85, 0xe4, 0x0d, 0x5b, 0xb2, 0x0b, 0x99, 0xfb, 0xbf, 0x64, 0x17,
	0xb2, 0xd7, 0x74, 0x75, 0x86, 0xfc, 0x56, 0x81, 0xf7, 0x0a, 0xfe, 0xcc, 0x95, 0xfc, 0xe9, 0x4d,
	0x7f, 0x20, 0xcb, 0xe5, 0xf9, 0xec, 0xe6, 0x93, 0x51, 0xd0, 0x6f, 0xa1, 0x93, 0xff, 0x33, 0x59,
	0xf2, 0x20, 0x2f, 0x5c, 0xca, 0x7f, 0x86, 0xdb, 0x79, 0xf8, 0x4a, 0xf8, 0x61, 0xca, 0x9f, 0xfa,
	0xa9, 0x34, 0xb9, 0x77, 0xdd, 0x8f, 0xa9, 0xe5, 0xde, 0x4a, 0xf2, 0x2b, 0x6d, 0x75, 0x86, 0xb8,
	0x58, 0xac, 0xcd, 0xfe, 0xaa, 0x9e, 0x48, 0x4d, 0x52, 0xfa, 0x7b, 0xfe, 0xce, 0x56, 0x51, 0x54,
	0xce, 0x71, 0xf7, 0xd1, 0xcf, 0x3f, 0x1a, 0x3a, 0x96, 0x6e, 0x0f, 0x1f, 0x3c, 0xde, 0xf6, 0xfd,
	0x07, 0x86, 0x33, 0x7a, 0x88, 0xff, 0x43, 0xc0, 0x70, 0xac, 0x87, 0x8c, 0x7a, 0x97, 0xa6, 0x41,
	0xd9, 0xc3, 0xe8, 0xdf, 0x0c, 0x08, 0x82, 0xfd, 0x0a, 0xa2, 0x3c, 0xfa, 0x7d, 0x00, 0x00, 0x00,
	0xff, 0xff, 0x00, 0xde, 0x55, 0x27, 0x80, 0x40, 0x00, 0x00,
}
