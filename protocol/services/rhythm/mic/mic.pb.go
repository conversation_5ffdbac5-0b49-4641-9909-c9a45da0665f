// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rhythm/mic.proto

package mic // import "golang.52tt.com/protocol/services/rhythm/mic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type SwitchMicSortReq struct {
	SwitchFlag           bool     `protobuf:"varint,1,opt,name=switch_flag,json=switchFlag,proto3" json:"switch_flag,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SwitchMicSortReq) Reset()         { *m = SwitchMicSortReq{} }
func (m *SwitchMicSortReq) String() string { return proto.CompactTextString(m) }
func (*SwitchMicSortReq) ProtoMessage()    {}
func (*SwitchMicSortReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mic_a18ee59520ce80e1, []int{0}
}
func (m *SwitchMicSortReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchMicSortReq.Unmarshal(m, b)
}
func (m *SwitchMicSortReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchMicSortReq.Marshal(b, m, deterministic)
}
func (dst *SwitchMicSortReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchMicSortReq.Merge(dst, src)
}
func (m *SwitchMicSortReq) XXX_Size() int {
	return xxx_messageInfo_SwitchMicSortReq.Size(m)
}
func (m *SwitchMicSortReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchMicSortReq.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchMicSortReq proto.InternalMessageInfo

func (m *SwitchMicSortReq) GetSwitchFlag() bool {
	if m != nil {
		return m.SwitchFlag
	}
	return false
}

func (m *SwitchMicSortReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type SwitchMicSortResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SwitchMicSortResp) Reset()         { *m = SwitchMicSortResp{} }
func (m *SwitchMicSortResp) String() string { return proto.CompactTextString(m) }
func (*SwitchMicSortResp) ProtoMessage()    {}
func (*SwitchMicSortResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mic_a18ee59520ce80e1, []int{1}
}
func (m *SwitchMicSortResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchMicSortResp.Unmarshal(m, b)
}
func (m *SwitchMicSortResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchMicSortResp.Marshal(b, m, deterministic)
}
func (dst *SwitchMicSortResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchMicSortResp.Merge(dst, src)
}
func (m *SwitchMicSortResp) XXX_Size() int {
	return xxx_messageInfo_SwitchMicSortResp.Size(m)
}
func (m *SwitchMicSortResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchMicSortResp.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchMicSortResp proto.InternalMessageInfo

type MicUserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Account              string   `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	Sex                  uint32   `protobuf:"varint,4,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MicUserInfo) Reset()         { *m = MicUserInfo{} }
func (m *MicUserInfo) String() string { return proto.CompactTextString(m) }
func (*MicUserInfo) ProtoMessage()    {}
func (*MicUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_mic_a18ee59520ce80e1, []int{2}
}
func (m *MicUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MicUserInfo.Unmarshal(m, b)
}
func (m *MicUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MicUserInfo.Marshal(b, m, deterministic)
}
func (dst *MicUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MicUserInfo.Merge(dst, src)
}
func (m *MicUserInfo) XXX_Size() int {
	return xxx_messageInfo_MicUserInfo.Size(m)
}
func (m *MicUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MicUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MicUserInfo proto.InternalMessageInfo

func (m *MicUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MicUserInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *MicUserInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *MicUserInfo) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

type MicSortInfo struct {
	MicFlag              bool           `protobuf:"varint,1,opt,name=mic_flag,json=micFlag,proto3" json:"mic_flag,omitempty"`
	UserInfo             []*MicUserInfo `protobuf:"bytes,2,rep,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *MicSortInfo) Reset()         { *m = MicSortInfo{} }
func (m *MicSortInfo) String() string { return proto.CompactTextString(m) }
func (*MicSortInfo) ProtoMessage()    {}
func (*MicSortInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_mic_a18ee59520ce80e1, []int{3}
}
func (m *MicSortInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MicSortInfo.Unmarshal(m, b)
}
func (m *MicSortInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MicSortInfo.Marshal(b, m, deterministic)
}
func (dst *MicSortInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MicSortInfo.Merge(dst, src)
}
func (m *MicSortInfo) XXX_Size() int {
	return xxx_messageInfo_MicSortInfo.Size(m)
}
func (m *MicSortInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MicSortInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MicSortInfo proto.InternalMessageInfo

func (m *MicSortInfo) GetMicFlag() bool {
	if m != nil {
		return m.MicFlag
	}
	return false
}

func (m *MicSortInfo) GetUserInfo() []*MicUserInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

type SetMicSortReq struct {
	ChannelId            uint32       `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Info                 *MicSortInfo `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	Uid                  uint32       `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetMicSortReq) Reset()         { *m = SetMicSortReq{} }
func (m *SetMicSortReq) String() string { return proto.CompactTextString(m) }
func (*SetMicSortReq) ProtoMessage()    {}
func (*SetMicSortReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mic_a18ee59520ce80e1, []int{4}
}
func (m *SetMicSortReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMicSortReq.Unmarshal(m, b)
}
func (m *SetMicSortReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMicSortReq.Marshal(b, m, deterministic)
}
func (dst *SetMicSortReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMicSortReq.Merge(dst, src)
}
func (m *SetMicSortReq) XXX_Size() int {
	return xxx_messageInfo_SetMicSortReq.Size(m)
}
func (m *SetMicSortReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMicSortReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetMicSortReq proto.InternalMessageInfo

func (m *SetMicSortReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetMicSortReq) GetInfo() *MicSortInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *SetMicSortReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type SetMicSortResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetMicSortResp) Reset()         { *m = SetMicSortResp{} }
func (m *SetMicSortResp) String() string { return proto.CompactTextString(m) }
func (*SetMicSortResp) ProtoMessage()    {}
func (*SetMicSortResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mic_a18ee59520ce80e1, []int{5}
}
func (m *SetMicSortResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMicSortResp.Unmarshal(m, b)
}
func (m *SetMicSortResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMicSortResp.Marshal(b, m, deterministic)
}
func (dst *SetMicSortResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMicSortResp.Merge(dst, src)
}
func (m *SetMicSortResp) XXX_Size() int {
	return xxx_messageInfo_SetMicSortResp.Size(m)
}
func (m *SetMicSortResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMicSortResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetMicSortResp proto.InternalMessageInfo

type GetMicSortReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMicSortReq) Reset()         { *m = GetMicSortReq{} }
func (m *GetMicSortReq) String() string { return proto.CompactTextString(m) }
func (*GetMicSortReq) ProtoMessage()    {}
func (*GetMicSortReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mic_a18ee59520ce80e1, []int{6}
}
func (m *GetMicSortReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMicSortReq.Unmarshal(m, b)
}
func (m *GetMicSortReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMicSortReq.Marshal(b, m, deterministic)
}
func (dst *GetMicSortReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMicSortReq.Merge(dst, src)
}
func (m *GetMicSortReq) XXX_Size() int {
	return xxx_messageInfo_GetMicSortReq.Size(m)
}
func (m *GetMicSortReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMicSortReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMicSortReq proto.InternalMessageInfo

func (m *GetMicSortReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetMicSortResp struct {
	Info                 *MicSortInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMicSortResp) Reset()         { *m = GetMicSortResp{} }
func (m *GetMicSortResp) String() string { return proto.CompactTextString(m) }
func (*GetMicSortResp) ProtoMessage()    {}
func (*GetMicSortResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mic_a18ee59520ce80e1, []int{7}
}
func (m *GetMicSortResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMicSortResp.Unmarshal(m, b)
}
func (m *GetMicSortResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMicSortResp.Marshal(b, m, deterministic)
}
func (dst *GetMicSortResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMicSortResp.Merge(dst, src)
}
func (m *GetMicSortResp) XXX_Size() int {
	return xxx_messageInfo_GetMicSortResp.Size(m)
}
func (m *GetMicSortResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMicSortResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMicSortResp proto.InternalMessageInfo

func (m *GetMicSortResp) GetInfo() *MicSortInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func init() {
	proto.RegisterType((*SwitchMicSortReq)(nil), "rhythm.mic.SwitchMicSortReq")
	proto.RegisterType((*SwitchMicSortResp)(nil), "rhythm.mic.SwitchMicSortResp")
	proto.RegisterType((*MicUserInfo)(nil), "rhythm.mic.MicUserInfo")
	proto.RegisterType((*MicSortInfo)(nil), "rhythm.mic.MicSortInfo")
	proto.RegisterType((*SetMicSortReq)(nil), "rhythm.mic.SetMicSortReq")
	proto.RegisterType((*SetMicSortResp)(nil), "rhythm.mic.SetMicSortResp")
	proto.RegisterType((*GetMicSortReq)(nil), "rhythm.mic.GetMicSortReq")
	proto.RegisterType((*GetMicSortResp)(nil), "rhythm.mic.GetMicSortResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MicClient is the client API for Mic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MicClient interface {
	// 设置麦位排序
	SetMicSort(ctx context.Context, in *SetMicSortReq, opts ...grpc.CallOption) (*SetMicSortResp, error)
	// 主动获取麦序
	GetMicSort(ctx context.Context, in *GetMicSortReq, opts ...grpc.CallOption) (*GetMicSortResp, error)
	// 开启或关闭麦位排序
	SwitchMicSort(ctx context.Context, in *SwitchMicSortReq, opts ...grpc.CallOption) (*SwitchMicSortResp, error)
}

type micClient struct {
	cc *grpc.ClientConn
}

func NewMicClient(cc *grpc.ClientConn) MicClient {
	return &micClient{cc}
}

func (c *micClient) SetMicSort(ctx context.Context, in *SetMicSortReq, opts ...grpc.CallOption) (*SetMicSortResp, error) {
	out := new(SetMicSortResp)
	err := c.cc.Invoke(ctx, "/rhythm.mic.Mic/SetMicSort", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *micClient) GetMicSort(ctx context.Context, in *GetMicSortReq, opts ...grpc.CallOption) (*GetMicSortResp, error) {
	out := new(GetMicSortResp)
	err := c.cc.Invoke(ctx, "/rhythm.mic.Mic/GetMicSort", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *micClient) SwitchMicSort(ctx context.Context, in *SwitchMicSortReq, opts ...grpc.CallOption) (*SwitchMicSortResp, error) {
	out := new(SwitchMicSortResp)
	err := c.cc.Invoke(ctx, "/rhythm.mic.Mic/SwitchMicSort", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MicServer is the server API for Mic service.
type MicServer interface {
	// 设置麦位排序
	SetMicSort(context.Context, *SetMicSortReq) (*SetMicSortResp, error)
	// 主动获取麦序
	GetMicSort(context.Context, *GetMicSortReq) (*GetMicSortResp, error)
	// 开启或关闭麦位排序
	SwitchMicSort(context.Context, *SwitchMicSortReq) (*SwitchMicSortResp, error)
}

func RegisterMicServer(s *grpc.Server, srv MicServer) {
	s.RegisterService(&_Mic_serviceDesc, srv)
}

func _Mic_SetMicSort_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetMicSortReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MicServer).SetMicSort(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.mic.Mic/SetMicSort",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MicServer).SetMicSort(ctx, req.(*SetMicSortReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Mic_GetMicSort_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMicSortReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MicServer).GetMicSort(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.mic.Mic/GetMicSort",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MicServer).GetMicSort(ctx, req.(*GetMicSortReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Mic_SwitchMicSort_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SwitchMicSortReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MicServer).SwitchMicSort(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.mic.Mic/SwitchMicSort",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MicServer).SwitchMicSort(ctx, req.(*SwitchMicSortReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Mic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "rhythm.mic.Mic",
	HandlerType: (*MicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetMicSort",
			Handler:    _Mic_SetMicSort_Handler,
		},
		{
			MethodName: "GetMicSort",
			Handler:    _Mic_GetMicSort_Handler,
		},
		{
			MethodName: "SwitchMicSort",
			Handler:    _Mic_SwitchMicSort_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rhythm/mic.proto",
}

func init() { proto.RegisterFile("rhythm/mic.proto", fileDescriptor_mic_a18ee59520ce80e1) }

var fileDescriptor_mic_a18ee59520ce80e1 = []byte{
	// 401 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x93, 0xcf, 0x8f, 0x94, 0x30,
	0x1c, 0xc5, 0x65, 0xd9, 0xb8, 0xcc, 0x77, 0xc2, 0x06, 0xeb, 0x41, 0x96, 0xb8, 0x71, 0xd2, 0xd3,
	0x24, 0x9a, 0x92, 0x8c, 0x7a, 0xf4, 0x62, 0xa2, 0x64, 0x0f, 0xe3, 0xa1, 0x13, 0x2f, 0x1e, 0x9c,
	0x60, 0xa7, 0x03, 0x8d, 0xb4, 0x45, 0x5a, 0x74, 0xfd, 0x7b, 0xfd, 0x47, 0x0c, 0x9d, 0x5f, 0x40,
	0x1c, 0xa3, 0xb7, 0xf6, 0xfb, 0xe0, 0xf3, 0x5e, 0x5f, 0x5a, 0x88, 0x9a, 0xf2, 0xa7, 0x2d, 0x65,
	0x2a, 0x05, 0x23, 0x75, 0xa3, 0xad, 0x46, 0xb0, 0x9b, 0x10, 0x29, 0x18, 0xa6, 0x10, 0xad, 0x7e,
	0x08, 0xcb, 0xca, 0xa5, 0x60, 0x2b, 0xdd, 0x58, 0xca, 0xbf, 0xa1, 0x67, 0x30, 0x35, 0x6e, 0xb6,
	0xde, 0x56, 0x79, 0x11, 0x7b, 0x33, 0x6f, 0x1e, 0x50, 0xd8, 0x8d, 0xde, 0x57, 0x79, 0x81, 0x6e,
	0x01, 0x58, 0x99, 0x2b, 0xc5, 0xab, 0xb5, 0xd8, 0xc4, 0x17, 0x33, 0x6f, 0x1e, 0xd2, 0xc9, 0x7e,
	0x72, 0xb7, 0xc1, 0x8f, 0xe1, 0xd1, 0x88, 0x69, 0x6a, 0x5c, 0xc0, 0x74, 0x29, 0xd8, 0x47, 0xc3,
	0x9b, 0x3b, 0xb5, 0xd5, 0x28, 0x02, 0xbf, 0x15, 0x1b, 0xc7, 0x0e, 0x69, 0xb7, 0x44, 0x09, 0x04,
	0x4a, 0xb0, 0xaf, 0x2a, 0x97, 0xdc, 0x21, 0x27, 0xf4, 0xb8, 0x47, 0x31, 0x5c, 0xe5, 0x8c, 0xe9,
	0x56, 0xd9, 0xd8, 0x77, 0xd2, 0x61, 0xdb, 0x71, 0x0c, 0xbf, 0x8f, 0x2f, 0x77, 0x1c, 0xc3, 0xef,
	0xf1, 0x67, 0x67, 0xd4, 0xf9, 0x3a, 0xa3, 0x1b, 0x08, 0xa4, 0x60, 0xfd, 0x93, 0x5c, 0x49, 0xc1,
	0xdc, 0x31, 0x5e, 0xc1, 0xa4, 0x35, 0xbc, 0x59, 0x0b, 0xb5, 0xd5, 0xf1, 0xc5, 0xcc, 0x9f, 0x4f,
	0x17, 0x4f, 0xc8, 0xa9, 0x1b, 0xd2, 0xcb, 0x4b, 0x83, 0x76, 0xbf, 0xc2, 0x12, 0xc2, 0x15, 0xb7,
	0xbd, 0xba, 0x86, 0x6d, 0x78, 0xa3, 0x36, 0xd0, 0x73, 0xb8, 0xdc, 0x1b, 0x78, 0x7f, 0x30, 0x38,
	0xe4, 0xa4, 0xee, 0xa3, 0x43, 0x2d, 0xfe, 0xb1, 0x16, 0x1c, 0xc1, 0x75, 0xdf, 0xce, 0xd4, 0x98,
	0x40, 0x98, 0xfd, 0x47, 0x00, 0xfc, 0x06, 0xae, 0xb3, 0x01, 0xe1, 0x18, 0xc9, 0xfb, 0x87, 0x48,
	0x8b, 0x5f, 0x1e, 0xf8, 0x4b, 0xc1, 0xd0, 0x3b, 0x80, 0x53, 0x10, 0x74, 0xd3, 0xff, 0x69, 0xd0,
	0x47, 0x92, 0x9c, 0x93, 0x4c, 0x8d, 0x1f, 0x74, 0x98, 0xec, 0x0c, 0x26, 0x3b, 0x8f, 0xc9, 0xc6,
	0x98, 0x0f, 0x10, 0x0e, 0xee, 0x18, 0x7a, 0x3a, 0x70, 0x1d, 0x5d, 0xe9, 0xe4, 0xf6, 0x2f, 0x6a,
	0xc7, 0x7b, 0x4b, 0x3e, 0xbd, 0x28, 0x74, 0x95, 0xab, 0x82, 0xbc, 0x5e, 0x58, 0x4b, 0x98, 0x96,
	0xa9, 0x7b, 0x2c, 0x4c, 0x57, 0xa9, 0xe1, 0xcd, 0x77, 0xc1, 0xb8, 0x49, 0x4f, 0x2f, 0xe9, 0xcb,
	0x43, 0xa7, 0xbe, 0xfc, 0x1d, 0x00, 0x00, 0xff, 0xff, 0x11, 0x41, 0xb3, 0x71, 0x5e, 0x03, 0x00,
	0x00,
}
