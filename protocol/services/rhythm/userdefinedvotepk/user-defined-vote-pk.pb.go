// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rhythm/user-defined-vote-pk.proto

package userdefinedvotepk // import "golang.52tt.com/protocol/services/rhythm/userdefinedvotepk"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type UserDefinedVotePKType int32

const (
	UserDefinedVotePKType_UnknownType      UserDefinedVotePKType = 0
	UserDefinedVotePKType_StringOptionType UserDefinedVotePKType = 100
)

var UserDefinedVotePKType_name = map[int32]string{
	0:   "UnknownType",
	100: "StringOptionType",
}
var UserDefinedVotePKType_value = map[string]int32{
	"UnknownType":      0,
	"StringOptionType": 100,
}

func (x UserDefinedVotePKType) String() string {
	return proto.EnumName(UserDefinedVotePKType_name, int32(x))
}
func (UserDefinedVotePKType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_defined_vote_pk_a24d0017ba517be1, []int{0}
}

// ------------------ 自定义投票 ------------------
type UserDefinedVotePkOptionInfo struct {
	OptionName           string   `protobuf:"bytes,1,opt,name=option_name,json=optionName,proto3" json:"option_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserDefinedVotePkOptionInfo) Reset()         { *m = UserDefinedVotePkOptionInfo{} }
func (m *UserDefinedVotePkOptionInfo) String() string { return proto.CompactTextString(m) }
func (*UserDefinedVotePkOptionInfo) ProtoMessage()    {}
func (*UserDefinedVotePkOptionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_defined_vote_pk_a24d0017ba517be1, []int{0}
}
func (m *UserDefinedVotePkOptionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDefinedVotePkOptionInfo.Unmarshal(m, b)
}
func (m *UserDefinedVotePkOptionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDefinedVotePkOptionInfo.Marshal(b, m, deterministic)
}
func (dst *UserDefinedVotePkOptionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDefinedVotePkOptionInfo.Merge(dst, src)
}
func (m *UserDefinedVotePkOptionInfo) XXX_Size() int {
	return xxx_messageInfo_UserDefinedVotePkOptionInfo.Size(m)
}
func (m *UserDefinedVotePkOptionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDefinedVotePkOptionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserDefinedVotePkOptionInfo proto.InternalMessageInfo

func (m *UserDefinedVotePkOptionInfo) GetOptionName() string {
	if m != nil {
		return m.OptionName
	}
	return ""
}

type UserDefinedVotePKStartReq struct {
	Uid                  uint32                         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32                         `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	DurationMin          uint32                         `protobuf:"varint,3,opt,name=duration_min,json=durationMin,proto3" json:"duration_min,omitempty"`
	PkType               uint32                         `protobuf:"varint,4,opt,name=pk_type,json=pkType,proto3" json:"pk_type,omitempty"`
	OptionList           []*UserDefinedVotePkOptionInfo `protobuf:"bytes,5,rep,name=option_list,json=optionList,proto3" json:"option_list,omitempty"`
	VoteCnt              uint32                         `protobuf:"varint,6,opt,name=vote_cnt,json=voteCnt,proto3" json:"vote_cnt,omitempty"`
	PkName               string                         `protobuf:"bytes,7,opt,name=pk_name,json=pkName,proto3" json:"pk_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *UserDefinedVotePKStartReq) Reset()         { *m = UserDefinedVotePKStartReq{} }
func (m *UserDefinedVotePKStartReq) String() string { return proto.CompactTextString(m) }
func (*UserDefinedVotePKStartReq) ProtoMessage()    {}
func (*UserDefinedVotePKStartReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_defined_vote_pk_a24d0017ba517be1, []int{1}
}
func (m *UserDefinedVotePKStartReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDefinedVotePKStartReq.Unmarshal(m, b)
}
func (m *UserDefinedVotePKStartReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDefinedVotePKStartReq.Marshal(b, m, deterministic)
}
func (dst *UserDefinedVotePKStartReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDefinedVotePKStartReq.Merge(dst, src)
}
func (m *UserDefinedVotePKStartReq) XXX_Size() int {
	return xxx_messageInfo_UserDefinedVotePKStartReq.Size(m)
}
func (m *UserDefinedVotePKStartReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDefinedVotePKStartReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserDefinedVotePKStartReq proto.InternalMessageInfo

func (m *UserDefinedVotePKStartReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserDefinedVotePKStartReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UserDefinedVotePKStartReq) GetDurationMin() uint32 {
	if m != nil {
		return m.DurationMin
	}
	return 0
}

func (m *UserDefinedVotePKStartReq) GetPkType() uint32 {
	if m != nil {
		return m.PkType
	}
	return 0
}

func (m *UserDefinedVotePKStartReq) GetOptionList() []*UserDefinedVotePkOptionInfo {
	if m != nil {
		return m.OptionList
	}
	return nil
}

func (m *UserDefinedVotePKStartReq) GetVoteCnt() uint32 {
	if m != nil {
		return m.VoteCnt
	}
	return 0
}

func (m *UserDefinedVotePKStartReq) GetPkName() string {
	if m != nil {
		return m.PkName
	}
	return ""
}

type UserDefinedVotePKStartResp struct {
	ChannelId            uint32                `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	StartTimestamp       uint32                `protobuf:"varint,2,opt,name=start_timestamp,json=startTimestamp,proto3" json:"start_timestamp,omitempty"`
	OptionList           []*UserDefinedOptInfo `protobuf:"bytes,3,rep,name=option_list,json=optionList,proto3" json:"option_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *UserDefinedVotePKStartResp) Reset()         { *m = UserDefinedVotePKStartResp{} }
func (m *UserDefinedVotePKStartResp) String() string { return proto.CompactTextString(m) }
func (*UserDefinedVotePKStartResp) ProtoMessage()    {}
func (*UserDefinedVotePKStartResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_defined_vote_pk_a24d0017ba517be1, []int{2}
}
func (m *UserDefinedVotePKStartResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDefinedVotePKStartResp.Unmarshal(m, b)
}
func (m *UserDefinedVotePKStartResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDefinedVotePKStartResp.Marshal(b, m, deterministic)
}
func (dst *UserDefinedVotePKStartResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDefinedVotePKStartResp.Merge(dst, src)
}
func (m *UserDefinedVotePKStartResp) XXX_Size() int {
	return xxx_messageInfo_UserDefinedVotePKStartResp.Size(m)
}
func (m *UserDefinedVotePKStartResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDefinedVotePKStartResp.DiscardUnknown(m)
}

var xxx_messageInfo_UserDefinedVotePKStartResp proto.InternalMessageInfo

func (m *UserDefinedVotePKStartResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UserDefinedVotePKStartResp) GetStartTimestamp() uint32 {
	if m != nil {
		return m.StartTimestamp
	}
	return 0
}

func (m *UserDefinedVotePKStartResp) GetOptionList() []*UserDefinedOptInfo {
	if m != nil {
		return m.OptionList
	}
	return nil
}

type UserDefinedCompetitor struct {
	OptInfo              *UserDefinedOptInfo `protobuf:"bytes,1,opt,name=opt_info,json=optInfo,proto3" json:"opt_info,omitempty"`
	Vote                 uint32              `protobuf:"varint,2,opt,name=vote,proto3" json:"vote,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *UserDefinedCompetitor) Reset()         { *m = UserDefinedCompetitor{} }
func (m *UserDefinedCompetitor) String() string { return proto.CompactTextString(m) }
func (*UserDefinedCompetitor) ProtoMessage()    {}
func (*UserDefinedCompetitor) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_defined_vote_pk_a24d0017ba517be1, []int{3}
}
func (m *UserDefinedCompetitor) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDefinedCompetitor.Unmarshal(m, b)
}
func (m *UserDefinedCompetitor) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDefinedCompetitor.Marshal(b, m, deterministic)
}
func (dst *UserDefinedCompetitor) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDefinedCompetitor.Merge(dst, src)
}
func (m *UserDefinedCompetitor) XXX_Size() int {
	return xxx_messageInfo_UserDefinedCompetitor.Size(m)
}
func (m *UserDefinedCompetitor) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDefinedCompetitor.DiscardUnknown(m)
}

var xxx_messageInfo_UserDefinedCompetitor proto.InternalMessageInfo

func (m *UserDefinedCompetitor) GetOptInfo() *UserDefinedOptInfo {
	if m != nil {
		return m.OptInfo
	}
	return nil
}

func (m *UserDefinedCompetitor) GetVote() uint32 {
	if m != nil {
		return m.Vote
	}
	return 0
}

type UserDefinedOptInfo struct {
	OptId                uint32                       `protobuf:"varint,1,opt,name=opt_id,json=optId,proto3" json:"opt_id,omitempty"`
	OptInfo              *UserDefinedVotePkOptionInfo `protobuf:"bytes,2,opt,name=opt_info,json=optInfo,proto3" json:"opt_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *UserDefinedOptInfo) Reset()         { *m = UserDefinedOptInfo{} }
func (m *UserDefinedOptInfo) String() string { return proto.CompactTextString(m) }
func (*UserDefinedOptInfo) ProtoMessage()    {}
func (*UserDefinedOptInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_defined_vote_pk_a24d0017ba517be1, []int{4}
}
func (m *UserDefinedOptInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDefinedOptInfo.Unmarshal(m, b)
}
func (m *UserDefinedOptInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDefinedOptInfo.Marshal(b, m, deterministic)
}
func (dst *UserDefinedOptInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDefinedOptInfo.Merge(dst, src)
}
func (m *UserDefinedOptInfo) XXX_Size() int {
	return xxx_messageInfo_UserDefinedOptInfo.Size(m)
}
func (m *UserDefinedOptInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDefinedOptInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserDefinedOptInfo proto.InternalMessageInfo

func (m *UserDefinedOptInfo) GetOptId() uint32 {
	if m != nil {
		return m.OptId
	}
	return 0
}

func (m *UserDefinedOptInfo) GetOptInfo() *UserDefinedVotePkOptionInfo {
	if m != nil {
		return m.OptInfo
	}
	return nil
}

type UserDefinedVotePKInfo struct {
	Uid                  uint32                `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32                `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	DurationMin          uint32                `protobuf:"varint,3,opt,name=duration_min,json=durationMin,proto3" json:"duration_min,omitempty"`
	StartTimestamp       uint32                `protobuf:"varint,4,opt,name=start_timestamp,json=startTimestamp,proto3" json:"start_timestamp,omitempty"`
	VoteCnt              uint32                `protobuf:"varint,5,opt,name=vote_cnt,json=voteCnt,proto3" json:"vote_cnt,omitempty"`
	PkName               string                `protobuf:"bytes,6,opt,name=pk_name,json=pkName,proto3" json:"pk_name,omitempty"`
	OptionList           []*UserDefinedOptInfo `protobuf:"bytes,7,rep,name=option_list,json=optionList,proto3" json:"option_list,omitempty"`
	PkType               uint32                `protobuf:"varint,8,opt,name=pk_type,json=pkType,proto3" json:"pk_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *UserDefinedVotePKInfo) Reset()         { *m = UserDefinedVotePKInfo{} }
func (m *UserDefinedVotePKInfo) String() string { return proto.CompactTextString(m) }
func (*UserDefinedVotePKInfo) ProtoMessage()    {}
func (*UserDefinedVotePKInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_defined_vote_pk_a24d0017ba517be1, []int{5}
}
func (m *UserDefinedVotePKInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDefinedVotePKInfo.Unmarshal(m, b)
}
func (m *UserDefinedVotePKInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDefinedVotePKInfo.Marshal(b, m, deterministic)
}
func (dst *UserDefinedVotePKInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDefinedVotePKInfo.Merge(dst, src)
}
func (m *UserDefinedVotePKInfo) XXX_Size() int {
	return xxx_messageInfo_UserDefinedVotePKInfo.Size(m)
}
func (m *UserDefinedVotePKInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDefinedVotePKInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserDefinedVotePKInfo proto.InternalMessageInfo

func (m *UserDefinedVotePKInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserDefinedVotePKInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UserDefinedVotePKInfo) GetDurationMin() uint32 {
	if m != nil {
		return m.DurationMin
	}
	return 0
}

func (m *UserDefinedVotePKInfo) GetStartTimestamp() uint32 {
	if m != nil {
		return m.StartTimestamp
	}
	return 0
}

func (m *UserDefinedVotePKInfo) GetVoteCnt() uint32 {
	if m != nil {
		return m.VoteCnt
	}
	return 0
}

func (m *UserDefinedVotePKInfo) GetPkName() string {
	if m != nil {
		return m.PkName
	}
	return ""
}

func (m *UserDefinedVotePKInfo) GetOptionList() []*UserDefinedOptInfo {
	if m != nil {
		return m.OptionList
	}
	return nil
}

func (m *UserDefinedVotePKInfo) GetPkType() uint32 {
	if m != nil {
		return m.PkType
	}
	return 0
}

type UserDefinedPKRankInfo struct {
	Info                 *UserDefinedVotePKInfo   `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	CompetitorList       []*UserDefinedCompetitor `protobuf:"bytes,2,rep,name=competitor_list,json=competitorList,proto3" json:"competitor_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *UserDefinedPKRankInfo) Reset()         { *m = UserDefinedPKRankInfo{} }
func (m *UserDefinedPKRankInfo) String() string { return proto.CompactTextString(m) }
func (*UserDefinedPKRankInfo) ProtoMessage()    {}
func (*UserDefinedPKRankInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_defined_vote_pk_a24d0017ba517be1, []int{6}
}
func (m *UserDefinedPKRankInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDefinedPKRankInfo.Unmarshal(m, b)
}
func (m *UserDefinedPKRankInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDefinedPKRankInfo.Marshal(b, m, deterministic)
}
func (dst *UserDefinedPKRankInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDefinedPKRankInfo.Merge(dst, src)
}
func (m *UserDefinedPKRankInfo) XXX_Size() int {
	return xxx_messageInfo_UserDefinedPKRankInfo.Size(m)
}
func (m *UserDefinedPKRankInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDefinedPKRankInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserDefinedPKRankInfo proto.InternalMessageInfo

func (m *UserDefinedPKRankInfo) GetInfo() *UserDefinedVotePKInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *UserDefinedPKRankInfo) GetCompetitorList() []*UserDefinedCompetitor {
	if m != nil {
		return m.CompetitorList
	}
	return nil
}

type GetUserDefinedVotePKReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserDefinedVotePKReq) Reset()         { *m = GetUserDefinedVotePKReq{} }
func (m *GetUserDefinedVotePKReq) String() string { return proto.CompactTextString(m) }
func (*GetUserDefinedVotePKReq) ProtoMessage()    {}
func (*GetUserDefinedVotePKReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_defined_vote_pk_a24d0017ba517be1, []int{7}
}
func (m *GetUserDefinedVotePKReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserDefinedVotePKReq.Unmarshal(m, b)
}
func (m *GetUserDefinedVotePKReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserDefinedVotePKReq.Marshal(b, m, deterministic)
}
func (dst *GetUserDefinedVotePKReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserDefinedVotePKReq.Merge(dst, src)
}
func (m *GetUserDefinedVotePKReq) XXX_Size() int {
	return xxx_messageInfo_GetUserDefinedVotePKReq.Size(m)
}
func (m *GetUserDefinedVotePKReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserDefinedVotePKReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserDefinedVotePKReq proto.InternalMessageInfo

func (m *GetUserDefinedVotePKReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserDefinedVotePKReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetUserDefinedVotePKResp struct {
	LeftVote             uint32                 `protobuf:"varint,1,opt,name=left_vote,json=leftVote,proto3" json:"left_vote,omitempty"`
	PkInfo               *UserDefinedPKRankInfo `protobuf:"bytes,2,opt,name=pk_info,json=pkInfo,proto3" json:"pk_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetUserDefinedVotePKResp) Reset()         { *m = GetUserDefinedVotePKResp{} }
func (m *GetUserDefinedVotePKResp) String() string { return proto.CompactTextString(m) }
func (*GetUserDefinedVotePKResp) ProtoMessage()    {}
func (*GetUserDefinedVotePKResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_defined_vote_pk_a24d0017ba517be1, []int{8}
}
func (m *GetUserDefinedVotePKResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserDefinedVotePKResp.Unmarshal(m, b)
}
func (m *GetUserDefinedVotePKResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserDefinedVotePKResp.Marshal(b, m, deterministic)
}
func (dst *GetUserDefinedVotePKResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserDefinedVotePKResp.Merge(dst, src)
}
func (m *GetUserDefinedVotePKResp) XXX_Size() int {
	return xxx_messageInfo_GetUserDefinedVotePKResp.Size(m)
}
func (m *GetUserDefinedVotePKResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserDefinedVotePKResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserDefinedVotePKResp proto.InternalMessageInfo

func (m *GetUserDefinedVotePKResp) GetLeftVote() uint32 {
	if m != nil {
		return m.LeftVote
	}
	return 0
}

func (m *GetUserDefinedVotePKResp) GetPkInfo() *UserDefinedPKRankInfo {
	if m != nil {
		return m.PkInfo
	}
	return nil
}

type UserDefinedVoteReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OptId                uint32   `protobuf:"varint,2,opt,name=opt_id,json=optId,proto3" json:"opt_id,omitempty"`
	VoteCnt              uint32   `protobuf:"varint,3,opt,name=vote_cnt,json=voteCnt,proto3" json:"vote_cnt,omitempty"`
	ChannelId            uint32   `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	StartTimestamp       uint32   `protobuf:"varint,5,opt,name=start_timestamp,json=startTimestamp,proto3" json:"start_timestamp,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserDefinedVoteReq) Reset()         { *m = UserDefinedVoteReq{} }
func (m *UserDefinedVoteReq) String() string { return proto.CompactTextString(m) }
func (*UserDefinedVoteReq) ProtoMessage()    {}
func (*UserDefinedVoteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_defined_vote_pk_a24d0017ba517be1, []int{9}
}
func (m *UserDefinedVoteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDefinedVoteReq.Unmarshal(m, b)
}
func (m *UserDefinedVoteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDefinedVoteReq.Marshal(b, m, deterministic)
}
func (dst *UserDefinedVoteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDefinedVoteReq.Merge(dst, src)
}
func (m *UserDefinedVoteReq) XXX_Size() int {
	return xxx_messageInfo_UserDefinedVoteReq.Size(m)
}
func (m *UserDefinedVoteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDefinedVoteReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserDefinedVoteReq proto.InternalMessageInfo

func (m *UserDefinedVoteReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserDefinedVoteReq) GetOptId() uint32 {
	if m != nil {
		return m.OptId
	}
	return 0
}

func (m *UserDefinedVoteReq) GetVoteCnt() uint32 {
	if m != nil {
		return m.VoteCnt
	}
	return 0
}

func (m *UserDefinedVoteReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UserDefinedVoteReq) GetStartTimestamp() uint32 {
	if m != nil {
		return m.StartTimestamp
	}
	return 0
}

type UserDefinedVoteResp struct {
	LeftVote             uint32   `protobuf:"varint,1,opt,name=left_vote,json=leftVote,proto3" json:"left_vote,omitempty"`
	RemainExtraVote      uint32   `protobuf:"varint,2,opt,name=remain_extra_vote,json=remainExtraVote,proto3" json:"remain_extra_vote,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserDefinedVoteResp) Reset()         { *m = UserDefinedVoteResp{} }
func (m *UserDefinedVoteResp) String() string { return proto.CompactTextString(m) }
func (*UserDefinedVoteResp) ProtoMessage()    {}
func (*UserDefinedVoteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_defined_vote_pk_a24d0017ba517be1, []int{10}
}
func (m *UserDefinedVoteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDefinedVoteResp.Unmarshal(m, b)
}
func (m *UserDefinedVoteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDefinedVoteResp.Marshal(b, m, deterministic)
}
func (dst *UserDefinedVoteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDefinedVoteResp.Merge(dst, src)
}
func (m *UserDefinedVoteResp) XXX_Size() int {
	return xxx_messageInfo_UserDefinedVoteResp.Size(m)
}
func (m *UserDefinedVoteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDefinedVoteResp.DiscardUnknown(m)
}

var xxx_messageInfo_UserDefinedVoteResp proto.InternalMessageInfo

func (m *UserDefinedVoteResp) GetLeftVote() uint32 {
	if m != nil {
		return m.LeftVote
	}
	return 0
}

func (m *UserDefinedVoteResp) GetRemainExtraVote() uint32 {
	if m != nil {
		return m.RemainExtraVote
	}
	return 0
}

type UserDefinedVotePKCancelReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	StartTimestamp       uint32   `protobuf:"varint,3,opt,name=start_timestamp,json=startTimestamp,proto3" json:"start_timestamp,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserDefinedVotePKCancelReq) Reset()         { *m = UserDefinedVotePKCancelReq{} }
func (m *UserDefinedVotePKCancelReq) String() string { return proto.CompactTextString(m) }
func (*UserDefinedVotePKCancelReq) ProtoMessage()    {}
func (*UserDefinedVotePKCancelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_defined_vote_pk_a24d0017ba517be1, []int{11}
}
func (m *UserDefinedVotePKCancelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDefinedVotePKCancelReq.Unmarshal(m, b)
}
func (m *UserDefinedVotePKCancelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDefinedVotePKCancelReq.Marshal(b, m, deterministic)
}
func (dst *UserDefinedVotePKCancelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDefinedVotePKCancelReq.Merge(dst, src)
}
func (m *UserDefinedVotePKCancelReq) XXX_Size() int {
	return xxx_messageInfo_UserDefinedVotePKCancelReq.Size(m)
}
func (m *UserDefinedVotePKCancelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDefinedVotePKCancelReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserDefinedVotePKCancelReq proto.InternalMessageInfo

func (m *UserDefinedVotePKCancelReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserDefinedVotePKCancelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UserDefinedVotePKCancelReq) GetStartTimestamp() uint32 {
	if m != nil {
		return m.StartTimestamp
	}
	return 0
}

type UserDefinedVotePKCancelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserDefinedVotePKCancelResp) Reset()         { *m = UserDefinedVotePKCancelResp{} }
func (m *UserDefinedVotePKCancelResp) String() string { return proto.CompactTextString(m) }
func (*UserDefinedVotePKCancelResp) ProtoMessage()    {}
func (*UserDefinedVotePKCancelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_defined_vote_pk_a24d0017ba517be1, []int{12}
}
func (m *UserDefinedVotePKCancelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDefinedVotePKCancelResp.Unmarshal(m, b)
}
func (m *UserDefinedVotePKCancelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDefinedVotePKCancelResp.Marshal(b, m, deterministic)
}
func (dst *UserDefinedVotePKCancelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDefinedVotePKCancelResp.Merge(dst, src)
}
func (m *UserDefinedVotePKCancelResp) XXX_Size() int {
	return xxx_messageInfo_UserDefinedVotePKCancelResp.Size(m)
}
func (m *UserDefinedVotePKCancelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDefinedVotePKCancelResp.DiscardUnknown(m)
}

var xxx_messageInfo_UserDefinedVotePKCancelResp proto.InternalMessageInfo

type AddVotePkUserTicketReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	StartTimestamp       uint32   `protobuf:"varint,2,opt,name=start_timestamp,json=startTimestamp,proto3" json:"start_timestamp,omitempty"`
	UidList              []uint32 `protobuf:"varint,3,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	Cnt                  uint32   `protobuf:"varint,4,opt,name=cnt,proto3" json:"cnt,omitempty"`
	PushVersion          uint32   `protobuf:"varint,5,opt,name=push_version,json=pushVersion,proto3" json:"push_version,omitempty"`
	UserAddTicketCnt     uint32   `protobuf:"varint,6,opt,name=user_add_ticket_cnt,json=userAddTicketCnt,proto3" json:"user_add_ticket_cnt,omitempty"`
	UserRemainTicketCnt  uint32   `protobuf:"varint,7,opt,name=user_remain_ticket_cnt,json=userRemainTicketCnt,proto3" json:"user_remain_ticket_cnt,omitempty"`
	IsEndAct             bool     `protobuf:"varint,8,opt,name=is_end_act,json=isEndAct,proto3" json:"is_end_act,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddVotePkUserTicketReq) Reset()         { *m = AddVotePkUserTicketReq{} }
func (m *AddVotePkUserTicketReq) String() string { return proto.CompactTextString(m) }
func (*AddVotePkUserTicketReq) ProtoMessage()    {}
func (*AddVotePkUserTicketReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_defined_vote_pk_a24d0017ba517be1, []int{13}
}
func (m *AddVotePkUserTicketReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddVotePkUserTicketReq.Unmarshal(m, b)
}
func (m *AddVotePkUserTicketReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddVotePkUserTicketReq.Marshal(b, m, deterministic)
}
func (dst *AddVotePkUserTicketReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddVotePkUserTicketReq.Merge(dst, src)
}
func (m *AddVotePkUserTicketReq) XXX_Size() int {
	return xxx_messageInfo_AddVotePkUserTicketReq.Size(m)
}
func (m *AddVotePkUserTicketReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddVotePkUserTicketReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddVotePkUserTicketReq proto.InternalMessageInfo

func (m *AddVotePkUserTicketReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AddVotePkUserTicketReq) GetStartTimestamp() uint32 {
	if m != nil {
		return m.StartTimestamp
	}
	return 0
}

func (m *AddVotePkUserTicketReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *AddVotePkUserTicketReq) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

func (m *AddVotePkUserTicketReq) GetPushVersion() uint32 {
	if m != nil {
		return m.PushVersion
	}
	return 0
}

func (m *AddVotePkUserTicketReq) GetUserAddTicketCnt() uint32 {
	if m != nil {
		return m.UserAddTicketCnt
	}
	return 0
}

func (m *AddVotePkUserTicketReq) GetUserRemainTicketCnt() uint32 {
	if m != nil {
		return m.UserRemainTicketCnt
	}
	return 0
}

func (m *AddVotePkUserTicketReq) GetIsEndAct() bool {
	if m != nil {
		return m.IsEndAct
	}
	return false
}

type AddVotePkUserTicketResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddVotePkUserTicketResp) Reset()         { *m = AddVotePkUserTicketResp{} }
func (m *AddVotePkUserTicketResp) String() string { return proto.CompactTextString(m) }
func (*AddVotePkUserTicketResp) ProtoMessage()    {}
func (*AddVotePkUserTicketResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_defined_vote_pk_a24d0017ba517be1, []int{14}
}
func (m *AddVotePkUserTicketResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddVotePkUserTicketResp.Unmarshal(m, b)
}
func (m *AddVotePkUserTicketResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddVotePkUserTicketResp.Marshal(b, m, deterministic)
}
func (dst *AddVotePkUserTicketResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddVotePkUserTicketResp.Merge(dst, src)
}
func (m *AddVotePkUserTicketResp) XXX_Size() int {
	return xxx_messageInfo_AddVotePkUserTicketResp.Size(m)
}
func (m *AddVotePkUserTicketResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddVotePkUserTicketResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddVotePkUserTicketResp proto.InternalMessageInfo

type HasUserDefinedVotePKReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HasUserDefinedVotePKReq) Reset()         { *m = HasUserDefinedVotePKReq{} }
func (m *HasUserDefinedVotePKReq) String() string { return proto.CompactTextString(m) }
func (*HasUserDefinedVotePKReq) ProtoMessage()    {}
func (*HasUserDefinedVotePKReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_defined_vote_pk_a24d0017ba517be1, []int{15}
}
func (m *HasUserDefinedVotePKReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HasUserDefinedVotePKReq.Unmarshal(m, b)
}
func (m *HasUserDefinedVotePKReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HasUserDefinedVotePKReq.Marshal(b, m, deterministic)
}
func (dst *HasUserDefinedVotePKReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HasUserDefinedVotePKReq.Merge(dst, src)
}
func (m *HasUserDefinedVotePKReq) XXX_Size() int {
	return xxx_messageInfo_HasUserDefinedVotePKReq.Size(m)
}
func (m *HasUserDefinedVotePKReq) XXX_DiscardUnknown() {
	xxx_messageInfo_HasUserDefinedVotePKReq.DiscardUnknown(m)
}

var xxx_messageInfo_HasUserDefinedVotePKReq proto.InternalMessageInfo

func (m *HasUserDefinedVotePKReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type HasUserDefinedVotePKResp struct {
	HasPk                bool     `protobuf:"varint,1,opt,name=has_pk,json=hasPk,proto3" json:"has_pk,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HasUserDefinedVotePKResp) Reset()         { *m = HasUserDefinedVotePKResp{} }
func (m *HasUserDefinedVotePKResp) String() string { return proto.CompactTextString(m) }
func (*HasUserDefinedVotePKResp) ProtoMessage()    {}
func (*HasUserDefinedVotePKResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_defined_vote_pk_a24d0017ba517be1, []int{16}
}
func (m *HasUserDefinedVotePKResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HasUserDefinedVotePKResp.Unmarshal(m, b)
}
func (m *HasUserDefinedVotePKResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HasUserDefinedVotePKResp.Marshal(b, m, deterministic)
}
func (dst *HasUserDefinedVotePKResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HasUserDefinedVotePKResp.Merge(dst, src)
}
func (m *HasUserDefinedVotePKResp) XXX_Size() int {
	return xxx_messageInfo_HasUserDefinedVotePKResp.Size(m)
}
func (m *HasUserDefinedVotePKResp) XXX_DiscardUnknown() {
	xxx_messageInfo_HasUserDefinedVotePKResp.DiscardUnknown(m)
}

var xxx_messageInfo_HasUserDefinedVotePKResp proto.InternalMessageInfo

func (m *HasUserDefinedVotePKResp) GetHasPk() bool {
	if m != nil {
		return m.HasPk
	}
	return false
}

func init() {
	proto.RegisterType((*UserDefinedVotePkOptionInfo)(nil), "userdefinedvotepk.UserDefinedVotePkOptionInfo")
	proto.RegisterType((*UserDefinedVotePKStartReq)(nil), "userdefinedvotepk.UserDefinedVotePKStartReq")
	proto.RegisterType((*UserDefinedVotePKStartResp)(nil), "userdefinedvotepk.UserDefinedVotePKStartResp")
	proto.RegisterType((*UserDefinedCompetitor)(nil), "userdefinedvotepk.UserDefinedCompetitor")
	proto.RegisterType((*UserDefinedOptInfo)(nil), "userdefinedvotepk.UserDefinedOptInfo")
	proto.RegisterType((*UserDefinedVotePKInfo)(nil), "userdefinedvotepk.UserDefinedVotePKInfo")
	proto.RegisterType((*UserDefinedPKRankInfo)(nil), "userdefinedvotepk.UserDefinedPKRankInfo")
	proto.RegisterType((*GetUserDefinedVotePKReq)(nil), "userdefinedvotepk.GetUserDefinedVotePKReq")
	proto.RegisterType((*GetUserDefinedVotePKResp)(nil), "userdefinedvotepk.GetUserDefinedVotePKResp")
	proto.RegisterType((*UserDefinedVoteReq)(nil), "userdefinedvotepk.UserDefinedVoteReq")
	proto.RegisterType((*UserDefinedVoteResp)(nil), "userdefinedvotepk.UserDefinedVoteResp")
	proto.RegisterType((*UserDefinedVotePKCancelReq)(nil), "userdefinedvotepk.UserDefinedVotePKCancelReq")
	proto.RegisterType((*UserDefinedVotePKCancelResp)(nil), "userdefinedvotepk.UserDefinedVotePKCancelResp")
	proto.RegisterType((*AddVotePkUserTicketReq)(nil), "userdefinedvotepk.AddVotePkUserTicketReq")
	proto.RegisterType((*AddVotePkUserTicketResp)(nil), "userdefinedvotepk.AddVotePkUserTicketResp")
	proto.RegisterType((*HasUserDefinedVotePKReq)(nil), "userdefinedvotepk.HasUserDefinedVotePKReq")
	proto.RegisterType((*HasUserDefinedVotePKResp)(nil), "userdefinedvotepk.HasUserDefinedVotePKResp")
	proto.RegisterEnum("userdefinedvotepk.UserDefinedVotePKType", UserDefinedVotePKType_name, UserDefinedVotePKType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// UserDefinedVotePKClient is the client API for UserDefinedVotePK service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UserDefinedVotePKClient interface {
	// 开始pk
	UserDefinedVotePKStart(ctx context.Context, in *UserDefinedVotePKStartReq, opts ...grpc.CallOption) (*UserDefinedVotePKStartResp, error)
	// 进房时获取pk信息
	GetUserDefinedVotePK(ctx context.Context, in *GetUserDefinedVotePKReq, opts ...grpc.CallOption) (*GetUserDefinedVotePKResp, error)
	// 用户投票
	UserDefinedVote(ctx context.Context, in *UserDefinedVoteReq, opts ...grpc.CallOption) (*UserDefinedVoteResp, error)
	// 取消pk
	UserDefinedVotePKCancel(ctx context.Context, in *UserDefinedVotePKCancelReq, opts ...grpc.CallOption) (*UserDefinedVotePKCancelResp, error)
	AddVotePkUserTicket(ctx context.Context, in *AddVotePkUserTicketReq, opts ...grpc.CallOption) (*AddVotePkUserTicketResp, error)
	HasUserDefinedVotePK(ctx context.Context, in *HasUserDefinedVotePKReq, opts ...grpc.CallOption) (*HasUserDefinedVotePKResp, error)
}

type userDefinedVotePKClient struct {
	cc *grpc.ClientConn
}

func NewUserDefinedVotePKClient(cc *grpc.ClientConn) UserDefinedVotePKClient {
	return &userDefinedVotePKClient{cc}
}

func (c *userDefinedVotePKClient) UserDefinedVotePKStart(ctx context.Context, in *UserDefinedVotePKStartReq, opts ...grpc.CallOption) (*UserDefinedVotePKStartResp, error) {
	out := new(UserDefinedVotePKStartResp)
	err := c.cc.Invoke(ctx, "/userdefinedvotepk.UserDefinedVotePK/UserDefinedVotePKStart", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userDefinedVotePKClient) GetUserDefinedVotePK(ctx context.Context, in *GetUserDefinedVotePKReq, opts ...grpc.CallOption) (*GetUserDefinedVotePKResp, error) {
	out := new(GetUserDefinedVotePKResp)
	err := c.cc.Invoke(ctx, "/userdefinedvotepk.UserDefinedVotePK/GetUserDefinedVotePK", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userDefinedVotePKClient) UserDefinedVote(ctx context.Context, in *UserDefinedVoteReq, opts ...grpc.CallOption) (*UserDefinedVoteResp, error) {
	out := new(UserDefinedVoteResp)
	err := c.cc.Invoke(ctx, "/userdefinedvotepk.UserDefinedVotePK/UserDefinedVote", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userDefinedVotePKClient) UserDefinedVotePKCancel(ctx context.Context, in *UserDefinedVotePKCancelReq, opts ...grpc.CallOption) (*UserDefinedVotePKCancelResp, error) {
	out := new(UserDefinedVotePKCancelResp)
	err := c.cc.Invoke(ctx, "/userdefinedvotepk.UserDefinedVotePK/UserDefinedVotePKCancel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userDefinedVotePKClient) AddVotePkUserTicket(ctx context.Context, in *AddVotePkUserTicketReq, opts ...grpc.CallOption) (*AddVotePkUserTicketResp, error) {
	out := new(AddVotePkUserTicketResp)
	err := c.cc.Invoke(ctx, "/userdefinedvotepk.UserDefinedVotePK/AddVotePkUserTicket", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userDefinedVotePKClient) HasUserDefinedVotePK(ctx context.Context, in *HasUserDefinedVotePKReq, opts ...grpc.CallOption) (*HasUserDefinedVotePKResp, error) {
	out := new(HasUserDefinedVotePKResp)
	err := c.cc.Invoke(ctx, "/userdefinedvotepk.UserDefinedVotePK/HasUserDefinedVotePK", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserDefinedVotePKServer is the server API for UserDefinedVotePK service.
type UserDefinedVotePKServer interface {
	// 开始pk
	UserDefinedVotePKStart(context.Context, *UserDefinedVotePKStartReq) (*UserDefinedVotePKStartResp, error)
	// 进房时获取pk信息
	GetUserDefinedVotePK(context.Context, *GetUserDefinedVotePKReq) (*GetUserDefinedVotePKResp, error)
	// 用户投票
	UserDefinedVote(context.Context, *UserDefinedVoteReq) (*UserDefinedVoteResp, error)
	// 取消pk
	UserDefinedVotePKCancel(context.Context, *UserDefinedVotePKCancelReq) (*UserDefinedVotePKCancelResp, error)
	AddVotePkUserTicket(context.Context, *AddVotePkUserTicketReq) (*AddVotePkUserTicketResp, error)
	HasUserDefinedVotePK(context.Context, *HasUserDefinedVotePKReq) (*HasUserDefinedVotePKResp, error)
}

func RegisterUserDefinedVotePKServer(s *grpc.Server, srv UserDefinedVotePKServer) {
	s.RegisterService(&_UserDefinedVotePK_serviceDesc, srv)
}

func _UserDefinedVotePK_UserDefinedVotePKStart_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserDefinedVotePKStartReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserDefinedVotePKServer).UserDefinedVotePKStart(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userdefinedvotepk.UserDefinedVotePK/UserDefinedVotePKStart",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserDefinedVotePKServer).UserDefinedVotePKStart(ctx, req.(*UserDefinedVotePKStartReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserDefinedVotePK_GetUserDefinedVotePK_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserDefinedVotePKReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserDefinedVotePKServer).GetUserDefinedVotePK(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userdefinedvotepk.UserDefinedVotePK/GetUserDefinedVotePK",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserDefinedVotePKServer).GetUserDefinedVotePK(ctx, req.(*GetUserDefinedVotePKReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserDefinedVotePK_UserDefinedVote_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserDefinedVoteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserDefinedVotePKServer).UserDefinedVote(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userdefinedvotepk.UserDefinedVotePK/UserDefinedVote",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserDefinedVotePKServer).UserDefinedVote(ctx, req.(*UserDefinedVoteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserDefinedVotePK_UserDefinedVotePKCancel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserDefinedVotePKCancelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserDefinedVotePKServer).UserDefinedVotePKCancel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userdefinedvotepk.UserDefinedVotePK/UserDefinedVotePKCancel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserDefinedVotePKServer).UserDefinedVotePKCancel(ctx, req.(*UserDefinedVotePKCancelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserDefinedVotePK_AddVotePkUserTicket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddVotePkUserTicketReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserDefinedVotePKServer).AddVotePkUserTicket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userdefinedvotepk.UserDefinedVotePK/AddVotePkUserTicket",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserDefinedVotePKServer).AddVotePkUserTicket(ctx, req.(*AddVotePkUserTicketReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserDefinedVotePK_HasUserDefinedVotePK_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HasUserDefinedVotePKReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserDefinedVotePKServer).HasUserDefinedVotePK(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userdefinedvotepk.UserDefinedVotePK/HasUserDefinedVotePK",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserDefinedVotePKServer).HasUserDefinedVotePK(ctx, req.(*HasUserDefinedVotePKReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _UserDefinedVotePK_serviceDesc = grpc.ServiceDesc{
	ServiceName: "userdefinedvotepk.UserDefinedVotePK",
	HandlerType: (*UserDefinedVotePKServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UserDefinedVotePKStart",
			Handler:    _UserDefinedVotePK_UserDefinedVotePKStart_Handler,
		},
		{
			MethodName: "GetUserDefinedVotePK",
			Handler:    _UserDefinedVotePK_GetUserDefinedVotePK_Handler,
		},
		{
			MethodName: "UserDefinedVote",
			Handler:    _UserDefinedVotePK_UserDefinedVote_Handler,
		},
		{
			MethodName: "UserDefinedVotePKCancel",
			Handler:    _UserDefinedVotePK_UserDefinedVotePKCancel_Handler,
		},
		{
			MethodName: "AddVotePkUserTicket",
			Handler:    _UserDefinedVotePK_AddVotePkUserTicket_Handler,
		},
		{
			MethodName: "HasUserDefinedVotePK",
			Handler:    _UserDefinedVotePK_HasUserDefinedVotePK_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rhythm/user-defined-vote-pk.proto",
}

func init() {
	proto.RegisterFile("rhythm/user-defined-vote-pk.proto", fileDescriptor_user_defined_vote_pk_a24d0017ba517be1)
}

var fileDescriptor_user_defined_vote_pk_a24d0017ba517be1 = []byte{
	// 963 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x57, 0x5f, 0x6f, 0x1b, 0x45,
	0x10, 0xc7, 0xff, 0x9d, 0x31, 0xad, 0x9d, 0x4d, 0x1b, 0x3b, 0x2e, 0x15, 0xed, 0x49, 0x85, 0x10,
	0xb0, 0x23, 0x52, 0x21, 0x21, 0x54, 0x55, 0x84, 0x50, 0x20, 0x14, 0x48, 0xb8, 0xa6, 0x79, 0xe0,
	0x81, 0xe3, 0xb8, 0xdb, 0xc4, 0xab, 0xf3, 0xed, 0x2e, 0xb7, 0xeb, 0xd0, 0xf0, 0x1d, 0xf8, 0x0e,
	0x3c, 0x82, 0xc4, 0x13, 0x0f, 0x7c, 0x3e, 0xb4, 0x73, 0x76, 0x7c, 0xb6, 0xd7, 0xc9, 0x25, 0xa2,
	0x6f, 0x7b, 0xf3, 0x6f, 0xe7, 0x37, 0xf3, 0x9b, 0x59, 0x1b, 0x1e, 0x26, 0x83, 0x73, 0x3d, 0x88,
	0xb7, 0x47, 0x8a, 0x26, 0xbd, 0x90, 0x9e, 0x30, 0x4e, 0xc3, 0xde, 0x99, 0xd0, 0xb4, 0x27, 0xa3,
	0xbe, 0x4c, 0x84, 0x16, 0x64, 0xd5, 0xe8, 0xc6, 0x2a, 0xa3, 0x91, 0x91, 0xf3, 0x14, 0xee, 0xbd,
	0x54, 0x34, 0xf9, 0x3c, 0x15, 0x1e, 0x0b, 0x4d, 0x0f, 0xa3, 0x03, 0xa9, 0x99, 0xe0, 0xfb, 0xfc,
	0x44, 0x90, 0xb7, 0xa1, 0x21, 0xf0, 0xcb, 0xe3, 0x7e, 0x4c, 0x3b, 0x85, 0x07, 0x85, 0xcd, 0x15,
	0x17, 0x52, 0xd1, 0x77, 0x7e, 0x4c, 0x9d, 0xdf, 0x8b, 0xb0, 0x31, 0x1f, 0xe0, 0xf9, 0x0b, 0xed,
	0x27, 0xda, 0xa5, 0xbf, 0x90, 0x16, 0x94, 0x46, 0x2c, 0x44, 0xb7, 0x5b, 0xae, 0x39, 0x92, 0xfb,
	0x00, 0xc1, 0xc0, 0xe7, 0x9c, 0x0e, 0x3d, 0x16, 0x76, 0x8a, 0xa8, 0x58, 0x19, 0x4b, 0xf6, 0x43,
	0xf2, 0x10, 0xde, 0x0c, 0x47, 0x89, 0x8f, 0x37, 0xc6, 0x8c, 0x77, 0x4a, 0x68, 0xd0, 0x98, 0xc8,
	0xbe, 0x65, 0x9c, 0xb4, 0xa1, 0x26, 0x23, 0x4f, 0x9f, 0x4b, 0xda, 0x29, 0xa3, 0xb6, 0x2a, 0xa3,
	0xa3, 0x73, 0x49, 0xc9, 0xc1, 0x45, 0xae, 0x43, 0xa6, 0x74, 0xa7, 0xf2, 0xa0, 0xb4, 0xd9, 0xd8,
	0xe9, 0xf7, 0x17, 0x30, 0xf7, 0x2f, 0x01, 0x3c, 0xc1, 0xf6, 0x0d, 0x53, 0x9a, 0x6c, 0x40, 0xdd,
	0x78, 0x78, 0x01, 0xd7, 0x9d, 0x2a, 0x5e, 0x55, 0x33, 0xdf, 0x7b, 0x5c, 0x8f, 0x93, 0xc0, 0x9a,
	0xd4, 0xb0, 0x26, 0x55, 0x19, 0x61, 0x3d, 0xfe, 0x2e, 0x40, 0x77, 0x59, 0x3d, 0x94, 0x9c, 0x83,
	0x5f, 0x98, 0x87, 0xff, 0x2e, 0x34, 0x95, 0xb1, 0xf5, 0x34, 0x8b, 0xa9, 0xd2, 0x7e, 0x2c, 0xc7,
	0x25, 0xba, 0x8d, 0xe2, 0xa3, 0x89, 0x94, 0x7c, 0x31, 0x8b, 0xb5, 0x84, 0x58, 0x1f, 0x5d, 0x8e,
	0xf5, 0x40, 0xea, 0x79, 0x88, 0x4e, 0x0c, 0x77, 0x33, 0x16, 0x7b, 0x22, 0x96, 0x54, 0x33, 0x2d,
	0x12, 0xf2, 0x29, 0xd4, 0x85, 0xd4, 0x1e, 0xe3, 0x27, 0x02, 0xd3, 0xcc, 0x1d, 0xbd, 0x26, 0xd2,
	0x03, 0x21, 0x50, 0x36, 0x56, 0x63, 0x00, 0x78, 0x76, 0xce, 0x80, 0x2c, 0xba, 0x90, 0xbb, 0x50,
	0xc5, 0xbb, 0x26, 0x05, 0xa9, 0x98, 0x10, 0x21, 0xd9, 0xcf, 0xa4, 0x50, 0xc4, 0x14, 0xae, 0xdb,
	0xcc, 0x49, 0x2e, 0xce, 0x5f, 0xc5, 0x19, 0x9c, 0x69, 0x57, 0xf0, 0xee, 0xd7, 0xc1, 0x50, 0x4b,
	0x17, 0xcb, 0xd6, 0x2e, 0x66, 0x09, 0x56, 0x59, 0x4a, 0xb0, 0x6a, 0x96, 0x60, 0xf3, 0x9d, 0xaf,
	0xdd, 0xb0, 0xf3, 0xd9, 0x31, 0xaa, 0x67, 0xc7, 0xc8, 0xf9, 0xb3, 0x30, 0x53, 0xab, 0xc3, 0xe7,
	0xae, 0xcf, 0x23, 0xac, 0xd5, 0x13, 0x28, 0x67, 0xf8, 0xb0, 0x99, 0xa3, 0x19, 0x58, 0x63, 0x17,
	0xbd, 0xc8, 0xf7, 0xd0, 0x0c, 0x2e, 0xf8, 0x95, 0x26, 0x5f, 0xc4, 0xe4, 0xaf, 0x08, 0x34, 0x25,
	0xa5, 0x7b, 0x7b, 0x1a, 0x00, 0xd9, 0xfb, 0x35, 0xb4, 0xbf, 0xa4, 0x7a, 0xe1, 0xd2, 0x9b, 0x6c,
	0x1e, 0xe7, 0x37, 0xe8, 0xd8, 0x63, 0x29, 0x49, 0xee, 0xc1, 0xca, 0x90, 0x9e, 0x68, 0x0f, 0xf9,
	0x9c, 0x86, 0xac, 0x1b, 0x81, 0x31, 0x21, 0xbb, 0x58, 0xc8, 0x0c, 0x4b, 0xaf, 0xc0, 0x33, 0x2d,
	0xa8, 0x29, 0x39, 0xd2, 0xf3, 0x8f, 0xc2, 0xcc, 0x5c, 0x98, 0xb0, 0x76, 0x0c, 0xd3, 0x49, 0x29,
	0x66, 0x27, 0x25, 0xcb, 0xa3, 0xd2, 0x2c, 0x8f, 0x66, 0x51, 0x97, 0x73, 0x2c, 0x9c, 0x8a, 0x8d,
	0xaa, 0xce, 0x8f, 0xb0, 0xb6, 0x90, 0xe1, 0x55, 0x95, 0xd9, 0x82, 0xd5, 0x84, 0xc6, 0x3e, 0xe3,
	0x1e, 0x7d, 0xa5, 0x13, 0xdf, 0xcb, 0xac, 0x83, 0x66, 0xaa, 0x78, 0x66, 0xe4, 0xc7, 0xe9, 0x66,
	0x58, 0x5c, 0x9b, 0x7b, 0x3e, 0x0f, 0xe8, 0xf0, 0x46, 0xef, 0x88, 0x05, 0x57, 0xc9, 0x8a, 0xeb,
	0xfe, 0xe2, 0xfb, 0x77, 0x71, 0xaf, 0x92, 0xce, 0x3f, 0x45, 0x58, 0xdf, 0x0d, 0xc7, 0x9b, 0xc5,
	0x18, 0x1e, 0xb1, 0x20, 0xa2, 0xf8, 0xb6, 0xfd, 0x5f, 0xab, 0x7c, 0x03, 0xea, 0x23, 0x16, 0x4e,
	0xf7, 0xf8, 0x2d, 0xb7, 0x36, 0x62, 0x21, 0xce, 0x68, 0x0b, 0x4a, 0xa6, 0xa5, 0x69, 0xd7, 0xcc,
	0xd1, 0x6c, 0x1f, 0x39, 0x52, 0x03, 0xef, 0x8c, 0x26, 0x8a, 0x09, 0x3e, 0x6e, 0x56, 0xc3, 0xc8,
	0x8e, 0x53, 0x11, 0xe9, 0xc1, 0x9a, 0xe1, 0x9f, 0xe7, 0x87, 0xa1, 0xa7, 0x31, 0xdb, 0xcc, 0x03,
	0xd6, 0x32, 0xaa, 0xdd, 0x30, 0x4c, 0x61, 0x18, 0x82, 0x3c, 0x86, 0x75, 0x34, 0x1f, 0x77, 0x2a,
	0xe3, 0x51, 0x43, 0x0f, 0x0c, 0xe6, 0xa2, 0x72, 0xea, 0xf4, 0x16, 0x00, 0x53, 0x1e, 0xe5, 0xa1,
	0xe7, 0x07, 0x1a, 0xf7, 0x47, 0xdd, 0xad, 0x33, 0xf5, 0x8c, 0x87, 0xbb, 0x81, 0x76, 0x36, 0xa0,
	0x6d, 0xad, 0x99, 0x92, 0xce, 0xc7, 0xd0, 0xfe, 0xca, 0x57, 0xd6, 0x89, 0xbd, 0xbc, 0x9e, 0xce,
	0x87, 0xd0, 0xb1, 0x7b, 0x2a, 0x69, 0xc6, 0x62, 0xe0, 0x2b, 0x4f, 0x46, 0xe8, 0x56, 0x77, 0x2b,
	0x03, 0x5f, 0x1d, 0x46, 0x5b, 0x4f, 0x2d, 0x4b, 0x1f, 0x7f, 0x29, 0x34, 0xa1, 0xf1, 0x92, 0x47,
	0x5c, 0xfc, 0xca, 0xcd, 0x67, 0xeb, 0x0d, 0x72, 0x07, 0x5a, 0x2f, 0x74, 0xc2, 0xf8, 0x69, 0xfa,
	0x78, 0xa0, 0x34, 0xdc, 0xf9, 0xb7, 0x02, 0xab, 0x0b, 0x01, 0xc8, 0x08, 0xd6, 0xed, 0x0f, 0x3c,
	0xf9, 0x20, 0xcf, 0x46, 0x9c, 0xfc, 0x36, 0xea, 0xf6, 0xae, 0x61, 0xad, 0x24, 0x11, 0x70, 0xc7,
	0xb6, 0x9f, 0xc8, 0x96, 0x25, 0xcc, 0x92, 0xa5, 0xd8, 0x7d, 0x3f, 0xb7, 0xad, 0x92, 0xe4, 0x27,
	0x68, 0xce, 0x29, 0xc8, 0xa3, 0xab, 0x53, 0x36, 0xd7, 0xbc, 0x93, 0xc7, 0x4c, 0x49, 0xf2, 0x0a,
	0xda, 0x4b, 0x66, 0x8f, 0xe4, 0x2a, 0xce, 0xc5, 0x7e, 0xe8, 0xf6, 0xaf, 0x63, 0xae, 0x24, 0x19,
	0xc2, 0x9a, 0x85, 0xa1, 0xe4, 0x3d, 0x4b, 0x18, 0xfb, 0xf4, 0x77, 0xb7, 0xf2, 0x9a, 0xa6, 0xad,
	0xb3, 0x51, 0xd7, 0xda, 0xba, 0x25, 0xd3, 0x61, 0x6d, 0xdd, 0xb2, 0x79, 0xf8, 0xec, 0xc9, 0x0f,
	0x9f, 0x9c, 0x8a, 0xa1, 0xcf, 0x4f, 0xfb, 0x1f, 0xed, 0x68, 0xdd, 0x0f, 0x44, 0xbc, 0x8d, 0x7f,
	0x00, 0x02, 0x31, 0xdc, 0x56, 0x34, 0x39, 0x63, 0x01, 0x55, 0xdb, 0x99, 0xbf, 0x0b, 0x33, 0x61,
	0x7f, 0xae, 0xa2, 0xed, 0xe3, 0xff, 0x02, 0x00, 0x00, 0xff, 0xff, 0x8d, 0x34, 0xa6, 0x95, 0x51,
	0x0c, 0x00, 0x00,
}
