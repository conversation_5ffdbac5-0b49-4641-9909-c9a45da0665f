// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rhythm/brand.proto

package brand // import "golang.52tt.com/protocol/services/rhythm/brand"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type BrandMemberRole int32

const (
	BrandMemberRole_UnknownRole BrandMemberRole = 0
	BrandMemberRole_TeamLeader  BrandMemberRole = 1
	BrandMemberRole_Normal      BrandMemberRole = 2
	BrandMemberRole_ViceCaption BrandMemberRole = 3
	BrandMemberRole_Producer    BrandMemberRole = 4
)

var BrandMemberRole_name = map[int32]string{
	0: "UnknownRole",
	1: "TeamLeader",
	2: "Normal",
	3: "ViceCaption",
	4: "Producer",
}
var BrandMemberRole_value = map[string]int32{
	"UnknownRole": 0,
	"TeamLeader":  1,
	"Normal":      2,
	"ViceCaption": 3,
	"Producer":    4,
}

func (x BrandMemberRole) String() string {
	return proto.EnumName(BrandMemberRole_name, int32(x))
}
func (BrandMemberRole) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{0}
}

type BrandStatus int32

const (
	BrandStatus_NoStatus BrandStatus = 0
	BrandStatus_Online   BrandStatus = 1
	BrandStatus_Offline  BrandStatus = 2
)

var BrandStatus_name = map[int32]string{
	0: "NoStatus",
	1: "Online",
	2: "Offline",
}
var BrandStatus_value = map[string]int32{
	"NoStatus": 0,
	"Online":   1,
	"Offline":  2,
}

func (x BrandStatus) String() string {
	return proto.EnumName(BrandStatus_name, int32(x))
}
func (BrandStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{1}
}

// 积分发放状态
type IntegralGrantStatus int32

const (
	IntegralGrantStatus_INTEGRAL_STATUS_NONE     IntegralGrantStatus = 0
	IntegralGrantStatus_INTEGRAL_STATUS_ADD      IntegralGrantStatus = 1
	IntegralGrantStatus_INTEGRAL_STATUS_DEDUCT   IntegralGrantStatus = 2
	IntegralGrantStatus_INTEGRAL_STATUS_UNISSUED IntegralGrantStatus = 3
)

var IntegralGrantStatus_name = map[int32]string{
	0: "INTEGRAL_STATUS_NONE",
	1: "INTEGRAL_STATUS_ADD",
	2: "INTEGRAL_STATUS_DEDUCT",
	3: "INTEGRAL_STATUS_UNISSUED",
}
var IntegralGrantStatus_value = map[string]int32{
	"INTEGRAL_STATUS_NONE":     0,
	"INTEGRAL_STATUS_ADD":      1,
	"INTEGRAL_STATUS_DEDUCT":   2,
	"INTEGRAL_STATUS_UNISSUED": 3,
}

func (x IntegralGrantStatus) String() string {
	return proto.EnumName(IntegralGrantStatus_name, int32(x))
}
func (IntegralGrantStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{2}
}

// 用户状态
type UserStatus int32

const (
	UserStatus_USER_STATUS_NORMAL   UserStatus = 0
	UserStatus_USER_STATUS_ABNORMAL UserStatus = 1
)

var UserStatus_name = map[int32]string{
	0: "USER_STATUS_NORMAL",
	1: "USER_STATUS_ABNORMAL",
}
var UserStatus_value = map[string]int32{
	"USER_STATUS_NORMAL":   0,
	"USER_STATUS_ABNORMAL": 1,
}

func (x UserStatus) String() string {
	return proto.EnumName(UserStatus_name, int32(x))
}
func (UserStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{3}
}

type ContentStatus int32

const (
	ContentStatus_CONTENT_STATUS_NONE                      ContentStatus = 0
	ContentStatus_CONTENT_STATUS_UNDER_REVIEW              ContentStatus = 1
	ContentStatus_CONTENT_STATUS_UNDER_REVIEW_AND_PREPARED ContentStatus = 2
	ContentStatus_CONTENT_STATUS_SUSPICIOUS                ContentStatus = 3
	ContentStatus_CONTENT_STATUS_ILLEGAL                   ContentStatus = 4
	ContentStatus_CONTENT_STATUS_NORMAL                    ContentStatus = 5
	ContentStatus_CONTENT_STATUS_DELETED                   ContentStatus = 6
	ContentStatus_CONTENT_STATUS_BANNED                    ContentStatus = 7
)

var ContentStatus_name = map[int32]string{
	0: "CONTENT_STATUS_NONE",
	1: "CONTENT_STATUS_UNDER_REVIEW",
	2: "CONTENT_STATUS_UNDER_REVIEW_AND_PREPARED",
	3: "CONTENT_STATUS_SUSPICIOUS",
	4: "CONTENT_STATUS_ILLEGAL",
	5: "CONTENT_STATUS_NORMAL",
	6: "CONTENT_STATUS_DELETED",
	7: "CONTENT_STATUS_BANNED",
}
var ContentStatus_value = map[string]int32{
	"CONTENT_STATUS_NONE":                      0,
	"CONTENT_STATUS_UNDER_REVIEW":              1,
	"CONTENT_STATUS_UNDER_REVIEW_AND_PREPARED": 2,
	"CONTENT_STATUS_SUSPICIOUS":                3,
	"CONTENT_STATUS_ILLEGAL":                   4,
	"CONTENT_STATUS_NORMAL":                    5,
	"CONTENT_STATUS_DELETED":                   6,
	"CONTENT_STATUS_BANNED":                    7,
}

func (x ContentStatus) String() string {
	return proto.EnumName(ContentStatus_name, int32(x))
}
func (ContentStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{4}
}

type AttachmentInfo_AttachmentType int32

const (
	AttachmentInfo_NONE  AttachmentInfo_AttachmentType = 0
	AttachmentInfo_IMAGE AttachmentInfo_AttachmentType = 1
	AttachmentInfo_GIF   AttachmentInfo_AttachmentType = 2
	AttachmentInfo_VIDEO AttachmentInfo_AttachmentType = 3
	AttachmentInfo_CMS   AttachmentInfo_AttachmentType = 4
	AttachmentInfo_AUDIO AttachmentInfo_AttachmentType = 5
)

var AttachmentInfo_AttachmentType_name = map[int32]string{
	0: "NONE",
	1: "IMAGE",
	2: "GIF",
	3: "VIDEO",
	4: "CMS",
	5: "AUDIO",
}
var AttachmentInfo_AttachmentType_value = map[string]int32{
	"NONE":  0,
	"IMAGE": 1,
	"GIF":   2,
	"VIDEO": 3,
	"CMS":   4,
	"AUDIO": 5,
}

func (x AttachmentInfo_AttachmentType) String() string {
	return proto.EnumName(AttachmentInfo_AttachmentType_name, int32(x))
}
func (AttachmentInfo_AttachmentType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{88, 0}
}

type FansAuthenticationInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CertTypeId           string   `protobuf:"bytes,2,opt,name=cert_type_id,json=certTypeId,proto3" json:"cert_type_id,omitempty"`
	ShortText            string   `protobuf:"bytes,3,opt,name=short_text,json=shortText,proto3" json:"short_text,omitempty"`
	Text                 string   `protobuf:"bytes,4,opt,name=text,proto3" json:"text,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,5,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	BeginTime            uint32   `protobuf:"varint,6,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,7,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FansAuthenticationInfo) Reset()         { *m = FansAuthenticationInfo{} }
func (m *FansAuthenticationInfo) String() string { return proto.CompactTextString(m) }
func (*FansAuthenticationInfo) ProtoMessage()    {}
func (*FansAuthenticationInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{0}
}
func (m *FansAuthenticationInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FansAuthenticationInfo.Unmarshal(m, b)
}
func (m *FansAuthenticationInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FansAuthenticationInfo.Marshal(b, m, deterministic)
}
func (dst *FansAuthenticationInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FansAuthenticationInfo.Merge(dst, src)
}
func (m *FansAuthenticationInfo) XXX_Size() int {
	return xxx_messageInfo_FansAuthenticationInfo.Size(m)
}
func (m *FansAuthenticationInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FansAuthenticationInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FansAuthenticationInfo proto.InternalMessageInfo

func (m *FansAuthenticationInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FansAuthenticationInfo) GetCertTypeId() string {
	if m != nil {
		return m.CertTypeId
	}
	return ""
}

func (m *FansAuthenticationInfo) GetShortText() string {
	if m != nil {
		return m.ShortText
	}
	return ""
}

func (m *FansAuthenticationInfo) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *FansAuthenticationInfo) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *FansAuthenticationInfo) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *FansAuthenticationInfo) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetFansAuthenticationReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFansAuthenticationReq) Reset()         { *m = GetFansAuthenticationReq{} }
func (m *GetFansAuthenticationReq) String() string { return proto.CompactTextString(m) }
func (*GetFansAuthenticationReq) ProtoMessage()    {}
func (*GetFansAuthenticationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{1}
}
func (m *GetFansAuthenticationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFansAuthenticationReq.Unmarshal(m, b)
}
func (m *GetFansAuthenticationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFansAuthenticationReq.Marshal(b, m, deterministic)
}
func (dst *GetFansAuthenticationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFansAuthenticationReq.Merge(dst, src)
}
func (m *GetFansAuthenticationReq) XXX_Size() int {
	return xxx_messageInfo_GetFansAuthenticationReq.Size(m)
}
func (m *GetFansAuthenticationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFansAuthenticationReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFansAuthenticationReq proto.InternalMessageInfo

func (m *GetFansAuthenticationReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetFansAuthenticationResp struct {
	FansAuthenticationInfo *FansAuthenticationInfo `protobuf:"bytes,1,opt,name=fans_authentication_info,json=fansAuthenticationInfo,proto3" json:"fans_authentication_info,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}                `json:"-"`
	XXX_unrecognized       []byte                  `json:"-"`
	XXX_sizecache          int32                   `json:"-"`
}

func (m *GetFansAuthenticationResp) Reset()         { *m = GetFansAuthenticationResp{} }
func (m *GetFansAuthenticationResp) String() string { return proto.CompactTextString(m) }
func (*GetFansAuthenticationResp) ProtoMessage()    {}
func (*GetFansAuthenticationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{2}
}
func (m *GetFansAuthenticationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFansAuthenticationResp.Unmarshal(m, b)
}
func (m *GetFansAuthenticationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFansAuthenticationResp.Marshal(b, m, deterministic)
}
func (dst *GetFansAuthenticationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFansAuthenticationResp.Merge(dst, src)
}
func (m *GetFansAuthenticationResp) XXX_Size() int {
	return xxx_messageInfo_GetFansAuthenticationResp.Size(m)
}
func (m *GetFansAuthenticationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFansAuthenticationResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFansAuthenticationResp proto.InternalMessageInfo

func (m *GetFansAuthenticationResp) GetFansAuthenticationInfo() *FansAuthenticationInfo {
	if m != nil {
		return m.FansAuthenticationInfo
	}
	return nil
}

type SetBrandReq struct {
	Brand                *BrandInfo `protobuf:"bytes,1,opt,name=brand,proto3" json:"brand,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *SetBrandReq) Reset()         { *m = SetBrandReq{} }
func (m *SetBrandReq) String() string { return proto.CompactTextString(m) }
func (*SetBrandReq) ProtoMessage()    {}
func (*SetBrandReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{3}
}
func (m *SetBrandReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetBrandReq.Unmarshal(m, b)
}
func (m *SetBrandReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetBrandReq.Marshal(b, m, deterministic)
}
func (dst *SetBrandReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetBrandReq.Merge(dst, src)
}
func (m *SetBrandReq) XXX_Size() int {
	return xxx_messageInfo_SetBrandReq.Size(m)
}
func (m *SetBrandReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetBrandReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetBrandReq proto.InternalMessageInfo

func (m *SetBrandReq) GetBrand() *BrandInfo {
	if m != nil {
		return m.Brand
	}
	return nil
}

type SetBrandResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetBrandResp) Reset()         { *m = SetBrandResp{} }
func (m *SetBrandResp) String() string { return proto.CompactTextString(m) }
func (*SetBrandResp) ProtoMessage()    {}
func (*SetBrandResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{4}
}
func (m *SetBrandResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetBrandResp.Unmarshal(m, b)
}
func (m *SetBrandResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetBrandResp.Marshal(b, m, deterministic)
}
func (dst *SetBrandResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetBrandResp.Merge(dst, src)
}
func (m *SetBrandResp) XXX_Size() int {
	return xxx_messageInfo_SetBrandResp.Size(m)
}
func (m *SetBrandResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetBrandResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetBrandResp proto.InternalMessageInfo

type GetBrandReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	BrandName            string   `protobuf:"bytes,3,opt,name=brand_name,json=brandName,proto3" json:"brand_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBrandReq) Reset()         { *m = GetBrandReq{} }
func (m *GetBrandReq) String() string { return proto.CompactTextString(m) }
func (*GetBrandReq) ProtoMessage()    {}
func (*GetBrandReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{5}
}
func (m *GetBrandReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBrandReq.Unmarshal(m, b)
}
func (m *GetBrandReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBrandReq.Marshal(b, m, deterministic)
}
func (dst *GetBrandReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBrandReq.Merge(dst, src)
}
func (m *GetBrandReq) XXX_Size() int {
	return xxx_messageInfo_GetBrandReq.Size(m)
}
func (m *GetBrandReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBrandReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBrandReq proto.InternalMessageInfo

func (m *GetBrandReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetBrandReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetBrandReq) GetBrandName() string {
	if m != nil {
		return m.BrandName
	}
	return ""
}

type BrandInfo struct {
	BrandId              string     `protobuf:"bytes,1,opt,name=brand_id,json=brandId,proto3" json:"brand_id,omitempty"`
	Name                 string     `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	MemberSize           uint32     `protobuf:"varint,3,opt,name=member_size,json=memberSize,proto3" json:"member_size,omitempty"`
	CreateTime           uint32     `protobuf:"varint,4,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Operator             string     `protobuf:"bytes,5,opt,name=operator,proto3" json:"operator,omitempty"`
	Intro                string     `protobuf:"bytes,6,opt,name=intro,proto3" json:"intro,omitempty"`
	MarkCount            uint32     `protobuf:"varint,7,opt,name=mark_count,json=markCount,proto3" json:"mark_count,omitempty"`
	FansCount            uint32     `protobuf:"varint,8,opt,name=fans_count,json=fansCount,proto3" json:"fans_count,omitempty"`
	TopicId              string     `protobuf:"bytes,9,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	SwitchChannelBg      bool       `protobuf:"varint,10,opt,name=switch_channel_bg,json=switchChannelBg,proto3" json:"switch_channel_bg,omitempty"`
	ChannelId            uint32     `protobuf:"varint,11,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	UnderPicUrl          string     `protobuf:"bytes,12,opt,name=under_pic_url,json=underPicUrl,proto3" json:"under_pic_url,omitempty"`
	UnderPicMd5          string     `protobuf:"bytes,13,opt,name=under_pic_md5,json=underPicMd5,proto3" json:"under_pic_md5,omitempty"`
	StagePicUrl          string     `protobuf:"bytes,14,opt,name=stage_pic_url,json=stagePicUrl,proto3" json:"stage_pic_url,omitempty"`
	StagePicMd5          string     `protobuf:"bytes,15,opt,name=stage_pic_md5,json=stagePicMd5,proto3" json:"stage_pic_md5,omitempty"`
	PicValidBeginTs      uint32     `protobuf:"varint,16,opt,name=pic_valid_begin_ts,json=picValidBeginTs,proto3" json:"pic_valid_begin_ts,omitempty"`
	PicValidEndTs        uint32     `protobuf:"varint,17,opt,name=pic_valid_end_ts,json=picValidEndTs,proto3" json:"pic_valid_end_ts,omitempty"`
	Info                 *BrandType `protobuf:"bytes,18,opt,name=info,proto3" json:"info,omitempty"`
	BrandTypeAttrInfo    string     `protobuf:"bytes,19,opt,name=brand_type_attr_info,json=brandTypeAttrInfo,proto3" json:"brand_type_attr_info,omitempty"`
	BrandIntegral        int32      `protobuf:"varint,20,opt,name=brand_integral,json=brandIntegral,proto3" json:"brand_integral,omitempty"`
	BrandSignCount       int32      `protobuf:"varint,21,opt,name=brand_sign_count,json=brandSignCount,proto3" json:"brand_sign_count,omitempty"`
	BrandStatus          uint32     `protobuf:"varint,22,opt,name=brand_status,json=brandStatus,proto3" json:"brand_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *BrandInfo) Reset()         { *m = BrandInfo{} }
func (m *BrandInfo) String() string { return proto.CompactTextString(m) }
func (*BrandInfo) ProtoMessage()    {}
func (*BrandInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{6}
}
func (m *BrandInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BrandInfo.Unmarshal(m, b)
}
func (m *BrandInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BrandInfo.Marshal(b, m, deterministic)
}
func (dst *BrandInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BrandInfo.Merge(dst, src)
}
func (m *BrandInfo) XXX_Size() int {
	return xxx_messageInfo_BrandInfo.Size(m)
}
func (m *BrandInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BrandInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BrandInfo proto.InternalMessageInfo

func (m *BrandInfo) GetBrandId() string {
	if m != nil {
		return m.BrandId
	}
	return ""
}

func (m *BrandInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *BrandInfo) GetMemberSize() uint32 {
	if m != nil {
		return m.MemberSize
	}
	return 0
}

func (m *BrandInfo) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *BrandInfo) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *BrandInfo) GetIntro() string {
	if m != nil {
		return m.Intro
	}
	return ""
}

func (m *BrandInfo) GetMarkCount() uint32 {
	if m != nil {
		return m.MarkCount
	}
	return 0
}

func (m *BrandInfo) GetFansCount() uint32 {
	if m != nil {
		return m.FansCount
	}
	return 0
}

func (m *BrandInfo) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *BrandInfo) GetSwitchChannelBg() bool {
	if m != nil {
		return m.SwitchChannelBg
	}
	return false
}

func (m *BrandInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BrandInfo) GetUnderPicUrl() string {
	if m != nil {
		return m.UnderPicUrl
	}
	return ""
}

func (m *BrandInfo) GetUnderPicMd5() string {
	if m != nil {
		return m.UnderPicMd5
	}
	return ""
}

func (m *BrandInfo) GetStagePicUrl() string {
	if m != nil {
		return m.StagePicUrl
	}
	return ""
}

func (m *BrandInfo) GetStagePicMd5() string {
	if m != nil {
		return m.StagePicMd5
	}
	return ""
}

func (m *BrandInfo) GetPicValidBeginTs() uint32 {
	if m != nil {
		return m.PicValidBeginTs
	}
	return 0
}

func (m *BrandInfo) GetPicValidEndTs() uint32 {
	if m != nil {
		return m.PicValidEndTs
	}
	return 0
}

func (m *BrandInfo) GetInfo() *BrandType {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *BrandInfo) GetBrandTypeAttrInfo() string {
	if m != nil {
		return m.BrandTypeAttrInfo
	}
	return ""
}

func (m *BrandInfo) GetBrandIntegral() int32 {
	if m != nil {
		return m.BrandIntegral
	}
	return 0
}

func (m *BrandInfo) GetBrandSignCount() int32 {
	if m != nil {
		return m.BrandSignCount
	}
	return 0
}

func (m *BrandInfo) GetBrandStatus() uint32 {
	if m != nil {
		return m.BrandStatus
	}
	return 0
}

type GetBrandResp struct {
	BrandList            []*BrandInfo `protobuf:"bytes,1,rep,name=brand_list,json=brandList,proto3" json:"brand_list,omitempty"`
	Total                uint32       `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetBrandResp) Reset()         { *m = GetBrandResp{} }
func (m *GetBrandResp) String() string { return proto.CompactTextString(m) }
func (*GetBrandResp) ProtoMessage()    {}
func (*GetBrandResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{7}
}
func (m *GetBrandResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBrandResp.Unmarshal(m, b)
}
func (m *GetBrandResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBrandResp.Marshal(b, m, deterministic)
}
func (dst *GetBrandResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBrandResp.Merge(dst, src)
}
func (m *GetBrandResp) XXX_Size() int {
	return xxx_messageInfo_GetBrandResp.Size(m)
}
func (m *GetBrandResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBrandResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBrandResp proto.InternalMessageInfo

func (m *GetBrandResp) GetBrandList() []*BrandInfo {
	if m != nil {
		return m.BrandList
	}
	return nil
}

func (m *GetBrandResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetBrandByIdReq struct {
	BrandId              string   `protobuf:"bytes,1,opt,name=brand_id,json=brandId,proto3" json:"brand_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBrandByIdReq) Reset()         { *m = GetBrandByIdReq{} }
func (m *GetBrandByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetBrandByIdReq) ProtoMessage()    {}
func (*GetBrandByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{8}
}
func (m *GetBrandByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBrandByIdReq.Unmarshal(m, b)
}
func (m *GetBrandByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBrandByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetBrandByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBrandByIdReq.Merge(dst, src)
}
func (m *GetBrandByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetBrandByIdReq.Size(m)
}
func (m *GetBrandByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBrandByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBrandByIdReq proto.InternalMessageInfo

func (m *GetBrandByIdReq) GetBrandId() string {
	if m != nil {
		return m.BrandId
	}
	return ""
}

type GetBrandByIdResp struct {
	Brand                *BrandInfo `protobuf:"bytes,1,opt,name=brand,proto3" json:"brand,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetBrandByIdResp) Reset()         { *m = GetBrandByIdResp{} }
func (m *GetBrandByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetBrandByIdResp) ProtoMessage()    {}
func (*GetBrandByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{9}
}
func (m *GetBrandByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBrandByIdResp.Unmarshal(m, b)
}
func (m *GetBrandByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBrandByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetBrandByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBrandByIdResp.Merge(dst, src)
}
func (m *GetBrandByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetBrandByIdResp.Size(m)
}
func (m *GetBrandByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBrandByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBrandByIdResp proto.InternalMessageInfo

func (m *GetBrandByIdResp) GetBrand() *BrandInfo {
	if m != nil {
		return m.Brand
	}
	return nil
}

type DelBrandReq struct {
	BrandId              string   `protobuf:"bytes,1,opt,name=brand_id,json=brandId,proto3" json:"brand_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelBrandReq) Reset()         { *m = DelBrandReq{} }
func (m *DelBrandReq) String() string { return proto.CompactTextString(m) }
func (*DelBrandReq) ProtoMessage()    {}
func (*DelBrandReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{10}
}
func (m *DelBrandReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelBrandReq.Unmarshal(m, b)
}
func (m *DelBrandReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelBrandReq.Marshal(b, m, deterministic)
}
func (dst *DelBrandReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelBrandReq.Merge(dst, src)
}
func (m *DelBrandReq) XXX_Size() int {
	return xxx_messageInfo_DelBrandReq.Size(m)
}
func (m *DelBrandReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelBrandReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelBrandReq proto.InternalMessageInfo

func (m *DelBrandReq) GetBrandId() string {
	if m != nil {
		return m.BrandId
	}
	return ""
}

type DelBrandResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelBrandResp) Reset()         { *m = DelBrandResp{} }
func (m *DelBrandResp) String() string { return proto.CompactTextString(m) }
func (*DelBrandResp) ProtoMessage()    {}
func (*DelBrandResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{11}
}
func (m *DelBrandResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelBrandResp.Unmarshal(m, b)
}
func (m *DelBrandResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelBrandResp.Marshal(b, m, deterministic)
}
func (dst *DelBrandResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelBrandResp.Merge(dst, src)
}
func (m *DelBrandResp) XXX_Size() int {
	return xxx_messageInfo_DelBrandResp.Size(m)
}
func (m *DelBrandResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelBrandResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelBrandResp proto.InternalMessageInfo

type ListBrandReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListBrandReq) Reset()         { *m = ListBrandReq{} }
func (m *ListBrandReq) String() string { return proto.CompactTextString(m) }
func (*ListBrandReq) ProtoMessage()    {}
func (*ListBrandReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{12}
}
func (m *ListBrandReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListBrandReq.Unmarshal(m, b)
}
func (m *ListBrandReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListBrandReq.Marshal(b, m, deterministic)
}
func (dst *ListBrandReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListBrandReq.Merge(dst, src)
}
func (m *ListBrandReq) XXX_Size() int {
	return xxx_messageInfo_ListBrandReq.Size(m)
}
func (m *ListBrandReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListBrandReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListBrandReq proto.InternalMessageInfo

type SimpleBrandInfo struct {
	BrandId              string   `protobuf:"bytes,1,opt,name=brand_id,json=brandId,proto3" json:"brand_id,omitempty"`
	BrandName            string   `protobuf:"bytes,2,opt,name=brand_name,json=brandName,proto3" json:"brand_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SimpleBrandInfo) Reset()         { *m = SimpleBrandInfo{} }
func (m *SimpleBrandInfo) String() string { return proto.CompactTextString(m) }
func (*SimpleBrandInfo) ProtoMessage()    {}
func (*SimpleBrandInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{13}
}
func (m *SimpleBrandInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SimpleBrandInfo.Unmarshal(m, b)
}
func (m *SimpleBrandInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SimpleBrandInfo.Marshal(b, m, deterministic)
}
func (dst *SimpleBrandInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SimpleBrandInfo.Merge(dst, src)
}
func (m *SimpleBrandInfo) XXX_Size() int {
	return xxx_messageInfo_SimpleBrandInfo.Size(m)
}
func (m *SimpleBrandInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SimpleBrandInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SimpleBrandInfo proto.InternalMessageInfo

func (m *SimpleBrandInfo) GetBrandId() string {
	if m != nil {
		return m.BrandId
	}
	return ""
}

func (m *SimpleBrandInfo) GetBrandName() string {
	if m != nil {
		return m.BrandName
	}
	return ""
}

type ListBrandResp struct {
	BrandList            []*SimpleBrandInfo `protobuf:"bytes,1,rep,name=brand_list,json=brandList,proto3" json:"brand_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *ListBrandResp) Reset()         { *m = ListBrandResp{} }
func (m *ListBrandResp) String() string { return proto.CompactTextString(m) }
func (*ListBrandResp) ProtoMessage()    {}
func (*ListBrandResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{14}
}
func (m *ListBrandResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListBrandResp.Unmarshal(m, b)
}
func (m *ListBrandResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListBrandResp.Marshal(b, m, deterministic)
}
func (dst *ListBrandResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListBrandResp.Merge(dst, src)
}
func (m *ListBrandResp) XXX_Size() int {
	return xxx_messageInfo_ListBrandResp.Size(m)
}
func (m *ListBrandResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListBrandResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListBrandResp proto.InternalMessageInfo

func (m *ListBrandResp) GetBrandList() []*SimpleBrandInfo {
	if m != nil {
		return m.BrandList
	}
	return nil
}

type BrandMemberInfo struct {
	BrandId              string   `protobuf:"bytes,1,opt,name=brand_id,json=brandId,proto3" json:"brand_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Role                 uint32   `protobuf:"varint,3,opt,name=role,proto3" json:"role,omitempty"`
	CreateTime           uint32   `protobuf:"varint,4,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Operator             string   `protobuf:"bytes,5,opt,name=operator,proto3" json:"operator,omitempty"`
	Intro                string   `protobuf:"bytes,6,opt,name=intro,proto3" json:"intro,omitempty"`
	BrandName            string   `protobuf:"bytes,7,opt,name=brand_name,json=brandName,proto3" json:"brand_name,omitempty"`
	BrandMemberStatus    uint32   `protobuf:"varint,8,opt,name=brand_member_status,json=brandMemberStatus,proto3" json:"brand_member_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BrandMemberInfo) Reset()         { *m = BrandMemberInfo{} }
func (m *BrandMemberInfo) String() string { return proto.CompactTextString(m) }
func (*BrandMemberInfo) ProtoMessage()    {}
func (*BrandMemberInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{15}
}
func (m *BrandMemberInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BrandMemberInfo.Unmarshal(m, b)
}
func (m *BrandMemberInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BrandMemberInfo.Marshal(b, m, deterministic)
}
func (dst *BrandMemberInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BrandMemberInfo.Merge(dst, src)
}
func (m *BrandMemberInfo) XXX_Size() int {
	return xxx_messageInfo_BrandMemberInfo.Size(m)
}
func (m *BrandMemberInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BrandMemberInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BrandMemberInfo proto.InternalMessageInfo

func (m *BrandMemberInfo) GetBrandId() string {
	if m != nil {
		return m.BrandId
	}
	return ""
}

func (m *BrandMemberInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BrandMemberInfo) GetRole() uint32 {
	if m != nil {
		return m.Role
	}
	return 0
}

func (m *BrandMemberInfo) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *BrandMemberInfo) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *BrandMemberInfo) GetIntro() string {
	if m != nil {
		return m.Intro
	}
	return ""
}

func (m *BrandMemberInfo) GetBrandName() string {
	if m != nil {
		return m.BrandName
	}
	return ""
}

func (m *BrandMemberInfo) GetBrandMemberStatus() uint32 {
	if m != nil {
		return m.BrandMemberStatus
	}
	return 0
}

type SetBrandMemberReq struct {
	MemberList           []*BrandMemberInfo `protobuf:"bytes,1,rep,name=member_list,json=memberList,proto3" json:"member_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SetBrandMemberReq) Reset()         { *m = SetBrandMemberReq{} }
func (m *SetBrandMemberReq) String() string { return proto.CompactTextString(m) }
func (*SetBrandMemberReq) ProtoMessage()    {}
func (*SetBrandMemberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{16}
}
func (m *SetBrandMemberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetBrandMemberReq.Unmarshal(m, b)
}
func (m *SetBrandMemberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetBrandMemberReq.Marshal(b, m, deterministic)
}
func (dst *SetBrandMemberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetBrandMemberReq.Merge(dst, src)
}
func (m *SetBrandMemberReq) XXX_Size() int {
	return xxx_messageInfo_SetBrandMemberReq.Size(m)
}
func (m *SetBrandMemberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetBrandMemberReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetBrandMemberReq proto.InternalMessageInfo

func (m *SetBrandMemberReq) GetMemberList() []*BrandMemberInfo {
	if m != nil {
		return m.MemberList
	}
	return nil
}

type SetBrandMemberResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetBrandMemberResp) Reset()         { *m = SetBrandMemberResp{} }
func (m *SetBrandMemberResp) String() string { return proto.CompactTextString(m) }
func (*SetBrandMemberResp) ProtoMessage()    {}
func (*SetBrandMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{17}
}
func (m *SetBrandMemberResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetBrandMemberResp.Unmarshal(m, b)
}
func (m *SetBrandMemberResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetBrandMemberResp.Marshal(b, m, deterministic)
}
func (dst *SetBrandMemberResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetBrandMemberResp.Merge(dst, src)
}
func (m *SetBrandMemberResp) XXX_Size() int {
	return xxx_messageInfo_SetBrandMemberResp.Size(m)
}
func (m *SetBrandMemberResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetBrandMemberResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetBrandMemberResp proto.InternalMessageInfo

type GetBrandMemberReq struct {
	BrandId              string   `protobuf:"bytes,1,opt,name=brand_id,json=brandId,proto3" json:"brand_id,omitempty"`
	Offset               uint32   `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	MemberStatus         uint32   `protobuf:"varint,5,opt,name=member_status,json=memberStatus,proto3" json:"member_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBrandMemberReq) Reset()         { *m = GetBrandMemberReq{} }
func (m *GetBrandMemberReq) String() string { return proto.CompactTextString(m) }
func (*GetBrandMemberReq) ProtoMessage()    {}
func (*GetBrandMemberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{18}
}
func (m *GetBrandMemberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBrandMemberReq.Unmarshal(m, b)
}
func (m *GetBrandMemberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBrandMemberReq.Marshal(b, m, deterministic)
}
func (dst *GetBrandMemberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBrandMemberReq.Merge(dst, src)
}
func (m *GetBrandMemberReq) XXX_Size() int {
	return xxx_messageInfo_GetBrandMemberReq.Size(m)
}
func (m *GetBrandMemberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBrandMemberReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBrandMemberReq proto.InternalMessageInfo

func (m *GetBrandMemberReq) GetBrandId() string {
	if m != nil {
		return m.BrandId
	}
	return ""
}

func (m *GetBrandMemberReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetBrandMemberReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetBrandMemberReq) GetMemberStatus() uint32 {
	if m != nil {
		return m.MemberStatus
	}
	return 0
}

type GetBrandMemberResp struct {
	MemberList           []*BrandMemberInfo `protobuf:"bytes,1,rep,name=member_list,json=memberList,proto3" json:"member_list,omitempty"`
	Total                uint32             `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetBrandMemberResp) Reset()         { *m = GetBrandMemberResp{} }
func (m *GetBrandMemberResp) String() string { return proto.CompactTextString(m) }
func (*GetBrandMemberResp) ProtoMessage()    {}
func (*GetBrandMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{19}
}
func (m *GetBrandMemberResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBrandMemberResp.Unmarshal(m, b)
}
func (m *GetBrandMemberResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBrandMemberResp.Marshal(b, m, deterministic)
}
func (dst *GetBrandMemberResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBrandMemberResp.Merge(dst, src)
}
func (m *GetBrandMemberResp) XXX_Size() int {
	return xxx_messageInfo_GetBrandMemberResp.Size(m)
}
func (m *GetBrandMemberResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBrandMemberResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBrandMemberResp proto.InternalMessageInfo

func (m *GetBrandMemberResp) GetMemberList() []*BrandMemberInfo {
	if m != nil {
		return m.MemberList
	}
	return nil
}

func (m *GetBrandMemberResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type DelBrandMemberReq struct {
	BrandId              string   `protobuf:"bytes,1,opt,name=brand_id,json=brandId,proto3" json:"brand_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelBrandMemberReq) Reset()         { *m = DelBrandMemberReq{} }
func (m *DelBrandMemberReq) String() string { return proto.CompactTextString(m) }
func (*DelBrandMemberReq) ProtoMessage()    {}
func (*DelBrandMemberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{20}
}
func (m *DelBrandMemberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelBrandMemberReq.Unmarshal(m, b)
}
func (m *DelBrandMemberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelBrandMemberReq.Marshal(b, m, deterministic)
}
func (dst *DelBrandMemberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelBrandMemberReq.Merge(dst, src)
}
func (m *DelBrandMemberReq) XXX_Size() int {
	return xxx_messageInfo_DelBrandMemberReq.Size(m)
}
func (m *DelBrandMemberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelBrandMemberReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelBrandMemberReq proto.InternalMessageInfo

func (m *DelBrandMemberReq) GetBrandId() string {
	if m != nil {
		return m.BrandId
	}
	return ""
}

func (m *DelBrandMemberReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type DelBrandMemberResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelBrandMemberResp) Reset()         { *m = DelBrandMemberResp{} }
func (m *DelBrandMemberResp) String() string { return proto.CompactTextString(m) }
func (*DelBrandMemberResp) ProtoMessage()    {}
func (*DelBrandMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{21}
}
func (m *DelBrandMemberResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelBrandMemberResp.Unmarshal(m, b)
}
func (m *DelBrandMemberResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelBrandMemberResp.Marshal(b, m, deterministic)
}
func (dst *DelBrandMemberResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelBrandMemberResp.Merge(dst, src)
}
func (m *DelBrandMemberResp) XXX_Size() int {
	return xxx_messageInfo_DelBrandMemberResp.Size(m)
}
func (m *DelBrandMemberResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelBrandMemberResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelBrandMemberResp proto.InternalMessageInfo

type BatGetBrandMemberReq struct {
	BrandId              []string `protobuf:"bytes,1,rep,name=brand_id,json=brandId,proto3" json:"brand_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetBrandMemberReq) Reset()         { *m = BatGetBrandMemberReq{} }
func (m *BatGetBrandMemberReq) String() string { return proto.CompactTextString(m) }
func (*BatGetBrandMemberReq) ProtoMessage()    {}
func (*BatGetBrandMemberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{22}
}
func (m *BatGetBrandMemberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetBrandMemberReq.Unmarshal(m, b)
}
func (m *BatGetBrandMemberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetBrandMemberReq.Marshal(b, m, deterministic)
}
func (dst *BatGetBrandMemberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetBrandMemberReq.Merge(dst, src)
}
func (m *BatGetBrandMemberReq) XXX_Size() int {
	return xxx_messageInfo_BatGetBrandMemberReq.Size(m)
}
func (m *BatGetBrandMemberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetBrandMemberReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetBrandMemberReq proto.InternalMessageInfo

func (m *BatGetBrandMemberReq) GetBrandId() []string {
	if m != nil {
		return m.BrandId
	}
	return nil
}

type MemberList struct {
	MemberList           []*BrandMemberInfo `protobuf:"bytes,1,rep,name=member_list,json=memberList,proto3" json:"member_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *MemberList) Reset()         { *m = MemberList{} }
func (m *MemberList) String() string { return proto.CompactTextString(m) }
func (*MemberList) ProtoMessage()    {}
func (*MemberList) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{23}
}
func (m *MemberList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberList.Unmarshal(m, b)
}
func (m *MemberList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberList.Marshal(b, m, deterministic)
}
func (dst *MemberList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberList.Merge(dst, src)
}
func (m *MemberList) XXX_Size() int {
	return xxx_messageInfo_MemberList.Size(m)
}
func (m *MemberList) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberList.DiscardUnknown(m)
}

var xxx_messageInfo_MemberList proto.InternalMessageInfo

func (m *MemberList) GetMemberList() []*BrandMemberInfo {
	if m != nil {
		return m.MemberList
	}
	return nil
}

type BatGetBrandMemberResp struct {
	BrandMemberMap       map[string]*MemberList `protobuf:"bytes,1,rep,name=brand_member_map,json=brandMemberMap,proto3" json:"brand_member_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *BatGetBrandMemberResp) Reset()         { *m = BatGetBrandMemberResp{} }
func (m *BatGetBrandMemberResp) String() string { return proto.CompactTextString(m) }
func (*BatGetBrandMemberResp) ProtoMessage()    {}
func (*BatGetBrandMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{24}
}
func (m *BatGetBrandMemberResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetBrandMemberResp.Unmarshal(m, b)
}
func (m *BatGetBrandMemberResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetBrandMemberResp.Marshal(b, m, deterministic)
}
func (dst *BatGetBrandMemberResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetBrandMemberResp.Merge(dst, src)
}
func (m *BatGetBrandMemberResp) XXX_Size() int {
	return xxx_messageInfo_BatGetBrandMemberResp.Size(m)
}
func (m *BatGetBrandMemberResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetBrandMemberResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetBrandMemberResp proto.InternalMessageInfo

func (m *BatGetBrandMemberResp) GetBrandMemberMap() map[string]*MemberList {
	if m != nil {
		return m.BrandMemberMap
	}
	return nil
}

type GetBrandMemberByUidsReq struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBrandMemberByUidsReq) Reset()         { *m = GetBrandMemberByUidsReq{} }
func (m *GetBrandMemberByUidsReq) String() string { return proto.CompactTextString(m) }
func (*GetBrandMemberByUidsReq) ProtoMessage()    {}
func (*GetBrandMemberByUidsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{25}
}
func (m *GetBrandMemberByUidsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBrandMemberByUidsReq.Unmarshal(m, b)
}
func (m *GetBrandMemberByUidsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBrandMemberByUidsReq.Marshal(b, m, deterministic)
}
func (dst *GetBrandMemberByUidsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBrandMemberByUidsReq.Merge(dst, src)
}
func (m *GetBrandMemberByUidsReq) XXX_Size() int {
	return xxx_messageInfo_GetBrandMemberByUidsReq.Size(m)
}
func (m *GetBrandMemberByUidsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBrandMemberByUidsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBrandMemberByUidsReq proto.InternalMessageInfo

func (m *GetBrandMemberByUidsReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type GetBrandMemberByUidsResp struct {
	MemberList           []*BrandMemberInfo `protobuf:"bytes,1,rep,name=member_list,json=memberList,proto3" json:"member_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetBrandMemberByUidsResp) Reset()         { *m = GetBrandMemberByUidsResp{} }
func (m *GetBrandMemberByUidsResp) String() string { return proto.CompactTextString(m) }
func (*GetBrandMemberByUidsResp) ProtoMessage()    {}
func (*GetBrandMemberByUidsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{26}
}
func (m *GetBrandMemberByUidsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBrandMemberByUidsResp.Unmarshal(m, b)
}
func (m *GetBrandMemberByUidsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBrandMemberByUidsResp.Marshal(b, m, deterministic)
}
func (dst *GetBrandMemberByUidsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBrandMemberByUidsResp.Merge(dst, src)
}
func (m *GetBrandMemberByUidsResp) XXX_Size() int {
	return xxx_messageInfo_GetBrandMemberByUidsResp.Size(m)
}
func (m *GetBrandMemberByUidsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBrandMemberByUidsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBrandMemberByUidsResp proto.InternalMessageInfo

func (m *GetBrandMemberByUidsResp) GetMemberList() []*BrandMemberInfo {
	if m != nil {
		return m.MemberList
	}
	return nil
}

// 查询用户是否有厂牌话题
type GetUserBrandTopicIDReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserBrandTopicIDReq) Reset()         { *m = GetUserBrandTopicIDReq{} }
func (m *GetUserBrandTopicIDReq) String() string { return proto.CompactTextString(m) }
func (*GetUserBrandTopicIDReq) ProtoMessage()    {}
func (*GetUserBrandTopicIDReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{27}
}
func (m *GetUserBrandTopicIDReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserBrandTopicIDReq.Unmarshal(m, b)
}
func (m *GetUserBrandTopicIDReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserBrandTopicIDReq.Marshal(b, m, deterministic)
}
func (dst *GetUserBrandTopicIDReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserBrandTopicIDReq.Merge(dst, src)
}
func (m *GetUserBrandTopicIDReq) XXX_Size() int {
	return xxx_messageInfo_GetUserBrandTopicIDReq.Size(m)
}
func (m *GetUserBrandTopicIDReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserBrandTopicIDReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserBrandTopicIDReq proto.InternalMessageInfo

func (m *GetUserBrandTopicIDReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

// ugc -topic
type GetUserBrandTopicIDResp struct {
	TopicId              string   `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	TopicName            string   `protobuf:"bytes,2,opt,name=topic_name,json=topicName,proto3" json:"topic_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserBrandTopicIDResp) Reset()         { *m = GetUserBrandTopicIDResp{} }
func (m *GetUserBrandTopicIDResp) String() string { return proto.CompactTextString(m) }
func (*GetUserBrandTopicIDResp) ProtoMessage()    {}
func (*GetUserBrandTopicIDResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{28}
}
func (m *GetUserBrandTopicIDResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserBrandTopicIDResp.Unmarshal(m, b)
}
func (m *GetUserBrandTopicIDResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserBrandTopicIDResp.Marshal(b, m, deterministic)
}
func (dst *GetUserBrandTopicIDResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserBrandTopicIDResp.Merge(dst, src)
}
func (m *GetUserBrandTopicIDResp) XXX_Size() int {
	return xxx_messageInfo_GetUserBrandTopicIDResp.Size(m)
}
func (m *GetUserBrandTopicIDResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserBrandTopicIDResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserBrandTopicIDResp proto.InternalMessageInfo

func (m *GetUserBrandTopicIDResp) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *GetUserBrandTopicIDResp) GetTopicName() string {
	if m != nil {
		return m.TopicName
	}
	return ""
}

// 批量添加厂牌
type BatchBrandInfo struct {
	BrandName            string   `protobuf:"bytes,1,opt,name=brand_name,json=brandName,proto3" json:"brand_name,omitempty"`
	MarkCount            uint32   `protobuf:"varint,2,opt,name=mark_count,json=markCount,proto3" json:"mark_count,omitempty"`
	BrandTypeName        string   `protobuf:"bytes,3,opt,name=brand_type_name,json=brandTypeName,proto3" json:"brand_type_name,omitempty"`
	BrandTypeAttrInfo    string   `protobuf:"bytes,4,opt,name=brand_type_attr_info,json=brandTypeAttrInfo,proto3" json:"brand_type_attr_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchBrandInfo) Reset()         { *m = BatchBrandInfo{} }
func (m *BatchBrandInfo) String() string { return proto.CompactTextString(m) }
func (*BatchBrandInfo) ProtoMessage()    {}
func (*BatchBrandInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{29}
}
func (m *BatchBrandInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchBrandInfo.Unmarshal(m, b)
}
func (m *BatchBrandInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchBrandInfo.Marshal(b, m, deterministic)
}
func (dst *BatchBrandInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchBrandInfo.Merge(dst, src)
}
func (m *BatchBrandInfo) XXX_Size() int {
	return xxx_messageInfo_BatchBrandInfo.Size(m)
}
func (m *BatchBrandInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchBrandInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BatchBrandInfo proto.InternalMessageInfo

func (m *BatchBrandInfo) GetBrandName() string {
	if m != nil {
		return m.BrandName
	}
	return ""
}

func (m *BatchBrandInfo) GetMarkCount() uint32 {
	if m != nil {
		return m.MarkCount
	}
	return 0
}

func (m *BatchBrandInfo) GetBrandTypeName() string {
	if m != nil {
		return m.BrandTypeName
	}
	return ""
}

func (m *BatchBrandInfo) GetBrandTypeAttrInfo() string {
	if m != nil {
		return m.BrandTypeAttrInfo
	}
	return ""
}

type BatchAddBrandReq struct {
	Operator             string            `protobuf:"bytes,1,opt,name=operator,proto3" json:"operator,omitempty"`
	BrandInfo            []*BatchBrandInfo `protobuf:"bytes,2,rep,name=brandInfo,proto3" json:"brandInfo,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchAddBrandReq) Reset()         { *m = BatchAddBrandReq{} }
func (m *BatchAddBrandReq) String() string { return proto.CompactTextString(m) }
func (*BatchAddBrandReq) ProtoMessage()    {}
func (*BatchAddBrandReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{30}
}
func (m *BatchAddBrandReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddBrandReq.Unmarshal(m, b)
}
func (m *BatchAddBrandReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddBrandReq.Marshal(b, m, deterministic)
}
func (dst *BatchAddBrandReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddBrandReq.Merge(dst, src)
}
func (m *BatchAddBrandReq) XXX_Size() int {
	return xxx_messageInfo_BatchAddBrandReq.Size(m)
}
func (m *BatchAddBrandReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddBrandReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddBrandReq proto.InternalMessageInfo

func (m *BatchAddBrandReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *BatchAddBrandReq) GetBrandInfo() []*BatchBrandInfo {
	if m != nil {
		return m.BrandInfo
	}
	return nil
}

type BatchAddBrandResp struct {
	Name                 []string `protobuf:"bytes,1,rep,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchAddBrandResp) Reset()         { *m = BatchAddBrandResp{} }
func (m *BatchAddBrandResp) String() string { return proto.CompactTextString(m) }
func (*BatchAddBrandResp) ProtoMessage()    {}
func (*BatchAddBrandResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{31}
}
func (m *BatchAddBrandResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddBrandResp.Unmarshal(m, b)
}
func (m *BatchAddBrandResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddBrandResp.Marshal(b, m, deterministic)
}
func (dst *BatchAddBrandResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddBrandResp.Merge(dst, src)
}
func (m *BatchAddBrandResp) XXX_Size() int {
	return xxx_messageInfo_BatchAddBrandResp.Size(m)
}
func (m *BatchAddBrandResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddBrandResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddBrandResp proto.InternalMessageInfo

func (m *BatchAddBrandResp) GetName() []string {
	if m != nil {
		return m.Name
	}
	return nil
}

// 厂牌名称模糊搜索
type BrandNameSearchReq struct {
	BrandName            string   `protobuf:"bytes,1,opt,name=brand_name,json=brandName,proto3" json:"brand_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BrandNameSearchReq) Reset()         { *m = BrandNameSearchReq{} }
func (m *BrandNameSearchReq) String() string { return proto.CompactTextString(m) }
func (*BrandNameSearchReq) ProtoMessage()    {}
func (*BrandNameSearchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{32}
}
func (m *BrandNameSearchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BrandNameSearchReq.Unmarshal(m, b)
}
func (m *BrandNameSearchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BrandNameSearchReq.Marshal(b, m, deterministic)
}
func (dst *BrandNameSearchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BrandNameSearchReq.Merge(dst, src)
}
func (m *BrandNameSearchReq) XXX_Size() int {
	return xxx_messageInfo_BrandNameSearchReq.Size(m)
}
func (m *BrandNameSearchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BrandNameSearchReq.DiscardUnknown(m)
}

var xxx_messageInfo_BrandNameSearchReq proto.InternalMessageInfo

func (m *BrandNameSearchReq) GetBrandName() string {
	if m != nil {
		return m.BrandName
	}
	return ""
}

type BrandIdNameInfo struct {
	BrandId              string   `protobuf:"bytes,1,opt,name=brand_id,json=brandId,proto3" json:"brand_id,omitempty"`
	BrandName            string   `protobuf:"bytes,2,opt,name=brand_name,json=brandName,proto3" json:"brand_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BrandIdNameInfo) Reset()         { *m = BrandIdNameInfo{} }
func (m *BrandIdNameInfo) String() string { return proto.CompactTextString(m) }
func (*BrandIdNameInfo) ProtoMessage()    {}
func (*BrandIdNameInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{33}
}
func (m *BrandIdNameInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BrandIdNameInfo.Unmarshal(m, b)
}
func (m *BrandIdNameInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BrandIdNameInfo.Marshal(b, m, deterministic)
}
func (dst *BrandIdNameInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BrandIdNameInfo.Merge(dst, src)
}
func (m *BrandIdNameInfo) XXX_Size() int {
	return xxx_messageInfo_BrandIdNameInfo.Size(m)
}
func (m *BrandIdNameInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BrandIdNameInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BrandIdNameInfo proto.InternalMessageInfo

func (m *BrandIdNameInfo) GetBrandId() string {
	if m != nil {
		return m.BrandId
	}
	return ""
}

func (m *BrandIdNameInfo) GetBrandName() string {
	if m != nil {
		return m.BrandName
	}
	return ""
}

type BrandNameSearchResp struct {
	Info                 []*BrandIdNameInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *BrandNameSearchResp) Reset()         { *m = BrandNameSearchResp{} }
func (m *BrandNameSearchResp) String() string { return proto.CompactTextString(m) }
func (*BrandNameSearchResp) ProtoMessage()    {}
func (*BrandNameSearchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{34}
}
func (m *BrandNameSearchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BrandNameSearchResp.Unmarshal(m, b)
}
func (m *BrandNameSearchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BrandNameSearchResp.Marshal(b, m, deterministic)
}
func (dst *BrandNameSearchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BrandNameSearchResp.Merge(dst, src)
}
func (m *BrandNameSearchResp) XXX_Size() int {
	return xxx_messageInfo_BrandNameSearchResp.Size(m)
}
func (m *BrandNameSearchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BrandNameSearchResp.DiscardUnknown(m)
}

var xxx_messageInfo_BrandNameSearchResp proto.InternalMessageInfo

func (m *BrandNameSearchResp) GetInfo() []*BrandIdNameInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

// 批量添加厂牌成员（新增接口）
type BatchSetMemberReq struct {
	Operator             string                `protobuf:"bytes,1,opt,name=operator,proto3" json:"operator,omitempty"`
	Info                 []*BatchSetMemberInfo `protobuf:"bytes,2,rep,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *BatchSetMemberReq) Reset()         { *m = BatchSetMemberReq{} }
func (m *BatchSetMemberReq) String() string { return proto.CompactTextString(m) }
func (*BatchSetMemberReq) ProtoMessage()    {}
func (*BatchSetMemberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{35}
}
func (m *BatchSetMemberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSetMemberReq.Unmarshal(m, b)
}
func (m *BatchSetMemberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSetMemberReq.Marshal(b, m, deterministic)
}
func (dst *BatchSetMemberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSetMemberReq.Merge(dst, src)
}
func (m *BatchSetMemberReq) XXX_Size() int {
	return xxx_messageInfo_BatchSetMemberReq.Size(m)
}
func (m *BatchSetMemberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSetMemberReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSetMemberReq proto.InternalMessageInfo

func (m *BatchSetMemberReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *BatchSetMemberReq) GetInfo() []*BatchSetMemberInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type BatchSetMemberInfo struct {
	BrandId              string   `protobuf:"bytes,1,opt,name=brand_id,json=brandId,proto3" json:"brand_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Role                 uint32   `protobuf:"varint,3,opt,name=role,proto3" json:"role,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchSetMemberInfo) Reset()         { *m = BatchSetMemberInfo{} }
func (m *BatchSetMemberInfo) String() string { return proto.CompactTextString(m) }
func (*BatchSetMemberInfo) ProtoMessage()    {}
func (*BatchSetMemberInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{36}
}
func (m *BatchSetMemberInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSetMemberInfo.Unmarshal(m, b)
}
func (m *BatchSetMemberInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSetMemberInfo.Marshal(b, m, deterministic)
}
func (dst *BatchSetMemberInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSetMemberInfo.Merge(dst, src)
}
func (m *BatchSetMemberInfo) XXX_Size() int {
	return xxx_messageInfo_BatchSetMemberInfo.Size(m)
}
func (m *BatchSetMemberInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSetMemberInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSetMemberInfo proto.InternalMessageInfo

func (m *BatchSetMemberInfo) GetBrandId() string {
	if m != nil {
		return m.BrandId
	}
	return ""
}

func (m *BatchSetMemberInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BatchSetMemberInfo) GetRole() uint32 {
	if m != nil {
		return m.Role
	}
	return 0
}

type BatchSetMemberResp struct {
	BrandName            []string `protobuf:"bytes,1,rep,name=brand_name,json=brandName,proto3" json:"brand_name,omitempty"`
	CheckTiming          uint32   `protobuf:"varint,2,opt,name=check_timing,json=checkTiming,proto3" json:"check_timing,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchSetMemberResp) Reset()         { *m = BatchSetMemberResp{} }
func (m *BatchSetMemberResp) String() string { return proto.CompactTextString(m) }
func (*BatchSetMemberResp) ProtoMessage()    {}
func (*BatchSetMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{37}
}
func (m *BatchSetMemberResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSetMemberResp.Unmarshal(m, b)
}
func (m *BatchSetMemberResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSetMemberResp.Marshal(b, m, deterministic)
}
func (dst *BatchSetMemberResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSetMemberResp.Merge(dst, src)
}
func (m *BatchSetMemberResp) XXX_Size() int {
	return xxx_messageInfo_BatchSetMemberResp.Size(m)
}
func (m *BatchSetMemberResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSetMemberResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSetMemberResp proto.InternalMessageInfo

func (m *BatchSetMemberResp) GetBrandName() []string {
	if m != nil {
		return m.BrandName
	}
	return nil
}

func (m *BatchSetMemberResp) GetCheckTiming() uint32 {
	if m != nil {
		return m.CheckTiming
	}
	return 0
}

// 批量删除厂牌成员（新增接口）
type BatchDelMemberInfo struct {
	BrandId              string   `protobuf:"bytes,1,opt,name=brand_id,json=brandId,proto3" json:"brand_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDelMemberInfo) Reset()         { *m = BatchDelMemberInfo{} }
func (m *BatchDelMemberInfo) String() string { return proto.CompactTextString(m) }
func (*BatchDelMemberInfo) ProtoMessage()    {}
func (*BatchDelMemberInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{38}
}
func (m *BatchDelMemberInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelMemberInfo.Unmarshal(m, b)
}
func (m *BatchDelMemberInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelMemberInfo.Marshal(b, m, deterministic)
}
func (dst *BatchDelMemberInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelMemberInfo.Merge(dst, src)
}
func (m *BatchDelMemberInfo) XXX_Size() int {
	return xxx_messageInfo_BatchDelMemberInfo.Size(m)
}
func (m *BatchDelMemberInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelMemberInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelMemberInfo proto.InternalMessageInfo

func (m *BatchDelMemberInfo) GetBrandId() string {
	if m != nil {
		return m.BrandId
	}
	return ""
}

func (m *BatchDelMemberInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type BatchDelMemberReq struct {
	Info                 []*BatchDelMemberInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *BatchDelMemberReq) Reset()         { *m = BatchDelMemberReq{} }
func (m *BatchDelMemberReq) String() string { return proto.CompactTextString(m) }
func (*BatchDelMemberReq) ProtoMessage()    {}
func (*BatchDelMemberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{39}
}
func (m *BatchDelMemberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelMemberReq.Unmarshal(m, b)
}
func (m *BatchDelMemberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelMemberReq.Marshal(b, m, deterministic)
}
func (dst *BatchDelMemberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelMemberReq.Merge(dst, src)
}
func (m *BatchDelMemberReq) XXX_Size() int {
	return xxx_messageInfo_BatchDelMemberReq.Size(m)
}
func (m *BatchDelMemberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelMemberReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelMemberReq proto.InternalMessageInfo

func (m *BatchDelMemberReq) GetInfo() []*BatchDelMemberInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type BatchDelMemberResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDelMemberResp) Reset()         { *m = BatchDelMemberResp{} }
func (m *BatchDelMemberResp) String() string { return proto.CompactTextString(m) }
func (*BatchDelMemberResp) ProtoMessage()    {}
func (*BatchDelMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{40}
}
func (m *BatchDelMemberResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelMemberResp.Unmarshal(m, b)
}
func (m *BatchDelMemberResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelMemberResp.Marshal(b, m, deterministic)
}
func (dst *BatchDelMemberResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelMemberResp.Merge(dst, src)
}
func (m *BatchDelMemberResp) XXX_Size() int {
	return xxx_messageInfo_BatchDelMemberResp.Size(m)
}
func (m *BatchDelMemberResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelMemberResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelMemberResp proto.InternalMessageInfo

// 厂牌类型
type BrandType struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	TypeAttr             string   `protobuf:"bytes,3,opt,name=type_attr,json=typeAttr,proto3" json:"type_attr,omitempty"`
	CertTypeName         string   `protobuf:"bytes,4,opt,name=cert_type_name,json=certTypeName,proto3" json:"cert_type_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BrandType) Reset()         { *m = BrandType{} }
func (m *BrandType) String() string { return proto.CompactTextString(m) }
func (*BrandType) ProtoMessage()    {}
func (*BrandType) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{41}
}
func (m *BrandType) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BrandType.Unmarshal(m, b)
}
func (m *BrandType) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BrandType.Marshal(b, m, deterministic)
}
func (dst *BrandType) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BrandType.Merge(dst, src)
}
func (m *BrandType) XXX_Size() int {
	return xxx_messageInfo_BrandType.Size(m)
}
func (m *BrandType) XXX_DiscardUnknown() {
	xxx_messageInfo_BrandType.DiscardUnknown(m)
}

var xxx_messageInfo_BrandType proto.InternalMessageInfo

func (m *BrandType) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *BrandType) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *BrandType) GetTypeAttr() string {
	if m != nil {
		return m.TypeAttr
	}
	return ""
}

func (m *BrandType) GetCertTypeName() string {
	if m != nil {
		return m.CertTypeName
	}
	return ""
}

type SetBrandTypeReq struct {
	Info                 *BrandType `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *SetBrandTypeReq) Reset()         { *m = SetBrandTypeReq{} }
func (m *SetBrandTypeReq) String() string { return proto.CompactTextString(m) }
func (*SetBrandTypeReq) ProtoMessage()    {}
func (*SetBrandTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{42}
}
func (m *SetBrandTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetBrandTypeReq.Unmarshal(m, b)
}
func (m *SetBrandTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetBrandTypeReq.Marshal(b, m, deterministic)
}
func (dst *SetBrandTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetBrandTypeReq.Merge(dst, src)
}
func (m *SetBrandTypeReq) XXX_Size() int {
	return xxx_messageInfo_SetBrandTypeReq.Size(m)
}
func (m *SetBrandTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetBrandTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetBrandTypeReq proto.InternalMessageInfo

func (m *SetBrandTypeReq) GetInfo() *BrandType {
	if m != nil {
		return m.Info
	}
	return nil
}

type SetBrandTypeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetBrandTypeResp) Reset()         { *m = SetBrandTypeResp{} }
func (m *SetBrandTypeResp) String() string { return proto.CompactTextString(m) }
func (*SetBrandTypeResp) ProtoMessage()    {}
func (*SetBrandTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{43}
}
func (m *SetBrandTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetBrandTypeResp.Unmarshal(m, b)
}
func (m *SetBrandTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetBrandTypeResp.Marshal(b, m, deterministic)
}
func (dst *SetBrandTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetBrandTypeResp.Merge(dst, src)
}
func (m *SetBrandTypeResp) XXX_Size() int {
	return xxx_messageInfo_SetBrandTypeResp.Size(m)
}
func (m *SetBrandTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetBrandTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetBrandTypeResp proto.InternalMessageInfo

// 删除厂牌类型 会同时删除该类型下的厂牌和其成员
type DelBrandTypeReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelBrandTypeReq) Reset()         { *m = DelBrandTypeReq{} }
func (m *DelBrandTypeReq) String() string { return proto.CompactTextString(m) }
func (*DelBrandTypeReq) ProtoMessage()    {}
func (*DelBrandTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{44}
}
func (m *DelBrandTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelBrandTypeReq.Unmarshal(m, b)
}
func (m *DelBrandTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelBrandTypeReq.Marshal(b, m, deterministic)
}
func (dst *DelBrandTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelBrandTypeReq.Merge(dst, src)
}
func (m *DelBrandTypeReq) XXX_Size() int {
	return xxx_messageInfo_DelBrandTypeReq.Size(m)
}
func (m *DelBrandTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelBrandTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelBrandTypeReq proto.InternalMessageInfo

func (m *DelBrandTypeReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type DelBrandTypeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelBrandTypeResp) Reset()         { *m = DelBrandTypeResp{} }
func (m *DelBrandTypeResp) String() string { return proto.CompactTextString(m) }
func (*DelBrandTypeResp) ProtoMessage()    {}
func (*DelBrandTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{45}
}
func (m *DelBrandTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelBrandTypeResp.Unmarshal(m, b)
}
func (m *DelBrandTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelBrandTypeResp.Marshal(b, m, deterministic)
}
func (dst *DelBrandTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelBrandTypeResp.Merge(dst, src)
}
func (m *DelBrandTypeResp) XXX_Size() int {
	return xxx_messageInfo_DelBrandTypeResp.Size(m)
}
func (m *DelBrandTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelBrandTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelBrandTypeResp proto.InternalMessageInfo

// 运营后台 无缓存
type GetBrandTypeFuzzySearchReq struct {
	Str                  string   `protobuf:"bytes,1,opt,name=str,proto3" json:"str,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBrandTypeFuzzySearchReq) Reset()         { *m = GetBrandTypeFuzzySearchReq{} }
func (m *GetBrandTypeFuzzySearchReq) String() string { return proto.CompactTextString(m) }
func (*GetBrandTypeFuzzySearchReq) ProtoMessage()    {}
func (*GetBrandTypeFuzzySearchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{46}
}
func (m *GetBrandTypeFuzzySearchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBrandTypeFuzzySearchReq.Unmarshal(m, b)
}
func (m *GetBrandTypeFuzzySearchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBrandTypeFuzzySearchReq.Marshal(b, m, deterministic)
}
func (dst *GetBrandTypeFuzzySearchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBrandTypeFuzzySearchReq.Merge(dst, src)
}
func (m *GetBrandTypeFuzzySearchReq) XXX_Size() int {
	return xxx_messageInfo_GetBrandTypeFuzzySearchReq.Size(m)
}
func (m *GetBrandTypeFuzzySearchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBrandTypeFuzzySearchReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBrandTypeFuzzySearchReq proto.InternalMessageInfo

func (m *GetBrandTypeFuzzySearchReq) GetStr() string {
	if m != nil {
		return m.Str
	}
	return ""
}

func (m *GetBrandTypeFuzzySearchReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetBrandTypeFuzzySearchReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetBrandTypeFuzzySearchResp struct {
	BrandTypeList        []*BrandType `protobuf:"bytes,1,rep,name=brand_type_list,json=brandTypeList,proto3" json:"brand_type_list,omitempty"`
	Total                uint32       `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetBrandTypeFuzzySearchResp) Reset()         { *m = GetBrandTypeFuzzySearchResp{} }
func (m *GetBrandTypeFuzzySearchResp) String() string { return proto.CompactTextString(m) }
func (*GetBrandTypeFuzzySearchResp) ProtoMessage()    {}
func (*GetBrandTypeFuzzySearchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{47}
}
func (m *GetBrandTypeFuzzySearchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBrandTypeFuzzySearchResp.Unmarshal(m, b)
}
func (m *GetBrandTypeFuzzySearchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBrandTypeFuzzySearchResp.Marshal(b, m, deterministic)
}
func (dst *GetBrandTypeFuzzySearchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBrandTypeFuzzySearchResp.Merge(dst, src)
}
func (m *GetBrandTypeFuzzySearchResp) XXX_Size() int {
	return xxx_messageInfo_GetBrandTypeFuzzySearchResp.Size(m)
}
func (m *GetBrandTypeFuzzySearchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBrandTypeFuzzySearchResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBrandTypeFuzzySearchResp proto.InternalMessageInfo

func (m *GetBrandTypeFuzzySearchResp) GetBrandTypeList() []*BrandType {
	if m != nil {
		return m.BrandTypeList
	}
	return nil
}

func (m *GetBrandTypeFuzzySearchResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 运营后台 无缓存 厂牌基础信息
type GetBrandBaseInfoReq struct {
	BrandName            string   `protobuf:"bytes,1,opt,name=brand_name,json=brandName,proto3" json:"brand_name,omitempty"`
	BrandTypeId          string   `protobuf:"bytes,2,opt,name=brand_type_id,json=brandTypeId,proto3" json:"brand_type_id,omitempty"`
	BrandTypeAttr        string   `protobuf:"bytes,3,opt,name=brand_type_attr,json=brandTypeAttr,proto3" json:"brand_type_attr,omitempty"`
	Offset               uint32   `protobuf:"varint,4,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	BrandStatus          uint32   `protobuf:"varint,6,opt,name=brand_status,json=brandStatus,proto3" json:"brand_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBrandBaseInfoReq) Reset()         { *m = GetBrandBaseInfoReq{} }
func (m *GetBrandBaseInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetBrandBaseInfoReq) ProtoMessage()    {}
func (*GetBrandBaseInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{48}
}
func (m *GetBrandBaseInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBrandBaseInfoReq.Unmarshal(m, b)
}
func (m *GetBrandBaseInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBrandBaseInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetBrandBaseInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBrandBaseInfoReq.Merge(dst, src)
}
func (m *GetBrandBaseInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetBrandBaseInfoReq.Size(m)
}
func (m *GetBrandBaseInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBrandBaseInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBrandBaseInfoReq proto.InternalMessageInfo

func (m *GetBrandBaseInfoReq) GetBrandName() string {
	if m != nil {
		return m.BrandName
	}
	return ""
}

func (m *GetBrandBaseInfoReq) GetBrandTypeId() string {
	if m != nil {
		return m.BrandTypeId
	}
	return ""
}

func (m *GetBrandBaseInfoReq) GetBrandTypeAttr() string {
	if m != nil {
		return m.BrandTypeAttr
	}
	return ""
}

func (m *GetBrandBaseInfoReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetBrandBaseInfoReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetBrandBaseInfoReq) GetBrandStatus() uint32 {
	if m != nil {
		return m.BrandStatus
	}
	return 0
}

type GetBrandBaseInfoResp struct {
	BrandList            []*BrandInfo `protobuf:"bytes,1,rep,name=brand_list,json=brandList,proto3" json:"brand_list,omitempty"`
	Total                uint32       `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetBrandBaseInfoResp) Reset()         { *m = GetBrandBaseInfoResp{} }
func (m *GetBrandBaseInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetBrandBaseInfoResp) ProtoMessage()    {}
func (*GetBrandBaseInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{49}
}
func (m *GetBrandBaseInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBrandBaseInfoResp.Unmarshal(m, b)
}
func (m *GetBrandBaseInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBrandBaseInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetBrandBaseInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBrandBaseInfoResp.Merge(dst, src)
}
func (m *GetBrandBaseInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetBrandBaseInfoResp.Size(m)
}
func (m *GetBrandBaseInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBrandBaseInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBrandBaseInfoResp proto.InternalMessageInfo

func (m *GetBrandBaseInfoResp) GetBrandList() []*BrandInfo {
	if m != nil {
		return m.BrandList
	}
	return nil
}

func (m *GetBrandBaseInfoResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetBrandTypeByIdReq struct {
	BrandId              string   `protobuf:"bytes,1,opt,name=brand_id,json=brandId,proto3" json:"brand_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBrandTypeByIdReq) Reset()         { *m = GetBrandTypeByIdReq{} }
func (m *GetBrandTypeByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetBrandTypeByIdReq) ProtoMessage()    {}
func (*GetBrandTypeByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{50}
}
func (m *GetBrandTypeByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBrandTypeByIdReq.Unmarshal(m, b)
}
func (m *GetBrandTypeByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBrandTypeByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetBrandTypeByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBrandTypeByIdReq.Merge(dst, src)
}
func (m *GetBrandTypeByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetBrandTypeByIdReq.Size(m)
}
func (m *GetBrandTypeByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBrandTypeByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBrandTypeByIdReq proto.InternalMessageInfo

func (m *GetBrandTypeByIdReq) GetBrandId() string {
	if m != nil {
		return m.BrandId
	}
	return ""
}

type GetBrandTypeByIdResp struct {
	Brand                *BrandType `protobuf:"bytes,1,opt,name=brand,proto3" json:"brand,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetBrandTypeByIdResp) Reset()         { *m = GetBrandTypeByIdResp{} }
func (m *GetBrandTypeByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetBrandTypeByIdResp) ProtoMessage()    {}
func (*GetBrandTypeByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{51}
}
func (m *GetBrandTypeByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBrandTypeByIdResp.Unmarshal(m, b)
}
func (m *GetBrandTypeByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBrandTypeByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetBrandTypeByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBrandTypeByIdResp.Merge(dst, src)
}
func (m *GetBrandTypeByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetBrandTypeByIdResp.Size(m)
}
func (m *GetBrandTypeByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBrandTypeByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBrandTypeByIdResp proto.InternalMessageInfo

func (m *GetBrandTypeByIdResp) GetBrand() *BrandType {
	if m != nil {
		return m.Brand
	}
	return nil
}

type SetBrandRoomReq struct {
	Info                 *BrandRoomInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *SetBrandRoomReq) Reset()         { *m = SetBrandRoomReq{} }
func (m *SetBrandRoomReq) String() string { return proto.CompactTextString(m) }
func (*SetBrandRoomReq) ProtoMessage()    {}
func (*SetBrandRoomReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{52}
}
func (m *SetBrandRoomReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetBrandRoomReq.Unmarshal(m, b)
}
func (m *SetBrandRoomReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetBrandRoomReq.Marshal(b, m, deterministic)
}
func (dst *SetBrandRoomReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetBrandRoomReq.Merge(dst, src)
}
func (m *SetBrandRoomReq) XXX_Size() int {
	return xxx_messageInfo_SetBrandRoomReq.Size(m)
}
func (m *SetBrandRoomReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetBrandRoomReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetBrandRoomReq proto.InternalMessageInfo

func (m *SetBrandRoomReq) GetInfo() *BrandRoomInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type SetBrandRoomResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetBrandRoomResp) Reset()         { *m = SetBrandRoomResp{} }
func (m *SetBrandRoomResp) String() string { return proto.CompactTextString(m) }
func (*SetBrandRoomResp) ProtoMessage()    {}
func (*SetBrandRoomResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{53}
}
func (m *SetBrandRoomResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetBrandRoomResp.Unmarshal(m, b)
}
func (m *SetBrandRoomResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetBrandRoomResp.Marshal(b, m, deterministic)
}
func (dst *SetBrandRoomResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetBrandRoomResp.Merge(dst, src)
}
func (m *SetBrandRoomResp) XXX_Size() int {
	return xxx_messageInfo_SetBrandRoomResp.Size(m)
}
func (m *SetBrandRoomResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetBrandRoomResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetBrandRoomResp proto.InternalMessageInfo

type BrandRoomInfo struct {
	BrandName            string   `protobuf:"bytes,1,opt,name=brand_name,json=brandName,proto3" json:"brand_name,omitempty"`
	BrandId              string   `protobuf:"bytes,2,opt,name=brand_id,json=brandId,proto3" json:"brand_id,omitempty"`
	TopicId              string   `protobuf:"bytes,3,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	SwitchChannelBg      bool     `protobuf:"varint,4,opt,name=switch_channel_bg,json=switchChannelBg,proto3" json:"switch_channel_bg,omitempty"`
	ChannelId            uint32   `protobuf:"varint,5,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	UnderPicUrl          string   `protobuf:"bytes,6,opt,name=under_pic_url,json=underPicUrl,proto3" json:"under_pic_url,omitempty"`
	UnderPicMd5          string   `protobuf:"bytes,7,opt,name=under_pic_md5,json=underPicMd5,proto3" json:"under_pic_md5,omitempty"`
	StagePicUrl          string   `protobuf:"bytes,8,opt,name=stage_pic_url,json=stagePicUrl,proto3" json:"stage_pic_url,omitempty"`
	StagePicMd5          string   `protobuf:"bytes,9,opt,name=stage_pic_md5,json=stagePicMd5,proto3" json:"stage_pic_md5,omitempty"`
	PicValidBeginTs      uint32   `protobuf:"varint,10,opt,name=pic_valid_begin_ts,json=picValidBeginTs,proto3" json:"pic_valid_begin_ts,omitempty"`
	PicValidEndTs        uint32   `protobuf:"varint,11,opt,name=pic_valid_end_ts,json=picValidEndTs,proto3" json:"pic_valid_end_ts,omitempty"`
	PcUnderPicUrl        string   `protobuf:"bytes,12,opt,name=pc_under_pic_url,json=pcUnderPicUrl,proto3" json:"pc_under_pic_url,omitempty"`
	PcUnderPicMd5        string   `protobuf:"bytes,13,opt,name=pc_under_pic_md5,json=pcUnderPicMd5,proto3" json:"pc_under_pic_md5,omitempty"`
	PcStagePicUrl        string   `protobuf:"bytes,14,opt,name=pc_stage_pic_url,json=pcStagePicUrl,proto3" json:"pc_stage_pic_url,omitempty"`
	PcStagePicMd5        string   `protobuf:"bytes,15,opt,name=pc_stage_pic_md5,json=pcStagePicMd5,proto3" json:"pc_stage_pic_md5,omitempty"`
	Operator             string   `protobuf:"bytes,16,opt,name=operator,proto3" json:"operator,omitempty"`
	TopicName            string   `protobuf:"bytes,17,opt,name=topic_name,json=topicName,proto3" json:"topic_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BrandRoomInfo) Reset()         { *m = BrandRoomInfo{} }
func (m *BrandRoomInfo) String() string { return proto.CompactTextString(m) }
func (*BrandRoomInfo) ProtoMessage()    {}
func (*BrandRoomInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{54}
}
func (m *BrandRoomInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BrandRoomInfo.Unmarshal(m, b)
}
func (m *BrandRoomInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BrandRoomInfo.Marshal(b, m, deterministic)
}
func (dst *BrandRoomInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BrandRoomInfo.Merge(dst, src)
}
func (m *BrandRoomInfo) XXX_Size() int {
	return xxx_messageInfo_BrandRoomInfo.Size(m)
}
func (m *BrandRoomInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BrandRoomInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BrandRoomInfo proto.InternalMessageInfo

func (m *BrandRoomInfo) GetBrandName() string {
	if m != nil {
		return m.BrandName
	}
	return ""
}

func (m *BrandRoomInfo) GetBrandId() string {
	if m != nil {
		return m.BrandId
	}
	return ""
}

func (m *BrandRoomInfo) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *BrandRoomInfo) GetSwitchChannelBg() bool {
	if m != nil {
		return m.SwitchChannelBg
	}
	return false
}

func (m *BrandRoomInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BrandRoomInfo) GetUnderPicUrl() string {
	if m != nil {
		return m.UnderPicUrl
	}
	return ""
}

func (m *BrandRoomInfo) GetUnderPicMd5() string {
	if m != nil {
		return m.UnderPicMd5
	}
	return ""
}

func (m *BrandRoomInfo) GetStagePicUrl() string {
	if m != nil {
		return m.StagePicUrl
	}
	return ""
}

func (m *BrandRoomInfo) GetStagePicMd5() string {
	if m != nil {
		return m.StagePicMd5
	}
	return ""
}

func (m *BrandRoomInfo) GetPicValidBeginTs() uint32 {
	if m != nil {
		return m.PicValidBeginTs
	}
	return 0
}

func (m *BrandRoomInfo) GetPicValidEndTs() uint32 {
	if m != nil {
		return m.PicValidEndTs
	}
	return 0
}

func (m *BrandRoomInfo) GetPcUnderPicUrl() string {
	if m != nil {
		return m.PcUnderPicUrl
	}
	return ""
}

func (m *BrandRoomInfo) GetPcUnderPicMd5() string {
	if m != nil {
		return m.PcUnderPicMd5
	}
	return ""
}

func (m *BrandRoomInfo) GetPcStagePicUrl() string {
	if m != nil {
		return m.PcStagePicUrl
	}
	return ""
}

func (m *BrandRoomInfo) GetPcStagePicMd5() string {
	if m != nil {
		return m.PcStagePicMd5
	}
	return ""
}

func (m *BrandRoomInfo) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *BrandRoomInfo) GetTopicName() string {
	if m != nil {
		return m.TopicName
	}
	return ""
}

type GetBrandRoomReq struct {
	Limit                uint32   `protobuf:"varint,1,opt,name=limit,proto3" json:"limit,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	BrandId              string   `protobuf:"bytes,4,opt,name=brand_id,json=brandId,proto3" json:"brand_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBrandRoomReq) Reset()         { *m = GetBrandRoomReq{} }
func (m *GetBrandRoomReq) String() string { return proto.CompactTextString(m) }
func (*GetBrandRoomReq) ProtoMessage()    {}
func (*GetBrandRoomReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{55}
}
func (m *GetBrandRoomReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBrandRoomReq.Unmarshal(m, b)
}
func (m *GetBrandRoomReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBrandRoomReq.Marshal(b, m, deterministic)
}
func (dst *GetBrandRoomReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBrandRoomReq.Merge(dst, src)
}
func (m *GetBrandRoomReq) XXX_Size() int {
	return xxx_messageInfo_GetBrandRoomReq.Size(m)
}
func (m *GetBrandRoomReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBrandRoomReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBrandRoomReq proto.InternalMessageInfo

func (m *GetBrandRoomReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetBrandRoomReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetBrandRoomReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetBrandRoomReq) GetBrandId() string {
	if m != nil {
		return m.BrandId
	}
	return ""
}

type GetBrandRoomResp struct {
	Info                 []*BrandRoomInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	Total                uint32           `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetBrandRoomResp) Reset()         { *m = GetBrandRoomResp{} }
func (m *GetBrandRoomResp) String() string { return proto.CompactTextString(m) }
func (*GetBrandRoomResp) ProtoMessage()    {}
func (*GetBrandRoomResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{56}
}
func (m *GetBrandRoomResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBrandRoomResp.Unmarshal(m, b)
}
func (m *GetBrandRoomResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBrandRoomResp.Marshal(b, m, deterministic)
}
func (dst *GetBrandRoomResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBrandRoomResp.Merge(dst, src)
}
func (m *GetBrandRoomResp) XXX_Size() int {
	return xxx_messageInfo_GetBrandRoomResp.Size(m)
}
func (m *GetBrandRoomResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBrandRoomResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBrandRoomResp proto.InternalMessageInfo

func (m *GetBrandRoomResp) GetInfo() []*BrandRoomInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *GetBrandRoomResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type DelBrandRoomReq struct {
	BrandId              string   `protobuf:"bytes,1,opt,name=brand_id,json=brandId,proto3" json:"brand_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelBrandRoomReq) Reset()         { *m = DelBrandRoomReq{} }
func (m *DelBrandRoomReq) String() string { return proto.CompactTextString(m) }
func (*DelBrandRoomReq) ProtoMessage()    {}
func (*DelBrandRoomReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{57}
}
func (m *DelBrandRoomReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelBrandRoomReq.Unmarshal(m, b)
}
func (m *DelBrandRoomReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelBrandRoomReq.Marshal(b, m, deterministic)
}
func (dst *DelBrandRoomReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelBrandRoomReq.Merge(dst, src)
}
func (m *DelBrandRoomReq) XXX_Size() int {
	return xxx_messageInfo_DelBrandRoomReq.Size(m)
}
func (m *DelBrandRoomReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelBrandRoomReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelBrandRoomReq proto.InternalMessageInfo

func (m *DelBrandRoomReq) GetBrandId() string {
	if m != nil {
		return m.BrandId
	}
	return ""
}

type DelBrandRoomResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelBrandRoomResp) Reset()         { *m = DelBrandRoomResp{} }
func (m *DelBrandRoomResp) String() string { return proto.CompactTextString(m) }
func (*DelBrandRoomResp) ProtoMessage()    {}
func (*DelBrandRoomResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{58}
}
func (m *DelBrandRoomResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelBrandRoomResp.Unmarshal(m, b)
}
func (m *DelBrandRoomResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelBrandRoomResp.Marshal(b, m, deterministic)
}
func (dst *DelBrandRoomResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelBrandRoomResp.Merge(dst, src)
}
func (m *DelBrandRoomResp) XXX_Size() int {
	return xxx_messageInfo_DelBrandRoomResp.Size(m)
}
func (m *DelBrandRoomResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelBrandRoomResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelBrandRoomResp proto.InternalMessageInfo

type GetBrandIdByUidReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBrandIdByUidReq) Reset()         { *m = GetBrandIdByUidReq{} }
func (m *GetBrandIdByUidReq) String() string { return proto.CompactTextString(m) }
func (*GetBrandIdByUidReq) ProtoMessage()    {}
func (*GetBrandIdByUidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{59}
}
func (m *GetBrandIdByUidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBrandIdByUidReq.Unmarshal(m, b)
}
func (m *GetBrandIdByUidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBrandIdByUidReq.Marshal(b, m, deterministic)
}
func (dst *GetBrandIdByUidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBrandIdByUidReq.Merge(dst, src)
}
func (m *GetBrandIdByUidReq) XXX_Size() int {
	return xxx_messageInfo_GetBrandIdByUidReq.Size(m)
}
func (m *GetBrandIdByUidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBrandIdByUidReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBrandIdByUidReq proto.InternalMessageInfo

func (m *GetBrandIdByUidReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetBrandIdByUidResp struct {
	BrandId              string   `protobuf:"bytes,1,opt,name=brand_id,json=brandId,proto3" json:"brand_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBrandIdByUidResp) Reset()         { *m = GetBrandIdByUidResp{} }
func (m *GetBrandIdByUidResp) String() string { return proto.CompactTextString(m) }
func (*GetBrandIdByUidResp) ProtoMessage()    {}
func (*GetBrandIdByUidResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{60}
}
func (m *GetBrandIdByUidResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBrandIdByUidResp.Unmarshal(m, b)
}
func (m *GetBrandIdByUidResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBrandIdByUidResp.Marshal(b, m, deterministic)
}
func (dst *GetBrandIdByUidResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBrandIdByUidResp.Merge(dst, src)
}
func (m *GetBrandIdByUidResp) XXX_Size() int {
	return xxx_messageInfo_GetBrandIdByUidResp.Size(m)
}
func (m *GetBrandIdByUidResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBrandIdByUidResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBrandIdByUidResp proto.InternalMessageInfo

func (m *GetBrandIdByUidResp) GetBrandId() string {
	if m != nil {
		return m.BrandId
	}
	return ""
}

type GetBrandTopicByIdReq struct {
	BrandId              string   `protobuf:"bytes,1,opt,name=brand_id,json=brandId,proto3" json:"brand_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBrandTopicByIdReq) Reset()         { *m = GetBrandTopicByIdReq{} }
func (m *GetBrandTopicByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetBrandTopicByIdReq) ProtoMessage()    {}
func (*GetBrandTopicByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{61}
}
func (m *GetBrandTopicByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBrandTopicByIdReq.Unmarshal(m, b)
}
func (m *GetBrandTopicByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBrandTopicByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetBrandTopicByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBrandTopicByIdReq.Merge(dst, src)
}
func (m *GetBrandTopicByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetBrandTopicByIdReq.Size(m)
}
func (m *GetBrandTopicByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBrandTopicByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBrandTopicByIdReq proto.InternalMessageInfo

func (m *GetBrandTopicByIdReq) GetBrandId() string {
	if m != nil {
		return m.BrandId
	}
	return ""
}

type GetBrandTopicByIdResp struct {
	TopicId              string   `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBrandTopicByIdResp) Reset()         { *m = GetBrandTopicByIdResp{} }
func (m *GetBrandTopicByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetBrandTopicByIdResp) ProtoMessage()    {}
func (*GetBrandTopicByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{62}
}
func (m *GetBrandTopicByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBrandTopicByIdResp.Unmarshal(m, b)
}
func (m *GetBrandTopicByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBrandTopicByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetBrandTopicByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBrandTopicByIdResp.Merge(dst, src)
}
func (m *GetBrandTopicByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetBrandTopicByIdResp.Size(m)
}
func (m *GetBrandTopicByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBrandTopicByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBrandTopicByIdResp proto.InternalMessageInfo

func (m *GetBrandTopicByIdResp) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

// 积分
type SetBrandIntegralReq struct {
	Info                 *BrandIntegralInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SetBrandIntegralReq) Reset()         { *m = SetBrandIntegralReq{} }
func (m *SetBrandIntegralReq) String() string { return proto.CompactTextString(m) }
func (*SetBrandIntegralReq) ProtoMessage()    {}
func (*SetBrandIntegralReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{63}
}
func (m *SetBrandIntegralReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetBrandIntegralReq.Unmarshal(m, b)
}
func (m *SetBrandIntegralReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetBrandIntegralReq.Marshal(b, m, deterministic)
}
func (dst *SetBrandIntegralReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetBrandIntegralReq.Merge(dst, src)
}
func (m *SetBrandIntegralReq) XXX_Size() int {
	return xxx_messageInfo_SetBrandIntegralReq.Size(m)
}
func (m *SetBrandIntegralReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetBrandIntegralReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetBrandIntegralReq proto.InternalMessageInfo

func (m *SetBrandIntegralReq) GetInfo() *BrandIntegralInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type SetBrandIntegralResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetBrandIntegralResp) Reset()         { *m = SetBrandIntegralResp{} }
func (m *SetBrandIntegralResp) String() string { return proto.CompactTextString(m) }
func (*SetBrandIntegralResp) ProtoMessage()    {}
func (*SetBrandIntegralResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{64}
}
func (m *SetBrandIntegralResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetBrandIntegralResp.Unmarshal(m, b)
}
func (m *SetBrandIntegralResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetBrandIntegralResp.Marshal(b, m, deterministic)
}
func (dst *SetBrandIntegralResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetBrandIntegralResp.Merge(dst, src)
}
func (m *SetBrandIntegralResp) XXX_Size() int {
	return xxx_messageInfo_SetBrandIntegralResp.Size(m)
}
func (m *SetBrandIntegralResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetBrandIntegralResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetBrandIntegralResp proto.InternalMessageInfo

type BrandIntegralInfo struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	BrandId              string   `protobuf:"bytes,2,opt,name=brand_id,json=brandId,proto3" json:"brand_id,omitempty"`
	BrandName            string   `protobuf:"bytes,3,opt,name=brand_name,json=brandName,proto3" json:"brand_name,omitempty"`
	Count                int32    `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	Intro                string   `protobuf:"bytes,5,opt,name=intro,proto3" json:"intro,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,6,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	Operate              string   `protobuf:"bytes,7,opt,name=operate,proto3" json:"operate,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BrandIntegralInfo) Reset()         { *m = BrandIntegralInfo{} }
func (m *BrandIntegralInfo) String() string { return proto.CompactTextString(m) }
func (*BrandIntegralInfo) ProtoMessage()    {}
func (*BrandIntegralInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{65}
}
func (m *BrandIntegralInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BrandIntegralInfo.Unmarshal(m, b)
}
func (m *BrandIntegralInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BrandIntegralInfo.Marshal(b, m, deterministic)
}
func (dst *BrandIntegralInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BrandIntegralInfo.Merge(dst, src)
}
func (m *BrandIntegralInfo) XXX_Size() int {
	return xxx_messageInfo_BrandIntegralInfo.Size(m)
}
func (m *BrandIntegralInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BrandIntegralInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BrandIntegralInfo proto.InternalMessageInfo

func (m *BrandIntegralInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *BrandIntegralInfo) GetBrandId() string {
	if m != nil {
		return m.BrandId
	}
	return ""
}

func (m *BrandIntegralInfo) GetBrandName() string {
	if m != nil {
		return m.BrandName
	}
	return ""
}

func (m *BrandIntegralInfo) GetCount() int32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *BrandIntegralInfo) GetIntro() string {
	if m != nil {
		return m.Intro
	}
	return ""
}

func (m *BrandIntegralInfo) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *BrandIntegralInfo) GetOperate() string {
	if m != nil {
		return m.Operate
	}
	return ""
}

type GetBrandIntegralReq struct {
	BrandId              string   `protobuf:"bytes,1,opt,name=brand_id,json=brandId,proto3" json:"brand_id,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Offset               uint32   `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBrandIntegralReq) Reset()         { *m = GetBrandIntegralReq{} }
func (m *GetBrandIntegralReq) String() string { return proto.CompactTextString(m) }
func (*GetBrandIntegralReq) ProtoMessage()    {}
func (*GetBrandIntegralReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{66}
}
func (m *GetBrandIntegralReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBrandIntegralReq.Unmarshal(m, b)
}
func (m *GetBrandIntegralReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBrandIntegralReq.Marshal(b, m, deterministic)
}
func (dst *GetBrandIntegralReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBrandIntegralReq.Merge(dst, src)
}
func (m *GetBrandIntegralReq) XXX_Size() int {
	return xxx_messageInfo_GetBrandIntegralReq.Size(m)
}
func (m *GetBrandIntegralReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBrandIntegralReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBrandIntegralReq proto.InternalMessageInfo

func (m *GetBrandIntegralReq) GetBrandId() string {
	if m != nil {
		return m.BrandId
	}
	return ""
}

func (m *GetBrandIntegralReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetBrandIntegralReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

type GetBrandIntegralResp struct {
	Info                 []*BrandIntegralInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	Total                uint32               `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetBrandIntegralResp) Reset()         { *m = GetBrandIntegralResp{} }
func (m *GetBrandIntegralResp) String() string { return proto.CompactTextString(m) }
func (*GetBrandIntegralResp) ProtoMessage()    {}
func (*GetBrandIntegralResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{67}
}
func (m *GetBrandIntegralResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBrandIntegralResp.Unmarshal(m, b)
}
func (m *GetBrandIntegralResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBrandIntegralResp.Marshal(b, m, deterministic)
}
func (dst *GetBrandIntegralResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBrandIntegralResp.Merge(dst, src)
}
func (m *GetBrandIntegralResp) XXX_Size() int {
	return xxx_messageInfo_GetBrandIntegralResp.Size(m)
}
func (m *GetBrandIntegralResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBrandIntegralResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBrandIntegralResp proto.InternalMessageInfo

func (m *GetBrandIntegralResp) GetInfo() []*BrandIntegralInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *GetBrandIntegralResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type BatSetBrandIntegralReq struct {
	Info                 []*BrandIntegralInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *BatSetBrandIntegralReq) Reset()         { *m = BatSetBrandIntegralReq{} }
func (m *BatSetBrandIntegralReq) String() string { return proto.CompactTextString(m) }
func (*BatSetBrandIntegralReq) ProtoMessage()    {}
func (*BatSetBrandIntegralReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{68}
}
func (m *BatSetBrandIntegralReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatSetBrandIntegralReq.Unmarshal(m, b)
}
func (m *BatSetBrandIntegralReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatSetBrandIntegralReq.Marshal(b, m, deterministic)
}
func (dst *BatSetBrandIntegralReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatSetBrandIntegralReq.Merge(dst, src)
}
func (m *BatSetBrandIntegralReq) XXX_Size() int {
	return xxx_messageInfo_BatSetBrandIntegralReq.Size(m)
}
func (m *BatSetBrandIntegralReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatSetBrandIntegralReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatSetBrandIntegralReq proto.InternalMessageInfo

func (m *BatSetBrandIntegralReq) GetInfo() []*BrandIntegralInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type BatSetBrandIntegralResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatSetBrandIntegralResp) Reset()         { *m = BatSetBrandIntegralResp{} }
func (m *BatSetBrandIntegralResp) String() string { return proto.CompactTextString(m) }
func (*BatSetBrandIntegralResp) ProtoMessage()    {}
func (*BatSetBrandIntegralResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{69}
}
func (m *BatSetBrandIntegralResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatSetBrandIntegralResp.Unmarshal(m, b)
}
func (m *BatSetBrandIntegralResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatSetBrandIntegralResp.Marshal(b, m, deterministic)
}
func (dst *BatSetBrandIntegralResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatSetBrandIntegralResp.Merge(dst, src)
}
func (m *BatSetBrandIntegralResp) XXX_Size() int {
	return xxx_messageInfo_BatSetBrandIntegralResp.Size(m)
}
func (m *BatSetBrandIntegralResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatSetBrandIntegralResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatSetBrandIntegralResp proto.InternalMessageInfo

type DelBrandIntegralReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelBrandIntegralReq) Reset()         { *m = DelBrandIntegralReq{} }
func (m *DelBrandIntegralReq) String() string { return proto.CompactTextString(m) }
func (*DelBrandIntegralReq) ProtoMessage()    {}
func (*DelBrandIntegralReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{70}
}
func (m *DelBrandIntegralReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelBrandIntegralReq.Unmarshal(m, b)
}
func (m *DelBrandIntegralReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelBrandIntegralReq.Marshal(b, m, deterministic)
}
func (dst *DelBrandIntegralReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelBrandIntegralReq.Merge(dst, src)
}
func (m *DelBrandIntegralReq) XXX_Size() int {
	return xxx_messageInfo_DelBrandIntegralReq.Size(m)
}
func (m *DelBrandIntegralReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelBrandIntegralReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelBrandIntegralReq proto.InternalMessageInfo

func (m *DelBrandIntegralReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type DelBrandIntegralResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelBrandIntegralResp) Reset()         { *m = DelBrandIntegralResp{} }
func (m *DelBrandIntegralResp) String() string { return proto.CompactTextString(m) }
func (*DelBrandIntegralResp) ProtoMessage()    {}
func (*DelBrandIntegralResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{71}
}
func (m *DelBrandIntegralResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelBrandIntegralResp.Unmarshal(m, b)
}
func (m *DelBrandIntegralResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelBrandIntegralResp.Marshal(b, m, deterministic)
}
func (dst *DelBrandIntegralResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelBrandIntegralResp.Merge(dst, src)
}
func (m *DelBrandIntegralResp) XXX_Size() int {
	return xxx_messageInfo_DelBrandIntegralResp.Size(m)
}
func (m *DelBrandIntegralResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelBrandIntegralResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelBrandIntegralResp proto.InternalMessageInfo

// 认证标
type SetSignCountReq struct {
	Info                 *SetSignCountInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SetSignCountReq) Reset()         { *m = SetSignCountReq{} }
func (m *SetSignCountReq) String() string { return proto.CompactTextString(m) }
func (*SetSignCountReq) ProtoMessage()    {}
func (*SetSignCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{72}
}
func (m *SetSignCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetSignCountReq.Unmarshal(m, b)
}
func (m *SetSignCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetSignCountReq.Marshal(b, m, deterministic)
}
func (dst *SetSignCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetSignCountReq.Merge(dst, src)
}
func (m *SetSignCountReq) XXX_Size() int {
	return xxx_messageInfo_SetSignCountReq.Size(m)
}
func (m *SetSignCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetSignCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetSignCountReq proto.InternalMessageInfo

func (m *SetSignCountReq) GetInfo() *SetSignCountInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type SetSignCountInfo struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	BrandId              string   `protobuf:"bytes,2,opt,name=brand_id,json=brandId,proto3" json:"brand_id,omitempty"`
	BrandName            string   `protobuf:"bytes,3,opt,name=brand_name,json=brandName,proto3" json:"brand_name,omitempty"`
	Count                int32    `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	Intro                string   `protobuf:"bytes,5,opt,name=intro,proto3" json:"intro,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,6,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	Operator             string   `protobuf:"bytes,7,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetSignCountInfo) Reset()         { *m = SetSignCountInfo{} }
func (m *SetSignCountInfo) String() string { return proto.CompactTextString(m) }
func (*SetSignCountInfo) ProtoMessage()    {}
func (*SetSignCountInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{73}
}
func (m *SetSignCountInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetSignCountInfo.Unmarshal(m, b)
}
func (m *SetSignCountInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetSignCountInfo.Marshal(b, m, deterministic)
}
func (dst *SetSignCountInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetSignCountInfo.Merge(dst, src)
}
func (m *SetSignCountInfo) XXX_Size() int {
	return xxx_messageInfo_SetSignCountInfo.Size(m)
}
func (m *SetSignCountInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SetSignCountInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SetSignCountInfo proto.InternalMessageInfo

func (m *SetSignCountInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *SetSignCountInfo) GetBrandId() string {
	if m != nil {
		return m.BrandId
	}
	return ""
}

func (m *SetSignCountInfo) GetBrandName() string {
	if m != nil {
		return m.BrandName
	}
	return ""
}

func (m *SetSignCountInfo) GetCount() int32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *SetSignCountInfo) GetIntro() string {
	if m != nil {
		return m.Intro
	}
	return ""
}

func (m *SetSignCountInfo) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *SetSignCountInfo) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type SetSignCountResp struct {
	Info                 *SetSignCountInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SetSignCountResp) Reset()         { *m = SetSignCountResp{} }
func (m *SetSignCountResp) String() string { return proto.CompactTextString(m) }
func (*SetSignCountResp) ProtoMessage()    {}
func (*SetSignCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{74}
}
func (m *SetSignCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetSignCountResp.Unmarshal(m, b)
}
func (m *SetSignCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetSignCountResp.Marshal(b, m, deterministic)
}
func (dst *SetSignCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetSignCountResp.Merge(dst, src)
}
func (m *SetSignCountResp) XXX_Size() int {
	return xxx_messageInfo_SetSignCountResp.Size(m)
}
func (m *SetSignCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetSignCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetSignCountResp proto.InternalMessageInfo

func (m *SetSignCountResp) GetInfo() *SetSignCountInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type BatSetSignCountReq struct {
	Info                 []*SetSignCountInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *BatSetSignCountReq) Reset()         { *m = BatSetSignCountReq{} }
func (m *BatSetSignCountReq) String() string { return proto.CompactTextString(m) }
func (*BatSetSignCountReq) ProtoMessage()    {}
func (*BatSetSignCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{75}
}
func (m *BatSetSignCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatSetSignCountReq.Unmarshal(m, b)
}
func (m *BatSetSignCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatSetSignCountReq.Marshal(b, m, deterministic)
}
func (dst *BatSetSignCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatSetSignCountReq.Merge(dst, src)
}
func (m *BatSetSignCountReq) XXX_Size() int {
	return xxx_messageInfo_BatSetSignCountReq.Size(m)
}
func (m *BatSetSignCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatSetSignCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatSetSignCountReq proto.InternalMessageInfo

func (m *BatSetSignCountReq) GetInfo() []*SetSignCountInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type BatSetSignCountResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatSetSignCountResp) Reset()         { *m = BatSetSignCountResp{} }
func (m *BatSetSignCountResp) String() string { return proto.CompactTextString(m) }
func (*BatSetSignCountResp) ProtoMessage()    {}
func (*BatSetSignCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{76}
}
func (m *BatSetSignCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatSetSignCountResp.Unmarshal(m, b)
}
func (m *BatSetSignCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatSetSignCountResp.Marshal(b, m, deterministic)
}
func (dst *BatSetSignCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatSetSignCountResp.Merge(dst, src)
}
func (m *BatSetSignCountResp) XXX_Size() int {
	return xxx_messageInfo_BatSetSignCountResp.Size(m)
}
func (m *BatSetSignCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatSetSignCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatSetSignCountResp proto.InternalMessageInfo

type DelSignCountReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelSignCountReq) Reset()         { *m = DelSignCountReq{} }
func (m *DelSignCountReq) String() string { return proto.CompactTextString(m) }
func (*DelSignCountReq) ProtoMessage()    {}
func (*DelSignCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{77}
}
func (m *DelSignCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelSignCountReq.Unmarshal(m, b)
}
func (m *DelSignCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelSignCountReq.Marshal(b, m, deterministic)
}
func (dst *DelSignCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelSignCountReq.Merge(dst, src)
}
func (m *DelSignCountReq) XXX_Size() int {
	return xxx_messageInfo_DelSignCountReq.Size(m)
}
func (m *DelSignCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelSignCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelSignCountReq proto.InternalMessageInfo

func (m *DelSignCountReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type DelSignCountResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelSignCountResp) Reset()         { *m = DelSignCountResp{} }
func (m *DelSignCountResp) String() string { return proto.CompactTextString(m) }
func (*DelSignCountResp) ProtoMessage()    {}
func (*DelSignCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{78}
}
func (m *DelSignCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelSignCountResp.Unmarshal(m, b)
}
func (m *DelSignCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelSignCountResp.Marshal(b, m, deterministic)
}
func (dst *DelSignCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelSignCountResp.Merge(dst, src)
}
func (m *DelSignCountResp) XXX_Size() int {
	return xxx_messageInfo_DelSignCountResp.Size(m)
}
func (m *DelSignCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelSignCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelSignCountResp proto.InternalMessageInfo

// 用户厂牌
type GetUserBrandInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserBrandInfoReq) Reset()         { *m = GetUserBrandInfoReq{} }
func (m *GetUserBrandInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserBrandInfoReq) ProtoMessage()    {}
func (*GetUserBrandInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{79}
}
func (m *GetUserBrandInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserBrandInfoReq.Unmarshal(m, b)
}
func (m *GetUserBrandInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserBrandInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserBrandInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserBrandInfoReq.Merge(dst, src)
}
func (m *GetUserBrandInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserBrandInfoReq.Size(m)
}
func (m *GetUserBrandInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserBrandInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserBrandInfoReq proto.InternalMessageInfo

func (m *GetUserBrandInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserBrandInfoResp struct {
	BrandId              string   `protobuf:"bytes,1,opt,name=brand_id,json=brandId,proto3" json:"brand_id,omitempty"`
	BrandName            string   `protobuf:"bytes,2,opt,name=brand_name,json=brandName,proto3" json:"brand_name,omitempty"`
	BrandType            string   `protobuf:"bytes,3,opt,name=brand_type,json=brandType,proto3" json:"brand_type,omitempty"`
	Attr                 string   `protobuf:"bytes,4,opt,name=attr,proto3" json:"attr,omitempty"`
	AttrInfo             string   `protobuf:"bytes,5,opt,name=attr_info,json=attrInfo,proto3" json:"attr_info,omitempty"`
	BrandRole            string   `protobuf:"bytes,6,opt,name=brand_role,json=brandRole,proto3" json:"brand_role,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserBrandInfoResp) Reset()         { *m = GetUserBrandInfoResp{} }
func (m *GetUserBrandInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserBrandInfoResp) ProtoMessage()    {}
func (*GetUserBrandInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{80}
}
func (m *GetUserBrandInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserBrandInfoResp.Unmarshal(m, b)
}
func (m *GetUserBrandInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserBrandInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserBrandInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserBrandInfoResp.Merge(dst, src)
}
func (m *GetUserBrandInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserBrandInfoResp.Size(m)
}
func (m *GetUserBrandInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserBrandInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserBrandInfoResp proto.InternalMessageInfo

func (m *GetUserBrandInfoResp) GetBrandId() string {
	if m != nil {
		return m.BrandId
	}
	return ""
}

func (m *GetUserBrandInfoResp) GetBrandName() string {
	if m != nil {
		return m.BrandName
	}
	return ""
}

func (m *GetUserBrandInfoResp) GetBrandType() string {
	if m != nil {
		return m.BrandType
	}
	return ""
}

func (m *GetUserBrandInfoResp) GetAttr() string {
	if m != nil {
		return m.Attr
	}
	return ""
}

func (m *GetUserBrandInfoResp) GetAttrInfo() string {
	if m != nil {
		return m.AttrInfo
	}
	return ""
}

func (m *GetUserBrandInfoResp) GetBrandRole() string {
	if m != nil {
		return m.BrandRole
	}
	return ""
}

type GetSignCountReq struct {
	BrandId              string   `protobuf:"bytes,1,opt,name=brand_id,json=brandId,proto3" json:"brand_id,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Offset               uint32   `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSignCountReq) Reset()         { *m = GetSignCountReq{} }
func (m *GetSignCountReq) String() string { return proto.CompactTextString(m) }
func (*GetSignCountReq) ProtoMessage()    {}
func (*GetSignCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{81}
}
func (m *GetSignCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSignCountReq.Unmarshal(m, b)
}
func (m *GetSignCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSignCountReq.Marshal(b, m, deterministic)
}
func (dst *GetSignCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSignCountReq.Merge(dst, src)
}
func (m *GetSignCountReq) XXX_Size() int {
	return xxx_messageInfo_GetSignCountReq.Size(m)
}
func (m *GetSignCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSignCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSignCountReq proto.InternalMessageInfo

func (m *GetSignCountReq) GetBrandId() string {
	if m != nil {
		return m.BrandId
	}
	return ""
}

func (m *GetSignCountReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetSignCountReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

type GetSignCountResp struct {
	Info                 []*SetSignCountInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	Total                uint32              `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetSignCountResp) Reset()         { *m = GetSignCountResp{} }
func (m *GetSignCountResp) String() string { return proto.CompactTextString(m) }
func (*GetSignCountResp) ProtoMessage()    {}
func (*GetSignCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{82}
}
func (m *GetSignCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSignCountResp.Unmarshal(m, b)
}
func (m *GetSignCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSignCountResp.Marshal(b, m, deterministic)
}
func (dst *GetSignCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSignCountResp.Merge(dst, src)
}
func (m *GetSignCountResp) XXX_Size() int {
	return xxx_messageInfo_GetSignCountResp.Size(m)
}
func (m *GetSignCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSignCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSignCountResp proto.InternalMessageInfo

func (m *GetSignCountResp) GetInfo() []*SetSignCountInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *GetSignCountResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type BrandTopicPostInfo struct {
	Id                   string             `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Ttid                 string             `protobuf:"bytes,2,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Nickname             string             `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	BrandList            []*SimpleBrandInfo `protobuf:"bytes,4,rep,name=brand_list,json=brandList,proto3" json:"brand_list,omitempty"`
	PostId               string             `protobuf:"bytes,5,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	PostTopics           []string           `protobuf:"bytes,6,rep,name=post_topics,json=postTopics,proto3" json:"post_topics,omitempty"`
	ReleationTopic       []string           `protobuf:"bytes,7,rep,name=releation_topic,json=releationTopic,proto3" json:"releation_topic,omitempty"`
	UserStatus           int32              `protobuf:"varint,8,opt,name=user_status,json=userStatus,proto3" json:"user_status,omitempty"`
	PostStatus           int32              `protobuf:"varint,9,opt,name=post_status,json=postStatus,proto3" json:"post_status,omitempty"`
	PostContent          string             `protobuf:"bytes,10,opt,name=post_content,json=postContent,proto3" json:"post_content,omitempty"`
	CreateTime           int64              `protobuf:"varint,11,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	PointsStatus         int32              `protobuf:"varint,12,opt,name=points_status,json=pointsStatus,proto3" json:"points_status,omitempty"`
	GrantBrandId         *SimpleBrandInfo   `protobuf:"bytes,13,opt,name=grant_brand_id,json=grantBrandId,proto3" json:"grant_brand_id,omitempty"`
	GrantBrandPoints     int32              `protobuf:"varint,14,opt,name=grant_brand_points,json=grantBrandPoints,proto3" json:"grant_brand_points,omitempty"`
	Attachments          []*AttachmentInfo  `protobuf:"bytes,15,rep,name=attachments,proto3" json:"attachments,omitempty"`
	PrivacyPolicy        int32              `protobuf:"varint,16,opt,name=privacy_policy,json=privacyPolicy,proto3" json:"privacy_policy,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *BrandTopicPostInfo) Reset()         { *m = BrandTopicPostInfo{} }
func (m *BrandTopicPostInfo) String() string { return proto.CompactTextString(m) }
func (*BrandTopicPostInfo) ProtoMessage()    {}
func (*BrandTopicPostInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{83}
}
func (m *BrandTopicPostInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BrandTopicPostInfo.Unmarshal(m, b)
}
func (m *BrandTopicPostInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BrandTopicPostInfo.Marshal(b, m, deterministic)
}
func (dst *BrandTopicPostInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BrandTopicPostInfo.Merge(dst, src)
}
func (m *BrandTopicPostInfo) XXX_Size() int {
	return xxx_messageInfo_BrandTopicPostInfo.Size(m)
}
func (m *BrandTopicPostInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BrandTopicPostInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BrandTopicPostInfo proto.InternalMessageInfo

func (m *BrandTopicPostInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *BrandTopicPostInfo) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *BrandTopicPostInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *BrandTopicPostInfo) GetBrandList() []*SimpleBrandInfo {
	if m != nil {
		return m.BrandList
	}
	return nil
}

func (m *BrandTopicPostInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *BrandTopicPostInfo) GetPostTopics() []string {
	if m != nil {
		return m.PostTopics
	}
	return nil
}

func (m *BrandTopicPostInfo) GetReleationTopic() []string {
	if m != nil {
		return m.ReleationTopic
	}
	return nil
}

func (m *BrandTopicPostInfo) GetUserStatus() int32 {
	if m != nil {
		return m.UserStatus
	}
	return 0
}

func (m *BrandTopicPostInfo) GetPostStatus() int32 {
	if m != nil {
		return m.PostStatus
	}
	return 0
}

func (m *BrandTopicPostInfo) GetPostContent() string {
	if m != nil {
		return m.PostContent
	}
	return ""
}

func (m *BrandTopicPostInfo) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *BrandTopicPostInfo) GetPointsStatus() int32 {
	if m != nil {
		return m.PointsStatus
	}
	return 0
}

func (m *BrandTopicPostInfo) GetGrantBrandId() *SimpleBrandInfo {
	if m != nil {
		return m.GrantBrandId
	}
	return nil
}

func (m *BrandTopicPostInfo) GetGrantBrandPoints() int32 {
	if m != nil {
		return m.GrantBrandPoints
	}
	return 0
}

func (m *BrandTopicPostInfo) GetAttachments() []*AttachmentInfo {
	if m != nil {
		return m.Attachments
	}
	return nil
}

func (m *BrandTopicPostInfo) GetPrivacyPolicy() int32 {
	if m != nil {
		return m.PrivacyPolicy
	}
	return 0
}

type UpdateBrandTopicPostPointsReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	BrandId              string   `protobuf:"bytes,2,opt,name=brand_id,json=brandId,proto3" json:"brand_id,omitempty"`
	BrandPoints          int32    `protobuf:"varint,3,opt,name=brand_points,json=brandPoints,proto3" json:"brand_points,omitempty"`
	Operate              string   `protobuf:"bytes,4,opt,name=operate,proto3" json:"operate,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateBrandTopicPostPointsReq) Reset()         { *m = UpdateBrandTopicPostPointsReq{} }
func (m *UpdateBrandTopicPostPointsReq) String() string { return proto.CompactTextString(m) }
func (*UpdateBrandTopicPostPointsReq) ProtoMessage()    {}
func (*UpdateBrandTopicPostPointsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{84}
}
func (m *UpdateBrandTopicPostPointsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateBrandTopicPostPointsReq.Unmarshal(m, b)
}
func (m *UpdateBrandTopicPostPointsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateBrandTopicPostPointsReq.Marshal(b, m, deterministic)
}
func (dst *UpdateBrandTopicPostPointsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateBrandTopicPostPointsReq.Merge(dst, src)
}
func (m *UpdateBrandTopicPostPointsReq) XXX_Size() int {
	return xxx_messageInfo_UpdateBrandTopicPostPointsReq.Size(m)
}
func (m *UpdateBrandTopicPostPointsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateBrandTopicPostPointsReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateBrandTopicPostPointsReq proto.InternalMessageInfo

func (m *UpdateBrandTopicPostPointsReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *UpdateBrandTopicPostPointsReq) GetBrandId() string {
	if m != nil {
		return m.BrandId
	}
	return ""
}

func (m *UpdateBrandTopicPostPointsReq) GetBrandPoints() int32 {
	if m != nil {
		return m.BrandPoints
	}
	return 0
}

func (m *UpdateBrandTopicPostPointsReq) GetOperate() string {
	if m != nil {
		return m.Operate
	}
	return ""
}

type UpdateBrandTopicPostPointsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateBrandTopicPostPointsResp) Reset()         { *m = UpdateBrandTopicPostPointsResp{} }
func (m *UpdateBrandTopicPostPointsResp) String() string { return proto.CompactTextString(m) }
func (*UpdateBrandTopicPostPointsResp) ProtoMessage()    {}
func (*UpdateBrandTopicPostPointsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{85}
}
func (m *UpdateBrandTopicPostPointsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateBrandTopicPostPointsResp.Unmarshal(m, b)
}
func (m *UpdateBrandTopicPostPointsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateBrandTopicPostPointsResp.Marshal(b, m, deterministic)
}
func (dst *UpdateBrandTopicPostPointsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateBrandTopicPostPointsResp.Merge(dst, src)
}
func (m *UpdateBrandTopicPostPointsResp) XXX_Size() int {
	return xxx_messageInfo_UpdateBrandTopicPostPointsResp.Size(m)
}
func (m *UpdateBrandTopicPostPointsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateBrandTopicPostPointsResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateBrandTopicPostPointsResp proto.InternalMessageInfo

type GetBrandTopicPostReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BrandId              string   `protobuf:"bytes,2,opt,name=brand_id,json=brandId,proto3" json:"brand_id,omitempty"`
	CreateBegin          uint32   `protobuf:"varint,3,opt,name=create_begin,json=createBegin,proto3" json:"create_begin,omitempty"`
	CreateEnd            uint32   `protobuf:"varint,4,opt,name=create_end,json=createEnd,proto3" json:"create_end,omitempty"`
	PointsStatus         int32    `protobuf:"varint,5,opt,name=points_status,json=pointsStatus,proto3" json:"points_status,omitempty"`
	Offset               uint32   `protobuf:"varint,6,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,7,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBrandTopicPostReq) Reset()         { *m = GetBrandTopicPostReq{} }
func (m *GetBrandTopicPostReq) String() string { return proto.CompactTextString(m) }
func (*GetBrandTopicPostReq) ProtoMessage()    {}
func (*GetBrandTopicPostReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{86}
}
func (m *GetBrandTopicPostReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBrandTopicPostReq.Unmarshal(m, b)
}
func (m *GetBrandTopicPostReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBrandTopicPostReq.Marshal(b, m, deterministic)
}
func (dst *GetBrandTopicPostReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBrandTopicPostReq.Merge(dst, src)
}
func (m *GetBrandTopicPostReq) XXX_Size() int {
	return xxx_messageInfo_GetBrandTopicPostReq.Size(m)
}
func (m *GetBrandTopicPostReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBrandTopicPostReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBrandTopicPostReq proto.InternalMessageInfo

func (m *GetBrandTopicPostReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetBrandTopicPostReq) GetBrandId() string {
	if m != nil {
		return m.BrandId
	}
	return ""
}

func (m *GetBrandTopicPostReq) GetCreateBegin() uint32 {
	if m != nil {
		return m.CreateBegin
	}
	return 0
}

func (m *GetBrandTopicPostReq) GetCreateEnd() uint32 {
	if m != nil {
		return m.CreateEnd
	}
	return 0
}

func (m *GetBrandTopicPostReq) GetPointsStatus() int32 {
	if m != nil {
		return m.PointsStatus
	}
	return 0
}

func (m *GetBrandTopicPostReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetBrandTopicPostReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetBrandTopicPostResp struct {
	Info                 []*BrandTopicPostInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	Total                int32                 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetBrandTopicPostResp) Reset()         { *m = GetBrandTopicPostResp{} }
func (m *GetBrandTopicPostResp) String() string { return proto.CompactTextString(m) }
func (*GetBrandTopicPostResp) ProtoMessage()    {}
func (*GetBrandTopicPostResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{87}
}
func (m *GetBrandTopicPostResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBrandTopicPostResp.Unmarshal(m, b)
}
func (m *GetBrandTopicPostResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBrandTopicPostResp.Marshal(b, m, deterministic)
}
func (dst *GetBrandTopicPostResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBrandTopicPostResp.Merge(dst, src)
}
func (m *GetBrandTopicPostResp) XXX_Size() int {
	return xxx_messageInfo_GetBrandTopicPostResp.Size(m)
}
func (m *GetBrandTopicPostResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBrandTopicPostResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBrandTopicPostResp proto.InternalMessageInfo

func (m *GetBrandTopicPostResp) GetInfo() []*BrandTopicPostInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *GetBrandTopicPostResp) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type AttachmentInfo struct {
	Key       string                        `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Type      AttachmentInfo_AttachmentType `protobuf:"varint,2,opt,name=type,proto3,enum=rhythm.brand.AttachmentInfo_AttachmentType" json:"type,omitempty"`
	Content   string                        `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	Extra     string                        `protobuf:"bytes,4,opt,name=extra,proto3" json:"extra,omitempty"`
	Status    ContentStatus                 `protobuf:"varint,5,opt,name=status,proto3,enum=rhythm.brand.ContentStatus" json:"status,omitempty"`
	VmContent string                        `protobuf:"bytes,6,opt,name=vm_content,json=vmContent,proto3" json:"vm_content,omitempty"`
	// 给type == video用的, 转码参数
	Param string `protobuf:"bytes,10,opt,name=param,proto3" json:"param,omitempty"`
	// 原始视频封面url
	OriginVideoCover     string   `protobuf:"bytes,11,opt,name=origin_video_cover,json=originVideoCover,proto3" json:"origin_video_cover,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AttachmentInfo) Reset()         { *m = AttachmentInfo{} }
func (m *AttachmentInfo) String() string { return proto.CompactTextString(m) }
func (*AttachmentInfo) ProtoMessage()    {}
func (*AttachmentInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{88}
}
func (m *AttachmentInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AttachmentInfo.Unmarshal(m, b)
}
func (m *AttachmentInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AttachmentInfo.Marshal(b, m, deterministic)
}
func (dst *AttachmentInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AttachmentInfo.Merge(dst, src)
}
func (m *AttachmentInfo) XXX_Size() int {
	return xxx_messageInfo_AttachmentInfo.Size(m)
}
func (m *AttachmentInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AttachmentInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AttachmentInfo proto.InternalMessageInfo

func (m *AttachmentInfo) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *AttachmentInfo) GetType() AttachmentInfo_AttachmentType {
	if m != nil {
		return m.Type
	}
	return AttachmentInfo_NONE
}

func (m *AttachmentInfo) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *AttachmentInfo) GetExtra() string {
	if m != nil {
		return m.Extra
	}
	return ""
}

func (m *AttachmentInfo) GetStatus() ContentStatus {
	if m != nil {
		return m.Status
	}
	return ContentStatus_CONTENT_STATUS_NONE
}

func (m *AttachmentInfo) GetVmContent() string {
	if m != nil {
		return m.VmContent
	}
	return ""
}

func (m *AttachmentInfo) GetParam() string {
	if m != nil {
		return m.Param
	}
	return ""
}

func (m *AttachmentInfo) GetOriginVideoCover() string {
	if m != nil {
		return m.OriginVideoCover
	}
	return ""
}

type GetMatchBrandReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMatchBrandReq) Reset()         { *m = GetMatchBrandReq{} }
func (m *GetMatchBrandReq) String() string { return proto.CompactTextString(m) }
func (*GetMatchBrandReq) ProtoMessage()    {}
func (*GetMatchBrandReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{89}
}
func (m *GetMatchBrandReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMatchBrandReq.Unmarshal(m, b)
}
func (m *GetMatchBrandReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMatchBrandReq.Marshal(b, m, deterministic)
}
func (dst *GetMatchBrandReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMatchBrandReq.Merge(dst, src)
}
func (m *GetMatchBrandReq) XXX_Size() int {
	return xxx_messageInfo_GetMatchBrandReq.Size(m)
}
func (m *GetMatchBrandReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMatchBrandReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMatchBrandReq proto.InternalMessageInfo

func (m *GetMatchBrandReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type GetMatchBrandResp struct {
	BrandInfo            []*BrandIdNameInfo `protobuf:"bytes,1,rep,name=brandInfo,proto3" json:"brandInfo,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetMatchBrandResp) Reset()         { *m = GetMatchBrandResp{} }
func (m *GetMatchBrandResp) String() string { return proto.CompactTextString(m) }
func (*GetMatchBrandResp) ProtoMessage()    {}
func (*GetMatchBrandResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_brand_ca39bef01c84f7e1, []int{90}
}
func (m *GetMatchBrandResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMatchBrandResp.Unmarshal(m, b)
}
func (m *GetMatchBrandResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMatchBrandResp.Marshal(b, m, deterministic)
}
func (dst *GetMatchBrandResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMatchBrandResp.Merge(dst, src)
}
func (m *GetMatchBrandResp) XXX_Size() int {
	return xxx_messageInfo_GetMatchBrandResp.Size(m)
}
func (m *GetMatchBrandResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMatchBrandResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMatchBrandResp proto.InternalMessageInfo

func (m *GetMatchBrandResp) GetBrandInfo() []*BrandIdNameInfo {
	if m != nil {
		return m.BrandInfo
	}
	return nil
}

func init() {
	proto.RegisterType((*FansAuthenticationInfo)(nil), "rhythm.brand.FansAuthenticationInfo")
	proto.RegisterType((*GetFansAuthenticationReq)(nil), "rhythm.brand.GetFansAuthenticationReq")
	proto.RegisterType((*GetFansAuthenticationResp)(nil), "rhythm.brand.GetFansAuthenticationResp")
	proto.RegisterType((*SetBrandReq)(nil), "rhythm.brand.SetBrandReq")
	proto.RegisterType((*SetBrandResp)(nil), "rhythm.brand.SetBrandResp")
	proto.RegisterType((*GetBrandReq)(nil), "rhythm.brand.GetBrandReq")
	proto.RegisterType((*BrandInfo)(nil), "rhythm.brand.BrandInfo")
	proto.RegisterType((*GetBrandResp)(nil), "rhythm.brand.GetBrandResp")
	proto.RegisterType((*GetBrandByIdReq)(nil), "rhythm.brand.GetBrandByIdReq")
	proto.RegisterType((*GetBrandByIdResp)(nil), "rhythm.brand.GetBrandByIdResp")
	proto.RegisterType((*DelBrandReq)(nil), "rhythm.brand.DelBrandReq")
	proto.RegisterType((*DelBrandResp)(nil), "rhythm.brand.DelBrandResp")
	proto.RegisterType((*ListBrandReq)(nil), "rhythm.brand.ListBrandReq")
	proto.RegisterType((*SimpleBrandInfo)(nil), "rhythm.brand.SimpleBrandInfo")
	proto.RegisterType((*ListBrandResp)(nil), "rhythm.brand.ListBrandResp")
	proto.RegisterType((*BrandMemberInfo)(nil), "rhythm.brand.BrandMemberInfo")
	proto.RegisterType((*SetBrandMemberReq)(nil), "rhythm.brand.SetBrandMemberReq")
	proto.RegisterType((*SetBrandMemberResp)(nil), "rhythm.brand.SetBrandMemberResp")
	proto.RegisterType((*GetBrandMemberReq)(nil), "rhythm.brand.GetBrandMemberReq")
	proto.RegisterType((*GetBrandMemberResp)(nil), "rhythm.brand.GetBrandMemberResp")
	proto.RegisterType((*DelBrandMemberReq)(nil), "rhythm.brand.DelBrandMemberReq")
	proto.RegisterType((*DelBrandMemberResp)(nil), "rhythm.brand.DelBrandMemberResp")
	proto.RegisterType((*BatGetBrandMemberReq)(nil), "rhythm.brand.BatGetBrandMemberReq")
	proto.RegisterType((*MemberList)(nil), "rhythm.brand.MemberList")
	proto.RegisterType((*BatGetBrandMemberResp)(nil), "rhythm.brand.BatGetBrandMemberResp")
	proto.RegisterMapType((map[string]*MemberList)(nil), "rhythm.brand.BatGetBrandMemberResp.BrandMemberMapEntry")
	proto.RegisterType((*GetBrandMemberByUidsReq)(nil), "rhythm.brand.GetBrandMemberByUidsReq")
	proto.RegisterType((*GetBrandMemberByUidsResp)(nil), "rhythm.brand.GetBrandMemberByUidsResp")
	proto.RegisterType((*GetUserBrandTopicIDReq)(nil), "rhythm.brand.GetUserBrandTopicIDReq")
	proto.RegisterType((*GetUserBrandTopicIDResp)(nil), "rhythm.brand.GetUserBrandTopicIDResp")
	proto.RegisterType((*BatchBrandInfo)(nil), "rhythm.brand.BatchBrandInfo")
	proto.RegisterType((*BatchAddBrandReq)(nil), "rhythm.brand.BatchAddBrandReq")
	proto.RegisterType((*BatchAddBrandResp)(nil), "rhythm.brand.BatchAddBrandResp")
	proto.RegisterType((*BrandNameSearchReq)(nil), "rhythm.brand.BrandNameSearchReq")
	proto.RegisterType((*BrandIdNameInfo)(nil), "rhythm.brand.BrandIdNameInfo")
	proto.RegisterType((*BrandNameSearchResp)(nil), "rhythm.brand.BrandNameSearchResp")
	proto.RegisterType((*BatchSetMemberReq)(nil), "rhythm.brand.BatchSetMemberReq")
	proto.RegisterType((*BatchSetMemberInfo)(nil), "rhythm.brand.BatchSetMemberInfo")
	proto.RegisterType((*BatchSetMemberResp)(nil), "rhythm.brand.BatchSetMemberResp")
	proto.RegisterType((*BatchDelMemberInfo)(nil), "rhythm.brand.BatchDelMemberInfo")
	proto.RegisterType((*BatchDelMemberReq)(nil), "rhythm.brand.BatchDelMemberReq")
	proto.RegisterType((*BatchDelMemberResp)(nil), "rhythm.brand.BatchDelMemberResp")
	proto.RegisterType((*BrandType)(nil), "rhythm.brand.BrandType")
	proto.RegisterType((*SetBrandTypeReq)(nil), "rhythm.brand.SetBrandTypeReq")
	proto.RegisterType((*SetBrandTypeResp)(nil), "rhythm.brand.SetBrandTypeResp")
	proto.RegisterType((*DelBrandTypeReq)(nil), "rhythm.brand.DelBrandTypeReq")
	proto.RegisterType((*DelBrandTypeResp)(nil), "rhythm.brand.DelBrandTypeResp")
	proto.RegisterType((*GetBrandTypeFuzzySearchReq)(nil), "rhythm.brand.GetBrandTypeFuzzySearchReq")
	proto.RegisterType((*GetBrandTypeFuzzySearchResp)(nil), "rhythm.brand.GetBrandTypeFuzzySearchResp")
	proto.RegisterType((*GetBrandBaseInfoReq)(nil), "rhythm.brand.GetBrandBaseInfoReq")
	proto.RegisterType((*GetBrandBaseInfoResp)(nil), "rhythm.brand.GetBrandBaseInfoResp")
	proto.RegisterType((*GetBrandTypeByIdReq)(nil), "rhythm.brand.GetBrandTypeByIdReq")
	proto.RegisterType((*GetBrandTypeByIdResp)(nil), "rhythm.brand.GetBrandTypeByIdResp")
	proto.RegisterType((*SetBrandRoomReq)(nil), "rhythm.brand.SetBrandRoomReq")
	proto.RegisterType((*SetBrandRoomResp)(nil), "rhythm.brand.SetBrandRoomResp")
	proto.RegisterType((*BrandRoomInfo)(nil), "rhythm.brand.BrandRoomInfo")
	proto.RegisterType((*GetBrandRoomReq)(nil), "rhythm.brand.GetBrandRoomReq")
	proto.RegisterType((*GetBrandRoomResp)(nil), "rhythm.brand.GetBrandRoomResp")
	proto.RegisterType((*DelBrandRoomReq)(nil), "rhythm.brand.DelBrandRoomReq")
	proto.RegisterType((*DelBrandRoomResp)(nil), "rhythm.brand.DelBrandRoomResp")
	proto.RegisterType((*GetBrandIdByUidReq)(nil), "rhythm.brand.GetBrandIdByUidReq")
	proto.RegisterType((*GetBrandIdByUidResp)(nil), "rhythm.brand.GetBrandIdByUidResp")
	proto.RegisterType((*GetBrandTopicByIdReq)(nil), "rhythm.brand.GetBrandTopicByIdReq")
	proto.RegisterType((*GetBrandTopicByIdResp)(nil), "rhythm.brand.GetBrandTopicByIdResp")
	proto.RegisterType((*SetBrandIntegralReq)(nil), "rhythm.brand.SetBrandIntegralReq")
	proto.RegisterType((*SetBrandIntegralResp)(nil), "rhythm.brand.SetBrandIntegralResp")
	proto.RegisterType((*BrandIntegralInfo)(nil), "rhythm.brand.BrandIntegralInfo")
	proto.RegisterType((*GetBrandIntegralReq)(nil), "rhythm.brand.GetBrandIntegralReq")
	proto.RegisterType((*GetBrandIntegralResp)(nil), "rhythm.brand.GetBrandIntegralResp")
	proto.RegisterType((*BatSetBrandIntegralReq)(nil), "rhythm.brand.BatSetBrandIntegralReq")
	proto.RegisterType((*BatSetBrandIntegralResp)(nil), "rhythm.brand.BatSetBrandIntegralResp")
	proto.RegisterType((*DelBrandIntegralReq)(nil), "rhythm.brand.DelBrandIntegralReq")
	proto.RegisterType((*DelBrandIntegralResp)(nil), "rhythm.brand.DelBrandIntegralResp")
	proto.RegisterType((*SetSignCountReq)(nil), "rhythm.brand.SetSignCountReq")
	proto.RegisterType((*SetSignCountInfo)(nil), "rhythm.brand.SetSignCountInfo")
	proto.RegisterType((*SetSignCountResp)(nil), "rhythm.brand.SetSignCountResp")
	proto.RegisterType((*BatSetSignCountReq)(nil), "rhythm.brand.BatSetSignCountReq")
	proto.RegisterType((*BatSetSignCountResp)(nil), "rhythm.brand.BatSetSignCountResp")
	proto.RegisterType((*DelSignCountReq)(nil), "rhythm.brand.DelSignCountReq")
	proto.RegisterType((*DelSignCountResp)(nil), "rhythm.brand.DelSignCountResp")
	proto.RegisterType((*GetUserBrandInfoReq)(nil), "rhythm.brand.GetUserBrandInfoReq")
	proto.RegisterType((*GetUserBrandInfoResp)(nil), "rhythm.brand.GetUserBrandInfoResp")
	proto.RegisterType((*GetSignCountReq)(nil), "rhythm.brand.GetSignCountReq")
	proto.RegisterType((*GetSignCountResp)(nil), "rhythm.brand.GetSignCountResp")
	proto.RegisterType((*BrandTopicPostInfo)(nil), "rhythm.brand.brandTopicPostInfo")
	proto.RegisterType((*UpdateBrandTopicPostPointsReq)(nil), "rhythm.brand.UpdateBrandTopicPostPointsReq")
	proto.RegisterType((*UpdateBrandTopicPostPointsResp)(nil), "rhythm.brand.UpdateBrandTopicPostPointsResp")
	proto.RegisterType((*GetBrandTopicPostReq)(nil), "rhythm.brand.GetBrandTopicPostReq")
	proto.RegisterType((*GetBrandTopicPostResp)(nil), "rhythm.brand.GetBrandTopicPostResp")
	proto.RegisterType((*AttachmentInfo)(nil), "rhythm.brand.AttachmentInfo")
	proto.RegisterType((*GetMatchBrandReq)(nil), "rhythm.brand.GetMatchBrandReq")
	proto.RegisterType((*GetMatchBrandResp)(nil), "rhythm.brand.GetMatchBrandResp")
	proto.RegisterEnum("rhythm.brand.BrandMemberRole", BrandMemberRole_name, BrandMemberRole_value)
	proto.RegisterEnum("rhythm.brand.BrandStatus", BrandStatus_name, BrandStatus_value)
	proto.RegisterEnum("rhythm.brand.IntegralGrantStatus", IntegralGrantStatus_name, IntegralGrantStatus_value)
	proto.RegisterEnum("rhythm.brand.UserStatus", UserStatus_name, UserStatus_value)
	proto.RegisterEnum("rhythm.brand.ContentStatus", ContentStatus_name, ContentStatus_value)
	proto.RegisterEnum("rhythm.brand.AttachmentInfo_AttachmentType", AttachmentInfo_AttachmentType_name, AttachmentInfo_AttachmentType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// BrandClient is the client API for Brand service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type BrandClient interface {
	// 厂牌管理
	SetBrand(ctx context.Context, in *SetBrandReq, opts ...grpc.CallOption) (*SetBrandResp, error)
	// 分页获取厂牌
	GetBrand(ctx context.Context, in *GetBrandReq, opts ...grpc.CallOption) (*GetBrandResp, error)
	GetBrandById(ctx context.Context, in *GetBrandByIdReq, opts ...grpc.CallOption) (*GetBrandByIdResp, error)
	DelBrand(ctx context.Context, in *DelBrandReq, opts ...grpc.CallOption) (*DelBrandResp, error)
	// 获取全部厂牌信息
	ListBrand(ctx context.Context, in *ListBrandReq, opts ...grpc.CallOption) (*ListBrandResp, error)
	// 厂牌成员管理
	SetBrandMember(ctx context.Context, in *SetBrandMemberReq, opts ...grpc.CallOption) (*SetBrandMemberResp, error)
	GetBrandMember(ctx context.Context, in *GetBrandMemberReq, opts ...grpc.CallOption) (*GetBrandMemberResp, error)
	DelBrandMember(ctx context.Context, in *DelBrandMemberReq, opts ...grpc.CallOption) (*DelBrandMemberResp, error)
	BatGetBrandMember(ctx context.Context, in *BatGetBrandMemberReq, opts ...grpc.CallOption) (*BatGetBrandMemberResp, error)
	GetBrandMemberByUids(ctx context.Context, in *GetBrandMemberByUidsReq, opts ...grpc.CallOption) (*GetBrandMemberByUidsResp, error)
	// 查询用户是否有厂牌话题
	GetUserBrandTopicID(ctx context.Context, in *GetUserBrandTopicIDReq, opts ...grpc.CallOption) (*GetUserBrandTopicIDResp, error)
	// 批量添加厂牌（新增接口）
	BatchAddBrand(ctx context.Context, in *BatchAddBrandReq, opts ...grpc.CallOption) (*BatchAddBrandResp, error)
	// 厂牌名称模糊搜索（新增接口）
	// 厂牌名称模糊搜索（新增接口）
	BrandNameSearch(ctx context.Context, in *BrandNameSearchReq, opts ...grpc.CallOption) (*BrandNameSearchResp, error)
	// 批量添加厂牌成员（新增接口）
	BatchSetMember(ctx context.Context, in *BatchSetMemberReq, opts ...grpc.CallOption) (*BatchSetMemberResp, error)
	// 批量删除厂牌成员（新增接口）
	BatchDelMember(ctx context.Context, in *BatchDelMemberReq, opts ...grpc.CallOption) (*BatchDelMemberResp, error)
	// 添加/编辑厂牌权益
	SetBrandRoom(ctx context.Context, in *SetBrandRoomReq, opts ...grpc.CallOption) (*SetBrandRoomResp, error)
	// 获取厂牌权益
	GetBrandRoom(ctx context.Context, in *GetBrandRoomReq, opts ...grpc.CallOption) (*GetBrandRoomResp, error)
	// 删除厂牌权益
	DelBrandRoom(ctx context.Context, in *DelBrandRoomReq, opts ...grpc.CallOption) (*DelBrandRoomResp, error)
	// 根据uid获取厂牌brandId
	GetBrandIdByUid(ctx context.Context, in *GetBrandIdByUidReq, opts ...grpc.CallOption) (*GetBrandIdByUidResp, error)
	GetBrandTypeById(ctx context.Context, in *GetBrandTypeByIdReq, opts ...grpc.CallOption) (*GetBrandTypeByIdResp, error)
	GetBrandTopicById(ctx context.Context, in *GetBrandTopicByIdReq, opts ...grpc.CallOption) (*GetBrandTopicByIdResp, error)
	// 厂牌类型
	SetBrandType(ctx context.Context, in *SetBrandTypeReq, opts ...grpc.CallOption) (*SetBrandTypeResp, error)
	DelBrandType(ctx context.Context, in *DelBrandTypeReq, opts ...grpc.CallOption) (*DelBrandTypeResp, error)
	GetBrandTypeFuzzySearch(ctx context.Context, in *GetBrandTypeFuzzySearchReq, opts ...grpc.CallOption) (*GetBrandTypeFuzzySearchResp, error)
	GetBrandBaseInfo(ctx context.Context, in *GetBrandBaseInfoReq, opts ...grpc.CallOption) (*GetBrandBaseInfoResp, error)
	// 添加/编辑厂牌积分
	SetBrandIntegral(ctx context.Context, in *SetBrandIntegralReq, opts ...grpc.CallOption) (*SetBrandIntegralResp, error)
	// 获取厂牌积分
	GetBrandIntegral(ctx context.Context, in *GetBrandIntegralReq, opts ...grpc.CallOption) (*GetBrandIntegralResp, error)
	// 批量添加接口
	BatSetBrandIntegral(ctx context.Context, in *BatSetBrandIntegralReq, opts ...grpc.CallOption) (*BatSetBrandIntegralResp, error)
	// 删除积分流水
	DelBrandIntegral(ctx context.Context, in *DelBrandIntegralReq, opts ...grpc.CallOption) (*DelBrandIntegralResp, error)
	// 添加/编辑认证标
	SetSignCount(ctx context.Context, in *SetSignCountReq, opts ...grpc.CallOption) (*SetSignCountResp, error)
	// 批量添加
	BatSetSignCount(ctx context.Context, in *BatSetSignCountReq, opts ...grpc.CallOption) (*BatSetSignCountResp, error)
	// 删除认证
	DelSignCount(ctx context.Context, in *DelSignCountReq, opts ...grpc.CallOption) (*DelSignCountResp, error)
	// 获取认证
	GetSignCount(ctx context.Context, in *GetSignCountReq, opts ...grpc.CallOption) (*GetSignCountResp, error)
	GetUserBrandInfo(ctx context.Context, in *GetUserBrandInfoReq, opts ...grpc.CallOption) (*GetUserBrandInfoResp, error)
	// 获取粉丝团认证标
	GetFansAuthentication(ctx context.Context, in *GetFansAuthenticationReq, opts ...grpc.CallOption) (*GetFansAuthenticationResp, error)
	// 更新帖子积分
	UpdateBrandTopicPostPoints(ctx context.Context, in *UpdateBrandTopicPostPointsReq, opts ...grpc.CallOption) (*UpdateBrandTopicPostPointsResp, error)
	// 查询帖子(包含查询厂牌名匹配查询，话题ID，ttid，发放积分状态，根绝发布日期查询)
	GetBrandTopicPost(ctx context.Context, in *GetBrandTopicPostReq, opts ...grpc.CallOption) (*GetBrandTopicPostResp, error)
	// 获取ID对应的匹配厂牌
	GetMatchBrand(ctx context.Context, in *GetMatchBrandReq, opts ...grpc.CallOption) (*GetMatchBrandResp, error)
}

type brandClient struct {
	cc *grpc.ClientConn
}

func NewBrandClient(cc *grpc.ClientConn) BrandClient {
	return &brandClient{cc}
}

func (c *brandClient) SetBrand(ctx context.Context, in *SetBrandReq, opts ...grpc.CallOption) (*SetBrandResp, error) {
	out := new(SetBrandResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/SetBrand", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) GetBrand(ctx context.Context, in *GetBrandReq, opts ...grpc.CallOption) (*GetBrandResp, error) {
	out := new(GetBrandResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/GetBrand", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) GetBrandById(ctx context.Context, in *GetBrandByIdReq, opts ...grpc.CallOption) (*GetBrandByIdResp, error) {
	out := new(GetBrandByIdResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/GetBrandById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) DelBrand(ctx context.Context, in *DelBrandReq, opts ...grpc.CallOption) (*DelBrandResp, error) {
	out := new(DelBrandResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/DelBrand", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) ListBrand(ctx context.Context, in *ListBrandReq, opts ...grpc.CallOption) (*ListBrandResp, error) {
	out := new(ListBrandResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/ListBrand", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) SetBrandMember(ctx context.Context, in *SetBrandMemberReq, opts ...grpc.CallOption) (*SetBrandMemberResp, error) {
	out := new(SetBrandMemberResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/SetBrandMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) GetBrandMember(ctx context.Context, in *GetBrandMemberReq, opts ...grpc.CallOption) (*GetBrandMemberResp, error) {
	out := new(GetBrandMemberResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/GetBrandMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) DelBrandMember(ctx context.Context, in *DelBrandMemberReq, opts ...grpc.CallOption) (*DelBrandMemberResp, error) {
	out := new(DelBrandMemberResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/DelBrandMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) BatGetBrandMember(ctx context.Context, in *BatGetBrandMemberReq, opts ...grpc.CallOption) (*BatGetBrandMemberResp, error) {
	out := new(BatGetBrandMemberResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/BatGetBrandMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) GetBrandMemberByUids(ctx context.Context, in *GetBrandMemberByUidsReq, opts ...grpc.CallOption) (*GetBrandMemberByUidsResp, error) {
	out := new(GetBrandMemberByUidsResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/GetBrandMemberByUids", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) GetUserBrandTopicID(ctx context.Context, in *GetUserBrandTopicIDReq, opts ...grpc.CallOption) (*GetUserBrandTopicIDResp, error) {
	out := new(GetUserBrandTopicIDResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/GetUserBrandTopicID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) BatchAddBrand(ctx context.Context, in *BatchAddBrandReq, opts ...grpc.CallOption) (*BatchAddBrandResp, error) {
	out := new(BatchAddBrandResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/BatchAddBrand", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) BrandNameSearch(ctx context.Context, in *BrandNameSearchReq, opts ...grpc.CallOption) (*BrandNameSearchResp, error) {
	out := new(BrandNameSearchResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/BrandNameSearch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) BatchSetMember(ctx context.Context, in *BatchSetMemberReq, opts ...grpc.CallOption) (*BatchSetMemberResp, error) {
	out := new(BatchSetMemberResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/BatchSetMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) BatchDelMember(ctx context.Context, in *BatchDelMemberReq, opts ...grpc.CallOption) (*BatchDelMemberResp, error) {
	out := new(BatchDelMemberResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/BatchDelMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) SetBrandRoom(ctx context.Context, in *SetBrandRoomReq, opts ...grpc.CallOption) (*SetBrandRoomResp, error) {
	out := new(SetBrandRoomResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/SetBrandRoom", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) GetBrandRoom(ctx context.Context, in *GetBrandRoomReq, opts ...grpc.CallOption) (*GetBrandRoomResp, error) {
	out := new(GetBrandRoomResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/GetBrandRoom", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) DelBrandRoom(ctx context.Context, in *DelBrandRoomReq, opts ...grpc.CallOption) (*DelBrandRoomResp, error) {
	out := new(DelBrandRoomResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/DelBrandRoom", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) GetBrandIdByUid(ctx context.Context, in *GetBrandIdByUidReq, opts ...grpc.CallOption) (*GetBrandIdByUidResp, error) {
	out := new(GetBrandIdByUidResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/GetBrandIdByUid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) GetBrandTypeById(ctx context.Context, in *GetBrandTypeByIdReq, opts ...grpc.CallOption) (*GetBrandTypeByIdResp, error) {
	out := new(GetBrandTypeByIdResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/GetBrandTypeById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) GetBrandTopicById(ctx context.Context, in *GetBrandTopicByIdReq, opts ...grpc.CallOption) (*GetBrandTopicByIdResp, error) {
	out := new(GetBrandTopicByIdResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/GetBrandTopicById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) SetBrandType(ctx context.Context, in *SetBrandTypeReq, opts ...grpc.CallOption) (*SetBrandTypeResp, error) {
	out := new(SetBrandTypeResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/SetBrandType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) DelBrandType(ctx context.Context, in *DelBrandTypeReq, opts ...grpc.CallOption) (*DelBrandTypeResp, error) {
	out := new(DelBrandTypeResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/DelBrandType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) GetBrandTypeFuzzySearch(ctx context.Context, in *GetBrandTypeFuzzySearchReq, opts ...grpc.CallOption) (*GetBrandTypeFuzzySearchResp, error) {
	out := new(GetBrandTypeFuzzySearchResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/GetBrandTypeFuzzySearch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) GetBrandBaseInfo(ctx context.Context, in *GetBrandBaseInfoReq, opts ...grpc.CallOption) (*GetBrandBaseInfoResp, error) {
	out := new(GetBrandBaseInfoResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/GetBrandBaseInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) SetBrandIntegral(ctx context.Context, in *SetBrandIntegralReq, opts ...grpc.CallOption) (*SetBrandIntegralResp, error) {
	out := new(SetBrandIntegralResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/SetBrandIntegral", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) GetBrandIntegral(ctx context.Context, in *GetBrandIntegralReq, opts ...grpc.CallOption) (*GetBrandIntegralResp, error) {
	out := new(GetBrandIntegralResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/GetBrandIntegral", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) BatSetBrandIntegral(ctx context.Context, in *BatSetBrandIntegralReq, opts ...grpc.CallOption) (*BatSetBrandIntegralResp, error) {
	out := new(BatSetBrandIntegralResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/BatSetBrandIntegral", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) DelBrandIntegral(ctx context.Context, in *DelBrandIntegralReq, opts ...grpc.CallOption) (*DelBrandIntegralResp, error) {
	out := new(DelBrandIntegralResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/DelBrandIntegral", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) SetSignCount(ctx context.Context, in *SetSignCountReq, opts ...grpc.CallOption) (*SetSignCountResp, error) {
	out := new(SetSignCountResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/SetSignCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) BatSetSignCount(ctx context.Context, in *BatSetSignCountReq, opts ...grpc.CallOption) (*BatSetSignCountResp, error) {
	out := new(BatSetSignCountResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/BatSetSignCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) DelSignCount(ctx context.Context, in *DelSignCountReq, opts ...grpc.CallOption) (*DelSignCountResp, error) {
	out := new(DelSignCountResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/DelSignCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) GetSignCount(ctx context.Context, in *GetSignCountReq, opts ...grpc.CallOption) (*GetSignCountResp, error) {
	out := new(GetSignCountResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/GetSignCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) GetUserBrandInfo(ctx context.Context, in *GetUserBrandInfoReq, opts ...grpc.CallOption) (*GetUserBrandInfoResp, error) {
	out := new(GetUserBrandInfoResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/GetUserBrandInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) GetFansAuthentication(ctx context.Context, in *GetFansAuthenticationReq, opts ...grpc.CallOption) (*GetFansAuthenticationResp, error) {
	out := new(GetFansAuthenticationResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/GetFansAuthentication", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) UpdateBrandTopicPostPoints(ctx context.Context, in *UpdateBrandTopicPostPointsReq, opts ...grpc.CallOption) (*UpdateBrandTopicPostPointsResp, error) {
	out := new(UpdateBrandTopicPostPointsResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/UpdateBrandTopicPostPoints", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) GetBrandTopicPost(ctx context.Context, in *GetBrandTopicPostReq, opts ...grpc.CallOption) (*GetBrandTopicPostResp, error) {
	out := new(GetBrandTopicPostResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/GetBrandTopicPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *brandClient) GetMatchBrand(ctx context.Context, in *GetMatchBrandReq, opts ...grpc.CallOption) (*GetMatchBrandResp, error) {
	out := new(GetMatchBrandResp)
	err := c.cc.Invoke(ctx, "/rhythm.brand.Brand/GetMatchBrand", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BrandServer is the server API for Brand service.
type BrandServer interface {
	// 厂牌管理
	SetBrand(context.Context, *SetBrandReq) (*SetBrandResp, error)
	// 分页获取厂牌
	GetBrand(context.Context, *GetBrandReq) (*GetBrandResp, error)
	GetBrandById(context.Context, *GetBrandByIdReq) (*GetBrandByIdResp, error)
	DelBrand(context.Context, *DelBrandReq) (*DelBrandResp, error)
	// 获取全部厂牌信息
	ListBrand(context.Context, *ListBrandReq) (*ListBrandResp, error)
	// 厂牌成员管理
	SetBrandMember(context.Context, *SetBrandMemberReq) (*SetBrandMemberResp, error)
	GetBrandMember(context.Context, *GetBrandMemberReq) (*GetBrandMemberResp, error)
	DelBrandMember(context.Context, *DelBrandMemberReq) (*DelBrandMemberResp, error)
	BatGetBrandMember(context.Context, *BatGetBrandMemberReq) (*BatGetBrandMemberResp, error)
	GetBrandMemberByUids(context.Context, *GetBrandMemberByUidsReq) (*GetBrandMemberByUidsResp, error)
	// 查询用户是否有厂牌话题
	GetUserBrandTopicID(context.Context, *GetUserBrandTopicIDReq) (*GetUserBrandTopicIDResp, error)
	// 批量添加厂牌（新增接口）
	BatchAddBrand(context.Context, *BatchAddBrandReq) (*BatchAddBrandResp, error)
	// 厂牌名称模糊搜索（新增接口）
	// 厂牌名称模糊搜索（新增接口）
	BrandNameSearch(context.Context, *BrandNameSearchReq) (*BrandNameSearchResp, error)
	// 批量添加厂牌成员（新增接口）
	BatchSetMember(context.Context, *BatchSetMemberReq) (*BatchSetMemberResp, error)
	// 批量删除厂牌成员（新增接口）
	BatchDelMember(context.Context, *BatchDelMemberReq) (*BatchDelMemberResp, error)
	// 添加/编辑厂牌权益
	SetBrandRoom(context.Context, *SetBrandRoomReq) (*SetBrandRoomResp, error)
	// 获取厂牌权益
	GetBrandRoom(context.Context, *GetBrandRoomReq) (*GetBrandRoomResp, error)
	// 删除厂牌权益
	DelBrandRoom(context.Context, *DelBrandRoomReq) (*DelBrandRoomResp, error)
	// 根据uid获取厂牌brandId
	GetBrandIdByUid(context.Context, *GetBrandIdByUidReq) (*GetBrandIdByUidResp, error)
	GetBrandTypeById(context.Context, *GetBrandTypeByIdReq) (*GetBrandTypeByIdResp, error)
	GetBrandTopicById(context.Context, *GetBrandTopicByIdReq) (*GetBrandTopicByIdResp, error)
	// 厂牌类型
	SetBrandType(context.Context, *SetBrandTypeReq) (*SetBrandTypeResp, error)
	DelBrandType(context.Context, *DelBrandTypeReq) (*DelBrandTypeResp, error)
	GetBrandTypeFuzzySearch(context.Context, *GetBrandTypeFuzzySearchReq) (*GetBrandTypeFuzzySearchResp, error)
	GetBrandBaseInfo(context.Context, *GetBrandBaseInfoReq) (*GetBrandBaseInfoResp, error)
	// 添加/编辑厂牌积分
	SetBrandIntegral(context.Context, *SetBrandIntegralReq) (*SetBrandIntegralResp, error)
	// 获取厂牌积分
	GetBrandIntegral(context.Context, *GetBrandIntegralReq) (*GetBrandIntegralResp, error)
	// 批量添加接口
	BatSetBrandIntegral(context.Context, *BatSetBrandIntegralReq) (*BatSetBrandIntegralResp, error)
	// 删除积分流水
	DelBrandIntegral(context.Context, *DelBrandIntegralReq) (*DelBrandIntegralResp, error)
	// 添加/编辑认证标
	SetSignCount(context.Context, *SetSignCountReq) (*SetSignCountResp, error)
	// 批量添加
	BatSetSignCount(context.Context, *BatSetSignCountReq) (*BatSetSignCountResp, error)
	// 删除认证
	DelSignCount(context.Context, *DelSignCountReq) (*DelSignCountResp, error)
	// 获取认证
	GetSignCount(context.Context, *GetSignCountReq) (*GetSignCountResp, error)
	GetUserBrandInfo(context.Context, *GetUserBrandInfoReq) (*GetUserBrandInfoResp, error)
	// 获取粉丝团认证标
	GetFansAuthentication(context.Context, *GetFansAuthenticationReq) (*GetFansAuthenticationResp, error)
	// 更新帖子积分
	UpdateBrandTopicPostPoints(context.Context, *UpdateBrandTopicPostPointsReq) (*UpdateBrandTopicPostPointsResp, error)
	// 查询帖子(包含查询厂牌名匹配查询，话题ID，ttid，发放积分状态，根绝发布日期查询)
	GetBrandTopicPost(context.Context, *GetBrandTopicPostReq) (*GetBrandTopicPostResp, error)
	// 获取ID对应的匹配厂牌
	GetMatchBrand(context.Context, *GetMatchBrandReq) (*GetMatchBrandResp, error)
}

func RegisterBrandServer(s *grpc.Server, srv BrandServer) {
	s.RegisterService(&_Brand_serviceDesc, srv)
}

func _Brand_SetBrand_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetBrandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).SetBrand(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/SetBrand",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).SetBrand(ctx, req.(*SetBrandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_GetBrand_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBrandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).GetBrand(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/GetBrand",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).GetBrand(ctx, req.(*GetBrandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_GetBrandById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBrandByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).GetBrandById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/GetBrandById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).GetBrandById(ctx, req.(*GetBrandByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_DelBrand_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelBrandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).DelBrand(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/DelBrand",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).DelBrand(ctx, req.(*DelBrandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_ListBrand_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListBrandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).ListBrand(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/ListBrand",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).ListBrand(ctx, req.(*ListBrandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_SetBrandMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetBrandMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).SetBrandMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/SetBrandMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).SetBrandMember(ctx, req.(*SetBrandMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_GetBrandMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBrandMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).GetBrandMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/GetBrandMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).GetBrandMember(ctx, req.(*GetBrandMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_DelBrandMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelBrandMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).DelBrandMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/DelBrandMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).DelBrandMember(ctx, req.(*DelBrandMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_BatGetBrandMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetBrandMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).BatGetBrandMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/BatGetBrandMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).BatGetBrandMember(ctx, req.(*BatGetBrandMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_GetBrandMemberByUids_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBrandMemberByUidsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).GetBrandMemberByUids(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/GetBrandMemberByUids",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).GetBrandMemberByUids(ctx, req.(*GetBrandMemberByUidsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_GetUserBrandTopicID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserBrandTopicIDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).GetUserBrandTopicID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/GetUserBrandTopicID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).GetUserBrandTopicID(ctx, req.(*GetUserBrandTopicIDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_BatchAddBrand_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchAddBrandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).BatchAddBrand(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/BatchAddBrand",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).BatchAddBrand(ctx, req.(*BatchAddBrandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_BrandNameSearch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BrandNameSearchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).BrandNameSearch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/BrandNameSearch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).BrandNameSearch(ctx, req.(*BrandNameSearchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_BatchSetMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchSetMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).BatchSetMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/BatchSetMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).BatchSetMember(ctx, req.(*BatchSetMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_BatchDelMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDelMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).BatchDelMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/BatchDelMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).BatchDelMember(ctx, req.(*BatchDelMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_SetBrandRoom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetBrandRoomReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).SetBrandRoom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/SetBrandRoom",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).SetBrandRoom(ctx, req.(*SetBrandRoomReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_GetBrandRoom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBrandRoomReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).GetBrandRoom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/GetBrandRoom",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).GetBrandRoom(ctx, req.(*GetBrandRoomReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_DelBrandRoom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelBrandRoomReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).DelBrandRoom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/DelBrandRoom",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).DelBrandRoom(ctx, req.(*DelBrandRoomReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_GetBrandIdByUid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBrandIdByUidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).GetBrandIdByUid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/GetBrandIdByUid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).GetBrandIdByUid(ctx, req.(*GetBrandIdByUidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_GetBrandTypeById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBrandTypeByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).GetBrandTypeById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/GetBrandTypeById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).GetBrandTypeById(ctx, req.(*GetBrandTypeByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_GetBrandTopicById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBrandTopicByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).GetBrandTopicById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/GetBrandTopicById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).GetBrandTopicById(ctx, req.(*GetBrandTopicByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_SetBrandType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetBrandTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).SetBrandType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/SetBrandType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).SetBrandType(ctx, req.(*SetBrandTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_DelBrandType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelBrandTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).DelBrandType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/DelBrandType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).DelBrandType(ctx, req.(*DelBrandTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_GetBrandTypeFuzzySearch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBrandTypeFuzzySearchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).GetBrandTypeFuzzySearch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/GetBrandTypeFuzzySearch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).GetBrandTypeFuzzySearch(ctx, req.(*GetBrandTypeFuzzySearchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_GetBrandBaseInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBrandBaseInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).GetBrandBaseInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/GetBrandBaseInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).GetBrandBaseInfo(ctx, req.(*GetBrandBaseInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_SetBrandIntegral_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetBrandIntegralReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).SetBrandIntegral(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/SetBrandIntegral",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).SetBrandIntegral(ctx, req.(*SetBrandIntegralReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_GetBrandIntegral_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBrandIntegralReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).GetBrandIntegral(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/GetBrandIntegral",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).GetBrandIntegral(ctx, req.(*GetBrandIntegralReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_BatSetBrandIntegral_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatSetBrandIntegralReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).BatSetBrandIntegral(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/BatSetBrandIntegral",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).BatSetBrandIntegral(ctx, req.(*BatSetBrandIntegralReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_DelBrandIntegral_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelBrandIntegralReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).DelBrandIntegral(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/DelBrandIntegral",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).DelBrandIntegral(ctx, req.(*DelBrandIntegralReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_SetSignCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetSignCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).SetSignCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/SetSignCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).SetSignCount(ctx, req.(*SetSignCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_BatSetSignCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatSetSignCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).BatSetSignCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/BatSetSignCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).BatSetSignCount(ctx, req.(*BatSetSignCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_DelSignCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelSignCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).DelSignCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/DelSignCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).DelSignCount(ctx, req.(*DelSignCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_GetSignCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSignCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).GetSignCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/GetSignCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).GetSignCount(ctx, req.(*GetSignCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_GetUserBrandInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserBrandInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).GetUserBrandInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/GetUserBrandInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).GetUserBrandInfo(ctx, req.(*GetUserBrandInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_GetFansAuthentication_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFansAuthenticationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).GetFansAuthentication(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/GetFansAuthentication",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).GetFansAuthentication(ctx, req.(*GetFansAuthenticationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_UpdateBrandTopicPostPoints_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBrandTopicPostPointsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).UpdateBrandTopicPostPoints(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/UpdateBrandTopicPostPoints",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).UpdateBrandTopicPostPoints(ctx, req.(*UpdateBrandTopicPostPointsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_GetBrandTopicPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBrandTopicPostReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).GetBrandTopicPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/GetBrandTopicPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).GetBrandTopicPost(ctx, req.(*GetBrandTopicPostReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_GetMatchBrand_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMatchBrandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandServer).GetMatchBrand(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rhythm.brand.Brand/GetMatchBrand",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandServer).GetMatchBrand(ctx, req.(*GetMatchBrandReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Brand_serviceDesc = grpc.ServiceDesc{
	ServiceName: "rhythm.brand.Brand",
	HandlerType: (*BrandServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetBrand",
			Handler:    _Brand_SetBrand_Handler,
		},
		{
			MethodName: "GetBrand",
			Handler:    _Brand_GetBrand_Handler,
		},
		{
			MethodName: "GetBrandById",
			Handler:    _Brand_GetBrandById_Handler,
		},
		{
			MethodName: "DelBrand",
			Handler:    _Brand_DelBrand_Handler,
		},
		{
			MethodName: "ListBrand",
			Handler:    _Brand_ListBrand_Handler,
		},
		{
			MethodName: "SetBrandMember",
			Handler:    _Brand_SetBrandMember_Handler,
		},
		{
			MethodName: "GetBrandMember",
			Handler:    _Brand_GetBrandMember_Handler,
		},
		{
			MethodName: "DelBrandMember",
			Handler:    _Brand_DelBrandMember_Handler,
		},
		{
			MethodName: "BatGetBrandMember",
			Handler:    _Brand_BatGetBrandMember_Handler,
		},
		{
			MethodName: "GetBrandMemberByUids",
			Handler:    _Brand_GetBrandMemberByUids_Handler,
		},
		{
			MethodName: "GetUserBrandTopicID",
			Handler:    _Brand_GetUserBrandTopicID_Handler,
		},
		{
			MethodName: "BatchAddBrand",
			Handler:    _Brand_BatchAddBrand_Handler,
		},
		{
			MethodName: "BrandNameSearch",
			Handler:    _Brand_BrandNameSearch_Handler,
		},
		{
			MethodName: "BatchSetMember",
			Handler:    _Brand_BatchSetMember_Handler,
		},
		{
			MethodName: "BatchDelMember",
			Handler:    _Brand_BatchDelMember_Handler,
		},
		{
			MethodName: "SetBrandRoom",
			Handler:    _Brand_SetBrandRoom_Handler,
		},
		{
			MethodName: "GetBrandRoom",
			Handler:    _Brand_GetBrandRoom_Handler,
		},
		{
			MethodName: "DelBrandRoom",
			Handler:    _Brand_DelBrandRoom_Handler,
		},
		{
			MethodName: "GetBrandIdByUid",
			Handler:    _Brand_GetBrandIdByUid_Handler,
		},
		{
			MethodName: "GetBrandTypeById",
			Handler:    _Brand_GetBrandTypeById_Handler,
		},
		{
			MethodName: "GetBrandTopicById",
			Handler:    _Brand_GetBrandTopicById_Handler,
		},
		{
			MethodName: "SetBrandType",
			Handler:    _Brand_SetBrandType_Handler,
		},
		{
			MethodName: "DelBrandType",
			Handler:    _Brand_DelBrandType_Handler,
		},
		{
			MethodName: "GetBrandTypeFuzzySearch",
			Handler:    _Brand_GetBrandTypeFuzzySearch_Handler,
		},
		{
			MethodName: "GetBrandBaseInfo",
			Handler:    _Brand_GetBrandBaseInfo_Handler,
		},
		{
			MethodName: "SetBrandIntegral",
			Handler:    _Brand_SetBrandIntegral_Handler,
		},
		{
			MethodName: "GetBrandIntegral",
			Handler:    _Brand_GetBrandIntegral_Handler,
		},
		{
			MethodName: "BatSetBrandIntegral",
			Handler:    _Brand_BatSetBrandIntegral_Handler,
		},
		{
			MethodName: "DelBrandIntegral",
			Handler:    _Brand_DelBrandIntegral_Handler,
		},
		{
			MethodName: "SetSignCount",
			Handler:    _Brand_SetSignCount_Handler,
		},
		{
			MethodName: "BatSetSignCount",
			Handler:    _Brand_BatSetSignCount_Handler,
		},
		{
			MethodName: "DelSignCount",
			Handler:    _Brand_DelSignCount_Handler,
		},
		{
			MethodName: "GetSignCount",
			Handler:    _Brand_GetSignCount_Handler,
		},
		{
			MethodName: "GetUserBrandInfo",
			Handler:    _Brand_GetUserBrandInfo_Handler,
		},
		{
			MethodName: "GetFansAuthentication",
			Handler:    _Brand_GetFansAuthentication_Handler,
		},
		{
			MethodName: "UpdateBrandTopicPostPoints",
			Handler:    _Brand_UpdateBrandTopicPostPoints_Handler,
		},
		{
			MethodName: "GetBrandTopicPost",
			Handler:    _Brand_GetBrandTopicPost_Handler,
		},
		{
			MethodName: "GetMatchBrand",
			Handler:    _Brand_GetMatchBrand_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rhythm/brand.proto",
}

func init() { proto.RegisterFile("rhythm/brand.proto", fileDescriptor_brand_ca39bef01c84f7e1) }

var fileDescriptor_brand_ca39bef01c84f7e1 = []byte{
	// 3546 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x3b, 0x5d, 0x73, 0x1b, 0x47,
	0x72, 0xc4, 0x17, 0x49, 0x34, 0x08, 0x72, 0x39, 0xa4, 0x28, 0x08, 0xb2, 0x24, 0x6a, 0x2d, 0x59,
	0x8c, 0x2c, 0x53, 0xb6, 0x64, 0x27, 0x29, 0xc7, 0x25, 0x07, 0x24, 0x20, 0x18, 0x09, 0xbf, 0x6a,
	0x41, 0x28, 0x89, 0xec, 0x18, 0xb5, 0x04, 0x96, 0xe4, 0x5a, 0xc0, 0xee, 0x7a, 0x77, 0x49, 0x9b,
	0xca, 0x53, 0x52, 0xa9, 0x4a, 0x55, 0xfe, 0x48, 0xfe, 0x44, 0xaa, 0xf2, 0x92, 0xfb, 0x01, 0xf7,
	0x74, 0x57, 0xf7, 0x3b, 0xfc, 0x74, 0x4f, 0x57, 0xd3, 0xb3, 0x1f, 0x33, 0xb3, 0x1f, 0x84, 0x2d,
	0x5f, 0xd5, 0xbd, 0xed, 0x4c, 0xf7, 0xf4, 0xf4, 0xf4, 0x74, 0xf7, 0xf4, 0x07, 0x00, 0xc4, 0x3d,
	0xbf, 0xf2, 0xcf, 0xa7, 0x4f, 0x4f, 0x5c, 0xdd, 0x1a, 0x6f, 0x3b, 0xae, 0xed, 0xdb, 0x64, 0x89,
	0xcd, 0x6d, 0xe3, 0x9c, 0xfa, 0x87, 0x02, 0x6c, 0xbc, 0xd4, 0x2d, 0xaf, 0x75, 0xe1, 0x9f, 0x1b,
	0x96, 0x6f, 0x8e, 0x74, 0xdf, 0xb4, 0xad, 0x9e, 0x75, 0x6a, 0x13, 0x05, 0x4a, 0x17, 0xe6, 0xb8,
	0x51, 0xd8, 0x2c, 0x6c, 0xd5, 0x35, 0xfa, 0x49, 0x36, 0x61, 0x69, 0x64, 0xb8, 0xfe, 0xd0, 0xbf,
	0x72, 0x8c, 0xa1, 0x39, 0x6e, 0x14, 0x37, 0x0b, 0x5b, 0x55, 0x0d, 0xe8, 0xdc, 0xf1, 0x95, 0x63,
	0xf4, 0xc6, 0xe4, 0x0e, 0x80, 0x77, 0x6e, 0x53, 0x14, 0xe3, 0x47, 0xbf, 0x51, 0x42, 0x78, 0x15,
	0x67, 0x8e, 0x8d, 0x1f, 0x7d, 0x42, 0xa0, 0x8c, 0x80, 0x32, 0x02, 0xf0, 0x9b, 0xdc, 0x83, 0xda,
	0x85, 0x33, 0xd6, 0x7d, 0x63, 0xe8, 0x9b, 0x53, 0xa3, 0x51, 0xc1, 0xed, 0x80, 0x4d, 0x1d, 0x9b,
	0x53, 0x83, 0xd2, 0x3c, 0x31, 0xce, 0x4c, 0x8b, 0xc1, 0xe7, 0x11, 0x5e, 0xc5, 0x19, 0x04, 0xdf,
	0x82, 0x45, 0xc3, 0x1a, 0x33, 0xe0, 0x02, 0x02, 0x17, 0x0c, 0x6b, 0x4c, 0x41, 0xea, 0x13, 0x68,
	0x74, 0x0d, 0x3f, 0x79, 0x3c, 0xcd, 0xf8, 0x3e, 0x79, 0x3a, 0xf5, 0xdf, 0xe0, 0x56, 0x06, 0xb6,
	0xe7, 0x90, 0x6f, 0xa1, 0x71, 0xaa, 0x5b, 0xde, 0x50, 0x17, 0x40, 0x43, 0xd3, 0x3a, 0xb5, 0x91,
	0x46, 0xed, 0xd9, 0x83, 0x6d, 0x5e, 0xb0, 0xdb, 0xe9, 0x42, 0xd5, 0x36, 0x4e, 0x53, 0xe7, 0xd5,
	0x2f, 0xa0, 0xd6, 0x37, 0xfc, 0x1d, 0xba, 0x94, 0x72, 0xf7, 0x11, 0x54, 0x90, 0x4c, 0x40, 0xfb,
	0xa6, 0x48, 0x1b, 0xd1, 0x90, 0x1c, 0xc3, 0x52, 0x97, 0x61, 0x29, 0x5e, 0xed, 0x39, 0xea, 0x6b,
	0xa8, 0x75, 0x39, 0x6a, 0x1b, 0x30, 0x6f, 0x9f, 0x9e, 0x7a, 0x86, 0x1f, 0x1c, 0x37, 0x18, 0x91,
	0x75, 0xa8, 0x4c, 0xcc, 0xa9, 0xe9, 0xe3, 0x45, 0xd6, 0x35, 0x36, 0x40, 0x79, 0xd3, 0x95, 0x43,
	0x4b, 0x9f, 0x1a, 0xe1, 0x1d, 0xe2, 0xcc, 0x81, 0x3e, 0x35, 0xd4, 0xff, 0x9e, 0x87, 0x6a, 0xc4,
	0x00, 0x95, 0x3e, 0x43, 0x0e, 0x64, 0x59, 0xd5, 0x16, 0x70, 0xdc, 0x1b, 0xd3, 0xcb, 0x46, 0x0a,
	0x4c, 0x4b, 0xf0, 0x9b, 0x5e, 0xf6, 0xd4, 0x98, 0x9e, 0x18, 0xee, 0xd0, 0x33, 0xdf, 0x32, 0xe2,
	0x75, 0x0d, 0xd8, 0x54, 0xdf, 0x7c, 0x8b, 0x08, 0x23, 0xd7, 0x88, 0xb4, 0xa1, 0xcc, 0x10, 0xd8,
	0x14, 0x5e, 0x77, 0x13, 0x16, 0x6d, 0xc7, 0x70, 0x75, 0xdf, 0x76, 0x51, 0x57, 0xaa, 0x5a, 0x34,
	0xa6, 0xe7, 0x31, 0x2d, 0xdf, 0xb5, 0x51, 0x49, 0xaa, 0x1a, 0x1b, 0xd0, 0xf3, 0x4c, 0x75, 0xf7,
	0xcd, 0x70, 0x64, 0x5f, 0x58, 0x7e, 0xa0, 0x22, 0x55, 0x3a, 0xb3, 0x4b, 0x27, 0x28, 0x18, 0x6f,
	0x96, 0x81, 0x17, 0x19, 0x98, 0xce, 0x30, 0xf0, 0x2d, 0x58, 0xf4, 0x6d, 0xc7, 0x1c, 0xd1, 0x03,
	0x56, 0xd9, 0x01, 0x71, 0xdc, 0x1b, 0x93, 0xc7, 0xb0, 0xea, 0xfd, 0x60, 0xfa, 0xa3, 0xf3, 0xe1,
	0xe8, 0x5c, 0xb7, 0x2c, 0x63, 0x32, 0x3c, 0x39, 0x6b, 0xc0, 0x66, 0x61, 0x6b, 0x51, 0x5b, 0x61,
	0x80, 0x5d, 0x36, 0xbf, 0x73, 0x46, 0x77, 0x09, 0x91, 0xcc, 0x71, 0xa3, 0xc6, 0x76, 0x09, 0x66,
	0x7a, 0x63, 0xa2, 0x42, 0xfd, 0xc2, 0x1a, 0x1b, 0xee, 0x90, 0xee, 0x74, 0xe1, 0x4e, 0x1a, 0x4b,
	0xb8, 0x55, 0x0d, 0x27, 0x8f, 0xcc, 0xd1, 0xc0, 0x9d, 0x88, 0x38, 0xd3, 0xf1, 0x67, 0x8d, 0xba,
	0x88, 0xb3, 0x3f, 0xfe, 0x8c, 0xe2, 0x78, 0xbe, 0x7e, 0x66, 0x44, 0x74, 0x96, 0x19, 0x0e, 0x4e,
	0xc6, 0x74, 0x62, 0x1c, 0x4a, 0x67, 0x45, 0xc4, 0xa1, 0x74, 0x3e, 0x04, 0x42, 0xa1, 0x97, 0xfa,
	0xc4, 0x1c, 0x0f, 0x03, 0xeb, 0xf3, 0x1a, 0x0a, 0xb2, 0xbd, 0xe2, 0x98, 0xa3, 0x57, 0x14, 0xb0,
	0x83, 0x36, 0xe8, 0x91, 0x47, 0xa0, 0xc4, 0xc8, 0x68, 0x8b, 0x5e, 0x63, 0x15, 0x51, 0xeb, 0x21,
	0x6a, 0xc7, 0x1a, 0x1f, 0x7b, 0xe4, 0x43, 0x28, 0xa3, 0xc1, 0x90, 0x4c, 0xa5, 0xa6, 0x6e, 0x44,
	0x43, 0x24, 0xf2, 0x14, 0xd6, 0x99, 0x66, 0xa1, 0xb7, 0xd1, 0x7d, 0xdf, 0x65, 0xd6, 0xb6, 0x86,
	0xdc, 0xae, 0x9e, 0x84, 0xe8, 0x2d, 0xdf, 0x77, 0x51, 0x15, 0x1f, 0xc2, 0x72, 0xa0, 0x8a, 0x96,
	0x6f, 0x9c, 0xb9, 0xfa, 0xa4, 0xb1, 0xbe, 0x59, 0xd8, 0xaa, 0x68, 0x75, 0xa6, 0x90, 0xc1, 0x24,
	0xd9, 0x02, 0x85, 0xa1, 0x79, 0xe6, 0x99, 0x15, 0xdc, 0xfa, 0x0d, 0x44, 0x64, 0xcb, 0xfb, 0xe6,
	0x99, 0xc5, 0xae, 0xfe, 0x3e, 0x2c, 0x05, 0x98, 0xbe, 0xee, 0x5f, 0x78, 0x8d, 0x0d, 0x3c, 0x53,
	0x8d, 0x61, 0xe1, 0x94, 0xfa, 0x0d, 0x2c, 0x75, 0x39, 0xc3, 0x23, 0x7f, 0x1d, 0xda, 0xce, 0xc4,
	0xf4, 0xa8, 0xb5, 0x95, 0xf2, 0x8c, 0x97, 0x19, 0xd5, 0x9e, 0xe9, 0xa1, 0x25, 0xfa, 0xb6, 0xaf,
	0x4f, 0x42, 0x4b, 0xc4, 0x81, 0xfa, 0x04, 0x56, 0x42, 0xea, 0x3b, 0x57, 0x3d, 0x34, 0xe5, 0x6c,
	0x7b, 0x53, 0x5b, 0xa0, 0x88, 0xd8, 0x9e, 0xf3, 0x73, 0xfd, 0xc8, 0x16, 0xd4, 0xda, 0xc6, 0x24,
	0xf2, 0x1b, 0x39, 0x9b, 0x2d, 0xc3, 0x52, 0x8c, 0xe9, 0x39, 0x74, 0x4c, 0x0f, 0x12, 0x2e, 0x55,
	0xff, 0x11, 0x56, 0xfa, 0xe6, 0xd4, 0x99, 0x18, 0x33, 0xb9, 0x0a, 0xd1, 0xe5, 0x14, 0x65, 0x97,
	0xb3, 0x0f, 0x75, 0x8e, 0xb8, 0xe7, 0x90, 0x2f, 0x52, 0xc4, 0x7c, 0x47, 0x3c, 0x9b, 0xb4, 0x3b,
	0x27, 0x6c, 0xf5, 0xa7, 0x02, 0xac, 0x20, 0x60, 0x1f, 0xfd, 0xce, 0x75, 0xcc, 0x05, 0x2f, 0x45,
	0x31, 0x7e, 0x07, 0x09, 0x94, 0x5d, 0x7b, 0x12, 0xba, 0x2f, 0xfc, 0xfe, 0x33, 0x39, 0x2e, 0x4e,
	0x2a, 0x0b, 0x92, 0x54, 0xc8, 0x36, 0xac, 0x31, 0x70, 0xe8, 0x51, 0x99, 0x96, 0x32, 0x0f, 0xc6,
	0xec, 0x83, 0x1d, 0x30, 0xd0, 0xd5, 0x3e, 0xac, 0x86, 0x8f, 0x04, 0x9b, 0xa7, 0x57, 0xfc, 0x22,
	0x72, 0xc8, 0xd9, 0xa2, 0x94, 0x64, 0x15, 0xfa, 0x6b, 0x94, 0xe5, 0x3a, 0x10, 0x99, 0xa8, 0xe7,
	0xa8, 0xff, 0x5e, 0x80, 0xd5, 0x6e, 0x62, 0xaf, 0x1c, 0x19, 0xc7, 0x2f, 0x54, 0x29, 0xfd, 0x85,
	0x2a, 0xf3, 0x2f, 0xd4, 0xfb, 0x50, 0x17, 0xcf, 0xcc, 0x82, 0x86, 0xa5, 0x29, 0x7f, 0xdc, 0xef,
	0x80, 0x74, 0x13, 0x9c, 0xbd, 0xeb, 0x79, 0x33, 0x0c, 0xf5, 0xef, 0x61, 0x35, 0xb4, 0x86, 0x99,
	0x8e, 0x9b, 0x50, 0x29, 0x2a, 0x47, 0x99, 0x82, 0xe7, 0xa8, 0x9f, 0xc0, 0xfa, 0x8e, 0xee, 0x5f,
	0x27, 0xc9, 0x12, 0x6f, 0x98, 0x7b, 0x00, 0xfb, 0x31, 0xbb, 0xef, 0x7a, 0xbd, 0xbf, 0x2b, 0xc0,
	0x8d, 0x14, 0x0e, 0x3c, 0x87, 0xe8, 0xa1, 0x1b, 0x0d, 0xe8, 0x4f, 0x75, 0x27, 0x20, 0xff, 0x37,
	0x12, 0xf9, 0xb4, 0xe5, 0xfc, 0xa6, 0xfb, 0xba, 0xd3, 0xb1, 0x7c, 0xf7, 0x2a, 0xf0, 0xbf, 0xd1,
	0x64, 0xf3, 0x6b, 0x58, 0x4b, 0x41, 0xa3, 0xc2, 0x7b, 0x63, 0x5c, 0x05, 0x22, 0xa5, 0x9f, 0x64,
	0x1b, 0x2a, 0x97, 0xfa, 0xe4, 0x82, 0x79, 0x8e, 0xda, 0xb3, 0x86, 0xc8, 0x40, 0x2c, 0x0e, 0x8d,
	0xa1, 0x7d, 0x5e, 0xfc, 0xdb, 0x82, 0xfa, 0x11, 0xdc, 0x14, 0xd9, 0xda, 0xb9, 0x1a, 0x98, 0x63,
	0x8f, 0x4a, 0x97, 0x40, 0xf9, 0xc2, 0x1c, 0x7b, 0x78, 0x9c, 0xba, 0x86, 0xdf, 0xea, 0x6b, 0x0c,
	0x25, 0x53, 0xd0, 0xdf, 0x5d, 0xa7, 0xd4, 0xc7, 0xb0, 0xd1, 0x35, 0xfc, 0x81, 0x67, 0xb8, 0xec,
	0x0d, 0xc4, 0xf0, 0xa2, 0x9d, 0x1e, 0xa4, 0xf6, 0x91, 0xed, 0x24, 0xae, 0xe7, 0x08, 0x91, 0x4a,
	0x41, 0x8c, 0x54, 0xee, 0x00, 0x30, 0x10, 0xef, 0x5f, 0x71, 0x06, 0xfd, 0xeb, 0xff, 0x14, 0x60,
	0x79, 0x47, 0xf7, 0x47, 0xe7, 0xb1, 0xb3, 0x16, 0x7d, 0x4f, 0x41, 0xf6, 0x3d, 0x62, 0x4c, 0x55,
	0x94, 0x63, 0xaa, 0x0f, 0x60, 0x85, 0x7b, 0xbb, 0xb9, 0x38, 0xb2, 0x1e, 0x3d, 0xdb, 0x48, 0x26,
	0xeb, 0x8d, 0x2f, 0x67, 0xbc, 0xf1, 0xea, 0x77, 0xa0, 0x20, 0xa3, 0xad, 0xf1, 0x38, 0x7a, 0xa5,
	0x78, 0xc7, 0x5a, 0x90, 0x1c, 0xeb, 0xe7, 0x50, 0x3d, 0x09, 0xcf, 0xd4, 0x28, 0xe2, 0xc5, 0xbc,
	0x97, 0x50, 0x4f, 0xee, 0xdc, 0x5a, 0x8c, 0xae, 0x3e, 0x82, 0x55, 0x69, 0x2f, 0xcf, 0x89, 0x82,
	0x5a, 0x66, 0x75, 0xf8, 0xad, 0x3e, 0x07, 0xb2, 0x13, 0x4a, 0xa6, 0x6f, 0xe8, 0xee, 0xe8, 0x9c,
	0xb2, 0x95, 0x2f, 0x41, 0xfa, 0x40, 0xb2, 0x5d, 0x71, 0xf8, 0x8e, 0x0f, 0xe4, 0x57, 0x81, 0xa5,
	0xf0, 0x1c, 0x78, 0x0e, 0xf9, 0x24, 0x88, 0xb7, 0xb2, 0x35, 0x32, 0xde, 0x9d, 0x45, 0x5d, 0xaa,
	0x11, 0x1c, 0xba, 0x6f, 0xf8, 0xb1, 0xbb, 0xc9, 0x93, 0xf0, 0xa7, 0xc1, 0x1e, 0x4c, 0xb8, 0x9b,
	0x29, 0xc2, 0x8d, 0x48, 0x71, 0xdb, 0x0c, 0x80, 0x24, 0x61, 0xef, 0xfc, 0x08, 0xab, 0xaf, 0x64,
	0xb2, 0x28, 0x06, 0xf9, 0x26, 0x4a, 0xa2, 0x2e, 0xdf, 0x87, 0xa5, 0xd1, 0xb9, 0x31, 0x7a, 0x43,
	0x1f, 0x6e, 0xd3, 0x3a, 0x0b, 0xf6, 0xa8, 0xe1, 0xdc, 0x31, 0x4e, 0xa9, 0xad, 0x80, 0x6e, 0xdb,
	0x98, 0xfc, 0x42, 0x76, 0xd5, 0x5e, 0x20, 0xd8, 0x88, 0x04, 0x15, 0xec, 0xa7, 0xc2, 0x05, 0xa5,
	0x09, 0x4f, 0xd8, 0x31, 0x10, 0xde, 0xba, 0xcc, 0x0d, 0xbe, 0x15, 0x6e, 0x90, 0x96, 0x51, 0x7b,
	0x21, 0xcb, 0x50, 0x8c, 0x98, 0x2a, 0x9a, 0xe9, 0xb9, 0xd8, 0x6d, 0xa8, 0x46, 0x66, 0x17, 0x98,
	0xe7, 0xa2, 0x1f, 0x18, 0x1b, 0x79, 0x00, 0xcb, 0x71, 0xaa, 0x8f, 0x4b, 0x99, 0x4d, 0x2e, 0x85,
	0xc9, 0x3e, 0xea, 0xdd, 0x0b, 0x58, 0x09, 0x5f, 0x7f, 0x8c, 0xdc, 0x8d, 0xef, 0xa3, 0x18, 0xbf,
	0x30, 0x43, 0x8c, 0xaf, 0x12, 0x50, 0xc4, 0xf5, 0x9e, 0xa3, 0xde, 0x87, 0x95, 0xf0, 0x25, 0x0c,
	0x69, 0x4a, 0xa7, 0xa1, 0xcb, 0x44, 0x14, 0xcf, 0x51, 0xbf, 0x81, 0x66, 0x97, 0x23, 0xf5, 0xf2,
	0xe2, 0xed, 0xdb, 0xab, 0xd8, 0x18, 0x15, 0x28, 0x79, 0x7e, 0xa8, 0xbc, 0xf4, 0x93, 0x8b, 0x38,
	0x8a, 0xe9, 0x11, 0x47, 0x89, 0x8b, 0x38, 0x54, 0x1f, 0x6e, 0x67, 0x52, 0xf7, 0x1c, 0xf2, 0xa5,
	0xe0, 0xef, 0xae, 0x89, 0xfd, 0x91, 0xe5, 0xd8, 0x11, 0xe6, 0x84, 0x15, 0xbf, 0x2d, 0xc0, 0x5a,
	0x14, 0xd2, 0xeb, 0x1e, 0xb3, 0xd3, 0x6b, 0x5d, 0x0b, 0x4d, 0xf0, 0x38, 0x6e, 0xa2, 0x3a, 0x4d,
	0x2d, 0xda, 0xb2, 0x37, 0x96, 0x3c, 0x34, 0xa7, 0x02, 0x75, 0xc1, 0xe9, 0x72, 0x62, 0x2a, 0xa7,
	0x8b, 0xa9, 0xc2, 0x07, 0x66, 0x72, 0xc6, 0x34, 0x9f, 0xcc, 0x98, 0xc6, 0xb0, 0x9e, 0x3c, 0xd2,
	0xaf, 0x9e, 0x39, 0x7d, 0x1c, 0x0b, 0x8e, 0x1e, 0x65, 0x86, 0xec, 0xa9, 0x13, 0xf3, 0x15, 0xaf,
	0x98, 0x2d, 0x83, 0xc2, 0x0b, 0x0d, 0x32, 0xa8, 0x9d, 0xd8, 0x22, 0x34, 0xdb, 0x9e, 0xd2, 0x4d,
	0x9f, 0x0a, 0x16, 0x71, 0x3b, 0x85, 0x00, 0xc5, 0xe4, 0xec, 0x9b, 0xb3, 0x0a, 0x46, 0xc3, 0x73,
	0xd4, 0x9f, 0xca, 0x50, 0x17, 0x70, 0xaf, 0x53, 0x02, 0xfe, 0xa8, 0x45, 0xd1, 0x39, 0xf1, 0x81,
	0x42, 0x69, 0x86, 0x92, 0x46, 0x79, 0x96, 0x92, 0x46, 0xe5, 0xda, 0x92, 0xc6, 0xfc, 0x0c, 0x25,
	0x8d, 0x85, 0x19, 0x4a, 0x1a, 0x8b, 0x33, 0x94, 0x34, 0xaa, 0xb3, 0x96, 0x34, 0x60, 0xf6, 0x92,
	0x46, 0x2d, 0xad, 0xa4, 0x41, 0x11, 0x47, 0xc3, 0xb4, 0xda, 0x4d, 0xdd, 0x19, 0x0d, 0xb8, 0xa3,
	0xca, 0x88, 0x71, 0x01, 0x87, 0x43, 0xa4, 0x7c, 0x32, 0xc4, 0xb4, 0x2a, 0x4e, 0xdd, 0x19, 0xf5,
	0xb9, 0x43, 0xcb, 0x88, 0x71, 0x29, 0x87, 0x43, 0xa4, 0x14, 0xf9, 0xe7, 0x5b, 0x91, 0x9e, 0x6f,
	0x31, 0x32, 0x5c, 0x95, 0x23, 0xc3, 0xab, 0xb8, 0x02, 0x11, 0xaa, 0x73, 0x64, 0xf9, 0x05, 0xde,
	0xf2, 0xb3, 0xdc, 0xa9, 0xa8, 0x24, 0x25, 0x59, 0x49, 0x78, 0x2d, 0x2d, 0x8b, 0x06, 0xf9, 0x2f,
	0x71, 0x39, 0x23, 0xb4, 0x02, 0xce, 0x94, 0x4a, 0x33, 0x99, 0x52, 0x76, 0x5d, 0x25, 0x2a, 0x5e,
	0x04, 0xa7, 0xca, 0xf1, 0x0c, 0xdc, 0x6b, 0x13, 0x99, 0xe3, 0x07, 0x71, 0x72, 0xd9, 0x1b, 0x63,
	0x2a, 0x90, 0x1e, 0xae, 0x73, 0x7e, 0x28, 0xc2, 0x63, 0xa1, 0x7a, 0xd6, 0x6e, 0x9f, 0x70, 0x7e,
	0x88, 0x5e, 0xc3, 0x0c, 0xae, 0xeb, 0x19, 0xdc, 0x48, 0x59, 0x92, 0x9b, 0x11, 0xa8, 0xff, 0x00,
	0x6b, 0xa1, 0x8f, 0x09, 0x2b, 0x63, 0x74, 0x97, 0xe7, 0x82, 0xaf, 0xba, 0x97, 0xea, 0x7f, 0x19,
	0x36, 0xe7, 0xaf, 0x36, 0x60, 0x3d, 0x49, 0xcb, 0x73, 0xd4, 0xff, 0x2f, 0xc0, 0x6a, 0x62, 0x4d,
	0x22, 0x34, 0xc9, 0x71, 0x54, 0xf9, 0x95, 0x68, 0x7a, 0xb9, 0x2c, 0xff, 0x28, 0x63, 0xf9, 0x8e,
	0x0d, 0xe2, 0x5a, 0x4a, 0x85, 0xaf, 0xa5, 0x48, 0x5d, 0x86, 0xf9, 0x44, 0x97, 0xa1, 0x01, 0x0b,
	0xcc, 0x28, 0xc2, 0x4a, 0x4b, 0x38, 0x54, 0xbf, 0xe5, 0xee, 0x90, 0x13, 0x55, 0x4e, 0xf4, 0x97,
	0x5e, 0x57, 0xcf, 0xa8, 0x71, 0xa8, 0x7a, 0x7c, 0xe3, 0xbc, 0xf8, 0xb8, 0xbb, 0x28, 0xcd, 0x7c,
	0x17, 0x19, 0x0a, 0xbf, 0x0f, 0x1b, 0x3b, 0xba, 0x9f, 0x7f, 0xe1, 0xb3, 0x6f, 0xa2, 0xde, 0x82,
	0x9b, 0xa9, 0xe4, 0x3c, 0x47, 0x7d, 0x08, 0x6b, 0xa1, 0xb1, 0xf0, 0xdb, 0xc8, 0x11, 0xdc, 0x06,
	0xac, 0x27, 0xd1, 0x3c, 0x47, 0xed, 0xe0, 0xf3, 0x19, 0x95, 0x60, 0xe9, 0xd2, 0x67, 0x82, 0x4a,
	0xde, 0x95, 0xaa, 0x7c, 0x1c, 0x32, 0xc7, 0xe0, 0x6f, 0x0a, 0xf8, 0x84, 0x0a, 0xa0, 0xbf, 0x54,
	0xc5, 0xe3, 0xbd, 0xf3, 0x82, 0xe8, 0x9d, 0xd5, 0x97, 0xe2, 0x31, 0x50, 0x2d, 0x7e, 0x89, 0x3c,
	0xbe, 0xc2, 0x8c, 0x21, 0x5b, 0xb2, 0xa5, 0x99, 0x29, 0xdd, 0x80, 0xb5, 0x04, 0xa5, 0x28, 0x68,
	0x17, 0xa8, 0xa7, 0x07, 0xed, 0xe2, 0xb2, 0x47, 0x68, 0x5a, 0x51, 0x35, 0x23, 0x8c, 0x6f, 0x93,
	0x7e, 0xf4, 0xff, 0x0a, 0x68, 0x24, 0x12, 0x66, 0xae, 0x27, 0xbd, 0x26, 0x67, 0x8e, 0xc1, 0x34,
	0x02, 0x16, 0xee, 0x18, 0x33, 0x28, 0x02, 0x65, 0x8c, 0x8a, 0x83, 0x56, 0x25, 0xfd, 0xa6, 0x19,
	0x53, 0x5c, 0xa3, 0x08, 0x6a, 0xb8, 0x7a, 0xd8, 0x7e, 0x88, 0xe8, 0x61, 0x56, 0x3a, 0xcf, 0xd1,
	0xd3, 0x68, 0x6a, 0xfa, 0x1a, 0x5f, 0x52, 0x41, 0x42, 0xbf, 0x9a, 0x07, 0xf9, 0x06, 0x9f, 0xca,
	0x2c, 0x35, 0x99, 0xf9, 0x72, 0x33, 0x9c, 0xc7, 0x1f, 0xcb, 0x40, 0x4e, 0xa2, 0xc7, 0xe5, 0xc8,
	0xf6, 0xd2, 0xcd, 0x89, 0x40, 0xd9, 0xf7, 0x23, 0x53, 0xc2, 0x6f, 0xaa, 0xdb, 0x96, 0x39, 0x7a,
	0xc3, 0x59, 0x51, 0x34, 0x96, 0x6a, 0xf8, 0xe5, 0x9f, 0x57, 0xc3, 0x27, 0x37, 0x61, 0xc1, 0xb1,
	0x3d, 0x3f, 0x8c, 0x3c, 0xab, 0xda, 0x3c, 0x1d, 0xf6, 0xc6, 0xd4, 0xde, 0x10, 0x80, 0x0f, 0x1d,
	0xcd, 0x40, 0x68, 0xb6, 0x0f, 0x74, 0x0a, 0xd9, 0xa7, 0x11, 0xdb, 0x8a, 0x6b, 0x4c, 0x0c, 0xd6,
	0xbf, 0x45, 0xac, 0xc6, 0x02, 0x22, 0x2d, 0x47, 0xd3, 0x88, 0x89, 0x96, 0xeb, 0x89, 0x75, 0xf5,
	0x8a, 0x06, 0x74, 0x8a, 0xa5, 0x32, 0xd1, 0x56, 0x01, 0x42, 0x95, 0x21, 0xd0, 0xa9, 0x00, 0xe1,
	0x3e, 0x2c, 0x21, 0xc2, 0xc8, 0xb6, 0x7c, 0xc3, 0xf2, 0x31, 0xd8, 0xac, 0x6a, 0xb8, 0x68, 0x97,
	0x4d, 0xc9, 0x6d, 0x03, 0x1a, 0x63, 0x96, 0x84, 0xb6, 0xc1, 0xfb, 0x50, 0x77, 0x6c, 0xd3, 0xf2,
	0xbd, 0x70, 0x9b, 0x25, 0xdc, 0x66, 0x89, 0x4d, 0x06, 0x1b, 0xed, 0xc2, 0xf2, 0x99, 0xab, 0x5b,
	0xfe, 0x30, 0xd2, 0xa7, 0x3a, 0x7a, 0x87, 0x6b, 0xe4, 0xb9, 0x84, 0x8b, 0x82, 0xe8, 0x84, 0x3c,
	0x01, 0xc2, 0x13, 0x61, 0x1b, 0x60, 0xe8, 0x59, 0xd1, 0x94, 0x18, 0xf3, 0x08, 0xe7, 0xc9, 0x0b,
	0xa8, 0xe9, 0xbe, 0xaf, 0x8f, 0xce, 0xa7, 0x06, 0x45, 0x5b, 0x49, 0xab, 0xad, 0xb5, 0x22, 0x04,
	0xdc, 0x8e, 0x5f, 0x40, 0x1e, 0xc2, 0xb2, 0xe3, 0x9a, 0x97, 0xfa, 0xe8, 0x6a, 0xe8, 0xd8, 0x13,
	0x73, 0x74, 0x85, 0xa1, 0x69, 0x45, 0xab, 0x07, 0xb3, 0x47, 0x38, 0xa9, 0xfe, 0x57, 0x01, 0xee,
	0x0c, 0xd0, 0x59, 0xee, 0x08, 0x2a, 0xc8, 0xb8, 0x48, 0xf1, 0x33, 0x79, 0x6e, 0x3d, 0x4a, 0x4f,
	0x83, 0xb3, 0x95, 0x70, 0x47, 0x96, 0x9e, 0x06, 0xc7, 0xe2, 0xc2, 0x80, 0xb2, 0x18, 0x06, 0x6c,
	0xc2, 0xdd, 0x3c, 0x46, 0x3c, 0x47, 0xfd, 0x7d, 0x41, 0x8a, 0xdd, 0x28, 0x3c, 0xd5, 0x9f, 0x5d,
	0xc3, 0x64, 0xa0, 0x11, 0x98, 0xa4, 0x04, 0xa6, 0x1e, 0x68, 0x09, 0xe6, 0x27, 0x18, 0x54, 0x33,
	0x14, 0xc3, 0x1a, 0x07, 0x89, 0x79, 0x95, 0xcd, 0x74, 0xac, 0x71, 0x52, 0x65, 0x2a, 0x29, 0x2a,
	0x13, 0xfb, 0x92, 0xf9, 0xf4, 0xc4, 0x7e, 0x81, 0xaf, 0x7f, 0x8c, 0xa4, 0x10, 0x93, 0x9d, 0xcc,
	0x73, 0xf2, 0x2b, 0x58, 0x49, 0xaf, 0x91, 0xe6, 0x68, 0x2a, 0x91, 0xa3, 0x29, 0xc2, 0xb2, 0xa8,
	0x32, 0x29, 0xb5, 0xfe, 0x2f, 0xa1, 0x8c, 0x0e, 0x9b, 0xae, 0x5c, 0x7e, 0xf6, 0x61, 0x9e, 0xc2,
	0x71, 0x43, 0x56, 0x73, 0xa2, 0x0b, 0xe9, 0x0d, 0x87, 0xf6, 0x18, 0x24, 0xbf, 0xc1, 0x90, 0x72,
	0x65, 0xfc, 0xe8, 0xbb, 0x7a, 0x70, 0xf3, 0x6c, 0x40, 0x9e, 0xc3, 0x3c, 0x27, 0xc6, 0x65, 0x39,
	0xeb, 0x08, 0x0c, 0x99, 0x49, 0x55, 0x0b, 0x50, 0xe9, 0x0d, 0x5d, 0x4e, 0x23, 0xbb, 0x0f, 0x1e,
	0x83, 0xcb, 0xe9, 0x6e, 0xbc, 0x93, 0xa3, 0xbb, 0xfa, 0x34, 0xf0, 0x08, 0x6c, 0x40, 0x0d, 0xd0,
	0x76, 0x4d, 0x9a, 0x98, 0x5e, 0x9a, 0x63, 0xc3, 0x1e, 0x8e, 0xec, 0x4b, 0xc3, 0x45, 0x97, 0x50,
	0xd5, 0x14, 0x06, 0x79, 0x45, 0x01, 0xbb, 0x74, 0x5e, 0xdd, 0xe7, 0x85, 0x85, 0x4f, 0xd6, 0x22,
	0x94, 0x0f, 0x0e, 0x0f, 0x3a, 0xca, 0x1c, 0xa9, 0x42, 0xa5, 0xb7, 0xdf, 0xea, 0x76, 0x94, 0x02,
	0x59, 0x80, 0x52, 0xb7, 0xf7, 0x52, 0x29, 0xd2, 0xb9, 0x57, 0xbd, 0x76, 0xe7, 0x50, 0x29, 0xd1,
	0xb9, 0xdd, 0xfd, 0xbe, 0x52, 0xa6, 0x73, 0xad, 0x41, 0xbb, 0x77, 0xa8, 0x54, 0x54, 0x15, 0xdf,
	0x90, 0xfd, 0xa8, 0x1a, 0x9e, 0xf6, 0x84, 0x1f, 0x61, 0x57, 0x8f, 0xc7, 0xf1, 0x1c, 0xf2, 0x77,
	0x7c, 0x89, 0x7d, 0xa6, 0x4a, 0x73, 0x8c, 0xff, 0xf8, 0x5f, 0x85, 0x4e, 0x2c, 0x7d, 0x28, 0xc9,
	0x0a, 0xd4, 0x06, 0xd6, 0x1b, 0xcb, 0xfe, 0xc1, 0xa2, 0x43, 0x65, 0x8e, 0x2c, 0x03, 0x1c, 0x1b,
	0xfa, 0x74, 0xcf, 0xd0, 0xc7, 0x86, 0xab, 0x14, 0x08, 0xc0, 0xfc, 0x81, 0xed, 0x4e, 0xf5, 0x89,
	0x52, 0xa4, 0xc8, 0xaf, 0xcc, 0x91, 0xb1, 0xab, 0x3b, 0xd4, 0x6f, 0x2b, 0x25, 0xb2, 0x04, 0x8b,
	0x47, 0xae, 0x3d, 0xbe, 0x18, 0x19, 0xae, 0x52, 0x7e, 0xfc, 0x29, 0xd4, 0x76, 0xe2, 0xda, 0x13,
	0x05, 0x1e, 0xd8, 0xec, 0x5b, 0x99, 0xa3, 0x74, 0x0e, 0xad, 0x89, 0x69, 0x19, 0x4a, 0x81, 0xd4,
	0x60, 0xe1, 0xf0, 0xf4, 0x14, 0x07, 0xc5, 0xc7, 0xff, 0x51, 0x80, 0xb5, 0x30, 0x2a, 0xed, 0x52,
	0xbf, 0x17, 0x2c, 0x6f, 0xc0, 0x7a, 0xef, 0xe0, 0xb8, 0xd3, 0xd5, 0x5a, 0x7b, 0xc3, 0xfe, 0x71,
	0xeb, 0x78, 0xd0, 0x1f, 0x06, 0xf2, 0xbe, 0x09, 0x6b, 0x32, 0xa4, 0xd5, 0x6e, 0x2b, 0x05, 0xd2,
	0x84, 0x0d, 0x19, 0xd0, 0xee, 0xb4, 0x07, 0xbb, 0xc7, 0x4a, 0x91, 0xbc, 0x07, 0x0d, 0x19, 0x36,
	0x38, 0xe8, 0xf5, 0xfb, 0x83, 0x4e, 0x5b, 0x29, 0x3d, 0x7e, 0x01, 0x30, 0x88, 0x9f, 0x9a, 0x0d,
	0x20, 0x83, 0x7e, 0x47, 0x8b, 0xb7, 0xd5, 0xf6, 0x5b, 0x7b, 0xca, 0x1c, 0x65, 0x89, 0x9f, 0x6f,
	0xed, 0x04, 0x90, 0xc2, 0xe3, 0xff, 0x2c, 0x42, 0x5d, 0xd0, 0x4d, 0xca, 0xe4, 0xee, 0xe1, 0xc1,
	0x71, 0xe7, 0xe0, 0x58, 0xe2, 0xfe, 0x1e, 0xdc, 0x96, 0x00, 0x83, 0x83, 0x76, 0x47, 0x1b, 0x6a,
	0x9d, 0x57, 0xbd, 0xce, 0x3f, 0x29, 0x05, 0xf2, 0x04, 0xb6, 0x72, 0x10, 0x86, 0xad, 0x83, 0xf6,
	0xf0, 0x48, 0xeb, 0x1c, 0xb5, 0xb4, 0x4e, 0x5b, 0x29, 0x92, 0x3b, 0x70, 0x4b, 0xc2, 0xee, 0x0f,
	0xfa, 0x47, 0xbd, 0xdd, 0xde, 0xe1, 0xa0, 0xaf, 0x94, 0xa8, 0x48, 0x24, 0x70, 0x6f, 0x6f, 0xaf,
	0xd3, 0x6d, 0xed, 0x29, 0x65, 0x72, 0x0b, 0x6e, 0x24, 0x58, 0xc4, 0xf3, 0x54, 0x52, 0x96, 0xb5,
	0x3b, 0x7b, 0x9d, 0xe3, 0x4e, 0x5b, 0x99, 0x4f, 0x59, 0xb6, 0xd3, 0x3a, 0x38, 0xe8, 0xb4, 0x95,
	0x85, 0x67, 0xff, 0xdb, 0x84, 0x0a, 0xaa, 0x00, 0xd9, 0x85, 0xc5, 0x30, 0x63, 0x21, 0xb7, 0x12,
	0xe1, 0x50, 0xa8, 0xf3, 0xcd, 0x66, 0x16, 0xc8, 0x73, 0xd4, 0x39, 0x4a, 0xa4, 0x9b, 0x41, 0xa4,
	0x9b, 0x4d, 0xa4, 0x2b, 0x12, 0x39, 0x8c, 0x7f, 0x34, 0x42, 0x53, 0x75, 0x72, 0x27, 0x1d, 0x3b,
	0xc8, 0xfc, 0x9b, 0x77, 0xf3, 0xc0, 0x21, 0x57, 0x61, 0x36, 0x25, 0x73, 0xc5, 0xfd, 0x9c, 0x43,
	0xe6, 0x4a, 0xf8, 0xfd, 0xc6, 0x1c, 0x79, 0x09, 0xd5, 0xe8, 0x47, 0x16, 0x44, 0x42, 0xe5, 0x7f,
	0xda, 0xd1, 0xbc, 0x9d, 0x09, 0x43, 0x3a, 0x03, 0x58, 0x16, 0x7f, 0x11, 0x40, 0xee, 0xa5, 0x8b,
	0x34, 0x6a, 0x83, 0x34, 0x37, 0xf3, 0x11, 0x42, 0xb2, 0xdd, 0x5c, 0xb2, 0xdd, 0xeb, 0xc8, 0x76,
	0x33, 0xc8, 0x8a, 0x7d, 0x77, 0x99, 0x6c, 0xa2, 0xaf, 0x2f, 0x93, 0x4d, 0x69, 0xdb, 0xcf, 0x91,
	0x6f, 0xb1, 0xdb, 0x23, 0x31, 0xac, 0x5e, 0xdb, 0x18, 0xff, 0xbe, 0xf9, 0xfe, 0x0c, 0xcd, 0x73,
	0x75, 0x8e, 0x9c, 0xc5, 0x91, 0x06, 0xdf, 0x8e, 0x26, 0x0f, 0xf3, 0x8e, 0x1c, 0x75, 0xb8, 0x9b,
	0x1f, 0xcc, 0x82, 0x86, 0x1b, 0x8d, 0xc5, 0x0c, 0x2d, 0xe8, 0x37, 0x93, 0x07, 0x09, 0x02, 0x29,
	0xed, 0xeb, 0xe6, 0xc3, 0x19, 0xb0, 0x70, 0x17, 0x0d, 0xea, 0x42, 0xab, 0x95, 0xdc, 0x4d, 0x69,
	0x85, 0x71, 0x3d, 0xdf, 0xe6, 0xbd, 0x5c, 0x38, 0xd2, 0xfc, 0xe7, 0xe0, 0x69, 0x89, 0x7b, 0xa2,
	0x64, 0x33, 0xe5, 0x5d, 0x12, 0x9a, 0xb6, 0xcd, 0xfb, 0xd7, 0x60, 0x84, 0x3a, 0x23, 0x76, 0x19,
	0xc9, 0xbd, 0xbc, 0xb6, 0x67, 0x8a, 0xce, 0x24, 0x9b, 0x94, 0x1c, 0xd9, 0xa8, 0xad, 0x97, 0x4a,
	0x96, 0xef, 0x1f, 0x36, 0x37, 0xf3, 0x11, 0x42, 0x6f, 0xc3, 0x77, 0x13, 0x64, 0x6f, 0x23, 0x75,
	0x2b, 0x9a, 0x77, 0xf3, 0xc0, 0xb2, 0xfb, 0x4a, 0x23, 0xd8, 0xcd, 0x27, 0xd8, 0x4d, 0x25, 0xc8,
	0x17, 0x58, 0x65, 0x82, 0x52, 0xa9, 0x56, 0x26, 0x98, 0xa8, 0xcd, 0xe2, 0xd5, 0x4b, 0x55, 0x57,
	0x92, 0xe1, 0x0b, 0xe2, 0xe2, 0xad, 0x7c, 0xf5, 0x29, 0x65, 0x5b, 0x75, 0x8e, 0x7c, 0x1d, 0x17,
	0xa5, 0xc3, 0x2e, 0x11, 0xc9, 0x58, 0xc8, 0xf5, 0x9d, 0x9a, 0xea, 0x75, 0x28, 0xa1, 0xd3, 0x48,
	0xd4, 0x71, 0x49, 0xd6, 0x52, 0xae, 0x36, 0x2c, 0x3b, 0x8d, 0xd4, 0x62, 0xb0, 0xa8, 0x09, 0x18,
	0x2f, 0x66, 0x68, 0x42, 0xd0, 0x75, 0xcd, 0xd2, 0x84, 0xa8, 0xe3, 0x2a, 0x5c, 0x5c, 0x1a, 0x41,
	0xa9, 0x8d, 0x9b, 0x75, 0x71, 0x1c, 0x41, 0x27, 0xfe, 0x51, 0x8e, 0xd4, 0x66, 0x25, 0x5b, 0xd9,
	0x22, 0x14, 0x7b, 0xbd, 0xcd, 0xbf, 0x9a, 0x11, 0x53, 0xbe, 0xd0, 0xb0, 0x1d, 0x99, 0x75, 0xa1,
	0x5c, 0x07, 0x36, 0xeb, 0x42, 0xf9, 0x8e, 0x26, 0x23, 0x2e, 0x17, 0x49, 0x65, 0xe2, 0x29, 0x35,
	0x59, 0x99, 0x78, 0x6a, 0x9d, 0x55, 0xe0, 0x3c, 0x8b, 0x78, 0xf7, 0x7a, 0xe2, 0xdd, 0x74, 0xe2,
	0xe3, 0xb0, 0xcc, 0x27, 0xd2, 0x7f, 0x90, 0xf0, 0x37, 0x69, 0xfc, 0x3f, 0x9c, 0x01, 0x2b, 0x3c,
	0x82, 0x5c, 0x05, 0x96, 0x8f, 0x90, 0x52, 0x4c, 0x96, 0x8f, 0x90, 0x5a, 0x48, 0x0e, 0xb5, 0x3d,
	0xfe, 0x35, 0xef, 0x9d, 0xec, 0x12, 0x58, 0xba, 0xb6, 0x8b, 0xa5, 0x4a, 0xf6, 0xa0, 0x88, 0xa5,
	0x4f, 0xb2, 0x99, 0x76, 0x52, 0x81, 0xec, 0xfd, 0x6b, 0x30, 0x38, 0x3b, 0xca, 0x64, 0x55, 0xaa,
	0xac, 0xa6, 0xd8, 0x51, 0x0a, 0xc1, 0x6e, 0xce, 0xd9, 0xbb, 0xf9, 0x67, 0xef, 0x26, 0x39, 0x64,
	0xca, 0x26, 0x94, 0x5f, 0x53, 0x94, 0x4d, 0x2e, 0xe4, 0xa6, 0x28, 0x5b, 0xa2, 0x82, 0xab, 0xce,
	0x91, 0xef, 0xb0, 0xb8, 0x90, 0xfc, 0xc3, 0x04, 0x49, 0x86, 0x29, 0xa9, 0xff, 0xe5, 0x68, 0x3e,
	0x9a, 0x09, 0x0f, 0xf7, 0xba, 0x82, 0x66, 0x76, 0x15, 0x87, 0x48, 0xe5, 0x84, 0xdc, 0xc2, 0x53,
	0xf3, 0xc9, 0xec, 0xc8, 0xa9, 0xee, 0x9d, 0x22, 0xe4, 0xba, 0xf7, 0xa0, 0x7c, 0x94, 0xeb, 0xde,
	0xc3, 0x42, 0x0c, 0x0b, 0xa2, 0x84, 0xec, 0x9c, 0x24, 0xaf, 0x55, 0x48, 0xef, 0x9b, 0xf7, 0x72,
	0xe1, 0x94, 0xe6, 0xce, 0xc7, 0xaf, 0xb7, 0xcf, 0xec, 0x89, 0x6e, 0x9d, 0x6d, 0x7f, 0xf6, 0xcc,
	0xf7, 0xb7, 0x47, 0xf6, 0xf4, 0x29, 0xfe, 0x8b, 0x68, 0x64, 0x4f, 0x9e, 0x7a, 0x86, 0x7b, 0x69,
	0x8e, 0x0c, 0xef, 0x29, 0xff, 0x27, 0xa3, 0x93, 0x79, 0x84, 0x3f, 0xff, 0x53, 0x00, 0x00, 0x00,
	0xff, 0xff, 0xd3, 0xd0, 0xc0, 0xb4, 0x7b, 0x34, 0x00, 0x00,
}
