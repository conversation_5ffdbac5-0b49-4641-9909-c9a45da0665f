// Code generated by protoc-gen-go. DO NOT EDIT.
// source: risk-control/award-center/award-center.proto

package award_center // import "golang.52tt.com/protocol/services/risk-control/award-center"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type EAwardStatus int32

const (
	EAwardStatus_Handling EAwardStatus = 0
	EAwardStatus_Fail     EAwardStatus = 1
	EAwardStatus_Success  EAwardStatus = 2
	EAwardStatus_Rollback EAwardStatus = 3
)

var EAwardStatus_name = map[int32]string{
	0: "Handling",
	1: "Fail",
	2: "Success",
	3: "Rollback",
}
var EAwardStatus_value = map[string]int32{
	"Handling": 0,
	"Fail":     1,
	"Success":  2,
	"Rollback": 3,
}

func (x EAwardStatus) String() string {
	return proto.EnumName(EAwardStatus_name, int32(x))
}
func (EAwardStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{0}
}

type EGiftType int32

const (
	EGiftType_UnknownGiftType EGiftType = 0
	EGiftType_Headwear        EGiftType = 1
	EGiftType_Horse           EGiftType = 2
	EGiftType_Medal           EGiftType = 3
	EGiftType_UserDecoration  EGiftType = 4
	EGiftType_Nameplate       EGiftType = 5
	EGiftType_OfficialCert    EGiftType = 6
	EGiftType_ChannelInfoCard EGiftType = 7
)

var EGiftType_name = map[int32]string{
	0: "UnknownGiftType",
	1: "Headwear",
	2: "Horse",
	3: "Medal",
	4: "UserDecoration",
	5: "Nameplate",
	6: "OfficialCert",
	7: "ChannelInfoCard",
}
var EGiftType_value = map[string]int32{
	"UnknownGiftType": 0,
	"Headwear":        1,
	"Horse":           2,
	"Medal":           3,
	"UserDecoration":  4,
	"Nameplate":       5,
	"OfficialCert":    6,
	"ChannelInfoCard": 7,
}

func (x EGiftType) String() string {
	return proto.EnumName(EGiftType_name, int32(x))
}
func (EGiftType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{1}
}

type AwardExpireType int32

const (
	AwardExpireType_Natural AwardExpireType = 0
	AwardExpireType_DayEnd  AwardExpireType = 1
)

var AwardExpireType_name = map[int32]string{
	0: "Natural",
	1: "DayEnd",
}
var AwardExpireType_value = map[string]int32{
	"Natural": 0,
	"DayEnd":  1,
}

func (x AwardExpireType) String() string {
	return proto.EnumName(AwardExpireType_name, int32(x))
}
func (AwardExpireType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{2}
}

type AwardCallbackReq struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Status               uint32   `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AwardCallbackReq) Reset()         { *m = AwardCallbackReq{} }
func (m *AwardCallbackReq) String() string { return proto.CompactTextString(m) }
func (*AwardCallbackReq) ProtoMessage()    {}
func (*AwardCallbackReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{0}
}
func (m *AwardCallbackReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardCallbackReq.Unmarshal(m, b)
}
func (m *AwardCallbackReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardCallbackReq.Marshal(b, m, deterministic)
}
func (dst *AwardCallbackReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardCallbackReq.Merge(dst, src)
}
func (m *AwardCallbackReq) XXX_Size() int {
	return xxx_messageInfo_AwardCallbackReq.Size(m)
}
func (m *AwardCallbackReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardCallbackReq.DiscardUnknown(m)
}

var xxx_messageInfo_AwardCallbackReq proto.InternalMessageInfo

func (m *AwardCallbackReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *AwardCallbackReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type AwardCallbackResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AwardCallbackResp) Reset()         { *m = AwardCallbackResp{} }
func (m *AwardCallbackResp) String() string { return proto.CompactTextString(m) }
func (*AwardCallbackResp) ProtoMessage()    {}
func (*AwardCallbackResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{1}
}
func (m *AwardCallbackResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardCallbackResp.Unmarshal(m, b)
}
func (m *AwardCallbackResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardCallbackResp.Marshal(b, m, deterministic)
}
func (dst *AwardCallbackResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardCallbackResp.Merge(dst, src)
}
func (m *AwardCallbackResp) XXX_Size() int {
	return xxx_messageInfo_AwardCallbackResp.Size(m)
}
func (m *AwardCallbackResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardCallbackResp.DiscardUnknown(m)
}

var xxx_messageInfo_AwardCallbackResp proto.InternalMessageInfo

type HeadwearAwardExtra struct {
	CpUid                uint32   `protobuf:"varint,1,opt,name=cp_uid,json=cpUid,proto3" json:"cp_uid,omitempty"`
	PushMsgPrefix        string   `protobuf:"bytes,2,opt,name=push_msg_prefix,json=pushMsgPrefix,proto3" json:"push_msg_prefix,omitempty"`
	CustomText           string   `protobuf:"bytes,3,opt,name=custom_text,json=customText,proto3" json:"custom_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HeadwearAwardExtra) Reset()         { *m = HeadwearAwardExtra{} }
func (m *HeadwearAwardExtra) String() string { return proto.CompactTextString(m) }
func (*HeadwearAwardExtra) ProtoMessage()    {}
func (*HeadwearAwardExtra) Descriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{2}
}
func (m *HeadwearAwardExtra) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HeadwearAwardExtra.Unmarshal(m, b)
}
func (m *HeadwearAwardExtra) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HeadwearAwardExtra.Marshal(b, m, deterministic)
}
func (dst *HeadwearAwardExtra) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HeadwearAwardExtra.Merge(dst, src)
}
func (m *HeadwearAwardExtra) XXX_Size() int {
	return xxx_messageInfo_HeadwearAwardExtra.Size(m)
}
func (m *HeadwearAwardExtra) XXX_DiscardUnknown() {
	xxx_messageInfo_HeadwearAwardExtra.DiscardUnknown(m)
}

var xxx_messageInfo_HeadwearAwardExtra proto.InternalMessageInfo

func (m *HeadwearAwardExtra) GetCpUid() uint32 {
	if m != nil {
		return m.CpUid
	}
	return 0
}

func (m *HeadwearAwardExtra) GetPushMsgPrefix() string {
	if m != nil {
		return m.PushMsgPrefix
	}
	return ""
}

func (m *HeadwearAwardExtra) GetCustomText() string {
	if m != nil {
		return m.CustomText
	}
	return ""
}

type UserDecorationExtra struct {
	Version              string   `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserDecorationExtra) Reset()         { *m = UserDecorationExtra{} }
func (m *UserDecorationExtra) String() string { return proto.CompactTextString(m) }
func (*UserDecorationExtra) ProtoMessage()    {}
func (*UserDecorationExtra) Descriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{3}
}
func (m *UserDecorationExtra) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDecorationExtra.Unmarshal(m, b)
}
func (m *UserDecorationExtra) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDecorationExtra.Marshal(b, m, deterministic)
}
func (dst *UserDecorationExtra) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDecorationExtra.Merge(dst, src)
}
func (m *UserDecorationExtra) XXX_Size() int {
	return xxx_messageInfo_UserDecorationExtra.Size(m)
}
func (m *UserDecorationExtra) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDecorationExtra.DiscardUnknown(m)
}

var xxx_messageInfo_UserDecorationExtra proto.InternalMessageInfo

func (m *UserDecorationExtra) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

type NameplateExtra struct {
	PushMsgPrefix        string   `protobuf:"bytes,1,opt,name=push_msg_prefix,json=pushMsgPrefix,proto3" json:"push_msg_prefix,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NameplateExtra) Reset()         { *m = NameplateExtra{} }
func (m *NameplateExtra) String() string { return proto.CompactTextString(m) }
func (*NameplateExtra) ProtoMessage()    {}
func (*NameplateExtra) Descriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{4}
}
func (m *NameplateExtra) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NameplateExtra.Unmarshal(m, b)
}
func (m *NameplateExtra) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NameplateExtra.Marshal(b, m, deterministic)
}
func (dst *NameplateExtra) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NameplateExtra.Merge(dst, src)
}
func (m *NameplateExtra) XXX_Size() int {
	return xxx_messageInfo_NameplateExtra.Size(m)
}
func (m *NameplateExtra) XXX_DiscardUnknown() {
	xxx_messageInfo_NameplateExtra.DiscardUnknown(m)
}

var xxx_messageInfo_NameplateExtra proto.InternalMessageInfo

func (m *NameplateExtra) GetPushMsgPrefix() string {
	if m != nil {
		return m.PushMsgPrefix
	}
	return ""
}

type OfficialCertExtra struct {
	Attribute            uint32   `protobuf:"varint,1,opt,name=attribute,proto3" json:"attribute,omitempty"`
	Intro                string   `protobuf:"bytes,2,opt,name=intro,proto3" json:"intro,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OfficialCertExtra) Reset()         { *m = OfficialCertExtra{} }
func (m *OfficialCertExtra) String() string { return proto.CompactTextString(m) }
func (*OfficialCertExtra) ProtoMessage()    {}
func (*OfficialCertExtra) Descriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{5}
}
func (m *OfficialCertExtra) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfficialCertExtra.Unmarshal(m, b)
}
func (m *OfficialCertExtra) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfficialCertExtra.Marshal(b, m, deterministic)
}
func (dst *OfficialCertExtra) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfficialCertExtra.Merge(dst, src)
}
func (m *OfficialCertExtra) XXX_Size() int {
	return xxx_messageInfo_OfficialCertExtra.Size(m)
}
func (m *OfficialCertExtra) XXX_DiscardUnknown() {
	xxx_messageInfo_OfficialCertExtra.DiscardUnknown(m)
}

var xxx_messageInfo_OfficialCertExtra proto.InternalMessageInfo

func (m *OfficialCertExtra) GetAttribute() uint32 {
	if m != nil {
		return m.Attribute
	}
	return 0
}

func (m *OfficialCertExtra) GetIntro() string {
	if m != nil {
		return m.Intro
	}
	return ""
}

type AwardReq struct {
	OrderId              string               `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	TargetUid            uint32               `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	BusinessId           uint32               `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	GiftId               string               `protobuf:"bytes,4,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftType             uint32               `protobuf:"varint,5,opt,name=gift_type,json=giftType,proto3" json:"gift_type,omitempty"`
	HoldingDay           uint32               `protobuf:"varint,6,opt,name=holding_day,json=holdingDay,proto3" json:"holding_day,omitempty"`
	OutsideTs            uint32               `protobuf:"varint,7,opt,name=outside_ts,json=outsideTs,proto3" json:"outside_ts,omitempty"`
	HeadwearExtra        *HeadwearAwardExtra  `protobuf:"bytes,8,opt,name=headwear_extra,json=headwearExtra,proto3" json:"headwear_extra,omitempty"`
	UserDecorationExtra  *UserDecorationExtra `protobuf:"bytes,9,opt,name=user_decoration_extra,json=userDecorationExtra,proto3" json:"user_decoration_extra,omitempty"`
	NameplateExtra       *NameplateExtra      `protobuf:"bytes,10,opt,name=nameplate_extra,json=nameplateExtra,proto3" json:"nameplate_extra,omitempty"`
	OfficialCertExtra    *OfficialCertExtra   `protobuf:"bytes,11,opt,name=official_cert_extra,json=officialCertExtra,proto3" json:"official_cert_extra,omitempty"`
	ExpireType           uint32               `protobuf:"varint,12,opt,name=expire_type,json=expireType,proto3" json:"expire_type,omitempty"`
	AutoWear             bool                 `protobuf:"varint,13,opt,name=auto_wear,json=autoWear,proto3" json:"auto_wear,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *AwardReq) Reset()         { *m = AwardReq{} }
func (m *AwardReq) String() string { return proto.CompactTextString(m) }
func (*AwardReq) ProtoMessage()    {}
func (*AwardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{6}
}
func (m *AwardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardReq.Unmarshal(m, b)
}
func (m *AwardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardReq.Marshal(b, m, deterministic)
}
func (dst *AwardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardReq.Merge(dst, src)
}
func (m *AwardReq) XXX_Size() int {
	return xxx_messageInfo_AwardReq.Size(m)
}
func (m *AwardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardReq.DiscardUnknown(m)
}

var xxx_messageInfo_AwardReq proto.InternalMessageInfo

func (m *AwardReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *AwardReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *AwardReq) GetBusinessId() uint32 {
	if m != nil {
		return m.BusinessId
	}
	return 0
}

func (m *AwardReq) GetGiftId() string {
	if m != nil {
		return m.GiftId
	}
	return ""
}

func (m *AwardReq) GetGiftType() uint32 {
	if m != nil {
		return m.GiftType
	}
	return 0
}

func (m *AwardReq) GetHoldingDay() uint32 {
	if m != nil {
		return m.HoldingDay
	}
	return 0
}

func (m *AwardReq) GetOutsideTs() uint32 {
	if m != nil {
		return m.OutsideTs
	}
	return 0
}

func (m *AwardReq) GetHeadwearExtra() *HeadwearAwardExtra {
	if m != nil {
		return m.HeadwearExtra
	}
	return nil
}

func (m *AwardReq) GetUserDecorationExtra() *UserDecorationExtra {
	if m != nil {
		return m.UserDecorationExtra
	}
	return nil
}

func (m *AwardReq) GetNameplateExtra() *NameplateExtra {
	if m != nil {
		return m.NameplateExtra
	}
	return nil
}

func (m *AwardReq) GetOfficialCertExtra() *OfficialCertExtra {
	if m != nil {
		return m.OfficialCertExtra
	}
	return nil
}

func (m *AwardReq) GetExpireType() uint32 {
	if m != nil {
		return m.ExpireType
	}
	return 0
}

func (m *AwardReq) GetAutoWear() bool {
	if m != nil {
		return m.AutoWear
	}
	return false
}

type AwardResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AwardResp) Reset()         { *m = AwardResp{} }
func (m *AwardResp) String() string { return proto.CompactTextString(m) }
func (*AwardResp) ProtoMessage()    {}
func (*AwardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{7}
}
func (m *AwardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardResp.Unmarshal(m, b)
}
func (m *AwardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardResp.Marshal(b, m, deterministic)
}
func (dst *AwardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardResp.Merge(dst, src)
}
func (m *AwardResp) XXX_Size() int {
	return xxx_messageInfo_AwardResp.Size(m)
}
func (m *AwardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardResp.DiscardUnknown(m)
}

var xxx_messageInfo_AwardResp proto.InternalMessageInfo

type RollbackAwardReq struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RollbackAwardReq) Reset()         { *m = RollbackAwardReq{} }
func (m *RollbackAwardReq) String() string { return proto.CompactTextString(m) }
func (*RollbackAwardReq) ProtoMessage()    {}
func (*RollbackAwardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{8}
}
func (m *RollbackAwardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RollbackAwardReq.Unmarshal(m, b)
}
func (m *RollbackAwardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RollbackAwardReq.Marshal(b, m, deterministic)
}
func (dst *RollbackAwardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RollbackAwardReq.Merge(dst, src)
}
func (m *RollbackAwardReq) XXX_Size() int {
	return xxx_messageInfo_RollbackAwardReq.Size(m)
}
func (m *RollbackAwardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RollbackAwardReq.DiscardUnknown(m)
}

var xxx_messageInfo_RollbackAwardReq proto.InternalMessageInfo

func (m *RollbackAwardReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type RollbackAwardResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RollbackAwardResp) Reset()         { *m = RollbackAwardResp{} }
func (m *RollbackAwardResp) String() string { return proto.CompactTextString(m) }
func (*RollbackAwardResp) ProtoMessage()    {}
func (*RollbackAwardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{9}
}
func (m *RollbackAwardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RollbackAwardResp.Unmarshal(m, b)
}
func (m *RollbackAwardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RollbackAwardResp.Marshal(b, m, deterministic)
}
func (dst *RollbackAwardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RollbackAwardResp.Merge(dst, src)
}
func (m *RollbackAwardResp) XXX_Size() int {
	return xxx_messageInfo_RollbackAwardResp.Size(m)
}
func (m *RollbackAwardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RollbackAwardResp.DiscardUnknown(m)
}

var xxx_messageInfo_RollbackAwardResp proto.InternalMessageInfo

type AwardOrder struct {
	OrderId              string               `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	AwardStatus          uint32               `protobuf:"varint,2,opt,name=award_status,json=awardStatus,proto3" json:"award_status,omitempty"`
	TargetUid            uint32               `protobuf:"varint,3,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	BusinessId           uint32               `protobuf:"varint,4,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	GiftId               string               `protobuf:"bytes,5,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftType             uint32               `protobuf:"varint,6,opt,name=gift_type,json=giftType,proto3" json:"gift_type,omitempty"`
	HoldingDay           uint32               `protobuf:"varint,7,opt,name=holding_day,json=holdingDay,proto3" json:"holding_day,omitempty"`
	UpdateTs             uint32               `protobuf:"varint,8,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	OutsideTs            uint32               `protobuf:"varint,9,opt,name=outside_ts,json=outsideTs,proto3" json:"outside_ts,omitempty"`
	CreateTs             uint32               `protobuf:"varint,10,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"`
	HeadwearExtra        *HeadwearAwardExtra  `protobuf:"bytes,11,opt,name=headwear_extra,json=headwearExtra,proto3" json:"headwear_extra,omitempty"`
	UserDecorationExtra  *UserDecorationExtra `protobuf:"bytes,12,opt,name=user_decoration_extra,json=userDecorationExtra,proto3" json:"user_decoration_extra,omitempty"`
	NameplateExtra       *NameplateExtra      `protobuf:"bytes,13,opt,name=nameplate_extra,json=nameplateExtra,proto3" json:"nameplate_extra,omitempty"`
	OfficialCertExtra    *OfficialCertExtra   `protobuf:"bytes,14,opt,name=official_cert_extra,json=officialCertExtra,proto3" json:"official_cert_extra,omitempty"`
	ExpireType           uint32               `protobuf:"varint,15,opt,name=expire_type,json=expireType,proto3" json:"expire_type,omitempty"`
	AutoWear             bool                 `protobuf:"varint,16,opt,name=auto_wear,json=autoWear,proto3" json:"auto_wear,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *AwardOrder) Reset()         { *m = AwardOrder{} }
func (m *AwardOrder) String() string { return proto.CompactTextString(m) }
func (*AwardOrder) ProtoMessage()    {}
func (*AwardOrder) Descriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{10}
}
func (m *AwardOrder) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardOrder.Unmarshal(m, b)
}
func (m *AwardOrder) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardOrder.Marshal(b, m, deterministic)
}
func (dst *AwardOrder) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardOrder.Merge(dst, src)
}
func (m *AwardOrder) XXX_Size() int {
	return xxx_messageInfo_AwardOrder.Size(m)
}
func (m *AwardOrder) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardOrder.DiscardUnknown(m)
}

var xxx_messageInfo_AwardOrder proto.InternalMessageInfo

func (m *AwardOrder) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *AwardOrder) GetAwardStatus() uint32 {
	if m != nil {
		return m.AwardStatus
	}
	return 0
}

func (m *AwardOrder) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *AwardOrder) GetBusinessId() uint32 {
	if m != nil {
		return m.BusinessId
	}
	return 0
}

func (m *AwardOrder) GetGiftId() string {
	if m != nil {
		return m.GiftId
	}
	return ""
}

func (m *AwardOrder) GetGiftType() uint32 {
	if m != nil {
		return m.GiftType
	}
	return 0
}

func (m *AwardOrder) GetHoldingDay() uint32 {
	if m != nil {
		return m.HoldingDay
	}
	return 0
}

func (m *AwardOrder) GetUpdateTs() uint32 {
	if m != nil {
		return m.UpdateTs
	}
	return 0
}

func (m *AwardOrder) GetOutsideTs() uint32 {
	if m != nil {
		return m.OutsideTs
	}
	return 0
}

func (m *AwardOrder) GetCreateTs() uint32 {
	if m != nil {
		return m.CreateTs
	}
	return 0
}

func (m *AwardOrder) GetHeadwearExtra() *HeadwearAwardExtra {
	if m != nil {
		return m.HeadwearExtra
	}
	return nil
}

func (m *AwardOrder) GetUserDecorationExtra() *UserDecorationExtra {
	if m != nil {
		return m.UserDecorationExtra
	}
	return nil
}

func (m *AwardOrder) GetNameplateExtra() *NameplateExtra {
	if m != nil {
		return m.NameplateExtra
	}
	return nil
}

func (m *AwardOrder) GetOfficialCertExtra() *OfficialCertExtra {
	if m != nil {
		return m.OfficialCertExtra
	}
	return nil
}

func (m *AwardOrder) GetExpireType() uint32 {
	if m != nil {
		return m.ExpireType
	}
	return 0
}

func (m *AwardOrder) GetAutoWear() bool {
	if m != nil {
		return m.AutoWear
	}
	return false
}

// 业务配置
type BusinessConf struct {
	BusinessId           uint32   `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Desc                 string   `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	SourceType           uint32   `protobuf:"varint,4,opt,name=source_type,json=sourceType,proto3" json:"source_type,omitempty"`
	Invalid              bool     `protobuf:"varint,5,opt,name=invalid,proto3" json:"invalid,omitempty"`
	IsDel                bool     `protobuf:"varint,6,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
	BeginTs              uint32   `protobuf:"varint,7,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,8,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BusinessConf) Reset()         { *m = BusinessConf{} }
func (m *BusinessConf) String() string { return proto.CompactTextString(m) }
func (*BusinessConf) ProtoMessage()    {}
func (*BusinessConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{11}
}
func (m *BusinessConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BusinessConf.Unmarshal(m, b)
}
func (m *BusinessConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BusinessConf.Marshal(b, m, deterministic)
}
func (dst *BusinessConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BusinessConf.Merge(dst, src)
}
func (m *BusinessConf) XXX_Size() int {
	return xxx_messageInfo_BusinessConf.Size(m)
}
func (m *BusinessConf) XXX_DiscardUnknown() {
	xxx_messageInfo_BusinessConf.DiscardUnknown(m)
}

var xxx_messageInfo_BusinessConf proto.InternalMessageInfo

func (m *BusinessConf) GetBusinessId() uint32 {
	if m != nil {
		return m.BusinessId
	}
	return 0
}

func (m *BusinessConf) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *BusinessConf) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *BusinessConf) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

func (m *BusinessConf) GetInvalid() bool {
	if m != nil {
		return m.Invalid
	}
	return false
}

func (m *BusinessConf) GetIsDel() bool {
	if m != nil {
		return m.IsDel
	}
	return false
}

func (m *BusinessConf) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *BusinessConf) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

// 增加业务配置
type AddBusinessConfReq struct {
	Name       string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Desc       string `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	SourceType uint32 `protobuf:"varint,3,opt,name=source_type,json=sourceType,proto3" json:"source_type,omitempty"`
	// uint32 default_hour_limit = 4;    // 业务默认小时发奖天数限制
	// uint32 default_daily_limit = 5;   // 业务默认日发奖天数限制
	// uint32 default_single_limit = 4;  // 业务默认单次发奖天数限制
	BeginTs              uint32   `protobuf:"varint,4,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,5,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddBusinessConfReq) Reset()         { *m = AddBusinessConfReq{} }
func (m *AddBusinessConfReq) String() string { return proto.CompactTextString(m) }
func (*AddBusinessConfReq) ProtoMessage()    {}
func (*AddBusinessConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{12}
}
func (m *AddBusinessConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddBusinessConfReq.Unmarshal(m, b)
}
func (m *AddBusinessConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddBusinessConfReq.Marshal(b, m, deterministic)
}
func (dst *AddBusinessConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddBusinessConfReq.Merge(dst, src)
}
func (m *AddBusinessConfReq) XXX_Size() int {
	return xxx_messageInfo_AddBusinessConfReq.Size(m)
}
func (m *AddBusinessConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddBusinessConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddBusinessConfReq proto.InternalMessageInfo

func (m *AddBusinessConfReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AddBusinessConfReq) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *AddBusinessConfReq) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

func (m *AddBusinessConfReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *AddBusinessConfReq) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

type AddBusinessConfResp struct {
	BusinessId           uint32   `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddBusinessConfResp) Reset()         { *m = AddBusinessConfResp{} }
func (m *AddBusinessConfResp) String() string { return proto.CompactTextString(m) }
func (*AddBusinessConfResp) ProtoMessage()    {}
func (*AddBusinessConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{13}
}
func (m *AddBusinessConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddBusinessConfResp.Unmarshal(m, b)
}
func (m *AddBusinessConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddBusinessConfResp.Marshal(b, m, deterministic)
}
func (dst *AddBusinessConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddBusinessConfResp.Merge(dst, src)
}
func (m *AddBusinessConfResp) XXX_Size() int {
	return xxx_messageInfo_AddBusinessConfResp.Size(m)
}
func (m *AddBusinessConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddBusinessConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddBusinessConfResp proto.InternalMessageInfo

func (m *AddBusinessConfResp) GetBusinessId() uint32 {
	if m != nil {
		return m.BusinessId
	}
	return 0
}

type GetBusinessConfReq struct {
	BusinessId           uint32   `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBusinessConfReq) Reset()         { *m = GetBusinessConfReq{} }
func (m *GetBusinessConfReq) String() string { return proto.CompactTextString(m) }
func (*GetBusinessConfReq) ProtoMessage()    {}
func (*GetBusinessConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{14}
}
func (m *GetBusinessConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBusinessConfReq.Unmarshal(m, b)
}
func (m *GetBusinessConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBusinessConfReq.Marshal(b, m, deterministic)
}
func (dst *GetBusinessConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBusinessConfReq.Merge(dst, src)
}
func (m *GetBusinessConfReq) XXX_Size() int {
	return xxx_messageInfo_GetBusinessConfReq.Size(m)
}
func (m *GetBusinessConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBusinessConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBusinessConfReq proto.InternalMessageInfo

func (m *GetBusinessConfReq) GetBusinessId() uint32 {
	if m != nil {
		return m.BusinessId
	}
	return 0
}

type GetBusinessConfResp struct {
	Conf                 *BusinessConf `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetBusinessConfResp) Reset()         { *m = GetBusinessConfResp{} }
func (m *GetBusinessConfResp) String() string { return proto.CompactTextString(m) }
func (*GetBusinessConfResp) ProtoMessage()    {}
func (*GetBusinessConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{15}
}
func (m *GetBusinessConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBusinessConfResp.Unmarshal(m, b)
}
func (m *GetBusinessConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBusinessConfResp.Marshal(b, m, deterministic)
}
func (dst *GetBusinessConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBusinessConfResp.Merge(dst, src)
}
func (m *GetBusinessConfResp) XXX_Size() int {
	return xxx_messageInfo_GetBusinessConfResp.Size(m)
}
func (m *GetBusinessConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBusinessConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBusinessConfResp proto.InternalMessageInfo

func (m *GetBusinessConfResp) GetConf() *BusinessConf {
	if m != nil {
		return m.Conf
	}
	return nil
}

type GetBusinessConfListReq struct {
	Page                 uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBusinessConfListReq) Reset()         { *m = GetBusinessConfListReq{} }
func (m *GetBusinessConfListReq) String() string { return proto.CompactTextString(m) }
func (*GetBusinessConfListReq) ProtoMessage()    {}
func (*GetBusinessConfListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{16}
}
func (m *GetBusinessConfListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBusinessConfListReq.Unmarshal(m, b)
}
func (m *GetBusinessConfListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBusinessConfListReq.Marshal(b, m, deterministic)
}
func (dst *GetBusinessConfListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBusinessConfListReq.Merge(dst, src)
}
func (m *GetBusinessConfListReq) XXX_Size() int {
	return xxx_messageInfo_GetBusinessConfListReq.Size(m)
}
func (m *GetBusinessConfListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBusinessConfListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBusinessConfListReq proto.InternalMessageInfo

func (m *GetBusinessConfListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetBusinessConfListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetBusinessConfListResp struct {
	ConfList             []*BusinessConf `protobuf:"bytes,1,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	Total                uint32          `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetBusinessConfListResp) Reset()         { *m = GetBusinessConfListResp{} }
func (m *GetBusinessConfListResp) String() string { return proto.CompactTextString(m) }
func (*GetBusinessConfListResp) ProtoMessage()    {}
func (*GetBusinessConfListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{17}
}
func (m *GetBusinessConfListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBusinessConfListResp.Unmarshal(m, b)
}
func (m *GetBusinessConfListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBusinessConfListResp.Marshal(b, m, deterministic)
}
func (dst *GetBusinessConfListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBusinessConfListResp.Merge(dst, src)
}
func (m *GetBusinessConfListResp) XXX_Size() int {
	return xxx_messageInfo_GetBusinessConfListResp.Size(m)
}
func (m *GetBusinessConfListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBusinessConfListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBusinessConfListResp proto.InternalMessageInfo

func (m *GetBusinessConfListResp) GetConfList() []*BusinessConf {
	if m != nil {
		return m.ConfList
	}
	return nil
}

func (m *GetBusinessConfListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type UpdateBusinessConfReq struct {
	Conf                 *BusinessConf `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UpdateBusinessConfReq) Reset()         { *m = UpdateBusinessConfReq{} }
func (m *UpdateBusinessConfReq) String() string { return proto.CompactTextString(m) }
func (*UpdateBusinessConfReq) ProtoMessage()    {}
func (*UpdateBusinessConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{18}
}
func (m *UpdateBusinessConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateBusinessConfReq.Unmarshal(m, b)
}
func (m *UpdateBusinessConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateBusinessConfReq.Marshal(b, m, deterministic)
}
func (dst *UpdateBusinessConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateBusinessConfReq.Merge(dst, src)
}
func (m *UpdateBusinessConfReq) XXX_Size() int {
	return xxx_messageInfo_UpdateBusinessConfReq.Size(m)
}
func (m *UpdateBusinessConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateBusinessConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateBusinessConfReq proto.InternalMessageInfo

func (m *UpdateBusinessConfReq) GetConf() *BusinessConf {
	if m != nil {
		return m.Conf
	}
	return nil
}

type UpdateBusinessConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateBusinessConfResp) Reset()         { *m = UpdateBusinessConfResp{} }
func (m *UpdateBusinessConfResp) String() string { return proto.CompactTextString(m) }
func (*UpdateBusinessConfResp) ProtoMessage()    {}
func (*UpdateBusinessConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{19}
}
func (m *UpdateBusinessConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateBusinessConfResp.Unmarshal(m, b)
}
func (m *UpdateBusinessConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateBusinessConfResp.Marshal(b, m, deterministic)
}
func (dst *UpdateBusinessConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateBusinessConfResp.Merge(dst, src)
}
func (m *UpdateBusinessConfResp) XXX_Size() int {
	return xxx_messageInfo_UpdateBusinessConfResp.Size(m)
}
func (m *UpdateBusinessConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateBusinessConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateBusinessConfResp proto.InternalMessageInfo

type DelBusinessConfReq struct {
	BusinessId           uint32   `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelBusinessConfReq) Reset()         { *m = DelBusinessConfReq{} }
func (m *DelBusinessConfReq) String() string { return proto.CompactTextString(m) }
func (*DelBusinessConfReq) ProtoMessage()    {}
func (*DelBusinessConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{20}
}
func (m *DelBusinessConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelBusinessConfReq.Unmarshal(m, b)
}
func (m *DelBusinessConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelBusinessConfReq.Marshal(b, m, deterministic)
}
func (dst *DelBusinessConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelBusinessConfReq.Merge(dst, src)
}
func (m *DelBusinessConfReq) XXX_Size() int {
	return xxx_messageInfo_DelBusinessConfReq.Size(m)
}
func (m *DelBusinessConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelBusinessConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelBusinessConfReq proto.InternalMessageInfo

func (m *DelBusinessConfReq) GetBusinessId() uint32 {
	if m != nil {
		return m.BusinessId
	}
	return 0
}

type DelBusinessConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelBusinessConfResp) Reset()         { *m = DelBusinessConfResp{} }
func (m *DelBusinessConfResp) String() string { return proto.CompactTextString(m) }
func (*DelBusinessConfResp) ProtoMessage()    {}
func (*DelBusinessConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{21}
}
func (m *DelBusinessConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelBusinessConfResp.Unmarshal(m, b)
}
func (m *DelBusinessConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelBusinessConfResp.Marshal(b, m, deterministic)
}
func (dst *DelBusinessConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelBusinessConfResp.Merge(dst, src)
}
func (m *DelBusinessConfResp) XXX_Size() int {
	return xxx_messageInfo_DelBusinessConfResp.Size(m)
}
func (m *DelBusinessConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelBusinessConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelBusinessConfResp proto.InternalMessageInfo

// 业务奖励配置
type AwardConf struct {
	BusinessId           uint32   `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	GiftId               string   `protobuf:"bytes,2,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftType             uint32   `protobuf:"varint,3,opt,name=gift_type,json=giftType,proto3" json:"gift_type,omitempty"`
	ItemDesc             string   `protobuf:"bytes,4,opt,name=item_desc,json=itemDesc,proto3" json:"item_desc,omitempty"`
	SingleLimit          uint32   `protobuf:"varint,5,opt,name=single_limit,json=singleLimit,proto3" json:"single_limit,omitempty"`
	DailyAwardCntLimit   uint32   `protobuf:"varint,6,opt,name=daily_award_cnt_limit,json=dailyAwardCntLimit,proto3" json:"daily_award_cnt_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AwardConf) Reset()         { *m = AwardConf{} }
func (m *AwardConf) String() string { return proto.CompactTextString(m) }
func (*AwardConf) ProtoMessage()    {}
func (*AwardConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{22}
}
func (m *AwardConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardConf.Unmarshal(m, b)
}
func (m *AwardConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardConf.Marshal(b, m, deterministic)
}
func (dst *AwardConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardConf.Merge(dst, src)
}
func (m *AwardConf) XXX_Size() int {
	return xxx_messageInfo_AwardConf.Size(m)
}
func (m *AwardConf) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardConf.DiscardUnknown(m)
}

var xxx_messageInfo_AwardConf proto.InternalMessageInfo

func (m *AwardConf) GetBusinessId() uint32 {
	if m != nil {
		return m.BusinessId
	}
	return 0
}

func (m *AwardConf) GetGiftId() string {
	if m != nil {
		return m.GiftId
	}
	return ""
}

func (m *AwardConf) GetGiftType() uint32 {
	if m != nil {
		return m.GiftType
	}
	return 0
}

func (m *AwardConf) GetItemDesc() string {
	if m != nil {
		return m.ItemDesc
	}
	return ""
}

func (m *AwardConf) GetSingleLimit() uint32 {
	if m != nil {
		return m.SingleLimit
	}
	return 0
}

func (m *AwardConf) GetDailyAwardCntLimit() uint32 {
	if m != nil {
		return m.DailyAwardCntLimit
	}
	return 0
}

// 增加业务奖励配置
type AddAwardConfReq struct {
	Conf                 *AwardConf `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *AddAwardConfReq) Reset()         { *m = AddAwardConfReq{} }
func (m *AddAwardConfReq) String() string { return proto.CompactTextString(m) }
func (*AddAwardConfReq) ProtoMessage()    {}
func (*AddAwardConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{23}
}
func (m *AddAwardConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddAwardConfReq.Unmarshal(m, b)
}
func (m *AddAwardConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddAwardConfReq.Marshal(b, m, deterministic)
}
func (dst *AddAwardConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddAwardConfReq.Merge(dst, src)
}
func (m *AddAwardConfReq) XXX_Size() int {
	return xxx_messageInfo_AddAwardConfReq.Size(m)
}
func (m *AddAwardConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddAwardConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddAwardConfReq proto.InternalMessageInfo

func (m *AddAwardConfReq) GetConf() *AwardConf {
	if m != nil {
		return m.Conf
	}
	return nil
}

type AddAwardConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddAwardConfResp) Reset()         { *m = AddAwardConfResp{} }
func (m *AddAwardConfResp) String() string { return proto.CompactTextString(m) }
func (*AddAwardConfResp) ProtoMessage()    {}
func (*AddAwardConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{24}
}
func (m *AddAwardConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddAwardConfResp.Unmarshal(m, b)
}
func (m *AddAwardConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddAwardConfResp.Marshal(b, m, deterministic)
}
func (dst *AddAwardConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddAwardConfResp.Merge(dst, src)
}
func (m *AddAwardConfResp) XXX_Size() int {
	return xxx_messageInfo_AddAwardConfResp.Size(m)
}
func (m *AddAwardConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddAwardConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddAwardConfResp proto.InternalMessageInfo

type GetAwardConfReq struct {
	BusinessId           uint32   `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	GiftId               string   `protobuf:"bytes,2,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftType             uint32   `protobuf:"varint,3,opt,name=gift_type,json=giftType,proto3" json:"gift_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAwardConfReq) Reset()         { *m = GetAwardConfReq{} }
func (m *GetAwardConfReq) String() string { return proto.CompactTextString(m) }
func (*GetAwardConfReq) ProtoMessage()    {}
func (*GetAwardConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{25}
}
func (m *GetAwardConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAwardConfReq.Unmarshal(m, b)
}
func (m *GetAwardConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAwardConfReq.Marshal(b, m, deterministic)
}
func (dst *GetAwardConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAwardConfReq.Merge(dst, src)
}
func (m *GetAwardConfReq) XXX_Size() int {
	return xxx_messageInfo_GetAwardConfReq.Size(m)
}
func (m *GetAwardConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAwardConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAwardConfReq proto.InternalMessageInfo

func (m *GetAwardConfReq) GetBusinessId() uint32 {
	if m != nil {
		return m.BusinessId
	}
	return 0
}

func (m *GetAwardConfReq) GetGiftId() string {
	if m != nil {
		return m.GiftId
	}
	return ""
}

func (m *GetAwardConfReq) GetGiftType() uint32 {
	if m != nil {
		return m.GiftType
	}
	return 0
}

type GetAwardConfResp struct {
	Conf                 *AwardConf `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetAwardConfResp) Reset()         { *m = GetAwardConfResp{} }
func (m *GetAwardConfResp) String() string { return proto.CompactTextString(m) }
func (*GetAwardConfResp) ProtoMessage()    {}
func (*GetAwardConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{26}
}
func (m *GetAwardConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAwardConfResp.Unmarshal(m, b)
}
func (m *GetAwardConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAwardConfResp.Marshal(b, m, deterministic)
}
func (dst *GetAwardConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAwardConfResp.Merge(dst, src)
}
func (m *GetAwardConfResp) XXX_Size() int {
	return xxx_messageInfo_GetAwardConfResp.Size(m)
}
func (m *GetAwardConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAwardConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAwardConfResp proto.InternalMessageInfo

func (m *GetAwardConfResp) GetConf() *AwardConf {
	if m != nil {
		return m.Conf
	}
	return nil
}

type GetAwardConfListReq struct {
	BusinessId           uint32   `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	GiftType             uint32   `protobuf:"varint,2,opt,name=gift_type,json=giftType,proto3" json:"gift_type,omitempty"`
	Page                 uint32   `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAwardConfListReq) Reset()         { *m = GetAwardConfListReq{} }
func (m *GetAwardConfListReq) String() string { return proto.CompactTextString(m) }
func (*GetAwardConfListReq) ProtoMessage()    {}
func (*GetAwardConfListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{27}
}
func (m *GetAwardConfListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAwardConfListReq.Unmarshal(m, b)
}
func (m *GetAwardConfListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAwardConfListReq.Marshal(b, m, deterministic)
}
func (dst *GetAwardConfListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAwardConfListReq.Merge(dst, src)
}
func (m *GetAwardConfListReq) XXX_Size() int {
	return xxx_messageInfo_GetAwardConfListReq.Size(m)
}
func (m *GetAwardConfListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAwardConfListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAwardConfListReq proto.InternalMessageInfo

func (m *GetAwardConfListReq) GetBusinessId() uint32 {
	if m != nil {
		return m.BusinessId
	}
	return 0
}

func (m *GetAwardConfListReq) GetGiftType() uint32 {
	if m != nil {
		return m.GiftType
	}
	return 0
}

func (m *GetAwardConfListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetAwardConfListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetAwardConfListResp struct {
	ConfList             []*AwardConf `protobuf:"bytes,1,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	Total                uint32       `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetAwardConfListResp) Reset()         { *m = GetAwardConfListResp{} }
func (m *GetAwardConfListResp) String() string { return proto.CompactTextString(m) }
func (*GetAwardConfListResp) ProtoMessage()    {}
func (*GetAwardConfListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{28}
}
func (m *GetAwardConfListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAwardConfListResp.Unmarshal(m, b)
}
func (m *GetAwardConfListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAwardConfListResp.Marshal(b, m, deterministic)
}
func (dst *GetAwardConfListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAwardConfListResp.Merge(dst, src)
}
func (m *GetAwardConfListResp) XXX_Size() int {
	return xxx_messageInfo_GetAwardConfListResp.Size(m)
}
func (m *GetAwardConfListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAwardConfListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAwardConfListResp proto.InternalMessageInfo

func (m *GetAwardConfListResp) GetConfList() []*AwardConf {
	if m != nil {
		return m.ConfList
	}
	return nil
}

func (m *GetAwardConfListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type UpdateAwardConfReq struct {
	Conf                 *AwardConf `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *UpdateAwardConfReq) Reset()         { *m = UpdateAwardConfReq{} }
func (m *UpdateAwardConfReq) String() string { return proto.CompactTextString(m) }
func (*UpdateAwardConfReq) ProtoMessage()    {}
func (*UpdateAwardConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{29}
}
func (m *UpdateAwardConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAwardConfReq.Unmarshal(m, b)
}
func (m *UpdateAwardConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAwardConfReq.Marshal(b, m, deterministic)
}
func (dst *UpdateAwardConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAwardConfReq.Merge(dst, src)
}
func (m *UpdateAwardConfReq) XXX_Size() int {
	return xxx_messageInfo_UpdateAwardConfReq.Size(m)
}
func (m *UpdateAwardConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAwardConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAwardConfReq proto.InternalMessageInfo

func (m *UpdateAwardConfReq) GetConf() *AwardConf {
	if m != nil {
		return m.Conf
	}
	return nil
}

type UpdateAwardConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateAwardConfResp) Reset()         { *m = UpdateAwardConfResp{} }
func (m *UpdateAwardConfResp) String() string { return proto.CompactTextString(m) }
func (*UpdateAwardConfResp) ProtoMessage()    {}
func (*UpdateAwardConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{30}
}
func (m *UpdateAwardConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAwardConfResp.Unmarshal(m, b)
}
func (m *UpdateAwardConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAwardConfResp.Marshal(b, m, deterministic)
}
func (dst *UpdateAwardConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAwardConfResp.Merge(dst, src)
}
func (m *UpdateAwardConfResp) XXX_Size() int {
	return xxx_messageInfo_UpdateAwardConfResp.Size(m)
}
func (m *UpdateAwardConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAwardConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAwardConfResp proto.InternalMessageInfo

type DelAwardConfReq struct {
	BusinessId           uint32   `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	GiftId               string   `protobuf:"bytes,2,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftType             uint32   `protobuf:"varint,3,opt,name=gift_type,json=giftType,proto3" json:"gift_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelAwardConfReq) Reset()         { *m = DelAwardConfReq{} }
func (m *DelAwardConfReq) String() string { return proto.CompactTextString(m) }
func (*DelAwardConfReq) ProtoMessage()    {}
func (*DelAwardConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{31}
}
func (m *DelAwardConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelAwardConfReq.Unmarshal(m, b)
}
func (m *DelAwardConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelAwardConfReq.Marshal(b, m, deterministic)
}
func (dst *DelAwardConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelAwardConfReq.Merge(dst, src)
}
func (m *DelAwardConfReq) XXX_Size() int {
	return xxx_messageInfo_DelAwardConfReq.Size(m)
}
func (m *DelAwardConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelAwardConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelAwardConfReq proto.InternalMessageInfo

func (m *DelAwardConfReq) GetBusinessId() uint32 {
	if m != nil {
		return m.BusinessId
	}
	return 0
}

func (m *DelAwardConfReq) GetGiftId() string {
	if m != nil {
		return m.GiftId
	}
	return ""
}

func (m *DelAwardConfReq) GetGiftType() uint32 {
	if m != nil {
		return m.GiftType
	}
	return 0
}

type DelAwardConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelAwardConfResp) Reset()         { *m = DelAwardConfResp{} }
func (m *DelAwardConfResp) String() string { return proto.CompactTextString(m) }
func (*DelAwardConfResp) ProtoMessage()    {}
func (*DelAwardConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_award_center_a5ffb82e810154f4, []int{32}
}
func (m *DelAwardConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelAwardConfResp.Unmarshal(m, b)
}
func (m *DelAwardConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelAwardConfResp.Marshal(b, m, deterministic)
}
func (dst *DelAwardConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelAwardConfResp.Merge(dst, src)
}
func (m *DelAwardConfResp) XXX_Size() int {
	return xxx_messageInfo_DelAwardConfResp.Size(m)
}
func (m *DelAwardConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelAwardConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelAwardConfResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*AwardCallbackReq)(nil), "award_center.AwardCallbackReq")
	proto.RegisterType((*AwardCallbackResp)(nil), "award_center.AwardCallbackResp")
	proto.RegisterType((*HeadwearAwardExtra)(nil), "award_center.HeadwearAwardExtra")
	proto.RegisterType((*UserDecorationExtra)(nil), "award_center.UserDecorationExtra")
	proto.RegisterType((*NameplateExtra)(nil), "award_center.NameplateExtra")
	proto.RegisterType((*OfficialCertExtra)(nil), "award_center.OfficialCertExtra")
	proto.RegisterType((*AwardReq)(nil), "award_center.AwardReq")
	proto.RegisterType((*AwardResp)(nil), "award_center.AwardResp")
	proto.RegisterType((*RollbackAwardReq)(nil), "award_center.RollbackAwardReq")
	proto.RegisterType((*RollbackAwardResp)(nil), "award_center.RollbackAwardResp")
	proto.RegisterType((*AwardOrder)(nil), "award_center.AwardOrder")
	proto.RegisterType((*BusinessConf)(nil), "award_center.BusinessConf")
	proto.RegisterType((*AddBusinessConfReq)(nil), "award_center.AddBusinessConfReq")
	proto.RegisterType((*AddBusinessConfResp)(nil), "award_center.AddBusinessConfResp")
	proto.RegisterType((*GetBusinessConfReq)(nil), "award_center.GetBusinessConfReq")
	proto.RegisterType((*GetBusinessConfResp)(nil), "award_center.GetBusinessConfResp")
	proto.RegisterType((*GetBusinessConfListReq)(nil), "award_center.GetBusinessConfListReq")
	proto.RegisterType((*GetBusinessConfListResp)(nil), "award_center.GetBusinessConfListResp")
	proto.RegisterType((*UpdateBusinessConfReq)(nil), "award_center.UpdateBusinessConfReq")
	proto.RegisterType((*UpdateBusinessConfResp)(nil), "award_center.UpdateBusinessConfResp")
	proto.RegisterType((*DelBusinessConfReq)(nil), "award_center.DelBusinessConfReq")
	proto.RegisterType((*DelBusinessConfResp)(nil), "award_center.DelBusinessConfResp")
	proto.RegisterType((*AwardConf)(nil), "award_center.AwardConf")
	proto.RegisterType((*AddAwardConfReq)(nil), "award_center.AddAwardConfReq")
	proto.RegisterType((*AddAwardConfResp)(nil), "award_center.AddAwardConfResp")
	proto.RegisterType((*GetAwardConfReq)(nil), "award_center.GetAwardConfReq")
	proto.RegisterType((*GetAwardConfResp)(nil), "award_center.GetAwardConfResp")
	proto.RegisterType((*GetAwardConfListReq)(nil), "award_center.GetAwardConfListReq")
	proto.RegisterType((*GetAwardConfListResp)(nil), "award_center.GetAwardConfListResp")
	proto.RegisterType((*UpdateAwardConfReq)(nil), "award_center.UpdateAwardConfReq")
	proto.RegisterType((*UpdateAwardConfResp)(nil), "award_center.UpdateAwardConfResp")
	proto.RegisterType((*DelAwardConfReq)(nil), "award_center.DelAwardConfReq")
	proto.RegisterType((*DelAwardConfResp)(nil), "award_center.DelAwardConfResp")
	proto.RegisterEnum("award_center.EAwardStatus", EAwardStatus_name, EAwardStatus_value)
	proto.RegisterEnum("award_center.EGiftType", EGiftType_name, EGiftType_value)
	proto.RegisterEnum("award_center.AwardExpireType", AwardExpireType_name, AwardExpireType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AwardCenterCallbackClient is the client API for AwardCenterCallback service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AwardCenterCallbackClient interface {
	AwardCallback(ctx context.Context, in *AwardCallbackReq, opts ...grpc.CallOption) (*AwardCallbackResp, error)
}

type awardCenterCallbackClient struct {
	cc *grpc.ClientConn
}

func NewAwardCenterCallbackClient(cc *grpc.ClientConn) AwardCenterCallbackClient {
	return &awardCenterCallbackClient{cc}
}

func (c *awardCenterCallbackClient) AwardCallback(ctx context.Context, in *AwardCallbackReq, opts ...grpc.CallOption) (*AwardCallbackResp, error) {
	out := new(AwardCallbackResp)
	err := c.cc.Invoke(ctx, "/award_center.AwardCenterCallback/AwardCallback", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AwardCenterCallbackServer is the server API for AwardCenterCallback service.
type AwardCenterCallbackServer interface {
	AwardCallback(context.Context, *AwardCallbackReq) (*AwardCallbackResp, error)
}

func RegisterAwardCenterCallbackServer(s *grpc.Server, srv AwardCenterCallbackServer) {
	s.RegisterService(&_AwardCenterCallback_serviceDesc, srv)
}

func _AwardCenterCallback_AwardCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AwardCallbackReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwardCenterCallbackServer).AwardCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/award_center.AwardCenterCallback/AwardCallback",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwardCenterCallbackServer).AwardCallback(ctx, req.(*AwardCallbackReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _AwardCenterCallback_serviceDesc = grpc.ServiceDesc{
	ServiceName: "award_center.AwardCenterCallback",
	HandlerType: (*AwardCenterCallbackServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AwardCallback",
			Handler:    _AwardCenterCallback_AwardCallback_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "risk-control/award-center/award-center.proto",
}

// AwardCenterClient is the client API for AwardCenter service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AwardCenterClient interface {
	// 发放奖励
	Award(ctx context.Context, in *AwardReq, opts ...grpc.CallOption) (*AwardResp, error)
	// 回滚发放的奖励
	RollbackAward(ctx context.Context, in *RollbackAwardReq, opts ...grpc.CallOption) (*RollbackAwardResp, error)
	// 对账接口
	GetAwardOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetAwardOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// 增加业务配置
	AddBusinessConf(ctx context.Context, in *AddBusinessConfReq, opts ...grpc.CallOption) (*AddBusinessConfResp, error)
	// 获取业务配置
	GetBusinessConf(ctx context.Context, in *GetBusinessConfReq, opts ...grpc.CallOption) (*GetBusinessConfResp, error)
	// 获取业务配置列表
	GetBusinessConfList(ctx context.Context, in *GetBusinessConfListReq, opts ...grpc.CallOption) (*GetBusinessConfListResp, error)
	// 更新业务配置
	UpdateBusinessConf(ctx context.Context, in *UpdateBusinessConfReq, opts ...grpc.CallOption) (*UpdateBusinessConfResp, error)
	// 删除业务配置
	DelBusinessConf(ctx context.Context, in *DelBusinessConfReq, opts ...grpc.CallOption) (*DelBusinessConfResp, error)
	// 增加奖励配置
	AddAwardConf(ctx context.Context, in *AddAwardConfReq, opts ...grpc.CallOption) (*AddAwardConfResp, error)
	// 获取奖励配置
	GetAwardConf(ctx context.Context, in *GetAwardConfReq, opts ...grpc.CallOption) (*GetAwardConfResp, error)
	// 获取奖励配置列表
	GetAwardConfList(ctx context.Context, in *GetAwardConfListReq, opts ...grpc.CallOption) (*GetAwardConfListResp, error)
	// 更新奖励配置
	UpdateAwardConf(ctx context.Context, in *UpdateAwardConfReq, opts ...grpc.CallOption) (*UpdateAwardConfResp, error)
	// 删除奖励配置
	DelAwardConf(ctx context.Context, in *DelAwardConfReq, opts ...grpc.CallOption) (*DelAwardConfResp, error)
}

type awardCenterClient struct {
	cc *grpc.ClientConn
}

func NewAwardCenterClient(cc *grpc.ClientConn) AwardCenterClient {
	return &awardCenterClient{cc}
}

func (c *awardCenterClient) Award(ctx context.Context, in *AwardReq, opts ...grpc.CallOption) (*AwardResp, error) {
	out := new(AwardResp)
	err := c.cc.Invoke(ctx, "/award_center.AwardCenter/Award", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awardCenterClient) RollbackAward(ctx context.Context, in *RollbackAwardReq, opts ...grpc.CallOption) (*RollbackAwardResp, error) {
	out := new(RollbackAwardResp)
	err := c.cc.Invoke(ctx, "/award_center.AwardCenter/RollbackAward", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awardCenterClient) GetAwardOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/award_center.AwardCenter/GetAwardOrderCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awardCenterClient) GetAwardOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/award_center.AwardCenter/GetAwardOrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awardCenterClient) AddBusinessConf(ctx context.Context, in *AddBusinessConfReq, opts ...grpc.CallOption) (*AddBusinessConfResp, error) {
	out := new(AddBusinessConfResp)
	err := c.cc.Invoke(ctx, "/award_center.AwardCenter/AddBusinessConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awardCenterClient) GetBusinessConf(ctx context.Context, in *GetBusinessConfReq, opts ...grpc.CallOption) (*GetBusinessConfResp, error) {
	out := new(GetBusinessConfResp)
	err := c.cc.Invoke(ctx, "/award_center.AwardCenter/GetBusinessConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awardCenterClient) GetBusinessConfList(ctx context.Context, in *GetBusinessConfListReq, opts ...grpc.CallOption) (*GetBusinessConfListResp, error) {
	out := new(GetBusinessConfListResp)
	err := c.cc.Invoke(ctx, "/award_center.AwardCenter/GetBusinessConfList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awardCenterClient) UpdateBusinessConf(ctx context.Context, in *UpdateBusinessConfReq, opts ...grpc.CallOption) (*UpdateBusinessConfResp, error) {
	out := new(UpdateBusinessConfResp)
	err := c.cc.Invoke(ctx, "/award_center.AwardCenter/UpdateBusinessConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awardCenterClient) DelBusinessConf(ctx context.Context, in *DelBusinessConfReq, opts ...grpc.CallOption) (*DelBusinessConfResp, error) {
	out := new(DelBusinessConfResp)
	err := c.cc.Invoke(ctx, "/award_center.AwardCenter/DelBusinessConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awardCenterClient) AddAwardConf(ctx context.Context, in *AddAwardConfReq, opts ...grpc.CallOption) (*AddAwardConfResp, error) {
	out := new(AddAwardConfResp)
	err := c.cc.Invoke(ctx, "/award_center.AwardCenter/AddAwardConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awardCenterClient) GetAwardConf(ctx context.Context, in *GetAwardConfReq, opts ...grpc.CallOption) (*GetAwardConfResp, error) {
	out := new(GetAwardConfResp)
	err := c.cc.Invoke(ctx, "/award_center.AwardCenter/GetAwardConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awardCenterClient) GetAwardConfList(ctx context.Context, in *GetAwardConfListReq, opts ...grpc.CallOption) (*GetAwardConfListResp, error) {
	out := new(GetAwardConfListResp)
	err := c.cc.Invoke(ctx, "/award_center.AwardCenter/GetAwardConfList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awardCenterClient) UpdateAwardConf(ctx context.Context, in *UpdateAwardConfReq, opts ...grpc.CallOption) (*UpdateAwardConfResp, error) {
	out := new(UpdateAwardConfResp)
	err := c.cc.Invoke(ctx, "/award_center.AwardCenter/UpdateAwardConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awardCenterClient) DelAwardConf(ctx context.Context, in *DelAwardConfReq, opts ...grpc.CallOption) (*DelAwardConfResp, error) {
	out := new(DelAwardConfResp)
	err := c.cc.Invoke(ctx, "/award_center.AwardCenter/DelAwardConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AwardCenterServer is the server API for AwardCenter service.
type AwardCenterServer interface {
	// 发放奖励
	Award(context.Context, *AwardReq) (*AwardResp, error)
	// 回滚发放的奖励
	RollbackAward(context.Context, *RollbackAwardReq) (*RollbackAwardResp, error)
	// 对账接口
	GetAwardOrderCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetAwardOrderList(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// 增加业务配置
	AddBusinessConf(context.Context, *AddBusinessConfReq) (*AddBusinessConfResp, error)
	// 获取业务配置
	GetBusinessConf(context.Context, *GetBusinessConfReq) (*GetBusinessConfResp, error)
	// 获取业务配置列表
	GetBusinessConfList(context.Context, *GetBusinessConfListReq) (*GetBusinessConfListResp, error)
	// 更新业务配置
	UpdateBusinessConf(context.Context, *UpdateBusinessConfReq) (*UpdateBusinessConfResp, error)
	// 删除业务配置
	DelBusinessConf(context.Context, *DelBusinessConfReq) (*DelBusinessConfResp, error)
	// 增加奖励配置
	AddAwardConf(context.Context, *AddAwardConfReq) (*AddAwardConfResp, error)
	// 获取奖励配置
	GetAwardConf(context.Context, *GetAwardConfReq) (*GetAwardConfResp, error)
	// 获取奖励配置列表
	GetAwardConfList(context.Context, *GetAwardConfListReq) (*GetAwardConfListResp, error)
	// 更新奖励配置
	UpdateAwardConf(context.Context, *UpdateAwardConfReq) (*UpdateAwardConfResp, error)
	// 删除奖励配置
	DelAwardConf(context.Context, *DelAwardConfReq) (*DelAwardConfResp, error)
}

func RegisterAwardCenterServer(s *grpc.Server, srv AwardCenterServer) {
	s.RegisterService(&_AwardCenter_serviceDesc, srv)
}

func _AwardCenter_Award_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AwardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwardCenterServer).Award(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/award_center.AwardCenter/Award",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwardCenterServer).Award(ctx, req.(*AwardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwardCenter_RollbackAward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RollbackAwardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwardCenterServer).RollbackAward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/award_center.AwardCenter/RollbackAward",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwardCenterServer).RollbackAward(ctx, req.(*RollbackAwardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwardCenter_GetAwardOrderCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwardCenterServer).GetAwardOrderCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/award_center.AwardCenter/GetAwardOrderCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwardCenterServer).GetAwardOrderCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwardCenter_GetAwardOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwardCenterServer).GetAwardOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/award_center.AwardCenter/GetAwardOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwardCenterServer).GetAwardOrderList(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwardCenter_AddBusinessConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddBusinessConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwardCenterServer).AddBusinessConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/award_center.AwardCenter/AddBusinessConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwardCenterServer).AddBusinessConf(ctx, req.(*AddBusinessConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwardCenter_GetBusinessConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBusinessConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwardCenterServer).GetBusinessConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/award_center.AwardCenter/GetBusinessConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwardCenterServer).GetBusinessConf(ctx, req.(*GetBusinessConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwardCenter_GetBusinessConfList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBusinessConfListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwardCenterServer).GetBusinessConfList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/award_center.AwardCenter/GetBusinessConfList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwardCenterServer).GetBusinessConfList(ctx, req.(*GetBusinessConfListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwardCenter_UpdateBusinessConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBusinessConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwardCenterServer).UpdateBusinessConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/award_center.AwardCenter/UpdateBusinessConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwardCenterServer).UpdateBusinessConf(ctx, req.(*UpdateBusinessConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwardCenter_DelBusinessConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelBusinessConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwardCenterServer).DelBusinessConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/award_center.AwardCenter/DelBusinessConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwardCenterServer).DelBusinessConf(ctx, req.(*DelBusinessConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwardCenter_AddAwardConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddAwardConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwardCenterServer).AddAwardConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/award_center.AwardCenter/AddAwardConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwardCenterServer).AddAwardConf(ctx, req.(*AddAwardConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwardCenter_GetAwardConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAwardConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwardCenterServer).GetAwardConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/award_center.AwardCenter/GetAwardConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwardCenterServer).GetAwardConf(ctx, req.(*GetAwardConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwardCenter_GetAwardConfList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAwardConfListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwardCenterServer).GetAwardConfList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/award_center.AwardCenter/GetAwardConfList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwardCenterServer).GetAwardConfList(ctx, req.(*GetAwardConfListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwardCenter_UpdateAwardConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAwardConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwardCenterServer).UpdateAwardConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/award_center.AwardCenter/UpdateAwardConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwardCenterServer).UpdateAwardConf(ctx, req.(*UpdateAwardConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwardCenter_DelAwardConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelAwardConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwardCenterServer).DelAwardConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/award_center.AwardCenter/DelAwardConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwardCenterServer).DelAwardConf(ctx, req.(*DelAwardConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _AwardCenter_serviceDesc = grpc.ServiceDesc{
	ServiceName: "award_center.AwardCenter",
	HandlerType: (*AwardCenterServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Award",
			Handler:    _AwardCenter_Award_Handler,
		},
		{
			MethodName: "RollbackAward",
			Handler:    _AwardCenter_RollbackAward_Handler,
		},
		{
			MethodName: "GetAwardOrderCount",
			Handler:    _AwardCenter_GetAwardOrderCount_Handler,
		},
		{
			MethodName: "GetAwardOrderList",
			Handler:    _AwardCenter_GetAwardOrderList_Handler,
		},
		{
			MethodName: "AddBusinessConf",
			Handler:    _AwardCenter_AddBusinessConf_Handler,
		},
		{
			MethodName: "GetBusinessConf",
			Handler:    _AwardCenter_GetBusinessConf_Handler,
		},
		{
			MethodName: "GetBusinessConfList",
			Handler:    _AwardCenter_GetBusinessConfList_Handler,
		},
		{
			MethodName: "UpdateBusinessConf",
			Handler:    _AwardCenter_UpdateBusinessConf_Handler,
		},
		{
			MethodName: "DelBusinessConf",
			Handler:    _AwardCenter_DelBusinessConf_Handler,
		},
		{
			MethodName: "AddAwardConf",
			Handler:    _AwardCenter_AddAwardConf_Handler,
		},
		{
			MethodName: "GetAwardConf",
			Handler:    _AwardCenter_GetAwardConf_Handler,
		},
		{
			MethodName: "GetAwardConfList",
			Handler:    _AwardCenter_GetAwardConfList_Handler,
		},
		{
			MethodName: "UpdateAwardConf",
			Handler:    _AwardCenter_UpdateAwardConf_Handler,
		},
		{
			MethodName: "DelAwardConf",
			Handler:    _AwardCenter_DelAwardConf_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "risk-control/award-center/award-center.proto",
}

func init() {
	proto.RegisterFile("risk-control/award-center/award-center.proto", fileDescriptor_award_center_a5ffb82e810154f4)
}

var fileDescriptor_award_center_a5ffb82e810154f4 = []byte{
	// 1629 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x58, 0x5f, 0x53, 0x1c, 0xc7,
	0x11, 0xbf, 0xe5, 0xfe, 0x6e, 0xdf, 0x1d, 0x2c, 0x73, 0x02, 0x4e, 0x20, 0x09, 0xd8, 0x28, 0x29,
	0x8a, 0x84, 0xa3, 0x42, 0xa2, 0x24, 0x95, 0x54, 0x92, 0x42, 0x40, 0x10, 0x15, 0x49, 0xb8, 0x56,
	0x60, 0xbb, 0xec, 0x87, 0xad, 0x61, 0x77, 0x38, 0xa6, 0xb4, 0xec, 0xae, 0x77, 0xe6, 0x10, 0xe8,
	0xd9, 0xcf, 0xf6, 0x57, 0xf1, 0x93, 0xbf, 0x87, 0x1f, 0xfc, 0xe4, 0x2f, 0xe3, 0x9a, 0xd9, 0x59,
	0xb8, 0x9d, 0xbd, 0x3f, 0x92, 0x0b, 0xfb, 0x89, 0x9b, 0x9e, 0xee, 0xdf, 0xf4, 0x74, 0xff, 0x7a,
	0xba, 0x17, 0xf8, 0x53, 0x42, 0xd9, 0xdb, 0x2d, 0x2f, 0x0a, 0x79, 0x12, 0x05, 0xdb, 0xf8, 0x1d,
	0x4e, 0xfc, 0x2d, 0x8f, 0x84, 0x9c, 0x24, 0xb9, 0x45, 0x2f, 0x4e, 0x22, 0x1e, 0xa1, 0x96, 0x94,
	0xb9, 0xa9, 0x6c, 0x79, 0x35, 0x21, 0x5e, 0x14, 0x7a, 0x34, 0x20, 0x5b, 0x57, 0x3b, 0xdb, 0xc3,
	0x8b, 0x54, 0xdd, 0x3e, 0x00, 0x6b, 0x57, 0x18, 0xec, 0xe1, 0x20, 0x38, 0xc3, 0xde, 0x5b, 0x87,
	0x7c, 0x85, 0x1e, 0x42, 0x23, 0x4a, 0x7c, 0x92, 0xb8, 0xd4, 0xef, 0x1a, 0x6b, 0xc6, 0x86, 0xe9,
	0xd4, 0xe5, 0xfa, 0xc8, 0x47, 0x8b, 0x50, 0x63, 0x1c, 0xf3, 0x01, 0xeb, 0xce, 0xac, 0x19, 0x1b,
	0x6d, 0x47, 0xad, 0xec, 0x0e, 0xcc, 0x6b, 0x30, 0x2c, 0xb6, 0x39, 0xa0, 0x17, 0x04, 0xfb, 0xef,
	0x08, 0x4e, 0xe4, 0xe6, 0xc1, 0x35, 0x4f, 0x30, 0x5a, 0x80, 0x9a, 0x17, 0xbb, 0x03, 0x85, 0xdd,
	0x76, 0xaa, 0x5e, 0x7c, 0x4a, 0x7d, 0xf4, 0x07, 0x98, 0x8b, 0x07, 0xec, 0xc2, 0xbd, 0x64, 0x7d,
	0x37, 0x4e, 0xc8, 0x39, 0xbd, 0x96, 0x47, 0x98, 0x4e, 0x5b, 0x88, 0x5f, 0xb1, 0xfe, 0x27, 0x52,
	0x88, 0x56, 0xa1, 0xe9, 0x0d, 0x18, 0x8f, 0x2e, 0x5d, 0x4e, 0xae, 0x79, 0xb7, 0x2c, 0x75, 0x20,
	0x15, 0x9d, 0x90, 0x6b, 0x6e, 0x6f, 0x43, 0xe7, 0x94, 0x91, 0x64, 0x9f, 0x78, 0x51, 0x82, 0x39,
	0x8d, 0xc2, 0xf4, 0xd8, 0x2e, 0xd4, 0xaf, 0x48, 0xc2, 0x68, 0x14, 0x66, 0x77, 0x52, 0x4b, 0xfb,
	0x1f, 0x30, 0xfb, 0x1a, 0x5f, 0x92, 0x38, 0xc0, 0x9c, 0xa4, 0xba, 0x23, 0x7c, 0x31, 0x46, 0xf8,
	0x62, 0x1f, 0xc2, 0xfc, 0xf1, 0xf9, 0x39, 0xf5, 0x28, 0x0e, 0xf6, 0x48, 0xc2, 0x53, 0xe3, 0x47,
	0x60, 0x62, 0xce, 0x13, 0x7a, 0x36, 0xe0, 0x44, 0x5d, 0xf1, 0x4e, 0x80, 0x1e, 0x40, 0x95, 0x8a,
	0x44, 0xaa, 0xcb, 0xa5, 0x0b, 0xfb, 0x87, 0x0a, 0x34, 0x64, 0x88, 0xa6, 0x84, 0xff, 0x31, 0x00,
	0xc7, 0x49, 0x9f, 0x70, 0x19, 0xbf, 0x34, 0x05, 0x66, 0x2a, 0x11, 0x31, 0x5c, 0x85, 0xe6, 0xd9,
	0x80, 0xd1, 0x90, 0x30, 0x26, 0x8c, 0xcb, 0x72, 0x1f, 0x32, 0xd1, 0x91, 0x8f, 0x96, 0xa0, 0xde,
	0xa7, 0xe7, 0x5c, 0x6c, 0x56, 0x24, 0x72, 0x4d, 0x2c, 0x8f, 0x7c, 0xb4, 0x02, 0xa6, 0xdc, 0xe0,
	0x37, 0x31, 0xe9, 0x56, 0xa5, 0x5d, 0x43, 0x08, 0x4e, 0x6e, 0x62, 0x22, 0x60, 0x2f, 0xa2, 0xc0,
	0xa7, 0x61, 0xdf, 0xf5, 0xf1, 0x4d, 0xb7, 0x96, 0xc2, 0x2a, 0xd1, 0x3e, 0xbe, 0x11, 0x6e, 0x45,
	0x03, 0xce, 0xa8, 0x4f, 0x5c, 0xce, 0xba, 0xf5, 0xd4, 0x2d, 0x25, 0x39, 0x61, 0xe8, 0x10, 0x66,
	0x2f, 0x14, 0x0f, 0x5c, 0x22, 0x62, 0xd4, 0x6d, 0xac, 0x19, 0x1b, 0xcd, 0x9d, 0xb5, 0xde, 0x30,
	0x57, 0x7b, 0x45, 0xae, 0x38, 0xed, 0xcc, 0x2e, 0x0d, 0xed, 0x29, 0x2c, 0x0c, 0x18, 0x49, 0x5c,
	0xff, 0x36, 0xb7, 0x0a, 0xcf, 0x94, 0x78, 0xeb, 0x79, 0xbc, 0x11, 0x2c, 0x70, 0x3a, 0x83, 0x11,
	0xd4, 0x38, 0x80, 0xb9, 0x30, 0x23, 0x80, 0x02, 0x04, 0x09, 0xf8, 0x28, 0x0f, 0x98, 0x67, 0x89,
	0x33, 0x1b, 0xe6, 0x59, 0x73, 0x0c, 0x9d, 0x48, 0xb1, 0xc1, 0xf5, 0x48, 0xc2, 0x15, 0x54, 0x53,
	0x42, 0xad, 0xe6, 0xa1, 0x0a, 0xb4, 0x71, 0xe6, 0xa3, 0x02, 0x93, 0x56, 0xa1, 0x49, 0xae, 0x63,
	0x9a, 0x90, 0x34, 0x2d, 0xad, 0x34, 0xee, 0xa9, 0x48, 0x26, 0x66, 0x05, 0x4c, 0x3c, 0xe0, 0x91,
	0x2b, 0x22, 0xd4, 0x6d, 0xaf, 0x19, 0x1b, 0x0d, 0xa7, 0x21, 0x04, 0x9f, 0x11, 0x9c, 0xd8, 0x4d,
	0x30, 0x15, 0xa5, 0x58, 0x6c, 0x6f, 0x81, 0xe5, 0x44, 0x69, 0x69, 0x7e, 0x00, 0xcf, 0x44, 0x39,
	0x6b, 0xea, 0x2c, 0xb6, 0xbf, 0xab, 0x02, 0xc8, 0xd5, 0xb1, 0xd0, 0x9a, 0x44, 0xd3, 0x75, 0x50,
	0xaf, 0x50, 0xee, 0xad, 0x68, 0x4a, 0xd9, 0x1b, 0x29, 0xd2, 0x98, 0x5c, 0x9e, 0xc2, 0xe4, 0xca,
	0x24, 0x26, 0x57, 0xc7, 0x33, 0xb9, 0x36, 0x99, 0xc9, 0xf5, 0x02, 0x93, 0x57, 0xc0, 0x1c, 0xc4,
	0xbe, 0xe0, 0x01, 0x67, 0x92, 0xa5, 0x6d, 0xa7, 0x91, 0x0a, 0x4e, 0x98, 0x46, 0x73, 0x53, 0xa7,
	0xf9, 0x0a, 0x98, 0x5e, 0x42, 0x94, 0x2d, 0xa4, 0xb6, 0xa9, 0x60, 0x64, 0x0d, 0x34, 0xef, 0xb9,
	0x06, 0x5a, 0xf7, 0x5d, 0x03, 0xed, 0xfb, 0xab, 0x81, 0xd9, 0xfb, 0xaa, 0x81, 0xb9, 0xc9, 0x35,
	0x60, 0x69, 0x35, 0xf0, 0x93, 0x01, 0xad, 0xe7, 0x8a, 0x34, 0x7b, 0x51, 0x78, 0xae, 0xf3, 0xca,
	0x28, 0xf0, 0x0a, 0x41, 0x45, 0x5c, 0x49, 0x3d, 0xcf, 0xf2, 0xb7, 0x90, 0xf9, 0x84, 0x79, 0xaa,
	0xd7, 0xc8, 0xdf, 0x02, 0x88, 0x45, 0x83, 0xc4, 0x53, 0x7e, 0x29, 0x82, 0xa6, 0x22, 0xe9, 0x57,
	0x17, 0xea, 0x34, 0xbc, 0xc2, 0x81, 0x22, 0x68, 0xc3, 0xc9, 0x96, 0xa2, 0x01, 0x52, 0xe6, 0xfa,
	0x24, 0x90, 0xf4, 0x6c, 0x38, 0x55, 0xca, 0xf6, 0x49, 0x20, 0xea, 0xe9, 0x8c, 0xf4, 0x69, 0x78,
	0xf7, 0x84, 0xd6, 0xe5, 0xfa, 0x84, 0x09, 0x0b, 0x12, 0xfa, 0x77, 0x94, 0xac, 0x92, 0xd0, 0x3f,
	0x61, 0xf6, 0xb7, 0x06, 0xa0, 0x5d, 0xdf, 0x1f, 0xbe, 0xa0, 0xa8, 0xeb, 0xec, 0x0a, 0xc6, 0x88,
	0x2b, 0xcc, 0x8c, 0xbf, 0x42, 0xb9, 0x70, 0x85, 0x61, 0x8f, 0x2a, 0xe3, 0x3c, 0xaa, 0x0e, 0x7b,
	0xf4, 0x37, 0xe8, 0x14, 0x1c, 0x62, 0xf1, 0xd4, 0xa8, 0xdb, 0xcf, 0x00, 0x1d, 0x12, 0xae, 0x5f,
	0x64, 0xaa, 0xd9, 0x01, 0x74, 0x0a, 0x66, 0x2c, 0x46, 0x3d, 0xa8, 0x78, 0x51, 0x78, 0x2e, 0x0d,
	0x9a, 0x3b, 0xcb, 0x79, 0xd6, 0xe5, 0xb4, 0xa5, 0x9e, 0x7d, 0x04, 0x8b, 0x1a, 0xcc, 0x4b, 0xca,
	0xb8, 0x0a, 0x65, 0x8c, 0xfb, 0x59, 0x1b, 0x97, 0xbf, 0x05, 0xe1, 0xc4, 0x5f, 0x97, 0xd1, 0xf7,
	0x44, 0xbd, 0x6c, 0x0d, 0x21, 0x78, 0x43, 0xdf, 0x13, 0xfb, 0x02, 0x96, 0x46, 0x42, 0xb1, 0x18,
	0xfd, 0x1d, 0x4c, 0x71, 0x9a, 0x1b, 0x50, 0xc6, 0xbb, 0xc6, 0x5a, 0x79, 0x8a, 0x6b, 0x0d, 0x4f,
	0x19, 0x8b, 0x91, 0x81, 0x47, 0x1c, 0x07, 0xea, 0xb0, 0x74, 0x61, 0x1f, 0xc2, 0xc2, 0xa9, 0x7c,
	0x98, 0xf4, 0xa8, 0x7d, 0xec, 0xed, 0xbb, 0xb0, 0x38, 0x0a, 0x88, 0xc5, 0x22, 0x2b, 0xfb, 0x24,
	0xf8, 0xe8, 0xac, 0x2c, 0x40, 0xa7, 0x60, 0xc6, 0x62, 0xfb, 0x47, 0x43, 0x35, 0xa4, 0x0f, 0x2b,
	0xc4, 0xa1, 0x07, 0x7e, 0x66, 0xfc, 0x03, 0x5f, 0xd6, 0x1e, 0xf8, 0x15, 0x30, 0x29, 0x27, 0x97,
	0xae, 0x24, 0x7b, 0x3a, 0xe2, 0x34, 0x84, 0x60, 0x5f, 0x10, 0x7e, 0x1d, 0x5a, 0x8c, 0x86, 0xfd,
	0x80, 0xb8, 0x01, 0xbd, 0xa4, 0x5c, 0x51, 0xb7, 0x99, 0xca, 0x5e, 0x0a, 0x11, 0xfa, 0x33, 0x2c,
	0xf8, 0x98, 0x06, 0x37, 0xae, 0x8a, 0x5a, 0xc8, 0x95, 0x6e, 0xda, 0x49, 0x90, 0xdc, 0x4c, 0x6f,
	0x11, 0x72, 0x69, 0x62, 0xff, 0x07, 0xe6, 0x76, 0x7d, 0xff, 0xf6, 0x66, 0x22, 0x44, 0x7f, 0xcc,
	0xa5, 0x60, 0x29, 0x9f, 0x82, 0x3b, 0xcd, 0x34, 0xfe, 0x08, 0xac, 0xbc, 0x3d, 0x8b, 0xed, 0x0b,
	0x98, 0x3b, 0x24, 0x3c, 0x87, 0xf9, 0xeb, 0x04, 0xcc, 0xfe, 0x2f, 0x58, 0xf9, 0x93, 0x58, 0xfc,
	0x71, 0xee, 0x7f, 0x6d, 0xc8, 0x22, 0xbc, 0x15, 0x67, 0xa5, 0x33, 0xd5, 0xdf, 0x9c, 0x5b, 0x33,
	0x5a, 0x1e, 0xb3, 0xc2, 0x2b, 0x8f, 0x2b, 0xbc, 0x8a, 0x56, 0x78, 0x67, 0xf0, 0xa0, 0xe8, 0x05,
	0x8b, 0xd1, 0x5f, 0x8b, 0x55, 0x37, 0xf6, 0x42, 0xd3, 0x4a, 0x6e, 0x17, 0x50, 0x5a, 0x29, 0xbf,
	0x3c, 0xd9, 0x0b, 0xd0, 0x29, 0x40, 0xa4, 0xf9, 0xde, 0x27, 0xc1, 0x6f, 0x91, 0x6f, 0x04, 0x56,
	0xfe, 0x24, 0x16, 0x6f, 0xee, 0x42, 0xeb, 0x60, 0x77, 0x68, 0x36, 0x6b, 0x41, 0xe3, 0x05, 0x0e,
	0xfd, 0x80, 0x86, 0x7d, 0xab, 0x84, 0x1a, 0x50, 0xf9, 0x1f, 0xa6, 0x81, 0x65, 0xa0, 0x26, 0xd4,
	0xdf, 0x0c, 0x3c, 0x8f, 0x30, 0x66, 0xcd, 0x08, 0xa5, 0x6c, 0x44, 0xb4, 0xca, 0x9b, 0xdf, 0x18,
	0x60, 0x1e, 0x1c, 0x66, 0xd9, 0xeb, 0xc0, 0xdc, 0x69, 0xf8, 0x36, 0x8c, 0xde, 0x85, 0x99, 0xc8,
	0x2a, 0x49, 0x54, 0x35, 0xc9, 0x58, 0x06, 0x32, 0xa1, 0xfa, 0x22, 0x4a, 0x18, 0xb1, 0x66, 0xc4,
	0xcf, 0x57, 0xc4, 0xc7, 0x81, 0x55, 0x46, 0x08, 0x66, 0xf3, 0x13, 0x8b, 0x55, 0x41, 0x6d, 0x30,
	0x6f, 0x87, 0x0e, 0xab, 0x8a, 0x2c, 0x68, 0x0d, 0x0f, 0x0e, 0x56, 0x4d, 0x9c, 0xb6, 0x77, 0x81,
	0xc3, 0x90, 0x04, 0x47, 0xe1, 0x79, 0xb4, 0x87, 0x13, 0xdf, 0xaa, 0x6f, 0x6e, 0xc2, 0x9c, 0x9a,
	0xa1, 0x6e, 0x27, 0x85, 0x26, 0xd4, 0x5f, 0x63, 0x3e, 0x48, 0x70, 0x60, 0x95, 0x10, 0x40, 0x6d,
	0x1f, 0xdf, 0x1c, 0x84, 0xbe, 0x65, 0xec, 0x50, 0xe8, 0xa4, 0x01, 0x91, 0x39, 0xcb, 0x3e, 0x61,
	0x91, 0x03, 0xed, 0xdc, 0x37, 0x2d, 0x7a, 0x32, 0x2a, 0xb7, 0x77, 0xdf, 0xcd, 0xcb, 0xab, 0x13,
	0xf7, 0x59, 0x6c, 0x97, 0x76, 0xbe, 0x37, 0xa1, 0x39, 0x74, 0x16, 0xfa, 0x27, 0x54, 0xe5, 0x12,
	0x2d, 0x8e, 0xb0, 0x15, 0x98, 0x4b, 0x23, 0xe5, 0x02, 0x4b, 0xf8, 0x97, 0x1b, 0xd2, 0x75, 0xff,
	0xf4, 0x81, 0x5f, 0xf7, 0xaf, 0x38, 0xe1, 0x97, 0xd0, 0x91, 0x6c, 0xc4, 0x77, 0x53, 0xfe, 0x5e,
	0x34, 0x08, 0x39, 0x7a, 0xd8, 0x73, 0xb2, 0xff, 0x1c, 0x7c, 0xba, 0xd3, 0x3b, 0xa1, 0x97, 0xc4,
	0xc1, 0x61, 0x9f, 0x08, 0xcc, 0xc5, 0xdc, 0x96, 0x54, 0x57, 0x50, 0xff, 0x87, 0xf9, 0x1c, 0x94,
	0x2c, 0xac, 0x09, 0x48, 0xf9, 0xad, 0xe3, 0xf4, 0x53, 0x82, 0x29, 0xb0, 0xcf, 0xe5, 0x23, 0x9b,
	0x1b, 0xe5, 0xb4, 0xc9, 0xb9, 0x38, 0x08, 0x2d, 0xaf, 0x4f, 0xd1, 0xc8, 0x90, 0xb5, 0x8e, 0xad,
	0x23, 0x17, 0x27, 0x13, 0x1d, 0x79, 0xc4, 0x10, 0x62, 0x97, 0x90, 0x5f, 0x98, 0x4e, 0x64, 0x08,
	0x9e, 0x4e, 0xb4, 0x55, 0xcf, 0xe7, 0xf2, 0xef, 0x3f, 0x40, 0x4b, 0x9e, 0x82, 0xb3, 0x47, 0x29,
	0x77, 0x85, 0xdf, 0x69, 0x9f, 0x01, 0xa3, 0x26, 0x85, 0xe5, 0xa7, 0xd3, 0x95, 0xb2, 0x10, 0x69,
	0x0d, 0x5d, 0x0f, 0x51, 0x71, 0x4c, 0xd0, 0x43, 0x34, 0x6a, 0x22, 0x28, 0xa1, 0x63, 0x68, 0x0d,
	0xf7, 0x3e, 0xf4, 0xb8, 0x90, 0xb1, 0xe1, 0x37, 0x71, 0xf9, 0xc9, 0xa4, 0xed, 0x0c, 0x70, 0xb8,
	0x0d, 0xe8, 0x80, 0x5a, 0x53, 0xd5, 0x01, 0xf5, 0x4e, 0x68, 0x97, 0xd0, 0x97, 0xf9, 0xfe, 0x28,
	0x33, 0xb8, 0x3e, 0xde, 0x2a, 0x4b, 0x9f, 0x3d, 0x4d, 0x25, 0x0b, 0xac, 0xd6, 0x0d, 0xf4, 0xc0,
	0x16, 0xfb, 0x8d, 0x1e, 0xd8, 0x51, 0xed, 0x44, 0xc6, 0x61, 0xf8, 0x99, 0xd7, 0xe3, 0xa0, 0x35,
	0x1b, 0x3d, 0x0e, 0x7a, 0x87, 0xb0, 0x4b, 0xcf, 0xff, 0xfd, 0xc5, 0xbf, 0xfa, 0x51, 0x80, 0xc3,
	0x7e, 0xef, 0xd9, 0x0e, 0xe7, 0x3d, 0x2f, 0xba, 0xdc, 0x96, 0xff, 0x40, 0xf4, 0xa2, 0x60, 0x9b,
	0x91, 0xe4, 0x8a, 0x7a, 0x84, 0x6d, 0x8f, 0xfd, 0x47, 0xe5, 0x59, 0x4d, 0x2a, 0xff, 0xe5, 0xe7,
	0x00, 0x00, 0x00, 0xff, 0xff, 0x62, 0xf8, 0xec, 0xa8, 0xcc, 0x14, 0x00, 0x00,
}
