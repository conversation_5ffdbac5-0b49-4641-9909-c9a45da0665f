// Code generated by protoc-gen-go. DO NOT EDIT.
// source: officialcert/officialcert.proto

package officialcert // import "golang.52tt.com/protocol/services/officialcert"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 枚举值为 1 2 4 8 16 32 ... 位运算
type ECertAttrType int32

const (
	ECertAttrType_ECertAttrTypeNone      ECertAttrType = 0
	ECertAttrType_ECertAttrTypeImNoLimit ECertAttrType = 1
)

var ECertAttrType_name = map[int32]string{
	0: "ECertAttrTypeNone",
	1: "ECertAttrTypeImNoLimit",
}
var ECertAttrType_value = map[string]int32{
	"ECertAttrTypeNone":      0,
	"ECertAttrTypeImNoLimit": 1,
}

func (x ECertAttrType) String() string {
	return proto.EnumName(ECertAttrType_name, int32(x))
}
func (ECertAttrType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{0}
}

type OfficialCertInfo struct {
	Uid       uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Title     string `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Intro     string `protobuf:"bytes,3,opt,name=intro,proto3" json:"intro,omitempty"`
	Style     string `protobuf:"bytes,4,opt,name=style,proto3" json:"style,omitempty"`
	Id        uint32 `protobuf:"varint,5,opt,name=id,proto3" json:"id,omitempty"`
	BeginTs   uint64 `protobuf:"varint,6,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs     uint64 `protobuf:"varint,7,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	IsUse     bool   `protobuf:"varint,8,opt,name=is_use,json=isUse,proto3" json:"is_use,omitempty"`
	Attribute uint32 `protobuf:"varint,9,opt,name=attribute,proto3" json:"attribute,omitempty"`
	// 大v图标动效
	CertifySpecialEffectTitle string   `protobuf:"bytes,10,opt,name=certify_special_effect_title,json=certifySpecialEffectTitle,proto3" json:"certify_special_effect_title,omitempty"`
	CertifySpecialEffectIcon  string   `protobuf:"bytes,11,opt,name=certify_special_effect_icon,json=certifySpecialEffectIcon,proto3" json:"certify_special_effect_icon,omitempty"`
	XXX_NoUnkeyedLiteral      struct{} `json:"-"`
	XXX_unrecognized          []byte   `json:"-"`
	XXX_sizecache             int32    `json:"-"`
}

func (m *OfficialCertInfo) Reset()         { *m = OfficialCertInfo{} }
func (m *OfficialCertInfo) String() string { return proto.CompactTextString(m) }
func (*OfficialCertInfo) ProtoMessage()    {}
func (*OfficialCertInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{0}
}
func (m *OfficialCertInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfficialCertInfo.Unmarshal(m, b)
}
func (m *OfficialCertInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfficialCertInfo.Marshal(b, m, deterministic)
}
func (dst *OfficialCertInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfficialCertInfo.Merge(dst, src)
}
func (m *OfficialCertInfo) XXX_Size() int {
	return xxx_messageInfo_OfficialCertInfo.Size(m)
}
func (m *OfficialCertInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OfficialCertInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OfficialCertInfo proto.InternalMessageInfo

func (m *OfficialCertInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *OfficialCertInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *OfficialCertInfo) GetIntro() string {
	if m != nil {
		return m.Intro
	}
	return ""
}

func (m *OfficialCertInfo) GetStyle() string {
	if m != nil {
		return m.Style
	}
	return ""
}

func (m *OfficialCertInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *OfficialCertInfo) GetBeginTs() uint64 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *OfficialCertInfo) GetEndTs() uint64 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *OfficialCertInfo) GetIsUse() bool {
	if m != nil {
		return m.IsUse
	}
	return false
}

func (m *OfficialCertInfo) GetAttribute() uint32 {
	if m != nil {
		return m.Attribute
	}
	return 0
}

func (m *OfficialCertInfo) GetCertifySpecialEffectTitle() string {
	if m != nil {
		return m.CertifySpecialEffectTitle
	}
	return ""
}

func (m *OfficialCertInfo) GetCertifySpecialEffectIcon() string {
	if m != nil {
		return m.CertifySpecialEffectIcon
	}
	return ""
}

type UserOfficialCertInfo struct {
	IsCertified          bool                `protobuf:"varint,1,opt,name=is_certified,json=isCertified,proto3" json:"is_certified,omitempty"`
	Cert                 *OfficialCertInfo   `protobuf:"bytes,2,opt,name=cert,proto3" json:"cert,omitempty"`
	CertList             []*OfficialCertInfo `protobuf:"bytes,3,rep,name=cert_list,json=certList,proto3" json:"cert_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *UserOfficialCertInfo) Reset()         { *m = UserOfficialCertInfo{} }
func (m *UserOfficialCertInfo) String() string { return proto.CompactTextString(m) }
func (*UserOfficialCertInfo) ProtoMessage()    {}
func (*UserOfficialCertInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{1}
}
func (m *UserOfficialCertInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserOfficialCertInfo.Unmarshal(m, b)
}
func (m *UserOfficialCertInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserOfficialCertInfo.Marshal(b, m, deterministic)
}
func (dst *UserOfficialCertInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserOfficialCertInfo.Merge(dst, src)
}
func (m *UserOfficialCertInfo) XXX_Size() int {
	return xxx_messageInfo_UserOfficialCertInfo.Size(m)
}
func (m *UserOfficialCertInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserOfficialCertInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserOfficialCertInfo proto.InternalMessageInfo

func (m *UserOfficialCertInfo) GetIsCertified() bool {
	if m != nil {
		return m.IsCertified
	}
	return false
}

func (m *UserOfficialCertInfo) GetCert() *OfficialCertInfo {
	if m != nil {
		return m.Cert
	}
	return nil
}

func (m *UserOfficialCertInfo) GetCertList() []*OfficialCertInfo {
	if m != nil {
		return m.CertList
	}
	return nil
}

type GetUserOfficialCertReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserOfficialCertReq) Reset()         { *m = GetUserOfficialCertReq{} }
func (m *GetUserOfficialCertReq) String() string { return proto.CompactTextString(m) }
func (*GetUserOfficialCertReq) ProtoMessage()    {}
func (*GetUserOfficialCertReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{2}
}
func (m *GetUserOfficialCertReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserOfficialCertReq.Unmarshal(m, b)
}
func (m *GetUserOfficialCertReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserOfficialCertReq.Marshal(b, m, deterministic)
}
func (dst *GetUserOfficialCertReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserOfficialCertReq.Merge(dst, src)
}
func (m *GetUserOfficialCertReq) XXX_Size() int {
	return xxx_messageInfo_GetUserOfficialCertReq.Size(m)
}
func (m *GetUserOfficialCertReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserOfficialCertReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserOfficialCertReq proto.InternalMessageInfo

func (m *GetUserOfficialCertReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserOfficialCertResp struct {
	Info                 *UserOfficialCertInfo `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetUserOfficialCertResp) Reset()         { *m = GetUserOfficialCertResp{} }
func (m *GetUserOfficialCertResp) String() string { return proto.CompactTextString(m) }
func (*GetUserOfficialCertResp) ProtoMessage()    {}
func (*GetUserOfficialCertResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{3}
}
func (m *GetUserOfficialCertResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserOfficialCertResp.Unmarshal(m, b)
}
func (m *GetUserOfficialCertResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserOfficialCertResp.Marshal(b, m, deterministic)
}
func (dst *GetUserOfficialCertResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserOfficialCertResp.Merge(dst, src)
}
func (m *GetUserOfficialCertResp) XXX_Size() int {
	return xxx_messageInfo_GetUserOfficialCertResp.Size(m)
}
func (m *GetUserOfficialCertResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserOfficialCertResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserOfficialCertResp proto.InternalMessageInfo

func (m *GetUserOfficialCertResp) GetInfo() *UserOfficialCertInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type ListUserOfficialCertReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListUserOfficialCertReq) Reset()         { *m = ListUserOfficialCertReq{} }
func (m *ListUserOfficialCertReq) String() string { return proto.CompactTextString(m) }
func (*ListUserOfficialCertReq) ProtoMessage()    {}
func (*ListUserOfficialCertReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{4}
}
func (m *ListUserOfficialCertReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListUserOfficialCertReq.Unmarshal(m, b)
}
func (m *ListUserOfficialCertReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListUserOfficialCertReq.Marshal(b, m, deterministic)
}
func (dst *ListUserOfficialCertReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListUserOfficialCertReq.Merge(dst, src)
}
func (m *ListUserOfficialCertReq) XXX_Size() int {
	return xxx_messageInfo_ListUserOfficialCertReq.Size(m)
}
func (m *ListUserOfficialCertReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListUserOfficialCertReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListUserOfficialCertReq proto.InternalMessageInfo

type ListUserOfficialCertResp struct {
	CertList             []*OfficialCertInfo `protobuf:"bytes,1,rep,name=cert_list,json=certList,proto3" json:"cert_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *ListUserOfficialCertResp) Reset()         { *m = ListUserOfficialCertResp{} }
func (m *ListUserOfficialCertResp) String() string { return proto.CompactTextString(m) }
func (*ListUserOfficialCertResp) ProtoMessage()    {}
func (*ListUserOfficialCertResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{5}
}
func (m *ListUserOfficialCertResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListUserOfficialCertResp.Unmarshal(m, b)
}
func (m *ListUserOfficialCertResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListUserOfficialCertResp.Marshal(b, m, deterministic)
}
func (dst *ListUserOfficialCertResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListUserOfficialCertResp.Merge(dst, src)
}
func (m *ListUserOfficialCertResp) XXX_Size() int {
	return xxx_messageInfo_ListUserOfficialCertResp.Size(m)
}
func (m *ListUserOfficialCertResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListUserOfficialCertResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListUserOfficialCertResp proto.InternalMessageInfo

func (m *ListUserOfficialCertResp) GetCertList() []*OfficialCertInfo {
	if m != nil {
		return m.CertList
	}
	return nil
}

type SetUserOfficialCertReq struct {
	Cert                 *OfficialCertInfo `protobuf:"bytes,1,opt,name=cert,proto3" json:"cert,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SetUserOfficialCertReq) Reset()         { *m = SetUserOfficialCertReq{} }
func (m *SetUserOfficialCertReq) String() string { return proto.CompactTextString(m) }
func (*SetUserOfficialCertReq) ProtoMessage()    {}
func (*SetUserOfficialCertReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{6}
}
func (m *SetUserOfficialCertReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserOfficialCertReq.Unmarshal(m, b)
}
func (m *SetUserOfficialCertReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserOfficialCertReq.Marshal(b, m, deterministic)
}
func (dst *SetUserOfficialCertReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserOfficialCertReq.Merge(dst, src)
}
func (m *SetUserOfficialCertReq) XXX_Size() int {
	return xxx_messageInfo_SetUserOfficialCertReq.Size(m)
}
func (m *SetUserOfficialCertReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserOfficialCertReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserOfficialCertReq proto.InternalMessageInfo

func (m *SetUserOfficialCertReq) GetCert() *OfficialCertInfo {
	if m != nil {
		return m.Cert
	}
	return nil
}

type SetUserOfficialCertResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserOfficialCertResp) Reset()         { *m = SetUserOfficialCertResp{} }
func (m *SetUserOfficialCertResp) String() string { return proto.CompactTextString(m) }
func (*SetUserOfficialCertResp) ProtoMessage()    {}
func (*SetUserOfficialCertResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{7}
}
func (m *SetUserOfficialCertResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserOfficialCertResp.Unmarshal(m, b)
}
func (m *SetUserOfficialCertResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserOfficialCertResp.Marshal(b, m, deterministic)
}
func (dst *SetUserOfficialCertResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserOfficialCertResp.Merge(dst, src)
}
func (m *SetUserOfficialCertResp) XXX_Size() int {
	return xxx_messageInfo_SetUserOfficialCertResp.Size(m)
}
func (m *SetUserOfficialCertResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserOfficialCertResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserOfficialCertResp proto.InternalMessageInfo

type DelUserOfficialCertReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Id                   uint32   `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelUserOfficialCertReq) Reset()         { *m = DelUserOfficialCertReq{} }
func (m *DelUserOfficialCertReq) String() string { return proto.CompactTextString(m) }
func (*DelUserOfficialCertReq) ProtoMessage()    {}
func (*DelUserOfficialCertReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{8}
}
func (m *DelUserOfficialCertReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelUserOfficialCertReq.Unmarshal(m, b)
}
func (m *DelUserOfficialCertReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelUserOfficialCertReq.Marshal(b, m, deterministic)
}
func (dst *DelUserOfficialCertReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelUserOfficialCertReq.Merge(dst, src)
}
func (m *DelUserOfficialCertReq) XXX_Size() int {
	return xxx_messageInfo_DelUserOfficialCertReq.Size(m)
}
func (m *DelUserOfficialCertReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelUserOfficialCertReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelUserOfficialCertReq proto.InternalMessageInfo

func (m *DelUserOfficialCertReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DelUserOfficialCertReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DelUserOfficialCertResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelUserOfficialCertResp) Reset()         { *m = DelUserOfficialCertResp{} }
func (m *DelUserOfficialCertResp) String() string { return proto.CompactTextString(m) }
func (*DelUserOfficialCertResp) ProtoMessage()    {}
func (*DelUserOfficialCertResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{9}
}
func (m *DelUserOfficialCertResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelUserOfficialCertResp.Unmarshal(m, b)
}
func (m *DelUserOfficialCertResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelUserOfficialCertResp.Marshal(b, m, deterministic)
}
func (dst *DelUserOfficialCertResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelUserOfficialCertResp.Merge(dst, src)
}
func (m *DelUserOfficialCertResp) XXX_Size() int {
	return xxx_messageInfo_DelUserOfficialCertResp.Size(m)
}
func (m *DelUserOfficialCertResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelUserOfficialCertResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelUserOfficialCertResp proto.InternalMessageInfo

type DelCertInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Id                   uint32   `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelCertInfo) Reset()         { *m = DelCertInfo{} }
func (m *DelCertInfo) String() string { return proto.CompactTextString(m) }
func (*DelCertInfo) ProtoMessage()    {}
func (*DelCertInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{10}
}
func (m *DelCertInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelCertInfo.Unmarshal(m, b)
}
func (m *DelCertInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelCertInfo.Marshal(b, m, deterministic)
}
func (dst *DelCertInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelCertInfo.Merge(dst, src)
}
func (m *DelCertInfo) XXX_Size() int {
	return xxx_messageInfo_DelCertInfo.Size(m)
}
func (m *DelCertInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DelCertInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DelCertInfo proto.InternalMessageInfo

func (m *DelCertInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DelCertInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type BatchDelOfficalCertsReq struct {
	InfoList             []*DelCertInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *BatchDelOfficalCertsReq) Reset()         { *m = BatchDelOfficalCertsReq{} }
func (m *BatchDelOfficalCertsReq) String() string { return proto.CompactTextString(m) }
func (*BatchDelOfficalCertsReq) ProtoMessage()    {}
func (*BatchDelOfficalCertsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{11}
}
func (m *BatchDelOfficalCertsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelOfficalCertsReq.Unmarshal(m, b)
}
func (m *BatchDelOfficalCertsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelOfficalCertsReq.Marshal(b, m, deterministic)
}
func (dst *BatchDelOfficalCertsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelOfficalCertsReq.Merge(dst, src)
}
func (m *BatchDelOfficalCertsReq) XXX_Size() int {
	return xxx_messageInfo_BatchDelOfficalCertsReq.Size(m)
}
func (m *BatchDelOfficalCertsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelOfficalCertsReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelOfficalCertsReq proto.InternalMessageInfo

func (m *BatchDelOfficalCertsReq) GetInfoList() []*DelCertInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type BatchDelOfficalCertsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDelOfficalCertsResp) Reset()         { *m = BatchDelOfficalCertsResp{} }
func (m *BatchDelOfficalCertsResp) String() string { return proto.CompactTextString(m) }
func (*BatchDelOfficalCertsResp) ProtoMessage()    {}
func (*BatchDelOfficalCertsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{12}
}
func (m *BatchDelOfficalCertsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelOfficalCertsResp.Unmarshal(m, b)
}
func (m *BatchDelOfficalCertsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelOfficalCertsResp.Marshal(b, m, deterministic)
}
func (dst *BatchDelOfficalCertsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelOfficalCertsResp.Merge(dst, src)
}
func (m *BatchDelOfficalCertsResp) XXX_Size() int {
	return xxx_messageInfo_BatchDelOfficalCertsResp.Size(m)
}
func (m *BatchDelOfficalCertsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelOfficalCertsResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelOfficalCertsResp proto.InternalMessageInfo

type BatchGetUserOfficialCertReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetUserOfficialCertReq) Reset()         { *m = BatchGetUserOfficialCertReq{} }
func (m *BatchGetUserOfficialCertReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserOfficialCertReq) ProtoMessage()    {}
func (*BatchGetUserOfficialCertReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{13}
}
func (m *BatchGetUserOfficialCertReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserOfficialCertReq.Unmarshal(m, b)
}
func (m *BatchGetUserOfficialCertReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserOfficialCertReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserOfficialCertReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserOfficialCertReq.Merge(dst, src)
}
func (m *BatchGetUserOfficialCertReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserOfficialCertReq.Size(m)
}
func (m *BatchGetUserOfficialCertReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserOfficialCertReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserOfficialCertReq proto.InternalMessageInfo

func (m *BatchGetUserOfficialCertReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchGetUserOfficialCertResp struct {
	InfoList             []*UserOfficialCertInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *BatchGetUserOfficialCertResp) Reset()         { *m = BatchGetUserOfficialCertResp{} }
func (m *BatchGetUserOfficialCertResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserOfficialCertResp) ProtoMessage()    {}
func (*BatchGetUserOfficialCertResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{14}
}
func (m *BatchGetUserOfficialCertResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserOfficialCertResp.Unmarshal(m, b)
}
func (m *BatchGetUserOfficialCertResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserOfficialCertResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserOfficialCertResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserOfficialCertResp.Merge(dst, src)
}
func (m *BatchGetUserOfficialCertResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserOfficialCertResp.Size(m)
}
func (m *BatchGetUserOfficialCertResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserOfficialCertResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserOfficialCertResp proto.InternalMessageInfo

func (m *BatchGetUserOfficialCertResp) GetInfoList() []*UserOfficialCertInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

// 设置用户佩戴的大v认证样式
type SetUserWearCertificationReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Id                   uint32   `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserWearCertificationReq) Reset()         { *m = SetUserWearCertificationReq{} }
func (m *SetUserWearCertificationReq) String() string { return proto.CompactTextString(m) }
func (*SetUserWearCertificationReq) ProtoMessage()    {}
func (*SetUserWearCertificationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{15}
}
func (m *SetUserWearCertificationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserWearCertificationReq.Unmarshal(m, b)
}
func (m *SetUserWearCertificationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserWearCertificationReq.Marshal(b, m, deterministic)
}
func (dst *SetUserWearCertificationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserWearCertificationReq.Merge(dst, src)
}
func (m *SetUserWearCertificationReq) XXX_Size() int {
	return xxx_messageInfo_SetUserWearCertificationReq.Size(m)
}
func (m *SetUserWearCertificationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserWearCertificationReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserWearCertificationReq proto.InternalMessageInfo

func (m *SetUserWearCertificationReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserWearCertificationReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type SetUserWearCertificationResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserWearCertificationResp) Reset()         { *m = SetUserWearCertificationResp{} }
func (m *SetUserWearCertificationResp) String() string { return proto.CompactTextString(m) }
func (*SetUserWearCertificationResp) ProtoMessage()    {}
func (*SetUserWearCertificationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{16}
}
func (m *SetUserWearCertificationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserWearCertificationResp.Unmarshal(m, b)
}
func (m *SetUserWearCertificationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserWearCertificationResp.Marshal(b, m, deterministic)
}
func (dst *SetUserWearCertificationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserWearCertificationResp.Merge(dst, src)
}
func (m *SetUserWearCertificationResp) XXX_Size() int {
	return xxx_messageInfo_SetUserWearCertificationResp.Size(m)
}
func (m *SetUserWearCertificationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserWearCertificationResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserWearCertificationResp proto.InternalMessageInfo

type GetUserAllOfficialCertsReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserAllOfficialCertsReq) Reset()         { *m = GetUserAllOfficialCertsReq{} }
func (m *GetUserAllOfficialCertsReq) String() string { return proto.CompactTextString(m) }
func (*GetUserAllOfficialCertsReq) ProtoMessage()    {}
func (*GetUserAllOfficialCertsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{17}
}
func (m *GetUserAllOfficialCertsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAllOfficialCertsReq.Unmarshal(m, b)
}
func (m *GetUserAllOfficialCertsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAllOfficialCertsReq.Marshal(b, m, deterministic)
}
func (dst *GetUserAllOfficialCertsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAllOfficialCertsReq.Merge(dst, src)
}
func (m *GetUserAllOfficialCertsReq) XXX_Size() int {
	return xxx_messageInfo_GetUserAllOfficialCertsReq.Size(m)
}
func (m *GetUserAllOfficialCertsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAllOfficialCertsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAllOfficialCertsReq proto.InternalMessageInfo

func (m *GetUserAllOfficialCertsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserAllOfficialCertsResp struct {
	IsCertified          bool                `protobuf:"varint,1,opt,name=is_certified,json=isCertified,proto3" json:"is_certified,omitempty"`
	CertList             []*OfficialCertInfo `protobuf:"bytes,2,rep,name=cert_list,json=certList,proto3" json:"cert_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetUserAllOfficialCertsResp) Reset()         { *m = GetUserAllOfficialCertsResp{} }
func (m *GetUserAllOfficialCertsResp) String() string { return proto.CompactTextString(m) }
func (*GetUserAllOfficialCertsResp) ProtoMessage()    {}
func (*GetUserAllOfficialCertsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{18}
}
func (m *GetUserAllOfficialCertsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAllOfficialCertsResp.Unmarshal(m, b)
}
func (m *GetUserAllOfficialCertsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAllOfficialCertsResp.Marshal(b, m, deterministic)
}
func (dst *GetUserAllOfficialCertsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAllOfficialCertsResp.Merge(dst, src)
}
func (m *GetUserAllOfficialCertsResp) XXX_Size() int {
	return xxx_messageInfo_GetUserAllOfficialCertsResp.Size(m)
}
func (m *GetUserAllOfficialCertsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAllOfficialCertsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAllOfficialCertsResp proto.InternalMessageInfo

func (m *GetUserAllOfficialCertsResp) GetIsCertified() bool {
	if m != nil {
		return m.IsCertified
	}
	return false
}

func (m *GetUserAllOfficialCertsResp) GetCertList() []*OfficialCertInfo {
	if m != nil {
		return m.CertList
	}
	return nil
}

// 获取用户有效的特权属性
type GetUserCertAttributeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserCertAttributeReq) Reset()         { *m = GetUserCertAttributeReq{} }
func (m *GetUserCertAttributeReq) String() string { return proto.CompactTextString(m) }
func (*GetUserCertAttributeReq) ProtoMessage()    {}
func (*GetUserCertAttributeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{19}
}
func (m *GetUserCertAttributeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCertAttributeReq.Unmarshal(m, b)
}
func (m *GetUserCertAttributeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCertAttributeReq.Marshal(b, m, deterministic)
}
func (dst *GetUserCertAttributeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCertAttributeReq.Merge(dst, src)
}
func (m *GetUserCertAttributeReq) XXX_Size() int {
	return xxx_messageInfo_GetUserCertAttributeReq.Size(m)
}
func (m *GetUserCertAttributeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCertAttributeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCertAttributeReq proto.InternalMessageInfo

func (m *GetUserCertAttributeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

// 有效的全部特权 ECertAttrType
type GetUserCertAttributeResp struct {
	Attr                 uint32   `protobuf:"varint,1,opt,name=attr,proto3" json:"attr,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserCertAttributeResp) Reset()         { *m = GetUserCertAttributeResp{} }
func (m *GetUserCertAttributeResp) String() string { return proto.CompactTextString(m) }
func (*GetUserCertAttributeResp) ProtoMessage()    {}
func (*GetUserCertAttributeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{20}
}
func (m *GetUserCertAttributeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCertAttributeResp.Unmarshal(m, b)
}
func (m *GetUserCertAttributeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCertAttributeResp.Marshal(b, m, deterministic)
}
func (dst *GetUserCertAttributeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCertAttributeResp.Merge(dst, src)
}
func (m *GetUserCertAttributeResp) XXX_Size() int {
	return xxx_messageInfo_GetUserCertAttributeResp.Size(m)
}
func (m *GetUserCertAttributeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCertAttributeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCertAttributeResp proto.InternalMessageInfo

func (m *GetUserCertAttributeResp) GetAttr() uint32 {
	if m != nil {
		return m.Attr
	}
	return 0
}

// 查询是否有如下特权
type CheckUserCertAttributeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Attr                 uint32   `protobuf:"varint,2,opt,name=attr,proto3" json:"attr,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUserCertAttributeReq) Reset()         { *m = CheckUserCertAttributeReq{} }
func (m *CheckUserCertAttributeReq) String() string { return proto.CompactTextString(m) }
func (*CheckUserCertAttributeReq) ProtoMessage()    {}
func (*CheckUserCertAttributeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{21}
}
func (m *CheckUserCertAttributeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserCertAttributeReq.Unmarshal(m, b)
}
func (m *CheckUserCertAttributeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserCertAttributeReq.Marshal(b, m, deterministic)
}
func (dst *CheckUserCertAttributeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserCertAttributeReq.Merge(dst, src)
}
func (m *CheckUserCertAttributeReq) XXX_Size() int {
	return xxx_messageInfo_CheckUserCertAttributeReq.Size(m)
}
func (m *CheckUserCertAttributeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserCertAttributeReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserCertAttributeReq proto.InternalMessageInfo

func (m *CheckUserCertAttributeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckUserCertAttributeReq) GetAttr() uint32 {
	if m != nil {
		return m.Attr
	}
	return 0
}

type CheckUserCertAttributeResp struct {
	Ok                   bool     `protobuf:"varint,1,opt,name=ok,proto3" json:"ok,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUserCertAttributeResp) Reset()         { *m = CheckUserCertAttributeResp{} }
func (m *CheckUserCertAttributeResp) String() string { return proto.CompactTextString(m) }
func (*CheckUserCertAttributeResp) ProtoMessage()    {}
func (*CheckUserCertAttributeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{22}
}
func (m *CheckUserCertAttributeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserCertAttributeResp.Unmarshal(m, b)
}
func (m *CheckUserCertAttributeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserCertAttributeResp.Marshal(b, m, deterministic)
}
func (dst *CheckUserCertAttributeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserCertAttributeResp.Merge(dst, src)
}
func (m *CheckUserCertAttributeResp) XXX_Size() int {
	return xxx_messageInfo_CheckUserCertAttributeResp.Size(m)
}
func (m *CheckUserCertAttributeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserCertAttributeResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserCertAttributeResp proto.InternalMessageInfo

func (m *CheckUserCertAttributeResp) GetOk() bool {
	if m != nil {
		return m.Ok
	}
	return false
}

// 新增
// 主理人信息
type DirectorCertInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Ttid                 string   `protobuf:"bytes,2,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	CooperationType      uint32   `protobuf:"varint,4,opt,name=cooperation_type,json=cooperationType,proto3" json:"cooperation_type,omitempty"`
	Status               uint32   `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	Introduce            string   `protobuf:"bytes,6,opt,name=introduce,proto3" json:"introduce,omitempty"`
	Manager              string   `protobuf:"bytes,7,opt,name=manager,proto3" json:"manager,omitempty"`
	Ts                   uint64   `protobuf:"varint,8,opt,name=ts,proto3" json:"ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DirectorCertInfo) Reset()         { *m = DirectorCertInfo{} }
func (m *DirectorCertInfo) String() string { return proto.CompactTextString(m) }
func (*DirectorCertInfo) ProtoMessage()    {}
func (*DirectorCertInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{23}
}
func (m *DirectorCertInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DirectorCertInfo.Unmarshal(m, b)
}
func (m *DirectorCertInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DirectorCertInfo.Marshal(b, m, deterministic)
}
func (dst *DirectorCertInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DirectorCertInfo.Merge(dst, src)
}
func (m *DirectorCertInfo) XXX_Size() int {
	return xxx_messageInfo_DirectorCertInfo.Size(m)
}
func (m *DirectorCertInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DirectorCertInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DirectorCertInfo proto.InternalMessageInfo

func (m *DirectorCertInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DirectorCertInfo) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *DirectorCertInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *DirectorCertInfo) GetCooperationType() uint32 {
	if m != nil {
		return m.CooperationType
	}
	return 0
}

func (m *DirectorCertInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *DirectorCertInfo) GetIntroduce() string {
	if m != nil {
		return m.Introduce
	}
	return ""
}

func (m *DirectorCertInfo) GetManager() string {
	if m != nil {
		return m.Manager
	}
	return ""
}

func (m *DirectorCertInfo) GetTs() uint64 {
	if m != nil {
		return m.Ts
	}
	return 0
}

type UserDirectorCertInfo struct {
	IsCertified          bool              `protobuf:"varint,1,opt,name=is_certified,json=isCertified,proto3" json:"is_certified,omitempty"`
	Cert                 *DirectorCertInfo `protobuf:"bytes,2,opt,name=cert,proto3" json:"cert,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UserDirectorCertInfo) Reset()         { *m = UserDirectorCertInfo{} }
func (m *UserDirectorCertInfo) String() string { return proto.CompactTextString(m) }
func (*UserDirectorCertInfo) ProtoMessage()    {}
func (*UserDirectorCertInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{24}
}
func (m *UserDirectorCertInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDirectorCertInfo.Unmarshal(m, b)
}
func (m *UserDirectorCertInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDirectorCertInfo.Marshal(b, m, deterministic)
}
func (dst *UserDirectorCertInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDirectorCertInfo.Merge(dst, src)
}
func (m *UserDirectorCertInfo) XXX_Size() int {
	return xxx_messageInfo_UserDirectorCertInfo.Size(m)
}
func (m *UserDirectorCertInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDirectorCertInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserDirectorCertInfo proto.InternalMessageInfo

func (m *UserDirectorCertInfo) GetIsCertified() bool {
	if m != nil {
		return m.IsCertified
	}
	return false
}

func (m *UserDirectorCertInfo) GetCert() *DirectorCertInfo {
	if m != nil {
		return m.Cert
	}
	return nil
}

type GetUserDirectorCertReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserDirectorCertReq) Reset()         { *m = GetUserDirectorCertReq{} }
func (m *GetUserDirectorCertReq) String() string { return proto.CompactTextString(m) }
func (*GetUserDirectorCertReq) ProtoMessage()    {}
func (*GetUserDirectorCertReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{25}
}
func (m *GetUserDirectorCertReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserDirectorCertReq.Unmarshal(m, b)
}
func (m *GetUserDirectorCertReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserDirectorCertReq.Marshal(b, m, deterministic)
}
func (dst *GetUserDirectorCertReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserDirectorCertReq.Merge(dst, src)
}
func (m *GetUserDirectorCertReq) XXX_Size() int {
	return xxx_messageInfo_GetUserDirectorCertReq.Size(m)
}
func (m *GetUserDirectorCertReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserDirectorCertReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserDirectorCertReq proto.InternalMessageInfo

func (m *GetUserDirectorCertReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserDirectorCertResp struct {
	Info                 *UserDirectorCertInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetUserDirectorCertResp) Reset()         { *m = GetUserDirectorCertResp{} }
func (m *GetUserDirectorCertResp) String() string { return proto.CompactTextString(m) }
func (*GetUserDirectorCertResp) ProtoMessage()    {}
func (*GetUserDirectorCertResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{26}
}
func (m *GetUserDirectorCertResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserDirectorCertResp.Unmarshal(m, b)
}
func (m *GetUserDirectorCertResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserDirectorCertResp.Marshal(b, m, deterministic)
}
func (dst *GetUserDirectorCertResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserDirectorCertResp.Merge(dst, src)
}
func (m *GetUserDirectorCertResp) XXX_Size() int {
	return xxx_messageInfo_GetUserDirectorCertResp.Size(m)
}
func (m *GetUserDirectorCertResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserDirectorCertResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserDirectorCertResp proto.InternalMessageInfo

func (m *GetUserDirectorCertResp) GetInfo() *UserDirectorCertInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type BatchGetUserDirectorCertReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetUserDirectorCertReq) Reset()         { *m = BatchGetUserDirectorCertReq{} }
func (m *BatchGetUserDirectorCertReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserDirectorCertReq) ProtoMessage()    {}
func (*BatchGetUserDirectorCertReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{27}
}
func (m *BatchGetUserDirectorCertReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserDirectorCertReq.Unmarshal(m, b)
}
func (m *BatchGetUserDirectorCertReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserDirectorCertReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserDirectorCertReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserDirectorCertReq.Merge(dst, src)
}
func (m *BatchGetUserDirectorCertReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserDirectorCertReq.Size(m)
}
func (m *BatchGetUserDirectorCertReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserDirectorCertReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserDirectorCertReq proto.InternalMessageInfo

func (m *BatchGetUserDirectorCertReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchGetUserDirectorCertResp struct {
	InfoList             []*UserDirectorCertInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *BatchGetUserDirectorCertResp) Reset()         { *m = BatchGetUserDirectorCertResp{} }
func (m *BatchGetUserDirectorCertResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserDirectorCertResp) ProtoMessage()    {}
func (*BatchGetUserDirectorCertResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{28}
}
func (m *BatchGetUserDirectorCertResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserDirectorCertResp.Unmarshal(m, b)
}
func (m *BatchGetUserDirectorCertResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserDirectorCertResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserDirectorCertResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserDirectorCertResp.Merge(dst, src)
}
func (m *BatchGetUserDirectorCertResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserDirectorCertResp.Size(m)
}
func (m *BatchGetUserDirectorCertResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserDirectorCertResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserDirectorCertResp proto.InternalMessageInfo

func (m *BatchGetUserDirectorCertResp) GetInfoList() []*UserDirectorCertInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type SetUserDirectorCertReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Status               uint32   `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	CooperationType      uint32   `protobuf:"varint,3,opt,name=cooperation_type,json=cooperationType,proto3" json:"cooperation_type,omitempty"`
	Introduce            string   `protobuf:"bytes,4,opt,name=introduce,proto3" json:"introduce,omitempty"`
	Manager              string   `protobuf:"bytes,5,opt,name=manager,proto3" json:"manager,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserDirectorCertReq) Reset()         { *m = SetUserDirectorCertReq{} }
func (m *SetUserDirectorCertReq) String() string { return proto.CompactTextString(m) }
func (*SetUserDirectorCertReq) ProtoMessage()    {}
func (*SetUserDirectorCertReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{29}
}
func (m *SetUserDirectorCertReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserDirectorCertReq.Unmarshal(m, b)
}
func (m *SetUserDirectorCertReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserDirectorCertReq.Marshal(b, m, deterministic)
}
func (dst *SetUserDirectorCertReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserDirectorCertReq.Merge(dst, src)
}
func (m *SetUserDirectorCertReq) XXX_Size() int {
	return xxx_messageInfo_SetUserDirectorCertReq.Size(m)
}
func (m *SetUserDirectorCertReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserDirectorCertReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserDirectorCertReq proto.InternalMessageInfo

func (m *SetUserDirectorCertReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserDirectorCertReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *SetUserDirectorCertReq) GetCooperationType() uint32 {
	if m != nil {
		return m.CooperationType
	}
	return 0
}

func (m *SetUserDirectorCertReq) GetIntroduce() string {
	if m != nil {
		return m.Introduce
	}
	return ""
}

func (m *SetUserDirectorCertReq) GetManager() string {
	if m != nil {
		return m.Manager
	}
	return ""
}

type SetUserDirectorCertResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserDirectorCertResp) Reset()         { *m = SetUserDirectorCertResp{} }
func (m *SetUserDirectorCertResp) String() string { return proto.CompactTextString(m) }
func (*SetUserDirectorCertResp) ProtoMessage()    {}
func (*SetUserDirectorCertResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{30}
}
func (m *SetUserDirectorCertResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserDirectorCertResp.Unmarshal(m, b)
}
func (m *SetUserDirectorCertResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserDirectorCertResp.Marshal(b, m, deterministic)
}
func (dst *SetUserDirectorCertResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserDirectorCertResp.Merge(dst, src)
}
func (m *SetUserDirectorCertResp) XXX_Size() int {
	return xxx_messageInfo_SetUserDirectorCertResp.Size(m)
}
func (m *SetUserDirectorCertResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserDirectorCertResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserDirectorCertResp proto.InternalMessageInfo

type AddUserDirectorCertReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CooperationType      uint32   `protobuf:"varint,2,opt,name=cooperation_type,json=cooperationType,proto3" json:"cooperation_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUserDirectorCertReq) Reset()         { *m = AddUserDirectorCertReq{} }
func (m *AddUserDirectorCertReq) String() string { return proto.CompactTextString(m) }
func (*AddUserDirectorCertReq) ProtoMessage()    {}
func (*AddUserDirectorCertReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{31}
}
func (m *AddUserDirectorCertReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserDirectorCertReq.Unmarshal(m, b)
}
func (m *AddUserDirectorCertReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserDirectorCertReq.Marshal(b, m, deterministic)
}
func (dst *AddUserDirectorCertReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserDirectorCertReq.Merge(dst, src)
}
func (m *AddUserDirectorCertReq) XXX_Size() int {
	return xxx_messageInfo_AddUserDirectorCertReq.Size(m)
}
func (m *AddUserDirectorCertReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserDirectorCertReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserDirectorCertReq proto.InternalMessageInfo

func (m *AddUserDirectorCertReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddUserDirectorCertReq) GetCooperationType() uint32 {
	if m != nil {
		return m.CooperationType
	}
	return 0
}

type AddUserDirectorCertResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUserDirectorCertResp) Reset()         { *m = AddUserDirectorCertResp{} }
func (m *AddUserDirectorCertResp) String() string { return proto.CompactTextString(m) }
func (*AddUserDirectorCertResp) ProtoMessage()    {}
func (*AddUserDirectorCertResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{32}
}
func (m *AddUserDirectorCertResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserDirectorCertResp.Unmarshal(m, b)
}
func (m *AddUserDirectorCertResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserDirectorCertResp.Marshal(b, m, deterministic)
}
func (dst *AddUserDirectorCertResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserDirectorCertResp.Merge(dst, src)
}
func (m *AddUserDirectorCertResp) XXX_Size() int {
	return xxx_messageInfo_AddUserDirectorCertResp.Size(m)
}
func (m *AddUserDirectorCertResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserDirectorCertResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserDirectorCertResp proto.InternalMessageInfo

type ListUserDirectorCertReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	PageSize             uint32   `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Ttid                 string   `protobuf:"bytes,3,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Nickname             string   `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Manager              string   `protobuf:"bytes,5,opt,name=manager,proto3" json:"manager,omitempty"`
	CooperationType      uint32   `protobuf:"varint,6,opt,name=cooperation_type,json=cooperationType,proto3" json:"cooperation_type,omitempty"`
	Status               uint32   `protobuf:"varint,7,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListUserDirectorCertReq) Reset()         { *m = ListUserDirectorCertReq{} }
func (m *ListUserDirectorCertReq) String() string { return proto.CompactTextString(m) }
func (*ListUserDirectorCertReq) ProtoMessage()    {}
func (*ListUserDirectorCertReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{33}
}
func (m *ListUserDirectorCertReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListUserDirectorCertReq.Unmarshal(m, b)
}
func (m *ListUserDirectorCertReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListUserDirectorCertReq.Marshal(b, m, deterministic)
}
func (dst *ListUserDirectorCertReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListUserDirectorCertReq.Merge(dst, src)
}
func (m *ListUserDirectorCertReq) XXX_Size() int {
	return xxx_messageInfo_ListUserDirectorCertReq.Size(m)
}
func (m *ListUserDirectorCertReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListUserDirectorCertReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListUserDirectorCertReq proto.InternalMessageInfo

func (m *ListUserDirectorCertReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *ListUserDirectorCertReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *ListUserDirectorCertReq) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *ListUserDirectorCertReq) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ListUserDirectorCertReq) GetManager() string {
	if m != nil {
		return m.Manager
	}
	return ""
}

func (m *ListUserDirectorCertReq) GetCooperationType() uint32 {
	if m != nil {
		return m.CooperationType
	}
	return 0
}

func (m *ListUserDirectorCertReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type ListUserDirectorCertResp struct {
	Total                uint32                  `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	InfoList             []*UserDirectorCertInfo `protobuf:"bytes,2,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *ListUserDirectorCertResp) Reset()         { *m = ListUserDirectorCertResp{} }
func (m *ListUserDirectorCertResp) String() string { return proto.CompactTextString(m) }
func (*ListUserDirectorCertResp) ProtoMessage()    {}
func (*ListUserDirectorCertResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_officialcert_c8fd96492302b0f1, []int{34}
}
func (m *ListUserDirectorCertResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListUserDirectorCertResp.Unmarshal(m, b)
}
func (m *ListUserDirectorCertResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListUserDirectorCertResp.Marshal(b, m, deterministic)
}
func (dst *ListUserDirectorCertResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListUserDirectorCertResp.Merge(dst, src)
}
func (m *ListUserDirectorCertResp) XXX_Size() int {
	return xxx_messageInfo_ListUserDirectorCertResp.Size(m)
}
func (m *ListUserDirectorCertResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListUserDirectorCertResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListUserDirectorCertResp proto.InternalMessageInfo

func (m *ListUserDirectorCertResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *ListUserDirectorCertResp) GetInfoList() []*UserDirectorCertInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func init() {
	proto.RegisterType((*OfficialCertInfo)(nil), "officialcert.OfficialCertInfo")
	proto.RegisterType((*UserOfficialCertInfo)(nil), "officialcert.UserOfficialCertInfo")
	proto.RegisterType((*GetUserOfficialCertReq)(nil), "officialcert.GetUserOfficialCertReq")
	proto.RegisterType((*GetUserOfficialCertResp)(nil), "officialcert.GetUserOfficialCertResp")
	proto.RegisterType((*ListUserOfficialCertReq)(nil), "officialcert.ListUserOfficialCertReq")
	proto.RegisterType((*ListUserOfficialCertResp)(nil), "officialcert.ListUserOfficialCertResp")
	proto.RegisterType((*SetUserOfficialCertReq)(nil), "officialcert.SetUserOfficialCertReq")
	proto.RegisterType((*SetUserOfficialCertResp)(nil), "officialcert.SetUserOfficialCertResp")
	proto.RegisterType((*DelUserOfficialCertReq)(nil), "officialcert.DelUserOfficialCertReq")
	proto.RegisterType((*DelUserOfficialCertResp)(nil), "officialcert.DelUserOfficialCertResp")
	proto.RegisterType((*DelCertInfo)(nil), "officialcert.DelCertInfo")
	proto.RegisterType((*BatchDelOfficalCertsReq)(nil), "officialcert.BatchDelOfficalCertsReq")
	proto.RegisterType((*BatchDelOfficalCertsResp)(nil), "officialcert.BatchDelOfficalCertsResp")
	proto.RegisterType((*BatchGetUserOfficialCertReq)(nil), "officialcert.BatchGetUserOfficialCertReq")
	proto.RegisterType((*BatchGetUserOfficialCertResp)(nil), "officialcert.BatchGetUserOfficialCertResp")
	proto.RegisterType((*SetUserWearCertificationReq)(nil), "officialcert.SetUserWearCertificationReq")
	proto.RegisterType((*SetUserWearCertificationResp)(nil), "officialcert.SetUserWearCertificationResp")
	proto.RegisterType((*GetUserAllOfficialCertsReq)(nil), "officialcert.GetUserAllOfficialCertsReq")
	proto.RegisterType((*GetUserAllOfficialCertsResp)(nil), "officialcert.GetUserAllOfficialCertsResp")
	proto.RegisterType((*GetUserCertAttributeReq)(nil), "officialcert.GetUserCertAttributeReq")
	proto.RegisterType((*GetUserCertAttributeResp)(nil), "officialcert.GetUserCertAttributeResp")
	proto.RegisterType((*CheckUserCertAttributeReq)(nil), "officialcert.CheckUserCertAttributeReq")
	proto.RegisterType((*CheckUserCertAttributeResp)(nil), "officialcert.CheckUserCertAttributeResp")
	proto.RegisterType((*DirectorCertInfo)(nil), "officialcert.DirectorCertInfo")
	proto.RegisterType((*UserDirectorCertInfo)(nil), "officialcert.UserDirectorCertInfo")
	proto.RegisterType((*GetUserDirectorCertReq)(nil), "officialcert.GetUserDirectorCertReq")
	proto.RegisterType((*GetUserDirectorCertResp)(nil), "officialcert.GetUserDirectorCertResp")
	proto.RegisterType((*BatchGetUserDirectorCertReq)(nil), "officialcert.BatchGetUserDirectorCertReq")
	proto.RegisterType((*BatchGetUserDirectorCertResp)(nil), "officialcert.BatchGetUserDirectorCertResp")
	proto.RegisterType((*SetUserDirectorCertReq)(nil), "officialcert.SetUserDirectorCertReq")
	proto.RegisterType((*SetUserDirectorCertResp)(nil), "officialcert.SetUserDirectorCertResp")
	proto.RegisterType((*AddUserDirectorCertReq)(nil), "officialcert.AddUserDirectorCertReq")
	proto.RegisterType((*AddUserDirectorCertResp)(nil), "officialcert.AddUserDirectorCertResp")
	proto.RegisterType((*ListUserDirectorCertReq)(nil), "officialcert.ListUserDirectorCertReq")
	proto.RegisterType((*ListUserDirectorCertResp)(nil), "officialcert.ListUserDirectorCertResp")
	proto.RegisterEnum("officialcert.ECertAttrType", ECertAttrType_name, ECertAttrType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// OfficialCertClient is the client API for OfficialCert service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type OfficialCertClient interface {
	GetUserOfficialCert(ctx context.Context, in *GetUserOfficialCertReq, opts ...grpc.CallOption) (*GetUserOfficialCertResp, error)
	ListUserOfficialCert(ctx context.Context, in *ListUserOfficialCertReq, opts ...grpc.CallOption) (*ListUserOfficialCertResp, error)
	// 包含主理人style
	ListUserOfficialCertV2(ctx context.Context, in *ListUserOfficialCertReq, opts ...grpc.CallOption) (*ListUserOfficialCertResp, error)
	SetUserOfficialCert(ctx context.Context, in *SetUserOfficialCertReq, opts ...grpc.CallOption) (*SetUserOfficialCertResp, error)
	DelUserOfficialCert(ctx context.Context, in *DelUserOfficialCertReq, opts ...grpc.CallOption) (*DelUserOfficialCertResp, error)
	BatchGetUserOfficialCert(ctx context.Context, in *BatchGetUserOfficialCertReq, opts ...grpc.CallOption) (*BatchGetUserOfficialCertResp, error)
	SetUserWearCertification(ctx context.Context, in *SetUserWearCertificationReq, opts ...grpc.CallOption) (*SetUserWearCertificationResp, error)
	GetUserAllOfficialCerts(ctx context.Context, in *GetUserAllOfficialCertsReq, opts ...grpc.CallOption) (*GetUserAllOfficialCertsResp, error)
	GetUserCertAttribute(ctx context.Context, in *GetUserCertAttributeReq, opts ...grpc.CallOption) (*GetUserCertAttributeResp, error)
	CheckUserCertAttribute(ctx context.Context, in *CheckUserCertAttributeReq, opts ...grpc.CallOption) (*CheckUserCertAttributeResp, error)
	BatchDelOfficalCerts(ctx context.Context, in *BatchDelOfficalCertsReq, opts ...grpc.CallOption) (*BatchDelOfficalCertsResp, error)
	// 获取用户的主理人认证信息
	GetUserDirectorCert(ctx context.Context, in *GetUserDirectorCertReq, opts ...grpc.CallOption) (*GetUserDirectorCertResp, error)
	// 批量获取用户的主理人认证信息
	BatchGetUserDirectorCert(ctx context.Context, in *BatchGetUserDirectorCertReq, opts ...grpc.CallOption) (*BatchGetUserDirectorCertResp, error)
	// 后台创建主理人
	AddUserDirectorCert(ctx context.Context, in *AddUserDirectorCertReq, opts ...grpc.CallOption) (*AddUserDirectorCertResp, error)
	// 后台配置主理人相关信息
	SetUserDirectorCert(ctx context.Context, in *SetUserDirectorCertReq, opts ...grpc.CallOption) (*SetUserDirectorCertResp, error)
	// 获取主理人列表
	ListUserDirectorCert(ctx context.Context, in *ListUserDirectorCertReq, opts ...grpc.CallOption) (*ListUserDirectorCertResp, error)
}

type officialCertClient struct {
	cc *grpc.ClientConn
}

func NewOfficialCertClient(cc *grpc.ClientConn) OfficialCertClient {
	return &officialCertClient{cc}
}

func (c *officialCertClient) GetUserOfficialCert(ctx context.Context, in *GetUserOfficialCertReq, opts ...grpc.CallOption) (*GetUserOfficialCertResp, error) {
	out := new(GetUserOfficialCertResp)
	err := c.cc.Invoke(ctx, "/officialcert.OfficialCert/GetUserOfficialCert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) ListUserOfficialCert(ctx context.Context, in *ListUserOfficialCertReq, opts ...grpc.CallOption) (*ListUserOfficialCertResp, error) {
	out := new(ListUserOfficialCertResp)
	err := c.cc.Invoke(ctx, "/officialcert.OfficialCert/ListUserOfficialCert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) ListUserOfficialCertV2(ctx context.Context, in *ListUserOfficialCertReq, opts ...grpc.CallOption) (*ListUserOfficialCertResp, error) {
	out := new(ListUserOfficialCertResp)
	err := c.cc.Invoke(ctx, "/officialcert.OfficialCert/ListUserOfficialCertV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) SetUserOfficialCert(ctx context.Context, in *SetUserOfficialCertReq, opts ...grpc.CallOption) (*SetUserOfficialCertResp, error) {
	out := new(SetUserOfficialCertResp)
	err := c.cc.Invoke(ctx, "/officialcert.OfficialCert/SetUserOfficialCert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) DelUserOfficialCert(ctx context.Context, in *DelUserOfficialCertReq, opts ...grpc.CallOption) (*DelUserOfficialCertResp, error) {
	out := new(DelUserOfficialCertResp)
	err := c.cc.Invoke(ctx, "/officialcert.OfficialCert/DelUserOfficialCert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) BatchGetUserOfficialCert(ctx context.Context, in *BatchGetUserOfficialCertReq, opts ...grpc.CallOption) (*BatchGetUserOfficialCertResp, error) {
	out := new(BatchGetUserOfficialCertResp)
	err := c.cc.Invoke(ctx, "/officialcert.OfficialCert/BatchGetUserOfficialCert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) SetUserWearCertification(ctx context.Context, in *SetUserWearCertificationReq, opts ...grpc.CallOption) (*SetUserWearCertificationResp, error) {
	out := new(SetUserWearCertificationResp)
	err := c.cc.Invoke(ctx, "/officialcert.OfficialCert/SetUserWearCertification", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) GetUserAllOfficialCerts(ctx context.Context, in *GetUserAllOfficialCertsReq, opts ...grpc.CallOption) (*GetUserAllOfficialCertsResp, error) {
	out := new(GetUserAllOfficialCertsResp)
	err := c.cc.Invoke(ctx, "/officialcert.OfficialCert/GetUserAllOfficialCerts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) GetUserCertAttribute(ctx context.Context, in *GetUserCertAttributeReq, opts ...grpc.CallOption) (*GetUserCertAttributeResp, error) {
	out := new(GetUserCertAttributeResp)
	err := c.cc.Invoke(ctx, "/officialcert.OfficialCert/GetUserCertAttribute", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) CheckUserCertAttribute(ctx context.Context, in *CheckUserCertAttributeReq, opts ...grpc.CallOption) (*CheckUserCertAttributeResp, error) {
	out := new(CheckUserCertAttributeResp)
	err := c.cc.Invoke(ctx, "/officialcert.OfficialCert/CheckUserCertAttribute", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) BatchDelOfficalCerts(ctx context.Context, in *BatchDelOfficalCertsReq, opts ...grpc.CallOption) (*BatchDelOfficalCertsResp, error) {
	out := new(BatchDelOfficalCertsResp)
	err := c.cc.Invoke(ctx, "/officialcert.OfficialCert/BatchDelOfficalCerts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) GetUserDirectorCert(ctx context.Context, in *GetUserDirectorCertReq, opts ...grpc.CallOption) (*GetUserDirectorCertResp, error) {
	out := new(GetUserDirectorCertResp)
	err := c.cc.Invoke(ctx, "/officialcert.OfficialCert/GetUserDirectorCert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) BatchGetUserDirectorCert(ctx context.Context, in *BatchGetUserDirectorCertReq, opts ...grpc.CallOption) (*BatchGetUserDirectorCertResp, error) {
	out := new(BatchGetUserDirectorCertResp)
	err := c.cc.Invoke(ctx, "/officialcert.OfficialCert/BatchGetUserDirectorCert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) AddUserDirectorCert(ctx context.Context, in *AddUserDirectorCertReq, opts ...grpc.CallOption) (*AddUserDirectorCertResp, error) {
	out := new(AddUserDirectorCertResp)
	err := c.cc.Invoke(ctx, "/officialcert.OfficialCert/AddUserDirectorCert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) SetUserDirectorCert(ctx context.Context, in *SetUserDirectorCertReq, opts ...grpc.CallOption) (*SetUserDirectorCertResp, error) {
	out := new(SetUserDirectorCertResp)
	err := c.cc.Invoke(ctx, "/officialcert.OfficialCert/SetUserDirectorCert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialCertClient) ListUserDirectorCert(ctx context.Context, in *ListUserDirectorCertReq, opts ...grpc.CallOption) (*ListUserDirectorCertResp, error) {
	out := new(ListUserDirectorCertResp)
	err := c.cc.Invoke(ctx, "/officialcert.OfficialCert/ListUserDirectorCert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OfficialCertServer is the server API for OfficialCert service.
type OfficialCertServer interface {
	GetUserOfficialCert(context.Context, *GetUserOfficialCertReq) (*GetUserOfficialCertResp, error)
	ListUserOfficialCert(context.Context, *ListUserOfficialCertReq) (*ListUserOfficialCertResp, error)
	// 包含主理人style
	ListUserOfficialCertV2(context.Context, *ListUserOfficialCertReq) (*ListUserOfficialCertResp, error)
	SetUserOfficialCert(context.Context, *SetUserOfficialCertReq) (*SetUserOfficialCertResp, error)
	DelUserOfficialCert(context.Context, *DelUserOfficialCertReq) (*DelUserOfficialCertResp, error)
	BatchGetUserOfficialCert(context.Context, *BatchGetUserOfficialCertReq) (*BatchGetUserOfficialCertResp, error)
	SetUserWearCertification(context.Context, *SetUserWearCertificationReq) (*SetUserWearCertificationResp, error)
	GetUserAllOfficialCerts(context.Context, *GetUserAllOfficialCertsReq) (*GetUserAllOfficialCertsResp, error)
	GetUserCertAttribute(context.Context, *GetUserCertAttributeReq) (*GetUserCertAttributeResp, error)
	CheckUserCertAttribute(context.Context, *CheckUserCertAttributeReq) (*CheckUserCertAttributeResp, error)
	BatchDelOfficalCerts(context.Context, *BatchDelOfficalCertsReq) (*BatchDelOfficalCertsResp, error)
	// 获取用户的主理人认证信息
	GetUserDirectorCert(context.Context, *GetUserDirectorCertReq) (*GetUserDirectorCertResp, error)
	// 批量获取用户的主理人认证信息
	BatchGetUserDirectorCert(context.Context, *BatchGetUserDirectorCertReq) (*BatchGetUserDirectorCertResp, error)
	// 后台创建主理人
	AddUserDirectorCert(context.Context, *AddUserDirectorCertReq) (*AddUserDirectorCertResp, error)
	// 后台配置主理人相关信息
	SetUserDirectorCert(context.Context, *SetUserDirectorCertReq) (*SetUserDirectorCertResp, error)
	// 获取主理人列表
	ListUserDirectorCert(context.Context, *ListUserDirectorCertReq) (*ListUserDirectorCertResp, error)
}

func RegisterOfficialCertServer(s *grpc.Server, srv OfficialCertServer) {
	s.RegisterService(&_OfficialCert_serviceDesc, srv)
}

func _OfficialCert_GetUserOfficialCert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserOfficialCertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).GetUserOfficialCert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert.OfficialCert/GetUserOfficialCert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).GetUserOfficialCert(ctx, req.(*GetUserOfficialCertReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_ListUserOfficialCert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUserOfficialCertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).ListUserOfficialCert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert.OfficialCert/ListUserOfficialCert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).ListUserOfficialCert(ctx, req.(*ListUserOfficialCertReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_ListUserOfficialCertV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUserOfficialCertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).ListUserOfficialCertV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert.OfficialCert/ListUserOfficialCertV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).ListUserOfficialCertV2(ctx, req.(*ListUserOfficialCertReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_SetUserOfficialCert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserOfficialCertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).SetUserOfficialCert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert.OfficialCert/SetUserOfficialCert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).SetUserOfficialCert(ctx, req.(*SetUserOfficialCertReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_DelUserOfficialCert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelUserOfficialCertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).DelUserOfficialCert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert.OfficialCert/DelUserOfficialCert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).DelUserOfficialCert(ctx, req.(*DelUserOfficialCertReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_BatchGetUserOfficialCert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserOfficialCertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).BatchGetUserOfficialCert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert.OfficialCert/BatchGetUserOfficialCert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).BatchGetUserOfficialCert(ctx, req.(*BatchGetUserOfficialCertReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_SetUserWearCertification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserWearCertificationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).SetUserWearCertification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert.OfficialCert/SetUserWearCertification",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).SetUserWearCertification(ctx, req.(*SetUserWearCertificationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_GetUserAllOfficialCerts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserAllOfficialCertsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).GetUserAllOfficialCerts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert.OfficialCert/GetUserAllOfficialCerts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).GetUserAllOfficialCerts(ctx, req.(*GetUserAllOfficialCertsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_GetUserCertAttribute_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserCertAttributeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).GetUserCertAttribute(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert.OfficialCert/GetUserCertAttribute",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).GetUserCertAttribute(ctx, req.(*GetUserCertAttributeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_CheckUserCertAttribute_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserCertAttributeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).CheckUserCertAttribute(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert.OfficialCert/CheckUserCertAttribute",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).CheckUserCertAttribute(ctx, req.(*CheckUserCertAttributeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_BatchDelOfficalCerts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDelOfficalCertsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).BatchDelOfficalCerts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert.OfficialCert/BatchDelOfficalCerts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).BatchDelOfficalCerts(ctx, req.(*BatchDelOfficalCertsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_GetUserDirectorCert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserDirectorCertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).GetUserDirectorCert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert.OfficialCert/GetUserDirectorCert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).GetUserDirectorCert(ctx, req.(*GetUserDirectorCertReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_BatchGetUserDirectorCert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserDirectorCertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).BatchGetUserDirectorCert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert.OfficialCert/BatchGetUserDirectorCert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).BatchGetUserDirectorCert(ctx, req.(*BatchGetUserDirectorCertReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_AddUserDirectorCert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserDirectorCertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).AddUserDirectorCert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert.OfficialCert/AddUserDirectorCert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).AddUserDirectorCert(ctx, req.(*AddUserDirectorCertReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_SetUserDirectorCert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserDirectorCertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).SetUserDirectorCert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert.OfficialCert/SetUserDirectorCert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).SetUserDirectorCert(ctx, req.(*SetUserDirectorCertReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialCert_ListUserDirectorCert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUserDirectorCertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialCertServer).ListUserDirectorCert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/officialcert.OfficialCert/ListUserDirectorCert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialCertServer).ListUserDirectorCert(ctx, req.(*ListUserDirectorCertReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _OfficialCert_serviceDesc = grpc.ServiceDesc{
	ServiceName: "officialcert.OfficialCert",
	HandlerType: (*OfficialCertServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserOfficialCert",
			Handler:    _OfficialCert_GetUserOfficialCert_Handler,
		},
		{
			MethodName: "ListUserOfficialCert",
			Handler:    _OfficialCert_ListUserOfficialCert_Handler,
		},
		{
			MethodName: "ListUserOfficialCertV2",
			Handler:    _OfficialCert_ListUserOfficialCertV2_Handler,
		},
		{
			MethodName: "SetUserOfficialCert",
			Handler:    _OfficialCert_SetUserOfficialCert_Handler,
		},
		{
			MethodName: "DelUserOfficialCert",
			Handler:    _OfficialCert_DelUserOfficialCert_Handler,
		},
		{
			MethodName: "BatchGetUserOfficialCert",
			Handler:    _OfficialCert_BatchGetUserOfficialCert_Handler,
		},
		{
			MethodName: "SetUserWearCertification",
			Handler:    _OfficialCert_SetUserWearCertification_Handler,
		},
		{
			MethodName: "GetUserAllOfficialCerts",
			Handler:    _OfficialCert_GetUserAllOfficialCerts_Handler,
		},
		{
			MethodName: "GetUserCertAttribute",
			Handler:    _OfficialCert_GetUserCertAttribute_Handler,
		},
		{
			MethodName: "CheckUserCertAttribute",
			Handler:    _OfficialCert_CheckUserCertAttribute_Handler,
		},
		{
			MethodName: "BatchDelOfficalCerts",
			Handler:    _OfficialCert_BatchDelOfficalCerts_Handler,
		},
		{
			MethodName: "GetUserDirectorCert",
			Handler:    _OfficialCert_GetUserDirectorCert_Handler,
		},
		{
			MethodName: "BatchGetUserDirectorCert",
			Handler:    _OfficialCert_BatchGetUserDirectorCert_Handler,
		},
		{
			MethodName: "AddUserDirectorCert",
			Handler:    _OfficialCert_AddUserDirectorCert_Handler,
		},
		{
			MethodName: "SetUserDirectorCert",
			Handler:    _OfficialCert_SetUserDirectorCert_Handler,
		},
		{
			MethodName: "ListUserDirectorCert",
			Handler:    _OfficialCert_ListUserDirectorCert_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "officialcert/officialcert.proto",
}

func init() {
	proto.RegisterFile("officialcert/officialcert.proto", fileDescriptor_officialcert_c8fd96492302b0f1)
}

var fileDescriptor_officialcert_c8fd96492302b0f1 = []byte{
	// 1234 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x58, 0xef, 0x6e, 0xe3, 0x44,
	0x10, 0x8f, 0x9d, 0x34, 0x4d, 0xa6, 0x2d, 0x84, 0xbd, 0x5e, 0xea, 0xa4, 0xd5, 0x51, 0x2c, 0x38,
	0xd2, 0x82, 0x52, 0x54, 0xc4, 0x09, 0x81, 0x50, 0xd5, 0x3f, 0x27, 0x54, 0xa9, 0x3a, 0x74, 0x4e,
	0xcb, 0x49, 0x7c, 0x89, 0x5c, 0x7b, 0x93, 0x5b, 0x35, 0xb1, 0x7d, 0xde, 0x0d, 0x52, 0x4f, 0xe2,
	0x71, 0xf8, 0xc2, 0x5b, 0xf0, 0x1c, 0x08, 0xf1, 0x2a, 0x68, 0xd7, 0x76, 0x62, 0x6f, 0x76, 0x53,
	0x57, 0xf0, 0xa9, 0x99, 0xd9, 0xd9, 0xf9, 0xb7, 0x33, 0xf3, 0x1b, 0x17, 0x3e, 0x0e, 0x47, 0x23,
	0xe2, 0x11, 0x77, 0xe2, 0xe1, 0x98, 0x1d, 0xe5, 0x89, 0x7e, 0x14, 0x87, 0x2c, 0x44, 0x9b, 0x79,
	0x9e, 0xfd, 0xb7, 0x09, 0xad, 0x9f, 0x52, 0xc6, 0x39, 0x8e, 0xd9, 0x65, 0x30, 0x0a, 0x51, 0x0b,
	0xaa, 0x33, 0xe2, 0x5b, 0xc6, 0xbe, 0xd1, 0xdb, 0x72, 0xf8, 0x4f, 0xb4, 0x0d, 0x6b, 0x8c, 0xb0,
	0x09, 0xb6, 0xcc, 0x7d, 0xa3, 0xd7, 0x74, 0x12, 0x82, 0x73, 0x49, 0xc0, 0xe2, 0xd0, 0xaa, 0x26,
	0x5c, 0x41, 0x70, 0x2e, 0x65, 0xf7, 0x13, 0x6c, 0xd5, 0x12, 0xae, 0x20, 0xd0, 0x07, 0x60, 0x12,
	0xdf, 0x5a, 0x13, 0x2a, 0x4d, 0xe2, 0xa3, 0x0e, 0x34, 0x6e, 0xf1, 0x98, 0x04, 0x43, 0x46, 0xad,
	0xfa, 0xbe, 0xd1, 0xab, 0x39, 0xeb, 0x82, 0xbe, 0xa6, 0xe8, 0x29, 0xd4, 0x71, 0xe0, 0xf3, 0x83,
	0x75, 0x71, 0xb0, 0x86, 0x03, 0x3f, 0x61, 0x13, 0x3a, 0x9c, 0x51, 0x6c, 0x35, 0xf6, 0x8d, 0x5e,
	0xc3, 0x59, 0x23, 0xf4, 0x86, 0x62, 0xb4, 0x07, 0x4d, 0x97, 0xb1, 0x98, 0xdc, 0xce, 0x18, 0xb6,
	0x9a, 0x42, 0xff, 0x82, 0x81, 0x4e, 0x60, 0x8f, 0xc7, 0x49, 0x46, 0xf7, 0x43, 0x1a, 0x61, 0x1e,
	0xe5, 0x10, 0x8f, 0x46, 0xd8, 0x63, 0xc3, 0x24, 0x1e, 0x10, 0x3e, 0x76, 0x52, 0x99, 0x41, 0x22,
	0xf2, 0x52, 0x48, 0x5c, 0x8b, 0x18, 0x7f, 0x80, 0x5d, 0x8d, 0x02, 0xe2, 0x85, 0x81, 0xb5, 0x21,
	0xee, 0x5b, 0xaa, 0xfb, 0x97, 0x5e, 0x18, 0xd8, 0x7f, 0x18, 0xb0, 0x7d, 0x43, 0x71, 0xbc, 0x94,
	0xe3, 0x4f, 0x60, 0x93, 0xd0, 0x61, 0x72, 0x8f, 0xe0, 0x24, 0xd9, 0x0d, 0x67, 0x83, 0xd0, 0xf3,
	0x8c, 0x85, 0x8e, 0xa1, 0xc6, 0xcf, 0x45, 0xce, 0x37, 0x8e, 0x9f, 0xf5, 0x0b, 0x8f, 0x29, 0x2b,
	0x74, 0x84, 0x2c, 0xfa, 0x1e, 0x9a, 0xfc, 0xef, 0x70, 0x42, 0x28, 0xb3, 0xaa, 0xfb, 0xd5, 0x12,
	0x17, 0x1b, 0x9c, 0x7d, 0x45, 0x28, 0xb3, 0x0f, 0xa1, 0xfd, 0x23, 0x66, 0xb2, 0xbb, 0x0e, 0x7e,
	0xb7, 0x5c, 0x11, 0xf6, 0x6b, 0xd8, 0x51, 0xca, 0xd2, 0x08, 0xbd, 0x80, 0x1a, 0x09, 0x46, 0x61,
	0xea, 0xb7, 0x5d, 0x34, 0xaf, 0x4a, 0x86, 0x23, 0xe4, 0xed, 0x0e, 0xec, 0x70, 0x37, 0x14, 0xf6,
	0xed, 0x37, 0x60, 0xa9, 0x8f, 0x68, 0x54, 0x0c, 0xd9, 0x78, 0x64, 0xc8, 0x57, 0xd0, 0x1e, 0xa8,
	0x43, 0xce, 0xb2, 0x6f, 0x94, 0xcf, 0x3e, 0x8f, 0x60, 0xa0, 0x4e, 0x8a, 0xfd, 0x1d, 0xb4, 0x2f,
	0xf0, 0xa4, 0x54, 0x6e, 0xd3, 0x5e, 0x31, 0xb3, 0x5e, 0xe1, 0x6a, 0x95, 0x77, 0x69, 0x64, 0x1f,
	0xc1, 0xc6, 0x05, 0x5e, 0xd5, 0xb9, 0xb2, 0xae, 0xd7, 0xb0, 0x73, 0xe6, 0x32, 0xef, 0xed, 0x05,
	0x9e, 0x08, 0x65, 0x89, 0x2e, 0xca, 0x1d, 0x79, 0x01, 0x4d, 0xfe, 0x0e, 0xf9, 0x44, 0x76, 0x8a,
	0x61, 0xe7, 0x4c, 0x39, 0x0d, 0x2e, 0x2b, 0x72, 0xd8, 0x05, 0x4b, 0xad, 0x92, 0x46, 0xf6, 0xb7,
	0xb0, 0x2b, 0xce, 0x34, 0x75, 0xd5, 0x81, 0xc6, 0x8c, 0xf8, 0x0b, 0x8b, 0x5b, 0xce, 0xfa, 0x8c,
	0xf8, 0x42, 0xeb, 0x10, 0xf6, 0xf4, 0x37, 0x69, 0x84, 0x4e, 0x96, 0xbd, 0x2d, 0x53, 0x6a, 0x0b,
	0xb7, 0x4f, 0x60, 0x37, 0x7d, 0xac, 0x37, 0xd8, 0x8d, 0xd3, 0xb6, 0xf3, 0x5c, 0x46, 0xc2, 0xa0,
	0xdc, 0xb3, 0x3c, 0x83, 0x3d, 0xbd, 0x02, 0x1a, 0xd9, 0x7d, 0xe8, 0xa6, 0xce, 0x9f, 0x4e, 0x26,
	0x79, 0x47, 0xa8, 0xba, 0xa5, 0x7e, 0x83, 0x5d, 0xad, 0x3c, 0x8d, 0xca, 0x4c, 0x8c, 0x42, 0x2b,
	0x98, 0x8f, 0x6c, 0x85, 0x2f, 0xe6, 0x1d, 0xcd, 0x0f, 0x4f, 0xb3, 0x11, 0xaa, 0xf6, 0xb5, 0x0f,
	0x96, 0x5a, 0x98, 0x46, 0x08, 0x41, 0x8d, 0x0f, 0xe0, 0x54, 0x5c, 0xfc, 0xb6, 0x4f, 0xa1, 0x73,
	0xfe, 0x16, 0x7b, 0x77, 0xe5, 0xd4, 0xcf, 0x55, 0x98, 0x39, 0x15, 0x5f, 0x42, 0x57, 0xa7, 0x82,
	0x46, 0xfc, 0x71, 0xc2, 0xbb, 0x34, 0x27, 0x66, 0x78, 0x67, 0xff, 0x63, 0x40, 0xeb, 0x82, 0xc4,
	0xd8, 0x63, 0x61, 0xbc, 0xa2, 0x3d, 0x10, 0xd4, 0x18, 0x4b, 0x5f, 0xb5, 0xe9, 0x88, 0xdf, 0xa8,
	0x0b, 0x8d, 0x80, 0x78, 0x77, 0x81, 0x3b, 0xc5, 0x29, 0xb2, 0xcd, 0x69, 0x74, 0x00, 0x2d, 0x2f,
	0x0c, 0x23, 0x1c, 0x8b, 0x67, 0x1e, 0xb2, 0xfb, 0x28, 0xc1, 0xb9, 0x2d, 0xe7, 0xc3, 0x1c, 0xff,
	0xfa, 0x3e, 0xc2, 0xa8, 0x0d, 0x75, 0xca, 0x5c, 0x36, 0xa3, 0x29, 0xea, 0xa5, 0x14, 0x07, 0x2c,
	0x01, 0x94, 0xfe, 0xcc, 0xc3, 0x02, 0xfa, 0x9a, 0xce, 0x82, 0x81, 0x2c, 0x58, 0x9f, 0xba, 0x81,
	0x3b, 0xc6, 0xb1, 0x40, 0xbf, 0xa6, 0x93, 0x91, 0x3c, 0x42, 0x46, 0x05, 0xf6, 0xd5, 0x1c, 0x93,
	0x51, 0x7b, 0x9a, 0x20, 0xcb, 0x52, 0x90, 0xff, 0x15, 0x59, 0x64, 0x85, 0xe9, 0x6c, 0x5b, 0x80,
	0x43, 0x5e, 0xe0, 0x21, 0x70, 0x28, 0xca, 0xe6, 0xc0, 0xc1, 0xd0, 0x81, 0xc3, 0xb2, 0x79, 0x01,
	0x0e, 0xd2, 0x20, 0x91, 0x7d, 0x28, 0x3f, 0x48, 0x96, 0x3c, 0x2a, 0x37, 0x48, 0x96, 0xdc, 0x5a,
	0x0c, 0x92, 0xdf, 0x8d, 0x39, 0x88, 0x3c, 0x98, 0x9a, 0x5c, 0x55, 0x98, 0x85, 0xaa, 0x50, 0x15,
	0x56, 0x55, 0x5d, 0x58, 0x85, 0x02, 0xaa, 0xad, 0x28, 0xa0, 0xb5, 0x42, 0x01, 0xe5, 0xd0, 0x49,
	0xce, 0x81, 0x7d, 0x03, 0xed, 0x53, 0xdf, 0x2f, 0x17, 0x81, 0xca, 0x53, 0x53, 0xe9, 0x29, 0xb7,
	0xa8, 0x54, 0x4b, 0x23, 0xfb, 0x2f, 0x63, 0x81, 0xf6, 0xb2, 0xcd, 0x36, 0xd4, 0xc3, 0xd1, 0x88,
	0x62, 0x96, 0x9a, 0x4d, 0x29, 0xb4, 0x0b, 0xcd, 0xc8, 0x1d, 0xe3, 0x21, 0x25, 0xef, 0x33, 0x93,
	0x0d, 0xce, 0x18, 0x90, 0xf7, 0x78, 0xde, 0xc9, 0x55, 0x4d, 0x27, 0xd7, 0xa4, 0x4e, 0xd6, 0xe6,
	0x49, 0x19, 0x60, 0xfd, 0xa1, 0x1e, 0x5f, 0xcf, 0xbf, 0xa6, 0xfd, 0x6e, 0xb1, 0xaf, 0x2c, 0xd5,
	0x1b, 0xdf, 0xa5, 0x43, 0xe6, 0x4e, 0xd2, 0xe0, 0x12, 0xa2, 0x58, 0x85, 0xe6, 0xe3, 0xab, 0xf0,
	0xf0, 0x0c, 0xb6, 0x5e, 0x66, 0x63, 0x51, 0xf8, 0xf6, 0x14, 0x3e, 0x2a, 0x30, 0x5e, 0x85, 0x01,
	0x6e, 0x55, 0x50, 0x17, 0xda, 0x05, 0xf6, 0xe5, 0xf4, 0x55, 0x78, 0x45, 0xa6, 0x84, 0xb5, 0x8c,
	0xe3, 0x3f, 0xb7, 0x60, 0x33, 0x8f, 0x10, 0xc8, 0x87, 0x27, 0x0a, 0xfc, 0x45, 0x9f, 0x16, 0x3d,
	0x53, 0x83, 0x7b, 0xf7, 0xb3, 0x12, 0x52, 0x34, 0xb2, 0x2b, 0x68, 0x0c, 0xdb, 0xaa, 0xed, 0x0e,
	0x49, 0x0a, 0x34, 0xcb, 0x61, 0xf7, 0x79, 0x19, 0x31, 0x61, 0x88, 0x40, 0x5b, 0x75, 0xfa, 0xf3,
	0xf1, 0xff, 0x6f, 0xca, 0x87, 0x27, 0x83, 0x87, 0x33, 0x37, 0x28, 0x95, 0xb9, 0x01, 0x5e, 0x61,
	0x45, 0xb1, 0x19, 0xca, 0x56, 0xd4, 0x8b, 0xa7, 0x6c, 0x45, 0xb7, 0x62, 0x56, 0x10, 0x4d, 0x17,
	0x3c, 0x55, 0x29, 0x1c, 0x14, 0x95, 0xac, 0x58, 0xf6, 0xba, 0x87, 0x65, 0x45, 0x33, 0xa3, 0xba,
	0xed, 0x4a, 0x36, 0xba, 0x62, 0x8d, 0x93, 0x8d, 0xae, 0x5c, 0xd8, 0x2a, 0x28, 0x9a, 0x03, 0x97,
	0xbc, 0x82, 0xa1, 0x9e, 0xb2, 0x9a, 0x15, 0x9b, 0x5d, 0xf7, 0xa0, 0xa4, 0x64, 0x56, 0xfb, 0xaa,
	0x45, 0x0a, 0xa9, 0x9b, 0x47, 0x5e, 0x9d, 0xe4, 0x82, 0xd4, 0xed, 0x64, 0x76, 0x05, 0x4d, 0xa1,
	0xad, 0x5e, 0x9f, 0xd0, 0xe7, 0x45, 0x1d, 0xda, 0x3d, 0xad, 0xdb, 0x2b, 0x27, 0x98, 0xc5, 0xa5,
	0xfa, 0x28, 0x90, 0xe3, 0xd2, 0x7c, 0x8b, 0xc8, 0x71, 0x69, 0xbf, 0x2f, 0x2a, 0xb9, 0x11, 0x95,
	0x1f, 0x8e, 0x9a, 0x11, 0x25, 0x21, 0x8d, 0x66, 0x44, 0x2d, 0x81, 0xd5, 0x52, 0x0b, 0x14, 0x4c,
	0xad, 0x68, 0x01, 0xd9, 0xde, 0x61, 0x59, 0xd1, 0x2c, 0x34, 0x05, 0x7c, 0xca, 0xa1, 0xa9, 0x81,
	0x5b, 0x0e, 0x4d, 0x87, 0xc3, 0xf9, 0x49, 0xb5, 0xca, 0xca, 0xa0, 0x54, 0x02, 0x07, 0xda, 0x58,
	0x72, 0x33, 0xbe, 0x60, 0x46, 0x33, 0x78, 0x65, 0x3b, 0xcf, 0xcb, 0x88, 0x71, 0x43, 0x67, 0x5f,
	0xfd, 0xd2, 0x1f, 0x87, 0x13, 0x37, 0x18, 0xf7, 0xbf, 0x39, 0x66, 0xac, 0xef, 0x85, 0xd3, 0x23,
	0xf1, 0x8f, 0x2f, 0x2f, 0x9c, 0x1c, 0x51, 0x1c, 0xff, 0x4a, 0x3c, 0x4c, 0x0b, 0xff, 0x17, 0xbb,
	0xad, 0x8b, 0xf3, 0xaf, 0xff, 0x0d, 0x00, 0x00, 0xff, 0xff, 0xc1, 0xaa, 0xe9, 0xc3, 0x3b, 0x13,
	0x00, 0x00,
}
