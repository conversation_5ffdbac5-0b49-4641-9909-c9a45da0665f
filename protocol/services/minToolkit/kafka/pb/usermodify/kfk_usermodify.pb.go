// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt-protocol/service/appsvr/src/minToolkit/kafka/pb/kafka_usermodify/kfk_usermodify.proto

package usermodify

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

//数据平台的pb
type UserGenderModify struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Gender               uint32   `protobuf:"varint,2,opt,name=gender,proto3" json:"gender,omitempty"`
	GenderAudio          uint32   `protobuf:"varint,3,opt,name=gender_audio,json=genderAudio,proto3" json:"gender_audio,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,4,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserGenderModify) Reset()         { *m = UserGenderModify{} }
func (m *UserGenderModify) String() string { return proto.CompactTextString(m) }
func (*UserGenderModify) ProtoMessage()    {}
func (*UserGenderModify) Descriptor() ([]byte, []int) {
	return fileDescriptor_ea2ef47e2554aecd, []int{0}
}

func (m *UserGenderModify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserGenderModify.Unmarshal(m, b)
}
func (m *UserGenderModify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserGenderModify.Marshal(b, m, deterministic)
}
func (m *UserGenderModify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserGenderModify.Merge(m, src)
}
func (m *UserGenderModify) XXX_Size() int {
	return xxx_messageInfo_UserGenderModify.Size(m)
}
func (m *UserGenderModify) XXX_DiscardUnknown() {
	xxx_messageInfo_UserGenderModify.DiscardUnknown(m)
}

var xxx_messageInfo_UserGenderModify proto.InternalMessageInfo

func (m *UserGenderModify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserGenderModify) GetGender() uint32 {
	if m != nil {
		return m.Gender
	}
	return 0
}

func (m *UserGenderModify) GetGenderAudio() uint32 {
	if m != nil {
		return m.GenderAudio
	}
	return 0
}

func (m *UserGenderModify) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func init() {
	proto.RegisterType((*UserGenderModify)(nil), "usermodify.UserGenderModify")
}

func init() {
	proto.RegisterFile("tt-protocol/service/appsvr/src/minToolkit/kafka/pb/kafka_usermodify/kfk_usermodify.proto", fileDescriptor_ea2ef47e2554aecd)
}

var fileDescriptor_ea2ef47e2554aecd = []byte{
	// 198 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x4c, 0xce, 0xbd, 0x6a, 0xc4, 0x30,
	0x10, 0x04, 0x60, 0x1c, 0x07, 0x17, 0xeb, 0x04, 0x8c, 0x8a, 0xa0, 0x2e, 0x3f, 0x55, 0x9a, 0x44,
	0x45, 0x9e, 0x20, 0x55, 0xaa, 0x34, 0xc1, 0x81, 0x74, 0x46, 0xb6, 0xd6, 0x61, 0x91, 0x65, 0x09,
	0xfd, 0x18, 0xae, 0xbb, 0x47, 0x3f, 0x2c, 0x1d, 0xdc, 0x75, 0x33, 0x9f, 0x06, 0xb1, 0xf0, 0x17,
	0xe3, 0x9b, 0xf3, 0x36, 0xda, 0xc9, 0x2e, 0x22, 0xa0, 0xdf, 0x68, 0x42, 0x21, 0x9d, 0x0b, 0x9b,
	0x17, 0xc1, 0x4f, 0xc2, 0xd0, 0xda, 0x5b, 0xbb, 0x68, 0x8a, 0x42, 0xcb, 0x59, 0x4b, 0xe1, 0xc6,
	0x12, 0x86, 0x14, 0xd0, 0x1b, 0xab, 0x68, 0x3e, 0x08, 0x3d, 0xeb, 0xab, 0xfa, 0x9e, 0xff, 0x63,
	0x70, 0x91, 0x97, 0x63, 0x05, 0xdd, 0x6f, 0x40, 0xff, 0x85, 0xab, 0x42, 0xff, 0x9d, 0x91, 0x75,
	0x50, 0x27, 0x52, 0xbc, 0x7a, 0xaa, 0x5e, 0xef, 0x7f, 0xf6, 0xc8, 0x1e, 0xa0, 0xf9, 0xcf, 0x0b,
	0x7e, 0x93, 0xf1, 0xdc, 0xd8, 0x33, 0xdc, 0x95, 0x34, 0xc8, 0xa4, 0xc8, 0xf2, 0x3a, 0xbf, 0xb6,
	0xc5, 0x3e, 0x77, 0x62, 0x8f, 0xd0, 0x26, 0xa7, 0x64, 0xc4, 0x21, 0x92, 0x41, 0x7e, 0x9b, 0x17,
	0x50, 0xa8, 0x27, 0x83, 0x63, 0x93, 0xaf, 0xfa, 0x38, 0x05, 0x00, 0x00, 0xff, 0xff, 0xea, 0x08,
	0x27, 0xd7, 0xf1, 0x00, 0x00, 0x00,
}
