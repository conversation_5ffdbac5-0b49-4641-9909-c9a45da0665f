// Code generated by protoc-gen-go. DO NOT EDIT.
// source: kafka_gamestatus.proto

package kafka_gameserver_api

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type GameStatusType int32

const (
	GameStatusType_InitStatus           GameStatusType = 0
	GameStatusType_StartGameStatus      GameStatusType = 1
	GameStatusType_EndGameStatus        GameStatusType = 2
	GameStatusType_LoginCntChangeStatus GameStatusType = 3
	GameStatusType_JoinCntChangeStatus  GameStatusType = 4
	GameStatusType_ReadyCntChangeStatus GameStatusType = 5
)

var GameStatusType_name = map[int32]string{
	0: "InitStatus",
	1: "StartGameStatus",
	2: "EndGameStatus",
	3: "LoginCntChangeStatus",
	4: "JoinCntChangeStatus",
	5: "ReadyCntChangeStatus",
}

var GameStatusType_value = map[string]int32{
	"InitStatus":           0,
	"StartGameStatus":      1,
	"EndGameStatus":        2,
	"LoginCntChangeStatus": 3,
	"JoinCntChangeStatus":  4,
	"ReadyCntChangeStatus": 5,
}

func (x GameStatusType) String() string {
	return proto.EnumName(GameStatusType_name, int32(x))
}

func (GameStatusType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_b1c955974ad40fae, []int{0}
}

//用于数据打包传kfk
type GameStatusNotifyType int32

const (
	GameStatusNotifyType_GameStatusNotify      GameStatusNotifyType = 0
	GameStatusNotifyType_GameSetStatusNotify   GameStatusNotifyType = 1
	GameStatusNotifyType_RoleChangeNotify      GameStatusNotifyType = 2
	GameStatusNotifyType_GameStatusV2Notify    GameStatusNotifyType = 3
	GameStatusNotifyType_GameSetStatusV2Notify GameStatusNotifyType = 4
)

var GameStatusNotifyType_name = map[int32]string{
	0: "GameStatusNotify",
	1: "GameSetStatusNotify",
	2: "RoleChangeNotify",
	3: "GameStatusV2Notify",
	4: "GameSetStatusV2Notify",
}

var GameStatusNotifyType_value = map[string]int32{
	"GameStatusNotify":      0,
	"GameSetStatusNotify":   1,
	"RoleChangeNotify":      2,
	"GameStatusV2Notify":    3,
	"GameSetStatusV2Notify": 4,
}

func (x GameStatusNotifyType) String() string {
	return proto.EnumName(GameStatusNotifyType_name, int32(x))
}

func (GameStatusNotifyType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_b1c955974ad40fae, []int{1}
}

type GameSetStatusType int32

const (
	GameSetStatusType_InitGameSetStatus  GameSetStatusType = 0
	GameSetStatusType_StartGameSetStatus GameSetStatusType = 1
	GameSetStatusType_EndGameSetStatus   GameSetStatusType = 2
)

var GameSetStatusType_name = map[int32]string{
	0: "InitGameSetStatus",
	1: "StartGameSetStatus",
	2: "EndGameSetStatus",
}

var GameSetStatusType_value = map[string]int32{
	"InitGameSetStatus":  0,
	"StartGameSetStatus": 1,
	"EndGameSetStatus":   2,
}

func (x GameSetStatusType) String() string {
	return proto.EnumName(GameSetStatusType_name, int32(x))
}

func (GameSetStatusType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_b1c955974ad40fae, []int{2}
}

type GameResultType int32

const (
	GameResultType_GameWin          GameResultType = 0
	GameResultType_GameTie          GameResultType = 1
	GameResultType_GameRank         GameResultType = 2
	GameResultType_GameWinWithScore GameResultType = 3
	GameResultType_GameTieWithScore GameResultType = 4
	GameResultType_GameOver         GameResultType = 5
)

var GameResultType_name = map[int32]string{
	0: "GameWin",
	1: "GameTie",
	2: "GameRank",
	3: "GameWinWithScore",
	4: "GameTieWithScore",
	5: "GameOver",
}

var GameResultType_value = map[string]int32{
	"GameWin":          0,
	"GameTie":          1,
	"GameRank":         2,
	"GameWinWithScore": 3,
	"GameTieWithScore": 4,
	"GameOver":         5,
}

func (x GameResultType) String() string {
	return proto.EnumName(GameResultType_name, int32(x))
}

func (GameResultType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_b1c955974ad40fae, []int{3}
}

type UserLeaveType int32

const (
	UserLeaveType_UserSimpleLeave UserLeaveType = 0
	UserLeaveType_UserExpireLeave UserLeaveType = 1
)

var UserLeaveType_name = map[int32]string{
	0: "UserSimpleLeave",
	1: "UserExpireLeave",
}

var UserLeaveType_value = map[string]int32{
	"UserSimpleLeave": 0,
	"UserExpireLeave": 1,
}

func (x UserLeaveType) String() string {
	return proto.EnumName(UserLeaveType_name, int32(x))
}

func (UserLeaveType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_b1c955974ad40fae, []int{4}
}

type GameStatusNotifyInfo struct {
	StatusType           GameStatusNotifyType `protobuf:"varint,1,opt,name=statusType,proto3,enum=kafka_gameserver_api.GameStatusNotifyType" json:"statusType"`
	GameStatusByte       []byte               `protobuf:"bytes,2,opt,name=gameStatusByte,proto3" json:"gameStatusByte"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GameStatusNotifyInfo) Reset()         { *m = GameStatusNotifyInfo{} }
func (m *GameStatusNotifyInfo) String() string { return proto.CompactTextString(m) }
func (*GameStatusNotifyInfo) ProtoMessage()    {}
func (*GameStatusNotifyInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_b1c955974ad40fae, []int{0}
}

func (m *GameStatusNotifyInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameStatusNotifyInfo.Unmarshal(m, b)
}
func (m *GameStatusNotifyInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameStatusNotifyInfo.Marshal(b, m, deterministic)
}
func (m *GameStatusNotifyInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameStatusNotifyInfo.Merge(m, src)
}
func (m *GameStatusNotifyInfo) XXX_Size() int {
	return xxx_messageInfo_GameStatusNotifyInfo.Size(m)
}
func (m *GameStatusNotifyInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GameStatusNotifyInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GameStatusNotifyInfo proto.InternalMessageInfo

func (m *GameStatusNotifyInfo) GetStatusType() GameStatusNotifyType {
	if m != nil {
		return m.StatusType
	}
	return GameStatusNotifyType_GameStatusNotify
}

func (m *GameStatusNotifyInfo) GetGameStatusByte() []byte {
	if m != nil {
		return m.GameStatusByte
	}
	return nil
}

//游戏玩法相关
type GameStatus struct {
	Gameid               uint32         `protobuf:"varint,1,opt,name=gameid,proto3" json:"gameid"`
	Roomid               uint32         `protobuf:"varint,2,opt,name=roomid,proto3" json:"roomid"`
	Status               GameStatusType `protobuf:"varint,3,opt,name=status,proto3,enum=kafka_gameserver_api.GameStatusType" json:"status"`
	LoginCnt             uint32         `protobuf:"varint,4,opt,name=login_cnt,json=loginCnt,proto3" json:"login_cnt"`
	JoinCnt              uint32         `protobuf:"varint,5,opt,name=join_cnt,json=joinCnt,proto3" json:"join_cnt"`
	ReadyCnt             uint32         `protobuf:"varint,6,opt,name=ready_cnt,json=readyCnt,proto3" json:"ready_cnt"`
	JoinUids             []uint32       `protobuf:"varint,7,rep,packed,name=join_uids,json=joinUids,proto3" json:"join_uids"`
	ReadyUids            []uint32       `protobuf:"varint,8,rep,packed,name=ready_uids,json=readyUids,proto3" json:"ready_uids"`
	InitialUid           uint32         `protobuf:"varint,9,opt,name=initial_uid,json=initialUid,proto3" json:"initial_uid"`
	CurrentVerion        string         `protobuf:"bytes,10,opt,name=current_verion,json=currentVerion,proto3" json:"current_verion"`
	GamePkmodel          string         `protobuf:"bytes,11,opt,name=game_pkmodel,json=gamePkmodel,proto3" json:"game_pkmodel"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GameStatus) Reset()         { *m = GameStatus{} }
func (m *GameStatus) String() string { return proto.CompactTextString(m) }
func (*GameStatus) ProtoMessage()    {}
func (*GameStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_b1c955974ad40fae, []int{1}
}

func (m *GameStatus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameStatus.Unmarshal(m, b)
}
func (m *GameStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameStatus.Marshal(b, m, deterministic)
}
func (m *GameStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameStatus.Merge(m, src)
}
func (m *GameStatus) XXX_Size() int {
	return xxx_messageInfo_GameStatus.Size(m)
}
func (m *GameStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_GameStatus.DiscardUnknown(m)
}

var xxx_messageInfo_GameStatus proto.InternalMessageInfo

func (m *GameStatus) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *GameStatus) GetRoomid() uint32 {
	if m != nil {
		return m.Roomid
	}
	return 0
}

func (m *GameStatus) GetStatus() GameStatusType {
	if m != nil {
		return m.Status
	}
	return GameStatusType_InitStatus
}

func (m *GameStatus) GetLoginCnt() uint32 {
	if m != nil {
		return m.LoginCnt
	}
	return 0
}

func (m *GameStatus) GetJoinCnt() uint32 {
	if m != nil {
		return m.JoinCnt
	}
	return 0
}

func (m *GameStatus) GetReadyCnt() uint32 {
	if m != nil {
		return m.ReadyCnt
	}
	return 0
}

func (m *GameStatus) GetJoinUids() []uint32 {
	if m != nil {
		return m.JoinUids
	}
	return nil
}

func (m *GameStatus) GetReadyUids() []uint32 {
	if m != nil {
		return m.ReadyUids
	}
	return nil
}

func (m *GameStatus) GetInitialUid() uint32 {
	if m != nil {
		return m.InitialUid
	}
	return 0
}

func (m *GameStatus) GetCurrentVerion() string {
	if m != nil {
		return m.CurrentVerion
	}
	return ""
}

func (m *GameStatus) GetGamePkmodel() string {
	if m != nil {
		return m.GamePkmodel
	}
	return ""
}

type GameStatusV2 struct {
	Gameid               uint32   `protobuf:"varint,1,opt,name=gameid,proto3" json:"gameid"`
	Roomid               uint32   `protobuf:"varint,2,opt,name=roomid,proto3" json:"roomid"`
	LoginCnt             uint32   `protobuf:"varint,3,opt,name=login_cnt,json=loginCnt,proto3" json:"login_cnt"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameStatusV2) Reset()         { *m = GameStatusV2{} }
func (m *GameStatusV2) String() string { return proto.CompactTextString(m) }
func (*GameStatusV2) ProtoMessage()    {}
func (*GameStatusV2) Descriptor() ([]byte, []int) {
	return fileDescriptor_b1c955974ad40fae, []int{2}
}

func (m *GameStatusV2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameStatusV2.Unmarshal(m, b)
}
func (m *GameStatusV2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameStatusV2.Marshal(b, m, deterministic)
}
func (m *GameStatusV2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameStatusV2.Merge(m, src)
}
func (m *GameStatusV2) XXX_Size() int {
	return xxx_messageInfo_GameStatusV2.Size(m)
}
func (m *GameStatusV2) XXX_DiscardUnknown() {
	xxx_messageInfo_GameStatusV2.DiscardUnknown(m)
}

var xxx_messageInfo_GameStatusV2 proto.InternalMessageInfo

func (m *GameStatusV2) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *GameStatusV2) GetRoomid() uint32 {
	if m != nil {
		return m.Roomid
	}
	return 0
}

func (m *GameStatusV2) GetLoginCnt() uint32 {
	if m != nil {
		return m.LoginCnt
	}
	return 0
}

type ScoreInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid"`
	Score                uint32   `protobuf:"varint,2,opt,name=score,proto3" json:"score"`
	Iswin                bool     `protobuf:"varint,3,opt,name=iswin,proto3" json:"iswin"`
	TeamId               uint32   `protobuf:"varint,4,opt,name=team_id,json=teamId,proto3" json:"team_id"`
	TeamDesc             string   `protobuf:"bytes,5,opt,name=team_desc,json=teamDesc,proto3" json:"team_desc"`
	UserDesc             string   `protobuf:"bytes,6,opt,name=user_desc,json=userDesc,proto3" json:"user_desc"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ScoreInfo) Reset()         { *m = ScoreInfo{} }
func (m *ScoreInfo) String() string { return proto.CompactTextString(m) }
func (*ScoreInfo) ProtoMessage()    {}
func (*ScoreInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_b1c955974ad40fae, []int{3}
}

func (m *ScoreInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScoreInfo.Unmarshal(m, b)
}
func (m *ScoreInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScoreInfo.Marshal(b, m, deterministic)
}
func (m *ScoreInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScoreInfo.Merge(m, src)
}
func (m *ScoreInfo) XXX_Size() int {
	return xxx_messageInfo_ScoreInfo.Size(m)
}
func (m *ScoreInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ScoreInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ScoreInfo proto.InternalMessageInfo

func (m *ScoreInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ScoreInfo) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *ScoreInfo) GetIswin() bool {
	if m != nil {
		return m.Iswin
	}
	return false
}

func (m *ScoreInfo) GetTeamId() uint32 {
	if m != nil {
		return m.TeamId
	}
	return 0
}

func (m *ScoreInfo) GetTeamDesc() string {
	if m != nil {
		return m.TeamDesc
	}
	return ""
}

func (m *ScoreInfo) GetUserDesc() string {
	if m != nil {
		return m.UserDesc
	}
	return ""
}

type ScoreInfoV2 struct {
	Openid               string   `protobuf:"bytes,1,opt,name=openid,proto3" json:"openid"`
	Score                uint32   `protobuf:"varint,2,opt,name=score,proto3" json:"score"`
	Iswin                bool     `protobuf:"varint,3,opt,name=iswin,proto3" json:"iswin"`
	TeamId               uint32   `protobuf:"varint,4,opt,name=team_id,json=teamId,proto3" json:"team_id"`
	TeamDesc             string   `protobuf:"bytes,5,opt,name=team_desc,json=teamDesc,proto3" json:"team_desc"`
	UserDesc             string   `protobuf:"bytes,6,opt,name=user_desc,json=userDesc,proto3" json:"user_desc"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ScoreInfoV2) Reset()         { *m = ScoreInfoV2{} }
func (m *ScoreInfoV2) String() string { return proto.CompactTextString(m) }
func (*ScoreInfoV2) ProtoMessage()    {}
func (*ScoreInfoV2) Descriptor() ([]byte, []int) {
	return fileDescriptor_b1c955974ad40fae, []int{4}
}

func (m *ScoreInfoV2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScoreInfoV2.Unmarshal(m, b)
}
func (m *ScoreInfoV2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScoreInfoV2.Marshal(b, m, deterministic)
}
func (m *ScoreInfoV2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScoreInfoV2.Merge(m, src)
}
func (m *ScoreInfoV2) XXX_Size() int {
	return xxx_messageInfo_ScoreInfoV2.Size(m)
}
func (m *ScoreInfoV2) XXX_DiscardUnknown() {
	xxx_messageInfo_ScoreInfoV2.DiscardUnknown(m)
}

var xxx_messageInfo_ScoreInfoV2 proto.InternalMessageInfo

func (m *ScoreInfoV2) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *ScoreInfoV2) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *ScoreInfoV2) GetIswin() bool {
	if m != nil {
		return m.Iswin
	}
	return false
}

func (m *ScoreInfoV2) GetTeamId() uint32 {
	if m != nil {
		return m.TeamId
	}
	return 0
}

func (m *ScoreInfoV2) GetTeamDesc() string {
	if m != nil {
		return m.TeamDesc
	}
	return ""
}

func (m *ScoreInfoV2) GetUserDesc() string {
	if m != nil {
		return m.UserDesc
	}
	return ""
}

type GetUidByOpenidInfo struct {
	Openid               string   `protobuf:"bytes,1,opt,name=openid,proto3" json:"openid"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUidByOpenidInfo) Reset()         { *m = GetUidByOpenidInfo{} }
func (m *GetUidByOpenidInfo) String() string { return proto.CompactTextString(m) }
func (*GetUidByOpenidInfo) ProtoMessage()    {}
func (*GetUidByOpenidInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_b1c955974ad40fae, []int{5}
}

func (m *GetUidByOpenidInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUidByOpenidInfo.Unmarshal(m, b)
}
func (m *GetUidByOpenidInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUidByOpenidInfo.Marshal(b, m, deterministic)
}
func (m *GetUidByOpenidInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUidByOpenidInfo.Merge(m, src)
}
func (m *GetUidByOpenidInfo) XXX_Size() int {
	return xxx_messageInfo_GetUidByOpenidInfo.Size(m)
}
func (m *GetUidByOpenidInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUidByOpenidInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GetUidByOpenidInfo proto.InternalMessageInfo

func (m *GetUidByOpenidInfo) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *GetUidByOpenidInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

//游戏局相关
type GameSetStatus struct {
	Gameid               uint32                `protobuf:"varint,1,opt,name=gameid,proto3" json:"gameid"`
	Roomid               uint32                `protobuf:"varint,2,opt,name=roomid,proto3" json:"roomid"`
	Setid                string                `protobuf:"bytes,3,opt,name=setid,proto3" json:"setid"`
	Cpid                 uint32                `protobuf:"varint,4,opt,name=cpid,proto3" json:"cpid"`
	StatusType           GameSetStatusType     `protobuf:"varint,5,opt,name=statusType,proto3,enum=kafka_gameserver_api.GameSetStatusType" json:"statusType"`
	Scoreinfo            []*ScoreInfo          `protobuf:"bytes,6,rep,name=scoreinfo,proto3" json:"scoreinfo"`
	ResultType           GameResultType        `protobuf:"varint,7,opt,name=resultType,proto3,enum=kafka_gameserver_api.GameResultType" json:"resultType"`
	Unit                 string                `protobuf:"bytes,8,opt,name=unit,proto3" json:"unit"`
	GameSetTime          uint32                `protobuf:"varint,9,opt,name=gameSetTime,proto3" json:"gameSetTime"`
	Infos                []*GetUidByOpenidInfo `protobuf:"bytes,10,rep,name=infos,proto3" json:"infos"`
	ResultDesc           string                `protobuf:"bytes,11,opt,name=result_desc,json=resultDesc,proto3" json:"result_desc"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GameSetStatus) Reset()         { *m = GameSetStatus{} }
func (m *GameSetStatus) String() string { return proto.CompactTextString(m) }
func (*GameSetStatus) ProtoMessage()    {}
func (*GameSetStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_b1c955974ad40fae, []int{6}
}

func (m *GameSetStatus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameSetStatus.Unmarshal(m, b)
}
func (m *GameSetStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameSetStatus.Marshal(b, m, deterministic)
}
func (m *GameSetStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameSetStatus.Merge(m, src)
}
func (m *GameSetStatus) XXX_Size() int {
	return xxx_messageInfo_GameSetStatus.Size(m)
}
func (m *GameSetStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_GameSetStatus.DiscardUnknown(m)
}

var xxx_messageInfo_GameSetStatus proto.InternalMessageInfo

func (m *GameSetStatus) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *GameSetStatus) GetRoomid() uint32 {
	if m != nil {
		return m.Roomid
	}
	return 0
}

func (m *GameSetStatus) GetSetid() string {
	if m != nil {
		return m.Setid
	}
	return ""
}

func (m *GameSetStatus) GetCpid() uint32 {
	if m != nil {
		return m.Cpid
	}
	return 0
}

func (m *GameSetStatus) GetStatusType() GameSetStatusType {
	if m != nil {
		return m.StatusType
	}
	return GameSetStatusType_InitGameSetStatus
}

func (m *GameSetStatus) GetScoreinfo() []*ScoreInfo {
	if m != nil {
		return m.Scoreinfo
	}
	return nil
}

func (m *GameSetStatus) GetResultType() GameResultType {
	if m != nil {
		return m.ResultType
	}
	return GameResultType_GameWin
}

func (m *GameSetStatus) GetUnit() string {
	if m != nil {
		return m.Unit
	}
	return ""
}

func (m *GameSetStatus) GetGameSetTime() uint32 {
	if m != nil {
		return m.GameSetTime
	}
	return 0
}

func (m *GameSetStatus) GetInfos() []*GetUidByOpenidInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

func (m *GameSetStatus) GetResultDesc() string {
	if m != nil {
		return m.ResultDesc
	}
	return ""
}

//游戏局相关
type GameSetStatusV2 struct {
	Gameid               uint32                `protobuf:"varint,1,opt,name=gameid,proto3" json:"gameid"`
	Roomid               uint32                `protobuf:"varint,2,opt,name=roomid,proto3" json:"roomid"`
	Setid                string                `protobuf:"bytes,3,opt,name=setid,proto3" json:"setid"`
	Cpid                 uint32                `protobuf:"varint,4,opt,name=cpid,proto3" json:"cpid"`
	StatusType           GameSetStatusType     `protobuf:"varint,5,opt,name=statusType,proto3,enum=kafka_gameserver_api.GameSetStatusType" json:"statusType"`
	Scoreinfo            []*ScoreInfoV2        `protobuf:"bytes,6,rep,name=scoreinfo,proto3" json:"scoreinfo"`
	ResultType           GameResultType        `protobuf:"varint,7,opt,name=resultType,proto3,enum=kafka_gameserver_api.GameResultType" json:"resultType"`
	Unit                 string                `protobuf:"bytes,8,opt,name=unit,proto3" json:"unit"`
	GameSetTime          uint32                `protobuf:"varint,9,opt,name=gameSetTime,proto3" json:"gameSetTime"`
	ResultDesc           string                `protobuf:"bytes,10,opt,name=result_desc,json=resultDesc,proto3" json:"result_desc"`
	Infos                []*GetUidByOpenidInfo `protobuf:"bytes,11,rep,name=infos,proto3" json:"infos"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GameSetStatusV2) Reset()         { *m = GameSetStatusV2{} }
func (m *GameSetStatusV2) String() string { return proto.CompactTextString(m) }
func (*GameSetStatusV2) ProtoMessage()    {}
func (*GameSetStatusV2) Descriptor() ([]byte, []int) {
	return fileDescriptor_b1c955974ad40fae, []int{7}
}

func (m *GameSetStatusV2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameSetStatusV2.Unmarshal(m, b)
}
func (m *GameSetStatusV2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameSetStatusV2.Marshal(b, m, deterministic)
}
func (m *GameSetStatusV2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameSetStatusV2.Merge(m, src)
}
func (m *GameSetStatusV2) XXX_Size() int {
	return xxx_messageInfo_GameSetStatusV2.Size(m)
}
func (m *GameSetStatusV2) XXX_DiscardUnknown() {
	xxx_messageInfo_GameSetStatusV2.DiscardUnknown(m)
}

var xxx_messageInfo_GameSetStatusV2 proto.InternalMessageInfo

func (m *GameSetStatusV2) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *GameSetStatusV2) GetRoomid() uint32 {
	if m != nil {
		return m.Roomid
	}
	return 0
}

func (m *GameSetStatusV2) GetSetid() string {
	if m != nil {
		return m.Setid
	}
	return ""
}

func (m *GameSetStatusV2) GetCpid() uint32 {
	if m != nil {
		return m.Cpid
	}
	return 0
}

func (m *GameSetStatusV2) GetStatusType() GameSetStatusType {
	if m != nil {
		return m.StatusType
	}
	return GameSetStatusType_InitGameSetStatus
}

func (m *GameSetStatusV2) GetScoreinfo() []*ScoreInfoV2 {
	if m != nil {
		return m.Scoreinfo
	}
	return nil
}

func (m *GameSetStatusV2) GetResultType() GameResultType {
	if m != nil {
		return m.ResultType
	}
	return GameResultType_GameWin
}

func (m *GameSetStatusV2) GetUnit() string {
	if m != nil {
		return m.Unit
	}
	return ""
}

func (m *GameSetStatusV2) GetGameSetTime() uint32 {
	if m != nil {
		return m.GameSetTime
	}
	return 0
}

func (m *GameSetStatusV2) GetResultDesc() string {
	if m != nil {
		return m.ResultDesc
	}
	return ""
}

func (m *GameSetStatusV2) GetInfos() []*GetUidByOpenidInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

//主持角色变更
type RoleChange struct {
	Gameid               uint32   `protobuf:"varint,1,opt,name=gameid,proto3" json:"gameid"`
	Roomid               uint32   `protobuf:"varint,2,opt,name=roomid,proto3" json:"roomid"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RoleChange) Reset()         { *m = RoleChange{} }
func (m *RoleChange) String() string { return proto.CompactTextString(m) }
func (*RoleChange) ProtoMessage()    {}
func (*RoleChange) Descriptor() ([]byte, []int) {
	return fileDescriptor_b1c955974ad40fae, []int{8}
}

func (m *RoleChange) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RoleChange.Unmarshal(m, b)
}
func (m *RoleChange) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RoleChange.Marshal(b, m, deterministic)
}
func (m *RoleChange) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RoleChange.Merge(m, src)
}
func (m *RoleChange) XXX_Size() int {
	return xxx_messageInfo_RoleChange.Size(m)
}
func (m *RoleChange) XXX_DiscardUnknown() {
	xxx_messageInfo_RoleChange.DiscardUnknown(m)
}

var xxx_messageInfo_RoleChange proto.InternalMessageInfo

func (m *RoleChange) GetGameid() uint32 {
	if m != nil {
		return m.Gameid
	}
	return 0
}

func (m *RoleChange) GetRoomid() uint32 {
	if m != nil {
		return m.Roomid
	}
	return 0
}

func (m *RoleChange) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type UserExit struct {
	LeaveType            UserLeaveType `protobuf:"varint,1,opt,name=leaveType,proto3,enum=kafka_gameserver_api.UserLeaveType" json:"leaveType"`
	Uid                  uint32        `protobuf:"varint,2,opt,name=uid,proto3" json:"uid"`
	Roomid               uint32        `protobuf:"varint,3,opt,name=roomid,proto3" json:"roomid"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UserExit) Reset()         { *m = UserExit{} }
func (m *UserExit) String() string { return proto.CompactTextString(m) }
func (*UserExit) ProtoMessage()    {}
func (*UserExit) Descriptor() ([]byte, []int) {
	return fileDescriptor_b1c955974ad40fae, []int{9}
}

func (m *UserExit) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserExit.Unmarshal(m, b)
}
func (m *UserExit) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserExit.Marshal(b, m, deterministic)
}
func (m *UserExit) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserExit.Merge(m, src)
}
func (m *UserExit) XXX_Size() int {
	return xxx_messageInfo_UserExit.Size(m)
}
func (m *UserExit) XXX_DiscardUnknown() {
	xxx_messageInfo_UserExit.DiscardUnknown(m)
}

var xxx_messageInfo_UserExit proto.InternalMessageInfo

func (m *UserExit) GetLeaveType() UserLeaveType {
	if m != nil {
		return m.LeaveType
	}
	return UserLeaveType_UserSimpleLeave
}

func (m *UserExit) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserExit) GetRoomid() uint32 {
	if m != nil {
		return m.Roomid
	}
	return 0
}

func init() {
	proto.RegisterEnum("kafka_gameserver_api.GameStatusType", GameStatusType_name, GameStatusType_value)
	proto.RegisterEnum("kafka_gameserver_api.GameStatusNotifyType", GameStatusNotifyType_name, GameStatusNotifyType_value)
	proto.RegisterEnum("kafka_gameserver_api.GameSetStatusType", GameSetStatusType_name, GameSetStatusType_value)
	proto.RegisterEnum("kafka_gameserver_api.GameResultType", GameResultType_name, GameResultType_value)
	proto.RegisterEnum("kafka_gameserver_api.UserLeaveType", UserLeaveType_name, UserLeaveType_value)
	proto.RegisterType((*GameStatusNotifyInfo)(nil), "kafka_gameserver_api.GameStatusNotifyInfo")
	proto.RegisterType((*GameStatus)(nil), "kafka_gameserver_api.GameStatus")
	proto.RegisterType((*GameStatusV2)(nil), "kafka_gameserver_api.GameStatusV2")
	proto.RegisterType((*ScoreInfo)(nil), "kafka_gameserver_api.ScoreInfo")
	proto.RegisterType((*ScoreInfoV2)(nil), "kafka_gameserver_api.ScoreInfoV2")
	proto.RegisterType((*GetUidByOpenidInfo)(nil), "kafka_gameserver_api.GetUidByOpenidInfo")
	proto.RegisterType((*GameSetStatus)(nil), "kafka_gameserver_api.GameSetStatus")
	proto.RegisterType((*GameSetStatusV2)(nil), "kafka_gameserver_api.GameSetStatusV2")
	proto.RegisterType((*RoleChange)(nil), "kafka_gameserver_api.RoleChange")
	proto.RegisterType((*UserExit)(nil), "kafka_gameserver_api.UserExit")
}

func init() { proto.RegisterFile("kafka_gamestatus.proto", fileDescriptor_b1c955974ad40fae) }

var fileDescriptor_b1c955974ad40fae = []byte{
	// 923 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x56, 0x4b, 0x6f, 0xdb, 0x46,
	0x10, 0x36, 0x45, 0xbd, 0x38, 0xb2, 0x14, 0x7a, 0xa3, 0x38, 0x0c, 0x82, 0x22, 0x8a, 0xfa, 0x12,
	0x74, 0xf0, 0xc1, 0x3d, 0x15, 0x68, 0x53, 0x34, 0x0f, 0x18, 0x0e, 0x82, 0xa4, 0x58, 0xf9, 0x51,
	0xa0, 0x07, 0x81, 0x15, 0xd7, 0xce, 0xd6, 0xd2, 0x52, 0x20, 0x57, 0x4e, 0xf5, 0x17, 0x7a, 0xe8,
	0xad, 0xb7, 0x1e, 0xfb, 0x7b, 0x7a, 0xe8, 0x8f, 0xe9, 0xb9, 0x98, 0xd9, 0x15, 0x1f, 0x92, 0x53,
	0x23, 0xbe, 0x38, 0x37, 0xee, 0xf7, 0xcd, 0x0c, 0xbf, 0x9d, 0x6f, 0x87, 0x4b, 0xd8, 0xbd, 0x08,
	0xcf, 0x2e, 0xc2, 0xf1, 0x79, 0x38, 0x13, 0xa9, 0x0e, 0xf5, 0x22, 0xdd, 0x9b, 0x27, 0xb1, 0x8e,
	0x59, 0xb7, 0x80, 0x8b, 0xe4, 0x52, 0x24, 0xe3, 0x70, 0x2e, 0xfb, 0xbf, 0x39, 0xd0, 0x3d, 0x08,
	0x67, 0x62, 0x44, 0xa1, 0xaf, 0x63, 0x2d, 0xcf, 0x96, 0x87, 0xea, 0x2c, 0x66, 0x2f, 0x01, 0x4c,
	0xfa, 0xd1, 0x72, 0x2e, 0x02, 0xa7, 0xe7, 0x0c, 0x3a, 0xfb, 0xc3, 0xbd, 0xab, 0x6a, 0xec, 0xad,
	0xe7, 0x63, 0x06, 0x2f, 0x64, 0xb3, 0x2f, 0xa0, 0x73, 0x9e, 0xc5, 0x3c, 0x5d, 0x6a, 0x11, 0x54,
	0x7a, 0xce, 0x60, 0x9b, 0xaf, 0xa1, 0xfd, 0x7f, 0x2b, 0x00, 0x79, 0x31, 0xb6, 0x0b, 0x75, 0x0c,
	0x90, 0x11, 0xbd, 0xbe, 0xcd, 0xed, 0x0a, 0xf1, 0x24, 0x8e, 0x67, 0x32, 0xa2, 0x32, 0x6d, 0x6e,
	0x57, 0xec, 0x1b, 0xa8, 0x9b, 0x97, 0x06, 0x2e, 0xc9, 0xfd, 0xec, 0x3a, 0xb9, 0x24, 0xd4, 0xe6,
	0xb0, 0x87, 0xe0, 0x4d, 0xe3, 0x73, 0xa9, 0xc6, 0x13, 0xa5, 0x83, 0x2a, 0x15, 0x6e, 0x12, 0xf0,
	0x4c, 0x69, 0xf6, 0x00, 0x9a, 0xbf, 0xc4, 0x96, 0xab, 0x11, 0xd7, 0xc0, 0x35, 0x52, 0x0f, 0xc1,
	0x4b, 0x44, 0x18, 0x2d, 0x89, 0xab, 0x9b, 0x3c, 0x02, 0x2c, 0x49, 0x79, 0x0b, 0x19, 0xa5, 0x41,
	0xa3, 0xe7, 0x22, 0x89, 0xc0, 0xb1, 0x8c, 0x52, 0xf6, 0x09, 0x80, 0xc9, 0x24, 0xb6, 0x49, 0xac,
	0xa9, 0x45, 0xf4, 0x23, 0x68, 0x49, 0x25, 0xb5, 0x0c, 0xa7, 0x18, 0x10, 0x78, 0x54, 0x1a, 0x2c,
	0x74, 0x2c, 0x23, 0xf6, 0x39, 0x74, 0x26, 0x8b, 0x24, 0x11, 0x4a, 0x8f, 0x2f, 0x45, 0x22, 0x63,
	0x15, 0x40, 0xcf, 0x19, 0x78, 0xbc, 0x6d, 0xd1, 0x13, 0x02, 0xd9, 0x63, 0xd8, 0xc6, 0x0e, 0x8c,
	0xe7, 0x17, 0xb3, 0x38, 0x12, 0xd3, 0xa0, 0x45, 0x41, 0x2d, 0xc4, 0x7e, 0x30, 0x50, 0xff, 0x27,
	0xd8, 0xce, 0xbb, 0x72, 0xb2, 0xff, 0xc1, 0x9d, 0x2f, 0xf5, 0xce, 0x2d, 0xf7, 0xae, 0xff, 0xa7,
	0x03, 0xde, 0x68, 0x12, 0x27, 0x82, 0xce, 0x95, 0x0f, 0xee, 0x22, 0xab, 0x8b, 0x8f, 0xac, 0x0b,
	0xb5, 0x14, 0x69, 0x5b, 0xd3, 0x2c, 0x10, 0x95, 0xe9, 0x3b, 0xa9, 0xa8, 0x5c, 0x93, 0x9b, 0x05,
	0xbb, 0x0f, 0x0d, 0x2d, 0xc2, 0xd9, 0x58, 0x46, 0xd6, 0xa2, 0x3a, 0x2e, 0x0f, 0x49, 0x01, 0x11,
	0x91, 0x48, 0x27, 0xe4, 0x90, 0xc7, 0x9b, 0x08, 0x3c, 0x17, 0xe9, 0x04, 0xc9, 0x45, 0x2a, 0x12,
	0x43, 0xd6, 0x0d, 0x89, 0x00, 0x92, 0xfd, 0xbf, 0x1c, 0x68, 0x65, 0xf2, 0xcc, 0xde, 0xe3, 0xb9,
	0x50, 0x56, 0xa3, 0xc7, 0xed, 0xea, 0xb6, 0x65, 0x3e, 0x01, 0x76, 0x20, 0xf4, 0xb1, 0x8c, 0x9e,
	0x2e, 0xdf, 0x90, 0x20, 0xea, 0xe6, 0xfb, 0xc4, 0xda, 0x2e, 0x57, 0xb2, 0x2e, 0xf7, 0xff, 0x76,
	0xa1, 0x4d, 0x1e, 0x0b, 0x7d, 0xc3, 0xf1, 0xc2, 0x06, 0x08, 0x2d, 0x23, 0xda, 0xaa, 0xc7, 0xcd,
	0x82, 0x31, 0xa8, 0x4e, 0xe6, 0xd9, 0x3e, 0xe9, 0x99, 0x1d, 0x94, 0xbe, 0x1d, 0x35, 0x1a, 0xc6,
	0x2f, 0xff, 0x67, 0x18, 0x57, 0x92, 0x36, 0x3e, 0x1c, 0xdf, 0x82, 0x47, 0x6d, 0x96, 0xea, 0x2c,
	0x0e, 0xea, 0x3d, 0x77, 0xd0, 0xda, 0x7f, 0x74, 0x75, 0x9d, 0xcc, 0x41, 0x9e, 0x67, 0xb0, 0xe7,
	0x38, 0x60, 0xe9, 0x62, 0xaa, 0x49, 0x47, 0xe3, 0xba, 0x8f, 0x02, 0xcf, 0x62, 0x79, 0x21, 0x0f,
	0x77, 0xb8, 0x50, 0x52, 0x07, 0x4d, 0xda, 0x36, 0x3d, 0xb3, 0x1e, 0xd0, 0xfc, 0x8c, 0x84, 0x3e,
	0x92, 0x33, 0x61, 0x67, 0xb3, 0x08, 0xb1, 0x27, 0x50, 0x43, 0x0d, 0x69, 0x00, 0x24, 0x7b, 0xf0,
	0x9e, 0xd7, 0x6e, 0x58, 0xca, 0x4d, 0x1a, 0x4e, 0xbf, 0xd1, 0x60, 0x8e, 0x83, 0x19, 0x5a, 0x2b,
	0x8b, 0x0e, 0xc4, 0x3f, 0x2e, 0xdc, 0x29, 0x75, 0xef, 0x06, 0x73, 0x7b, 0x0b, 0x96, 0x7e, 0xb7,
	0x69, 0xe9, 0xe3, 0x6b, 0x2c, 0x3d, 0xd9, 0xff, 0x58, 0x4c, 0x5d, 0x33, 0x05, 0xd6, 0x4d, 0xc9,
	0x5d, 0x6f, 0xdd, 0xc8, 0xf5, 0xfe, 0x6b, 0x00, 0x1e, 0x4f, 0xc5, 0xb3, 0xb7, 0xa1, 0x3a, 0x17,
	0x1f, 0x6c, 0xa7, 0x9d, 0x7a, 0x37, 0x9f, 0xfa, 0x77, 0xd0, 0x3c, 0x4e, 0x45, 0xf2, 0xe2, 0x57,
	0xa9, 0xd9, 0xf7, 0xe0, 0x4d, 0x45, 0x78, 0x29, 0x0a, 0x17, 0xfa, 0xa7, 0x57, 0xeb, 0xc3, 0x94,
	0x57, 0xab, 0x50, 0x9e, 0x67, 0x6d, 0x7e, 0x56, 0x0a, 0x52, 0xdc, 0xa2, 0x94, 0xe1, 0x1f, 0x0e,
	0x74, 0xca, 0x17, 0x2d, 0xeb, 0x00, 0x1c, 0x2a, 0x69, 0xcf, 0x85, 0xbf, 0xc5, 0xee, 0xc2, 0x9d,
	0x91, 0x0e, 0x13, 0x9d, 0x87, 0xf9, 0x0e, 0xdb, 0x81, 0xf6, 0x0b, 0x15, 0x15, 0xa0, 0x0a, 0x0b,
	0xa0, 0xfb, 0xca, 0xde, 0x25, 0xa6, 0x2f, 0x96, 0x71, 0xd9, 0x7d, 0xb8, 0xfb, 0x32, 0xde, 0x24,
	0xaa, 0x98, 0xc2, 0xed, 0x15, 0x5c, 0x62, 0x6a, 0xc3, 0xdf, 0xaf, 0xf8, 0xdf, 0x21, 0x75, 0x5d,
	0xf0, 0xd7, 0x71, 0x7f, 0x0b, 0xdf, 0x50, 0x3a, 0xce, 0x96, 0x70, 0x30, 0x3c, 0x37, 0xca, 0xa2,
	0x15, 0xb6, 0x0b, 0xac, 0x78, 0x8f, 0x5a, 0xdc, 0x65, 0x0f, 0xe0, 0xde, 0xda, 0xa8, 0x5a, 0xaa,
	0x3a, 0xfc, 0x11, 0x76, 0x36, 0x06, 0x86, 0xdd, 0x83, 0x1d, 0x6c, 0x55, 0x89, 0xf0, 0xb7, 0xb0,
	0x7c, 0xde, 0xb1, 0x0c, 0x27, 0x31, 0xab, 0xa6, 0x65, 0x68, 0x65, 0x98, 0x18, 0x07, 0xf2, 0x01,
	0x60, 0x2d, 0x68, 0x20, 0x72, 0x2a, 0x95, 0xbf, 0xb5, 0x5a, 0x1c, 0x49, 0xe1, 0x3b, 0x6c, 0x1b,
	0x9a, 0x14, 0x1b, 0xaa, 0x0b, 0xbf, 0xb2, 0xea, 0xc5, 0xa9, 0x54, 0xa7, 0x52, 0xbf, 0xa5, 0x39,
	0xf4, 0xdd, 0x15, 0x7a, 0x24, 0x45, 0x8e, 0x56, 0x57, 0x99, 0x6f, 0x2e, 0x45, 0xe2, 0xd7, 0x86,
	0x5f, 0x43, 0xbb, 0x74, 0x78, 0xd0, 0x64, 0x04, 0x46, 0x72, 0x36, 0x9f, 0x0a, 0x82, 0x8d, 0xf3,
	0xe6, 0x54, 0xce, 0x65, 0x62, 0x41, 0xe7, 0xe7, 0x3a, 0xfd, 0xa6, 0x7e, 0xf5, 0x5f, 0x00, 0x00,
	0x00, 0xff, 0xff, 0xe4, 0x86, 0x59, 0x90, 0xc0, 0x0a, 0x00, 0x00,
}
