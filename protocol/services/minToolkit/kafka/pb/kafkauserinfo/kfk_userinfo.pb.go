// Code generated by protoc-gen-gogo.
// source: src/minToolkit/kafka/pb/kafkauserinfo/kfk_userinfo.proto
// DO NOT EDIT!

/*
Package kafka_user_info is a generated protocol buffer package.

It is generated from these files:

	src/minToolkit/kafka/pb/kafkauserinfo/kfk_userinfo.proto

It has these top-level messages:

	KUserInfoUpdateEvent
	UserEvent
	UserUnregEventOpt
	UserNameEventOpt
	UserLevelEventOpt
	UserCertifyEventOpt
	UserSexEventOpt
	BanUserEventOpt
	UserSignatureEventOpt
*/
package kafka_user_info

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"

import io "io"
import fmt1 "fmt"
import github_com_gogo_protobuf_proto "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type EVENT_TYPE int32

const (
	EVENT_TYPE_EVENT_REG       EVENT_TYPE = 1
	EVENT_TYPE_EVENT_LOGIN     EVENT_TYPE = 2
	EVENT_TYPE_EVENT_LOGOUT    EVENT_TYPE = 3
	EVENT_TYPE_EVENT_NAME_CHG  EVENT_TYPE = 4
	EVENT_TYPE_EVENT_LEVEL_CHG EVENT_TYPE = 5
	EVENT_TYPE_EVENT_CERTIFY   EVENT_TYPE = 6
	EVENT_TYPE_EVENT_UNCERTIFY EVENT_TYPE = 7
	EVENT_TYPE_EVENT_UNREG     EVENT_TYPE = 8
	EVENT_TYPE_EVENT_FORBID    EVENT_TYPE = 9
	EVENT_TYPE_EVENT_UNFORBID  EVENT_TYPE = 10
	EVENT_TYPE_EVENT_PWD_CHG   EVENT_TYPE = 11
	EVENT_TYPE_EVENT_SEX_CHG   EVENT_TYPE = 12
	EVENT_TYPE_EVENT_SIGN_CHG  EVENT_TYPE = 13
)

var EVENT_TYPE_name = map[int32]string{
	1:  "EVENT_REG",
	2:  "EVENT_LOGIN",
	3:  "EVENT_LOGOUT",
	4:  "EVENT_NAME_CHG",
	5:  "EVENT_LEVEL_CHG",
	6:  "EVENT_CERTIFY",
	7:  "EVENT_UNCERTIFY",
	8:  "EVENT_UNREG",
	9:  "EVENT_FORBID",
	10: "EVENT_UNFORBID",
	11: "EVENT_PWD_CHG",
	12: "EVENT_SEX_CHG",
	13: "EVENT_SIGN_CHG",
}
var EVENT_TYPE_value = map[string]int32{
	"EVENT_REG":       1,
	"EVENT_LOGIN":     2,
	"EVENT_LOGOUT":    3,
	"EVENT_NAME_CHG":  4,
	"EVENT_LEVEL_CHG": 5,
	"EVENT_CERTIFY":   6,
	"EVENT_UNCERTIFY": 7,
	"EVENT_UNREG":     8,
	"EVENT_FORBID":    9,
	"EVENT_UNFORBID":  10,
	"EVENT_PWD_CHG":   11,
	"EVENT_SEX_CHG":   12,
	"EVENT_SIGN_CHG":  13,
}

func (x EVENT_TYPE) Enum() *EVENT_TYPE {
	p := new(EVENT_TYPE)
	*p = x
	return p
}
func (x EVENT_TYPE) String() string {
	return proto.EnumName(EVENT_TYPE_name, int32(x))
}
func (x *EVENT_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(EVENT_TYPE_value, data, "EVENT_TYPE")
	if err != nil {
		return err
	}
	*x = EVENT_TYPE(value)
	return nil
}
func (EVENT_TYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorKfkUserinfo, []int{0} }

// 不建议使用该kfk
type KUserInfoUpdateEvent struct {
	Uid                 uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Sex                 uint32 `protobuf:"varint,2,req,name=sex" json:"sex"`
	RegisteredTimestamp uint32 `protobuf:"varint,3,req,name=registered_timestamp,json=registeredTimestamp" json:"registered_timestamp"`
	UpdateTimestamp     uint32 `protobuf:"varint,4,req,name=update_timestamp,json=updateTimestamp" json:"update_timestamp"`
}

func (m *KUserInfoUpdateEvent) Reset()                    { *m = KUserInfoUpdateEvent{} }
func (m *KUserInfoUpdateEvent) String() string            { return proto.CompactTextString(m) }
func (*KUserInfoUpdateEvent) ProtoMessage()               {}
func (*KUserInfoUpdateEvent) Descriptor() ([]byte, []int) { return fileDescriptorKfkUserinfo, []int{0} }

func (m *KUserInfoUpdateEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *KUserInfoUpdateEvent) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *KUserInfoUpdateEvent) GetRegisteredTimestamp() uint32 {
	if m != nil {
		return m.RegisteredTimestamp
	}
	return 0
}

func (m *KUserInfoUpdateEvent) GetUpdateTimestamp() uint32 {
	if m != nil {
		return m.UpdateTimestamp
	}
	return 0
}

// 用户变化事件
type UserEvent struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Type      uint32 `protobuf:"varint,2,req,name=type" json:"type"`
	UpdateTs  uint32 `protobuf:"varint,3,req,name=update_ts,json=updateTs" json:"update_ts"`
	OptPbInfo []byte `protobuf:"bytes,4,opt,name=opt_pb_info,json=optPbInfo" json:"opt_pb_info"`
}

func (m *UserEvent) Reset()                    { *m = UserEvent{} }
func (m *UserEvent) String() string            { return proto.CompactTextString(m) }
func (*UserEvent) ProtoMessage()               {}
func (*UserEvent) Descriptor() ([]byte, []int) { return fileDescriptorKfkUserinfo, []int{1} }

func (m *UserEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserEvent) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *UserEvent) GetUpdateTs() uint32 {
	if m != nil {
		return m.UpdateTs
	}
	return 0
}

func (m *UserEvent) GetOptPbInfo() []byte {
	if m != nil {
		return m.OptPbInfo
	}
	return nil
}

type UserUnregEventOpt struct {
	AppName string `protobuf:"bytes,1,opt,name=app_name,json=appName" json:"app_name"`
}

func (m *UserUnregEventOpt) Reset()                    { *m = UserUnregEventOpt{} }
func (m *UserUnregEventOpt) String() string            { return proto.CompactTextString(m) }
func (*UserUnregEventOpt) ProtoMessage()               {}
func (*UserUnregEventOpt) Descriptor() ([]byte, []int) { return fileDescriptorKfkUserinfo, []int{2} }

func (m *UserUnregEventOpt) GetAppName() string {
	if m != nil {
		return m.AppName
	}
	return ""
}

type UserNameEventOpt struct {
	Nickname string `protobuf:"bytes,1,req,name=nickname" json:"nickname"`
	Level    uint32 `protobuf:"varint,2,req,name=level" json:"level"`
}

func (m *UserNameEventOpt) Reset()                    { *m = UserNameEventOpt{} }
func (m *UserNameEventOpt) String() string            { return proto.CompactTextString(m) }
func (*UserNameEventOpt) ProtoMessage()               {}
func (*UserNameEventOpt) Descriptor() ([]byte, []int) { return fileDescriptorKfkUserinfo, []int{3} }

func (m *UserNameEventOpt) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserNameEventOpt) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

type UserLevelEventOpt struct {
	OriginLevel uint32 `protobuf:"varint,1,req,name=origin_level,json=originLevel" json:"origin_level"`
	Level       uint32 `protobuf:"varint,2,req,name=level" json:"level"`
}

func (m *UserLevelEventOpt) Reset()                    { *m = UserLevelEventOpt{} }
func (m *UserLevelEventOpt) String() string            { return proto.CompactTextString(m) }
func (*UserLevelEventOpt) ProtoMessage()               {}
func (*UserLevelEventOpt) Descriptor() ([]byte, []int) { return fileDescriptorKfkUserinfo, []int{4} }

func (m *UserLevelEventOpt) GetOriginLevel() uint32 {
	if m != nil {
		return m.OriginLevel
	}
	return 0
}

func (m *UserLevelEventOpt) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

type UserCertifyEventOpt struct {
	CertifyStyle string `protobuf:"bytes,1,req,name=certify_style,json=certifyStyle" json:"certify_style"`
}

func (m *UserCertifyEventOpt) Reset()                    { *m = UserCertifyEventOpt{} }
func (m *UserCertifyEventOpt) String() string            { return proto.CompactTextString(m) }
func (*UserCertifyEventOpt) ProtoMessage()               {}
func (*UserCertifyEventOpt) Descriptor() ([]byte, []int) { return fileDescriptorKfkUserinfo, []int{5} }

func (m *UserCertifyEventOpt) GetCertifyStyle() string {
	if m != nil {
		return m.CertifyStyle
	}
	return ""
}

type UserSexEventOpt struct {
	Sex          uint32 `protobuf:"varint,1,req,name=sex" json:"sex"`
	RegisteredAt uint32 `protobuf:"varint,2,req,name=registered_at,json=registeredAt" json:"registered_at"`
}

func (m *UserSexEventOpt) Reset()                    { *m = UserSexEventOpt{} }
func (m *UserSexEventOpt) String() string            { return proto.CompactTextString(m) }
func (*UserSexEventOpt) ProtoMessage()               {}
func (*UserSexEventOpt) Descriptor() ([]byte, []int) { return fileDescriptorKfkUserinfo, []int{6} }

func (m *UserSexEventOpt) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *UserSexEventOpt) GetRegisteredAt() uint32 {
	if m != nil {
		return m.RegisteredAt
	}
	return 0
}

type BanUserEventOpt struct {
	OpType       uint32 `protobuf:"varint,1,opt,name=op_type,json=opType" json:"op_type"`
	DeviceId     string `protobuf:"bytes,2,opt,name=device_id,json=deviceId" json:"device_id"`
	ClientIp     string `protobuf:"bytes,3,opt,name=client_ip,json=clientIp" json:"client_ip"`
	At           uint32 `protobuf:"varint,4,opt,name=at" json:"at"`
	RecoveryAt   uint32 `protobuf:"varint,5,opt,name=recovery_at,json=recoveryAt" json:"recovery_at"`
	Reason       string `protobuf:"bytes,6,opt,name=reason" json:"reason"`
	OperatorId   string `protobuf:"bytes,7,opt,name=operator_id,json=operatorId" json:"operator_id"`
	ProofPic     string `protobuf:"bytes,8,opt,name=proof_pic,json=proofPic" json:"proof_pic"`
	ExtInfo      string `protobuf:"bytes,9,opt,name=ext_info,json=extInfo" json:"ext_info"`
	NoLog        bool   `protobuf:"varint,10,opt,name=no_log,json=noLog" json:"no_log"`
	ReasonDetail string `protobuf:"bytes,11,opt,name=reason_detail,json=reasonDetail" json:"reason_detail"`
}

func (m *BanUserEventOpt) Reset()                    { *m = BanUserEventOpt{} }
func (m *BanUserEventOpt) String() string            { return proto.CompactTextString(m) }
func (*BanUserEventOpt) ProtoMessage()               {}
func (*BanUserEventOpt) Descriptor() ([]byte, []int) { return fileDescriptorKfkUserinfo, []int{7} }

func (m *BanUserEventOpt) GetOpType() uint32 {
	if m != nil {
		return m.OpType
	}
	return 0
}

func (m *BanUserEventOpt) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *BanUserEventOpt) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

func (m *BanUserEventOpt) GetAt() uint32 {
	if m != nil {
		return m.At
	}
	return 0
}

func (m *BanUserEventOpt) GetRecoveryAt() uint32 {
	if m != nil {
		return m.RecoveryAt
	}
	return 0
}

func (m *BanUserEventOpt) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *BanUserEventOpt) GetOperatorId() string {
	if m != nil {
		return m.OperatorId
	}
	return ""
}

func (m *BanUserEventOpt) GetProofPic() string {
	if m != nil {
		return m.ProofPic
	}
	return ""
}

func (m *BanUserEventOpt) GetExtInfo() string {
	if m != nil {
		return m.ExtInfo
	}
	return ""
}

func (m *BanUserEventOpt) GetNoLog() bool {
	if m != nil {
		return m.NoLog
	}
	return false
}

func (m *BanUserEventOpt) GetReasonDetail() string {
	if m != nil {
		return m.ReasonDetail
	}
	return ""
}

type UserSignatureEventOpt struct {
	Signature string `protobuf:"bytes,1,opt,name=signature" json:"signature"`
}

func (m *UserSignatureEventOpt) Reset()         { *m = UserSignatureEventOpt{} }
func (m *UserSignatureEventOpt) String() string { return proto.CompactTextString(m) }
func (*UserSignatureEventOpt) ProtoMessage()    {}
func (*UserSignatureEventOpt) Descriptor() ([]byte, []int) {
	return fileDescriptorKfkUserinfo, []int{8}
}

func (m *UserSignatureEventOpt) GetSignature() string {
	if m != nil {
		return m.Signature
	}
	return ""
}

func init() {
	proto.RegisterType((*KUserInfoUpdateEvent)(nil), "kafka_user_info.KUserInfoUpdateEvent")
	proto.RegisterType((*UserEvent)(nil), "kafka_user_info.UserEvent")
	proto.RegisterType((*UserUnregEventOpt)(nil), "kafka_user_info.UserUnregEventOpt")
	proto.RegisterType((*UserNameEventOpt)(nil), "kafka_user_info.UserNameEventOpt")
	proto.RegisterType((*UserLevelEventOpt)(nil), "kafka_user_info.UserLevelEventOpt")
	proto.RegisterType((*UserCertifyEventOpt)(nil), "kafka_user_info.UserCertifyEventOpt")
	proto.RegisterType((*UserSexEventOpt)(nil), "kafka_user_info.UserSexEventOpt")
	proto.RegisterType((*BanUserEventOpt)(nil), "kafka_user_info.BanUserEventOpt")
	proto.RegisterType((*UserSignatureEventOpt)(nil), "kafka_user_info.UserSignatureEventOpt")
	proto.RegisterEnum("kafka_user_info.EVENT_TYPE", EVENT_TYPE_name, EVENT_TYPE_value)
}
func (m *KUserInfoUpdateEvent) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *KUserInfoUpdateEvent) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkUserinfo(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintKfkUserinfo(dAtA, i, uint64(m.Sex))
	dAtA[i] = 0x18
	i++
	i = encodeVarintKfkUserinfo(dAtA, i, uint64(m.RegisteredTimestamp))
	dAtA[i] = 0x20
	i++
	i = encodeVarintKfkUserinfo(dAtA, i, uint64(m.UpdateTimestamp))
	return i, nil
}

func (m *UserEvent) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserEvent) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkUserinfo(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintKfkUserinfo(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x18
	i++
	i = encodeVarintKfkUserinfo(dAtA, i, uint64(m.UpdateTs))
	if m.OptPbInfo != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintKfkUserinfo(dAtA, i, uint64(len(m.OptPbInfo)))
		i += copy(dAtA[i:], m.OptPbInfo)
	}
	return i, nil
}

func (m *UserUnregEventOpt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserUnregEventOpt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintKfkUserinfo(dAtA, i, uint64(len(m.AppName)))
	i += copy(dAtA[i:], m.AppName)
	return i, nil
}

func (m *UserNameEventOpt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserNameEventOpt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintKfkUserinfo(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	dAtA[i] = 0x10
	i++
	i = encodeVarintKfkUserinfo(dAtA, i, uint64(m.Level))
	return i, nil
}

func (m *UserLevelEventOpt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserLevelEventOpt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkUserinfo(dAtA, i, uint64(m.OriginLevel))
	dAtA[i] = 0x10
	i++
	i = encodeVarintKfkUserinfo(dAtA, i, uint64(m.Level))
	return i, nil
}

func (m *UserCertifyEventOpt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserCertifyEventOpt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintKfkUserinfo(dAtA, i, uint64(len(m.CertifyStyle)))
	i += copy(dAtA[i:], m.CertifyStyle)
	return i, nil
}

func (m *UserSexEventOpt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserSexEventOpt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkUserinfo(dAtA, i, uint64(m.Sex))
	dAtA[i] = 0x10
	i++
	i = encodeVarintKfkUserinfo(dAtA, i, uint64(m.RegisteredAt))
	return i, nil
}

func (m *BanUserEventOpt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BanUserEventOpt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkUserinfo(dAtA, i, uint64(m.OpType))
	dAtA[i] = 0x12
	i++
	i = encodeVarintKfkUserinfo(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintKfkUserinfo(dAtA, i, uint64(len(m.ClientIp)))
	i += copy(dAtA[i:], m.ClientIp)
	dAtA[i] = 0x20
	i++
	i = encodeVarintKfkUserinfo(dAtA, i, uint64(m.At))
	dAtA[i] = 0x28
	i++
	i = encodeVarintKfkUserinfo(dAtA, i, uint64(m.RecoveryAt))
	dAtA[i] = 0x32
	i++
	i = encodeVarintKfkUserinfo(dAtA, i, uint64(len(m.Reason)))
	i += copy(dAtA[i:], m.Reason)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintKfkUserinfo(dAtA, i, uint64(len(m.OperatorId)))
	i += copy(dAtA[i:], m.OperatorId)
	dAtA[i] = 0x42
	i++
	i = encodeVarintKfkUserinfo(dAtA, i, uint64(len(m.ProofPic)))
	i += copy(dAtA[i:], m.ProofPic)
	dAtA[i] = 0x4a
	i++
	i = encodeVarintKfkUserinfo(dAtA, i, uint64(len(m.ExtInfo)))
	i += copy(dAtA[i:], m.ExtInfo)
	dAtA[i] = 0x50
	i++
	if m.NoLog {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x5a
	i++
	i = encodeVarintKfkUserinfo(dAtA, i, uint64(len(m.ReasonDetail)))
	i += copy(dAtA[i:], m.ReasonDetail)
	return i, nil
}

func (m *UserSignatureEventOpt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserSignatureEventOpt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintKfkUserinfo(dAtA, i, uint64(len(m.Signature)))
	i += copy(dAtA[i:], m.Signature)
	return i, nil
}

func encodeFixed64KfkUserinfo(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32KfkUserinfo(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintKfkUserinfo(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *KUserInfoUpdateEvent) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkUserinfo(uint64(m.Uid))
	n += 1 + sovKfkUserinfo(uint64(m.Sex))
	n += 1 + sovKfkUserinfo(uint64(m.RegisteredTimestamp))
	n += 1 + sovKfkUserinfo(uint64(m.UpdateTimestamp))
	return n
}

func (m *UserEvent) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkUserinfo(uint64(m.Uid))
	n += 1 + sovKfkUserinfo(uint64(m.Type))
	n += 1 + sovKfkUserinfo(uint64(m.UpdateTs))
	if m.OptPbInfo != nil {
		l = len(m.OptPbInfo)
		n += 1 + l + sovKfkUserinfo(uint64(l))
	}
	return n
}

func (m *UserUnregEventOpt) Size() (n int) {
	var l int
	_ = l
	l = len(m.AppName)
	n += 1 + l + sovKfkUserinfo(uint64(l))
	return n
}

func (m *UserNameEventOpt) Size() (n int) {
	var l int
	_ = l
	l = len(m.Nickname)
	n += 1 + l + sovKfkUserinfo(uint64(l))
	n += 1 + sovKfkUserinfo(uint64(m.Level))
	return n
}

func (m *UserLevelEventOpt) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkUserinfo(uint64(m.OriginLevel))
	n += 1 + sovKfkUserinfo(uint64(m.Level))
	return n
}

func (m *UserCertifyEventOpt) Size() (n int) {
	var l int
	_ = l
	l = len(m.CertifyStyle)
	n += 1 + l + sovKfkUserinfo(uint64(l))
	return n
}

func (m *UserSexEventOpt) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkUserinfo(uint64(m.Sex))
	n += 1 + sovKfkUserinfo(uint64(m.RegisteredAt))
	return n
}

func (m *BanUserEventOpt) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkUserinfo(uint64(m.OpType))
	l = len(m.DeviceId)
	n += 1 + l + sovKfkUserinfo(uint64(l))
	l = len(m.ClientIp)
	n += 1 + l + sovKfkUserinfo(uint64(l))
	n += 1 + sovKfkUserinfo(uint64(m.At))
	n += 1 + sovKfkUserinfo(uint64(m.RecoveryAt))
	l = len(m.Reason)
	n += 1 + l + sovKfkUserinfo(uint64(l))
	l = len(m.OperatorId)
	n += 1 + l + sovKfkUserinfo(uint64(l))
	l = len(m.ProofPic)
	n += 1 + l + sovKfkUserinfo(uint64(l))
	l = len(m.ExtInfo)
	n += 1 + l + sovKfkUserinfo(uint64(l))
	n += 2
	l = len(m.ReasonDetail)
	n += 1 + l + sovKfkUserinfo(uint64(l))
	return n
}

func (m *UserSignatureEventOpt) Size() (n int) {
	var l int
	_ = l
	l = len(m.Signature)
	n += 1 + l + sovKfkUserinfo(uint64(l))
	return n
}

func sovKfkUserinfo(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozKfkUserinfo(x uint64) (n int) {
	return sovKfkUserinfo(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *KUserInfoUpdateEvent) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkUserinfo
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: KUserInfoUpdateEvent: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: KUserInfoUpdateEvent: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkUserinfo
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Sex", wireType)
			}
			m.Sex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkUserinfo
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Sex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field RegisteredTimestamp", wireType)
			}
			m.RegisteredTimestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkUserinfo
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegisteredTimestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field UpdateTimestamp", wireType)
			}
			m.UpdateTimestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkUserinfo
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UpdateTimestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipKfkUserinfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkUserinfo
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("sex")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("registered_timestamp")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("update_timestamp")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserEvent) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkUserinfo
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: UserEvent: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: UserEvent: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkUserinfo
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkUserinfo
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field UpdateTs", wireType)
			}
			m.UpdateTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkUserinfo
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UpdateTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OptPbInfo", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkUserinfo
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthKfkUserinfo
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OptPbInfo = append(m.OptPbInfo[:0], dAtA[iNdEx:postIndex]...)
			if m.OptPbInfo == nil {
				m.OptPbInfo = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipKfkUserinfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkUserinfo
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("update_ts")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserUnregEventOpt) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkUserinfo
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: UserUnregEventOpt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: UserUnregEventOpt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field AppName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkUserinfo
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkUserinfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipKfkUserinfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkUserinfo
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserNameEventOpt) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkUserinfo
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: UserNameEventOpt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: UserNameEventOpt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkUserinfo
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkUserinfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Level", wireType)
			}
			m.Level = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkUserinfo
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Level |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipKfkUserinfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkUserinfo
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("nickname")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("level")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserLevelEventOpt) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkUserinfo
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: UserLevelEventOpt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: UserLevelEventOpt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OriginLevel", wireType)
			}
			m.OriginLevel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkUserinfo
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OriginLevel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Level", wireType)
			}
			m.Level = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkUserinfo
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Level |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipKfkUserinfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkUserinfo
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("origin_level")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("level")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserCertifyEventOpt) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkUserinfo
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: UserCertifyEventOpt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: UserCertifyEventOpt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field CertifyStyle", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkUserinfo
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkUserinfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CertifyStyle = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipKfkUserinfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkUserinfo
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("certify_style")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserSexEventOpt) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkUserinfo
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: UserSexEventOpt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: UserSexEventOpt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Sex", wireType)
			}
			m.Sex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkUserinfo
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Sex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field RegisteredAt", wireType)
			}
			m.RegisteredAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkUserinfo
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegisteredAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipKfkUserinfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkUserinfo
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("sex")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("registered_at")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BanUserEventOpt) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkUserinfo
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: BanUserEventOpt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: BanUserEventOpt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OpType", wireType)
			}
			m.OpType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkUserinfo
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkUserinfo
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkUserinfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ClientIp", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkUserinfo
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkUserinfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ClientIp = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field At", wireType)
			}
			m.At = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkUserinfo
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.At |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field RecoveryAt", wireType)
			}
			m.RecoveryAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkUserinfo
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RecoveryAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Reason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkUserinfo
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkUserinfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Reason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OperatorId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkUserinfo
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkUserinfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OperatorId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ProofPic", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkUserinfo
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkUserinfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ProofPic = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ExtInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkUserinfo
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkUserinfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ExtInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field NoLog", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkUserinfo
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.NoLog = bool(v != 0)
		case 11:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ReasonDetail", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkUserinfo
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkUserinfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ReasonDetail = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipKfkUserinfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkUserinfo
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserSignatureEventOpt) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkUserinfo
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: UserSignatureEventOpt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: UserSignatureEventOpt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Signature", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkUserinfo
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkUserinfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Signature = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipKfkUserinfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkUserinfo
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipKfkUserinfo(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowKfkUserinfo
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowKfkUserinfo
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowKfkUserinfo
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthKfkUserinfo
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowKfkUserinfo
					}
					if iNdEx >= l {
						return 0, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipKfkUserinfo(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt1.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthKfkUserinfo = fmt1.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowKfkUserinfo   = fmt1.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("src/minToolkit/kafka/pb/kafkauserinfo/kfk_userinfo.proto", fileDescriptorKfkUserinfo)
}

var fileDescriptorKfkUserinfo = []byte{
	// 764 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x7c, 0x93, 0xcb, 0x8e, 0xe3, 0x44,
	0x14, 0x86, 0xc7, 0xee, 0x74, 0x3a, 0x3e, 0x49, 0x88, 0xa7, 0xba, 0x41, 0x16, 0x97, 0x9e, 0x60,
	0x81, 0xe8, 0x61, 0x41, 0x36, 0x48, 0x20, 0xb1, 0xa1, 0x2f, 0x9e, 0x26, 0xa2, 0x71, 0x47, 0x89,
	0x33, 0xcc, 0xac, 0xac, 0x6a, 0xa7, 0x12, 0x95, 0x92, 0xb8, 0x4a, 0xe5, 0x4a, 0x94, 0xec, 0x58,
	0x20, 0xd6, 0x3c, 0x0a, 0x8f, 0x31, 0x4b, 0x9e, 0x00, 0xa1, 0xe6, 0x45, 0x50, 0x55, 0xf9, 0x96,
	0x59, 0xf4, 0xce, 0xf5, 0x9d, 0xf3, 0x9f, 0xf3, 0xa7, 0xea, 0x0f, 0x7c, 0x9f, 0x89, 0x64, 0xb0,
	0xa6, 0x69, 0xc4, 0xd8, 0x6a, 0x49, 0xe5, 0x60, 0x89, 0xe7, 0x4b, 0x3c, 0xe0, 0x0f, 0xe6, 0x63,
	0x93, 0x11, 0x41, 0xd3, 0x39, 0x1b, 0x2c, 0xe7, 0xcb, 0xb8, 0x38, 0x7c, 0xc3, 0x05, 0x93, 0x0c,
	0xf5, 0x74, 0x87, 0xa6, 0xb1, 0xc2, 0xfe, 0x5f, 0x16, 0x9c, 0xfd, 0x3c, 0xcd, 0x88, 0x18, 0xa6,
	0x73, 0x36, 0xe5, 0x33, 0x2c, 0x49, 0xb0, 0x25, 0xa9, 0x44, 0x1f, 0xc1, 0xd1, 0x86, 0xce, 0x3c,
	0xab, 0x6f, 0x5f, 0x74, 0xaf, 0x1a, 0xef, 0xfe, 0x79, 0xf1, 0x6c, 0xac, 0x80, 0xe2, 0x19, 0xd9,
	0x79, 0x76, 0x9d, 0x67, 0x64, 0x87, 0xbe, 0x83, 0x33, 0x41, 0x16, 0x34, 0x93, 0x44, 0x90, 0x59,
	0x2c, 0xe9, 0x9a, 0x64, 0x12, 0xaf, 0xb9, 0x77, 0x54, 0x6b, 0x3c, 0xad, 0x3a, 0xa2, 0xa2, 0x01,
	0x0d, 0xc0, 0xdd, 0xe8, 0xbd, 0x35, 0x51, 0xa3, 0x26, 0xea, 0x99, 0x6a, 0x29, 0xf0, 0xff, 0xb0,
	0xc0, 0x51, 0x8e, 0x9f, 0xf6, 0xe9, 0x41, 0x43, 0xee, 0x39, 0x39, 0x30, 0xaa, 0x09, 0xfa, 0x1c,
	0x9c, 0x62, 0x61, 0x76, 0x60, 0xaf, 0x95, 0x6f, 0xca, 0xd0, 0x17, 0xd0, 0x66, 0x5c, 0xc6, 0xfc,
	0x41, 0x5f, 0x92, 0xd7, 0xe8, 0x5b, 0x17, 0x9d, 0xbc, 0xc9, 0x61, 0x5c, 0x8e, 0x1e, 0xd4, 0x6d,
	0xf9, 0xdf, 0xc2, 0x73, 0xe5, 0x63, 0x9a, 0x0a, 0xb2, 0xd0, 0x66, 0xee, 0xb9, 0x44, 0x2f, 0xa0,
	0x85, 0x39, 0x8f, 0x53, 0xbc, 0x26, 0x9e, 0xd5, 0xb7, 0x2e, 0x9c, 0x5c, 0x77, 0x82, 0x39, 0x0f,
	0xf1, 0x9a, 0xf8, 0x23, 0x70, 0x95, 0x4a, 0x7d, 0x97, 0xa2, 0x3e, 0xb4, 0x52, 0x9a, 0x2c, 0x73,
	0x91, 0x5d, 0x8a, 0x4a, 0x8a, 0x3e, 0x86, 0xe3, 0x15, 0xd9, 0x92, 0xd5, 0xc1, 0xef, 0x31, 0xc8,
	0x7f, 0x63, 0x7c, 0xdc, 0xa9, 0x43, 0x39, 0xf2, 0x2b, 0xe8, 0x30, 0x41, 0x17, 0x34, 0x8d, 0x8d,
	0xae, 0x7e, 0x41, 0x6d, 0x53, 0xd1, 0x82, 0x27, 0x27, 0xff, 0x08, 0xa7, 0x6a, 0xf2, 0x35, 0x11,
	0x92, 0xce, 0xf7, 0xe5, 0xec, 0x97, 0xd0, 0x4d, 0x0c, 0x8a, 0x33, 0xb9, 0x5f, 0x1d, 0x7a, 0xee,
	0xe4, 0xa5, 0x89, 0xaa, 0xf8, 0x11, 0xf4, 0xd4, 0x84, 0x09, 0xd9, 0x95, 0xea, 0x3c, 0x41, 0xd6,
	0xfb, 0x09, 0x7a, 0x09, 0xdd, 0x5a, 0x82, 0xb0, 0x3c, 0x30, 0xd4, 0xa9, 0x4a, 0x97, 0xd2, 0xff,
	0xfd, 0x08, 0x7a, 0x57, 0x38, 0x2d, 0x53, 0xa0, 0xc6, 0x7e, 0x06, 0x27, 0x8c, 0xc7, 0xfa, 0xcd,
	0xd5, 0xbd, 0x17, 0xc2, 0x26, 0xe3, 0x51, 0xfe, 0xea, 0x33, 0xb2, 0xa5, 0x09, 0x89, 0xe9, 0xcc,
	0xb3, 0x6b, 0x0f, 0xd3, 0x32, 0x78, 0x38, 0x53, 0x2d, 0xc9, 0x8a, 0x92, 0x54, 0xc6, 0x54, 0xe5,
	0xb6, 0xd6, 0x62, 0xf0, 0x90, 0xa3, 0x33, 0xb0, 0xb1, 0xd4, 0x79, 0x28, 0xe6, 0xdb, 0x58, 0xa2,
	0x2f, 0xa1, 0x2d, 0x48, 0xc2, 0xb6, 0x44, 0xec, 0x95, 0xef, 0xe3, 0x5a, 0x19, 0x8a, 0xc2, 0xa5,
	0x44, 0x9f, 0x42, 0x53, 0x10, 0x9c, 0xb1, 0xd4, 0x6b, 0xd6, 0x86, 0xe7, 0x4c, 0x0d, 0x61, 0x9c,
	0x08, 0x2c, 0x99, 0x50, 0x16, 0x4f, 0x6a, 0x2d, 0x50, 0x14, 0x8c, 0x49, 0x2e, 0x18, 0x9b, 0xc7,
	0x9c, 0x26, 0x5e, 0xab, 0x6e, 0x52, 0xe3, 0x11, 0x4d, 0x54, 0x04, 0xc9, 0x4e, 0x9a, 0xe8, 0x3a,
	0xf5, 0x08, 0x92, 0x9d, 0x54, 0xc1, 0x45, 0x9f, 0x40, 0x33, 0x65, 0xf1, 0x8a, 0x2d, 0x3c, 0xe8,
	0x5b, 0x17, 0xad, 0xe2, 0xcd, 0x53, 0x76, 0xc7, 0x16, 0xe6, 0x19, 0x94, 0xa3, 0x78, 0x46, 0x24,
	0xa6, 0x2b, 0xaf, 0x5d, 0x1b, 0xd1, 0x31, 0xa5, 0x1b, 0x5d, 0xf1, 0x7f, 0x80, 0x0f, 0xf5, 0xe3,
	0xd2, 0x45, 0x8a, 0xe5, 0x46, 0x54, 0x79, 0xf6, 0xc1, 0xc9, 0x0a, 0x78, 0xf0, 0x2f, 0xa8, 0xf0,
	0xd7, 0xbf, 0xd9, 0x00, 0xc1, 0xeb, 0x20, 0x8c, 0xe2, 0xe8, 0xed, 0x28, 0x40, 0x5d, 0x70, 0xcc,
	0x69, 0x1c, 0xdc, 0xba, 0x16, 0xea, 0x41, 0xdb, 0x1c, 0xef, 0xee, 0x6f, 0x87, 0xa1, 0x6b, 0x23,
	0x17, 0x3a, 0x25, 0xb8, 0x9f, 0x46, 0xee, 0x11, 0x42, 0xf0, 0x81, 0x21, 0xe1, 0xe5, 0x2f, 0x41,
	0x7c, 0xfd, 0xd3, 0xad, 0xdb, 0x40, 0xa7, 0xd0, 0xcb, 0xbb, 0x82, 0xd7, 0xc1, 0x9d, 0x86, 0xc7,
	0xe8, 0x39, 0x74, 0x0d, 0xbc, 0x0e, 0xc6, 0xd1, 0xf0, 0xd5, 0x5b, 0xb7, 0x59, 0xf5, 0x4d, 0xc3,
	0x02, 0x9e, 0x54, 0x3b, 0xa7, 0xa1, 0x32, 0xd1, 0xaa, 0x76, 0xbe, 0xba, 0x1f, 0x5f, 0x0d, 0x6f,
	0x5c, 0xa7, 0xda, 0x39, 0x0d, 0x73, 0x06, 0xd5, 0xf8, 0xd1, 0xaf, 0x37, 0x7a, 0x63, 0xbb, 0x42,
	0x93, 0xe0, 0x8d, 0x46, 0x9d, 0x4a, 0x39, 0x19, 0xde, 0x86, 0x9a, 0x75, 0xaf, 0xdc, 0x77, 0x8f,
	0xe7, 0xd6, 0xdf, 0x8f, 0xe7, 0xd6, 0xbf, 0x8f, 0xe7, 0xd6, 0x9f, 0xff, 0x9d, 0x3f, 0xfb, 0x3f,
	0x00, 0x00, 0xff, 0xff, 0x06, 0x2a, 0xda, 0x3d, 0xda, 0x05, 0x00, 0x00,
}
