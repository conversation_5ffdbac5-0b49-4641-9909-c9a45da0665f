// Code generated by protoc-gen-gogo.
// source: src/minToolkit/kafka/pb/kfk_simple_channel.proto
// DO NOT EDIT!

/*
	Package kafkachannalevent is a generated protocol buffer package.

	It is generated from these files:
		src/minToolkit/kafka/pb/kfk_simple_channel.proto

	It has these top-level messages:
		ChSimpleEvent
		ChSimpleLeaveOpt
		ChSimpleEnterOpt
		ChSimpleExpiredNotifyOpt
		ChSimpleAdminUpdateOpt
*/
package kafkachannalevent

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"

import io "io"
import fmt1 "fmt"
import github_com_gogo_protobuf_proto "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type ESIMPLE_EVENT_TYPE int32

const (
	ESIMPLE_EVENT_TYPE_ENUM_SIMPLE_ENTER           ESIMPLE_EVENT_TYPE = 1
	ESIMPLE_EVENT_TYPE_ENUM_SIMPLE_LEAVE           ESIMPLE_EVENT_TYPE = 2
	ESIMPLE_EVENT_TYPE_ENUM_SIMPLE_EXPIRE_QUIT     ESIMPLE_EVENT_TYPE = 3
	ESIMPLE_EVENT_TYPE_ENUM_SIMPLE_EXPIRE_NOTIFY   ESIMPLE_EVENT_TYPE = 4
	ESIMPLE_EVENT_TYPE_ENUM_SIMPLE_ADMIN_UPDATE    ESIMPLE_EVENT_TYPE = 5
	ESIMPLE_EVENT_TYPE_ENUM_SIMPLE_MUTE            ESIMPLE_EVENT_TYPE = 6
	ESIMPLE_EVENT_TYPE_ENUM_SIMPLE_UNMUTE          ESIMPLE_EVENT_TYPE = 7
	ESIMPLE_EVENT_TYPE_ENUM_SIMPLE_PCHELPER_NOTIFY ESIMPLE_EVENT_TYPE = 8
)

var ESIMPLE_EVENT_TYPE_name = map[int32]string{
	1: "ENUM_SIMPLE_ENTER",
	2: "ENUM_SIMPLE_LEAVE",
	3: "ENUM_SIMPLE_EXPIRE_QUIT",
	4: "ENUM_SIMPLE_EXPIRE_NOTIFY",
	5: "ENUM_SIMPLE_ADMIN_UPDATE",
	6: "ENUM_SIMPLE_MUTE",
	7: "ENUM_SIMPLE_UNMUTE",
	8: "ENUM_SIMPLE_PCHELPER_NOTIFY",
}
var ESIMPLE_EVENT_TYPE_value = map[string]int32{
	"ENUM_SIMPLE_ENTER":           1,
	"ENUM_SIMPLE_LEAVE":           2,
	"ENUM_SIMPLE_EXPIRE_QUIT":     3,
	"ENUM_SIMPLE_EXPIRE_NOTIFY":   4,
	"ENUM_SIMPLE_ADMIN_UPDATE":    5,
	"ENUM_SIMPLE_MUTE":            6,
	"ENUM_SIMPLE_UNMUTE":          7,
	"ENUM_SIMPLE_PCHELPER_NOTIFY": 8,
}

func (x ESIMPLE_EVENT_TYPE) Enum() *ESIMPLE_EVENT_TYPE {
	p := new(ESIMPLE_EVENT_TYPE)
	*p = x
	return p
}
func (x ESIMPLE_EVENT_TYPE) String() string {
	return proto.EnumName(ESIMPLE_EVENT_TYPE_name, int32(x))
}
func (x *ESIMPLE_EVENT_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ESIMPLE_EVENT_TYPE_value, data, "ESIMPLE_EVENT_TYPE")
	if err != nil {
		return err
	}
	*x = ESIMPLE_EVENT_TYPE(value)
	return nil
}
func (ESIMPLE_EVENT_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorKfkSimpleChannel, []int{0}
}

type CHANNEL_TYPE int32

const (
	CHANNEL_TYPE_DEFAULT                   CHANNEL_TYPE = 0
	CHANNEL_TYPE_COMMON_GAME_UNION_CHANNEL CHANNEL_TYPE = 1
	CHANNEL_TYPE_LIVE_CHANNEL              CHANNEL_TYPE = 2
	CHANNEL_TYPE_TOPIC_CHANNEL             CHANNEL_TYPE = 3
	CHANNEL_TYPE_PUBLIC_GAME_UNION_CHANNEL CHANNEL_TYPE = 4
	CHANNEL_TYPE_TEMPORARY_GAME_CHANNEL    CHANNEL_TYPE = 6
)

var CHANNEL_TYPE_name = map[int32]string{
	0: "DEFAULT",
	1: "COMMON_GAME_UNION_CHANNEL",
	2: "LIVE_CHANNEL",
	3: "TOPIC_CHANNEL",
	4: "PUBLIC_GAME_UNION_CHANNEL",
	6: "TEMPORARY_GAME_CHANNEL",
}
var CHANNEL_TYPE_value = map[string]int32{
	"DEFAULT":                   0,
	"COMMON_GAME_UNION_CHANNEL": 1,
	"LIVE_CHANNEL":              2,
	"TOPIC_CHANNEL":             3,
	"PUBLIC_GAME_UNION_CHANNEL": 4,
	"TEMPORARY_GAME_CHANNEL":    6,
}

func (x CHANNEL_TYPE) Enum() *CHANNEL_TYPE {
	p := new(CHANNEL_TYPE)
	*p = x
	return p
}
func (x CHANNEL_TYPE) String() string {
	return proto.EnumName(CHANNEL_TYPE_name, int32(x))
}
func (x *CHANNEL_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(CHANNEL_TYPE_value, data, "CHANNEL_TYPE")
	if err != nil {
		return err
	}
	*x = CHANNEL_TYPE(value)
	return nil
}
func (CHANNEL_TYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorKfkSimpleChannel, []int{1} }

type ChSimpleEvent struct {
	Uid         uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ChId        uint32 `protobuf:"varint,2,req,name=ch_id,json=chId" json:"ch_id"`
	EventType   uint32 `protobuf:"varint,3,req,name=event_type,json=eventType" json:"event_type"`
	ChannelType uint32 `protobuf:"varint,4,opt,name=channel_type,json=channelType" json:"channel_type"`
	OptPbInfo   []byte `protobuf:"bytes,5,opt,name=opt_pb_info,json=optPbInfo" json:"opt_pb_info"`
}

func (m *ChSimpleEvent) Reset()                    { *m = ChSimpleEvent{} }
func (m *ChSimpleEvent) String() string            { return proto.CompactTextString(m) }
func (*ChSimpleEvent) ProtoMessage()               {}
func (*ChSimpleEvent) Descriptor() ([]byte, []int) { return fileDescriptorKfkSimpleChannel, []int{0} }

func (m *ChSimpleEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChSimpleEvent) GetChId() uint32 {
	if m != nil {
		return m.ChId
	}
	return 0
}

func (m *ChSimpleEvent) GetEventType() uint32 {
	if m != nil {
		return m.EventType
	}
	return 0
}

func (m *ChSimpleEvent) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *ChSimpleEvent) GetOptPbInfo() []byte {
	if m != nil {
		return m.OptPbInfo
	}
	return nil
}

type ChSimpleLeaveOpt struct {
	TsMs             uint64 `protobuf:"varint,1,opt,name=ts_ms,json=tsMs" json:"ts_ms"`
	RemainMembercnt  uint32 `protobuf:"varint,2,opt,name=remain_membercnt,json=remainMembercnt" json:"remain_membercnt"`
	RemainAdmincnt   uint32 `protobuf:"varint,3,opt,name=remain_admincnt,json=remainAdmincnt" json:"remain_admincnt"`
	OnlineSecond     uint32 `protobuf:"varint,4,opt,name=online_second,json=onlineSecond" json:"online_second"`
	OpUid            uint32 `protobuf:"varint,5,opt,name=op_uid,json=opUid" json:"op_uid"`
	CreaterUid       uint32 `protobuf:"varint,6,opt,name=creater_uid,json=createrUid" json:"creater_uid"`
	ChannelDisplayId uint32 `protobuf:"varint,7,opt,name=channel_display_id,json=channelDisplayId" json:"channel_display_id"`
	ChannelViewId    string `protobuf:"bytes,8,opt,name=channel_view_id,json=channelViewId" json:"channel_view_id"`
	LastEnterTs      uint32 `protobuf:"varint,9,opt,name=last_enter_ts,json=lastEnterTs" json:"last_enter_ts"`
	AppId            uint32 `protobuf:"varint,10,opt,name=app_id,json=appId" json:"app_id"`
	MarketId         uint32 `protobuf:"varint,11,opt,name=market_id,json=marketId" json:"market_id"`
}

func (m *ChSimpleLeaveOpt) Reset()                    { *m = ChSimpleLeaveOpt{} }
func (m *ChSimpleLeaveOpt) String() string            { return proto.CompactTextString(m) }
func (*ChSimpleLeaveOpt) ProtoMessage()               {}
func (*ChSimpleLeaveOpt) Descriptor() ([]byte, []int) { return fileDescriptorKfkSimpleChannel, []int{1} }

func (m *ChSimpleLeaveOpt) GetTsMs() uint64 {
	if m != nil {
		return m.TsMs
	}
	return 0
}

func (m *ChSimpleLeaveOpt) GetRemainMembercnt() uint32 {
	if m != nil {
		return m.RemainMembercnt
	}
	return 0
}

func (m *ChSimpleLeaveOpt) GetRemainAdmincnt() uint32 {
	if m != nil {
		return m.RemainAdmincnt
	}
	return 0
}

func (m *ChSimpleLeaveOpt) GetOnlineSecond() uint32 {
	if m != nil {
		return m.OnlineSecond
	}
	return 0
}

func (m *ChSimpleLeaveOpt) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *ChSimpleLeaveOpt) GetCreaterUid() uint32 {
	if m != nil {
		return m.CreaterUid
	}
	return 0
}

func (m *ChSimpleLeaveOpt) GetChannelDisplayId() uint32 {
	if m != nil {
		return m.ChannelDisplayId
	}
	return 0
}

func (m *ChSimpleLeaveOpt) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

func (m *ChSimpleLeaveOpt) GetLastEnterTs() uint32 {
	if m != nil {
		return m.LastEnterTs
	}
	return 0
}

func (m *ChSimpleLeaveOpt) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *ChSimpleLeaveOpt) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

type ChSimpleEnterOpt struct {
	TsMs             uint64 `protobuf:"varint,1,opt,name=ts_ms,json=tsMs" json:"ts_ms"`
	IsPwd            bool   `protobuf:"varint,2,opt,name=is_pwd,json=isPwd" json:"is_pwd"`
	RemainMembercnt  uint32 `protobuf:"varint,3,opt,name=remain_membercnt,json=remainMembercnt" json:"remain_membercnt"`
	RemainAdmincnt   uint32 `protobuf:"varint,4,opt,name=remain_admincnt,json=remainAdmincnt" json:"remain_admincnt"`
	NobilityLevel    uint32 `protobuf:"varint,5,opt,name=nobility_level,json=nobilityLevel" json:"nobility_level"`
	Invisible        bool   `protobuf:"varint,6,opt,name=invisible" json:"invisible"`
	Source           uint32 `protobuf:"varint,7,opt,name=source" json:"source"`
	FollowFriendUid  uint32 `protobuf:"varint,8,opt,name=follow_friend_uid,json=followFriendUid" json:"follow_friend_uid"`
	LastCid          uint32 `protobuf:"varint,9,opt,name=last_cid,json=lastCid" json:"last_cid"`
	ChannelDisplayId uint32 `protobuf:"varint,10,opt,name=channel_display_id,json=channelDisplayId" json:"channel_display_id"`
	ChannelViewId    string `protobuf:"bytes,11,opt,name=channel_view_id,json=channelViewId" json:"channel_view_id"`
	AppId            uint32 `protobuf:"varint,12,opt,name=app_id,json=appId" json:"app_id"`
	MarketId         uint32 `protobuf:"varint,13,opt,name=market_id,json=marketId" json:"market_id"`
}

func (m *ChSimpleEnterOpt) Reset()                    { *m = ChSimpleEnterOpt{} }
func (m *ChSimpleEnterOpt) String() string            { return proto.CompactTextString(m) }
func (*ChSimpleEnterOpt) ProtoMessage()               {}
func (*ChSimpleEnterOpt) Descriptor() ([]byte, []int) { return fileDescriptorKfkSimpleChannel, []int{2} }

func (m *ChSimpleEnterOpt) GetTsMs() uint64 {
	if m != nil {
		return m.TsMs
	}
	return 0
}

func (m *ChSimpleEnterOpt) GetIsPwd() bool {
	if m != nil {
		return m.IsPwd
	}
	return false
}

func (m *ChSimpleEnterOpt) GetRemainMembercnt() uint32 {
	if m != nil {
		return m.RemainMembercnt
	}
	return 0
}

func (m *ChSimpleEnterOpt) GetRemainAdmincnt() uint32 {
	if m != nil {
		return m.RemainAdmincnt
	}
	return 0
}

func (m *ChSimpleEnterOpt) GetNobilityLevel() uint32 {
	if m != nil {
		return m.NobilityLevel
	}
	return 0
}

func (m *ChSimpleEnterOpt) GetInvisible() bool {
	if m != nil {
		return m.Invisible
	}
	return false
}

func (m *ChSimpleEnterOpt) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *ChSimpleEnterOpt) GetFollowFriendUid() uint32 {
	if m != nil {
		return m.FollowFriendUid
	}
	return 0
}

func (m *ChSimpleEnterOpt) GetLastCid() uint32 {
	if m != nil {
		return m.LastCid
	}
	return 0
}

func (m *ChSimpleEnterOpt) GetChannelDisplayId() uint32 {
	if m != nil {
		return m.ChannelDisplayId
	}
	return 0
}

func (m *ChSimpleEnterOpt) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

func (m *ChSimpleEnterOpt) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *ChSimpleEnterOpt) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

type ChSimpleExpiredNotifyOpt struct {
	ExpireTs         uint32 `protobuf:"varint,1,opt,name=expire_ts,json=expireTs" json:"expire_ts"`
	ChannelSwitch    uint32 `protobuf:"varint,2,opt,name=channel_switch,json=channelSwitch" json:"channel_switch"`
	MicId            uint32 `protobuf:"varint,3,opt,name=mic_id,json=micId" json:"mic_id"`
	ChannelCreater   uint32 `protobuf:"varint,4,opt,name=channel_creater,json=channelCreater" json:"channel_creater"`
	ChannelDisplayId uint32 `protobuf:"varint,5,opt,name=channel_display_id,json=channelDisplayId" json:"channel_display_id"`
	ChannelBindId    uint32 `protobuf:"varint,6,opt,name=channel_bind_id,json=channelBindId" json:"channel_bind_id"`
}

func (m *ChSimpleExpiredNotifyOpt) Reset()         { *m = ChSimpleExpiredNotifyOpt{} }
func (m *ChSimpleExpiredNotifyOpt) String() string { return proto.CompactTextString(m) }
func (*ChSimpleExpiredNotifyOpt) ProtoMessage()    {}
func (*ChSimpleExpiredNotifyOpt) Descriptor() ([]byte, []int) {
	return fileDescriptorKfkSimpleChannel, []int{3}
}

func (m *ChSimpleExpiredNotifyOpt) GetExpireTs() uint32 {
	if m != nil {
		return m.ExpireTs
	}
	return 0
}

func (m *ChSimpleExpiredNotifyOpt) GetChannelSwitch() uint32 {
	if m != nil {
		return m.ChannelSwitch
	}
	return 0
}

func (m *ChSimpleExpiredNotifyOpt) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *ChSimpleExpiredNotifyOpt) GetChannelCreater() uint32 {
	if m != nil {
		return m.ChannelCreater
	}
	return 0
}

func (m *ChSimpleExpiredNotifyOpt) GetChannelDisplayId() uint32 {
	if m != nil {
		return m.ChannelDisplayId
	}
	return 0
}

func (m *ChSimpleExpiredNotifyOpt) GetChannelBindId() uint32 {
	if m != nil {
		return m.ChannelBindId
	}
	return 0
}

type ChSimpleAdminUpdateOpt struct {
	TargetUid uint32 `protobuf:"varint,1,req,name=target_uid,json=targetUid" json:"target_uid"`
	OpType    uint32 `protobuf:"varint,2,req,name=op_type,json=opType" json:"op_type"`
	AdminRole uint32 `protobuf:"varint,3,req,name=admin_role,json=adminRole" json:"admin_role"`
	TsMs      uint32 `protobuf:"varint,4,opt,name=ts_ms,json=tsMs" json:"ts_ms"`
}

func (m *ChSimpleAdminUpdateOpt) Reset()         { *m = ChSimpleAdminUpdateOpt{} }
func (m *ChSimpleAdminUpdateOpt) String() string { return proto.CompactTextString(m) }
func (*ChSimpleAdminUpdateOpt) ProtoMessage()    {}
func (*ChSimpleAdminUpdateOpt) Descriptor() ([]byte, []int) {
	return fileDescriptorKfkSimpleChannel, []int{4}
}

func (m *ChSimpleAdminUpdateOpt) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *ChSimpleAdminUpdateOpt) GetOpType() uint32 {
	if m != nil {
		return m.OpType
	}
	return 0
}

func (m *ChSimpleAdminUpdateOpt) GetAdminRole() uint32 {
	if m != nil {
		return m.AdminRole
	}
	return 0
}

func (m *ChSimpleAdminUpdateOpt) GetTsMs() uint32 {
	if m != nil {
		return m.TsMs
	}
	return 0
}

func init() {
	proto.RegisterType((*ChSimpleEvent)(nil), "kafka_simple_ch.ChSimpleEvent")
	proto.RegisterType((*ChSimpleLeaveOpt)(nil), "kafka_simple_ch.ChSimpleLeaveOpt")
	proto.RegisterType((*ChSimpleEnterOpt)(nil), "kafka_simple_ch.ChSimpleEnterOpt")
	proto.RegisterType((*ChSimpleExpiredNotifyOpt)(nil), "kafka_simple_ch.ChSimpleExpiredNotifyOpt")
	proto.RegisterType((*ChSimpleAdminUpdateOpt)(nil), "kafka_simple_ch.ChSimpleAdminUpdateOpt")
	proto.RegisterEnum("kafka_simple_ch.ESIMPLE_EVENT_TYPE", ESIMPLE_EVENT_TYPE_name, ESIMPLE_EVENT_TYPE_value)
	proto.RegisterEnum("kafka_simple_ch.CHANNEL_TYPE", CHANNEL_TYPE_name, CHANNEL_TYPE_value)
}
func (m *ChSimpleEvent) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChSimpleEvent) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(m.ChId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(m.EventType))
	dAtA[i] = 0x20
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(m.ChannelType))
	if m.OptPbInfo != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(len(m.OptPbInfo)))
		i += copy(dAtA[i:], m.OptPbInfo)
	}
	return i, nil
}

func (m *ChSimpleLeaveOpt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChSimpleLeaveOpt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(m.TsMs))
	dAtA[i] = 0x10
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(m.RemainMembercnt))
	dAtA[i] = 0x18
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(m.RemainAdmincnt))
	dAtA[i] = 0x20
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(m.OnlineSecond))
	dAtA[i] = 0x28
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(m.OpUid))
	dAtA[i] = 0x30
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(m.CreaterUid))
	dAtA[i] = 0x38
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(m.ChannelDisplayId))
	dAtA[i] = 0x42
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(len(m.ChannelViewId)))
	i += copy(dAtA[i:], m.ChannelViewId)
	dAtA[i] = 0x48
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(m.LastEnterTs))
	dAtA[i] = 0x50
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(m.AppId))
	dAtA[i] = 0x58
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(m.MarketId))
	return i, nil
}

func (m *ChSimpleEnterOpt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChSimpleEnterOpt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(m.TsMs))
	dAtA[i] = 0x10
	i++
	if m.IsPwd {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x18
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(m.RemainMembercnt))
	dAtA[i] = 0x20
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(m.RemainAdmincnt))
	dAtA[i] = 0x28
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(m.NobilityLevel))
	dAtA[i] = 0x30
	i++
	if m.Invisible {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x38
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(m.Source))
	dAtA[i] = 0x40
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(m.FollowFriendUid))
	dAtA[i] = 0x48
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(m.LastCid))
	dAtA[i] = 0x50
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(m.ChannelDisplayId))
	dAtA[i] = 0x5a
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(len(m.ChannelViewId)))
	i += copy(dAtA[i:], m.ChannelViewId)
	dAtA[i] = 0x60
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(m.AppId))
	dAtA[i] = 0x68
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(m.MarketId))
	return i, nil
}

func (m *ChSimpleExpiredNotifyOpt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChSimpleExpiredNotifyOpt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(m.ExpireTs))
	dAtA[i] = 0x10
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(m.ChannelSwitch))
	dAtA[i] = 0x18
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(m.MicId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(m.ChannelCreater))
	dAtA[i] = 0x28
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(m.ChannelDisplayId))
	dAtA[i] = 0x30
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(m.ChannelBindId))
	return i, nil
}

func (m *ChSimpleAdminUpdateOpt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChSimpleAdminUpdateOpt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(m.TargetUid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(m.OpType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(m.AdminRole))
	dAtA[i] = 0x20
	i++
	i = encodeVarintKfkSimpleChannel(dAtA, i, uint64(m.TsMs))
	return i, nil
}

func encodeFixed64KfkSimpleChannel(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32KfkSimpleChannel(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintKfkSimpleChannel(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *ChSimpleEvent) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkSimpleChannel(uint64(m.Uid))
	n += 1 + sovKfkSimpleChannel(uint64(m.ChId))
	n += 1 + sovKfkSimpleChannel(uint64(m.EventType))
	n += 1 + sovKfkSimpleChannel(uint64(m.ChannelType))
	if m.OptPbInfo != nil {
		l = len(m.OptPbInfo)
		n += 1 + l + sovKfkSimpleChannel(uint64(l))
	}
	return n
}

func (m *ChSimpleLeaveOpt) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkSimpleChannel(uint64(m.TsMs))
	n += 1 + sovKfkSimpleChannel(uint64(m.RemainMembercnt))
	n += 1 + sovKfkSimpleChannel(uint64(m.RemainAdmincnt))
	n += 1 + sovKfkSimpleChannel(uint64(m.OnlineSecond))
	n += 1 + sovKfkSimpleChannel(uint64(m.OpUid))
	n += 1 + sovKfkSimpleChannel(uint64(m.CreaterUid))
	n += 1 + sovKfkSimpleChannel(uint64(m.ChannelDisplayId))
	l = len(m.ChannelViewId)
	n += 1 + l + sovKfkSimpleChannel(uint64(l))
	n += 1 + sovKfkSimpleChannel(uint64(m.LastEnterTs))
	n += 1 + sovKfkSimpleChannel(uint64(m.AppId))
	n += 1 + sovKfkSimpleChannel(uint64(m.MarketId))
	return n
}

func (m *ChSimpleEnterOpt) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkSimpleChannel(uint64(m.TsMs))
	n += 2
	n += 1 + sovKfkSimpleChannel(uint64(m.RemainMembercnt))
	n += 1 + sovKfkSimpleChannel(uint64(m.RemainAdmincnt))
	n += 1 + sovKfkSimpleChannel(uint64(m.NobilityLevel))
	n += 2
	n += 1 + sovKfkSimpleChannel(uint64(m.Source))
	n += 1 + sovKfkSimpleChannel(uint64(m.FollowFriendUid))
	n += 1 + sovKfkSimpleChannel(uint64(m.LastCid))
	n += 1 + sovKfkSimpleChannel(uint64(m.ChannelDisplayId))
	l = len(m.ChannelViewId)
	n += 1 + l + sovKfkSimpleChannel(uint64(l))
	n += 1 + sovKfkSimpleChannel(uint64(m.AppId))
	n += 1 + sovKfkSimpleChannel(uint64(m.MarketId))
	return n
}

func (m *ChSimpleExpiredNotifyOpt) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkSimpleChannel(uint64(m.ExpireTs))
	n += 1 + sovKfkSimpleChannel(uint64(m.ChannelSwitch))
	n += 1 + sovKfkSimpleChannel(uint64(m.MicId))
	n += 1 + sovKfkSimpleChannel(uint64(m.ChannelCreater))
	n += 1 + sovKfkSimpleChannel(uint64(m.ChannelDisplayId))
	n += 1 + sovKfkSimpleChannel(uint64(m.ChannelBindId))
	return n
}

func (m *ChSimpleAdminUpdateOpt) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkSimpleChannel(uint64(m.TargetUid))
	n += 1 + sovKfkSimpleChannel(uint64(m.OpType))
	n += 1 + sovKfkSimpleChannel(uint64(m.AdminRole))
	n += 1 + sovKfkSimpleChannel(uint64(m.TsMs))
	return n
}

func sovKfkSimpleChannel(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozKfkSimpleChannel(x uint64) (n int) {
	return sovKfkSimpleChannel(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *ChSimpleEvent) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkSimpleChannel
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: ChSimpleEvent: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: ChSimpleEvent: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChId", wireType)
			}
			m.ChId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field EventType", wireType)
			}
			m.EventType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EventType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OptPbInfo", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthKfkSimpleChannel
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OptPbInfo = append(m.OptPbInfo[:0], dAtA[iNdEx:postIndex]...)
			if m.OptPbInfo == nil {
				m.OptPbInfo = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipKfkSimpleChannel(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkSimpleChannel
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("ch_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("event_type")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChSimpleLeaveOpt) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkSimpleChannel
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: ChSimpleLeaveOpt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: ChSimpleLeaveOpt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field TsMs", wireType)
			}
			m.TsMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TsMs |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field RemainMembercnt", wireType)
			}
			m.RemainMembercnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RemainMembercnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field RemainAdmincnt", wireType)
			}
			m.RemainAdmincnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RemainAdmincnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OnlineSecond", wireType)
			}
			m.OnlineSecond = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OnlineSecond |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field CreaterUid", wireType)
			}
			m.CreaterUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreaterUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelDisplayId", wireType)
			}
			m.ChannelDisplayId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelDisplayId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelViewId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkSimpleChannel
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ChannelViewId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field LastEnterTs", wireType)
			}
			m.LastEnterTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastEnterTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field MarketId", wireType)
			}
			m.MarketId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MarketId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipKfkSimpleChannel(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkSimpleChannel
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChSimpleEnterOpt) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkSimpleChannel
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: ChSimpleEnterOpt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: ChSimpleEnterOpt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field TsMs", wireType)
			}
			m.TsMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TsMs |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field IsPwd", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsPwd = bool(v != 0)
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field RemainMembercnt", wireType)
			}
			m.RemainMembercnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RemainMembercnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field RemainAdmincnt", wireType)
			}
			m.RemainAdmincnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RemainAdmincnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field NobilityLevel", wireType)
			}
			m.NobilityLevel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NobilityLevel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Invisible", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Invisible = bool(v != 0)
		case 7:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			m.Source = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Source |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field FollowFriendUid", wireType)
			}
			m.FollowFriendUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FollowFriendUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field LastCid", wireType)
			}
			m.LastCid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastCid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelDisplayId", wireType)
			}
			m.ChannelDisplayId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelDisplayId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelViewId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkSimpleChannel
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ChannelViewId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field MarketId", wireType)
			}
			m.MarketId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MarketId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipKfkSimpleChannel(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkSimpleChannel
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChSimpleExpiredNotifyOpt) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkSimpleChannel
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: ChSimpleExpiredNotifyOpt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: ChSimpleExpiredNotifyOpt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ExpireTs", wireType)
			}
			m.ExpireTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpireTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelSwitch", wireType)
			}
			m.ChannelSwitch = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelSwitch |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field MicId", wireType)
			}
			m.MicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelCreater", wireType)
			}
			m.ChannelCreater = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelCreater |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelDisplayId", wireType)
			}
			m.ChannelDisplayId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelDisplayId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelBindId", wireType)
			}
			m.ChannelBindId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelBindId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipKfkSimpleChannel(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkSimpleChannel
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChSimpleAdminUpdateOpt) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkSimpleChannel
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: ChSimpleAdminUpdateOpt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: ChSimpleAdminUpdateOpt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OpType", wireType)
			}
			m.OpType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field AdminRole", wireType)
			}
			m.AdminRole = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AdminRole |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field TsMs", wireType)
			}
			m.TsMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TsMs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipKfkSimpleChannel(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkSimpleChannel
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("target_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("op_type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("admin_role")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipKfkSimpleChannel(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowKfkSimpleChannel
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowKfkSimpleChannel
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthKfkSimpleChannel
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowKfkSimpleChannel
					}
					if iNdEx >= l {
						return 0, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipKfkSimpleChannel(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt1.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthKfkSimpleChannel = fmt1.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowKfkSimpleChannel   = fmt1.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("src/minToolkit/kafka/pb/kfk_simple_channel.proto", fileDescriptorKfkSimpleChannel)
}

var fileDescriptorKfkSimpleChannel = []byte{
	// 964 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x94, 0xdd, 0x6e, 0xe3, 0x44,
	0x1c, 0xc5, 0xd7, 0xf9, 0x6a, 0xf2, 0x4f, 0xb2, 0x75, 0x47, 0x50, 0xb2, 0x74, 0xb7, 0xed, 0xb6,
	0x20, 0xca, 0xf2, 0xd1, 0x15, 0x6f, 0x90, 0xa6, 0x2e, 0x6b, 0x29, 0x71, 0x4c, 0xea, 0x54, 0xf4,
	0x6a, 0xe4, 0x78, 0x26, 0x74, 0x14, 0xdb, 0x33, 0xb2, 0x67, 0x1b, 0xfa, 0x12, 0x88, 0x4b, 0x84,
	0x78, 0x0e, 0x9e, 0xa1, 0x97, 0x3c, 0x01, 0x42, 0xe5, 0x92, 0x97, 0x40, 0x33, 0xb6, 0x5b, 0xa7,
	0x6a, 0x59, 0xf6, 0x32, 0xe7, 0x77, 0x3c, 0xf9, 0xcf, 0x9c, 0x33, 0x03, 0xaf, 0xd3, 0x24, 0x38,
	0x8c, 0x58, 0xec, 0x71, 0x1e, 0x2e, 0x98, 0x3c, 0x5c, 0xf8, 0xf3, 0x85, 0x7f, 0x28, 0x66, 0x87,
	0x8b, 0xf9, 0x02, 0xa7, 0x2c, 0x12, 0x21, 0xc5, 0xc1, 0x85, 0x1f, 0xc7, 0x34, 0xfc, 0x5a, 0x24,
	0x5c, 0x72, 0xb4, 0xae, 0x2d, 0x77, 0x6c, 0xef, 0x77, 0x03, 0xba, 0x83, 0x8b, 0x53, 0xfd, 0xdb,
	0xba, 0xa4, 0xb1, 0x44, 0x9b, 0x50, 0x7d, 0xcb, 0x48, 0xcf, 0xd8, 0xad, 0x1c, 0x74, 0x8f, 0x6a,
	0xd7, 0x7f, 0xee, 0x3c, 0x99, 0x28, 0x01, 0x3d, 0x83, 0x7a, 0x70, 0x81, 0x19, 0xe9, 0x55, 0x4a,
	0xa4, 0x16, 0x5c, 0xd8, 0x04, 0xed, 0x03, 0x50, 0xf5, 0x2d, 0x96, 0x57, 0x82, 0xf6, 0xaa, 0x25,
	0xde, 0xd2, 0xba, 0x77, 0x25, 0x28, 0xfa, 0x0c, 0x3a, 0xf9, 0x2c, 0x99, 0xad, 0xb6, 0x6b, 0xdc,
	0xda, 0xda, 0x39, 0xd1, 0xc6, 0x4f, 0xa0, 0xcd, 0x85, 0xc4, 0x62, 0x86, 0x59, 0x3c, 0xe7, 0xbd,
	0xfa, 0xae, 0x71, 0xd0, 0x29, 0x96, 0xe3, 0x42, 0xba, 0x33, 0x3b, 0x9e, 0xf3, 0xbd, 0xeb, 0x2a,
	0x98, 0xc5, 0xe0, 0x43, 0xea, 0x5f, 0xd2, 0xb1, 0x90, 0x6a, 0x46, 0x99, 0xe2, 0x28, 0xed, 0x19,
	0xbb, 0xc6, 0x41, 0xad, 0x98, 0x51, 0xa6, 0xa3, 0x14, 0x1d, 0x82, 0x99, 0xd0, 0xc8, 0x67, 0x31,
	0x8e, 0x68, 0x34, 0xa3, 0x49, 0x10, 0xcb, 0x5e, 0xa5, 0x34, 0xc2, 0x7a, 0x46, 0x47, 0x05, 0x44,
	0x5f, 0x41, 0x2e, 0x61, 0x9f, 0x44, 0x2c, 0x56, 0xfe, 0x6a, 0xc9, 0xff, 0x34, 0x83, 0xfd, 0x9c,
	0xa1, 0xcf, 0xa1, 0xcb, 0xe3, 0x90, 0xc5, 0x14, 0xa7, 0x34, 0xe0, 0x31, 0x59, 0xd9, 0x5f, 0x27,
	0x43, 0xa7, 0x9a, 0xa0, 0x2d, 0x68, 0x70, 0x81, 0xd5, 0x21, 0xd7, 0x4b, 0x9e, 0x3a, 0x17, 0x53,
	0x46, 0xd0, 0xa7, 0xd0, 0x0e, 0x12, 0xea, 0x4b, 0x9a, 0x68, 0x47, 0xa3, 0xe4, 0x80, 0x1c, 0x28,
	0xdb, 0x37, 0x80, 0x8a, 0xd3, 0x24, 0x2c, 0x15, 0xa1, 0x7f, 0xa5, 0xa2, 0x59, 0x2b, 0xb9, 0xcd,
	0x9c, 0x1f, 0x67, 0xd8, 0x26, 0xe8, 0x4b, 0x58, 0x2f, 0xbe, 0xb9, 0x64, 0x74, 0xa9, 0x3e, 0x68,
	0xee, 0x1a, 0x07, 0xad, 0xfc, 0x83, 0x6e, 0x0e, 0xcf, 0x18, 0x5d, 0xda, 0x04, 0x1d, 0x40, 0x37,
	0xf4, 0x53, 0x89, 0x69, 0xac, 0x66, 0x91, 0x69, 0xaf, 0x55, 0x0e, 0x4c, 0x21, 0x4b, 0x11, 0x2f,
	0x55, 0xfb, 0xf1, 0x85, 0x50, 0xcb, 0x41, 0x79, 0x3f, 0xbe, 0x10, 0x36, 0x41, 0x2f, 0xa1, 0x15,
	0xf9, 0xc9, 0x82, 0x4a, 0xc5, 0xdb, 0x25, 0xde, 0xcc, 0x64, 0x9b, 0xec, 0xfd, 0x54, 0xbb, 0x8b,
	0x52, 0xaf, 0xf9, 0x8e, 0x28, 0xb7, 0xa0, 0xc1, 0x52, 0x2c, 0x96, 0x44, 0x07, 0xd8, 0x2c, 0xfe,
	0x8f, 0xa5, 0xee, 0x92, 0x3c, 0x98, 0x73, 0xf5, 0x3d, 0x73, 0xae, 0xfd, 0x47, 0xce, 0x5f, 0xc0,
	0xd3, 0x98, 0xcf, 0x58, 0xc8, 0xe4, 0x15, 0x0e, 0xe9, 0x25, 0x0d, 0x57, 0x42, 0xec, 0x16, 0x6c,
	0xa8, 0x10, 0xda, 0x83, 0x16, 0x8b, 0x2f, 0x59, 0xca, 0x66, 0x21, 0xd5, 0x51, 0x16, 0xc3, 0xde,
	0xc9, 0xe8, 0x39, 0x34, 0x52, 0xfe, 0x36, 0x09, 0xe8, 0x4a, 0x7a, 0xb9, 0x86, 0x5e, 0xc3, 0xc6,
	0x9c, 0x87, 0x21, 0x5f, 0xe2, 0x79, 0xc2, 0x68, 0x4c, 0x74, 0x29, 0x9a, 0xe5, 0xfd, 0x64, 0xf8,
	0x44, 0x53, 0xd5, 0x8c, 0x1d, 0x68, 0xea, 0xdc, 0x02, 0x46, 0x56, 0x22, 0x5b, 0x53, 0xea, 0xe0,
	0xd1, 0xea, 0xc0, 0xfb, 0x56, 0xa7, 0xfd, 0x78, 0x75, 0xee, 0x0a, 0xd1, 0x79, 0x47, 0x21, 0xba,
	0x0f, 0x16, 0xe2, 0x97, 0x0a, 0xf4, 0x6e, 0x0b, 0xf1, 0xa3, 0x60, 0x09, 0x25, 0x0e, 0x97, 0x6c,
	0x7e, 0xa5, 0x8a, 0xf1, 0x12, 0x5a, 0x54, 0x6b, 0xaa, 0x93, 0x46, 0xf9, 0xfb, 0x4c, 0xf6, 0x52,
	0x95, 0x51, 0x31, 0x6d, 0xba, 0x64, 0x32, 0xb8, 0x58, 0xb9, 0xe9, 0xc5, 0xb0, 0xa7, 0x1a, 0xa9,
	0x61, 0x23, 0x16, 0xa8, 0x61, 0xca, 0x35, 0xa9, 0x47, 0x2c, 0xb0, 0x89, 0x2a, 0x47, 0xb1, 0x52,
	0x7e, 0xf9, 0x56, 0xcb, 0x91, 0xc3, 0x41, 0xc6, 0x1e, 0x39, 0xda, 0xfa, 0xff, 0x3d, 0xda, 0x19,
	0x8b, 0x09, 0xbe, 0x77, 0xe9, 0x8b, 0x69, 0x8f, 0x58, 0x4c, 0x6c, 0xb2, 0xf7, 0x9b, 0x01, 0x9b,
	0xc5, 0xd1, 0xe8, 0x4e, 0x4e, 0x05, 0xf1, 0xa5, 0x7e, 0xfc, 0xf6, 0x01, 0xa4, 0x9f, 0xfc, 0x40,
	0x25, 0xbe, 0xff, 0x7e, 0xb7, 0x32, 0x5d, 0xb5, 0xe3, 0x05, 0xac, 0x71, 0x91, 0x3d, 0xc0, 0xe5,
	0x77, 0xbc, 0xc1, 0x85, 0x7e, 0x7b, 0xf7, 0x01, 0xf4, 0x2d, 0xc0, 0x09, 0x0f, 0xef, 0xbd, 0xe4,
	0x5a, 0x9f, 0xf0, 0x90, 0xde, 0x5d, 0xcd, 0xf2, 0x51, 0xe8, 0xab, 0xf9, 0xea, 0x1f, 0x03, 0x90,
	0x75, 0x6a, 0x8f, 0xdc, 0xa1, 0x85, 0xad, 0x33, 0xcb, 0xf1, 0xb0, 0x77, 0xee, 0x5a, 0xe8, 0x43,
	0xd8, 0xb0, 0x9c, 0xe9, 0x08, 0x17, 0xc4, 0xf1, 0xac, 0x89, 0x69, 0xdc, 0x97, 0x87, 0x56, 0xff,
	0xcc, 0x32, 0x2b, 0x68, 0x0b, 0x3e, 0x5a, 0x71, 0x7f, 0xef, 0xda, 0x13, 0x0b, 0x7f, 0x37, 0xb5,
	0x3d, 0xb3, 0x8a, 0x5e, 0xc0, 0xb3, 0x07, 0xa0, 0x33, 0xf6, 0xec, 0x93, 0x73, 0xb3, 0x86, 0x9e,
	0x43, 0xaf, 0x8c, 0xfb, 0xc7, 0x23, 0xdb, 0xc1, 0x53, 0xf7, 0xb8, 0xef, 0x59, 0x66, 0x1d, 0x7d,
	0x00, 0x66, 0x99, 0x8e, 0xa6, 0x9e, 0x65, 0x36, 0xd0, 0x26, 0xa0, 0xb2, 0x3a, 0x75, 0xb4, 0xbe,
	0x86, 0x76, 0x60, 0xab, 0xac, 0xbb, 0x83, 0x37, 0xd6, 0xd0, 0xb5, 0x26, 0xc5, 0x9f, 0x35, 0x5f,
	0xfd, 0x6a, 0x40, 0x67, 0xf0, 0xa6, 0xef, 0x38, 0xd6, 0x30, 0xdb, 0x67, 0x1b, 0xd6, 0x8e, 0xad,
	0x93, 0xfe, 0x74, 0xe8, 0x99, 0x4f, 0xd4, 0xa4, 0x83, 0xf1, 0x68, 0x34, 0x76, 0xf0, 0xb7, 0xfd,
	0x91, 0x5a, 0xd6, 0x1e, 0x3b, 0x38, 0xb7, 0x9b, 0x06, 0x32, 0xa1, 0x33, 0xb4, 0xcf, 0xac, 0x5b,
	0xa5, 0x82, 0x36, 0xa0, 0xeb, 0x8d, 0x5d, 0x7b, 0x70, 0x2b, 0xe9, 0xdd, 0xba, 0xd3, 0xa3, 0xa1,
	0x3d, 0x78, 0x68, 0x8d, 0x1a, 0xfa, 0x18, 0x36, 0x3d, 0x6b, 0xe4, 0x8e, 0x27, 0xfd, 0xc9, 0x79,
	0xe6, 0x28, 0x58, 0xe3, 0xc8, 0xbc, 0xbe, 0xd9, 0x36, 0xfe, 0xb8, 0xd9, 0x36, 0xfe, 0xba, 0xd9,
	0x36, 0x7e, 0xfe, 0x7b, 0xfb, 0xc9, 0xbf, 0x01, 0x00, 0x00, 0xff, 0xff, 0xb0, 0x3d, 0x52, 0x05,
	0x2f, 0x08, 0x00, 0x00,
}
