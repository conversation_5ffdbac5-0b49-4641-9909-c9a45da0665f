// Code generated by protoc-gen-gogo.
// source: src/minToolkit/kafka/pb/kfk_footmark.proto
// DO NOT EDIT!

/*
	Package kafkafootmark is a generated protocol buffer package.

	It is generated from these files:
		src/minToolkit/kafka/pb/kfk_footmark.proto

	It has these top-level messages:
		FootmarkRec
		UserLoginData
		ChannelMemberEnterData
		ChannelMemberLeaveData
		ChannelMemberHoldMicData
		ChannelMemberReleaseMicData
		ChannelMemberEventData
		GroupEntryEventData
		GroupChangeEventData
		FriendshipChangeData
		TextContent
		ImageContent
		VoiceContent
		IMData
		ChannelImData
		UgcUserInfo
		UgcAttachmentInfo
		UgcPostEvent
		UgcCommentEvent
		UgcEventData
*/
package kafkafootmark

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"

import io "io"
import fmt1 "fmt"
import github_com_gogo_protobuf_proto "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type ContentType int32

const (
	ContentType_CT_INVALID     ContentType = 0
	ContentType_CT_TEXT        ContentType = 1
	ContentType_CT_IMAGE       ContentType = 2
	ContentType_CT_VOICE       ContentType = 3
	ContentType_CT_TEXT_AT_ONE ContentType = 4
	ContentType_CT_TEXT_AT_ALL ContentType = 5
)

var ContentType_name = map[int32]string{
	0: "CT_INVALID",
	1: "CT_TEXT",
	2: "CT_IMAGE",
	3: "CT_VOICE",
	4: "CT_TEXT_AT_ONE",
	5: "CT_TEXT_AT_ALL",
}
var ContentType_value = map[string]int32{
	"CT_INVALID":     0,
	"CT_TEXT":        1,
	"CT_IMAGE":       2,
	"CT_VOICE":       3,
	"CT_TEXT_AT_ONE": 4,
	"CT_TEXT_AT_ALL": 5,
}

func (x ContentType) Enum() *ContentType {
	p := new(ContentType)
	*p = x
	return p
}
func (x ContentType) String() string {
	return proto.EnumName(ContentType_name, int32(x))
}
func (x *ContentType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ContentType_value, data, "ContentType")
	if err != nil {
		return err
	}
	*x = ContentType(value)
	return nil
}
func (ContentType) EnumDescriptor() ([]byte, []int) { return fileDescriptorKfkFootmark, []int{0} }

type ChannelMemberEventData_EventType int32

const (
	ChannelMemberEventData_ENTER_CHANNEL ChannelMemberEventData_EventType = 1
	ChannelMemberEventData_LEAVE_CHANNEL ChannelMemberEventData_EventType = 2
	ChannelMemberEventData_HOLD_MIC      ChannelMemberEventData_EventType = 3
	ChannelMemberEventData_RELEASE_MIC   ChannelMemberEventData_EventType = 4
)

var ChannelMemberEventData_EventType_name = map[int32]string{
	1: "ENTER_CHANNEL",
	2: "LEAVE_CHANNEL",
	3: "HOLD_MIC",
	4: "RELEASE_MIC",
}
var ChannelMemberEventData_EventType_value = map[string]int32{
	"ENTER_CHANNEL": 1,
	"LEAVE_CHANNEL": 2,
	"HOLD_MIC":      3,
	"RELEASE_MIC":   4,
}

func (x ChannelMemberEventData_EventType) Enum() *ChannelMemberEventData_EventType {
	p := new(ChannelMemberEventData_EventType)
	*p = x
	return p
}
func (x ChannelMemberEventData_EventType) String() string {
	return proto.EnumName(ChannelMemberEventData_EventType_name, int32(x))
}
func (x *ChannelMemberEventData_EventType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ChannelMemberEventData_EventType_value, data, "ChannelMemberEventData_EventType")
	if err != nil {
		return err
	}
	*x = ChannelMemberEventData_EventType(value)
	return nil
}
func (ChannelMemberEventData_EventType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorKfkFootmark, []int{6, 0}
}

type GroupEntryEventData_EntryType int32

const (
	GroupEntryEventData_JOIN_GROUP GroupEntryEventData_EntryType = 1
	GroupEntryEventData_QUIT_GROUP GroupEntryEventData_EntryType = 2
)

var GroupEntryEventData_EntryType_name = map[int32]string{
	1: "JOIN_GROUP",
	2: "QUIT_GROUP",
}
var GroupEntryEventData_EntryType_value = map[string]int32{
	"JOIN_GROUP": 1,
	"QUIT_GROUP": 2,
}

func (x GroupEntryEventData_EntryType) Enum() *GroupEntryEventData_EntryType {
	p := new(GroupEntryEventData_EntryType)
	*p = x
	return p
}
func (x GroupEntryEventData_EntryType) String() string {
	return proto.EnumName(GroupEntryEventData_EntryType_name, int32(x))
}
func (x *GroupEntryEventData_EntryType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(GroupEntryEventData_EntryType_value, data, "GroupEntryEventData_EntryType")
	if err != nil {
		return err
	}
	*x = GroupEntryEventData_EntryType(value)
	return nil
}
func (GroupEntryEventData_EntryType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorKfkFootmark, []int{7, 0}
}

type GroupChangeEventData_ChangeType int32

const (
	GroupChangeEventData_CREATE_GROUP  GroupChangeEventData_ChangeType = 1
	GroupChangeEventData_DISMISS_GROUP GroupChangeEventData_ChangeType = 2
)

var GroupChangeEventData_ChangeType_name = map[int32]string{
	1: "CREATE_GROUP",
	2: "DISMISS_GROUP",
}
var GroupChangeEventData_ChangeType_value = map[string]int32{
	"CREATE_GROUP":  1,
	"DISMISS_GROUP": 2,
}

func (x GroupChangeEventData_ChangeType) Enum() *GroupChangeEventData_ChangeType {
	p := new(GroupChangeEventData_ChangeType)
	*p = x
	return p
}
func (x GroupChangeEventData_ChangeType) String() string {
	return proto.EnumName(GroupChangeEventData_ChangeType_name, int32(x))
}
func (x *GroupChangeEventData_ChangeType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(GroupChangeEventData_ChangeType_value, data, "GroupChangeEventData_ChangeType")
	if err != nil {
		return err
	}
	*x = GroupChangeEventData_ChangeType(value)
	return nil
}
func (GroupChangeEventData_ChangeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorKfkFootmark, []int{8, 0}
}

type FriendshipChangeData_ChangeType int32

const (
	FriendshipChangeData_ADD_FRIEND FriendshipChangeData_ChangeType = 1
	FriendshipChangeData_DEL_FRIEND FriendshipChangeData_ChangeType = 2
	FriendshipChangeData_ADD_FOLLOW FriendshipChangeData_ChangeType = 3
	FriendshipChangeData_DEL_FOLLOW FriendshipChangeData_ChangeType = 4
)

var FriendshipChangeData_ChangeType_name = map[int32]string{
	1: "ADD_FRIEND",
	2: "DEL_FRIEND",
	3: "ADD_FOLLOW",
	4: "DEL_FOLLOW",
}
var FriendshipChangeData_ChangeType_value = map[string]int32{
	"ADD_FRIEND": 1,
	"DEL_FRIEND": 2,
	"ADD_FOLLOW": 3,
	"DEL_FOLLOW": 4,
}

func (x FriendshipChangeData_ChangeType) Enum() *FriendshipChangeData_ChangeType {
	p := new(FriendshipChangeData_ChangeType)
	*p = x
	return p
}
func (x FriendshipChangeData_ChangeType) String() string {
	return proto.EnumName(FriendshipChangeData_ChangeType_name, int32(x))
}
func (x *FriendshipChangeData_ChangeType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(FriendshipChangeData_ChangeType_value, data, "FriendshipChangeData_ChangeType")
	if err != nil {
		return err
	}
	*x = FriendshipChangeData_ChangeType(value)
	return nil
}
func (FriendshipChangeData_ChangeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorKfkFootmark, []int{9, 0}
}

type ImageContent_Type int32

const (
	ImageContent_RAW ImageContent_Type = 1
	ImageContent_URL ImageContent_Type = 2
	ImageContent_KEY ImageContent_Type = 3
)

var ImageContent_Type_name = map[int32]string{
	1: "RAW",
	2: "URL",
	3: "KEY",
}
var ImageContent_Type_value = map[string]int32{
	"RAW": 1,
	"URL": 2,
	"KEY": 3,
}

func (x ImageContent_Type) Enum() *ImageContent_Type {
	p := new(ImageContent_Type)
	*p = x
	return p
}
func (x ImageContent_Type) String() string {
	return proto.EnumName(ImageContent_Type_name, int32(x))
}
func (x *ImageContent_Type) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ImageContent_Type_value, data, "ImageContent_Type")
	if err != nil {
		return err
	}
	*x = ImageContent_Type(value)
	return nil
}
func (ImageContent_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorKfkFootmark, []int{11, 0}
}

type VoiceContent_AV_CODEC int32

const (
	VoiceContent_AV_CUSTOM_OPUS VoiceContent_AV_CODEC = 1
	VoiceContent_AV_PCM         VoiceContent_AV_CODEC = 2
	VoiceContent_AV_MP3         VoiceContent_AV_CODEC = 3
)

var VoiceContent_AV_CODEC_name = map[int32]string{
	1: "AV_CUSTOM_OPUS",
	2: "AV_PCM",
	3: "AV_MP3",
}
var VoiceContent_AV_CODEC_value = map[string]int32{
	"AV_CUSTOM_OPUS": 1,
	"AV_PCM":         2,
	"AV_MP3":         3,
}

func (x VoiceContent_AV_CODEC) Enum() *VoiceContent_AV_CODEC {
	p := new(VoiceContent_AV_CODEC)
	*p = x
	return p
}
func (x VoiceContent_AV_CODEC) String() string {
	return proto.EnumName(VoiceContent_AV_CODEC_name, int32(x))
}
func (x *VoiceContent_AV_CODEC) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(VoiceContent_AV_CODEC_value, data, "VoiceContent_AV_CODEC")
	if err != nil {
		return err
	}
	*x = VoiceContent_AV_CODEC(value)
	return nil
}
func (VoiceContent_AV_CODEC) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorKfkFootmark, []int{12, 0}
}

type IMData_TargetTo int32

const (
	IMData_TO_USER  IMData_TargetTo = 1
	IMData_TO_GROUP IMData_TargetTo = 2
)

var IMData_TargetTo_name = map[int32]string{
	1: "TO_USER",
	2: "TO_GROUP",
}
var IMData_TargetTo_value = map[string]int32{
	"TO_USER":  1,
	"TO_GROUP": 2,
}

func (x IMData_TargetTo) Enum() *IMData_TargetTo {
	p := new(IMData_TargetTo)
	*p = x
	return p
}
func (x IMData_TargetTo) String() string {
	return proto.EnumName(IMData_TargetTo_name, int32(x))
}
func (x *IMData_TargetTo) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(IMData_TargetTo_value, data, "IMData_TargetTo")
	if err != nil {
		return err
	}
	*x = IMData_TargetTo(value)
	return nil
}
func (IMData_TargetTo) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorKfkFootmark, []int{13, 0}
}

type UgcAttachmentInfo_AttachmentType int32

const (
	UgcAttachmentInfo_None  UgcAttachmentInfo_AttachmentType = 0
	UgcAttachmentInfo_IMAGE UgcAttachmentInfo_AttachmentType = 1
	UgcAttachmentInfo_GIF   UgcAttachmentInfo_AttachmentType = 2
	UgcAttachmentInfo_VIDEO UgcAttachmentInfo_AttachmentType = 3
	UgcAttachmentInfo_CMS   UgcAttachmentInfo_AttachmentType = 4
)

var UgcAttachmentInfo_AttachmentType_name = map[int32]string{
	0: "None",
	1: "IMAGE",
	2: "GIF",
	3: "VIDEO",
	4: "CMS",
}
var UgcAttachmentInfo_AttachmentType_value = map[string]int32{
	"None":  0,
	"IMAGE": 1,
	"GIF":   2,
	"VIDEO": 3,
	"CMS":   4,
}

func (x UgcAttachmentInfo_AttachmentType) Enum() *UgcAttachmentInfo_AttachmentType {
	p := new(UgcAttachmentInfo_AttachmentType)
	*p = x
	return p
}
func (x UgcAttachmentInfo_AttachmentType) String() string {
	return proto.EnumName(UgcAttachmentInfo_AttachmentType_name, int32(x))
}
func (x *UgcAttachmentInfo_AttachmentType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(UgcAttachmentInfo_AttachmentType_value, data, "UgcAttachmentInfo_AttachmentType")
	if err != nil {
		return err
	}
	*x = UgcAttachmentInfo_AttachmentType(value)
	return nil
}
func (UgcAttachmentInfo_AttachmentType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorKfkFootmark, []int{16, 0}
}

type UgcPostEvent_OP_TYPE int32

const (
	UgcPostEvent_POST_OP_ADD UgcPostEvent_OP_TYPE = 1
)

var UgcPostEvent_OP_TYPE_name = map[int32]string{
	1: "POST_OP_ADD",
}
var UgcPostEvent_OP_TYPE_value = map[string]int32{
	"POST_OP_ADD": 1,
}

func (x UgcPostEvent_OP_TYPE) Enum() *UgcPostEvent_OP_TYPE {
	p := new(UgcPostEvent_OP_TYPE)
	*p = x
	return p
}
func (x UgcPostEvent_OP_TYPE) String() string {
	return proto.EnumName(UgcPostEvent_OP_TYPE_name, int32(x))
}
func (x *UgcPostEvent_OP_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(UgcPostEvent_OP_TYPE_value, data, "UgcPostEvent_OP_TYPE")
	if err != nil {
		return err
	}
	*x = UgcPostEvent_OP_TYPE(value)
	return nil
}
func (UgcPostEvent_OP_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorKfkFootmark, []int{17, 0}
}

type UgcCommentEvent_OP_TYPE int32

const (
	UgcCommentEvent_COMMENT_OP_ADD UgcCommentEvent_OP_TYPE = 1
)

var UgcCommentEvent_OP_TYPE_name = map[int32]string{
	1: "COMMENT_OP_ADD",
}
var UgcCommentEvent_OP_TYPE_value = map[string]int32{
	"COMMENT_OP_ADD": 1,
}

func (x UgcCommentEvent_OP_TYPE) Enum() *UgcCommentEvent_OP_TYPE {
	p := new(UgcCommentEvent_OP_TYPE)
	*p = x
	return p
}
func (x UgcCommentEvent_OP_TYPE) String() string {
	return proto.EnumName(UgcCommentEvent_OP_TYPE_name, int32(x))
}
func (x *UgcCommentEvent_OP_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(UgcCommentEvent_OP_TYPE_value, data, "UgcCommentEvent_OP_TYPE")
	if err != nil {
		return err
	}
	*x = UgcCommentEvent_OP_TYPE(value)
	return nil
}
func (UgcCommentEvent_OP_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorKfkFootmark, []int{18, 0}
}

type UgcEventData_EventType int32

const (
	UgcEventData_UGC_ET_POST    UgcEventData_EventType = 1
	UgcEventData_UGC_ET_COMMENT UgcEventData_EventType = 2
)

var UgcEventData_EventType_name = map[int32]string{
	1: "UGC_ET_POST",
	2: "UGC_ET_COMMENT",
}
var UgcEventData_EventType_value = map[string]int32{
	"UGC_ET_POST":    1,
	"UGC_ET_COMMENT": 2,
}

func (x UgcEventData_EventType) Enum() *UgcEventData_EventType {
	p := new(UgcEventData_EventType)
	*p = x
	return p
}
func (x UgcEventData_EventType) String() string {
	return proto.EnumName(UgcEventData_EventType_name, int32(x))
}
func (x *UgcEventData_EventType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(UgcEventData_EventType_value, data, "UgcEventData_EventType")
	if err != nil {
		return err
	}
	*x = UgcEventData_EventType(value)
	return nil
}
func (UgcEventData_EventType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorKfkFootmark, []int{19, 0}
}

type FootmarkRec struct {
	Appid   string `protobuf:"bytes,1,req,name=appid" json:"appid"`
	Id      string `protobuf:"bytes,2,req,name=id" json:"id"`
	At      uint32 `protobuf:"varint,3,req,name=at" json:"at"`
	BizType string `protobuf:"bytes,4,req,name=biz_type,json=bizType" json:"biz_type"`
	BizData []byte `protobuf:"bytes,5,req,name=biz_data,json=bizData" json:"biz_data"`
}

func (m *FootmarkRec) Reset()                    { *m = FootmarkRec{} }
func (m *FootmarkRec) String() string            { return proto.CompactTextString(m) }
func (*FootmarkRec) ProtoMessage()               {}
func (*FootmarkRec) Descriptor() ([]byte, []int) { return fileDescriptorKfkFootmark, []int{0} }

func (m *FootmarkRec) GetAppid() string {
	if m != nil {
		return m.Appid
	}
	return ""
}

func (m *FootmarkRec) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *FootmarkRec) GetAt() uint32 {
	if m != nil {
		return m.At
	}
	return 0
}

func (m *FootmarkRec) GetBizType() string {
	if m != nil {
		return m.BizType
	}
	return ""
}

func (m *FootmarkRec) GetBizData() []byte {
	if m != nil {
		return m.BizData
	}
	return nil
}

// 登录
type UserLoginData struct {
	// 账号
	Uid     uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Account string `protobuf:"bytes,2,opt,name=account" json:"account"`
	// 登录
	LoginType        string `protobuf:"bytes,3,req,name=login_type,json=loginType" json:"login_type"`
	LoginUsername    string `protobuf:"bytes,4,opt,name=login_username,json=loginUsername" json:"login_username"`
	Result           int32  `protobuf:"varint,5,req,name=result" json:"result"`
	ThirdPartyType   uint32 `protobuf:"varint,6,opt,name=third_party_type,json=thirdPartyType" json:"third_party_type"`
	ThirdPartyOpenid string `protobuf:"bytes,7,opt,name=third_party_openid,json=thirdPartyOpenid" json:"third_party_openid"`
	LoginAt          uint32 `protobuf:"varint,8,req,name=login_at,json=loginAt" json:"login_at"`
	// 设备
	Imei        string `protobuf:"bytes,9,opt,name=imei" json:"imei"`
	OsVer       string `protobuf:"bytes,10,opt,name=os_ver,json=osVer" json:"os_ver"`
	OsType      string `protobuf:"bytes,11,opt,name=os_type,json=osType" json:"os_type"`
	DeviceModel string `protobuf:"bytes,12,opt,name=device_model,json=deviceModel" json:"device_model"`
	DeviceInfo  string `protobuf:"bytes,13,opt,name=device_info,json=deviceInfo" json:"device_info"`
	IsEmulator  uint32 `protobuf:"varint,14,opt,name=is_emulator,json=isEmulator" json:"is_emulator"`
	DeviceId    string `protobuf:"bytes,15,opt,name=device_id,json=deviceId" json:"device_id"`
	// 客户端
	TerminalType     uint32 `protobuf:"varint,16,opt,name=terminal_type,json=terminalType" json:"terminal_type"`
	ClientType       string `protobuf:"bytes,17,opt,name=client_type,json=clientType" json:"client_type"`
	ClientVer        uint32 `protobuf:"varint,18,opt,name=client_ver,json=clientVer" json:"client_ver"`
	PkgSignature     string `protobuf:"bytes,19,opt,name=pkg_signature,json=pkgSignature" json:"pkg_signature"`
	ClientChannelId  string `protobuf:"bytes,20,opt,name=client_channel_id,json=clientChannelId" json:"client_channel_id"`
	ClientIp         string `protobuf:"bytes,21,opt,name=client_ip,json=clientIp" json:"client_ip"`
	ClientPort       int32  `protobuf:"varint,22,opt,name=client_port,json=clientPort" json:"client_port"`
	ClientIpLocation string `protobuf:"bytes,23,opt,name=client_ip_location,json=clientIpLocation" json:"client_ip_location"`
	Alias            string `protobuf:"bytes,24,opt,name=alias" json:"alias"`
	Phone            string `protobuf:"bytes,25,opt,name=phone" json:"phone"`
	Nickname         string `protobuf:"bytes,26,opt,name=nickname" json:"nickname"`
	Avatar           string `protobuf:"bytes,27,opt,name=avatar" json:"avatar"`
	Signature        string `protobuf:"bytes,28,opt,name=signature" json:"signature"`
	ProxyIp          string `protobuf:"bytes,29,opt,name=proxy_ip,json=proxyIp" json:"proxy_ip"`
	ProxyPort        int32  `protobuf:"varint,30,opt,name=proxy_port,json=proxyPort" json:"proxy_port"`
	ClientId         int32  `protobuf:"varint,31,opt,name=client_id,json=clientId" json:"client_id"`
	MarketId         uint32 `protobuf:"varint,32,opt,name=market_id,json=marketId" json:"market_id"`
}

func (m *UserLoginData) Reset()                    { *m = UserLoginData{} }
func (m *UserLoginData) String() string            { return proto.CompactTextString(m) }
func (*UserLoginData) ProtoMessage()               {}
func (*UserLoginData) Descriptor() ([]byte, []int) { return fileDescriptorKfkFootmark, []int{1} }

func (m *UserLoginData) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserLoginData) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserLoginData) GetLoginType() string {
	if m != nil {
		return m.LoginType
	}
	return ""
}

func (m *UserLoginData) GetLoginUsername() string {
	if m != nil {
		return m.LoginUsername
	}
	return ""
}

func (m *UserLoginData) GetResult() int32 {
	if m != nil {
		return m.Result
	}
	return 0
}

func (m *UserLoginData) GetThirdPartyType() uint32 {
	if m != nil {
		return m.ThirdPartyType
	}
	return 0
}

func (m *UserLoginData) GetThirdPartyOpenid() string {
	if m != nil {
		return m.ThirdPartyOpenid
	}
	return ""
}

func (m *UserLoginData) GetLoginAt() uint32 {
	if m != nil {
		return m.LoginAt
	}
	return 0
}

func (m *UserLoginData) GetImei() string {
	if m != nil {
		return m.Imei
	}
	return ""
}

func (m *UserLoginData) GetOsVer() string {
	if m != nil {
		return m.OsVer
	}
	return ""
}

func (m *UserLoginData) GetOsType() string {
	if m != nil {
		return m.OsType
	}
	return ""
}

func (m *UserLoginData) GetDeviceModel() string {
	if m != nil {
		return m.DeviceModel
	}
	return ""
}

func (m *UserLoginData) GetDeviceInfo() string {
	if m != nil {
		return m.DeviceInfo
	}
	return ""
}

func (m *UserLoginData) GetIsEmulator() uint32 {
	if m != nil {
		return m.IsEmulator
	}
	return 0
}

func (m *UserLoginData) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *UserLoginData) GetTerminalType() uint32 {
	if m != nil {
		return m.TerminalType
	}
	return 0
}

func (m *UserLoginData) GetClientType() string {
	if m != nil {
		return m.ClientType
	}
	return ""
}

func (m *UserLoginData) GetClientVer() uint32 {
	if m != nil {
		return m.ClientVer
	}
	return 0
}

func (m *UserLoginData) GetPkgSignature() string {
	if m != nil {
		return m.PkgSignature
	}
	return ""
}

func (m *UserLoginData) GetClientChannelId() string {
	if m != nil {
		return m.ClientChannelId
	}
	return ""
}

func (m *UserLoginData) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

func (m *UserLoginData) GetClientPort() int32 {
	if m != nil {
		return m.ClientPort
	}
	return 0
}

func (m *UserLoginData) GetClientIpLocation() string {
	if m != nil {
		return m.ClientIpLocation
	}
	return ""
}

func (m *UserLoginData) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

func (m *UserLoginData) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *UserLoginData) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserLoginData) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *UserLoginData) GetSignature() string {
	if m != nil {
		return m.Signature
	}
	return ""
}

func (m *UserLoginData) GetProxyIp() string {
	if m != nil {
		return m.ProxyIp
	}
	return ""
}

func (m *UserLoginData) GetProxyPort() int32 {
	if m != nil {
		return m.ProxyPort
	}
	return 0
}

func (m *UserLoginData) GetClientId() int32 {
	if m != nil {
		return m.ClientId
	}
	return 0
}

func (m *UserLoginData) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

// 房间事件:  进房/退房 上麦/下麦
type ChannelMemberEnterData struct {
}

func (m *ChannelMemberEnterData) Reset()         { *m = ChannelMemberEnterData{} }
func (m *ChannelMemberEnterData) String() string { return proto.CompactTextString(m) }
func (*ChannelMemberEnterData) ProtoMessage()    {}
func (*ChannelMemberEnterData) Descriptor() ([]byte, []int) {
	return fileDescriptorKfkFootmark, []int{2}
}

type ChannelMemberLeaveData struct {
	Duration uint32 `protobuf:"varint,1,req,name=duration" json:"duration"`
}

func (m *ChannelMemberLeaveData) Reset()         { *m = ChannelMemberLeaveData{} }
func (m *ChannelMemberLeaveData) String() string { return proto.CompactTextString(m) }
func (*ChannelMemberLeaveData) ProtoMessage()    {}
func (*ChannelMemberLeaveData) Descriptor() ([]byte, []int) {
	return fileDescriptorKfkFootmark, []int{3}
}

func (m *ChannelMemberLeaveData) GetDuration() uint32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

type ChannelMemberHoldMicData struct {
	MicId uint32 `protobuf:"varint,1,opt,name=mic_id,json=micId" json:"mic_id"`
}

func (m *ChannelMemberHoldMicData) Reset()         { *m = ChannelMemberHoldMicData{} }
func (m *ChannelMemberHoldMicData) String() string { return proto.CompactTextString(m) }
func (*ChannelMemberHoldMicData) ProtoMessage()    {}
func (*ChannelMemberHoldMicData) Descriptor() ([]byte, []int) {
	return fileDescriptorKfkFootmark, []int{4}
}

func (m *ChannelMemberHoldMicData) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

type ChannelMemberReleaseMicData struct {
	MicId uint32 `protobuf:"varint,1,opt,name=mic_id,json=micId" json:"mic_id"`
}

func (m *ChannelMemberReleaseMicData) Reset()         { *m = ChannelMemberReleaseMicData{} }
func (m *ChannelMemberReleaseMicData) String() string { return proto.CompactTextString(m) }
func (*ChannelMemberReleaseMicData) ProtoMessage()    {}
func (*ChannelMemberReleaseMicData) Descriptor() ([]byte, []int) {
	return fileDescriptorKfkFootmark, []int{5}
}

func (m *ChannelMemberReleaseMicData) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

type ChannelMemberEventData struct {
	ChannelId        uint32                       `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	Appid            uint32                       `protobuf:"varint,2,req,name=appid" json:"appid"`
	Uid              uint32                       `protobuf:"varint,3,req,name=uid" json:"uid"`
	Event            int32                        `protobuf:"varint,4,req,name=event" json:"event"`
	At               uint32                       `protobuf:"varint,5,req,name=at" json:"at"`
	ChannelDisplayId uint32                       `protobuf:"varint,6,opt,name=channel_display_id,json=channelDisplayId" json:"channel_display_id"`
	ChannelType      string                       `protobuf:"bytes,7,opt,name=channel_type,json=channelType" json:"channel_type"`
	EnterChannel     *ChannelMemberEnterData      `protobuf:"bytes,8,opt,name=enter_channel,json=enterChannel" json:"enter_channel,omitempty"`
	LeaveChannel     *ChannelMemberLeaveData      `protobuf:"bytes,9,opt,name=leave_channel,json=leaveChannel" json:"leave_channel,omitempty"`
	HoldMic          *ChannelMemberHoldMicData    `protobuf:"bytes,10,opt,name=hold_mic,json=holdMic" json:"hold_mic,omitempty"`
	ReleaseMic       *ChannelMemberReleaseMicData `protobuf:"bytes,11,opt,name=release_mic,json=releaseMic" json:"release_mic,omitempty"`
	// 用户信息 for wa
	Account     string `protobuf:"bytes,12,opt,name=account" json:"account"`
	Ip          string `protobuf:"bytes,13,opt,name=ip" json:"ip"`
	Port        int32  `protobuf:"varint,14,opt,name=port" json:"port"`
	Imei        string `protobuf:"bytes,15,opt,name=imei" json:"imei"`
	OsVer       string `protobuf:"bytes,16,opt,name=os_ver,json=osVer" json:"os_ver"`
	OsType      string `protobuf:"bytes,17,opt,name=os_type,json=osType" json:"os_type"`
	DeviceModel string `protobuf:"bytes,18,opt,name=device_model,json=deviceModel" json:"device_model"`
}

func (m *ChannelMemberEventData) Reset()         { *m = ChannelMemberEventData{} }
func (m *ChannelMemberEventData) String() string { return proto.CompactTextString(m) }
func (*ChannelMemberEventData) ProtoMessage()    {}
func (*ChannelMemberEventData) Descriptor() ([]byte, []int) {
	return fileDescriptorKfkFootmark, []int{6}
}

func (m *ChannelMemberEventData) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelMemberEventData) GetAppid() uint32 {
	if m != nil {
		return m.Appid
	}
	return 0
}

func (m *ChannelMemberEventData) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelMemberEventData) GetEvent() int32 {
	if m != nil {
		return m.Event
	}
	return 0
}

func (m *ChannelMemberEventData) GetAt() uint32 {
	if m != nil {
		return m.At
	}
	return 0
}

func (m *ChannelMemberEventData) GetChannelDisplayId() uint32 {
	if m != nil {
		return m.ChannelDisplayId
	}
	return 0
}

func (m *ChannelMemberEventData) GetChannelType() string {
	if m != nil {
		return m.ChannelType
	}
	return ""
}

func (m *ChannelMemberEventData) GetEnterChannel() *ChannelMemberEnterData {
	if m != nil {
		return m.EnterChannel
	}
	return nil
}

func (m *ChannelMemberEventData) GetLeaveChannel() *ChannelMemberLeaveData {
	if m != nil {
		return m.LeaveChannel
	}
	return nil
}

func (m *ChannelMemberEventData) GetHoldMic() *ChannelMemberHoldMicData {
	if m != nil {
		return m.HoldMic
	}
	return nil
}

func (m *ChannelMemberEventData) GetReleaseMic() *ChannelMemberReleaseMicData {
	if m != nil {
		return m.ReleaseMic
	}
	return nil
}

func (m *ChannelMemberEventData) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ChannelMemberEventData) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *ChannelMemberEventData) GetPort() int32 {
	if m != nil {
		return m.Port
	}
	return 0
}

func (m *ChannelMemberEventData) GetImei() string {
	if m != nil {
		return m.Imei
	}
	return ""
}

func (m *ChannelMemberEventData) GetOsVer() string {
	if m != nil {
		return m.OsVer
	}
	return ""
}

func (m *ChannelMemberEventData) GetOsType() string {
	if m != nil {
		return m.OsType
	}
	return ""
}

func (m *ChannelMemberEventData) GetDeviceModel() string {
	if m != nil {
		return m.DeviceModel
	}
	return ""
}

// 群进出事件
type GroupEntryEventData struct {
	Uid            uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Event          int32  `protobuf:"varint,2,req,name=event" json:"event"`
	At             uint32 `protobuf:"varint,3,req,name=at" json:"at"`
	GroupId        uint32 `protobuf:"varint,4,req,name=group_id,json=groupId" json:"group_id"`
	GroupType      uint32 `protobuf:"varint,5,opt,name=group_type,json=groupType" json:"group_type"`
	CreatorUid     uint32 `protobuf:"varint,6,opt,name=creator_uid,json=creatorUid" json:"creator_uid"`
	CreateAt       uint32 `protobuf:"varint,7,opt,name=create_at,json=createAt" json:"create_at"`
	DisplayId      uint32 `protobuf:"varint,8,opt,name=display_id,json=displayId" json:"display_id"`
	Name           string `protobuf:"bytes,9,opt,name=name" json:"name"`
	Account        string `protobuf:"bytes,10,opt,name=account" json:"account"`
	Nickname       string `protobuf:"bytes,11,opt,name=nickname" json:"nickname"`
	Role           uint32 `protobuf:"varint,12,opt,name=role" json:"role"`
	InviterUid     uint32 `protobuf:"varint,13,opt,name=inviter_uid,json=inviterUid" json:"inviter_uid"`
	InviterAccount string `protobuf:"bytes,14,opt,name=inviter_account,json=inviterAccount" json:"inviter_account"`
}

func (m *GroupEntryEventData) Reset()                    { *m = GroupEntryEventData{} }
func (m *GroupEntryEventData) String() string            { return proto.CompactTextString(m) }
func (*GroupEntryEventData) ProtoMessage()               {}
func (*GroupEntryEventData) Descriptor() ([]byte, []int) { return fileDescriptorKfkFootmark, []int{7} }

func (m *GroupEntryEventData) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GroupEntryEventData) GetEvent() int32 {
	if m != nil {
		return m.Event
	}
	return 0
}

func (m *GroupEntryEventData) GetAt() uint32 {
	if m != nil {
		return m.At
	}
	return 0
}

func (m *GroupEntryEventData) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GroupEntryEventData) GetGroupType() uint32 {
	if m != nil {
		return m.GroupType
	}
	return 0
}

func (m *GroupEntryEventData) GetCreatorUid() uint32 {
	if m != nil {
		return m.CreatorUid
	}
	return 0
}

func (m *GroupEntryEventData) GetCreateAt() uint32 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *GroupEntryEventData) GetDisplayId() uint32 {
	if m != nil {
		return m.DisplayId
	}
	return 0
}

func (m *GroupEntryEventData) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GroupEntryEventData) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *GroupEntryEventData) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *GroupEntryEventData) GetRole() uint32 {
	if m != nil {
		return m.Role
	}
	return 0
}

func (m *GroupEntryEventData) GetInviterUid() uint32 {
	if m != nil {
		return m.InviterUid
	}
	return 0
}

func (m *GroupEntryEventData) GetInviterAccount() string {
	if m != nil {
		return m.InviterAccount
	}
	return ""
}

// 群变更事件
type GroupChangeEventData struct {
	Uid        uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GroupId    uint32 `protobuf:"varint,2,req,name=group_id,json=groupId" json:"group_id"`
	ChangeType int32  `protobuf:"varint,3,req,name=change_type,json=changeType" json:"change_type"`
	At         uint32 `protobuf:"varint,4,req,name=at" json:"at"`
	GroupType  uint32 `protobuf:"varint,5,opt,name=group_type,json=groupType" json:"group_type"`
	CreateAt   uint32 `protobuf:"varint,6,opt,name=create_at,json=createAt" json:"create_at"`
	DisplayId  uint32 `protobuf:"varint,7,opt,name=display_id,json=displayId" json:"display_id"`
	Name       string `protobuf:"bytes,8,opt,name=name" json:"name"`
	// 创建者信息
	Account  string `protobuf:"bytes,9,opt,name=account" json:"account"`
	Nickname string `protobuf:"bytes,10,opt,name=nickname" json:"nickname"`
	Ip       string `protobuf:"bytes,11,opt,name=ip" json:"ip"`
	Port     int32  `protobuf:"varint,12,opt,name=port" json:"port"`
	Imei     string `protobuf:"bytes,13,opt,name=imei" json:"imei"`
	DeviceId string `protobuf:"bytes,14,opt,name=device_id,json=deviceId" json:"device_id"`
}

func (m *GroupChangeEventData) Reset()                    { *m = GroupChangeEventData{} }
func (m *GroupChangeEventData) String() string            { return proto.CompactTextString(m) }
func (*GroupChangeEventData) ProtoMessage()               {}
func (*GroupChangeEventData) Descriptor() ([]byte, []int) { return fileDescriptorKfkFootmark, []int{8} }

func (m *GroupChangeEventData) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GroupChangeEventData) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GroupChangeEventData) GetChangeType() int32 {
	if m != nil {
		return m.ChangeType
	}
	return 0
}

func (m *GroupChangeEventData) GetAt() uint32 {
	if m != nil {
		return m.At
	}
	return 0
}

func (m *GroupChangeEventData) GetGroupType() uint32 {
	if m != nil {
		return m.GroupType
	}
	return 0
}

func (m *GroupChangeEventData) GetCreateAt() uint32 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *GroupChangeEventData) GetDisplayId() uint32 {
	if m != nil {
		return m.DisplayId
	}
	return 0
}

func (m *GroupChangeEventData) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GroupChangeEventData) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *GroupChangeEventData) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *GroupChangeEventData) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *GroupChangeEventData) GetPort() int32 {
	if m != nil {
		return m.Port
	}
	return 0
}

func (m *GroupChangeEventData) GetImei() string {
	if m != nil {
		return m.Imei
	}
	return ""
}

func (m *GroupChangeEventData) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

// biz_type:friend
type FriendshipChangeData struct {
	Uid            uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	TargetUid      uint32 `protobuf:"varint,2,req,name=target_uid,json=targetUid" json:"target_uid"`
	ChangeType     int32  `protobuf:"varint,3,req,name=change_type,json=changeType" json:"change_type"`
	At             uint32 `protobuf:"varint,4,opt,name=at" json:"at"`
	Account        string `protobuf:"bytes,5,opt,name=account" json:"account"`
	Nickname       string `protobuf:"bytes,6,opt,name=nickname" json:"nickname"`
	TargetAccount  string `protobuf:"bytes,7,opt,name=target_account,json=targetAccount" json:"target_account"`
	TargetNickname string `protobuf:"bytes,8,opt,name=target_nickname,json=targetNickname" json:"target_nickname"`
	TargetPhone    string `protobuf:"bytes,9,opt,name=target_phone,json=targetPhone" json:"target_phone"`
}

func (m *FriendshipChangeData) Reset()                    { *m = FriendshipChangeData{} }
func (m *FriendshipChangeData) String() string            { return proto.CompactTextString(m) }
func (*FriendshipChangeData) ProtoMessage()               {}
func (*FriendshipChangeData) Descriptor() ([]byte, []int) { return fileDescriptorKfkFootmark, []int{9} }

func (m *FriendshipChangeData) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FriendshipChangeData) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *FriendshipChangeData) GetChangeType() int32 {
	if m != nil {
		return m.ChangeType
	}
	return 0
}

func (m *FriendshipChangeData) GetAt() uint32 {
	if m != nil {
		return m.At
	}
	return 0
}

func (m *FriendshipChangeData) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *FriendshipChangeData) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *FriendshipChangeData) GetTargetAccount() string {
	if m != nil {
		return m.TargetAccount
	}
	return ""
}

func (m *FriendshipChangeData) GetTargetNickname() string {
	if m != nil {
		return m.TargetNickname
	}
	return ""
}

func (m *FriendshipChangeData) GetTargetPhone() string {
	if m != nil {
		return m.TargetPhone
	}
	return ""
}

type TextContent struct {
	Data []byte `protobuf:"bytes,1,req,name=data" json:"data"`
}

func (m *TextContent) Reset()                    { *m = TextContent{} }
func (m *TextContent) String() string            { return proto.CompactTextString(m) }
func (*TextContent) ProtoMessage()               {}
func (*TextContent) Descriptor() ([]byte, []int) { return fileDescriptorKfkFootmark, []int{10} }

func (m *TextContent) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

type ImageContent struct {
	Data        []byte `protobuf:"bytes,1,req,name=data" json:"data"`
	Type        int32  `protobuf:"varint,2,req,name=type" json:"type"`
	ImageFormat string `protobuf:"bytes,3,req,name=image_format,json=imageFormat" json:"image_format"`
	Key         string `protobuf:"bytes,4,opt,name=key" json:"key"`
}

func (m *ImageContent) Reset()                    { *m = ImageContent{} }
func (m *ImageContent) String() string            { return proto.CompactTextString(m) }
func (*ImageContent) ProtoMessage()               {}
func (*ImageContent) Descriptor() ([]byte, []int) { return fileDescriptorKfkFootmark, []int{11} }

func (m *ImageContent) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *ImageContent) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *ImageContent) GetImageFormat() string {
	if m != nil {
		return m.ImageFormat
	}
	return ""
}

func (m *ImageContent) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

type VoiceContent struct {
	Data  []byte `protobuf:"bytes,1,req,name=data" json:"data"`
	Codec int32  `protobuf:"varint,2,opt,name=codec" json:"codec"`
	Key   string `protobuf:"bytes,3,opt,name=key" json:"key"`
}

func (m *VoiceContent) Reset()                    { *m = VoiceContent{} }
func (m *VoiceContent) String() string            { return proto.CompactTextString(m) }
func (*VoiceContent) ProtoMessage()               {}
func (*VoiceContent) Descriptor() ([]byte, []int) { return fileDescriptorKfkFootmark, []int{12} }

func (m *VoiceContent) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *VoiceContent) GetCodec() int32 {
	if m != nil {
		return m.Codec
	}
	return 0
}

func (m *VoiceContent) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

type IMData struct {
	FromUid       uint32 `protobuf:"varint,1,req,name=from_uid,json=fromUid" json:"from_uid"`
	FromAccount   string `protobuf:"bytes,2,opt,name=from_account,json=fromAccount" json:"from_account"`
	TargetType    uint32 `protobuf:"varint,3,req,name=target_type,json=targetType" json:"target_type"`
	TargetId      uint32 `protobuf:"varint,4,req,name=target_id,json=targetId" json:"target_id"`
	TargetAccount string `protobuf:"bytes,5,opt,name=target_account,json=targetAccount" json:"target_account"`
	At            uint32 `protobuf:"varint,6,req,name=at" json:"at"`
	ContentType   uint32 `protobuf:"varint,7,req,name=content_type,json=contentType" json:"content_type"`
	Content       []byte `protobuf:"bytes,8,req,name=content" json:"content"`
	// 发送者信息: 暂时群消息需要填充
	SenderIp       string `protobuf:"bytes,9,opt,name=sender_ip,json=senderIp" json:"sender_ip"`
	SenderPort     int32  `protobuf:"varint,10,opt,name=sender_port,json=senderPort" json:"sender_port"`
	SenderDeviceId string `protobuf:"bytes,11,opt,name=sender_device_id,json=senderDeviceId" json:"sender_device_id"`
	SenderImei     string `protobuf:"bytes,12,opt,name=sender_imei,json=senderImei" json:"sender_imei"`
	// 消息id, 使用 svr_msg_id
	MsgId          int64  `protobuf:"varint,13,opt,name=msg_id,json=msgId" json:"msg_id"`
	FromNickname   string `protobuf:"bytes,14,opt,name=from_nickname,json=fromNickname" json:"from_nickname"`
	TargetNickname string `protobuf:"bytes,15,opt,name=target_nickname,json=targetNickname" json:"target_nickname"`
	GroupDisplayId uint32 `protobuf:"varint,16,opt,name=group_display_id,json=groupDisplayId" json:"group_display_id"`
	MsgSourceType  uint32 `protobuf:"varint,17,opt,name=msg_source_type,json=msgSourceType" json:"msg_source_type"`
}

func (m *IMData) Reset()                    { *m = IMData{} }
func (m *IMData) String() string            { return proto.CompactTextString(m) }
func (*IMData) ProtoMessage()               {}
func (*IMData) Descriptor() ([]byte, []int) { return fileDescriptorKfkFootmark, []int{13} }

func (m *IMData) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *IMData) GetFromAccount() string {
	if m != nil {
		return m.FromAccount
	}
	return ""
}

func (m *IMData) GetTargetType() uint32 {
	if m != nil {
		return m.TargetType
	}
	return 0
}

func (m *IMData) GetTargetId() uint32 {
	if m != nil {
		return m.TargetId
	}
	return 0
}

func (m *IMData) GetTargetAccount() string {
	if m != nil {
		return m.TargetAccount
	}
	return ""
}

func (m *IMData) GetAt() uint32 {
	if m != nil {
		return m.At
	}
	return 0
}

func (m *IMData) GetContentType() uint32 {
	if m != nil {
		return m.ContentType
	}
	return 0
}

func (m *IMData) GetContent() []byte {
	if m != nil {
		return m.Content
	}
	return nil
}

func (m *IMData) GetSenderIp() string {
	if m != nil {
		return m.SenderIp
	}
	return ""
}

func (m *IMData) GetSenderPort() int32 {
	if m != nil {
		return m.SenderPort
	}
	return 0
}

func (m *IMData) GetSenderDeviceId() string {
	if m != nil {
		return m.SenderDeviceId
	}
	return ""
}

func (m *IMData) GetSenderImei() string {
	if m != nil {
		return m.SenderImei
	}
	return ""
}

func (m *IMData) GetMsgId() int64 {
	if m != nil {
		return m.MsgId
	}
	return 0
}

func (m *IMData) GetFromNickname() string {
	if m != nil {
		return m.FromNickname
	}
	return ""
}

func (m *IMData) GetTargetNickname() string {
	if m != nil {
		return m.TargetNickname
	}
	return ""
}

func (m *IMData) GetGroupDisplayId() uint32 {
	if m != nil {
		return m.GroupDisplayId
	}
	return 0
}

func (m *IMData) GetMsgSourceType() uint32 {
	if m != nil {
		return m.MsgSourceType
	}
	return 0
}

//
type ChannelImData struct {
	FromUid          uint32 `protobuf:"varint,1,req,name=from_uid,json=fromUid" json:"from_uid"`
	FromAccount      string `protobuf:"bytes,2,opt,name=from_account,json=fromAccount" json:"from_account"`
	ChannelId        uint32 `protobuf:"varint,3,req,name=channel_id,json=channelId" json:"channel_id"`
	Appid            uint32 `protobuf:"varint,4,req,name=appid" json:"appid"`
	ChannelDisplayId uint32 `protobuf:"varint,5,opt,name=channel_display_id,json=channelDisplayId" json:"channel_display_id"`
	ChannelType      string `protobuf:"bytes,6,opt,name=channel_type,json=channelType" json:"channel_type"`
	At               uint32 `protobuf:"varint,7,req,name=at" json:"at"`
	ContentType      uint32 `protobuf:"varint,8,req,name=content_type,json=contentType" json:"content_type"`
	Content          []byte `protobuf:"bytes,9,req,name=content" json:"content"`
}

func (m *ChannelImData) Reset()                    { *m = ChannelImData{} }
func (m *ChannelImData) String() string            { return proto.CompactTextString(m) }
func (*ChannelImData) ProtoMessage()               {}
func (*ChannelImData) Descriptor() ([]byte, []int) { return fileDescriptorKfkFootmark, []int{14} }

func (m *ChannelImData) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *ChannelImData) GetFromAccount() string {
	if m != nil {
		return m.FromAccount
	}
	return ""
}

func (m *ChannelImData) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelImData) GetAppid() uint32 {
	if m != nil {
		return m.Appid
	}
	return 0
}

func (m *ChannelImData) GetChannelDisplayId() uint32 {
	if m != nil {
		return m.ChannelDisplayId
	}
	return 0
}

func (m *ChannelImData) GetChannelType() string {
	if m != nil {
		return m.ChannelType
	}
	return ""
}

func (m *ChannelImData) GetAt() uint32 {
	if m != nil {
		return m.At
	}
	return 0
}

func (m *ChannelImData) GetContentType() uint32 {
	if m != nil {
		return m.ContentType
	}
	return 0
}

func (m *ChannelImData) GetContent() []byte {
	if m != nil {
		return m.Content
	}
	return nil
}

//
// ugc: Post/Comment
//
type UgcUserInfo struct {
	Uid        uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Account    string `protobuf:"bytes,2,opt,name=account" json:"account"`
	Alias      string `protobuf:"bytes,3,opt,name=alias" json:"alias"`
	Nickname   string `protobuf:"bytes,4,opt,name=nickname" json:"nickname"`
	ClientIp   string `protobuf:"bytes,5,opt,name=client_ip,json=clientIp" json:"client_ip"`
	ClientPort int32  `protobuf:"varint,6,opt,name=client_port,json=clientPort" json:"client_port"`
	Imei       string `protobuf:"bytes,7,opt,name=imei" json:"imei"`
}

func (m *UgcUserInfo) Reset()                    { *m = UgcUserInfo{} }
func (m *UgcUserInfo) String() string            { return proto.CompactTextString(m) }
func (*UgcUserInfo) ProtoMessage()               {}
func (*UgcUserInfo) Descriptor() ([]byte, []int) { return fileDescriptorKfkFootmark, []int{15} }

func (m *UgcUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UgcUserInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UgcUserInfo) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

func (m *UgcUserInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UgcUserInfo) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

func (m *UgcUserInfo) GetClientPort() int32 {
	if m != nil {
		return m.ClientPort
	}
	return 0
}

func (m *UgcUserInfo) GetImei() string {
	if m != nil {
		return m.Imei
	}
	return ""
}

type UgcAttachmentInfo struct {
	Type    UgcAttachmentInfo_AttachmentType `protobuf:"varint,1,req,name=type,enum=kafkafootmark.UgcAttachmentInfo_AttachmentType" json:"type"`
	Content string                           `protobuf:"bytes,2,req,name=content" json:"content"`
}

func (m *UgcAttachmentInfo) Reset()                    { *m = UgcAttachmentInfo{} }
func (m *UgcAttachmentInfo) String() string            { return proto.CompactTextString(m) }
func (*UgcAttachmentInfo) ProtoMessage()               {}
func (*UgcAttachmentInfo) Descriptor() ([]byte, []int) { return fileDescriptorKfkFootmark, []int{16} }

func (m *UgcAttachmentInfo) GetType() UgcAttachmentInfo_AttachmentType {
	if m != nil {
		return m.Type
	}
	return UgcAttachmentInfo_None
}

func (m *UgcAttachmentInfo) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type UgcPostEvent struct {
	OpType      int32                `protobuf:"varint,1,req,name=op_type,json=opType" json:"op_type"`
	PostId      string               `protobuf:"bytes,2,req,name=post_id,json=postId" json:"post_id"`
	Content     string               `protobuf:"bytes,3,opt,name=content" json:"content"`
	Attachments []*UgcAttachmentInfo `protobuf:"bytes,4,rep,name=attachments" json:"attachments,omitempty"`
	CreateAt    uint32               `protobuf:"varint,5,opt,name=create_at,json=createAt" json:"create_at"`
	Creator     *UgcUserInfo         `protobuf:"bytes,6,opt,name=creator" json:"creator,omitempty"`
}

func (m *UgcPostEvent) Reset()                    { *m = UgcPostEvent{} }
func (m *UgcPostEvent) String() string            { return proto.CompactTextString(m) }
func (*UgcPostEvent) ProtoMessage()               {}
func (*UgcPostEvent) Descriptor() ([]byte, []int) { return fileDescriptorKfkFootmark, []int{17} }

func (m *UgcPostEvent) GetOpType() int32 {
	if m != nil {
		return m.OpType
	}
	return 0
}

func (m *UgcPostEvent) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *UgcPostEvent) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *UgcPostEvent) GetAttachments() []*UgcAttachmentInfo {
	if m != nil {
		return m.Attachments
	}
	return nil
}

func (m *UgcPostEvent) GetCreateAt() uint32 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *UgcPostEvent) GetCreator() *UgcUserInfo {
	if m != nil {
		return m.Creator
	}
	return nil
}

type UgcCommentEvent struct {
	OpType      int32                `protobuf:"varint,1,req,name=op_type,json=opType" json:"op_type"`
	CommentId   string               `protobuf:"bytes,2,req,name=comment_id,json=commentId" json:"comment_id"`
	RefId       string               `protobuf:"bytes,3,opt,name=ref_id,json=refId" json:"ref_id"`
	PostId      string               `protobuf:"bytes,4,opt,name=post_id,json=postId" json:"post_id"`
	Content     string               `protobuf:"bytes,5,opt,name=content" json:"content"`
	Attachments []*UgcAttachmentInfo `protobuf:"bytes,6,rep,name=attachments" json:"attachments,omitempty"`
	CreateAt    uint32               `protobuf:"varint,7,opt,name=create_at,json=createAt" json:"create_at"`
	Creator     *UgcUserInfo         `protobuf:"bytes,8,opt,name=creator" json:"creator,omitempty"`
	ReplyTo     *UgcUserInfo         `protobuf:"bytes,9,opt,name=reply_to,json=replyTo" json:"reply_to,omitempty"`
}

func (m *UgcCommentEvent) Reset()                    { *m = UgcCommentEvent{} }
func (m *UgcCommentEvent) String() string            { return proto.CompactTextString(m) }
func (*UgcCommentEvent) ProtoMessage()               {}
func (*UgcCommentEvent) Descriptor() ([]byte, []int) { return fileDescriptorKfkFootmark, []int{18} }

func (m *UgcCommentEvent) GetOpType() int32 {
	if m != nil {
		return m.OpType
	}
	return 0
}

func (m *UgcCommentEvent) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *UgcCommentEvent) GetRefId() string {
	if m != nil {
		return m.RefId
	}
	return ""
}

func (m *UgcCommentEvent) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *UgcCommentEvent) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *UgcCommentEvent) GetAttachments() []*UgcAttachmentInfo {
	if m != nil {
		return m.Attachments
	}
	return nil
}

func (m *UgcCommentEvent) GetCreateAt() uint32 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *UgcCommentEvent) GetCreator() *UgcUserInfo {
	if m != nil {
		return m.Creator
	}
	return nil
}

func (m *UgcCommentEvent) GetReplyTo() *UgcUserInfo {
	if m != nil {
		return m.ReplyTo
	}
	return nil
}

type UgcEventData struct {
	EventType int32            `protobuf:"varint,1,req,name=event_type,json=eventType" json:"event_type"`
	At        uint32           `protobuf:"varint,2,req,name=at" json:"at"`
	Post      *UgcPostEvent    `protobuf:"bytes,3,opt,name=post" json:"post,omitempty"`
	Comment   *UgcCommentEvent `protobuf:"bytes,4,opt,name=comment" json:"comment,omitempty"`
}

func (m *UgcEventData) Reset()                    { *m = UgcEventData{} }
func (m *UgcEventData) String() string            { return proto.CompactTextString(m) }
func (*UgcEventData) ProtoMessage()               {}
func (*UgcEventData) Descriptor() ([]byte, []int) { return fileDescriptorKfkFootmark, []int{19} }

func (m *UgcEventData) GetEventType() int32 {
	if m != nil {
		return m.EventType
	}
	return 0
}

func (m *UgcEventData) GetAt() uint32 {
	if m != nil {
		return m.At
	}
	return 0
}

func (m *UgcEventData) GetPost() *UgcPostEvent {
	if m != nil {
		return m.Post
	}
	return nil
}

func (m *UgcEventData) GetComment() *UgcCommentEvent {
	if m != nil {
		return m.Comment
	}
	return nil
}

func init() {
	proto.RegisterType((*FootmarkRec)(nil), "kafkafootmark.FootmarkRec")
	proto.RegisterType((*UserLoginData)(nil), "kafkafootmark.UserLoginData")
	proto.RegisterType((*ChannelMemberEnterData)(nil), "kafkafootmark.ChannelMemberEnterData")
	proto.RegisterType((*ChannelMemberLeaveData)(nil), "kafkafootmark.ChannelMemberLeaveData")
	proto.RegisterType((*ChannelMemberHoldMicData)(nil), "kafkafootmark.ChannelMemberHoldMicData")
	proto.RegisterType((*ChannelMemberReleaseMicData)(nil), "kafkafootmark.ChannelMemberReleaseMicData")
	proto.RegisterType((*ChannelMemberEventData)(nil), "kafkafootmark.ChannelMemberEventData")
	proto.RegisterType((*GroupEntryEventData)(nil), "kafkafootmark.GroupEntryEventData")
	proto.RegisterType((*GroupChangeEventData)(nil), "kafkafootmark.GroupChangeEventData")
	proto.RegisterType((*FriendshipChangeData)(nil), "kafkafootmark.FriendshipChangeData")
	proto.RegisterType((*TextContent)(nil), "kafkafootmark.TextContent")
	proto.RegisterType((*ImageContent)(nil), "kafkafootmark.ImageContent")
	proto.RegisterType((*VoiceContent)(nil), "kafkafootmark.VoiceContent")
	proto.RegisterType((*IMData)(nil), "kafkafootmark.IMData")
	proto.RegisterType((*ChannelImData)(nil), "kafkafootmark.ChannelImData")
	proto.RegisterType((*UgcUserInfo)(nil), "kafkafootmark.UgcUserInfo")
	proto.RegisterType((*UgcAttachmentInfo)(nil), "kafkafootmark.UgcAttachmentInfo")
	proto.RegisterType((*UgcPostEvent)(nil), "kafkafootmark.UgcPostEvent")
	proto.RegisterType((*UgcCommentEvent)(nil), "kafkafootmark.UgcCommentEvent")
	proto.RegisterType((*UgcEventData)(nil), "kafkafootmark.UgcEventData")
	proto.RegisterEnum("kafkafootmark.ContentType", ContentType_name, ContentType_value)
	proto.RegisterEnum("kafkafootmark.ChannelMemberEventData_EventType", ChannelMemberEventData_EventType_name, ChannelMemberEventData_EventType_value)
	proto.RegisterEnum("kafkafootmark.GroupEntryEventData_EntryType", GroupEntryEventData_EntryType_name, GroupEntryEventData_EntryType_value)
	proto.RegisterEnum("kafkafootmark.GroupChangeEventData_ChangeType", GroupChangeEventData_ChangeType_name, GroupChangeEventData_ChangeType_value)
	proto.RegisterEnum("kafkafootmark.FriendshipChangeData_ChangeType", FriendshipChangeData_ChangeType_name, FriendshipChangeData_ChangeType_value)
	proto.RegisterEnum("kafkafootmark.ImageContent_Type", ImageContent_Type_name, ImageContent_Type_value)
	proto.RegisterEnum("kafkafootmark.VoiceContent_AV_CODEC", VoiceContent_AV_CODEC_name, VoiceContent_AV_CODEC_value)
	proto.RegisterEnum("kafkafootmark.IMData_TargetTo", IMData_TargetTo_name, IMData_TargetTo_value)
	proto.RegisterEnum("kafkafootmark.UgcAttachmentInfo_AttachmentType", UgcAttachmentInfo_AttachmentType_name, UgcAttachmentInfo_AttachmentType_value)
	proto.RegisterEnum("kafkafootmark.UgcPostEvent_OP_TYPE", UgcPostEvent_OP_TYPE_name, UgcPostEvent_OP_TYPE_value)
	proto.RegisterEnum("kafkafootmark.UgcCommentEvent_OP_TYPE", UgcCommentEvent_OP_TYPE_name, UgcCommentEvent_OP_TYPE_value)
	proto.RegisterEnum("kafkafootmark.UgcEventData_EventType", UgcEventData_EventType_name, UgcEventData_EventType_value)
}
func (m *FootmarkRec) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FootmarkRec) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Appid)))
	i += copy(dAtA[i:], m.Appid)
	dAtA[i] = 0x12
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Id)))
	i += copy(dAtA[i:], m.Id)
	dAtA[i] = 0x18
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.At))
	dAtA[i] = 0x22
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.BizType)))
	i += copy(dAtA[i:], m.BizType)
	if m.BizData != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.BizData)))
		i += copy(dAtA[i:], m.BizData)
	}
	return i, nil
}

func (m *UserLoginData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserLoginData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.LoginType)))
	i += copy(dAtA[i:], m.LoginType)
	dAtA[i] = 0x22
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.LoginUsername)))
	i += copy(dAtA[i:], m.LoginUsername)
	dAtA[i] = 0x28
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.Result))
	dAtA[i] = 0x30
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.ThirdPartyType))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.ThirdPartyOpenid)))
	i += copy(dAtA[i:], m.ThirdPartyOpenid)
	dAtA[i] = 0x40
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.LoginAt))
	dAtA[i] = 0x4a
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Imei)))
	i += copy(dAtA[i:], m.Imei)
	dAtA[i] = 0x52
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.OsVer)))
	i += copy(dAtA[i:], m.OsVer)
	dAtA[i] = 0x5a
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.OsType)))
	i += copy(dAtA[i:], m.OsType)
	dAtA[i] = 0x62
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.DeviceModel)))
	i += copy(dAtA[i:], m.DeviceModel)
	dAtA[i] = 0x6a
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.DeviceInfo)))
	i += copy(dAtA[i:], m.DeviceInfo)
	dAtA[i] = 0x70
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.IsEmulator))
	dAtA[i] = 0x7a
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	dAtA[i] = 0x80
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.TerminalType))
	dAtA[i] = 0x8a
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.ClientType)))
	i += copy(dAtA[i:], m.ClientType)
	dAtA[i] = 0x90
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.ClientVer))
	dAtA[i] = 0x9a
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.PkgSignature)))
	i += copy(dAtA[i:], m.PkgSignature)
	dAtA[i] = 0xa2
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.ClientChannelId)))
	i += copy(dAtA[i:], m.ClientChannelId)
	dAtA[i] = 0xaa
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.ClientIp)))
	i += copy(dAtA[i:], m.ClientIp)
	dAtA[i] = 0xb0
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.ClientPort))
	dAtA[i] = 0xba
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.ClientIpLocation)))
	i += copy(dAtA[i:], m.ClientIpLocation)
	dAtA[i] = 0xc2
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Alias)))
	i += copy(dAtA[i:], m.Alias)
	dAtA[i] = 0xca
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	dAtA[i] = 0xd2
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	dAtA[i] = 0xda
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Avatar)))
	i += copy(dAtA[i:], m.Avatar)
	dAtA[i] = 0xe2
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Signature)))
	i += copy(dAtA[i:], m.Signature)
	dAtA[i] = 0xea
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.ProxyIp)))
	i += copy(dAtA[i:], m.ProxyIp)
	dAtA[i] = 0xf0
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.ProxyPort))
	dAtA[i] = 0xf8
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.ClientId))
	dAtA[i] = 0x80
	i++
	dAtA[i] = 0x2
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.MarketId))
	return i, nil
}

func (m *ChannelMemberEnterData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelMemberEnterData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *ChannelMemberLeaveData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelMemberLeaveData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.Duration))
	return i, nil
}

func (m *ChannelMemberHoldMicData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelMemberHoldMicData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.MicId))
	return i, nil
}

func (m *ChannelMemberReleaseMicData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelMemberReleaseMicData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.MicId))
	return i, nil
}

func (m *ChannelMemberEventData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelMemberEventData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.Appid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x20
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.Event))
	dAtA[i] = 0x28
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.At))
	dAtA[i] = 0x30
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.ChannelDisplayId))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.ChannelType)))
	i += copy(dAtA[i:], m.ChannelType)
	if m.EnterChannel != nil {
		dAtA[i] = 0x42
		i++
		i = encodeVarintKfkFootmark(dAtA, i, uint64(m.EnterChannel.Size()))
		n1, err := m.EnterChannel.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	if m.LeaveChannel != nil {
		dAtA[i] = 0x4a
		i++
		i = encodeVarintKfkFootmark(dAtA, i, uint64(m.LeaveChannel.Size()))
		n2, err := m.LeaveChannel.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	if m.HoldMic != nil {
		dAtA[i] = 0x52
		i++
		i = encodeVarintKfkFootmark(dAtA, i, uint64(m.HoldMic.Size()))
		n3, err := m.HoldMic.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	if m.ReleaseMic != nil {
		dAtA[i] = 0x5a
		i++
		i = encodeVarintKfkFootmark(dAtA, i, uint64(m.ReleaseMic.Size()))
		n4, err := m.ReleaseMic.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	dAtA[i] = 0x62
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x6a
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Ip)))
	i += copy(dAtA[i:], m.Ip)
	dAtA[i] = 0x70
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.Port))
	dAtA[i] = 0x7a
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Imei)))
	i += copy(dAtA[i:], m.Imei)
	dAtA[i] = 0x82
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.OsVer)))
	i += copy(dAtA[i:], m.OsVer)
	dAtA[i] = 0x8a
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.OsType)))
	i += copy(dAtA[i:], m.OsType)
	dAtA[i] = 0x92
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.DeviceModel)))
	i += copy(dAtA[i:], m.DeviceModel)
	return i, nil
}

func (m *GroupEntryEventData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GroupEntryEventData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.Event))
	dAtA[i] = 0x18
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.At))
	dAtA[i] = 0x20
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.GroupId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.GroupType))
	dAtA[i] = 0x30
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.CreatorUid))
	dAtA[i] = 0x38
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.CreateAt))
	dAtA[i] = 0x40
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.DisplayId))
	dAtA[i] = 0x4a
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x52
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x5a
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	dAtA[i] = 0x60
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.Role))
	dAtA[i] = 0x68
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.InviterUid))
	dAtA[i] = 0x72
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.InviterAccount)))
	i += copy(dAtA[i:], m.InviterAccount)
	return i, nil
}

func (m *GroupChangeEventData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GroupChangeEventData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.GroupId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.ChangeType))
	dAtA[i] = 0x20
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.At))
	dAtA[i] = 0x28
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.GroupType))
	dAtA[i] = 0x30
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.CreateAt))
	dAtA[i] = 0x38
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.DisplayId))
	dAtA[i] = 0x42
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x4a
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x52
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	dAtA[i] = 0x5a
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Ip)))
	i += copy(dAtA[i:], m.Ip)
	dAtA[i] = 0x60
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.Port))
	dAtA[i] = 0x6a
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Imei)))
	i += copy(dAtA[i:], m.Imei)
	dAtA[i] = 0x72
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	return i, nil
}

func (m *FriendshipChangeData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FriendshipChangeData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.TargetUid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.ChangeType))
	dAtA[i] = 0x20
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.At))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x32
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.TargetAccount)))
	i += copy(dAtA[i:], m.TargetAccount)
	dAtA[i] = 0x42
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.TargetNickname)))
	i += copy(dAtA[i:], m.TargetNickname)
	dAtA[i] = 0x4a
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.TargetPhone)))
	i += copy(dAtA[i:], m.TargetPhone)
	return i, nil
}

func (m *TextContent) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TextContent) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Data != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Data)))
		i += copy(dAtA[i:], m.Data)
	}
	return i, nil
}

func (m *ImageContent) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ImageContent) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Data != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Data)))
		i += copy(dAtA[i:], m.Data)
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.ImageFormat)))
	i += copy(dAtA[i:], m.ImageFormat)
	dAtA[i] = 0x22
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Key)))
	i += copy(dAtA[i:], m.Key)
	return i, nil
}

func (m *VoiceContent) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VoiceContent) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Data != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Data)))
		i += copy(dAtA[i:], m.Data)
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.Codec))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Key)))
	i += copy(dAtA[i:], m.Key)
	return i, nil
}

func (m *IMData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IMData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.FromUid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.FromAccount)))
	i += copy(dAtA[i:], m.FromAccount)
	dAtA[i] = 0x18
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.TargetType))
	dAtA[i] = 0x20
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.TargetId))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.TargetAccount)))
	i += copy(dAtA[i:], m.TargetAccount)
	dAtA[i] = 0x30
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.At))
	dAtA[i] = 0x38
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.ContentType))
	if m.Content != nil {
		dAtA[i] = 0x42
		i++
		i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Content)))
		i += copy(dAtA[i:], m.Content)
	}
	dAtA[i] = 0x4a
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.SenderIp)))
	i += copy(dAtA[i:], m.SenderIp)
	dAtA[i] = 0x50
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.SenderPort))
	dAtA[i] = 0x5a
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.SenderDeviceId)))
	i += copy(dAtA[i:], m.SenderDeviceId)
	dAtA[i] = 0x62
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.SenderImei)))
	i += copy(dAtA[i:], m.SenderImei)
	dAtA[i] = 0x68
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.MsgId))
	dAtA[i] = 0x72
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.FromNickname)))
	i += copy(dAtA[i:], m.FromNickname)
	dAtA[i] = 0x7a
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.TargetNickname)))
	i += copy(dAtA[i:], m.TargetNickname)
	dAtA[i] = 0x80
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.GroupDisplayId))
	dAtA[i] = 0x88
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.MsgSourceType))
	return i, nil
}

func (m *ChannelImData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelImData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.FromUid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.FromAccount)))
	i += copy(dAtA[i:], m.FromAccount)
	dAtA[i] = 0x18
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.Appid))
	dAtA[i] = 0x28
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.ChannelDisplayId))
	dAtA[i] = 0x32
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.ChannelType)))
	i += copy(dAtA[i:], m.ChannelType)
	dAtA[i] = 0x38
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.At))
	dAtA[i] = 0x40
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.ContentType))
	if m.Content != nil {
		dAtA[i] = 0x4a
		i++
		i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Content)))
		i += copy(dAtA[i:], m.Content)
	}
	return i, nil
}

func (m *UgcUserInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UgcUserInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Alias)))
	i += copy(dAtA[i:], m.Alias)
	dAtA[i] = 0x22
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.ClientIp)))
	i += copy(dAtA[i:], m.ClientIp)
	dAtA[i] = 0x30
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.ClientPort))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Imei)))
	i += copy(dAtA[i:], m.Imei)
	return i, nil
}

func (m *UgcAttachmentInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UgcAttachmentInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x12
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	return i, nil
}

func (m *UgcPostEvent) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UgcPostEvent) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.OpType))
	dAtA[i] = 0x12
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.PostId)))
	i += copy(dAtA[i:], m.PostId)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	if len(m.Attachments) > 0 {
		for _, msg := range m.Attachments {
			dAtA[i] = 0x22
			i++
			i = encodeVarintKfkFootmark(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x28
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.CreateAt))
	if m.Creator != nil {
		dAtA[i] = 0x32
		i++
		i = encodeVarintKfkFootmark(dAtA, i, uint64(m.Creator.Size()))
		n5, err := m.Creator.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	return i, nil
}

func (m *UgcCommentEvent) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UgcCommentEvent) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.OpType))
	dAtA[i] = 0x12
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.CommentId)))
	i += copy(dAtA[i:], m.CommentId)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.RefId)))
	i += copy(dAtA[i:], m.RefId)
	dAtA[i] = 0x22
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.PostId)))
	i += copy(dAtA[i:], m.PostId)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	if len(m.Attachments) > 0 {
		for _, msg := range m.Attachments {
			dAtA[i] = 0x32
			i++
			i = encodeVarintKfkFootmark(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x38
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.CreateAt))
	if m.Creator != nil {
		dAtA[i] = 0x42
		i++
		i = encodeVarintKfkFootmark(dAtA, i, uint64(m.Creator.Size()))
		n6, err := m.Creator.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	if m.ReplyTo != nil {
		dAtA[i] = 0x4a
		i++
		i = encodeVarintKfkFootmark(dAtA, i, uint64(m.ReplyTo.Size()))
		n7, err := m.ReplyTo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	return i, nil
}

func (m *UgcEventData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UgcEventData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.EventType))
	dAtA[i] = 0x10
	i++
	i = encodeVarintKfkFootmark(dAtA, i, uint64(m.At))
	if m.Post != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintKfkFootmark(dAtA, i, uint64(m.Post.Size()))
		n8, err := m.Post.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	if m.Comment != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintKfkFootmark(dAtA, i, uint64(m.Comment.Size()))
		n9, err := m.Comment.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	return i, nil
}

func encodeFixed64KfkFootmark(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32KfkFootmark(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintKfkFootmark(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *FootmarkRec) Size() (n int) {
	var l int
	_ = l
	l = len(m.Appid)
	n += 1 + l + sovKfkFootmark(uint64(l))
	l = len(m.Id)
	n += 1 + l + sovKfkFootmark(uint64(l))
	n += 1 + sovKfkFootmark(uint64(m.At))
	l = len(m.BizType)
	n += 1 + l + sovKfkFootmark(uint64(l))
	if m.BizData != nil {
		l = len(m.BizData)
		n += 1 + l + sovKfkFootmark(uint64(l))
	}
	return n
}

func (m *UserLoginData) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkFootmark(uint64(m.Uid))
	l = len(m.Account)
	n += 1 + l + sovKfkFootmark(uint64(l))
	l = len(m.LoginType)
	n += 1 + l + sovKfkFootmark(uint64(l))
	l = len(m.LoginUsername)
	n += 1 + l + sovKfkFootmark(uint64(l))
	n += 1 + sovKfkFootmark(uint64(m.Result))
	n += 1 + sovKfkFootmark(uint64(m.ThirdPartyType))
	l = len(m.ThirdPartyOpenid)
	n += 1 + l + sovKfkFootmark(uint64(l))
	n += 1 + sovKfkFootmark(uint64(m.LoginAt))
	l = len(m.Imei)
	n += 1 + l + sovKfkFootmark(uint64(l))
	l = len(m.OsVer)
	n += 1 + l + sovKfkFootmark(uint64(l))
	l = len(m.OsType)
	n += 1 + l + sovKfkFootmark(uint64(l))
	l = len(m.DeviceModel)
	n += 1 + l + sovKfkFootmark(uint64(l))
	l = len(m.DeviceInfo)
	n += 1 + l + sovKfkFootmark(uint64(l))
	n += 1 + sovKfkFootmark(uint64(m.IsEmulator))
	l = len(m.DeviceId)
	n += 1 + l + sovKfkFootmark(uint64(l))
	n += 2 + sovKfkFootmark(uint64(m.TerminalType))
	l = len(m.ClientType)
	n += 2 + l + sovKfkFootmark(uint64(l))
	n += 2 + sovKfkFootmark(uint64(m.ClientVer))
	l = len(m.PkgSignature)
	n += 2 + l + sovKfkFootmark(uint64(l))
	l = len(m.ClientChannelId)
	n += 2 + l + sovKfkFootmark(uint64(l))
	l = len(m.ClientIp)
	n += 2 + l + sovKfkFootmark(uint64(l))
	n += 2 + sovKfkFootmark(uint64(m.ClientPort))
	l = len(m.ClientIpLocation)
	n += 2 + l + sovKfkFootmark(uint64(l))
	l = len(m.Alias)
	n += 2 + l + sovKfkFootmark(uint64(l))
	l = len(m.Phone)
	n += 2 + l + sovKfkFootmark(uint64(l))
	l = len(m.Nickname)
	n += 2 + l + sovKfkFootmark(uint64(l))
	l = len(m.Avatar)
	n += 2 + l + sovKfkFootmark(uint64(l))
	l = len(m.Signature)
	n += 2 + l + sovKfkFootmark(uint64(l))
	l = len(m.ProxyIp)
	n += 2 + l + sovKfkFootmark(uint64(l))
	n += 2 + sovKfkFootmark(uint64(m.ProxyPort))
	n += 2 + sovKfkFootmark(uint64(m.ClientId))
	n += 2 + sovKfkFootmark(uint64(m.MarketId))
	return n
}

func (m *ChannelMemberEnterData) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *ChannelMemberLeaveData) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkFootmark(uint64(m.Duration))
	return n
}

func (m *ChannelMemberHoldMicData) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkFootmark(uint64(m.MicId))
	return n
}

func (m *ChannelMemberReleaseMicData) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkFootmark(uint64(m.MicId))
	return n
}

func (m *ChannelMemberEventData) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkFootmark(uint64(m.ChannelId))
	n += 1 + sovKfkFootmark(uint64(m.Appid))
	n += 1 + sovKfkFootmark(uint64(m.Uid))
	n += 1 + sovKfkFootmark(uint64(m.Event))
	n += 1 + sovKfkFootmark(uint64(m.At))
	n += 1 + sovKfkFootmark(uint64(m.ChannelDisplayId))
	l = len(m.ChannelType)
	n += 1 + l + sovKfkFootmark(uint64(l))
	if m.EnterChannel != nil {
		l = m.EnterChannel.Size()
		n += 1 + l + sovKfkFootmark(uint64(l))
	}
	if m.LeaveChannel != nil {
		l = m.LeaveChannel.Size()
		n += 1 + l + sovKfkFootmark(uint64(l))
	}
	if m.HoldMic != nil {
		l = m.HoldMic.Size()
		n += 1 + l + sovKfkFootmark(uint64(l))
	}
	if m.ReleaseMic != nil {
		l = m.ReleaseMic.Size()
		n += 1 + l + sovKfkFootmark(uint64(l))
	}
	l = len(m.Account)
	n += 1 + l + sovKfkFootmark(uint64(l))
	l = len(m.Ip)
	n += 1 + l + sovKfkFootmark(uint64(l))
	n += 1 + sovKfkFootmark(uint64(m.Port))
	l = len(m.Imei)
	n += 1 + l + sovKfkFootmark(uint64(l))
	l = len(m.OsVer)
	n += 2 + l + sovKfkFootmark(uint64(l))
	l = len(m.OsType)
	n += 2 + l + sovKfkFootmark(uint64(l))
	l = len(m.DeviceModel)
	n += 2 + l + sovKfkFootmark(uint64(l))
	return n
}

func (m *GroupEntryEventData) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkFootmark(uint64(m.Uid))
	n += 1 + sovKfkFootmark(uint64(m.Event))
	n += 1 + sovKfkFootmark(uint64(m.At))
	n += 1 + sovKfkFootmark(uint64(m.GroupId))
	n += 1 + sovKfkFootmark(uint64(m.GroupType))
	n += 1 + sovKfkFootmark(uint64(m.CreatorUid))
	n += 1 + sovKfkFootmark(uint64(m.CreateAt))
	n += 1 + sovKfkFootmark(uint64(m.DisplayId))
	l = len(m.Name)
	n += 1 + l + sovKfkFootmark(uint64(l))
	l = len(m.Account)
	n += 1 + l + sovKfkFootmark(uint64(l))
	l = len(m.Nickname)
	n += 1 + l + sovKfkFootmark(uint64(l))
	n += 1 + sovKfkFootmark(uint64(m.Role))
	n += 1 + sovKfkFootmark(uint64(m.InviterUid))
	l = len(m.InviterAccount)
	n += 1 + l + sovKfkFootmark(uint64(l))
	return n
}

func (m *GroupChangeEventData) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkFootmark(uint64(m.Uid))
	n += 1 + sovKfkFootmark(uint64(m.GroupId))
	n += 1 + sovKfkFootmark(uint64(m.ChangeType))
	n += 1 + sovKfkFootmark(uint64(m.At))
	n += 1 + sovKfkFootmark(uint64(m.GroupType))
	n += 1 + sovKfkFootmark(uint64(m.CreateAt))
	n += 1 + sovKfkFootmark(uint64(m.DisplayId))
	l = len(m.Name)
	n += 1 + l + sovKfkFootmark(uint64(l))
	l = len(m.Account)
	n += 1 + l + sovKfkFootmark(uint64(l))
	l = len(m.Nickname)
	n += 1 + l + sovKfkFootmark(uint64(l))
	l = len(m.Ip)
	n += 1 + l + sovKfkFootmark(uint64(l))
	n += 1 + sovKfkFootmark(uint64(m.Port))
	l = len(m.Imei)
	n += 1 + l + sovKfkFootmark(uint64(l))
	l = len(m.DeviceId)
	n += 1 + l + sovKfkFootmark(uint64(l))
	return n
}

func (m *FriendshipChangeData) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkFootmark(uint64(m.Uid))
	n += 1 + sovKfkFootmark(uint64(m.TargetUid))
	n += 1 + sovKfkFootmark(uint64(m.ChangeType))
	n += 1 + sovKfkFootmark(uint64(m.At))
	l = len(m.Account)
	n += 1 + l + sovKfkFootmark(uint64(l))
	l = len(m.Nickname)
	n += 1 + l + sovKfkFootmark(uint64(l))
	l = len(m.TargetAccount)
	n += 1 + l + sovKfkFootmark(uint64(l))
	l = len(m.TargetNickname)
	n += 1 + l + sovKfkFootmark(uint64(l))
	l = len(m.TargetPhone)
	n += 1 + l + sovKfkFootmark(uint64(l))
	return n
}

func (m *TextContent) Size() (n int) {
	var l int
	_ = l
	if m.Data != nil {
		l = len(m.Data)
		n += 1 + l + sovKfkFootmark(uint64(l))
	}
	return n
}

func (m *ImageContent) Size() (n int) {
	var l int
	_ = l
	if m.Data != nil {
		l = len(m.Data)
		n += 1 + l + sovKfkFootmark(uint64(l))
	}
	n += 1 + sovKfkFootmark(uint64(m.Type))
	l = len(m.ImageFormat)
	n += 1 + l + sovKfkFootmark(uint64(l))
	l = len(m.Key)
	n += 1 + l + sovKfkFootmark(uint64(l))
	return n
}

func (m *VoiceContent) Size() (n int) {
	var l int
	_ = l
	if m.Data != nil {
		l = len(m.Data)
		n += 1 + l + sovKfkFootmark(uint64(l))
	}
	n += 1 + sovKfkFootmark(uint64(m.Codec))
	l = len(m.Key)
	n += 1 + l + sovKfkFootmark(uint64(l))
	return n
}

func (m *IMData) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkFootmark(uint64(m.FromUid))
	l = len(m.FromAccount)
	n += 1 + l + sovKfkFootmark(uint64(l))
	n += 1 + sovKfkFootmark(uint64(m.TargetType))
	n += 1 + sovKfkFootmark(uint64(m.TargetId))
	l = len(m.TargetAccount)
	n += 1 + l + sovKfkFootmark(uint64(l))
	n += 1 + sovKfkFootmark(uint64(m.At))
	n += 1 + sovKfkFootmark(uint64(m.ContentType))
	if m.Content != nil {
		l = len(m.Content)
		n += 1 + l + sovKfkFootmark(uint64(l))
	}
	l = len(m.SenderIp)
	n += 1 + l + sovKfkFootmark(uint64(l))
	n += 1 + sovKfkFootmark(uint64(m.SenderPort))
	l = len(m.SenderDeviceId)
	n += 1 + l + sovKfkFootmark(uint64(l))
	l = len(m.SenderImei)
	n += 1 + l + sovKfkFootmark(uint64(l))
	n += 1 + sovKfkFootmark(uint64(m.MsgId))
	l = len(m.FromNickname)
	n += 1 + l + sovKfkFootmark(uint64(l))
	l = len(m.TargetNickname)
	n += 1 + l + sovKfkFootmark(uint64(l))
	n += 2 + sovKfkFootmark(uint64(m.GroupDisplayId))
	n += 2 + sovKfkFootmark(uint64(m.MsgSourceType))
	return n
}

func (m *ChannelImData) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkFootmark(uint64(m.FromUid))
	l = len(m.FromAccount)
	n += 1 + l + sovKfkFootmark(uint64(l))
	n += 1 + sovKfkFootmark(uint64(m.ChannelId))
	n += 1 + sovKfkFootmark(uint64(m.Appid))
	n += 1 + sovKfkFootmark(uint64(m.ChannelDisplayId))
	l = len(m.ChannelType)
	n += 1 + l + sovKfkFootmark(uint64(l))
	n += 1 + sovKfkFootmark(uint64(m.At))
	n += 1 + sovKfkFootmark(uint64(m.ContentType))
	if m.Content != nil {
		l = len(m.Content)
		n += 1 + l + sovKfkFootmark(uint64(l))
	}
	return n
}

func (m *UgcUserInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkFootmark(uint64(m.Uid))
	l = len(m.Account)
	n += 1 + l + sovKfkFootmark(uint64(l))
	l = len(m.Alias)
	n += 1 + l + sovKfkFootmark(uint64(l))
	l = len(m.Nickname)
	n += 1 + l + sovKfkFootmark(uint64(l))
	l = len(m.ClientIp)
	n += 1 + l + sovKfkFootmark(uint64(l))
	n += 1 + sovKfkFootmark(uint64(m.ClientPort))
	l = len(m.Imei)
	n += 1 + l + sovKfkFootmark(uint64(l))
	return n
}

func (m *UgcAttachmentInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkFootmark(uint64(m.Type))
	l = len(m.Content)
	n += 1 + l + sovKfkFootmark(uint64(l))
	return n
}

func (m *UgcPostEvent) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkFootmark(uint64(m.OpType))
	l = len(m.PostId)
	n += 1 + l + sovKfkFootmark(uint64(l))
	l = len(m.Content)
	n += 1 + l + sovKfkFootmark(uint64(l))
	if len(m.Attachments) > 0 {
		for _, e := range m.Attachments {
			l = e.Size()
			n += 1 + l + sovKfkFootmark(uint64(l))
		}
	}
	n += 1 + sovKfkFootmark(uint64(m.CreateAt))
	if m.Creator != nil {
		l = m.Creator.Size()
		n += 1 + l + sovKfkFootmark(uint64(l))
	}
	return n
}

func (m *UgcCommentEvent) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkFootmark(uint64(m.OpType))
	l = len(m.CommentId)
	n += 1 + l + sovKfkFootmark(uint64(l))
	l = len(m.RefId)
	n += 1 + l + sovKfkFootmark(uint64(l))
	l = len(m.PostId)
	n += 1 + l + sovKfkFootmark(uint64(l))
	l = len(m.Content)
	n += 1 + l + sovKfkFootmark(uint64(l))
	if len(m.Attachments) > 0 {
		for _, e := range m.Attachments {
			l = e.Size()
			n += 1 + l + sovKfkFootmark(uint64(l))
		}
	}
	n += 1 + sovKfkFootmark(uint64(m.CreateAt))
	if m.Creator != nil {
		l = m.Creator.Size()
		n += 1 + l + sovKfkFootmark(uint64(l))
	}
	if m.ReplyTo != nil {
		l = m.ReplyTo.Size()
		n += 1 + l + sovKfkFootmark(uint64(l))
	}
	return n
}

func (m *UgcEventData) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkFootmark(uint64(m.EventType))
	n += 1 + sovKfkFootmark(uint64(m.At))
	if m.Post != nil {
		l = m.Post.Size()
		n += 1 + l + sovKfkFootmark(uint64(l))
	}
	if m.Comment != nil {
		l = m.Comment.Size()
		n += 1 + l + sovKfkFootmark(uint64(l))
	}
	return n
}

func sovKfkFootmark(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozKfkFootmark(x uint64) (n int) {
	return sovKfkFootmark(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *FootmarkRec) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkFootmark
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: FootmarkRec: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: FootmarkRec: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Appid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Appid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Id = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field At", wireType)
			}
			m.At = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.At |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field BizType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BizType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field BizData", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BizData = append(m.BizData[:0], dAtA[iNdEx:postIndex]...)
			if m.BizData == nil {
				m.BizData = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipKfkFootmark(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("appid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("at")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("biz_type")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("biz_data")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserLoginData) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkFootmark
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: UserLoginData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: UserLoginData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field LoginType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LoginType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field LoginUsername", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LoginUsername = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			m.Result = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Result |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 6:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ThirdPartyType", wireType)
			}
			m.ThirdPartyType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ThirdPartyType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ThirdPartyOpenid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ThirdPartyOpenid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field LoginAt", wireType)
			}
			m.LoginAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LoginAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 9:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Imei", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Imei = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OsVer", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OsVer = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OsType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OsType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field DeviceModel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DeviceModel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 13:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field DeviceInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DeviceInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 14:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field IsEmulator", wireType)
			}
			m.IsEmulator = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IsEmulator |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 15:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 16:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field TerminalType", wireType)
			}
			m.TerminalType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TerminalType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 17:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ClientType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ClientType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 18:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ClientVer", wireType)
			}
			m.ClientVer = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientVer |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 19:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field PkgSignature", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PkgSignature = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 20:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ClientChannelId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ClientChannelId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 21:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ClientIp", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ClientIp = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 22:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ClientPort", wireType)
			}
			m.ClientPort = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientPort |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 23:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ClientIpLocation", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ClientIpLocation = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 24:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Alias", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Alias = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 25:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 26:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 27:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Avatar", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Avatar = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 28:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Signature", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Signature = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 29:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ProxyIp", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ProxyIp = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 30:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ProxyPort", wireType)
			}
			m.ProxyPort = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProxyPort |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 31:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ClientId", wireType)
			}
			m.ClientId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientId |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 32:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field MarketId", wireType)
			}
			m.MarketId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MarketId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipKfkFootmark(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("login_type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("result")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("login_at")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelMemberEnterData) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkFootmark
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: ChannelMemberEnterData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: ChannelMemberEnterData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipKfkFootmark(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelMemberLeaveData) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkFootmark
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: ChannelMemberLeaveData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: ChannelMemberLeaveData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Duration", wireType)
			}
			m.Duration = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Duration |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipKfkFootmark(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("duration")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelMemberHoldMicData) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkFootmark
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: ChannelMemberHoldMicData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: ChannelMemberHoldMicData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field MicId", wireType)
			}
			m.MicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipKfkFootmark(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelMemberReleaseMicData) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkFootmark
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: ChannelMemberReleaseMicData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: ChannelMemberReleaseMicData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field MicId", wireType)
			}
			m.MicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipKfkFootmark(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelMemberEventData) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkFootmark
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: ChannelMemberEventData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: ChannelMemberEventData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Appid", wireType)
			}
			m.Appid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Appid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Event", wireType)
			}
			m.Event = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Event |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field At", wireType)
			}
			m.At = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.At |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelDisplayId", wireType)
			}
			m.ChannelDisplayId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelDisplayId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ChannelType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field EnterChannel", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.EnterChannel == nil {
				m.EnterChannel = &ChannelMemberEnterData{}
			}
			if err := m.EnterChannel.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field LeaveChannel", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.LeaveChannel == nil {
				m.LeaveChannel = &ChannelMemberLeaveData{}
			}
			if err := m.LeaveChannel.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field HoldMic", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.HoldMic == nil {
				m.HoldMic = &ChannelMemberHoldMicData{}
			}
			if err := m.HoldMic.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ReleaseMic", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ReleaseMic == nil {
				m.ReleaseMic = &ChannelMemberReleaseMicData{}
			}
			if err := m.ReleaseMic.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 13:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Ip", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ip = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 14:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Port", wireType)
			}
			m.Port = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Port |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 15:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Imei", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Imei = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 16:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OsVer", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OsVer = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 17:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OsType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OsType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 18:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field DeviceModel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DeviceModel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipKfkFootmark(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("appid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("event")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("at")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GroupEntryEventData) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkFootmark
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: GroupEntryEventData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: GroupEntryEventData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Event", wireType)
			}
			m.Event = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Event |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field At", wireType)
			}
			m.At = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.At |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field GroupId", wireType)
			}
			m.GroupId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field GroupType", wireType)
			}
			m.GroupType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field CreatorUid", wireType)
			}
			m.CreatorUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreatorUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field CreateAt", wireType)
			}
			m.CreateAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field DisplayId", wireType)
			}
			m.DisplayId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DisplayId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Role", wireType)
			}
			m.Role = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Role |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field InviterUid", wireType)
			}
			m.InviterUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.InviterUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 14:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field InviterAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.InviterAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipKfkFootmark(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("event")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("at")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("group_id")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GroupChangeEventData) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkFootmark
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: GroupChangeEventData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: GroupChangeEventData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field GroupId", wireType)
			}
			m.GroupId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChangeType", wireType)
			}
			m.ChangeType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChangeType |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field At", wireType)
			}
			m.At = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.At |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field GroupType", wireType)
			}
			m.GroupType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field CreateAt", wireType)
			}
			m.CreateAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field DisplayId", wireType)
			}
			m.DisplayId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DisplayId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Ip", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ip = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Port", wireType)
			}
			m.Port = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Port |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Imei", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Imei = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipKfkFootmark(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("group_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("change_type")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("at")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *FriendshipChangeData) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkFootmark
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: FriendshipChangeData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: FriendshipChangeData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChangeType", wireType)
			}
			m.ChangeType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChangeType |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field At", wireType)
			}
			m.At = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.At |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field TargetAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TargetAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field TargetNickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TargetNickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field TargetPhone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TargetPhone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipKfkFootmark(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("target_uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("change_type")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TextContent) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkFootmark
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: TextContent: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: TextContent: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Data = append(m.Data[:0], dAtA[iNdEx:postIndex]...)
			if m.Data == nil {
				m.Data = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipKfkFootmark(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("data")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ImageContent) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkFootmark
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: ImageContent: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: ImageContent: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Data = append(m.Data[:0], dAtA[iNdEx:postIndex]...)
			if m.Data == nil {
				m.Data = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ImageFormat", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ImageFormat = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipKfkFootmark(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("data")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("image_format")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *VoiceContent) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkFootmark
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: VoiceContent: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: VoiceContent: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Data = append(m.Data[:0], dAtA[iNdEx:postIndex]...)
			if m.Data == nil {
				m.Data = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Codec", wireType)
			}
			m.Codec = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Codec |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipKfkFootmark(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("data")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *IMData) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkFootmark
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: IMData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: IMData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field FromUid", wireType)
			}
			m.FromUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FromUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field FromAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FromAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field TargetType", wireType)
			}
			m.TargetType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field TargetId", wireType)
			}
			m.TargetId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 5:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field TargetAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TargetAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field At", wireType)
			}
			m.At = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.At |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 7:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ContentType", wireType)
			}
			m.ContentType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ContentType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 8:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Content = append(m.Content[:0], dAtA[iNdEx:postIndex]...)
			if m.Content == nil {
				m.Content = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000020)
		case 9:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field SenderIp", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SenderIp = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field SenderPort", wireType)
			}
			m.SenderPort = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SenderPort |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field SenderDeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SenderDeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field SenderImei", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SenderImei = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 13:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field MsgId", wireType)
			}
			m.MsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MsgId |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 14:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field FromNickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FromNickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 15:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field TargetNickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TargetNickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 16:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field GroupDisplayId", wireType)
			}
			m.GroupDisplayId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupDisplayId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 17:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field MsgSourceType", wireType)
			}
			m.MsgSourceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MsgSourceType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipKfkFootmark(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("from_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("target_type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("target_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("at")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("content_type")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("content")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelImData) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkFootmark
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: ChannelImData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: ChannelImData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field FromUid", wireType)
			}
			m.FromUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FromUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field FromAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FromAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Appid", wireType)
			}
			m.Appid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Appid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 5:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelDisplayId", wireType)
			}
			m.ChannelDisplayId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelDisplayId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ChannelType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field At", wireType)
			}
			m.At = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.At |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 8:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ContentType", wireType)
			}
			m.ContentType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ContentType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 9:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Content = append(m.Content[:0], dAtA[iNdEx:postIndex]...)
			if m.Content == nil {
				m.Content = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000020)
		default:
			iNdEx = preIndex
			skippy, err := skipKfkFootmark(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("from_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("appid")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("at")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("content_type")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("content")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UgcUserInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkFootmark
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: UgcUserInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: UgcUserInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Alias", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Alias = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ClientIp", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ClientIp = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ClientPort", wireType)
			}
			m.ClientPort = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientPort |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Imei", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Imei = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipKfkFootmark(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UgcAttachmentInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkFootmark
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: UgcAttachmentInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: UgcAttachmentInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (UgcAttachmentInfo_AttachmentType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipKfkFootmark(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("content")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UgcPostEvent) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkFootmark
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: UgcPostEvent: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: UgcPostEvent: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OpType", wireType)
			}
			m.OpType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpType |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field PostId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PostId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Attachments", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Attachments = append(m.Attachments, &UgcAttachmentInfo{})
			if err := m.Attachments[len(m.Attachments)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field CreateAt", wireType)
			}
			m.CreateAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Creator", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Creator == nil {
				m.Creator = &UgcUserInfo{}
			}
			if err := m.Creator.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipKfkFootmark(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("op_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("post_id")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UgcCommentEvent) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkFootmark
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: UgcCommentEvent: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: UgcCommentEvent: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OpType", wireType)
			}
			m.OpType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpType |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field CommentId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CommentId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field RefId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RefId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field PostId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PostId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Attachments", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Attachments = append(m.Attachments, &UgcAttachmentInfo{})
			if err := m.Attachments[len(m.Attachments)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field CreateAt", wireType)
			}
			m.CreateAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Creator", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Creator == nil {
				m.Creator = &UgcUserInfo{}
			}
			if err := m.Creator.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ReplyTo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ReplyTo == nil {
				m.ReplyTo = &UgcUserInfo{}
			}
			if err := m.ReplyTo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipKfkFootmark(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("op_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("comment_id")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UgcEventData) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkFootmark
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: UgcEventData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: UgcEventData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field EventType", wireType)
			}
			m.EventType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EventType |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field At", wireType)
			}
			m.At = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.At |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Post", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Post == nil {
				m.Post = &UgcPostEvent{}
			}
			if err := m.Post.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Comment", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Comment == nil {
				m.Comment = &UgcCommentEvent{}
			}
			if err := m.Comment.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipKfkFootmark(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkFootmark
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("event_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("at")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipKfkFootmark(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowKfkFootmark
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowKfkFootmark
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthKfkFootmark
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowKfkFootmark
					}
					if iNdEx >= l {
						return 0, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipKfkFootmark(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt1.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthKfkFootmark = fmt1.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowKfkFootmark   = fmt1.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("src/minToolkit/kafka/pb/kfk_footmark.proto", fileDescriptorKfkFootmark)
}

var fileDescriptorKfkFootmark = []byte{
	// 2381 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x58, 0x5f, 0x6f, 0xdb, 0xd6,
	0x15, 0x0f, 0xa9, 0xff, 0x87, 0x92, 0x4c, 0xb3, 0x59, 0xc7, 0x39, 0x8d, 0xa3, 0x32, 0x08, 0x92,
	0x26, 0x9b, 0xdd, 0x79, 0x7f, 0xd1, 0x37, 0x45, 0x62, 0x12, 0xb6, 0x92, 0xa5, 0xe9, 0x8f, 0xbb,
	0x3e, 0x11, 0x0c, 0x79, 0x2d, 0x13, 0x96, 0x48, 0x82, 0xa4, 0x8d, 0xba, 0xdf, 0x61, 0xc0, 0x1e,
	0x06, 0xec, 0x0b, 0xac, 0xfd, 0x20, 0x03, 0x06, 0x14, 0x7b, 0xda, 0xd3, 0x1e, 0x87, 0x21, 0x03,
	0xb6, 0x4f, 0xb0, 0xf7, 0xe1, 0x9e, 0x7b, 0x49, 0x5d, 0xc9, 0xb2, 0xad, 0x6e, 0x7b, 0x13, 0xcf,
	0xef, 0xdc, 0xa3, 0x73, 0xcf, 0xff, 0x73, 0xe1, 0x79, 0x12, 0xbb, 0x87, 0x0b, 0x3f, 0x98, 0x84,
	0xe1, 0xfc, 0xdc, 0x4f, 0x0f, 0xcf, 0x9d, 0xd3, 0x73, 0xe7, 0x30, 0x7a, 0x7b, 0x78, 0x7e, 0x7a,
	0x6e, 0x9f, 0x86, 0x61, 0xba, 0x70, 0xe2, 0xf3, 0x83, 0x28, 0x0e, 0xd3, 0x50, 0x6b, 0x20, 0x98,
	0x11, 0x8d, 0xdf, 0x4b, 0xa0, 0xbc, 0xe2, 0x1f, 0x23, 0xe2, 0x6a, 0x7b, 0x50, 0x72, 0xa2, 0xc8,
	0xf7, 0x74, 0xa9, 0x25, 0x3f, 0xab, 0xbd, 0x2c, 0x7e, 0xfb, 0xb7, 0x47, 0xf7, 0x46, 0x8c, 0xa4,
	0xdd, 0x07, 0xd9, 0xf7, 0x74, 0x59, 0x00, 0x64, 0x46, 0x75, 0x52, 0xbd, 0xd0, 0x92, 0x9f, 0x35,
	0x32, 0xaa, 0x93, 0x6a, 0x8f, 0xa0, 0xfa, 0xd6, 0xff, 0xca, 0x4e, 0xaf, 0x22, 0xa2, 0x17, 0x85,
	0x13, 0x95, 0xb7, 0xfe, 0x57, 0x93, 0xab, 0x88, 0x64, 0x0c, 0x9e, 0x93, 0x3a, 0x7a, 0xa9, 0x25,
	0x3f, 0xab, 0x0b, 0x0c, 0x5d, 0x27, 0x75, 0x8c, 0x6f, 0x6a, 0xd0, 0x98, 0x26, 0x24, 0xee, 0x85,
	0x33, 0x3f, 0xa0, 0x14, 0xed, 0x7d, 0x28, 0x5c, 0x70, 0xcd, 0xb2, 0xbf, 0xa2, 0x04, 0x6d, 0x1f,
	0x2a, 0x8e, 0xeb, 0x86, 0x17, 0x41, 0xaa, 0xcb, 0x2d, 0x69, 0xf9, 0x57, 0x9c, 0xa8, 0x3d, 0x06,
	0x98, 0x53, 0x21, 0x4c, 0x9b, 0x82, 0xa0, 0x4d, 0x0d, 0xe9, 0xa8, 0xcf, 0x0b, 0x68, 0x32, 0xa6,
	0x8b, 0x84, 0xc4, 0x81, 0xb3, 0xa0, 0x6a, 0x2f, 0x65, 0x35, 0x10, 0x9b, 0x72, 0x48, 0xfb, 0x00,
	0xca, 0x31, 0x49, 0x2e, 0xe6, 0x29, 0xaa, 0x5e, 0xe2, 0x4c, 0x9c, 0xa6, 0x1d, 0x80, 0x9a, 0x9e,
	0xf9, 0xb1, 0x67, 0x47, 0x4e, 0x9c, 0x5e, 0xb1, 0x7f, 0x2d, 0xb7, 0xa4, 0x5c, 0xe9, 0x26, 0xa2,
	0x43, 0x0a, 0xe2, 0x5f, 0x1f, 0x81, 0x26, 0xf2, 0x87, 0x11, 0x09, 0x7c, 0x4f, 0xaf, 0x08, 0x7f,
	0xaf, 0x2e, 0x4f, 0x0c, 0x10, 0xa5, 0xe6, 0x63, 0xea, 0x3a, 0xa9, 0x5e, 0x15, 0x0c, 0x52, 0x41,
	0x6a, 0x3b, 0xd5, 0x74, 0x28, 0xfa, 0x0b, 0xe2, 0xeb, 0x35, 0x41, 0x0c, 0x52, 0xb4, 0x07, 0x50,
	0x0e, 0x13, 0xfb, 0x92, 0xc4, 0x3a, 0x08, 0x58, 0x29, 0x4c, 0x4e, 0x48, 0xac, 0x3d, 0x84, 0x4a,
	0x98, 0x30, 0x95, 0x15, 0x01, 0x2d, 0x87, 0x09, 0xaa, 0xfa, 0x14, 0xea, 0x1e, 0xb9, 0xf4, 0x5d,
	0x62, 0x2f, 0x42, 0x8f, 0xcc, 0xf5, 0xba, 0xc0, 0xa3, 0x30, 0xa4, 0x4f, 0x01, 0xed, 0x09, 0xf0,
	0x4f, 0xdb, 0x0f, 0x4e, 0x43, 0xbd, 0x21, 0xf0, 0x01, 0x03, 0xac, 0xe0, 0x34, 0xa4, 0x6c, 0x7e,
	0x62, 0x93, 0xc5, 0xc5, 0xdc, 0x49, 0xc3, 0x58, 0x6f, 0x0a, 0x56, 0x02, 0x3f, 0x31, 0x39, 0x5d,
	0xfb, 0x10, 0x6a, 0x99, 0x34, 0x4f, 0xdf, 0x11, 0x64, 0x55, 0xb9, 0x2c, 0x4f, 0xfb, 0x08, 0x1a,
	0x29, 0x89, 0x17, 0x7e, 0xe0, 0xcc, 0x99, 0xfa, 0xaa, 0x20, 0xab, 0x9e, 0x41, 0x78, 0x89, 0x27,
	0xa0, 0xb8, 0x73, 0x9f, 0x04, 0x29, 0x63, 0xdc, 0x15, 0x75, 0x63, 0x00, 0xb2, 0x3d, 0x06, 0xfe,
	0x85, 0xb6, 0xd2, 0x04, 0x71, 0x35, 0x46, 0xa7, 0xf6, 0xfa, 0x08, 0x1a, 0xd1, 0xf9, 0xcc, 0x4e,
	0xfc, 0x59, 0xe0, 0xa4, 0x17, 0x31, 0xd1, 0xdf, 0x13, 0xa4, 0xd5, 0xa3, 0xf3, 0xd9, 0x38, 0x43,
	0xb4, 0x8f, 0x61, 0x97, 0xcb, 0x73, 0xcf, 0x9c, 0x20, 0x20, 0x73, 0x7a, 0x99, 0xfb, 0x02, 0xfb,
	0x0e, 0x83, 0x3b, 0x0c, 0xb5, 0x3c, 0x7a, 0x6d, 0x7e, 0xc2, 0x8f, 0xf4, 0xef, 0x89, 0xd7, 0x66,
	0x64, 0x2b, 0x12, 0xee, 0x12, 0x85, 0x71, 0xaa, 0xbf, 0xdf, 0x92, 0xf2, 0x70, 0xe4, 0xda, 0x0f,
	0xc3, 0x38, 0xa5, 0x21, 0x96, 0x4b, 0xb2, 0xe7, 0xa1, 0xeb, 0xa4, 0x7e, 0x18, 0xe8, 0xdf, 0x17,
	0x43, 0x2c, 0x13, 0xd9, 0xe3, 0x28, 0x96, 0x82, 0xb9, 0xef, 0x24, 0xba, 0x2e, 0x86, 0x09, 0x92,
	0x28, 0x16, 0x9d, 0x85, 0x01, 0xd1, 0x7f, 0x20, 0x62, 0x48, 0xd2, 0x5a, 0x50, 0x0d, 0x7c, 0xf7,
	0x1c, 0x73, 0x68, 0x4f, 0x54, 0x3a, 0xa3, 0xd2, 0xf4, 0x71, 0x2e, 0x9d, 0xd4, 0x89, 0xf5, 0x07,
	0x62, 0x8c, 0x31, 0x9a, 0x66, 0x40, 0x6d, 0x69, 0xce, 0x0f, 0x04, 0x86, 0x25, 0x99, 0x86, 0x7f,
	0x14, 0x87, 0x5f, 0x5e, 0x51, 0xc3, 0x3c, 0x14, 0x73, 0x1e, 0xa9, 0x56, 0x44, 0x9d, 0xc7, 0x18,
	0xd0, 0x2c, 0xfb, 0x82, 0x59, 0x6a, 0x48, 0x47, 0xab, 0x08, 0xf6, 0xf5, 0xf4, 0x47, 0x02, 0x4f,
	0x66, 0x5f, 0x74, 0x01, 0x2d, 0x8d, 0x04, 0x59, 0x5a, 0x42, 0x0c, 0x54, 0x19, 0xd9, 0xf2, 0x0c,
	0x1d, 0xde, 0xe7, 0x2e, 0xeb, 0x93, 0xc5, 0x5b, 0x12, 0x9b, 0x41, 0x4a, 0x62, 0x2c, 0x61, 0x9f,
	0xac, 0x21, 0x3d, 0xe2, 0x5c, 0x12, 0x2c, 0x65, 0x2d, 0xa8, 0x7a, 0x17, 0x31, 0xf3, 0x82, 0x58,
	0xcf, 0x72, 0xaa, 0xf1, 0x0b, 0xd0, 0x57, 0xce, 0xbe, 0x09, 0xe7, 0x5e, 0xdf, 0x77, 0xf1, 0xf4,
	0x03, 0x28, 0x2f, 0x7c, 0xd7, 0xc6, 0x5a, 0xb8, 0xd4, 0xa8, 0xb4, 0xf0, 0x5d, 0xcb, 0x33, 0x3e,
	0x81, 0x07, 0x2b, 0x07, 0x47, 0x64, 0x4e, 0x9c, 0x84, 0x6c, 0x75, 0xf6, 0xcf, 0xe5, 0xf5, 0xbb,
	0x5c, 0x92, 0x20, 0xc5, 0x73, 0x34, 0x1b, 0x96, 0x61, 0x2b, 0xea, 0x5c, 0x73, 0xf3, 0x80, 0xcd,
	0xbb, 0x87, 0x2c, 0xe0, 0xbc, 0x7b, 0xf0, 0xea, 0x5d, 0x58, 0xaf, 0xde, 0x7b, 0x50, 0x22, 0xf4,
	0x5f, 0xb0, 0x4d, 0x64, 0x0e, 0x60, 0x24, 0xde, 0x5b, 0x4a, 0x6b, 0xbd, 0x85, 0x06, 0x33, 0x57,
	0xc5, 0xf3, 0x93, 0x68, 0xee, 0x5c, 0x51, 0x95, 0xc4, 0x0a, 0xab, 0x72, 0xbc, 0xcb, 0x60, 0xcb,
	0xa3, 0x85, 0x2b, 0x3b, 0x83, 0x49, 0x2f, 0x56, 0x57, 0x85, 0x23, 0x98, 0xf5, 0x9f, 0x42, 0x83,
	0x50, 0x07, 0x66, 0x49, 0xaa, 0x57, 0x5b, 0xd2, 0x33, 0xe5, 0xe8, 0xc9, 0xc1, 0x4a, 0xdf, 0x3c,
	0xd8, 0xec, 0xf1, 0x51, 0x1d, 0xcf, 0x72, 0x90, 0xca, 0x9a, 0x53, 0x97, 0xe7, 0xb2, 0x6a, 0x77,
	0xcb, 0xca, 0x63, 0x64, 0x54, 0xc7, 0xb3, 0x99, 0xac, 0x97, 0x50, 0x3d, 0x0b, 0xe7, 0x9e, 0xbd,
	0xf0, 0x5d, 0xac, 0xdb, 0xca, 0xd1, 0xd3, 0xdb, 0xc4, 0x08, 0xe1, 0x32, 0xaa, 0x9c, 0xb1, 0x0f,
	0xed, 0x33, 0x50, 0x62, 0x16, 0x0d, 0x28, 0x46, 0x41, 0x31, 0xcf, 0x6f, 0x13, 0xb3, 0x1a, 0x3c,
	0x23, 0x88, 0xf3, 0x6f, 0xb1, 0xeb, 0xd6, 0x37, 0x75, 0x5d, 0x3a, 0x2d, 0x44, 0x2b, 0x85, 0x5f,
	0xf6, 0x23, 0xda, 0x96, 0x30, 0x23, 0x9b, 0x42, 0xb6, 0x21, 0x25, 0x6f, 0x58, 0x3b, 0xb7, 0x34,
	0x2c, 0xf5, 0xd6, 0x86, 0xb5, 0xbb, 0x45, 0xc3, 0xd2, 0x6e, 0x68, 0x58, 0xc6, 0x10, 0x6a, 0x18,
	0xec, 0x78, 0x6a, 0x17, 0x1a, 0xe6, 0xf1, 0xc4, 0x1c, 0xd9, 0x9d, 0x37, 0xed, 0xe3, 0x63, 0xb3,
	0xa7, 0x4a, 0x94, 0xd4, 0x33, 0xdb, 0x27, 0x66, 0x4e, 0x92, 0xb5, 0x3a, 0x54, 0xdf, 0x0c, 0x7a,
	0x5d, 0xbb, 0x6f, 0x75, 0xd4, 0x82, 0xb6, 0x03, 0xca, 0xc8, 0xec, 0x99, 0xed, 0xb1, 0x89, 0x84,
	0xa2, 0xf1, 0x9b, 0x22, 0xbc, 0xf7, 0x3a, 0x0e, 0x2f, 0x22, 0x33, 0x48, 0xe3, 0xab, 0x65, 0x26,
	0xdd, 0x34, 0xc6, 0xe4, 0x89, 0x20, 0xdf, 0x94, 0x08, 0x1b, 0x86, 0xac, 0x19, 0xfd, 0x03, 0x1a,
	0xfe, 0x45, 0x71, 0x08, 0x40, 0xaa, 0xe5, 0xd1, 0xa4, 0x65, 0x0c, 0x68, 0x9f, 0x92, 0xd8, 0xc2,
	0x90, 0x9e, 0xb7, 0xc3, 0x98, 0xd0, 0x3e, 0x6b, 0x5f, 0xac, 0xe5, 0x11, 0x70, 0x60, 0xea, 0xb3,
	0x66, 0x44, 0xbf, 0x08, 0x1d, 0x39, 0x2a, 0x62, 0x25, 0x64, 0xe4, 0x36, 0x0e, 0x5a, 0x42, 0x42,
	0x56, 0xc5, 0xbf, 0xf3, 0xf2, 0x4c, 0xd4, 0xa1, 0x88, 0xad, 0x61, 0x65, 0x30, 0xc1, 0xb6, 0x20,
	0x44, 0x14, 0x6c, 0x8a, 0x28, 0xb1, 0xb1, 0x28, 0x1b, 0x1b, 0x8b, 0x0e, 0xc5, 0x38, 0x9c, 0x13,
	0x0c, 0xc8, 0xec, 0xaf, 0x91, 0x82, 0x83, 0x46, 0x70, 0xe9, 0xd3, 0xc4, 0xa6, 0x97, 0x6c, 0xac,
	0x0c, 0x1a, 0x0c, 0xa0, 0x97, 0xfc, 0x11, 0xec, 0x64, 0x6c, 0x99, 0x2a, 0x4d, 0xe1, 0x9f, 0x9a,
	0x1c, 0x6c, 0x33, 0xcc, 0x78, 0x01, 0x35, 0x74, 0x2e, 0xda, 0xb1, 0x09, 0xf0, 0xe9, 0xc0, 0x3a,
	0xb6, 0x5f, 0x8f, 0x06, 0xd3, 0xa1, 0x2a, 0xd1, 0xef, 0x5f, 0x4d, 0xad, 0x09, 0xff, 0x96, 0x8d,
	0x7f, 0x17, 0xe0, 0x3e, 0xc6, 0x03, 0xcd, 0xb0, 0x19, 0xb9, 0x3b, 0x20, 0x44, 0xf7, 0xca, 0x9b,
	0xdc, 0x4b, 0x3d, 0x87, 0xb2, 0x96, 0x93, 0xed, 0xb2, 0xf9, 0x23, 0x80, 0x8a, 0xb1, 0xe0, 0x29,
	0xae, 0x05, 0xcf, 0x56, 0xb1, 0xb1, 0xe2, 0xf4, 0xf2, 0x16, 0x4e, 0xaf, 0xdc, 0xee, 0xf4, 0xea,
	0x6d, 0x4e, 0xaf, 0xdd, 0xe5, 0x74, 0xd8, 0xe8, 0x74, 0x56, 0x68, 0x94, 0x1b, 0x0a, 0x4d, 0xfd,
	0xc6, 0x42, 0xd3, 0xb8, 0x56, 0x68, 0x56, 0xc6, 0xcc, 0xe6, 0xa6, 0x31, 0xd3, 0xf8, 0x31, 0x40,
	0x67, 0x69, 0x59, 0x15, 0xea, 0x9d, 0x91, 0xd9, 0x9e, 0x98, 0xb9, 0xd3, 0x77, 0xa1, 0xd1, 0xb5,
	0xc6, 0x7d, 0x6b, 0x3c, 0xce, 0xfd, 0xfe, 0x75, 0x01, 0xee, 0xbf, 0x8a, 0x7d, 0x12, 0x78, 0xc9,
	0x99, 0xcf, 0x9d, 0x7f, 0xab, 0xdf, 0x1f, 0x03, 0xa4, 0x4e, 0x3c, 0x23, 0x29, 0x86, 0xaa, 0xe8,
	0xf9, 0x1a, 0xa3, 0xd3, 0x48, 0xfd, 0x8e, 0xbe, 0x97, 0x56, 0x7c, 0x2f, 0x18, 0xbd, 0x74, 0x97,
	0xd1, 0xcb, 0x1b, 0x8d, 0xfe, 0x02, 0x9a, 0x5c, 0xc7, 0x4c, 0x90, 0xd8, 0x51, 0x1b, 0x0c, 0xe3,
	0x69, 0x42, 0xb3, 0x8a, 0x33, 0xe7, 0x52, 0xc5, 0x40, 0xe0, 0x92, 0x8e, 0x33, 0xd9, 0x4f, 0xa1,
	0xce, 0xd9, 0xd9, 0x8c, 0x29, 0xc6, 0x85, 0xc2, 0x90, 0x21, 0x05, 0x8c, 0xde, 0x8a, 0x33, 0x9a,
	0x00, 0xed, 0x6e, 0xd7, 0x7e, 0x35, 0xb2, 0xcc, 0xe3, 0x2e, 0xcb, 0xbf, 0xae, 0xd9, 0xcb, 0xbe,
	0xe5, 0x1c, 0x1f, 0xf4, 0x7a, 0x83, 0xcf, 0xd5, 0x42, 0x8e, 0xb3, 0xef, 0xa2, 0xf1, 0x14, 0x94,
	0x09, 0xf9, 0x32, 0xed, 0x84, 0x41, 0x4a, 0x4b, 0xae, 0x0e, 0x45, 0x5c, 0x4e, 0x25, 0x61, 0x39,
	0x45, 0x8a, 0xf1, 0x8d, 0x04, 0x75, 0x6b, 0xe1, 0xcc, 0xc8, 0x9d, 0xac, 0x14, 0x41, 0xf7, 0x88,
	0x25, 0x1d, 0x29, 0xf4, 0x92, 0x3e, 0x95, 0x61, 0x9f, 0x86, 0xf1, 0x82, 0xd7, 0xf6, 0xfc, 0x92,
	0x88, 0xbc, 0x42, 0x80, 0x46, 0xc9, 0x39, 0xb9, 0x5a, 0xd9, 0x46, 0x29, 0xc1, 0xf8, 0x10, 0x8a,
	0x78, 0xed, 0x0a, 0x14, 0x46, 0xed, 0xcf, 0x55, 0x89, 0xfe, 0x98, 0x8e, 0x68, 0x5f, 0xaa, 0x40,
	0xe1, 0x33, 0xf3, 0x0b, 0xb5, 0x60, 0xfc, 0x4e, 0x82, 0xfa, 0x49, 0xe8, 0xbb, 0x5b, 0x28, 0xba,
	0x07, 0x25, 0x37, 0xf4, 0x88, 0x8b, 0x1b, 0x74, 0xde, 0x7c, 0x90, 0x94, 0x69, 0x50, 0x58, 0xd7,
	0xe0, 0xe7, 0x50, 0x6d, 0x9f, 0xd8, 0x9d, 0x41, 0xd7, 0xec, 0x68, 0x1a, 0x34, 0xe9, 0xef, 0xe9,
	0x78, 0x32, 0xe8, 0xdb, 0x83, 0xe1, 0x74, 0xac, 0x4a, 0x1a, 0x40, 0xb9, 0x7d, 0x62, 0x0f, 0x3b,
	0x7d, 0x55, 0xe6, 0xbf, 0xfb, 0xc3, 0x9f, 0xa8, 0x05, 0xe3, 0x4f, 0x25, 0x28, 0x5b, 0x7d, 0x4c,
	0x81, 0x47, 0x50, 0x3d, 0x8d, 0xc3, 0x85, 0xbd, 0x9e, 0x07, 0x15, 0x4a, 0xa5, 0x61, 0xfe, 0x14,
	0xea, 0xc8, 0xb0, 0x69, 0xc1, 0x57, 0x28, 0x92, 0xc5, 0xd8, 0x13, 0xe0, 0xa1, 0xb1, 0xcc, 0x87,
	0xbc, 0xc0, 0x33, 0x20, 0x2b, 0x68, 0x9c, 0x6d, 0xad, 0x67, 0x56, 0x19, 0xd9, 0xf2, 0x36, 0x84,
	0x76, 0xe9, 0xe6, 0xd0, 0x66, 0xf9, 0x55, 0x5e, 0xab, 0xad, 0x74, 0xda, 0x64, 0x26, 0xcf, 0xa6,
	0xcd, 0x25, 0xae, 0x70, 0x04, 0xd5, 0xd9, 0x87, 0x0a, 0xff, 0xc4, 0x2d, 0x3e, 0x7f, 0x04, 0xe1,
	0x44, 0xaa, 0x6e, 0x42, 0x02, 0x8f, 0xc4, 0x74, 0xd1, 0x11, 0xf3, 0xa0, 0xca, 0xc8, 0x6c, 0x03,
	0xe4, 0x2c, 0x58, 0xef, 0x40, 0xdc, 0x00, 0x19, 0x80, 0xbb, 0xce, 0x01, 0xa8, 0x9c, 0x6d, 0x59,
	0xe2, 0xc4, 0x9a, 0xd9, 0x64, 0x68, 0x37, 0xdb, 0xa7, 0x97, 0x62, 0xb1, 0x58, 0x8a, 0x23, 0x1e,
	0x17, 0x6b, 0xf1, 0xd9, 0x6c, 0x91, 0xcc, 0x6c, 0xde, 0x52, 0x0b, 0xf9, 0x3a, 0x91, 0xcc, 0xd8,
	0x4e, 0x8e, 0xce, 0xcb, 0xb3, 0x5e, 0xac, 0xa9, 0xe8, 0xd7, 0x3c, 0xe7, 0x37, 0x94, 0x88, 0x9d,
	0x5b, 0x4a, 0xc4, 0x01, 0xa8, 0xac, 0x79, 0x09, 0xad, 0x47, 0x5c, 0xf8, 0x9b, 0x88, 0x2e, 0xc7,
	0xff, 0x1f, 0xc2, 0x0e, 0x55, 0x33, 0x09, 0x2f, 0x62, 0x97, 0x2c, 0xa7, 0xc5, 0x8c, 0xbd, 0xb1,
	0x48, 0x66, 0x63, 0xc4, 0xa8, 0x57, 0x8c, 0x27, 0x50, 0x9d, 0xb0, 0x90, 0x09, 0x35, 0x05, 0x2a,
	0x93, 0x81, 0x3d, 0x1d, 0x9b, 0x23, 0x55, 0xa2, 0x13, 0xdf, 0x64, 0x90, 0x17, 0xf6, 0xbf, 0xca,
	0xd0, 0xc8, 0x96, 0xf5, 0xc5, 0xff, 0x39, 0x9c, 0x57, 0xd7, 0xad, 0xc2, 0x1d, 0xeb, 0x56, 0xf1,
	0xfa, 0xba, 0xb5, 0x79, 0x49, 0x2a, 0x7d, 0xa7, 0x25, 0xa9, 0x7c, 0xd3, 0x92, 0xc4, 0xa2, 0xbe,
	0x72, 0x47, 0xd4, 0x57, 0xb7, 0x88, 0xfa, 0xda, 0x86, 0xa8, 0x37, 0xfe, 0x25, 0x81, 0x32, 0x9d,
	0xb9, 0xd3, 0x84, 0xc4, 0xf8, 0x4a, 0xf4, 0xdf, 0x3e, 0xfc, 0xe5, 0x2f, 0x18, 0x85, 0xeb, 0x2f,
	0x18, 0x62, 0x8b, 0x2b, 0x6e, 0x6c, 0x71, 0x2b, 0xaf, 0x2f, 0xa5, 0x6d, 0x5e, 0x5f, 0xca, 0x37,
	0xbc, 0xbe, 0x64, 0x13, 0x47, 0x65, 0x7d, 0xe2, 0x30, 0xfe, 0x28, 0xc1, 0xee, 0x74, 0xe6, 0xb6,
	0xd3, 0xd4, 0x71, 0xcf, 0x16, 0x54, 0x28, 0xbd, 0xaf, 0xc5, 0xbb, 0x06, 0xbd, 0x70, 0xf3, 0xe8,
	0x70, 0x6d, 0x41, 0xbb, 0xc6, 0x7f, 0xb0, 0xfc, 0xa4, 0xe6, 0x5d, 0x69, 0x33, 0x82, 0xa9, 0xc5,
	0x87, 0xdb, 0xdc, 0xd4, 0x2f, 0xa1, 0xb9, 0x7a, 0x5a, 0xab, 0x42, 0xf1, 0x38, 0x0c, 0x88, 0x7a,
	0x4f, 0xab, 0x41, 0xc9, 0xea, 0xb7, 0x5f, 0x9b, 0xac, 0xb7, 0xbc, 0xb6, 0x5e, 0xa9, 0x32, 0xa5,
	0x9d, 0x58, 0x5d, 0x73, 0xa0, 0x16, 0x28, 0xad, 0xd3, 0x1f, 0xab, 0x45, 0xe3, 0x6b, 0x19, 0xea,
	0xd3, 0x99, 0x3b, 0x0c, 0x93, 0x14, 0x87, 0x5a, 0xdc, 0xc9, 0xf8, 0x5c, 0x29, 0x89, 0xef, 0xa3,
	0x21, 0x1b, 0x2a, 0x1f, 0x42, 0x25, 0x0a, 0x93, 0xd4, 0x5e, 0x7b, 0x4c, 0x2e, 0x53, 0xa2, 0xe5,
	0x89, 0x2a, 0x8b, 0x7e, 0xcb, 0x6b, 0xe2, 0x4b, 0x50, 0x9c, 0x5c, 0xe5, 0x44, 0x2f, 0xb6, 0x0a,
	0xcf, 0x94, 0xa3, 0xd6, 0x5d, 0x46, 0x1a, 0x89, 0x87, 0x56, 0xe7, 0xda, 0xd2, 0xc6, 0xb9, 0xf6,
	0xa7, 0x50, 0xe1, 0xdb, 0x0f, 0xfa, 0x55, 0x39, 0xda, 0xbb, 0xfe, 0x17, 0x59, 0x84, 0x8e, 0x32,
	0x56, 0x63, 0x0f, 0x2a, 0x83, 0xa1, 0x3d, 0xf9, 0x62, 0x68, 0xd2, 0x85, 0x70, 0x38, 0x18, 0x4f,
	0xec, 0xc1, 0xd0, 0x6e, 0x77, 0xbb, 0xaa, 0x64, 0xfc, 0xa1, 0x00, 0x3b, 0xd3, 0x99, 0xdb, 0x09,
	0x17, 0x54, 0x89, 0xad, 0x4c, 0x45, 0xcb, 0x00, 0x63, 0x5f, 0xb7, 0x56, 0x8d, 0xd3, 0x2d, 0x8f,
	0xd6, 0xe0, 0x98, 0x9c, 0xb2, 0x3a, 0x21, 0xc4, 0x79, 0x4c, 0x4e, 0x2d, 0x4f, 0x34, 0xb6, 0x18,
	0xe6, 0x1b, 0x8c, 0x5d, 0xda, 0xc2, 0xd8, 0xe5, 0xff, 0xd9, 0xd8, 0x95, 0xbb, 0x8c, 0x5d, 0xdd,
	0xda, 0xd8, 0xda, 0xcf, 0xa0, 0x1a, 0x93, 0x68, 0x7e, 0x65, 0xa7, 0x21, 0x7f, 0x5a, 0xb9, 0xf5,
	0x18, 0xf2, 0x4e, 0x42, 0xe3, 0xe1, 0xd2, 0x47, 0x1a, 0x34, 0x3b, 0x83, 0x7e, 0xdf, 0x3c, 0x16,
	0xdc, 0xf4, 0x4f, 0x09, 0xc3, 0x79, 0xe5, 0xe9, 0x0b, 0xb7, 0xf0, 0xeb, 0x6e, 0xaa, 0x91, 0xfc,
	0xc9, 0x80, 0x95, 0x44, 0x79, 0xad, 0x24, 0x1e, 0xd2, 0x2d, 0x24, 0x61, 0x81, 0xac, 0x1c, 0x3d,
	0xb8, 0xae, 0x5d, 0x9e, 0x34, 0x23, 0x64, 0xd4, 0x7e, 0x49, 0xfd, 0x81, 0x8e, 0x45, 0x77, 0x29,
	0x47, 0xfb, 0xd7, 0xcf, 0x88, 0x01, 0x34, 0xca, 0xd8, 0x8d, 0x8f, 0xc5, 0x07, 0x8c, 0x1d, 0x50,
	0xa6, 0xaf, 0x3b, 0xb6, 0x39, 0xb1, 0x69, 0x08, 0xaa, 0x12, 0xbd, 0x28, 0x27, 0xf0, 0xfb, 0xaa,
	0xf2, 0xf3, 0x00, 0x94, 0x8e, 0x50, 0x95, 0x9b, 0x00, 0x9d, 0x89, 0x6d, 0x1d, 0x9f, 0xb4, 0x7b,
	0x56, 0x57, 0xbd, 0x47, 0x3b, 0x5f, 0x67, 0x62, 0x4f, 0xcc, 0x5f, 0x4f, 0x58, 0xe7, 0xa3, 0x20,
	0x96, 0x03, 0x99, 0x7f, 0x9d, 0x0c, 0xac, 0x8e, 0xa9, 0x16, 0xd0, 0x88, 0x8c, 0xd1, 0x6e, 0x4f,
	0xec, 0xc1, 0xb1, 0xa9, 0x16, 0xd7, 0x68, 0xed, 0x5e, 0x4f, 0x2d, 0xbd, 0x54, 0xbf, 0x7d, 0xb7,
	0x2f, 0xfd, 0xe5, 0xdd, 0xbe, 0xf4, 0xf7, 0x77, 0xfb, 0xd2, 0x6f, 0xff, 0xb1, 0x7f, 0xef, 0x3f,
	0x01, 0x00, 0x00, 0xff, 0xff, 0xfc, 0xaa, 0xd1, 0x94, 0xb9, 0x1a, 0x00, 0x00,
}
