// Code generated by protoc-gen-gogo.
// source: src/channelmemberVipRank/channelmemberviprank.proto
// DO NOT EDIT!

/*
	Package channelmemberviprank is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/channelmemberVipRank/channelmemberviprank.proto

	It has these top-level messages:
		ChannelMemberVip
		ChannelMemberRank
		GetMemberRankListReq
		GetMemberRankListResp
		MemberConsumeInfo
		GetConsumeTopNReq
		GetConsumeTopNResp
		GetUserConsumeInfoReq
		GetUserConsumeInfoResp
		BatGetUserConsumeInfoReq
		BatGetUserConsumeInfoResp
		RemoveOnlineMemberRankReq
		RemoveOnlineMemberRankResp
		SendCommBreakingNewsReq
		SendCommBreakingNewsResp
		OperAddUserChannelTCoinReq
		OperAddUserChannelTCoinResp
		GetUnderTheMircoRankReq
		GetUnderTheMircoRankResp
		GetUnderTheMircoOnlineCntReq
		SimpleChannelOnlineCount
		GetUnderTheMircoOnlineCntResp
		GetMemberWeekRankListReq
		GetMemberWeekRankListResp
		ChannelHourRankInfo
		GetChannelHourRankListReq
		GetChannelHourRankListResp
		GetHourRankByIdReq
		GetHourRankByIdResp
		IncrChannelHourRankScoreReq
		IncrChannelHourRankScoreResp
		IncrMemberWeekRankScoreReq
		IncrMemberWeekRankScoreResp
		UpdateUserTempInvisibleStatusReq
		UpdateUserTempInvisibleStatusResp
		GetMemberNobilityInfoReq
		GetMemberNobilityInfoResp
		RemoveLiveChannelOnlineRankReq
		RemoveLiveChannelOnlineRankResp
		HideUserChannelConsumeReq
		HideUserChannelConsumeResp
*/
package channelmemberviprank

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// 小时榜类型 与ga::ChannelHourRankType保持一致
type ChannelHourRankType int32

const (
	ChannelHourRankType_TOTAL ChannelHourRankType = 0
)

var ChannelHourRankType_name = map[int32]string{
	0: "TOTAL",
}
var ChannelHourRankType_value = map[string]int32{
	"TOTAL": 0,
}

func (x ChannelHourRankType) Enum() *ChannelHourRankType {
	p := new(ChannelHourRankType)
	*p = x
	return p
}
func (x ChannelHourRankType) String() string {
	return proto.EnumName(ChannelHourRankType_name, int32(x))
}
func (x *ChannelHourRankType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ChannelHourRankType_value, data, "ChannelHourRankType")
	if err != nil {
		return err
	}
	*x = ChannelHourRankType(value)
	return nil
}
func (ChannelHourRankType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{0}
}

type ChannelMemberVip struct {
	CurrLevelId       uint32 `protobuf:"varint,1,opt,name=curr_level_id,json=currLevelId" json:"curr_level_id"`
	CurrLevelName     string `protobuf:"bytes,2,opt,name=curr_level_name,json=currLevelName" json:"curr_level_name"`
	CurrLevelValue    uint32 `protobuf:"varint,3,opt,name=curr_level_value,json=currLevelValue" json:"curr_level_value"`
	NextLevelId       uint32 `protobuf:"varint,4,opt,name=next_level_id,json=nextLevelId" json:"next_level_id"`
	NextLevelName     string `protobuf:"bytes,5,opt,name=next_level_name,json=nextLevelName" json:"next_level_name"`
	NextLevelMinValue uint32 `protobuf:"varint,6,opt,name=next_level_min_value,json=nextLevelMinValue" json:"next_level_min_value"`
	NobilityLevel     uint32 `protobuf:"varint,7,opt,name=nobility_level,json=nobilityLevel" json:"nobility_level"`
	Invisible         bool   `protobuf:"varint,8,opt,name=invisible" json:"invisible"`
}

func (m *ChannelMemberVip) Reset()         { *m = ChannelMemberVip{} }
func (m *ChannelMemberVip) String() string { return proto.CompactTextString(m) }
func (*ChannelMemberVip) ProtoMessage()    {}
func (*ChannelMemberVip) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{0}
}

func (m *ChannelMemberVip) GetCurrLevelId() uint32 {
	if m != nil {
		return m.CurrLevelId
	}
	return 0
}

func (m *ChannelMemberVip) GetCurrLevelName() string {
	if m != nil {
		return m.CurrLevelName
	}
	return ""
}

func (m *ChannelMemberVip) GetCurrLevelValue() uint32 {
	if m != nil {
		return m.CurrLevelValue
	}
	return 0
}

func (m *ChannelMemberVip) GetNextLevelId() uint32 {
	if m != nil {
		return m.NextLevelId
	}
	return 0
}

func (m *ChannelMemberVip) GetNextLevelName() string {
	if m != nil {
		return m.NextLevelName
	}
	return ""
}

func (m *ChannelMemberVip) GetNextLevelMinValue() uint32 {
	if m != nil {
		return m.NextLevelMinValue
	}
	return 0
}

func (m *ChannelMemberVip) GetNobilityLevel() uint32 {
	if m != nil {
		return m.NobilityLevel
	}
	return 0
}

func (m *ChannelMemberVip) GetInvisible() bool {
	if m != nil {
		return m.Invisible
	}
	return false
}

// 房间成员排名信息
type ChannelMemberRank struct {
	Uid          uint32            `protobuf:"varint,1,req,name=uid" json:"uid"`
	Ts           uint32            `protobuf:"varint,2,opt,name=ts" json:"ts"`
	RankValue    uint32            `protobuf:"varint,3,opt,name=rank_value,json=rankValue" json:"rank_value"`
	VipLevelInfo *ChannelMemberVip `protobuf:"bytes,4,opt,name=vip_level_info,json=vipLevelInfo" json:"vip_level_info,omitempty"`
	Rank         uint32            `protobuf:"varint,5,opt,name=rank" json:"rank"`
	TotalConsum  uint32            `protobuf:"varint,6,opt,name=total_consum,json=totalConsum" json:"total_consum"`
}

func (m *ChannelMemberRank) Reset()         { *m = ChannelMemberRank{} }
func (m *ChannelMemberRank) String() string { return proto.CompactTextString(m) }
func (*ChannelMemberRank) ProtoMessage()    {}
func (*ChannelMemberRank) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{1}
}

func (m *ChannelMemberRank) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelMemberRank) GetTs() uint32 {
	if m != nil {
		return m.Ts
	}
	return 0
}

func (m *ChannelMemberRank) GetRankValue() uint32 {
	if m != nil {
		return m.RankValue
	}
	return 0
}

func (m *ChannelMemberRank) GetVipLevelInfo() *ChannelMemberVip {
	if m != nil {
		return m.VipLevelInfo
	}
	return nil
}

func (m *ChannelMemberRank) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *ChannelMemberRank) GetTotalConsum() uint32 {
	if m != nil {
		return m.TotalConsum
	}
	return 0
}

// 获取频道成员排序列表
type GetMemberRankListReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	OpUid     uint32 `protobuf:"varint,2,req,name=op_uid,json=opUid" json:"op_uid"`
	StartIdx  uint32 `protobuf:"varint,3,req,name=start_idx,json=startIdx" json:"start_idx"`
	Getsize   uint32 `protobuf:"varint,4,req,name=getsize" json:"getsize"`
	// 需要获取自己的排名信息才填以下字段
	Uid            uint32 `protobuf:"varint,5,opt,name=uid" json:"uid"`
	ViewCnt        uint32 `protobuf:"varint,6,opt,name=view_cnt,json=viewCnt" json:"view_cnt"`
	IsTestMod      bool   `protobuf:"varint,7,opt,name=is_test_mod,json=isTestMod" json:"is_test_mod"`
	InvisibleVaild bool   `protobuf:"varint,8,opt,name=invisible_vaild,json=invisibleVaild" json:"invisible_vaild"`
	IsLiveChannel  bool   `protobuf:"varint,9,opt,name=is_live_channel,json=isLiveChannel" json:"is_live_channel"`
}

func (m *GetMemberRankListReq) Reset()         { *m = GetMemberRankListReq{} }
func (m *GetMemberRankListReq) String() string { return proto.CompactTextString(m) }
func (*GetMemberRankListReq) ProtoMessage()    {}
func (*GetMemberRankListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{2}
}

func (m *GetMemberRankListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetMemberRankListReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *GetMemberRankListReq) GetStartIdx() uint32 {
	if m != nil {
		return m.StartIdx
	}
	return 0
}

func (m *GetMemberRankListReq) GetGetsize() uint32 {
	if m != nil {
		return m.Getsize
	}
	return 0
}

func (m *GetMemberRankListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetMemberRankListReq) GetViewCnt() uint32 {
	if m != nil {
		return m.ViewCnt
	}
	return 0
}

func (m *GetMemberRankListReq) GetIsTestMod() bool {
	if m != nil {
		return m.IsTestMod
	}
	return false
}

func (m *GetMemberRankListReq) GetInvisibleVaild() bool {
	if m != nil {
		return m.InvisibleVaild
	}
	return false
}

func (m *GetMemberRankListReq) GetIsLiveChannel() bool {
	if m != nil {
		return m.IsLiveChannel
	}
	return false
}

type GetMemberRankListResp struct {
	ChannelId     uint32               `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	MemberList    []*ChannelMemberRank `protobuf:"bytes,2,rep,name=member_list,json=memberList" json:"member_list,omitempty"`
	AllMemberSize uint32               `protobuf:"varint,3,req,name=all_member_size,json=allMemberSize" json:"all_member_size"`
	MyRankInfo    *ChannelMemberRank   `protobuf:"bytes,4,opt,name=my_rank_info,json=myRankInfo" json:"my_rank_info,omitempty"`
	DValue        uint32               `protobuf:"varint,5,opt,name=d_value,json=dValue" json:"d_value"`
	Rank          uint32               `protobuf:"varint,6,opt,name=rank" json:"rank"`
}

func (m *GetMemberRankListResp) Reset()         { *m = GetMemberRankListResp{} }
func (m *GetMemberRankListResp) String() string { return proto.CompactTextString(m) }
func (*GetMemberRankListResp) ProtoMessage()    {}
func (*GetMemberRankListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{3}
}

func (m *GetMemberRankListResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetMemberRankListResp) GetMemberList() []*ChannelMemberRank {
	if m != nil {
		return m.MemberList
	}
	return nil
}

func (m *GetMemberRankListResp) GetAllMemberSize() uint32 {
	if m != nil {
		return m.AllMemberSize
	}
	return 0
}

func (m *GetMemberRankListResp) GetMyRankInfo() *ChannelMemberRank {
	if m != nil {
		return m.MyRankInfo
	}
	return nil
}

func (m *GetMemberRankListResp) GetDValue() uint32 {
	if m != nil {
		return m.DValue
	}
	return 0
}

func (m *GetMemberRankListResp) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

// 获取房间消费土豪榜
type MemberConsumeInfo struct {
	Uid                 uint32            `protobuf:"varint,1,req,name=uid" json:"uid"`
	ConsumeCnt          uint32            `protobuf:"varint,2,req,name=consume_cnt,json=consumeCnt" json:"consume_cnt"`
	VipLevelInfo        *ChannelMemberVip `protobuf:"bytes,3,opt,name=vip_level_info,json=vipLevelInfo" json:"vip_level_info,omitempty"`
	IsAutoHiddenConsume bool              `protobuf:"varint,4,opt,name=is_auto_hidden_consume,json=isAutoHiddenConsume" json:"is_auto_hidden_consume"`
}

func (m *MemberConsumeInfo) Reset()         { *m = MemberConsumeInfo{} }
func (m *MemberConsumeInfo) String() string { return proto.CompactTextString(m) }
func (*MemberConsumeInfo) ProtoMessage()    {}
func (*MemberConsumeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{4}
}

func (m *MemberConsumeInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MemberConsumeInfo) GetConsumeCnt() uint32 {
	if m != nil {
		return m.ConsumeCnt
	}
	return 0
}

func (m *MemberConsumeInfo) GetVipLevelInfo() *ChannelMemberVip {
	if m != nil {
		return m.VipLevelInfo
	}
	return nil
}

func (m *MemberConsumeInfo) GetIsAutoHiddenConsume() bool {
	if m != nil {
		return m.IsAutoHiddenConsume
	}
	return false
}

type GetConsumeTopNReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	BeginIdx  uint32 `protobuf:"varint,2,req,name=begin_idx,json=beginIdx" json:"begin_idx"`
	Limit     uint32 `protobuf:"varint,3,req,name=limit" json:"limit"`
	// 需要获取自己的排名信息才填以下字段
	Uid          uint32 `protobuf:"varint,4,opt,name=uid" json:"uid"`
	ViewCnt      uint32 `protobuf:"varint,5,opt,name=view_cnt,json=viewCnt" json:"view_cnt"`
	IsShowHidden bool   `protobuf:"varint,6,opt,name=is_show_hidden,json=isShowHidden" json:"is_show_hidden"`
	ChannelType  uint32 `protobuf:"varint,7,opt,name=channel_type,json=channelType" json:"channel_type"`
}

func (m *GetConsumeTopNReq) Reset()         { *m = GetConsumeTopNReq{} }
func (m *GetConsumeTopNReq) String() string { return proto.CompactTextString(m) }
func (*GetConsumeTopNReq) ProtoMessage()    {}
func (*GetConsumeTopNReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{5}
}

func (m *GetConsumeTopNReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetConsumeTopNReq) GetBeginIdx() uint32 {
	if m != nil {
		return m.BeginIdx
	}
	return 0
}

func (m *GetConsumeTopNReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetConsumeTopNReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetConsumeTopNReq) GetViewCnt() uint32 {
	if m != nil {
		return m.ViewCnt
	}
	return 0
}

func (m *GetConsumeTopNReq) GetIsShowHidden() bool {
	if m != nil {
		return m.IsShowHidden
	}
	return false
}

func (m *GetConsumeTopNReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

type GetConsumeTopNResp struct {
	ConsumeList         []*MemberConsumeInfo `protobuf:"bytes,1,rep,name=consume_list,json=consumeList" json:"consume_list,omitempty"`
	TotalCnt            uint32               `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt" json:"total_cnt"`
	MyRankInfo          *MemberConsumeInfo   `protobuf:"bytes,3,opt,name=my_rank_info,json=myRankInfo" json:"my_rank_info,omitempty"`
	DValue              uint32               `protobuf:"varint,4,opt,name=d_value,json=dValue" json:"d_value"`
	Rank                uint32               `protobuf:"varint,5,opt,name=rank" json:"rank"`
	IsCanHideConsume    bool                 `protobuf:"varint,6,opt,name=is_can_hide_consume,json=isCanHideConsume" json:"is_can_hide_consume"`
	IsHiddenConsume     bool                 `protobuf:"varint,7,opt,name=is_hidden_consume,json=isHiddenConsume" json:"is_hidden_consume"`
	IsAutoHiddenConsume bool                 `protobuf:"varint,8,opt,name=is_auto_hidden_consume,json=isAutoHiddenConsume" json:"is_auto_hidden_consume"`
}

func (m *GetConsumeTopNResp) Reset()         { *m = GetConsumeTopNResp{} }
func (m *GetConsumeTopNResp) String() string { return proto.CompactTextString(m) }
func (*GetConsumeTopNResp) ProtoMessage()    {}
func (*GetConsumeTopNResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{6}
}

func (m *GetConsumeTopNResp) GetConsumeList() []*MemberConsumeInfo {
	if m != nil {
		return m.ConsumeList
	}
	return nil
}

func (m *GetConsumeTopNResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

func (m *GetConsumeTopNResp) GetMyRankInfo() *MemberConsumeInfo {
	if m != nil {
		return m.MyRankInfo
	}
	return nil
}

func (m *GetConsumeTopNResp) GetDValue() uint32 {
	if m != nil {
		return m.DValue
	}
	return 0
}

func (m *GetConsumeTopNResp) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *GetConsumeTopNResp) GetIsCanHideConsume() bool {
	if m != nil {
		return m.IsCanHideConsume
	}
	return false
}

func (m *GetConsumeTopNResp) GetIsHiddenConsume() bool {
	if m != nil {
		return m.IsHiddenConsume
	}
	return false
}

func (m *GetConsumeTopNResp) GetIsAutoHiddenConsume() bool {
	if m != nil {
		return m.IsAutoHiddenConsume
	}
	return false
}

// 获取指定用户的消费信息信息
type GetUserConsumeInfoReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	Uid       uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
}

func (m *GetUserConsumeInfoReq) Reset()         { *m = GetUserConsumeInfoReq{} }
func (m *GetUserConsumeInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserConsumeInfoReq) ProtoMessage()    {}
func (*GetUserConsumeInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{7}
}

func (m *GetUserConsumeInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetUserConsumeInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserConsumeInfoResp struct {
	ConsumeInfo *MemberConsumeInfo `protobuf:"bytes,1,req,name=consume_info,json=consumeInfo" json:"consume_info,omitempty"`
}

func (m *GetUserConsumeInfoResp) Reset()         { *m = GetUserConsumeInfoResp{} }
func (m *GetUserConsumeInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserConsumeInfoResp) ProtoMessage()    {}
func (*GetUserConsumeInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{8}
}

func (m *GetUserConsumeInfoResp) GetConsumeInfo() *MemberConsumeInfo {
	if m != nil {
		return m.ConsumeInfo
	}
	return nil
}

type BatGetUserConsumeInfoReq struct {
	ChannelId uint32   `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	UidList   []uint32 `protobuf:"varint,2,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *BatGetUserConsumeInfoReq) Reset()         { *m = BatGetUserConsumeInfoReq{} }
func (m *BatGetUserConsumeInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatGetUserConsumeInfoReq) ProtoMessage()    {}
func (*BatGetUserConsumeInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{9}
}

func (m *BatGetUserConsumeInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BatGetUserConsumeInfoReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatGetUserConsumeInfoResp struct {
	ConsumeInfo []*MemberConsumeInfo `protobuf:"bytes,1,rep,name=consume_info,json=consumeInfo" json:"consume_info,omitempty"`
}

func (m *BatGetUserConsumeInfoResp) Reset()         { *m = BatGetUserConsumeInfoResp{} }
func (m *BatGetUserConsumeInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatGetUserConsumeInfoResp) ProtoMessage()    {}
func (*BatGetUserConsumeInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{10}
}

func (m *BatGetUserConsumeInfoResp) GetConsumeInfo() []*MemberConsumeInfo {
	if m != nil {
		return m.ConsumeInfo
	}
	return nil
}

// 将用户移出房间成员列表 用于修正
type RemoveOnlineMemberRankReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	Uid       uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
}

func (m *RemoveOnlineMemberRankReq) Reset()         { *m = RemoveOnlineMemberRankReq{} }
func (m *RemoveOnlineMemberRankReq) String() string { return proto.CompactTextString(m) }
func (*RemoveOnlineMemberRankReq) ProtoMessage()    {}
func (*RemoveOnlineMemberRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{11}
}

func (m *RemoveOnlineMemberRankReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RemoveOnlineMemberRankReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type RemoveOnlineMemberRankResp struct {
	RemovedUid uint32 `protobuf:"varint,1,opt,name=removed_uid,json=removedUid" json:"removed_uid"`
}

func (m *RemoveOnlineMemberRankResp) Reset()         { *m = RemoveOnlineMemberRankResp{} }
func (m *RemoveOnlineMemberRankResp) String() string { return proto.CompactTextString(m) }
func (*RemoveOnlineMemberRankResp) ProtoMessage()    {}
func (*RemoveOnlineMemberRankResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{12}
}

func (m *RemoveOnlineMemberRankResp) GetRemovedUid() uint32 {
	if m != nil {
		return m.RemovedUid
	}
	return 0
}

// 测试发送广播事件
type SendCommBreakingNewsReq struct {
	ChannelId uint32 `protobuf:"varint,1,opt,name=channel_id,json=channelId" json:"channel_id"`
	Uid       uint32 `protobuf:"varint,2,opt,name=uid" json:"uid"`
}

func (m *SendCommBreakingNewsReq) Reset()         { *m = SendCommBreakingNewsReq{} }
func (m *SendCommBreakingNewsReq) String() string { return proto.CompactTextString(m) }
func (*SendCommBreakingNewsReq) ProtoMessage()    {}
func (*SendCommBreakingNewsReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{13}
}

func (m *SendCommBreakingNewsReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SendCommBreakingNewsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type SendCommBreakingNewsResp struct {
}

func (m *SendCommBreakingNewsResp) Reset()         { *m = SendCommBreakingNewsResp{} }
func (m *SendCommBreakingNewsResp) String() string { return proto.CompactTextString(m) }
func (*SendCommBreakingNewsResp) ProtoMessage()    {}
func (*SendCommBreakingNewsResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{14}
}

// 运维补发房间礼物
type OperAddUserChannelTCoinReq struct {
	ChannelId uint32 `protobuf:"varint,1,opt,name=channel_id,json=channelId" json:"channel_id"`
	Uid       uint32 `protobuf:"varint,2,opt,name=uid" json:"uid"`
	TcoinCnt  uint32 `protobuf:"varint,3,opt,name=tcoin_cnt,json=tcoinCnt" json:"tcoin_cnt"`
	OrderId   string `protobuf:"bytes,4,opt,name=order_id,json=orderId" json:"order_id"`
}

func (m *OperAddUserChannelTCoinReq) Reset()         { *m = OperAddUserChannelTCoinReq{} }
func (m *OperAddUserChannelTCoinReq) String() string { return proto.CompactTextString(m) }
func (*OperAddUserChannelTCoinReq) ProtoMessage()    {}
func (*OperAddUserChannelTCoinReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{15}
}

func (m *OperAddUserChannelTCoinReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *OperAddUserChannelTCoinReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *OperAddUserChannelTCoinReq) GetTcoinCnt() uint32 {
	if m != nil {
		return m.TcoinCnt
	}
	return 0
}

func (m *OperAddUserChannelTCoinReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type OperAddUserChannelTCoinResp struct {
	IsOrderidExist bool `protobuf:"varint,1,opt,name=is_orderid_exist,json=isOrderidExist" json:"is_orderid_exist"`
}

func (m *OperAddUserChannelTCoinResp) Reset()         { *m = OperAddUserChannelTCoinResp{} }
func (m *OperAddUserChannelTCoinResp) String() string { return proto.CompactTextString(m) }
func (*OperAddUserChannelTCoinResp) ProtoMessage()    {}
func (*OperAddUserChannelTCoinResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{16}
}

func (m *OperAddUserChannelTCoinResp) GetIsOrderidExist() bool {
	if m != nil {
		return m.IsOrderidExist
	}
	return false
}

// 麦下用户列表
type GetUnderTheMircoRankReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	Begin     uint32 `protobuf:"varint,2,req,name=begin" json:"begin"`
	Limit     uint32 `protobuf:"varint,3,req,name=limit" json:"limit"`
}

func (m *GetUnderTheMircoRankReq) Reset()         { *m = GetUnderTheMircoRankReq{} }
func (m *GetUnderTheMircoRankReq) String() string { return proto.CompactTextString(m) }
func (*GetUnderTheMircoRankReq) ProtoMessage()    {}
func (*GetUnderTheMircoRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{17}
}

func (m *GetUnderTheMircoRankReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetUnderTheMircoRankReq) GetBegin() uint32 {
	if m != nil {
		return m.Begin
	}
	return 0
}

func (m *GetUnderTheMircoRankReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetUnderTheMircoRankResp struct {
	UidList         []uint32 `protobuf:"varint,1,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
	CurrMemberTotal uint32   `protobuf:"varint,2,req,name=curr_member_total,json=currMemberTotal" json:"curr_member_total"`
}

func (m *GetUnderTheMircoRankResp) Reset()         { *m = GetUnderTheMircoRankResp{} }
func (m *GetUnderTheMircoRankResp) String() string { return proto.CompactTextString(m) }
func (*GetUnderTheMircoRankResp) ProtoMessage()    {}
func (*GetUnderTheMircoRankResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{18}
}

func (m *GetUnderTheMircoRankResp) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetUnderTheMircoRankResp) GetCurrMemberTotal() uint32 {
	if m != nil {
		return m.CurrMemberTotal
	}
	return 0
}

// 基于 麦下用户 计算房间在线人数，不是 100% 准确
type GetUnderTheMircoOnlineCntReq struct {
	CidList []uint32 `protobuf:"varint,1,rep,name=cid_list,json=cidList" json:"cid_list,omitempty"`
}

func (m *GetUnderTheMircoOnlineCntReq) Reset()         { *m = GetUnderTheMircoOnlineCntReq{} }
func (m *GetUnderTheMircoOnlineCntReq) String() string { return proto.CompactTextString(m) }
func (*GetUnderTheMircoOnlineCntReq) ProtoMessage()    {}
func (*GetUnderTheMircoOnlineCntReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{19}
}

func (m *GetUnderTheMircoOnlineCntReq) GetCidList() []uint32 {
	if m != nil {
		return m.CidList
	}
	return nil
}

type SimpleChannelOnlineCount struct {
	Cid         uint32 `protobuf:"varint,1,req,name=cid" json:"cid"`
	OnlineCount uint32 `protobuf:"varint,2,req,name=online_count,json=onlineCount" json:"online_count"`
}

func (m *SimpleChannelOnlineCount) Reset()         { *m = SimpleChannelOnlineCount{} }
func (m *SimpleChannelOnlineCount) String() string { return proto.CompactTextString(m) }
func (*SimpleChannelOnlineCount) ProtoMessage()    {}
func (*SimpleChannelOnlineCount) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{20}
}

func (m *SimpleChannelOnlineCount) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *SimpleChannelOnlineCount) GetOnlineCount() uint32 {
	if m != nil {
		return m.OnlineCount
	}
	return 0
}

type GetUnderTheMircoOnlineCntResp struct {
	InfoList []*SimpleChannelOnlineCount `protobuf:"bytes,1,rep,name=info_list,json=infoList" json:"info_list,omitempty"`
}

func (m *GetUnderTheMircoOnlineCntResp) Reset()         { *m = GetUnderTheMircoOnlineCntResp{} }
func (m *GetUnderTheMircoOnlineCntResp) String() string { return proto.CompactTextString(m) }
func (*GetUnderTheMircoOnlineCntResp) ProtoMessage()    {}
func (*GetUnderTheMircoOnlineCntResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{21}
}

func (m *GetUnderTheMircoOnlineCntResp) GetInfoList() []*SimpleChannelOnlineCount {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type GetMemberWeekRankListReq struct {
	Cid   uint32 `protobuf:"varint,1,req,name=cid" json:"cid"`
	Begin uint32 `protobuf:"varint,2,req,name=begin" json:"begin"`
	Limit uint32 `protobuf:"varint,3,req,name=limit" json:"limit"`
	// 需要获取自己的排名信息才填以下字段
	Uid     uint32 `protobuf:"varint,4,opt,name=uid" json:"uid"`
	ViewCnt uint32 `protobuf:"varint,5,opt,name=view_cnt,json=viewCnt" json:"view_cnt"`
}

func (m *GetMemberWeekRankListReq) Reset()         { *m = GetMemberWeekRankListReq{} }
func (m *GetMemberWeekRankListReq) String() string { return proto.CompactTextString(m) }
func (*GetMemberWeekRankListReq) ProtoMessage()    {}
func (*GetMemberWeekRankListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{22}
}

func (m *GetMemberWeekRankListReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *GetMemberWeekRankListReq) GetBegin() uint32 {
	if m != nil {
		return m.Begin
	}
	return 0
}

func (m *GetMemberWeekRankListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetMemberWeekRankListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetMemberWeekRankListReq) GetViewCnt() uint32 {
	if m != nil {
		return m.ViewCnt
	}
	return 0
}

type GetMemberWeekRankListResp struct {
	RankInfoList []*MemberConsumeInfo `protobuf:"bytes,1,rep,name=rank_info_list,json=rankInfoList" json:"rank_info_list,omitempty"`
	MyRankInfo   *MemberConsumeInfo   `protobuf:"bytes,2,opt,name=my_rank_info,json=myRankInfo" json:"my_rank_info,omitempty"`
	DValue       uint32               `protobuf:"varint,3,opt,name=d_value,json=dValue" json:"d_value"`
	Rank         uint32               `protobuf:"varint,4,opt,name=rank" json:"rank"`
}

func (m *GetMemberWeekRankListResp) Reset()         { *m = GetMemberWeekRankListResp{} }
func (m *GetMemberWeekRankListResp) String() string { return proto.CompactTextString(m) }
func (*GetMemberWeekRankListResp) ProtoMessage()    {}
func (*GetMemberWeekRankListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{23}
}

func (m *GetMemberWeekRankListResp) GetRankInfoList() []*MemberConsumeInfo {
	if m != nil {
		return m.RankInfoList
	}
	return nil
}

func (m *GetMemberWeekRankListResp) GetMyRankInfo() *MemberConsumeInfo {
	if m != nil {
		return m.MyRankInfo
	}
	return nil
}

func (m *GetMemberWeekRankListResp) GetDValue() uint32 {
	if m != nil {
		return m.DValue
	}
	return 0
}

func (m *GetMemberWeekRankListResp) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

type ChannelHourRankInfo struct {
	Cid    uint32 `protobuf:"varint,1,req,name=cid" json:"cid"`
	Score  uint32 `protobuf:"varint,2,req,name=score" json:"score"`
	Rank   uint32 `protobuf:"varint,3,req,name=rank" json:"rank"`
	DValue uint32 `protobuf:"varint,4,opt,name=d_value,json=dValue" json:"d_value"`
	TagId  uint32 `protobuf:"varint,5,opt,name=tag_id,json=tagId" json:"tag_id"`
}

func (m *ChannelHourRankInfo) Reset()         { *m = ChannelHourRankInfo{} }
func (m *ChannelHourRankInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelHourRankInfo) ProtoMessage()    {}
func (*ChannelHourRankInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{24}
}

func (m *ChannelHourRankInfo) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *ChannelHourRankInfo) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *ChannelHourRankInfo) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *ChannelHourRankInfo) GetDValue() uint32 {
	if m != nil {
		return m.DValue
	}
	return 0
}

func (m *ChannelHourRankInfo) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

type GetChannelHourRankListReq struct {
	Type  uint32 `protobuf:"varint,1,req,name=type" json:"type"`
	Hour  uint32 `protobuf:"varint,2,req,name=hour" json:"hour"`
	Begin uint32 `protobuf:"varint,3,req,name=begin" json:"begin"`
	Limit uint32 `protobuf:"varint,4,req,name=limit" json:"limit"`
	// 需要获取本房间的排名信息才填以下字段
	Cid     uint32 `protobuf:"varint,5,opt,name=cid" json:"cid"`
	ViewCnt uint32 `protobuf:"varint,6,opt,name=view_cnt,json=viewCnt" json:"view_cnt"`
}

func (m *GetChannelHourRankListReq) Reset()         { *m = GetChannelHourRankListReq{} }
func (m *GetChannelHourRankListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelHourRankListReq) ProtoMessage()    {}
func (*GetChannelHourRankListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{25}
}

func (m *GetChannelHourRankListReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GetChannelHourRankListReq) GetHour() uint32 {
	if m != nil {
		return m.Hour
	}
	return 0
}

func (m *GetChannelHourRankListReq) GetBegin() uint32 {
	if m != nil {
		return m.Begin
	}
	return 0
}

func (m *GetChannelHourRankListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetChannelHourRankListReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *GetChannelHourRankListReq) GetViewCnt() uint32 {
	if m != nil {
		return m.ViewCnt
	}
	return 0
}

type GetChannelHourRankListResp struct {
	RankInfoList []*ChannelHourRankInfo `protobuf:"bytes,1,rep,name=rank_info_list,json=rankInfoList" json:"rank_info_list,omitempty"`
	RealViewCnt  uint32                 `protobuf:"varint,2,req,name=real_view_cnt,json=realViewCnt" json:"real_view_cnt"`
	MyRankInfo   *ChannelHourRankInfo   `protobuf:"bytes,3,opt,name=my_rank_info,json=myRankInfo" json:"my_rank_info,omitempty"`
}

func (m *GetChannelHourRankListResp) Reset()         { *m = GetChannelHourRankListResp{} }
func (m *GetChannelHourRankListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelHourRankListResp) ProtoMessage()    {}
func (*GetChannelHourRankListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{26}
}

func (m *GetChannelHourRankListResp) GetRankInfoList() []*ChannelHourRankInfo {
	if m != nil {
		return m.RankInfoList
	}
	return nil
}

func (m *GetChannelHourRankListResp) GetRealViewCnt() uint32 {
	if m != nil {
		return m.RealViewCnt
	}
	return 0
}

func (m *GetChannelHourRankListResp) GetMyRankInfo() *ChannelHourRankInfo {
	if m != nil {
		return m.MyRankInfo
	}
	return nil
}

type GetHourRankByIdReq struct {
	Cid     uint32 `protobuf:"varint,1,req,name=cid" json:"cid"`
	Type    uint32 `protobuf:"varint,2,req,name=type" json:"type"`
	Hour    uint32 `protobuf:"varint,3,req,name=hour" json:"hour"`
	ViewCnt uint32 `protobuf:"varint,4,req,name=view_cnt,json=viewCnt" json:"view_cnt"`
}

func (m *GetHourRankByIdReq) Reset()         { *m = GetHourRankByIdReq{} }
func (m *GetHourRankByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetHourRankByIdReq) ProtoMessage()    {}
func (*GetHourRankByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{27}
}

func (m *GetHourRankByIdReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *GetHourRankByIdReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GetHourRankByIdReq) GetHour() uint32 {
	if m != nil {
		return m.Hour
	}
	return 0
}

func (m *GetHourRankByIdReq) GetViewCnt() uint32 {
	if m != nil {
		return m.ViewCnt
	}
	return 0
}

type GetHourRankByIdResp struct {
	RankInfo    *ChannelHourRankInfo `protobuf:"bytes,1,req,name=rank_info,json=rankInfo" json:"rank_info,omitempty"`
	RealViewCnt uint32               `protobuf:"varint,2,req,name=real_view_cnt,json=realViewCnt" json:"real_view_cnt"`
}

func (m *GetHourRankByIdResp) Reset()         { *m = GetHourRankByIdResp{} }
func (m *GetHourRankByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetHourRankByIdResp) ProtoMessage()    {}
func (*GetHourRankByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{28}
}

func (m *GetHourRankByIdResp) GetRankInfo() *ChannelHourRankInfo {
	if m != nil {
		return m.RankInfo
	}
	return nil
}

func (m *GetHourRankByIdResp) GetRealViewCnt() uint32 {
	if m != nil {
		return m.RealViewCnt
	}
	return 0
}

// 测试用 增加小时榜分数
type IncrChannelHourRankScoreReq struct {
	Cid   uint32 `protobuf:"varint,1,req,name=cid" json:"cid"`
	Type  uint32 `protobuf:"varint,2,req,name=type" json:"type"`
	Hour  uint32 `protobuf:"varint,3,req,name=hour" json:"hour"`
	Score uint32 `protobuf:"varint,4,req,name=score" json:"score"`
}

func (m *IncrChannelHourRankScoreReq) Reset()         { *m = IncrChannelHourRankScoreReq{} }
func (m *IncrChannelHourRankScoreReq) String() string { return proto.CompactTextString(m) }
func (*IncrChannelHourRankScoreReq) ProtoMessage()    {}
func (*IncrChannelHourRankScoreReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{29}
}

func (m *IncrChannelHourRankScoreReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *IncrChannelHourRankScoreReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *IncrChannelHourRankScoreReq) GetHour() uint32 {
	if m != nil {
		return m.Hour
	}
	return 0
}

func (m *IncrChannelHourRankScoreReq) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

type IncrChannelHourRankScoreResp struct {
}

func (m *IncrChannelHourRankScoreResp) Reset()         { *m = IncrChannelHourRankScoreResp{} }
func (m *IncrChannelHourRankScoreResp) String() string { return proto.CompactTextString(m) }
func (*IncrChannelHourRankScoreResp) ProtoMessage()    {}
func (*IncrChannelHourRankScoreResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{30}
}

// 测试用 增加房间成员周榜分数
type IncrMemberWeekRankScoreReq struct {
	Cid   uint32 `protobuf:"varint,1,req,name=cid" json:"cid"`
	Uid   uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
	Ts    uint32 `protobuf:"varint,3,req,name=ts" json:"ts"`
	Score uint32 `protobuf:"varint,4,req,name=score" json:"score"`
}

func (m *IncrMemberWeekRankScoreReq) Reset()         { *m = IncrMemberWeekRankScoreReq{} }
func (m *IncrMemberWeekRankScoreReq) String() string { return proto.CompactTextString(m) }
func (*IncrMemberWeekRankScoreReq) ProtoMessage()    {}
func (*IncrMemberWeekRankScoreReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{31}
}

func (m *IncrMemberWeekRankScoreReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *IncrMemberWeekRankScoreReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *IncrMemberWeekRankScoreReq) GetTs() uint32 {
	if m != nil {
		return m.Ts
	}
	return 0
}

func (m *IncrMemberWeekRankScoreReq) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

type IncrMemberWeekRankScoreResp struct {
}

func (m *IncrMemberWeekRankScoreResp) Reset()         { *m = IncrMemberWeekRankScoreResp{} }
func (m *IncrMemberWeekRankScoreResp) String() string { return proto.CompactTextString(m) }
func (*IncrMemberWeekRankScoreResp) ProtoMessage()    {}
func (*IncrMemberWeekRankScoreResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{32}
}

type UpdateUserTempInvisibleStatusReq struct {
	Uid         uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ChannelId   uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	Invisible   bool   `protobuf:"varint,3,req,name=invisible" json:"invisible"`
	Level       uint32 `protobuf:"varint,4,req,name=level" json:"level"`
	ChannelType uint32 `protobuf:"varint,5,opt,name=channel_type,json=channelType" json:"channel_type"`
}

func (m *UpdateUserTempInvisibleStatusReq) Reset()         { *m = UpdateUserTempInvisibleStatusReq{} }
func (m *UpdateUserTempInvisibleStatusReq) String() string { return proto.CompactTextString(m) }
func (*UpdateUserTempInvisibleStatusReq) ProtoMessage()    {}
func (*UpdateUserTempInvisibleStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{33}
}

func (m *UpdateUserTempInvisibleStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateUserTempInvisibleStatusReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UpdateUserTempInvisibleStatusReq) GetInvisible() bool {
	if m != nil {
		return m.Invisible
	}
	return false
}

func (m *UpdateUserTempInvisibleStatusReq) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *UpdateUserTempInvisibleStatusReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

type UpdateUserTempInvisibleStatusResp struct {
}

func (m *UpdateUserTempInvisibleStatusResp) Reset()         { *m = UpdateUserTempInvisibleStatusResp{} }
func (m *UpdateUserTempInvisibleStatusResp) String() string { return proto.CompactTextString(m) }
func (*UpdateUserTempInvisibleStatusResp) ProtoMessage()    {}
func (*UpdateUserTempInvisibleStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{34}
}

type GetMemberNobilityInfoReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ChannelId uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *GetMemberNobilityInfoReq) Reset()         { *m = GetMemberNobilityInfoReq{} }
func (m *GetMemberNobilityInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetMemberNobilityInfoReq) ProtoMessage()    {}
func (*GetMemberNobilityInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{35}
}

func (m *GetMemberNobilityInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetMemberNobilityInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetMemberNobilityInfoResp struct {
	NobilityLevel uint32 `protobuf:"varint,1,req,name=nobility_level,json=nobilityLevel" json:"nobility_level"`
	Invisible     bool   `protobuf:"varint,2,req,name=invisible" json:"invisible"`
}

func (m *GetMemberNobilityInfoResp) Reset()         { *m = GetMemberNobilityInfoResp{} }
func (m *GetMemberNobilityInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetMemberNobilityInfoResp) ProtoMessage()    {}
func (*GetMemberNobilityInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{36}
}

func (m *GetMemberNobilityInfoResp) GetNobilityLevel() uint32 {
	if m != nil {
		return m.NobilityLevel
	}
	return 0
}

func (m *GetMemberNobilityInfoResp) GetInvisible() bool {
	if m != nil {
		return m.Invisible
	}
	return false
}

// （语音直播主播下播时）清除直播房在线榜
type RemoveLiveChannelOnlineRankReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *RemoveLiveChannelOnlineRankReq) Reset()         { *m = RemoveLiveChannelOnlineRankReq{} }
func (m *RemoveLiveChannelOnlineRankReq) String() string { return proto.CompactTextString(m) }
func (*RemoveLiveChannelOnlineRankReq) ProtoMessage()    {}
func (*RemoveLiveChannelOnlineRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{37}
}

func (m *RemoveLiveChannelOnlineRankReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type RemoveLiveChannelOnlineRankResp struct {
}

func (m *RemoveLiveChannelOnlineRankResp) Reset()         { *m = RemoveLiveChannelOnlineRankResp{} }
func (m *RemoveLiveChannelOnlineRankResp) String() string { return proto.CompactTextString(m) }
func (*RemoveLiveChannelOnlineRankResp) ProtoMessage()    {}
func (*RemoveLiveChannelOnlineRankResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{38}
}

type HideUserChannelConsumeReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ChannelId uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	IsHide    bool   `protobuf:"varint,3,req,name=is_hide,json=isHide" json:"is_hide"`
}

func (m *HideUserChannelConsumeReq) Reset()         { *m = HideUserChannelConsumeReq{} }
func (m *HideUserChannelConsumeReq) String() string { return proto.CompactTextString(m) }
func (*HideUserChannelConsumeReq) ProtoMessage()    {}
func (*HideUserChannelConsumeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{39}
}

func (m *HideUserChannelConsumeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *HideUserChannelConsumeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *HideUserChannelConsumeReq) GetIsHide() bool {
	if m != nil {
		return m.IsHide
	}
	return false
}

type HideUserChannelConsumeResp struct {
}

func (m *HideUserChannelConsumeResp) Reset()         { *m = HideUserChannelConsumeResp{} }
func (m *HideUserChannelConsumeResp) String() string { return proto.CompactTextString(m) }
func (*HideUserChannelConsumeResp) ProtoMessage()    {}
func (*HideUserChannelConsumeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmemberviprank, []int{40}
}

func init() {
	proto.RegisterType((*ChannelMemberVip)(nil), "channelmemberviprank.ChannelMemberVip")
	proto.RegisterType((*ChannelMemberRank)(nil), "channelmemberviprank.ChannelMemberRank")
	proto.RegisterType((*GetMemberRankListReq)(nil), "channelmemberviprank.GetMemberRankListReq")
	proto.RegisterType((*GetMemberRankListResp)(nil), "channelmemberviprank.GetMemberRankListResp")
	proto.RegisterType((*MemberConsumeInfo)(nil), "channelmemberviprank.MemberConsumeInfo")
	proto.RegisterType((*GetConsumeTopNReq)(nil), "channelmemberviprank.GetConsumeTopNReq")
	proto.RegisterType((*GetConsumeTopNResp)(nil), "channelmemberviprank.GetConsumeTopNResp")
	proto.RegisterType((*GetUserConsumeInfoReq)(nil), "channelmemberviprank.GetUserConsumeInfoReq")
	proto.RegisterType((*GetUserConsumeInfoResp)(nil), "channelmemberviprank.GetUserConsumeInfoResp")
	proto.RegisterType((*BatGetUserConsumeInfoReq)(nil), "channelmemberviprank.BatGetUserConsumeInfoReq")
	proto.RegisterType((*BatGetUserConsumeInfoResp)(nil), "channelmemberviprank.BatGetUserConsumeInfoResp")
	proto.RegisterType((*RemoveOnlineMemberRankReq)(nil), "channelmemberviprank.RemoveOnlineMemberRankReq")
	proto.RegisterType((*RemoveOnlineMemberRankResp)(nil), "channelmemberviprank.RemoveOnlineMemberRankResp")
	proto.RegisterType((*SendCommBreakingNewsReq)(nil), "channelmemberviprank.SendCommBreakingNewsReq")
	proto.RegisterType((*SendCommBreakingNewsResp)(nil), "channelmemberviprank.SendCommBreakingNewsResp")
	proto.RegisterType((*OperAddUserChannelTCoinReq)(nil), "channelmemberviprank.OperAddUserChannelTCoinReq")
	proto.RegisterType((*OperAddUserChannelTCoinResp)(nil), "channelmemberviprank.OperAddUserChannelTCoinResp")
	proto.RegisterType((*GetUnderTheMircoRankReq)(nil), "channelmemberviprank.GetUnderTheMircoRankReq")
	proto.RegisterType((*GetUnderTheMircoRankResp)(nil), "channelmemberviprank.GetUnderTheMircoRankResp")
	proto.RegisterType((*GetUnderTheMircoOnlineCntReq)(nil), "channelmemberviprank.GetUnderTheMircoOnlineCntReq")
	proto.RegisterType((*SimpleChannelOnlineCount)(nil), "channelmemberviprank.SimpleChannelOnlineCount")
	proto.RegisterType((*GetUnderTheMircoOnlineCntResp)(nil), "channelmemberviprank.GetUnderTheMircoOnlineCntResp")
	proto.RegisterType((*GetMemberWeekRankListReq)(nil), "channelmemberviprank.GetMemberWeekRankListReq")
	proto.RegisterType((*GetMemberWeekRankListResp)(nil), "channelmemberviprank.GetMemberWeekRankListResp")
	proto.RegisterType((*ChannelHourRankInfo)(nil), "channelmemberviprank.ChannelHourRankInfo")
	proto.RegisterType((*GetChannelHourRankListReq)(nil), "channelmemberviprank.GetChannelHourRankListReq")
	proto.RegisterType((*GetChannelHourRankListResp)(nil), "channelmemberviprank.GetChannelHourRankListResp")
	proto.RegisterType((*GetHourRankByIdReq)(nil), "channelmemberviprank.GetHourRankByIdReq")
	proto.RegisterType((*GetHourRankByIdResp)(nil), "channelmemberviprank.GetHourRankByIdResp")
	proto.RegisterType((*IncrChannelHourRankScoreReq)(nil), "channelmemberviprank.IncrChannelHourRankScoreReq")
	proto.RegisterType((*IncrChannelHourRankScoreResp)(nil), "channelmemberviprank.IncrChannelHourRankScoreResp")
	proto.RegisterType((*IncrMemberWeekRankScoreReq)(nil), "channelmemberviprank.IncrMemberWeekRankScoreReq")
	proto.RegisterType((*IncrMemberWeekRankScoreResp)(nil), "channelmemberviprank.IncrMemberWeekRankScoreResp")
	proto.RegisterType((*UpdateUserTempInvisibleStatusReq)(nil), "channelmemberviprank.UpdateUserTempInvisibleStatusReq")
	proto.RegisterType((*UpdateUserTempInvisibleStatusResp)(nil), "channelmemberviprank.UpdateUserTempInvisibleStatusResp")
	proto.RegisterType((*GetMemberNobilityInfoReq)(nil), "channelmemberviprank.GetMemberNobilityInfoReq")
	proto.RegisterType((*GetMemberNobilityInfoResp)(nil), "channelmemberviprank.GetMemberNobilityInfoResp")
	proto.RegisterType((*RemoveLiveChannelOnlineRankReq)(nil), "channelmemberviprank.RemoveLiveChannelOnlineRankReq")
	proto.RegisterType((*RemoveLiveChannelOnlineRankResp)(nil), "channelmemberviprank.RemoveLiveChannelOnlineRankResp")
	proto.RegisterType((*HideUserChannelConsumeReq)(nil), "channelmemberviprank.HideUserChannelConsumeReq")
	proto.RegisterType((*HideUserChannelConsumeResp)(nil), "channelmemberviprank.HideUserChannelConsumeResp")
	proto.RegisterEnum("channelmemberviprank.ChannelHourRankType", ChannelHourRankType_name, ChannelHourRankType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for ChannelMemberVipRank service

type ChannelMemberVipRankClient interface {
	// 获取房间的排名列表
	GetMemberRankList(ctx context.Context, in *GetMemberRankListReq, opts ...grpc.CallOption) (*GetMemberRankListResp, error)
	// 获取指定用户的排名信息
	GetUserConsumeInfo(ctx context.Context, in *GetUserConsumeInfoReq, opts ...grpc.CallOption) (*GetUserConsumeInfoResp, error)
	// 获取房间的消费榜
	GetConsumeTopN(ctx context.Context, in *GetConsumeTopNReq, opts ...grpc.CallOption) (*GetConsumeTopNResp, error)
	// 批量获取指定用户的排名信息
	BatGetUserConsumeInfo(ctx context.Context, in *BatGetUserConsumeInfoReq, opts ...grpc.CallOption) (*BatGetUserConsumeInfoResp, error)
	// 麦下用户列表
	GetUnderTheMircoRank(ctx context.Context, in *GetUnderTheMircoRankReq, opts ...grpc.CallOption) (*GetUnderTheMircoRankResp, error)
	// 基于 麦下用户列表 计算房间在线人数
	GetUnderTheMircoOnlineCnt(ctx context.Context, in *GetUnderTheMircoOnlineCntReq, opts ...grpc.CallOption) (*GetUnderTheMircoOnlineCntResp, error)
	// 获取房间成员周流水榜
	GetMemberWeekRankList(ctx context.Context, in *GetMemberWeekRankListReq, opts ...grpc.CallOption) (*GetMemberWeekRankListResp, error)
	// 获取房间小时榜
	GetChannelHourRankList(ctx context.Context, in *GetChannelHourRankListReq, opts ...grpc.CallOption) (*GetChannelHourRankListResp, error)
	// 通过房间id获取房间在小时榜中的排名信息
	GetHourRankById(ctx context.Context, in *GetHourRankByIdReq, opts ...grpc.CallOption) (*GetHourRankByIdResp, error)
	// 将用户移出房间成员列表 用于修正数据
	RemoveOnlineMemberRank(ctx context.Context, in *RemoveOnlineMemberRankReq, opts ...grpc.CallOption) (*RemoveOnlineMemberRankResp, error)
	// 测试发送全网大事记消息
	SendCommBreakingNews(ctx context.Context, in *SendCommBreakingNewsReq, opts ...grpc.CallOption) (*SendCommBreakingNewsResp, error)
	// 运维给用户补发房间T豆消费
	OperAddUserChannelTCoin(ctx context.Context, in *OperAddUserChannelTCoinReq, opts ...grpc.CallOption) (*OperAddUserChannelTCoinResp, error)
	// 测试用 增加小时榜分数
	IncrChannelHourRankScore(ctx context.Context, in *IncrChannelHourRankScoreReq, opts ...grpc.CallOption) (*IncrChannelHourRankScoreResp, error)
	// 测试用 增加房间成员周榜分数
	IncrMemberWeekRankScore(ctx context.Context, in *IncrMemberWeekRankScoreReq, opts ...grpc.CallOption) (*IncrMemberWeekRankScoreResp, error)
	// 设置房间临时隐身状态
	UpdateUserTempInvisibleStatus(ctx context.Context, in *UpdateUserTempInvisibleStatusReq, opts ...grpc.CallOption) (*UpdateUserTempInvisibleStatusResp, error)
	// 设置房间临时隐身状态
	GetMemberNobilityInfo(ctx context.Context, in *GetMemberNobilityInfoReq, opts ...grpc.CallOption) (*GetMemberNobilityInfoResp, error)
	// （语音直播主播下播时）清除直播房在线榜
	RemoveLiveChannelOnlineRank(ctx context.Context, in *RemoveLiveChannelOnlineRankReq, opts ...grpc.CallOption) (*RemoveLiveChannelOnlineRankResp, error)
	HideChannelConsume(ctx context.Context, in *HideUserChannelConsumeReq, opts ...grpc.CallOption) (*HideUserChannelConsumeResp, error)
}

type channelMemberVipRankClient struct {
	cc *grpc.ClientConn
}

func NewChannelMemberVipRankClient(cc *grpc.ClientConn) ChannelMemberVipRankClient {
	return &channelMemberVipRankClient{cc}
}

func (c *channelMemberVipRankClient) GetMemberRankList(ctx context.Context, in *GetMemberRankListReq, opts ...grpc.CallOption) (*GetMemberRankListResp, error) {
	out := new(GetMemberRankListResp)
	err := grpc.Invoke(ctx, "/channelmemberviprank.ChannelMemberVipRank/GetMemberRankList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMemberVipRankClient) GetUserConsumeInfo(ctx context.Context, in *GetUserConsumeInfoReq, opts ...grpc.CallOption) (*GetUserConsumeInfoResp, error) {
	out := new(GetUserConsumeInfoResp)
	err := grpc.Invoke(ctx, "/channelmemberviprank.ChannelMemberVipRank/GetUserConsumeInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMemberVipRankClient) GetConsumeTopN(ctx context.Context, in *GetConsumeTopNReq, opts ...grpc.CallOption) (*GetConsumeTopNResp, error) {
	out := new(GetConsumeTopNResp)
	err := grpc.Invoke(ctx, "/channelmemberviprank.ChannelMemberVipRank/GetConsumeTopN", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMemberVipRankClient) BatGetUserConsumeInfo(ctx context.Context, in *BatGetUserConsumeInfoReq, opts ...grpc.CallOption) (*BatGetUserConsumeInfoResp, error) {
	out := new(BatGetUserConsumeInfoResp)
	err := grpc.Invoke(ctx, "/channelmemberviprank.ChannelMemberVipRank/BatGetUserConsumeInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMemberVipRankClient) GetUnderTheMircoRank(ctx context.Context, in *GetUnderTheMircoRankReq, opts ...grpc.CallOption) (*GetUnderTheMircoRankResp, error) {
	out := new(GetUnderTheMircoRankResp)
	err := grpc.Invoke(ctx, "/channelmemberviprank.ChannelMemberVipRank/GetUnderTheMircoRank", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMemberVipRankClient) GetUnderTheMircoOnlineCnt(ctx context.Context, in *GetUnderTheMircoOnlineCntReq, opts ...grpc.CallOption) (*GetUnderTheMircoOnlineCntResp, error) {
	out := new(GetUnderTheMircoOnlineCntResp)
	err := grpc.Invoke(ctx, "/channelmemberviprank.ChannelMemberVipRank/GetUnderTheMircoOnlineCnt", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMemberVipRankClient) GetMemberWeekRankList(ctx context.Context, in *GetMemberWeekRankListReq, opts ...grpc.CallOption) (*GetMemberWeekRankListResp, error) {
	out := new(GetMemberWeekRankListResp)
	err := grpc.Invoke(ctx, "/channelmemberviprank.ChannelMemberVipRank/GetMemberWeekRankList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMemberVipRankClient) GetChannelHourRankList(ctx context.Context, in *GetChannelHourRankListReq, opts ...grpc.CallOption) (*GetChannelHourRankListResp, error) {
	out := new(GetChannelHourRankListResp)
	err := grpc.Invoke(ctx, "/channelmemberviprank.ChannelMemberVipRank/GetChannelHourRankList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMemberVipRankClient) GetHourRankById(ctx context.Context, in *GetHourRankByIdReq, opts ...grpc.CallOption) (*GetHourRankByIdResp, error) {
	out := new(GetHourRankByIdResp)
	err := grpc.Invoke(ctx, "/channelmemberviprank.ChannelMemberVipRank/GetHourRankById", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMemberVipRankClient) RemoveOnlineMemberRank(ctx context.Context, in *RemoveOnlineMemberRankReq, opts ...grpc.CallOption) (*RemoveOnlineMemberRankResp, error) {
	out := new(RemoveOnlineMemberRankResp)
	err := grpc.Invoke(ctx, "/channelmemberviprank.ChannelMemberVipRank/RemoveOnlineMemberRank", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMemberVipRankClient) SendCommBreakingNews(ctx context.Context, in *SendCommBreakingNewsReq, opts ...grpc.CallOption) (*SendCommBreakingNewsResp, error) {
	out := new(SendCommBreakingNewsResp)
	err := grpc.Invoke(ctx, "/channelmemberviprank.ChannelMemberVipRank/SendCommBreakingNews", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMemberVipRankClient) OperAddUserChannelTCoin(ctx context.Context, in *OperAddUserChannelTCoinReq, opts ...grpc.CallOption) (*OperAddUserChannelTCoinResp, error) {
	out := new(OperAddUserChannelTCoinResp)
	err := grpc.Invoke(ctx, "/channelmemberviprank.ChannelMemberVipRank/OperAddUserChannelTCoin", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMemberVipRankClient) IncrChannelHourRankScore(ctx context.Context, in *IncrChannelHourRankScoreReq, opts ...grpc.CallOption) (*IncrChannelHourRankScoreResp, error) {
	out := new(IncrChannelHourRankScoreResp)
	err := grpc.Invoke(ctx, "/channelmemberviprank.ChannelMemberVipRank/IncrChannelHourRankScore", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMemberVipRankClient) IncrMemberWeekRankScore(ctx context.Context, in *IncrMemberWeekRankScoreReq, opts ...grpc.CallOption) (*IncrMemberWeekRankScoreResp, error) {
	out := new(IncrMemberWeekRankScoreResp)
	err := grpc.Invoke(ctx, "/channelmemberviprank.ChannelMemberVipRank/IncrMemberWeekRankScore", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMemberVipRankClient) UpdateUserTempInvisibleStatus(ctx context.Context, in *UpdateUserTempInvisibleStatusReq, opts ...grpc.CallOption) (*UpdateUserTempInvisibleStatusResp, error) {
	out := new(UpdateUserTempInvisibleStatusResp)
	err := grpc.Invoke(ctx, "/channelmemberviprank.ChannelMemberVipRank/UpdateUserTempInvisibleStatus", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMemberVipRankClient) GetMemberNobilityInfo(ctx context.Context, in *GetMemberNobilityInfoReq, opts ...grpc.CallOption) (*GetMemberNobilityInfoResp, error) {
	out := new(GetMemberNobilityInfoResp)
	err := grpc.Invoke(ctx, "/channelmemberviprank.ChannelMemberVipRank/GetMemberNobilityInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMemberVipRankClient) RemoveLiveChannelOnlineRank(ctx context.Context, in *RemoveLiveChannelOnlineRankReq, opts ...grpc.CallOption) (*RemoveLiveChannelOnlineRankResp, error) {
	out := new(RemoveLiveChannelOnlineRankResp)
	err := grpc.Invoke(ctx, "/channelmemberviprank.ChannelMemberVipRank/RemoveLiveChannelOnlineRank", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMemberVipRankClient) HideChannelConsume(ctx context.Context, in *HideUserChannelConsumeReq, opts ...grpc.CallOption) (*HideUserChannelConsumeResp, error) {
	out := new(HideUserChannelConsumeResp)
	err := grpc.Invoke(ctx, "/channelmemberviprank.ChannelMemberVipRank/HideChannelConsume", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for ChannelMemberVipRank service

type ChannelMemberVipRankServer interface {
	// 获取房间的排名列表
	GetMemberRankList(context.Context, *GetMemberRankListReq) (*GetMemberRankListResp, error)
	// 获取指定用户的排名信息
	GetUserConsumeInfo(context.Context, *GetUserConsumeInfoReq) (*GetUserConsumeInfoResp, error)
	// 获取房间的消费榜
	GetConsumeTopN(context.Context, *GetConsumeTopNReq) (*GetConsumeTopNResp, error)
	// 批量获取指定用户的排名信息
	BatGetUserConsumeInfo(context.Context, *BatGetUserConsumeInfoReq) (*BatGetUserConsumeInfoResp, error)
	// 麦下用户列表
	GetUnderTheMircoRank(context.Context, *GetUnderTheMircoRankReq) (*GetUnderTheMircoRankResp, error)
	// 基于 麦下用户列表 计算房间在线人数
	GetUnderTheMircoOnlineCnt(context.Context, *GetUnderTheMircoOnlineCntReq) (*GetUnderTheMircoOnlineCntResp, error)
	// 获取房间成员周流水榜
	GetMemberWeekRankList(context.Context, *GetMemberWeekRankListReq) (*GetMemberWeekRankListResp, error)
	// 获取房间小时榜
	GetChannelHourRankList(context.Context, *GetChannelHourRankListReq) (*GetChannelHourRankListResp, error)
	// 通过房间id获取房间在小时榜中的排名信息
	GetHourRankById(context.Context, *GetHourRankByIdReq) (*GetHourRankByIdResp, error)
	// 将用户移出房间成员列表 用于修正数据
	RemoveOnlineMemberRank(context.Context, *RemoveOnlineMemberRankReq) (*RemoveOnlineMemberRankResp, error)
	// 测试发送全网大事记消息
	SendCommBreakingNews(context.Context, *SendCommBreakingNewsReq) (*SendCommBreakingNewsResp, error)
	// 运维给用户补发房间T豆消费
	OperAddUserChannelTCoin(context.Context, *OperAddUserChannelTCoinReq) (*OperAddUserChannelTCoinResp, error)
	// 测试用 增加小时榜分数
	IncrChannelHourRankScore(context.Context, *IncrChannelHourRankScoreReq) (*IncrChannelHourRankScoreResp, error)
	// 测试用 增加房间成员周榜分数
	IncrMemberWeekRankScore(context.Context, *IncrMemberWeekRankScoreReq) (*IncrMemberWeekRankScoreResp, error)
	// 设置房间临时隐身状态
	UpdateUserTempInvisibleStatus(context.Context, *UpdateUserTempInvisibleStatusReq) (*UpdateUserTempInvisibleStatusResp, error)
	// 设置房间临时隐身状态
	GetMemberNobilityInfo(context.Context, *GetMemberNobilityInfoReq) (*GetMemberNobilityInfoResp, error)
	// （语音直播主播下播时）清除直播房在线榜
	RemoveLiveChannelOnlineRank(context.Context, *RemoveLiveChannelOnlineRankReq) (*RemoveLiveChannelOnlineRankResp, error)
	HideChannelConsume(context.Context, *HideUserChannelConsumeReq) (*HideUserChannelConsumeResp, error)
}

func RegisterChannelMemberVipRankServer(s *grpc.Server, srv ChannelMemberVipRankServer) {
	s.RegisterService(&_ChannelMemberVipRank_serviceDesc, srv)
}

func _ChannelMemberVipRank_GetMemberRankList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMemberRankListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMemberVipRankServer).GetMemberRankList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelmemberviprank.ChannelMemberVipRank/GetMemberRankList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMemberVipRankServer).GetMemberRankList(ctx, req.(*GetMemberRankListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMemberVipRank_GetUserConsumeInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserConsumeInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMemberVipRankServer).GetUserConsumeInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelmemberviprank.ChannelMemberVipRank/GetUserConsumeInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMemberVipRankServer).GetUserConsumeInfo(ctx, req.(*GetUserConsumeInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMemberVipRank_GetConsumeTopN_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConsumeTopNReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMemberVipRankServer).GetConsumeTopN(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelmemberviprank.ChannelMemberVipRank/GetConsumeTopN",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMemberVipRankServer).GetConsumeTopN(ctx, req.(*GetConsumeTopNReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMemberVipRank_BatGetUserConsumeInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetUserConsumeInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMemberVipRankServer).BatGetUserConsumeInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelmemberviprank.ChannelMemberVipRank/BatGetUserConsumeInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMemberVipRankServer).BatGetUserConsumeInfo(ctx, req.(*BatGetUserConsumeInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMemberVipRank_GetUnderTheMircoRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUnderTheMircoRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMemberVipRankServer).GetUnderTheMircoRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelmemberviprank.ChannelMemberVipRank/GetUnderTheMircoRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMemberVipRankServer).GetUnderTheMircoRank(ctx, req.(*GetUnderTheMircoRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMemberVipRank_GetUnderTheMircoOnlineCnt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUnderTheMircoOnlineCntReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMemberVipRankServer).GetUnderTheMircoOnlineCnt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelmemberviprank.ChannelMemberVipRank/GetUnderTheMircoOnlineCnt",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMemberVipRankServer).GetUnderTheMircoOnlineCnt(ctx, req.(*GetUnderTheMircoOnlineCntReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMemberVipRank_GetMemberWeekRankList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMemberWeekRankListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMemberVipRankServer).GetMemberWeekRankList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelmemberviprank.ChannelMemberVipRank/GetMemberWeekRankList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMemberVipRankServer).GetMemberWeekRankList(ctx, req.(*GetMemberWeekRankListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMemberVipRank_GetChannelHourRankList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelHourRankListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMemberVipRankServer).GetChannelHourRankList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelmemberviprank.ChannelMemberVipRank/GetChannelHourRankList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMemberVipRankServer).GetChannelHourRankList(ctx, req.(*GetChannelHourRankListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMemberVipRank_GetHourRankById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHourRankByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMemberVipRankServer).GetHourRankById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelmemberviprank.ChannelMemberVipRank/GetHourRankById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMemberVipRankServer).GetHourRankById(ctx, req.(*GetHourRankByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMemberVipRank_RemoveOnlineMemberRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveOnlineMemberRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMemberVipRankServer).RemoveOnlineMemberRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelmemberviprank.ChannelMemberVipRank/RemoveOnlineMemberRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMemberVipRankServer).RemoveOnlineMemberRank(ctx, req.(*RemoveOnlineMemberRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMemberVipRank_SendCommBreakingNews_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendCommBreakingNewsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMemberVipRankServer).SendCommBreakingNews(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelmemberviprank.ChannelMemberVipRank/SendCommBreakingNews",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMemberVipRankServer).SendCommBreakingNews(ctx, req.(*SendCommBreakingNewsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMemberVipRank_OperAddUserChannelTCoin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OperAddUserChannelTCoinReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMemberVipRankServer).OperAddUserChannelTCoin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelmemberviprank.ChannelMemberVipRank/OperAddUserChannelTCoin",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMemberVipRankServer).OperAddUserChannelTCoin(ctx, req.(*OperAddUserChannelTCoinReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMemberVipRank_IncrChannelHourRankScore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IncrChannelHourRankScoreReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMemberVipRankServer).IncrChannelHourRankScore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelmemberviprank.ChannelMemberVipRank/IncrChannelHourRankScore",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMemberVipRankServer).IncrChannelHourRankScore(ctx, req.(*IncrChannelHourRankScoreReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMemberVipRank_IncrMemberWeekRankScore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IncrMemberWeekRankScoreReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMemberVipRankServer).IncrMemberWeekRankScore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelmemberviprank.ChannelMemberVipRank/IncrMemberWeekRankScore",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMemberVipRankServer).IncrMemberWeekRankScore(ctx, req.(*IncrMemberWeekRankScoreReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMemberVipRank_UpdateUserTempInvisibleStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserTempInvisibleStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMemberVipRankServer).UpdateUserTempInvisibleStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelmemberviprank.ChannelMemberVipRank/UpdateUserTempInvisibleStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMemberVipRankServer).UpdateUserTempInvisibleStatus(ctx, req.(*UpdateUserTempInvisibleStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMemberVipRank_GetMemberNobilityInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMemberNobilityInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMemberVipRankServer).GetMemberNobilityInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelmemberviprank.ChannelMemberVipRank/GetMemberNobilityInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMemberVipRankServer).GetMemberNobilityInfo(ctx, req.(*GetMemberNobilityInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMemberVipRank_RemoveLiveChannelOnlineRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveLiveChannelOnlineRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMemberVipRankServer).RemoveLiveChannelOnlineRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelmemberviprank.ChannelMemberVipRank/RemoveLiveChannelOnlineRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMemberVipRankServer).RemoveLiveChannelOnlineRank(ctx, req.(*RemoveLiveChannelOnlineRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMemberVipRank_HideChannelConsume_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HideUserChannelConsumeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMemberVipRankServer).HideChannelConsume(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelmemberviprank.ChannelMemberVipRank/HideChannelConsume",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMemberVipRankServer).HideChannelConsume(ctx, req.(*HideUserChannelConsumeReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelMemberVipRank_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channelmemberviprank.ChannelMemberVipRank",
	HandlerType: (*ChannelMemberVipRankServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetMemberRankList",
			Handler:    _ChannelMemberVipRank_GetMemberRankList_Handler,
		},
		{
			MethodName: "GetUserConsumeInfo",
			Handler:    _ChannelMemberVipRank_GetUserConsumeInfo_Handler,
		},
		{
			MethodName: "GetConsumeTopN",
			Handler:    _ChannelMemberVipRank_GetConsumeTopN_Handler,
		},
		{
			MethodName: "BatGetUserConsumeInfo",
			Handler:    _ChannelMemberVipRank_BatGetUserConsumeInfo_Handler,
		},
		{
			MethodName: "GetUnderTheMircoRank",
			Handler:    _ChannelMemberVipRank_GetUnderTheMircoRank_Handler,
		},
		{
			MethodName: "GetUnderTheMircoOnlineCnt",
			Handler:    _ChannelMemberVipRank_GetUnderTheMircoOnlineCnt_Handler,
		},
		{
			MethodName: "GetMemberWeekRankList",
			Handler:    _ChannelMemberVipRank_GetMemberWeekRankList_Handler,
		},
		{
			MethodName: "GetChannelHourRankList",
			Handler:    _ChannelMemberVipRank_GetChannelHourRankList_Handler,
		},
		{
			MethodName: "GetHourRankById",
			Handler:    _ChannelMemberVipRank_GetHourRankById_Handler,
		},
		{
			MethodName: "RemoveOnlineMemberRank",
			Handler:    _ChannelMemberVipRank_RemoveOnlineMemberRank_Handler,
		},
		{
			MethodName: "SendCommBreakingNews",
			Handler:    _ChannelMemberVipRank_SendCommBreakingNews_Handler,
		},
		{
			MethodName: "OperAddUserChannelTCoin",
			Handler:    _ChannelMemberVipRank_OperAddUserChannelTCoin_Handler,
		},
		{
			MethodName: "IncrChannelHourRankScore",
			Handler:    _ChannelMemberVipRank_IncrChannelHourRankScore_Handler,
		},
		{
			MethodName: "IncrMemberWeekRankScore",
			Handler:    _ChannelMemberVipRank_IncrMemberWeekRankScore_Handler,
		},
		{
			MethodName: "UpdateUserTempInvisibleStatus",
			Handler:    _ChannelMemberVipRank_UpdateUserTempInvisibleStatus_Handler,
		},
		{
			MethodName: "GetMemberNobilityInfo",
			Handler:    _ChannelMemberVipRank_GetMemberNobilityInfo_Handler,
		},
		{
			MethodName: "RemoveLiveChannelOnlineRank",
			Handler:    _ChannelMemberVipRank_RemoveLiveChannelOnlineRank_Handler,
		},
		{
			MethodName: "HideChannelConsume",
			Handler:    _ChannelMemberVipRank_HideChannelConsume_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/channelmemberVipRank/channelmemberviprank.proto",
}

func (m *ChannelMemberVip) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelMemberVip) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.CurrLevelId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(len(m.CurrLevelName)))
	i += copy(dAtA[i:], m.CurrLevelName)
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.CurrLevelValue))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.NextLevelId))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(len(m.NextLevelName)))
	i += copy(dAtA[i:], m.NextLevelName)
	dAtA[i] = 0x30
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.NextLevelMinValue))
	dAtA[i] = 0x38
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.NobilityLevel))
	dAtA[i] = 0x40
	i++
	if m.Invisible {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *ChannelMemberRank) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelMemberRank) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Ts))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.RankValue))
	if m.VipLevelInfo != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.VipLevelInfo.Size()))
		n1, err := m.VipLevelInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Rank))
	dAtA[i] = 0x30
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.TotalConsum))
	return i, nil
}

func (m *GetMemberRankListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMemberRankListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.OpUid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.StartIdx))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Getsize))
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x30
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.ViewCnt))
	dAtA[i] = 0x38
	i++
	if m.IsTestMod {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x40
	i++
	if m.InvisibleVaild {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x48
	i++
	if m.IsLiveChannel {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetMemberRankListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMemberRankListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.ChannelId))
	if len(m.MemberList) > 0 {
		for _, msg := range m.MemberList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintChannelmemberviprank(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.AllMemberSize))
	if m.MyRankInfo != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.MyRankInfo.Size()))
		n2, err := m.MyRankInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.DValue))
	dAtA[i] = 0x30
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Rank))
	return i, nil
}

func (m *MemberConsumeInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MemberConsumeInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.ConsumeCnt))
	if m.VipLevelInfo != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.VipLevelInfo.Size()))
		n3, err := m.VipLevelInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	dAtA[i] = 0x20
	i++
	if m.IsAutoHiddenConsume {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetConsumeTopNReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetConsumeTopNReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.BeginIdx))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Limit))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.ViewCnt))
	dAtA[i] = 0x30
	i++
	if m.IsShowHidden {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x38
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.ChannelType))
	return i, nil
}

func (m *GetConsumeTopNResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetConsumeTopNResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ConsumeList) > 0 {
		for _, msg := range m.ConsumeList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelmemberviprank(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.TotalCnt))
	if m.MyRankInfo != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.MyRankInfo.Size()))
		n4, err := m.MyRankInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.DValue))
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Rank))
	dAtA[i] = 0x30
	i++
	if m.IsCanHideConsume {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x38
	i++
	if m.IsHiddenConsume {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x40
	i++
	if m.IsAutoHiddenConsume {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetUserConsumeInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserConsumeInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetUserConsumeInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserConsumeInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ConsumeInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("consume_info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.ConsumeInfo.Size()))
		n5, err := m.ConsumeInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	return i, nil
}

func (m *BatGetUserConsumeInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatGetUserConsumeInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.ChannelId))
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintChannelmemberviprank(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *BatGetUserConsumeInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatGetUserConsumeInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ConsumeInfo) > 0 {
		for _, msg := range m.ConsumeInfo {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelmemberviprank(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *RemoveOnlineMemberRankReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveOnlineMemberRankReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *RemoveOnlineMemberRankResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveOnlineMemberRankResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.RemovedUid))
	return i, nil
}

func (m *SendCommBreakingNewsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendCommBreakingNewsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *SendCommBreakingNewsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendCommBreakingNewsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *OperAddUserChannelTCoinReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OperAddUserChannelTCoinReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.TcoinCnt))
	dAtA[i] = 0x22
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(len(m.OrderId)))
	i += copy(dAtA[i:], m.OrderId)
	return i, nil
}

func (m *OperAddUserChannelTCoinResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OperAddUserChannelTCoinResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.IsOrderidExist {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetUnderTheMircoRankReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUnderTheMircoRankReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Begin))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Limit))
	return i, nil
}

func (m *GetUnderTheMircoRankResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUnderTheMircoRankResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelmemberviprank(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.CurrMemberTotal))
	return i, nil
}

func (m *GetUnderTheMircoOnlineCntReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUnderTheMircoOnlineCntReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.CidList) > 0 {
		for _, num := range m.CidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelmemberviprank(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *SimpleChannelOnlineCount) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SimpleChannelOnlineCount) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Cid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.OnlineCount))
	return i, nil
}

func (m *GetUnderTheMircoOnlineCntResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUnderTheMircoOnlineCntResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.InfoList) > 0 {
		for _, msg := range m.InfoList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelmemberviprank(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetMemberWeekRankListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMemberWeekRankListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Cid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Begin))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Limit))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.ViewCnt))
	return i, nil
}

func (m *GetMemberWeekRankListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMemberWeekRankListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.RankInfoList) > 0 {
		for _, msg := range m.RankInfoList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelmemberviprank(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.MyRankInfo != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.MyRankInfo.Size()))
		n6, err := m.MyRankInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.DValue))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Rank))
	return i, nil
}

func (m *ChannelHourRankInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelHourRankInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Cid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Score))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Rank))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.DValue))
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.TagId))
	return i, nil
}

func (m *GetChannelHourRankListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelHourRankListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Hour))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Begin))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Limit))
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Cid))
	dAtA[i] = 0x30
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.ViewCnt))
	return i, nil
}

func (m *GetChannelHourRankListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelHourRankListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.RankInfoList) > 0 {
		for _, msg := range m.RankInfoList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelmemberviprank(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.RealViewCnt))
	if m.MyRankInfo != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.MyRankInfo.Size()))
		n7, err := m.MyRankInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	return i, nil
}

func (m *GetHourRankByIdReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetHourRankByIdReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Cid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Hour))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.ViewCnt))
	return i, nil
}

func (m *GetHourRankByIdResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetHourRankByIdResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.RankInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("rank_info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.RankInfo.Size()))
		n8, err := m.RankInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.RealViewCnt))
	return i, nil
}

func (m *IncrChannelHourRankScoreReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IncrChannelHourRankScoreReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Cid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Hour))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Score))
	return i, nil
}

func (m *IncrChannelHourRankScoreResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IncrChannelHourRankScoreResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *IncrMemberWeekRankScoreReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IncrMemberWeekRankScoreReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Cid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Ts))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Score))
	return i, nil
}

func (m *IncrMemberWeekRankScoreResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IncrMemberWeekRankScoreResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *UpdateUserTempInvisibleStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateUserTempInvisibleStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	if m.Invisible {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Level))
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.ChannelType))
	return i, nil
}

func (m *UpdateUserTempInvisibleStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateUserTempInvisibleStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetMemberNobilityInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMemberNobilityInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *GetMemberNobilityInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMemberNobilityInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.NobilityLevel))
	dAtA[i] = 0x10
	i++
	if m.Invisible {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *RemoveLiveChannelOnlineRankReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveLiveChannelOnlineRankReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *RemoveLiveChannelOnlineRankResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveLiveChannelOnlineRankResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *HideUserChannelConsumeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *HideUserChannelConsumeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmemberviprank(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	if m.IsHide {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *HideUserChannelConsumeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *HideUserChannelConsumeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func encodeFixed64Channelmemberviprank(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Channelmemberviprank(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintChannelmemberviprank(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *ChannelMemberVip) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmemberviprank(uint64(m.CurrLevelId))
	l = len(m.CurrLevelName)
	n += 1 + l + sovChannelmemberviprank(uint64(l))
	n += 1 + sovChannelmemberviprank(uint64(m.CurrLevelValue))
	n += 1 + sovChannelmemberviprank(uint64(m.NextLevelId))
	l = len(m.NextLevelName)
	n += 1 + l + sovChannelmemberviprank(uint64(l))
	n += 1 + sovChannelmemberviprank(uint64(m.NextLevelMinValue))
	n += 1 + sovChannelmemberviprank(uint64(m.NobilityLevel))
	n += 2
	return n
}

func (m *ChannelMemberRank) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmemberviprank(uint64(m.Uid))
	n += 1 + sovChannelmemberviprank(uint64(m.Ts))
	n += 1 + sovChannelmemberviprank(uint64(m.RankValue))
	if m.VipLevelInfo != nil {
		l = m.VipLevelInfo.Size()
		n += 1 + l + sovChannelmemberviprank(uint64(l))
	}
	n += 1 + sovChannelmemberviprank(uint64(m.Rank))
	n += 1 + sovChannelmemberviprank(uint64(m.TotalConsum))
	return n
}

func (m *GetMemberRankListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmemberviprank(uint64(m.ChannelId))
	n += 1 + sovChannelmemberviprank(uint64(m.OpUid))
	n += 1 + sovChannelmemberviprank(uint64(m.StartIdx))
	n += 1 + sovChannelmemberviprank(uint64(m.Getsize))
	n += 1 + sovChannelmemberviprank(uint64(m.Uid))
	n += 1 + sovChannelmemberviprank(uint64(m.ViewCnt))
	n += 2
	n += 2
	n += 2
	return n
}

func (m *GetMemberRankListResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmemberviprank(uint64(m.ChannelId))
	if len(m.MemberList) > 0 {
		for _, e := range m.MemberList {
			l = e.Size()
			n += 1 + l + sovChannelmemberviprank(uint64(l))
		}
	}
	n += 1 + sovChannelmemberviprank(uint64(m.AllMemberSize))
	if m.MyRankInfo != nil {
		l = m.MyRankInfo.Size()
		n += 1 + l + sovChannelmemberviprank(uint64(l))
	}
	n += 1 + sovChannelmemberviprank(uint64(m.DValue))
	n += 1 + sovChannelmemberviprank(uint64(m.Rank))
	return n
}

func (m *MemberConsumeInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmemberviprank(uint64(m.Uid))
	n += 1 + sovChannelmemberviprank(uint64(m.ConsumeCnt))
	if m.VipLevelInfo != nil {
		l = m.VipLevelInfo.Size()
		n += 1 + l + sovChannelmemberviprank(uint64(l))
	}
	n += 2
	return n
}

func (m *GetConsumeTopNReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmemberviprank(uint64(m.ChannelId))
	n += 1 + sovChannelmemberviprank(uint64(m.BeginIdx))
	n += 1 + sovChannelmemberviprank(uint64(m.Limit))
	n += 1 + sovChannelmemberviprank(uint64(m.Uid))
	n += 1 + sovChannelmemberviprank(uint64(m.ViewCnt))
	n += 2
	n += 1 + sovChannelmemberviprank(uint64(m.ChannelType))
	return n
}

func (m *GetConsumeTopNResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ConsumeList) > 0 {
		for _, e := range m.ConsumeList {
			l = e.Size()
			n += 1 + l + sovChannelmemberviprank(uint64(l))
		}
	}
	n += 1 + sovChannelmemberviprank(uint64(m.TotalCnt))
	if m.MyRankInfo != nil {
		l = m.MyRankInfo.Size()
		n += 1 + l + sovChannelmemberviprank(uint64(l))
	}
	n += 1 + sovChannelmemberviprank(uint64(m.DValue))
	n += 1 + sovChannelmemberviprank(uint64(m.Rank))
	n += 2
	n += 2
	n += 2
	return n
}

func (m *GetUserConsumeInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmemberviprank(uint64(m.ChannelId))
	n += 1 + sovChannelmemberviprank(uint64(m.Uid))
	return n
}

func (m *GetUserConsumeInfoResp) Size() (n int) {
	var l int
	_ = l
	if m.ConsumeInfo != nil {
		l = m.ConsumeInfo.Size()
		n += 1 + l + sovChannelmemberviprank(uint64(l))
	}
	return n
}

func (m *BatGetUserConsumeInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmemberviprank(uint64(m.ChannelId))
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovChannelmemberviprank(uint64(e))
		}
	}
	return n
}

func (m *BatGetUserConsumeInfoResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ConsumeInfo) > 0 {
		for _, e := range m.ConsumeInfo {
			l = e.Size()
			n += 1 + l + sovChannelmemberviprank(uint64(l))
		}
	}
	return n
}

func (m *RemoveOnlineMemberRankReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmemberviprank(uint64(m.ChannelId))
	n += 1 + sovChannelmemberviprank(uint64(m.Uid))
	return n
}

func (m *RemoveOnlineMemberRankResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmemberviprank(uint64(m.RemovedUid))
	return n
}

func (m *SendCommBreakingNewsReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmemberviprank(uint64(m.ChannelId))
	n += 1 + sovChannelmemberviprank(uint64(m.Uid))
	return n
}

func (m *SendCommBreakingNewsResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *OperAddUserChannelTCoinReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmemberviprank(uint64(m.ChannelId))
	n += 1 + sovChannelmemberviprank(uint64(m.Uid))
	n += 1 + sovChannelmemberviprank(uint64(m.TcoinCnt))
	l = len(m.OrderId)
	n += 1 + l + sovChannelmemberviprank(uint64(l))
	return n
}

func (m *OperAddUserChannelTCoinResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	return n
}

func (m *GetUnderTheMircoRankReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmemberviprank(uint64(m.ChannelId))
	n += 1 + sovChannelmemberviprank(uint64(m.Begin))
	n += 1 + sovChannelmemberviprank(uint64(m.Limit))
	return n
}

func (m *GetUnderTheMircoRankResp) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovChannelmemberviprank(uint64(e))
		}
	}
	n += 1 + sovChannelmemberviprank(uint64(m.CurrMemberTotal))
	return n
}

func (m *GetUnderTheMircoOnlineCntReq) Size() (n int) {
	var l int
	_ = l
	if len(m.CidList) > 0 {
		for _, e := range m.CidList {
			n += 1 + sovChannelmemberviprank(uint64(e))
		}
	}
	return n
}

func (m *SimpleChannelOnlineCount) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmemberviprank(uint64(m.Cid))
	n += 1 + sovChannelmemberviprank(uint64(m.OnlineCount))
	return n
}

func (m *GetUnderTheMircoOnlineCntResp) Size() (n int) {
	var l int
	_ = l
	if len(m.InfoList) > 0 {
		for _, e := range m.InfoList {
			l = e.Size()
			n += 1 + l + sovChannelmemberviprank(uint64(l))
		}
	}
	return n
}

func (m *GetMemberWeekRankListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmemberviprank(uint64(m.Cid))
	n += 1 + sovChannelmemberviprank(uint64(m.Begin))
	n += 1 + sovChannelmemberviprank(uint64(m.Limit))
	n += 1 + sovChannelmemberviprank(uint64(m.Uid))
	n += 1 + sovChannelmemberviprank(uint64(m.ViewCnt))
	return n
}

func (m *GetMemberWeekRankListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.RankInfoList) > 0 {
		for _, e := range m.RankInfoList {
			l = e.Size()
			n += 1 + l + sovChannelmemberviprank(uint64(l))
		}
	}
	if m.MyRankInfo != nil {
		l = m.MyRankInfo.Size()
		n += 1 + l + sovChannelmemberviprank(uint64(l))
	}
	n += 1 + sovChannelmemberviprank(uint64(m.DValue))
	n += 1 + sovChannelmemberviprank(uint64(m.Rank))
	return n
}

func (m *ChannelHourRankInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmemberviprank(uint64(m.Cid))
	n += 1 + sovChannelmemberviprank(uint64(m.Score))
	n += 1 + sovChannelmemberviprank(uint64(m.Rank))
	n += 1 + sovChannelmemberviprank(uint64(m.DValue))
	n += 1 + sovChannelmemberviprank(uint64(m.TagId))
	return n
}

func (m *GetChannelHourRankListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmemberviprank(uint64(m.Type))
	n += 1 + sovChannelmemberviprank(uint64(m.Hour))
	n += 1 + sovChannelmemberviprank(uint64(m.Begin))
	n += 1 + sovChannelmemberviprank(uint64(m.Limit))
	n += 1 + sovChannelmemberviprank(uint64(m.Cid))
	n += 1 + sovChannelmemberviprank(uint64(m.ViewCnt))
	return n
}

func (m *GetChannelHourRankListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.RankInfoList) > 0 {
		for _, e := range m.RankInfoList {
			l = e.Size()
			n += 1 + l + sovChannelmemberviprank(uint64(l))
		}
	}
	n += 1 + sovChannelmemberviprank(uint64(m.RealViewCnt))
	if m.MyRankInfo != nil {
		l = m.MyRankInfo.Size()
		n += 1 + l + sovChannelmemberviprank(uint64(l))
	}
	return n
}

func (m *GetHourRankByIdReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmemberviprank(uint64(m.Cid))
	n += 1 + sovChannelmemberviprank(uint64(m.Type))
	n += 1 + sovChannelmemberviprank(uint64(m.Hour))
	n += 1 + sovChannelmemberviprank(uint64(m.ViewCnt))
	return n
}

func (m *GetHourRankByIdResp) Size() (n int) {
	var l int
	_ = l
	if m.RankInfo != nil {
		l = m.RankInfo.Size()
		n += 1 + l + sovChannelmemberviprank(uint64(l))
	}
	n += 1 + sovChannelmemberviprank(uint64(m.RealViewCnt))
	return n
}

func (m *IncrChannelHourRankScoreReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmemberviprank(uint64(m.Cid))
	n += 1 + sovChannelmemberviprank(uint64(m.Type))
	n += 1 + sovChannelmemberviprank(uint64(m.Hour))
	n += 1 + sovChannelmemberviprank(uint64(m.Score))
	return n
}

func (m *IncrChannelHourRankScoreResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *IncrMemberWeekRankScoreReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmemberviprank(uint64(m.Cid))
	n += 1 + sovChannelmemberviprank(uint64(m.Uid))
	n += 1 + sovChannelmemberviprank(uint64(m.Ts))
	n += 1 + sovChannelmemberviprank(uint64(m.Score))
	return n
}

func (m *IncrMemberWeekRankScoreResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *UpdateUserTempInvisibleStatusReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmemberviprank(uint64(m.Uid))
	n += 1 + sovChannelmemberviprank(uint64(m.ChannelId))
	n += 2
	n += 1 + sovChannelmemberviprank(uint64(m.Level))
	n += 1 + sovChannelmemberviprank(uint64(m.ChannelType))
	return n
}

func (m *UpdateUserTempInvisibleStatusResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetMemberNobilityInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmemberviprank(uint64(m.Uid))
	n += 1 + sovChannelmemberviprank(uint64(m.ChannelId))
	return n
}

func (m *GetMemberNobilityInfoResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmemberviprank(uint64(m.NobilityLevel))
	n += 2
	return n
}

func (m *RemoveLiveChannelOnlineRankReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmemberviprank(uint64(m.ChannelId))
	return n
}

func (m *RemoveLiveChannelOnlineRankResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *HideUserChannelConsumeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmemberviprank(uint64(m.Uid))
	n += 1 + sovChannelmemberviprank(uint64(m.ChannelId))
	n += 2
	return n
}

func (m *HideUserChannelConsumeResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func sovChannelmemberviprank(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozChannelmemberviprank(x uint64) (n int) {
	return sovChannelmemberviprank(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *ChannelMemberVip) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelMemberVip: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelMemberVip: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CurrLevelId", wireType)
			}
			m.CurrLevelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CurrLevelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CurrLevelName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CurrLevelName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CurrLevelValue", wireType)
			}
			m.CurrLevelValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CurrLevelValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NextLevelId", wireType)
			}
			m.NextLevelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NextLevelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NextLevelName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.NextLevelName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NextLevelMinValue", wireType)
			}
			m.NextLevelMinValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NextLevelMinValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NobilityLevel", wireType)
			}
			m.NobilityLevel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NobilityLevel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Invisible", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Invisible = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelMemberRank) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelMemberRank: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelMemberRank: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ts", wireType)
			}
			m.Ts = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ts |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankValue", wireType)
			}
			m.RankValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RankValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VipLevelInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.VipLevelInfo == nil {
				m.VipLevelInfo = &ChannelMemberVip{}
			}
			if err := m.VipLevelInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Rank", wireType)
			}
			m.Rank = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Rank |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalConsum", wireType)
			}
			m.TotalConsum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalConsum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMemberRankListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMemberRankListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMemberRankListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartIdx", wireType)
			}
			m.StartIdx = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartIdx |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Getsize", wireType)
			}
			m.Getsize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Getsize |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ViewCnt", wireType)
			}
			m.ViewCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ViewCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsTestMod", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsTestMod = bool(v != 0)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field InvisibleVaild", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.InvisibleVaild = bool(v != 0)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsLiveChannel", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsLiveChannel = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start_idx")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("getsize")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMemberRankListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMemberRankListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMemberRankListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MemberList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MemberList = append(m.MemberList, &ChannelMemberRank{})
			if err := m.MemberList[len(m.MemberList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AllMemberSize", wireType)
			}
			m.AllMemberSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AllMemberSize |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MyRankInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.MyRankInfo == nil {
				m.MyRankInfo = &ChannelMemberRank{}
			}
			if err := m.MyRankInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DValue", wireType)
			}
			m.DValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Rank", wireType)
			}
			m.Rank = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Rank |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("all_member_size")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MemberConsumeInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MemberConsumeInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MemberConsumeInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConsumeCnt", wireType)
			}
			m.ConsumeCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConsumeCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VipLevelInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.VipLevelInfo == nil {
				m.VipLevelInfo = &ChannelMemberVip{}
			}
			if err := m.VipLevelInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsAutoHiddenConsume", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsAutoHiddenConsume = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("consume_cnt")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetConsumeTopNReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetConsumeTopNReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetConsumeTopNReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginIdx", wireType)
			}
			m.BeginIdx = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginIdx |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ViewCnt", wireType)
			}
			m.ViewCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ViewCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsShowHidden", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsShowHidden = bool(v != 0)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("begin_idx")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetConsumeTopNResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetConsumeTopNResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetConsumeTopNResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConsumeList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ConsumeList = append(m.ConsumeList, &MemberConsumeInfo{})
			if err := m.ConsumeList[len(m.ConsumeList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalCnt", wireType)
			}
			m.TotalCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MyRankInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.MyRankInfo == nil {
				m.MyRankInfo = &MemberConsumeInfo{}
			}
			if err := m.MyRankInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DValue", wireType)
			}
			m.DValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Rank", wireType)
			}
			m.Rank = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Rank |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsCanHideConsume", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsCanHideConsume = bool(v != 0)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsHiddenConsume", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsHiddenConsume = bool(v != 0)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsAutoHiddenConsume", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsAutoHiddenConsume = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserConsumeInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserConsumeInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserConsumeInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserConsumeInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserConsumeInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserConsumeInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConsumeInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ConsumeInfo == nil {
				m.ConsumeInfo = &MemberConsumeInfo{}
			}
			if err := m.ConsumeInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("consume_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatGetUserConsumeInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatGetUserConsumeInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatGetUserConsumeInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelmemberviprank
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelmemberviprank
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelmemberviprank
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelmemberviprank
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatGetUserConsumeInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatGetUserConsumeInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatGetUserConsumeInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConsumeInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ConsumeInfo = append(m.ConsumeInfo, &MemberConsumeInfo{})
			if err := m.ConsumeInfo[len(m.ConsumeInfo)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveOnlineMemberRankReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveOnlineMemberRankReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveOnlineMemberRankReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveOnlineMemberRankResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveOnlineMemberRankResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveOnlineMemberRankResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RemovedUid", wireType)
			}
			m.RemovedUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RemovedUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendCommBreakingNewsReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendCommBreakingNewsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendCommBreakingNewsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendCommBreakingNewsResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendCommBreakingNewsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendCommBreakingNewsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *OperAddUserChannelTCoinReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: OperAddUserChannelTCoinReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: OperAddUserChannelTCoinReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TcoinCnt", wireType)
			}
			m.TcoinCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TcoinCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *OperAddUserChannelTCoinResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: OperAddUserChannelTCoinResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: OperAddUserChannelTCoinResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsOrderidExist", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsOrderidExist = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUnderTheMircoRankReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUnderTheMircoRankReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUnderTheMircoRankReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Begin", wireType)
			}
			m.Begin = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Begin |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("begin")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUnderTheMircoRankResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUnderTheMircoRankResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUnderTheMircoRankResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelmemberviprank
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelmemberviprank
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelmemberviprank
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelmemberviprank
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CurrMemberTotal", wireType)
			}
			m.CurrMemberTotal = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CurrMemberTotal |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("curr_member_total")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUnderTheMircoOnlineCntReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUnderTheMircoOnlineCntReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUnderTheMircoOnlineCntReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelmemberviprank
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.CidList = append(m.CidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelmemberviprank
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelmemberviprank
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelmemberviprank
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.CidList = append(m.CidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field CidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SimpleChannelOnlineCount) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SimpleChannelOnlineCount: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SimpleChannelOnlineCount: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Cid", wireType)
			}
			m.Cid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Cid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OnlineCount", wireType)
			}
			m.OnlineCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OnlineCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("cid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("online_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUnderTheMircoOnlineCntResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUnderTheMircoOnlineCntResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUnderTheMircoOnlineCntResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field InfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.InfoList = append(m.InfoList, &SimpleChannelOnlineCount{})
			if err := m.InfoList[len(m.InfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMemberWeekRankListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMemberWeekRankListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMemberWeekRankListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Cid", wireType)
			}
			m.Cid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Cid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Begin", wireType)
			}
			m.Begin = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Begin |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ViewCnt", wireType)
			}
			m.ViewCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ViewCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("cid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("begin")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMemberWeekRankListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMemberWeekRankListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMemberWeekRankListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RankInfoList = append(m.RankInfoList, &MemberConsumeInfo{})
			if err := m.RankInfoList[len(m.RankInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MyRankInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.MyRankInfo == nil {
				m.MyRankInfo = &MemberConsumeInfo{}
			}
			if err := m.MyRankInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DValue", wireType)
			}
			m.DValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Rank", wireType)
			}
			m.Rank = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Rank |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelHourRankInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelHourRankInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelHourRankInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Cid", wireType)
			}
			m.Cid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Cid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Score", wireType)
			}
			m.Score = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Score |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Rank", wireType)
			}
			m.Rank = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Rank |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DValue", wireType)
			}
			m.DValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagId", wireType)
			}
			m.TagId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("cid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("score")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("rank")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelHourRankListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelHourRankListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelHourRankListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Hour", wireType)
			}
			m.Hour = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Hour |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Begin", wireType)
			}
			m.Begin = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Begin |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Cid", wireType)
			}
			m.Cid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Cid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ViewCnt", wireType)
			}
			m.ViewCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ViewCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("hour")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("begin")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelHourRankListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelHourRankListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelHourRankListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RankInfoList = append(m.RankInfoList, &ChannelHourRankInfo{})
			if err := m.RankInfoList[len(m.RankInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RealViewCnt", wireType)
			}
			m.RealViewCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RealViewCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MyRankInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.MyRankInfo == nil {
				m.MyRankInfo = &ChannelHourRankInfo{}
			}
			if err := m.MyRankInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("real_view_cnt")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetHourRankByIdReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetHourRankByIdReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetHourRankByIdReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Cid", wireType)
			}
			m.Cid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Cid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Hour", wireType)
			}
			m.Hour = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Hour |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ViewCnt", wireType)
			}
			m.ViewCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ViewCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("cid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("hour")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("view_cnt")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetHourRankByIdResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetHourRankByIdResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetHourRankByIdResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.RankInfo == nil {
				m.RankInfo = &ChannelHourRankInfo{}
			}
			if err := m.RankInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RealViewCnt", wireType)
			}
			m.RealViewCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RealViewCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("rank_info")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("real_view_cnt")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *IncrChannelHourRankScoreReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: IncrChannelHourRankScoreReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: IncrChannelHourRankScoreReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Cid", wireType)
			}
			m.Cid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Cid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Hour", wireType)
			}
			m.Hour = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Hour |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Score", wireType)
			}
			m.Score = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Score |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("cid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("hour")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("score")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *IncrChannelHourRankScoreResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: IncrChannelHourRankScoreResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: IncrChannelHourRankScoreResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *IncrMemberWeekRankScoreReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: IncrMemberWeekRankScoreReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: IncrMemberWeekRankScoreReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Cid", wireType)
			}
			m.Cid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Cid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ts", wireType)
			}
			m.Ts = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ts |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Score", wireType)
			}
			m.Score = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Score |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("cid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ts")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("score")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *IncrMemberWeekRankScoreResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: IncrMemberWeekRankScoreResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: IncrMemberWeekRankScoreResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateUserTempInvisibleStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateUserTempInvisibleStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateUserTempInvisibleStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Invisible", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Invisible = bool(v != 0)
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Level", wireType)
			}
			m.Level = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Level |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("invisible")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("level")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateUserTempInvisibleStatusResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateUserTempInvisibleStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateUserTempInvisibleStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMemberNobilityInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMemberNobilityInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMemberNobilityInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMemberNobilityInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMemberNobilityInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMemberNobilityInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NobilityLevel", wireType)
			}
			m.NobilityLevel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NobilityLevel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Invisible", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Invisible = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("nobility_level")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("invisible")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveLiveChannelOnlineRankReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveLiveChannelOnlineRankReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveLiveChannelOnlineRankReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveLiveChannelOnlineRankResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveLiveChannelOnlineRankResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveLiveChannelOnlineRankResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *HideUserChannelConsumeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: HideUserChannelConsumeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: HideUserChannelConsumeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsHide", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsHide = bool(v != 0)
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_hide")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *HideUserChannelConsumeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: HideUserChannelConsumeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: HideUserChannelConsumeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmemberviprank(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmemberviprank
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipChannelmemberviprank(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowChannelmemberviprank
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChannelmemberviprank
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthChannelmemberviprank
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowChannelmemberviprank
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipChannelmemberviprank(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthChannelmemberviprank = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowChannelmemberviprank   = fmt2.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("src/channelmemberVipRank/channelmemberviprank.proto", fileDescriptorChannelmemberviprank)
}

var fileDescriptorChannelmemberviprank = []byte{
	// 2485 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x59, 0x4d, 0x6c, 0x1c, 0x49,
	0x15, 0x4e, 0xcf, 0x8c, 0xed, 0x99, 0xe7, 0x9f, 0x38, 0x15, 0x6f, 0x32, 0xee, 0x24, 0x4e, 0xbb,
	0x76, 0x21, 0xde, 0x24, 0x76, 0x36, 0x0e, 0x61, 0xb5, 0x23, 0xcb, 0x22, 0xf1, 0xee, 0x26, 0x66,
	0xe3, 0x58, 0xb2, 0x9d, 0x2c, 0x3f, 0x87, 0x56, 0xbb, 0xbb, 0x62, 0x97, 0xdc, 0xd3, 0xdd, 0x4c,
	0xf5, 0x4c, 0xec, 0x3d, 0x20, 0x38, 0xac, 0x40, 0x9c, 0x10, 0x57, 0x84, 0x38, 0x24, 0xfc, 0x49,
	0x1c, 0x38, 0xae, 0x84, 0x84, 0x90, 0x38, 0x90, 0x05, 0x96, 0xe5, 0xc6, 0x01, 0x89, 0x9f, 0xc0,
	0x21, 0x27, 0xc4, 0x0d, 0x89, 0x13, 0xaa, 0xea, 0xee, 0x99, 0xea, 0x9e, 0xae, 0xf1, 0x8c, 0xbd,
	0x7b, 0xb2, 0xa6, 0xea, 0x75, 0xd5, 0xf7, 0xde, 0xfb, 0xea, 0xfd, 0x19, 0x6e, 0xb0, 0x86, 0x7d,
	0xcd, 0xde, 0xb5, 0x3c, 0x8f, 0xb8, 0x75, 0x52, 0xdf, 0x26, 0x8d, 0x87, 0x34, 0xd8, 0xb0, 0xbc,
	0xbd, 0xf4, 0x62, 0x8b, 0x06, 0x0d, 0xcb, 0xdb, 0x5b, 0x08, 0x1a, 0x7e, 0xe8, 0xa3, 0xa9, 0xbc,
	0x3d, 0xfd, 0x15, 0xdb, 0xaf, 0xd7, 0x7d, 0xef, 0x5a, 0xe8, 0xb6, 0x02, 0x6a, 0xef, 0xb9, 0xe4,
	0x1a, 0xdb, 0xdb, 0x6e, 0x52, 0x37, 0xa4, 0x5e, 0x78, 0x10, 0x90, 0xe8, 0x5b, 0xfc, 0xbf, 0x02,
	0x4c, 0xae, 0x44, 0x9f, 0xaf, 0x25, 0xf7, 0xa1, 0x39, 0x18, 0xb7, 0x9b, 0x8d, 0x86, 0xe9, 0x92,
	0x16, 0x71, 0x4d, 0xea, 0x54, 0x35, 0x43, 0x9b, 0x1b, 0xbf, 0x5d, 0x7a, 0xf6, 0xd7, 0x8b, 0x27,
	0x36, 0x46, 0xf9, 0xd6, 0x3d, 0xbe, 0xb3, 0xea, 0xa0, 0xab, 0x70, 0x52, 0x92, 0xf4, 0xac, 0x3a,
	0xa9, 0x16, 0x0c, 0x6d, 0xae, 0x12, 0xcb, 0x8e, 0xb7, 0x65, 0xef, 0x5b, 0x75, 0x82, 0x16, 0x60,
	0x52, 0x92, 0x6e, 0x59, 0x6e, 0x93, 0x54, 0x8b, 0xd2, 0xd1, 0x13, 0x6d, 0xf1, 0x87, 0x7c, 0x8f,
	0xe3, 0xf0, 0xc8, 0x7e, 0xd8, 0xc1, 0x51, 0x92, 0x71, 0xf0, 0x2d, 0x09, 0x87, 0x24, 0x29, 0x70,
	0x0c, 0xc9, 0x38, 0xda, 0xb2, 0x02, 0xc7, 0x4d, 0x98, 0x92, 0xa4, 0xeb, 0xd4, 0x8b, 0xb1, 0x0c,
	0x4b, 0xc7, 0x9f, 0x6a, 0x7f, 0xb2, 0x46, 0xbd, 0x08, 0xce, 0x15, 0x98, 0xf0, 0xfc, 0x6d, 0xea,
	0xd2, 0xf0, 0x20, 0xfa, 0xb4, 0x3a, 0x22, 0x7d, 0x30, 0x9e, 0xec, 0x89, 0x8f, 0x10, 0x86, 0x0a,
	0xf5, 0x5a, 0x94, 0xd1, 0x6d, 0x97, 0x54, 0xcb, 0x86, 0x36, 0x57, 0x8e, 0xe5, 0x3a, 0xcb, 0xf8,
	0xbf, 0x1a, 0x9c, 0x4a, 0x19, 0x9f, 0x7b, 0x1a, 0x9d, 0x81, 0x62, 0x53, 0xd8, 0xbc, 0xd0, 0x3e,
	0x9b, 0x2f, 0xa0, 0x29, 0x28, 0x84, 0x4c, 0x98, 0x37, 0x59, 0x2e, 0x84, 0x0c, 0xbd, 0x0c, 0xc0,
	0xdd, 0x9d, 0x63, 0xcd, 0x0a, 0x5f, 0x8f, 0x90, 0xdf, 0x83, 0x89, 0x16, 0x0d, 0x12, 0x3b, 0x7a,
	0x8f, 0x7c, 0x61, 0xc9, 0xd1, 0xc5, 0xcf, 0x2e, 0xe4, 0xd2, 0x2a, 0x4b, 0x88, 0x8d, 0xb1, 0x16,
	0x0d, 0x22, 0x53, 0x7b, 0x8f, 0x7c, 0x54, 0x85, 0x12, 0x17, 0x13, 0x16, 0x4e, 0x2e, 0x13, 0x2b,
	0xe8, 0x12, 0x8c, 0x85, 0x7e, 0x68, 0xb9, 0xa6, 0xed, 0x7b, 0xac, 0x59, 0x4f, 0x19, 0x74, 0x54,
	0xec, 0xac, 0x88, 0x0d, 0xfc, 0xe7, 0x02, 0x4c, 0xdd, 0x21, 0x61, 0x47, 0xeb, 0x7b, 0x94, 0x85,
	0x1b, 0xe4, 0x6b, 0x5c, 0x9d, 0x18, 0x92, 0x99, 0xb1, 0x41, 0x25, 0x5e, 0x5f, 0x75, 0xd0, 0x39,
	0x18, 0xf6, 0x03, 0x93, 0x1b, 0xa9, 0x20, 0x09, 0x0c, 0xf9, 0xc1, 0x03, 0xea, 0xa0, 0x59, 0xa8,
	0xb0, 0xd0, 0x6a, 0x84, 0x26, 0x75, 0xf6, 0xab, 0x45, 0x69, 0xbf, 0x2c, 0x96, 0x57, 0x9d, 0x7d,
	0x34, 0x03, 0x23, 0x3b, 0x24, 0x64, 0xf4, 0x3d, 0x52, 0x2d, 0x49, 0x02, 0xc9, 0x62, 0xe2, 0x01,
	0x59, 0x3f, 0xe1, 0x81, 0x8b, 0x50, 0x6e, 0x51, 0xf2, 0xd8, 0xb4, 0xbd, 0x30, 0xa5, 0xda, 0x08,
	0x5f, 0x5d, 0xf1, 0x42, 0xf4, 0x0a, 0x8c, 0x52, 0x66, 0x86, 0x84, 0x85, 0x66, 0xdd, 0x77, 0x04,
	0x3d, 0x3a, 0x6e, 0x67, 0x5b, 0x84, 0x85, 0x6b, 0xbe, 0x83, 0xe6, 0xe1, 0x64, 0x9b, 0x03, 0x66,
	0xcb, 0xa2, 0xae, 0x93, 0x22, 0xc8, 0x44, 0x7b, 0xf3, 0x21, 0xdf, 0xe3, 0xdc, 0xa6, 0xcc, 0x74,
	0x69, 0x8b, 0x98, 0xb1, 0x09, 0xaa, 0x15, 0x49, 0x7c, 0x9c, 0xb2, 0x7b, 0xb4, 0x45, 0x62, 0x9f,
	0xe1, 0x0f, 0x0a, 0xf0, 0x52, 0x8e, 0x65, 0x59, 0xd0, 0x9f, 0x69, 0xef, 0xc2, 0x68, 0xc4, 0x05,
	0xd3, 0xa5, 0x2c, 0xac, 0x16, 0x8c, 0xe2, 0xdc, 0xe8, 0xe2, 0xa5, 0x3e, 0x68, 0xc2, 0xaf, 0xda,
	0x80, 0x48, 0x80, 0x5f, 0xc9, 0x61, 0x5b, 0xae, 0x6b, 0xc6, 0xa7, 0x09, 0x63, 0xcb, 0xde, 0x18,
	0xb7, 0xdc, 0xf8, 0xcb, 0x4d, 0x6e, 0xf2, 0x55, 0x18, 0xab, 0x1f, 0x98, 0x82, 0xc9, 0x12, 0x3f,
	0x07, 0xb8, 0xf8, 0x80, 0xff, 0x15, 0xf4, 0xbc, 0x00, 0x23, 0x4e, 0xfc, 0x1c, 0x64, 0x0f, 0x0e,
	0x3b, 0xd1, 0x5b, 0x48, 0xd8, 0x3b, 0x9c, 0x65, 0x2f, 0xfe, 0x8b, 0x06, 0xa7, 0xa2, 0x33, 0x23,
	0x96, 0x12, 0x71, 0x9c, 0xea, 0x39, 0x7e, 0x06, 0x46, 0x23, 0x96, 0x13, 0xc1, 0x07, 0x99, 0x89,
	0x10, 0x6f, 0x70, 0x4a, 0x74, 0x3f, 0xbd, 0xe2, 0x31, 0x9e, 0xde, 0x1b, 0x70, 0x86, 0x32, 0xd3,
	0x6a, 0x86, 0xbe, 0xb9, 0x4b, 0x1d, 0x87, 0x78, 0xf1, 0x4b, 0x23, 0xc2, 0x60, 0x09, 0x25, 0x4e,
	0x53, 0x76, 0xab, 0x19, 0xfa, 0x77, 0x85, 0x44, 0xac, 0x0b, 0x7e, 0xbf, 0x00, 0xa7, 0xee, 0x90,
	0x30, 0xfe, 0xb9, 0xe5, 0x07, 0xf7, 0xfb, 0x7e, 0x6f, 0xb3, 0x50, 0xd9, 0x26, 0x3b, 0xd4, 0x13,
	0x4f, 0x4a, 0x56, 0xb4, 0x2c, 0x96, 0xf9, 0x93, 0xd2, 0x61, 0xc8, 0xa5, 0x75, 0x1a, 0xa6, 0x7c,
	0x1c, 0x2d, 0x25, 0x16, 0x2c, 0xf5, 0x7a, 0x4e, 0x43, 0x79, 0xcf, 0xe9, 0x32, 0x4c, 0x50, 0x66,
	0xb2, 0x5d, 0xff, 0x71, 0xac, 0xad, 0x70, 0x5a, 0xa2, 0xe5, 0x18, 0x65, 0x9b, 0xbb, 0xfe, 0xe3,
	0x48, 0x4b, 0x1e, 0x7a, 0x12, 0x45, 0x78, 0x7a, 0x4b, 0x85, 0xe6, 0xd1, 0x78, 0x67, 0xeb, 0x20,
	0x20, 0xf8, 0x67, 0x45, 0x40, 0x59, 0x3b, 0xb0, 0x00, 0x7d, 0x11, 0xc6, 0x12, 0x77, 0x0a, 0xe6,
	0x6b, 0xbd, 0x98, 0xdf, 0xc5, 0x92, 0x8d, 0x84, 0x0b, 0x82, 0xfa, 0xb3, 0x50, 0x89, 0xc3, 0xa0,
	0x20, 0x46, 0x07, 0x48, 0x39, 0x8a, 0x81, 0x5e, 0xd8, 0xc5, 0xf7, 0x62, 0x2f, 0xbe, 0x77, 0x5f,
	0xa7, 0xe0, 0x7b, 0xa9, 0x07, 0xdf, 0xbb, 0xa3, 0xf5, 0x0d, 0x38, 0x4d, 0x99, 0x69, 0x5b, 0x1e,
	0xb7, 0x2e, 0x69, 0x33, 0x49, 0xb6, 0xf1, 0x24, 0x65, 0x2b, 0x96, 0x77, 0x97, 0x3a, 0x24, 0xbe,
	0x1d, 0xbd, 0x06, 0xa7, 0x28, 0xcb, 0x92, 0x4f, 0x0e, 0x74, 0x27, 0x29, 0x4b, 0x11, 0xaf, 0x07,
	0x67, 0xcb, 0x87, 0x71, 0x76, 0x4b, 0xc4, 0xb2, 0x07, 0x2c, 0xad, 0x7c, 0xbf, 0xb4, 0x8d, 0x79,
	0x57, 0xc8, 0xbc, 0x5c, 0xec, 0xc0, 0x99, 0xbc, 0x53, 0xd3, 0x24, 0x10, 0x5e, 0xe1, 0x07, 0x1f,
	0x81, 0x04, 0xfc, 0x07, 0xfe, 0x0a, 0x54, 0x6f, 0x5b, 0xe1, 0x31, 0xe0, 0x4f, 0x43, 0xb9, 0x49,
	0x9d, 0x4e, 0x1c, 0x1e, 0xdf, 0x18, 0x69, 0x52, 0x87, 0x13, 0x0c, 0xef, 0xc0, 0xb4, 0xe2, 0xec,
	0x5c, 0x25, 0x8a, 0x47, 0x56, 0xe2, 0x4b, 0x30, 0xbd, 0x41, 0xea, 0x7e, 0x8b, 0xac, 0x7b, 0x2e,
	0xf5, 0x88, 0x14, 0x71, 0x8f, 0xeb, 0x84, 0x15, 0xd0, 0x55, 0x27, 0xb3, 0x80, 0x07, 0xd7, 0x86,
	0xd8, 0x75, 0xcc, 0x66, 0xa6, 0xfe, 0x84, 0x78, 0xe3, 0x01, 0x75, 0xf0, 0x43, 0x38, 0xbb, 0x49,
	0x3c, 0x67, 0xc5, 0xaf, 0xd7, 0x6f, 0x37, 0x88, 0xb5, 0x47, 0xbd, 0x9d, 0xfb, 0xe4, 0x31, 0xcb,
	0x03, 0xa7, 0xf5, 0x04, 0x97, 0x8e, 0x4c, 0x58, 0x87, 0x6a, 0xfe, 0xb9, 0x2c, 0xc0, 0x3f, 0xd0,
	0x40, 0x5f, 0x0f, 0x48, 0xe3, 0x96, 0xe3, 0x08, 0xeb, 0xc7, 0xa1, 0x65, 0xc5, 0xa7, 0xde, 0x71,
	0xef, 0x15, 0x81, 0xc3, 0xf6, 0xa9, 0x27, 0x02, 0x47, 0x31, 0x15, 0x38, 0xf8, 0x32, 0x0f, 0x1c,
	0x17, 0xa1, 0xec, 0x37, 0x1c, 0xd2, 0x48, 0xca, 0xe1, 0xa4, 0xc4, 0x1d, 0x11, 0xab, 0xab, 0x0e,
	0x5e, 0x83, 0x73, 0x4a, 0x78, 0x2c, 0xe0, 0x35, 0x38, 0x65, 0xa6, 0x10, 0xa6, 0x8e, 0x49, 0xf6,
	0xa3, 0x58, 0x27, 0x57, 0x1f, 0x6c, 0x3d, 0xda, 0x7c, 0x8b, 0xef, 0xe1, 0x16, 0x9c, 0xe5, 0x3c,
	0xf3, 0x1c, 0xd2, 0xd8, 0xda, 0x25, 0x6b, 0xb4, 0x61, 0xfb, 0x03, 0xf9, 0x5f, 0x87, 0x21, 0x91,
	0x24, 0xd2, 0xa5, 0x9a, 0x58, 0xea, 0x95, 0x34, 0xf0, 0x0e, 0x54, 0xf3, 0xef, 0x65, 0x41, 0xea,
	0x65, 0x68, 0xa9, 0x97, 0xc1, 0xc3, 0x93, 0x68, 0x31, 0xe2, 0xb2, 0x43, 0xc4, 0xdb, 0xd4, 0xd5,
	0xa2, 0x5f, 0x89, 0xd8, 0xb6, 0xc5, 0x37, 0xf1, 0x1b, 0x70, 0x3e, 0x7b, 0x51, 0x44, 0xc9, 0x15,
	0x4f, 0x54, 0xa4, 0xd3, 0x50, 0xb6, 0x33, 0x97, 0xd9, 0xf1, 0x33, 0xfc, 0x2a, 0x54, 0x37, 0x69,
	0x3d, 0x70, 0x93, 0xe2, 0x2b, 0xfe, 0xce, 0x6f, 0x7a, 0x22, 0xe9, 0xd9, 0xd9, 0xb2, 0xc1, 0xa6,
	0x0e, 0xcf, 0x53, 0xbe, 0x10, 0x33, 0x6d, 0x2e, 0x97, 0xc2, 0x36, 0xea, 0x77, 0x0e, 0xc0, 0x2e,
	0x5c, 0xe8, 0x81, 0x8b, 0x05, 0xe8, 0x1d, 0xde, 0x61, 0x3c, 0xf2, 0xe5, 0x74, 0xb5, 0x90, 0xff,
	0xc8, 0x55, 0x20, 0x37, 0xca, 0xfc, 0x00, 0xa1, 0xca, 0x13, 0x4d, 0xd8, 0x3b, 0x32, 0xcc, 0xbb,
	0x84, 0xec, 0xc9, 0x45, 0xb9, 0x4a, 0x97, 0x23, 0xfa, 0xf6, 0xc8, 0x05, 0x01, 0xfe, 0xb7, 0x06,
	0xd3, 0x0a, 0x94, 0x2c, 0x40, 0x6b, 0x30, 0xd1, 0x4e, 0xa8, 0x47, 0x4a, 0xe2, 0x63, 0x8d, 0x38,
	0xa7, 0x0a, 0x2a, 0x65, 0x53, 0x74, 0xe1, 0x13, 0x49, 0xd1, 0xc5, 0x1e, 0x29, 0xba, 0xd4, 0x55,
	0x92, 0xfe, 0x50, 0x83, 0xd3, 0xb1, 0xdf, 0xee, 0xfa, 0xcd, 0x46, 0xfb, 0xc0, 0x1e, 0x1e, 0x61,
	0xb6, 0xdf, 0x20, 0x69, 0x8f, 0x88, 0xa5, 0xf6, 0x2d, 0xb2, 0x43, 0xa2, 0x42, 0xe0, 0x90, 0x0a,
	0xe2, 0x1c, 0x0c, 0x87, 0xd6, 0x8e, 0x99, 0xe9, 0x88, 0x86, 0x42, 0x6b, 0x67, 0xd5, 0xc1, 0xbf,
	0x8e, 0x5c, 0x92, 0x01, 0x99, 0x30, 0xa7, 0x0a, 0x25, 0x51, 0x8d, 0xc9, 0x40, 0xc5, 0x0a, 0xdf,
	0xd9, 0xf5, 0x9b, 0x8d, 0x14, 0x50, 0xb1, 0xd2, 0x61, 0x55, 0xb1, 0x07, 0xab, 0x4a, 0xb9, 0xac,
	0xb2, 0xb3, 0x5d, 0x9b, 0xdd, 0x47, 0xd7, 0x86, 0xff, 0xa5, 0x81, 0xae, 0x52, 0x81, 0x05, 0x68,
	0x5d, 0x41, 0xab, 0x57, 0x7b, 0x56, 0xf0, 0xb2, 0xbb, 0x32, 0xc4, 0x9a, 0x83, 0xf1, 0x06, 0xb1,
	0x5c, 0xb3, 0x8d, 0x2a, 0x15, 0x03, 0xf8, 0xd6, 0xc3, 0xb8, 0x00, 0x7e, 0x27, 0xb7, 0x4a, 0x1c,
	0xe0, 0x62, 0x89, 0x84, 0xf8, 0x9b, 0x9a, 0x28, 0x7c, 0x93, 0xfd, 0xdb, 0x07, 0xab, 0x4e, 0xaf,
	0xc7, 0x9d, 0xb8, 0xae, 0xa0, 0x74, 0x5d, 0xb1, 0xcb, 0x75, 0xb2, 0xa9, 0x53, 0x9d, 0x75, 0x62,
	0xea, 0x6f, 0x69, 0x70, 0xba, 0x0b, 0x03, 0x0b, 0xd0, 0xdb, 0x50, 0xe9, 0x68, 0x19, 0x55, 0x5d,
	0x03, 0x68, 0x59, 0x4e, 0xcc, 0xdb, 0xbf, 0x69, 0xf1, 0xfb, 0x1a, 0x9c, 0x5b, 0xf5, 0xec, 0x46,
	0xe6, 0xbc, 0x4d, 0xfe, 0x54, 0x3e, 0x69, 0xb3, 0xb4, 0x5f, 0x65, 0xa9, 0xeb, 0x55, 0xe2, 0x19,
	0x38, 0xaf, 0x86, 0xc1, 0x02, 0xfc, 0x75, 0xd0, 0xf9, 0x7e, 0x3a, 0xe4, 0x1d, 0x8a, 0x52, 0x51,
	0x75, 0xc5, 0x33, 0x24, 0x19, 0x61, 0x21, 0x64, 0x3d, 0xf1, 0x5d, 0x88, 0xcc, 0x94, 0x7b, 0x3f,
	0x0b, 0xf0, 0x33, 0x0d, 0x8c, 0x07, 0x81, 0x63, 0x85, 0x84, 0x57, 0x1b, 0x5b, 0xa4, 0x1e, 0xac,
	0x26, 0xd3, 0x8b, 0xcd, 0xd0, 0x0a, 0x9b, 0x2c, 0x46, 0x99, 0xdb, 0x42, 0xa7, 0x0b, 0x88, 0x42,
	0x7e, 0x01, 0x91, 0x1a, 0xa4, 0x71, 0xe4, 0xdd, 0x83, 0x34, 0x11, 0x16, 0xc4, 0x40, 0x2e, 0x1d,
	0x16, 0xc4, 0x20, 0x2e, 0xdb, 0x18, 0x0e, 0xa9, 0x1a, 0xc3, 0x97, 0x61, 0xf6, 0x10, 0x4d, 0x58,
	0x80, 0xdf, 0x95, 0xd2, 0xe4, 0xfd, 0x78, 0xe0, 0x97, 0x54, 0xf5, 0xc7, 0x51, 0x13, 0xbb, 0x52,
	0x66, 0x4b, 0x1f, 0xcc, 0x82, 0x9c, 0xc9, 0xa3, 0x7c, 0x49, 0xaf, 0xc9, 0x63, 0x21, 0xd7, 0x60,
	0xf8, 0x2d, 0x98, 0x89, 0xaa, 0x6f, 0x69, 0x74, 0x14, 0x15, 0x06, 0x83, 0x14, 0x77, 0x78, 0x16,
	0x2e, 0xf6, 0x3c, 0x86, 0x05, 0xf8, 0x31, 0x4c, 0xf3, 0xf6, 0x51, 0xaa, 0x45, 0xe3, 0x24, 0x79,
	0x6c, 0x62, 0x5c, 0x80, 0x91, 0xa8, 0x13, 0x4d, 0xd3, 0x62, 0x58, 0xf4, 0x9f, 0x04, 0x9f, 0x07,
	0x5d, 0x75, 0x31, 0x0b, 0x2e, 0x1b, 0x5d, 0x79, 0x95, 0x73, 0x00, 0x55, 0x60, 0x68, 0x6b, 0x7d,
	0xeb, 0xd6, 0xbd, 0xc9, 0x13, 0x8b, 0xff, 0xd1, 0x61, 0xaa, 0x6b, 0x1a, 0xc3, 0xb3, 0xe5, 0x6f,
	0x35, 0x31, 0x48, 0x49, 0x4f, 0xd8, 0xd0, 0xe5, 0xfc, 0x70, 0x95, 0x37, 0xe4, 0xd4, 0xaf, 0xf4,
	0x2d, 0xcb, 0x02, 0xfc, 0xe5, 0x6f, 0x3c, 0x7d, 0x51, 0xd4, 0xbe, 0xf3, 0xf4, 0x45, 0x11, 0xf6,
	0x6b, 0xdb, 0x35, 0xb7, 0xd6, 0xac, 0xd5, 0x6b, 0xdf, 0x7b, 0xfa, 0xa2, 0xf8, 0x85, 0xf9, 0x7d,
	0x63, 0x29, 0x3e, 0xc8, 0xa0, 0xce, 0xb2, 0x31, 0xbf, 0x6d, 0x2c, 0x89, 0x34, 0x69, 0x50, 0x67,
	0x7f, 0xd9, 0x98, 0x77, 0x8d, 0x25, 0x91, 0x19, 0x97, 0x8d, 0xf9, 0xa6, 0xb1, 0xd4, 0x14, 0x22,
	0x75, 0x63, 0x29, 0x89, 0x8d, 0xcb, 0xe8, 0x49, 0x94, 0x11, 0x32, 0x4d, 0x24, 0x52, 0xc3, 0xeb,
	0x6e, 0x65, 0xf5, 0xab, 0xfd, 0x0b, 0xb3, 0x00, 0x2f, 0x72, 0x65, 0x0a, 0x5c, 0x99, 0xd2, 0x7e,
	0xad, 0x29, 0xd4, 0xb8, 0x28, 0xa9, 0xb1, 0xfa, 0x66, 0x84, 0x35, 0xb4, 0x1a, 0x3b, 0x24, 0x34,
	0x04, 0x64, 0xf4, 0x2b, 0x0d, 0x26, 0xd2, 0x03, 0x1b, 0x74, 0x49, 0x79, 0x69, 0x7a, 0xbc, 0xa5,
	0xcf, 0xf5, 0x27, 0x98, 0x98, 0xb9, 0xf8, 0xa9, 0x98, 0xf9, 0xe7, 0x1a, 0xbc, 0x94, 0xdb, 0xae,
	0x23, 0x45, 0xbd, 0xae, 0x9a, 0x1b, 0xe8, 0xd7, 0x06, 0x92, 0x4f, 0xec, 0x5d, 0x52, 0xda, 0x3b,
	0xd2, 0x27, 0x6d, 0x70, 0xf4, 0x4b, 0x4d, 0xcc, 0xe6, 0xbb, 0x5a, 0x2f, 0x34, 0xaf, 0x76, 0x75,
	0x4e, 0x7b, 0xa8, 0x2f, 0x0c, 0x22, 0xce, 0x02, 0xfc, 0x26, 0xc7, 0x3a, 0xc4, 0xb1, 0x8e, 0xd8,
	0xb5, 0x26, 0xf7, 0x01, 0x87, 0xbb, 0x30, 0x6f, 0x77, 0xc3, 0x6d, 0xa6, 0xfc, 0x20, 0xfb, 0x00,
	0xfd, 0x24, 0xaa, 0x47, 0xf3, 0xfb, 0x26, 0xb4, 0xd8, 0x1f, 0x26, 0xb9, 0x01, 0xd4, 0x6f, 0x0c,
	0xfc, 0x0d, 0x0b, 0xf0, 0x2c, 0x57, 0x66, 0x98, 0x2b, 0x53, 0xb0, 0x85, 0xd9, 0x27, 0xb3, 0x7a,
	0xa0, 0x8f, 0x35, 0x69, 0x52, 0x2f, 0x37, 0x33, 0x68, 0xe1, 0x90, 0xf8, 0x90, 0xe9, 0xcf, 0x54,
	0xb4, 0x50, 0x76, 0x4a, 0xf8, 0x01, 0x47, 0x37, 0x92, 0x43, 0xf6, 0x25, 0x25, 0xd9, 0xfb, 0x21,
	0xfa, 0xdf, 0x34, 0x31, 0x59, 0xcb, 0x29, 0xa4, 0x91, 0x1a, 0x62, 0x7e, 0xe7, 0xa0, 0xbf, 0x36,
	0xd8, 0x07, 0x2c, 0xc0, 0xdb, 0x5c, 0xa9, 0x32, 0x57, 0x6a, 0x2c, 0x52, 0x2a, 0xac, 0xd5, 0x6b,
	0xbb, 0x42, 0xad, 0x3b, 0x7d, 0xaa, 0x15, 0x1a, 0x4b, 0xbc, 0x46, 0xc8, 0xe8, 0x65, 0xcc, 0xef,
	0x1a, 0x4b, 0xbc, 0x90, 0x5b, 0x46, 0xbf, 0xd0, 0xe0, 0x64, 0xa6, 0x7e, 0x45, 0xea, 0x18, 0x93,
	0x29, 0xb5, 0xf5, 0x57, 0xfb, 0x94, 0x64, 0x01, 0xbe, 0xcb, 0x95, 0xa9, 0x70, 0x65, 0xca, 0xfb,
	0x92, 0x22, 0xd7, 0xbb, 0x14, 0x39, 0x14, 0xf2, 0x4f, 0x35, 0x38, 0x93, 0x3f, 0x69, 0x53, 0x39,
	0x45, 0x39, 0xf1, 0x53, 0x39, 0x45, 0x3d, 0xc8, 0xc3, 0x57, 0xb8, 0x1e, 0x4e, 0x2a, 0x00, 0x55,
	0xf3, 0x02, 0x90, 0x88, 0x3c, 0x4f, 0x34, 0x98, 0xca, 0x9b, 0xbb, 0xa9, 0x22, 0x8f, 0x62, 0xf6,
	0xa7, 0x8a, 0x3c, 0xca, 0x91, 0xde, 0x55, 0x0e, 0xf2, 0x99, 0xd6, 0x27, 0xca, 0x8f, 0x34, 0x38,
	0xab, 0x98, 0xb0, 0x21, 0x85, 0x81, 0xd4, 0xf3, 0x42, 0xfd, 0xfa, 0x80, 0x5f, 0xb0, 0x00, 0xaf,
	0x71, 0xb8, 0x1f, 0x6a, 0x31, 0x39, 0x78, 0xa4, 0x74, 0x04, 0xe4, 0x9b, 0x2a, 0xc8, 0x82, 0xee,
	0x62, 0x82, 0x68, 0x44, 0xe4, 0x70, 0x8c, 0xa5, 0x64, 0x88, 0xb8, 0x8c, 0x3e, 0xd4, 0xa0, 0xaa,
	0x6a, 0x41, 0x90, 0x02, 0x5e, 0x8f, 0xce, 0x49, 0x5f, 0x1c, 0xf4, 0x13, 0x16, 0xe0, 0xb7, 0xb9,
	0x4a, 0xbf, 0xd3, 0xda, 0x7c, 0xdf, 0xad, 0xb1, 0x5a, 0x14, 0xfd, 0xd5, 0x7c, 0x4f, 0x18, 0x6e,
	0xcc, 0x33, 0x63, 0x49, 0x34, 0x2b, 0xcb, 0xe8, 0x37, 0x1a, 0x9c, 0x55, 0xb4, 0x2b, 0x2a, 0xdf,
	0xa8, 0xbb, 0x2b, 0xfd, 0xfa, 0x80, 0x5f, 0xb0, 0x00, 0xaf, 0x70, 0x45, 0x7e, 0xdf, 0xf1, 0x4d,
	0x18, 0x2b, 0x72, 0x45, 0xed, 0x1b, 0xae, 0x11, 0x4b, 0x69, 0xf1, 0x0f, 0x0d, 0x2e, 0xf4, 0x6c,
	0x45, 0xd0, 0xe7, 0xf3, 0x91, 0x1d, 0xd6, 0x89, 0xe9, 0xaf, 0x1f, 0xe9, 0x3b, 0x16, 0xe0, 0x75,
	0xae, 0xd7, 0x1f, 0xba, 0x39, 0xf7, 0x7a, 0x4f, 0xce, 0xb5, 0x1b, 0x8e, 0x88, 0x73, 0x9d, 0x9a,
	0x7e, 0x19, 0xfd, 0x58, 0xce, 0x7e, 0x72, 0xc3, 0x73, 0x68, 0xf6, 0xcb, 0xb4, 0x5d, 0x87, 0x66,
	0xbf, 0x6c, 0x37, 0x15, 0x3d, 0xf7, 0x8f, 0xfa, 0x7d, 0xee, 0x1f, 0x68, 0x70, 0xae, 0x47, 0x93,
	0x83, 0x3e, 0xd7, 0x2b, 0x26, 0xaa, 0xda, 0x2b, 0xfd, 0xe6, 0x11, 0xbe, 0x4a, 0xa0, 0xff, 0xb1,
	0x5f, 0xe8, 0x3f, 0xd2, 0x00, 0x89, 0xff, 0xdd, 0xa5, 0xfa, 0x1f, 0x55, 0xd8, 0x57, 0xb6, 0x69,
	0xaa, 0xb0, 0xaf, 0x6e, 0xaf, 0x22, 0x9c, 0x1f, 0xf7, 0x89, 0x53, 0x1f, 0xfe, 0xf6, 0xd3, 0x17,
	0xc5, 0xef, 0xbf, 0x77, 0x7b, 0xf2, 0xd9, 0xf3, 0x19, 0xed, 0x4f, 0xcf, 0x67, 0xb4, 0xbf, 0x3f,
	0x9f, 0xd1, 0xbe, 0xfb, 0xcf, 0x99, 0x13, 0xff, 0x0f, 0x00, 0x00, 0xff, 0xff, 0x42, 0x6d, 0xc6,
	0x5c, 0x10, 0x25, 0x00, 0x00,
}
