// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-ktv/channel-ktv.proto

package channel_ktv // import "golang.52tt.com/protocol/services/channel-ktv"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type HandClapUserMicStatus int32

const (
	HandClapUserMicStatus_HandClapUserMicStatus_UNKNOW HandClapUserMicStatus = 0
	HandClapUserMicStatus_HandClapUserMicStatus_ON_MIC HandClapUserMicStatus = 1
	HandClapUserMicStatus_HandClapUserMicStatus_MISS   HandClapUserMicStatus = 2
)

var HandClapUserMicStatus_name = map[int32]string{
	0: "HandClapUserMicStatus_UNKNOW",
	1: "HandClapUserMicStatus_ON_MIC",
	2: "HandClapUserMicStatus_MISS",
}
var HandClapUserMicStatus_value = map[string]int32{
	"HandClapUserMicStatus_UNKNOW": 0,
	"HandClapUserMicStatus_ON_MIC": 1,
	"HandClapUserMicStatus_MISS":   2,
}

func (x HandClapUserMicStatus) String() string {
	return proto.EnumName(HandClapUserMicStatus_name, int32(x))
}
func (HandClapUserMicStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{0}
}

type ChannelKTVUpdate_ChannelKTVUpdateStatus int32

const (
	ChannelKTVUpdate_NOORDER  ChannelKTVUpdate_ChannelKTVUpdateStatus = 0
	ChannelKTVUpdate_DOWNLOAD ChannelKTVUpdate_ChannelKTVUpdateStatus = 1
	ChannelKTVUpdate_PLAYING  ChannelKTVUpdate_ChannelKTVUpdateStatus = 2
	ChannelKTVUpdate_END      ChannelKTVUpdate_ChannelKTVUpdateStatus = 3
	ChannelKTVUpdate_RESULT   ChannelKTVUpdate_ChannelKTVUpdateStatus = 4
)

var ChannelKTVUpdate_ChannelKTVUpdateStatus_name = map[int32]string{
	0: "NOORDER",
	1: "DOWNLOAD",
	2: "PLAYING",
	3: "END",
	4: "RESULT",
}
var ChannelKTVUpdate_ChannelKTVUpdateStatus_value = map[string]int32{
	"NOORDER":  0,
	"DOWNLOAD": 1,
	"PLAYING":  2,
	"END":      3,
	"RESULT":   4,
}

func (x ChannelKTVUpdate_ChannelKTVUpdateStatus) String() string {
	return proto.EnumName(ChannelKTVUpdate_ChannelKTVUpdateStatus_name, int32(x))
}
func (ChannelKTVUpdate_ChannelKTVUpdateStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{54, 0}
}

type ChannelKTVUpdate_PlayingUpdateType int32

const (
	ChannelKTVUpdate_NORMAL           ChannelKTVUpdate_PlayingUpdateType = 0
	ChannelKTVUpdate_JOIN             ChannelKTVUpdate_PlayingUpdateType = 1
	ChannelKTVUpdate_QUIT             ChannelKTVUpdate_PlayingUpdateType = 2
	ChannelKTVUpdate_LOST             ChannelKTVUpdate_PlayingUpdateType = 3
	ChannelKTVUpdate_CHANGE_MAIN      ChannelKTVUpdate_PlayingUpdateType = 4
	ChannelKTVUpdate_CHANGE_SONG      ChannelKTVUpdate_PlayingUpdateType = 5
	ChannelKTVUpdate_CHANGE_RANK      ChannelKTVUpdate_PlayingUpdateType = 6
	ChannelKTVUpdate_PERFECT_ENDING   ChannelKTVUpdate_PlayingUpdateType = 7
	ChannelKTVUpdate_FIRST_PERFECT    ChannelKTVUpdate_PlayingUpdateType = 8
	ChannelKTVUpdate_PERFECT_CHORUS   ChannelKTVUpdate_PlayingUpdateType = 9
	ChannelKTVUpdate_COOL_FIT         ChannelKTVUpdate_PlayingUpdateType = 10
	ChannelKTVUpdate_FLY_THE_AUDIENCE ChannelKTVUpdate_PlayingUpdateType = 11
	ChannelKTVUpdate_ACE              ChannelKTVUpdate_PlayingUpdateType = 12
)

var ChannelKTVUpdate_PlayingUpdateType_name = map[int32]string{
	0:  "NORMAL",
	1:  "JOIN",
	2:  "QUIT",
	3:  "LOST",
	4:  "CHANGE_MAIN",
	5:  "CHANGE_SONG",
	6:  "CHANGE_RANK",
	7:  "PERFECT_ENDING",
	8:  "FIRST_PERFECT",
	9:  "PERFECT_CHORUS",
	10: "COOL_FIT",
	11: "FLY_THE_AUDIENCE",
	12: "ACE",
}
var ChannelKTVUpdate_PlayingUpdateType_value = map[string]int32{
	"NORMAL":           0,
	"JOIN":             1,
	"QUIT":             2,
	"LOST":             3,
	"CHANGE_MAIN":      4,
	"CHANGE_SONG":      5,
	"CHANGE_RANK":      6,
	"PERFECT_ENDING":   7,
	"FIRST_PERFECT":    8,
	"PERFECT_CHORUS":   9,
	"COOL_FIT":         10,
	"FLY_THE_AUDIENCE": 11,
	"ACE":              12,
}

func (x ChannelKTVUpdate_PlayingUpdateType) String() string {
	return proto.EnumName(ChannelKTVUpdate_PlayingUpdateType_name, int32(x))
}
func (ChannelKTVUpdate_PlayingUpdateType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{54, 1}
}

type ChannelKTVPlayListUpdate_UpdateType int32

const (
	ChannelKTVPlayListUpdate_ADD ChannelKTVPlayListUpdate_UpdateType = 0
	ChannelKTVPlayListUpdate_UP  ChannelKTVPlayListUpdate_UpdateType = 1
	ChannelKTVPlayListUpdate_DEL ChannelKTVPlayListUpdate_UpdateType = 2
)

var ChannelKTVPlayListUpdate_UpdateType_name = map[int32]string{
	0: "ADD",
	1: "UP",
	2: "DEL",
}
var ChannelKTVPlayListUpdate_UpdateType_value = map[string]int32{
	"ADD": 0,
	"UP":  1,
	"DEL": 2,
}

func (x ChannelKTVPlayListUpdate_UpdateType) String() string {
	return proto.EnumName(ChannelKTVPlayListUpdate_UpdateType_name, int32(x))
}
func (ChannelKTVPlayListUpdate_UpdateType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{66, 0}
}

type KTVCopyright struct {
	SongLyric            int32    `protobuf:"varint,1,opt,name=song_lyric,json=songLyric,proto3" json:"song_lyric,omitempty"`
	Recordingval         int32    `protobuf:"varint,2,opt,name=recordingval,proto3" json:"recordingval,omitempty"`
	Channel              int32    `protobuf:"varint,3,opt,name=channel,proto3" json:"channel,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KTVCopyright) Reset()         { *m = KTVCopyright{} }
func (m *KTVCopyright) String() string { return proto.CompactTextString(m) }
func (*KTVCopyright) ProtoMessage()    {}
func (*KTVCopyright) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{0}
}
func (m *KTVCopyright) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KTVCopyright.Unmarshal(m, b)
}
func (m *KTVCopyright) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KTVCopyright.Marshal(b, m, deterministic)
}
func (dst *KTVCopyright) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KTVCopyright.Merge(dst, src)
}
func (m *KTVCopyright) XXX_Size() int {
	return xxx_messageInfo_KTVCopyright.Size(m)
}
func (m *KTVCopyright) XXX_DiscardUnknown() {
	xxx_messageInfo_KTVCopyright.DiscardUnknown(m)
}

var xxx_messageInfo_KTVCopyright proto.InternalMessageInfo

func (m *KTVCopyright) GetSongLyric() int32 {
	if m != nil {
		return m.SongLyric
	}
	return 0
}

func (m *KTVCopyright) GetRecordingval() int32 {
	if m != nil {
		return m.Recordingval
	}
	return 0
}

func (m *KTVCopyright) GetChannel() int32 {
	if m != nil {
		return m.Channel
	}
	return 0
}

type KTVSong struct {
	SongId               string        `protobuf:"bytes,1,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	SongName             string        `protobuf:"bytes,2,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	SingerName           string        `protobuf:"bytes,3,opt,name=singer_name,json=singerName,proto3" json:"singer_name,omitempty"`
	AlbumName            string        `protobuf:"bytes,4,opt,name=album_name,json=albumName,proto3" json:"album_name,omitempty"`
	AlbumImg             string        `protobuf:"bytes,5,opt,name=album_img,json=albumImg,proto3" json:"album_img,omitempty"`
	Duration             int32         `protobuf:"varint,6,opt,name=duration,proto3" json:"duration,omitempty"`
	IsAdd                bool          `protobuf:"varint,7,opt,name=is_add,json=isAdd,proto3" json:"is_add,omitempty"`
	Copyright            *KTVCopyright `protobuf:"bytes,8,opt,name=copyright,proto3" json:"copyright,omitempty"`
	SegmentBegin         uint32        `protobuf:"varint,9,opt,name=segment_begin,json=segmentBegin,proto3" json:"segment_begin,omitempty"`
	SegmentEnd           uint32        `protobuf:"varint,10,opt,name=segment_end,json=segmentEnd,proto3" json:"segment_end,omitempty"`
	IsClimax             bool          `protobuf:"varint,11,opt,name=is_climax,json=isClimax,proto3" json:"is_climax,omitempty"`
	VendorId             int32         `protobuf:"varint,12,opt,name=vendor_id,json=vendorId,proto3" json:"vendor_id,omitempty"`
	PitchAbility         int32         `protobuf:"varint,13,opt,name=pitch_ability,json=pitchAbility,proto3" json:"pitch_ability,omitempty"`
	MetaId               string        `protobuf:"bytes,99,opt,name=meta_id,json=metaId,proto3" json:"meta_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *KTVSong) Reset()         { *m = KTVSong{} }
func (m *KTVSong) String() string { return proto.CompactTextString(m) }
func (*KTVSong) ProtoMessage()    {}
func (*KTVSong) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{1}
}
func (m *KTVSong) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KTVSong.Unmarshal(m, b)
}
func (m *KTVSong) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KTVSong.Marshal(b, m, deterministic)
}
func (dst *KTVSong) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KTVSong.Merge(dst, src)
}
func (m *KTVSong) XXX_Size() int {
	return xxx_messageInfo_KTVSong.Size(m)
}
func (m *KTVSong) XXX_DiscardUnknown() {
	xxx_messageInfo_KTVSong.DiscardUnknown(m)
}

var xxx_messageInfo_KTVSong proto.InternalMessageInfo

func (m *KTVSong) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *KTVSong) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

func (m *KTVSong) GetSingerName() string {
	if m != nil {
		return m.SingerName
	}
	return ""
}

func (m *KTVSong) GetAlbumName() string {
	if m != nil {
		return m.AlbumName
	}
	return ""
}

func (m *KTVSong) GetAlbumImg() string {
	if m != nil {
		return m.AlbumImg
	}
	return ""
}

func (m *KTVSong) GetDuration() int32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

func (m *KTVSong) GetIsAdd() bool {
	if m != nil {
		return m.IsAdd
	}
	return false
}

func (m *KTVSong) GetCopyright() *KTVCopyright {
	if m != nil {
		return m.Copyright
	}
	return nil
}

func (m *KTVSong) GetSegmentBegin() uint32 {
	if m != nil {
		return m.SegmentBegin
	}
	return 0
}

func (m *KTVSong) GetSegmentEnd() uint32 {
	if m != nil {
		return m.SegmentEnd
	}
	return 0
}

func (m *KTVSong) GetIsClimax() bool {
	if m != nil {
		return m.IsClimax
	}
	return false
}

func (m *KTVSong) GetVendorId() int32 {
	if m != nil {
		return m.VendorId
	}
	return 0
}

func (m *KTVSong) GetPitchAbility() int32 {
	if m != nil {
		return m.PitchAbility
	}
	return 0
}

func (m *KTVSong) GetMetaId() string {
	if m != nil {
		return m.MetaId
	}
	return ""
}

type KTVUser struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Username             string   `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Sex                  int32    `protobuf:"varint,4,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KTVUser) Reset()         { *m = KTVUser{} }
func (m *KTVUser) String() string { return proto.CompactTextString(m) }
func (*KTVUser) ProtoMessage()    {}
func (*KTVUser) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{2}
}
func (m *KTVUser) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KTVUser.Unmarshal(m, b)
}
func (m *KTVUser) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KTVUser.Marshal(b, m, deterministic)
}
func (dst *KTVUser) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KTVUser.Merge(dst, src)
}
func (m *KTVUser) XXX_Size() int {
	return xxx_messageInfo_KTVUser.Size(m)
}
func (m *KTVUser) XXX_DiscardUnknown() {
	xxx_messageInfo_KTVUser.DiscardUnknown(m)
}

var xxx_messageInfo_KTVUser proto.InternalMessageInfo

func (m *KTVUser) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *KTVUser) GetUsername() string {
	if m != nil {
		return m.Username
	}
	return ""
}

func (m *KTVUser) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *KTVUser) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

type KTVRecordSinger struct {
	User                 *KTVUser `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	ScoreTutti           uint32   `protobuf:"varint,2,opt,name=score_tutti,json=scoreTutti,proto3" json:"score_tutti,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KTVRecordSinger) Reset()         { *m = KTVRecordSinger{} }
func (m *KTVRecordSinger) String() string { return proto.CompactTextString(m) }
func (*KTVRecordSinger) ProtoMessage()    {}
func (*KTVRecordSinger) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{3}
}
func (m *KTVRecordSinger) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KTVRecordSinger.Unmarshal(m, b)
}
func (m *KTVRecordSinger) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KTVRecordSinger.Marshal(b, m, deterministic)
}
func (dst *KTVRecordSinger) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KTVRecordSinger.Merge(dst, src)
}
func (m *KTVRecordSinger) XXX_Size() int {
	return xxx_messageInfo_KTVRecordSinger.Size(m)
}
func (m *KTVRecordSinger) XXX_DiscardUnknown() {
	xxx_messageInfo_KTVRecordSinger.DiscardUnknown(m)
}

var xxx_messageInfo_KTVRecordSinger proto.InternalMessageInfo

func (m *KTVRecordSinger) GetUser() *KTVUser {
	if m != nil {
		return m.User
	}
	return nil
}

func (m *KTVRecordSinger) GetScoreTutti() uint32 {
	if m != nil {
		return m.ScoreTutti
	}
	return 0
}

type KTVRecord struct {
	Song                 *KTVSong           `protobuf:"bytes,1,opt,name=song,proto3" json:"song,omitempty"`
	ScoreSolo            uint32             `protobuf:"varint,2,opt,name=score_solo,json=scoreSolo,proto3" json:"score_solo,omitempty"`
	ScoreTotalTutti      uint32             `protobuf:"varint,4,opt,name=score_total_tutti,json=scoreTotalTutti,proto3" json:"score_total_tutti,omitempty"`
	SingerList           []*KTVRecordSinger `protobuf:"bytes,3,rep,name=singer_list,json=singerList,proto3" json:"singer_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *KTVRecord) Reset()         { *m = KTVRecord{} }
func (m *KTVRecord) String() string { return proto.CompactTextString(m) }
func (*KTVRecord) ProtoMessage()    {}
func (*KTVRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{4}
}
func (m *KTVRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KTVRecord.Unmarshal(m, b)
}
func (m *KTVRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KTVRecord.Marshal(b, m, deterministic)
}
func (dst *KTVRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KTVRecord.Merge(dst, src)
}
func (m *KTVRecord) XXX_Size() int {
	return xxx_messageInfo_KTVRecord.Size(m)
}
func (m *KTVRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_KTVRecord.DiscardUnknown(m)
}

var xxx_messageInfo_KTVRecord proto.InternalMessageInfo

func (m *KTVRecord) GetSong() *KTVSong {
	if m != nil {
		return m.Song
	}
	return nil
}

func (m *KTVRecord) GetScoreSolo() uint32 {
	if m != nil {
		return m.ScoreSolo
	}
	return 0
}

func (m *KTVRecord) GetScoreTotalTutti() uint32 {
	if m != nil {
		return m.ScoreTotalTutti
	}
	return 0
}

func (m *KTVRecord) GetSingerList() []*KTVRecordSinger {
	if m != nil {
		return m.SingerList
	}
	return nil
}

type KTVOrder struct {
	IndexId              int64     `protobuf:"varint,1,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	Song                 *KTVSong  `protobuf:"bytes,2,opt,name=song,proto3" json:"song,omitempty"`
	User                 *KTVUser  `protobuf:"bytes,3,opt,name=user,proto3" json:"user,omitempty"`
	Glory                *KTVGlory `protobuf:"bytes,4,opt,name=glory,proto3" json:"glory,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *KTVOrder) Reset()         { *m = KTVOrder{} }
func (m *KTVOrder) String() string { return proto.CompactTextString(m) }
func (*KTVOrder) ProtoMessage()    {}
func (*KTVOrder) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{5}
}
func (m *KTVOrder) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KTVOrder.Unmarshal(m, b)
}
func (m *KTVOrder) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KTVOrder.Marshal(b, m, deterministic)
}
func (dst *KTVOrder) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KTVOrder.Merge(dst, src)
}
func (m *KTVOrder) XXX_Size() int {
	return xxx_messageInfo_KTVOrder.Size(m)
}
func (m *KTVOrder) XXX_DiscardUnknown() {
	xxx_messageInfo_KTVOrder.DiscardUnknown(m)
}

var xxx_messageInfo_KTVOrder proto.InternalMessageInfo

func (m *KTVOrder) GetIndexId() int64 {
	if m != nil {
		return m.IndexId
	}
	return 0
}

func (m *KTVOrder) GetSong() *KTVSong {
	if m != nil {
		return m.Song
	}
	return nil
}

func (m *KTVOrder) GetUser() *KTVUser {
	if m != nil {
		return m.User
	}
	return nil
}

func (m *KTVOrder) GetGlory() *KTVGlory {
	if m != nil {
		return m.Glory
	}
	return nil
}

type KTVGlory struct {
	GloryName            string   `protobuf:"bytes,1,opt,name=glory_name,json=gloryName,proto3" json:"glory_name,omitempty"`
	GloryImg             string   `protobuf:"bytes,2,opt,name=glory_img,json=gloryImg,proto3" json:"glory_img,omitempty"`
	GloryColor           string   `protobuf:"bytes,3,opt,name=glory_color,json=gloryColor,proto3" json:"glory_color,omitempty"`
	GloryRank            uint32   `protobuf:"varint,4,opt,name=glory_rank,json=gloryRank,proto3" json:"glory_rank,omitempty"`
	PlayColor            string   `protobuf:"bytes,5,opt,name=play_color,json=playColor,proto3" json:"play_color,omitempty"`
	BannerImg            string   `protobuf:"bytes,6,opt,name=banner_img,json=bannerImg,proto3" json:"banner_img,omitempty"`
	GloryBgImg           string   `protobuf:"bytes,7,opt,name=glory_bg_img,json=gloryBgImg,proto3" json:"glory_bg_img,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KTVGlory) Reset()         { *m = KTVGlory{} }
func (m *KTVGlory) String() string { return proto.CompactTextString(m) }
func (*KTVGlory) ProtoMessage()    {}
func (*KTVGlory) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{6}
}
func (m *KTVGlory) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KTVGlory.Unmarshal(m, b)
}
func (m *KTVGlory) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KTVGlory.Marshal(b, m, deterministic)
}
func (dst *KTVGlory) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KTVGlory.Merge(dst, src)
}
func (m *KTVGlory) XXX_Size() int {
	return xxx_messageInfo_KTVGlory.Size(m)
}
func (m *KTVGlory) XXX_DiscardUnknown() {
	xxx_messageInfo_KTVGlory.DiscardUnknown(m)
}

var xxx_messageInfo_KTVGlory proto.InternalMessageInfo

func (m *KTVGlory) GetGloryName() string {
	if m != nil {
		return m.GloryName
	}
	return ""
}

func (m *KTVGlory) GetGloryImg() string {
	if m != nil {
		return m.GloryImg
	}
	return ""
}

func (m *KTVGlory) GetGloryColor() string {
	if m != nil {
		return m.GloryColor
	}
	return ""
}

func (m *KTVGlory) GetGloryRank() uint32 {
	if m != nil {
		return m.GloryRank
	}
	return 0
}

func (m *KTVGlory) GetPlayColor() string {
	if m != nil {
		return m.PlayColor
	}
	return ""
}

func (m *KTVGlory) GetBannerImg() string {
	if m != nil {
		return m.BannerImg
	}
	return ""
}

func (m *KTVGlory) GetGloryBgImg() string {
	if m != nil {
		return m.GloryBgImg
	}
	return ""
}

type GetChannelKTVSongListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Page                 uint32   `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	Size                 uint32   `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	ChannelId            uint32   `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ClientVersion        uint32   `protobuf:"varint,100,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	DeviceId             string   `protobuf:"bytes,101,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	ClientType           uint32   `protobuf:"varint,102,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelKTVSongListReq) Reset()         { *m = GetChannelKTVSongListReq{} }
func (m *GetChannelKTVSongListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelKTVSongListReq) ProtoMessage()    {}
func (*GetChannelKTVSongListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{7}
}
func (m *GetChannelKTVSongListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelKTVSongListReq.Unmarshal(m, b)
}
func (m *GetChannelKTVSongListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelKTVSongListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelKTVSongListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelKTVSongListReq.Merge(dst, src)
}
func (m *GetChannelKTVSongListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelKTVSongListReq.Size(m)
}
func (m *GetChannelKTVSongListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelKTVSongListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelKTVSongListReq proto.InternalMessageInfo

func (m *GetChannelKTVSongListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetChannelKTVSongListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetChannelKTVSongListReq) GetSize() uint32 {
	if m != nil {
		return m.Size
	}
	return 0
}

func (m *GetChannelKTVSongListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelKTVSongListReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *GetChannelKTVSongListReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *GetChannelKTVSongListReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

type GetChannelKTVSongListResp struct {
	SongList             []*KTVSong `protobuf:"bytes,1,rep,name=song_list,json=songList,proto3" json:"song_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetChannelKTVSongListResp) Reset()         { *m = GetChannelKTVSongListResp{} }
func (m *GetChannelKTVSongListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelKTVSongListResp) ProtoMessage()    {}
func (*GetChannelKTVSongListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{8}
}
func (m *GetChannelKTVSongListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelKTVSongListResp.Unmarshal(m, b)
}
func (m *GetChannelKTVSongListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelKTVSongListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelKTVSongListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelKTVSongListResp.Merge(dst, src)
}
func (m *GetChannelKTVSongListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelKTVSongListResp.Size(m)
}
func (m *GetChannelKTVSongListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelKTVSongListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelKTVSongListResp proto.InternalMessageInfo

func (m *GetChannelKTVSongListResp) GetSongList() []*KTVSong {
	if m != nil {
		return m.SongList
	}
	return nil
}

type GetChannelKTVHistoryListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Page                 uint32   `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	Size                 uint32   `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	ChannelId            uint32   `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ClientVersion        uint32   `protobuf:"varint,100,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	DeviceId             string   `protobuf:"bytes,101,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	ClientType           uint32   `protobuf:"varint,102,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelKTVHistoryListReq) Reset()         { *m = GetChannelKTVHistoryListReq{} }
func (m *GetChannelKTVHistoryListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelKTVHistoryListReq) ProtoMessage()    {}
func (*GetChannelKTVHistoryListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{9}
}
func (m *GetChannelKTVHistoryListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelKTVHistoryListReq.Unmarshal(m, b)
}
func (m *GetChannelKTVHistoryListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelKTVHistoryListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelKTVHistoryListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelKTVHistoryListReq.Merge(dst, src)
}
func (m *GetChannelKTVHistoryListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelKTVHistoryListReq.Size(m)
}
func (m *GetChannelKTVHistoryListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelKTVHistoryListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelKTVHistoryListReq proto.InternalMessageInfo

func (m *GetChannelKTVHistoryListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetChannelKTVHistoryListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetChannelKTVHistoryListReq) GetSize() uint32 {
	if m != nil {
		return m.Size
	}
	return 0
}

func (m *GetChannelKTVHistoryListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelKTVHistoryListReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *GetChannelKTVHistoryListReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *GetChannelKTVHistoryListReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

type GetChannelKTVHistoryListResp struct {
	RecordList           []*KTVRecord `protobuf:"bytes,1,rep,name=record_list,json=recordList,proto3" json:"record_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelKTVHistoryListResp) Reset()         { *m = GetChannelKTVHistoryListResp{} }
func (m *GetChannelKTVHistoryListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelKTVHistoryListResp) ProtoMessage()    {}
func (*GetChannelKTVHistoryListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{10}
}
func (m *GetChannelKTVHistoryListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelKTVHistoryListResp.Unmarshal(m, b)
}
func (m *GetChannelKTVHistoryListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelKTVHistoryListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelKTVHistoryListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelKTVHistoryListResp.Merge(dst, src)
}
func (m *GetChannelKTVHistoryListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelKTVHistoryListResp.Size(m)
}
func (m *GetChannelKTVHistoryListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelKTVHistoryListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelKTVHistoryListResp proto.InternalMessageInfo

func (m *GetChannelKTVHistoryListResp) GetRecordList() []*KTVRecord {
	if m != nil {
		return m.RecordList
	}
	return nil
}

type GetChannelKTVRecommendListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Page                 uint32   `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	Size                 uint32   `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	ChannelId            uint32   `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SongListTypeId       int64    `protobuf:"varint,5,opt,name=song_list_type_id,json=songListTypeId,proto3" json:"song_list_type_id,omitempty"`
	ClientVersion        uint32   `protobuf:"varint,100,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	DeviceId             string   `protobuf:"bytes,101,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	ClientType           uint32   `protobuf:"varint,102,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelKTVRecommendListReq) Reset()         { *m = GetChannelKTVRecommendListReq{} }
func (m *GetChannelKTVRecommendListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelKTVRecommendListReq) ProtoMessage()    {}
func (*GetChannelKTVRecommendListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{11}
}
func (m *GetChannelKTVRecommendListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelKTVRecommendListReq.Unmarshal(m, b)
}
func (m *GetChannelKTVRecommendListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelKTVRecommendListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelKTVRecommendListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelKTVRecommendListReq.Merge(dst, src)
}
func (m *GetChannelKTVRecommendListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelKTVRecommendListReq.Size(m)
}
func (m *GetChannelKTVRecommendListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelKTVRecommendListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelKTVRecommendListReq proto.InternalMessageInfo

func (m *GetChannelKTVRecommendListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetChannelKTVRecommendListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetChannelKTVRecommendListReq) GetSize() uint32 {
	if m != nil {
		return m.Size
	}
	return 0
}

func (m *GetChannelKTVRecommendListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelKTVRecommendListReq) GetSongListTypeId() int64 {
	if m != nil {
		return m.SongListTypeId
	}
	return 0
}

func (m *GetChannelKTVRecommendListReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *GetChannelKTVRecommendListReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *GetChannelKTVRecommendListReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

type GetChannelKTVRecommendListResp struct {
	SongList             []*KTVSong `protobuf:"bytes,1,rep,name=song_list,json=songList,proto3" json:"song_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetChannelKTVRecommendListResp) Reset()         { *m = GetChannelKTVRecommendListResp{} }
func (m *GetChannelKTVRecommendListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelKTVRecommendListResp) ProtoMessage()    {}
func (*GetChannelKTVRecommendListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{12}
}
func (m *GetChannelKTVRecommendListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelKTVRecommendListResp.Unmarshal(m, b)
}
func (m *GetChannelKTVRecommendListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelKTVRecommendListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelKTVRecommendListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelKTVRecommendListResp.Merge(dst, src)
}
func (m *GetChannelKTVRecommendListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelKTVRecommendListResp.Size(m)
}
func (m *GetChannelKTVRecommendListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelKTVRecommendListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelKTVRecommendListResp proto.InternalMessageInfo

func (m *GetChannelKTVRecommendListResp) GetSongList() []*KTVSong {
	if m != nil {
		return m.SongList
	}
	return nil
}

type GetChannelKTVPlayListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Page                 uint32   `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	Size                 uint32   `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	ChannelId            uint32   `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelKTVPlayListReq) Reset()         { *m = GetChannelKTVPlayListReq{} }
func (m *GetChannelKTVPlayListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelKTVPlayListReq) ProtoMessage()    {}
func (*GetChannelKTVPlayListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{13}
}
func (m *GetChannelKTVPlayListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelKTVPlayListReq.Unmarshal(m, b)
}
func (m *GetChannelKTVPlayListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelKTVPlayListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelKTVPlayListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelKTVPlayListReq.Merge(dst, src)
}
func (m *GetChannelKTVPlayListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelKTVPlayListReq.Size(m)
}
func (m *GetChannelKTVPlayListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelKTVPlayListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelKTVPlayListReq proto.InternalMessageInfo

func (m *GetChannelKTVPlayListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetChannelKTVPlayListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetChannelKTVPlayListReq) GetSize() uint32 {
	if m != nil {
		return m.Size
	}
	return 0
}

func (m *GetChannelKTVPlayListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelKTVPlayListResp struct {
	OrderList            []*KTVOrder `protobuf:"bytes,1,rep,name=order_list,json=orderList,proto3" json:"order_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetChannelKTVPlayListResp) Reset()         { *m = GetChannelKTVPlayListResp{} }
func (m *GetChannelKTVPlayListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelKTVPlayListResp) ProtoMessage()    {}
func (*GetChannelKTVPlayListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{14}
}
func (m *GetChannelKTVPlayListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelKTVPlayListResp.Unmarshal(m, b)
}
func (m *GetChannelKTVPlayListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelKTVPlayListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelKTVPlayListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelKTVPlayListResp.Merge(dst, src)
}
func (m *GetChannelKTVPlayListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelKTVPlayListResp.Size(m)
}
func (m *GetChannelKTVPlayListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelKTVPlayListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelKTVPlayListResp proto.InternalMessageInfo

func (m *GetChannelKTVPlayListResp) GetOrderList() []*KTVOrder {
	if m != nil {
		return m.OrderList
	}
	return nil
}

type GetChannelKTVGuessLikeSongListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ClientVersion        uint32   `protobuf:"varint,100,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	DeviceId             string   `protobuf:"bytes,101,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	ClientType           uint32   `protobuf:"varint,102,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelKTVGuessLikeSongListReq) Reset()         { *m = GetChannelKTVGuessLikeSongListReq{} }
func (m *GetChannelKTVGuessLikeSongListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelKTVGuessLikeSongListReq) ProtoMessage()    {}
func (*GetChannelKTVGuessLikeSongListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{15}
}
func (m *GetChannelKTVGuessLikeSongListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelKTVGuessLikeSongListReq.Unmarshal(m, b)
}
func (m *GetChannelKTVGuessLikeSongListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelKTVGuessLikeSongListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelKTVGuessLikeSongListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelKTVGuessLikeSongListReq.Merge(dst, src)
}
func (m *GetChannelKTVGuessLikeSongListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelKTVGuessLikeSongListReq.Size(m)
}
func (m *GetChannelKTVGuessLikeSongListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelKTVGuessLikeSongListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelKTVGuessLikeSongListReq proto.InternalMessageInfo

func (m *GetChannelKTVGuessLikeSongListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetChannelKTVGuessLikeSongListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelKTVGuessLikeSongListReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *GetChannelKTVGuessLikeSongListReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *GetChannelKTVGuessLikeSongListReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

type GetChannelKTVGuessLikeSongListResp struct {
	SongList             []*KTVSong `protobuf:"bytes,1,rep,name=song_list,json=songList,proto3" json:"song_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetChannelKTVGuessLikeSongListResp) Reset()         { *m = GetChannelKTVGuessLikeSongListResp{} }
func (m *GetChannelKTVGuessLikeSongListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelKTVGuessLikeSongListResp) ProtoMessage()    {}
func (*GetChannelKTVGuessLikeSongListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{16}
}
func (m *GetChannelKTVGuessLikeSongListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelKTVGuessLikeSongListResp.Unmarshal(m, b)
}
func (m *GetChannelKTVGuessLikeSongListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelKTVGuessLikeSongListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelKTVGuessLikeSongListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelKTVGuessLikeSongListResp.Merge(dst, src)
}
func (m *GetChannelKTVGuessLikeSongListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelKTVGuessLikeSongListResp.Size(m)
}
func (m *GetChannelKTVGuessLikeSongListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelKTVGuessLikeSongListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelKTVGuessLikeSongListResp proto.InternalMessageInfo

func (m *GetChannelKTVGuessLikeSongListResp) GetSongList() []*KTVSong {
	if m != nil {
		return m.SongList
	}
	return nil
}

type AddChannelKTVSongToPlayListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Song                 *KTVSong `protobuf:"bytes,2,opt,name=song,proto3" json:"song,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ShareToken           string   `protobuf:"bytes,4,opt,name=share_token,json=shareToken,proto3" json:"share_token,omitempty"`
	SongListId           uint32   `protobuf:"varint,5,opt,name=song_list_id,json=songListId,proto3" json:"song_list_id,omitempty"`
	ShareTokenList       []string `protobuf:"bytes,6,rep,name=share_token_list,json=shareTokenList,proto3" json:"share_token_list,omitempty"`
	IsClimax             bool     `protobuf:"varint,7,opt,name=is_climax,json=isClimax,proto3" json:"is_climax,omitempty"`
	ClientVersion        uint32   `protobuf:"varint,100,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	DeviceId             string   `protobuf:"bytes,101,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	ClientType           uint32   `protobuf:"varint,102,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddChannelKTVSongToPlayListReq) Reset()         { *m = AddChannelKTVSongToPlayListReq{} }
func (m *AddChannelKTVSongToPlayListReq) String() string { return proto.CompactTextString(m) }
func (*AddChannelKTVSongToPlayListReq) ProtoMessage()    {}
func (*AddChannelKTVSongToPlayListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{17}
}
func (m *AddChannelKTVSongToPlayListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddChannelKTVSongToPlayListReq.Unmarshal(m, b)
}
func (m *AddChannelKTVSongToPlayListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddChannelKTVSongToPlayListReq.Marshal(b, m, deterministic)
}
func (dst *AddChannelKTVSongToPlayListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddChannelKTVSongToPlayListReq.Merge(dst, src)
}
func (m *AddChannelKTVSongToPlayListReq) XXX_Size() int {
	return xxx_messageInfo_AddChannelKTVSongToPlayListReq.Size(m)
}
func (m *AddChannelKTVSongToPlayListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddChannelKTVSongToPlayListReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddChannelKTVSongToPlayListReq proto.InternalMessageInfo

func (m *AddChannelKTVSongToPlayListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddChannelKTVSongToPlayListReq) GetSong() *KTVSong {
	if m != nil {
		return m.Song
	}
	return nil
}

func (m *AddChannelKTVSongToPlayListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AddChannelKTVSongToPlayListReq) GetShareToken() string {
	if m != nil {
		return m.ShareToken
	}
	return ""
}

func (m *AddChannelKTVSongToPlayListReq) GetSongListId() uint32 {
	if m != nil {
		return m.SongListId
	}
	return 0
}

func (m *AddChannelKTVSongToPlayListReq) GetShareTokenList() []string {
	if m != nil {
		return m.ShareTokenList
	}
	return nil
}

func (m *AddChannelKTVSongToPlayListReq) GetIsClimax() bool {
	if m != nil {
		return m.IsClimax
	}
	return false
}

func (m *AddChannelKTVSongToPlayListReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *AddChannelKTVSongToPlayListReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *AddChannelKTVSongToPlayListReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

type AddChannelKTVSongToPlayListResp struct {
	Info                 *ChannelKTVUpdate `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	Song                 *KTVOrder         `protobuf:"bytes,2,opt,name=song,proto3" json:"song,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *AddChannelKTVSongToPlayListResp) Reset()         { *m = AddChannelKTVSongToPlayListResp{} }
func (m *AddChannelKTVSongToPlayListResp) String() string { return proto.CompactTextString(m) }
func (*AddChannelKTVSongToPlayListResp) ProtoMessage()    {}
func (*AddChannelKTVSongToPlayListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{18}
}
func (m *AddChannelKTVSongToPlayListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddChannelKTVSongToPlayListResp.Unmarshal(m, b)
}
func (m *AddChannelKTVSongToPlayListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddChannelKTVSongToPlayListResp.Marshal(b, m, deterministic)
}
func (dst *AddChannelKTVSongToPlayListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddChannelKTVSongToPlayListResp.Merge(dst, src)
}
func (m *AddChannelKTVSongToPlayListResp) XXX_Size() int {
	return xxx_messageInfo_AddChannelKTVSongToPlayListResp.Size(m)
}
func (m *AddChannelKTVSongToPlayListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddChannelKTVSongToPlayListResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddChannelKTVSongToPlayListResp proto.InternalMessageInfo

func (m *AddChannelKTVSongToPlayListResp) GetInfo() *ChannelKTVUpdate {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *AddChannelKTVSongToPlayListResp) GetSong() *KTVOrder {
	if m != nil {
		return m.Song
	}
	return nil
}

type MoveUpChannelKTVSongReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	IndexId              int64    `protobuf:"varint,2,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MoveUpChannelKTVSongReq) Reset()         { *m = MoveUpChannelKTVSongReq{} }
func (m *MoveUpChannelKTVSongReq) String() string { return proto.CompactTextString(m) }
func (*MoveUpChannelKTVSongReq) ProtoMessage()    {}
func (*MoveUpChannelKTVSongReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{19}
}
func (m *MoveUpChannelKTVSongReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MoveUpChannelKTVSongReq.Unmarshal(m, b)
}
func (m *MoveUpChannelKTVSongReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MoveUpChannelKTVSongReq.Marshal(b, m, deterministic)
}
func (dst *MoveUpChannelKTVSongReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MoveUpChannelKTVSongReq.Merge(dst, src)
}
func (m *MoveUpChannelKTVSongReq) XXX_Size() int {
	return xxx_messageInfo_MoveUpChannelKTVSongReq.Size(m)
}
func (m *MoveUpChannelKTVSongReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MoveUpChannelKTVSongReq.DiscardUnknown(m)
}

var xxx_messageInfo_MoveUpChannelKTVSongReq proto.InternalMessageInfo

func (m *MoveUpChannelKTVSongReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MoveUpChannelKTVSongReq) GetIndexId() int64 {
	if m != nil {
		return m.IndexId
	}
	return 0
}

func (m *MoveUpChannelKTVSongReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type MoveUpChannelKTVSongResp struct {
	Info                 *ChannelKTVUpdate `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *MoveUpChannelKTVSongResp) Reset()         { *m = MoveUpChannelKTVSongResp{} }
func (m *MoveUpChannelKTVSongResp) String() string { return proto.CompactTextString(m) }
func (*MoveUpChannelKTVSongResp) ProtoMessage()    {}
func (*MoveUpChannelKTVSongResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{20}
}
func (m *MoveUpChannelKTVSongResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MoveUpChannelKTVSongResp.Unmarshal(m, b)
}
func (m *MoveUpChannelKTVSongResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MoveUpChannelKTVSongResp.Marshal(b, m, deterministic)
}
func (dst *MoveUpChannelKTVSongResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MoveUpChannelKTVSongResp.Merge(dst, src)
}
func (m *MoveUpChannelKTVSongResp) XXX_Size() int {
	return xxx_messageInfo_MoveUpChannelKTVSongResp.Size(m)
}
func (m *MoveUpChannelKTVSongResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MoveUpChannelKTVSongResp.DiscardUnknown(m)
}

var xxx_messageInfo_MoveUpChannelKTVSongResp proto.InternalMessageInfo

func (m *MoveUpChannelKTVSongResp) GetInfo() *ChannelKTVUpdate {
	if m != nil {
		return m.Info
	}
	return nil
}

type RemoveChannelKTVSongReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	IndexId              int64    `protobuf:"varint,2,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveChannelKTVSongReq) Reset()         { *m = RemoveChannelKTVSongReq{} }
func (m *RemoveChannelKTVSongReq) String() string { return proto.CompactTextString(m) }
func (*RemoveChannelKTVSongReq) ProtoMessage()    {}
func (*RemoveChannelKTVSongReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{21}
}
func (m *RemoveChannelKTVSongReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveChannelKTVSongReq.Unmarshal(m, b)
}
func (m *RemoveChannelKTVSongReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveChannelKTVSongReq.Marshal(b, m, deterministic)
}
func (dst *RemoveChannelKTVSongReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveChannelKTVSongReq.Merge(dst, src)
}
func (m *RemoveChannelKTVSongReq) XXX_Size() int {
	return xxx_messageInfo_RemoveChannelKTVSongReq.Size(m)
}
func (m *RemoveChannelKTVSongReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveChannelKTVSongReq.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveChannelKTVSongReq proto.InternalMessageInfo

func (m *RemoveChannelKTVSongReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RemoveChannelKTVSongReq) GetIndexId() int64 {
	if m != nil {
		return m.IndexId
	}
	return 0
}

func (m *RemoveChannelKTVSongReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type RemoveChannelKTVSongResp struct {
	Info                 *ChannelKTVUpdate `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *RemoveChannelKTVSongResp) Reset()         { *m = RemoveChannelKTVSongResp{} }
func (m *RemoveChannelKTVSongResp) String() string { return proto.CompactTextString(m) }
func (*RemoveChannelKTVSongResp) ProtoMessage()    {}
func (*RemoveChannelKTVSongResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{22}
}
func (m *RemoveChannelKTVSongResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveChannelKTVSongResp.Unmarshal(m, b)
}
func (m *RemoveChannelKTVSongResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveChannelKTVSongResp.Marshal(b, m, deterministic)
}
func (dst *RemoveChannelKTVSongResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveChannelKTVSongResp.Merge(dst, src)
}
func (m *RemoveChannelKTVSongResp) XXX_Size() int {
	return xxx_messageInfo_RemoveChannelKTVSongResp.Size(m)
}
func (m *RemoveChannelKTVSongResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveChannelKTVSongResp.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveChannelKTVSongResp proto.InternalMessageInfo

func (m *RemoveChannelKTVSongResp) GetInfo() *ChannelKTVUpdate {
	if m != nil {
		return m.Info
	}
	return nil
}

type BeginChannelKTVSingReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SongId               string   `protobuf:"bytes,2,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	DeviceId             string   `protobuf:"bytes,4,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BeginChannelKTVSingReq) Reset()         { *m = BeginChannelKTVSingReq{} }
func (m *BeginChannelKTVSingReq) String() string { return proto.CompactTextString(m) }
func (*BeginChannelKTVSingReq) ProtoMessage()    {}
func (*BeginChannelKTVSingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{23}
}
func (m *BeginChannelKTVSingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BeginChannelKTVSingReq.Unmarshal(m, b)
}
func (m *BeginChannelKTVSingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BeginChannelKTVSingReq.Marshal(b, m, deterministic)
}
func (dst *BeginChannelKTVSingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BeginChannelKTVSingReq.Merge(dst, src)
}
func (m *BeginChannelKTVSingReq) XXX_Size() int {
	return xxx_messageInfo_BeginChannelKTVSingReq.Size(m)
}
func (m *BeginChannelKTVSingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BeginChannelKTVSingReq.DiscardUnknown(m)
}

var xxx_messageInfo_BeginChannelKTVSingReq proto.InternalMessageInfo

func (m *BeginChannelKTVSingReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BeginChannelKTVSingReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *BeginChannelKTVSingReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BeginChannelKTVSingReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

type BeginChannelKTVSingResp struct {
	Info                 *ChannelKTVUpdate `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BeginChannelKTVSingResp) Reset()         { *m = BeginChannelKTVSingResp{} }
func (m *BeginChannelKTVSingResp) String() string { return proto.CompactTextString(m) }
func (*BeginChannelKTVSingResp) ProtoMessage()    {}
func (*BeginChannelKTVSingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{24}
}
func (m *BeginChannelKTVSingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BeginChannelKTVSingResp.Unmarshal(m, b)
}
func (m *BeginChannelKTVSingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BeginChannelKTVSingResp.Marshal(b, m, deterministic)
}
func (dst *BeginChannelKTVSingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BeginChannelKTVSingResp.Merge(dst, src)
}
func (m *BeginChannelKTVSingResp) XXX_Size() int {
	return xxx_messageInfo_BeginChannelKTVSingResp.Size(m)
}
func (m *BeginChannelKTVSingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BeginChannelKTVSingResp.DiscardUnknown(m)
}

var xxx_messageInfo_BeginChannelKTVSingResp proto.InternalMessageInfo

func (m *BeginChannelKTVSingResp) GetInfo() *ChannelKTVUpdate {
	if m != nil {
		return m.Info
	}
	return nil
}

type GetChannelKTVInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ClientType           uint32   `protobuf:"varint,3,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	ClientVersion        uint32   `protobuf:"varint,4,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	DeviceId             string   `protobuf:"bytes,5,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelKTVInfoReq) Reset()         { *m = GetChannelKTVInfoReq{} }
func (m *GetChannelKTVInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelKTVInfoReq) ProtoMessage()    {}
func (*GetChannelKTVInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{25}
}
func (m *GetChannelKTVInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelKTVInfoReq.Unmarshal(m, b)
}
func (m *GetChannelKTVInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelKTVInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelKTVInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelKTVInfoReq.Merge(dst, src)
}
func (m *GetChannelKTVInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelKTVInfoReq.Size(m)
}
func (m *GetChannelKTVInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelKTVInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelKTVInfoReq proto.InternalMessageInfo

func (m *GetChannelKTVInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetChannelKTVInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelKTVInfoReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *GetChannelKTVInfoReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *GetChannelKTVInfoReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

type GetChannelKTVInfoResp struct {
	Info                 *ChannelKTVUpdate `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	InteractionShowAll   bool              `protobuf:"varint,2,opt,name=interaction_show_all,json=interactionShowAll,proto3" json:"interaction_show_all,omitempty"`
	UserRemainLightNum   uint32            `protobuf:"varint,3,opt,name=user_remain_light_num,json=userRemainLightNum,proto3" json:"user_remain_light_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetChannelKTVInfoResp) Reset()         { *m = GetChannelKTVInfoResp{} }
func (m *GetChannelKTVInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelKTVInfoResp) ProtoMessage()    {}
func (*GetChannelKTVInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{26}
}
func (m *GetChannelKTVInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelKTVInfoResp.Unmarshal(m, b)
}
func (m *GetChannelKTVInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelKTVInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelKTVInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelKTVInfoResp.Merge(dst, src)
}
func (m *GetChannelKTVInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelKTVInfoResp.Size(m)
}
func (m *GetChannelKTVInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelKTVInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelKTVInfoResp proto.InternalMessageInfo

func (m *GetChannelKTVInfoResp) GetInfo() *ChannelKTVUpdate {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *GetChannelKTVInfoResp) GetInteractionShowAll() bool {
	if m != nil {
		return m.InteractionShowAll
	}
	return false
}

func (m *GetChannelKTVInfoResp) GetUserRemainLightNum() uint32 {
	if m != nil {
		return m.UserRemainLightNum
	}
	return 0
}

type JoinChannelKTVSingReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SongId               string   `protobuf:"bytes,3,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	IsSilently           bool     `protobuf:"varint,4,opt,name=is_silently,json=isSilently,proto3" json:"is_silently,omitempty"`
	ClientVersion        uint32   `protobuf:"varint,100,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	DeviceId             string   `protobuf:"bytes,101,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	ClientType           uint32   `protobuf:"varint,102,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JoinChannelKTVSingReq) Reset()         { *m = JoinChannelKTVSingReq{} }
func (m *JoinChannelKTVSingReq) String() string { return proto.CompactTextString(m) }
func (*JoinChannelKTVSingReq) ProtoMessage()    {}
func (*JoinChannelKTVSingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{27}
}
func (m *JoinChannelKTVSingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinChannelKTVSingReq.Unmarshal(m, b)
}
func (m *JoinChannelKTVSingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinChannelKTVSingReq.Marshal(b, m, deterministic)
}
func (dst *JoinChannelKTVSingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinChannelKTVSingReq.Merge(dst, src)
}
func (m *JoinChannelKTVSingReq) XXX_Size() int {
	return xxx_messageInfo_JoinChannelKTVSingReq.Size(m)
}
func (m *JoinChannelKTVSingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinChannelKTVSingReq.DiscardUnknown(m)
}

var xxx_messageInfo_JoinChannelKTVSingReq proto.InternalMessageInfo

func (m *JoinChannelKTVSingReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *JoinChannelKTVSingReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *JoinChannelKTVSingReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *JoinChannelKTVSingReq) GetIsSilently() bool {
	if m != nil {
		return m.IsSilently
	}
	return false
}

func (m *JoinChannelKTVSingReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *JoinChannelKTVSingReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *JoinChannelKTVSingReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

type JoinChannelKTVSingResp struct {
	Info                 *ChannelKTVUpdate `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *JoinChannelKTVSingResp) Reset()         { *m = JoinChannelKTVSingResp{} }
func (m *JoinChannelKTVSingResp) String() string { return proto.CompactTextString(m) }
func (*JoinChannelKTVSingResp) ProtoMessage()    {}
func (*JoinChannelKTVSingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{28}
}
func (m *JoinChannelKTVSingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinChannelKTVSingResp.Unmarshal(m, b)
}
func (m *JoinChannelKTVSingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinChannelKTVSingResp.Marshal(b, m, deterministic)
}
func (dst *JoinChannelKTVSingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinChannelKTVSingResp.Merge(dst, src)
}
func (m *JoinChannelKTVSingResp) XXX_Size() int {
	return xxx_messageInfo_JoinChannelKTVSingResp.Size(m)
}
func (m *JoinChannelKTVSingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinChannelKTVSingResp.DiscardUnknown(m)
}

var xxx_messageInfo_JoinChannelKTVSingResp proto.InternalMessageInfo

func (m *JoinChannelKTVSingResp) GetInfo() *ChannelKTVUpdate {
	if m != nil {
		return m.Info
	}
	return nil
}

type QuitChannelKTVSingReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SongId               string   `protobuf:"bytes,3,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QuitChannelKTVSingReq) Reset()         { *m = QuitChannelKTVSingReq{} }
func (m *QuitChannelKTVSingReq) String() string { return proto.CompactTextString(m) }
func (*QuitChannelKTVSingReq) ProtoMessage()    {}
func (*QuitChannelKTVSingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{29}
}
func (m *QuitChannelKTVSingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuitChannelKTVSingReq.Unmarshal(m, b)
}
func (m *QuitChannelKTVSingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuitChannelKTVSingReq.Marshal(b, m, deterministic)
}
func (dst *QuitChannelKTVSingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuitChannelKTVSingReq.Merge(dst, src)
}
func (m *QuitChannelKTVSingReq) XXX_Size() int {
	return xxx_messageInfo_QuitChannelKTVSingReq.Size(m)
}
func (m *QuitChannelKTVSingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QuitChannelKTVSingReq.DiscardUnknown(m)
}

var xxx_messageInfo_QuitChannelKTVSingReq proto.InternalMessageInfo

func (m *QuitChannelKTVSingReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *QuitChannelKTVSingReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *QuitChannelKTVSingReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

type QuitChannelKTVSingResp struct {
	Info                 *ChannelKTVUpdate `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *QuitChannelKTVSingResp) Reset()         { *m = QuitChannelKTVSingResp{} }
func (m *QuitChannelKTVSingResp) String() string { return proto.CompactTextString(m) }
func (*QuitChannelKTVSingResp) ProtoMessage()    {}
func (*QuitChannelKTVSingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{30}
}
func (m *QuitChannelKTVSingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuitChannelKTVSingResp.Unmarshal(m, b)
}
func (m *QuitChannelKTVSingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuitChannelKTVSingResp.Marshal(b, m, deterministic)
}
func (dst *QuitChannelKTVSingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuitChannelKTVSingResp.Merge(dst, src)
}
func (m *QuitChannelKTVSingResp) XXX_Size() int {
	return xxx_messageInfo_QuitChannelKTVSingResp.Size(m)
}
func (m *QuitChannelKTVSingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_QuitChannelKTVSingResp.DiscardUnknown(m)
}

var xxx_messageInfo_QuitChannelKTVSingResp proto.InternalMessageInfo

func (m *QuitChannelKTVSingResp) GetInfo() *ChannelKTVUpdate {
	if m != nil {
		return m.Info
	}
	return nil
}

type UpdateChannelKTVScoreReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Score                uint32   `protobuf:"varint,3,opt,name=score,proto3" json:"score,omitempty"`
	SongId               string   `protobuf:"bytes,4,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	Prefect              uint32   `protobuf:"varint,5,opt,name=prefect,proto3" json:"prefect,omitempty"`
	LastLrc              uint32   `protobuf:"varint,6,opt,name=last_lrc,json=lastLrc,proto3" json:"last_lrc,omitempty"`
	IsOriginalSong       bool     `protobuf:"varint,7,opt,name=is_original_song,json=isOriginalSong,proto3" json:"is_original_song,omitempty"`
	MaxScore             uint32   `protobuf:"varint,8,opt,name=max_score,json=maxScore,proto3" json:"max_score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateChannelKTVScoreReq) Reset()         { *m = UpdateChannelKTVScoreReq{} }
func (m *UpdateChannelKTVScoreReq) String() string { return proto.CompactTextString(m) }
func (*UpdateChannelKTVScoreReq) ProtoMessage()    {}
func (*UpdateChannelKTVScoreReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{31}
}
func (m *UpdateChannelKTVScoreReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateChannelKTVScoreReq.Unmarshal(m, b)
}
func (m *UpdateChannelKTVScoreReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateChannelKTVScoreReq.Marshal(b, m, deterministic)
}
func (dst *UpdateChannelKTVScoreReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateChannelKTVScoreReq.Merge(dst, src)
}
func (m *UpdateChannelKTVScoreReq) XXX_Size() int {
	return xxx_messageInfo_UpdateChannelKTVScoreReq.Size(m)
}
func (m *UpdateChannelKTVScoreReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateChannelKTVScoreReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateChannelKTVScoreReq proto.InternalMessageInfo

func (m *UpdateChannelKTVScoreReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateChannelKTVScoreReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UpdateChannelKTVScoreReq) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *UpdateChannelKTVScoreReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *UpdateChannelKTVScoreReq) GetPrefect() uint32 {
	if m != nil {
		return m.Prefect
	}
	return 0
}

func (m *UpdateChannelKTVScoreReq) GetLastLrc() uint32 {
	if m != nil {
		return m.LastLrc
	}
	return 0
}

func (m *UpdateChannelKTVScoreReq) GetIsOriginalSong() bool {
	if m != nil {
		return m.IsOriginalSong
	}
	return false
}

func (m *UpdateChannelKTVScoreReq) GetMaxScore() uint32 {
	if m != nil {
		return m.MaxScore
	}
	return 0
}

type UpdateChannelKTVScoreResp struct {
	Interval             uint32   `protobuf:"varint,1,opt,name=interval,proto3" json:"interval,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateChannelKTVScoreResp) Reset()         { *m = UpdateChannelKTVScoreResp{} }
func (m *UpdateChannelKTVScoreResp) String() string { return proto.CompactTextString(m) }
func (*UpdateChannelKTVScoreResp) ProtoMessage()    {}
func (*UpdateChannelKTVScoreResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{32}
}
func (m *UpdateChannelKTVScoreResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateChannelKTVScoreResp.Unmarshal(m, b)
}
func (m *UpdateChannelKTVScoreResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateChannelKTVScoreResp.Marshal(b, m, deterministic)
}
func (dst *UpdateChannelKTVScoreResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateChannelKTVScoreResp.Merge(dst, src)
}
func (m *UpdateChannelKTVScoreResp) XXX_Size() int {
	return xxx_messageInfo_UpdateChannelKTVScoreResp.Size(m)
}
func (m *UpdateChannelKTVScoreResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateChannelKTVScoreResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateChannelKTVScoreResp proto.InternalMessageInfo

func (m *UpdateChannelKTVScoreResp) GetInterval() uint32 {
	if m != nil {
		return m.Interval
	}
	return 0
}

type SwitchChannelKTVBGReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Open                 bool     `protobuf:"varint,3,opt,name=open,proto3" json:"open,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SwitchChannelKTVBGReq) Reset()         { *m = SwitchChannelKTVBGReq{} }
func (m *SwitchChannelKTVBGReq) String() string { return proto.CompactTextString(m) }
func (*SwitchChannelKTVBGReq) ProtoMessage()    {}
func (*SwitchChannelKTVBGReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{33}
}
func (m *SwitchChannelKTVBGReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchChannelKTVBGReq.Unmarshal(m, b)
}
func (m *SwitchChannelKTVBGReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchChannelKTVBGReq.Marshal(b, m, deterministic)
}
func (dst *SwitchChannelKTVBGReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchChannelKTVBGReq.Merge(dst, src)
}
func (m *SwitchChannelKTVBGReq) XXX_Size() int {
	return xxx_messageInfo_SwitchChannelKTVBGReq.Size(m)
}
func (m *SwitchChannelKTVBGReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchChannelKTVBGReq.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchChannelKTVBGReq proto.InternalMessageInfo

func (m *SwitchChannelKTVBGReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SwitchChannelKTVBGReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SwitchChannelKTVBGReq) GetOpen() bool {
	if m != nil {
		return m.Open
	}
	return false
}

type SwitchChannelKTVBGResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SwitchChannelKTVBGResp) Reset()         { *m = SwitchChannelKTVBGResp{} }
func (m *SwitchChannelKTVBGResp) String() string { return proto.CompactTextString(m) }
func (*SwitchChannelKTVBGResp) ProtoMessage()    {}
func (*SwitchChannelKTVBGResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{34}
}
func (m *SwitchChannelKTVBGResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchChannelKTVBGResp.Unmarshal(m, b)
}
func (m *SwitchChannelKTVBGResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchChannelKTVBGResp.Marshal(b, m, deterministic)
}
func (dst *SwitchChannelKTVBGResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchChannelKTVBGResp.Merge(dst, src)
}
func (m *SwitchChannelKTVBGResp) XXX_Size() int {
	return xxx_messageInfo_SwitchChannelKTVBGResp.Size(m)
}
func (m *SwitchChannelKTVBGResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchChannelKTVBGResp.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchChannelKTVBGResp proto.InternalMessageInfo

type GetChannelKTVBGReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelKTVBGReq) Reset()         { *m = GetChannelKTVBGReq{} }
func (m *GetChannelKTVBGReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelKTVBGReq) ProtoMessage()    {}
func (*GetChannelKTVBGReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{35}
}
func (m *GetChannelKTVBGReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelKTVBGReq.Unmarshal(m, b)
}
func (m *GetChannelKTVBGReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelKTVBGReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelKTVBGReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelKTVBGReq.Merge(dst, src)
}
func (m *GetChannelKTVBGReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelKTVBGReq.Size(m)
}
func (m *GetChannelKTVBGReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelKTVBGReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelKTVBGReq proto.InternalMessageInfo

func (m *GetChannelKTVBGReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetChannelKTVBGResp struct {
	Open                 bool     `protobuf:"varint,1,opt,name=open,proto3" json:"open,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelKTVBGResp) Reset()         { *m = GetChannelKTVBGResp{} }
func (m *GetChannelKTVBGResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelKTVBGResp) ProtoMessage()    {}
func (*GetChannelKTVBGResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{36}
}
func (m *GetChannelKTVBGResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelKTVBGResp.Unmarshal(m, b)
}
func (m *GetChannelKTVBGResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelKTVBGResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelKTVBGResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelKTVBGResp.Merge(dst, src)
}
func (m *GetChannelKTVBGResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelKTVBGResp.Size(m)
}
func (m *GetChannelKTVBGResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelKTVBGResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelKTVBGResp proto.InternalMessageInfo

func (m *GetChannelKTVBGResp) GetOpen() bool {
	if m != nil {
		return m.Open
	}
	return false
}

type ChannelKTVHandClapReq struct {
	Uid                  uint32                `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32                `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	FromUserMicStatus    HandClapUserMicStatus `protobuf:"varint,3,opt,name=from_user_mic_status,json=fromUserMicStatus,proto3,enum=channel_ktv.HandClapUserMicStatus" json:"from_user_mic_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *ChannelKTVHandClapReq) Reset()         { *m = ChannelKTVHandClapReq{} }
func (m *ChannelKTVHandClapReq) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVHandClapReq) ProtoMessage()    {}
func (*ChannelKTVHandClapReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{37}
}
func (m *ChannelKTVHandClapReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVHandClapReq.Unmarshal(m, b)
}
func (m *ChannelKTVHandClapReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVHandClapReq.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVHandClapReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVHandClapReq.Merge(dst, src)
}
func (m *ChannelKTVHandClapReq) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVHandClapReq.Size(m)
}
func (m *ChannelKTVHandClapReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVHandClapReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVHandClapReq proto.InternalMessageInfo

func (m *ChannelKTVHandClapReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelKTVHandClapReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelKTVHandClapReq) GetFromUserMicStatus() HandClapUserMicStatus {
	if m != nil {
		return m.FromUserMicStatus
	}
	return HandClapUserMicStatus_HandClapUserMicStatus_UNKNOW
}

type ChannelKTVHandClapResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelKTVHandClapResp) Reset()         { *m = ChannelKTVHandClapResp{} }
func (m *ChannelKTVHandClapResp) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVHandClapResp) ProtoMessage()    {}
func (*ChannelKTVHandClapResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{38}
}
func (m *ChannelKTVHandClapResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVHandClapResp.Unmarshal(m, b)
}
func (m *ChannelKTVHandClapResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVHandClapResp.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVHandClapResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVHandClapResp.Merge(dst, src)
}
func (m *ChannelKTVHandClapResp) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVHandClapResp.Size(m)
}
func (m *ChannelKTVHandClapResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVHandClapResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVHandClapResp proto.InternalMessageInfo

type EndChannelKTVSingReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SongId               string   `protobuf:"bytes,3,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EndChannelKTVSingReq) Reset()         { *m = EndChannelKTVSingReq{} }
func (m *EndChannelKTVSingReq) String() string { return proto.CompactTextString(m) }
func (*EndChannelKTVSingReq) ProtoMessage()    {}
func (*EndChannelKTVSingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{39}
}
func (m *EndChannelKTVSingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EndChannelKTVSingReq.Unmarshal(m, b)
}
func (m *EndChannelKTVSingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EndChannelKTVSingReq.Marshal(b, m, deterministic)
}
func (dst *EndChannelKTVSingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EndChannelKTVSingReq.Merge(dst, src)
}
func (m *EndChannelKTVSingReq) XXX_Size() int {
	return xxx_messageInfo_EndChannelKTVSingReq.Size(m)
}
func (m *EndChannelKTVSingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_EndChannelKTVSingReq.DiscardUnknown(m)
}

var xxx_messageInfo_EndChannelKTVSingReq proto.InternalMessageInfo

func (m *EndChannelKTVSingReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *EndChannelKTVSingReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *EndChannelKTVSingReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

type EndChannelKTVSingResp struct {
	Info                 *ChannelKTVUpdate `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *EndChannelKTVSingResp) Reset()         { *m = EndChannelKTVSingResp{} }
func (m *EndChannelKTVSingResp) String() string { return proto.CompactTextString(m) }
func (*EndChannelKTVSingResp) ProtoMessage()    {}
func (*EndChannelKTVSingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{40}
}
func (m *EndChannelKTVSingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EndChannelKTVSingResp.Unmarshal(m, b)
}
func (m *EndChannelKTVSingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EndChannelKTVSingResp.Marshal(b, m, deterministic)
}
func (dst *EndChannelKTVSingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EndChannelKTVSingResp.Merge(dst, src)
}
func (m *EndChannelKTVSingResp) XXX_Size() int {
	return xxx_messageInfo_EndChannelKTVSingResp.Size(m)
}
func (m *EndChannelKTVSingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_EndChannelKTVSingResp.DiscardUnknown(m)
}

var xxx_messageInfo_EndChannelKTVSingResp proto.InternalMessageInfo

func (m *EndChannelKTVSingResp) GetInfo() *ChannelKTVUpdate {
	if m != nil {
		return m.Info
	}
	return nil
}

type ConfirmKTVHeartBeatReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SongId               string   `protobuf:"bytes,3,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConfirmKTVHeartBeatReq) Reset()         { *m = ConfirmKTVHeartBeatReq{} }
func (m *ConfirmKTVHeartBeatReq) String() string { return proto.CompactTextString(m) }
func (*ConfirmKTVHeartBeatReq) ProtoMessage()    {}
func (*ConfirmKTVHeartBeatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{41}
}
func (m *ConfirmKTVHeartBeatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfirmKTVHeartBeatReq.Unmarshal(m, b)
}
func (m *ConfirmKTVHeartBeatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfirmKTVHeartBeatReq.Marshal(b, m, deterministic)
}
func (dst *ConfirmKTVHeartBeatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfirmKTVHeartBeatReq.Merge(dst, src)
}
func (m *ConfirmKTVHeartBeatReq) XXX_Size() int {
	return xxx_messageInfo_ConfirmKTVHeartBeatReq.Size(m)
}
func (m *ConfirmKTVHeartBeatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfirmKTVHeartBeatReq.DiscardUnknown(m)
}

var xxx_messageInfo_ConfirmKTVHeartBeatReq proto.InternalMessageInfo

func (m *ConfirmKTVHeartBeatReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ConfirmKTVHeartBeatReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ConfirmKTVHeartBeatReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

type ConfirmKTVHeartBeatResp struct {
	Info                 *ChannelKTVUpdate `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ConfirmKTVHeartBeatResp) Reset()         { *m = ConfirmKTVHeartBeatResp{} }
func (m *ConfirmKTVHeartBeatResp) String() string { return proto.CompactTextString(m) }
func (*ConfirmKTVHeartBeatResp) ProtoMessage()    {}
func (*ConfirmKTVHeartBeatResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{42}
}
func (m *ConfirmKTVHeartBeatResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfirmKTVHeartBeatResp.Unmarshal(m, b)
}
func (m *ConfirmKTVHeartBeatResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfirmKTVHeartBeatResp.Marshal(b, m, deterministic)
}
func (dst *ConfirmKTVHeartBeatResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfirmKTVHeartBeatResp.Merge(dst, src)
}
func (m *ConfirmKTVHeartBeatResp) XXX_Size() int {
	return xxx_messageInfo_ConfirmKTVHeartBeatResp.Size(m)
}
func (m *ConfirmKTVHeartBeatResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfirmKTVHeartBeatResp.DiscardUnknown(m)
}

var xxx_messageInfo_ConfirmKTVHeartBeatResp proto.InternalMessageInfo

func (m *ConfirmKTVHeartBeatResp) GetInfo() *ChannelKTVUpdate {
	if m != nil {
		return m.Info
	}
	return nil
}

type LostHeartReq struct {
	InfoList             []*LostInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *LostHeartReq) Reset()         { *m = LostHeartReq{} }
func (m *LostHeartReq) String() string { return proto.CompactTextString(m) }
func (*LostHeartReq) ProtoMessage()    {}
func (*LostHeartReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{43}
}
func (m *LostHeartReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LostHeartReq.Unmarshal(m, b)
}
func (m *LostHeartReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LostHeartReq.Marshal(b, m, deterministic)
}
func (dst *LostHeartReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LostHeartReq.Merge(dst, src)
}
func (m *LostHeartReq) XXX_Size() int {
	return xxx_messageInfo_LostHeartReq.Size(m)
}
func (m *LostHeartReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LostHeartReq.DiscardUnknown(m)
}

var xxx_messageInfo_LostHeartReq proto.InternalMessageInfo

func (m *LostHeartReq) GetInfoList() []*LostInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type LostHeartResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LostHeartResp) Reset()         { *m = LostHeartResp{} }
func (m *LostHeartResp) String() string { return proto.CompactTextString(m) }
func (*LostHeartResp) ProtoMessage()    {}
func (*LostHeartResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{44}
}
func (m *LostHeartResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LostHeartResp.Unmarshal(m, b)
}
func (m *LostHeartResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LostHeartResp.Marshal(b, m, deterministic)
}
func (dst *LostHeartResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LostHeartResp.Merge(dst, src)
}
func (m *LostHeartResp) XXX_Size() int {
	return xxx_messageInfo_LostHeartResp.Size(m)
}
func (m *LostHeartResp) XXX_DiscardUnknown() {
	xxx_messageInfo_LostHeartResp.DiscardUnknown(m)
}

var xxx_messageInfo_LostHeartResp proto.InternalMessageInfo

type LostInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LostInfo) Reset()         { *m = LostInfo{} }
func (m *LostInfo) String() string { return proto.CompactTextString(m) }
func (*LostInfo) ProtoMessage()    {}
func (*LostInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{45}
}
func (m *LostInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LostInfo.Unmarshal(m, b)
}
func (m *LostInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LostInfo.Marshal(b, m, deterministic)
}
func (dst *LostInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LostInfo.Merge(dst, src)
}
func (m *LostInfo) XXX_Size() int {
	return xxx_messageInfo_LostInfo.Size(m)
}
func (m *LostInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_LostInfo.DiscardUnknown(m)
}

var xxx_messageInfo_LostInfo proto.InternalMessageInfo

func (m *LostInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *LostInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type KickOutKTVMemberReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	VictimId             uint32   `protobuf:"varint,3,opt,name=victim_id,json=victimId,proto3" json:"victim_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KickOutKTVMemberReq) Reset()         { *m = KickOutKTVMemberReq{} }
func (m *KickOutKTVMemberReq) String() string { return proto.CompactTextString(m) }
func (*KickOutKTVMemberReq) ProtoMessage()    {}
func (*KickOutKTVMemberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{46}
}
func (m *KickOutKTVMemberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KickOutKTVMemberReq.Unmarshal(m, b)
}
func (m *KickOutKTVMemberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KickOutKTVMemberReq.Marshal(b, m, deterministic)
}
func (dst *KickOutKTVMemberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KickOutKTVMemberReq.Merge(dst, src)
}
func (m *KickOutKTVMemberReq) XXX_Size() int {
	return xxx_messageInfo_KickOutKTVMemberReq.Size(m)
}
func (m *KickOutKTVMemberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_KickOutKTVMemberReq.DiscardUnknown(m)
}

var xxx_messageInfo_KickOutKTVMemberReq proto.InternalMessageInfo

func (m *KickOutKTVMemberReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *KickOutKTVMemberReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *KickOutKTVMemberReq) GetVictimId() uint32 {
	if m != nil {
		return m.VictimId
	}
	return 0
}

type KickOutKTVMemberResp struct {
	Info                 *ChannelKTVUpdate `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *KickOutKTVMemberResp) Reset()         { *m = KickOutKTVMemberResp{} }
func (m *KickOutKTVMemberResp) String() string { return proto.CompactTextString(m) }
func (*KickOutKTVMemberResp) ProtoMessage()    {}
func (*KickOutKTVMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{47}
}
func (m *KickOutKTVMemberResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KickOutKTVMemberResp.Unmarshal(m, b)
}
func (m *KickOutKTVMemberResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KickOutKTVMemberResp.Marshal(b, m, deterministic)
}
func (dst *KickOutKTVMemberResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KickOutKTVMemberResp.Merge(dst, src)
}
func (m *KickOutKTVMemberResp) XXX_Size() int {
	return xxx_messageInfo_KickOutKTVMemberResp.Size(m)
}
func (m *KickOutKTVMemberResp) XXX_DiscardUnknown() {
	xxx_messageInfo_KickOutKTVMemberResp.DiscardUnknown(m)
}

var xxx_messageInfo_KickOutKTVMemberResp proto.InternalMessageInfo

func (m *KickOutKTVMemberResp) GetInfo() *ChannelKTVUpdate {
	if m != nil {
		return m.Info
	}
	return nil
}

type CutChannelKTVSongReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SongId               string   `protobuf:"bytes,3,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CutChannelKTVSongReq) Reset()         { *m = CutChannelKTVSongReq{} }
func (m *CutChannelKTVSongReq) String() string { return proto.CompactTextString(m) }
func (*CutChannelKTVSongReq) ProtoMessage()    {}
func (*CutChannelKTVSongReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{48}
}
func (m *CutChannelKTVSongReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CutChannelKTVSongReq.Unmarshal(m, b)
}
func (m *CutChannelKTVSongReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CutChannelKTVSongReq.Marshal(b, m, deterministic)
}
func (dst *CutChannelKTVSongReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CutChannelKTVSongReq.Merge(dst, src)
}
func (m *CutChannelKTVSongReq) XXX_Size() int {
	return xxx_messageInfo_CutChannelKTVSongReq.Size(m)
}
func (m *CutChannelKTVSongReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CutChannelKTVSongReq.DiscardUnknown(m)
}

var xxx_messageInfo_CutChannelKTVSongReq proto.InternalMessageInfo

func (m *CutChannelKTVSongReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CutChannelKTVSongReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CutChannelKTVSongReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

type CutChannelKTVSongResp struct {
	Info                 *ChannelKTVUpdate `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *CutChannelKTVSongResp) Reset()         { *m = CutChannelKTVSongResp{} }
func (m *CutChannelKTVSongResp) String() string { return proto.CompactTextString(m) }
func (*CutChannelKTVSongResp) ProtoMessage()    {}
func (*CutChannelKTVSongResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{49}
}
func (m *CutChannelKTVSongResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CutChannelKTVSongResp.Unmarshal(m, b)
}
func (m *CutChannelKTVSongResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CutChannelKTVSongResp.Marshal(b, m, deterministic)
}
func (dst *CutChannelKTVSongResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CutChannelKTVSongResp.Merge(dst, src)
}
func (m *CutChannelKTVSongResp) XXX_Size() int {
	return xxx_messageInfo_CutChannelKTVSongResp.Size(m)
}
func (m *CutChannelKTVSongResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CutChannelKTVSongResp.DiscardUnknown(m)
}

var xxx_messageInfo_CutChannelKTVSongResp proto.InternalMessageInfo

func (m *CutChannelKTVSongResp) GetInfo() *ChannelKTVUpdate {
	if m != nil {
		return m.Info
	}
	return nil
}

type ListChannelKTVSongListTypeReq struct {
	TypeId               int64    `protobuf:"varint,1,opt,name=type_id,json=typeId,proto3" json:"type_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListChannelKTVSongListTypeReq) Reset()         { *m = ListChannelKTVSongListTypeReq{} }
func (m *ListChannelKTVSongListTypeReq) String() string { return proto.CompactTextString(m) }
func (*ListChannelKTVSongListTypeReq) ProtoMessage()    {}
func (*ListChannelKTVSongListTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{50}
}
func (m *ListChannelKTVSongListTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListChannelKTVSongListTypeReq.Unmarshal(m, b)
}
func (m *ListChannelKTVSongListTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListChannelKTVSongListTypeReq.Marshal(b, m, deterministic)
}
func (dst *ListChannelKTVSongListTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListChannelKTVSongListTypeReq.Merge(dst, src)
}
func (m *ListChannelKTVSongListTypeReq) XXX_Size() int {
	return xxx_messageInfo_ListChannelKTVSongListTypeReq.Size(m)
}
func (m *ListChannelKTVSongListTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListChannelKTVSongListTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListChannelKTVSongListTypeReq proto.InternalMessageInfo

func (m *ListChannelKTVSongListTypeReq) GetTypeId() int64 {
	if m != nil {
		return m.TypeId
	}
	return 0
}

type ListChannelKTVSongListTypeResp struct {
	SongListTypeList     []*KTVSongListType `protobuf:"bytes,1,rep,name=song_list_type_list,json=songListTypeList,proto3" json:"song_list_type_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *ListChannelKTVSongListTypeResp) Reset()         { *m = ListChannelKTVSongListTypeResp{} }
func (m *ListChannelKTVSongListTypeResp) String() string { return proto.CompactTextString(m) }
func (*ListChannelKTVSongListTypeResp) ProtoMessage()    {}
func (*ListChannelKTVSongListTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{51}
}
func (m *ListChannelKTVSongListTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListChannelKTVSongListTypeResp.Unmarshal(m, b)
}
func (m *ListChannelKTVSongListTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListChannelKTVSongListTypeResp.Marshal(b, m, deterministic)
}
func (dst *ListChannelKTVSongListTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListChannelKTVSongListTypeResp.Merge(dst, src)
}
func (m *ListChannelKTVSongListTypeResp) XXX_Size() int {
	return xxx_messageInfo_ListChannelKTVSongListTypeResp.Size(m)
}
func (m *ListChannelKTVSongListTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListChannelKTVSongListTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListChannelKTVSongListTypeResp proto.InternalMessageInfo

func (m *ListChannelKTVSongListTypeResp) GetSongListTypeList() []*KTVSongListType {
	if m != nil {
		return m.SongListTypeList
	}
	return nil
}

// 歌单类型
type KTVSongListType struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Title                string   `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	CoverUrl             string   `protobuf:"bytes,4,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url,omitempty"`
	Position             uint32   `protobuf:"varint,5,opt,name=position,proto3" json:"position,omitempty"`
	Description          string   `protobuf:"bytes,7,opt,name=description,proto3" json:"description,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KTVSongListType) Reset()         { *m = KTVSongListType{} }
func (m *KTVSongListType) String() string { return proto.CompactTextString(m) }
func (*KTVSongListType) ProtoMessage()    {}
func (*KTVSongListType) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{52}
}
func (m *KTVSongListType) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KTVSongListType.Unmarshal(m, b)
}
func (m *KTVSongListType) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KTVSongListType.Marshal(b, m, deterministic)
}
func (dst *KTVSongListType) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KTVSongListType.Merge(dst, src)
}
func (m *KTVSongListType) XXX_Size() int {
	return xxx_messageInfo_KTVSongListType.Size(m)
}
func (m *KTVSongListType) XXX_DiscardUnknown() {
	xxx_messageInfo_KTVSongListType.DiscardUnknown(m)
}

var xxx_messageInfo_KTVSongListType proto.InternalMessageInfo

func (m *KTVSongListType) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *KTVSongListType) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *KTVSongListType) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *KTVSongListType) GetCoverUrl() string {
	if m != nil {
		return m.CoverUrl
	}
	return ""
}

func (m *KTVSongListType) GetPosition() uint32 {
	if m != nil {
		return m.Position
	}
	return 0
}

func (m *KTVSongListType) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

type KTVSinger struct {
	User                 *KTVUser `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	Score                uint32   `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
	Main                 bool     `protobuf:"varint,3,opt,name=main,proto3" json:"main,omitempty"`
	Playing              bool     `protobuf:"varint,4,opt,name=playing,proto3" json:"playing,omitempty"`
	BestUid              uint32   `protobuf:"varint,5,opt,name=best_uid,json=bestUid,proto3" json:"best_uid,omitempty"`
	SpecialEventFlag     bool     `protobuf:"varint,6,opt,name=special_event_flag,json=specialEventFlag,proto3" json:"special_event_flag,omitempty"`
	IsEvenOriginalSong   bool     `protobuf:"varint,7,opt,name=is_even_original_song,json=isEvenOriginalSong,proto3" json:"is_even_original_song,omitempty"`
	IsSilently           bool     `protobuf:"varint,8,opt,name=is_silently,json=isSilently,proto3" json:"is_silently,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KTVSinger) Reset()         { *m = KTVSinger{} }
func (m *KTVSinger) String() string { return proto.CompactTextString(m) }
func (*KTVSinger) ProtoMessage()    {}
func (*KTVSinger) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{53}
}
func (m *KTVSinger) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KTVSinger.Unmarshal(m, b)
}
func (m *KTVSinger) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KTVSinger.Marshal(b, m, deterministic)
}
func (dst *KTVSinger) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KTVSinger.Merge(dst, src)
}
func (m *KTVSinger) XXX_Size() int {
	return xxx_messageInfo_KTVSinger.Size(m)
}
func (m *KTVSinger) XXX_DiscardUnknown() {
	xxx_messageInfo_KTVSinger.DiscardUnknown(m)
}

var xxx_messageInfo_KTVSinger proto.InternalMessageInfo

func (m *KTVSinger) GetUser() *KTVUser {
	if m != nil {
		return m.User
	}
	return nil
}

func (m *KTVSinger) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *KTVSinger) GetMain() bool {
	if m != nil {
		return m.Main
	}
	return false
}

func (m *KTVSinger) GetPlaying() bool {
	if m != nil {
		return m.Playing
	}
	return false
}

func (m *KTVSinger) GetBestUid() uint32 {
	if m != nil {
		return m.BestUid
	}
	return 0
}

func (m *KTVSinger) GetSpecialEventFlag() bool {
	if m != nil {
		return m.SpecialEventFlag
	}
	return false
}

func (m *KTVSinger) GetIsEvenOriginalSong() bool {
	if m != nil {
		return m.IsEvenOriginalSong
	}
	return false
}

func (m *KTVSinger) GetIsSilently() bool {
	if m != nil {
		return m.IsSilently
	}
	return false
}

type ChannelKTVUpdate struct {
	ChannelId                  uint32                                  `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Status                     ChannelKTVUpdate_ChannelKTVUpdateStatus `protobuf:"varint,2,opt,name=status,proto3,enum=channel_ktv.ChannelKTVUpdate_ChannelKTVUpdateStatus" json:"status,omitempty"`
	SingId                     string                                  `protobuf:"bytes,3,opt,name=sing_id,json=singId,proto3" json:"sing_id,omitempty"`
	SingName                   string                                  `protobuf:"bytes,4,opt,name=sing_name,json=singName,proto3" json:"sing_name,omitempty"`
	SingUid                    uint32                                  `protobuf:"varint,5,opt,name=sing_uid,json=singUid,proto3" json:"sing_uid,omitempty"`
	SingerName                 string                                  `protobuf:"bytes,6,opt,name=singer_name,json=singerName,proto3" json:"singer_name,omitempty"`
	ShareToken                 string                                  `protobuf:"bytes,7,opt,name=share_token,json=shareToken,proto3" json:"share_token,omitempty"`
	Duration                   uint32                                  `protobuf:"varint,8,opt,name=duration,proto3" json:"duration,omitempty"`
	SingerList                 []*KTVSinger                            `protobuf:"bytes,9,rep,name=singer_list,json=singerList,proto3" json:"singer_list,omitempty"`
	PlayListCount              uint32                                  `protobuf:"varint,10,opt,name=play_list_count,json=playListCount,proto3" json:"play_list_count,omitempty"`
	NextSingId                 string                                  `protobuf:"bytes,11,opt,name=next_sing_id,json=nextSingId,proto3" json:"next_sing_id,omitempty"`
	NextSingName               string                                  `protobuf:"bytes,12,opt,name=next_sing_name,json=nextSingName,proto3" json:"next_sing_name,omitempty"`
	NextSingUid                uint32                                  `protobuf:"varint,13,opt,name=next_sing_uid,json=nextSingUid,proto3" json:"next_sing_uid,omitempty"`
	NextSingerName             string                                  `protobuf:"bytes,14,opt,name=next_singer_name,json=nextSingerName,proto3" json:"next_singer_name,omitempty"`
	Type                       ChannelKTVUpdate_PlayingUpdateType      `protobuf:"varint,15,opt,name=type,proto3,enum=channel_ktv.ChannelKTVUpdate_PlayingUpdateType" json:"type,omitempty"`
	BgOpen                     bool                                    `protobuf:"varint,16,opt,name=bg_open,json=bgOpen,proto3" json:"bg_open,omitempty"`
	UpdateTime                 int64                                   `protobuf:"varint,17,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	StartTime                  int64                                   `protobuf:"varint,18,opt,name=StartTime,proto3" json:"StartTime,omitempty"`
	SpecialEventMap            map[uint32]uint32                       `protobuf:"bytes,19,rep,name=special_event_map,json=specialEventMap,proto3" json:"special_event_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	SpecialEventAppendScoreMap map[uint32]uint32                       `protobuf:"bytes,22,rep,name=special_event_append_score_map,json=specialEventAppendScoreMap,proto3" json:"special_event_append_score_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	MaxScore                   uint32                                  `protobuf:"varint,21,opt,name=max_score,json=maxScore,proto3" json:"max_score,omitempty"`
	IsOriginalSong             bool                                    `protobuf:"varint,23,opt,name=is_original_song,json=isOriginalSong,proto3" json:"is_original_song,omitempty"`
	Glory                      *KTVGlory                               `protobuf:"bytes,24,opt,name=glory,proto3" json:"glory,omitempty"`
	IsPowerSong                bool                                    `protobuf:"varint,25,opt,name=is_power_song,json=isPowerSong,proto3" json:"is_power_song,omitempty"`
	BurstLightUserList         []uint32                                `protobuf:"varint,26,rep,packed,name=burst_light_user_list,json=burstLightUserList,proto3" json:"burst_light_user_list,omitempty"`
	RatingName                 string                                  `protobuf:"bytes,27,opt,name=RatingName,proto3" json:"RatingName,omitempty"`
	IsClimax                   bool                                    `protobuf:"varint,28,opt,name=is_climax,json=isClimax,proto3" json:"is_climax,omitempty"`
	SegmentBegin               uint32                                  `protobuf:"varint,29,opt,name=segment_begin,json=segmentBegin,proto3" json:"segment_begin,omitempty"`
	SegmentEnd                 uint32                                  `protobuf:"varint,30,opt,name=segment_end,json=segmentEnd,proto3" json:"segment_end,omitempty"`
	HandClapList               []uint32                                `protobuf:"varint,31,rep,packed,name=hand_clap_list,json=handClapList,proto3" json:"hand_clap_list,omitempty"`
	HandClapUser               []*KTVUser                              `protobuf:"bytes,32,rep,name=hand_clap_user,json=handClapUser,proto3" json:"hand_clap_user,omitempty"`
	VendorId                   int32                                   `protobuf:"varint,33,opt,name=vendor_id,json=vendorId,proto3" json:"vendor_id,omitempty"`
	PitchAbility               int32                                   `protobuf:"varint,34,opt,name=pitch_ability,json=pitchAbility,proto3" json:"pitch_ability,omitempty"`
	XXX_NoUnkeyedLiteral       struct{}                                `json:"-"`
	XXX_unrecognized           []byte                                  `json:"-"`
	XXX_sizecache              int32                                   `json:"-"`
}

func (m *ChannelKTVUpdate) Reset()         { *m = ChannelKTVUpdate{} }
func (m *ChannelKTVUpdate) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVUpdate) ProtoMessage()    {}
func (*ChannelKTVUpdate) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{54}
}
func (m *ChannelKTVUpdate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVUpdate.Unmarshal(m, b)
}
func (m *ChannelKTVUpdate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVUpdate.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVUpdate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVUpdate.Merge(dst, src)
}
func (m *ChannelKTVUpdate) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVUpdate.Size(m)
}
func (m *ChannelKTVUpdate) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVUpdate.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVUpdate proto.InternalMessageInfo

func (m *ChannelKTVUpdate) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelKTVUpdate) GetStatus() ChannelKTVUpdate_ChannelKTVUpdateStatus {
	if m != nil {
		return m.Status
	}
	return ChannelKTVUpdate_NOORDER
}

func (m *ChannelKTVUpdate) GetSingId() string {
	if m != nil {
		return m.SingId
	}
	return ""
}

func (m *ChannelKTVUpdate) GetSingName() string {
	if m != nil {
		return m.SingName
	}
	return ""
}

func (m *ChannelKTVUpdate) GetSingUid() uint32 {
	if m != nil {
		return m.SingUid
	}
	return 0
}

func (m *ChannelKTVUpdate) GetSingerName() string {
	if m != nil {
		return m.SingerName
	}
	return ""
}

func (m *ChannelKTVUpdate) GetShareToken() string {
	if m != nil {
		return m.ShareToken
	}
	return ""
}

func (m *ChannelKTVUpdate) GetDuration() uint32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

func (m *ChannelKTVUpdate) GetSingerList() []*KTVSinger {
	if m != nil {
		return m.SingerList
	}
	return nil
}

func (m *ChannelKTVUpdate) GetPlayListCount() uint32 {
	if m != nil {
		return m.PlayListCount
	}
	return 0
}

func (m *ChannelKTVUpdate) GetNextSingId() string {
	if m != nil {
		return m.NextSingId
	}
	return ""
}

func (m *ChannelKTVUpdate) GetNextSingName() string {
	if m != nil {
		return m.NextSingName
	}
	return ""
}

func (m *ChannelKTVUpdate) GetNextSingUid() uint32 {
	if m != nil {
		return m.NextSingUid
	}
	return 0
}

func (m *ChannelKTVUpdate) GetNextSingerName() string {
	if m != nil {
		return m.NextSingerName
	}
	return ""
}

func (m *ChannelKTVUpdate) GetType() ChannelKTVUpdate_PlayingUpdateType {
	if m != nil {
		return m.Type
	}
	return ChannelKTVUpdate_NORMAL
}

func (m *ChannelKTVUpdate) GetBgOpen() bool {
	if m != nil {
		return m.BgOpen
	}
	return false
}

func (m *ChannelKTVUpdate) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *ChannelKTVUpdate) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *ChannelKTVUpdate) GetSpecialEventMap() map[uint32]uint32 {
	if m != nil {
		return m.SpecialEventMap
	}
	return nil
}

func (m *ChannelKTVUpdate) GetSpecialEventAppendScoreMap() map[uint32]uint32 {
	if m != nil {
		return m.SpecialEventAppendScoreMap
	}
	return nil
}

func (m *ChannelKTVUpdate) GetMaxScore() uint32 {
	if m != nil {
		return m.MaxScore
	}
	return 0
}

func (m *ChannelKTVUpdate) GetIsOriginalSong() bool {
	if m != nil {
		return m.IsOriginalSong
	}
	return false
}

func (m *ChannelKTVUpdate) GetGlory() *KTVGlory {
	if m != nil {
		return m.Glory
	}
	return nil
}

func (m *ChannelKTVUpdate) GetIsPowerSong() bool {
	if m != nil {
		return m.IsPowerSong
	}
	return false
}

func (m *ChannelKTVUpdate) GetBurstLightUserList() []uint32 {
	if m != nil {
		return m.BurstLightUserList
	}
	return nil
}

func (m *ChannelKTVUpdate) GetRatingName() string {
	if m != nil {
		return m.RatingName
	}
	return ""
}

func (m *ChannelKTVUpdate) GetIsClimax() bool {
	if m != nil {
		return m.IsClimax
	}
	return false
}

func (m *ChannelKTVUpdate) GetSegmentBegin() uint32 {
	if m != nil {
		return m.SegmentBegin
	}
	return 0
}

func (m *ChannelKTVUpdate) GetSegmentEnd() uint32 {
	if m != nil {
		return m.SegmentEnd
	}
	return 0
}

func (m *ChannelKTVUpdate) GetHandClapList() []uint32 {
	if m != nil {
		return m.HandClapList
	}
	return nil
}

func (m *ChannelKTVUpdate) GetHandClapUser() []*KTVUser {
	if m != nil {
		return m.HandClapUser
	}
	return nil
}

func (m *ChannelKTVUpdate) GetVendorId() int32 {
	if m != nil {
		return m.VendorId
	}
	return 0
}

func (m *ChannelKTVUpdate) GetPitchAbility() int32 {
	if m != nil {
		return m.PitchAbility
	}
	return 0
}

type MatchKTVChannelReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SongList             uint32   `protobuf:"varint,2,opt,name=songList,proto3" json:"songList,omitempty"`
	ClientVersion        uint32   `protobuf:"varint,3,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	ClientType           uint32   `protobuf:"varint,4,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	ClientIp             string   `protobuf:"bytes,5,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	ClientTerminal       uint32   `protobuf:"varint,6,opt,name=client_terminal,json=clientTerminal,proto3" json:"client_terminal,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MatchKTVChannelReq) Reset()         { *m = MatchKTVChannelReq{} }
func (m *MatchKTVChannelReq) String() string { return proto.CompactTextString(m) }
func (*MatchKTVChannelReq) ProtoMessage()    {}
func (*MatchKTVChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{55}
}
func (m *MatchKTVChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MatchKTVChannelReq.Unmarshal(m, b)
}
func (m *MatchKTVChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MatchKTVChannelReq.Marshal(b, m, deterministic)
}
func (dst *MatchKTVChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MatchKTVChannelReq.Merge(dst, src)
}
func (m *MatchKTVChannelReq) XXX_Size() int {
	return xxx_messageInfo_MatchKTVChannelReq.Size(m)
}
func (m *MatchKTVChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MatchKTVChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_MatchKTVChannelReq proto.InternalMessageInfo

func (m *MatchKTVChannelReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MatchKTVChannelReq) GetSongList() uint32 {
	if m != nil {
		return m.SongList
	}
	return 0
}

func (m *MatchKTVChannelReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *MatchKTVChannelReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *MatchKTVChannelReq) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

func (m *MatchKTVChannelReq) GetClientTerminal() uint32 {
	if m != nil {
		return m.ClientTerminal
	}
	return 0
}

type MatchKTVChannelResp struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channelId,proto3" json:"channelId,omitempty"`
	ToastMessage         string   `protobuf:"bytes,2,opt,name=toast_message,json=toastMessage,proto3" json:"toast_message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MatchKTVChannelResp) Reset()         { *m = MatchKTVChannelResp{} }
func (m *MatchKTVChannelResp) String() string { return proto.CompactTextString(m) }
func (*MatchKTVChannelResp) ProtoMessage()    {}
func (*MatchKTVChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{56}
}
func (m *MatchKTVChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MatchKTVChannelResp.Unmarshal(m, b)
}
func (m *MatchKTVChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MatchKTVChannelResp.Marshal(b, m, deterministic)
}
func (dst *MatchKTVChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MatchKTVChannelResp.Merge(dst, src)
}
func (m *MatchKTVChannelResp) XXX_Size() int {
	return xxx_messageInfo_MatchKTVChannelResp.Size(m)
}
func (m *MatchKTVChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MatchKTVChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_MatchKTVChannelResp proto.InternalMessageInfo

func (m *MatchKTVChannelResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MatchKTVChannelResp) GetToastMessage() string {
	if m != nil {
		return m.ToastMessage
	}
	return ""
}

// 批量获取播放列表状态的请求参数
type GetPlayListStatusReq struct {
	ChannelIds           []uint32 `protobuf:"varint,1,rep,packed,name=channel_ids,json=channelIds,proto3" json:"channel_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPlayListStatusReq) Reset()         { *m = GetPlayListStatusReq{} }
func (m *GetPlayListStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetPlayListStatusReq) ProtoMessage()    {}
func (*GetPlayListStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{57}
}
func (m *GetPlayListStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlayListStatusReq.Unmarshal(m, b)
}
func (m *GetPlayListStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlayListStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetPlayListStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlayListStatusReq.Merge(dst, src)
}
func (m *GetPlayListStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetPlayListStatusReq.Size(m)
}
func (m *GetPlayListStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlayListStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlayListStatusReq proto.InternalMessageInfo

func (m *GetPlayListStatusReq) GetChannelIds() []uint32 {
	if m != nil {
		return m.ChannelIds
	}
	return nil
}

// 批量获取播放列表状态的响应参数
type GetPlayListStatusResp struct {
	StatusesMapping      map[uint32]*PlayListStatus `protobuf:"bytes,1,rep,name=statusesMapping,proto3" json:"statusesMapping,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetPlayListStatusResp) Reset()         { *m = GetPlayListStatusResp{} }
func (m *GetPlayListStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetPlayListStatusResp) ProtoMessage()    {}
func (*GetPlayListStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{58}
}
func (m *GetPlayListStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlayListStatusResp.Unmarshal(m, b)
}
func (m *GetPlayListStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlayListStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetPlayListStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlayListStatusResp.Merge(dst, src)
}
func (m *GetPlayListStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetPlayListStatusResp.Size(m)
}
func (m *GetPlayListStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlayListStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlayListStatusResp proto.InternalMessageInfo

func (m *GetPlayListStatusResp) GetStatusesMapping() map[uint32]*PlayListStatus {
	if m != nil {
		return m.StatusesMapping
	}
	return nil
}

// 播放列表状态
type PlayListStatus struct {
	ListLen              uint32                         `protobuf:"varint,1,opt,name=list_len,json=listLen,proto3" json:"list_len,omitempty"`
	CurPlayingSong       *PlayListStatus_CurPlayingSong `protobuf:"bytes,2,opt,name=cur_playing_song,json=curPlayingSong,proto3" json:"cur_playing_song,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *PlayListStatus) Reset()         { *m = PlayListStatus{} }
func (m *PlayListStatus) String() string { return proto.CompactTextString(m) }
func (*PlayListStatus) ProtoMessage()    {}
func (*PlayListStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{59}
}
func (m *PlayListStatus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlayListStatus.Unmarshal(m, b)
}
func (m *PlayListStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlayListStatus.Marshal(b, m, deterministic)
}
func (dst *PlayListStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlayListStatus.Merge(dst, src)
}
func (m *PlayListStatus) XXX_Size() int {
	return xxx_messageInfo_PlayListStatus.Size(m)
}
func (m *PlayListStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_PlayListStatus.DiscardUnknown(m)
}

var xxx_messageInfo_PlayListStatus proto.InternalMessageInfo

func (m *PlayListStatus) GetListLen() uint32 {
	if m != nil {
		return m.ListLen
	}
	return 0
}

func (m *PlayListStatus) GetCurPlayingSong() *PlayListStatus_CurPlayingSong {
	if m != nil {
		return m.CurPlayingSong
	}
	return nil
}

type PlayListStatus_CurPlayingSong struct {
	SongName             string   `protobuf:"bytes,1,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	SingerName           string   `protobuf:"bytes,2,opt,name=singer_name,json=singerName,proto3" json:"singer_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlayListStatus_CurPlayingSong) Reset()         { *m = PlayListStatus_CurPlayingSong{} }
func (m *PlayListStatus_CurPlayingSong) String() string { return proto.CompactTextString(m) }
func (*PlayListStatus_CurPlayingSong) ProtoMessage()    {}
func (*PlayListStatus_CurPlayingSong) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{59, 0}
}
func (m *PlayListStatus_CurPlayingSong) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlayListStatus_CurPlayingSong.Unmarshal(m, b)
}
func (m *PlayListStatus_CurPlayingSong) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlayListStatus_CurPlayingSong.Marshal(b, m, deterministic)
}
func (dst *PlayListStatus_CurPlayingSong) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlayListStatus_CurPlayingSong.Merge(dst, src)
}
func (m *PlayListStatus_CurPlayingSong) XXX_Size() int {
	return xxx_messageInfo_PlayListStatus_CurPlayingSong.Size(m)
}
func (m *PlayListStatus_CurPlayingSong) XXX_DiscardUnknown() {
	xxx_messageInfo_PlayListStatus_CurPlayingSong.DiscardUnknown(m)
}

var xxx_messageInfo_PlayListStatus_CurPlayingSong proto.InternalMessageInfo

func (m *PlayListStatus_CurPlayingSong) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

func (m *PlayListStatus_CurPlayingSong) GetSingerName() string {
	if m != nil {
		return m.SingerName
	}
	return ""
}

// 获取特殊事件的文案
type GetSpecialEventCopyWritingReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSpecialEventCopyWritingReq) Reset()         { *m = GetSpecialEventCopyWritingReq{} }
func (m *GetSpecialEventCopyWritingReq) String() string { return proto.CompactTextString(m) }
func (*GetSpecialEventCopyWritingReq) ProtoMessage()    {}
func (*GetSpecialEventCopyWritingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{60}
}
func (m *GetSpecialEventCopyWritingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSpecialEventCopyWritingReq.Unmarshal(m, b)
}
func (m *GetSpecialEventCopyWritingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSpecialEventCopyWritingReq.Marshal(b, m, deterministic)
}
func (dst *GetSpecialEventCopyWritingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSpecialEventCopyWritingReq.Merge(dst, src)
}
func (m *GetSpecialEventCopyWritingReq) XXX_Size() int {
	return xxx_messageInfo_GetSpecialEventCopyWritingReq.Size(m)
}
func (m *GetSpecialEventCopyWritingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSpecialEventCopyWritingReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSpecialEventCopyWritingReq proto.InternalMessageInfo

type GetSpecialEventCopyWritingResp struct {
	SpecialEventCopyWritingMap map[uint32]string `protobuf:"bytes,1,rep,name=special_event_copy_writing_map,json=specialEventCopyWritingMap,proto3" json:"special_event_copy_writing_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral       struct{}          `json:"-"`
	XXX_unrecognized           []byte            `json:"-"`
	XXX_sizecache              int32             `json:"-"`
}

func (m *GetSpecialEventCopyWritingResp) Reset()         { *m = GetSpecialEventCopyWritingResp{} }
func (m *GetSpecialEventCopyWritingResp) String() string { return proto.CompactTextString(m) }
func (*GetSpecialEventCopyWritingResp) ProtoMessage()    {}
func (*GetSpecialEventCopyWritingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{61}
}
func (m *GetSpecialEventCopyWritingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSpecialEventCopyWritingResp.Unmarshal(m, b)
}
func (m *GetSpecialEventCopyWritingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSpecialEventCopyWritingResp.Marshal(b, m, deterministic)
}
func (dst *GetSpecialEventCopyWritingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSpecialEventCopyWritingResp.Merge(dst, src)
}
func (m *GetSpecialEventCopyWritingResp) XXX_Size() int {
	return xxx_messageInfo_GetSpecialEventCopyWritingResp.Size(m)
}
func (m *GetSpecialEventCopyWritingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSpecialEventCopyWritingResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSpecialEventCopyWritingResp proto.InternalMessageInfo

func (m *GetSpecialEventCopyWritingResp) GetSpecialEventCopyWritingMap() map[uint32]string {
	if m != nil {
		return m.SpecialEventCopyWritingMap
	}
	return nil
}

// 根据歌单获取歌曲信息
type GetChannelKTVSongListByIdReq struct {
	SongListId           uint32   `protobuf:"varint,1,opt,name=song_list_id,json=songListId,proto3" json:"song_list_id,omitempty"`
	ClientVersion        uint32   `protobuf:"varint,100,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	DeviceId             string   `protobuf:"bytes,101,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	ClientType           uint32   `protobuf:"varint,102,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelKTVSongListByIdReq) Reset()         { *m = GetChannelKTVSongListByIdReq{} }
func (m *GetChannelKTVSongListByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelKTVSongListByIdReq) ProtoMessage()    {}
func (*GetChannelKTVSongListByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{62}
}
func (m *GetChannelKTVSongListByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelKTVSongListByIdReq.Unmarshal(m, b)
}
func (m *GetChannelKTVSongListByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelKTVSongListByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelKTVSongListByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelKTVSongListByIdReq.Merge(dst, src)
}
func (m *GetChannelKTVSongListByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelKTVSongListByIdReq.Size(m)
}
func (m *GetChannelKTVSongListByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelKTVSongListByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelKTVSongListByIdReq proto.InternalMessageInfo

func (m *GetChannelKTVSongListByIdReq) GetSongListId() uint32 {
	if m != nil {
		return m.SongListId
	}
	return 0
}

func (m *GetChannelKTVSongListByIdReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *GetChannelKTVSongListByIdReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *GetChannelKTVSongListByIdReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

type GetChannelKTVSongListByIdResp struct {
	SongList             []*KTVSong `protobuf:"bytes,1,rep,name=song_list,json=songList,proto3" json:"song_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetChannelKTVSongListByIdResp) Reset()         { *m = GetChannelKTVSongListByIdResp{} }
func (m *GetChannelKTVSongListByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelKTVSongListByIdResp) ProtoMessage()    {}
func (*GetChannelKTVSongListByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{63}
}
func (m *GetChannelKTVSongListByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelKTVSongListByIdResp.Unmarshal(m, b)
}
func (m *GetChannelKTVSongListByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelKTVSongListByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelKTVSongListByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelKTVSongListByIdResp.Merge(dst, src)
}
func (m *GetChannelKTVSongListByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelKTVSongListByIdResp.Size(m)
}
func (m *GetChannelKTVSongListByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelKTVSongListByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelKTVSongListByIdResp proto.InternalMessageInfo

func (m *GetChannelKTVSongListByIdResp) GetSongList() []*KTVSong {
	if m != nil {
		return m.SongList
	}
	return nil
}

type BatchGetChannelKTVInfoReq struct {
	ChannelIdList        []uint32 `protobuf:"varint,1,rep,packed,name=channel_id_list,json=channelIdList,proto3" json:"channel_id_list,omitempty"`
	ClientType           uint32   `protobuf:"varint,2,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	ClientVersion        uint32   `protobuf:"varint,3,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetChannelKTVInfoReq) Reset()         { *m = BatchGetChannelKTVInfoReq{} }
func (m *BatchGetChannelKTVInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelKTVInfoReq) ProtoMessage()    {}
func (*BatchGetChannelKTVInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{64}
}
func (m *BatchGetChannelKTVInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelKTVInfoReq.Unmarshal(m, b)
}
func (m *BatchGetChannelKTVInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelKTVInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelKTVInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelKTVInfoReq.Merge(dst, src)
}
func (m *BatchGetChannelKTVInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelKTVInfoReq.Size(m)
}
func (m *BatchGetChannelKTVInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelKTVInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelKTVInfoReq proto.InternalMessageInfo

func (m *BatchGetChannelKTVInfoReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

func (m *BatchGetChannelKTVInfoReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *BatchGetChannelKTVInfoReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

type BatchGetChannelKTVInfoResp struct {
	InfoList             []*ChannelKTVUpdate `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *BatchGetChannelKTVInfoResp) Reset()         { *m = BatchGetChannelKTVInfoResp{} }
func (m *BatchGetChannelKTVInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelKTVInfoResp) ProtoMessage()    {}
func (*BatchGetChannelKTVInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{65}
}
func (m *BatchGetChannelKTVInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelKTVInfoResp.Unmarshal(m, b)
}
func (m *BatchGetChannelKTVInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelKTVInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelKTVInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelKTVInfoResp.Merge(dst, src)
}
func (m *BatchGetChannelKTVInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelKTVInfoResp.Size(m)
}
func (m *BatchGetChannelKTVInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelKTVInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelKTVInfoResp proto.InternalMessageInfo

func (m *BatchGetChannelKTVInfoResp) GetInfoList() []*ChannelKTVUpdate {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type ChannelKTVPlayListUpdate struct {
	ChannelId            uint32                              `protobuf:"varint,1,opt,name=channelId,proto3" json:"channelId,omitempty"`
	Uid                  uint32                              `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Song                 []*KTVOrder                         `protobuf:"bytes,3,rep,name=song,proto3" json:"song,omitempty"`
	UpdateType           ChannelKTVPlayListUpdate_UpdateType `protobuf:"varint,4,opt,name=update_type,json=updateType,proto3,enum=channel_ktv.ChannelKTVPlayListUpdate_UpdateType" json:"update_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *ChannelKTVPlayListUpdate) Reset()         { *m = ChannelKTVPlayListUpdate{} }
func (m *ChannelKTVPlayListUpdate) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVPlayListUpdate) ProtoMessage()    {}
func (*ChannelKTVPlayListUpdate) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{66}
}
func (m *ChannelKTVPlayListUpdate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVPlayListUpdate.Unmarshal(m, b)
}
func (m *ChannelKTVPlayListUpdate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVPlayListUpdate.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVPlayListUpdate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVPlayListUpdate.Merge(dst, src)
}
func (m *ChannelKTVPlayListUpdate) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVPlayListUpdate.Size(m)
}
func (m *ChannelKTVPlayListUpdate) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVPlayListUpdate.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVPlayListUpdate proto.InternalMessageInfo

func (m *ChannelKTVPlayListUpdate) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelKTVPlayListUpdate) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelKTVPlayListUpdate) GetSong() []*KTVOrder {
	if m != nil {
		return m.Song
	}
	return nil
}

func (m *ChannelKTVPlayListUpdate) GetUpdateType() ChannelKTVPlayListUpdate_UpdateType {
	if m != nil {
		return m.UpdateType
	}
	return ChannelKTVPlayListUpdate_ADD
}

type QuerySongReq struct {
	Keyword              string   `protobuf:"bytes,1,opt,name=keyword,proto3" json:"keyword,omitempty"`
	Page                 uint32   `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QuerySongReq) Reset()         { *m = QuerySongReq{} }
func (m *QuerySongReq) String() string { return proto.CompactTextString(m) }
func (*QuerySongReq) ProtoMessage()    {}
func (*QuerySongReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{67}
}
func (m *QuerySongReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuerySongReq.Unmarshal(m, b)
}
func (m *QuerySongReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuerySongReq.Marshal(b, m, deterministic)
}
func (dst *QuerySongReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuerySongReq.Merge(dst, src)
}
func (m *QuerySongReq) XXX_Size() int {
	return xxx_messageInfo_QuerySongReq.Size(m)
}
func (m *QuerySongReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QuerySongReq.DiscardUnknown(m)
}

var xxx_messageInfo_QuerySongReq proto.InternalMessageInfo

func (m *QuerySongReq) GetKeyword() string {
	if m != nil {
		return m.Keyword
	}
	return ""
}

func (m *QuerySongReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *QuerySongReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type QuerySongResp struct {
	Code                 uint32                `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message              string                `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Songs                []*QuerySongResp_Song `protobuf:"bytes,3,rep,name=songs,proto3" json:"songs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *QuerySongResp) Reset()         { *m = QuerySongResp{} }
func (m *QuerySongResp) String() string { return proto.CompactTextString(m) }
func (*QuerySongResp) ProtoMessage()    {}
func (*QuerySongResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{68}
}
func (m *QuerySongResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuerySongResp.Unmarshal(m, b)
}
func (m *QuerySongResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuerySongResp.Marshal(b, m, deterministic)
}
func (dst *QuerySongResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuerySongResp.Merge(dst, src)
}
func (m *QuerySongResp) XXX_Size() int {
	return xxx_messageInfo_QuerySongResp.Size(m)
}
func (m *QuerySongResp) XXX_DiscardUnknown() {
	xxx_messageInfo_QuerySongResp.DiscardUnknown(m)
}

var xxx_messageInfo_QuerySongResp proto.InternalMessageInfo

func (m *QuerySongResp) GetCode() uint32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *QuerySongResp) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *QuerySongResp) GetSongs() []*QuerySongResp_Song {
	if m != nil {
		return m.Songs
	}
	return nil
}

type QuerySongResp_Song struct {
	SongId               string                    `protobuf:"bytes,1,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	SongName             string                    `protobuf:"bytes,2,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	SingerId             string                    `protobuf:"bytes,3,opt,name=singer_id,json=singerId,proto3" json:"singer_id,omitempty"`
	SingerName           string                    `protobuf:"bytes,4,opt,name=singer_name,json=singerName,proto3" json:"singer_name,omitempty"`
	SingerImg            string                    `protobuf:"bytes,5,opt,name=singer_img,json=singerImg,proto3" json:"singer_img,omitempty"`
	AlbumId              string                    `protobuf:"bytes,6,opt,name=album_id,json=albumId,proto3" json:"album_id,omitempty"`
	AlbumName            string                    `protobuf:"bytes,7,opt,name=album_name,json=albumName,proto3" json:"album_name,omitempty"`
	AlbumImg             string                    `protobuf:"bytes,8,opt,name=album_img,json=albumImg,proto3" json:"album_img,omitempty"`
	AlbumImgMini         string                    `protobuf:"bytes,9,opt,name=album_img_mini,json=albumImgMini,proto3" json:"album_img_mini,omitempty"`
	AlbumImgSmall        string                    `protobuf:"bytes,10,opt,name=album_img_small,json=albumImgSmall,proto3" json:"album_img_small,omitempty"`
	AlbumImgMedium       string                    `protobuf:"bytes,11,opt,name=album_img_medium,json=albumImgMedium,proto3" json:"album_img_medium,omitempty"`
	Copyright            *QuerySongResp_Copyright  `protobuf:"bytes,12,opt,name=copyright,proto3" json:"copyright,omitempty"`
	VendorId             int32                     `protobuf:"varint,13,opt,name=vendor_id,json=vendorId,proto3" json:"vendor_id,omitempty"`
	PitchAbility         int32                     `protobuf:"varint,14,opt,name=pitch_ability,json=pitchAbility,proto3" json:"pitch_ability,omitempty"`
	HasShortSegment      int32                     `protobuf:"varint,15,opt,name=has_short_segment,json=hasShortSegment,proto3" json:"has_short_segment,omitempty"`
	ClimaxInfo           *QuerySongResp_ClimaxInfo `protobuf:"bytes,16,opt,name=climax_info,json=climaxInfo,proto3" json:"climax_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *QuerySongResp_Song) Reset()         { *m = QuerySongResp_Song{} }
func (m *QuerySongResp_Song) String() string { return proto.CompactTextString(m) }
func (*QuerySongResp_Song) ProtoMessage()    {}
func (*QuerySongResp_Song) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{68, 0}
}
func (m *QuerySongResp_Song) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuerySongResp_Song.Unmarshal(m, b)
}
func (m *QuerySongResp_Song) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuerySongResp_Song.Marshal(b, m, deterministic)
}
func (dst *QuerySongResp_Song) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuerySongResp_Song.Merge(dst, src)
}
func (m *QuerySongResp_Song) XXX_Size() int {
	return xxx_messageInfo_QuerySongResp_Song.Size(m)
}
func (m *QuerySongResp_Song) XXX_DiscardUnknown() {
	xxx_messageInfo_QuerySongResp_Song.DiscardUnknown(m)
}

var xxx_messageInfo_QuerySongResp_Song proto.InternalMessageInfo

func (m *QuerySongResp_Song) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *QuerySongResp_Song) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

func (m *QuerySongResp_Song) GetSingerId() string {
	if m != nil {
		return m.SingerId
	}
	return ""
}

func (m *QuerySongResp_Song) GetSingerName() string {
	if m != nil {
		return m.SingerName
	}
	return ""
}

func (m *QuerySongResp_Song) GetSingerImg() string {
	if m != nil {
		return m.SingerImg
	}
	return ""
}

func (m *QuerySongResp_Song) GetAlbumId() string {
	if m != nil {
		return m.AlbumId
	}
	return ""
}

func (m *QuerySongResp_Song) GetAlbumName() string {
	if m != nil {
		return m.AlbumName
	}
	return ""
}

func (m *QuerySongResp_Song) GetAlbumImg() string {
	if m != nil {
		return m.AlbumImg
	}
	return ""
}

func (m *QuerySongResp_Song) GetAlbumImgMini() string {
	if m != nil {
		return m.AlbumImgMini
	}
	return ""
}

func (m *QuerySongResp_Song) GetAlbumImgSmall() string {
	if m != nil {
		return m.AlbumImgSmall
	}
	return ""
}

func (m *QuerySongResp_Song) GetAlbumImgMedium() string {
	if m != nil {
		return m.AlbumImgMedium
	}
	return ""
}

func (m *QuerySongResp_Song) GetCopyright() *QuerySongResp_Copyright {
	if m != nil {
		return m.Copyright
	}
	return nil
}

func (m *QuerySongResp_Song) GetVendorId() int32 {
	if m != nil {
		return m.VendorId
	}
	return 0
}

func (m *QuerySongResp_Song) GetPitchAbility() int32 {
	if m != nil {
		return m.PitchAbility
	}
	return 0
}

func (m *QuerySongResp_Song) GetHasShortSegment() int32 {
	if m != nil {
		return m.HasShortSegment
	}
	return 0
}

func (m *QuerySongResp_Song) GetClimaxInfo() *QuerySongResp_ClimaxInfo {
	if m != nil {
		return m.ClimaxInfo
	}
	return nil
}

type QuerySongResp_Copyright struct {
	SongLyric            uint32   `protobuf:"varint,1,opt,name=song_lyric,json=songLyric,proto3" json:"song_lyric,omitempty"`
	Recording            uint32   `protobuf:"varint,2,opt,name=recording,proto3" json:"recording,omitempty"`
	Channel              uint32   `protobuf:"varint,3,opt,name=channel,proto3" json:"channel,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QuerySongResp_Copyright) Reset()         { *m = QuerySongResp_Copyright{} }
func (m *QuerySongResp_Copyright) String() string { return proto.CompactTextString(m) }
func (*QuerySongResp_Copyright) ProtoMessage()    {}
func (*QuerySongResp_Copyright) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{68, 1}
}
func (m *QuerySongResp_Copyright) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuerySongResp_Copyright.Unmarshal(m, b)
}
func (m *QuerySongResp_Copyright) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuerySongResp_Copyright.Marshal(b, m, deterministic)
}
func (dst *QuerySongResp_Copyright) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuerySongResp_Copyright.Merge(dst, src)
}
func (m *QuerySongResp_Copyright) XXX_Size() int {
	return xxx_messageInfo_QuerySongResp_Copyright.Size(m)
}
func (m *QuerySongResp_Copyright) XXX_DiscardUnknown() {
	xxx_messageInfo_QuerySongResp_Copyright.DiscardUnknown(m)
}

var xxx_messageInfo_QuerySongResp_Copyright proto.InternalMessageInfo

func (m *QuerySongResp_Copyright) GetSongLyric() uint32 {
	if m != nil {
		return m.SongLyric
	}
	return 0
}

func (m *QuerySongResp_Copyright) GetRecording() uint32 {
	if m != nil {
		return m.Recording
	}
	return 0
}

func (m *QuerySongResp_Copyright) GetChannel() uint32 {
	if m != nil {
		return m.Channel
	}
	return 0
}

type QuerySongResp_ClimaxInfo struct {
	SegmentBegin         uint32   `protobuf:"varint,1,opt,name=segment_begin,json=segmentBegin,proto3" json:"segment_begin,omitempty"`
	SegmentEnd           uint32   `protobuf:"varint,2,opt,name=segment_end,json=segmentEnd,proto3" json:"segment_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QuerySongResp_ClimaxInfo) Reset()         { *m = QuerySongResp_ClimaxInfo{} }
func (m *QuerySongResp_ClimaxInfo) String() string { return proto.CompactTextString(m) }
func (*QuerySongResp_ClimaxInfo) ProtoMessage()    {}
func (*QuerySongResp_ClimaxInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{68, 2}
}
func (m *QuerySongResp_ClimaxInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuerySongResp_ClimaxInfo.Unmarshal(m, b)
}
func (m *QuerySongResp_ClimaxInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuerySongResp_ClimaxInfo.Marshal(b, m, deterministic)
}
func (dst *QuerySongResp_ClimaxInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuerySongResp_ClimaxInfo.Merge(dst, src)
}
func (m *QuerySongResp_ClimaxInfo) XXX_Size() int {
	return xxx_messageInfo_QuerySongResp_ClimaxInfo.Size(m)
}
func (m *QuerySongResp_ClimaxInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_QuerySongResp_ClimaxInfo.DiscardUnknown(m)
}

var xxx_messageInfo_QuerySongResp_ClimaxInfo proto.InternalMessageInfo

func (m *QuerySongResp_ClimaxInfo) GetSegmentBegin() uint32 {
	if m != nil {
		return m.SegmentBegin
	}
	return 0
}

func (m *QuerySongResp_ClimaxInfo) GetSegmentEnd() uint32 {
	if m != nil {
		return m.SegmentEnd
	}
	return 0
}

// 爆灯
type ChannelKTVBurstLightReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	DeviceId             string   `protobuf:"bytes,3,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelKTVBurstLightReq) Reset()         { *m = ChannelKTVBurstLightReq{} }
func (m *ChannelKTVBurstLightReq) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVBurstLightReq) ProtoMessage()    {}
func (*ChannelKTVBurstLightReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{69}
}
func (m *ChannelKTVBurstLightReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVBurstLightReq.Unmarshal(m, b)
}
func (m *ChannelKTVBurstLightReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVBurstLightReq.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVBurstLightReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVBurstLightReq.Merge(dst, src)
}
func (m *ChannelKTVBurstLightReq) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVBurstLightReq.Size(m)
}
func (m *ChannelKTVBurstLightReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVBurstLightReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVBurstLightReq proto.InternalMessageInfo

func (m *ChannelKTVBurstLightReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelKTVBurstLightReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelKTVBurstLightReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

type ChannelKTVBurstLightResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelKTVBurstLightResp) Reset()         { *m = ChannelKTVBurstLightResp{} }
func (m *ChannelKTVBurstLightResp) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVBurstLightResp) ProtoMessage()    {}
func (*ChannelKTVBurstLightResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{70}
}
func (m *ChannelKTVBurstLightResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVBurstLightResp.Unmarshal(m, b)
}
func (m *ChannelKTVBurstLightResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVBurstLightResp.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVBurstLightResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVBurstLightResp.Merge(dst, src)
}
func (m *ChannelKTVBurstLightResp) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVBurstLightResp.Size(m)
}
func (m *ChannelKTVBurstLightResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVBurstLightResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVBurstLightResp proto.InternalMessageInfo

// 一键击掌
type ChannelKTVHighFiveReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	UidList              []uint32 `protobuf:"varint,3,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelKTVHighFiveReq) Reset()         { *m = ChannelKTVHighFiveReq{} }
func (m *ChannelKTVHighFiveReq) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVHighFiveReq) ProtoMessage()    {}
func (*ChannelKTVHighFiveReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{71}
}
func (m *ChannelKTVHighFiveReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVHighFiveReq.Unmarshal(m, b)
}
func (m *ChannelKTVHighFiveReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVHighFiveReq.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVHighFiveReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVHighFiveReq.Merge(dst, src)
}
func (m *ChannelKTVHighFiveReq) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVHighFiveReq.Size(m)
}
func (m *ChannelKTVHighFiveReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVHighFiveReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVHighFiveReq proto.InternalMessageInfo

func (m *ChannelKTVHighFiveReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelKTVHighFiveReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelKTVHighFiveReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type ChannelKTVHighFiveResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelKTVHighFiveResp) Reset()         { *m = ChannelKTVHighFiveResp{} }
func (m *ChannelKTVHighFiveResp) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVHighFiveResp) ProtoMessage()    {}
func (*ChannelKTVHighFiveResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{72}
}
func (m *ChannelKTVHighFiveResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVHighFiveResp.Unmarshal(m, b)
}
func (m *ChannelKTVHighFiveResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVHighFiveResp.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVHighFiveResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVHighFiveResp.Merge(dst, src)
}
func (m *ChannelKTVHighFiveResp) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVHighFiveResp.Size(m)
}
func (m *ChannelKTVHighFiveResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVHighFiveResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVHighFiveResp proto.InternalMessageInfo

type ZegoSongList struct {
	TypeId               uint32      `protobuf:"varint,1,opt,name=type_id,json=typeId,proto3" json:"type_id,omitempty"`
	SongList             []*ZegoSong `protobuf:"bytes,2,rep,name=song_list,json=songList,proto3" json:"song_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *ZegoSongList) Reset()         { *m = ZegoSongList{} }
func (m *ZegoSongList) String() string { return proto.CompactTextString(m) }
func (*ZegoSongList) ProtoMessage()    {}
func (*ZegoSongList) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{73}
}
func (m *ZegoSongList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ZegoSongList.Unmarshal(m, b)
}
func (m *ZegoSongList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ZegoSongList.Marshal(b, m, deterministic)
}
func (dst *ZegoSongList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ZegoSongList.Merge(dst, src)
}
func (m *ZegoSongList) XXX_Size() int {
	return xxx_messageInfo_ZegoSongList.Size(m)
}
func (m *ZegoSongList) XXX_DiscardUnknown() {
	xxx_messageInfo_ZegoSongList.DiscardUnknown(m)
}

var xxx_messageInfo_ZegoSongList proto.InternalMessageInfo

func (m *ZegoSongList) GetTypeId() uint32 {
	if m != nil {
		return m.TypeId
	}
	return 0
}

func (m *ZegoSongList) GetSongList() []*ZegoSong {
	if m != nil {
		return m.SongList
	}
	return nil
}

type ZegoSong struct {
	SingerImg            string     `protobuf:"bytes,1,opt,name=singer_img,json=singerImg,proto3" json:"singer_img,omitempty"`
	AlbumImg             string     `protobuf:"bytes,2,opt,name=album_img,json=albumImg,proto3" json:"album_img,omitempty"`
	AlbumImgMini         string     `protobuf:"bytes,3,opt,name=album_img_mini,json=albumImgMini,proto3" json:"album_img_mini,omitempty"`
	AlbumImgSmall        string     `protobuf:"bytes,4,opt,name=album_img_small,json=albumImgSmall,proto3" json:"album_img_small,omitempty"`
	AlbumImgMedium       string     `protobuf:"bytes,5,opt,name=album_img_medium,json=albumImgMedium,proto3" json:"album_img_medium,omitempty"`
	SongId               string     `protobuf:"bytes,6,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	SongName             string     `protobuf:"bytes,7,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	SingerName           string     `protobuf:"bytes,8,opt,name=singer_name,json=singerName,proto3" json:"singer_name,omitempty"`
	AlbumName            string     `protobuf:"bytes,9,opt,name=album_name,json=albumName,proto3" json:"album_name,omitempty"`
	CopyRight            *Copyright `protobuf:"bytes,10,opt,name=copy_right,json=copyRight,proto3" json:"copy_right,omitempty"`
	VendorId             int32      `protobuf:"varint,11,opt,name=vendor_id,json=vendorId,proto3" json:"vendor_id,omitempty"`
	PitchAbility         int32      `protobuf:"varint,12,opt,name=pitch_ability,json=pitchAbility,proto3" json:"pitch_ability,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *ZegoSong) Reset()         { *m = ZegoSong{} }
func (m *ZegoSong) String() string { return proto.CompactTextString(m) }
func (*ZegoSong) ProtoMessage()    {}
func (*ZegoSong) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{74}
}
func (m *ZegoSong) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ZegoSong.Unmarshal(m, b)
}
func (m *ZegoSong) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ZegoSong.Marshal(b, m, deterministic)
}
func (dst *ZegoSong) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ZegoSong.Merge(dst, src)
}
func (m *ZegoSong) XXX_Size() int {
	return xxx_messageInfo_ZegoSong.Size(m)
}
func (m *ZegoSong) XXX_DiscardUnknown() {
	xxx_messageInfo_ZegoSong.DiscardUnknown(m)
}

var xxx_messageInfo_ZegoSong proto.InternalMessageInfo

func (m *ZegoSong) GetSingerImg() string {
	if m != nil {
		return m.SingerImg
	}
	return ""
}

func (m *ZegoSong) GetAlbumImg() string {
	if m != nil {
		return m.AlbumImg
	}
	return ""
}

func (m *ZegoSong) GetAlbumImgMini() string {
	if m != nil {
		return m.AlbumImgMini
	}
	return ""
}

func (m *ZegoSong) GetAlbumImgSmall() string {
	if m != nil {
		return m.AlbumImgSmall
	}
	return ""
}

func (m *ZegoSong) GetAlbumImgMedium() string {
	if m != nil {
		return m.AlbumImgMedium
	}
	return ""
}

func (m *ZegoSong) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *ZegoSong) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

func (m *ZegoSong) GetSingerName() string {
	if m != nil {
		return m.SingerName
	}
	return ""
}

func (m *ZegoSong) GetAlbumName() string {
	if m != nil {
		return m.AlbumName
	}
	return ""
}

func (m *ZegoSong) GetCopyRight() *Copyright {
	if m != nil {
		return m.CopyRight
	}
	return nil
}

func (m *ZegoSong) GetVendorId() int32 {
	if m != nil {
		return m.VendorId
	}
	return 0
}

func (m *ZegoSong) GetPitchAbility() int32 {
	if m != nil {
		return m.PitchAbility
	}
	return 0
}

type Copyright struct {
	SongLyric            int32    `protobuf:"varint,1,opt,name=song_lyric,json=songLyric,proto3" json:"song_lyric,omitempty"`
	Recording            int32    `protobuf:"varint,2,opt,name=recording,proto3" json:"recording,omitempty"`
	Channel              int32    `protobuf:"varint,3,opt,name=channel,proto3" json:"channel,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Copyright) Reset()         { *m = Copyright{} }
func (m *Copyright) String() string { return proto.CompactTextString(m) }
func (*Copyright) ProtoMessage()    {}
func (*Copyright) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{75}
}
func (m *Copyright) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Copyright.Unmarshal(m, b)
}
func (m *Copyright) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Copyright.Marshal(b, m, deterministic)
}
func (dst *Copyright) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Copyright.Merge(dst, src)
}
func (m *Copyright) XXX_Size() int {
	return xxx_messageInfo_Copyright.Size(m)
}
func (m *Copyright) XXX_DiscardUnknown() {
	xxx_messageInfo_Copyright.DiscardUnknown(m)
}

var xxx_messageInfo_Copyright proto.InternalMessageInfo

func (m *Copyright) GetSongLyric() int32 {
	if m != nil {
		return m.SongLyric
	}
	return 0
}

func (m *Copyright) GetRecording() int32 {
	if m != nil {
		return m.Recording
	}
	return 0
}

func (m *Copyright) GetChannel() int32 {
	if m != nil {
		return m.Channel
	}
	return 0
}

// 获取引导高潮歌单列表
type ChannelKTVGuideClimaxSongListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ClientVersion        uint32   `protobuf:"varint,100,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	DeviceId             string   `protobuf:"bytes,101,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	ClientType           uint32   `protobuf:"varint,102,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelKTVGuideClimaxSongListReq) Reset()         { *m = ChannelKTVGuideClimaxSongListReq{} }
func (m *ChannelKTVGuideClimaxSongListReq) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVGuideClimaxSongListReq) ProtoMessage()    {}
func (*ChannelKTVGuideClimaxSongListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{76}
}
func (m *ChannelKTVGuideClimaxSongListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVGuideClimaxSongListReq.Unmarshal(m, b)
}
func (m *ChannelKTVGuideClimaxSongListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVGuideClimaxSongListReq.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVGuideClimaxSongListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVGuideClimaxSongListReq.Merge(dst, src)
}
func (m *ChannelKTVGuideClimaxSongListReq) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVGuideClimaxSongListReq.Size(m)
}
func (m *ChannelKTVGuideClimaxSongListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVGuideClimaxSongListReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVGuideClimaxSongListReq proto.InternalMessageInfo

func (m *ChannelKTVGuideClimaxSongListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelKTVGuideClimaxSongListReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *ChannelKTVGuideClimaxSongListReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *ChannelKTVGuideClimaxSongListReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

type ChannelKTVGuideClimaxSongListResp struct {
	SongList             []*KTVSong `protobuf:"bytes,1,rep,name=song_list,json=songList,proto3" json:"song_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *ChannelKTVGuideClimaxSongListResp) Reset()         { *m = ChannelKTVGuideClimaxSongListResp{} }
func (m *ChannelKTVGuideClimaxSongListResp) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVGuideClimaxSongListResp) ProtoMessage()    {}
func (*ChannelKTVGuideClimaxSongListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{77}
}
func (m *ChannelKTVGuideClimaxSongListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVGuideClimaxSongListResp.Unmarshal(m, b)
}
func (m *ChannelKTVGuideClimaxSongListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVGuideClimaxSongListResp.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVGuideClimaxSongListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVGuideClimaxSongListResp.Merge(dst, src)
}
func (m *ChannelKTVGuideClimaxSongListResp) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVGuideClimaxSongListResp.Size(m)
}
func (m *ChannelKTVGuideClimaxSongListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVGuideClimaxSongListResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVGuideClimaxSongListResp proto.InternalMessageInfo

func (m *ChannelKTVGuideClimaxSongListResp) GetSongList() []*KTVSong {
	if m != nil {
		return m.SongList
	}
	return nil
}

// 批量获取歌曲高潮片段
type ChannelKTVBatchSongClimaxInfosReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SongIds              []string `protobuf:"bytes,2,rep,name=song_ids,json=songIds,proto3" json:"song_ids,omitempty"`
	ClientVersion        uint32   `protobuf:"varint,100,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	DeviceId             string   `protobuf:"bytes,101,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	ClientType           uint32   `protobuf:"varint,102,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelKTVBatchSongClimaxInfosReq) Reset()         { *m = ChannelKTVBatchSongClimaxInfosReq{} }
func (m *ChannelKTVBatchSongClimaxInfosReq) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVBatchSongClimaxInfosReq) ProtoMessage()    {}
func (*ChannelKTVBatchSongClimaxInfosReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{78}
}
func (m *ChannelKTVBatchSongClimaxInfosReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVBatchSongClimaxInfosReq.Unmarshal(m, b)
}
func (m *ChannelKTVBatchSongClimaxInfosReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVBatchSongClimaxInfosReq.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVBatchSongClimaxInfosReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVBatchSongClimaxInfosReq.Merge(dst, src)
}
func (m *ChannelKTVBatchSongClimaxInfosReq) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVBatchSongClimaxInfosReq.Size(m)
}
func (m *ChannelKTVBatchSongClimaxInfosReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVBatchSongClimaxInfosReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVBatchSongClimaxInfosReq proto.InternalMessageInfo

func (m *ChannelKTVBatchSongClimaxInfosReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelKTVBatchSongClimaxInfosReq) GetSongIds() []string {
	if m != nil {
		return m.SongIds
	}
	return nil
}

func (m *ChannelKTVBatchSongClimaxInfosReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *ChannelKTVBatchSongClimaxInfosReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *ChannelKTVBatchSongClimaxInfosReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

type ChannelKTVBatchSongClimaxInfosResp struct {
	ClimaxInfoMap        map[string]*ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo `protobuf:"bytes,1,rep,name=climax_info_map,json=climaxInfoMap,proto3" json:"climax_info_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                                                  `json:"-"`
	XXX_unrecognized     []byte                                                    `json:"-"`
	XXX_sizecache        int32                                                     `json:"-"`
}

func (m *ChannelKTVBatchSongClimaxInfosResp) Reset()         { *m = ChannelKTVBatchSongClimaxInfosResp{} }
func (m *ChannelKTVBatchSongClimaxInfosResp) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVBatchSongClimaxInfosResp) ProtoMessage()    {}
func (*ChannelKTVBatchSongClimaxInfosResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{79}
}
func (m *ChannelKTVBatchSongClimaxInfosResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVBatchSongClimaxInfosResp.Unmarshal(m, b)
}
func (m *ChannelKTVBatchSongClimaxInfosResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVBatchSongClimaxInfosResp.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVBatchSongClimaxInfosResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVBatchSongClimaxInfosResp.Merge(dst, src)
}
func (m *ChannelKTVBatchSongClimaxInfosResp) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVBatchSongClimaxInfosResp.Size(m)
}
func (m *ChannelKTVBatchSongClimaxInfosResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVBatchSongClimaxInfosResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVBatchSongClimaxInfosResp proto.InternalMessageInfo

func (m *ChannelKTVBatchSongClimaxInfosResp) GetClimaxInfoMap() map[string]*ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo {
	if m != nil {
		return m.ClimaxInfoMap
	}
	return nil
}

type ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo struct {
	SegmentBegin         uint32   `protobuf:"varint,1,opt,name=segment_begin,json=segmentBegin,proto3" json:"segment_begin,omitempty"`
	SegmentEnd           uint32   `protobuf:"varint,2,opt,name=segment_end,json=segmentEnd,proto3" json:"segment_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo) Reset() {
	*m = ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo{}
}
func (m *ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo) String() string {
	return proto.CompactTextString(m)
}
func (*ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo) ProtoMessage() {}
func (*ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{79, 1}
}
func (m *ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo.Unmarshal(m, b)
}
func (m *ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo.Merge(dst, src)
}
func (m *ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo.Size(m)
}
func (m *ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo proto.InternalMessageInfo

func (m *ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo) GetSegmentBegin() uint32 {
	if m != nil {
		return m.SegmentBegin
	}
	return 0
}

func (m *ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo) GetSegmentEnd() uint32 {
	if m != nil {
		return m.SegmentEnd
	}
	return 0
}

// 跳过前奏
type ChannelKTVSkipTheIntroReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SongId               string   `protobuf:"bytes,3,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	SkipTime             uint32   `protobuf:"varint,4,opt,name=skip_time,json=skipTime,proto3" json:"skip_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelKTVSkipTheIntroReq) Reset()         { *m = ChannelKTVSkipTheIntroReq{} }
func (m *ChannelKTVSkipTheIntroReq) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVSkipTheIntroReq) ProtoMessage()    {}
func (*ChannelKTVSkipTheIntroReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{80}
}
func (m *ChannelKTVSkipTheIntroReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVSkipTheIntroReq.Unmarshal(m, b)
}
func (m *ChannelKTVSkipTheIntroReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVSkipTheIntroReq.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVSkipTheIntroReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVSkipTheIntroReq.Merge(dst, src)
}
func (m *ChannelKTVSkipTheIntroReq) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVSkipTheIntroReq.Size(m)
}
func (m *ChannelKTVSkipTheIntroReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVSkipTheIntroReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVSkipTheIntroReq proto.InternalMessageInfo

func (m *ChannelKTVSkipTheIntroReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelKTVSkipTheIntroReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelKTVSkipTheIntroReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *ChannelKTVSkipTheIntroReq) GetSkipTime() uint32 {
	if m != nil {
		return m.SkipTime
	}
	return 0
}

type ChannelKTVSkipTheIntroResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelKTVSkipTheIntroResp) Reset()         { *m = ChannelKTVSkipTheIntroResp{} }
func (m *ChannelKTVSkipTheIntroResp) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVSkipTheIntroResp) ProtoMessage()    {}
func (*ChannelKTVSkipTheIntroResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_270d51215214308a, []int{81}
}
func (m *ChannelKTVSkipTheIntroResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVSkipTheIntroResp.Unmarshal(m, b)
}
func (m *ChannelKTVSkipTheIntroResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVSkipTheIntroResp.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVSkipTheIntroResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVSkipTheIntroResp.Merge(dst, src)
}
func (m *ChannelKTVSkipTheIntroResp) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVSkipTheIntroResp.Size(m)
}
func (m *ChannelKTVSkipTheIntroResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVSkipTheIntroResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVSkipTheIntroResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*KTVCopyright)(nil), "channel_ktv.KTVCopyright")
	proto.RegisterType((*KTVSong)(nil), "channel_ktv.KTVSong")
	proto.RegisterType((*KTVUser)(nil), "channel_ktv.KTVUser")
	proto.RegisterType((*KTVRecordSinger)(nil), "channel_ktv.KTVRecordSinger")
	proto.RegisterType((*KTVRecord)(nil), "channel_ktv.KTVRecord")
	proto.RegisterType((*KTVOrder)(nil), "channel_ktv.KTVOrder")
	proto.RegisterType((*KTVGlory)(nil), "channel_ktv.KTVGlory")
	proto.RegisterType((*GetChannelKTVSongListReq)(nil), "channel_ktv.GetChannelKTVSongListReq")
	proto.RegisterType((*GetChannelKTVSongListResp)(nil), "channel_ktv.GetChannelKTVSongListResp")
	proto.RegisterType((*GetChannelKTVHistoryListReq)(nil), "channel_ktv.GetChannelKTVHistoryListReq")
	proto.RegisterType((*GetChannelKTVHistoryListResp)(nil), "channel_ktv.GetChannelKTVHistoryListResp")
	proto.RegisterType((*GetChannelKTVRecommendListReq)(nil), "channel_ktv.GetChannelKTVRecommendListReq")
	proto.RegisterType((*GetChannelKTVRecommendListResp)(nil), "channel_ktv.GetChannelKTVRecommendListResp")
	proto.RegisterType((*GetChannelKTVPlayListReq)(nil), "channel_ktv.GetChannelKTVPlayListReq")
	proto.RegisterType((*GetChannelKTVPlayListResp)(nil), "channel_ktv.GetChannelKTVPlayListResp")
	proto.RegisterType((*GetChannelKTVGuessLikeSongListReq)(nil), "channel_ktv.GetChannelKTVGuessLikeSongListReq")
	proto.RegisterType((*GetChannelKTVGuessLikeSongListResp)(nil), "channel_ktv.GetChannelKTVGuessLikeSongListResp")
	proto.RegisterType((*AddChannelKTVSongToPlayListReq)(nil), "channel_ktv.AddChannelKTVSongToPlayListReq")
	proto.RegisterType((*AddChannelKTVSongToPlayListResp)(nil), "channel_ktv.AddChannelKTVSongToPlayListResp")
	proto.RegisterType((*MoveUpChannelKTVSongReq)(nil), "channel_ktv.MoveUpChannelKTVSongReq")
	proto.RegisterType((*MoveUpChannelKTVSongResp)(nil), "channel_ktv.MoveUpChannelKTVSongResp")
	proto.RegisterType((*RemoveChannelKTVSongReq)(nil), "channel_ktv.RemoveChannelKTVSongReq")
	proto.RegisterType((*RemoveChannelKTVSongResp)(nil), "channel_ktv.RemoveChannelKTVSongResp")
	proto.RegisterType((*BeginChannelKTVSingReq)(nil), "channel_ktv.BeginChannelKTVSingReq")
	proto.RegisterType((*BeginChannelKTVSingResp)(nil), "channel_ktv.BeginChannelKTVSingResp")
	proto.RegisterType((*GetChannelKTVInfoReq)(nil), "channel_ktv.GetChannelKTVInfoReq")
	proto.RegisterType((*GetChannelKTVInfoResp)(nil), "channel_ktv.GetChannelKTVInfoResp")
	proto.RegisterType((*JoinChannelKTVSingReq)(nil), "channel_ktv.JoinChannelKTVSingReq")
	proto.RegisterType((*JoinChannelKTVSingResp)(nil), "channel_ktv.JoinChannelKTVSingResp")
	proto.RegisterType((*QuitChannelKTVSingReq)(nil), "channel_ktv.QuitChannelKTVSingReq")
	proto.RegisterType((*QuitChannelKTVSingResp)(nil), "channel_ktv.QuitChannelKTVSingResp")
	proto.RegisterType((*UpdateChannelKTVScoreReq)(nil), "channel_ktv.UpdateChannelKTVScoreReq")
	proto.RegisterType((*UpdateChannelKTVScoreResp)(nil), "channel_ktv.UpdateChannelKTVScoreResp")
	proto.RegisterType((*SwitchChannelKTVBGReq)(nil), "channel_ktv.SwitchChannelKTVBGReq")
	proto.RegisterType((*SwitchChannelKTVBGResp)(nil), "channel_ktv.SwitchChannelKTVBGResp")
	proto.RegisterType((*GetChannelKTVBGReq)(nil), "channel_ktv.GetChannelKTVBGReq")
	proto.RegisterType((*GetChannelKTVBGResp)(nil), "channel_ktv.GetChannelKTVBGResp")
	proto.RegisterType((*ChannelKTVHandClapReq)(nil), "channel_ktv.ChannelKTVHandClapReq")
	proto.RegisterType((*ChannelKTVHandClapResp)(nil), "channel_ktv.ChannelKTVHandClapResp")
	proto.RegisterType((*EndChannelKTVSingReq)(nil), "channel_ktv.EndChannelKTVSingReq")
	proto.RegisterType((*EndChannelKTVSingResp)(nil), "channel_ktv.EndChannelKTVSingResp")
	proto.RegisterType((*ConfirmKTVHeartBeatReq)(nil), "channel_ktv.ConfirmKTVHeartBeatReq")
	proto.RegisterType((*ConfirmKTVHeartBeatResp)(nil), "channel_ktv.ConfirmKTVHeartBeatResp")
	proto.RegisterType((*LostHeartReq)(nil), "channel_ktv.LostHeartReq")
	proto.RegisterType((*LostHeartResp)(nil), "channel_ktv.LostHeartResp")
	proto.RegisterType((*LostInfo)(nil), "channel_ktv.LostInfo")
	proto.RegisterType((*KickOutKTVMemberReq)(nil), "channel_ktv.KickOutKTVMemberReq")
	proto.RegisterType((*KickOutKTVMemberResp)(nil), "channel_ktv.KickOutKTVMemberResp")
	proto.RegisterType((*CutChannelKTVSongReq)(nil), "channel_ktv.CutChannelKTVSongReq")
	proto.RegisterType((*CutChannelKTVSongResp)(nil), "channel_ktv.CutChannelKTVSongResp")
	proto.RegisterType((*ListChannelKTVSongListTypeReq)(nil), "channel_ktv.ListChannelKTVSongListTypeReq")
	proto.RegisterType((*ListChannelKTVSongListTypeResp)(nil), "channel_ktv.ListChannelKTVSongListTypeResp")
	proto.RegisterType((*KTVSongListType)(nil), "channel_ktv.KTVSongListType")
	proto.RegisterType((*KTVSinger)(nil), "channel_ktv.KTVSinger")
	proto.RegisterType((*ChannelKTVUpdate)(nil), "channel_ktv.ChannelKTVUpdate")
	proto.RegisterMapType((map[uint32]uint32)(nil), "channel_ktv.ChannelKTVUpdate.SpecialEventAppendScoreMapEntry")
	proto.RegisterMapType((map[uint32]uint32)(nil), "channel_ktv.ChannelKTVUpdate.SpecialEventMapEntry")
	proto.RegisterType((*MatchKTVChannelReq)(nil), "channel_ktv.MatchKTVChannelReq")
	proto.RegisterType((*MatchKTVChannelResp)(nil), "channel_ktv.MatchKTVChannelResp")
	proto.RegisterType((*GetPlayListStatusReq)(nil), "channel_ktv.GetPlayListStatusReq")
	proto.RegisterType((*GetPlayListStatusResp)(nil), "channel_ktv.GetPlayListStatusResp")
	proto.RegisterMapType((map[uint32]*PlayListStatus)(nil), "channel_ktv.GetPlayListStatusResp.StatusesMappingEntry")
	proto.RegisterType((*PlayListStatus)(nil), "channel_ktv.PlayListStatus")
	proto.RegisterType((*PlayListStatus_CurPlayingSong)(nil), "channel_ktv.PlayListStatus.CurPlayingSong")
	proto.RegisterType((*GetSpecialEventCopyWritingReq)(nil), "channel_ktv.GetSpecialEventCopyWritingReq")
	proto.RegisterType((*GetSpecialEventCopyWritingResp)(nil), "channel_ktv.GetSpecialEventCopyWritingResp")
	proto.RegisterMapType((map[uint32]string)(nil), "channel_ktv.GetSpecialEventCopyWritingResp.SpecialEventCopyWritingMapEntry")
	proto.RegisterType((*GetChannelKTVSongListByIdReq)(nil), "channel_ktv.GetChannelKTVSongListByIdReq")
	proto.RegisterType((*GetChannelKTVSongListByIdResp)(nil), "channel_ktv.GetChannelKTVSongListByIdResp")
	proto.RegisterType((*BatchGetChannelKTVInfoReq)(nil), "channel_ktv.BatchGetChannelKTVInfoReq")
	proto.RegisterType((*BatchGetChannelKTVInfoResp)(nil), "channel_ktv.BatchGetChannelKTVInfoResp")
	proto.RegisterType((*ChannelKTVPlayListUpdate)(nil), "channel_ktv.ChannelKTVPlayListUpdate")
	proto.RegisterType((*QuerySongReq)(nil), "channel_ktv.QuerySongReq")
	proto.RegisterType((*QuerySongResp)(nil), "channel_ktv.QuerySongResp")
	proto.RegisterType((*QuerySongResp_Song)(nil), "channel_ktv.QuerySongResp.Song")
	proto.RegisterType((*QuerySongResp_Copyright)(nil), "channel_ktv.QuerySongResp.Copyright")
	proto.RegisterType((*QuerySongResp_ClimaxInfo)(nil), "channel_ktv.QuerySongResp.ClimaxInfo")
	proto.RegisterType((*ChannelKTVBurstLightReq)(nil), "channel_ktv.ChannelKTVBurstLightReq")
	proto.RegisterType((*ChannelKTVBurstLightResp)(nil), "channel_ktv.ChannelKTVBurstLightResp")
	proto.RegisterType((*ChannelKTVHighFiveReq)(nil), "channel_ktv.ChannelKTVHighFiveReq")
	proto.RegisterType((*ChannelKTVHighFiveResp)(nil), "channel_ktv.ChannelKTVHighFiveResp")
	proto.RegisterType((*ZegoSongList)(nil), "channel_ktv.ZegoSongList")
	proto.RegisterType((*ZegoSong)(nil), "channel_ktv.ZegoSong")
	proto.RegisterType((*Copyright)(nil), "channel_ktv.Copyright")
	proto.RegisterType((*ChannelKTVGuideClimaxSongListReq)(nil), "channel_ktv.ChannelKTVGuideClimaxSongListReq")
	proto.RegisterType((*ChannelKTVGuideClimaxSongListResp)(nil), "channel_ktv.ChannelKTVGuideClimaxSongListResp")
	proto.RegisterType((*ChannelKTVBatchSongClimaxInfosReq)(nil), "channel_ktv.ChannelKTVBatchSongClimaxInfosReq")
	proto.RegisterType((*ChannelKTVBatchSongClimaxInfosResp)(nil), "channel_ktv.ChannelKTVBatchSongClimaxInfosResp")
	proto.RegisterMapType((map[string]*ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo)(nil), "channel_ktv.ChannelKTVBatchSongClimaxInfosResp.ClimaxInfoMapEntry")
	proto.RegisterType((*ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo)(nil), "channel_ktv.ChannelKTVBatchSongClimaxInfosResp.ClimaxInfo")
	proto.RegisterType((*ChannelKTVSkipTheIntroReq)(nil), "channel_ktv.ChannelKTVSkipTheIntroReq")
	proto.RegisterType((*ChannelKTVSkipTheIntroResp)(nil), "channel_ktv.ChannelKTVSkipTheIntroResp")
	proto.RegisterEnum("channel_ktv.HandClapUserMicStatus", HandClapUserMicStatus_name, HandClapUserMicStatus_value)
	proto.RegisterEnum("channel_ktv.ChannelKTVUpdate_ChannelKTVUpdateStatus", ChannelKTVUpdate_ChannelKTVUpdateStatus_name, ChannelKTVUpdate_ChannelKTVUpdateStatus_value)
	proto.RegisterEnum("channel_ktv.ChannelKTVUpdate_PlayingUpdateType", ChannelKTVUpdate_PlayingUpdateType_name, ChannelKTVUpdate_PlayingUpdateType_value)
	proto.RegisterEnum("channel_ktv.ChannelKTVPlayListUpdate_UpdateType", ChannelKTVPlayListUpdate_UpdateType_name, ChannelKTVPlayListUpdate_UpdateType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelKTVClient is the client API for ChannelKTV service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelKTVClient interface {
	// 获取榜单歌单列表，分页
	GetChannelKTVSongList(ctx context.Context, in *GetChannelKTVSongListReq, opts ...grpc.CallOption) (*GetChannelKTVSongListResp, error)
	// 获取已唱历史列表
	GetChannelKTVHistoryList(ctx context.Context, in *GetChannelKTVHistoryListReq, opts ...grpc.CallOption) (*GetChannelKTVHistoryListResp, error)
	// 获取推荐歌曲列表
	GetChannelKTVRecommendList(ctx context.Context, in *GetChannelKTVRecommendListReq, opts ...grpc.CallOption) (*GetChannelKTVRecommendListResp, error)
	// 获取已点列表
	GetChannelKTVPlayList(ctx context.Context, in *GetChannelKTVPlayListReq, opts ...grpc.CallOption) (*GetChannelKTVPlayListResp, error)
	// 获取猜你想唱列表
	GetChannelKTVGuessLikeSongList(ctx context.Context, in *GetChannelKTVGuessLikeSongListReq, opts ...grpc.CallOption) (*GetChannelKTVGuessLikeSongListResp, error)
	// 点歌
	AddChannelKTVSongToPlayList(ctx context.Context, in *AddChannelKTVSongToPlayListReq, opts ...grpc.CallOption) (*AddChannelKTVSongToPlayListResp, error)
	// 顶歌
	MoveUpChannelKTVSong(ctx context.Context, in *MoveUpChannelKTVSongReq, opts ...grpc.CallOption) (*MoveUpChannelKTVSongResp, error)
	// 删歌
	RemoveChannelKTVSong(ctx context.Context, in *RemoveChannelKTVSongReq, opts ...grpc.CallOption) (*RemoveChannelKTVSongResp, error)
	// 伴奏下载完成，开始演唱
	BeginChannelKTVSing(ctx context.Context, in *BeginChannelKTVSingReq, opts ...grpc.CallOption) (*BeginChannelKTVSingResp, error)
	// 获取KTV房的当前状态
	GetChannelKTVInfo(ctx context.Context, in *GetChannelKTVInfoReq, opts ...grpc.CallOption) (*GetChannelKTVInfoResp, error)
	// 加入合唱
	JoinChannelKTVSing(ctx context.Context, in *JoinChannelKTVSingReq, opts ...grpc.CallOption) (*JoinChannelKTVSingResp, error)
	// 退出合唱
	QuitChannelKTVSing(ctx context.Context, in *QuitChannelKTVSingReq, opts ...grpc.CallOption) (*QuitChannelKTVSingResp, error)
	// 打分心跳
	UpdateChannelKTVScore(ctx context.Context, in *UpdateChannelKTVScoreReq, opts ...grpc.CallOption) (*UpdateChannelKTVScoreResp, error)
	// 背景开关
	SwitchChannelKTVBG(ctx context.Context, in *SwitchChannelKTVBGReq, opts ...grpc.CallOption) (*SwitchChannelKTVBGResp, error)
	GetChannelKTVBG(ctx context.Context, in *GetChannelKTVBGReq, opts ...grpc.CallOption) (*GetChannelKTVBGResp, error)
	// 鼓掌
	ChannelKTVHandClap(ctx context.Context, in *ChannelKTVHandClapReq, opts ...grpc.CallOption) (*ChannelKTVHandClapResp, error)
	// 结束当前演唱
	EndChannelKTVSing(ctx context.Context, in *EndChannelKTVSingReq, opts ...grpc.CallOption) (*EndChannelKTVSingResp, error)
	// 确认推流
	ConfirmKTVHeartBeat(ctx context.Context, in *ConfirmKTVHeartBeatReq, opts ...grpc.CallOption) (*ConfirmKTVHeartBeatResp, error)
	LostHeart(ctx context.Context, in *LostHeartReq, opts ...grpc.CallOption) (*LostHeartResp, error)
	// 踢人
	KickOutKTVMember(ctx context.Context, in *KickOutKTVMemberReq, opts ...grpc.CallOption) (*KickOutKTVMemberResp, error)
	// 切歌
	CutChannelKTVSong(ctx context.Context, in *CutChannelKTVSongReq, opts ...grpc.CallOption) (*CutChannelKTVSongResp, error)
	// 获取歌单类型列表
	ListChannelKTVSongListType(ctx context.Context, in *ListChannelKTVSongListTypeReq, opts ...grpc.CallOption) (*ListChannelKTVSongListTypeResp, error)
	// 批量获取播放列表状态
	BatchGetPlayListStatus(ctx context.Context, in *GetPlayListStatusReq, opts ...grpc.CallOption) (*GetPlayListStatusResp, error)
	MatchKTVChannel(ctx context.Context, in *MatchKTVChannelReq, opts ...grpc.CallOption) (*MatchKTVChannelResp, error)
	// 获取特殊事件的文案
	GetSpecialEventCopyWriting(ctx context.Context, in *GetSpecialEventCopyWritingReq, opts ...grpc.CallOption) (*GetSpecialEventCopyWritingResp, error)
	// 根据歌单获取歌曲信息
	GetChannelKTVSongListById(ctx context.Context, in *GetChannelKTVSongListByIdReq, opts ...grpc.CallOption) (*GetChannelKTVSongListByIdResp, error)
	// 批量获取房间播放器信息
	BatchGetChannelKTVInfo(ctx context.Context, in *BatchGetChannelKTVInfoReq, opts ...grpc.CallOption) (*BatchGetChannelKTVInfoResp, error)
	// 搜索歌曲
	QuerySong(ctx context.Context, in *QuerySongReq, opts ...grpc.CallOption) (*QuerySongResp, error)
	// 爆灯
	ChannelKTVBurstLight(ctx context.Context, in *ChannelKTVBurstLightReq, opts ...grpc.CallOption) (*ChannelKTVBurstLightResp, error)
	// 一键击掌
	ChannelKTVHighFive(ctx context.Context, in *ChannelKTVHighFiveReq, opts ...grpc.CallOption) (*ChannelKTVHighFiveResp, error)
	// 获取引导高潮歌单列表
	ChannelKTVGuideClimaxSongList(ctx context.Context, in *ChannelKTVGuideClimaxSongListReq, opts ...grpc.CallOption) (*ChannelKTVGuideClimaxSongListResp, error)
	// 获取引导高潮歌单列表
	ChannelKTVBatchSongClimaxInfos(ctx context.Context, in *ChannelKTVBatchSongClimaxInfosReq, opts ...grpc.CallOption) (*ChannelKTVBatchSongClimaxInfosResp, error)
	// 跳过前奏
	ChannelKTVSkipTheIntro(ctx context.Context, in *ChannelKTVSkipTheIntroReq, opts ...grpc.CallOption) (*ChannelKTVSkipTheIntroResp, error)
}

type channelKTVClient struct {
	cc *grpc.ClientConn
}

func NewChannelKTVClient(cc *grpc.ClientConn) ChannelKTVClient {
	return &channelKTVClient{cc}
}

func (c *channelKTVClient) GetChannelKTVSongList(ctx context.Context, in *GetChannelKTVSongListReq, opts ...grpc.CallOption) (*GetChannelKTVSongListResp, error) {
	out := new(GetChannelKTVSongListResp)
	err := c.cc.Invoke(ctx, "/channel_ktv.ChannelKTV/GetChannelKTVSongList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelKTVClient) GetChannelKTVHistoryList(ctx context.Context, in *GetChannelKTVHistoryListReq, opts ...grpc.CallOption) (*GetChannelKTVHistoryListResp, error) {
	out := new(GetChannelKTVHistoryListResp)
	err := c.cc.Invoke(ctx, "/channel_ktv.ChannelKTV/GetChannelKTVHistoryList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelKTVClient) GetChannelKTVRecommendList(ctx context.Context, in *GetChannelKTVRecommendListReq, opts ...grpc.CallOption) (*GetChannelKTVRecommendListResp, error) {
	out := new(GetChannelKTVRecommendListResp)
	err := c.cc.Invoke(ctx, "/channel_ktv.ChannelKTV/GetChannelKTVRecommendList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelKTVClient) GetChannelKTVPlayList(ctx context.Context, in *GetChannelKTVPlayListReq, opts ...grpc.CallOption) (*GetChannelKTVPlayListResp, error) {
	out := new(GetChannelKTVPlayListResp)
	err := c.cc.Invoke(ctx, "/channel_ktv.ChannelKTV/GetChannelKTVPlayList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelKTVClient) GetChannelKTVGuessLikeSongList(ctx context.Context, in *GetChannelKTVGuessLikeSongListReq, opts ...grpc.CallOption) (*GetChannelKTVGuessLikeSongListResp, error) {
	out := new(GetChannelKTVGuessLikeSongListResp)
	err := c.cc.Invoke(ctx, "/channel_ktv.ChannelKTV/GetChannelKTVGuessLikeSongList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelKTVClient) AddChannelKTVSongToPlayList(ctx context.Context, in *AddChannelKTVSongToPlayListReq, opts ...grpc.CallOption) (*AddChannelKTVSongToPlayListResp, error) {
	out := new(AddChannelKTVSongToPlayListResp)
	err := c.cc.Invoke(ctx, "/channel_ktv.ChannelKTV/AddChannelKTVSongToPlayList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelKTVClient) MoveUpChannelKTVSong(ctx context.Context, in *MoveUpChannelKTVSongReq, opts ...grpc.CallOption) (*MoveUpChannelKTVSongResp, error) {
	out := new(MoveUpChannelKTVSongResp)
	err := c.cc.Invoke(ctx, "/channel_ktv.ChannelKTV/MoveUpChannelKTVSong", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelKTVClient) RemoveChannelKTVSong(ctx context.Context, in *RemoveChannelKTVSongReq, opts ...grpc.CallOption) (*RemoveChannelKTVSongResp, error) {
	out := new(RemoveChannelKTVSongResp)
	err := c.cc.Invoke(ctx, "/channel_ktv.ChannelKTV/RemoveChannelKTVSong", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelKTVClient) BeginChannelKTVSing(ctx context.Context, in *BeginChannelKTVSingReq, opts ...grpc.CallOption) (*BeginChannelKTVSingResp, error) {
	out := new(BeginChannelKTVSingResp)
	err := c.cc.Invoke(ctx, "/channel_ktv.ChannelKTV/BeginChannelKTVSing", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelKTVClient) GetChannelKTVInfo(ctx context.Context, in *GetChannelKTVInfoReq, opts ...grpc.CallOption) (*GetChannelKTVInfoResp, error) {
	out := new(GetChannelKTVInfoResp)
	err := c.cc.Invoke(ctx, "/channel_ktv.ChannelKTV/GetChannelKTVInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelKTVClient) JoinChannelKTVSing(ctx context.Context, in *JoinChannelKTVSingReq, opts ...grpc.CallOption) (*JoinChannelKTVSingResp, error) {
	out := new(JoinChannelKTVSingResp)
	err := c.cc.Invoke(ctx, "/channel_ktv.ChannelKTV/JoinChannelKTVSing", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelKTVClient) QuitChannelKTVSing(ctx context.Context, in *QuitChannelKTVSingReq, opts ...grpc.CallOption) (*QuitChannelKTVSingResp, error) {
	out := new(QuitChannelKTVSingResp)
	err := c.cc.Invoke(ctx, "/channel_ktv.ChannelKTV/QuitChannelKTVSing", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelKTVClient) UpdateChannelKTVScore(ctx context.Context, in *UpdateChannelKTVScoreReq, opts ...grpc.CallOption) (*UpdateChannelKTVScoreResp, error) {
	out := new(UpdateChannelKTVScoreResp)
	err := c.cc.Invoke(ctx, "/channel_ktv.ChannelKTV/UpdateChannelKTVScore", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelKTVClient) SwitchChannelKTVBG(ctx context.Context, in *SwitchChannelKTVBGReq, opts ...grpc.CallOption) (*SwitchChannelKTVBGResp, error) {
	out := new(SwitchChannelKTVBGResp)
	err := c.cc.Invoke(ctx, "/channel_ktv.ChannelKTV/SwitchChannelKTVBG", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelKTVClient) GetChannelKTVBG(ctx context.Context, in *GetChannelKTVBGReq, opts ...grpc.CallOption) (*GetChannelKTVBGResp, error) {
	out := new(GetChannelKTVBGResp)
	err := c.cc.Invoke(ctx, "/channel_ktv.ChannelKTV/GetChannelKTVBG", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelKTVClient) ChannelKTVHandClap(ctx context.Context, in *ChannelKTVHandClapReq, opts ...grpc.CallOption) (*ChannelKTVHandClapResp, error) {
	out := new(ChannelKTVHandClapResp)
	err := c.cc.Invoke(ctx, "/channel_ktv.ChannelKTV/ChannelKTVHandClap", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelKTVClient) EndChannelKTVSing(ctx context.Context, in *EndChannelKTVSingReq, opts ...grpc.CallOption) (*EndChannelKTVSingResp, error) {
	out := new(EndChannelKTVSingResp)
	err := c.cc.Invoke(ctx, "/channel_ktv.ChannelKTV/EndChannelKTVSing", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelKTVClient) ConfirmKTVHeartBeat(ctx context.Context, in *ConfirmKTVHeartBeatReq, opts ...grpc.CallOption) (*ConfirmKTVHeartBeatResp, error) {
	out := new(ConfirmKTVHeartBeatResp)
	err := c.cc.Invoke(ctx, "/channel_ktv.ChannelKTV/ConfirmKTVHeartBeat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelKTVClient) LostHeart(ctx context.Context, in *LostHeartReq, opts ...grpc.CallOption) (*LostHeartResp, error) {
	out := new(LostHeartResp)
	err := c.cc.Invoke(ctx, "/channel_ktv.ChannelKTV/LostHeart", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelKTVClient) KickOutKTVMember(ctx context.Context, in *KickOutKTVMemberReq, opts ...grpc.CallOption) (*KickOutKTVMemberResp, error) {
	out := new(KickOutKTVMemberResp)
	err := c.cc.Invoke(ctx, "/channel_ktv.ChannelKTV/KickOutKTVMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelKTVClient) CutChannelKTVSong(ctx context.Context, in *CutChannelKTVSongReq, opts ...grpc.CallOption) (*CutChannelKTVSongResp, error) {
	out := new(CutChannelKTVSongResp)
	err := c.cc.Invoke(ctx, "/channel_ktv.ChannelKTV/CutChannelKTVSong", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelKTVClient) ListChannelKTVSongListType(ctx context.Context, in *ListChannelKTVSongListTypeReq, opts ...grpc.CallOption) (*ListChannelKTVSongListTypeResp, error) {
	out := new(ListChannelKTVSongListTypeResp)
	err := c.cc.Invoke(ctx, "/channel_ktv.ChannelKTV/ListChannelKTVSongListType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelKTVClient) BatchGetPlayListStatus(ctx context.Context, in *GetPlayListStatusReq, opts ...grpc.CallOption) (*GetPlayListStatusResp, error) {
	out := new(GetPlayListStatusResp)
	err := c.cc.Invoke(ctx, "/channel_ktv.ChannelKTV/BatchGetPlayListStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelKTVClient) MatchKTVChannel(ctx context.Context, in *MatchKTVChannelReq, opts ...grpc.CallOption) (*MatchKTVChannelResp, error) {
	out := new(MatchKTVChannelResp)
	err := c.cc.Invoke(ctx, "/channel_ktv.ChannelKTV/MatchKTVChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelKTVClient) GetSpecialEventCopyWriting(ctx context.Context, in *GetSpecialEventCopyWritingReq, opts ...grpc.CallOption) (*GetSpecialEventCopyWritingResp, error) {
	out := new(GetSpecialEventCopyWritingResp)
	err := c.cc.Invoke(ctx, "/channel_ktv.ChannelKTV/GetSpecialEventCopyWriting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelKTVClient) GetChannelKTVSongListById(ctx context.Context, in *GetChannelKTVSongListByIdReq, opts ...grpc.CallOption) (*GetChannelKTVSongListByIdResp, error) {
	out := new(GetChannelKTVSongListByIdResp)
	err := c.cc.Invoke(ctx, "/channel_ktv.ChannelKTV/GetChannelKTVSongListById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelKTVClient) BatchGetChannelKTVInfo(ctx context.Context, in *BatchGetChannelKTVInfoReq, opts ...grpc.CallOption) (*BatchGetChannelKTVInfoResp, error) {
	out := new(BatchGetChannelKTVInfoResp)
	err := c.cc.Invoke(ctx, "/channel_ktv.ChannelKTV/BatchGetChannelKTVInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelKTVClient) QuerySong(ctx context.Context, in *QuerySongReq, opts ...grpc.CallOption) (*QuerySongResp, error) {
	out := new(QuerySongResp)
	err := c.cc.Invoke(ctx, "/channel_ktv.ChannelKTV/QuerySong", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelKTVClient) ChannelKTVBurstLight(ctx context.Context, in *ChannelKTVBurstLightReq, opts ...grpc.CallOption) (*ChannelKTVBurstLightResp, error) {
	out := new(ChannelKTVBurstLightResp)
	err := c.cc.Invoke(ctx, "/channel_ktv.ChannelKTV/ChannelKTVBurstLight", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelKTVClient) ChannelKTVHighFive(ctx context.Context, in *ChannelKTVHighFiveReq, opts ...grpc.CallOption) (*ChannelKTVHighFiveResp, error) {
	out := new(ChannelKTVHighFiveResp)
	err := c.cc.Invoke(ctx, "/channel_ktv.ChannelKTV/ChannelKTVHighFive", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelKTVClient) ChannelKTVGuideClimaxSongList(ctx context.Context, in *ChannelKTVGuideClimaxSongListReq, opts ...grpc.CallOption) (*ChannelKTVGuideClimaxSongListResp, error) {
	out := new(ChannelKTVGuideClimaxSongListResp)
	err := c.cc.Invoke(ctx, "/channel_ktv.ChannelKTV/ChannelKTVGuideClimaxSongList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelKTVClient) ChannelKTVBatchSongClimaxInfos(ctx context.Context, in *ChannelKTVBatchSongClimaxInfosReq, opts ...grpc.CallOption) (*ChannelKTVBatchSongClimaxInfosResp, error) {
	out := new(ChannelKTVBatchSongClimaxInfosResp)
	err := c.cc.Invoke(ctx, "/channel_ktv.ChannelKTV/ChannelKTVBatchSongClimaxInfos", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelKTVClient) ChannelKTVSkipTheIntro(ctx context.Context, in *ChannelKTVSkipTheIntroReq, opts ...grpc.CallOption) (*ChannelKTVSkipTheIntroResp, error) {
	out := new(ChannelKTVSkipTheIntroResp)
	err := c.cc.Invoke(ctx, "/channel_ktv.ChannelKTV/ChannelKTVSkipTheIntro", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelKTVServer is the server API for ChannelKTV service.
type ChannelKTVServer interface {
	// 获取榜单歌单列表，分页
	GetChannelKTVSongList(context.Context, *GetChannelKTVSongListReq) (*GetChannelKTVSongListResp, error)
	// 获取已唱历史列表
	GetChannelKTVHistoryList(context.Context, *GetChannelKTVHistoryListReq) (*GetChannelKTVHistoryListResp, error)
	// 获取推荐歌曲列表
	GetChannelKTVRecommendList(context.Context, *GetChannelKTVRecommendListReq) (*GetChannelKTVRecommendListResp, error)
	// 获取已点列表
	GetChannelKTVPlayList(context.Context, *GetChannelKTVPlayListReq) (*GetChannelKTVPlayListResp, error)
	// 获取猜你想唱列表
	GetChannelKTVGuessLikeSongList(context.Context, *GetChannelKTVGuessLikeSongListReq) (*GetChannelKTVGuessLikeSongListResp, error)
	// 点歌
	AddChannelKTVSongToPlayList(context.Context, *AddChannelKTVSongToPlayListReq) (*AddChannelKTVSongToPlayListResp, error)
	// 顶歌
	MoveUpChannelKTVSong(context.Context, *MoveUpChannelKTVSongReq) (*MoveUpChannelKTVSongResp, error)
	// 删歌
	RemoveChannelKTVSong(context.Context, *RemoveChannelKTVSongReq) (*RemoveChannelKTVSongResp, error)
	// 伴奏下载完成，开始演唱
	BeginChannelKTVSing(context.Context, *BeginChannelKTVSingReq) (*BeginChannelKTVSingResp, error)
	// 获取KTV房的当前状态
	GetChannelKTVInfo(context.Context, *GetChannelKTVInfoReq) (*GetChannelKTVInfoResp, error)
	// 加入合唱
	JoinChannelKTVSing(context.Context, *JoinChannelKTVSingReq) (*JoinChannelKTVSingResp, error)
	// 退出合唱
	QuitChannelKTVSing(context.Context, *QuitChannelKTVSingReq) (*QuitChannelKTVSingResp, error)
	// 打分心跳
	UpdateChannelKTVScore(context.Context, *UpdateChannelKTVScoreReq) (*UpdateChannelKTVScoreResp, error)
	// 背景开关
	SwitchChannelKTVBG(context.Context, *SwitchChannelKTVBGReq) (*SwitchChannelKTVBGResp, error)
	GetChannelKTVBG(context.Context, *GetChannelKTVBGReq) (*GetChannelKTVBGResp, error)
	// 鼓掌
	ChannelKTVHandClap(context.Context, *ChannelKTVHandClapReq) (*ChannelKTVHandClapResp, error)
	// 结束当前演唱
	EndChannelKTVSing(context.Context, *EndChannelKTVSingReq) (*EndChannelKTVSingResp, error)
	// 确认推流
	ConfirmKTVHeartBeat(context.Context, *ConfirmKTVHeartBeatReq) (*ConfirmKTVHeartBeatResp, error)
	LostHeart(context.Context, *LostHeartReq) (*LostHeartResp, error)
	// 踢人
	KickOutKTVMember(context.Context, *KickOutKTVMemberReq) (*KickOutKTVMemberResp, error)
	// 切歌
	CutChannelKTVSong(context.Context, *CutChannelKTVSongReq) (*CutChannelKTVSongResp, error)
	// 获取歌单类型列表
	ListChannelKTVSongListType(context.Context, *ListChannelKTVSongListTypeReq) (*ListChannelKTVSongListTypeResp, error)
	// 批量获取播放列表状态
	BatchGetPlayListStatus(context.Context, *GetPlayListStatusReq) (*GetPlayListStatusResp, error)
	MatchKTVChannel(context.Context, *MatchKTVChannelReq) (*MatchKTVChannelResp, error)
	// 获取特殊事件的文案
	GetSpecialEventCopyWriting(context.Context, *GetSpecialEventCopyWritingReq) (*GetSpecialEventCopyWritingResp, error)
	// 根据歌单获取歌曲信息
	GetChannelKTVSongListById(context.Context, *GetChannelKTVSongListByIdReq) (*GetChannelKTVSongListByIdResp, error)
	// 批量获取房间播放器信息
	BatchGetChannelKTVInfo(context.Context, *BatchGetChannelKTVInfoReq) (*BatchGetChannelKTVInfoResp, error)
	// 搜索歌曲
	QuerySong(context.Context, *QuerySongReq) (*QuerySongResp, error)
	// 爆灯
	ChannelKTVBurstLight(context.Context, *ChannelKTVBurstLightReq) (*ChannelKTVBurstLightResp, error)
	// 一键击掌
	ChannelKTVHighFive(context.Context, *ChannelKTVHighFiveReq) (*ChannelKTVHighFiveResp, error)
	// 获取引导高潮歌单列表
	ChannelKTVGuideClimaxSongList(context.Context, *ChannelKTVGuideClimaxSongListReq) (*ChannelKTVGuideClimaxSongListResp, error)
	// 获取引导高潮歌单列表
	ChannelKTVBatchSongClimaxInfos(context.Context, *ChannelKTVBatchSongClimaxInfosReq) (*ChannelKTVBatchSongClimaxInfosResp, error)
	// 跳过前奏
	ChannelKTVSkipTheIntro(context.Context, *ChannelKTVSkipTheIntroReq) (*ChannelKTVSkipTheIntroResp, error)
}

func RegisterChannelKTVServer(s *grpc.Server, srv ChannelKTVServer) {
	s.RegisterService(&_ChannelKTV_serviceDesc, srv)
}

func _ChannelKTV_GetChannelKTVSongList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelKTVSongListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelKTVServer).GetChannelKTVSongList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ktv.ChannelKTV/GetChannelKTVSongList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelKTVServer).GetChannelKTVSongList(ctx, req.(*GetChannelKTVSongListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelKTV_GetChannelKTVHistoryList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelKTVHistoryListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelKTVServer).GetChannelKTVHistoryList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ktv.ChannelKTV/GetChannelKTVHistoryList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelKTVServer).GetChannelKTVHistoryList(ctx, req.(*GetChannelKTVHistoryListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelKTV_GetChannelKTVRecommendList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelKTVRecommendListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelKTVServer).GetChannelKTVRecommendList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ktv.ChannelKTV/GetChannelKTVRecommendList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelKTVServer).GetChannelKTVRecommendList(ctx, req.(*GetChannelKTVRecommendListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelKTV_GetChannelKTVPlayList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelKTVPlayListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelKTVServer).GetChannelKTVPlayList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ktv.ChannelKTV/GetChannelKTVPlayList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelKTVServer).GetChannelKTVPlayList(ctx, req.(*GetChannelKTVPlayListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelKTV_GetChannelKTVGuessLikeSongList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelKTVGuessLikeSongListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelKTVServer).GetChannelKTVGuessLikeSongList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ktv.ChannelKTV/GetChannelKTVGuessLikeSongList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelKTVServer).GetChannelKTVGuessLikeSongList(ctx, req.(*GetChannelKTVGuessLikeSongListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelKTV_AddChannelKTVSongToPlayList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddChannelKTVSongToPlayListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelKTVServer).AddChannelKTVSongToPlayList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ktv.ChannelKTV/AddChannelKTVSongToPlayList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelKTVServer).AddChannelKTVSongToPlayList(ctx, req.(*AddChannelKTVSongToPlayListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelKTV_MoveUpChannelKTVSong_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MoveUpChannelKTVSongReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelKTVServer).MoveUpChannelKTVSong(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ktv.ChannelKTV/MoveUpChannelKTVSong",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelKTVServer).MoveUpChannelKTVSong(ctx, req.(*MoveUpChannelKTVSongReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelKTV_RemoveChannelKTVSong_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveChannelKTVSongReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelKTVServer).RemoveChannelKTVSong(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ktv.ChannelKTV/RemoveChannelKTVSong",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelKTVServer).RemoveChannelKTVSong(ctx, req.(*RemoveChannelKTVSongReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelKTV_BeginChannelKTVSing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BeginChannelKTVSingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelKTVServer).BeginChannelKTVSing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ktv.ChannelKTV/BeginChannelKTVSing",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelKTVServer).BeginChannelKTVSing(ctx, req.(*BeginChannelKTVSingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelKTV_GetChannelKTVInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelKTVInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelKTVServer).GetChannelKTVInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ktv.ChannelKTV/GetChannelKTVInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelKTVServer).GetChannelKTVInfo(ctx, req.(*GetChannelKTVInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelKTV_JoinChannelKTVSing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JoinChannelKTVSingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelKTVServer).JoinChannelKTVSing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ktv.ChannelKTV/JoinChannelKTVSing",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelKTVServer).JoinChannelKTVSing(ctx, req.(*JoinChannelKTVSingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelKTV_QuitChannelKTVSing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QuitChannelKTVSingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelKTVServer).QuitChannelKTVSing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ktv.ChannelKTV/QuitChannelKTVSing",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelKTVServer).QuitChannelKTVSing(ctx, req.(*QuitChannelKTVSingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelKTV_UpdateChannelKTVScore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateChannelKTVScoreReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelKTVServer).UpdateChannelKTVScore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ktv.ChannelKTV/UpdateChannelKTVScore",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelKTVServer).UpdateChannelKTVScore(ctx, req.(*UpdateChannelKTVScoreReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelKTV_SwitchChannelKTVBG_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SwitchChannelKTVBGReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelKTVServer).SwitchChannelKTVBG(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ktv.ChannelKTV/SwitchChannelKTVBG",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelKTVServer).SwitchChannelKTVBG(ctx, req.(*SwitchChannelKTVBGReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelKTV_GetChannelKTVBG_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelKTVBGReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelKTVServer).GetChannelKTVBG(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ktv.ChannelKTV/GetChannelKTVBG",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelKTVServer).GetChannelKTVBG(ctx, req.(*GetChannelKTVBGReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelKTV_ChannelKTVHandClap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelKTVHandClapReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelKTVServer).ChannelKTVHandClap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ktv.ChannelKTV/ChannelKTVHandClap",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelKTVServer).ChannelKTVHandClap(ctx, req.(*ChannelKTVHandClapReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelKTV_EndChannelKTVSing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EndChannelKTVSingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelKTVServer).EndChannelKTVSing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ktv.ChannelKTV/EndChannelKTVSing",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelKTVServer).EndChannelKTVSing(ctx, req.(*EndChannelKTVSingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelKTV_ConfirmKTVHeartBeat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfirmKTVHeartBeatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelKTVServer).ConfirmKTVHeartBeat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ktv.ChannelKTV/ConfirmKTVHeartBeat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelKTVServer).ConfirmKTVHeartBeat(ctx, req.(*ConfirmKTVHeartBeatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelKTV_LostHeart_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LostHeartReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelKTVServer).LostHeart(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ktv.ChannelKTV/LostHeart",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelKTVServer).LostHeart(ctx, req.(*LostHeartReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelKTV_KickOutKTVMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(KickOutKTVMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelKTVServer).KickOutKTVMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ktv.ChannelKTV/KickOutKTVMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelKTVServer).KickOutKTVMember(ctx, req.(*KickOutKTVMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelKTV_CutChannelKTVSong_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CutChannelKTVSongReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelKTVServer).CutChannelKTVSong(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ktv.ChannelKTV/CutChannelKTVSong",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelKTVServer).CutChannelKTVSong(ctx, req.(*CutChannelKTVSongReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelKTV_ListChannelKTVSongListType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListChannelKTVSongListTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelKTVServer).ListChannelKTVSongListType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ktv.ChannelKTV/ListChannelKTVSongListType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelKTVServer).ListChannelKTVSongListType(ctx, req.(*ListChannelKTVSongListTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelKTV_BatchGetPlayListStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlayListStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelKTVServer).BatchGetPlayListStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ktv.ChannelKTV/BatchGetPlayListStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelKTVServer).BatchGetPlayListStatus(ctx, req.(*GetPlayListStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelKTV_MatchKTVChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MatchKTVChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelKTVServer).MatchKTVChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ktv.ChannelKTV/MatchKTVChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelKTVServer).MatchKTVChannel(ctx, req.(*MatchKTVChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelKTV_GetSpecialEventCopyWriting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSpecialEventCopyWritingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelKTVServer).GetSpecialEventCopyWriting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ktv.ChannelKTV/GetSpecialEventCopyWriting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelKTVServer).GetSpecialEventCopyWriting(ctx, req.(*GetSpecialEventCopyWritingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelKTV_GetChannelKTVSongListById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelKTVSongListByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelKTVServer).GetChannelKTVSongListById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ktv.ChannelKTV/GetChannelKTVSongListById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelKTVServer).GetChannelKTVSongListById(ctx, req.(*GetChannelKTVSongListByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelKTV_BatchGetChannelKTVInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetChannelKTVInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelKTVServer).BatchGetChannelKTVInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ktv.ChannelKTV/BatchGetChannelKTVInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelKTVServer).BatchGetChannelKTVInfo(ctx, req.(*BatchGetChannelKTVInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelKTV_QuerySong_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QuerySongReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelKTVServer).QuerySong(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ktv.ChannelKTV/QuerySong",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelKTVServer).QuerySong(ctx, req.(*QuerySongReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelKTV_ChannelKTVBurstLight_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelKTVBurstLightReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelKTVServer).ChannelKTVBurstLight(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ktv.ChannelKTV/ChannelKTVBurstLight",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelKTVServer).ChannelKTVBurstLight(ctx, req.(*ChannelKTVBurstLightReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelKTV_ChannelKTVHighFive_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelKTVHighFiveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelKTVServer).ChannelKTVHighFive(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ktv.ChannelKTV/ChannelKTVHighFive",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelKTVServer).ChannelKTVHighFive(ctx, req.(*ChannelKTVHighFiveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelKTV_ChannelKTVGuideClimaxSongList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelKTVGuideClimaxSongListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelKTVServer).ChannelKTVGuideClimaxSongList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ktv.ChannelKTV/ChannelKTVGuideClimaxSongList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelKTVServer).ChannelKTVGuideClimaxSongList(ctx, req.(*ChannelKTVGuideClimaxSongListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelKTV_ChannelKTVBatchSongClimaxInfos_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelKTVBatchSongClimaxInfosReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelKTVServer).ChannelKTVBatchSongClimaxInfos(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ktv.ChannelKTV/ChannelKTVBatchSongClimaxInfos",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelKTVServer).ChannelKTVBatchSongClimaxInfos(ctx, req.(*ChannelKTVBatchSongClimaxInfosReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelKTV_ChannelKTVSkipTheIntro_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelKTVSkipTheIntroReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelKTVServer).ChannelKTVSkipTheIntro(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ktv.ChannelKTV/ChannelKTVSkipTheIntro",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelKTVServer).ChannelKTVSkipTheIntro(ctx, req.(*ChannelKTVSkipTheIntroReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelKTV_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_ktv.ChannelKTV",
	HandlerType: (*ChannelKTVServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetChannelKTVSongList",
			Handler:    _ChannelKTV_GetChannelKTVSongList_Handler,
		},
		{
			MethodName: "GetChannelKTVHistoryList",
			Handler:    _ChannelKTV_GetChannelKTVHistoryList_Handler,
		},
		{
			MethodName: "GetChannelKTVRecommendList",
			Handler:    _ChannelKTV_GetChannelKTVRecommendList_Handler,
		},
		{
			MethodName: "GetChannelKTVPlayList",
			Handler:    _ChannelKTV_GetChannelKTVPlayList_Handler,
		},
		{
			MethodName: "GetChannelKTVGuessLikeSongList",
			Handler:    _ChannelKTV_GetChannelKTVGuessLikeSongList_Handler,
		},
		{
			MethodName: "AddChannelKTVSongToPlayList",
			Handler:    _ChannelKTV_AddChannelKTVSongToPlayList_Handler,
		},
		{
			MethodName: "MoveUpChannelKTVSong",
			Handler:    _ChannelKTV_MoveUpChannelKTVSong_Handler,
		},
		{
			MethodName: "RemoveChannelKTVSong",
			Handler:    _ChannelKTV_RemoveChannelKTVSong_Handler,
		},
		{
			MethodName: "BeginChannelKTVSing",
			Handler:    _ChannelKTV_BeginChannelKTVSing_Handler,
		},
		{
			MethodName: "GetChannelKTVInfo",
			Handler:    _ChannelKTV_GetChannelKTVInfo_Handler,
		},
		{
			MethodName: "JoinChannelKTVSing",
			Handler:    _ChannelKTV_JoinChannelKTVSing_Handler,
		},
		{
			MethodName: "QuitChannelKTVSing",
			Handler:    _ChannelKTV_QuitChannelKTVSing_Handler,
		},
		{
			MethodName: "UpdateChannelKTVScore",
			Handler:    _ChannelKTV_UpdateChannelKTVScore_Handler,
		},
		{
			MethodName: "SwitchChannelKTVBG",
			Handler:    _ChannelKTV_SwitchChannelKTVBG_Handler,
		},
		{
			MethodName: "GetChannelKTVBG",
			Handler:    _ChannelKTV_GetChannelKTVBG_Handler,
		},
		{
			MethodName: "ChannelKTVHandClap",
			Handler:    _ChannelKTV_ChannelKTVHandClap_Handler,
		},
		{
			MethodName: "EndChannelKTVSing",
			Handler:    _ChannelKTV_EndChannelKTVSing_Handler,
		},
		{
			MethodName: "ConfirmKTVHeartBeat",
			Handler:    _ChannelKTV_ConfirmKTVHeartBeat_Handler,
		},
		{
			MethodName: "LostHeart",
			Handler:    _ChannelKTV_LostHeart_Handler,
		},
		{
			MethodName: "KickOutKTVMember",
			Handler:    _ChannelKTV_KickOutKTVMember_Handler,
		},
		{
			MethodName: "CutChannelKTVSong",
			Handler:    _ChannelKTV_CutChannelKTVSong_Handler,
		},
		{
			MethodName: "ListChannelKTVSongListType",
			Handler:    _ChannelKTV_ListChannelKTVSongListType_Handler,
		},
		{
			MethodName: "BatchGetPlayListStatus",
			Handler:    _ChannelKTV_BatchGetPlayListStatus_Handler,
		},
		{
			MethodName: "MatchKTVChannel",
			Handler:    _ChannelKTV_MatchKTVChannel_Handler,
		},
		{
			MethodName: "GetSpecialEventCopyWriting",
			Handler:    _ChannelKTV_GetSpecialEventCopyWriting_Handler,
		},
		{
			MethodName: "GetChannelKTVSongListById",
			Handler:    _ChannelKTV_GetChannelKTVSongListById_Handler,
		},
		{
			MethodName: "BatchGetChannelKTVInfo",
			Handler:    _ChannelKTV_BatchGetChannelKTVInfo_Handler,
		},
		{
			MethodName: "QuerySong",
			Handler:    _ChannelKTV_QuerySong_Handler,
		},
		{
			MethodName: "ChannelKTVBurstLight",
			Handler:    _ChannelKTV_ChannelKTVBurstLight_Handler,
		},
		{
			MethodName: "ChannelKTVHighFive",
			Handler:    _ChannelKTV_ChannelKTVHighFive_Handler,
		},
		{
			MethodName: "ChannelKTVGuideClimaxSongList",
			Handler:    _ChannelKTV_ChannelKTVGuideClimaxSongList_Handler,
		},
		{
			MethodName: "ChannelKTVBatchSongClimaxInfos",
			Handler:    _ChannelKTV_ChannelKTVBatchSongClimaxInfos_Handler,
		},
		{
			MethodName: "ChannelKTVSkipTheIntro",
			Handler:    _ChannelKTV_ChannelKTVSkipTheIntro_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "channel-ktv/channel-ktv.proto",
}

func init() {
	proto.RegisterFile("channel-ktv/channel-ktv.proto", fileDescriptor_channel_ktv_270d51215214308a)
}

var fileDescriptor_channel_ktv_270d51215214308a = []byte{
	// 4381 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x3c, 0x4b, 0x6c, 0x1b, 0x49,
	0x76, 0x6a, 0x92, 0x12, 0xc9, 0x27, 0x51, 0xa2, 0xcb, 0x92, 0x4d, 0xd1, 0x3f, 0xb9, 0xe7, 0xb3,
	0xb6, 0x77, 0x57, 0xde, 0x51, 0x76, 0xe0, 0xc5, 0x04, 0x7b, 0xd0, 0xcf, 0x1e, 0x8e, 0x24, 0xca,
	0x6e, 0x52, 0x9e, 0x9d, 0xcd, 0x20, 0xbd, 0xad, 0x66, 0x99, 0xaa, 0xa8, 0xd9, 0xdd, 0xd3, 0xdd,
	0x94, 0xad, 0x4d, 0x80, 0xcd, 0x26, 0x97, 0x00, 0x39, 0x04, 0xb9, 0x04, 0xc8, 0x21, 0xc7, 0xe4,
	0x94, 0x20, 0xa7, 0x1c, 0x72, 0xca, 0x2d, 0xe7, 0xe4, 0xb6, 0xc0, 0x22, 0x97, 0x3d, 0x24, 0x40,
	0x4e, 0x39, 0x05, 0xc8, 0x2d, 0xa8, 0x57, 0xdd, 0xcd, 0xee, 0x62, 0x37, 0x29, 0x73, 0xd7, 0x46,
	0x90, 0x1b, 0xeb, 0xd5, 0xeb, 0xaa, 0x57, 0xaf, 0xea, 0xfd, 0x9f, 0x04, 0x77, 0xcc, 0x33, 0xc3,
	0xb6, 0xa9, 0xf5, 0xdd, 0xf3, 0xe0, 0xe2, 0x71, 0xe2, 0xf7, 0xa6, 0xeb, 0x39, 0x81, 0x43, 0x16,
	0x43, 0x90, 0x7e, 0x1e, 0x5c, 0xa8, 0xe7, 0xb0, 0x74, 0xd0, 0x7d, 0xb9, 0xeb, 0xb8, 0x97, 0x1e,
	0xeb, 0x9f, 0x05, 0xe4, 0x0e, 0x80, 0xef, 0xd8, 0x7d, 0xdd, 0xba, 0xf4, 0x98, 0xd9, 0x50, 0x36,
	0x94, 0x07, 0xf3, 0x5a, 0x95, 0x43, 0x0e, 0x39, 0x80, 0xa8, 0xb0, 0xe4, 0x51, 0xd3, 0xf1, 0x7a,
	0xcc, 0xee, 0x5f, 0x18, 0x56, 0xa3, 0x80, 0x08, 0x29, 0x18, 0x69, 0x40, 0x39, 0xdc, 0xa1, 0x51,
	0xc4, 0xe9, 0x68, 0xa8, 0xfe, 0x73, 0x11, 0xca, 0x07, 0xdd, 0x97, 0x1d, 0xc7, 0xee, 0x93, 0x9b,
	0x50, 0xc6, 0x8d, 0x58, 0x0f, 0x77, 0xa9, 0x6a, 0x0b, 0x7c, 0xd8, 0xea, 0x91, 0x5b, 0x80, 0xfb,
	0xe9, 0xb6, 0x31, 0xa0, 0xb8, 0x7e, 0x55, 0xab, 0x70, 0x40, 0xdb, 0x18, 0x50, 0x72, 0x0f, 0x16,
	0x7d, 0x66, 0xf7, 0xa9, 0x27, 0xa6, 0x8b, 0x38, 0x0d, 0x02, 0x84, 0x08, 0x77, 0x00, 0x0c, 0xeb,
	0x74, 0x38, 0x10, 0xf3, 0x25, 0x9c, 0xaf, 0x22, 0x04, 0xa7, 0x6f, 0x81, 0x18, 0xe8, 0x6c, 0xd0,
	0x6f, 0xcc, 0x8b, 0xc5, 0x11, 0xd0, 0x1a, 0xf4, 0x49, 0x13, 0x2a, 0xbd, 0xa1, 0x67, 0x04, 0xcc,
	0xb1, 0x1b, 0x0b, 0x48, 0x79, 0x3c, 0x26, 0x6b, 0xb0, 0xc0, 0x7c, 0xdd, 0xe8, 0xf5, 0x1a, 0xe5,
	0x0d, 0xe5, 0x41, 0x45, 0x9b, 0x67, 0xfe, 0x76, 0xaf, 0x47, 0x9e, 0x40, 0xd5, 0x8c, 0x78, 0xd7,
	0xa8, 0x6c, 0x28, 0x0f, 0x16, 0xb7, 0xd6, 0x37, 0x13, 0xfc, 0xdd, 0x4c, 0x32, 0x57, 0x1b, 0xe1,
	0x92, 0x0f, 0xa0, 0xe6, 0xd3, 0xfe, 0x80, 0xda, 0x81, 0x7e, 0x4a, 0xfb, 0xcc, 0x6e, 0x54, 0x37,
	0x94, 0x07, 0x35, 0x6d, 0x29, 0x04, 0xee, 0x70, 0x18, 0x9e, 0x36, 0x44, 0xa2, 0x76, 0xaf, 0x01,
	0x88, 0x02, 0x21, 0x68, 0xdf, 0x46, 0x5e, 0x31, 0x5f, 0x37, 0x2d, 0x36, 0x30, 0xde, 0x34, 0x16,
	0x91, 0xb0, 0x0a, 0xf3, 0x77, 0x71, 0xcc, 0x27, 0x2f, 0xa8, 0xdd, 0x73, 0x3c, 0xce, 0xe3, 0x25,
	0x71, 0x1e, 0x01, 0x68, 0xf5, 0xf8, 0xfe, 0x2e, 0x0b, 0xcc, 0x33, 0xdd, 0x38, 0x65, 0x16, 0x0b,
	0x2e, 0x1b, 0x35, 0x71, 0x93, 0x08, 0xdc, 0x16, 0x30, 0x7e, 0x47, 0x03, 0x1a, 0x18, 0xfc, 0x7b,
	0x53, 0xdc, 0x11, 0x1f, 0xb6, 0x7a, 0x2a, 0xc5, 0x7b, 0x3c, 0xf1, 0xa9, 0x47, 0xea, 0x50, 0x1c,
	0x86, 0x77, 0x58, 0xd3, 0xf8, 0x4f, 0xce, 0xc6, 0xa1, 0x4f, 0xbd, 0xe4, 0xfd, 0x45, 0x63, 0x3e,
	0x67, 0x33, 0xf3, 0x3c, 0x71, 0x79, 0xf1, 0x98, 0xaf, 0xe4, 0xd3, 0x37, 0x78, 0x67, 0xf3, 0x1a,
	0xff, 0xa9, 0x7e, 0x0d, 0x2b, 0x07, 0xdd, 0x97, 0x1a, 0x3e, 0xae, 0x0e, 0xde, 0x31, 0x79, 0x00,
	0x25, 0xbe, 0x18, 0xee, 0xb7, 0xb8, 0xb5, 0x2a, 0xf3, 0x9a, 0x93, 0xa4, 0x21, 0x06, 0x32, 0xcf,
	0x74, 0x3c, 0xaa, 0x07, 0xc3, 0x20, 0x60, 0x48, 0x09, 0x67, 0x1e, 0x07, 0x75, 0x39, 0x44, 0xfd,
	0x27, 0x05, 0xaa, 0xf1, 0xf2, 0x7c, 0x61, 0xfe, 0xca, 0xf2, 0x16, 0xe6, 0x6f, 0x56, 0x43, 0x0c,
	0x14, 0x11, 0x5c, 0xd8, 0x77, 0x2c, 0x27, 0x5c, 0xb7, 0x8a, 0x90, 0x8e, 0x63, 0x39, 0xe4, 0x11,
	0x5c, 0x0b, 0xf7, 0x75, 0x02, 0xc3, 0x0a, 0x77, 0x2f, 0x21, 0xd6, 0x8a, 0xd8, 0x9d, 0xc3, 0x91,
	0x04, 0xf2, 0xc3, 0xf8, 0x39, 0x5b, 0xcc, 0x0f, 0x1a, 0xc5, 0x8d, 0xe2, 0x83, 0xc5, 0xad, 0xdb,
	0xf2, 0xde, 0x49, 0x06, 0x44, 0x8f, 0xfd, 0x90, 0xf9, 0x81, 0xfa, 0x37, 0x0a, 0x54, 0x0e, 0xba,
	0x2f, 0x8f, 0xbd, 0x1e, 0xf5, 0xc8, 0x3a, 0x54, 0x98, 0xdd, 0xa3, 0x6f, 0x22, 0x89, 0x2a, 0x6a,
	0x65, 0x1c, 0xb7, 0x46, 0x67, 0x2b, 0x4c, 0x3d, 0x5b, 0xc4, 0xde, 0xe2, 0x54, 0xf6, 0x7e, 0x1b,
	0xe6, 0xfb, 0x96, 0xe3, 0x5d, 0xe2, 0xd1, 0x16, 0xb7, 0xd6, 0x64, 0xd4, 0x67, 0x7c, 0x52, 0x13,
	0x38, 0xea, 0xbf, 0x0b, 0x42, 0x11, 0xc6, 0xf9, 0x87, 0x50, 0x21, 0xa2, 0x42, 0xf8, 0xab, 0x08,
	0x89, 0x44, 0x54, 0x4c, 0x73, 0x11, 0x0d, 0xdf, 0x0f, 0x02, 0xb8, 0x88, 0xde, 0x83, 0x45, 0x31,
	0x69, 0x3a, 0x96, 0xe3, 0x45, 0xf2, 0x8f, 0xa0, 0x5d, 0x0e, 0x19, 0x2d, 0xee, 0x19, 0xf6, 0x79,
	0xc8, 0x76, 0xb1, 0x9e, 0x66, 0xd8, 0xe7, 0x7c, 0xda, 0xb5, 0x8c, 0xe8, 0x73, 0xa1, 0x00, 0xaa,
	0x1c, 0x12, 0x7f, 0x7d, 0xca, 0x4f, 0xe1, 0xe1, 0xe6, 0x0b, 0x62, 0x5a, 0x40, 0xf8, 0xee, 0x1b,
	0xb0, 0x24, 0x16, 0x3f, 0xed, 0x23, 0x42, 0x39, 0xb1, 0xfd, 0x4e, 0xbf, 0x35, 0xe8, 0xab, 0xbf,
	0x50, 0xa0, 0xf1, 0x8c, 0x06, 0xbb, 0x82, 0x17, 0x21, 0x6f, 0xf9, 0x5d, 0x69, 0xf4, 0x9b, 0x0c,
	0x51, 0x21, 0x50, 0x72, 0x8d, 0x3e, 0x0d, 0x1f, 0x11, 0xfe, 0xe6, 0x30, 0x9f, 0xfd, 0x54, 0x88,
	0x47, 0x4d, 0xc3, 0xdf, 0x9c, 0xae, 0x88, 0xbd, 0xac, 0x17, 0x9d, 0x2a, 0x84, 0xb4, 0x7a, 0xe4,
	0x23, 0x58, 0x36, 0x2d, 0xc6, 0xd5, 0xc4, 0x05, 0xf5, 0x7c, 0xae, 0xbe, 0x7a, 0x88, 0x52, 0x13,
	0xd0, 0x97, 0x02, 0xc8, 0x39, 0xdb, 0xa3, 0x17, 0xcc, 0xa4, 0x7c, 0x11, 0x2a, 0x38, 0x2b, 0x00,
	0xad, 0x1e, 0xe7, 0x6c, 0xb8, 0x46, 0x70, 0xe9, 0xd2, 0xc6, 0x2b, 0x21, 0x2e, 0x02, 0xd4, 0xbd,
	0x74, 0xa9, 0xda, 0x86, 0xf5, 0x9c, 0x93, 0xf9, 0x2e, 0xf9, 0x24, 0x54, 0xda, 0xf8, 0x8c, 0x15,
	0x7c, 0xc6, 0xd9, 0xcf, 0x0c, 0x55, 0x39, 0x3e, 0xde, 0x5f, 0x2a, 0x70, 0x2b, 0xb5, 0xe0, 0xe7,
	0xcc, 0x0f, 0x1c, 0xef, 0xf2, 0xff, 0x07, 0xb7, 0xbe, 0x84, 0xdb, 0xf9, 0x87, 0xf3, 0x5d, 0xf2,
	0x04, 0x16, 0x85, 0xd1, 0x4c, 0xb2, 0xec, 0x46, 0xb6, 0xe4, 0x6b, 0x20, 0x50, 0x91, 0x6d, 0x7f,
	0x52, 0x80, 0x3b, 0xa9, 0x95, 0x39, 0xce, 0x60, 0x40, 0xed, 0xde, 0x3b, 0x67, 0xdc, 0x43, 0xb8,
	0x16, 0x5f, 0x32, 0x9e, 0x9b, 0x63, 0xcd, 0xa3, 0xaa, 0x59, 0x8e, 0xae, 0x95, 0x1f, 0xfe, 0x7d,
	0xf1, 0xb8, 0x03, 0x77, 0x27, 0x71, 0x62, 0xb6, 0x67, 0xe9, 0x4b, 0x02, 0xfc, 0xdc, 0x32, 0xde,
	0xf9, 0x93, 0x54, 0x5f, 0x48, 0xb2, 0x35, 0xda, 0xd4, 0x77, 0xc9, 0xf7, 0x01, 0x1c, 0xae, 0xe1,
	0x93, 0xa7, 0x18, 0x53, 0xb7, 0x68, 0x03, 0xb4, 0x2a, 0x22, 0xe2, 0x39, 0xfe, 0x51, 0x81, 0xfb,
	0xa9, 0x35, 0x9f, 0x0d, 0xa9, 0xef, 0x1f, 0xb2, 0x73, 0x3a, 0x59, 0x25, 0xfd, 0xdf, 0x10, 0x1e,
	0x75, 0x1a, 0xe9, 0xb3, 0x5d, 0xee, 0x7f, 0x16, 0xe0, 0xee, 0x76, 0xaf, 0x97, 0x56, 0x62, 0x5d,
	0x67, 0xf2, 0x1d, 0x5f, 0xdd, 0x7a, 0xa6, 0x79, 0x57, 0x94, 0x79, 0xc7, 0x3d, 0x92, 0x33, 0x03,
	0x3d, 0x83, 0x73, 0x6a, 0x87, 0xce, 0x29, 0x20, 0xa8, 0xcb, 0x21, 0xdc, 0xbe, 0x8c, 0x04, 0x2c,
	0x94, 0x2d, 0xee, 0xb3, 0x84, 0xe4, 0xa3, 0x25, 0xaf, 0x27, 0x96, 0x10, 0x47, 0x5f, 0xd8, 0x28,
	0x3e, 0xa8, 0x6a, 0xcb, 0xa3, 0x75, 0x38, 0x6e, 0xda, 0x35, 0x2c, 0x4b, 0xae, 0xe1, 0x7b, 0xb9,
	0xc5, 0x9f, 0xc1, 0xbd, 0x89, 0xbc, 0xc6, 0x2b, 0x2c, 0x31, 0xfb, 0x95, 0x13, 0x3a, 0x5d, 0x77,
	0x52, 0xac, 0x1d, 0x7d, 0x78, 0xe2, 0xf6, 0x8c, 0x80, 0x6a, 0x88, 0x4a, 0x1e, 0xa6, 0x6e, 0x23,
	0x47, 0x0e, 0x10, 0x45, 0xa5, 0x70, 0xf3, 0xc8, 0xb9, 0xa0, 0x27, 0x6e, 0x9a, 0x86, 0xec, 0x5b,
	0x4e, 0xba, 0x4f, 0x85, 0xb4, 0xfb, 0x34, 0xf9, 0x5a, 0xd5, 0x23, 0x68, 0x64, 0x6f, 0x33, 0xd3,
	0x01, 0x39, 0xd5, 0x1a, 0x1d, 0x38, 0x17, 0xf4, 0x9d, 0x53, 0x9d, 0xbd, 0xcd, 0x6c, 0x54, 0xff,
	0x0c, 0x6e, 0x60, 0xcc, 0x92, 0x58, 0x8d, 0xe5, 0x11, 0x9d, 0x08, 0xfd, 0x0a, 0xa9, 0xd0, 0x6f,
	0x8a, 0xfc, 0xa4, 0x9e, 0x63, 0x29, 0xfd, 0x1c, 0xd5, 0x43, 0xb8, 0x99, 0x49, 0xc0, 0x6c, 0xc7,
	0xf9, 0x5b, 0x05, 0x56, 0x53, 0x2a, 0xa8, 0x65, 0xbf, 0x72, 0xae, 0xa2, 0x30, 0x0b, 0x19, 0x42,
	0x9f, 0x14, 0x93, 0xa2, 0x2c, 0x26, 0x19, 0xb2, 0x58, 0x9a, 0x2a, 0x8b, 0xf3, 0xd2, 0xe1, 0xff,
	0x5e, 0x81, 0xb5, 0x0c, 0x72, 0x67, 0x93, 0xb0, 0xef, 0xc1, 0x2a, 0xb3, 0x03, 0xea, 0x19, 0x26,
	0x8f, 0x7c, 0x75, 0xff, 0xcc, 0x79, 0xad, 0x1b, 0x96, 0x88, 0xf5, 0x2b, 0x1a, 0x49, 0xcc, 0x75,
	0xce, 0x9c, 0xd7, 0xdb, 0x96, 0x45, 0x3e, 0x81, 0x35, 0x1e, 0x13, 0xe8, 0x1e, 0x1d, 0x18, 0x8c,
	0x6b, 0xa5, 0xfe, 0x59, 0xa0, 0xdb, 0xc3, 0x41, 0x78, 0x5a, 0xc2, 0x27, 0x35, 0x9c, 0x3b, 0xe4,
	0x53, 0xed, 0xe1, 0x40, 0xfd, 0x0f, 0x05, 0xd6, 0xbe, 0x70, 0xae, 0xf6, 0x5e, 0xa6, 0x70, 0x38,
	0xf1, 0x9c, 0x8a, 0xa9, 0xe7, 0x74, 0x0f, 0x16, 0x99, 0xaf, 0xfb, 0xcc, 0xa2, 0x76, 0x60, 0x89,
	0x40, 0xa5, 0xa2, 0x01, 0xf3, 0x3b, 0x21, 0xe4, 0xfd, 0xa8, 0xc1, 0x03, 0xb8, 0x91, 0x75, 0xd0,
	0xd9, 0xde, 0xa5, 0x01, 0x6b, 0x2f, 0x86, 0x2c, 0x78, 0x87, 0x5c, 0xe3, 0xf4, 0x66, 0x6d, 0x31,
	0x1b, 0xbd, 0xff, 0xad, 0x40, 0x43, 0x00, 0x12, 0xeb, 0xf1, 0x20, 0x78, 0x26, 0x9a, 0x57, 0x61,
	0x1e, 0x23, 0xe8, 0xf0, 0x5d, 0x89, 0x41, 0xf2, 0x24, 0xa5, 0xd4, 0xfd, 0x37, 0xa0, 0xec, 0x7a,
	0xf4, 0x15, 0x35, 0x83, 0xd0, 0x92, 0x46, 0x43, 0xae, 0x36, 0x2d, 0xc3, 0x0f, 0x74, 0xcb, 0x33,
	0x31, 0xca, 0xab, 0x69, 0x65, 0x3e, 0x3e, 0xf4, 0x4c, 0x6e, 0x61, 0x99, 0xaf, 0x3b, 0x1e, 0xeb,
	0x33, 0xdb, 0xb0, 0x74, 0xb4, 0x35, 0xc2, 0x7c, 0x2e, 0x33, 0xff, 0x38, 0x04, 0x63, 0x06, 0xeb,
	0x16, 0x54, 0x07, 0xc6, 0x1b, 0x5d, 0x50, 0x54, 0xc1, 0x55, 0x2a, 0x03, 0xe3, 0x0d, 0x1e, 0x4f,
	0x7d, 0x02, 0xeb, 0x39, 0xe7, 0xf6, 0x5d, 0xd2, 0xe4, 0x5a, 0x3b, 0xa0, 0xde, 0x85, 0x61, 0x85,
	0xa7, 0x8f, 0xc7, 0xea, 0xd7, 0xb0, 0xd6, 0x79, 0xcd, 0x02, 0xf3, 0x6c, 0xf4, 0xe1, 0xce, 0xb3,
	0x99, 0xb8, 0x45, 0xa0, 0xe4, 0xb8, 0xd4, 0x46, 0x66, 0x55, 0x34, 0xfc, 0xad, 0x36, 0xe0, 0x46,
	0xd6, 0xea, 0xbe, 0xab, 0x7e, 0x0c, 0x24, 0xa5, 0x41, 0x72, 0x36, 0x55, 0x1f, 0xc2, 0xf5, 0x31,
	0x3c, 0xdf, 0x8d, 0x37, 0x53, 0x12, 0x9b, 0xfd, 0x95, 0x02, 0x6b, 0x89, 0x08, 0xc8, 0xb0, 0x7b,
	0xbb, 0x96, 0xe1, 0xce, 0x74, 0x96, 0x0e, 0xac, 0xbe, 0xf2, 0x9c, 0x81, 0x8e, 0x6a, 0x66, 0xc0,
	0x4c, 0xdd, 0x0f, 0x8c, 0x60, 0xe8, 0xe3, 0xd9, 0x96, 0xb7, 0xd4, 0xd4, 0x53, 0x8c, 0x36, 0x3a,
	0xf1, 0xa9, 0x77, 0xc4, 0xcc, 0x0e, 0x62, 0x6a, 0xd7, 0xf8, 0xf7, 0x29, 0x10, 0x67, 0x46, 0x16,
	0x79, 0xbe, 0xab, 0xfe, 0x04, 0x56, 0xf7, 0xed, 0xde, 0xbb, 0x94, 0xb2, 0x2f, 0x60, 0x2d, 0x63,
	0x87, 0xd9, 0x84, 0xec, 0x14, 0x6e, 0xec, 0x3a, 0xf6, 0x2b, 0xe6, 0x0d, 0xf8, 0x39, 0xa8, 0xe1,
	0x05, 0x3b, 0xd4, 0x08, 0x7e, 0xb3, 0xf4, 0x1e, 0xc2, 0xcd, 0xcc, 0x3d, 0x66, 0xa3, 0x78, 0x07,
	0x96, 0x0e, 0x1d, 0x3f, 0xc0, 0x75, 0x38, 0x9d, 0x5b, 0x50, 0xe5, 0xf0, 0xfc, 0x08, 0x87, 0x63,
	0xa3, 0x4d, 0xab, 0x70, 0x3c, 0xf4, 0xe5, 0x57, 0xa0, 0x96, 0x58, 0xc3, 0x77, 0xd5, 0xdf, 0x86,
	0x4a, 0x84, 0xf6, 0xd6, 0x07, 0x57, 0x4d, 0xb8, 0x7e, 0xc0, 0xcc, 0xf3, 0xe3, 0x61, 0x70, 0xd0,
	0x7d, 0x79, 0x44, 0x07, 0xa7, 0xdc, 0x62, 0xcd, 0xc0, 0xc0, 0x5b, 0x50, 0xbd, 0x60, 0x66, 0xc0,
	0x06, 0x23, 0x0f, 0xa6, 0x22, 0x00, 0xad, 0x9e, 0xda, 0x82, 0xd5, 0xf1, 0x4d, 0x66, 0xe3, 0xe0,
	0x4f, 0x60, 0x75, 0x77, 0x18, 0x5c, 0xc5, 0x45, 0xfc, 0x35, 0x5e, 0x68, 0xc6, 0x0e, 0xb3, 0x51,
	0xfb, 0x03, 0xb8, 0xc3, 0xef, 0x6c, 0x3c, 0x79, 0xc4, 0x2d, 0x24, 0x27, 0xfb, 0x26, 0x94, 0xa3,
	0x84, 0x82, 0xc8, 0x5d, 0x2e, 0x04, 0x98, 0x48, 0x50, 0x07, 0x70, 0x77, 0xd2, 0x97, 0xbe, 0x4b,
	0x0e, 0xe0, 0xba, 0x94, 0x95, 0x48, 0xbc, 0xa2, 0xdb, 0x59, 0xd1, 0x5a, 0xfc, 0x79, 0x3d, 0x99,
	0xb5, 0x88, 0x32, 0xaa, 0x2b, 0x12, 0x16, 0x59, 0x86, 0x42, 0x4c, 0x56, 0x41, 0xc4, 0xfc, 0x89,
	0xdc, 0x36, 0xfe, 0xe6, 0x96, 0x29, 0x60, 0x81, 0x15, 0x25, 0xb5, 0xc5, 0x80, 0x3f, 0x06, 0xd3,
	0xb9, 0xa0, 0x9e, 0x3e, 0xf4, 0xac, 0xc8, 0x61, 0x45, 0xc0, 0x89, 0x67, 0x71, 0x23, 0xe0, 0x3a,
	0x3e, 0xc3, 0x6a, 0x83, 0x30, 0x4f, 0xf1, 0x98, 0x6c, 0xc0, 0x62, 0x8f, 0xfa, 0xa6, 0xc7, 0x5c,
	0x9c, 0x16, 0x79, 0xc6, 0x24, 0x48, 0xfd, 0x8b, 0x02, 0x26, 0xaf, 0xdf, 0x3a, 0x2b, 0x1e, 0x9b,
	0xd0, 0x42, 0xd2, 0x84, 0x12, 0x28, 0x71, 0xef, 0x2c, 0x32, 0x15, 0xfc, 0x37, 0x5a, 0x4f, 0xcb,
	0xb8, 0x64, 0x76, 0x3f, 0xf4, 0x9c, 0xa2, 0x21, 0xb7, 0x9e, 0xa7, 0xd4, 0x0f, 0xf4, 0x61, 0x1c,
	0xa2, 0x96, 0xf9, 0xf8, 0x84, 0xf5, 0xc8, 0x77, 0x80, 0xf8, 0x2e, 0x35, 0x99, 0x61, 0xe9, 0xf4,
	0x82, 0x3b, 0x45, 0xaf, 0x2c, 0x43, 0x24, 0x52, 0x2b, 0x5a, 0x3d, 0x9c, 0xd9, 0xe7, 0x13, 0x4f,
	0x2d, 0xa3, 0xcf, 0xfd, 0x46, 0xe6, 0x23, 0x62, 0xa6, 0xc1, 0x25, 0xcc, 0xe7, 0xb8, 0x29, 0xa3,
	0x2b, 0xf9, 0x74, 0x15, 0xd9, 0xa7, 0x53, 0xff, 0x6d, 0x19, 0xea, 0xf2, 0x2b, 0x94, 0x64, 0x40,
	0x91, 0x65, 0xe0, 0x10, 0x16, 0x42, 0x7b, 0x52, 0x40, 0x7b, 0xf2, 0xfd, 0x89, 0x6f, 0x7a, 0x0c,
	0x10, 0x5a, 0x98, 0x70, 0x0d, 0x94, 0x28, 0x96, 0x96, 0x28, 0x16, 0x57, 0xb6, 0x58, 0x54, 0xd9,
	0x0a, 0x9f, 0x03, 0x07, 0x60, 0xda, 0x7b, 0x1d, 0xf0, 0x77, 0x92, 0xa9, 0x7c, 0xcc, 0x99, 0x2a,
	0x15, 0xbd, 0x16, 0xc6, 0x8a, 0x5e, 0x52, 0x62, 0xa1, 0x3c, 0x96, 0x58, 0x48, 0x56, 0xb6, 0x42,
	0x4f, 0x25, 0xae, 0x6c, 0x3d, 0x49, 0xd7, 0x20, 0xaa, 0xd9, 0x99, 0xc8, 0xf1, 0xea, 0x03, 0xf9,
	0x18, 0x56, 0x30, 0x97, 0x8e, 0x82, 0x67, 0x3a, 0x43, 0x3b, 0x08, 0x2b, 0x54, 0x35, 0x37, 0x8c,
	0xf1, 0x77, 0x39, 0x90, 0x6c, 0xc0, 0x92, 0x4d, 0xdf, 0x04, 0x7a, 0xc4, 0x94, 0x45, 0x41, 0x1e,
	0x87, 0x75, 0x04, 0x63, 0x3e, 0x84, 0xe5, 0x11, 0x06, 0x9e, 0x71, 0x09, 0x71, 0x96, 0x22, 0x1c,
	0x3c, 0xa5, 0x0a, 0xb5, 0x11, 0x16, 0x67, 0x53, 0x0d, 0x77, 0x5b, 0x8c, 0x90, 0x4e, 0x30, 0x57,
	0x53, 0x8f, 0x71, 0x22, 0x7e, 0x2d, 0xe3, 0x5a, 0xcb, 0x11, 0x5a, 0xc8, 0xb3, 0x5d, 0x28, 0xa1,
	0xc3, 0xbe, 0x82, 0x37, 0xfe, 0x78, 0xf2, 0x8d, 0x3f, 0x17, 0x2f, 0x5f, 0x8c, 0x50, 0x75, 0xe0,
	0xc7, 0xfc, 0xaa, 0x4f, 0xfb, 0x3a, 0x3a, 0x3e, 0x75, 0x7c, 0x89, 0x0b, 0xa7, 0xfd, 0x63, 0x97,
	0x62, 0xe5, 0x6e, 0x88, 0xc8, 0x7a, 0xc0, 0x06, 0xb4, 0x71, 0x0d, 0x95, 0x07, 0x08, 0x50, 0x97,
	0x0d, 0x28, 0xb9, 0x0d, 0xd5, 0x4e, 0x60, 0x78, 0x01, 0x1f, 0x34, 0x08, 0x4e, 0x8f, 0x00, 0xe4,
	0x77, 0xe1, 0x5a, 0x5a, 0x8c, 0x06, 0x86, 0xdb, 0xb8, 0x8e, 0x37, 0xb3, 0x35, 0x99, 0xd2, 0x4e,
	0x42, 0xc6, 0x8e, 0x0c, 0x77, 0xdf, 0x0e, 0xbc, 0x4b, 0x6d, 0xc5, 0x4f, 0x43, 0xc9, 0xcf, 0x15,
	0xb8, 0x9b, 0xde, 0xc0, 0x70, 0x5d, 0x6a, 0xf7, 0x84, 0x33, 0x8b, 0xbb, 0xdd, 0xc0, 0xdd, 0x7e,
	0x78, 0xf5, 0xdd, 0xb6, 0x71, 0x05, 0x74, 0x72, 0xe3, 0x8d, 0x9b, 0x7e, 0x2e, 0x42, 0xda, 0x7d,
	0x5e, 0x4b, 0xbb, 0xcf, 0x99, 0x5e, 0xf8, 0xcd, 0x4c, 0x2f, 0x3c, 0xae, 0x43, 0x35, 0xa6, 0xd7,
	0xa1, 0xf8, 0x13, 0x62, 0xbe, 0xee, 0x3a, 0xaf, 0xa9, 0x27, 0xd6, 0x5c, 0xc7, 0x35, 0x17, 0x99,
	0xff, 0x9c, 0xc3, 0x70, 0xc1, 0x4f, 0x60, 0xed, 0x74, 0xe8, 0xf1, 0xe0, 0x00, 0xc3, 0x58, 0xf4,
	0x38, 0x51, 0x32, 0x9a, 0x1b, 0x45, 0x1e, 0xcc, 0xe2, 0x24, 0xc6, 0xb1, 0x5c, 0xad, 0xa2, 0x24,
	0xdc, 0x05, 0xd0, 0x8c, 0x20, 0x7c, 0xa7, 0x8d, 0x5b, 0xe2, 0x7d, 0x8f, 0x20, 0xe9, 0x5c, 0xdc,
	0x6d, 0x29, 0x17, 0x37, 0x56, 0x09, 0xbe, 0x33, 0xbd, 0x12, 0x7c, 0x77, 0xac, 0x12, 0xfc, 0x21,
	0x2c, 0x9f, 0x19, 0x76, 0x4f, 0x37, 0x2d, 0xc3, 0x15, 0xe4, 0xde, 0x43, 0x72, 0x97, 0xce, 0x42,
	0xbf, 0x16, 0x09, 0xfd, 0x2c, 0x89, 0x85, 0x16, 0x63, 0x23, 0x3b, 0x6f, 0x8a, 0x16, 0x23, 0xfe,
	0x16, 0x0b, 0xbd, 0xa9, 0x72, 0xf2, 0xfd, 0x69, 0xe5, 0x64, 0x75, 0xbc, 0x9c, 0xdc, 0xdc, 0x81,
	0xd5, 0xac, 0xe7, 0xc9, 0x7d, 0x96, 0x73, 0x7a, 0x19, 0xf9, 0x2c, 0xe7, 0xf4, 0x92, 0x5b, 0xa9,
	0x0b, 0xc3, 0x1a, 0xc6, 0x56, 0x0a, 0x07, 0x9f, 0x15, 0x7e, 0xa0, 0x34, 0x8f, 0xe0, 0xde, 0x94,
	0x47, 0xf7, 0x36, 0xcb, 0xa9, 0x27, 0xc9, 0x10, 0x20, 0xa9, 0xcd, 0xc9, 0x22, 0x94, 0xdb, 0xc7,
	0xc7, 0xda, 0xde, 0xbe, 0x56, 0x9f, 0x23, 0x4b, 0x50, 0xd9, 0x3b, 0xfe, 0xb2, 0x7d, 0x78, 0xbc,
	0xbd, 0x57, 0x57, 0xf8, 0xd4, 0xf3, 0xc3, 0xed, 0xaf, 0x5a, 0xed, 0x67, 0xf5, 0x02, 0x29, 0x43,
	0x71, 0xbf, 0xbd, 0x57, 0x2f, 0x12, 0x80, 0x05, 0x6d, 0xbf, 0x73, 0x72, 0xd8, 0xad, 0x97, 0xd4,
	0x5f, 0x28, 0x70, 0x6d, 0x4c, 0x67, 0x70, 0x8c, 0xf6, 0xb1, 0x76, 0xb4, 0x7d, 0x58, 0x9f, 0x23,
	0x15, 0x28, 0x7d, 0x71, 0xdc, 0x6a, 0xd7, 0x15, 0xfe, 0xeb, 0xc5, 0x49, 0xab, 0x5b, 0x2f, 0xf0,
	0x5f, 0x87, 0xc7, 0x9d, 0x6e, 0xbd, 0x48, 0x56, 0x60, 0x71, 0xf7, 0xf3, 0xed, 0xf6, 0xb3, 0x7d,
	0xfd, 0x68, 0xbb, 0xd5, 0xae, 0x97, 0x12, 0x80, 0xce, 0x71, 0xfb, 0x59, 0x7d, 0x3e, 0x01, 0xd0,
	0xb6, 0xdb, 0x07, 0xf5, 0x05, 0x42, 0x60, 0xf9, 0xf9, 0xbe, 0xf6, 0x74, 0x7f, 0xb7, 0xab, 0xef,
	0xb7, 0xf7, 0x38, 0x6d, 0x65, 0x72, 0x0d, 0x6a, 0x4f, 0x5b, 0x5a, 0xa7, 0xab, 0x87, 0x33, 0xf5,
	0x4a, 0x12, 0x6d, 0xf7, 0xf3, 0x63, 0xed, 0xa4, 0x53, 0xaf, 0xf2, 0xd3, 0xed, 0x1e, 0x1f, 0x1f,
	0xea, 0x4f, 0x5b, 0xdd, 0x3a, 0x90, 0x55, 0xa8, 0x3f, 0x3d, 0xfc, 0x4a, 0xef, 0x7e, 0xbe, 0xaf,
	0x6f, 0x9f, 0xec, 0xb5, 0xf6, 0xdb, 0xbb, 0xfb, 0xf5, 0x45, 0x7e, 0xcc, 0xed, 0xdd, 0xfd, 0xfa,
	0x92, 0xfa, 0x2f, 0x0a, 0x90, 0x23, 0x23, 0x30, 0xcf, 0x0e, 0xba, 0x2f, 0x43, 0xd6, 0x65, 0xfb,
	0x9d, 0x4d, 0x88, 0xf3, 0xee, 0x21, 0xdf, 0xe3, 0x71, 0x46, 0xe2, 0xa5, 0x98, 0x95, 0x78, 0x91,
	0x72, 0x2b, 0xa5, 0xb1, 0xdc, 0x19, 0x77, 0xb0, 0x04, 0x02, 0x73, 0xa3, 0xa4, 0x98, 0x00, 0xb4,
	0x5c, 0xf2, 0x2d, 0x58, 0x89, 0xbe, 0xa6, 0xde, 0x80, 0x2b, 0x8c, 0x30, 0xd6, 0x0f, 0xf7, 0xee,
	0x86, 0x50, 0xf5, 0x47, 0x70, 0x7d, 0xec, 0x44, 0xbe, 0xcb, 0x55, 0x74, 0xec, 0x22, 0x8c, 0xfb,
	0x0c, 0x1f, 0x40, 0x2d, 0x70, 0x0c, 0x3f, 0xd0, 0x07, 0xd4, 0xf7, 0xa3, 0x12, 0x50, 0x55, 0x5b,
	0x42, 0xe0, 0x91, 0x80, 0xa9, 0x4f, 0x30, 0x8b, 0x18, 0xa5, 0xbc, 0x43, 0x3f, 0x81, 0x7e, 0x83,
	0x07, 0x8b, 0xfd, 0x11, 0x1f, 0x7d, 0x55, 0x7e, 0xb0, 0x68, 0x71, 0x5f, 0xfd, 0xa5, 0x48, 0xe8,
	0xc9, 0x5f, 0xfa, 0x2e, 0x31, 0x60, 0x45, 0xf8, 0x19, 0xd4, 0x3f, 0x32, 0x5c, 0x97, 0x61, 0xcb,
	0x02, 0x97, 0xe1, 0x27, 0x29, 0x19, 0xce, 0xfc, 0x78, 0xb3, 0x93, 0xfe, 0x32, 0xb2, 0x0e, 0x69,
	0x68, 0x53, 0x87, 0xd5, 0x2c, 0xc4, 0x0c, 0xc1, 0xfa, 0x24, 0x29, 0x58, 0x8b, 0x5b, 0xb7, 0x52,
	0x24, 0x48, 0xfb, 0x27, 0xa4, 0xee, 0x5f, 0x15, 0x58, 0x4e, 0xcf, 0x62, 0x46, 0x86, 0xfb, 0x11,
	0x56, 0x98, 0x43, 0xa8, 0x69, 0x65, 0x3e, 0x3e, 0xa4, 0x36, 0xe9, 0x42, 0xdd, 0x1c, 0x7a, 0x7a,
	0xe8, 0x7d, 0xea, 0x89, 0xec, 0xff, 0xa3, 0x09, 0xfb, 0x6d, 0xee, 0x0e, 0xbd, 0x50, 0x04, 0x31,
	0x72, 0x59, 0x36, 0x53, 0xe3, 0x66, 0x1b, 0x96, 0xd3, 0x18, 0xe9, 0xc6, 0x23, 0x65, 0x72, 0xe3,
	0x51, 0x41, 0xf6, 0xc1, 0xd4, 0x7b, 0x58, 0x96, 0x4d, 0xea, 0xa6, 0x5d, 0xc7, 0xbd, 0xfc, 0xd2,
	0x63, 0x81, 0xc8, 0x1d, 0xa8, 0x3f, 0x2f, 0x60, 0xb9, 0x32, 0x17, 0xc3, 0x77, 0xc9, 0x9f, 0x8d,
	0x99, 0x65, 0xd3, 0x71, 0x2f, 0xf5, 0xd7, 0x02, 0x05, 0xcd, 0xb2, 0xb8, 0xeb, 0x03, 0xf9, 0xae,
	0x27, 0xac, 0xba, 0x99, 0x33, 0x97, 0x6d, 0xa4, 0xd3, 0x08, 0xb2, 0xba, 0xcd, 0xf8, 0x7c, 0x9a,
	0xba, 0xad, 0x26, 0x2f, 0xfe, 0xaf, 0x15, 0xa9, 0x2c, 0x1e, 0x05, 0x5a, 0x3b, 0x97, 0xad, 0x1e,
	0x17, 0x0c, 0xb9, 0x02, 0xa6, 0x8c, 0x55, 0xc0, 0xde, 0x4b, 0xce, 0x56, 0x93, 0x6a, 0xec, 0x69,
	0x32, 0x67, 0xab, 0x3d, 0xfe, 0xa9, 0x02, 0xeb, 0x3b, 0x5c, 0xcd, 0x64, 0xd6, 0x15, 0x3e, 0x86,
	0x95, 0x91, 0x46, 0x18, 0x2d, 0xcb, 0xcf, 0x15, 0x69, 0x05, 0xd4, 0x9c, 0x12, 0xe9, 0x85, 0x2b,
	0x94, 0x13, 0xb2, 0x54, 0xab, 0xfa, 0x23, 0x68, 0xe6, 0x11, 0xe3, 0xbb, 0xe4, 0xb3, 0xf1, 0x7c,
	0xcc, 0x94, 0x38, 0x7f, 0x94, 0x97, 0xf9, 0x2f, 0x05, 0x1a, 0xe3, 0x95, 0xec, 0x30, 0x10, 0x9b,
	0xac, 0x53, 0x43, 0x23, 0x52, 0x18, 0x19, 0x91, 0xa8, 0xda, 0x57, 0x9c, 0x54, 0xf5, 0x16, 0xc5,
	0xd7, 0x17, 0x23, 0x97, 0x3b, 0x32, 0x16, 0xcb, 0x5b, 0xdf, 0xcb, 0xa1, 0x3a, 0x4d, 0xd6, 0x66,
	0xc2, 0xb1, 0x8f, 0x9c, 0x74, 0xfe, 0x0c, 0x3e, 0x06, 0x48, 0x98, 0x6f, 0x6e, 0x02, 0xf7, 0xf6,
	0xea, 0x73, 0x64, 0x01, 0x0a, 0x27, 0xcf, 0xeb, 0x0a, 0x07, 0xec, 0xed, 0x1f, 0xd6, 0x0b, 0x6a,
	0x1b, 0x96, 0x5e, 0x0c, 0xa9, 0x77, 0x19, 0x25, 0x61, 0x1a, 0x50, 0x3e, 0xa7, 0x97, 0xaf, 0x1d,
	0x2f, 0xea, 0x6d, 0x8c, 0x86, 0x99, 0xfd, 0x02, 0xe1, 0xa9, 0x8b, 0xa3, 0x1c, 0xeb, 0x9f, 0x97,
	0xa1, 0x96, 0x58, 0x50, 0xa4, 0x57, 0x4d, 0xa7, 0x47, 0x43, 0x96, 0xe1, 0x6f, 0xbe, 0x4b, 0xda,
	0xf6, 0x44, 0x43, 0xf2, 0x29, 0xcc, 0x73, 0x96, 0xf8, 0x21, 0xdb, 0xee, 0xa5, 0x98, 0x90, 0x5a,
	0x78, 0x13, 0x7f, 0x08, 0xec, 0xe6, 0xaf, 0x4a, 0x50, 0xfa, 0x35, 0x7a, 0x33, 0xc3, 0xf0, 0x96,
	0x7a, 0xa3, 0xc8, 0xb7, 0x22, 0x00, 0xad, 0xb1, 0x18, 0xb6, 0x94, 0xd5, 0xb8, 0x19, 0x7d, 0x1d,
	0xb7, 0x66, 0x86, 0xeb, 0xb5, 0x06, 0x98, 0x73, 0x08, 0x1b, 0x37, 0x7b, 0x61, 0x00, 0x5c, 0x16,
	0x7d, 0x9b, 0x3d, 0xa9, 0xe5, 0xb3, 0x3c, 0xb1, 0xe5, 0xb3, 0x22, 0xb5, 0x7c, 0x7e, 0x08, 0xcb,
	0xf1, 0xa4, 0x3e, 0x60, 0x36, 0xc3, 0x3e, 0xcc, 0xaa, 0xb6, 0x14, 0x61, 0x1c, 0x31, 0x9b, 0x71,
	0xe1, 0x1c, 0x61, 0xf9, 0x03, 0xc3, 0xb2, 0x30, 0xd2, 0xad, 0x6a, 0xb5, 0x08, 0xad, 0xc3, 0x81,
	0x3c, 0x6a, 0x49, 0xac, 0x46, 0x7b, 0x6c, 0x38, 0x08, 0xa3, 0xdd, 0xe5, 0x78, 0x3d, 0x84, 0x92,
	0x9d, 0x64, 0xdf, 0xe8, 0x12, 0x1a, 0xb3, 0x0f, 0x27, 0xdc, 0x52, 0x66, 0x0b, 0x69, 0xca, 0x21,
	0xaf, 0x4d, 0x73, 0xc8, 0x97, 0x33, 0xfa, 0x3b, 0x1f, 0xc1, 0xb5, 0x33, 0xc3, 0xd7, 0xfd, 0x33,
	0xc7, 0x0b, 0xf4, 0x30, 0x98, 0xc0, 0x80, 0x78, 0x5e, 0x5b, 0x39, 0x33, 0xfc, 0x0e, 0x87, 0x77,
	0x04, 0x98, 0x3c, 0x45, 0xc5, 0xc3, 0x23, 0x36, 0x4c, 0xfe, 0xd5, 0x91, 0xe6, 0x8f, 0x26, 0xd1,
	0x8c, 0xd8, 0xa8, 0x52, 0xc0, 0x8c, 0x7f, 0x37, 0x4f, 0xa1, 0x3a, 0xa9, 0xdb, 0xb8, 0x96, 0xec,
	0x36, 0xbe, 0x0d, 0xd5, 0xb8, 0xb3, 0x38, 0xca, 0x5c, 0xc6, 0x00, 0xb9, 0xcf, 0xb8, 0x16, 0xf7,
	0x19, 0x37, 0x35, 0x80, 0xd1, 0xee, 0xe3, 0x01, 0x96, 0x32, 0x3d, 0xc0, 0x2a, 0xc8, 0x01, 0x96,
	0xda, 0x87, 0x9b, 0x89, 0xa2, 0x47, 0x1c, 0x03, 0xce, 0x9a, 0x24, 0x1e, 0x19, 0xa7, 0xa2, 0x54,
	0xcb, 0x6d, 0x26, 0xd5, 0x67, 0x72, 0x23, 0xdf, 0x55, 0xcd, 0x54, 0x41, 0x85, 0xf5, 0xcf, 0x9e,
	0xb2, 0x8b, 0xd9, 0x4a, 0x69, 0xeb, 0x50, 0x19, 0x46, 0x86, 0xa6, 0x88, 0x86, 0xa6, 0x3c, 0x64,
	0xa2, 0xc3, 0x2c, 0x5d, 0x16, 0x89, 0x37, 0xf1, 0x5d, 0xf5, 0x77, 0x60, 0xe9, 0xc7, 0xb4, 0xef,
	0x44, 0xd6, 0x50, 0xce, 0xda, 0xd6, 0xa2, 0xac, 0x2d, 0xd9, 0x4a, 0x9a, 0xc7, 0x42, 0x86, 0xee,
	0x8e, 0x96, 0x49, 0xd8, 0xc7, 0xbf, 0x2b, 0x42, 0x25, 0x02, 0x4b, 0xda, 0x40, 0x91, 0xb5, 0x41,
	0x4a, 0xa6, 0x0b, 0x53, 0x65, 0xba, 0x78, 0x35, 0x99, 0x2e, 0x5d, 0x55, 0xa6, 0xe7, 0x33, 0x65,
	0x3a, 0xa1, 0x35, 0x17, 0xf2, 0xb5, 0x66, 0x79, 0xb2, 0x63, 0x59, 0x99, 0xd2, 0xd1, 0x5e, 0x95,
	0xd5, 0xdb, 0xa7, 0x00, 0xe8, 0x24, 0x0a, 0x55, 0x02, 0x28, 0x96, 0xe9, 0xec, 0x9d, 0xa4, 0x3c,
	0xb4, 0x71, 0xe5, 0xb1, 0x38, 0x4d, 0x79, 0x2c, 0x8d, 0x2b, 0x0f, 0x75, 0x8a, 0x20, 0xcf, 0x4f,
	0x14, 0xe4, 0xf9, 0x09, 0x82, 0x9c, 0xf8, 0x83, 0x81, 0xbf, 0x54, 0x60, 0x23, 0xd9, 0x06, 0xc6,
	0x7a, 0x54, 0x08, 0xf6, 0xe4, 0x1e, 0xb6, 0xf7, 0xe2, 0x23, 0xbe, 0x84, 0xfb, 0x53, 0x48, 0x9b,
	0xcd, 0x4f, 0xfc, 0x07, 0x25, 0xb9, 0x30, 0x3a, 0x69, 0x1c, 0x63, 0xa4, 0xd0, 0xfc, 0xdc, 0x56,
	0xa0, 0xf0, 0xf9, 0xf9, 0x28, 0x72, 0x55, 0xad, 0x2c, 0xde, 0x9f, 0xff, 0x7e, 0xf8, 0xf1, 0xab,
	0x02, 0xa8, 0xd3, 0xe8, 0xf6, 0x5d, 0xf2, 0x7b, 0x18, 0x95, 0x47, 0x76, 0x24, 0x11, 0xd3, 0xec,
	0xe4, 0xb8, 0x6a, 0x79, 0x2b, 0x25, 0x0c, 0x4c, 0x1c, 0xca, 0xd4, 0xcc, 0x24, 0xac, 0xf9, 0x07,
	0x40, 0xc6, 0x91, 0x92, 0x01, 0x4b, 0x55, 0x04, 0x2c, 0xcf, 0xd3, 0x61, 0xec, 0x67, 0xb3, 0x53,
	0x92, 0x4c, 0x55, 0xbd, 0x0b, 0x2b, 0xf4, 0x47, 0x0a, 0xac, 0x27, 0xc2, 0x92, 0x73, 0xe6, 0x76,
	0xcf, 0x68, 0xcb, 0x0e, 0x3c, 0xe7, 0x37, 0xdb, 0x3a, 0xc3, 0x55, 0xd6, 0x39, 0x73, 0x45, 0xf6,
	0xba, 0x14, 0x66, 0x6f, 0xf8, 0x6e, 0x6c, 0x40, 0xd5, 0xdb, 0xd0, 0xcc, 0xa3, 0xc1, 0x77, 0x1f,
	0xfd, 0x3e, 0xac, 0x65, 0x56, 0xe0, 0xc9, 0x06, 0xdc, 0xce, 0x9c, 0xd0, 0x4f, 0xda, 0x07, 0xed,
	0xe3, 0x2f, 0xeb, 0x73, 0xf9, 0x18, 0xc7, 0x6d, 0xfd, 0xa8, 0xb5, 0x5b, 0x57, 0xc8, 0x5d, 0x68,
	0x66, 0x63, 0x1c, 0xb5, 0x3a, 0x9d, 0x7a, 0x61, 0xeb, 0x7f, 0x9a, 0x00, 0x23, 0xda, 0xc8, 0x99,
	0xd4, 0x16, 0x15, 0x5b, 0xae, 0x8f, 0xe4, 0x00, 0x3a, 0xb3, 0x63, 0xbf, 0xf9, 0xf1, 0x55, 0xd0,
	0x7c, 0x57, 0x9d, 0x23, 0xdf, 0x48, 0x6d, 0xc3, 0x89, 0x7e, 0x6f, 0xf2, 0x20, 0x7f, 0x95, 0x74,
	0xcf, 0x7b, 0xf3, 0xe1, 0x15, 0x31, 0x71, 0xcb, 0xd7, 0xd0, 0xcc, 0x6f, 0x7f, 0x26, 0x8f, 0xf2,
	0x97, 0x92, 0x3b, 0xc6, 0x9b, 0xdf, 0xbe, 0x32, 0x2e, 0x6e, 0x2c, 0x73, 0x35, 0x0a, 0xa6, 0x26,
	0x71, 0x35, 0xd1, 0x62, 0x3b, 0x89, 0xab, 0xc9, 0xee, 0x50, 0x75, 0x8e, 0xfc, 0xb1, 0x22, 0xb5,
	0x78, 0x8f, 0x75, 0x02, 0x93, 0xcd, 0xfc, 0xc5, 0xb2, 0x3a, 0x9e, 0x9b, 0x8f, 0xdf, 0x0a, 0x1f,
	0xa9, 0xf8, 0x29, 0xdc, 0x9a, 0xd0, 0xc8, 0x4a, 0xd2, 0xdc, 0x9b, 0xdc, 0x5e, 0xdc, 0xfc, 0xce,
	0xd5, 0x91, 0x71, 0x6f, 0x0a, 0xab, 0x59, 0xcd, 0xa5, 0x24, 0x1d, 0x2d, 0xe4, 0xb4, 0xb9, 0x36,
	0x3f, 0xba, 0x02, 0x56, 0xb4, 0x4d, 0x56, 0x37, 0xa8, 0xb4, 0x4d, 0x4e, 0x5f, 0xaa, 0xb4, 0x4d,
	0x5e, 0x5b, 0xa9, 0x3a, 0x47, 0x4e, 0xe1, 0x7a, 0x46, 0x93, 0x26, 0xf9, 0x20, 0xf5, 0x7d, 0x76,
	0x1f, 0x69, 0xf3, 0xc3, 0xe9, 0x48, 0xb8, 0xc7, 0xd7, 0x70, 0x6d, 0x2c, 0xa9, 0x41, 0xee, 0xe7,
	0xdf, 0x7a, 0x98, 0x81, 0x69, 0xaa, 0xd3, 0x50, 0x70, 0x75, 0x1d, 0xc8, 0x78, 0x37, 0x1f, 0x49,
	0x7f, 0x9b, 0xd9, 0xd7, 0xd8, 0xfc, 0x60, 0x2a, 0x4e, 0xb4, 0xc1, 0x78, 0xfb, 0x9d, 0xb4, 0x41,
	0x66, 0x0b, 0xa0, 0xb4, 0x41, 0x76, 0x0f, 0x9f, 0x90, 0xde, 0xcc, 0xce, 0x34, 0x49, 0x7a, 0xf3,
	0xba, 0xf6, 0x24, 0xe9, 0xcd, 0x6d, 0x72, 0x13, 0x47, 0x19, 0x6f, 0x36, 0x93, 0x8e, 0x92, 0xd9,
	0xeb, 0x26, 0x1d, 0x25, 0xa7, 0x63, 0x6d, 0x8e, 0xbc, 0x84, 0x15, 0xa9, 0x17, 0x8d, 0xdc, 0xcb,
	0xbf, 0x45, 0xb1, 0xf4, 0xc6, 0x64, 0x84, 0x88, 0xf0, 0xf1, 0xc6, 0x30, 0x89, 0xf0, 0xcc, 0xc6,
	0x36, 0x89, 0xf0, 0x9c, 0xee, 0x32, 0x7c, 0xa3, 0x63, 0xdd, 0x5f, 0xd2, 0x1b, 0xcd, 0xea, 0x3f,
	0x93, 0xde, 0x68, 0x66, 0x03, 0x99, 0x90, 0xb2, 0x8c, 0x5e, 0x2d, 0x49, 0xca, 0xb2, 0x3b, 0xc6,
	0x24, 0x29, 0xcb, 0x69, 0xf9, 0x52, 0xe7, 0xc8, 0x1e, 0x54, 0xe3, 0xee, 0x2b, 0xb2, 0x3e, 0xd6,
	0xab, 0x15, 0x75, 0x76, 0x35, 0x9b, 0x79, 0x53, 0xb8, 0xca, 0x57, 0x50, 0x97, 0x1b, 0xa2, 0x48,
	0xfa, 0x82, 0x32, 0x9a, 0xb2, 0x9a, 0xf7, 0xa7, 0x60, 0x44, 0x2c, 0x1e, 0x6b, 0x5f, 0x92, 0x58,
	0x9c, 0xd5, 0x40, 0x25, 0xb1, 0x38, 0xb3, 0x03, 0x4a, 0xd8, 0xde, 0xfc, 0xb6, 0x24, 0xc9, 0xf6,
	0x4e, 0xec, 0x7c, 0x92, 0x6c, 0xef, 0xe4, 0x5e, 0x27, 0x75, 0x8e, 0x18, 0x70, 0x23, 0xca, 0xdb,
	0x4a, 0x15, 0x94, 0xfb, 0xd3, 0xea, 0x3f, 0x19, 0x2a, 0x6e, 0xbc, 0x44, 0x24, 0xa4, 0x4a, 0x2a,
	0x87, 0x49, 0x52, 0x35, 0x5e, 0xfe, 0x93, 0xa4, 0x2a, 0xa3, 0x9a, 0x16, 0xfb, 0x2b, 0x39, 0xe5,
	0x84, 0x71, 0x7f, 0x25, 0xbf, 0x94, 0x32, 0xee, 0xaf, 0x4c, 0x28, 0x7f, 0xa8, 0x73, 0x24, 0xc8,
	0xf9, 0xcb, 0xc5, 0x9d, 0xcb, 0x56, 0x8f, 0x3c, 0x9c, 0xee, 0xe2, 0x85, 0xc5, 0x89, 0xe6, 0xa3,
	0xab, 0xa2, 0xe2, 0xae, 0xe7, 0xa3, 0x9b, 0x92, 0x8c, 0x51, 0x5a, 0x83, 0xe6, 0xd6, 0x04, 0x9a,
	0xdf, 0xba, 0x12, 0x5e, 0x24, 0x8e, 0x71, 0xf6, 0x4d, 0x12, 0xc7, 0x64, 0x66, 0x5a, 0x12, 0xc7,
	0x54, 0xc2, 0x4e, 0x78, 0x01, 0x59, 0xa9, 0x27, 0xc9, 0x0b, 0xc8, 0x49, 0x83, 0x49, 0x5e, 0x40,
	0x6e, 0x0e, 0x4b, 0x56, 0xaf, 0x61, 0x82, 0x29, 0x5f, 0xbd, 0x8e, 0xd2, 0x5c, 0xf9, 0xea, 0x35,
	0x99, 0xa5, 0x9a, 0x23, 0x7f, 0xa8, 0xc0, 0x9d, 0x89, 0xb1, 0x39, 0xf9, 0x6e, 0xce, 0x42, 0xd9,
	0x29, 0x86, 0xe6, 0xe6, 0xdb, 0xa0, 0xc7, 0x9e, 0xeb, 0xe4, 0xc8, 0x91, 0x6c, 0xbe, 0x55, 0x98,
	0x29, 0x7b, 0xae, 0xd3, 0xc3, 0x52, 0xf1, 0x06, 0xb3, 0x23, 0x35, 0xe9, 0x0d, 0xe6, 0x86, 0x94,
	0xd2, 0x1b, 0xcc, 0x0f, 0xfb, 0xd4, 0xb9, 0x9d, 0xc7, 0x3f, 0xfe, 0x6e, 0xdf, 0xb1, 0x0c, 0xbb,
	0xbf, 0xf9, 0xe9, 0x56, 0x10, 0x6c, 0x9a, 0xce, 0xe0, 0x31, 0xfe, 0xc3, 0x09, 0xd3, 0xb1, 0x1e,
	0xfb, 0xd4, 0xbb, 0x60, 0x26, 0xf5, 0x93, 0xff, 0x8e, 0xe2, 0x74, 0x01, 0xa7, 0x7f, 0xeb, 0x7f,
	0x03, 0x00, 0x00, 0xff, 0xff, 0xcd, 0xd6, 0xd0, 0xe8, 0xb0, 0x42, 0x00, 0x00,
}
