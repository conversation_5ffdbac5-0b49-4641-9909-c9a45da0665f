// Code generated by protoc-gen-gogo.
// source: service/appsvr/src/backpacksvr/backpack.proto
// DO NOT EDIT!

/*
	Package backpack is a generated protocol buffer package.

	namespace

	It is generated from these files:
		service/appsvr/src/backpacksvr/backpack.proto

	It has these top-level messages:
		PackageCfg
		PackageItemCfg
		FuncCardCfg
		LotteryFragmentCfg
		UserBackpackItem
		UserBackpackLog
		AddPackageCfgReq
		AddPackageCfgResp
		DelPackageCfgReq
		DelPackageCfgResp
		GetPackageCfgReq
		GetPackageCfgResp
		AddPackageItemCfgReq
		AddPackageItemCfgResp
		ModPackageItemCfgReq
		ModPackageItemCfgResp
		DelPackageItemCfgReq
		DelPackageItemCfgResp
		GetPackageItemCfgReq
		GetPackageItemCfgResp
		GiveUserPackageReq
		GiveUserPackageResp
		UseBackpackExtraInfo
		UseBackpackItemReq
		UseBackpackItemResp
		GetUseItemOrderInfoReq
		GetUseItemOrderInfoResp
		ProcBackpackItemTimeoutReq
		ProcBackpackItemTimeoutResp
		GetUserBackpackReq
		GetUserBackpackResp
		GetUserFuncCardUseReq
		GetUserFuncCardUseResp
		AddFuncCardCfgReq
		AddFuncCardCfgResp
		BatchGetUserFuncCardUseReq
		BatchGetUserFuncCardUseResp
		DelFuncCardCfgReq
		DelFuncCardCfgResp
		GetFuncCardCfgReq
		GetFuncCardCfgResp
		CheckUserFuncCardUseReq
		CheckUserFuncCardUseResp
		SetUserFuncCardUseReq
		SetUserFuncCardUseResp
		GetUserBackpackLogReq
		GetUserBackpackLogResp
		AddItemCfgReq
		AddItemCfgResp
		DelItemCfgReq
		DelItemCfgResp
		GetItemCfgReq
		GetItemCfgResp
		UseItemInfo
		TransactionInfo
		FreeZeItemReq
		FreeZeItemResp
		UserPackageSum
		GetUserPackageSumReq
		GetUserPackageSumResp
		GetOrderCountByTimeRangeReq
		GetOrderCountByTimeRangeResp
		GetOrderListByTimeRangeReq
		GetOrderListByTimeRangeResp
		TransformOpt
		ConversionItemReq
		ConversionItemResp
		RollBackUserItemReq
		RollBackUserItemResp
		DeductItem
		DeductDetail
		BatchDeductUserItemReq
		DeductResult
		BatchDeductUserItemResp
		UseOrderDetail
		TimeRangeReq
		CountResp
		OrderIdsResp
*/
package backpack

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

import io1 "io"
import fmt2 "fmt"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type LogType int32

const (
	LogType_LOG_TYPE_INVALID                LogType = 0
	LogType_LOG_TYPE_USE                    LogType = 1
	LogType_LOG_TYPE_EXPIRE                 LogType = 2
	LogType_LOG_TYPE_FRAGMENT_EXCHANGE      LogType = 3
	LogType_LOG_TYPE_FRAGMENT_ROLLBACK      LogType = 4
	LogType_LOG_TYPE_ITEM_CONVERSION        LogType = 5
	LogType_LOG_TYPE_ROLLBACK               LogType = 6
	LogType_LOG_TYPE_OPRATE_DEDUCT          LogType = 7
	LogType_LOG_TYPE_BUSINESS_DEDUCT        LogType = 8
	LogType_LOG_TYPE_ENERGY_ITEM_CONVERSION LogType = 9
)

var LogType_name = map[int32]string{
	0: "LOG_TYPE_INVALID",
	1: "LOG_TYPE_USE",
	2: "LOG_TYPE_EXPIRE",
	3: "LOG_TYPE_FRAGMENT_EXCHANGE",
	4: "LOG_TYPE_FRAGMENT_ROLLBACK",
	5: "LOG_TYPE_ITEM_CONVERSION",
	6: "LOG_TYPE_ROLLBACK",
	7: "LOG_TYPE_OPRATE_DEDUCT",
	8: "LOG_TYPE_BUSINESS_DEDUCT",
	9: "LOG_TYPE_ENERGY_ITEM_CONVERSION",
}
var LogType_value = map[string]int32{
	"LOG_TYPE_INVALID":                0,
	"LOG_TYPE_USE":                    1,
	"LOG_TYPE_EXPIRE":                 2,
	"LOG_TYPE_FRAGMENT_EXCHANGE":      3,
	"LOG_TYPE_FRAGMENT_ROLLBACK":      4,
	"LOG_TYPE_ITEM_CONVERSION":        5,
	"LOG_TYPE_ROLLBACK":               6,
	"LOG_TYPE_OPRATE_DEDUCT":          7,
	"LOG_TYPE_BUSINESS_DEDUCT":        8,
	"LOG_TYPE_ENERGY_ITEM_CONVERSION": 9,
}

func (x LogType) String() string {
	return proto.EnumName(LogType_name, int32(x))
}
func (LogType) EnumDescriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{0} }

type PackageItemType int32

const (
	PackageItemType_UNKNOW_ITEM_TYPE          PackageItemType = 0
	PackageItemType_BACKPACK_PRESENT          PackageItemType = 1
	PackageItemType_BACKPACK_CARD_RICH_EXP    PackageItemType = 2
	PackageItemType_BACKPACK_CARD_CHARM_EXP   PackageItemType = 3
	PackageItemType_BACKPACK_LOTTERY_FRAGMENT PackageItemType = 4
)

var PackageItemType_name = map[int32]string{
	0: "UNKNOW_ITEM_TYPE",
	1: "BACKPACK_PRESENT",
	2: "BACKPACK_CARD_RICH_EXP",
	3: "BACKPACK_CARD_CHARM_EXP",
	4: "BACKPACK_LOTTERY_FRAGMENT",
}
var PackageItemType_value = map[string]int32{
	"UNKNOW_ITEM_TYPE":          0,
	"BACKPACK_PRESENT":          1,
	"BACKPACK_CARD_RICH_EXP":    2,
	"BACKPACK_CARD_CHARM_EXP":   3,
	"BACKPACK_LOTTERY_FRAGMENT": 4,
}

func (x PackageItemType) String() string {
	return proto.EnumName(PackageItemType_name, int32(x))
}
func (PackageItemType) EnumDescriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{1} }

// 包裹来源
type PackageSourceType int32

const (
	PackageSourceType_UNKNOW_PACKAGE_SOURCE                       PackageSourceType = 0
	PackageSourceType_PACKAGE_SOURCE_ACTIVITY_PRESENT             PackageSourceType = 1
	PackageSourceType_PACKAGE_SOURCE_DAILY_CHECKIN                PackageSourceType = 2
	PackageSourceType_PACKAGE_SOURCE_FIRST_RECHARGE               PackageSourceType = 3
	PackageSourceType_PACKAGE_SOURCE_OFFICIAL                     PackageSourceType = 4
	PackageSourceType_PACKAGE_SOURCE_SMASHEGG                     PackageSourceType = 5
	PackageSourceType_PACKAGE_SOURCE_CONVERSION                   PackageSourceType = 6
	PackageSourceType_PACKAGE_SOURCE_AWARD_CENTER                 PackageSourceType = 7
	PackageSourceType_PACKAGE_SOURCE_AWARD_YUYIN_LIVE_MISSION     PackageSourceType = 8
	PackageSourceType_PACKAGE_SOURCE_INTERACTION_INTIMACY_MISSION PackageSourceType = 9
	PackageSourceType_PACKAGE_SOURCE_ITEM_CONVERSION              PackageSourceType = 10
	PackageSourceType_PACKAGE_SOURCE_NOBILITY                     PackageSourceType = 11
	PackageSourceType_PACKAGE_SOURCE_TBEAN_BUY                    PackageSourceType = 12
	PackageSourceType_PACKAGE_SOURCE_VIP_ACTIVITY                 PackageSourceType = 15
	PackageSourceType_PACKAGE_SOURCE_ENERGY_STONE_CONVERSION      PackageSourceType = 16
)

var PackageSourceType_name = map[int32]string{
	0:  "UNKNOW_PACKAGE_SOURCE",
	1:  "PACKAGE_SOURCE_ACTIVITY_PRESENT",
	2:  "PACKAGE_SOURCE_DAILY_CHECKIN",
	3:  "PACKAGE_SOURCE_FIRST_RECHARGE",
	4:  "PACKAGE_SOURCE_OFFICIAL",
	5:  "PACKAGE_SOURCE_SMASHEGG",
	6:  "PACKAGE_SOURCE_CONVERSION",
	7:  "PACKAGE_SOURCE_AWARD_CENTER",
	8:  "PACKAGE_SOURCE_AWARD_YUYIN_LIVE_MISSION",
	9:  "PACKAGE_SOURCE_INTERACTION_INTIMACY_MISSION",
	10: "PACKAGE_SOURCE_ITEM_CONVERSION",
	11: "PACKAGE_SOURCE_NOBILITY",
	12: "PACKAGE_SOURCE_TBEAN_BUY",
	15: "PACKAGE_SOURCE_VIP_ACTIVITY",
	16: "PACKAGE_SOURCE_ENERGY_STONE_CONVERSION",
}
var PackageSourceType_value = map[string]int32{
	"UNKNOW_PACKAGE_SOURCE":                       0,
	"PACKAGE_SOURCE_ACTIVITY_PRESENT":             1,
	"PACKAGE_SOURCE_DAILY_CHECKIN":                2,
	"PACKAGE_SOURCE_FIRST_RECHARGE":               3,
	"PACKAGE_SOURCE_OFFICIAL":                     4,
	"PACKAGE_SOURCE_SMASHEGG":                     5,
	"PACKAGE_SOURCE_CONVERSION":                   6,
	"PACKAGE_SOURCE_AWARD_CENTER":                 7,
	"PACKAGE_SOURCE_AWARD_YUYIN_LIVE_MISSION":     8,
	"PACKAGE_SOURCE_INTERACTION_INTIMACY_MISSION": 9,
	"PACKAGE_SOURCE_ITEM_CONVERSION":              10,
	"PACKAGE_SOURCE_NOBILITY":                     11,
	"PACKAGE_SOURCE_TBEAN_BUY":                    12,
	"PACKAGE_SOURCE_VIP_ACTIVITY":                 15,
	"PACKAGE_SOURCE_ENERGY_STONE_CONVERSION":      16,
}

func (x PackageSourceType) String() string {
	return proto.EnumName(PackageSourceType_name, int32(x))
}
func (PackageSourceType) EnumDescriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{2} }

type FREEZETYPE int32

const (
	FREEZETYPE_FREEZETYPE_UNVALID  FREEZETYPE = 0
	FREEZETYPE_FREEZETYPE_PREPARE  FREEZETYPE = 1
	FREEZETYPE_FREEZETYPE_COMMIT   FREEZETYPE = 2
	FREEZETYPE_FREEZETYPE_ROLLBACK FREEZETYPE = 3
)

var FREEZETYPE_name = map[int32]string{
	0: "FREEZETYPE_UNVALID",
	1: "FREEZETYPE_PREPARE",
	2: "FREEZETYPE_COMMIT",
	3: "FREEZETYPE_ROLLBACK",
}
var FREEZETYPE_value = map[string]int32{
	"FREEZETYPE_UNVALID":  0,
	"FREEZETYPE_PREPARE":  1,
	"FREEZETYPE_COMMIT":   2,
	"FREEZETYPE_ROLLBACK": 3,
}

func (x FREEZETYPE) String() string {
	return proto.EnumName(FREEZETYPE_name, int32(x))
}
func (FREEZETYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{3} }

type DeductType int32

const (
	DeductType_UNKNOW_DEDUCT_TYPE DeductType = 0
	DeductType_OPRATE_DEDUCT      DeductType = 1
	DeductType_BUSINESS_DEDUCT    DeductType = 2
)

var DeductType_name = map[int32]string{
	0: "UNKNOW_DEDUCT_TYPE",
	1: "OPRATE_DEDUCT",
	2: "BUSINESS_DEDUCT",
}
var DeductType_value = map[string]int32{
	"UNKNOW_DEDUCT_TYPE": 0,
	"OPRATE_DEDUCT":      1,
	"BUSINESS_DEDUCT":    2,
}

func (x DeductType) String() string {
	return proto.EnumName(DeductType_name, int32(x))
}
func (DeductType) EnumDescriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{4} }

type DeductFailType int32

const (
	DeductFailType_DEDUCTFAILTYPE_UNVALID          DeductFailType = 0
	DeductFailType_DEDUCTFAILTYPE_UID_NOT_EXIST    DeductFailType = 1
	DeductFailType_DEDUCTFAILTYPE_ITEM_NOT_ENOUGH  DeductFailType = 2
	DeductFailType_DEDUCTFAILTYPE_DEDUCT_ITEM_FAIL DeductFailType = 3
)

var DeductFailType_name = map[int32]string{
	0: "DEDUCTFAILTYPE_UNVALID",
	1: "DEDUCTFAILTYPE_UID_NOT_EXIST",
	2: "DEDUCTFAILTYPE_ITEM_NOT_ENOUGH",
	3: "DEDUCTFAILTYPE_DEDUCT_ITEM_FAIL",
}
var DeductFailType_value = map[string]int32{
	"DEDUCTFAILTYPE_UNVALID":          0,
	"DEDUCTFAILTYPE_UID_NOT_EXIST":    1,
	"DEDUCTFAILTYPE_ITEM_NOT_ENOUGH":  2,
	"DEDUCTFAILTYPE_DEDUCT_ITEM_FAIL": 3,
}

func (x DeductFailType) String() string {
	return proto.EnumName(DeductFailType_name, int32(x))
}
func (DeductFailType) EnumDescriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{5} }

// 包裹配置
type PackageCfg struct {
	BgId  uint32 `protobuf:"varint,1,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
	Name  string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Desc  string `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	IsDel bool   `protobuf:"varint,4,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
}

func (m *PackageCfg) Reset()                    { *m = PackageCfg{} }
func (m *PackageCfg) String() string            { return proto.CompactTextString(m) }
func (*PackageCfg) ProtoMessage()               {}
func (*PackageCfg) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{0} }

func (m *PackageCfg) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

func (m *PackageCfg) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *PackageCfg) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *PackageCfg) GetIsDel() bool {
	if m != nil {
		return m.IsDel
	}
	return false
}

type PackageItemCfg struct {
	BgItemId       uint32 `protobuf:"varint,1,opt,name=bg_item_id,json=bgItemId,proto3" json:"bg_item_id,omitempty"`
	BgId           uint32 `protobuf:"varint,2,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
	ItemType       uint32 `protobuf:"varint,3,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	SourceId       uint32 `protobuf:"varint,4,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	ItemCount      uint32 `protobuf:"varint,5,opt,name=item_count,json=itemCount,proto3" json:"item_count,omitempty"`
	FinTime        uint32 `protobuf:"varint,6,opt,name=fin_time,json=finTime,proto3" json:"fin_time,omitempty"`
	IsDel          bool   `protobuf:"varint,7,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
	Weight         uint32 `protobuf:"varint,8,opt,name=weight,proto3" json:"weight,omitempty"`
	DynamicFinTime uint32 `protobuf:"varint,9,opt,name=dynamic_fin_time,json=dynamicFinTime,proto3" json:"dynamic_fin_time,omitempty"`
	Months         uint32 `protobuf:"varint,10,opt,name=months,proto3" json:"months,omitempty"`
}

func (m *PackageItemCfg) Reset()                    { *m = PackageItemCfg{} }
func (m *PackageItemCfg) String() string            { return proto.CompactTextString(m) }
func (*PackageItemCfg) ProtoMessage()               {}
func (*PackageItemCfg) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{1} }

func (m *PackageItemCfg) GetBgItemId() uint32 {
	if m != nil {
		return m.BgItemId
	}
	return 0
}

func (m *PackageItemCfg) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

func (m *PackageItemCfg) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *PackageItemCfg) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *PackageItemCfg) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

func (m *PackageItemCfg) GetFinTime() uint32 {
	if m != nil {
		return m.FinTime
	}
	return 0
}

func (m *PackageItemCfg) GetIsDel() bool {
	if m != nil {
		return m.IsDel
	}
	return false
}

func (m *PackageItemCfg) GetWeight() uint32 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *PackageItemCfg) GetDynamicFinTime() uint32 {
	if m != nil {
		return m.DynamicFinTime
	}
	return 0
}

func (m *PackageItemCfg) GetMonths() uint32 {
	if m != nil {
		return m.Months
	}
	return 0
}

// 功能卡片配置
type FuncCardCfg struct {
	CardId    uint32 `protobuf:"varint,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	CardType  uint32 `protobuf:"varint,2,opt,name=card_type,json=cardType,proto3" json:"card_type,omitempty"`
	CardName  string `protobuf:"bytes,3,opt,name=card_name,json=cardName,proto3" json:"card_name,omitempty"`
	CardDesc  string `protobuf:"bytes,4,opt,name=card_desc,json=cardDesc,proto3" json:"card_desc,omitempty"`
	CardUrl   string `protobuf:"bytes,5,opt,name=card_url,json=cardUrl,proto3" json:"card_url,omitempty"`
	CardTimes uint32 `protobuf:"varint,6,opt,name=card_times,json=cardTimes,proto3" json:"card_times,omitempty"`
	ValidTime uint32 `protobuf:"varint,7,opt,name=valid_time,json=validTime,proto3" json:"valid_time,omitempty"`
	IsDel     uint32 `protobuf:"varint,8,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
	Uid       uint32 `protobuf:"varint,9,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (m *FuncCardCfg) Reset()                    { *m = FuncCardCfg{} }
func (m *FuncCardCfg) String() string            { return proto.CompactTextString(m) }
func (*FuncCardCfg) ProtoMessage()               {}
func (*FuncCardCfg) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{2} }

func (m *FuncCardCfg) GetCardId() uint32 {
	if m != nil {
		return m.CardId
	}
	return 0
}

func (m *FuncCardCfg) GetCardType() uint32 {
	if m != nil {
		return m.CardType
	}
	return 0
}

func (m *FuncCardCfg) GetCardName() string {
	if m != nil {
		return m.CardName
	}
	return ""
}

func (m *FuncCardCfg) GetCardDesc() string {
	if m != nil {
		return m.CardDesc
	}
	return ""
}

func (m *FuncCardCfg) GetCardUrl() string {
	if m != nil {
		return m.CardUrl
	}
	return ""
}

func (m *FuncCardCfg) GetCardTimes() uint32 {
	if m != nil {
		return m.CardTimes
	}
	return 0
}

func (m *FuncCardCfg) GetValidTime() uint32 {
	if m != nil {
		return m.ValidTime
	}
	return 0
}

func (m *FuncCardCfg) GetIsDel() uint32 {
	if m != nil {
		return m.IsDel
	}
	return 0
}

func (m *FuncCardCfg) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

// 砸蛋得的抽奖碎片
type LotteryFragmentCfg struct {
	FragmentId    uint32 `protobuf:"varint,1,opt,name=fragment_id,json=fragmentId,proto3" json:"fragment_id,omitempty"`
	FragmentType  uint32 `protobuf:"varint,2,opt,name=fragment_type,json=fragmentType,proto3" json:"fragment_type,omitempty"`
	FragmentName  string `protobuf:"bytes,3,opt,name=fragment_name,json=fragmentName,proto3" json:"fragment_name,omitempty"`
	FragmentDesc  string `protobuf:"bytes,4,opt,name=fragment_desc,json=fragmentDesc,proto3" json:"fragment_desc,omitempty"`
	FragmentUrl   string `protobuf:"bytes,5,opt,name=fragment_url,json=fragmentUrl,proto3" json:"fragment_url,omitempty"`
	IsDel         uint32 `protobuf:"varint,6,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
	FragmentPrice uint32 `protobuf:"varint,7,opt,name=fragment_price,json=fragmentPrice,proto3" json:"fragment_price,omitempty"`
}

func (m *LotteryFragmentCfg) Reset()                    { *m = LotteryFragmentCfg{} }
func (m *LotteryFragmentCfg) String() string            { return proto.CompactTextString(m) }
func (*LotteryFragmentCfg) ProtoMessage()               {}
func (*LotteryFragmentCfg) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{3} }

func (m *LotteryFragmentCfg) GetFragmentId() uint32 {
	if m != nil {
		return m.FragmentId
	}
	return 0
}

func (m *LotteryFragmentCfg) GetFragmentType() uint32 {
	if m != nil {
		return m.FragmentType
	}
	return 0
}

func (m *LotteryFragmentCfg) GetFragmentName() string {
	if m != nil {
		return m.FragmentName
	}
	return ""
}

func (m *LotteryFragmentCfg) GetFragmentDesc() string {
	if m != nil {
		return m.FragmentDesc
	}
	return ""
}

func (m *LotteryFragmentCfg) GetFragmentUrl() string {
	if m != nil {
		return m.FragmentUrl
	}
	return ""
}

func (m *LotteryFragmentCfg) GetIsDel() uint32 {
	if m != nil {
		return m.IsDel
	}
	return 0
}

func (m *LotteryFragmentCfg) GetFragmentPrice() uint32 {
	if m != nil {
		return m.FragmentPrice
	}
	return 0
}

type UserBackpackItem struct {
	ItemType       uint32 `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	UserItemId     uint32 `protobuf:"varint,2,opt,name=user_item_id,json=userItemId,proto3" json:"user_item_id,omitempty"`
	ItemCount      uint32 `protobuf:"varint,3,opt,name=item_count,json=itemCount,proto3" json:"item_count,omitempty"`
	FinTime        uint32 `protobuf:"varint,4,opt,name=fin_time,json=finTime,proto3" json:"fin_time,omitempty"`
	SourceId       uint32 `protobuf:"varint,5,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	Weight         uint32 `protobuf:"varint,6,opt,name=weight,proto3" json:"weight,omitempty"`
	ObtainTime     uint32 `protobuf:"varint,7,opt,name=obtain_time,json=obtainTime,proto3" json:"obtain_time,omitempty"`
	SourceType     uint32 `protobuf:"varint,8,opt,name=source_type,json=sourceType,proto3" json:"source_type,omitempty"`
	FinalItemCount uint32 `protobuf:"varint,9,opt,name=final_item_count,json=finalItemCount,proto3" json:"final_item_count,omitempty"`
}

func (m *UserBackpackItem) Reset()                    { *m = UserBackpackItem{} }
func (m *UserBackpackItem) String() string            { return proto.CompactTextString(m) }
func (*UserBackpackItem) ProtoMessage()               {}
func (*UserBackpackItem) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{4} }

func (m *UserBackpackItem) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *UserBackpackItem) GetUserItemId() uint32 {
	if m != nil {
		return m.UserItemId
	}
	return 0
}

func (m *UserBackpackItem) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

func (m *UserBackpackItem) GetFinTime() uint32 {
	if m != nil {
		return m.FinTime
	}
	return 0
}

func (m *UserBackpackItem) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *UserBackpackItem) GetWeight() uint32 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *UserBackpackItem) GetObtainTime() uint32 {
	if m != nil {
		return m.ObtainTime
	}
	return 0
}

func (m *UserBackpackItem) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

func (m *UserBackpackItem) GetFinalItemCount() uint32 {
	if m != nil {
		return m.FinalItemCount
	}
	return 0
}

type UserBackpackLog struct {
	ItemType  uint32 `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	ItemCount uint32 `protobuf:"varint,2,opt,name=item_count,json=itemCount,proto3" json:"item_count,omitempty"`
	SourceId  uint32 `protobuf:"varint,3,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	LogType   uint32 `protobuf:"varint,4,opt,name=log_type,json=logType,proto3" json:"log_type,omitempty"`
	LogTime   uint32 `protobuf:"varint,5,opt,name=log_time,json=logTime,proto3" json:"log_time,omitempty"`
}

func (m *UserBackpackLog) Reset()                    { *m = UserBackpackLog{} }
func (m *UserBackpackLog) String() string            { return proto.CompactTextString(m) }
func (*UserBackpackLog) ProtoMessage()               {}
func (*UserBackpackLog) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{5} }

func (m *UserBackpackLog) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *UserBackpackLog) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

func (m *UserBackpackLog) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *UserBackpackLog) GetLogType() uint32 {
	if m != nil {
		return m.LogType
	}
	return 0
}

func (m *UserBackpackLog) GetLogTime() uint32 {
	if m != nil {
		return m.LogTime
	}
	return 0
}

type AddPackageCfgReq struct {
	Cfg *PackageCfg `protobuf:"bytes,1,opt,name=cfg" json:"cfg,omitempty"`
}

func (m *AddPackageCfgReq) Reset()                    { *m = AddPackageCfgReq{} }
func (m *AddPackageCfgReq) String() string            { return proto.CompactTextString(m) }
func (*AddPackageCfgReq) ProtoMessage()               {}
func (*AddPackageCfgReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{6} }

func (m *AddPackageCfgReq) GetCfg() *PackageCfg {
	if m != nil {
		return m.Cfg
	}
	return nil
}

type AddPackageCfgResp struct {
	Cfg *PackageCfg `protobuf:"bytes,1,opt,name=cfg" json:"cfg,omitempty"`
}

func (m *AddPackageCfgResp) Reset()                    { *m = AddPackageCfgResp{} }
func (m *AddPackageCfgResp) String() string            { return proto.CompactTextString(m) }
func (*AddPackageCfgResp) ProtoMessage()               {}
func (*AddPackageCfgResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{7} }

func (m *AddPackageCfgResp) GetCfg() *PackageCfg {
	if m != nil {
		return m.Cfg
	}
	return nil
}

type DelPackageCfgReq struct {
	BgId uint32 `protobuf:"varint,1,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
}

func (m *DelPackageCfgReq) Reset()                    { *m = DelPackageCfgReq{} }
func (m *DelPackageCfgReq) String() string            { return proto.CompactTextString(m) }
func (*DelPackageCfgReq) ProtoMessage()               {}
func (*DelPackageCfgReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{8} }

func (m *DelPackageCfgReq) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

type DelPackageCfgResp struct {
}

func (m *DelPackageCfgResp) Reset()                    { *m = DelPackageCfgResp{} }
func (m *DelPackageCfgResp) String() string            { return proto.CompactTextString(m) }
func (*DelPackageCfgResp) ProtoMessage()               {}
func (*DelPackageCfgResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{9} }

type GetPackageCfgReq struct {
}

func (m *GetPackageCfgReq) Reset()                    { *m = GetPackageCfgReq{} }
func (m *GetPackageCfgReq) String() string            { return proto.CompactTextString(m) }
func (*GetPackageCfgReq) ProtoMessage()               {}
func (*GetPackageCfgReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{10} }

type GetPackageCfgResp struct {
	CfgList []*PackageCfg `protobuf:"bytes,1,rep,name=cfg_list,json=cfgList" json:"cfg_list,omitempty"`
}

func (m *GetPackageCfgResp) Reset()                    { *m = GetPackageCfgResp{} }
func (m *GetPackageCfgResp) String() string            { return proto.CompactTextString(m) }
func (*GetPackageCfgResp) ProtoMessage()               {}
func (*GetPackageCfgResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{11} }

func (m *GetPackageCfgResp) GetCfgList() []*PackageCfg {
	if m != nil {
		return m.CfgList
	}
	return nil
}

type AddPackageItemCfgReq struct {
	ItemCfg *PackageItemCfg `protobuf:"bytes,1,opt,name=item_cfg,json=itemCfg" json:"item_cfg,omitempty"`
}

func (m *AddPackageItemCfgReq) Reset()                    { *m = AddPackageItemCfgReq{} }
func (m *AddPackageItemCfgReq) String() string            { return proto.CompactTextString(m) }
func (*AddPackageItemCfgReq) ProtoMessage()               {}
func (*AddPackageItemCfgReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{12} }

func (m *AddPackageItemCfgReq) GetItemCfg() *PackageItemCfg {
	if m != nil {
		return m.ItemCfg
	}
	return nil
}

type AddPackageItemCfgResp struct {
	ItemCfg *PackageItemCfg `protobuf:"bytes,1,opt,name=item_cfg,json=itemCfg" json:"item_cfg,omitempty"`
}

func (m *AddPackageItemCfgResp) Reset()                    { *m = AddPackageItemCfgResp{} }
func (m *AddPackageItemCfgResp) String() string            { return proto.CompactTextString(m) }
func (*AddPackageItemCfgResp) ProtoMessage()               {}
func (*AddPackageItemCfgResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{13} }

func (m *AddPackageItemCfgResp) GetItemCfg() *PackageItemCfg {
	if m != nil {
		return m.ItemCfg
	}
	return nil
}

type ModPackageItemCfgReq struct {
	ItemCfg *PackageItemCfg `protobuf:"bytes,1,opt,name=item_cfg,json=itemCfg" json:"item_cfg,omitempty"`
}

func (m *ModPackageItemCfgReq) Reset()                    { *m = ModPackageItemCfgReq{} }
func (m *ModPackageItemCfgReq) String() string            { return proto.CompactTextString(m) }
func (*ModPackageItemCfgReq) ProtoMessage()               {}
func (*ModPackageItemCfgReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{14} }

func (m *ModPackageItemCfgReq) GetItemCfg() *PackageItemCfg {
	if m != nil {
		return m.ItemCfg
	}
	return nil
}

type ModPackageItemCfgResp struct {
}

func (m *ModPackageItemCfgResp) Reset()                    { *m = ModPackageItemCfgResp{} }
func (m *ModPackageItemCfgResp) String() string            { return proto.CompactTextString(m) }
func (*ModPackageItemCfgResp) ProtoMessage()               {}
func (*ModPackageItemCfgResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{15} }

type DelPackageItemCfgReq struct {
	BgId     uint32 `protobuf:"varint,1,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
	BgItemId uint32 `protobuf:"varint,2,opt,name=bg_item_id,json=bgItemId,proto3" json:"bg_item_id,omitempty"`
}

func (m *DelPackageItemCfgReq) Reset()                    { *m = DelPackageItemCfgReq{} }
func (m *DelPackageItemCfgReq) String() string            { return proto.CompactTextString(m) }
func (*DelPackageItemCfgReq) ProtoMessage()               {}
func (*DelPackageItemCfgReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{16} }

func (m *DelPackageItemCfgReq) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

func (m *DelPackageItemCfgReq) GetBgItemId() uint32 {
	if m != nil {
		return m.BgItemId
	}
	return 0
}

type DelPackageItemCfgResp struct {
}

func (m *DelPackageItemCfgResp) Reset()                    { *m = DelPackageItemCfgResp{} }
func (m *DelPackageItemCfgResp) String() string            { return proto.CompactTextString(m) }
func (*DelPackageItemCfgResp) ProtoMessage()               {}
func (*DelPackageItemCfgResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{17} }

type GetPackageItemCfgReq struct {
	BgId uint32 `protobuf:"varint,1,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
}

func (m *GetPackageItemCfgReq) Reset()                    { *m = GetPackageItemCfgReq{} }
func (m *GetPackageItemCfgReq) String() string            { return proto.CompactTextString(m) }
func (*GetPackageItemCfgReq) ProtoMessage()               {}
func (*GetPackageItemCfgReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{18} }

func (m *GetPackageItemCfgReq) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

type GetPackageItemCfgResp struct {
	ItemCfgList []*PackageItemCfg `protobuf:"bytes,1,rep,name=item_cfg_list,json=itemCfgList" json:"item_cfg_list,omitempty"`
}

func (m *GetPackageItemCfgResp) Reset()                    { *m = GetPackageItemCfgResp{} }
func (m *GetPackageItemCfgResp) String() string            { return proto.CompactTextString(m) }
func (*GetPackageItemCfgResp) ProtoMessage()               {}
func (*GetPackageItemCfgResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{19} }

func (m *GetPackageItemCfgResp) GetItemCfgList() []*PackageItemCfg {
	if m != nil {
		return m.ItemCfgList
	}
	return nil
}

// user backpack
type GiveUserPackageReq struct {
	Uid            uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BgId           uint32 `protobuf:"varint,2,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
	Num            uint32 `protobuf:"varint,3,opt,name=num,proto3" json:"num,omitempty"`
	Source         uint32 `protobuf:"varint,4,opt,name=source,proto3" json:"source,omitempty"`
	OrderId        string `protobuf:"bytes,5,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	ExpireDuration uint32 `protobuf:"varint,6,opt,name=expire_duration,json=expireDuration,proto3" json:"expire_duration,omitempty"`
	// example:expire_duration=1,当前时间为 2019-10-28 10:42:00 这该物品过期时间为 2019-10-29 00:00:00
	// note :expire_duration>=1 则会忽略 包裹配置的 dynamic_fin_time, months
	SourceAppId string `protobuf:"bytes,7,opt,name=source_app_id,json=sourceAppId,proto3" json:"source_app_id,omitempty"`
	TotalPrice  uint32 `protobuf:"varint,8,opt,name=total_price,json=totalPrice,proto3" json:"total_price,omitempty"`
	OutsideTime uint32 `protobuf:"varint,9,opt,name=outside_time,json=outsideTime,proto3" json:"outside_time,omitempty"`
}

func (m *GiveUserPackageReq) Reset()                    { *m = GiveUserPackageReq{} }
func (m *GiveUserPackageReq) String() string            { return proto.CompactTextString(m) }
func (*GiveUserPackageReq) ProtoMessage()               {}
func (*GiveUserPackageReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{20} }

func (m *GiveUserPackageReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GiveUserPackageReq) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

func (m *GiveUserPackageReq) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *GiveUserPackageReq) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *GiveUserPackageReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *GiveUserPackageReq) GetExpireDuration() uint32 {
	if m != nil {
		return m.ExpireDuration
	}
	return 0
}

func (m *GiveUserPackageReq) GetSourceAppId() string {
	if m != nil {
		return m.SourceAppId
	}
	return ""
}

func (m *GiveUserPackageReq) GetTotalPrice() uint32 {
	if m != nil {
		return m.TotalPrice
	}
	return 0
}

func (m *GiveUserPackageReq) GetOutsideTime() uint32 {
	if m != nil {
		return m.OutsideTime
	}
	return 0
}

type GiveUserPackageResp struct {
}

func (m *GiveUserPackageResp) Reset()                    { *m = GiveUserPackageResp{} }
func (m *GiveUserPackageResp) String() string            { return proto.CompactTextString(m) }
func (*GiveUserPackageResp) ProtoMessage()               {}
func (*GiveUserPackageResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{21} }

type UseBackpackExtraInfo struct {
	TargetUid uint32 `protobuf:"varint,1,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	ChannelId uint32 `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	UseCount  uint32 `protobuf:"varint,3,opt,name=use_count,json=useCount,proto3" json:"use_count,omitempty"`
	DealToken string `protobuf:"bytes,4,opt,name=deal_token,json=dealToken,proto3" json:"deal_token,omitempty"`
}

func (m *UseBackpackExtraInfo) Reset()                    { *m = UseBackpackExtraInfo{} }
func (m *UseBackpackExtraInfo) String() string            { return proto.CompactTextString(m) }
func (*UseBackpackExtraInfo) ProtoMessage()               {}
func (*UseBackpackExtraInfo) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{22} }

func (m *UseBackpackExtraInfo) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *UseBackpackExtraInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UseBackpackExtraInfo) GetUseCount() uint32 {
	if m != nil {
		return m.UseCount
	}
	return 0
}

func (m *UseBackpackExtraInfo) GetDealToken() string {
	if m != nil {
		return m.DealToken
	}
	return ""
}

type UseBackpackItemReq struct {
	Uid            uint32                `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ItemType       uint32                `protobuf:"varint,2,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	UserItemId     uint32                `protobuf:"varint,3,opt,name=user_item_id,json=userItemId,proto3" json:"user_item_id,omitempty"`
	SourceId       uint32                `protobuf:"varint,4,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	OrderId        string                `protobuf:"bytes,5,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	UseCount       uint32                `protobuf:"varint,6,opt,name=use_count,json=useCount,proto3" json:"use_count,omitempty"`
	OutsideTime    uint32                `protobuf:"varint,7,opt,name=outside_time,json=outsideTime,proto3" json:"outside_time,omitempty"`
	OrderIdList    []string              `protobuf:"bytes,8,rep,name=order_id_list,json=orderIdList" json:"order_id_list,omitempty"`
	ItemPrice      uint32                `protobuf:"varint,9,opt,name=item_price,json=itemPrice,proto3" json:"item_price,omitempty"`
	PriceType      uint32                `protobuf:"varint,10,opt,name=price_type,json=priceType,proto3" json:"price_type,omitempty"`
	TargetUidList  []uint32              `protobuf:"varint,11,rep,packed,name=target_uid_list,json=targetUidList" json:"target_uid_list,omitempty"`
	ExtraInfo      *UseBackpackExtraInfo `protobuf:"bytes,12,opt,name=extra_info,json=extraInfo" json:"extra_info,omitempty"`
	UseReasionType uint32                `protobuf:"varint,13,opt,name=use_reasion_type,json=useReasionType,proto3" json:"use_reasion_type,omitempty"`
}

func (m *UseBackpackItemReq) Reset()                    { *m = UseBackpackItemReq{} }
func (m *UseBackpackItemReq) String() string            { return proto.CompactTextString(m) }
func (*UseBackpackItemReq) ProtoMessage()               {}
func (*UseBackpackItemReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{23} }

func (m *UseBackpackItemReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UseBackpackItemReq) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *UseBackpackItemReq) GetUserItemId() uint32 {
	if m != nil {
		return m.UserItemId
	}
	return 0
}

func (m *UseBackpackItemReq) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *UseBackpackItemReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *UseBackpackItemReq) GetUseCount() uint32 {
	if m != nil {
		return m.UseCount
	}
	return 0
}

func (m *UseBackpackItemReq) GetOutsideTime() uint32 {
	if m != nil {
		return m.OutsideTime
	}
	return 0
}

func (m *UseBackpackItemReq) GetOrderIdList() []string {
	if m != nil {
		return m.OrderIdList
	}
	return nil
}

func (m *UseBackpackItemReq) GetItemPrice() uint32 {
	if m != nil {
		return m.ItemPrice
	}
	return 0
}

func (m *UseBackpackItemReq) GetPriceType() uint32 {
	if m != nil {
		return m.PriceType
	}
	return 0
}

func (m *UseBackpackItemReq) GetTargetUidList() []uint32 {
	if m != nil {
		return m.TargetUidList
	}
	return nil
}

func (m *UseBackpackItemReq) GetExtraInfo() *UseBackpackExtraInfo {
	if m != nil {
		return m.ExtraInfo
	}
	return nil
}

func (m *UseBackpackItemReq) GetUseReasionType() uint32 {
	if m != nil {
		return m.UseReasionType
	}
	return 0
}

type UseBackpackItemResp struct {
	Remain    uint32 `protobuf:"varint,1,opt,name=remain,proto3" json:"remain,omitempty"`
	DealToken string `protobuf:"bytes,2,opt,name=deal_token,json=dealToken,proto3" json:"deal_token,omitempty"`
	FinTime   uint32 `protobuf:"varint,3,opt,name=fin_time,json=finTime,proto3" json:"fin_time,omitempty"`
}

func (m *UseBackpackItemResp) Reset()                    { *m = UseBackpackItemResp{} }
func (m *UseBackpackItemResp) String() string            { return proto.CompactTextString(m) }
func (*UseBackpackItemResp) ProtoMessage()               {}
func (*UseBackpackItemResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{24} }

func (m *UseBackpackItemResp) GetRemain() uint32 {
	if m != nil {
		return m.Remain
	}
	return 0
}

func (m *UseBackpackItemResp) GetDealToken() string {
	if m != nil {
		return m.DealToken
	}
	return ""
}

func (m *UseBackpackItemResp) GetFinTime() uint32 {
	if m != nil {
		return m.FinTime
	}
	return 0
}

type GetUseItemOrderInfoReq struct {
	OrderId string `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
}

func (m *GetUseItemOrderInfoReq) Reset()                    { *m = GetUseItemOrderInfoReq{} }
func (m *GetUseItemOrderInfoReq) String() string            { return proto.CompactTextString(m) }
func (*GetUseItemOrderInfoReq) ProtoMessage()               {}
func (*GetUseItemOrderInfoReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{25} }

func (m *GetUseItemOrderInfoReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type GetUseItemOrderInfoResp struct {
	UseOrderDetail    *UseOrderDetail       `protobuf:"bytes,1,opt,name=use_order_detail,json=useOrderDetail" json:"use_order_detail,omitempty"`
	UseOrderExtraInfo *UseBackpackExtraInfo `protobuf:"bytes,2,opt,name=use_order_extra_info,json=useOrderExtraInfo" json:"use_order_extra_info,omitempty"`
}

func (m *GetUseItemOrderInfoResp) Reset()                    { *m = GetUseItemOrderInfoResp{} }
func (m *GetUseItemOrderInfoResp) String() string            { return proto.CompactTextString(m) }
func (*GetUseItemOrderInfoResp) ProtoMessage()               {}
func (*GetUseItemOrderInfoResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{26} }

func (m *GetUseItemOrderInfoResp) GetUseOrderDetail() *UseOrderDetail {
	if m != nil {
		return m.UseOrderDetail
	}
	return nil
}

func (m *GetUseItemOrderInfoResp) GetUseOrderExtraInfo() *UseBackpackExtraInfo {
	if m != nil {
		return m.UseOrderExtraInfo
	}
	return nil
}

type ProcBackpackItemTimeoutReq struct {
	UserItemId uint32 `protobuf:"varint,1,opt,name=user_item_id,json=userItemId,proto3" json:"user_item_id,omitempty"`
	Uid        uint32 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	ItemType   uint32 `protobuf:"varint,3,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	SourceId   uint32 `protobuf:"varint,4,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	ItemCount  uint32 `protobuf:"varint,5,opt,name=item_count,json=itemCount,proto3" json:"item_count,omitempty"`
	FinTime    uint32 `protobuf:"varint,6,opt,name=fin_time,json=finTime,proto3" json:"fin_time,omitempty"`
}

func (m *ProcBackpackItemTimeoutReq) Reset()         { *m = ProcBackpackItemTimeoutReq{} }
func (m *ProcBackpackItemTimeoutReq) String() string { return proto.CompactTextString(m) }
func (*ProcBackpackItemTimeoutReq) ProtoMessage()    {}
func (*ProcBackpackItemTimeoutReq) Descriptor() ([]byte, []int) {
	return fileDescriptorBackpack, []int{27}
}

func (m *ProcBackpackItemTimeoutReq) GetUserItemId() uint32 {
	if m != nil {
		return m.UserItemId
	}
	return 0
}

func (m *ProcBackpackItemTimeoutReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ProcBackpackItemTimeoutReq) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *ProcBackpackItemTimeoutReq) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *ProcBackpackItemTimeoutReq) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

func (m *ProcBackpackItemTimeoutReq) GetFinTime() uint32 {
	if m != nil {
		return m.FinTime
	}
	return 0
}

type ProcBackpackItemTimeoutResp struct {
}

func (m *ProcBackpackItemTimeoutResp) Reset()         { *m = ProcBackpackItemTimeoutResp{} }
func (m *ProcBackpackItemTimeoutResp) String() string { return proto.CompactTextString(m) }
func (*ProcBackpackItemTimeoutResp) ProtoMessage()    {}
func (*ProcBackpackItemTimeoutResp) Descriptor() ([]byte, []int) {
	return fileDescriptorBackpack, []int{28}
}

type GetUserBackpackReq struct {
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (m *GetUserBackpackReq) Reset()                    { *m = GetUserBackpackReq{} }
func (m *GetUserBackpackReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserBackpackReq) ProtoMessage()               {}
func (*GetUserBackpackReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{29} }

func (m *GetUserBackpackReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserBackpackResp struct {
	UserItemList []*UserBackpackItem `protobuf:"bytes,1,rep,name=user_item_list,json=userItemList" json:"user_item_list,omitempty"`
	LastObtainTs int64               `protobuf:"varint,2,opt,name=last_obtain_ts,json=lastObtainTs,proto3" json:"last_obtain_ts,omitempty"`
}

func (m *GetUserBackpackResp) Reset()                    { *m = GetUserBackpackResp{} }
func (m *GetUserBackpackResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserBackpackResp) ProtoMessage()               {}
func (*GetUserBackpackResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{30} }

func (m *GetUserBackpackResp) GetUserItemList() []*UserBackpackItem {
	if m != nil {
		return m.UserItemList
	}
	return nil
}

func (m *GetUserBackpackResp) GetLastObtainTs() int64 {
	if m != nil {
		return m.LastObtainTs
	}
	return 0
}

type GetUserFuncCardUseReq struct {
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (m *GetUserFuncCardUseReq) Reset()                    { *m = GetUserFuncCardUseReq{} }
func (m *GetUserFuncCardUseReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserFuncCardUseReq) ProtoMessage()               {}
func (*GetUserFuncCardUseReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{31} }

func (m *GetUserFuncCardUseReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserFuncCardUseResp struct {
	UserItemList []*FuncCardCfg `protobuf:"bytes,1,rep,name=user_item_list,json=userItemList" json:"user_item_list,omitempty"`
}

func (m *GetUserFuncCardUseResp) Reset()                    { *m = GetUserFuncCardUseResp{} }
func (m *GetUserFuncCardUseResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserFuncCardUseResp) ProtoMessage()               {}
func (*GetUserFuncCardUseResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{32} }

func (m *GetUserFuncCardUseResp) GetUserItemList() []*FuncCardCfg {
	if m != nil {
		return m.UserItemList
	}
	return nil
}

type AddFuncCardCfgReq struct {
	CardCfg *FuncCardCfg `protobuf:"bytes,1,opt,name=card_cfg,json=cardCfg" json:"card_cfg,omitempty"`
}

func (m *AddFuncCardCfgReq) Reset()                    { *m = AddFuncCardCfgReq{} }
func (m *AddFuncCardCfgReq) String() string            { return proto.CompactTextString(m) }
func (*AddFuncCardCfgReq) ProtoMessage()               {}
func (*AddFuncCardCfgReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{33} }

func (m *AddFuncCardCfgReq) GetCardCfg() *FuncCardCfg {
	if m != nil {
		return m.CardCfg
	}
	return nil
}

type AddFuncCardCfgResp struct {
}

func (m *AddFuncCardCfgResp) Reset()                    { *m = AddFuncCardCfgResp{} }
func (m *AddFuncCardCfgResp) String() string            { return proto.CompactTextString(m) }
func (*AddFuncCardCfgResp) ProtoMessage()               {}
func (*AddFuncCardCfgResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{34} }

type BatchGetUserFuncCardUseReq struct {
	UidList []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *BatchGetUserFuncCardUseReq) Reset()         { *m = BatchGetUserFuncCardUseReq{} }
func (m *BatchGetUserFuncCardUseReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserFuncCardUseReq) ProtoMessage()    {}
func (*BatchGetUserFuncCardUseReq) Descriptor() ([]byte, []int) {
	return fileDescriptorBackpack, []int{35}
}

func (m *BatchGetUserFuncCardUseReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchGetUserFuncCardUseResp struct {
	UserItemList []*FuncCardCfg `protobuf:"bytes,1,rep,name=user_item_list,json=userItemList" json:"user_item_list,omitempty"`
}

func (m *BatchGetUserFuncCardUseResp) Reset()         { *m = BatchGetUserFuncCardUseResp{} }
func (m *BatchGetUserFuncCardUseResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserFuncCardUseResp) ProtoMessage()    {}
func (*BatchGetUserFuncCardUseResp) Descriptor() ([]byte, []int) {
	return fileDescriptorBackpack, []int{36}
}

func (m *BatchGetUserFuncCardUseResp) GetUserItemList() []*FuncCardCfg {
	if m != nil {
		return m.UserItemList
	}
	return nil
}

type DelFuncCardCfgReq struct {
	CardId uint32 `protobuf:"varint,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
}

func (m *DelFuncCardCfgReq) Reset()                    { *m = DelFuncCardCfgReq{} }
func (m *DelFuncCardCfgReq) String() string            { return proto.CompactTextString(m) }
func (*DelFuncCardCfgReq) ProtoMessage()               {}
func (*DelFuncCardCfgReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{37} }

func (m *DelFuncCardCfgReq) GetCardId() uint32 {
	if m != nil {
		return m.CardId
	}
	return 0
}

type DelFuncCardCfgResp struct {
}

func (m *DelFuncCardCfgResp) Reset()                    { *m = DelFuncCardCfgResp{} }
func (m *DelFuncCardCfgResp) String() string            { return proto.CompactTextString(m) }
func (*DelFuncCardCfgResp) ProtoMessage()               {}
func (*DelFuncCardCfgResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{38} }

type GetFuncCardCfgReq struct {
	CardIdList []uint32 `protobuf:"varint,1,rep,packed,name=card_id_list,json=cardIdList" json:"card_id_list,omitempty"`
}

func (m *GetFuncCardCfgReq) Reset()                    { *m = GetFuncCardCfgReq{} }
func (m *GetFuncCardCfgReq) String() string            { return proto.CompactTextString(m) }
func (*GetFuncCardCfgReq) ProtoMessage()               {}
func (*GetFuncCardCfgReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{39} }

func (m *GetFuncCardCfgReq) GetCardIdList() []uint32 {
	if m != nil {
		return m.CardIdList
	}
	return nil
}

type GetFuncCardCfgResp struct {
	CardCfgList []*FuncCardCfg `protobuf:"bytes,1,rep,name=card_cfg_list,json=cardCfgList" json:"card_cfg_list,omitempty"`
}

func (m *GetFuncCardCfgResp) Reset()                    { *m = GetFuncCardCfgResp{} }
func (m *GetFuncCardCfgResp) String() string            { return proto.CompactTextString(m) }
func (*GetFuncCardCfgResp) ProtoMessage()               {}
func (*GetFuncCardCfgResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{40} }

func (m *GetFuncCardCfgResp) GetCardCfgList() []*FuncCardCfg {
	if m != nil {
		return m.CardCfgList
	}
	return nil
}

type CheckUserFuncCardUseReq struct {
	Uid      uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CardType uint32 `protobuf:"varint,2,opt,name=card_type,json=cardType,proto3" json:"card_type,omitempty"`
}

func (m *CheckUserFuncCardUseReq) Reset()                    { *m = CheckUserFuncCardUseReq{} }
func (m *CheckUserFuncCardUseReq) String() string            { return proto.CompactTextString(m) }
func (*CheckUserFuncCardUseReq) ProtoMessage()               {}
func (*CheckUserFuncCardUseReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{41} }

func (m *CheckUserFuncCardUseReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckUserFuncCardUseReq) GetCardType() uint32 {
	if m != nil {
		return m.CardType
	}
	return 0
}

type CheckUserFuncCardUseResp struct {
	IsUse bool `protobuf:"varint,1,opt,name=is_use,json=isUse,proto3" json:"is_use,omitempty"`
}

func (m *CheckUserFuncCardUseResp) Reset()         { *m = CheckUserFuncCardUseResp{} }
func (m *CheckUserFuncCardUseResp) String() string { return proto.CompactTextString(m) }
func (*CheckUserFuncCardUseResp) ProtoMessage()    {}
func (*CheckUserFuncCardUseResp) Descriptor() ([]byte, []int) {
	return fileDescriptorBackpack, []int{42}
}

func (m *CheckUserFuncCardUseResp) GetIsUse() bool {
	if m != nil {
		return m.IsUse
	}
	return false
}

type SetUserFuncCardUseReq struct {
	Uid      uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CardType uint32 `protobuf:"varint,2,opt,name=card_type,json=cardType,proto3" json:"card_type,omitempty"`
	CardId   uint32 `protobuf:"varint,3,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
}

func (m *SetUserFuncCardUseReq) Reset()                    { *m = SetUserFuncCardUseReq{} }
func (m *SetUserFuncCardUseReq) String() string            { return proto.CompactTextString(m) }
func (*SetUserFuncCardUseReq) ProtoMessage()               {}
func (*SetUserFuncCardUseReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{43} }

func (m *SetUserFuncCardUseReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserFuncCardUseReq) GetCardType() uint32 {
	if m != nil {
		return m.CardType
	}
	return 0
}

func (m *SetUserFuncCardUseReq) GetCardId() uint32 {
	if m != nil {
		return m.CardId
	}
	return 0
}

type SetUserFuncCardUseResp struct {
}

func (m *SetUserFuncCardUseResp) Reset()                    { *m = SetUserFuncCardUseResp{} }
func (m *SetUserFuncCardUseResp) String() string            { return proto.CompactTextString(m) }
func (*SetUserFuncCardUseResp) ProtoMessage()               {}
func (*SetUserFuncCardUseResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{44} }

type GetUserBackpackLogReq struct {
	Uid       uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BeginTime uint32 `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime   uint32 `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	ItemType  uint32 `protobuf:"varint,4,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	SourceId  uint32 `protobuf:"varint,5,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	LogType   uint32 `protobuf:"varint,6,opt,name=log_type,json=logType,proto3" json:"log_type,omitempty"`
}

func (m *GetUserBackpackLogReq) Reset()                    { *m = GetUserBackpackLogReq{} }
func (m *GetUserBackpackLogReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserBackpackLogReq) ProtoMessage()               {}
func (*GetUserBackpackLogReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{45} }

func (m *GetUserBackpackLogReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserBackpackLogReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetUserBackpackLogReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetUserBackpackLogReq) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *GetUserBackpackLogReq) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *GetUserBackpackLogReq) GetLogType() uint32 {
	if m != nil {
		return m.LogType
	}
	return 0
}

type GetUserBackpackLogResp struct {
	LogList []*UserBackpackLog `protobuf:"bytes,1,rep,name=log_list,json=logList" json:"log_list,omitempty"`
}

func (m *GetUserBackpackLogResp) Reset()                    { *m = GetUserBackpackLogResp{} }
func (m *GetUserBackpackLogResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserBackpackLogResp) ProtoMessage()               {}
func (*GetUserBackpackLogResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{46} }

func (m *GetUserBackpackLogResp) GetLogList() []*UserBackpackLog {
	if m != nil {
		return m.LogList
	}
	return nil
}

// 此接口暂时不支持添加卡片
type AddItemCfgReq struct {
	ItemType uint32 `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	ItemCfg  []byte `protobuf:"bytes,2,opt,name=item_cfg,json=itemCfg,proto3" json:"item_cfg,omitempty"`
}

func (m *AddItemCfgReq) Reset()                    { *m = AddItemCfgReq{} }
func (m *AddItemCfgReq) String() string            { return proto.CompactTextString(m) }
func (*AddItemCfgReq) ProtoMessage()               {}
func (*AddItemCfgReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{47} }

func (m *AddItemCfgReq) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *AddItemCfgReq) GetItemCfg() []byte {
	if m != nil {
		return m.ItemCfg
	}
	return nil
}

type AddItemCfgResp struct {
	ItemType uint32 `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	ItemCfg  []byte `protobuf:"bytes,2,opt,name=item_cfg,json=itemCfg,proto3" json:"item_cfg,omitempty"`
}

func (m *AddItemCfgResp) Reset()                    { *m = AddItemCfgResp{} }
func (m *AddItemCfgResp) String() string            { return proto.CompactTextString(m) }
func (*AddItemCfgResp) ProtoMessage()               {}
func (*AddItemCfgResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{48} }

func (m *AddItemCfgResp) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *AddItemCfgResp) GetItemCfg() []byte {
	if m != nil {
		return m.ItemCfg
	}
	return nil
}

type DelItemCfgReq struct {
	ItemType uint32 `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	// uint32 item_source_type = 3;
	ItemCfg []byte `protobuf:"bytes,2,opt,name=item_cfg,json=itemCfg,proto3" json:"item_cfg,omitempty"`
}

func (m *DelItemCfgReq) Reset()                    { *m = DelItemCfgReq{} }
func (m *DelItemCfgReq) String() string            { return proto.CompactTextString(m) }
func (*DelItemCfgReq) ProtoMessage()               {}
func (*DelItemCfgReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{49} }

func (m *DelItemCfgReq) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *DelItemCfgReq) GetItemCfg() []byte {
	if m != nil {
		return m.ItemCfg
	}
	return nil
}

type DelItemCfgResp struct {
}

func (m *DelItemCfgResp) Reset()                    { *m = DelItemCfgResp{} }
func (m *DelItemCfgResp) String() string            { return proto.CompactTextString(m) }
func (*DelItemCfgResp) ProtoMessage()               {}
func (*DelItemCfgResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{50} }

type GetItemCfgReq struct {
	ItemType         uint32   `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	ItemSourceIdList []uint32 `protobuf:"varint,2,rep,packed,name=item_source_id_list,json=itemSourceIdList" json:"item_source_id_list,omitempty"`
	GetAll           bool     `protobuf:"varint,3,opt,name=get_all,json=getAll,proto3" json:"get_all,omitempty"`
}

func (m *GetItemCfgReq) Reset()                    { *m = GetItemCfgReq{} }
func (m *GetItemCfgReq) String() string            { return proto.CompactTextString(m) }
func (*GetItemCfgReq) ProtoMessage()               {}
func (*GetItemCfgReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{51} }

func (m *GetItemCfgReq) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *GetItemCfgReq) GetItemSourceIdList() []uint32 {
	if m != nil {
		return m.ItemSourceIdList
	}
	return nil
}

func (m *GetItemCfgReq) GetGetAll() bool {
	if m != nil {
		return m.GetAll
	}
	return false
}

type GetItemCfgResp struct {
	ItemCfgList [][]byte `protobuf:"bytes,1,rep,name=item_cfg_list,json=itemCfgList" json:"item_cfg_list,omitempty"`
}

func (m *GetItemCfgResp) Reset()                    { *m = GetItemCfgResp{} }
func (m *GetItemCfgResp) String() string            { return proto.CompactTextString(m) }
func (*GetItemCfgResp) ProtoMessage()               {}
func (*GetItemCfgResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{52} }

func (m *GetItemCfgResp) GetItemCfgList() [][]byte {
	if m != nil {
		return m.ItemCfgList
	}
	return nil
}

type UseItemInfo struct {
	ItemType   uint32 `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	UserItemId uint32 `protobuf:"varint,2,opt,name=user_item_id,json=userItemId,proto3" json:"user_item_id,omitempty"`
	SourceId   uint32 `protobuf:"varint,3,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	UseCount   uint32 `protobuf:"varint,4,opt,name=use_count,json=useCount,proto3" json:"use_count,omitempty"`
	TotalPrice uint32 `protobuf:"varint,5,opt,name=total_price,json=totalPrice,proto3" json:"total_price,omitempty"`
}

func (m *UseItemInfo) Reset()                    { *m = UseItemInfo{} }
func (m *UseItemInfo) String() string            { return proto.CompactTextString(m) }
func (*UseItemInfo) ProtoMessage()               {}
func (*UseItemInfo) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{53} }

func (m *UseItemInfo) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *UseItemInfo) GetUserItemId() uint32 {
	if m != nil {
		return m.UserItemId
	}
	return 0
}

func (m *UseItemInfo) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *UseItemInfo) GetUseCount() uint32 {
	if m != nil {
		return m.UseCount
	}
	return 0
}

func (m *UseItemInfo) GetTotalPrice() uint32 {
	if m != nil {
		return m.TotalPrice
	}
	return 0
}

type TransactionInfo struct {
	FreezeType uint32 `protobuf:"varint,1,opt,name=freeze_type,json=freezeType,proto3" json:"freeze_type,omitempty"`
	OperTime   uint32 `protobuf:"varint,2,opt,name=oper_time,json=operTime,proto3" json:"oper_time,omitempty"`
	OrderId    string `protobuf:"bytes,3,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	ExpireTime uint32 `protobuf:"varint,4,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
}

func (m *TransactionInfo) Reset()                    { *m = TransactionInfo{} }
func (m *TransactionInfo) String() string            { return proto.CompactTextString(m) }
func (*TransactionInfo) ProtoMessage()               {}
func (*TransactionInfo) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{54} }

func (m *TransactionInfo) GetFreezeType() uint32 {
	if m != nil {
		return m.FreezeType
	}
	return 0
}

func (m *TransactionInfo) GetOperTime() uint32 {
	if m != nil {
		return m.OperTime
	}
	return 0
}

func (m *TransactionInfo) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *TransactionInfo) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

// only support BACKPACK_LOTTERY_FRAGMENT
// TODO support other type  (2019-10-22,T1035)
// doc: see FreeZeItem.md
type FreeZeItemReq struct {
	TansacationInfo *TransactionInfo `protobuf:"bytes,1,opt,name=tansacation_info,json=tansacationInfo" json:"tansacation_info,omitempty"`
	ItemInfoList    []*UseItemInfo   `protobuf:"bytes,2,rep,name=Item_info_list,json=ItemInfoList" json:"Item_info_list,omitempty"`
	Uid             uint32           `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (m *FreeZeItemReq) Reset()                    { *m = FreeZeItemReq{} }
func (m *FreeZeItemReq) String() string            { return proto.CompactTextString(m) }
func (*FreeZeItemReq) ProtoMessage()               {}
func (*FreeZeItemReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{55} }

func (m *FreeZeItemReq) GetTansacationInfo() *TransactionInfo {
	if m != nil {
		return m.TansacationInfo
	}
	return nil
}

func (m *FreeZeItemReq) GetItemInfoList() []*UseItemInfo {
	if m != nil {
		return m.ItemInfoList
	}
	return nil
}

func (m *FreeZeItemReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type FreeZeItemResp struct {
}

func (m *FreeZeItemResp) Reset()                    { *m = FreeZeItemResp{} }
func (m *FreeZeItemResp) String() string            { return proto.CompactTextString(m) }
func (*FreeZeItemResp) ProtoMessage()               {}
func (*FreeZeItemResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{56} }

type UserPackageSum struct {
	ItemType  uint32 `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	ItemId    uint32 `protobuf:"varint,2,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ItemCount uint32 `protobuf:"varint,3,opt,name=item_count,json=itemCount,proto3" json:"item_count,omitempty"`
}

func (m *UserPackageSum) Reset()                    { *m = UserPackageSum{} }
func (m *UserPackageSum) String() string            { return proto.CompactTextString(m) }
func (*UserPackageSum) ProtoMessage()               {}
func (*UserPackageSum) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{57} }

func (m *UserPackageSum) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *UserPackageSum) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *UserPackageSum) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

type GetUserPackageSumReq struct {
	Uid      uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ItemType uint32 `protobuf:"varint,2,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	ItemId   uint32 `protobuf:"varint,3,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
}

func (m *GetUserPackageSumReq) Reset()                    { *m = GetUserPackageSumReq{} }
func (m *GetUserPackageSumReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserPackageSumReq) ProtoMessage()               {}
func (*GetUserPackageSumReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{58} }

func (m *GetUserPackageSumReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserPackageSumReq) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *GetUserPackageSumReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

type GetUserPackageSumResp struct {
	ItemSum *UserPackageSum `protobuf:"bytes,1,opt,name=item_sum,json=itemSum" json:"item_sum,omitempty"`
}

func (m *GetUserPackageSumResp) Reset()                    { *m = GetUserPackageSumResp{} }
func (m *GetUserPackageSumResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserPackageSumResp) ProtoMessage()               {}
func (*GetUserPackageSumResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{59} }

func (m *GetUserPackageSumResp) GetItemSum() *UserPackageSum {
	if m != nil {
		return m.ItemSum
	}
	return nil
}

type GetOrderCountByTimeRangeReq struct {
	BeginTime uint32 `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime   uint32 `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	LogType   uint32 `protobuf:"varint,3,opt,name=log_type,json=logType,proto3" json:"log_type,omitempty"`
	Paras     string `protobuf:"bytes,4,opt,name=paras,proto3" json:"paras,omitempty"`
}

func (m *GetOrderCountByTimeRangeReq) Reset()         { *m = GetOrderCountByTimeRangeReq{} }
func (m *GetOrderCountByTimeRangeReq) String() string { return proto.CompactTextString(m) }
func (*GetOrderCountByTimeRangeReq) ProtoMessage()    {}
func (*GetOrderCountByTimeRangeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorBackpack, []int{60}
}

func (m *GetOrderCountByTimeRangeReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetOrderCountByTimeRangeReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetOrderCountByTimeRangeReq) GetLogType() uint32 {
	if m != nil {
		return m.LogType
	}
	return 0
}

func (m *GetOrderCountByTimeRangeReq) GetParas() string {
	if m != nil {
		return m.Paras
	}
	return ""
}

type GetOrderCountByTimeRangeResp struct {
	Count    uint32 `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	UseCount uint32 `protobuf:"varint,2,opt,name=use_count,json=useCount,proto3" json:"use_count,omitempty"`
	Value    uint32 `protobuf:"varint,3,opt,name=value,proto3" json:"value,omitempty"`
}

func (m *GetOrderCountByTimeRangeResp) Reset()         { *m = GetOrderCountByTimeRangeResp{} }
func (m *GetOrderCountByTimeRangeResp) String() string { return proto.CompactTextString(m) }
func (*GetOrderCountByTimeRangeResp) ProtoMessage()    {}
func (*GetOrderCountByTimeRangeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorBackpack, []int{61}
}

func (m *GetOrderCountByTimeRangeResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetOrderCountByTimeRangeResp) GetUseCount() uint32 {
	if m != nil {
		return m.UseCount
	}
	return 0
}

func (m *GetOrderCountByTimeRangeResp) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

type GetOrderListByTimeRangeReq struct {
	BeginTime uint32 `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime   uint32 `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	LogType   uint32 `protobuf:"varint,3,opt,name=log_type,json=logType,proto3" json:"log_type,omitempty"`
}

func (m *GetOrderListByTimeRangeReq) Reset()         { *m = GetOrderListByTimeRangeReq{} }
func (m *GetOrderListByTimeRangeReq) String() string { return proto.CompactTextString(m) }
func (*GetOrderListByTimeRangeReq) ProtoMessage()    {}
func (*GetOrderListByTimeRangeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorBackpack, []int{62}
}

func (m *GetOrderListByTimeRangeReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetOrderListByTimeRangeReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetOrderListByTimeRangeReq) GetLogType() uint32 {
	if m != nil {
		return m.LogType
	}
	return 0
}

type GetOrderListByTimeRangeResp struct {
	OrderList []string `protobuf:"bytes,1,rep,name=order_list,json=orderList" json:"order_list,omitempty"`
}

func (m *GetOrderListByTimeRangeResp) Reset()         { *m = GetOrderListByTimeRangeResp{} }
func (m *GetOrderListByTimeRangeResp) String() string { return proto.CompactTextString(m) }
func (*GetOrderListByTimeRangeResp) ProtoMessage()    {}
func (*GetOrderListByTimeRangeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorBackpack, []int{63}
}

func (m *GetOrderListByTimeRangeResp) GetOrderList() []string {
	if m != nil {
		return m.OrderList
	}
	return nil
}

type TransformOpt struct {
	Uid           uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Source        uint32 `protobuf:"varint,2,opt,name=source,proto3" json:"source,omitempty"`
	LogType       uint32 `protobuf:"varint,3,opt,name=log_type,json=logType,proto3" json:"log_type,omitempty"`
	OutsideTime   uint32 `protobuf:"varint,4,opt,name=outside_time,json=outsideTime,proto3" json:"outside_time,omitempty"`
	OrderId       string `protobuf:"bytes,5,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	BgId          uint32 `protobuf:"varint,6,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
	BgNum         uint32 `protobuf:"varint,7,opt,name=bg_num,json=bgNum,proto3" json:"bg_num,omitempty"`
	MaterialPrice uint32 `protobuf:"varint,8,opt,name=material_price,json=materialPrice,proto3" json:"material_price,omitempty"`
	GainPrice     uint32 `protobuf:"varint,9,opt,name=gain_price,json=gainPrice,proto3" json:"gain_price,omitempty"`
}

func (m *TransformOpt) Reset()                    { *m = TransformOpt{} }
func (m *TransformOpt) String() string            { return proto.CompactTextString(m) }
func (*TransformOpt) ProtoMessage()               {}
func (*TransformOpt) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{64} }

func (m *TransformOpt) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TransformOpt) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *TransformOpt) GetLogType() uint32 {
	if m != nil {
		return m.LogType
	}
	return 0
}

func (m *TransformOpt) GetOutsideTime() uint32 {
	if m != nil {
		return m.OutsideTime
	}
	return 0
}

func (m *TransformOpt) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *TransformOpt) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

func (m *TransformOpt) GetBgNum() uint32 {
	if m != nil {
		return m.BgNum
	}
	return 0
}

func (m *TransformOpt) GetMaterialPrice() uint32 {
	if m != nil {
		return m.MaterialPrice
	}
	return 0
}

func (m *TransformOpt) GetGainPrice() uint32 {
	if m != nil {
		return m.GainPrice
	}
	return 0
}

type ConversionItemReq struct {
	Uid              uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OutsideTime      uint32         `protobuf:"varint,2,opt,name=outside_time,json=outsideTime,proto3" json:"outside_time,omitempty"`
	BgId             uint32         `protobuf:"varint,3,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
	BgNum            uint32         `protobuf:"varint,4,opt,name=bg_num,json=bgNum,proto3" json:"bg_num,omitempty"`
	Source           uint32         `protobuf:"varint,5,opt,name=source,proto3" json:"source,omitempty"`
	MaterialPrice    uint32         `protobuf:"varint,6,opt,name=material_price,json=materialPrice,proto3" json:"material_price,omitempty"`
	ConversionPrice  uint32         `protobuf:"varint,7,opt,name=conversion_price,json=conversionPrice,proto3" json:"conversion_price,omitempty"`
	OrderId          string         `protobuf:"bytes,8,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	MaterialItemList []*UseItemInfo `protobuf:"bytes,9,rep,name=material_item_list,json=materialItemList" json:"material_item_list,omitempty"`
}

func (m *ConversionItemReq) Reset()                    { *m = ConversionItemReq{} }
func (m *ConversionItemReq) String() string            { return proto.CompactTextString(m) }
func (*ConversionItemReq) ProtoMessage()               {}
func (*ConversionItemReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{65} }

func (m *ConversionItemReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ConversionItemReq) GetOutsideTime() uint32 {
	if m != nil {
		return m.OutsideTime
	}
	return 0
}

func (m *ConversionItemReq) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

func (m *ConversionItemReq) GetBgNum() uint32 {
	if m != nil {
		return m.BgNum
	}
	return 0
}

func (m *ConversionItemReq) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *ConversionItemReq) GetMaterialPrice() uint32 {
	if m != nil {
		return m.MaterialPrice
	}
	return 0
}

func (m *ConversionItemReq) GetConversionPrice() uint32 {
	if m != nil {
		return m.ConversionPrice
	}
	return 0
}

func (m *ConversionItemReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *ConversionItemReq) GetMaterialItemList() []*UseItemInfo {
	if m != nil {
		return m.MaterialItemList
	}
	return nil
}

type ConversionItemResp struct {
}

func (m *ConversionItemResp) Reset()                    { *m = ConversionItemResp{} }
func (m *ConversionItemResp) String() string            { return proto.CompactTextString(m) }
func (*ConversionItemResp) ProtoMessage()               {}
func (*ConversionItemResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{66} }

type RollBackUserItemReq struct {
	Uid           uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CreateTime    uint32 `protobuf:"varint,2,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	OriginOrderId string `protobuf:"bytes,3,opt,name=origin_order_id,json=originOrderId,proto3" json:"origin_order_id,omitempty"`
}

func (m *RollBackUserItemReq) Reset()                    { *m = RollBackUserItemReq{} }
func (m *RollBackUserItemReq) String() string            { return proto.CompactTextString(m) }
func (*RollBackUserItemReq) ProtoMessage()               {}
func (*RollBackUserItemReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{67} }

func (m *RollBackUserItemReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RollBackUserItemReq) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *RollBackUserItemReq) GetOriginOrderId() string {
	if m != nil {
		return m.OriginOrderId
	}
	return ""
}

type RollBackUserItemResp struct {
}

func (m *RollBackUserItemResp) Reset()                    { *m = RollBackUserItemResp{} }
func (m *RollBackUserItemResp) String() string            { return proto.CompactTextString(m) }
func (*RollBackUserItemResp) ProtoMessage()               {}
func (*RollBackUserItemResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{68} }

type DeductItem struct {
	ItemType uint32 `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	SourceId uint32 `protobuf:"varint,2,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	Count    uint32 `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
}

func (m *DeductItem) Reset()                    { *m = DeductItem{} }
func (m *DeductItem) String() string            { return proto.CompactTextString(m) }
func (*DeductItem) ProtoMessage()               {}
func (*DeductItem) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{69} }

func (m *DeductItem) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *DeductItem) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *DeductItem) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type DeductDetail struct {
	Uid         uint32        `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ItemList    []*DeductItem `protobuf:"bytes,2,rep,name=item_list,json=itemList" json:"item_list,omitempty"`
	Count       uint32        `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	SourceType  uint32        `protobuf:"varint,4,opt,name=source_type,json=sourceType,proto3" json:"source_type,omitempty"`
	IsAllSource bool          `protobuf:"varint,5,opt,name=is_all_source,json=isAllSource,proto3" json:"is_all_source,omitempty"`
}

func (m *DeductDetail) Reset()                    { *m = DeductDetail{} }
func (m *DeductDetail) String() string            { return proto.CompactTextString(m) }
func (*DeductDetail) ProtoMessage()               {}
func (*DeductDetail) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{70} }

func (m *DeductDetail) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DeductDetail) GetItemList() []*DeductItem {
	if m != nil {
		return m.ItemList
	}
	return nil
}

func (m *DeductDetail) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *DeductDetail) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

func (m *DeductDetail) GetIsAllSource() bool {
	if m != nil {
		return m.IsAllSource
	}
	return false
}

type BatchDeductUserItemReq struct {
	DeductList []*DeductDetail `protobuf:"bytes,1,rep,name=deduct_list,json=deductList" json:"deduct_list,omitempty"`
	Oper       string          `protobuf:"bytes,2,opt,name=oper,proto3" json:"oper,omitempty"`
	OrderId    string          `protobuf:"bytes,3,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	DeductType uint32          `protobuf:"varint,4,opt,name=deduct_type,json=deductType,proto3" json:"deduct_type,omitempty"`
}

func (m *BatchDeductUserItemReq) Reset()                    { *m = BatchDeductUserItemReq{} }
func (m *BatchDeductUserItemReq) String() string            { return proto.CompactTextString(m) }
func (*BatchDeductUserItemReq) ProtoMessage()               {}
func (*BatchDeductUserItemReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{71} }

func (m *BatchDeductUserItemReq) GetDeductList() []*DeductDetail {
	if m != nil {
		return m.DeductList
	}
	return nil
}

func (m *BatchDeductUserItemReq) GetOper() string {
	if m != nil {
		return m.Oper
	}
	return ""
}

func (m *BatchDeductUserItemReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *BatchDeductUserItemReq) GetDeductType() uint32 {
	if m != nil {
		return m.DeductType
	}
	return 0
}

type DeductResult struct {
	Uid             uint32        `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SuccessItemList []*DeductItem `protobuf:"bytes,2,rep,name=success_item_list,json=successItemList" json:"success_item_list,omitempty"`
	FailItemList    []*DeductItem `protobuf:"bytes,3,rep,name=fail_item_list,json=failItemList" json:"fail_item_list,omitempty"`
	FailType        uint32        `protobuf:"varint,4,opt,name=fail_type,json=failType,proto3" json:"fail_type,omitempty"`
}

func (m *DeductResult) Reset()                    { *m = DeductResult{} }
func (m *DeductResult) String() string            { return proto.CompactTextString(m) }
func (*DeductResult) ProtoMessage()               {}
func (*DeductResult) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{72} }

func (m *DeductResult) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DeductResult) GetSuccessItemList() []*DeductItem {
	if m != nil {
		return m.SuccessItemList
	}
	return nil
}

func (m *DeductResult) GetFailItemList() []*DeductItem {
	if m != nil {
		return m.FailItemList
	}
	return nil
}

func (m *DeductResult) GetFailType() uint32 {
	if m != nil {
		return m.FailType
	}
	return 0
}

type BatchDeductUserItemResp struct {
	DeductList []*DeductResult `protobuf:"bytes,1,rep,name=deduct_list,json=deductList" json:"deduct_list,omitempty"`
}

func (m *BatchDeductUserItemResp) Reset()                    { *m = BatchDeductUserItemResp{} }
func (m *BatchDeductUserItemResp) String() string            { return proto.CompactTextString(m) }
func (*BatchDeductUserItemResp) ProtoMessage()               {}
func (*BatchDeductUserItemResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{73} }

func (m *BatchDeductUserItemResp) GetDeductList() []*DeductResult {
	if m != nil {
		return m.DeductList
	}
	return nil
}

type UseOrderDetail struct {
	Uid        uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OrderId    string `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	SourceId   uint32 `protobuf:"varint,3,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	ItemType   uint32 `protobuf:"varint,4,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	UseCount   uint32 `protobuf:"varint,5,opt,name=use_count,json=useCount,proto3" json:"use_count,omitempty"`
	PriceType  uint32 `protobuf:"varint,6,opt,name=price_type,json=priceType,proto3" json:"price_type,omitempty"`
	CreateTime uint32 `protobuf:"varint,7,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UserItemId uint32 `protobuf:"varint,8,opt,name=user_item_id,json=userItemId,proto3" json:"user_item_id,omitempty"`
}

func (m *UseOrderDetail) Reset()                    { *m = UseOrderDetail{} }
func (m *UseOrderDetail) String() string            { return proto.CompactTextString(m) }
func (*UseOrderDetail) ProtoMessage()               {}
func (*UseOrderDetail) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{74} }

func (m *UseOrderDetail) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UseOrderDetail) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *UseOrderDetail) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *UseOrderDetail) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *UseOrderDetail) GetUseCount() uint32 {
	if m != nil {
		return m.UseCount
	}
	return 0
}

func (m *UseOrderDetail) GetPriceType() uint32 {
	if m != nil {
		return m.PriceType
	}
	return 0
}

func (m *UseOrderDetail) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *UseOrderDetail) GetUserItemId() uint32 {
	if m != nil {
		return m.UserItemId
	}
	return 0
}

// 获得订单数据
type TimeRangeReq struct {
	BeginTime int64  `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime   int64  `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Params    string `protobuf:"bytes,3,opt,name=params,proto3" json:"params,omitempty"`
}

func (m *TimeRangeReq) Reset()                    { *m = TimeRangeReq{} }
func (m *TimeRangeReq) String() string            { return proto.CompactTextString(m) }
func (*TimeRangeReq) ProtoMessage()               {}
func (*TimeRangeReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{75} }

func (m *TimeRangeReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *TimeRangeReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *TimeRangeReq) GetParams() string {
	if m != nil {
		return m.Params
	}
	return ""
}

// 响应order_id个数
type CountResp struct {
	Count uint32 `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	Value uint32 `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (m *CountResp) Reset()                    { *m = CountResp{} }
func (m *CountResp) String() string            { return proto.CompactTextString(m) }
func (*CountResp) ProtoMessage()               {}
func (*CountResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{76} }

func (m *CountResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *CountResp) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

// 响应orderId详情
type OrderIdsResp struct {
	OrderIds []string `protobuf:"bytes,1,rep,name=order_ids,json=orderIds" json:"order_ids,omitempty"`
}

func (m *OrderIdsResp) Reset()                    { *m = OrderIdsResp{} }
func (m *OrderIdsResp) String() string            { return proto.CompactTextString(m) }
func (*OrderIdsResp) ProtoMessage()               {}
func (*OrderIdsResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{77} }

func (m *OrderIdsResp) GetOrderIds() []string {
	if m != nil {
		return m.OrderIds
	}
	return nil
}

func init() {
	proto.RegisterType((*PackageCfg)(nil), "backpack.PackageCfg")
	proto.RegisterType((*PackageItemCfg)(nil), "backpack.PackageItemCfg")
	proto.RegisterType((*FuncCardCfg)(nil), "backpack.FuncCardCfg")
	proto.RegisterType((*LotteryFragmentCfg)(nil), "backpack.LotteryFragmentCfg")
	proto.RegisterType((*UserBackpackItem)(nil), "backpack.UserBackpackItem")
	proto.RegisterType((*UserBackpackLog)(nil), "backpack.UserBackpackLog")
	proto.RegisterType((*AddPackageCfgReq)(nil), "backpack.AddPackageCfgReq")
	proto.RegisterType((*AddPackageCfgResp)(nil), "backpack.AddPackageCfgResp")
	proto.RegisterType((*DelPackageCfgReq)(nil), "backpack.DelPackageCfgReq")
	proto.RegisterType((*DelPackageCfgResp)(nil), "backpack.DelPackageCfgResp")
	proto.RegisterType((*GetPackageCfgReq)(nil), "backpack.GetPackageCfgReq")
	proto.RegisterType((*GetPackageCfgResp)(nil), "backpack.GetPackageCfgResp")
	proto.RegisterType((*AddPackageItemCfgReq)(nil), "backpack.AddPackageItemCfgReq")
	proto.RegisterType((*AddPackageItemCfgResp)(nil), "backpack.AddPackageItemCfgResp")
	proto.RegisterType((*ModPackageItemCfgReq)(nil), "backpack.ModPackageItemCfgReq")
	proto.RegisterType((*ModPackageItemCfgResp)(nil), "backpack.ModPackageItemCfgResp")
	proto.RegisterType((*DelPackageItemCfgReq)(nil), "backpack.DelPackageItemCfgReq")
	proto.RegisterType((*DelPackageItemCfgResp)(nil), "backpack.DelPackageItemCfgResp")
	proto.RegisterType((*GetPackageItemCfgReq)(nil), "backpack.GetPackageItemCfgReq")
	proto.RegisterType((*GetPackageItemCfgResp)(nil), "backpack.GetPackageItemCfgResp")
	proto.RegisterType((*GiveUserPackageReq)(nil), "backpack.GiveUserPackageReq")
	proto.RegisterType((*GiveUserPackageResp)(nil), "backpack.GiveUserPackageResp")
	proto.RegisterType((*UseBackpackExtraInfo)(nil), "backpack.UseBackpackExtraInfo")
	proto.RegisterType((*UseBackpackItemReq)(nil), "backpack.UseBackpackItemReq")
	proto.RegisterType((*UseBackpackItemResp)(nil), "backpack.UseBackpackItemResp")
	proto.RegisterType((*GetUseItemOrderInfoReq)(nil), "backpack.GetUseItemOrderInfoReq")
	proto.RegisterType((*GetUseItemOrderInfoResp)(nil), "backpack.GetUseItemOrderInfoResp")
	proto.RegisterType((*ProcBackpackItemTimeoutReq)(nil), "backpack.ProcBackpackItemTimeoutReq")
	proto.RegisterType((*ProcBackpackItemTimeoutResp)(nil), "backpack.ProcBackpackItemTimeoutResp")
	proto.RegisterType((*GetUserBackpackReq)(nil), "backpack.GetUserBackpackReq")
	proto.RegisterType((*GetUserBackpackResp)(nil), "backpack.GetUserBackpackResp")
	proto.RegisterType((*GetUserFuncCardUseReq)(nil), "backpack.GetUserFuncCardUseReq")
	proto.RegisterType((*GetUserFuncCardUseResp)(nil), "backpack.GetUserFuncCardUseResp")
	proto.RegisterType((*AddFuncCardCfgReq)(nil), "backpack.AddFuncCardCfgReq")
	proto.RegisterType((*AddFuncCardCfgResp)(nil), "backpack.AddFuncCardCfgResp")
	proto.RegisterType((*BatchGetUserFuncCardUseReq)(nil), "backpack.BatchGetUserFuncCardUseReq")
	proto.RegisterType((*BatchGetUserFuncCardUseResp)(nil), "backpack.BatchGetUserFuncCardUseResp")
	proto.RegisterType((*DelFuncCardCfgReq)(nil), "backpack.DelFuncCardCfgReq")
	proto.RegisterType((*DelFuncCardCfgResp)(nil), "backpack.DelFuncCardCfgResp")
	proto.RegisterType((*GetFuncCardCfgReq)(nil), "backpack.GetFuncCardCfgReq")
	proto.RegisterType((*GetFuncCardCfgResp)(nil), "backpack.GetFuncCardCfgResp")
	proto.RegisterType((*CheckUserFuncCardUseReq)(nil), "backpack.CheckUserFuncCardUseReq")
	proto.RegisterType((*CheckUserFuncCardUseResp)(nil), "backpack.CheckUserFuncCardUseResp")
	proto.RegisterType((*SetUserFuncCardUseReq)(nil), "backpack.SetUserFuncCardUseReq")
	proto.RegisterType((*SetUserFuncCardUseResp)(nil), "backpack.SetUserFuncCardUseResp")
	proto.RegisterType((*GetUserBackpackLogReq)(nil), "backpack.GetUserBackpackLogReq")
	proto.RegisterType((*GetUserBackpackLogResp)(nil), "backpack.GetUserBackpackLogResp")
	proto.RegisterType((*AddItemCfgReq)(nil), "backpack.AddItemCfgReq")
	proto.RegisterType((*AddItemCfgResp)(nil), "backpack.AddItemCfgResp")
	proto.RegisterType((*DelItemCfgReq)(nil), "backpack.DelItemCfgReq")
	proto.RegisterType((*DelItemCfgResp)(nil), "backpack.DelItemCfgResp")
	proto.RegisterType((*GetItemCfgReq)(nil), "backpack.GetItemCfgReq")
	proto.RegisterType((*GetItemCfgResp)(nil), "backpack.GetItemCfgResp")
	proto.RegisterType((*UseItemInfo)(nil), "backpack.UseItemInfo")
	proto.RegisterType((*TransactionInfo)(nil), "backpack.TransactionInfo")
	proto.RegisterType((*FreeZeItemReq)(nil), "backpack.FreeZeItemReq")
	proto.RegisterType((*FreeZeItemResp)(nil), "backpack.FreeZeItemResp")
	proto.RegisterType((*UserPackageSum)(nil), "backpack.UserPackageSum")
	proto.RegisterType((*GetUserPackageSumReq)(nil), "backpack.GetUserPackageSumReq")
	proto.RegisterType((*GetUserPackageSumResp)(nil), "backpack.GetUserPackageSumResp")
	proto.RegisterType((*GetOrderCountByTimeRangeReq)(nil), "backpack.GetOrderCountByTimeRangeReq")
	proto.RegisterType((*GetOrderCountByTimeRangeResp)(nil), "backpack.GetOrderCountByTimeRangeResp")
	proto.RegisterType((*GetOrderListByTimeRangeReq)(nil), "backpack.GetOrderListByTimeRangeReq")
	proto.RegisterType((*GetOrderListByTimeRangeResp)(nil), "backpack.GetOrderListByTimeRangeResp")
	proto.RegisterType((*TransformOpt)(nil), "backpack.TransformOpt")
	proto.RegisterType((*ConversionItemReq)(nil), "backpack.ConversionItemReq")
	proto.RegisterType((*ConversionItemResp)(nil), "backpack.ConversionItemResp")
	proto.RegisterType((*RollBackUserItemReq)(nil), "backpack.RollBackUserItemReq")
	proto.RegisterType((*RollBackUserItemResp)(nil), "backpack.RollBackUserItemResp")
	proto.RegisterType((*DeductItem)(nil), "backpack.DeductItem")
	proto.RegisterType((*DeductDetail)(nil), "backpack.DeductDetail")
	proto.RegisterType((*BatchDeductUserItemReq)(nil), "backpack.BatchDeductUserItemReq")
	proto.RegisterType((*DeductResult)(nil), "backpack.DeductResult")
	proto.RegisterType((*BatchDeductUserItemResp)(nil), "backpack.BatchDeductUserItemResp")
	proto.RegisterType((*UseOrderDetail)(nil), "backpack.UseOrderDetail")
	proto.RegisterType((*TimeRangeReq)(nil), "backpack.TimeRangeReq")
	proto.RegisterType((*CountResp)(nil), "backpack.CountResp")
	proto.RegisterType((*OrderIdsResp)(nil), "backpack.OrderIdsResp")
	proto.RegisterEnum("backpack.LogType", LogType_name, LogType_value)
	proto.RegisterEnum("backpack.PackageItemType", PackageItemType_name, PackageItemType_value)
	proto.RegisterEnum("backpack.PackageSourceType", PackageSourceType_name, PackageSourceType_value)
	proto.RegisterEnum("backpack.FREEZETYPE", FREEZETYPE_name, FREEZETYPE_value)
	proto.RegisterEnum("backpack.DeductType", DeductType_name, DeductType_value)
	proto.RegisterEnum("backpack.DeductFailType", DeductFailType_name, DeductFailType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for Backpack service

type BackpackClient interface {
	AddPackageCfg(ctx context.Context, in *AddPackageCfgReq, opts ...grpc.CallOption) (*AddPackageCfgResp, error)
	GetPackageCfg(ctx context.Context, in *GetPackageCfgReq, opts ...grpc.CallOption) (*GetPackageCfgResp, error)
	DelPackageCfg(ctx context.Context, in *DelPackageCfgReq, opts ...grpc.CallOption) (*DelPackageCfgResp, error)
	AddPackageItemCfg(ctx context.Context, in *AddPackageItemCfgReq, opts ...grpc.CallOption) (*AddPackageItemCfgResp, error)
	GetPackageItemCfg(ctx context.Context, in *GetPackageItemCfgReq, opts ...grpc.CallOption) (*GetPackageItemCfgResp, error)
	ModPackageItemCfg(ctx context.Context, in *ModPackageItemCfgReq, opts ...grpc.CallOption) (*ModPackageItemCfgResp, error)
	DelPackageItemCfg(ctx context.Context, in *DelPackageItemCfgReq, opts ...grpc.CallOption) (*DelPackageItemCfgResp, error)
	AddFuncCardCfg(ctx context.Context, in *AddFuncCardCfgReq, opts ...grpc.CallOption) (*AddFuncCardCfgResp, error)
	DelFuncCardCfg(ctx context.Context, in *DelFuncCardCfgReq, opts ...grpc.CallOption) (*DelFuncCardCfgResp, error)
	GetFuncCardCfg(ctx context.Context, in *GetFuncCardCfgReq, opts ...grpc.CallOption) (*GetFuncCardCfgResp, error)
	GiveUserPackage(ctx context.Context, in *GiveUserPackageReq, opts ...grpc.CallOption) (*GiveUserPackageResp, error)
	UseBackpackItem(ctx context.Context, in *UseBackpackItemReq, opts ...grpc.CallOption) (*UseBackpackItemResp, error)
	GetUserBackpack(ctx context.Context, in *GetUserBackpackReq, opts ...grpc.CallOption) (*GetUserBackpackResp, error)
	CheckUserFuncCardUse(ctx context.Context, in *CheckUserFuncCardUseReq, opts ...grpc.CallOption) (*CheckUserFuncCardUseResp, error)
	SetUserFuncCardUse(ctx context.Context, in *SetUserFuncCardUseReq, opts ...grpc.CallOption) (*SetUserFuncCardUseResp, error)
	GetUserFuncCardUse(ctx context.Context, in *GetUserFuncCardUseReq, opts ...grpc.CallOption) (*GetUserFuncCardUseResp, error)
	ProcBackpackItemTimeout(ctx context.Context, in *ProcBackpackItemTimeoutReq, opts ...grpc.CallOption) (*ProcBackpackItemTimeoutResp, error)
	GetUserBackpackLog(ctx context.Context, in *GetUserBackpackLogReq, opts ...grpc.CallOption) (*GetUserBackpackLogResp, error)
	AddItemCfg(ctx context.Context, in *AddItemCfgReq, opts ...grpc.CallOption) (*AddItemCfgResp, error)
	DelItemCfg(ctx context.Context, in *DelItemCfgReq, opts ...grpc.CallOption) (*DelItemCfgResp, error)
	GetItemCfg(ctx context.Context, in *GetItemCfgReq, opts ...grpc.CallOption) (*GetItemCfgResp, error)
	FreeZeItem(ctx context.Context, in *FreeZeItemReq, opts ...grpc.CallOption) (*FreeZeItemResp, error)
	GetUserPackageSum(ctx context.Context, in *GetUserPackageSumReq, opts ...grpc.CallOption) (*GetUserPackageSumResp, error)
	GetOrderCountByTimeRange(ctx context.Context, in *GetOrderCountByTimeRangeReq, opts ...grpc.CallOption) (*GetOrderCountByTimeRangeResp, error)
	GetOrderListByTimeRange(ctx context.Context, in *GetOrderListByTimeRangeReq, opts ...grpc.CallOption) (*GetOrderListByTimeRangeResp, error)
	ConversionItem(ctx context.Context, in *ConversionItemReq, opts ...grpc.CallOption) (*ConversionItemResp, error)
	RollBackUserItem(ctx context.Context, in *RollBackUserItemReq, opts ...grpc.CallOption) (*RollBackUserItemResp, error)
	BatchDeductUserItem(ctx context.Context, in *BatchDeductUserItemReq, opts ...grpc.CallOption) (*BatchDeductUserItemResp, error)
	GetUseItemOrderInfo(ctx context.Context, in *GetUseItemOrderInfoReq, opts ...grpc.CallOption) (*GetUseItemOrderInfoResp, error)
	GetTimeRangeOrderData(ctx context.Context, in *TimeRangeReq, opts ...grpc.CallOption) (*CountResp, error)
	GetTimeRangeUseOrderData(ctx context.Context, in *TimeRangeReq, opts ...grpc.CallOption) (*CountResp, error)
	GetTimeRangeOrderList(ctx context.Context, in *TimeRangeReq, opts ...grpc.CallOption) (*OrderIdsResp, error)
	BatchGetUserFuncCardUse(ctx context.Context, in *BatchGetUserFuncCardUseReq, opts ...grpc.CallOption) (*BatchGetUserFuncCardUseResp, error)
}

type backpackClient struct {
	cc *grpc.ClientConn
}

func NewBackpackClient(cc *grpc.ClientConn) BackpackClient {
	return &backpackClient{cc}
}

func (c *backpackClient) AddPackageCfg(ctx context.Context, in *AddPackageCfgReq, opts ...grpc.CallOption) (*AddPackageCfgResp, error) {
	out := new(AddPackageCfgResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/AddPackageCfg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) GetPackageCfg(ctx context.Context, in *GetPackageCfgReq, opts ...grpc.CallOption) (*GetPackageCfgResp, error) {
	out := new(GetPackageCfgResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/GetPackageCfg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) DelPackageCfg(ctx context.Context, in *DelPackageCfgReq, opts ...grpc.CallOption) (*DelPackageCfgResp, error) {
	out := new(DelPackageCfgResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/DelPackageCfg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) AddPackageItemCfg(ctx context.Context, in *AddPackageItemCfgReq, opts ...grpc.CallOption) (*AddPackageItemCfgResp, error) {
	out := new(AddPackageItemCfgResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/AddPackageItemCfg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) GetPackageItemCfg(ctx context.Context, in *GetPackageItemCfgReq, opts ...grpc.CallOption) (*GetPackageItemCfgResp, error) {
	out := new(GetPackageItemCfgResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/GetPackageItemCfg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) ModPackageItemCfg(ctx context.Context, in *ModPackageItemCfgReq, opts ...grpc.CallOption) (*ModPackageItemCfgResp, error) {
	out := new(ModPackageItemCfgResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/ModPackageItemCfg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) DelPackageItemCfg(ctx context.Context, in *DelPackageItemCfgReq, opts ...grpc.CallOption) (*DelPackageItemCfgResp, error) {
	out := new(DelPackageItemCfgResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/DelPackageItemCfg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) AddFuncCardCfg(ctx context.Context, in *AddFuncCardCfgReq, opts ...grpc.CallOption) (*AddFuncCardCfgResp, error) {
	out := new(AddFuncCardCfgResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/AddFuncCardCfg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) DelFuncCardCfg(ctx context.Context, in *DelFuncCardCfgReq, opts ...grpc.CallOption) (*DelFuncCardCfgResp, error) {
	out := new(DelFuncCardCfgResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/DelFuncCardCfg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) GetFuncCardCfg(ctx context.Context, in *GetFuncCardCfgReq, opts ...grpc.CallOption) (*GetFuncCardCfgResp, error) {
	out := new(GetFuncCardCfgResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/GetFuncCardCfg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) GiveUserPackage(ctx context.Context, in *GiveUserPackageReq, opts ...grpc.CallOption) (*GiveUserPackageResp, error) {
	out := new(GiveUserPackageResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/GiveUserPackage", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) UseBackpackItem(ctx context.Context, in *UseBackpackItemReq, opts ...grpc.CallOption) (*UseBackpackItemResp, error) {
	out := new(UseBackpackItemResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/UseBackpackItem", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) GetUserBackpack(ctx context.Context, in *GetUserBackpackReq, opts ...grpc.CallOption) (*GetUserBackpackResp, error) {
	out := new(GetUserBackpackResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/GetUserBackpack", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) CheckUserFuncCardUse(ctx context.Context, in *CheckUserFuncCardUseReq, opts ...grpc.CallOption) (*CheckUserFuncCardUseResp, error) {
	out := new(CheckUserFuncCardUseResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/CheckUserFuncCardUse", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) SetUserFuncCardUse(ctx context.Context, in *SetUserFuncCardUseReq, opts ...grpc.CallOption) (*SetUserFuncCardUseResp, error) {
	out := new(SetUserFuncCardUseResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/SetUserFuncCardUse", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) GetUserFuncCardUse(ctx context.Context, in *GetUserFuncCardUseReq, opts ...grpc.CallOption) (*GetUserFuncCardUseResp, error) {
	out := new(GetUserFuncCardUseResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/GetUserFuncCardUse", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) ProcBackpackItemTimeout(ctx context.Context, in *ProcBackpackItemTimeoutReq, opts ...grpc.CallOption) (*ProcBackpackItemTimeoutResp, error) {
	out := new(ProcBackpackItemTimeoutResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/ProcBackpackItemTimeout", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) GetUserBackpackLog(ctx context.Context, in *GetUserBackpackLogReq, opts ...grpc.CallOption) (*GetUserBackpackLogResp, error) {
	out := new(GetUserBackpackLogResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/GetUserBackpackLog", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) AddItemCfg(ctx context.Context, in *AddItemCfgReq, opts ...grpc.CallOption) (*AddItemCfgResp, error) {
	out := new(AddItemCfgResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/AddItemCfg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) DelItemCfg(ctx context.Context, in *DelItemCfgReq, opts ...grpc.CallOption) (*DelItemCfgResp, error) {
	out := new(DelItemCfgResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/DelItemCfg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) GetItemCfg(ctx context.Context, in *GetItemCfgReq, opts ...grpc.CallOption) (*GetItemCfgResp, error) {
	out := new(GetItemCfgResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/GetItemCfg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) FreeZeItem(ctx context.Context, in *FreeZeItemReq, opts ...grpc.CallOption) (*FreeZeItemResp, error) {
	out := new(FreeZeItemResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/FreeZeItem", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) GetUserPackageSum(ctx context.Context, in *GetUserPackageSumReq, opts ...grpc.CallOption) (*GetUserPackageSumResp, error) {
	out := new(GetUserPackageSumResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/GetUserPackageSum", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) GetOrderCountByTimeRange(ctx context.Context, in *GetOrderCountByTimeRangeReq, opts ...grpc.CallOption) (*GetOrderCountByTimeRangeResp, error) {
	out := new(GetOrderCountByTimeRangeResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/GetOrderCountByTimeRange", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) GetOrderListByTimeRange(ctx context.Context, in *GetOrderListByTimeRangeReq, opts ...grpc.CallOption) (*GetOrderListByTimeRangeResp, error) {
	out := new(GetOrderListByTimeRangeResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/GetOrderListByTimeRange", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) ConversionItem(ctx context.Context, in *ConversionItemReq, opts ...grpc.CallOption) (*ConversionItemResp, error) {
	out := new(ConversionItemResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/ConversionItem", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) RollBackUserItem(ctx context.Context, in *RollBackUserItemReq, opts ...grpc.CallOption) (*RollBackUserItemResp, error) {
	out := new(RollBackUserItemResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/RollBackUserItem", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) BatchDeductUserItem(ctx context.Context, in *BatchDeductUserItemReq, opts ...grpc.CallOption) (*BatchDeductUserItemResp, error) {
	out := new(BatchDeductUserItemResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/BatchDeductUserItem", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) GetUseItemOrderInfo(ctx context.Context, in *GetUseItemOrderInfoReq, opts ...grpc.CallOption) (*GetUseItemOrderInfoResp, error) {
	out := new(GetUseItemOrderInfoResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/GetUseItemOrderInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) GetTimeRangeOrderData(ctx context.Context, in *TimeRangeReq, opts ...grpc.CallOption) (*CountResp, error) {
	out := new(CountResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/GetTimeRangeOrderData", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) GetTimeRangeUseOrderData(ctx context.Context, in *TimeRangeReq, opts ...grpc.CallOption) (*CountResp, error) {
	out := new(CountResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/GetTimeRangeUseOrderData", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) GetTimeRangeOrderList(ctx context.Context, in *TimeRangeReq, opts ...grpc.CallOption) (*OrderIdsResp, error) {
	out := new(OrderIdsResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/GetTimeRangeOrderList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) BatchGetUserFuncCardUse(ctx context.Context, in *BatchGetUserFuncCardUseReq, opts ...grpc.CallOption) (*BatchGetUserFuncCardUseResp, error) {
	out := new(BatchGetUserFuncCardUseResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/BatchGetUserFuncCardUse", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for Backpack service

type BackpackServer interface {
	AddPackageCfg(context.Context, *AddPackageCfgReq) (*AddPackageCfgResp, error)
	GetPackageCfg(context.Context, *GetPackageCfgReq) (*GetPackageCfgResp, error)
	DelPackageCfg(context.Context, *DelPackageCfgReq) (*DelPackageCfgResp, error)
	AddPackageItemCfg(context.Context, *AddPackageItemCfgReq) (*AddPackageItemCfgResp, error)
	GetPackageItemCfg(context.Context, *GetPackageItemCfgReq) (*GetPackageItemCfgResp, error)
	ModPackageItemCfg(context.Context, *ModPackageItemCfgReq) (*ModPackageItemCfgResp, error)
	DelPackageItemCfg(context.Context, *DelPackageItemCfgReq) (*DelPackageItemCfgResp, error)
	AddFuncCardCfg(context.Context, *AddFuncCardCfgReq) (*AddFuncCardCfgResp, error)
	DelFuncCardCfg(context.Context, *DelFuncCardCfgReq) (*DelFuncCardCfgResp, error)
	GetFuncCardCfg(context.Context, *GetFuncCardCfgReq) (*GetFuncCardCfgResp, error)
	GiveUserPackage(context.Context, *GiveUserPackageReq) (*GiveUserPackageResp, error)
	UseBackpackItem(context.Context, *UseBackpackItemReq) (*UseBackpackItemResp, error)
	GetUserBackpack(context.Context, *GetUserBackpackReq) (*GetUserBackpackResp, error)
	CheckUserFuncCardUse(context.Context, *CheckUserFuncCardUseReq) (*CheckUserFuncCardUseResp, error)
	SetUserFuncCardUse(context.Context, *SetUserFuncCardUseReq) (*SetUserFuncCardUseResp, error)
	GetUserFuncCardUse(context.Context, *GetUserFuncCardUseReq) (*GetUserFuncCardUseResp, error)
	ProcBackpackItemTimeout(context.Context, *ProcBackpackItemTimeoutReq) (*ProcBackpackItemTimeoutResp, error)
	GetUserBackpackLog(context.Context, *GetUserBackpackLogReq) (*GetUserBackpackLogResp, error)
	AddItemCfg(context.Context, *AddItemCfgReq) (*AddItemCfgResp, error)
	DelItemCfg(context.Context, *DelItemCfgReq) (*DelItemCfgResp, error)
	GetItemCfg(context.Context, *GetItemCfgReq) (*GetItemCfgResp, error)
	FreeZeItem(context.Context, *FreeZeItemReq) (*FreeZeItemResp, error)
	GetUserPackageSum(context.Context, *GetUserPackageSumReq) (*GetUserPackageSumResp, error)
	GetOrderCountByTimeRange(context.Context, *GetOrderCountByTimeRangeReq) (*GetOrderCountByTimeRangeResp, error)
	GetOrderListByTimeRange(context.Context, *GetOrderListByTimeRangeReq) (*GetOrderListByTimeRangeResp, error)
	ConversionItem(context.Context, *ConversionItemReq) (*ConversionItemResp, error)
	RollBackUserItem(context.Context, *RollBackUserItemReq) (*RollBackUserItemResp, error)
	BatchDeductUserItem(context.Context, *BatchDeductUserItemReq) (*BatchDeductUserItemResp, error)
	GetUseItemOrderInfo(context.Context, *GetUseItemOrderInfoReq) (*GetUseItemOrderInfoResp, error)
	GetTimeRangeOrderData(context.Context, *TimeRangeReq) (*CountResp, error)
	GetTimeRangeUseOrderData(context.Context, *TimeRangeReq) (*CountResp, error)
	GetTimeRangeOrderList(context.Context, *TimeRangeReq) (*OrderIdsResp, error)
	BatchGetUserFuncCardUse(context.Context, *BatchGetUserFuncCardUseReq) (*BatchGetUserFuncCardUseResp, error)
}

func RegisterBackpackServer(s *grpc.Server, srv BackpackServer) {
	s.RegisterService(&_Backpack_serviceDesc, srv)
}

func _Backpack_AddPackageCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPackageCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).AddPackageCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/AddPackageCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).AddPackageCfg(ctx, req.(*AddPackageCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_GetPackageCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPackageCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).GetPackageCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/GetPackageCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).GetPackageCfg(ctx, req.(*GetPackageCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_DelPackageCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelPackageCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).DelPackageCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/DelPackageCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).DelPackageCfg(ctx, req.(*DelPackageCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_AddPackageItemCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPackageItemCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).AddPackageItemCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/AddPackageItemCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).AddPackageItemCfg(ctx, req.(*AddPackageItemCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_GetPackageItemCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPackageItemCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).GetPackageItemCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/GetPackageItemCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).GetPackageItemCfg(ctx, req.(*GetPackageItemCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_ModPackageItemCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModPackageItemCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).ModPackageItemCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/ModPackageItemCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).ModPackageItemCfg(ctx, req.(*ModPackageItemCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_DelPackageItemCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelPackageItemCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).DelPackageItemCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/DelPackageItemCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).DelPackageItemCfg(ctx, req.(*DelPackageItemCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_AddFuncCardCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddFuncCardCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).AddFuncCardCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/AddFuncCardCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).AddFuncCardCfg(ctx, req.(*AddFuncCardCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_DelFuncCardCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelFuncCardCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).DelFuncCardCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/DelFuncCardCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).DelFuncCardCfg(ctx, req.(*DelFuncCardCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_GetFuncCardCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFuncCardCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).GetFuncCardCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/GetFuncCardCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).GetFuncCardCfg(ctx, req.(*GetFuncCardCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_GiveUserPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GiveUserPackageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).GiveUserPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/GiveUserPackage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).GiveUserPackage(ctx, req.(*GiveUserPackageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_UseBackpackItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UseBackpackItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).UseBackpackItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/UseBackpackItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).UseBackpackItem(ctx, req.(*UseBackpackItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_GetUserBackpack_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserBackpackReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).GetUserBackpack(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/GetUserBackpack",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).GetUserBackpack(ctx, req.(*GetUserBackpackReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_CheckUserFuncCardUse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserFuncCardUseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).CheckUserFuncCardUse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/CheckUserFuncCardUse",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).CheckUserFuncCardUse(ctx, req.(*CheckUserFuncCardUseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_SetUserFuncCardUse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserFuncCardUseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).SetUserFuncCardUse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/SetUserFuncCardUse",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).SetUserFuncCardUse(ctx, req.(*SetUserFuncCardUseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_GetUserFuncCardUse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserFuncCardUseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).GetUserFuncCardUse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/GetUserFuncCardUse",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).GetUserFuncCardUse(ctx, req.(*GetUserFuncCardUseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_ProcBackpackItemTimeout_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcBackpackItemTimeoutReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).ProcBackpackItemTimeout(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/ProcBackpackItemTimeout",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).ProcBackpackItemTimeout(ctx, req.(*ProcBackpackItemTimeoutReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_GetUserBackpackLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserBackpackLogReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).GetUserBackpackLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/GetUserBackpackLog",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).GetUserBackpackLog(ctx, req.(*GetUserBackpackLogReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_AddItemCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddItemCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).AddItemCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/AddItemCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).AddItemCfg(ctx, req.(*AddItemCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_DelItemCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelItemCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).DelItemCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/DelItemCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).DelItemCfg(ctx, req.(*DelItemCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_GetItemCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetItemCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).GetItemCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/GetItemCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).GetItemCfg(ctx, req.(*GetItemCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_FreeZeItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FreeZeItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).FreeZeItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/FreeZeItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).FreeZeItem(ctx, req.(*FreeZeItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_GetUserPackageSum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserPackageSumReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).GetUserPackageSum(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/GetUserPackageSum",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).GetUserPackageSum(ctx, req.(*GetUserPackageSumReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_GetOrderCountByTimeRange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderCountByTimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).GetOrderCountByTimeRange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/GetOrderCountByTimeRange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).GetOrderCountByTimeRange(ctx, req.(*GetOrderCountByTimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_GetOrderListByTimeRange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderListByTimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).GetOrderListByTimeRange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/GetOrderListByTimeRange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).GetOrderListByTimeRange(ctx, req.(*GetOrderListByTimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_ConversionItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConversionItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).ConversionItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/ConversionItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).ConversionItem(ctx, req.(*ConversionItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_RollBackUserItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RollBackUserItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).RollBackUserItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/RollBackUserItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).RollBackUserItem(ctx, req.(*RollBackUserItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_BatchDeductUserItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDeductUserItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).BatchDeductUserItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/BatchDeductUserItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).BatchDeductUserItem(ctx, req.(*BatchDeductUserItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_GetUseItemOrderInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUseItemOrderInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).GetUseItemOrderInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/GetUseItemOrderInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).GetUseItemOrderInfo(ctx, req.(*GetUseItemOrderInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_GetTimeRangeOrderData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).GetTimeRangeOrderData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/GetTimeRangeOrderData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).GetTimeRangeOrderData(ctx, req.(*TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_GetTimeRangeUseOrderData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).GetTimeRangeUseOrderData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/GetTimeRangeUseOrderData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).GetTimeRangeUseOrderData(ctx, req.(*TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_GetTimeRangeOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).GetTimeRangeOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/GetTimeRangeOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).GetTimeRangeOrderList(ctx, req.(*TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_BatchGetUserFuncCardUse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserFuncCardUseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).BatchGetUserFuncCardUse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/BatchGetUserFuncCardUse",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).BatchGetUserFuncCardUse(ctx, req.(*BatchGetUserFuncCardUseReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Backpack_serviceDesc = grpc.ServiceDesc{
	ServiceName: "backpack.Backpack",
	HandlerType: (*BackpackServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddPackageCfg",
			Handler:    _Backpack_AddPackageCfg_Handler,
		},
		{
			MethodName: "GetPackageCfg",
			Handler:    _Backpack_GetPackageCfg_Handler,
		},
		{
			MethodName: "DelPackageCfg",
			Handler:    _Backpack_DelPackageCfg_Handler,
		},
		{
			MethodName: "AddPackageItemCfg",
			Handler:    _Backpack_AddPackageItemCfg_Handler,
		},
		{
			MethodName: "GetPackageItemCfg",
			Handler:    _Backpack_GetPackageItemCfg_Handler,
		},
		{
			MethodName: "ModPackageItemCfg",
			Handler:    _Backpack_ModPackageItemCfg_Handler,
		},
		{
			MethodName: "DelPackageItemCfg",
			Handler:    _Backpack_DelPackageItemCfg_Handler,
		},
		{
			MethodName: "AddFuncCardCfg",
			Handler:    _Backpack_AddFuncCardCfg_Handler,
		},
		{
			MethodName: "DelFuncCardCfg",
			Handler:    _Backpack_DelFuncCardCfg_Handler,
		},
		{
			MethodName: "GetFuncCardCfg",
			Handler:    _Backpack_GetFuncCardCfg_Handler,
		},
		{
			MethodName: "GiveUserPackage",
			Handler:    _Backpack_GiveUserPackage_Handler,
		},
		{
			MethodName: "UseBackpackItem",
			Handler:    _Backpack_UseBackpackItem_Handler,
		},
		{
			MethodName: "GetUserBackpack",
			Handler:    _Backpack_GetUserBackpack_Handler,
		},
		{
			MethodName: "CheckUserFuncCardUse",
			Handler:    _Backpack_CheckUserFuncCardUse_Handler,
		},
		{
			MethodName: "SetUserFuncCardUse",
			Handler:    _Backpack_SetUserFuncCardUse_Handler,
		},
		{
			MethodName: "GetUserFuncCardUse",
			Handler:    _Backpack_GetUserFuncCardUse_Handler,
		},
		{
			MethodName: "ProcBackpackItemTimeout",
			Handler:    _Backpack_ProcBackpackItemTimeout_Handler,
		},
		{
			MethodName: "GetUserBackpackLog",
			Handler:    _Backpack_GetUserBackpackLog_Handler,
		},
		{
			MethodName: "AddItemCfg",
			Handler:    _Backpack_AddItemCfg_Handler,
		},
		{
			MethodName: "DelItemCfg",
			Handler:    _Backpack_DelItemCfg_Handler,
		},
		{
			MethodName: "GetItemCfg",
			Handler:    _Backpack_GetItemCfg_Handler,
		},
		{
			MethodName: "FreeZeItem",
			Handler:    _Backpack_FreeZeItem_Handler,
		},
		{
			MethodName: "GetUserPackageSum",
			Handler:    _Backpack_GetUserPackageSum_Handler,
		},
		{
			MethodName: "GetOrderCountByTimeRange",
			Handler:    _Backpack_GetOrderCountByTimeRange_Handler,
		},
		{
			MethodName: "GetOrderListByTimeRange",
			Handler:    _Backpack_GetOrderListByTimeRange_Handler,
		},
		{
			MethodName: "ConversionItem",
			Handler:    _Backpack_ConversionItem_Handler,
		},
		{
			MethodName: "RollBackUserItem",
			Handler:    _Backpack_RollBackUserItem_Handler,
		},
		{
			MethodName: "BatchDeductUserItem",
			Handler:    _Backpack_BatchDeductUserItem_Handler,
		},
		{
			MethodName: "GetUseItemOrderInfo",
			Handler:    _Backpack_GetUseItemOrderInfo_Handler,
		},
		{
			MethodName: "GetTimeRangeOrderData",
			Handler:    _Backpack_GetTimeRangeOrderData_Handler,
		},
		{
			MethodName: "GetTimeRangeUseOrderData",
			Handler:    _Backpack_GetTimeRangeUseOrderData_Handler,
		},
		{
			MethodName: "GetTimeRangeOrderList",
			Handler:    _Backpack_GetTimeRangeOrderList_Handler,
		},
		{
			MethodName: "BatchGetUserFuncCardUse",
			Handler:    _Backpack_BatchGetUserFuncCardUse_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service/appsvr/src/backpacksvr/backpack.proto",
}

func (m *PackageCfg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PackageCfg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BgId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.BgId))
	}
	if len(m.Name) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.Name)))
		i += copy(dAtA[i:], m.Name)
	}
	if len(m.Desc) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.Desc)))
		i += copy(dAtA[i:], m.Desc)
	}
	if m.IsDel {
		dAtA[i] = 0x20
		i++
		if m.IsDel {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	return i, nil
}

func (m *PackageItemCfg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PackageItemCfg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BgItemId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.BgItemId))
	}
	if m.BgId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.BgId))
	}
	if m.ItemType != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemType))
	}
	if m.SourceId != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.SourceId))
	}
	if m.ItemCount != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemCount))
	}
	if m.FinTime != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.FinTime))
	}
	if m.IsDel {
		dAtA[i] = 0x38
		i++
		if m.IsDel {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	if m.Weight != 0 {
		dAtA[i] = 0x40
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Weight))
	}
	if m.DynamicFinTime != 0 {
		dAtA[i] = 0x48
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.DynamicFinTime))
	}
	if m.Months != 0 {
		dAtA[i] = 0x50
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Months))
	}
	return i, nil
}

func (m *FuncCardCfg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FuncCardCfg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.CardId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.CardId))
	}
	if m.CardType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.CardType))
	}
	if len(m.CardName) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.CardName)))
		i += copy(dAtA[i:], m.CardName)
	}
	if len(m.CardDesc) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.CardDesc)))
		i += copy(dAtA[i:], m.CardDesc)
	}
	if len(m.CardUrl) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.CardUrl)))
		i += copy(dAtA[i:], m.CardUrl)
	}
	if m.CardTimes != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.CardTimes))
	}
	if m.ValidTime != 0 {
		dAtA[i] = 0x38
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ValidTime))
	}
	if m.IsDel != 0 {
		dAtA[i] = 0x40
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.IsDel))
	}
	if m.Uid != 0 {
		dAtA[i] = 0x48
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Uid))
	}
	return i, nil
}

func (m *LotteryFragmentCfg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LotteryFragmentCfg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.FragmentId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.FragmentId))
	}
	if m.FragmentType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.FragmentType))
	}
	if len(m.FragmentName) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.FragmentName)))
		i += copy(dAtA[i:], m.FragmentName)
	}
	if len(m.FragmentDesc) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.FragmentDesc)))
		i += copy(dAtA[i:], m.FragmentDesc)
	}
	if len(m.FragmentUrl) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.FragmentUrl)))
		i += copy(dAtA[i:], m.FragmentUrl)
	}
	if m.IsDel != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.IsDel))
	}
	if m.FragmentPrice != 0 {
		dAtA[i] = 0x38
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.FragmentPrice))
	}
	return i, nil
}

func (m *UserBackpackItem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserBackpackItem) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ItemType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemType))
	}
	if m.UserItemId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.UserItemId))
	}
	if m.ItemCount != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemCount))
	}
	if m.FinTime != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.FinTime))
	}
	if m.SourceId != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.SourceId))
	}
	if m.Weight != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Weight))
	}
	if m.ObtainTime != 0 {
		dAtA[i] = 0x38
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ObtainTime))
	}
	if m.SourceType != 0 {
		dAtA[i] = 0x40
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.SourceType))
	}
	if m.FinalItemCount != 0 {
		dAtA[i] = 0x48
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.FinalItemCount))
	}
	return i, nil
}

func (m *UserBackpackLog) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserBackpackLog) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ItemType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemType))
	}
	if m.ItemCount != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemCount))
	}
	if m.SourceId != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.SourceId))
	}
	if m.LogType != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.LogType))
	}
	if m.LogTime != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.LogTime))
	}
	return i, nil
}

func (m *AddPackageCfgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddPackageCfgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Cfg != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Cfg.Size()))
		n1, err := m.Cfg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *AddPackageCfgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddPackageCfgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Cfg != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Cfg.Size()))
		n2, err := m.Cfg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func (m *DelPackageCfgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelPackageCfgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BgId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.BgId))
	}
	return i, nil
}

func (m *DelPackageCfgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelPackageCfgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetPackageCfgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPackageCfgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetPackageCfgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPackageCfgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.CfgList) > 0 {
		for _, msg := range m.CfgList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintBackpack(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *AddPackageItemCfgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddPackageItemCfgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ItemCfg != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemCfg.Size()))
		n3, err := m.ItemCfg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	return i, nil
}

func (m *AddPackageItemCfgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddPackageItemCfgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ItemCfg != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemCfg.Size()))
		n4, err := m.ItemCfg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	return i, nil
}

func (m *ModPackageItemCfgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModPackageItemCfgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ItemCfg != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemCfg.Size()))
		n5, err := m.ItemCfg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	return i, nil
}

func (m *ModPackageItemCfgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModPackageItemCfgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *DelPackageItemCfgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelPackageItemCfgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BgId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.BgId))
	}
	if m.BgItemId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.BgItemId))
	}
	return i, nil
}

func (m *DelPackageItemCfgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelPackageItemCfgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetPackageItemCfgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPackageItemCfgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BgId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.BgId))
	}
	return i, nil
}

func (m *GetPackageItemCfgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPackageItemCfgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ItemCfgList) > 0 {
		for _, msg := range m.ItemCfgList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintBackpack(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GiveUserPackageReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GiveUserPackageReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Uid))
	}
	if m.BgId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.BgId))
	}
	if m.Num != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Num))
	}
	if m.Source != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Source))
	}
	if len(m.OrderId) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.OrderId)))
		i += copy(dAtA[i:], m.OrderId)
	}
	if m.ExpireDuration != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ExpireDuration))
	}
	if len(m.SourceAppId) > 0 {
		dAtA[i] = 0x3a
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.SourceAppId)))
		i += copy(dAtA[i:], m.SourceAppId)
	}
	if m.TotalPrice != 0 {
		dAtA[i] = 0x40
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.TotalPrice))
	}
	if m.OutsideTime != 0 {
		dAtA[i] = 0x48
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.OutsideTime))
	}
	return i, nil
}

func (m *GiveUserPackageResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GiveUserPackageResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *UseBackpackExtraInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UseBackpackExtraInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.TargetUid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.TargetUid))
	}
	if m.ChannelId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ChannelId))
	}
	if m.UseCount != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.UseCount))
	}
	if len(m.DealToken) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.DealToken)))
		i += copy(dAtA[i:], m.DealToken)
	}
	return i, nil
}

func (m *UseBackpackItemReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UseBackpackItemReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Uid))
	}
	if m.ItemType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemType))
	}
	if m.UserItemId != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.UserItemId))
	}
	if m.SourceId != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.SourceId))
	}
	if len(m.OrderId) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.OrderId)))
		i += copy(dAtA[i:], m.OrderId)
	}
	if m.UseCount != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.UseCount))
	}
	if m.OutsideTime != 0 {
		dAtA[i] = 0x38
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.OutsideTime))
	}
	if len(m.OrderIdList) > 0 {
		for _, s := range m.OrderIdList {
			dAtA[i] = 0x42
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if m.ItemPrice != 0 {
		dAtA[i] = 0x48
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemPrice))
	}
	if m.PriceType != 0 {
		dAtA[i] = 0x50
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.PriceType))
	}
	if len(m.TargetUidList) > 0 {
		dAtA7 := make([]byte, len(m.TargetUidList)*10)
		var j6 int
		for _, num := range m.TargetUidList {
			for num >= 1<<7 {
				dAtA7[j6] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j6++
			}
			dAtA7[j6] = uint8(num)
			j6++
		}
		dAtA[i] = 0x5a
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(j6))
		i += copy(dAtA[i:], dAtA7[:j6])
	}
	if m.ExtraInfo != nil {
		dAtA[i] = 0x62
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ExtraInfo.Size()))
		n8, err := m.ExtraInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	if m.UseReasionType != 0 {
		dAtA[i] = 0x68
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.UseReasionType))
	}
	return i, nil
}

func (m *UseBackpackItemResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UseBackpackItemResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Remain != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Remain))
	}
	if len(m.DealToken) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.DealToken)))
		i += copy(dAtA[i:], m.DealToken)
	}
	if m.FinTime != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.FinTime))
	}
	return i, nil
}

func (m *GetUseItemOrderInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUseItemOrderInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.OrderId) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.OrderId)))
		i += copy(dAtA[i:], m.OrderId)
	}
	return i, nil
}

func (m *GetUseItemOrderInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUseItemOrderInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.UseOrderDetail != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.UseOrderDetail.Size()))
		n9, err := m.UseOrderDetail.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	if m.UseOrderExtraInfo != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.UseOrderExtraInfo.Size()))
		n10, err := m.UseOrderExtraInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	return i, nil
}

func (m *ProcBackpackItemTimeoutReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ProcBackpackItemTimeoutReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.UserItemId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.UserItemId))
	}
	if m.Uid != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Uid))
	}
	if m.ItemType != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemType))
	}
	if m.SourceId != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.SourceId))
	}
	if m.ItemCount != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemCount))
	}
	if m.FinTime != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.FinTime))
	}
	return i, nil
}

func (m *ProcBackpackItemTimeoutResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ProcBackpackItemTimeoutResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetUserBackpackReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserBackpackReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Uid))
	}
	return i, nil
}

func (m *GetUserBackpackResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserBackpackResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UserItemList) > 0 {
		for _, msg := range m.UserItemList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintBackpack(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.LastObtainTs != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.LastObtainTs))
	}
	return i, nil
}

func (m *GetUserFuncCardUseReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserFuncCardUseReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Uid))
	}
	return i, nil
}

func (m *GetUserFuncCardUseResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserFuncCardUseResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UserItemList) > 0 {
		for _, msg := range m.UserItemList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintBackpack(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *AddFuncCardCfgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddFuncCardCfgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.CardCfg != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.CardCfg.Size()))
		n11, err := m.CardCfg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	return i, nil
}

func (m *AddFuncCardCfgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddFuncCardCfgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *BatchGetUserFuncCardUseReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetUserFuncCardUseReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		dAtA13 := make([]byte, len(m.UidList)*10)
		var j12 int
		for _, num := range m.UidList {
			for num >= 1<<7 {
				dAtA13[j12] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j12++
			}
			dAtA13[j12] = uint8(num)
			j12++
		}
		dAtA[i] = 0xa
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(j12))
		i += copy(dAtA[i:], dAtA13[:j12])
	}
	return i, nil
}

func (m *BatchGetUserFuncCardUseResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetUserFuncCardUseResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UserItemList) > 0 {
		for _, msg := range m.UserItemList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintBackpack(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *DelFuncCardCfgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelFuncCardCfgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.CardId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.CardId))
	}
	return i, nil
}

func (m *DelFuncCardCfgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelFuncCardCfgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetFuncCardCfgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFuncCardCfgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.CardIdList) > 0 {
		dAtA15 := make([]byte, len(m.CardIdList)*10)
		var j14 int
		for _, num := range m.CardIdList {
			for num >= 1<<7 {
				dAtA15[j14] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j14++
			}
			dAtA15[j14] = uint8(num)
			j14++
		}
		dAtA[i] = 0xa
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(j14))
		i += copy(dAtA[i:], dAtA15[:j14])
	}
	return i, nil
}

func (m *GetFuncCardCfgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFuncCardCfgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.CardCfgList) > 0 {
		for _, msg := range m.CardCfgList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintBackpack(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *CheckUserFuncCardUseReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUserFuncCardUseReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Uid))
	}
	if m.CardType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.CardType))
	}
	return i, nil
}

func (m *CheckUserFuncCardUseResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUserFuncCardUseResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.IsUse {
		dAtA[i] = 0x8
		i++
		if m.IsUse {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	return i, nil
}

func (m *SetUserFuncCardUseReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetUserFuncCardUseReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Uid))
	}
	if m.CardType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.CardType))
	}
	if m.CardId != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.CardId))
	}
	return i, nil
}

func (m *SetUserFuncCardUseResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetUserFuncCardUseResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetUserBackpackLogReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserBackpackLogReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Uid))
	}
	if m.BeginTime != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.BeginTime))
	}
	if m.EndTime != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.EndTime))
	}
	if m.ItemType != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemType))
	}
	if m.SourceId != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.SourceId))
	}
	if m.LogType != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.LogType))
	}
	return i, nil
}

func (m *GetUserBackpackLogResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserBackpackLogResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.LogList) > 0 {
		for _, msg := range m.LogList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintBackpack(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *AddItemCfgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddItemCfgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ItemType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemType))
	}
	if len(m.ItemCfg) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.ItemCfg)))
		i += copy(dAtA[i:], m.ItemCfg)
	}
	return i, nil
}

func (m *AddItemCfgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddItemCfgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ItemType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemType))
	}
	if len(m.ItemCfg) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.ItemCfg)))
		i += copy(dAtA[i:], m.ItemCfg)
	}
	return i, nil
}

func (m *DelItemCfgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelItemCfgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ItemType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemType))
	}
	if len(m.ItemCfg) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.ItemCfg)))
		i += copy(dAtA[i:], m.ItemCfg)
	}
	return i, nil
}

func (m *DelItemCfgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelItemCfgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetItemCfgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetItemCfgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ItemType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemType))
	}
	if len(m.ItemSourceIdList) > 0 {
		dAtA17 := make([]byte, len(m.ItemSourceIdList)*10)
		var j16 int
		for _, num := range m.ItemSourceIdList {
			for num >= 1<<7 {
				dAtA17[j16] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j16++
			}
			dAtA17[j16] = uint8(num)
			j16++
		}
		dAtA[i] = 0x12
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(j16))
		i += copy(dAtA[i:], dAtA17[:j16])
	}
	if m.GetAll {
		dAtA[i] = 0x18
		i++
		if m.GetAll {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	return i, nil
}

func (m *GetItemCfgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetItemCfgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ItemCfgList) > 0 {
		for _, b := range m.ItemCfgList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintBackpack(dAtA, i, uint64(len(b)))
			i += copy(dAtA[i:], b)
		}
	}
	return i, nil
}

func (m *UseItemInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UseItemInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ItemType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemType))
	}
	if m.UserItemId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.UserItemId))
	}
	if m.SourceId != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.SourceId))
	}
	if m.UseCount != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.UseCount))
	}
	if m.TotalPrice != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.TotalPrice))
	}
	return i, nil
}

func (m *TransactionInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TransactionInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.FreezeType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.FreezeType))
	}
	if m.OperTime != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.OperTime))
	}
	if len(m.OrderId) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.OrderId)))
		i += copy(dAtA[i:], m.OrderId)
	}
	if m.ExpireTime != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ExpireTime))
	}
	return i, nil
}

func (m *FreeZeItemReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FreeZeItemReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.TansacationInfo != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.TansacationInfo.Size()))
		n18, err := m.TansacationInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n18
	}
	if len(m.ItemInfoList) > 0 {
		for _, msg := range m.ItemInfoList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintBackpack(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.Uid != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Uid))
	}
	return i, nil
}

func (m *FreeZeItemResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FreeZeItemResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *UserPackageSum) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserPackageSum) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ItemType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemType))
	}
	if m.ItemId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemId))
	}
	if m.ItemCount != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemCount))
	}
	return i, nil
}

func (m *GetUserPackageSumReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserPackageSumReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Uid))
	}
	if m.ItemType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemType))
	}
	if m.ItemId != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemId))
	}
	return i, nil
}

func (m *GetUserPackageSumResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserPackageSumResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ItemSum != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemSum.Size()))
		n19, err := m.ItemSum.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n19
	}
	return i, nil
}

func (m *GetOrderCountByTimeRangeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOrderCountByTimeRangeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BeginTime != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.BeginTime))
	}
	if m.EndTime != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.EndTime))
	}
	if m.LogType != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.LogType))
	}
	if len(m.Paras) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.Paras)))
		i += copy(dAtA[i:], m.Paras)
	}
	return i, nil
}

func (m *GetOrderCountByTimeRangeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOrderCountByTimeRangeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Count != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Count))
	}
	if m.UseCount != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.UseCount))
	}
	if m.Value != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Value))
	}
	return i, nil
}

func (m *GetOrderListByTimeRangeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOrderListByTimeRangeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BeginTime != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.BeginTime))
	}
	if m.EndTime != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.EndTime))
	}
	if m.LogType != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.LogType))
	}
	return i, nil
}

func (m *GetOrderListByTimeRangeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOrderListByTimeRangeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.OrderList) > 0 {
		for _, s := range m.OrderList {
			dAtA[i] = 0xa
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *TransformOpt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TransformOpt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Uid))
	}
	if m.Source != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Source))
	}
	if m.LogType != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.LogType))
	}
	if m.OutsideTime != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.OutsideTime))
	}
	if len(m.OrderId) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.OrderId)))
		i += copy(dAtA[i:], m.OrderId)
	}
	if m.BgId != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.BgId))
	}
	if m.BgNum != 0 {
		dAtA[i] = 0x38
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.BgNum))
	}
	if m.MaterialPrice != 0 {
		dAtA[i] = 0x40
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.MaterialPrice))
	}
	if m.GainPrice != 0 {
		dAtA[i] = 0x48
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.GainPrice))
	}
	return i, nil
}

func (m *ConversionItemReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ConversionItemReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Uid))
	}
	if m.OutsideTime != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.OutsideTime))
	}
	if m.BgId != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.BgId))
	}
	if m.BgNum != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.BgNum))
	}
	if m.Source != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Source))
	}
	if m.MaterialPrice != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.MaterialPrice))
	}
	if m.ConversionPrice != 0 {
		dAtA[i] = 0x38
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ConversionPrice))
	}
	if len(m.OrderId) > 0 {
		dAtA[i] = 0x42
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.OrderId)))
		i += copy(dAtA[i:], m.OrderId)
	}
	if len(m.MaterialItemList) > 0 {
		for _, msg := range m.MaterialItemList {
			dAtA[i] = 0x4a
			i++
			i = encodeVarintBackpack(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ConversionItemResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ConversionItemResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *RollBackUserItemReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RollBackUserItemReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Uid))
	}
	if m.CreateTime != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.CreateTime))
	}
	if len(m.OriginOrderId) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.OriginOrderId)))
		i += copy(dAtA[i:], m.OriginOrderId)
	}
	return i, nil
}

func (m *RollBackUserItemResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RollBackUserItemResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *DeductItem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeductItem) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ItemType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemType))
	}
	if m.SourceId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.SourceId))
	}
	if m.Count != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Count))
	}
	return i, nil
}

func (m *DeductDetail) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeductDetail) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Uid))
	}
	if len(m.ItemList) > 0 {
		for _, msg := range m.ItemList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintBackpack(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.Count != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Count))
	}
	if m.SourceType != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.SourceType))
	}
	if m.IsAllSource {
		dAtA[i] = 0x28
		i++
		if m.IsAllSource {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	return i, nil
}

func (m *BatchDeductUserItemReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchDeductUserItemReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.DeductList) > 0 {
		for _, msg := range m.DeductList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintBackpack(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.Oper) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.Oper)))
		i += copy(dAtA[i:], m.Oper)
	}
	if len(m.OrderId) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.OrderId)))
		i += copy(dAtA[i:], m.OrderId)
	}
	if m.DeductType != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.DeductType))
	}
	return i, nil
}

func (m *DeductResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeductResult) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Uid))
	}
	if len(m.SuccessItemList) > 0 {
		for _, msg := range m.SuccessItemList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintBackpack(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.FailItemList) > 0 {
		for _, msg := range m.FailItemList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintBackpack(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.FailType != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.FailType))
	}
	return i, nil
}

func (m *BatchDeductUserItemResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchDeductUserItemResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.DeductList) > 0 {
		for _, msg := range m.DeductList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintBackpack(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *UseOrderDetail) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UseOrderDetail) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Uid))
	}
	if len(m.OrderId) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.OrderId)))
		i += copy(dAtA[i:], m.OrderId)
	}
	if m.SourceId != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.SourceId))
	}
	if m.ItemType != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemType))
	}
	if m.UseCount != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.UseCount))
	}
	if m.PriceType != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.PriceType))
	}
	if m.CreateTime != 0 {
		dAtA[i] = 0x38
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.CreateTime))
	}
	if m.UserItemId != 0 {
		dAtA[i] = 0x40
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.UserItemId))
	}
	return i, nil
}

func (m *TimeRangeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TimeRangeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BeginTime != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.BeginTime))
	}
	if m.EndTime != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.EndTime))
	}
	if len(m.Params) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.Params)))
		i += copy(dAtA[i:], m.Params)
	}
	return i, nil
}

func (m *CountResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CountResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Count != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Count))
	}
	if m.Value != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Value))
	}
	return i, nil
}

func (m *OrderIdsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OrderIdsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.OrderIds) > 0 {
		for _, s := range m.OrderIds {
			dAtA[i] = 0xa
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func encodeFixed64Backpack(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Backpack(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintBackpack(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *PackageCfg) Size() (n int) {
	var l int
	_ = l
	if m.BgId != 0 {
		n += 1 + sovBackpack(uint64(m.BgId))
	}
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	l = len(m.Desc)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	if m.IsDel {
		n += 2
	}
	return n
}

func (m *PackageItemCfg) Size() (n int) {
	var l int
	_ = l
	if m.BgItemId != 0 {
		n += 1 + sovBackpack(uint64(m.BgItemId))
	}
	if m.BgId != 0 {
		n += 1 + sovBackpack(uint64(m.BgId))
	}
	if m.ItemType != 0 {
		n += 1 + sovBackpack(uint64(m.ItemType))
	}
	if m.SourceId != 0 {
		n += 1 + sovBackpack(uint64(m.SourceId))
	}
	if m.ItemCount != 0 {
		n += 1 + sovBackpack(uint64(m.ItemCount))
	}
	if m.FinTime != 0 {
		n += 1 + sovBackpack(uint64(m.FinTime))
	}
	if m.IsDel {
		n += 2
	}
	if m.Weight != 0 {
		n += 1 + sovBackpack(uint64(m.Weight))
	}
	if m.DynamicFinTime != 0 {
		n += 1 + sovBackpack(uint64(m.DynamicFinTime))
	}
	if m.Months != 0 {
		n += 1 + sovBackpack(uint64(m.Months))
	}
	return n
}

func (m *FuncCardCfg) Size() (n int) {
	var l int
	_ = l
	if m.CardId != 0 {
		n += 1 + sovBackpack(uint64(m.CardId))
	}
	if m.CardType != 0 {
		n += 1 + sovBackpack(uint64(m.CardType))
	}
	l = len(m.CardName)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	l = len(m.CardDesc)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	l = len(m.CardUrl)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	if m.CardTimes != 0 {
		n += 1 + sovBackpack(uint64(m.CardTimes))
	}
	if m.ValidTime != 0 {
		n += 1 + sovBackpack(uint64(m.ValidTime))
	}
	if m.IsDel != 0 {
		n += 1 + sovBackpack(uint64(m.IsDel))
	}
	if m.Uid != 0 {
		n += 1 + sovBackpack(uint64(m.Uid))
	}
	return n
}

func (m *LotteryFragmentCfg) Size() (n int) {
	var l int
	_ = l
	if m.FragmentId != 0 {
		n += 1 + sovBackpack(uint64(m.FragmentId))
	}
	if m.FragmentType != 0 {
		n += 1 + sovBackpack(uint64(m.FragmentType))
	}
	l = len(m.FragmentName)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	l = len(m.FragmentDesc)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	l = len(m.FragmentUrl)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	if m.IsDel != 0 {
		n += 1 + sovBackpack(uint64(m.IsDel))
	}
	if m.FragmentPrice != 0 {
		n += 1 + sovBackpack(uint64(m.FragmentPrice))
	}
	return n
}

func (m *UserBackpackItem) Size() (n int) {
	var l int
	_ = l
	if m.ItemType != 0 {
		n += 1 + sovBackpack(uint64(m.ItemType))
	}
	if m.UserItemId != 0 {
		n += 1 + sovBackpack(uint64(m.UserItemId))
	}
	if m.ItemCount != 0 {
		n += 1 + sovBackpack(uint64(m.ItemCount))
	}
	if m.FinTime != 0 {
		n += 1 + sovBackpack(uint64(m.FinTime))
	}
	if m.SourceId != 0 {
		n += 1 + sovBackpack(uint64(m.SourceId))
	}
	if m.Weight != 0 {
		n += 1 + sovBackpack(uint64(m.Weight))
	}
	if m.ObtainTime != 0 {
		n += 1 + sovBackpack(uint64(m.ObtainTime))
	}
	if m.SourceType != 0 {
		n += 1 + sovBackpack(uint64(m.SourceType))
	}
	if m.FinalItemCount != 0 {
		n += 1 + sovBackpack(uint64(m.FinalItemCount))
	}
	return n
}

func (m *UserBackpackLog) Size() (n int) {
	var l int
	_ = l
	if m.ItemType != 0 {
		n += 1 + sovBackpack(uint64(m.ItemType))
	}
	if m.ItemCount != 0 {
		n += 1 + sovBackpack(uint64(m.ItemCount))
	}
	if m.SourceId != 0 {
		n += 1 + sovBackpack(uint64(m.SourceId))
	}
	if m.LogType != 0 {
		n += 1 + sovBackpack(uint64(m.LogType))
	}
	if m.LogTime != 0 {
		n += 1 + sovBackpack(uint64(m.LogTime))
	}
	return n
}

func (m *AddPackageCfgReq) Size() (n int) {
	var l int
	_ = l
	if m.Cfg != nil {
		l = m.Cfg.Size()
		n += 1 + l + sovBackpack(uint64(l))
	}
	return n
}

func (m *AddPackageCfgResp) Size() (n int) {
	var l int
	_ = l
	if m.Cfg != nil {
		l = m.Cfg.Size()
		n += 1 + l + sovBackpack(uint64(l))
	}
	return n
}

func (m *DelPackageCfgReq) Size() (n int) {
	var l int
	_ = l
	if m.BgId != 0 {
		n += 1 + sovBackpack(uint64(m.BgId))
	}
	return n
}

func (m *DelPackageCfgResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetPackageCfgReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetPackageCfgResp) Size() (n int) {
	var l int
	_ = l
	if len(m.CfgList) > 0 {
		for _, e := range m.CfgList {
			l = e.Size()
			n += 1 + l + sovBackpack(uint64(l))
		}
	}
	return n
}

func (m *AddPackageItemCfgReq) Size() (n int) {
	var l int
	_ = l
	if m.ItemCfg != nil {
		l = m.ItemCfg.Size()
		n += 1 + l + sovBackpack(uint64(l))
	}
	return n
}

func (m *AddPackageItemCfgResp) Size() (n int) {
	var l int
	_ = l
	if m.ItemCfg != nil {
		l = m.ItemCfg.Size()
		n += 1 + l + sovBackpack(uint64(l))
	}
	return n
}

func (m *ModPackageItemCfgReq) Size() (n int) {
	var l int
	_ = l
	if m.ItemCfg != nil {
		l = m.ItemCfg.Size()
		n += 1 + l + sovBackpack(uint64(l))
	}
	return n
}

func (m *ModPackageItemCfgResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *DelPackageItemCfgReq) Size() (n int) {
	var l int
	_ = l
	if m.BgId != 0 {
		n += 1 + sovBackpack(uint64(m.BgId))
	}
	if m.BgItemId != 0 {
		n += 1 + sovBackpack(uint64(m.BgItemId))
	}
	return n
}

func (m *DelPackageItemCfgResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetPackageItemCfgReq) Size() (n int) {
	var l int
	_ = l
	if m.BgId != 0 {
		n += 1 + sovBackpack(uint64(m.BgId))
	}
	return n
}

func (m *GetPackageItemCfgResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ItemCfgList) > 0 {
		for _, e := range m.ItemCfgList {
			l = e.Size()
			n += 1 + l + sovBackpack(uint64(l))
		}
	}
	return n
}

func (m *GiveUserPackageReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovBackpack(uint64(m.Uid))
	}
	if m.BgId != 0 {
		n += 1 + sovBackpack(uint64(m.BgId))
	}
	if m.Num != 0 {
		n += 1 + sovBackpack(uint64(m.Num))
	}
	if m.Source != 0 {
		n += 1 + sovBackpack(uint64(m.Source))
	}
	l = len(m.OrderId)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	if m.ExpireDuration != 0 {
		n += 1 + sovBackpack(uint64(m.ExpireDuration))
	}
	l = len(m.SourceAppId)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	if m.TotalPrice != 0 {
		n += 1 + sovBackpack(uint64(m.TotalPrice))
	}
	if m.OutsideTime != 0 {
		n += 1 + sovBackpack(uint64(m.OutsideTime))
	}
	return n
}

func (m *GiveUserPackageResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *UseBackpackExtraInfo) Size() (n int) {
	var l int
	_ = l
	if m.TargetUid != 0 {
		n += 1 + sovBackpack(uint64(m.TargetUid))
	}
	if m.ChannelId != 0 {
		n += 1 + sovBackpack(uint64(m.ChannelId))
	}
	if m.UseCount != 0 {
		n += 1 + sovBackpack(uint64(m.UseCount))
	}
	l = len(m.DealToken)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	return n
}

func (m *UseBackpackItemReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovBackpack(uint64(m.Uid))
	}
	if m.ItemType != 0 {
		n += 1 + sovBackpack(uint64(m.ItemType))
	}
	if m.UserItemId != 0 {
		n += 1 + sovBackpack(uint64(m.UserItemId))
	}
	if m.SourceId != 0 {
		n += 1 + sovBackpack(uint64(m.SourceId))
	}
	l = len(m.OrderId)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	if m.UseCount != 0 {
		n += 1 + sovBackpack(uint64(m.UseCount))
	}
	if m.OutsideTime != 0 {
		n += 1 + sovBackpack(uint64(m.OutsideTime))
	}
	if len(m.OrderIdList) > 0 {
		for _, s := range m.OrderIdList {
			l = len(s)
			n += 1 + l + sovBackpack(uint64(l))
		}
	}
	if m.ItemPrice != 0 {
		n += 1 + sovBackpack(uint64(m.ItemPrice))
	}
	if m.PriceType != 0 {
		n += 1 + sovBackpack(uint64(m.PriceType))
	}
	if len(m.TargetUidList) > 0 {
		l = 0
		for _, e := range m.TargetUidList {
			l += sovBackpack(uint64(e))
		}
		n += 1 + sovBackpack(uint64(l)) + l
	}
	if m.ExtraInfo != nil {
		l = m.ExtraInfo.Size()
		n += 1 + l + sovBackpack(uint64(l))
	}
	if m.UseReasionType != 0 {
		n += 1 + sovBackpack(uint64(m.UseReasionType))
	}
	return n
}

func (m *UseBackpackItemResp) Size() (n int) {
	var l int
	_ = l
	if m.Remain != 0 {
		n += 1 + sovBackpack(uint64(m.Remain))
	}
	l = len(m.DealToken)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	if m.FinTime != 0 {
		n += 1 + sovBackpack(uint64(m.FinTime))
	}
	return n
}

func (m *GetUseItemOrderInfoReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.OrderId)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	return n
}

func (m *GetUseItemOrderInfoResp) Size() (n int) {
	var l int
	_ = l
	if m.UseOrderDetail != nil {
		l = m.UseOrderDetail.Size()
		n += 1 + l + sovBackpack(uint64(l))
	}
	if m.UseOrderExtraInfo != nil {
		l = m.UseOrderExtraInfo.Size()
		n += 1 + l + sovBackpack(uint64(l))
	}
	return n
}

func (m *ProcBackpackItemTimeoutReq) Size() (n int) {
	var l int
	_ = l
	if m.UserItemId != 0 {
		n += 1 + sovBackpack(uint64(m.UserItemId))
	}
	if m.Uid != 0 {
		n += 1 + sovBackpack(uint64(m.Uid))
	}
	if m.ItemType != 0 {
		n += 1 + sovBackpack(uint64(m.ItemType))
	}
	if m.SourceId != 0 {
		n += 1 + sovBackpack(uint64(m.SourceId))
	}
	if m.ItemCount != 0 {
		n += 1 + sovBackpack(uint64(m.ItemCount))
	}
	if m.FinTime != 0 {
		n += 1 + sovBackpack(uint64(m.FinTime))
	}
	return n
}

func (m *ProcBackpackItemTimeoutResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetUserBackpackReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovBackpack(uint64(m.Uid))
	}
	return n
}

func (m *GetUserBackpackResp) Size() (n int) {
	var l int
	_ = l
	if len(m.UserItemList) > 0 {
		for _, e := range m.UserItemList {
			l = e.Size()
			n += 1 + l + sovBackpack(uint64(l))
		}
	}
	if m.LastObtainTs != 0 {
		n += 1 + sovBackpack(uint64(m.LastObtainTs))
	}
	return n
}

func (m *GetUserFuncCardUseReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovBackpack(uint64(m.Uid))
	}
	return n
}

func (m *GetUserFuncCardUseResp) Size() (n int) {
	var l int
	_ = l
	if len(m.UserItemList) > 0 {
		for _, e := range m.UserItemList {
			l = e.Size()
			n += 1 + l + sovBackpack(uint64(l))
		}
	}
	return n
}

func (m *AddFuncCardCfgReq) Size() (n int) {
	var l int
	_ = l
	if m.CardCfg != nil {
		l = m.CardCfg.Size()
		n += 1 + l + sovBackpack(uint64(l))
	}
	return n
}

func (m *AddFuncCardCfgResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *BatchGetUserFuncCardUseReq) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		l = 0
		for _, e := range m.UidList {
			l += sovBackpack(uint64(e))
		}
		n += 1 + sovBackpack(uint64(l)) + l
	}
	return n
}

func (m *BatchGetUserFuncCardUseResp) Size() (n int) {
	var l int
	_ = l
	if len(m.UserItemList) > 0 {
		for _, e := range m.UserItemList {
			l = e.Size()
			n += 1 + l + sovBackpack(uint64(l))
		}
	}
	return n
}

func (m *DelFuncCardCfgReq) Size() (n int) {
	var l int
	_ = l
	if m.CardId != 0 {
		n += 1 + sovBackpack(uint64(m.CardId))
	}
	return n
}

func (m *DelFuncCardCfgResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetFuncCardCfgReq) Size() (n int) {
	var l int
	_ = l
	if len(m.CardIdList) > 0 {
		l = 0
		for _, e := range m.CardIdList {
			l += sovBackpack(uint64(e))
		}
		n += 1 + sovBackpack(uint64(l)) + l
	}
	return n
}

func (m *GetFuncCardCfgResp) Size() (n int) {
	var l int
	_ = l
	if len(m.CardCfgList) > 0 {
		for _, e := range m.CardCfgList {
			l = e.Size()
			n += 1 + l + sovBackpack(uint64(l))
		}
	}
	return n
}

func (m *CheckUserFuncCardUseReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovBackpack(uint64(m.Uid))
	}
	if m.CardType != 0 {
		n += 1 + sovBackpack(uint64(m.CardType))
	}
	return n
}

func (m *CheckUserFuncCardUseResp) Size() (n int) {
	var l int
	_ = l
	if m.IsUse {
		n += 2
	}
	return n
}

func (m *SetUserFuncCardUseReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovBackpack(uint64(m.Uid))
	}
	if m.CardType != 0 {
		n += 1 + sovBackpack(uint64(m.CardType))
	}
	if m.CardId != 0 {
		n += 1 + sovBackpack(uint64(m.CardId))
	}
	return n
}

func (m *SetUserFuncCardUseResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetUserBackpackLogReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovBackpack(uint64(m.Uid))
	}
	if m.BeginTime != 0 {
		n += 1 + sovBackpack(uint64(m.BeginTime))
	}
	if m.EndTime != 0 {
		n += 1 + sovBackpack(uint64(m.EndTime))
	}
	if m.ItemType != 0 {
		n += 1 + sovBackpack(uint64(m.ItemType))
	}
	if m.SourceId != 0 {
		n += 1 + sovBackpack(uint64(m.SourceId))
	}
	if m.LogType != 0 {
		n += 1 + sovBackpack(uint64(m.LogType))
	}
	return n
}

func (m *GetUserBackpackLogResp) Size() (n int) {
	var l int
	_ = l
	if len(m.LogList) > 0 {
		for _, e := range m.LogList {
			l = e.Size()
			n += 1 + l + sovBackpack(uint64(l))
		}
	}
	return n
}

func (m *AddItemCfgReq) Size() (n int) {
	var l int
	_ = l
	if m.ItemType != 0 {
		n += 1 + sovBackpack(uint64(m.ItemType))
	}
	l = len(m.ItemCfg)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	return n
}

func (m *AddItemCfgResp) Size() (n int) {
	var l int
	_ = l
	if m.ItemType != 0 {
		n += 1 + sovBackpack(uint64(m.ItemType))
	}
	l = len(m.ItemCfg)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	return n
}

func (m *DelItemCfgReq) Size() (n int) {
	var l int
	_ = l
	if m.ItemType != 0 {
		n += 1 + sovBackpack(uint64(m.ItemType))
	}
	l = len(m.ItemCfg)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	return n
}

func (m *DelItemCfgResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetItemCfgReq) Size() (n int) {
	var l int
	_ = l
	if m.ItemType != 0 {
		n += 1 + sovBackpack(uint64(m.ItemType))
	}
	if len(m.ItemSourceIdList) > 0 {
		l = 0
		for _, e := range m.ItemSourceIdList {
			l += sovBackpack(uint64(e))
		}
		n += 1 + sovBackpack(uint64(l)) + l
	}
	if m.GetAll {
		n += 2
	}
	return n
}

func (m *GetItemCfgResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ItemCfgList) > 0 {
		for _, b := range m.ItemCfgList {
			l = len(b)
			n += 1 + l + sovBackpack(uint64(l))
		}
	}
	return n
}

func (m *UseItemInfo) Size() (n int) {
	var l int
	_ = l
	if m.ItemType != 0 {
		n += 1 + sovBackpack(uint64(m.ItemType))
	}
	if m.UserItemId != 0 {
		n += 1 + sovBackpack(uint64(m.UserItemId))
	}
	if m.SourceId != 0 {
		n += 1 + sovBackpack(uint64(m.SourceId))
	}
	if m.UseCount != 0 {
		n += 1 + sovBackpack(uint64(m.UseCount))
	}
	if m.TotalPrice != 0 {
		n += 1 + sovBackpack(uint64(m.TotalPrice))
	}
	return n
}

func (m *TransactionInfo) Size() (n int) {
	var l int
	_ = l
	if m.FreezeType != 0 {
		n += 1 + sovBackpack(uint64(m.FreezeType))
	}
	if m.OperTime != 0 {
		n += 1 + sovBackpack(uint64(m.OperTime))
	}
	l = len(m.OrderId)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	if m.ExpireTime != 0 {
		n += 1 + sovBackpack(uint64(m.ExpireTime))
	}
	return n
}

func (m *FreeZeItemReq) Size() (n int) {
	var l int
	_ = l
	if m.TansacationInfo != nil {
		l = m.TansacationInfo.Size()
		n += 1 + l + sovBackpack(uint64(l))
	}
	if len(m.ItemInfoList) > 0 {
		for _, e := range m.ItemInfoList {
			l = e.Size()
			n += 1 + l + sovBackpack(uint64(l))
		}
	}
	if m.Uid != 0 {
		n += 1 + sovBackpack(uint64(m.Uid))
	}
	return n
}

func (m *FreeZeItemResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *UserPackageSum) Size() (n int) {
	var l int
	_ = l
	if m.ItemType != 0 {
		n += 1 + sovBackpack(uint64(m.ItemType))
	}
	if m.ItemId != 0 {
		n += 1 + sovBackpack(uint64(m.ItemId))
	}
	if m.ItemCount != 0 {
		n += 1 + sovBackpack(uint64(m.ItemCount))
	}
	return n
}

func (m *GetUserPackageSumReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovBackpack(uint64(m.Uid))
	}
	if m.ItemType != 0 {
		n += 1 + sovBackpack(uint64(m.ItemType))
	}
	if m.ItemId != 0 {
		n += 1 + sovBackpack(uint64(m.ItemId))
	}
	return n
}

func (m *GetUserPackageSumResp) Size() (n int) {
	var l int
	_ = l
	if m.ItemSum != nil {
		l = m.ItemSum.Size()
		n += 1 + l + sovBackpack(uint64(l))
	}
	return n
}

func (m *GetOrderCountByTimeRangeReq) Size() (n int) {
	var l int
	_ = l
	if m.BeginTime != 0 {
		n += 1 + sovBackpack(uint64(m.BeginTime))
	}
	if m.EndTime != 0 {
		n += 1 + sovBackpack(uint64(m.EndTime))
	}
	if m.LogType != 0 {
		n += 1 + sovBackpack(uint64(m.LogType))
	}
	l = len(m.Paras)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	return n
}

func (m *GetOrderCountByTimeRangeResp) Size() (n int) {
	var l int
	_ = l
	if m.Count != 0 {
		n += 1 + sovBackpack(uint64(m.Count))
	}
	if m.UseCount != 0 {
		n += 1 + sovBackpack(uint64(m.UseCount))
	}
	if m.Value != 0 {
		n += 1 + sovBackpack(uint64(m.Value))
	}
	return n
}

func (m *GetOrderListByTimeRangeReq) Size() (n int) {
	var l int
	_ = l
	if m.BeginTime != 0 {
		n += 1 + sovBackpack(uint64(m.BeginTime))
	}
	if m.EndTime != 0 {
		n += 1 + sovBackpack(uint64(m.EndTime))
	}
	if m.LogType != 0 {
		n += 1 + sovBackpack(uint64(m.LogType))
	}
	return n
}

func (m *GetOrderListByTimeRangeResp) Size() (n int) {
	var l int
	_ = l
	if len(m.OrderList) > 0 {
		for _, s := range m.OrderList {
			l = len(s)
			n += 1 + l + sovBackpack(uint64(l))
		}
	}
	return n
}

func (m *TransformOpt) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovBackpack(uint64(m.Uid))
	}
	if m.Source != 0 {
		n += 1 + sovBackpack(uint64(m.Source))
	}
	if m.LogType != 0 {
		n += 1 + sovBackpack(uint64(m.LogType))
	}
	if m.OutsideTime != 0 {
		n += 1 + sovBackpack(uint64(m.OutsideTime))
	}
	l = len(m.OrderId)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	if m.BgId != 0 {
		n += 1 + sovBackpack(uint64(m.BgId))
	}
	if m.BgNum != 0 {
		n += 1 + sovBackpack(uint64(m.BgNum))
	}
	if m.MaterialPrice != 0 {
		n += 1 + sovBackpack(uint64(m.MaterialPrice))
	}
	if m.GainPrice != 0 {
		n += 1 + sovBackpack(uint64(m.GainPrice))
	}
	return n
}

func (m *ConversionItemReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovBackpack(uint64(m.Uid))
	}
	if m.OutsideTime != 0 {
		n += 1 + sovBackpack(uint64(m.OutsideTime))
	}
	if m.BgId != 0 {
		n += 1 + sovBackpack(uint64(m.BgId))
	}
	if m.BgNum != 0 {
		n += 1 + sovBackpack(uint64(m.BgNum))
	}
	if m.Source != 0 {
		n += 1 + sovBackpack(uint64(m.Source))
	}
	if m.MaterialPrice != 0 {
		n += 1 + sovBackpack(uint64(m.MaterialPrice))
	}
	if m.ConversionPrice != 0 {
		n += 1 + sovBackpack(uint64(m.ConversionPrice))
	}
	l = len(m.OrderId)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	if len(m.MaterialItemList) > 0 {
		for _, e := range m.MaterialItemList {
			l = e.Size()
			n += 1 + l + sovBackpack(uint64(l))
		}
	}
	return n
}

func (m *ConversionItemResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *RollBackUserItemReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovBackpack(uint64(m.Uid))
	}
	if m.CreateTime != 0 {
		n += 1 + sovBackpack(uint64(m.CreateTime))
	}
	l = len(m.OriginOrderId)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	return n
}

func (m *RollBackUserItemResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *DeductItem) Size() (n int) {
	var l int
	_ = l
	if m.ItemType != 0 {
		n += 1 + sovBackpack(uint64(m.ItemType))
	}
	if m.SourceId != 0 {
		n += 1 + sovBackpack(uint64(m.SourceId))
	}
	if m.Count != 0 {
		n += 1 + sovBackpack(uint64(m.Count))
	}
	return n
}

func (m *DeductDetail) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovBackpack(uint64(m.Uid))
	}
	if len(m.ItemList) > 0 {
		for _, e := range m.ItemList {
			l = e.Size()
			n += 1 + l + sovBackpack(uint64(l))
		}
	}
	if m.Count != 0 {
		n += 1 + sovBackpack(uint64(m.Count))
	}
	if m.SourceType != 0 {
		n += 1 + sovBackpack(uint64(m.SourceType))
	}
	if m.IsAllSource {
		n += 2
	}
	return n
}

func (m *BatchDeductUserItemReq) Size() (n int) {
	var l int
	_ = l
	if len(m.DeductList) > 0 {
		for _, e := range m.DeductList {
			l = e.Size()
			n += 1 + l + sovBackpack(uint64(l))
		}
	}
	l = len(m.Oper)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	l = len(m.OrderId)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	if m.DeductType != 0 {
		n += 1 + sovBackpack(uint64(m.DeductType))
	}
	return n
}

func (m *DeductResult) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovBackpack(uint64(m.Uid))
	}
	if len(m.SuccessItemList) > 0 {
		for _, e := range m.SuccessItemList {
			l = e.Size()
			n += 1 + l + sovBackpack(uint64(l))
		}
	}
	if len(m.FailItemList) > 0 {
		for _, e := range m.FailItemList {
			l = e.Size()
			n += 1 + l + sovBackpack(uint64(l))
		}
	}
	if m.FailType != 0 {
		n += 1 + sovBackpack(uint64(m.FailType))
	}
	return n
}

func (m *BatchDeductUserItemResp) Size() (n int) {
	var l int
	_ = l
	if len(m.DeductList) > 0 {
		for _, e := range m.DeductList {
			l = e.Size()
			n += 1 + l + sovBackpack(uint64(l))
		}
	}
	return n
}

func (m *UseOrderDetail) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovBackpack(uint64(m.Uid))
	}
	l = len(m.OrderId)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	if m.SourceId != 0 {
		n += 1 + sovBackpack(uint64(m.SourceId))
	}
	if m.ItemType != 0 {
		n += 1 + sovBackpack(uint64(m.ItemType))
	}
	if m.UseCount != 0 {
		n += 1 + sovBackpack(uint64(m.UseCount))
	}
	if m.PriceType != 0 {
		n += 1 + sovBackpack(uint64(m.PriceType))
	}
	if m.CreateTime != 0 {
		n += 1 + sovBackpack(uint64(m.CreateTime))
	}
	if m.UserItemId != 0 {
		n += 1 + sovBackpack(uint64(m.UserItemId))
	}
	return n
}

func (m *TimeRangeReq) Size() (n int) {
	var l int
	_ = l
	if m.BeginTime != 0 {
		n += 1 + sovBackpack(uint64(m.BeginTime))
	}
	if m.EndTime != 0 {
		n += 1 + sovBackpack(uint64(m.EndTime))
	}
	l = len(m.Params)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	return n
}

func (m *CountResp) Size() (n int) {
	var l int
	_ = l
	if m.Count != 0 {
		n += 1 + sovBackpack(uint64(m.Count))
	}
	if m.Value != 0 {
		n += 1 + sovBackpack(uint64(m.Value))
	}
	return n
}

func (m *OrderIdsResp) Size() (n int) {
	var l int
	_ = l
	if len(m.OrderIds) > 0 {
		for _, s := range m.OrderIds {
			l = len(s)
			n += 1 + l + sovBackpack(uint64(l))
		}
	}
	return n
}

func sovBackpack(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozBackpack(x uint64) (n int) {
	return sovBackpack(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *PackageCfg) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PackageCfg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PackageCfg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BgId", wireType)
			}
			m.BgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Desc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Desc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsDel", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsDel = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PackageItemCfg) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PackageItemCfg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PackageItemCfg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BgItemId", wireType)
			}
			m.BgItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BgItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BgId", wireType)
			}
			m.BgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceId", wireType)
			}
			m.SourceId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemCount", wireType)
			}
			m.ItemCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FinTime", wireType)
			}
			m.FinTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FinTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsDel", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsDel = bool(v != 0)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Weight", wireType)
			}
			m.Weight = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Weight |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DynamicFinTime", wireType)
			}
			m.DynamicFinTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DynamicFinTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Months", wireType)
			}
			m.Months = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Months |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FuncCardCfg) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FuncCardCfg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FuncCardCfg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardId", wireType)
			}
			m.CardId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CardId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardType", wireType)
			}
			m.CardType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CardType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CardName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CardDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CardUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardTimes", wireType)
			}
			m.CardTimes = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CardTimes |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ValidTime", wireType)
			}
			m.ValidTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ValidTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsDel", wireType)
			}
			m.IsDel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IsDel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LotteryFragmentCfg) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LotteryFragmentCfg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LotteryFragmentCfg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FragmentId", wireType)
			}
			m.FragmentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FragmentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FragmentType", wireType)
			}
			m.FragmentType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FragmentType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FragmentName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FragmentName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FragmentDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FragmentDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FragmentUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FragmentUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsDel", wireType)
			}
			m.IsDel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IsDel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FragmentPrice", wireType)
			}
			m.FragmentPrice = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FragmentPrice |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserBackpackItem) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserBackpackItem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserBackpackItem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserItemId", wireType)
			}
			m.UserItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UserItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemCount", wireType)
			}
			m.ItemCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FinTime", wireType)
			}
			m.FinTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FinTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceId", wireType)
			}
			m.SourceId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Weight", wireType)
			}
			m.Weight = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Weight |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ObtainTime", wireType)
			}
			m.ObtainTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ObtainTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceType", wireType)
			}
			m.SourceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FinalItemCount", wireType)
			}
			m.FinalItemCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FinalItemCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserBackpackLog) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserBackpackLog: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserBackpackLog: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemCount", wireType)
			}
			m.ItemCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceId", wireType)
			}
			m.SourceId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LogType", wireType)
			}
			m.LogType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LogType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LogTime", wireType)
			}
			m.LogTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LogTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddPackageCfgReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddPackageCfgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddPackageCfgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Cfg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Cfg == nil {
				m.Cfg = &PackageCfg{}
			}
			if err := m.Cfg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddPackageCfgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddPackageCfgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddPackageCfgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Cfg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Cfg == nil {
				m.Cfg = &PackageCfg{}
			}
			if err := m.Cfg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelPackageCfgReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelPackageCfgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelPackageCfgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BgId", wireType)
			}
			m.BgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelPackageCfgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelPackageCfgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelPackageCfgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPackageCfgReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPackageCfgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPackageCfgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPackageCfgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPackageCfgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPackageCfgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CfgList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CfgList = append(m.CfgList, &PackageCfg{})
			if err := m.CfgList[len(m.CfgList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddPackageItemCfgReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddPackageItemCfgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddPackageItemCfgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemCfg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ItemCfg == nil {
				m.ItemCfg = &PackageItemCfg{}
			}
			if err := m.ItemCfg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddPackageItemCfgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddPackageItemCfgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddPackageItemCfgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemCfg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ItemCfg == nil {
				m.ItemCfg = &PackageItemCfg{}
			}
			if err := m.ItemCfg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModPackageItemCfgReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModPackageItemCfgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModPackageItemCfgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemCfg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ItemCfg == nil {
				m.ItemCfg = &PackageItemCfg{}
			}
			if err := m.ItemCfg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModPackageItemCfgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModPackageItemCfgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModPackageItemCfgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelPackageItemCfgReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelPackageItemCfgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelPackageItemCfgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BgId", wireType)
			}
			m.BgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BgItemId", wireType)
			}
			m.BgItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BgItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelPackageItemCfgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelPackageItemCfgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelPackageItemCfgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPackageItemCfgReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPackageItemCfgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPackageItemCfgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BgId", wireType)
			}
			m.BgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPackageItemCfgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPackageItemCfgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPackageItemCfgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemCfgList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemCfgList = append(m.ItemCfgList, &PackageItemCfg{})
			if err := m.ItemCfgList[len(m.ItemCfgList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GiveUserPackageReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GiveUserPackageReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GiveUserPackageReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BgId", wireType)
			}
			m.BgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Num", wireType)
			}
			m.Num = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Num |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			m.Source = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Source |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpireDuration", wireType)
			}
			m.ExpireDuration = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpireDuration |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceAppId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SourceAppId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalPrice", wireType)
			}
			m.TotalPrice = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalPrice |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OutsideTime", wireType)
			}
			m.OutsideTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OutsideTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GiveUserPackageResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GiveUserPackageResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GiveUserPackageResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UseBackpackExtraInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UseBackpackExtraInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UseBackpackExtraInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UseCount", wireType)
			}
			m.UseCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UseCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DealToken", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DealToken = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UseBackpackItemReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UseBackpackItemReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UseBackpackItemReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserItemId", wireType)
			}
			m.UserItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UserItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceId", wireType)
			}
			m.SourceId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UseCount", wireType)
			}
			m.UseCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UseCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OutsideTime", wireType)
			}
			m.OutsideTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OutsideTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderIdList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderIdList = append(m.OrderIdList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemPrice", wireType)
			}
			m.ItemPrice = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemPrice |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PriceType", wireType)
			}
			m.PriceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PriceType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBackpack
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.TargetUidList = append(m.TargetUidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBackpack
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthBackpack
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowBackpack
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.TargetUidList = append(m.TargetUidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUidList", wireType)
			}
		case 12:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtraInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ExtraInfo == nil {
				m.ExtraInfo = &UseBackpackExtraInfo{}
			}
			if err := m.ExtraInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 13:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UseReasionType", wireType)
			}
			m.UseReasionType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UseReasionType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UseBackpackItemResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UseBackpackItemResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UseBackpackItemResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Remain", wireType)
			}
			m.Remain = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Remain |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DealToken", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DealToken = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FinTime", wireType)
			}
			m.FinTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FinTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUseItemOrderInfoReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUseItemOrderInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUseItemOrderInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUseItemOrderInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUseItemOrderInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUseItemOrderInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UseOrderDetail", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UseOrderDetail == nil {
				m.UseOrderDetail = &UseOrderDetail{}
			}
			if err := m.UseOrderDetail.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UseOrderExtraInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UseOrderExtraInfo == nil {
				m.UseOrderExtraInfo = &UseBackpackExtraInfo{}
			}
			if err := m.UseOrderExtraInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ProcBackpackItemTimeoutReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ProcBackpackItemTimeoutReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ProcBackpackItemTimeoutReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserItemId", wireType)
			}
			m.UserItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UserItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceId", wireType)
			}
			m.SourceId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemCount", wireType)
			}
			m.ItemCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FinTime", wireType)
			}
			m.FinTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FinTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ProcBackpackItemTimeoutResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ProcBackpackItemTimeoutResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ProcBackpackItemTimeoutResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserBackpackReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserBackpackReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserBackpackReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserBackpackResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserBackpackResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserBackpackResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserItemList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserItemList = append(m.UserItemList, &UserBackpackItem{})
			if err := m.UserItemList[len(m.UserItemList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastObtainTs", wireType)
			}
			m.LastObtainTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastObtainTs |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserFuncCardUseReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserFuncCardUseReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserFuncCardUseReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserFuncCardUseResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserFuncCardUseResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserFuncCardUseResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserItemList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserItemList = append(m.UserItemList, &FuncCardCfg{})
			if err := m.UserItemList[len(m.UserItemList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddFuncCardCfgReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddFuncCardCfgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddFuncCardCfgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardCfg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.CardCfg == nil {
				m.CardCfg = &FuncCardCfg{}
			}
			if err := m.CardCfg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddFuncCardCfgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddFuncCardCfgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddFuncCardCfgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetUserFuncCardUseReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetUserFuncCardUseReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetUserFuncCardUseReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBackpack
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBackpack
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthBackpack
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowBackpack
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetUserFuncCardUseResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetUserFuncCardUseResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetUserFuncCardUseResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserItemList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserItemList = append(m.UserItemList, &FuncCardCfg{})
			if err := m.UserItemList[len(m.UserItemList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelFuncCardCfgReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelFuncCardCfgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelFuncCardCfgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardId", wireType)
			}
			m.CardId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CardId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelFuncCardCfgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelFuncCardCfgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelFuncCardCfgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFuncCardCfgReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFuncCardCfgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFuncCardCfgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBackpack
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.CardIdList = append(m.CardIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBackpack
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthBackpack
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowBackpack
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.CardIdList = append(m.CardIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFuncCardCfgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFuncCardCfgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFuncCardCfgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardCfgList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CardCfgList = append(m.CardCfgList, &FuncCardCfg{})
			if err := m.CardCfgList[len(m.CardCfgList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckUserFuncCardUseReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckUserFuncCardUseReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckUserFuncCardUseReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardType", wireType)
			}
			m.CardType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CardType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckUserFuncCardUseResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckUserFuncCardUseResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckUserFuncCardUseResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsUse", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsUse = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetUserFuncCardUseReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetUserFuncCardUseReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetUserFuncCardUseReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardType", wireType)
			}
			m.CardType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CardType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardId", wireType)
			}
			m.CardId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CardId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetUserFuncCardUseResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetUserFuncCardUseResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetUserFuncCardUseResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserBackpackLogReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserBackpackLogReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserBackpackLogReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceId", wireType)
			}
			m.SourceId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LogType", wireType)
			}
			m.LogType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LogType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserBackpackLogResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserBackpackLogResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserBackpackLogResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LogList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LogList = append(m.LogList, &UserBackpackLog{})
			if err := m.LogList[len(m.LogList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddItemCfgReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddItemCfgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddItemCfgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemCfg", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemCfg = append(m.ItemCfg[:0], dAtA[iNdEx:postIndex]...)
			if m.ItemCfg == nil {
				m.ItemCfg = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddItemCfgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddItemCfgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddItemCfgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemCfg", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemCfg = append(m.ItemCfg[:0], dAtA[iNdEx:postIndex]...)
			if m.ItemCfg == nil {
				m.ItemCfg = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelItemCfgReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelItemCfgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelItemCfgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemCfg", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemCfg = append(m.ItemCfg[:0], dAtA[iNdEx:postIndex]...)
			if m.ItemCfg == nil {
				m.ItemCfg = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelItemCfgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelItemCfgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelItemCfgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetItemCfgReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetItemCfgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetItemCfgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBackpack
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ItemSourceIdList = append(m.ItemSourceIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBackpack
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthBackpack
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowBackpack
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ItemSourceIdList = append(m.ItemSourceIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemSourceIdList", wireType)
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GetAll", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.GetAll = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetItemCfgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetItemCfgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetItemCfgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemCfgList", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemCfgList = append(m.ItemCfgList, make([]byte, postIndex-iNdEx))
			copy(m.ItemCfgList[len(m.ItemCfgList)-1], dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UseItemInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UseItemInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UseItemInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserItemId", wireType)
			}
			m.UserItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UserItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceId", wireType)
			}
			m.SourceId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UseCount", wireType)
			}
			m.UseCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UseCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalPrice", wireType)
			}
			m.TotalPrice = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalPrice |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TransactionInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TransactionInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TransactionInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FreezeType", wireType)
			}
			m.FreezeType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FreezeType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperTime", wireType)
			}
			m.OperTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OperTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpireTime", wireType)
			}
			m.ExpireTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpireTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FreeZeItemReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FreeZeItemReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FreeZeItemReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TansacationInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.TansacationInfo == nil {
				m.TansacationInfo = &TransactionInfo{}
			}
			if err := m.TansacationInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemInfoList = append(m.ItemInfoList, &UseItemInfo{})
			if err := m.ItemInfoList[len(m.ItemInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FreeZeItemResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FreeZeItemResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FreeZeItemResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserPackageSum) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserPackageSum: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserPackageSum: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			m.ItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemCount", wireType)
			}
			m.ItemCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserPackageSumReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserPackageSumReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserPackageSumReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			m.ItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserPackageSumResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserPackageSumResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserPackageSumResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemSum", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ItemSum == nil {
				m.ItemSum = &UserPackageSum{}
			}
			if err := m.ItemSum.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOrderCountByTimeRangeReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOrderCountByTimeRangeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOrderCountByTimeRangeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LogType", wireType)
			}
			m.LogType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LogType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Paras", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Paras = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOrderCountByTimeRangeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOrderCountByTimeRangeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOrderCountByTimeRangeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UseCount", wireType)
			}
			m.UseCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UseCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			m.Value = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Value |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOrderListByTimeRangeReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOrderListByTimeRangeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOrderListByTimeRangeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LogType", wireType)
			}
			m.LogType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LogType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOrderListByTimeRangeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOrderListByTimeRangeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOrderListByTimeRangeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderList = append(m.OrderList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TransformOpt) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TransformOpt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TransformOpt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			m.Source = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Source |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LogType", wireType)
			}
			m.LogType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LogType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OutsideTime", wireType)
			}
			m.OutsideTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OutsideTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BgId", wireType)
			}
			m.BgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BgNum", wireType)
			}
			m.BgNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BgNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MaterialPrice", wireType)
			}
			m.MaterialPrice = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MaterialPrice |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GainPrice", wireType)
			}
			m.GainPrice = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GainPrice |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ConversionItemReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ConversionItemReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ConversionItemReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OutsideTime", wireType)
			}
			m.OutsideTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OutsideTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BgId", wireType)
			}
			m.BgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BgNum", wireType)
			}
			m.BgNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BgNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			m.Source = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Source |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MaterialPrice", wireType)
			}
			m.MaterialPrice = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MaterialPrice |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConversionPrice", wireType)
			}
			m.ConversionPrice = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConversionPrice |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MaterialItemList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MaterialItemList = append(m.MaterialItemList, &UseItemInfo{})
			if err := m.MaterialItemList[len(m.MaterialItemList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ConversionItemResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ConversionItemResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ConversionItemResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RollBackUserItemReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RollBackUserItemReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RollBackUserItemReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateTime", wireType)
			}
			m.CreateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OriginOrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OriginOrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RollBackUserItemResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RollBackUserItemResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RollBackUserItemResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeductItem) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeductItem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeductItem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceId", wireType)
			}
			m.SourceId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeductDetail) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeductDetail: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeductDetail: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemList = append(m.ItemList, &DeductItem{})
			if err := m.ItemList[len(m.ItemList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceType", wireType)
			}
			m.SourceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsAllSource", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsAllSource = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchDeductUserItemReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchDeductUserItemReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchDeductUserItemReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeductList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeductList = append(m.DeductList, &DeductDetail{})
			if err := m.DeductList[len(m.DeductList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Oper", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Oper = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeductType", wireType)
			}
			m.DeductType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DeductType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeductResult) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeductResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeductResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SuccessItemList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SuccessItemList = append(m.SuccessItemList, &DeductItem{})
			if err := m.SuccessItemList[len(m.SuccessItemList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FailItemList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FailItemList = append(m.FailItemList, &DeductItem{})
			if err := m.FailItemList[len(m.FailItemList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FailType", wireType)
			}
			m.FailType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FailType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchDeductUserItemResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchDeductUserItemResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchDeductUserItemResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeductList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeductList = append(m.DeductList, &DeductResult{})
			if err := m.DeductList[len(m.DeductList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UseOrderDetail) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UseOrderDetail: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UseOrderDetail: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceId", wireType)
			}
			m.SourceId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UseCount", wireType)
			}
			m.UseCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UseCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PriceType", wireType)
			}
			m.PriceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PriceType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateTime", wireType)
			}
			m.CreateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserItemId", wireType)
			}
			m.UserItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UserItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TimeRangeReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TimeRangeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TimeRangeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Params", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Params = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CountResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CountResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CountResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			m.Value = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Value |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *OrderIdsResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: OrderIdsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: OrderIdsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderIds", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderIds = append(m.OrderIds, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipBackpack(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthBackpack
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowBackpack
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipBackpack(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthBackpack = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowBackpack   = fmt2.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("service/appsvr/src/backpacksvr/backpack.proto", fileDescriptorBackpack)
}

var fileDescriptorBackpack = []byte{
	// 4355 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x5b, 0xdb, 0x6f, 0x5b, 0xc9,
	0x79, 0xf7, 0x21, 0x25, 0x8a, 0xfc, 0x44, 0x51, 0x47, 0xa3, 0x3b, 0x65, 0x49, 0xc7, 0xb3, 0xb6,
	0xd7, 0xeb, 0xad, 0xec, 0xee, 0x6e, 0x82, 0x6d, 0xb8, 0xaa, 0x10, 0x8a, 0xa2, 0x64, 0xc2, 0x32,
	0x29, 0x1c, 0x92, 0xde, 0x68, 0xb3, 0x29, 0xf7, 0x88, 0x1c, 0xd1, 0xa7, 0xe6, 0xe5, 0x98, 0x73,
	0xe8, 0xb5, 0x03, 0x04, 0x2d, 0x50, 0x14, 0xd8, 0xa6, 0x0f, 0x4d, 0x03, 0x04, 0x01, 0x82, 0x02,
	0x4d, 0x01, 0x03, 0xed, 0x43, 0x91, 0xe7, 0x02, 0x05, 0xfa, 0xd4, 0x87, 0x3e, 0x06, 0xe8, 0x53,
	0xdf, 0x8a, 0x2d, 0x50, 0xec, 0x5f, 0xd0, 0xd7, 0x16, 0x33, 0x73, 0x2e, 0x73, 0x2e, 0xa4, 0xd4,
	0x4d, 0xb2, 0x6f, 0x3c, 0xdf, 0xcc, 0x7c, 0x97, 0xdf, 0xcc, 0x77, 0x99, 0x0b, 0x61, 0x8f, 0x92,
	0xd1, 0x4b, 0xb3, 0x4d, 0x1e, 0x1a, 0x96, 0x45, 0x5f, 0x8e, 0x1e, 0xd2, 0x51, 0xfb, 0xe1, 0x85,
	0xd1, 0x7e, 0x6e, 0x19, 0xed, 0xe7, 0xec, 0xdb, 0xfd, 0xfd, 0xc0, 0x1a, 0x0d, 0xed, 0x21, 0x4a,
	0xbb, 0xdf, 0xf9, 0xdb, 0xed, 0x61, 0xbf, 0x3f, 0x1c, 0x3c, 0xb4, 0x7b, 0x2f, 0x2d, 0xb3, 0xfd,
	0xbc, 0x47, 0x1e, 0xd2, 0xe7, 0x17, 0x63, 0xb3, 0x67, 0x9b, 0x03, 0xfb, 0xb5, 0x45, 0x44, 0x7f,
	0xfc, 0x19, 0xc0, 0x99, 0xd1, 0x7e, 0x6e, 0x74, 0x49, 0xe9, 0xb2, 0x8b, 0x96, 0x61, 0xf6, 0xa2,
	0xdb, 0x32, 0x3b, 0x1b, 0x8a, 0xa6, 0xdc, 0x5b, 0xd0, 0x67, 0x2e, 0xba, 0x95, 0x0e, 0x42, 0x30,
	0x33, 0x30, 0xfa, 0x64, 0x23, 0xa1, 0x29, 0xf7, 0x32, 0x3a, 0xff, 0xcd, 0x68, 0x1d, 0x42, 0xdb,
	0x1b, 0x49, 0x41, 0x63, 0xbf, 0xd1, 0x2a, 0xa4, 0x4c, 0xda, 0xea, 0x90, 0xde, 0xc6, 0x8c, 0xa6,
	0xdc, 0x4b, 0xeb, 0xb3, 0x26, 0x3d, 0x22, 0x3d, 0xfc, 0x0f, 0x09, 0xc8, 0x39, 0x22, 0x2a, 0x36,
	0xe9, 0x33, 0x31, 0x37, 0x01, 0x98, 0x18, 0x9b, 0xf4, 0x7d, 0x59, 0xe9, 0x8b, 0x2e, 0x6b, 0xae,
	0x74, 0x7c, 0x25, 0x12, 0x92, 0x12, 0x5b, 0x90, 0xe1, 0xfd, 0x99, 0xea, 0x5c, 0xea, 0x82, 0x9e,
	0x66, 0x84, 0xc6, 0x6b, 0x8b, 0xb0, 0x46, 0x3a, 0x1c, 0x8f, 0xda, 0x84, 0x8d, 0x9a, 0x11, 0x8d,
	0x82, 0x50, 0xe9, 0xa0, 0x6d, 0x00, 0x3e, 0xb2, 0x3d, 0x1c, 0x0f, 0xec, 0x8d, 0x59, 0xde, 0xca,
	0x79, 0x95, 0x18, 0x01, 0x6d, 0x42, 0xfa, 0xd2, 0x1c, 0xb4, 0x6c, 0xb3, 0x4f, 0x36, 0x52, 0xbc,
	0x71, 0xee, 0xd2, 0x1c, 0x34, 0xcc, 0x3e, 0x91, 0x0c, 0x9a, 0x93, 0x0c, 0x42, 0x6b, 0x90, 0xfa,
	0x9c, 0x98, 0xdd, 0x67, 0xf6, 0x46, 0x9a, 0xf7, 0x77, 0xbe, 0xd0, 0x3d, 0x50, 0x3b, 0xaf, 0x07,
	0x46, 0xdf, 0x6c, 0xb7, 0x3c, 0x8e, 0x19, 0xde, 0x23, 0xe7, 0xd0, 0x8f, 0x1d, 0xc6, 0x6b, 0x90,
	0xea, 0x0f, 0x07, 0xf6, 0x33, 0xba, 0x01, 0x82, 0x83, 0xf8, 0xc2, 0xff, 0xab, 0xc0, 0xfc, 0xf1,
	0x78, 0xd0, 0x2e, 0x19, 0xa3, 0x0e, 0xc3, 0x69, 0x1d, 0xe6, 0xda, 0xc6, 0xa8, 0xe3, 0x83, 0x94,
	0x62, 0x9f, 0x02, 0x0d, 0xde, 0xc0, 0xd1, 0x10, 0x30, 0xa5, 0x19, 0xc1, 0x45, 0x83, 0x37, 0xf2,
	0x49, 0x13, 0x13, 0xc4, 0x1b, 0xab, 0x6c, 0xe2, 0xdc, 0x46, 0x3e, 0x7b, 0x33, 0x7e, 0xe3, 0x11,
	0x9b, 0xc1, 0x4d, 0xe0, 0xbf, 0x5b, 0xe3, 0x51, 0x8f, 0x03, 0x95, 0xd1, 0xb9, 0xfc, 0xe6, 0xa8,
	0xc7, 0x50, 0x14, 0x12, 0xcd, 0x3e, 0xa1, 0x0e, 0x50, 0x9c, 0x13, 0x33, 0x88, 0xb2, 0xe6, 0x97,
	0x46, 0xcf, 0x14, 0xed, 0x1c, 0xae, 0x05, 0x3d, 0xc3, 0x29, 0x21, 0x24, 0x05, 0x64, 0x0e, 0x92,
	0x2a, 0x24, 0xc7, 0x66, 0xc7, 0x01, 0x89, 0xfd, 0xc4, 0x7f, 0x9e, 0x00, 0x74, 0x3a, 0xb4, 0x6d,
	0x32, 0x7a, 0x7d, 0x3c, 0x32, 0xba, 0x7d, 0x32, 0xb0, 0x19, 0x10, 0xbb, 0x30, 0x7f, 0xe9, 0x7c,
	0xfa, 0x60, 0x80, 0x4b, 0xaa, 0x74, 0xd0, 0x5b, 0xb0, 0xe0, 0x75, 0x90, 0x40, 0xc9, 0xba, 0x44,
	0x0e, 0x8c, 0xdc, 0x49, 0x02, 0xc7, 0xeb, 0xc4, 0x01, 0x92, 0x3b, 0x49, 0x20, 0x79, 0x9d, 0x38,
	0x50, 0xb7, 0xc0, 0xfb, 0x96, 0xc0, 0xf2, 0x74, 0x64, 0x80, 0xf9, 0x26, 0xa7, 0x64, 0x93, 0xef,
	0x40, 0xce, 0x1b, 0x69, 0x8d, 0xcc, 0xb6, 0x0b, 0x96, 0x27, 0xf4, 0x8c, 0x11, 0xf1, 0x3f, 0x26,
	0x40, 0x6d, 0x52, 0x32, 0x3a, 0x74, 0xbc, 0x99, 0xb9, 0x46, 0xd0, 0x07, 0x94, 0x90, 0x0f, 0x68,
	0x90, 0x1d, 0x53, 0x32, 0xf2, 0xbc, 0x4a, 0x00, 0x00, 0x8c, 0xe6, 0xf8, 0x55, 0xd0, 0x11, 0x92,
	0xd3, 0x1c, 0x61, 0x26, 0xe8, 0x08, 0x01, 0xff, 0x9a, 0x0d, 0xf9, 0x97, 0xef, 0x0e, 0xa9, 0x80,
	0x3b, 0xec, 0xc2, 0xfc, 0xf0, 0xc2, 0x36, 0x5c, 0x96, 0xc2, 0x4c, 0x10, 0x24, 0xce, 0x75, 0x17,
	0xe6, 0x1d, 0xae, 0xdc, 0x20, 0xb1, 0x32, 0x40, 0x90, 0xb8, 0x49, 0xf7, 0x40, 0xbd, 0x34, 0x07,
	0x46, 0xaf, 0x25, 0xa9, 0xed, 0x38, 0x14, 0xa7, 0x57, 0x5c, 0xdd, 0xf1, 0x2f, 0x15, 0x58, 0x94,
	0xe1, 0x3a, 0x1d, 0x76, 0xa7, 0xa3, 0x15, 0xc4, 0x22, 0x11, 0xc6, 0x22, 0x60, 0x70, 0x32, 0x64,
	0xf0, 0x26, 0xa4, 0x7b, 0xc3, 0xae, 0xe0, 0xeb, 0x00, 0xd5, 0x1b, 0x76, 0x39, 0x5b, 0xb7, 0x89,
	0x19, 0x3c, 0xeb, 0x37, 0x99, 0x7d, 0x82, 0x0b, 0xa0, 0x16, 0x3b, 0x1d, 0x3f, 0xd6, 0xea, 0xe4,
	0x05, 0xba, 0x0b, 0xc9, 0xf6, 0x65, 0x97, 0x2b, 0x37, 0xff, 0xfe, 0xca, 0x03, 0x2f, 0x94, 0x4b,
	0xbd, 0x58, 0x07, 0xfc, 0x11, 0x2c, 0x85, 0xc6, 0x52, 0xeb, 0xda, 0x83, 0xdf, 0x06, 0xf5, 0x88,
	0xf4, 0x82, 0x82, 0xe3, 0xe2, 0x3c, 0x5e, 0x86, 0xa5, 0x50, 0x47, 0x6a, 0x61, 0x04, 0xea, 0x09,
	0xb1, 0x03, 0xa3, 0xf1, 0x11, 0x2c, 0x85, 0x68, 0xd4, 0x42, 0x0f, 0x21, 0xdd, 0xbe, 0xec, 0xb6,
	0x7a, 0x26, 0xb5, 0x37, 0x14, 0x2d, 0x39, 0x51, 0xa7, 0xb9, 0xf6, 0x65, 0xf7, 0xd4, 0xa4, 0x36,
	0x7e, 0x0c, 0x2b, 0xbe, 0x51, 0x4e, 0x66, 0x60, 0xba, 0x7d, 0x00, 0x69, 0x31, 0x35, 0x9e, 0x71,
	0x1b, 0x11, 0x46, 0x6e, 0xf7, 0x39, 0x53, 0xfc, 0xc0, 0xa7, 0xb0, 0x1a, 0xc3, 0x8c, 0x5a, 0x5f,
	0x8f, 0xdb, 0x63, 0x58, 0x79, 0x32, 0xfc, 0x6d, 0xa9, 0xb6, 0x0e, 0xab, 0x31, 0xcc, 0xa8, 0x85,
	0x2b, 0xb0, 0xe2, 0xe3, 0x2d, 0x49, 0x89, 0x4d, 0xc2, 0xc1, 0x94, 0x99, 0x08, 0xa6, 0x4c, 0x26,
	0x23, 0x86, 0x15, 0xb5, 0xf0, 0xbb, 0xb0, 0xe2, 0x4f, 0xd5, 0x15, 0x32, 0x70, 0x13, 0x56, 0x63,
	0x3a, 0x53, 0x0b, 0xed, 0xc3, 0x82, 0x6b, 0xb7, 0x3c, 0xc1, 0x93, 0x8d, 0x9f, 0x77, 0x8c, 0xe7,
	0x13, 0xfd, 0xd7, 0x09, 0x40, 0x27, 0xe6, 0x4b, 0xc2, 0x1c, 0xd4, 0xe9, 0xc7, 0x54, 0x70, 0x82,
	0xbf, 0xe2, 0x05, 0xff, 0xf8, 0xc4, 0xaf, 0x42, 0x72, 0x30, 0xee, 0x3b, 0x4e, 0xc8, 0x7e, 0xb2,
	0x80, 0x23, 0x7c, 0xd1, 0xf1, 0x3e, 0xe7, 0x8b, 0x39, 0xdf, 0x70, 0xd4, 0x61, 0x21, 0xb0, 0xe3,
	0x66, 0x2f, 0xfe, 0x5d, 0xe9, 0xa0, 0xb7, 0x61, 0x91, 0xbc, 0xb2, 0xcc, 0x11, 0x69, 0x75, 0xc6,
	0x23, 0xc3, 0x36, 0x87, 0x03, 0x27, 0x58, 0xe5, 0x04, 0xf9, 0xc8, 0xa1, 0x22, 0x0c, 0x0b, 0x8e,
	0xe3, 0x1b, 0x96, 0xc5, 0x18, 0xcd, 0x89, 0xc8, 0x2e, 0x88, 0x45, 0xcb, 0xaa, 0x74, 0x58, 0xdc,
	0xb2, 0x87, 0xb6, 0xd1, 0x73, 0xe2, 0xb7, 0x13, 0xb7, 0x38, 0x89, 0x07, 0x6f, 0x96, 0x1d, 0x86,
	0x63, 0x9b, 0x9a, 0x1d, 0x22, 0x17, 0x01, 0xf3, 0x0e, 0x8d, 0x47, 0x83, 0x55, 0x58, 0x8e, 0x40,
	0x42, 0x2d, 0xfc, 0x13, 0x05, 0x56, 0x9a, 0x94, 0xb8, 0x61, 0xac, 0xfc, 0xca, 0x1e, 0x19, 0x95,
	0xc1, 0xe5, 0x90, 0xc5, 0x2b, 0xdb, 0x18, 0x75, 0x89, 0xdd, 0xf2, 0x31, 0xcb, 0x08, 0x4a, 0xd3,
	0xe4, 0xa1, 0xbd, 0xfd, 0xcc, 0x18, 0x0c, 0x48, 0xcf, 0x87, 0x2f, 0xe3, 0x50, 0x44, 0xb9, 0x30,
	0xa6, 0x24, 0x10, 0xf8, 0xd3, 0x63, 0x4a, 0x44, 0xac, 0xdb, 0x06, 0xe8, 0x10, 0xa3, 0xd7, 0xb2,
	0x87, 0xcf, 0xc9, 0xc0, 0xc9, 0x76, 0x19, 0x46, 0x69, 0x30, 0x02, 0xfe, 0x75, 0x12, 0x90, 0xa4,
	0x12, 0x9b, 0xe1, 0xf8, 0xd9, 0x0b, 0xc4, 0xdb, 0xc4, 0x15, 0xd9, 0x29, 0x19, 0xc9, 0x4e, 0x53,
	0x6b, 0xb8, 0x29, 0x53, 0x1b, 0xb0, 0x2d, 0x15, 0xb2, 0x2d, 0x3c, 0x13, 0x73, 0x91, 0x99, 0x60,
	0x33, 0xee, 0xb2, 0x16, 0x6b, 0x3b, 0xad, 0x25, 0xd9, 0x8c, 0x3b, 0xfc, 0xd9, 0x0a, 0xf6, 0xb2,
	0x85, 0x98, 0xf0, 0x8c, 0x9f, 0x2d, 0xc4, 0x7c, 0x6f, 0x03, 0xf0, 0x16, 0x61, 0xba, 0x28, 0xe9,
	0x32, 0x9c, 0xc2, 0x6d, 0xbf, 0x0b, 0x8b, 0xfe, 0xdc, 0x09, 0x19, 0xf3, 0x5a, 0x92, 0xe5, 0x7c,
	0x6f, 0x02, 0xb9, 0x94, 0x3f, 0x04, 0x20, 0x6c, 0xc2, 0x5b, 0xe6, 0xe0, 0x72, 0xb8, 0x91, 0xe5,
	0xf1, 0x65, 0xc7, 0x77, 0xb1, 0xb8, 0x75, 0xa1, 0x67, 0x88, 0xb7, 0x44, 0xee, 0x81, 0xca, 0x80,
	0x18, 0x11, 0x83, 0x9a, 0xc3, 0x81, 0xd0, 0x65, 0x41, 0x2c, 0xf2, 0x31, 0x25, 0xba, 0x20, 0x33,
	0x85, 0x70, 0x17, 0x96, 0x23, 0x33, 0x4a, 0x2d, 0xe6, 0x57, 0x23, 0xd2, 0x37, 0xcc, 0x81, 0x5b,
	0x6c, 0x8a, 0xaf, 0xd0, 0x02, 0x49, 0x84, 0x16, 0x48, 0xa0, 0x6e, 0x48, 0x06, 0xea, 0x06, 0xfc,
	0x01, 0xac, 0x9d, 0x10, 0xbb, 0x49, 0x79, 0x5c, 0xa8, 0x71, 0x40, 0x99, 0xd2, 0xe4, 0x45, 0x60,
	0x42, 0x95, 0xc0, 0x84, 0xe2, 0x5f, 0x29, 0xb0, 0x1e, 0x3b, 0x8a, 0x5a, 0xe8, 0x50, 0xd8, 0x28,
	0x86, 0x76, 0x88, 0x6d, 0x98, 0xbd, 0x68, 0x20, 0x6e, 0x52, 0xc2, 0x47, 0x1d, 0xf1, 0x76, 0x6e,
	0xbd, 0xf4, 0x8d, 0x6a, 0xb0, 0xe2, 0xf3, 0x90, 0x00, 0x4f, 0x5c, 0x0b, 0xf0, 0x25, 0x97, 0x9b,
	0x47, 0xc2, 0xff, 0xaa, 0x40, 0xfe, 0x6c, 0x34, 0x6c, 0xcb, 0x80, 0x32, 0xf3, 0x87, 0x63, 0x9b,
	0x99, 0x1a, 0x5e, 0xfa, 0x4a, 0x64, 0xe9, 0x3b, 0xbe, 0x94, 0x98, 0xe0, 0x4b, 0xdf, 0xcc, 0x6e,
	0x07, 0x6f, 0xc3, 0xd6, 0x44, 0x2b, 0xa8, 0x85, 0xef, 0x02, 0x12, 0xb3, 0xe2, 0x15, 0x59, 0xb1,
	0x61, 0x00, 0xff, 0x08, 0x96, 0x23, 0xfd, 0xa8, 0x85, 0xbe, 0x0b, 0x39, 0x1f, 0x05, 0x29, 0x87,
	0xe4, 0x03, 0x78, 0x07, 0xea, 0x5d, 0x3d, 0xeb, 0x62, 0xc4, 0xdd, 0xe3, 0x36, 0xe4, 0x7a, 0x06,
	0xb5, 0x5b, 0x6e, 0x51, 0x49, 0x39, 0x60, 0x49, 0x3d, 0xcb, 0xa8, 0x35, 0x51, 0x56, 0x52, 0xfc,
	0x0e, 0xcf, 0x61, 0x8c, 0x95, 0xbb, 0x91, 0x6a, 0xd2, 0xf8, 0x74, 0x83, 0x9b, 0xee, 0xea, 0x0c,
	0x76, 0xa5, 0x16, 0xfa, 0x68, 0x82, 0xb2, 0xab, 0xbe, 0xb2, 0xd2, 0x36, 0x2d, 0xa8, 0x27, 0x2e,
	0xf3, 0x62, 0x4d, 0x6e, 0x27, 0x2f, 0xd0, 0xef, 0x3b, 0x3b, 0x2b, 0xbf, 0x72, 0x98, 0xc0, 0x8b,
	0x6f, 0xb8, 0x58, 0xd9, 0xb0, 0x02, 0x28, 0xcc, 0x86, 0x5a, 0xf8, 0x43, 0xc8, 0x1f, 0x1a, 0x76,
	0xfb, 0x59, 0xbc, 0x8d, 0x9b, 0x90, 0xf6, 0x42, 0x8c, 0xc2, 0x43, 0xcc, 0xdc, 0x58, 0x04, 0x17,
	0xfc, 0x09, 0x6c, 0x4d, 0x1c, 0xf8, 0x9b, 0x5a, 0xfc, 0x7b, 0xbc, 0x70, 0x0c, 0x59, 0x3c, 0x69,
	0xef, 0xca, 0x0c, 0x0b, 0xf7, 0xa6, 0x16, 0xfe, 0x36, 0xaf, 0x29, 0x43, 0x3c, 0x34, 0xc8, 0x3a,
	0x3c, 0x64, 0x9b, 0x40, 0x30, 0xe2, 0xa2, 0x6b, 0x7c, 0x55, 0x86, 0x98, 0xa1, 0xef, 0xc0, 0x82,
	0x8b, 0xf6, 0x35, 0x8c, 0x99, 0x77, 0x20, 0xe7, 0x0c, 0x1f, 0xc1, 0x7a, 0xe9, 0x19, 0x69, 0x3f,
	0xbf, 0xce, 0x0a, 0x9a, 0xba, 0x0d, 0xc7, 0xef, 0xc1, 0x46, 0x3c, 0x27, 0x6a, 0x39, 0x9b, 0xc3,
	0x31, 0x15, 0x1b, 0x13, 0x7e, 0xb2, 0xd0, 0xa4, 0x04, 0xb7, 0x60, 0xb5, 0x7e, 0xbd, 0xc5, 0x3b,
	0xfd, 0x04, 0x40, 0xc2, 0x3e, 0x19, 0xc0, 0x7e, 0x03, 0xd6, 0xea, 0xb1, 0x0b, 0x00, 0xff, 0x93,
	0xe2, 0x39, 0x8e, 0xb4, 0x89, 0x8a, 0x97, 0xbd, 0x0d, 0x70, 0x41, 0xba, 0x6e, 0x18, 0x71, 0xaa,
	0x0d, 0x4e, 0xe1, 0x19, 0x75, 0x13, 0xd2, 0x64, 0xd0, 0x09, 0x24, 0x04, 0x32, 0xe8, 0xb8, 0x1b,
	0x49, 0x3f, 0xae, 0xcd, 0x4c, 0x8b, 0x6b, 0xb3, 0x53, 0x36, 0x5d, 0xa9, 0xc0, 0xa6, 0x0b, 0x57,
	0x3d, 0x3f, 0x0e, 0x68, 0x4e, 0x2d, 0xf4, 0x2d, 0x31, 0x48, 0x5a, 0x02, 0x9b, 0xf1, 0xe1, 0x86,
	0x0d, 0x60, 0xfc, 0xf8, 0x12, 0x38, 0x81, 0x85, 0x62, 0xa7, 0x23, 0x15, 0xcb, 0x53, 0x77, 0x92,
	0x9b, 0xd2, 0x9e, 0x80, 0x41, 0x91, 0xf5, 0x2b, 0xff, 0x47, 0x90, 0x93, 0x19, 0x51, 0xeb, 0x6b,
	0x73, 0x3a, 0x81, 0x85, 0x23, 0xd2, 0xfb, 0x2d, 0xa8, 0xa4, 0x42, 0x4e, 0x66, 0x44, 0x2d, 0x6c,
	0xc3, 0xc2, 0x09, 0xb1, 0xaf, 0xcb, 0x7a, 0x0f, 0x96, 0x79, 0xa3, 0x37, 0x51, 0x02, 0xdc, 0x04,
	0x77, 0x4c, 0x95, 0x35, 0xd5, 0x9d, 0x19, 0xe3, 0x31, 0x7b, 0x1d, 0xe6, 0x58, 0xdd, 0x63, 0xf4,
	0x7a, 0x7c, 0x25, 0xa4, 0xf5, 0x54, 0x97, 0xd8, 0xc5, 0x5e, 0x0f, 0x7f, 0x0b, 0x72, 0xb2, 0x54,
	0x6a, 0xb1, 0x3a, 0x2c, 0xba, 0xc7, 0xc8, 0x06, 0x77, 0x12, 0x7f, 0xaf, 0xc0, 0xbc, 0x53, 0x17,
	0xf0, 0x92, 0xe7, 0x37, 0x3c, 0x10, 0x99, 0xba, 0xcb, 0x0f, 0xd4, 0x95, 0x33, 0xa1, 0xba, 0x32,
	0xb4, 0x05, 0x98, 0x0d, 0x6f, 0x01, 0xf0, 0x5f, 0x2a, 0xb0, 0xd8, 0x18, 0x19, 0x03, 0x6a, 0xb4,
	0xd9, 0xbe, 0x82, 0x6b, 0xcb, 0x0f, 0xb1, 0x08, 0xf9, 0x21, 0x91, 0xf5, 0x05, 0x41, 0x72, 0x1d,
	0x60, 0x68, 0x91, 0x91, 0xec, 0x56, 0x69, 0x46, 0x70, 0xbd, 0xca, 0xab, 0x98, 0x92, 0xc1, 0x12,
	0x78, 0x17, 0xe6, 0x9d, 0xdd, 0x8d, 0x74, 0x78, 0x03, 0x82, 0xc4, 0x53, 0xfb, 0x1b, 0x05, 0x16,
	0x8e, 0x47, 0x84, 0x7c, 0x42, 0xdc, 0xf2, 0xfd, 0x08, 0x54, 0x9b, 0x6b, 0xc7, 0xb7, 0x3d, 0xa2,
	0x00, 0x12, 0x79, 0x49, 0xf2, 0x90, 0x90, 0x01, 0xfa, 0xa2, 0x34, 0x84, 0x5b, 0xf4, 0x11, 0xe4,
	0x2a, 0x1c, 0xdd, 0xc1, 0xe5, 0xd0, 0x5f, 0x08, 0x81, 0x40, 0x2b, 0x4d, 0x97, 0x9e, 0x75, 0x7f,
	0xf1, 0xb5, 0xe1, 0xc4, 0x95, 0xa4, 0x9f, 0x90, 0x55, 0xc8, 0xc9, 0x5a, 0x52, 0x0b, 0x13, 0xc8,
	0x49, 0x5b, 0xa4, 0xfa, 0xf8, 0x8a, 0x33, 0xb0, 0x75, 0x98, 0x0b, 0xce, 0x76, 0xca, 0xbc, 0xce,
	0xd1, 0x17, 0xfe, 0x23, 0xbe, 0x4b, 0x0e, 0x4a, 0xfa, 0x1a, 0x9b, 0x1c, 0x49, 0x7c, 0x52, 0x16,
	0x8f, 0x4f, 0xbd, 0xd8, 0x2a, 0xf3, 0x97, 0x4e, 0x27, 0xe8, 0xb8, 0x1f, 0x5b, 0xc7, 0xca, 0xfd,
	0x39, 0xef, 0xfa, 0xb8, 0x8f, 0xbf, 0x50, 0x60, 0xeb, 0x84, 0xd8, 0xbc, 0x0a, 0xe5, 0xfa, 0x1f,
	0xbe, 0x66, 0xb3, 0xac, 0x1b, 0x03, 0xb1, 0xb1, 0x0e, 0x86, 0x67, 0x65, 0x5a, 0x78, 0x4e, 0x04,
	0xc3, 0xb3, 0x1c, 0x64, 0x93, 0xc1, 0x93, 0xad, 0x15, 0x98, 0xb5, 0x8c, 0x91, 0x41, 0x9d, 0x0d,
	0xa2, 0xf8, 0xc0, 0x5d, 0xb8, 0x39, 0x59, 0x13, 0x6a, 0xb1, 0x51, 0x02, 0x72, 0xa1, 0x85, 0xf8,
	0x08, 0xba, 0x56, 0x22, 0xe4, 0x5a, 0x2b, 0x30, 0xfb, 0xd2, 0xe8, 0x8d, 0x5d, 0x05, 0xc4, 0x07,
	0x7e, 0x01, 0x79, 0x57, 0x10, 0x5b, 0x3c, 0xdf, 0x80, 0xc5, 0x78, 0xdf, 0x47, 0x39, 0x22, 0x92,
	0x5a, 0x4c, 0xa6, 0xf0, 0x47, 0x2f, 0x58, 0x65, 0xf4, 0xcc, 0xd0, 0xed, 0x8d, 0xff, 0x2c, 0x01,
	0x59, 0xee, 0x3f, 0x97, 0xc3, 0x51, 0xbf, 0x66, 0xd9, 0x31, 0x6b, 0xc9, 0x3f, 0xc7, 0x48, 0x84,
	0xcf, 0x31, 0x26, 0xcd, 0x42, 0x78, 0x3f, 0x3b, 0x13, 0xdd, 0xcf, 0x4e, 0xd9, 0x2a, 0x7b, 0xe7,
	0x2b, 0x29, 0xe9, 0x7c, 0x65, 0x15, 0x52, 0x17, 0xdd, 0xd6, 0x60, 0xdc, 0x77, 0x36, 0xc7, 0xb3,
	0x17, 0xdd, 0xea, 0xb8, 0x8f, 0xee, 0x40, 0xae, 0x6f, 0xd8, 0x64, 0x64, 0x86, 0xce, 0x39, 0x16,
	0x5c, 0xaa, 0xb7, 0xf5, 0xed, 0xb2, 0x6a, 0x3c, 0xb0, 0x33, 0x66, 0x14, 0x11, 0x06, 0xff, 0x25,
	0x01, 0x4b, 0xa5, 0xe1, 0xe0, 0x25, 0x19, 0xb1, 0xcd, 0xe7, 0xe4, 0xb3, 0x83, 0xb0, 0x5d, 0x89,
	0xa8, 0x5d, 0x9e, 0xf2, 0xc9, 0x58, 0xe5, 0x67, 0x64, 0xe5, 0x7d, 0x64, 0x67, 0x03, 0xc8, 0x46,
	0x8d, 0x4a, 0xc5, 0x19, 0xf5, 0x0e, 0xa8, 0x6d, 0x4f, 0xe9, 0xc0, 0x29, 0xfd, 0xa2, 0x4f, 0x17,
	0x5d, 0x65, 0xb4, 0xd3, 0x41, 0xb4, 0x4b, 0x80, 0x3c, 0x61, 0x7e, 0x59, 0x9d, 0x99, 0x16, 0x20,
	0x55, 0x77, 0x80, 0x57, 0x5a, 0xaf, 0x00, 0x0a, 0xe3, 0x47, 0x2d, 0x6c, 0xc1, 0xb2, 0x3e, 0xec,
	0xf5, 0x58, 0xf5, 0xd2, 0x74, 0xd2, 0x59, 0x3c, 0xae, 0xbb, 0x30, 0xdf, 0x1e, 0x11, 0xc3, 0x0e,
	0xc0, 0x0a, 0x82, 0xc4, 0x51, 0xbd, 0x0b, 0x8b, 0xc3, 0x91, 0xc9, 0x5c, 0x27, 0x94, 0x5c, 0x16,
	0x04, 0xb9, 0xe6, 0x6c, 0xca, 0xd7, 0x60, 0x25, 0x2a, 0x91, 0x5a, 0xf8, 0x53, 0x80, 0x23, 0xd2,
	0x19, 0xb7, 0xed, 0xab, 0x2f, 0x28, 0x02, 0xd9, 0x36, 0x11, 0xca, 0xb6, 0x5e, 0xa0, 0x48, 0x4a,
	0x81, 0x02, 0xff, 0x4a, 0x81, 0xac, 0x60, 0xef, 0xec, 0xdd, 0xa3, 0x16, 0xbe, 0xe7, 0x88, 0x94,
	0xb2, 0x8f, 0x74, 0xee, 0xec, 0xeb, 0x26, 0x14, 0xe1, 0x89, 0x27, 0x56, 0x56, 0xf8, 0x36, 0x62,
	0x26, 0x72, 0x1b, 0xc1, 0x0a, 0x14, 0xca, 0x4a, 0x99, 0x96, 0xb4, 0xb6, 0xd2, 0xfa, 0xbc, 0x49,
	0x8b, 0xbd, 0x9e, 0xa8, 0x7a, 0xf0, 0xdf, 0x29, 0xb0, 0xc6, 0xb7, 0x59, 0x42, 0xb0, 0x3c, 0x39,
	0x1f, 0xc2, 0x7c, 0x87, 0x13, 0xe5, 0x72, 0x74, 0x2d, 0xac, 0xaa, 0x73, 0x66, 0x01, 0xa2, 0x2b,
	0x57, 0x17, 0xc1, 0x0c, 0x2b, 0x02, 0xdc, 0xeb, 0x57, 0xf6, 0xfb, 0x8a, 0x62, 0xc0, 0x91, 0x23,
	0xdb, 0x21, 0x48, 0x3c, 0xae, 0xfd, 0xb3, 0x07, 0xaa, 0x4e, 0xe8, 0xb8, 0x17, 0x17, 0x99, 0xbe,
	0x0b, 0x4b, 0x74, 0xdc, 0x6e, 0x13, 0x4a, 0x5b, 0xd7, 0x03, 0x77, 0xd1, 0xe9, 0xee, 0x6d, 0xd6,
	0x0b, 0x90, 0xbb, 0x34, 0x4c, 0x79, 0xe1, 0x27, 0xa7, 0x0c, 0xcf, 0xb2, 0xbe, 0xde, 0xd8, 0x2d,
	0xc8, 0xf0, 0xb1, 0xf2, 0x26, 0x81, 0x11, 0xb8, 0xf6, 0x3a, 0xac, 0xc7, 0x02, 0x4c, 0xad, 0x6b,
	0x22, 0x2c, 0x8c, 0x96, 0x11, 0xc6, 0xff, 0xa3, 0xf0, 0x32, 0x43, 0x3e, 0x24, 0x8a, 0x62, 0x22,
	0x43, 0x9e, 0x88, 0x1c, 0x41, 0x4e, 0xad, 0x23, 0xa7, 0x6e, 0x79, 0xfc, 0x4c, 0x38, 0x1b, 0x3d,
	0x98, 0x95, 0x8e, 0x15, 0x53, 0xe1, 0x63, 0xc5, 0x90, 0x6f, 0xcf, 0x45, 0x7c, 0x3b, 0x5c, 0x00,
	0xa7, 0xc3, 0x05, 0x30, 0xfe, 0x0c, 0xb2, 0x57, 0xe4, 0xd1, 0xe4, 0xb4, 0x3c, 0x9a, 0xf4, 0xf3,
	0xe8, 0x1a, 0xa4, 0x58, 0x45, 0xd0, 0xa7, 0xce, 0x72, 0x74, 0xbe, 0xf0, 0x87, 0x90, 0xe1, 0xc6,
	0x4c, 0xa9, 0x06, 0xbc, 0x84, 0x9f, 0x90, 0x13, 0xfe, 0xbb, 0x90, 0x75, 0x62, 0x0f, 0x75, 0x77,
	0x4e, 0x2e, 0xfc, 0xd4, 0xc9, 0xb6, 0x69, 0x07, 0x7f, 0x7a, 0xff, 0x17, 0x09, 0x98, 0x3b, 0xf5,
	0x0a, 0x15, 0xf5, 0xb4, 0x76, 0xd2, 0x6a, 0x9c, 0x9f, 0x95, 0x5b, 0x95, 0xea, 0xd3, 0xe2, 0x69,
	0xe5, 0x48, 0xbd, 0x81, 0x54, 0xc8, 0x7a, 0xd4, 0x66, 0xbd, 0xac, 0x2a, 0x68, 0x19, 0x16, 0x3d,
	0x4a, 0xf9, 0x7b, 0x67, 0x15, 0xbd, 0xac, 0x26, 0xd0, 0x0e, 0xe4, 0x3d, 0xe2, 0xb1, 0x5e, 0x3c,
	0x79, 0x52, 0xae, 0x36, 0x5a, 0xe5, 0xef, 0x95, 0x1e, 0x15, 0xab, 0x27, 0x65, 0x35, 0x19, 0xdf,
	0xae, 0xd7, 0x4e, 0x4f, 0x0f, 0x8b, 0xa5, 0xc7, 0xea, 0x0c, 0xba, 0x09, 0x1b, 0xbe, 0xf0, 0x46,
	0xf9, 0x49, 0xab, 0x54, 0xab, 0x3e, 0x2d, 0xeb, 0xf5, 0x4a, 0xad, 0xaa, 0xce, 0xa2, 0x55, 0x58,
	0xf2, 0x5a, 0xbd, 0x41, 0x29, 0x94, 0x87, 0x35, 0x8f, 0x5c, 0x3b, 0xd3, 0x8b, 0x8d, 0x72, 0xeb,
	0xa8, 0x7c, 0xd4, 0x2c, 0x35, 0xd4, 0xb9, 0x00, 0xc3, 0xc3, 0x66, 0xbd, 0x52, 0x2d, 0xd7, 0xeb,
	0x6e, 0x6b, 0x1a, 0xbd, 0x05, 0xbb, 0xbe, 0x0d, 0xd5, 0xb2, 0x7e, 0x72, 0x1e, 0x91, 0x9a, 0xb9,
	0xff, 0x33, 0x05, 0x16, 0xa5, 0xeb, 0x19, 0x17, 0xa4, 0x66, 0xf5, 0x71, 0xb5, 0xf6, 0xb1, 0xe8,
	0xcf, 0x18, 0xa8, 0x37, 0x18, 0x95, 0xa9, 0x74, 0x56, 0x2c, 0x3d, 0x6e, 0x9d, 0xe9, 0xe5, 0x7a,
	0xb9, 0xda, 0x50, 0x15, 0xa6, 0x9e, 0x47, 0x2d, 0x15, 0xf5, 0xa3, 0x96, 0x5e, 0x29, 0x3d, 0x62,
	0x90, 0xa9, 0x09, 0xb4, 0x05, 0xeb, 0xc1, 0xb6, 0xd2, 0xa3, 0xa2, 0xfe, 0x84, 0x37, 0x26, 0xd1,
	0x36, 0x6c, 0x7a, 0x8d, 0xa7, 0xb5, 0x46, 0xa3, 0xac, 0x9f, 0x7b, 0xa0, 0xa9, 0x33, 0xf7, 0xff,
	0x66, 0x06, 0x96, 0xdc, 0xf2, 0xd6, 0x8f, 0xb2, 0x9b, 0xb0, 0xea, 0x68, 0xc6, 0xc6, 0x15, 0x4f,
	0xca, 0xad, 0x7a, 0xad, 0xa9, 0x97, 0x98, 0x7a, 0x6f, 0xc1, 0x6e, 0x90, 0xd6, 0x2a, 0x96, 0x1a,
	0x95, 0xa7, 0x95, 0xc6, 0xb9, 0xa4, 0xad, 0x06, 0x37, 0x43, 0x9d, 0x8e, 0x8a, 0x95, 0xd3, 0xf3,
	0x56, 0xe9, 0x51, 0xb9, 0xf4, 0xb8, 0x52, 0x55, 0x13, 0xe8, 0x16, 0x6c, 0x87, 0x7a, 0x1c, 0x57,
	0xf4, 0x7a, 0xa3, 0xa5, 0x97, 0x99, 0xf2, 0x7c, 0x9a, 0xb7, 0x60, 0x3d, 0xd4, 0xa5, 0x76, 0x7c,
	0x5c, 0x29, 0x55, 0x8a, 0xa7, 0xea, 0x4c, 0x4c, 0x63, 0xfd, 0x49, 0xb1, 0xfe, 0xa8, 0x7c, 0x72,
	0xa2, 0xce, 0x32, 0x9b, 0x43, 0x8d, 0xd2, 0x5c, 0xa4, 0xd0, 0x2e, 0x6c, 0x85, 0x4d, 0xf8, 0x98,
	0xc3, 0x56, 0xae, 0x36, 0xca, 0xba, 0x3a, 0x87, 0xde, 0x85, 0xb7, 0x63, 0x3b, 0x9c, 0x37, 0xcf,
	0x2b, 0xd5, 0xd6, 0x69, 0xe5, 0x69, 0xb9, 0xf5, 0xa4, 0x52, 0xe7, 0xdc, 0xd2, 0xe8, 0x21, 0xbc,
	0x1b, 0xea, 0x5c, 0x61, 0x6c, 0x18, 0x2a, 0xb5, 0x2a, 0xfb, 0x5d, 0x79, 0x52, 0x2c, 0x9d, 0x7b,
	0x03, 0x32, 0x08, 0xc3, 0x4e, 0x78, 0x40, 0x68, 0xb9, 0x40, 0x8c, 0x79, 0xd5, 0xda, 0x61, 0xe5,
	0xb4, 0xd2, 0x38, 0x57, 0xe7, 0xd9, 0x72, 0x0c, 0x35, 0x36, 0x0e, 0xcb, 0xc5, 0x6a, 0xeb, 0xb0,
	0x79, 0xae, 0x66, 0x63, 0xac, 0x7b, 0x5a, 0x39, 0xf3, 0x26, 0x49, 0x5d, 0x44, 0xf7, 0xe1, 0x6e,
	0xa8, 0x83, 0xb3, 0x6a, 0xeb, 0x8d, 0x5a, 0x35, 0x00, 0x95, 0x7a, 0xbf, 0x07, 0x70, 0xac, 0x97,
	0xcb, 0x9f, 0x94, 0xd9, 0xe2, 0x44, 0x6b, 0x80, 0xfc, 0xaf, 0x56, 0xd3, 0xf3, 0xeb, 0x20, 0xfd,
	0x4c, 0x2f, 0x9f, 0x15, 0x75, 0xe6, 0xdd, 0xab, 0xb0, 0x24, 0xd1, 0x4b, 0xb5, 0x27, 0x4f, 0x2a,
	0x0d, 0x35, 0x81, 0xd6, 0x61, 0x59, 0x22, 0x7b, 0x3e, 0x98, 0xbc, 0x7f, 0xea, 0xd6, 0x31, 0x7c,
	0x11, 0xae, 0x01, 0x72, 0x16, 0xa1, 0x70, 0x35, 0xd7, 0x41, 0x96, 0x60, 0x21, 0xe8, 0xa0, 0x3c,
	0x8c, 0x84, 0xfd, 0x32, 0x71, 0xff, 0xe7, 0x0a, 0xe4, 0x04, 0xbb, 0x63, 0x27, 0x6f, 0x31, 0x2f,
	0x12, 0xcd, 0xc7, 0xc5, 0xca, 0x69, 0xc8, 0x08, 0x0d, 0x6e, 0x86, 0xdb, 0x2a, 0x47, 0xad, 0x6a,
	0x8d, 0x85, 0x9e, 0x4a, 0x9d, 0x49, 0xc1, 0xb0, 0x13, 0xea, 0xc1, 0x27, 0x8e, 0x77, 0xa9, 0xd6,
	0x9a, 0x27, 0x8f, 0xd4, 0x04, 0x73, 0x8f, 0x50, 0x1f, 0x47, 0x79, 0xde, 0x95, 0x11, 0xd5, 0xe4,
	0xfb, 0x3f, 0xbe, 0x05, 0x69, 0xf7, 0xd0, 0x0b, 0x59, 0xfc, 0xa0, 0x4b, 0x7a, 0xfe, 0x25, 0x1d,
	0xc6, 0x87, 0xdf, 0x2a, 0xe4, 0xb7, 0x26, 0xb6, 0x51, 0x0b, 0xdf, 0xfd, 0xd3, 0x37, 0x5f, 0x25,
	0x95, 0x1f, 0xbf, 0xf9, 0x2a, 0x39, 0x33, 0x28, 0x74, 0x0a, 0x3f, 0x7d, 0xf3, 0x55, 0x72, 0x79,
	0x6f, 0xa0, 0xed, 0x0f, 0x8c, 0x3e, 0x39, 0xd0, 0xf6, 0x3a, 0xda, 0x7e, 0x87, 0xd0, 0xf6, 0x01,
	0x3a, 0xe7, 0x87, 0x4d, 0xf1, 0x12, 0xc3, 0xcf, 0x0c, 0x64, 0x89, 0x91, 0xe7, 0x06, 0x78, 0x91,
	0x49, 0x4c, 0x30, 0x89, 0x37, 0x98, 0xb4, 0x1b, 0x88, 0xf0, 0x23, 0xb2, 0x78, 0xd6, 0xe1, 0xf7,
	0x0f, 0x32, 0xeb, 0xe8, 0x93, 0x87, 0x2d, 0xc6, 0x3a, 0xc9, 0x58, 0x27, 0x5e, 0x71, 0x53, 0x60,
	0xef, 0x95, 0xb6, 0xcf, 0x37, 0x1f, 0x07, 0xe8, 0xdf, 0x15, 0xf9, 0x2d, 0x86, 0xfb, 0xa0, 0x6d,
	0x27, 0x0e, 0x1c, 0xff, 0x4c, 0x2d, 0xbf, 0x3b, 0xb5, 0x9d, 0x5a, 0xf8, 0x47, 0x4c, 0xe6, 0x0c,
	0x93, 0x99, 0x7b, 0x55, 0xb0, 0x0b, 0xb4, 0x30, 0x28, 0xf4, 0x0b, 0x9f, 0x17, 0x5e, 0x73, 0xf9,
	0x9f, 0xf9, 0xf2, 0xb5, 0x3d, 0x5b, 0xdb, 0x67, 0xf5, 0xc0, 0x81, 0xb6, 0x47, 0xb5, 0x7d, 0xaf,
	0xec, 0x38, 0xd0, 0x18, 0xdc, 0x3c, 0xa1, 0x1e, 0x68, 0x7b, 0x7d, 0x6d, 0xdf, 0xbd, 0xe5, 0x39,
	0xd0, 0xf6, 0x3e, 0xd7, 0xf6, 0xc5, 0x93, 0x9c, 0x03, 0xed, 0xfb, 0x7b, 0xaf, 0xb5, 0xfd, 0xf0,
	0x23, 0xb5, 0x83, 0x1f, 0xa0, 0x17, 0xf2, 0x8b, 0x8e, 0x18, 0xa3, 0xe2, 0xde, 0x10, 0xc8, 0x46,
	0xc5, 0x3e, 0x1b, 0x10, 0x40, 0xce, 0x4e, 0x00, 0xf2, 0xbf, 0x15, 0x58, 0x8a, 0xbc, 0x8b, 0x90,
	0x65, 0xc6, 0xbd, 0xc0, 0x90, 0x65, 0xc6, 0x3f, 0xaa, 0xf8, 0x2b, 0x85, 0x09, 0x4d, 0x31, 0xa1,
	0xaa, 0x51, 0x88, 0x62, 0x69, 0xed, 0x19, 0x42, 0x05, 0x51, 0x14, 0x1d, 0x68, 0xbf, 0x73, 0x6c,
	0xff, 0x44, 0x7e, 0x56, 0x13, 0x63, 0x67, 0xdc, 0x1b, 0x10, 0xd9, 0xce, 0xf8, 0x87, 0x1d, 0xef,
	0x30, 0x33, 0xe7, 0xb8, 0xc7, 0xbd, 0x2a, 0x50, 0x6e, 0xda, 0x9a, 0x6c, 0x0a, 0xdd, 0x77, 0x2d,
	0x44, 0x3f, 0x55, 0xf8, 0x39, 0xb4, 0xfc, 0xb0, 0x30, 0xe8, 0xcc, 0xc1, 0x5b, 0x97, 0xfc, 0xcd,
	0xc9, 0x8d, 0xd4, 0xc2, 0x65, 0x26, 0x38, 0xcd, 0x04, 0xa7, 0x5f, 0x15, 0x06, 0x1c, 0x5f, 0x26,
	0xfc, 0x01, 0x13, 0xee, 0x80, 0xe7, 0x3b, 0x3e, 0x03, 0xd4, 0xec, 0x13, 0x2a, 0x10, 0xf5, 0x5f,
	0x08, 0x1e, 0xa0, 0x3f, 0xe6, 0x07, 0xd1, 0x13, 0x74, 0x8a, 0xdc, 0x26, 0xc9, 0x3a, 0xc5, 0x5c,
	0x1e, 0x6d, 0x33, 0x9d, 0x32, 0xd2, 0x42, 0xcb, 0x32, 0x6d, 0x9c, 0x2b, 0x90, 0x03, 0xf4, 0x29,
	0x3f, 0x6c, 0x9e, 0x20, 0x2b, 0x72, 0xeb, 0x24, 0xcb, 0x8a, 0xde, 0x2d, 0x89, 0xc0, 0x03, 0x52,
	0xe0, 0xf9, 0xa5, 0x02, 0x8b, 0xa1, 0xb7, 0x1c, 0x48, 0x66, 0x11, 0x79, 0xf9, 0x92, 0xdf, 0x9e,
	0xd2, 0x4a, 0x2d, 0x7c, 0xc6, 0x24, 0xcc, 0x33, 0x09, 0xd9, 0x71, 0xe1, 0xa2, 0x30, 0x28, 0xd0,
	0x82, 0x5d, 0x10, 0x76, 0x7d, 0x7b, 0x6f, 0xac, 0xed, 0x8f, 0xf9, 0x04, 0x5f, 0xf8, 0x73, 0xcd,
	0xf0, 0x1e, 0xf7, 0xe5, 0x55, 0xcb, 0x96, 0xf5, 0xbe, 0x5b, 0x2f, 0x1f, 0xa0, 0xbf, 0x15, 0xcf,
	0xe3, 0x02, 0x8f, 0x09, 0x6f, 0xc6, 0x5e, 0x74, 0x3b, 0xbb, 0x55, 0x59, 0xc5, 0x98, 0xa7, 0x02,
	0xb8, 0xc6, 0x54, 0xcc, 0xf2, 0x45, 0x30, 0x2e, 0xd8, 0x05, 0xd3, 0x59, 0x04, 0x7f, 0xe0, 0xab,
	0x67, 0x6b, 0xfb, 0xde, 0xb6, 0xe7, 0x40, 0xdb, 0x33, 0xb5, 0x7d, 0x79, 0x2f, 0x12, 0xf6, 0x30,
	0x64, 0xc2, 0x62, 0xe8, 0x0e, 0x07, 0x05, 0xa7, 0x21, 0x74, 0xf1, 0x1c, 0xc0, 0x30, 0x7a, 0xdd,
	0x8c, 0x37, 0x99, 0x82, 0x0b, 0x7c, 0x45, 0x8c, 0xb9, 0x6a, 0x69, 0x57, 0x35, 0xf4, 0x17, 0x0a,
	0xac, 0xc4, 0x5d, 0xcc, 0xa1, 0x5b, 0x3e, 0xcb, 0x09, 0x57, 0x80, 0x79, 0x7c, 0x55, 0x17, 0x6a,
	0xe1, 0xfb, 0x4c, 0x74, 0x8e, 0x7b, 0x26, 0xc3, 0x86, 0x09, 0x5f, 0x0f, 0xe0, 0xe2, 0xdd, 0xdb,
	0x71, 0xd7, 0x44, 0xd1, 0x0b, 0x39, 0x24, 0x79, 0x7f, 0xec, 0x7d, 0x60, 0x5e, 0x9b, 0xde, 0x81,
	0x5a, 0xf8, 0x3b, 0x4c, 0x8b, 0x45, 0xa6, 0x45, 0x8a, 0x69, 0x21, 0x72, 0xf2, 0xed, 0x09, 0x7a,
	0xf0, 0x0c, 0xed, 0xb9, 0xcb, 0xc8, 0xbb, 0xe9, 0x9f, 0xa0, 0xd3, 0xc9, 0x55, 0x3a, 0xc5, 0x5f,
	0x32, 0x8b, 0x49, 0x51, 0x63, 0x27, 0xe5, 0x3f, 0x14, 0x58, 0x9f, 0xf0, 0xfa, 0x00, 0xdd, 0x96,
	0x9e, 0x99, 0x4d, 0x7c, 0x66, 0x91, 0xbf, 0x73, 0x8d, 0x5e, 0xd4, 0xc2, 0x2f, 0x98, 0x0e, 0x4b,
	0xdc, 0xb9, 0xec, 0xc2, 0x98, 0xaf, 0x5c, 0x96, 0x1e, 0x98, 0x36, 0x4f, 0x19, 0x28, 0xa1, 0x55,
	0xea, 0xe1, 0x65, 0x06, 0xd7, 0x73, 0x34, 0x3f, 0xf8, 0xf7, 0x0b, 0xa1, 0x24, 0x81, 0xbe, 0x54,
	0x22, 0x4f, 0x27, 0x4e, 0x87, 0xdd, 0x18, 0x40, 0x83, 0x17, 0xaf, 0x31, 0x80, 0x86, 0xee, 0x37,
	0xf1, 0x17, 0x3c, 0xd9, 0x21, 0x27, 0x54, 0x08, 0x3f, 0xbc, 0x28, 0x10, 0x6e, 0x4d, 0xdf, 0xd3,
	0xfd, 0xfb, 0xcc, 0x2e, 0xf7, 0x44, 0x59, 0x7b, 0xef, 0xc1, 0x98, 0x12, 0xed, 0xfd, 0x07, 0xe2,
	0x7a, 0x28, 0x64, 0x99, 0xf6, 0xde, 0x03, 0x6b, 0x44, 0x28, 0xe1, 0x46, 0x84, 0x6c, 0x64, 0x01,
	0xc7, 0x3b, 0x1c, 0x38, 0xd0, 0xf6, 0x88, 0xb6, 0xef, 0x9e, 0x06, 0x1c, 0xfc, 0x00, 0x9d, 0x01,
	0xf8, 0x77, 0x9d, 0x68, 0x3d, 0x90, 0x42, 0xa4, 0xbc, 0xb6, 0x11, 0xdf, 0xe0, 0xc6, 0xd5, 0x65,
	0x29, 0xae, 0x9e, 0xb1, 0x92, 0xbc, 0x17, 0xc3, 0x31, 0x70, 0x13, 0x2a, 0x73, 0x0c, 0xdd, 0x6c,
	0x72, 0x8e, 0x2b, 0x12, 0xc7, 0xcf, 0x00, 0xfc, 0x4b, 0x47, 0x99, 0x63, 0xe0, 0x02, 0x54, 0xe6,
	0x18, 0xbc, 0xa3, 0xc4, 0xbb, 0x8c, 0xe3, 0x2a, 0x5f, 0xc0, 0xc2, 0xb1, 0x73, 0x7b, 0x76, 0x41,
	0x5a, 0x17, 0x4c, 0x67, 0xff, 0x06, 0x4b, 0x96, 0x10, 0xb8, 0x7d, 0x93, 0x25, 0x84, 0x2e, 0xbc,
	0xb8, 0xce, 0x6b, 0x81, 0xb2, 0x76, 0x29, 0x72, 0x75, 0x14, 0xaa, 0xcc, 0x22, 0xf7, 0x56, 0xf9,
	0xdd, 0xa9, 0xed, 0xae, 0x98, 0x75, 0x49, 0xcc, 0x2f, 0x14, 0xd8, 0x98, 0x74, 0x93, 0x83, 0xee,
	0x04, 0xd8, 0x4d, 0xba, 0x77, 0xca, 0xdf, 0xbd, 0x4e, 0x37, 0x6a, 0xe1, 0x87, 0x4c, 0xf8, 0x06,
	0x0f, 0x90, 0xee, 0x62, 0xbd, 0x19, 0x5d, 0x5d, 0xfe, 0xe2, 0x42, 0x3f, 0x17, 0x2f, 0xc2, 0xe2,
	0xae, 0x62, 0xe4, 0xe0, 0x30, 0xf9, 0x82, 0x28, 0x7f, 0xe7, 0x1a, 0xbd, 0x5c, 0xcd, 0x36, 0xff,
	0x1f, 0x9a, 0x7d, 0x0a, 0xb9, 0xe0, 0xf1, 0xbc, 0x5c, 0x59, 0x44, 0x2e, 0x3e, 0xe4, 0xca, 0x22,
	0xe6, 0x54, 0x9f, 0x4f, 0x4a, 0x5e, 0x9a, 0x94, 0x67, 0xa0, 0x86, 0x0f, 0xdd, 0x91, 0x94, 0xf7,
	0x62, 0xae, 0x00, 0xf2, 0x3b, 0xd3, 0x9a, 0xa9, 0x85, 0x57, 0x99, 0x8c, 0x2d, 0x2f, 0x2d, 0x88,
	0xaa, 0xe2, 0x06, 0x7a, 0x01, 0xcb, 0x31, 0xa7, 0xaa, 0x48, 0x8a, 0x40, 0xf1, 0xa7, 0xda, 0xf9,
	0x5b, 0x57, 0xf4, 0x70, 0x45, 0xde, 0x8c, 0x88, 0xec, 0xbb, 0xef, 0xc4, 0x02, 0xaf, 0xfc, 0x50,
	0x24, 0xe8, 0x85, 0x9f, 0x0e, 0xca, 0x22, 0x27, 0x3c, 0x13, 0xc4, 0x4b, 0x4c, 0xe4, 0xb6, 0x54,
	0x0f, 0xde, 0x40, 0x1f, 0xf3, 0x2b, 0x58, 0x6f, 0xba, 0xc5, 0x59, 0xaf, 0x61, 0x1b, 0x48, 0x3a,
	0x20, 0x0e, 0x2c, 0x99, 0x65, 0x79, 0xae, 0x9c, 0x13, 0x4c, 0xc1, 0x78, 0x27, 0xc0, 0xf8, 0x13,
	0xee, 0x38, 0xde, 0x50, 0xef, 0x1c, 0xf9, 0xeb, 0xf1, 0xde, 0x0d, 0xf1, 0x8e, 0x2a, 0xcd, 0x8f,
	0xc8, 0x27, 0x31, 0x96, 0xe8, 0xf2, 0xe9, 0xa9, 0xe0, 0xad, 0x05, 0x78, 0xbf, 0x76, 0x0e, 0xd2,
	0x63, 0x52, 0xfd, 0xed, 0xd0, 0xa4, 0xc6, 0xe7, 0xfb, 0x3b, 0xd7, 0xe8, 0xe5, 0x8a, 0xbe, 0x25,
	0x8b, 0xce, 0xa7, 0xbe, 0x78, 0xf3, 0x55, 0xf2, 0x67, 0x3f, 0x3c, 0x54, 0xff, 0xed, 0xcb, 0x1d,
	0xe5, 0xd7, 0x5f, 0xee, 0x28, 0xff, 0xf9, 0xe5, 0x8e, 0xf2, 0x93, 0xff, 0xda, 0xb9, 0x71, 0x91,
	0xe2, 0x7f, 0x4a, 0xfb, 0xe0, 0xff, 0x02, 0x00, 0x00, 0xff, 0xff, 0xc0, 0xfa, 0xb7, 0x22, 0xf5,
	0x36, 0x00, 0x00,
}
