// Code generated by protoc-gen-gogo.
// source: src/channeldrawgame/channeldrawgame.proto
// DO NOT EDIT!

/*
	Package channeldrawgame is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/channeldrawgame/channeldrawgame.proto

	It has these top-level messages:
		Point
		Line
		Picture
		CancelLineReq
		CancelLineResp
		RemovePictureReq
		RemovePictureResp
		GetPictureReq
		GetPictureResp
		CreatLineReq
		CreatLineResp
		AddPointListReq
		AddPointListResp
		CancelByUidReq
		CancelByUidResp
		SetDrawStatusReq
		SetDrawStatusResp
		SetBoardStatusReq
		SetBoardStatusResp
		GetBoardStatusReq
		GetBoardStatusResp
		GetDrawStatusListReq
		GetDrawStatusListResp
		LinePara
		GetLineParaListReq
		GetLineParaListResp
*/
package channeldrawgame

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type LineType int32

const (
	LineType_PURE_COLOR LineType = 0
	LineType_MULT_COLOR LineType = 1
	LineType_ICON_IMAGE LineType = 2
	LineType_RUBBER     LineType = 3
)

var LineType_name = map[int32]string{
	0: "PURE_COLOR",
	1: "MULT_COLOR",
	2: "ICON_IMAGE",
	3: "RUBBER",
}
var LineType_value = map[string]int32{
	"PURE_COLOR": 0,
	"MULT_COLOR": 1,
	"ICON_IMAGE": 2,
	"RUBBER":     3,
}

func (x LineType) Enum() *LineType {
	p := new(LineType)
	*p = x
	return p
}
func (x LineType) String() string {
	return proto.EnumName(LineType_name, int32(x))
}
func (x *LineType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(LineType_value, data, "LineType")
	if err != nil {
		return err
	}
	*x = LineType(value)
	return nil
}
func (LineType) EnumDescriptor() ([]byte, []int) { return fileDescriptorChanneldrawgame, []int{0} }

type Point struct {
	X uint32 `protobuf:"varint,1,req,name=x" json:"x"`
	Y uint32 `protobuf:"varint,2,req,name=y" json:"y"`
}

func (m *Point) Reset()                    { *m = Point{} }
func (m *Point) String() string            { return proto.CompactTextString(m) }
func (*Point) ProtoMessage()               {}
func (*Point) Descriptor() ([]byte, []int) { return fileDescriptorChanneldrawgame, []int{0} }

func (m *Point) GetX() uint32 {
	if m != nil {
		return m.X
	}
	return 0
}

func (m *Point) GetY() uint32 {
	if m != nil {
		return m.Y
	}
	return 0
}

// 线结构，由很多点组成。
type Line struct {
	LineID    uint32   `protobuf:"varint,1,req,name=lineID" json:"lineID"`
	Uid       uint32   `protobuf:"varint,2,req,name=uid" json:"uid"`
	Size_     uint32   `protobuf:"varint,3,req,name=size" json:"size"`
	Color     string   `protobuf:"bytes,4,req,name=color" json:"color"`
	PointList []*Point `protobuf:"bytes,5,rep,name=PointList" json:"PointList,omitempty"`
	ParaId    string   `protobuf:"bytes,6,opt,name=para_id,json=paraId" json:"para_id"`
	LineType  uint32   `protobuf:"varint,7,opt,name=line_type,json=lineType" json:"line_type"`
}

func (m *Line) Reset()                    { *m = Line{} }
func (m *Line) String() string            { return proto.CompactTextString(m) }
func (*Line) ProtoMessage()               {}
func (*Line) Descriptor() ([]byte, []int) { return fileDescriptorChanneldrawgame, []int{1} }

func (m *Line) GetLineID() uint32 {
	if m != nil {
		return m.LineID
	}
	return 0
}

func (m *Line) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *Line) GetSize_() uint32 {
	if m != nil {
		return m.Size_
	}
	return 0
}

func (m *Line) GetColor() string {
	if m != nil {
		return m.Color
	}
	return ""
}

func (m *Line) GetPointList() []*Point {
	if m != nil {
		return m.PointList
	}
	return nil
}

func (m *Line) GetParaId() string {
	if m != nil {
		return m.ParaId
	}
	return ""
}

func (m *Line) GetLineType() uint32 {
	if m != nil {
		return m.LineType
	}
	return 0
}

// 画结构，由很多线组成
type Picture struct {
	ChannelID uint32  `protobuf:"varint,1,req,name=channelID" json:"channelID"`
	LineList  []*Line `protobuf:"bytes,2,rep,name=lineList" json:"lineList,omitempty"`
}

func (m *Picture) Reset()                    { *m = Picture{} }
func (m *Picture) String() string            { return proto.CompactTextString(m) }
func (*Picture) ProtoMessage()               {}
func (*Picture) Descriptor() ([]byte, []int) { return fileDescriptorChanneldrawgame, []int{2} }

func (m *Picture) GetChannelID() uint32 {
	if m != nil {
		return m.ChannelID
	}
	return 0
}

func (m *Picture) GetLineList() []*Line {
	if m != nil {
		return m.LineList
	}
	return nil
}

// 撤回一条线
type CancelLineReq struct {
	ChannelID uint32 `protobuf:"varint,1,req,name=channelID" json:"channelID"`
	LineID    uint32 `protobuf:"varint,2,req,name=lineID" json:"lineID"`
}

func (m *CancelLineReq) Reset()                    { *m = CancelLineReq{} }
func (m *CancelLineReq) String() string            { return proto.CompactTextString(m) }
func (*CancelLineReq) ProtoMessage()               {}
func (*CancelLineReq) Descriptor() ([]byte, []int) { return fileDescriptorChanneldrawgame, []int{3} }

func (m *CancelLineReq) GetChannelID() uint32 {
	if m != nil {
		return m.ChannelID
	}
	return 0
}

func (m *CancelLineReq) GetLineID() uint32 {
	if m != nil {
		return m.LineID
	}
	return 0
}

type CancelLineResp struct {
}

func (m *CancelLineResp) Reset()                    { *m = CancelLineResp{} }
func (m *CancelLineResp) String() string            { return proto.CompactTextString(m) }
func (*CancelLineResp) ProtoMessage()               {}
func (*CancelLineResp) Descriptor() ([]byte, []int) { return fileDescriptorChanneldrawgame, []int{4} }

// 房管用于删除整幅画
type RemovePictureReq struct {
	ChannelID uint32 `protobuf:"varint,1,req,name=channelID" json:"channelID"`
}

func (m *RemovePictureReq) Reset()                    { *m = RemovePictureReq{} }
func (m *RemovePictureReq) String() string            { return proto.CompactTextString(m) }
func (*RemovePictureReq) ProtoMessage()               {}
func (*RemovePictureReq) Descriptor() ([]byte, []int) { return fileDescriptorChanneldrawgame, []int{5} }

func (m *RemovePictureReq) GetChannelID() uint32 {
	if m != nil {
		return m.ChannelID
	}
	return 0
}

type RemovePictureResp struct {
	OperID uint32 `protobuf:"varint,1,opt,name=operID" json:"operID"`
}

func (m *RemovePictureResp) Reset()                    { *m = RemovePictureResp{} }
func (m *RemovePictureResp) String() string            { return proto.CompactTextString(m) }
func (*RemovePictureResp) ProtoMessage()               {}
func (*RemovePictureResp) Descriptor() ([]byte, []int) { return fileDescriptorChanneldrawgame, []int{6} }

func (m *RemovePictureResp) GetOperID() uint32 {
	if m != nil {
		return m.OperID
	}
	return 0
}

// 拿整幅画
type GetPictureReq struct {
	ChannelID uint32 `protobuf:"varint,1,req,name=channelID" json:"channelID"`
}

func (m *GetPictureReq) Reset()                    { *m = GetPictureReq{} }
func (m *GetPictureReq) String() string            { return proto.CompactTextString(m) }
func (*GetPictureReq) ProtoMessage()               {}
func (*GetPictureReq) Descriptor() ([]byte, []int) { return fileDescriptorChanneldrawgame, []int{7} }

func (m *GetPictureReq) GetChannelID() uint32 {
	if m != nil {
		return m.ChannelID
	}
	return 0
}

type GetPictureResp struct {
	OperID  uint32   `protobuf:"varint,1,opt,name=operID" json:"operID"`
	Picture *Picture `protobuf:"bytes,2,opt,name=picture" json:"picture,omitempty"`
}

func (m *GetPictureResp) Reset()                    { *m = GetPictureResp{} }
func (m *GetPictureResp) String() string            { return proto.CompactTextString(m) }
func (*GetPictureResp) ProtoMessage()               {}
func (*GetPictureResp) Descriptor() ([]byte, []int) { return fileDescriptorChanneldrawgame, []int{8} }

func (m *GetPictureResp) GetOperID() uint32 {
	if m != nil {
		return m.OperID
	}
	return 0
}

func (m *GetPictureResp) GetPicture() *Picture {
	if m != nil {
		return m.Picture
	}
	return nil
}

type CreatLineReq struct {
	ChannelID uint32 `protobuf:"varint,1,req,name=channelID" json:"channelID"`
	Size_     uint32 `protobuf:"varint,2,req,name=size" json:"size"`
	Color     string `protobuf:"bytes,3,req,name=color" json:"color"`
	ParaId    string `protobuf:"bytes,4,opt,name=para_id,json=paraId" json:"para_id"`
	LineType  uint32 `protobuf:"varint,5,opt,name=line_type,json=lineType" json:"line_type"`
}

func (m *CreatLineReq) Reset()                    { *m = CreatLineReq{} }
func (m *CreatLineReq) String() string            { return proto.CompactTextString(m) }
func (*CreatLineReq) ProtoMessage()               {}
func (*CreatLineReq) Descriptor() ([]byte, []int) { return fileDescriptorChanneldrawgame, []int{9} }

func (m *CreatLineReq) GetChannelID() uint32 {
	if m != nil {
		return m.ChannelID
	}
	return 0
}

func (m *CreatLineReq) GetSize_() uint32 {
	if m != nil {
		return m.Size_
	}
	return 0
}

func (m *CreatLineReq) GetColor() string {
	if m != nil {
		return m.Color
	}
	return ""
}

func (m *CreatLineReq) GetParaId() string {
	if m != nil {
		return m.ParaId
	}
	return ""
}

func (m *CreatLineReq) GetLineType() uint32 {
	if m != nil {
		return m.LineType
	}
	return 0
}

type CreatLineResp struct {
	ChannelID uint32 `protobuf:"varint,1,req,name=channelID" json:"channelID"`
	LineID    uint32 `protobuf:"varint,2,req,name=lineID" json:"lineID"`
	OperID    uint32 `protobuf:"varint,3,req,name=operID" json:"operID"`
}

func (m *CreatLineResp) Reset()                    { *m = CreatLineResp{} }
func (m *CreatLineResp) String() string            { return proto.CompactTextString(m) }
func (*CreatLineResp) ProtoMessage()               {}
func (*CreatLineResp) Descriptor() ([]byte, []int) { return fileDescriptorChanneldrawgame, []int{10} }

func (m *CreatLineResp) GetChannelID() uint32 {
	if m != nil {
		return m.ChannelID
	}
	return 0
}

func (m *CreatLineResp) GetLineID() uint32 {
	if m != nil {
		return m.LineID
	}
	return 0
}

func (m *CreatLineResp) GetOperID() uint32 {
	if m != nil {
		return m.OperID
	}
	return 0
}

type AddPointListReq struct {
	ChannelID uint32   `protobuf:"varint,1,req,name=channelID" json:"channelID"`
	LineID    uint32   `protobuf:"varint,2,req,name=lineID" json:"lineID"`
	PointList []*Point `protobuf:"bytes,3,rep,name=pointList" json:"pointList,omitempty"`
}

func (m *AddPointListReq) Reset()                    { *m = AddPointListReq{} }
func (m *AddPointListReq) String() string            { return proto.CompactTextString(m) }
func (*AddPointListReq) ProtoMessage()               {}
func (*AddPointListReq) Descriptor() ([]byte, []int) { return fileDescriptorChanneldrawgame, []int{11} }

func (m *AddPointListReq) GetChannelID() uint32 {
	if m != nil {
		return m.ChannelID
	}
	return 0
}

func (m *AddPointListReq) GetLineID() uint32 {
	if m != nil {
		return m.LineID
	}
	return 0
}

func (m *AddPointListReq) GetPointList() []*Point {
	if m != nil {
		return m.PointList
	}
	return nil
}

type AddPointListResp struct {
}

func (m *AddPointListResp) Reset()                    { *m = AddPointListResp{} }
func (m *AddPointListResp) String() string            { return proto.CompactTextString(m) }
func (*AddPointListResp) ProtoMessage()               {}
func (*AddPointListResp) Descriptor() ([]byte, []int) { return fileDescriptorChanneldrawgame, []int{12} }

type CancelByUidReq struct {
	ChannelID uint32 `protobuf:"varint,1,req,name=channelID" json:"channelID"`
}

func (m *CancelByUidReq) Reset()                    { *m = CancelByUidReq{} }
func (m *CancelByUidReq) String() string            { return proto.CompactTextString(m) }
func (*CancelByUidReq) ProtoMessage()               {}
func (*CancelByUidReq) Descriptor() ([]byte, []int) { return fileDescriptorChanneldrawgame, []int{13} }

func (m *CancelByUidReq) GetChannelID() uint32 {
	if m != nil {
		return m.ChannelID
	}
	return 0
}

type CancelByUidResp struct {
}

func (m *CancelByUidResp) Reset()                    { *m = CancelByUidResp{} }
func (m *CancelByUidResp) String() string            { return proto.CompactTextString(m) }
func (*CancelByUidResp) ProtoMessage()               {}
func (*CancelByUidResp) Descriptor() ([]byte, []int) { return fileDescriptorChanneldrawgame, []int{14} }

type SetDrawStatusReq struct {
	ChannelID uint32 `protobuf:"varint,1,req,name=channelID" json:"channelID"`
	Status    uint32 `protobuf:"varint,2,req,name=status" json:"status"`
}

func (m *SetDrawStatusReq) Reset()                    { *m = SetDrawStatusReq{} }
func (m *SetDrawStatusReq) String() string            { return proto.CompactTextString(m) }
func (*SetDrawStatusReq) ProtoMessage()               {}
func (*SetDrawStatusReq) Descriptor() ([]byte, []int) { return fileDescriptorChanneldrawgame, []int{15} }

func (m *SetDrawStatusReq) GetChannelID() uint32 {
	if m != nil {
		return m.ChannelID
	}
	return 0
}

func (m *SetDrawStatusReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type SetDrawStatusResp struct {
	Status uint32 `protobuf:"varint,1,req,name=status" json:"status"`
}

func (m *SetDrawStatusResp) Reset()         { *m = SetDrawStatusResp{} }
func (m *SetDrawStatusResp) String() string { return proto.CompactTextString(m) }
func (*SetDrawStatusResp) ProtoMessage()    {}
func (*SetDrawStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldrawgame, []int{16}
}

func (m *SetDrawStatusResp) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type SetBoardStatusReq struct {
	ChannelID uint32 `protobuf:"varint,1,req,name=channelID" json:"channelID"`
	Status    uint32 `protobuf:"varint,2,req,name=status" json:"status"`
}

func (m *SetBoardStatusReq) Reset()         { *m = SetBoardStatusReq{} }
func (m *SetBoardStatusReq) String() string { return proto.CompactTextString(m) }
func (*SetBoardStatusReq) ProtoMessage()    {}
func (*SetBoardStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldrawgame, []int{17}
}

func (m *SetBoardStatusReq) GetChannelID() uint32 {
	if m != nil {
		return m.ChannelID
	}
	return 0
}

func (m *SetBoardStatusReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type SetBoardStatusResp struct {
	Status uint32 `protobuf:"varint,1,req,name=status" json:"status"`
}

func (m *SetBoardStatusResp) Reset()         { *m = SetBoardStatusResp{} }
func (m *SetBoardStatusResp) String() string { return proto.CompactTextString(m) }
func (*SetBoardStatusResp) ProtoMessage()    {}
func (*SetBoardStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldrawgame, []int{18}
}

func (m *SetBoardStatusResp) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type GetBoardStatusReq struct {
	ChannelID uint32 `protobuf:"varint,1,req,name=channelID" json:"channelID"`
}

func (m *GetBoardStatusReq) Reset()         { *m = GetBoardStatusReq{} }
func (m *GetBoardStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetBoardStatusReq) ProtoMessage()    {}
func (*GetBoardStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldrawgame, []int{19}
}

func (m *GetBoardStatusReq) GetChannelID() uint32 {
	if m != nil {
		return m.ChannelID
	}
	return 0
}

type GetBoardStatusResp struct {
	ChannelID uint32 `protobuf:"varint,1,req,name=channelID" json:"channelID"`
	Status    uint32 `protobuf:"varint,2,req,name=status" json:"status"`
	Interval  uint32 `protobuf:"varint,3,opt,name=interval" json:"interval"`
}

func (m *GetBoardStatusResp) Reset()         { *m = GetBoardStatusResp{} }
func (m *GetBoardStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetBoardStatusResp) ProtoMessage()    {}
func (*GetBoardStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldrawgame, []int{20}
}

func (m *GetBoardStatusResp) GetChannelID() uint32 {
	if m != nil {
		return m.ChannelID
	}
	return 0
}

func (m *GetBoardStatusResp) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetBoardStatusResp) GetInterval() uint32 {
	if m != nil {
		return m.Interval
	}
	return 0
}

// 拿正在绘画UID列表
type GetDrawStatusListReq struct {
	ChannelID uint32 `protobuf:"varint,1,req,name=channelID" json:"channelID"`
}

func (m *GetDrawStatusListReq) Reset()         { *m = GetDrawStatusListReq{} }
func (m *GetDrawStatusListReq) String() string { return proto.CompactTextString(m) }
func (*GetDrawStatusListReq) ProtoMessage()    {}
func (*GetDrawStatusListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldrawgame, []int{21}
}

func (m *GetDrawStatusListReq) GetChannelID() uint32 {
	if m != nil {
		return m.ChannelID
	}
	return 0
}

type GetDrawStatusListResp struct {
	UidList []uint32 `protobuf:"varint,1,rep,name=uidList" json:"uidList,omitempty"`
}

func (m *GetDrawStatusListResp) Reset()         { *m = GetDrawStatusListResp{} }
func (m *GetDrawStatusListResp) String() string { return proto.CompactTextString(m) }
func (*GetDrawStatusListResp) ProtoMessage()    {}
func (*GetDrawStatusListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldrawgame, []int{22}
}

func (m *GetDrawStatusListResp) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type LinePara struct {
	ParaId     string   `protobuf:"bytes,1,req,name=para_id,json=paraId" json:"para_id"`
	Name       string   `protobuf:"bytes,2,req,name=name" json:"name"`
	PreviewUrl string   `protobuf:"bytes,3,req,name=preview_url,json=previewUrl" json:"preview_url"`
	IconUrl    string   `protobuf:"bytes,4,req,name=icon_url,json=iconUrl" json:"icon_url"`
	ColorList  []string `protobuf:"bytes,5,rep,name=color_list,json=colorList" json:"color_list,omitempty"`
	Type       uint32   `protobuf:"varint,6,req,name=type" json:"type"`
}

func (m *LinePara) Reset()                    { *m = LinePara{} }
func (m *LinePara) String() string            { return proto.CompactTextString(m) }
func (*LinePara) ProtoMessage()               {}
func (*LinePara) Descriptor() ([]byte, []int) { return fileDescriptorChanneldrawgame, []int{23} }

func (m *LinePara) GetParaId() string {
	if m != nil {
		return m.ParaId
	}
	return ""
}

func (m *LinePara) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *LinePara) GetPreviewUrl() string {
	if m != nil {
		return m.PreviewUrl
	}
	return ""
}

func (m *LinePara) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *LinePara) GetColorList() []string {
	if m != nil {
		return m.ColorList
	}
	return nil
}

func (m *LinePara) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type GetLineParaListReq struct {
}

func (m *GetLineParaListReq) Reset()         { *m = GetLineParaListReq{} }
func (m *GetLineParaListReq) String() string { return proto.CompactTextString(m) }
func (*GetLineParaListReq) ProtoMessage()    {}
func (*GetLineParaListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldrawgame, []int{24}
}

type GetLineParaListResp struct {
	LineList []*LinePara `protobuf:"bytes,1,rep,name=line_list,json=lineList" json:"line_list,omitempty"`
}

func (m *GetLineParaListResp) Reset()         { *m = GetLineParaListResp{} }
func (m *GetLineParaListResp) String() string { return proto.CompactTextString(m) }
func (*GetLineParaListResp) ProtoMessage()    {}
func (*GetLineParaListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldrawgame, []int{25}
}

func (m *GetLineParaListResp) GetLineList() []*LinePara {
	if m != nil {
		return m.LineList
	}
	return nil
}

func init() {
	proto.RegisterType((*Point)(nil), "channeldrawgame.Point")
	proto.RegisterType((*Line)(nil), "channeldrawgame.Line")
	proto.RegisterType((*Picture)(nil), "channeldrawgame.Picture")
	proto.RegisterType((*CancelLineReq)(nil), "channeldrawgame.CancelLineReq")
	proto.RegisterType((*CancelLineResp)(nil), "channeldrawgame.CancelLineResp")
	proto.RegisterType((*RemovePictureReq)(nil), "channeldrawgame.RemovePictureReq")
	proto.RegisterType((*RemovePictureResp)(nil), "channeldrawgame.RemovePictureResp")
	proto.RegisterType((*GetPictureReq)(nil), "channeldrawgame.GetPictureReq")
	proto.RegisterType((*GetPictureResp)(nil), "channeldrawgame.GetPictureResp")
	proto.RegisterType((*CreatLineReq)(nil), "channeldrawgame.CreatLineReq")
	proto.RegisterType((*CreatLineResp)(nil), "channeldrawgame.CreatLineResp")
	proto.RegisterType((*AddPointListReq)(nil), "channeldrawgame.AddPointListReq")
	proto.RegisterType((*AddPointListResp)(nil), "channeldrawgame.AddPointListResp")
	proto.RegisterType((*CancelByUidReq)(nil), "channeldrawgame.CancelByUidReq")
	proto.RegisterType((*CancelByUidResp)(nil), "channeldrawgame.CancelByUidResp")
	proto.RegisterType((*SetDrawStatusReq)(nil), "channeldrawgame.SetDrawStatusReq")
	proto.RegisterType((*SetDrawStatusResp)(nil), "channeldrawgame.SetDrawStatusResp")
	proto.RegisterType((*SetBoardStatusReq)(nil), "channeldrawgame.SetBoardStatusReq")
	proto.RegisterType((*SetBoardStatusResp)(nil), "channeldrawgame.SetBoardStatusResp")
	proto.RegisterType((*GetBoardStatusReq)(nil), "channeldrawgame.GetBoardStatusReq")
	proto.RegisterType((*GetBoardStatusResp)(nil), "channeldrawgame.GetBoardStatusResp")
	proto.RegisterType((*GetDrawStatusListReq)(nil), "channeldrawgame.GetDrawStatusListReq")
	proto.RegisterType((*GetDrawStatusListResp)(nil), "channeldrawgame.GetDrawStatusListResp")
	proto.RegisterType((*LinePara)(nil), "channeldrawgame.LinePara")
	proto.RegisterType((*GetLineParaListReq)(nil), "channeldrawgame.GetLineParaListReq")
	proto.RegisterType((*GetLineParaListResp)(nil), "channeldrawgame.GetLineParaListResp")
	proto.RegisterEnum("channeldrawgame.LineType", LineType_name, LineType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for Channeldrawgame service

type ChanneldrawgameClient interface {
	CreateLine(ctx context.Context, in *CreatLineReq, opts ...grpc.CallOption) (*CreatLineResp, error)
	CancelLine(ctx context.Context, in *CancelLineReq, opts ...grpc.CallOption) (*CancelLineResp, error)
	AddPointList(ctx context.Context, in *AddPointListReq, opts ...grpc.CallOption) (*AddPointListResp, error)
	CancelByUid(ctx context.Context, in *CancelByUidReq, opts ...grpc.CallOption) (*CancelByUidResp, error)
	RemovePicture(ctx context.Context, in *RemovePictureReq, opts ...grpc.CallOption) (*RemovePictureResp, error)
	GetPicture(ctx context.Context, in *GetPictureReq, opts ...grpc.CallOption) (*GetPictureResp, error)
	SetBoardStatus(ctx context.Context, in *SetBoardStatusReq, opts ...grpc.CallOption) (*SetBoardStatusResp, error)
	SetDrawStatus(ctx context.Context, in *SetDrawStatusReq, opts ...grpc.CallOption) (*SetDrawStatusResp, error)
	GetBoardStatus(ctx context.Context, in *GetBoardStatusReq, opts ...grpc.CallOption) (*GetBoardStatusResp, error)
	GetDrawStatusList(ctx context.Context, in *GetDrawStatusListReq, opts ...grpc.CallOption) (*GetDrawStatusListResp, error)
	GetLineParaList(ctx context.Context, in *GetLineParaListReq, opts ...grpc.CallOption) (*GetLineParaListResp, error)
}

type channeldrawgameClient struct {
	cc *grpc.ClientConn
}

func NewChanneldrawgameClient(cc *grpc.ClientConn) ChanneldrawgameClient {
	return &channeldrawgameClient{cc}
}

func (c *channeldrawgameClient) CreateLine(ctx context.Context, in *CreatLineReq, opts ...grpc.CallOption) (*CreatLineResp, error) {
	out := new(CreatLineResp)
	err := grpc.Invoke(ctx, "/channeldrawgame.channeldrawgame/CreateLine", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channeldrawgameClient) CancelLine(ctx context.Context, in *CancelLineReq, opts ...grpc.CallOption) (*CancelLineResp, error) {
	out := new(CancelLineResp)
	err := grpc.Invoke(ctx, "/channeldrawgame.channeldrawgame/CancelLine", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channeldrawgameClient) AddPointList(ctx context.Context, in *AddPointListReq, opts ...grpc.CallOption) (*AddPointListResp, error) {
	out := new(AddPointListResp)
	err := grpc.Invoke(ctx, "/channeldrawgame.channeldrawgame/AddPointList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channeldrawgameClient) CancelByUid(ctx context.Context, in *CancelByUidReq, opts ...grpc.CallOption) (*CancelByUidResp, error) {
	out := new(CancelByUidResp)
	err := grpc.Invoke(ctx, "/channeldrawgame.channeldrawgame/CancelByUid", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channeldrawgameClient) RemovePicture(ctx context.Context, in *RemovePictureReq, opts ...grpc.CallOption) (*RemovePictureResp, error) {
	out := new(RemovePictureResp)
	err := grpc.Invoke(ctx, "/channeldrawgame.channeldrawgame/RemovePicture", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channeldrawgameClient) GetPicture(ctx context.Context, in *GetPictureReq, opts ...grpc.CallOption) (*GetPictureResp, error) {
	out := new(GetPictureResp)
	err := grpc.Invoke(ctx, "/channeldrawgame.channeldrawgame/GetPicture", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channeldrawgameClient) SetBoardStatus(ctx context.Context, in *SetBoardStatusReq, opts ...grpc.CallOption) (*SetBoardStatusResp, error) {
	out := new(SetBoardStatusResp)
	err := grpc.Invoke(ctx, "/channeldrawgame.channeldrawgame/SetBoardStatus", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channeldrawgameClient) SetDrawStatus(ctx context.Context, in *SetDrawStatusReq, opts ...grpc.CallOption) (*SetDrawStatusResp, error) {
	out := new(SetDrawStatusResp)
	err := grpc.Invoke(ctx, "/channeldrawgame.channeldrawgame/SetDrawStatus", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channeldrawgameClient) GetBoardStatus(ctx context.Context, in *GetBoardStatusReq, opts ...grpc.CallOption) (*GetBoardStatusResp, error) {
	out := new(GetBoardStatusResp)
	err := grpc.Invoke(ctx, "/channeldrawgame.channeldrawgame/GetBoardStatus", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channeldrawgameClient) GetDrawStatusList(ctx context.Context, in *GetDrawStatusListReq, opts ...grpc.CallOption) (*GetDrawStatusListResp, error) {
	out := new(GetDrawStatusListResp)
	err := grpc.Invoke(ctx, "/channeldrawgame.channeldrawgame/GetDrawStatusList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channeldrawgameClient) GetLineParaList(ctx context.Context, in *GetLineParaListReq, opts ...grpc.CallOption) (*GetLineParaListResp, error) {
	out := new(GetLineParaListResp)
	err := grpc.Invoke(ctx, "/channeldrawgame.channeldrawgame/GetLineParaList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for Channeldrawgame service

type ChanneldrawgameServer interface {
	CreateLine(context.Context, *CreatLineReq) (*CreatLineResp, error)
	CancelLine(context.Context, *CancelLineReq) (*CancelLineResp, error)
	AddPointList(context.Context, *AddPointListReq) (*AddPointListResp, error)
	CancelByUid(context.Context, *CancelByUidReq) (*CancelByUidResp, error)
	RemovePicture(context.Context, *RemovePictureReq) (*RemovePictureResp, error)
	GetPicture(context.Context, *GetPictureReq) (*GetPictureResp, error)
	SetBoardStatus(context.Context, *SetBoardStatusReq) (*SetBoardStatusResp, error)
	SetDrawStatus(context.Context, *SetDrawStatusReq) (*SetDrawStatusResp, error)
	GetBoardStatus(context.Context, *GetBoardStatusReq) (*GetBoardStatusResp, error)
	GetDrawStatusList(context.Context, *GetDrawStatusListReq) (*GetDrawStatusListResp, error)
	GetLineParaList(context.Context, *GetLineParaListReq) (*GetLineParaListResp, error)
}

func RegisterChanneldrawgameServer(s *grpc.Server, srv ChanneldrawgameServer) {
	s.RegisterService(&_Channeldrawgame_serviceDesc, srv)
}

func _Channeldrawgame_CreateLine_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatLineReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChanneldrawgameServer).CreateLine(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channeldrawgame.channeldrawgame/CreateLine",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChanneldrawgameServer).CreateLine(ctx, req.(*CreatLineReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channeldrawgame_CancelLine_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelLineReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChanneldrawgameServer).CancelLine(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channeldrawgame.channeldrawgame/CancelLine",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChanneldrawgameServer).CancelLine(ctx, req.(*CancelLineReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channeldrawgame_AddPointList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPointListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChanneldrawgameServer).AddPointList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channeldrawgame.channeldrawgame/AddPointList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChanneldrawgameServer).AddPointList(ctx, req.(*AddPointListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channeldrawgame_CancelByUid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelByUidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChanneldrawgameServer).CancelByUid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channeldrawgame.channeldrawgame/CancelByUid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChanneldrawgameServer).CancelByUid(ctx, req.(*CancelByUidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channeldrawgame_RemovePicture_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemovePictureReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChanneldrawgameServer).RemovePicture(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channeldrawgame.channeldrawgame/RemovePicture",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChanneldrawgameServer).RemovePicture(ctx, req.(*RemovePictureReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channeldrawgame_GetPicture_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPictureReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChanneldrawgameServer).GetPicture(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channeldrawgame.channeldrawgame/GetPicture",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChanneldrawgameServer).GetPicture(ctx, req.(*GetPictureReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channeldrawgame_SetBoardStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetBoardStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChanneldrawgameServer).SetBoardStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channeldrawgame.channeldrawgame/SetBoardStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChanneldrawgameServer).SetBoardStatus(ctx, req.(*SetBoardStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channeldrawgame_SetDrawStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetDrawStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChanneldrawgameServer).SetDrawStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channeldrawgame.channeldrawgame/SetDrawStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChanneldrawgameServer).SetDrawStatus(ctx, req.(*SetDrawStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channeldrawgame_GetBoardStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBoardStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChanneldrawgameServer).GetBoardStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channeldrawgame.channeldrawgame/GetBoardStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChanneldrawgameServer).GetBoardStatus(ctx, req.(*GetBoardStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channeldrawgame_GetDrawStatusList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDrawStatusListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChanneldrawgameServer).GetDrawStatusList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channeldrawgame.channeldrawgame/GetDrawStatusList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChanneldrawgameServer).GetDrawStatusList(ctx, req.(*GetDrawStatusListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channeldrawgame_GetLineParaList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLineParaListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChanneldrawgameServer).GetLineParaList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channeldrawgame.channeldrawgame/GetLineParaList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChanneldrawgameServer).GetLineParaList(ctx, req.(*GetLineParaListReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Channeldrawgame_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channeldrawgame.channeldrawgame",
	HandlerType: (*ChanneldrawgameServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateLine",
			Handler:    _Channeldrawgame_CreateLine_Handler,
		},
		{
			MethodName: "CancelLine",
			Handler:    _Channeldrawgame_CancelLine_Handler,
		},
		{
			MethodName: "AddPointList",
			Handler:    _Channeldrawgame_AddPointList_Handler,
		},
		{
			MethodName: "CancelByUid",
			Handler:    _Channeldrawgame_CancelByUid_Handler,
		},
		{
			MethodName: "RemovePicture",
			Handler:    _Channeldrawgame_RemovePicture_Handler,
		},
		{
			MethodName: "GetPicture",
			Handler:    _Channeldrawgame_GetPicture_Handler,
		},
		{
			MethodName: "SetBoardStatus",
			Handler:    _Channeldrawgame_SetBoardStatus_Handler,
		},
		{
			MethodName: "SetDrawStatus",
			Handler:    _Channeldrawgame_SetDrawStatus_Handler,
		},
		{
			MethodName: "GetBoardStatus",
			Handler:    _Channeldrawgame_GetBoardStatus_Handler,
		},
		{
			MethodName: "GetDrawStatusList",
			Handler:    _Channeldrawgame_GetDrawStatusList_Handler,
		},
		{
			MethodName: "GetLineParaList",
			Handler:    _Channeldrawgame_GetLineParaList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/channeldrawgame/channeldrawgame.proto",
}

func (m *Point) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Point) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(m.X))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(m.Y))
	return i, nil
}

func (m *Line) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Line) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(m.LineID))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(m.Size_))
	dAtA[i] = 0x22
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(len(m.Color)))
	i += copy(dAtA[i:], m.Color)
	if len(m.PointList) > 0 {
		for _, msg := range m.PointList {
			dAtA[i] = 0x2a
			i++
			i = encodeVarintChanneldrawgame(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x32
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(len(m.ParaId)))
	i += copy(dAtA[i:], m.ParaId)
	dAtA[i] = 0x38
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(m.LineType))
	return i, nil
}

func (m *Picture) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Picture) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(m.ChannelID))
	if len(m.LineList) > 0 {
		for _, msg := range m.LineList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintChanneldrawgame(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *CancelLineReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CancelLineReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(m.ChannelID))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(m.LineID))
	return i, nil
}

func (m *CancelLineResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CancelLineResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *RemovePictureReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemovePictureReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(m.ChannelID))
	return i, nil
}

func (m *RemovePictureResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemovePictureResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(m.OperID))
	return i, nil
}

func (m *GetPictureReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPictureReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(m.ChannelID))
	return i, nil
}

func (m *GetPictureResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPictureResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(m.OperID))
	if m.Picture != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintChanneldrawgame(dAtA, i, uint64(m.Picture.Size()))
		n1, err := m.Picture.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *CreatLineReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreatLineReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(m.ChannelID))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(m.Size_))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(len(m.Color)))
	i += copy(dAtA[i:], m.Color)
	dAtA[i] = 0x22
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(len(m.ParaId)))
	i += copy(dAtA[i:], m.ParaId)
	dAtA[i] = 0x28
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(m.LineType))
	return i, nil
}

func (m *CreatLineResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreatLineResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(m.ChannelID))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(m.LineID))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(m.OperID))
	return i, nil
}

func (m *AddPointListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddPointListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(m.ChannelID))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(m.LineID))
	if len(m.PointList) > 0 {
		for _, msg := range m.PointList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintChanneldrawgame(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *AddPointListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddPointListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *CancelByUidReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CancelByUidReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(m.ChannelID))
	return i, nil
}

func (m *CancelByUidResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CancelByUidResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *SetDrawStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetDrawStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(m.ChannelID))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(m.Status))
	return i, nil
}

func (m *SetDrawStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetDrawStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(m.Status))
	return i, nil
}

func (m *SetBoardStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetBoardStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(m.ChannelID))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(m.Status))
	return i, nil
}

func (m *SetBoardStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetBoardStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(m.Status))
	return i, nil
}

func (m *GetBoardStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetBoardStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(m.ChannelID))
	return i, nil
}

func (m *GetBoardStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetBoardStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(m.ChannelID))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(m.Interval))
	return i, nil
}

func (m *GetDrawStatusListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetDrawStatusListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(m.ChannelID))
	return i, nil
}

func (m *GetDrawStatusListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetDrawStatusListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChanneldrawgame(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *LinePara) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LinePara) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(len(m.ParaId)))
	i += copy(dAtA[i:], m.ParaId)
	dAtA[i] = 0x12
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(len(m.PreviewUrl)))
	i += copy(dAtA[i:], m.PreviewUrl)
	dAtA[i] = 0x22
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(len(m.IconUrl)))
	i += copy(dAtA[i:], m.IconUrl)
	if len(m.ColorList) > 0 {
		for _, s := range m.ColorList {
			dAtA[i] = 0x2a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x30
	i++
	i = encodeVarintChanneldrawgame(dAtA, i, uint64(m.Type))
	return i, nil
}

func (m *GetLineParaListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLineParaListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetLineParaListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLineParaListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.LineList) > 0 {
		for _, msg := range m.LineList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChanneldrawgame(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func encodeFixed64Channeldrawgame(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Channeldrawgame(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintChanneldrawgame(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *Point) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldrawgame(uint64(m.X))
	n += 1 + sovChanneldrawgame(uint64(m.Y))
	return n
}

func (m *Line) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldrawgame(uint64(m.LineID))
	n += 1 + sovChanneldrawgame(uint64(m.Uid))
	n += 1 + sovChanneldrawgame(uint64(m.Size_))
	l = len(m.Color)
	n += 1 + l + sovChanneldrawgame(uint64(l))
	if len(m.PointList) > 0 {
		for _, e := range m.PointList {
			l = e.Size()
			n += 1 + l + sovChanneldrawgame(uint64(l))
		}
	}
	l = len(m.ParaId)
	n += 1 + l + sovChanneldrawgame(uint64(l))
	n += 1 + sovChanneldrawgame(uint64(m.LineType))
	return n
}

func (m *Picture) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldrawgame(uint64(m.ChannelID))
	if len(m.LineList) > 0 {
		for _, e := range m.LineList {
			l = e.Size()
			n += 1 + l + sovChanneldrawgame(uint64(l))
		}
	}
	return n
}

func (m *CancelLineReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldrawgame(uint64(m.ChannelID))
	n += 1 + sovChanneldrawgame(uint64(m.LineID))
	return n
}

func (m *CancelLineResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *RemovePictureReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldrawgame(uint64(m.ChannelID))
	return n
}

func (m *RemovePictureResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldrawgame(uint64(m.OperID))
	return n
}

func (m *GetPictureReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldrawgame(uint64(m.ChannelID))
	return n
}

func (m *GetPictureResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldrawgame(uint64(m.OperID))
	if m.Picture != nil {
		l = m.Picture.Size()
		n += 1 + l + sovChanneldrawgame(uint64(l))
	}
	return n
}

func (m *CreatLineReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldrawgame(uint64(m.ChannelID))
	n += 1 + sovChanneldrawgame(uint64(m.Size_))
	l = len(m.Color)
	n += 1 + l + sovChanneldrawgame(uint64(l))
	l = len(m.ParaId)
	n += 1 + l + sovChanneldrawgame(uint64(l))
	n += 1 + sovChanneldrawgame(uint64(m.LineType))
	return n
}

func (m *CreatLineResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldrawgame(uint64(m.ChannelID))
	n += 1 + sovChanneldrawgame(uint64(m.LineID))
	n += 1 + sovChanneldrawgame(uint64(m.OperID))
	return n
}

func (m *AddPointListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldrawgame(uint64(m.ChannelID))
	n += 1 + sovChanneldrawgame(uint64(m.LineID))
	if len(m.PointList) > 0 {
		for _, e := range m.PointList {
			l = e.Size()
			n += 1 + l + sovChanneldrawgame(uint64(l))
		}
	}
	return n
}

func (m *AddPointListResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *CancelByUidReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldrawgame(uint64(m.ChannelID))
	return n
}

func (m *CancelByUidResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *SetDrawStatusReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldrawgame(uint64(m.ChannelID))
	n += 1 + sovChanneldrawgame(uint64(m.Status))
	return n
}

func (m *SetDrawStatusResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldrawgame(uint64(m.Status))
	return n
}

func (m *SetBoardStatusReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldrawgame(uint64(m.ChannelID))
	n += 1 + sovChanneldrawgame(uint64(m.Status))
	return n
}

func (m *SetBoardStatusResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldrawgame(uint64(m.Status))
	return n
}

func (m *GetBoardStatusReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldrawgame(uint64(m.ChannelID))
	return n
}

func (m *GetBoardStatusResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldrawgame(uint64(m.ChannelID))
	n += 1 + sovChanneldrawgame(uint64(m.Status))
	n += 1 + sovChanneldrawgame(uint64(m.Interval))
	return n
}

func (m *GetDrawStatusListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldrawgame(uint64(m.ChannelID))
	return n
}

func (m *GetDrawStatusListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovChanneldrawgame(uint64(e))
		}
	}
	return n
}

func (m *LinePara) Size() (n int) {
	var l int
	_ = l
	l = len(m.ParaId)
	n += 1 + l + sovChanneldrawgame(uint64(l))
	l = len(m.Name)
	n += 1 + l + sovChanneldrawgame(uint64(l))
	l = len(m.PreviewUrl)
	n += 1 + l + sovChanneldrawgame(uint64(l))
	l = len(m.IconUrl)
	n += 1 + l + sovChanneldrawgame(uint64(l))
	if len(m.ColorList) > 0 {
		for _, s := range m.ColorList {
			l = len(s)
			n += 1 + l + sovChanneldrawgame(uint64(l))
		}
	}
	n += 1 + sovChanneldrawgame(uint64(m.Type))
	return n
}

func (m *GetLineParaListReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetLineParaListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.LineList) > 0 {
		for _, e := range m.LineList {
			l = e.Size()
			n += 1 + l + sovChanneldrawgame(uint64(l))
		}
	}
	return n
}

func sovChanneldrawgame(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozChanneldrawgame(x uint64) (n int) {
	return sovChanneldrawgame(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *Point) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldrawgame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: Point: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: Point: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field X", wireType)
			}
			m.X = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.X |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Y", wireType)
			}
			m.Y = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Y |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldrawgame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("x")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("y")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *Line) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldrawgame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: Line: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: Line: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LineID", wireType)
			}
			m.LineID = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LineID |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Size_", wireType)
			}
			m.Size_ = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Size_ |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Color", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Color = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PointList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PointList = append(m.PointList, &Point{})
			if err := m.PointList[len(m.PointList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ParaId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ParaId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LineType", wireType)
			}
			m.LineType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LineType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldrawgame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("lineID")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("size")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("color")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *Picture) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldrawgame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: Picture: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: Picture: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelID", wireType)
			}
			m.ChannelID = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelID |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LineList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LineList = append(m.LineList, &Line{})
			if err := m.LineList[len(m.LineList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldrawgame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channelID")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CancelLineReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldrawgame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CancelLineReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CancelLineReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelID", wireType)
			}
			m.ChannelID = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelID |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LineID", wireType)
			}
			m.LineID = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LineID |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldrawgame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channelID")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("lineID")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CancelLineResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldrawgame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CancelLineResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CancelLineResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldrawgame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemovePictureReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldrawgame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemovePictureReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemovePictureReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelID", wireType)
			}
			m.ChannelID = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelID |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldrawgame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channelID")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemovePictureResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldrawgame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemovePictureResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemovePictureResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperID", wireType)
			}
			m.OperID = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OperID |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldrawgame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPictureReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldrawgame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPictureReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPictureReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelID", wireType)
			}
			m.ChannelID = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelID |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldrawgame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channelID")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPictureResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldrawgame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPictureResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPictureResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperID", wireType)
			}
			m.OperID = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OperID |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Picture", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Picture == nil {
				m.Picture = &Picture{}
			}
			if err := m.Picture.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldrawgame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreatLineReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldrawgame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreatLineReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreatLineReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelID", wireType)
			}
			m.ChannelID = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelID |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Size_", wireType)
			}
			m.Size_ = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Size_ |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Color", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Color = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ParaId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ParaId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LineType", wireType)
			}
			m.LineType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LineType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldrawgame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channelID")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("size")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("color")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreatLineResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldrawgame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreatLineResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreatLineResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelID", wireType)
			}
			m.ChannelID = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelID |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LineID", wireType)
			}
			m.LineID = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LineID |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperID", wireType)
			}
			m.OperID = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OperID |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldrawgame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channelID")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("lineID")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("operID")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddPointListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldrawgame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddPointListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddPointListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelID", wireType)
			}
			m.ChannelID = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelID |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LineID", wireType)
			}
			m.LineID = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LineID |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PointList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PointList = append(m.PointList, &Point{})
			if err := m.PointList[len(m.PointList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldrawgame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channelID")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("lineID")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddPointListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldrawgame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddPointListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddPointListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldrawgame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CancelByUidReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldrawgame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CancelByUidReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CancelByUidReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelID", wireType)
			}
			m.ChannelID = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelID |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldrawgame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channelID")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CancelByUidResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldrawgame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CancelByUidResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CancelByUidResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldrawgame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetDrawStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldrawgame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetDrawStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetDrawStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelID", wireType)
			}
			m.ChannelID = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelID |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldrawgame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channelID")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetDrawStatusResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldrawgame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetDrawStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetDrawStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldrawgame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetBoardStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldrawgame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetBoardStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetBoardStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelID", wireType)
			}
			m.ChannelID = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelID |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldrawgame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channelID")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetBoardStatusResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldrawgame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetBoardStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetBoardStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldrawgame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetBoardStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldrawgame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetBoardStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetBoardStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelID", wireType)
			}
			m.ChannelID = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelID |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldrawgame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channelID")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetBoardStatusResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldrawgame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetBoardStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetBoardStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelID", wireType)
			}
			m.ChannelID = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelID |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Interval", wireType)
			}
			m.Interval = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Interval |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldrawgame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channelID")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetDrawStatusListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldrawgame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetDrawStatusListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetDrawStatusListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelID", wireType)
			}
			m.ChannelID = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelID |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldrawgame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channelID")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetDrawStatusListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldrawgame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetDrawStatusListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetDrawStatusListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChanneldrawgame
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChanneldrawgame
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChanneldrawgame
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChanneldrawgame
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldrawgame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LinePara) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldrawgame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LinePara: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LinePara: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ParaId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ParaId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PreviewUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PreviewUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IconUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IconUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ColorList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ColorList = append(m.ColorList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldrawgame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("para_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("preview_url")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("icon_url")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLineParaListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldrawgame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLineParaListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLineParaListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldrawgame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLineParaListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldrawgame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLineParaListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLineParaListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LineList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LineList = append(m.LineList, &LinePara{})
			if err := m.LineList[len(m.LineList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldrawgame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldrawgame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipChanneldrawgame(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowChanneldrawgame
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChanneldrawgame
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthChanneldrawgame
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowChanneldrawgame
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipChanneldrawgame(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthChanneldrawgame = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowChanneldrawgame   = fmt2.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("src/channeldrawgame/channeldrawgame.proto", fileDescriptorChanneldrawgame)
}

var fileDescriptorChanneldrawgame = []byte{
	// 1162 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x96, 0xcd, 0x6f, 0x1b, 0x45,
	0x14, 0xc0, 0x3b, 0x5e, 0xc7, 0x1f, 0xcf, 0x71, 0xe2, 0x0c, 0x6d, 0x59, 0x56, 0x24, 0xd9, 0x4e,
	0x5b, 0x14, 0x82, 0xd2, 0x28, 0xa6, 0x6a, 0x51, 0x64, 0x59, 0x8a, 0x93, 0x60, 0x45, 0x4a, 0x48,
	0x70, 0xe3, 0xb3, 0xd9, 0xda, 0x2b, 0x58, 0x75, 0xb3, 0x3b, 0xec, 0xac, 0x93, 0xb8, 0xe2, 0x80,
	0x38, 0xf0, 0x75, 0x00, 0xd4, 0x1b, 0x12, 0xc7, 0x9c, 0x10, 0x7f, 0x06, 0x87, 0x1e, 0xf9, 0x0b,
	0x10, 0x0a, 0x97, 0x88, 0xbf, 0x02, 0xcd, 0x78, 0x77, 0xbd, 0x1f, 0xfe, 0x4a, 0xdb, 0xe3, 0xbe,
	0x79, 0x6f, 0xde, 0x6f, 0xde, 0xbe, 0x2f, 0x78, 0x9f, 0x39, 0xed, 0xf5, 0xf6, 0x17, 0x9a, 0x65,
	0xe9, 0x66, 0xc7, 0xd1, 0xce, 0x3e, 0xd7, 0x4e, 0xf4, 0xf8, 0xf7, 0x03, 0xea, 0xd8, 0xae, 0x8d,
	0xe7, 0x63, 0x62, 0xe5, 0x5e, 0xdb, 0x3e, 0x39, 0xb1, 0xad, 0x75, 0xd7, 0x3c, 0xa5, 0x46, 0xfb,
	0x99, 0xa9, 0xaf, 0xb3, 0x67, 0x4f, 0xbb, 0x86, 0xe9, 0x1a, 0x96, 0xdb, 0xa3, 0x9e, 0x19, 0x59,
	0x87, 0x99, 0x23, 0xdb, 0xb0, 0x5c, 0x8c, 0x01, 0x9d, 0xcb, 0x48, 0x4d, 0xad, 0x14, 0x6b, 0xe9,
	0x97, 0x7f, 0x2f, 0xdf, 0x68, 0xa0, 0x73, 0x2e, 0xeb, 0xc9, 0xa9, 0xb0, 0xac, 0x47, 0xfe, 0x43,
	0x90, 0xde, 0x37, 0x2c, 0x1d, 0xbf, 0x0b, 0x19, 0xd3, 0xb0, 0xf4, 0xbd, 0x9d, 0x88, 0x95, 0x27,
	0xc3, 0xb7, 0x41, 0xea, 0x1a, 0x9d, 0x88, 0x31, 0x17, 0x60, 0x19, 0xd2, 0xcc, 0x78, 0xae, 0xcb,
	0x52, 0xe8, 0x40, 0x48, 0xb0, 0x02, 0x33, 0x6d, 0xdb, 0xb4, 0x1d, 0x39, 0xad, 0xa6, 0x56, 0xf2,
	0xde, 0x51, 0x5f, 0x84, 0x1f, 0x42, 0x5e, 0x50, 0xee, 0x1b, 0xcc, 0x95, 0x67, 0x54, 0x69, 0xa5,
	0x50, 0xbe, 0xfd, 0x20, 0x1e, 0x07, 0xa1, 0xd1, 0x18, 0x28, 0xe2, 0x45, 0xc8, 0x52, 0xcd, 0xd1,
	0x5a, 0x46, 0x47, 0xce, 0xa8, 0x28, 0xb8, 0x33, 0xc3, 0x85, 0x7b, 0x1d, 0x7c, 0x07, 0xf2, 0x1c,
	0xb6, 0xc5, 0xa3, 0x21, 0x67, 0x55, 0x14, 0xf0, 0xe4, 0xb8, 0xf8, 0xb8, 0x47, 0x75, 0xf2, 0x19,
	0x64, 0x8f, 0x8c, 0xb6, 0xdb, 0x75, 0x74, 0x4c, 0x20, 0xef, 0x39, 0x8c, 0xbd, 0x78, 0x20, 0xc6,
	0x1b, 0x20, 0x4c, 0x05, 0x65, 0x4a, 0x50, 0xde, 0x4a, 0x50, 0xf2, 0xd8, 0x35, 0x02, 0x35, 0xf2,
	0x29, 0x14, 0xb7, 0x35, 0xab, 0xad, 0x9b, 0x42, 0xae, 0x7f, 0x39, 0x95, 0x9f, 0x41, 0xe8, 0x53,
	0xc9, 0xd0, 0x93, 0x12, 0xcc, 0x85, 0xaf, 0x64, 0x94, 0x3c, 0x82, 0x52, 0x43, 0x3f, 0xb1, 0x4f,
	0x75, 0xef, 0x31, 0x53, 0xfa, 0x21, 0x1b, 0xb0, 0x10, 0xb3, 0x63, 0x94, 0x3b, 0xb7, 0xa9, 0xee,
	0x08, 0xab, 0x41, 0xcc, 0x3c, 0x19, 0xf9, 0x10, 0x8a, 0x75, 0xdd, 0xbd, 0xa6, 0x9f, 0xa7, 0x30,
	0x17, 0x36, 0x9a, 0xe4, 0x04, 0x97, 0x21, 0x4b, 0xfb, 0xca, 0x72, 0x4a, 0x45, 0x2b, 0x85, 0xb2,
	0x9c, 0x4c, 0x06, 0xef, 0x32, 0x5f, 0x91, 0xfc, 0x8e, 0x60, 0x76, 0xdb, 0xd1, 0x35, 0xf7, 0x3a,
	0x81, 0xf6, 0xb3, 0x35, 0x35, 0x3a, 0x5b, 0xa5, 0x64, 0xb6, 0x86, 0xf2, 0x2e, 0x3d, 0x29, 0xef,
	0x66, 0x86, 0xe6, 0x9d, 0x0d, 0xc5, 0x10, 0x2b, 0xa3, 0xaf, 0x9f, 0x15, 0xa1, 0x88, 0x86, 0x4b,
	0xcf, 0xff, 0x6d, 0x3f, 0x20, 0x98, 0xdf, 0xea, 0x74, 0x82, 0xda, 0x79, 0x23, 0x99, 0xc8, 0xcb,
	0x96, 0x06, 0x65, 0x2b, 0x8d, 0x2f, 0xdb, 0x40, 0x91, 0x60, 0x28, 0x45, 0x51, 0x18, 0x25, 0x0f,
	0xfd, 0x9c, 0xae, 0xf5, 0x9a, 0x46, 0x67, 0xda, 0xbc, 0x5a, 0x80, 0xf9, 0x88, 0x15, 0xa3, 0xe4,
	0x18, 0x4a, 0x4f, 0x74, 0x77, 0xc7, 0xd1, 0xce, 0x9e, 0xb8, 0x9a, 0xdb, 0x65, 0xd7, 0x78, 0x28,
	0x13, 0x06, 0xd1, 0x87, 0xf6, 0x65, 0xbc, 0x50, 0x62, 0xb7, 0xf6, 0x73, 0xd8, 0x33, 0x41, 0x43,
	0x4c, 0x9a, 0xc2, 0xa4, 0x66, 0x6b, 0x4e, 0xe7, 0x4d, 0x92, 0x94, 0x01, 0xc7, 0xaf, 0x9d, 0x88,
	0xf2, 0x18, 0x16, 0xea, 0xaf, 0x82, 0x42, 0xce, 0x01, 0xd7, 0x93, 0xce, 0x5e, 0xfb, 0x11, 0x58,
	0x85, 0x9c, 0x61, 0xb9, 0xba, 0x73, 0xaa, 0x99, 0xb2, 0x14, 0x2e, 0x10, 0x5f, 0x4a, 0x36, 0xe1,
	0x66, 0x3d, 0x1c, 0xf0, 0x6b, 0xe4, 0x2c, 0xd9, 0x80, 0x5b, 0x43, 0x6c, 0x19, 0xc5, 0x32, 0x64,
	0xbb, 0x46, 0x47, 0x24, 0x2b, 0x52, 0xa5, 0x95, 0x62, 0xc3, 0xff, 0x24, 0x7f, 0x22, 0xc8, 0xf1,
	0x5a, 0x3c, 0xd2, 0x1c, 0x2d, 0x5c, 0xde, 0x28, 0x54, 0xfc, 0x7e, 0x79, 0xcb, 0x90, 0xb6, 0xb4,
	0x93, 0x7e, 0xcf, 0xf0, 0xcf, 0x84, 0x04, 0xdf, 0x87, 0x02, 0x75, 0xf4, 0x53, 0x43, 0x3f, 0x6b,
	0x75, 0x1d, 0x33, 0xd2, 0x39, 0xc0, 0x3b, 0x68, 0x3a, 0x26, 0x5e, 0x86, 0x9c, 0xd1, 0xb6, 0x2d,
	0xa1, 0x13, 0x9e, 0x85, 0x59, 0x2e, 0xe5, 0x0a, 0x8b, 0x00, 0xa2, 0xd1, 0xb4, 0x4c, 0x7f, 0x1c,
	0xe6, 0x1b, 0x79, 0x21, 0x11, 0x63, 0x4f, 0x86, 0xb4, 0x68, 0x2d, 0x99, 0x70, 0xd3, 0xe2, 0x12,
	0x72, 0x53, 0xfc, 0x2f, 0xff, 0x21, 0x5e, 0xcc, 0xc8, 0x01, 0xbc, 0x95, 0x90, 0x32, 0x8a, 0x1f,
	0x79, 0x6d, 0xca, 0xf4, 0xe3, 0x51, 0x28, 0xbf, 0x33, 0x74, 0x9a, 0x71, 0xab, 0xc1, 0x44, 0x5b,
	0xfd, 0xb8, 0x1f, 0x2a, 0xde, 0xc7, 0xf0, 0x1c, 0xc0, 0x51, 0xb3, 0xb1, 0xdb, 0xda, 0x3e, 0xdc,
	0x3f, 0x6c, 0x94, 0x6e, 0xf0, 0xef, 0x83, 0xe6, 0xfe, 0xb1, 0xf7, 0x8d, 0xf8, 0xf7, 0xde, 0xf6,
	0xe1, 0x27, 0xad, 0xbd, 0x83, 0xad, 0xfa, 0x6e, 0x29, 0x85, 0x01, 0x32, 0x8d, 0x66, 0xad, 0xb6,
	0xdb, 0x28, 0x49, 0xe5, 0xdf, 0x66, 0x21, 0xbe, 0xd3, 0xe0, 0x9f, 0x10, 0x80, 0x68, 0x8c, 0xba,
	0x58, 0x41, 0x16, 0x13, 0x3c, 0xe1, 0x0e, 0xaf, 0x2c, 0x8d, 0x3b, 0x66, 0x94, 0x6c, 0x7d, 0x7d,
	0x71, 0x25, 0xa1, 0x1f, 0x2f, 0xae, 0xa4, 0x5c, 0x77, 0xf3, 0x7c, 0x93, 0x6d, 0x3a, 0x9b, 0x2f,
	0x2e, 0xae, 0xa4, 0xd5, 0xb5, 0xae, 0x5a, 0xe9, 0x1a, 0x9d, 0xaa, 0xba, 0x76, 0xae, 0x56, 0x82,
	0xbc, 0xa9, 0xaa, 0x6b, 0xac, 0xc2, 0x7b, 0x7f, 0x55, 0x5d, 0x73, 0x2a, 0x22, 0xdc, 0x55, 0xfc,
	0x1d, 0x07, 0x0a, 0x86, 0x2d, 0x1e, 0xe2, 0x31, 0x3c, 0xdc, 0x95, 0xe5, 0xb1, 0xe7, 0x8c, 0x92,
	0x8f, 0x38, 0x52, 0x8a, 0x23, 0x65, 0x38, 0x92, 0x29, 0x80, 0xee, 0x8e, 0x04, 0x32, 0xd5, 0x4a,
	0xbf, 0xd5, 0x56, 0xf1, 0xaf, 0x08, 0x66, 0xc3, 0x6d, 0x13, 0xab, 0x09, 0x5f, 0xb1, 0x06, 0xaf,
	0xdc, 0x99, 0xa0, 0xc1, 0x28, 0xd9, 0xe1, 0x3c, 0x52, 0x10, 0x22, 0xea, 0x11, 0xad, 0x8d, 0x24,
	0xa2, 0x15, 0xd1, 0xc8, 0x19, 0x87, 0x0b, 0xd8, 0xbe, 0x82, 0x42, 0xa8, 0x0f, 0xe3, 0x51, 0x51,
	0xf0, 0x7b, 0xbb, 0xa2, 0x8e, 0x57, 0x60, 0x94, 0xac, 0x72, 0xae, 0x34, 0xe7, 0x4a, 0x73, 0x2e,
	0xce, 0xf4, 0xf6, 0x08, 0x26, 0xfc, 0x0d, 0x82, 0x62, 0x64, 0x8d, 0xc1, 0xc9, 0x87, 0xc7, 0xd7,
	0x23, 0x85, 0x4c, 0x52, 0xf1, 0x21, 0x66, 0xa6, 0x83, 0xe8, 0x01, 0x0c, 0x56, 0x9c, 0x21, 0x79,
	0x12, 0x59, 0x9a, 0x86, 0xe4, 0x49, 0x74, 0x3f, 0xea, 0xbb, 0xce, 0x4c, 0xe7, 0xfa, 0x05, 0x82,
	0xb9, 0xe8, 0x4c, 0xc0, 0xc9, 0xd7, 0x25, 0x66, 0x91, 0x72, 0x77, 0xa2, 0x0e, 0xa3, 0xe4, 0x31,
	0xe7, 0xc8, 0x06, 0xf9, 0xea, 0x0a, 0x12, 0x32, 0x32, 0x3b, 0xdc, 0x4a, 0xbf, 0xc3, 0x57, 0xf1,
	0xcf, 0x08, 0x8a, 0x91, 0x91, 0x39, 0xe4, 0xa7, 0xc4, 0x07, 0xb5, 0x42, 0x26, 0xa9, 0xf8, 0x44,
	0xb9, 0x57, 0x20, 0xfa, 0x16, 0x89, 0x2d, 0x74, 0x7c, 0x98, 0xea, 0x53, 0x84, 0x29, 0x39, 0x12,
	0xc9, 0x07, 0x1c, 0x2a, 0x1f, 0xf9, 0x5d, 0xf2, 0x28, 0x24, 0x1e, 0x9a, 0x85, 0xc4, 0x80, 0xc2,
	0xf7, 0x87, 0xf9, 0x49, 0x0c, 0x40, 0xe5, 0xbd, 0x69, 0xd4, 0x7c, 0x22, 0x98, 0x92, 0x88, 0x6f,
	0x87, 0xb1, 0x11, 0x81, 0x87, 0xbe, 0x3b, 0x36, 0x5a, 0x94, 0x7b, 0x93, 0x95, 0x7c, 0x96, 0xc2,
	0x74, 0x2c, 0x4a, 0xe6, 0xfb, 0x8b, 0x2b, 0xe9, 0x8f, 0xe7, 0xb5, 0xd2, 0xcb, 0xcb, 0x25, 0xf4,
	0xd7, 0xe5, 0x12, 0xfa, 0xe7, 0x72, 0x09, 0xfd, 0xf2, 0xef, 0xd2, 0x8d, 0xff, 0x03, 0x00, 0x00,
	0xff, 0xff, 0x0a, 0x01, 0x6d, 0x4c, 0x2d, 0x0f, 0x00, 0x00,
}
