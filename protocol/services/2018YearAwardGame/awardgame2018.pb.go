// Code generated by protoc-gen-gogo.
// source: services/2018YearAwardGame/awardgame2018.proto
// DO NOT EDIT!

/*
	Package awardgame2018 is a generated protocol buffer package.

	namespace

	It is generated from these files:
		services/2018YearAwardGame/awardgame2018.proto

	It has these top-level messages:
		SingleTypeSignUpInfo
		MutiTypeSignUpMember
		MutiTypeTeamInfo
		MutiTypeSignUpInfo
		RichManSignUpInfo
		GetUserSignUpInfoReq
		GetUserSignUpInfoResp
		GetUserMutiSignUpInfoReq
		GetUserMutiSignUpInfoResp
		SetUserSignUpInfoReq
		SetUserSignUpInfoResp
		JoinMutiTypeTeamReq
		JoinMutiTypeTeamResp
		QuitMutiTypeTeamReq
		QuitMutiTypeTeamResp
		CreateMutiTypeTeamReq
		CreateMutiTypeTeamResp
		UpdateMutiTypeTeamNotifyReq
		UpdateMutiTypeTeamNotifyResp
		GetMutiTypeTeamInfoReq
		GetMutiTypeTeamInfoResp
		SingleTypeSignUpCount
		MutiTypeSignUpCount
		GetSignupCountReq
		GetSignupCountResp
		GetCurrStageReq
		GetCurrStageResp
		NotifyDateChangeReq
		MultiTypeTeamMemberRankInfo
		MultiTypeTeamRankBrief
		MultiTypeTeamRankInfo
		GetMultiTypeTeamRankListReq
		GetMultiTypeTeamRankListResp
		GetMultiTypeTeamRankInfoReq
		GetMultiTypeTeamRankInfoResp
		RichmanRankItem
		RichmanMyRankValue
		GetRichmanRankListReq
		GetRichmanRankListResp
		SingleRankItem
		SingleMyRankValue
		GetSingleRankListReq
		GetSingleRankListResp
		ChannelRankItem
		GetChannelRankListReq
		GetChannelRankListResp
		RichmanBattleProgress
		GetRichmanBattleProgressListReq
		GetRichmanBattleProgressListResp
		DoTestReq
		DoTestResp
		ShorttimeComsumeInfo
*/
package awardgame2018

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import io1 "io"
import fmt2 "fmt"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type EStageType int32

const (
	EStageType_ENUM_STAGE_TYPE_INVALID         EStageType = 0
	EStageType_ENUM_STAGE_TYPE_SIGNUP          EStageType = 1
	EStageType_ENUM_STAGE_TYPE_GOLD_GAME       EStageType = 2
	EStageType_ENUM_STAGE_TYPE_PT_GAME         EStageType = 3
	EStageType_ENUM_STAGE_TYPE_DIAMOND_GAME    EStageType = 4
	EStageType_ENUM_STAGE_TYPE_KING_GAME       EStageType = 5
	EStageType_ENUM_STAGE_TYPE_SUPER_KING_GAME EStageType = 6
	EStageType_ENUM_STAGE_TYPE_FIN             EStageType = 7
)

var EStageType_name = map[int32]string{
	0: "ENUM_STAGE_TYPE_INVALID",
	1: "ENUM_STAGE_TYPE_SIGNUP",
	2: "ENUM_STAGE_TYPE_GOLD_GAME",
	3: "ENUM_STAGE_TYPE_PT_GAME",
	4: "ENUM_STAGE_TYPE_DIAMOND_GAME",
	5: "ENUM_STAGE_TYPE_KING_GAME",
	6: "ENUM_STAGE_TYPE_SUPER_KING_GAME",
	7: "ENUM_STAGE_TYPE_FIN",
}
var EStageType_value = map[string]int32{
	"ENUM_STAGE_TYPE_INVALID":         0,
	"ENUM_STAGE_TYPE_SIGNUP":          1,
	"ENUM_STAGE_TYPE_GOLD_GAME":       2,
	"ENUM_STAGE_TYPE_PT_GAME":         3,
	"ENUM_STAGE_TYPE_DIAMOND_GAME":    4,
	"ENUM_STAGE_TYPE_KING_GAME":       5,
	"ENUM_STAGE_TYPE_SUPER_KING_GAME": 6,
	"ENUM_STAGE_TYPE_FIN":             7,
}

func (x EStageType) String() string {
	return proto.EnumName(EStageType_name, int32(x))
}
func (EStageType) EnumDescriptor() ([]byte, []int) { return fileDescriptorAwardgame2018, []int{0} }

// 单人榜 报名
type ESingleSignUpType int32

const (
	ESingleSignUpType_ENUM_SINGLE_SIGNUP_INVALID      ESingleSignUpType = 0
	ESingleSignUpType_ENUM_SINGLE_SIGNUP_MAN          ESingleSignUpType = 1
	ESingleSignUpType_ENUM_SINGLE_SIGNUP_WOMAN        ESingleSignUpType = 2
	ESingleSignUpType_ENUM_SINGLE_SIGNUP_PRESENTER    ESingleSignUpType = 3
	ESingleSignUpType_ENUM_SINGLE_SIGNUP_MAN_SINGER   ESingleSignUpType = 4
	ESingleSignUpType_ENUM_SINGLE_SIGNUP_WOMAN_SINGER ESingleSignUpType = 5
	ESingleSignUpType_ENUM_SINGLE_SIGNUP_SKILL        ESingleSignUpType = 6
)

var ESingleSignUpType_name = map[int32]string{
	0: "ENUM_SINGLE_SIGNUP_INVALID",
	1: "ENUM_SINGLE_SIGNUP_MAN",
	2: "ENUM_SINGLE_SIGNUP_WOMAN",
	3: "ENUM_SINGLE_SIGNUP_PRESENTER",
	4: "ENUM_SINGLE_SIGNUP_MAN_SINGER",
	5: "ENUM_SINGLE_SIGNUP_WOMAN_SINGER",
	6: "ENUM_SINGLE_SIGNUP_SKILL",
}
var ESingleSignUpType_value = map[string]int32{
	"ENUM_SINGLE_SIGNUP_INVALID":      0,
	"ENUM_SINGLE_SIGNUP_MAN":          1,
	"ENUM_SINGLE_SIGNUP_WOMAN":        2,
	"ENUM_SINGLE_SIGNUP_PRESENTER":    3,
	"ENUM_SINGLE_SIGNUP_MAN_SINGER":   4,
	"ENUM_SINGLE_SIGNUP_WOMAN_SINGER": 5,
	"ENUM_SINGLE_SIGNUP_SKILL":        6,
}

func (x ESingleSignUpType) String() string {
	return proto.EnumName(ESingleSignUpType_name, int32(x))
}
func (ESingleSignUpType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{1}
}

// 多人榜 报名
type EMutiSignUpType int32

const (
	EMutiSignUpType_ENUM_MUTI_SIGNUP_INVALID EMutiSignUpType = 0
	EMutiSignUpType_ENUM_MUTI_SIGNUP_CP      EMutiSignUpType = 1
	EMutiSignUpType_ENUM_MUTI_SIGNUP_TEAM    EMutiSignUpType = 2
)

var EMutiSignUpType_name = map[int32]string{
	0: "ENUM_MUTI_SIGNUP_INVALID",
	1: "ENUM_MUTI_SIGNUP_CP",
	2: "ENUM_MUTI_SIGNUP_TEAM",
}
var EMutiSignUpType_value = map[string]int32{
	"ENUM_MUTI_SIGNUP_INVALID": 0,
	"ENUM_MUTI_SIGNUP_CP":      1,
	"ENUM_MUTI_SIGNUP_TEAM":    2,
}

func (x EMutiSignUpType) String() string {
	return proto.EnumName(EMutiSignUpType_name, int32(x))
}
func (EMutiSignUpType) EnumDescriptor() ([]byte, []int) { return fileDescriptorAwardgame2018, []int{2} }

type EMutiSignUpQuitReasonType int32

const (
	EMutiSignUpQuitReasonType_ENUM_MUTI_SIGNUPQUIT_REASON_INVALID  EMutiSignUpQuitReasonType = 0
	EMutiSignUpQuitReasonType_ENUM_MUTI_SIGNUPQUIT_REASON_SELF     EMutiSignUpQuitReasonType = 1
	EMutiSignUpQuitReasonType_ENUM_MUTI_SIGNUPQUIT_REASON_KICK     EMutiSignUpQuitReasonType = 2
	EMutiSignUpQuitReasonType_ENUM_MUTI_SIGNUPQUIT_REASON_DISSMISS EMutiSignUpQuitReasonType = 3
	EMutiSignUpQuitReasonType_ENUM_MUTI_SIGNUPQUIT_REASON_CLEANUP  EMutiSignUpQuitReasonType = 4
)

var EMutiSignUpQuitReasonType_name = map[int32]string{
	0: "ENUM_MUTI_SIGNUPQUIT_REASON_INVALID",
	1: "ENUM_MUTI_SIGNUPQUIT_REASON_SELF",
	2: "ENUM_MUTI_SIGNUPQUIT_REASON_KICK",
	3: "ENUM_MUTI_SIGNUPQUIT_REASON_DISSMISS",
	4: "ENUM_MUTI_SIGNUPQUIT_REASON_CLEANUP",
}
var EMutiSignUpQuitReasonType_value = map[string]int32{
	"ENUM_MUTI_SIGNUPQUIT_REASON_INVALID":  0,
	"ENUM_MUTI_SIGNUPQUIT_REASON_SELF":     1,
	"ENUM_MUTI_SIGNUPQUIT_REASON_KICK":     2,
	"ENUM_MUTI_SIGNUPQUIT_REASON_DISSMISS": 3,
	"ENUM_MUTI_SIGNUPQUIT_REASON_CLEANUP":  4,
}

func (x EMutiSignUpQuitReasonType) String() string {
	return proto.EnumName(EMutiSignUpQuitReasonType_name, int32(x))
}
func (EMutiSignUpQuitReasonType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{3}
}

type SingleTypeSignUpInfo struct {
	Uid         uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	IsPayTicket bool   `protobuf:"varint,2,opt,name=is_pay_ticket,json=isPayTicket,proto3" json:"is_pay_ticket,omitempty"`
	SingleType  uint32 `protobuf:"varint,3,opt,name=single_type,json=singleType,proto3" json:"single_type,omitempty"`
	JoinTs      uint32 `protobuf:"varint,4,opt,name=join_ts,json=joinTs,proto3" json:"join_ts,omitempty"`
}

func (m *SingleTypeSignUpInfo) Reset()         { *m = SingleTypeSignUpInfo{} }
func (m *SingleTypeSignUpInfo) String() string { return proto.CompactTextString(m) }
func (*SingleTypeSignUpInfo) ProtoMessage()    {}
func (*SingleTypeSignUpInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{0}
}

func (m *SingleTypeSignUpInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SingleTypeSignUpInfo) GetIsPayTicket() bool {
	if m != nil {
		return m.IsPayTicket
	}
	return false
}

func (m *SingleTypeSignUpInfo) GetSingleType() uint32 {
	if m != nil {
		return m.SingleType
	}
	return 0
}

func (m *SingleTypeSignUpInfo) GetJoinTs() uint32 {
	if m != nil {
		return m.JoinTs
	}
	return 0
}

type MutiTypeSignUpMember struct {
	Uid         uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	JoinTs      uint32 `protobuf:"varint,2,opt,name=join_ts,json=joinTs,proto3" json:"join_ts,omitempty"`
	IsPayTicket bool   `protobuf:"varint,3,opt,name=is_pay_ticket,json=isPayTicket,proto3" json:"is_pay_ticket,omitempty"`
	IsCreator   bool   `protobuf:"varint,4,opt,name=is_creator,json=isCreator,proto3" json:"is_creator,omitempty"`
	MutiType    uint32 `protobuf:"varint,5,opt,name=muti_type,json=mutiType,proto3" json:"muti_type,omitempty"`
}

func (m *MutiTypeSignUpMember) Reset()         { *m = MutiTypeSignUpMember{} }
func (m *MutiTypeSignUpMember) String() string { return proto.CompactTextString(m) }
func (*MutiTypeSignUpMember) ProtoMessage()    {}
func (*MutiTypeSignUpMember) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{1}
}

func (m *MutiTypeSignUpMember) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MutiTypeSignUpMember) GetJoinTs() uint32 {
	if m != nil {
		return m.JoinTs
	}
	return 0
}

func (m *MutiTypeSignUpMember) GetIsPayTicket() bool {
	if m != nil {
		return m.IsPayTicket
	}
	return false
}

func (m *MutiTypeSignUpMember) GetIsCreator() bool {
	if m != nil {
		return m.IsCreator
	}
	return false
}

func (m *MutiTypeSignUpMember) GetMutiType() uint32 {
	if m != nil {
		return m.MutiType
	}
	return 0
}

type MutiTypeTeamInfo struct {
	MutiTeamId     uint32                  `protobuf:"varint,1,opt,name=muti_team_id,json=mutiTeamId,proto3" json:"muti_team_id,omitempty"`
	MutiTeamType   uint32                  `protobuf:"varint,2,opt,name=muti_team_type,json=mutiTeamType,proto3" json:"muti_team_type,omitempty"`
	MemberFullList []*MutiTypeSignUpMember `protobuf:"bytes,3,rep,name=member_full_list,json=memberFullList" json:"member_full_list,omitempty"`
	CreaterUid     uint32                  `protobuf:"varint,4,opt,name=creater_uid,json=createrUid,proto3" json:"creater_uid,omitempty"`
}

func (m *MutiTypeTeamInfo) Reset()                    { *m = MutiTypeTeamInfo{} }
func (m *MutiTypeTeamInfo) String() string            { return proto.CompactTextString(m) }
func (*MutiTypeTeamInfo) ProtoMessage()               {}
func (*MutiTypeTeamInfo) Descriptor() ([]byte, []int) { return fileDescriptorAwardgame2018, []int{2} }

func (m *MutiTypeTeamInfo) GetMutiTeamId() uint32 {
	if m != nil {
		return m.MutiTeamId
	}
	return 0
}

func (m *MutiTypeTeamInfo) GetMutiTeamType() uint32 {
	if m != nil {
		return m.MutiTeamType
	}
	return 0
}

func (m *MutiTypeTeamInfo) GetMemberFullList() []*MutiTypeSignUpMember {
	if m != nil {
		return m.MemberFullList
	}
	return nil
}

func (m *MutiTypeTeamInfo) GetCreaterUid() uint32 {
	if m != nil {
		return m.CreaterUid
	}
	return 0
}

type MutiTypeSignUpInfo struct {
	MutiType     uint32 `protobuf:"varint,1,opt,name=muti_type,json=mutiType,proto3" json:"muti_type,omitempty"`
	MutiTeamId   uint32 `protobuf:"varint,2,opt,name=muti_team_id,json=mutiTeamId,proto3" json:"muti_team_id,omitempty"`
	MutiTeamType uint32 `protobuf:"varint,3,opt,name=muti_team_type,json=mutiTeamType,proto3" json:"muti_team_type,omitempty"`
	JoinTs       uint32 `protobuf:"varint,4,opt,name=join_ts,json=joinTs,proto3" json:"join_ts,omitempty"`
	//
	IsPayTicket    bool                    `protobuf:"varint,5,opt,name=is_pay_ticket,json=isPayTicket,proto3" json:"is_pay_ticket,omitempty"`
	IsCreator      bool                    `protobuf:"varint,6,opt,name=is_creator,json=isCreator,proto3" json:"is_creator,omitempty"`
	MemberFullList []*MutiTypeSignUpMember `protobuf:"bytes,7,rep,name=member_full_list,json=memberFullList" json:"member_full_list,omitempty"`
	// 我上次退出队伍的原因
	LastTeamQuitReason   uint32 `protobuf:"varint,10,opt,name=last_team_quit_reason,json=lastTeamQuitReason,proto3" json:"last_team_quit_reason,omitempty"`
	LastTeamQuitId       uint32 `protobuf:"varint,11,opt,name=last_team_quit_id,json=lastTeamQuitId,proto3" json:"last_team_quit_id,omitempty"`
	LastTeamQuitOpUid    uint32 `protobuf:"varint,12,opt,name=last_team_quit_op_uid,json=lastTeamQuitOpUid,proto3" json:"last_team_quit_op_uid,omitempty"`
	IsNeedNotifyTeamQuit bool   `protobuf:"varint,13,opt,name=is_need_notify_team_quit,json=isNeedNotifyTeamQuit,proto3" json:"is_need_notify_team_quit,omitempty"`
	LastTeamQuitType     uint32 `protobuf:"varint,14,opt,name=last_team_quit_type,json=lastTeamQuitType,proto3" json:"last_team_quit_type,omitempty"`
}

func (m *MutiTypeSignUpInfo) Reset()                    { *m = MutiTypeSignUpInfo{} }
func (m *MutiTypeSignUpInfo) String() string            { return proto.CompactTextString(m) }
func (*MutiTypeSignUpInfo) ProtoMessage()               {}
func (*MutiTypeSignUpInfo) Descriptor() ([]byte, []int) { return fileDescriptorAwardgame2018, []int{3} }

func (m *MutiTypeSignUpInfo) GetMutiType() uint32 {
	if m != nil {
		return m.MutiType
	}
	return 0
}

func (m *MutiTypeSignUpInfo) GetMutiTeamId() uint32 {
	if m != nil {
		return m.MutiTeamId
	}
	return 0
}

func (m *MutiTypeSignUpInfo) GetMutiTeamType() uint32 {
	if m != nil {
		return m.MutiTeamType
	}
	return 0
}

func (m *MutiTypeSignUpInfo) GetJoinTs() uint32 {
	if m != nil {
		return m.JoinTs
	}
	return 0
}

func (m *MutiTypeSignUpInfo) GetIsPayTicket() bool {
	if m != nil {
		return m.IsPayTicket
	}
	return false
}

func (m *MutiTypeSignUpInfo) GetIsCreator() bool {
	if m != nil {
		return m.IsCreator
	}
	return false
}

func (m *MutiTypeSignUpInfo) GetMemberFullList() []*MutiTypeSignUpMember {
	if m != nil {
		return m.MemberFullList
	}
	return nil
}

func (m *MutiTypeSignUpInfo) GetLastTeamQuitReason() uint32 {
	if m != nil {
		return m.LastTeamQuitReason
	}
	return 0
}

func (m *MutiTypeSignUpInfo) GetLastTeamQuitId() uint32 {
	if m != nil {
		return m.LastTeamQuitId
	}
	return 0
}

func (m *MutiTypeSignUpInfo) GetLastTeamQuitOpUid() uint32 {
	if m != nil {
		return m.LastTeamQuitOpUid
	}
	return 0
}

func (m *MutiTypeSignUpInfo) GetIsNeedNotifyTeamQuit() bool {
	if m != nil {
		return m.IsNeedNotifyTeamQuit
	}
	return false
}

func (m *MutiTypeSignUpInfo) GetLastTeamQuitType() uint32 {
	if m != nil {
		return m.LastTeamQuitType
	}
	return 0
}

// 神豪榜 报名信息
type RichManSignUpInfo struct {
	Uid         uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	IsPayTicket bool   `protobuf:"varint,2,opt,name=is_pay_ticket,json=isPayTicket,proto3" json:"is_pay_ticket,omitempty"`
	JoinTs      uint32 `protobuf:"varint,3,opt,name=join_ts,json=joinTs,proto3" json:"join_ts,omitempty"`
}

func (m *RichManSignUpInfo) Reset()                    { *m = RichManSignUpInfo{} }
func (m *RichManSignUpInfo) String() string            { return proto.CompactTextString(m) }
func (*RichManSignUpInfo) ProtoMessage()               {}
func (*RichManSignUpInfo) Descriptor() ([]byte, []int) { return fileDescriptorAwardgame2018, []int{4} }

func (m *RichManSignUpInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RichManSignUpInfo) GetIsPayTicket() bool {
	if m != nil {
		return m.IsPayTicket
	}
	return false
}

func (m *RichManSignUpInfo) GetJoinTs() uint32 {
	if m != nil {
		return m.JoinTs
	}
	return 0
}

// 获取用户的报名信息
type GetUserSignUpInfoReq struct {
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (m *GetUserSignUpInfoReq) Reset()         { *m = GetUserSignUpInfoReq{} }
func (m *GetUserSignUpInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserSignUpInfoReq) ProtoMessage()    {}
func (*GetUserSignUpInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{5}
}

func (m *GetUserSignUpInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserSignUpInfoResp struct {
	SingleSignup  *SingleTypeSignUpInfo `protobuf:"bytes,1,opt,name=single_signup,json=singleSignup" json:"single_signup,omitempty"`
	MutiSignup    *MutiTypeSignUpInfo   `protobuf:"bytes,2,opt,name=muti_signup,json=mutiSignup" json:"muti_signup,omitempty"`
	RichmanSignup *RichManSignUpInfo    `protobuf:"bytes,3,opt,name=richman_signup,json=richmanSignup" json:"richman_signup,omitempty"`
}

func (m *GetUserSignUpInfoResp) Reset()         { *m = GetUserSignUpInfoResp{} }
func (m *GetUserSignUpInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserSignUpInfoResp) ProtoMessage()    {}
func (*GetUserSignUpInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{6}
}

func (m *GetUserSignUpInfoResp) GetSingleSignup() *SingleTypeSignUpInfo {
	if m != nil {
		return m.SingleSignup
	}
	return nil
}

func (m *GetUserSignUpInfoResp) GetMutiSignup() *MutiTypeSignUpInfo {
	if m != nil {
		return m.MutiSignup
	}
	return nil
}

func (m *GetUserSignUpInfoResp) GetRichmanSignup() *RichManSignUpInfo {
	if m != nil {
		return m.RichmanSignup
	}
	return nil
}

// 获取用户的多人榜报名信息
type GetUserMutiSignUpInfoReq struct {
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (m *GetUserMutiSignUpInfoReq) Reset()         { *m = GetUserMutiSignUpInfoReq{} }
func (m *GetUserMutiSignUpInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserMutiSignUpInfoReq) ProtoMessage()    {}
func (*GetUserMutiSignUpInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{7}
}

func (m *GetUserMutiSignUpInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserMutiSignUpInfoResp struct {
	MutiSignup *MutiTypeSignUpInfo `protobuf:"bytes,1,opt,name=muti_signup,json=mutiSignup" json:"muti_signup,omitempty"`
}

func (m *GetUserMutiSignUpInfoResp) Reset()         { *m = GetUserMutiSignUpInfoResp{} }
func (m *GetUserMutiSignUpInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserMutiSignUpInfoResp) ProtoMessage()    {}
func (*GetUserMutiSignUpInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{8}
}

func (m *GetUserMutiSignUpInfoResp) GetMutiSignup() *MutiTypeSignUpInfo {
	if m != nil {
		return m.MutiSignup
	}
	return nil
}

// 设置报名信息
type SetUserSignUpInfoReq struct {
	Uid         uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	IsPayTicket bool   `protobuf:"varint,2,opt,name=is_pay_ticket,json=isPayTicket,proto3" json:"is_pay_ticket,omitempty"`
	// 到底是报名哪个榜单 每次只能报一个
	SingleSignupType uint32 `protobuf:"varint,3,opt,name=single_signup_type,json=singleSignupType,proto3" json:"single_signup_type,omitempty"`
	MutiSignupType   uint32 `protobuf:"varint,4,opt,name=muti_signup_type,json=mutiSignupType,proto3" json:"muti_signup_type,omitempty"`
	IsSignupRichman  bool   `protobuf:"varint,5,opt,name=is_signup_richman,json=isSignupRichman,proto3" json:"is_signup_richman,omitempty"`
}

func (m *SetUserSignUpInfoReq) Reset()         { *m = SetUserSignUpInfoReq{} }
func (m *SetUserSignUpInfoReq) String() string { return proto.CompactTextString(m) }
func (*SetUserSignUpInfoReq) ProtoMessage()    {}
func (*SetUserSignUpInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{9}
}

func (m *SetUserSignUpInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserSignUpInfoReq) GetIsPayTicket() bool {
	if m != nil {
		return m.IsPayTicket
	}
	return false
}

func (m *SetUserSignUpInfoReq) GetSingleSignupType() uint32 {
	if m != nil {
		return m.SingleSignupType
	}
	return 0
}

func (m *SetUserSignUpInfoReq) GetMutiSignupType() uint32 {
	if m != nil {
		return m.MutiSignupType
	}
	return 0
}

func (m *SetUserSignUpInfoReq) GetIsSignupRichman() bool {
	if m != nil {
		return m.IsSignupRichman
	}
	return false
}

type SetUserSignUpInfoResp struct {
	SingleSignup  *SingleTypeSignUpInfo `protobuf:"bytes,1,opt,name=single_signup,json=singleSignup" json:"single_signup,omitempty"`
	MutiSignup    *MutiTypeSignUpInfo   `protobuf:"bytes,2,opt,name=muti_signup,json=mutiSignup" json:"muti_signup,omitempty"`
	RichmanSignup *RichManSignUpInfo    `protobuf:"bytes,3,opt,name=richman_signup,json=richmanSignup" json:"richman_signup,omitempty"`
}

func (m *SetUserSignUpInfoResp) Reset()         { *m = SetUserSignUpInfoResp{} }
func (m *SetUserSignUpInfoResp) String() string { return proto.CompactTextString(m) }
func (*SetUserSignUpInfoResp) ProtoMessage()    {}
func (*SetUserSignUpInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{10}
}

func (m *SetUserSignUpInfoResp) GetSingleSignup() *SingleTypeSignUpInfo {
	if m != nil {
		return m.SingleSignup
	}
	return nil
}

func (m *SetUserSignUpInfoResp) GetMutiSignup() *MutiTypeSignUpInfo {
	if m != nil {
		return m.MutiSignup
	}
	return nil
}

func (m *SetUserSignUpInfoResp) GetRichmanSignup() *RichManSignUpInfo {
	if m != nil {
		return m.RichmanSignup
	}
	return nil
}

// 加入 某个多人榜报名 队伍
type JoinMutiTypeTeamReq struct {
	Uid        uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	MutiTeamId uint32 `protobuf:"varint,2,opt,name=muti_team_id,json=mutiTeamId,proto3" json:"muti_team_id,omitempty"`
}

func (m *JoinMutiTypeTeamReq) Reset()         { *m = JoinMutiTypeTeamReq{} }
func (m *JoinMutiTypeTeamReq) String() string { return proto.CompactTextString(m) }
func (*JoinMutiTypeTeamReq) ProtoMessage()    {}
func (*JoinMutiTypeTeamReq) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{11}
}

func (m *JoinMutiTypeTeamReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *JoinMutiTypeTeamReq) GetMutiTeamId() uint32 {
	if m != nil {
		return m.MutiTeamId
	}
	return 0
}

type JoinMutiTypeTeamResp struct {
}

func (m *JoinMutiTypeTeamResp) Reset()         { *m = JoinMutiTypeTeamResp{} }
func (m *JoinMutiTypeTeamResp) String() string { return proto.CompactTextString(m) }
func (*JoinMutiTypeTeamResp) ProtoMessage()    {}
func (*JoinMutiTypeTeamResp) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{12}
}

// 退出 多人榜报名 队伍
type QuitMutiTypeTeamReq struct {
	OpUid      uint32 `protobuf:"varint,1,opt,name=op_uid,json=opUid,proto3" json:"op_uid,omitempty"`
	TargetUid  uint32 `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	ReasonType uint32 `protobuf:"varint,3,opt,name=reason_type,json=reasonType,proto3" json:"reason_type,omitempty"`
	MutiTeamId uint32 `protobuf:"varint,4,opt,name=muti_team_id,json=mutiTeamId,proto3" json:"muti_team_id,omitempty"`
}

func (m *QuitMutiTypeTeamReq) Reset()         { *m = QuitMutiTypeTeamReq{} }
func (m *QuitMutiTypeTeamReq) String() string { return proto.CompactTextString(m) }
func (*QuitMutiTypeTeamReq) ProtoMessage()    {}
func (*QuitMutiTypeTeamReq) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{13}
}

func (m *QuitMutiTypeTeamReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *QuitMutiTypeTeamReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *QuitMutiTypeTeamReq) GetReasonType() uint32 {
	if m != nil {
		return m.ReasonType
	}
	return 0
}

func (m *QuitMutiTypeTeamReq) GetMutiTeamId() uint32 {
	if m != nil {
		return m.MutiTeamId
	}
	return 0
}

type QuitMutiTypeTeamResp struct {
	TargetuserNewTeamId uint32 `protobuf:"varint,1,opt,name=targetuser_new_team_id,json=targetuserNewTeamId,proto3" json:"targetuser_new_team_id,omitempty"`
	TargetuserMutiType  uint32 `protobuf:"varint,2,opt,name=targetuser_muti_type,json=targetuserMutiType,proto3" json:"targetuser_muti_type,omitempty"`
}

func (m *QuitMutiTypeTeamResp) Reset()         { *m = QuitMutiTypeTeamResp{} }
func (m *QuitMutiTypeTeamResp) String() string { return proto.CompactTextString(m) }
func (*QuitMutiTypeTeamResp) ProtoMessage()    {}
func (*QuitMutiTypeTeamResp) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{14}
}

func (m *QuitMutiTypeTeamResp) GetTargetuserNewTeamId() uint32 {
	if m != nil {
		return m.TargetuserNewTeamId
	}
	return 0
}

func (m *QuitMutiTypeTeamResp) GetTargetuserMutiType() uint32 {
	if m != nil {
		return m.TargetuserMutiType
	}
	return 0
}

// 创建队伍
type CreateMutiTypeTeamReq struct {
	OpUid uint32 `protobuf:"varint,1,opt,name=op_uid,json=opUid,proto3" json:"op_uid,omitempty"`
}

func (m *CreateMutiTypeTeamReq) Reset()         { *m = CreateMutiTypeTeamReq{} }
func (m *CreateMutiTypeTeamReq) String() string { return proto.CompactTextString(m) }
func (*CreateMutiTypeTeamReq) ProtoMessage()    {}
func (*CreateMutiTypeTeamReq) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{15}
}

func (m *CreateMutiTypeTeamReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

type CreateMutiTypeTeamResp struct {
	MutiTeamId uint32 `protobuf:"varint,1,opt,name=muti_team_id,json=mutiTeamId,proto3" json:"muti_team_id,omitempty"`
}

func (m *CreateMutiTypeTeamResp) Reset()         { *m = CreateMutiTypeTeamResp{} }
func (m *CreateMutiTypeTeamResp) String() string { return proto.CompactTextString(m) }
func (*CreateMutiTypeTeamResp) ProtoMessage()    {}
func (*CreateMutiTypeTeamResp) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{16}
}

func (m *CreateMutiTypeTeamResp) GetMutiTeamId() uint32 {
	if m != nil {
		return m.MutiTeamId
	}
	return 0
}

// 设置用户队伍提醒
type UpdateMutiTypeTeamNotifyReq struct {
	OpUid  uint32 `protobuf:"varint,1,opt,name=op_uid,json=opUid,proto3" json:"op_uid,omitempty"`
	IsOpen bool   `protobuf:"varint,2,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
}

func (m *UpdateMutiTypeTeamNotifyReq) Reset()         { *m = UpdateMutiTypeTeamNotifyReq{} }
func (m *UpdateMutiTypeTeamNotifyReq) String() string { return proto.CompactTextString(m) }
func (*UpdateMutiTypeTeamNotifyReq) ProtoMessage()    {}
func (*UpdateMutiTypeTeamNotifyReq) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{17}
}

func (m *UpdateMutiTypeTeamNotifyReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *UpdateMutiTypeTeamNotifyReq) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

type UpdateMutiTypeTeamNotifyResp struct {
}

func (m *UpdateMutiTypeTeamNotifyResp) Reset()         { *m = UpdateMutiTypeTeamNotifyResp{} }
func (m *UpdateMutiTypeTeamNotifyResp) String() string { return proto.CompactTextString(m) }
func (*UpdateMutiTypeTeamNotifyResp) ProtoMessage()    {}
func (*UpdateMutiTypeTeamNotifyResp) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{18}
}

// 获取队伍信息
type GetMutiTypeTeamInfoReq struct {
	TeamId uint32 `protobuf:"varint,1,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"`
}

func (m *GetMutiTypeTeamInfoReq) Reset()         { *m = GetMutiTypeTeamInfoReq{} }
func (m *GetMutiTypeTeamInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetMutiTypeTeamInfoReq) ProtoMessage()    {}
func (*GetMutiTypeTeamInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{19}
}

func (m *GetMutiTypeTeamInfoReq) GetTeamId() uint32 {
	if m != nil {
		return m.TeamId
	}
	return 0
}

type GetMutiTypeTeamInfoResp struct {
	MutiInfo *MutiTypeTeamInfo `protobuf:"bytes,1,opt,name=muti_info,json=mutiInfo" json:"muti_info,omitempty"`
}

func (m *GetMutiTypeTeamInfoResp) Reset()         { *m = GetMutiTypeTeamInfoResp{} }
func (m *GetMutiTypeTeamInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetMutiTypeTeamInfoResp) ProtoMessage()    {}
func (*GetMutiTypeTeamInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{20}
}

func (m *GetMutiTypeTeamInfoResp) GetMutiInfo() *MutiTypeTeamInfo {
	if m != nil {
		return m.MutiInfo
	}
	return nil
}

// 获取报名人数
type SingleTypeSignUpCount struct {
	SingleType uint32 `protobuf:"varint,1,opt,name=single_type,json=singleType,proto3" json:"single_type,omitempty"`
	Count      uint32 `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (m *SingleTypeSignUpCount) Reset()         { *m = SingleTypeSignUpCount{} }
func (m *SingleTypeSignUpCount) String() string { return proto.CompactTextString(m) }
func (*SingleTypeSignUpCount) ProtoMessage()    {}
func (*SingleTypeSignUpCount) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{21}
}

func (m *SingleTypeSignUpCount) GetSingleType() uint32 {
	if m != nil {
		return m.SingleType
	}
	return 0
}

func (m *SingleTypeSignUpCount) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type MutiTypeSignUpCount struct {
	MutiType uint32 `protobuf:"varint,1,opt,name=muti_type,json=mutiType,proto3" json:"muti_type,omitempty"`
	Count    uint32 `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (m *MutiTypeSignUpCount) Reset()         { *m = MutiTypeSignUpCount{} }
func (m *MutiTypeSignUpCount) String() string { return proto.CompactTextString(m) }
func (*MutiTypeSignUpCount) ProtoMessage()    {}
func (*MutiTypeSignUpCount) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{22}
}

func (m *MutiTypeSignUpCount) GetMutiType() uint32 {
	if m != nil {
		return m.MutiType
	}
	return 0
}

func (m *MutiTypeSignUpCount) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetSignupCountReq struct {
}

func (m *GetSignupCountReq) Reset()                    { *m = GetSignupCountReq{} }
func (m *GetSignupCountReq) String() string            { return proto.CompactTextString(m) }
func (*GetSignupCountReq) ProtoMessage()               {}
func (*GetSignupCountReq) Descriptor() ([]byte, []int) { return fileDescriptorAwardgame2018, []int{23} }

type GetSignupCountResp struct {
	SingleSignupCntList []*SingleTypeSignUpCount `protobuf:"bytes,1,rep,name=single_signup_cnt_list,json=singleSignupCntList" json:"single_signup_cnt_list,omitempty"`
	MutiSignupCntList   []*MutiTypeSignUpCount   `protobuf:"bytes,2,rep,name=muti_signup_cnt_list,json=mutiSignupCntList" json:"muti_signup_cnt_list,omitempty"`
	RichmanSignupCnt    uint32                   `protobuf:"varint,3,opt,name=richman_signup_cnt,json=richmanSignupCnt,proto3" json:"richman_signup_cnt,omitempty"`
}

func (m *GetSignupCountResp) Reset()                    { *m = GetSignupCountResp{} }
func (m *GetSignupCountResp) String() string            { return proto.CompactTextString(m) }
func (*GetSignupCountResp) ProtoMessage()               {}
func (*GetSignupCountResp) Descriptor() ([]byte, []int) { return fileDescriptorAwardgame2018, []int{24} }

func (m *GetSignupCountResp) GetSingleSignupCntList() []*SingleTypeSignUpCount {
	if m != nil {
		return m.SingleSignupCntList
	}
	return nil
}

func (m *GetSignupCountResp) GetMutiSignupCntList() []*MutiTypeSignUpCount {
	if m != nil {
		return m.MutiSignupCntList
	}
	return nil
}

func (m *GetSignupCountResp) GetRichmanSignupCnt() uint32 {
	if m != nil {
		return m.RichmanSignupCnt
	}
	return 0
}

// 获取当前 年度盛典的阶段
type GetCurrStageReq struct {
}

func (m *GetCurrStageReq) Reset()                    { *m = GetCurrStageReq{} }
func (m *GetCurrStageReq) String() string            { return proto.CompactTextString(m) }
func (*GetCurrStageReq) ProtoMessage()               {}
func (*GetCurrStageReq) Descriptor() ([]byte, []int) { return fileDescriptorAwardgame2018, []int{25} }

type GetCurrStageResp struct {
	CurrStage        uint32 `protobuf:"varint,1,opt,name=curr_stage,json=currStage,proto3" json:"curr_stage,omitempty"`
	CurrStageBeginTs uint32 `protobuf:"varint,2,opt,name=curr_stage_begin_ts,json=currStageBeginTs,proto3" json:"curr_stage_begin_ts,omitempty"`
	NextStage        uint32 `protobuf:"varint,3,opt,name=next_stage,json=nextStage,proto3" json:"next_stage,omitempty"`
	NextStageBeginTs uint32 `protobuf:"varint,4,opt,name=next_stage_begin_ts,json=nextStageBeginTs,proto3" json:"next_stage_begin_ts,omitempty"`
}

func (m *GetCurrStageResp) Reset()                    { *m = GetCurrStageResp{} }
func (m *GetCurrStageResp) String() string            { return proto.CompactTextString(m) }
func (*GetCurrStageResp) ProtoMessage()               {}
func (*GetCurrStageResp) Descriptor() ([]byte, []int) { return fileDescriptorAwardgame2018, []int{26} }

func (m *GetCurrStageResp) GetCurrStage() uint32 {
	if m != nil {
		return m.CurrStage
	}
	return 0
}

func (m *GetCurrStageResp) GetCurrStageBeginTs() uint32 {
	if m != nil {
		return m.CurrStageBeginTs
	}
	return 0
}

func (m *GetCurrStageResp) GetNextStage() uint32 {
	if m != nil {
		return m.NextStage
	}
	return 0
}

func (m *GetCurrStageResp) GetNextStageBeginTs() uint32 {
	if m != nil {
		return m.NextStageBeginTs
	}
	return 0
}

type NotifyDateChangeReq struct {
	DateInt uint32 `protobuf:"varint,1,opt,name=date_int,json=dateInt,proto3" json:"date_int,omitempty"`
}

func (m *NotifyDateChangeReq) Reset()         { *m = NotifyDateChangeReq{} }
func (m *NotifyDateChangeReq) String() string { return proto.CompactTextString(m) }
func (*NotifyDateChangeReq) ProtoMessage()    {}
func (*NotifyDateChangeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{27}
}

func (m *NotifyDateChangeReq) GetDateInt() uint32 {
	if m != nil {
		return m.DateInt
	}
	return 0
}

// 多人榜
// 排行数据
type MultiTypeTeamMemberRankInfo struct {
	TeamId    uint32 `protobuf:"varint,1,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"`
	Uid       uint32 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	RankValue uint32 `protobuf:"varint,3,opt,name=rank_value,json=rankValue,proto3" json:"rank_value,omitempty"`
	IsCreator bool   `protobuf:"varint,4,opt,name=is_creator,json=isCreator,proto3" json:"is_creator,omitempty"`
}

func (m *MultiTypeTeamMemberRankInfo) Reset()         { *m = MultiTypeTeamMemberRankInfo{} }
func (m *MultiTypeTeamMemberRankInfo) String() string { return proto.CompactTextString(m) }
func (*MultiTypeTeamMemberRankInfo) ProtoMessage()    {}
func (*MultiTypeTeamMemberRankInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{28}
}

func (m *MultiTypeTeamMemberRankInfo) GetTeamId() uint32 {
	if m != nil {
		return m.TeamId
	}
	return 0
}

func (m *MultiTypeTeamMemberRankInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MultiTypeTeamMemberRankInfo) GetRankValue() uint32 {
	if m != nil {
		return m.RankValue
	}
	return 0
}

func (m *MultiTypeTeamMemberRankInfo) GetIsCreator() bool {
	if m != nil {
		return m.IsCreator
	}
	return false
}

type MultiTypeTeamRankBrief struct {
	TeamId     uint32 `protobuf:"varint,1,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"`
	ListType   uint32 `protobuf:"varint,2,opt,name=list_type,json=listType,proto3" json:"list_type,omitempty"`
	RankValue  uint32 `protobuf:"varint,3,opt,name=rank_value,json=rankValue,proto3" json:"rank_value,omitempty"`
	Ranking    uint32 `protobuf:"varint,4,opt,name=ranking,proto3" json:"ranking,omitempty"`
	BonusValue uint32 `protobuf:"varint,5,opt,name=bonus_value,json=bonusValue,proto3" json:"bonus_value,omitempty"`
}

func (m *MultiTypeTeamRankBrief) Reset()         { *m = MultiTypeTeamRankBrief{} }
func (m *MultiTypeTeamRankBrief) String() string { return proto.CompactTextString(m) }
func (*MultiTypeTeamRankBrief) ProtoMessage()    {}
func (*MultiTypeTeamRankBrief) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{29}
}

func (m *MultiTypeTeamRankBrief) GetTeamId() uint32 {
	if m != nil {
		return m.TeamId
	}
	return 0
}

func (m *MultiTypeTeamRankBrief) GetListType() uint32 {
	if m != nil {
		return m.ListType
	}
	return 0
}

func (m *MultiTypeTeamRankBrief) GetRankValue() uint32 {
	if m != nil {
		return m.RankValue
	}
	return 0
}

func (m *MultiTypeTeamRankBrief) GetRanking() uint32 {
	if m != nil {
		return m.Ranking
	}
	return 0
}

func (m *MultiTypeTeamRankBrief) GetBonusValue() uint32 {
	if m != nil {
		return m.BonusValue
	}
	return 0
}

type MultiTypeTeamRankInfo struct {
	BriefInfo  *MultiTypeTeamRankBrief        `protobuf:"bytes,1,opt,name=brief_info,json=briefInfo" json:"brief_info,omitempty"`
	MemberList []*MultiTypeTeamMemberRankInfo `protobuf:"bytes,2,rep,name=member_list,json=memberList" json:"member_list,omitempty"`
}

func (m *MultiTypeTeamRankInfo) Reset()         { *m = MultiTypeTeamRankInfo{} }
func (m *MultiTypeTeamRankInfo) String() string { return proto.CompactTextString(m) }
func (*MultiTypeTeamRankInfo) ProtoMessage()    {}
func (*MultiTypeTeamRankInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{30}
}

func (m *MultiTypeTeamRankInfo) GetBriefInfo() *MultiTypeTeamRankBrief {
	if m != nil {
		return m.BriefInfo
	}
	return nil
}

func (m *MultiTypeTeamRankInfo) GetMemberList() []*MultiTypeTeamMemberRankInfo {
	if m != nil {
		return m.MemberList
	}
	return nil
}

// 获取多人榜的队伍榜单
type GetMultiTypeTeamRankListReq struct {
	MultiTeamType uint32 `protobuf:"varint,1,opt,name=multi_team_type,json=multiTeamType,proto3" json:"multi_team_type,omitempty"`
	DateInt       uint32 `protobuf:"varint,2,opt,name=date_int,json=dateInt,proto3" json:"date_int,omitempty"`
	Stage         uint32 `protobuf:"varint,3,opt,name=stage,proto3" json:"stage,omitempty"`
	Offset        uint32 `protobuf:"varint,4,opt,name=offset,proto3" json:"offset,omitempty"`
	Count         uint32 `protobuf:"varint,5,opt,name=count,proto3" json:"count,omitempty"`
}

func (m *GetMultiTypeTeamRankListReq) Reset()         { *m = GetMultiTypeTeamRankListReq{} }
func (m *GetMultiTypeTeamRankListReq) String() string { return proto.CompactTextString(m) }
func (*GetMultiTypeTeamRankListReq) ProtoMessage()    {}
func (*GetMultiTypeTeamRankListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{31}
}

func (m *GetMultiTypeTeamRankListReq) GetMultiTeamType() uint32 {
	if m != nil {
		return m.MultiTeamType
	}
	return 0
}

func (m *GetMultiTypeTeamRankListReq) GetDateInt() uint32 {
	if m != nil {
		return m.DateInt
	}
	return 0
}

func (m *GetMultiTypeTeamRankListReq) GetStage() uint32 {
	if m != nil {
		return m.Stage
	}
	return 0
}

func (m *GetMultiTypeTeamRankListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetMultiTypeTeamRankListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetMultiTypeTeamRankListResp struct {
	TeamList       []*MultiTypeTeamRankInfo `protobuf:"bytes,1,rep,name=team_list,json=teamList" json:"team_list,omitempty"`
	IsValidStage   bool                     `protobuf:"varint,2,opt,name=is_valid_stage,json=isValidStage,proto3" json:"is_valid_stage,omitempty"`
	TotalTeamCount uint32                   `protobuf:"varint,3,opt,name=total_team_count,json=totalTeamCount,proto3" json:"total_team_count,omitempty"`
}

func (m *GetMultiTypeTeamRankListResp) Reset()         { *m = GetMultiTypeTeamRankListResp{} }
func (m *GetMultiTypeTeamRankListResp) String() string { return proto.CompactTextString(m) }
func (*GetMultiTypeTeamRankListResp) ProtoMessage()    {}
func (*GetMultiTypeTeamRankListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{32}
}

func (m *GetMultiTypeTeamRankListResp) GetTeamList() []*MultiTypeTeamRankInfo {
	if m != nil {
		return m.TeamList
	}
	return nil
}

func (m *GetMultiTypeTeamRankListResp) GetIsValidStage() bool {
	if m != nil {
		return m.IsValidStage
	}
	return false
}

func (m *GetMultiTypeTeamRankListResp) GetTotalTeamCount() uint32 {
	if m != nil {
		return m.TotalTeamCount
	}
	return 0
}

// 获取多人榜的队伍排行信息
type GetMultiTypeTeamRankInfoReq struct {
	Uid           uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	DateInt       uint32 `protobuf:"varint,2,opt,name=date_int,json=dateInt,proto3" json:"date_int,omitempty"`
	Stage         uint32 `protobuf:"varint,3,opt,name=stage,proto3" json:"stage,omitempty"`
	MultiTeamType uint32 `protobuf:"varint,4,opt,name=multi_team_type,json=multiTeamType,proto3" json:"multi_team_type,omitempty"`
}

func (m *GetMultiTypeTeamRankInfoReq) Reset()         { *m = GetMultiTypeTeamRankInfoReq{} }
func (m *GetMultiTypeTeamRankInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetMultiTypeTeamRankInfoReq) ProtoMessage()    {}
func (*GetMultiTypeTeamRankInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{33}
}

func (m *GetMultiTypeTeamRankInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetMultiTypeTeamRankInfoReq) GetDateInt() uint32 {
	if m != nil {
		return m.DateInt
	}
	return 0
}

func (m *GetMultiTypeTeamRankInfoReq) GetStage() uint32 {
	if m != nil {
		return m.Stage
	}
	return 0
}

func (m *GetMultiTypeTeamRankInfoReq) GetMultiTeamType() uint32 {
	if m != nil {
		return m.MultiTeamType
	}
	return 0
}

type GetMultiTypeTeamRankInfoResp struct {
	TeamInfo     *MultiTypeTeamRankInfo `protobuf:"bytes,1,opt,name=team_info,json=teamInfo" json:"team_info,omitempty"`
	IsSignedUp   bool                   `protobuf:"varint,2,opt,name=is_signed_up,json=isSignedUp,proto3" json:"is_signed_up,omitempty"`
	DiffValue    uint32                 `protobuf:"varint,3,opt,name=diff_value,json=diffValue,proto3" json:"diff_value,omitempty"`
	ToBePromoted bool                   `protobuf:"varint,4,opt,name=to_be_promoted,json=toBePromoted,proto3" json:"to_be_promoted,omitempty"`
}

func (m *GetMultiTypeTeamRankInfoResp) Reset()         { *m = GetMultiTypeTeamRankInfoResp{} }
func (m *GetMultiTypeTeamRankInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetMultiTypeTeamRankInfoResp) ProtoMessage()    {}
func (*GetMultiTypeTeamRankInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{34}
}

func (m *GetMultiTypeTeamRankInfoResp) GetTeamInfo() *MultiTypeTeamRankInfo {
	if m != nil {
		return m.TeamInfo
	}
	return nil
}

func (m *GetMultiTypeTeamRankInfoResp) GetIsSignedUp() bool {
	if m != nil {
		return m.IsSignedUp
	}
	return false
}

func (m *GetMultiTypeTeamRankInfoResp) GetDiffValue() uint32 {
	if m != nil {
		return m.DiffValue
	}
	return 0
}

func (m *GetMultiTypeTeamRankInfoResp) GetToBePromoted() bool {
	if m != nil {
		return m.ToBePromoted
	}
	return false
}

// 神豪榜 榜单
type RichmanRankItem struct {
	Uid          uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Value        uint32 `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
	AddtionValue uint32 `protobuf:"varint,3,opt,name=addtion_value,json=addtionValue,proto3" json:"addtion_value,omitempty"`
}

func (m *RichmanRankItem) Reset()                    { *m = RichmanRankItem{} }
func (m *RichmanRankItem) String() string            { return proto.CompactTextString(m) }
func (*RichmanRankItem) ProtoMessage()               {}
func (*RichmanRankItem) Descriptor() ([]byte, []int) { return fileDescriptorAwardgame2018, []int{35} }

func (m *RichmanRankItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RichmanRankItem) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *RichmanRankItem) GetAddtionValue() uint32 {
	if m != nil {
		return m.AddtionValue
	}
	return 0
}

type RichmanMyRankValue struct {
	Uid              uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Value            uint32 `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
	Rank             uint32 `protobuf:"varint,3,opt,name=rank,proto3" json:"rank,omitempty"`
	IsQualified      bool   `protobuf:"varint,4,opt,name=is_qualified,json=isQualified,proto3" json:"is_qualified,omitempty"`
	UpgradeDifValue  uint32 `protobuf:"varint,5,opt,name=upgrade_dif_value,json=upgradeDifValue,proto3" json:"upgrade_dif_value,omitempty"`
	JoinrankDifValue uint32 `protobuf:"varint,6,opt,name=joinrank_dif_value,json=joinrankDifValue,proto3" json:"joinrank_dif_value,omitempty"`
}

func (m *RichmanMyRankValue) Reset()                    { *m = RichmanMyRankValue{} }
func (m *RichmanMyRankValue) String() string            { return proto.CompactTextString(m) }
func (*RichmanMyRankValue) ProtoMessage()               {}
func (*RichmanMyRankValue) Descriptor() ([]byte, []int) { return fileDescriptorAwardgame2018, []int{36} }

func (m *RichmanMyRankValue) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RichmanMyRankValue) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *RichmanMyRankValue) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *RichmanMyRankValue) GetIsQualified() bool {
	if m != nil {
		return m.IsQualified
	}
	return false
}

func (m *RichmanMyRankValue) GetUpgradeDifValue() uint32 {
	if m != nil {
		return m.UpgradeDifValue
	}
	return 0
}

func (m *RichmanMyRankValue) GetJoinrankDifValue() uint32 {
	if m != nil {
		return m.JoinrankDifValue
	}
	return 0
}

type GetRichmanRankListReq struct {
	Uid          uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Offset       uint32 `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Count        uint32 `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	StageId      uint32 `protobuf:"varint,4,opt,name=stage_id,json=stageId,proto3" json:"stage_id,omitempty"`
	Day          string `protobuf:"bytes,5,opt,name=day,proto3" json:"day,omitempty"`
	IsNeedMyRank bool   `protobuf:"varint,6,opt,name=is_need_my_rank,json=isNeedMyRank,proto3" json:"is_need_my_rank,omitempty"`
}

func (m *GetRichmanRankListReq) Reset()         { *m = GetRichmanRankListReq{} }
func (m *GetRichmanRankListReq) String() string { return proto.CompactTextString(m) }
func (*GetRichmanRankListReq) ProtoMessage()    {}
func (*GetRichmanRankListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{37}
}

func (m *GetRichmanRankListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRichmanRankListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetRichmanRankListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetRichmanRankListReq) GetStageId() uint32 {
	if m != nil {
		return m.StageId
	}
	return 0
}

func (m *GetRichmanRankListReq) GetDay() string {
	if m != nil {
		return m.Day
	}
	return ""
}

func (m *GetRichmanRankListReq) GetIsNeedMyRank() bool {
	if m != nil {
		return m.IsNeedMyRank
	}
	return false
}

type GetRichmanRankListResp struct {
	RankList       []*RichmanRankItem  `protobuf:"bytes,1,rep,name=rank_list,json=rankList" json:"rank_list,omitempty"`
	MyRank         *RichmanMyRankValue `protobuf:"bytes,2,opt,name=my_rank,json=myRank" json:"my_rank,omitempty"`
	TotalRankCount uint32              `protobuf:"varint,3,opt,name=total_rank_count,json=totalRankCount,proto3" json:"total_rank_count,omitempty"`
}

func (m *GetRichmanRankListResp) Reset()         { *m = GetRichmanRankListResp{} }
func (m *GetRichmanRankListResp) String() string { return proto.CompactTextString(m) }
func (*GetRichmanRankListResp) ProtoMessage()    {}
func (*GetRichmanRankListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{38}
}

func (m *GetRichmanRankListResp) GetRankList() []*RichmanRankItem {
	if m != nil {
		return m.RankList
	}
	return nil
}

func (m *GetRichmanRankListResp) GetMyRank() *RichmanMyRankValue {
	if m != nil {
		return m.MyRank
	}
	return nil
}

func (m *GetRichmanRankListResp) GetTotalRankCount() uint32 {
	if m != nil {
		return m.TotalRankCount
	}
	return 0
}

// 个人榜
type SingleRankItem struct {
	Uid          uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Value        uint32 `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
	AddtionValue uint32 `protobuf:"varint,3,opt,name=addtion_value,json=addtionValue,proto3" json:"addtion_value,omitempty"`
}

func (m *SingleRankItem) Reset()                    { *m = SingleRankItem{} }
func (m *SingleRankItem) String() string            { return proto.CompactTextString(m) }
func (*SingleRankItem) ProtoMessage()               {}
func (*SingleRankItem) Descriptor() ([]byte, []int) { return fileDescriptorAwardgame2018, []int{39} }

func (m *SingleRankItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SingleRankItem) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *SingleRankItem) GetAddtionValue() uint32 {
	if m != nil {
		return m.AddtionValue
	}
	return 0
}

type SingleMyRankValue struct {
	Uid              uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SingleSignupType uint32 `protobuf:"varint,2,opt,name=single_signup_type,json=singleSignupType,proto3" json:"single_signup_type,omitempty"`
	Value            uint32 `protobuf:"varint,3,opt,name=value,proto3" json:"value,omitempty"`
	Rank             uint32 `protobuf:"varint,4,opt,name=rank,proto3" json:"rank,omitempty"`
	UpgradeDifValue  uint32 `protobuf:"varint,5,opt,name=upgrade_dif_value,json=upgradeDifValue,proto3" json:"upgrade_dif_value,omitempty"`
	JoinrankDifValue uint32 `protobuf:"varint,6,opt,name=joinrank_dif_value,json=joinrankDifValue,proto3" json:"joinrank_dif_value,omitempty"`
	IsQualified      bool   `protobuf:"varint,7,opt,name=is_qualified,json=isQualified,proto3" json:"is_qualified,omitempty"`
}

func (m *SingleMyRankValue) Reset()                    { *m = SingleMyRankValue{} }
func (m *SingleMyRankValue) String() string            { return proto.CompactTextString(m) }
func (*SingleMyRankValue) ProtoMessage()               {}
func (*SingleMyRankValue) Descriptor() ([]byte, []int) { return fileDescriptorAwardgame2018, []int{40} }

func (m *SingleMyRankValue) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SingleMyRankValue) GetSingleSignupType() uint32 {
	if m != nil {
		return m.SingleSignupType
	}
	return 0
}

func (m *SingleMyRankValue) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *SingleMyRankValue) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *SingleMyRankValue) GetUpgradeDifValue() uint32 {
	if m != nil {
		return m.UpgradeDifValue
	}
	return 0
}

func (m *SingleMyRankValue) GetJoinrankDifValue() uint32 {
	if m != nil {
		return m.JoinrankDifValue
	}
	return 0
}

func (m *SingleMyRankValue) GetIsQualified() bool {
	if m != nil {
		return m.IsQualified
	}
	return false
}

type GetSingleRankListReq struct {
	Uid          uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SingleType   uint32 `protobuf:"varint,2,opt,name=single_type,json=singleType,proto3" json:"single_type,omitempty"`
	Offset       uint32 `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Count        uint32 `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	StageId      uint32 `protobuf:"varint,5,opt,name=stage_id,json=stageId,proto3" json:"stage_id,omitempty"`
	Day          string `protobuf:"bytes,6,opt,name=day,proto3" json:"day,omitempty"`
	IsNeedMyRank bool   `protobuf:"varint,7,opt,name=is_need_my_rank,json=isNeedMyRank,proto3" json:"is_need_my_rank,omitempty"`
}

func (m *GetSingleRankListReq) Reset()         { *m = GetSingleRankListReq{} }
func (m *GetSingleRankListReq) String() string { return proto.CompactTextString(m) }
func (*GetSingleRankListReq) ProtoMessage()    {}
func (*GetSingleRankListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{41}
}

func (m *GetSingleRankListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetSingleRankListReq) GetSingleType() uint32 {
	if m != nil {
		return m.SingleType
	}
	return 0
}

func (m *GetSingleRankListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetSingleRankListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetSingleRankListReq) GetStageId() uint32 {
	if m != nil {
		return m.StageId
	}
	return 0
}

func (m *GetSingleRankListReq) GetDay() string {
	if m != nil {
		return m.Day
	}
	return ""
}

func (m *GetSingleRankListReq) GetIsNeedMyRank() bool {
	if m != nil {
		return m.IsNeedMyRank
	}
	return false
}

type GetSingleRankListResp struct {
	RankList       []*SingleRankItem  `protobuf:"bytes,1,rep,name=rank_list,json=rankList" json:"rank_list,omitempty"`
	MyRank         *SingleMyRankValue `protobuf:"bytes,2,opt,name=my_rank,json=myRank" json:"my_rank,omitempty"`
	TotalRankCount uint32             `protobuf:"varint,3,opt,name=total_rank_count,json=totalRankCount,proto3" json:"total_rank_count,omitempty"`
}

func (m *GetSingleRankListResp) Reset()         { *m = GetSingleRankListResp{} }
func (m *GetSingleRankListResp) String() string { return proto.CompactTextString(m) }
func (*GetSingleRankListResp) ProtoMessage()    {}
func (*GetSingleRankListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{42}
}

func (m *GetSingleRankListResp) GetRankList() []*SingleRankItem {
	if m != nil {
		return m.RankList
	}
	return nil
}

func (m *GetSingleRankListResp) GetMyRank() *SingleMyRankValue {
	if m != nil {
		return m.MyRank
	}
	return nil
}

func (m *GetSingleRankListResp) GetTotalRankCount() uint32 {
	if m != nil {
		return m.TotalRankCount
	}
	return 0
}

// 房间榜
type ChannelRankItem struct {
	Cid          uint32 `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	Value        uint32 `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
	AddtionValue uint32 `protobuf:"varint,3,opt,name=addtion_value,json=addtionValue,proto3" json:"addtion_value,omitempty"`
}

func (m *ChannelRankItem) Reset()                    { *m = ChannelRankItem{} }
func (m *ChannelRankItem) String() string            { return proto.CompactTextString(m) }
func (*ChannelRankItem) ProtoMessage()               {}
func (*ChannelRankItem) Descriptor() ([]byte, []int) { return fileDescriptorAwardgame2018, []int{43} }

func (m *ChannelRankItem) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *ChannelRankItem) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *ChannelRankItem) GetAddtionValue() uint32 {
	if m != nil {
		return m.AddtionValue
	}
	return 0
}

type GetChannelRankListReq struct {
	Uid     uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Offset  uint32 `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Count   uint32 `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	StageId uint32 `protobuf:"varint,4,opt,name=stage_id,json=stageId,proto3" json:"stage_id,omitempty"`
	Day     string `protobuf:"bytes,5,opt,name=day,proto3" json:"day,omitempty"`
}

func (m *GetChannelRankListReq) Reset()         { *m = GetChannelRankListReq{} }
func (m *GetChannelRankListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelRankListReq) ProtoMessage()    {}
func (*GetChannelRankListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{44}
}

func (m *GetChannelRankListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetChannelRankListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetChannelRankListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetChannelRankListReq) GetStageId() uint32 {
	if m != nil {
		return m.StageId
	}
	return 0
}

func (m *GetChannelRankListReq) GetDay() string {
	if m != nil {
		return m.Day
	}
	return ""
}

type GetChannelRankListResp struct {
	RankList       []*ChannelRankItem `protobuf:"bytes,1,rep,name=rank_list,json=rankList" json:"rank_list,omitempty"`
	TotalRankCount uint32             `protobuf:"varint,2,opt,name=total_rank_count,json=totalRankCount,proto3" json:"total_rank_count,omitempty"`
}

func (m *GetChannelRankListResp) Reset()         { *m = GetChannelRankListResp{} }
func (m *GetChannelRankListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelRankListResp) ProtoMessage()    {}
func (*GetChannelRankListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{45}
}

func (m *GetChannelRankListResp) GetRankList() []*ChannelRankItem {
	if m != nil {
		return m.RankList
	}
	return nil
}

func (m *GetChannelRankListResp) GetTotalRankCount() uint32 {
	if m != nil {
		return m.TotalRankCount
	}
	return 0
}

// 战况
type RichmanBattleProgress struct {
	TimeTs uint32 `protobuf:"varint,1,opt,name=time_ts,json=timeTs,proto3" json:"time_ts,omitempty"`
	Msg    string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (m *RichmanBattleProgress) Reset()         { *m = RichmanBattleProgress{} }
func (m *RichmanBattleProgress) String() string { return proto.CompactTextString(m) }
func (*RichmanBattleProgress) ProtoMessage()    {}
func (*RichmanBattleProgress) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{46}
}

func (m *RichmanBattleProgress) GetTimeTs() uint32 {
	if m != nil {
		return m.TimeTs
	}
	return 0
}

func (m *RichmanBattleProgress) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type GetRichmanBattleProgressListReq struct {
}

func (m *GetRichmanBattleProgressListReq) Reset()         { *m = GetRichmanBattleProgressListReq{} }
func (m *GetRichmanBattleProgressListReq) String() string { return proto.CompactTextString(m) }
func (*GetRichmanBattleProgressListReq) ProtoMessage()    {}
func (*GetRichmanBattleProgressListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{47}
}

type GetRichmanBattleProgressListResp struct {
	ProgressList []*RichmanBattleProgress `protobuf:"bytes,1,rep,name=progress_list,json=progressList" json:"progress_list,omitempty"`
}

func (m *GetRichmanBattleProgressListResp) Reset()         { *m = GetRichmanBattleProgressListResp{} }
func (m *GetRichmanBattleProgressListResp) String() string { return proto.CompactTextString(m) }
func (*GetRichmanBattleProgressListResp) ProtoMessage()    {}
func (*GetRichmanBattleProgressListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{48}
}

func (m *GetRichmanBattleProgressListResp) GetProgressList() []*RichmanBattleProgress {
	if m != nil {
		return m.ProgressList
	}
	return nil
}

//
type DoTestReq struct {
	Uid        uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TestType   uint32 `protobuf:"varint,2,opt,name=test_type,json=testType,proto3" json:"test_type,omitempty"`
	ItemId     uint32 `protobuf:"varint,3,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	TargetUid  uint32 `protobuf:"varint,4,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	TotalPrice uint32 `protobuf:"varint,5,opt,name=total_price,json=totalPrice,proto3" json:"total_price,omitempty"`
	SendTime   uint32 `protobuf:"varint,6,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	ChannelId  uint32 `protobuf:"varint,7,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
}

func (m *DoTestReq) Reset()                    { *m = DoTestReq{} }
func (m *DoTestReq) String() string            { return proto.CompactTextString(m) }
func (*DoTestReq) ProtoMessage()               {}
func (*DoTestReq) Descriptor() ([]byte, []int) { return fileDescriptorAwardgame2018, []int{49} }

func (m *DoTestReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DoTestReq) GetTestType() uint32 {
	if m != nil {
		return m.TestType
	}
	return 0
}

func (m *DoTestReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *DoTestReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *DoTestReq) GetTotalPrice() uint32 {
	if m != nil {
		return m.TotalPrice
	}
	return 0
}

func (m *DoTestReq) GetSendTime() uint32 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

func (m *DoTestReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type DoTestResp struct {
	Cnt uint32 `protobuf:"varint,1,opt,name=cnt,proto3" json:"cnt,omitempty"`
}

func (m *DoTestResp) Reset()                    { *m = DoTestResp{} }
func (m *DoTestResp) String() string            { return proto.CompactTextString(m) }
func (*DoTestResp) ProtoMessage()               {}
func (*DoTestResp) Descriptor() ([]byte, []int) { return fileDescriptorAwardgame2018, []int{50} }

func (m *DoTestResp) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

type ShorttimeComsumeInfo struct {
	Uid     uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Price   uint32 `protobuf:"varint,2,opt,name=price,proto3" json:"price,omitempty"`
	Ts      uint32 `protobuf:"varint,3,opt,name=ts,proto3" json:"ts,omitempty"`
	OrderId string `protobuf:"bytes,4,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
}

func (m *ShorttimeComsumeInfo) Reset()         { *m = ShorttimeComsumeInfo{} }
func (m *ShorttimeComsumeInfo) String() string { return proto.CompactTextString(m) }
func (*ShorttimeComsumeInfo) ProtoMessage()    {}
func (*ShorttimeComsumeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardgame2018, []int{51}
}

func (m *ShorttimeComsumeInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ShorttimeComsumeInfo) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *ShorttimeComsumeInfo) GetTs() uint32 {
	if m != nil {
		return m.Ts
	}
	return 0
}

func (m *ShorttimeComsumeInfo) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func init() {
	proto.RegisterType((*SingleTypeSignUpInfo)(nil), "awardgame2018.SingleTypeSignUpInfo")
	proto.RegisterType((*MutiTypeSignUpMember)(nil), "awardgame2018.MutiTypeSignUpMember")
	proto.RegisterType((*MutiTypeTeamInfo)(nil), "awardgame2018.MutiTypeTeamInfo")
	proto.RegisterType((*MutiTypeSignUpInfo)(nil), "awardgame2018.MutiTypeSignUpInfo")
	proto.RegisterType((*RichManSignUpInfo)(nil), "awardgame2018.RichManSignUpInfo")
	proto.RegisterType((*GetUserSignUpInfoReq)(nil), "awardgame2018.GetUserSignUpInfoReq")
	proto.RegisterType((*GetUserSignUpInfoResp)(nil), "awardgame2018.GetUserSignUpInfoResp")
	proto.RegisterType((*GetUserMutiSignUpInfoReq)(nil), "awardgame2018.GetUserMutiSignUpInfoReq")
	proto.RegisterType((*GetUserMutiSignUpInfoResp)(nil), "awardgame2018.GetUserMutiSignUpInfoResp")
	proto.RegisterType((*SetUserSignUpInfoReq)(nil), "awardgame2018.SetUserSignUpInfoReq")
	proto.RegisterType((*SetUserSignUpInfoResp)(nil), "awardgame2018.SetUserSignUpInfoResp")
	proto.RegisterType((*JoinMutiTypeTeamReq)(nil), "awardgame2018.JoinMutiTypeTeamReq")
	proto.RegisterType((*JoinMutiTypeTeamResp)(nil), "awardgame2018.JoinMutiTypeTeamResp")
	proto.RegisterType((*QuitMutiTypeTeamReq)(nil), "awardgame2018.QuitMutiTypeTeamReq")
	proto.RegisterType((*QuitMutiTypeTeamResp)(nil), "awardgame2018.QuitMutiTypeTeamResp")
	proto.RegisterType((*CreateMutiTypeTeamReq)(nil), "awardgame2018.CreateMutiTypeTeamReq")
	proto.RegisterType((*CreateMutiTypeTeamResp)(nil), "awardgame2018.CreateMutiTypeTeamResp")
	proto.RegisterType((*UpdateMutiTypeTeamNotifyReq)(nil), "awardgame2018.UpdateMutiTypeTeamNotifyReq")
	proto.RegisterType((*UpdateMutiTypeTeamNotifyResp)(nil), "awardgame2018.UpdateMutiTypeTeamNotifyResp")
	proto.RegisterType((*GetMutiTypeTeamInfoReq)(nil), "awardgame2018.GetMutiTypeTeamInfoReq")
	proto.RegisterType((*GetMutiTypeTeamInfoResp)(nil), "awardgame2018.GetMutiTypeTeamInfoResp")
	proto.RegisterType((*SingleTypeSignUpCount)(nil), "awardgame2018.SingleTypeSignUpCount")
	proto.RegisterType((*MutiTypeSignUpCount)(nil), "awardgame2018.MutiTypeSignUpCount")
	proto.RegisterType((*GetSignupCountReq)(nil), "awardgame2018.GetSignupCountReq")
	proto.RegisterType((*GetSignupCountResp)(nil), "awardgame2018.GetSignupCountResp")
	proto.RegisterType((*GetCurrStageReq)(nil), "awardgame2018.GetCurrStageReq")
	proto.RegisterType((*GetCurrStageResp)(nil), "awardgame2018.GetCurrStageResp")
	proto.RegisterType((*NotifyDateChangeReq)(nil), "awardgame2018.NotifyDateChangeReq")
	proto.RegisterType((*MultiTypeTeamMemberRankInfo)(nil), "awardgame2018.MultiTypeTeamMemberRankInfo")
	proto.RegisterType((*MultiTypeTeamRankBrief)(nil), "awardgame2018.MultiTypeTeamRankBrief")
	proto.RegisterType((*MultiTypeTeamRankInfo)(nil), "awardgame2018.MultiTypeTeamRankInfo")
	proto.RegisterType((*GetMultiTypeTeamRankListReq)(nil), "awardgame2018.GetMultiTypeTeamRankListReq")
	proto.RegisterType((*GetMultiTypeTeamRankListResp)(nil), "awardgame2018.GetMultiTypeTeamRankListResp")
	proto.RegisterType((*GetMultiTypeTeamRankInfoReq)(nil), "awardgame2018.GetMultiTypeTeamRankInfoReq")
	proto.RegisterType((*GetMultiTypeTeamRankInfoResp)(nil), "awardgame2018.GetMultiTypeTeamRankInfoResp")
	proto.RegisterType((*RichmanRankItem)(nil), "awardgame2018.RichmanRankItem")
	proto.RegisterType((*RichmanMyRankValue)(nil), "awardgame2018.RichmanMyRankValue")
	proto.RegisterType((*GetRichmanRankListReq)(nil), "awardgame2018.GetRichmanRankListReq")
	proto.RegisterType((*GetRichmanRankListResp)(nil), "awardgame2018.GetRichmanRankListResp")
	proto.RegisterType((*SingleRankItem)(nil), "awardgame2018.SingleRankItem")
	proto.RegisterType((*SingleMyRankValue)(nil), "awardgame2018.SingleMyRankValue")
	proto.RegisterType((*GetSingleRankListReq)(nil), "awardgame2018.GetSingleRankListReq")
	proto.RegisterType((*GetSingleRankListResp)(nil), "awardgame2018.GetSingleRankListResp")
	proto.RegisterType((*ChannelRankItem)(nil), "awardgame2018.ChannelRankItem")
	proto.RegisterType((*GetChannelRankListReq)(nil), "awardgame2018.GetChannelRankListReq")
	proto.RegisterType((*GetChannelRankListResp)(nil), "awardgame2018.GetChannelRankListResp")
	proto.RegisterType((*RichmanBattleProgress)(nil), "awardgame2018.RichmanBattleProgress")
	proto.RegisterType((*GetRichmanBattleProgressListReq)(nil), "awardgame2018.GetRichmanBattleProgressListReq")
	proto.RegisterType((*GetRichmanBattleProgressListResp)(nil), "awardgame2018.GetRichmanBattleProgressListResp")
	proto.RegisterType((*DoTestReq)(nil), "awardgame2018.DoTestReq")
	proto.RegisterType((*DoTestResp)(nil), "awardgame2018.DoTestResp")
	proto.RegisterType((*ShorttimeComsumeInfo)(nil), "awardgame2018.ShorttimeComsumeInfo")
	proto.RegisterEnum("awardgame2018.EStageType", EStageType_name, EStageType_value)
	proto.RegisterEnum("awardgame2018.ESingleSignUpType", ESingleSignUpType_name, ESingleSignUpType_value)
	proto.RegisterEnum("awardgame2018.EMutiSignUpType", EMutiSignUpType_name, EMutiSignUpType_value)
	proto.RegisterEnum("awardgame2018.EMutiSignUpQuitReasonType", EMutiSignUpQuitReasonType_name, EMutiSignUpQuitReasonType_value)
}
func (m *SingleTypeSignUpInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SingleTypeSignUpInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Uid))
	}
	if m.IsPayTicket {
		dAtA[i] = 0x10
		i++
		if m.IsPayTicket {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	if m.SingleType != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.SingleType))
	}
	if m.JoinTs != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.JoinTs))
	}
	return i, nil
}

func (m *MutiTypeSignUpMember) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MutiTypeSignUpMember) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Uid))
	}
	if m.JoinTs != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.JoinTs))
	}
	if m.IsPayTicket {
		dAtA[i] = 0x18
		i++
		if m.IsPayTicket {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	if m.IsCreator {
		dAtA[i] = 0x20
		i++
		if m.IsCreator {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	if m.MutiType != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.MutiType))
	}
	return i, nil
}

func (m *MutiTypeTeamInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MutiTypeTeamInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.MutiTeamId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.MutiTeamId))
	}
	if m.MutiTeamType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.MutiTeamType))
	}
	if len(m.MemberFullList) > 0 {
		for _, msg := range m.MemberFullList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintAwardgame2018(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.CreaterUid != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.CreaterUid))
	}
	return i, nil
}

func (m *MutiTypeSignUpInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MutiTypeSignUpInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.MutiType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.MutiType))
	}
	if m.MutiTeamId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.MutiTeamId))
	}
	if m.MutiTeamType != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.MutiTeamType))
	}
	if m.JoinTs != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.JoinTs))
	}
	if m.IsPayTicket {
		dAtA[i] = 0x28
		i++
		if m.IsPayTicket {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	if m.IsCreator {
		dAtA[i] = 0x30
		i++
		if m.IsCreator {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	if len(m.MemberFullList) > 0 {
		for _, msg := range m.MemberFullList {
			dAtA[i] = 0x3a
			i++
			i = encodeVarintAwardgame2018(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.LastTeamQuitReason != 0 {
		dAtA[i] = 0x50
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.LastTeamQuitReason))
	}
	if m.LastTeamQuitId != 0 {
		dAtA[i] = 0x58
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.LastTeamQuitId))
	}
	if m.LastTeamQuitOpUid != 0 {
		dAtA[i] = 0x60
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.LastTeamQuitOpUid))
	}
	if m.IsNeedNotifyTeamQuit {
		dAtA[i] = 0x68
		i++
		if m.IsNeedNotifyTeamQuit {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	if m.LastTeamQuitType != 0 {
		dAtA[i] = 0x70
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.LastTeamQuitType))
	}
	return i, nil
}

func (m *RichManSignUpInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RichManSignUpInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Uid))
	}
	if m.IsPayTicket {
		dAtA[i] = 0x10
		i++
		if m.IsPayTicket {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	if m.JoinTs != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.JoinTs))
	}
	return i, nil
}

func (m *GetUserSignUpInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserSignUpInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Uid))
	}
	return i, nil
}

func (m *GetUserSignUpInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserSignUpInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.SingleSignup != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.SingleSignup.Size()))
		n1, err := m.SingleSignup.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	if m.MutiSignup != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.MutiSignup.Size()))
		n2, err := m.MutiSignup.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	if m.RichmanSignup != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.RichmanSignup.Size()))
		n3, err := m.RichmanSignup.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	return i, nil
}

func (m *GetUserMutiSignUpInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserMutiSignUpInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Uid))
	}
	return i, nil
}

func (m *GetUserMutiSignUpInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserMutiSignUpInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.MutiSignup != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.MutiSignup.Size()))
		n4, err := m.MutiSignup.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	return i, nil
}

func (m *SetUserSignUpInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetUserSignUpInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Uid))
	}
	if m.IsPayTicket {
		dAtA[i] = 0x10
		i++
		if m.IsPayTicket {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	if m.SingleSignupType != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.SingleSignupType))
	}
	if m.MutiSignupType != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.MutiSignupType))
	}
	if m.IsSignupRichman {
		dAtA[i] = 0x28
		i++
		if m.IsSignupRichman {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	return i, nil
}

func (m *SetUserSignUpInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetUserSignUpInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.SingleSignup != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.SingleSignup.Size()))
		n5, err := m.SingleSignup.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	if m.MutiSignup != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.MutiSignup.Size()))
		n6, err := m.MutiSignup.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	if m.RichmanSignup != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.RichmanSignup.Size()))
		n7, err := m.RichmanSignup.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	return i, nil
}

func (m *JoinMutiTypeTeamReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *JoinMutiTypeTeamReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Uid))
	}
	if m.MutiTeamId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.MutiTeamId))
	}
	return i, nil
}

func (m *JoinMutiTypeTeamResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *JoinMutiTypeTeamResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *QuitMutiTypeTeamReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QuitMutiTypeTeamReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.OpUid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.OpUid))
	}
	if m.TargetUid != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.TargetUid))
	}
	if m.ReasonType != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.ReasonType))
	}
	if m.MutiTeamId != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.MutiTeamId))
	}
	return i, nil
}

func (m *QuitMutiTypeTeamResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QuitMutiTypeTeamResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.TargetuserNewTeamId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.TargetuserNewTeamId))
	}
	if m.TargetuserMutiType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.TargetuserMutiType))
	}
	return i, nil
}

func (m *CreateMutiTypeTeamReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateMutiTypeTeamReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.OpUid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.OpUid))
	}
	return i, nil
}

func (m *CreateMutiTypeTeamResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateMutiTypeTeamResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.MutiTeamId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.MutiTeamId))
	}
	return i, nil
}

func (m *UpdateMutiTypeTeamNotifyReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateMutiTypeTeamNotifyReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.OpUid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.OpUid))
	}
	if m.IsOpen {
		dAtA[i] = 0x10
		i++
		if m.IsOpen {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	return i, nil
}

func (m *UpdateMutiTypeTeamNotifyResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateMutiTypeTeamNotifyResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetMutiTypeTeamInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMutiTypeTeamInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.TeamId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.TeamId))
	}
	return i, nil
}

func (m *GetMutiTypeTeamInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMutiTypeTeamInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.MutiInfo != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.MutiInfo.Size()))
		n8, err := m.MutiInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	return i, nil
}

func (m *SingleTypeSignUpCount) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SingleTypeSignUpCount) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.SingleType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.SingleType))
	}
	if m.Count != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Count))
	}
	return i, nil
}

func (m *MutiTypeSignUpCount) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MutiTypeSignUpCount) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.MutiType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.MutiType))
	}
	if m.Count != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Count))
	}
	return i, nil
}

func (m *GetSignupCountReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSignupCountReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetSignupCountResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSignupCountResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.SingleSignupCntList) > 0 {
		for _, msg := range m.SingleSignupCntList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintAwardgame2018(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.MutiSignupCntList) > 0 {
		for _, msg := range m.MutiSignupCntList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintAwardgame2018(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.RichmanSignupCnt != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.RichmanSignupCnt))
	}
	return i, nil
}

func (m *GetCurrStageReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCurrStageReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetCurrStageResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCurrStageResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.CurrStage != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.CurrStage))
	}
	if m.CurrStageBeginTs != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.CurrStageBeginTs))
	}
	if m.NextStage != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.NextStage))
	}
	if m.NextStageBeginTs != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.NextStageBeginTs))
	}
	return i, nil
}

func (m *NotifyDateChangeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NotifyDateChangeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.DateInt != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.DateInt))
	}
	return i, nil
}

func (m *MultiTypeTeamMemberRankInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MultiTypeTeamMemberRankInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.TeamId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.TeamId))
	}
	if m.Uid != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Uid))
	}
	if m.RankValue != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.RankValue))
	}
	if m.IsCreator {
		dAtA[i] = 0x20
		i++
		if m.IsCreator {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	return i, nil
}

func (m *MultiTypeTeamRankBrief) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MultiTypeTeamRankBrief) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.TeamId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.TeamId))
	}
	if m.ListType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.ListType))
	}
	if m.RankValue != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.RankValue))
	}
	if m.Ranking != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Ranking))
	}
	if m.BonusValue != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.BonusValue))
	}
	return i, nil
}

func (m *MultiTypeTeamRankInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MultiTypeTeamRankInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BriefInfo != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.BriefInfo.Size()))
		n9, err := m.BriefInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	if len(m.MemberList) > 0 {
		for _, msg := range m.MemberList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintAwardgame2018(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetMultiTypeTeamRankListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMultiTypeTeamRankListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.MultiTeamType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.MultiTeamType))
	}
	if m.DateInt != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.DateInt))
	}
	if m.Stage != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Stage))
	}
	if m.Offset != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Offset))
	}
	if m.Count != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Count))
	}
	return i, nil
}

func (m *GetMultiTypeTeamRankListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMultiTypeTeamRankListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.TeamList) > 0 {
		for _, msg := range m.TeamList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintAwardgame2018(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.IsValidStage {
		dAtA[i] = 0x10
		i++
		if m.IsValidStage {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	if m.TotalTeamCount != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.TotalTeamCount))
	}
	return i, nil
}

func (m *GetMultiTypeTeamRankInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMultiTypeTeamRankInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Uid))
	}
	if m.DateInt != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.DateInt))
	}
	if m.Stage != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Stage))
	}
	if m.MultiTeamType != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.MultiTeamType))
	}
	return i, nil
}

func (m *GetMultiTypeTeamRankInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMultiTypeTeamRankInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.TeamInfo != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.TeamInfo.Size()))
		n10, err := m.TeamInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	if m.IsSignedUp {
		dAtA[i] = 0x10
		i++
		if m.IsSignedUp {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	if m.DiffValue != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.DiffValue))
	}
	if m.ToBePromoted {
		dAtA[i] = 0x20
		i++
		if m.ToBePromoted {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	return i, nil
}

func (m *RichmanRankItem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RichmanRankItem) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Uid))
	}
	if m.Value != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Value))
	}
	if m.AddtionValue != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.AddtionValue))
	}
	return i, nil
}

func (m *RichmanMyRankValue) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RichmanMyRankValue) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Uid))
	}
	if m.Value != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Value))
	}
	if m.Rank != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Rank))
	}
	if m.IsQualified {
		dAtA[i] = 0x20
		i++
		if m.IsQualified {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	if m.UpgradeDifValue != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.UpgradeDifValue))
	}
	if m.JoinrankDifValue != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.JoinrankDifValue))
	}
	return i, nil
}

func (m *GetRichmanRankListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRichmanRankListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Uid))
	}
	if m.Offset != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Offset))
	}
	if m.Count != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Count))
	}
	if m.StageId != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.StageId))
	}
	if len(m.Day) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(len(m.Day)))
		i += copy(dAtA[i:], m.Day)
	}
	if m.IsNeedMyRank {
		dAtA[i] = 0x30
		i++
		if m.IsNeedMyRank {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	return i, nil
}

func (m *GetRichmanRankListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRichmanRankListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.RankList) > 0 {
		for _, msg := range m.RankList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintAwardgame2018(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.MyRank != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.MyRank.Size()))
		n11, err := m.MyRank.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	if m.TotalRankCount != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.TotalRankCount))
	}
	return i, nil
}

func (m *SingleRankItem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SingleRankItem) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Uid))
	}
	if m.Value != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Value))
	}
	if m.AddtionValue != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.AddtionValue))
	}
	return i, nil
}

func (m *SingleMyRankValue) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SingleMyRankValue) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Uid))
	}
	if m.SingleSignupType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.SingleSignupType))
	}
	if m.Value != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Value))
	}
	if m.Rank != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Rank))
	}
	if m.UpgradeDifValue != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.UpgradeDifValue))
	}
	if m.JoinrankDifValue != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.JoinrankDifValue))
	}
	if m.IsQualified {
		dAtA[i] = 0x38
		i++
		if m.IsQualified {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	return i, nil
}

func (m *GetSingleRankListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSingleRankListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Uid))
	}
	if m.SingleType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.SingleType))
	}
	if m.Offset != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Offset))
	}
	if m.Count != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Count))
	}
	if m.StageId != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.StageId))
	}
	if len(m.Day) > 0 {
		dAtA[i] = 0x32
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(len(m.Day)))
		i += copy(dAtA[i:], m.Day)
	}
	if m.IsNeedMyRank {
		dAtA[i] = 0x38
		i++
		if m.IsNeedMyRank {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	return i, nil
}

func (m *GetSingleRankListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSingleRankListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.RankList) > 0 {
		for _, msg := range m.RankList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintAwardgame2018(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.MyRank != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.MyRank.Size()))
		n12, err := m.MyRank.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n12
	}
	if m.TotalRankCount != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.TotalRankCount))
	}
	return i, nil
}

func (m *ChannelRankItem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelRankItem) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Cid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Cid))
	}
	if m.Value != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Value))
	}
	if m.AddtionValue != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.AddtionValue))
	}
	return i, nil
}

func (m *GetChannelRankListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelRankListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Uid))
	}
	if m.Offset != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Offset))
	}
	if m.Count != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Count))
	}
	if m.StageId != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.StageId))
	}
	if len(m.Day) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(len(m.Day)))
		i += copy(dAtA[i:], m.Day)
	}
	return i, nil
}

func (m *GetChannelRankListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelRankListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.RankList) > 0 {
		for _, msg := range m.RankList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintAwardgame2018(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.TotalRankCount != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.TotalRankCount))
	}
	return i, nil
}

func (m *RichmanBattleProgress) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RichmanBattleProgress) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.TimeTs != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.TimeTs))
	}
	if len(m.Msg) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(len(m.Msg)))
		i += copy(dAtA[i:], m.Msg)
	}
	return i, nil
}

func (m *GetRichmanBattleProgressListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRichmanBattleProgressListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetRichmanBattleProgressListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRichmanBattleProgressListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ProgressList) > 0 {
		for _, msg := range m.ProgressList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintAwardgame2018(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *DoTestReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DoTestReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Uid))
	}
	if m.TestType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.TestType))
	}
	if m.ItemId != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.ItemId))
	}
	if m.TargetUid != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.TargetUid))
	}
	if m.TotalPrice != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.TotalPrice))
	}
	if m.SendTime != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.SendTime))
	}
	if m.ChannelId != 0 {
		dAtA[i] = 0x38
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.ChannelId))
	}
	return i, nil
}

func (m *DoTestResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DoTestResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Cnt != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Cnt))
	}
	return i, nil
}

func (m *ShorttimeComsumeInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ShorttimeComsumeInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Uid))
	}
	if m.Price != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Price))
	}
	if m.Ts != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(m.Ts))
	}
	if len(m.OrderId) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintAwardgame2018(dAtA, i, uint64(len(m.OrderId)))
		i += copy(dAtA[i:], m.OrderId)
	}
	return i, nil
}

func encodeFixed64Awardgame2018(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Awardgame2018(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintAwardgame2018(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *SingleTypeSignUpInfo) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Uid))
	}
	if m.IsPayTicket {
		n += 2
	}
	if m.SingleType != 0 {
		n += 1 + sovAwardgame2018(uint64(m.SingleType))
	}
	if m.JoinTs != 0 {
		n += 1 + sovAwardgame2018(uint64(m.JoinTs))
	}
	return n
}

func (m *MutiTypeSignUpMember) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Uid))
	}
	if m.JoinTs != 0 {
		n += 1 + sovAwardgame2018(uint64(m.JoinTs))
	}
	if m.IsPayTicket {
		n += 2
	}
	if m.IsCreator {
		n += 2
	}
	if m.MutiType != 0 {
		n += 1 + sovAwardgame2018(uint64(m.MutiType))
	}
	return n
}

func (m *MutiTypeTeamInfo) Size() (n int) {
	var l int
	_ = l
	if m.MutiTeamId != 0 {
		n += 1 + sovAwardgame2018(uint64(m.MutiTeamId))
	}
	if m.MutiTeamType != 0 {
		n += 1 + sovAwardgame2018(uint64(m.MutiTeamType))
	}
	if len(m.MemberFullList) > 0 {
		for _, e := range m.MemberFullList {
			l = e.Size()
			n += 1 + l + sovAwardgame2018(uint64(l))
		}
	}
	if m.CreaterUid != 0 {
		n += 1 + sovAwardgame2018(uint64(m.CreaterUid))
	}
	return n
}

func (m *MutiTypeSignUpInfo) Size() (n int) {
	var l int
	_ = l
	if m.MutiType != 0 {
		n += 1 + sovAwardgame2018(uint64(m.MutiType))
	}
	if m.MutiTeamId != 0 {
		n += 1 + sovAwardgame2018(uint64(m.MutiTeamId))
	}
	if m.MutiTeamType != 0 {
		n += 1 + sovAwardgame2018(uint64(m.MutiTeamType))
	}
	if m.JoinTs != 0 {
		n += 1 + sovAwardgame2018(uint64(m.JoinTs))
	}
	if m.IsPayTicket {
		n += 2
	}
	if m.IsCreator {
		n += 2
	}
	if len(m.MemberFullList) > 0 {
		for _, e := range m.MemberFullList {
			l = e.Size()
			n += 1 + l + sovAwardgame2018(uint64(l))
		}
	}
	if m.LastTeamQuitReason != 0 {
		n += 1 + sovAwardgame2018(uint64(m.LastTeamQuitReason))
	}
	if m.LastTeamQuitId != 0 {
		n += 1 + sovAwardgame2018(uint64(m.LastTeamQuitId))
	}
	if m.LastTeamQuitOpUid != 0 {
		n += 1 + sovAwardgame2018(uint64(m.LastTeamQuitOpUid))
	}
	if m.IsNeedNotifyTeamQuit {
		n += 2
	}
	if m.LastTeamQuitType != 0 {
		n += 1 + sovAwardgame2018(uint64(m.LastTeamQuitType))
	}
	return n
}

func (m *RichManSignUpInfo) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Uid))
	}
	if m.IsPayTicket {
		n += 2
	}
	if m.JoinTs != 0 {
		n += 1 + sovAwardgame2018(uint64(m.JoinTs))
	}
	return n
}

func (m *GetUserSignUpInfoReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Uid))
	}
	return n
}

func (m *GetUserSignUpInfoResp) Size() (n int) {
	var l int
	_ = l
	if m.SingleSignup != nil {
		l = m.SingleSignup.Size()
		n += 1 + l + sovAwardgame2018(uint64(l))
	}
	if m.MutiSignup != nil {
		l = m.MutiSignup.Size()
		n += 1 + l + sovAwardgame2018(uint64(l))
	}
	if m.RichmanSignup != nil {
		l = m.RichmanSignup.Size()
		n += 1 + l + sovAwardgame2018(uint64(l))
	}
	return n
}

func (m *GetUserMutiSignUpInfoReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Uid))
	}
	return n
}

func (m *GetUserMutiSignUpInfoResp) Size() (n int) {
	var l int
	_ = l
	if m.MutiSignup != nil {
		l = m.MutiSignup.Size()
		n += 1 + l + sovAwardgame2018(uint64(l))
	}
	return n
}

func (m *SetUserSignUpInfoReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Uid))
	}
	if m.IsPayTicket {
		n += 2
	}
	if m.SingleSignupType != 0 {
		n += 1 + sovAwardgame2018(uint64(m.SingleSignupType))
	}
	if m.MutiSignupType != 0 {
		n += 1 + sovAwardgame2018(uint64(m.MutiSignupType))
	}
	if m.IsSignupRichman {
		n += 2
	}
	return n
}

func (m *SetUserSignUpInfoResp) Size() (n int) {
	var l int
	_ = l
	if m.SingleSignup != nil {
		l = m.SingleSignup.Size()
		n += 1 + l + sovAwardgame2018(uint64(l))
	}
	if m.MutiSignup != nil {
		l = m.MutiSignup.Size()
		n += 1 + l + sovAwardgame2018(uint64(l))
	}
	if m.RichmanSignup != nil {
		l = m.RichmanSignup.Size()
		n += 1 + l + sovAwardgame2018(uint64(l))
	}
	return n
}

func (m *JoinMutiTypeTeamReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Uid))
	}
	if m.MutiTeamId != 0 {
		n += 1 + sovAwardgame2018(uint64(m.MutiTeamId))
	}
	return n
}

func (m *JoinMutiTypeTeamResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *QuitMutiTypeTeamReq) Size() (n int) {
	var l int
	_ = l
	if m.OpUid != 0 {
		n += 1 + sovAwardgame2018(uint64(m.OpUid))
	}
	if m.TargetUid != 0 {
		n += 1 + sovAwardgame2018(uint64(m.TargetUid))
	}
	if m.ReasonType != 0 {
		n += 1 + sovAwardgame2018(uint64(m.ReasonType))
	}
	if m.MutiTeamId != 0 {
		n += 1 + sovAwardgame2018(uint64(m.MutiTeamId))
	}
	return n
}

func (m *QuitMutiTypeTeamResp) Size() (n int) {
	var l int
	_ = l
	if m.TargetuserNewTeamId != 0 {
		n += 1 + sovAwardgame2018(uint64(m.TargetuserNewTeamId))
	}
	if m.TargetuserMutiType != 0 {
		n += 1 + sovAwardgame2018(uint64(m.TargetuserMutiType))
	}
	return n
}

func (m *CreateMutiTypeTeamReq) Size() (n int) {
	var l int
	_ = l
	if m.OpUid != 0 {
		n += 1 + sovAwardgame2018(uint64(m.OpUid))
	}
	return n
}

func (m *CreateMutiTypeTeamResp) Size() (n int) {
	var l int
	_ = l
	if m.MutiTeamId != 0 {
		n += 1 + sovAwardgame2018(uint64(m.MutiTeamId))
	}
	return n
}

func (m *UpdateMutiTypeTeamNotifyReq) Size() (n int) {
	var l int
	_ = l
	if m.OpUid != 0 {
		n += 1 + sovAwardgame2018(uint64(m.OpUid))
	}
	if m.IsOpen {
		n += 2
	}
	return n
}

func (m *UpdateMutiTypeTeamNotifyResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetMutiTypeTeamInfoReq) Size() (n int) {
	var l int
	_ = l
	if m.TeamId != 0 {
		n += 1 + sovAwardgame2018(uint64(m.TeamId))
	}
	return n
}

func (m *GetMutiTypeTeamInfoResp) Size() (n int) {
	var l int
	_ = l
	if m.MutiInfo != nil {
		l = m.MutiInfo.Size()
		n += 1 + l + sovAwardgame2018(uint64(l))
	}
	return n
}

func (m *SingleTypeSignUpCount) Size() (n int) {
	var l int
	_ = l
	if m.SingleType != 0 {
		n += 1 + sovAwardgame2018(uint64(m.SingleType))
	}
	if m.Count != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Count))
	}
	return n
}

func (m *MutiTypeSignUpCount) Size() (n int) {
	var l int
	_ = l
	if m.MutiType != 0 {
		n += 1 + sovAwardgame2018(uint64(m.MutiType))
	}
	if m.Count != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Count))
	}
	return n
}

func (m *GetSignupCountReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetSignupCountResp) Size() (n int) {
	var l int
	_ = l
	if len(m.SingleSignupCntList) > 0 {
		for _, e := range m.SingleSignupCntList {
			l = e.Size()
			n += 1 + l + sovAwardgame2018(uint64(l))
		}
	}
	if len(m.MutiSignupCntList) > 0 {
		for _, e := range m.MutiSignupCntList {
			l = e.Size()
			n += 1 + l + sovAwardgame2018(uint64(l))
		}
	}
	if m.RichmanSignupCnt != 0 {
		n += 1 + sovAwardgame2018(uint64(m.RichmanSignupCnt))
	}
	return n
}

func (m *GetCurrStageReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetCurrStageResp) Size() (n int) {
	var l int
	_ = l
	if m.CurrStage != 0 {
		n += 1 + sovAwardgame2018(uint64(m.CurrStage))
	}
	if m.CurrStageBeginTs != 0 {
		n += 1 + sovAwardgame2018(uint64(m.CurrStageBeginTs))
	}
	if m.NextStage != 0 {
		n += 1 + sovAwardgame2018(uint64(m.NextStage))
	}
	if m.NextStageBeginTs != 0 {
		n += 1 + sovAwardgame2018(uint64(m.NextStageBeginTs))
	}
	return n
}

func (m *NotifyDateChangeReq) Size() (n int) {
	var l int
	_ = l
	if m.DateInt != 0 {
		n += 1 + sovAwardgame2018(uint64(m.DateInt))
	}
	return n
}

func (m *MultiTypeTeamMemberRankInfo) Size() (n int) {
	var l int
	_ = l
	if m.TeamId != 0 {
		n += 1 + sovAwardgame2018(uint64(m.TeamId))
	}
	if m.Uid != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Uid))
	}
	if m.RankValue != 0 {
		n += 1 + sovAwardgame2018(uint64(m.RankValue))
	}
	if m.IsCreator {
		n += 2
	}
	return n
}

func (m *MultiTypeTeamRankBrief) Size() (n int) {
	var l int
	_ = l
	if m.TeamId != 0 {
		n += 1 + sovAwardgame2018(uint64(m.TeamId))
	}
	if m.ListType != 0 {
		n += 1 + sovAwardgame2018(uint64(m.ListType))
	}
	if m.RankValue != 0 {
		n += 1 + sovAwardgame2018(uint64(m.RankValue))
	}
	if m.Ranking != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Ranking))
	}
	if m.BonusValue != 0 {
		n += 1 + sovAwardgame2018(uint64(m.BonusValue))
	}
	return n
}

func (m *MultiTypeTeamRankInfo) Size() (n int) {
	var l int
	_ = l
	if m.BriefInfo != nil {
		l = m.BriefInfo.Size()
		n += 1 + l + sovAwardgame2018(uint64(l))
	}
	if len(m.MemberList) > 0 {
		for _, e := range m.MemberList {
			l = e.Size()
			n += 1 + l + sovAwardgame2018(uint64(l))
		}
	}
	return n
}

func (m *GetMultiTypeTeamRankListReq) Size() (n int) {
	var l int
	_ = l
	if m.MultiTeamType != 0 {
		n += 1 + sovAwardgame2018(uint64(m.MultiTeamType))
	}
	if m.DateInt != 0 {
		n += 1 + sovAwardgame2018(uint64(m.DateInt))
	}
	if m.Stage != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Stage))
	}
	if m.Offset != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Offset))
	}
	if m.Count != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Count))
	}
	return n
}

func (m *GetMultiTypeTeamRankListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.TeamList) > 0 {
		for _, e := range m.TeamList {
			l = e.Size()
			n += 1 + l + sovAwardgame2018(uint64(l))
		}
	}
	if m.IsValidStage {
		n += 2
	}
	if m.TotalTeamCount != 0 {
		n += 1 + sovAwardgame2018(uint64(m.TotalTeamCount))
	}
	return n
}

func (m *GetMultiTypeTeamRankInfoReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Uid))
	}
	if m.DateInt != 0 {
		n += 1 + sovAwardgame2018(uint64(m.DateInt))
	}
	if m.Stage != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Stage))
	}
	if m.MultiTeamType != 0 {
		n += 1 + sovAwardgame2018(uint64(m.MultiTeamType))
	}
	return n
}

func (m *GetMultiTypeTeamRankInfoResp) Size() (n int) {
	var l int
	_ = l
	if m.TeamInfo != nil {
		l = m.TeamInfo.Size()
		n += 1 + l + sovAwardgame2018(uint64(l))
	}
	if m.IsSignedUp {
		n += 2
	}
	if m.DiffValue != 0 {
		n += 1 + sovAwardgame2018(uint64(m.DiffValue))
	}
	if m.ToBePromoted {
		n += 2
	}
	return n
}

func (m *RichmanRankItem) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Uid))
	}
	if m.Value != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Value))
	}
	if m.AddtionValue != 0 {
		n += 1 + sovAwardgame2018(uint64(m.AddtionValue))
	}
	return n
}

func (m *RichmanMyRankValue) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Uid))
	}
	if m.Value != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Value))
	}
	if m.Rank != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Rank))
	}
	if m.IsQualified {
		n += 2
	}
	if m.UpgradeDifValue != 0 {
		n += 1 + sovAwardgame2018(uint64(m.UpgradeDifValue))
	}
	if m.JoinrankDifValue != 0 {
		n += 1 + sovAwardgame2018(uint64(m.JoinrankDifValue))
	}
	return n
}

func (m *GetRichmanRankListReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Uid))
	}
	if m.Offset != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Offset))
	}
	if m.Count != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Count))
	}
	if m.StageId != 0 {
		n += 1 + sovAwardgame2018(uint64(m.StageId))
	}
	l = len(m.Day)
	if l > 0 {
		n += 1 + l + sovAwardgame2018(uint64(l))
	}
	if m.IsNeedMyRank {
		n += 2
	}
	return n
}

func (m *GetRichmanRankListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.RankList) > 0 {
		for _, e := range m.RankList {
			l = e.Size()
			n += 1 + l + sovAwardgame2018(uint64(l))
		}
	}
	if m.MyRank != nil {
		l = m.MyRank.Size()
		n += 1 + l + sovAwardgame2018(uint64(l))
	}
	if m.TotalRankCount != 0 {
		n += 1 + sovAwardgame2018(uint64(m.TotalRankCount))
	}
	return n
}

func (m *SingleRankItem) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Uid))
	}
	if m.Value != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Value))
	}
	if m.AddtionValue != 0 {
		n += 1 + sovAwardgame2018(uint64(m.AddtionValue))
	}
	return n
}

func (m *SingleMyRankValue) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Uid))
	}
	if m.SingleSignupType != 0 {
		n += 1 + sovAwardgame2018(uint64(m.SingleSignupType))
	}
	if m.Value != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Value))
	}
	if m.Rank != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Rank))
	}
	if m.UpgradeDifValue != 0 {
		n += 1 + sovAwardgame2018(uint64(m.UpgradeDifValue))
	}
	if m.JoinrankDifValue != 0 {
		n += 1 + sovAwardgame2018(uint64(m.JoinrankDifValue))
	}
	if m.IsQualified {
		n += 2
	}
	return n
}

func (m *GetSingleRankListReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Uid))
	}
	if m.SingleType != 0 {
		n += 1 + sovAwardgame2018(uint64(m.SingleType))
	}
	if m.Offset != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Offset))
	}
	if m.Count != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Count))
	}
	if m.StageId != 0 {
		n += 1 + sovAwardgame2018(uint64(m.StageId))
	}
	l = len(m.Day)
	if l > 0 {
		n += 1 + l + sovAwardgame2018(uint64(l))
	}
	if m.IsNeedMyRank {
		n += 2
	}
	return n
}

func (m *GetSingleRankListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.RankList) > 0 {
		for _, e := range m.RankList {
			l = e.Size()
			n += 1 + l + sovAwardgame2018(uint64(l))
		}
	}
	if m.MyRank != nil {
		l = m.MyRank.Size()
		n += 1 + l + sovAwardgame2018(uint64(l))
	}
	if m.TotalRankCount != 0 {
		n += 1 + sovAwardgame2018(uint64(m.TotalRankCount))
	}
	return n
}

func (m *ChannelRankItem) Size() (n int) {
	var l int
	_ = l
	if m.Cid != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Cid))
	}
	if m.Value != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Value))
	}
	if m.AddtionValue != 0 {
		n += 1 + sovAwardgame2018(uint64(m.AddtionValue))
	}
	return n
}

func (m *GetChannelRankListReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Uid))
	}
	if m.Offset != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Offset))
	}
	if m.Count != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Count))
	}
	if m.StageId != 0 {
		n += 1 + sovAwardgame2018(uint64(m.StageId))
	}
	l = len(m.Day)
	if l > 0 {
		n += 1 + l + sovAwardgame2018(uint64(l))
	}
	return n
}

func (m *GetChannelRankListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.RankList) > 0 {
		for _, e := range m.RankList {
			l = e.Size()
			n += 1 + l + sovAwardgame2018(uint64(l))
		}
	}
	if m.TotalRankCount != 0 {
		n += 1 + sovAwardgame2018(uint64(m.TotalRankCount))
	}
	return n
}

func (m *RichmanBattleProgress) Size() (n int) {
	var l int
	_ = l
	if m.TimeTs != 0 {
		n += 1 + sovAwardgame2018(uint64(m.TimeTs))
	}
	l = len(m.Msg)
	if l > 0 {
		n += 1 + l + sovAwardgame2018(uint64(l))
	}
	return n
}

func (m *GetRichmanBattleProgressListReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetRichmanBattleProgressListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ProgressList) > 0 {
		for _, e := range m.ProgressList {
			l = e.Size()
			n += 1 + l + sovAwardgame2018(uint64(l))
		}
	}
	return n
}

func (m *DoTestReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Uid))
	}
	if m.TestType != 0 {
		n += 1 + sovAwardgame2018(uint64(m.TestType))
	}
	if m.ItemId != 0 {
		n += 1 + sovAwardgame2018(uint64(m.ItemId))
	}
	if m.TargetUid != 0 {
		n += 1 + sovAwardgame2018(uint64(m.TargetUid))
	}
	if m.TotalPrice != 0 {
		n += 1 + sovAwardgame2018(uint64(m.TotalPrice))
	}
	if m.SendTime != 0 {
		n += 1 + sovAwardgame2018(uint64(m.SendTime))
	}
	if m.ChannelId != 0 {
		n += 1 + sovAwardgame2018(uint64(m.ChannelId))
	}
	return n
}

func (m *DoTestResp) Size() (n int) {
	var l int
	_ = l
	if m.Cnt != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Cnt))
	}
	return n
}

func (m *ShorttimeComsumeInfo) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Uid))
	}
	if m.Price != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Price))
	}
	if m.Ts != 0 {
		n += 1 + sovAwardgame2018(uint64(m.Ts))
	}
	l = len(m.OrderId)
	if l > 0 {
		n += 1 + l + sovAwardgame2018(uint64(l))
	}
	return n
}

func sovAwardgame2018(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozAwardgame2018(x uint64) (n int) {
	return sovAwardgame2018(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *SingleTypeSignUpInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SingleTypeSignUpInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SingleTypeSignUpInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsPayTicket", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsPayTicket = bool(v != 0)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SingleType", wireType)
			}
			m.SingleType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SingleType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field JoinTs", wireType)
			}
			m.JoinTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.JoinTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MutiTypeSignUpMember) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MutiTypeSignUpMember: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MutiTypeSignUpMember: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field JoinTs", wireType)
			}
			m.JoinTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.JoinTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsPayTicket", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsPayTicket = bool(v != 0)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsCreator", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsCreator = bool(v != 0)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MutiType", wireType)
			}
			m.MutiType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MutiType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MutiTypeTeamInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MutiTypeTeamInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MutiTypeTeamInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MutiTeamId", wireType)
			}
			m.MutiTeamId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MutiTeamId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MutiTeamType", wireType)
			}
			m.MutiTeamType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MutiTeamType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MemberFullList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MemberFullList = append(m.MemberFullList, &MutiTypeSignUpMember{})
			if err := m.MemberFullList[len(m.MemberFullList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreaterUid", wireType)
			}
			m.CreaterUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreaterUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MutiTypeSignUpInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MutiTypeSignUpInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MutiTypeSignUpInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MutiType", wireType)
			}
			m.MutiType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MutiType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MutiTeamId", wireType)
			}
			m.MutiTeamId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MutiTeamId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MutiTeamType", wireType)
			}
			m.MutiTeamType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MutiTeamType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field JoinTs", wireType)
			}
			m.JoinTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.JoinTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsPayTicket", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsPayTicket = bool(v != 0)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsCreator", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsCreator = bool(v != 0)
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MemberFullList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MemberFullList = append(m.MemberFullList, &MutiTypeSignUpMember{})
			if err := m.MemberFullList[len(m.MemberFullList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastTeamQuitReason", wireType)
			}
			m.LastTeamQuitReason = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastTeamQuitReason |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastTeamQuitId", wireType)
			}
			m.LastTeamQuitId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastTeamQuitId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastTeamQuitOpUid", wireType)
			}
			m.LastTeamQuitOpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastTeamQuitOpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsNeedNotifyTeamQuit", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsNeedNotifyTeamQuit = bool(v != 0)
		case 14:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastTeamQuitType", wireType)
			}
			m.LastTeamQuitType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastTeamQuitType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RichManSignUpInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RichManSignUpInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RichManSignUpInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsPayTicket", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsPayTicket = bool(v != 0)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field JoinTs", wireType)
			}
			m.JoinTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.JoinTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserSignUpInfoReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserSignUpInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserSignUpInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserSignUpInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserSignUpInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserSignUpInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SingleSignup", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.SingleSignup == nil {
				m.SingleSignup = &SingleTypeSignUpInfo{}
			}
			if err := m.SingleSignup.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MutiSignup", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.MutiSignup == nil {
				m.MutiSignup = &MutiTypeSignUpInfo{}
			}
			if err := m.MutiSignup.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RichmanSignup", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.RichmanSignup == nil {
				m.RichmanSignup = &RichManSignUpInfo{}
			}
			if err := m.RichmanSignup.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserMutiSignUpInfoReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserMutiSignUpInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserMutiSignUpInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserMutiSignUpInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserMutiSignUpInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserMutiSignUpInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MutiSignup", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.MutiSignup == nil {
				m.MutiSignup = &MutiTypeSignUpInfo{}
			}
			if err := m.MutiSignup.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetUserSignUpInfoReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetUserSignUpInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetUserSignUpInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsPayTicket", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsPayTicket = bool(v != 0)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SingleSignupType", wireType)
			}
			m.SingleSignupType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SingleSignupType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MutiSignupType", wireType)
			}
			m.MutiSignupType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MutiSignupType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsSignupRichman", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsSignupRichman = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetUserSignUpInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetUserSignUpInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetUserSignUpInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SingleSignup", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.SingleSignup == nil {
				m.SingleSignup = &SingleTypeSignUpInfo{}
			}
			if err := m.SingleSignup.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MutiSignup", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.MutiSignup == nil {
				m.MutiSignup = &MutiTypeSignUpInfo{}
			}
			if err := m.MutiSignup.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RichmanSignup", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.RichmanSignup == nil {
				m.RichmanSignup = &RichManSignUpInfo{}
			}
			if err := m.RichmanSignup.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *JoinMutiTypeTeamReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: JoinMutiTypeTeamReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: JoinMutiTypeTeamReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MutiTeamId", wireType)
			}
			m.MutiTeamId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MutiTeamId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *JoinMutiTypeTeamResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: JoinMutiTypeTeamResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: JoinMutiTypeTeamResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *QuitMutiTypeTeamReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: QuitMutiTypeTeamReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: QuitMutiTypeTeamReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReasonType", wireType)
			}
			m.ReasonType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReasonType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MutiTeamId", wireType)
			}
			m.MutiTeamId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MutiTeamId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *QuitMutiTypeTeamResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: QuitMutiTypeTeamResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: QuitMutiTypeTeamResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetuserNewTeamId", wireType)
			}
			m.TargetuserNewTeamId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetuserNewTeamId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetuserMutiType", wireType)
			}
			m.TargetuserMutiType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetuserMutiType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateMutiTypeTeamReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateMutiTypeTeamReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateMutiTypeTeamReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateMutiTypeTeamResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateMutiTypeTeamResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateMutiTypeTeamResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MutiTeamId", wireType)
			}
			m.MutiTeamId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MutiTeamId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateMutiTypeTeamNotifyReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateMutiTypeTeamNotifyReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateMutiTypeTeamNotifyReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsOpen", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsOpen = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateMutiTypeTeamNotifyResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateMutiTypeTeamNotifyResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateMutiTypeTeamNotifyResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMutiTypeTeamInfoReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMutiTypeTeamInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMutiTypeTeamInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TeamId", wireType)
			}
			m.TeamId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TeamId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMutiTypeTeamInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMutiTypeTeamInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMutiTypeTeamInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MutiInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.MutiInfo == nil {
				m.MutiInfo = &MutiTypeTeamInfo{}
			}
			if err := m.MutiInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SingleTypeSignUpCount) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SingleTypeSignUpCount: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SingleTypeSignUpCount: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SingleType", wireType)
			}
			m.SingleType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SingleType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MutiTypeSignUpCount) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MutiTypeSignUpCount: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MutiTypeSignUpCount: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MutiType", wireType)
			}
			m.MutiType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MutiType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSignupCountReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSignupCountReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSignupCountReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSignupCountResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSignupCountResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSignupCountResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SingleSignupCntList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SingleSignupCntList = append(m.SingleSignupCntList, &SingleTypeSignUpCount{})
			if err := m.SingleSignupCntList[len(m.SingleSignupCntList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MutiSignupCntList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MutiSignupCntList = append(m.MutiSignupCntList, &MutiTypeSignUpCount{})
			if err := m.MutiSignupCntList[len(m.MutiSignupCntList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RichmanSignupCnt", wireType)
			}
			m.RichmanSignupCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RichmanSignupCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCurrStageReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetCurrStageReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetCurrStageReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCurrStageResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetCurrStageResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetCurrStageResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CurrStage", wireType)
			}
			m.CurrStage = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CurrStage |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CurrStageBeginTs", wireType)
			}
			m.CurrStageBeginTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CurrStageBeginTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NextStage", wireType)
			}
			m.NextStage = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NextStage |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NextStageBeginTs", wireType)
			}
			m.NextStageBeginTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NextStageBeginTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *NotifyDateChangeReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NotifyDateChangeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NotifyDateChangeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DateInt", wireType)
			}
			m.DateInt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DateInt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MultiTypeTeamMemberRankInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MultiTypeTeamMemberRankInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MultiTypeTeamMemberRankInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TeamId", wireType)
			}
			m.TeamId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TeamId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankValue", wireType)
			}
			m.RankValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RankValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsCreator", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsCreator = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MultiTypeTeamRankBrief) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MultiTypeTeamRankBrief: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MultiTypeTeamRankBrief: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TeamId", wireType)
			}
			m.TeamId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TeamId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ListType", wireType)
			}
			m.ListType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ListType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankValue", wireType)
			}
			m.RankValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RankValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ranking", wireType)
			}
			m.Ranking = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ranking |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BonusValue", wireType)
			}
			m.BonusValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BonusValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MultiTypeTeamRankInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MultiTypeTeamRankInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MultiTypeTeamRankInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BriefInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BriefInfo == nil {
				m.BriefInfo = &MultiTypeTeamRankBrief{}
			}
			if err := m.BriefInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MemberList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MemberList = append(m.MemberList, &MultiTypeTeamMemberRankInfo{})
			if err := m.MemberList[len(m.MemberList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMultiTypeTeamRankListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMultiTypeTeamRankListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMultiTypeTeamRankListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MultiTeamType", wireType)
			}
			m.MultiTeamType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MultiTeamType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DateInt", wireType)
			}
			m.DateInt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DateInt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Stage", wireType)
			}
			m.Stage = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Stage |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMultiTypeTeamRankListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMultiTypeTeamRankListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMultiTypeTeamRankListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TeamList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TeamList = append(m.TeamList, &MultiTypeTeamRankInfo{})
			if err := m.TeamList[len(m.TeamList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsValidStage", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsValidStage = bool(v != 0)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalTeamCount", wireType)
			}
			m.TotalTeamCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalTeamCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMultiTypeTeamRankInfoReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMultiTypeTeamRankInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMultiTypeTeamRankInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DateInt", wireType)
			}
			m.DateInt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DateInt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Stage", wireType)
			}
			m.Stage = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Stage |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MultiTeamType", wireType)
			}
			m.MultiTeamType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MultiTeamType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMultiTypeTeamRankInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMultiTypeTeamRankInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMultiTypeTeamRankInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TeamInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.TeamInfo == nil {
				m.TeamInfo = &MultiTypeTeamRankInfo{}
			}
			if err := m.TeamInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsSignedUp", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsSignedUp = bool(v != 0)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DiffValue", wireType)
			}
			m.DiffValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DiffValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToBePromoted", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ToBePromoted = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RichmanRankItem) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RichmanRankItem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RichmanRankItem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			m.Value = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Value |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AddtionValue", wireType)
			}
			m.AddtionValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AddtionValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RichmanMyRankValue) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RichmanMyRankValue: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RichmanMyRankValue: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			m.Value = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Value |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Rank", wireType)
			}
			m.Rank = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Rank |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsQualified", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsQualified = bool(v != 0)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UpgradeDifValue", wireType)
			}
			m.UpgradeDifValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UpgradeDifValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field JoinrankDifValue", wireType)
			}
			m.JoinrankDifValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.JoinrankDifValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRichmanRankListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRichmanRankListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRichmanRankListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StageId", wireType)
			}
			m.StageId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StageId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Day", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Day = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsNeedMyRank", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsNeedMyRank = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRichmanRankListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRichmanRankListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRichmanRankListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RankList = append(m.RankList, &RichmanRankItem{})
			if err := m.RankList[len(m.RankList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MyRank", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.MyRank == nil {
				m.MyRank = &RichmanMyRankValue{}
			}
			if err := m.MyRank.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalRankCount", wireType)
			}
			m.TotalRankCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalRankCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SingleRankItem) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SingleRankItem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SingleRankItem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			m.Value = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Value |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AddtionValue", wireType)
			}
			m.AddtionValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AddtionValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SingleMyRankValue) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SingleMyRankValue: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SingleMyRankValue: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SingleSignupType", wireType)
			}
			m.SingleSignupType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SingleSignupType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			m.Value = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Value |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Rank", wireType)
			}
			m.Rank = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Rank |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UpgradeDifValue", wireType)
			}
			m.UpgradeDifValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UpgradeDifValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field JoinrankDifValue", wireType)
			}
			m.JoinrankDifValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.JoinrankDifValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsQualified", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsQualified = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSingleRankListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSingleRankListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSingleRankListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SingleType", wireType)
			}
			m.SingleType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SingleType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StageId", wireType)
			}
			m.StageId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StageId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Day", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Day = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsNeedMyRank", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsNeedMyRank = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSingleRankListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSingleRankListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSingleRankListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RankList = append(m.RankList, &SingleRankItem{})
			if err := m.RankList[len(m.RankList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MyRank", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.MyRank == nil {
				m.MyRank = &SingleMyRankValue{}
			}
			if err := m.MyRank.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalRankCount", wireType)
			}
			m.TotalRankCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalRankCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelRankItem) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelRankItem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelRankItem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Cid", wireType)
			}
			m.Cid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Cid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			m.Value = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Value |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AddtionValue", wireType)
			}
			m.AddtionValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AddtionValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelRankListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelRankListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelRankListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StageId", wireType)
			}
			m.StageId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StageId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Day", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Day = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelRankListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelRankListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelRankListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RankList = append(m.RankList, &ChannelRankItem{})
			if err := m.RankList[len(m.RankList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalRankCount", wireType)
			}
			m.TotalRankCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalRankCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RichmanBattleProgress) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RichmanBattleProgress: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RichmanBattleProgress: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TimeTs", wireType)
			}
			m.TimeTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TimeTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Msg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRichmanBattleProgressListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRichmanBattleProgressListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRichmanBattleProgressListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRichmanBattleProgressListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRichmanBattleProgressListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRichmanBattleProgressListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProgressList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ProgressList = append(m.ProgressList, &RichmanBattleProgress{})
			if err := m.ProgressList[len(m.ProgressList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DoTestReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DoTestReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DoTestReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TestType", wireType)
			}
			m.TestType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TestType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			m.ItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalPrice", wireType)
			}
			m.TotalPrice = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalPrice |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SendTime", wireType)
			}
			m.SendTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SendTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DoTestResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DoTestResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DoTestResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Cnt", wireType)
			}
			m.Cnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Cnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ShorttimeComsumeInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ShorttimeComsumeInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ShorttimeComsumeInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Price", wireType)
			}
			m.Price = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Price |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ts", wireType)
			}
			m.Ts = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ts |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAwardgame2018(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardgame2018
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipAwardgame2018(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowAwardgame2018
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAwardgame2018
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthAwardgame2018
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowAwardgame2018
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipAwardgame2018(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthAwardgame2018 = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowAwardgame2018   = fmt2.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("services/2018YearAwardGame/awardgame2018.proto", fileDescriptorAwardgame2018)
}

var fileDescriptorAwardgame2018 = []byte{
	// 3171 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe4, 0x5a, 0xcb, 0x6f, 0x23, 0xc7,
	0xd1, 0xf7, 0x90, 0x12, 0x25, 0x95, 0x5e, 0x54, 0xeb, 0xb1, 0x5a, 0xee, 0xae, 0x76, 0x76, 0x56,
	0x6b, 0xcb, 0xeb, 0x7d, 0x2f, 0x60, 0x7c, 0x1f, 0x3f, 0x79, 0x01, 0x3d, 0x68, 0x99, 0x5e, 0x91,
	0x92, 0x49, 0xca, 0x86, 0xbf, 0x0f, 0xf6, 0x7c, 0x23, 0x4e, 0x4b, 0xdb, 0x59, 0xce, 0x63, 0xa7,
	0x87, 0xbb, 0xab, 0x00, 0x46, 0x0c, 0xc4, 0x09, 0x8c, 0x20, 0x48, 0x8c, 0x5c, 0x73, 0x32, 0xe0,
	0x43, 0xe0, 0x00, 0x01, 0x72, 0x0b, 0x92, 0x5b, 0x4e, 0x46, 0x82, 0xc0, 0x46, 0xfe, 0x82, 0xc4,
	0xbe, 0xf8, 0x94, 0x43, 0x80, 0x5c, 0x72, 0x0a, 0xfa, 0x31, 0x9c, 0x27, 0x29, 0xda, 0x86, 0x91,
	0x43, 0x2e, 0x02, 0xbb, 0xbb, 0xba, 0xea, 0x57, 0xd5, 0x55, 0xd5, 0x55, 0x3d, 0x82, 0x1b, 0x14,
	0x7b, 0x8f, 0x49, 0x1b, 0xd3, 0x9b, 0x77, 0x6e, 0xdd, 0xfe, 0xaf, 0x37, 0xb1, 0xe1, 0x6d, 0x3c,
	0x31, 0x3c, 0x73, 0xc7, 0xb0, 0xf0, 0x4d, 0x83, 0xfd, 0x3a, 0x36, 0x2c, 0xcc, 0x96, 0x6e, 0xb8,
	0x9e, 0xe3, 0x3b, 0x68, 0x3a, 0x36, 0x59, 0x5a, 0x6d, 0x3b, 0x96, 0xe5, 0xd8, 0x37, 0xfd, 0xce,
	0x63, 0x97, 0xb4, 0x1f, 0x76, 0xf0, 0x4d, 0xfa, 0xf0, 0xb0, 0x4b, 0x3a, 0x3e, 0xb1, 0xfd, 0x13,
	0x17, 0x8b, 0x4d, 0xda, 0x0f, 0x15, 0x58, 0x68, 0x12, 0xfb, 0xb8, 0x83, 0x5b, 0x27, 0x2e, 0x6e,
	0x92, 0x63, 0xfb, 0xc0, 0xad, 0xda, 0x47, 0x0e, 0x2a, 0x42, 0xbe, 0x4b, 0xcc, 0x65, 0x45, 0x55,
	0xd6, 0xa6, 0x1b, 0xec, 0x27, 0xd2, 0x60, 0x9a, 0x50, 0xdd, 0x35, 0x4e, 0x74, 0x9f, 0xb4, 0x1f,
	0x62, 0x7f, 0x39, 0xa7, 0x2a, 0x6b, 0xe3, 0x8d, 0x49, 0x42, 0xf7, 0x8d, 0x93, 0x16, 0x9f, 0x42,
	0x17, 0x61, 0x92, 0x72, 0x6e, 0x3a, 0x93, 0xb1, 0x9c, 0xe7, 0xbb, 0x81, 0xf6, 0x04, 0xa0, 0x33,
	0x30, 0xf6, 0x1d, 0x87, 0xd8, 0xba, 0x4f, 0x97, 0x47, 0xf8, 0x62, 0x81, 0x0d, 0x5b, 0x54, 0xfb,
	0x50, 0x81, 0x85, 0x5a, 0xd7, 0x27, 0x21, 0x8c, 0x1a, 0xb6, 0x0e, 0xb1, 0x97, 0x01, 0x24, 0xc2,
	0x23, 0x17, 0xe5, 0x91, 0x46, 0x98, 0x4f, 0x23, 0xbc, 0x00, 0x40, 0xa8, 0xde, 0xf6, 0xb0, 0xe1,
	0x3b, 0x1e, 0xc7, 0x30, 0xde, 0x98, 0x20, 0x74, 0x4b, 0x4c, 0xa0, 0x73, 0x30, 0x61, 0x75, 0x7d,
	0x22, 0xe0, 0x8f, 0x72, 0xee, 0xe3, 0x96, 0x84, 0xa5, 0xfd, 0x41, 0x81, 0x62, 0x80, 0xb1, 0x85,
	0x0d, 0x8b, 0x1b, 0x4a, 0x85, 0x29, 0xb1, 0x03, 0x1b, 0x96, 0xde, 0x03, 0x0a, 0x7c, 0x13, 0xa3,
	0x31, 0xd1, 0x2a, 0xcc, 0x84, 0x14, 0x9c, 0xb1, 0x80, 0x3d, 0x15, 0xd0, 0x70, 0xcb, 0xd4, 0xa0,
	0x68, 0x71, 0x8d, 0xf5, 0xa3, 0x6e, 0xa7, 0xa3, 0x77, 0x08, 0x65, 0xf8, 0xf3, 0x6b, 0x93, 0x77,
	0x2e, 0xdf, 0x88, 0x1f, 0x77, 0x96, 0x99, 0x1a, 0x33, 0x62, 0xf3, 0xcb, 0xdd, 0x4e, 0x67, 0x97,
	0x50, 0x7e, 0x12, 0x5c, 0x49, 0xec, 0xe9, 0xcc, 0x7c, 0xc2, 0xd8, 0x20, 0xa7, 0x0e, 0x88, 0xa9,
	0xfd, 0x7c, 0x04, 0x50, 0x9c, 0x13, 0x57, 0x27, 0x66, 0x00, 0x25, 0x6e, 0x80, 0x94, 0xae, 0xb9,
	0x21, 0x74, 0xcd, 0x67, 0xe8, 0xda, 0xcf, 0x0b, 0xd2, 0x27, 0x38, 0x7a, 0xda, 0x09, 0x16, 0x92,
	0x27, 0x98, 0x65, 0xc7, 0xb1, 0xaf, 0x6f, 0xc7, 0xdb, 0xb0, 0xd8, 0x31, 0xa8, 0x2f, 0x14, 0x7a,
	0xd4, 0x25, 0xbe, 0xee, 0x61, 0x83, 0x3a, 0xf6, 0x32, 0x70, 0xe0, 0x88, 0x2d, 0x32, 0xbd, 0x5e,
	0xeb, 0x12, 0xbf, 0xc1, 0x57, 0xd0, 0xf3, 0x30, 0x97, 0xd8, 0x42, 0xcc, 0xe5, 0x49, 0x4e, 0x3e,
	0x13, 0x25, 0xaf, 0x9a, 0xe8, 0x56, 0x8a, 0xbb, 0xe3, 0xf2, 0xf3, 0x9a, 0xe2, 0xe4, 0x73, 0x51,
	0xf2, 0x3d, 0xf7, 0x80, 0x98, 0xe8, 0x45, 0x58, 0x26, 0x54, 0xb7, 0x31, 0x36, 0x75, 0xdb, 0xf1,
	0xc9, 0xd1, 0x49, 0xb8, 0x77, 0x79, 0x9a, 0xdb, 0x62, 0x81, 0xd0, 0x3a, 0xc6, 0x66, 0x9d, 0xaf,
	0x06, 0x9b, 0xd1, 0x75, 0x98, 0x4f, 0x48, 0xe2, 0xa7, 0x33, 0xc3, 0xe5, 0x14, 0xa3, 0x72, 0xb8,
	0xab, 0x1f, 0xc2, 0x5c, 0x83, 0xb4, 0x1f, 0xd4, 0x0c, 0xfb, 0x1b, 0xe7, 0x84, 0xc8, 0x61, 0xe7,
	0x63, 0x21, 0xbf, 0x06, 0x0b, 0x3b, 0xd8, 0x3f, 0xa0, 0xd8, 0x0b, 0x65, 0x34, 0xf0, 0xa3, 0xb4,
	0x18, 0xed, 0x6f, 0x0a, 0x2c, 0x66, 0x90, 0x52, 0x17, 0xbd, 0x02, 0xd3, 0x32, 0xe1, 0x50, 0x72,
	0x6c, 0x77, 0x5d, 0xbe, 0x2b, 0x7d, 0xd4, 0x59, 0x29, 0xae, 0x31, 0x25, 0x76, 0x36, 0xf9, 0x46,
	0xb4, 0x09, 0x93, 0xdc, 0x73, 0x25, 0x9f, 0x1c, 0xe7, 0x73, 0x69, 0xa0, 0xcb, 0x70, 0x2e, 0xdc,
	0xfb, 0x25, 0x8f, 0x1d, 0x98, 0xf1, 0x48, 0xfb, 0x81, 0x65, 0xd8, 0x01, 0x9b, 0x3c, 0x67, 0xa3,
	0x26, 0xd8, 0xa4, 0x4c, 0xdb, 0x98, 0x96, 0xfb, 0x04, 0x23, 0xed, 0x1a, 0x2c, 0x4b, 0x7d, 0x6b,
	0x92, 0xfb, 0x20, 0xf3, 0xe8, 0x70, 0xb6, 0x0f, 0x35, 0x4d, 0xe9, 0xa5, 0x7c, 0x0d, 0xbd, 0xb4,
	0xcf, 0xd8, 0x2d, 0x31, 0xd4, 0x51, 0x0d, 0xe5, 0x11, 0xd7, 0x00, 0xc5, 0x0e, 0x2d, 0x9a, 0x28,
	0x8a, 0xd1, 0x43, 0xe1, 0xc9, 0x62, 0x0d, 0x8a, 0x11, 0x05, 0x04, 0xad, 0xc8, 0x1a, 0x33, 0x21,
	0x44, 0x4e, 0x79, 0x15, 0xe6, 0x08, 0x0d, 0xe8, 0xa4, 0x41, 0x65, 0x06, 0x99, 0x25, 0x54, 0x10,
	0x36, 0xc4, 0x34, 0x77, 0xa9, 0xe6, 0x7f, 0x92, 0x4b, 0x55, 0x61, 0xfe, 0x55, 0x87, 0xd8, 0xd1,
	0xfb, 0x2b, 0xfb, 0x04, 0x4f, 0x4d, 0xf2, 0xda, 0x12, 0x2c, 0xa4, 0x59, 0x51, 0x57, 0xfb, 0x89,
	0x02, 0xf3, 0x2c, 0x83, 0x24, 0x65, 0x2c, 0x42, 0x41, 0xa6, 0x35, 0x21, 0x66, 0xd4, 0xe1, 0xa9,
	0xec, 0x02, 0x80, 0x6f, 0x78, 0xc7, 0xd8, 0xe7, 0x4b, 0x42, 0xcc, 0x84, 0x98, 0x61, 0xcb, 0x17,
	0x61, 0x52, 0xa4, 0xda, 0x58, 0x2d, 0x21, 0xa6, 0x32, 0x6f, 0xa3, 0x91, 0x14, 0xd0, 0x77, 0x60,
	0x21, 0x8d, 0x87, 0xba, 0xe8, 0x2e, 0x2c, 0x09, 0x39, 0x5d, 0x8a, 0x3d, 0xdd, 0xc6, 0x4f, 0x12,
	0xb7, 0xf7, 0x7c, 0xb8, 0x5a, 0xc7, 0x4f, 0xe4, 0xd5, 0x76, 0x0b, 0x16, 0x22, 0x9b, 0xc2, 0x4b,
	0x52, 0x00, 0x47, 0xe1, 0x5a, 0x20, 0x4e, 0xbb, 0x01, 0x8b, 0xfc, 0x56, 0xc2, 0xc3, 0x19, 0x44,
	0x2b, 0xc3, 0x52, 0x16, 0x3d, 0x75, 0x4f, 0x2f, 0x32, 0xb4, 0x1a, 0x9c, 0x3b, 0x70, 0xcd, 0xc4,
	0x5e, 0x71, 0x07, 0x0c, 0x38, 0x82, 0x33, 0x30, 0x46, 0xa8, 0xee, 0xb8, 0xd8, 0x96, 0x71, 0x5a,
	0x20, 0x74, 0xcf, 0xc5, 0xb6, 0xb6, 0x02, 0xe7, 0xfb, 0xb3, 0xa3, 0xae, 0x76, 0x1b, 0x96, 0x76,
	0xb0, 0x9f, 0x2c, 0x86, 0x98, 0xa4, 0x33, 0x30, 0x16, 0x47, 0x59, 0xf0, 0x05, 0xc2, 0x37, 0xe0,
	0x4c, 0xe6, 0x16, 0xea, 0xa2, 0x75, 0x59, 0x74, 0x10, 0xfb, 0xc8, 0x91, 0xe1, 0x76, 0xb1, 0x4f,
	0x98, 0xf4, 0xf6, 0xf1, 0xaa, 0x84, 0xfd, 0xd2, 0xea, 0xb0, 0x98, 0x0c, 0xc6, 0x2d, 0xa7, 0x6b,
	0xa7, 0xaa, 0x51, 0x25, 0x55, 0x8d, 0x2e, 0xc0, 0x68, 0x9b, 0x51, 0xca, 0x33, 0x14, 0x03, 0xed,
	0x15, 0x98, 0x8f, 0x07, 0xa5, 0xe0, 0x36, 0xb0, 0x32, 0xca, 0xe6, 0x34, 0x0f, 0x73, 0x3b, 0xd8,
	0x17, 0x01, 0xc8, 0x99, 0x34, 0xf0, 0x23, 0xed, 0xef, 0x0a, 0xa0, 0xe4, 0x2c, 0x75, 0xd1, 0x9b,
	0xb0, 0x14, 0x4f, 0x8a, 0x6d, 0xdb, 0x17, 0xd5, 0x8b, 0xc2, 0xab, 0x97, 0xd5, 0x53, 0xf2, 0x8f,
	0xe0, 0x34, 0x1f, 0x4d, 0x40, 0x5b, 0xb6, 0xcf, 0x6b, 0x98, 0x26, 0x2c, 0x44, 0x33, 0x68, 0x8f,
	0x71, 0x8e, 0x33, 0xd6, 0x06, 0x26, 0x24, 0xc1, 0x76, 0x2e, 0xcc, 0x48, 0x01, 0xd3, 0x6b, 0x80,
	0xe2, 0x89, 0x89, 0xf1, 0x0d, 0x92, 0x78, 0x2c, 0xf5, 0x6c, 0xd9, 0xbe, 0x36, 0x07, 0xb3, 0x3b,
	0xd8, 0xdf, 0xea, 0x7a, 0x5e, 0xd3, 0x37, 0x8e, 0x31, 0xb3, 0xc3, 0xaf, 0x14, 0x28, 0xc6, 0xe7,
	0xa8, 0xcb, 0x72, 0x42, 0xbb, 0xeb, 0x79, 0x3a, 0x65, 0x33, 0xd2, 0xca, 0x13, 0xed, 0x80, 0x84,
	0x55, 0x31, 0xe1, 0xb2, 0x7e, 0x88, 0x8f, 0xa3, 0x6d, 0x40, 0xb1, 0x47, 0xb7, 0xc9, 0x16, 0x5a,
	0x94, 0x71, 0xb3, 0xf1, 0x53, 0x5f, 0x72, 0x13, 0xd8, 0x26, 0xd8, 0x4c, 0x8f, 0x5b, 0xb8, 0x1c,
	0x72, 0x13, 0x79, 0xa4, 0xd8, 0xa3, 0x93, 0xdc, 0xb4, 0x5b, 0x30, 0x2f, 0x22, 0x60, 0xdb, 0xf0,
	0xf1, 0xd6, 0x03, 0xc3, 0xe6, 0x7a, 0xa0, 0xb3, 0x30, 0xce, 0x02, 0x45, 0x27, 0xb6, 0x2f, 0x01,
	0x8f, 0xb1, 0x71, 0xd5, 0xf6, 0x59, 0x77, 0x75, 0xae, 0xd6, 0xed, 0x84, 0x9e, 0x2b, 0x8b, 0x4c,
	0xc3, 0x7e, 0xc8, 0x0b, 0xaa, 0x7e, 0xb1, 0x12, 0x64, 0xe5, 0x5c, 0x98, 0x95, 0x2f, 0x00, 0x78,
	0x86, 0xfd, 0x50, 0x7f, 0x6c, 0x74, 0xba, 0x3d, 0x55, 0xd8, 0xcc, 0xeb, 0x6c, 0xe2, 0x94, 0xb6,
	0x46, 0xfb, 0x85, 0x02, 0x4b, 0x31, 0x20, 0x0c, 0xc2, 0xa6, 0x47, 0xf0, 0x51, 0x7f, 0x0c, 0xe7,
	0x60, 0x82, 0x79, 0x49, 0x34, 0xc9, 0x8d, 0xb3, 0x09, 0xee, 0xef, 0xa7, 0xc0, 0x59, 0x86, 0x31,
	0x36, 0x20, 0xf6, 0xb1, 0xb4, 0x66, 0x30, 0x64, 0x31, 0x79, 0xe8, 0xd8, 0x5d, 0x2a, 0x77, 0x8a,
	0x16, 0x0b, 0xf8, 0x14, 0xdf, 0xaa, 0x7d, 0xac, 0xc0, 0x62, 0x0a, 0x2a, 0xb7, 0xd6, 0x36, 0xc0,
	0x21, 0x83, 0x1c, 0x4d, 0x13, 0x57, 0x52, 0xce, 0x9b, 0xa5, 0x64, 0x63, 0x82, 0x6f, 0xe4, 0x5c,
	0xee, 0xc3, 0xa4, 0xec, 0x0f, 0x22, 0x31, 0x70, 0x75, 0x10, 0x9b, 0xf8, 0xa1, 0x35, 0x40, 0x6c,
	0x67, 0x41, 0xa0, 0x7d, 0xa4, 0xc0, 0x39, 0x9e, 0xd4, 0x12, 0x52, 0xd9, 0x22, 0xf3, 0x8d, 0x67,
	0x61, 0xd6, 0x62, 0x6b, 0x91, 0x7e, 0x48, 0x18, 0x79, 0x9a, 0x4f, 0xf7, 0x1a, 0xa2, 0xa8, 0x0f,
	0xe5, 0x62, 0x3e, 0xc4, 0x32, 0x4b, 0xd4, 0x7d, 0xc5, 0x00, 0x2d, 0x41, 0xc1, 0x39, 0x3a, 0xa2,
	0xd8, 0x0f, 0x1a, 0x28, 0x31, 0x0a, 0xf3, 0xd0, 0x68, 0x34, 0x0f, 0xfd, 0x5a, 0x81, 0xf3, 0xfd,
	0x61, 0x52, 0x17, 0x6d, 0xc0, 0x04, 0x47, 0x38, 0x20, 0xdf, 0x64, 0x9e, 0x49, 0x63, 0x9c, 0x6d,
	0xe3, 0xf9, 0x60, 0x15, 0x66, 0x08, 0x3f, 0x55, 0x62, 0xca, 0x78, 0x13, 0x37, 0xca, 0x14, 0x61,
	0x07, 0x4b, 0x4c, 0x11, 0x72, 0x6b, 0x50, 0xf4, 0x1d, 0xdf, 0xe8, 0x08, 0x83, 0x08, 0xa8, 0x42,
	0xb1, 0x19, 0x3e, 0xcf, 0x98, 0xf3, 0x94, 0xa3, 0xbd, 0xdf, 0xc7, 0xb4, 0xfd, 0x4b, 0xcf, 0xaf,
	0x6c, 0xc4, 0x8c, 0xd3, 0x19, 0xc9, 0x38, 0x1d, 0xed, 0x93, 0x3e, 0xe6, 0xeb, 0xdd, 0x5f, 0x81,
	0xf9, 0x22, 0x8e, 0xf9, 0x15, 0xcc, 0x17, 0x3c, 0x23, 0xc8, 0xda, 0x15, 0x9b, 0xba, 0x2c, 0x16,
	0xc7, 0x1b, 0x20, 0xca, 0x56, 0x6c, 0x1e, 0xf0, 0xd4, 0x68, 0x92, 0xa3, 0xa3, 0x78, 0xc8, 0xb1,
	0x19, 0x11, 0x72, 0xab, 0x30, 0xe3, 0x3b, 0xfa, 0x21, 0xd6, 0x5d, 0xcf, 0xb1, 0x1c, 0x1f, 0x9b,
	0x32, 0x0b, 0x4c, 0xf9, 0xce, 0x26, 0xde, 0x97, 0x73, 0xda, 0xdb, 0x30, 0x2b, 0x2b, 0x60, 0x8e,
	0xc1, 0xc7, 0x56, 0x86, 0x21, 0x17, 0x60, 0x54, 0x08, 0x91, 0x97, 0x19, 0x1f, 0xa0, 0xcb, 0x30,
	0x6d, 0x98, 0xa6, 0x4f, 0x1c, 0x3b, 0x06, 0x61, 0x4a, 0x4e, 0x8a, 0xe8, 0xfd, 0x93, 0x02, 0x48,
	0x0a, 0xa8, 0x9d, 0x34, 0x7a, 0xf9, 0x60, 0x58, 0x19, 0x08, 0x46, 0x58, 0xa2, 0x90, 0xac, 0xf9,
	0x6f, 0x74, 0x89, 0x5b, 0xe6, 0x51, 0xd7, 0xe8, 0x90, 0x23, 0xd2, 0x53, 0x6b, 0x92, 0xd0, 0xd7,
	0x82, 0x29, 0x56, 0xf8, 0x77, 0xdd, 0x63, 0xcf, 0x30, 0xb1, 0x6e, 0x92, 0xa3, 0x58, 0x6a, 0x99,
	0x95, 0x0b, 0xdb, 0x44, 0xda, 0xe9, 0x1a, 0x20, 0xd6, 0x7f, 0xf2, 0xec, 0x15, 0x12, 0x17, 0x44,
	0xce, 0x0f, 0x56, 0x02, 0x6a, 0xed, 0x97, 0xa2, 0xf3, 0x8c, 0xd8, 0x2c, 0x08, 0xed, 0xb4, 0x4a,
	0x61, 0x4c, 0xe6, 0xb2, 0x63, 0x32, 0x1f, 0x89, 0x49, 0xe6, 0xad, 0xe2, 0xde, 0xe9, 0x55, 0xae,
	0x63, 0x7c, 0x2c, 0xb2, 0xbf, 0x69, 0x9c, 0x70, 0x05, 0x26, 0x1a, 0xec, 0x27, 0xba, 0x02, 0xb3,
	0x41, 0xd7, 0x6f, 0x9d, 0xe8, 0xdc, 0x44, 0x85, 0x20, 0xba, 0x58, 0xb3, 0x2f, 0x6c, 0xad, 0xfd,
	0x4e, 0xe1, 0x65, 0x59, 0x0a, 0x2d, 0x75, 0xd1, 0xff, 0x00, 0x4f, 0xcf, 0xd1, 0x08, 0x5f, 0xc9,
	0x68, 0x21, 0x22, 0x8e, 0xd1, 0x18, 0xf7, 0x24, 0x03, 0x54, 0x86, 0xb1, 0x40, 0x6c, 0x76, 0x13,
	0x93, 0x3e, 0xf2, 0x46, 0xc1, 0xe2, 0x83, 0x30, 0xe2, 0xb9, 0xf8, 0x74, 0xc4, 0x33, 0x22, 0x11,
	0xf1, 0x6f, 0xc1, 0x8c, 0x28, 0x6a, 0xbe, 0x1d, 0xd7, 0xfc, 0xa7, 0x02, 0x73, 0x82, 0xff, 0x60,
	0xcf, 0xcc, 0xee, 0x4e, 0x73, 0x7d, 0xba, 0xd3, 0x1e, 0xa0, 0x7c, 0x96, 0x1f, 0x8f, 0x44, 0xfc,
	0xf8, 0x5b, 0x73, 0xd2, 0x54, 0x84, 0x8c, 0xa5, 0x22, 0x44, 0xfb, 0xa3, 0xc2, 0x1f, 0x5b, 0x42,
	0xfb, 0xf6, 0x77, 0xe3, 0x44, 0xd5, 0x9c, 0x4b, 0x55, 0xcd, 0xa1, 0x9f, 0xe7, 0xb3, 0xfd, 0x7c,
	0xa4, 0x9f, 0x9f, 0x8f, 0x66, 0xfa, 0x79, 0x61, 0xa0, 0x9f, 0x8f, 0x65, 0xf8, 0xf9, 0x6f, 0x44,
	0x54, 0x26, 0xb5, 0xa1, 0x2e, 0x2a, 0xa7, 0xdd, 0xfc, 0x42, 0x66, 0xe1, 0x9c, 0xe1, 0xe5, 0xff,
	0x9d, 0xf4, 0x72, 0x35, 0x73, 0xe7, 0x37, 0x73, 0xf2, 0xb7, 0x61, 0x96, 0x95, 0x8e, 0x36, 0xee,
	0x44, 0xbd, 0xbc, 0x1d, 0x1e, 0x41, 0xfb, 0x9b, 0x79, 0xf9, 0x7b, 0xc2, 0x34, 0x11, 0x19, 0xff,
	0x8e, 0x84, 0xa5, 0x7d, 0x8f, 0x27, 0xa2, 0x14, 0x8a, 0xe1, 0x12, 0x51, 0xc2, 0x40, 0x91, 0x23,
	0xca, 0xb2, 0x73, 0x2e, 0xd3, 0xce, 0x9b, 0xb0, 0x28, 0x93, 0xd2, 0xa6, 0xe1, 0xfb, 0x1d, 0x76,
	0x03, 0x1e, 0x7b, 0x98, 0x52, 0x5e, 0xef, 0x12, 0x0b, 0xb3, 0x42, 0x3f, 0xa8, 0x77, 0x89, 0x85,
	0x5b, 0x94, 0x29, 0x61, 0xd1, 0x63, 0xce, 0x6e, 0xa2, 0xc1, 0x7e, 0x6a, 0x97, 0xe0, 0x62, 0x98,
	0x4d, 0xe3, 0x6c, 0xa4, 0x51, 0x35, 0x0b, 0xd4, 0xc1, 0x24, 0xd4, 0x45, 0x55, 0x98, 0x76, 0xe5,
	0xdc, 0xa0, 0x02, 0x2b, 0x93, 0x49, 0x63, 0xca, 0x8d, 0xb0, 0xd3, 0x3e, 0x55, 0x60, 0x62, 0xdb,
	0x69, 0xe1, 0x7e, 0x27, 0x7a, 0x8e, 0x15, 0x22, 0x89, 0x9a, 0x9d, 0x4d, 0x04, 0xaf, 0xee, 0xc4,
	0xc7, 0xbc, 0xd2, 0x97, 0x81, 0xcb, 0x86, 0xd5, 0xe4, 0x43, 0xcc, 0x48, 0xc6, 0x43, 0x8c, 0x30,
	0xba, 0xeb, 0x91, 0x76, 0xaf, 0x64, 0xe7, 0x53, 0xfb, 0x6c, 0x86, 0x49, 0xa5, 0xd8, 0x36, 0x75,
	0x66, 0x48, 0x99, 0xa4, 0xc6, 0xd9, 0x44, 0x8b, 0x58, 0xbc, 0x53, 0x68, 0x8b, 0xf3, 0x64, 0x82,
	0xc7, 0x64, 0x47, 0x27, 0x66, 0xaa, 0xa6, 0xb6, 0x02, 0x10, 0x28, 0x44, 0x5d, 0x1e, 0x0a, 0xbd,
	0x36, 0x8a, 0xfd, 0xd4, 0x08, 0x2c, 0x34, 0x1f, 0x38, 0x9e, 0xcf, 0x78, 0x6f, 0x39, 0x16, 0xed,
	0x5a, 0xb8, 0xcf, 0x5b, 0xf4, 0x02, 0x8c, 0x0a, 0x80, 0x32, 0x68, 0xf8, 0x00, 0xcd, 0x40, 0xae,
	0xf7, 0xf0, 0x9c, 0xf3, 0x29, 0xf3, 0x62, 0xc7, 0x33, 0xb1, 0x17, 0x78, 0xf1, 0x44, 0x63, 0x8c,
	0x8f, 0xab, 0xe6, 0xd5, 0x77, 0x73, 0x00, 0x15, 0x5e, 0xa6, 0x72, 0x73, 0x9d, 0x83, 0x33, 0x95,
	0xfa, 0x41, 0x4d, 0x6f, 0xb6, 0x36, 0x76, 0x2a, 0x7a, 0xeb, 0xcd, 0xfd, 0x8a, 0x5e, 0xad, 0xbf,
	0xbe, 0xb1, 0x5b, 0xdd, 0x2e, 0x3e, 0x83, 0x4a, 0xb0, 0x94, 0x5c, 0x6c, 0x56, 0x77, 0xea, 0x07,
	0xfb, 0x45, 0x05, 0x5d, 0x80, 0xb3, 0xc9, 0xb5, 0x9d, 0xbd, 0xdd, 0x6d, 0x7d, 0x67, 0xa3, 0x56,
	0x29, 0xe6, 0xb2, 0xf8, 0xee, 0xb7, 0xc4, 0x62, 0x1e, 0xa9, 0x70, 0x3e, 0xb9, 0xb8, 0x5d, 0xdd,
	0xa8, 0xed, 0xd5, 0xe5, 0xf6, 0x91, 0x2c, 0xee, 0xf7, 0xab, 0xf5, 0x1d, 0xb1, 0x3c, 0x8a, 0x2e,
	0xc3, 0xc5, 0x14, 0xb0, 0x83, 0xfd, 0x4a, 0x23, 0x42, 0x54, 0x40, 0x67, 0x60, 0x3e, 0x49, 0xf4,
	0x72, 0xb5, 0x5e, 0x1c, 0xbb, 0xfa, 0x0f, 0x05, 0xe6, 0x2a, 0xcd, 0xde, 0x1d, 0x77, 0x20, 0xee,
	0xb8, 0x15, 0x28, 0x09, 0xf2, 0x6a, 0x7d, 0x67, 0x37, 0x50, 0x34, 0xcb, 0x18, 0xb1, 0xf5, 0xda,
	0x46, 0xbd, 0xa8, 0xa0, 0xf3, 0xb0, 0x9c, 0xb1, 0xf6, 0xc6, 0x1e, 0x5b, 0xcd, 0x85, 0xea, 0xc6,
	0x56, 0xf7, 0x1b, 0x95, 0x66, 0xa5, 0xde, 0xaa, 0x34, 0x8a, 0x79, 0x74, 0x09, 0x2e, 0x64, 0xf3,
	0xe6, 0x33, 0x95, 0x46, 0x71, 0x24, 0x54, 0x39, 0x2d, 0x22, 0x20, 0x1a, 0xed, 0x83, 0xa3, 0x79,
	0xbf, 0xba, 0xbb, 0x5b, 0x2c, 0x5c, 0x6d, 0xc3, 0x6c, 0x25, 0x7c, 0x3b, 0xe7, 0x4a, 0x07, 0x1b,
	0x6a, 0x07, 0xad, 0x6a, 0x5a, 0xe5, 0xc0, 0x82, 0xd1, 0xd5, 0x2d, 0x76, 0xf8, 0x67, 0x61, 0x31,
	0xb5, 0xd0, 0xaa, 0x6c, 0xd4, 0x8a, 0xb9, 0xab, 0x5f, 0x28, 0x70, 0x36, 0x22, 0x25, 0xfc, 0x62,
	0xc4, 0xe5, 0x3d, 0x07, 0x97, 0x93, 0x1b, 0x5f, 0x3b, 0xa8, 0xb6, 0xf4, 0x46, 0x65, 0xa3, 0xb9,
	0x57, 0x8f, 0x88, 0x5e, 0x05, 0x75, 0x10, 0x61, 0xb3, 0xb2, 0xfb, 0x72, 0x51, 0x39, 0x8d, 0xea,
	0x7e, 0x75, 0xeb, 0x7e, 0x31, 0x87, 0xd6, 0x60, 0x75, 0x10, 0xd5, 0x76, 0xb5, 0xd9, 0xac, 0x55,
	0x9b, 0xcd, 0x62, 0xfe, 0x34, 0x78, 0x5b, 0xbb, 0x95, 0x0d, 0xe6, 0xfd, 0x23, 0x77, 0x3e, 0x5d,
	0x84, 0xf8, 0x97, 0x68, 0xf4, 0xff, 0x30, 0x15, 0x7d, 0xe7, 0x41, 0xc9, 0x74, 0x9f, 0x78, 0x18,
	0x2a, 0x5d, 0x1c, 0xb8, 0x4e, 0x5d, 0x6d, 0xf6, 0xdd, 0x8f, 0xbe, 0xcc, 0x2b, 0x3f, 0xfa, 0xe8,
	0xcb, 0xfc, 0x33, 0x3f, 0x63, 0x7f, 0xd0, 0x09, 0x7f, 0x67, 0x8b, 0xbf, 0xe5, 0xa3, 0xcb, 0x69,
	0x36, 0xa9, 0x0f, 0x18, 0xa5, 0xd5, 0xd3, 0x89, 0xa8, 0xab, 0x9d, 0x65, 0x02, 0x73, 0x4c, 0x60,
	0xae, 0x5b, 0x66, 0x22, 0xc7, 0xaf, 0x77, 0xd5, 0xf5, 0x2e, 0x31, 0xef, 0xa1, 0xdf, 0xb2, 0xaa,
	0xf2, 0x54, 0xd9, 0xcd, 0x61, 0x64, 0x67, 0x7e, 0x8e, 0xd0, 0xfe, 0x97, 0xc9, 0xce, 0x33, 0xd9,
	0xd0, 0x2d, 0xd3, 0xb2, 0x55, 0xf6, 0xca, 0x4f, 0x39, 0x86, 0x8d, 0x00, 0x83, 0x7a, 0x9d, 0xaa,
	0xeb, 0xa2, 0x3e, 0x53, 0x59, 0xe2, 0xbf, 0xa7, 0x5e, 0xb7, 0xd4, 0x75, 0xab, 0xeb, 0x93, 0x60,
	0xe8, 0xa9, 0xeb, 0xf2, 0x45, 0xee, 0x9e, 0x7a, 0xfd, 0xa9, 0xba, 0x4e, 0xa8, 0xea, 0x1a, 0x27,
	0xf7, 0xd0, 0x8f, 0x15, 0x28, 0x26, 0x5f, 0xf2, 0x51, 0xf2, 0x3d, 0x30, 0xe3, 0xab, 0x41, 0xe9,
	0xf2, 0xa9, 0x34, 0xd4, 0xd5, 0x6e, 0x32, 0xe4, 0x23, 0x0c, 0xf9, 0x48, 0x57, 0x62, 0x3e, 0x1f,
	0x62, 0x7e, 0x2a, 0x21, 0x8a, 0x22, 0x5b, 0x65, 0xb6, 0x7c, 0x5f, 0x81, 0x62, 0xf2, 0xbd, 0x3e,
	0x05, 0x27, 0xe3, 0x03, 0x43, 0x0a, 0x4e, 0xd6, 0xa3, 0xbf, 0xf6, 0x02, 0x83, 0x33, 0x2a, 0xe1,
	0xf8, 0x1c, 0xce, 0x72, 0x08, 0xc7, 0x57, 0xd7, 0xc5, 0x8d, 0xa7, 0xf2, 0x63, 0x7d, 0x07, 0x50,
	0xfa, 0x29, 0x1e, 0x25, 0x4f, 0x2c, 0xf3, 0x75, 0xbf, 0x74, 0x65, 0x08, 0x2a, 0xea, 0x6a, 0x25,
	0x86, 0xa7, 0x10, 0x71, 0xaa, 0x89, 0x1e, 0x1a, 0xf4, 0x53, 0x05, 0x96, 0xfb, 0xbd, 0xbf, 0xa3,
	0xe4, 0x63, 0xd5, 0x80, 0x77, 0xff, 0xd2, 0x0b, 0x43, 0xd3, 0x06, 0x88, 0xc6, 0xb2, 0x11, 0xbd,
	0xa7, 0xc0, 0x7c, 0xc6, 0xf3, 0x3d, 0xba, 0x92, 0x0e, 0xa0, 0x8c, 0xaf, 0x02, 0xa5, 0x67, 0x87,
	0x21, 0xa3, 0xae, 0xb6, 0xc2, 0x20, 0x8c, 0x73, 0x08, 0xc2, 0x63, 0xa6, 0x99, 0xa3, 0xf8, 0xd8,
	0xb0, 0x54, 0x0e, 0xe3, 0x08, 0x66, 0xe2, 0x6f, 0xe7, 0x48, 0x4d, 0x73, 0x8e, 0x3f, 0xb8, 0x97,
	0x2e, 0x9d, 0x42, 0x11, 0x64, 0x94, 0x89, 0x48, 0x46, 0xf9, 0x40, 0x3c, 0xf9, 0xf4, 0x2d, 0xec,
	0xd0, 0x8d, 0x34, 0xd3, 0x41, 0x85, 0x62, 0xe9, 0xe6, 0x57, 0xa2, 0x0f, 0x20, 0x4d, 0x46, 0x20,
	0x7d, 0x28, 0xbe, 0x1b, 0x24, 0x9a, 0x7b, 0xb4, 0xda, 0x97, 0x71, 0xa4, 0xf8, 0x2f, 0x5d, 0x19,
	0x82, 0x8a, 0xba, 0xda, 0x3d, 0x26, 0x74, 0x8a, 0x09, 0x1d, 0xef, 0x96, 0x9d, 0x32, 0x2d, 0x9b,
	0xfc, 0x10, 0x9e, 0x0b, 0xe3, 0xc4, 0x51, 0xd7, 0x45, 0x97, 0x10, 0xa4, 0x9d, 0xef, 0xb2, 0x04,
	0x63, 0xaa, 0xeb, 0x26, 0x4b, 0x28, 0x1f, 0x2b, 0xf2, 0x8b, 0x47, 0xb4, 0x31, 0xcb, 0xca, 0xc4,
	0xa9, 0x46, 0x34, 0x2b, 0x13, 0xa7, 0xfb, 0x3b, 0xed, 0x55, 0x06, 0x70, 0x51, 0x66, 0x43, 0x3f,
	0x02, 0xf1, 0x76, 0x3c, 0x94, 0x45, 0xde, 0x1b, 0x0c, 0xf6, 0xcf, 0x0a, 0xff, 0xca, 0x9e, 0xf9,
	0x2a, 0x9a, 0x0a, 0xb2, 0x01, 0xaf, 0xbc, 0xa9, 0x20, 0x1b, 0xf4, 0xd4, 0xaa, 0xbd, 0xc5, 0x34,
	0xb8, 0xc8, 0x35, 0xf0, 0xcb, 0x66, 0x99, 0x96, 0x9d, 0xb2, 0xcd, 0x35, 0xd8, 0x66, 0xc0, 0x13,
	0xef, 0x91, 0x01, 0x5a, 0xf1, 0xaa, 0x29, 0xb5, 0x60, 0xf7, 0x60, 0x42, 0x39, 0x5b, 0x5d, 0xe7,
	0xcd, 0xd0, 0x3d, 0xf4, 0xfb, 0x3e, 0x4a, 0xf1, 0x60, 0x1d, 0x46, 0xa9, 0x20, 0x62, 0x5f, 0x18,
	0x9a, 0x96, 0xba, 0x5a, 0x8d, 0x29, 0xa5, 0x4a, 0xbf, 0x61, 0x4a, 0x89, 0xfc, 0xfa, 0x62, 0x78,
	0x28, 0x7d, 0xd5, 0xc8, 0x50, 0x1a, 0xfd, 0x20, 0xfc, 0x7f, 0x8f, 0xf8, 0x7f, 0x34, 0xa0, 0xe7,
	0xb2, 0xef, 0xeb, 0xd4, 0x7f, 0x49, 0x94, 0xd6, 0x86, 0x23, 0x0c, 0x2e, 0xf7, 0x4b, 0x99, 0x97,
	0xbb, 0x0f, 0xc5, 0xe4, 0x17, 0x9f, 0xd4, 0x7d, 0x94, 0xf1, 0x49, 0xa8, 0x74, 0xfe, 0x46, 0xef,
	0xbf, 0xee, 0x6e, 0x34, 0xef, 0x6f, 0x8a, 0xff, 0xba, 0xab, 0x58, 0xae, 0x7f, 0xa2, 0xef, 0x6f,
	0x8a, 0x1c, 0xa7, 0x71, 0x81, 0xa6, 0xcc, 0x71, 0x51, 0xeb, 0x04, 0x81, 0x9e, 0x68, 0x9e, 0xb3,
	0x02, 0x3d, 0xdd, 0xe5, 0x67, 0x05, 0x7a, 0x46, 0x17, 0x2e, 0x02, 0xfd, 0xf9, 0xaf, 0x1f, 0xe8,
	0x7f, 0x55, 0xa0, 0x20, 0xfa, 0x36, 0xb4, 0x9c, 0x90, 0xd8, 0xeb, 0x4f, 0x4b, 0x67, 0xfb, 0xac,
	0x50, 0x57, 0xfb, 0x50, 0x61, 0x00, 0xee, 0x30, 0x00, 0x33, 0x2c, 0x90, 0x49, 0xd9, 0x2e, 0x7b,
	0x65, 0x2a, 0xcb, 0x84, 0xef, 0x2b, 0xf1, 0x68, 0xc6, 0xd4, 0xe7, 0xa5, 0x8c, 0x7a, 0xfb, 0x25,
	0xa3, 0xed, 0x5b, 0xf4, 0x58, 0xbd, 0xf3, 0x52, 0x6b, 0x6f, 0xff, 0xae, 0x7a, 0xf7, 0x25, 0xde,
	0x78, 0xba, 0x1e, 0xa6, 0x98, 0x39, 0xd6, 0xff, 0x5d, 0x27, 0xea, 0xba, 0x6c, 0x72, 0x45, 0x58,
	0x44, 0x3a, 0x57, 0x59, 0x08, 0xe1, 0xf6, 0x63, 0x3d, 0x2c, 0x9b, 0x82, 0xbe, 0x55, 0x54, 0x24,
	0x61, 0xa7, 0x7a, 0xef, 0xad, 0x52, 0xe1, 0x7d, 0x06, 0xe5, 0xc9, 0x66, 0xf1, 0x93, 0xcf, 0x57,
	0x94, 0xcf, 0x3e, 0x5f, 0x51, 0xfe, 0xf2, 0xf9, 0x8a, 0xf2, 0xc1, 0x17, 0x2b, 0xcf, 0x1c, 0x16,
	0xf8, 0x3f, 0x4f, 0xde, 0xfd, 0x57, 0x00, 0x00, 0x00, 0xff, 0xff, 0x95, 0x84, 0x74, 0xd7, 0xa3,
	0x29, 0x00, 0x00,
}
