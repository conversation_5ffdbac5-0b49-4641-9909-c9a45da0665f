// Code generated by protoc-gen-go. DO NOT EDIT.
// source: cybros/arbiter/v2/video.proto

package v2 // import "golang.52tt.com/protocol/services/cybros/arbiter/v2"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type VideoBinaryData struct {
	FormatInfo           *VideoBinaryData_VideoFormatInfo `protobuf:"bytes,1,opt,name=format_info,json=formatInfo,proto3" json:"format_info,omitempty"`
	Content              []byte                           `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *VideoBinaryData) Reset()         { *m = VideoBinaryData{} }
func (m *VideoBinaryData) String() string { return proto.CompactTextString(m) }
func (*VideoBinaryData) ProtoMessage()    {}
func (*VideoBinaryData) Descriptor() ([]byte, []int) {
	return fileDescriptor_video_0822c0485f635f5d, []int{0}
}
func (m *VideoBinaryData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VideoBinaryData.Unmarshal(m, b)
}
func (m *VideoBinaryData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VideoBinaryData.Marshal(b, m, deterministic)
}
func (dst *VideoBinaryData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VideoBinaryData.Merge(dst, src)
}
func (m *VideoBinaryData) XXX_Size() int {
	return xxx_messageInfo_VideoBinaryData.Size(m)
}
func (m *VideoBinaryData) XXX_DiscardUnknown() {
	xxx_messageInfo_VideoBinaryData.DiscardUnknown(m)
}

var xxx_messageInfo_VideoBinaryData proto.InternalMessageInfo

func (m *VideoBinaryData) GetFormatInfo() *VideoBinaryData_VideoFormatInfo {
	if m != nil {
		return m.FormatInfo
	}
	return nil
}

func (m *VideoBinaryData) GetContent() []byte {
	if m != nil {
		return m.Content
	}
	return nil
}

type VideoBinaryData_VideoFormatInfo struct {
	Format               string   `protobuf:"bytes,1,opt,name=format,proto3" json:"format,omitempty"`
	SampleRate           uint32   `protobuf:"varint,2,opt,name=sample_rate,json=sampleRate,proto3" json:"sample_rate,omitempty"`
	Track                uint32   `protobuf:"varint,3,opt,name=track,proto3" json:"track,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VideoBinaryData_VideoFormatInfo) Reset()         { *m = VideoBinaryData_VideoFormatInfo{} }
func (m *VideoBinaryData_VideoFormatInfo) String() string { return proto.CompactTextString(m) }
func (*VideoBinaryData_VideoFormatInfo) ProtoMessage()    {}
func (*VideoBinaryData_VideoFormatInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_video_0822c0485f635f5d, []int{0, 0}
}
func (m *VideoBinaryData_VideoFormatInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VideoBinaryData_VideoFormatInfo.Unmarshal(m, b)
}
func (m *VideoBinaryData_VideoFormatInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VideoBinaryData_VideoFormatInfo.Marshal(b, m, deterministic)
}
func (dst *VideoBinaryData_VideoFormatInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VideoBinaryData_VideoFormatInfo.Merge(dst, src)
}
func (m *VideoBinaryData_VideoFormatInfo) XXX_Size() int {
	return xxx_messageInfo_VideoBinaryData_VideoFormatInfo.Size(m)
}
func (m *VideoBinaryData_VideoFormatInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_VideoBinaryData_VideoFormatInfo.DiscardUnknown(m)
}

var xxx_messageInfo_VideoBinaryData_VideoFormatInfo proto.InternalMessageInfo

func (m *VideoBinaryData_VideoFormatInfo) GetFormat() string {
	if m != nil {
		return m.Format
	}
	return ""
}

func (m *VideoBinaryData_VideoFormatInfo) GetSampleRate() uint32 {
	if m != nil {
		return m.SampleRate
	}
	return 0
}

func (m *VideoBinaryData_VideoFormatInfo) GetTrack() uint32 {
	if m != nil {
		return m.Track
	}
	return 0
}

type VideoData struct {
	Metadata *Metadata `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// Types that are valid to be assigned to VideoData:
	//	*VideoData_Url
	//	*VideoData_VideoBin
	VideoData            isVideoData_VideoData `protobuf_oneof:"video_data"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *VideoData) Reset()         { *m = VideoData{} }
func (m *VideoData) String() string { return proto.CompactTextString(m) }
func (*VideoData) ProtoMessage()    {}
func (*VideoData) Descriptor() ([]byte, []int) {
	return fileDescriptor_video_0822c0485f635f5d, []int{1}
}
func (m *VideoData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VideoData.Unmarshal(m, b)
}
func (m *VideoData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VideoData.Marshal(b, m, deterministic)
}
func (dst *VideoData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VideoData.Merge(dst, src)
}
func (m *VideoData) XXX_Size() int {
	return xxx_messageInfo_VideoData.Size(m)
}
func (m *VideoData) XXX_DiscardUnknown() {
	xxx_messageInfo_VideoData.DiscardUnknown(m)
}

var xxx_messageInfo_VideoData proto.InternalMessageInfo

func (m *VideoData) GetMetadata() *Metadata {
	if m != nil {
		return m.Metadata
	}
	return nil
}

type isVideoData_VideoData interface {
	isVideoData_VideoData()
}

type VideoData_Url struct {
	Url string `protobuf:"bytes,2,opt,name=url,proto3,oneof"`
}

type VideoData_VideoBin struct {
	VideoBin *VideoBinaryData `protobuf:"bytes,3,opt,name=video_bin,json=videoBin,proto3,oneof"`
}

func (*VideoData_Url) isVideoData_VideoData() {}

func (*VideoData_VideoBin) isVideoData_VideoData() {}

func (m *VideoData) GetVideoData() isVideoData_VideoData {
	if m != nil {
		return m.VideoData
	}
	return nil
}

func (m *VideoData) GetUrl() string {
	if x, ok := m.GetVideoData().(*VideoData_Url); ok {
		return x.Url
	}
	return ""
}

func (m *VideoData) GetVideoBin() *VideoBinaryData {
	if x, ok := m.GetVideoData().(*VideoData_VideoBin); ok {
		return x.VideoBin
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*VideoData) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _VideoData_OneofMarshaler, _VideoData_OneofUnmarshaler, _VideoData_OneofSizer, []interface{}{
		(*VideoData_Url)(nil),
		(*VideoData_VideoBin)(nil),
	}
}

func _VideoData_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*VideoData)
	// video_data
	switch x := m.VideoData.(type) {
	case *VideoData_Url:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		b.EncodeStringBytes(x.Url)
	case *VideoData_VideoBin:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.VideoBin); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("VideoData.VideoData has unexpected type %T", x)
	}
	return nil
}

func _VideoData_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*VideoData)
	switch tag {
	case 2: // video_data.url
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeStringBytes()
		m.VideoData = &VideoData_Url{x}
		return true, err
	case 3: // video_data.video_bin
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(VideoBinaryData)
		err := b.DecodeMessage(msg)
		m.VideoData = &VideoData_VideoBin{msg}
		return true, err
	default:
		return false, nil
	}
}

func _VideoData_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*VideoData)
	// video_data
	switch x := m.VideoData.(type) {
	case *VideoData_Url:
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(len(x.Url)))
		n += len(x.Url)
	case *VideoData_VideoBin:
		s := proto.Size(x.VideoBin)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type ScanVideoReq struct {
	Context              *TaskContext `protobuf:"bytes,1,opt,name=context,proto3" json:"context,omitempty"`
	VideoData            *VideoData   `protobuf:"bytes,2,opt,name=video_data,json=videoData,proto3" json:"video_data,omitempty"`
	Callback             *Callback    `protobuf:"bytes,3,opt,name=callback,proto3" json:"callback,omitempty"`
	TextData             *TextData    `protobuf:"bytes,4,opt,name=text_data,json=textData,proto3" json:"text_data,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ScanVideoReq) Reset()         { *m = ScanVideoReq{} }
func (m *ScanVideoReq) String() string { return proto.CompactTextString(m) }
func (*ScanVideoReq) ProtoMessage()    {}
func (*ScanVideoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_video_0822c0485f635f5d, []int{2}
}
func (m *ScanVideoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScanVideoReq.Unmarshal(m, b)
}
func (m *ScanVideoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScanVideoReq.Marshal(b, m, deterministic)
}
func (dst *ScanVideoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScanVideoReq.Merge(dst, src)
}
func (m *ScanVideoReq) XXX_Size() int {
	return xxx_messageInfo_ScanVideoReq.Size(m)
}
func (m *ScanVideoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ScanVideoReq.DiscardUnknown(m)
}

var xxx_messageInfo_ScanVideoReq proto.InternalMessageInfo

func (m *ScanVideoReq) GetContext() *TaskContext {
	if m != nil {
		return m.Context
	}
	return nil
}

func (m *ScanVideoReq) GetVideoData() *VideoData {
	if m != nil {
		return m.VideoData
	}
	return nil
}

func (m *ScanVideoReq) GetCallback() *Callback {
	if m != nil {
		return m.Callback
	}
	return nil
}

func (m *ScanVideoReq) GetTextData() *TextData {
	if m != nil {
		return m.TextData
	}
	return nil
}

type ScanVideoResp struct {
	TaskId               string   `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ScanVideoResp) Reset()         { *m = ScanVideoResp{} }
func (m *ScanVideoResp) String() string { return proto.CompactTextString(m) }
func (*ScanVideoResp) ProtoMessage()    {}
func (*ScanVideoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_video_0822c0485f635f5d, []int{3}
}
func (m *ScanVideoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScanVideoResp.Unmarshal(m, b)
}
func (m *ScanVideoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScanVideoResp.Marshal(b, m, deterministic)
}
func (dst *ScanVideoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScanVideoResp.Merge(dst, src)
}
func (m *ScanVideoResp) XXX_Size() int {
	return xxx_messageInfo_ScanVideoResp.Size(m)
}
func (m *ScanVideoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ScanVideoResp.DiscardUnknown(m)
}

var xxx_messageInfo_ScanVideoResp proto.InternalMessageInfo

func (m *ScanVideoResp) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

type QueryVideoTaskResultReq struct {
	TaskId               string   `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryVideoTaskResultReq) Reset()         { *m = QueryVideoTaskResultReq{} }
func (m *QueryVideoTaskResultReq) String() string { return proto.CompactTextString(m) }
func (*QueryVideoTaskResultReq) ProtoMessage()    {}
func (*QueryVideoTaskResultReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_video_0822c0485f635f5d, []int{4}
}
func (m *QueryVideoTaskResultReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryVideoTaskResultReq.Unmarshal(m, b)
}
func (m *QueryVideoTaskResultReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryVideoTaskResultReq.Marshal(b, m, deterministic)
}
func (dst *QueryVideoTaskResultReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryVideoTaskResultReq.Merge(dst, src)
}
func (m *QueryVideoTaskResultReq) XXX_Size() int {
	return xxx_messageInfo_QueryVideoTaskResultReq.Size(m)
}
func (m *QueryVideoTaskResultReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryVideoTaskResultReq.DiscardUnknown(m)
}

var xxx_messageInfo_QueryVideoTaskResultReq proto.InternalMessageInfo

func (m *QueryVideoTaskResultReq) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

type QueryVideoTaskResultResp struct {
	TaskStatus           TaskStatus `protobuf:"varint,1,opt,name=task_status,json=taskStatus,proto3,enum=cybros.arbiter.v2.TaskStatus" json:"task_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *QueryVideoTaskResultResp) Reset()         { *m = QueryVideoTaskResultResp{} }
func (m *QueryVideoTaskResultResp) String() string { return proto.CompactTextString(m) }
func (*QueryVideoTaskResultResp) ProtoMessage()    {}
func (*QueryVideoTaskResultResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_video_0822c0485f635f5d, []int{5}
}
func (m *QueryVideoTaskResultResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryVideoTaskResultResp.Unmarshal(m, b)
}
func (m *QueryVideoTaskResultResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryVideoTaskResultResp.Marshal(b, m, deterministic)
}
func (dst *QueryVideoTaskResultResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryVideoTaskResultResp.Merge(dst, src)
}
func (m *QueryVideoTaskResultResp) XXX_Size() int {
	return xxx_messageInfo_QueryVideoTaskResultResp.Size(m)
}
func (m *QueryVideoTaskResultResp) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryVideoTaskResultResp.DiscardUnknown(m)
}

var xxx_messageInfo_QueryVideoTaskResultResp proto.InternalMessageInfo

func (m *QueryVideoTaskResultResp) GetTaskStatus() TaskStatus {
	if m != nil {
		return m.TaskStatus
	}
	return TaskStatus_SUCCESS
}

func init() {
	proto.RegisterType((*VideoBinaryData)(nil), "cybros.arbiter.v2.VideoBinaryData")
	proto.RegisterType((*VideoBinaryData_VideoFormatInfo)(nil), "cybros.arbiter.v2.VideoBinaryData.VideoFormatInfo")
	proto.RegisterType((*VideoData)(nil), "cybros.arbiter.v2.VideoData")
	proto.RegisterType((*ScanVideoReq)(nil), "cybros.arbiter.v2.ScanVideoReq")
	proto.RegisterType((*ScanVideoResp)(nil), "cybros.arbiter.v2.ScanVideoResp")
	proto.RegisterType((*QueryVideoTaskResultReq)(nil), "cybros.arbiter.v2.QueryVideoTaskResultReq")
	proto.RegisterType((*QueryVideoTaskResultResp)(nil), "cybros.arbiter.v2.QueryVideoTaskResultResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// VideoClient is the client API for Video service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type VideoClient interface {
	AsyncScanVideo(ctx context.Context, in *ScanVideoReq, opts ...grpc.CallOption) (*ScanVideoResp, error)
	QueryVideoTaskResult(ctx context.Context, in *QueryVideoTaskResultReq, opts ...grpc.CallOption) (*QueryVideoTaskResultResp, error)
}

type videoClient struct {
	cc *grpc.ClientConn
}

func NewVideoClient(cc *grpc.ClientConn) VideoClient {
	return &videoClient{cc}
}

func (c *videoClient) AsyncScanVideo(ctx context.Context, in *ScanVideoReq, opts ...grpc.CallOption) (*ScanVideoResp, error) {
	out := new(ScanVideoResp)
	err := c.cc.Invoke(ctx, "/cybros.arbiter.v2.Video/AsyncScanVideo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoClient) QueryVideoTaskResult(ctx context.Context, in *QueryVideoTaskResultReq, opts ...grpc.CallOption) (*QueryVideoTaskResultResp, error) {
	out := new(QueryVideoTaskResultResp)
	err := c.cc.Invoke(ctx, "/cybros.arbiter.v2.Video/QueryVideoTaskResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VideoServer is the server API for Video service.
type VideoServer interface {
	AsyncScanVideo(context.Context, *ScanVideoReq) (*ScanVideoResp, error)
	QueryVideoTaskResult(context.Context, *QueryVideoTaskResultReq) (*QueryVideoTaskResultResp, error)
}

func RegisterVideoServer(s *grpc.Server, srv VideoServer) {
	s.RegisterService(&_Video_serviceDesc, srv)
}

func _Video_AsyncScanVideo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScanVideoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoServer).AsyncScanVideo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cybros.arbiter.v2.Video/AsyncScanVideo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoServer).AsyncScanVideo(ctx, req.(*ScanVideoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Video_QueryVideoTaskResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryVideoTaskResultReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoServer).QueryVideoTaskResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cybros.arbiter.v2.Video/QueryVideoTaskResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoServer).QueryVideoTaskResult(ctx, req.(*QueryVideoTaskResultReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Video_serviceDesc = grpc.ServiceDesc{
	ServiceName: "cybros.arbiter.v2.Video",
	HandlerType: (*VideoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AsyncScanVideo",
			Handler:    _Video_AsyncScanVideo_Handler,
		},
		{
			MethodName: "QueryVideoTaskResult",
			Handler:    _Video_QueryVideoTaskResult_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "cybros/arbiter/v2/video.proto",
}

func init() {
	proto.RegisterFile("cybros/arbiter/v2/video.proto", fileDescriptor_video_0822c0485f635f5d)
}

var fileDescriptor_video_0822c0485f635f5d = []byte{
	// 542 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x54, 0x5d, 0x6f, 0xd3, 0x30,
	0x14, 0x5d, 0x18, 0xdb, 0xda, 0x9b, 0x6e, 0x08, 0x6b, 0x62, 0x55, 0xd9, 0x47, 0x95, 0xa7, 0x0a,
	0xa4, 0x44, 0xca, 0x34, 0x31, 0x09, 0x09, 0x69, 0x1d, 0x42, 0xdb, 0x03, 0x0f, 0xb8, 0x13, 0x0f,
	0x7b, 0x29, 0x8e, 0xeb, 0x56, 0x51, 0xd3, 0x38, 0xd8, 0xb7, 0x55, 0xfb, 0x93, 0xf8, 0x31, 0xfc,
	0x09, 0x7e, 0x05, 0x8f, 0x28, 0xb6, 0x5b, 0x2a, 0x9a, 0x02, 0x6f, 0xbe, 0xb9, 0xe7, 0xdc, 0x73,
	0xce, 0x8d, 0x65, 0x38, 0xe3, 0x8b, 0x44, 0x49, 0x1d, 0x31, 0x95, 0xa4, 0x28, 0x54, 0x34, 0x8b,
	0xa3, 0x59, 0x3a, 0x10, 0x32, 0x2c, 0x94, 0x44, 0x49, 0x9e, 0xdb, 0x76, 0xe8, 0xda, 0xe1, 0x2c,
	0x6e, 0x9d, 0x6f, 0x32, 0x46, 0x99, 0x4c, 0x58, 0x66, 0x29, 0xad, 0xf6, 0x66, 0x9f, 0xb3, 0x2c,
	0x4b, 0x18, 0x1f, 0x3b, 0xc4, 0xe9, 0x26, 0x02, 0xc5, 0x1c, 0x6d, 0x37, 0xf8, 0xe1, 0xc1, 0xb3,
	0xcf, 0xa5, 0x85, 0x6e, 0x9a, 0x33, 0xb5, 0x78, 0xcf, 0x90, 0x91, 0x1e, 0xf8, 0x43, 0xa9, 0x26,
	0x0c, 0xfb, 0x69, 0x3e, 0x94, 0x4d, 0xaf, 0xed, 0x75, 0xfc, 0x38, 0x0e, 0x37, 0xcc, 0x85, 0x7f,
	0x10, 0x6d, 0xfd, 0xc1, 0x50, 0xef, 0xf3, 0xa1, 0xa4, 0x30, 0x5c, 0x9d, 0x49, 0x13, 0x0e, 0xb8,
	0xcc, 0x51, 0xe4, 0xd8, 0x7c, 0xd2, 0xf6, 0x3a, 0x0d, 0xba, 0x2c, 0x5b, 0x5f, 0x9c, 0x83, 0xdf,
	0x44, 0xf2, 0x02, 0xf6, 0x2d, 0xd5, 0x88, 0xd7, 0xa9, 0xab, 0xc8, 0x05, 0xf8, 0x9a, 0x4d, 0x8a,
	0x4c, 0xf4, 0x15, 0x43, 0x61, 0x06, 0x1d, 0x52, 0xb0, 0x9f, 0x28, 0x43, 0x41, 0x8e, 0x61, 0x0f,
	0x15, 0xe3, 0xe3, 0xe6, 0xae, 0x69, 0xd9, 0x22, 0xf8, 0xe6, 0x41, 0xdd, 0x48, 0x98, 0x78, 0x6f,
	0xa0, 0x36, 0x11, 0xc8, 0x06, 0x0c, 0x99, 0xcb, 0xf6, 0xb2, 0x22, 0xdb, 0x47, 0x07, 0xa1, 0x2b,
	0x30, 0x21, 0xb0, 0x3b, 0x55, 0x99, 0x51, 0xad, 0xdf, 0xed, 0xd0, 0xb2, 0x20, 0x37, 0x50, 0x37,
	0x7f, 0xb0, 0x9f, 0xa4, 0xb9, 0x11, 0xf5, 0xe3, 0xe0, 0xdf, 0x9b, 0xba, 0xdb, 0xa1, 0xb5, 0x99,
	0xfb, 0xd4, 0x6d, 0x00, 0xd8, 0x11, 0xa5, 0x48, 0xf0, 0xd3, 0x83, 0x46, 0x8f, 0xb3, 0xdc, 0x30,
	0xa8, 0xf8, 0x4a, 0xae, 0xdd, 0xe2, 0xe6, 0xe8, 0xdc, 0x9e, 0x57, 0xcc, 0x7f, 0x60, 0x7a, 0x7c,
	0x6b, 0x51, 0x74, 0x09, 0x27, 0x6f, 0xd7, 0x07, 0x1b, 0xdb, 0x7e, 0x7c, 0xba, 0xcd, 0x5c, 0x69,
	0x8b, 0xda, 0x2c, 0xcb, 0x2d, 0x2d, 0x2f, 0x92, 0xcb, 0x55, 0xb5, 0xa5, 0x5b, 0x07, 0xa1, 0x2b,
	0x30, 0xb9, 0x86, 0x7a, 0xa9, 0x6e, 0x45, 0x9f, 0x6e, 0x65, 0x3e, 0x88, 0x39, 0x1a, 0xcd, 0x1a,
	0xba, 0x53, 0xd0, 0x81, 0xc3, 0xb5, 0xe4, 0xba, 0x20, 0x27, 0x70, 0x80, 0x4c, 0x8f, 0xfb, 0xe9,
	0x60, 0x79, 0x0f, 0xca, 0xf2, 0x7e, 0x10, 0xc4, 0x70, 0xf2, 0x69, 0x2a, 0xd4, 0xc2, 0x40, 0xcb,
	0xec, 0x54, 0xe8, 0x69, 0x86, 0xe5, 0xba, 0xb6, 0x72, 0x1e, 0xa1, 0x59, 0xcd, 0xd1, 0x05, 0x79,
	0x07, 0xbe, 0x21, 0x69, 0x64, 0x38, 0xd5, 0x86, 0x78, 0x14, 0x9f, 0x6d, 0xd9, 0x73, 0xcf, 0x80,
	0x28, 0xe0, 0xea, 0x1c, 0x7f, 0xf7, 0x60, 0xcf, 0xcc, 0x25, 0x3d, 0x38, 0xba, 0xd1, 0x8b, 0x9c,
	0xaf, 0x82, 0x90, 0x8b, 0x8a, 0x31, 0xeb, 0x3f, 0xb8, 0xd5, 0xfe, 0x3b, 0x40, 0x17, 0x44, 0xc2,
	0x71, 0x95, 0x75, 0xf2, 0xaa, 0x82, 0xb9, 0x65, 0x2f, 0xad, 0xd7, 0xff, 0x8d, 0xd5, 0x45, 0xf7,
	0xea, 0xf1, 0x72, 0x24, 0x33, 0x96, 0x8f, 0xc2, 0xab, 0x18, 0x31, 0xe4, 0x72, 0x12, 0x99, 0xe7,
	0x82, 0xcb, 0x2c, 0xd2, 0x42, 0xcd, 0x52, 0x2e, 0x74, 0xb4, 0xf1, 0xae, 0x24, 0xfb, 0x06, 0x74,
	0xf9, 0x2b, 0x00, 0x00, 0xff, 0xff, 0x39, 0x4b, 0x23, 0x5b, 0xe7, 0x04, 0x00, 0x00,
}
