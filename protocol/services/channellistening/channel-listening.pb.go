// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-listening/channel-listening.proto

package channellistening // import "golang.52tt.com/protocol/services/channellistening"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 歌单类型
type SongType int32

const (
	SongType_OldSongSheetType SongType = 0
	SongType_NewSongSheetType SongType = 1
)

var SongType_name = map[int32]string{
	0: "OldSongSheetType",
	1: "NewSongSheetType",
}
var SongType_value = map[string]int32{
	"OldSongSheetType": 0,
	"NewSongSheetType": 1,
}

func (x SongType) String() string {
	return proto.EnumName(SongType_name, int32(x))
}
func (SongType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{0}
}

type Constellation int32

const (
	Constellation_undefined   Constellation = 0
	Constellation_aries       Constellation = 1
	Constellation_taurus      Constellation = 2
	Constellation_gemini      Constellation = 3
	Constellation_cancer      Constellation = 4
	Constellation_leo         Constellation = 5
	Constellation_virgo       Constellation = 6
	Constellation_libra       Constellation = 7
	Constellation_scorpio     Constellation = 8
	Constellation_sagittarius Constellation = 9
	Constellation_capricorn   Constellation = 10
	Constellation_aquarius    Constellation = 11
	Constellation_pisces      Constellation = 12
)

var Constellation_name = map[int32]string{
	0:  "undefined",
	1:  "aries",
	2:  "taurus",
	3:  "gemini",
	4:  "cancer",
	5:  "leo",
	6:  "virgo",
	7:  "libra",
	8:  "scorpio",
	9:  "sagittarius",
	10: "capricorn",
	11: "aquarius",
	12: "pisces",
}
var Constellation_value = map[string]int32{
	"undefined":   0,
	"aries":       1,
	"taurus":      2,
	"gemini":      3,
	"cancer":      4,
	"leo":         5,
	"virgo":       6,
	"libra":       7,
	"scorpio":     8,
	"sagittarius": 9,
	"capricorn":   10,
	"aquarius":    11,
	"pisces":      12,
}

func (x Constellation) String() string {
	return proto.EnumName(Constellation_name, int32(x))
}
func (Constellation) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{1}
}

type ChannelListeningCheckInType int32

const (
	ChannelListeningCheckInType_ChannelListeningCheckInType_UNDEFINED   ChannelListeningCheckInType = 0
	ChannelListeningCheckInType_ChannelListeningCheckInType_SUCCESS     ChannelListeningCheckInType = 1
	ChannelListeningCheckInType_ChannelListeningCheckInType_GET_TITLE   ChannelListeningCheckInType = 2
	ChannelListeningCheckInType_ChannelListeningCheckInType_RENEW_TITLE ChannelListeningCheckInType = 3
)

var ChannelListeningCheckInType_name = map[int32]string{
	0: "ChannelListeningCheckInType_UNDEFINED",
	1: "ChannelListeningCheckInType_SUCCESS",
	2: "ChannelListeningCheckInType_GET_TITLE",
	3: "ChannelListeningCheckInType_RENEW_TITLE",
}
var ChannelListeningCheckInType_value = map[string]int32{
	"ChannelListeningCheckInType_UNDEFINED":   0,
	"ChannelListeningCheckInType_SUCCESS":     1,
	"ChannelListeningCheckInType_GET_TITLE":   2,
	"ChannelListeningCheckInType_RENEW_TITLE": 3,
}

func (x ChannelListeningCheckInType) String() string {
	return proto.EnumName(ChannelListeningCheckInType_name, int32(x))
}
func (ChannelListeningCheckInType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{2}
}

type ListeningDropCardV3Req struct {
	Uid                  uint32        `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32        `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Constellation        Constellation `protobuf:"varint,3,opt,name=constellation,proto3,enum=channellistening.Constellation" json:"constellation,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ListeningDropCardV3Req) Reset()         { *m = ListeningDropCardV3Req{} }
func (m *ListeningDropCardV3Req) String() string { return proto.CompactTextString(m) }
func (*ListeningDropCardV3Req) ProtoMessage()    {}
func (*ListeningDropCardV3Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{0}
}
func (m *ListeningDropCardV3Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningDropCardV3Req.Unmarshal(m, b)
}
func (m *ListeningDropCardV3Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningDropCardV3Req.Marshal(b, m, deterministic)
}
func (dst *ListeningDropCardV3Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningDropCardV3Req.Merge(dst, src)
}
func (m *ListeningDropCardV3Req) XXX_Size() int {
	return xxx_messageInfo_ListeningDropCardV3Req.Size(m)
}
func (m *ListeningDropCardV3Req) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningDropCardV3Req.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningDropCardV3Req proto.InternalMessageInfo

func (m *ListeningDropCardV3Req) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ListeningDropCardV3Req) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ListeningDropCardV3Req) GetConstellation() Constellation {
	if m != nil {
		return m.Constellation
	}
	return Constellation_undefined
}

type ListeningDropCardV3Resp struct {
	ShareCard            *ChannelListeningShareCardV3 `protobuf:"bytes,1,opt,name=share_card,json=shareCard,proto3" json:"share_card,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *ListeningDropCardV3Resp) Reset()         { *m = ListeningDropCardV3Resp{} }
func (m *ListeningDropCardV3Resp) String() string { return proto.CompactTextString(m) }
func (*ListeningDropCardV3Resp) ProtoMessage()    {}
func (*ListeningDropCardV3Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{1}
}
func (m *ListeningDropCardV3Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningDropCardV3Resp.Unmarshal(m, b)
}
func (m *ListeningDropCardV3Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningDropCardV3Resp.Marshal(b, m, deterministic)
}
func (dst *ListeningDropCardV3Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningDropCardV3Resp.Merge(dst, src)
}
func (m *ListeningDropCardV3Resp) XXX_Size() int {
	return xxx_messageInfo_ListeningDropCardV3Resp.Size(m)
}
func (m *ListeningDropCardV3Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningDropCardV3Resp.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningDropCardV3Resp proto.InternalMessageInfo

func (m *ListeningDropCardV3Resp) GetShareCard() *ChannelListeningShareCardV3 {
	if m != nil {
		return m.ShareCard
	}
	return nil
}

// 喜欢某首歌
type LikeSongReq struct {
	Uid                  uint32                `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32                `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Song                 *ChannelListeningSong `protobuf:"bytes,3,opt,name=song,proto3" json:"song,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *LikeSongReq) Reset()         { *m = LikeSongReq{} }
func (m *LikeSongReq) String() string { return proto.CompactTextString(m) }
func (*LikeSongReq) ProtoMessage()    {}
func (*LikeSongReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{2}
}
func (m *LikeSongReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LikeSongReq.Unmarshal(m, b)
}
func (m *LikeSongReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LikeSongReq.Marshal(b, m, deterministic)
}
func (dst *LikeSongReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LikeSongReq.Merge(dst, src)
}
func (m *LikeSongReq) XXX_Size() int {
	return xxx_messageInfo_LikeSongReq.Size(m)
}
func (m *LikeSongReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LikeSongReq.DiscardUnknown(m)
}

var xxx_messageInfo_LikeSongReq proto.InternalMessageInfo

func (m *LikeSongReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *LikeSongReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *LikeSongReq) GetSong() *ChannelListeningSong {
	if m != nil {
		return m.Song
	}
	return nil
}

type LikeSongResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LikeSongResp) Reset()         { *m = LikeSongResp{} }
func (m *LikeSongResp) String() string { return proto.CompactTextString(m) }
func (*LikeSongResp) ProtoMessage()    {}
func (*LikeSongResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{3}
}
func (m *LikeSongResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LikeSongResp.Unmarshal(m, b)
}
func (m *LikeSongResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LikeSongResp.Marshal(b, m, deterministic)
}
func (dst *LikeSongResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LikeSongResp.Merge(dst, src)
}
func (m *LikeSongResp) XXX_Size() int {
	return xxx_messageInfo_LikeSongResp.Size(m)
}
func (m *LikeSongResp) XXX_DiscardUnknown() {
	xxx_messageInfo_LikeSongResp.DiscardUnknown(m)
}

var xxx_messageInfo_LikeSongResp proto.InternalMessageInfo

type ChannelListeningSong struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelListeningSong) Reset()         { *m = ChannelListeningSong{} }
func (m *ChannelListeningSong) String() string { return proto.CompactTextString(m) }
func (*ChannelListeningSong) ProtoMessage()    {}
func (*ChannelListeningSong) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{4}
}
func (m *ChannelListeningSong) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelListeningSong.Unmarshal(m, b)
}
func (m *ChannelListeningSong) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelListeningSong.Marshal(b, m, deterministic)
}
func (dst *ChannelListeningSong) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelListeningSong.Merge(dst, src)
}
func (m *ChannelListeningSong) XXX_Size() int {
	return xxx_messageInfo_ChannelListeningSong.Size(m)
}
func (m *ChannelListeningSong) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelListeningSong.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelListeningSong proto.InternalMessageInfo

func (m *ChannelListeningSong) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ChannelListeningSong) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

// 被喜欢歌曲列表
type GetBeLikedSongListReq struct {
	ChannelId            uint32                    `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Count                uint32                    `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	LoadMore             *ChannelListeningLoadMore `protobuf:"bytes,3,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetBeLikedSongListReq) Reset()         { *m = GetBeLikedSongListReq{} }
func (m *GetBeLikedSongListReq) String() string { return proto.CompactTextString(m) }
func (*GetBeLikedSongListReq) ProtoMessage()    {}
func (*GetBeLikedSongListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{5}
}
func (m *GetBeLikedSongListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBeLikedSongListReq.Unmarshal(m, b)
}
func (m *GetBeLikedSongListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBeLikedSongListReq.Marshal(b, m, deterministic)
}
func (dst *GetBeLikedSongListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBeLikedSongListReq.Merge(dst, src)
}
func (m *GetBeLikedSongListReq) XXX_Size() int {
	return xxx_messageInfo_GetBeLikedSongListReq.Size(m)
}
func (m *GetBeLikedSongListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBeLikedSongListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBeLikedSongListReq proto.InternalMessageInfo

func (m *GetBeLikedSongListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetBeLikedSongListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetBeLikedSongListReq) GetLoadMore() *ChannelListeningLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type GetBeLikedSongListResp struct {
	Songs                []*BeLikedSong            `protobuf:"bytes,1,rep,name=songs,proto3" json:"songs,omitempty"`
	LoadMore             *ChannelListeningLoadMore `protobuf:"bytes,2,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetBeLikedSongListResp) Reset()         { *m = GetBeLikedSongListResp{} }
func (m *GetBeLikedSongListResp) String() string { return proto.CompactTextString(m) }
func (*GetBeLikedSongListResp) ProtoMessage()    {}
func (*GetBeLikedSongListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{6}
}
func (m *GetBeLikedSongListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBeLikedSongListResp.Unmarshal(m, b)
}
func (m *GetBeLikedSongListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBeLikedSongListResp.Marshal(b, m, deterministic)
}
func (dst *GetBeLikedSongListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBeLikedSongListResp.Merge(dst, src)
}
func (m *GetBeLikedSongListResp) XXX_Size() int {
	return xxx_messageInfo_GetBeLikedSongListResp.Size(m)
}
func (m *GetBeLikedSongListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBeLikedSongListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBeLikedSongListResp proto.InternalMessageInfo

func (m *GetBeLikedSongListResp) GetSongs() []*BeLikedSong {
	if m != nil {
		return m.Songs
	}
	return nil
}

func (m *GetBeLikedSongListResp) GetLoadMore() *ChannelListeningLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type BeLikedSong struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Sex                  uint32   `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	SongName             string   `protobuf:"bytes,4,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BeLikedSong) Reset()         { *m = BeLikedSong{} }
func (m *BeLikedSong) String() string { return proto.CompactTextString(m) }
func (*BeLikedSong) ProtoMessage()    {}
func (*BeLikedSong) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{7}
}
func (m *BeLikedSong) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BeLikedSong.Unmarshal(m, b)
}
func (m *BeLikedSong) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BeLikedSong.Marshal(b, m, deterministic)
}
func (dst *BeLikedSong) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BeLikedSong.Merge(dst, src)
}
func (m *BeLikedSong) XXX_Size() int {
	return xxx_messageInfo_BeLikedSong.Size(m)
}
func (m *BeLikedSong) XXX_DiscardUnknown() {
	xxx_messageInfo_BeLikedSong.DiscardUnknown(m)
}

var xxx_messageInfo_BeLikedSong proto.InternalMessageInfo

func (m *BeLikedSong) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *BeLikedSong) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *BeLikedSong) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *BeLikedSong) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

// 喜欢的歌曲列表
type GetLikedSongListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLikedSongListReq) Reset()         { *m = GetLikedSongListReq{} }
func (m *GetLikedSongListReq) String() string { return proto.CompactTextString(m) }
func (*GetLikedSongListReq) ProtoMessage()    {}
func (*GetLikedSongListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{8}
}
func (m *GetLikedSongListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLikedSongListReq.Unmarshal(m, b)
}
func (m *GetLikedSongListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLikedSongListReq.Marshal(b, m, deterministic)
}
func (dst *GetLikedSongListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLikedSongListReq.Merge(dst, src)
}
func (m *GetLikedSongListReq) XXX_Size() int {
	return xxx_messageInfo_GetLikedSongListReq.Size(m)
}
func (m *GetLikedSongListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLikedSongListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLikedSongListReq proto.InternalMessageInfo

func (m *GetLikedSongListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetLikedSongListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetLikedSongListResp struct {
	SongList             []string `protobuf:"bytes,1,rep,name=song_list,json=songList,proto3" json:"song_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLikedSongListResp) Reset()         { *m = GetLikedSongListResp{} }
func (m *GetLikedSongListResp) String() string { return proto.CompactTextString(m) }
func (*GetLikedSongListResp) ProtoMessage()    {}
func (*GetLikedSongListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{9}
}
func (m *GetLikedSongListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLikedSongListResp.Unmarshal(m, b)
}
func (m *GetLikedSongListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLikedSongListResp.Marshal(b, m, deterministic)
}
func (dst *GetLikedSongListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLikedSongListResp.Merge(dst, src)
}
func (m *GetLikedSongListResp) XXX_Size() int {
	return xxx_messageInfo_GetLikedSongListResp.Size(m)
}
func (m *GetLikedSongListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLikedSongListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLikedSongListResp proto.InternalMessageInfo

func (m *GetLikedSongListResp) GetSongList() []string {
	if m != nil {
		return m.SongList
	}
	return nil
}

type ChannelListeningLoadMore struct {
	LastPage             uint32   `protobuf:"varint,1,opt,name=last_page,json=lastPage,proto3" json:"last_page,omitempty"`
	LastCount            uint32   `protobuf:"varint,2,opt,name=last_count,json=lastCount,proto3" json:"last_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelListeningLoadMore) Reset()         { *m = ChannelListeningLoadMore{} }
func (m *ChannelListeningLoadMore) String() string { return proto.CompactTextString(m) }
func (*ChannelListeningLoadMore) ProtoMessage()    {}
func (*ChannelListeningLoadMore) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{10}
}
func (m *ChannelListeningLoadMore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelListeningLoadMore.Unmarshal(m, b)
}
func (m *ChannelListeningLoadMore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelListeningLoadMore.Marshal(b, m, deterministic)
}
func (dst *ChannelListeningLoadMore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelListeningLoadMore.Merge(dst, src)
}
func (m *ChannelListeningLoadMore) XXX_Size() int {
	return xxx_messageInfo_ChannelListeningLoadMore.Size(m)
}
func (m *ChannelListeningLoadMore) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelListeningLoadMore.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelListeningLoadMore proto.InternalMessageInfo

func (m *ChannelListeningLoadMore) GetLastPage() uint32 {
	if m != nil {
		return m.LastPage
	}
	return 0
}

func (m *ChannelListeningLoadMore) GetLastCount() uint32 {
	if m != nil {
		return m.LastCount
	}
	return 0
}

// 挂房听歌简要信息
type GetChannelListeningSimpleInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelListeningSimpleInfoReq) Reset()         { *m = GetChannelListeningSimpleInfoReq{} }
func (m *GetChannelListeningSimpleInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelListeningSimpleInfoReq) ProtoMessage()    {}
func (*GetChannelListeningSimpleInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{11}
}
func (m *GetChannelListeningSimpleInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelListeningSimpleInfoReq.Unmarshal(m, b)
}
func (m *GetChannelListeningSimpleInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelListeningSimpleInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelListeningSimpleInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelListeningSimpleInfoReq.Merge(dst, src)
}
func (m *GetChannelListeningSimpleInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelListeningSimpleInfoReq.Size(m)
}
func (m *GetChannelListeningSimpleInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelListeningSimpleInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelListeningSimpleInfoReq proto.InternalMessageInfo

func (m *GetChannelListeningSimpleInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetChannelListeningSimpleInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelListeningSimpleInfoResp struct {
	LikeUserAccounts     []string                     `protobuf:"bytes,1,rep,name=like_user_accounts,json=likeUserAccounts,proto3" json:"like_user_accounts,omitempty"`
	LikeUserCount        uint32                       `protobuf:"varint,2,opt,name=like_user_count,json=likeUserCount,proto3" json:"like_user_count,omitempty"`
	TodayFlowerCount     uint32                       `protobuf:"varint,3,opt,name=today_flower_count,json=todayFlowerCount,proto3" json:"today_flower_count,omitempty"`
	EnteredAt            uint32                       `protobuf:"varint,4,opt,name=entered_at,json=enteredAt,proto3" json:"entered_at,omitempty"`
	Theme                *ChannelListenTheme          `protobuf:"bytes,5,opt,name=theme,proto3" json:"theme,omitempty"`
	RuleUrl              string                       `protobuf:"bytes,6,opt,name=rule_url,json=ruleUrl,proto3" json:"rule_url,omitempty"`
	CheckInTopic         string                       `protobuf:"bytes,7,opt,name=check_in_topic,json=checkInTopic,proto3" json:"check_in_topic,omitempty"`
	CheckInInfo          *ChannelListeningCheckInInfo `protobuf:"bytes,8,opt,name=check_in_info,json=checkInInfo,proto3" json:"check_in_info,omitempty"`
	HasUserSetMood       bool                         `protobuf:"varint,9,opt,name=has_user_set_mood,json=hasUserSetMood,proto3" json:"has_user_set_mood,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *GetChannelListeningSimpleInfoResp) Reset()         { *m = GetChannelListeningSimpleInfoResp{} }
func (m *GetChannelListeningSimpleInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelListeningSimpleInfoResp) ProtoMessage()    {}
func (*GetChannelListeningSimpleInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{12}
}
func (m *GetChannelListeningSimpleInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelListeningSimpleInfoResp.Unmarshal(m, b)
}
func (m *GetChannelListeningSimpleInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelListeningSimpleInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelListeningSimpleInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelListeningSimpleInfoResp.Merge(dst, src)
}
func (m *GetChannelListeningSimpleInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelListeningSimpleInfoResp.Size(m)
}
func (m *GetChannelListeningSimpleInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelListeningSimpleInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelListeningSimpleInfoResp proto.InternalMessageInfo

func (m *GetChannelListeningSimpleInfoResp) GetLikeUserAccounts() []string {
	if m != nil {
		return m.LikeUserAccounts
	}
	return nil
}

func (m *GetChannelListeningSimpleInfoResp) GetLikeUserCount() uint32 {
	if m != nil {
		return m.LikeUserCount
	}
	return 0
}

func (m *GetChannelListeningSimpleInfoResp) GetTodayFlowerCount() uint32 {
	if m != nil {
		return m.TodayFlowerCount
	}
	return 0
}

func (m *GetChannelListeningSimpleInfoResp) GetEnteredAt() uint32 {
	if m != nil {
		return m.EnteredAt
	}
	return 0
}

func (m *GetChannelListeningSimpleInfoResp) GetTheme() *ChannelListenTheme {
	if m != nil {
		return m.Theme
	}
	return nil
}

func (m *GetChannelListeningSimpleInfoResp) GetRuleUrl() string {
	if m != nil {
		return m.RuleUrl
	}
	return ""
}

func (m *GetChannelListeningSimpleInfoResp) GetCheckInTopic() string {
	if m != nil {
		return m.CheckInTopic
	}
	return ""
}

func (m *GetChannelListeningSimpleInfoResp) GetCheckInInfo() *ChannelListeningCheckInInfo {
	if m != nil {
		return m.CheckInInfo
	}
	return nil
}

func (m *GetChannelListeningSimpleInfoResp) GetHasUserSetMood() bool {
	if m != nil {
		return m.HasUserSetMood
	}
	return false
}

type ChannelListeningCheckInInfo struct {
	HaveCheckIn          bool     `protobuf:"varint,1,opt,name=have_check_in,json=haveCheckIn,proto3" json:"have_check_in,omitempty"`
	Day                  uint32   `protobuf:"varint,2,opt,name=day,proto3" json:"day,omitempty"`
	Title                string   `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	ExpireAt             uint32   `protobuf:"varint,4,opt,name=expire_at,json=expireAt,proto3" json:"expire_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelListeningCheckInInfo) Reset()         { *m = ChannelListeningCheckInInfo{} }
func (m *ChannelListeningCheckInInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelListeningCheckInInfo) ProtoMessage()    {}
func (*ChannelListeningCheckInInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{13}
}
func (m *ChannelListeningCheckInInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelListeningCheckInInfo.Unmarshal(m, b)
}
func (m *ChannelListeningCheckInInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelListeningCheckInInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelListeningCheckInInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelListeningCheckInInfo.Merge(dst, src)
}
func (m *ChannelListeningCheckInInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelListeningCheckInInfo.Size(m)
}
func (m *ChannelListeningCheckInInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelListeningCheckInInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelListeningCheckInInfo proto.InternalMessageInfo

func (m *ChannelListeningCheckInInfo) GetHaveCheckIn() bool {
	if m != nil {
		return m.HaveCheckIn
	}
	return false
}

func (m *ChannelListeningCheckInInfo) GetDay() uint32 {
	if m != nil {
		return m.Day
	}
	return 0
}

func (m *ChannelListeningCheckInInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ChannelListeningCheckInInfo) GetExpireAt() uint32 {
	if m != nil {
		return m.ExpireAt
	}
	return 0
}

// 获取花花列表
type GetUserFlowerListReq struct {
	Uid                  uint32                    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32                    `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Count                uint32                    `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	LoadMore             *ChannelListeningLoadMore `protobuf:"bytes,4,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetUserFlowerListReq) Reset()         { *m = GetUserFlowerListReq{} }
func (m *GetUserFlowerListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserFlowerListReq) ProtoMessage()    {}
func (*GetUserFlowerListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{14}
}
func (m *GetUserFlowerListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserFlowerListReq.Unmarshal(m, b)
}
func (m *GetUserFlowerListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserFlowerListReq.Marshal(b, m, deterministic)
}
func (dst *GetUserFlowerListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserFlowerListReq.Merge(dst, src)
}
func (m *GetUserFlowerListReq) XXX_Size() int {
	return xxx_messageInfo_GetUserFlowerListReq.Size(m)
}
func (m *GetUserFlowerListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserFlowerListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserFlowerListReq proto.InternalMessageInfo

func (m *GetUserFlowerListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserFlowerListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetUserFlowerListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetUserFlowerListReq) GetLoadMore() *ChannelListeningLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type GetUserFlowerListResp struct {
	Flowers              []*UserFlower             `protobuf:"bytes,1,rep,name=flowers,proto3" json:"flowers,omitempty"`
	Flower               *UserFlower               `protobuf:"bytes,2,opt,name=flower,proto3" json:"flower,omitempty"`
	TodayFlowerCount     uint32                    `protobuf:"varint,3,opt,name=today_flower_count,json=todayFlowerCount,proto3" json:"today_flower_count,omitempty"`
	LoadMore             *ChannelListeningLoadMore `protobuf:"bytes,4,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetUserFlowerListResp) Reset()         { *m = GetUserFlowerListResp{} }
func (m *GetUserFlowerListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserFlowerListResp) ProtoMessage()    {}
func (*GetUserFlowerListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{15}
}
func (m *GetUserFlowerListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserFlowerListResp.Unmarshal(m, b)
}
func (m *GetUserFlowerListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserFlowerListResp.Marshal(b, m, deterministic)
}
func (dst *GetUserFlowerListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserFlowerListResp.Merge(dst, src)
}
func (m *GetUserFlowerListResp) XXX_Size() int {
	return xxx_messageInfo_GetUserFlowerListResp.Size(m)
}
func (m *GetUserFlowerListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserFlowerListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserFlowerListResp proto.InternalMessageInfo

func (m *GetUserFlowerListResp) GetFlowers() []*UserFlower {
	if m != nil {
		return m.Flowers
	}
	return nil
}

func (m *GetUserFlowerListResp) GetFlower() *UserFlower {
	if m != nil {
		return m.Flower
	}
	return nil
}

func (m *GetUserFlowerListResp) GetTodayFlowerCount() uint32 {
	if m != nil {
		return m.TodayFlowerCount
	}
	return 0
}

func (m *GetUserFlowerListResp) GetLoadMore() *ChannelListeningLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type UserFlower struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Sex                  uint32   `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	Rank                 uint32   `protobuf:"varint,4,opt,name=rank,proto3" json:"rank,omitempty"`
	Num                  uint32   `protobuf:"varint,5,opt,name=num,proto3" json:"num,omitempty"`
	CheckInTitle         string   `protobuf:"bytes,6,opt,name=check_in_title,json=checkInTitle,proto3" json:"check_in_title,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserFlower) Reset()         { *m = UserFlower{} }
func (m *UserFlower) String() string { return proto.CompactTextString(m) }
func (*UserFlower) ProtoMessage()    {}
func (*UserFlower) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{16}
}
func (m *UserFlower) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserFlower.Unmarshal(m, b)
}
func (m *UserFlower) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserFlower.Marshal(b, m, deterministic)
}
func (dst *UserFlower) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserFlower.Merge(dst, src)
}
func (m *UserFlower) XXX_Size() int {
	return xxx_messageInfo_UserFlower.Size(m)
}
func (m *UserFlower) XXX_DiscardUnknown() {
	xxx_messageInfo_UserFlower.DiscardUnknown(m)
}

var xxx_messageInfo_UserFlower proto.InternalMessageInfo

func (m *UserFlower) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserFlower) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserFlower) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *UserFlower) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *UserFlower) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *UserFlower) GetCheckInTitle() string {
	if m != nil {
		return m.CheckInTitle
	}
	return ""
}

type GetFlowerDetailReq struct {
	Uid                  uint32                    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32                    `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Count                uint32                    `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	LoadMore             *ChannelListeningLoadMore `protobuf:"bytes,4,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetFlowerDetailReq) Reset()         { *m = GetFlowerDetailReq{} }
func (m *GetFlowerDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetFlowerDetailReq) ProtoMessage()    {}
func (*GetFlowerDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{17}
}
func (m *GetFlowerDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFlowerDetailReq.Unmarshal(m, b)
}
func (m *GetFlowerDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFlowerDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetFlowerDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFlowerDetailReq.Merge(dst, src)
}
func (m *GetFlowerDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetFlowerDetailReq.Size(m)
}
func (m *GetFlowerDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFlowerDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFlowerDetailReq proto.InternalMessageInfo

func (m *GetFlowerDetailReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetFlowerDetailReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetFlowerDetailReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetFlowerDetailReq) GetLoadMore() *ChannelListeningLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type GetFlowerDetailResp struct {
	Details              []*FlowerDetail           `protobuf:"bytes,1,rep,name=details,proto3" json:"details,omitempty"`
	LoadMore             *ChannelListeningLoadMore `protobuf:"bytes,2,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetFlowerDetailResp) Reset()         { *m = GetFlowerDetailResp{} }
func (m *GetFlowerDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetFlowerDetailResp) ProtoMessage()    {}
func (*GetFlowerDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{18}
}
func (m *GetFlowerDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFlowerDetailResp.Unmarshal(m, b)
}
func (m *GetFlowerDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFlowerDetailResp.Marshal(b, m, deterministic)
}
func (dst *GetFlowerDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFlowerDetailResp.Merge(dst, src)
}
func (m *GetFlowerDetailResp) XXX_Size() int {
	return xxx_messageInfo_GetFlowerDetailResp.Size(m)
}
func (m *GetFlowerDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFlowerDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFlowerDetailResp proto.InternalMessageInfo

func (m *GetFlowerDetailResp) GetDetails() []*FlowerDetail {
	if m != nil {
		return m.Details
	}
	return nil
}

func (m *GetFlowerDetailResp) GetLoadMore() *ChannelListeningLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type FlowerDetail struct {
	Date                 string   `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	Desc                 string   `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	FlowerCount          uint32   `protobuf:"varint,3,opt,name=flower_count,json=flowerCount,proto3" json:"flower_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FlowerDetail) Reset()         { *m = FlowerDetail{} }
func (m *FlowerDetail) String() string { return proto.CompactTextString(m) }
func (*FlowerDetail) ProtoMessage()    {}
func (*FlowerDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{19}
}
func (m *FlowerDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FlowerDetail.Unmarshal(m, b)
}
func (m *FlowerDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FlowerDetail.Marshal(b, m, deterministic)
}
func (dst *FlowerDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FlowerDetail.Merge(dst, src)
}
func (m *FlowerDetail) XXX_Size() int {
	return xxx_messageInfo_FlowerDetail.Size(m)
}
func (m *FlowerDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_FlowerDetail.DiscardUnknown(m)
}

var xxx_messageInfo_FlowerDetail proto.InternalMessageInfo

func (m *FlowerDetail) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

func (m *FlowerDetail) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *FlowerDetail) GetFlowerCount() uint32 {
	if m != nil {
		return m.FlowerCount
	}
	return 0
}

type ChannelListenTheme struct {
	Bg                   string   `protobuf:"bytes,1,opt,name=bg,proto3" json:"bg,omitempty"`
	PlayerBg             string   `protobuf:"bytes,2,opt,name=player_bg,json=playerBg,proto3" json:"player_bg,omitempty"`
	ClockBg              string   `protobuf:"bytes,3,opt,name=clock_bg,json=clockBg,proto3" json:"clock_bg,omitempty"`
	HalfFlowerUrl        string   `protobuf:"bytes,4,opt,name=half_flower_url,json=halfFlowerUrl,proto3" json:"half_flower_url,omitempty"`
	EntireFlowerUrl      string   `protobuf:"bytes,5,opt,name=entire_flower_url,json=entireFlowerUrl,proto3" json:"entire_flower_url,omitempty"`
	Unit                 string   `protobuf:"bytes,6,opt,name=unit,proto3" json:"unit,omitempty"`
	Name                 string   `protobuf:"bytes,7,opt,name=name,proto3" json:"name,omitempty"`
	HoldMicBgs           []string `protobuf:"bytes,8,rep,name=hold_mic_bgs,json=holdMicBgs,proto3" json:"hold_mic_bgs,omitempty"`
	LeaveMicBg           string   `protobuf:"bytes,9,opt,name=leave_mic_bg,json=leaveMicBg,proto3" json:"leave_mic_bg,omitempty"`
	MicSeat              string   `protobuf:"bytes,10,opt,name=mic_seat,json=micSeat,proto3" json:"mic_seat,omitempty"`
	NormalSeat           string   `protobuf:"bytes,11,opt,name=normal_seat,json=normalSeat,proto3" json:"normal_seat,omitempty"`
	ClosedSeat           string   `protobuf:"bytes,12,opt,name=closed_seat,json=closedSeat,proto3" json:"closed_seat,omitempty"`
	ShareEntranceIcon    string   `protobuf:"bytes,13,opt,name=share_entrance_icon,json=shareEntranceIcon,proto3" json:"share_entrance_icon,omitempty"`
	NewHoldMicBgs        []string `protobuf:"bytes,14,rep,name=new_hold_mic_bgs,json=newHoldMicBgs,proto3" json:"new_hold_mic_bgs,omitempty"`
	NewLeaveMicBg        string   `protobuf:"bytes,15,opt,name=new_leave_mic_bg,json=newLeaveMicBg,proto3" json:"new_leave_mic_bg,omitempty"`
	ResourceUrl          string   `protobuf:"bytes,16,opt,name=resource_url,json=resourceUrl,proto3" json:"resource_url,omitempty"`
	ResourceMd5          string   `protobuf:"bytes,17,opt,name=resource_md5,json=resourceMd5,proto3" json:"resource_md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelListenTheme) Reset()         { *m = ChannelListenTheme{} }
func (m *ChannelListenTheme) String() string { return proto.CompactTextString(m) }
func (*ChannelListenTheme) ProtoMessage()    {}
func (*ChannelListenTheme) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{20}
}
func (m *ChannelListenTheme) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelListenTheme.Unmarshal(m, b)
}
func (m *ChannelListenTheme) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelListenTheme.Marshal(b, m, deterministic)
}
func (dst *ChannelListenTheme) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelListenTheme.Merge(dst, src)
}
func (m *ChannelListenTheme) XXX_Size() int {
	return xxx_messageInfo_ChannelListenTheme.Size(m)
}
func (m *ChannelListenTheme) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelListenTheme.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelListenTheme proto.InternalMessageInfo

func (m *ChannelListenTheme) GetBg() string {
	if m != nil {
		return m.Bg
	}
	return ""
}

func (m *ChannelListenTheme) GetPlayerBg() string {
	if m != nil {
		return m.PlayerBg
	}
	return ""
}

func (m *ChannelListenTheme) GetClockBg() string {
	if m != nil {
		return m.ClockBg
	}
	return ""
}

func (m *ChannelListenTheme) GetHalfFlowerUrl() string {
	if m != nil {
		return m.HalfFlowerUrl
	}
	return ""
}

func (m *ChannelListenTheme) GetEntireFlowerUrl() string {
	if m != nil {
		return m.EntireFlowerUrl
	}
	return ""
}

func (m *ChannelListenTheme) GetUnit() string {
	if m != nil {
		return m.Unit
	}
	return ""
}

func (m *ChannelListenTheme) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ChannelListenTheme) GetHoldMicBgs() []string {
	if m != nil {
		return m.HoldMicBgs
	}
	return nil
}

func (m *ChannelListenTheme) GetLeaveMicBg() string {
	if m != nil {
		return m.LeaveMicBg
	}
	return ""
}

func (m *ChannelListenTheme) GetMicSeat() string {
	if m != nil {
		return m.MicSeat
	}
	return ""
}

func (m *ChannelListenTheme) GetNormalSeat() string {
	if m != nil {
		return m.NormalSeat
	}
	return ""
}

func (m *ChannelListenTheme) GetClosedSeat() string {
	if m != nil {
		return m.ClosedSeat
	}
	return ""
}

func (m *ChannelListenTheme) GetShareEntranceIcon() string {
	if m != nil {
		return m.ShareEntranceIcon
	}
	return ""
}

func (m *ChannelListenTheme) GetNewHoldMicBgs() []string {
	if m != nil {
		return m.NewHoldMicBgs
	}
	return nil
}

func (m *ChannelListenTheme) GetNewLeaveMicBg() string {
	if m != nil {
		return m.NewLeaveMicBg
	}
	return ""
}

func (m *ChannelListenTheme) GetResourceUrl() string {
	if m != nil {
		return m.ResourceUrl
	}
	return ""
}

func (m *ChannelListenTheme) GetResourceMd5() string {
	if m != nil {
		return m.ResourceMd5
	}
	return ""
}

// 添加歌单
type AddSongMenuReq struct {
	Menu                 *SongMenu `protobuf:"bytes,1,opt,name=menu,proto3" json:"menu,omitempty"`
	SongSheetType        uint32    `protobuf:"varint,2,opt,name=song_sheet_type,json=songSheetType,proto3" json:"song_sheet_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *AddSongMenuReq) Reset()         { *m = AddSongMenuReq{} }
func (m *AddSongMenuReq) String() string { return proto.CompactTextString(m) }
func (*AddSongMenuReq) ProtoMessage()    {}
func (*AddSongMenuReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{21}
}
func (m *AddSongMenuReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddSongMenuReq.Unmarshal(m, b)
}
func (m *AddSongMenuReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddSongMenuReq.Marshal(b, m, deterministic)
}
func (dst *AddSongMenuReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddSongMenuReq.Merge(dst, src)
}
func (m *AddSongMenuReq) XXX_Size() int {
	return xxx_messageInfo_AddSongMenuReq.Size(m)
}
func (m *AddSongMenuReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddSongMenuReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddSongMenuReq proto.InternalMessageInfo

func (m *AddSongMenuReq) GetMenu() *SongMenu {
	if m != nil {
		return m.Menu
	}
	return nil
}

func (m *AddSongMenuReq) GetSongSheetType() uint32 {
	if m != nil {
		return m.SongSheetType
	}
	return 0
}

type AddSongMenuResp struct {
	Menu                 *SongMenu `protobuf:"bytes,1,opt,name=menu,proto3" json:"menu,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *AddSongMenuResp) Reset()         { *m = AddSongMenuResp{} }
func (m *AddSongMenuResp) String() string { return proto.CompactTextString(m) }
func (*AddSongMenuResp) ProtoMessage()    {}
func (*AddSongMenuResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{22}
}
func (m *AddSongMenuResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddSongMenuResp.Unmarshal(m, b)
}
func (m *AddSongMenuResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddSongMenuResp.Marshal(b, m, deterministic)
}
func (dst *AddSongMenuResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddSongMenuResp.Merge(dst, src)
}
func (m *AddSongMenuResp) XXX_Size() int {
	return xxx_messageInfo_AddSongMenuResp.Size(m)
}
func (m *AddSongMenuResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddSongMenuResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddSongMenuResp proto.InternalMessageInfo

func (m *AddSongMenuResp) GetMenu() *SongMenu {
	if m != nil {
		return m.Menu
	}
	return nil
}

type SongMenu struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Bg                   string   `protobuf:"bytes,3,opt,name=bg,proto3" json:"bg,omitempty"`
	IsOpen               bool     `protobuf:"varint,4,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SongMenu) Reset()         { *m = SongMenu{} }
func (m *SongMenu) String() string { return proto.CompactTextString(m) }
func (*SongMenu) ProtoMessage()    {}
func (*SongMenu) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{23}
}
func (m *SongMenu) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SongMenu.Unmarshal(m, b)
}
func (m *SongMenu) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SongMenu.Marshal(b, m, deterministic)
}
func (dst *SongMenu) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SongMenu.Merge(dst, src)
}
func (m *SongMenu) XXX_Size() int {
	return xxx_messageInfo_SongMenu.Size(m)
}
func (m *SongMenu) XXX_DiscardUnknown() {
	xxx_messageInfo_SongMenu.DiscardUnknown(m)
}

var xxx_messageInfo_SongMenu proto.InternalMessageInfo

func (m *SongMenu) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *SongMenu) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SongMenu) GetBg() string {
	if m != nil {
		return m.Bg
	}
	return ""
}

func (m *SongMenu) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

// 获取歌单
type GetAllSongMenuReq struct {
	SongSheetType        uint32   `protobuf:"varint,1,opt,name=song_sheet_type,json=songSheetType,proto3" json:"song_sheet_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllSongMenuReq) Reset()         { *m = GetAllSongMenuReq{} }
func (m *GetAllSongMenuReq) String() string { return proto.CompactTextString(m) }
func (*GetAllSongMenuReq) ProtoMessage()    {}
func (*GetAllSongMenuReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{24}
}
func (m *GetAllSongMenuReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllSongMenuReq.Unmarshal(m, b)
}
func (m *GetAllSongMenuReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllSongMenuReq.Marshal(b, m, deterministic)
}
func (dst *GetAllSongMenuReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllSongMenuReq.Merge(dst, src)
}
func (m *GetAllSongMenuReq) XXX_Size() int {
	return xxx_messageInfo_GetAllSongMenuReq.Size(m)
}
func (m *GetAllSongMenuReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllSongMenuReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllSongMenuReq proto.InternalMessageInfo

func (m *GetAllSongMenuReq) GetSongSheetType() uint32 {
	if m != nil {
		return m.SongSheetType
	}
	return 0
}

type GetAllSongMenuResp struct {
	Menus                []*SongMenu `protobuf:"bytes,1,rep,name=menus,proto3" json:"menus,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetAllSongMenuResp) Reset()         { *m = GetAllSongMenuResp{} }
func (m *GetAllSongMenuResp) String() string { return proto.CompactTextString(m) }
func (*GetAllSongMenuResp) ProtoMessage()    {}
func (*GetAllSongMenuResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{25}
}
func (m *GetAllSongMenuResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllSongMenuResp.Unmarshal(m, b)
}
func (m *GetAllSongMenuResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllSongMenuResp.Marshal(b, m, deterministic)
}
func (dst *GetAllSongMenuResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllSongMenuResp.Merge(dst, src)
}
func (m *GetAllSongMenuResp) XXX_Size() int {
	return xxx_messageInfo_GetAllSongMenuResp.Size(m)
}
func (m *GetAllSongMenuResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllSongMenuResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllSongMenuResp proto.InternalMessageInfo

func (m *GetAllSongMenuResp) GetMenus() []*SongMenu {
	if m != nil {
		return m.Menus
	}
	return nil
}

// 更新歌单
type UpdateSongMenuReq struct {
	Menu                 *SongMenu `protobuf:"bytes,1,opt,name=menu,proto3" json:"menu,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *UpdateSongMenuReq) Reset()         { *m = UpdateSongMenuReq{} }
func (m *UpdateSongMenuReq) String() string { return proto.CompactTextString(m) }
func (*UpdateSongMenuReq) ProtoMessage()    {}
func (*UpdateSongMenuReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{26}
}
func (m *UpdateSongMenuReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateSongMenuReq.Unmarshal(m, b)
}
func (m *UpdateSongMenuReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateSongMenuReq.Marshal(b, m, deterministic)
}
func (dst *UpdateSongMenuReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateSongMenuReq.Merge(dst, src)
}
func (m *UpdateSongMenuReq) XXX_Size() int {
	return xxx_messageInfo_UpdateSongMenuReq.Size(m)
}
func (m *UpdateSongMenuReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateSongMenuReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateSongMenuReq proto.InternalMessageInfo

func (m *UpdateSongMenuReq) GetMenu() *SongMenu {
	if m != nil {
		return m.Menu
	}
	return nil
}

type UpdateSongMenuResp struct {
	Menu                 *SongMenu `protobuf:"bytes,1,opt,name=menu,proto3" json:"menu,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *UpdateSongMenuResp) Reset()         { *m = UpdateSongMenuResp{} }
func (m *UpdateSongMenuResp) String() string { return proto.CompactTextString(m) }
func (*UpdateSongMenuResp) ProtoMessage()    {}
func (*UpdateSongMenuResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{27}
}
func (m *UpdateSongMenuResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateSongMenuResp.Unmarshal(m, b)
}
func (m *UpdateSongMenuResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateSongMenuResp.Marshal(b, m, deterministic)
}
func (dst *UpdateSongMenuResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateSongMenuResp.Merge(dst, src)
}
func (m *UpdateSongMenuResp) XXX_Size() int {
	return xxx_messageInfo_UpdateSongMenuResp.Size(m)
}
func (m *UpdateSongMenuResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateSongMenuResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateSongMenuResp proto.InternalMessageInfo

func (m *UpdateSongMenuResp) GetMenu() *SongMenu {
	if m != nil {
		return m.Menu
	}
	return nil
}

// 重排序歌单
type ReorderSongMenuReq struct {
	Menus                []*SongMenu `protobuf:"bytes,1,rep,name=menus,proto3" json:"menus,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *ReorderSongMenuReq) Reset()         { *m = ReorderSongMenuReq{} }
func (m *ReorderSongMenuReq) String() string { return proto.CompactTextString(m) }
func (*ReorderSongMenuReq) ProtoMessage()    {}
func (*ReorderSongMenuReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{28}
}
func (m *ReorderSongMenuReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReorderSongMenuReq.Unmarshal(m, b)
}
func (m *ReorderSongMenuReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReorderSongMenuReq.Marshal(b, m, deterministic)
}
func (dst *ReorderSongMenuReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReorderSongMenuReq.Merge(dst, src)
}
func (m *ReorderSongMenuReq) XXX_Size() int {
	return xxx_messageInfo_ReorderSongMenuReq.Size(m)
}
func (m *ReorderSongMenuReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReorderSongMenuReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReorderSongMenuReq proto.InternalMessageInfo

func (m *ReorderSongMenuReq) GetMenus() []*SongMenu {
	if m != nil {
		return m.Menus
	}
	return nil
}

type ReorderSongMenuResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReorderSongMenuResp) Reset()         { *m = ReorderSongMenuResp{} }
func (m *ReorderSongMenuResp) String() string { return proto.CompactTextString(m) }
func (*ReorderSongMenuResp) ProtoMessage()    {}
func (*ReorderSongMenuResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{29}
}
func (m *ReorderSongMenuResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReorderSongMenuResp.Unmarshal(m, b)
}
func (m *ReorderSongMenuResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReorderSongMenuResp.Marshal(b, m, deterministic)
}
func (dst *ReorderSongMenuResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReorderSongMenuResp.Merge(dst, src)
}
func (m *ReorderSongMenuResp) XXX_Size() int {
	return xxx_messageInfo_ReorderSongMenuResp.Size(m)
}
func (m *ReorderSongMenuResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReorderSongMenuResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReorderSongMenuResp proto.InternalMessageInfo

// 往歌单添加歌曲
type AddSongReq struct {
	Song                 *Song    `protobuf:"bytes,1,opt,name=song,proto3" json:"song,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddSongReq) Reset()         { *m = AddSongReq{} }
func (m *AddSongReq) String() string { return proto.CompactTextString(m) }
func (*AddSongReq) ProtoMessage()    {}
func (*AddSongReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{30}
}
func (m *AddSongReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddSongReq.Unmarshal(m, b)
}
func (m *AddSongReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddSongReq.Marshal(b, m, deterministic)
}
func (dst *AddSongReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddSongReq.Merge(dst, src)
}
func (m *AddSongReq) XXX_Size() int {
	return xxx_messageInfo_AddSongReq.Size(m)
}
func (m *AddSongReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddSongReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddSongReq proto.InternalMessageInfo

func (m *AddSongReq) GetSong() *Song {
	if m != nil {
		return m.Song
	}
	return nil
}

type AddSongResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddSongResp) Reset()         { *m = AddSongResp{} }
func (m *AddSongResp) String() string { return proto.CompactTextString(m) }
func (*AddSongResp) ProtoMessage()    {}
func (*AddSongResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{31}
}
func (m *AddSongResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddSongResp.Unmarshal(m, b)
}
func (m *AddSongResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddSongResp.Marshal(b, m, deterministic)
}
func (dst *AddSongResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddSongResp.Merge(dst, src)
}
func (m *AddSongResp) XXX_Size() int {
	return xxx_messageInfo_AddSongResp.Size(m)
}
func (m *AddSongResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddSongResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddSongResp proto.InternalMessageInfo

type UpdateSongStatusReq struct {
	SongId               string   `protobuf:"bytes,1,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	Status               uint32   `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateSongStatusReq) Reset()         { *m = UpdateSongStatusReq{} }
func (m *UpdateSongStatusReq) String() string { return proto.CompactTextString(m) }
func (*UpdateSongStatusReq) ProtoMessage()    {}
func (*UpdateSongStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{32}
}
func (m *UpdateSongStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateSongStatusReq.Unmarshal(m, b)
}
func (m *UpdateSongStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateSongStatusReq.Marshal(b, m, deterministic)
}
func (dst *UpdateSongStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateSongStatusReq.Merge(dst, src)
}
func (m *UpdateSongStatusReq) XXX_Size() int {
	return xxx_messageInfo_UpdateSongStatusReq.Size(m)
}
func (m *UpdateSongStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateSongStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateSongStatusReq proto.InternalMessageInfo

func (m *UpdateSongStatusReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *UpdateSongStatusReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type UpdateSongStatusResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateSongStatusResp) Reset()         { *m = UpdateSongStatusResp{} }
func (m *UpdateSongStatusResp) String() string { return proto.CompactTextString(m) }
func (*UpdateSongStatusResp) ProtoMessage()    {}
func (*UpdateSongStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{33}
}
func (m *UpdateSongStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateSongStatusResp.Unmarshal(m, b)
}
func (m *UpdateSongStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateSongStatusResp.Marshal(b, m, deterministic)
}
func (dst *UpdateSongStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateSongStatusResp.Merge(dst, src)
}
func (m *UpdateSongStatusResp) XXX_Size() int {
	return xxx_messageInfo_UpdateSongStatusResp.Size(m)
}
func (m *UpdateSongStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateSongStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateSongStatusResp proto.InternalMessageInfo

type BatchAddSongReq struct {
	Songs                []*Song  `protobuf:"bytes,1,rep,name=songs,proto3" json:"songs,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchAddSongReq) Reset()         { *m = BatchAddSongReq{} }
func (m *BatchAddSongReq) String() string { return proto.CompactTextString(m) }
func (*BatchAddSongReq) ProtoMessage()    {}
func (*BatchAddSongReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{34}
}
func (m *BatchAddSongReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddSongReq.Unmarshal(m, b)
}
func (m *BatchAddSongReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddSongReq.Marshal(b, m, deterministic)
}
func (dst *BatchAddSongReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddSongReq.Merge(dst, src)
}
func (m *BatchAddSongReq) XXX_Size() int {
	return xxx_messageInfo_BatchAddSongReq.Size(m)
}
func (m *BatchAddSongReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddSongReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddSongReq proto.InternalMessageInfo

func (m *BatchAddSongReq) GetSongs() []*Song {
	if m != nil {
		return m.Songs
	}
	return nil
}

type BatchAddSongResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchAddSongResp) Reset()         { *m = BatchAddSongResp{} }
func (m *BatchAddSongResp) String() string { return proto.CompactTextString(m) }
func (*BatchAddSongResp) ProtoMessage()    {}
func (*BatchAddSongResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{35}
}
func (m *BatchAddSongResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddSongResp.Unmarshal(m, b)
}
func (m *BatchAddSongResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddSongResp.Marshal(b, m, deterministic)
}
func (dst *BatchAddSongResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddSongResp.Merge(dst, src)
}
func (m *BatchAddSongResp) XXX_Size() int {
	return xxx_messageInfo_BatchAddSongResp.Size(m)
}
func (m *BatchAddSongResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddSongResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddSongResp proto.InternalMessageInfo

// 搜索歌曲
type SearchSongReq struct {
	MenuId               string   `protobuf:"bytes,1,opt,name=menu_id,json=menuId,proto3" json:"menu_id,omitempty"`
	SongName             string   `protobuf:"bytes,2,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	Singer               string   `protobuf:"bytes,3,opt,name=singer,proto3" json:"singer,omitempty"`
	Page                 uint32   `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	Count                uint32   `protobuf:"varint,5,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchSongReq) Reset()         { *m = SearchSongReq{} }
func (m *SearchSongReq) String() string { return proto.CompactTextString(m) }
func (*SearchSongReq) ProtoMessage()    {}
func (*SearchSongReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{36}
}
func (m *SearchSongReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchSongReq.Unmarshal(m, b)
}
func (m *SearchSongReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchSongReq.Marshal(b, m, deterministic)
}
func (dst *SearchSongReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchSongReq.Merge(dst, src)
}
func (m *SearchSongReq) XXX_Size() int {
	return xxx_messageInfo_SearchSongReq.Size(m)
}
func (m *SearchSongReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchSongReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchSongReq proto.InternalMessageInfo

func (m *SearchSongReq) GetMenuId() string {
	if m != nil {
		return m.MenuId
	}
	return ""
}

func (m *SearchSongReq) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

func (m *SearchSongReq) GetSinger() string {
	if m != nil {
		return m.Singer
	}
	return ""
}

func (m *SearchSongReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *SearchSongReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type SearchSongResp struct {
	Songs                []*Song  `protobuf:"bytes,1,rep,name=songs,proto3" json:"songs,omitempty"`
	TotalCount           uint32   `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchSongResp) Reset()         { *m = SearchSongResp{} }
func (m *SearchSongResp) String() string { return proto.CompactTextString(m) }
func (*SearchSongResp) ProtoMessage()    {}
func (*SearchSongResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{37}
}
func (m *SearchSongResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchSongResp.Unmarshal(m, b)
}
func (m *SearchSongResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchSongResp.Marshal(b, m, deterministic)
}
func (dst *SearchSongResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchSongResp.Merge(dst, src)
}
func (m *SearchSongResp) XXX_Size() int {
	return xxx_messageInfo_SearchSongResp.Size(m)
}
func (m *SearchSongResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchSongResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchSongResp proto.InternalMessageInfo

func (m *SearchSongResp) GetSongs() []*Song {
	if m != nil {
		return m.Songs
	}
	return nil
}

func (m *SearchSongResp) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

// 获取歌曲
type GetSongByMenuIdReq struct {
	MenuId               string   `protobuf:"bytes,1,opt,name=menu_id,json=menuId,proto3" json:"menu_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSongByMenuIdReq) Reset()         { *m = GetSongByMenuIdReq{} }
func (m *GetSongByMenuIdReq) String() string { return proto.CompactTextString(m) }
func (*GetSongByMenuIdReq) ProtoMessage()    {}
func (*GetSongByMenuIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{38}
}
func (m *GetSongByMenuIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSongByMenuIdReq.Unmarshal(m, b)
}
func (m *GetSongByMenuIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSongByMenuIdReq.Marshal(b, m, deterministic)
}
func (dst *GetSongByMenuIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSongByMenuIdReq.Merge(dst, src)
}
func (m *GetSongByMenuIdReq) XXX_Size() int {
	return xxx_messageInfo_GetSongByMenuIdReq.Size(m)
}
func (m *GetSongByMenuIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSongByMenuIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSongByMenuIdReq proto.InternalMessageInfo

func (m *GetSongByMenuIdReq) GetMenuId() string {
	if m != nil {
		return m.MenuId
	}
	return ""
}

type GetSongByMenuIdResp struct {
	Songs                []*Song  `protobuf:"bytes,1,rep,name=songs,proto3" json:"songs,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSongByMenuIdResp) Reset()         { *m = GetSongByMenuIdResp{} }
func (m *GetSongByMenuIdResp) String() string { return proto.CompactTextString(m) }
func (*GetSongByMenuIdResp) ProtoMessage()    {}
func (*GetSongByMenuIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{39}
}
func (m *GetSongByMenuIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSongByMenuIdResp.Unmarshal(m, b)
}
func (m *GetSongByMenuIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSongByMenuIdResp.Marshal(b, m, deterministic)
}
func (dst *GetSongByMenuIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSongByMenuIdResp.Merge(dst, src)
}
func (m *GetSongByMenuIdResp) XXX_Size() int {
	return xxx_messageInfo_GetSongByMenuIdResp.Size(m)
}
func (m *GetSongByMenuIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSongByMenuIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSongByMenuIdResp proto.InternalMessageInfo

func (m *GetSongByMenuIdResp) GetSongs() []*Song {
	if m != nil {
		return m.Songs
	}
	return nil
}

// 删除歌曲
type DeleteSongReq struct {
	MenuId               string   `protobuf:"bytes,1,opt,name=menu_id,json=menuId,proto3" json:"menu_id,omitempty"`
	SongIds              []string `protobuf:"bytes,2,rep,name=song_ids,json=songIds,proto3" json:"song_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteSongReq) Reset()         { *m = DeleteSongReq{} }
func (m *DeleteSongReq) String() string { return proto.CompactTextString(m) }
func (*DeleteSongReq) ProtoMessage()    {}
func (*DeleteSongReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{40}
}
func (m *DeleteSongReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteSongReq.Unmarshal(m, b)
}
func (m *DeleteSongReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteSongReq.Marshal(b, m, deterministic)
}
func (dst *DeleteSongReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteSongReq.Merge(dst, src)
}
func (m *DeleteSongReq) XXX_Size() int {
	return xxx_messageInfo_DeleteSongReq.Size(m)
}
func (m *DeleteSongReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteSongReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteSongReq proto.InternalMessageInfo

func (m *DeleteSongReq) GetMenuId() string {
	if m != nil {
		return m.MenuId
	}
	return ""
}

func (m *DeleteSongReq) GetSongIds() []string {
	if m != nil {
		return m.SongIds
	}
	return nil
}

type DeleteSongResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteSongResp) Reset()         { *m = DeleteSongResp{} }
func (m *DeleteSongResp) String() string { return proto.CompactTextString(m) }
func (*DeleteSongResp) ProtoMessage()    {}
func (*DeleteSongResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{41}
}
func (m *DeleteSongResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteSongResp.Unmarshal(m, b)
}
func (m *DeleteSongResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteSongResp.Marshal(b, m, deterministic)
}
func (dst *DeleteSongResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteSongResp.Merge(dst, src)
}
func (m *DeleteSongResp) XXX_Size() int {
	return xxx_messageInfo_DeleteSongResp.Size(m)
}
func (m *DeleteSongResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteSongResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteSongResp proto.InternalMessageInfo

// 删除歌曲
type DeleteSongV2Req struct {
	SongId               string   `protobuf:"bytes,1,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteSongV2Req) Reset()         { *m = DeleteSongV2Req{} }
func (m *DeleteSongV2Req) String() string { return proto.CompactTextString(m) }
func (*DeleteSongV2Req) ProtoMessage()    {}
func (*DeleteSongV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{42}
}
func (m *DeleteSongV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteSongV2Req.Unmarshal(m, b)
}
func (m *DeleteSongV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteSongV2Req.Marshal(b, m, deterministic)
}
func (dst *DeleteSongV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteSongV2Req.Merge(dst, src)
}
func (m *DeleteSongV2Req) XXX_Size() int {
	return xxx_messageInfo_DeleteSongV2Req.Size(m)
}
func (m *DeleteSongV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteSongV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteSongV2Req proto.InternalMessageInfo

func (m *DeleteSongV2Req) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

type DeleteSongV2Resp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteSongV2Resp) Reset()         { *m = DeleteSongV2Resp{} }
func (m *DeleteSongV2Resp) String() string { return proto.CompactTextString(m) }
func (*DeleteSongV2Resp) ProtoMessage()    {}
func (*DeleteSongV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{43}
}
func (m *DeleteSongV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteSongV2Resp.Unmarshal(m, b)
}
func (m *DeleteSongV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteSongV2Resp.Marshal(b, m, deterministic)
}
func (dst *DeleteSongV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteSongV2Resp.Merge(dst, src)
}
func (m *DeleteSongV2Resp) XXX_Size() int {
	return xxx_messageInfo_DeleteSongV2Resp.Size(m)
}
func (m *DeleteSongV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteSongV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteSongV2Resp proto.InternalMessageInfo

type Song struct {
	Id                   string    `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string    `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Singer               string    `protobuf:"bytes,3,opt,name=singer,proto3" json:"singer,omitempty"`
	MusicType            uint32    `protobuf:"varint,4,opt,name=music_type,json=musicType,proto3" json:"music_type,omitempty"`
	File                 string    `protobuf:"bytes,5,opt,name=file,proto3" json:"file,omitempty"`
	FileSize             string    `protobuf:"bytes,6,opt,name=file_size,json=fileSize,proto3" json:"file_size,omitempty"`
	FileType             string    `protobuf:"bytes,7,opt,name=file_type,json=fileType,proto3" json:"file_type,omitempty"`
	FileHash             string    `protobuf:"bytes,8,opt,name=file_hash,json=fileHash,proto3" json:"file_hash,omitempty"`
	Uid                  uint32    `protobuf:"varint,9,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string    `protobuf:"bytes,10,opt,name=nickname,proto3" json:"nickname,omitempty"`
	CreatedAt            uint64    `protobuf:"varint,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	Status               uint32    `protobuf:"varint,12,opt,name=status,proto3" json:"status,omitempty"`
	Account              string    `protobuf:"bytes,13,opt,name=account,proto3" json:"account,omitempty"`
	Order                uint32    `protobuf:"varint,14,opt,name=order,proto3" json:"order,omitempty"`
	Menu                 *SongMenu `protobuf:"bytes,15,opt,name=menu,proto3" json:"menu,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *Song) Reset()         { *m = Song{} }
func (m *Song) String() string { return proto.CompactTextString(m) }
func (*Song) ProtoMessage()    {}
func (*Song) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{44}
}
func (m *Song) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Song.Unmarshal(m, b)
}
func (m *Song) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Song.Marshal(b, m, deterministic)
}
func (dst *Song) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Song.Merge(dst, src)
}
func (m *Song) XXX_Size() int {
	return xxx_messageInfo_Song.Size(m)
}
func (m *Song) XXX_DiscardUnknown() {
	xxx_messageInfo_Song.DiscardUnknown(m)
}

var xxx_messageInfo_Song proto.InternalMessageInfo

func (m *Song) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *Song) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Song) GetSinger() string {
	if m != nil {
		return m.Singer
	}
	return ""
}

func (m *Song) GetMusicType() uint32 {
	if m != nil {
		return m.MusicType
	}
	return 0
}

func (m *Song) GetFile() string {
	if m != nil {
		return m.File
	}
	return ""
}

func (m *Song) GetFileSize() string {
	if m != nil {
		return m.FileSize
	}
	return ""
}

func (m *Song) GetFileType() string {
	if m != nil {
		return m.FileType
	}
	return ""
}

func (m *Song) GetFileHash() string {
	if m != nil {
		return m.FileHash
	}
	return ""
}

func (m *Song) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *Song) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *Song) GetCreatedAt() uint64 {
	if m != nil {
		return m.CreatedAt
	}
	return 0
}

func (m *Song) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *Song) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *Song) GetOrder() uint32 {
	if m != nil {
		return m.Order
	}
	return 0
}

func (m *Song) GetMenu() *SongMenu {
	if m != nil {
		return m.Menu
	}
	return nil
}

// 获取所有歌曲
type GetSongsReq struct {
	Page                 uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Count                uint32   `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSongsReq) Reset()         { *m = GetSongsReq{} }
func (m *GetSongsReq) String() string { return proto.CompactTextString(m) }
func (*GetSongsReq) ProtoMessage()    {}
func (*GetSongsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{45}
}
func (m *GetSongsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSongsReq.Unmarshal(m, b)
}
func (m *GetSongsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSongsReq.Marshal(b, m, deterministic)
}
func (dst *GetSongsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSongsReq.Merge(dst, src)
}
func (m *GetSongsReq) XXX_Size() int {
	return xxx_messageInfo_GetSongsReq.Size(m)
}
func (m *GetSongsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSongsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSongsReq proto.InternalMessageInfo

func (m *GetSongsReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetSongsReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetSongsResp struct {
	Songs                []*Song  `protobuf:"bytes,1,rep,name=songs,proto3" json:"songs,omitempty"`
	TotalCount           uint32   `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSongsResp) Reset()         { *m = GetSongsResp{} }
func (m *GetSongsResp) String() string { return proto.CompactTextString(m) }
func (*GetSongsResp) ProtoMessage()    {}
func (*GetSongsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{46}
}
func (m *GetSongsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSongsResp.Unmarshal(m, b)
}
func (m *GetSongsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSongsResp.Marshal(b, m, deterministic)
}
func (dst *GetSongsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSongsResp.Merge(dst, src)
}
func (m *GetSongsResp) XXX_Size() int {
	return xxx_messageInfo_GetSongsResp.Size(m)
}
func (m *GetSongsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSongsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSongsResp proto.InternalMessageInfo

func (m *GetSongsResp) GetSongs() []*Song {
	if m != nil {
		return m.Songs
	}
	return nil
}

func (m *GetSongsResp) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

// 歌曲排序
type ReorderSongReq struct {
	Song                 *Song    `protobuf:"bytes,1,opt,name=song,proto3" json:"song,omitempty"`
	TargetOrder          uint32   `protobuf:"varint,2,opt,name=target_order,json=targetOrder,proto3" json:"target_order,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReorderSongReq) Reset()         { *m = ReorderSongReq{} }
func (m *ReorderSongReq) String() string { return proto.CompactTextString(m) }
func (*ReorderSongReq) ProtoMessage()    {}
func (*ReorderSongReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{47}
}
func (m *ReorderSongReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReorderSongReq.Unmarshal(m, b)
}
func (m *ReorderSongReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReorderSongReq.Marshal(b, m, deterministic)
}
func (dst *ReorderSongReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReorderSongReq.Merge(dst, src)
}
func (m *ReorderSongReq) XXX_Size() int {
	return xxx_messageInfo_ReorderSongReq.Size(m)
}
func (m *ReorderSongReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReorderSongReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReorderSongReq proto.InternalMessageInfo

func (m *ReorderSongReq) GetSong() *Song {
	if m != nil {
		return m.Song
	}
	return nil
}

func (m *ReorderSongReq) GetTargetOrder() uint32 {
	if m != nil {
		return m.TargetOrder
	}
	return 0
}

type ReorderSongResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReorderSongResp) Reset()         { *m = ReorderSongResp{} }
func (m *ReorderSongResp) String() string { return proto.CompactTextString(m) }
func (*ReorderSongResp) ProtoMessage()    {}
func (*ReorderSongResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{48}
}
func (m *ReorderSongResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReorderSongResp.Unmarshal(m, b)
}
func (m *ReorderSongResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReorderSongResp.Marshal(b, m, deterministic)
}
func (dst *ReorderSongResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReorderSongResp.Merge(dst, src)
}
func (m *ReorderSongResp) XXX_Size() int {
	return xxx_messageInfo_ReorderSongResp.Size(m)
}
func (m *ReorderSongResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReorderSongResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReorderSongResp proto.InternalMessageInfo

type SetListeningCheckInTopicReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Topic                string   `protobuf:"bytes,2,opt,name=topic,proto3" json:"topic,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetListeningCheckInTopicReq) Reset()         { *m = SetListeningCheckInTopicReq{} }
func (m *SetListeningCheckInTopicReq) String() string { return proto.CompactTextString(m) }
func (*SetListeningCheckInTopicReq) ProtoMessage()    {}
func (*SetListeningCheckInTopicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{49}
}
func (m *SetListeningCheckInTopicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetListeningCheckInTopicReq.Unmarshal(m, b)
}
func (m *SetListeningCheckInTopicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetListeningCheckInTopicReq.Marshal(b, m, deterministic)
}
func (dst *SetListeningCheckInTopicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetListeningCheckInTopicReq.Merge(dst, src)
}
func (m *SetListeningCheckInTopicReq) XXX_Size() int {
	return xxx_messageInfo_SetListeningCheckInTopicReq.Size(m)
}
func (m *SetListeningCheckInTopicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetListeningCheckInTopicReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetListeningCheckInTopicReq proto.InternalMessageInfo

func (m *SetListeningCheckInTopicReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetListeningCheckInTopicReq) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

type SetListeningCheckInTopicResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetListeningCheckInTopicResp) Reset()         { *m = SetListeningCheckInTopicResp{} }
func (m *SetListeningCheckInTopicResp) String() string { return proto.CompactTextString(m) }
func (*SetListeningCheckInTopicResp) ProtoMessage()    {}
func (*SetListeningCheckInTopicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{50}
}
func (m *SetListeningCheckInTopicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetListeningCheckInTopicResp.Unmarshal(m, b)
}
func (m *SetListeningCheckInTopicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetListeningCheckInTopicResp.Marshal(b, m, deterministic)
}
func (dst *SetListeningCheckInTopicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetListeningCheckInTopicResp.Merge(dst, src)
}
func (m *SetListeningCheckInTopicResp) XXX_Size() int {
	return xxx_messageInfo_SetListeningCheckInTopicResp.Size(m)
}
func (m *SetListeningCheckInTopicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetListeningCheckInTopicResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetListeningCheckInTopicResp proto.InternalMessageInfo

type ListeningCheckInReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListeningCheckInReq) Reset()         { *m = ListeningCheckInReq{} }
func (m *ListeningCheckInReq) String() string { return proto.CompactTextString(m) }
func (*ListeningCheckInReq) ProtoMessage()    {}
func (*ListeningCheckInReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{51}
}
func (m *ListeningCheckInReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningCheckInReq.Unmarshal(m, b)
}
func (m *ListeningCheckInReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningCheckInReq.Marshal(b, m, deterministic)
}
func (dst *ListeningCheckInReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningCheckInReq.Merge(dst, src)
}
func (m *ListeningCheckInReq) XXX_Size() int {
	return xxx_messageInfo_ListeningCheckInReq.Size(m)
}
func (m *ListeningCheckInReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningCheckInReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningCheckInReq proto.InternalMessageInfo

func (m *ListeningCheckInReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ListeningCheckInReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ListeningCheckInResp struct {
	Type                 ChannelListeningCheckInType       `protobuf:"varint,1,opt,name=type,proto3,enum=channellistening.ChannelListeningCheckInType" json:"type,omitempty"`
	CheckInInfo          *ChannelListeningCheckInInfo      `protobuf:"bytes,2,opt,name=check_in_info,json=checkInInfo,proto3" json:"check_in_info,omitempty"`
	ShareCard            *ChannelListeningCheckInShareCard `protobuf:"bytes,3,opt,name=share_card,json=shareCard,proto3" json:"share_card,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *ListeningCheckInResp) Reset()         { *m = ListeningCheckInResp{} }
func (m *ListeningCheckInResp) String() string { return proto.CompactTextString(m) }
func (*ListeningCheckInResp) ProtoMessage()    {}
func (*ListeningCheckInResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{52}
}
func (m *ListeningCheckInResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningCheckInResp.Unmarshal(m, b)
}
func (m *ListeningCheckInResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningCheckInResp.Marshal(b, m, deterministic)
}
func (dst *ListeningCheckInResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningCheckInResp.Merge(dst, src)
}
func (m *ListeningCheckInResp) XXX_Size() int {
	return xxx_messageInfo_ListeningCheckInResp.Size(m)
}
func (m *ListeningCheckInResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningCheckInResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningCheckInResp proto.InternalMessageInfo

func (m *ListeningCheckInResp) GetType() ChannelListeningCheckInType {
	if m != nil {
		return m.Type
	}
	return ChannelListeningCheckInType_ChannelListeningCheckInType_UNDEFINED
}

func (m *ListeningCheckInResp) GetCheckInInfo() *ChannelListeningCheckInInfo {
	if m != nil {
		return m.CheckInInfo
	}
	return nil
}

func (m *ListeningCheckInResp) GetShareCard() *ChannelListeningCheckInShareCard {
	if m != nil {
		return m.ShareCard
	}
	return nil
}

type ConstellationReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConstellationReq) Reset()         { *m = ConstellationReq{} }
func (m *ConstellationReq) String() string { return proto.CompactTextString(m) }
func (*ConstellationReq) ProtoMessage()    {}
func (*ConstellationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{53}
}
func (m *ConstellationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConstellationReq.Unmarshal(m, b)
}
func (m *ConstellationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConstellationReq.Marshal(b, m, deterministic)
}
func (dst *ConstellationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConstellationReq.Merge(dst, src)
}
func (m *ConstellationReq) XXX_Size() int {
	return xxx_messageInfo_ConstellationReq.Size(m)
}
func (m *ConstellationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ConstellationReq.DiscardUnknown(m)
}

var xxx_messageInfo_ConstellationReq proto.InternalMessageInfo

func (m *ConstellationReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type ConstellationResp struct {
	ConstellationChoose  Constellation `protobuf:"varint,1,opt,name=constellation_choose,json=constellationChoose,proto3,enum=channellistening.Constellation" json:"constellation_choose,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ConstellationResp) Reset()         { *m = ConstellationResp{} }
func (m *ConstellationResp) String() string { return proto.CompactTextString(m) }
func (*ConstellationResp) ProtoMessage()    {}
func (*ConstellationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{54}
}
func (m *ConstellationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConstellationResp.Unmarshal(m, b)
}
func (m *ConstellationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConstellationResp.Marshal(b, m, deterministic)
}
func (dst *ConstellationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConstellationResp.Merge(dst, src)
}
func (m *ConstellationResp) XXX_Size() int {
	return xxx_messageInfo_ConstellationResp.Size(m)
}
func (m *ConstellationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ConstellationResp.DiscardUnknown(m)
}

var xxx_messageInfo_ConstellationResp proto.InternalMessageInfo

func (m *ConstellationResp) GetConstellationChoose() Constellation {
	if m != nil {
		return m.ConstellationChoose
	}
	return Constellation_undefined
}

type ListeningCheckInV3Req struct {
	Uid                  uint32        `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32        `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ConstellationChoose  Constellation `protobuf:"varint,3,opt,name=constellation_choose,json=constellationChoose,proto3,enum=channellistening.Constellation" json:"constellation_choose,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ListeningCheckInV3Req) Reset()         { *m = ListeningCheckInV3Req{} }
func (m *ListeningCheckInV3Req) String() string { return proto.CompactTextString(m) }
func (*ListeningCheckInV3Req) ProtoMessage()    {}
func (*ListeningCheckInV3Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{55}
}
func (m *ListeningCheckInV3Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningCheckInV3Req.Unmarshal(m, b)
}
func (m *ListeningCheckInV3Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningCheckInV3Req.Marshal(b, m, deterministic)
}
func (dst *ListeningCheckInV3Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningCheckInV3Req.Merge(dst, src)
}
func (m *ListeningCheckInV3Req) XXX_Size() int {
	return xxx_messageInfo_ListeningCheckInV3Req.Size(m)
}
func (m *ListeningCheckInV3Req) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningCheckInV3Req.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningCheckInV3Req proto.InternalMessageInfo

func (m *ListeningCheckInV3Req) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ListeningCheckInV3Req) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ListeningCheckInV3Req) GetConstellationChoose() Constellation {
	if m != nil {
		return m.ConstellationChoose
	}
	return Constellation_undefined
}

type ListeningCheckInV3Resp struct {
	Type                 ChannelListeningCheckInType  `protobuf:"varint,1,opt,name=type,proto3,enum=channellistening.ChannelListeningCheckInType" json:"type,omitempty"`
	CheckInInfo          *ChannelListeningCheckInInfo `protobuf:"bytes,2,opt,name=check_in_info,json=checkInInfo,proto3" json:"check_in_info,omitempty"`
	ShareCard            *ChannelListeningShareCardV3 `protobuf:"bytes,3,opt,name=share_card,json=shareCard,proto3" json:"share_card,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *ListeningCheckInV3Resp) Reset()         { *m = ListeningCheckInV3Resp{} }
func (m *ListeningCheckInV3Resp) String() string { return proto.CompactTextString(m) }
func (*ListeningCheckInV3Resp) ProtoMessage()    {}
func (*ListeningCheckInV3Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{56}
}
func (m *ListeningCheckInV3Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningCheckInV3Resp.Unmarshal(m, b)
}
func (m *ListeningCheckInV3Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningCheckInV3Resp.Marshal(b, m, deterministic)
}
func (dst *ListeningCheckInV3Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningCheckInV3Resp.Merge(dst, src)
}
func (m *ListeningCheckInV3Resp) XXX_Size() int {
	return xxx_messageInfo_ListeningCheckInV3Resp.Size(m)
}
func (m *ListeningCheckInV3Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningCheckInV3Resp.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningCheckInV3Resp proto.InternalMessageInfo

func (m *ListeningCheckInV3Resp) GetType() ChannelListeningCheckInType {
	if m != nil {
		return m.Type
	}
	return ChannelListeningCheckInType_ChannelListeningCheckInType_UNDEFINED
}

func (m *ListeningCheckInV3Resp) GetCheckInInfo() *ChannelListeningCheckInInfo {
	if m != nil {
		return m.CheckInInfo
	}
	return nil
}

func (m *ListeningCheckInV3Resp) GetShareCard() *ChannelListeningShareCardV3 {
	if m != nil {
		return m.ShareCard
	}
	return nil
}

type ListeningCheckInSharedReq struct {
	ShareId              string   `protobuf:"bytes,1,opt,name=share_id,json=shareId,proto3" json:"share_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListeningCheckInSharedReq) Reset()         { *m = ListeningCheckInSharedReq{} }
func (m *ListeningCheckInSharedReq) String() string { return proto.CompactTextString(m) }
func (*ListeningCheckInSharedReq) ProtoMessage()    {}
func (*ListeningCheckInSharedReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{57}
}
func (m *ListeningCheckInSharedReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningCheckInSharedReq.Unmarshal(m, b)
}
func (m *ListeningCheckInSharedReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningCheckInSharedReq.Marshal(b, m, deterministic)
}
func (dst *ListeningCheckInSharedReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningCheckInSharedReq.Merge(dst, src)
}
func (m *ListeningCheckInSharedReq) XXX_Size() int {
	return xxx_messageInfo_ListeningCheckInSharedReq.Size(m)
}
func (m *ListeningCheckInSharedReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningCheckInSharedReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningCheckInSharedReq proto.InternalMessageInfo

func (m *ListeningCheckInSharedReq) GetShareId() string {
	if m != nil {
		return m.ShareId
	}
	return ""
}

type ListeningCheckInSharedResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListeningCheckInSharedResp) Reset()         { *m = ListeningCheckInSharedResp{} }
func (m *ListeningCheckInSharedResp) String() string { return proto.CompactTextString(m) }
func (*ListeningCheckInSharedResp) ProtoMessage()    {}
func (*ListeningCheckInSharedResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{58}
}
func (m *ListeningCheckInSharedResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningCheckInSharedResp.Unmarshal(m, b)
}
func (m *ListeningCheckInSharedResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningCheckInSharedResp.Marshal(b, m, deterministic)
}
func (dst *ListeningCheckInSharedResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningCheckInSharedResp.Merge(dst, src)
}
func (m *ListeningCheckInSharedResp) XXX_Size() int {
	return xxx_messageInfo_ListeningCheckInSharedResp.Size(m)
}
func (m *ListeningCheckInSharedResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningCheckInSharedResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningCheckInSharedResp proto.InternalMessageInfo

type ChannelListeningShareCardV3 struct {
	Account              string                            `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	NickName             string                            `protobuf:"bytes,2,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	ShowTime             int64                             `protobuf:"varint,3,opt,name=show_time,json=showTime,proto3" json:"show_time,omitempty"`
	DisplayId            uint32                            `protobuf:"varint,4,opt,name=display_id,json=displayId,proto3" json:"display_id,omitempty"`
	ChannelId            uint32                            `protobuf:"varint,5,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelName          string                            `protobuf:"bytes,6,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	CardImg              string                            `protobuf:"bytes,7,opt,name=card_img,json=cardImg,proto3" json:"card_img,omitempty"`
	BgImg                string                            `protobuf:"bytes,8,opt,name=bg_img,json=bgImg,proto3" json:"bg_img,omitempty"`
	BtDesc               string                            `protobuf:"bytes,9,opt,name=bt_desc,json=btDesc,proto3" json:"bt_desc,omitempty"`
	WebTitle             string                            `protobuf:"bytes,10,opt,name=web_title,json=webTitle,proto3" json:"web_title,omitempty"`
	Fortune              string                            `protobuf:"bytes,11,opt,name=fortune,proto3" json:"fortune,omitempty"`
	MatchConstellation   Constellation                     `protobuf:"varint,12,opt,name=match_constellation,json=matchConstellation,proto3,enum=channellistening.Constellation" json:"match_constellation,omitempty"`
	ChooseConstellation  Constellation                     `protobuf:"varint,13,opt,name=choose_constellation,json=chooseConstellation,proto3,enum=channellistening.Constellation" json:"choose_constellation,omitempty"`
	ShareInfo            *ChannelListeningCheckInShareInfo `protobuf:"bytes,14,opt,name=share_info,json=shareInfo,proto3" json:"share_info,omitempty"`
	ChannelViewId        string                            `protobuf:"bytes,15,opt,name=channel_view_id,json=channelViewId,proto3" json:"channel_view_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *ChannelListeningShareCardV3) Reset()         { *m = ChannelListeningShareCardV3{} }
func (m *ChannelListeningShareCardV3) String() string { return proto.CompactTextString(m) }
func (*ChannelListeningShareCardV3) ProtoMessage()    {}
func (*ChannelListeningShareCardV3) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{59}
}
func (m *ChannelListeningShareCardV3) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelListeningShareCardV3.Unmarshal(m, b)
}
func (m *ChannelListeningShareCardV3) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelListeningShareCardV3.Marshal(b, m, deterministic)
}
func (dst *ChannelListeningShareCardV3) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelListeningShareCardV3.Merge(dst, src)
}
func (m *ChannelListeningShareCardV3) XXX_Size() int {
	return xxx_messageInfo_ChannelListeningShareCardV3.Size(m)
}
func (m *ChannelListeningShareCardV3) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelListeningShareCardV3.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelListeningShareCardV3 proto.InternalMessageInfo

func (m *ChannelListeningShareCardV3) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ChannelListeningShareCardV3) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *ChannelListeningShareCardV3) GetShowTime() int64 {
	if m != nil {
		return m.ShowTime
	}
	return 0
}

func (m *ChannelListeningShareCardV3) GetDisplayId() uint32 {
	if m != nil {
		return m.DisplayId
	}
	return 0
}

func (m *ChannelListeningShareCardV3) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelListeningShareCardV3) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *ChannelListeningShareCardV3) GetCardImg() string {
	if m != nil {
		return m.CardImg
	}
	return ""
}

func (m *ChannelListeningShareCardV3) GetBgImg() string {
	if m != nil {
		return m.BgImg
	}
	return ""
}

func (m *ChannelListeningShareCardV3) GetBtDesc() string {
	if m != nil {
		return m.BtDesc
	}
	return ""
}

func (m *ChannelListeningShareCardV3) GetWebTitle() string {
	if m != nil {
		return m.WebTitle
	}
	return ""
}

func (m *ChannelListeningShareCardV3) GetFortune() string {
	if m != nil {
		return m.Fortune
	}
	return ""
}

func (m *ChannelListeningShareCardV3) GetMatchConstellation() Constellation {
	if m != nil {
		return m.MatchConstellation
	}
	return Constellation_undefined
}

func (m *ChannelListeningShareCardV3) GetChooseConstellation() Constellation {
	if m != nil {
		return m.ChooseConstellation
	}
	return Constellation_undefined
}

func (m *ChannelListeningShareCardV3) GetShareInfo() *ChannelListeningCheckInShareInfo {
	if m != nil {
		return m.ShareInfo
	}
	return nil
}

func (m *ChannelListeningShareCardV3) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

type ChannelListeningCheckInShareCard struct {
	CheckInTitleDesc     string                            `protobuf:"bytes,1,opt,name=check_in_title_desc,json=checkInTitleDesc,proto3" json:"check_in_title_desc,omitempty"`
	ShowTime             int64                             `protobuf:"varint,2,opt,name=show_time,json=showTime,proto3" json:"show_time,omitempty"`
	Account              string                            `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	NickName             string                            `protobuf:"bytes,4,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	LittleTitle          string                            `protobuf:"bytes,5,opt,name=little_title,json=littleTitle,proto3" json:"little_title,omitempty"`
	Title                string                            `protobuf:"bytes,6,opt,name=title,proto3" json:"title,omitempty"`
	ChannelId            uint32                            `protobuf:"varint,7,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	CardImg              string                            `protobuf:"bytes,8,opt,name=card_img,json=cardImg,proto3" json:"card_img,omitempty"`
	WebBg                string                            `protobuf:"bytes,9,opt,name=web_bg,json=webBg,proto3" json:"web_bg,omitempty"`
	BtDesc               string                            `protobuf:"bytes,10,opt,name=bt_desc,json=btDesc,proto3" json:"bt_desc,omitempty"`
	WebTitle             string                            `protobuf:"bytes,11,opt,name=web_title,json=webTitle,proto3" json:"web_title,omitempty"`
	ShareInfo            *ChannelListeningCheckInShareInfo `protobuf:"bytes,12,opt,name=share_info,json=shareInfo,proto3" json:"share_info,omitempty"`
	Uid                  uint32                            `protobuf:"varint,13,opt,name=uid,proto3" json:"uid,omitempty"`
	DisplayId            uint32                            `protobuf:"varint,14,opt,name=display_id,json=displayId,proto3" json:"display_id,omitempty"`
	ChannelViewId        string                            `protobuf:"bytes,15,opt,name=channel_view_id,json=channelViewId,proto3" json:"channel_view_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *ChannelListeningCheckInShareCard) Reset()         { *m = ChannelListeningCheckInShareCard{} }
func (m *ChannelListeningCheckInShareCard) String() string { return proto.CompactTextString(m) }
func (*ChannelListeningCheckInShareCard) ProtoMessage()    {}
func (*ChannelListeningCheckInShareCard) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{60}
}
func (m *ChannelListeningCheckInShareCard) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelListeningCheckInShareCard.Unmarshal(m, b)
}
func (m *ChannelListeningCheckInShareCard) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelListeningCheckInShareCard.Marshal(b, m, deterministic)
}
func (dst *ChannelListeningCheckInShareCard) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelListeningCheckInShareCard.Merge(dst, src)
}
func (m *ChannelListeningCheckInShareCard) XXX_Size() int {
	return xxx_messageInfo_ChannelListeningCheckInShareCard.Size(m)
}
func (m *ChannelListeningCheckInShareCard) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelListeningCheckInShareCard.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelListeningCheckInShareCard proto.InternalMessageInfo

func (m *ChannelListeningCheckInShareCard) GetCheckInTitleDesc() string {
	if m != nil {
		return m.CheckInTitleDesc
	}
	return ""
}

func (m *ChannelListeningCheckInShareCard) GetShowTime() int64 {
	if m != nil {
		return m.ShowTime
	}
	return 0
}

func (m *ChannelListeningCheckInShareCard) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ChannelListeningCheckInShareCard) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *ChannelListeningCheckInShareCard) GetLittleTitle() string {
	if m != nil {
		return m.LittleTitle
	}
	return ""
}

func (m *ChannelListeningCheckInShareCard) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ChannelListeningCheckInShareCard) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelListeningCheckInShareCard) GetCardImg() string {
	if m != nil {
		return m.CardImg
	}
	return ""
}

func (m *ChannelListeningCheckInShareCard) GetWebBg() string {
	if m != nil {
		return m.WebBg
	}
	return ""
}

func (m *ChannelListeningCheckInShareCard) GetBtDesc() string {
	if m != nil {
		return m.BtDesc
	}
	return ""
}

func (m *ChannelListeningCheckInShareCard) GetWebTitle() string {
	if m != nil {
		return m.WebTitle
	}
	return ""
}

func (m *ChannelListeningCheckInShareCard) GetShareInfo() *ChannelListeningCheckInShareInfo {
	if m != nil {
		return m.ShareInfo
	}
	return nil
}

func (m *ChannelListeningCheckInShareCard) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelListeningCheckInShareCard) GetDisplayId() uint32 {
	if m != nil {
		return m.DisplayId
	}
	return 0
}

func (m *ChannelListeningCheckInShareCard) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

type GetChannelListeningCheckInShareCardReq struct {
	ShareId              string   `protobuf:"bytes,1,opt,name=share_id,json=shareId,proto3" json:"share_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelListeningCheckInShareCardReq) Reset() {
	*m = GetChannelListeningCheckInShareCardReq{}
}
func (m *GetChannelListeningCheckInShareCardReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelListeningCheckInShareCardReq) ProtoMessage()    {}
func (*GetChannelListeningCheckInShareCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{61}
}
func (m *GetChannelListeningCheckInShareCardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelListeningCheckInShareCardReq.Unmarshal(m, b)
}
func (m *GetChannelListeningCheckInShareCardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelListeningCheckInShareCardReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelListeningCheckInShareCardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelListeningCheckInShareCardReq.Merge(dst, src)
}
func (m *GetChannelListeningCheckInShareCardReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelListeningCheckInShareCardReq.Size(m)
}
func (m *GetChannelListeningCheckInShareCardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelListeningCheckInShareCardReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelListeningCheckInShareCardReq proto.InternalMessageInfo

func (m *GetChannelListeningCheckInShareCardReq) GetShareId() string {
	if m != nil {
		return m.ShareId
	}
	return ""
}

type GetChannelListeningCheckInShareCardResp struct {
	ShareCard            *ChannelListeningCheckInShareCard `protobuf:"bytes,1,opt,name=share_card,json=shareCard,proto3" json:"share_card,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *GetChannelListeningCheckInShareCardResp) Reset() {
	*m = GetChannelListeningCheckInShareCardResp{}
}
func (m *GetChannelListeningCheckInShareCardResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelListeningCheckInShareCardResp) ProtoMessage()    {}
func (*GetChannelListeningCheckInShareCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{62}
}
func (m *GetChannelListeningCheckInShareCardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelListeningCheckInShareCardResp.Unmarshal(m, b)
}
func (m *GetChannelListeningCheckInShareCardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelListeningCheckInShareCardResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelListeningCheckInShareCardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelListeningCheckInShareCardResp.Merge(dst, src)
}
func (m *GetChannelListeningCheckInShareCardResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelListeningCheckInShareCardResp.Size(m)
}
func (m *GetChannelListeningCheckInShareCardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelListeningCheckInShareCardResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelListeningCheckInShareCardResp proto.InternalMessageInfo

func (m *GetChannelListeningCheckInShareCardResp) GetShareCard() *ChannelListeningCheckInShareCard {
	if m != nil {
		return m.ShareCard
	}
	return nil
}

type ChannelListeningCheckInShareInfo struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	SubTitle             string   `protobuf:"bytes,3,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	Img                  string   `protobuf:"bytes,4,opt,name=img,proto3" json:"img,omitempty"`
	ShareUrl             string   `protobuf:"bytes,5,opt,name=share_url,json=shareUrl,proto3" json:"share_url,omitempty"`
	NewSubTitle          string   `protobuf:"bytes,6,opt,name=new_sub_title,json=newSubTitle,proto3" json:"new_sub_title,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelListeningCheckInShareInfo) Reset()         { *m = ChannelListeningCheckInShareInfo{} }
func (m *ChannelListeningCheckInShareInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelListeningCheckInShareInfo) ProtoMessage()    {}
func (*ChannelListeningCheckInShareInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{63}
}
func (m *ChannelListeningCheckInShareInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelListeningCheckInShareInfo.Unmarshal(m, b)
}
func (m *ChannelListeningCheckInShareInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelListeningCheckInShareInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelListeningCheckInShareInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelListeningCheckInShareInfo.Merge(dst, src)
}
func (m *ChannelListeningCheckInShareInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelListeningCheckInShareInfo.Size(m)
}
func (m *ChannelListeningCheckInShareInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelListeningCheckInShareInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelListeningCheckInShareInfo proto.InternalMessageInfo

func (m *ChannelListeningCheckInShareInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ChannelListeningCheckInShareInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ChannelListeningCheckInShareInfo) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *ChannelListeningCheckInShareInfo) GetImg() string {
	if m != nil {
		return m.Img
	}
	return ""
}

func (m *ChannelListeningCheckInShareInfo) GetShareUrl() string {
	if m != nil {
		return m.ShareUrl
	}
	return ""
}

func (m *ChannelListeningCheckInShareInfo) GetNewSubTitle() string {
	if m != nil {
		return m.NewSubTitle
	}
	return ""
}

type GetListeningCheckInShareCardV3Req struct {
	ShareId              string   `protobuf:"bytes,1,opt,name=share_id,json=shareId,proto3" json:"share_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetListeningCheckInShareCardV3Req) Reset()         { *m = GetListeningCheckInShareCardV3Req{} }
func (m *GetListeningCheckInShareCardV3Req) String() string { return proto.CompactTextString(m) }
func (*GetListeningCheckInShareCardV3Req) ProtoMessage()    {}
func (*GetListeningCheckInShareCardV3Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{64}
}
func (m *GetListeningCheckInShareCardV3Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetListeningCheckInShareCardV3Req.Unmarshal(m, b)
}
func (m *GetListeningCheckInShareCardV3Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetListeningCheckInShareCardV3Req.Marshal(b, m, deterministic)
}
func (dst *GetListeningCheckInShareCardV3Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetListeningCheckInShareCardV3Req.Merge(dst, src)
}
func (m *GetListeningCheckInShareCardV3Req) XXX_Size() int {
	return xxx_messageInfo_GetListeningCheckInShareCardV3Req.Size(m)
}
func (m *GetListeningCheckInShareCardV3Req) XXX_DiscardUnknown() {
	xxx_messageInfo_GetListeningCheckInShareCardV3Req.DiscardUnknown(m)
}

var xxx_messageInfo_GetListeningCheckInShareCardV3Req proto.InternalMessageInfo

func (m *GetListeningCheckInShareCardV3Req) GetShareId() string {
	if m != nil {
		return m.ShareId
	}
	return ""
}

type GetListeningCheckInShareCardV3Resp struct {
	ShareCard            *ChannelListeningShareCardV3 `protobuf:"bytes,1,opt,name=share_card,json=shareCard,proto3" json:"share_card,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *GetListeningCheckInShareCardV3Resp) Reset()         { *m = GetListeningCheckInShareCardV3Resp{} }
func (m *GetListeningCheckInShareCardV3Resp) String() string { return proto.CompactTextString(m) }
func (*GetListeningCheckInShareCardV3Resp) ProtoMessage()    {}
func (*GetListeningCheckInShareCardV3Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{65}
}
func (m *GetListeningCheckInShareCardV3Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetListeningCheckInShareCardV3Resp.Unmarshal(m, b)
}
func (m *GetListeningCheckInShareCardV3Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetListeningCheckInShareCardV3Resp.Marshal(b, m, deterministic)
}
func (dst *GetListeningCheckInShareCardV3Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetListeningCheckInShareCardV3Resp.Merge(dst, src)
}
func (m *GetListeningCheckInShareCardV3Resp) XXX_Size() int {
	return xxx_messageInfo_GetListeningCheckInShareCardV3Resp.Size(m)
}
func (m *GetListeningCheckInShareCardV3Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetListeningCheckInShareCardV3Resp.DiscardUnknown(m)
}

var xxx_messageInfo_GetListeningCheckInShareCardV3Resp proto.InternalMessageInfo

func (m *GetListeningCheckInShareCardV3Resp) GetShareCard() *ChannelListeningShareCardV3 {
	if m != nil {
		return m.ShareCard
	}
	return nil
}

// 获取打卡榜
type GetListeningUserCheckInInfoListReq struct {
	ChannelId            uint32                    `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32                    `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Count                uint32                    `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	LoadMore             *ChannelListeningLoadMore `protobuf:"bytes,4,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetListeningUserCheckInInfoListReq) Reset()         { *m = GetListeningUserCheckInInfoListReq{} }
func (m *GetListeningUserCheckInInfoListReq) String() string { return proto.CompactTextString(m) }
func (*GetListeningUserCheckInInfoListReq) ProtoMessage()    {}
func (*GetListeningUserCheckInInfoListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{66}
}
func (m *GetListeningUserCheckInInfoListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetListeningUserCheckInInfoListReq.Unmarshal(m, b)
}
func (m *GetListeningUserCheckInInfoListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetListeningUserCheckInInfoListReq.Marshal(b, m, deterministic)
}
func (dst *GetListeningUserCheckInInfoListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetListeningUserCheckInInfoListReq.Merge(dst, src)
}
func (m *GetListeningUserCheckInInfoListReq) XXX_Size() int {
	return xxx_messageInfo_GetListeningUserCheckInInfoListReq.Size(m)
}
func (m *GetListeningUserCheckInInfoListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetListeningUserCheckInInfoListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetListeningUserCheckInInfoListReq proto.InternalMessageInfo

func (m *GetListeningUserCheckInInfoListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetListeningUserCheckInInfoListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetListeningUserCheckInInfoListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetListeningUserCheckInInfoListReq) GetLoadMore() *ChannelListeningLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type GetListeningUserCheckInInfoListResp struct {
	List                 []*UserCheckInInfo        `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Info                 *UserCheckInInfo          `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	LoadMore             *ChannelListeningLoadMore `protobuf:"bytes,3,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetListeningUserCheckInInfoListResp) Reset()         { *m = GetListeningUserCheckInInfoListResp{} }
func (m *GetListeningUserCheckInInfoListResp) String() string { return proto.CompactTextString(m) }
func (*GetListeningUserCheckInInfoListResp) ProtoMessage()    {}
func (*GetListeningUserCheckInInfoListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{67}
}
func (m *GetListeningUserCheckInInfoListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetListeningUserCheckInInfoListResp.Unmarshal(m, b)
}
func (m *GetListeningUserCheckInInfoListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetListeningUserCheckInInfoListResp.Marshal(b, m, deterministic)
}
func (dst *GetListeningUserCheckInInfoListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetListeningUserCheckInInfoListResp.Merge(dst, src)
}
func (m *GetListeningUserCheckInInfoListResp) XXX_Size() int {
	return xxx_messageInfo_GetListeningUserCheckInInfoListResp.Size(m)
}
func (m *GetListeningUserCheckInInfoListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetListeningUserCheckInInfoListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetListeningUserCheckInInfoListResp proto.InternalMessageInfo

func (m *GetListeningUserCheckInInfoListResp) GetList() []*UserCheckInInfo {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetListeningUserCheckInInfoListResp) GetInfo() *UserCheckInInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *GetListeningUserCheckInInfoListResp) GetLoadMore() *ChannelListeningLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type UserCheckInInfo struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Sex                  uint32   `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	Rank                 uint32   `protobuf:"varint,4,opt,name=rank,proto3" json:"rank,omitempty"`
	Day                  uint32   `protobuf:"varint,5,opt,name=day,proto3" json:"day,omitempty"`
	CheckInTitle         string   `protobuf:"bytes,6,opt,name=check_in_title,json=checkInTitle,proto3" json:"check_in_title,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserCheckInInfo) Reset()         { *m = UserCheckInInfo{} }
func (m *UserCheckInInfo) String() string { return proto.CompactTextString(m) }
func (*UserCheckInInfo) ProtoMessage()    {}
func (*UserCheckInInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{68}
}
func (m *UserCheckInInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserCheckInInfo.Unmarshal(m, b)
}
func (m *UserCheckInInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserCheckInInfo.Marshal(b, m, deterministic)
}
func (dst *UserCheckInInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserCheckInInfo.Merge(dst, src)
}
func (m *UserCheckInInfo) XXX_Size() int {
	return xxx_messageInfo_UserCheckInInfo.Size(m)
}
func (m *UserCheckInInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserCheckInInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserCheckInInfo proto.InternalMessageInfo

func (m *UserCheckInInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserCheckInInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserCheckInInfo) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *UserCheckInInfo) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *UserCheckInInfo) GetDay() uint32 {
	if m != nil {
		return m.Day
	}
	return 0
}

func (m *UserCheckInInfo) GetCheckInTitle() string {
	if m != nil {
		return m.CheckInTitle
	}
	return ""
}

// 获取当天在房详情
type GetSharedInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSharedInfoReq) Reset()         { *m = GetSharedInfoReq{} }
func (m *GetSharedInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetSharedInfoReq) ProtoMessage()    {}
func (*GetSharedInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{69}
}
func (m *GetSharedInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSharedInfoReq.Unmarshal(m, b)
}
func (m *GetSharedInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSharedInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetSharedInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSharedInfoReq.Merge(dst, src)
}
func (m *GetSharedInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetSharedInfoReq.Size(m)
}
func (m *GetSharedInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSharedInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSharedInfoReq proto.InternalMessageInfo

func (m *GetSharedInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetSharedInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetSharedInfoResp struct {
	Title                string          `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	BgUrl                string          `protobuf:"bytes,2,opt,name=bg_url,json=bgUrl,proto3" json:"bg_url,omitempty"`
	Text                 string          `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	CardUrl              string          `protobuf:"bytes,4,opt,name=card_url,json=cardUrl,proto3" json:"card_url,omitempty"`
	Theme                string          `protobuf:"bytes,5,opt,name=theme,proto3" json:"theme,omitempty"`
	Mood                 *SharedInfoMood `protobuf:"bytes,6,opt,name=mood,proto3" json:"mood,omitempty"`
	NewBgUrl             string          `protobuf:"bytes,7,opt,name=new_bg_url,json=newBgUrl,proto3" json:"new_bg_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetSharedInfoResp) Reset()         { *m = GetSharedInfoResp{} }
func (m *GetSharedInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetSharedInfoResp) ProtoMessage()    {}
func (*GetSharedInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{70}
}
func (m *GetSharedInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSharedInfoResp.Unmarshal(m, b)
}
func (m *GetSharedInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSharedInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetSharedInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSharedInfoResp.Merge(dst, src)
}
func (m *GetSharedInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetSharedInfoResp.Size(m)
}
func (m *GetSharedInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSharedInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSharedInfoResp proto.InternalMessageInfo

func (m *GetSharedInfoResp) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GetSharedInfoResp) GetBgUrl() string {
	if m != nil {
		return m.BgUrl
	}
	return ""
}

func (m *GetSharedInfoResp) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *GetSharedInfoResp) GetCardUrl() string {
	if m != nil {
		return m.CardUrl
	}
	return ""
}

func (m *GetSharedInfoResp) GetTheme() string {
	if m != nil {
		return m.Theme
	}
	return ""
}

func (m *GetSharedInfoResp) GetMood() *SharedInfoMood {
	if m != nil {
		return m.Mood
	}
	return nil
}

func (m *GetSharedInfoResp) GetNewBgUrl() string {
	if m != nil {
		return m.NewBgUrl
	}
	return ""
}

type SharedInfoMood struct {
	HandLottie           string   `protobuf:"bytes,1,opt,name=hand_lottie,json=handLottie,proto3" json:"hand_lottie,omitempty"`
	FootLottie           string   `protobuf:"bytes,2,opt,name=foot_lottie,json=footLottie,proto3" json:"foot_lottie,omitempty"`
	Bg                   string   `protobuf:"bytes,3,opt,name=bg,proto3" json:"bg,omitempty"`
	MoodName             string   `protobuf:"bytes,4,opt,name=mood_name,json=moodName,proto3" json:"mood_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SharedInfoMood) Reset()         { *m = SharedInfoMood{} }
func (m *SharedInfoMood) String() string { return proto.CompactTextString(m) }
func (*SharedInfoMood) ProtoMessage()    {}
func (*SharedInfoMood) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{71}
}
func (m *SharedInfoMood) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SharedInfoMood.Unmarshal(m, b)
}
func (m *SharedInfoMood) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SharedInfoMood.Marshal(b, m, deterministic)
}
func (dst *SharedInfoMood) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SharedInfoMood.Merge(dst, src)
}
func (m *SharedInfoMood) XXX_Size() int {
	return xxx_messageInfo_SharedInfoMood.Size(m)
}
func (m *SharedInfoMood) XXX_DiscardUnknown() {
	xxx_messageInfo_SharedInfoMood.DiscardUnknown(m)
}

var xxx_messageInfo_SharedInfoMood proto.InternalMessageInfo

func (m *SharedInfoMood) GetHandLottie() string {
	if m != nil {
		return m.HandLottie
	}
	return ""
}

func (m *SharedInfoMood) GetFootLottie() string {
	if m != nil {
		return m.FootLottie
	}
	return ""
}

func (m *SharedInfoMood) GetBg() string {
	if m != nil {
		return m.Bg
	}
	return ""
}

func (m *SharedInfoMood) GetMoodName() string {
	if m != nil {
		return m.MoodName
	}
	return ""
}

type BatchGetCheckInInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uids                 []uint32 `protobuf:"varint,2,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetCheckInInfoReq) Reset()         { *m = BatchGetCheckInInfoReq{} }
func (m *BatchGetCheckInInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetCheckInInfoReq) ProtoMessage()    {}
func (*BatchGetCheckInInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{72}
}
func (m *BatchGetCheckInInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetCheckInInfoReq.Unmarshal(m, b)
}
func (m *BatchGetCheckInInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetCheckInInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetCheckInInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetCheckInInfoReq.Merge(dst, src)
}
func (m *BatchGetCheckInInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetCheckInInfoReq.Size(m)
}
func (m *BatchGetCheckInInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetCheckInInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetCheckInInfoReq proto.InternalMessageInfo

func (m *BatchGetCheckInInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BatchGetCheckInInfoReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type BatchGetCheckInInfoResp struct {
	Info                 map[uint32]*ChannelListeningCheckInInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                                `json:"-"`
	XXX_unrecognized     []byte                                  `json:"-"`
	XXX_sizecache        int32                                   `json:"-"`
}

func (m *BatchGetCheckInInfoResp) Reset()         { *m = BatchGetCheckInInfoResp{} }
func (m *BatchGetCheckInInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetCheckInInfoResp) ProtoMessage()    {}
func (*BatchGetCheckInInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{73}
}
func (m *BatchGetCheckInInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetCheckInInfoResp.Unmarshal(m, b)
}
func (m *BatchGetCheckInInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetCheckInInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetCheckInInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetCheckInInfoResp.Merge(dst, src)
}
func (m *BatchGetCheckInInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetCheckInInfoResp.Size(m)
}
func (m *BatchGetCheckInInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetCheckInInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetCheckInInfoResp proto.InternalMessageInfo

func (m *BatchGetCheckInInfoResp) GetInfo() map[uint32]*ChannelListeningCheckInInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type GetUserFlowerReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserFlowerReq) Reset()         { *m = GetUserFlowerReq{} }
func (m *GetUserFlowerReq) String() string { return proto.CompactTextString(m) }
func (*GetUserFlowerReq) ProtoMessage()    {}
func (*GetUserFlowerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{74}
}
func (m *GetUserFlowerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserFlowerReq.Unmarshal(m, b)
}
func (m *GetUserFlowerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserFlowerReq.Marshal(b, m, deterministic)
}
func (dst *GetUserFlowerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserFlowerReq.Merge(dst, src)
}
func (m *GetUserFlowerReq) XXX_Size() int {
	return xxx_messageInfo_GetUserFlowerReq.Size(m)
}
func (m *GetUserFlowerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserFlowerReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserFlowerReq proto.InternalMessageInfo

func (m *GetUserFlowerReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetUserFlowerReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserFlowerResp struct {
	Flower               *UserFlower `protobuf:"bytes,1,opt,name=flower,proto3" json:"flower,omitempty"`
	FlowerName           string      `protobuf:"bytes,2,opt,name=flower_name,json=flowerName,proto3" json:"flower_name,omitempty"`
	Unit                 string      `protobuf:"bytes,3,opt,name=unit,proto3" json:"unit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetUserFlowerResp) Reset()         { *m = GetUserFlowerResp{} }
func (m *GetUserFlowerResp) String() string { return proto.CompactTextString(m) }
func (*GetUserFlowerResp) ProtoMessage()    {}
func (*GetUserFlowerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{75}
}
func (m *GetUserFlowerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserFlowerResp.Unmarshal(m, b)
}
func (m *GetUserFlowerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserFlowerResp.Marshal(b, m, deterministic)
}
func (dst *GetUserFlowerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserFlowerResp.Merge(dst, src)
}
func (m *GetUserFlowerResp) XXX_Size() int {
	return xxx_messageInfo_GetUserFlowerResp.Size(m)
}
func (m *GetUserFlowerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserFlowerResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserFlowerResp proto.InternalMessageInfo

func (m *GetUserFlowerResp) GetFlower() *UserFlower {
	if m != nil {
		return m.Flower
	}
	return nil
}

func (m *GetUserFlowerResp) GetFlowerName() string {
	if m != nil {
		return m.FlowerName
	}
	return ""
}

func (m *GetUserFlowerResp) GetUnit() string {
	if m != nil {
		return m.Unit
	}
	return ""
}

type BatchGetUserFlowerReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	UidList              []uint32 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetUserFlowerReq) Reset()         { *m = BatchGetUserFlowerReq{} }
func (m *BatchGetUserFlowerReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserFlowerReq) ProtoMessage()    {}
func (*BatchGetUserFlowerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{76}
}
func (m *BatchGetUserFlowerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserFlowerReq.Unmarshal(m, b)
}
func (m *BatchGetUserFlowerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserFlowerReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserFlowerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserFlowerReq.Merge(dst, src)
}
func (m *BatchGetUserFlowerReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserFlowerReq.Size(m)
}
func (m *BatchGetUserFlowerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserFlowerReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserFlowerReq proto.InternalMessageInfo

func (m *BatchGetUserFlowerReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BatchGetUserFlowerReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchGetUserFlowerResp struct {
	UserFlowerMap        map[uint32]*UserFlower `protobuf:"bytes,1,rep,name=user_flower_map,json=userFlowerMap,proto3" json:"user_flower_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	FlowerName           string                 `protobuf:"bytes,2,opt,name=flower_name,json=flowerName,proto3" json:"flower_name,omitempty"`
	Unit                 string                 `protobuf:"bytes,3,opt,name=unit,proto3" json:"unit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *BatchGetUserFlowerResp) Reset()         { *m = BatchGetUserFlowerResp{} }
func (m *BatchGetUserFlowerResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserFlowerResp) ProtoMessage()    {}
func (*BatchGetUserFlowerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{77}
}
func (m *BatchGetUserFlowerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserFlowerResp.Unmarshal(m, b)
}
func (m *BatchGetUserFlowerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserFlowerResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserFlowerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserFlowerResp.Merge(dst, src)
}
func (m *BatchGetUserFlowerResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserFlowerResp.Size(m)
}
func (m *BatchGetUserFlowerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserFlowerResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserFlowerResp proto.InternalMessageInfo

func (m *BatchGetUserFlowerResp) GetUserFlowerMap() map[uint32]*UserFlower {
	if m != nil {
		return m.UserFlowerMap
	}
	return nil
}

func (m *BatchGetUserFlowerResp) GetFlowerName() string {
	if m != nil {
		return m.FlowerName
	}
	return ""
}

func (m *BatchGetUserFlowerResp) GetUnit() string {
	if m != nil {
		return m.Unit
	}
	return ""
}

type MusicBook struct {
	RhythmPoint          map[uint32]uint32 `protobuf:"bytes,1,rep,name=rhythm_point,json=rhythmPoint,proto3" json:"rhythm_point,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *MusicBook) Reset()         { *m = MusicBook{} }
func (m *MusicBook) String() string { return proto.CompactTextString(m) }
func (*MusicBook) ProtoMessage()    {}
func (*MusicBook) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{78}
}
func (m *MusicBook) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicBook.Unmarshal(m, b)
}
func (m *MusicBook) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicBook.Marshal(b, m, deterministic)
}
func (dst *MusicBook) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicBook.Merge(dst, src)
}
func (m *MusicBook) XXX_Size() int {
	return xxx_messageInfo_MusicBook.Size(m)
}
func (m *MusicBook) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicBook.DiscardUnknown(m)
}

var xxx_messageInfo_MusicBook proto.InternalMessageInfo

func (m *MusicBook) GetRhythmPoint() map[uint32]uint32 {
	if m != nil {
		return m.RhythmPoint
	}
	return nil
}

type ListeningMood struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Text                 string   `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	Img                  string   `protobuf:"bytes,3,opt,name=img,proto3" json:"img,omitempty"`
	Lottie1Url           string   `protobuf:"bytes,4,opt,name=lottie1_url,json=lottie1Url,proto3" json:"lottie1_url,omitempty"`
	Lottie1Md5           string   `protobuf:"bytes,5,opt,name=lottie1_md5,json=lottie1Md5,proto3" json:"lottie1_md5,omitempty"`
	Lottie2Url           string   `protobuf:"bytes,6,opt,name=lottie2_url,json=lottie2Url,proto3" json:"lottie2_url,omitempty"`
	Lottie2Md5           string   `protobuf:"bytes,7,opt,name=lottie2_md5,json=lottie2Md5,proto3" json:"lottie2_md5,omitempty"`
	LottieCompleteUrl    string   `protobuf:"bytes,8,opt,name=lottie_complete_url,json=lottieCompleteUrl,proto3" json:"lottie_complete_url,omitempty"`
	LottieCompleteMd5    string   `protobuf:"bytes,9,opt,name=lottie_complete_md5,json=lottieCompleteMd5,proto3" json:"lottie_complete_md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListeningMood) Reset()         { *m = ListeningMood{} }
func (m *ListeningMood) String() string { return proto.CompactTextString(m) }
func (*ListeningMood) ProtoMessage()    {}
func (*ListeningMood) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{79}
}
func (m *ListeningMood) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningMood.Unmarshal(m, b)
}
func (m *ListeningMood) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningMood.Marshal(b, m, deterministic)
}
func (dst *ListeningMood) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningMood.Merge(dst, src)
}
func (m *ListeningMood) XXX_Size() int {
	return xxx_messageInfo_ListeningMood.Size(m)
}
func (m *ListeningMood) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningMood.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningMood proto.InternalMessageInfo

func (m *ListeningMood) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ListeningMood) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *ListeningMood) GetImg() string {
	if m != nil {
		return m.Img
	}
	return ""
}

func (m *ListeningMood) GetLottie1Url() string {
	if m != nil {
		return m.Lottie1Url
	}
	return ""
}

func (m *ListeningMood) GetLottie1Md5() string {
	if m != nil {
		return m.Lottie1Md5
	}
	return ""
}

func (m *ListeningMood) GetLottie2Url() string {
	if m != nil {
		return m.Lottie2Url
	}
	return ""
}

func (m *ListeningMood) GetLottie2Md5() string {
	if m != nil {
		return m.Lottie2Md5
	}
	return ""
}

func (m *ListeningMood) GetLottieCompleteUrl() string {
	if m != nil {
		return m.LottieCompleteUrl
	}
	return ""
}

func (m *ListeningMood) GetLottieCompleteMd5() string {
	if m != nil {
		return m.LottieCompleteMd5
	}
	return ""
}

type GetAllMoodCfgReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllMoodCfgReq) Reset()         { *m = GetAllMoodCfgReq{} }
func (m *GetAllMoodCfgReq) String() string { return proto.CompactTextString(m) }
func (*GetAllMoodCfgReq) ProtoMessage()    {}
func (*GetAllMoodCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{80}
}
func (m *GetAllMoodCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllMoodCfgReq.Unmarshal(m, b)
}
func (m *GetAllMoodCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllMoodCfgReq.Marshal(b, m, deterministic)
}
func (dst *GetAllMoodCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllMoodCfgReq.Merge(dst, src)
}
func (m *GetAllMoodCfgReq) XXX_Size() int {
	return xxx_messageInfo_GetAllMoodCfgReq.Size(m)
}
func (m *GetAllMoodCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllMoodCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllMoodCfgReq proto.InternalMessageInfo

type GetAllMoodCfgResp struct {
	Moods                []*ListeningMood `protobuf:"bytes,1,rep,name=moods,proto3" json:"moods,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetAllMoodCfgResp) Reset()         { *m = GetAllMoodCfgResp{} }
func (m *GetAllMoodCfgResp) String() string { return proto.CompactTextString(m) }
func (*GetAllMoodCfgResp) ProtoMessage()    {}
func (*GetAllMoodCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{81}
}
func (m *GetAllMoodCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllMoodCfgResp.Unmarshal(m, b)
}
func (m *GetAllMoodCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllMoodCfgResp.Marshal(b, m, deterministic)
}
func (dst *GetAllMoodCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllMoodCfgResp.Merge(dst, src)
}
func (m *GetAllMoodCfgResp) XXX_Size() int {
	return xxx_messageInfo_GetAllMoodCfgResp.Size(m)
}
func (m *GetAllMoodCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllMoodCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllMoodCfgResp proto.InternalMessageInfo

func (m *GetAllMoodCfgResp) GetMoods() []*ListeningMood {
	if m != nil {
		return m.Moods
	}
	return nil
}

type SetListeningUserMoodReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	MoodId               string   `protobuf:"bytes,3,opt,name=mood_id,json=moodId,proto3" json:"mood_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetListeningUserMoodReq) Reset()         { *m = SetListeningUserMoodReq{} }
func (m *SetListeningUserMoodReq) String() string { return proto.CompactTextString(m) }
func (*SetListeningUserMoodReq) ProtoMessage()    {}
func (*SetListeningUserMoodReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{82}
}
func (m *SetListeningUserMoodReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetListeningUserMoodReq.Unmarshal(m, b)
}
func (m *SetListeningUserMoodReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetListeningUserMoodReq.Marshal(b, m, deterministic)
}
func (dst *SetListeningUserMoodReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetListeningUserMoodReq.Merge(dst, src)
}
func (m *SetListeningUserMoodReq) XXX_Size() int {
	return xxx_messageInfo_SetListeningUserMoodReq.Size(m)
}
func (m *SetListeningUserMoodReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetListeningUserMoodReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetListeningUserMoodReq proto.InternalMessageInfo

func (m *SetListeningUserMoodReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetListeningUserMoodReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetListeningUserMoodReq) GetMoodId() string {
	if m != nil {
		return m.MoodId
	}
	return ""
}

type SetListeningUserMoodResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetListeningUserMoodResp) Reset()         { *m = SetListeningUserMoodResp{} }
func (m *SetListeningUserMoodResp) String() string { return proto.CompactTextString(m) }
func (*SetListeningUserMoodResp) ProtoMessage()    {}
func (*SetListeningUserMoodResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{83}
}
func (m *SetListeningUserMoodResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetListeningUserMoodResp.Unmarshal(m, b)
}
func (m *SetListeningUserMoodResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetListeningUserMoodResp.Marshal(b, m, deterministic)
}
func (dst *SetListeningUserMoodResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetListeningUserMoodResp.Merge(dst, src)
}
func (m *SetListeningUserMoodResp) XXX_Size() int {
	return xxx_messageInfo_SetListeningUserMoodResp.Size(m)
}
func (m *SetListeningUserMoodResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetListeningUserMoodResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetListeningUserMoodResp proto.InternalMessageInfo

type GetAllListeningOnMicUserMoodReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllListeningOnMicUserMoodReq) Reset()         { *m = GetAllListeningOnMicUserMoodReq{} }
func (m *GetAllListeningOnMicUserMoodReq) String() string { return proto.CompactTextString(m) }
func (*GetAllListeningOnMicUserMoodReq) ProtoMessage()    {}
func (*GetAllListeningOnMicUserMoodReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{84}
}
func (m *GetAllListeningOnMicUserMoodReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllListeningOnMicUserMoodReq.Unmarshal(m, b)
}
func (m *GetAllListeningOnMicUserMoodReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllListeningOnMicUserMoodReq.Marshal(b, m, deterministic)
}
func (dst *GetAllListeningOnMicUserMoodReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllListeningOnMicUserMoodReq.Merge(dst, src)
}
func (m *GetAllListeningOnMicUserMoodReq) XXX_Size() int {
	return xxx_messageInfo_GetAllListeningOnMicUserMoodReq.Size(m)
}
func (m *GetAllListeningOnMicUserMoodReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllListeningOnMicUserMoodReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllListeningOnMicUserMoodReq proto.InternalMessageInfo

func (m *GetAllListeningOnMicUserMoodReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetAllListeningOnMicUserMoodReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetAllListeningOnMicUserMoodResp struct {
	UserMood             map[uint32]*ListeningMood `protobuf:"bytes,1,rep,name=user_mood,json=userMood,proto3" json:"user_mood,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetAllListeningOnMicUserMoodResp) Reset()         { *m = GetAllListeningOnMicUserMoodResp{} }
func (m *GetAllListeningOnMicUserMoodResp) String() string { return proto.CompactTextString(m) }
func (*GetAllListeningOnMicUserMoodResp) ProtoMessage()    {}
func (*GetAllListeningOnMicUserMoodResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_e507ccca8f7f266c, []int{85}
}
func (m *GetAllListeningOnMicUserMoodResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllListeningOnMicUserMoodResp.Unmarshal(m, b)
}
func (m *GetAllListeningOnMicUserMoodResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllListeningOnMicUserMoodResp.Marshal(b, m, deterministic)
}
func (dst *GetAllListeningOnMicUserMoodResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllListeningOnMicUserMoodResp.Merge(dst, src)
}
func (m *GetAllListeningOnMicUserMoodResp) XXX_Size() int {
	return xxx_messageInfo_GetAllListeningOnMicUserMoodResp.Size(m)
}
func (m *GetAllListeningOnMicUserMoodResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllListeningOnMicUserMoodResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllListeningOnMicUserMoodResp proto.InternalMessageInfo

func (m *GetAllListeningOnMicUserMoodResp) GetUserMood() map[uint32]*ListeningMood {
	if m != nil {
		return m.UserMood
	}
	return nil
}

func init() {
	proto.RegisterType((*ListeningDropCardV3Req)(nil), "channellistening.ListeningDropCardV3Req")
	proto.RegisterType((*ListeningDropCardV3Resp)(nil), "channellistening.ListeningDropCardV3Resp")
	proto.RegisterType((*LikeSongReq)(nil), "channellistening.LikeSongReq")
	proto.RegisterType((*LikeSongResp)(nil), "channellistening.LikeSongResp")
	proto.RegisterType((*ChannelListeningSong)(nil), "channellistening.ChannelListeningSong")
	proto.RegisterType((*GetBeLikedSongListReq)(nil), "channellistening.GetBeLikedSongListReq")
	proto.RegisterType((*GetBeLikedSongListResp)(nil), "channellistening.GetBeLikedSongListResp")
	proto.RegisterType((*BeLikedSong)(nil), "channellistening.BeLikedSong")
	proto.RegisterType((*GetLikedSongListReq)(nil), "channellistening.GetLikedSongListReq")
	proto.RegisterType((*GetLikedSongListResp)(nil), "channellistening.GetLikedSongListResp")
	proto.RegisterType((*ChannelListeningLoadMore)(nil), "channellistening.ChannelListeningLoadMore")
	proto.RegisterType((*GetChannelListeningSimpleInfoReq)(nil), "channellistening.GetChannelListeningSimpleInfoReq")
	proto.RegisterType((*GetChannelListeningSimpleInfoResp)(nil), "channellistening.GetChannelListeningSimpleInfoResp")
	proto.RegisterType((*ChannelListeningCheckInInfo)(nil), "channellistening.ChannelListeningCheckInInfo")
	proto.RegisterType((*GetUserFlowerListReq)(nil), "channellistening.GetUserFlowerListReq")
	proto.RegisterType((*GetUserFlowerListResp)(nil), "channellistening.GetUserFlowerListResp")
	proto.RegisterType((*UserFlower)(nil), "channellistening.UserFlower")
	proto.RegisterType((*GetFlowerDetailReq)(nil), "channellistening.GetFlowerDetailReq")
	proto.RegisterType((*GetFlowerDetailResp)(nil), "channellistening.GetFlowerDetailResp")
	proto.RegisterType((*FlowerDetail)(nil), "channellistening.FlowerDetail")
	proto.RegisterType((*ChannelListenTheme)(nil), "channellistening.ChannelListenTheme")
	proto.RegisterType((*AddSongMenuReq)(nil), "channellistening.AddSongMenuReq")
	proto.RegisterType((*AddSongMenuResp)(nil), "channellistening.AddSongMenuResp")
	proto.RegisterType((*SongMenu)(nil), "channellistening.SongMenu")
	proto.RegisterType((*GetAllSongMenuReq)(nil), "channellistening.GetAllSongMenuReq")
	proto.RegisterType((*GetAllSongMenuResp)(nil), "channellistening.GetAllSongMenuResp")
	proto.RegisterType((*UpdateSongMenuReq)(nil), "channellistening.UpdateSongMenuReq")
	proto.RegisterType((*UpdateSongMenuResp)(nil), "channellistening.UpdateSongMenuResp")
	proto.RegisterType((*ReorderSongMenuReq)(nil), "channellistening.ReorderSongMenuReq")
	proto.RegisterType((*ReorderSongMenuResp)(nil), "channellistening.ReorderSongMenuResp")
	proto.RegisterType((*AddSongReq)(nil), "channellistening.AddSongReq")
	proto.RegisterType((*AddSongResp)(nil), "channellistening.AddSongResp")
	proto.RegisterType((*UpdateSongStatusReq)(nil), "channellistening.UpdateSongStatusReq")
	proto.RegisterType((*UpdateSongStatusResp)(nil), "channellistening.UpdateSongStatusResp")
	proto.RegisterType((*BatchAddSongReq)(nil), "channellistening.BatchAddSongReq")
	proto.RegisterType((*BatchAddSongResp)(nil), "channellistening.BatchAddSongResp")
	proto.RegisterType((*SearchSongReq)(nil), "channellistening.SearchSongReq")
	proto.RegisterType((*SearchSongResp)(nil), "channellistening.SearchSongResp")
	proto.RegisterType((*GetSongByMenuIdReq)(nil), "channellistening.GetSongByMenuIdReq")
	proto.RegisterType((*GetSongByMenuIdResp)(nil), "channellistening.GetSongByMenuIdResp")
	proto.RegisterType((*DeleteSongReq)(nil), "channellistening.DeleteSongReq")
	proto.RegisterType((*DeleteSongResp)(nil), "channellistening.DeleteSongResp")
	proto.RegisterType((*DeleteSongV2Req)(nil), "channellistening.DeleteSongV2Req")
	proto.RegisterType((*DeleteSongV2Resp)(nil), "channellistening.DeleteSongV2Resp")
	proto.RegisterType((*Song)(nil), "channellistening.Song")
	proto.RegisterType((*GetSongsReq)(nil), "channellistening.GetSongsReq")
	proto.RegisterType((*GetSongsResp)(nil), "channellistening.GetSongsResp")
	proto.RegisterType((*ReorderSongReq)(nil), "channellistening.ReorderSongReq")
	proto.RegisterType((*ReorderSongResp)(nil), "channellistening.ReorderSongResp")
	proto.RegisterType((*SetListeningCheckInTopicReq)(nil), "channellistening.SetListeningCheckInTopicReq")
	proto.RegisterType((*SetListeningCheckInTopicResp)(nil), "channellistening.SetListeningCheckInTopicResp")
	proto.RegisterType((*ListeningCheckInReq)(nil), "channellistening.ListeningCheckInReq")
	proto.RegisterType((*ListeningCheckInResp)(nil), "channellistening.ListeningCheckInResp")
	proto.RegisterType((*ConstellationReq)(nil), "channellistening.ConstellationReq")
	proto.RegisterType((*ConstellationResp)(nil), "channellistening.ConstellationResp")
	proto.RegisterType((*ListeningCheckInV3Req)(nil), "channellistening.ListeningCheckInV3Req")
	proto.RegisterType((*ListeningCheckInV3Resp)(nil), "channellistening.ListeningCheckInV3Resp")
	proto.RegisterType((*ListeningCheckInSharedReq)(nil), "channellistening.ListeningCheckInSharedReq")
	proto.RegisterType((*ListeningCheckInSharedResp)(nil), "channellistening.ListeningCheckInSharedResp")
	proto.RegisterType((*ChannelListeningShareCardV3)(nil), "channellistening.ChannelListeningShareCardV3")
	proto.RegisterType((*ChannelListeningCheckInShareCard)(nil), "channellistening.ChannelListeningCheckInShareCard")
	proto.RegisterType((*GetChannelListeningCheckInShareCardReq)(nil), "channellistening.GetChannelListeningCheckInShareCardReq")
	proto.RegisterType((*GetChannelListeningCheckInShareCardResp)(nil), "channellistening.GetChannelListeningCheckInShareCardResp")
	proto.RegisterType((*ChannelListeningCheckInShareInfo)(nil), "channellistening.ChannelListeningCheckInShareInfo")
	proto.RegisterType((*GetListeningCheckInShareCardV3Req)(nil), "channellistening.GetListeningCheckInShareCardV3Req")
	proto.RegisterType((*GetListeningCheckInShareCardV3Resp)(nil), "channellistening.GetListeningCheckInShareCardV3Resp")
	proto.RegisterType((*GetListeningUserCheckInInfoListReq)(nil), "channellistening.GetListeningUserCheckInInfoListReq")
	proto.RegisterType((*GetListeningUserCheckInInfoListResp)(nil), "channellistening.GetListeningUserCheckInInfoListResp")
	proto.RegisterType((*UserCheckInInfo)(nil), "channellistening.UserCheckInInfo")
	proto.RegisterType((*GetSharedInfoReq)(nil), "channellistening.GetSharedInfoReq")
	proto.RegisterType((*GetSharedInfoResp)(nil), "channellistening.GetSharedInfoResp")
	proto.RegisterType((*SharedInfoMood)(nil), "channellistening.SharedInfoMood")
	proto.RegisterType((*BatchGetCheckInInfoReq)(nil), "channellistening.BatchGetCheckInInfoReq")
	proto.RegisterType((*BatchGetCheckInInfoResp)(nil), "channellistening.BatchGetCheckInInfoResp")
	proto.RegisterMapType((map[uint32]*ChannelListeningCheckInInfo)(nil), "channellistening.BatchGetCheckInInfoResp.InfoEntry")
	proto.RegisterType((*GetUserFlowerReq)(nil), "channellistening.GetUserFlowerReq")
	proto.RegisterType((*GetUserFlowerResp)(nil), "channellistening.GetUserFlowerResp")
	proto.RegisterType((*BatchGetUserFlowerReq)(nil), "channellistening.BatchGetUserFlowerReq")
	proto.RegisterType((*BatchGetUserFlowerResp)(nil), "channellistening.BatchGetUserFlowerResp")
	proto.RegisterMapType((map[uint32]*UserFlower)(nil), "channellistening.BatchGetUserFlowerResp.UserFlowerMapEntry")
	proto.RegisterType((*MusicBook)(nil), "channellistening.MusicBook")
	proto.RegisterMapType((map[uint32]uint32)(nil), "channellistening.MusicBook.RhythmPointEntry")
	proto.RegisterType((*ListeningMood)(nil), "channellistening.ListeningMood")
	proto.RegisterType((*GetAllMoodCfgReq)(nil), "channellistening.GetAllMoodCfgReq")
	proto.RegisterType((*GetAllMoodCfgResp)(nil), "channellistening.GetAllMoodCfgResp")
	proto.RegisterType((*SetListeningUserMoodReq)(nil), "channellistening.SetListeningUserMoodReq")
	proto.RegisterType((*SetListeningUserMoodResp)(nil), "channellistening.SetListeningUserMoodResp")
	proto.RegisterType((*GetAllListeningOnMicUserMoodReq)(nil), "channellistening.GetAllListeningOnMicUserMoodReq")
	proto.RegisterType((*GetAllListeningOnMicUserMoodResp)(nil), "channellistening.GetAllListeningOnMicUserMoodResp")
	proto.RegisterMapType((map[uint32]*ListeningMood)(nil), "channellistening.GetAllListeningOnMicUserMoodResp.UserMoodEntry")
	proto.RegisterEnum("channellistening.SongType", SongType_name, SongType_value)
	proto.RegisterEnum("channellistening.Constellation", Constellation_name, Constellation_value)
	proto.RegisterEnum("channellistening.ChannelListeningCheckInType", ChannelListeningCheckInType_name, ChannelListeningCheckInType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelListeningClient is the client API for ChannelListening service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelListeningClient interface {
	// 喜欢某首歌
	LikeSong(ctx context.Context, in *LikeSongReq, opts ...grpc.CallOption) (*LikeSongResp, error)
	// 获取被喜欢歌曲列表
	GetBeLikedSongList(ctx context.Context, in *GetBeLikedSongListReq, opts ...grpc.CallOption) (*GetBeLikedSongListResp, error)
	// 获取喜欢的歌曲列表
	GetLikedSongList(ctx context.Context, in *GetLikedSongListReq, opts ...grpc.CallOption) (*GetLikedSongListResp, error)
	GetChannelListeningSimpleInfo(ctx context.Context, in *GetChannelListeningSimpleInfoReq, opts ...grpc.CallOption) (*GetChannelListeningSimpleInfoResp, error)
	// 获取花花列表
	GetUserFlowerList(ctx context.Context, in *GetUserFlowerListReq, opts ...grpc.CallOption) (*GetUserFlowerListResp, error)
	// 获取花花详情
	GetFlowerDetail(ctx context.Context, in *GetFlowerDetailReq, opts ...grpc.CallOption) (*GetFlowerDetailResp, error)
	// 添加歌单
	AddSongMenu(ctx context.Context, in *AddSongMenuReq, opts ...grpc.CallOption) (*AddSongMenuResp, error)
	// 获取歌单
	GetAllSongMenu(ctx context.Context, in *GetAllSongMenuReq, opts ...grpc.CallOption) (*GetAllSongMenuResp, error)
	// 获取歌单
	GetAllSongMenuForApp(ctx context.Context, in *GetAllSongMenuReq, opts ...grpc.CallOption) (*GetAllSongMenuResp, error)
	// 更新歌单
	UpdateSongMenu(ctx context.Context, in *UpdateSongMenuReq, opts ...grpc.CallOption) (*UpdateSongMenuResp, error)
	// 重排序歌单
	ReorderSongMenu(ctx context.Context, in *ReorderSongMenuReq, opts ...grpc.CallOption) (*ReorderSongMenuResp, error)
	// 往歌单添加歌曲
	AddSong(ctx context.Context, in *AddSongReq, opts ...grpc.CallOption) (*AddSongResp, error)
	// 更新审核状态
	UpdateSongStatus(ctx context.Context, in *UpdateSongStatusReq, opts ...grpc.CallOption) (*UpdateSongStatusResp, error)
	// 批量往歌单添加歌曲
	BatchAddSong(ctx context.Context, in *BatchAddSongReq, opts ...grpc.CallOption) (*BatchAddSongResp, error)
	// 搜索歌曲
	SearchSong(ctx context.Context, in *SearchSongReq, opts ...grpc.CallOption) (*SearchSongResp, error)
	// 获取歌曲
	GetSongByMenuId(ctx context.Context, in *GetSongByMenuIdReq, opts ...grpc.CallOption) (*GetSongByMenuIdResp, error)
	// 删除歌曲
	DeleteSong(ctx context.Context, in *DeleteSongReq, opts ...grpc.CallOption) (*DeleteSongResp, error)
	// 删除歌曲
	DeleteSongV2(ctx context.Context, in *DeleteSongV2Req, opts ...grpc.CallOption) (*DeleteSongV2Resp, error)
	// 获取所有歌曲
	GetSongs(ctx context.Context, in *GetSongsReq, opts ...grpc.CallOption) (*GetSongsResp, error)
	// 歌曲排序
	ReorderSong(ctx context.Context, in *ReorderSongReq, opts ...grpc.CallOption) (*ReorderSongResp, error)
	// 设置打卡话题
	SetListeningCheckInTopic(ctx context.Context, in *SetListeningCheckInTopicReq, opts ...grpc.CallOption) (*SetListeningCheckInTopicResp, error)
	// 获取状态配置
	GetAllMoodCfg(ctx context.Context, in *GetAllMoodCfgReq, opts ...grpc.CallOption) (*GetAllMoodCfgResp, error)
	// 设置状态
	SetListeningUserMood(ctx context.Context, in *SetListeningUserMoodReq, opts ...grpc.CallOption) (*SetListeningUserMoodResp, error)
	// 获取麦上用户状态
	GetAllListeningOnMicUserMood(ctx context.Context, in *GetAllListeningOnMicUserMoodReq, opts ...grpc.CallOption) (*GetAllListeningOnMicUserMoodResp, error)
	// 打卡
	ListeningCheckIn(ctx context.Context, in *ListeningCheckInReq, opts ...grpc.CallOption) (*ListeningCheckInResp, error)
	ListeningCheckInV2(ctx context.Context, in *ListeningCheckInReq, opts ...grpc.CallOption) (*ListeningCheckInResp, error)
	ListeningCheckInV3(ctx context.Context, in *ListeningCheckInV3Req, opts ...grpc.CallOption) (*ListeningCheckInV3Resp, error)
	// 获取掉落卡片
	ListeningDropCardV3(ctx context.Context, in *ListeningDropCardV3Req, opts ...grpc.CallOption) (*ListeningDropCardV3Resp, error)
	// 分享
	ListeningCheckInShared(ctx context.Context, in *ListeningCheckInSharedReq, opts ...grpc.CallOption) (*ListeningCheckInSharedResp, error)
	ListeningCheckInSharedV3(ctx context.Context, in *ListeningCheckInSharedReq, opts ...grpc.CallOption) (*ListeningCheckInSharedResp, error)
	// 获取分享内容
	GetChannelListeningCheckInShareCard(ctx context.Context, in *GetChannelListeningCheckInShareCardReq, opts ...grpc.CallOption) (*GetChannelListeningCheckInShareCardResp, error)
	GetListeningCheckInShareCardV3(ctx context.Context, in *GetListeningCheckInShareCardV3Req, opts ...grpc.CallOption) (*GetListeningCheckInShareCardV3Resp, error)
	// 获取星座
	Constellation(ctx context.Context, in *ConstellationReq, opts ...grpc.CallOption) (*ConstellationResp, error)
	// 获取打卡榜
	GetListeningUserCheckInInfoList(ctx context.Context, in *GetListeningUserCheckInInfoListReq, opts ...grpc.CallOption) (*GetListeningUserCheckInInfoListResp, error)
	// 获取当日在房详情
	GetSharedInfo(ctx context.Context, in *GetSharedInfoReq, opts ...grpc.CallOption) (*GetSharedInfoResp, error)
	// 批量获取签到信息
	BatchGetCheckInInfo(ctx context.Context, in *BatchGetCheckInInfoReq, opts ...grpc.CallOption) (*BatchGetCheckInInfoResp, error)
	// 获取花花详情
	GetUserFlower(ctx context.Context, in *GetUserFlowerReq, opts ...grpc.CallOption) (*GetUserFlowerResp, error)
	BatchGetUserFlower(ctx context.Context, in *BatchGetUserFlowerReq, opts ...grpc.CallOption) (*BatchGetUserFlowerResp, error)
}

type channelListeningClient struct {
	cc *grpc.ClientConn
}

func NewChannelListeningClient(cc *grpc.ClientConn) ChannelListeningClient {
	return &channelListeningClient{cc}
}

func (c *channelListeningClient) LikeSong(ctx context.Context, in *LikeSongReq, opts ...grpc.CallOption) (*LikeSongResp, error) {
	out := new(LikeSongResp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/LikeSong", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) GetBeLikedSongList(ctx context.Context, in *GetBeLikedSongListReq, opts ...grpc.CallOption) (*GetBeLikedSongListResp, error) {
	out := new(GetBeLikedSongListResp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/GetBeLikedSongList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) GetLikedSongList(ctx context.Context, in *GetLikedSongListReq, opts ...grpc.CallOption) (*GetLikedSongListResp, error) {
	out := new(GetLikedSongListResp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/GetLikedSongList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) GetChannelListeningSimpleInfo(ctx context.Context, in *GetChannelListeningSimpleInfoReq, opts ...grpc.CallOption) (*GetChannelListeningSimpleInfoResp, error) {
	out := new(GetChannelListeningSimpleInfoResp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/GetChannelListeningSimpleInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) GetUserFlowerList(ctx context.Context, in *GetUserFlowerListReq, opts ...grpc.CallOption) (*GetUserFlowerListResp, error) {
	out := new(GetUserFlowerListResp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/GetUserFlowerList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) GetFlowerDetail(ctx context.Context, in *GetFlowerDetailReq, opts ...grpc.CallOption) (*GetFlowerDetailResp, error) {
	out := new(GetFlowerDetailResp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/GetFlowerDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) AddSongMenu(ctx context.Context, in *AddSongMenuReq, opts ...grpc.CallOption) (*AddSongMenuResp, error) {
	out := new(AddSongMenuResp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/AddSongMenu", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) GetAllSongMenu(ctx context.Context, in *GetAllSongMenuReq, opts ...grpc.CallOption) (*GetAllSongMenuResp, error) {
	out := new(GetAllSongMenuResp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/GetAllSongMenu", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) GetAllSongMenuForApp(ctx context.Context, in *GetAllSongMenuReq, opts ...grpc.CallOption) (*GetAllSongMenuResp, error) {
	out := new(GetAllSongMenuResp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/GetAllSongMenuForApp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) UpdateSongMenu(ctx context.Context, in *UpdateSongMenuReq, opts ...grpc.CallOption) (*UpdateSongMenuResp, error) {
	out := new(UpdateSongMenuResp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/UpdateSongMenu", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) ReorderSongMenu(ctx context.Context, in *ReorderSongMenuReq, opts ...grpc.CallOption) (*ReorderSongMenuResp, error) {
	out := new(ReorderSongMenuResp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/ReorderSongMenu", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) AddSong(ctx context.Context, in *AddSongReq, opts ...grpc.CallOption) (*AddSongResp, error) {
	out := new(AddSongResp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/AddSong", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) UpdateSongStatus(ctx context.Context, in *UpdateSongStatusReq, opts ...grpc.CallOption) (*UpdateSongStatusResp, error) {
	out := new(UpdateSongStatusResp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/UpdateSongStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) BatchAddSong(ctx context.Context, in *BatchAddSongReq, opts ...grpc.CallOption) (*BatchAddSongResp, error) {
	out := new(BatchAddSongResp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/BatchAddSong", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) SearchSong(ctx context.Context, in *SearchSongReq, opts ...grpc.CallOption) (*SearchSongResp, error) {
	out := new(SearchSongResp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/SearchSong", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) GetSongByMenuId(ctx context.Context, in *GetSongByMenuIdReq, opts ...grpc.CallOption) (*GetSongByMenuIdResp, error) {
	out := new(GetSongByMenuIdResp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/GetSongByMenuId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) DeleteSong(ctx context.Context, in *DeleteSongReq, opts ...grpc.CallOption) (*DeleteSongResp, error) {
	out := new(DeleteSongResp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/DeleteSong", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) DeleteSongV2(ctx context.Context, in *DeleteSongV2Req, opts ...grpc.CallOption) (*DeleteSongV2Resp, error) {
	out := new(DeleteSongV2Resp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/DeleteSongV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) GetSongs(ctx context.Context, in *GetSongsReq, opts ...grpc.CallOption) (*GetSongsResp, error) {
	out := new(GetSongsResp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/GetSongs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) ReorderSong(ctx context.Context, in *ReorderSongReq, opts ...grpc.CallOption) (*ReorderSongResp, error) {
	out := new(ReorderSongResp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/ReorderSong", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) SetListeningCheckInTopic(ctx context.Context, in *SetListeningCheckInTopicReq, opts ...grpc.CallOption) (*SetListeningCheckInTopicResp, error) {
	out := new(SetListeningCheckInTopicResp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/SetListeningCheckInTopic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) GetAllMoodCfg(ctx context.Context, in *GetAllMoodCfgReq, opts ...grpc.CallOption) (*GetAllMoodCfgResp, error) {
	out := new(GetAllMoodCfgResp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/GetAllMoodCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) SetListeningUserMood(ctx context.Context, in *SetListeningUserMoodReq, opts ...grpc.CallOption) (*SetListeningUserMoodResp, error) {
	out := new(SetListeningUserMoodResp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/SetListeningUserMood", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) GetAllListeningOnMicUserMood(ctx context.Context, in *GetAllListeningOnMicUserMoodReq, opts ...grpc.CallOption) (*GetAllListeningOnMicUserMoodResp, error) {
	out := new(GetAllListeningOnMicUserMoodResp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/GetAllListeningOnMicUserMood", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) ListeningCheckIn(ctx context.Context, in *ListeningCheckInReq, opts ...grpc.CallOption) (*ListeningCheckInResp, error) {
	out := new(ListeningCheckInResp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/ListeningCheckIn", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) ListeningCheckInV2(ctx context.Context, in *ListeningCheckInReq, opts ...grpc.CallOption) (*ListeningCheckInResp, error) {
	out := new(ListeningCheckInResp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/ListeningCheckInV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) ListeningCheckInV3(ctx context.Context, in *ListeningCheckInV3Req, opts ...grpc.CallOption) (*ListeningCheckInV3Resp, error) {
	out := new(ListeningCheckInV3Resp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/ListeningCheckInV3", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) ListeningDropCardV3(ctx context.Context, in *ListeningDropCardV3Req, opts ...grpc.CallOption) (*ListeningDropCardV3Resp, error) {
	out := new(ListeningDropCardV3Resp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/ListeningDropCardV3", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) ListeningCheckInShared(ctx context.Context, in *ListeningCheckInSharedReq, opts ...grpc.CallOption) (*ListeningCheckInSharedResp, error) {
	out := new(ListeningCheckInSharedResp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/ListeningCheckInShared", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) ListeningCheckInSharedV3(ctx context.Context, in *ListeningCheckInSharedReq, opts ...grpc.CallOption) (*ListeningCheckInSharedResp, error) {
	out := new(ListeningCheckInSharedResp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/ListeningCheckInSharedV3", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) GetChannelListeningCheckInShareCard(ctx context.Context, in *GetChannelListeningCheckInShareCardReq, opts ...grpc.CallOption) (*GetChannelListeningCheckInShareCardResp, error) {
	out := new(GetChannelListeningCheckInShareCardResp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/GetChannelListeningCheckInShareCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) GetListeningCheckInShareCardV3(ctx context.Context, in *GetListeningCheckInShareCardV3Req, opts ...grpc.CallOption) (*GetListeningCheckInShareCardV3Resp, error) {
	out := new(GetListeningCheckInShareCardV3Resp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/GetListeningCheckInShareCardV3", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) Constellation(ctx context.Context, in *ConstellationReq, opts ...grpc.CallOption) (*ConstellationResp, error) {
	out := new(ConstellationResp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/Constellation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) GetListeningUserCheckInInfoList(ctx context.Context, in *GetListeningUserCheckInInfoListReq, opts ...grpc.CallOption) (*GetListeningUserCheckInInfoListResp, error) {
	out := new(GetListeningUserCheckInInfoListResp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/GetListeningUserCheckInInfoList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) GetSharedInfo(ctx context.Context, in *GetSharedInfoReq, opts ...grpc.CallOption) (*GetSharedInfoResp, error) {
	out := new(GetSharedInfoResp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/GetSharedInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) BatchGetCheckInInfo(ctx context.Context, in *BatchGetCheckInInfoReq, opts ...grpc.CallOption) (*BatchGetCheckInInfoResp, error) {
	out := new(BatchGetCheckInInfoResp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/BatchGetCheckInInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) GetUserFlower(ctx context.Context, in *GetUserFlowerReq, opts ...grpc.CallOption) (*GetUserFlowerResp, error) {
	out := new(GetUserFlowerResp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/GetUserFlower", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningClient) BatchGetUserFlower(ctx context.Context, in *BatchGetUserFlowerReq, opts ...grpc.CallOption) (*BatchGetUserFlowerResp, error) {
	out := new(BatchGetUserFlowerResp)
	err := c.cc.Invoke(ctx, "/channellistening.ChannelListening/BatchGetUserFlower", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelListeningServer is the server API for ChannelListening service.
type ChannelListeningServer interface {
	// 喜欢某首歌
	LikeSong(context.Context, *LikeSongReq) (*LikeSongResp, error)
	// 获取被喜欢歌曲列表
	GetBeLikedSongList(context.Context, *GetBeLikedSongListReq) (*GetBeLikedSongListResp, error)
	// 获取喜欢的歌曲列表
	GetLikedSongList(context.Context, *GetLikedSongListReq) (*GetLikedSongListResp, error)
	GetChannelListeningSimpleInfo(context.Context, *GetChannelListeningSimpleInfoReq) (*GetChannelListeningSimpleInfoResp, error)
	// 获取花花列表
	GetUserFlowerList(context.Context, *GetUserFlowerListReq) (*GetUserFlowerListResp, error)
	// 获取花花详情
	GetFlowerDetail(context.Context, *GetFlowerDetailReq) (*GetFlowerDetailResp, error)
	// 添加歌单
	AddSongMenu(context.Context, *AddSongMenuReq) (*AddSongMenuResp, error)
	// 获取歌单
	GetAllSongMenu(context.Context, *GetAllSongMenuReq) (*GetAllSongMenuResp, error)
	// 获取歌单
	GetAllSongMenuForApp(context.Context, *GetAllSongMenuReq) (*GetAllSongMenuResp, error)
	// 更新歌单
	UpdateSongMenu(context.Context, *UpdateSongMenuReq) (*UpdateSongMenuResp, error)
	// 重排序歌单
	ReorderSongMenu(context.Context, *ReorderSongMenuReq) (*ReorderSongMenuResp, error)
	// 往歌单添加歌曲
	AddSong(context.Context, *AddSongReq) (*AddSongResp, error)
	// 更新审核状态
	UpdateSongStatus(context.Context, *UpdateSongStatusReq) (*UpdateSongStatusResp, error)
	// 批量往歌单添加歌曲
	BatchAddSong(context.Context, *BatchAddSongReq) (*BatchAddSongResp, error)
	// 搜索歌曲
	SearchSong(context.Context, *SearchSongReq) (*SearchSongResp, error)
	// 获取歌曲
	GetSongByMenuId(context.Context, *GetSongByMenuIdReq) (*GetSongByMenuIdResp, error)
	// 删除歌曲
	DeleteSong(context.Context, *DeleteSongReq) (*DeleteSongResp, error)
	// 删除歌曲
	DeleteSongV2(context.Context, *DeleteSongV2Req) (*DeleteSongV2Resp, error)
	// 获取所有歌曲
	GetSongs(context.Context, *GetSongsReq) (*GetSongsResp, error)
	// 歌曲排序
	ReorderSong(context.Context, *ReorderSongReq) (*ReorderSongResp, error)
	// 设置打卡话题
	SetListeningCheckInTopic(context.Context, *SetListeningCheckInTopicReq) (*SetListeningCheckInTopicResp, error)
	// 获取状态配置
	GetAllMoodCfg(context.Context, *GetAllMoodCfgReq) (*GetAllMoodCfgResp, error)
	// 设置状态
	SetListeningUserMood(context.Context, *SetListeningUserMoodReq) (*SetListeningUserMoodResp, error)
	// 获取麦上用户状态
	GetAllListeningOnMicUserMood(context.Context, *GetAllListeningOnMicUserMoodReq) (*GetAllListeningOnMicUserMoodResp, error)
	// 打卡
	ListeningCheckIn(context.Context, *ListeningCheckInReq) (*ListeningCheckInResp, error)
	ListeningCheckInV2(context.Context, *ListeningCheckInReq) (*ListeningCheckInResp, error)
	ListeningCheckInV3(context.Context, *ListeningCheckInV3Req) (*ListeningCheckInV3Resp, error)
	// 获取掉落卡片
	ListeningDropCardV3(context.Context, *ListeningDropCardV3Req) (*ListeningDropCardV3Resp, error)
	// 分享
	ListeningCheckInShared(context.Context, *ListeningCheckInSharedReq) (*ListeningCheckInSharedResp, error)
	ListeningCheckInSharedV3(context.Context, *ListeningCheckInSharedReq) (*ListeningCheckInSharedResp, error)
	// 获取分享内容
	GetChannelListeningCheckInShareCard(context.Context, *GetChannelListeningCheckInShareCardReq) (*GetChannelListeningCheckInShareCardResp, error)
	GetListeningCheckInShareCardV3(context.Context, *GetListeningCheckInShareCardV3Req) (*GetListeningCheckInShareCardV3Resp, error)
	// 获取星座
	Constellation(context.Context, *ConstellationReq) (*ConstellationResp, error)
	// 获取打卡榜
	GetListeningUserCheckInInfoList(context.Context, *GetListeningUserCheckInInfoListReq) (*GetListeningUserCheckInInfoListResp, error)
	// 获取当日在房详情
	GetSharedInfo(context.Context, *GetSharedInfoReq) (*GetSharedInfoResp, error)
	// 批量获取签到信息
	BatchGetCheckInInfo(context.Context, *BatchGetCheckInInfoReq) (*BatchGetCheckInInfoResp, error)
	// 获取花花详情
	GetUserFlower(context.Context, *GetUserFlowerReq) (*GetUserFlowerResp, error)
	BatchGetUserFlower(context.Context, *BatchGetUserFlowerReq) (*BatchGetUserFlowerResp, error)
}

func RegisterChannelListeningServer(s *grpc.Server, srv ChannelListeningServer) {
	s.RegisterService(&_ChannelListening_serviceDesc, srv)
}

func _ChannelListening_LikeSong_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LikeSongReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).LikeSong(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/LikeSong",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).LikeSong(ctx, req.(*LikeSongReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_GetBeLikedSongList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBeLikedSongListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).GetBeLikedSongList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/GetBeLikedSongList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).GetBeLikedSongList(ctx, req.(*GetBeLikedSongListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_GetLikedSongList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLikedSongListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).GetLikedSongList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/GetLikedSongList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).GetLikedSongList(ctx, req.(*GetLikedSongListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_GetChannelListeningSimpleInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelListeningSimpleInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).GetChannelListeningSimpleInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/GetChannelListeningSimpleInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).GetChannelListeningSimpleInfo(ctx, req.(*GetChannelListeningSimpleInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_GetUserFlowerList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserFlowerListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).GetUserFlowerList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/GetUserFlowerList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).GetUserFlowerList(ctx, req.(*GetUserFlowerListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_GetFlowerDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFlowerDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).GetFlowerDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/GetFlowerDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).GetFlowerDetail(ctx, req.(*GetFlowerDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_AddSongMenu_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddSongMenuReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).AddSongMenu(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/AddSongMenu",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).AddSongMenu(ctx, req.(*AddSongMenuReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_GetAllSongMenu_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllSongMenuReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).GetAllSongMenu(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/GetAllSongMenu",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).GetAllSongMenu(ctx, req.(*GetAllSongMenuReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_GetAllSongMenuForApp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllSongMenuReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).GetAllSongMenuForApp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/GetAllSongMenuForApp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).GetAllSongMenuForApp(ctx, req.(*GetAllSongMenuReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_UpdateSongMenu_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSongMenuReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).UpdateSongMenu(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/UpdateSongMenu",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).UpdateSongMenu(ctx, req.(*UpdateSongMenuReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_ReorderSongMenu_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReorderSongMenuReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).ReorderSongMenu(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/ReorderSongMenu",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).ReorderSongMenu(ctx, req.(*ReorderSongMenuReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_AddSong_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddSongReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).AddSong(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/AddSong",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).AddSong(ctx, req.(*AddSongReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_UpdateSongStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSongStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).UpdateSongStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/UpdateSongStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).UpdateSongStatus(ctx, req.(*UpdateSongStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_BatchAddSong_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchAddSongReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).BatchAddSong(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/BatchAddSong",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).BatchAddSong(ctx, req.(*BatchAddSongReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_SearchSong_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchSongReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).SearchSong(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/SearchSong",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).SearchSong(ctx, req.(*SearchSongReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_GetSongByMenuId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSongByMenuIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).GetSongByMenuId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/GetSongByMenuId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).GetSongByMenuId(ctx, req.(*GetSongByMenuIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_DeleteSong_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSongReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).DeleteSong(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/DeleteSong",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).DeleteSong(ctx, req.(*DeleteSongReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_DeleteSongV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSongV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).DeleteSongV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/DeleteSongV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).DeleteSongV2(ctx, req.(*DeleteSongV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_GetSongs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSongsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).GetSongs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/GetSongs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).GetSongs(ctx, req.(*GetSongsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_ReorderSong_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReorderSongReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).ReorderSong(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/ReorderSong",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).ReorderSong(ctx, req.(*ReorderSongReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_SetListeningCheckInTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetListeningCheckInTopicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).SetListeningCheckInTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/SetListeningCheckInTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).SetListeningCheckInTopic(ctx, req.(*SetListeningCheckInTopicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_GetAllMoodCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllMoodCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).GetAllMoodCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/GetAllMoodCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).GetAllMoodCfg(ctx, req.(*GetAllMoodCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_SetListeningUserMood_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetListeningUserMoodReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).SetListeningUserMood(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/SetListeningUserMood",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).SetListeningUserMood(ctx, req.(*SetListeningUserMoodReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_GetAllListeningOnMicUserMood_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllListeningOnMicUserMoodReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).GetAllListeningOnMicUserMood(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/GetAllListeningOnMicUserMood",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).GetAllListeningOnMicUserMood(ctx, req.(*GetAllListeningOnMicUserMoodReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_ListeningCheckIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListeningCheckInReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).ListeningCheckIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/ListeningCheckIn",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).ListeningCheckIn(ctx, req.(*ListeningCheckInReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_ListeningCheckInV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListeningCheckInReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).ListeningCheckInV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/ListeningCheckInV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).ListeningCheckInV2(ctx, req.(*ListeningCheckInReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_ListeningCheckInV3_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListeningCheckInV3Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).ListeningCheckInV3(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/ListeningCheckInV3",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).ListeningCheckInV3(ctx, req.(*ListeningCheckInV3Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_ListeningDropCardV3_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListeningDropCardV3Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).ListeningDropCardV3(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/ListeningDropCardV3",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).ListeningDropCardV3(ctx, req.(*ListeningDropCardV3Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_ListeningCheckInShared_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListeningCheckInSharedReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).ListeningCheckInShared(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/ListeningCheckInShared",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).ListeningCheckInShared(ctx, req.(*ListeningCheckInSharedReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_ListeningCheckInSharedV3_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListeningCheckInSharedReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).ListeningCheckInSharedV3(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/ListeningCheckInSharedV3",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).ListeningCheckInSharedV3(ctx, req.(*ListeningCheckInSharedReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_GetChannelListeningCheckInShareCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelListeningCheckInShareCardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).GetChannelListeningCheckInShareCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/GetChannelListeningCheckInShareCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).GetChannelListeningCheckInShareCard(ctx, req.(*GetChannelListeningCheckInShareCardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_GetListeningCheckInShareCardV3_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetListeningCheckInShareCardV3Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).GetListeningCheckInShareCardV3(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/GetListeningCheckInShareCardV3",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).GetListeningCheckInShareCardV3(ctx, req.(*GetListeningCheckInShareCardV3Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_Constellation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConstellationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).Constellation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/Constellation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).Constellation(ctx, req.(*ConstellationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_GetListeningUserCheckInInfoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetListeningUserCheckInInfoListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).GetListeningUserCheckInInfoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/GetListeningUserCheckInInfoList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).GetListeningUserCheckInInfoList(ctx, req.(*GetListeningUserCheckInInfoListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_GetSharedInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSharedInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).GetSharedInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/GetSharedInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).GetSharedInfo(ctx, req.(*GetSharedInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_BatchGetCheckInInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetCheckInInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).BatchGetCheckInInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/BatchGetCheckInInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).BatchGetCheckInInfo(ctx, req.(*BatchGetCheckInInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_GetUserFlower_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserFlowerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).GetUserFlower(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/GetUserFlower",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).GetUserFlower(ctx, req.(*GetUserFlowerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListening_BatchGetUserFlower_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserFlowerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningServer).BatchGetUserFlower(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channellistening.ChannelListening/BatchGetUserFlower",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningServer).BatchGetUserFlower(ctx, req.(*BatchGetUserFlowerReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelListening_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channellistening.ChannelListening",
	HandlerType: (*ChannelListeningServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "LikeSong",
			Handler:    _ChannelListening_LikeSong_Handler,
		},
		{
			MethodName: "GetBeLikedSongList",
			Handler:    _ChannelListening_GetBeLikedSongList_Handler,
		},
		{
			MethodName: "GetLikedSongList",
			Handler:    _ChannelListening_GetLikedSongList_Handler,
		},
		{
			MethodName: "GetChannelListeningSimpleInfo",
			Handler:    _ChannelListening_GetChannelListeningSimpleInfo_Handler,
		},
		{
			MethodName: "GetUserFlowerList",
			Handler:    _ChannelListening_GetUserFlowerList_Handler,
		},
		{
			MethodName: "GetFlowerDetail",
			Handler:    _ChannelListening_GetFlowerDetail_Handler,
		},
		{
			MethodName: "AddSongMenu",
			Handler:    _ChannelListening_AddSongMenu_Handler,
		},
		{
			MethodName: "GetAllSongMenu",
			Handler:    _ChannelListening_GetAllSongMenu_Handler,
		},
		{
			MethodName: "GetAllSongMenuForApp",
			Handler:    _ChannelListening_GetAllSongMenuForApp_Handler,
		},
		{
			MethodName: "UpdateSongMenu",
			Handler:    _ChannelListening_UpdateSongMenu_Handler,
		},
		{
			MethodName: "ReorderSongMenu",
			Handler:    _ChannelListening_ReorderSongMenu_Handler,
		},
		{
			MethodName: "AddSong",
			Handler:    _ChannelListening_AddSong_Handler,
		},
		{
			MethodName: "UpdateSongStatus",
			Handler:    _ChannelListening_UpdateSongStatus_Handler,
		},
		{
			MethodName: "BatchAddSong",
			Handler:    _ChannelListening_BatchAddSong_Handler,
		},
		{
			MethodName: "SearchSong",
			Handler:    _ChannelListening_SearchSong_Handler,
		},
		{
			MethodName: "GetSongByMenuId",
			Handler:    _ChannelListening_GetSongByMenuId_Handler,
		},
		{
			MethodName: "DeleteSong",
			Handler:    _ChannelListening_DeleteSong_Handler,
		},
		{
			MethodName: "DeleteSongV2",
			Handler:    _ChannelListening_DeleteSongV2_Handler,
		},
		{
			MethodName: "GetSongs",
			Handler:    _ChannelListening_GetSongs_Handler,
		},
		{
			MethodName: "ReorderSong",
			Handler:    _ChannelListening_ReorderSong_Handler,
		},
		{
			MethodName: "SetListeningCheckInTopic",
			Handler:    _ChannelListening_SetListeningCheckInTopic_Handler,
		},
		{
			MethodName: "GetAllMoodCfg",
			Handler:    _ChannelListening_GetAllMoodCfg_Handler,
		},
		{
			MethodName: "SetListeningUserMood",
			Handler:    _ChannelListening_SetListeningUserMood_Handler,
		},
		{
			MethodName: "GetAllListeningOnMicUserMood",
			Handler:    _ChannelListening_GetAllListeningOnMicUserMood_Handler,
		},
		{
			MethodName: "ListeningCheckIn",
			Handler:    _ChannelListening_ListeningCheckIn_Handler,
		},
		{
			MethodName: "ListeningCheckInV2",
			Handler:    _ChannelListening_ListeningCheckInV2_Handler,
		},
		{
			MethodName: "ListeningCheckInV3",
			Handler:    _ChannelListening_ListeningCheckInV3_Handler,
		},
		{
			MethodName: "ListeningDropCardV3",
			Handler:    _ChannelListening_ListeningDropCardV3_Handler,
		},
		{
			MethodName: "ListeningCheckInShared",
			Handler:    _ChannelListening_ListeningCheckInShared_Handler,
		},
		{
			MethodName: "ListeningCheckInSharedV3",
			Handler:    _ChannelListening_ListeningCheckInSharedV3_Handler,
		},
		{
			MethodName: "GetChannelListeningCheckInShareCard",
			Handler:    _ChannelListening_GetChannelListeningCheckInShareCard_Handler,
		},
		{
			MethodName: "GetListeningCheckInShareCardV3",
			Handler:    _ChannelListening_GetListeningCheckInShareCardV3_Handler,
		},
		{
			MethodName: "Constellation",
			Handler:    _ChannelListening_Constellation_Handler,
		},
		{
			MethodName: "GetListeningUserCheckInInfoList",
			Handler:    _ChannelListening_GetListeningUserCheckInInfoList_Handler,
		},
		{
			MethodName: "GetSharedInfo",
			Handler:    _ChannelListening_GetSharedInfo_Handler,
		},
		{
			MethodName: "BatchGetCheckInInfo",
			Handler:    _ChannelListening_BatchGetCheckInInfo_Handler,
		},
		{
			MethodName: "GetUserFlower",
			Handler:    _ChannelListening_GetUserFlower_Handler,
		},
		{
			MethodName: "BatchGetUserFlower",
			Handler:    _ChannelListening_BatchGetUserFlower_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "channel-listening/channel-listening.proto",
}

func init() {
	proto.RegisterFile("channel-listening/channel-listening.proto", fileDescriptor_channel_listening_e507ccca8f7f266c)
}

var fileDescriptor_channel_listening_e507ccca8f7f266c = []byte{
	// 3981 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x3b, 0x5d, 0x6f, 0x1c, 0x47,
	0x72, 0x1c, 0xee, 0x92, 0xbb, 0x5b, 0xfb, 0xc1, 0x55, 0x8b, 0x92, 0xd6, 0x2b, 0xc9, 0xa2, 0xc6,
	0xb2, 0xbe, 0xce, 0xa2, 0x73, 0x4b, 0xd3, 0x71, 0x7c, 0xc0, 0x25, 0x24, 0x45, 0xc9, 0xcc, 0x89,
	0x92, 0xbd, 0x94, 0x64, 0xe4, 0x10, 0x67, 0x32, 0x9c, 0x69, 0xee, 0x0e, 0x38, 0x3b, 0x33, 0x9e,
	0x9e, 0x15, 0x4d, 0x27, 0x48, 0x9e, 0x92, 0x03, 0x02, 0x04, 0xb8, 0x0b, 0x90, 0x20, 0x0f, 0x7e,
	0xc8, 0x43, 0x12, 0x20, 0xcf, 0xc9, 0x43, 0x5e, 0xef, 0x31, 0xf9, 0x07, 0x01, 0x02, 0xe4, 0x57,
	0xe4, 0xed, 0x80, 0x04, 0x5d, 0xdd, 0xb3, 0xf3, 0xb1, 0xbd, 0x1f, 0xa4, 0x68, 0xc0, 0x4f, 0x3b,
	0x5d, 0x5d, 0x5d, 0x5d, 0xdd, 0x5d, 0x5d, 0x55, 0x5d, 0x55, 0x0b, 0x0f, 0xac, 0xbe, 0xe9, 0x79,
	0xd4, 0x7d, 0xe4, 0x3a, 0x2c, 0xa2, 0x9e, 0xe3, 0xf5, 0x3e, 0x1c, 0x83, 0xac, 0x07, 0xa1, 0x1f,
	0xf9, 0xa4, 0x29, 0x3b, 0x46, 0x70, 0xfd, 0x97, 0x1a, 0x5c, 0x7d, 0x16, 0xb7, 0x1e, 0x87, 0x7e,
	0xb0, 0x63, 0x86, 0xf6, 0xeb, 0x8d, 0x2e, 0xfd, 0x9a, 0x34, 0xa1, 0x30, 0x74, 0xec, 0x96, 0xb6,
	0xa6, 0xdd, 0xaf, 0x77, 0xf9, 0x27, 0xb9, 0x09, 0x20, 0x09, 0x18, 0x8e, 0xdd, 0x5a, 0xc4, 0x8e,
	0x8a, 0x84, 0xec, 0xd9, 0x64, 0x17, 0xea, 0x96, 0xef, 0xb1, 0x88, 0xba, 0xae, 0x19, 0x39, 0xbe,
	0xd7, 0x2a, 0xac, 0x69, 0xf7, 0x1b, 0x9d, 0x5b, 0xeb, 0xf9, 0x59, 0xd7, 0x77, 0xd2, 0x68, 0xdd,
	0xec, 0x28, 0xbd, 0x07, 0xd7, 0x94, 0x1c, 0xb1, 0x80, 0x3c, 0x03, 0x60, 0x7d, 0x33, 0xa4, 0x86,
	0x65, 0x86, 0x82, 0xb3, 0x6a, 0xe7, 0x91, 0x82, 0xbc, 0x00, 0x8c, 0xa8, 0x1c, 0xf0, 0x31, 0x92,
	0x4c, 0x85, 0xc5, 0x0d, 0xfd, 0x5b, 0xa8, 0x3e, 0x73, 0x8e, 0xe9, 0x81, 0xef, 0xf5, 0xce, 0xb5,
	0xde, 0x4f, 0xa1, 0xc8, 0x7c, 0xaf, 0x87, 0xcb, 0xac, 0x76, 0xee, 0xce, 0xc1, 0x07, 0x9f, 0x09,
	0xc7, 0xe8, 0x0d, 0xa8, 0x25, 0x73, 0xb3, 0x40, 0xff, 0x14, 0x56, 0x55, 0xd8, 0xa4, 0x01, 0x8b,
	0x92, 0xa7, 0x4a, 0x77, 0xd1, 0xb1, 0x09, 0x81, 0xa2, 0x67, 0x0e, 0x28, 0x32, 0x53, 0xe9, 0xe2,
	0xb7, 0xfe, 0xb7, 0x1a, 0x5c, 0x79, 0x4a, 0xa3, 0x6d, 0xca, 0x29, 0xda, 0x7c, 0x18, 0xa7, 0xc1,
	0x97, 0x94, 0x5d, 0x80, 0x96, 0x5f, 0xc0, 0x2a, 0x2c, 0x59, 0xfe, 0xd0, 0x8b, 0xe4, 0xd2, 0x44,
	0x83, 0x3c, 0x85, 0x8a, 0xeb, 0x9b, 0xb6, 0x31, 0xf0, 0x43, 0x2a, 0xd7, 0xf6, 0x70, 0xf6, 0xda,
	0x9e, 0xf9, 0xa6, 0xbd, 0xef, 0x87, 0xb4, 0x5b, 0x76, 0xe5, 0x97, 0xfe, 0x77, 0x1a, 0x5c, 0x55,
	0xf1, 0xc5, 0x02, 0xb2, 0x01, 0x4b, 0x7c, 0x1b, 0x58, 0x4b, 0x5b, 0x2b, 0xdc, 0xaf, 0x76, 0x6e,
	0x8e, 0xd3, 0x4f, 0x8d, 0xea, 0x0a, 0xdc, 0x2c, 0x63, 0x8b, 0x6f, 0xc1, 0x58, 0x08, 0xd5, 0x14,
	0x79, 0xd2, 0x82, 0x92, 0x69, 0x89, 0x8d, 0x10, 0x1b, 0x1d, 0x37, 0x49, 0x1b, 0xca, 0x9e, 0x63,
	0x1d, 0xa7, 0x76, 0x7c, 0xd4, 0xe6, 0xe2, 0xc2, 0xe8, 0x37, 0xb8, 0x41, 0xf5, 0x2e, 0xff, 0x24,
	0xd7, 0xa1, 0xc2, 0x19, 0x35, 0x10, 0xbd, 0x28, 0xd0, 0x39, 0xe0, 0x39, 0x3f, 0xa4, 0x27, 0x70,
	0xf9, 0x29, 0x8d, 0xc6, 0x4e, 0xe8, 0xac, 0x42, 0xa7, 0x6f, 0xc0, 0xea, 0x38, 0x1d, 0x16, 0x8c,
	0x26, 0xe7, 0x1b, 0x81, 0xbb, 0x2a, 0x27, 0xe7, 0x08, 0xfa, 0x6b, 0x68, 0x4d, 0xda, 0x16, 0x3e,
	0xd0, 0x35, 0x59, 0x64, 0x04, 0x66, 0x8f, 0x4a, 0x3e, 0xca, 0x1c, 0xf0, 0xb9, 0xd9, 0xa3, 0x9c,
	0x19, 0xec, 0x4c, 0x8b, 0x09, 0xa2, 0xef, 0x70, 0x80, 0x7e, 0x00, 0x6b, 0x4f, 0x69, 0x34, 0x26,
	0xb8, 0xce, 0x20, 0x70, 0xe9, 0x9e, 0x77, 0xe4, 0x9f, 0x6b, 0x85, 0xbf, 0x2e, 0xc0, 0xed, 0x19,
	0x54, 0x59, 0x40, 0x3e, 0x00, 0xe2, 0x3a, 0xc7, 0xd4, 0x18, 0x32, 0x1a, 0x1a, 0xf2, 0xbc, 0x98,
	0x5c, 0x78, 0x93, 0xf7, 0xbc, 0x62, 0x34, 0xdc, 0x92, 0x70, 0x72, 0x17, 0x56, 0x12, 0xec, 0xf4,
	0x62, 0xea, 0x31, 0x2a, 0x2e, 0x88, 0x53, 0x8d, 0x7c, 0xdb, 0x3c, 0x35, 0x8e, 0x5c, 0xff, 0x64,
	0x84, 0x2a, 0xce, 0xb8, 0x89, 0x3d, 0x4f, 0xb0, 0x43, 0x60, 0xdf, 0x04, 0xa0, 0x5e, 0x44, 0x43,
	0x6a, 0x1b, 0x66, 0x84, 0x27, 0x5e, 0xef, 0x56, 0x24, 0x64, 0x2b, 0x22, 0x9f, 0xc2, 0x52, 0xd4,
	0xa7, 0x03, 0xda, 0x5a, 0x42, 0x59, 0xbd, 0x33, 0x43, 0x56, 0x5f, 0x72, 0xdc, 0xae, 0x18, 0x42,
	0xde, 0x81, 0x72, 0x38, 0x74, 0xa9, 0x31, 0x0c, 0xdd, 0xd6, 0xb2, 0x10, 0x4a, 0xde, 0x7e, 0x15,
	0xba, 0xe4, 0x0e, 0x34, 0xac, 0x3e, 0xb5, 0x8e, 0x0d, 0xc7, 0x33, 0x22, 0x3f, 0x70, 0xac, 0x56,
	0x09, 0x11, 0x6a, 0x08, 0xdd, 0xf3, 0x5e, 0x72, 0x18, 0xf9, 0x02, 0xea, 0x23, 0x2c, 0xc7, 0x3b,
	0xf2, 0x5b, 0xe5, 0x79, 0xb5, 0xe5, 0x8e, 0x20, 0x83, 0x3b, 0x5d, 0xb5, 0x92, 0x06, 0x79, 0x00,
	0x97, 0xfa, 0x26, 0x13, 0x7b, 0xc8, 0x68, 0x64, 0x0c, 0x7c, 0xdf, 0x6e, 0x55, 0xd6, 0xb4, 0xfb,
	0xe5, 0x6e, 0xa3, 0x6f, 0x32, 0xbe, 0x8b, 0x07, 0x34, 0xda, 0xf7, 0x7d, 0x5b, 0xff, 0x4b, 0x0d,
	0xae, 0x4f, 0xa1, 0x4b, 0x74, 0xa8, 0xf7, 0xcd, 0x37, 0xd4, 0x88, 0x59, 0x44, 0xf1, 0x28, 0x77,
	0xab, 0x1c, 0x28, 0xf1, 0xb8, 0xe0, 0xd8, 0xe6, 0xa9, 0x3c, 0x27, 0xfe, 0xc9, 0xf5, 0x55, 0xe4,
	0x44, 0xae, 0xd0, 0x4a, 0x95, 0xae, 0x68, 0x70, 0x01, 0xa6, 0xdf, 0x04, 0x4e, 0x48, 0x93, 0x43,
	0x28, 0x0b, 0xc0, 0x56, 0xa4, 0xff, 0xb3, 0x86, 0xf7, 0x85, 0xf3, 0x26, 0x4e, 0xee, 0xbc, 0x17,
	0x2f, 0x51, 0x96, 0x85, 0x89, 0xca, 0xb2, 0xf8, 0x16, 0x3a, 0xe9, 0x37, 0x42, 0x89, 0xe7, 0x19,
	0x65, 0x01, 0xf9, 0x18, 0x4a, 0x42, 0x1a, 0x63, 0x6d, 0x79, 0x63, 0x7c, 0x82, 0x64, 0x58, 0x37,
	0x46, 0x26, 0x1f, 0xc1, 0xb2, 0xf8, 0x94, 0xba, 0x72, 0xfa, 0x30, 0x89, 0x7b, 0xc6, 0x1b, 0x70,
	0x61, 0xcb, 0xff, 0x07, 0x0d, 0x20, 0xe1, 0xe6, 0xc2, 0x54, 0x32, 0x81, 0x62, 0x68, 0x7a, 0xc7,
	0x52, 0x2c, 0xf0, 0x9b, 0x63, 0x79, 0xc3, 0x01, 0x5e, 0xca, 0x7a, 0x97, 0x7f, 0x66, 0x6f, 0x14,
	0x0a, 0xd8, 0x72, 0xf6, 0x46, 0x71, 0x98, 0xfe, 0x8f, 0x1a, 0x90, 0xa7, 0x34, 0x12, 0x1c, 0x3e,
	0xa6, 0x91, 0xe9, 0xb8, 0x3f, 0x44, 0x41, 0xfa, 0x7b, 0x0d, 0x2d, 0x4d, 0x96, 0x4d, 0x16, 0x90,
	0x4f, 0xa0, 0x64, 0x63, 0x2b, 0x16, 0xa3, 0x77, 0xc7, 0xc9, 0x67, 0x06, 0xc5, 0xe8, 0x17, 0x67,
	0x77, 0xff, 0x00, 0x6a, 0xe9, 0x19, 0xf8, 0xe9, 0xd8, 0x66, 0x44, 0xe5, 0x11, 0xe3, 0x37, 0xc2,
	0x28, 0xb3, 0x62, 0x07, 0x87, 0x7f, 0x93, 0xdb, 0x50, 0x53, 0x48, 0x63, 0xf5, 0x28, 0x11, 0x44,
	0xfd, 0xd7, 0x45, 0x20, 0xe3, 0xda, 0x94, 0xbb, 0x4f, 0x87, 0xbd, 0xd8, 0x7d, 0x3a, 0xec, 0x71,
	0x5d, 0x11, 0xb8, 0xe6, 0x29, 0x0d, 0x8d, 0xc3, 0x5e, 0x2c, 0x3e, 0x02, 0xb0, 0xdd, 0xe3, 0x3a,
	0xd7, 0x72, 0x7d, 0xeb, 0x98, 0xf7, 0x09, 0x0d, 0x53, 0xc2, 0xf6, 0x76, 0x8f, 0xdb, 0x8f, 0xbe,
	0xe9, 0x1e, 0xc5, 0x97, 0x82, 0x6b, 0x65, 0x61, 0xe0, 0xeb, 0x1c, 0x2c, 0x16, 0xc5, 0x75, 0xf3,
	0x43, 0xb8, 0x44, 0xbd, 0x88, 0xeb, 0xa2, 0x14, 0xe6, 0x12, 0x62, 0xae, 0x88, 0x8e, 0x04, 0x97,
	0x40, 0x71, 0xe8, 0x39, 0x91, 0x94, 0x35, 0xfc, 0x1e, 0xb9, 0x77, 0xa5, 0xc4, 0xbd, 0x23, 0x6b,
	0x50, 0xeb, 0xfb, 0xae, 0x6d, 0x0c, 0x1c, 0xcb, 0x38, 0xec, 0xb1, 0x56, 0x19, 0x6d, 0x1c, 0x70,
	0xd8, 0xbe, 0x63, 0x6d, 0xf7, 0x18, 0xc7, 0x70, 0x29, 0x57, 0xa7, 0x02, 0x05, 0x75, 0x72, 0xa5,
	0x0b, 0x08, 0x43, 0x14, 0xbe, 0x34, 0xde, 0xc7, 0xa8, 0x19, 0xb5, 0x40, 0x2c, 0x6d, 0xe0, 0x58,
	0x07, 0xd4, 0x8c, 0xc8, 0x2d, 0xa8, 0x7a, 0x7e, 0x38, 0x30, 0x5d, 0xd1, 0x5b, 0x15, 0x63, 0x05,
	0x28, 0x46, 0xb0, 0x5c, 0x9f, 0x51, 0x5b, 0x20, 0xd4, 0x04, 0x82, 0x00, 0x21, 0xc2, 0x3a, 0x5c,
	0x16, 0x5e, 0x39, 0xf5, 0xa2, 0xd0, 0xf4, 0x2c, 0x6a, 0x38, 0x96, 0xef, 0xb5, 0xea, 0x88, 0x78,
	0x09, 0xbb, 0x76, 0x65, 0xcf, 0x9e, 0xe5, 0x7b, 0xe4, 0x1e, 0x34, 0x3d, 0x7a, 0x62, 0x64, 0x16,
	0xd5, 0xc0, 0x45, 0xd5, 0x3d, 0x7a, 0xf2, 0x59, 0xb2, 0x2e, 0x89, 0x98, 0x59, 0xdb, 0x8a, 0xd8,
	0x76, 0x8f, 0x9e, 0x3c, 0x4b, 0x96, 0x77, 0x1b, 0x6a, 0x21, 0x65, 0xfe, 0x30, 0xb4, 0x84, 0xc5,
	0x6c, 0x22, 0x52, 0x35, 0x86, 0xf1, 0xdd, 0x4e, 0xa3, 0x0c, 0xec, 0xcd, 0xd6, 0xa5, 0x2c, 0xca,
	0xbe, 0xbd, 0xa9, 0xf7, 0xa1, 0xb1, 0x65, 0xa3, 0x57, 0xb5, 0x4f, 0xbd, 0x21, 0xbf, 0xdb, 0xeb,
	0x50, 0x1c, 0x50, 0x6f, 0x28, 0x5f, 0x1a, 0xed, 0x71, 0xa1, 0x1f, 0x21, 0x23, 0x1e, 0x17, 0x13,
	0x74, 0xc2, 0x58, 0x9f, 0xd2, 0xc8, 0x88, 0x4e, 0x03, 0x1a, 0xbb, 0x19, 0x1c, 0x7c, 0xc0, 0xa1,
	0x2f, 0x4f, 0x03, 0xaa, 0x6f, 0xc1, 0x4a, 0x66, 0x26, 0x16, 0x9c, 0x75, 0x2a, 0xfd, 0x4b, 0x28,
	0xc7, 0x90, 0x79, 0x1e, 0x09, 0xf2, 0x26, 0x14, 0x46, 0x37, 0xe1, 0x1a, 0x94, 0x1c, 0x66, 0xf8,
	0x01, 0xf5, 0x50, 0x92, 0xcb, 0xdd, 0x65, 0x87, 0xbd, 0x08, 0xa8, 0xa7, 0xff, 0x04, 0x2e, 0x3d,
	0xa5, 0xd1, 0x96, 0xeb, 0xa6, 0x37, 0x42, 0xb1, 0x30, 0x4d, 0xb5, 0xb0, 0x27, 0xa8, 0x22, 0x33,
	0x83, 0x59, 0x40, 0x7e, 0x0b, 0x96, 0x38, 0xcf, 0xb1, 0xe2, 0x99, 0xb6, 0x38, 0x81, 0xa8, 0xef,
	0xc0, 0xa5, 0x57, 0x01, 0xd7, 0x07, 0x6f, 0x71, 0x1a, 0xfa, 0x63, 0x20, 0x79, 0x22, 0xe7, 0xd8,
	0xe8, 0x27, 0x40, 0xba, 0xd4, 0x0f, 0x6d, 0x1a, 0xa6, 0x79, 0x39, 0xfb, 0x92, 0xae, 0xc0, 0xe5,
	0x31, 0x3a, 0x2c, 0xd0, 0x3f, 0x01, 0x90, 0xa2, 0xc0, 0xc9, 0x3e, 0x94, 0x4f, 0x4a, 0xc1, 0xdc,
	0x55, 0x35, 0x55, 0xf9, 0x84, 0xac, 0x43, 0x75, 0x34, 0x92, 0x05, 0xfc, 0x81, 0x91, 0xac, 0xf6,
	0x20, 0x32, 0xa3, 0x21, 0xe3, 0x14, 0xaf, 0x41, 0x09, 0x4f, 0x6e, 0x24, 0x20, 0xcb, 0xbc, 0xb9,
	0x67, 0x93, 0xab, 0xb0, 0xcc, 0x10, 0x4b, 0x8a, 0xa8, 0x6c, 0xe9, 0x57, 0x61, 0x75, 0x9c, 0x0e,
	0x0b, 0xf4, 0xdf, 0x85, 0x95, 0x6d, 0x33, 0xb2, 0xfa, 0x29, 0x6e, 0x3f, 0xc8, 0xbe, 0xe2, 0x26,
	0xb1, 0x2b, 0x90, 0x74, 0x02, 0xcd, 0x2c, 0x01, 0x16, 0xe8, 0xbf, 0xd0, 0xa0, 0x7e, 0x40, 0xcd,
	0xd0, 0xea, 0xc7, 0x34, 0xaf, 0x41, 0x89, 0xef, 0x57, 0x8a, 0x5f, 0xde, 0xdc, 0xb3, 0xb3, 0xaf,
	0xab, 0xc5, 0xec, 0xeb, 0x0a, 0x17, 0xe3, 0x78, 0x3d, 0x1a, 0x4a, 0x09, 0x97, 0x2d, 0x7e, 0x13,
	0xf0, 0x5d, 0x23, 0xed, 0x3f, 0xff, 0x4e, 0xec, 0xef, 0x52, 0xca, 0xfe, 0xea, 0x06, 0x34, 0xd2,
	0x8c, 0xe0, 0x0b, 0xe3, 0x0c, 0xab, 0xe3, 0x5a, 0x32, 0xf2, 0x23, 0xd3, 0xcd, 0xbc, 0x2e, 0x00,
	0x41, 0xc2, 0x42, 0x3d, 0xc2, 0xab, 0xc1, 0x87, 0x6c, 0x9f, 0xee, 0xe3, 0x92, 0xa6, 0x2d, 0x57,
	0xdf, 0x41, 0x2b, 0x9e, 0x45, 0x3f, 0x2b, 0x53, 0xfa, 0x0e, 0xd4, 0x1f, 0x53, 0x97, 0x46, 0x74,
	0xe6, 0xee, 0xbe, 0x03, 0x65, 0x29, 0x26, 0x5c, 0x1e, 0xb8, 0x2e, 0x2e, 0x09, 0x39, 0x61, 0x7a,
	0x13, 0x1a, 0x69, 0x22, 0x2c, 0xd0, 0x1f, 0xc2, 0x4a, 0x02, 0x79, 0xdd, 0x99, 0x26, 0x66, 0xfc,
	0xd4, 0xb3, 0xb8, 0x2c, 0xd0, 0x7f, 0x55, 0x80, 0xe2, 0xbc, 0xd1, 0x8d, 0x89, 0x47, 0x7b, 0x13,
	0x60, 0x30, 0x64, 0x8e, 0x25, 0xb4, 0x91, 0x7c, 0x7c, 0x21, 0x84, 0x6b, 0x22, 0x4e, 0xea, 0xc8,
	0x71, 0xa9, 0x34, 0xbe, 0xf8, 0xcd, 0x45, 0x88, 0xff, 0x1a, 0xcc, 0xf9, 0x36, 0x76, 0xf1, 0xca,
	0x1c, 0x70, 0xe0, 0x7c, 0x9b, 0x74, 0x22, 0xb9, 0x52, 0xd2, 0x89, 0xd4, 0xe2, 0xce, 0xbe, 0xc9,
	0xfa, 0xf8, 0x92, 0x92, 0x9d, 0x9f, 0x99, 0xac, 0x1f, 0x7b, 0x80, 0x95, 0xc4, 0x03, 0x4c, 0x3b,
	0xa9, 0x90, 0x73, 0x52, 0xb9, 0x77, 0x18, 0x52, 0x33, 0x12, 0x8f, 0x46, 0x6e, 0x6e, 0x8b, 0xdd,
	0x8a, 0x84, 0x6c, 0x45, 0xa9, 0x6b, 0x59, 0x4b, 0x5f, 0xcb, 0xb4, 0x47, 0x5c, 0xcf, 0x7a, 0xc4,
	0xab, 0xb0, 0x84, 0x6a, 0xa5, 0xd5, 0x10, 0xf2, 0x8c, 0x8d, 0x91, 0x9a, 0x5b, 0x99, 0x53, 0xcd,
	0xfd, 0x36, 0x54, 0xa5, 0xbc, 0xa1, 0xda, 0x88, 0x2f, 0x8e, 0xa6, 0xba, 0x38, 0xe9, 0x70, 0x91,
	0xfe, 0x15, 0xd4, 0x92, 0x81, 0x17, 0x7f, 0x6d, 0x0c, 0x68, 0xa4, 0xd4, 0xe6, 0x19, 0x75, 0x24,
	0xb7, 0xfa, 0x91, 0x19, 0xf6, 0x68, 0x64, 0x88, 0x2d, 0x12, 0xf4, 0xab, 0x02, 0xf6, 0x82, 0x83,
	0xf4, 0x4b, 0xb0, 0x92, 0x99, 0x80, 0x05, 0x7a, 0x17, 0xae, 0x1f, 0xd0, 0x28, 0xff, 0x70, 0xc5,
	0x77, 0xf5, 0x7c, 0x51, 0x35, 0xf1, 0x2c, 0x5f, 0x94, 0xaf, 0x54, 0xde, 0xd0, 0xdf, 0x85, 0x1b,
	0x93, 0x69, 0x0a, 0xf5, 0x9d, 0xef, 0x3c, 0x57, 0xf4, 0xe4, 0xff, 0x34, 0x58, 0x1d, 0x27, 0xc4,
	0x02, 0xb2, 0x05, 0xc5, 0x91, 0xdd, 0x6e, 0x9c, 0x21, 0x0e, 0xc0, 0xe5, 0xbf, 0x8b, 0x43, 0xc7,
	0x63, 0x0a, 0x8b, 0x6f, 0x1d, 0x53, 0xf8, 0x22, 0x13, 0xd1, 0x15, 0xd1, 0xc6, 0xce, 0xdc, 0xf4,
	0x46, 0x81, 0xdd, 0x74, 0x58, 0xf7, 0x0e, 0x34, 0xb3, 0xf1, 0x65, 0xd5, 0x36, 0xea, 0x3d, 0xb8,
	0x94, 0xc3, 0x62, 0x01, 0xe9, 0xc2, 0x6a, 0x26, 0x16, 0x6d, 0x58, 0x7d, 0xdf, 0x67, 0xf1, 0x9e,
	0xcd, 0x0c, 0x64, 0x5f, 0xce, 0x0c, 0xde, 0xc1, 0xb1, 0xfa, 0x77, 0x1a, 0x5c, 0xc9, 0xf3, 0x7d,
	0xce, 0x00, 0xfb, 0x24, 0xf6, 0x0a, 0x6f, 0xc1, 0xde, 0x6f, 0xd2, 0x09, 0x80, 0x14, 0x7b, 0x3f,
	0x58, 0x89, 0x79, 0xa6, 0x90, 0x98, 0xf3, 0xe7, 0x00, 0x3e, 0x86, 0x77, 0x94, 0x42, 0x85, 0xc6,
	0x99, 0x1b, 0x45, 0x9c, 0x6a, 0x64, 0xa4, 0x4a, 0xd8, 0xde, 0xb3, 0xf5, 0x1b, 0xd0, 0x9e, 0x34,
	0x8e, 0x05, 0xfa, 0xff, 0x16, 0xc7, 0xc3, 0x5f, 0x29, 0x06, 0xa6, 0x84, 0x37, 0xae, 0x43, 0x85,
	0x5b, 0x0a, 0x23, 0x1f, 0xdf, 0x40, 0x2f, 0x87, 0xbb, 0x40, 0x7d, 0xff, 0xc4, 0x88, 0x9c, 0x81,
	0x38, 0xf4, 0x42, 0xb7, 0xcc, 0x01, 0x2f, 0x1d, 0x61, 0x57, 0x6c, 0x87, 0xf1, 0xc7, 0x2c, 0x67,
	0x57, 0xda, 0x43, 0x09, 0xd9, 0xcb, 0x8b, 0xd6, 0x52, 0x5e, 0xb4, 0x6e, 0x43, 0x2d, 0xee, 0xc6,
	0xa9, 0x85, 0x75, 0xac, 0x4a, 0x18, 0xce, 0xce, 0x9f, 0xc7, 0x66, 0x68, 0x1b, 0xce, 0xa0, 0x27,
	0xed, 0x63, 0x89, 0xb7, 0xf7, 0x06, 0x3d, 0x72, 0x05, 0x96, 0x0f, 0x7b, 0xd8, 0x21, 0x6c, 0xe3,
	0xd2, 0x61, 0x8f, 0x83, 0xaf, 0x41, 0xe9, 0x30, 0x32, 0xf0, 0x39, 0x2f, 0x9e, 0xa4, 0xcb, 0x87,
	0xd1, 0x63, 0xfe, 0xa0, 0xbf, 0x0e, 0x95, 0x13, 0x7a, 0x28, 0x63, 0x2d, 0xd2, 0x40, 0x9e, 0xd0,
	0x43, 0x8c, 0xb3, 0xf0, 0xcd, 0x39, 0xf2, 0xc3, 0x68, 0xe8, 0x51, 0xf9, 0x18, 0x8d, 0x9b, 0xe4,
	0x73, 0xb8, 0x3c, 0xe0, 0x1e, 0xa4, 0x91, 0x4d, 0x33, 0xd5, 0xe6, 0x13, 0x7f, 0x82, 0x63, 0x33,
	0x30, 0xbc, 0x51, 0x78, 0x0f, 0x72, 0x24, 0xeb, 0xf3, 0xde, 0x28, 0x1c, 0x9c, 0xa5, 0x39, 0x52,
	0x69, 0x28, 0xf0, 0x8d, 0xf3, 0xa8, 0x34, 0x94, 0x7a, 0x21, 0xa5, 0x28, 0xf3, 0x77, 0x61, 0x25,
	0x3e, 0x9d, 0x37, 0x0e, 0x3d, 0xe1, 0x27, 0x28, 0xdf, 0xc1, 0x12, 0xfc, 0xda, 0xa1, 0x27, 0x7b,
	0xb6, 0xfe, 0xab, 0x22, 0xac, 0xcd, 0x52, 0x95, 0xe4, 0x11, 0x5c, 0xce, 0x46, 0xbb, 0xc4, 0x09,
	0x09, 0x41, 0x6c, 0xa6, 0x43, 0x5e, 0xf1, 0x59, 0x25, 0x42, 0xb7, 0x98, 0x13, 0xba, 0x94, 0x20,
	0x17, 0xa6, 0x08, 0x72, 0x31, 0x27, 0xc8, 0xb7, 0xa1, 0xe6, 0x3a, 0x11, 0x9f, 0x5a, 0x88, 0x80,
	0x70, 0xd2, 0xaa, 0x02, 0x26, 0xa4, 0x60, 0x14, 0xeb, 0x5d, 0x4e, 0xc7, 0x7a, 0xb3, 0x52, 0x5c,
	0xca, 0x4b, 0x71, 0x5a, 0x44, 0xcb, 0x63, 0x22, 0xca, 0x45, 0x6e, 0x14, 0x1d, 0x59, 0x3a, 0xa1,
	0x87, 0xdb, 0x19, 0x11, 0x85, 0xc9, 0x22, 0x5a, 0xcd, 0x89, 0x68, 0xf6, 0x88, 0x6b, 0x17, 0x71,
	0xc4, 0xd2, 0x18, 0xd4, 0x33, 0xc6, 0x20, 0x75, 0xa1, 0x1b, 0xf9, 0x0b, 0x3d, 0xaf, 0x4c, 0xec,
	0xc0, 0x5d, 0x45, 0x36, 0x65, 0xcc, 0x80, 0x4e, 0x57, 0x77, 0x7f, 0x0a, 0xf7, 0xe6, 0x22, 0xc2,
	0x82, 0x9c, 0x45, 0xd7, 0x2e, 0xc2, 0xa2, 0xff, 0xbb, 0x36, 0x5d, 0xac, 0x71, 0x03, 0xf3, 0x6f,
	0x89, 0x91, 0x00, 0x2d, 0xe6, 0x92, 0x05, 0x6c, 0x18, 0x1f, 0x6b, 0x41, 0xbe, 0x22, 0x87, 0xf2,
	0x58, 0x9b, 0x50, 0xe0, 0x92, 0x23, 0xa4, 0x95, 0x7f, 0x0a, 0xe1, 0xe7, 0x8b, 0x49, 0xe2, 0x78,
	0x62, 0x8f, 0x5e, 0x85, 0x2e, 0xd1, 0xa1, 0xee, 0xd1, 0x13, 0x23, 0xa1, 0x27, 0x95, 0xa6, 0x47,
	0x4f, 0x0e, 0x24, 0x49, 0xfd, 0xa7, 0x98, 0xcb, 0x9a, 0xb8, 0x4a, 0xe1, 0x08, 0x4c, 0xd9, 0xf8,
	0x10, 0xf4, 0x59, 0xe3, 0x2f, 0x3c, 0x2f, 0xfe, 0x6f, 0x5a, 0x76, 0x52, 0x4c, 0x8f, 0x25, 0x16,
	0x78, 0xce, 0xe4, 0xb2, 0x14, 0xe8, 0xc5, 0x44, 0xa0, 0xbf, 0xe7, 0xc0, 0xf7, 0x7f, 0x6b, 0xf0,
	0xde, 0x4c, 0xb6, 0x59, 0x40, 0x36, 0xa1, 0x38, 0x4a, 0x92, 0x56, 0x3b, 0xb7, 0xd5, 0x59, 0x91,
	0xb4, 0xfb, 0x81, 0xe8, 0x7c, 0x58, 0xca, 0x83, 0x99, 0x67, 0x18, 0x47, 0xbf, 0xb8, 0x6c, 0xfa,
	0x3f, 0x69, 0xb0, 0x92, 0x9b, 0xe2, 0xfb, 0x4e, 0x93, 0xd8, 0xe6, 0x69, 0x9c, 0x26, 0xb1, 0xcd,
	0xd3, 0x39, 0xd3, 0x24, 0x3b, 0xd0, 0xe4, 0xef, 0x41, 0x74, 0x86, 0xe2, 0x1c, 0xf0, 0x59, 0x45,
	0x45, 0xff, 0x2f, 0x0d, 0xa3, 0x90, 0x69, 0x2a, 0x2c, 0x48, 0xae, 0xb4, 0x96, 0xbe, 0xd2, 0xc2,
	0xf9, 0xe0, 0x17, 0x74, 0x31, 0x76, 0x3e, 0x64, 0x78, 0x3d, 0xa2, 0xdf, 0xc4, 0x76, 0x09, 0xbf,
	0x47, 0xf6, 0x21, 0x89, 0xdf, 0xa3, 0x7d, 0xe0, 0xe8, 0xab, 0xe9, 0x64, 0x6d, 0x25, 0x4e, 0xc3,
	0x7e, 0x04, 0x45, 0xcc, 0x72, 0x2e, 0xe3, 0xc1, 0xad, 0x29, 0xde, 0x9a, 0x23, 0x0e, 0xf7, 0x7d,
	0xdf, 0xee, 0x22, 0x36, 0xb9, 0x01, 0xc0, 0x15, 0x83, 0xe4, 0x4a, 0xc6, 0x12, 0x3c, 0x7a, 0xb2,
	0xcd, 0x19, 0xd3, 0xff, 0x1c, 0x1a, 0xd9, 0x51, 0xfc, 0x11, 0xdc, 0x37, 0x3d, 0xdb, 0x70, 0xfd,
	0x28, 0x72, 0xe2, 0xd5, 0x01, 0x07, 0x3d, 0x43, 0x08, 0x47, 0x38, 0xf2, 0xfd, 0x28, 0x46, 0x10,
	0xeb, 0x04, 0x0e, 0x92, 0x08, 0xf9, 0xe8, 0xee, 0x75, 0xa8, 0x70, 0x4e, 0x32, 0xd6, 0x97, 0x03,
	0xb0, 0x14, 0xe1, 0x67, 0x70, 0x15, 0x03, 0x71, 0xa8, 0xd1, 0x13, 0x79, 0x9d, 0x7d, 0x4e, 0x04,
	0x8a, 0xc3, 0x38, 0x40, 0x54, 0xef, 0xe2, 0xb7, 0xfe, 0x9f, 0x1a, 0x5c, 0x53, 0x52, 0x63, 0x01,
	0x79, 0x2a, 0xaf, 0x8c, 0xb8, 0x69, 0x1b, 0x8a, 0x22, 0x0f, 0xf5, 0xc0, 0x75, 0xfe, 0xb1, 0xeb,
	0x45, 0xe1, 0xa9, 0xb8, 0x44, 0xed, 0x23, 0xa8, 0x8c, 0x40, 0x5c, 0x5a, 0x8e, 0xe9, 0x69, 0xfc,
	0x6c, 0x3a, 0xa6, 0xa7, 0x64, 0x07, 0x96, 0xde, 0x98, 0xee, 0x90, 0x9e, 0xef, 0x75, 0x21, 0xc6,
	0x7e, 0xba, 0xf8, 0x89, 0x26, 0x65, 0x37, 0x95, 0x15, 0x3d, 0x8f, 0xec, 0xfe, 0x19, 0x8a, 0x6e,
	0x9a, 0x08, 0x0b, 0x52, 0xc9, 0x58, 0xed, 0x0c, 0xc9, 0x58, 0x7e, 0xee, 0x22, 0x8f, 0x94, 0xba,
	0xc8, 0x20, 0x40, 0xe8, 0x48, 0xc5, 0x39, 0xa4, 0x42, 0x92, 0x43, 0xd2, 0xbf, 0x80, 0x2b, 0xf1,
	0xbe, 0x9e, 0x69, 0x25, 0xef, 0x40, 0x79, 0xe8, 0xd8, 0xa2, 0x80, 0x44, 0x9c, 0x70, 0x69, 0xe8,
	0xd8, 0x58, 0x3f, 0xf2, 0x37, 0x8b, 0x89, 0xc8, 0xe4, 0x16, 0x66, 0xc1, 0x0a, 0x16, 0x04, 0x48,
	0x3e, 0x07, 0x66, 0x20, 0x8f, 0xfb, 0x27, 0x93, 0x8f, 0x3b, 0x4b, 0x22, 0xb5, 0xf0, 0x7d, 0x33,
	0x10, 0xc7, 0x5e, 0x1f, 0xa6, 0x61, 0xe7, 0xda, 0x87, 0xf6, 0x1f, 0x01, 0x19, 0xa7, 0xac, 0x90,
	0x9e, 0x4e, 0x56, 0x7a, 0xa6, 0x9f, 0x4c, 0x4a, 0x58, 0xbe, 0xd3, 0xa0, 0xb2, 0x3f, 0x64, 0x8e,
	0xb5, 0xed, 0xfb, 0xc7, 0xe4, 0x05, 0xd4, 0xc2, 0xfe, 0x69, 0xd4, 0x1f, 0x18, 0x81, 0xef, 0x78,
	0xb1, 0x75, 0xf9, 0x60, 0x9c, 0xd8, 0x68, 0xc8, 0x7a, 0x17, 0xf1, 0x3f, 0xe7, 0xe8, 0x62, 0xd5,
	0xd5, 0x30, 0x81, 0xb4, 0x7f, 0x0a, 0xcd, 0x3c, 0x82, 0x82, 0xf9, 0xd5, 0x34, 0xf3, 0xf5, 0x34,
	0x7b, 0xff, 0xb2, 0x08, 0xf5, 0x91, 0xbc, 0xa3, 0x9a, 0x51, 0x44, 0x5b, 0x51, 0x43, 0x2e, 0xa6,
	0x34, 0xa4, 0x74, 0x81, 0x0a, 0x89, 0x0b, 0x74, 0x0b, 0xaa, 0x42, 0xed, 0xfc, 0x38, 0xa5, 0x36,
	0x41, 0x82, 0xb8, 0xe6, 0x4c, 0x21, 0x0c, 0xec, 0x4d, 0xa9, 0x3f, 0x63, 0x84, 0x7d, 0x7b, 0x33,
	0x41, 0xe8, 0xa4, 0xca, 0x59, 0x24, 0x42, 0x27, 0x43, 0xa1, 0x83, 0x14, 0x4a, 0x19, 0x04, 0x4e,
	0x61, 0x1d, 0x2e, 0x8b, 0x96, 0x61, 0xf9, 0x83, 0xc0, 0xa5, 0x91, 0x70, 0xc8, 0x84, 0x8b, 0x7f,
	0x49, 0x74, 0xed, 0xc8, 0x1e, 0x4e, 0x50, 0x81, 0xcf, 0x09, 0x57, 0x54, 0xf8, 0xfb, 0xf6, 0xa6,
	0x4e, 0xf0, 0xde, 0x6f, 0xb9, 0x2e, 0xdf, 0xa7, 0x9d, 0xa3, 0x5e, 0x97, 0x7e, 0xad, 0xff, 0x7e,
	0x9c, 0x07, 0x1b, 0xc1, 0xd0, 0x77, 0x58, 0xe2, 0x6a, 0x34, 0x0e, 0x6e, 0x2a, 0x1e, 0x88, 0x99,
	0x2d, 0xef, 0x0a, 0x6c, 0xdd, 0x82, 0x6b, 0x07, 0x39, 0xcf, 0x04, 0xbb, 0xcf, 0xe3, 0x45, 0x5d,
	0x83, 0x12, 0xaa, 0x76, 0xc7, 0x8e, 0x03, 0xe2, 0xbc, 0xb9, 0x67, 0xeb, 0x6d, 0x68, 0xa9, 0x27,
	0xc1, 0x88, 0xe6, 0x2d, 0xb1, 0x98, 0x51, 0xf7, 0x0b, 0x6f, 0xdf, 0xb1, 0xde, 0x86, 0x11, 0xfd,
	0x7f, 0x34, 0xac, 0xfe, 0x9a, 0x42, 0x94, 0x05, 0xe4, 0x2b, 0xa8, 0xa0, 0x7a, 0x40, 0x2b, 0x2a,
	0x36, 0xed, 0xf7, 0xc6, 0x37, 0x6d, 0x16, 0x99, 0xf5, 0xb8, 0x21, 0xee, 0x49, 0x79, 0x28, 0x9b,
	0xed, 0x3f, 0x84, 0x7a, 0xa6, 0x4b, 0x71, 0x43, 0x36, 0xb3, 0xd7, 0x7b, 0xf6, 0x91, 0x8d, 0xae,
	0xd0, 0xc3, 0x8f, 0x45, 0x8e, 0x15, 0x33, 0x00, 0xab, 0xd0, 0x7c, 0xe1, 0x62, 0xe2, 0x6a, 0x94,
	0xed, 0x6c, 0x2e, 0x70, 0xe8, 0x73, 0x7a, 0x92, 0x85, 0x6a, 0x0f, 0xff, 0x55, 0x83, 0x7a, 0x36,
	0x26, 0x50, 0x87, 0xca, 0xd0, 0xb3, 0xe9, 0x91, 0xe3, 0x51, 0xbb, 0xb9, 0x40, 0x2a, 0xb0, 0x64,
	0x86, 0x0e, 0x65, 0x4d, 0x8d, 0x00, 0x2c, 0x47, 0xe6, 0x30, 0x1c, 0xb2, 0xe6, 0x22, 0xff, 0xee,
	0xd1, 0x81, 0xe3, 0x39, 0xcd, 0x02, 0xff, 0xb6, 0x4c, 0xcf, 0xa2, 0x61, 0xb3, 0x48, 0x4a, 0x50,
	0x70, 0xa9, 0xdf, 0x5c, 0xe2, 0xe3, 0xde, 0x38, 0x61, 0xcf, 0x6f, 0x2e, 0xf3, 0x4f, 0xd7, 0x39,
	0x0c, 0xcd, 0x66, 0x89, 0x54, 0xa1, 0xc4, 0x2c, 0x3f, 0x0c, 0x1c, 0xbf, 0x59, 0x26, 0x2b, 0x50,
	0x65, 0x66, 0xcf, 0x89, 0x22, 0x33, 0x74, 0x86, 0xac, 0x59, 0xe1, 0x53, 0x5b, 0x66, 0x10, 0x3a,
	0x96, 0x1f, 0x7a, 0x4d, 0x20, 0x35, 0x28, 0x9b, 0x5f, 0x0f, 0x45, 0x67, 0x95, 0xcf, 0x12, 0x38,
	0xcc, 0xa2, 0xac, 0x59, 0x7b, 0xf8, 0x1f, 0x93, 0x6b, 0xb6, 0x70, 0x07, 0x1e, 0xc0, 0xfb, 0x53,
	0xba, 0x8d, 0x57, 0xcf, 0x1f, 0xef, 0x3e, 0xd9, 0x7b, 0xbe, 0xfb, 0xb8, 0xb9, 0x40, 0xee, 0xc1,
	0x7b, 0xd3, 0x50, 0x0f, 0x5e, 0xed, 0xec, 0xec, 0x1e, 0x1c, 0x34, 0xb5, 0x59, 0x34, 0x9f, 0xee,
	0xbe, 0x34, 0x5e, 0xee, 0xbd, 0x7c, 0xb6, 0xdb, 0x5c, 0x24, 0x3f, 0x82, 0x7b, 0xd3, 0x50, 0xbb,
	0xbb, 0xcf, 0x77, 0xbf, 0x94, 0xc8, 0x85, 0xce, 0x5f, 0xdc, 0x82, 0x66, 0x1e, 0x9b, 0xfc, 0x0c,
	0xca, 0x71, 0xcd, 0x2d, 0xb9, 0xa9, 0x12, 0x83, 0x51, 0x2d, 0x70, 0xfb, 0xdd, 0x69, 0xdd, 0x2c,
	0xd0, 0x17, 0x88, 0x83, 0xe9, 0xbc, 0x5c, 0x6d, 0x2b, 0xb9, 0xa7, 0x94, 0xed, 0xf1, 0xca, 0xdc,
	0xf6, 0xfd, 0xf9, 0x10, 0x71, 0x2a, 0x0b, 0xb5, 0x53, 0x76, 0xa2, 0xf7, 0x95, 0xe3, 0xc7, 0xa6,
	0xb9, 0x3b, 0x0f, 0x1a, 0x4e, 0xf2, 0x57, 0x1a, 0xdc, 0x9c, 0x5a, 0x75, 0x49, 0x3a, 0x4a, 0x5a,
	0x53, 0x8b, 0x3f, 0xdb, 0x1b, 0x67, 0x1e, 0x83, 0xcc, 0x1c, 0xe5, 0x5c, 0x28, 0x5c, 0xb2, 0x7a,
	0x2d, 0x63, 0x95, 0x7d, 0xed, 0x7b, 0x73, 0xe1, 0xe1, 0x3c, 0x7f, 0x0c, 0x2b, 0xb9, 0x52, 0x29,
	0x72, 0x47, 0x39, 0x3a, 0x57, 0xf4, 0xd5, 0x7e, 0x7f, 0x0e, 0x2c, 0x9c, 0xe1, 0xe5, 0x28, 0x49,
	0x8f, 0x95, 0x1a, 0x8a, 0x17, 0x44, 0xb6, 0xe4, 0xa4, 0x7d, 0x7b, 0x06, 0x06, 0x52, 0xfd, 0x0a,
	0x1a, 0xd9, 0x32, 0x0b, 0xf2, 0xde, 0x24, 0xa5, 0x9a, 0xa6, 0x7d, 0x67, 0x36, 0x92, 0x14, 0xb8,
	0xd5, 0x2c, 0xfc, 0x89, 0x1f, 0x6e, 0x05, 0xc1, 0xc5, 0x4e, 0xf2, 0x15, 0x34, 0xb2, 0xd5, 0x19,
	0x2a, 0xf2, 0x63, 0x45, 0x20, 0x2a, 0xf2, 0xe3, 0x45, 0x1e, 0xe2, 0x68, 0x73, 0xe5, 0x16, 0xaa,
	0xa3, 0x1d, 0xaf, 0xec, 0x50, 0x1d, 0xad, 0xaa, 0x6e, 0x63, 0x81, 0x7c, 0x06, 0x25, 0x79, 0x32,
	0xe4, 0xc6, 0xc4, 0x43, 0xe3, 0x14, 0x6f, 0x4e, 0xe9, 0x8d, 0x2f, 0x78, 0xbe, 0xe4, 0x42, 0x75,
	0xc1, 0x15, 0xe5, 0x1d, 0xaa, 0x0b, 0xae, 0xac, 0xde, 0x58, 0x20, 0x5f, 0x42, 0x2d, 0x5d, 0x7e,
	0x41, 0x6e, 0x4f, 0xf0, 0xcf, 0x53, 0x8c, 0xeb, 0xb3, 0x50, 0x90, 0xf0, 0x17, 0x00, 0x49, 0xe5,
	0x04, 0x51, 0xd8, 0xd7, 0x4c, 0x81, 0x47, 0x7b, 0x6d, 0x3a, 0x42, 0xea, 0x5e, 0xa6, 0x8b, 0x1f,
	0x26, 0xdc, 0xcb, 0x5c, 0x39, 0xc5, 0x84, 0x7b, 0x99, 0xaf, 0xa2, 0x10, 0x4c, 0x27, 0x65, 0x09,
	0x2a, 0xa6, 0x33, 0x75, 0x13, 0x2a, 0xa6, 0x73, 0x35, 0x11, 0xb8, 0xc1, 0xe9, 0x4a, 0x07, 0xd5,
	0x06, 0xe7, 0xaa, 0x26, 0x54, 0x1b, 0x3c, 0x56, 0x2c, 0xb1, 0xc0, 0xed, 0x56, 0x9c, 0x61, 0x57,
	0xd9, 0xad, 0x54, 0xda, 0x5e, 0x65, 0xb7, 0xd2, 0xc9, 0x79, 0xa1, 0x90, 0x52, 0xe2, 0xac, 0x52,
	0x48, 0xd9, 0x74, 0xbb, 0x4a, 0x21, 0xe5, 0xf3, 0xe5, 0x0b, 0xe4, 0x4f, 0xb2, 0xbe, 0x67, 0x3a,
	0xbb, 0x4d, 0x1e, 0xa9, 0x0e, 0x7c, 0x62, 0x76, 0xbd, 0xbd, 0x7e, 0x16, 0x74, 0x9c, 0xfc, 0xe7,
	0x50, 0xcf, 0x78, 0xea, 0x44, 0x9f, 0xa4, 0x82, 0x12, 0xf7, 0xbe, 0xfd, 0xde, 0x4c, 0x1c, 0xa4,
	0xed, 0xc3, 0xaa, 0xca, 0xa9, 0x26, 0x0f, 0xa6, 0x73, 0x99, 0x72, 0xac, 0xdb, 0x0f, 0xe7, 0x45,
	0xc5, 0x09, 0x7f, 0xa1, 0xc1, 0x8d, 0x69, 0xee, 0x30, 0xf9, 0xf1, 0x59, 0xdd, 0xe7, 0xaf, 0xdb,
	0x9d, 0xb3, 0x7b, 0xdc, 0x42, 0x2b, 0xe5, 0x77, 0x5d, 0xa5, 0x95, 0x14, 0x55, 0x0b, 0x2a, 0xad,
	0xa4, 0xaa, 0x49, 0xd0, 0x17, 0x08, 0x05, 0x32, 0x96, 0x7d, 0xee, 0x5c, 0xfc, 0x34, 0x8e, 0x62,
	0x9a, 0x0d, 0x95, 0xb7, 0xa6, 0xcc, 0xd4, 0xab, 0xbc, 0x35, 0x75, 0xce, 0x5c, 0x5f, 0x20, 0x6e,
	0xaa, 0x90, 0x23, 0xf9, 0xfb, 0x1a, 0x99, 0x46, 0x22, 0xf3, 0xbf, 0xbb, 0xf6, 0x83, 0x39, 0x31,
	0x71, 0xb6, 0xe1, 0x78, 0xf6, 0x5e, 0x04, 0x17, 0xc9, 0x8f, 0x66, 0xf3, 0x3c, 0xca, 0x74, 0xb7,
	0x3f, 0x98, 0x1f, 0x19, 0xa7, 0x3d, 0x81, 0x96, 0xba, 0xff, 0xf5, 0xc6, 0xf7, 0x3b, 0xf1, 0x77,
	0x22, 0xc8, 0x3f, 0x33, 0xc9, 0xf9, 0xc9, 0x5c, 0x8e, 0xa7, 0x22, 0x0b, 0xd6, 0xfe, 0x9d, 0x73,
	0x8e, 0x44, 0xf6, 0xfe, 0x5a, 0x83, 0x77, 0xa7, 0xe7, 0x6b, 0xc8, 0xc6, 0x04, 0x97, 0x7c, 0x5a,
	0x86, 0xa8, 0xfd, 0xd1, 0xd9, 0x07, 0xc5, 0xaa, 0x31, 0xfb, 0x10, 0xd5, 0x67, 0xa5, 0xb4, 0xd5,
	0xaa, 0x71, 0xac, 0x54, 0x46, 0x5f, 0x20, 0xbf, 0xd4, 0x30, 0xa8, 0x30, 0x2d, 0xdf, 0x42, 0x66,
	0xf0, 0xad, 0xce, 0x2c, 0xb5, 0x37, 0xcf, 0x31, 0x2a, 0x65, 0x09, 0x92, 0xe8, 0xfa, 0x04, 0x4b,
	0x90, 0x49, 0x4e, 0x4c, 0xb0, 0x04, 0xd9, 0xd4, 0x83, 0xb8, 0xd7, 0x8a, 0x70, 0xb5, 0xea, 0x5e,
	0xab, 0x83, 0xeb, 0xaa, 0x7b, 0x3d, 0x21, 0xfe, 0x3d, 0x5a, 0x49, 0xea, 0x1f, 0x31, 0xfa, 0x8c,
	0x57, 0xcd, 0xe4, 0x95, 0x64, 0xa3, 0xad, 0x42, 0x19, 0x8e, 0x47, 0x62, 0x55, 0xca, 0x50, 0x19,
	0x46, 0x6e, 0xdf, 0x9f, 0x37, 0xb0, 0xab, 0x2f, 0x6c, 0x7f, 0xf4, 0xf3, 0x4e, 0xcf, 0x77, 0x4d,
	0xaf, 0xb7, 0xbe, 0xd9, 0x89, 0xa2, 0x75, 0xcb, 0x1f, 0x7c, 0x88, 0xff, 0x44, 0xb6, 0x7c, 0xf7,
	0x43, 0x46, 0xc3, 0x37, 0x8e, 0x45, 0xd9, 0x87, 0x79, 0x72, 0x87, 0xcb, 0x88, 0xb3, 0xf1, 0xff,
	0x01, 0x00, 0x00, 0xff, 0xff, 0x06, 0xbc, 0xf6, 0x7b, 0xda, 0x3c, 0x00, 0x00,
}
