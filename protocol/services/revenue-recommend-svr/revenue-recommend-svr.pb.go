// Code generated by protoc-gen-go. DO NOT EDIT.
// source: revenue-recommend-svr/revenue-recommend-svr.proto

package revenue_recommend_svr // import "golang.52tt.com/protocol/services/revenue-recommend-svr"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 请求来源
type GetRevenueRecListReq_ReqSource int32

const (
	GetRevenueRecListReq_Req_Source_Invalid       GetRevenueRecListReq_ReqSource = 0
	GetRevenueRecListReq_Req_Source_Ugc_Recommend GetRevenueRecListReq_ReqSource = 1
	GetRevenueRecListReq_Req_Source_Ugc_King      GetRevenueRecListReq_ReqSource = 2
)

var GetRevenueRecListReq_ReqSource_name = map[int32]string{
	0: "Req_Source_Invalid",
	1: "Req_Source_Ugc_Recommend",
	2: "Req_Source_Ugc_King",
}
var GetRevenueRecListReq_ReqSource_value = map[string]int32{
	"Req_Source_Invalid":       0,
	"Req_Source_Ugc_Recommend": 1,
	"Req_Source_Ugc_King":      2,
}

func (x GetRevenueRecListReq_ReqSource) String() string {
	return proto.EnumName(GetRevenueRecListReq_ReqSource_name, int32(x))
}
func (GetRevenueRecListReq_ReqSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_revenue_recommend_svr_80a036f300387d96, []int{1, 0}
}

// 推荐内容类型
type GetUserRevenueRecReq_RecType int32

const (
	GetUserRevenueRecReq_Req_Type_Invalid      GetUserRevenueRecReq_RecType = 0
	GetUserRevenueRecReq_Req_Type_Top_Over_Lay GetUserRevenueRecReq_RecType = 1
)

var GetUserRevenueRecReq_RecType_name = map[int32]string{
	0: "Req_Type_Invalid",
	1: "Req_Type_Top_Over_Lay",
}
var GetUserRevenueRecReq_RecType_value = map[string]int32{
	"Req_Type_Invalid":      0,
	"Req_Type_Top_Over_Lay": 1,
}

func (x GetUserRevenueRecReq_RecType) String() string {
	return proto.EnumName(GetUserRevenueRecReq_RecType_name, int32(x))
}
func (GetUserRevenueRecReq_RecType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_revenue_recommend_svr_80a036f300387d96, []int{3, 0}
}

type RevenueRecInfo struct {
	MarshalType          uint32   `protobuf:"varint,1,opt,name=marshal_type,json=marshalType,proto3" json:"marshal_type,omitempty"`
	Data                 []byte   `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	IsShow               bool     `protobuf:"varint,3,opt,name=is_show,json=isShow,proto3" json:"is_show,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RevenueRecInfo) Reset()         { *m = RevenueRecInfo{} }
func (m *RevenueRecInfo) String() string { return proto.CompactTextString(m) }
func (*RevenueRecInfo) ProtoMessage()    {}
func (*RevenueRecInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_recommend_svr_80a036f300387d96, []int{0}
}
func (m *RevenueRecInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RevenueRecInfo.Unmarshal(m, b)
}
func (m *RevenueRecInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RevenueRecInfo.Marshal(b, m, deterministic)
}
func (dst *RevenueRecInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RevenueRecInfo.Merge(dst, src)
}
func (m *RevenueRecInfo) XXX_Size() int {
	return xxx_messageInfo_RevenueRecInfo.Size(m)
}
func (m *RevenueRecInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RevenueRecInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RevenueRecInfo proto.InternalMessageInfo

func (m *RevenueRecInfo) GetMarshalType() uint32 {
	if m != nil {
		return m.MarshalType
	}
	return 0
}

func (m *RevenueRecInfo) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *RevenueRecInfo) GetIsShow() bool {
	if m != nil {
		return m.IsShow
	}
	return false
}

// 获取营收推荐列表
type GetRevenueRecListReq struct {
	ReqSource            uint32   `protobuf:"varint,1,opt,name=req_source,json=reqSource,proto3" json:"req_source,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Count                uint32   `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	TabId                uint32   `protobuf:"varint,4,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRevenueRecListReq) Reset()         { *m = GetRevenueRecListReq{} }
func (m *GetRevenueRecListReq) String() string { return proto.CompactTextString(m) }
func (*GetRevenueRecListReq) ProtoMessage()    {}
func (*GetRevenueRecListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_recommend_svr_80a036f300387d96, []int{1}
}
func (m *GetRevenueRecListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRevenueRecListReq.Unmarshal(m, b)
}
func (m *GetRevenueRecListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRevenueRecListReq.Marshal(b, m, deterministic)
}
func (dst *GetRevenueRecListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRevenueRecListReq.Merge(dst, src)
}
func (m *GetRevenueRecListReq) XXX_Size() int {
	return xxx_messageInfo_GetRevenueRecListReq.Size(m)
}
func (m *GetRevenueRecListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRevenueRecListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRevenueRecListReq proto.InternalMessageInfo

func (m *GetRevenueRecListReq) GetReqSource() uint32 {
	if m != nil {
		return m.ReqSource
	}
	return 0
}

func (m *GetRevenueRecListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRevenueRecListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetRevenueRecListReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetRevenueRecListResp struct {
	InfoList             []*RevenueRecInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	CidList              []uint32          `protobuf:"varint,2,rep,packed,name=cid_list,json=cidList,proto3" json:"cid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetRevenueRecListResp) Reset()         { *m = GetRevenueRecListResp{} }
func (m *GetRevenueRecListResp) String() string { return proto.CompactTextString(m) }
func (*GetRevenueRecListResp) ProtoMessage()    {}
func (*GetRevenueRecListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_recommend_svr_80a036f300387d96, []int{2}
}
func (m *GetRevenueRecListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRevenueRecListResp.Unmarshal(m, b)
}
func (m *GetRevenueRecListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRevenueRecListResp.Marshal(b, m, deterministic)
}
func (dst *GetRevenueRecListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRevenueRecListResp.Merge(dst, src)
}
func (m *GetRevenueRecListResp) XXX_Size() int {
	return xxx_messageInfo_GetRevenueRecListResp.Size(m)
}
func (m *GetRevenueRecListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRevenueRecListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRevenueRecListResp proto.InternalMessageInfo

func (m *GetRevenueRecListResp) GetInfoList() []*RevenueRecInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetRevenueRecListResp) GetCidList() []uint32 {
	if m != nil {
		return m.CidList
	}
	return nil
}

// 获取用户营收推荐内容
type GetUserRevenueRecReq struct {
	RecType              uint32            `protobuf:"varint,1,opt,name=rec_type,json=recType,proto3" json:"rec_type,omitempty"`
	Uid                  uint32            `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	RefreshCnt           uint32            `protobuf:"varint,3,opt,name=refresh_cnt,json=refreshCnt,proto3" json:"refresh_cnt,omitempty"`
	MapTypeCnt           map[uint32]uint32 `protobuf:"bytes,4,rep,name=map_type_cnt,json=mapTypeCnt,proto3" json:"map_type_cnt,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetUserRevenueRecReq) Reset()         { *m = GetUserRevenueRecReq{} }
func (m *GetUserRevenueRecReq) String() string { return proto.CompactTextString(m) }
func (*GetUserRevenueRecReq) ProtoMessage()    {}
func (*GetUserRevenueRecReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_recommend_svr_80a036f300387d96, []int{3}
}
func (m *GetUserRevenueRecReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRevenueRecReq.Unmarshal(m, b)
}
func (m *GetUserRevenueRecReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRevenueRecReq.Marshal(b, m, deterministic)
}
func (dst *GetUserRevenueRecReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRevenueRecReq.Merge(dst, src)
}
func (m *GetUserRevenueRecReq) XXX_Size() int {
	return xxx_messageInfo_GetUserRevenueRecReq.Size(m)
}
func (m *GetUserRevenueRecReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRevenueRecReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRevenueRecReq proto.InternalMessageInfo

func (m *GetUserRevenueRecReq) GetRecType() uint32 {
	if m != nil {
		return m.RecType
	}
	return 0
}

func (m *GetUserRevenueRecReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserRevenueRecReq) GetRefreshCnt() uint32 {
	if m != nil {
		return m.RefreshCnt
	}
	return 0
}

func (m *GetUserRevenueRecReq) GetMapTypeCnt() map[uint32]uint32 {
	if m != nil {
		return m.MapTypeCnt
	}
	return nil
}

type GetUserRevenueRecResp struct {
	RecInfo              []byte   `protobuf:"bytes,1,opt,name=rec_info,json=recInfo,proto3" json:"rec_info,omitempty"`
	IntervalTs           uint32   `protobuf:"varint,2,opt,name=interval_ts,json=intervalTs,proto3" json:"interval_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserRevenueRecResp) Reset()         { *m = GetUserRevenueRecResp{} }
func (m *GetUserRevenueRecResp) String() string { return proto.CompactTextString(m) }
func (*GetUserRevenueRecResp) ProtoMessage()    {}
func (*GetUserRevenueRecResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_recommend_svr_80a036f300387d96, []int{4}
}
func (m *GetUserRevenueRecResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRevenueRecResp.Unmarshal(m, b)
}
func (m *GetUserRevenueRecResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRevenueRecResp.Marshal(b, m, deterministic)
}
func (dst *GetUserRevenueRecResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRevenueRecResp.Merge(dst, src)
}
func (m *GetUserRevenueRecResp) XXX_Size() int {
	return xxx_messageInfo_GetUserRevenueRecResp.Size(m)
}
func (m *GetUserRevenueRecResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRevenueRecResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRevenueRecResp proto.InternalMessageInfo

func (m *GetUserRevenueRecResp) GetRecInfo() []byte {
	if m != nil {
		return m.RecInfo
	}
	return nil
}

func (m *GetUserRevenueRecResp) GetIntervalTs() uint32 {
	if m != nil {
		return m.IntervalTs
	}
	return 0
}

type AddTopOverlayFollowInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Cid                  uint32   `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	FollowUid            uint32   `protobuf:"varint,3,opt,name=follow_uid,json=followUid,proto3" json:"follow_uid,omitempty"`
	FollowerTxt          string   `protobuf:"bytes,4,opt,name=follower_txt,json=followerTxt,proto3" json:"follower_txt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddTopOverlayFollowInfoReq) Reset()         { *m = AddTopOverlayFollowInfoReq{} }
func (m *AddTopOverlayFollowInfoReq) String() string { return proto.CompactTextString(m) }
func (*AddTopOverlayFollowInfoReq) ProtoMessage()    {}
func (*AddTopOverlayFollowInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_recommend_svr_80a036f300387d96, []int{5}
}
func (m *AddTopOverlayFollowInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddTopOverlayFollowInfoReq.Unmarshal(m, b)
}
func (m *AddTopOverlayFollowInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddTopOverlayFollowInfoReq.Marshal(b, m, deterministic)
}
func (dst *AddTopOverlayFollowInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddTopOverlayFollowInfoReq.Merge(dst, src)
}
func (m *AddTopOverlayFollowInfoReq) XXX_Size() int {
	return xxx_messageInfo_AddTopOverlayFollowInfoReq.Size(m)
}
func (m *AddTopOverlayFollowInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddTopOverlayFollowInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddTopOverlayFollowInfoReq proto.InternalMessageInfo

func (m *AddTopOverlayFollowInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddTopOverlayFollowInfoReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *AddTopOverlayFollowInfoReq) GetFollowUid() uint32 {
	if m != nil {
		return m.FollowUid
	}
	return 0
}

func (m *AddTopOverlayFollowInfoReq) GetFollowerTxt() string {
	if m != nil {
		return m.FollowerTxt
	}
	return ""
}

type AddTopOverlayFollowInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddTopOverlayFollowInfoResp) Reset()         { *m = AddTopOverlayFollowInfoResp{} }
func (m *AddTopOverlayFollowInfoResp) String() string { return proto.CompactTextString(m) }
func (*AddTopOverlayFollowInfoResp) ProtoMessage()    {}
func (*AddTopOverlayFollowInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_recommend_svr_80a036f300387d96, []int{6}
}
func (m *AddTopOverlayFollowInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddTopOverlayFollowInfoResp.Unmarshal(m, b)
}
func (m *AddTopOverlayFollowInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddTopOverlayFollowInfoResp.Marshal(b, m, deterministic)
}
func (dst *AddTopOverlayFollowInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddTopOverlayFollowInfoResp.Merge(dst, src)
}
func (m *AddTopOverlayFollowInfoResp) XXX_Size() int {
	return xxx_messageInfo_AddTopOverlayFollowInfoResp.Size(m)
}
func (m *AddTopOverlayFollowInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddTopOverlayFollowInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddTopOverlayFollowInfoResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*RevenueRecInfo)(nil), "revenue_recommend_svr.RevenueRecInfo")
	proto.RegisterType((*GetRevenueRecListReq)(nil), "revenue_recommend_svr.GetRevenueRecListReq")
	proto.RegisterType((*GetRevenueRecListResp)(nil), "revenue_recommend_svr.GetRevenueRecListResp")
	proto.RegisterType((*GetUserRevenueRecReq)(nil), "revenue_recommend_svr.GetUserRevenueRecReq")
	proto.RegisterMapType((map[uint32]uint32)(nil), "revenue_recommend_svr.GetUserRevenueRecReq.MapTypeCntEntry")
	proto.RegisterType((*GetUserRevenueRecResp)(nil), "revenue_recommend_svr.GetUserRevenueRecResp")
	proto.RegisterType((*AddTopOverlayFollowInfoReq)(nil), "revenue_recommend_svr.AddTopOverlayFollowInfoReq")
	proto.RegisterType((*AddTopOverlayFollowInfoResp)(nil), "revenue_recommend_svr.AddTopOverlayFollowInfoResp")
	proto.RegisterEnum("revenue_recommend_svr.GetRevenueRecListReq_ReqSource", GetRevenueRecListReq_ReqSource_name, GetRevenueRecListReq_ReqSource_value)
	proto.RegisterEnum("revenue_recommend_svr.GetUserRevenueRecReq_RecType", GetUserRevenueRecReq_RecType_name, GetUserRevenueRecReq_RecType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RevenueRecommendServiceClient is the client API for RevenueRecommendService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RevenueRecommendServiceClient interface {
	// 获取ugc的推荐营收房
	GetRevenueRecList(ctx context.Context, in *GetRevenueRecListReq, opts ...grpc.CallOption) (*GetRevenueRecListResp, error)
	// 获取用户营收推荐内容
	GetUserRevenueRec(ctx context.Context, in *GetUserRevenueRecReq, opts ...grpc.CallOption) (*GetUserRevenueRecResp, error)
	// 添加顶部浮窗推荐跟随信息
	AddTopOverlayFollowInfo(ctx context.Context, in *AddTopOverlayFollowInfoReq, opts ...grpc.CallOption) (*AddTopOverlayFollowInfoResp, error)
}

type revenueRecommendServiceClient struct {
	cc *grpc.ClientConn
}

func NewRevenueRecommendServiceClient(cc *grpc.ClientConn) RevenueRecommendServiceClient {
	return &revenueRecommendServiceClient{cc}
}

func (c *revenueRecommendServiceClient) GetRevenueRecList(ctx context.Context, in *GetRevenueRecListReq, opts ...grpc.CallOption) (*GetRevenueRecListResp, error) {
	out := new(GetRevenueRecListResp)
	err := c.cc.Invoke(ctx, "/revenue_recommend_svr.RevenueRecommendService/GetRevenueRecList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueRecommendServiceClient) GetUserRevenueRec(ctx context.Context, in *GetUserRevenueRecReq, opts ...grpc.CallOption) (*GetUserRevenueRecResp, error) {
	out := new(GetUserRevenueRecResp)
	err := c.cc.Invoke(ctx, "/revenue_recommend_svr.RevenueRecommendService/GetUserRevenueRec", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueRecommendServiceClient) AddTopOverlayFollowInfo(ctx context.Context, in *AddTopOverlayFollowInfoReq, opts ...grpc.CallOption) (*AddTopOverlayFollowInfoResp, error) {
	out := new(AddTopOverlayFollowInfoResp)
	err := c.cc.Invoke(ctx, "/revenue_recommend_svr.RevenueRecommendService/AddTopOverlayFollowInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RevenueRecommendServiceServer is the server API for RevenueRecommendService service.
type RevenueRecommendServiceServer interface {
	// 获取ugc的推荐营收房
	GetRevenueRecList(context.Context, *GetRevenueRecListReq) (*GetRevenueRecListResp, error)
	// 获取用户营收推荐内容
	GetUserRevenueRec(context.Context, *GetUserRevenueRecReq) (*GetUserRevenueRecResp, error)
	// 添加顶部浮窗推荐跟随信息
	AddTopOverlayFollowInfo(context.Context, *AddTopOverlayFollowInfoReq) (*AddTopOverlayFollowInfoResp, error)
}

func RegisterRevenueRecommendServiceServer(s *grpc.Server, srv RevenueRecommendServiceServer) {
	s.RegisterService(&_RevenueRecommendService_serviceDesc, srv)
}

func _RevenueRecommendService_GetRevenueRecList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRevenueRecListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueRecommendServiceServer).GetRevenueRecList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_recommend_svr.RevenueRecommendService/GetRevenueRecList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueRecommendServiceServer).GetRevenueRecList(ctx, req.(*GetRevenueRecListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueRecommendService_GetUserRevenueRec_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserRevenueRecReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueRecommendServiceServer).GetUserRevenueRec(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_recommend_svr.RevenueRecommendService/GetUserRevenueRec",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueRecommendServiceServer).GetUserRevenueRec(ctx, req.(*GetUserRevenueRecReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueRecommendService_AddTopOverlayFollowInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddTopOverlayFollowInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueRecommendServiceServer).AddTopOverlayFollowInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_recommend_svr.RevenueRecommendService/AddTopOverlayFollowInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueRecommendServiceServer).AddTopOverlayFollowInfo(ctx, req.(*AddTopOverlayFollowInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _RevenueRecommendService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "revenue_recommend_svr.RevenueRecommendService",
	HandlerType: (*RevenueRecommendServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetRevenueRecList",
			Handler:    _RevenueRecommendService_GetRevenueRecList_Handler,
		},
		{
			MethodName: "GetUserRevenueRec",
			Handler:    _RevenueRecommendService_GetUserRevenueRec_Handler,
		},
		{
			MethodName: "AddTopOverlayFollowInfo",
			Handler:    _RevenueRecommendService_AddTopOverlayFollowInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "revenue-recommend-svr/revenue-recommend-svr.proto",
}

func init() {
	proto.RegisterFile("revenue-recommend-svr/revenue-recommend-svr.proto", fileDescriptor_revenue_recommend_svr_80a036f300387d96)
}

var fileDescriptor_revenue_recommend_svr_80a036f300387d96 = []byte{
	// 686 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x54, 0xd1, 0x6e, 0x1a, 0x3b,
	0x10, 0xcd, 0x42, 0x6e, 0x80, 0x21, 0xdc, 0xcb, 0x75, 0x43, 0x21, 0xb4, 0x51, 0xe8, 0x4a, 0x95,
	0x90, 0xda, 0x10, 0x85, 0xaa, 0x6a, 0x9b, 0xaa, 0x0f, 0x4d, 0xd4, 0x56, 0x51, 0x53, 0x55, 0x32,
	0xe4, 0x25, 0x52, 0xe5, 0x6e, 0xbc, 0x06, 0xac, 0x2e, 0xeb, 0xc5, 0x36, 0x9b, 0xf0, 0x9a, 0x0f,
	0xeb, 0x37, 0xf4, 0x93, 0x2a, 0x7b, 0x17, 0x48, 0x02, 0x44, 0xed, 0x9b, 0x7d, 0xc6, 0x9e, 0x39,
	0x73, 0x8e, 0xc7, 0x70, 0x20, 0x59, 0xcc, 0xc2, 0x31, 0xdb, 0x93, 0x8c, 0x8a, 0xe1, 0x90, 0x85,
	0xfe, 0x9e, 0x8a, 0xe5, 0xfe, 0x52, 0xb4, 0x15, 0x49, 0xa1, 0x05, 0xaa, 0xa4, 0x41, 0x32, 0x0b,
	0x12, 0x15, 0x4b, 0xf7, 0x3b, 0xfc, 0x8b, 0x93, 0x00, 0x66, 0xf4, 0x24, 0xec, 0x09, 0xf4, 0x04,
	0x36, 0x87, 0x9e, 0x54, 0x03, 0x2f, 0x20, 0x7a, 0x12, 0xb1, 0x9a, 0xd3, 0x70, 0x9a, 0x25, 0x5c,
	0x4c, 0xb1, 0xee, 0x24, 0x62, 0x08, 0xc1, 0xba, 0xef, 0x69, 0xaf, 0x96, 0x69, 0x38, 0xcd, 0x4d,
	0x6c, 0xd7, 0xa8, 0x0a, 0x39, 0xae, 0x88, 0x1a, 0x88, 0xcb, 0x5a, 0xb6, 0xe1, 0x34, 0xf3, 0x78,
	0x83, 0xab, 0xce, 0x40, 0x5c, 0xba, 0xbf, 0x1c, 0xd8, 0xfa, 0xc4, 0xf4, 0xbc, 0xca, 0x29, 0x57,
	0x1a, 0xb3, 0x11, 0xda, 0x01, 0x90, 0x6c, 0x44, 0x94, 0x18, 0x4b, 0x3a, 0x2d, 0x53, 0x90, 0x6c,
	0xd4, 0xb1, 0x00, 0x2a, 0x43, 0x76, 0xcc, 0x7d, 0x5b, 0xa3, 0x84, 0xcd, 0x12, 0x6d, 0xc1, 0x3f,
	0x54, 0x8c, 0x43, 0x6d, 0x0b, 0x94, 0x70, 0xb2, 0x41, 0x15, 0xd8, 0xd0, 0xde, 0x05, 0xe1, 0x7e,
	0x6d, 0x3d, 0x81, 0xb5, 0x77, 0x71, 0xe2, 0xbb, 0xe7, 0x50, 0xc0, 0xb3, 0x5c, 0x0f, 0x01, 0x61,
	0x36, 0x22, 0xc9, 0x8e, 0x9c, 0x84, 0xb1, 0x17, 0x70, 0xbf, 0xbc, 0x86, 0x1e, 0x43, 0xed, 0x06,
	0x7e, 0xd6, 0xa7, 0x04, 0x4f, 0xd5, 0x29, 0x3b, 0xa8, 0x0a, 0x0f, 0xee, 0x44, 0x3f, 0xf3, 0xb0,
	0x5f, 0xce, 0xb8, 0x31, 0x54, 0x96, 0x74, 0xa4, 0x22, 0x74, 0x04, 0x05, 0x1e, 0xf6, 0x04, 0x09,
	0xb8, 0xd2, 0x35, 0xa7, 0x91, 0x6d, 0x16, 0xdb, 0x4f, 0x5b, 0x4b, 0x85, 0x6f, 0xdd, 0x56, 0x1d,
	0xe7, 0xcd, 0x3d, 0x93, 0x07, 0x6d, 0x43, 0x9e, 0x72, 0x3f, 0x49, 0x91, 0x69, 0x64, 0x9b, 0x25,
	0x9c, 0xa3, 0xdc, 0x37, 0x21, 0xf7, 0x67, 0xc6, 0x4a, 0x79, 0xa6, 0x98, 0x9c, 0x5f, 0x37, 0x52,
	0x6e, 0x43, 0x5e, 0x32, 0x7a, 0xd3, 0xaf, 0x9c, 0x64, 0xd4, 0x7a, 0xb5, 0x28, 0xe3, 0x2e, 0x14,
	0x25, 0xeb, 0x49, 0xa6, 0x06, 0x84, 0xce, 0xc4, 0x84, 0x14, 0x3a, 0x0e, 0x35, 0xfa, 0x66, 0x5e,
	0x40, 0x64, 0xb3, 0xd9, 0x13, 0xeb, 0xb6, 0x91, 0xb7, 0x2b, 0x1a, 0x59, 0x46, 0xa8, 0xf5, 0xc5,
	0x8b, 0x4c, 0xf5, 0xe3, 0x50, 0x7f, 0x08, 0xb5, 0x9c, 0x60, 0x18, 0xce, 0x80, 0xfa, 0x3b, 0xf8,
	0xef, 0x4e, 0xd8, 0x90, 0xfc, 0xc1, 0x26, 0x29, 0x75, 0xb3, 0x34, 0x5e, 0xc7, 0x5e, 0x30, 0x66,
	0x29, 0xf1, 0x64, 0x73, 0x98, 0x79, 0xed, 0xb8, 0x87, 0x90, 0xc3, 0x69, 0x6f, 0x5b, 0x50, 0x36,
	0x06, 0x99, 0xf5, 0x0d, 0x53, 0xb7, 0xa1, 0x32, 0x43, 0xbb, 0x22, 0x22, 0x5f, 0x63, 0x26, 0xc9,
	0xa9, 0x37, 0x29, 0x3b, 0x6e, 0xc7, 0x1a, 0x77, 0x97, 0xae, 0x8a, 0xa6, 0x02, 0x1a, 0x13, 0x2c,
	0x8b, 0x4d, 0x2b, 0xa0, 0x9d, 0x87, 0x5d, 0x28, 0xf2, 0x50, 0x33, 0x19, 0x9b, 0x81, 0x50, 0x29,
	0x1f, 0x98, 0x42, 0x5d, 0xe5, 0x5e, 0x3b, 0x50, 0x7f, 0xef, 0xfb, 0x5d, 0x11, 0x99, 0x4a, 0x81,
	0x37, 0xf9, 0x28, 0x82, 0x40, 0x5c, 0x5a, 0x5b, 0xd9, 0x68, 0x6a, 0x80, 0x33, 0x37, 0xa0, 0x0c,
	0x59, 0x3a, 0xb7, 0x84, 0x72, 0xdf, 0x8c, 0x42, 0xcf, 0x5e, 0x22, 0xe6, 0x68, 0xe2, 0x48, 0x21,
	0x41, 0xce, 0xb8, 0x6f, 0x46, 0x32, 0xd9, 0x30, 0x49, 0xf4, 0x95, 0xb6, 0x0f, 0xbd, 0x80, 0x8b,
	0x53, 0xac, 0x7b, 0xa5, 0xdd, 0x1d, 0x78, 0xb4, 0x92, 0x83, 0x8a, 0xda, 0xd7, 0x59, 0xa8, 0xce,
	0x5b, 0x4e, 0xdc, 0xeb, 0x30, 0x19, 0x73, 0xca, 0x50, 0x04, 0xff, 0x2f, 0xbc, 0x66, 0xf4, 0x6c,
	0xb5, 0xdb, 0x0b, 0x93, 0x5c, 0x7f, 0xfe, 0xe7, 0x87, 0x55, 0xe4, 0xae, 0xa5, 0x15, 0x6f, 0xdb,
	0x70, 0x5f, 0xc5, 0x85, 0xf7, 0x75, 0x5f, 0xc5, 0x45, 0x77, 0xdd, 0x35, 0x74, 0xed, 0x40, 0x75,
	0x85, 0x3e, 0xe8, 0x60, 0x45, 0xae, 0xd5, 0x9e, 0xd6, 0xdb, 0x7f, 0x7b, 0xc5, 0x90, 0x38, 0x7a,
	0x73, 0xfe, 0xaa, 0x2f, 0x02, 0x2f, 0xec, 0xb7, 0x5e, 0xb6, 0xb5, 0x6e, 0x51, 0x31, 0xdc, 0xb7,
	0x7f, 0x33, 0x15, 0xc1, 0xbe, 0x4a, 0xcc, 0x50, 0xcb, 0xff, 0xf0, 0x8b, 0x0d, 0x7b, 0xf0, 0xc5,
	0xef, 0x00, 0x00, 0x00, 0xff, 0xff, 0x9e, 0x92, 0x54, 0xe0, 0xf9, 0x05, 0x00, 0x00,
}
