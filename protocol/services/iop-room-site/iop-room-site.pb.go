// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/iop-room-site/iop-room-site.proto

package iop_room_site // import "golang.52tt.com/protocol/services/iop-room-site"

/*
buf:lint:ignore PACKAGE_DIRECTORY_MATCH
buf:lint:ignore PACKAGE_SAME_DIRECTORY
*/

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type GetRecommendRoomByTagIdReq struct {
	AppName              string            `protobuf:"bytes,1,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
	AppType              string            `protobuf:"bytes,2,opt,name=app_type,json=appType,proto3" json:"app_type,omitempty"`
	SiteId               string            `protobuf:"bytes,3,opt,name=site_id,json=siteId,proto3" json:"site_id,omitempty"`
	BizSiteId            string            `protobuf:"bytes,4,opt,name=biz_site_id,json=bizSiteId,proto3" json:"biz_site_id,omitempty"`
	Uid                  string            `protobuf:"bytes,5,opt,name=uid,proto3" json:"uid,omitempty"`
	ReqExt               map[string]string `protobuf:"bytes,6,rep,name=req_ext,json=reqExt,proto3" json:"req_ext,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetRecommendRoomByTagIdReq) Reset()         { *m = GetRecommendRoomByTagIdReq{} }
func (m *GetRecommendRoomByTagIdReq) String() string { return proto.CompactTextString(m) }
func (*GetRecommendRoomByTagIdReq) ProtoMessage()    {}
func (*GetRecommendRoomByTagIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_iop_room_site_4daa979f34de25b4, []int{0}
}
func (m *GetRecommendRoomByTagIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendRoomByTagIdReq.Unmarshal(m, b)
}
func (m *GetRecommendRoomByTagIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendRoomByTagIdReq.Marshal(b, m, deterministic)
}
func (dst *GetRecommendRoomByTagIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendRoomByTagIdReq.Merge(dst, src)
}
func (m *GetRecommendRoomByTagIdReq) XXX_Size() int {
	return xxx_messageInfo_GetRecommendRoomByTagIdReq.Size(m)
}
func (m *GetRecommendRoomByTagIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendRoomByTagIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendRoomByTagIdReq proto.InternalMessageInfo

func (m *GetRecommendRoomByTagIdReq) GetAppName() string {
	if m != nil {
		return m.AppName
	}
	return ""
}

func (m *GetRecommendRoomByTagIdReq) GetAppType() string {
	if m != nil {
		return m.AppType
	}
	return ""
}

func (m *GetRecommendRoomByTagIdReq) GetSiteId() string {
	if m != nil {
		return m.SiteId
	}
	return ""
}

func (m *GetRecommendRoomByTagIdReq) GetBizSiteId() string {
	if m != nil {
		return m.BizSiteId
	}
	return ""
}

func (m *GetRecommendRoomByTagIdReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *GetRecommendRoomByTagIdReq) GetReqExt() map[string]string {
	if m != nil {
		return m.ReqExt
	}
	return nil
}

type GetRecommendRoomByTagIdResp struct {
	Code                 uint32            `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	RoomInfo             *RoomInfo         `protobuf:"bytes,2,opt,name=room_info,json=roomInfo,proto3" json:"room_info,omitempty"`
	RespExt              map[string]string `protobuf:"bytes,3,rep,name=resp_ext,json=respExt,proto3" json:"resp_ext,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetRecommendRoomByTagIdResp) Reset()         { *m = GetRecommendRoomByTagIdResp{} }
func (m *GetRecommendRoomByTagIdResp) String() string { return proto.CompactTextString(m) }
func (*GetRecommendRoomByTagIdResp) ProtoMessage()    {}
func (*GetRecommendRoomByTagIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_iop_room_site_4daa979f34de25b4, []int{1}
}
func (m *GetRecommendRoomByTagIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendRoomByTagIdResp.Unmarshal(m, b)
}
func (m *GetRecommendRoomByTagIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendRoomByTagIdResp.Marshal(b, m, deterministic)
}
func (dst *GetRecommendRoomByTagIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendRoomByTagIdResp.Merge(dst, src)
}
func (m *GetRecommendRoomByTagIdResp) XXX_Size() int {
	return xxx_messageInfo_GetRecommendRoomByTagIdResp.Size(m)
}
func (m *GetRecommendRoomByTagIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendRoomByTagIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendRoomByTagIdResp proto.InternalMessageInfo

func (m *GetRecommendRoomByTagIdResp) GetCode() uint32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetRecommendRoomByTagIdResp) GetRoomInfo() *RoomInfo {
	if m != nil {
		return m.RoomInfo
	}
	return nil
}

func (m *GetRecommendRoomByTagIdResp) GetRespExt() map[string]string {
	if m != nil {
		return m.RespExt
	}
	return nil
}

type RoomInfo struct {
	RoomId               string   `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RoomInfo) Reset()         { *m = RoomInfo{} }
func (m *RoomInfo) String() string { return proto.CompactTextString(m) }
func (*RoomInfo) ProtoMessage()    {}
func (*RoomInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_iop_room_site_4daa979f34de25b4, []int{2}
}
func (m *RoomInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RoomInfo.Unmarshal(m, b)
}
func (m *RoomInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RoomInfo.Marshal(b, m, deterministic)
}
func (dst *RoomInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RoomInfo.Merge(dst, src)
}
func (m *RoomInfo) XXX_Size() int {
	return xxx_messageInfo_RoomInfo.Size(m)
}
func (m *RoomInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RoomInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RoomInfo proto.InternalMessageInfo

func (m *RoomInfo) GetRoomId() string {
	if m != nil {
		return m.RoomId
	}
	return ""
}

func init() {
	proto.RegisterType((*GetRecommendRoomByTagIdReq)(nil), "com.quwan.dspiopother.proto.GetRecommendRoomByTagIdReq")
	proto.RegisterMapType((map[string]string)(nil), "com.quwan.dspiopother.proto.GetRecommendRoomByTagIdReq.ReqExtEntry")
	proto.RegisterType((*GetRecommendRoomByTagIdResp)(nil), "com.quwan.dspiopother.proto.GetRecommendRoomByTagIdResp")
	proto.RegisterMapType((map[string]string)(nil), "com.quwan.dspiopother.proto.GetRecommendRoomByTagIdResp.RespExtEntry")
	proto.RegisterType((*RoomInfo)(nil), "com.quwan.dspiopother.proto.RoomInfo")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RoomSiteServiceClient is the client API for RoomSiteService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RoomSiteServiceClient interface {
	// 获取指定SiteId下的推荐房间
	GetRecommendRoomBySiteId(ctx context.Context, in *GetRecommendRoomByTagIdReq, opts ...grpc.CallOption) (*GetRecommendRoomByTagIdResp, error)
}

type roomSiteServiceClient struct {
	cc *grpc.ClientConn
}

func NewRoomSiteServiceClient(cc *grpc.ClientConn) RoomSiteServiceClient {
	return &roomSiteServiceClient{cc}
}

func (c *roomSiteServiceClient) GetRecommendRoomBySiteId(ctx context.Context, in *GetRecommendRoomByTagIdReq, opts ...grpc.CallOption) (*GetRecommendRoomByTagIdResp, error) {
	out := new(GetRecommendRoomByTagIdResp)
	err := c.cc.Invoke(ctx, "/com.quwan.dspiopother.proto.RoomSiteService/GetRecommendRoomBySiteId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RoomSiteServiceServer is the server API for RoomSiteService service.
type RoomSiteServiceServer interface {
	// 获取指定SiteId下的推荐房间
	GetRecommendRoomBySiteId(context.Context, *GetRecommendRoomByTagIdReq) (*GetRecommendRoomByTagIdResp, error)
}

func RegisterRoomSiteServiceServer(s *grpc.Server, srv RoomSiteServiceServer) {
	s.RegisterService(&_RoomSiteService_serviceDesc, srv)
}

func _RoomSiteService_GetRecommendRoomBySiteId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecommendRoomByTagIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomSiteServiceServer).GetRecommendRoomBySiteId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/com.quwan.dspiopother.proto.RoomSiteService/GetRecommendRoomBySiteId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomSiteServiceServer).GetRecommendRoomBySiteId(ctx, req.(*GetRecommendRoomByTagIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _RoomSiteService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "com.quwan.dspiopother.proto.RoomSiteService",
	HandlerType: (*RoomSiteServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetRecommendRoomBySiteId",
			Handler:    _RoomSiteService_GetRecommendRoomBySiteId_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/iop-room-site/iop-room-site.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/iop-room-site/iop-room-site.proto", fileDescriptor_iop_room_site_4daa979f34de25b4)
}

var fileDescriptor_iop_room_site_4daa979f34de25b4 = []byte{
	// 443 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x53, 0xd1, 0x6e, 0xd3, 0x30,
	0x14, 0x25, 0xed, 0x96, 0xb6, 0xb7, 0x20, 0x90, 0x85, 0x44, 0xe8, 0x24, 0x34, 0x15, 0x21, 0xed,
	0x65, 0x09, 0x2a, 0x42, 0x8c, 0x3d, 0x16, 0x55, 0xa8, 0x2f, 0x7b, 0xc8, 0xf6, 0x84, 0x90, 0x82,
	0x1b, 0xdf, 0x15, 0x6b, 0x4d, 0xae, 0x63, 0xbb, 0x65, 0xd9, 0x4f, 0xf0, 0xc0, 0x27, 0xf0, 0x0f,
	0x7c, 0x1f, 0xb2, 0xd3, 0x4e, 0x9b, 0xd0, 0x2a, 0xd1, 0x97, 0xe8, 0x9e, 0x7b, 0x1c, 0x1f, 0x9f,
	0x73, 0x6d, 0x78, 0x6b, 0x6d, 0x52, 0x2d, 0x65, 0x7e, 0x65, 0xe4, 0x62, 0x85, 0x3a, 0x91, 0xa4,
	0x8e, 0x35, 0x51, 0x71, 0x6c, 0xa4, 0xc5, 0xfb, 0x28, 0x56, 0x9a, 0x2c, 0xb1, 0x83, 0x9c, 0x8a,
	0xb8, 0x5a, 0xfe, 0xe0, 0x65, 0x2c, 0x8c, 0x92, 0xa4, 0xc8, 0x7e, 0x47, 0xdd, 0x90, 0xc3, 0x3f,
	0x2d, 0x18, 0x7c, 0x46, 0x9b, 0x62, 0x4e, 0x45, 0x81, 0xa5, 0x48, 0x89, 0x8a, 0x71, 0x7d, 0xc1,
	0xe7, 0x53, 0x91, 0x62, 0xc5, 0x5e, 0x42, 0x97, 0x2b, 0x95, 0x95, 0xbc, 0xc0, 0x28, 0x38, 0x0c,
	0x8e, 0x7a, 0x69, 0x87, 0x2b, 0x75, 0xc6, 0x0b, 0xdc, 0x50, 0xb6, 0x56, 0x18, 0xb5, 0x6e, 0xa9,
	0x8b, 0x5a, 0x21, 0x7b, 0x01, 0x1d, 0xa7, 0x9f, 0x49, 0x11, 0xb5, 0x3d, 0x13, 0x3a, 0x38, 0x15,
	0xec, 0x15, 0xf4, 0x67, 0xf2, 0x26, 0xdb, 0x90, 0x7b, 0x9e, 0xec, 0xcd, 0xe4, 0xcd, 0x79, 0xc3,
	0x3f, 0x83, 0xf6, 0x52, 0x8a, 0x68, 0xdf, 0xf7, 0x5d, 0xc9, 0xbe, 0x42, 0x47, 0x63, 0x95, 0xe1,
	0xb5, 0x8d, 0xc2, 0xc3, 0xf6, 0x51, 0x7f, 0xf4, 0x29, 0xde, 0x62, 0x27, 0x7e, 0xd8, 0x4a, 0x9c,
	0x62, 0x35, 0xb9, 0xb6, 0x93, 0xd2, 0xea, 0x3a, 0x0d, 0xb5, 0x07, 0x83, 0x8f, 0xd0, 0xbf, 0xd3,
	0x76, 0xf2, 0x57, 0x58, 0xaf, 0x8d, 0xba, 0x92, 0x3d, 0x87, 0xfd, 0x15, 0x5f, 0x2c, 0x37, 0x0e,
	0x1b, 0x70, 0xda, 0x3a, 0x09, 0x86, 0xbf, 0x5a, 0x70, 0xf0, 0xa0, 0x9a, 0x51, 0x8c, 0xc1, 0x5e,
	0x4e, 0xa2, 0x49, 0xed, 0x49, 0xea, 0x6b, 0x36, 0x86, 0x9e, 0x1b, 0x4e, 0x26, 0xcb, 0x4b, 0xf2,
	0x3b, 0xf6, 0x47, 0x6f, 0xb6, 0xda, 0x71, 0x9b, 0x4e, 0xcb, 0x4b, 0x4a, 0xbb, 0x7a, 0x5d, 0xb1,
	0x6f, 0xd0, 0xd5, 0x68, 0x94, 0x4f, 0xa4, 0xed, 0x13, 0x99, 0xec, 0x96, 0x88, 0x51, 0xb1, 0xfb,
	0xdc, 0x66, 0xd2, 0xd1, 0x0d, 0x1a, 0x9c, 0xc2, 0xe3, 0xbb, 0xc4, 0x7f, 0xa5, 0xf2, 0x1a, 0xba,
	0x9b, 0x33, 0xbb, 0x5b, 0xd0, 0xb8, 0x15, 0xeb, 0x7f, 0x43, 0x6f, 0x42, 0x8c, 0x7e, 0x07, 0xf0,
	0xd4, 0xad, 0x72, 0x43, 0x3f, 0x47, 0xbd, 0x92, 0x39, 0xb2, 0x9f, 0x01, 0x44, 0xff, 0x1e, 0x75,
	0x7d, 0x2d, 0x3e, 0xec, 0x38, 0xf3, 0xc1, 0xc9, 0xae, 0xd1, 0x0c, 0x1f, 0x8d, 0xcf, 0x60, 0xdb,
	0xc3, 0xf9, 0x92, 0xcc, 0x69, 0xc1, 0xcb, 0x79, 0xfc, 0x7e, 0x64, 0xad, 0x53, 0x49, 0x7c, 0x3b,
	0xa7, 0x45, 0x62, 0x1a, 0x4b, 0xe6, 0xfe, 0x63, 0x9c, 0x85, 0x7e, 0xc1, 0xbb, 0xbf, 0x01, 0x00,
	0x00, 0xff, 0xff, 0x96, 0xcd, 0xe3, 0x28, 0xc1, 0x03, 0x00, 0x00,
}
