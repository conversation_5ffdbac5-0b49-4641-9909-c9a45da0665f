// Code generated by protoc-gen-go. DO NOT EDIT.
// source: src/antispam/asyncjob/antispam_async.proto

package antispam_async

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type TASK_TYPE int32

const (
	TASK_TYPE_TEXT_CHECK_MESSAGE_PUSH_TASK TASK_TYPE = 1
	TASK_TYPE_SHUMEI_REGISTER_CHECK_TASK   TASK_TYPE = 2
	TASK_TYPE_SHUMEI_AUTH_CHECK_TASK       TASK_TYPE = 3
	TASK_TYPE_DATAVISOR_LOGIN_CHECK_TASK   TASK_TYPE = 4
	TASK_TYPE_DATAVISOR_COMM_CHECK_TASK    TASK_TYPE = 5
)

var TASK_TYPE_name = map[int32]string{
	1: "TEXT_CHECK_MESSAGE_PUSH_TASK",
	2: "SHUMEI_REGISTER_CHECK_TASK",
	3: "SHUMEI_AUTH_CHECK_TASK",
	4: "DATAVISOR_LOGIN_CHECK_TASK",
	5: "DATAVISOR_COMM_CHECK_TASK",
}
var TASK_TYPE_value = map[string]int32{
	"TEXT_CHECK_MESSAGE_PUSH_TASK": 1,
	"SHUMEI_REGISTER_CHECK_TASK":   2,
	"SHUMEI_AUTH_CHECK_TASK":       3,
	"DATAVISOR_LOGIN_CHECK_TASK":   4,
	"DATAVISOR_COMM_CHECK_TASK":    5,
}

func (x TASK_TYPE) Enum() *TASK_TYPE {
	p := new(TASK_TYPE)
	*p = x
	return p
}
func (x TASK_TYPE) String() string {
	return proto.EnumName(TASK_TYPE_name, int32(x))
}
func (x *TASK_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(TASK_TYPE_value, data, "TASK_TYPE")
	if err != nil {
		return err
	}
	*x = TASK_TYPE(value)
	return nil
}
func (TASK_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_antispam_async_e8fc9dbc435c6444, []int{0}
}

type TextCheckMessagePushTask struct {
	Content              *string  `protobuf:"bytes,1,req,name=content" json:"content,omitempty"`
	Result               *int32   `protobuf:"varint,2,req,name=result" json:"result,omitempty"`
	LableInfo            *string  `protobuf:"bytes,3,req,name=lable_info,json=lableInfo" json:"lable_info,omitempty"`
	Account              *string  `protobuf:"bytes,4,opt,name=account" json:"account,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TextCheckMessagePushTask) Reset()         { *m = TextCheckMessagePushTask{} }
func (m *TextCheckMessagePushTask) String() string { return proto.CompactTextString(m) }
func (*TextCheckMessagePushTask) ProtoMessage()    {}
func (*TextCheckMessagePushTask) Descriptor() ([]byte, []int) {
	return fileDescriptor_antispam_async_e8fc9dbc435c6444, []int{0}
}
func (m *TextCheckMessagePushTask) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TextCheckMessagePushTask.Unmarshal(m, b)
}
func (m *TextCheckMessagePushTask) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TextCheckMessagePushTask.Marshal(b, m, deterministic)
}
func (dst *TextCheckMessagePushTask) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TextCheckMessagePushTask.Merge(dst, src)
}
func (m *TextCheckMessagePushTask) XXX_Size() int {
	return xxx_messageInfo_TextCheckMessagePushTask.Size(m)
}
func (m *TextCheckMessagePushTask) XXX_DiscardUnknown() {
	xxx_messageInfo_TextCheckMessagePushTask.DiscardUnknown(m)
}

var xxx_messageInfo_TextCheckMessagePushTask proto.InternalMessageInfo

func (m *TextCheckMessagePushTask) GetContent() string {
	if m != nil && m.Content != nil {
		return *m.Content
	}
	return ""
}

func (m *TextCheckMessagePushTask) GetResult() int32 {
	if m != nil && m.Result != nil {
		return *m.Result
	}
	return 0
}

func (m *TextCheckMessagePushTask) GetLableInfo() string {
	if m != nil && m.LableInfo != nil {
		return *m.LableInfo
	}
	return ""
}

func (m *TextCheckMessagePushTask) GetAccount() string {
	if m != nil && m.Account != nil {
		return *m.Account
	}
	return ""
}

type ShumeiRegisterCheckTask struct {
	ShumeiRegisterCheckReq []byte   `protobuf:"bytes,1,req,name=shumei_register_check_req,json=shumeiRegisterCheckReq" json:"shumei_register_check_req,omitempty"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *ShumeiRegisterCheckTask) Reset()         { *m = ShumeiRegisterCheckTask{} }
func (m *ShumeiRegisterCheckTask) String() string { return proto.CompactTextString(m) }
func (*ShumeiRegisterCheckTask) ProtoMessage()    {}
func (*ShumeiRegisterCheckTask) Descriptor() ([]byte, []int) {
	return fileDescriptor_antispam_async_e8fc9dbc435c6444, []int{1}
}
func (m *ShumeiRegisterCheckTask) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShumeiRegisterCheckTask.Unmarshal(m, b)
}
func (m *ShumeiRegisterCheckTask) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShumeiRegisterCheckTask.Marshal(b, m, deterministic)
}
func (dst *ShumeiRegisterCheckTask) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShumeiRegisterCheckTask.Merge(dst, src)
}
func (m *ShumeiRegisterCheckTask) XXX_Size() int {
	return xxx_messageInfo_ShumeiRegisterCheckTask.Size(m)
}
func (m *ShumeiRegisterCheckTask) XXX_DiscardUnknown() {
	xxx_messageInfo_ShumeiRegisterCheckTask.DiscardUnknown(m)
}

var xxx_messageInfo_ShumeiRegisterCheckTask proto.InternalMessageInfo

func (m *ShumeiRegisterCheckTask) GetShumeiRegisterCheckReq() []byte {
	if m != nil {
		return m.ShumeiRegisterCheckReq
	}
	return nil
}

type ShumeiAuthCheckTask struct {
	ShumeiAuthCheckReq   []byte   `protobuf:"bytes,1,req,name=shumei_auth_check_req,json=shumeiAuthCheckReq" json:"shumei_auth_check_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShumeiAuthCheckTask) Reset()         { *m = ShumeiAuthCheckTask{} }
func (m *ShumeiAuthCheckTask) String() string { return proto.CompactTextString(m) }
func (*ShumeiAuthCheckTask) ProtoMessage()    {}
func (*ShumeiAuthCheckTask) Descriptor() ([]byte, []int) {
	return fileDescriptor_antispam_async_e8fc9dbc435c6444, []int{2}
}
func (m *ShumeiAuthCheckTask) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShumeiAuthCheckTask.Unmarshal(m, b)
}
func (m *ShumeiAuthCheckTask) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShumeiAuthCheckTask.Marshal(b, m, deterministic)
}
func (dst *ShumeiAuthCheckTask) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShumeiAuthCheckTask.Merge(dst, src)
}
func (m *ShumeiAuthCheckTask) XXX_Size() int {
	return xxx_messageInfo_ShumeiAuthCheckTask.Size(m)
}
func (m *ShumeiAuthCheckTask) XXX_DiscardUnknown() {
	xxx_messageInfo_ShumeiAuthCheckTask.DiscardUnknown(m)
}

var xxx_messageInfo_ShumeiAuthCheckTask proto.InternalMessageInfo

func (m *ShumeiAuthCheckTask) GetShumeiAuthCheckReq() []byte {
	if m != nil {
		return m.ShumeiAuthCheckReq
	}
	return nil
}

type DataVisorLoginCheckTask struct {
	DatavisorLoginCheckReq []byte   `protobuf:"bytes,1,req,name=datavisor_login_check_req,json=datavisorLoginCheckReq" json:"datavisor_login_check_req,omitempty"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *DataVisorLoginCheckTask) Reset()         { *m = DataVisorLoginCheckTask{} }
func (m *DataVisorLoginCheckTask) String() string { return proto.CompactTextString(m) }
func (*DataVisorLoginCheckTask) ProtoMessage()    {}
func (*DataVisorLoginCheckTask) Descriptor() ([]byte, []int) {
	return fileDescriptor_antispam_async_e8fc9dbc435c6444, []int{3}
}
func (m *DataVisorLoginCheckTask) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DataVisorLoginCheckTask.Unmarshal(m, b)
}
func (m *DataVisorLoginCheckTask) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DataVisorLoginCheckTask.Marshal(b, m, deterministic)
}
func (dst *DataVisorLoginCheckTask) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DataVisorLoginCheckTask.Merge(dst, src)
}
func (m *DataVisorLoginCheckTask) XXX_Size() int {
	return xxx_messageInfo_DataVisorLoginCheckTask.Size(m)
}
func (m *DataVisorLoginCheckTask) XXX_DiscardUnknown() {
	xxx_messageInfo_DataVisorLoginCheckTask.DiscardUnknown(m)
}

var xxx_messageInfo_DataVisorLoginCheckTask proto.InternalMessageInfo

func (m *DataVisorLoginCheckTask) GetDatavisorLoginCheckReq() []byte {
	if m != nil {
		return m.DatavisorLoginCheckReq
	}
	return nil
}

type DataVisorCommCheckTask struct {
	DatavisorCommCheckReq []byte   `protobuf:"bytes,1,req,name=datavisor_comm_check_req,json=datavisorCommCheckReq" json:"datavisor_comm_check_req,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *DataVisorCommCheckTask) Reset()         { *m = DataVisorCommCheckTask{} }
func (m *DataVisorCommCheckTask) String() string { return proto.CompactTextString(m) }
func (*DataVisorCommCheckTask) ProtoMessage()    {}
func (*DataVisorCommCheckTask) Descriptor() ([]byte, []int) {
	return fileDescriptor_antispam_async_e8fc9dbc435c6444, []int{4}
}
func (m *DataVisorCommCheckTask) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DataVisorCommCheckTask.Unmarshal(m, b)
}
func (m *DataVisorCommCheckTask) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DataVisorCommCheckTask.Marshal(b, m, deterministic)
}
func (dst *DataVisorCommCheckTask) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DataVisorCommCheckTask.Merge(dst, src)
}
func (m *DataVisorCommCheckTask) XXX_Size() int {
	return xxx_messageInfo_DataVisorCommCheckTask.Size(m)
}
func (m *DataVisorCommCheckTask) XXX_DiscardUnknown() {
	xxx_messageInfo_DataVisorCommCheckTask.DiscardUnknown(m)
}

var xxx_messageInfo_DataVisorCommCheckTask proto.InternalMessageInfo

func (m *DataVisorCommCheckTask) GetDatavisorCommCheckReq() []byte {
	if m != nil {
		return m.DatavisorCommCheckReq
	}
	return nil
}

func init() {
	proto.RegisterType((*TextCheckMessagePushTask)(nil), "antispam.async.TextCheckMessagePushTask")
	proto.RegisterType((*ShumeiRegisterCheckTask)(nil), "antispam.async.ShumeiRegisterCheckTask")
	proto.RegisterType((*ShumeiAuthCheckTask)(nil), "antispam.async.ShumeiAuthCheckTask")
	proto.RegisterType((*DataVisorLoginCheckTask)(nil), "antispam.async.DataVisorLoginCheckTask")
	proto.RegisterType((*DataVisorCommCheckTask)(nil), "antispam.async.DataVisorCommCheckTask")
	proto.RegisterEnum("antispam.async.TASK_TYPE", TASK_TYPE_name, TASK_TYPE_value)
}

func init() {
	proto.RegisterFile("src/antispam/asyncjob/antispam_async.proto", fileDescriptor_antispam_async_e8fc9dbc435c6444)
}

var fileDescriptor_antispam_async_e8fc9dbc435c6444 = []byte{
	// 405 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x64, 0x8e, 0x41, 0x6f, 0xd3, 0x30,
	0x18, 0x86, 0x95, 0x6e, 0x03, 0xf5, 0x13, 0x42, 0xc8, 0x68, 0x5d, 0x36, 0x31, 0x54, 0xf5, 0x34,
	0xed, 0xb0, 0x89, 0x13, 0xe2, 0x18, 0x65, 0x51, 0x13, 0x6d, 0xa1, 0xc5, 0x76, 0x27, 0x38, 0x59,
	0x9e, 0xf1, 0x9a, 0xb0, 0xc6, 0x5e, 0x63, 0x07, 0xc1, 0x0f, 0xe0, 0xbf, 0xf0, 0x33, 0x91, 0x4d,
	0x96, 0x74, 0xca, 0xf1, 0x7b, 0x9f, 0xef, 0x7d, 0xf4, 0xc2, 0xb9, 0xa9, 0xc5, 0x25, 0x57, 0xb6,
	0x34, 0x8f, 0xbc, 0xba, 0xe4, 0xe6, 0xb7, 0x12, 0x3f, 0xf4, 0x5d, 0x97, 0x30, 0x9f, 0x5c, 0x3c,
	0xd6, 0xda, 0x6a, 0xf4, 0xfa, 0x29, 0xbd, 0xf0, 0xe9, 0xec, 0x4f, 0x00, 0x21, 0x95, 0xbf, 0x6c,
	0x5c, 0x48, 0xf1, 0x90, 0x4b, 0x63, 0xf8, 0x5a, 0x2e, 0x1b, 0x53, 0x50, 0x6e, 0x1e, 0x50, 0x08,
	0x2f, 0x85, 0x56, 0x56, 0x2a, 0x1b, 0x06, 0xd3, 0xd1, 0xd9, 0x18, 0x3f, 0x9d, 0x68, 0x02, 0x2f,
	0x6a, 0x69, 0x9a, 0x8d, 0x0d, 0x47, 0xd3, 0xd1, 0xd9, 0x01, 0x6e, 0x2f, 0x74, 0x0a, 0xb0, 0xe1,
	0x77, 0x1b, 0xc9, 0x4a, 0x75, 0xaf, 0xc3, 0x3d, 0x5f, 0x1a, 0xfb, 0x24, 0x53, 0xf7, 0xda, 0x09,
	0xb9, 0x10, 0xba, 0x51, 0x36, 0xdc, 0x9f, 0x06, 0x4e, 0xd8, 0x9e, 0x33, 0x0a, 0x47, 0xa4, 0x68,
	0x2a, 0x59, 0x62, 0xb9, 0x2e, 0x8d, 0x95, 0xb5, 0x1f, 0xe4, 0x57, 0x7c, 0x82, 0x63, 0xe3, 0x11,
	0xab, 0x5b, 0xc6, 0x84, 0x83, 0xac, 0x96, 0x5b, 0xbf, 0xeb, 0x15, 0x9e, 0x98, 0x61, 0x17, 0xcb,
	0xed, 0x2c, 0x85, 0xb7, 0xff, 0xad, 0x51, 0x63, 0x8b, 0xde, 0xf8, 0x01, 0x0e, 0x5b, 0x23, 0x6f,
	0x6c, 0x31, 0xb0, 0x21, 0xf3, 0xbc, 0xe3, 0x4c, 0x14, 0x8e, 0xae, 0xb8, 0xe5, 0xb7, 0xa5, 0xd1,
	0xf5, 0x8d, 0x5e, 0x97, 0xea, 0xd9, 0xbe, 0xef, 0xdc, 0xf2, 0x9f, 0x0e, 0xb1, 0x8d, 0x63, 0xc3,
	0x7d, 0xdd, 0x43, 0xdf, 0x75, 0xd6, 0x2f, 0x30, 0xe9, 0xac, 0xb1, 0xae, 0xaa, 0x5e, 0xfa, 0x11,
	0xc2, 0x5e, 0x2a, 0x74, 0x55, 0x0d, 0x9c, 0x87, 0x1d, 0xef, 0x9a, 0x58, 0x6e, 0xcf, 0xff, 0x06,
	0x30, 0xa6, 0x11, 0xb9, 0x66, 0xf4, 0xdb, 0x32, 0x41, 0x53, 0x78, 0x47, 0x93, 0xaf, 0x94, 0xc5,
	0x69, 0x12, 0x5f, 0xb3, 0x3c, 0x21, 0x24, 0x9a, 0x27, 0x6c, 0xb9, 0x22, 0x29, 0x73, 0x4f, 0x6f,
	0x02, 0xf4, 0x1e, 0x4e, 0x48, 0xba, 0xca, 0x93, 0x8c, 0xe1, 0x64, 0x9e, 0x11, 0x9a, 0xe0, 0xf6,
	0xd9, 0xf3, 0x11, 0x3a, 0x81, 0x49, 0xcb, 0xa3, 0x15, 0x4d, 0x77, 0xd9, 0x9e, 0xeb, 0x5e, 0x45,
	0x34, 0xba, 0xcd, 0xc8, 0x02, 0xb3, 0x9b, 0xc5, 0x3c, 0xfb, 0xbc, 0xcb, 0xf7, 0xd1, 0x29, 0x1c,
	0xf7, 0x3c, 0x5e, 0xe4, 0xf9, 0x2e, 0x3e, 0xf8, 0x17, 0x00, 0x00, 0xff, 0xff, 0x31, 0xf7, 0xf8,
	0x84, 0xb8, 0x02, 0x00, 0x00,
}
