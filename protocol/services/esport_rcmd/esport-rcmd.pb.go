// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/esport-rcmd/esport-rcmd.proto

package esport_rcmd // import "golang.52tt.com/protocol/services/esport_rcmd"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import esport_hall "golang.52tt.com/protocol/services/esport_hall"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type SceneType int32

const (
	SceneType_SCENE_TYPE_UNKNOWN                SceneType = 0
	SceneType_SCENE_TYPE_ESPORT_AREA            SceneType = 1
	SceneType_SCENE_TYPE_HOME_PAGE_COACH_LIST   SceneType = 2
	SceneType_SCENE_TYPE_TOP_OVERALL_FLOATING   SceneType = 3
	SceneType_SCENE_TYPE_ESPORT_ACTIVITY_PAGE   SceneType = 4
	SceneType_SCENE_TYPE_UGC_CHANNEL            SceneType = 5
	SceneType_SCENE_TYPE_UGC_TOGETHER_ROOM_LIST SceneType = 6
	SceneType_SCENE_TYPE_ESPORT_TAB             SceneType = 7
	SceneType_SCENE_TYPE_ESPORT_VOICE_GOD_PAGE  SceneType = 8
	SceneType_SCENE_TYPE_ESPORT_SKILL_GOD_PAGE  SceneType = 9
	SceneType_SCENE_TYPE_ESPORT_NEW_GOD_PAGE    SceneType = 10
	SceneType_SCENE_TYPE_ESPORT_BACK_RECALL     SceneType = 11
	SceneType_SCENE_TYPE_ONE_KEY_FIND_COACH     SceneType = 12
)

var SceneType_name = map[int32]string{
	0:  "SCENE_TYPE_UNKNOWN",
	1:  "SCENE_TYPE_ESPORT_AREA",
	2:  "SCENE_TYPE_HOME_PAGE_COACH_LIST",
	3:  "SCENE_TYPE_TOP_OVERALL_FLOATING",
	4:  "SCENE_TYPE_ESPORT_ACTIVITY_PAGE",
	5:  "SCENE_TYPE_UGC_CHANNEL",
	6:  "SCENE_TYPE_UGC_TOGETHER_ROOM_LIST",
	7:  "SCENE_TYPE_ESPORT_TAB",
	8:  "SCENE_TYPE_ESPORT_VOICE_GOD_PAGE",
	9:  "SCENE_TYPE_ESPORT_SKILL_GOD_PAGE",
	10: "SCENE_TYPE_ESPORT_NEW_GOD_PAGE",
	11: "SCENE_TYPE_ESPORT_BACK_RECALL",
	12: "SCENE_TYPE_ONE_KEY_FIND_COACH",
}
var SceneType_value = map[string]int32{
	"SCENE_TYPE_UNKNOWN":                0,
	"SCENE_TYPE_ESPORT_AREA":            1,
	"SCENE_TYPE_HOME_PAGE_COACH_LIST":   2,
	"SCENE_TYPE_TOP_OVERALL_FLOATING":   3,
	"SCENE_TYPE_ESPORT_ACTIVITY_PAGE":   4,
	"SCENE_TYPE_UGC_CHANNEL":            5,
	"SCENE_TYPE_UGC_TOGETHER_ROOM_LIST": 6,
	"SCENE_TYPE_ESPORT_TAB":             7,
	"SCENE_TYPE_ESPORT_VOICE_GOD_PAGE":  8,
	"SCENE_TYPE_ESPORT_SKILL_GOD_PAGE":  9,
	"SCENE_TYPE_ESPORT_NEW_GOD_PAGE":    10,
	"SCENE_TYPE_ESPORT_BACK_RECALL":     11,
	"SCENE_TYPE_ONE_KEY_FIND_COACH":     12,
}

func (x SceneType) String() string {
	return proto.EnumName(SceneType_name, int32(x))
}
func (SceneType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{0}
}

type MultilevelSortType int32

const (
	MultilevelSortType_MULTILEVEL_SORT_TYPE_UNKNOWN               MultilevelSortType = 0
	MultilevelSortType_MULTILEVEL_SORT_TYPE_SOCRE                 MultilevelSortType = 1
	MultilevelSortType_MULTILEVEL_SORT_TYPE_QUICK_RECEIVE         MultilevelSortType = 2
	MultilevelSortType_MULTILEVEL_SORT_TYPE_FIRST_ROUND           MultilevelSortType = 3
	MultilevelSortType_MULTILEVEL_SORT_TYPE_DIFF_SEX              MultilevelSortType = 4
	MultilevelSortType_MULTILEVEL_SORT_TYPE_ONLINE_STATUS         MultilevelSortType = 5
	MultilevelSortType_MULTILEVEL_SORT_TYPE_CONVERSION_RATE       MultilevelSortType = 6
	MultilevelSortType_MULTILEVEL_SORT_TYPE_IS_NEW_CUSTOMER_PRICE MultilevelSortType = 7
	MultilevelSortType_MULTILEVEL_SORT_TYPE_ON_MIC                MultilevelSortType = 8
	MultilevelSortType_MULTILEVEL_SORT_TYPE_IN_CHANNEL            MultilevelSortType = 9
)

var MultilevelSortType_name = map[int32]string{
	0: "MULTILEVEL_SORT_TYPE_UNKNOWN",
	1: "MULTILEVEL_SORT_TYPE_SOCRE",
	2: "MULTILEVEL_SORT_TYPE_QUICK_RECEIVE",
	3: "MULTILEVEL_SORT_TYPE_FIRST_ROUND",
	4: "MULTILEVEL_SORT_TYPE_DIFF_SEX",
	5: "MULTILEVEL_SORT_TYPE_ONLINE_STATUS",
	6: "MULTILEVEL_SORT_TYPE_CONVERSION_RATE",
	7: "MULTILEVEL_SORT_TYPE_IS_NEW_CUSTOMER_PRICE",
	8: "MULTILEVEL_SORT_TYPE_ON_MIC",
	9: "MULTILEVEL_SORT_TYPE_IN_CHANNEL",
}
var MultilevelSortType_value = map[string]int32{
	"MULTILEVEL_SORT_TYPE_UNKNOWN":               0,
	"MULTILEVEL_SORT_TYPE_SOCRE":                 1,
	"MULTILEVEL_SORT_TYPE_QUICK_RECEIVE":         2,
	"MULTILEVEL_SORT_TYPE_FIRST_ROUND":           3,
	"MULTILEVEL_SORT_TYPE_DIFF_SEX":              4,
	"MULTILEVEL_SORT_TYPE_ONLINE_STATUS":         5,
	"MULTILEVEL_SORT_TYPE_CONVERSION_RATE":       6,
	"MULTILEVEL_SORT_TYPE_IS_NEW_CUSTOMER_PRICE": 7,
	"MULTILEVEL_SORT_TYPE_ON_MIC":                8,
	"MULTILEVEL_SORT_TYPE_IN_CHANNEL":            9,
}

func (x MultilevelSortType) String() string {
	return proto.EnumName(MultilevelSortType_name, int32(x))
}
func (MultilevelSortType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{1}
}

type FilterType int32

const (
	FilterType_FILTER_TYPE_UNKNOWN        FilterType = 0
	FilterType_FILTER_TYPE_RECENT_EXPOSED FilterType = 1
	FilterType_FILTER_TYPE_RECENT_FOLLOW  FilterType = 2
	FilterType_FILTER_TYPE_RECENT_CHAT    FilterType = 3
	FilterType_FILTER_TYPE_RECENT_ORDER   FilterType = 4
	FilterType_FILTER_TYPE_USER_GROUP     FilterType = 5
)

var FilterType_name = map[int32]string{
	0: "FILTER_TYPE_UNKNOWN",
	1: "FILTER_TYPE_RECENT_EXPOSED",
	2: "FILTER_TYPE_RECENT_FOLLOW",
	3: "FILTER_TYPE_RECENT_CHAT",
	4: "FILTER_TYPE_RECENT_ORDER",
	5: "FILTER_TYPE_USER_GROUP",
}
var FilterType_value = map[string]int32{
	"FILTER_TYPE_UNKNOWN":        0,
	"FILTER_TYPE_RECENT_EXPOSED": 1,
	"FILTER_TYPE_RECENT_FOLLOW":  2,
	"FILTER_TYPE_RECENT_CHAT":    3,
	"FILTER_TYPE_RECENT_ORDER":   4,
	"FILTER_TYPE_USER_GROUP":     5,
}

func (x FilterType) String() string {
	return proto.EnumName(FilterType_name, int32(x))
}
func (FilterType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{2}
}

type RcmdTag int32

const (
	RcmdTag_RCMD_TAG_UNKNOWN       RcmdTag = 0
	RcmdTag_RCMD_TAG_QUICK_RECEIVE RcmdTag = 1
)

var RcmdTag_name = map[int32]string{
	0: "RCMD_TAG_UNKNOWN",
	1: "RCMD_TAG_QUICK_RECEIVE",
}
var RcmdTag_value = map[string]int32{
	"RCMD_TAG_UNKNOWN":       0,
	"RCMD_TAG_QUICK_RECEIVE": 1,
}

func (x RcmdTag) String() string {
	return proto.EnumName(RcmdTag_name, int32(x))
}
func (RcmdTag) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{3}
}

type RcmdTagType int32

const (
	RcmdTagType_RCMD_TAG_TYPE_UNKNOWN RcmdTagType = 0
	RcmdTagType_RCMD_TAG_TYPE_NUMBER  RcmdTagType = 1
	RcmdTagType_RCMD_TAG_TYPE_TEXT    RcmdTagType = 2
)

var RcmdTagType_name = map[int32]string{
	0: "RCMD_TAG_TYPE_UNKNOWN",
	1: "RCMD_TAG_TYPE_NUMBER",
	2: "RCMD_TAG_TYPE_TEXT",
}
var RcmdTagType_value = map[string]int32{
	"RCMD_TAG_TYPE_UNKNOWN": 0,
	"RCMD_TAG_TYPE_NUMBER":  1,
	"RCMD_TAG_TYPE_TEXT":    2,
}

func (x RcmdTagType) String() string {
	return proto.EnumName(RcmdTagType_name, int32(x))
}
func (RcmdTagType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{4}
}

type RcmdTagCompareType int32

const (
	RcmdTagCompareType_RCMD_TAG_COMPARE_TYPE_UNKNOWN RcmdTagCompareType = 0
	RcmdTagCompareType_RCMD_TAG_COMPARE_TYPE_GREATER RcmdTagCompareType = 1
	RcmdTagCompareType_RCMD_TAG_COMPARE_TYPE_LESS    RcmdTagCompareType = 2
	RcmdTagCompareType_RCMD_TAG_COMPARE_TYPE_EQUAL   RcmdTagCompareType = 3
)

var RcmdTagCompareType_name = map[int32]string{
	0: "RCMD_TAG_COMPARE_TYPE_UNKNOWN",
	1: "RCMD_TAG_COMPARE_TYPE_GREATER",
	2: "RCMD_TAG_COMPARE_TYPE_LESS",
	3: "RCMD_TAG_COMPARE_TYPE_EQUAL",
}
var RcmdTagCompareType_value = map[string]int32{
	"RCMD_TAG_COMPARE_TYPE_UNKNOWN": 0,
	"RCMD_TAG_COMPARE_TYPE_GREATER": 1,
	"RCMD_TAG_COMPARE_TYPE_LESS":    2,
	"RCMD_TAG_COMPARE_TYPE_EQUAL":   3,
}

func (x RcmdTagCompareType) String() string {
	return proto.EnumName(RcmdTagCompareType_name, int32(x))
}
func (RcmdTagCompareType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{5}
}

// 策略状态
type StrategyStatus int32

const (
	StrategyStatus_STRATEGY_STATUS_UNSPECIFIED StrategyStatus = 0
	StrategyStatus_STRATEGY_STATUS_RUNNING     StrategyStatus = 1
	StrategyStatus_STRATEGY_STATUS_PAUSED      StrategyStatus = 2
	StrategyStatus_STRATEGY_STATUS_DRAFT       StrategyStatus = 3
)

var StrategyStatus_name = map[int32]string{
	0: "STRATEGY_STATUS_UNSPECIFIED",
	1: "STRATEGY_STATUS_RUNNING",
	2: "STRATEGY_STATUS_PAUSED",
	3: "STRATEGY_STATUS_DRAFT",
}
var StrategyStatus_value = map[string]int32{
	"STRATEGY_STATUS_UNSPECIFIED": 0,
	"STRATEGY_STATUS_RUNNING":     1,
	"STRATEGY_STATUS_PAUSED":      2,
	"STRATEGY_STATUS_DRAFT":       3,
}

func (x StrategyStatus) String() string {
	return proto.EnumName(StrategyStatus_name, int32(x))
}
func (StrategyStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{6}
}

type DelimitType int32

const (
	DelimitType_DELIMIT_TYPE_UNSPECIFIED DelimitType = 0
	DelimitType_DELIMIT_TYPE_AB_TEST     DelimitType = 1
	DelimitType_DELIMIT_TYPE_USER_GROUP  DelimitType = 2
)

var DelimitType_name = map[int32]string{
	0: "DELIMIT_TYPE_UNSPECIFIED",
	1: "DELIMIT_TYPE_AB_TEST",
	2: "DELIMIT_TYPE_USER_GROUP",
}
var DelimitType_value = map[string]int32{
	"DELIMIT_TYPE_UNSPECIFIED": 0,
	"DELIMIT_TYPE_AB_TEST":     1,
	"DELIMIT_TYPE_USER_GROUP":  2,
}

func (x DelimitType) String() string {
	return proto.EnumName(DelimitType_name, int32(x))
}
func (DelimitType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{7}
}

type RecallSourceRuleType int32

const (
	RecallSourceRuleType_RECALL_SOURCE_RULE_TYPE_UNKNOWN            RecallSourceRuleType = 0
	RecallSourceRuleType_RECALL_SOURCE_RULE_TYPE_USER_GROUP         RecallSourceRuleType = 1
	RecallSourceRuleType_RECALL_SOURCE_RULE_TYPE_QUICK_RECEIVE      RecallSourceRuleType = 3
	RecallSourceRuleType_RECALL_SOURCE_RULE_TYPE_REG_LESS_7_DAYS    RecallSourceRuleType = 4
	RecallSourceRuleType_RECALL_SOURCE_RULE_TYPE_ONLINE_STATUS      RecallSourceRuleType = 5
	RecallSourceRuleType_RECALL_SOURCE_RULE_TYPE_FIRST_ROUND_USABLE RecallSourceRuleType = 6
	RecallSourceRuleType_RECALL_SOURCE_RULE_TYPE_COUPON_USABLE      RecallSourceRuleType = 7
	RecallSourceRuleType_RECALL_SOURCE_RULE_TYPE_COACH_PRICE        RecallSourceRuleType = 8
	RecallSourceRuleType_RECALL_SOURCE_RULE_TYPE_OPEN_SKILL         RecallSourceRuleType = 9
	RecallSourceRuleType_RECALL_SOURCE_RULE_TYPE_NEW_CUSTOMER_PRICE RecallSourceRuleType = 10
	RecallSourceRuleType_RECALL_SOURCE_RULE_TYPE_GUARANTEE_WIN      RecallSourceRuleType = 11
	RecallSourceRuleType_RECALL_SOURCE_RULE_TYPE_SKILL_LABEL        RecallSourceRuleType = 12
	RecallSourceRuleType_RECALL_SOURCE_RULE_TYPE_SPECIAL_LABEL      RecallSourceRuleType = 13
	RecallSourceRuleType_RECALL_SOURCE_RULE_TYPE_VOICE_LABEL        RecallSourceRuleType = 14
	RecallSourceRuleType_RECALL_SOURCE_RULE_TYPE_COACH_LABEL        RecallSourceRuleType = 15
	RecallSourceRuleType_RECALL_SOURCE_RULE_TYPE_PERSONAL_COACH     RecallSourceRuleType = 16
	RecallSourceRuleType_RECALL_SOURCE_RULE_TYPE_GUILD_COACH        RecallSourceRuleType = 17
	RecallSourceRuleType_RECALL_SOURCE_RULE_TYPE_ON_MIC             RecallSourceRuleType = 18
	RecallSourceRuleType_RECALL_SOURCE_RULE_TYPE_IN_CHANNEL         RecallSourceRuleType = 19
)

var RecallSourceRuleType_name = map[int32]string{
	0:  "RECALL_SOURCE_RULE_TYPE_UNKNOWN",
	1:  "RECALL_SOURCE_RULE_TYPE_USER_GROUP",
	3:  "RECALL_SOURCE_RULE_TYPE_QUICK_RECEIVE",
	4:  "RECALL_SOURCE_RULE_TYPE_REG_LESS_7_DAYS",
	5:  "RECALL_SOURCE_RULE_TYPE_ONLINE_STATUS",
	6:  "RECALL_SOURCE_RULE_TYPE_FIRST_ROUND_USABLE",
	7:  "RECALL_SOURCE_RULE_TYPE_COUPON_USABLE",
	8:  "RECALL_SOURCE_RULE_TYPE_COACH_PRICE",
	9:  "RECALL_SOURCE_RULE_TYPE_OPEN_SKILL",
	10: "RECALL_SOURCE_RULE_TYPE_NEW_CUSTOMER_PRICE",
	11: "RECALL_SOURCE_RULE_TYPE_GUARANTEE_WIN",
	12: "RECALL_SOURCE_RULE_TYPE_SKILL_LABEL",
	13: "RECALL_SOURCE_RULE_TYPE_SPECIAL_LABEL",
	14: "RECALL_SOURCE_RULE_TYPE_VOICE_LABEL",
	15: "RECALL_SOURCE_RULE_TYPE_COACH_LABEL",
	16: "RECALL_SOURCE_RULE_TYPE_PERSONAL_COACH",
	17: "RECALL_SOURCE_RULE_TYPE_GUILD_COACH",
	18: "RECALL_SOURCE_RULE_TYPE_ON_MIC",
	19: "RECALL_SOURCE_RULE_TYPE_IN_CHANNEL",
}
var RecallSourceRuleType_value = map[string]int32{
	"RECALL_SOURCE_RULE_TYPE_UNKNOWN":            0,
	"RECALL_SOURCE_RULE_TYPE_USER_GROUP":         1,
	"RECALL_SOURCE_RULE_TYPE_QUICK_RECEIVE":      3,
	"RECALL_SOURCE_RULE_TYPE_REG_LESS_7_DAYS":    4,
	"RECALL_SOURCE_RULE_TYPE_ONLINE_STATUS":      5,
	"RECALL_SOURCE_RULE_TYPE_FIRST_ROUND_USABLE": 6,
	"RECALL_SOURCE_RULE_TYPE_COUPON_USABLE":      7,
	"RECALL_SOURCE_RULE_TYPE_COACH_PRICE":        8,
	"RECALL_SOURCE_RULE_TYPE_OPEN_SKILL":         9,
	"RECALL_SOURCE_RULE_TYPE_NEW_CUSTOMER_PRICE": 10,
	"RECALL_SOURCE_RULE_TYPE_GUARANTEE_WIN":      11,
	"RECALL_SOURCE_RULE_TYPE_SKILL_LABEL":        12,
	"RECALL_SOURCE_RULE_TYPE_SPECIAL_LABEL":      13,
	"RECALL_SOURCE_RULE_TYPE_VOICE_LABEL":        14,
	"RECALL_SOURCE_RULE_TYPE_COACH_LABEL":        15,
	"RECALL_SOURCE_RULE_TYPE_PERSONAL_COACH":     16,
	"RECALL_SOURCE_RULE_TYPE_GUILD_COACH":        17,
	"RECALL_SOURCE_RULE_TYPE_ON_MIC":             18,
	"RECALL_SOURCE_RULE_TYPE_IN_CHANNEL":         19,
}

func (x RecallSourceRuleType) String() string {
	return proto.EnumName(RecallSourceRuleType_name, int32(x))
}
func (RecallSourceRuleType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{8}
}

type RecallSourceRuleOperatorType int32

const (
	RecallSourceRuleOperatorType_RECALL_SOURCE_RULE_OPERATOR_TYPE_UNKNOWN     RecallSourceRuleOperatorType = 0
	RecallSourceRuleOperatorType_RECALL_SOURCE_RULE_OPERATOR_TYPE_EQ          RecallSourceRuleOperatorType = 1
	RecallSourceRuleOperatorType_RECALL_SOURCE_RULE_OPERATOR_TYPE_GT          RecallSourceRuleOperatorType = 2
	RecallSourceRuleOperatorType_RECALL_SOURCE_RULE_OPERATOR_TYPE_GTE         RecallSourceRuleOperatorType = 3
	RecallSourceRuleOperatorType_RECALL_SOURCE_RULE_OPERATOR_TYPE_LT          RecallSourceRuleOperatorType = 4
	RecallSourceRuleOperatorType_RECALL_SOURCE_RULE_OPERATOR_TYPE_LTE         RecallSourceRuleOperatorType = 5
	RecallSourceRuleOperatorType_RECALL_SOURCE_RULE_OPERATOR_TYPE_CONTAIN     RecallSourceRuleOperatorType = 6
	RecallSourceRuleOperatorType_RECALL_SOURCE_RULE_OPERATOR_TYPE_NOT_CONTAIN RecallSourceRuleOperatorType = 7
)

var RecallSourceRuleOperatorType_name = map[int32]string{
	0: "RECALL_SOURCE_RULE_OPERATOR_TYPE_UNKNOWN",
	1: "RECALL_SOURCE_RULE_OPERATOR_TYPE_EQ",
	2: "RECALL_SOURCE_RULE_OPERATOR_TYPE_GT",
	3: "RECALL_SOURCE_RULE_OPERATOR_TYPE_GTE",
	4: "RECALL_SOURCE_RULE_OPERATOR_TYPE_LT",
	5: "RECALL_SOURCE_RULE_OPERATOR_TYPE_LTE",
	6: "RECALL_SOURCE_RULE_OPERATOR_TYPE_CONTAIN",
	7: "RECALL_SOURCE_RULE_OPERATOR_TYPE_NOT_CONTAIN",
}
var RecallSourceRuleOperatorType_value = map[string]int32{
	"RECALL_SOURCE_RULE_OPERATOR_TYPE_UNKNOWN":     0,
	"RECALL_SOURCE_RULE_OPERATOR_TYPE_EQ":          1,
	"RECALL_SOURCE_RULE_OPERATOR_TYPE_GT":          2,
	"RECALL_SOURCE_RULE_OPERATOR_TYPE_GTE":         3,
	"RECALL_SOURCE_RULE_OPERATOR_TYPE_LT":          4,
	"RECALL_SOURCE_RULE_OPERATOR_TYPE_LTE":         5,
	"RECALL_SOURCE_RULE_OPERATOR_TYPE_CONTAIN":     6,
	"RECALL_SOURCE_RULE_OPERATOR_TYPE_NOT_CONTAIN": 7,
}

func (x RecallSourceRuleOperatorType) String() string {
	return proto.EnumName(RecallSourceRuleOperatorType_name, int32(x))
}
func (RecallSourceRuleOperatorType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{9}
}

type SortParameterType int32

const (
	SortParameterType_SORT_PARAMETER_TYPE_UNKNOWN        SortParameterType = 0
	SortParameterType_SORT_PARAMETER_TYPE_GOD_LEVEL      SortParameterType = 1
	SortParameterType_SORT_PARAMETER_TYPE_IS_FAMOUS      SortParameterType = 2
	SortParameterType_SORT_PARAMETER_TYPE_GUARANTEE_WIN  SortParameterType = 3
	SortParameterType_SORT_PARAMETER_TYPE_ONLINE_STATUS  SortParameterType = 4
	SortParameterType_SORT_PARAMETER_TYPE_USER_SEX       SortParameterType = 5
	SortParameterType_SORT_PARAMETER_TYPE_EXPOSE_LIMIT   SortParameterType = 6
	SortParameterType_SORT_PARAMETER_TYPE_CHANNEL_STATUS SortParameterType = 7
)

var SortParameterType_name = map[int32]string{
	0: "SORT_PARAMETER_TYPE_UNKNOWN",
	1: "SORT_PARAMETER_TYPE_GOD_LEVEL",
	2: "SORT_PARAMETER_TYPE_IS_FAMOUS",
	3: "SORT_PARAMETER_TYPE_GUARANTEE_WIN",
	4: "SORT_PARAMETER_TYPE_ONLINE_STATUS",
	5: "SORT_PARAMETER_TYPE_USER_SEX",
	6: "SORT_PARAMETER_TYPE_EXPOSE_LIMIT",
	7: "SORT_PARAMETER_TYPE_CHANNEL_STATUS",
}
var SortParameterType_value = map[string]int32{
	"SORT_PARAMETER_TYPE_UNKNOWN":        0,
	"SORT_PARAMETER_TYPE_GOD_LEVEL":      1,
	"SORT_PARAMETER_TYPE_IS_FAMOUS":      2,
	"SORT_PARAMETER_TYPE_GUARANTEE_WIN":  3,
	"SORT_PARAMETER_TYPE_ONLINE_STATUS":  4,
	"SORT_PARAMETER_TYPE_USER_SEX":       5,
	"SORT_PARAMETER_TYPE_EXPOSE_LIMIT":   6,
	"SORT_PARAMETER_TYPE_CHANNEL_STATUS": 7,
}

func (x SortParameterType) String() string {
	return proto.EnumName(SortParameterType_name, int32(x))
}
func (SortParameterType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{10}
}

type RecallSource_SortParameterType int32

const (
	RecallSource_SORT_PARAMETER_TYPE_UNKNOWN  RecallSource_SortParameterType = 0
	RecallSource_SORT_PARAMETER_TYPE_CUSTOMER RecallSource_SortParameterType = 1
	RecallSource_SORT_PARAMETER_TYPE_RANDOM   RecallSource_SortParameterType = 2
)

var RecallSource_SortParameterType_name = map[int32]string{
	0: "SORT_PARAMETER_TYPE_UNKNOWN",
	1: "SORT_PARAMETER_TYPE_CUSTOMER",
	2: "SORT_PARAMETER_TYPE_RANDOM",
}
var RecallSource_SortParameterType_value = map[string]int32{
	"SORT_PARAMETER_TYPE_UNKNOWN":  0,
	"SORT_PARAMETER_TYPE_CUSTOMER": 1,
	"SORT_PARAMETER_TYPE_RANDOM":   2,
}

func (x RecallSource_SortParameterType) String() string {
	return proto.EnumName(RecallSource_SortParameterType_name, int32(x))
}
func (RecallSource_SortParameterType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{6, 0}
}

// 获取电竞推荐数据请求
type GetEsportRcmdSkillProductReq struct {
	SceneType SceneType `protobuf:"varint,1,opt,name=scene_type,json=sceneType,proto3,enum=esport_rcmd.SceneType" json:"scene_type,omitempty"`
	SkillId   uint32    `protobuf:"varint,2,opt,name=skill_id,json=skillId,proto3" json:"skill_id,omitempty"`
	Offset    uint32    `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit     uint32    `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	// ======= 各场景特有参数 =======
	GameProperty         []*esport_hall.GameProperty `protobuf:"bytes,5,rep,name=game_property,json=gameProperty,proto3" json:"game_property,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetEsportRcmdSkillProductReq) Reset()         { *m = GetEsportRcmdSkillProductReq{} }
func (m *GetEsportRcmdSkillProductReq) String() string { return proto.CompactTextString(m) }
func (*GetEsportRcmdSkillProductReq) ProtoMessage()    {}
func (*GetEsportRcmdSkillProductReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{0}
}
func (m *GetEsportRcmdSkillProductReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEsportRcmdSkillProductReq.Unmarshal(m, b)
}
func (m *GetEsportRcmdSkillProductReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEsportRcmdSkillProductReq.Marshal(b, m, deterministic)
}
func (dst *GetEsportRcmdSkillProductReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEsportRcmdSkillProductReq.Merge(dst, src)
}
func (m *GetEsportRcmdSkillProductReq) XXX_Size() int {
	return xxx_messageInfo_GetEsportRcmdSkillProductReq.Size(m)
}
func (m *GetEsportRcmdSkillProductReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEsportRcmdSkillProductReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetEsportRcmdSkillProductReq proto.InternalMessageInfo

func (m *GetEsportRcmdSkillProductReq) GetSceneType() SceneType {
	if m != nil {
		return m.SceneType
	}
	return SceneType_SCENE_TYPE_UNKNOWN
}

func (m *GetEsportRcmdSkillProductReq) GetSkillId() uint32 {
	if m != nil {
		return m.SkillId
	}
	return 0
}

func (m *GetEsportRcmdSkillProductReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetEsportRcmdSkillProductReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetEsportRcmdSkillProductReq) GetGameProperty() []*esport_hall.GameProperty {
	if m != nil {
		return m.GameProperty
	}
	return nil
}

type EsportRcmdSkillProduct struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SkillId              uint32   `protobuf:"varint,2,opt,name=skill_id,json=skillId,proto3" json:"skill_id,omitempty"`
	SkillProductId       uint32   `protobuf:"varint,3,opt,name=skill_product_id,json=skillProductId,proto3" json:"skill_product_id,omitempty"`
	StrategyId           uint32   `protobuf:"varint,4,opt,name=strategy_id,json=strategyId,proto3" json:"strategy_id,omitempty"`
	RecallSourceId       uint32   `protobuf:"varint,5,opt,name=recall_source_id,json=recallSourceId,proto3" json:"recall_source_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EsportRcmdSkillProduct) Reset()         { *m = EsportRcmdSkillProduct{} }
func (m *EsportRcmdSkillProduct) String() string { return proto.CompactTextString(m) }
func (*EsportRcmdSkillProduct) ProtoMessage()    {}
func (*EsportRcmdSkillProduct) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{1}
}
func (m *EsportRcmdSkillProduct) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EsportRcmdSkillProduct.Unmarshal(m, b)
}
func (m *EsportRcmdSkillProduct) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EsportRcmdSkillProduct.Marshal(b, m, deterministic)
}
func (dst *EsportRcmdSkillProduct) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EsportRcmdSkillProduct.Merge(dst, src)
}
func (m *EsportRcmdSkillProduct) XXX_Size() int {
	return xxx_messageInfo_EsportRcmdSkillProduct.Size(m)
}
func (m *EsportRcmdSkillProduct) XXX_DiscardUnknown() {
	xxx_messageInfo_EsportRcmdSkillProduct.DiscardUnknown(m)
}

var xxx_messageInfo_EsportRcmdSkillProduct proto.InternalMessageInfo

func (m *EsportRcmdSkillProduct) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *EsportRcmdSkillProduct) GetSkillId() uint32 {
	if m != nil {
		return m.SkillId
	}
	return 0
}

func (m *EsportRcmdSkillProduct) GetSkillProductId() uint32 {
	if m != nil {
		return m.SkillProductId
	}
	return 0
}

func (m *EsportRcmdSkillProduct) GetStrategyId() uint32 {
	if m != nil {
		return m.StrategyId
	}
	return 0
}

func (m *EsportRcmdSkillProduct) GetRecallSourceId() uint32 {
	if m != nil {
		return m.RecallSourceId
	}
	return 0
}

type GetEsportRcmdSkillProductResp struct {
	SkillProductList     []*EsportRcmdSkillProduct `protobuf:"bytes,1,rep,name=skill_product_list,json=skillProductList,proto3" json:"skill_product_list,omitempty"`
	NextOffset           uint32                    `protobuf:"varint,2,opt,name=next_offset,json=nextOffset,proto3" json:"next_offset,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetEsportRcmdSkillProductResp) Reset()         { *m = GetEsportRcmdSkillProductResp{} }
func (m *GetEsportRcmdSkillProductResp) String() string { return proto.CompactTextString(m) }
func (*GetEsportRcmdSkillProductResp) ProtoMessage()    {}
func (*GetEsportRcmdSkillProductResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{2}
}
func (m *GetEsportRcmdSkillProductResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEsportRcmdSkillProductResp.Unmarshal(m, b)
}
func (m *GetEsportRcmdSkillProductResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEsportRcmdSkillProductResp.Marshal(b, m, deterministic)
}
func (dst *GetEsportRcmdSkillProductResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEsportRcmdSkillProductResp.Merge(dst, src)
}
func (m *GetEsportRcmdSkillProductResp) XXX_Size() int {
	return xxx_messageInfo_GetEsportRcmdSkillProductResp.Size(m)
}
func (m *GetEsportRcmdSkillProductResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEsportRcmdSkillProductResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetEsportRcmdSkillProductResp proto.InternalMessageInfo

func (m *GetEsportRcmdSkillProductResp) GetSkillProductList() []*EsportRcmdSkillProduct {
	if m != nil {
		return m.SkillProductList
	}
	return nil
}

func (m *GetEsportRcmdSkillProductResp) GetNextOffset() uint32 {
	if m != nil {
		return m.NextOffset
	}
	return 0
}

// 策略信息
type Strategy struct {
	Id                    uint32                     `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	SceneType             uint32                     `protobuf:"varint,2,opt,name=scene_type,json=sceneType,proto3" json:"scene_type,omitempty"`
	Name                  string                     `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Priority              uint32                     `protobuf:"varint,4,opt,name=priority,proto3" json:"priority,omitempty"`
	Status                StrategyStatus             `protobuf:"varint,5,opt,name=status,proto3,enum=esport_rcmd.StrategyStatus" json:"status,omitempty"`
	LastOperator          string                     `protobuf:"bytes,6,opt,name=last_operator,json=lastOperator,proto3" json:"last_operator,omitempty"`
	LastOperationTime     int64                      `protobuf:"varint,7,opt,name=last_operation_time,json=lastOperationTime,proto3" json:"last_operation_time,omitempty"`
	DelimitType           DelimitType                `protobuf:"varint,8,opt,name=delimit_type,json=delimitType,proto3,enum=esport_rcmd.DelimitType" json:"delimit_type,omitempty"`
	AbTestSetting         *Strategy_AbTestSetting    `protobuf:"bytes,9,opt,name=ab_test_setting,json=abTestSetting,proto3" json:"ab_test_setting,omitempty"`
	UserGroupSetting      *Strategy_UserGroupSetting `protobuf:"bytes,10,opt,name=user_group_setting,json=userGroupSetting,proto3" json:"user_group_setting,omitempty"`
	TestListIds           []int64                    `protobuf:"varint,20,rep,packed,name=test_list_ids,json=testListIds,proto3" json:"test_list_ids,omitempty"`
	RecallSources         []*Strategy_RecallSource   `protobuf:"bytes,21,rep,name=recall_sources,json=recallSources,proto3" json:"recall_sources,omitempty"`
	MinimumRecallQuantity int32                      `protobuf:"varint,22,opt,name=minimum_recall_quantity,json=minimumRecallQuantity,proto3" json:"minimum_recall_quantity,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}                   `json:"-"`
	XXX_unrecognized      []byte                     `json:"-"`
	XXX_sizecache         int32                      `json:"-"`
}

func (m *Strategy) Reset()         { *m = Strategy{} }
func (m *Strategy) String() string { return proto.CompactTextString(m) }
func (*Strategy) ProtoMessage()    {}
func (*Strategy) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{3}
}
func (m *Strategy) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Strategy.Unmarshal(m, b)
}
func (m *Strategy) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Strategy.Marshal(b, m, deterministic)
}
func (dst *Strategy) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Strategy.Merge(dst, src)
}
func (m *Strategy) XXX_Size() int {
	return xxx_messageInfo_Strategy.Size(m)
}
func (m *Strategy) XXX_DiscardUnknown() {
	xxx_messageInfo_Strategy.DiscardUnknown(m)
}

var xxx_messageInfo_Strategy proto.InternalMessageInfo

func (m *Strategy) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Strategy) GetSceneType() uint32 {
	if m != nil {
		return m.SceneType
	}
	return 0
}

func (m *Strategy) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Strategy) GetPriority() uint32 {
	if m != nil {
		return m.Priority
	}
	return 0
}

func (m *Strategy) GetStatus() StrategyStatus {
	if m != nil {
		return m.Status
	}
	return StrategyStatus_STRATEGY_STATUS_UNSPECIFIED
}

func (m *Strategy) GetLastOperator() string {
	if m != nil {
		return m.LastOperator
	}
	return ""
}

func (m *Strategy) GetLastOperationTime() int64 {
	if m != nil {
		return m.LastOperationTime
	}
	return 0
}

func (m *Strategy) GetDelimitType() DelimitType {
	if m != nil {
		return m.DelimitType
	}
	return DelimitType_DELIMIT_TYPE_UNSPECIFIED
}

func (m *Strategy) GetAbTestSetting() *Strategy_AbTestSetting {
	if m != nil {
		return m.AbTestSetting
	}
	return nil
}

func (m *Strategy) GetUserGroupSetting() *Strategy_UserGroupSetting {
	if m != nil {
		return m.UserGroupSetting
	}
	return nil
}

func (m *Strategy) GetTestListIds() []int64 {
	if m != nil {
		return m.TestListIds
	}
	return nil
}

func (m *Strategy) GetRecallSources() []*Strategy_RecallSource {
	if m != nil {
		return m.RecallSources
	}
	return nil
}

func (m *Strategy) GetMinimumRecallQuantity() int32 {
	if m != nil {
		return m.MinimumRecallQuantity
	}
	return 0
}

// 召回源信息
type Strategy_RecallSource struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	TotalQuantity        int32    `protobuf:"varint,2,opt,name=total_quantity,json=totalQuantity,proto3" json:"total_quantity,omitempty"`
	MinimumQuantity      int32    `protobuf:"varint,3,opt,name=minimum_quantity,json=minimumQuantity,proto3" json:"minimum_quantity,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Strategy_RecallSource) Reset()         { *m = Strategy_RecallSource{} }
func (m *Strategy_RecallSource) String() string { return proto.CompactTextString(m) }
func (*Strategy_RecallSource) ProtoMessage()    {}
func (*Strategy_RecallSource) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{3, 0}
}
func (m *Strategy_RecallSource) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Strategy_RecallSource.Unmarshal(m, b)
}
func (m *Strategy_RecallSource) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Strategy_RecallSource.Marshal(b, m, deterministic)
}
func (dst *Strategy_RecallSource) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Strategy_RecallSource.Merge(dst, src)
}
func (m *Strategy_RecallSource) XXX_Size() int {
	return xxx_messageInfo_Strategy_RecallSource.Size(m)
}
func (m *Strategy_RecallSource) XXX_DiscardUnknown() {
	xxx_messageInfo_Strategy_RecallSource.DiscardUnknown(m)
}

var xxx_messageInfo_Strategy_RecallSource proto.InternalMessageInfo

func (m *Strategy_RecallSource) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Strategy_RecallSource) GetTotalQuantity() int32 {
	if m != nil {
		return m.TotalQuantity
	}
	return 0
}

func (m *Strategy_RecallSource) GetMinimumQuantity() int32 {
	if m != nil {
		return m.MinimumQuantity
	}
	return 0
}

type Strategy_AbTestSetting struct {
	ExperimentTag        string   `protobuf:"bytes,1,opt,name=experiment_tag,json=experimentTag,proto3" json:"experiment_tag,omitempty"`
	ExperimentArgs       []string `protobuf:"bytes,2,rep,name=experiment_args,json=experimentArgs,proto3" json:"experiment_args,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Strategy_AbTestSetting) Reset()         { *m = Strategy_AbTestSetting{} }
func (m *Strategy_AbTestSetting) String() string { return proto.CompactTextString(m) }
func (*Strategy_AbTestSetting) ProtoMessage()    {}
func (*Strategy_AbTestSetting) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{3, 1}
}
func (m *Strategy_AbTestSetting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Strategy_AbTestSetting.Unmarshal(m, b)
}
func (m *Strategy_AbTestSetting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Strategy_AbTestSetting.Marshal(b, m, deterministic)
}
func (dst *Strategy_AbTestSetting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Strategy_AbTestSetting.Merge(dst, src)
}
func (m *Strategy_AbTestSetting) XXX_Size() int {
	return xxx_messageInfo_Strategy_AbTestSetting.Size(m)
}
func (m *Strategy_AbTestSetting) XXX_DiscardUnknown() {
	xxx_messageInfo_Strategy_AbTestSetting.DiscardUnknown(m)
}

var xxx_messageInfo_Strategy_AbTestSetting proto.InternalMessageInfo

func (m *Strategy_AbTestSetting) GetExperimentTag() string {
	if m != nil {
		return m.ExperimentTag
	}
	return ""
}

func (m *Strategy_AbTestSetting) GetExperimentArgs() []string {
	if m != nil {
		return m.ExperimentArgs
	}
	return nil
}

type Strategy_UserGroupSetting struct {
	UserGroupIds         []int64  `protobuf:"varint,1,rep,packed,name=user_group_ids,json=userGroupIds,proto3" json:"user_group_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Strategy_UserGroupSetting) Reset()         { *m = Strategy_UserGroupSetting{} }
func (m *Strategy_UserGroupSetting) String() string { return proto.CompactTextString(m) }
func (*Strategy_UserGroupSetting) ProtoMessage()    {}
func (*Strategy_UserGroupSetting) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{3, 2}
}
func (m *Strategy_UserGroupSetting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Strategy_UserGroupSetting.Unmarshal(m, b)
}
func (m *Strategy_UserGroupSetting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Strategy_UserGroupSetting.Marshal(b, m, deterministic)
}
func (dst *Strategy_UserGroupSetting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Strategy_UserGroupSetting.Merge(dst, src)
}
func (m *Strategy_UserGroupSetting) XXX_Size() int {
	return xxx_messageInfo_Strategy_UserGroupSetting.Size(m)
}
func (m *Strategy_UserGroupSetting) XXX_DiscardUnknown() {
	xxx_messageInfo_Strategy_UserGroupSetting.DiscardUnknown(m)
}

var xxx_messageInfo_Strategy_UserGroupSetting proto.InternalMessageInfo

func (m *Strategy_UserGroupSetting) GetUserGroupIds() []int64 {
	if m != nil {
		return m.UserGroupIds
	}
	return nil
}

// 过滤规则信息（游离类型）
type FilterRule struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FilterRule) Reset()         { *m = FilterRule{} }
func (m *FilterRule) String() string { return proto.CompactTextString(m) }
func (*FilterRule) ProtoMessage()    {}
func (*FilterRule) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{4}
}
func (m *FilterRule) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FilterRule.Unmarshal(m, b)
}
func (m *FilterRule) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FilterRule.Marshal(b, m, deterministic)
}
func (dst *FilterRule) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FilterRule.Merge(dst, src)
}
func (m *FilterRule) XXX_Size() int {
	return xxx_messageInfo_FilterRule.Size(m)
}
func (m *FilterRule) XXX_DiscardUnknown() {
	xxx_messageInfo_FilterRule.DiscardUnknown(m)
}

var xxx_messageInfo_FilterRule proto.InternalMessageInfo

func (m *FilterRule) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *FilterRule) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

// 场景信息（游离类型）
type Scene struct {
	Type                 uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Children             []*Scene `protobuf:"bytes,3,rep,name=children,proto3" json:"children,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Scene) Reset()         { *m = Scene{} }
func (m *Scene) String() string { return proto.CompactTextString(m) }
func (*Scene) ProtoMessage()    {}
func (*Scene) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{5}
}
func (m *Scene) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Scene.Unmarshal(m, b)
}
func (m *Scene) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Scene.Marshal(b, m, deterministic)
}
func (dst *Scene) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Scene.Merge(dst, src)
}
func (m *Scene) XXX_Size() int {
	return xxx_messageInfo_Scene.Size(m)
}
func (m *Scene) XXX_DiscardUnknown() {
	xxx_messageInfo_Scene.DiscardUnknown(m)
}

var xxx_messageInfo_Scene proto.InternalMessageInfo

func (m *Scene) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *Scene) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Scene) GetChildren() []*Scene {
	if m != nil {
		return m.Children
	}
	return nil
}

type RecallSource struct {
	Id                   uint32                          `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string                          `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	LastOperator         string                          `protobuf:"bytes,3,opt,name=last_operator,json=lastOperator,proto3" json:"last_operator,omitempty"`
	LastOperationTime    int64                           `protobuf:"varint,4,opt,name=last_operation_time,json=lastOperationTime,proto3" json:"last_operation_time,omitempty"`
	RuleSetting          *RecallSourceRuleSetting        `protobuf:"bytes,5,opt,name=rule_setting,json=ruleSetting,proto3" json:"rule_setting,omitempty"`
	SortParameterType    uint32                          `protobuf:"varint,6,opt,name=sort_parameter_type,json=sortParameterType,proto3" json:"sort_parameter_type,omitempty"`
	Customer             *SortParameterTable             `protobuf:"bytes,7,opt,name=customer,proto3" json:"customer,omitempty"`
	QueueRuleTypeList    []uint32                        `protobuf:"varint,10,rep,packed,name=queue_rule_type_list,json=queueRuleTypeList,proto3" json:"queue_rule_type_list,omitempty"`
	FilterRuleValues     []*RecallSource_FilterRuleValue `protobuf:"bytes,11,rep,name=filter_rule_values,json=filterRuleValues,proto3" json:"filter_rule_values,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *RecallSource) Reset()         { *m = RecallSource{} }
func (m *RecallSource) String() string { return proto.CompactTextString(m) }
func (*RecallSource) ProtoMessage()    {}
func (*RecallSource) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{6}
}
func (m *RecallSource) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecallSource.Unmarshal(m, b)
}
func (m *RecallSource) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecallSource.Marshal(b, m, deterministic)
}
func (dst *RecallSource) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecallSource.Merge(dst, src)
}
func (m *RecallSource) XXX_Size() int {
	return xxx_messageInfo_RecallSource.Size(m)
}
func (m *RecallSource) XXX_DiscardUnknown() {
	xxx_messageInfo_RecallSource.DiscardUnknown(m)
}

var xxx_messageInfo_RecallSource proto.InternalMessageInfo

func (m *RecallSource) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *RecallSource) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *RecallSource) GetLastOperator() string {
	if m != nil {
		return m.LastOperator
	}
	return ""
}

func (m *RecallSource) GetLastOperationTime() int64 {
	if m != nil {
		return m.LastOperationTime
	}
	return 0
}

func (m *RecallSource) GetRuleSetting() *RecallSourceRuleSetting {
	if m != nil {
		return m.RuleSetting
	}
	return nil
}

func (m *RecallSource) GetSortParameterType() uint32 {
	if m != nil {
		return m.SortParameterType
	}
	return 0
}

func (m *RecallSource) GetCustomer() *SortParameterTable {
	if m != nil {
		return m.Customer
	}
	return nil
}

func (m *RecallSource) GetQueueRuleTypeList() []uint32 {
	if m != nil {
		return m.QueueRuleTypeList
	}
	return nil
}

func (m *RecallSource) GetFilterRuleValues() []*RecallSource_FilterRuleValue {
	if m != nil {
		return m.FilterRuleValues
	}
	return nil
}

// 过滤规则信息
type RecallSource_FilterRuleValue struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Quantity             int32    `protobuf:"varint,2,opt,name=quantity,proto3" json:"quantity,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecallSource_FilterRuleValue) Reset()         { *m = RecallSource_FilterRuleValue{} }
func (m *RecallSource_FilterRuleValue) String() string { return proto.CompactTextString(m) }
func (*RecallSource_FilterRuleValue) ProtoMessage()    {}
func (*RecallSource_FilterRuleValue) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{6, 0}
}
func (m *RecallSource_FilterRuleValue) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecallSource_FilterRuleValue.Unmarshal(m, b)
}
func (m *RecallSource_FilterRuleValue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecallSource_FilterRuleValue.Marshal(b, m, deterministic)
}
func (dst *RecallSource_FilterRuleValue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecallSource_FilterRuleValue.Merge(dst, src)
}
func (m *RecallSource_FilterRuleValue) XXX_Size() int {
	return xxx_messageInfo_RecallSource_FilterRuleValue.Size(m)
}
func (m *RecallSource_FilterRuleValue) XXX_DiscardUnknown() {
	xxx_messageInfo_RecallSource_FilterRuleValue.DiscardUnknown(m)
}

var xxx_messageInfo_RecallSource_FilterRuleValue proto.InternalMessageInfo

func (m *RecallSource_FilterRuleValue) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *RecallSource_FilterRuleValue) GetQuantity() int32 {
	if m != nil {
		return m.Quantity
	}
	return 0
}

type RecallSourceRuleSetting struct {
	RuleSet              []*RecallSourceRuleSetting_Set `protobuf:"bytes,1,rep,name=rule_set,json=ruleSet,proto3" json:"rule_set,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *RecallSourceRuleSetting) Reset()         { *m = RecallSourceRuleSetting{} }
func (m *RecallSourceRuleSetting) String() string { return proto.CompactTextString(m) }
func (*RecallSourceRuleSetting) ProtoMessage()    {}
func (*RecallSourceRuleSetting) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{7}
}
func (m *RecallSourceRuleSetting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecallSourceRuleSetting.Unmarshal(m, b)
}
func (m *RecallSourceRuleSetting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecallSourceRuleSetting.Marshal(b, m, deterministic)
}
func (dst *RecallSourceRuleSetting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecallSourceRuleSetting.Merge(dst, src)
}
func (m *RecallSourceRuleSetting) XXX_Size() int {
	return xxx_messageInfo_RecallSourceRuleSetting.Size(m)
}
func (m *RecallSourceRuleSetting) XXX_DiscardUnknown() {
	xxx_messageInfo_RecallSourceRuleSetting.DiscardUnknown(m)
}

var xxx_messageInfo_RecallSourceRuleSetting proto.InternalMessageInfo

func (m *RecallSourceRuleSetting) GetRuleSet() []*RecallSourceRuleSetting_Set {
	if m != nil {
		return m.RuleSet
	}
	return nil
}

type RecallSourceRuleSetting_Condition struct {
	RuleType             uint32   `protobuf:"varint,1,opt,name=rule_type,json=ruleType,proto3" json:"rule_type,omitempty"`
	Value                string   `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	OperatorType         uint32   `protobuf:"varint,3,opt,name=operator_type,json=operatorType,proto3" json:"operator_type,omitempty"`
	SelectedPath         []uint32 `protobuf:"varint,4,rep,packed,name=selected_path,json=selectedPath,proto3" json:"selected_path,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecallSourceRuleSetting_Condition) Reset()         { *m = RecallSourceRuleSetting_Condition{} }
func (m *RecallSourceRuleSetting_Condition) String() string { return proto.CompactTextString(m) }
func (*RecallSourceRuleSetting_Condition) ProtoMessage()    {}
func (*RecallSourceRuleSetting_Condition) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{7, 0}
}
func (m *RecallSourceRuleSetting_Condition) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecallSourceRuleSetting_Condition.Unmarshal(m, b)
}
func (m *RecallSourceRuleSetting_Condition) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecallSourceRuleSetting_Condition.Marshal(b, m, deterministic)
}
func (dst *RecallSourceRuleSetting_Condition) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecallSourceRuleSetting_Condition.Merge(dst, src)
}
func (m *RecallSourceRuleSetting_Condition) XXX_Size() int {
	return xxx_messageInfo_RecallSourceRuleSetting_Condition.Size(m)
}
func (m *RecallSourceRuleSetting_Condition) XXX_DiscardUnknown() {
	xxx_messageInfo_RecallSourceRuleSetting_Condition.DiscardUnknown(m)
}

var xxx_messageInfo_RecallSourceRuleSetting_Condition proto.InternalMessageInfo

func (m *RecallSourceRuleSetting_Condition) GetRuleType() uint32 {
	if m != nil {
		return m.RuleType
	}
	return 0
}

func (m *RecallSourceRuleSetting_Condition) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

func (m *RecallSourceRuleSetting_Condition) GetOperatorType() uint32 {
	if m != nil {
		return m.OperatorType
	}
	return 0
}

func (m *RecallSourceRuleSetting_Condition) GetSelectedPath() []uint32 {
	if m != nil {
		return m.SelectedPath
	}
	return nil
}

type RecallSourceRuleSetting_Set struct {
	Conditions           []*RecallSourceRuleSetting_Condition `protobuf:"bytes,1,rep,name=conditions,proto3" json:"conditions,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                             `json:"-"`
	XXX_unrecognized     []byte                               `json:"-"`
	XXX_sizecache        int32                                `json:"-"`
}

func (m *RecallSourceRuleSetting_Set) Reset()         { *m = RecallSourceRuleSetting_Set{} }
func (m *RecallSourceRuleSetting_Set) String() string { return proto.CompactTextString(m) }
func (*RecallSourceRuleSetting_Set) ProtoMessage()    {}
func (*RecallSourceRuleSetting_Set) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{7, 1}
}
func (m *RecallSourceRuleSetting_Set) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecallSourceRuleSetting_Set.Unmarshal(m, b)
}
func (m *RecallSourceRuleSetting_Set) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecallSourceRuleSetting_Set.Marshal(b, m, deterministic)
}
func (dst *RecallSourceRuleSetting_Set) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecallSourceRuleSetting_Set.Merge(dst, src)
}
func (m *RecallSourceRuleSetting_Set) XXX_Size() int {
	return xxx_messageInfo_RecallSourceRuleSetting_Set.Size(m)
}
func (m *RecallSourceRuleSetting_Set) XXX_DiscardUnknown() {
	xxx_messageInfo_RecallSourceRuleSetting_Set.DiscardUnknown(m)
}

var xxx_messageInfo_RecallSourceRuleSetting_Set proto.InternalMessageInfo

func (m *RecallSourceRuleSetting_Set) GetConditions() []*RecallSourceRuleSetting_Condition {
	if m != nil {
		return m.Conditions
	}
	return nil
}

// 召回源规则（游离类型）
type RecallSourceRule struct {
	Type                 uint32                       `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Name                 string                       `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Operator             []*RecallSourceRule_Operator `protobuf:"bytes,3,rep,name=operator,proto3" json:"operator,omitempty"`
	Children             []*RecallSourceRule          `protobuf:"bytes,4,rep,name=children,proto3" json:"children,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *RecallSourceRule) Reset()         { *m = RecallSourceRule{} }
func (m *RecallSourceRule) String() string { return proto.CompactTextString(m) }
func (*RecallSourceRule) ProtoMessage()    {}
func (*RecallSourceRule) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{8}
}
func (m *RecallSourceRule) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecallSourceRule.Unmarshal(m, b)
}
func (m *RecallSourceRule) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecallSourceRule.Marshal(b, m, deterministic)
}
func (dst *RecallSourceRule) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecallSourceRule.Merge(dst, src)
}
func (m *RecallSourceRule) XXX_Size() int {
	return xxx_messageInfo_RecallSourceRule.Size(m)
}
func (m *RecallSourceRule) XXX_DiscardUnknown() {
	xxx_messageInfo_RecallSourceRule.DiscardUnknown(m)
}

var xxx_messageInfo_RecallSourceRule proto.InternalMessageInfo

func (m *RecallSourceRule) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *RecallSourceRule) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *RecallSourceRule) GetOperator() []*RecallSourceRule_Operator {
	if m != nil {
		return m.Operator
	}
	return nil
}

func (m *RecallSourceRule) GetChildren() []*RecallSourceRule {
	if m != nil {
		return m.Children
	}
	return nil
}

type RecallSourceRule_Operator struct {
	Type                 uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Values               []string `protobuf:"bytes,3,rep,name=values,proto3" json:"values,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecallSourceRule_Operator) Reset()         { *m = RecallSourceRule_Operator{} }
func (m *RecallSourceRule_Operator) String() string { return proto.CompactTextString(m) }
func (*RecallSourceRule_Operator) ProtoMessage()    {}
func (*RecallSourceRule_Operator) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{8, 0}
}
func (m *RecallSourceRule_Operator) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecallSourceRule_Operator.Unmarshal(m, b)
}
func (m *RecallSourceRule_Operator) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecallSourceRule_Operator.Marshal(b, m, deterministic)
}
func (dst *RecallSourceRule_Operator) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecallSourceRule_Operator.Merge(dst, src)
}
func (m *RecallSourceRule_Operator) XXX_Size() int {
	return xxx_messageInfo_RecallSourceRule_Operator.Size(m)
}
func (m *RecallSourceRule_Operator) XXX_DiscardUnknown() {
	xxx_messageInfo_RecallSourceRule_Operator.DiscardUnknown(m)
}

var xxx_messageInfo_RecallSourceRule_Operator proto.InternalMessageInfo

func (m *RecallSourceRule_Operator) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *RecallSourceRule_Operator) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *RecallSourceRule_Operator) GetValues() []string {
	if m != nil {
		return m.Values
	}
	return nil
}

// 排序参数表（游离类型）
type SortParameterTable struct {
	Parameters           []*SortParameterTable_SortParameter `protobuf:"bytes,1,rep,name=parameters,proto3" json:"parameters,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *SortParameterTable) Reset()         { *m = SortParameterTable{} }
func (m *SortParameterTable) String() string { return proto.CompactTextString(m) }
func (*SortParameterTable) ProtoMessage()    {}
func (*SortParameterTable) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{9}
}
func (m *SortParameterTable) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SortParameterTable.Unmarshal(m, b)
}
func (m *SortParameterTable) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SortParameterTable.Marshal(b, m, deterministic)
}
func (dst *SortParameterTable) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SortParameterTable.Merge(dst, src)
}
func (m *SortParameterTable) XXX_Size() int {
	return xxx_messageInfo_SortParameterTable.Size(m)
}
func (m *SortParameterTable) XXX_DiscardUnknown() {
	xxx_messageInfo_SortParameterTable.DiscardUnknown(m)
}

var xxx_messageInfo_SortParameterTable proto.InternalMessageInfo

func (m *SortParameterTable) GetParameters() []*SortParameterTable_SortParameter {
	if m != nil {
		return m.Parameters
	}
	return nil
}

type SortParameterTable_Item struct {
	Value                string   `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	Score                int32    `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SortParameterTable_Item) Reset()         { *m = SortParameterTable_Item{} }
func (m *SortParameterTable_Item) String() string { return proto.CompactTextString(m) }
func (*SortParameterTable_Item) ProtoMessage()    {}
func (*SortParameterTable_Item) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{9, 0}
}
func (m *SortParameterTable_Item) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SortParameterTable_Item.Unmarshal(m, b)
}
func (m *SortParameterTable_Item) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SortParameterTable_Item.Marshal(b, m, deterministic)
}
func (dst *SortParameterTable_Item) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SortParameterTable_Item.Merge(dst, src)
}
func (m *SortParameterTable_Item) XXX_Size() int {
	return xxx_messageInfo_SortParameterTable_Item.Size(m)
}
func (m *SortParameterTable_Item) XXX_DiscardUnknown() {
	xxx_messageInfo_SortParameterTable_Item.DiscardUnknown(m)
}

var xxx_messageInfo_SortParameterTable_Item proto.InternalMessageInfo

func (m *SortParameterTable_Item) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

func (m *SortParameterTable_Item) GetScore() int32 {
	if m != nil {
		return m.Score
	}
	return 0
}

type SortParameterTable_SortParameter struct {
	Type                 uint32                              `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Name                 string                              `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Items                []*SortParameterTable_Item          `protobuf:"bytes,3,rep,name=items,proto3" json:"items,omitempty"`
	Children             []*SortParameterTable_SortParameter `protobuf:"bytes,4,rep,name=children,proto3" json:"children,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *SortParameterTable_SortParameter) Reset()         { *m = SortParameterTable_SortParameter{} }
func (m *SortParameterTable_SortParameter) String() string { return proto.CompactTextString(m) }
func (*SortParameterTable_SortParameter) ProtoMessage()    {}
func (*SortParameterTable_SortParameter) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{9, 1}
}
func (m *SortParameterTable_SortParameter) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SortParameterTable_SortParameter.Unmarshal(m, b)
}
func (m *SortParameterTable_SortParameter) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SortParameterTable_SortParameter.Marshal(b, m, deterministic)
}
func (dst *SortParameterTable_SortParameter) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SortParameterTable_SortParameter.Merge(dst, src)
}
func (m *SortParameterTable_SortParameter) XXX_Size() int {
	return xxx_messageInfo_SortParameterTable_SortParameter.Size(m)
}
func (m *SortParameterTable_SortParameter) XXX_DiscardUnknown() {
	xxx_messageInfo_SortParameterTable_SortParameter.DiscardUnknown(m)
}

var xxx_messageInfo_SortParameterTable_SortParameter proto.InternalMessageInfo

func (m *SortParameterTable_SortParameter) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *SortParameterTable_SortParameter) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SortParameterTable_SortParameter) GetItems() []*SortParameterTable_Item {
	if m != nil {
		return m.Items
	}
	return nil
}

func (m *SortParameterTable_SortParameter) GetChildren() []*SortParameterTable_SortParameter {
	if m != nil {
		return m.Children
	}
	return nil
}

// 多级排序规则（游离类型）
type RecallSourceQueueRule struct {
	Type                 uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecallSourceQueueRule) Reset()         { *m = RecallSourceQueueRule{} }
func (m *RecallSourceQueueRule) String() string { return proto.CompactTextString(m) }
func (*RecallSourceQueueRule) ProtoMessage()    {}
func (*RecallSourceQueueRule) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{10}
}
func (m *RecallSourceQueueRule) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecallSourceQueueRule.Unmarshal(m, b)
}
func (m *RecallSourceQueueRule) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecallSourceQueueRule.Marshal(b, m, deterministic)
}
func (dst *RecallSourceQueueRule) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecallSourceQueueRule.Merge(dst, src)
}
func (m *RecallSourceQueueRule) XXX_Size() int {
	return xxx_messageInfo_RecallSourceQueueRule.Size(m)
}
func (m *RecallSourceQueueRule) XXX_DiscardUnknown() {
	xxx_messageInfo_RecallSourceQueueRule.DiscardUnknown(m)
}

var xxx_messageInfo_RecallSourceQueueRule proto.InternalMessageInfo

func (m *RecallSourceQueueRule) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *RecallSourceQueueRule) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type GetRecallSourceQueueRuleRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecallSourceQueueRuleRequest) Reset()         { *m = GetRecallSourceQueueRuleRequest{} }
func (m *GetRecallSourceQueueRuleRequest) String() string { return proto.CompactTextString(m) }
func (*GetRecallSourceQueueRuleRequest) ProtoMessage()    {}
func (*GetRecallSourceQueueRuleRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{11}
}
func (m *GetRecallSourceQueueRuleRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecallSourceQueueRuleRequest.Unmarshal(m, b)
}
func (m *GetRecallSourceQueueRuleRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecallSourceQueueRuleRequest.Marshal(b, m, deterministic)
}
func (dst *GetRecallSourceQueueRuleRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecallSourceQueueRuleRequest.Merge(dst, src)
}
func (m *GetRecallSourceQueueRuleRequest) XXX_Size() int {
	return xxx_messageInfo_GetRecallSourceQueueRuleRequest.Size(m)
}
func (m *GetRecallSourceQueueRuleRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecallSourceQueueRuleRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecallSourceQueueRuleRequest proto.InternalMessageInfo

type GetRecallSourceQueueRuleResponse struct {
	List                 []*RecallSourceQueueRule `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetRecallSourceQueueRuleResponse) Reset()         { *m = GetRecallSourceQueueRuleResponse{} }
func (m *GetRecallSourceQueueRuleResponse) String() string { return proto.CompactTextString(m) }
func (*GetRecallSourceQueueRuleResponse) ProtoMessage()    {}
func (*GetRecallSourceQueueRuleResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{12}
}
func (m *GetRecallSourceQueueRuleResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecallSourceQueueRuleResponse.Unmarshal(m, b)
}
func (m *GetRecallSourceQueueRuleResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecallSourceQueueRuleResponse.Marshal(b, m, deterministic)
}
func (dst *GetRecallSourceQueueRuleResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecallSourceQueueRuleResponse.Merge(dst, src)
}
func (m *GetRecallSourceQueueRuleResponse) XXX_Size() int {
	return xxx_messageInfo_GetRecallSourceQueueRuleResponse.Size(m)
}
func (m *GetRecallSourceQueueRuleResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecallSourceQueueRuleResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecallSourceQueueRuleResponse proto.InternalMessageInfo

func (m *GetRecallSourceQueueRuleResponse) GetList() []*RecallSourceQueueRule {
	if m != nil {
		return m.List
	}
	return nil
}

// 请求策略列表
type ListStrategiesRequest struct {
	SceneType            uint32   `protobuf:"varint,1,opt,name=scene_type,json=sceneType,proto3" json:"scene_type,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	PageNumber           uint32   `protobuf:"varint,3,opt,name=page_number,json=pageNumber,proto3" json:"page_number,omitempty"`
	PageSize             uint32   `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListStrategiesRequest) Reset()         { *m = ListStrategiesRequest{} }
func (m *ListStrategiesRequest) String() string { return proto.CompactTextString(m) }
func (*ListStrategiesRequest) ProtoMessage()    {}
func (*ListStrategiesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{13}
}
func (m *ListStrategiesRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListStrategiesRequest.Unmarshal(m, b)
}
func (m *ListStrategiesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListStrategiesRequest.Marshal(b, m, deterministic)
}
func (dst *ListStrategiesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListStrategiesRequest.Merge(dst, src)
}
func (m *ListStrategiesRequest) XXX_Size() int {
	return xxx_messageInfo_ListStrategiesRequest.Size(m)
}
func (m *ListStrategiesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ListStrategiesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ListStrategiesRequest proto.InternalMessageInfo

func (m *ListStrategiesRequest) GetSceneType() uint32 {
	if m != nil {
		return m.SceneType
	}
	return 0
}

func (m *ListStrategiesRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ListStrategiesRequest) GetPageNumber() uint32 {
	if m != nil {
		return m.PageNumber
	}
	return 0
}

func (m *ListStrategiesRequest) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

// 响应策略列表
type ListStrategiesResponse struct {
	Strategies           []*Strategy `protobuf:"bytes,1,rep,name=strategies,proto3" json:"strategies,omitempty"`
	Total                uint32      `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *ListStrategiesResponse) Reset()         { *m = ListStrategiesResponse{} }
func (m *ListStrategiesResponse) String() string { return proto.CompactTextString(m) }
func (*ListStrategiesResponse) ProtoMessage()    {}
func (*ListStrategiesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{14}
}
func (m *ListStrategiesResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListStrategiesResponse.Unmarshal(m, b)
}
func (m *ListStrategiesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListStrategiesResponse.Marshal(b, m, deterministic)
}
func (dst *ListStrategiesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListStrategiesResponse.Merge(dst, src)
}
func (m *ListStrategiesResponse) XXX_Size() int {
	return xxx_messageInfo_ListStrategiesResponse.Size(m)
}
func (m *ListStrategiesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ListStrategiesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ListStrategiesResponse proto.InternalMessageInfo

func (m *ListStrategiesResponse) GetStrategies() []*Strategy {
	if m != nil {
		return m.Strategies
	}
	return nil
}

func (m *ListStrategiesResponse) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 获取策略详情请求
type GetStrategyRequest struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStrategyRequest) Reset()         { *m = GetStrategyRequest{} }
func (m *GetStrategyRequest) String() string { return proto.CompactTextString(m) }
func (*GetStrategyRequest) ProtoMessage()    {}
func (*GetStrategyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{15}
}
func (m *GetStrategyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStrategyRequest.Unmarshal(m, b)
}
func (m *GetStrategyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStrategyRequest.Marshal(b, m, deterministic)
}
func (dst *GetStrategyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStrategyRequest.Merge(dst, src)
}
func (m *GetStrategyRequest) XXX_Size() int {
	return xxx_messageInfo_GetStrategyRequest.Size(m)
}
func (m *GetStrategyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStrategyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetStrategyRequest proto.InternalMessageInfo

func (m *GetStrategyRequest) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 获取策略详情响应
type GetStrategyResponse struct {
	Strategy             *Strategy `protobuf:"bytes,1,opt,name=strategy,proto3" json:"strategy,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetStrategyResponse) Reset()         { *m = GetStrategyResponse{} }
func (m *GetStrategyResponse) String() string { return proto.CompactTextString(m) }
func (*GetStrategyResponse) ProtoMessage()    {}
func (*GetStrategyResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{16}
}
func (m *GetStrategyResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStrategyResponse.Unmarshal(m, b)
}
func (m *GetStrategyResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStrategyResponse.Marshal(b, m, deterministic)
}
func (dst *GetStrategyResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStrategyResponse.Merge(dst, src)
}
func (m *GetStrategyResponse) XXX_Size() int {
	return xxx_messageInfo_GetStrategyResponse.Size(m)
}
func (m *GetStrategyResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStrategyResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetStrategyResponse proto.InternalMessageInfo

func (m *GetStrategyResponse) GetStrategy() *Strategy {
	if m != nil {
		return m.Strategy
	}
	return nil
}

// 创建策略请求
type CreateStrategyRequest struct {
	Strategy             *Strategy `protobuf:"bytes,1,opt,name=strategy,proto3" json:"strategy,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *CreateStrategyRequest) Reset()         { *m = CreateStrategyRequest{} }
func (m *CreateStrategyRequest) String() string { return proto.CompactTextString(m) }
func (*CreateStrategyRequest) ProtoMessage()    {}
func (*CreateStrategyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{17}
}
func (m *CreateStrategyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateStrategyRequest.Unmarshal(m, b)
}
func (m *CreateStrategyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateStrategyRequest.Marshal(b, m, deterministic)
}
func (dst *CreateStrategyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateStrategyRequest.Merge(dst, src)
}
func (m *CreateStrategyRequest) XXX_Size() int {
	return xxx_messageInfo_CreateStrategyRequest.Size(m)
}
func (m *CreateStrategyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateStrategyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateStrategyRequest proto.InternalMessageInfo

func (m *CreateStrategyRequest) GetStrategy() *Strategy {
	if m != nil {
		return m.Strategy
	}
	return nil
}

// 创建策略响应
type CreateStrategyResponse struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateStrategyResponse) Reset()         { *m = CreateStrategyResponse{} }
func (m *CreateStrategyResponse) String() string { return proto.CompactTextString(m) }
func (*CreateStrategyResponse) ProtoMessage()    {}
func (*CreateStrategyResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{18}
}
func (m *CreateStrategyResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateStrategyResponse.Unmarshal(m, b)
}
func (m *CreateStrategyResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateStrategyResponse.Marshal(b, m, deterministic)
}
func (dst *CreateStrategyResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateStrategyResponse.Merge(dst, src)
}
func (m *CreateStrategyResponse) XXX_Size() int {
	return xxx_messageInfo_CreateStrategyResponse.Size(m)
}
func (m *CreateStrategyResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateStrategyResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateStrategyResponse proto.InternalMessageInfo

func (m *CreateStrategyResponse) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 更新策略请求
type UpdateStrategyRequest struct {
	Strategy             *Strategy `protobuf:"bytes,1,opt,name=strategy,proto3" json:"strategy,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *UpdateStrategyRequest) Reset()         { *m = UpdateStrategyRequest{} }
func (m *UpdateStrategyRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateStrategyRequest) ProtoMessage()    {}
func (*UpdateStrategyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{19}
}
func (m *UpdateStrategyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateStrategyRequest.Unmarshal(m, b)
}
func (m *UpdateStrategyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateStrategyRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateStrategyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateStrategyRequest.Merge(dst, src)
}
func (m *UpdateStrategyRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateStrategyRequest.Size(m)
}
func (m *UpdateStrategyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateStrategyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateStrategyRequest proto.InternalMessageInfo

func (m *UpdateStrategyRequest) GetStrategy() *Strategy {
	if m != nil {
		return m.Strategy
	}
	return nil
}

// 更新策略响应
type UpdateStrategyResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateStrategyResponse) Reset()         { *m = UpdateStrategyResponse{} }
func (m *UpdateStrategyResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateStrategyResponse) ProtoMessage()    {}
func (*UpdateStrategyResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{20}
}
func (m *UpdateStrategyResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateStrategyResponse.Unmarshal(m, b)
}
func (m *UpdateStrategyResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateStrategyResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateStrategyResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateStrategyResponse.Merge(dst, src)
}
func (m *UpdateStrategyResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateStrategyResponse.Size(m)
}
func (m *UpdateStrategyResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateStrategyResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateStrategyResponse proto.InternalMessageInfo

// 暂停策略请求
type PauseStrategyRequest struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PauseStrategyRequest) Reset()         { *m = PauseStrategyRequest{} }
func (m *PauseStrategyRequest) String() string { return proto.CompactTextString(m) }
func (*PauseStrategyRequest) ProtoMessage()    {}
func (*PauseStrategyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{21}
}
func (m *PauseStrategyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PauseStrategyRequest.Unmarshal(m, b)
}
func (m *PauseStrategyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PauseStrategyRequest.Marshal(b, m, deterministic)
}
func (dst *PauseStrategyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PauseStrategyRequest.Merge(dst, src)
}
func (m *PauseStrategyRequest) XXX_Size() int {
	return xxx_messageInfo_PauseStrategyRequest.Size(m)
}
func (m *PauseStrategyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PauseStrategyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PauseStrategyRequest proto.InternalMessageInfo

func (m *PauseStrategyRequest) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 暂停策略响应
type PauseStrategyResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PauseStrategyResponse) Reset()         { *m = PauseStrategyResponse{} }
func (m *PauseStrategyResponse) String() string { return proto.CompactTextString(m) }
func (*PauseStrategyResponse) ProtoMessage()    {}
func (*PauseStrategyResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{22}
}
func (m *PauseStrategyResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PauseStrategyResponse.Unmarshal(m, b)
}
func (m *PauseStrategyResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PauseStrategyResponse.Marshal(b, m, deterministic)
}
func (dst *PauseStrategyResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PauseStrategyResponse.Merge(dst, src)
}
func (m *PauseStrategyResponse) XXX_Size() int {
	return xxx_messageInfo_PauseStrategyResponse.Size(m)
}
func (m *PauseStrategyResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PauseStrategyResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PauseStrategyResponse proto.InternalMessageInfo

// 启动策略请求
type StartStrategyRequest struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartStrategyRequest) Reset()         { *m = StartStrategyRequest{} }
func (m *StartStrategyRequest) String() string { return proto.CompactTextString(m) }
func (*StartStrategyRequest) ProtoMessage()    {}
func (*StartStrategyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{23}
}
func (m *StartStrategyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartStrategyRequest.Unmarshal(m, b)
}
func (m *StartStrategyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartStrategyRequest.Marshal(b, m, deterministic)
}
func (dst *StartStrategyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartStrategyRequest.Merge(dst, src)
}
func (m *StartStrategyRequest) XXX_Size() int {
	return xxx_messageInfo_StartStrategyRequest.Size(m)
}
func (m *StartStrategyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_StartStrategyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_StartStrategyRequest proto.InternalMessageInfo

func (m *StartStrategyRequest) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 启动策略响应
type StartStrategyResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartStrategyResponse) Reset()         { *m = StartStrategyResponse{} }
func (m *StartStrategyResponse) String() string { return proto.CompactTextString(m) }
func (*StartStrategyResponse) ProtoMessage()    {}
func (*StartStrategyResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{24}
}
func (m *StartStrategyResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartStrategyResponse.Unmarshal(m, b)
}
func (m *StartStrategyResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartStrategyResponse.Marshal(b, m, deterministic)
}
func (dst *StartStrategyResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartStrategyResponse.Merge(dst, src)
}
func (m *StartStrategyResponse) XXX_Size() int {
	return xxx_messageInfo_StartStrategyResponse.Size(m)
}
func (m *StartStrategyResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_StartStrategyResponse.DiscardUnknown(m)
}

var xxx_messageInfo_StartStrategyResponse proto.InternalMessageInfo

// 删除策略请求
type DeleteStrategyRequest struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteStrategyRequest) Reset()         { *m = DeleteStrategyRequest{} }
func (m *DeleteStrategyRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteStrategyRequest) ProtoMessage()    {}
func (*DeleteStrategyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{25}
}
func (m *DeleteStrategyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteStrategyRequest.Unmarshal(m, b)
}
func (m *DeleteStrategyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteStrategyRequest.Marshal(b, m, deterministic)
}
func (dst *DeleteStrategyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteStrategyRequest.Merge(dst, src)
}
func (m *DeleteStrategyRequest) XXX_Size() int {
	return xxx_messageInfo_DeleteStrategyRequest.Size(m)
}
func (m *DeleteStrategyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteStrategyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteStrategyRequest proto.InternalMessageInfo

func (m *DeleteStrategyRequest) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 删除策略响应
type DeleteStrategyResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteStrategyResponse) Reset()         { *m = DeleteStrategyResponse{} }
func (m *DeleteStrategyResponse) String() string { return proto.CompactTextString(m) }
func (*DeleteStrategyResponse) ProtoMessage()    {}
func (*DeleteStrategyResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{26}
}
func (m *DeleteStrategyResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteStrategyResponse.Unmarshal(m, b)
}
func (m *DeleteStrategyResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteStrategyResponse.Marshal(b, m, deterministic)
}
func (dst *DeleteStrategyResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteStrategyResponse.Merge(dst, src)
}
func (m *DeleteStrategyResponse) XXX_Size() int {
	return xxx_messageInfo_DeleteStrategyResponse.Size(m)
}
func (m *DeleteStrategyResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteStrategyResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteStrategyResponse proto.InternalMessageInfo

// 获取过滤规则请求
type GetFilterRulesRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFilterRulesRequest) Reset()         { *m = GetFilterRulesRequest{} }
func (m *GetFilterRulesRequest) String() string { return proto.CompactTextString(m) }
func (*GetFilterRulesRequest) ProtoMessage()    {}
func (*GetFilterRulesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{27}
}
func (m *GetFilterRulesRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFilterRulesRequest.Unmarshal(m, b)
}
func (m *GetFilterRulesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFilterRulesRequest.Marshal(b, m, deterministic)
}
func (dst *GetFilterRulesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFilterRulesRequest.Merge(dst, src)
}
func (m *GetFilterRulesRequest) XXX_Size() int {
	return xxx_messageInfo_GetFilterRulesRequest.Size(m)
}
func (m *GetFilterRulesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFilterRulesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetFilterRulesRequest proto.InternalMessageInfo

// 获取过滤规则响应
type GetFilterRulesResponse struct {
	FilterRules          []*FilterRule `protobuf:"bytes,1,rep,name=filter_rules,json=filterRules,proto3" json:"filter_rules,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetFilterRulesResponse) Reset()         { *m = GetFilterRulesResponse{} }
func (m *GetFilterRulesResponse) String() string { return proto.CompactTextString(m) }
func (*GetFilterRulesResponse) ProtoMessage()    {}
func (*GetFilterRulesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{28}
}
func (m *GetFilterRulesResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFilterRulesResponse.Unmarshal(m, b)
}
func (m *GetFilterRulesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFilterRulesResponse.Marshal(b, m, deterministic)
}
func (dst *GetFilterRulesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFilterRulesResponse.Merge(dst, src)
}
func (m *GetFilterRulesResponse) XXX_Size() int {
	return xxx_messageInfo_GetFilterRulesResponse.Size(m)
}
func (m *GetFilterRulesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFilterRulesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetFilterRulesResponse proto.InternalMessageInfo

func (m *GetFilterRulesResponse) GetFilterRules() []*FilterRule {
	if m != nil {
		return m.FilterRules
	}
	return nil
}

// 获取场景列表请求
type GetScenesRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetScenesRequest) Reset()         { *m = GetScenesRequest{} }
func (m *GetScenesRequest) String() string { return proto.CompactTextString(m) }
func (*GetScenesRequest) ProtoMessage()    {}
func (*GetScenesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{29}
}
func (m *GetScenesRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScenesRequest.Unmarshal(m, b)
}
func (m *GetScenesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScenesRequest.Marshal(b, m, deterministic)
}
func (dst *GetScenesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScenesRequest.Merge(dst, src)
}
func (m *GetScenesRequest) XXX_Size() int {
	return xxx_messageInfo_GetScenesRequest.Size(m)
}
func (m *GetScenesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScenesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetScenesRequest proto.InternalMessageInfo

// 获取场景列表响应
type GetScenesResponse struct {
	List                 []*Scene `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetScenesResponse) Reset()         { *m = GetScenesResponse{} }
func (m *GetScenesResponse) String() string { return proto.CompactTextString(m) }
func (*GetScenesResponse) ProtoMessage()    {}
func (*GetScenesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{30}
}
func (m *GetScenesResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScenesResponse.Unmarshal(m, b)
}
func (m *GetScenesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScenesResponse.Marshal(b, m, deterministic)
}
func (dst *GetScenesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScenesResponse.Merge(dst, src)
}
func (m *GetScenesResponse) XXX_Size() int {
	return xxx_messageInfo_GetScenesResponse.Size(m)
}
func (m *GetScenesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScenesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetScenesResponse proto.InternalMessageInfo

func (m *GetScenesResponse) GetList() []*Scene {
	if m != nil {
		return m.List
	}
	return nil
}

type ListRecallSourcesRequest struct {
	PageNumber           uint32   `protobuf:"varint,1,opt,name=page_number,json=pageNumber,proto3" json:"page_number,omitempty"`
	PageSize             uint32   `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Id                   uint32   `protobuf:"varint,4,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListRecallSourcesRequest) Reset()         { *m = ListRecallSourcesRequest{} }
func (m *ListRecallSourcesRequest) String() string { return proto.CompactTextString(m) }
func (*ListRecallSourcesRequest) ProtoMessage()    {}
func (*ListRecallSourcesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{31}
}
func (m *ListRecallSourcesRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListRecallSourcesRequest.Unmarshal(m, b)
}
func (m *ListRecallSourcesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListRecallSourcesRequest.Marshal(b, m, deterministic)
}
func (dst *ListRecallSourcesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListRecallSourcesRequest.Merge(dst, src)
}
func (m *ListRecallSourcesRequest) XXX_Size() int {
	return xxx_messageInfo_ListRecallSourcesRequest.Size(m)
}
func (m *ListRecallSourcesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ListRecallSourcesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ListRecallSourcesRequest proto.InternalMessageInfo

func (m *ListRecallSourcesRequest) GetPageNumber() uint32 {
	if m != nil {
		return m.PageNumber
	}
	return 0
}

func (m *ListRecallSourcesRequest) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *ListRecallSourcesRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ListRecallSourcesRequest) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type ListRecallSourcesResponse struct {
	RecallSources        []*RecallSource `protobuf:"bytes,1,rep,name=recall_sources,json=recallSources,proto3" json:"recall_sources,omitempty"`
	Total                uint32          `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ListRecallSourcesResponse) Reset()         { *m = ListRecallSourcesResponse{} }
func (m *ListRecallSourcesResponse) String() string { return proto.CompactTextString(m) }
func (*ListRecallSourcesResponse) ProtoMessage()    {}
func (*ListRecallSourcesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{32}
}
func (m *ListRecallSourcesResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListRecallSourcesResponse.Unmarshal(m, b)
}
func (m *ListRecallSourcesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListRecallSourcesResponse.Marshal(b, m, deterministic)
}
func (dst *ListRecallSourcesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListRecallSourcesResponse.Merge(dst, src)
}
func (m *ListRecallSourcesResponse) XXX_Size() int {
	return xxx_messageInfo_ListRecallSourcesResponse.Size(m)
}
func (m *ListRecallSourcesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ListRecallSourcesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ListRecallSourcesResponse proto.InternalMessageInfo

func (m *ListRecallSourcesResponse) GetRecallSources() []*RecallSource {
	if m != nil {
		return m.RecallSources
	}
	return nil
}

func (m *ListRecallSourcesResponse) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetRecallSourceDetailsRequest struct {
	RecallSourceId       uint32   `protobuf:"varint,1,opt,name=recall_source_id,json=recallSourceId,proto3" json:"recall_source_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecallSourceDetailsRequest) Reset()         { *m = GetRecallSourceDetailsRequest{} }
func (m *GetRecallSourceDetailsRequest) String() string { return proto.CompactTextString(m) }
func (*GetRecallSourceDetailsRequest) ProtoMessage()    {}
func (*GetRecallSourceDetailsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{33}
}
func (m *GetRecallSourceDetailsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecallSourceDetailsRequest.Unmarshal(m, b)
}
func (m *GetRecallSourceDetailsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecallSourceDetailsRequest.Marshal(b, m, deterministic)
}
func (dst *GetRecallSourceDetailsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecallSourceDetailsRequest.Merge(dst, src)
}
func (m *GetRecallSourceDetailsRequest) XXX_Size() int {
	return xxx_messageInfo_GetRecallSourceDetailsRequest.Size(m)
}
func (m *GetRecallSourceDetailsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecallSourceDetailsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecallSourceDetailsRequest proto.InternalMessageInfo

func (m *GetRecallSourceDetailsRequest) GetRecallSourceId() uint32 {
	if m != nil {
		return m.RecallSourceId
	}
	return 0
}

type GetRecallSourceDetailsResponse struct {
	RecallSource         *RecallSource `protobuf:"bytes,1,opt,name=recall_source,json=recallSource,proto3" json:"recall_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetRecallSourceDetailsResponse) Reset()         { *m = GetRecallSourceDetailsResponse{} }
func (m *GetRecallSourceDetailsResponse) String() string { return proto.CompactTextString(m) }
func (*GetRecallSourceDetailsResponse) ProtoMessage()    {}
func (*GetRecallSourceDetailsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{34}
}
func (m *GetRecallSourceDetailsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecallSourceDetailsResponse.Unmarshal(m, b)
}
func (m *GetRecallSourceDetailsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecallSourceDetailsResponse.Marshal(b, m, deterministic)
}
func (dst *GetRecallSourceDetailsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecallSourceDetailsResponse.Merge(dst, src)
}
func (m *GetRecallSourceDetailsResponse) XXX_Size() int {
	return xxx_messageInfo_GetRecallSourceDetailsResponse.Size(m)
}
func (m *GetRecallSourceDetailsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecallSourceDetailsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecallSourceDetailsResponse proto.InternalMessageInfo

func (m *GetRecallSourceDetailsResponse) GetRecallSource() *RecallSource {
	if m != nil {
		return m.RecallSource
	}
	return nil
}

type CreateRecallSourceRequest struct {
	RecallSource         *RecallSource `protobuf:"bytes,1,opt,name=recall_source,json=recallSource,proto3" json:"recall_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CreateRecallSourceRequest) Reset()         { *m = CreateRecallSourceRequest{} }
func (m *CreateRecallSourceRequest) String() string { return proto.CompactTextString(m) }
func (*CreateRecallSourceRequest) ProtoMessage()    {}
func (*CreateRecallSourceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{35}
}
func (m *CreateRecallSourceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateRecallSourceRequest.Unmarshal(m, b)
}
func (m *CreateRecallSourceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateRecallSourceRequest.Marshal(b, m, deterministic)
}
func (dst *CreateRecallSourceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateRecallSourceRequest.Merge(dst, src)
}
func (m *CreateRecallSourceRequest) XXX_Size() int {
	return xxx_messageInfo_CreateRecallSourceRequest.Size(m)
}
func (m *CreateRecallSourceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateRecallSourceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateRecallSourceRequest proto.InternalMessageInfo

func (m *CreateRecallSourceRequest) GetRecallSource() *RecallSource {
	if m != nil {
		return m.RecallSource
	}
	return nil
}

type CreateRecallSourceResponse struct {
	RecallSourceId       uint32   `protobuf:"varint,1,opt,name=recall_source_id,json=recallSourceId,proto3" json:"recall_source_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateRecallSourceResponse) Reset()         { *m = CreateRecallSourceResponse{} }
func (m *CreateRecallSourceResponse) String() string { return proto.CompactTextString(m) }
func (*CreateRecallSourceResponse) ProtoMessage()    {}
func (*CreateRecallSourceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{36}
}
func (m *CreateRecallSourceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateRecallSourceResponse.Unmarshal(m, b)
}
func (m *CreateRecallSourceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateRecallSourceResponse.Marshal(b, m, deterministic)
}
func (dst *CreateRecallSourceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateRecallSourceResponse.Merge(dst, src)
}
func (m *CreateRecallSourceResponse) XXX_Size() int {
	return xxx_messageInfo_CreateRecallSourceResponse.Size(m)
}
func (m *CreateRecallSourceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateRecallSourceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateRecallSourceResponse proto.InternalMessageInfo

func (m *CreateRecallSourceResponse) GetRecallSourceId() uint32 {
	if m != nil {
		return m.RecallSourceId
	}
	return 0
}

type UpdateRecallSourceRequest struct {
	RecallSource         *RecallSource `protobuf:"bytes,1,opt,name=recall_source,json=recallSource,proto3" json:"recall_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UpdateRecallSourceRequest) Reset()         { *m = UpdateRecallSourceRequest{} }
func (m *UpdateRecallSourceRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateRecallSourceRequest) ProtoMessage()    {}
func (*UpdateRecallSourceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{37}
}
func (m *UpdateRecallSourceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateRecallSourceRequest.Unmarshal(m, b)
}
func (m *UpdateRecallSourceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateRecallSourceRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateRecallSourceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateRecallSourceRequest.Merge(dst, src)
}
func (m *UpdateRecallSourceRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateRecallSourceRequest.Size(m)
}
func (m *UpdateRecallSourceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateRecallSourceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateRecallSourceRequest proto.InternalMessageInfo

func (m *UpdateRecallSourceRequest) GetRecallSource() *RecallSource {
	if m != nil {
		return m.RecallSource
	}
	return nil
}

type UpdateRecallSourceResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateRecallSourceResponse) Reset()         { *m = UpdateRecallSourceResponse{} }
func (m *UpdateRecallSourceResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateRecallSourceResponse) ProtoMessage()    {}
func (*UpdateRecallSourceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{38}
}
func (m *UpdateRecallSourceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateRecallSourceResponse.Unmarshal(m, b)
}
func (m *UpdateRecallSourceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateRecallSourceResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateRecallSourceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateRecallSourceResponse.Merge(dst, src)
}
func (m *UpdateRecallSourceResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateRecallSourceResponse.Size(m)
}
func (m *UpdateRecallSourceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateRecallSourceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateRecallSourceResponse proto.InternalMessageInfo

type DeleteRecallSourceRequest struct {
	RecallSourceId       uint32   `protobuf:"varint,1,opt,name=recall_source_id,json=recallSourceId,proto3" json:"recall_source_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteRecallSourceRequest) Reset()         { *m = DeleteRecallSourceRequest{} }
func (m *DeleteRecallSourceRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteRecallSourceRequest) ProtoMessage()    {}
func (*DeleteRecallSourceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{39}
}
func (m *DeleteRecallSourceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteRecallSourceRequest.Unmarshal(m, b)
}
func (m *DeleteRecallSourceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteRecallSourceRequest.Marshal(b, m, deterministic)
}
func (dst *DeleteRecallSourceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteRecallSourceRequest.Merge(dst, src)
}
func (m *DeleteRecallSourceRequest) XXX_Size() int {
	return xxx_messageInfo_DeleteRecallSourceRequest.Size(m)
}
func (m *DeleteRecallSourceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteRecallSourceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteRecallSourceRequest proto.InternalMessageInfo

func (m *DeleteRecallSourceRequest) GetRecallSourceId() uint32 {
	if m != nil {
		return m.RecallSourceId
	}
	return 0
}

type DeleteRecallSourceResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteRecallSourceResponse) Reset()         { *m = DeleteRecallSourceResponse{} }
func (m *DeleteRecallSourceResponse) String() string { return proto.CompactTextString(m) }
func (*DeleteRecallSourceResponse) ProtoMessage()    {}
func (*DeleteRecallSourceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{40}
}
func (m *DeleteRecallSourceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteRecallSourceResponse.Unmarshal(m, b)
}
func (m *DeleteRecallSourceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteRecallSourceResponse.Marshal(b, m, deterministic)
}
func (dst *DeleteRecallSourceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteRecallSourceResponse.Merge(dst, src)
}
func (m *DeleteRecallSourceResponse) XXX_Size() int {
	return xxx_messageInfo_DeleteRecallSourceResponse.Size(m)
}
func (m *DeleteRecallSourceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteRecallSourceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteRecallSourceResponse proto.InternalMessageInfo

type GetRecallSourceRuleListRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecallSourceRuleListRequest) Reset()         { *m = GetRecallSourceRuleListRequest{} }
func (m *GetRecallSourceRuleListRequest) String() string { return proto.CompactTextString(m) }
func (*GetRecallSourceRuleListRequest) ProtoMessage()    {}
func (*GetRecallSourceRuleListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{41}
}
func (m *GetRecallSourceRuleListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecallSourceRuleListRequest.Unmarshal(m, b)
}
func (m *GetRecallSourceRuleListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecallSourceRuleListRequest.Marshal(b, m, deterministic)
}
func (dst *GetRecallSourceRuleListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecallSourceRuleListRequest.Merge(dst, src)
}
func (m *GetRecallSourceRuleListRequest) XXX_Size() int {
	return xxx_messageInfo_GetRecallSourceRuleListRequest.Size(m)
}
func (m *GetRecallSourceRuleListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecallSourceRuleListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecallSourceRuleListRequest proto.InternalMessageInfo

type GetRecallSourceRuleListResponse struct {
	Rules                []*RecallSourceRule `protobuf:"bytes,1,rep,name=rules,proto3" json:"rules,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetRecallSourceRuleListResponse) Reset()         { *m = GetRecallSourceRuleListResponse{} }
func (m *GetRecallSourceRuleListResponse) String() string { return proto.CompactTextString(m) }
func (*GetRecallSourceRuleListResponse) ProtoMessage()    {}
func (*GetRecallSourceRuleListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{42}
}
func (m *GetRecallSourceRuleListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecallSourceRuleListResponse.Unmarshal(m, b)
}
func (m *GetRecallSourceRuleListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecallSourceRuleListResponse.Marshal(b, m, deterministic)
}
func (dst *GetRecallSourceRuleListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecallSourceRuleListResponse.Merge(dst, src)
}
func (m *GetRecallSourceRuleListResponse) XXX_Size() int {
	return xxx_messageInfo_GetRecallSourceRuleListResponse.Size(m)
}
func (m *GetRecallSourceRuleListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecallSourceRuleListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecallSourceRuleListResponse proto.InternalMessageInfo

func (m *GetRecallSourceRuleListResponse) GetRules() []*RecallSourceRule {
	if m != nil {
		return m.Rules
	}
	return nil
}

type GetDefaultSortParameterTableRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDefaultSortParameterTableRequest) Reset()         { *m = GetDefaultSortParameterTableRequest{} }
func (m *GetDefaultSortParameterTableRequest) String() string { return proto.CompactTextString(m) }
func (*GetDefaultSortParameterTableRequest) ProtoMessage()    {}
func (*GetDefaultSortParameterTableRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{43}
}
func (m *GetDefaultSortParameterTableRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDefaultSortParameterTableRequest.Unmarshal(m, b)
}
func (m *GetDefaultSortParameterTableRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDefaultSortParameterTableRequest.Marshal(b, m, deterministic)
}
func (dst *GetDefaultSortParameterTableRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDefaultSortParameterTableRequest.Merge(dst, src)
}
func (m *GetDefaultSortParameterTableRequest) XXX_Size() int {
	return xxx_messageInfo_GetDefaultSortParameterTableRequest.Size(m)
}
func (m *GetDefaultSortParameterTableRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDefaultSortParameterTableRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetDefaultSortParameterTableRequest proto.InternalMessageInfo

type GetDefaultSortParameterTableResponse struct {
	SortParameterTable   *SortParameterTable `protobuf:"bytes,1,opt,name=sort_parameter_table,json=sortParameterTable,proto3" json:"sort_parameter_table,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetDefaultSortParameterTableResponse) Reset()         { *m = GetDefaultSortParameterTableResponse{} }
func (m *GetDefaultSortParameterTableResponse) String() string { return proto.CompactTextString(m) }
func (*GetDefaultSortParameterTableResponse) ProtoMessage()    {}
func (*GetDefaultSortParameterTableResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{44}
}
func (m *GetDefaultSortParameterTableResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDefaultSortParameterTableResponse.Unmarshal(m, b)
}
func (m *GetDefaultSortParameterTableResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDefaultSortParameterTableResponse.Marshal(b, m, deterministic)
}
func (dst *GetDefaultSortParameterTableResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDefaultSortParameterTableResponse.Merge(dst, src)
}
func (m *GetDefaultSortParameterTableResponse) XXX_Size() int {
	return xxx_messageInfo_GetDefaultSortParameterTableResponse.Size(m)
}
func (m *GetDefaultSortParameterTableResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDefaultSortParameterTableResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetDefaultSortParameterTableResponse proto.InternalMessageInfo

func (m *GetDefaultSortParameterTableResponse) GetSortParameterTable() *SortParameterTable {
	if m != nil {
		return m.SortParameterTable
	}
	return nil
}

type UpdateDefaultSortParameterTableRequest struct {
	SortParameterTable   *SortParameterTable `protobuf:"bytes,1,opt,name=sort_parameter_table,json=sortParameterTable,proto3" json:"sort_parameter_table,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *UpdateDefaultSortParameterTableRequest) Reset() {
	*m = UpdateDefaultSortParameterTableRequest{}
}
func (m *UpdateDefaultSortParameterTableRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateDefaultSortParameterTableRequest) ProtoMessage()    {}
func (*UpdateDefaultSortParameterTableRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{45}
}
func (m *UpdateDefaultSortParameterTableRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateDefaultSortParameterTableRequest.Unmarshal(m, b)
}
func (m *UpdateDefaultSortParameterTableRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateDefaultSortParameterTableRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateDefaultSortParameterTableRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateDefaultSortParameterTableRequest.Merge(dst, src)
}
func (m *UpdateDefaultSortParameterTableRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateDefaultSortParameterTableRequest.Size(m)
}
func (m *UpdateDefaultSortParameterTableRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateDefaultSortParameterTableRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateDefaultSortParameterTableRequest proto.InternalMessageInfo

func (m *UpdateDefaultSortParameterTableRequest) GetSortParameterTable() *SortParameterTable {
	if m != nil {
		return m.SortParameterTable
	}
	return nil
}

type UpdateDefaultSortParameterTableResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateDefaultSortParameterTableResponse) Reset() {
	*m = UpdateDefaultSortParameterTableResponse{}
}
func (m *UpdateDefaultSortParameterTableResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateDefaultSortParameterTableResponse) ProtoMessage()    {}
func (*UpdateDefaultSortParameterTableResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{46}
}
func (m *UpdateDefaultSortParameterTableResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateDefaultSortParameterTableResponse.Unmarshal(m, b)
}
func (m *UpdateDefaultSortParameterTableResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateDefaultSortParameterTableResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateDefaultSortParameterTableResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateDefaultSortParameterTableResponse.Merge(dst, src)
}
func (m *UpdateDefaultSortParameterTableResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateDefaultSortParameterTableResponse.Size(m)
}
func (m *UpdateDefaultSortParameterTableResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateDefaultSortParameterTableResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateDefaultSortParameterTableResponse proto.InternalMessageInfo

type GetRecallSourceDataRequest struct {
	RecallSourceId       uint32   `protobuf:"varint,1,opt,name=recall_source_id,json=recallSourceId,proto3" json:"recall_source_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecallSourceDataRequest) Reset()         { *m = GetRecallSourceDataRequest{} }
func (m *GetRecallSourceDataRequest) String() string { return proto.CompactTextString(m) }
func (*GetRecallSourceDataRequest) ProtoMessage()    {}
func (*GetRecallSourceDataRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{47}
}
func (m *GetRecallSourceDataRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecallSourceDataRequest.Unmarshal(m, b)
}
func (m *GetRecallSourceDataRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecallSourceDataRequest.Marshal(b, m, deterministic)
}
func (dst *GetRecallSourceDataRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecallSourceDataRequest.Merge(dst, src)
}
func (m *GetRecallSourceDataRequest) XXX_Size() int {
	return xxx_messageInfo_GetRecallSourceDataRequest.Size(m)
}
func (m *GetRecallSourceDataRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecallSourceDataRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecallSourceDataRequest proto.InternalMessageInfo

func (m *GetRecallSourceDataRequest) GetRecallSourceId() uint32 {
	if m != nil {
		return m.RecallSourceId
	}
	return 0
}

type GetRecallSourceDataResponse struct {
	SkillProductList     []*EsportRcmdSkillProduct `protobuf:"bytes,1,rep,name=skill_product_list,json=skillProductList,proto3" json:"skill_product_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetRecallSourceDataResponse) Reset()         { *m = GetRecallSourceDataResponse{} }
func (m *GetRecallSourceDataResponse) String() string { return proto.CompactTextString(m) }
func (*GetRecallSourceDataResponse) ProtoMessage()    {}
func (*GetRecallSourceDataResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{48}
}
func (m *GetRecallSourceDataResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecallSourceDataResponse.Unmarshal(m, b)
}
func (m *GetRecallSourceDataResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecallSourceDataResponse.Marshal(b, m, deterministic)
}
func (dst *GetRecallSourceDataResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecallSourceDataResponse.Merge(dst, src)
}
func (m *GetRecallSourceDataResponse) XXX_Size() int {
	return xxx_messageInfo_GetRecallSourceDataResponse.Size(m)
}
func (m *GetRecallSourceDataResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecallSourceDataResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecallSourceDataResponse proto.InternalMessageInfo

func (m *GetRecallSourceDataResponse) GetSkillProductList() []*EsportRcmdSkillProduct {
	if m != nil {
		return m.SkillProductList
	}
	return nil
}

type GetNewRcmdSwitchRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SceneType            uint32   `protobuf:"varint,2,opt,name=scene_type,json=sceneType,proto3" json:"scene_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNewRcmdSwitchRequest) Reset()         { *m = GetNewRcmdSwitchRequest{} }
func (m *GetNewRcmdSwitchRequest) String() string { return proto.CompactTextString(m) }
func (*GetNewRcmdSwitchRequest) ProtoMessage()    {}
func (*GetNewRcmdSwitchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{49}
}
func (m *GetNewRcmdSwitchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewRcmdSwitchRequest.Unmarshal(m, b)
}
func (m *GetNewRcmdSwitchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewRcmdSwitchRequest.Marshal(b, m, deterministic)
}
func (dst *GetNewRcmdSwitchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewRcmdSwitchRequest.Merge(dst, src)
}
func (m *GetNewRcmdSwitchRequest) XXX_Size() int {
	return xxx_messageInfo_GetNewRcmdSwitchRequest.Size(m)
}
func (m *GetNewRcmdSwitchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewRcmdSwitchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewRcmdSwitchRequest proto.InternalMessageInfo

func (m *GetNewRcmdSwitchRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetNewRcmdSwitchRequest) GetSceneType() uint32 {
	if m != nil {
		return m.SceneType
	}
	return 0
}

type GetNewRcmdSwitchResponse struct {
	NewRcmdSwitch        bool     `protobuf:"varint,1,opt,name=new_rcmd_switch,json=newRcmdSwitch,proto3" json:"new_rcmd_switch,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNewRcmdSwitchResponse) Reset()         { *m = GetNewRcmdSwitchResponse{} }
func (m *GetNewRcmdSwitchResponse) String() string { return proto.CompactTextString(m) }
func (*GetNewRcmdSwitchResponse) ProtoMessage()    {}
func (*GetNewRcmdSwitchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{50}
}
func (m *GetNewRcmdSwitchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewRcmdSwitchResponse.Unmarshal(m, b)
}
func (m *GetNewRcmdSwitchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewRcmdSwitchResponse.Marshal(b, m, deterministic)
}
func (dst *GetNewRcmdSwitchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewRcmdSwitchResponse.Merge(dst, src)
}
func (m *GetNewRcmdSwitchResponse) XXX_Size() int {
	return xxx_messageInfo_GetNewRcmdSwitchResponse.Size(m)
}
func (m *GetNewRcmdSwitchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewRcmdSwitchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewRcmdSwitchResponse proto.InternalMessageInfo

func (m *GetNewRcmdSwitchResponse) GetNewRcmdSwitch() bool {
	if m != nil {
		return m.NewRcmdSwitch
	}
	return false
}

type AddSortParameterTableItemRequest struct {
	SortParameterTable   *SortParameterTable `protobuf:"bytes,1,opt,name=sort_parameter_table,json=sortParameterTable,proto3" json:"sort_parameter_table,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *AddSortParameterTableItemRequest) Reset()         { *m = AddSortParameterTableItemRequest{} }
func (m *AddSortParameterTableItemRequest) String() string { return proto.CompactTextString(m) }
func (*AddSortParameterTableItemRequest) ProtoMessage()    {}
func (*AddSortParameterTableItemRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{51}
}
func (m *AddSortParameterTableItemRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddSortParameterTableItemRequest.Unmarshal(m, b)
}
func (m *AddSortParameterTableItemRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddSortParameterTableItemRequest.Marshal(b, m, deterministic)
}
func (dst *AddSortParameterTableItemRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddSortParameterTableItemRequest.Merge(dst, src)
}
func (m *AddSortParameterTableItemRequest) XXX_Size() int {
	return xxx_messageInfo_AddSortParameterTableItemRequest.Size(m)
}
func (m *AddSortParameterTableItemRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddSortParameterTableItemRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddSortParameterTableItemRequest proto.InternalMessageInfo

func (m *AddSortParameterTableItemRequest) GetSortParameterTable() *SortParameterTable {
	if m != nil {
		return m.SortParameterTable
	}
	return nil
}

type AddSortParameterTableItemResponse struct {
	Count                uint32   `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddSortParameterTableItemResponse) Reset()         { *m = AddSortParameterTableItemResponse{} }
func (m *AddSortParameterTableItemResponse) String() string { return proto.CompactTextString(m) }
func (*AddSortParameterTableItemResponse) ProtoMessage()    {}
func (*AddSortParameterTableItemResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{52}
}
func (m *AddSortParameterTableItemResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddSortParameterTableItemResponse.Unmarshal(m, b)
}
func (m *AddSortParameterTableItemResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddSortParameterTableItemResponse.Marshal(b, m, deterministic)
}
func (dst *AddSortParameterTableItemResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddSortParameterTableItemResponse.Merge(dst, src)
}
func (m *AddSortParameterTableItemResponse) XXX_Size() int {
	return xxx_messageInfo_AddSortParameterTableItemResponse.Size(m)
}
func (m *AddSortParameterTableItemResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddSortParameterTableItemResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddSortParameterTableItemResponse proto.InternalMessageInfo

func (m *AddSortParameterTableItemResponse) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type BatGetCoachChannelInfoRequest struct {
	CoachList            []uint32 `protobuf:"varint,1,rep,packed,name=coach_list,json=coachList,proto3" json:"coach_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetCoachChannelInfoRequest) Reset()         { *m = BatGetCoachChannelInfoRequest{} }
func (m *BatGetCoachChannelInfoRequest) String() string { return proto.CompactTextString(m) }
func (*BatGetCoachChannelInfoRequest) ProtoMessage()    {}
func (*BatGetCoachChannelInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{53}
}
func (m *BatGetCoachChannelInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetCoachChannelInfoRequest.Unmarshal(m, b)
}
func (m *BatGetCoachChannelInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetCoachChannelInfoRequest.Marshal(b, m, deterministic)
}
func (dst *BatGetCoachChannelInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetCoachChannelInfoRequest.Merge(dst, src)
}
func (m *BatGetCoachChannelInfoRequest) XXX_Size() int {
	return xxx_messageInfo_BatGetCoachChannelInfoRequest.Size(m)
}
func (m *BatGetCoachChannelInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetCoachChannelInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetCoachChannelInfoRequest proto.InternalMessageInfo

func (m *BatGetCoachChannelInfoRequest) GetCoachList() []uint32 {
	if m != nil {
		return m.CoachList
	}
	return nil
}

type BatGetCoachChannelInfoResponse struct {
	CoachMap             map[uint32]*BatGetCoachChannelInfoResponse_CoachChannelInfo `protobuf:"bytes,1,rep,name=coach_map,json=coachMap,proto3" json:"coach_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                                                    `json:"-"`
	XXX_unrecognized     []byte                                                      `json:"-"`
	XXX_sizecache        int32                                                       `json:"-"`
}

func (m *BatGetCoachChannelInfoResponse) Reset()         { *m = BatGetCoachChannelInfoResponse{} }
func (m *BatGetCoachChannelInfoResponse) String() string { return proto.CompactTextString(m) }
func (*BatGetCoachChannelInfoResponse) ProtoMessage()    {}
func (*BatGetCoachChannelInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{54}
}
func (m *BatGetCoachChannelInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetCoachChannelInfoResponse.Unmarshal(m, b)
}
func (m *BatGetCoachChannelInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetCoachChannelInfoResponse.Marshal(b, m, deterministic)
}
func (dst *BatGetCoachChannelInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetCoachChannelInfoResponse.Merge(dst, src)
}
func (m *BatGetCoachChannelInfoResponse) XXX_Size() int {
	return xxx_messageInfo_BatGetCoachChannelInfoResponse.Size(m)
}
func (m *BatGetCoachChannelInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetCoachChannelInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetCoachChannelInfoResponse proto.InternalMessageInfo

func (m *BatGetCoachChannelInfoResponse) GetCoachMap() map[uint32]*BatGetCoachChannelInfoResponse_CoachChannelInfo {
	if m != nil {
		return m.CoachMap
	}
	return nil
}

type BatGetCoachChannelInfoResponse_CoachChannelInfo struct {
	CoachUid             uint32   `protobuf:"varint,1,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	OnMic                bool     `protobuf:"varint,3,opt,name=on_mic,json=onMic,proto3" json:"on_mic,omitempty"`
	Locked               bool     `protobuf:"varint,4,opt,name=locked,proto3" json:"locked,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetCoachChannelInfoResponse_CoachChannelInfo) Reset() {
	*m = BatGetCoachChannelInfoResponse_CoachChannelInfo{}
}
func (m *BatGetCoachChannelInfoResponse_CoachChannelInfo) String() string {
	return proto.CompactTextString(m)
}
func (*BatGetCoachChannelInfoResponse_CoachChannelInfo) ProtoMessage() {}
func (*BatGetCoachChannelInfoResponse_CoachChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac, []int{54, 0}
}
func (m *BatGetCoachChannelInfoResponse_CoachChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetCoachChannelInfoResponse_CoachChannelInfo.Unmarshal(m, b)
}
func (m *BatGetCoachChannelInfoResponse_CoachChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetCoachChannelInfoResponse_CoachChannelInfo.Marshal(b, m, deterministic)
}
func (dst *BatGetCoachChannelInfoResponse_CoachChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetCoachChannelInfoResponse_CoachChannelInfo.Merge(dst, src)
}
func (m *BatGetCoachChannelInfoResponse_CoachChannelInfo) XXX_Size() int {
	return xxx_messageInfo_BatGetCoachChannelInfoResponse_CoachChannelInfo.Size(m)
}
func (m *BatGetCoachChannelInfoResponse_CoachChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetCoachChannelInfoResponse_CoachChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetCoachChannelInfoResponse_CoachChannelInfo proto.InternalMessageInfo

func (m *BatGetCoachChannelInfoResponse_CoachChannelInfo) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

func (m *BatGetCoachChannelInfoResponse_CoachChannelInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BatGetCoachChannelInfoResponse_CoachChannelInfo) GetOnMic() bool {
	if m != nil {
		return m.OnMic
	}
	return false
}

func (m *BatGetCoachChannelInfoResponse_CoachChannelInfo) GetLocked() bool {
	if m != nil {
		return m.Locked
	}
	return false
}

func init() {
	proto.RegisterType((*GetEsportRcmdSkillProductReq)(nil), "esport_rcmd.GetEsportRcmdSkillProductReq")
	proto.RegisterType((*EsportRcmdSkillProduct)(nil), "esport_rcmd.EsportRcmdSkillProduct")
	proto.RegisterType((*GetEsportRcmdSkillProductResp)(nil), "esport_rcmd.GetEsportRcmdSkillProductResp")
	proto.RegisterType((*Strategy)(nil), "esport_rcmd.Strategy")
	proto.RegisterType((*Strategy_RecallSource)(nil), "esport_rcmd.Strategy.RecallSource")
	proto.RegisterType((*Strategy_AbTestSetting)(nil), "esport_rcmd.Strategy.AbTestSetting")
	proto.RegisterType((*Strategy_UserGroupSetting)(nil), "esport_rcmd.Strategy.UserGroupSetting")
	proto.RegisterType((*FilterRule)(nil), "esport_rcmd.FilterRule")
	proto.RegisterType((*Scene)(nil), "esport_rcmd.Scene")
	proto.RegisterType((*RecallSource)(nil), "esport_rcmd.RecallSource")
	proto.RegisterType((*RecallSource_FilterRuleValue)(nil), "esport_rcmd.RecallSource.FilterRuleValue")
	proto.RegisterType((*RecallSourceRuleSetting)(nil), "esport_rcmd.RecallSourceRuleSetting")
	proto.RegisterType((*RecallSourceRuleSetting_Condition)(nil), "esport_rcmd.RecallSourceRuleSetting.Condition")
	proto.RegisterType((*RecallSourceRuleSetting_Set)(nil), "esport_rcmd.RecallSourceRuleSetting.Set")
	proto.RegisterType((*RecallSourceRule)(nil), "esport_rcmd.RecallSourceRule")
	proto.RegisterType((*RecallSourceRule_Operator)(nil), "esport_rcmd.RecallSourceRule.Operator")
	proto.RegisterType((*SortParameterTable)(nil), "esport_rcmd.SortParameterTable")
	proto.RegisterType((*SortParameterTable_Item)(nil), "esport_rcmd.SortParameterTable.Item")
	proto.RegisterType((*SortParameterTable_SortParameter)(nil), "esport_rcmd.SortParameterTable.SortParameter")
	proto.RegisterType((*RecallSourceQueueRule)(nil), "esport_rcmd.RecallSourceQueueRule")
	proto.RegisterType((*GetRecallSourceQueueRuleRequest)(nil), "esport_rcmd.GetRecallSourceQueueRuleRequest")
	proto.RegisterType((*GetRecallSourceQueueRuleResponse)(nil), "esport_rcmd.GetRecallSourceQueueRuleResponse")
	proto.RegisterType((*ListStrategiesRequest)(nil), "esport_rcmd.ListStrategiesRequest")
	proto.RegisterType((*ListStrategiesResponse)(nil), "esport_rcmd.ListStrategiesResponse")
	proto.RegisterType((*GetStrategyRequest)(nil), "esport_rcmd.GetStrategyRequest")
	proto.RegisterType((*GetStrategyResponse)(nil), "esport_rcmd.GetStrategyResponse")
	proto.RegisterType((*CreateStrategyRequest)(nil), "esport_rcmd.CreateStrategyRequest")
	proto.RegisterType((*CreateStrategyResponse)(nil), "esport_rcmd.CreateStrategyResponse")
	proto.RegisterType((*UpdateStrategyRequest)(nil), "esport_rcmd.UpdateStrategyRequest")
	proto.RegisterType((*UpdateStrategyResponse)(nil), "esport_rcmd.UpdateStrategyResponse")
	proto.RegisterType((*PauseStrategyRequest)(nil), "esport_rcmd.PauseStrategyRequest")
	proto.RegisterType((*PauseStrategyResponse)(nil), "esport_rcmd.PauseStrategyResponse")
	proto.RegisterType((*StartStrategyRequest)(nil), "esport_rcmd.StartStrategyRequest")
	proto.RegisterType((*StartStrategyResponse)(nil), "esport_rcmd.StartStrategyResponse")
	proto.RegisterType((*DeleteStrategyRequest)(nil), "esport_rcmd.DeleteStrategyRequest")
	proto.RegisterType((*DeleteStrategyResponse)(nil), "esport_rcmd.DeleteStrategyResponse")
	proto.RegisterType((*GetFilterRulesRequest)(nil), "esport_rcmd.GetFilterRulesRequest")
	proto.RegisterType((*GetFilterRulesResponse)(nil), "esport_rcmd.GetFilterRulesResponse")
	proto.RegisterType((*GetScenesRequest)(nil), "esport_rcmd.GetScenesRequest")
	proto.RegisterType((*GetScenesResponse)(nil), "esport_rcmd.GetScenesResponse")
	proto.RegisterType((*ListRecallSourcesRequest)(nil), "esport_rcmd.ListRecallSourcesRequest")
	proto.RegisterType((*ListRecallSourcesResponse)(nil), "esport_rcmd.ListRecallSourcesResponse")
	proto.RegisterType((*GetRecallSourceDetailsRequest)(nil), "esport_rcmd.GetRecallSourceDetailsRequest")
	proto.RegisterType((*GetRecallSourceDetailsResponse)(nil), "esport_rcmd.GetRecallSourceDetailsResponse")
	proto.RegisterType((*CreateRecallSourceRequest)(nil), "esport_rcmd.CreateRecallSourceRequest")
	proto.RegisterType((*CreateRecallSourceResponse)(nil), "esport_rcmd.CreateRecallSourceResponse")
	proto.RegisterType((*UpdateRecallSourceRequest)(nil), "esport_rcmd.UpdateRecallSourceRequest")
	proto.RegisterType((*UpdateRecallSourceResponse)(nil), "esport_rcmd.UpdateRecallSourceResponse")
	proto.RegisterType((*DeleteRecallSourceRequest)(nil), "esport_rcmd.DeleteRecallSourceRequest")
	proto.RegisterType((*DeleteRecallSourceResponse)(nil), "esport_rcmd.DeleteRecallSourceResponse")
	proto.RegisterType((*GetRecallSourceRuleListRequest)(nil), "esport_rcmd.GetRecallSourceRuleListRequest")
	proto.RegisterType((*GetRecallSourceRuleListResponse)(nil), "esport_rcmd.GetRecallSourceRuleListResponse")
	proto.RegisterType((*GetDefaultSortParameterTableRequest)(nil), "esport_rcmd.GetDefaultSortParameterTableRequest")
	proto.RegisterType((*GetDefaultSortParameterTableResponse)(nil), "esport_rcmd.GetDefaultSortParameterTableResponse")
	proto.RegisterType((*UpdateDefaultSortParameterTableRequest)(nil), "esport_rcmd.UpdateDefaultSortParameterTableRequest")
	proto.RegisterType((*UpdateDefaultSortParameterTableResponse)(nil), "esport_rcmd.UpdateDefaultSortParameterTableResponse")
	proto.RegisterType((*GetRecallSourceDataRequest)(nil), "esport_rcmd.GetRecallSourceDataRequest")
	proto.RegisterType((*GetRecallSourceDataResponse)(nil), "esport_rcmd.GetRecallSourceDataResponse")
	proto.RegisterType((*GetNewRcmdSwitchRequest)(nil), "esport_rcmd.GetNewRcmdSwitchRequest")
	proto.RegisterType((*GetNewRcmdSwitchResponse)(nil), "esport_rcmd.GetNewRcmdSwitchResponse")
	proto.RegisterType((*AddSortParameterTableItemRequest)(nil), "esport_rcmd.AddSortParameterTableItemRequest")
	proto.RegisterType((*AddSortParameterTableItemResponse)(nil), "esport_rcmd.AddSortParameterTableItemResponse")
	proto.RegisterType((*BatGetCoachChannelInfoRequest)(nil), "esport_rcmd.BatGetCoachChannelInfoRequest")
	proto.RegisterType((*BatGetCoachChannelInfoResponse)(nil), "esport_rcmd.BatGetCoachChannelInfoResponse")
	proto.RegisterMapType((map[uint32]*BatGetCoachChannelInfoResponse_CoachChannelInfo)(nil), "esport_rcmd.BatGetCoachChannelInfoResponse.CoachMapEntry")
	proto.RegisterType((*BatGetCoachChannelInfoResponse_CoachChannelInfo)(nil), "esport_rcmd.BatGetCoachChannelInfoResponse.CoachChannelInfo")
	proto.RegisterEnum("esport_rcmd.SceneType", SceneType_name, SceneType_value)
	proto.RegisterEnum("esport_rcmd.MultilevelSortType", MultilevelSortType_name, MultilevelSortType_value)
	proto.RegisterEnum("esport_rcmd.FilterType", FilterType_name, FilterType_value)
	proto.RegisterEnum("esport_rcmd.RcmdTag", RcmdTag_name, RcmdTag_value)
	proto.RegisterEnum("esport_rcmd.RcmdTagType", RcmdTagType_name, RcmdTagType_value)
	proto.RegisterEnum("esport_rcmd.RcmdTagCompareType", RcmdTagCompareType_name, RcmdTagCompareType_value)
	proto.RegisterEnum("esport_rcmd.StrategyStatus", StrategyStatus_name, StrategyStatus_value)
	proto.RegisterEnum("esport_rcmd.DelimitType", DelimitType_name, DelimitType_value)
	proto.RegisterEnum("esport_rcmd.RecallSourceRuleType", RecallSourceRuleType_name, RecallSourceRuleType_value)
	proto.RegisterEnum("esport_rcmd.RecallSourceRuleOperatorType", RecallSourceRuleOperatorType_name, RecallSourceRuleOperatorType_value)
	proto.RegisterEnum("esport_rcmd.SortParameterType", SortParameterType_name, SortParameterType_value)
	proto.RegisterEnum("esport_rcmd.RecallSource_SortParameterType", RecallSource_SortParameterType_name, RecallSource_SortParameterType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// EsportRcmdServiceClient is the client API for EsportRcmdService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type EsportRcmdServiceClient interface {
	// 获取电竞推荐数据
	GetEsportRcmdSkillProduct(ctx context.Context, in *GetEsportRcmdSkillProductReq, opts ...grpc.CallOption) (*GetEsportRcmdSkillProductResp, error)
	// 获取召回源数据
	GetRecallSourceData(ctx context.Context, in *GetRecallSourceDataRequest, opts ...grpc.CallOption) (*GetRecallSourceDataResponse, error)
	// 批量获取大神房间信息
	BatGetCoachChannelInfo(ctx context.Context, in *BatGetCoachChannelInfoRequest, opts ...grpc.CallOption) (*BatGetCoachChannelInfoResponse, error)
	// =====================运营后台=====================
	// 列出策略列表
	ListStrategies(ctx context.Context, in *ListStrategiesRequest, opts ...grpc.CallOption) (*ListStrategiesResponse, error)
	// 获取特定策略的详细信息
	GetStrategy(ctx context.Context, in *GetStrategyRequest, opts ...grpc.CallOption) (*GetStrategyResponse, error)
	// 创建新策略
	CreateStrategy(ctx context.Context, in *CreateStrategyRequest, opts ...grpc.CallOption) (*CreateStrategyResponse, error)
	// 更新现有策略
	UpdateStrategy(ctx context.Context, in *UpdateStrategyRequest, opts ...grpc.CallOption) (*UpdateStrategyResponse, error)
	// 暂停策略
	PauseStrategy(ctx context.Context, in *PauseStrategyRequest, opts ...grpc.CallOption) (*PauseStrategyResponse, error)
	// 启动策略
	StartStrategy(ctx context.Context, in *StartStrategyRequest, opts ...grpc.CallOption) (*StartStrategyResponse, error)
	// 删除策略
	DeleteStrategy(ctx context.Context, in *DeleteStrategyRequest, opts ...grpc.CallOption) (*DeleteStrategyResponse, error)
	// 获取过滤规则
	GetFilterRules(ctx context.Context, in *GetFilterRulesRequest, opts ...grpc.CallOption) (*GetFilterRulesResponse, error)
	// 获取场景列表
	GetScenes(ctx context.Context, in *GetScenesRequest, opts ...grpc.CallOption) (*GetScenesResponse, error)
	// 列出所有召回源
	ListRecallSources(ctx context.Context, in *ListRecallSourcesRequest, opts ...grpc.CallOption) (*ListRecallSourcesResponse, error)
	// 获取某个召回源的详细信息
	GetRecallSourceDetails(ctx context.Context, in *GetRecallSourceDetailsRequest, opts ...grpc.CallOption) (*GetRecallSourceDetailsResponse, error)
	// 新建召回源
	CreateRecallSource(ctx context.Context, in *CreateRecallSourceRequest, opts ...grpc.CallOption) (*CreateRecallSourceResponse, error)
	// 编辑召回源
	UpdateRecallSource(ctx context.Context, in *UpdateRecallSourceRequest, opts ...grpc.CallOption) (*UpdateRecallSourceResponse, error)
	// 删除召回源
	DeleteRecallSource(ctx context.Context, in *DeleteRecallSourceRequest, opts ...grpc.CallOption) (*DeleteRecallSourceResponse, error)
	// 获取召回源规则列表
	GetRecallSourceRuleList(ctx context.Context, in *GetRecallSourceRuleListRequest, opts ...grpc.CallOption) (*GetRecallSourceRuleListResponse, error)
	// 获取默认的排序参数表
	GetDefaultSortParameterTable(ctx context.Context, in *GetDefaultSortParameterTableRequest, opts ...grpc.CallOption) (*GetDefaultSortParameterTableResponse, error)
	// 更新默认的排序参数表
	UpdateDefaultSortParameterTable(ctx context.Context, in *UpdateDefaultSortParameterTableRequest, opts ...grpc.CallOption) (*UpdateDefaultSortParameterTableResponse, error)
	// 获取多级排序的规则列表
	GetRecallSourceQueueRule(ctx context.Context, in *GetRecallSourceQueueRuleRequest, opts ...grpc.CallOption) (*GetRecallSourceQueueRuleResponse, error)
	// 获取新推荐接口开关
	GetNewRcmdSwitch(ctx context.Context, in *GetNewRcmdSwitchRequest, opts ...grpc.CallOption) (*GetNewRcmdSwitchResponse, error)
	// 添加排序参数表项，这是个内部的接口，会添加到全局的排序参数表，并同步到其他自定义的排序参数表
	AddSortParameterTableItem(ctx context.Context, in *AddSortParameterTableItemRequest, opts ...grpc.CallOption) (*AddSortParameterTableItemResponse, error)
}

type esportRcmdServiceClient struct {
	cc *grpc.ClientConn
}

func NewEsportRcmdServiceClient(cc *grpc.ClientConn) EsportRcmdServiceClient {
	return &esportRcmdServiceClient{cc}
}

func (c *esportRcmdServiceClient) GetEsportRcmdSkillProduct(ctx context.Context, in *GetEsportRcmdSkillProductReq, opts ...grpc.CallOption) (*GetEsportRcmdSkillProductResp, error) {
	out := new(GetEsportRcmdSkillProductResp)
	err := c.cc.Invoke(ctx, "/esport_rcmd.EsportRcmdService/GetEsportRcmdSkillProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdServiceClient) GetRecallSourceData(ctx context.Context, in *GetRecallSourceDataRequest, opts ...grpc.CallOption) (*GetRecallSourceDataResponse, error) {
	out := new(GetRecallSourceDataResponse)
	err := c.cc.Invoke(ctx, "/esport_rcmd.EsportRcmdService/GetRecallSourceData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdServiceClient) BatGetCoachChannelInfo(ctx context.Context, in *BatGetCoachChannelInfoRequest, opts ...grpc.CallOption) (*BatGetCoachChannelInfoResponse, error) {
	out := new(BatGetCoachChannelInfoResponse)
	err := c.cc.Invoke(ctx, "/esport_rcmd.EsportRcmdService/BatGetCoachChannelInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdServiceClient) ListStrategies(ctx context.Context, in *ListStrategiesRequest, opts ...grpc.CallOption) (*ListStrategiesResponse, error) {
	out := new(ListStrategiesResponse)
	err := c.cc.Invoke(ctx, "/esport_rcmd.EsportRcmdService/ListStrategies", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdServiceClient) GetStrategy(ctx context.Context, in *GetStrategyRequest, opts ...grpc.CallOption) (*GetStrategyResponse, error) {
	out := new(GetStrategyResponse)
	err := c.cc.Invoke(ctx, "/esport_rcmd.EsportRcmdService/GetStrategy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdServiceClient) CreateStrategy(ctx context.Context, in *CreateStrategyRequest, opts ...grpc.CallOption) (*CreateStrategyResponse, error) {
	out := new(CreateStrategyResponse)
	err := c.cc.Invoke(ctx, "/esport_rcmd.EsportRcmdService/CreateStrategy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdServiceClient) UpdateStrategy(ctx context.Context, in *UpdateStrategyRequest, opts ...grpc.CallOption) (*UpdateStrategyResponse, error) {
	out := new(UpdateStrategyResponse)
	err := c.cc.Invoke(ctx, "/esport_rcmd.EsportRcmdService/UpdateStrategy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdServiceClient) PauseStrategy(ctx context.Context, in *PauseStrategyRequest, opts ...grpc.CallOption) (*PauseStrategyResponse, error) {
	out := new(PauseStrategyResponse)
	err := c.cc.Invoke(ctx, "/esport_rcmd.EsportRcmdService/PauseStrategy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdServiceClient) StartStrategy(ctx context.Context, in *StartStrategyRequest, opts ...grpc.CallOption) (*StartStrategyResponse, error) {
	out := new(StartStrategyResponse)
	err := c.cc.Invoke(ctx, "/esport_rcmd.EsportRcmdService/StartStrategy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdServiceClient) DeleteStrategy(ctx context.Context, in *DeleteStrategyRequest, opts ...grpc.CallOption) (*DeleteStrategyResponse, error) {
	out := new(DeleteStrategyResponse)
	err := c.cc.Invoke(ctx, "/esport_rcmd.EsportRcmdService/DeleteStrategy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdServiceClient) GetFilterRules(ctx context.Context, in *GetFilterRulesRequest, opts ...grpc.CallOption) (*GetFilterRulesResponse, error) {
	out := new(GetFilterRulesResponse)
	err := c.cc.Invoke(ctx, "/esport_rcmd.EsportRcmdService/GetFilterRules", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdServiceClient) GetScenes(ctx context.Context, in *GetScenesRequest, opts ...grpc.CallOption) (*GetScenesResponse, error) {
	out := new(GetScenesResponse)
	err := c.cc.Invoke(ctx, "/esport_rcmd.EsportRcmdService/GetScenes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdServiceClient) ListRecallSources(ctx context.Context, in *ListRecallSourcesRequest, opts ...grpc.CallOption) (*ListRecallSourcesResponse, error) {
	out := new(ListRecallSourcesResponse)
	err := c.cc.Invoke(ctx, "/esport_rcmd.EsportRcmdService/ListRecallSources", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdServiceClient) GetRecallSourceDetails(ctx context.Context, in *GetRecallSourceDetailsRequest, opts ...grpc.CallOption) (*GetRecallSourceDetailsResponse, error) {
	out := new(GetRecallSourceDetailsResponse)
	err := c.cc.Invoke(ctx, "/esport_rcmd.EsportRcmdService/GetRecallSourceDetails", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdServiceClient) CreateRecallSource(ctx context.Context, in *CreateRecallSourceRequest, opts ...grpc.CallOption) (*CreateRecallSourceResponse, error) {
	out := new(CreateRecallSourceResponse)
	err := c.cc.Invoke(ctx, "/esport_rcmd.EsportRcmdService/CreateRecallSource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdServiceClient) UpdateRecallSource(ctx context.Context, in *UpdateRecallSourceRequest, opts ...grpc.CallOption) (*UpdateRecallSourceResponse, error) {
	out := new(UpdateRecallSourceResponse)
	err := c.cc.Invoke(ctx, "/esport_rcmd.EsportRcmdService/UpdateRecallSource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdServiceClient) DeleteRecallSource(ctx context.Context, in *DeleteRecallSourceRequest, opts ...grpc.CallOption) (*DeleteRecallSourceResponse, error) {
	out := new(DeleteRecallSourceResponse)
	err := c.cc.Invoke(ctx, "/esport_rcmd.EsportRcmdService/DeleteRecallSource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdServiceClient) GetRecallSourceRuleList(ctx context.Context, in *GetRecallSourceRuleListRequest, opts ...grpc.CallOption) (*GetRecallSourceRuleListResponse, error) {
	out := new(GetRecallSourceRuleListResponse)
	err := c.cc.Invoke(ctx, "/esport_rcmd.EsportRcmdService/GetRecallSourceRuleList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdServiceClient) GetDefaultSortParameterTable(ctx context.Context, in *GetDefaultSortParameterTableRequest, opts ...grpc.CallOption) (*GetDefaultSortParameterTableResponse, error) {
	out := new(GetDefaultSortParameterTableResponse)
	err := c.cc.Invoke(ctx, "/esport_rcmd.EsportRcmdService/GetDefaultSortParameterTable", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdServiceClient) UpdateDefaultSortParameterTable(ctx context.Context, in *UpdateDefaultSortParameterTableRequest, opts ...grpc.CallOption) (*UpdateDefaultSortParameterTableResponse, error) {
	out := new(UpdateDefaultSortParameterTableResponse)
	err := c.cc.Invoke(ctx, "/esport_rcmd.EsportRcmdService/UpdateDefaultSortParameterTable", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdServiceClient) GetRecallSourceQueueRule(ctx context.Context, in *GetRecallSourceQueueRuleRequest, opts ...grpc.CallOption) (*GetRecallSourceQueueRuleResponse, error) {
	out := new(GetRecallSourceQueueRuleResponse)
	err := c.cc.Invoke(ctx, "/esport_rcmd.EsportRcmdService/GetRecallSourceQueueRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdServiceClient) GetNewRcmdSwitch(ctx context.Context, in *GetNewRcmdSwitchRequest, opts ...grpc.CallOption) (*GetNewRcmdSwitchResponse, error) {
	out := new(GetNewRcmdSwitchResponse)
	err := c.cc.Invoke(ctx, "/esport_rcmd.EsportRcmdService/GetNewRcmdSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportRcmdServiceClient) AddSortParameterTableItem(ctx context.Context, in *AddSortParameterTableItemRequest, opts ...grpc.CallOption) (*AddSortParameterTableItemResponse, error) {
	out := new(AddSortParameterTableItemResponse)
	err := c.cc.Invoke(ctx, "/esport_rcmd.EsportRcmdService/AddSortParameterTableItem", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EsportRcmdServiceServer is the server API for EsportRcmdService service.
type EsportRcmdServiceServer interface {
	// 获取电竞推荐数据
	GetEsportRcmdSkillProduct(context.Context, *GetEsportRcmdSkillProductReq) (*GetEsportRcmdSkillProductResp, error)
	// 获取召回源数据
	GetRecallSourceData(context.Context, *GetRecallSourceDataRequest) (*GetRecallSourceDataResponse, error)
	// 批量获取大神房间信息
	BatGetCoachChannelInfo(context.Context, *BatGetCoachChannelInfoRequest) (*BatGetCoachChannelInfoResponse, error)
	// =====================运营后台=====================
	// 列出策略列表
	ListStrategies(context.Context, *ListStrategiesRequest) (*ListStrategiesResponse, error)
	// 获取特定策略的详细信息
	GetStrategy(context.Context, *GetStrategyRequest) (*GetStrategyResponse, error)
	// 创建新策略
	CreateStrategy(context.Context, *CreateStrategyRequest) (*CreateStrategyResponse, error)
	// 更新现有策略
	UpdateStrategy(context.Context, *UpdateStrategyRequest) (*UpdateStrategyResponse, error)
	// 暂停策略
	PauseStrategy(context.Context, *PauseStrategyRequest) (*PauseStrategyResponse, error)
	// 启动策略
	StartStrategy(context.Context, *StartStrategyRequest) (*StartStrategyResponse, error)
	// 删除策略
	DeleteStrategy(context.Context, *DeleteStrategyRequest) (*DeleteStrategyResponse, error)
	// 获取过滤规则
	GetFilterRules(context.Context, *GetFilterRulesRequest) (*GetFilterRulesResponse, error)
	// 获取场景列表
	GetScenes(context.Context, *GetScenesRequest) (*GetScenesResponse, error)
	// 列出所有召回源
	ListRecallSources(context.Context, *ListRecallSourcesRequest) (*ListRecallSourcesResponse, error)
	// 获取某个召回源的详细信息
	GetRecallSourceDetails(context.Context, *GetRecallSourceDetailsRequest) (*GetRecallSourceDetailsResponse, error)
	// 新建召回源
	CreateRecallSource(context.Context, *CreateRecallSourceRequest) (*CreateRecallSourceResponse, error)
	// 编辑召回源
	UpdateRecallSource(context.Context, *UpdateRecallSourceRequest) (*UpdateRecallSourceResponse, error)
	// 删除召回源
	DeleteRecallSource(context.Context, *DeleteRecallSourceRequest) (*DeleteRecallSourceResponse, error)
	// 获取召回源规则列表
	GetRecallSourceRuleList(context.Context, *GetRecallSourceRuleListRequest) (*GetRecallSourceRuleListResponse, error)
	// 获取默认的排序参数表
	GetDefaultSortParameterTable(context.Context, *GetDefaultSortParameterTableRequest) (*GetDefaultSortParameterTableResponse, error)
	// 更新默认的排序参数表
	UpdateDefaultSortParameterTable(context.Context, *UpdateDefaultSortParameterTableRequest) (*UpdateDefaultSortParameterTableResponse, error)
	// 获取多级排序的规则列表
	GetRecallSourceQueueRule(context.Context, *GetRecallSourceQueueRuleRequest) (*GetRecallSourceQueueRuleResponse, error)
	// 获取新推荐接口开关
	GetNewRcmdSwitch(context.Context, *GetNewRcmdSwitchRequest) (*GetNewRcmdSwitchResponse, error)
	// 添加排序参数表项，这是个内部的接口，会添加到全局的排序参数表，并同步到其他自定义的排序参数表
	AddSortParameterTableItem(context.Context, *AddSortParameterTableItemRequest) (*AddSortParameterTableItemResponse, error)
}

func RegisterEsportRcmdServiceServer(s *grpc.Server, srv EsportRcmdServiceServer) {
	s.RegisterService(&_EsportRcmdService_serviceDesc, srv)
}

func _EsportRcmdService_GetEsportRcmdSkillProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEsportRcmdSkillProductReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdServiceServer).GetEsportRcmdSkillProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_rcmd.EsportRcmdService/GetEsportRcmdSkillProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdServiceServer).GetEsportRcmdSkillProduct(ctx, req.(*GetEsportRcmdSkillProductReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdService_GetRecallSourceData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecallSourceDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdServiceServer).GetRecallSourceData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_rcmd.EsportRcmdService/GetRecallSourceData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdServiceServer).GetRecallSourceData(ctx, req.(*GetRecallSourceDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdService_BatGetCoachChannelInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetCoachChannelInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdServiceServer).BatGetCoachChannelInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_rcmd.EsportRcmdService/BatGetCoachChannelInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdServiceServer).BatGetCoachChannelInfo(ctx, req.(*BatGetCoachChannelInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdService_ListStrategies_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListStrategiesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdServiceServer).ListStrategies(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_rcmd.EsportRcmdService/ListStrategies",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdServiceServer).ListStrategies(ctx, req.(*ListStrategiesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdService_GetStrategy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStrategyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdServiceServer).GetStrategy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_rcmd.EsportRcmdService/GetStrategy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdServiceServer).GetStrategy(ctx, req.(*GetStrategyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdService_CreateStrategy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateStrategyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdServiceServer).CreateStrategy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_rcmd.EsportRcmdService/CreateStrategy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdServiceServer).CreateStrategy(ctx, req.(*CreateStrategyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdService_UpdateStrategy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateStrategyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdServiceServer).UpdateStrategy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_rcmd.EsportRcmdService/UpdateStrategy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdServiceServer).UpdateStrategy(ctx, req.(*UpdateStrategyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdService_PauseStrategy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PauseStrategyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdServiceServer).PauseStrategy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_rcmd.EsportRcmdService/PauseStrategy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdServiceServer).PauseStrategy(ctx, req.(*PauseStrategyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdService_StartStrategy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartStrategyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdServiceServer).StartStrategy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_rcmd.EsportRcmdService/StartStrategy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdServiceServer).StartStrategy(ctx, req.(*StartStrategyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdService_DeleteStrategy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteStrategyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdServiceServer).DeleteStrategy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_rcmd.EsportRcmdService/DeleteStrategy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdServiceServer).DeleteStrategy(ctx, req.(*DeleteStrategyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdService_GetFilterRules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFilterRulesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdServiceServer).GetFilterRules(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_rcmd.EsportRcmdService/GetFilterRules",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdServiceServer).GetFilterRules(ctx, req.(*GetFilterRulesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdService_GetScenes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetScenesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdServiceServer).GetScenes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_rcmd.EsportRcmdService/GetScenes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdServiceServer).GetScenes(ctx, req.(*GetScenesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdService_ListRecallSources_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRecallSourcesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdServiceServer).ListRecallSources(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_rcmd.EsportRcmdService/ListRecallSources",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdServiceServer).ListRecallSources(ctx, req.(*ListRecallSourcesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdService_GetRecallSourceDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecallSourceDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdServiceServer).GetRecallSourceDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_rcmd.EsportRcmdService/GetRecallSourceDetails",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdServiceServer).GetRecallSourceDetails(ctx, req.(*GetRecallSourceDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdService_CreateRecallSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRecallSourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdServiceServer).CreateRecallSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_rcmd.EsportRcmdService/CreateRecallSource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdServiceServer).CreateRecallSource(ctx, req.(*CreateRecallSourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdService_UpdateRecallSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRecallSourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdServiceServer).UpdateRecallSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_rcmd.EsportRcmdService/UpdateRecallSource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdServiceServer).UpdateRecallSource(ctx, req.(*UpdateRecallSourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdService_DeleteRecallSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteRecallSourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdServiceServer).DeleteRecallSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_rcmd.EsportRcmdService/DeleteRecallSource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdServiceServer).DeleteRecallSource(ctx, req.(*DeleteRecallSourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdService_GetRecallSourceRuleList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecallSourceRuleListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdServiceServer).GetRecallSourceRuleList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_rcmd.EsportRcmdService/GetRecallSourceRuleList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdServiceServer).GetRecallSourceRuleList(ctx, req.(*GetRecallSourceRuleListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdService_GetDefaultSortParameterTable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDefaultSortParameterTableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdServiceServer).GetDefaultSortParameterTable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_rcmd.EsportRcmdService/GetDefaultSortParameterTable",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdServiceServer).GetDefaultSortParameterTable(ctx, req.(*GetDefaultSortParameterTableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdService_UpdateDefaultSortParameterTable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDefaultSortParameterTableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdServiceServer).UpdateDefaultSortParameterTable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_rcmd.EsportRcmdService/UpdateDefaultSortParameterTable",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdServiceServer).UpdateDefaultSortParameterTable(ctx, req.(*UpdateDefaultSortParameterTableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdService_GetRecallSourceQueueRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecallSourceQueueRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdServiceServer).GetRecallSourceQueueRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_rcmd.EsportRcmdService/GetRecallSourceQueueRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdServiceServer).GetRecallSourceQueueRule(ctx, req.(*GetRecallSourceQueueRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdService_GetNewRcmdSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNewRcmdSwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdServiceServer).GetNewRcmdSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_rcmd.EsportRcmdService/GetNewRcmdSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdServiceServer).GetNewRcmdSwitch(ctx, req.(*GetNewRcmdSwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportRcmdService_AddSortParameterTableItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddSortParameterTableItemRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportRcmdServiceServer).AddSortParameterTableItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_rcmd.EsportRcmdService/AddSortParameterTableItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportRcmdServiceServer).AddSortParameterTableItem(ctx, req.(*AddSortParameterTableItemRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _EsportRcmdService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "esport_rcmd.EsportRcmdService",
	HandlerType: (*EsportRcmdServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetEsportRcmdSkillProduct",
			Handler:    _EsportRcmdService_GetEsportRcmdSkillProduct_Handler,
		},
		{
			MethodName: "GetRecallSourceData",
			Handler:    _EsportRcmdService_GetRecallSourceData_Handler,
		},
		{
			MethodName: "BatGetCoachChannelInfo",
			Handler:    _EsportRcmdService_BatGetCoachChannelInfo_Handler,
		},
		{
			MethodName: "ListStrategies",
			Handler:    _EsportRcmdService_ListStrategies_Handler,
		},
		{
			MethodName: "GetStrategy",
			Handler:    _EsportRcmdService_GetStrategy_Handler,
		},
		{
			MethodName: "CreateStrategy",
			Handler:    _EsportRcmdService_CreateStrategy_Handler,
		},
		{
			MethodName: "UpdateStrategy",
			Handler:    _EsportRcmdService_UpdateStrategy_Handler,
		},
		{
			MethodName: "PauseStrategy",
			Handler:    _EsportRcmdService_PauseStrategy_Handler,
		},
		{
			MethodName: "StartStrategy",
			Handler:    _EsportRcmdService_StartStrategy_Handler,
		},
		{
			MethodName: "DeleteStrategy",
			Handler:    _EsportRcmdService_DeleteStrategy_Handler,
		},
		{
			MethodName: "GetFilterRules",
			Handler:    _EsportRcmdService_GetFilterRules_Handler,
		},
		{
			MethodName: "GetScenes",
			Handler:    _EsportRcmdService_GetScenes_Handler,
		},
		{
			MethodName: "ListRecallSources",
			Handler:    _EsportRcmdService_ListRecallSources_Handler,
		},
		{
			MethodName: "GetRecallSourceDetails",
			Handler:    _EsportRcmdService_GetRecallSourceDetails_Handler,
		},
		{
			MethodName: "CreateRecallSource",
			Handler:    _EsportRcmdService_CreateRecallSource_Handler,
		},
		{
			MethodName: "UpdateRecallSource",
			Handler:    _EsportRcmdService_UpdateRecallSource_Handler,
		},
		{
			MethodName: "DeleteRecallSource",
			Handler:    _EsportRcmdService_DeleteRecallSource_Handler,
		},
		{
			MethodName: "GetRecallSourceRuleList",
			Handler:    _EsportRcmdService_GetRecallSourceRuleList_Handler,
		},
		{
			MethodName: "GetDefaultSortParameterTable",
			Handler:    _EsportRcmdService_GetDefaultSortParameterTable_Handler,
		},
		{
			MethodName: "UpdateDefaultSortParameterTable",
			Handler:    _EsportRcmdService_UpdateDefaultSortParameterTable_Handler,
		},
		{
			MethodName: "GetRecallSourceQueueRule",
			Handler:    _EsportRcmdService_GetRecallSourceQueueRule_Handler,
		},
		{
			MethodName: "GetNewRcmdSwitch",
			Handler:    _EsportRcmdService_GetNewRcmdSwitch_Handler,
		},
		{
			MethodName: "AddSortParameterTableItem",
			Handler:    _EsportRcmdService_AddSortParameterTableItem_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/esport-rcmd/esport-rcmd.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/esport-rcmd/esport-rcmd.proto", fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac)
}

var fileDescriptor_esport_rcmd_0d5c075ee7f1d2ac = []byte{
	// 3536 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x5a, 0x4f, 0x73, 0xdb, 0x48,
	0x76, 0x37, 0x48, 0xfd, 0xa1, 0x9e, 0x44, 0x19, 0xee, 0x91, 0x64, 0x0a, 0xfe, 0x23, 0x19, 0x96,
	0x65, 0x0d, 0x77, 0x46, 0x9e, 0xf5, 0x64, 0x37, 0x3b, 0x33, 0xc9, 0x24, 0x10, 0x05, 0xd1, 0xb0,
	0x29, 0x80, 0x6e, 0x80, 0xf2, 0x78, 0xf6, 0x80, 0xc0, 0x64, 0x5b, 0x46, 0xcc, 0x7f, 0x06, 0x40,
	0x7b, 0x35, 0x49, 0x4e, 0xa9, 0xad, 0xec, 0x21, 0x55, 0x39, 0xe4, 0x98, 0xaf, 0x91, 0x43, 0x2a,
	0xa9, 0x4a, 0xa5, 0xf2, 0x29, 0x72, 0xca, 0x27, 0xc8, 0x29, 0xd7, 0x54, 0xa5, 0x52, 0xdd, 0x68,
	0x82, 0x00, 0x08, 0x90, 0xf4, 0x66, 0xe6, 0xc6, 0xee, 0xfe, 0xf5, 0xef, 0xbd, 0xee, 0xf7, 0x5e,
	0x77, 0xe3, 0x3d, 0xc2, 0x67, 0x41, 0xf0, 0xe8, 0xdd, 0xc8, 0x6d, 0xbf, 0xf5, 0xdd, 0xee, 0x7b,
	0xe2, 0x3d, 0x22, 0xfe, 0x70, 0xe0, 0x05, 0x9f, 0x7b, 0xed, 0x5e, 0x27, 0xfe, 0xfb, 0x78, 0xe8,
	0x0d, 0x82, 0x01, 0x5a, 0x0f, 0xbb, 0x6c, 0xda, 0x25, 0xe5, 0x4c, 0x7d, 0xe3, 0x74, 0xbb, 0xf1,
	0xdf, 0xe1, 0x54, 0xf9, 0x3f, 0x05, 0xb8, 0x5d, 0x27, 0x81, 0xca, 0x06, 0x70, 0xbb, 0xd7, 0x31,
	0xdf, 0xba, 0xdd, 0x6e, 0xd3, 0x1b, 0x74, 0x46, 0xed, 0x00, 0x93, 0x77, 0xe8, 0x17, 0x00, 0x7e,
	0x9b, 0xf4, 0x89, 0x1d, 0x5c, 0x0d, 0x49, 0x45, 0xd8, 0x17, 0x8e, 0x36, 0x1f, 0xef, 0x1c, 0xc7,
	0x04, 0x1e, 0x9b, 0x74, 0xd8, 0xba, 0x1a, 0x12, 0xbc, 0xe6, 0x8f, 0x7f, 0xa2, 0x5d, 0x28, 0xf9,
	0x94, 0xc9, 0x76, 0x3b, 0x95, 0xc2, 0xbe, 0x70, 0x54, 0xc6, 0xab, 0xac, 0xad, 0x75, 0xd0, 0x0e,
	0xac, 0x0c, 0x5e, 0xbf, 0xf6, 0x49, 0x50, 0x29, 0xb2, 0x01, 0xde, 0x42, 0x5b, 0xb0, 0xdc, 0x75,
	0x7b, 0x6e, 0x50, 0x59, 0x62, 0xdd, 0x61, 0x03, 0x7d, 0x0b, 0xe5, 0x4b, 0xa7, 0x47, 0xec, 0xa1,
	0x37, 0x18, 0x12, 0x2f, 0xb8, 0xaa, 0x2c, 0xef, 0x17, 0x8f, 0xd6, 0x1f, 0xef, 0x8e, 0x55, 0x60,
	0x6b, 0xa9, 0x3b, 0x3d, 0xd2, 0xe4, 0x00, 0xbc, 0x71, 0x19, 0x6b, 0xc9, 0xff, 0x2c, 0xc0, 0x4e,
	0xf6, 0xea, 0x90, 0x08, 0xc5, 0x91, 0xdb, 0x61, 0x6b, 0x2a, 0x63, 0xfa, 0x73, 0x96, 0xd6, 0x47,
	0x20, 0x86, 0x43, 0xc3, 0x70, 0x36, 0x85, 0x84, 0xfa, 0x6f, 0xfa, 0x31, 0x52, 0xad, 0x83, 0xf6,
	0x60, 0xdd, 0x0f, 0x3c, 0x27, 0x20, 0x97, 0x57, 0x14, 0x14, 0xae, 0x06, 0xc6, 0x5d, 0x21, 0x95,
	0x47, 0xda, 0x4e, 0xb7, 0x6b, 0xfb, 0x83, 0x91, 0xd7, 0x26, 0x14, 0xb5, 0x1c, 0x52, 0x85, 0xfd,
	0x26, 0xeb, 0xd6, 0x3a, 0xf2, 0xdf, 0x0b, 0x70, 0x67, 0x86, 0x75, 0xfc, 0x21, 0x7a, 0x0e, 0x28,
	0xa9, 0x56, 0xd7, 0xf5, 0x83, 0x8a, 0xc0, 0xf6, 0xe8, 0x7e, 0xc2, 0x4c, 0x39, 0x24, 0x62, 0x5c,
	0xfb, 0x86, 0xeb, 0x07, 0x54, 0xff, 0x3e, 0xf9, 0x4d, 0x60, 0x73, 0x23, 0x85, 0xfb, 0x00, 0xb4,
	0xcb, 0x60, 0x3d, 0xf2, 0xdf, 0xac, 0x42, 0xc9, 0xe4, 0xcb, 0x41, 0x9b, 0x50, 0x88, 0xf6, 0xb0,
	0xe0, 0x76, 0xd0, 0x9d, 0x84, 0xbf, 0x84, 0x93, 0x63, 0x7e, 0x81, 0x60, 0xa9, 0xef, 0xf4, 0x08,
	0xdb, 0xba, 0x35, 0xcc, 0x7e, 0x23, 0x09, 0x4a, 0x43, 0xcf, 0x1d, 0x78, 0x6e, 0x70, 0xc5, 0x77,
	0x2b, 0x6a, 0xa3, 0x2f, 0x61, 0xc5, 0x0f, 0x9c, 0x60, 0xe4, 0xb3, 0x1d, 0xda, 0x7c, 0x7c, 0x2b,
	0xe9, 0x7a, 0x5c, 0x0b, 0x93, 0x41, 0x30, 0x87, 0xa2, 0xfb, 0x50, 0xee, 0x3a, 0x7e, 0x60, 0x53,
	0x17, 0x70, 0x82, 0x81, 0x57, 0x59, 0x61, 0xd2, 0x36, 0x68, 0xa7, 0xc1, 0xfb, 0xd0, 0x31, 0x7c,
	0x12, 0x03, 0xb9, 0x83, 0xbe, 0x1d, 0xb8, 0x3d, 0x52, 0x59, 0xdd, 0x17, 0x8e, 0x8a, 0xf8, 0xc6,
	0x04, 0xea, 0x0e, 0xfa, 0x96, 0xdb, 0x23, 0xe8, 0x1b, 0xd8, 0xe8, 0x10, 0xe6, 0x93, 0xe1, 0xd2,
	0x4a, 0x4c, 0x9f, 0x4a, 0x42, 0x9f, 0xd3, 0x10, 0xc0, 0x82, 0x61, 0xbd, 0x33, 0x69, 0xa0, 0x67,
	0x70, 0xdd, 0x79, 0x65, 0x07, 0xc4, 0x0f, 0x6c, 0x9f, 0x04, 0x81, 0xdb, 0xbf, 0xac, 0xac, 0xed,
	0x0b, 0x53, 0x36, 0x1a, 0xaf, 0xe7, 0x58, 0x79, 0x65, 0x11, 0x3f, 0x30, 0x43, 0x28, 0x2e, 0x3b,
	0xf1, 0x26, 0xb2, 0x00, 0x8d, 0x7c, 0xe2, 0xd9, 0x97, 0xde, 0x60, 0x34, 0x8c, 0xf8, 0x80, 0xf1,
	0x1d, 0x66, 0xf3, 0xb5, 0x7c, 0xe2, 0xd5, 0x29, 0x7c, 0x4c, 0x29, 0x8e, 0x52, 0x3d, 0x48, 0x86,
	0x32, 0xd3, 0x8f, 0x3a, 0x90, 0xed, 0x76, 0xfc, 0xca, 0xd6, 0x7e, 0xf1, 0xa8, 0x88, 0xd7, 0x69,
	0x27, 0xf5, 0x0b, 0xad, 0xe3, 0x23, 0x0d, 0x36, 0x13, 0x9e, 0xeb, 0x57, 0xb6, 0x99, 0xa7, 0xc9,
	0xd9, 0x52, 0x71, 0xcc, 0x9b, 0x71, 0x39, 0xee, 0xdb, 0x3e, 0xfa, 0x25, 0xdc, 0xec, 0xb9, 0x7d,
	0xb7, 0x37, 0xea, 0xd9, 0x9c, 0xf2, 0xdd, 0xc8, 0xe9, 0x07, 0xd4, 0x07, 0x76, 0xf6, 0x85, 0xa3,
	0x65, 0xbc, 0xcd, 0x87, 0x43, 0x92, 0xe7, 0x7c, 0x50, 0x1a, 0xc2, 0x46, 0x9c, 0x36, 0xe6, 0x7f,
	0x45, 0xe6, 0x7f, 0x0f, 0x60, 0x33, 0x18, 0x04, 0x4e, 0x8c, 0xae, 0xc0, 0xe8, 0xca, 0xac, 0x77,
	0x4c, 0x83, 0x3e, 0x05, 0x71, 0x2c, 0x3e, 0x02, 0x16, 0x19, 0xf0, 0x3a, 0xef, 0x8f, 0x24, 0xda,
	0x50, 0x4e, 0x98, 0x83, 0x8a, 0x20, 0xbf, 0x19, 0x12, 0xcf, 0xed, 0x91, 0x7e, 0x60, 0x07, 0xce,
	0x25, 0x13, 0xbf, 0x86, 0xcb, 0x93, 0x5e, 0xcb, 0xb9, 0x44, 0x0f, 0xe1, 0x7a, 0x0c, 0xe6, 0x78,
	0x97, 0x7e, 0xa5, 0xb0, 0x5f, 0x3c, 0x5a, 0xc3, 0xb1, 0xd9, 0x8a, 0x77, 0xe9, 0x4b, 0xbf, 0x02,
	0x31, 0x6d, 0x1f, 0x74, 0x00, 0x9b, 0x31, 0x1b, 0x53, 0x73, 0x08, 0xcc, 0x1c, 0x1b, 0x91, 0xdd,
	0xb4, 0x8e, 0x2f, 0x7f, 0x01, 0x70, 0xe6, 0x76, 0x03, 0xe2, 0xe1, 0x51, 0x97, 0x4c, 0x85, 0xe2,
	0x38, 0xd6, 0x0a, 0x93, 0x58, 0x93, 0x6d, 0x58, 0x66, 0xe7, 0x35, 0x1d, 0x8c, 0x4e, 0xf4, 0x32,
	0x66, 0xbf, 0xb3, 0x26, 0xa0, 0x63, 0x28, 0xb5, 0xdf, 0xb8, 0xdd, 0x8e, 0x47, 0xfa, 0x95, 0x22,
	0x33, 0x36, 0x9a, 0x3e, 0xfd, 0x71, 0x84, 0x91, 0xff, 0x77, 0x29, 0xd7, 0x40, 0xb9, 0x5a, 0x4d,
	0x07, 0x6c, 0x71, 0xf1, 0x80, 0x5d, 0xca, 0x0b, 0xd8, 0x3a, 0x6c, 0x78, 0xa3, 0x2e, 0x89, 0x02,
	0x64, 0x99, 0x05, 0xc8, 0x41, 0x42, 0xfb, 0x84, 0x87, 0x8e, 0xba, 0x64, 0x1c, 0x1e, 0xeb, 0xde,
	0xa4, 0x41, 0x05, 0xfb, 0x74, 0xc6, 0xd0, 0xf1, 0x9c, 0x1e, 0x09, 0x88, 0x17, 0x1e, 0x00, 0x2b,
	0x6c, 0x49, 0x37, 0xe8, 0x50, 0x73, 0x3c, 0xc2, 0x82, 0xfd, 0x1b, 0x28, 0xb5, 0x47, 0x7e, 0x30,
	0xe8, 0x11, 0x8f, 0x1d, 0x27, 0xeb, 0x8f, 0xf7, 0x92, 0x5b, 0x96, 0x98, 0xe1, 0xbc, 0xea, 0xd2,
	0xfd, 0xe3, 0x13, 0xd0, 0x23, 0xd8, 0x7a, 0x37, 0x22, 0x23, 0x62, 0x33, 0xdd, 0xa9, 0xa0, 0xf0,
	0x48, 0x87, 0xfd, 0x22, 0x95, 0xc6, 0xc6, 0xa8, 0xa6, 0x54, 0x12, 0x3b, 0xae, 0x5f, 0x00, 0x7a,
	0xcd, 0x7c, 0x20, 0x9c, 0xf1, 0xde, 0xe9, 0x8e, 0x88, 0x5f, 0x59, 0x67, 0xa6, 0xfa, 0x34, 0x77,
	0xb1, 0xc7, 0x13, 0xbf, 0xb9, 0xa0, 0x33, 0xb0, 0xf8, 0x3a, 0xd9, 0xe1, 0x4b, 0x7f, 0x0c, 0xd7,
	0x53, 0xa0, 0x29, 0x5b, 0x4a, 0x50, 0x4a, 0x85, 0x59, 0xd4, 0x96, 0xdf, 0xc3, 0x0d, 0x73, 0x6a,
	0x6b, 0xf6, 0xe0, 0x96, 0x69, 0x60, 0xcb, 0x6e, 0x2a, 0x58, 0x39, 0x57, 0x2d, 0x15, 0xdb, 0xd6,
	0xcb, 0xa6, 0x6a, 0xb7, 0xf4, 0x67, 0xba, 0xf1, 0x42, 0x17, 0xaf, 0xa1, 0x7d, 0xb8, 0x9d, 0x05,
	0xa8, 0xb5, 0x4c, 0xcb, 0x38, 0x57, 0xb1, 0x28, 0xa0, 0xbb, 0x20, 0x65, 0x21, 0xb0, 0xa2, 0x9f,
	0x1a, 0xe7, 0x62, 0x41, 0xfe, 0xb7, 0x02, 0xdc, 0xcc, 0x31, 0x2b, 0xaa, 0x41, 0x69, 0xec, 0x12,
	0xfc, 0x8e, 0x3c, 0x5a, 0xc4, 0x1d, 0x8e, 0x4d, 0x12, 0xe0, 0x55, 0xee, 0x12, 0xd2, 0xef, 0x04,
	0x58, 0xab, 0x0d, 0xfa, 0x1d, 0x97, 0x7a, 0x1a, 0xba, 0x05, 0x6b, 0x91, 0xa5, 0xf8, 0xce, 0x30,
	0x19, 0x6c, 0xb9, 0x5b, 0xb0, 0xcc, 0xec, 0xc1, 0x9d, 0x3d, 0x6c, 0x50, 0x6f, 0x1f, 0x3b, 0x7a,
	0x38, 0x2d, 0x7c, 0x47, 0x6c, 0x8c, 0x3b, 0xd9, 0xd4, 0xfb, 0x50, 0xf6, 0x49, 0x97, 0xb4, 0x03,
	0xd2, 0xb1, 0x87, 0x4e, 0xf0, 0xa6, 0xb2, 0xc4, 0x1c, 0x60, 0x63, 0xdc, 0xd9, 0x74, 0x82, 0x37,
	0x52, 0x0b, 0x8a, 0x26, 0x09, 0x90, 0x0e, 0xd0, 0x1e, 0x2b, 0xe4, 0xf3, 0x85, 0x1d, 0x2f, 0xb4,
	0xb0, 0x68, 0x1d, 0x38, 0xc6, 0x20, 0xff, 0xb6, 0x00, 0x62, 0x7a, 0xc6, 0xc2, 0x07, 0xc6, 0x09,
	0x94, 0x62, 0x61, 0x5c, 0x9c, 0xba, 0x93, 0xd2, 0xc4, 0xc7, 0xe3, 0x00, 0xc7, 0xd1, 0x3c, 0xf4,
	0x55, 0xec, 0xd0, 0x59, 0x62, 0x1c, 0x77, 0x66, 0x72, 0x4c, 0xce, 0x1f, 0xe9, 0x29, 0x94, 0xa2,
	0x13, 0x63, 0x51, 0x95, 0x77, 0x60, 0x85, 0x87, 0x4d, 0x91, 0x1d, 0xd0, 0xbc, 0x25, 0xff, 0x47,
	0x01, 0xd0, 0x74, 0xb0, 0xa2, 0x73, 0x80, 0xe8, 0x28, 0x18, 0x6f, 0xf7, 0xe7, 0x73, 0x22, 0x3c,
	0xd9, 0x85, 0x63, 0x04, 0xd2, 0x63, 0x58, 0xd2, 0x02, 0xd2, 0x9b, 0x38, 0x8b, 0x10, 0x77, 0x96,
	0x2d, 0x58, 0xf6, 0xdb, 0x03, 0x8f, 0xf0, 0xf8, 0x0a, 0x1b, 0xd2, 0xbf, 0x0a, 0x50, 0x4e, 0x30,
	0x2e, 0xbc, 0xd6, 0xaf, 0x61, 0xd9, 0x0d, 0x48, 0xcf, 0xe7, 0xb6, 0x39, 0x98, 0xa7, 0x37, 0x55,
	0x0d, 0x87, 0x53, 0x90, 0x36, 0x65, 0x96, 0x8f, 0x5c, 0xf6, 0xe4, 0x9a, 0xf8, 0x13, 0xd8, 0x8e,
	0x1b, 0xf1, 0xf9, 0xf8, 0x58, 0x5b, 0x74, 0x1d, 0xf2, 0x3d, 0xd8, 0xab, 0x93, 0x20, 0x93, 0x03,
	0x93, 0x77, 0x23, 0xe2, 0x07, 0xf2, 0xf7, 0xb0, 0x9f, 0x0f, 0xf1, 0x87, 0x83, 0xbe, 0x4f, 0xd0,
	0x2f, 0x61, 0x29, 0xf6, 0x62, 0x96, 0x73, 0xbd, 0x6c, 0x32, 0x93, 0xe1, 0xe5, 0xdf, 0x09, 0xb0,
	0x4d, 0x8f, 0x5f, 0xfe, 0xd6, 0x71, 0x89, 0xcf, 0xa5, 0xa6, 0x1e, 0xc0, 0x42, 0xde, 0x03, 0x38,
	0x6e, 0x93, 0x3d, 0x58, 0x1f, 0x3a, 0x97, 0xc4, 0xee, 0x8f, 0x7a, 0xaf, 0x88, 0xc7, 0x8f, 0x03,
	0xa0, 0x5d, 0x3a, 0xeb, 0xa1, 0x87, 0x0c, 0x03, 0xf8, 0xee, 0x0f, 0x24, 0x7a, 0x22, 0x3b, 0x97,
	0xc4, 0x74, 0x7f, 0x20, 0x32, 0x81, 0x9d, 0xb4, 0x26, 0x7c, 0x71, 0xf4, 0xdb, 0x2d, 0xea, 0xe5,
	0x4b, 0xdc, 0xce, 0x7c, 0xaa, 0xe1, 0x18, 0x90, 0xba, 0x1c, 0x7b, 0x2c, 0xf1, 0xd7, 0x7b, 0xd8,
	0x90, 0x0f, 0x00, 0xd5, 0x49, 0x10, 0x4d, 0xe0, 0xab, 0x4d, 0xdd, 0x08, 0xf2, 0x13, 0xf8, 0x24,
	0x81, 0xe2, 0x9a, 0xfc, 0x1c, 0x4a, 0xe3, 0x0f, 0x20, 0x06, 0xce, 0xd5, 0x23, 0x82, 0xc9, 0x4f,
	0x61, 0xbb, 0xe6, 0x11, 0x27, 0x20, 0x69, 0x91, 0xbf, 0x07, 0xd7, 0x11, 0xec, 0xa4, 0xb9, 0xb8,
	0x62, 0x69, 0xfd, 0x9f, 0xc2, 0x76, 0x6b, 0xd8, 0xf9, 0x71, 0xa4, 0x56, 0x60, 0x27, 0xcd, 0x15,
	0x4a, 0x95, 0x0f, 0x61, 0xab, 0xe9, 0x8c, 0x7c, 0x32, 0x6f, 0x37, 0x6f, 0xc2, 0x76, 0x0a, 0x37,
	0x21, 0x30, 0x03, 0xc7, 0x0b, 0x16, 0x20, 0x48, 0xe1, 0x38, 0xc1, 0x43, 0xd8, 0x3e, 0x25, 0x5d,
	0x12, 0xcc, 0x55, 0xa1, 0x02, 0x3b, 0x69, 0x20, 0xa7, 0xb8, 0x09, 0xdb, 0x75, 0x12, 0x4c, 0x9e,
	0x08, 0xe3, 0x08, 0x90, 0x2d, 0xd8, 0x49, 0x0f, 0xf0, 0xdd, 0xfe, 0x1a, 0x36, 0x62, 0x6f, 0x95,
	0xb1, 0x4b, 0xde, 0x4c, 0x6c, 0xe4, 0x64, 0x1e, 0x5e, 0x9f, 0xbc, 0x49, 0x7c, 0x19, 0x81, 0x48,
	0x3d, 0x8b, 0x06, 0x52, 0x24, 0xe9, 0x1b, 0xb8, 0x11, 0xeb, 0xe3, 0x42, 0x0e, 0x13, 0x21, 0x9d,
	0xf5, 0x5a, 0x0d, 0x43, 0xf8, 0x2f, 0xa1, 0x42, 0xe3, 0x26, 0x1e, 0xe5, 0x51, 0x10, 0xa7, 0x22,
	0x52, 0x98, 0x1d, 0x91, 0x85, 0x64, 0x44, 0x66, 0x7e, 0xe4, 0x86, 0xfb, 0xba, 0x14, 0xed, 0xab,
	0x0f, 0xbb, 0x19, 0xd2, 0xf9, 0x12, 0xfe, 0x74, 0xea, 0x3b, 0x4b, 0x48, 0x66, 0x3d, 0xa6, 0x6f,
	0xc1, 0xd4, 0xe7, 0x55, 0x76, 0x0c, 0x6b, 0x2c, 0x9d, 0x10, 0x9f, 0x77, 0x4a, 0x02, 0xc7, 0xed,
	0x46, 0xeb, 0xce, 0x4a, 0x4d, 0x08, 0x99, 0xa9, 0x89, 0x3f, 0x83, 0xbb, 0x79, 0x54, 0x7c, 0x11,
	0xdf, 0x42, 0x39, 0xc1, 0xc5, 0xc3, 0x66, 0xc6, 0x1a, 0x36, 0xe2, 0x32, 0xe4, 0x5f, 0xc3, 0x6e,
	0x18, 0xb4, 0x09, 0x0c, 0x57, 0xf4, 0xff, 0x4b, 0x7e, 0x06, 0x52, 0x16, 0x39, 0x57, 0x7d, 0xf1,
	0x6d, 0xf8, 0x35, 0xec, 0x86, 0x31, 0xfe, 0x53, 0x28, 0x79, 0x1b, 0xa4, 0x2c, 0x72, 0x1e, 0x7f,
	0x2a, 0xec, 0x86, 0x91, 0x99, 0x25, 0x7a, 0xf1, 0x15, 0xdc, 0x06, 0x29, 0x8b, 0x86, 0x0b, 0xd9,
	0x9f, 0x32, 0x33, 0x8d, 0xc6, 0xd0, 0x73, 0xc3, 0x18, 0xbc, 0x98, 0xba, 0x88, 0x27, 0x08, 0xbe,
	0x9d, 0x5f, 0xc2, 0x72, 0x3c, 0xde, 0xe7, 0xbc, 0xe5, 0x42, 0xac, 0xfc, 0x00, 0xee, 0xd7, 0x49,
	0x70, 0x4a, 0x5e, 0x3b, 0xa3, 0x6e, 0x90, 0xf1, 0xc9, 0xc4, 0xc5, 0x5f, 0xc1, 0xc1, 0x6c, 0x18,
	0xd7, 0xe1, 0x39, 0x6c, 0xa5, 0x3f, 0xe2, 0xe8, 0x38, 0x37, 0xc9, 0xdc, 0x0f, 0x34, 0xe4, 0x4f,
	0xf5, 0xc9, 0x7f, 0x01, 0x87, 0xa1, 0x79, 0xe6, 0x29, 0xf9, 0x53, 0x08, 0xff, 0x14, 0x1e, 0xce,
	0x15, 0xce, 0x6d, 0x78, 0x06, 0x52, 0x3a, 0x54, 0x9d, 0xc0, 0xf9, 0x78, 0x4f, 0x19, 0xc2, 0xad,
	0x4c, 0x9e, 0x68, 0x87, 0x7f, 0xec, 0x54, 0xa4, 0xfc, 0x14, 0x6e, 0xd6, 0x49, 0xa0, 0x93, 0x0f,
	0x0c, 0xfb, 0xc1, 0x0d, 0xda, 0x6f, 0xc6, 0x6a, 0x4f, 0x27, 0x6f, 0x67, 0x67, 0x1e, 0xe5, 0x13,
	0xa8, 0x4c, 0x73, 0x45, 0x57, 0xc6, 0xf5, 0x3e, 0xf9, 0xc0, 0x94, 0xb3, 0x7d, 0x36, 0xc4, 0x88,
	0x4b, 0xb8, 0xdc, 0x8f, 0xe3, 0xe5, 0x11, 0xec, 0x2b, 0x9d, 0xce, 0xf4, 0x56, 0xb3, 0x47, 0xf2,
	0x4f, 0x67, 0xeb, 0xaf, 0xe0, 0xde, 0x0c, 0xb1, 0x7c, 0x0d, 0x5b, 0xb0, 0xdc, 0x1e, 0x8c, 0xfa,
	0x01, 0xdf, 0x92, 0xb0, 0x21, 0x7f, 0x0b, 0x77, 0x4e, 0x9c, 0xa0, 0x4e, 0x82, 0xda, 0xc0, 0x69,
	0xbf, 0xa9, 0xbd, 0x71, 0xfa, 0x7d, 0xd2, 0xd5, 0xfa, 0xaf, 0x07, 0xb1, 0xe7, 0x6a, 0x9b, 0x0e,
	0x4d, 0xac, 0x55, 0xc6, 0x6b, 0xac, 0x87, 0x59, 0xe0, 0xbf, 0x0b, 0x70, 0x37, 0x8f, 0x80, 0x0b,
	0xbe, 0x80, 0x10, 0x6f, 0xf7, 0x9c, 0x21, 0x37, 0xf7, 0x57, 0x89, 0x55, 0xce, 0x9e, 0x7f, 0xcc,
	0x06, 0xce, 0x9d, 0xa1, 0xda, 0x0f, 0xbc, 0x2b, 0x5c, 0x6a, 0xf3, 0xa6, 0xf4, 0x57, 0x20, 0xa6,
	0xe7, 0xd0, 0x6b, 0x37, 0x94, 0x35, 0xb1, 0x7d, 0x38, 0xa1, 0x15, 0x3a, 0x40, 0x3b, 0xc4, 0x4e,
	0xf2, 0xf7, 0x6b, 0xbc, 0x47, 0xeb, 0xa0, 0x6d, 0x58, 0x19, 0xf4, 0xed, 0x9e, 0xdb, 0x66, 0xf7,
	0x72, 0x09, 0x2f, 0x0f, 0xfa, 0xe7, 0x6e, 0x9b, 0x7e, 0xfc, 0x75, 0x07, 0xed, 0xb7, 0x24, 0xbc,
	0x9c, 0x4b, 0x98, 0xb7, 0xa4, 0x2b, 0x28, 0x27, 0x34, 0xa3, 0x1e, 0xf7, 0x96, 0x5c, 0x8d, 0x3d,
	0xee, 0x2d, 0xb9, 0x42, 0x38, 0xfe, 0x79, 0xbf, 0xfe, 0xf8, 0x8f, 0x3e, 0x7a, 0xd5, 0xf1, 0x81,
	0x90, 0xea, 0xeb, 0xc2, 0xaf, 0x84, 0xea, 0xbf, 0x17, 0x61, 0x2d, 0xaa, 0xaa, 0xa0, 0x1d, 0x40,
	0x66, 0x4d, 0xd5, 0xd5, 0x74, 0xaa, 0x44, 0x82, 0x9d, 0x58, 0xbf, 0x6a, 0x36, 0x0d, 0x6c, 0xd9,
	0x0a, 0x56, 0x15, 0x51, 0x40, 0xf7, 0x61, 0x2f, 0x36, 0xf6, 0xc4, 0x38, 0x57, 0xed, 0xa6, 0x52,
	0x57, 0xed, 0x9a, 0xa1, 0xd4, 0x9e, 0xd8, 0x0d, 0xcd, 0xb4, 0xc4, 0x42, 0x0a, 0x64, 0x19, 0x4d,
	0xdb, 0xb8, 0x50, 0xb1, 0xd2, 0x68, 0xd8, 0x67, 0x0d, 0x43, 0xb1, 0x34, 0xbd, 0x2e, 0x16, 0x53,
	0xa0, 0xb1, 0x94, 0x9a, 0xa5, 0x5d, 0x68, 0xd6, 0x4b, 0x46, 0x2a, 0x2e, 0xa5, 0x54, 0x69, 0xd5,
	0x6b, 0x76, 0xed, 0x89, 0xa2, 0xeb, 0x6a, 0x43, 0x5c, 0x46, 0x0f, 0xe0, 0x5e, 0x6a, 0xcc, 0x32,
	0xea, 0xaa, 0xf5, 0x44, 0xc5, 0x36, 0x36, 0x8c, 0xf3, 0x50, 0x99, 0x15, 0xb4, 0x0b, 0xdb, 0xd3,
	0x72, 0x2c, 0xe5, 0x44, 0x5c, 0x45, 0x07, 0xb0, 0x3f, 0x3d, 0x74, 0x61, 0x68, 0x35, 0xd5, 0xae,
	0x1b, 0xa7, 0xa1, 0x0e, 0xa5, 0x6c, 0x94, 0xf9, 0x4c, 0x6b, 0x34, 0x26, 0xa8, 0x35, 0x24, 0xc3,
	0xdd, 0x69, 0x94, 0xae, 0xbe, 0x98, 0x60, 0x00, 0xdd, 0x83, 0x3b, 0xd3, 0x98, 0x13, 0xa5, 0xf6,
	0xcc, 0xc6, 0x6a, 0x4d, 0x69, 0x34, 0xc4, 0xf5, 0x14, 0xc4, 0xd0, 0x55, 0xfb, 0x99, 0xfa, 0xd2,
	0x3e, 0xd3, 0xf4, 0xd3, 0x70, 0x8b, 0xc5, 0x8d, 0xea, 0xdf, 0x15, 0x01, 0x9d, 0x8f, 0xba, 0x81,
	0xdb, 0x25, 0xef, 0x49, 0x97, 0x06, 0x2f, 0xb3, 0xe6, 0x3e, 0xdc, 0x3e, 0x6f, 0x35, 0x2c, 0xad,
	0xa1, 0x5e, 0xa8, 0x0d, 0x9b, 0x65, 0xb2, 0x52, 0x76, 0xbd, 0x0b, 0x52, 0x26, 0xc2, 0x34, 0x6a,
	0x58, 0x15, 0x05, 0x74, 0x08, 0x72, 0xe6, 0xf8, 0xf3, 0x96, 0x16, 0xaa, 0xa8, 0x6a, 0x17, 0xaa,
	0x58, 0xa0, 0x1b, 0x92, 0x89, 0x3b, 0xd3, 0xb0, 0x69, 0xd9, 0xd8, 0x68, 0xe9, 0xa7, 0x62, 0x91,
	0xae, 0x24, 0x13, 0x75, 0xaa, 0x9d, 0x9d, 0xd9, 0xa6, 0xfa, 0x9d, 0xb8, 0x94, 0x2b, 0xd0, 0xd0,
	0x1b, 0x9a, 0xae, 0xda, 0xa6, 0xa5, 0x58, 0x2d, 0x53, 0x5c, 0x46, 0x47, 0x70, 0x90, 0x89, 0xab,
	0x19, 0xfa, 0x85, 0x8a, 0x4d, 0xcd, 0xd0, 0x6d, 0xac, 0x58, 0xaa, 0xb8, 0x82, 0x8e, 0xa1, 0x9a,
	0x89, 0xd4, 0x4c, 0x66, 0x8b, 0x71, 0xb6, 0xcf, 0x6e, 0x62, 0xad, 0xa6, 0x8a, 0xab, 0x68, 0x0f,
	0x6e, 0xe5, 0x68, 0x60, 0x9f, 0x6b, 0x35, 0xb1, 0x44, 0xbd, 0x34, 0x9b, 0x50, 0x8f, 0x3c, 0x71,
	0xad, 0xfa, 0x2f, 0xc2, 0x38, 0x5d, 0xce, 0x2c, 0x71, 0x13, 0x3e, 0x39, 0xd3, 0x1a, 0x19, 0x39,
	0xc8, 0xbb, 0x20, 0xc5, 0x07, 0xe8, 0x8e, 0xea, 0x96, 0xad, 0x7e, 0xd7, 0x34, 0x4c, 0xf5, 0x54,
	0x14, 0xd0, 0x1d, 0xd8, 0xcd, 0x18, 0x3f, 0x33, 0x1a, 0x0d, 0xe3, 0x85, 0x58, 0x40, 0xb7, 0xe0,
	0x66, 0xc6, 0x70, 0xed, 0x89, 0x62, 0x89, 0x45, 0x74, 0x1b, 0x2a, 0x19, 0x83, 0x06, 0x3e, 0x55,
	0x71, 0x18, 0x47, 0x09, 0x95, 0x4c, 0x15, 0xdb, 0x75, 0x6c, 0xb4, 0x9a, 0xe2, 0x72, 0xf5, 0x1b,
	0x58, 0xa5, 0x37, 0x91, 0xe5, 0x5c, 0xa2, 0x2d, 0x10, 0x71, 0xed, 0xfc, 0xd4, 0xb6, 0x94, 0x7a,
	0xf2, 0x3c, 0x88, 0x7a, 0x93, 0xbe, 0x20, 0x54, 0xbf, 0x87, 0x75, 0x3e, 0x99, 0x57, 0x67, 0xb7,
	0x23, 0x68, 0x6a, 0xf1, 0x15, 0xd8, 0x4a, 0x0e, 0xe9, 0xad, 0xf3, 0x13, 0x96, 0x78, 0xdd, 0x01,
	0x94, 0x1c, 0xb1, 0xd4, 0xef, 0x2c, 0xb1, 0x50, 0xfd, 0x07, 0x01, 0x10, 0x27, 0xaf, 0x0d, 0x7a,
	0x43, 0xc7, 0x0b, 0x8f, 0xad, 0x7b, 0x70, 0x27, 0x82, 0xd7, 0x8c, 0xf3, 0xa6, 0x82, 0xa7, 0x4e,
	0xb0, 0x5c, 0x48, 0x1d, 0xab, 0x8a, 0x35, 0xce, 0xf6, 0x66, 0x43, 0x1a, 0xaa, 0x69, 0x8a, 0x05,
	0xea, 0x19, 0xd9, 0xe3, 0xea, 0xf3, 0x96, 0xd2, 0x10, 0x8b, 0xd5, 0xdf, 0x0a, 0xb0, 0x99, 0x2c,
	0x13, 0xb2, 0x24, 0xb4, 0x45, 0x3d, 0xb1, 0xfe, 0x92, 0x3b, 0xaf, 0xdd, 0xd2, 0xcd, 0xa6, 0x5a,
	0xd3, 0xce, 0x34, 0xf5, 0x54, 0xbc, 0x46, 0x2d, 0x98, 0x06, 0xe0, 0x96, 0xae, 0xd3, 0x03, 0x51,
	0x60, 0x67, 0x5d, 0x6a, 0xb0, 0xa9, 0xb4, 0xa8, 0x67, 0x14, 0xd8, 0x21, 0x96, 0x1a, 0x3b, 0xc5,
	0xca, 0x99, 0x25, 0x16, 0xab, 0xaf, 0x60, 0x3d, 0x56, 0x1d, 0xa4, 0x7e, 0x70, 0xaa, 0x36, 0xb4,
	0x73, 0x2d, 0x0a, 0xff, 0xb8, 0x02, 0x15, 0xd8, 0x4a, 0x8c, 0x2a, 0x27, 0xb6, 0xa5, 0x9a, 0x96,
	0x28, 0x50, 0xd5, 0x92, 0xf3, 0x26, 0x2e, 0x52, 0xa8, 0xfe, 0xe3, 0x0a, 0x6c, 0xa5, 0x9f, 0xd3,
	0x3c, 0x99, 0xbc, 0x17, 0x1e, 0x5d, 0xb6, 0x69, 0xb4, 0x70, 0x4d, 0xb5, 0x71, 0xab, 0x31, 0x65,
	0x8d, 0x43, 0x90, 0x73, 0x41, 0x13, 0x29, 0x02, 0xfa, 0x14, 0x1e, 0xe4, 0xe1, 0x92, 0x6e, 0x57,
	0x44, 0x3f, 0x83, 0x87, 0x79, 0x50, 0xac, 0xd6, 0x99, 0x0d, 0xed, 0x3f, 0xb4, 0x4f, 0x95, 0x97,
	0xa6, 0xb8, 0x34, 0x8b, 0x37, 0x7d, 0xd2, 0x1c, 0x43, 0x35, 0x0f, 0x1a, 0x3b, 0xdd, 0xec, 0x96,
	0xa9, 0x9c, 0x34, 0xe8, 0x79, 0x33, 0x83, 0xba, 0x66, 0xb4, 0x9a, 0x86, 0x3e, 0x86, 0xae, 0xa2,
	0x87, 0x70, 0x3f, 0x1f, 0x4a, 0x2f, 0xcf, 0xf0, 0x4c, 0x2a, 0xcd, 0xda, 0x2e, 0xa3, 0xa9, 0xea,
	0xe1, 0xd5, 0x23, 0xae, 0xcd, 0xd2, 0x35, 0xe3, 0xac, 0x83, 0x59, 0xba, 0xd6, 0x5b, 0x0a, 0x56,
	0x74, 0x4b, 0x55, 0xed, 0x17, 0x9a, 0x2e, 0xae, 0xcf, 0xd2, 0x35, 0xbc, 0xf8, 0x1a, 0xca, 0x89,
	0xda, 0x10, 0x37, 0x66, 0x71, 0x32, 0xb7, 0x53, 0xc6, 0xd0, 0xf2, 0x2c, 0xce, 0xf0, 0xca, 0x0d,
	0x81, 0x9b, 0xf3, 0x37, 0x2a, 0x04, 0x5e, 0x47, 0x55, 0x38, 0xcc, 0x03, 0x36, 0x55, 0x6c, 0x1a,
	0xba, 0xd2, 0xe0, 0x97, 0xa6, 0x38, 0x8b, 0xb4, 0xde, 0xd2, 0x1a, 0xe3, 0xdb, 0xf5, 0x06, 0xbd,
	0xc7, 0xf3, 0x9d, 0x85, 0x5d, 0x0a, 0x68, 0x96, 0x85, 0x62, 0xf7, 0xc2, 0x27, 0xd5, 0xff, 0x29,
	0xc0, 0xed, 0x74, 0xd8, 0x18, 0xf1, 0x5a, 0xcc, 0x67, 0x70, 0x94, 0x41, 0x64, 0x34, 0x55, 0xac,
	0x58, 0xc6, 0xd4, 0xf5, 0x91, 0xbd, 0x86, 0x24, 0x5a, 0x7d, 0x2e, 0x0a, 0x0b, 0x01, 0xeb, 0xf4,
	0xa1, 0x76, 0x04, 0x07, 0x0b, 0x00, 0x69, 0xc0, 0x2d, 0x42, 0xd9, 0xb0, 0xc4, 0xa5, 0x85, 0x28,
	0x1b, 0x96, 0x2a, 0x2e, 0x2f, 0xb4, 0xf8, 0x9a, 0xa1, 0x5b, 0x8a, 0xa6, 0x8b, 0x2b, 0xe8, 0x0b,
	0xf8, 0x6c, 0x2e, 0x5a, 0x37, 0xac, 0x68, 0xc6, 0x6a, 0xf5, 0x9f, 0x0a, 0xbf, 0x57, 0xa1, 0x90,
	0xbe, 0xc0, 0x32, 0x00, 0xf4, 0x19, 0xc7, 0x1e, 0x01, 0xa2, 0x90, 0x07, 0xd1, 0x4c, 0xfb, 0x4c,
	0x39, 0x37, 0x5a, 0xf4, 0xfa, 0xa0, 0x8f, 0xd3, 0x2c, 0x96, 0x44, 0xa0, 0x15, 0xf3, 0x60, 0xc9,
	0x63, 0x69, 0x29, 0xaf, 0x78, 0xc9, 0x4e, 0x4f, 0xfa, 0x94, 0x5a, 0x66, 0x8f, 0xd4, 0x0c, 0x44,
	0xf8, 0xb6, 0xb0, 0xd9, 0xb9, 0x2e, 0xae, 0x50, 0xc7, 0xcd, 0x2c, 0x82, 0x86, 0x1e, 0x3b, 0x96,
	0xb7, 0xfa, 0xf8, 0xbf, 0x10, 0xdc, 0x88, 0x7d, 0x4b, 0x13, 0xef, 0xbd, 0xdb, 0x26, 0x28, 0x80,
	0xdd, 0xdc, 0xff, 0x0c, 0xa1, 0x64, 0x45, 0x78, 0xd6, 0x3f, 0xbf, 0xa4, 0xea, 0xa2, 0x50, 0x7f,
	0x28, 0x5f, 0x43, 0x7f, 0xce, 0x12, 0xff, 0xe9, 0xe4, 0x00, 0x7a, 0x98, 0x26, 0xc9, 0x49, 0x43,
	0x48, 0x47, 0xf3, 0x81, 0x3c, 0x9d, 0x71, 0x0d, 0xf9, 0xb0, 0x93, 0xfd, 0x75, 0x85, 0xaa, 0x0b,
	0x7d, 0x82, 0x85, 0x12, 0x7f, 0xf6, 0x11, 0x9f, 0x6b, 0xf2, 0x35, 0xf4, 0x12, 0x36, 0x93, 0x65,
	0x16, 0x94, 0xac, 0x16, 0x65, 0x56, 0x83, 0xa4, 0xfb, 0x33, 0x31, 0xfc, 0x0b, 0xba, 0x09, 0xeb,
	0xb1, 0xa2, 0x09, 0xda, 0x4b, 0x6f, 0x45, 0x2a, 0x47, 0x2f, 0xed, 0xe7, 0x03, 0x38, 0xe3, 0x4b,
	0xd8, 0x4c, 0x16, 0x3c, 0x52, 0xca, 0x66, 0x56, 0x56, 0x52, 0xca, 0xe6, 0x54, 0x4c, 0x5e, 0xc2,
	0x66, 0xb2, 0xaa, 0x91, 0xa2, 0xce, 0x2c, 0x9f, 0xa4, 0xa8, 0xb3, 0xcb, 0x22, 0xe8, 0x02, 0xca,
	0x89, 0x72, 0x07, 0xba, 0x97, 0x98, 0x95, 0x55, 0x32, 0x91, 0xe4, 0x59, 0x90, 0x09, 0x6f, 0xa2,
	0x0a, 0x92, 0xe2, 0xcd, 0xaa, 0xa4, 0x48, 0xf2, 0x2c, 0xc8, 0x64, 0x2b, 0x92, 0xb5, 0x91, 0xd4,
	0x56, 0x64, 0x56, 0x58, 0x52, 0x5b, 0x91, 0x5d, 0x5c, 0xa1, 0xd4, 0xc9, 0x1a, 0x4a, 0x8a, 0x3a,
	0xb3, 0xf2, 0x92, 0xa2, 0xce, 0x29, 0xc2, 0x3c, 0x85, 0xb5, 0xa8, 0x68, 0x82, 0xee, 0x4c, 0xb9,
	0x52, 0xbc, 0xc0, 0x22, 0xdd, 0xcd, 0x1b, 0xe6, 0x5c, 0xaf, 0xe0, 0xc6, 0x54, 0x15, 0x03, 0x3d,
	0x98, 0xf2, 0xf9, 0xac, 0x1a, 0x8b, 0x74, 0x38, 0x0f, 0xc6, 0x65, 0xbc, 0x63, 0xe5, 0xa4, 0x8c,
	0x4a, 0x03, 0xaa, 0xce, 0x3c, 0x33, 0x12, 0x95, 0x8d, 0x54, 0xb4, 0xcf, 0x29, 0x5d, 0x10, 0x40,
	0xd3, 0xd5, 0x01, 0x74, 0x98, 0x11, 0x1e, 0x19, 0xb9, 0x77, 0xe9, 0xe1, 0x5c, 0xdc, 0x44, 0xcc,
	0x74, 0x7e, 0x3f, 0x25, 0x26, 0xb7, 0xba, 0x90, 0x12, 0x93, 0x5f, 0x28, 0xa0, 0x62, 0xa6, 0x33,
	0xfc, 0x29, 0x31, 0xb9, 0x95, 0x84, 0x94, 0x98, 0xfc, 0x52, 0x01, 0x0a, 0x58, 0xb2, 0x36, 0xab,
	0x10, 0x80, 0x66, 0x6e, 0x7e, 0xaa, 0xa0, 0x20, 0x7d, 0xb6, 0x18, 0x98, 0x4b, 0xfd, 0xeb, 0xf0,
	0x0f, 0xcc, 0xb9, 0x59, 0x70, 0xf4, 0x45, 0x9a, 0x6e, 0x5e, 0xb6, 0x5e, 0xfa, 0xf9, 0x47, 0xcc,
	0xe0, 0x5a, 0xfc, 0xad, 0x00, 0x7b, 0x73, 0xd2, 0xf1, 0xe8, 0xcb, 0x0c, 0x7b, 0xcd, 0xd5, 0xe5,
	0x0f, 0x3e, 0x6e, 0x12, 0x57, 0xe7, 0x03, 0xcb, 0x75, 0x67, 0xff, 0xc1, 0x62, 0xe6, 0xf6, 0xa6,
	0xff, 0x43, 0x21, 0x7d, 0xbe, 0x20, 0x9a, 0x0b, 0xb6, 0x59, 0x91, 0x36, 0x91, 0x64, 0x47, 0x07,
	0x69, 0x8a, 0xac, 0x7c, 0xbe, 0xf4, 0x60, 0x0e, 0x8a, 0x0b, 0xf8, 0x01, 0x76, 0x73, 0x53, 0xe1,
	0x28, 0xa9, 0xec, 0xbc, 0x4c, 0xbd, 0x74, 0xbc, 0x28, 0x3c, 0x94, 0x7d, 0xf2, 0xe8, 0xfb, 0xcf,
	0x2f, 0x07, 0x5d, 0xa7, 0x7f, 0x79, 0xfc, 0x8b, 0xc7, 0x41, 0x70, 0xdc, 0x1e, 0xf4, 0x1e, 0xb1,
	0x3f, 0xd1, 0xb7, 0x07, 0xdd, 0x47, 0x7e, 0xf8, 0xf8, 0xf2, 0x1f, 0xc5, 0x28, 0x5f, 0xad, 0xb0,
	0xe1, 0x2f, 0xff, 0x2f, 0x00, 0x00, 0xff, 0xff, 0x10, 0x59, 0x85, 0x37, 0xce, 0x2f, 0x00, 0x00,
}
