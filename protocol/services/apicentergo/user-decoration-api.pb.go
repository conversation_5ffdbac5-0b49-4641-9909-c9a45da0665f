// Code generated by protoc-gen-go. DO NOT EDIT.
// source: apicenter-go/user-decoration-api.proto

package apicentergo // import "golang.52tt.com/protocol/services/apicentergo"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 用户装饰品的类型
type DecorationType int32

const (
	DecorationType_INVALID           DecorationType = 0
	DecorationType_FLOAT             DecorationType = 1
	DecorationType_WALL              DecorationType = 2
	DecorationType_CHANNEL_INFO_CARD DecorationType = 3
)

var DecorationType_name = map[int32]string{
	0: "INVALID",
	1: "FLOAT",
	2: "WALL",
	3: "CHANNEL_INFO_CARD",
}
var DecorationType_value = map[string]int32{
	"INVALID":           0,
	"FLOAT":             1,
	"WALL":              2,
	"CHANNEL_INFO_CARD": 3,
}

func (x DecorationType) String() string {
	return proto.EnumName(DecorationType_name, int32(x))
}
func (DecorationType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_decoration_api_1c787afee859f700, []int{0}
}

// 时间叠加类型
type TimeOverlayType int32

const (
	TimeOverlayType_TIMEINVALID TimeOverlayType = 0
	TimeOverlayType_TIMEALTER   TimeOverlayType = 1
	TimeOverlayType_TIMEAPPEND  TimeOverlayType = 2
)

var TimeOverlayType_name = map[int32]string{
	0: "TIMEINVALID",
	1: "TIMEALTER",
	2: "TIMEAPPEND",
}
var TimeOverlayType_value = map[string]int32{
	"TIMEINVALID": 0,
	"TIMEALTER":   1,
	"TIMEAPPEND":  2,
}

func (x TimeOverlayType) String() string {
	return proto.EnumName(TimeOverlayType_name, int32(x))
}
func (TimeOverlayType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_decoration_api_1c787afee859f700, []int{1}
}

type DecorationSourceType int32

const (
	DecorationSourceType_DecUnknown DecorationSourceType = 0
	DecorationSourceType_DecLottie  DecorationSourceType = 1
	DecorationSourceType_DecVap     DecorationSourceType = 2
)

var DecorationSourceType_name = map[int32]string{
	0: "DecUnknown",
	1: "DecLottie",
	2: "DecVap",
}
var DecorationSourceType_value = map[string]int32{
	"DecUnknown": 0,
	"DecLottie":  1,
	"DecVap":     2,
}

func (x DecorationSourceType) String() string {
	return proto.EnumName(DecorationSourceType_name, int32(x))
}
func (DecorationSourceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_decoration_api_1c787afee859f700, []int{2}
}

type DecorationCustomType int32

const (
	DecorationCustomType_DecNone   DecorationCustomType = 0
	DecorationCustomType_DecFusion DecorationCustomType = 1
)

var DecorationCustomType_name = map[int32]string{
	0: "DecNone",
	1: "DecFusion",
}
var DecorationCustomType_value = map[string]int32{
	"DecNone":   0,
	"DecFusion": 1,
}

func (x DecorationCustomType) String() string {
	return proto.EnumName(DecorationCustomType_name, int32(x))
}
func (DecorationCustomType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_decoration_api_1c787afee859f700, []int{3}
}

// 装饰品主要信息
type DecorationConfig struct {
	Typ                  DecorationType `protobuf:"varint,1,opt,name=typ,proto3,enum=apicentergo.DecorationType" json:"typ,omitempty"`
	Id                   string         `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	Version              string         `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
	FusionTtid           string         `protobuf:"bytes,4,opt,name=fusion_ttid,json=fusionTtid,proto3" json:"fusion_ttid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *DecorationConfig) Reset()         { *m = DecorationConfig{} }
func (m *DecorationConfig) String() string { return proto.CompactTextString(m) }
func (*DecorationConfig) ProtoMessage()    {}
func (*DecorationConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_decoration_api_1c787afee859f700, []int{0}
}
func (m *DecorationConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DecorationConfig.Unmarshal(m, b)
}
func (m *DecorationConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DecorationConfig.Marshal(b, m, deterministic)
}
func (dst *DecorationConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DecorationConfig.Merge(dst, src)
}
func (m *DecorationConfig) XXX_Size() int {
	return xxx_messageInfo_DecorationConfig.Size(m)
}
func (m *DecorationConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_DecorationConfig.DiscardUnknown(m)
}

var xxx_messageInfo_DecorationConfig proto.InternalMessageInfo

func (m *DecorationConfig) GetTyp() DecorationType {
	if m != nil {
		return m.Typ
	}
	return DecorationType_INVALID
}

func (m *DecorationConfig) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *DecorationConfig) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

func (m *DecorationConfig) GetFusionTtid() string {
	if m != nil {
		return m.FusionTtid
	}
	return ""
}

// 装饰品细节信息 , 部分主页飘用不到的就注释掉了
type DecDetail struct {
	Name   string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Url    string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	Md5    string `protobuf:"bytes,3,opt,name=md5,proto3" json:"md5,omitempty"`
	UrlPic string `protobuf:"bytes,4,opt,name=url_pic,json=urlPic,proto3" json:"url_pic,omitempty"`
	//  WallExtend wall_extend = 5; // 礼物墙光效额外信息，序列化存在mysql的wall_extend中
	//  bool is_show = 6; // 运营后台配置，是否展示在光效架 ，必须放在外面以便过滤
	Rank uint32 `protobuf:"varint,7,opt,name=rank,proto3" json:"rank,omitempty"`
	//  int64 begin = 8; // 运营后台配置，在光效架展示的开始时间
	//  int64 end = 9; // 运营后台配置，在光效架展示的结束时间
	//  StatusType StatusType = 10; //【不用配】用于向logic返回特效的一个特定状态
	UpdateUser           string               `protobuf:"bytes,11,opt,name=update_user,json=updateUser,proto3" json:"update_user,omitempty"`
	UpdateTime           int64                `protobuf:"varint,12,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	ResourceUrl          string               `protobuf:"bytes,13,opt,name=resource_url,json=resourceUrl,proto3" json:"resource_url,omitempty"`
	Notes                string               `protobuf:"bytes,14,opt,name=notes,proto3" json:"notes,omitempty"`
	Type                 DecorationCustomType `protobuf:"varint,15,opt,name=type,proto3,enum=apicentergo.DecorationCustomType" json:"type,omitempty"`
	DefaultAwardTime     uint32               `protobuf:"varint,16,opt,name=default_award_time,json=defaultAwardTime,proto3" json:"default_award_time,omitempty"`
	SourceType           DecorationSourceType `protobuf:"varint,17,opt,name=source_type,json=sourceType,proto3,enum=apicentergo.DecorationSourceType" json:"source_type,omitempty"`
	FusionColor          string               `protobuf:"bytes,18,opt,name=fusion_color,json=fusionColor,proto3" json:"fusion_color,omitempty"`
	ResourceMd5          string               `protobuf:"bytes,19,opt,name=resource_md5,json=resourceMd5,proto3" json:"resource_md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *DecDetail) Reset()         { *m = DecDetail{} }
func (m *DecDetail) String() string { return proto.CompactTextString(m) }
func (*DecDetail) ProtoMessage()    {}
func (*DecDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_decoration_api_1c787afee859f700, []int{1}
}
func (m *DecDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DecDetail.Unmarshal(m, b)
}
func (m *DecDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DecDetail.Marshal(b, m, deterministic)
}
func (dst *DecDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DecDetail.Merge(dst, src)
}
func (m *DecDetail) XXX_Size() int {
	return xxx_messageInfo_DecDetail.Size(m)
}
func (m *DecDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_DecDetail.DiscardUnknown(m)
}

var xxx_messageInfo_DecDetail proto.InternalMessageInfo

func (m *DecDetail) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *DecDetail) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *DecDetail) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

func (m *DecDetail) GetUrlPic() string {
	if m != nil {
		return m.UrlPic
	}
	return ""
}

func (m *DecDetail) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *DecDetail) GetUpdateUser() string {
	if m != nil {
		return m.UpdateUser
	}
	return ""
}

func (m *DecDetail) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *DecDetail) GetResourceUrl() string {
	if m != nil {
		return m.ResourceUrl
	}
	return ""
}

func (m *DecDetail) GetNotes() string {
	if m != nil {
		return m.Notes
	}
	return ""
}

func (m *DecDetail) GetType() DecorationCustomType {
	if m != nil {
		return m.Type
	}
	return DecorationCustomType_DecNone
}

func (m *DecDetail) GetDefaultAwardTime() uint32 {
	if m != nil {
		return m.DefaultAwardTime
	}
	return 0
}

func (m *DecDetail) GetSourceType() DecorationSourceType {
	if m != nil {
		return m.SourceType
	}
	return DecorationSourceType_DecUnknown
}

func (m *DecDetail) GetFusionColor() string {
	if m != nil {
		return m.FusionColor
	}
	return ""
}

func (m *DecDetail) GetResourceMd5() string {
	if m != nil {
		return m.ResourceMd5
	}
	return ""
}

type UpsertDecorationReq struct {
	Uid                  uint32            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Dec                  *DecorationConfig `protobuf:"bytes,2,opt,name=dec,proto3" json:"dec,omitempty"`
	Typ                  TimeOverlayType   `protobuf:"varint,3,opt,name=typ,proto3,enum=apicentergo.TimeOverlayType" json:"typ,omitempty"`
	Dur                  int64             `protobuf:"varint,4,opt,name=dur,proto3" json:"dur,omitempty"`
	OrderId              string            `protobuf:"bytes,5,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	FusionUid            uint32            `protobuf:"varint,6,opt,name=fusion_uid,json=fusionUid,proto3" json:"fusion_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UpsertDecorationReq) Reset()         { *m = UpsertDecorationReq{} }
func (m *UpsertDecorationReq) String() string { return proto.CompactTextString(m) }
func (*UpsertDecorationReq) ProtoMessage()    {}
func (*UpsertDecorationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_decoration_api_1c787afee859f700, []int{2}
}
func (m *UpsertDecorationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertDecorationReq.Unmarshal(m, b)
}
func (m *UpsertDecorationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertDecorationReq.Marshal(b, m, deterministic)
}
func (dst *UpsertDecorationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertDecorationReq.Merge(dst, src)
}
func (m *UpsertDecorationReq) XXX_Size() int {
	return xxx_messageInfo_UpsertDecorationReq.Size(m)
}
func (m *UpsertDecorationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertDecorationReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertDecorationReq proto.InternalMessageInfo

func (m *UpsertDecorationReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpsertDecorationReq) GetDec() *DecorationConfig {
	if m != nil {
		return m.Dec
	}
	return nil
}

func (m *UpsertDecorationReq) GetTyp() TimeOverlayType {
	if m != nil {
		return m.Typ
	}
	return TimeOverlayType_TIMEINVALID
}

func (m *UpsertDecorationReq) GetDur() int64 {
	if m != nil {
		return m.Dur
	}
	return 0
}

func (m *UpsertDecorationReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *UpsertDecorationReq) GetFusionUid() uint32 {
	if m != nil {
		return m.FusionUid
	}
	return 0
}

type UpsertDecorationResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertDecorationResp) Reset()         { *m = UpsertDecorationResp{} }
func (m *UpsertDecorationResp) String() string { return proto.CompactTextString(m) }
func (*UpsertDecorationResp) ProtoMessage()    {}
func (*UpsertDecorationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_decoration_api_1c787afee859f700, []int{3}
}
func (m *UpsertDecorationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertDecorationResp.Unmarshal(m, b)
}
func (m *UpsertDecorationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertDecorationResp.Marshal(b, m, deterministic)
}
func (dst *UpsertDecorationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertDecorationResp.Merge(dst, src)
}
func (m *UpsertDecorationResp) XXX_Size() int {
	return xxx_messageInfo_UpsertDecorationResp.Size(m)
}
func (m *UpsertDecorationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertDecorationResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertDecorationResp proto.InternalMessageInfo

type DecorationInfo struct {
	Decoration           *DecorationConfig `protobuf:"bytes,1,opt,name=decoration,proto3" json:"decoration,omitempty"`
	DecDetail            *DecDetail        `protobuf:"bytes,2,opt,name=decDetail,proto3" json:"decDetail,omitempty"`
	Time                 *DecorationTim    `protobuf:"bytes,3,opt,name=time,proto3" json:"time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *DecorationInfo) Reset()         { *m = DecorationInfo{} }
func (m *DecorationInfo) String() string { return proto.CompactTextString(m) }
func (*DecorationInfo) ProtoMessage()    {}
func (*DecorationInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_decoration_api_1c787afee859f700, []int{4}
}
func (m *DecorationInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DecorationInfo.Unmarshal(m, b)
}
func (m *DecorationInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DecorationInfo.Marshal(b, m, deterministic)
}
func (dst *DecorationInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DecorationInfo.Merge(dst, src)
}
func (m *DecorationInfo) XXX_Size() int {
	return xxx_messageInfo_DecorationInfo.Size(m)
}
func (m *DecorationInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DecorationInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DecorationInfo proto.InternalMessageInfo

func (m *DecorationInfo) GetDecoration() *DecorationConfig {
	if m != nil {
		return m.Decoration
	}
	return nil
}

func (m *DecorationInfo) GetDecDetail() *DecDetail {
	if m != nil {
		return m.DecDetail
	}
	return nil
}

func (m *DecorationInfo) GetTime() *DecorationTim {
	if m != nil {
		return m.Time
	}
	return nil
}

type DecorationsReq struct {
	Typ                  DecorationType `protobuf:"varint,1,opt,name=typ,proto3,enum=apicentergo.DecorationType" json:"typ,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *DecorationsReq) Reset()         { *m = DecorationsReq{} }
func (m *DecorationsReq) String() string { return proto.CompactTextString(m) }
func (*DecorationsReq) ProtoMessage()    {}
func (*DecorationsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_decoration_api_1c787afee859f700, []int{5}
}
func (m *DecorationsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DecorationsReq.Unmarshal(m, b)
}
func (m *DecorationsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DecorationsReq.Marshal(b, m, deterministic)
}
func (dst *DecorationsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DecorationsReq.Merge(dst, src)
}
func (m *DecorationsReq) XXX_Size() int {
	return xxx_messageInfo_DecorationsReq.Size(m)
}
func (m *DecorationsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DecorationsReq.DiscardUnknown(m)
}

var xxx_messageInfo_DecorationsReq proto.InternalMessageInfo

func (m *DecorationsReq) GetTyp() DecorationType {
	if m != nil {
		return m.Typ
	}
	return DecorationType_INVALID
}

type DecorationsResp struct {
	DecorationInfos      []*DecorationInfo `protobuf:"bytes,1,rep,name=decoration_infos,json=decorationInfos,proto3" json:"decoration_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *DecorationsResp) Reset()         { *m = DecorationsResp{} }
func (m *DecorationsResp) String() string { return proto.CompactTextString(m) }
func (*DecorationsResp) ProtoMessage()    {}
func (*DecorationsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_decoration_api_1c787afee859f700, []int{6}
}
func (m *DecorationsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DecorationsResp.Unmarshal(m, b)
}
func (m *DecorationsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DecorationsResp.Marshal(b, m, deterministic)
}
func (dst *DecorationsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DecorationsResp.Merge(dst, src)
}
func (m *DecorationsResp) XXX_Size() int {
	return xxx_messageInfo_DecorationsResp.Size(m)
}
func (m *DecorationsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DecorationsResp.DiscardUnknown(m)
}

var xxx_messageInfo_DecorationsResp proto.InternalMessageInfo

func (m *DecorationsResp) GetDecorationInfos() []*DecorationInfo {
	if m != nil {
		return m.DecorationInfos
	}
	return nil
}

type InsertDecorationReq struct {
	DecorationInfo       *DecorationInfo `protobuf:"bytes,1,opt,name=decoration_info,json=decorationInfo,proto3" json:"decoration_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *InsertDecorationReq) Reset()         { *m = InsertDecorationReq{} }
func (m *InsertDecorationReq) String() string { return proto.CompactTextString(m) }
func (*InsertDecorationReq) ProtoMessage()    {}
func (*InsertDecorationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_decoration_api_1c787afee859f700, []int{7}
}
func (m *InsertDecorationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsertDecorationReq.Unmarshal(m, b)
}
func (m *InsertDecorationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsertDecorationReq.Marshal(b, m, deterministic)
}
func (dst *InsertDecorationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsertDecorationReq.Merge(dst, src)
}
func (m *InsertDecorationReq) XXX_Size() int {
	return xxx_messageInfo_InsertDecorationReq.Size(m)
}
func (m *InsertDecorationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_InsertDecorationReq.DiscardUnknown(m)
}

var xxx_messageInfo_InsertDecorationReq proto.InternalMessageInfo

func (m *InsertDecorationReq) GetDecorationInfo() *DecorationInfo {
	if m != nil {
		return m.DecorationInfo
	}
	return nil
}

type InsertDecorationResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InsertDecorationResp) Reset()         { *m = InsertDecorationResp{} }
func (m *InsertDecorationResp) String() string { return proto.CompactTextString(m) }
func (*InsertDecorationResp) ProtoMessage()    {}
func (*InsertDecorationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_decoration_api_1c787afee859f700, []int{8}
}
func (m *InsertDecorationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsertDecorationResp.Unmarshal(m, b)
}
func (m *InsertDecorationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsertDecorationResp.Marshal(b, m, deterministic)
}
func (dst *InsertDecorationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsertDecorationResp.Merge(dst, src)
}
func (m *InsertDecorationResp) XXX_Size() int {
	return xxx_messageInfo_InsertDecorationResp.Size(m)
}
func (m *InsertDecorationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_InsertDecorationResp.DiscardUnknown(m)
}

var xxx_messageInfo_InsertDecorationResp proto.InternalMessageInfo

type DelDecorationReq struct {
	Decoration           *DecorationConfig `protobuf:"bytes,1,opt,name=decoration,proto3" json:"decoration,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *DelDecorationReq) Reset()         { *m = DelDecorationReq{} }
func (m *DelDecorationReq) String() string { return proto.CompactTextString(m) }
func (*DelDecorationReq) ProtoMessage()    {}
func (*DelDecorationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_decoration_api_1c787afee859f700, []int{9}
}
func (m *DelDecorationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelDecorationReq.Unmarshal(m, b)
}
func (m *DelDecorationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelDecorationReq.Marshal(b, m, deterministic)
}
func (dst *DelDecorationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelDecorationReq.Merge(dst, src)
}
func (m *DelDecorationReq) XXX_Size() int {
	return xxx_messageInfo_DelDecorationReq.Size(m)
}
func (m *DelDecorationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelDecorationReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelDecorationReq proto.InternalMessageInfo

func (m *DelDecorationReq) GetDecoration() *DecorationConfig {
	if m != nil {
		return m.Decoration
	}
	return nil
}

type DelDecorationResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelDecorationResp) Reset()         { *m = DelDecorationResp{} }
func (m *DelDecorationResp) String() string { return proto.CompactTextString(m) }
func (*DelDecorationResp) ProtoMessage()    {}
func (*DelDecorationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_decoration_api_1c787afee859f700, []int{10}
}
func (m *DelDecorationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelDecorationResp.Unmarshal(m, b)
}
func (m *DelDecorationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelDecorationResp.Marshal(b, m, deterministic)
}
func (dst *DelDecorationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelDecorationResp.Merge(dst, src)
}
func (m *DelDecorationResp) XXX_Size() int {
	return xxx_messageInfo_DelDecorationResp.Size(m)
}
func (m *DelDecorationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelDecorationResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelDecorationResp proto.InternalMessageInfo

type UserDecorationsReq struct {
	Uid                  uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Typ                  DecorationType `protobuf:"varint,2,opt,name=typ,proto3,enum=apicentergo.DecorationType" json:"typ,omitempty"`
	FloatId              string         `protobuf:"bytes,3,opt,name=float_id,json=floatId,proto3" json:"float_id,omitempty"`
	FloatName            string         `protobuf:"bytes,4,opt,name=float_name,json=floatName,proto3" json:"float_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UserDecorationsReq) Reset()         { *m = UserDecorationsReq{} }
func (m *UserDecorationsReq) String() string { return proto.CompactTextString(m) }
func (*UserDecorationsReq) ProtoMessage()    {}
func (*UserDecorationsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_decoration_api_1c787afee859f700, []int{11}
}
func (m *UserDecorationsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDecorationsReq.Unmarshal(m, b)
}
func (m *UserDecorationsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDecorationsReq.Marshal(b, m, deterministic)
}
func (dst *UserDecorationsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDecorationsReq.Merge(dst, src)
}
func (m *UserDecorationsReq) XXX_Size() int {
	return xxx_messageInfo_UserDecorationsReq.Size(m)
}
func (m *UserDecorationsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDecorationsReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserDecorationsReq proto.InternalMessageInfo

func (m *UserDecorationsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserDecorationsReq) GetTyp() DecorationType {
	if m != nil {
		return m.Typ
	}
	return DecorationType_INVALID
}

func (m *UserDecorationsReq) GetFloatId() string {
	if m != nil {
		return m.FloatId
	}
	return ""
}

func (m *UserDecorationsReq) GetFloatName() string {
	if m != nil {
		return m.FloatName
	}
	return ""
}

type UserDecorationsResp struct {
	Uid                  uint32            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Ttid                 uint32            `protobuf:"varint,2,opt,name=ttid,proto3" json:"ttid,omitempty"`
	DecInfos             []*DecorationInfo `protobuf:"bytes,3,rep,name=decInfos,proto3" json:"decInfos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UserDecorationsResp) Reset()         { *m = UserDecorationsResp{} }
func (m *UserDecorationsResp) String() string { return proto.CompactTextString(m) }
func (*UserDecorationsResp) ProtoMessage()    {}
func (*UserDecorationsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_decoration_api_1c787afee859f700, []int{12}
}
func (m *UserDecorationsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDecorationsResp.Unmarshal(m, b)
}
func (m *UserDecorationsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDecorationsResp.Marshal(b, m, deterministic)
}
func (dst *UserDecorationsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDecorationsResp.Merge(dst, src)
}
func (m *UserDecorationsResp) XXX_Size() int {
	return xxx_messageInfo_UserDecorationsResp.Size(m)
}
func (m *UserDecorationsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDecorationsResp.DiscardUnknown(m)
}

var xxx_messageInfo_UserDecorationsResp proto.InternalMessageInfo

func (m *UserDecorationsResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserDecorationsResp) GetTtid() uint32 {
	if m != nil {
		return m.Ttid
	}
	return 0
}

func (m *UserDecorationsResp) GetDecInfos() []*DecorationInfo {
	if m != nil {
		return m.DecInfos
	}
	return nil
}

type BatchUpsertDecorationReq struct {
	User                 []*DecorationUserInfo `protobuf:"bytes,1,rep,name=user,proto3" json:"user,omitempty"`
	Dec                  *DecorationConfig     `protobuf:"bytes,2,opt,name=dec,proto3" json:"dec,omitempty"`
	Typ                  TimeOverlayType       `protobuf:"varint,3,opt,name=typ,proto3,enum=apicentergo.TimeOverlayType" json:"typ,omitempty"`
	Dur                  int64                 `protobuf:"varint,4,opt,name=dur,proto3" json:"dur,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *BatchUpsertDecorationReq) Reset()         { *m = BatchUpsertDecorationReq{} }
func (m *BatchUpsertDecorationReq) String() string { return proto.CompactTextString(m) }
func (*BatchUpsertDecorationReq) ProtoMessage()    {}
func (*BatchUpsertDecorationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_decoration_api_1c787afee859f700, []int{13}
}
func (m *BatchUpsertDecorationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUpsertDecorationReq.Unmarshal(m, b)
}
func (m *BatchUpsertDecorationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUpsertDecorationReq.Marshal(b, m, deterministic)
}
func (dst *BatchUpsertDecorationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUpsertDecorationReq.Merge(dst, src)
}
func (m *BatchUpsertDecorationReq) XXX_Size() int {
	return xxx_messageInfo_BatchUpsertDecorationReq.Size(m)
}
func (m *BatchUpsertDecorationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUpsertDecorationReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUpsertDecorationReq proto.InternalMessageInfo

func (m *BatchUpsertDecorationReq) GetUser() []*DecorationUserInfo {
	if m != nil {
		return m.User
	}
	return nil
}

func (m *BatchUpsertDecorationReq) GetDec() *DecorationConfig {
	if m != nil {
		return m.Dec
	}
	return nil
}

func (m *BatchUpsertDecorationReq) GetTyp() TimeOverlayType {
	if m != nil {
		return m.Typ
	}
	return TimeOverlayType_TIMEINVALID
}

func (m *BatchUpsertDecorationReq) GetDur() int64 {
	if m != nil {
		return m.Dur
	}
	return 0
}

type DecorationUserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	FusionUid            uint32   `protobuf:"varint,2,opt,name=fusion_uid,json=fusionUid,proto3" json:"fusion_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DecorationUserInfo) Reset()         { *m = DecorationUserInfo{} }
func (m *DecorationUserInfo) String() string { return proto.CompactTextString(m) }
func (*DecorationUserInfo) ProtoMessage()    {}
func (*DecorationUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_decoration_api_1c787afee859f700, []int{14}
}
func (m *DecorationUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DecorationUserInfo.Unmarshal(m, b)
}
func (m *DecorationUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DecorationUserInfo.Marshal(b, m, deterministic)
}
func (dst *DecorationUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DecorationUserInfo.Merge(dst, src)
}
func (m *DecorationUserInfo) XXX_Size() int {
	return xxx_messageInfo_DecorationUserInfo.Size(m)
}
func (m *DecorationUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DecorationUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DecorationUserInfo proto.InternalMessageInfo

func (m *DecorationUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DecorationUserInfo) GetFusionUid() uint32 {
	if m != nil {
		return m.FusionUid
	}
	return 0
}

type BatchUpsertDecorationResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchUpsertDecorationResp) Reset()         { *m = BatchUpsertDecorationResp{} }
func (m *BatchUpsertDecorationResp) String() string { return proto.CompactTextString(m) }
func (*BatchUpsertDecorationResp) ProtoMessage()    {}
func (*BatchUpsertDecorationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_decoration_api_1c787afee859f700, []int{15}
}
func (m *BatchUpsertDecorationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUpsertDecorationResp.Unmarshal(m, b)
}
func (m *BatchUpsertDecorationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUpsertDecorationResp.Marshal(b, m, deterministic)
}
func (dst *BatchUpsertDecorationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUpsertDecorationResp.Merge(dst, src)
}
func (m *BatchUpsertDecorationResp) XXX_Size() int {
	return xxx_messageInfo_BatchUpsertDecorationResp.Size(m)
}
func (m *BatchUpsertDecorationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUpsertDecorationResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUpsertDecorationResp proto.InternalMessageInfo

// 装饰品时间信息
type DecorationTim struct {
	Begin                int64    `protobuf:"varint,1,opt,name=begin,proto3" json:"begin,omitempty"`
	End                  int64    `protobuf:"varint,2,opt,name=end,proto3" json:"end,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DecorationTim) Reset()         { *m = DecorationTim{} }
func (m *DecorationTim) String() string { return proto.CompactTextString(m) }
func (*DecorationTim) ProtoMessage()    {}
func (*DecorationTim) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_decoration_api_1c787afee859f700, []int{16}
}
func (m *DecorationTim) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DecorationTim.Unmarshal(m, b)
}
func (m *DecorationTim) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DecorationTim.Marshal(b, m, deterministic)
}
func (dst *DecorationTim) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DecorationTim.Merge(dst, src)
}
func (m *DecorationTim) XXX_Size() int {
	return xxx_messageInfo_DecorationTim.Size(m)
}
func (m *DecorationTim) XXX_DiscardUnknown() {
	xxx_messageInfo_DecorationTim.DiscardUnknown(m)
}

var xxx_messageInfo_DecorationTim proto.InternalMessageInfo

func (m *DecorationTim) GetBegin() int64 {
	if m != nil {
		return m.Begin
	}
	return 0
}

func (m *DecorationTim) GetEnd() int64 {
	if m != nil {
		return m.End
	}
	return 0
}

func init() {
	proto.RegisterType((*DecorationConfig)(nil), "apicentergo.DecorationConfig")
	proto.RegisterType((*DecDetail)(nil), "apicentergo.DecDetail")
	proto.RegisterType((*UpsertDecorationReq)(nil), "apicentergo.UpsertDecorationReq")
	proto.RegisterType((*UpsertDecorationResp)(nil), "apicentergo.UpsertDecorationResp")
	proto.RegisterType((*DecorationInfo)(nil), "apicentergo.DecorationInfo")
	proto.RegisterType((*DecorationsReq)(nil), "apicentergo.DecorationsReq")
	proto.RegisterType((*DecorationsResp)(nil), "apicentergo.DecorationsResp")
	proto.RegisterType((*InsertDecorationReq)(nil), "apicentergo.InsertDecorationReq")
	proto.RegisterType((*InsertDecorationResp)(nil), "apicentergo.InsertDecorationResp")
	proto.RegisterType((*DelDecorationReq)(nil), "apicentergo.DelDecorationReq")
	proto.RegisterType((*DelDecorationResp)(nil), "apicentergo.DelDecorationResp")
	proto.RegisterType((*UserDecorationsReq)(nil), "apicentergo.UserDecorationsReq")
	proto.RegisterType((*UserDecorationsResp)(nil), "apicentergo.UserDecorationsResp")
	proto.RegisterType((*BatchUpsertDecorationReq)(nil), "apicentergo.BatchUpsertDecorationReq")
	proto.RegisterType((*DecorationUserInfo)(nil), "apicentergo.DecorationUserInfo")
	proto.RegisterType((*BatchUpsertDecorationResp)(nil), "apicentergo.BatchUpsertDecorationResp")
	proto.RegisterType((*DecorationTim)(nil), "apicentergo.DecorationTim")
	proto.RegisterEnum("apicentergo.DecorationType", DecorationType_name, DecorationType_value)
	proto.RegisterEnum("apicentergo.TimeOverlayType", TimeOverlayType_name, TimeOverlayType_value)
	proto.RegisterEnum("apicentergo.DecorationSourceType", DecorationSourceType_name, DecorationSourceType_value)
	proto.RegisterEnum("apicentergo.DecorationCustomType", DecorationCustomType_name, DecorationCustomType_value)
}

func init() {
	proto.RegisterFile("apicenter-go/user-decoration-api.proto", fileDescriptor_user_decoration_api_1c787afee859f700)
}

var fileDescriptor_user_decoration_api_1c787afee859f700 = []byte{
	// 991 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x56, 0xdd, 0x6e, 0xe3, 0x44,
	0x14, 0xc6, 0x71, 0xfa, 0x93, 0x93, 0x6d, 0xe2, 0x4e, 0xcb, 0xe2, 0xb2, 0xac, 0xb6, 0xeb, 0x0b,
	0x14, 0x55, 0x24, 0x91, 0xba, 0x54, 0x7b, 0x85, 0x50, 0x1a, 0xa7, 0x22, 0x52, 0x36, 0x2d, 0x26,
	0x59, 0x04, 0x5c, 0x58, 0x5e, 0xcf, 0x24, 0x8c, 0xd6, 0xf1, 0x98, 0xf1, 0xb8, 0xab, 0xbe, 0x03,
	0x12, 0x4f, 0xc3, 0x0b, 0xf0, 0x0c, 0x5c, 0xf1, 0x34, 0x68, 0x8e, 0xdd, 0xfc, 0x36, 0xbb, 0x20,
	0x2e, 0xb8, 0x3b, 0x73, 0xce, 0x99, 0xf3, 0xf3, 0x9d, 0x33, 0x9f, 0x0d, 0x9f, 0x07, 0x09, 0x0f,
	0x59, 0xac, 0x98, 0x6c, 0x4e, 0x45, 0x3b, 0x4b, 0x99, 0x6c, 0x52, 0x16, 0x0a, 0x19, 0x28, 0x2e,
	0xe2, 0x66, 0x90, 0xf0, 0x56, 0x22, 0x85, 0x12, 0xa4, 0x3a, 0xf7, 0x9b, 0x0a, 0xe7, 0x57, 0x03,
	0x2c, 0x77, 0xee, 0xd5, 0x15, 0xf1, 0x84, 0x4f, 0x49, 0x13, 0x4c, 0x75, 0x97, 0xd8, 0xc6, 0xa9,
	0xd1, 0xa8, 0x9d, 0x3f, 0x69, 0x2d, 0xf9, 0xb7, 0x16, 0xbe, 0xa3, 0xbb, 0x84, 0x79, 0xda, 0x8f,
	0xd4, 0xa0, 0xc4, 0xa9, 0x5d, 0x3a, 0x35, 0x1a, 0x15, 0xaf, 0xc4, 0x29, 0xb1, 0x61, 0xef, 0x96,
	0xc9, 0x94, 0x8b, 0xd8, 0x36, 0x51, 0x79, 0x7f, 0x24, 0xcf, 0xa0, 0x3a, 0xc9, 0xb4, 0xe4, 0x2b,
	0xc5, 0xa9, 0x5d, 0x46, 0x2b, 0xe4, 0xaa, 0x91, 0xe2, 0xd4, 0xf9, 0xcb, 0x84, 0x8a, 0xcb, 0x42,
	0x97, 0xa9, 0x80, 0x47, 0x84, 0x40, 0x39, 0x0e, 0x66, 0x0c, 0x0b, 0xa9, 0x78, 0x28, 0x13, 0x0b,
	0xcc, 0x4c, 0x46, 0x45, 0x36, 0x2d, 0x6a, 0xcd, 0x8c, 0x5e, 0x14, 0xa9, 0xb4, 0x48, 0x3e, 0x81,
	0xbd, 0x4c, 0x46, 0x7e, 0xc2, 0xc3, 0x22, 0xc5, 0x6e, 0x26, 0xa3, 0x1b, 0x1e, 0xea, 0x80, 0x32,
	0x88, 0xdf, 0xda, 0x7b, 0xa7, 0x46, 0xe3, 0xc0, 0x43, 0x59, 0xd7, 0x94, 0x25, 0x34, 0x50, 0xcc,
	0xd7, 0x90, 0xd9, 0xd5, 0xbc, 0xa6, 0x5c, 0x35, 0x4e, 0x99, 0x5c, 0x72, 0x50, 0x7c, 0xc6, 0xec,
	0x47, 0xa7, 0x46, 0xc3, 0xbc, 0x77, 0x18, 0xf1, 0x19, 0x23, 0xcf, 0xe1, 0x91, 0x64, 0xa9, 0xc8,
	0x64, 0xc8, 0x7c, 0x5d, 0xdb, 0x01, 0x86, 0xa8, 0xde, 0xeb, 0xc6, 0x32, 0x22, 0xc7, 0xb0, 0x13,
	0x0b, 0xc5, 0x52, 0xbb, 0x86, 0xb6, 0xfc, 0x40, 0x2e, 0xa0, 0xac, 0xee, 0x12, 0x66, 0xd7, 0x11,
	0xe8, 0xe7, 0x5b, 0x80, 0xee, 0x66, 0xa9, 0x12, 0x33, 0x84, 0x1b, 0xdd, 0xc9, 0x17, 0x40, 0x28,
	0x9b, 0x04, 0x59, 0xa4, 0xfc, 0xe0, 0x5d, 0x20, 0x69, 0x5e, 0x97, 0x85, 0x3d, 0x59, 0x85, 0xa5,
	0xa3, 0x0d, 0x58, 0xdd, 0x25, 0x54, 0x8b, 0xda, 0x30, 0xd7, 0xe1, 0x7b, 0x73, 0x7d, 0x87, 0x9e,
	0x98, 0x0b, 0xd2, 0xb9, 0xac, 0x3b, 0x2c, 0xe6, 0x16, 0x8a, 0x48, 0x48, 0x9b, 0xe4, 0x1d, 0xe6,
	0xba, 0xae, 0x56, 0xad, 0x80, 0xa0, 0xc7, 0x71, 0xb4, 0x0a, 0xc2, 0x2b, 0x7a, 0xe1, 0xfc, 0x69,
	0xc0, 0xd1, 0x38, 0x49, 0x99, 0x54, 0x8b, 0x84, 0x1e, 0xfb, 0x05, 0x47, 0xca, 0x29, 0x4e, 0xf9,
	0xc0, 0xd3, 0x22, 0x69, 0x83, 0x49, 0x59, 0x88, 0x43, 0xae, 0x9e, 0x3f, 0xdd, 0x86, 0x0b, 0x2e,
	0xab, 0xa7, 0x3d, 0x49, 0x2b, 0xdf, 0x58, 0x13, 0x9b, 0xfb, 0x6c, 0xe5, 0x82, 0x06, 0xe1, 0xfa,
	0x96, 0xc9, 0x28, 0xb8, 0x5b, 0xac, 0xac, 0x05, 0x26, 0xcd, 0x24, 0x6e, 0x87, 0xe9, 0x69, 0x91,
	0x9c, 0xc0, 0xbe, 0x90, 0x94, 0x49, 0x9f, 0x53, 0x7b, 0x27, 0xdf, 0x5a, 0x3c, 0xf7, 0x29, 0x79,
	0x0a, 0xc5, 0x8a, 0xfa, 0xba, 0xcc, 0x5d, 0x2c, 0xb3, 0x92, 0x6b, 0xc6, 0x9c, 0x3a, 0x8f, 0xe1,
	0x78, 0xb3, 0xab, 0x34, 0x71, 0x7e, 0x37, 0xa0, 0xb6, 0x50, 0xf5, 0xe3, 0x89, 0x20, 0x5f, 0x01,
	0x2c, 0x9e, 0x24, 0x36, 0xfc, 0xc1, 0xf6, 0x96, 0x2e, 0x90, 0x2f, 0xa1, 0x42, 0xef, 0x1f, 0x47,
	0x01, 0xce, 0xe3, 0xf5, 0xdb, 0xb9, 0xd5, 0x5b, 0x38, 0x92, 0x16, 0x94, 0x71, 0x41, 0x4c, 0xbc,
	0xf0, 0xe9, 0xb6, 0xe7, 0xcc, 0x67, 0x1e, 0xfa, 0x39, 0x5f, 0x2f, 0x97, 0x9d, 0xea, 0x01, 0xfd,
	0x3b, 0x3e, 0x70, 0x7e, 0x80, 0xfa, 0x4a, 0x80, 0x34, 0x21, 0x57, 0x60, 0x2d, 0xfa, 0xf0, 0x79,
	0x3c, 0x11, 0xa9, 0x6d, 0x9c, 0x9a, 0x8d, 0xea, 0xd6, 0x70, 0x1a, 0x2f, 0xaf, 0x4e, 0x57, 0xce,
	0xa9, 0xf3, 0x13, 0x1c, 0xf5, 0xe3, 0xcd, 0x0d, 0x72, 0xa1, 0xbe, 0x16, 0xbe, 0x00, 0xf7, 0xbd,
	0xd1, 0x6b, 0xab, 0xd1, 0xf5, 0x20, 0x37, 0x83, 0xa7, 0x89, 0xf3, 0xad, 0xa6, 0xc8, 0x68, 0x35,
	0xe3, 0x7f, 0x9b, 0xa4, 0x73, 0x04, 0x87, 0x6b, 0x21, 0xd3, 0xc4, 0xf9, 0xcd, 0x00, 0xa2, 0x19,
	0x67, 0x0d, 0xfd, 0xcd, 0xe7, 0x51, 0xcc, 0xa3, 0xf4, 0x0f, 0xf9, 0xf9, 0x04, 0xf6, 0x27, 0x91,
	0x08, 0x94, 0x5e, 0xed, 0x82, 0x90, 0xf1, 0x5c, 0xac, 0x36, 0x9a, 0x90, 0x67, 0x73, 0xb2, 0xac,
	0xa0, 0x66, 0x18, 0xcc, 0x98, 0xa3, 0xe0, 0x68, 0xa3, 0xa0, 0x34, 0x79, 0xa0, 0x22, 0x02, 0x65,
	0x64, 0xf4, 0x52, 0x4e, 0xac, 0x5a, 0x26, 0x2f, 0x61, 0x9f, 0xb2, 0x10, 0xe7, 0x66, 0x9b, 0x1f,
	0x9e, 0xf5, 0xdc, 0xd9, 0xf9, 0xc3, 0x00, 0xfb, 0x32, 0x50, 0xe1, 0xcf, 0x0f, 0x91, 0xc5, 0x0b,
	0x28, 0x23, 0x4f, 0xe7, 0xdb, 0xf3, 0x6c, 0x4b, 0x44, 0x5d, 0x35, 0x46, 0x45, 0xe7, 0xff, 0x81,
	0x4f, 0x9c, 0x1e, 0x90, 0xcd, 0x72, 0x1e, 0x40, 0x6e, 0x95, 0x5c, 0x4a, 0xeb, 0xe4, 0xf2, 0x04,
	0x4e, 0xb6, 0x40, 0x91, 0x26, 0xce, 0x4b, 0x38, 0x58, 0x79, 0xc0, 0xfa, 0x33, 0xf3, 0x86, 0x4d,
	0x79, 0xbe, 0x90, 0xa6, 0x97, 0x1f, 0x74, 0x52, 0x16, 0xe7, 0xb1, 0x4d, 0x4f, 0x8b, 0x67, 0xfd,
	0xe5, 0x27, 0x8e, 0x0c, 0x5f, 0x85, 0xbd, 0xfe, 0xf0, 0x75, 0x67, 0xd0, 0x77, 0xad, 0x8f, 0x48,
	0x05, 0x76, 0xae, 0x06, 0xd7, 0x9d, 0x91, 0x65, 0x90, 0x7d, 0x28, 0x7f, 0xdf, 0x19, 0x0c, 0xac,
	0x12, 0xf9, 0x18, 0x0e, 0xbb, 0xdf, 0x74, 0x86, 0xc3, 0xde, 0xc0, 0xef, 0x0f, 0xaf, 0xae, 0xfd,
	0x6e, 0xc7, 0x73, 0x2d, 0xf3, 0xac, 0x03, 0xf5, 0x35, 0x44, 0x48, 0x1d, 0xaa, 0xa3, 0xfe, 0xab,
	0xde, 0x22, 0xde, 0x01, 0x54, 0xb4, 0xa2, 0x33, 0x18, 0xf5, 0x3c, 0xcb, 0x20, 0x35, 0x00, 0x3c,
	0xde, 0xdc, 0xf4, 0x86, 0xae, 0x55, 0x3a, 0xeb, 0xc0, 0xf1, 0x43, 0x5f, 0x20, 0xed, 0xe7, 0xb2,
	0x70, 0x1c, 0xbf, 0x8d, 0xc5, 0xbb, 0x38, 0x0f, 0xe3, 0xb2, 0x70, 0x20, 0x94, 0xe2, 0xcc, 0x32,
	0x08, 0xc0, 0xae, 0xcb, 0xc2, 0xd7, 0x41, 0x62, 0x95, 0xce, 0xce, 0x97, 0x43, 0x2c, 0x3e, 0x98,
	0xba, 0x2d, 0x97, 0x85, 0x43, 0x11, 0xb3, 0xf9, 0xfd, 0x2b, 0xc4, 0xd6, 0x32, 0x2e, 0xdb, 0x3f,
	0x36, 0xa7, 0x22, 0x0a, 0xe2, 0x69, 0xeb, 0xe2, 0x5c, 0xa9, 0x56, 0x28, 0x66, 0x6d, 0xfc, 0x41,
	0x0a, 0x45, 0xd4, 0x4e, 0x99, 0xbc, 0xe5, 0x21, 0x4b, 0xdb, 0x4b, 0x93, 0x7f, 0xb3, 0x8b, 0xe6,
	0x17, 0x7f, 0x07, 0x00, 0x00, 0xff, 0xff, 0x47, 0x3d, 0xab, 0xc3, 0x69, 0x09, 0x00, 0x00,
}
