// Code generated by protoc-gen-go. DO NOT EDIT.
// source: apicenter-go/channel-recommend-api.proto

package apicentergo // import "golang.52tt.com/protocol/services/apicentergo"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 推荐等级
type RecommendLevel int32

const (
	RecommendLevel_Recommend_Invalid RecommendLevel = 0
	RecommendLevel_Recommend_Level_S RecommendLevel = 1
	RecommendLevel_Recommend_Level_A RecommendLevel = 2
	RecommendLevel_Recommend_Level_B RecommendLevel = 3
)

var RecommendLevel_name = map[int32]string{
	0: "Recommend_Invalid",
	1: "Recommend_Level_S",
	2: "Recommend_Level_A",
	3: "Recommend_Level_B",
}
var RecommendLevel_value = map[string]int32{
	"Recommend_Invalid": 0,
	"Recommend_Level_S": 1,
	"Recommend_Level_A": 2,
	"Recommend_Level_B": 3,
}

func (x RecommendLevel) String() string {
	return proto.EnumName(RecommendLevel_name, int32(x))
}
func (RecommendLevel) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{0}
}

// 发放类型
type FlowCardGrantType int32

const (
	FlowCardGrantType_GrantGuild  FlowCardGrantType = 0
	FlowCardGrantType_GrantAnchor FlowCardGrantType = 1
)

var FlowCardGrantType_name = map[int32]string{
	0: "GrantGuild",
	1: "GrantAnchor",
}
var FlowCardGrantType_value = map[string]int32{
	"GrantGuild":  0,
	"GrantAnchor": 1,
}

func (x FlowCardGrantType) String() string {
	return proto.EnumName(FlowCardGrantType_name, int32(x))
}
func (FlowCardGrantType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{1}
}

// 推荐等级
type ChannelLevel int32

const (
	ChannelLevel_Channel_Invalid ChannelLevel = 0
	ChannelLevel_Channel_Level_S ChannelLevel = 1
	ChannelLevel_Channel_Level_A ChannelLevel = 2
	ChannelLevel_Channel_Level_B ChannelLevel = 3
	ChannelLevel_Channel_Level_C ChannelLevel = 4
	ChannelLevel_Channel_Level_D ChannelLevel = 5
	ChannelLevel_Channel_Level_E ChannelLevel = 6
	ChannelLevel_Channel_Level_F ChannelLevel = 7
	ChannelLevel_Channel_Level_G ChannelLevel = 8
)

var ChannelLevel_name = map[int32]string{
	0: "Channel_Invalid",
	1: "Channel_Level_S",
	2: "Channel_Level_A",
	3: "Channel_Level_B",
	4: "Channel_Level_C",
	5: "Channel_Level_D",
	6: "Channel_Level_E",
	7: "Channel_Level_F",
	8: "Channel_Level_G",
}
var ChannelLevel_value = map[string]int32{
	"Channel_Invalid": 0,
	"Channel_Level_S": 1,
	"Channel_Level_A": 2,
	"Channel_Level_B": 3,
	"Channel_Level_C": 4,
	"Channel_Level_D": 5,
	"Channel_Level_E": 6,
	"Channel_Level_F": 7,
	"Channel_Level_G": 8,
}

func (x ChannelLevel) String() string {
	return proto.EnumName(ChannelLevel_name, int32(x))
}
func (ChannelLevel) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{2}
}

// 封禁状态
type SanctionStatus int32

const (
	SanctionStatus_Sanction_Status_None   SanctionStatus = 0
	SanctionStatus_Sanction_Status_BANNED SanctionStatus = 1
)

var SanctionStatus_name = map[int32]string{
	0: "Sanction_Status_None",
	1: "Sanction_Status_BANNED",
}
var SanctionStatus_value = map[string]int32{
	"Sanction_Status_None":   0,
	"Sanction_Status_BANNED": 1,
}

func (x SanctionStatus) String() string {
	return proto.EnumName(SanctionStatus_name, int32(x))
}
func (SanctionStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{3}
}

// 推荐库配置类型
type PrepareType int32

const (
	PrepareType_Prepare_Type_Invalid PrepareType = 0
	PrepareType_Prepare_Type_Manual  PrepareType = 1
	PrepareType_Prepare_Type_Auto    PrepareType = 2
)

var PrepareType_name = map[int32]string{
	0: "Prepare_Type_Invalid",
	1: "Prepare_Type_Manual",
	2: "Prepare_Type_Auto",
}
var PrepareType_value = map[string]int32{
	"Prepare_Type_Invalid": 0,
	"Prepare_Type_Manual":  1,
	"Prepare_Type_Auto":    2,
}

func (x PrepareType) String() string {
	return proto.EnumName(PrepareType_name, int32(x))
}
func (PrepareType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{4}
}

// 推荐库操作类型
type PrepareOperType int32

const (
	PrepareOperType_Prepare_Oper_Type_Invalid PrepareOperType = 0
	PrepareOperType_Prepare_Oper_Type_Add     PrepareOperType = 1
	PrepareOperType_Prepare_Oper_Type_Update  PrepareOperType = 2
	PrepareOperType_Prepare_Oper_Type_Del     PrepareOperType = 3
)

var PrepareOperType_name = map[int32]string{
	0: "Prepare_Oper_Type_Invalid",
	1: "Prepare_Oper_Type_Add",
	2: "Prepare_Oper_Type_Update",
	3: "Prepare_Oper_Type_Del",
}
var PrepareOperType_value = map[string]int32{
	"Prepare_Oper_Type_Invalid": 0,
	"Prepare_Oper_Type_Add":     1,
	"Prepare_Oper_Type_Update":  2,
	"Prepare_Oper_Type_Del":     3,
}

func (x PrepareOperType) String() string {
	return proto.EnumName(PrepareOperType_name, int32(x))
}
func (PrepareOperType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{5}
}

// 配置状态
type DisplaySceneStatusType int32

const (
	DisplaySceneStatusType_Display_Scene_Status_Invalid DisplaySceneStatusType = 0
	DisplaySceneStatusType_Display_Scene_Status_Future  DisplaySceneStatusType = 1
	DisplaySceneStatusType_Display_Scene_Status_Valid   DisplaySceneStatusType = 2
	DisplaySceneStatusType_Display_Scene_Status_Expire  DisplaySceneStatusType = 3
)

var DisplaySceneStatusType_name = map[int32]string{
	0: "Display_Scene_Status_Invalid",
	1: "Display_Scene_Status_Future",
	2: "Display_Scene_Status_Valid",
	3: "Display_Scene_Status_Expire",
}
var DisplaySceneStatusType_value = map[string]int32{
	"Display_Scene_Status_Invalid": 0,
	"Display_Scene_Status_Future":  1,
	"Display_Scene_Status_Valid":   2,
	"Display_Scene_Status_Expire":  3,
}

func (x DisplaySceneStatusType) String() string {
	return proto.EnumName(DisplaySceneStatusType_name, int32(x))
}
func (DisplaySceneStatusType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{6}
}

// 配置时间类型
type DisplaySceneTimeType int32

const (
	DisplaySceneTimeType_Display_Scene_Time_Invalid DisplaySceneTimeType = 0
	DisplaySceneTimeType_Display_Scene_Time_Regular DisplaySceneTimeType = 1
	DisplaySceneTimeType_Display_Scene_Time_Forever DisplaySceneTimeType = 2
)

var DisplaySceneTimeType_name = map[int32]string{
	0: "Display_Scene_Time_Invalid",
	1: "Display_Scene_Time_Regular",
	2: "Display_Scene_Time_Forever",
}
var DisplaySceneTimeType_value = map[string]int32{
	"Display_Scene_Time_Invalid": 0,
	"Display_Scene_Time_Regular": 1,
	"Display_Scene_Time_Forever": 2,
}

func (x DisplaySceneTimeType) String() string {
	return proto.EnumName(DisplaySceneTimeType_name, int32(x))
}
func (DisplaySceneTimeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{7}
}

// 流量卡限额配置
type LevelConf struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	Cnt                  uint32   `protobuf:"varint,2,opt,name=cnt,proto3" json:"cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LevelConf) Reset()         { *m = LevelConf{} }
func (m *LevelConf) String() string { return proto.CompactTextString(m) }
func (*LevelConf) ProtoMessage()    {}
func (*LevelConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{0}
}
func (m *LevelConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelConf.Unmarshal(m, b)
}
func (m *LevelConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelConf.Marshal(b, m, deterministic)
}
func (dst *LevelConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelConf.Merge(dst, src)
}
func (m *LevelConf) XXX_Size() int {
	return xxx_messageInfo_LevelConf.Size(m)
}
func (m *LevelConf) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelConf.DiscardUnknown(m)
}

var xxx_messageInfo_LevelConf proto.InternalMessageInfo

func (m *LevelConf) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *LevelConf) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

type LimitConf struct {
	HourCnt              uint32       `protobuf:"varint,1,opt,name=hourCnt,proto3" json:"hourCnt,omitempty"`
	ConfList             []*LevelConf `protobuf:"bytes,2,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *LimitConf) Reset()         { *m = LimitConf{} }
func (m *LimitConf) String() string { return proto.CompactTextString(m) }
func (*LimitConf) ProtoMessage()    {}
func (*LimitConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{1}
}
func (m *LimitConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LimitConf.Unmarshal(m, b)
}
func (m *LimitConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LimitConf.Marshal(b, m, deterministic)
}
func (dst *LimitConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LimitConf.Merge(dst, src)
}
func (m *LimitConf) XXX_Size() int {
	return xxx_messageInfo_LimitConf.Size(m)
}
func (m *LimitConf) XXX_DiscardUnknown() {
	xxx_messageInfo_LimitConf.DiscardUnknown(m)
}

var xxx_messageInfo_LimitConf proto.InternalMessageInfo

func (m *LimitConf) GetHourCnt() uint32 {
	if m != nil {
		return m.HourCnt
	}
	return 0
}

func (m *LimitConf) GetConfList() []*LevelConf {
	if m != nil {
		return m.ConfList
	}
	return nil
}

type FlowCardLimitConf struct {
	Id                   uint32       `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	BeginTs              uint32       `protobuf:"varint,2,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32       `protobuf:"varint,3,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	ConfList             []*LimitConf `protobuf:"bytes,4,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *FlowCardLimitConf) Reset()         { *m = FlowCardLimitConf{} }
func (m *FlowCardLimitConf) String() string { return proto.CompactTextString(m) }
func (*FlowCardLimitConf) ProtoMessage()    {}
func (*FlowCardLimitConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{2}
}
func (m *FlowCardLimitConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FlowCardLimitConf.Unmarshal(m, b)
}
func (m *FlowCardLimitConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FlowCardLimitConf.Marshal(b, m, deterministic)
}
func (dst *FlowCardLimitConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FlowCardLimitConf.Merge(dst, src)
}
func (m *FlowCardLimitConf) XXX_Size() int {
	return xxx_messageInfo_FlowCardLimitConf.Size(m)
}
func (m *FlowCardLimitConf) XXX_DiscardUnknown() {
	xxx_messageInfo_FlowCardLimitConf.DiscardUnknown(m)
}

var xxx_messageInfo_FlowCardLimitConf proto.InternalMessageInfo

func (m *FlowCardLimitConf) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *FlowCardLimitConf) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *FlowCardLimitConf) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *FlowCardLimitConf) GetConfList() []*LimitConf {
	if m != nil {
		return m.ConfList
	}
	return nil
}

type GetFlowCardLimitConfListReq struct {
	Page                 uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	BeginTs              uint32   `protobuf:"varint,3,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,4,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFlowCardLimitConfListReq) Reset()         { *m = GetFlowCardLimitConfListReq{} }
func (m *GetFlowCardLimitConfListReq) String() string { return proto.CompactTextString(m) }
func (*GetFlowCardLimitConfListReq) ProtoMessage()    {}
func (*GetFlowCardLimitConfListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{3}
}
func (m *GetFlowCardLimitConfListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFlowCardLimitConfListReq.Unmarshal(m, b)
}
func (m *GetFlowCardLimitConfListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFlowCardLimitConfListReq.Marshal(b, m, deterministic)
}
func (dst *GetFlowCardLimitConfListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFlowCardLimitConfListReq.Merge(dst, src)
}
func (m *GetFlowCardLimitConfListReq) XXX_Size() int {
	return xxx_messageInfo_GetFlowCardLimitConfListReq.Size(m)
}
func (m *GetFlowCardLimitConfListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFlowCardLimitConfListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFlowCardLimitConfListReq proto.InternalMessageInfo

func (m *GetFlowCardLimitConfListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetFlowCardLimitConfListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetFlowCardLimitConfListReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *GetFlowCardLimitConfListReq) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

type GetFlowCardLimitConfListResp struct {
	ConfList             []*FlowCardLimitConf `protobuf:"bytes,1,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	NextPage             uint32               `protobuf:"varint,2,opt,name=next_page,json=nextPage,proto3" json:"next_page,omitempty"`
	TotalCnt             uint32               `protobuf:"varint,3,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetFlowCardLimitConfListResp) Reset()         { *m = GetFlowCardLimitConfListResp{} }
func (m *GetFlowCardLimitConfListResp) String() string { return proto.CompactTextString(m) }
func (*GetFlowCardLimitConfListResp) ProtoMessage()    {}
func (*GetFlowCardLimitConfListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{4}
}
func (m *GetFlowCardLimitConfListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFlowCardLimitConfListResp.Unmarshal(m, b)
}
func (m *GetFlowCardLimitConfListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFlowCardLimitConfListResp.Marshal(b, m, deterministic)
}
func (dst *GetFlowCardLimitConfListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFlowCardLimitConfListResp.Merge(dst, src)
}
func (m *GetFlowCardLimitConfListResp) XXX_Size() int {
	return xxx_messageInfo_GetFlowCardLimitConfListResp.Size(m)
}
func (m *GetFlowCardLimitConfListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFlowCardLimitConfListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFlowCardLimitConfListResp proto.InternalMessageInfo

func (m *GetFlowCardLimitConfListResp) GetConfList() []*FlowCardLimitConf {
	if m != nil {
		return m.ConfList
	}
	return nil
}

func (m *GetFlowCardLimitConfListResp) GetNextPage() uint32 {
	if m != nil {
		return m.NextPage
	}
	return 0
}

func (m *GetFlowCardLimitConfListResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

// 增加流量卡限额配置
type AddFlowCardLimitConfReq struct {
	Conf                 *FlowCardLimitConf `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *AddFlowCardLimitConfReq) Reset()         { *m = AddFlowCardLimitConfReq{} }
func (m *AddFlowCardLimitConfReq) String() string { return proto.CompactTextString(m) }
func (*AddFlowCardLimitConfReq) ProtoMessage()    {}
func (*AddFlowCardLimitConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{5}
}
func (m *AddFlowCardLimitConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddFlowCardLimitConfReq.Unmarshal(m, b)
}
func (m *AddFlowCardLimitConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddFlowCardLimitConfReq.Marshal(b, m, deterministic)
}
func (dst *AddFlowCardLimitConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddFlowCardLimitConfReq.Merge(dst, src)
}
func (m *AddFlowCardLimitConfReq) XXX_Size() int {
	return xxx_messageInfo_AddFlowCardLimitConfReq.Size(m)
}
func (m *AddFlowCardLimitConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddFlowCardLimitConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddFlowCardLimitConfReq proto.InternalMessageInfo

func (m *AddFlowCardLimitConfReq) GetConf() *FlowCardLimitConf {
	if m != nil {
		return m.Conf
	}
	return nil
}

type AddFlowCardLimitConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddFlowCardLimitConfResp) Reset()         { *m = AddFlowCardLimitConfResp{} }
func (m *AddFlowCardLimitConfResp) String() string { return proto.CompactTextString(m) }
func (*AddFlowCardLimitConfResp) ProtoMessage()    {}
func (*AddFlowCardLimitConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{6}
}
func (m *AddFlowCardLimitConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddFlowCardLimitConfResp.Unmarshal(m, b)
}
func (m *AddFlowCardLimitConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddFlowCardLimitConfResp.Marshal(b, m, deterministic)
}
func (dst *AddFlowCardLimitConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddFlowCardLimitConfResp.Merge(dst, src)
}
func (m *AddFlowCardLimitConfResp) XXX_Size() int {
	return xxx_messageInfo_AddFlowCardLimitConfResp.Size(m)
}
func (m *AddFlowCardLimitConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddFlowCardLimitConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddFlowCardLimitConfResp proto.InternalMessageInfo

// 更新流量卡限额配置
type UpdateFlowCardLimitConfReq struct {
	Conf                 *FlowCardLimitConf `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *UpdateFlowCardLimitConfReq) Reset()         { *m = UpdateFlowCardLimitConfReq{} }
func (m *UpdateFlowCardLimitConfReq) String() string { return proto.CompactTextString(m) }
func (*UpdateFlowCardLimitConfReq) ProtoMessage()    {}
func (*UpdateFlowCardLimitConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{7}
}
func (m *UpdateFlowCardLimitConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateFlowCardLimitConfReq.Unmarshal(m, b)
}
func (m *UpdateFlowCardLimitConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateFlowCardLimitConfReq.Marshal(b, m, deterministic)
}
func (dst *UpdateFlowCardLimitConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateFlowCardLimitConfReq.Merge(dst, src)
}
func (m *UpdateFlowCardLimitConfReq) XXX_Size() int {
	return xxx_messageInfo_UpdateFlowCardLimitConfReq.Size(m)
}
func (m *UpdateFlowCardLimitConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateFlowCardLimitConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateFlowCardLimitConfReq proto.InternalMessageInfo

func (m *UpdateFlowCardLimitConfReq) GetConf() *FlowCardLimitConf {
	if m != nil {
		return m.Conf
	}
	return nil
}

type UpdateFlowCardLimitConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateFlowCardLimitConfResp) Reset()         { *m = UpdateFlowCardLimitConfResp{} }
func (m *UpdateFlowCardLimitConfResp) String() string { return proto.CompactTextString(m) }
func (*UpdateFlowCardLimitConfResp) ProtoMessage()    {}
func (*UpdateFlowCardLimitConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{8}
}
func (m *UpdateFlowCardLimitConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateFlowCardLimitConfResp.Unmarshal(m, b)
}
func (m *UpdateFlowCardLimitConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateFlowCardLimitConfResp.Marshal(b, m, deterministic)
}
func (dst *UpdateFlowCardLimitConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateFlowCardLimitConfResp.Merge(dst, src)
}
func (m *UpdateFlowCardLimitConfResp) XXX_Size() int {
	return xxx_messageInfo_UpdateFlowCardLimitConfResp.Size(m)
}
func (m *UpdateFlowCardLimitConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateFlowCardLimitConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateFlowCardLimitConfResp proto.InternalMessageInfo

// 删除流量卡限额配置
type DelFlowCardLimitConfReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelFlowCardLimitConfReq) Reset()         { *m = DelFlowCardLimitConfReq{} }
func (m *DelFlowCardLimitConfReq) String() string { return proto.CompactTextString(m) }
func (*DelFlowCardLimitConfReq) ProtoMessage()    {}
func (*DelFlowCardLimitConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{9}
}
func (m *DelFlowCardLimitConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelFlowCardLimitConfReq.Unmarshal(m, b)
}
func (m *DelFlowCardLimitConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelFlowCardLimitConfReq.Marshal(b, m, deterministic)
}
func (dst *DelFlowCardLimitConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelFlowCardLimitConfReq.Merge(dst, src)
}
func (m *DelFlowCardLimitConfReq) XXX_Size() int {
	return xxx_messageInfo_DelFlowCardLimitConfReq.Size(m)
}
func (m *DelFlowCardLimitConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelFlowCardLimitConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelFlowCardLimitConfReq proto.InternalMessageInfo

func (m *DelFlowCardLimitConfReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DelFlowCardLimitConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelFlowCardLimitConfResp) Reset()         { *m = DelFlowCardLimitConfResp{} }
func (m *DelFlowCardLimitConfResp) String() string { return proto.CompactTextString(m) }
func (*DelFlowCardLimitConfResp) ProtoMessage()    {}
func (*DelFlowCardLimitConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{10}
}
func (m *DelFlowCardLimitConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelFlowCardLimitConfResp.Unmarshal(m, b)
}
func (m *DelFlowCardLimitConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelFlowCardLimitConfResp.Marshal(b, m, deterministic)
}
func (dst *DelFlowCardLimitConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelFlowCardLimitConfResp.Merge(dst, src)
}
func (m *DelFlowCardLimitConfResp) XXX_Size() int {
	return xxx_messageInfo_DelFlowCardLimitConfResp.Size(m)
}
func (m *DelFlowCardLimitConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelFlowCardLimitConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelFlowCardLimitConfResp proto.InternalMessageInfo

type FlowCardGrantInfo struct {
	GrantId              uint32   `protobuf:"varint,1,opt,name=grant_id,json=grantId,proto3" json:"grant_id,omitempty"`
	Id                   uint32   `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Level                uint32   `protobuf:"varint,4,opt,name=level,proto3" json:"level,omitempty"`
	ExpirtTs             uint32   `protobuf:"varint,5,opt,name=expirt_ts,json=expirtTs,proto3" json:"expirt_ts,omitempty"`
	Cnt                  uint32   `protobuf:"varint,6,opt,name=cnt,proto3" json:"cnt,omitempty"`
	Remark               string   `protobuf:"bytes,7,opt,name=remark,proto3" json:"remark,omitempty"`
	IsBanned             bool     `protobuf:"varint,8,opt,name=is_banned,json=isBanned,proto3" json:"is_banned,omitempty"`
	BanBeginTs           uint32   `protobuf:"varint,9,opt,name=ban_begin_ts,json=banBeginTs,proto3" json:"ban_begin_ts,omitempty"`
	BanEndTs             uint32   `protobuf:"varint,10,opt,name=ban_end_ts,json=banEndTs,proto3" json:"ban_end_ts,omitempty"`
	Ttid                 string   `protobuf:"bytes,11,opt,name=ttid,proto3" json:"ttid,omitempty"`
	OrderId              string   `protobuf:"bytes,12,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FlowCardGrantInfo) Reset()         { *m = FlowCardGrantInfo{} }
func (m *FlowCardGrantInfo) String() string { return proto.CompactTextString(m) }
func (*FlowCardGrantInfo) ProtoMessage()    {}
func (*FlowCardGrantInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{11}
}
func (m *FlowCardGrantInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FlowCardGrantInfo.Unmarshal(m, b)
}
func (m *FlowCardGrantInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FlowCardGrantInfo.Marshal(b, m, deterministic)
}
func (dst *FlowCardGrantInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FlowCardGrantInfo.Merge(dst, src)
}
func (m *FlowCardGrantInfo) XXX_Size() int {
	return xxx_messageInfo_FlowCardGrantInfo.Size(m)
}
func (m *FlowCardGrantInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FlowCardGrantInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FlowCardGrantInfo proto.InternalMessageInfo

func (m *FlowCardGrantInfo) GetGrantId() uint32 {
	if m != nil {
		return m.GrantId
	}
	return 0
}

func (m *FlowCardGrantInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *FlowCardGrantInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *FlowCardGrantInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *FlowCardGrantInfo) GetExpirtTs() uint32 {
	if m != nil {
		return m.ExpirtTs
	}
	return 0
}

func (m *FlowCardGrantInfo) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

func (m *FlowCardGrantInfo) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *FlowCardGrantInfo) GetIsBanned() bool {
	if m != nil {
		return m.IsBanned
	}
	return false
}

func (m *FlowCardGrantInfo) GetBanBeginTs() uint32 {
	if m != nil {
		return m.BanBeginTs
	}
	return 0
}

func (m *FlowCardGrantInfo) GetBanEndTs() uint32 {
	if m != nil {
		return m.BanEndTs
	}
	return 0
}

func (m *FlowCardGrantInfo) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *FlowCardGrantInfo) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

// 获取流量卡列表
type GetGrantFlowCardListReq struct {
	GrantType            uint32   `protobuf:"varint,1,opt,name=grant_type,json=grantType,proto3" json:"grant_type,omitempty"`
	Id                   string   `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	Page                 uint32   `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGrantFlowCardListReq) Reset()         { *m = GetGrantFlowCardListReq{} }
func (m *GetGrantFlowCardListReq) String() string { return proto.CompactTextString(m) }
func (*GetGrantFlowCardListReq) ProtoMessage()    {}
func (*GetGrantFlowCardListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{12}
}
func (m *GetGrantFlowCardListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGrantFlowCardListReq.Unmarshal(m, b)
}
func (m *GetGrantFlowCardListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGrantFlowCardListReq.Marshal(b, m, deterministic)
}
func (dst *GetGrantFlowCardListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGrantFlowCardListReq.Merge(dst, src)
}
func (m *GetGrantFlowCardListReq) XXX_Size() int {
	return xxx_messageInfo_GetGrantFlowCardListReq.Size(m)
}
func (m *GetGrantFlowCardListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGrantFlowCardListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGrantFlowCardListReq proto.InternalMessageInfo

func (m *GetGrantFlowCardListReq) GetGrantType() uint32 {
	if m != nil {
		return m.GrantType
	}
	return 0
}

func (m *GetGrantFlowCardListReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GetGrantFlowCardListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetGrantFlowCardListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetGrantFlowCardListResp struct {
	InfoList             []*FlowCardGrantInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	NextPage             uint32               `protobuf:"varint,2,opt,name=next_page,json=nextPage,proto3" json:"next_page,omitempty"`
	TotalCnt             uint32               `protobuf:"varint,3,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetGrantFlowCardListResp) Reset()         { *m = GetGrantFlowCardListResp{} }
func (m *GetGrantFlowCardListResp) String() string { return proto.CompactTextString(m) }
func (*GetGrantFlowCardListResp) ProtoMessage()    {}
func (*GetGrantFlowCardListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{13}
}
func (m *GetGrantFlowCardListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGrantFlowCardListResp.Unmarshal(m, b)
}
func (m *GetGrantFlowCardListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGrantFlowCardListResp.Marshal(b, m, deterministic)
}
func (dst *GetGrantFlowCardListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGrantFlowCardListResp.Merge(dst, src)
}
func (m *GetGrantFlowCardListResp) XXX_Size() int {
	return xxx_messageInfo_GetGrantFlowCardListResp.Size(m)
}
func (m *GetGrantFlowCardListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGrantFlowCardListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGrantFlowCardListResp proto.InternalMessageInfo

func (m *GetGrantFlowCardListResp) GetInfoList() []*FlowCardGrantInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetGrantFlowCardListResp) GetNextPage() uint32 {
	if m != nil {
		return m.NextPage
	}
	return 0
}

func (m *GetGrantFlowCardListResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

// 发放流量卡
type GrantFlowCardReq struct {
	GrantType            uint32               `protobuf:"varint,1,opt,name=grant_type,json=grantType,proto3" json:"grant_type,omitempty"`
	InfoList             []*FlowCardGrantInfo `protobuf:"bytes,2,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GrantFlowCardReq) Reset()         { *m = GrantFlowCardReq{} }
func (m *GrantFlowCardReq) String() string { return proto.CompactTextString(m) }
func (*GrantFlowCardReq) ProtoMessage()    {}
func (*GrantFlowCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{14}
}
func (m *GrantFlowCardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GrantFlowCardReq.Unmarshal(m, b)
}
func (m *GrantFlowCardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GrantFlowCardReq.Marshal(b, m, deterministic)
}
func (dst *GrantFlowCardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GrantFlowCardReq.Merge(dst, src)
}
func (m *GrantFlowCardReq) XXX_Size() int {
	return xxx_messageInfo_GrantFlowCardReq.Size(m)
}
func (m *GrantFlowCardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GrantFlowCardReq.DiscardUnknown(m)
}

var xxx_messageInfo_GrantFlowCardReq proto.InternalMessageInfo

func (m *GrantFlowCardReq) GetGrantType() uint32 {
	if m != nil {
		return m.GrantType
	}
	return 0
}

func (m *GrantFlowCardReq) GetInfoList() []*FlowCardGrantInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type GrantFlowCardResp struct {
	ErrIdList            []string `protobuf:"bytes,1,rep,name=err_id_list,json=errIdList,proto3" json:"err_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GrantFlowCardResp) Reset()         { *m = GrantFlowCardResp{} }
func (m *GrantFlowCardResp) String() string { return proto.CompactTextString(m) }
func (*GrantFlowCardResp) ProtoMessage()    {}
func (*GrantFlowCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{15}
}
func (m *GrantFlowCardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GrantFlowCardResp.Unmarshal(m, b)
}
func (m *GrantFlowCardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GrantFlowCardResp.Marshal(b, m, deterministic)
}
func (dst *GrantFlowCardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GrantFlowCardResp.Merge(dst, src)
}
func (m *GrantFlowCardResp) XXX_Size() int {
	return xxx_messageInfo_GrantFlowCardResp.Size(m)
}
func (m *GrantFlowCardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GrantFlowCardResp.DiscardUnknown(m)
}

var xxx_messageInfo_GrantFlowCardResp proto.InternalMessageInfo

func (m *GrantFlowCardResp) GetErrIdList() []string {
	if m != nil {
		return m.ErrIdList
	}
	return nil
}

// 回收流量卡
type ReclaimGrantedFlowCardReq struct {
	GrantType            uint32   `protobuf:"varint,1,opt,name=grant_type,json=grantType,proto3" json:"grant_type,omitempty"`
	GrantId              uint32   `protobuf:"varint,2,opt,name=grant_id,json=grantId,proto3" json:"grant_id,omitempty"`
	ReclaimCnt           uint32   `protobuf:"varint,3,opt,name=reclaim_cnt,json=reclaimCnt,proto3" json:"reclaim_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReclaimGrantedFlowCardReq) Reset()         { *m = ReclaimGrantedFlowCardReq{} }
func (m *ReclaimGrantedFlowCardReq) String() string { return proto.CompactTextString(m) }
func (*ReclaimGrantedFlowCardReq) ProtoMessage()    {}
func (*ReclaimGrantedFlowCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{16}
}
func (m *ReclaimGrantedFlowCardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReclaimGrantedFlowCardReq.Unmarshal(m, b)
}
func (m *ReclaimGrantedFlowCardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReclaimGrantedFlowCardReq.Marshal(b, m, deterministic)
}
func (dst *ReclaimGrantedFlowCardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReclaimGrantedFlowCardReq.Merge(dst, src)
}
func (m *ReclaimGrantedFlowCardReq) XXX_Size() int {
	return xxx_messageInfo_ReclaimGrantedFlowCardReq.Size(m)
}
func (m *ReclaimGrantedFlowCardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReclaimGrantedFlowCardReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReclaimGrantedFlowCardReq proto.InternalMessageInfo

func (m *ReclaimGrantedFlowCardReq) GetGrantType() uint32 {
	if m != nil {
		return m.GrantType
	}
	return 0
}

func (m *ReclaimGrantedFlowCardReq) GetGrantId() uint32 {
	if m != nil {
		return m.GrantId
	}
	return 0
}

func (m *ReclaimGrantedFlowCardReq) GetReclaimCnt() uint32 {
	if m != nil {
		return m.ReclaimCnt
	}
	return 0
}

type ReclaimGrantedFlowCardResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReclaimGrantedFlowCardResp) Reset()         { *m = ReclaimGrantedFlowCardResp{} }
func (m *ReclaimGrantedFlowCardResp) String() string { return proto.CompactTextString(m) }
func (*ReclaimGrantedFlowCardResp) ProtoMessage()    {}
func (*ReclaimGrantedFlowCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{17}
}
func (m *ReclaimGrantedFlowCardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReclaimGrantedFlowCardResp.Unmarshal(m, b)
}
func (m *ReclaimGrantedFlowCardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReclaimGrantedFlowCardResp.Marshal(b, m, deterministic)
}
func (dst *ReclaimGrantedFlowCardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReclaimGrantedFlowCardResp.Merge(dst, src)
}
func (m *ReclaimGrantedFlowCardResp) XXX_Size() int {
	return xxx_messageInfo_ReclaimGrantedFlowCardResp.Size(m)
}
func (m *ReclaimGrantedFlowCardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReclaimGrantedFlowCardResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReclaimGrantedFlowCardResp proto.InternalMessageInfo

// 回收流量卡
type BanGrantedFlowCardReq struct {
	GrantType            uint32   `protobuf:"varint,1,opt,name=grant_type,json=grantType,proto3" json:"grant_type,omitempty"`
	GrantId              uint32   `protobuf:"varint,2,opt,name=grant_id,json=grantId,proto3" json:"grant_id,omitempty"`
	BeginTs              uint32   `protobuf:"varint,3,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,4,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BanGrantedFlowCardReq) Reset()         { *m = BanGrantedFlowCardReq{} }
func (m *BanGrantedFlowCardReq) String() string { return proto.CompactTextString(m) }
func (*BanGrantedFlowCardReq) ProtoMessage()    {}
func (*BanGrantedFlowCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{18}
}
func (m *BanGrantedFlowCardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BanGrantedFlowCardReq.Unmarshal(m, b)
}
func (m *BanGrantedFlowCardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BanGrantedFlowCardReq.Marshal(b, m, deterministic)
}
func (dst *BanGrantedFlowCardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BanGrantedFlowCardReq.Merge(dst, src)
}
func (m *BanGrantedFlowCardReq) XXX_Size() int {
	return xxx_messageInfo_BanGrantedFlowCardReq.Size(m)
}
func (m *BanGrantedFlowCardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BanGrantedFlowCardReq.DiscardUnknown(m)
}

var xxx_messageInfo_BanGrantedFlowCardReq proto.InternalMessageInfo

func (m *BanGrantedFlowCardReq) GetGrantType() uint32 {
	if m != nil {
		return m.GrantType
	}
	return 0
}

func (m *BanGrantedFlowCardReq) GetGrantId() uint32 {
	if m != nil {
		return m.GrantId
	}
	return 0
}

func (m *BanGrantedFlowCardReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *BanGrantedFlowCardReq) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

type BanGrantedFlowCardResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BanGrantedFlowCardResp) Reset()         { *m = BanGrantedFlowCardResp{} }
func (m *BanGrantedFlowCardResp) String() string { return proto.CompactTextString(m) }
func (*BanGrantedFlowCardResp) ProtoMessage()    {}
func (*BanGrantedFlowCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{19}
}
func (m *BanGrantedFlowCardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BanGrantedFlowCardResp.Unmarshal(m, b)
}
func (m *BanGrantedFlowCardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BanGrantedFlowCardResp.Marshal(b, m, deterministic)
}
func (dst *BanGrantedFlowCardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BanGrantedFlowCardResp.Merge(dst, src)
}
func (m *BanGrantedFlowCardResp) XXX_Size() int {
	return xxx_messageInfo_BanGrantedFlowCardResp.Size(m)
}
func (m *BanGrantedFlowCardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BanGrantedFlowCardResp.DiscardUnknown(m)
}

var xxx_messageInfo_BanGrantedFlowCardResp proto.InternalMessageInfo

// 时间段
type TimeSection struct {
	BeginTime            uint32   `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TimeSection) Reset()         { *m = TimeSection{} }
func (m *TimeSection) String() string { return proto.CompactTextString(m) }
func (*TimeSection) ProtoMessage()    {}
func (*TimeSection) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{20}
}
func (m *TimeSection) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TimeSection.Unmarshal(m, b)
}
func (m *TimeSection) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TimeSection.Marshal(b, m, deterministic)
}
func (dst *TimeSection) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TimeSection.Merge(dst, src)
}
func (m *TimeSection) XXX_Size() int {
	return xxx_messageInfo_TimeSection.Size(m)
}
func (m *TimeSection) XXX_DiscardUnknown() {
	xxx_messageInfo_TimeSection.DiscardUnknown(m)
}

var xxx_messageInfo_TimeSection proto.InternalMessageInfo

func (m *TimeSection) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *TimeSection) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

// 推荐库基础信息
type PrepareBaseInfo struct {
	ChannelId            uint32       `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TagId                uint32       `protobuf:"varint,2,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	NewLevel             uint32       `protobuf:"varint,3,opt,name=new_level,json=newLevel,proto3" json:"new_level,omitempty"`
	NewSection           *TimeSection `protobuf:"bytes,4,opt,name=new_section,json=newSection,proto3" json:"new_section,omitempty"`
	OldLevel             uint32       `protobuf:"varint,5,opt,name=old_level,json=oldLevel,proto3" json:"old_level,omitempty"`
	OldSection           *TimeSection `protobuf:"bytes,6,opt,name=old_section,json=oldSection,proto3" json:"old_section,omitempty"`
	QuickLevel           uint32       `protobuf:"varint,7,opt,name=quick_level,json=quickLevel,proto3" json:"quick_level,omitempty"`
	QuickSection         *TimeSection `protobuf:"bytes,8,opt,name=quick_section,json=quickSection,proto3" json:"quick_section,omitempty"`
	UpdateTs             uint32       `protobuf:"varint,9,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	PrepareType          uint32       `protobuf:"varint,10,opt,name=prepare_type,json=prepareType,proto3" json:"prepare_type,omitempty"`
	Id                   uint32       `protobuf:"varint,11,opt,name=id,proto3" json:"id,omitempty"`
	Operator             string       `protobuf:"bytes,12,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PrepareBaseInfo) Reset()         { *m = PrepareBaseInfo{} }
func (m *PrepareBaseInfo) String() string { return proto.CompactTextString(m) }
func (*PrepareBaseInfo) ProtoMessage()    {}
func (*PrepareBaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{21}
}
func (m *PrepareBaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PrepareBaseInfo.Unmarshal(m, b)
}
func (m *PrepareBaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PrepareBaseInfo.Marshal(b, m, deterministic)
}
func (dst *PrepareBaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PrepareBaseInfo.Merge(dst, src)
}
func (m *PrepareBaseInfo) XXX_Size() int {
	return xxx_messageInfo_PrepareBaseInfo.Size(m)
}
func (m *PrepareBaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PrepareBaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PrepareBaseInfo proto.InternalMessageInfo

func (m *PrepareBaseInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PrepareBaseInfo) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *PrepareBaseInfo) GetNewLevel() uint32 {
	if m != nil {
		return m.NewLevel
	}
	return 0
}

func (m *PrepareBaseInfo) GetNewSection() *TimeSection {
	if m != nil {
		return m.NewSection
	}
	return nil
}

func (m *PrepareBaseInfo) GetOldLevel() uint32 {
	if m != nil {
		return m.OldLevel
	}
	return 0
}

func (m *PrepareBaseInfo) GetOldSection() *TimeSection {
	if m != nil {
		return m.OldSection
	}
	return nil
}

func (m *PrepareBaseInfo) GetQuickLevel() uint32 {
	if m != nil {
		return m.QuickLevel
	}
	return 0
}

func (m *PrepareBaseInfo) GetQuickSection() *TimeSection {
	if m != nil {
		return m.QuickSection
	}
	return nil
}

func (m *PrepareBaseInfo) GetUpdateTs() uint32 {
	if m != nil {
		return m.UpdateTs
	}
	return 0
}

func (m *PrepareBaseInfo) GetPrepareType() uint32 {
	if m != nil {
		return m.PrepareType
	}
	return 0
}

func (m *PrepareBaseInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PrepareBaseInfo) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

// 房间信息
type PgcBaseInfo struct {
	ChannelId            uint32        `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelViewId        string        `protobuf:"bytes,2,opt,name=channel_view_id,json=channelViewId,proto3" json:"channel_view_id,omitempty"`
	ChannelName          string        `protobuf:"bytes,3,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	OwnerInfo            *UserBaseInfo `protobuf:"bytes,4,opt,name=owner_info,json=ownerInfo,proto3" json:"owner_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PgcBaseInfo) Reset()         { *m = PgcBaseInfo{} }
func (m *PgcBaseInfo) String() string { return proto.CompactTextString(m) }
func (*PgcBaseInfo) ProtoMessage()    {}
func (*PgcBaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{22}
}
func (m *PgcBaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcBaseInfo.Unmarshal(m, b)
}
func (m *PgcBaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcBaseInfo.Marshal(b, m, deterministic)
}
func (dst *PgcBaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcBaseInfo.Merge(dst, src)
}
func (m *PgcBaseInfo) XXX_Size() int {
	return xxx_messageInfo_PgcBaseInfo.Size(m)
}
func (m *PgcBaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcBaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PgcBaseInfo proto.InternalMessageInfo

func (m *PgcBaseInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PgcBaseInfo) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

func (m *PgcBaseInfo) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *PgcBaseInfo) GetOwnerInfo() *UserBaseInfo {
	if m != nil {
		return m.OwnerInfo
	}
	return nil
}

// 公会信息
type GuildBaseInfo struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GuildName            string   `protobuf:"bytes,2,opt,name=guild_name,json=guildName,proto3" json:"guild_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildBaseInfo) Reset()         { *m = GuildBaseInfo{} }
func (m *GuildBaseInfo) String() string { return proto.CompactTextString(m) }
func (*GuildBaseInfo) ProtoMessage()    {}
func (*GuildBaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{23}
}
func (m *GuildBaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildBaseInfo.Unmarshal(m, b)
}
func (m *GuildBaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildBaseInfo.Marshal(b, m, deterministic)
}
func (dst *GuildBaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildBaseInfo.Merge(dst, src)
}
func (m *GuildBaseInfo) XXX_Size() int {
	return xxx_messageInfo_GuildBaseInfo.Size(m)
}
func (m *GuildBaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildBaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GuildBaseInfo proto.InternalMessageInfo

func (m *GuildBaseInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildBaseInfo) GetGuildName() string {
	if m != nil {
		return m.GuildName
	}
	return ""
}

// 用户信息
type UserBaseInfo struct {
	Ttid                 string   `protobuf:"bytes,1,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserBaseInfo) Reset()         { *m = UserBaseInfo{} }
func (m *UserBaseInfo) String() string { return proto.CompactTextString(m) }
func (*UserBaseInfo) ProtoMessage()    {}
func (*UserBaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{24}
}
func (m *UserBaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserBaseInfo.Unmarshal(m, b)
}
func (m *UserBaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserBaseInfo.Marshal(b, m, deterministic)
}
func (dst *UserBaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserBaseInfo.Merge(dst, src)
}
func (m *UserBaseInfo) XXX_Size() int {
	return xxx_messageInfo_UserBaseInfo.Size(m)
}
func (m *UserBaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserBaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserBaseInfo proto.InternalMessageInfo

func (m *UserBaseInfo) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *UserBaseInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

// 房间推荐信息
type PrepareChannelInfo struct {
	BaseInfo             *PrepareBaseInfo `protobuf:"bytes,1,opt,name=base_info,json=baseInfo,proto3" json:"base_info,omitempty"`
	ChannelInfo          *PgcBaseInfo     `protobuf:"bytes,2,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	GuildInfo            *GuildBaseInfo   `protobuf:"bytes,3,opt,name=guild_info,json=guildInfo,proto3" json:"guild_info,omitempty"`
	SanctionStatus       uint32           `protobuf:"varint,4,opt,name=sanction_status,json=sanctionStatus,proto3" json:"sanction_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *PrepareChannelInfo) Reset()         { *m = PrepareChannelInfo{} }
func (m *PrepareChannelInfo) String() string { return proto.CompactTextString(m) }
func (*PrepareChannelInfo) ProtoMessage()    {}
func (*PrepareChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{25}
}
func (m *PrepareChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PrepareChannelInfo.Unmarshal(m, b)
}
func (m *PrepareChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PrepareChannelInfo.Marshal(b, m, deterministic)
}
func (dst *PrepareChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PrepareChannelInfo.Merge(dst, src)
}
func (m *PrepareChannelInfo) XXX_Size() int {
	return xxx_messageInfo_PrepareChannelInfo.Size(m)
}
func (m *PrepareChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PrepareChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PrepareChannelInfo proto.InternalMessageInfo

func (m *PrepareChannelInfo) GetBaseInfo() *PrepareBaseInfo {
	if m != nil {
		return m.BaseInfo
	}
	return nil
}

func (m *PrepareChannelInfo) GetChannelInfo() *PgcBaseInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

func (m *PrepareChannelInfo) GetGuildInfo() *GuildBaseInfo {
	if m != nil {
		return m.GuildInfo
	}
	return nil
}

func (m *PrepareChannelInfo) GetSanctionStatus() uint32 {
	if m != nil {
		return m.SanctionStatus
	}
	return 0
}

// 获取推荐库列表
type GetPrepareChannelListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPrepareChannelListReq) Reset()         { *m = GetPrepareChannelListReq{} }
func (m *GetPrepareChannelListReq) String() string { return proto.CompactTextString(m) }
func (*GetPrepareChannelListReq) ProtoMessage()    {}
func (*GetPrepareChannelListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{26}
}
func (m *GetPrepareChannelListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPrepareChannelListReq.Unmarshal(m, b)
}
func (m *GetPrepareChannelListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPrepareChannelListReq.Marshal(b, m, deterministic)
}
func (dst *GetPrepareChannelListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPrepareChannelListReq.Merge(dst, src)
}
func (m *GetPrepareChannelListReq) XXX_Size() int {
	return xxx_messageInfo_GetPrepareChannelListReq.Size(m)
}
func (m *GetPrepareChannelListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPrepareChannelListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPrepareChannelListReq proto.InternalMessageInfo

type GetPrepareChannelListResp struct {
	PrepareChannelList   []*PrepareChannelInfo `protobuf:"bytes,1,rep,name=prepare_channel_list,json=prepareChannelList,proto3" json:"prepare_channel_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetPrepareChannelListResp) Reset()         { *m = GetPrepareChannelListResp{} }
func (m *GetPrepareChannelListResp) String() string { return proto.CompactTextString(m) }
func (*GetPrepareChannelListResp) ProtoMessage()    {}
func (*GetPrepareChannelListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{27}
}
func (m *GetPrepareChannelListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPrepareChannelListResp.Unmarshal(m, b)
}
func (m *GetPrepareChannelListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPrepareChannelListResp.Marshal(b, m, deterministic)
}
func (dst *GetPrepareChannelListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPrepareChannelListResp.Merge(dst, src)
}
func (m *GetPrepareChannelListResp) XXX_Size() int {
	return xxx_messageInfo_GetPrepareChannelListResp.Size(m)
}
func (m *GetPrepareChannelListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPrepareChannelListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPrepareChannelListResp proto.InternalMessageInfo

func (m *GetPrepareChannelListResp) GetPrepareChannelList() []*PrepareChannelInfo {
	if m != nil {
		return m.PrepareChannelList
	}
	return nil
}

// 设置推荐库房间信息
type SetPrepareChannelReq struct {
	InfoList             []*PrepareBaseInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	Operator             string             `protobuf:"bytes,2,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SetPrepareChannelReq) Reset()         { *m = SetPrepareChannelReq{} }
func (m *SetPrepareChannelReq) String() string { return proto.CompactTextString(m) }
func (*SetPrepareChannelReq) ProtoMessage()    {}
func (*SetPrepareChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{28}
}
func (m *SetPrepareChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPrepareChannelReq.Unmarshal(m, b)
}
func (m *SetPrepareChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPrepareChannelReq.Marshal(b, m, deterministic)
}
func (dst *SetPrepareChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPrepareChannelReq.Merge(dst, src)
}
func (m *SetPrepareChannelReq) XXX_Size() int {
	return xxx_messageInfo_SetPrepareChannelReq.Size(m)
}
func (m *SetPrepareChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPrepareChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetPrepareChannelReq proto.InternalMessageInfo

func (m *SetPrepareChannelReq) GetInfoList() []*PrepareBaseInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *SetPrepareChannelReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type SetPrepareChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPrepareChannelResp) Reset()         { *m = SetPrepareChannelResp{} }
func (m *SetPrepareChannelResp) String() string { return proto.CompactTextString(m) }
func (*SetPrepareChannelResp) ProtoMessage()    {}
func (*SetPrepareChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{29}
}
func (m *SetPrepareChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPrepareChannelResp.Unmarshal(m, b)
}
func (m *SetPrepareChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPrepareChannelResp.Marshal(b, m, deterministic)
}
func (dst *SetPrepareChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPrepareChannelResp.Merge(dst, src)
}
func (m *SetPrepareChannelResp) XXX_Size() int {
	return xxx_messageInfo_SetPrepareChannelResp.Size(m)
}
func (m *SetPrepareChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPrepareChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetPrepareChannelResp proto.InternalMessageInfo

// 删除推荐库房间信息
type DelPrepareChannelReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Operator             string   `protobuf:"bytes,2,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPrepareChannelReq) Reset()         { *m = DelPrepareChannelReq{} }
func (m *DelPrepareChannelReq) String() string { return proto.CompactTextString(m) }
func (*DelPrepareChannelReq) ProtoMessage()    {}
func (*DelPrepareChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{30}
}
func (m *DelPrepareChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPrepareChannelReq.Unmarshal(m, b)
}
func (m *DelPrepareChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPrepareChannelReq.Marshal(b, m, deterministic)
}
func (dst *DelPrepareChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPrepareChannelReq.Merge(dst, src)
}
func (m *DelPrepareChannelReq) XXX_Size() int {
	return xxx_messageInfo_DelPrepareChannelReq.Size(m)
}
func (m *DelPrepareChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPrepareChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelPrepareChannelReq proto.InternalMessageInfo

func (m *DelPrepareChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *DelPrepareChannelReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type DelPrepareChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPrepareChannelResp) Reset()         { *m = DelPrepareChannelResp{} }
func (m *DelPrepareChannelResp) String() string { return proto.CompactTextString(m) }
func (*DelPrepareChannelResp) ProtoMessage()    {}
func (*DelPrepareChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{31}
}
func (m *DelPrepareChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPrepareChannelResp.Unmarshal(m, b)
}
func (m *DelPrepareChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPrepareChannelResp.Marshal(b, m, deterministic)
}
func (dst *DelPrepareChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPrepareChannelResp.Merge(dst, src)
}
func (m *DelPrepareChannelResp) XXX_Size() int {
	return xxx_messageInfo_DelPrepareChannelResp.Size(m)
}
func (m *DelPrepareChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPrepareChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelPrepareChannelResp proto.InternalMessageInfo

// 获取公会公开房列表
type GetGuildChannelListReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildChannelListReq) Reset()         { *m = GetGuildChannelListReq{} }
func (m *GetGuildChannelListReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildChannelListReq) ProtoMessage()    {}
func (*GetGuildChannelListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{32}
}
func (m *GetGuildChannelListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildChannelListReq.Unmarshal(m, b)
}
func (m *GetGuildChannelListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildChannelListReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildChannelListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildChannelListReq.Merge(dst, src)
}
func (m *GetGuildChannelListReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildChannelListReq.Size(m)
}
func (m *GetGuildChannelListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildChannelListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildChannelListReq proto.InternalMessageInfo

func (m *GetGuildChannelListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetGuildChannelListResp struct {
	InfoList             []*PrepareChannelInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetGuildChannelListResp) Reset()         { *m = GetGuildChannelListResp{} }
func (m *GetGuildChannelListResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildChannelListResp) ProtoMessage()    {}
func (*GetGuildChannelListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{33}
}
func (m *GetGuildChannelListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildChannelListResp.Unmarshal(m, b)
}
func (m *GetGuildChannelListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildChannelListResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildChannelListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildChannelListResp.Merge(dst, src)
}
func (m *GetGuildChannelListResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildChannelListResp.Size(m)
}
func (m *GetGuildChannelListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildChannelListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildChannelListResp proto.InternalMessageInfo

func (m *GetGuildChannelListResp) GetInfoList() []*PrepareChannelInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

// 获取指定房间还没生效列表
type GetChannelPrepareListReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelPrepareListReq) Reset()         { *m = GetChannelPrepareListReq{} }
func (m *GetChannelPrepareListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelPrepareListReq) ProtoMessage()    {}
func (*GetChannelPrepareListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{34}
}
func (m *GetChannelPrepareListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelPrepareListReq.Unmarshal(m, b)
}
func (m *GetChannelPrepareListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelPrepareListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelPrepareListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelPrepareListReq.Merge(dst, src)
}
func (m *GetChannelPrepareListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelPrepareListReq.Size(m)
}
func (m *GetChannelPrepareListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelPrepareListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelPrepareListReq proto.InternalMessageInfo

func (m *GetChannelPrepareListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelPrepareListResp struct {
	PrepareChannelList   []*PrepareChannelInfo `protobuf:"bytes,1,rep,name=prepare_channel_list,json=prepareChannelList,proto3" json:"prepare_channel_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetChannelPrepareListResp) Reset()         { *m = GetChannelPrepareListResp{} }
func (m *GetChannelPrepareListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelPrepareListResp) ProtoMessage()    {}
func (*GetChannelPrepareListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{35}
}
func (m *GetChannelPrepareListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelPrepareListResp.Unmarshal(m, b)
}
func (m *GetChannelPrepareListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelPrepareListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelPrepareListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelPrepareListResp.Merge(dst, src)
}
func (m *GetChannelPrepareListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelPrepareListResp.Size(m)
}
func (m *GetChannelPrepareListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelPrepareListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelPrepareListResp proto.InternalMessageInfo

func (m *GetChannelPrepareListResp) GetPrepareChannelList() []*PrepareChannelInfo {
	if m != nil {
		return m.PrepareChannelList
	}
	return nil
}

// 获取推荐库备份信息
type GetPrepareBackupListReq struct {
	Version              int64    `protobuf:"varint,1,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPrepareBackupListReq) Reset()         { *m = GetPrepareBackupListReq{} }
func (m *GetPrepareBackupListReq) String() string { return proto.CompactTextString(m) }
func (*GetPrepareBackupListReq) ProtoMessage()    {}
func (*GetPrepareBackupListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{36}
}
func (m *GetPrepareBackupListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPrepareBackupListReq.Unmarshal(m, b)
}
func (m *GetPrepareBackupListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPrepareBackupListReq.Marshal(b, m, deterministic)
}
func (dst *GetPrepareBackupListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPrepareBackupListReq.Merge(dst, src)
}
func (m *GetPrepareBackupListReq) XXX_Size() int {
	return xxx_messageInfo_GetPrepareBackupListReq.Size(m)
}
func (m *GetPrepareBackupListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPrepareBackupListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPrepareBackupListReq proto.InternalMessageInfo

func (m *GetPrepareBackupListReq) GetVersion() int64 {
	if m != nil {
		return m.Version
	}
	return 0
}

type GetPrepareBackupListResp struct {
	InfoList             []*PrepareChannelInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetPrepareBackupListResp) Reset()         { *m = GetPrepareBackupListResp{} }
func (m *GetPrepareBackupListResp) String() string { return proto.CompactTextString(m) }
func (*GetPrepareBackupListResp) ProtoMessage()    {}
func (*GetPrepareBackupListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{37}
}
func (m *GetPrepareBackupListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPrepareBackupListResp.Unmarshal(m, b)
}
func (m *GetPrepareBackupListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPrepareBackupListResp.Marshal(b, m, deterministic)
}
func (dst *GetPrepareBackupListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPrepareBackupListResp.Merge(dst, src)
}
func (m *GetPrepareBackupListResp) XXX_Size() int {
	return xxx_messageInfo_GetPrepareBackupListResp.Size(m)
}
func (m *GetPrepareBackupListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPrepareBackupListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPrepareBackupListResp proto.InternalMessageInfo

func (m *GetPrepareBackupListResp) GetInfoList() []*PrepareChannelInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

// 房间推荐信息
type PrepareOperRecord struct {
	OperType             uint32              `protobuf:"varint,1,opt,name=oper_type,json=operType,proto3" json:"oper_type,omitempty"`
	Info                 *PrepareChannelInfo `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *PrepareOperRecord) Reset()         { *m = PrepareOperRecord{} }
func (m *PrepareOperRecord) String() string { return proto.CompactTextString(m) }
func (*PrepareOperRecord) ProtoMessage()    {}
func (*PrepareOperRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{38}
}
func (m *PrepareOperRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PrepareOperRecord.Unmarshal(m, b)
}
func (m *PrepareOperRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PrepareOperRecord.Marshal(b, m, deterministic)
}
func (dst *PrepareOperRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PrepareOperRecord.Merge(dst, src)
}
func (m *PrepareOperRecord) XXX_Size() int {
	return xxx_messageInfo_PrepareOperRecord.Size(m)
}
func (m *PrepareOperRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_PrepareOperRecord.DiscardUnknown(m)
}

var xxx_messageInfo_PrepareOperRecord proto.InternalMessageInfo

func (m *PrepareOperRecord) GetOperType() uint32 {
	if m != nil {
		return m.OperType
	}
	return 0
}

func (m *PrepareOperRecord) GetInfo() *PrepareChannelInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type GetPrepareOperRecordListReq struct {
	Page                 uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPrepareOperRecordListReq) Reset()         { *m = GetPrepareOperRecordListReq{} }
func (m *GetPrepareOperRecordListReq) String() string { return proto.CompactTextString(m) }
func (*GetPrepareOperRecordListReq) ProtoMessage()    {}
func (*GetPrepareOperRecordListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{39}
}
func (m *GetPrepareOperRecordListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPrepareOperRecordListReq.Unmarshal(m, b)
}
func (m *GetPrepareOperRecordListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPrepareOperRecordListReq.Marshal(b, m, deterministic)
}
func (dst *GetPrepareOperRecordListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPrepareOperRecordListReq.Merge(dst, src)
}
func (m *GetPrepareOperRecordListReq) XXX_Size() int {
	return xxx_messageInfo_GetPrepareOperRecordListReq.Size(m)
}
func (m *GetPrepareOperRecordListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPrepareOperRecordListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPrepareOperRecordListReq proto.InternalMessageInfo

func (m *GetPrepareOperRecordListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetPrepareOperRecordListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetPrepareOperRecordListResp struct {
	RecordList           []*PrepareOperRecord `protobuf:"bytes,1,rep,name=record_list,json=recordList,proto3" json:"record_list,omitempty"`
	TotalCnt             uint32               `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetPrepareOperRecordListResp) Reset()         { *m = GetPrepareOperRecordListResp{} }
func (m *GetPrepareOperRecordListResp) String() string { return proto.CompactTextString(m) }
func (*GetPrepareOperRecordListResp) ProtoMessage()    {}
func (*GetPrepareOperRecordListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{40}
}
func (m *GetPrepareOperRecordListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPrepareOperRecordListResp.Unmarshal(m, b)
}
func (m *GetPrepareOperRecordListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPrepareOperRecordListResp.Marshal(b, m, deterministic)
}
func (dst *GetPrepareOperRecordListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPrepareOperRecordListResp.Merge(dst, src)
}
func (m *GetPrepareOperRecordListResp) XXX_Size() int {
	return xxx_messageInfo_GetPrepareOperRecordListResp.Size(m)
}
func (m *GetPrepareOperRecordListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPrepareOperRecordListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPrepareOperRecordListResp proto.InternalMessageInfo

func (m *GetPrepareOperRecordListResp) GetRecordList() []*PrepareOperRecord {
	if m != nil {
		return m.RecordList
	}
	return nil
}

func (m *GetPrepareOperRecordListResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

// 快速进房豆腐块配置
type DisplaySceneInfo struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Type                 uint32   `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	Title                string   `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	SceneId              uint32   `protobuf:"varint,4,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	MarketIdList         []uint32 `protobuf:"varint,5,rep,packed,name=market_id_list,json=marketIdList,proto3" json:"market_id_list,omitempty"`
	Platform             uint32   `protobuf:"varint,6,opt,name=platform,proto3" json:"platform,omitempty"`
	MinVersion           string   `protobuf:"bytes,7,opt,name=min_version,json=minVersion,proto3" json:"min_version,omitempty"`
	BeginTs              uint32   `protobuf:"varint,8,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,9,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	SceneType            uint32   `protobuf:"varint,10,opt,name=scene_type,json=sceneType,proto3" json:"scene_type,omitempty"`
	TagId                uint32   `protobuf:"varint,11,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	JumpUrl              string   `protobuf:"bytes,12,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	Weight               uint32   `protobuf:"varint,13,opt,name=weight,proto3" json:"weight,omitempty"`
	BgImg                string   `protobuf:"bytes,14,opt,name=bg_img,json=bgImg,proto3" json:"bg_img,omitempty"`
	SmallImg             string   `protobuf:"bytes,15,opt,name=small_img,json=smallImg,proto3" json:"small_img,omitempty"`
	StaticImg            string   `protobuf:"bytes,16,opt,name=static_img,json=staticImg,proto3" json:"static_img,omitempty"`
	DynamicUrl           string   `protobuf:"bytes,17,opt,name=dynamic_url,json=dynamicUrl,proto3" json:"dynamic_url,omitempty"`
	DynamicMd5           string   `protobuf:"bytes,18,opt,name=dynamic_md5,json=dynamicMd5,proto3" json:"dynamic_md5,omitempty"`
	DynamicJsonKey       string   `protobuf:"bytes,19,opt,name=dynamic_json_key,json=dynamicJsonKey,proto3" json:"dynamic_json_key,omitempty"`
	Operator             string   `protobuf:"bytes,20,opt,name=operator,proto3" json:"operator,omitempty"`
	UpdateTs             uint32   `protobuf:"varint,21,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	Status               uint32   `protobuf:"varint,22,opt,name=status,proto3" json:"status,omitempty"`
	TimeType             uint32   `protobuf:"varint,23,opt,name=time_type,json=timeType,proto3" json:"time_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisplaySceneInfo) Reset()         { *m = DisplaySceneInfo{} }
func (m *DisplaySceneInfo) String() string { return proto.CompactTextString(m) }
func (*DisplaySceneInfo) ProtoMessage()    {}
func (*DisplaySceneInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{41}
}
func (m *DisplaySceneInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisplaySceneInfo.Unmarshal(m, b)
}
func (m *DisplaySceneInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisplaySceneInfo.Marshal(b, m, deterministic)
}
func (dst *DisplaySceneInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisplaySceneInfo.Merge(dst, src)
}
func (m *DisplaySceneInfo) XXX_Size() int {
	return xxx_messageInfo_DisplaySceneInfo.Size(m)
}
func (m *DisplaySceneInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DisplaySceneInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DisplaySceneInfo proto.InternalMessageInfo

func (m *DisplaySceneInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DisplaySceneInfo) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *DisplaySceneInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *DisplaySceneInfo) GetSceneId() uint32 {
	if m != nil {
		return m.SceneId
	}
	return 0
}

func (m *DisplaySceneInfo) GetMarketIdList() []uint32 {
	if m != nil {
		return m.MarketIdList
	}
	return nil
}

func (m *DisplaySceneInfo) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

func (m *DisplaySceneInfo) GetMinVersion() string {
	if m != nil {
		return m.MinVersion
	}
	return ""
}

func (m *DisplaySceneInfo) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *DisplaySceneInfo) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *DisplaySceneInfo) GetSceneType() uint32 {
	if m != nil {
		return m.SceneType
	}
	return 0
}

func (m *DisplaySceneInfo) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *DisplaySceneInfo) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *DisplaySceneInfo) GetWeight() uint32 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *DisplaySceneInfo) GetBgImg() string {
	if m != nil {
		return m.BgImg
	}
	return ""
}

func (m *DisplaySceneInfo) GetSmallImg() string {
	if m != nil {
		return m.SmallImg
	}
	return ""
}

func (m *DisplaySceneInfo) GetStaticImg() string {
	if m != nil {
		return m.StaticImg
	}
	return ""
}

func (m *DisplaySceneInfo) GetDynamicUrl() string {
	if m != nil {
		return m.DynamicUrl
	}
	return ""
}

func (m *DisplaySceneInfo) GetDynamicMd5() string {
	if m != nil {
		return m.DynamicMd5
	}
	return ""
}

func (m *DisplaySceneInfo) GetDynamicJsonKey() string {
	if m != nil {
		return m.DynamicJsonKey
	}
	return ""
}

func (m *DisplaySceneInfo) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *DisplaySceneInfo) GetUpdateTs() uint32 {
	if m != nil {
		return m.UpdateTs
	}
	return 0
}

func (m *DisplaySceneInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *DisplaySceneInfo) GetTimeType() uint32 {
	if m != nil {
		return m.TimeType
	}
	return 0
}

// 查询列表
type GetDisplaySceneInfoListReq struct {
	Type                 uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	SceneId              uint32   `protobuf:"varint,2,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	MarketId             uint32   `protobuf:"varint,3,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	Status               uint32   `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	BeginTs              uint32   `protobuf:"varint,5,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,6,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	Page                 uint32   `protobuf:"varint,7,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,8,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDisplaySceneInfoListReq) Reset()         { *m = GetDisplaySceneInfoListReq{} }
func (m *GetDisplaySceneInfoListReq) String() string { return proto.CompactTextString(m) }
func (*GetDisplaySceneInfoListReq) ProtoMessage()    {}
func (*GetDisplaySceneInfoListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{42}
}
func (m *GetDisplaySceneInfoListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDisplaySceneInfoListReq.Unmarshal(m, b)
}
func (m *GetDisplaySceneInfoListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDisplaySceneInfoListReq.Marshal(b, m, deterministic)
}
func (dst *GetDisplaySceneInfoListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDisplaySceneInfoListReq.Merge(dst, src)
}
func (m *GetDisplaySceneInfoListReq) XXX_Size() int {
	return xxx_messageInfo_GetDisplaySceneInfoListReq.Size(m)
}
func (m *GetDisplaySceneInfoListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDisplaySceneInfoListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetDisplaySceneInfoListReq proto.InternalMessageInfo

func (m *GetDisplaySceneInfoListReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GetDisplaySceneInfoListReq) GetSceneId() uint32 {
	if m != nil {
		return m.SceneId
	}
	return 0
}

func (m *GetDisplaySceneInfoListReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *GetDisplaySceneInfoListReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetDisplaySceneInfoListReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *GetDisplaySceneInfoListReq) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *GetDisplaySceneInfoListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetDisplaySceneInfoListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetDisplaySceneInfoListResp struct {
	InfoList             []*DisplaySceneInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	TotalCnt             uint32              `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetDisplaySceneInfoListResp) Reset()         { *m = GetDisplaySceneInfoListResp{} }
func (m *GetDisplaySceneInfoListResp) String() string { return proto.CompactTextString(m) }
func (*GetDisplaySceneInfoListResp) ProtoMessage()    {}
func (*GetDisplaySceneInfoListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{43}
}
func (m *GetDisplaySceneInfoListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDisplaySceneInfoListResp.Unmarshal(m, b)
}
func (m *GetDisplaySceneInfoListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDisplaySceneInfoListResp.Marshal(b, m, deterministic)
}
func (dst *GetDisplaySceneInfoListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDisplaySceneInfoListResp.Merge(dst, src)
}
func (m *GetDisplaySceneInfoListResp) XXX_Size() int {
	return xxx_messageInfo_GetDisplaySceneInfoListResp.Size(m)
}
func (m *GetDisplaySceneInfoListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDisplaySceneInfoListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetDisplaySceneInfoListResp proto.InternalMessageInfo

func (m *GetDisplaySceneInfoListResp) GetInfoList() []*DisplaySceneInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetDisplaySceneInfoListResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

// 增加配置
type AddDisplaySceneInfoReq struct {
	Info                 *DisplaySceneInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *AddDisplaySceneInfoReq) Reset()         { *m = AddDisplaySceneInfoReq{} }
func (m *AddDisplaySceneInfoReq) String() string { return proto.CompactTextString(m) }
func (*AddDisplaySceneInfoReq) ProtoMessage()    {}
func (*AddDisplaySceneInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{44}
}
func (m *AddDisplaySceneInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddDisplaySceneInfoReq.Unmarshal(m, b)
}
func (m *AddDisplaySceneInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddDisplaySceneInfoReq.Marshal(b, m, deterministic)
}
func (dst *AddDisplaySceneInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddDisplaySceneInfoReq.Merge(dst, src)
}
func (m *AddDisplaySceneInfoReq) XXX_Size() int {
	return xxx_messageInfo_AddDisplaySceneInfoReq.Size(m)
}
func (m *AddDisplaySceneInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddDisplaySceneInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddDisplaySceneInfoReq proto.InternalMessageInfo

func (m *AddDisplaySceneInfoReq) GetInfo() *DisplaySceneInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type AddDisplaySceneInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddDisplaySceneInfoResp) Reset()         { *m = AddDisplaySceneInfoResp{} }
func (m *AddDisplaySceneInfoResp) String() string { return proto.CompactTextString(m) }
func (*AddDisplaySceneInfoResp) ProtoMessage()    {}
func (*AddDisplaySceneInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{45}
}
func (m *AddDisplaySceneInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddDisplaySceneInfoResp.Unmarshal(m, b)
}
func (m *AddDisplaySceneInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddDisplaySceneInfoResp.Marshal(b, m, deterministic)
}
func (dst *AddDisplaySceneInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddDisplaySceneInfoResp.Merge(dst, src)
}
func (m *AddDisplaySceneInfoResp) XXX_Size() int {
	return xxx_messageInfo_AddDisplaySceneInfoResp.Size(m)
}
func (m *AddDisplaySceneInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddDisplaySceneInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddDisplaySceneInfoResp proto.InternalMessageInfo

// 删除配置
type DelDisplaySceneInfoReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelDisplaySceneInfoReq) Reset()         { *m = DelDisplaySceneInfoReq{} }
func (m *DelDisplaySceneInfoReq) String() string { return proto.CompactTextString(m) }
func (*DelDisplaySceneInfoReq) ProtoMessage()    {}
func (*DelDisplaySceneInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{46}
}
func (m *DelDisplaySceneInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelDisplaySceneInfoReq.Unmarshal(m, b)
}
func (m *DelDisplaySceneInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelDisplaySceneInfoReq.Marshal(b, m, deterministic)
}
func (dst *DelDisplaySceneInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelDisplaySceneInfoReq.Merge(dst, src)
}
func (m *DelDisplaySceneInfoReq) XXX_Size() int {
	return xxx_messageInfo_DelDisplaySceneInfoReq.Size(m)
}
func (m *DelDisplaySceneInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelDisplaySceneInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelDisplaySceneInfoReq proto.InternalMessageInfo

func (m *DelDisplaySceneInfoReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DelDisplaySceneInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelDisplaySceneInfoResp) Reset()         { *m = DelDisplaySceneInfoResp{} }
func (m *DelDisplaySceneInfoResp) String() string { return proto.CompactTextString(m) }
func (*DelDisplaySceneInfoResp) ProtoMessage()    {}
func (*DelDisplaySceneInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_api_8d0f7737a09438b0, []int{47}
}
func (m *DelDisplaySceneInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelDisplaySceneInfoResp.Unmarshal(m, b)
}
func (m *DelDisplaySceneInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelDisplaySceneInfoResp.Marshal(b, m, deterministic)
}
func (dst *DelDisplaySceneInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelDisplaySceneInfoResp.Merge(dst, src)
}
func (m *DelDisplaySceneInfoResp) XXX_Size() int {
	return xxx_messageInfo_DelDisplaySceneInfoResp.Size(m)
}
func (m *DelDisplaySceneInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelDisplaySceneInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelDisplaySceneInfoResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*LevelConf)(nil), "apicentergo.LevelConf")
	proto.RegisterType((*LimitConf)(nil), "apicentergo.LimitConf")
	proto.RegisterType((*FlowCardLimitConf)(nil), "apicentergo.FlowCardLimitConf")
	proto.RegisterType((*GetFlowCardLimitConfListReq)(nil), "apicentergo.GetFlowCardLimitConfListReq")
	proto.RegisterType((*GetFlowCardLimitConfListResp)(nil), "apicentergo.GetFlowCardLimitConfListResp")
	proto.RegisterType((*AddFlowCardLimitConfReq)(nil), "apicentergo.AddFlowCardLimitConfReq")
	proto.RegisterType((*AddFlowCardLimitConfResp)(nil), "apicentergo.AddFlowCardLimitConfResp")
	proto.RegisterType((*UpdateFlowCardLimitConfReq)(nil), "apicentergo.UpdateFlowCardLimitConfReq")
	proto.RegisterType((*UpdateFlowCardLimitConfResp)(nil), "apicentergo.UpdateFlowCardLimitConfResp")
	proto.RegisterType((*DelFlowCardLimitConfReq)(nil), "apicentergo.DelFlowCardLimitConfReq")
	proto.RegisterType((*DelFlowCardLimitConfResp)(nil), "apicentergo.DelFlowCardLimitConfResp")
	proto.RegisterType((*FlowCardGrantInfo)(nil), "apicentergo.FlowCardGrantInfo")
	proto.RegisterType((*GetGrantFlowCardListReq)(nil), "apicentergo.GetGrantFlowCardListReq")
	proto.RegisterType((*GetGrantFlowCardListResp)(nil), "apicentergo.GetGrantFlowCardListResp")
	proto.RegisterType((*GrantFlowCardReq)(nil), "apicentergo.GrantFlowCardReq")
	proto.RegisterType((*GrantFlowCardResp)(nil), "apicentergo.GrantFlowCardResp")
	proto.RegisterType((*ReclaimGrantedFlowCardReq)(nil), "apicentergo.ReclaimGrantedFlowCardReq")
	proto.RegisterType((*ReclaimGrantedFlowCardResp)(nil), "apicentergo.ReclaimGrantedFlowCardResp")
	proto.RegisterType((*BanGrantedFlowCardReq)(nil), "apicentergo.BanGrantedFlowCardReq")
	proto.RegisterType((*BanGrantedFlowCardResp)(nil), "apicentergo.BanGrantedFlowCardResp")
	proto.RegisterType((*TimeSection)(nil), "apicentergo.TimeSection")
	proto.RegisterType((*PrepareBaseInfo)(nil), "apicentergo.PrepareBaseInfo")
	proto.RegisterType((*PgcBaseInfo)(nil), "apicentergo.PgcBaseInfo")
	proto.RegisterType((*GuildBaseInfo)(nil), "apicentergo.GuildBaseInfo")
	proto.RegisterType((*UserBaseInfo)(nil), "apicentergo.UserBaseInfo")
	proto.RegisterType((*PrepareChannelInfo)(nil), "apicentergo.PrepareChannelInfo")
	proto.RegisterType((*GetPrepareChannelListReq)(nil), "apicentergo.GetPrepareChannelListReq")
	proto.RegisterType((*GetPrepareChannelListResp)(nil), "apicentergo.GetPrepareChannelListResp")
	proto.RegisterType((*SetPrepareChannelReq)(nil), "apicentergo.SetPrepareChannelReq")
	proto.RegisterType((*SetPrepareChannelResp)(nil), "apicentergo.SetPrepareChannelResp")
	proto.RegisterType((*DelPrepareChannelReq)(nil), "apicentergo.DelPrepareChannelReq")
	proto.RegisterType((*DelPrepareChannelResp)(nil), "apicentergo.DelPrepareChannelResp")
	proto.RegisterType((*GetGuildChannelListReq)(nil), "apicentergo.GetGuildChannelListReq")
	proto.RegisterType((*GetGuildChannelListResp)(nil), "apicentergo.GetGuildChannelListResp")
	proto.RegisterType((*GetChannelPrepareListReq)(nil), "apicentergo.GetChannelPrepareListReq")
	proto.RegisterType((*GetChannelPrepareListResp)(nil), "apicentergo.GetChannelPrepareListResp")
	proto.RegisterType((*GetPrepareBackupListReq)(nil), "apicentergo.GetPrepareBackupListReq")
	proto.RegisterType((*GetPrepareBackupListResp)(nil), "apicentergo.GetPrepareBackupListResp")
	proto.RegisterType((*PrepareOperRecord)(nil), "apicentergo.PrepareOperRecord")
	proto.RegisterType((*GetPrepareOperRecordListReq)(nil), "apicentergo.GetPrepareOperRecordListReq")
	proto.RegisterType((*GetPrepareOperRecordListResp)(nil), "apicentergo.GetPrepareOperRecordListResp")
	proto.RegisterType((*DisplaySceneInfo)(nil), "apicentergo.DisplaySceneInfo")
	proto.RegisterType((*GetDisplaySceneInfoListReq)(nil), "apicentergo.GetDisplaySceneInfoListReq")
	proto.RegisterType((*GetDisplaySceneInfoListResp)(nil), "apicentergo.GetDisplaySceneInfoListResp")
	proto.RegisterType((*AddDisplaySceneInfoReq)(nil), "apicentergo.AddDisplaySceneInfoReq")
	proto.RegisterType((*AddDisplaySceneInfoResp)(nil), "apicentergo.AddDisplaySceneInfoResp")
	proto.RegisterType((*DelDisplaySceneInfoReq)(nil), "apicentergo.DelDisplaySceneInfoReq")
	proto.RegisterType((*DelDisplaySceneInfoResp)(nil), "apicentergo.DelDisplaySceneInfoResp")
	proto.RegisterEnum("apicentergo.RecommendLevel", RecommendLevel_name, RecommendLevel_value)
	proto.RegisterEnum("apicentergo.FlowCardGrantType", FlowCardGrantType_name, FlowCardGrantType_value)
	proto.RegisterEnum("apicentergo.ChannelLevel", ChannelLevel_name, ChannelLevel_value)
	proto.RegisterEnum("apicentergo.SanctionStatus", SanctionStatus_name, SanctionStatus_value)
	proto.RegisterEnum("apicentergo.PrepareType", PrepareType_name, PrepareType_value)
	proto.RegisterEnum("apicentergo.PrepareOperType", PrepareOperType_name, PrepareOperType_value)
	proto.RegisterEnum("apicentergo.DisplaySceneStatusType", DisplaySceneStatusType_name, DisplaySceneStatusType_value)
	proto.RegisterEnum("apicentergo.DisplaySceneTimeType", DisplaySceneTimeType_name, DisplaySceneTimeType_value)
}

func init() {
	proto.RegisterFile("apicenter-go/channel-recommend-api.proto", fileDescriptor_channel_recommend_api_8d0f7737a09438b0)
}

var fileDescriptor_channel_recommend_api_8d0f7737a09438b0 = []byte{
	// 2109 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x59, 0x4b, 0x73, 0xdb, 0xc8,
	0xf1, 0x37, 0x48, 0x4a, 0x24, 0x9b, 0x7a, 0xc0, 0xb0, 0x44, 0x51, 0x2f, 0x5b, 0x7f, 0xd4, 0xbf,
	0x12, 0x45, 0x55, 0x96, 0x2a, 0x56, 0x5c, 0x15, 0x67, 0xf3, 0x28, 0x3d, 0x2c, 0x15, 0xb3, 0xb6,
	0x56, 0x4b, 0xc9, 0xde, 0xd4, 0x5e, 0x50, 0x10, 0x31, 0xa2, 0x67, 0x05, 0x0c, 0x60, 0x60, 0x28,
	0x59, 0x5b, 0x39, 0xa4, 0x2a, 0x39, 0xe5, 0x96, 0x54, 0xe5, 0x96, 0x2f, 0x91, 0x73, 0x8e, 0x39,
	0xe6, 0x63, 0xe4, 0x0b, 0xe4, 0x23, 0xa4, 0xa6, 0x67, 0x00, 0x0e, 0x08, 0x70, 0xb9, 0xce, 0x6e,
	0x4e, 0x62, 0x77, 0xcf, 0xf4, 0xfc, 0xba, 0xa7, 0x5f, 0x03, 0xc1, 0xb6, 0x1b, 0xd1, 0x3e, 0x61,
	0x9c, 0xc4, 0x4f, 0x07, 0xe1, 0x5e, 0xff, 0x9d, 0xcb, 0x18, 0xf1, 0x9f, 0xc6, 0xa4, 0x1f, 0x06,
	0x01, 0x61, 0xde, 0x53, 0x37, 0xa2, 0xbb, 0x51, 0x1c, 0xf2, 0xd0, 0x6a, 0x65, 0x2b, 0x07, 0xa1,
	0xbd, 0x0f, 0xcd, 0x57, 0xe4, 0x96, 0xf8, 0x47, 0x21, 0xbb, 0xb6, 0x96, 0x60, 0xc6, 0x17, 0x44,
	0xc7, 0xd8, 0x32, 0xb6, 0xe7, 0x7b, 0x92, 0xb0, 0x4c, 0xa8, 0xf6, 0x19, 0xef, 0x54, 0x90, 0x27,
	0x7e, 0xda, 0x5f, 0x42, 0xf3, 0x15, 0x0d, 0x28, 0xc7, 0x4d, 0x1d, 0xa8, 0xbf, 0x0b, 0x87, 0xf1,
	0x11, 0xe3, 0x6a, 0x5b, 0x4a, 0x5a, 0xfb, 0xd0, 0xec, 0x87, 0xec, 0xda, 0xf1, 0x69, 0x22, 0xb6,
	0x57, 0xb7, 0x5b, 0xcf, 0xda, 0xbb, 0xda, 0xe1, 0xbb, 0xd9, 0xc9, 0xbd, 0x86, 0x58, 0xf8, 0x8a,
	0x26, 0xdc, 0xfe, 0xa3, 0x01, 0x0f, 0x4f, 0xfc, 0xf0, 0xee, 0xc8, 0x8d, 0xbd, 0xd1, 0x21, 0x0b,
	0x50, 0xa1, 0x9e, 0xd2, 0x5f, 0xa1, 0x9e, 0xb5, 0x0a, 0x8d, 0x2b, 0x32, 0xa0, 0xcc, 0xe1, 0x89,
	0x02, 0x56, 0x47, 0xfa, 0x32, 0xb1, 0x96, 0x61, 0x96, 0x30, 0x4f, 0x08, 0xaa, 0xd2, 0x0a, 0xc2,
	0xbc, 0xcb, 0x24, 0x0f, 0xa6, 0x56, 0x06, 0x26, 0x3d, 0x4c, 0x03, 0xf3, 0x3b, 0x03, 0xd6, 0x4f,
	0x09, 0x2f, 0xe0, 0x11, 0xc2, 0x1e, 0x79, 0x6f, 0x59, 0x50, 0x8b, 0xdc, 0x01, 0x51, 0xc0, 0xf0,
	0xb7, 0xb5, 0x0e, 0x4d, 0xf1, 0xd7, 0x49, 0xe8, 0xd7, 0x44, 0x61, 0x6b, 0x08, 0xc6, 0x05, 0xfd,
	0x9a, 0xe4, 0x70, 0x57, 0x27, 0xe1, 0xae, 0x69, 0xb8, 0xed, 0xbf, 0x18, 0xb0, 0x31, 0x19, 0x42,
	0x12, 0x59, 0x9f, 0xe8, 0x86, 0x19, 0x68, 0xd8, 0xe3, 0x9c, 0x61, 0x85, 0xad, 0x23, 0x03, 0x05,
	0x58, 0x46, 0x3e, 0x70, 0x07, 0xad, 0x50, 0x60, 0x05, 0xe3, 0x5c, 0x59, 0xc2, 0x43, 0xee, 0xfa,
	0x8e, 0xb8, 0x7e, 0x89, 0xb6, 0x81, 0x8c, 0x23, 0xc6, 0xed, 0xd7, 0xb0, 0x72, 0xe0, 0x79, 0x45,
	0xdd, 0xe4, 0xbd, 0xf5, 0x0c, 0x6a, 0xe2, 0x00, 0xf4, 0xca, 0x74, 0x30, 0xb8, 0xd6, 0x5e, 0x83,
	0x4e, 0xb9, 0xba, 0x24, 0xb2, 0xcf, 0x61, 0xed, 0x4d, 0xe4, 0xb9, 0x9c, 0x7c, 0x6f, 0xa7, 0x6d,
	0xc2, 0xfa, 0x44, 0x8d, 0x49, 0x64, 0xff, 0x08, 0x56, 0x8e, 0x89, 0x5f, 0x7a, 0xda, 0x58, 0x20,
	0x0a, 0xdc, 0xe5, 0x4b, 0x93, 0xc8, 0xfe, 0x7b, 0x65, 0x14, 0xca, 0xa7, 0xb1, 0xcb, 0x78, 0x97,
	0x5d, 0x87, 0x22, 0x04, 0x06, 0x82, 0x70, 0x32, 0x3d, 0x75, 0xa4, 0xbb, 0x9e, 0x52, 0x5e, 0xc9,
	0xa2, 0xdc, 0x82, 0x1a, 0x73, 0x03, 0x82, 0xbe, 0x6f, 0xf6, 0xf0, 0xf7, 0x28, 0x47, 0x6b, 0x7a,
	0x8e, 0xae, 0x43, 0x93, 0x7c, 0x88, 0x68, 0xcc, 0x45, 0xfc, 0xcc, 0xc8, 0xab, 0x92, 0x8c, 0xcb,
	0x24, 0x4d, 0xe0, 0xd9, 0x2c, 0x81, 0xad, 0x36, 0xcc, 0xc6, 0x24, 0x70, 0xe3, 0x9b, 0x4e, 0x1d,
	0x55, 0x2b, 0x4a, 0xa8, 0xa1, 0x89, 0x73, 0x25, 0x6a, 0x87, 0xd7, 0x69, 0x6c, 0x19, 0xdb, 0x8d,
	0x5e, 0x83, 0x26, 0x87, 0x48, 0x5b, 0x5b, 0x30, 0x77, 0xe5, 0x32, 0x27, 0x8b, 0xdf, 0x26, 0xea,
	0x83, 0x2b, 0x97, 0x1d, 0xaa, 0x10, 0xde, 0x00, 0x41, 0x39, 0x2a, 0x8c, 0x41, 0xc2, 0xb8, 0x72,
	0xd9, 0x4b, 0xcc, 0x40, 0x0b, 0x6a, 0x9c, 0x53, 0xaf, 0xd3, 0x92, 0xd6, 0x88, 0xdf, 0xc2, 0x19,
	0x61, 0xec, 0x91, 0x58, 0x38, 0x63, 0x0e, 0xf9, 0x75, 0xa4, 0xbb, 0x9e, 0x7d, 0x0f, 0x2b, 0xa7,
	0x84, 0xa3, 0xdf, 0x46, 0xee, 0x95, 0x69, 0xb7, 0x09, 0x20, 0x5d, 0xc8, 0xef, 0xa3, 0x34, 0xf9,
	0x9a, 0xc8, 0xb9, 0xbc, 0x8f, 0x88, 0xe6, 0xc6, 0x66, 0xea, 0x46, 0x8c, 0xef, 0xea, 0xa4, 0x2c,
	0xad, 0xe5, 0xb3, 0xd4, 0xfe, 0x93, 0x01, 0x9d, 0xf2, 0xb3, 0x65, 0xbe, 0x51, 0x76, 0x1d, 0x4e,
	0xcf, 0xb7, 0xec, 0xca, 0x7b, 0x0d, 0xb1, 0xe1, 0x3b, 0xe6, 0x1b, 0x03, 0x33, 0x87, 0xe7, 0x5b,
	0xf8, 0x21, 0x87, 0xb4, 0xf2, 0x71, 0x48, 0xed, 0x7d, 0x78, 0x38, 0x76, 0x5e, 0x12, 0x59, 0x8f,
	0xa1, 0x45, 0x62, 0x71, 0x59, 0x23, 0xeb, 0x9b, 0xbd, 0x26, 0x89, 0xe3, 0x2e, 0xfa, 0xc7, 0xbe,
	0x85, 0xd5, 0x1e, 0xe9, 0xfb, 0x2e, 0x0d, 0x70, 0x2f, 0xf1, 0x3e, 0x02, 0xad, 0x9e, 0x17, 0x95,
	0x7c, 0x5e, 0x3c, 0x81, 0x56, 0x2c, 0xd5, 0x6a, 0xae, 0x01, 0xc5, 0x12, 0xce, 0xd9, 0x80, 0xb5,
	0x49, 0xe7, 0x26, 0x91, 0xfd, 0x7b, 0x03, 0x96, 0x0f, 0x5d, 0xf6, 0xbd, 0x42, 0xfa, 0xf8, 0x42,
	0xde, 0x81, 0x76, 0x19, 0x88, 0x24, 0xb2, 0x4f, 0xa1, 0x75, 0x49, 0x03, 0x72, 0x41, 0xfa, 0x9c,
	0x86, 0x4c, 0x80, 0x52, 0xaa, 0x69, 0x90, 0x81, 0x92, 0xca, 0x69, 0x80, 0xa0, 0x50, 0xbd, 0x10,
	0x2a, 0x50, 0xe2, 0x00, 0x1a, 0x10, 0xfb, 0x1f, 0x55, 0x58, 0x3c, 0x8f, 0x49, 0xe4, 0xc6, 0xe4,
	0xd0, 0x4d, 0x08, 0x96, 0x9b, 0x4d, 0x00, 0x35, 0x0c, 0x8c, 0x0a, 0x4e, 0x53, 0x71, 0xba, 0x9e,
	0x00, 0xcb, 0xdd, 0xc1, 0xc8, 0xc0, 0x19, 0xee, 0x0e, 0xba, 0x9e, 0x8c, 0xd3, 0x3b, 0x47, 0x56,
	0x9a, 0x6a, 0x1a, 0xa7, 0x77, 0xd8, 0xb0, 0xad, 0x17, 0xd0, 0x12, 0xc2, 0x44, 0xe2, 0x45, 0x2b,
	0x5b, 0xcf, 0x3a, 0xb9, 0xc8, 0xd2, 0xec, 0xe9, 0x01, 0x23, 0x77, 0xa9, 0x6d, 0xeb, 0xd0, 0x0c,
	0x7d, 0x4f, 0xe9, 0x55, 0x75, 0x2a, 0xf4, 0xbd, 0x4c, 0xaf, 0x10, 0xa6, 0x7a, 0x67, 0xa7, 0xe9,
	0x0d, 0x7d, 0x2f, 0xd5, 0xfb, 0x04, 0x5a, 0xef, 0x87, 0xb4, 0x7f, 0xa3, 0x34, 0xd7, 0x65, 0x84,
	0x20, 0x4b, 0xea, 0xfe, 0x05, 0xcc, 0xcb, 0x05, 0xa9, 0xf6, 0xc6, 0x14, 0xed, 0x73, 0xb8, 0x5c,
	0xc3, 0x3d, 0xc4, 0x86, 0x31, 0x2a, 0x7c, 0x0d, 0xc9, 0xb8, 0x4c, 0xac, 0xff, 0x83, 0xb9, 0x48,
	0x7a, 0x5d, 0xc6, 0x91, 0x2c, 0x7c, 0x2d, 0xc5, 0xd3, 0x4a, 0x52, 0x2b, 0xab, 0xec, 0x6b, 0xd0,
	0x08, 0x23, 0x12, 0xbb, 0x3c, 0x8c, 0x55, 0xdd, 0xcb, 0x68, 0xfb, 0x6f, 0x06, 0xb4, 0xce, 0x07,
	0xfd, 0x6f, 0x7b, 0x83, 0x3f, 0x80, 0xc5, 0x54, 0x7c, 0x4b, 0xc9, 0x9d, 0x93, 0x95, 0xbe, 0x79,
	0xc5, 0x7e, 0x4b, 0xc9, 0x5d, 0xd7, 0x13, 0x28, 0xd3, 0x75, 0x5a, 0x53, 0x69, 0x29, 0xde, 0x99,
	0xe8, 0x2d, 0x3f, 0x05, 0x08, 0xef, 0x98, 0xa8, 0xc6, 0xec, 0x3a, 0x54, 0xf7, 0xba, 0x9a, 0xf3,
	0xd0, 0x9b, 0x84, 0xc4, 0x29, 0xb0, 0x5e, 0x13, 0x17, 0x8b, 0x9f, 0x76, 0x17, 0xe6, 0x4f, 0x87,
	0xd4, 0xf7, 0x32, 0xd0, 0x22, 0x75, 0x04, 0x43, 0xef, 0x72, 0x82, 0xee, 0x7a, 0x98, 0x74, 0x28,
	0x42, 0x18, 0x12, 0x6b, 0x13, 0x39, 0x02, 0x84, 0xfd, 0x4b, 0x98, 0xd3, 0x4f, 0xc9, 0xda, 0x86,
	0xa1, 0xb5, 0x8d, 0x35, 0x68, 0x30, 0xda, 0xbf, 0xd1, 0x14, 0x64, 0xb4, 0xfd, 0x6f, 0x03, 0x2c,
	0x95, 0x04, 0x47, 0xca, 0x49, 0x42, 0xcd, 0x0b, 0x68, 0x5e, 0xb9, 0x09, 0x91, 0xa6, 0xc9, 0x59,
	0x61, 0x23, 0x67, 0xda, 0x58, 0xe2, 0x88, 0xc6, 0xa5, 0x10, 0x7c, 0x32, 0xf2, 0x1c, 0xee, 0xae,
	0x94, 0x84, 0x8e, 0x76, 0x61, 0x99, 0x4f, 0xd5, 0xb9, 0xca, 0x5a, 0xdc, 0x5a, 0xc5, 0xad, 0x6b,
	0xb9, 0xad, 0x39, 0xc7, 0x29, 0x4f, 0xe0, 0xd6, 0x1f, 0xc2, 0x62, 0xe2, 0x32, 0x0c, 0x40, 0x27,
	0xe1, 0x2e, 0x1f, 0xa6, 0x15, 0x65, 0x21, 0x65, 0x5f, 0x20, 0x57, 0x0c, 0x21, 0xa7, 0x84, 0xe7,
	0x8d, 0x56, 0xbd, 0xd2, 0x66, 0xb0, 0x3a, 0x41, 0x96, 0x44, 0xd6, 0xe7, 0xb0, 0x94, 0x46, 0x6e,
	0x6a, 0xa1, 0xd6, 0xd6, 0x9e, 0x94, 0xf9, 0x47, 0xf3, 0x69, 0xcf, 0x8a, 0x0a, 0x6a, 0xed, 0x00,
	0x96, 0x2e, 0xc6, 0xcf, 0x13, 0xa5, 0xf6, 0x45, 0xb1, 0x6d, 0x4e, 0xf1, 0x7f, 0xd6, 0x34, 0xf5,
	0x64, 0xa9, 0x8c, 0x25, 0xcb, 0x0a, 0x2c, 0x97, 0x1c, 0x97, 0x44, 0xf6, 0xe7, 0xb0, 0x74, 0x4c,
	0xfc, 0x22, 0x8e, 0x29, 0xd9, 0x34, 0xe5, 0xac, 0x12, 0x95, 0x49, 0x64, 0xef, 0x43, 0x5b, 0x8c,
	0x0b, 0xe2, 0xe2, 0xf2, 0xde, 0xff, 0x86, 0x34, 0xb0, 0xbf, 0x90, 0xf3, 0x4d, 0x61, 0x53, 0x12,
	0x59, 0x3f, 0x2f, 0xfa, 0x6a, 0xea, 0x5d, 0x8c, 0x3a, 0xf7, 0x0b, 0x8c, 0x06, 0x25, 0x53, 0x2b,
	0xb5, 0xc9, 0xe9, 0x1b, 0xac, 0x57, 0xc1, 0x52, 0xb6, 0xf5, 0x7f, 0x13, 0x2c, 0xfb, 0xe8, 0x83,
	0xec, 0xe6, 0xfb, 0x37, 0xc3, 0x28, 0x45, 0xda, 0x81, 0xfa, 0x2d, 0x89, 0x13, 0x51, 0xaa, 0x05,
	0xcc, 0x6a, 0x2f, 0x25, 0xed, 0xdf, 0xe8, 0xd1, 0xae, 0x6f, 0xfa, 0xce, 0x9e, 0x23, 0xf0, 0x50,
	0xc9, 0x3f, 0x8b, 0x48, 0xdc, 0x23, 0xfd, 0x30, 0xc6, 0x56, 0x28, 0x22, 0x40, 0x1f, 0x11, 0x30,
	0x24, 0xb0, 0xae, 0xef, 0x43, 0x4d, 0x2b, 0x09, 0x53, 0x8f, 0xc2, 0xc5, 0xf6, 0x19, 0x3e, 0x2a,
	0x0b, 0x27, 0xfd, 0xb7, 0x8f, 0x4a, 0xfb, 0xb7, 0xf8, 0x42, 0x9c, 0xa0, 0x2f, 0x89, 0xac, 0x5f,
	0xe1, 0xf8, 0x14, 0xc6, 0xde, 0xe4, 0x99, 0xb5, 0xb0, 0x19, 0xc7, 0x2b, 0xa5, 0x24, 0x3f, 0x98,
	0x56, 0xc6, 0x06, 0xd3, 0x3f, 0xcf, 0x80, 0x79, 0x4c, 0x93, 0xc8, 0x77, 0xef, 0x2f, 0xfa, 0x84,
	0xc9, 0x92, 0x39, 0xfe, 0x5e, 0x17, 0x45, 0x5c, 0xf8, 0x4f, 0x6e, 0xc6, 0xdf, 0xe2, 0x25, 0xc3,
	0x29, 0xf7, 0xd3, 0x4e, 0x24, 0x09, 0x91, 0x31, 0x89, 0x50, 0x23, 0xe2, 0x53, 0x56, 0xbb, 0x3a,
	0xd2, 0x5d, 0xcf, 0xfa, 0x7f, 0x58, 0x10, 0xaf, 0x14, 0xc2, 0xb3, 0x01, 0x74, 0x66, 0xab, 0xba,
	0x3d, 0xdf, 0x9b, 0x93, 0x5c, 0x39, 0x83, 0x8a, 0x0c, 0x8e, 0x7c, 0x97, 0x5f, 0x87, 0x71, 0xa0,
	0x9e, 0x3c, 0x19, 0x2d, 0xc6, 0x84, 0x80, 0x32, 0x27, 0x0d, 0x2c, 0xf9, 0xf8, 0x81, 0x80, 0xb2,
	0xb7, 0x92, 0x93, 0x1b, 0xeb, 0x1a, 0x93, 0xc6, 0xba, 0xa6, 0xfe, 0x5d, 0x61, 0x13, 0x40, 0xe2,
	0xd5, 0x5a, 0x7f, 0x13, 0x39, 0x18, 0x20, 0xa3, 0xf9, 0xaa, 0xa5, 0xcf, 0x57, 0xab, 0xd0, 0xf8,
	0x6a, 0x18, 0x44, 0xce, 0x30, 0xf6, 0xd3, 0x77, 0x8f, 0xa0, 0xdf, 0xc4, 0xbe, 0x78, 0x9b, 0xdd,
	0x11, 0x3a, 0x78, 0xc7, 0x3b, 0xf3, 0xb8, 0x43, 0x51, 0x42, 0xd3, 0xd5, 0xc0, 0xa1, 0xc1, 0xa0,
	0xb3, 0x20, 0xfd, 0x75, 0x35, 0xe8, 0x06, 0x03, 0x71, 0x37, 0x49, 0xe0, 0xfa, 0x3e, 0x4a, 0x16,
	0x65, 0xc5, 0x42, 0x86, 0x10, 0x0a, 0x70, 0xdc, 0xe5, 0xb4, 0x8f, 0x52, 0x53, 0xb6, 0x5a, 0xc9,
	0x11, 0xe2, 0x27, 0xd0, 0xf2, 0xee, 0x99, 0x1b, 0xd0, 0x3e, 0x02, 0x79, 0x28, 0xdd, 0xa1, 0x58,
	0x02, 0x8b, 0xb6, 0x20, 0xf0, 0x9e, 0x77, 0xac, 0xdc, 0x82, 0xd7, 0xde, 0x73, 0x6b, 0x1b, 0xcc,
	0x74, 0xc1, 0x57, 0x49, 0xc8, 0x9c, 0x1b, 0x72, 0xdf, 0x79, 0x84, 0xab, 0x16, 0x14, 0xff, 0xd7,
	0x49, 0xc8, 0x3e, 0x25, 0xf7, 0xb9, 0xc2, 0xba, 0x94, 0x2f, 0xac, 0xf9, 0xe9, 0x6a, 0x79, 0x6c,
	0xba, 0x6a, 0xc3, 0xac, 0x6a, 0x7e, 0x6d, 0xe9, 0x0f, 0x49, 0x61, 0x50, 0xd2, 0x40, 0xf9, 0x7d,
	0x45, 0x05, 0x25, 0x0d, 0xd0, 0xed, 0xf6, 0xbf, 0x0c, 0x58, 0x3b, 0x25, 0x7c, 0x3c, 0x2e, 0xb5,
	0x14, 0xd3, 0xd2, 0x59, 0x86, 0xa3, 0x1e, 0x78, 0x95, 0x7c, 0xe0, 0xad, 0x43, 0x33, 0x0b, 0xbc,
	0x74, 0x1a, 0x4e, 0x63, 0x4e, 0xc3, 0x57, 0xcb, 0xe1, 0xd3, 0x43, 0x69, 0x66, 0x52, 0x28, 0xcd,
	0xea, 0xa1, 0x94, 0x26, 0x7e, 0x7d, 0x52, 0xe2, 0x37, 0xc6, 0x12, 0xff, 0x16, 0x0b, 0x49, 0xb9,
	0x91, 0x49, 0x64, 0xfd, 0xac, 0x58, 0x0c, 0x37, 0x73, 0x59, 0x3f, 0xbe, 0x33, 0xff, 0x50, 0x9d,
	0x9c, 0xf2, 0x9f, 0x42, 0xfb, 0xc0, 0xf3, 0x0a, 0xbb, 0xc9, 0x7b, 0xeb, 0xc7, 0xaa, 0x1e, 0xca,
	0x01, 0x6b, 0xca, 0x69, 0xb2, 0x1a, 0xae, 0xe2, 0x87, 0xa4, 0xa2, 0xb2, 0x24, 0xb2, 0xb7, 0xa1,
	0x7d, 0x4c, 0xfc, 0xb2, 0x73, 0xc6, 0x3f, 0xc3, 0xac, 0xe2, 0x17, 0x9b, 0x32, 0x25, 0x3b, 0x3e,
	0x2c, 0xf4, 0xd2, 0xaf, 0xa0, 0xf2, 0x2d, 0xb0, 0x0c, 0x0f, 0x33, 0x8e, 0xd3, 0x65, 0xb7, 0xae,
	0x4f, 0x3d, 0xf3, 0x41, 0x9e, 0x8d, 0x2b, 0x9d, 0x0b, 0xd3, 0x28, 0x63, 0x1f, 0x98, 0x95, 0x32,
	0xf6, 0xa1, 0x59, 0xdd, 0xf9, 0xc9, 0xd8, 0x27, 0x1f, 0x35, 0xfd, 0x03, 0x12, 0xd8, 0xec, 0xcd,
	0x07, 0xd6, 0x22, 0xb4, 0x90, 0x3e, 0x60, 0xfd, 0x77, 0x61, 0x6c, 0x1a, 0x3b, 0xff, 0x34, 0x60,
	0x2e, 0xed, 0x8b, 0x08, 0xf1, 0x11, 0x2c, 0x2a, 0x5a, 0x03, 0xa8, 0x31, 0x47, 0xf0, 0x0a, 0x4c,
	0x01, 0xae, 0xc0, 0x3c, 0x34, 0xab, 0x45, 0xe6, 0x91, 0x59, 0x2b, 0x32, 0x8f, 0xcd, 0x99, 0x22,
	0xf3, 0xa5, 0x39, 0x5b, 0x64, 0x9e, 0x98, 0xf5, 0x22, 0xf3, 0xd4, 0x6c, 0xec, 0x9c, 0xc0, 0xc2,
	0x45, 0x6e, 0x40, 0xb5, 0x3a, 0xb0, 0x94, 0x72, 0x1c, 0xc9, 0x72, 0xce, 0x42, 0x46, 0xcc, 0x07,
	0xd6, 0x1a, 0xb4, 0xc7, 0x25, 0x87, 0x07, 0x67, 0x67, 0x2f, 0x8f, 0x4d, 0x63, 0xe7, 0x0b, 0x68,
	0x9d, 0x6b, 0x6f, 0xa8, 0x0e, 0x2c, 0x29, 0xd2, 0x11, 0xb4, 0xe6, 0x98, 0x15, 0x78, 0x94, 0x93,
	0xbc, 0x76, 0xd9, 0xd0, 0xf5, 0xe5, 0xdd, 0xe5, 0x04, 0x07, 0x43, 0x1e, 0x9a, 0x95, 0x9d, 0x3f,
	0x18, 0xd9, 0x3b, 0xf9, 0xb3, 0xb4, 0x93, 0x6f, 0xc2, 0x6a, 0xba, 0x54, 0xf0, 0xc6, 0x8f, 0x58,
	0x85, 0xe5, 0xa2, 0xf8, 0xc0, 0xf3, 0x4c, 0xc3, 0xda, 0x80, 0x4e, 0x51, 0x24, 0x3f, 0x2f, 0x9a,
	0x95, 0xf2, 0x8d, 0xc7, 0xc4, 0x37, 0xab, 0x3b, 0x7f, 0x35, 0xa0, 0xad, 0x87, 0xac, 0xb4, 0x1f,
	0xd1, 0x6c, 0xc1, 0x86, 0x92, 0x38, 0x28, 0x4a, 0x7d, 0x33, 0x02, 0xf4, 0x04, 0xd6, 0x4b, 0x57,
	0x9c, 0x0c, 0xf9, 0x30, 0x26, 0xa6, 0x61, 0x3d, 0x86, 0xb5, 0xd2, 0x05, 0x6f, 0x51, 0x41, 0x65,
	0xa2, 0x82, 0x97, 0x1f, 0x22, 0x1a, 0x13, 0xb3, 0xba, 0x73, 0x0b, 0x4b, 0x3a, 0xba, 0x4b, 0x55,
	0x5b, 0x8b, 0x8a, 0x85, 0x44, 0x43, 0x56, 0x2e, 0xef, 0x91, 0xc1, 0xd0, 0x77, 0xe3, 0x32, 0x60,
	0x28, 0x3f, 0x09, 0x63, 0x72, 0x4b, 0x62, 0xb3, 0x72, 0xb8, 0xf7, 0xe5, 0xd3, 0x41, 0xe8, 0xbb,
	0x6c, 0xb0, 0xfb, 0xfc, 0x19, 0xe7, 0xbb, 0xfd, 0x30, 0xd8, 0xc3, 0x7f, 0x5c, 0xf4, 0x43, 0x7f,
	0x2f, 0x21, 0xf1, 0x2d, 0xed, 0x93, 0x64, 0x4f, 0x2b, 0x2c, 0x57, 0xb3, 0x28, 0xde, 0xff, 0x4f,
	0x00, 0x00, 0x00, 0xff, 0xff, 0x63, 0x74, 0xc7, 0xfe, 0x03, 0x19, 0x00, 0x00,
}
