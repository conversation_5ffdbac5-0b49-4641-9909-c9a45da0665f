// Code generated by protoc-gen-go. DO NOT EDIT.
// source: apicenter-go/userservice-api.proto

package apicentergo // import "golang.52tt.com/protocol/services/apicentergo"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 按移位定义
type BanOpType int32

const (
	BanOpType_BAN_OP_BAN_NIL BanOpType = 0
	// 封 操作
	BanOpType_BAN_OP_BAN_USER      BanOpType = 1
	BanOpType_BAN_OP_BAN_DEVICE    BanOpType = 2
	BanOpType_BAN_OP_BAN_CLIENT_IP BanOpType = 4
	BanOpType_BAN_OP_BAN_PHONE     BanOpType = 8
	// 解封 操作
	BanOpType_BAN_OP_UNBAN_USER      BanOpType = 256
	BanOpType_BAN_OP_UNBAN_DEVICE    BanOpType = 512
	BanOpType_BAN_OP_UNBAN_CLIENT_IP BanOpType = 1024
	BanOpType_BAN_OP_UNBAN_PHONE     BanOpType = 2048
)

var BanOpType_name = map[int32]string{
	0:    "BAN_OP_BAN_NIL",
	1:    "BAN_OP_BAN_USER",
	2:    "BAN_OP_BAN_DEVICE",
	4:    "BAN_OP_BAN_CLIENT_IP",
	8:    "BAN_OP_BAN_PHONE",
	256:  "BAN_OP_UNBAN_USER",
	512:  "BAN_OP_UNBAN_DEVICE",
	1024: "BAN_OP_UNBAN_CLIENT_IP",
	2048: "BAN_OP_UNBAN_PHONE",
}
var BanOpType_value = map[string]int32{
	"BAN_OP_BAN_NIL":         0,
	"BAN_OP_BAN_USER":        1,
	"BAN_OP_BAN_DEVICE":      2,
	"BAN_OP_BAN_CLIENT_IP":   4,
	"BAN_OP_BAN_PHONE":       8,
	"BAN_OP_UNBAN_USER":      256,
	"BAN_OP_UNBAN_DEVICE":    512,
	"BAN_OP_UNBAN_CLIENT_IP": 1024,
	"BAN_OP_UNBAN_PHONE":     2048,
}

func (x BanOpType) String() string {
	return proto.EnumName(BanOpType_name, int32(x))
}
func (BanOpType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{0}
}

// 封禁申诉
type BannedAppealState int32

const (
	BannedAppealState_BANNED_APPEAL_STATE_NONE  BannedAppealState = 0
	BannedAppealState_BANNED_APPEAL_STATE_PEND  BannedAppealState = 1
	BannedAppealState_BANNED_APPEAL_STATE_PASS  BannedAppealState = 2
	BannedAppealState_BANNED_APPEAL_STATE_DENY  BannedAppealState = 4
	BannedAppealState_BANNED_APPEAL_STATE_BLACK BannedAppealState = 5
)

var BannedAppealState_name = map[int32]string{
	0: "BANNED_APPEAL_STATE_NONE",
	1: "BANNED_APPEAL_STATE_PEND",
	2: "BANNED_APPEAL_STATE_PASS",
	4: "BANNED_APPEAL_STATE_DENY",
	5: "BANNED_APPEAL_STATE_BLACK",
}
var BannedAppealState_value = map[string]int32{
	"BANNED_APPEAL_STATE_NONE":  0,
	"BANNED_APPEAL_STATE_PEND":  1,
	"BANNED_APPEAL_STATE_PASS":  2,
	"BANNED_APPEAL_STATE_DENY":  4,
	"BANNED_APPEAL_STATE_BLACK": 5,
}

func (x BannedAppealState) String() string {
	return proto.EnumName(BannedAppealState_name, int32(x))
}
func (BannedAppealState) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{1}
}

type BannedReasonCode int32

const (
	BannedReasonCode_BANNED_REASON_CODE_NORMAL          BannedReasonCode = 0
	BannedReasonCode_BANNED_REASON_CODE_BAN             BannedReasonCode = 1
	BannedReasonCode_BANNED_REASON_CODE_ACCOUNT_RECYCLE BannedReasonCode = 2
	BannedReasonCode_BANNED_REASON_CODE_ACCOUNT_BLACK   BannedReasonCode = 4
)

var BannedReasonCode_name = map[int32]string{
	0: "BANNED_REASON_CODE_NORMAL",
	1: "BANNED_REASON_CODE_BAN",
	2: "BANNED_REASON_CODE_ACCOUNT_RECYCLE",
	4: "BANNED_REASON_CODE_ACCOUNT_BLACK",
}
var BannedReasonCode_value = map[string]int32{
	"BANNED_REASON_CODE_NORMAL":          0,
	"BANNED_REASON_CODE_BAN":             1,
	"BANNED_REASON_CODE_ACCOUNT_RECYCLE": 2,
	"BANNED_REASON_CODE_ACCOUNT_BLACK":   4,
}

func (x BannedReasonCode) String() string {
	return proto.EnumName(BannedReasonCode_name, int32(x))
}
func (BannedReasonCode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{2}
}

// 黑名单复核用户标签
type BannedCheckUserTag int32

const (
	BannedCheckUserTag_BANNED_CHECK_USER_TAG_UNSPECIFIED BannedCheckUserTag = 0
	BannedCheckUserTag_BANNED_CHECK_USER_TAG_HIGH_PAY    BannedCheckUserTag = 1
	BannedCheckUserTag_BANNED_CHECK_USER_TAG_PROFESSION  BannedCheckUserTag = 2
)

var BannedCheckUserTag_name = map[int32]string{
	0: "BANNED_CHECK_USER_TAG_UNSPECIFIED",
	1: "BANNED_CHECK_USER_TAG_HIGH_PAY",
	2: "BANNED_CHECK_USER_TAG_PROFESSION",
}
var BannedCheckUserTag_value = map[string]int32{
	"BANNED_CHECK_USER_TAG_UNSPECIFIED": 0,
	"BANNED_CHECK_USER_TAG_HIGH_PAY":    1,
	"BANNED_CHECK_USER_TAG_PROFESSION":  2,
}

func (x BannedCheckUserTag) String() string {
	return proto.EnumName(BannedCheckUserTag_name, int32(x))
}
func (BannedCheckUserTag) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{3}
}

// 黑名单复核封禁来源
type BannedCheckSource int32

const (
	BannedCheckSource_BANNED_CHECK_SOURCE_UNSPECIFIED BannedCheckSource = 0
	BannedCheckSource_BANNED_CHECK_SOURCE_AUDIT       BannedCheckSource = 1
	BannedCheckSource_BANNED_CHECK_SOURCE_STRATEGY    BannedCheckSource = 2
)

var BannedCheckSource_name = map[int32]string{
	0: "BANNED_CHECK_SOURCE_UNSPECIFIED",
	1: "BANNED_CHECK_SOURCE_AUDIT",
	2: "BANNED_CHECK_SOURCE_STRATEGY",
}
var BannedCheckSource_value = map[string]int32{
	"BANNED_CHECK_SOURCE_UNSPECIFIED": 0,
	"BANNED_CHECK_SOURCE_AUDIT":       1,
	"BANNED_CHECK_SOURCE_STRATEGY":    2,
}

func (x BannedCheckSource) String() string {
	return proto.EnumName(BannedCheckSource_name, int32(x))
}
func (BannedCheckSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{4}
}

type BannedCheckRecordType int32

const (
	BannedCheckRecordType_BANNED_CHECK_RECORD_TYPE_UNSPECIFIED BannedCheckRecordType = 0
	BannedCheckRecordType_BANNED_CHECK_RECORD_TYPE_TO_CHECK    BannedCheckRecordType = 1
	BannedCheckRecordType_BANNED_CHECK_RECORD_TYPE_CHECKED     BannedCheckRecordType = 2
)

var BannedCheckRecordType_name = map[int32]string{
	0: "BANNED_CHECK_RECORD_TYPE_UNSPECIFIED",
	1: "BANNED_CHECK_RECORD_TYPE_TO_CHECK",
	2: "BANNED_CHECK_RECORD_TYPE_CHECKED",
}
var BannedCheckRecordType_value = map[string]int32{
	"BANNED_CHECK_RECORD_TYPE_UNSPECIFIED": 0,
	"BANNED_CHECK_RECORD_TYPE_TO_CHECK":    1,
	"BANNED_CHECK_RECORD_TYPE_CHECKED":     2,
}

func (x BannedCheckRecordType) String() string {
	return proto.EnumName(BannedCheckRecordType_name, int32(x))
}
func (BannedCheckRecordType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{5}
}

// 复核操作类型
type BannedCheckType int32

const (
	BannedCheckType_BANNED_CHECK_TYPE_UNSPECIFIED       BannedCheckType = 0
	BannedCheckType_BANNED_CHECK_TYPE_KEEP_BANNED       BannedCheckType = 1
	BannedCheckType_BANNED_CHECK_TYPE_UNBANNED_BY_CHECK BannedCheckType = 2
	BannedCheckType_BANNED_CHECK_TYPE_UNBANNED_BY_OTHER BannedCheckType = 3
	BannedCheckType_BANNED_CHECK_TYPE_REJECT_UNBAN      BannedCheckType = 4
)

var BannedCheckType_name = map[int32]string{
	0: "BANNED_CHECK_TYPE_UNSPECIFIED",
	1: "BANNED_CHECK_TYPE_KEEP_BANNED",
	2: "BANNED_CHECK_TYPE_UNBANNED_BY_CHECK",
	3: "BANNED_CHECK_TYPE_UNBANNED_BY_OTHER",
	4: "BANNED_CHECK_TYPE_REJECT_UNBAN",
}
var BannedCheckType_value = map[string]int32{
	"BANNED_CHECK_TYPE_UNSPECIFIED":       0,
	"BANNED_CHECK_TYPE_KEEP_BANNED":       1,
	"BANNED_CHECK_TYPE_UNBANNED_BY_CHECK": 2,
	"BANNED_CHECK_TYPE_UNBANNED_BY_OTHER": 3,
	"BANNED_CHECK_TYPE_REJECT_UNBAN":      4,
}

func (x BannedCheckType) String() string {
	return proto.EnumName(BannedCheckType_name, int32(x))
}
func (BannedCheckType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{6}
}

// 认证类型
type AuthType int32

const (
	AuthType_AUTH_INVALID          AuthType = 0
	AuthType_ENUM_AUTH_TYPE_IDCARD AuthType = 1
	AuthType_ENUM_AUTH_TYPE_FACE   AuthType = 2
)

var AuthType_name = map[int32]string{
	0: "AUTH_INVALID",
	1: "ENUM_AUTH_TYPE_IDCARD",
	2: "ENUM_AUTH_TYPE_FACE",
}
var AuthType_value = map[string]int32{
	"AUTH_INVALID":          0,
	"ENUM_AUTH_TYPE_IDCARD": 1,
	"ENUM_AUTH_TYPE_FACE":   2,
}

func (x AuthType) String() string {
	return proto.EnumName(AuthType_name, int32(x))
}
func (AuthType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{7}
}

// 认证状态
type EAuthStatus int32

const (
	EAuthStatus_STATUS_INVALID      EAuthStatus = 0
	EAuthStatus_ENUM_AUTH_NONE      EAuthStatus = 1
	EAuthStatus_ENUM_AUTH_CHECK     EAuthStatus = 2
	EAuthStatus_ENUM_AUTH_PASS      EAuthStatus = 3
	EAuthStatus_ENUM_AUTH_UNAPPROVE EAuthStatus = 4
)

var EAuthStatus_name = map[int32]string{
	0: "STATUS_INVALID",
	1: "ENUM_AUTH_NONE",
	2: "ENUM_AUTH_CHECK",
	3: "ENUM_AUTH_PASS",
	4: "ENUM_AUTH_UNAPPROVE",
}
var EAuthStatus_value = map[string]int32{
	"STATUS_INVALID":      0,
	"ENUM_AUTH_NONE":      1,
	"ENUM_AUTH_CHECK":     2,
	"ENUM_AUTH_PASS":      3,
	"ENUM_AUTH_UNAPPROVE": 4,
}

func (x EAuthStatus) String() string {
	return proto.EnumName(EAuthStatus_name, int32(x))
}
func (EAuthStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{8}
}

type BanType int32

const (
	BanType_OP_UNKNOWN BanType = 0
	BanType_OP_BAN     BanType = 1
	BanType_OP_UNBAN   BanType = 2
)

var BanType_name = map[int32]string{
	0: "OP_UNKNOWN",
	1: "OP_BAN",
	2: "OP_UNBAN",
}
var BanType_value = map[string]int32{
	"OP_UNKNOWN": 0,
	"OP_BAN":     1,
	"OP_UNBAN":   2,
}

func (x BanType) String() string {
	return proto.EnumName(BanType_name, int32(x))
}
func (BanType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{9}
}

type UpdateBannedCheckRecordReq_UpdateType int32

const (
	UpdateBannedCheckRecordReq_UPDATE_TYPE_UNSPECIFIED    UpdateBannedCheckRecordReq_UpdateType = 0
	UpdateBannedCheckRecordReq_UPDATE_TYPE_RECOVER        UpdateBannedCheckRecordReq_UpdateType = 1
	UpdateBannedCheckRecordReq_UPDATE_TYPE_KEEP_BANNED    UpdateBannedCheckRecordReq_UpdateType = 2
	UpdateBannedCheckRecordReq_UPDATE_TYPE_REJECT_RECOVER UpdateBannedCheckRecordReq_UpdateType = 3
	UpdateBannedCheckRecordReq_UPDATE_TYPE_CHECKING       UpdateBannedCheckRecordReq_UpdateType = 4
	UpdateBannedCheckRecordReq_UPDATE_TYPE_TO_CHECK       UpdateBannedCheckRecordReq_UpdateType = 5
)

var UpdateBannedCheckRecordReq_UpdateType_name = map[int32]string{
	0: "UPDATE_TYPE_UNSPECIFIED",
	1: "UPDATE_TYPE_RECOVER",
	2: "UPDATE_TYPE_KEEP_BANNED",
	3: "UPDATE_TYPE_REJECT_RECOVER",
	4: "UPDATE_TYPE_CHECKING",
	5: "UPDATE_TYPE_TO_CHECK",
}
var UpdateBannedCheckRecordReq_UpdateType_value = map[string]int32{
	"UPDATE_TYPE_UNSPECIFIED":    0,
	"UPDATE_TYPE_RECOVER":        1,
	"UPDATE_TYPE_KEEP_BANNED":    2,
	"UPDATE_TYPE_REJECT_RECOVER": 3,
	"UPDATE_TYPE_CHECKING":       4,
	"UPDATE_TYPE_TO_CHECK":       5,
}

func (x UpdateBannedCheckRecordReq_UpdateType) String() string {
	return proto.EnumName(UpdateBannedCheckRecordReq_UpdateType_name, int32(x))
}
func (UpdateBannedCheckRecordReq_UpdateType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{29, 0}
}

type GetUserControlInfoReq_QueryType int32

const (
	GetUserControlInfoReq_QUERY_ALL     GetUserControlInfoReq_QueryType = 0
	GetUserControlInfoReq_QUERY_BY_UIDS GetUserControlInfoReq_QueryType = 1
)

var GetUserControlInfoReq_QueryType_name = map[int32]string{
	0: "QUERY_ALL",
	1: "QUERY_BY_UIDS",
}
var GetUserControlInfoReq_QueryType_value = map[string]int32{
	"QUERY_ALL":     0,
	"QUERY_BY_UIDS": 1,
}

func (x GetUserControlInfoReq_QueryType) String() string {
	return proto.EnumName(GetUserControlInfoReq_QueryType_name, int32(x))
}
func (GetUserControlInfoReq_QueryType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{65, 0}
}

type AwardInfo struct {
	Ttid                 string   `protobuf:"bytes,1,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Value                uint32   `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AwardInfo) Reset()         { *m = AwardInfo{} }
func (m *AwardInfo) String() string { return proto.CompactTextString(m) }
func (*AwardInfo) ProtoMessage()    {}
func (*AwardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{0}
}
func (m *AwardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardInfo.Unmarshal(m, b)
}
func (m *AwardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardInfo.Marshal(b, m, deterministic)
}
func (dst *AwardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardInfo.Merge(dst, src)
}
func (m *AwardInfo) XXX_Size() int {
	return xxx_messageInfo_AwardInfo.Size(m)
}
func (m *AwardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AwardInfo proto.InternalMessageInfo

func (m *AwardInfo) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *AwardInfo) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

type AwardRecord struct {
	Value                uint32   `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
	OpTime               string   `protobuf:"bytes,2,opt,name=op_time,json=opTime,proto3" json:"op_time,omitempty"`
	Desc                 string   `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AwardRecord) Reset()         { *m = AwardRecord{} }
func (m *AwardRecord) String() string { return proto.CompactTextString(m) }
func (*AwardRecord) ProtoMessage()    {}
func (*AwardRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{1}
}
func (m *AwardRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardRecord.Unmarshal(m, b)
}
func (m *AwardRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardRecord.Marshal(b, m, deterministic)
}
func (dst *AwardRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardRecord.Merge(dst, src)
}
func (m *AwardRecord) XXX_Size() int {
	return xxx_messageInfo_AwardRecord.Size(m)
}
func (m *AwardRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardRecord.DiscardUnknown(m)
}

var xxx_messageInfo_AwardRecord proto.InternalMessageInfo

func (m *AwardRecord) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *AwardRecord) GetOpTime() string {
	if m != nil {
		return m.OpTime
	}
	return ""
}

func (m *AwardRecord) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

// 发放T豆
type AwardTBeanToUserReq struct {
	OpUserTtid           string       `protobuf:"bytes,1,opt,name=op_user_ttid,json=opUserTtid,proto3" json:"op_user_ttid,omitempty"`
	OpUserPwdmd5         string       `protobuf:"bytes,2,opt,name=op_user_pwdmd5,json=opUserPwdmd5,proto3" json:"op_user_pwdmd5,omitempty"`
	PayPwdMd5            string       `protobuf:"bytes,3,opt,name=pay_pwd_md5,json=payPwdMd5,proto3" json:"pay_pwd_md5,omitempty"`
	AwardList            []*AwardInfo `protobuf:"bytes,4,rep,name=award_list,json=awardList,proto3" json:"award_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AwardTBeanToUserReq) Reset()         { *m = AwardTBeanToUserReq{} }
func (m *AwardTBeanToUserReq) String() string { return proto.CompactTextString(m) }
func (*AwardTBeanToUserReq) ProtoMessage()    {}
func (*AwardTBeanToUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{2}
}
func (m *AwardTBeanToUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardTBeanToUserReq.Unmarshal(m, b)
}
func (m *AwardTBeanToUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardTBeanToUserReq.Marshal(b, m, deterministic)
}
func (dst *AwardTBeanToUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardTBeanToUserReq.Merge(dst, src)
}
func (m *AwardTBeanToUserReq) XXX_Size() int {
	return xxx_messageInfo_AwardTBeanToUserReq.Size(m)
}
func (m *AwardTBeanToUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardTBeanToUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_AwardTBeanToUserReq proto.InternalMessageInfo

func (m *AwardTBeanToUserReq) GetOpUserTtid() string {
	if m != nil {
		return m.OpUserTtid
	}
	return ""
}

func (m *AwardTBeanToUserReq) GetOpUserPwdmd5() string {
	if m != nil {
		return m.OpUserPwdmd5
	}
	return ""
}

func (m *AwardTBeanToUserReq) GetPayPwdMd5() string {
	if m != nil {
		return m.PayPwdMd5
	}
	return ""
}

func (m *AwardTBeanToUserReq) GetAwardList() []*AwardInfo {
	if m != nil {
		return m.AwardList
	}
	return nil
}

type AwardTBeanToUserResp struct {
	FailedTtids          []string `protobuf:"bytes,1,rep,name=failed_ttids,json=failedTtids,proto3" json:"failed_ttids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AwardTBeanToUserResp) Reset()         { *m = AwardTBeanToUserResp{} }
func (m *AwardTBeanToUserResp) String() string { return proto.CompactTextString(m) }
func (*AwardTBeanToUserResp) ProtoMessage()    {}
func (*AwardTBeanToUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{3}
}
func (m *AwardTBeanToUserResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardTBeanToUserResp.Unmarshal(m, b)
}
func (m *AwardTBeanToUserResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardTBeanToUserResp.Marshal(b, m, deterministic)
}
func (dst *AwardTBeanToUserResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardTBeanToUserResp.Merge(dst, src)
}
func (m *AwardTBeanToUserResp) XXX_Size() int {
	return xxx_messageInfo_AwardTBeanToUserResp.Size(m)
}
func (m *AwardTBeanToUserResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardTBeanToUserResp.DiscardUnknown(m)
}

var xxx_messageInfo_AwardTBeanToUserResp proto.InternalMessageInfo

func (m *AwardTBeanToUserResp) GetFailedTtids() []string {
	if m != nil {
		return m.FailedTtids
	}
	return nil
}

// 发放红钻
type AwardDiamondToUserReq struct {
	AwardList            []*AwardInfo `protobuf:"bytes,1,rep,name=award_list,json=awardList,proto3" json:"award_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AwardDiamondToUserReq) Reset()         { *m = AwardDiamondToUserReq{} }
func (m *AwardDiamondToUserReq) String() string { return proto.CompactTextString(m) }
func (*AwardDiamondToUserReq) ProtoMessage()    {}
func (*AwardDiamondToUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{4}
}
func (m *AwardDiamondToUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardDiamondToUserReq.Unmarshal(m, b)
}
func (m *AwardDiamondToUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardDiamondToUserReq.Marshal(b, m, deterministic)
}
func (dst *AwardDiamondToUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardDiamondToUserReq.Merge(dst, src)
}
func (m *AwardDiamondToUserReq) XXX_Size() int {
	return xxx_messageInfo_AwardDiamondToUserReq.Size(m)
}
func (m *AwardDiamondToUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardDiamondToUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_AwardDiamondToUserReq proto.InternalMessageInfo

func (m *AwardDiamondToUserReq) GetAwardList() []*AwardInfo {
	if m != nil {
		return m.AwardList
	}
	return nil
}

type AwardDiamondToUserResp struct {
	FailedTtids          []string `protobuf:"bytes,1,rep,name=failed_ttids,json=failedTtids,proto3" json:"failed_ttids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AwardDiamondToUserResp) Reset()         { *m = AwardDiamondToUserResp{} }
func (m *AwardDiamondToUserResp) String() string { return proto.CompactTextString(m) }
func (*AwardDiamondToUserResp) ProtoMessage()    {}
func (*AwardDiamondToUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{5}
}
func (m *AwardDiamondToUserResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardDiamondToUserResp.Unmarshal(m, b)
}
func (m *AwardDiamondToUserResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardDiamondToUserResp.Marshal(b, m, deterministic)
}
func (dst *AwardDiamondToUserResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardDiamondToUserResp.Merge(dst, src)
}
func (m *AwardDiamondToUserResp) XXX_Size() int {
	return xxx_messageInfo_AwardDiamondToUserResp.Size(m)
}
func (m *AwardDiamondToUserResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardDiamondToUserResp.DiscardUnknown(m)
}

var xxx_messageInfo_AwardDiamondToUserResp proto.InternalMessageInfo

func (m *AwardDiamondToUserResp) GetFailedTtids() []string {
	if m != nil {
		return m.FailedTtids
	}
	return nil
}

// 获取红钻发放记录
type GetAwardDiamondRecordReq struct {
	Ttid                 string   `protobuf:"bytes,1,opt,name=ttid,proto3" json:"ttid,omitempty"`
	BeginTime            uint32   `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAwardDiamondRecordReq) Reset()         { *m = GetAwardDiamondRecordReq{} }
func (m *GetAwardDiamondRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetAwardDiamondRecordReq) ProtoMessage()    {}
func (*GetAwardDiamondRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{6}
}
func (m *GetAwardDiamondRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAwardDiamondRecordReq.Unmarshal(m, b)
}
func (m *GetAwardDiamondRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAwardDiamondRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetAwardDiamondRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAwardDiamondRecordReq.Merge(dst, src)
}
func (m *GetAwardDiamondRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetAwardDiamondRecordReq.Size(m)
}
func (m *GetAwardDiamondRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAwardDiamondRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAwardDiamondRecordReq proto.InternalMessageInfo

func (m *GetAwardDiamondRecordReq) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *GetAwardDiamondRecordReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetAwardDiamondRecordReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetAwardDiamondRecordResp struct {
	RecordList           []*AwardRecord `protobuf:"bytes,1,rep,name=record_list,json=recordList,proto3" json:"record_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetAwardDiamondRecordResp) Reset()         { *m = GetAwardDiamondRecordResp{} }
func (m *GetAwardDiamondRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetAwardDiamondRecordResp) ProtoMessage()    {}
func (*GetAwardDiamondRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{7}
}
func (m *GetAwardDiamondRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAwardDiamondRecordResp.Unmarshal(m, b)
}
func (m *GetAwardDiamondRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAwardDiamondRecordResp.Marshal(b, m, deterministic)
}
func (dst *GetAwardDiamondRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAwardDiamondRecordResp.Merge(dst, src)
}
func (m *GetAwardDiamondRecordResp) XXX_Size() int {
	return xxx_messageInfo_GetAwardDiamondRecordResp.Size(m)
}
func (m *GetAwardDiamondRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAwardDiamondRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAwardDiamondRecordResp proto.InternalMessageInfo

func (m *GetAwardDiamondRecordResp) GetRecordList() []*AwardRecord {
	if m != nil {
		return m.RecordList
	}
	return nil
}

// 发放经验
type AwardExpToUserReq struct {
	AwardList            []*AwardInfo `protobuf:"bytes,1,rep,name=award_list,json=awardList,proto3" json:"award_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AwardExpToUserReq) Reset()         { *m = AwardExpToUserReq{} }
func (m *AwardExpToUserReq) String() string { return proto.CompactTextString(m) }
func (*AwardExpToUserReq) ProtoMessage()    {}
func (*AwardExpToUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{8}
}
func (m *AwardExpToUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardExpToUserReq.Unmarshal(m, b)
}
func (m *AwardExpToUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardExpToUserReq.Marshal(b, m, deterministic)
}
func (dst *AwardExpToUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardExpToUserReq.Merge(dst, src)
}
func (m *AwardExpToUserReq) XXX_Size() int {
	return xxx_messageInfo_AwardExpToUserReq.Size(m)
}
func (m *AwardExpToUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardExpToUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_AwardExpToUserReq proto.InternalMessageInfo

func (m *AwardExpToUserReq) GetAwardList() []*AwardInfo {
	if m != nil {
		return m.AwardList
	}
	return nil
}

type AwardExpToUserResp struct {
	FailedTtids          []string `protobuf:"bytes,1,rep,name=failed_ttids,json=failedTtids,proto3" json:"failed_ttids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AwardExpToUserResp) Reset()         { *m = AwardExpToUserResp{} }
func (m *AwardExpToUserResp) String() string { return proto.CompactTextString(m) }
func (*AwardExpToUserResp) ProtoMessage()    {}
func (*AwardExpToUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{9}
}
func (m *AwardExpToUserResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardExpToUserResp.Unmarshal(m, b)
}
func (m *AwardExpToUserResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardExpToUserResp.Marshal(b, m, deterministic)
}
func (dst *AwardExpToUserResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardExpToUserResp.Merge(dst, src)
}
func (m *AwardExpToUserResp) XXX_Size() int {
	return xxx_messageInfo_AwardExpToUserResp.Size(m)
}
func (m *AwardExpToUserResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardExpToUserResp.DiscardUnknown(m)
}

var xxx_messageInfo_AwardExpToUserResp proto.InternalMessageInfo

func (m *AwardExpToUserResp) GetFailedTtids() []string {
	if m != nil {
		return m.FailedTtids
	}
	return nil
}

// 获取经验发放记录
type GetAwardExpRecordReq struct {
	Ttid                 string   `protobuf:"bytes,1,opt,name=ttid,proto3" json:"ttid,omitempty"`
	BeginTime            uint32   `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAwardExpRecordReq) Reset()         { *m = GetAwardExpRecordReq{} }
func (m *GetAwardExpRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetAwardExpRecordReq) ProtoMessage()    {}
func (*GetAwardExpRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{10}
}
func (m *GetAwardExpRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAwardExpRecordReq.Unmarshal(m, b)
}
func (m *GetAwardExpRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAwardExpRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetAwardExpRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAwardExpRecordReq.Merge(dst, src)
}
func (m *GetAwardExpRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetAwardExpRecordReq.Size(m)
}
func (m *GetAwardExpRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAwardExpRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAwardExpRecordReq proto.InternalMessageInfo

func (m *GetAwardExpRecordReq) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *GetAwardExpRecordReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetAwardExpRecordReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetAwardExpRecordResp struct {
	RecordList           []*AwardRecord `protobuf:"bytes,1,rep,name=record_list,json=recordList,proto3" json:"record_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetAwardExpRecordResp) Reset()         { *m = GetAwardExpRecordResp{} }
func (m *GetAwardExpRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetAwardExpRecordResp) ProtoMessage()    {}
func (*GetAwardExpRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{11}
}
func (m *GetAwardExpRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAwardExpRecordResp.Unmarshal(m, b)
}
func (m *GetAwardExpRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAwardExpRecordResp.Marshal(b, m, deterministic)
}
func (dst *GetAwardExpRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAwardExpRecordResp.Merge(dst, src)
}
func (m *GetAwardExpRecordResp) XXX_Size() int {
	return xxx_messageInfo_GetAwardExpRecordResp.Size(m)
}
func (m *GetAwardExpRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAwardExpRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAwardExpRecordResp proto.InternalMessageInfo

func (m *GetAwardExpRecordResp) GetRecordList() []*AwardRecord {
	if m != nil {
		return m.RecordList
	}
	return nil
}

// 用户登录信息
type UserLoginInfo struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	DeviceInfo           string   `protobuf:"bytes,2,opt,name=device_info,json=deviceInfo,proto3" json:"device_info,omitempty"`
	DeviceId             string   `protobuf:"bytes,3,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	DeviceModel          string   `protobuf:"bytes,4,opt,name=device_model,json=deviceModel,proto3" json:"device_model,omitempty"`
	IsEmulator           uint32   `protobuf:"varint,5,opt,name=is_emulator,json=isEmulator,proto3" json:"is_emulator,omitempty"`
	ClientVer            string   `protobuf:"bytes,6,opt,name=client_ver,json=clientVer,proto3" json:"client_ver,omitempty"`
	ClientIp             string   `protobuf:"bytes,7,opt,name=clientIp,proto3" json:"clientIp,omitempty"`
	ClientType           uint32   `protobuf:"varint,8,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	OsVer                string   `protobuf:"bytes,9,opt,name=os_ver,json=osVer,proto3" json:"os_ver,omitempty"`
	OsType               string   `protobuf:"bytes,10,opt,name=os_type,json=osType,proto3" json:"os_type,omitempty"`
	LoginTime            string   `protobuf:"bytes,11,opt,name=login_time,json=loginTime,proto3" json:"login_time,omitempty"`
	OpType               uint32   `protobuf:"varint,12,opt,name=op_type,json=opType,proto3" json:"op_type,omitempty"`
	Result               int32    `protobuf:"varint,13,opt,name=result,proto3" json:"result,omitempty"`
	Terminal             string   `protobuf:"bytes,14,opt,name=terminal,proto3" json:"terminal,omitempty"`
	Phone                string   `protobuf:"bytes,15,opt,name=phone,proto3" json:"phone,omitempty"`
	OpenId               string   `protobuf:"bytes,16,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserLoginInfo) Reset()         { *m = UserLoginInfo{} }
func (m *UserLoginInfo) String() string { return proto.CompactTextString(m) }
func (*UserLoginInfo) ProtoMessage()    {}
func (*UserLoginInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{12}
}
func (m *UserLoginInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserLoginInfo.Unmarshal(m, b)
}
func (m *UserLoginInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserLoginInfo.Marshal(b, m, deterministic)
}
func (dst *UserLoginInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserLoginInfo.Merge(dst, src)
}
func (m *UserLoginInfo) XXX_Size() int {
	return xxx_messageInfo_UserLoginInfo.Size(m)
}
func (m *UserLoginInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserLoginInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserLoginInfo proto.InternalMessageInfo

func (m *UserLoginInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserLoginInfo) GetDeviceInfo() string {
	if m != nil {
		return m.DeviceInfo
	}
	return ""
}

func (m *UserLoginInfo) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *UserLoginInfo) GetDeviceModel() string {
	if m != nil {
		return m.DeviceModel
	}
	return ""
}

func (m *UserLoginInfo) GetIsEmulator() uint32 {
	if m != nil {
		return m.IsEmulator
	}
	return 0
}

func (m *UserLoginInfo) GetClientVer() string {
	if m != nil {
		return m.ClientVer
	}
	return ""
}

func (m *UserLoginInfo) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

func (m *UserLoginInfo) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *UserLoginInfo) GetOsVer() string {
	if m != nil {
		return m.OsVer
	}
	return ""
}

func (m *UserLoginInfo) GetOsType() string {
	if m != nil {
		return m.OsType
	}
	return ""
}

func (m *UserLoginInfo) GetLoginTime() string {
	if m != nil {
		return m.LoginTime
	}
	return ""
}

func (m *UserLoginInfo) GetOpType() uint32 {
	if m != nil {
		return m.OpType
	}
	return 0
}

func (m *UserLoginInfo) GetResult() int32 {
	if m != nil {
		return m.Result
	}
	return 0
}

func (m *UserLoginInfo) GetTerminal() string {
	if m != nil {
		return m.Terminal
	}
	return ""
}

func (m *UserLoginInfo) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *UserLoginInfo) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

// 获取登录信息
type GetUserLoginInfoReq struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	BeginTime            uint32   `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	NeedAutoLogin        bool     `protobuf:"varint,4,opt,name=need_auto_login,json=needAutoLogin,proto3" json:"need_auto_login,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserLoginInfoReq) Reset()         { *m = GetUserLoginInfoReq{} }
func (m *GetUserLoginInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserLoginInfoReq) ProtoMessage()    {}
func (*GetUserLoginInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{13}
}
func (m *GetUserLoginInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserLoginInfoReq.Unmarshal(m, b)
}
func (m *GetUserLoginInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserLoginInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserLoginInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserLoginInfoReq.Merge(dst, src)
}
func (m *GetUserLoginInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserLoginInfoReq.Size(m)
}
func (m *GetUserLoginInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserLoginInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserLoginInfoReq proto.InternalMessageInfo

func (m *GetUserLoginInfoReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *GetUserLoginInfoReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetUserLoginInfoReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetUserLoginInfoReq) GetNeedAutoLogin() bool {
	if m != nil {
		return m.NeedAutoLogin
	}
	return false
}

type GetUserLoginInfoResp struct {
	InfoList             []*UserLoginInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetUserLoginInfoResp) Reset()         { *m = GetUserLoginInfoResp{} }
func (m *GetUserLoginInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserLoginInfoResp) ProtoMessage()    {}
func (*GetUserLoginInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{14}
}
func (m *GetUserLoginInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserLoginInfoResp.Unmarshal(m, b)
}
func (m *GetUserLoginInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserLoginInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserLoginInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserLoginInfoResp.Merge(dst, src)
}
func (m *GetUserLoginInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserLoginInfoResp.Size(m)
}
func (m *GetUserLoginInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserLoginInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserLoginInfoResp proto.InternalMessageInfo

func (m *GetUserLoginInfoResp) GetInfoList() []*UserLoginInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type BannedRecord struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	OpTime               uint32   `protobuf:"varint,2,opt,name=op_time,json=opTime,proto3" json:"op_time,omitempty"`
	OpType               uint32   `protobuf:"varint,3,opt,name=op_type,json=opType,proto3" json:"op_type,omitempty"`
	OperatorName         string   `protobuf:"bytes,4,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	Reason               string   `protobuf:"bytes,5,opt,name=reason,proto3" json:"reason,omitempty"`
	ProofPic             string   `protobuf:"bytes,6,opt,name=proof_pic,json=proofPic,proto3" json:"proof_pic,omitempty"`
	AutoRecoveryAt       uint32   `protobuf:"varint,7,opt,name=auto_recovery_at,json=autoRecoveryAt,proto3" json:"auto_recovery_at,omitempty"`
	ReasonDetail         string   `protobuf:"bytes,8,opt,name=reason_detail,json=reasonDetail,proto3" json:"reason_detail,omitempty"`
	Uid                  uint32   `protobuf:"varint,9,opt,name=uid,proto3" json:"uid,omitempty"`
	BannedDays           uint64   `protobuf:"varint,10,opt,name=banned_days,json=bannedDays,proto3" json:"banned_days,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BannedRecord) Reset()         { *m = BannedRecord{} }
func (m *BannedRecord) String() string { return proto.CompactTextString(m) }
func (*BannedRecord) ProtoMessage()    {}
func (*BannedRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{15}
}
func (m *BannedRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BannedRecord.Unmarshal(m, b)
}
func (m *BannedRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BannedRecord.Marshal(b, m, deterministic)
}
func (dst *BannedRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BannedRecord.Merge(dst, src)
}
func (m *BannedRecord) XXX_Size() int {
	return xxx_messageInfo_BannedRecord.Size(m)
}
func (m *BannedRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_BannedRecord.DiscardUnknown(m)
}

var xxx_messageInfo_BannedRecord proto.InternalMessageInfo

func (m *BannedRecord) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *BannedRecord) GetOpTime() uint32 {
	if m != nil {
		return m.OpTime
	}
	return 0
}

func (m *BannedRecord) GetOpType() uint32 {
	if m != nil {
		return m.OpType
	}
	return 0
}

func (m *BannedRecord) GetOperatorName() string {
	if m != nil {
		return m.OperatorName
	}
	return ""
}

func (m *BannedRecord) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *BannedRecord) GetProofPic() string {
	if m != nil {
		return m.ProofPic
	}
	return ""
}

func (m *BannedRecord) GetAutoRecoveryAt() uint32 {
	if m != nil {
		return m.AutoRecoveryAt
	}
	return 0
}

func (m *BannedRecord) GetReasonDetail() string {
	if m != nil {
		return m.ReasonDetail
	}
	return ""
}

func (m *BannedRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BannedRecord) GetBannedDays() uint64 {
	if m != nil {
		return m.BannedDays
	}
	return 0
}

// 获取封禁黑名单列表
type GetBannedRecordReq struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Page                 uint32   `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Uid                  uint32   `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	BeginTime            uint32   `protobuf:"varint,5,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	OperatorName         string   `protobuf:"bytes,7,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	Reason               string   `protobuf:"bytes,8,opt,name=reason,proto3" json:"reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBannedRecordReq) Reset()         { *m = GetBannedRecordReq{} }
func (m *GetBannedRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetBannedRecordReq) ProtoMessage()    {}
func (*GetBannedRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{16}
}
func (m *GetBannedRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBannedRecordReq.Unmarshal(m, b)
}
func (m *GetBannedRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBannedRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetBannedRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBannedRecordReq.Merge(dst, src)
}
func (m *GetBannedRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetBannedRecordReq.Size(m)
}
func (m *GetBannedRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBannedRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBannedRecordReq proto.InternalMessageInfo

func (m *GetBannedRecordReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *GetBannedRecordReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetBannedRecordReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetBannedRecordReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetBannedRecordReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetBannedRecordReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetBannedRecordReq) GetOperatorName() string {
	if m != nil {
		return m.OperatorName
	}
	return ""
}

func (m *GetBannedRecordReq) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

type GetBannedRecordResp struct {
	RecordList           []*BannedRecord `protobuf:"bytes,1,rep,name=record_list,json=recordList,proto3" json:"record_list,omitempty"`
	TotalCnt             uint32          `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetBannedRecordResp) Reset()         { *m = GetBannedRecordResp{} }
func (m *GetBannedRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetBannedRecordResp) ProtoMessage()    {}
func (*GetBannedRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{17}
}
func (m *GetBannedRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBannedRecordResp.Unmarshal(m, b)
}
func (m *GetBannedRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBannedRecordResp.Marshal(b, m, deterministic)
}
func (dst *GetBannedRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBannedRecordResp.Merge(dst, src)
}
func (m *GetBannedRecordResp) XXX_Size() int {
	return xxx_messageInfo_GetBannedRecordResp.Size(m)
}
func (m *GetBannedRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBannedRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBannedRecordResp proto.InternalMessageInfo

func (m *GetBannedRecordResp) GetRecordList() []*BannedRecord {
	if m != nil {
		return m.RecordList
	}
	return nil
}

func (m *GetBannedRecordResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

// 获取封禁操作人
type GetBannedOperatorReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBannedOperatorReq) Reset()         { *m = GetBannedOperatorReq{} }
func (m *GetBannedOperatorReq) String() string { return proto.CompactTextString(m) }
func (*GetBannedOperatorReq) ProtoMessage()    {}
func (*GetBannedOperatorReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{18}
}
func (m *GetBannedOperatorReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBannedOperatorReq.Unmarshal(m, b)
}
func (m *GetBannedOperatorReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBannedOperatorReq.Marshal(b, m, deterministic)
}
func (dst *GetBannedOperatorReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBannedOperatorReq.Merge(dst, src)
}
func (m *GetBannedOperatorReq) XXX_Size() int {
	return xxx_messageInfo_GetBannedOperatorReq.Size(m)
}
func (m *GetBannedOperatorReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBannedOperatorReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBannedOperatorReq proto.InternalMessageInfo

type BannedOperator struct {
	OperatorName         string   `protobuf:"bytes,1,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BannedOperator) Reset()         { *m = BannedOperator{} }
func (m *BannedOperator) String() string { return proto.CompactTextString(m) }
func (*BannedOperator) ProtoMessage()    {}
func (*BannedOperator) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{19}
}
func (m *BannedOperator) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BannedOperator.Unmarshal(m, b)
}
func (m *BannedOperator) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BannedOperator.Marshal(b, m, deterministic)
}
func (dst *BannedOperator) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BannedOperator.Merge(dst, src)
}
func (m *BannedOperator) XXX_Size() int {
	return xxx_messageInfo_BannedOperator.Size(m)
}
func (m *BannedOperator) XXX_DiscardUnknown() {
	xxx_messageInfo_BannedOperator.DiscardUnknown(m)
}

var xxx_messageInfo_BannedOperator proto.InternalMessageInfo

func (m *BannedOperator) GetOperatorName() string {
	if m != nil {
		return m.OperatorName
	}
	return ""
}

type GetBannedOperatorResp struct {
	OperatorList         []*BannedOperator `protobuf:"bytes,1,rep,name=operator_list,json=operatorList,proto3" json:"operator_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetBannedOperatorResp) Reset()         { *m = GetBannedOperatorResp{} }
func (m *GetBannedOperatorResp) String() string { return proto.CompactTextString(m) }
func (*GetBannedOperatorResp) ProtoMessage()    {}
func (*GetBannedOperatorResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{20}
}
func (m *GetBannedOperatorResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBannedOperatorResp.Unmarshal(m, b)
}
func (m *GetBannedOperatorResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBannedOperatorResp.Marshal(b, m, deterministic)
}
func (dst *GetBannedOperatorResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBannedOperatorResp.Merge(dst, src)
}
func (m *GetBannedOperatorResp) XXX_Size() int {
	return xxx_messageInfo_GetBannedOperatorResp.Size(m)
}
func (m *GetBannedOperatorResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBannedOperatorResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBannedOperatorResp proto.InternalMessageInfo

func (m *GetBannedOperatorResp) GetOperatorList() []*BannedOperator {
	if m != nil {
		return m.OperatorList
	}
	return nil
}

type BannedAppealRecord struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Ttid                 string   `protobuf:"bytes,3,opt,name=ttid,proto3" json:"ttid,omitempty"`
	State                uint32   `protobuf:"varint,4,opt,name=state,proto3" json:"state,omitempty"`
	ReasonCode           uint32   `protobuf:"varint,5,opt,name=reason_code,json=reasonCode,proto3" json:"reason_code,omitempty"`
	BannedBeginAt        int64    `protobuf:"varint,6,opt,name=banned_begin_at,json=bannedBeginAt,proto3" json:"banned_begin_at,omitempty"`
	BannedEndAt          int64    `protobuf:"varint,7,opt,name=banned_end_at,json=bannedEndAt,proto3" json:"banned_end_at,omitempty"`
	CreateAt             int64    `protobuf:"varint,8,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	UpdateAt             int64    `protobuf:"varint,9,opt,name=update_at,json=updateAt,proto3" json:"update_at,omitempty"`
	CreateDesc           string   `protobuf:"bytes,10,opt,name=create_desc,json=createDesc,proto3" json:"create_desc,omitempty"`
	UpdateDesc           string   `protobuf:"bytes,11,opt,name=update_desc,json=updateDesc,proto3" json:"update_desc,omitempty"`
	Operator             string   `protobuf:"bytes,12,opt,name=operator,proto3" json:"operator,omitempty"`
	MarketId             uint32   `protobuf:"varint,13,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BannedAppealRecord) Reset()         { *m = BannedAppealRecord{} }
func (m *BannedAppealRecord) String() string { return proto.CompactTextString(m) }
func (*BannedAppealRecord) ProtoMessage()    {}
func (*BannedAppealRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{21}
}
func (m *BannedAppealRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BannedAppealRecord.Unmarshal(m, b)
}
func (m *BannedAppealRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BannedAppealRecord.Marshal(b, m, deterministic)
}
func (dst *BannedAppealRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BannedAppealRecord.Merge(dst, src)
}
func (m *BannedAppealRecord) XXX_Size() int {
	return xxx_messageInfo_BannedAppealRecord.Size(m)
}
func (m *BannedAppealRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_BannedAppealRecord.DiscardUnknown(m)
}

var xxx_messageInfo_BannedAppealRecord proto.InternalMessageInfo

func (m *BannedAppealRecord) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *BannedAppealRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BannedAppealRecord) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *BannedAppealRecord) GetState() uint32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *BannedAppealRecord) GetReasonCode() uint32 {
	if m != nil {
		return m.ReasonCode
	}
	return 0
}

func (m *BannedAppealRecord) GetBannedBeginAt() int64 {
	if m != nil {
		return m.BannedBeginAt
	}
	return 0
}

func (m *BannedAppealRecord) GetBannedEndAt() int64 {
	if m != nil {
		return m.BannedEndAt
	}
	return 0
}

func (m *BannedAppealRecord) GetCreateAt() int64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *BannedAppealRecord) GetUpdateAt() int64 {
	if m != nil {
		return m.UpdateAt
	}
	return 0
}

func (m *BannedAppealRecord) GetCreateDesc() string {
	if m != nil {
		return m.CreateDesc
	}
	return ""
}

func (m *BannedAppealRecord) GetUpdateDesc() string {
	if m != nil {
		return m.UpdateDesc
	}
	return ""
}

func (m *BannedAppealRecord) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *BannedAppealRecord) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

type GetBannedAppealRecordReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	TtidList             []string `protobuf:"bytes,2,rep,name=ttid_list,json=ttidList,proto3" json:"ttid_list,omitempty"`
	StateList            []uint32 `protobuf:"varint,3,rep,packed,name=state_list,json=stateList,proto3" json:"state_list,omitempty"`
	ReasonCodeList       []uint32 `protobuf:"varint,4,rep,packed,name=reason_code_list,json=reasonCodeList,proto3" json:"reason_code_list,omitempty"`
	CreateBeginAt        int64    `protobuf:"varint,5,opt,name=create_begin_at,json=createBeginAt,proto3" json:"create_begin_at,omitempty"`
	CreateEndAt          int64    `protobuf:"varint,6,opt,name=create_end_at,json=createEndAt,proto3" json:"create_end_at,omitempty"`
	Offset               uint32   `protobuf:"varint,7,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,8,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBannedAppealRecordReq) Reset()         { *m = GetBannedAppealRecordReq{} }
func (m *GetBannedAppealRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetBannedAppealRecordReq) ProtoMessage()    {}
func (*GetBannedAppealRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{22}
}
func (m *GetBannedAppealRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBannedAppealRecordReq.Unmarshal(m, b)
}
func (m *GetBannedAppealRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBannedAppealRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetBannedAppealRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBannedAppealRecordReq.Merge(dst, src)
}
func (m *GetBannedAppealRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetBannedAppealRecordReq.Size(m)
}
func (m *GetBannedAppealRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBannedAppealRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBannedAppealRecordReq proto.InternalMessageInfo

func (m *GetBannedAppealRecordReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetBannedAppealRecordReq) GetTtidList() []string {
	if m != nil {
		return m.TtidList
	}
	return nil
}

func (m *GetBannedAppealRecordReq) GetStateList() []uint32 {
	if m != nil {
		return m.StateList
	}
	return nil
}

func (m *GetBannedAppealRecordReq) GetReasonCodeList() []uint32 {
	if m != nil {
		return m.ReasonCodeList
	}
	return nil
}

func (m *GetBannedAppealRecordReq) GetCreateBeginAt() int64 {
	if m != nil {
		return m.CreateBeginAt
	}
	return 0
}

func (m *GetBannedAppealRecordReq) GetCreateEndAt() int64 {
	if m != nil {
		return m.CreateEndAt
	}
	return 0
}

func (m *GetBannedAppealRecordReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetBannedAppealRecordReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetBannedAppealRecordResp struct {
	RecordList           []*BannedAppealRecord `protobuf:"bytes,1,rep,name=record_list,json=recordList,proto3" json:"record_list,omitempty"`
	Total                uint32                `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetBannedAppealRecordResp) Reset()         { *m = GetBannedAppealRecordResp{} }
func (m *GetBannedAppealRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetBannedAppealRecordResp) ProtoMessage()    {}
func (*GetBannedAppealRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{23}
}
func (m *GetBannedAppealRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBannedAppealRecordResp.Unmarshal(m, b)
}
func (m *GetBannedAppealRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBannedAppealRecordResp.Marshal(b, m, deterministic)
}
func (dst *GetBannedAppealRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBannedAppealRecordResp.Merge(dst, src)
}
func (m *GetBannedAppealRecordResp) XXX_Size() int {
	return xxx_messageInfo_GetBannedAppealRecordResp.Size(m)
}
func (m *GetBannedAppealRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBannedAppealRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBannedAppealRecordResp proto.InternalMessageInfo

func (m *GetBannedAppealRecordResp) GetRecordList() []*BannedAppealRecord {
	if m != nil {
		return m.RecordList
	}
	return nil
}

func (m *GetBannedAppealRecordResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type UpdateBannedAppealRecordReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	State                uint32   `protobuf:"varint,3,opt,name=state,proto3" json:"state,omitempty"`
	UpdateDesc           string   `protobuf:"bytes,4,opt,name=update_desc,json=updateDesc,proto3" json:"update_desc,omitempty"`
	Operator             string   `protobuf:"bytes,5,opt,name=operator,proto3" json:"operator,omitempty"`
	MarketId             uint32   `protobuf:"varint,6,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateBannedAppealRecordReq) Reset()         { *m = UpdateBannedAppealRecordReq{} }
func (m *UpdateBannedAppealRecordReq) String() string { return proto.CompactTextString(m) }
func (*UpdateBannedAppealRecordReq) ProtoMessage()    {}
func (*UpdateBannedAppealRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{24}
}
func (m *UpdateBannedAppealRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateBannedAppealRecordReq.Unmarshal(m, b)
}
func (m *UpdateBannedAppealRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateBannedAppealRecordReq.Marshal(b, m, deterministic)
}
func (dst *UpdateBannedAppealRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateBannedAppealRecordReq.Merge(dst, src)
}
func (m *UpdateBannedAppealRecordReq) XXX_Size() int {
	return xxx_messageInfo_UpdateBannedAppealRecordReq.Size(m)
}
func (m *UpdateBannedAppealRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateBannedAppealRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateBannedAppealRecordReq proto.InternalMessageInfo

func (m *UpdateBannedAppealRecordReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UpdateBannedAppealRecordReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateBannedAppealRecordReq) GetState() uint32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *UpdateBannedAppealRecordReq) GetUpdateDesc() string {
	if m != nil {
		return m.UpdateDesc
	}
	return ""
}

func (m *UpdateBannedAppealRecordReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *UpdateBannedAppealRecordReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

type UpdateBannedAppealRecordResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateBannedAppealRecordResp) Reset()         { *m = UpdateBannedAppealRecordResp{} }
func (m *UpdateBannedAppealRecordResp) String() string { return proto.CompactTextString(m) }
func (*UpdateBannedAppealRecordResp) ProtoMessage()    {}
func (*UpdateBannedAppealRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{25}
}
func (m *UpdateBannedAppealRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateBannedAppealRecordResp.Unmarshal(m, b)
}
func (m *UpdateBannedAppealRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateBannedAppealRecordResp.Marshal(b, m, deterministic)
}
func (dst *UpdateBannedAppealRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateBannedAppealRecordResp.Merge(dst, src)
}
func (m *UpdateBannedAppealRecordResp) XXX_Size() int {
	return xxx_messageInfo_UpdateBannedAppealRecordResp.Size(m)
}
func (m *UpdateBannedAppealRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateBannedAppealRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateBannedAppealRecordResp proto.InternalMessageInfo

type GetBannedCheckRecordReq struct {
	RecordType            BannedCheckRecordType `protobuf:"varint,1,opt,name=record_type,json=recordType,proto3,enum=apicentergo.BannedCheckRecordType" json:"record_type,omitempty"`
	Offset                uint32                `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                 uint32                `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	UidList               []uint32              `protobuf:"varint,4,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	TtidList              []string              `protobuf:"bytes,5,rep,name=ttid_list,json=ttidList,proto3" json:"ttid_list,omitempty"`
	UserTagList           []BannedCheckUserTag  `protobuf:"varint,6,rep,packed,name=user_tag_list,json=userTagList,proto3,enum=apicentergo.BannedCheckUserTag" json:"user_tag_list,omitempty"`
	SourceList            []BannedCheckSource   `protobuf:"varint,7,rep,packed,name=source_list,json=sourceList,proto3,enum=apicentergo.BannedCheckSource" json:"source_list,omitempty"`
	ReasonList            []string              `protobuf:"bytes,8,rep,name=reason_list,json=reasonList,proto3" json:"reason_list,omitempty"`
	OperatorNameList      []string              `protobuf:"bytes,9,rep,name=operator_name_list,json=operatorNameList,proto3" json:"operator_name_list,omitempty"`
	BannedBeginAt         int64                 `protobuf:"varint,10,opt,name=banned_begin_at,json=bannedBeginAt,proto3" json:"banned_begin_at,omitempty"`
	BannedEndAt           int64                 `protobuf:"varint,11,opt,name=banned_end_at,json=bannedEndAt,proto3" json:"banned_end_at,omitempty"`
	CheckOperatorNameList []string              `protobuf:"bytes,12,rep,name=check_operator_name_list,json=checkOperatorNameList,proto3" json:"check_operator_name_list,omitempty"`
	CheckBeginAt          int64                 `protobuf:"varint,13,opt,name=check_begin_at,json=checkBeginAt,proto3" json:"check_begin_at,omitempty"`
	CheckEndAt            int64                 `protobuf:"varint,14,opt,name=check_end_at,json=checkEndAt,proto3" json:"check_end_at,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}              `json:"-"`
	XXX_unrecognized      []byte                `json:"-"`
	XXX_sizecache         int32                 `json:"-"`
}

func (m *GetBannedCheckRecordReq) Reset()         { *m = GetBannedCheckRecordReq{} }
func (m *GetBannedCheckRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetBannedCheckRecordReq) ProtoMessage()    {}
func (*GetBannedCheckRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{26}
}
func (m *GetBannedCheckRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBannedCheckRecordReq.Unmarshal(m, b)
}
func (m *GetBannedCheckRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBannedCheckRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetBannedCheckRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBannedCheckRecordReq.Merge(dst, src)
}
func (m *GetBannedCheckRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetBannedCheckRecordReq.Size(m)
}
func (m *GetBannedCheckRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBannedCheckRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBannedCheckRecordReq proto.InternalMessageInfo

func (m *GetBannedCheckRecordReq) GetRecordType() BannedCheckRecordType {
	if m != nil {
		return m.RecordType
	}
	return BannedCheckRecordType_BANNED_CHECK_RECORD_TYPE_UNSPECIFIED
}

func (m *GetBannedCheckRecordReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetBannedCheckRecordReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetBannedCheckRecordReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetBannedCheckRecordReq) GetTtidList() []string {
	if m != nil {
		return m.TtidList
	}
	return nil
}

func (m *GetBannedCheckRecordReq) GetUserTagList() []BannedCheckUserTag {
	if m != nil {
		return m.UserTagList
	}
	return nil
}

func (m *GetBannedCheckRecordReq) GetSourceList() []BannedCheckSource {
	if m != nil {
		return m.SourceList
	}
	return nil
}

func (m *GetBannedCheckRecordReq) GetReasonList() []string {
	if m != nil {
		return m.ReasonList
	}
	return nil
}

func (m *GetBannedCheckRecordReq) GetOperatorNameList() []string {
	if m != nil {
		return m.OperatorNameList
	}
	return nil
}

func (m *GetBannedCheckRecordReq) GetBannedBeginAt() int64 {
	if m != nil {
		return m.BannedBeginAt
	}
	return 0
}

func (m *GetBannedCheckRecordReq) GetBannedEndAt() int64 {
	if m != nil {
		return m.BannedEndAt
	}
	return 0
}

func (m *GetBannedCheckRecordReq) GetCheckOperatorNameList() []string {
	if m != nil {
		return m.CheckOperatorNameList
	}
	return nil
}

func (m *GetBannedCheckRecordReq) GetCheckBeginAt() int64 {
	if m != nil {
		return m.CheckBeginAt
	}
	return 0
}

func (m *GetBannedCheckRecordReq) GetCheckEndAt() int64 {
	if m != nil {
		return m.CheckEndAt
	}
	return 0
}

type BannedCheckRecord struct {
	TaskId               uint32             `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	Uid                  uint32             `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Ttid                 string             `protobuf:"bytes,3,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Nickname             string             `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Phone                string             `protobuf:"bytes,5,opt,name=phone,proto3" json:"phone,omitempty"`
	IdentityNum          string             `protobuf:"bytes,6,opt,name=identity_num,json=identityNum,proto3" json:"identity_num,omitempty"`
	UserTag              BannedCheckUserTag `protobuf:"varint,7,opt,name=user_tag,json=userTag,proto3,enum=apicentergo.BannedCheckUserTag" json:"user_tag,omitempty"`
	Reason               string             `protobuf:"bytes,8,opt,name=reason,proto3" json:"reason,omitempty"`
	ReasonDetail         string             `protobuf:"bytes,9,opt,name=reason_detail,json=reasonDetail,proto3" json:"reason_detail,omitempty"`
	ProofPic             string             `protobuf:"bytes,10,opt,name=proof_pic,json=proofPic,proto3" json:"proof_pic,omitempty"`
	BannedDays           uint64             `protobuf:"varint,11,opt,name=banned_days,json=bannedDays,proto3" json:"banned_days,omitempty"`
	Source               BannedCheckSource  `protobuf:"varint,12,opt,name=source,proto3,enum=apicentergo.BannedCheckSource" json:"source,omitempty"`
	BannedAt             int64              `protobuf:"varint,13,opt,name=banned_at,json=bannedAt,proto3" json:"banned_at,omitempty"`
	OperatorName         string             `protobuf:"bytes,14,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	OpType               uint32             `protobuf:"varint,15,opt,name=op_type,json=opType,proto3" json:"op_type,omitempty"`
	CheckAt              int64              `protobuf:"varint,16,opt,name=check_at,json=checkAt,proto3" json:"check_at,omitempty"`
	CheckOperatorName    string             `protobuf:"bytes,17,opt,name=check_operator_name,json=checkOperatorName,proto3" json:"check_operator_name,omitempty"`
	RecoverReason        string             `protobuf:"bytes,18,opt,name=recover_reason,json=recoverReason,proto3" json:"recover_reason,omitempty"`
	CheckResult          string             `protobuf:"bytes,19,opt,name=check_result,json=checkResult,proto3" json:"check_result,omitempty"`
	CheckType            BannedCheckType    `protobuf:"varint,20,opt,name=check_type,json=checkType,proto3,enum=apicentergo.BannedCheckType" json:"check_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *BannedCheckRecord) Reset()         { *m = BannedCheckRecord{} }
func (m *BannedCheckRecord) String() string { return proto.CompactTextString(m) }
func (*BannedCheckRecord) ProtoMessage()    {}
func (*BannedCheckRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{27}
}
func (m *BannedCheckRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BannedCheckRecord.Unmarshal(m, b)
}
func (m *BannedCheckRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BannedCheckRecord.Marshal(b, m, deterministic)
}
func (dst *BannedCheckRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BannedCheckRecord.Merge(dst, src)
}
func (m *BannedCheckRecord) XXX_Size() int {
	return xxx_messageInfo_BannedCheckRecord.Size(m)
}
func (m *BannedCheckRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_BannedCheckRecord.DiscardUnknown(m)
}

var xxx_messageInfo_BannedCheckRecord proto.InternalMessageInfo

func (m *BannedCheckRecord) GetTaskId() uint32 {
	if m != nil {
		return m.TaskId
	}
	return 0
}

func (m *BannedCheckRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BannedCheckRecord) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *BannedCheckRecord) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *BannedCheckRecord) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *BannedCheckRecord) GetIdentityNum() string {
	if m != nil {
		return m.IdentityNum
	}
	return ""
}

func (m *BannedCheckRecord) GetUserTag() BannedCheckUserTag {
	if m != nil {
		return m.UserTag
	}
	return BannedCheckUserTag_BANNED_CHECK_USER_TAG_UNSPECIFIED
}

func (m *BannedCheckRecord) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *BannedCheckRecord) GetReasonDetail() string {
	if m != nil {
		return m.ReasonDetail
	}
	return ""
}

func (m *BannedCheckRecord) GetProofPic() string {
	if m != nil {
		return m.ProofPic
	}
	return ""
}

func (m *BannedCheckRecord) GetBannedDays() uint64 {
	if m != nil {
		return m.BannedDays
	}
	return 0
}

func (m *BannedCheckRecord) GetSource() BannedCheckSource {
	if m != nil {
		return m.Source
	}
	return BannedCheckSource_BANNED_CHECK_SOURCE_UNSPECIFIED
}

func (m *BannedCheckRecord) GetBannedAt() int64 {
	if m != nil {
		return m.BannedAt
	}
	return 0
}

func (m *BannedCheckRecord) GetOperatorName() string {
	if m != nil {
		return m.OperatorName
	}
	return ""
}

func (m *BannedCheckRecord) GetOpType() uint32 {
	if m != nil {
		return m.OpType
	}
	return 0
}

func (m *BannedCheckRecord) GetCheckAt() int64 {
	if m != nil {
		return m.CheckAt
	}
	return 0
}

func (m *BannedCheckRecord) GetCheckOperatorName() string {
	if m != nil {
		return m.CheckOperatorName
	}
	return ""
}

func (m *BannedCheckRecord) GetRecoverReason() string {
	if m != nil {
		return m.RecoverReason
	}
	return ""
}

func (m *BannedCheckRecord) GetCheckResult() string {
	if m != nil {
		return m.CheckResult
	}
	return ""
}

func (m *BannedCheckRecord) GetCheckType() BannedCheckType {
	if m != nil {
		return m.CheckType
	}
	return BannedCheckType_BANNED_CHECK_TYPE_UNSPECIFIED
}

type GetBannedCheckRecordResp struct {
	RecordList           []*BannedCheckRecord `protobuf:"bytes,1,rep,name=record_list,json=recordList,proto3" json:"record_list,omitempty"`
	TotalCnt             uint32               `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetBannedCheckRecordResp) Reset()         { *m = GetBannedCheckRecordResp{} }
func (m *GetBannedCheckRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetBannedCheckRecordResp) ProtoMessage()    {}
func (*GetBannedCheckRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{28}
}
func (m *GetBannedCheckRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBannedCheckRecordResp.Unmarshal(m, b)
}
func (m *GetBannedCheckRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBannedCheckRecordResp.Marshal(b, m, deterministic)
}
func (dst *GetBannedCheckRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBannedCheckRecordResp.Merge(dst, src)
}
func (m *GetBannedCheckRecordResp) XXX_Size() int {
	return xxx_messageInfo_GetBannedCheckRecordResp.Size(m)
}
func (m *GetBannedCheckRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBannedCheckRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBannedCheckRecordResp proto.InternalMessageInfo

func (m *GetBannedCheckRecordResp) GetRecordList() []*BannedCheckRecord {
	if m != nil {
		return m.RecordList
	}
	return nil
}

func (m *GetBannedCheckRecordResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

type UpdateBannedCheckRecordReq struct {
	UpdateType           UpdateBannedCheckRecordReq_UpdateType `protobuf:"varint,1,opt,name=update_type,json=updateType,proto3,enum=apicentergo.UpdateBannedCheckRecordReq_UpdateType" json:"update_type,omitempty"`
	TaskIdList           []uint32                              `protobuf:"varint,2,rep,packed,name=task_id_list,json=taskIdList,proto3" json:"task_id_list,omitempty"`
	OperatorName         string                                `protobuf:"bytes,3,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	Reason               string                                `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,omitempty"`
	IsRecoverDevice      bool                                  `protobuf:"varint,5,opt,name=is_recover_device,json=isRecoverDevice,proto3" json:"is_recover_device,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                              `json:"-"`
	XXX_unrecognized     []byte                                `json:"-"`
	XXX_sizecache        int32                                 `json:"-"`
}

func (m *UpdateBannedCheckRecordReq) Reset()         { *m = UpdateBannedCheckRecordReq{} }
func (m *UpdateBannedCheckRecordReq) String() string { return proto.CompactTextString(m) }
func (*UpdateBannedCheckRecordReq) ProtoMessage()    {}
func (*UpdateBannedCheckRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{29}
}
func (m *UpdateBannedCheckRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateBannedCheckRecordReq.Unmarshal(m, b)
}
func (m *UpdateBannedCheckRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateBannedCheckRecordReq.Marshal(b, m, deterministic)
}
func (dst *UpdateBannedCheckRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateBannedCheckRecordReq.Merge(dst, src)
}
func (m *UpdateBannedCheckRecordReq) XXX_Size() int {
	return xxx_messageInfo_UpdateBannedCheckRecordReq.Size(m)
}
func (m *UpdateBannedCheckRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateBannedCheckRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateBannedCheckRecordReq proto.InternalMessageInfo

func (m *UpdateBannedCheckRecordReq) GetUpdateType() UpdateBannedCheckRecordReq_UpdateType {
	if m != nil {
		return m.UpdateType
	}
	return UpdateBannedCheckRecordReq_UPDATE_TYPE_UNSPECIFIED
}

func (m *UpdateBannedCheckRecordReq) GetTaskIdList() []uint32 {
	if m != nil {
		return m.TaskIdList
	}
	return nil
}

func (m *UpdateBannedCheckRecordReq) GetOperatorName() string {
	if m != nil {
		return m.OperatorName
	}
	return ""
}

func (m *UpdateBannedCheckRecordReq) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *UpdateBannedCheckRecordReq) GetIsRecoverDevice() bool {
	if m != nil {
		return m.IsRecoverDevice
	}
	return false
}

type UpdateBannedCheckRecordResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateBannedCheckRecordResp) Reset()         { *m = UpdateBannedCheckRecordResp{} }
func (m *UpdateBannedCheckRecordResp) String() string { return proto.CompactTextString(m) }
func (*UpdateBannedCheckRecordResp) ProtoMessage()    {}
func (*UpdateBannedCheckRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{30}
}
func (m *UpdateBannedCheckRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateBannedCheckRecordResp.Unmarshal(m, b)
}
func (m *UpdateBannedCheckRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateBannedCheckRecordResp.Marshal(b, m, deterministic)
}
func (dst *UpdateBannedCheckRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateBannedCheckRecordResp.Merge(dst, src)
}
func (m *UpdateBannedCheckRecordResp) XXX_Size() int {
	return xxx_messageInfo_UpdateBannedCheckRecordResp.Size(m)
}
func (m *UpdateBannedCheckRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateBannedCheckRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateBannedCheckRecordResp proto.InternalMessageInfo

type GetBannedCheckOperatorReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBannedCheckOperatorReq) Reset()         { *m = GetBannedCheckOperatorReq{} }
func (m *GetBannedCheckOperatorReq) String() string { return proto.CompactTextString(m) }
func (*GetBannedCheckOperatorReq) ProtoMessage()    {}
func (*GetBannedCheckOperatorReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{31}
}
func (m *GetBannedCheckOperatorReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBannedCheckOperatorReq.Unmarshal(m, b)
}
func (m *GetBannedCheckOperatorReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBannedCheckOperatorReq.Marshal(b, m, deterministic)
}
func (dst *GetBannedCheckOperatorReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBannedCheckOperatorReq.Merge(dst, src)
}
func (m *GetBannedCheckOperatorReq) XXX_Size() int {
	return xxx_messageInfo_GetBannedCheckOperatorReq.Size(m)
}
func (m *GetBannedCheckOperatorReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBannedCheckOperatorReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBannedCheckOperatorReq proto.InternalMessageInfo

type GetBannedCheckOperatorResp struct {
	OperatorList         []*BannedOperator `protobuf:"bytes,1,rep,name=operator_list,json=operatorList,proto3" json:"operator_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetBannedCheckOperatorResp) Reset()         { *m = GetBannedCheckOperatorResp{} }
func (m *GetBannedCheckOperatorResp) String() string { return proto.CompactTextString(m) }
func (*GetBannedCheckOperatorResp) ProtoMessage()    {}
func (*GetBannedCheckOperatorResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{32}
}
func (m *GetBannedCheckOperatorResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBannedCheckOperatorResp.Unmarshal(m, b)
}
func (m *GetBannedCheckOperatorResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBannedCheckOperatorResp.Marshal(b, m, deterministic)
}
func (dst *GetBannedCheckOperatorResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBannedCheckOperatorResp.Merge(dst, src)
}
func (m *GetBannedCheckOperatorResp) XXX_Size() int {
	return xxx_messageInfo_GetBannedCheckOperatorResp.Size(m)
}
func (m *GetBannedCheckOperatorResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBannedCheckOperatorResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBannedCheckOperatorResp proto.InternalMessageInfo

func (m *GetBannedCheckOperatorResp) GetOperatorList() []*BannedOperator {
	if m != nil {
		return m.OperatorList
	}
	return nil
}

type UserLoginDevice struct {
	DeviceId             string   `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	LastLoginTs          uint32   `protobuf:"varint,2,opt,name=last_login_ts,json=lastLoginTs,proto3" json:"last_login_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserLoginDevice) Reset()         { *m = UserLoginDevice{} }
func (m *UserLoginDevice) String() string { return proto.CompactTextString(m) }
func (*UserLoginDevice) ProtoMessage()    {}
func (*UserLoginDevice) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{33}
}
func (m *UserLoginDevice) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserLoginDevice.Unmarshal(m, b)
}
func (m *UserLoginDevice) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserLoginDevice.Marshal(b, m, deterministic)
}
func (dst *UserLoginDevice) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserLoginDevice.Merge(dst, src)
}
func (m *UserLoginDevice) XXX_Size() int {
	return xxx_messageInfo_UserLoginDevice.Size(m)
}
func (m *UserLoginDevice) XXX_DiscardUnknown() {
	xxx_messageInfo_UserLoginDevice.DiscardUnknown(m)
}

var xxx_messageInfo_UserLoginDevice proto.InternalMessageInfo

func (m *UserLoginDevice) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *UserLoginDevice) GetLastLoginTs() uint32 {
	if m != nil {
		return m.LastLoginTs
	}
	return 0
}

// 获取用户登录设备信息
type GetUserLoginDeviceReq struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserLoginDeviceReq) Reset()         { *m = GetUserLoginDeviceReq{} }
func (m *GetUserLoginDeviceReq) String() string { return proto.CompactTextString(m) }
func (*GetUserLoginDeviceReq) ProtoMessage()    {}
func (*GetUserLoginDeviceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{34}
}
func (m *GetUserLoginDeviceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserLoginDeviceReq.Unmarshal(m, b)
}
func (m *GetUserLoginDeviceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserLoginDeviceReq.Marshal(b, m, deterministic)
}
func (dst *GetUserLoginDeviceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserLoginDeviceReq.Merge(dst, src)
}
func (m *GetUserLoginDeviceReq) XXX_Size() int {
	return xxx_messageInfo_GetUserLoginDeviceReq.Size(m)
}
func (m *GetUserLoginDeviceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserLoginDeviceReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserLoginDeviceReq proto.InternalMessageInfo

func (m *GetUserLoginDeviceReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

type GetUserLoginDeviceResp struct {
	InfoList             []*UserLoginDevice `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetUserLoginDeviceResp) Reset()         { *m = GetUserLoginDeviceResp{} }
func (m *GetUserLoginDeviceResp) String() string { return proto.CompactTextString(m) }
func (*GetUserLoginDeviceResp) ProtoMessage()    {}
func (*GetUserLoginDeviceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{35}
}
func (m *GetUserLoginDeviceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserLoginDeviceResp.Unmarshal(m, b)
}
func (m *GetUserLoginDeviceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserLoginDeviceResp.Marshal(b, m, deterministic)
}
func (dst *GetUserLoginDeviceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserLoginDeviceResp.Merge(dst, src)
}
func (m *GetUserLoginDeviceResp) XXX_Size() int {
	return xxx_messageInfo_GetUserLoginDeviceResp.Size(m)
}
func (m *GetUserLoginDeviceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserLoginDeviceResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserLoginDeviceResp proto.InternalMessageInfo

func (m *GetUserLoginDeviceResp) GetInfoList() []*UserLoginDevice {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type UserLoginWithDevice struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	LastLoginTs          uint32   `protobuf:"varint,2,opt,name=last_login_ts,json=lastLoginTs,proto3" json:"last_login_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserLoginWithDevice) Reset()         { *m = UserLoginWithDevice{} }
func (m *UserLoginWithDevice) String() string { return proto.CompactTextString(m) }
func (*UserLoginWithDevice) ProtoMessage()    {}
func (*UserLoginWithDevice) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{36}
}
func (m *UserLoginWithDevice) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserLoginWithDevice.Unmarshal(m, b)
}
func (m *UserLoginWithDevice) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserLoginWithDevice.Marshal(b, m, deterministic)
}
func (dst *UserLoginWithDevice) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserLoginWithDevice.Merge(dst, src)
}
func (m *UserLoginWithDevice) XXX_Size() int {
	return xxx_messageInfo_UserLoginWithDevice.Size(m)
}
func (m *UserLoginWithDevice) XXX_DiscardUnknown() {
	xxx_messageInfo_UserLoginWithDevice.DiscardUnknown(m)
}

var xxx_messageInfo_UserLoginWithDevice proto.InternalMessageInfo

func (m *UserLoginWithDevice) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserLoginWithDevice) GetLastLoginTs() uint32 {
	if m != nil {
		return m.LastLoginTs
	}
	return 0
}

// 查到登录过设备的账号
type GetUserLoginWithDeviceReq struct {
	DeviceId             string   `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserLoginWithDeviceReq) Reset()         { *m = GetUserLoginWithDeviceReq{} }
func (m *GetUserLoginWithDeviceReq) String() string { return proto.CompactTextString(m) }
func (*GetUserLoginWithDeviceReq) ProtoMessage()    {}
func (*GetUserLoginWithDeviceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{37}
}
func (m *GetUserLoginWithDeviceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserLoginWithDeviceReq.Unmarshal(m, b)
}
func (m *GetUserLoginWithDeviceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserLoginWithDeviceReq.Marshal(b, m, deterministic)
}
func (dst *GetUserLoginWithDeviceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserLoginWithDeviceReq.Merge(dst, src)
}
func (m *GetUserLoginWithDeviceReq) XXX_Size() int {
	return xxx_messageInfo_GetUserLoginWithDeviceReq.Size(m)
}
func (m *GetUserLoginWithDeviceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserLoginWithDeviceReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserLoginWithDeviceReq proto.InternalMessageInfo

func (m *GetUserLoginWithDeviceReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

type GetUserLoginWithDeviceResp struct {
	InfoList             []*UserLoginWithDevice `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetUserLoginWithDeviceResp) Reset()         { *m = GetUserLoginWithDeviceResp{} }
func (m *GetUserLoginWithDeviceResp) String() string { return proto.CompactTextString(m) }
func (*GetUserLoginWithDeviceResp) ProtoMessage()    {}
func (*GetUserLoginWithDeviceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{38}
}
func (m *GetUserLoginWithDeviceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserLoginWithDeviceResp.Unmarshal(m, b)
}
func (m *GetUserLoginWithDeviceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserLoginWithDeviceResp.Marshal(b, m, deterministic)
}
func (dst *GetUserLoginWithDeviceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserLoginWithDeviceResp.Merge(dst, src)
}
func (m *GetUserLoginWithDeviceResp) XXX_Size() int {
	return xxx_messageInfo_GetUserLoginWithDeviceResp.Size(m)
}
func (m *GetUserLoginWithDeviceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserLoginWithDeviceResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserLoginWithDeviceResp proto.InternalMessageInfo

func (m *GetUserLoginWithDeviceResp) GetInfoList() []*UserLoginWithDevice {
	if m != nil {
		return m.InfoList
	}
	return nil
}

// 封禁账号
type BanUserReq struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Reason               string   `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	OperatorName         string   `protobuf:"bytes,3,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	AutoRecoveryAt       int64    `protobuf:"varint,4,opt,name=auto_recovery_at,json=autoRecoveryAt,proto3" json:"auto_recovery_at,omitempty"`
	WithDevice           bool     `protobuf:"varint,5,opt,name=with_device,json=withDevice,proto3" json:"with_device,omitempty"`
	ProofPic             string   `protobuf:"bytes,6,opt,name=proof_pic,json=proofPic,proto3" json:"proof_pic,omitempty"`
	ReasonDetail         string   `protobuf:"bytes,7,opt,name=reason_detail,json=reasonDetail,proto3" json:"reason_detail,omitempty"`
	BannedDays           uint64   `protobuf:"varint,8,opt,name=banned_days,json=bannedDays,proto3" json:"banned_days,omitempty"`
	RetRespErr           bool     `protobuf:"varint,9,opt,name=ret_resp_err,json=retRespErr,proto3" json:"ret_resp_err,omitempty"`
	Uid                  uint32   `protobuf:"varint,10,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BanUserReq) Reset()         { *m = BanUserReq{} }
func (m *BanUserReq) String() string { return proto.CompactTextString(m) }
func (*BanUserReq) ProtoMessage()    {}
func (*BanUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{39}
}
func (m *BanUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BanUserReq.Unmarshal(m, b)
}
func (m *BanUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BanUserReq.Marshal(b, m, deterministic)
}
func (dst *BanUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BanUserReq.Merge(dst, src)
}
func (m *BanUserReq) XXX_Size() int {
	return xxx_messageInfo_BanUserReq.Size(m)
}
func (m *BanUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BanUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_BanUserReq proto.InternalMessageInfo

func (m *BanUserReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *BanUserReq) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *BanUserReq) GetOperatorName() string {
	if m != nil {
		return m.OperatorName
	}
	return ""
}

func (m *BanUserReq) GetAutoRecoveryAt() int64 {
	if m != nil {
		return m.AutoRecoveryAt
	}
	return 0
}

func (m *BanUserReq) GetWithDevice() bool {
	if m != nil {
		return m.WithDevice
	}
	return false
}

func (m *BanUserReq) GetProofPic() string {
	if m != nil {
		return m.ProofPic
	}
	return ""
}

func (m *BanUserReq) GetReasonDetail() string {
	if m != nil {
		return m.ReasonDetail
	}
	return ""
}

func (m *BanUserReq) GetBannedDays() uint64 {
	if m != nil {
		return m.BannedDays
	}
	return 0
}

func (m *BanUserReq) GetRetRespErr() bool {
	if m != nil {
		return m.RetRespErr
	}
	return false
}

func (m *BanUserReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type BanUserResp struct {
	ErrCode              int32    `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	ErrMsg               string   `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BanUserResp) Reset()         { *m = BanUserResp{} }
func (m *BanUserResp) String() string { return proto.CompactTextString(m) }
func (*BanUserResp) ProtoMessage()    {}
func (*BanUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{40}
}
func (m *BanUserResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BanUserResp.Unmarshal(m, b)
}
func (m *BanUserResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BanUserResp.Marshal(b, m, deterministic)
}
func (dst *BanUserResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BanUserResp.Merge(dst, src)
}
func (m *BanUserResp) XXX_Size() int {
	return xxx_messageInfo_BanUserResp.Size(m)
}
func (m *BanUserResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BanUserResp.DiscardUnknown(m)
}

var xxx_messageInfo_BanUserResp proto.InternalMessageInfo

func (m *BanUserResp) GetErrCode() int32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *BanUserResp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

type CanRecoverDeviceReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CanRecoverDeviceReq) Reset()         { *m = CanRecoverDeviceReq{} }
func (m *CanRecoverDeviceReq) String() string { return proto.CompactTextString(m) }
func (*CanRecoverDeviceReq) ProtoMessage()    {}
func (*CanRecoverDeviceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{41}
}
func (m *CanRecoverDeviceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CanRecoverDeviceReq.Unmarshal(m, b)
}
func (m *CanRecoverDeviceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CanRecoverDeviceReq.Marshal(b, m, deterministic)
}
func (dst *CanRecoverDeviceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CanRecoverDeviceReq.Merge(dst, src)
}
func (m *CanRecoverDeviceReq) XXX_Size() int {
	return xxx_messageInfo_CanRecoverDeviceReq.Size(m)
}
func (m *CanRecoverDeviceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CanRecoverDeviceReq.DiscardUnknown(m)
}

var xxx_messageInfo_CanRecoverDeviceReq proto.InternalMessageInfo

func (m *CanRecoverDeviceReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CanRecoverDeviceResp struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CanRecover           bool     `protobuf:"varint,2,opt,name=can_recover,json=canRecover,proto3" json:"can_recover,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CanRecoverDeviceResp) Reset()         { *m = CanRecoverDeviceResp{} }
func (m *CanRecoverDeviceResp) String() string { return proto.CompactTextString(m) }
func (*CanRecoverDeviceResp) ProtoMessage()    {}
func (*CanRecoverDeviceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{42}
}
func (m *CanRecoverDeviceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CanRecoverDeviceResp.Unmarshal(m, b)
}
func (m *CanRecoverDeviceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CanRecoverDeviceResp.Marshal(b, m, deterministic)
}
func (dst *CanRecoverDeviceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CanRecoverDeviceResp.Merge(dst, src)
}
func (m *CanRecoverDeviceResp) XXX_Size() int {
	return xxx_messageInfo_CanRecoverDeviceResp.Size(m)
}
func (m *CanRecoverDeviceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CanRecoverDeviceResp.DiscardUnknown(m)
}

var xxx_messageInfo_CanRecoverDeviceResp proto.InternalMessageInfo

func (m *CanRecoverDeviceResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CanRecoverDeviceResp) GetCanRecover() bool {
	if m != nil {
		return m.CanRecover
	}
	return false
}

// 恢复用户
type RecoverUserReq struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Reason               string   `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	OperatorName         string   `protobuf:"bytes,3,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	IsRecoverDevice      bool     `protobuf:"varint,4,opt,name=is_recover_device,json=isRecoverDevice,proto3" json:"is_recover_device,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecoverUserReq) Reset()         { *m = RecoverUserReq{} }
func (m *RecoverUserReq) String() string { return proto.CompactTextString(m) }
func (*RecoverUserReq) ProtoMessage()    {}
func (*RecoverUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{43}
}
func (m *RecoverUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecoverUserReq.Unmarshal(m, b)
}
func (m *RecoverUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecoverUserReq.Marshal(b, m, deterministic)
}
func (dst *RecoverUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecoverUserReq.Merge(dst, src)
}
func (m *RecoverUserReq) XXX_Size() int {
	return xxx_messageInfo_RecoverUserReq.Size(m)
}
func (m *RecoverUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RecoverUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_RecoverUserReq proto.InternalMessageInfo

func (m *RecoverUserReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *RecoverUserReq) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *RecoverUserReq) GetOperatorName() string {
	if m != nil {
		return m.OperatorName
	}
	return ""
}

func (m *RecoverUserReq) GetIsRecoverDevice() bool {
	if m != nil {
		return m.IsRecoverDevice
	}
	return false
}

type RecoverUserResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecoverUserResp) Reset()         { *m = RecoverUserResp{} }
func (m *RecoverUserResp) String() string { return proto.CompactTextString(m) }
func (*RecoverUserResp) ProtoMessage()    {}
func (*RecoverUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{44}
}
func (m *RecoverUserResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecoverUserResp.Unmarshal(m, b)
}
func (m *RecoverUserResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecoverUserResp.Marshal(b, m, deterministic)
}
func (dst *RecoverUserResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecoverUserResp.Merge(dst, src)
}
func (m *RecoverUserResp) XXX_Size() int {
	return xxx_messageInfo_RecoverUserResp.Size(m)
}
func (m *RecoverUserResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RecoverUserResp.DiscardUnknown(m)
}

var xxx_messageInfo_RecoverUserResp proto.InternalMessageInfo

type RecoverUserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Reason               string   `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	IsRecoverDevice      bool     `protobuf:"varint,4,opt,name=is_recover_device,json=isRecoverDevice,proto3" json:"is_recover_device,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecoverUserInfo) Reset()         { *m = RecoverUserInfo{} }
func (m *RecoverUserInfo) String() string { return proto.CompactTextString(m) }
func (*RecoverUserInfo) ProtoMessage()    {}
func (*RecoverUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{45}
}
func (m *RecoverUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecoverUserInfo.Unmarshal(m, b)
}
func (m *RecoverUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecoverUserInfo.Marshal(b, m, deterministic)
}
func (dst *RecoverUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecoverUserInfo.Merge(dst, src)
}
func (m *RecoverUserInfo) XXX_Size() int {
	return xxx_messageInfo_RecoverUserInfo.Size(m)
}
func (m *RecoverUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RecoverUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RecoverUserInfo proto.InternalMessageInfo

func (m *RecoverUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RecoverUserInfo) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *RecoverUserInfo) GetIsRecoverDevice() bool {
	if m != nil {
		return m.IsRecoverDevice
	}
	return false
}

type BatchRecoverUserReq struct {
	List                 []*RecoverUserInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	OperatorName         string             `protobuf:"bytes,2,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	IsCheck              bool               `protobuf:"varint,3,opt,name=is_check,json=isCheck,proto3" json:"is_check,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *BatchRecoverUserReq) Reset()         { *m = BatchRecoverUserReq{} }
func (m *BatchRecoverUserReq) String() string { return proto.CompactTextString(m) }
func (*BatchRecoverUserReq) ProtoMessage()    {}
func (*BatchRecoverUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{46}
}
func (m *BatchRecoverUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchRecoverUserReq.Unmarshal(m, b)
}
func (m *BatchRecoverUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchRecoverUserReq.Marshal(b, m, deterministic)
}
func (dst *BatchRecoverUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchRecoverUserReq.Merge(dst, src)
}
func (m *BatchRecoverUserReq) XXX_Size() int {
	return xxx_messageInfo_BatchRecoverUserReq.Size(m)
}
func (m *BatchRecoverUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchRecoverUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchRecoverUserReq proto.InternalMessageInfo

func (m *BatchRecoverUserReq) GetList() []*RecoverUserInfo {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *BatchRecoverUserReq) GetOperatorName() string {
	if m != nil {
		return m.OperatorName
	}
	return ""
}

func (m *BatchRecoverUserReq) GetIsCheck() bool {
	if m != nil {
		return m.IsCheck
	}
	return false
}

type BatchRecoverUserResp struct {
	ValidNum             uint32   `protobuf:"varint,1,opt,name=valid_num,json=validNum,proto3" json:"valid_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchRecoverUserResp) Reset()         { *m = BatchRecoverUserResp{} }
func (m *BatchRecoverUserResp) String() string { return proto.CompactTextString(m) }
func (*BatchRecoverUserResp) ProtoMessage()    {}
func (*BatchRecoverUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{47}
}
func (m *BatchRecoverUserResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchRecoverUserResp.Unmarshal(m, b)
}
func (m *BatchRecoverUserResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchRecoverUserResp.Marshal(b, m, deterministic)
}
func (dst *BatchRecoverUserResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchRecoverUserResp.Merge(dst, src)
}
func (m *BatchRecoverUserResp) XXX_Size() int {
	return xxx_messageInfo_BatchRecoverUserResp.Size(m)
}
func (m *BatchRecoverUserResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchRecoverUserResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchRecoverUserResp proto.InternalMessageInfo

func (m *BatchRecoverUserResp) GetValidNum() uint32 {
	if m != nil {
		return m.ValidNum
	}
	return 0
}

// 获取用户邀请记录
type GetUserInviteHistoryReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Cnt                  uint32   `protobuf:"varint,2,opt,name=cnt,proto3" json:"cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserInviteHistoryReq) Reset()         { *m = GetUserInviteHistoryReq{} }
func (m *GetUserInviteHistoryReq) String() string { return proto.CompactTextString(m) }
func (*GetUserInviteHistoryReq) ProtoMessage()    {}
func (*GetUserInviteHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{48}
}
func (m *GetUserInviteHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserInviteHistoryReq.Unmarshal(m, b)
}
func (m *GetUserInviteHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserInviteHistoryReq.Marshal(b, m, deterministic)
}
func (dst *GetUserInviteHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserInviteHistoryReq.Merge(dst, src)
}
func (m *GetUserInviteHistoryReq) XXX_Size() int {
	return xxx_messageInfo_GetUserInviteHistoryReq.Size(m)
}
func (m *GetUserInviteHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserInviteHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserInviteHistoryReq proto.InternalMessageInfo

func (m *GetUserInviteHistoryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserInviteHistoryReq) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

type GetUserInviteHistoryResp struct {
	InviteList           []string `protobuf:"bytes,1,rep,name=invite_list,json=inviteList,proto3" json:"invite_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserInviteHistoryResp) Reset()         { *m = GetUserInviteHistoryResp{} }
func (m *GetUserInviteHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetUserInviteHistoryResp) ProtoMessage()    {}
func (*GetUserInviteHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{49}
}
func (m *GetUserInviteHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserInviteHistoryResp.Unmarshal(m, b)
}
func (m *GetUserInviteHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserInviteHistoryResp.Marshal(b, m, deterministic)
}
func (dst *GetUserInviteHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserInviteHistoryResp.Merge(dst, src)
}
func (m *GetUserInviteHistoryResp) XXX_Size() int {
	return xxx_messageInfo_GetUserInviteHistoryResp.Size(m)
}
func (m *GetUserInviteHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserInviteHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserInviteHistoryResp proto.InternalMessageInfo

func (m *GetUserInviteHistoryResp) GetInviteList() []string {
	if m != nil {
		return m.InviteList
	}
	return nil
}

// 重置用户密码
type ResetUserPasswordReq struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Phone                string   `protobuf:"bytes,2,opt,name=phone,proto3" json:"phone,omitempty"`
	OperatorName         string   `protobuf:"bytes,3,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ResetUserPasswordReq) Reset()         { *m = ResetUserPasswordReq{} }
func (m *ResetUserPasswordReq) String() string { return proto.CompactTextString(m) }
func (*ResetUserPasswordReq) ProtoMessage()    {}
func (*ResetUserPasswordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{50}
}
func (m *ResetUserPasswordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ResetUserPasswordReq.Unmarshal(m, b)
}
func (m *ResetUserPasswordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ResetUserPasswordReq.Marshal(b, m, deterministic)
}
func (dst *ResetUserPasswordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResetUserPasswordReq.Merge(dst, src)
}
func (m *ResetUserPasswordReq) XXX_Size() int {
	return xxx_messageInfo_ResetUserPasswordReq.Size(m)
}
func (m *ResetUserPasswordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ResetUserPasswordReq.DiscardUnknown(m)
}

var xxx_messageInfo_ResetUserPasswordReq proto.InternalMessageInfo

func (m *ResetUserPasswordReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ResetUserPasswordReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *ResetUserPasswordReq) GetOperatorName() string {
	if m != nil {
		return m.OperatorName
	}
	return ""
}

type ResetUserPasswordResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ResetUserPasswordResp) Reset()         { *m = ResetUserPasswordResp{} }
func (m *ResetUserPasswordResp) String() string { return proto.CompactTextString(m) }
func (*ResetUserPasswordResp) ProtoMessage()    {}
func (*ResetUserPasswordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{51}
}
func (m *ResetUserPasswordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ResetUserPasswordResp.Unmarshal(m, b)
}
func (m *ResetUserPasswordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ResetUserPasswordResp.Marshal(b, m, deterministic)
}
func (dst *ResetUserPasswordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResetUserPasswordResp.Merge(dst, src)
}
func (m *ResetUserPasswordResp) XXX_Size() int {
	return xxx_messageInfo_ResetUserPasswordResp.Size(m)
}
func (m *ResetUserPasswordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ResetUserPasswordResp.DiscardUnknown(m)
}

var xxx_messageInfo_ResetUserPasswordResp proto.InternalMessageInfo

// 解绑用户手机
type UnbindUserPhoneReq struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	OperatorName         string   `protobuf:"bytes,2,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnbindUserPhoneReq) Reset()         { *m = UnbindUserPhoneReq{} }
func (m *UnbindUserPhoneReq) String() string { return proto.CompactTextString(m) }
func (*UnbindUserPhoneReq) ProtoMessage()    {}
func (*UnbindUserPhoneReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{52}
}
func (m *UnbindUserPhoneReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnbindUserPhoneReq.Unmarshal(m, b)
}
func (m *UnbindUserPhoneReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnbindUserPhoneReq.Marshal(b, m, deterministic)
}
func (dst *UnbindUserPhoneReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnbindUserPhoneReq.Merge(dst, src)
}
func (m *UnbindUserPhoneReq) XXX_Size() int {
	return xxx_messageInfo_UnbindUserPhoneReq.Size(m)
}
func (m *UnbindUserPhoneReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UnbindUserPhoneReq.DiscardUnknown(m)
}

var xxx_messageInfo_UnbindUserPhoneReq proto.InternalMessageInfo

func (m *UnbindUserPhoneReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UnbindUserPhoneReq) GetOperatorName() string {
	if m != nil {
		return m.OperatorName
	}
	return ""
}

type UnbindUserPhoneResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnbindUserPhoneResp) Reset()         { *m = UnbindUserPhoneResp{} }
func (m *UnbindUserPhoneResp) String() string { return proto.CompactTextString(m) }
func (*UnbindUserPhoneResp) ProtoMessage()    {}
func (*UnbindUserPhoneResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{53}
}
func (m *UnbindUserPhoneResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnbindUserPhoneResp.Unmarshal(m, b)
}
func (m *UnbindUserPhoneResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnbindUserPhoneResp.Marshal(b, m, deterministic)
}
func (dst *UnbindUserPhoneResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnbindUserPhoneResp.Merge(dst, src)
}
func (m *UnbindUserPhoneResp) XXX_Size() int {
	return xxx_messageInfo_UnbindUserPhoneResp.Size(m)
}
func (m *UnbindUserPhoneResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UnbindUserPhoneResp.DiscardUnknown(m)
}

var xxx_messageInfo_UnbindUserPhoneResp proto.InternalMessageInfo

// 重置密保问题
type ClearSecurityQuestionReq struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	OperatorName         string   `protobuf:"bytes,2,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearSecurityQuestionReq) Reset()         { *m = ClearSecurityQuestionReq{} }
func (m *ClearSecurityQuestionReq) String() string { return proto.CompactTextString(m) }
func (*ClearSecurityQuestionReq) ProtoMessage()    {}
func (*ClearSecurityQuestionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{54}
}
func (m *ClearSecurityQuestionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearSecurityQuestionReq.Unmarshal(m, b)
}
func (m *ClearSecurityQuestionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearSecurityQuestionReq.Marshal(b, m, deterministic)
}
func (dst *ClearSecurityQuestionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearSecurityQuestionReq.Merge(dst, src)
}
func (m *ClearSecurityQuestionReq) XXX_Size() int {
	return xxx_messageInfo_ClearSecurityQuestionReq.Size(m)
}
func (m *ClearSecurityQuestionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearSecurityQuestionReq.DiscardUnknown(m)
}

var xxx_messageInfo_ClearSecurityQuestionReq proto.InternalMessageInfo

func (m *ClearSecurityQuestionReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ClearSecurityQuestionReq) GetOperatorName() string {
	if m != nil {
		return m.OperatorName
	}
	return ""
}

type ClearSecurityQuestionResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearSecurityQuestionResp) Reset()         { *m = ClearSecurityQuestionResp{} }
func (m *ClearSecurityQuestionResp) String() string { return proto.CompactTextString(m) }
func (*ClearSecurityQuestionResp) ProtoMessage()    {}
func (*ClearSecurityQuestionResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{55}
}
func (m *ClearSecurityQuestionResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearSecurityQuestionResp.Unmarshal(m, b)
}
func (m *ClearSecurityQuestionResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearSecurityQuestionResp.Marshal(b, m, deterministic)
}
func (dst *ClearSecurityQuestionResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearSecurityQuestionResp.Merge(dst, src)
}
func (m *ClearSecurityQuestionResp) XXX_Size() int {
	return xxx_messageInfo_ClearSecurityQuestionResp.Size(m)
}
func (m *ClearSecurityQuestionResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearSecurityQuestionResp.DiscardUnknown(m)
}

var xxx_messageInfo_ClearSecurityQuestionResp proto.InternalMessageInfo

// 解绑三方账户
type DetachThirdpartReq struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	OperatorName         string   `protobuf:"bytes,2,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DetachThirdpartReq) Reset()         { *m = DetachThirdpartReq{} }
func (m *DetachThirdpartReq) String() string { return proto.CompactTextString(m) }
func (*DetachThirdpartReq) ProtoMessage()    {}
func (*DetachThirdpartReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{56}
}
func (m *DetachThirdpartReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DetachThirdpartReq.Unmarshal(m, b)
}
func (m *DetachThirdpartReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DetachThirdpartReq.Marshal(b, m, deterministic)
}
func (dst *DetachThirdpartReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DetachThirdpartReq.Merge(dst, src)
}
func (m *DetachThirdpartReq) XXX_Size() int {
	return xxx_messageInfo_DetachThirdpartReq.Size(m)
}
func (m *DetachThirdpartReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DetachThirdpartReq.DiscardUnknown(m)
}

var xxx_messageInfo_DetachThirdpartReq proto.InternalMessageInfo

func (m *DetachThirdpartReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *DetachThirdpartReq) GetOperatorName() string {
	if m != nil {
		return m.OperatorName
	}
	return ""
}

type DetachThirdpartResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DetachThirdpartResp) Reset()         { *m = DetachThirdpartResp{} }
func (m *DetachThirdpartResp) String() string { return proto.CompactTextString(m) }
func (*DetachThirdpartResp) ProtoMessage()    {}
func (*DetachThirdpartResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{57}
}
func (m *DetachThirdpartResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DetachThirdpartResp.Unmarshal(m, b)
}
func (m *DetachThirdpartResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DetachThirdpartResp.Marshal(b, m, deterministic)
}
func (dst *DetachThirdpartResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DetachThirdpartResp.Merge(dst, src)
}
func (m *DetachThirdpartResp) XXX_Size() int {
	return xxx_messageInfo_DetachThirdpartResp.Size(m)
}
func (m *DetachThirdpartResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DetachThirdpartResp.DiscardUnknown(m)
}

var xxx_messageInfo_DetachThirdpartResp proto.InternalMessageInfo

// 大V认证
type OfficialCertInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Intro                string   `protobuf:"bytes,3,opt,name=intro,proto3" json:"intro,omitempty"`
	Style                string   `protobuf:"bytes,4,opt,name=style,proto3" json:"style,omitempty"`
	Id                   uint32   `protobuf:"varint,5,opt,name=id,proto3" json:"id,omitempty"`
	BeginTs              uint64   `protobuf:"varint,6,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint64   `protobuf:"varint,7,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	IsUse                bool     `protobuf:"varint,8,opt,name=is_use,json=isUse,proto3" json:"is_use,omitempty"`
	Attribute            uint32   `protobuf:"varint,9,opt,name=attribute,proto3" json:"attribute,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OfficialCertInfo) Reset()         { *m = OfficialCertInfo{} }
func (m *OfficialCertInfo) String() string { return proto.CompactTextString(m) }
func (*OfficialCertInfo) ProtoMessage()    {}
func (*OfficialCertInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{58}
}
func (m *OfficialCertInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfficialCertInfo.Unmarshal(m, b)
}
func (m *OfficialCertInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfficialCertInfo.Marshal(b, m, deterministic)
}
func (dst *OfficialCertInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfficialCertInfo.Merge(dst, src)
}
func (m *OfficialCertInfo) XXX_Size() int {
	return xxx_messageInfo_OfficialCertInfo.Size(m)
}
func (m *OfficialCertInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OfficialCertInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OfficialCertInfo proto.InternalMessageInfo

func (m *OfficialCertInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *OfficialCertInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *OfficialCertInfo) GetIntro() string {
	if m != nil {
		return m.Intro
	}
	return ""
}

func (m *OfficialCertInfo) GetStyle() string {
	if m != nil {
		return m.Style
	}
	return ""
}

func (m *OfficialCertInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *OfficialCertInfo) GetBeginTs() uint64 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *OfficialCertInfo) GetEndTs() uint64 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *OfficialCertInfo) GetIsUse() bool {
	if m != nil {
		return m.IsUse
	}
	return false
}

func (m *OfficialCertInfo) GetAttribute() uint32 {
	if m != nil {
		return m.Attribute
	}
	return 0
}

type UserOfficialCertInfo struct {
	IsCertified          bool              `protobuf:"varint,1,opt,name=is_certified,json=isCertified,proto3" json:"is_certified,omitempty"`
	Cert                 *OfficialCertInfo `protobuf:"bytes,2,opt,name=cert,proto3" json:"cert,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UserOfficialCertInfo) Reset()         { *m = UserOfficialCertInfo{} }
func (m *UserOfficialCertInfo) String() string { return proto.CompactTextString(m) }
func (*UserOfficialCertInfo) ProtoMessage()    {}
func (*UserOfficialCertInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{59}
}
func (m *UserOfficialCertInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserOfficialCertInfo.Unmarshal(m, b)
}
func (m *UserOfficialCertInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserOfficialCertInfo.Marshal(b, m, deterministic)
}
func (dst *UserOfficialCertInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserOfficialCertInfo.Merge(dst, src)
}
func (m *UserOfficialCertInfo) XXX_Size() int {
	return xxx_messageInfo_UserOfficialCertInfo.Size(m)
}
func (m *UserOfficialCertInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserOfficialCertInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserOfficialCertInfo proto.InternalMessageInfo

func (m *UserOfficialCertInfo) GetIsCertified() bool {
	if m != nil {
		return m.IsCertified
	}
	return false
}

func (m *UserOfficialCertInfo) GetCert() *OfficialCertInfo {
	if m != nil {
		return m.Cert
	}
	return nil
}

// 批量获取用户大V认证信息
type BatchGetUserOfficialCertReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetUserOfficialCertReq) Reset()         { *m = BatchGetUserOfficialCertReq{} }
func (m *BatchGetUserOfficialCertReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserOfficialCertReq) ProtoMessage()    {}
func (*BatchGetUserOfficialCertReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{60}
}
func (m *BatchGetUserOfficialCertReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserOfficialCertReq.Unmarshal(m, b)
}
func (m *BatchGetUserOfficialCertReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserOfficialCertReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserOfficialCertReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserOfficialCertReq.Merge(dst, src)
}
func (m *BatchGetUserOfficialCertReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserOfficialCertReq.Size(m)
}
func (m *BatchGetUserOfficialCertReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserOfficialCertReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserOfficialCertReq proto.InternalMessageInfo

func (m *BatchGetUserOfficialCertReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchGetUserOfficialCertResp struct {
	InfoList             []*UserOfficialCertInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *BatchGetUserOfficialCertResp) Reset()         { *m = BatchGetUserOfficialCertResp{} }
func (m *BatchGetUserOfficialCertResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserOfficialCertResp) ProtoMessage()    {}
func (*BatchGetUserOfficialCertResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{61}
}
func (m *BatchGetUserOfficialCertResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserOfficialCertResp.Unmarshal(m, b)
}
func (m *BatchGetUserOfficialCertResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserOfficialCertResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserOfficialCertResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserOfficialCertResp.Merge(dst, src)
}
func (m *BatchGetUserOfficialCertResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserOfficialCertResp.Size(m)
}
func (m *BatchGetUserOfficialCertResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserOfficialCertResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserOfficialCertResp proto.InternalMessageInfo

func (m *BatchGetUserOfficialCertResp) GetInfoList() []*UserOfficialCertInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

// 获取用户经验信息
type GetUserExpReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserExpReq) Reset()         { *m = GetUserExpReq{} }
func (m *GetUserExpReq) String() string { return proto.CompactTextString(m) }
func (*GetUserExpReq) ProtoMessage()    {}
func (*GetUserExpReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{62}
}
func (m *GetUserExpReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserExpReq.Unmarshal(m, b)
}
func (m *GetUserExpReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserExpReq.Marshal(b, m, deterministic)
}
func (dst *GetUserExpReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserExpReq.Merge(dst, src)
}
func (m *GetUserExpReq) XXX_Size() int {
	return xxx_messageInfo_GetUserExpReq.Size(m)
}
func (m *GetUserExpReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserExpReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserExpReq proto.InternalMessageInfo

func (m *GetUserExpReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserExpResp struct {
	Exp                  int32    `protobuf:"varint,1,opt,name=exp,proto3" json:"exp,omitempty"`
	Level                uint32   `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserExpResp) Reset()         { *m = GetUserExpResp{} }
func (m *GetUserExpResp) String() string { return proto.CompactTextString(m) }
func (*GetUserExpResp) ProtoMessage()    {}
func (*GetUserExpResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{63}
}
func (m *GetUserExpResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserExpResp.Unmarshal(m, b)
}
func (m *GetUserExpResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserExpResp.Marshal(b, m, deterministic)
}
func (dst *GetUserExpResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserExpResp.Merge(dst, src)
}
func (m *GetUserExpResp) XXX_Size() int {
	return xxx_messageInfo_GetUserExpResp.Size(m)
}
func (m *GetUserExpResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserExpResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserExpResp proto.InternalMessageInfo

func (m *GetUserExpResp) GetExp() int32 {
	if m != nil {
		return m.Exp
	}
	return 0
}

func (m *GetUserExpResp) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

// 用户受控状态信息
type UserControlInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Ttid                 string   `protobuf:"bytes,2,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	RegisterTs           uint32   `protobuf:"varint,4,opt,name=register_ts,json=registerTs,proto3" json:"register_ts,omitempty"`
	RichLvName           string   `protobuf:"bytes,5,opt,name=rich_lv_name,json=richLvName,proto3" json:"rich_lv_name,omitempty"`
	IsControlled         bool     `protobuf:"varint,6,opt,name=is_controlled,json=isControlled,proto3" json:"is_controlled,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserControlInfo) Reset()         { *m = UserControlInfo{} }
func (m *UserControlInfo) String() string { return proto.CompactTextString(m) }
func (*UserControlInfo) ProtoMessage()    {}
func (*UserControlInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{64}
}
func (m *UserControlInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserControlInfo.Unmarshal(m, b)
}
func (m *UserControlInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserControlInfo.Marshal(b, m, deterministic)
}
func (dst *UserControlInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserControlInfo.Merge(dst, src)
}
func (m *UserControlInfo) XXX_Size() int {
	return xxx_messageInfo_UserControlInfo.Size(m)
}
func (m *UserControlInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserControlInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserControlInfo proto.InternalMessageInfo

func (m *UserControlInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserControlInfo) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *UserControlInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserControlInfo) GetRegisterTs() uint32 {
	if m != nil {
		return m.RegisterTs
	}
	return 0
}

func (m *UserControlInfo) GetRichLvName() string {
	if m != nil {
		return m.RichLvName
	}
	return ""
}

func (m *UserControlInfo) GetIsControlled() bool {
	if m != nil {
		return m.IsControlled
	}
	return false
}

// 获取用户受控信息
type GetUserControlInfoReq struct {
	QueryType            uint32   `protobuf:"varint,1,opt,name=query_type,json=queryType,proto3" json:"query_type,omitempty"`
	UidList              []uint32 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	Page                 uint32   `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserControlInfoReq) Reset()         { *m = GetUserControlInfoReq{} }
func (m *GetUserControlInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserControlInfoReq) ProtoMessage()    {}
func (*GetUserControlInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{65}
}
func (m *GetUserControlInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserControlInfoReq.Unmarshal(m, b)
}
func (m *GetUserControlInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserControlInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserControlInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserControlInfoReq.Merge(dst, src)
}
func (m *GetUserControlInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserControlInfoReq.Size(m)
}
func (m *GetUserControlInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserControlInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserControlInfoReq proto.InternalMessageInfo

func (m *GetUserControlInfoReq) GetQueryType() uint32 {
	if m != nil {
		return m.QueryType
	}
	return 0
}

func (m *GetUserControlInfoReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetUserControlInfoReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetUserControlInfoReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetUserControlInfoResp struct {
	InfoList             []*UserControlInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	NextPage             uint32             `protobuf:"varint,2,opt,name=next_page,json=nextPage,proto3" json:"next_page,omitempty"`
	TotalCnt             uint32             `protobuf:"varint,3,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetUserControlInfoResp) Reset()         { *m = GetUserControlInfoResp{} }
func (m *GetUserControlInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserControlInfoResp) ProtoMessage()    {}
func (*GetUserControlInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{66}
}
func (m *GetUserControlInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserControlInfoResp.Unmarshal(m, b)
}
func (m *GetUserControlInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserControlInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserControlInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserControlInfoResp.Merge(dst, src)
}
func (m *GetUserControlInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserControlInfoResp.Size(m)
}
func (m *GetUserControlInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserControlInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserControlInfoResp proto.InternalMessageInfo

func (m *GetUserControlInfoResp) GetInfoList() []*UserControlInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetUserControlInfoResp) GetNextPage() uint32 {
	if m != nil {
		return m.NextPage
	}
	return 0
}

func (m *GetUserControlInfoResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

// 解除受控用户的受控状态
type BatRemoveUserControlledReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatRemoveUserControlledReq) Reset()         { *m = BatRemoveUserControlledReq{} }
func (m *BatRemoveUserControlledReq) String() string { return proto.CompactTextString(m) }
func (*BatRemoveUserControlledReq) ProtoMessage()    {}
func (*BatRemoveUserControlledReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{67}
}
func (m *BatRemoveUserControlledReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatRemoveUserControlledReq.Unmarshal(m, b)
}
func (m *BatRemoveUserControlledReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatRemoveUserControlledReq.Marshal(b, m, deterministic)
}
func (dst *BatRemoveUserControlledReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatRemoveUserControlledReq.Merge(dst, src)
}
func (m *BatRemoveUserControlledReq) XXX_Size() int {
	return xxx_messageInfo_BatRemoveUserControlledReq.Size(m)
}
func (m *BatRemoveUserControlledReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatRemoveUserControlledReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatRemoveUserControlledReq proto.InternalMessageInfo

func (m *BatRemoveUserControlledReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatRemoveUserControlledResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatRemoveUserControlledResp) Reset()         { *m = BatRemoveUserControlledResp{} }
func (m *BatRemoveUserControlledResp) String() string { return proto.CompactTextString(m) }
func (*BatRemoveUserControlledResp) ProtoMessage()    {}
func (*BatRemoveUserControlledResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{68}
}
func (m *BatRemoveUserControlledResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatRemoveUserControlledResp.Unmarshal(m, b)
}
func (m *BatRemoveUserControlledResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatRemoveUserControlledResp.Marshal(b, m, deterministic)
}
func (dst *BatRemoveUserControlledResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatRemoveUserControlledResp.Merge(dst, src)
}
func (m *BatRemoveUserControlledResp) XXX_Size() int {
	return xxx_messageInfo_BatRemoveUserControlledResp.Size(m)
}
func (m *BatRemoveUserControlledResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatRemoveUserControlledResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatRemoveUserControlledResp proto.InternalMessageInfo

// 获取同实名信息用户列表
type GetTheSameRealNameUserListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTheSameRealNameUserListReq) Reset()         { *m = GetTheSameRealNameUserListReq{} }
func (m *GetTheSameRealNameUserListReq) String() string { return proto.CompactTextString(m) }
func (*GetTheSameRealNameUserListReq) ProtoMessage()    {}
func (*GetTheSameRealNameUserListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{69}
}
func (m *GetTheSameRealNameUserListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTheSameRealNameUserListReq.Unmarshal(m, b)
}
func (m *GetTheSameRealNameUserListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTheSameRealNameUserListReq.Marshal(b, m, deterministic)
}
func (dst *GetTheSameRealNameUserListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTheSameRealNameUserListReq.Merge(dst, src)
}
func (m *GetTheSameRealNameUserListReq) XXX_Size() int {
	return xxx_messageInfo_GetTheSameRealNameUserListReq.Size(m)
}
func (m *GetTheSameRealNameUserListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTheSameRealNameUserListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTheSameRealNameUserListReq proto.InternalMessageInfo

func (m *GetTheSameRealNameUserListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetTheSameRealNameUserListResp struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTheSameRealNameUserListResp) Reset()         { *m = GetTheSameRealNameUserListResp{} }
func (m *GetTheSameRealNameUserListResp) String() string { return proto.CompactTextString(m) }
func (*GetTheSameRealNameUserListResp) ProtoMessage()    {}
func (*GetTheSameRealNameUserListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{70}
}
func (m *GetTheSameRealNameUserListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTheSameRealNameUserListResp.Unmarshal(m, b)
}
func (m *GetTheSameRealNameUserListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTheSameRealNameUserListResp.Marshal(b, m, deterministic)
}
func (dst *GetTheSameRealNameUserListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTheSameRealNameUserListResp.Merge(dst, src)
}
func (m *GetTheSameRealNameUserListResp) XXX_Size() int {
	return xxx_messageInfo_GetTheSameRealNameUserListResp.Size(m)
}
func (m *GetTheSameRealNameUserListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTheSameRealNameUserListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTheSameRealNameUserListResp proto.InternalMessageInfo

func (m *GetTheSameRealNameUserListResp) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

// 认证的身份证信息
type AuthIdCardInfo struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	IdentityNum          string   `protobuf:"bytes,2,opt,name=identity_num,json=identityNum,proto3" json:"identity_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AuthIdCardInfo) Reset()         { *m = AuthIdCardInfo{} }
func (m *AuthIdCardInfo) String() string { return proto.CompactTextString(m) }
func (*AuthIdCardInfo) ProtoMessage()    {}
func (*AuthIdCardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{71}
}
func (m *AuthIdCardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AuthIdCardInfo.Unmarshal(m, b)
}
func (m *AuthIdCardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AuthIdCardInfo.Marshal(b, m, deterministic)
}
func (dst *AuthIdCardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AuthIdCardInfo.Merge(dst, src)
}
func (m *AuthIdCardInfo) XXX_Size() int {
	return xxx_messageInfo_AuthIdCardInfo.Size(m)
}
func (m *AuthIdCardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AuthIdCardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AuthIdCardInfo proto.InternalMessageInfo

func (m *AuthIdCardInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AuthIdCardInfo) GetIdentityNum() string {
	if m != nil {
		return m.IdentityNum
	}
	return ""
}

// 认证类型信息
type AuthTypeInfo struct {
	AuthType             uint32   `protobuf:"varint,1,opt,name=auth_type,json=authType,proto3" json:"auth_type,omitempty"`
	AuthStauts           uint32   `protobuf:"varint,2,opt,name=auth_stauts,json=authStauts,proto3" json:"auth_stauts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AuthTypeInfo) Reset()         { *m = AuthTypeInfo{} }
func (m *AuthTypeInfo) String() string { return proto.CompactTextString(m) }
func (*AuthTypeInfo) ProtoMessage()    {}
func (*AuthTypeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{72}
}
func (m *AuthTypeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AuthTypeInfo.Unmarshal(m, b)
}
func (m *AuthTypeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AuthTypeInfo.Marshal(b, m, deterministic)
}
func (dst *AuthTypeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AuthTypeInfo.Merge(dst, src)
}
func (m *AuthTypeInfo) XXX_Size() int {
	return xxx_messageInfo_AuthTypeInfo.Size(m)
}
func (m *AuthTypeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AuthTypeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AuthTypeInfo proto.InternalMessageInfo

func (m *AuthTypeInfo) GetAuthType() uint32 {
	if m != nil {
		return m.AuthType
	}
	return 0
}

func (m *AuthTypeInfo) GetAuthStauts() uint32 {
	if m != nil {
		return m.AuthStauts
	}
	return 0
}

// 获取实名认证信息
type GetUserRealNameAuthInfoV2Req struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	IsNeedAuthPhone      bool     `protobuf:"varint,2,opt,name=is_need_auth_phone,json=isNeedAuthPhone,proto3" json:"is_need_auth_phone,omitempty"`
	IsNeedIdcardInfo     bool     `protobuf:"varint,3,opt,name=is_need_idcard_info,json=isNeedIdcardInfo,proto3" json:"is_need_idcard_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserRealNameAuthInfoV2Req) Reset()         { *m = GetUserRealNameAuthInfoV2Req{} }
func (m *GetUserRealNameAuthInfoV2Req) String() string { return proto.CompactTextString(m) }
func (*GetUserRealNameAuthInfoV2Req) ProtoMessage()    {}
func (*GetUserRealNameAuthInfoV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{73}
}
func (m *GetUserRealNameAuthInfoV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRealNameAuthInfoV2Req.Unmarshal(m, b)
}
func (m *GetUserRealNameAuthInfoV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRealNameAuthInfoV2Req.Marshal(b, m, deterministic)
}
func (dst *GetUserRealNameAuthInfoV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRealNameAuthInfoV2Req.Merge(dst, src)
}
func (m *GetUserRealNameAuthInfoV2Req) XXX_Size() int {
	return xxx_messageInfo_GetUserRealNameAuthInfoV2Req.Size(m)
}
func (m *GetUserRealNameAuthInfoV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRealNameAuthInfoV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRealNameAuthInfoV2Req proto.InternalMessageInfo

func (m *GetUserRealNameAuthInfoV2Req) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserRealNameAuthInfoV2Req) GetIsNeedAuthPhone() bool {
	if m != nil {
		return m.IsNeedAuthPhone
	}
	return false
}

func (m *GetUserRealNameAuthInfoV2Req) GetIsNeedIdcardInfo() bool {
	if m != nil {
		return m.IsNeedIdcardInfo
	}
	return false
}

type GetUserRealNameAuthInfoV2Resp struct {
	AuthList             []*AuthTypeInfo `protobuf:"bytes,1,rep,name=auth_list,json=authList,proto3" json:"auth_list,omitempty"`
	IdcardInfo           *AuthIdCardInfo `protobuf:"bytes,2,opt,name=idcard_info,json=idcardInfo,proto3" json:"idcard_info,omitempty"`
	AuthPhone            string          `protobuf:"bytes,3,opt,name=auth_phone,json=authPhone,proto3" json:"auth_phone,omitempty"`
	IsAdult              bool            `protobuf:"varint,4,opt,name=is_adult,json=isAdult,proto3" json:"is_adult,omitempty"`
	Age                  uint32          `protobuf:"varint,5,opt,name=age,proto3" json:"age,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetUserRealNameAuthInfoV2Resp) Reset()         { *m = GetUserRealNameAuthInfoV2Resp{} }
func (m *GetUserRealNameAuthInfoV2Resp) String() string { return proto.CompactTextString(m) }
func (*GetUserRealNameAuthInfoV2Resp) ProtoMessage()    {}
func (*GetUserRealNameAuthInfoV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{74}
}
func (m *GetUserRealNameAuthInfoV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRealNameAuthInfoV2Resp.Unmarshal(m, b)
}
func (m *GetUserRealNameAuthInfoV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRealNameAuthInfoV2Resp.Marshal(b, m, deterministic)
}
func (dst *GetUserRealNameAuthInfoV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRealNameAuthInfoV2Resp.Merge(dst, src)
}
func (m *GetUserRealNameAuthInfoV2Resp) XXX_Size() int {
	return xxx_messageInfo_GetUserRealNameAuthInfoV2Resp.Size(m)
}
func (m *GetUserRealNameAuthInfoV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRealNameAuthInfoV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRealNameAuthInfoV2Resp proto.InternalMessageInfo

func (m *GetUserRealNameAuthInfoV2Resp) GetAuthList() []*AuthTypeInfo {
	if m != nil {
		return m.AuthList
	}
	return nil
}

func (m *GetUserRealNameAuthInfoV2Resp) GetIdcardInfo() *AuthIdCardInfo {
	if m != nil {
		return m.IdcardInfo
	}
	return nil
}

func (m *GetUserRealNameAuthInfoV2Resp) GetAuthPhone() string {
	if m != nil {
		return m.AuthPhone
	}
	return ""
}

func (m *GetUserRealNameAuthInfoV2Resp) GetIsAdult() bool {
	if m != nil {
		return m.IsAdult
	}
	return false
}

func (m *GetUserRealNameAuthInfoV2Resp) GetAge() uint32 {
	if m != nil {
		return m.Age
	}
	return 0
}

// 提供给运营平台使用的，根据uid查询身份证，所以接口的数据结构的字段不多
type BatchGetUserRealNameAuthInfoReq struct {
	Uid                  []uint32 `protobuf:"varint,1,rep,packed,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetUserRealNameAuthInfoReq) Reset()         { *m = BatchGetUserRealNameAuthInfoReq{} }
func (m *BatchGetUserRealNameAuthInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserRealNameAuthInfoReq) ProtoMessage()    {}
func (*BatchGetUserRealNameAuthInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{75}
}
func (m *BatchGetUserRealNameAuthInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserRealNameAuthInfoReq.Unmarshal(m, b)
}
func (m *BatchGetUserRealNameAuthInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserRealNameAuthInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserRealNameAuthInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserRealNameAuthInfoReq.Merge(dst, src)
}
func (m *BatchGetUserRealNameAuthInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserRealNameAuthInfoReq.Size(m)
}
func (m *BatchGetUserRealNameAuthInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserRealNameAuthInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserRealNameAuthInfoReq proto.InternalMessageInfo

func (m *BatchGetUserRealNameAuthInfoReq) GetUid() []uint32 {
	if m != nil {
		return m.Uid
	}
	return nil
}

type UserRealNameAuthInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	IdentityCard         string   `protobuf:"bytes,2,opt,name=identity_card,json=identityCard,proto3" json:"identity_card,omitempty"`
	Phone                string   `protobuf:"bytes,3,opt,name=phone,proto3" json:"phone,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserRealNameAuthInfo) Reset()         { *m = UserRealNameAuthInfo{} }
func (m *UserRealNameAuthInfo) String() string { return proto.CompactTextString(m) }
func (*UserRealNameAuthInfo) ProtoMessage()    {}
func (*UserRealNameAuthInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{76}
}
func (m *UserRealNameAuthInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserRealNameAuthInfo.Unmarshal(m, b)
}
func (m *UserRealNameAuthInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserRealNameAuthInfo.Marshal(b, m, deterministic)
}
func (dst *UserRealNameAuthInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserRealNameAuthInfo.Merge(dst, src)
}
func (m *UserRealNameAuthInfo) XXX_Size() int {
	return xxx_messageInfo_UserRealNameAuthInfo.Size(m)
}
func (m *UserRealNameAuthInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserRealNameAuthInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserRealNameAuthInfo proto.InternalMessageInfo

func (m *UserRealNameAuthInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserRealNameAuthInfo) GetIdentityCard() string {
	if m != nil {
		return m.IdentityCard
	}
	return ""
}

func (m *UserRealNameAuthInfo) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

type BatchGetUserRealNameAuthInfoResp struct {
	List                 []*UserRealNameAuthInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *BatchGetUserRealNameAuthInfoResp) Reset()         { *m = BatchGetUserRealNameAuthInfoResp{} }
func (m *BatchGetUserRealNameAuthInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserRealNameAuthInfoResp) ProtoMessage()    {}
func (*BatchGetUserRealNameAuthInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{77}
}
func (m *BatchGetUserRealNameAuthInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserRealNameAuthInfoResp.Unmarshal(m, b)
}
func (m *BatchGetUserRealNameAuthInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserRealNameAuthInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserRealNameAuthInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserRealNameAuthInfoResp.Merge(dst, src)
}
func (m *BatchGetUserRealNameAuthInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserRealNameAuthInfoResp.Size(m)
}
func (m *BatchGetUserRealNameAuthInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserRealNameAuthInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserRealNameAuthInfoResp proto.InternalMessageInfo

func (m *BatchGetUserRealNameAuthInfoResp) GetList() []*UserRealNameAuthInfo {
	if m != nil {
		return m.List
	}
	return nil
}

// 用户在设备上的历史登陆信息
type UserDeviceLastLoginInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Version              string   `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	Province             string   `protobuf:"bytes,3,opt,name=province,proto3" json:"province,omitempty"`
	City                 string   `protobuf:"bytes,4,opt,name=city,proto3" json:"city,omitempty"`
	Ip                   string   `protobuf:"bytes,5,opt,name=ip,proto3" json:"ip,omitempty"`
	LastLoginDate        int64    `protobuf:"varint,6,opt,name=last_login_date,json=lastLoginDate,proto3" json:"last_login_date,omitempty"`
	DeviceId             string   `protobuf:"bytes,7,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	Platform             string   `protobuf:"bytes,8,opt,name=platform,proto3" json:"platform,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserDeviceLastLoginInfo) Reset()         { *m = UserDeviceLastLoginInfo{} }
func (m *UserDeviceLastLoginInfo) String() string { return proto.CompactTextString(m) }
func (*UserDeviceLastLoginInfo) ProtoMessage()    {}
func (*UserDeviceLastLoginInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{78}
}
func (m *UserDeviceLastLoginInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDeviceLastLoginInfo.Unmarshal(m, b)
}
func (m *UserDeviceLastLoginInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDeviceLastLoginInfo.Marshal(b, m, deterministic)
}
func (dst *UserDeviceLastLoginInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDeviceLastLoginInfo.Merge(dst, src)
}
func (m *UserDeviceLastLoginInfo) XXX_Size() int {
	return xxx_messageInfo_UserDeviceLastLoginInfo.Size(m)
}
func (m *UserDeviceLastLoginInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDeviceLastLoginInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserDeviceLastLoginInfo proto.InternalMessageInfo

func (m *UserDeviceLastLoginInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserDeviceLastLoginInfo) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

func (m *UserDeviceLastLoginInfo) GetProvince() string {
	if m != nil {
		return m.Province
	}
	return ""
}

func (m *UserDeviceLastLoginInfo) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *UserDeviceLastLoginInfo) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *UserDeviceLastLoginInfo) GetLastLoginDate() int64 {
	if m != nil {
		return m.LastLoginDate
	}
	return 0
}

func (m *UserDeviceLastLoginInfo) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *UserDeviceLastLoginInfo) GetPlatform() string {
	if m != nil {
		return m.Platform
	}
	return ""
}

// 设备信息
type DeviceInfo struct {
	DeviceId             string                     `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	BanStatus            uint32                     `protobuf:"varint,2,opt,name=ban_status,json=banStatus,proto3" json:"ban_status,omitempty"`
	UserInfo             []*UserDeviceLastLoginInfo `protobuf:"bytes,3,rep,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *DeviceInfo) Reset()         { *m = DeviceInfo{} }
func (m *DeviceInfo) String() string { return proto.CompactTextString(m) }
func (*DeviceInfo) ProtoMessage()    {}
func (*DeviceInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{79}
}
func (m *DeviceInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeviceInfo.Unmarshal(m, b)
}
func (m *DeviceInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeviceInfo.Marshal(b, m, deterministic)
}
func (dst *DeviceInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeviceInfo.Merge(dst, src)
}
func (m *DeviceInfo) XXX_Size() int {
	return xxx_messageInfo_DeviceInfo.Size(m)
}
func (m *DeviceInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DeviceInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DeviceInfo proto.InternalMessageInfo

func (m *DeviceInfo) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *DeviceInfo) GetBanStatus() uint32 {
	if m != nil {
		return m.BanStatus
	}
	return 0
}

func (m *DeviceInfo) GetUserInfo() []*UserDeviceLastLoginInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

type GetDeviceLastLoginInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	DeviceId             string   `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDeviceLastLoginInfoReq) Reset()         { *m = GetDeviceLastLoginInfoReq{} }
func (m *GetDeviceLastLoginInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetDeviceLastLoginInfoReq) ProtoMessage()    {}
func (*GetDeviceLastLoginInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{80}
}
func (m *GetDeviceLastLoginInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDeviceLastLoginInfoReq.Unmarshal(m, b)
}
func (m *GetDeviceLastLoginInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDeviceLastLoginInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetDeviceLastLoginInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDeviceLastLoginInfoReq.Merge(dst, src)
}
func (m *GetDeviceLastLoginInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetDeviceLastLoginInfoReq.Size(m)
}
func (m *GetDeviceLastLoginInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDeviceLastLoginInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetDeviceLastLoginInfoReq proto.InternalMessageInfo

func (m *GetDeviceLastLoginInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetDeviceLastLoginInfoReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

type GetDeviceLastLoginInfoResp struct {
	DeviceList           []*DeviceInfo `protobuf:"bytes,1,rep,name=device_list,json=deviceList,proto3" json:"device_list,omitempty"`
	Count                uint32        `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetDeviceLastLoginInfoResp) Reset()         { *m = GetDeviceLastLoginInfoResp{} }
func (m *GetDeviceLastLoginInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetDeviceLastLoginInfoResp) ProtoMessage()    {}
func (*GetDeviceLastLoginInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{81}
}
func (m *GetDeviceLastLoginInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDeviceLastLoginInfoResp.Unmarshal(m, b)
}
func (m *GetDeviceLastLoginInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDeviceLastLoginInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetDeviceLastLoginInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDeviceLastLoginInfoResp.Merge(dst, src)
}
func (m *GetDeviceLastLoginInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetDeviceLastLoginInfoResp.Size(m)
}
func (m *GetDeviceLastLoginInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDeviceLastLoginInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetDeviceLastLoginInfoResp proto.InternalMessageInfo

func (m *GetDeviceLastLoginInfoResp) GetDeviceList() []*DeviceInfo {
	if m != nil {
		return m.DeviceList
	}
	return nil
}

func (m *GetDeviceLastLoginInfoResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type DeviceBanLog struct {
	DeviceId             string   `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	OpType               BanType  `protobuf:"varint,2,opt,name=op_type,json=opType,proto3,enum=apicentergo.BanType" json:"op_type,omitempty"`
	At                   int64    `protobuf:"varint,3,opt,name=at,proto3" json:"at,omitempty"`
	BanDays              string   `protobuf:"bytes,4,opt,name=ban_days,json=banDays,proto3" json:"ban_days,omitempty"`
	BanUidList           []uint32 `protobuf:"varint,5,rep,packed,name=ban_uid_list,json=banUidList,proto3" json:"ban_uid_list,omitempty"`
	RecoveryAt           int64    `protobuf:"varint,6,opt,name=recovery_at,json=recoveryAt,proto3" json:"recovery_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeviceBanLog) Reset()         { *m = DeviceBanLog{} }
func (m *DeviceBanLog) String() string { return proto.CompactTextString(m) }
func (*DeviceBanLog) ProtoMessage()    {}
func (*DeviceBanLog) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{82}
}
func (m *DeviceBanLog) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeviceBanLog.Unmarshal(m, b)
}
func (m *DeviceBanLog) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeviceBanLog.Marshal(b, m, deterministic)
}
func (dst *DeviceBanLog) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeviceBanLog.Merge(dst, src)
}
func (m *DeviceBanLog) XXX_Size() int {
	return xxx_messageInfo_DeviceBanLog.Size(m)
}
func (m *DeviceBanLog) XXX_DiscardUnknown() {
	xxx_messageInfo_DeviceBanLog.DiscardUnknown(m)
}

var xxx_messageInfo_DeviceBanLog proto.InternalMessageInfo

func (m *DeviceBanLog) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *DeviceBanLog) GetOpType() BanType {
	if m != nil {
		return m.OpType
	}
	return BanType_OP_UNKNOWN
}

func (m *DeviceBanLog) GetAt() int64 {
	if m != nil {
		return m.At
	}
	return 0
}

func (m *DeviceBanLog) GetBanDays() string {
	if m != nil {
		return m.BanDays
	}
	return ""
}

func (m *DeviceBanLog) GetBanUidList() []uint32 {
	if m != nil {
		return m.BanUidList
	}
	return nil
}

func (m *DeviceBanLog) GetRecoveryAt() int64 {
	if m != nil {
		return m.RecoveryAt
	}
	return 0
}

type GetBanLogReq struct {
	DeviceId             string   `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Page                 uint32   `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBanLogReq) Reset()         { *m = GetBanLogReq{} }
func (m *GetBanLogReq) String() string { return proto.CompactTextString(m) }
func (*GetBanLogReq) ProtoMessage()    {}
func (*GetBanLogReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{83}
}
func (m *GetBanLogReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBanLogReq.Unmarshal(m, b)
}
func (m *GetBanLogReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBanLogReq.Marshal(b, m, deterministic)
}
func (dst *GetBanLogReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBanLogReq.Merge(dst, src)
}
func (m *GetBanLogReq) XXX_Size() int {
	return xxx_messageInfo_GetBanLogReq.Size(m)
}
func (m *GetBanLogReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBanLogReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBanLogReq proto.InternalMessageInfo

func (m *GetBanLogReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *GetBanLogReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetBanLogReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

type GetBanLogResp struct {
	BanLog               []*DeviceBanLog `protobuf:"bytes,1,rep,name=ban_log,json=banLog,proto3" json:"ban_log,omitempty"`
	Limit                uint32          `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Page                 uint32          `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	Count                uint32          `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetBanLogResp) Reset()         { *m = GetBanLogResp{} }
func (m *GetBanLogResp) String() string { return proto.CompactTextString(m) }
func (*GetBanLogResp) ProtoMessage()    {}
func (*GetBanLogResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{84}
}
func (m *GetBanLogResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBanLogResp.Unmarshal(m, b)
}
func (m *GetBanLogResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBanLogResp.Marshal(b, m, deterministic)
}
func (dst *GetBanLogResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBanLogResp.Merge(dst, src)
}
func (m *GetBanLogResp) XXX_Size() int {
	return xxx_messageInfo_GetBanLogResp.Size(m)
}
func (m *GetBanLogResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBanLogResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBanLogResp proto.InternalMessageInfo

func (m *GetBanLogResp) GetBanLog() []*DeviceBanLog {
	if m != nil {
		return m.BanLog
	}
	return nil
}

func (m *GetBanLogResp) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetBanLogResp) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetBanLogResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type UserBanStatus struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BanStatus            uint32   `protobuf:"varint,2,opt,name=ban_status,json=banStatus,proto3" json:"ban_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserBanStatus) Reset()         { *m = UserBanStatus{} }
func (m *UserBanStatus) String() string { return proto.CompactTextString(m) }
func (*UserBanStatus) ProtoMessage()    {}
func (*UserBanStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{85}
}
func (m *UserBanStatus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserBanStatus.Unmarshal(m, b)
}
func (m *UserBanStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserBanStatus.Marshal(b, m, deterministic)
}
func (dst *UserBanStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserBanStatus.Merge(dst, src)
}
func (m *UserBanStatus) XXX_Size() int {
	return xxx_messageInfo_UserBanStatus.Size(m)
}
func (m *UserBanStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_UserBanStatus.DiscardUnknown(m)
}

var xxx_messageInfo_UserBanStatus proto.InternalMessageInfo

func (m *UserBanStatus) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserBanStatus) GetBanStatus() uint32 {
	if m != nil {
		return m.BanStatus
	}
	return 0
}

type GetUserBanStatusReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserBanStatusReq) Reset()         { *m = GetUserBanStatusReq{} }
func (m *GetUserBanStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetUserBanStatusReq) ProtoMessage()    {}
func (*GetUserBanStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{86}
}
func (m *GetUserBanStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserBanStatusReq.Unmarshal(m, b)
}
func (m *GetUserBanStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserBanStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetUserBanStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserBanStatusReq.Merge(dst, src)
}
func (m *GetUserBanStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetUserBanStatusReq.Size(m)
}
func (m *GetUserBanStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserBanStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserBanStatusReq proto.InternalMessageInfo

func (m *GetUserBanStatusReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type GetUserBanStatusResp struct {
	UserStatus           []*UserBanStatus `protobuf:"bytes,1,rep,name=user_status,json=userStatus,proto3" json:"user_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetUserBanStatusResp) Reset()         { *m = GetUserBanStatusResp{} }
func (m *GetUserBanStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetUserBanStatusResp) ProtoMessage()    {}
func (*GetUserBanStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{87}
}
func (m *GetUserBanStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserBanStatusResp.Unmarshal(m, b)
}
func (m *GetUserBanStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserBanStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetUserBanStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserBanStatusResp.Merge(dst, src)
}
func (m *GetUserBanStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetUserBanStatusResp.Size(m)
}
func (m *GetUserBanStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserBanStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserBanStatusResp proto.InternalMessageInfo

func (m *GetUserBanStatusResp) GetUserStatus() []*UserBanStatus {
	if m != nil {
		return m.UserStatus
	}
	return nil
}

type BatchBanUserWithDeviceReq struct {
	DeviceId             string   `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	UidList              []uint32 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	OpType               BanType  `protobuf:"varint,3,opt,name=op_type,json=opType,proto3,enum=apicentergo.BanType" json:"op_type,omitempty"`
	Reason               string   `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,omitempty"`
	AutoRecoveryAt       int64    `protobuf:"varint,5,opt,name=auto_recovery_at,json=autoRecoveryAt,proto3" json:"auto_recovery_at,omitempty"`
	ProofPic             string   `protobuf:"bytes,6,opt,name=proof_pic,json=proofPic,proto3" json:"proof_pic,omitempty"`
	ReasonDetail         string   `protobuf:"bytes,7,opt,name=reason_detail,json=reasonDetail,proto3" json:"reason_detail,omitempty"`
	BannedDays           uint64   `protobuf:"varint,8,opt,name=banned_days,json=bannedDays,proto3" json:"banned_days,omitempty"`
	OperatorName         string   `protobuf:"bytes,9,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchBanUserWithDeviceReq) Reset()         { *m = BatchBanUserWithDeviceReq{} }
func (m *BatchBanUserWithDeviceReq) String() string { return proto.CompactTextString(m) }
func (*BatchBanUserWithDeviceReq) ProtoMessage()    {}
func (*BatchBanUserWithDeviceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{88}
}
func (m *BatchBanUserWithDeviceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchBanUserWithDeviceReq.Unmarshal(m, b)
}
func (m *BatchBanUserWithDeviceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchBanUserWithDeviceReq.Marshal(b, m, deterministic)
}
func (dst *BatchBanUserWithDeviceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchBanUserWithDeviceReq.Merge(dst, src)
}
func (m *BatchBanUserWithDeviceReq) XXX_Size() int {
	return xxx_messageInfo_BatchBanUserWithDeviceReq.Size(m)
}
func (m *BatchBanUserWithDeviceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchBanUserWithDeviceReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchBanUserWithDeviceReq proto.InternalMessageInfo

func (m *BatchBanUserWithDeviceReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *BatchBanUserWithDeviceReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatchBanUserWithDeviceReq) GetOpType() BanType {
	if m != nil {
		return m.OpType
	}
	return BanType_OP_UNKNOWN
}

func (m *BatchBanUserWithDeviceReq) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *BatchBanUserWithDeviceReq) GetAutoRecoveryAt() int64 {
	if m != nil {
		return m.AutoRecoveryAt
	}
	return 0
}

func (m *BatchBanUserWithDeviceReq) GetProofPic() string {
	if m != nil {
		return m.ProofPic
	}
	return ""
}

func (m *BatchBanUserWithDeviceReq) GetReasonDetail() string {
	if m != nil {
		return m.ReasonDetail
	}
	return ""
}

func (m *BatchBanUserWithDeviceReq) GetBannedDays() uint64 {
	if m != nil {
		return m.BannedDays
	}
	return 0
}

func (m *BatchBanUserWithDeviceReq) GetOperatorName() string {
	if m != nil {
		return m.OperatorName
	}
	return ""
}

type BatchBanUserWithDeviceResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchBanUserWithDeviceResp) Reset()         { *m = BatchBanUserWithDeviceResp{} }
func (m *BatchBanUserWithDeviceResp) String() string { return proto.CompactTextString(m) }
func (*BatchBanUserWithDeviceResp) ProtoMessage()    {}
func (*BatchBanUserWithDeviceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userservice_api_dcfb7f86fcd43c6e, []int{89}
}
func (m *BatchBanUserWithDeviceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchBanUserWithDeviceResp.Unmarshal(m, b)
}
func (m *BatchBanUserWithDeviceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchBanUserWithDeviceResp.Marshal(b, m, deterministic)
}
func (dst *BatchBanUserWithDeviceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchBanUserWithDeviceResp.Merge(dst, src)
}
func (m *BatchBanUserWithDeviceResp) XXX_Size() int {
	return xxx_messageInfo_BatchBanUserWithDeviceResp.Size(m)
}
func (m *BatchBanUserWithDeviceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchBanUserWithDeviceResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchBanUserWithDeviceResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*AwardInfo)(nil), "apicentergo.AwardInfo")
	proto.RegisterType((*AwardRecord)(nil), "apicentergo.AwardRecord")
	proto.RegisterType((*AwardTBeanToUserReq)(nil), "apicentergo.AwardTBeanToUserReq")
	proto.RegisterType((*AwardTBeanToUserResp)(nil), "apicentergo.AwardTBeanToUserResp")
	proto.RegisterType((*AwardDiamondToUserReq)(nil), "apicentergo.AwardDiamondToUserReq")
	proto.RegisterType((*AwardDiamondToUserResp)(nil), "apicentergo.AwardDiamondToUserResp")
	proto.RegisterType((*GetAwardDiamondRecordReq)(nil), "apicentergo.GetAwardDiamondRecordReq")
	proto.RegisterType((*GetAwardDiamondRecordResp)(nil), "apicentergo.GetAwardDiamondRecordResp")
	proto.RegisterType((*AwardExpToUserReq)(nil), "apicentergo.AwardExpToUserReq")
	proto.RegisterType((*AwardExpToUserResp)(nil), "apicentergo.AwardExpToUserResp")
	proto.RegisterType((*GetAwardExpRecordReq)(nil), "apicentergo.GetAwardExpRecordReq")
	proto.RegisterType((*GetAwardExpRecordResp)(nil), "apicentergo.GetAwardExpRecordResp")
	proto.RegisterType((*UserLoginInfo)(nil), "apicentergo.UserLoginInfo")
	proto.RegisterType((*GetUserLoginInfoReq)(nil), "apicentergo.GetUserLoginInfoReq")
	proto.RegisterType((*GetUserLoginInfoResp)(nil), "apicentergo.GetUserLoginInfoResp")
	proto.RegisterType((*BannedRecord)(nil), "apicentergo.BannedRecord")
	proto.RegisterType((*GetBannedRecordReq)(nil), "apicentergo.GetBannedRecordReq")
	proto.RegisterType((*GetBannedRecordResp)(nil), "apicentergo.GetBannedRecordResp")
	proto.RegisterType((*GetBannedOperatorReq)(nil), "apicentergo.GetBannedOperatorReq")
	proto.RegisterType((*BannedOperator)(nil), "apicentergo.BannedOperator")
	proto.RegisterType((*GetBannedOperatorResp)(nil), "apicentergo.GetBannedOperatorResp")
	proto.RegisterType((*BannedAppealRecord)(nil), "apicentergo.BannedAppealRecord")
	proto.RegisterType((*GetBannedAppealRecordReq)(nil), "apicentergo.GetBannedAppealRecordReq")
	proto.RegisterType((*GetBannedAppealRecordResp)(nil), "apicentergo.GetBannedAppealRecordResp")
	proto.RegisterType((*UpdateBannedAppealRecordReq)(nil), "apicentergo.UpdateBannedAppealRecordReq")
	proto.RegisterType((*UpdateBannedAppealRecordResp)(nil), "apicentergo.UpdateBannedAppealRecordResp")
	proto.RegisterType((*GetBannedCheckRecordReq)(nil), "apicentergo.GetBannedCheckRecordReq")
	proto.RegisterType((*BannedCheckRecord)(nil), "apicentergo.BannedCheckRecord")
	proto.RegisterType((*GetBannedCheckRecordResp)(nil), "apicentergo.GetBannedCheckRecordResp")
	proto.RegisterType((*UpdateBannedCheckRecordReq)(nil), "apicentergo.UpdateBannedCheckRecordReq")
	proto.RegisterType((*UpdateBannedCheckRecordResp)(nil), "apicentergo.UpdateBannedCheckRecordResp")
	proto.RegisterType((*GetBannedCheckOperatorReq)(nil), "apicentergo.GetBannedCheckOperatorReq")
	proto.RegisterType((*GetBannedCheckOperatorResp)(nil), "apicentergo.GetBannedCheckOperatorResp")
	proto.RegisterType((*UserLoginDevice)(nil), "apicentergo.UserLoginDevice")
	proto.RegisterType((*GetUserLoginDeviceReq)(nil), "apicentergo.GetUserLoginDeviceReq")
	proto.RegisterType((*GetUserLoginDeviceResp)(nil), "apicentergo.GetUserLoginDeviceResp")
	proto.RegisterType((*UserLoginWithDevice)(nil), "apicentergo.UserLoginWithDevice")
	proto.RegisterType((*GetUserLoginWithDeviceReq)(nil), "apicentergo.GetUserLoginWithDeviceReq")
	proto.RegisterType((*GetUserLoginWithDeviceResp)(nil), "apicentergo.GetUserLoginWithDeviceResp")
	proto.RegisterType((*BanUserReq)(nil), "apicentergo.BanUserReq")
	proto.RegisterType((*BanUserResp)(nil), "apicentergo.BanUserResp")
	proto.RegisterType((*CanRecoverDeviceReq)(nil), "apicentergo.CanRecoverDeviceReq")
	proto.RegisterType((*CanRecoverDeviceResp)(nil), "apicentergo.CanRecoverDeviceResp")
	proto.RegisterType((*RecoverUserReq)(nil), "apicentergo.RecoverUserReq")
	proto.RegisterType((*RecoverUserResp)(nil), "apicentergo.RecoverUserResp")
	proto.RegisterType((*RecoverUserInfo)(nil), "apicentergo.RecoverUserInfo")
	proto.RegisterType((*BatchRecoverUserReq)(nil), "apicentergo.BatchRecoverUserReq")
	proto.RegisterType((*BatchRecoverUserResp)(nil), "apicentergo.BatchRecoverUserResp")
	proto.RegisterType((*GetUserInviteHistoryReq)(nil), "apicentergo.GetUserInviteHistoryReq")
	proto.RegisterType((*GetUserInviteHistoryResp)(nil), "apicentergo.GetUserInviteHistoryResp")
	proto.RegisterType((*ResetUserPasswordReq)(nil), "apicentergo.ResetUserPasswordReq")
	proto.RegisterType((*ResetUserPasswordResp)(nil), "apicentergo.ResetUserPasswordResp")
	proto.RegisterType((*UnbindUserPhoneReq)(nil), "apicentergo.UnbindUserPhoneReq")
	proto.RegisterType((*UnbindUserPhoneResp)(nil), "apicentergo.UnbindUserPhoneResp")
	proto.RegisterType((*ClearSecurityQuestionReq)(nil), "apicentergo.ClearSecurityQuestionReq")
	proto.RegisterType((*ClearSecurityQuestionResp)(nil), "apicentergo.ClearSecurityQuestionResp")
	proto.RegisterType((*DetachThirdpartReq)(nil), "apicentergo.DetachThirdpartReq")
	proto.RegisterType((*DetachThirdpartResp)(nil), "apicentergo.DetachThirdpartResp")
	proto.RegisterType((*OfficialCertInfo)(nil), "apicentergo.OfficialCertInfo")
	proto.RegisterType((*UserOfficialCertInfo)(nil), "apicentergo.UserOfficialCertInfo")
	proto.RegisterType((*BatchGetUserOfficialCertReq)(nil), "apicentergo.BatchGetUserOfficialCertReq")
	proto.RegisterType((*BatchGetUserOfficialCertResp)(nil), "apicentergo.BatchGetUserOfficialCertResp")
	proto.RegisterType((*GetUserExpReq)(nil), "apicentergo.GetUserExpReq")
	proto.RegisterType((*GetUserExpResp)(nil), "apicentergo.GetUserExpResp")
	proto.RegisterType((*UserControlInfo)(nil), "apicentergo.UserControlInfo")
	proto.RegisterType((*GetUserControlInfoReq)(nil), "apicentergo.GetUserControlInfoReq")
	proto.RegisterType((*GetUserControlInfoResp)(nil), "apicentergo.GetUserControlInfoResp")
	proto.RegisterType((*BatRemoveUserControlledReq)(nil), "apicentergo.BatRemoveUserControlledReq")
	proto.RegisterType((*BatRemoveUserControlledResp)(nil), "apicentergo.BatRemoveUserControlledResp")
	proto.RegisterType((*GetTheSameRealNameUserListReq)(nil), "apicentergo.GetTheSameRealNameUserListReq")
	proto.RegisterType((*GetTheSameRealNameUserListResp)(nil), "apicentergo.GetTheSameRealNameUserListResp")
	proto.RegisterType((*AuthIdCardInfo)(nil), "apicentergo.AuthIdCardInfo")
	proto.RegisterType((*AuthTypeInfo)(nil), "apicentergo.AuthTypeInfo")
	proto.RegisterType((*GetUserRealNameAuthInfoV2Req)(nil), "apicentergo.GetUserRealNameAuthInfoV2Req")
	proto.RegisterType((*GetUserRealNameAuthInfoV2Resp)(nil), "apicentergo.GetUserRealNameAuthInfoV2Resp")
	proto.RegisterType((*BatchGetUserRealNameAuthInfoReq)(nil), "apicentergo.BatchGetUserRealNameAuthInfoReq")
	proto.RegisterType((*UserRealNameAuthInfo)(nil), "apicentergo.UserRealNameAuthInfo")
	proto.RegisterType((*BatchGetUserRealNameAuthInfoResp)(nil), "apicentergo.BatchGetUserRealNameAuthInfoResp")
	proto.RegisterType((*UserDeviceLastLoginInfo)(nil), "apicentergo.UserDeviceLastLoginInfo")
	proto.RegisterType((*DeviceInfo)(nil), "apicentergo.DeviceInfo")
	proto.RegisterType((*GetDeviceLastLoginInfoReq)(nil), "apicentergo.GetDeviceLastLoginInfoReq")
	proto.RegisterType((*GetDeviceLastLoginInfoResp)(nil), "apicentergo.GetDeviceLastLoginInfoResp")
	proto.RegisterType((*DeviceBanLog)(nil), "apicentergo.DeviceBanLog")
	proto.RegisterType((*GetBanLogReq)(nil), "apicentergo.GetBanLogReq")
	proto.RegisterType((*GetBanLogResp)(nil), "apicentergo.GetBanLogResp")
	proto.RegisterType((*UserBanStatus)(nil), "apicentergo.UserBanStatus")
	proto.RegisterType((*GetUserBanStatusReq)(nil), "apicentergo.GetUserBanStatusReq")
	proto.RegisterType((*GetUserBanStatusResp)(nil), "apicentergo.GetUserBanStatusResp")
	proto.RegisterType((*BatchBanUserWithDeviceReq)(nil), "apicentergo.BatchBanUserWithDeviceReq")
	proto.RegisterType((*BatchBanUserWithDeviceResp)(nil), "apicentergo.BatchBanUserWithDeviceResp")
	proto.RegisterEnum("apicentergo.BanOpType", BanOpType_name, BanOpType_value)
	proto.RegisterEnum("apicentergo.BannedAppealState", BannedAppealState_name, BannedAppealState_value)
	proto.RegisterEnum("apicentergo.BannedReasonCode", BannedReasonCode_name, BannedReasonCode_value)
	proto.RegisterEnum("apicentergo.BannedCheckUserTag", BannedCheckUserTag_name, BannedCheckUserTag_value)
	proto.RegisterEnum("apicentergo.BannedCheckSource", BannedCheckSource_name, BannedCheckSource_value)
	proto.RegisterEnum("apicentergo.BannedCheckRecordType", BannedCheckRecordType_name, BannedCheckRecordType_value)
	proto.RegisterEnum("apicentergo.BannedCheckType", BannedCheckType_name, BannedCheckType_value)
	proto.RegisterEnum("apicentergo.AuthType", AuthType_name, AuthType_value)
	proto.RegisterEnum("apicentergo.EAuthStatus", EAuthStatus_name, EAuthStatus_value)
	proto.RegisterEnum("apicentergo.BanType", BanType_name, BanType_value)
	proto.RegisterEnum("apicentergo.UpdateBannedCheckRecordReq_UpdateType", UpdateBannedCheckRecordReq_UpdateType_name, UpdateBannedCheckRecordReq_UpdateType_value)
	proto.RegisterEnum("apicentergo.GetUserControlInfoReq_QueryType", GetUserControlInfoReq_QueryType_name, GetUserControlInfoReq_QueryType_value)
}

func init() {
	proto.RegisterFile("apicenter-go/userservice-api.proto", fileDescriptor_userservice_api_dcfb7f86fcd43c6e)
}

var fileDescriptor_userservice_api_dcfb7f86fcd43c6e = []byte{
	// 4149 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x3b, 0x4d, 0x6f, 0x1b, 0x49,
	0x76, 0xd3, 0x24, 0x25, 0x35, 0x1f, 0x45, 0x8a, 0x6e, 0x49, 0x16, 0x65, 0x5b, 0xb6, 0xdc, 0xe3,
	0x9d, 0x35, 0x94, 0xd8, 0xde, 0xf5, 0xc4, 0x3b, 0xb3, 0x3b, 0xd9, 0x64, 0x29, 0x92, 0x63, 0x73,
	0x46, 0x26, 0x39, 0x4d, 0xd2, 0x0b, 0x25, 0xc0, 0x36, 0xda, 0xec, 0x92, 0xd4, 0x30, 0xd9, 0xdd,
	0xd3, 0x55, 0x94, 0xad, 0x3d, 0x0d, 0x12, 0x04, 0x39, 0xe4, 0x03, 0x08, 0x82, 0x9c, 0xf2, 0x0f,
	0x72, 0x09, 0xf2, 0x81, 0x5c, 0x72, 0x0c, 0x02, 0xe4, 0x90, 0x5c, 0x72, 0xcf, 0x21, 0x08, 0xb0,
	0xc8, 0xcf, 0x08, 0xea, 0x55, 0x75, 0xb3, 0xbf, 0x48, 0x2b, 0xd9, 0xd9, 0xdc, 0x58, 0xaf, 0xde,
	0xab, 0x7a, 0x5f, 0xf5, 0xea, 0xbd, 0x57, 0x4d, 0xd0, 0x2d, 0xdf, 0x99, 0x10, 0x97, 0x91, 0xe0,
	0xd1, 0xb9, 0xf7, 0x64, 0x4e, 0x49, 0x40, 0x49, 0x70, 0xe9, 0x4c, 0xc8, 0x23, 0xcb, 0x77, 0x1e,
	0xfb, 0x81, 0xc7, 0x3c, 0xad, 0x12, 0xe1, 0x9c, 0x7b, 0xfa, 0x33, 0x28, 0x37, 0xdf, 0x5a, 0x81,
	0xdd, 0x75, 0xcf, 0x3c, 0x4d, 0x83, 0x12, 0x63, 0x8e, 0xdd, 0x50, 0x0e, 0x95, 0x87, 0x65, 0x03,
	0x7f, 0x6b, 0x3b, 0xb0, 0x76, 0x69, 0x4d, 0xe7, 0xa4, 0x51, 0x38, 0x54, 0x1e, 0x56, 0x0d, 0x31,
	0xd0, 0x07, 0x50, 0x41, 0x32, 0x83, 0x4c, 0xbc, 0x20, 0x86, 0xa4, 0xc4, 0x90, 0xb4, 0x3d, 0xd8,
	0xf0, 0x7c, 0x93, 0x39, 0x33, 0x41, 0x5c, 0x36, 0xd6, 0x3d, 0x7f, 0xe4, 0xcc, 0x08, 0xdf, 0xc7,
	0x26, 0x74, 0xd2, 0x28, 0x8a, 0x7d, 0xf8, 0x6f, 0xfd, 0xef, 0x15, 0xd8, 0xc6, 0x25, 0x47, 0xc7,
	0xc4, 0x72, 0x47, 0xde, 0x98, 0x92, 0xc0, 0x20, 0x5f, 0x6b, 0x87, 0xb0, 0xe9, 0xf9, 0x26, 0x97,
	0xc4, 0x8c, 0xf1, 0x06, 0x9e, 0xcf, 0x11, 0x46, 0x9c, 0xc3, 0x07, 0x50, 0x0b, 0x31, 0xfc, 0xb7,
	0xf6, 0xcc, 0x7e, 0x26, 0x77, 0xdb, 0x14, 0x38, 0x03, 0x84, 0x69, 0x77, 0xa1, 0xe2, 0x5b, 0x57,
	0x1c, 0xc3, 0xe4, 0x28, 0x62, 0xeb, 0xb2, 0x6f, 0x5d, 0x0d, 0xde, 0xda, 0x2f, 0xed, 0x67, 0xda,
	0x33, 0x00, 0x8b, 0x6f, 0x6f, 0x4e, 0x1d, 0xca, 0x1a, 0xa5, 0xc3, 0xe2, 0xc3, 0xca, 0xd3, 0x9b,
	0x8f, 0x63, 0xaa, 0x7a, 0x1c, 0xe9, 0xc9, 0x28, 0x23, 0xe6, 0x89, 0x43, 0x99, 0xfe, 0x43, 0xd8,
	0xc9, 0x72, 0x4d, 0x7d, 0xed, 0x3e, 0x6c, 0x9e, 0x59, 0xce, 0x94, 0xd8, 0xc8, 0x35, 0x6d, 0x28,
	0x87, 0xc5, 0x87, 0x65, 0xa3, 0x22, 0x60, 0x9c, 0x6d, 0xaa, 0xf7, 0x60, 0x17, 0x49, 0xdb, 0x8e,
	0x35, 0xf3, 0x5c, 0x7b, 0x21, 0x72, 0x92, 0x15, 0xe5, 0xba, 0xac, 0x7c, 0x06, 0x37, 0xf3, 0xd6,
	0xbb, 0x1e, 0x33, 0x17, 0xd0, 0x78, 0x4e, 0x58, 0x9c, 0x5e, 0x98, 0x96, 0xf3, 0x93, 0xe7, 0x16,
	0x07, 0x00, 0xaf, 0xc9, 0xb9, 0xe3, 0x2e, 0xcc, 0x5b, 0x35, 0xca, 0x08, 0x41, 0x0b, 0xef, 0x83,
	0x4a, 0x5c, 0x5b, 0x4c, 0x16, 0x71, 0x72, 0x83, 0xb8, 0x36, 0x9f, 0xd2, 0x5f, 0xc1, 0xfe, 0x92,
	0x9d, 0xa8, 0xaf, 0xfd, 0x10, 0x2a, 0x01, 0x8e, 0xe2, 0xb2, 0x37, 0xb2, 0xb2, 0x4b, 0x12, 0x10,
	0xc8, 0x28, 0xfe, 0x17, 0x70, 0x03, 0xa7, 0x3a, 0xef, 0xfc, 0x5f, 0x5a, 0x95, 0x9f, 0x80, 0x96,
	0x5e, 0xeb, 0x7a, 0x6a, 0xb4, 0x61, 0x27, 0x14, 0xae, 0xf3, 0xce, 0xff, 0x55, 0xa9, 0xd0, 0x80,
	0xdd, 0x9c, 0x5d, 0x7e, 0x39, 0xf5, 0xfd, 0x7b, 0x11, 0xaa, 0x5c, 0xd2, 0x13, 0xef, 0xdc, 0x71,
	0x31, 0x1a, 0x34, 0x60, 0xc3, 0x9a, 0x4c, 0xbc, 0xb9, 0xcb, 0x24, 0xdb, 0xe1, 0x50, 0xbb, 0x07,
	0x15, 0x9b, 0xf0, 0xa8, 0x62, 0x3a, 0xee, 0x99, 0x27, 0x8f, 0x1b, 0x08, 0x10, 0x92, 0xde, 0x86,
	0x72, 0x88, 0x60, 0xcb, 0xa3, 0xa6, 0xca, 0x69, 0x9b, 0xab, 0x51, 0x4e, 0xce, 0x3c, 0x9b, 0x4c,
	0x1b, 0x25, 0x9c, 0x97, 0x2b, 0xbe, 0xe4, 0x20, 0xbe, 0x81, 0x43, 0x4d, 0x32, 0x9b, 0x4f, 0x2d,
	0xe6, 0x05, 0x8d, 0x35, 0x14, 0x1f, 0x1c, 0xda, 0x91, 0x10, 0xae, 0xbb, 0xc9, 0xd4, 0x21, 0x2e,
	0x33, 0x2f, 0x49, 0xd0, 0x58, 0x17, 0x87, 0x59, 0x40, 0x5e, 0x91, 0x40, 0xbb, 0x05, 0xaa, 0x18,
	0x74, 0xfd, 0xc6, 0x86, 0xd8, 0x3e, 0x1c, 0xf3, 0xb5, 0x25, 0x29, 0xbb, 0xf2, 0x49, 0x43, 0x15,
	0x6b, 0x0b, 0xd0, 0xe8, 0xca, 0x27, 0xda, 0x2e, 0xac, 0x7b, 0x14, 0xd7, 0x2d, 0x23, 0xe9, 0x9a,
	0x47, 0xf9, 0x9a, 0x3c, 0x9a, 0x51, 0x41, 0x03, 0x32, 0x9a, 0x51, 0xc4, 0x3f, 0x00, 0x98, 0x7a,
	0x91, 0x1d, 0x2b, 0x82, 0x17, 0x84, 0xa0, 0x1d, 0x65, 0x14, 0xe4, 0x74, 0x9b, 0xb8, 0x17, 0x8f,
	0x82, 0x9c, 0xee, 0x26, 0xac, 0x07, 0x84, 0xce, 0xa7, 0xac, 0x51, 0x3d, 0x54, 0x1e, 0xae, 0x19,
	0x72, 0xc4, 0x99, 0x67, 0x24, 0x98, 0x39, 0xae, 0x35, 0x6d, 0xd4, 0x04, 0xf3, 0xe1, 0x98, 0x07,
	0x5a, 0xff, 0xc2, 0x73, 0x49, 0x63, 0x4b, 0xb0, 0x86, 0x03, 0xb1, 0x05, 0x71, 0xb9, 0xb2, 0xeb,
	0x61, 0xa0, 0x25, 0x6e, 0xd7, 0xd6, 0xff, 0x4c, 0x81, 0xed, 0xe7, 0x84, 0x25, 0xec, 0xca, 0xdd,
	0x71, 0xb9, 0x69, 0xff, 0xcf, 0x4e, 0xa9, 0x7d, 0x04, 0x5b, 0x2e, 0x21, 0xb6, 0x69, 0xcd, 0x99,
	0x67, 0xa2, 0xf8, 0x68, 0x59, 0xd5, 0xa8, 0x72, 0x70, 0x73, 0xce, 0x3c, 0x64, 0x41, 0xef, 0xe3,
	0x11, 0x49, 0xb1, 0x44, 0x7d, 0xed, 0x13, 0x28, 0x73, 0x6f, 0x8a, 0x7b, 0xee, 0xad, 0x84, 0xe7,
	0x26, 0x49, 0x54, 0x8e, 0x8c, 0x9e, 0xfb, 0x0f, 0x05, 0xd8, 0x3c, 0xb6, 0x5c, 0x97, 0x84, 0xb7,
	0xd1, 0x72, 0xe9, 0x52, 0x37, 0x52, 0x35, 0xba, 0x91, 0x62, 0x46, 0x2a, 0x26, 0x8c, 0xf4, 0x21,
	0x54, 0x3d, 0x9f, 0x04, 0xdc, 0xe9, 0x4c, 0xd7, 0x9a, 0x11, 0xe9, 0xad, 0x9b, 0x21, 0xb0, 0x67,
	0xcd, 0xa4, 0x25, 0x2d, 0xea, 0xb9, 0xe8, 0xa9, 0x65, 0x43, 0x8e, 0xf8, 0x31, 0xf0, 0x03, 0xcf,
	0x3b, 0x33, 0x7d, 0x67, 0x22, 0x9d, 0x54, 0x45, 0xc0, 0xc0, 0x99, 0x68, 0x0f, 0xa1, 0x8e, 0xaa,
	0xe2, 0x67, 0xf0, 0x92, 0x04, 0x57, 0xa6, 0xc5, 0xd0, 0x57, 0xab, 0x46, 0x8d, 0xc3, 0x0d, 0x09,
	0x6e, 0x32, 0xce, 0x83, 0x58, 0xd0, 0xb4, 0x09, 0xb3, 0x9c, 0x29, 0xfa, 0x6c, 0xd9, 0xd8, 0x14,
	0xc0, 0x36, 0xc2, 0xb4, 0x3a, 0x14, 0xe7, 0x8e, 0x8d, 0x2e, 0x5b, 0x35, 0xf8, 0x4f, 0xee, 0xe8,
	0xaf, 0x51, 0x2d, 0xa6, 0x6d, 0x5d, 0x51, 0x74, 0xda, 0x92, 0x01, 0x02, 0xd4, 0xb6, 0xae, 0xa8,
	0xfe, 0xdf, 0x0a, 0x68, 0xcf, 0x09, 0x8b, 0xeb, 0x6e, 0xb5, 0x73, 0x68, 0x50, 0xf2, 0xad, 0xf3,
	0x50, 0x77, 0xf8, 0x1b, 0x65, 0xb4, 0xce, 0x89, 0x49, 0x9d, 0x9f, 0x87, 0xba, 0x53, 0x39, 0x60,
	0xe8, 0xfc, 0x9c, 0x84, 0x4c, 0x95, 0x16, 0x4c, 0x25, 0xfd, 0x6b, 0x6d, 0x95, 0x7f, 0xad, 0x27,
	0xfd, 0x2b, 0x63, 0x89, 0x8d, 0x95, 0x96, 0x50, 0xe3, 0x96, 0xd0, 0x5d, 0x3c, 0x07, 0x49, 0x49,
	0xa9, 0xaf, 0xfd, 0x28, 0x2f, 0x5e, 0xee, 0x27, 0xbc, 0x2e, 0x41, 0x13, 0x0b, 0x98, 0x5c, 0x70,
	0xe6, 0x31, 0x6b, 0x6a, 0x4e, 0x5c, 0x26, 0x35, 0xa2, 0x22, 0xa0, 0xe5, 0x32, 0xfd, 0x26, 0x3a,
	0xb9, 0xa0, 0xed, 0x4b, 0x06, 0x0d, 0xf2, 0xb5, 0xfe, 0x0c, 0x6a, 0x49, 0x60, 0x56, 0x2c, 0x25,
	0x2b, 0x96, 0x7e, 0x8a, 0x01, 0x3f, 0xbd, 0x1c, 0xf5, 0xb5, 0x9f, 0xc4, 0xa8, 0x63, 0x22, 0xdc,
	0xce, 0x11, 0x21, 0xa2, 0x8b, 0x96, 0xc6, 0xd3, 0xf3, 0x7b, 0x45, 0xd0, 0x04, 0x42, 0xd3, 0xf7,
	0x89, 0x35, 0x95, 0x67, 0xa8, 0x06, 0x05, 0x79, 0x5d, 0x55, 0x8d, 0x82, 0x63, 0x87, 0x96, 0x2c,
	0x2c, 0x2c, 0x19, 0x5e, 0x69, 0xc5, 0x64, 0xb2, 0x48, 0x99, 0xc5, 0x88, 0xb4, 0xb8, 0x18, 0x70,
	0x47, 0x94, 0xfe, 0x3b, 0xf1, 0xec, 0xd0, 0xe8, 0x20, 0x40, 0x2d, 0xcf, 0xc6, 0xd0, 0x21, 0x3d,
	0x55, 0xf8, 0x86, 0xc5, 0xd0, 0xf8, 0x45, 0xa3, 0x2a, 0xc0, 0xc7, 0x1c, 0xda, 0x64, 0x9a, 0x0e,
	0x12, 0x60, 0x72, 0x27, 0x91, 0xe7, 0xa5, 0x68, 0x48, 0x37, 0xef, 0xb8, 0x76, 0x13, 0xcd, 0x32,
	0x09, 0x88, 0xc5, 0x08, 0x9f, 0x57, 0x71, 0x5e, 0x15, 0x00, 0x31, 0x39, 0xf7, 0x6d, 0x39, 0x59,
	0x16, 0x93, 0x02, 0xd0, 0xc4, 0x5b, 0x4d, 0x52, 0x62, 0x72, 0x2a, 0x82, 0x3c, 0x08, 0x50, 0x9b,
	0xd0, 0x09, 0x47, 0x90, 0xd4, 0x88, 0x20, 0x22, 0x3d, 0x08, 0x10, 0x22, 0xdc, 0x02, 0x35, 0xd4,
	0x2d, 0xc6, 0xfa, 0xb2, 0x11, 0x8d, 0xf9, 0xd6, 0x33, 0x2b, 0x78, 0x43, 0x18, 0x8f, 0xd2, 0x55,
	0xe1, 0x2e, 0x02, 0xd0, 0xb5, 0xf5, 0xbf, 0x28, 0x60, 0xfa, 0x95, 0xb5, 0x03, 0x3f, 0x8f, 0xfb,
	0xa0, 0xce, 0x9d, 0x98, 0x87, 0x56, 0x8d, 0x8d, 0xb9, 0xb3, 0xf0, 0x41, 0x16, 0xce, 0x15, 0x30,
	0x1d, 0x51, 0x39, 0x00, 0x27, 0x0f, 0x00, 0x50, 0xff, 0x62, 0xb6, 0x88, 0x94, 0x65, 0x84, 0xe0,
	0xf4, 0x43, 0xa8, 0xc7, 0xac, 0xb2, 0x48, 0x7b, 0xab, 0x46, 0x6d, 0x61, 0x1a, 0xc4, 0xfc, 0x08,
	0xb6, 0xa4, 0x62, 0x22, 0xf3, 0xac, 0x09, 0xf3, 0x08, 0x70, 0xcc, 0x3c, 0x12, 0x4f, 0x9a, 0x47,
	0x18, 0x51, 0x6a, 0x55, 0x98, 0xe7, 0x26, 0xac, 0x7b, 0x67, 0x67, 0x94, 0x84, 0xb1, 0x4e, 0x8e,
	0xb8, 0xe7, 0x4c, 0x9d, 0x99, 0xc3, 0xe4, 0x7d, 0x2c, 0x06, 0x3a, 0xc5, 0x5c, 0x31, 0x4f, 0x2d,
	0xe8, 0xfb, 0x39, 0x87, 0xf7, 0x5e, 0x8e, 0xe7, 0x27, 0x28, 0xe3, 0x47, 0x78, 0x07, 0xd6, 0xf0,
	0xc4, 0x86, 0xb5, 0x0d, 0x0e, 0xf4, 0xbf, 0x56, 0xe0, 0xf6, 0x18, 0x8d, 0x9a, 0x6f, 0x8f, 0xf7,
	0x1f, 0x8d, 0xe8, 0x18, 0x14, 0x53, 0xc7, 0x20, 0xee, 0x3e, 0xa5, 0x95, 0xee, 0xb3, 0xb6, 0xca,
	0x7d, 0xd6, 0x53, 0xee, 0x73, 0x17, 0xee, 0x2c, 0x67, 0x98, 0xfa, 0xfa, 0x2f, 0x4a, 0xb0, 0x17,
	0xe9, 0xb1, 0x75, 0x41, 0x26, 0x6f, 0x16, 0xd2, 0xb4, 0x22, 0x2d, 0xe2, 0xed, 0xc7, 0xc5, 0xaa,
	0x3d, 0xd5, 0x73, 0xb4, 0x18, 0xa3, 0xe3, 0x37, 0x63, 0xa8, 0xc8, 0x30, 0x95, 0x91, 0x56, 0x2d,
	0xe4, 0x5b, 0xb5, 0x18, 0xb3, 0x6a, 0xc2, 0xa1, 0x4b, 0x2b, 0x1c, 0x7a, 0x2d, 0xe5, 0xd0, 0x2d,
	0xa8, 0x8a, 0x3a, 0xd0, 0x3a, 0x17, 0x08, 0xeb, 0x87, 0xc5, 0x87, 0xb5, 0x5c, 0x93, 0x23, 0xb3,
	0x58, 0x21, 0x5a, 0xe7, 0x46, 0x65, 0x2e, 0x7e, 0xe0, 0x22, 0xbf, 0x0d, 0x15, 0xea, 0xcd, 0x83,
	0x89, 0xf4, 0xf8, 0x0d, 0x5c, 0xe2, 0xee, 0xb2, 0x25, 0x86, 0x88, 0x6a, 0x80, 0x20, 0xc1, 0x05,
	0x16, 0xd1, 0x0c, 0x17, 0x50, 0x91, 0x49, 0x19, 0xcd, 0x10, 0xe1, 0xd7, 0x41, 0x4b, 0x44, 0x74,
	0x81, 0x57, 0x46, 0xbc, 0x7a, 0x3c, 0xac, 0x87, 0x87, 0x2b, 0x1d, 0xfb, 0xe0, 0x5a, 0xb1, 0xaf,
	0x92, 0x8d, 0x7d, 0x9f, 0x40, 0x63, 0xc2, 0xb9, 0x36, 0x73, 0xf6, 0xdf, 0xc4, 0xfd, 0x77, 0x71,
	0xbe, 0x9f, 0x66, 0xe2, 0x01, 0xd4, 0x04, 0x61, 0xc4, 0x43, 0x15, 0x57, 0xdf, 0x44, 0x68, 0xc8,
	0xc2, 0x21, 0x88, 0x71, 0xc8, 0x41, 0x0d, 0x71, 0x00, 0x61, 0xc8, 0x80, 0xfe, 0x4f, 0x6b, 0x70,
	0x23, 0xe3, 0x2d, 0x3c, 0xb9, 0x62, 0x16, 0x7d, 0x63, 0x46, 0xa7, 0x66, 0x9d, 0x0f, 0xbb, 0xd7,
	0xbd, 0x54, 0x6e, 0x81, 0xea, 0x3a, 0x93, 0x37, 0xb1, 0xec, 0x2b, 0x1a, 0x2f, 0xf2, 0xe1, 0xb5,
	0x78, 0x3e, 0x7c, 0x1f, 0x36, 0x1d, 0x9b, 0xb8, 0xcc, 0x61, 0x57, 0xa6, 0x3b, 0x9f, 0xc9, 0xd4,
	0xab, 0x12, 0xc2, 0x7a, 0xf3, 0x99, 0xf6, 0x23, 0x50, 0x43, 0x5f, 0xc2, 0x48, 0x74, 0x0d, 0x37,
	0xda, 0x90, 0x6e, 0xb4, 0x2c, 0xc9, 0xc8, 0xe6, 0x69, 0xe5, 0x9c, 0x3c, 0x2d, 0x91, 0x13, 0x42,
	0x2a, 0x27, 0x4c, 0xa5, 0x6c, 0x95, 0x74, 0xca, 0xa6, 0xfd, 0x00, 0xd6, 0x85, 0x2b, 0xe2, 0xfd,
	0xf2, 0x7e, 0xc7, 0x95, 0xd8, 0x7c, 0x57, 0xb9, 0x70, 0x64, 0x5b, 0x55, 0x00, 0x44, 0x7e, 0x99,
	0x4c, 0x41, 0x6a, 0x39, 0x99, 0x55, 0x2c, 0x43, 0xde, 0x4a, 0x64, 0xc8, 0xfb, 0xa0, 0x0a, 0xaf,
	0xb0, 0x18, 0x56, 0x1f, 0x45, 0x63, 0x03, 0xc7, 0x4d, 0xa6, 0x3d, 0x86, 0xed, 0x1c, 0x7f, 0x6c,
	0xdc, 0xc0, 0xe5, 0x6f, 0x64, 0x5c, 0x51, 0xfb, 0x0e, 0xd4, 0x64, 0x36, 0x6c, 0x4a, 0x05, 0x6b,
	0x88, 0x5a, 0x95, 0x50, 0x43, 0xe8, 0xf9, 0x7e, 0xe8, 0x87, 0xb2, 0x7c, 0xda, 0x16, 0xe6, 0x9d,
	0x08, 0x97, 0xc3, 0x1a, 0xea, 0x33, 0x10, 0x6e, 0x29, 0x18, 0xde, 0x41, 0x5d, 0xdd, 0x59, 0xa6,
	0x2b, 0x0c, 0x67, 0xe5, 0x49, 0xf8, 0x53, 0x7f, 0x17, 0xbb, 0x8c, 0x13, 0xd1, 0x92, 0xfa, 0x3c,
	0x7c, 0x64, 0x2f, 0x9d, 0xbb, 0xab, 0xc3, 0xe5, 0xf5, 0xd3, 0xc6, 0xbf, 0x2d, 0xc2, 0xad, 0x78,
	0x24, 0x4f, 0xc5, 0xea, 0x61, 0x74, 0x83, 0xc4, 0x62, 0xf5, 0xd3, 0x64, 0x91, 0xb4, 0x94, 0x5a,
	0x4e, 0x89, 0xd8, 0x3d, 0x8f, 0x7e, 0xf3, 0x53, 0x2d, 0x4f, 0xe7, 0x22, 0x8d, 0xa8, 0x1a, 0x20,
	0x8e, 0x28, 0xb2, 0x9c, 0xf1, 0x8f, 0xe2, 0xca, 0xcc, 0xbb, 0x94, 0x38, 0x14, 0x47, 0x70, 0xc3,
	0xa1, 0x61, 0x91, 0x63, 0x8a, 0x22, 0x1f, 0x4f, 0xab, 0x6a, 0x6c, 0x39, 0x54, 0x56, 0x39, 0x6d,
	0x04, 0xeb, 0x7f, 0xa7, 0x00, 0x2c, 0xb8, 0xd4, 0x6e, 0xc3, 0xde, 0x78, 0xd0, 0x6e, 0x8e, 0x3a,
	0xe6, 0xe8, 0x74, 0xd0, 0x31, 0xc7, 0xbd, 0xe1, 0xa0, 0xd3, 0xea, 0x7e, 0xde, 0xed, 0xb4, 0xeb,
	0x1f, 0x68, 0x7b, 0xb0, 0x1d, 0x9f, 0x34, 0x3a, 0xad, 0xfe, 0xab, 0x8e, 0x51, 0x57, 0xd2, 0x54,
	0x5f, 0x76, 0x3a, 0x03, 0xf3, 0xb8, 0xd9, 0xeb, 0x75, 0xda, 0xf5, 0x82, 0x76, 0x17, 0x6e, 0x25,
	0xa9, 0xbe, 0xe8, 0xb4, 0x46, 0x11, 0x71, 0x51, 0x6b, 0xc0, 0x4e, 0x7c, 0xbe, 0xf5, 0xa2, 0xd3,
	0xfa, 0xb2, 0xdb, 0x7b, 0x5e, 0x2f, 0xa5, 0x67, 0x46, 0x7d, 0x31, 0x59, 0x5f, 0xd3, 0x0f, 0x92,
	0xe9, 0x42, 0xca, 0x63, 0xf4, 0xdb, 0xb1, 0x1c, 0xa6, 0x15, 0x77, 0x79, 0x5e, 0x0f, 0xfc, 0x0c,
	0x6e, 0x2d, 0x9b, 0xfc, 0x56, 0xb2, 0x7b, 0x03, 0xb6, 0xa2, 0xb2, 0x59, 0x28, 0x39, 0xd9, 0x9b,
	0x51, 0x52, 0xbd, 0x19, 0x1d, 0xaa, 0x53, 0x8b, 0x32, 0x53, 0x36, 0x34, 0xa8, 0xf4, 0xd0, 0x0a,
	0x07, 0xe2, 0x22, 0x23, 0xaa, 0x7f, 0x1f, 0x8b, 0x91, 0xd4, 0xb2, 0x2b, 0x0b, 0x47, 0x7d, 0x08,
	0x37, 0xf3, 0x48, 0xb0, 0x63, 0x95, 0xa9, 0xfa, 0xef, 0xe4, 0x57, 0xfd, 0x92, 0x68, 0x51, 0xf7,
	0x0f, 0x61, 0x3b, 0x9a, 0xfc, 0xa9, 0xc3, 0x2e, 0xa4, 0x7c, 0xcb, 0xcb, 0xd7, 0xeb, 0x08, 0xf7,
	0x29, 0x5a, 0x2b, 0x67, 0x5d, 0x2e, 0xe0, 0x2a, 0xd5, 0xe9, 0xbf, 0x8b, 0xa6, 0xcc, 0xa5, 0xa4,
	0xbe, 0xf6, 0xe3, 0xac, 0x9c, 0x87, 0xf9, 0x72, 0xc6, 0x08, 0x17, 0xb2, 0xfe, 0x73, 0x01, 0xe0,
	0xd8, 0x72, 0xc3, 0xb6, 0xe6, 0x72, 0x19, 0x17, 0xc7, 0xb0, 0x90, 0xbe, 0x9b, 0xde, 0x7f, 0x86,
	0xf3, 0x5a, 0x12, 0x25, 0x0c, 0xe9, 0xe9, 0x96, 0xc4, 0x3d, 0xa8, 0xbc, 0x75, 0xd8, 0x45, 0xf2,
	0x3c, 0xc3, 0xdb, 0x85, 0x15, 0x56, 0xb6, 0x3e, 0x32, 0x17, 0xe5, 0x46, 0xce, 0x45, 0x99, 0xba,
	0x0b, 0xd5, 0xcc, 0x5d, 0x78, 0x08, 0x9b, 0x01, 0x61, 0xfc, 0x12, 0xf0, 0x4d, 0x12, 0x88, 0x6e,
	0x9d, 0xca, 0x63, 0x2d, 0xe3, 0x1a, 0xef, 0x04, 0x41, 0x98, 0x5f, 0x40, 0x94, 0x5f, 0xe8, 0x4d,
	0xa8, 0x44, 0x6a, 0xa4, 0x3e, 0xb6, 0x1b, 0x82, 0x40, 0x94, 0xa5, 0x0a, 0x36, 0xe1, 0x36, 0x48,
	0x10, 0x60, 0x4d, 0xba, 0x07, 0xfc, 0xa7, 0x39, 0xa3, 0xe7, 0xa1, 0x26, 0x49, 0x10, 0xbc, 0xa4,
	0xe7, 0xfa, 0x77, 0x61, 0xbb, 0x65, 0xb9, 0x89, 0xc0, 0xc5, 0x4d, 0x22, 0xf7, 0x52, 0x16, 0x7b,
	0x75, 0x61, 0x27, 0x8b, 0x48, 0xfd, 0x2c, 0x26, 0x56, 0x9e, 0x96, 0x1b, 0xaa, 0x1d, 0xf7, 0x53,
	0x0d, 0x98, 0x44, 0xc4, 0xfa, 0x9f, 0x2b, 0x50, 0x93, 0xbf, 0x7f, 0xc5, 0x2e, 0x90, 0x1b, 0xae,
	0x4b, 0xf9, 0xe1, 0xfa, 0x06, 0x6c, 0x25, 0x98, 0xa2, 0xbe, 0x7e, 0x9e, 0x00, 0x61, 0x2f, 0x38,
	0x2b, 0xee, 0x32, 0x06, 0xff, 0x37, 0x7b, 0xff, 0xa1, 0x02, 0xdb, 0xc7, 0x16, 0x9b, 0x5c, 0xa4,
	0xd4, 0xf2, 0x3d, 0x28, 0x2d, 0x0d, 0x25, 0x29, 0xce, 0x0c, 0xc4, 0xcc, 0xaa, 0xa5, 0x90, 0xa3,
	0x96, 0x7d, 0x50, 0x1d, 0x6a, 0x62, 0x8a, 0x80, 0x6a, 0x53, 0x8d, 0x0d, 0x87, 0x62, 0xc0, 0xd6,
	0x3f, 0x86, 0x9d, 0x2c, 0x23, 0xd4, 0xe7, 0x27, 0xe0, 0xd2, 0x9a, 0x3a, 0x36, 0x66, 0xa0, 0x42,
	0x7a, 0x15, 0x01, 0xbd, 0xf9, 0x4c, 0xff, 0x31, 0x16, 0x64, 0x82, 0x93, 0x4b, 0x87, 0x91, 0x17,
	0x0e, 0x65, 0x5e, 0x70, 0x95, 0xeb, 0x48, 0x1c, 0xb2, 0x48, 0x16, 0xf8, 0x4f, 0xfd, 0x33, 0xcc,
	0x50, 0x72, 0xc8, 0x29, 0xf6, 0xb7, 0x1d, 0x04, 0x2e, 0x62, 0x4d, 0xd9, 0x00, 0x01, 0xc2, 0x58,
	0xf2, 0x06, 0x76, 0x0c, 0x42, 0x05, 0xf9, 0xc0, 0xa2, 0xf4, 0xed, 0x7b, 0xfb, 0x7e, 0x51, 0x96,
	0x5d, 0x88, 0x67, 0xd9, 0xd7, 0xf1, 0x27, 0x7d, 0x0f, 0x76, 0x73, 0x36, 0xa3, 0xbe, 0x3e, 0x04,
	0x6d, 0xec, 0xbe, 0x76, 0x5c, 0x1b, 0x67, 0xf8, 0x82, 0xab, 0x79, 0xb8, 0x8e, 0x99, 0xf4, 0x5d,
	0xd8, 0xce, 0x2c, 0x4a, 0x7d, 0xfd, 0x14, 0x1a, 0xad, 0x29, 0xb1, 0x82, 0x21, 0x99, 0xcc, 0x03,
	0x87, 0x5d, 0x7d, 0x35, 0x27, 0x94, 0x39, 0x9e, 0xfb, 0x2d, 0xec, 0x78, 0x1b, 0xf6, 0x97, 0x2c,
	0x2d, 0x64, 0xe4, 0xc1, 0x6c, 0x72, 0x31, 0xba, 0x70, 0x02, 0xdb, 0xb7, 0x02, 0xf6, 0xed, 0xc8,
	0x98, 0x59, 0x94, 0xfa, 0xfa, 0x7f, 0x28, 0x50, 0xef, 0x9f, 0x9d, 0x39, 0x13, 0xc7, 0x9a, 0xb6,
	0x48, 0xc0, 0x96, 0x9c, 0xbd, 0x1d, 0x58, 0x63, 0x0e, 0x9b, 0x46, 0xa6, 0xc4, 0x01, 0x87, 0x3a,
	0x2e, 0x0b, 0x3c, 0x69, 0x42, 0x31, 0x10, 0x6d, 0x8c, 0xab, 0x69, 0x58, 0x75, 0x89, 0x81, 0x6c,
	0x7f, 0xac, 0x45, 0xed, 0x8f, 0x7d, 0x50, 0x65, 0x47, 0x97, 0x62, 0xa0, 0x2f, 0x19, 0x1b, 0xa2,
	0x9f, 0x4b, 0xb5, 0x5d, 0x58, 0xc7, 0x6e, 0x2e, 0xc5, 0x00, 0x5f, 0x32, 0xd6, 0x88, 0x6b, 0x0b,
	0xb0, 0x43, 0xcd, 0x39, 0x15, 0x8f, 0x2f, 0xaa, 0xb1, 0xe6, 0xd0, 0x31, 0x25, 0xda, 0x1d, 0x28,
	0x5b, 0x8c, 0x05, 0xce, 0xeb, 0x39, 0x23, 0xb2, 0x8f, 0xbd, 0x00, 0xe8, 0x53, 0xd8, 0xe1, 0x46,
	0xcd, 0x88, 0xc8, 0x6b, 0x3d, 0x6a, 0x4e, 0x48, 0xc0, 0x9c, 0x33, 0x87, 0x08, 0x59, 0x55, 0xa3,
	0xe2, 0xd0, 0x56, 0x08, 0xd2, 0xbe, 0x0f, 0x25, 0x3e, 0x8f, 0x22, 0x57, 0x9e, 0x1e, 0x24, 0x62,
	0x42, 0x7a, 0x3d, 0x03, 0x51, 0xf5, 0x4f, 0xe1, 0x36, 0x1e, 0x6a, 0x79, 0xca, 0xe2, 0x58, 0xab,
	0x5b, 0x72, 0xfa, 0xcf, 0xe0, 0xce, 0x72, 0x4a, 0xea, 0x6b, 0xbf, 0x95, 0x4d, 0x04, 0xee, 0x67,
	0x12, 0x81, 0x0c, 0x57, 0x8b, 0x4c, 0xe0, 0x3e, 0x54, 0xe5, 0xd2, 0xf8, 0xf4, 0x97, 0x77, 0xf1,
	0x7c, 0x0a, 0xb5, 0x38, 0x8a, 0xb8, 0x72, 0xc8, 0x3b, 0x5f, 0x5e, 0x71, 0xfc, 0x27, 0x76, 0x66,
	0xc8, 0x25, 0x89, 0x5a, 0x5f, 0x38, 0xd0, 0xff, 0x51, 0x11, 0xf9, 0x62, 0xcb, 0xe3, 0x0e, 0x30,
	0x5d, 0xe2, 0x43, 0x61, 0x91, 0x5e, 0x58, 0x52, 0xa4, 0x17, 0x53, 0x45, 0x3a, 0x76, 0x4c, 0xce,
	0x1d, 0xca, 0x78, 0xbd, 0x4d, 0x65, 0x6f, 0x18, 0x42, 0xd0, 0x48, 0xdc, 0xe4, 0xce, 0xe4, 0xc2,
	0x9c, 0x5e, 0x0a, 0xb7, 0x17, 0xc5, 0x3c, 0x70, 0xd8, 0xc9, 0x25, 0xc6, 0xdf, 0x0f, 0xa1, 0xca,
	0xad, 0x2c, 0xd8, 0x9a, 0x12, 0xd1, 0x02, 0x53, 0x8d, 0x4d, 0x87, 0xb6, 0x22, 0x98, 0xfe, 0x37,
	0x4a, 0x94, 0x99, 0xc6, 0x04, 0xe0, 0x3a, 0x3a, 0x00, 0xf8, 0x7a, 0xce, 0x13, 0x9a, 0xa8, 0x6e,
	0xaa, 0x1a, 0x65, 0x84, 0x84, 0x25, 0xec, 0x3c, 0x59, 0xfe, 0x44, 0x0d, 0xa9, 0xf0, 0xc9, 0xa3,
	0xb8, 0xec, 0xc9, 0xa3, 0x94, 0x7c, 0xf2, 0xd0, 0x1f, 0x41, 0xf9, 0xab, 0x68, 0xe1, 0x2a, 0x94,
	0xbf, 0x1a, 0x77, 0x8c, 0x53, 0xb3, 0x79, 0x72, 0x52, 0xff, 0x40, 0xbb, 0x01, 0x55, 0x31, 0x3c,
	0x3e, 0x35, 0xc7, 0xdd, 0xf6, 0xb0, 0xae, 0xe8, 0x7f, 0xa2, 0x44, 0xa9, 0x71, 0x82, 0xe7, 0xeb,
	0xa6, 0xc6, 0x71, 0xa2, 0xc8, 0x49, 0x38, 0x87, 0x2e, 0x79, 0xc7, 0xcc, 0xd8, 0x6b, 0x8d, 0xca,
	0x01, 0x03, 0xc9, 0xfe, 0xa2, 0x02, 0x2d, 0xa6, 0x2a, 0xd0, 0x4f, 0xe0, 0xd6, 0xb1, 0xc5, 0x0c,
	0x32, 0xf3, 0x2e, 0x49, 0x6c, 0xfd, 0x29, 0x79, 0x4f, 0x2b, 0x9a, 0x57, 0x41, 0x4b, 0x09, 0xa9,
	0xaf, 0x7f, 0x1f, 0x0e, 0x9e, 0x13, 0x36, 0xba, 0x20, 0x43, 0x6b, 0x46, 0x0c, 0x62, 0x4d, 0xb9,
	0x59, 0x31, 0xe7, 0x75, 0x28, 0xcb, 0x77, 0xe3, 0xdf, 0x80, 0xbb, 0xab, 0x48, 0xa8, 0xcf, 0x8d,
	0x33, 0x0f, 0x1f, 0xe2, 0xab, 0x06, 0xfe, 0xd6, 0x9f, 0x43, 0xad, 0x39, 0x67, 0x17, 0x5d, 0xbb,
	0x15, 0xfb, 0xaa, 0x25, 0xf6, 0xb0, 0x82, 0xbf, 0x33, 0x1d, 0xa2, 0x42, 0xa6, 0x43, 0xa4, 0x9f,
	0xc0, 0x26, 0x5f, 0x88, 0xdb, 0x31, 0x7c, 0xd3, 0xb6, 0xe6, 0xec, 0x22, 0xee, 0x42, 0xaa, 0x25,
	0x11, 0xb8, 0x8b, 0xe3, 0x24, 0x65, 0xd6, 0x3c, 0x2a, 0x2c, 0x80, 0x83, 0x86, 0x08, 0xd1, 0xff,
	0x58, 0x81, 0x3b, 0xd2, 0xce, 0xa1, 0x28, 0xc8, 0xa6, 0x7b, 0xe6, 0xbd, 0x7a, 0x9a, 0x7f, 0xed,
	0xff, 0x1a, 0x68, 0x0e, 0x35, 0xc3, 0x37, 0xd5, 0x0b, 0x73, 0x71, 0x05, 0x63, 0x3e, 0xd4, 0x13,
	0xaf, 0xaa, 0x17, 0x78, 0xcd, 0x69, 0x8f, 0x60, 0x3b, 0x44, 0x76, 0xec, 0x89, 0x15, 0xd8, 0xe2,
	0x69, 0x5e, 0xe4, 0x2a, 0x75, 0x81, 0xdd, 0xc5, 0x09, 0xbe, 0xa5, 0xfe, 0x9f, 0x0a, 0xda, 0x63,
	0x19, 0x3b, 0xd4, 0xd7, 0x7e, 0x20, 0xc5, 0x5d, 0xfa, 0x30, 0x16, 0x57, 0x8e, 0xd0, 0x04, 0xba,
	0xde, 0x6f, 0x42, 0x25, 0xce, 0x80, 0x88, 0xb9, 0xb7, 0x33, 0x94, 0x0b, 0xfb, 0x18, 0xe0, 0x44,
	0x7c, 0xf1, 0x83, 0x1a, 0x93, 0x55, 0x7e, 0xa4, 0x63, 0x45, 0x52, 0x8a, 0x34, 0xcc, 0xb2, 0xe7,
	0x53, 0x26, 0x13, 0xc3, 0x0d, 0x87, 0x36, 0xf9, 0x90, 0xeb, 0x8f, 0x3b, 0xbb, 0xb8, 0x97, 0xf8,
	0x4f, 0xfd, 0x63, 0xb8, 0x17, 0x8f, 0xc4, 0x69, 0x39, 0x13, 0x4a, 0x2f, 0x86, 0x4e, 0x37, 0x11,
	0xd7, 0x4c, 0x1a, 0x39, 0xc7, 0x3c, 0x3c, 0x24, 0x85, 0x2e, 0xc4, 0xf9, 0x0f, 0x2f, 0xeb, 0x10,
	0xc8, 0xe5, 0x5b, 0x64, 0x4e, 0xc5, 0x58, 0xe6, 0xa4, 0x9f, 0xc2, 0xe1, 0x6a, 0xce, 0xa8, 0xaf,
	0x3d, 0x4b, 0x24, 0xb2, 0xd9, 0x2b, 0x22, 0x43, 0x84, 0xe8, 0xfa, 0x2f, 0x14, 0xd8, 0xe3, 0xd3,
	0x22, 0x4d, 0x3e, 0x09, 0x2b, 0xdb, 0x25, 0x32, 0x34, 0x60, 0xe3, 0x92, 0x04, 0xd4, 0x89, 0x52,
	0xf1, 0x70, 0xc8, 0xe3, 0xb9, 0x1f, 0x78, 0x97, 0x8e, 0x3b, 0x89, 0xe2, 0x79, 0x38, 0xe6, 0x07,
	0x6a, 0xe2, 0xb0, 0x2b, 0x99, 0x16, 0xe0, 0x6f, 0xcc, 0x0a, 0x7c, 0x19, 0xb8, 0x0b, 0x8e, 0xaf,
	0x7d, 0x04, 0x5b, 0xb1, 0x5a, 0xdb, 0xb6, 0x18, 0x09, 0x9f, 0xf4, 0xa2, 0x6a, 0xbb, 0x6d, 0xb1,
	0x54, 0x37, 0x62, 0x23, 0xd5, 0x8d, 0xe0, 0x4c, 0x4c, 0x2d, 0x76, 0xe6, 0x05, 0x33, 0xd9, 0x6a,
	0x8d, 0xc6, 0xfa, 0x1f, 0x29, 0x00, 0xed, 0x25, 0x5f, 0x9c, 0xa4, 0xbb, 0x1a, 0x07, 0xc0, 0xeb,
	0x46, 0x7e, 0x38, 0xd9, 0x9c, 0x46, 0x1f, 0x35, 0x58, 0xee, 0x10, 0x01, 0x5a, 0x13, 0xca, 0xd8,
	0x0b, 0x96, 0x27, 0x86, 0xeb, 0xfb, 0x41, 0x46, 0xdf, 0x39, 0x0a, 0x35, 0xb0, 0x85, 0x8c, 0xe7,
	0xe9, 0x0b, 0x6c, 0x1b, 0xe4, 0xe1, 0xe4, 0x1e, 0xed, 0x04, 0xb7, 0x85, 0x54, 0x23, 0x61, 0x8a,
	0x8d, 0x84, 0xdc, 0xb5, 0xa8, 0xaf, 0x7d, 0x1a, 0x7d, 0x7b, 0x13, 0x73, 0x8f, 0xbd, 0x04, 0xbb,
	0x0b, 0xb5, 0x84, 0x1f, 0xe5, 0x84, 0xaf, 0x5d, 0x22, 0xeb, 0x94, 0x57, 0xbe, 0x68, 0xcd, 0xfc,
	0x8b, 0x02, 0x9b, 0x82, 0xe0, 0xd8, 0x72, 0x4f, 0xbc, 0xf3, 0xd5, 0x9a, 0x7c, 0xb4, 0xe8, 0x02,
	0x17, 0xb0, 0xfb, 0xb8, 0x93, 0xee, 0x45, 0x61, 0x7f, 0x31, 0xec, 0x0d, 0xd7, 0xa0, 0x60, 0x89,
	0x3b, 0xa6, 0x68, 0x14, 0x2c, 0x7c, 0xf9, 0xe1, 0x86, 0xc0, 0x82, 0x5e, 0x78, 0xcf, 0xc6, 0x6b,
	0xcb, 0x0d, 0xab, 0x79, 0x3e, 0x35, 0x8f, 0x3f, 0xfe, 0x54, 0xb1, 0xde, 0x1f, 0xcb, 0xab, 0xf8,
	0x9e, 0x68, 0xbd, 0x86, 0x8d, 0x09, 0xe1, 0x4e, 0x10, 0x44, 0x4d, 0x09, 0x7d, 0x0c, 0x9b, 0xa2,
	0x99, 0x76, 0xe2, 0x9d, 0xbf, 0xaf, 0x5d, 0xb3, 0x78, 0x9a, 0x2a, 0xc4, 0x9f, 0xa6, 0x72, 0xae,
	0x7b, 0xfd, 0xf7, 0x15, 0x4c, 0xb9, 0xc2, 0x75, 0xa9, 0xaf, 0x3d, 0x05, 0xce, 0x36, 0xf7, 0xed,
	0xdc, 0xc8, 0x18, 0x57, 0xa7, 0xb1, 0xfe, 0x5a, 0xa8, 0xf5, 0xda, 0xfb, 0x2d, 0xec, 0x54, 0x8a,
	0xdb, 0xe9, 0x27, 0xe2, 0xf3, 0xac, 0xe3, 0xc8, 0x6b, 0xb3, 0x5e, 0xb5, 0xda, 0xcd, 0xf5, 0xef,
	0x45, 0xdf, 0x02, 0x45, 0x8b, 0xbc, 0xe7, 0x4e, 0x1f, 0x46, 0x9f, 0xea, 0xc4, 0x28, 0xa8, 0xaf,
	0x7d, 0x06, 0xf8, 0xa4, 0x16, 0xee, 0xb4, 0xec, 0x63, 0x9d, 0x05, 0x11, 0x70, 0x74, 0xc9, 0xc6,
	0xbf, 0x16, 0x60, 0x1f, 0xa3, 0x9f, 0x6c, 0xc4, 0x5c, 0xbf, 0xc5, 0xb6, 0x2a, 0x4f, 0x7b, 0x94,
	0xfc, 0x80, 0xe7, 0x7d, 0x8e, 0xb9, 0xac, 0x5b, 0x9d, 0xd7, 0x01, 0x5b, 0xcb, 0xed, 0x80, 0xfd,
	0x3f, 0x34, 0xb8, 0x32, 0xe5, 0x60, 0x39, 0xa7, 0x1c, 0xbc, 0x83, 0x09, 0x5b, 0xae, 0x36, 0xa9,
	0x7f, 0xf4, 0x5f, 0x0a, 0x94, 0x8f, 0x2d, 0xb7, 0x2f, 0xa4, 0xd6, 0xa0, 0x76, 0xdc, 0xec, 0x99,
	0x7d, 0x6c, 0x88, 0x9b, 0xbd, 0x2e, 0xcf, 0x49, 0xb7, 0x61, 0x2b, 0x06, 0x1b, 0x0f, 0xb1, 0x87,
	0xbe, 0x0b, 0x37, 0x62, 0xc0, 0x76, 0xe7, 0x55, 0xb7, 0xd5, 0xa9, 0x17, 0xb4, 0x06, 0xec, 0xc4,
	0xc0, 0xad, 0x93, 0x6e, 0xa7, 0x37, 0x32, 0xbb, 0x83, 0x7a, 0x49, 0xdb, 0x81, 0x7a, 0x6c, 0x66,
	0xf0, 0xa2, 0xdf, 0xeb, 0xd4, 0x55, 0xed, 0x66, 0xb4, 0xcc, 0xb8, 0x17, 0xad, 0xfe, 0x0d, 0x5f,
	0x67, 0x3b, 0x01, 0x97, 0x1b, 0x7c, 0x53, 0xd2, 0x6e, 0xc3, 0xcd, 0xc4, 0xcc, 0x62, 0x8f, 0x6f,
	0x54, 0x6d, 0x0f, 0xb4, 0xc4, 0xa4, 0xd8, 0xe6, 0x9b, 0xfa, 0xd1, 0x5f, 0x29, 0xe1, 0xb3, 0xa3,
	0x78, 0xfa, 0x1e, 0xe2, 0x7b, 0xfb, 0x1d, 0x68, 0x88, 0xbe, 0xbf, 0xd9, 0x1c, 0x0c, 0x3a, 0xcd,
	0x13, 0x73, 0x38, 0x6a, 0x8e, 0x3a, 0x66, 0x8f, 0x13, 0x7d, 0xb0, 0x6c, 0x76, 0xd0, 0xe9, 0xb5,
	0xeb, 0xca, 0xd2, 0xd9, 0xe6, 0x70, 0x58, 0x2f, 0x2c, 0x9b, 0x6d, 0x77, 0x7a, 0xa7, 0xf5, 0x92,
	0x76, 0x00, 0xfb, 0x79, 0xb3, 0xc7, 0x27, 0xcd, 0xd6, 0x97, 0xf5, 0xb5, 0xa3, 0xbf, 0x54, 0xa0,
	0x1e, 0x7e, 0x54, 0x14, 0x7d, 0x01, 0xb3, 0xa0, 0x31, 0x3a, 0xcd, 0x61, 0xbf, 0x67, 0xb6, 0xfa,
	0x6d, 0xce, 0xaa, 0xf1, 0xb2, 0xc9, 0x8d, 0x74, 0x0b, 0xd5, 0x92, 0x9e, 0x3e, 0x6e, 0xf6, 0xea,
	0x8a, 0xf6, 0x11, 0xe8, 0x39, 0x73, 0xcd, 0x56, 0xab, 0x3f, 0xee, 0xe1, 0xd3, 0xc6, 0x69, 0xeb,
	0x84, 0x1b, 0xef, 0x01, 0x1c, 0xae, 0xc0, 0x13, 0xdc, 0x95, 0x8e, 0xfe, 0x40, 0x09, 0x3f, 0x07,
	0x8a, 0xbf, 0x7d, 0x6a, 0xdf, 0x81, 0xfb, 0x92, 0x18, 0x5f, 0x3d, 0xd0, 0x92, 0xe6, 0xa8, 0xf9,
	0x3c, 0xf5, 0x28, 0xa3, 0xc3, 0xdd, 0x7c, 0xb4, 0x17, 0xdd, 0xe7, 0x2f, 0xcc, 0x41, 0xf3, 0xb4,
	0xae, 0xc4, 0xf8, 0x48, 0xe1, 0x0c, 0x8c, 0xfe, 0xe7, 0x9d, 0xe1, 0xb0, 0xdb, 0xef, 0xd5, 0x0b,
	0x47, 0x57, 0x89, 0x87, 0x64, 0xf1, 0x9a, 0xa9, 0x7d, 0x08, 0xf7, 0x12, 0xa4, 0xc3, 0xfe, 0xd8,
	0x68, 0xa5, 0x1f, 0x86, 0x16, 0xaa, 0x4c, 0x20, 0x35, 0xc7, 0xed, 0xee, 0xa8, 0xae, 0x68, 0x87,
	0x70, 0x27, 0x6f, 0x7a, 0x38, 0x32, 0x9a, 0xa3, 0xce, 0xf3, 0xd3, 0x7a, 0xe1, 0xe8, 0x4f, 0x15,
	0xd8, 0xcd, 0xfd, 0xe4, 0x41, 0x7b, 0x08, 0x0f, 0x12, 0xb4, 0x46, 0xa7, 0xd5, 0x37, 0xda, 0x79,
	0xaf, 0x53, 0x69, 0x7d, 0xc5, 0x31, 0xa3, 0xa7, 0xa3, 0xac, 0x2e, 0xe2, 0x68, 0x08, 0xe9, 0xb4,
	0xeb, 0x85, 0xa3, 0x7f, 0x53, 0x60, 0x2b, 0xf5, 0x5c, 0xa9, 0xdd, 0x87, 0x83, 0x04, 0x65, 0x0e,
	0x0f, 0xb9, 0x28, 0xf1, 0xe7, 0x30, 0x45, 0xfb, 0x2e, 0x7c, 0x98, 0xb7, 0x8a, 0x84, 0x1d, 0x9f,
	0x4a, 0x46, 0x0b, 0xef, 0x47, 0xec, 0x8f, 0x5e, 0xe0, 0x03, 0x5a, 0xda, 0x03, 0xe2, 0xcf, 0x6c,
	0x88, 0x5f, 0x2f, 0x1d, 0x0d, 0x40, 0x0d, 0x8b, 0x07, 0xad, 0x0e, 0x9b, 0xcd, 0xf1, 0xe8, 0x85,
	0xd9, 0xed, 0xbd, 0x6a, 0x9e, 0x74, 0x39, 0xdb, 0xfb, 0xb0, 0xdb, 0xe9, 0x8d, 0x5f, 0x9a, 0x08,
	0x46, 0xf2, 0x6e, 0xbb, 0xd5, 0x34, 0x38, 0xbb, 0x7b, 0xb0, 0x9d, 0x9a, 0xfa, 0xbc, 0xc9, 0x03,
	0xd3, 0xd1, 0x5b, 0xa8, 0x74, 0x9a, 0xa2, 0xd8, 0xe2, 0x57, 0xa3, 0x06, 0x35, 0x7e, 0xe6, 0xc6,
	0xc3, 0xd8, 0xb2, 0x1a, 0xd4, 0x16, 0xb4, 0x18, 0x03, 0x14, 0x1e, 0xfb, 0x16, 0xb0, 0x50, 0xd4,
	0x04, 0x22, 0x1e, 0xf8, 0x62, 0x72, 0xe3, 0x71, 0xaf, 0x39, 0x18, 0x18, 0xfd, 0x57, 0x9d, 0x7a,
	0xe9, 0xe8, 0x63, 0xd8, 0x90, 0x57, 0x8b, 0x56, 0x03, 0xc0, 0xc8, 0xf4, 0x65, 0xaf, 0xff, 0xd3,
	0x5e, 0xfd, 0x03, 0x0d, 0x60, 0x5d, 0x84, 0xc3, 0xba, 0xa2, 0x6d, 0x82, 0x1a, 0x46, 0xad, 0x7a,
	0xe1, 0xf8, 0xc9, 0xef, 0x3c, 0x3a, 0xf7, 0xa6, 0x96, 0x7b, 0xfe, 0xf8, 0xd9, 0x53, 0xc6, 0x1e,
	0x4f, 0xbc, 0xd9, 0x13, 0xfc, 0x67, 0xc6, 0xc4, 0x9b, 0x3e, 0x91, 0x7f, 0xd7, 0xa0, 0x4f, 0x62,
	0xb7, 0xd7, 0xeb, 0x75, 0x9c, 0xfe, 0xf8, 0x7f, 0x02, 0x00, 0x00, 0xff, 0xff, 0x84, 0xab, 0x2e,
	0x4a, 0xde, 0x31, 0x00, 0x00,
}
