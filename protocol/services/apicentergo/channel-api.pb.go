// Code generated by protoc-gen-go. DO NOT EDIT.
// source: apicenter-go/channel-api.proto

package apicentergo // import "golang.52tt.com/protocol/services/apicentergo"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 制裁类型
type ENUM_SANCTION_OP_TYPE int32

const (
	ENUM_SANCTION_OP_TYPE_E_SANCTION_OP_TYPE_NONE    ENUM_SANCTION_OP_TYPE = 0
	ENUM_SANCTION_OP_TYPE_E_SANCTION_OP_TYPE_WARNING ENUM_SANCTION_OP_TYPE = 3
	ENUM_SANCTION_OP_TYPE_E_SANCTION_OP_TYPE_BANNED  ENUM_SANCTION_OP_TYPE = 4
	ENUM_SANCTION_OP_TYPE_E_SANCTION_OP_TYPE_IGNORE  ENUM_SANCTION_OP_TYPE = 25
)

var ENUM_SANCTION_OP_TYPE_name = map[int32]string{
	0:  "E_SANCTION_OP_TYPE_NONE",
	3:  "E_SANCTION_OP_TYPE_WARNING",
	4:  "E_SANCTION_OP_TYPE_BANNED",
	25: "E_SANCTION_OP_TYPE_IGNORE",
}
var ENUM_SANCTION_OP_TYPE_value = map[string]int32{
	"E_SANCTION_OP_TYPE_NONE":    0,
	"E_SANCTION_OP_TYPE_WARNING": 3,
	"E_SANCTION_OP_TYPE_BANNED":  4,
	"E_SANCTION_OP_TYPE_IGNORE":  25,
}

func (x ENUM_SANCTION_OP_TYPE) String() string {
	return proto.EnumName(ENUM_SANCTION_OP_TYPE_name, int32(x))
}
func (ENUM_SANCTION_OP_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_api_8b6cf8133c6b50f5, []int{0}
}

type GetChannelSimpleInfoReq_QueryType int32

const (
	GetChannelSimpleInfoReq_QUERY_BY_CHANNELID     GetChannelSimpleInfoReq_QueryType = 0
	GetChannelSimpleInfoReq_QUERY_BY_DISPLAYID     GetChannelSimpleInfoReq_QueryType = 1
	GetChannelSimpleInfoReq_QUERY_BY_CHANNELVIEWID GetChannelSimpleInfoReq_QueryType = 2
)

var GetChannelSimpleInfoReq_QueryType_name = map[int32]string{
	0: "QUERY_BY_CHANNELID",
	1: "QUERY_BY_DISPLAYID",
	2: "QUERY_BY_CHANNELVIEWID",
}
var GetChannelSimpleInfoReq_QueryType_value = map[string]int32{
	"QUERY_BY_CHANNELID":     0,
	"QUERY_BY_DISPLAYID":     1,
	"QUERY_BY_CHANNELVIEWID": 2,
}

func (x GetChannelSimpleInfoReq_QueryType) String() string {
	return proto.EnumName(GetChannelSimpleInfoReq_QueryType_name, int32(x))
}
func (GetChannelSimpleInfoReq_QueryType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_api_8b6cf8133c6b50f5, []int{3, 0}
}

type ChannelSimpleInfo struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	DisplayId            uint32   `protobuf:"varint,2,opt,name=display_id,json=displayId,proto3" json:"display_id,omitempty"`
	AppId                uint32   `protobuf:"varint,3,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	HasPwd               bool     `protobuf:"varint,4,opt,name=has_pwd,json=hasPwd,proto3" json:"has_pwd,omitempty"`
	ChannelType          uint32   `protobuf:"varint,5,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	BindId               uint32   `protobuf:"varint,6,opt,name=bind_id,json=bindId,proto3" json:"bind_id,omitempty"`
	SwitchFlag           uint32   `protobuf:"varint,7,opt,name=switch_flag,json=switchFlag,proto3" json:"switch_flag,omitempty"`
	CreaterUid           uint32   `protobuf:"varint,8,opt,name=creater_uid,json=createrUid,proto3" json:"creater_uid,omitempty"`
	CreateTs             uint32   `protobuf:"varint,9,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"`
	IconMd5              string   `protobuf:"bytes,10,opt,name=icon_md5,json=iconMd5,proto3" json:"icon_md5,omitempty"`
	TopicTitle           string   `protobuf:"bytes,11,opt,name=topic_title,json=topicTitle,proto3" json:"topic_title,omitempty"`
	Passwd               string   `protobuf:"bytes,12,opt,name=passwd,proto3" json:"passwd,omitempty"`
	Name                 string   `protobuf:"bytes,13,opt,name=name,proto3" json:"name,omitempty"`
	IsDel                bool     `protobuf:"varint,14,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
	ChannelViewId        string   `protobuf:"bytes,15,opt,name=channel_view_id,json=channelViewId,proto3" json:"channel_view_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelSimpleInfo) Reset()         { *m = ChannelSimpleInfo{} }
func (m *ChannelSimpleInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelSimpleInfo) ProtoMessage()    {}
func (*ChannelSimpleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_api_8b6cf8133c6b50f5, []int{0}
}
func (m *ChannelSimpleInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelSimpleInfo.Unmarshal(m, b)
}
func (m *ChannelSimpleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelSimpleInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelSimpleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelSimpleInfo.Merge(dst, src)
}
func (m *ChannelSimpleInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelSimpleInfo.Size(m)
}
func (m *ChannelSimpleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelSimpleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelSimpleInfo proto.InternalMessageInfo

func (m *ChannelSimpleInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelSimpleInfo) GetDisplayId() uint32 {
	if m != nil {
		return m.DisplayId
	}
	return 0
}

func (m *ChannelSimpleInfo) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *ChannelSimpleInfo) GetHasPwd() bool {
	if m != nil {
		return m.HasPwd
	}
	return false
}

func (m *ChannelSimpleInfo) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *ChannelSimpleInfo) GetBindId() uint32 {
	if m != nil {
		return m.BindId
	}
	return 0
}

func (m *ChannelSimpleInfo) GetSwitchFlag() uint32 {
	if m != nil {
		return m.SwitchFlag
	}
	return 0
}

func (m *ChannelSimpleInfo) GetCreaterUid() uint32 {
	if m != nil {
		return m.CreaterUid
	}
	return 0
}

func (m *ChannelSimpleInfo) GetCreateTs() uint32 {
	if m != nil {
		return m.CreateTs
	}
	return 0
}

func (m *ChannelSimpleInfo) GetIconMd5() string {
	if m != nil {
		return m.IconMd5
	}
	return ""
}

func (m *ChannelSimpleInfo) GetTopicTitle() string {
	if m != nil {
		return m.TopicTitle
	}
	return ""
}

func (m *ChannelSimpleInfo) GetPasswd() string {
	if m != nil {
		return m.Passwd
	}
	return ""
}

func (m *ChannelSimpleInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ChannelSimpleInfo) GetIsDel() bool {
	if m != nil {
		return m.IsDel
	}
	return false
}

func (m *ChannelSimpleInfo) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

// 批量获取房间信息
type BatchGetChannelSimpleInfoReq struct {
	ChannelIdList        []uint32 `protobuf:"varint,1,rep,packed,name=channel_id_list,json=channelIdList,proto3" json:"channel_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetChannelSimpleInfoReq) Reset()         { *m = BatchGetChannelSimpleInfoReq{} }
func (m *BatchGetChannelSimpleInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelSimpleInfoReq) ProtoMessage()    {}
func (*BatchGetChannelSimpleInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_api_8b6cf8133c6b50f5, []int{1}
}
func (m *BatchGetChannelSimpleInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelSimpleInfoReq.Unmarshal(m, b)
}
func (m *BatchGetChannelSimpleInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelSimpleInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelSimpleInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelSimpleInfoReq.Merge(dst, src)
}
func (m *BatchGetChannelSimpleInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelSimpleInfoReq.Size(m)
}
func (m *BatchGetChannelSimpleInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelSimpleInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelSimpleInfoReq proto.InternalMessageInfo

func (m *BatchGetChannelSimpleInfoReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type BatchGetChannelSimpleInfoResp struct {
	ChannelSimpleList    []*ChannelSimpleInfo `protobuf:"bytes,1,rep,name=channel_simple_list,json=channelSimpleList,proto3" json:"channel_simple_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *BatchGetChannelSimpleInfoResp) Reset()         { *m = BatchGetChannelSimpleInfoResp{} }
func (m *BatchGetChannelSimpleInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelSimpleInfoResp) ProtoMessage()    {}
func (*BatchGetChannelSimpleInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_api_8b6cf8133c6b50f5, []int{2}
}
func (m *BatchGetChannelSimpleInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelSimpleInfoResp.Unmarshal(m, b)
}
func (m *BatchGetChannelSimpleInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelSimpleInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelSimpleInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelSimpleInfoResp.Merge(dst, src)
}
func (m *BatchGetChannelSimpleInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelSimpleInfoResp.Size(m)
}
func (m *BatchGetChannelSimpleInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelSimpleInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelSimpleInfoResp proto.InternalMessageInfo

func (m *BatchGetChannelSimpleInfoResp) GetChannelSimpleList() []*ChannelSimpleInfo {
	if m != nil {
		return m.ChannelSimpleList
	}
	return nil
}

// 获取房间信息
type GetChannelSimpleInfoReq struct {
	QueryType            uint32   `protobuf:"varint,1,opt,name=query_type,json=queryType,proto3" json:"query_type,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	DisplayId            uint32   `protobuf:"varint,3,opt,name=display_id,json=displayId,proto3" json:"display_id,omitempty"`
	ChannelViewId        string   `protobuf:"bytes,4,opt,name=channel_view_id,json=channelViewId,proto3" json:"channel_view_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelSimpleInfoReq) Reset()         { *m = GetChannelSimpleInfoReq{} }
func (m *GetChannelSimpleInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelSimpleInfoReq) ProtoMessage()    {}
func (*GetChannelSimpleInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_api_8b6cf8133c6b50f5, []int{3}
}
func (m *GetChannelSimpleInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelSimpleInfoReq.Unmarshal(m, b)
}
func (m *GetChannelSimpleInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelSimpleInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelSimpleInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelSimpleInfoReq.Merge(dst, src)
}
func (m *GetChannelSimpleInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelSimpleInfoReq.Size(m)
}
func (m *GetChannelSimpleInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelSimpleInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelSimpleInfoReq proto.InternalMessageInfo

func (m *GetChannelSimpleInfoReq) GetQueryType() uint32 {
	if m != nil {
		return m.QueryType
	}
	return 0
}

func (m *GetChannelSimpleInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelSimpleInfoReq) GetDisplayId() uint32 {
	if m != nil {
		return m.DisplayId
	}
	return 0
}

func (m *GetChannelSimpleInfoReq) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

type GetChannelSimpleInfoResp struct {
	ChannelInfo          *ChannelSimpleInfo `protobuf:"bytes,1,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetChannelSimpleInfoResp) Reset()         { *m = GetChannelSimpleInfoResp{} }
func (m *GetChannelSimpleInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelSimpleInfoResp) ProtoMessage()    {}
func (*GetChannelSimpleInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_api_8b6cf8133c6b50f5, []int{4}
}
func (m *GetChannelSimpleInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelSimpleInfoResp.Unmarshal(m, b)
}
func (m *GetChannelSimpleInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelSimpleInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelSimpleInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelSimpleInfoResp.Merge(dst, src)
}
func (m *GetChannelSimpleInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelSimpleInfoResp.Size(m)
}
func (m *GetChannelSimpleInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelSimpleInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelSimpleInfoResp proto.InternalMessageInfo

func (m *GetChannelSimpleInfoResp) GetChannelInfo() *ChannelSimpleInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

// 举报信息
type ReportInfo struct {
	ReportReason         string   `protobuf:"bytes,1,opt,name=report_reason,json=reportReason,proto3" json:"report_reason,omitempty"`
	ReportType           uint32   `protobuf:"varint,2,opt,name=report_type,json=reportType,proto3" json:"report_type,omitempty"`
	OpTtAcc              string   `protobuf:"bytes,3,opt,name=op_tt_acc,json=opTtAcc,proto3" json:"op_tt_acc,omitempty"`
	ReportTs             uint32   `protobuf:"varint,4,opt,name=report_ts,json=reportTs,proto3" json:"report_ts,omitempty"`
	PicUrl               string   `protobuf:"bytes,5,opt,name=pic_url,json=picUrl,proto3" json:"pic_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportInfo) Reset()         { *m = ReportInfo{} }
func (m *ReportInfo) String() string { return proto.CompactTextString(m) }
func (*ReportInfo) ProtoMessage()    {}
func (*ReportInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_api_8b6cf8133c6b50f5, []int{5}
}
func (m *ReportInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportInfo.Unmarshal(m, b)
}
func (m *ReportInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportInfo.Marshal(b, m, deterministic)
}
func (dst *ReportInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportInfo.Merge(dst, src)
}
func (m *ReportInfo) XXX_Size() int {
	return xxx_messageInfo_ReportInfo.Size(m)
}
func (m *ReportInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ReportInfo proto.InternalMessageInfo

func (m *ReportInfo) GetReportReason() string {
	if m != nil {
		return m.ReportReason
	}
	return ""
}

func (m *ReportInfo) GetReportType() uint32 {
	if m != nil {
		return m.ReportType
	}
	return 0
}

func (m *ReportInfo) GetOpTtAcc() string {
	if m != nil {
		return m.OpTtAcc
	}
	return ""
}

func (m *ReportInfo) GetReportTs() uint32 {
	if m != nil {
		return m.ReportTs
	}
	return 0
}

func (m *ReportInfo) GetPicUrl() string {
	if m != nil {
		return m.PicUrl
	}
	return ""
}

// 房间基础信息
type ChannelBaseInfo struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelName          string   `protobuf:"bytes,2,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	ChannelDisplayId     uint32   `protobuf:"varint,3,opt,name=channel_display_id,json=channelDisplayId,proto3" json:"channel_display_id,omitempty"`
	ChannelTypeDesc      string   `protobuf:"bytes,4,opt,name=channel_type_desc,json=channelTypeDesc,proto3" json:"channel_type_desc,omitempty"`
	OwnerNickname        string   `protobuf:"bytes,5,opt,name=owner_nickname,json=ownerNickname,proto3" json:"owner_nickname,omitempty"`
	OwnerTtid            string   `protobuf:"bytes,6,opt,name=owner_ttid,json=ownerTtid,proto3" json:"owner_ttid,omitempty"`
	ChannelViewId        string   `protobuf:"bytes,7,opt,name=channel_view_id,json=channelViewId,proto3" json:"channel_view_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelBaseInfo) Reset()         { *m = ChannelBaseInfo{} }
func (m *ChannelBaseInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelBaseInfo) ProtoMessage()    {}
func (*ChannelBaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_api_8b6cf8133c6b50f5, []int{6}
}
func (m *ChannelBaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelBaseInfo.Unmarshal(m, b)
}
func (m *ChannelBaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelBaseInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelBaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelBaseInfo.Merge(dst, src)
}
func (m *ChannelBaseInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelBaseInfo.Size(m)
}
func (m *ChannelBaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelBaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelBaseInfo proto.InternalMessageInfo

func (m *ChannelBaseInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelBaseInfo) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *ChannelBaseInfo) GetChannelDisplayId() uint32 {
	if m != nil {
		return m.ChannelDisplayId
	}
	return 0
}

func (m *ChannelBaseInfo) GetChannelTypeDesc() string {
	if m != nil {
		return m.ChannelTypeDesc
	}
	return ""
}

func (m *ChannelBaseInfo) GetOwnerNickname() string {
	if m != nil {
		return m.OwnerNickname
	}
	return ""
}

func (m *ChannelBaseInfo) GetOwnerTtid() string {
	if m != nil {
		return m.OwnerTtid
	}
	return ""
}

func (m *ChannelBaseInfo) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

// 封禁信息
type SanctionInfo struct {
	SanctionOpType       uint32   `protobuf:"varint,1,opt,name=sanction_op_type,json=sanctionOpType,proto3" json:"sanction_op_type,omitempty"`
	OpTs                 uint32   `protobuf:"varint,2,opt,name=op_ts,json=opTs,proto3" json:"op_ts,omitempty"`
	OpUser               string   `protobuf:"bytes,3,opt,name=op_user,json=opUser,proto3" json:"op_user,omitempty"`
	BannedSecond         int32    `protobuf:"varint,4,opt,name=banned_second,json=bannedSecond,proto3" json:"banned_second,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SanctionInfo) Reset()         { *m = SanctionInfo{} }
func (m *SanctionInfo) String() string { return proto.CompactTextString(m) }
func (*SanctionInfo) ProtoMessage()    {}
func (*SanctionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_api_8b6cf8133c6b50f5, []int{7}
}
func (m *SanctionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SanctionInfo.Unmarshal(m, b)
}
func (m *SanctionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SanctionInfo.Marshal(b, m, deterministic)
}
func (dst *SanctionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SanctionInfo.Merge(dst, src)
}
func (m *SanctionInfo) XXX_Size() int {
	return xxx_messageInfo_SanctionInfo.Size(m)
}
func (m *SanctionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SanctionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SanctionInfo proto.InternalMessageInfo

func (m *SanctionInfo) GetSanctionOpType() uint32 {
	if m != nil {
		return m.SanctionOpType
	}
	return 0
}

func (m *SanctionInfo) GetOpTs() uint32 {
	if m != nil {
		return m.OpTs
	}
	return 0
}

func (m *SanctionInfo) GetOpUser() string {
	if m != nil {
		return m.OpUser
	}
	return ""
}

func (m *SanctionInfo) GetBannedSecond() int32 {
	if m != nil {
		return m.BannedSecond
	}
	return 0
}

type ChannelProcListItem struct {
	BaseInfo             *ChannelBaseInfo `protobuf:"bytes,1,opt,name=base_info,json=baseInfo,proto3" json:"base_info,omitempty"`
	IsWhite              bool             `protobuf:"varint,2,opt,name=is_white,json=isWhite,proto3" json:"is_white,omitempty"`
	ReportRealCnt        uint32           `protobuf:"varint,3,opt,name=report_real_cnt,json=reportRealCnt,proto3" json:"report_real_cnt,omitempty"`
	ReportList           []*ReportInfo    `protobuf:"bytes,4,rep,name=report_list,json=reportList,proto3" json:"report_list,omitempty"`
	SanctionInfo         *SanctionInfo    `protobuf:"bytes,5,opt,name=sanction_info,json=sanctionInfo,proto3" json:"sanction_info,omitempty"`
	LastUpdateTs         uint32           `protobuf:"varint,6,opt,name=last_update_ts,json=lastUpdateTs,proto3" json:"last_update_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *ChannelProcListItem) Reset()         { *m = ChannelProcListItem{} }
func (m *ChannelProcListItem) String() string { return proto.CompactTextString(m) }
func (*ChannelProcListItem) ProtoMessage()    {}
func (*ChannelProcListItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_api_8b6cf8133c6b50f5, []int{8}
}
func (m *ChannelProcListItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelProcListItem.Unmarshal(m, b)
}
func (m *ChannelProcListItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelProcListItem.Marshal(b, m, deterministic)
}
func (dst *ChannelProcListItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelProcListItem.Merge(dst, src)
}
func (m *ChannelProcListItem) XXX_Size() int {
	return xxx_messageInfo_ChannelProcListItem.Size(m)
}
func (m *ChannelProcListItem) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelProcListItem.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelProcListItem proto.InternalMessageInfo

func (m *ChannelProcListItem) GetBaseInfo() *ChannelBaseInfo {
	if m != nil {
		return m.BaseInfo
	}
	return nil
}

func (m *ChannelProcListItem) GetIsWhite() bool {
	if m != nil {
		return m.IsWhite
	}
	return false
}

func (m *ChannelProcListItem) GetReportRealCnt() uint32 {
	if m != nil {
		return m.ReportRealCnt
	}
	return 0
}

func (m *ChannelProcListItem) GetReportList() []*ReportInfo {
	if m != nil {
		return m.ReportList
	}
	return nil
}

func (m *ChannelProcListItem) GetSanctionInfo() *SanctionInfo {
	if m != nil {
		return m.SanctionInfo
	}
	return nil
}

func (m *ChannelProcListItem) GetLastUpdateTs() uint32 {
	if m != nil {
		return m.LastUpdateTs
	}
	return 0
}

// 获取所有的房间举报信息列表
type GetAllReportHistoryListReq struct {
	IsGetWaitProc        bool     `protobuf:"varint,1,opt,name=is_get_wait_proc,json=isGetWaitProc,proto3" json:"is_get_wait_proc,omitempty"`
	PageIdx              uint32   `protobuf:"varint,2,opt,name=page_idx,json=pageIdx,proto3" json:"page_idx,omitempty"`
	PageSize             uint32   `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	BeginTs              uint32   `protobuf:"varint,4,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,5,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllReportHistoryListReq) Reset()         { *m = GetAllReportHistoryListReq{} }
func (m *GetAllReportHistoryListReq) String() string { return proto.CompactTextString(m) }
func (*GetAllReportHistoryListReq) ProtoMessage()    {}
func (*GetAllReportHistoryListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_api_8b6cf8133c6b50f5, []int{9}
}
func (m *GetAllReportHistoryListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllReportHistoryListReq.Unmarshal(m, b)
}
func (m *GetAllReportHistoryListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllReportHistoryListReq.Marshal(b, m, deterministic)
}
func (dst *GetAllReportHistoryListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllReportHistoryListReq.Merge(dst, src)
}
func (m *GetAllReportHistoryListReq) XXX_Size() int {
	return xxx_messageInfo_GetAllReportHistoryListReq.Size(m)
}
func (m *GetAllReportHistoryListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllReportHistoryListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllReportHistoryListReq proto.InternalMessageInfo

func (m *GetAllReportHistoryListReq) GetIsGetWaitProc() bool {
	if m != nil {
		return m.IsGetWaitProc
	}
	return false
}

func (m *GetAllReportHistoryListReq) GetPageIdx() uint32 {
	if m != nil {
		return m.PageIdx
	}
	return 0
}

func (m *GetAllReportHistoryListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetAllReportHistoryListReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *GetAllReportHistoryListReq) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

type GetAllReportHistoryListResp struct {
	ProcList             []*ChannelProcListItem `protobuf:"bytes,1,rep,name=proc_list,json=procList,proto3" json:"proc_list,omitempty"`
	TotalListSize        uint32                 `protobuf:"varint,2,opt,name=total_list_size,json=totalListSize,proto3" json:"total_list_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetAllReportHistoryListResp) Reset()         { *m = GetAllReportHistoryListResp{} }
func (m *GetAllReportHistoryListResp) String() string { return proto.CompactTextString(m) }
func (*GetAllReportHistoryListResp) ProtoMessage()    {}
func (*GetAllReportHistoryListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_api_8b6cf8133c6b50f5, []int{10}
}
func (m *GetAllReportHistoryListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllReportHistoryListResp.Unmarshal(m, b)
}
func (m *GetAllReportHistoryListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllReportHistoryListResp.Marshal(b, m, deterministic)
}
func (dst *GetAllReportHistoryListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllReportHistoryListResp.Merge(dst, src)
}
func (m *GetAllReportHistoryListResp) XXX_Size() int {
	return xxx_messageInfo_GetAllReportHistoryListResp.Size(m)
}
func (m *GetAllReportHistoryListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllReportHistoryListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllReportHistoryListResp proto.InternalMessageInfo

func (m *GetAllReportHistoryListResp) GetProcList() []*ChannelProcListItem {
	if m != nil {
		return m.ProcList
	}
	return nil
}

func (m *GetAllReportHistoryListResp) GetTotalListSize() uint32 {
	if m != nil {
		return m.TotalListSize
	}
	return 0
}

// 获取被举报房间详情
type GetChannelReportStatReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelReportStatReq) Reset()         { *m = GetChannelReportStatReq{} }
func (m *GetChannelReportStatReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelReportStatReq) ProtoMessage()    {}
func (*GetChannelReportStatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_api_8b6cf8133c6b50f5, []int{11}
}
func (m *GetChannelReportStatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelReportStatReq.Unmarshal(m, b)
}
func (m *GetChannelReportStatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelReportStatReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelReportStatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelReportStatReq.Merge(dst, src)
}
func (m *GetChannelReportStatReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelReportStatReq.Size(m)
}
func (m *GetChannelReportStatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelReportStatReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelReportStatReq proto.InternalMessageInfo

func (m *GetChannelReportStatReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelReportStatResp struct {
	BaseInfo             *ChannelBaseInfo `protobuf:"bytes,1,opt,name=base_info,json=baseInfo,proto3" json:"base_info,omitempty"`
	BannedCnt            uint32           `protobuf:"varint,2,opt,name=banned_cnt,json=bannedCnt,proto3" json:"banned_cnt,omitempty"`
	WarningCnt           uint32           `protobuf:"varint,3,opt,name=warning_cnt,json=warningCnt,proto3" json:"warning_cnt,omitempty"`
	ReprotCnt            uint32           `protobuf:"varint,4,opt,name=reprot_cnt,json=reprotCnt,proto3" json:"reprot_cnt,omitempty"`
	MaxOlCnt             uint32           `protobuf:"varint,5,opt,name=max_ol_cnt,json=maxOlCnt,proto3" json:"max_ol_cnt,omitempty"`
	CollectCnt           uint32           `protobuf:"varint,6,opt,name=collect_cnt,json=collectCnt,proto3" json:"collect_cnt,omitempty"`
	GiftCnt              uint32           `protobuf:"varint,7,opt,name=gift_cnt,json=giftCnt,proto3" json:"gift_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetChannelReportStatResp) Reset()         { *m = GetChannelReportStatResp{} }
func (m *GetChannelReportStatResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelReportStatResp) ProtoMessage()    {}
func (*GetChannelReportStatResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_api_8b6cf8133c6b50f5, []int{12}
}
func (m *GetChannelReportStatResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelReportStatResp.Unmarshal(m, b)
}
func (m *GetChannelReportStatResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelReportStatResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelReportStatResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelReportStatResp.Merge(dst, src)
}
func (m *GetChannelReportStatResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelReportStatResp.Size(m)
}
func (m *GetChannelReportStatResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelReportStatResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelReportStatResp proto.InternalMessageInfo

func (m *GetChannelReportStatResp) GetBaseInfo() *ChannelBaseInfo {
	if m != nil {
		return m.BaseInfo
	}
	return nil
}

func (m *GetChannelReportStatResp) GetBannedCnt() uint32 {
	if m != nil {
		return m.BannedCnt
	}
	return 0
}

func (m *GetChannelReportStatResp) GetWarningCnt() uint32 {
	if m != nil {
		return m.WarningCnt
	}
	return 0
}

func (m *GetChannelReportStatResp) GetReprotCnt() uint32 {
	if m != nil {
		return m.ReprotCnt
	}
	return 0
}

func (m *GetChannelReportStatResp) GetMaxOlCnt() uint32 {
	if m != nil {
		return m.MaxOlCnt
	}
	return 0
}

func (m *GetChannelReportStatResp) GetCollectCnt() uint32 {
	if m != nil {
		return m.CollectCnt
	}
	return 0
}

func (m *GetChannelReportStatResp) GetGiftCnt() uint32 {
	if m != nil {
		return m.GiftCnt
	}
	return 0
}

type ReportListItem struct {
	ReportList           []*ReportInfo `protobuf:"bytes,1,rep,name=report_list,json=reportList,proto3" json:"report_list,omitempty"`
	SanctionInfo         *SanctionInfo `protobuf:"bytes,2,opt,name=sanction_info,json=sanctionInfo,proto3" json:"sanction_info,omitempty"`
	LastUpdateTs         uint32        `protobuf:"varint,3,opt,name=last_update_ts,json=lastUpdateTs,proto3" json:"last_update_ts,omitempty"`
	ReportRealCnt        uint32        `protobuf:"varint,4,opt,name=report_real_cnt,json=reportRealCnt,proto3" json:"report_real_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ReportListItem) Reset()         { *m = ReportListItem{} }
func (m *ReportListItem) String() string { return proto.CompactTextString(m) }
func (*ReportListItem) ProtoMessage()    {}
func (*ReportListItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_api_8b6cf8133c6b50f5, []int{13}
}
func (m *ReportListItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportListItem.Unmarshal(m, b)
}
func (m *ReportListItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportListItem.Marshal(b, m, deterministic)
}
func (dst *ReportListItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportListItem.Merge(dst, src)
}
func (m *ReportListItem) XXX_Size() int {
	return xxx_messageInfo_ReportListItem.Size(m)
}
func (m *ReportListItem) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportListItem.DiscardUnknown(m)
}

var xxx_messageInfo_ReportListItem proto.InternalMessageInfo

func (m *ReportListItem) GetReportList() []*ReportInfo {
	if m != nil {
		return m.ReportList
	}
	return nil
}

func (m *ReportListItem) GetSanctionInfo() *SanctionInfo {
	if m != nil {
		return m.SanctionInfo
	}
	return nil
}

func (m *ReportListItem) GetLastUpdateTs() uint32 {
	if m != nil {
		return m.LastUpdateTs
	}
	return 0
}

func (m *ReportListItem) GetReportRealCnt() uint32 {
	if m != nil {
		return m.ReportRealCnt
	}
	return 0
}

// 获取房间被处理详情
type GetChannelReportProcListReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	PageIdx              uint32   `protobuf:"varint,2,opt,name=page_idx,json=pageIdx,proto3" json:"page_idx,omitempty"`
	PageSize             uint32   `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelReportProcListReq) Reset()         { *m = GetChannelReportProcListReq{} }
func (m *GetChannelReportProcListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelReportProcListReq) ProtoMessage()    {}
func (*GetChannelReportProcListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_api_8b6cf8133c6b50f5, []int{14}
}
func (m *GetChannelReportProcListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelReportProcListReq.Unmarshal(m, b)
}
func (m *GetChannelReportProcListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelReportProcListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelReportProcListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelReportProcListReq.Merge(dst, src)
}
func (m *GetChannelReportProcListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelReportProcListReq.Size(m)
}
func (m *GetChannelReportProcListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelReportProcListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelReportProcListReq proto.InternalMessageInfo

func (m *GetChannelReportProcListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelReportProcListReq) GetPageIdx() uint32 {
	if m != nil {
		return m.PageIdx
	}
	return 0
}

func (m *GetChannelReportProcListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetChannelReportProcListResp struct {
	ReportList           []*ReportListItem `protobuf:"bytes,1,rep,name=report_list,json=reportList,proto3" json:"report_list,omitempty"`
	TotalListSize        uint32            `protobuf:"varint,2,opt,name=total_list_size,json=totalListSize,proto3" json:"total_list_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetChannelReportProcListResp) Reset()         { *m = GetChannelReportProcListResp{} }
func (m *GetChannelReportProcListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelReportProcListResp) ProtoMessage()    {}
func (*GetChannelReportProcListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_api_8b6cf8133c6b50f5, []int{15}
}
func (m *GetChannelReportProcListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelReportProcListResp.Unmarshal(m, b)
}
func (m *GetChannelReportProcListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelReportProcListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelReportProcListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelReportProcListResp.Merge(dst, src)
}
func (m *GetChannelReportProcListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelReportProcListResp.Size(m)
}
func (m *GetChannelReportProcListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelReportProcListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelReportProcListResp proto.InternalMessageInfo

func (m *GetChannelReportProcListResp) GetReportList() []*ReportListItem {
	if m != nil {
		return m.ReportList
	}
	return nil
}

func (m *GetChannelReportProcListResp) GetTotalListSize() uint32 {
	if m != nil {
		return m.TotalListSize
	}
	return 0
}

// 制裁房间
type SanctionChannelReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	OpUser               string   `protobuf:"bytes,2,opt,name=op_user,json=opUser,proto3" json:"op_user,omitempty"`
	OpType               uint32   `protobuf:"varint,3,opt,name=op_type,json=opType,proto3" json:"op_type,omitempty"`
	BannedSecond         uint32   `protobuf:"varint,4,opt,name=banned_second,json=bannedSecond,proto3" json:"banned_second,omitempty"`
	BannedReason         string   `protobuf:"bytes,5,opt,name=banned_reason,json=bannedReason,proto3" json:"banned_reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SanctionChannelReq) Reset()         { *m = SanctionChannelReq{} }
func (m *SanctionChannelReq) String() string { return proto.CompactTextString(m) }
func (*SanctionChannelReq) ProtoMessage()    {}
func (*SanctionChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_api_8b6cf8133c6b50f5, []int{16}
}
func (m *SanctionChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SanctionChannelReq.Unmarshal(m, b)
}
func (m *SanctionChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SanctionChannelReq.Marshal(b, m, deterministic)
}
func (dst *SanctionChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SanctionChannelReq.Merge(dst, src)
}
func (m *SanctionChannelReq) XXX_Size() int {
	return xxx_messageInfo_SanctionChannelReq.Size(m)
}
func (m *SanctionChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SanctionChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_SanctionChannelReq proto.InternalMessageInfo

func (m *SanctionChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SanctionChannelReq) GetOpUser() string {
	if m != nil {
		return m.OpUser
	}
	return ""
}

func (m *SanctionChannelReq) GetOpType() uint32 {
	if m != nil {
		return m.OpType
	}
	return 0
}

func (m *SanctionChannelReq) GetBannedSecond() uint32 {
	if m != nil {
		return m.BannedSecond
	}
	return 0
}

func (m *SanctionChannelReq) GetBannedReason() string {
	if m != nil {
		return m.BannedReason
	}
	return ""
}

type SanctionChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SanctionChannelResp) Reset()         { *m = SanctionChannelResp{} }
func (m *SanctionChannelResp) String() string { return proto.CompactTextString(m) }
func (*SanctionChannelResp) ProtoMessage()    {}
func (*SanctionChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_api_8b6cf8133c6b50f5, []int{17}
}
func (m *SanctionChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SanctionChannelResp.Unmarshal(m, b)
}
func (m *SanctionChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SanctionChannelResp.Marshal(b, m, deterministic)
}
func (dst *SanctionChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SanctionChannelResp.Merge(dst, src)
}
func (m *SanctionChannelResp) XXX_Size() int {
	return xxx_messageInfo_SanctionChannelResp.Size(m)
}
func (m *SanctionChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SanctionChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_SanctionChannelResp proto.InternalMessageInfo

type BannedChannelListItem struct {
	BaseInfo             *ChannelBaseInfo `protobuf:"bytes,1,opt,name=base_info,json=baseInfo,proto3" json:"base_info,omitempty"`
	ReaminBannedSecond   uint32           `protobuf:"varint,2,opt,name=reamin_banned_second,json=reaminBannedSecond,proto3" json:"reamin_banned_second,omitempty"`
	BannedReason         string           `protobuf:"bytes,3,opt,name=banned_reason,json=bannedReason,proto3" json:"banned_reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *BannedChannelListItem) Reset()         { *m = BannedChannelListItem{} }
func (m *BannedChannelListItem) String() string { return proto.CompactTextString(m) }
func (*BannedChannelListItem) ProtoMessage()    {}
func (*BannedChannelListItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_api_8b6cf8133c6b50f5, []int{18}
}
func (m *BannedChannelListItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BannedChannelListItem.Unmarshal(m, b)
}
func (m *BannedChannelListItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BannedChannelListItem.Marshal(b, m, deterministic)
}
func (dst *BannedChannelListItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BannedChannelListItem.Merge(dst, src)
}
func (m *BannedChannelListItem) XXX_Size() int {
	return xxx_messageInfo_BannedChannelListItem.Size(m)
}
func (m *BannedChannelListItem) XXX_DiscardUnknown() {
	xxx_messageInfo_BannedChannelListItem.DiscardUnknown(m)
}

var xxx_messageInfo_BannedChannelListItem proto.InternalMessageInfo

func (m *BannedChannelListItem) GetBaseInfo() *ChannelBaseInfo {
	if m != nil {
		return m.BaseInfo
	}
	return nil
}

func (m *BannedChannelListItem) GetReaminBannedSecond() uint32 {
	if m != nil {
		return m.ReaminBannedSecond
	}
	return 0
}

func (m *BannedChannelListItem) GetBannedReason() string {
	if m != nil {
		return m.BannedReason
	}
	return ""
}

// 获取被封禁房间列表
type GetBannedChannelListReq struct {
	PageIdx              uint32   `protobuf:"varint,1,opt,name=page_idx,json=pageIdx,proto3" json:"page_idx,omitempty"`
	PageSize             uint32   `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBannedChannelListReq) Reset()         { *m = GetBannedChannelListReq{} }
func (m *GetBannedChannelListReq) String() string { return proto.CompactTextString(m) }
func (*GetBannedChannelListReq) ProtoMessage()    {}
func (*GetBannedChannelListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_api_8b6cf8133c6b50f5, []int{19}
}
func (m *GetBannedChannelListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBannedChannelListReq.Unmarshal(m, b)
}
func (m *GetBannedChannelListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBannedChannelListReq.Marshal(b, m, deterministic)
}
func (dst *GetBannedChannelListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBannedChannelListReq.Merge(dst, src)
}
func (m *GetBannedChannelListReq) XXX_Size() int {
	return xxx_messageInfo_GetBannedChannelListReq.Size(m)
}
func (m *GetBannedChannelListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBannedChannelListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBannedChannelListReq proto.InternalMessageInfo

func (m *GetBannedChannelListReq) GetPageIdx() uint32 {
	if m != nil {
		return m.PageIdx
	}
	return 0
}

func (m *GetBannedChannelListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetBannedChannelListResp struct {
	BannedChannelList    []*BannedChannelListItem `protobuf:"bytes,1,rep,name=banned_channel_list,json=bannedChannelList,proto3" json:"banned_channel_list,omitempty"`
	TotalListSize        uint32                   `protobuf:"varint,2,opt,name=total_list_size,json=totalListSize,proto3" json:"total_list_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetBannedChannelListResp) Reset()         { *m = GetBannedChannelListResp{} }
func (m *GetBannedChannelListResp) String() string { return proto.CompactTextString(m) }
func (*GetBannedChannelListResp) ProtoMessage()    {}
func (*GetBannedChannelListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_api_8b6cf8133c6b50f5, []int{20}
}
func (m *GetBannedChannelListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBannedChannelListResp.Unmarshal(m, b)
}
func (m *GetBannedChannelListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBannedChannelListResp.Marshal(b, m, deterministic)
}
func (dst *GetBannedChannelListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBannedChannelListResp.Merge(dst, src)
}
func (m *GetBannedChannelListResp) XXX_Size() int {
	return xxx_messageInfo_GetBannedChannelListResp.Size(m)
}
func (m *GetBannedChannelListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBannedChannelListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBannedChannelListResp proto.InternalMessageInfo

func (m *GetBannedChannelListResp) GetBannedChannelList() []*BannedChannelListItem {
	if m != nil {
		return m.BannedChannelList
	}
	return nil
}

func (m *GetBannedChannelListResp) GetTotalListSize() uint32 {
	if m != nil {
		return m.TotalListSize
	}
	return 0
}

// 增加白名单房间
type AddChannelWhiteListInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddChannelWhiteListInfoReq) Reset()         { *m = AddChannelWhiteListInfoReq{} }
func (m *AddChannelWhiteListInfoReq) String() string { return proto.CompactTextString(m) }
func (*AddChannelWhiteListInfoReq) ProtoMessage()    {}
func (*AddChannelWhiteListInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_api_8b6cf8133c6b50f5, []int{21}
}
func (m *AddChannelWhiteListInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddChannelWhiteListInfoReq.Unmarshal(m, b)
}
func (m *AddChannelWhiteListInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddChannelWhiteListInfoReq.Marshal(b, m, deterministic)
}
func (dst *AddChannelWhiteListInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddChannelWhiteListInfoReq.Merge(dst, src)
}
func (m *AddChannelWhiteListInfoReq) XXX_Size() int {
	return xxx_messageInfo_AddChannelWhiteListInfoReq.Size(m)
}
func (m *AddChannelWhiteListInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddChannelWhiteListInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddChannelWhiteListInfoReq proto.InternalMessageInfo

func (m *AddChannelWhiteListInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type AddChannelWhiteListInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddChannelWhiteListInfoResp) Reset()         { *m = AddChannelWhiteListInfoResp{} }
func (m *AddChannelWhiteListInfoResp) String() string { return proto.CompactTextString(m) }
func (*AddChannelWhiteListInfoResp) ProtoMessage()    {}
func (*AddChannelWhiteListInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_api_8b6cf8133c6b50f5, []int{22}
}
func (m *AddChannelWhiteListInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddChannelWhiteListInfoResp.Unmarshal(m, b)
}
func (m *AddChannelWhiteListInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddChannelWhiteListInfoResp.Marshal(b, m, deterministic)
}
func (dst *AddChannelWhiteListInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddChannelWhiteListInfoResp.Merge(dst, src)
}
func (m *AddChannelWhiteListInfoResp) XXX_Size() int {
	return xxx_messageInfo_AddChannelWhiteListInfoResp.Size(m)
}
func (m *AddChannelWhiteListInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddChannelWhiteListInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddChannelWhiteListInfoResp proto.InternalMessageInfo

// 删除白名单房间
type DelChannelWhiteListInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelChannelWhiteListInfoReq) Reset()         { *m = DelChannelWhiteListInfoReq{} }
func (m *DelChannelWhiteListInfoReq) String() string { return proto.CompactTextString(m) }
func (*DelChannelWhiteListInfoReq) ProtoMessage()    {}
func (*DelChannelWhiteListInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_api_8b6cf8133c6b50f5, []int{23}
}
func (m *DelChannelWhiteListInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelChannelWhiteListInfoReq.Unmarshal(m, b)
}
func (m *DelChannelWhiteListInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelChannelWhiteListInfoReq.Marshal(b, m, deterministic)
}
func (dst *DelChannelWhiteListInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelChannelWhiteListInfoReq.Merge(dst, src)
}
func (m *DelChannelWhiteListInfoReq) XXX_Size() int {
	return xxx_messageInfo_DelChannelWhiteListInfoReq.Size(m)
}
func (m *DelChannelWhiteListInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelChannelWhiteListInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelChannelWhiteListInfoReq proto.InternalMessageInfo

func (m *DelChannelWhiteListInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type DelChannelWhiteListInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelChannelWhiteListInfoResp) Reset()         { *m = DelChannelWhiteListInfoResp{} }
func (m *DelChannelWhiteListInfoResp) String() string { return proto.CompactTextString(m) }
func (*DelChannelWhiteListInfoResp) ProtoMessage()    {}
func (*DelChannelWhiteListInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_api_8b6cf8133c6b50f5, []int{24}
}
func (m *DelChannelWhiteListInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelChannelWhiteListInfoResp.Unmarshal(m, b)
}
func (m *DelChannelWhiteListInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelChannelWhiteListInfoResp.Marshal(b, m, deterministic)
}
func (dst *DelChannelWhiteListInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelChannelWhiteListInfoResp.Merge(dst, src)
}
func (m *DelChannelWhiteListInfoResp) XXX_Size() int {
	return xxx_messageInfo_DelChannelWhiteListInfoResp.Size(m)
}
func (m *DelChannelWhiteListInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelChannelWhiteListInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelChannelWhiteListInfoResp proto.InternalMessageInfo

// 获取白名单房间列表
type GetChannelWhiteListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelWhiteListReq) Reset()         { *m = GetChannelWhiteListReq{} }
func (m *GetChannelWhiteListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelWhiteListReq) ProtoMessage()    {}
func (*GetChannelWhiteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_api_8b6cf8133c6b50f5, []int{25}
}
func (m *GetChannelWhiteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelWhiteListReq.Unmarshal(m, b)
}
func (m *GetChannelWhiteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelWhiteListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelWhiteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelWhiteListReq.Merge(dst, src)
}
func (m *GetChannelWhiteListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelWhiteListReq.Size(m)
}
func (m *GetChannelWhiteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelWhiteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelWhiteListReq proto.InternalMessageInfo

type GetChannelWhiteListResp struct {
	WhiteList            []*ChannelBaseInfo `protobuf:"bytes,1,rep,name=white_list,json=whiteList,proto3" json:"white_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetChannelWhiteListResp) Reset()         { *m = GetChannelWhiteListResp{} }
func (m *GetChannelWhiteListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelWhiteListResp) ProtoMessage()    {}
func (*GetChannelWhiteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_api_8b6cf8133c6b50f5, []int{26}
}
func (m *GetChannelWhiteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelWhiteListResp.Unmarshal(m, b)
}
func (m *GetChannelWhiteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelWhiteListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelWhiteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelWhiteListResp.Merge(dst, src)
}
func (m *GetChannelWhiteListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelWhiteListResp.Size(m)
}
func (m *GetChannelWhiteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelWhiteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelWhiteListResp proto.InternalMessageInfo

func (m *GetChannelWhiteListResp) GetWhiteList() []*ChannelBaseInfo {
	if m != nil {
		return m.WhiteList
	}
	return nil
}

// 娱乐房标签信息
type ChannelTagConfigInfo struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TagId                uint32   `protobuf:"varint,2,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelTagConfigInfo) Reset()         { *m = ChannelTagConfigInfo{} }
func (m *ChannelTagConfigInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelTagConfigInfo) ProtoMessage()    {}
func (*ChannelTagConfigInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_api_8b6cf8133c6b50f5, []int{27}
}
func (m *ChannelTagConfigInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelTagConfigInfo.Unmarshal(m, b)
}
func (m *ChannelTagConfigInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelTagConfigInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelTagConfigInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelTagConfigInfo.Merge(dst, src)
}
func (m *ChannelTagConfigInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelTagConfigInfo.Size(m)
}
func (m *ChannelTagConfigInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelTagConfigInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelTagConfigInfo proto.InternalMessageInfo

func (m *ChannelTagConfigInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelTagConfigInfo) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *ChannelTagConfigInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

// 批量获取房间标签信息
type BatchGetChannelTagReq struct {
	ChannelIdList        []uint32 `protobuf:"varint,1,rep,packed,name=channel_id_list,json=channelIdList,proto3" json:"channel_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetChannelTagReq) Reset()         { *m = BatchGetChannelTagReq{} }
func (m *BatchGetChannelTagReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelTagReq) ProtoMessage()    {}
func (*BatchGetChannelTagReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_api_8b6cf8133c6b50f5, []int{28}
}
func (m *BatchGetChannelTagReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelTagReq.Unmarshal(m, b)
}
func (m *BatchGetChannelTagReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelTagReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelTagReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelTagReq.Merge(dst, src)
}
func (m *BatchGetChannelTagReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelTagReq.Size(m)
}
func (m *BatchGetChannelTagReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelTagReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelTagReq proto.InternalMessageInfo

func (m *BatchGetChannelTagReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type BatchGetChannelTagResp struct {
	TagList              []*ChannelTagConfigInfo `protobuf:"bytes,1,rep,name=tag_list,json=tagList,proto3" json:"tag_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *BatchGetChannelTagResp) Reset()         { *m = BatchGetChannelTagResp{} }
func (m *BatchGetChannelTagResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelTagResp) ProtoMessage()    {}
func (*BatchGetChannelTagResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_api_8b6cf8133c6b50f5, []int{29}
}
func (m *BatchGetChannelTagResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelTagResp.Unmarshal(m, b)
}
func (m *BatchGetChannelTagResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelTagResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelTagResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelTagResp.Merge(dst, src)
}
func (m *BatchGetChannelTagResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelTagResp.Size(m)
}
func (m *BatchGetChannelTagResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelTagResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelTagResp proto.InternalMessageInfo

func (m *BatchGetChannelTagResp) GetTagList() []*ChannelTagConfigInfo {
	if m != nil {
		return m.TagList
	}
	return nil
}

// 推送房间消息
type PushMsgToChannelReq struct {
	BusinessMark         string   `protobuf:"bytes,1,opt,name=business_mark,json=businessMark,proto3" json:"business_mark,omitempty"`
	BusinessDetailInfo   string   `protobuf:"bytes,2,opt,name=business_detail_info,json=businessDetailInfo,proto3" json:"business_detail_info,omitempty"`
	Type                 uint32   `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	Uid                  uint32   `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,5,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	PbData               []byte   `protobuf:"bytes,6,opt,name=pb_data,json=pbData,proto3" json:"pb_data,omitempty"`
	Content              string   `protobuf:"bytes,7,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushMsgToChannelReq) Reset()         { *m = PushMsgToChannelReq{} }
func (m *PushMsgToChannelReq) String() string { return proto.CompactTextString(m) }
func (*PushMsgToChannelReq) ProtoMessage()    {}
func (*PushMsgToChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_api_8b6cf8133c6b50f5, []int{30}
}
func (m *PushMsgToChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushMsgToChannelReq.Unmarshal(m, b)
}
func (m *PushMsgToChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushMsgToChannelReq.Marshal(b, m, deterministic)
}
func (dst *PushMsgToChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushMsgToChannelReq.Merge(dst, src)
}
func (m *PushMsgToChannelReq) XXX_Size() int {
	return xxx_messageInfo_PushMsgToChannelReq.Size(m)
}
func (m *PushMsgToChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PushMsgToChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_PushMsgToChannelReq proto.InternalMessageInfo

func (m *PushMsgToChannelReq) GetBusinessMark() string {
	if m != nil {
		return m.BusinessMark
	}
	return ""
}

func (m *PushMsgToChannelReq) GetBusinessDetailInfo() string {
	if m != nil {
		return m.BusinessDetailInfo
	}
	return ""
}

func (m *PushMsgToChannelReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *PushMsgToChannelReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PushMsgToChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PushMsgToChannelReq) GetPbData() []byte {
	if m != nil {
		return m.PbData
	}
	return nil
}

func (m *PushMsgToChannelReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type PushMsgToChannelResp struct {
	RequestId            string   `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushMsgToChannelResp) Reset()         { *m = PushMsgToChannelResp{} }
func (m *PushMsgToChannelResp) String() string { return proto.CompactTextString(m) }
func (*PushMsgToChannelResp) ProtoMessage()    {}
func (*PushMsgToChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_api_8b6cf8133c6b50f5, []int{31}
}
func (m *PushMsgToChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushMsgToChannelResp.Unmarshal(m, b)
}
func (m *PushMsgToChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushMsgToChannelResp.Marshal(b, m, deterministic)
}
func (dst *PushMsgToChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushMsgToChannelResp.Merge(dst, src)
}
func (m *PushMsgToChannelResp) XXX_Size() int {
	return xxx_messageInfo_PushMsgToChannelResp.Size(m)
}
func (m *PushMsgToChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PushMsgToChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_PushMsgToChannelResp proto.InternalMessageInfo

func (m *PushMsgToChannelResp) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

func init() {
	proto.RegisterType((*ChannelSimpleInfo)(nil), "apicentergo.ChannelSimpleInfo")
	proto.RegisterType((*BatchGetChannelSimpleInfoReq)(nil), "apicentergo.BatchGetChannelSimpleInfoReq")
	proto.RegisterType((*BatchGetChannelSimpleInfoResp)(nil), "apicentergo.BatchGetChannelSimpleInfoResp")
	proto.RegisterType((*GetChannelSimpleInfoReq)(nil), "apicentergo.GetChannelSimpleInfoReq")
	proto.RegisterType((*GetChannelSimpleInfoResp)(nil), "apicentergo.GetChannelSimpleInfoResp")
	proto.RegisterType((*ReportInfo)(nil), "apicentergo.ReportInfo")
	proto.RegisterType((*ChannelBaseInfo)(nil), "apicentergo.ChannelBaseInfo")
	proto.RegisterType((*SanctionInfo)(nil), "apicentergo.SanctionInfo")
	proto.RegisterType((*ChannelProcListItem)(nil), "apicentergo.ChannelProcListItem")
	proto.RegisterType((*GetAllReportHistoryListReq)(nil), "apicentergo.GetAllReportHistoryListReq")
	proto.RegisterType((*GetAllReportHistoryListResp)(nil), "apicentergo.GetAllReportHistoryListResp")
	proto.RegisterType((*GetChannelReportStatReq)(nil), "apicentergo.GetChannelReportStatReq")
	proto.RegisterType((*GetChannelReportStatResp)(nil), "apicentergo.GetChannelReportStatResp")
	proto.RegisterType((*ReportListItem)(nil), "apicentergo.ReportListItem")
	proto.RegisterType((*GetChannelReportProcListReq)(nil), "apicentergo.GetChannelReportProcListReq")
	proto.RegisterType((*GetChannelReportProcListResp)(nil), "apicentergo.GetChannelReportProcListResp")
	proto.RegisterType((*SanctionChannelReq)(nil), "apicentergo.SanctionChannelReq")
	proto.RegisterType((*SanctionChannelResp)(nil), "apicentergo.SanctionChannelResp")
	proto.RegisterType((*BannedChannelListItem)(nil), "apicentergo.BannedChannelListItem")
	proto.RegisterType((*GetBannedChannelListReq)(nil), "apicentergo.GetBannedChannelListReq")
	proto.RegisterType((*GetBannedChannelListResp)(nil), "apicentergo.GetBannedChannelListResp")
	proto.RegisterType((*AddChannelWhiteListInfoReq)(nil), "apicentergo.AddChannelWhiteListInfoReq")
	proto.RegisterType((*AddChannelWhiteListInfoResp)(nil), "apicentergo.AddChannelWhiteListInfoResp")
	proto.RegisterType((*DelChannelWhiteListInfoReq)(nil), "apicentergo.DelChannelWhiteListInfoReq")
	proto.RegisterType((*DelChannelWhiteListInfoResp)(nil), "apicentergo.DelChannelWhiteListInfoResp")
	proto.RegisterType((*GetChannelWhiteListReq)(nil), "apicentergo.GetChannelWhiteListReq")
	proto.RegisterType((*GetChannelWhiteListResp)(nil), "apicentergo.GetChannelWhiteListResp")
	proto.RegisterType((*ChannelTagConfigInfo)(nil), "apicentergo.ChannelTagConfigInfo")
	proto.RegisterType((*BatchGetChannelTagReq)(nil), "apicentergo.BatchGetChannelTagReq")
	proto.RegisterType((*BatchGetChannelTagResp)(nil), "apicentergo.BatchGetChannelTagResp")
	proto.RegisterType((*PushMsgToChannelReq)(nil), "apicentergo.PushMsgToChannelReq")
	proto.RegisterType((*PushMsgToChannelResp)(nil), "apicentergo.PushMsgToChannelResp")
	proto.RegisterEnum("apicentergo.ENUM_SANCTION_OP_TYPE", ENUM_SANCTION_OP_TYPE_name, ENUM_SANCTION_OP_TYPE_value)
	proto.RegisterEnum("apicentergo.GetChannelSimpleInfoReq_QueryType", GetChannelSimpleInfoReq_QueryType_name, GetChannelSimpleInfoReq_QueryType_value)
}

func init() {
	proto.RegisterFile("apicenter-go/channel-api.proto", fileDescriptor_channel_api_8b6cf8133c6b50f5)
}

var fileDescriptor_channel_api_8b6cf8133c6b50f5 = []byte{
	// 1747 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x58, 0xcd, 0x72, 0xdb, 0xc8,
	0x11, 0x5e, 0x90, 0xe2, 0x0f, 0x5a, 0xa2, 0x4c, 0x43, 0x96, 0x4c, 0x49, 0x96, 0x57, 0x8b, 0xfc,
	0xa9, 0xb6, 0x62, 0x29, 0xb5, 0x29, 0x57, 0x6d, 0x6a, 0xf3, 0x53, 0x92, 0xa8, 0xd5, 0xa2, 0xca,
	0xa6, 0x64, 0x88, 0xb2, 0xca, 0xa9, 0x4a, 0x4d, 0x86, 0xc0, 0x08, 0x9a, 0x32, 0x08, 0x8c, 0x31,
	0xc3, 0xa5, 0xb5, 0xe7, 0xdc, 0x52, 0x95, 0x43, 0x0e, 0xb9, 0xe4, 0x9a, 0x4b, 0x4e, 0x7b, 0xc8,
	0x23, 0xe4, 0x25, 0xf2, 0x00, 0x79, 0x87, 0x5c, 0x53, 0xf3, 0x03, 0x12, 0x14, 0x21, 0xd9, 0xb1,
	0x73, 0xe3, 0x7c, 0x3d, 0xd3, 0x98, 0xee, 0xfe, 0xbe, 0x6e, 0x80, 0xf0, 0x18, 0x33, 0x1a, 0x90,
	0x44, 0x90, 0xec, 0x49, 0x94, 0xee, 0x05, 0x57, 0x38, 0x49, 0x48, 0xfc, 0x04, 0x33, 0xba, 0xcb,
	0xb2, 0x54, 0xa4, 0xce, 0xe2, 0xc4, 0x1e, 0xa5, 0xee, 0x3f, 0xab, 0x70, 0xff, 0x50, 0x6f, 0x39,
	0xa3, 0x43, 0x16, 0x13, 0x2f, 0xb9, 0x4c, 0x9d, 0x2d, 0x00, 0x73, 0x0e, 0xd1, 0xb0, 0x63, 0x6d,
	0x5b, 0x3b, 0x2d, 0xdf, 0x36, 0x88, 0x17, 0x4a, 0x73, 0x48, 0x39, 0x8b, 0xf1, 0xb5, 0x34, 0x57,
	0xb4, 0xd9, 0x20, 0x5e, 0xe8, 0xac, 0x42, 0x1d, 0x33, 0x26, 0x4d, 0x55, 0x65, 0xaa, 0x61, 0xc6,
	0xbc, 0xd0, 0x79, 0x08, 0x8d, 0x2b, 0xcc, 0x11, 0x1b, 0x87, 0x9d, 0x85, 0x6d, 0x6b, 0xa7, 0xe9,
	0xd7, 0xaf, 0x30, 0x3f, 0x1d, 0x87, 0xce, 0x67, 0xb0, 0x94, 0x3f, 0x4d, 0x5c, 0x33, 0xd2, 0xa9,
	0xa9, 0x53, 0x8b, 0x06, 0xeb, 0x5f, 0x33, 0x22, 0xcf, 0x0e, 0x68, 0x12, 0x4a, 0x9f, 0x75, 0x65,
	0xad, 0xcb, 0xa5, 0x17, 0x3a, 0x9f, 0xc2, 0x22, 0x1f, 0x53, 0x11, 0x5c, 0xa1, 0xcb, 0x18, 0x47,
	0x9d, 0x86, 0x32, 0x82, 0x86, 0xbe, 0x8e, 0x71, 0x24, 0x37, 0x04, 0x19, 0xc1, 0x82, 0x64, 0x68,
	0x44, 0xc3, 0x4e, 0x53, 0x6f, 0x30, 0xd0, 0x39, 0x0d, 0x9d, 0x4d, 0xb0, 0xf5, 0x0a, 0x09, 0xde,
	0xb1, 0x95, 0xb9, 0xa9, 0x81, 0x3e, 0x77, 0xd6, 0xa1, 0x49, 0x83, 0x34, 0x41, 0xc3, 0xf0, 0x69,
	0x07, 0xb6, 0xad, 0x1d, 0xdb, 0x6f, 0xc8, 0xf5, 0xf3, 0xf0, 0xa9, 0x74, 0x2c, 0x52, 0x46, 0x03,
	0x24, 0xa8, 0x88, 0x49, 0x67, 0x51, 0x59, 0x41, 0x41, 0x7d, 0x89, 0x38, 0x6b, 0x50, 0x67, 0x98,
	0xf3, 0x71, 0xd8, 0x59, 0x52, 0x36, 0xb3, 0x72, 0x1c, 0x58, 0x48, 0xf0, 0x90, 0x74, 0x5a, 0x0a,
	0x55, 0xbf, 0x65, 0xca, 0x28, 0x47, 0x21, 0x89, 0x3b, 0xcb, 0x2a, 0x35, 0x35, 0xca, 0xbb, 0x24,
	0x76, 0x7e, 0x0c, 0xf7, 0xf2, 0xcc, 0x7c, 0x4b, 0xc9, 0x58, 0x86, 0x7f, 0x4f, 0x9d, 0x6a, 0x19,
	0xf8, 0x25, 0x25, 0x63, 0x2f, 0x74, 0xbf, 0x86, 0x47, 0x07, 0x58, 0x04, 0x57, 0xc7, 0x44, 0xcc,
	0x15, 0xd3, 0x27, 0x6f, 0x8a, 0x7e, 0x68, 0x88, 0x62, 0xca, 0x45, 0xc7, 0xda, 0xae, 0xee, 0xb4,
	0x26, 0x7e, 0xbc, 0xf0, 0x19, 0xe5, 0xc2, 0x4d, 0x61, 0xeb, 0x0e, 0x3f, 0x9c, 0x39, 0x3d, 0x58,
	0xc9, 0x1d, 0x71, 0x65, 0x99, 0x3a, 0x5b, 0xfc, 0xe2, 0xf1, 0x6e, 0x81, 0x59, 0xbb, 0xf3, 0x0e,
	0xee, 0x07, 0x45, 0x48, 0x3d, 0xf0, 0x3f, 0x16, 0x3c, 0xbc, 0xed, 0xd2, 0x5b, 0x00, 0x6f, 0x46,
	0x24, 0xbb, 0xd6, 0xa4, 0x30, 0x24, 0x54, 0x88, 0xa2, 0xc4, 0x2c, 0x47, 0x2b, 0x77, 0x73, 0xb4,
	0x7a, 0x93, 0xa3, 0x25, 0x99, 0x5d, 0x28, 0xcb, 0xec, 0x05, 0xd8, 0x2f, 0x26, 0x8f, 0x5c, 0x03,
	0xe7, 0xc5, 0xf9, 0x91, 0xff, 0x0a, 0x1d, 0xbc, 0x42, 0x87, 0xdf, 0xec, 0xf7, 0x7a, 0x47, 0xcf,
	0xbc, 0x6e, 0xfb, 0x93, 0x19, 0xbc, 0xeb, 0x9d, 0x9d, 0x3e, 0xdb, 0x7f, 0xe5, 0x75, 0xdb, 0x96,
	0xb3, 0x01, 0x6b, 0x37, 0xf7, 0xbf, 0xf4, 0x8e, 0x2e, 0xbc, 0x6e, 0xbb, 0xe2, 0xfe, 0x0e, 0x3a,
	0xb7, 0x66, 0x79, 0x7f, 0x2a, 0x08, 0x9a, 0x5c, 0xa6, 0x2a, 0xf6, 0x77, 0xa7, 0x37, 0x17, 0x8c,
	0x5c, 0xb8, 0x7f, 0xb3, 0x00, 0x7c, 0xc2, 0xd2, 0x4c, 0x28, 0x41, 0xff, 0x00, 0x5a, 0x99, 0x5a,
	0xa1, 0x8c, 0x60, 0x9e, 0x26, 0xca, 0xa5, 0xed, 0x2f, 0x69, 0xd0, 0x57, 0x98, 0x64, 0xb4, 0xd9,
	0xa4, 0x32, 0xae, 0x53, 0x0a, 0x1a, 0x52, 0xf1, 0x6f, 0x80, 0x9d, 0x32, 0x24, 0x04, 0xc2, 0x41,
	0xa0, 0x52, 0x6a, 0xfb, 0x8d, 0x94, 0xf5, 0xc5, 0x7e, 0x10, 0x48, 0x19, 0xe5, 0x87, 0xb9, 0x4a,
	0x65, 0xcb, 0x6f, 0x9a, 0xa3, 0x5c, 0xca, 0x57, 0x2a, 0x65, 0x94, 0xc5, 0x4a, 0xdc, 0x52, 0x0b,
	0x34, 0x38, 0xcf, 0x62, 0xf7, 0xaf, 0x15, 0xb8, 0x67, 0x22, 0x39, 0xc0, 0xfc, 0xbd, 0x9a, 0x4f,
	0xa1, 0x5b, 0x28, 0x19, 0x55, 0x94, 0xc3, 0x3c, 0xf8, 0x9e, 0x54, 0xd3, 0x4f, 0xc1, 0xc9, 0xb7,
	0xcc, 0x71, 0xa0, 0x6d, 0x2c, 0xdd, 0x09, 0x15, 0x3e, 0x87, 0xfb, 0xc5, 0xf6, 0x83, 0x42, 0xc2,
	0x03, 0x43, 0x86, 0x7b, 0x85, 0x1e, 0xd4, 0x25, 0x3c, 0x70, 0x7e, 0x04, 0xcb, 0xe9, 0x38, 0x21,
	0x19, 0x4a, 0x68, 0xf0, 0x5a, 0x3d, 0x5e, 0xc7, 0xd3, 0x52, 0x68, 0xcf, 0x80, 0x32, 0x04, 0xbd,
	0x4d, 0x08, 0xd3, 0xb1, 0x6c, 0xdf, 0x56, 0x48, 0x5f, 0xd0, 0x52, 0xf2, 0x35, 0xca, 0xc8, 0xf7,
	0x47, 0x0b, 0x96, 0xce, 0x70, 0x12, 0x08, 0x9a, 0x26, 0x2a, 0x35, 0x3b, 0xd0, 0xe6, 0x66, 0x8d,
	0x64, 0x25, 0xa6, 0xc2, 0x58, 0xce, 0xf1, 0x13, 0xa6, 0x4a, 0xb5, 0x02, 0x35, 0xb9, 0x81, 0x9b,
	0x2a, 0x2e, 0xa4, 0x4c, 0x97, 0x21, 0x65, 0x68, 0xc4, 0x49, 0x66, 0xaa, 0x57, 0x4f, 0xd9, 0x39,
	0x27, 0x99, 0xa4, 0xc7, 0x40, 0x3e, 0x38, 0x44, 0x9c, 0x04, 0x69, 0xa2, 0xb5, 0x50, 0xf3, 0x97,
	0x34, 0x78, 0xa6, 0x30, 0xf7, 0x1f, 0x15, 0x58, 0x31, 0xb5, 0x3a, 0xcd, 0xd2, 0x40, 0xea, 0xd7,
	0x13, 0x64, 0xe8, 0xfc, 0x02, 0xec, 0x01, 0xe6, 0xa4, 0x48, 0xd5, 0x47, 0x65, 0x54, 0xcd, 0x0b,
	0xec, 0x37, 0x07, 0x79, 0xa9, 0x65, 0x7b, 0xe5, 0x68, 0x7c, 0x45, 0x85, 0xae, 0x63, 0xd3, 0x6f,
	0x50, 0x7e, 0x21, 0x97, 0x32, 0x47, 0x53, 0xc6, 0xc6, 0x28, 0x48, 0x84, 0x29, 0x60, 0x6b, 0xc2,
	0xd9, 0xf8, 0x30, 0x11, 0xce, 0x97, 0x13, 0xd2, 0xaa, 0x4e, 0xb4, 0xa0, 0x3a, 0xd1, 0xc3, 0x99,
	0xe7, 0x4f, 0x75, 0x90, 0xb3, 0x59, 0xde, 0xdd, 0xf9, 0x35, 0xb4, 0x26, 0xc9, 0x54, 0x77, 0xaf,
	0xa9, 0xbb, 0xaf, 0xcf, 0x9c, 0x2d, 0xa6, 0xdf, 0x5f, 0xe2, 0xc5, 0x62, 0xfc, 0x10, 0x96, 0x63,
	0xcc, 0x05, 0x1a, 0xb1, 0xd0, 0x4c, 0x0f, 0x3d, 0x9a, 0x96, 0x24, 0x7a, 0xae, 0xc0, 0x3e, 0x77,
	0xbf, 0xb7, 0x60, 0xe3, 0x98, 0x88, 0xfd, 0x38, 0xd6, 0xd7, 0xf8, 0x86, 0x72, 0x91, 0x66, 0xd7,
	0xf2, 0x06, 0xb2, 0xc9, 0xfd, 0x04, 0xda, 0x94, 0xa3, 0x88, 0x08, 0x34, 0xc6, 0x54, 0x20, 0x96,
	0xa5, 0x81, 0xca, 0x61, 0xd3, 0x6f, 0x51, 0x7e, 0x4c, 0xc4, 0x05, 0xa6, 0x42, 0x66, 0x5b, 0xa6,
	0x8a, 0xe1, 0x88, 0x20, 0x1a, 0xbe, 0x35, 0x35, 0x6d, 0xc8, 0xb5, 0x17, 0xbe, 0x95, 0xd2, 0x53,
	0x26, 0x4e, 0xbf, 0x23, 0x26, 0x49, 0x6a, 0xef, 0x19, 0xfd, 0x8e, 0xc8, 0x73, 0x03, 0x12, 0xd1,
	0x64, 0x2a, 0xcb, 0x86, 0x5a, 0xf7, 0xb9, 0x1c, 0x3a, 0x24, 0x09, 0xa5, 0x41, 0x4f, 0xdc, 0x1a,
	0x49, 0xc2, 0x3e, 0x77, 0xff, 0x60, 0xc1, 0xe6, 0xad, 0x37, 0xe6, 0xcc, 0xf9, 0x15, 0xd8, 0xf2,
	0x9a, 0xc5, 0xce, 0xbf, 0x5d, 0x56, 0xef, 0x22, 0x49, 0xfc, 0x26, 0x33, 0x2b, 0x59, 0x58, 0x91,
	0x0a, 0x1c, 0xab, 0xf3, 0xfa, 0xce, 0x3a, 0x9e, 0x96, 0x82, 0xe5, 0x1e, 0x79, 0x71, 0xf7, 0xcb,
	0xe2, 0x64, 0xd0, 0x37, 0x39, 0x13, 0x58, 0x98, 0xc9, 0x70, 0x47, 0x87, 0x70, 0xff, 0x54, 0x29,
	0xf6, 0xd6, 0xe2, 0x51, 0xce, 0x3e, 0x86, 0xad, 0x5b, 0x00, 0x46, 0x25, 0x92, 0x8d, 0x66, 0xe2,
	0x68, 0x44, 0x32, 0xf1, 0x53, 0x58, 0x1c, 0xe3, 0x2c, 0xa1, 0x49, 0x54, 0x60, 0x2b, 0x18, 0x48,
	0x6e, 0xd8, 0x02, 0x49, 0xbf, 0x2c, 0x15, 0xca, 0xae, 0x8b, 0x61, 0x6b, 0x44, 0x9a, 0x1f, 0x01,
	0x0c, 0xf1, 0x5b, 0x94, 0x6a, 0xb2, 0xeb, 0x92, 0x34, 0x87, 0xf8, 0xed, 0x49, 0x6c, 0xbc, 0x07,
	0x69, 0x1c, 0x93, 0x40, 0x9f, 0xae, 0x9b, 0xf7, 0x18, 0x0d, 0xc9, 0x0d, 0xeb, 0xd0, 0x8c, 0xe8,
	0xa5, 0xb6, 0xea, 0xd7, 0xa0, 0x86, 0x5c, 0x1f, 0x26, 0xc2, 0xfd, 0x97, 0x05, 0xcb, 0xfe, 0x84,
	0xf8, 0x4a, 0xb4, 0x37, 0x64, 0x63, 0x7d, 0x84, 0x6c, 0x2a, 0x1f, 0x2b, 0x9b, 0xea, 0xbc, 0x6c,
	0xca, 0xe4, 0xbf, 0x50, 0x22, 0x7f, 0x57, 0x28, 0xae, 0xce, 0x94, 0x3a, 0xe7, 0xdd, 0xbb, 0x99,
	0xf2, 0xa1, 0xa2, 0x92, 0x12, 0x79, 0x74, 0xfb, 0x63, 0x39, 0x73, 0x7e, 0x59, 0x96, 0xde, 0xcd,
	0x92, 0xf4, 0x4e, 0x04, 0x52, 0x4c, 0xf1, 0xfb, 0x4a, 0xe4, 0x7b, 0x0b, 0x9c, 0x3c, 0xd3, 0x93,
	0xbb, 0xbc, 0x33, 0xe8, 0xc2, 0x14, 0xa8, 0xcc, 0x4c, 0x01, 0x6d, 0x50, 0x43, 0x45, 0x07, 0x5c,
	0x4f, 0xf5, 0x30, 0x29, 0x1d, 0x0f, 0xad, 0xd9, 0xf1, 0x50, 0xd8, 0x64, 0x5e, 0x31, 0xf4, 0x64,
	0x34, 0x9b, 0xf4, 0x2b, 0x86, 0xbb, 0x0a, 0x2b, 0x73, 0x17, 0xe6, 0xcc, 0xfd, 0xbb, 0x05, 0xab,
	0x07, 0x5a, 0x48, 0x1a, 0xfd, 0x7f, 0x0c, 0x97, 0x9f, 0xc1, 0x83, 0x8c, 0xe0, 0x21, 0x4d, 0xd0,
	0xec, 0xe5, 0x75, 0x2a, 0x1d, 0x6d, 0x3b, 0xb8, 0x33, 0x84, 0x6a, 0x49, 0x08, 0x2f, 0x54, 0x5f,
	0x9a, 0xbb, 0xad, 0x4c, 0x7c, 0x91, 0x4e, 0xd6, 0x1d, 0x74, 0xaa, 0xdc, 0xa0, 0xd3, 0x5f, 0x2c,
	0xd5, 0xb0, 0x4a, 0x7c, 0x72, 0xe6, 0xf8, 0xb0, 0x92, 0x77, 0x1d, 0x53, 0xd4, 0x02, 0xa5, 0xdc,
	0x99, 0x5c, 0x94, 0xa6, 0xd0, 0xbf, 0x3f, 0xb8, 0x09, 0xbf, 0x37, 0xc1, 0xbe, 0x82, 0x8d, 0xfd,
	0x30, 0x3f, 0xa9, 0xe6, 0xb2, 0xf2, 0x3a, 0x7d, 0x41, 0xbf, 0xab, 0x0d, 0x6f, 0xc1, 0xe6, 0xad,
	0x87, 0x39, 0x93, 0xbe, 0xbb, 0x24, 0xfe, 0x70, 0xdf, 0xb7, 0x1e, 0xe6, 0xcc, 0xed, 0xc0, 0xda,
	0x54, 0x9e, 0x13, 0xb3, 0x4f, 0xde, 0xb8, 0x2f, 0x8b, 0x53, 0xa5, 0x60, 0xe1, 0xcc, 0xf9, 0x0a,
	0x40, 0xbd, 0x89, 0x14, 0xf3, 0x7b, 0x37, 0xd7, 0xec, 0x71, 0xee, 0xc0, 0xfd, 0x3d, 0x3c, 0x30,
	0xd6, 0x3e, 0x8e, 0x0e, 0xd3, 0xe4, 0x92, 0x46, 0xef, 0xf3, 0x32, 0xbb, 0x0a, 0x75, 0x81, 0xa3,
	0xe9, 0x07, 0x4c, 0x4d, 0xe0, 0xc8, 0x9b, 0x7e, 0x22, 0x56, 0xa7, 0x9f, 0x88, 0xee, 0x6f, 0xa4,
	0x44, 0x66, 0xbe, 0xcd, 0xfa, 0x38, 0xfa, 0x5f, 0x3e, 0xee, 0x5e, 0xc2, 0x5a, 0x99, 0x03, 0xd5,
	0xad, 0x9a, 0xf2, 0x16, 0x85, 0xb8, 0x3f, 0x2b, 0x8b, 0x7b, 0x26, 0x32, 0xbf, 0x21, 0x70, 0xa4,
	0xfc, 0xfe, 0xdb, 0x82, 0x95, 0xd3, 0x11, 0xbf, 0x7a, 0xce, 0xa3, 0x7e, 0x5a, 0x68, 0x43, 0x52,
	0x4d, 0x23, 0x4e, 0x13, 0xc2, 0x39, 0x1a, 0xe2, 0xec, 0x75, 0xfe, 0xcd, 0x91, 0x83, 0xcf, 0x71,
	0xf6, 0x5a, 0x8a, 0x74, 0xb2, 0x29, 0x24, 0x02, 0xd3, 0x78, 0x3a, 0x54, 0x6c, 0xdf, 0xc9, 0x6d,
	0x5d, 0x65, 0x52, 0x19, 0x75, 0x60, 0xa1, 0xd0, 0xa2, 0xd4, 0x6f, 0xa7, 0x0d, 0xd5, 0x11, 0xcd,
	0xdb, 0x92, 0xfc, 0x79, 0x23, 0xef, 0xb5, 0x92, 0x1e, 0xc8, 0x06, 0x28, 0xc4, 0x02, 0xab, 0x49,
	0xba, 0xe4, 0xd7, 0xd9, 0xa0, 0x8b, 0x05, 0x76, 0x3a, 0xd0, 0x08, 0xd2, 0x44, 0x10, 0x33, 0x44,
	0x6d, 0x3f, 0x5f, 0xba, 0x4f, 0xe1, 0xc1, 0x7c, 0x94, 0x9c, 0xe9, 0xa9, 0xfe, 0x66, 0x44, 0xb8,
	0xc8, 0x2b, 0x6c, 0xcb, 0xa9, 0xae, 0x10, 0x2f, 0xfc, 0xfc, 0xcf, 0x16, 0xac, 0x1e, 0xf5, 0xce,
	0x9f, 0xa3, 0xb3, 0xfd, 0xde, 0x61, 0xdf, 0x3b, 0xe9, 0xa1, 0x93, 0x53, 0xd4, 0x7f, 0x75, 0x7a,
	0xe4, 0x6c, 0xc2, 0xc3, 0xa3, 0x39, 0x14, 0xf5, 0x4e, 0x7a, 0x47, 0xed, 0x4f, 0x9c, 0xc7, 0xb0,
	0x51, 0x62, 0xbc, 0xd8, 0xf7, 0x7b, 0x5e, 0xef, 0xb8, 0x5d, 0x75, 0xb6, 0x60, 0xbd, 0xc4, 0x7e,
	0x20, 0xbf, 0x31, 0xbb, 0xed, 0x85, 0x5b, 0xcc, 0xde, 0x71, 0xef, 0xc4, 0x3f, 0x6a, 0xaf, 0x1f,
	0xec, 0xfd, 0xf6, 0x49, 0x94, 0xc6, 0x38, 0x89, 0x76, 0x9f, 0x7e, 0x21, 0xc4, 0x6e, 0x90, 0x0e,
	0xf7, 0xd4, 0x7f, 0x43, 0x41, 0x1a, 0xef, 0x71, 0x92, 0x7d, 0x4b, 0x03, 0xc2, 0xf7, 0x0a, 0x0c,
	0x18, 0xd4, 0x95, 0xf9, 0xe7, 0xff, 0x0d, 0x00, 0x00, 0xff, 0xff, 0x3c, 0x7e, 0x33, 0xb6, 0x5c,
	0x12, 0x00, 0x00,
}
