// Code generated by protoc-gen-go. DO NOT EDIT.
// source: apicenter-go/knight-group-api.proto

package apicentergo // import "golang.52tt.com/protocol/services/apicentergo"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type GetKnightScoreListReq_QueryType int32

const (
	GetKnightScoreListReq_Query_All  GetKnightScoreListReq_QueryType = 0
	GetKnightScoreListReq_Query_Ttid GetKnightScoreListReq_QueryType = 1
	GetKnightScoreListReq_Query_Uid  GetKnightScoreListReq_QueryType = 2
)

var GetKnightScoreListReq_QueryType_name = map[int32]string{
	0: "Query_All",
	1: "Query_Ttid",
	2: "Query_Uid",
}
var GetKnightScoreListReq_QueryType_value = map[string]int32{
	"Query_All":  0,
	"Query_Ttid": 1,
	"Query_Uid":  2,
}

func (x GetKnightScoreListReq_QueryType) String() string {
	return proto.EnumName(GetKnightScoreListReq_QueryType_name, int32(x))
}
func (GetKnightScoreListReq_QueryType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_knight_group_api_22ab9baed7cbdbe6, []int{1, 0}
}

type KnightScore struct {
	Ttid                         string   `protobuf:"bytes,1,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Uid                          uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname                     string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	LastMonthRemainScore         uint32   `protobuf:"varint,4,opt,name=last_month_remain_score,json=lastMonthRemainScore,proto3" json:"last_month_remain_score,omitempty"`
	NewScore                     uint32   `protobuf:"varint,5,opt,name=new_score,json=newScore,proto3" json:"new_score,omitempty"`
	TotalScore                   uint32   `protobuf:"varint,6,opt,name=total_score,json=totalScore,proto3" json:"total_score,omitempty"`
	ExchangeMoneyScore           uint32   `protobuf:"varint,7,opt,name=exchange_money_score,json=exchangeMoneyScore,proto3" json:"exchange_money_score,omitempty"`
	ExchangeTbeanScore           uint32   `protobuf:"varint,8,opt,name=exchange_tbean_score,json=exchangeTbeanScore,proto3" json:"exchange_tbean_score,omitempty"`
	ExchangeMoneyFailReturnScore uint32   `protobuf:"varint,9,opt,name=exchange_money_fail_return_score,json=exchangeMoneyFailReturnScore,proto3" json:"exchange_money_fail_return_score,omitempty"`
	OfficialReclaimScore         uint32   `protobuf:"varint,10,opt,name=official_reclaim_score,json=officialReclaimScore,proto3" json:"official_reclaim_score,omitempty"`
	OfficialGrantScore           uint32   `protobuf:"varint,11,opt,name=official_grant_score,json=officialGrantScore,proto3" json:"official_grant_score,omitempty"`
	RemainScore                  uint32   `protobuf:"varint,12,opt,name=remain_score,json=remainScore,proto3" json:"remain_score,omitempty"`
	RemainExchangeMoney          uint32   `protobuf:"varint,13,opt,name=remain_exchange_money,json=remainExchangeMoney,proto3" json:"remain_exchange_money,omitempty"`
	GuildKnightScore             uint32   `protobuf:"varint,14,opt,name=guild_knight_score,json=guildKnightScore,proto3" json:"guild_knight_score,omitempty"`
	XXX_NoUnkeyedLiteral         struct{} `json:"-"`
	XXX_unrecognized             []byte   `json:"-"`
	XXX_sizecache                int32    `json:"-"`
}

func (m *KnightScore) Reset()         { *m = KnightScore{} }
func (m *KnightScore) String() string { return proto.CompactTextString(m) }
func (*KnightScore) ProtoMessage()    {}
func (*KnightScore) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_group_api_22ab9baed7cbdbe6, []int{0}
}
func (m *KnightScore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KnightScore.Unmarshal(m, b)
}
func (m *KnightScore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KnightScore.Marshal(b, m, deterministic)
}
func (dst *KnightScore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KnightScore.Merge(dst, src)
}
func (m *KnightScore) XXX_Size() int {
	return xxx_messageInfo_KnightScore.Size(m)
}
func (m *KnightScore) XXX_DiscardUnknown() {
	xxx_messageInfo_KnightScore.DiscardUnknown(m)
}

var xxx_messageInfo_KnightScore proto.InternalMessageInfo

func (m *KnightScore) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *KnightScore) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *KnightScore) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *KnightScore) GetLastMonthRemainScore() uint32 {
	if m != nil {
		return m.LastMonthRemainScore
	}
	return 0
}

func (m *KnightScore) GetNewScore() uint32 {
	if m != nil {
		return m.NewScore
	}
	return 0
}

func (m *KnightScore) GetTotalScore() uint32 {
	if m != nil {
		return m.TotalScore
	}
	return 0
}

func (m *KnightScore) GetExchangeMoneyScore() uint32 {
	if m != nil {
		return m.ExchangeMoneyScore
	}
	return 0
}

func (m *KnightScore) GetExchangeTbeanScore() uint32 {
	if m != nil {
		return m.ExchangeTbeanScore
	}
	return 0
}

func (m *KnightScore) GetExchangeMoneyFailReturnScore() uint32 {
	if m != nil {
		return m.ExchangeMoneyFailReturnScore
	}
	return 0
}

func (m *KnightScore) GetOfficialReclaimScore() uint32 {
	if m != nil {
		return m.OfficialReclaimScore
	}
	return 0
}

func (m *KnightScore) GetOfficialGrantScore() uint32 {
	if m != nil {
		return m.OfficialGrantScore
	}
	return 0
}

func (m *KnightScore) GetRemainScore() uint32 {
	if m != nil {
		return m.RemainScore
	}
	return 0
}

func (m *KnightScore) GetRemainExchangeMoney() uint32 {
	if m != nil {
		return m.RemainExchangeMoney
	}
	return 0
}

func (m *KnightScore) GetGuildKnightScore() uint32 {
	if m != nil {
		return m.GuildKnightScore
	}
	return 0
}

type GetKnightScoreListReq struct {
	QueryType            uint32   `protobuf:"varint,1,opt,name=query_type,json=queryType,proto3" json:"query_type,omitempty"`
	BeginTs              uint32   `protobuf:"varint,2,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,3,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	TtidList             []string `protobuf:"bytes,4,rep,name=ttid_list,json=ttidList,proto3" json:"ttid_list,omitempty"`
	UidList              []uint32 `protobuf:"varint,5,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	MinRemainScore       uint32   `protobuf:"varint,6,opt,name=min_remain_score,json=minRemainScore,proto3" json:"min_remain_score,omitempty"`
	Page                 uint32   `protobuf:"varint,7,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,8,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetKnightScoreListReq) Reset()         { *m = GetKnightScoreListReq{} }
func (m *GetKnightScoreListReq) String() string { return proto.CompactTextString(m) }
func (*GetKnightScoreListReq) ProtoMessage()    {}
func (*GetKnightScoreListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_group_api_22ab9baed7cbdbe6, []int{1}
}
func (m *GetKnightScoreListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetKnightScoreListReq.Unmarshal(m, b)
}
func (m *GetKnightScoreListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetKnightScoreListReq.Marshal(b, m, deterministic)
}
func (dst *GetKnightScoreListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetKnightScoreListReq.Merge(dst, src)
}
func (m *GetKnightScoreListReq) XXX_Size() int {
	return xxx_messageInfo_GetKnightScoreListReq.Size(m)
}
func (m *GetKnightScoreListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetKnightScoreListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetKnightScoreListReq proto.InternalMessageInfo

func (m *GetKnightScoreListReq) GetQueryType() uint32 {
	if m != nil {
		return m.QueryType
	}
	return 0
}

func (m *GetKnightScoreListReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *GetKnightScoreListReq) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *GetKnightScoreListReq) GetTtidList() []string {
	if m != nil {
		return m.TtidList
	}
	return nil
}

func (m *GetKnightScoreListReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetKnightScoreListReq) GetMinRemainScore() uint32 {
	if m != nil {
		return m.MinRemainScore
	}
	return 0
}

func (m *GetKnightScoreListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetKnightScoreListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetKnightScoreListResp struct {
	List                 []*KnightScore `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	TotalCnt             uint32         `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	NextPage             uint32         `protobuf:"varint,3,opt,name=next_page,json=nextPage,proto3" json:"next_page,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetKnightScoreListResp) Reset()         { *m = GetKnightScoreListResp{} }
func (m *GetKnightScoreListResp) String() string { return proto.CompactTextString(m) }
func (*GetKnightScoreListResp) ProtoMessage()    {}
func (*GetKnightScoreListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_group_api_22ab9baed7cbdbe6, []int{2}
}
func (m *GetKnightScoreListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetKnightScoreListResp.Unmarshal(m, b)
}
func (m *GetKnightScoreListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetKnightScoreListResp.Marshal(b, m, deterministic)
}
func (dst *GetKnightScoreListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetKnightScoreListResp.Merge(dst, src)
}
func (m *GetKnightScoreListResp) XXX_Size() int {
	return xxx_messageInfo_GetKnightScoreListResp.Size(m)
}
func (m *GetKnightScoreListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetKnightScoreListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetKnightScoreListResp proto.InternalMessageInfo

func (m *GetKnightScoreListResp) GetList() []*KnightScore {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetKnightScoreListResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

func (m *GetKnightScoreListResp) GetNextPage() uint32 {
	if m != nil {
		return m.NextPage
	}
	return 0
}

func init() {
	proto.RegisterType((*KnightScore)(nil), "apicentergo.KnightScore")
	proto.RegisterType((*GetKnightScoreListReq)(nil), "apicentergo.GetKnightScoreListReq")
	proto.RegisterType((*GetKnightScoreListResp)(nil), "apicentergo.GetKnightScoreListResp")
	proto.RegisterEnum("apicentergo.GetKnightScoreListReq_QueryType", GetKnightScoreListReq_QueryType_name, GetKnightScoreListReq_QueryType_value)
}

func init() {
	proto.RegisterFile("apicenter-go/knight-group-api.proto", fileDescriptor_knight_group_api_22ab9baed7cbdbe6)
}

var fileDescriptor_knight_group_api_22ab9baed7cbdbe6 = []byte{
	// 608 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x6c, 0x94, 0xcf, 0x72, 0xd3, 0x30,
	0x10, 0xc6, 0x49, 0x9d, 0xb6, 0xc9, 0xa6, 0xe9, 0x64, 0x44, 0x5b, 0xcc, 0xbf, 0x21, 0x84, 0x4b,
	0x0e, 0x4d, 0xc2, 0x14, 0x7a, 0xe0, 0x08, 0x0c, 0xed, 0x01, 0x3a, 0x03, 0x6e, 0xb8, 0x70, 0xf1,
	0xa8, 0xf6, 0xd6, 0xd5, 0xd4, 0x96, 0x5d, 0x7b, 0x4d, 0x93, 0x9e, 0x78, 0x2a, 0xde, 0x86, 0x77,
	0x61, 0xb4, 0xb2, 0x83, 0x03, 0x9c, 0x62, 0xe9, 0xf7, 0x7d, 0xbb, 0xd2, 0xce, 0xa7, 0xc0, 0x0b,
	0x99, 0xa9, 0x00, 0x35, 0x61, 0x3e, 0x89, 0xd2, 0xd9, 0xb5, 0x56, 0xd1, 0x15, 0x4d, 0xa2, 0x3c,
	0x2d, 0xb3, 0x89, 0xcc, 0xd4, 0x34, 0xcb, 0x53, 0x4a, 0x45, 0x6f, 0x25, 0x8a, 0xd2, 0xd1, 0xaf,
	0x36, 0xf4, 0x3e, 0xb2, 0xee, 0x3c, 0x48, 0x73, 0x14, 0x02, 0xda, 0x44, 0x2a, 0x74, 0x5b, 0xc3,
	0xd6, 0xb8, 0xeb, 0xf1, 0xb7, 0x18, 0x80, 0x53, 0xaa, 0xd0, 0xdd, 0x18, 0xb6, 0xc6, 0x7d, 0xcf,
	0x7c, 0x8a, 0x47, 0xd0, 0xd1, 0x2a, 0xb8, 0xd6, 0x32, 0x41, 0xd7, 0x61, 0xe5, 0x6a, 0x2d, 0x8e,
	0xe1, 0x41, 0x2c, 0x0b, 0xf2, 0x93, 0x54, 0xd3, 0x95, 0x9f, 0x63, 0x22, 0x95, 0xf6, 0x0b, 0x53,
	0xdc, 0x6d, 0x73, 0x85, 0x3d, 0x83, 0xcf, 0x0c, 0xf5, 0x18, 0xda, 0xc6, 0x8f, 0xa1, 0xab, 0xf1,
	0xb6, 0x12, 0x6e, 0xb2, 0xb0, 0xa3, 0xf1, 0xd6, 0xc2, 0x67, 0xd0, 0xa3, 0x94, 0x64, 0x5c, 0xe1,
	0x2d, 0xc6, 0xc0, 0x5b, 0x56, 0xf0, 0x12, 0xf6, 0x70, 0x11, 0x5c, 0x49, 0x1d, 0xa1, 0x69, 0x8c,
	0xcb, 0x4a, 0xb9, 0xcd, 0x4a, 0x51, 0xb3, 0x33, 0x83, 0xfe, 0x75, 0xd0, 0x05, 0xca, 0xfa, 0x8c,
	0x9d, 0x75, 0xc7, 0xdc, 0x20, 0xeb, 0x38, 0x81, 0xe1, 0x5f, 0x3d, 0x2e, 0xa5, 0x8a, 0xfd, 0x1c,
	0xa9, 0xcc, 0x6b, 0x77, 0x97, 0xdd, 0x4f, 0xd6, 0xfa, 0x9d, 0x48, 0x15, 0x7b, 0x2c, 0xb2, 0x75,
	0x5e, 0xc3, 0x41, 0x7a, 0x79, 0xa9, 0x02, 0x25, 0x8d, 0x39, 0x88, 0xa5, 0x4a, 0x2a, 0x37, 0xd8,
	0xf9, 0xd4, 0xd4, 0xb3, 0x70, 0x75, 0xde, 0x95, 0x2b, 0xca, 0xa5, 0xa6, 0xca, 0xd3, 0xb3, 0xe7,
	0xad, 0xd9, 0xa9, 0x41, 0xd6, 0xf1, 0x1c, 0x76, 0xd6, 0xa6, 0xbf, 0xc3, 0xca, 0x5e, 0xde, 0x18,
	0xfa, 0x11, 0xec, 0x57, 0x92, 0xf5, 0x9b, 0xb9, 0x7d, 0xd6, 0xde, 0xb7, 0xf0, 0x43, 0xf3, 0x36,
	0xe2, 0x10, 0x44, 0x54, 0xaa, 0x38, 0xf4, 0x6d, 0xbc, 0xaa, 0xe2, 0xbb, 0x6c, 0x18, 0x30, 0x69,
	0xe4, 0x69, 0xf4, 0x73, 0x03, 0xf6, 0x4f, 0x91, 0x1a, 0x5b, 0x9f, 0x54, 0x41, 0x1e, 0xde, 0x88,
	0xa7, 0x00, 0x37, 0x25, 0xe6, 0x4b, 0x9f, 0x96, 0x19, 0x72, 0xde, 0xfa, 0x5e, 0x97, 0x77, 0xe6,
	0xcb, 0x0c, 0xc5, 0x43, 0xe8, 0x5c, 0x60, 0xa4, 0xb4, 0x4f, 0x45, 0x95, 0xbc, 0x6d, 0x5e, 0xcf,
	0x0b, 0xb1, 0x0f, 0x5b, 0xa8, 0x43, 0x03, 0x1c, 0x06, 0x9b, 0xa8, 0xc3, 0x79, 0x61, 0x12, 0x64,
	0xe2, 0xea, 0xc7, 0xaa, 0x20, 0xb7, 0x3d, 0x74, 0x4c, 0x2a, 0xcd, 0x86, 0x69, 0x68, 0xca, 0x95,
	0x35, 0xdb, 0x1c, 0x3a, 0xa6, 0x5c, 0x59, 0xa1, 0x31, 0x0c, 0x12, 0xa5, 0xd7, 0x93, 0x6a, 0x13,
	0xb6, 0x9b, 0x28, 0xdd, 0xcc, 0xa8, 0x80, 0x76, 0x26, 0xa3, 0x3a, 0x55, 0xfc, 0x6d, 0xba, 0x9a,
	0x5f, 0xbf, 0x50, 0x77, 0x75, 0x78, 0x3a, 0x66, 0xe3, 0x5c, 0xdd, 0xe1, 0xe8, 0x0d, 0x74, 0xbf,
	0xac, 0x6e, 0xd4, 0xaf, 0x16, 0xfe, 0xdb, 0x38, 0x1e, 0xdc, 0x13, 0xbb, 0x00, 0x76, 0x39, 0x27,
	0x15, 0x0e, 0x5a, 0x7f, 0xf0, 0x57, 0x15, 0x0e, 0x36, 0x46, 0x3f, 0x5a, 0x70, 0xf0, 0xbf, 0xc1,
	0x15, 0x99, 0x38, 0x84, 0x36, 0xdf, 0xa3, 0x35, 0x74, 0xc6, 0xbd, 0x23, 0x77, 0xda, 0x78, 0xcf,
	0xd3, 0x86, 0xde, 0x63, 0x15, 0x8f, 0x85, 0xdf, 0x4e, 0xa0, 0xa9, 0x9a, 0x64, 0x87, 0x37, 0xde,
	0x6b, 0xb2, 0xaf, 0x6e, 0x41, 0x3e, 0x5f, 0xcb, 0xa9, 0x5f, 0xdd, 0x82, 0x3e, 0xcb, 0x08, 0xdf,
	0xcd, 0xbe, 0x4d, 0xa2, 0x34, 0x96, 0x3a, 0x9a, 0x1e, 0x1f, 0x11, 0x4d, 0x83, 0x34, 0x99, 0xf1,
	0x3f, 0x48, 0x90, 0xc6, 0xb3, 0x02, 0xf3, 0xef, 0x2a, 0xc0, 0x62, 0xd6, 0x68, 0x7e, 0xb1, 0xc5,
	0xf8, 0xd5, 0xef, 0x00, 0x00, 0x00, 0xff, 0xff, 0xc8, 0x27, 0x40, 0xcf, 0x87, 0x04, 0x00, 0x00,
}
