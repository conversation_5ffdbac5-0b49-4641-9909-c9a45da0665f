// Code generated by protoc-gen-go. DO NOT EDIT.
// source: apicenter-go/comm_api.proto

package apicentergo // import "golang.52tt.com/protocol/services/apicentergo"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type BreakingNewsType int32

const (
	BreakingNewsType_BreakingNewsType_Common         BreakingNewsType = 0
	BreakingNewsType_BreakingNewsType_Halloween      BreakingNewsType = 1
	BreakingNewsType_BreakingNewsType_SpringFestival BreakingNewsType = 2
)

var BreakingNewsType_name = map[int32]string{
	0: "BreakingNewsType_Common",
	1: "BreakingNewsType_Halloween",
	2: "BreakingNewsType_SpringFestival",
}
var BreakingNewsType_value = map[string]int32{
	"BreakingNewsType_Common":         0,
	"BreakingNewsType_Halloween":      1,
	"BreakingNewsType_SpringFestival": 2,
}

func (x BreakingNewsType) String() string {
	return proto.EnumName(BreakingNewsType_name, int32(x))
}
func (BreakingNewsType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_comm_api_e43c0e5b600edded, []int{0}
}

// 违规类型
type ViolationsType int32

const (
	ViolationsType_Violations_Invalid ViolationsType = 0
	ViolationsType_Violations_A       ViolationsType = 1
	ViolationsType_Violations_B       ViolationsType = 2
	ViolationsType_Violations_C       ViolationsType = 3
)

var ViolationsType_name = map[int32]string{
	0: "Violations_Invalid",
	1: "Violations_A",
	2: "Violations_B",
	3: "Violations_C",
}
var ViolationsType_value = map[string]int32{
	"Violations_Invalid": 0,
	"Violations_A":       1,
	"Violations_B":       2,
	"Violations_C":       3,
}

func (x ViolationsType) String() string {
	return proto.EnumName(ViolationsType_name, int32(x))
}
func (ViolationsType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_comm_api_e43c0e5b600edded, []int{1}
}

// 用户消息推送
type PushMsgToUserReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	PushCmd              uint32   `protobuf:"varint,2,opt,name=push_cmd,json=pushCmd,proto3" json:"push_cmd,omitempty"`
	Content              []byte   `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushMsgToUserReq) Reset()         { *m = PushMsgToUserReq{} }
func (m *PushMsgToUserReq) String() string { return proto.CompactTextString(m) }
func (*PushMsgToUserReq) ProtoMessage()    {}
func (*PushMsgToUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_comm_api_e43c0e5b600edded, []int{0}
}
func (m *PushMsgToUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushMsgToUserReq.Unmarshal(m, b)
}
func (m *PushMsgToUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushMsgToUserReq.Marshal(b, m, deterministic)
}
func (dst *PushMsgToUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushMsgToUserReq.Merge(dst, src)
}
func (m *PushMsgToUserReq) XXX_Size() int {
	return xxx_messageInfo_PushMsgToUserReq.Size(m)
}
func (m *PushMsgToUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PushMsgToUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_PushMsgToUserReq proto.InternalMessageInfo

func (m *PushMsgToUserReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *PushMsgToUserReq) GetPushCmd() uint32 {
	if m != nil {
		return m.PushCmd
	}
	return 0
}

func (m *PushMsgToUserReq) GetContent() []byte {
	if m != nil {
		return m.Content
	}
	return nil
}

type PushMsgToUserResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushMsgToUserResp) Reset()         { *m = PushMsgToUserResp{} }
func (m *PushMsgToUserResp) String() string { return proto.CompactTextString(m) }
func (*PushMsgToUserResp) ProtoMessage()    {}
func (*PushMsgToUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_comm_api_e43c0e5b600edded, []int{1}
}
func (m *PushMsgToUserResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushMsgToUserResp.Unmarshal(m, b)
}
func (m *PushMsgToUserResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushMsgToUserResp.Marshal(b, m, deterministic)
}
func (dst *PushMsgToUserResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushMsgToUserResp.Merge(dst, src)
}
func (m *PushMsgToUserResp) XXX_Size() int {
	return xxx_messageInfo_PushMsgToUserResp.Size(m)
}
func (m *PushMsgToUserResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PushMsgToUserResp.DiscardUnknown(m)
}

var xxx_messageInfo_PushMsgToUserResp proto.InternalMessageInfo

// excel信息
type ExcelData struct {
	ColData              []string `protobuf:"bytes,1,rep,name=col_data,json=colData,proto3" json:"col_data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExcelData) Reset()         { *m = ExcelData{} }
func (m *ExcelData) String() string { return proto.CompactTextString(m) }
func (*ExcelData) ProtoMessage()    {}
func (*ExcelData) Descriptor() ([]byte, []int) {
	return fileDescriptor_comm_api_e43c0e5b600edded, []int{2}
}
func (m *ExcelData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExcelData.Unmarshal(m, b)
}
func (m *ExcelData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExcelData.Marshal(b, m, deterministic)
}
func (dst *ExcelData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExcelData.Merge(dst, src)
}
func (m *ExcelData) XXX_Size() int {
	return xxx_messageInfo_ExcelData.Size(m)
}
func (m *ExcelData) XXX_DiscardUnknown() {
	xxx_messageInfo_ExcelData.DiscardUnknown(m)
}

var xxx_messageInfo_ExcelData proto.InternalMessageInfo

func (m *ExcelData) GetColData() []string {
	if m != nil {
		return m.ColData
	}
	return nil
}

// 推送的excel数据请求
type PushFromExcelReq struct {
	Template             string       `protobuf:"bytes,1,opt,name=template,proto3" json:"template,omitempty"`
	ExcelData            []*ExcelData `protobuf:"bytes,2,rep,name=excel_data,json=excelData,proto3" json:"excel_data,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PushFromExcelReq) Reset()         { *m = PushFromExcelReq{} }
func (m *PushFromExcelReq) String() string { return proto.CompactTextString(m) }
func (*PushFromExcelReq) ProtoMessage()    {}
func (*PushFromExcelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_comm_api_e43c0e5b600edded, []int{3}
}
func (m *PushFromExcelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushFromExcelReq.Unmarshal(m, b)
}
func (m *PushFromExcelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushFromExcelReq.Marshal(b, m, deterministic)
}
func (dst *PushFromExcelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushFromExcelReq.Merge(dst, src)
}
func (m *PushFromExcelReq) XXX_Size() int {
	return xxx_messageInfo_PushFromExcelReq.Size(m)
}
func (m *PushFromExcelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PushFromExcelReq.DiscardUnknown(m)
}

var xxx_messageInfo_PushFromExcelReq proto.InternalMessageInfo

func (m *PushFromExcelReq) GetTemplate() string {
	if m != nil {
		return m.Template
	}
	return ""
}

func (m *PushFromExcelReq) GetExcelData() []*ExcelData {
	if m != nil {
		return m.ExcelData
	}
	return nil
}

// 推送的excel数据响应
type PushFromExcelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushFromExcelResp) Reset()         { *m = PushFromExcelResp{} }
func (m *PushFromExcelResp) String() string { return proto.CompactTextString(m) }
func (*PushFromExcelResp) ProtoMessage()    {}
func (*PushFromExcelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_comm_api_e43c0e5b600edded, []int{4}
}
func (m *PushFromExcelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushFromExcelResp.Unmarshal(m, b)
}
func (m *PushFromExcelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushFromExcelResp.Marshal(b, m, deterministic)
}
func (dst *PushFromExcelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushFromExcelResp.Merge(dst, src)
}
func (m *PushFromExcelResp) XXX_Size() int {
	return xxx_messageInfo_PushFromExcelResp.Size(m)
}
func (m *PushFromExcelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PushFromExcelResp.DiscardUnknown(m)
}

var xxx_messageInfo_PushFromExcelResp proto.InternalMessageInfo

// 活动通用全服公告接口
type PushActBreakingNewsReq struct {
	FromUid              uint32   `protobuf:"varint,1,opt,name=from_uid,json=fromUid,proto3" json:"from_uid,omitempty"`
	FromCid              uint32   `protobuf:"varint,2,opt,name=from_cid,json=fromCid,proto3" json:"from_cid,omitempty"`
	NewsPrefix           string   `protobuf:"bytes,3,opt,name=news_prefix,json=newsPrefix,proto3" json:"news_prefix,omitempty"`
	NewsContent          string   `protobuf:"bytes,4,opt,name=news_content,json=newsContent,proto3" json:"news_content,omitempty"`
	JumpUrl              string   `protobuf:"bytes,5,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	HardUrl              string   `protobuf:"bytes,6,opt,name=hard_url,json=hardUrl,proto3" json:"hard_url,omitempty"`
	Type                 uint32   `protobuf:"varint,7,opt,name=type,proto3" json:"type,omitempty"`
	TargetUid            uint32   `protobuf:"varint,8,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushActBreakingNewsReq) Reset()         { *m = PushActBreakingNewsReq{} }
func (m *PushActBreakingNewsReq) String() string { return proto.CompactTextString(m) }
func (*PushActBreakingNewsReq) ProtoMessage()    {}
func (*PushActBreakingNewsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_comm_api_e43c0e5b600edded, []int{5}
}
func (m *PushActBreakingNewsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushActBreakingNewsReq.Unmarshal(m, b)
}
func (m *PushActBreakingNewsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushActBreakingNewsReq.Marshal(b, m, deterministic)
}
func (dst *PushActBreakingNewsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushActBreakingNewsReq.Merge(dst, src)
}
func (m *PushActBreakingNewsReq) XXX_Size() int {
	return xxx_messageInfo_PushActBreakingNewsReq.Size(m)
}
func (m *PushActBreakingNewsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PushActBreakingNewsReq.DiscardUnknown(m)
}

var xxx_messageInfo_PushActBreakingNewsReq proto.InternalMessageInfo

func (m *PushActBreakingNewsReq) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *PushActBreakingNewsReq) GetFromCid() uint32 {
	if m != nil {
		return m.FromCid
	}
	return 0
}

func (m *PushActBreakingNewsReq) GetNewsPrefix() string {
	if m != nil {
		return m.NewsPrefix
	}
	return ""
}

func (m *PushActBreakingNewsReq) GetNewsContent() string {
	if m != nil {
		return m.NewsContent
	}
	return ""
}

func (m *PushActBreakingNewsReq) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *PushActBreakingNewsReq) GetHardUrl() string {
	if m != nil {
		return m.HardUrl
	}
	return ""
}

func (m *PushActBreakingNewsReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *PushActBreakingNewsReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type PushActBreakingNewsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushActBreakingNewsResp) Reset()         { *m = PushActBreakingNewsResp{} }
func (m *PushActBreakingNewsResp) String() string { return proto.CompactTextString(m) }
func (*PushActBreakingNewsResp) ProtoMessage()    {}
func (*PushActBreakingNewsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_comm_api_e43c0e5b600edded, []int{6}
}
func (m *PushActBreakingNewsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushActBreakingNewsResp.Unmarshal(m, b)
}
func (m *PushActBreakingNewsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushActBreakingNewsResp.Marshal(b, m, deterministic)
}
func (dst *PushActBreakingNewsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushActBreakingNewsResp.Merge(dst, src)
}
func (m *PushActBreakingNewsResp) XXX_Size() int {
	return xxx_messageInfo_PushActBreakingNewsResp.Size(m)
}
func (m *PushActBreakingNewsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PushActBreakingNewsResp.DiscardUnknown(m)
}

var xxx_messageInfo_PushActBreakingNewsResp proto.InternalMessageInfo

// 滚刀推送
type HobPushReq struct {
	Template             string       `protobuf:"bytes,1,opt,name=template,proto3" json:"template,omitempty"`
	Placeholder          string       `protobuf:"bytes,2,opt,name=placeholder,proto3" json:"placeholder,omitempty"`
	ExcelData            []*ExcelData `protobuf:"bytes,3,rep,name=excel_data,json=excelData,proto3" json:"excel_data,omitempty"`
	Operator             string       `protobuf:"bytes,4,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *HobPushReq) Reset()         { *m = HobPushReq{} }
func (m *HobPushReq) String() string { return proto.CompactTextString(m) }
func (*HobPushReq) ProtoMessage()    {}
func (*HobPushReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_comm_api_e43c0e5b600edded, []int{7}
}
func (m *HobPushReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HobPushReq.Unmarshal(m, b)
}
func (m *HobPushReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HobPushReq.Marshal(b, m, deterministic)
}
func (dst *HobPushReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HobPushReq.Merge(dst, src)
}
func (m *HobPushReq) XXX_Size() int {
	return xxx_messageInfo_HobPushReq.Size(m)
}
func (m *HobPushReq) XXX_DiscardUnknown() {
	xxx_messageInfo_HobPushReq.DiscardUnknown(m)
}

var xxx_messageInfo_HobPushReq proto.InternalMessageInfo

func (m *HobPushReq) GetTemplate() string {
	if m != nil {
		return m.Template
	}
	return ""
}

func (m *HobPushReq) GetPlaceholder() string {
	if m != nil {
		return m.Placeholder
	}
	return ""
}

func (m *HobPushReq) GetExcelData() []*ExcelData {
	if m != nil {
		return m.ExcelData
	}
	return nil
}

func (m *HobPushReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type HobPushResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HobPushResp) Reset()         { *m = HobPushResp{} }
func (m *HobPushResp) String() string { return proto.CompactTextString(m) }
func (*HobPushResp) ProtoMessage()    {}
func (*HobPushResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_comm_api_e43c0e5b600edded, []int{8}
}
func (m *HobPushResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HobPushResp.Unmarshal(m, b)
}
func (m *HobPushResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HobPushResp.Marshal(b, m, deterministic)
}
func (dst *HobPushResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HobPushResp.Merge(dst, src)
}
func (m *HobPushResp) XXX_Size() int {
	return xxx_messageInfo_HobPushResp.Size(m)
}
func (m *HobPushResp) XXX_DiscardUnknown() {
	xxx_messageInfo_HobPushResp.DiscardUnknown(m)
}

var xxx_messageInfo_HobPushResp proto.InternalMessageInfo

// 滚刀推送查询
type GetHobPushRecordReq struct {
	FromHobTtid          string   `protobuf:"bytes,1,opt,name=from_hob_ttid,json=fromHobTtid,proto3" json:"from_hob_ttid,omitempty"`
	ToHobTtid            string   `protobuf:"bytes,2,opt,name=to_hob_ttid,json=toHobTtid,proto3" json:"to_hob_ttid,omitempty"`
	Operator             string   `protobuf:"bytes,3,opt,name=operator,proto3" json:"operator,omitempty"`
	BeginTime            int64    `protobuf:"varint,4,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Page                 uint32   `protobuf:"varint,6,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,7,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetHobPushRecordReq) Reset()         { *m = GetHobPushRecordReq{} }
func (m *GetHobPushRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetHobPushRecordReq) ProtoMessage()    {}
func (*GetHobPushRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_comm_api_e43c0e5b600edded, []int{9}
}
func (m *GetHobPushRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHobPushRecordReq.Unmarshal(m, b)
}
func (m *GetHobPushRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHobPushRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetHobPushRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHobPushRecordReq.Merge(dst, src)
}
func (m *GetHobPushRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetHobPushRecordReq.Size(m)
}
func (m *GetHobPushRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHobPushRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetHobPushRecordReq proto.InternalMessageInfo

func (m *GetHobPushRecordReq) GetFromHobTtid() string {
	if m != nil {
		return m.FromHobTtid
	}
	return ""
}

func (m *GetHobPushRecordReq) GetToHobTtid() string {
	if m != nil {
		return m.ToHobTtid
	}
	return ""
}

func (m *GetHobPushRecordReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *GetHobPushRecordReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetHobPushRecordReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetHobPushRecordReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetHobPushRecordReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type HobPushRecord struct {
	FromHobTtid          string   `protobuf:"bytes,1,opt,name=from_hob_ttid,json=fromHobTtid,proto3" json:"from_hob_ttid,omitempty"`
	ToHobTtid            string   `protobuf:"bytes,2,opt,name=to_hob_ttid,json=toHobTtid,proto3" json:"to_hob_ttid,omitempty"`
	Content              string   `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	Operator             string   `protobuf:"bytes,4,opt,name=operator,proto3" json:"operator,omitempty"`
	OpTime               int64    `protobuf:"varint,5,opt,name=op_time,json=opTime,proto3" json:"op_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HobPushRecord) Reset()         { *m = HobPushRecord{} }
func (m *HobPushRecord) String() string { return proto.CompactTextString(m) }
func (*HobPushRecord) ProtoMessage()    {}
func (*HobPushRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_comm_api_e43c0e5b600edded, []int{10}
}
func (m *HobPushRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HobPushRecord.Unmarshal(m, b)
}
func (m *HobPushRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HobPushRecord.Marshal(b, m, deterministic)
}
func (dst *HobPushRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HobPushRecord.Merge(dst, src)
}
func (m *HobPushRecord) XXX_Size() int {
	return xxx_messageInfo_HobPushRecord.Size(m)
}
func (m *HobPushRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_HobPushRecord.DiscardUnknown(m)
}

var xxx_messageInfo_HobPushRecord proto.InternalMessageInfo

func (m *HobPushRecord) GetFromHobTtid() string {
	if m != nil {
		return m.FromHobTtid
	}
	return ""
}

func (m *HobPushRecord) GetToHobTtid() string {
	if m != nil {
		return m.ToHobTtid
	}
	return ""
}

func (m *HobPushRecord) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *HobPushRecord) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *HobPushRecord) GetOpTime() int64 {
	if m != nil {
		return m.OpTime
	}
	return 0
}

type GetHobPushRecordResp struct {
	RecordList           []*HobPushRecord `protobuf:"bytes,1,rep,name=record_list,json=recordList,proto3" json:"record_list,omitempty"`
	TotalCnt             uint32           `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetHobPushRecordResp) Reset()         { *m = GetHobPushRecordResp{} }
func (m *GetHobPushRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetHobPushRecordResp) ProtoMessage()    {}
func (*GetHobPushRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_comm_api_e43c0e5b600edded, []int{11}
}
func (m *GetHobPushRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHobPushRecordResp.Unmarshal(m, b)
}
func (m *GetHobPushRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHobPushRecordResp.Marshal(b, m, deterministic)
}
func (dst *GetHobPushRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHobPushRecordResp.Merge(dst, src)
}
func (m *GetHobPushRecordResp) XXX_Size() int {
	return xxx_messageInfo_GetHobPushRecordResp.Size(m)
}
func (m *GetHobPushRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHobPushRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetHobPushRecordResp proto.InternalMessageInfo

func (m *GetHobPushRecordResp) GetRecordList() []*HobPushRecord {
	if m != nil {
		return m.RecordList
	}
	return nil
}

func (m *GetHobPushRecordResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

// 违规信息
type ViolationsInfo struct {
	ViolationsType       uint32   `protobuf:"varint,1,opt,name=violations_type,json=violationsType,proto3" json:"violations_type,omitempty"`
	ViolationsCnt        uint32   `protobuf:"varint,2,opt,name=violations_cnt,json=violationsCnt,proto3" json:"violations_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ViolationsInfo) Reset()         { *m = ViolationsInfo{} }
func (m *ViolationsInfo) String() string { return proto.CompactTextString(m) }
func (*ViolationsInfo) ProtoMessage()    {}
func (*ViolationsInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_comm_api_e43c0e5b600edded, []int{12}
}
func (m *ViolationsInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ViolationsInfo.Unmarshal(m, b)
}
func (m *ViolationsInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ViolationsInfo.Marshal(b, m, deterministic)
}
func (dst *ViolationsInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ViolationsInfo.Merge(dst, src)
}
func (m *ViolationsInfo) XXX_Size() int {
	return xxx_messageInfo_ViolationsInfo.Size(m)
}
func (m *ViolationsInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ViolationsInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ViolationsInfo proto.InternalMessageInfo

func (m *ViolationsInfo) GetViolationsType() uint32 {
	if m != nil {
		return m.ViolationsType
	}
	return 0
}

func (m *ViolationsInfo) GetViolationsCnt() uint32 {
	if m != nil {
		return m.ViolationsCnt
	}
	return 0
}

// 从T盾获取用户的违规信息
type GetUserViolationsInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BeginTs              uint32   `protobuf:"varint,2,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,3,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserViolationsInfoReq) Reset()         { *m = GetUserViolationsInfoReq{} }
func (m *GetUserViolationsInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserViolationsInfoReq) ProtoMessage()    {}
func (*GetUserViolationsInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_comm_api_e43c0e5b600edded, []int{13}
}
func (m *GetUserViolationsInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserViolationsInfoReq.Unmarshal(m, b)
}
func (m *GetUserViolationsInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserViolationsInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserViolationsInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserViolationsInfoReq.Merge(dst, src)
}
func (m *GetUserViolationsInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserViolationsInfoReq.Size(m)
}
func (m *GetUserViolationsInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserViolationsInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserViolationsInfoReq proto.InternalMessageInfo

func (m *GetUserViolationsInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserViolationsInfoReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *GetUserViolationsInfoReq) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

type GetUserViolationsInfoResp struct {
	VioList              []*ViolationsInfo `protobuf:"bytes,1,rep,name=vio_list,json=vioList,proto3" json:"vio_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetUserViolationsInfoResp) Reset()         { *m = GetUserViolationsInfoResp{} }
func (m *GetUserViolationsInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserViolationsInfoResp) ProtoMessage()    {}
func (*GetUserViolationsInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_comm_api_e43c0e5b600edded, []int{14}
}
func (m *GetUserViolationsInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserViolationsInfoResp.Unmarshal(m, b)
}
func (m *GetUserViolationsInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserViolationsInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserViolationsInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserViolationsInfoResp.Merge(dst, src)
}
func (m *GetUserViolationsInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserViolationsInfoResp.Size(m)
}
func (m *GetUserViolationsInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserViolationsInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserViolationsInfoResp proto.InternalMessageInfo

func (m *GetUserViolationsInfoResp) GetVioList() []*ViolationsInfo {
	if m != nil {
		return m.VioList
	}
	return nil
}

func init() {
	proto.RegisterType((*PushMsgToUserReq)(nil), "apicentergo.PushMsgToUserReq")
	proto.RegisterType((*PushMsgToUserResp)(nil), "apicentergo.PushMsgToUserResp")
	proto.RegisterType((*ExcelData)(nil), "apicentergo.ExcelData")
	proto.RegisterType((*PushFromExcelReq)(nil), "apicentergo.PushFromExcelReq")
	proto.RegisterType((*PushFromExcelResp)(nil), "apicentergo.PushFromExcelResp")
	proto.RegisterType((*PushActBreakingNewsReq)(nil), "apicentergo.PushActBreakingNewsReq")
	proto.RegisterType((*PushActBreakingNewsResp)(nil), "apicentergo.PushActBreakingNewsResp")
	proto.RegisterType((*HobPushReq)(nil), "apicentergo.HobPushReq")
	proto.RegisterType((*HobPushResp)(nil), "apicentergo.HobPushResp")
	proto.RegisterType((*GetHobPushRecordReq)(nil), "apicentergo.GetHobPushRecordReq")
	proto.RegisterType((*HobPushRecord)(nil), "apicentergo.HobPushRecord")
	proto.RegisterType((*GetHobPushRecordResp)(nil), "apicentergo.GetHobPushRecordResp")
	proto.RegisterType((*ViolationsInfo)(nil), "apicentergo.ViolationsInfo")
	proto.RegisterType((*GetUserViolationsInfoReq)(nil), "apicentergo.GetUserViolationsInfoReq")
	proto.RegisterType((*GetUserViolationsInfoResp)(nil), "apicentergo.GetUserViolationsInfoResp")
	proto.RegisterEnum("apicentergo.BreakingNewsType", BreakingNewsType_name, BreakingNewsType_value)
	proto.RegisterEnum("apicentergo.ViolationsType", ViolationsType_name, ViolationsType_value)
}

func init() {
	proto.RegisterFile("apicenter-go/comm_api.proto", fileDescriptor_comm_api_e43c0e5b600edded)
}

var fileDescriptor_comm_api_e43c0e5b600edded = []byte{
	// 856 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x55, 0x5f, 0x73, 0xdb, 0x44,
	0x10, 0xaf, 0xe2, 0x24, 0xb6, 0x56, 0x71, 0x11, 0x57, 0x48, 0x94, 0x78, 0x68, 0x8d, 0x18, 0x20,
	0xd3, 0x99, 0xda, 0x33, 0x61, 0xca, 0x0b, 0x4f, 0x8d, 0xa1, 0x4d, 0x67, 0x80, 0xe9, 0x28, 0x09,
	0x0f, 0x3c, 0x54, 0x9c, 0xa5, 0x8d, 0x7c, 0x20, 0xdd, 0x1d, 0x77, 0x67, 0xa7, 0xf4, 0xbb, 0xf0,
	0xc2, 0x27, 0xe3, 0x7b, 0xf0, 0xc2, 0xdc, 0xc9, 0x96, 0x65, 0x87, 0x7f, 0x0f, 0x3c, 0x59, 0xfb,
	0xfb, 0xed, 0xdd, 0xee, 0xfe, 0x76, 0xf7, 0x0c, 0x03, 0x2a, 0x59, 0x86, 0xdc, 0xa0, 0x7a, 0x52,
	0x88, 0x71, 0x26, 0xaa, 0x2a, 0xa5, 0x92, 0x8d, 0xa4, 0x12, 0x46, 0x90, 0xa0, 0x21, 0x0b, 0x11,
	0x4f, 0x21, 0x7c, 0x35, 0xd7, 0xb3, 0x6f, 0x74, 0x71, 0x25, 0xae, 0x35, 0xaa, 0x04, 0x7f, 0x26,
	0xc7, 0xd0, 0x9b, 0xb3, 0x3c, 0x2d, 0x99, 0x36, 0x91, 0x37, 0xec, 0x9c, 0xf6, 0x93, 0xee, 0x9c,
	0xe5, 0x5f, 0x33, 0x6d, 0x2c, 0x25, 0xe7, 0x7a, 0x96, 0x66, 0x55, 0x1e, 0xed, 0x0c, 0x3d, 0x4b,
	0x59, 0x7b, 0x52, 0xe5, 0x24, 0x82, 0x6e, 0x26, 0xb8, 0x41, 0x6e, 0xa2, 0xce, 0xd0, 0x3b, 0x3d,
	0x48, 0x56, 0x66, 0xfc, 0x00, 0xde, 0xdd, 0x8a, 0xa1, 0x65, 0xfc, 0x09, 0xf8, 0x5f, 0xbd, 0xc9,
	0xb0, 0xfc, 0x92, 0x1a, 0x6a, 0xaf, 0xcd, 0x44, 0x99, 0xe6, 0xd4, 0x50, 0x17, 0xd1, 0xb7, 0x87,
	0x1d, 0x15, 0x63, 0x9d, 0xe0, 0x73, 0x25, 0x2a, 0xe7, 0x6f, 0x13, 0x3c, 0x81, 0x9e, 0xc1, 0x4a,
	0x96, 0xd4, 0x60, 0xe4, 0x0d, 0xbd, 0x53, 0x3f, 0x69, 0x6c, 0xf2, 0x14, 0x00, 0xad, 0x5f, 0x7d,
	0xd9, 0xce, 0xb0, 0x73, 0x1a, 0x9c, 0x1d, 0x8e, 0x5a, 0x25, 0x8f, 0x9a, 0xb0, 0x89, 0x8f, 0xab,
	0xcf, 0x55, 0x8e, 0xad, 0x30, 0x5a, 0xc6, 0x7f, 0x78, 0x70, 0x68, 0xd1, 0x67, 0x99, 0x39, 0x57,
	0x48, 0x7f, 0x62, 0xbc, 0xf8, 0x16, 0x6f, 0xf5, 0x52, 0xa3, 0x1b, 0x25, 0xaa, 0x74, 0xce, 0x72,
	0x97, 0x42, 0x3f, 0xe9, 0x5a, 0xfb, 0x9a, 0xe5, 0x0d, 0x95, 0xb1, 0x46, 0x23, 0x6b, 0x4f, 0x58,
	0x4e, 0x1e, 0x41, 0xc0, 0xf1, 0x56, 0xa7, 0x52, 0xe1, 0x0d, 0x7b, 0xe3, 0x74, 0xf2, 0x13, 0xb0,
	0xd0, 0x2b, 0x87, 0x90, 0x0f, 0xe1, 0xc0, 0x39, 0xac, 0x94, 0xdc, 0x75, 0x1e, 0xee, 0xd0, 0xa4,
	0x86, 0xec, 0xf5, 0x3f, 0xce, 0x2b, 0x99, 0xce, 0x55, 0x19, 0xed, 0x39, 0xba, 0x6b, 0xed, 0x6b,
	0x55, 0x5a, 0x6a, 0x46, 0x55, 0xee, 0xa8, 0xfd, 0x9a, 0xb2, 0xb6, 0xa5, 0x08, 0xec, 0x9a, 0x5f,
	0x24, 0x46, 0x5d, 0x97, 0x90, 0xfb, 0x26, 0x1f, 0x00, 0x18, 0xaa, 0x0a, 0x34, 0xae, 0x8a, 0x9e,
	0x63, 0xfc, 0x1a, 0xb9, 0x66, 0x79, 0x7c, 0x0c, 0x47, 0x7f, 0x59, 0xbc, 0x96, 0xf1, 0xaf, 0x1e,
	0xc0, 0x85, 0x98, 0x5a, 0xfa, 0xdf, 0xfa, 0x31, 0x84, 0x40, 0x96, 0x34, 0xc3, 0x99, 0x28, 0x73,
	0x54, 0x4e, 0x10, 0x3f, 0x69, 0x43, 0x5b, 0x1d, 0xeb, 0xfc, 0xc7, 0x8e, 0xd9, 0xa0, 0x42, 0xa2,
	0xa2, 0x46, 0xa8, 0xa5, 0x4c, 0x8d, 0x1d, 0xf7, 0x21, 0x68, 0xd2, 0xd3, 0x32, 0xfe, 0xdd, 0x83,
	0x07, 0x2f, 0xd0, 0x34, 0x50, 0x26, 0x54, 0x6e, 0xf3, 0x8e, 0xa1, 0xef, 0x3a, 0x35, 0x13, 0xd3,
	0xd4, 0x98, 0x65, 0x27, 0xfd, 0x24, 0xb0, 0xe0, 0x85, 0x98, 0x5e, 0x19, 0x96, 0x93, 0x87, 0x10,
	0x18, 0xb1, 0xf6, 0xa8, 0xf3, 0xf7, 0x8d, 0x58, 0xf1, 0xed, 0x34, 0x3a, 0x9b, 0x69, 0x58, 0x81,
	0xa7, 0x58, 0x30, 0x9e, 0x1a, 0x56, 0xa1, 0x4b, 0xb2, 0x93, 0xf8, 0x0e, 0xb9, 0x62, 0x15, 0xda,
	0x76, 0x21, 0xcf, 0x6b, 0x72, 0xcf, 0x91, 0x5d, 0xe4, 0xb9, 0xa3, 0x08, 0xec, 0x4a, 0x5a, 0xa0,
	0xeb, 0x62, 0x3f, 0x71, 0xdf, 0x64, 0x00, 0xbe, 0xfd, 0x4d, 0x35, 0x7b, 0xbb, 0xea, 0x63, 0xcf,
	0x02, 0x97, 0xec, 0x2d, 0xc6, 0xbf, 0x79, 0xd0, 0xdf, 0xa8, 0xef, 0x7f, 0x29, 0x6e, 0x6b, 0xa7,
	0xfd, 0x66, 0xa7, 0xff, 0x49, 0x7d, 0x72, 0x04, 0x5d, 0x21, 0xdb, 0x65, 0xed, 0x0b, 0x69, 0xab,
	0x8a, 0x25, 0xbc, 0x77, 0xb7, 0x0d, 0x5a, 0x92, 0x2f, 0x20, 0x50, 0xce, 0x5a, 0xbf, 0x39, 0xc1,
	0xd9, 0xc9, 0xc6, 0x08, 0x6c, 0x1e, 0x82, 0xda, 0xdd, 0x3d, 0x49, 0x03, 0xf0, 0x8d, 0x30, 0xb4,
	0x4c, 0x33, 0x6e, 0x96, 0xfb, 0xd6, 0x73, 0xc0, 0x84, 0x9b, 0xf8, 0x07, 0xb8, 0xff, 0x1d, 0x13,
	0x25, 0x35, 0x4c, 0x70, 0xfd, 0x92, 0xdf, 0x08, 0xf2, 0x29, 0xbc, 0xb3, 0x68, 0x90, 0xd4, 0xed,
	0x44, 0xbd, 0xbf, 0xf7, 0xd7, 0xf0, 0x95, 0xdd, 0x8e, 0x8f, 0xa1, 0x85, 0xb4, 0x2e, 0xef, 0xaf,
	0x51, 0x1b, 0xe1, 0x35, 0x44, 0x2f, 0xd0, 0xd8, 0x67, 0x6d, 0x33, 0x90, 0x9d, 0xaf, 0x10, 0x3a,
	0xeb, 0xf7, 0xc1, 0x7e, 0xda, 0x96, 0x2f, 0x27, 0x42, 0xaf, 0xde, 0x86, 0x7a, 0x1e, 0x34, 0x79,
	0x1f, 0xf6, 0xdd, 0x34, 0x68, 0x27, 0x75, 0x3f, 0xd9, 0xb3, 0xb3, 0xa0, 0xe3, 0x4b, 0x38, 0xfe,
	0x9b, 0xfb, 0xb5, 0x24, 0x9f, 0x43, 0x6f, 0xc1, 0x44, 0x5b, 0xb5, 0xc1, 0x86, 0x6a, 0x5b, 0x47,
	0xba, 0x0b, 0x26, 0xac, 0x66, 0x8f, 0x0d, 0x84, 0xed, 0x9d, 0x76, 0xf5, 0x0e, 0xe0, 0x68, 0x1b,
	0x4b, 0x27, 0xa2, 0xaa, 0x04, 0x0f, 0xef, 0x91, 0x87, 0x70, 0x72, 0x87, 0xbc, 0xa0, 0x65, 0x29,
	0x6e, 0x11, 0x79, 0xe8, 0x91, 0x8f, 0xe0, 0xd1, 0x1d, 0xfe, 0x52, 0x2a, 0xc6, 0x8b, 0xe7, 0xa8,
	0x0d, 0x5b, 0xd0, 0x32, 0xdc, 0x79, 0xfc, 0xba, 0xdd, 0x0c, 0x17, 0xf3, 0x10, 0xc8, 0x1a, 0x49,
	0x5f, 0xf2, 0x05, 0x2d, 0x59, 0x1e, 0xde, 0x23, 0x21, 0x1c, 0xb4, 0xf0, 0x67, 0xa1, 0xb7, 0x85,
	0x9c, 0x87, 0x3b, 0x5b, 0xc8, 0x24, 0xec, 0x9c, 0x8f, 0xbf, 0x7f, 0x52, 0x88, 0x92, 0xf2, 0x62,
	0xf4, 0xf4, 0xcc, 0x98, 0x51, 0x26, 0xaa, 0xb1, 0xfb, 0xc7, 0xcb, 0x44, 0x39, 0xd6, 0xa8, 0x16,
	0x2c, 0x43, 0x3d, 0x6e, 0xc9, 0x33, 0xdd, 0x77, 0xf4, 0x67, 0x7f, 0x06, 0x00, 0x00, 0xff, 0xff,
	0x05, 0xbf, 0xaa, 0xe3, 0x2f, 0x07, 0x00, 0x00,
}
