// Code generated by protoc-gen-go. DO NOT EDIT.
// source: apicenter-go/apicenter-go-main.proto

package apicentergo // import "golang.52tt.com/protocol/services/apicentergo"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import dark_gift_bonus "golang.52tt.com/protocol/services/dark-gift-bonus"
import settlement_bill "golang.52tt.com/protocol/services/settlement-bill"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ApicenterGoClient is the client API for ApicenterGo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ApicenterGoClient interface {
	// 用户礼物
	GetPresentConfigById(ctx context.Context, in *GetPresentConfigByIdReq, opts ...grpc.CallOption) (*GetPresentConfigByIdResp, error)
	GetPresentConfigList(ctx context.Context, in *GetPresentConfigListReq, opts ...grpc.CallOption) (*GetPresentConfigListResp, error)
	AddPresentConfig(ctx context.Context, in *AddPresentConfigReq, opts ...grpc.CallOption) (*AddPresentConfigResp, error)
	DelPresentConfig(ctx context.Context, in *DelPresentConfigReq, opts ...grpc.CallOption) (*DelPresentConfigResp, error)
	UpdatePresentConfig(ctx context.Context, in *UpdatePresentConfigReq, opts ...grpc.CallOption) (*UpdatePresentConfigResp, error)
	GetUserPresentSend(ctx context.Context, in *GetUserPresentSendReq, opts ...grpc.CallOption) (*GetUserPresentSendResp, error)
	GetUserPresentReceive(ctx context.Context, in *GetUserPresentReceiveReq, opts ...grpc.CallOption) (*GetUserPresentReceiveResp, error)
	GetAllFellowPresent(ctx context.Context, in *GetAllFellowPresentReq, opts ...grpc.CallOption) (*GetAllFellowPresentResp, error)
	// 背包包裹
	AddPackageCfg(ctx context.Context, in *AddPackageCfgReq, opts ...grpc.CallOption) (*AddPackageCfgResp, error)
	DelPackageCfg(ctx context.Context, in *DelPackageCfgReq, opts ...grpc.CallOption) (*DelPackageCfgResp, error)
	GetPackageCfg(ctx context.Context, in *GetPackageCfgReq, opts ...grpc.CallOption) (*GetPackageCfgResp, error)
	GetPackageCfgByIds(ctx context.Context, in *GetPackageCfgByIdsReq, opts ...grpc.CallOption) (*GetPackageCfgByIdsResp, error)
	AddPackageItemCfg(ctx context.Context, in *AddPackageItemCfgReq, opts ...grpc.CallOption) (*AddPackageItemCfgResp, error)
	ModPackageItemCfg(ctx context.Context, in *ModPackageItemCfgReq, opts ...grpc.CallOption) (*ModPackageItemCfgResp, error)
	DelPackageItemCfg(ctx context.Context, in *DelPackageItemCfgReq, opts ...grpc.CallOption) (*DelPackageItemCfgResp, error)
	GetPackageItemCfg(ctx context.Context, in *GetPackageItemCfgReq, opts ...grpc.CallOption) (*GetPackageItemCfgResp, error)
	GiveUserPackage(ctx context.Context, in *GiveUserPackageReq, opts ...grpc.CallOption) (*GiveUserPackageResp, error)
	AddFuncCardCfg(ctx context.Context, in *AddFuncCardCfgReq, opts ...grpc.CallOption) (*AddFuncCardCfgResp, error)
	DelFuncCardCfg(ctx context.Context, in *DelFuncCardCfgReq, opts ...grpc.CallOption) (*DelFuncCardCfgResp, error)
	GetFuncCardCfg(ctx context.Context, in *GetFuncCardCfgReq, opts ...grpc.CallOption) (*GetFuncCardCfgResp, error)
	GetUserBackpack(ctx context.Context, in *GetUserBackpackReq, opts ...grpc.CallOption) (*GetUserBackpackResp, error)
	GetUserPackageReceive(ctx context.Context, in *GetUserPackageReceiveReq, opts ...grpc.CallOption) (*GetUserPackageReceiveResp, error)
	// 风控配置相关
	AddBusiness(ctx context.Context, in *AddBusinessReq, opts ...grpc.CallOption) (*AddBusinessResp, error)
	GetAllBusiness(ctx context.Context, in *GetAllBusinessReq, opts ...grpc.CallOption) (*GetAllBusinessResp, error)
	GetBusinessByIds(ctx context.Context, in *GetBusinessByIdsReq, opts ...grpc.CallOption) (*GetBusinessByIdsResp, error)
	AddBusinessRiskControlConf(ctx context.Context, in *AddBusinessRiskControlConfReq, opts ...grpc.CallOption) (*AddBusinessRiskControlConfResp, error)
	ModBusinessRiskControlConf(ctx context.Context, in *ModBusinessRiskControlConfReq, opts ...grpc.CallOption) (*ModBusinessRiskControlConfResp, error)
	GetBusinessRiskControlConf(ctx context.Context, in *GetBusinessRiskControlConfReq, opts ...grpc.CallOption) (*GetBusinessRiskControlConfResp, error)
	// 用户账号相关
	GetUserInfo(ctx context.Context, in *GetUserInfoReq, opts ...grpc.CallOption) (*GetUserInfoResp, error)
	BatchGetUserInfo(ctx context.Context, in *BatchGetUserInfoReq, opts ...grpc.CallOption) (*BatchGetUserInfoResp, error)
	GetUserWithExtraInfo(ctx context.Context, in *GetUserWithExtraInfoReq, opts ...grpc.CallOption) (*GetUserWithExtraInfoResp, error)
	BatGetUserByTTid(ctx context.Context, in *BatGetUserByTTidReq, opts ...grpc.CallOption) (*BatGetUserByTTidResp, error)
	// 合成相关
	GetUserConversion(ctx context.Context, in *GetUserConversionReq, opts ...grpc.CallOption) (*GetUserConversionResp, error)
	BatchDeductUserItem(ctx context.Context, in *BatchDeductUserItemReq, opts ...grpc.CallOption) (*BatchDeductUserItemResp, error)
	// 公会相关
	GetGuildById(ctx context.Context, in *GetGuildByIdReq, opts ...grpc.CallOption) (*GetGuildByIdResp, error)
	BatchGetGuild(ctx context.Context, in *BatchGetGuildReq, opts ...grpc.CallOption) (*BatchGetGuildResp, error)
	BatchGetGuildV2(ctx context.Context, in *BatchGetGuildReq, opts ...grpc.CallOption) (*BatchGetGuildResp, error)
	ModifyGuildName(ctx context.Context, in *ModifyGuildNameReq, opts ...grpc.CallOption) (*ModifyGuildNameResp, error)
	SetGuildShortId(ctx context.Context, in *SetGuildShortIdReq, opts ...grpc.CallOption) (*SetGuildShortIdResp, error)
	UnsetGuildShortId(ctx context.Context, in *UnsetGuildShortIdReq, opts ...grpc.CallOption) (*UnsetGuildShortIdResp, error)
	ModifyGuildGameLimit(ctx context.Context, in *ModifyGuildGameLimitReq, opts ...grpc.CallOption) (*ModifyGuildGameLimitResp, error)
	// 签约模块相关接口
	GetApplySignRecord(ctx context.Context, in *GetApplySignRecordReq, opts ...grpc.CallOption) (*GetApplySignRecordResp, error)
	OfficialHandleApplySign(ctx context.Context, in *OfficialHandleApplySignReq, opts ...grpc.CallOption) (*OfficialHandleApplySignResp, error)
	BatchGetContractInfo(ctx context.Context, in *BatchGetContractInfoReq, opts ...grpc.CallOption) (*BatchGetContractInfoResp, error)
	ReclaimAnchorIdentity(ctx context.Context, in *ReclaimAnchorIdentityReq, opts ...grpc.CallOption) (*ReclaimAnchorIdentityResp, error)
	BatchGetApplyBlacklist(ctx context.Context, in *BatchGetApplyBlacklistReq, opts ...grpc.CallOption) (*BatchGetApplyBlacklistResp, error)
	HandleApplyBlackInfo(ctx context.Context, in *HandleApplyBlackInfoReq, opts ...grpc.CallOption) (*HandleApplyBlackInfoResp, error)
	GetLiveAnchorExamine(ctx context.Context, in *GetLiveAnchorExamineReq, opts ...grpc.CallOption) (*GetLiveAnchorExamineResp, error)
	GetAllLiveAnchorExamine(ctx context.Context, in *GetAllLiveAnchorExamineReq, opts ...grpc.CallOption) (*GetAllLiveAnchorExamineResp, error)
	UpdateLiveAnchorExamineStatus(ctx context.Context, in *UpdateLiveAnchorExamineStatusReq, opts ...grpc.CallOption) (*UpdateLiveAnchorExamineStatusResp, error)
	UpdateLiveAnchorExamineTime(ctx context.Context, in *UpdateLiveAnchorExamineTimeReq, opts ...grpc.CallOption) (*UpdateLiveAnchorExamineTimeResp, error)
	GetAnchorIdentityLog(ctx context.Context, in *GetAnchorIdentityLogReq, opts ...grpc.CallOption) (*GetAnchorIdentityLogResp, error)
	GetPractitionerMonthActiveInfo(ctx context.Context, in *GetPractitionerMonthActiveInfoReq, opts ...grpc.CallOption) (*GetPractitionerMonthActiveInfoResp, error)
	// 用户相关
	AwardTBeanToUser(ctx context.Context, in *AwardTBeanToUserReq, opts ...grpc.CallOption) (*AwardTBeanToUserResp, error)
	AwardDiamondToUser(ctx context.Context, in *AwardDiamondToUserReq, opts ...grpc.CallOption) (*AwardDiamondToUserResp, error)
	GetAwardDiamondRecord(ctx context.Context, in *GetAwardDiamondRecordReq, opts ...grpc.CallOption) (*GetAwardDiamondRecordResp, error)
	AwardExpToUser(ctx context.Context, in *AwardExpToUserReq, opts ...grpc.CallOption) (*AwardExpToUserResp, error)
	GetAwardExpRecord(ctx context.Context, in *GetAwardExpRecordReq, opts ...grpc.CallOption) (*GetAwardExpRecordResp, error)
	GetUserLoginInfo(ctx context.Context, in *GetUserLoginInfoReq, opts ...grpc.CallOption) (*GetUserLoginInfoResp, error)
	GetBannedRecord(ctx context.Context, in *GetBannedRecordReq, opts ...grpc.CallOption) (*GetBannedRecordResp, error)
	GetBannedOperator(ctx context.Context, in *GetBannedOperatorReq, opts ...grpc.CallOption) (*GetBannedOperatorResp, error)
	GetBannedAppealRecord(ctx context.Context, in *GetBannedAppealRecordReq, opts ...grpc.CallOption) (*GetBannedAppealRecordResp, error)
	UpdateBannedAppealRecord(ctx context.Context, in *UpdateBannedAppealRecordReq, opts ...grpc.CallOption) (*UpdateBannedAppealRecordResp, error)
	GetBannedCheckRecord(ctx context.Context, in *GetBannedCheckRecordReq, opts ...grpc.CallOption) (*GetBannedCheckRecordResp, error)
	UpdateBannedCheckRecord(ctx context.Context, in *UpdateBannedCheckRecordReq, opts ...grpc.CallOption) (*UpdateBannedCheckRecordResp, error)
	GetBannedCheckOperator(ctx context.Context, in *GetBannedCheckOperatorReq, opts ...grpc.CallOption) (*GetBannedCheckOperatorResp, error)
	GetUserLoginDevice(ctx context.Context, in *GetUserLoginDeviceReq, opts ...grpc.CallOption) (*GetUserLoginDeviceResp, error)
	GetUserLoginWithDevice(ctx context.Context, in *GetUserLoginWithDeviceReq, opts ...grpc.CallOption) (*GetUserLoginWithDeviceResp, error)
	BanUser(ctx context.Context, in *BanUserReq, opts ...grpc.CallOption) (*BanUserResp, error)
	RecoverUser(ctx context.Context, in *RecoverUserReq, opts ...grpc.CallOption) (*RecoverUserResp, error)
	BatchRecoverUser(ctx context.Context, in *BatchRecoverUserReq, opts ...grpc.CallOption) (*BatchRecoverUserResp, error)
	GetDeviceLastLoginInfo(ctx context.Context, in *GetDeviceLastLoginInfoReq, opts ...grpc.CallOption) (*GetDeviceLastLoginInfoResp, error)
	GetBanLog(ctx context.Context, in *GetBanLogReq, opts ...grpc.CallOption) (*GetBanLogResp, error)
	GetUserBanStatus(ctx context.Context, in *GetUserBanStatusReq, opts ...grpc.CallOption) (*GetUserBanStatusResp, error)
	BatchBanUserWithDevice(ctx context.Context, in *BatchBanUserWithDeviceReq, opts ...grpc.CallOption) (*BatchBanUserWithDeviceResp, error)
	CanRecoverDevice(ctx context.Context, in *CanRecoverDeviceReq, opts ...grpc.CallOption) (*CanRecoverDeviceResp, error)
	GetUserInviteHistory(ctx context.Context, in *GetUserInviteHistoryReq, opts ...grpc.CallOption) (*GetUserInviteHistoryResp, error)
	ResetUserPassword(ctx context.Context, in *ResetUserPasswordReq, opts ...grpc.CallOption) (*ResetUserPasswordResp, error)
	UnbindUserPhone(ctx context.Context, in *UnbindUserPhoneReq, opts ...grpc.CallOption) (*UnbindUserPhoneResp, error)
	ClearSecurityQuestion(ctx context.Context, in *ClearSecurityQuestionReq, opts ...grpc.CallOption) (*ClearSecurityQuestionResp, error)
	DetachThirdpart(ctx context.Context, in *DetachThirdpartReq, opts ...grpc.CallOption) (*DetachThirdpartResp, error)
	BatchGetUserOfficialCert(ctx context.Context, in *BatchGetUserOfficialCertReq, opts ...grpc.CallOption) (*BatchGetUserOfficialCertResp, error)
	GetUserExp(ctx context.Context, in *GetUserExpReq, opts ...grpc.CallOption) (*GetUserExpResp, error)
	GetUserControlInfo(ctx context.Context, in *GetUserControlInfoReq, opts ...grpc.CallOption) (*GetUserControlInfoResp, error)
	BatRemoveUserControlled(ctx context.Context, in *BatRemoveUserControlledReq, opts ...grpc.CallOption) (*BatRemoveUserControlledResp, error)
	GetTheSameRealNameUserList(ctx context.Context, in *GetTheSameRealNameUserListReq, opts ...grpc.CallOption) (*GetTheSameRealNameUserListResp, error)
	GetUserRealNameAuthInfoV2(ctx context.Context, in *GetUserRealNameAuthInfoV2Req, opts ...grpc.CallOption) (*GetUserRealNameAuthInfoV2Resp, error)
	BatchGetUserRealNameAuthInfo(ctx context.Context, in *BatchGetUserRealNameAuthInfoReq, opts ...grpc.CallOption) (*BatchGetUserRealNameAuthInfoResp, error)
	// 房间相关
	BatchGetChannelSimpleInfo(ctx context.Context, in *BatchGetChannelSimpleInfoReq, opts ...grpc.CallOption) (*BatchGetChannelSimpleInfoResp, error)
	GetAllReportHistoryList(ctx context.Context, in *GetAllReportHistoryListReq, opts ...grpc.CallOption) (*GetAllReportHistoryListResp, error)
	GetChannelReportStat(ctx context.Context, in *GetChannelReportStatReq, opts ...grpc.CallOption) (*GetChannelReportStatResp, error)
	GetChannelReportProcList(ctx context.Context, in *GetChannelReportProcListReq, opts ...grpc.CallOption) (*GetChannelReportProcListResp, error)
	SanctionChannel(ctx context.Context, in *SanctionChannelReq, opts ...grpc.CallOption) (*SanctionChannelResp, error)
	GetBannedChannelList(ctx context.Context, in *GetBannedChannelListReq, opts ...grpc.CallOption) (*GetBannedChannelListResp, error)
	AddChannelWhiteListInfo(ctx context.Context, in *AddChannelWhiteListInfoReq, opts ...grpc.CallOption) (*AddChannelWhiteListInfoResp, error)
	DelChannelWhiteListInfo(ctx context.Context, in *DelChannelWhiteListInfoReq, opts ...grpc.CallOption) (*DelChannelWhiteListInfoResp, error)
	GetChannelWhiteList(ctx context.Context, in *GetChannelWhiteListReq, opts ...grpc.CallOption) (*GetChannelWhiteListResp, error)
	GetChannelSimpleInfo(ctx context.Context, in *GetChannelSimpleInfoReq, opts ...grpc.CallOption) (*GetChannelSimpleInfoResp, error)
	BatchGetChannelTag(ctx context.Context, in *BatchGetChannelTagReq, opts ...grpc.CallOption) (*BatchGetChannelTagResp, error)
	PushMsgToChannel(ctx context.Context, in *PushMsgToChannelReq, opts ...grpc.CallOption) (*PushMsgToChannelResp, error)
	// 通用接口
	PushMsgToUser(ctx context.Context, in *PushMsgToUserReq, opts ...grpc.CallOption) (*PushMsgToUserResp, error)
	PushActBreakingNews(ctx context.Context, in *PushActBreakingNewsReq, opts ...grpc.CallOption) (*PushActBreakingNewsResp, error)
	// 推送excel的数据接口
	PushFromExcel(ctx context.Context, in *PushFromExcelReq, opts ...grpc.CallOption) (*PushFromExcelResp, error)
	// 滚刀推送
	HobPush(ctx context.Context, in *HobPushReq, opts ...grpc.CallOption) (*HobPushResp, error)
	GetHobPushRecord(ctx context.Context, in *GetHobPushRecordReq, opts ...grpc.CallOption) (*GetHobPushRecordResp, error)
	// 房间麦位模式
	GetChannelMicMode(ctx context.Context, in *GetChannelMicModeReq, opts ...grpc.CallOption) (*GetChannelMicModeResp, error)
	// 房间背景
	AddChannelBackgroundConf(ctx context.Context, in *AddChannelBackgroundConfReq, opts ...grpc.CallOption) (*AddChannelBackgroundConfResp, error)
	DelChannelBackgroundConf(ctx context.Context, in *DelChannelBackgroundConfReq, opts ...grpc.CallOption) (*DelChannelBackgroundConfResp, error)
	GetChannelBackgroundConf(ctx context.Context, in *GetChannelBackgroundConfReq, opts ...grpc.CallOption) (*GetChannelBackgroundConfResp, error)
	// 添加背景配置V2
	AddChannelBackgroundConfV2(ctx context.Context, in *AddChannelBackgroundConfReq, opts ...grpc.CallOption) (*AddChannelBackgroundConfResp, error)
	// 添加背景配置V2
	UpdateChannelBackgroundConfV2(ctx context.Context, in *UpdateChannelBackgroundConfReq, opts ...grpc.CallOption) (*UpdateChannelBackgroundConfResp, error)
	// 删除背景配置V2
	DelChannelBackgroundConfV2(ctx context.Context, in *DelChannelBackgroundConfReq, opts ...grpc.CallOption) (*DelChannelBackgroundConfResp, error)
	// 获取背景配置V2
	GetChannelBackgroundConfV2(ctx context.Context, in *GetChannelBackgroundConfV2Req, opts ...grpc.CallOption) (*GetChannelBackgroundConfV2Resp, error)
	// 批量下发背景
	BatchGiveChannelBg(ctx context.Context, in *BatchGiveChannelBgReq, opts ...grpc.CallOption) (*BatchGiveChannelBgResp, error)
	// 更新下发的背景信息
	UpdateGivenChannelBg(ctx context.Context, in *UpdateGivenChannelBgReq, opts ...grpc.CallOption) (*UpdateGivenChannelBgResp, error)
	// 删除下发的背景信息
	DeleteGivenChannelBg(ctx context.Context, in *DeleteGivenChannelBgReq, opts ...grpc.CallOption) (*DeleteGivenChannelBgResp, error)
	// 查询下发的背景信息
	ListGivenChannelBg(ctx context.Context, in *ListGivenChannelBgReq, opts ...grpc.CallOption) (*ListGivenChannelBgResp, error)
	// 搜索白名单后台
	SwitchSearchWhiteList(ctx context.Context, in *SwitchSearchWhiteListReq, opts ...grpc.CallOption) (*SwitchSearchWhiteListResp, error)
	GetSearchWhiteListStatus(ctx context.Context, in *GetSearchWhiteListStatusReq, opts ...grpc.CallOption) (*GetSearchWhiteListStatusResp, error)
	BatchAddSearchWhiteList(ctx context.Context, in *BatchAddSearchWhiteListReq, opts ...grpc.CallOption) (*BatchAddSearchWhiteListResp, error)
	BatchDelSearchWhiteList(ctx context.Context, in *BatchDelSearchWhiteListReq, opts ...grpc.CallOption) (*BatchDelSearchWhiteListResp, error)
	GetSearchWhiteList(ctx context.Context, in *GetSearchWhiteListReq, opts ...grpc.CallOption) (*GetSearchWhiteListResp, error)
	ExportHotWord(ctx context.Context, in *ExportHotWordReq, opts ...grpc.CallOption) (*ExportHotWordResp, error)
	// 语音直播相关
	GetAnchorDailyRecordWithDateList(ctx context.Context, in *GetAnchorDailyRecordWithDateListReq, opts ...grpc.CallOption) (*GetAnchorDailyRecordWithDateListResp, error)
	AddAppointPkInfo(ctx context.Context, in *AddAppointPkInfoReq, opts ...grpc.CallOption) (*AddAppointPkInfoResp, error)
	UpdateAppointPkInfo(ctx context.Context, in *UpdateAppointPkInfoReq, opts ...grpc.CallOption) (*UpdateAppointPkInfoResp, error)
	DelAppointPkInfo(ctx context.Context, in *DelAppointPkInfoReq, opts ...grpc.CallOption) (*DelAppointPkInfoResp, error)
	GetAppointPkInfoList(ctx context.Context, in *GetAppointPkInfoListReq, opts ...grpc.CallOption) (*GetAppointPkInfoListResp, error)
	BatchAddAppointPkInfo(ctx context.Context, in *BatchAddAppointPkInfoReq, opts ...grpc.CallOption) (*BatchAddAppointPkInfoResp, error)
	AddPlateConfig(ctx context.Context, in *AddPlateConfigReq, opts ...grpc.CallOption) (*AddPlateConfigResp, error)
	UpdatePlateConfig(ctx context.Context, in *UpdatePlateConfigReq, opts ...grpc.CallOption) (*UpdatePlateConfigResp, error)
	DelPlateConfig(ctx context.Context, in *DelPlateConfigReq, opts ...grpc.CallOption) (*DelPlateConfigResp, error)
	GetPlateConfigList(ctx context.Context, in *GetPlateConfigListReq, opts ...grpc.CallOption) (*GetPlateConfigListResp, error)
	GrantAnchorPlate(ctx context.Context, in *GrantAnchorPlateReq, opts ...grpc.CallOption) (*GrantAnchorPlateResp, error)
	UpdateGrantedPlateInfo(ctx context.Context, in *UpdateGrantedPlateInfoReq, opts ...grpc.CallOption) (*UpdateGrantedPlateInfoResp, error)
	DelGrantedPlateInfo(ctx context.Context, in *DelGrantedPlateInfoReq, opts ...grpc.CallOption) (*DelGrantedPlateInfoResp, error)
	GetGrantedPlateList(ctx context.Context, in *GetGrantedPlateListReq, opts ...grpc.CallOption) (*GetGrantedPlateListResp, error)
	GetPlateConfigById(ctx context.Context, in *GetPlateConfigByIdReq, opts ...grpc.CallOption) (*GetPlateConfigByIdResp, error)
	WearAnchorPlate(ctx context.Context, in *WearAnchorPlateReq, opts ...grpc.CallOption) (*WearAnchorPlateResp, error)
	BatGrantAnchorPlate(ctx context.Context, in *BatGrantAnchorPlateReq, opts ...grpc.CallOption) (*BatGrantAnchorPlateResp, error)
	GrantLiveMissionAward(ctx context.Context, in *GrantLiveMissionAwardReq, opts ...grpc.CallOption) (*GrantLiveMissionAwardResp, error)
	GetAnchorList(ctx context.Context, in *GetAnchorListReq, opts ...grpc.CallOption) (*GetAnchorListResp, error)
	BatDelChannelLiveInfo(ctx context.Context, in *BatDelChannelLiveInfoReq, opts ...grpc.CallOption) (*BatDelChannelLiveInfoResp, error)
	SetChannelLiveTag(ctx context.Context, in *SetChannelLiveTagReq, opts ...grpc.CallOption) (*SetChannelLiveTagResp, error)
	BatchAddAnchor(ctx context.Context, in *BatchAddAnchorReq, opts ...grpc.CallOption) (*BatchAddAnchorResp, error)
	GetAnchorOperRecord(ctx context.Context, in *GetAnchorOperRecordReq, opts ...grpc.CallOption) (*GetAnchorOperRecordResp, error)
	AddVirtualAnchorPer(ctx context.Context, in *AddVirtualAnchorPerReq, opts ...grpc.CallOption) (*AddVirtualAnchorPerResp, error)
	UpdateVirtualAnchorPer(ctx context.Context, in *UpdateVirtualAnchorPerReq, opts ...grpc.CallOption) (*UpdateVirtualAnchorPerResp, error)
	DelVirtualAnchorPer(ctx context.Context, in *DelVirtualAnchorPerReq, opts ...grpc.CallOption) (*DelVirtualAnchorPerResp, error)
	GetVirtualAnchorPerList(ctx context.Context, in *GetVirtualAnchorPerListReq, opts ...grpc.CallOption) (*GetVirtualAnchorPerListResp, error)
	GrantFansGiftPrivilege(ctx context.Context, in *GrantFansGiftPrivilegeReq, opts ...grpc.CallOption) (*GrantFansGiftPrivilegeResp, error)
	GetAnchorScoreList(ctx context.Context, in *GetAnchorScoreListReq, opts ...grpc.CallOption) (*GetAnchorScoreListResp, error)
	// 小众游戏相关
	GetMinorityGame(ctx context.Context, in *GetMinorityGameReq, opts ...grpc.CallOption) (*GetMinorityGameResp, error)
	AddMinorityGame(ctx context.Context, in *AddMinorityGameReq, opts ...grpc.CallOption) (*AddMinorityGameResp, error)
	RemoveMinorityGame(ctx context.Context, in *RemoveMinorityGameReq, opts ...grpc.CallOption) (*RemoveMinorityGameResp, error)
	ChangeMinorityGame(ctx context.Context, in *ChangeMinorityGameReq, opts ...grpc.CallOption) (*ChangeMinorityGameResp, error)
	// 贵族相关
	AddTempNobility(ctx context.Context, in *AddTempNobilityReq, opts ...grpc.CallOption) (*AddTempNobilityResp, error)
	GetTempNobilityList(ctx context.Context, in *GetTempNobilityListReq, opts ...grpc.CallOption) (*GetTempNobilityListResp, error)
	// 魔法精灵(幸运礼物)相关
	AddMagicSpirit(ctx context.Context, in *AddMagicSpiritReq, opts ...grpc.CallOption) (*AddMagicSpiritResp, error)
	DelMagicSpirit(ctx context.Context, in *DelMagicSpiritReq, opts ...grpc.CallOption) (*DelMagicSpiritResp, error)
	GetMagicSpirit(ctx context.Context, in *GetMagicSpiritReq, opts ...grpc.CallOption) (*GetMagicSpiritResp, error)
	UpdateMagicSpirit(ctx context.Context, in *UpdateMagicSpiritReq, opts ...grpc.CallOption) (*UpdateMagicSpiritResp, error)
	AddMagicSpiritPond(ctx context.Context, in *AddMagicSpiritPondReq, opts ...grpc.CallOption) (*AddMagicSpiritPondResp, error)
	GetMagicSpiritPond(ctx context.Context, in *GetMagicSpiritPondReq, opts ...grpc.CallOption) (*GetMagicSpiritPondResp, error)
	GetMagicSpiritConfTmp(ctx context.Context, in *GetMagicSpiritConfTmpReq, opts ...grpc.CallOption) (*GetMagicSpiritConfTmpResp, error)
	AddMagicSpiritBlacklist(ctx context.Context, in *AddMagicSpiritBlacklistReq, opts ...grpc.CallOption) (*AddMagicSpiritBlacklistResp, error)
	GetMagicSpiritBlacklist(ctx context.Context, in *GetMagicSpiritBlackListReq, opts ...grpc.CallOption) (*GetMagicSpiritBlackListResp, error)
	DelMagicSpiritBlacklist(ctx context.Context, in *DelMagicSpiritBlacklistReq, opts ...grpc.CallOption) (*DelMagicSpiritBlacklistResp, error)
	SetCommonConf(ctx context.Context, in *SetCommonConfReq, opts ...grpc.CallOption) (*SetCommonConfResp, error)
	GetCommonConf(ctx context.Context, in *GetCommonConfReq, opts ...grpc.CallOption) (*GetCommonConfResp, error)
	// 升级礼物
	// 获取升级礼物的所有历史版本，后台管理使用
	GetLevelupPresentVersionList(ctx context.Context, in *ItemReq, opts ...grpc.CallOption) (*VersionList, error)
	// 新增活动升级礼物的版本，后台管理使用
	AddLevelupPresentVersion(ctx context.Context, in *ItemVersionReq, opts ...grpc.CallOption) (*EmptyMsg, error)
	// 查询父级礼物列表
	GetLevelupParentPresentList(ctx context.Context, in *OffsetTypeReq, opts ...grpc.CallOption) (*EntireParentPresentDataList, error)
	// 查询子级礼物列表
	GetLevelupChildPresentList(ctx context.Context, in *ItemReq, opts ...grpc.CallOption) (*EntireChildPresentDataList, error)
	// 新增父级升级礼物信息
	AddLevelupParentPresent(ctx context.Context, in *AddLevelupParentPresentReq, opts ...grpc.CallOption) (*EmptyMsg, error)
	// 修改父级升级礼物信息
	UpdateLevelupParentPresent(ctx context.Context, in *UpdateLevelupParentPresentReq, opts ...grpc.CallOption) (*EmptyMsg, error)
	// 删除父级升级礼物
	DeleteLevelupParentPresent(ctx context.Context, in *ItemReq, opts ...grpc.CallOption) (*EmptyMsg, error)
	// 新增子级升级礼物信息
	AddLevelupChildPresent(ctx context.Context, in *AddLevelupChildPresentReq, opts ...grpc.CallOption) (*EmptyMsg, error)
	// 修改子级升级礼物信息
	UpdateLevelupChildPresent(ctx context.Context, in *UpdateLevelupChildPresentReq, opts ...grpc.CallOption) (*EmptyMsg, error)
	// 删除子级升级礼物
	DeleteLevelupChildPresent(ctx context.Context, in *ItemReq, opts ...grpc.CallOption) (*EmptyMsg, error)
	// 查询父级礼物信息
	GetLevelupParentPresent(ctx context.Context, in *ItemReq, opts ...grpc.CallOption) (*EntireParentPresentData, error)
	// 查询子级礼物信息
	GetLevelupChildPresent(ctx context.Context, in *ItemReq, opts ...grpc.CallOption) (*EntireChildPresentData, error)
	// 结算工单化
	// 税点录入
	RecordTaxRate(ctx context.Context, in *settlement_bill.RecordTaxRateReq, opts ...grpc.CallOption) (*settlement_bill.RecordTaxRateResp, error)
	// 获取当前时间点会长税点
	GetGuildTaxRate(ctx context.Context, in *settlement_bill.GetGuildTaxRateReq, opts ...grpc.CallOption) (*settlement_bill.GetGuildTaxRateResp, error)
	// 获取税点列表
	GetTaxRateList(ctx context.Context, in *settlement_bill.GetTaxRateListReq, opts ...grpc.CallOption) (*settlement_bill.GetTaxRateListResp, error)
	// 额外收益录入
	RecordExtraIncome(ctx context.Context, in *settlement_bill.RecordExtraIncomeReq, opts ...grpc.CallOption) (*settlement_bill.RecordExtraIncomeResp, error)
	// 获取额外收益录入列表
	GetExtraIncomeRecordList(ctx context.Context, in *settlement_bill.GetExtraIncomeRecordListReq, opts ...grpc.CallOption) (*settlement_bill.GetExtraIncomeRecordListResp, error)
	// 获取额外收益详情列表 - 深度
	GetExtraIncomeDetailDeepCoop(ctx context.Context, in *settlement_bill.GetExtraIncomeDetailReq, opts ...grpc.CallOption) (*settlement_bill.GetExtraIncomeDetailDeepCoopResp, error)
	// 获取额外收益详情列表 - 主播补贴
	GetExtraIncomeDetailChannelSubsidy(ctx context.Context, in *settlement_bill.GetExtraIncomeDetailReq, opts ...grpc.CallOption) (*settlement_bill.GetExtraIncomeDetailChannelSubsidyResp, error)
	// 获取额外收益详情列表 - 新公会补贴
	GetExtraIncomeDetailNewGuildSubsidy(ctx context.Context, in *settlement_bill.GetExtraIncomeDetailReq, opts ...grpc.CallOption) (*settlement_bill.GetExtraIncomeDetailNewGuildSubsidyResp, error)
	// 获取额外收益详情列表 - 扣款
	GetExtraIncomeDetailDeduct(ctx context.Context, in *settlement_bill.GetExtraIncomeDetailReq, opts ...grpc.CallOption) (*settlement_bill.GetExtraIncomeDetailDeductResp, error)
	// 黑暗礼物奖励相关接口
	AddDarkGiftBonusBuffConf(ctx context.Context, in *dark_gift_bonus.AddBuffConfReq, opts ...grpc.CallOption) (*dark_gift_bonus.AddBuffConfResp, error)
	DelDarkGiftBonusBuffConf(ctx context.Context, in *dark_gift_bonus.DelBuffConfReq, opts ...grpc.CallOption) (*dark_gift_bonus.DelBuffConfResp, error)
	GetDarkGiftBonusBuffConf(ctx context.Context, in *dark_gift_bonus.GetBuffConfReq, opts ...grpc.CallOption) (*dark_gift_bonus.GetBuffConfResp, error)
	UpdateDarkGiftBonusBuffConf(ctx context.Context, in *dark_gift_bonus.UpdateBuffConfReq, opts ...grpc.CallOption) (*dark_gift_bonus.UpdateBuffConfResp, error)
	// 坐骑相关接口
	// 添加座骑配置
	AddUserDecorationConfig(ctx context.Context, in *AddUserDecorationConfigReq, opts ...grpc.CallOption) (*AddUserDecorationConfigResp, error)
	// 删除座骑配置
	DelUserDecorationConfig(ctx context.Context, in *DelUserDecorationConfigReq, opts ...grpc.CallOption) (*DelUserDecorationConfigResp, error)
	// 更新座骑配置
	UpdateUserDecorationConfig(ctx context.Context, in *UpdateUserDecorationConfigReq, opts ...grpc.CallOption) (*UpdateUserDecorationConfigResp, error)
	// 获取所有座骑配置
	GetChannelEnterSpecialEffectConfig(ctx context.Context, in *EmptyMsg, opts ...grpc.CallOption) (*ChannelEnterSpecialEffectConfig, error)
	// 配置财富/贵族等级特定座骑
	SetUserDecorationSpecialLevel(ctx context.Context, in *SetUserDecorationSpecialLevelReq, opts ...grpc.CallOption) (*SetUserDecorationSpecialLevelResp, error)
	// 发放座骑
	GrantDecorationToUserV2(ctx context.Context, in *GrantDecorationToUserReq, opts ...grpc.CallOption) (*GrantDecorationToUserResp, error)
	// 批量发放座骑
	BatchGrantDecorationToUser(ctx context.Context, in *BatchGrantDecorationToUserReq, opts ...grpc.CallOption) (*BatchGrantDecorationToUserResp, error)
	// 添加稀缺关系
	AddRareRelationship(ctx context.Context, in *RareRelationshipAddReq, opts ...grpc.CallOption) (*RareRelationshipAddResp, error)
	// 编辑稀缺关系
	UpdateRareRelationship(ctx context.Context, in *RareRelationshipUpdateReq, opts ...grpc.CallOption) (*RareRelationshipUpdateResp, error)
	// 删除稀缺关系
	DelRareRelationship(ctx context.Context, in *RareRelationshipDeleteReq, opts ...grpc.CallOption) (*RareRelationshipDeleteResp, error)
	// 分页查询稀缺关系列表
	GetRareRelationshipList(ctx context.Context, in *RareRelationshipListReq, opts ...grpc.CallOption) (*RareRelationshipListResp, error)
	// 根据条件查询稀缺关系
	GetRareRelationship(ctx context.Context, in *RareRelationshipGetReq, opts ...grpc.CallOption) (*RareRelationshipGetResp, error)
	// 分页查询稀缺关系下发列表
	GetChannelRareRelationshipBindingList(ctx context.Context, in *ChannelRareRelationshipBindingListReq, opts ...grpc.CallOption) (*ChannelRareRelationshipBindingListResp, error)
	// 下发稀缺关系
	AddChannelRareRelationshipBinding(ctx context.Context, in *ChannelRareRelationshipBindingAddReq, opts ...grpc.CallOption) (*ChannelRareRelationshipBindingAddResp, error)
	// 编辑下发稀缺关系信息
	UpdateChannelRareRelationshipBinding(ctx context.Context, in *ChannelRareRelationshipBindingUpdateReq, opts ...grpc.CallOption) (*ChannelRareRelationshipBindingUpdateResp, error)
	// 删除下发稀缺关系信息
	DelChannelRareRelationshipBinding(ctx context.Context, in *ChannelRareRelationshipBindingDeleteReq, opts ...grpc.CallOption) (*ChannelRareRelationshipBindingDeleteResp, error)
	//  主题房相关
	ListTopicChannel(ctx context.Context, in *ListTopicChannelReq, opts ...grpc.CallOption) (*ListTopicChannelResp, error)
	// 主播考核接口/////////////////////////////////////////////////////
	// 上传白名单
	AnchorCheckUploadWhiteList(ctx context.Context, in *WhiteListReq, opts ...grpc.CallOption) (*EmptyMsg, error)
	// 查询考核列表
	AnchorCheckGetCheckList(ctx context.Context, in *AnchorCheckGetCheckListReq, opts ...grpc.CallOption) (*AnchorCheckGetCheckListResp, error)
	// 录入考核
	AnchorCheckSetCheckData(ctx context.Context, in *AnchorCheckSetCheckDataReq, opts ...grpc.CallOption) (*EmptyMsg, error)
	// 导出某个时间段白名单过期的用户
	ExportWhiteExpireList(ctx context.Context, in *ExportWhiteExpireListReq, opts ...grpc.CallOption) (*ExportWhiteExpireListResp, error)
	// 置顶pia戏房间
	AddStickPiaRoom(ctx context.Context, in *AddStickPiaRoomReq, opts ...grpc.CallOption) (*EmptyMsg, error)
	GetStickPiaRoom(ctx context.Context, in *GetStickPiaRoomReq, opts ...grpc.CallOption) (*GetStickPiaRoomResp, error)
	UpdateStickPiaRoom(ctx context.Context, in *UpdateStickPiaRoomReq, opts ...grpc.CallOption) (*EmptyMsg, error)
	// 房间预告位（不使用）
	AddPiaRoomAd(ctx context.Context, in *AddPiaRoomAdReq, opts ...grpc.CallOption) (*EmptyMsg, error)
	GetPiaRoomAd(ctx context.Context, in *GetPiaRoomAdReq, opts ...grpc.CallOption) (*GetPiaRoomAdResp, error)
	UpdatePiaRoomAd(ctx context.Context, in *UpdatePiaRoomAdReq, opts ...grpc.CallOption) (*EmptyMsg, error)
	// 删除记录，共用
	DelPiaRoomRecord(ctx context.Context, in *DelPiaRoomRecordReq, opts ...grpc.CallOption) (*EmptyMsg, error)
	// 房间pia戏权限管理
	GrantPiaRoomPermission(ctx context.Context, in *GrantPiaRoomPermissionReq, opts ...grpc.CallOption) (*EmptyMsg, error)
	GetPiaRoomPermission(ctx context.Context, in *GetPiaRoomPermissionReq, opts ...grpc.CallOption) (*GetPiaRoomPermissionResp, error)
	CancelPiaRoomPermission(ctx context.Context, in *CancelPiaRoomPermissionReq, opts ...grpc.CallOption) (*EmptyMsg, error)
	BatchGetGuildInfoByDisplayId(ctx context.Context, in *BatchGetGuildInfoByDisplayIdReq, opts ...grpc.CallOption) (*BatchGetGuildInfoByDisplayIdResp, error)
	BatchGrantPiaRoomPermission(ctx context.Context, in *BatchGrantPiaRoomPermissionReq, opts ...grpc.CallOption) (*EmptyMsg, error)
	BatchCancelPiaRoomPermission(ctx context.Context, in *BatchCancelPiaRoomPermissionReq, opts ...grpc.CallOption) (*EmptyMsg, error)
	GetTimeOverlapped(ctx context.Context, in *GetTimeOverlappedReq, opts ...grpc.CallOption) (*EmptyMsg, error)
	// pia戏剧本置顶
	AddStickDrama(ctx context.Context, in *AddStickDramaReq, opts ...grpc.CallOption) (*EmptyMsg, error)
	UpdateStickDrama(ctx context.Context, in *UpdateStickDramaReq, opts ...grpc.CallOption) (*EmptyMsg, error)
	DeleteStickDrama(ctx context.Context, in *DeleteStickDramaReq, opts ...grpc.CallOption) (*EmptyMsg, error)
	SearchStickDrama(ctx context.Context, in *SearchStickDramaReq, opts ...grpc.CallOption) (*SearchStickDramaResp, error)
	GetDramaInfoByDramaId(ctx context.Context, in *GetDramaInfoByDramaIdReq, opts ...grpc.CallOption) (*GetDramaInfoByDramaIdResp, error)
	GetStickDramaTags(ctx context.Context, in *GetStickDramaTagsReq, opts ...grpc.CallOption) (*GetStickDramaTagsResp, error)
	CheckStickDramaTime(ctx context.Context, in *CheckStickDramaTimeReq, opts ...grpc.CallOption) (*EmptyMsg, error)
	// ****** 房间推荐相关 *********//
	GetFlowCardLimitConfList(ctx context.Context, in *GetFlowCardLimitConfListReq, opts ...grpc.CallOption) (*GetFlowCardLimitConfListResp, error)
	AddFlowCardLimitConf(ctx context.Context, in *AddFlowCardLimitConfReq, opts ...grpc.CallOption) (*AddFlowCardLimitConfResp, error)
	UpdateFlowCardLimitConf(ctx context.Context, in *UpdateFlowCardLimitConfReq, opts ...grpc.CallOption) (*UpdateFlowCardLimitConfResp, error)
	DelFlowCardLimitConf(ctx context.Context, in *DelFlowCardLimitConfReq, opts ...grpc.CallOption) (*DelFlowCardLimitConfResp, error)
	GetGrantFlowCardList(ctx context.Context, in *GetGrantFlowCardListReq, opts ...grpc.CallOption) (*GetGrantFlowCardListResp, error)
	GrantFlowCard(ctx context.Context, in *GrantFlowCardReq, opts ...grpc.CallOption) (*GrantFlowCardResp, error)
	ReclaimGrantedFlowCard(ctx context.Context, in *ReclaimGrantedFlowCardReq, opts ...grpc.CallOption) (*ReclaimGrantedFlowCardResp, error)
	BanGrantedFlowCard(ctx context.Context, in *BanGrantedFlowCardReq, opts ...grpc.CallOption) (*BanGrantedFlowCardResp, error)
	GetPrepareChannelList(ctx context.Context, in *GetPrepareChannelListReq, opts ...grpc.CallOption) (*GetPrepareChannelListResp, error)
	SetPrepareChannel(ctx context.Context, in *SetPrepareChannelReq, opts ...grpc.CallOption) (*SetPrepareChannelResp, error)
	DelPrepareChannel(ctx context.Context, in *DelPrepareChannelReq, opts ...grpc.CallOption) (*DelPrepareChannelResp, error)
	GetGuildChannelList(ctx context.Context, in *GetGuildChannelListReq, opts ...grpc.CallOption) (*GetGuildChannelListResp, error)
	GetChannelPrepareList(ctx context.Context, in *GetChannelPrepareListReq, opts ...grpc.CallOption) (*GetChannelPrepareListResp, error)
	GetPrepareBackupList(ctx context.Context, in *GetPrepareBackupListReq, opts ...grpc.CallOption) (*GetPrepareBackupListResp, error)
	GetPrepareOperRecordList(ctx context.Context, in *GetPrepareOperRecordListReq, opts ...grpc.CallOption) (*GetPrepareOperRecordListResp, error)
	GetDisplaySceneInfoList(ctx context.Context, in *GetDisplaySceneInfoListReq, opts ...grpc.CallOption) (*GetDisplaySceneInfoListResp, error)
	AddDisplaySceneInfo(ctx context.Context, in *AddDisplaySceneInfoReq, opts ...grpc.CallOption) (*AddDisplaySceneInfoResp, error)
	DelDisplaySceneInfo(ctx context.Context, in *DelDisplaySceneInfoReq, opts ...grpc.CallOption) (*DelDisplaySceneInfoResp, error)
	// 房间玩法管理
	BatchGetChannelMode(ctx context.Context, in *BatchGetChannelModeReq, opts ...grpc.CallOption) (*BatchGetChannelModeResp, error)
	BatchOperateChannelMode(ctx context.Context, in *BatchOperateChannelModeReq, opts ...grpc.CallOption) (*EmptyMsg, error)
	// 骑士团
	GetKnightScoreList(ctx context.Context, in *GetKnightScoreListReq, opts ...grpc.CallOption) (*GetKnightScoreListResp, error)
	// 礼物额外配置
	// 按id/名称搜索礼物
	SearchPresent(ctx context.Context, in *SearchPresentReq, opts ...grpc.CallOption) (*SearchPresentResp, error)
	// 获取已有礼物浮层的礼物列表
	GetPresentFloatLayer(ctx context.Context, in *GetPresentFloatLayerReq, opts ...grpc.CallOption) (*GetPresentFloatLayerResp, error)
	// 添加礼物浮层
	AddPresentFloatLayer(ctx context.Context, in *AddPresentFloatLayerReq, opts ...grpc.CallOption) (*AddPresentFloatLayerResp, error)
	// 修改礼物浮层
	UpdatePresentFloatLayer(ctx context.Context, in *UpdatePresentFloatLayerReq, opts ...grpc.CallOption) (*UpdatePresentFloatLayerResp, error)
	// 删除礼物浮层
	DelPresentFloatLayer(ctx context.Context, in *DelPresentFloatLayerReq, opts ...grpc.CallOption) (*DelPresentFloatLayerResp, error)
	// 获取闪光效果库
	GetFlashEffectConfig(ctx context.Context, in *GetFlashEffectConfigReq, opts ...grpc.CallOption) (*GetFlashEffectConfigResp, error)
	// 添加闪光效果
	AddFlashEffectConfig(ctx context.Context, in *AddFlashEffectConfigReq, opts ...grpc.CallOption) (*AddFlashEffectConfigResp, error)
	// 更新闪光效果
	UpdateFlashEffectConfig(ctx context.Context, in *UpdateFlashEffectConfigReq, opts ...grpc.CallOption) (*UpdateFlashEffectConfigResp, error)
	// 删除闪光效果
	DelFlashEffectConfig(ctx context.Context, in *DelFlashEffectConfigReq, opts ...grpc.CallOption) (*DelFlashEffectConfigResp, error)
	// 获取已绑定闪光效果的礼物列表
	GetPresentFlashEffect(ctx context.Context, in *GetPresentFlashEffectReq, opts ...grpc.CallOption) (*GetPresentFlashEffectResp, error)
	// 绑定闪光效果(更新、删除都用此接口)
	BoundPresentFlashEffect(ctx context.Context, in *BoundPresentFlashEffectReq, opts ...grpc.CallOption) (*BoundPresentFlashEffectResp, error)
	// UpsertDecoration 用于为用户添加或更新一个装饰品。
	UpsertDecoration(ctx context.Context, in *UpsertDecorationReq, opts ...grpc.CallOption) (*UpsertDecorationResp, error)
	// Decorations 返回某类装饰品。
	Decorations(ctx context.Context, in *DecorationsReq, opts ...grpc.CallOption) (*DecorationsResp, error)
	// InsertDecoration 插入装饰品。
	InsertDecoration(ctx context.Context, in *InsertDecorationReq, opts ...grpc.CallOption) (*InsertDecorationResp, error)
	// DelDecoration 删除装饰品。
	DelDecoration(ctx context.Context, in *DelDecorationReq, opts ...grpc.CallOption) (*DelDecorationResp, error)
	// UserDecorations 查询用户的装饰
	UserDecorations(ctx context.Context, in *UserDecorationsReq, opts ...grpc.CallOption) (*UserDecorationsResp, error)
	// BatchUserDecorations 批量为用户添加或更新一个装饰品
	BatchUserDecorations(ctx context.Context, in *BatchUpsertDecorationReq, opts ...grpc.CallOption) (*BatchUpsertDecorationResp, error)
	// 靓号管理
	UpdateAlias(ctx context.Context, in *UpdateAliasReq, opts ...grpc.CallOption) (*UpdateAliasResp, error)
	ChangeDisplayID(ctx context.Context, in *ChangeDisplayIDReq, opts ...grpc.CallOption) (*ChangeDisplayIDResp, error)
	ListAlias(ctx context.Context, in *ListAliasReq, opts ...grpc.CallOption) (*ListAliasResp, error)
	// 创建账号
	CreateUserByAlias(ctx context.Context, in *CreateUserByAliasReq, opts ...grpc.CallOption) (*CreateUserByAliasResp, error)
	// 专属定制礼物
	AddCustomizedPresentConfig(ctx context.Context, in *AddCustomizedPresentConfigReq, opts ...grpc.CallOption) (*AddCustomizedPresentConfigResp, error)
	UpdateCustomizedPresentConfig(ctx context.Context, in *UpdateCustomizedPresentConfigReq, opts ...grpc.CallOption) (*UpdateCustomizedPresentConfigResp, error)
	GetAllCustomizedPresentConfig(ctx context.Context, in *GetAllCustomizedPresentConfigReq, opts ...grpc.CallOption) (*GetAllCustomizedPresentConfigResp, error)
	DelCustomizedPresentConfig(ctx context.Context, in *DelCustomizedPresentConfigReq, opts ...grpc.CallOption) (*DelCustomizedPresentConfigResp, error)
	// 从T盾获取用户的违规信息
	GetUserViolationsInfo(ctx context.Context, in *GetUserViolationsInfoReq, opts ...grpc.CallOption) (*GetUserViolationsInfoResp, error)
	// 强制抱上麦
	TakeHoldChannelMicByForce(ctx context.Context, in *ChannelMicForceTakeHoldReq, opts ...grpc.CallOption) (*ChannelMicForceTakeHoldResp, error)
	SetChannelMicStatus(ctx context.Context, in *SetChannelMicStatusReq, opts ...grpc.CallOption) (*SetChannelMicStatusResp, error)
	// 房间玩法权限
	BatchGetChannelGameplayPerm(ctx context.Context, in *BatchGetChannelGameplayPermReq, opts ...grpc.CallOption) (*BatchGetChannelGameplayPermResp, error)
	BatchOperateChannelGameplayPerm(ctx context.Context, in *BatchOperateChannelGameplayPermReq, opts ...grpc.CallOption) (*BatchOperateChannelGameplayPermResp, error)
	// 大神带飞券 - 礼物配置
	AddTicketPresentConfig(ctx context.Context, in *AddTicketPresentConfigReq, opts ...grpc.CallOption) (*AddTicketPresentConfigResp, error)
	UpdateTicketPresentConfig(ctx context.Context, in *UpdateTicketPresentConfigReq, opts ...grpc.CallOption) (*UpdateTicketPresentConfigResp, error)
	GetAllTicketPresentConfig(ctx context.Context, in *GetAllTicketPresentConfigReq, opts ...grpc.CallOption) (*GetAllTicketPresentConfigResp, error)
	DelTicketPresentConfig(ctx context.Context, in *DelTicketPresentConfigReq, opts ...grpc.CallOption) (*DelTicketPresentConfigResp, error)
	// 添加注销白名单
	// 支持单个或批量通过TTid/Uid添加白名单
	AddUserUnregWhite(ctx context.Context, in *AddUserUnregWhiteReq, opts ...grpc.CallOption) (*AddUserUnregWhiteResp, error)
	// 查询当前申请加入注销白名单用户的信息
	GetCurUnregUserInfo(ctx context.Context, in *GetCurUnregUserInfoReq, opts ...grpc.CallOption) (*GetCurUnregUserInfoResp, error)
	// 查询用户在申请白名单时的信息
	GetUnregWhiteUserInfo(ctx context.Context, in *GetUnregWhiteUserInfoReq, opts ...grpc.CallOption) (*GetUnregWhiteUserInfoResp, error)
	// 权限礼物 - 珍宝馆
	AddTreasurePrivilege(ctx context.Context, in *AddTreasurePrivilegeReq, opts ...grpc.CallOption) (*AddTreasurePrivilegeResp, error)
	GetTreasurePrivilegeList(ctx context.Context, in *GetTreasurePrivilegeListReq, opts ...grpc.CallOption) (*GetTreasurePrivilegeListResp, error)
	DelTreasurePrivilegeList(ctx context.Context, in *DelTreasurePrivilegeListReq, opts ...grpc.CallOption) (*DelTreasurePrivilegeListResp, error)
	CheckTreasurePrivilege(ctx context.Context, in *CheckTreasurePrivilegeReq, opts ...grpc.CallOption) (*CheckTreasurePrivilegeResp, error)
	// 礼物墙 - 未获得礼物
	// 运营后台 - 批量添加未获得礼物配置
	BatchAddMissingItemsConfig(ctx context.Context, in *BatchAddMissingItemsConfigRequest, opts ...grpc.CallOption) (*BatchAddMissingItemsConfigResponse, error)
	// 运营后台 - 未获得礼物配置获取
	GetAllMissingItemsConfig(ctx context.Context, in *GetAllMissingItemsConfigRequest, opts ...grpc.CallOption) (*GetAllMissingItemsConfigResponse, error)
	// 运营后台 - 未获得礼物配置更新
	UpdateMissingItemsConfig(ctx context.Context, in *UpdateMissingItemsConfigRequest, opts ...grpc.CallOption) (*UpdateMissingItemsConfigResponse, error)
	// 运营后台 - 未获得礼物配置删除
	DeleteMissingItemsConfig(ctx context.Context, in *DeleteMissingItemsConfigRequest, opts ...grpc.CallOption) (*DeleteMissingItemsConfigResponse, error)
}

type apicenterGoClient struct {
	cc *grpc.ClientConn
}

func NewApicenterGoClient(cc *grpc.ClientConn) ApicenterGoClient {
	return &apicenterGoClient{cc}
}

func (c *apicenterGoClient) GetPresentConfigById(ctx context.Context, in *GetPresentConfigByIdReq, opts ...grpc.CallOption) (*GetPresentConfigByIdResp, error) {
	out := new(GetPresentConfigByIdResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetPresentConfigById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetPresentConfigList(ctx context.Context, in *GetPresentConfigListReq, opts ...grpc.CallOption) (*GetPresentConfigListResp, error) {
	out := new(GetPresentConfigListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetPresentConfigList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AddPresentConfig(ctx context.Context, in *AddPresentConfigReq, opts ...grpc.CallOption) (*AddPresentConfigResp, error) {
	out := new(AddPresentConfigResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AddPresentConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) DelPresentConfig(ctx context.Context, in *DelPresentConfigReq, opts ...grpc.CallOption) (*DelPresentConfigResp, error) {
	out := new(DelPresentConfigResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/DelPresentConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) UpdatePresentConfig(ctx context.Context, in *UpdatePresentConfigReq, opts ...grpc.CallOption) (*UpdatePresentConfigResp, error) {
	out := new(UpdatePresentConfigResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/UpdatePresentConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetUserPresentSend(ctx context.Context, in *GetUserPresentSendReq, opts ...grpc.CallOption) (*GetUserPresentSendResp, error) {
	out := new(GetUserPresentSendResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetUserPresentSend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetUserPresentReceive(ctx context.Context, in *GetUserPresentReceiveReq, opts ...grpc.CallOption) (*GetUserPresentReceiveResp, error) {
	out := new(GetUserPresentReceiveResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetUserPresentReceive", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetAllFellowPresent(ctx context.Context, in *GetAllFellowPresentReq, opts ...grpc.CallOption) (*GetAllFellowPresentResp, error) {
	out := new(GetAllFellowPresentResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetAllFellowPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AddPackageCfg(ctx context.Context, in *AddPackageCfgReq, opts ...grpc.CallOption) (*AddPackageCfgResp, error) {
	out := new(AddPackageCfgResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AddPackageCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) DelPackageCfg(ctx context.Context, in *DelPackageCfgReq, opts ...grpc.CallOption) (*DelPackageCfgResp, error) {
	out := new(DelPackageCfgResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/DelPackageCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetPackageCfg(ctx context.Context, in *GetPackageCfgReq, opts ...grpc.CallOption) (*GetPackageCfgResp, error) {
	out := new(GetPackageCfgResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetPackageCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetPackageCfgByIds(ctx context.Context, in *GetPackageCfgByIdsReq, opts ...grpc.CallOption) (*GetPackageCfgByIdsResp, error) {
	out := new(GetPackageCfgByIdsResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetPackageCfgByIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AddPackageItemCfg(ctx context.Context, in *AddPackageItemCfgReq, opts ...grpc.CallOption) (*AddPackageItemCfgResp, error) {
	out := new(AddPackageItemCfgResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AddPackageItemCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) ModPackageItemCfg(ctx context.Context, in *ModPackageItemCfgReq, opts ...grpc.CallOption) (*ModPackageItemCfgResp, error) {
	out := new(ModPackageItemCfgResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/ModPackageItemCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) DelPackageItemCfg(ctx context.Context, in *DelPackageItemCfgReq, opts ...grpc.CallOption) (*DelPackageItemCfgResp, error) {
	out := new(DelPackageItemCfgResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/DelPackageItemCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetPackageItemCfg(ctx context.Context, in *GetPackageItemCfgReq, opts ...grpc.CallOption) (*GetPackageItemCfgResp, error) {
	out := new(GetPackageItemCfgResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetPackageItemCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GiveUserPackage(ctx context.Context, in *GiveUserPackageReq, opts ...grpc.CallOption) (*GiveUserPackageResp, error) {
	out := new(GiveUserPackageResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GiveUserPackage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AddFuncCardCfg(ctx context.Context, in *AddFuncCardCfgReq, opts ...grpc.CallOption) (*AddFuncCardCfgResp, error) {
	out := new(AddFuncCardCfgResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AddFuncCardCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) DelFuncCardCfg(ctx context.Context, in *DelFuncCardCfgReq, opts ...grpc.CallOption) (*DelFuncCardCfgResp, error) {
	out := new(DelFuncCardCfgResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/DelFuncCardCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetFuncCardCfg(ctx context.Context, in *GetFuncCardCfgReq, opts ...grpc.CallOption) (*GetFuncCardCfgResp, error) {
	out := new(GetFuncCardCfgResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetFuncCardCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetUserBackpack(ctx context.Context, in *GetUserBackpackReq, opts ...grpc.CallOption) (*GetUserBackpackResp, error) {
	out := new(GetUserBackpackResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetUserBackpack", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetUserPackageReceive(ctx context.Context, in *GetUserPackageReceiveReq, opts ...grpc.CallOption) (*GetUserPackageReceiveResp, error) {
	out := new(GetUserPackageReceiveResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetUserPackageReceive", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AddBusiness(ctx context.Context, in *AddBusinessReq, opts ...grpc.CallOption) (*AddBusinessResp, error) {
	out := new(AddBusinessResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AddBusiness", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetAllBusiness(ctx context.Context, in *GetAllBusinessReq, opts ...grpc.CallOption) (*GetAllBusinessResp, error) {
	out := new(GetAllBusinessResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetAllBusiness", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetBusinessByIds(ctx context.Context, in *GetBusinessByIdsReq, opts ...grpc.CallOption) (*GetBusinessByIdsResp, error) {
	out := new(GetBusinessByIdsResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetBusinessByIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AddBusinessRiskControlConf(ctx context.Context, in *AddBusinessRiskControlConfReq, opts ...grpc.CallOption) (*AddBusinessRiskControlConfResp, error) {
	out := new(AddBusinessRiskControlConfResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AddBusinessRiskControlConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) ModBusinessRiskControlConf(ctx context.Context, in *ModBusinessRiskControlConfReq, opts ...grpc.CallOption) (*ModBusinessRiskControlConfResp, error) {
	out := new(ModBusinessRiskControlConfResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/ModBusinessRiskControlConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetBusinessRiskControlConf(ctx context.Context, in *GetBusinessRiskControlConfReq, opts ...grpc.CallOption) (*GetBusinessRiskControlConfResp, error) {
	out := new(GetBusinessRiskControlConfResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetBusinessRiskControlConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetUserInfo(ctx context.Context, in *GetUserInfoReq, opts ...grpc.CallOption) (*GetUserInfoResp, error) {
	out := new(GetUserInfoResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetUserInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) BatchGetUserInfo(ctx context.Context, in *BatchGetUserInfoReq, opts ...grpc.CallOption) (*BatchGetUserInfoResp, error) {
	out := new(BatchGetUserInfoResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/BatchGetUserInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetUserWithExtraInfo(ctx context.Context, in *GetUserWithExtraInfoReq, opts ...grpc.CallOption) (*GetUserWithExtraInfoResp, error) {
	out := new(GetUserWithExtraInfoResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetUserWithExtraInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) BatGetUserByTTid(ctx context.Context, in *BatGetUserByTTidReq, opts ...grpc.CallOption) (*BatGetUserByTTidResp, error) {
	out := new(BatGetUserByTTidResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/BatGetUserByTTid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetUserConversion(ctx context.Context, in *GetUserConversionReq, opts ...grpc.CallOption) (*GetUserConversionResp, error) {
	out := new(GetUserConversionResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetUserConversion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) BatchDeductUserItem(ctx context.Context, in *BatchDeductUserItemReq, opts ...grpc.CallOption) (*BatchDeductUserItemResp, error) {
	out := new(BatchDeductUserItemResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/BatchDeductUserItem", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetGuildById(ctx context.Context, in *GetGuildByIdReq, opts ...grpc.CallOption) (*GetGuildByIdResp, error) {
	out := new(GetGuildByIdResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetGuildById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) BatchGetGuild(ctx context.Context, in *BatchGetGuildReq, opts ...grpc.CallOption) (*BatchGetGuildResp, error) {
	out := new(BatchGetGuildResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/BatchGetGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) BatchGetGuildV2(ctx context.Context, in *BatchGetGuildReq, opts ...grpc.CallOption) (*BatchGetGuildResp, error) {
	out := new(BatchGetGuildResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/BatchGetGuildV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) ModifyGuildName(ctx context.Context, in *ModifyGuildNameReq, opts ...grpc.CallOption) (*ModifyGuildNameResp, error) {
	out := new(ModifyGuildNameResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/ModifyGuildName", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) SetGuildShortId(ctx context.Context, in *SetGuildShortIdReq, opts ...grpc.CallOption) (*SetGuildShortIdResp, error) {
	out := new(SetGuildShortIdResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/SetGuildShortId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) UnsetGuildShortId(ctx context.Context, in *UnsetGuildShortIdReq, opts ...grpc.CallOption) (*UnsetGuildShortIdResp, error) {
	out := new(UnsetGuildShortIdResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/UnsetGuildShortId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) ModifyGuildGameLimit(ctx context.Context, in *ModifyGuildGameLimitReq, opts ...grpc.CallOption) (*ModifyGuildGameLimitResp, error) {
	out := new(ModifyGuildGameLimitResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/ModifyGuildGameLimit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetApplySignRecord(ctx context.Context, in *GetApplySignRecordReq, opts ...grpc.CallOption) (*GetApplySignRecordResp, error) {
	out := new(GetApplySignRecordResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetApplySignRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) OfficialHandleApplySign(ctx context.Context, in *OfficialHandleApplySignReq, opts ...grpc.CallOption) (*OfficialHandleApplySignResp, error) {
	out := new(OfficialHandleApplySignResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/OfficialHandleApplySign", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) BatchGetContractInfo(ctx context.Context, in *BatchGetContractInfoReq, opts ...grpc.CallOption) (*BatchGetContractInfoResp, error) {
	out := new(BatchGetContractInfoResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/BatchGetContractInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) ReclaimAnchorIdentity(ctx context.Context, in *ReclaimAnchorIdentityReq, opts ...grpc.CallOption) (*ReclaimAnchorIdentityResp, error) {
	out := new(ReclaimAnchorIdentityResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/ReclaimAnchorIdentity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) BatchGetApplyBlacklist(ctx context.Context, in *BatchGetApplyBlacklistReq, opts ...grpc.CallOption) (*BatchGetApplyBlacklistResp, error) {
	out := new(BatchGetApplyBlacklistResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/BatchGetApplyBlacklist", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) HandleApplyBlackInfo(ctx context.Context, in *HandleApplyBlackInfoReq, opts ...grpc.CallOption) (*HandleApplyBlackInfoResp, error) {
	out := new(HandleApplyBlackInfoResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/HandleApplyBlackInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetLiveAnchorExamine(ctx context.Context, in *GetLiveAnchorExamineReq, opts ...grpc.CallOption) (*GetLiveAnchorExamineResp, error) {
	out := new(GetLiveAnchorExamineResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetLiveAnchorExamine", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetAllLiveAnchorExamine(ctx context.Context, in *GetAllLiveAnchorExamineReq, opts ...grpc.CallOption) (*GetAllLiveAnchorExamineResp, error) {
	out := new(GetAllLiveAnchorExamineResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetAllLiveAnchorExamine", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) UpdateLiveAnchorExamineStatus(ctx context.Context, in *UpdateLiveAnchorExamineStatusReq, opts ...grpc.CallOption) (*UpdateLiveAnchorExamineStatusResp, error) {
	out := new(UpdateLiveAnchorExamineStatusResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/UpdateLiveAnchorExamineStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) UpdateLiveAnchorExamineTime(ctx context.Context, in *UpdateLiveAnchorExamineTimeReq, opts ...grpc.CallOption) (*UpdateLiveAnchorExamineTimeResp, error) {
	out := new(UpdateLiveAnchorExamineTimeResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/UpdateLiveAnchorExamineTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetAnchorIdentityLog(ctx context.Context, in *GetAnchorIdentityLogReq, opts ...grpc.CallOption) (*GetAnchorIdentityLogResp, error) {
	out := new(GetAnchorIdentityLogResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetAnchorIdentityLog", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetPractitionerMonthActiveInfo(ctx context.Context, in *GetPractitionerMonthActiveInfoReq, opts ...grpc.CallOption) (*GetPractitionerMonthActiveInfoResp, error) {
	out := new(GetPractitionerMonthActiveInfoResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetPractitionerMonthActiveInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AwardTBeanToUser(ctx context.Context, in *AwardTBeanToUserReq, opts ...grpc.CallOption) (*AwardTBeanToUserResp, error) {
	out := new(AwardTBeanToUserResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AwardTBeanToUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AwardDiamondToUser(ctx context.Context, in *AwardDiamondToUserReq, opts ...grpc.CallOption) (*AwardDiamondToUserResp, error) {
	out := new(AwardDiamondToUserResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AwardDiamondToUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetAwardDiamondRecord(ctx context.Context, in *GetAwardDiamondRecordReq, opts ...grpc.CallOption) (*GetAwardDiamondRecordResp, error) {
	out := new(GetAwardDiamondRecordResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetAwardDiamondRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AwardExpToUser(ctx context.Context, in *AwardExpToUserReq, opts ...grpc.CallOption) (*AwardExpToUserResp, error) {
	out := new(AwardExpToUserResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AwardExpToUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetAwardExpRecord(ctx context.Context, in *GetAwardExpRecordReq, opts ...grpc.CallOption) (*GetAwardExpRecordResp, error) {
	out := new(GetAwardExpRecordResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetAwardExpRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetUserLoginInfo(ctx context.Context, in *GetUserLoginInfoReq, opts ...grpc.CallOption) (*GetUserLoginInfoResp, error) {
	out := new(GetUserLoginInfoResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetUserLoginInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetBannedRecord(ctx context.Context, in *GetBannedRecordReq, opts ...grpc.CallOption) (*GetBannedRecordResp, error) {
	out := new(GetBannedRecordResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetBannedRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetBannedOperator(ctx context.Context, in *GetBannedOperatorReq, opts ...grpc.CallOption) (*GetBannedOperatorResp, error) {
	out := new(GetBannedOperatorResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetBannedOperator", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetBannedAppealRecord(ctx context.Context, in *GetBannedAppealRecordReq, opts ...grpc.CallOption) (*GetBannedAppealRecordResp, error) {
	out := new(GetBannedAppealRecordResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetBannedAppealRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) UpdateBannedAppealRecord(ctx context.Context, in *UpdateBannedAppealRecordReq, opts ...grpc.CallOption) (*UpdateBannedAppealRecordResp, error) {
	out := new(UpdateBannedAppealRecordResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/UpdateBannedAppealRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetBannedCheckRecord(ctx context.Context, in *GetBannedCheckRecordReq, opts ...grpc.CallOption) (*GetBannedCheckRecordResp, error) {
	out := new(GetBannedCheckRecordResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetBannedCheckRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) UpdateBannedCheckRecord(ctx context.Context, in *UpdateBannedCheckRecordReq, opts ...grpc.CallOption) (*UpdateBannedCheckRecordResp, error) {
	out := new(UpdateBannedCheckRecordResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/UpdateBannedCheckRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetBannedCheckOperator(ctx context.Context, in *GetBannedCheckOperatorReq, opts ...grpc.CallOption) (*GetBannedCheckOperatorResp, error) {
	out := new(GetBannedCheckOperatorResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetBannedCheckOperator", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetUserLoginDevice(ctx context.Context, in *GetUserLoginDeviceReq, opts ...grpc.CallOption) (*GetUserLoginDeviceResp, error) {
	out := new(GetUserLoginDeviceResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetUserLoginDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetUserLoginWithDevice(ctx context.Context, in *GetUserLoginWithDeviceReq, opts ...grpc.CallOption) (*GetUserLoginWithDeviceResp, error) {
	out := new(GetUserLoginWithDeviceResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetUserLoginWithDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) BanUser(ctx context.Context, in *BanUserReq, opts ...grpc.CallOption) (*BanUserResp, error) {
	out := new(BanUserResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/BanUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) RecoverUser(ctx context.Context, in *RecoverUserReq, opts ...grpc.CallOption) (*RecoverUserResp, error) {
	out := new(RecoverUserResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/RecoverUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) BatchRecoverUser(ctx context.Context, in *BatchRecoverUserReq, opts ...grpc.CallOption) (*BatchRecoverUserResp, error) {
	out := new(BatchRecoverUserResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/BatchRecoverUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetDeviceLastLoginInfo(ctx context.Context, in *GetDeviceLastLoginInfoReq, opts ...grpc.CallOption) (*GetDeviceLastLoginInfoResp, error) {
	out := new(GetDeviceLastLoginInfoResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetDeviceLastLoginInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetBanLog(ctx context.Context, in *GetBanLogReq, opts ...grpc.CallOption) (*GetBanLogResp, error) {
	out := new(GetBanLogResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetBanLog", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetUserBanStatus(ctx context.Context, in *GetUserBanStatusReq, opts ...grpc.CallOption) (*GetUserBanStatusResp, error) {
	out := new(GetUserBanStatusResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetUserBanStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) BatchBanUserWithDevice(ctx context.Context, in *BatchBanUserWithDeviceReq, opts ...grpc.CallOption) (*BatchBanUserWithDeviceResp, error) {
	out := new(BatchBanUserWithDeviceResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/BatchBanUserWithDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) CanRecoverDevice(ctx context.Context, in *CanRecoverDeviceReq, opts ...grpc.CallOption) (*CanRecoverDeviceResp, error) {
	out := new(CanRecoverDeviceResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/CanRecoverDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetUserInviteHistory(ctx context.Context, in *GetUserInviteHistoryReq, opts ...grpc.CallOption) (*GetUserInviteHistoryResp, error) {
	out := new(GetUserInviteHistoryResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetUserInviteHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) ResetUserPassword(ctx context.Context, in *ResetUserPasswordReq, opts ...grpc.CallOption) (*ResetUserPasswordResp, error) {
	out := new(ResetUserPasswordResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/ResetUserPassword", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) UnbindUserPhone(ctx context.Context, in *UnbindUserPhoneReq, opts ...grpc.CallOption) (*UnbindUserPhoneResp, error) {
	out := new(UnbindUserPhoneResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/UnbindUserPhone", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) ClearSecurityQuestion(ctx context.Context, in *ClearSecurityQuestionReq, opts ...grpc.CallOption) (*ClearSecurityQuestionResp, error) {
	out := new(ClearSecurityQuestionResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/ClearSecurityQuestion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) DetachThirdpart(ctx context.Context, in *DetachThirdpartReq, opts ...grpc.CallOption) (*DetachThirdpartResp, error) {
	out := new(DetachThirdpartResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/DetachThirdpart", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) BatchGetUserOfficialCert(ctx context.Context, in *BatchGetUserOfficialCertReq, opts ...grpc.CallOption) (*BatchGetUserOfficialCertResp, error) {
	out := new(BatchGetUserOfficialCertResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/BatchGetUserOfficialCert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetUserExp(ctx context.Context, in *GetUserExpReq, opts ...grpc.CallOption) (*GetUserExpResp, error) {
	out := new(GetUserExpResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetUserExp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetUserControlInfo(ctx context.Context, in *GetUserControlInfoReq, opts ...grpc.CallOption) (*GetUserControlInfoResp, error) {
	out := new(GetUserControlInfoResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetUserControlInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) BatRemoveUserControlled(ctx context.Context, in *BatRemoveUserControlledReq, opts ...grpc.CallOption) (*BatRemoveUserControlledResp, error) {
	out := new(BatRemoveUserControlledResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/BatRemoveUserControlled", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetTheSameRealNameUserList(ctx context.Context, in *GetTheSameRealNameUserListReq, opts ...grpc.CallOption) (*GetTheSameRealNameUserListResp, error) {
	out := new(GetTheSameRealNameUserListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetTheSameRealNameUserList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetUserRealNameAuthInfoV2(ctx context.Context, in *GetUserRealNameAuthInfoV2Req, opts ...grpc.CallOption) (*GetUserRealNameAuthInfoV2Resp, error) {
	out := new(GetUserRealNameAuthInfoV2Resp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetUserRealNameAuthInfoV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) BatchGetUserRealNameAuthInfo(ctx context.Context, in *BatchGetUserRealNameAuthInfoReq, opts ...grpc.CallOption) (*BatchGetUserRealNameAuthInfoResp, error) {
	out := new(BatchGetUserRealNameAuthInfoResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/BatchGetUserRealNameAuthInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) BatchGetChannelSimpleInfo(ctx context.Context, in *BatchGetChannelSimpleInfoReq, opts ...grpc.CallOption) (*BatchGetChannelSimpleInfoResp, error) {
	out := new(BatchGetChannelSimpleInfoResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/BatchGetChannelSimpleInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetAllReportHistoryList(ctx context.Context, in *GetAllReportHistoryListReq, opts ...grpc.CallOption) (*GetAllReportHistoryListResp, error) {
	out := new(GetAllReportHistoryListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetAllReportHistoryList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetChannelReportStat(ctx context.Context, in *GetChannelReportStatReq, opts ...grpc.CallOption) (*GetChannelReportStatResp, error) {
	out := new(GetChannelReportStatResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetChannelReportStat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetChannelReportProcList(ctx context.Context, in *GetChannelReportProcListReq, opts ...grpc.CallOption) (*GetChannelReportProcListResp, error) {
	out := new(GetChannelReportProcListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetChannelReportProcList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) SanctionChannel(ctx context.Context, in *SanctionChannelReq, opts ...grpc.CallOption) (*SanctionChannelResp, error) {
	out := new(SanctionChannelResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/SanctionChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetBannedChannelList(ctx context.Context, in *GetBannedChannelListReq, opts ...grpc.CallOption) (*GetBannedChannelListResp, error) {
	out := new(GetBannedChannelListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetBannedChannelList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AddChannelWhiteListInfo(ctx context.Context, in *AddChannelWhiteListInfoReq, opts ...grpc.CallOption) (*AddChannelWhiteListInfoResp, error) {
	out := new(AddChannelWhiteListInfoResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AddChannelWhiteListInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) DelChannelWhiteListInfo(ctx context.Context, in *DelChannelWhiteListInfoReq, opts ...grpc.CallOption) (*DelChannelWhiteListInfoResp, error) {
	out := new(DelChannelWhiteListInfoResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/DelChannelWhiteListInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetChannelWhiteList(ctx context.Context, in *GetChannelWhiteListReq, opts ...grpc.CallOption) (*GetChannelWhiteListResp, error) {
	out := new(GetChannelWhiteListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetChannelWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetChannelSimpleInfo(ctx context.Context, in *GetChannelSimpleInfoReq, opts ...grpc.CallOption) (*GetChannelSimpleInfoResp, error) {
	out := new(GetChannelSimpleInfoResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetChannelSimpleInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) BatchGetChannelTag(ctx context.Context, in *BatchGetChannelTagReq, opts ...grpc.CallOption) (*BatchGetChannelTagResp, error) {
	out := new(BatchGetChannelTagResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/BatchGetChannelTag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) PushMsgToChannel(ctx context.Context, in *PushMsgToChannelReq, opts ...grpc.CallOption) (*PushMsgToChannelResp, error) {
	out := new(PushMsgToChannelResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/PushMsgToChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) PushMsgToUser(ctx context.Context, in *PushMsgToUserReq, opts ...grpc.CallOption) (*PushMsgToUserResp, error) {
	out := new(PushMsgToUserResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/PushMsgToUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) PushActBreakingNews(ctx context.Context, in *PushActBreakingNewsReq, opts ...grpc.CallOption) (*PushActBreakingNewsResp, error) {
	out := new(PushActBreakingNewsResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/PushActBreakingNews", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) PushFromExcel(ctx context.Context, in *PushFromExcelReq, opts ...grpc.CallOption) (*PushFromExcelResp, error) {
	out := new(PushFromExcelResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/PushFromExcel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) HobPush(ctx context.Context, in *HobPushReq, opts ...grpc.CallOption) (*HobPushResp, error) {
	out := new(HobPushResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/HobPush", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetHobPushRecord(ctx context.Context, in *GetHobPushRecordReq, opts ...grpc.CallOption) (*GetHobPushRecordResp, error) {
	out := new(GetHobPushRecordResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetHobPushRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetChannelMicMode(ctx context.Context, in *GetChannelMicModeReq, opts ...grpc.CallOption) (*GetChannelMicModeResp, error) {
	out := new(GetChannelMicModeResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetChannelMicMode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AddChannelBackgroundConf(ctx context.Context, in *AddChannelBackgroundConfReq, opts ...grpc.CallOption) (*AddChannelBackgroundConfResp, error) {
	out := new(AddChannelBackgroundConfResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AddChannelBackgroundConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) DelChannelBackgroundConf(ctx context.Context, in *DelChannelBackgroundConfReq, opts ...grpc.CallOption) (*DelChannelBackgroundConfResp, error) {
	out := new(DelChannelBackgroundConfResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/DelChannelBackgroundConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetChannelBackgroundConf(ctx context.Context, in *GetChannelBackgroundConfReq, opts ...grpc.CallOption) (*GetChannelBackgroundConfResp, error) {
	out := new(GetChannelBackgroundConfResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetChannelBackgroundConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AddChannelBackgroundConfV2(ctx context.Context, in *AddChannelBackgroundConfReq, opts ...grpc.CallOption) (*AddChannelBackgroundConfResp, error) {
	out := new(AddChannelBackgroundConfResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AddChannelBackgroundConfV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) UpdateChannelBackgroundConfV2(ctx context.Context, in *UpdateChannelBackgroundConfReq, opts ...grpc.CallOption) (*UpdateChannelBackgroundConfResp, error) {
	out := new(UpdateChannelBackgroundConfResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/UpdateChannelBackgroundConfV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) DelChannelBackgroundConfV2(ctx context.Context, in *DelChannelBackgroundConfReq, opts ...grpc.CallOption) (*DelChannelBackgroundConfResp, error) {
	out := new(DelChannelBackgroundConfResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/DelChannelBackgroundConfV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetChannelBackgroundConfV2(ctx context.Context, in *GetChannelBackgroundConfV2Req, opts ...grpc.CallOption) (*GetChannelBackgroundConfV2Resp, error) {
	out := new(GetChannelBackgroundConfV2Resp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetChannelBackgroundConfV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) BatchGiveChannelBg(ctx context.Context, in *BatchGiveChannelBgReq, opts ...grpc.CallOption) (*BatchGiveChannelBgResp, error) {
	out := new(BatchGiveChannelBgResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/BatchGiveChannelBg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) UpdateGivenChannelBg(ctx context.Context, in *UpdateGivenChannelBgReq, opts ...grpc.CallOption) (*UpdateGivenChannelBgResp, error) {
	out := new(UpdateGivenChannelBgResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/UpdateGivenChannelBg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) DeleteGivenChannelBg(ctx context.Context, in *DeleteGivenChannelBgReq, opts ...grpc.CallOption) (*DeleteGivenChannelBgResp, error) {
	out := new(DeleteGivenChannelBgResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/DeleteGivenChannelBg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) ListGivenChannelBg(ctx context.Context, in *ListGivenChannelBgReq, opts ...grpc.CallOption) (*ListGivenChannelBgResp, error) {
	out := new(ListGivenChannelBgResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/ListGivenChannelBg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) SwitchSearchWhiteList(ctx context.Context, in *SwitchSearchWhiteListReq, opts ...grpc.CallOption) (*SwitchSearchWhiteListResp, error) {
	out := new(SwitchSearchWhiteListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/SwitchSearchWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetSearchWhiteListStatus(ctx context.Context, in *GetSearchWhiteListStatusReq, opts ...grpc.CallOption) (*GetSearchWhiteListStatusResp, error) {
	out := new(GetSearchWhiteListStatusResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetSearchWhiteListStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) BatchAddSearchWhiteList(ctx context.Context, in *BatchAddSearchWhiteListReq, opts ...grpc.CallOption) (*BatchAddSearchWhiteListResp, error) {
	out := new(BatchAddSearchWhiteListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/BatchAddSearchWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) BatchDelSearchWhiteList(ctx context.Context, in *BatchDelSearchWhiteListReq, opts ...grpc.CallOption) (*BatchDelSearchWhiteListResp, error) {
	out := new(BatchDelSearchWhiteListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/BatchDelSearchWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetSearchWhiteList(ctx context.Context, in *GetSearchWhiteListReq, opts ...grpc.CallOption) (*GetSearchWhiteListResp, error) {
	out := new(GetSearchWhiteListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetSearchWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) ExportHotWord(ctx context.Context, in *ExportHotWordReq, opts ...grpc.CallOption) (*ExportHotWordResp, error) {
	out := new(ExportHotWordResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/ExportHotWord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetAnchorDailyRecordWithDateList(ctx context.Context, in *GetAnchorDailyRecordWithDateListReq, opts ...grpc.CallOption) (*GetAnchorDailyRecordWithDateListResp, error) {
	out := new(GetAnchorDailyRecordWithDateListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetAnchorDailyRecordWithDateList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AddAppointPkInfo(ctx context.Context, in *AddAppointPkInfoReq, opts ...grpc.CallOption) (*AddAppointPkInfoResp, error) {
	out := new(AddAppointPkInfoResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AddAppointPkInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) UpdateAppointPkInfo(ctx context.Context, in *UpdateAppointPkInfoReq, opts ...grpc.CallOption) (*UpdateAppointPkInfoResp, error) {
	out := new(UpdateAppointPkInfoResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/UpdateAppointPkInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) DelAppointPkInfo(ctx context.Context, in *DelAppointPkInfoReq, opts ...grpc.CallOption) (*DelAppointPkInfoResp, error) {
	out := new(DelAppointPkInfoResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/DelAppointPkInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetAppointPkInfoList(ctx context.Context, in *GetAppointPkInfoListReq, opts ...grpc.CallOption) (*GetAppointPkInfoListResp, error) {
	out := new(GetAppointPkInfoListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetAppointPkInfoList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) BatchAddAppointPkInfo(ctx context.Context, in *BatchAddAppointPkInfoReq, opts ...grpc.CallOption) (*BatchAddAppointPkInfoResp, error) {
	out := new(BatchAddAppointPkInfoResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/BatchAddAppointPkInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AddPlateConfig(ctx context.Context, in *AddPlateConfigReq, opts ...grpc.CallOption) (*AddPlateConfigResp, error) {
	out := new(AddPlateConfigResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AddPlateConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) UpdatePlateConfig(ctx context.Context, in *UpdatePlateConfigReq, opts ...grpc.CallOption) (*UpdatePlateConfigResp, error) {
	out := new(UpdatePlateConfigResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/UpdatePlateConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) DelPlateConfig(ctx context.Context, in *DelPlateConfigReq, opts ...grpc.CallOption) (*DelPlateConfigResp, error) {
	out := new(DelPlateConfigResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/DelPlateConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetPlateConfigList(ctx context.Context, in *GetPlateConfigListReq, opts ...grpc.CallOption) (*GetPlateConfigListResp, error) {
	out := new(GetPlateConfigListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetPlateConfigList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GrantAnchorPlate(ctx context.Context, in *GrantAnchorPlateReq, opts ...grpc.CallOption) (*GrantAnchorPlateResp, error) {
	out := new(GrantAnchorPlateResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GrantAnchorPlate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) UpdateGrantedPlateInfo(ctx context.Context, in *UpdateGrantedPlateInfoReq, opts ...grpc.CallOption) (*UpdateGrantedPlateInfoResp, error) {
	out := new(UpdateGrantedPlateInfoResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/UpdateGrantedPlateInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) DelGrantedPlateInfo(ctx context.Context, in *DelGrantedPlateInfoReq, opts ...grpc.CallOption) (*DelGrantedPlateInfoResp, error) {
	out := new(DelGrantedPlateInfoResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/DelGrantedPlateInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetGrantedPlateList(ctx context.Context, in *GetGrantedPlateListReq, opts ...grpc.CallOption) (*GetGrantedPlateListResp, error) {
	out := new(GetGrantedPlateListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetGrantedPlateList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetPlateConfigById(ctx context.Context, in *GetPlateConfigByIdReq, opts ...grpc.CallOption) (*GetPlateConfigByIdResp, error) {
	out := new(GetPlateConfigByIdResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetPlateConfigById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) WearAnchorPlate(ctx context.Context, in *WearAnchorPlateReq, opts ...grpc.CallOption) (*WearAnchorPlateResp, error) {
	out := new(WearAnchorPlateResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/WearAnchorPlate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) BatGrantAnchorPlate(ctx context.Context, in *BatGrantAnchorPlateReq, opts ...grpc.CallOption) (*BatGrantAnchorPlateResp, error) {
	out := new(BatGrantAnchorPlateResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/BatGrantAnchorPlate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GrantLiveMissionAward(ctx context.Context, in *GrantLiveMissionAwardReq, opts ...grpc.CallOption) (*GrantLiveMissionAwardResp, error) {
	out := new(GrantLiveMissionAwardResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GrantLiveMissionAward", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetAnchorList(ctx context.Context, in *GetAnchorListReq, opts ...grpc.CallOption) (*GetAnchorListResp, error) {
	out := new(GetAnchorListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetAnchorList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) BatDelChannelLiveInfo(ctx context.Context, in *BatDelChannelLiveInfoReq, opts ...grpc.CallOption) (*BatDelChannelLiveInfoResp, error) {
	out := new(BatDelChannelLiveInfoResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/BatDelChannelLiveInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) SetChannelLiveTag(ctx context.Context, in *SetChannelLiveTagReq, opts ...grpc.CallOption) (*SetChannelLiveTagResp, error) {
	out := new(SetChannelLiveTagResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/SetChannelLiveTag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) BatchAddAnchor(ctx context.Context, in *BatchAddAnchorReq, opts ...grpc.CallOption) (*BatchAddAnchorResp, error) {
	out := new(BatchAddAnchorResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/BatchAddAnchor", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetAnchorOperRecord(ctx context.Context, in *GetAnchorOperRecordReq, opts ...grpc.CallOption) (*GetAnchorOperRecordResp, error) {
	out := new(GetAnchorOperRecordResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetAnchorOperRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AddVirtualAnchorPer(ctx context.Context, in *AddVirtualAnchorPerReq, opts ...grpc.CallOption) (*AddVirtualAnchorPerResp, error) {
	out := new(AddVirtualAnchorPerResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AddVirtualAnchorPer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) UpdateVirtualAnchorPer(ctx context.Context, in *UpdateVirtualAnchorPerReq, opts ...grpc.CallOption) (*UpdateVirtualAnchorPerResp, error) {
	out := new(UpdateVirtualAnchorPerResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/UpdateVirtualAnchorPer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) DelVirtualAnchorPer(ctx context.Context, in *DelVirtualAnchorPerReq, opts ...grpc.CallOption) (*DelVirtualAnchorPerResp, error) {
	out := new(DelVirtualAnchorPerResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/DelVirtualAnchorPer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetVirtualAnchorPerList(ctx context.Context, in *GetVirtualAnchorPerListReq, opts ...grpc.CallOption) (*GetVirtualAnchorPerListResp, error) {
	out := new(GetVirtualAnchorPerListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetVirtualAnchorPerList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GrantFansGiftPrivilege(ctx context.Context, in *GrantFansGiftPrivilegeReq, opts ...grpc.CallOption) (*GrantFansGiftPrivilegeResp, error) {
	out := new(GrantFansGiftPrivilegeResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GrantFansGiftPrivilege", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetAnchorScoreList(ctx context.Context, in *GetAnchorScoreListReq, opts ...grpc.CallOption) (*GetAnchorScoreListResp, error) {
	out := new(GetAnchorScoreListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetAnchorScoreList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetMinorityGame(ctx context.Context, in *GetMinorityGameReq, opts ...grpc.CallOption) (*GetMinorityGameResp, error) {
	out := new(GetMinorityGameResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetMinorityGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AddMinorityGame(ctx context.Context, in *AddMinorityGameReq, opts ...grpc.CallOption) (*AddMinorityGameResp, error) {
	out := new(AddMinorityGameResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AddMinorityGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) RemoveMinorityGame(ctx context.Context, in *RemoveMinorityGameReq, opts ...grpc.CallOption) (*RemoveMinorityGameResp, error) {
	out := new(RemoveMinorityGameResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/RemoveMinorityGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) ChangeMinorityGame(ctx context.Context, in *ChangeMinorityGameReq, opts ...grpc.CallOption) (*ChangeMinorityGameResp, error) {
	out := new(ChangeMinorityGameResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/ChangeMinorityGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AddTempNobility(ctx context.Context, in *AddTempNobilityReq, opts ...grpc.CallOption) (*AddTempNobilityResp, error) {
	out := new(AddTempNobilityResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AddTempNobility", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetTempNobilityList(ctx context.Context, in *GetTempNobilityListReq, opts ...grpc.CallOption) (*GetTempNobilityListResp, error) {
	out := new(GetTempNobilityListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetTempNobilityList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AddMagicSpirit(ctx context.Context, in *AddMagicSpiritReq, opts ...grpc.CallOption) (*AddMagicSpiritResp, error) {
	out := new(AddMagicSpiritResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AddMagicSpirit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) DelMagicSpirit(ctx context.Context, in *DelMagicSpiritReq, opts ...grpc.CallOption) (*DelMagicSpiritResp, error) {
	out := new(DelMagicSpiritResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/DelMagicSpirit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetMagicSpirit(ctx context.Context, in *GetMagicSpiritReq, opts ...grpc.CallOption) (*GetMagicSpiritResp, error) {
	out := new(GetMagicSpiritResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetMagicSpirit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) UpdateMagicSpirit(ctx context.Context, in *UpdateMagicSpiritReq, opts ...grpc.CallOption) (*UpdateMagicSpiritResp, error) {
	out := new(UpdateMagicSpiritResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/UpdateMagicSpirit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AddMagicSpiritPond(ctx context.Context, in *AddMagicSpiritPondReq, opts ...grpc.CallOption) (*AddMagicSpiritPondResp, error) {
	out := new(AddMagicSpiritPondResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AddMagicSpiritPond", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetMagicSpiritPond(ctx context.Context, in *GetMagicSpiritPondReq, opts ...grpc.CallOption) (*GetMagicSpiritPondResp, error) {
	out := new(GetMagicSpiritPondResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetMagicSpiritPond", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetMagicSpiritConfTmp(ctx context.Context, in *GetMagicSpiritConfTmpReq, opts ...grpc.CallOption) (*GetMagicSpiritConfTmpResp, error) {
	out := new(GetMagicSpiritConfTmpResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetMagicSpiritConfTmp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AddMagicSpiritBlacklist(ctx context.Context, in *AddMagicSpiritBlacklistReq, opts ...grpc.CallOption) (*AddMagicSpiritBlacklistResp, error) {
	out := new(AddMagicSpiritBlacklistResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AddMagicSpiritBlacklist", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetMagicSpiritBlacklist(ctx context.Context, in *GetMagicSpiritBlackListReq, opts ...grpc.CallOption) (*GetMagicSpiritBlackListResp, error) {
	out := new(GetMagicSpiritBlackListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetMagicSpiritBlacklist", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) DelMagicSpiritBlacklist(ctx context.Context, in *DelMagicSpiritBlacklistReq, opts ...grpc.CallOption) (*DelMagicSpiritBlacklistResp, error) {
	out := new(DelMagicSpiritBlacklistResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/DelMagicSpiritBlacklist", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) SetCommonConf(ctx context.Context, in *SetCommonConfReq, opts ...grpc.CallOption) (*SetCommonConfResp, error) {
	out := new(SetCommonConfResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/SetCommonConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetCommonConf(ctx context.Context, in *GetCommonConfReq, opts ...grpc.CallOption) (*GetCommonConfResp, error) {
	out := new(GetCommonConfResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetCommonConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetLevelupPresentVersionList(ctx context.Context, in *ItemReq, opts ...grpc.CallOption) (*VersionList, error) {
	out := new(VersionList)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetLevelupPresentVersionList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AddLevelupPresentVersion(ctx context.Context, in *ItemVersionReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AddLevelupPresentVersion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetLevelupParentPresentList(ctx context.Context, in *OffsetTypeReq, opts ...grpc.CallOption) (*EntireParentPresentDataList, error) {
	out := new(EntireParentPresentDataList)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetLevelupParentPresentList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetLevelupChildPresentList(ctx context.Context, in *ItemReq, opts ...grpc.CallOption) (*EntireChildPresentDataList, error) {
	out := new(EntireChildPresentDataList)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetLevelupChildPresentList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AddLevelupParentPresent(ctx context.Context, in *AddLevelupParentPresentReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AddLevelupParentPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) UpdateLevelupParentPresent(ctx context.Context, in *UpdateLevelupParentPresentReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/UpdateLevelupParentPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) DeleteLevelupParentPresent(ctx context.Context, in *ItemReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/DeleteLevelupParentPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AddLevelupChildPresent(ctx context.Context, in *AddLevelupChildPresentReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AddLevelupChildPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) UpdateLevelupChildPresent(ctx context.Context, in *UpdateLevelupChildPresentReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/UpdateLevelupChildPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) DeleteLevelupChildPresent(ctx context.Context, in *ItemReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/DeleteLevelupChildPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetLevelupParentPresent(ctx context.Context, in *ItemReq, opts ...grpc.CallOption) (*EntireParentPresentData, error) {
	out := new(EntireParentPresentData)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetLevelupParentPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetLevelupChildPresent(ctx context.Context, in *ItemReq, opts ...grpc.CallOption) (*EntireChildPresentData, error) {
	out := new(EntireChildPresentData)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetLevelupChildPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) RecordTaxRate(ctx context.Context, in *settlement_bill.RecordTaxRateReq, opts ...grpc.CallOption) (*settlement_bill.RecordTaxRateResp, error) {
	out := new(settlement_bill.RecordTaxRateResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/RecordTaxRate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetGuildTaxRate(ctx context.Context, in *settlement_bill.GetGuildTaxRateReq, opts ...grpc.CallOption) (*settlement_bill.GetGuildTaxRateResp, error) {
	out := new(settlement_bill.GetGuildTaxRateResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetGuildTaxRate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetTaxRateList(ctx context.Context, in *settlement_bill.GetTaxRateListReq, opts ...grpc.CallOption) (*settlement_bill.GetTaxRateListResp, error) {
	out := new(settlement_bill.GetTaxRateListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetTaxRateList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) RecordExtraIncome(ctx context.Context, in *settlement_bill.RecordExtraIncomeReq, opts ...grpc.CallOption) (*settlement_bill.RecordExtraIncomeResp, error) {
	out := new(settlement_bill.RecordExtraIncomeResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/RecordExtraIncome", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetExtraIncomeRecordList(ctx context.Context, in *settlement_bill.GetExtraIncomeRecordListReq, opts ...grpc.CallOption) (*settlement_bill.GetExtraIncomeRecordListResp, error) {
	out := new(settlement_bill.GetExtraIncomeRecordListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetExtraIncomeRecordList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetExtraIncomeDetailDeepCoop(ctx context.Context, in *settlement_bill.GetExtraIncomeDetailReq, opts ...grpc.CallOption) (*settlement_bill.GetExtraIncomeDetailDeepCoopResp, error) {
	out := new(settlement_bill.GetExtraIncomeDetailDeepCoopResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetExtraIncomeDetailDeepCoop", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetExtraIncomeDetailChannelSubsidy(ctx context.Context, in *settlement_bill.GetExtraIncomeDetailReq, opts ...grpc.CallOption) (*settlement_bill.GetExtraIncomeDetailChannelSubsidyResp, error) {
	out := new(settlement_bill.GetExtraIncomeDetailChannelSubsidyResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetExtraIncomeDetailChannelSubsidy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetExtraIncomeDetailNewGuildSubsidy(ctx context.Context, in *settlement_bill.GetExtraIncomeDetailReq, opts ...grpc.CallOption) (*settlement_bill.GetExtraIncomeDetailNewGuildSubsidyResp, error) {
	out := new(settlement_bill.GetExtraIncomeDetailNewGuildSubsidyResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetExtraIncomeDetailNewGuildSubsidy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetExtraIncomeDetailDeduct(ctx context.Context, in *settlement_bill.GetExtraIncomeDetailReq, opts ...grpc.CallOption) (*settlement_bill.GetExtraIncomeDetailDeductResp, error) {
	out := new(settlement_bill.GetExtraIncomeDetailDeductResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetExtraIncomeDetailDeduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AddDarkGiftBonusBuffConf(ctx context.Context, in *dark_gift_bonus.AddBuffConfReq, opts ...grpc.CallOption) (*dark_gift_bonus.AddBuffConfResp, error) {
	out := new(dark_gift_bonus.AddBuffConfResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AddDarkGiftBonusBuffConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) DelDarkGiftBonusBuffConf(ctx context.Context, in *dark_gift_bonus.DelBuffConfReq, opts ...grpc.CallOption) (*dark_gift_bonus.DelBuffConfResp, error) {
	out := new(dark_gift_bonus.DelBuffConfResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/DelDarkGiftBonusBuffConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetDarkGiftBonusBuffConf(ctx context.Context, in *dark_gift_bonus.GetBuffConfReq, opts ...grpc.CallOption) (*dark_gift_bonus.GetBuffConfResp, error) {
	out := new(dark_gift_bonus.GetBuffConfResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetDarkGiftBonusBuffConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) UpdateDarkGiftBonusBuffConf(ctx context.Context, in *dark_gift_bonus.UpdateBuffConfReq, opts ...grpc.CallOption) (*dark_gift_bonus.UpdateBuffConfResp, error) {
	out := new(dark_gift_bonus.UpdateBuffConfResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/UpdateDarkGiftBonusBuffConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AddUserDecorationConfig(ctx context.Context, in *AddUserDecorationConfigReq, opts ...grpc.CallOption) (*AddUserDecorationConfigResp, error) {
	out := new(AddUserDecorationConfigResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AddUserDecorationConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) DelUserDecorationConfig(ctx context.Context, in *DelUserDecorationConfigReq, opts ...grpc.CallOption) (*DelUserDecorationConfigResp, error) {
	out := new(DelUserDecorationConfigResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/DelUserDecorationConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) UpdateUserDecorationConfig(ctx context.Context, in *UpdateUserDecorationConfigReq, opts ...grpc.CallOption) (*UpdateUserDecorationConfigResp, error) {
	out := new(UpdateUserDecorationConfigResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/UpdateUserDecorationConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetChannelEnterSpecialEffectConfig(ctx context.Context, in *EmptyMsg, opts ...grpc.CallOption) (*ChannelEnterSpecialEffectConfig, error) {
	out := new(ChannelEnterSpecialEffectConfig)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetChannelEnterSpecialEffectConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) SetUserDecorationSpecialLevel(ctx context.Context, in *SetUserDecorationSpecialLevelReq, opts ...grpc.CallOption) (*SetUserDecorationSpecialLevelResp, error) {
	out := new(SetUserDecorationSpecialLevelResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/SetUserDecorationSpecialLevel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GrantDecorationToUserV2(ctx context.Context, in *GrantDecorationToUserReq, opts ...grpc.CallOption) (*GrantDecorationToUserResp, error) {
	out := new(GrantDecorationToUserResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GrantDecorationToUserV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) BatchGrantDecorationToUser(ctx context.Context, in *BatchGrantDecorationToUserReq, opts ...grpc.CallOption) (*BatchGrantDecorationToUserResp, error) {
	out := new(BatchGrantDecorationToUserResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/BatchGrantDecorationToUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AddRareRelationship(ctx context.Context, in *RareRelationshipAddReq, opts ...grpc.CallOption) (*RareRelationshipAddResp, error) {
	out := new(RareRelationshipAddResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AddRareRelationship", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) UpdateRareRelationship(ctx context.Context, in *RareRelationshipUpdateReq, opts ...grpc.CallOption) (*RareRelationshipUpdateResp, error) {
	out := new(RareRelationshipUpdateResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/UpdateRareRelationship", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) DelRareRelationship(ctx context.Context, in *RareRelationshipDeleteReq, opts ...grpc.CallOption) (*RareRelationshipDeleteResp, error) {
	out := new(RareRelationshipDeleteResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/DelRareRelationship", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetRareRelationshipList(ctx context.Context, in *RareRelationshipListReq, opts ...grpc.CallOption) (*RareRelationshipListResp, error) {
	out := new(RareRelationshipListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetRareRelationshipList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetRareRelationship(ctx context.Context, in *RareRelationshipGetReq, opts ...grpc.CallOption) (*RareRelationshipGetResp, error) {
	out := new(RareRelationshipGetResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetRareRelationship", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetChannelRareRelationshipBindingList(ctx context.Context, in *ChannelRareRelationshipBindingListReq, opts ...grpc.CallOption) (*ChannelRareRelationshipBindingListResp, error) {
	out := new(ChannelRareRelationshipBindingListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetChannelRareRelationshipBindingList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AddChannelRareRelationshipBinding(ctx context.Context, in *ChannelRareRelationshipBindingAddReq, opts ...grpc.CallOption) (*ChannelRareRelationshipBindingAddResp, error) {
	out := new(ChannelRareRelationshipBindingAddResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AddChannelRareRelationshipBinding", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) UpdateChannelRareRelationshipBinding(ctx context.Context, in *ChannelRareRelationshipBindingUpdateReq, opts ...grpc.CallOption) (*ChannelRareRelationshipBindingUpdateResp, error) {
	out := new(ChannelRareRelationshipBindingUpdateResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/UpdateChannelRareRelationshipBinding", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) DelChannelRareRelationshipBinding(ctx context.Context, in *ChannelRareRelationshipBindingDeleteReq, opts ...grpc.CallOption) (*ChannelRareRelationshipBindingDeleteResp, error) {
	out := new(ChannelRareRelationshipBindingDeleteResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/DelChannelRareRelationshipBinding", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) ListTopicChannel(ctx context.Context, in *ListTopicChannelReq, opts ...grpc.CallOption) (*ListTopicChannelResp, error) {
	out := new(ListTopicChannelResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/ListTopicChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AnchorCheckUploadWhiteList(ctx context.Context, in *WhiteListReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AnchorCheckUploadWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AnchorCheckGetCheckList(ctx context.Context, in *AnchorCheckGetCheckListReq, opts ...grpc.CallOption) (*AnchorCheckGetCheckListResp, error) {
	out := new(AnchorCheckGetCheckListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AnchorCheckGetCheckList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AnchorCheckSetCheckData(ctx context.Context, in *AnchorCheckSetCheckDataReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AnchorCheckSetCheckData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) ExportWhiteExpireList(ctx context.Context, in *ExportWhiteExpireListReq, opts ...grpc.CallOption) (*ExportWhiteExpireListResp, error) {
	out := new(ExportWhiteExpireListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/ExportWhiteExpireList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AddStickPiaRoom(ctx context.Context, in *AddStickPiaRoomReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AddStickPiaRoom", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetStickPiaRoom(ctx context.Context, in *GetStickPiaRoomReq, opts ...grpc.CallOption) (*GetStickPiaRoomResp, error) {
	out := new(GetStickPiaRoomResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetStickPiaRoom", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) UpdateStickPiaRoom(ctx context.Context, in *UpdateStickPiaRoomReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/UpdateStickPiaRoom", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AddPiaRoomAd(ctx context.Context, in *AddPiaRoomAdReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AddPiaRoomAd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetPiaRoomAd(ctx context.Context, in *GetPiaRoomAdReq, opts ...grpc.CallOption) (*GetPiaRoomAdResp, error) {
	out := new(GetPiaRoomAdResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetPiaRoomAd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) UpdatePiaRoomAd(ctx context.Context, in *UpdatePiaRoomAdReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/UpdatePiaRoomAd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) DelPiaRoomRecord(ctx context.Context, in *DelPiaRoomRecordReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/DelPiaRoomRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GrantPiaRoomPermission(ctx context.Context, in *GrantPiaRoomPermissionReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GrantPiaRoomPermission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetPiaRoomPermission(ctx context.Context, in *GetPiaRoomPermissionReq, opts ...grpc.CallOption) (*GetPiaRoomPermissionResp, error) {
	out := new(GetPiaRoomPermissionResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetPiaRoomPermission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) CancelPiaRoomPermission(ctx context.Context, in *CancelPiaRoomPermissionReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/CancelPiaRoomPermission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) BatchGetGuildInfoByDisplayId(ctx context.Context, in *BatchGetGuildInfoByDisplayIdReq, opts ...grpc.CallOption) (*BatchGetGuildInfoByDisplayIdResp, error) {
	out := new(BatchGetGuildInfoByDisplayIdResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/BatchGetGuildInfoByDisplayId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) BatchGrantPiaRoomPermission(ctx context.Context, in *BatchGrantPiaRoomPermissionReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/BatchGrantPiaRoomPermission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) BatchCancelPiaRoomPermission(ctx context.Context, in *BatchCancelPiaRoomPermissionReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/BatchCancelPiaRoomPermission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetTimeOverlapped(ctx context.Context, in *GetTimeOverlappedReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetTimeOverlapped", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AddStickDrama(ctx context.Context, in *AddStickDramaReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AddStickDrama", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) UpdateStickDrama(ctx context.Context, in *UpdateStickDramaReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/UpdateStickDrama", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) DeleteStickDrama(ctx context.Context, in *DeleteStickDramaReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/DeleteStickDrama", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) SearchStickDrama(ctx context.Context, in *SearchStickDramaReq, opts ...grpc.CallOption) (*SearchStickDramaResp, error) {
	out := new(SearchStickDramaResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/SearchStickDrama", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetDramaInfoByDramaId(ctx context.Context, in *GetDramaInfoByDramaIdReq, opts ...grpc.CallOption) (*GetDramaInfoByDramaIdResp, error) {
	out := new(GetDramaInfoByDramaIdResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetDramaInfoByDramaId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetStickDramaTags(ctx context.Context, in *GetStickDramaTagsReq, opts ...grpc.CallOption) (*GetStickDramaTagsResp, error) {
	out := new(GetStickDramaTagsResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetStickDramaTags", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) CheckStickDramaTime(ctx context.Context, in *CheckStickDramaTimeReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/CheckStickDramaTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetFlowCardLimitConfList(ctx context.Context, in *GetFlowCardLimitConfListReq, opts ...grpc.CallOption) (*GetFlowCardLimitConfListResp, error) {
	out := new(GetFlowCardLimitConfListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetFlowCardLimitConfList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AddFlowCardLimitConf(ctx context.Context, in *AddFlowCardLimitConfReq, opts ...grpc.CallOption) (*AddFlowCardLimitConfResp, error) {
	out := new(AddFlowCardLimitConfResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AddFlowCardLimitConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) UpdateFlowCardLimitConf(ctx context.Context, in *UpdateFlowCardLimitConfReq, opts ...grpc.CallOption) (*UpdateFlowCardLimitConfResp, error) {
	out := new(UpdateFlowCardLimitConfResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/UpdateFlowCardLimitConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) DelFlowCardLimitConf(ctx context.Context, in *DelFlowCardLimitConfReq, opts ...grpc.CallOption) (*DelFlowCardLimitConfResp, error) {
	out := new(DelFlowCardLimitConfResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/DelFlowCardLimitConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetGrantFlowCardList(ctx context.Context, in *GetGrantFlowCardListReq, opts ...grpc.CallOption) (*GetGrantFlowCardListResp, error) {
	out := new(GetGrantFlowCardListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetGrantFlowCardList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GrantFlowCard(ctx context.Context, in *GrantFlowCardReq, opts ...grpc.CallOption) (*GrantFlowCardResp, error) {
	out := new(GrantFlowCardResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GrantFlowCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) ReclaimGrantedFlowCard(ctx context.Context, in *ReclaimGrantedFlowCardReq, opts ...grpc.CallOption) (*ReclaimGrantedFlowCardResp, error) {
	out := new(ReclaimGrantedFlowCardResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/ReclaimGrantedFlowCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) BanGrantedFlowCard(ctx context.Context, in *BanGrantedFlowCardReq, opts ...grpc.CallOption) (*BanGrantedFlowCardResp, error) {
	out := new(BanGrantedFlowCardResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/BanGrantedFlowCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetPrepareChannelList(ctx context.Context, in *GetPrepareChannelListReq, opts ...grpc.CallOption) (*GetPrepareChannelListResp, error) {
	out := new(GetPrepareChannelListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetPrepareChannelList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) SetPrepareChannel(ctx context.Context, in *SetPrepareChannelReq, opts ...grpc.CallOption) (*SetPrepareChannelResp, error) {
	out := new(SetPrepareChannelResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/SetPrepareChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) DelPrepareChannel(ctx context.Context, in *DelPrepareChannelReq, opts ...grpc.CallOption) (*DelPrepareChannelResp, error) {
	out := new(DelPrepareChannelResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/DelPrepareChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetGuildChannelList(ctx context.Context, in *GetGuildChannelListReq, opts ...grpc.CallOption) (*GetGuildChannelListResp, error) {
	out := new(GetGuildChannelListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetGuildChannelList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetChannelPrepareList(ctx context.Context, in *GetChannelPrepareListReq, opts ...grpc.CallOption) (*GetChannelPrepareListResp, error) {
	out := new(GetChannelPrepareListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetChannelPrepareList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetPrepareBackupList(ctx context.Context, in *GetPrepareBackupListReq, opts ...grpc.CallOption) (*GetPrepareBackupListResp, error) {
	out := new(GetPrepareBackupListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetPrepareBackupList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetPrepareOperRecordList(ctx context.Context, in *GetPrepareOperRecordListReq, opts ...grpc.CallOption) (*GetPrepareOperRecordListResp, error) {
	out := new(GetPrepareOperRecordListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetPrepareOperRecordList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetDisplaySceneInfoList(ctx context.Context, in *GetDisplaySceneInfoListReq, opts ...grpc.CallOption) (*GetDisplaySceneInfoListResp, error) {
	out := new(GetDisplaySceneInfoListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetDisplaySceneInfoList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AddDisplaySceneInfo(ctx context.Context, in *AddDisplaySceneInfoReq, opts ...grpc.CallOption) (*AddDisplaySceneInfoResp, error) {
	out := new(AddDisplaySceneInfoResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AddDisplaySceneInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) DelDisplaySceneInfo(ctx context.Context, in *DelDisplaySceneInfoReq, opts ...grpc.CallOption) (*DelDisplaySceneInfoResp, error) {
	out := new(DelDisplaySceneInfoResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/DelDisplaySceneInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) BatchGetChannelMode(ctx context.Context, in *BatchGetChannelModeReq, opts ...grpc.CallOption) (*BatchGetChannelModeResp, error) {
	out := new(BatchGetChannelModeResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/BatchGetChannelMode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) BatchOperateChannelMode(ctx context.Context, in *BatchOperateChannelModeReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/BatchOperateChannelMode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetKnightScoreList(ctx context.Context, in *GetKnightScoreListReq, opts ...grpc.CallOption) (*GetKnightScoreListResp, error) {
	out := new(GetKnightScoreListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetKnightScoreList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) SearchPresent(ctx context.Context, in *SearchPresentReq, opts ...grpc.CallOption) (*SearchPresentResp, error) {
	out := new(SearchPresentResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/SearchPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetPresentFloatLayer(ctx context.Context, in *GetPresentFloatLayerReq, opts ...grpc.CallOption) (*GetPresentFloatLayerResp, error) {
	out := new(GetPresentFloatLayerResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetPresentFloatLayer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AddPresentFloatLayer(ctx context.Context, in *AddPresentFloatLayerReq, opts ...grpc.CallOption) (*AddPresentFloatLayerResp, error) {
	out := new(AddPresentFloatLayerResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AddPresentFloatLayer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) UpdatePresentFloatLayer(ctx context.Context, in *UpdatePresentFloatLayerReq, opts ...grpc.CallOption) (*UpdatePresentFloatLayerResp, error) {
	out := new(UpdatePresentFloatLayerResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/UpdatePresentFloatLayer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) DelPresentFloatLayer(ctx context.Context, in *DelPresentFloatLayerReq, opts ...grpc.CallOption) (*DelPresentFloatLayerResp, error) {
	out := new(DelPresentFloatLayerResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/DelPresentFloatLayer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetFlashEffectConfig(ctx context.Context, in *GetFlashEffectConfigReq, opts ...grpc.CallOption) (*GetFlashEffectConfigResp, error) {
	out := new(GetFlashEffectConfigResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetFlashEffectConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AddFlashEffectConfig(ctx context.Context, in *AddFlashEffectConfigReq, opts ...grpc.CallOption) (*AddFlashEffectConfigResp, error) {
	out := new(AddFlashEffectConfigResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AddFlashEffectConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) UpdateFlashEffectConfig(ctx context.Context, in *UpdateFlashEffectConfigReq, opts ...grpc.CallOption) (*UpdateFlashEffectConfigResp, error) {
	out := new(UpdateFlashEffectConfigResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/UpdateFlashEffectConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) DelFlashEffectConfig(ctx context.Context, in *DelFlashEffectConfigReq, opts ...grpc.CallOption) (*DelFlashEffectConfigResp, error) {
	out := new(DelFlashEffectConfigResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/DelFlashEffectConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetPresentFlashEffect(ctx context.Context, in *GetPresentFlashEffectReq, opts ...grpc.CallOption) (*GetPresentFlashEffectResp, error) {
	out := new(GetPresentFlashEffectResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetPresentFlashEffect", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) BoundPresentFlashEffect(ctx context.Context, in *BoundPresentFlashEffectReq, opts ...grpc.CallOption) (*BoundPresentFlashEffectResp, error) {
	out := new(BoundPresentFlashEffectResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/BoundPresentFlashEffect", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) UpsertDecoration(ctx context.Context, in *UpsertDecorationReq, opts ...grpc.CallOption) (*UpsertDecorationResp, error) {
	out := new(UpsertDecorationResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/UpsertDecoration", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) Decorations(ctx context.Context, in *DecorationsReq, opts ...grpc.CallOption) (*DecorationsResp, error) {
	out := new(DecorationsResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/Decorations", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) InsertDecoration(ctx context.Context, in *InsertDecorationReq, opts ...grpc.CallOption) (*InsertDecorationResp, error) {
	out := new(InsertDecorationResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/InsertDecoration", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) DelDecoration(ctx context.Context, in *DelDecorationReq, opts ...grpc.CallOption) (*DelDecorationResp, error) {
	out := new(DelDecorationResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/DelDecoration", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) UserDecorations(ctx context.Context, in *UserDecorationsReq, opts ...grpc.CallOption) (*UserDecorationsResp, error) {
	out := new(UserDecorationsResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/UserDecorations", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) BatchUserDecorations(ctx context.Context, in *BatchUpsertDecorationReq, opts ...grpc.CallOption) (*BatchUpsertDecorationResp, error) {
	out := new(BatchUpsertDecorationResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/BatchUserDecorations", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) UpdateAlias(ctx context.Context, in *UpdateAliasReq, opts ...grpc.CallOption) (*UpdateAliasResp, error) {
	out := new(UpdateAliasResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/UpdateAlias", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) ChangeDisplayID(ctx context.Context, in *ChangeDisplayIDReq, opts ...grpc.CallOption) (*ChangeDisplayIDResp, error) {
	out := new(ChangeDisplayIDResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/ChangeDisplayID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) ListAlias(ctx context.Context, in *ListAliasReq, opts ...grpc.CallOption) (*ListAliasResp, error) {
	out := new(ListAliasResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/ListAlias", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) CreateUserByAlias(ctx context.Context, in *CreateUserByAliasReq, opts ...grpc.CallOption) (*CreateUserByAliasResp, error) {
	out := new(CreateUserByAliasResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/CreateUserByAlias", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AddCustomizedPresentConfig(ctx context.Context, in *AddCustomizedPresentConfigReq, opts ...grpc.CallOption) (*AddCustomizedPresentConfigResp, error) {
	out := new(AddCustomizedPresentConfigResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AddCustomizedPresentConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) UpdateCustomizedPresentConfig(ctx context.Context, in *UpdateCustomizedPresentConfigReq, opts ...grpc.CallOption) (*UpdateCustomizedPresentConfigResp, error) {
	out := new(UpdateCustomizedPresentConfigResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/UpdateCustomizedPresentConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetAllCustomizedPresentConfig(ctx context.Context, in *GetAllCustomizedPresentConfigReq, opts ...grpc.CallOption) (*GetAllCustomizedPresentConfigResp, error) {
	out := new(GetAllCustomizedPresentConfigResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetAllCustomizedPresentConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) DelCustomizedPresentConfig(ctx context.Context, in *DelCustomizedPresentConfigReq, opts ...grpc.CallOption) (*DelCustomizedPresentConfigResp, error) {
	out := new(DelCustomizedPresentConfigResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/DelCustomizedPresentConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetUserViolationsInfo(ctx context.Context, in *GetUserViolationsInfoReq, opts ...grpc.CallOption) (*GetUserViolationsInfoResp, error) {
	out := new(GetUserViolationsInfoResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetUserViolationsInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) TakeHoldChannelMicByForce(ctx context.Context, in *ChannelMicForceTakeHoldReq, opts ...grpc.CallOption) (*ChannelMicForceTakeHoldResp, error) {
	out := new(ChannelMicForceTakeHoldResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/TakeHoldChannelMicByForce", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) SetChannelMicStatus(ctx context.Context, in *SetChannelMicStatusReq, opts ...grpc.CallOption) (*SetChannelMicStatusResp, error) {
	out := new(SetChannelMicStatusResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/SetChannelMicStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) BatchGetChannelGameplayPerm(ctx context.Context, in *BatchGetChannelGameplayPermReq, opts ...grpc.CallOption) (*BatchGetChannelGameplayPermResp, error) {
	out := new(BatchGetChannelGameplayPermResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/BatchGetChannelGameplayPerm", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) BatchOperateChannelGameplayPerm(ctx context.Context, in *BatchOperateChannelGameplayPermReq, opts ...grpc.CallOption) (*BatchOperateChannelGameplayPermResp, error) {
	out := new(BatchOperateChannelGameplayPermResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/BatchOperateChannelGameplayPerm", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AddTicketPresentConfig(ctx context.Context, in *AddTicketPresentConfigReq, opts ...grpc.CallOption) (*AddTicketPresentConfigResp, error) {
	out := new(AddTicketPresentConfigResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AddTicketPresentConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) UpdateTicketPresentConfig(ctx context.Context, in *UpdateTicketPresentConfigReq, opts ...grpc.CallOption) (*UpdateTicketPresentConfigResp, error) {
	out := new(UpdateTicketPresentConfigResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/UpdateTicketPresentConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetAllTicketPresentConfig(ctx context.Context, in *GetAllTicketPresentConfigReq, opts ...grpc.CallOption) (*GetAllTicketPresentConfigResp, error) {
	out := new(GetAllTicketPresentConfigResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetAllTicketPresentConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) DelTicketPresentConfig(ctx context.Context, in *DelTicketPresentConfigReq, opts ...grpc.CallOption) (*DelTicketPresentConfigResp, error) {
	out := new(DelTicketPresentConfigResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/DelTicketPresentConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AddUserUnregWhite(ctx context.Context, in *AddUserUnregWhiteReq, opts ...grpc.CallOption) (*AddUserUnregWhiteResp, error) {
	out := new(AddUserUnregWhiteResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AddUserUnregWhite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetCurUnregUserInfo(ctx context.Context, in *GetCurUnregUserInfoReq, opts ...grpc.CallOption) (*GetCurUnregUserInfoResp, error) {
	out := new(GetCurUnregUserInfoResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetCurUnregUserInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetUnregWhiteUserInfo(ctx context.Context, in *GetUnregWhiteUserInfoReq, opts ...grpc.CallOption) (*GetUnregWhiteUserInfoResp, error) {
	out := new(GetUnregWhiteUserInfoResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetUnregWhiteUserInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) AddTreasurePrivilege(ctx context.Context, in *AddTreasurePrivilegeReq, opts ...grpc.CallOption) (*AddTreasurePrivilegeResp, error) {
	out := new(AddTreasurePrivilegeResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/AddTreasurePrivilege", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetTreasurePrivilegeList(ctx context.Context, in *GetTreasurePrivilegeListReq, opts ...grpc.CallOption) (*GetTreasurePrivilegeListResp, error) {
	out := new(GetTreasurePrivilegeListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetTreasurePrivilegeList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) DelTreasurePrivilegeList(ctx context.Context, in *DelTreasurePrivilegeListReq, opts ...grpc.CallOption) (*DelTreasurePrivilegeListResp, error) {
	out := new(DelTreasurePrivilegeListResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/DelTreasurePrivilegeList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) CheckTreasurePrivilege(ctx context.Context, in *CheckTreasurePrivilegeReq, opts ...grpc.CallOption) (*CheckTreasurePrivilegeResp, error) {
	out := new(CheckTreasurePrivilegeResp)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/CheckTreasurePrivilege", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) BatchAddMissingItemsConfig(ctx context.Context, in *BatchAddMissingItemsConfigRequest, opts ...grpc.CallOption) (*BatchAddMissingItemsConfigResponse, error) {
	out := new(BatchAddMissingItemsConfigResponse)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/BatchAddMissingItemsConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) GetAllMissingItemsConfig(ctx context.Context, in *GetAllMissingItemsConfigRequest, opts ...grpc.CallOption) (*GetAllMissingItemsConfigResponse, error) {
	out := new(GetAllMissingItemsConfigResponse)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/GetAllMissingItemsConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) UpdateMissingItemsConfig(ctx context.Context, in *UpdateMissingItemsConfigRequest, opts ...grpc.CallOption) (*UpdateMissingItemsConfigResponse, error) {
	out := new(UpdateMissingItemsConfigResponse)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/UpdateMissingItemsConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apicenterGoClient) DeleteMissingItemsConfig(ctx context.Context, in *DeleteMissingItemsConfigRequest, opts ...grpc.CallOption) (*DeleteMissingItemsConfigResponse, error) {
	out := new(DeleteMissingItemsConfigResponse)
	err := c.cc.Invoke(ctx, "/apicentergo.ApicenterGo/DeleteMissingItemsConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ApicenterGoServer is the server API for ApicenterGo service.
type ApicenterGoServer interface {
	// 用户礼物
	GetPresentConfigById(context.Context, *GetPresentConfigByIdReq) (*GetPresentConfigByIdResp, error)
	GetPresentConfigList(context.Context, *GetPresentConfigListReq) (*GetPresentConfigListResp, error)
	AddPresentConfig(context.Context, *AddPresentConfigReq) (*AddPresentConfigResp, error)
	DelPresentConfig(context.Context, *DelPresentConfigReq) (*DelPresentConfigResp, error)
	UpdatePresentConfig(context.Context, *UpdatePresentConfigReq) (*UpdatePresentConfigResp, error)
	GetUserPresentSend(context.Context, *GetUserPresentSendReq) (*GetUserPresentSendResp, error)
	GetUserPresentReceive(context.Context, *GetUserPresentReceiveReq) (*GetUserPresentReceiveResp, error)
	GetAllFellowPresent(context.Context, *GetAllFellowPresentReq) (*GetAllFellowPresentResp, error)
	// 背包包裹
	AddPackageCfg(context.Context, *AddPackageCfgReq) (*AddPackageCfgResp, error)
	DelPackageCfg(context.Context, *DelPackageCfgReq) (*DelPackageCfgResp, error)
	GetPackageCfg(context.Context, *GetPackageCfgReq) (*GetPackageCfgResp, error)
	GetPackageCfgByIds(context.Context, *GetPackageCfgByIdsReq) (*GetPackageCfgByIdsResp, error)
	AddPackageItemCfg(context.Context, *AddPackageItemCfgReq) (*AddPackageItemCfgResp, error)
	ModPackageItemCfg(context.Context, *ModPackageItemCfgReq) (*ModPackageItemCfgResp, error)
	DelPackageItemCfg(context.Context, *DelPackageItemCfgReq) (*DelPackageItemCfgResp, error)
	GetPackageItemCfg(context.Context, *GetPackageItemCfgReq) (*GetPackageItemCfgResp, error)
	GiveUserPackage(context.Context, *GiveUserPackageReq) (*GiveUserPackageResp, error)
	AddFuncCardCfg(context.Context, *AddFuncCardCfgReq) (*AddFuncCardCfgResp, error)
	DelFuncCardCfg(context.Context, *DelFuncCardCfgReq) (*DelFuncCardCfgResp, error)
	GetFuncCardCfg(context.Context, *GetFuncCardCfgReq) (*GetFuncCardCfgResp, error)
	GetUserBackpack(context.Context, *GetUserBackpackReq) (*GetUserBackpackResp, error)
	GetUserPackageReceive(context.Context, *GetUserPackageReceiveReq) (*GetUserPackageReceiveResp, error)
	// 风控配置相关
	AddBusiness(context.Context, *AddBusinessReq) (*AddBusinessResp, error)
	GetAllBusiness(context.Context, *GetAllBusinessReq) (*GetAllBusinessResp, error)
	GetBusinessByIds(context.Context, *GetBusinessByIdsReq) (*GetBusinessByIdsResp, error)
	AddBusinessRiskControlConf(context.Context, *AddBusinessRiskControlConfReq) (*AddBusinessRiskControlConfResp, error)
	ModBusinessRiskControlConf(context.Context, *ModBusinessRiskControlConfReq) (*ModBusinessRiskControlConfResp, error)
	GetBusinessRiskControlConf(context.Context, *GetBusinessRiskControlConfReq) (*GetBusinessRiskControlConfResp, error)
	// 用户账号相关
	GetUserInfo(context.Context, *GetUserInfoReq) (*GetUserInfoResp, error)
	BatchGetUserInfo(context.Context, *BatchGetUserInfoReq) (*BatchGetUserInfoResp, error)
	GetUserWithExtraInfo(context.Context, *GetUserWithExtraInfoReq) (*GetUserWithExtraInfoResp, error)
	BatGetUserByTTid(context.Context, *BatGetUserByTTidReq) (*BatGetUserByTTidResp, error)
	// 合成相关
	GetUserConversion(context.Context, *GetUserConversionReq) (*GetUserConversionResp, error)
	BatchDeductUserItem(context.Context, *BatchDeductUserItemReq) (*BatchDeductUserItemResp, error)
	// 公会相关
	GetGuildById(context.Context, *GetGuildByIdReq) (*GetGuildByIdResp, error)
	BatchGetGuild(context.Context, *BatchGetGuildReq) (*BatchGetGuildResp, error)
	BatchGetGuildV2(context.Context, *BatchGetGuildReq) (*BatchGetGuildResp, error)
	ModifyGuildName(context.Context, *ModifyGuildNameReq) (*ModifyGuildNameResp, error)
	SetGuildShortId(context.Context, *SetGuildShortIdReq) (*SetGuildShortIdResp, error)
	UnsetGuildShortId(context.Context, *UnsetGuildShortIdReq) (*UnsetGuildShortIdResp, error)
	ModifyGuildGameLimit(context.Context, *ModifyGuildGameLimitReq) (*ModifyGuildGameLimitResp, error)
	// 签约模块相关接口
	GetApplySignRecord(context.Context, *GetApplySignRecordReq) (*GetApplySignRecordResp, error)
	OfficialHandleApplySign(context.Context, *OfficialHandleApplySignReq) (*OfficialHandleApplySignResp, error)
	BatchGetContractInfo(context.Context, *BatchGetContractInfoReq) (*BatchGetContractInfoResp, error)
	ReclaimAnchorIdentity(context.Context, *ReclaimAnchorIdentityReq) (*ReclaimAnchorIdentityResp, error)
	BatchGetApplyBlacklist(context.Context, *BatchGetApplyBlacklistReq) (*BatchGetApplyBlacklistResp, error)
	HandleApplyBlackInfo(context.Context, *HandleApplyBlackInfoReq) (*HandleApplyBlackInfoResp, error)
	GetLiveAnchorExamine(context.Context, *GetLiveAnchorExamineReq) (*GetLiveAnchorExamineResp, error)
	GetAllLiveAnchorExamine(context.Context, *GetAllLiveAnchorExamineReq) (*GetAllLiveAnchorExamineResp, error)
	UpdateLiveAnchorExamineStatus(context.Context, *UpdateLiveAnchorExamineStatusReq) (*UpdateLiveAnchorExamineStatusResp, error)
	UpdateLiveAnchorExamineTime(context.Context, *UpdateLiveAnchorExamineTimeReq) (*UpdateLiveAnchorExamineTimeResp, error)
	GetAnchorIdentityLog(context.Context, *GetAnchorIdentityLogReq) (*GetAnchorIdentityLogResp, error)
	GetPractitionerMonthActiveInfo(context.Context, *GetPractitionerMonthActiveInfoReq) (*GetPractitionerMonthActiveInfoResp, error)
	// 用户相关
	AwardTBeanToUser(context.Context, *AwardTBeanToUserReq) (*AwardTBeanToUserResp, error)
	AwardDiamondToUser(context.Context, *AwardDiamondToUserReq) (*AwardDiamondToUserResp, error)
	GetAwardDiamondRecord(context.Context, *GetAwardDiamondRecordReq) (*GetAwardDiamondRecordResp, error)
	AwardExpToUser(context.Context, *AwardExpToUserReq) (*AwardExpToUserResp, error)
	GetAwardExpRecord(context.Context, *GetAwardExpRecordReq) (*GetAwardExpRecordResp, error)
	GetUserLoginInfo(context.Context, *GetUserLoginInfoReq) (*GetUserLoginInfoResp, error)
	GetBannedRecord(context.Context, *GetBannedRecordReq) (*GetBannedRecordResp, error)
	GetBannedOperator(context.Context, *GetBannedOperatorReq) (*GetBannedOperatorResp, error)
	GetBannedAppealRecord(context.Context, *GetBannedAppealRecordReq) (*GetBannedAppealRecordResp, error)
	UpdateBannedAppealRecord(context.Context, *UpdateBannedAppealRecordReq) (*UpdateBannedAppealRecordResp, error)
	GetBannedCheckRecord(context.Context, *GetBannedCheckRecordReq) (*GetBannedCheckRecordResp, error)
	UpdateBannedCheckRecord(context.Context, *UpdateBannedCheckRecordReq) (*UpdateBannedCheckRecordResp, error)
	GetBannedCheckOperator(context.Context, *GetBannedCheckOperatorReq) (*GetBannedCheckOperatorResp, error)
	GetUserLoginDevice(context.Context, *GetUserLoginDeviceReq) (*GetUserLoginDeviceResp, error)
	GetUserLoginWithDevice(context.Context, *GetUserLoginWithDeviceReq) (*GetUserLoginWithDeviceResp, error)
	BanUser(context.Context, *BanUserReq) (*BanUserResp, error)
	RecoverUser(context.Context, *RecoverUserReq) (*RecoverUserResp, error)
	BatchRecoverUser(context.Context, *BatchRecoverUserReq) (*BatchRecoverUserResp, error)
	GetDeviceLastLoginInfo(context.Context, *GetDeviceLastLoginInfoReq) (*GetDeviceLastLoginInfoResp, error)
	GetBanLog(context.Context, *GetBanLogReq) (*GetBanLogResp, error)
	GetUserBanStatus(context.Context, *GetUserBanStatusReq) (*GetUserBanStatusResp, error)
	BatchBanUserWithDevice(context.Context, *BatchBanUserWithDeviceReq) (*BatchBanUserWithDeviceResp, error)
	CanRecoverDevice(context.Context, *CanRecoverDeviceReq) (*CanRecoverDeviceResp, error)
	GetUserInviteHistory(context.Context, *GetUserInviteHistoryReq) (*GetUserInviteHistoryResp, error)
	ResetUserPassword(context.Context, *ResetUserPasswordReq) (*ResetUserPasswordResp, error)
	UnbindUserPhone(context.Context, *UnbindUserPhoneReq) (*UnbindUserPhoneResp, error)
	ClearSecurityQuestion(context.Context, *ClearSecurityQuestionReq) (*ClearSecurityQuestionResp, error)
	DetachThirdpart(context.Context, *DetachThirdpartReq) (*DetachThirdpartResp, error)
	BatchGetUserOfficialCert(context.Context, *BatchGetUserOfficialCertReq) (*BatchGetUserOfficialCertResp, error)
	GetUserExp(context.Context, *GetUserExpReq) (*GetUserExpResp, error)
	GetUserControlInfo(context.Context, *GetUserControlInfoReq) (*GetUserControlInfoResp, error)
	BatRemoveUserControlled(context.Context, *BatRemoveUserControlledReq) (*BatRemoveUserControlledResp, error)
	GetTheSameRealNameUserList(context.Context, *GetTheSameRealNameUserListReq) (*GetTheSameRealNameUserListResp, error)
	GetUserRealNameAuthInfoV2(context.Context, *GetUserRealNameAuthInfoV2Req) (*GetUserRealNameAuthInfoV2Resp, error)
	BatchGetUserRealNameAuthInfo(context.Context, *BatchGetUserRealNameAuthInfoReq) (*BatchGetUserRealNameAuthInfoResp, error)
	// 房间相关
	BatchGetChannelSimpleInfo(context.Context, *BatchGetChannelSimpleInfoReq) (*BatchGetChannelSimpleInfoResp, error)
	GetAllReportHistoryList(context.Context, *GetAllReportHistoryListReq) (*GetAllReportHistoryListResp, error)
	GetChannelReportStat(context.Context, *GetChannelReportStatReq) (*GetChannelReportStatResp, error)
	GetChannelReportProcList(context.Context, *GetChannelReportProcListReq) (*GetChannelReportProcListResp, error)
	SanctionChannel(context.Context, *SanctionChannelReq) (*SanctionChannelResp, error)
	GetBannedChannelList(context.Context, *GetBannedChannelListReq) (*GetBannedChannelListResp, error)
	AddChannelWhiteListInfo(context.Context, *AddChannelWhiteListInfoReq) (*AddChannelWhiteListInfoResp, error)
	DelChannelWhiteListInfo(context.Context, *DelChannelWhiteListInfoReq) (*DelChannelWhiteListInfoResp, error)
	GetChannelWhiteList(context.Context, *GetChannelWhiteListReq) (*GetChannelWhiteListResp, error)
	GetChannelSimpleInfo(context.Context, *GetChannelSimpleInfoReq) (*GetChannelSimpleInfoResp, error)
	BatchGetChannelTag(context.Context, *BatchGetChannelTagReq) (*BatchGetChannelTagResp, error)
	PushMsgToChannel(context.Context, *PushMsgToChannelReq) (*PushMsgToChannelResp, error)
	// 通用接口
	PushMsgToUser(context.Context, *PushMsgToUserReq) (*PushMsgToUserResp, error)
	PushActBreakingNews(context.Context, *PushActBreakingNewsReq) (*PushActBreakingNewsResp, error)
	// 推送excel的数据接口
	PushFromExcel(context.Context, *PushFromExcelReq) (*PushFromExcelResp, error)
	// 滚刀推送
	HobPush(context.Context, *HobPushReq) (*HobPushResp, error)
	GetHobPushRecord(context.Context, *GetHobPushRecordReq) (*GetHobPushRecordResp, error)
	// 房间麦位模式
	GetChannelMicMode(context.Context, *GetChannelMicModeReq) (*GetChannelMicModeResp, error)
	// 房间背景
	AddChannelBackgroundConf(context.Context, *AddChannelBackgroundConfReq) (*AddChannelBackgroundConfResp, error)
	DelChannelBackgroundConf(context.Context, *DelChannelBackgroundConfReq) (*DelChannelBackgroundConfResp, error)
	GetChannelBackgroundConf(context.Context, *GetChannelBackgroundConfReq) (*GetChannelBackgroundConfResp, error)
	// 添加背景配置V2
	AddChannelBackgroundConfV2(context.Context, *AddChannelBackgroundConfReq) (*AddChannelBackgroundConfResp, error)
	// 添加背景配置V2
	UpdateChannelBackgroundConfV2(context.Context, *UpdateChannelBackgroundConfReq) (*UpdateChannelBackgroundConfResp, error)
	// 删除背景配置V2
	DelChannelBackgroundConfV2(context.Context, *DelChannelBackgroundConfReq) (*DelChannelBackgroundConfResp, error)
	// 获取背景配置V2
	GetChannelBackgroundConfV2(context.Context, *GetChannelBackgroundConfV2Req) (*GetChannelBackgroundConfV2Resp, error)
	// 批量下发背景
	BatchGiveChannelBg(context.Context, *BatchGiveChannelBgReq) (*BatchGiveChannelBgResp, error)
	// 更新下发的背景信息
	UpdateGivenChannelBg(context.Context, *UpdateGivenChannelBgReq) (*UpdateGivenChannelBgResp, error)
	// 删除下发的背景信息
	DeleteGivenChannelBg(context.Context, *DeleteGivenChannelBgReq) (*DeleteGivenChannelBgResp, error)
	// 查询下发的背景信息
	ListGivenChannelBg(context.Context, *ListGivenChannelBgReq) (*ListGivenChannelBgResp, error)
	// 搜索白名单后台
	SwitchSearchWhiteList(context.Context, *SwitchSearchWhiteListReq) (*SwitchSearchWhiteListResp, error)
	GetSearchWhiteListStatus(context.Context, *GetSearchWhiteListStatusReq) (*GetSearchWhiteListStatusResp, error)
	BatchAddSearchWhiteList(context.Context, *BatchAddSearchWhiteListReq) (*BatchAddSearchWhiteListResp, error)
	BatchDelSearchWhiteList(context.Context, *BatchDelSearchWhiteListReq) (*BatchDelSearchWhiteListResp, error)
	GetSearchWhiteList(context.Context, *GetSearchWhiteListReq) (*GetSearchWhiteListResp, error)
	ExportHotWord(context.Context, *ExportHotWordReq) (*ExportHotWordResp, error)
	// 语音直播相关
	GetAnchorDailyRecordWithDateList(context.Context, *GetAnchorDailyRecordWithDateListReq) (*GetAnchorDailyRecordWithDateListResp, error)
	AddAppointPkInfo(context.Context, *AddAppointPkInfoReq) (*AddAppointPkInfoResp, error)
	UpdateAppointPkInfo(context.Context, *UpdateAppointPkInfoReq) (*UpdateAppointPkInfoResp, error)
	DelAppointPkInfo(context.Context, *DelAppointPkInfoReq) (*DelAppointPkInfoResp, error)
	GetAppointPkInfoList(context.Context, *GetAppointPkInfoListReq) (*GetAppointPkInfoListResp, error)
	BatchAddAppointPkInfo(context.Context, *BatchAddAppointPkInfoReq) (*BatchAddAppointPkInfoResp, error)
	AddPlateConfig(context.Context, *AddPlateConfigReq) (*AddPlateConfigResp, error)
	UpdatePlateConfig(context.Context, *UpdatePlateConfigReq) (*UpdatePlateConfigResp, error)
	DelPlateConfig(context.Context, *DelPlateConfigReq) (*DelPlateConfigResp, error)
	GetPlateConfigList(context.Context, *GetPlateConfigListReq) (*GetPlateConfigListResp, error)
	GrantAnchorPlate(context.Context, *GrantAnchorPlateReq) (*GrantAnchorPlateResp, error)
	UpdateGrantedPlateInfo(context.Context, *UpdateGrantedPlateInfoReq) (*UpdateGrantedPlateInfoResp, error)
	DelGrantedPlateInfo(context.Context, *DelGrantedPlateInfoReq) (*DelGrantedPlateInfoResp, error)
	GetGrantedPlateList(context.Context, *GetGrantedPlateListReq) (*GetGrantedPlateListResp, error)
	GetPlateConfigById(context.Context, *GetPlateConfigByIdReq) (*GetPlateConfigByIdResp, error)
	WearAnchorPlate(context.Context, *WearAnchorPlateReq) (*WearAnchorPlateResp, error)
	BatGrantAnchorPlate(context.Context, *BatGrantAnchorPlateReq) (*BatGrantAnchorPlateResp, error)
	GrantLiveMissionAward(context.Context, *GrantLiveMissionAwardReq) (*GrantLiveMissionAwardResp, error)
	GetAnchorList(context.Context, *GetAnchorListReq) (*GetAnchorListResp, error)
	BatDelChannelLiveInfo(context.Context, *BatDelChannelLiveInfoReq) (*BatDelChannelLiveInfoResp, error)
	SetChannelLiveTag(context.Context, *SetChannelLiveTagReq) (*SetChannelLiveTagResp, error)
	BatchAddAnchor(context.Context, *BatchAddAnchorReq) (*BatchAddAnchorResp, error)
	GetAnchorOperRecord(context.Context, *GetAnchorOperRecordReq) (*GetAnchorOperRecordResp, error)
	AddVirtualAnchorPer(context.Context, *AddVirtualAnchorPerReq) (*AddVirtualAnchorPerResp, error)
	UpdateVirtualAnchorPer(context.Context, *UpdateVirtualAnchorPerReq) (*UpdateVirtualAnchorPerResp, error)
	DelVirtualAnchorPer(context.Context, *DelVirtualAnchorPerReq) (*DelVirtualAnchorPerResp, error)
	GetVirtualAnchorPerList(context.Context, *GetVirtualAnchorPerListReq) (*GetVirtualAnchorPerListResp, error)
	GrantFansGiftPrivilege(context.Context, *GrantFansGiftPrivilegeReq) (*GrantFansGiftPrivilegeResp, error)
	GetAnchorScoreList(context.Context, *GetAnchorScoreListReq) (*GetAnchorScoreListResp, error)
	// 小众游戏相关
	GetMinorityGame(context.Context, *GetMinorityGameReq) (*GetMinorityGameResp, error)
	AddMinorityGame(context.Context, *AddMinorityGameReq) (*AddMinorityGameResp, error)
	RemoveMinorityGame(context.Context, *RemoveMinorityGameReq) (*RemoveMinorityGameResp, error)
	ChangeMinorityGame(context.Context, *ChangeMinorityGameReq) (*ChangeMinorityGameResp, error)
	// 贵族相关
	AddTempNobility(context.Context, *AddTempNobilityReq) (*AddTempNobilityResp, error)
	GetTempNobilityList(context.Context, *GetTempNobilityListReq) (*GetTempNobilityListResp, error)
	// 魔法精灵(幸运礼物)相关
	AddMagicSpirit(context.Context, *AddMagicSpiritReq) (*AddMagicSpiritResp, error)
	DelMagicSpirit(context.Context, *DelMagicSpiritReq) (*DelMagicSpiritResp, error)
	GetMagicSpirit(context.Context, *GetMagicSpiritReq) (*GetMagicSpiritResp, error)
	UpdateMagicSpirit(context.Context, *UpdateMagicSpiritReq) (*UpdateMagicSpiritResp, error)
	AddMagicSpiritPond(context.Context, *AddMagicSpiritPondReq) (*AddMagicSpiritPondResp, error)
	GetMagicSpiritPond(context.Context, *GetMagicSpiritPondReq) (*GetMagicSpiritPondResp, error)
	GetMagicSpiritConfTmp(context.Context, *GetMagicSpiritConfTmpReq) (*GetMagicSpiritConfTmpResp, error)
	AddMagicSpiritBlacklist(context.Context, *AddMagicSpiritBlacklistReq) (*AddMagicSpiritBlacklistResp, error)
	GetMagicSpiritBlacklist(context.Context, *GetMagicSpiritBlackListReq) (*GetMagicSpiritBlackListResp, error)
	DelMagicSpiritBlacklist(context.Context, *DelMagicSpiritBlacklistReq) (*DelMagicSpiritBlacklistResp, error)
	SetCommonConf(context.Context, *SetCommonConfReq) (*SetCommonConfResp, error)
	GetCommonConf(context.Context, *GetCommonConfReq) (*GetCommonConfResp, error)
	// 升级礼物
	// 获取升级礼物的所有历史版本，后台管理使用
	GetLevelupPresentVersionList(context.Context, *ItemReq) (*VersionList, error)
	// 新增活动升级礼物的版本，后台管理使用
	AddLevelupPresentVersion(context.Context, *ItemVersionReq) (*EmptyMsg, error)
	// 查询父级礼物列表
	GetLevelupParentPresentList(context.Context, *OffsetTypeReq) (*EntireParentPresentDataList, error)
	// 查询子级礼物列表
	GetLevelupChildPresentList(context.Context, *ItemReq) (*EntireChildPresentDataList, error)
	// 新增父级升级礼物信息
	AddLevelupParentPresent(context.Context, *AddLevelupParentPresentReq) (*EmptyMsg, error)
	// 修改父级升级礼物信息
	UpdateLevelupParentPresent(context.Context, *UpdateLevelupParentPresentReq) (*EmptyMsg, error)
	// 删除父级升级礼物
	DeleteLevelupParentPresent(context.Context, *ItemReq) (*EmptyMsg, error)
	// 新增子级升级礼物信息
	AddLevelupChildPresent(context.Context, *AddLevelupChildPresentReq) (*EmptyMsg, error)
	// 修改子级升级礼物信息
	UpdateLevelupChildPresent(context.Context, *UpdateLevelupChildPresentReq) (*EmptyMsg, error)
	// 删除子级升级礼物
	DeleteLevelupChildPresent(context.Context, *ItemReq) (*EmptyMsg, error)
	// 查询父级礼物信息
	GetLevelupParentPresent(context.Context, *ItemReq) (*EntireParentPresentData, error)
	// 查询子级礼物信息
	GetLevelupChildPresent(context.Context, *ItemReq) (*EntireChildPresentData, error)
	// 结算工单化
	// 税点录入
	RecordTaxRate(context.Context, *settlement_bill.RecordTaxRateReq) (*settlement_bill.RecordTaxRateResp, error)
	// 获取当前时间点会长税点
	GetGuildTaxRate(context.Context, *settlement_bill.GetGuildTaxRateReq) (*settlement_bill.GetGuildTaxRateResp, error)
	// 获取税点列表
	GetTaxRateList(context.Context, *settlement_bill.GetTaxRateListReq) (*settlement_bill.GetTaxRateListResp, error)
	// 额外收益录入
	RecordExtraIncome(context.Context, *settlement_bill.RecordExtraIncomeReq) (*settlement_bill.RecordExtraIncomeResp, error)
	// 获取额外收益录入列表
	GetExtraIncomeRecordList(context.Context, *settlement_bill.GetExtraIncomeRecordListReq) (*settlement_bill.GetExtraIncomeRecordListResp, error)
	// 获取额外收益详情列表 - 深度
	GetExtraIncomeDetailDeepCoop(context.Context, *settlement_bill.GetExtraIncomeDetailReq) (*settlement_bill.GetExtraIncomeDetailDeepCoopResp, error)
	// 获取额外收益详情列表 - 主播补贴
	GetExtraIncomeDetailChannelSubsidy(context.Context, *settlement_bill.GetExtraIncomeDetailReq) (*settlement_bill.GetExtraIncomeDetailChannelSubsidyResp, error)
	// 获取额外收益详情列表 - 新公会补贴
	GetExtraIncomeDetailNewGuildSubsidy(context.Context, *settlement_bill.GetExtraIncomeDetailReq) (*settlement_bill.GetExtraIncomeDetailNewGuildSubsidyResp, error)
	// 获取额外收益详情列表 - 扣款
	GetExtraIncomeDetailDeduct(context.Context, *settlement_bill.GetExtraIncomeDetailReq) (*settlement_bill.GetExtraIncomeDetailDeductResp, error)
	// 黑暗礼物奖励相关接口
	AddDarkGiftBonusBuffConf(context.Context, *dark_gift_bonus.AddBuffConfReq) (*dark_gift_bonus.AddBuffConfResp, error)
	DelDarkGiftBonusBuffConf(context.Context, *dark_gift_bonus.DelBuffConfReq) (*dark_gift_bonus.DelBuffConfResp, error)
	GetDarkGiftBonusBuffConf(context.Context, *dark_gift_bonus.GetBuffConfReq) (*dark_gift_bonus.GetBuffConfResp, error)
	UpdateDarkGiftBonusBuffConf(context.Context, *dark_gift_bonus.UpdateBuffConfReq) (*dark_gift_bonus.UpdateBuffConfResp, error)
	// 坐骑相关接口
	// 添加座骑配置
	AddUserDecorationConfig(context.Context, *AddUserDecorationConfigReq) (*AddUserDecorationConfigResp, error)
	// 删除座骑配置
	DelUserDecorationConfig(context.Context, *DelUserDecorationConfigReq) (*DelUserDecorationConfigResp, error)
	// 更新座骑配置
	UpdateUserDecorationConfig(context.Context, *UpdateUserDecorationConfigReq) (*UpdateUserDecorationConfigResp, error)
	// 获取所有座骑配置
	GetChannelEnterSpecialEffectConfig(context.Context, *EmptyMsg) (*ChannelEnterSpecialEffectConfig, error)
	// 配置财富/贵族等级特定座骑
	SetUserDecorationSpecialLevel(context.Context, *SetUserDecorationSpecialLevelReq) (*SetUserDecorationSpecialLevelResp, error)
	// 发放座骑
	GrantDecorationToUserV2(context.Context, *GrantDecorationToUserReq) (*GrantDecorationToUserResp, error)
	// 批量发放座骑
	BatchGrantDecorationToUser(context.Context, *BatchGrantDecorationToUserReq) (*BatchGrantDecorationToUserResp, error)
	// 添加稀缺关系
	AddRareRelationship(context.Context, *RareRelationshipAddReq) (*RareRelationshipAddResp, error)
	// 编辑稀缺关系
	UpdateRareRelationship(context.Context, *RareRelationshipUpdateReq) (*RareRelationshipUpdateResp, error)
	// 删除稀缺关系
	DelRareRelationship(context.Context, *RareRelationshipDeleteReq) (*RareRelationshipDeleteResp, error)
	// 分页查询稀缺关系列表
	GetRareRelationshipList(context.Context, *RareRelationshipListReq) (*RareRelationshipListResp, error)
	// 根据条件查询稀缺关系
	GetRareRelationship(context.Context, *RareRelationshipGetReq) (*RareRelationshipGetResp, error)
	// 分页查询稀缺关系下发列表
	GetChannelRareRelationshipBindingList(context.Context, *ChannelRareRelationshipBindingListReq) (*ChannelRareRelationshipBindingListResp, error)
	// 下发稀缺关系
	AddChannelRareRelationshipBinding(context.Context, *ChannelRareRelationshipBindingAddReq) (*ChannelRareRelationshipBindingAddResp, error)
	// 编辑下发稀缺关系信息
	UpdateChannelRareRelationshipBinding(context.Context, *ChannelRareRelationshipBindingUpdateReq) (*ChannelRareRelationshipBindingUpdateResp, error)
	// 删除下发稀缺关系信息
	DelChannelRareRelationshipBinding(context.Context, *ChannelRareRelationshipBindingDeleteReq) (*ChannelRareRelationshipBindingDeleteResp, error)
	//  主题房相关
	ListTopicChannel(context.Context, *ListTopicChannelReq) (*ListTopicChannelResp, error)
	// 主播考核接口/////////////////////////////////////////////////////
	// 上传白名单
	AnchorCheckUploadWhiteList(context.Context, *WhiteListReq) (*EmptyMsg, error)
	// 查询考核列表
	AnchorCheckGetCheckList(context.Context, *AnchorCheckGetCheckListReq) (*AnchorCheckGetCheckListResp, error)
	// 录入考核
	AnchorCheckSetCheckData(context.Context, *AnchorCheckSetCheckDataReq) (*EmptyMsg, error)
	// 导出某个时间段白名单过期的用户
	ExportWhiteExpireList(context.Context, *ExportWhiteExpireListReq) (*ExportWhiteExpireListResp, error)
	// 置顶pia戏房间
	AddStickPiaRoom(context.Context, *AddStickPiaRoomReq) (*EmptyMsg, error)
	GetStickPiaRoom(context.Context, *GetStickPiaRoomReq) (*GetStickPiaRoomResp, error)
	UpdateStickPiaRoom(context.Context, *UpdateStickPiaRoomReq) (*EmptyMsg, error)
	// 房间预告位（不使用）
	AddPiaRoomAd(context.Context, *AddPiaRoomAdReq) (*EmptyMsg, error)
	GetPiaRoomAd(context.Context, *GetPiaRoomAdReq) (*GetPiaRoomAdResp, error)
	UpdatePiaRoomAd(context.Context, *UpdatePiaRoomAdReq) (*EmptyMsg, error)
	// 删除记录，共用
	DelPiaRoomRecord(context.Context, *DelPiaRoomRecordReq) (*EmptyMsg, error)
	// 房间pia戏权限管理
	GrantPiaRoomPermission(context.Context, *GrantPiaRoomPermissionReq) (*EmptyMsg, error)
	GetPiaRoomPermission(context.Context, *GetPiaRoomPermissionReq) (*GetPiaRoomPermissionResp, error)
	CancelPiaRoomPermission(context.Context, *CancelPiaRoomPermissionReq) (*EmptyMsg, error)
	BatchGetGuildInfoByDisplayId(context.Context, *BatchGetGuildInfoByDisplayIdReq) (*BatchGetGuildInfoByDisplayIdResp, error)
	BatchGrantPiaRoomPermission(context.Context, *BatchGrantPiaRoomPermissionReq) (*EmptyMsg, error)
	BatchCancelPiaRoomPermission(context.Context, *BatchCancelPiaRoomPermissionReq) (*EmptyMsg, error)
	GetTimeOverlapped(context.Context, *GetTimeOverlappedReq) (*EmptyMsg, error)
	// pia戏剧本置顶
	AddStickDrama(context.Context, *AddStickDramaReq) (*EmptyMsg, error)
	UpdateStickDrama(context.Context, *UpdateStickDramaReq) (*EmptyMsg, error)
	DeleteStickDrama(context.Context, *DeleteStickDramaReq) (*EmptyMsg, error)
	SearchStickDrama(context.Context, *SearchStickDramaReq) (*SearchStickDramaResp, error)
	GetDramaInfoByDramaId(context.Context, *GetDramaInfoByDramaIdReq) (*GetDramaInfoByDramaIdResp, error)
	GetStickDramaTags(context.Context, *GetStickDramaTagsReq) (*GetStickDramaTagsResp, error)
	CheckStickDramaTime(context.Context, *CheckStickDramaTimeReq) (*EmptyMsg, error)
	// ****** 房间推荐相关 *********//
	GetFlowCardLimitConfList(context.Context, *GetFlowCardLimitConfListReq) (*GetFlowCardLimitConfListResp, error)
	AddFlowCardLimitConf(context.Context, *AddFlowCardLimitConfReq) (*AddFlowCardLimitConfResp, error)
	UpdateFlowCardLimitConf(context.Context, *UpdateFlowCardLimitConfReq) (*UpdateFlowCardLimitConfResp, error)
	DelFlowCardLimitConf(context.Context, *DelFlowCardLimitConfReq) (*DelFlowCardLimitConfResp, error)
	GetGrantFlowCardList(context.Context, *GetGrantFlowCardListReq) (*GetGrantFlowCardListResp, error)
	GrantFlowCard(context.Context, *GrantFlowCardReq) (*GrantFlowCardResp, error)
	ReclaimGrantedFlowCard(context.Context, *ReclaimGrantedFlowCardReq) (*ReclaimGrantedFlowCardResp, error)
	BanGrantedFlowCard(context.Context, *BanGrantedFlowCardReq) (*BanGrantedFlowCardResp, error)
	GetPrepareChannelList(context.Context, *GetPrepareChannelListReq) (*GetPrepareChannelListResp, error)
	SetPrepareChannel(context.Context, *SetPrepareChannelReq) (*SetPrepareChannelResp, error)
	DelPrepareChannel(context.Context, *DelPrepareChannelReq) (*DelPrepareChannelResp, error)
	GetGuildChannelList(context.Context, *GetGuildChannelListReq) (*GetGuildChannelListResp, error)
	GetChannelPrepareList(context.Context, *GetChannelPrepareListReq) (*GetChannelPrepareListResp, error)
	GetPrepareBackupList(context.Context, *GetPrepareBackupListReq) (*GetPrepareBackupListResp, error)
	GetPrepareOperRecordList(context.Context, *GetPrepareOperRecordListReq) (*GetPrepareOperRecordListResp, error)
	GetDisplaySceneInfoList(context.Context, *GetDisplaySceneInfoListReq) (*GetDisplaySceneInfoListResp, error)
	AddDisplaySceneInfo(context.Context, *AddDisplaySceneInfoReq) (*AddDisplaySceneInfoResp, error)
	DelDisplaySceneInfo(context.Context, *DelDisplaySceneInfoReq) (*DelDisplaySceneInfoResp, error)
	// 房间玩法管理
	BatchGetChannelMode(context.Context, *BatchGetChannelModeReq) (*BatchGetChannelModeResp, error)
	BatchOperateChannelMode(context.Context, *BatchOperateChannelModeReq) (*EmptyMsg, error)
	// 骑士团
	GetKnightScoreList(context.Context, *GetKnightScoreListReq) (*GetKnightScoreListResp, error)
	// 礼物额外配置
	// 按id/名称搜索礼物
	SearchPresent(context.Context, *SearchPresentReq) (*SearchPresentResp, error)
	// 获取已有礼物浮层的礼物列表
	GetPresentFloatLayer(context.Context, *GetPresentFloatLayerReq) (*GetPresentFloatLayerResp, error)
	// 添加礼物浮层
	AddPresentFloatLayer(context.Context, *AddPresentFloatLayerReq) (*AddPresentFloatLayerResp, error)
	// 修改礼物浮层
	UpdatePresentFloatLayer(context.Context, *UpdatePresentFloatLayerReq) (*UpdatePresentFloatLayerResp, error)
	// 删除礼物浮层
	DelPresentFloatLayer(context.Context, *DelPresentFloatLayerReq) (*DelPresentFloatLayerResp, error)
	// 获取闪光效果库
	GetFlashEffectConfig(context.Context, *GetFlashEffectConfigReq) (*GetFlashEffectConfigResp, error)
	// 添加闪光效果
	AddFlashEffectConfig(context.Context, *AddFlashEffectConfigReq) (*AddFlashEffectConfigResp, error)
	// 更新闪光效果
	UpdateFlashEffectConfig(context.Context, *UpdateFlashEffectConfigReq) (*UpdateFlashEffectConfigResp, error)
	// 删除闪光效果
	DelFlashEffectConfig(context.Context, *DelFlashEffectConfigReq) (*DelFlashEffectConfigResp, error)
	// 获取已绑定闪光效果的礼物列表
	GetPresentFlashEffect(context.Context, *GetPresentFlashEffectReq) (*GetPresentFlashEffectResp, error)
	// 绑定闪光效果(更新、删除都用此接口)
	BoundPresentFlashEffect(context.Context, *BoundPresentFlashEffectReq) (*BoundPresentFlashEffectResp, error)
	// UpsertDecoration 用于为用户添加或更新一个装饰品。
	UpsertDecoration(context.Context, *UpsertDecorationReq) (*UpsertDecorationResp, error)
	// Decorations 返回某类装饰品。
	Decorations(context.Context, *DecorationsReq) (*DecorationsResp, error)
	// InsertDecoration 插入装饰品。
	InsertDecoration(context.Context, *InsertDecorationReq) (*InsertDecorationResp, error)
	// DelDecoration 删除装饰品。
	DelDecoration(context.Context, *DelDecorationReq) (*DelDecorationResp, error)
	// UserDecorations 查询用户的装饰
	UserDecorations(context.Context, *UserDecorationsReq) (*UserDecorationsResp, error)
	// BatchUserDecorations 批量为用户添加或更新一个装饰品
	BatchUserDecorations(context.Context, *BatchUpsertDecorationReq) (*BatchUpsertDecorationResp, error)
	// 靓号管理
	UpdateAlias(context.Context, *UpdateAliasReq) (*UpdateAliasResp, error)
	ChangeDisplayID(context.Context, *ChangeDisplayIDReq) (*ChangeDisplayIDResp, error)
	ListAlias(context.Context, *ListAliasReq) (*ListAliasResp, error)
	// 创建账号
	CreateUserByAlias(context.Context, *CreateUserByAliasReq) (*CreateUserByAliasResp, error)
	// 专属定制礼物
	AddCustomizedPresentConfig(context.Context, *AddCustomizedPresentConfigReq) (*AddCustomizedPresentConfigResp, error)
	UpdateCustomizedPresentConfig(context.Context, *UpdateCustomizedPresentConfigReq) (*UpdateCustomizedPresentConfigResp, error)
	GetAllCustomizedPresentConfig(context.Context, *GetAllCustomizedPresentConfigReq) (*GetAllCustomizedPresentConfigResp, error)
	DelCustomizedPresentConfig(context.Context, *DelCustomizedPresentConfigReq) (*DelCustomizedPresentConfigResp, error)
	// 从T盾获取用户的违规信息
	GetUserViolationsInfo(context.Context, *GetUserViolationsInfoReq) (*GetUserViolationsInfoResp, error)
	// 强制抱上麦
	TakeHoldChannelMicByForce(context.Context, *ChannelMicForceTakeHoldReq) (*ChannelMicForceTakeHoldResp, error)
	SetChannelMicStatus(context.Context, *SetChannelMicStatusReq) (*SetChannelMicStatusResp, error)
	// 房间玩法权限
	BatchGetChannelGameplayPerm(context.Context, *BatchGetChannelGameplayPermReq) (*BatchGetChannelGameplayPermResp, error)
	BatchOperateChannelGameplayPerm(context.Context, *BatchOperateChannelGameplayPermReq) (*BatchOperateChannelGameplayPermResp, error)
	// 大神带飞券 - 礼物配置
	AddTicketPresentConfig(context.Context, *AddTicketPresentConfigReq) (*AddTicketPresentConfigResp, error)
	UpdateTicketPresentConfig(context.Context, *UpdateTicketPresentConfigReq) (*UpdateTicketPresentConfigResp, error)
	GetAllTicketPresentConfig(context.Context, *GetAllTicketPresentConfigReq) (*GetAllTicketPresentConfigResp, error)
	DelTicketPresentConfig(context.Context, *DelTicketPresentConfigReq) (*DelTicketPresentConfigResp, error)
	// 添加注销白名单
	// 支持单个或批量通过TTid/Uid添加白名单
	AddUserUnregWhite(context.Context, *AddUserUnregWhiteReq) (*AddUserUnregWhiteResp, error)
	// 查询当前申请加入注销白名单用户的信息
	GetCurUnregUserInfo(context.Context, *GetCurUnregUserInfoReq) (*GetCurUnregUserInfoResp, error)
	// 查询用户在申请白名单时的信息
	GetUnregWhiteUserInfo(context.Context, *GetUnregWhiteUserInfoReq) (*GetUnregWhiteUserInfoResp, error)
	// 权限礼物 - 珍宝馆
	AddTreasurePrivilege(context.Context, *AddTreasurePrivilegeReq) (*AddTreasurePrivilegeResp, error)
	GetTreasurePrivilegeList(context.Context, *GetTreasurePrivilegeListReq) (*GetTreasurePrivilegeListResp, error)
	DelTreasurePrivilegeList(context.Context, *DelTreasurePrivilegeListReq) (*DelTreasurePrivilegeListResp, error)
	CheckTreasurePrivilege(context.Context, *CheckTreasurePrivilegeReq) (*CheckTreasurePrivilegeResp, error)
	// 礼物墙 - 未获得礼物
	// 运营后台 - 批量添加未获得礼物配置
	BatchAddMissingItemsConfig(context.Context, *BatchAddMissingItemsConfigRequest) (*BatchAddMissingItemsConfigResponse, error)
	// 运营后台 - 未获得礼物配置获取
	GetAllMissingItemsConfig(context.Context, *GetAllMissingItemsConfigRequest) (*GetAllMissingItemsConfigResponse, error)
	// 运营后台 - 未获得礼物配置更新
	UpdateMissingItemsConfig(context.Context, *UpdateMissingItemsConfigRequest) (*UpdateMissingItemsConfigResponse, error)
	// 运营后台 - 未获得礼物配置删除
	DeleteMissingItemsConfig(context.Context, *DeleteMissingItemsConfigRequest) (*DeleteMissingItemsConfigResponse, error)
}

func RegisterApicenterGoServer(s *grpc.Server, srv ApicenterGoServer) {
	s.RegisterService(&_ApicenterGo_serviceDesc, srv)
}

func _ApicenterGo_GetPresentConfigById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentConfigByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetPresentConfigById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetPresentConfigById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetPresentConfigById(ctx, req.(*GetPresentConfigByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetPresentConfigList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentConfigListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetPresentConfigList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetPresentConfigList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetPresentConfigList(ctx, req.(*GetPresentConfigListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AddPresentConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPresentConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AddPresentConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AddPresentConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AddPresentConfig(ctx, req.(*AddPresentConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_DelPresentConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelPresentConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).DelPresentConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/DelPresentConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).DelPresentConfig(ctx, req.(*DelPresentConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_UpdatePresentConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePresentConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).UpdatePresentConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/UpdatePresentConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).UpdatePresentConfig(ctx, req.(*UpdatePresentConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetUserPresentSend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserPresentSendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetUserPresentSend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetUserPresentSend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetUserPresentSend(ctx, req.(*GetUserPresentSendReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetUserPresentReceive_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserPresentReceiveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetUserPresentReceive(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetUserPresentReceive",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetUserPresentReceive(ctx, req.(*GetUserPresentReceiveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetAllFellowPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllFellowPresentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetAllFellowPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetAllFellowPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetAllFellowPresent(ctx, req.(*GetAllFellowPresentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AddPackageCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPackageCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AddPackageCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AddPackageCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AddPackageCfg(ctx, req.(*AddPackageCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_DelPackageCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelPackageCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).DelPackageCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/DelPackageCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).DelPackageCfg(ctx, req.(*DelPackageCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetPackageCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPackageCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetPackageCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetPackageCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetPackageCfg(ctx, req.(*GetPackageCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetPackageCfgByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPackageCfgByIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetPackageCfgByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetPackageCfgByIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetPackageCfgByIds(ctx, req.(*GetPackageCfgByIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AddPackageItemCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPackageItemCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AddPackageItemCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AddPackageItemCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AddPackageItemCfg(ctx, req.(*AddPackageItemCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_ModPackageItemCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModPackageItemCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).ModPackageItemCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/ModPackageItemCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).ModPackageItemCfg(ctx, req.(*ModPackageItemCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_DelPackageItemCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelPackageItemCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).DelPackageItemCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/DelPackageItemCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).DelPackageItemCfg(ctx, req.(*DelPackageItemCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetPackageItemCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPackageItemCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetPackageItemCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetPackageItemCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetPackageItemCfg(ctx, req.(*GetPackageItemCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GiveUserPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GiveUserPackageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GiveUserPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GiveUserPackage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GiveUserPackage(ctx, req.(*GiveUserPackageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AddFuncCardCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddFuncCardCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AddFuncCardCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AddFuncCardCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AddFuncCardCfg(ctx, req.(*AddFuncCardCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_DelFuncCardCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelFuncCardCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).DelFuncCardCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/DelFuncCardCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).DelFuncCardCfg(ctx, req.(*DelFuncCardCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetFuncCardCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFuncCardCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetFuncCardCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetFuncCardCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetFuncCardCfg(ctx, req.(*GetFuncCardCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetUserBackpack_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserBackpackReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetUserBackpack(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetUserBackpack",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetUserBackpack(ctx, req.(*GetUserBackpackReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetUserPackageReceive_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserPackageReceiveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetUserPackageReceive(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetUserPackageReceive",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetUserPackageReceive(ctx, req.(*GetUserPackageReceiveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AddBusiness_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddBusinessReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AddBusiness(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AddBusiness",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AddBusiness(ctx, req.(*AddBusinessReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetAllBusiness_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllBusinessReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetAllBusiness(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetAllBusiness",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetAllBusiness(ctx, req.(*GetAllBusinessReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetBusinessByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBusinessByIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetBusinessByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetBusinessByIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetBusinessByIds(ctx, req.(*GetBusinessByIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AddBusinessRiskControlConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddBusinessRiskControlConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AddBusinessRiskControlConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AddBusinessRiskControlConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AddBusinessRiskControlConf(ctx, req.(*AddBusinessRiskControlConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_ModBusinessRiskControlConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModBusinessRiskControlConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).ModBusinessRiskControlConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/ModBusinessRiskControlConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).ModBusinessRiskControlConf(ctx, req.(*ModBusinessRiskControlConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetBusinessRiskControlConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBusinessRiskControlConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetBusinessRiskControlConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetBusinessRiskControlConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetBusinessRiskControlConf(ctx, req.(*GetBusinessRiskControlConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetUserInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetUserInfo(ctx, req.(*GetUserInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_BatchGetUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).BatchGetUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/BatchGetUserInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).BatchGetUserInfo(ctx, req.(*BatchGetUserInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetUserWithExtraInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserWithExtraInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetUserWithExtraInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetUserWithExtraInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetUserWithExtraInfo(ctx, req.(*GetUserWithExtraInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_BatGetUserByTTid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetUserByTTidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).BatGetUserByTTid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/BatGetUserByTTid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).BatGetUserByTTid(ctx, req.(*BatGetUserByTTidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetUserConversion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserConversionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetUserConversion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetUserConversion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetUserConversion(ctx, req.(*GetUserConversionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_BatchDeductUserItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDeductUserItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).BatchDeductUserItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/BatchDeductUserItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).BatchDeductUserItem(ctx, req.(*BatchDeductUserItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetGuildById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetGuildById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetGuildById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetGuildById(ctx, req.(*GetGuildByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_BatchGetGuild_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetGuildReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).BatchGetGuild(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/BatchGetGuild",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).BatchGetGuild(ctx, req.(*BatchGetGuildReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_BatchGetGuildV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetGuildReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).BatchGetGuildV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/BatchGetGuildV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).BatchGetGuildV2(ctx, req.(*BatchGetGuildReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_ModifyGuildName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyGuildNameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).ModifyGuildName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/ModifyGuildName",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).ModifyGuildName(ctx, req.(*ModifyGuildNameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_SetGuildShortId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetGuildShortIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).SetGuildShortId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/SetGuildShortId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).SetGuildShortId(ctx, req.(*SetGuildShortIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_UnsetGuildShortId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnsetGuildShortIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).UnsetGuildShortId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/UnsetGuildShortId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).UnsetGuildShortId(ctx, req.(*UnsetGuildShortIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_ModifyGuildGameLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyGuildGameLimitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).ModifyGuildGameLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/ModifyGuildGameLimit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).ModifyGuildGameLimit(ctx, req.(*ModifyGuildGameLimitReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetApplySignRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetApplySignRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetApplySignRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetApplySignRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetApplySignRecord(ctx, req.(*GetApplySignRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_OfficialHandleApplySign_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OfficialHandleApplySignReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).OfficialHandleApplySign(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/OfficialHandleApplySign",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).OfficialHandleApplySign(ctx, req.(*OfficialHandleApplySignReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_BatchGetContractInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetContractInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).BatchGetContractInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/BatchGetContractInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).BatchGetContractInfo(ctx, req.(*BatchGetContractInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_ReclaimAnchorIdentity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReclaimAnchorIdentityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).ReclaimAnchorIdentity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/ReclaimAnchorIdentity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).ReclaimAnchorIdentity(ctx, req.(*ReclaimAnchorIdentityReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_BatchGetApplyBlacklist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetApplyBlacklistReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).BatchGetApplyBlacklist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/BatchGetApplyBlacklist",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).BatchGetApplyBlacklist(ctx, req.(*BatchGetApplyBlacklistReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_HandleApplyBlackInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleApplyBlackInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).HandleApplyBlackInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/HandleApplyBlackInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).HandleApplyBlackInfo(ctx, req.(*HandleApplyBlackInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetLiveAnchorExamine_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLiveAnchorExamineReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetLiveAnchorExamine(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetLiveAnchorExamine",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetLiveAnchorExamine(ctx, req.(*GetLiveAnchorExamineReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetAllLiveAnchorExamine_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllLiveAnchorExamineReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetAllLiveAnchorExamine(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetAllLiveAnchorExamine",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetAllLiveAnchorExamine(ctx, req.(*GetAllLiveAnchorExamineReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_UpdateLiveAnchorExamineStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateLiveAnchorExamineStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).UpdateLiveAnchorExamineStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/UpdateLiveAnchorExamineStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).UpdateLiveAnchorExamineStatus(ctx, req.(*UpdateLiveAnchorExamineStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_UpdateLiveAnchorExamineTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateLiveAnchorExamineTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).UpdateLiveAnchorExamineTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/UpdateLiveAnchorExamineTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).UpdateLiveAnchorExamineTime(ctx, req.(*UpdateLiveAnchorExamineTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetAnchorIdentityLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAnchorIdentityLogReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetAnchorIdentityLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetAnchorIdentityLog",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetAnchorIdentityLog(ctx, req.(*GetAnchorIdentityLogReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetPractitionerMonthActiveInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPractitionerMonthActiveInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetPractitionerMonthActiveInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetPractitionerMonthActiveInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetPractitionerMonthActiveInfo(ctx, req.(*GetPractitionerMonthActiveInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AwardTBeanToUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AwardTBeanToUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AwardTBeanToUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AwardTBeanToUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AwardTBeanToUser(ctx, req.(*AwardTBeanToUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AwardDiamondToUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AwardDiamondToUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AwardDiamondToUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AwardDiamondToUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AwardDiamondToUser(ctx, req.(*AwardDiamondToUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetAwardDiamondRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAwardDiamondRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetAwardDiamondRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetAwardDiamondRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetAwardDiamondRecord(ctx, req.(*GetAwardDiamondRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AwardExpToUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AwardExpToUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AwardExpToUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AwardExpToUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AwardExpToUser(ctx, req.(*AwardExpToUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetAwardExpRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAwardExpRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetAwardExpRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetAwardExpRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetAwardExpRecord(ctx, req.(*GetAwardExpRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetUserLoginInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserLoginInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetUserLoginInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetUserLoginInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetUserLoginInfo(ctx, req.(*GetUserLoginInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetBannedRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBannedRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetBannedRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetBannedRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetBannedRecord(ctx, req.(*GetBannedRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetBannedOperator_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBannedOperatorReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetBannedOperator(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetBannedOperator",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetBannedOperator(ctx, req.(*GetBannedOperatorReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetBannedAppealRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBannedAppealRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetBannedAppealRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetBannedAppealRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetBannedAppealRecord(ctx, req.(*GetBannedAppealRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_UpdateBannedAppealRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBannedAppealRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).UpdateBannedAppealRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/UpdateBannedAppealRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).UpdateBannedAppealRecord(ctx, req.(*UpdateBannedAppealRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetBannedCheckRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBannedCheckRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetBannedCheckRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetBannedCheckRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetBannedCheckRecord(ctx, req.(*GetBannedCheckRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_UpdateBannedCheckRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBannedCheckRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).UpdateBannedCheckRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/UpdateBannedCheckRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).UpdateBannedCheckRecord(ctx, req.(*UpdateBannedCheckRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetBannedCheckOperator_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBannedCheckOperatorReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetBannedCheckOperator(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetBannedCheckOperator",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetBannedCheckOperator(ctx, req.(*GetBannedCheckOperatorReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetUserLoginDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserLoginDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetUserLoginDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetUserLoginDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetUserLoginDevice(ctx, req.(*GetUserLoginDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetUserLoginWithDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserLoginWithDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetUserLoginWithDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetUserLoginWithDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetUserLoginWithDevice(ctx, req.(*GetUserLoginWithDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_BanUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BanUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).BanUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/BanUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).BanUser(ctx, req.(*BanUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_RecoverUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecoverUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).RecoverUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/RecoverUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).RecoverUser(ctx, req.(*RecoverUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_BatchRecoverUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchRecoverUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).BatchRecoverUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/BatchRecoverUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).BatchRecoverUser(ctx, req.(*BatchRecoverUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetDeviceLastLoginInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeviceLastLoginInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetDeviceLastLoginInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetDeviceLastLoginInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetDeviceLastLoginInfo(ctx, req.(*GetDeviceLastLoginInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetBanLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBanLogReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetBanLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetBanLog",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetBanLog(ctx, req.(*GetBanLogReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetUserBanStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserBanStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetUserBanStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetUserBanStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetUserBanStatus(ctx, req.(*GetUserBanStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_BatchBanUserWithDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchBanUserWithDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).BatchBanUserWithDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/BatchBanUserWithDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).BatchBanUserWithDevice(ctx, req.(*BatchBanUserWithDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_CanRecoverDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CanRecoverDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).CanRecoverDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/CanRecoverDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).CanRecoverDevice(ctx, req.(*CanRecoverDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetUserInviteHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserInviteHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetUserInviteHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetUserInviteHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetUserInviteHistory(ctx, req.(*GetUserInviteHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_ResetUserPassword_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetUserPasswordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).ResetUserPassword(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/ResetUserPassword",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).ResetUserPassword(ctx, req.(*ResetUserPasswordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_UnbindUserPhone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnbindUserPhoneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).UnbindUserPhone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/UnbindUserPhone",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).UnbindUserPhone(ctx, req.(*UnbindUserPhoneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_ClearSecurityQuestion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearSecurityQuestionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).ClearSecurityQuestion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/ClearSecurityQuestion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).ClearSecurityQuestion(ctx, req.(*ClearSecurityQuestionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_DetachThirdpart_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DetachThirdpartReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).DetachThirdpart(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/DetachThirdpart",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).DetachThirdpart(ctx, req.(*DetachThirdpartReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_BatchGetUserOfficialCert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserOfficialCertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).BatchGetUserOfficialCert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/BatchGetUserOfficialCert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).BatchGetUserOfficialCert(ctx, req.(*BatchGetUserOfficialCertReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetUserExp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserExpReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetUserExp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetUserExp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetUserExp(ctx, req.(*GetUserExpReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetUserControlInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserControlInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetUserControlInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetUserControlInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetUserControlInfo(ctx, req.(*GetUserControlInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_BatRemoveUserControlled_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatRemoveUserControlledReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).BatRemoveUserControlled(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/BatRemoveUserControlled",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).BatRemoveUserControlled(ctx, req.(*BatRemoveUserControlledReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetTheSameRealNameUserList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTheSameRealNameUserListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetTheSameRealNameUserList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetTheSameRealNameUserList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetTheSameRealNameUserList(ctx, req.(*GetTheSameRealNameUserListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetUserRealNameAuthInfoV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserRealNameAuthInfoV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetUserRealNameAuthInfoV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetUserRealNameAuthInfoV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetUserRealNameAuthInfoV2(ctx, req.(*GetUserRealNameAuthInfoV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_BatchGetUserRealNameAuthInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserRealNameAuthInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).BatchGetUserRealNameAuthInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/BatchGetUserRealNameAuthInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).BatchGetUserRealNameAuthInfo(ctx, req.(*BatchGetUserRealNameAuthInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_BatchGetChannelSimpleInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetChannelSimpleInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).BatchGetChannelSimpleInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/BatchGetChannelSimpleInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).BatchGetChannelSimpleInfo(ctx, req.(*BatchGetChannelSimpleInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetAllReportHistoryList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllReportHistoryListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetAllReportHistoryList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetAllReportHistoryList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetAllReportHistoryList(ctx, req.(*GetAllReportHistoryListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetChannelReportStat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelReportStatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetChannelReportStat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetChannelReportStat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetChannelReportStat(ctx, req.(*GetChannelReportStatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetChannelReportProcList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelReportProcListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetChannelReportProcList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetChannelReportProcList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetChannelReportProcList(ctx, req.(*GetChannelReportProcListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_SanctionChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SanctionChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).SanctionChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/SanctionChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).SanctionChannel(ctx, req.(*SanctionChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetBannedChannelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBannedChannelListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetBannedChannelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetBannedChannelList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetBannedChannelList(ctx, req.(*GetBannedChannelListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AddChannelWhiteListInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddChannelWhiteListInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AddChannelWhiteListInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AddChannelWhiteListInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AddChannelWhiteListInfo(ctx, req.(*AddChannelWhiteListInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_DelChannelWhiteListInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelChannelWhiteListInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).DelChannelWhiteListInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/DelChannelWhiteListInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).DelChannelWhiteListInfo(ctx, req.(*DelChannelWhiteListInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetChannelWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelWhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetChannelWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetChannelWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetChannelWhiteList(ctx, req.(*GetChannelWhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetChannelSimpleInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelSimpleInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetChannelSimpleInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetChannelSimpleInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetChannelSimpleInfo(ctx, req.(*GetChannelSimpleInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_BatchGetChannelTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetChannelTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).BatchGetChannelTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/BatchGetChannelTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).BatchGetChannelTag(ctx, req.(*BatchGetChannelTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_PushMsgToChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushMsgToChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).PushMsgToChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/PushMsgToChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).PushMsgToChannel(ctx, req.(*PushMsgToChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_PushMsgToUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushMsgToUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).PushMsgToUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/PushMsgToUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).PushMsgToUser(ctx, req.(*PushMsgToUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_PushActBreakingNews_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushActBreakingNewsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).PushActBreakingNews(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/PushActBreakingNews",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).PushActBreakingNews(ctx, req.(*PushActBreakingNewsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_PushFromExcel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushFromExcelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).PushFromExcel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/PushFromExcel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).PushFromExcel(ctx, req.(*PushFromExcelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_HobPush_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HobPushReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).HobPush(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/HobPush",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).HobPush(ctx, req.(*HobPushReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetHobPushRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHobPushRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetHobPushRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetHobPushRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetHobPushRecord(ctx, req.(*GetHobPushRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetChannelMicMode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelMicModeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetChannelMicMode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetChannelMicMode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetChannelMicMode(ctx, req.(*GetChannelMicModeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AddChannelBackgroundConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddChannelBackgroundConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AddChannelBackgroundConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AddChannelBackgroundConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AddChannelBackgroundConf(ctx, req.(*AddChannelBackgroundConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_DelChannelBackgroundConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelChannelBackgroundConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).DelChannelBackgroundConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/DelChannelBackgroundConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).DelChannelBackgroundConf(ctx, req.(*DelChannelBackgroundConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetChannelBackgroundConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelBackgroundConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetChannelBackgroundConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetChannelBackgroundConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetChannelBackgroundConf(ctx, req.(*GetChannelBackgroundConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AddChannelBackgroundConfV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddChannelBackgroundConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AddChannelBackgroundConfV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AddChannelBackgroundConfV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AddChannelBackgroundConfV2(ctx, req.(*AddChannelBackgroundConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_UpdateChannelBackgroundConfV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateChannelBackgroundConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).UpdateChannelBackgroundConfV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/UpdateChannelBackgroundConfV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).UpdateChannelBackgroundConfV2(ctx, req.(*UpdateChannelBackgroundConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_DelChannelBackgroundConfV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelChannelBackgroundConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).DelChannelBackgroundConfV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/DelChannelBackgroundConfV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).DelChannelBackgroundConfV2(ctx, req.(*DelChannelBackgroundConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetChannelBackgroundConfV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelBackgroundConfV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetChannelBackgroundConfV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetChannelBackgroundConfV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetChannelBackgroundConfV2(ctx, req.(*GetChannelBackgroundConfV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_BatchGiveChannelBg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGiveChannelBgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).BatchGiveChannelBg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/BatchGiveChannelBg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).BatchGiveChannelBg(ctx, req.(*BatchGiveChannelBgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_UpdateGivenChannelBg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGivenChannelBgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).UpdateGivenChannelBg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/UpdateGivenChannelBg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).UpdateGivenChannelBg(ctx, req.(*UpdateGivenChannelBgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_DeleteGivenChannelBg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteGivenChannelBgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).DeleteGivenChannelBg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/DeleteGivenChannelBg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).DeleteGivenChannelBg(ctx, req.(*DeleteGivenChannelBgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_ListGivenChannelBg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListGivenChannelBgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).ListGivenChannelBg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/ListGivenChannelBg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).ListGivenChannelBg(ctx, req.(*ListGivenChannelBgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_SwitchSearchWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SwitchSearchWhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).SwitchSearchWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/SwitchSearchWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).SwitchSearchWhiteList(ctx, req.(*SwitchSearchWhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetSearchWhiteListStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSearchWhiteListStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetSearchWhiteListStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetSearchWhiteListStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetSearchWhiteListStatus(ctx, req.(*GetSearchWhiteListStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_BatchAddSearchWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchAddSearchWhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).BatchAddSearchWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/BatchAddSearchWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).BatchAddSearchWhiteList(ctx, req.(*BatchAddSearchWhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_BatchDelSearchWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDelSearchWhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).BatchDelSearchWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/BatchDelSearchWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).BatchDelSearchWhiteList(ctx, req.(*BatchDelSearchWhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetSearchWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSearchWhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetSearchWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetSearchWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetSearchWhiteList(ctx, req.(*GetSearchWhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_ExportHotWord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExportHotWordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).ExportHotWord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/ExportHotWord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).ExportHotWord(ctx, req.(*ExportHotWordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetAnchorDailyRecordWithDateList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAnchorDailyRecordWithDateListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetAnchorDailyRecordWithDateList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetAnchorDailyRecordWithDateList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetAnchorDailyRecordWithDateList(ctx, req.(*GetAnchorDailyRecordWithDateListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AddAppointPkInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddAppointPkInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AddAppointPkInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AddAppointPkInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AddAppointPkInfo(ctx, req.(*AddAppointPkInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_UpdateAppointPkInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAppointPkInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).UpdateAppointPkInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/UpdateAppointPkInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).UpdateAppointPkInfo(ctx, req.(*UpdateAppointPkInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_DelAppointPkInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelAppointPkInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).DelAppointPkInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/DelAppointPkInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).DelAppointPkInfo(ctx, req.(*DelAppointPkInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetAppointPkInfoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAppointPkInfoListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetAppointPkInfoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetAppointPkInfoList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetAppointPkInfoList(ctx, req.(*GetAppointPkInfoListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_BatchAddAppointPkInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchAddAppointPkInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).BatchAddAppointPkInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/BatchAddAppointPkInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).BatchAddAppointPkInfo(ctx, req.(*BatchAddAppointPkInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AddPlateConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPlateConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AddPlateConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AddPlateConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AddPlateConfig(ctx, req.(*AddPlateConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_UpdatePlateConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePlateConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).UpdatePlateConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/UpdatePlateConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).UpdatePlateConfig(ctx, req.(*UpdatePlateConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_DelPlateConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelPlateConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).DelPlateConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/DelPlateConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).DelPlateConfig(ctx, req.(*DelPlateConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetPlateConfigList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlateConfigListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetPlateConfigList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetPlateConfigList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetPlateConfigList(ctx, req.(*GetPlateConfigListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GrantAnchorPlate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GrantAnchorPlateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GrantAnchorPlate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GrantAnchorPlate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GrantAnchorPlate(ctx, req.(*GrantAnchorPlateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_UpdateGrantedPlateInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGrantedPlateInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).UpdateGrantedPlateInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/UpdateGrantedPlateInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).UpdateGrantedPlateInfo(ctx, req.(*UpdateGrantedPlateInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_DelGrantedPlateInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelGrantedPlateInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).DelGrantedPlateInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/DelGrantedPlateInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).DelGrantedPlateInfo(ctx, req.(*DelGrantedPlateInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetGrantedPlateList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGrantedPlateListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetGrantedPlateList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetGrantedPlateList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetGrantedPlateList(ctx, req.(*GetGrantedPlateListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetPlateConfigById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlateConfigByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetPlateConfigById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetPlateConfigById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetPlateConfigById(ctx, req.(*GetPlateConfigByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_WearAnchorPlate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WearAnchorPlateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).WearAnchorPlate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/WearAnchorPlate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).WearAnchorPlate(ctx, req.(*WearAnchorPlateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_BatGrantAnchorPlate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGrantAnchorPlateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).BatGrantAnchorPlate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/BatGrantAnchorPlate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).BatGrantAnchorPlate(ctx, req.(*BatGrantAnchorPlateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GrantLiveMissionAward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GrantLiveMissionAwardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GrantLiveMissionAward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GrantLiveMissionAward",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GrantLiveMissionAward(ctx, req.(*GrantLiveMissionAwardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetAnchorList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAnchorListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetAnchorList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetAnchorList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetAnchorList(ctx, req.(*GetAnchorListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_BatDelChannelLiveInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatDelChannelLiveInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).BatDelChannelLiveInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/BatDelChannelLiveInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).BatDelChannelLiveInfo(ctx, req.(*BatDelChannelLiveInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_SetChannelLiveTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelLiveTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).SetChannelLiveTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/SetChannelLiveTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).SetChannelLiveTag(ctx, req.(*SetChannelLiveTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_BatchAddAnchor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchAddAnchorReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).BatchAddAnchor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/BatchAddAnchor",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).BatchAddAnchor(ctx, req.(*BatchAddAnchorReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetAnchorOperRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAnchorOperRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetAnchorOperRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetAnchorOperRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetAnchorOperRecord(ctx, req.(*GetAnchorOperRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AddVirtualAnchorPer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddVirtualAnchorPerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AddVirtualAnchorPer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AddVirtualAnchorPer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AddVirtualAnchorPer(ctx, req.(*AddVirtualAnchorPerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_UpdateVirtualAnchorPer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateVirtualAnchorPerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).UpdateVirtualAnchorPer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/UpdateVirtualAnchorPer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).UpdateVirtualAnchorPer(ctx, req.(*UpdateVirtualAnchorPerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_DelVirtualAnchorPer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelVirtualAnchorPerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).DelVirtualAnchorPer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/DelVirtualAnchorPer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).DelVirtualAnchorPer(ctx, req.(*DelVirtualAnchorPerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetVirtualAnchorPerList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVirtualAnchorPerListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetVirtualAnchorPerList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetVirtualAnchorPerList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetVirtualAnchorPerList(ctx, req.(*GetVirtualAnchorPerListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GrantFansGiftPrivilege_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GrantFansGiftPrivilegeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GrantFansGiftPrivilege(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GrantFansGiftPrivilege",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GrantFansGiftPrivilege(ctx, req.(*GrantFansGiftPrivilegeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetAnchorScoreList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAnchorScoreListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetAnchorScoreList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetAnchorScoreList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetAnchorScoreList(ctx, req.(*GetAnchorScoreListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetMinorityGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMinorityGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetMinorityGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetMinorityGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetMinorityGame(ctx, req.(*GetMinorityGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AddMinorityGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddMinorityGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AddMinorityGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AddMinorityGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AddMinorityGame(ctx, req.(*AddMinorityGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_RemoveMinorityGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveMinorityGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).RemoveMinorityGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/RemoveMinorityGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).RemoveMinorityGame(ctx, req.(*RemoveMinorityGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_ChangeMinorityGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeMinorityGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).ChangeMinorityGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/ChangeMinorityGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).ChangeMinorityGame(ctx, req.(*ChangeMinorityGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AddTempNobility_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddTempNobilityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AddTempNobility(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AddTempNobility",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AddTempNobility(ctx, req.(*AddTempNobilityReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetTempNobilityList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTempNobilityListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetTempNobilityList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetTempNobilityList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetTempNobilityList(ctx, req.(*GetTempNobilityListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AddMagicSpirit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddMagicSpiritReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AddMagicSpirit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AddMagicSpirit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AddMagicSpirit(ctx, req.(*AddMagicSpiritReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_DelMagicSpirit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelMagicSpiritReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).DelMagicSpirit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/DelMagicSpirit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).DelMagicSpirit(ctx, req.(*DelMagicSpiritReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetMagicSpirit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMagicSpiritReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetMagicSpirit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetMagicSpirit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetMagicSpirit(ctx, req.(*GetMagicSpiritReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_UpdateMagicSpirit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateMagicSpiritReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).UpdateMagicSpirit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/UpdateMagicSpirit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).UpdateMagicSpirit(ctx, req.(*UpdateMagicSpiritReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AddMagicSpiritPond_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddMagicSpiritPondReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AddMagicSpiritPond(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AddMagicSpiritPond",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AddMagicSpiritPond(ctx, req.(*AddMagicSpiritPondReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetMagicSpiritPond_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMagicSpiritPondReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetMagicSpiritPond(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetMagicSpiritPond",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetMagicSpiritPond(ctx, req.(*GetMagicSpiritPondReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetMagicSpiritConfTmp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMagicSpiritConfTmpReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetMagicSpiritConfTmp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetMagicSpiritConfTmp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetMagicSpiritConfTmp(ctx, req.(*GetMagicSpiritConfTmpReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AddMagicSpiritBlacklist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddMagicSpiritBlacklistReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AddMagicSpiritBlacklist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AddMagicSpiritBlacklist",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AddMagicSpiritBlacklist(ctx, req.(*AddMagicSpiritBlacklistReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetMagicSpiritBlacklist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMagicSpiritBlackListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetMagicSpiritBlacklist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetMagicSpiritBlacklist",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetMagicSpiritBlacklist(ctx, req.(*GetMagicSpiritBlackListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_DelMagicSpiritBlacklist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelMagicSpiritBlacklistReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).DelMagicSpiritBlacklist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/DelMagicSpiritBlacklist",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).DelMagicSpiritBlacklist(ctx, req.(*DelMagicSpiritBlacklistReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_SetCommonConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetCommonConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).SetCommonConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/SetCommonConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).SetCommonConf(ctx, req.(*SetCommonConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetCommonConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommonConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetCommonConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetCommonConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetCommonConf(ctx, req.(*GetCommonConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetLevelupPresentVersionList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetLevelupPresentVersionList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetLevelupPresentVersionList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetLevelupPresentVersionList(ctx, req.(*ItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AddLevelupPresentVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ItemVersionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AddLevelupPresentVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AddLevelupPresentVersion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AddLevelupPresentVersion(ctx, req.(*ItemVersionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetLevelupParentPresentList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OffsetTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetLevelupParentPresentList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetLevelupParentPresentList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetLevelupParentPresentList(ctx, req.(*OffsetTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetLevelupChildPresentList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetLevelupChildPresentList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetLevelupChildPresentList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetLevelupChildPresentList(ctx, req.(*ItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AddLevelupParentPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddLevelupParentPresentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AddLevelupParentPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AddLevelupParentPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AddLevelupParentPresent(ctx, req.(*AddLevelupParentPresentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_UpdateLevelupParentPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateLevelupParentPresentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).UpdateLevelupParentPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/UpdateLevelupParentPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).UpdateLevelupParentPresent(ctx, req.(*UpdateLevelupParentPresentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_DeleteLevelupParentPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).DeleteLevelupParentPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/DeleteLevelupParentPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).DeleteLevelupParentPresent(ctx, req.(*ItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AddLevelupChildPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddLevelupChildPresentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AddLevelupChildPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AddLevelupChildPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AddLevelupChildPresent(ctx, req.(*AddLevelupChildPresentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_UpdateLevelupChildPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateLevelupChildPresentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).UpdateLevelupChildPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/UpdateLevelupChildPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).UpdateLevelupChildPresent(ctx, req.(*UpdateLevelupChildPresentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_DeleteLevelupChildPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).DeleteLevelupChildPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/DeleteLevelupChildPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).DeleteLevelupChildPresent(ctx, req.(*ItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetLevelupParentPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetLevelupParentPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetLevelupParentPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetLevelupParentPresent(ctx, req.(*ItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetLevelupChildPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetLevelupChildPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetLevelupChildPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetLevelupChildPresent(ctx, req.(*ItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_RecordTaxRate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(settlement_bill.RecordTaxRateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).RecordTaxRate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/RecordTaxRate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).RecordTaxRate(ctx, req.(*settlement_bill.RecordTaxRateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetGuildTaxRate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(settlement_bill.GetGuildTaxRateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetGuildTaxRate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetGuildTaxRate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetGuildTaxRate(ctx, req.(*settlement_bill.GetGuildTaxRateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetTaxRateList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(settlement_bill.GetTaxRateListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetTaxRateList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetTaxRateList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetTaxRateList(ctx, req.(*settlement_bill.GetTaxRateListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_RecordExtraIncome_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(settlement_bill.RecordExtraIncomeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).RecordExtraIncome(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/RecordExtraIncome",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).RecordExtraIncome(ctx, req.(*settlement_bill.RecordExtraIncomeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetExtraIncomeRecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(settlement_bill.GetExtraIncomeRecordListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetExtraIncomeRecordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetExtraIncomeRecordList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetExtraIncomeRecordList(ctx, req.(*settlement_bill.GetExtraIncomeRecordListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetExtraIncomeDetailDeepCoop_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(settlement_bill.GetExtraIncomeDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetExtraIncomeDetailDeepCoop(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetExtraIncomeDetailDeepCoop",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetExtraIncomeDetailDeepCoop(ctx, req.(*settlement_bill.GetExtraIncomeDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetExtraIncomeDetailChannelSubsidy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(settlement_bill.GetExtraIncomeDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetExtraIncomeDetailChannelSubsidy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetExtraIncomeDetailChannelSubsidy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetExtraIncomeDetailChannelSubsidy(ctx, req.(*settlement_bill.GetExtraIncomeDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetExtraIncomeDetailNewGuildSubsidy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(settlement_bill.GetExtraIncomeDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetExtraIncomeDetailNewGuildSubsidy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetExtraIncomeDetailNewGuildSubsidy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetExtraIncomeDetailNewGuildSubsidy(ctx, req.(*settlement_bill.GetExtraIncomeDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetExtraIncomeDetailDeduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(settlement_bill.GetExtraIncomeDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetExtraIncomeDetailDeduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetExtraIncomeDetailDeduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetExtraIncomeDetailDeduct(ctx, req.(*settlement_bill.GetExtraIncomeDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AddDarkGiftBonusBuffConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dark_gift_bonus.AddBuffConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AddDarkGiftBonusBuffConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AddDarkGiftBonusBuffConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AddDarkGiftBonusBuffConf(ctx, req.(*dark_gift_bonus.AddBuffConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_DelDarkGiftBonusBuffConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dark_gift_bonus.DelBuffConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).DelDarkGiftBonusBuffConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/DelDarkGiftBonusBuffConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).DelDarkGiftBonusBuffConf(ctx, req.(*dark_gift_bonus.DelBuffConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetDarkGiftBonusBuffConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dark_gift_bonus.GetBuffConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetDarkGiftBonusBuffConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetDarkGiftBonusBuffConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetDarkGiftBonusBuffConf(ctx, req.(*dark_gift_bonus.GetBuffConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_UpdateDarkGiftBonusBuffConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dark_gift_bonus.UpdateBuffConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).UpdateDarkGiftBonusBuffConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/UpdateDarkGiftBonusBuffConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).UpdateDarkGiftBonusBuffConf(ctx, req.(*dark_gift_bonus.UpdateBuffConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AddUserDecorationConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserDecorationConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AddUserDecorationConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AddUserDecorationConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AddUserDecorationConfig(ctx, req.(*AddUserDecorationConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_DelUserDecorationConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelUserDecorationConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).DelUserDecorationConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/DelUserDecorationConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).DelUserDecorationConfig(ctx, req.(*DelUserDecorationConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_UpdateUserDecorationConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserDecorationConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).UpdateUserDecorationConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/UpdateUserDecorationConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).UpdateUserDecorationConfig(ctx, req.(*UpdateUserDecorationConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetChannelEnterSpecialEffectConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyMsg)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetChannelEnterSpecialEffectConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetChannelEnterSpecialEffectConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetChannelEnterSpecialEffectConfig(ctx, req.(*EmptyMsg))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_SetUserDecorationSpecialLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserDecorationSpecialLevelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).SetUserDecorationSpecialLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/SetUserDecorationSpecialLevel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).SetUserDecorationSpecialLevel(ctx, req.(*SetUserDecorationSpecialLevelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GrantDecorationToUserV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GrantDecorationToUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GrantDecorationToUserV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GrantDecorationToUserV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GrantDecorationToUserV2(ctx, req.(*GrantDecorationToUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_BatchGrantDecorationToUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGrantDecorationToUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).BatchGrantDecorationToUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/BatchGrantDecorationToUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).BatchGrantDecorationToUser(ctx, req.(*BatchGrantDecorationToUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AddRareRelationship_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RareRelationshipAddReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AddRareRelationship(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AddRareRelationship",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AddRareRelationship(ctx, req.(*RareRelationshipAddReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_UpdateRareRelationship_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RareRelationshipUpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).UpdateRareRelationship(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/UpdateRareRelationship",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).UpdateRareRelationship(ctx, req.(*RareRelationshipUpdateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_DelRareRelationship_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RareRelationshipDeleteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).DelRareRelationship(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/DelRareRelationship",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).DelRareRelationship(ctx, req.(*RareRelationshipDeleteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetRareRelationshipList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RareRelationshipListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetRareRelationshipList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetRareRelationshipList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetRareRelationshipList(ctx, req.(*RareRelationshipListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetRareRelationship_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RareRelationshipGetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetRareRelationship(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetRareRelationship",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetRareRelationship(ctx, req.(*RareRelationshipGetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetChannelRareRelationshipBindingList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelRareRelationshipBindingListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetChannelRareRelationshipBindingList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetChannelRareRelationshipBindingList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetChannelRareRelationshipBindingList(ctx, req.(*ChannelRareRelationshipBindingListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AddChannelRareRelationshipBinding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelRareRelationshipBindingAddReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AddChannelRareRelationshipBinding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AddChannelRareRelationshipBinding",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AddChannelRareRelationshipBinding(ctx, req.(*ChannelRareRelationshipBindingAddReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_UpdateChannelRareRelationshipBinding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelRareRelationshipBindingUpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).UpdateChannelRareRelationshipBinding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/UpdateChannelRareRelationshipBinding",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).UpdateChannelRareRelationshipBinding(ctx, req.(*ChannelRareRelationshipBindingUpdateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_DelChannelRareRelationshipBinding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelRareRelationshipBindingDeleteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).DelChannelRareRelationshipBinding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/DelChannelRareRelationshipBinding",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).DelChannelRareRelationshipBinding(ctx, req.(*ChannelRareRelationshipBindingDeleteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_ListTopicChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTopicChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).ListTopicChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/ListTopicChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).ListTopicChannel(ctx, req.(*ListTopicChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AnchorCheckUploadWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AnchorCheckUploadWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AnchorCheckUploadWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AnchorCheckUploadWhiteList(ctx, req.(*WhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AnchorCheckGetCheckList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AnchorCheckGetCheckListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AnchorCheckGetCheckList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AnchorCheckGetCheckList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AnchorCheckGetCheckList(ctx, req.(*AnchorCheckGetCheckListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AnchorCheckSetCheckData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AnchorCheckSetCheckDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AnchorCheckSetCheckData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AnchorCheckSetCheckData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AnchorCheckSetCheckData(ctx, req.(*AnchorCheckSetCheckDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_ExportWhiteExpireList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExportWhiteExpireListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).ExportWhiteExpireList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/ExportWhiteExpireList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).ExportWhiteExpireList(ctx, req.(*ExportWhiteExpireListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AddStickPiaRoom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddStickPiaRoomReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AddStickPiaRoom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AddStickPiaRoom",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AddStickPiaRoom(ctx, req.(*AddStickPiaRoomReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetStickPiaRoom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStickPiaRoomReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetStickPiaRoom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetStickPiaRoom",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetStickPiaRoom(ctx, req.(*GetStickPiaRoomReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_UpdateStickPiaRoom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateStickPiaRoomReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).UpdateStickPiaRoom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/UpdateStickPiaRoom",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).UpdateStickPiaRoom(ctx, req.(*UpdateStickPiaRoomReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AddPiaRoomAd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPiaRoomAdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AddPiaRoomAd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AddPiaRoomAd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AddPiaRoomAd(ctx, req.(*AddPiaRoomAdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetPiaRoomAd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPiaRoomAdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetPiaRoomAd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetPiaRoomAd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetPiaRoomAd(ctx, req.(*GetPiaRoomAdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_UpdatePiaRoomAd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePiaRoomAdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).UpdatePiaRoomAd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/UpdatePiaRoomAd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).UpdatePiaRoomAd(ctx, req.(*UpdatePiaRoomAdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_DelPiaRoomRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelPiaRoomRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).DelPiaRoomRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/DelPiaRoomRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).DelPiaRoomRecord(ctx, req.(*DelPiaRoomRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GrantPiaRoomPermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GrantPiaRoomPermissionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GrantPiaRoomPermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GrantPiaRoomPermission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GrantPiaRoomPermission(ctx, req.(*GrantPiaRoomPermissionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetPiaRoomPermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPiaRoomPermissionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetPiaRoomPermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetPiaRoomPermission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetPiaRoomPermission(ctx, req.(*GetPiaRoomPermissionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_CancelPiaRoomPermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelPiaRoomPermissionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).CancelPiaRoomPermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/CancelPiaRoomPermission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).CancelPiaRoomPermission(ctx, req.(*CancelPiaRoomPermissionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_BatchGetGuildInfoByDisplayId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetGuildInfoByDisplayIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).BatchGetGuildInfoByDisplayId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/BatchGetGuildInfoByDisplayId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).BatchGetGuildInfoByDisplayId(ctx, req.(*BatchGetGuildInfoByDisplayIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_BatchGrantPiaRoomPermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGrantPiaRoomPermissionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).BatchGrantPiaRoomPermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/BatchGrantPiaRoomPermission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).BatchGrantPiaRoomPermission(ctx, req.(*BatchGrantPiaRoomPermissionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_BatchCancelPiaRoomPermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCancelPiaRoomPermissionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).BatchCancelPiaRoomPermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/BatchCancelPiaRoomPermission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).BatchCancelPiaRoomPermission(ctx, req.(*BatchCancelPiaRoomPermissionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetTimeOverlapped_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTimeOverlappedReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetTimeOverlapped(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetTimeOverlapped",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetTimeOverlapped(ctx, req.(*GetTimeOverlappedReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AddStickDrama_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddStickDramaReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AddStickDrama(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AddStickDrama",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AddStickDrama(ctx, req.(*AddStickDramaReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_UpdateStickDrama_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateStickDramaReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).UpdateStickDrama(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/UpdateStickDrama",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).UpdateStickDrama(ctx, req.(*UpdateStickDramaReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_DeleteStickDrama_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteStickDramaReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).DeleteStickDrama(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/DeleteStickDrama",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).DeleteStickDrama(ctx, req.(*DeleteStickDramaReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_SearchStickDrama_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchStickDramaReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).SearchStickDrama(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/SearchStickDrama",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).SearchStickDrama(ctx, req.(*SearchStickDramaReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetDramaInfoByDramaId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDramaInfoByDramaIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetDramaInfoByDramaId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetDramaInfoByDramaId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetDramaInfoByDramaId(ctx, req.(*GetDramaInfoByDramaIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetStickDramaTags_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStickDramaTagsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetStickDramaTags(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetStickDramaTags",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetStickDramaTags(ctx, req.(*GetStickDramaTagsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_CheckStickDramaTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckStickDramaTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).CheckStickDramaTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/CheckStickDramaTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).CheckStickDramaTime(ctx, req.(*CheckStickDramaTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetFlowCardLimitConfList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFlowCardLimitConfListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetFlowCardLimitConfList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetFlowCardLimitConfList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetFlowCardLimitConfList(ctx, req.(*GetFlowCardLimitConfListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AddFlowCardLimitConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddFlowCardLimitConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AddFlowCardLimitConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AddFlowCardLimitConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AddFlowCardLimitConf(ctx, req.(*AddFlowCardLimitConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_UpdateFlowCardLimitConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateFlowCardLimitConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).UpdateFlowCardLimitConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/UpdateFlowCardLimitConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).UpdateFlowCardLimitConf(ctx, req.(*UpdateFlowCardLimitConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_DelFlowCardLimitConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelFlowCardLimitConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).DelFlowCardLimitConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/DelFlowCardLimitConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).DelFlowCardLimitConf(ctx, req.(*DelFlowCardLimitConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetGrantFlowCardList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGrantFlowCardListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetGrantFlowCardList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetGrantFlowCardList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetGrantFlowCardList(ctx, req.(*GetGrantFlowCardListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GrantFlowCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GrantFlowCardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GrantFlowCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GrantFlowCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GrantFlowCard(ctx, req.(*GrantFlowCardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_ReclaimGrantedFlowCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReclaimGrantedFlowCardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).ReclaimGrantedFlowCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/ReclaimGrantedFlowCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).ReclaimGrantedFlowCard(ctx, req.(*ReclaimGrantedFlowCardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_BanGrantedFlowCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BanGrantedFlowCardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).BanGrantedFlowCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/BanGrantedFlowCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).BanGrantedFlowCard(ctx, req.(*BanGrantedFlowCardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetPrepareChannelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPrepareChannelListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetPrepareChannelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetPrepareChannelList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetPrepareChannelList(ctx, req.(*GetPrepareChannelListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_SetPrepareChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetPrepareChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).SetPrepareChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/SetPrepareChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).SetPrepareChannel(ctx, req.(*SetPrepareChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_DelPrepareChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelPrepareChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).DelPrepareChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/DelPrepareChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).DelPrepareChannel(ctx, req.(*DelPrepareChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetGuildChannelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildChannelListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetGuildChannelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetGuildChannelList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetGuildChannelList(ctx, req.(*GetGuildChannelListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetChannelPrepareList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelPrepareListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetChannelPrepareList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetChannelPrepareList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetChannelPrepareList(ctx, req.(*GetChannelPrepareListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetPrepareBackupList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPrepareBackupListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetPrepareBackupList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetPrepareBackupList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetPrepareBackupList(ctx, req.(*GetPrepareBackupListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetPrepareOperRecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPrepareOperRecordListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetPrepareOperRecordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetPrepareOperRecordList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetPrepareOperRecordList(ctx, req.(*GetPrepareOperRecordListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetDisplaySceneInfoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDisplaySceneInfoListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetDisplaySceneInfoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetDisplaySceneInfoList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetDisplaySceneInfoList(ctx, req.(*GetDisplaySceneInfoListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AddDisplaySceneInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddDisplaySceneInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AddDisplaySceneInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AddDisplaySceneInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AddDisplaySceneInfo(ctx, req.(*AddDisplaySceneInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_DelDisplaySceneInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelDisplaySceneInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).DelDisplaySceneInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/DelDisplaySceneInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).DelDisplaySceneInfo(ctx, req.(*DelDisplaySceneInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_BatchGetChannelMode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetChannelModeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).BatchGetChannelMode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/BatchGetChannelMode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).BatchGetChannelMode(ctx, req.(*BatchGetChannelModeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_BatchOperateChannelMode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchOperateChannelModeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).BatchOperateChannelMode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/BatchOperateChannelMode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).BatchOperateChannelMode(ctx, req.(*BatchOperateChannelModeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetKnightScoreList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetKnightScoreListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetKnightScoreList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetKnightScoreList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetKnightScoreList(ctx, req.(*GetKnightScoreListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_SearchPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchPresentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).SearchPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/SearchPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).SearchPresent(ctx, req.(*SearchPresentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetPresentFloatLayer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentFloatLayerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetPresentFloatLayer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetPresentFloatLayer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetPresentFloatLayer(ctx, req.(*GetPresentFloatLayerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AddPresentFloatLayer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPresentFloatLayerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AddPresentFloatLayer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AddPresentFloatLayer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AddPresentFloatLayer(ctx, req.(*AddPresentFloatLayerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_UpdatePresentFloatLayer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePresentFloatLayerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).UpdatePresentFloatLayer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/UpdatePresentFloatLayer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).UpdatePresentFloatLayer(ctx, req.(*UpdatePresentFloatLayerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_DelPresentFloatLayer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelPresentFloatLayerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).DelPresentFloatLayer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/DelPresentFloatLayer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).DelPresentFloatLayer(ctx, req.(*DelPresentFloatLayerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetFlashEffectConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFlashEffectConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetFlashEffectConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetFlashEffectConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetFlashEffectConfig(ctx, req.(*GetFlashEffectConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AddFlashEffectConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddFlashEffectConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AddFlashEffectConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AddFlashEffectConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AddFlashEffectConfig(ctx, req.(*AddFlashEffectConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_UpdateFlashEffectConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateFlashEffectConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).UpdateFlashEffectConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/UpdateFlashEffectConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).UpdateFlashEffectConfig(ctx, req.(*UpdateFlashEffectConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_DelFlashEffectConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelFlashEffectConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).DelFlashEffectConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/DelFlashEffectConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).DelFlashEffectConfig(ctx, req.(*DelFlashEffectConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetPresentFlashEffect_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentFlashEffectReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetPresentFlashEffect(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetPresentFlashEffect",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetPresentFlashEffect(ctx, req.(*GetPresentFlashEffectReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_BoundPresentFlashEffect_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BoundPresentFlashEffectReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).BoundPresentFlashEffect(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/BoundPresentFlashEffect",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).BoundPresentFlashEffect(ctx, req.(*BoundPresentFlashEffectReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_UpsertDecoration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertDecorationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).UpsertDecoration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/UpsertDecoration",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).UpsertDecoration(ctx, req.(*UpsertDecorationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_Decorations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DecorationsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).Decorations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/Decorations",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).Decorations(ctx, req.(*DecorationsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_InsertDecoration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InsertDecorationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).InsertDecoration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/InsertDecoration",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).InsertDecoration(ctx, req.(*InsertDecorationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_DelDecoration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelDecorationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).DelDecoration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/DelDecoration",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).DelDecoration(ctx, req.(*DelDecorationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_UserDecorations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserDecorationsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).UserDecorations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/UserDecorations",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).UserDecorations(ctx, req.(*UserDecorationsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_BatchUserDecorations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUpsertDecorationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).BatchUserDecorations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/BatchUserDecorations",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).BatchUserDecorations(ctx, req.(*BatchUpsertDecorationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_UpdateAlias_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAliasReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).UpdateAlias(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/UpdateAlias",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).UpdateAlias(ctx, req.(*UpdateAliasReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_ChangeDisplayID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeDisplayIDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).ChangeDisplayID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/ChangeDisplayID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).ChangeDisplayID(ctx, req.(*ChangeDisplayIDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_ListAlias_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAliasReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).ListAlias(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/ListAlias",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).ListAlias(ctx, req.(*ListAliasReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_CreateUserByAlias_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateUserByAliasReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).CreateUserByAlias(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/CreateUserByAlias",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).CreateUserByAlias(ctx, req.(*CreateUserByAliasReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AddCustomizedPresentConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddCustomizedPresentConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AddCustomizedPresentConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AddCustomizedPresentConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AddCustomizedPresentConfig(ctx, req.(*AddCustomizedPresentConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_UpdateCustomizedPresentConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCustomizedPresentConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).UpdateCustomizedPresentConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/UpdateCustomizedPresentConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).UpdateCustomizedPresentConfig(ctx, req.(*UpdateCustomizedPresentConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetAllCustomizedPresentConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllCustomizedPresentConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetAllCustomizedPresentConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetAllCustomizedPresentConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetAllCustomizedPresentConfig(ctx, req.(*GetAllCustomizedPresentConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_DelCustomizedPresentConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelCustomizedPresentConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).DelCustomizedPresentConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/DelCustomizedPresentConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).DelCustomizedPresentConfig(ctx, req.(*DelCustomizedPresentConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetUserViolationsInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserViolationsInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetUserViolationsInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetUserViolationsInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetUserViolationsInfo(ctx, req.(*GetUserViolationsInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_TakeHoldChannelMicByForce_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelMicForceTakeHoldReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).TakeHoldChannelMicByForce(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/TakeHoldChannelMicByForce",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).TakeHoldChannelMicByForce(ctx, req.(*ChannelMicForceTakeHoldReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_SetChannelMicStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelMicStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).SetChannelMicStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/SetChannelMicStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).SetChannelMicStatus(ctx, req.(*SetChannelMicStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_BatchGetChannelGameplayPerm_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetChannelGameplayPermReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).BatchGetChannelGameplayPerm(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/BatchGetChannelGameplayPerm",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).BatchGetChannelGameplayPerm(ctx, req.(*BatchGetChannelGameplayPermReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_BatchOperateChannelGameplayPerm_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchOperateChannelGameplayPermReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).BatchOperateChannelGameplayPerm(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/BatchOperateChannelGameplayPerm",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).BatchOperateChannelGameplayPerm(ctx, req.(*BatchOperateChannelGameplayPermReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AddTicketPresentConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddTicketPresentConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AddTicketPresentConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AddTicketPresentConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AddTicketPresentConfig(ctx, req.(*AddTicketPresentConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_UpdateTicketPresentConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTicketPresentConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).UpdateTicketPresentConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/UpdateTicketPresentConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).UpdateTicketPresentConfig(ctx, req.(*UpdateTicketPresentConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetAllTicketPresentConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllTicketPresentConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetAllTicketPresentConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetAllTicketPresentConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetAllTicketPresentConfig(ctx, req.(*GetAllTicketPresentConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_DelTicketPresentConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelTicketPresentConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).DelTicketPresentConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/DelTicketPresentConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).DelTicketPresentConfig(ctx, req.(*DelTicketPresentConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AddUserUnregWhite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserUnregWhiteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AddUserUnregWhite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AddUserUnregWhite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AddUserUnregWhite(ctx, req.(*AddUserUnregWhiteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetCurUnregUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCurUnregUserInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetCurUnregUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetCurUnregUserInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetCurUnregUserInfo(ctx, req.(*GetCurUnregUserInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetUnregWhiteUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUnregWhiteUserInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetUnregWhiteUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetUnregWhiteUserInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetUnregWhiteUserInfo(ctx, req.(*GetUnregWhiteUserInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_AddTreasurePrivilege_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddTreasurePrivilegeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).AddTreasurePrivilege(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/AddTreasurePrivilege",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).AddTreasurePrivilege(ctx, req.(*AddTreasurePrivilegeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetTreasurePrivilegeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTreasurePrivilegeListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetTreasurePrivilegeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetTreasurePrivilegeList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetTreasurePrivilegeList(ctx, req.(*GetTreasurePrivilegeListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_DelTreasurePrivilegeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelTreasurePrivilegeListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).DelTreasurePrivilegeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/DelTreasurePrivilegeList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).DelTreasurePrivilegeList(ctx, req.(*DelTreasurePrivilegeListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_CheckTreasurePrivilege_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckTreasurePrivilegeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).CheckTreasurePrivilege(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/CheckTreasurePrivilege",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).CheckTreasurePrivilege(ctx, req.(*CheckTreasurePrivilegeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_BatchAddMissingItemsConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchAddMissingItemsConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).BatchAddMissingItemsConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/BatchAddMissingItemsConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).BatchAddMissingItemsConfig(ctx, req.(*BatchAddMissingItemsConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_GetAllMissingItemsConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllMissingItemsConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).GetAllMissingItemsConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/GetAllMissingItemsConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).GetAllMissingItemsConfig(ctx, req.(*GetAllMissingItemsConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_UpdateMissingItemsConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateMissingItemsConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).UpdateMissingItemsConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/UpdateMissingItemsConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).UpdateMissingItemsConfig(ctx, req.(*UpdateMissingItemsConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApicenterGo_DeleteMissingItemsConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteMissingItemsConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApicenterGoServer).DeleteMissingItemsConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/apicentergo.ApicenterGo/DeleteMissingItemsConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApicenterGoServer).DeleteMissingItemsConfig(ctx, req.(*DeleteMissingItemsConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _ApicenterGo_serviceDesc = grpc.ServiceDesc{
	ServiceName: "apicentergo.ApicenterGo",
	HandlerType: (*ApicenterGoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPresentConfigById",
			Handler:    _ApicenterGo_GetPresentConfigById_Handler,
		},
		{
			MethodName: "GetPresentConfigList",
			Handler:    _ApicenterGo_GetPresentConfigList_Handler,
		},
		{
			MethodName: "AddPresentConfig",
			Handler:    _ApicenterGo_AddPresentConfig_Handler,
		},
		{
			MethodName: "DelPresentConfig",
			Handler:    _ApicenterGo_DelPresentConfig_Handler,
		},
		{
			MethodName: "UpdatePresentConfig",
			Handler:    _ApicenterGo_UpdatePresentConfig_Handler,
		},
		{
			MethodName: "GetUserPresentSend",
			Handler:    _ApicenterGo_GetUserPresentSend_Handler,
		},
		{
			MethodName: "GetUserPresentReceive",
			Handler:    _ApicenterGo_GetUserPresentReceive_Handler,
		},
		{
			MethodName: "GetAllFellowPresent",
			Handler:    _ApicenterGo_GetAllFellowPresent_Handler,
		},
		{
			MethodName: "AddPackageCfg",
			Handler:    _ApicenterGo_AddPackageCfg_Handler,
		},
		{
			MethodName: "DelPackageCfg",
			Handler:    _ApicenterGo_DelPackageCfg_Handler,
		},
		{
			MethodName: "GetPackageCfg",
			Handler:    _ApicenterGo_GetPackageCfg_Handler,
		},
		{
			MethodName: "GetPackageCfgByIds",
			Handler:    _ApicenterGo_GetPackageCfgByIds_Handler,
		},
		{
			MethodName: "AddPackageItemCfg",
			Handler:    _ApicenterGo_AddPackageItemCfg_Handler,
		},
		{
			MethodName: "ModPackageItemCfg",
			Handler:    _ApicenterGo_ModPackageItemCfg_Handler,
		},
		{
			MethodName: "DelPackageItemCfg",
			Handler:    _ApicenterGo_DelPackageItemCfg_Handler,
		},
		{
			MethodName: "GetPackageItemCfg",
			Handler:    _ApicenterGo_GetPackageItemCfg_Handler,
		},
		{
			MethodName: "GiveUserPackage",
			Handler:    _ApicenterGo_GiveUserPackage_Handler,
		},
		{
			MethodName: "AddFuncCardCfg",
			Handler:    _ApicenterGo_AddFuncCardCfg_Handler,
		},
		{
			MethodName: "DelFuncCardCfg",
			Handler:    _ApicenterGo_DelFuncCardCfg_Handler,
		},
		{
			MethodName: "GetFuncCardCfg",
			Handler:    _ApicenterGo_GetFuncCardCfg_Handler,
		},
		{
			MethodName: "GetUserBackpack",
			Handler:    _ApicenterGo_GetUserBackpack_Handler,
		},
		{
			MethodName: "GetUserPackageReceive",
			Handler:    _ApicenterGo_GetUserPackageReceive_Handler,
		},
		{
			MethodName: "AddBusiness",
			Handler:    _ApicenterGo_AddBusiness_Handler,
		},
		{
			MethodName: "GetAllBusiness",
			Handler:    _ApicenterGo_GetAllBusiness_Handler,
		},
		{
			MethodName: "GetBusinessByIds",
			Handler:    _ApicenterGo_GetBusinessByIds_Handler,
		},
		{
			MethodName: "AddBusinessRiskControlConf",
			Handler:    _ApicenterGo_AddBusinessRiskControlConf_Handler,
		},
		{
			MethodName: "ModBusinessRiskControlConf",
			Handler:    _ApicenterGo_ModBusinessRiskControlConf_Handler,
		},
		{
			MethodName: "GetBusinessRiskControlConf",
			Handler:    _ApicenterGo_GetBusinessRiskControlConf_Handler,
		},
		{
			MethodName: "GetUserInfo",
			Handler:    _ApicenterGo_GetUserInfo_Handler,
		},
		{
			MethodName: "BatchGetUserInfo",
			Handler:    _ApicenterGo_BatchGetUserInfo_Handler,
		},
		{
			MethodName: "GetUserWithExtraInfo",
			Handler:    _ApicenterGo_GetUserWithExtraInfo_Handler,
		},
		{
			MethodName: "BatGetUserByTTid",
			Handler:    _ApicenterGo_BatGetUserByTTid_Handler,
		},
		{
			MethodName: "GetUserConversion",
			Handler:    _ApicenterGo_GetUserConversion_Handler,
		},
		{
			MethodName: "BatchDeductUserItem",
			Handler:    _ApicenterGo_BatchDeductUserItem_Handler,
		},
		{
			MethodName: "GetGuildById",
			Handler:    _ApicenterGo_GetGuildById_Handler,
		},
		{
			MethodName: "BatchGetGuild",
			Handler:    _ApicenterGo_BatchGetGuild_Handler,
		},
		{
			MethodName: "BatchGetGuildV2",
			Handler:    _ApicenterGo_BatchGetGuildV2_Handler,
		},
		{
			MethodName: "ModifyGuildName",
			Handler:    _ApicenterGo_ModifyGuildName_Handler,
		},
		{
			MethodName: "SetGuildShortId",
			Handler:    _ApicenterGo_SetGuildShortId_Handler,
		},
		{
			MethodName: "UnsetGuildShortId",
			Handler:    _ApicenterGo_UnsetGuildShortId_Handler,
		},
		{
			MethodName: "ModifyGuildGameLimit",
			Handler:    _ApicenterGo_ModifyGuildGameLimit_Handler,
		},
		{
			MethodName: "GetApplySignRecord",
			Handler:    _ApicenterGo_GetApplySignRecord_Handler,
		},
		{
			MethodName: "OfficialHandleApplySign",
			Handler:    _ApicenterGo_OfficialHandleApplySign_Handler,
		},
		{
			MethodName: "BatchGetContractInfo",
			Handler:    _ApicenterGo_BatchGetContractInfo_Handler,
		},
		{
			MethodName: "ReclaimAnchorIdentity",
			Handler:    _ApicenterGo_ReclaimAnchorIdentity_Handler,
		},
		{
			MethodName: "BatchGetApplyBlacklist",
			Handler:    _ApicenterGo_BatchGetApplyBlacklist_Handler,
		},
		{
			MethodName: "HandleApplyBlackInfo",
			Handler:    _ApicenterGo_HandleApplyBlackInfo_Handler,
		},
		{
			MethodName: "GetLiveAnchorExamine",
			Handler:    _ApicenterGo_GetLiveAnchorExamine_Handler,
		},
		{
			MethodName: "GetAllLiveAnchorExamine",
			Handler:    _ApicenterGo_GetAllLiveAnchorExamine_Handler,
		},
		{
			MethodName: "UpdateLiveAnchorExamineStatus",
			Handler:    _ApicenterGo_UpdateLiveAnchorExamineStatus_Handler,
		},
		{
			MethodName: "UpdateLiveAnchorExamineTime",
			Handler:    _ApicenterGo_UpdateLiveAnchorExamineTime_Handler,
		},
		{
			MethodName: "GetAnchorIdentityLog",
			Handler:    _ApicenterGo_GetAnchorIdentityLog_Handler,
		},
		{
			MethodName: "GetPractitionerMonthActiveInfo",
			Handler:    _ApicenterGo_GetPractitionerMonthActiveInfo_Handler,
		},
		{
			MethodName: "AwardTBeanToUser",
			Handler:    _ApicenterGo_AwardTBeanToUser_Handler,
		},
		{
			MethodName: "AwardDiamondToUser",
			Handler:    _ApicenterGo_AwardDiamondToUser_Handler,
		},
		{
			MethodName: "GetAwardDiamondRecord",
			Handler:    _ApicenterGo_GetAwardDiamondRecord_Handler,
		},
		{
			MethodName: "AwardExpToUser",
			Handler:    _ApicenterGo_AwardExpToUser_Handler,
		},
		{
			MethodName: "GetAwardExpRecord",
			Handler:    _ApicenterGo_GetAwardExpRecord_Handler,
		},
		{
			MethodName: "GetUserLoginInfo",
			Handler:    _ApicenterGo_GetUserLoginInfo_Handler,
		},
		{
			MethodName: "GetBannedRecord",
			Handler:    _ApicenterGo_GetBannedRecord_Handler,
		},
		{
			MethodName: "GetBannedOperator",
			Handler:    _ApicenterGo_GetBannedOperator_Handler,
		},
		{
			MethodName: "GetBannedAppealRecord",
			Handler:    _ApicenterGo_GetBannedAppealRecord_Handler,
		},
		{
			MethodName: "UpdateBannedAppealRecord",
			Handler:    _ApicenterGo_UpdateBannedAppealRecord_Handler,
		},
		{
			MethodName: "GetBannedCheckRecord",
			Handler:    _ApicenterGo_GetBannedCheckRecord_Handler,
		},
		{
			MethodName: "UpdateBannedCheckRecord",
			Handler:    _ApicenterGo_UpdateBannedCheckRecord_Handler,
		},
		{
			MethodName: "GetBannedCheckOperator",
			Handler:    _ApicenterGo_GetBannedCheckOperator_Handler,
		},
		{
			MethodName: "GetUserLoginDevice",
			Handler:    _ApicenterGo_GetUserLoginDevice_Handler,
		},
		{
			MethodName: "GetUserLoginWithDevice",
			Handler:    _ApicenterGo_GetUserLoginWithDevice_Handler,
		},
		{
			MethodName: "BanUser",
			Handler:    _ApicenterGo_BanUser_Handler,
		},
		{
			MethodName: "RecoverUser",
			Handler:    _ApicenterGo_RecoverUser_Handler,
		},
		{
			MethodName: "BatchRecoverUser",
			Handler:    _ApicenterGo_BatchRecoverUser_Handler,
		},
		{
			MethodName: "GetDeviceLastLoginInfo",
			Handler:    _ApicenterGo_GetDeviceLastLoginInfo_Handler,
		},
		{
			MethodName: "GetBanLog",
			Handler:    _ApicenterGo_GetBanLog_Handler,
		},
		{
			MethodName: "GetUserBanStatus",
			Handler:    _ApicenterGo_GetUserBanStatus_Handler,
		},
		{
			MethodName: "BatchBanUserWithDevice",
			Handler:    _ApicenterGo_BatchBanUserWithDevice_Handler,
		},
		{
			MethodName: "CanRecoverDevice",
			Handler:    _ApicenterGo_CanRecoverDevice_Handler,
		},
		{
			MethodName: "GetUserInviteHistory",
			Handler:    _ApicenterGo_GetUserInviteHistory_Handler,
		},
		{
			MethodName: "ResetUserPassword",
			Handler:    _ApicenterGo_ResetUserPassword_Handler,
		},
		{
			MethodName: "UnbindUserPhone",
			Handler:    _ApicenterGo_UnbindUserPhone_Handler,
		},
		{
			MethodName: "ClearSecurityQuestion",
			Handler:    _ApicenterGo_ClearSecurityQuestion_Handler,
		},
		{
			MethodName: "DetachThirdpart",
			Handler:    _ApicenterGo_DetachThirdpart_Handler,
		},
		{
			MethodName: "BatchGetUserOfficialCert",
			Handler:    _ApicenterGo_BatchGetUserOfficialCert_Handler,
		},
		{
			MethodName: "GetUserExp",
			Handler:    _ApicenterGo_GetUserExp_Handler,
		},
		{
			MethodName: "GetUserControlInfo",
			Handler:    _ApicenterGo_GetUserControlInfo_Handler,
		},
		{
			MethodName: "BatRemoveUserControlled",
			Handler:    _ApicenterGo_BatRemoveUserControlled_Handler,
		},
		{
			MethodName: "GetTheSameRealNameUserList",
			Handler:    _ApicenterGo_GetTheSameRealNameUserList_Handler,
		},
		{
			MethodName: "GetUserRealNameAuthInfoV2",
			Handler:    _ApicenterGo_GetUserRealNameAuthInfoV2_Handler,
		},
		{
			MethodName: "BatchGetUserRealNameAuthInfo",
			Handler:    _ApicenterGo_BatchGetUserRealNameAuthInfo_Handler,
		},
		{
			MethodName: "BatchGetChannelSimpleInfo",
			Handler:    _ApicenterGo_BatchGetChannelSimpleInfo_Handler,
		},
		{
			MethodName: "GetAllReportHistoryList",
			Handler:    _ApicenterGo_GetAllReportHistoryList_Handler,
		},
		{
			MethodName: "GetChannelReportStat",
			Handler:    _ApicenterGo_GetChannelReportStat_Handler,
		},
		{
			MethodName: "GetChannelReportProcList",
			Handler:    _ApicenterGo_GetChannelReportProcList_Handler,
		},
		{
			MethodName: "SanctionChannel",
			Handler:    _ApicenterGo_SanctionChannel_Handler,
		},
		{
			MethodName: "GetBannedChannelList",
			Handler:    _ApicenterGo_GetBannedChannelList_Handler,
		},
		{
			MethodName: "AddChannelWhiteListInfo",
			Handler:    _ApicenterGo_AddChannelWhiteListInfo_Handler,
		},
		{
			MethodName: "DelChannelWhiteListInfo",
			Handler:    _ApicenterGo_DelChannelWhiteListInfo_Handler,
		},
		{
			MethodName: "GetChannelWhiteList",
			Handler:    _ApicenterGo_GetChannelWhiteList_Handler,
		},
		{
			MethodName: "GetChannelSimpleInfo",
			Handler:    _ApicenterGo_GetChannelSimpleInfo_Handler,
		},
		{
			MethodName: "BatchGetChannelTag",
			Handler:    _ApicenterGo_BatchGetChannelTag_Handler,
		},
		{
			MethodName: "PushMsgToChannel",
			Handler:    _ApicenterGo_PushMsgToChannel_Handler,
		},
		{
			MethodName: "PushMsgToUser",
			Handler:    _ApicenterGo_PushMsgToUser_Handler,
		},
		{
			MethodName: "PushActBreakingNews",
			Handler:    _ApicenterGo_PushActBreakingNews_Handler,
		},
		{
			MethodName: "PushFromExcel",
			Handler:    _ApicenterGo_PushFromExcel_Handler,
		},
		{
			MethodName: "HobPush",
			Handler:    _ApicenterGo_HobPush_Handler,
		},
		{
			MethodName: "GetHobPushRecord",
			Handler:    _ApicenterGo_GetHobPushRecord_Handler,
		},
		{
			MethodName: "GetChannelMicMode",
			Handler:    _ApicenterGo_GetChannelMicMode_Handler,
		},
		{
			MethodName: "AddChannelBackgroundConf",
			Handler:    _ApicenterGo_AddChannelBackgroundConf_Handler,
		},
		{
			MethodName: "DelChannelBackgroundConf",
			Handler:    _ApicenterGo_DelChannelBackgroundConf_Handler,
		},
		{
			MethodName: "GetChannelBackgroundConf",
			Handler:    _ApicenterGo_GetChannelBackgroundConf_Handler,
		},
		{
			MethodName: "AddChannelBackgroundConfV2",
			Handler:    _ApicenterGo_AddChannelBackgroundConfV2_Handler,
		},
		{
			MethodName: "UpdateChannelBackgroundConfV2",
			Handler:    _ApicenterGo_UpdateChannelBackgroundConfV2_Handler,
		},
		{
			MethodName: "DelChannelBackgroundConfV2",
			Handler:    _ApicenterGo_DelChannelBackgroundConfV2_Handler,
		},
		{
			MethodName: "GetChannelBackgroundConfV2",
			Handler:    _ApicenterGo_GetChannelBackgroundConfV2_Handler,
		},
		{
			MethodName: "BatchGiveChannelBg",
			Handler:    _ApicenterGo_BatchGiveChannelBg_Handler,
		},
		{
			MethodName: "UpdateGivenChannelBg",
			Handler:    _ApicenterGo_UpdateGivenChannelBg_Handler,
		},
		{
			MethodName: "DeleteGivenChannelBg",
			Handler:    _ApicenterGo_DeleteGivenChannelBg_Handler,
		},
		{
			MethodName: "ListGivenChannelBg",
			Handler:    _ApicenterGo_ListGivenChannelBg_Handler,
		},
		{
			MethodName: "SwitchSearchWhiteList",
			Handler:    _ApicenterGo_SwitchSearchWhiteList_Handler,
		},
		{
			MethodName: "GetSearchWhiteListStatus",
			Handler:    _ApicenterGo_GetSearchWhiteListStatus_Handler,
		},
		{
			MethodName: "BatchAddSearchWhiteList",
			Handler:    _ApicenterGo_BatchAddSearchWhiteList_Handler,
		},
		{
			MethodName: "BatchDelSearchWhiteList",
			Handler:    _ApicenterGo_BatchDelSearchWhiteList_Handler,
		},
		{
			MethodName: "GetSearchWhiteList",
			Handler:    _ApicenterGo_GetSearchWhiteList_Handler,
		},
		{
			MethodName: "ExportHotWord",
			Handler:    _ApicenterGo_ExportHotWord_Handler,
		},
		{
			MethodName: "GetAnchorDailyRecordWithDateList",
			Handler:    _ApicenterGo_GetAnchorDailyRecordWithDateList_Handler,
		},
		{
			MethodName: "AddAppointPkInfo",
			Handler:    _ApicenterGo_AddAppointPkInfo_Handler,
		},
		{
			MethodName: "UpdateAppointPkInfo",
			Handler:    _ApicenterGo_UpdateAppointPkInfo_Handler,
		},
		{
			MethodName: "DelAppointPkInfo",
			Handler:    _ApicenterGo_DelAppointPkInfo_Handler,
		},
		{
			MethodName: "GetAppointPkInfoList",
			Handler:    _ApicenterGo_GetAppointPkInfoList_Handler,
		},
		{
			MethodName: "BatchAddAppointPkInfo",
			Handler:    _ApicenterGo_BatchAddAppointPkInfo_Handler,
		},
		{
			MethodName: "AddPlateConfig",
			Handler:    _ApicenterGo_AddPlateConfig_Handler,
		},
		{
			MethodName: "UpdatePlateConfig",
			Handler:    _ApicenterGo_UpdatePlateConfig_Handler,
		},
		{
			MethodName: "DelPlateConfig",
			Handler:    _ApicenterGo_DelPlateConfig_Handler,
		},
		{
			MethodName: "GetPlateConfigList",
			Handler:    _ApicenterGo_GetPlateConfigList_Handler,
		},
		{
			MethodName: "GrantAnchorPlate",
			Handler:    _ApicenterGo_GrantAnchorPlate_Handler,
		},
		{
			MethodName: "UpdateGrantedPlateInfo",
			Handler:    _ApicenterGo_UpdateGrantedPlateInfo_Handler,
		},
		{
			MethodName: "DelGrantedPlateInfo",
			Handler:    _ApicenterGo_DelGrantedPlateInfo_Handler,
		},
		{
			MethodName: "GetGrantedPlateList",
			Handler:    _ApicenterGo_GetGrantedPlateList_Handler,
		},
		{
			MethodName: "GetPlateConfigById",
			Handler:    _ApicenterGo_GetPlateConfigById_Handler,
		},
		{
			MethodName: "WearAnchorPlate",
			Handler:    _ApicenterGo_WearAnchorPlate_Handler,
		},
		{
			MethodName: "BatGrantAnchorPlate",
			Handler:    _ApicenterGo_BatGrantAnchorPlate_Handler,
		},
		{
			MethodName: "GrantLiveMissionAward",
			Handler:    _ApicenterGo_GrantLiveMissionAward_Handler,
		},
		{
			MethodName: "GetAnchorList",
			Handler:    _ApicenterGo_GetAnchorList_Handler,
		},
		{
			MethodName: "BatDelChannelLiveInfo",
			Handler:    _ApicenterGo_BatDelChannelLiveInfo_Handler,
		},
		{
			MethodName: "SetChannelLiveTag",
			Handler:    _ApicenterGo_SetChannelLiveTag_Handler,
		},
		{
			MethodName: "BatchAddAnchor",
			Handler:    _ApicenterGo_BatchAddAnchor_Handler,
		},
		{
			MethodName: "GetAnchorOperRecord",
			Handler:    _ApicenterGo_GetAnchorOperRecord_Handler,
		},
		{
			MethodName: "AddVirtualAnchorPer",
			Handler:    _ApicenterGo_AddVirtualAnchorPer_Handler,
		},
		{
			MethodName: "UpdateVirtualAnchorPer",
			Handler:    _ApicenterGo_UpdateVirtualAnchorPer_Handler,
		},
		{
			MethodName: "DelVirtualAnchorPer",
			Handler:    _ApicenterGo_DelVirtualAnchorPer_Handler,
		},
		{
			MethodName: "GetVirtualAnchorPerList",
			Handler:    _ApicenterGo_GetVirtualAnchorPerList_Handler,
		},
		{
			MethodName: "GrantFansGiftPrivilege",
			Handler:    _ApicenterGo_GrantFansGiftPrivilege_Handler,
		},
		{
			MethodName: "GetAnchorScoreList",
			Handler:    _ApicenterGo_GetAnchorScoreList_Handler,
		},
		{
			MethodName: "GetMinorityGame",
			Handler:    _ApicenterGo_GetMinorityGame_Handler,
		},
		{
			MethodName: "AddMinorityGame",
			Handler:    _ApicenterGo_AddMinorityGame_Handler,
		},
		{
			MethodName: "RemoveMinorityGame",
			Handler:    _ApicenterGo_RemoveMinorityGame_Handler,
		},
		{
			MethodName: "ChangeMinorityGame",
			Handler:    _ApicenterGo_ChangeMinorityGame_Handler,
		},
		{
			MethodName: "AddTempNobility",
			Handler:    _ApicenterGo_AddTempNobility_Handler,
		},
		{
			MethodName: "GetTempNobilityList",
			Handler:    _ApicenterGo_GetTempNobilityList_Handler,
		},
		{
			MethodName: "AddMagicSpirit",
			Handler:    _ApicenterGo_AddMagicSpirit_Handler,
		},
		{
			MethodName: "DelMagicSpirit",
			Handler:    _ApicenterGo_DelMagicSpirit_Handler,
		},
		{
			MethodName: "GetMagicSpirit",
			Handler:    _ApicenterGo_GetMagicSpirit_Handler,
		},
		{
			MethodName: "UpdateMagicSpirit",
			Handler:    _ApicenterGo_UpdateMagicSpirit_Handler,
		},
		{
			MethodName: "AddMagicSpiritPond",
			Handler:    _ApicenterGo_AddMagicSpiritPond_Handler,
		},
		{
			MethodName: "GetMagicSpiritPond",
			Handler:    _ApicenterGo_GetMagicSpiritPond_Handler,
		},
		{
			MethodName: "GetMagicSpiritConfTmp",
			Handler:    _ApicenterGo_GetMagicSpiritConfTmp_Handler,
		},
		{
			MethodName: "AddMagicSpiritBlacklist",
			Handler:    _ApicenterGo_AddMagicSpiritBlacklist_Handler,
		},
		{
			MethodName: "GetMagicSpiritBlacklist",
			Handler:    _ApicenterGo_GetMagicSpiritBlacklist_Handler,
		},
		{
			MethodName: "DelMagicSpiritBlacklist",
			Handler:    _ApicenterGo_DelMagicSpiritBlacklist_Handler,
		},
		{
			MethodName: "SetCommonConf",
			Handler:    _ApicenterGo_SetCommonConf_Handler,
		},
		{
			MethodName: "GetCommonConf",
			Handler:    _ApicenterGo_GetCommonConf_Handler,
		},
		{
			MethodName: "GetLevelupPresentVersionList",
			Handler:    _ApicenterGo_GetLevelupPresentVersionList_Handler,
		},
		{
			MethodName: "AddLevelupPresentVersion",
			Handler:    _ApicenterGo_AddLevelupPresentVersion_Handler,
		},
		{
			MethodName: "GetLevelupParentPresentList",
			Handler:    _ApicenterGo_GetLevelupParentPresentList_Handler,
		},
		{
			MethodName: "GetLevelupChildPresentList",
			Handler:    _ApicenterGo_GetLevelupChildPresentList_Handler,
		},
		{
			MethodName: "AddLevelupParentPresent",
			Handler:    _ApicenterGo_AddLevelupParentPresent_Handler,
		},
		{
			MethodName: "UpdateLevelupParentPresent",
			Handler:    _ApicenterGo_UpdateLevelupParentPresent_Handler,
		},
		{
			MethodName: "DeleteLevelupParentPresent",
			Handler:    _ApicenterGo_DeleteLevelupParentPresent_Handler,
		},
		{
			MethodName: "AddLevelupChildPresent",
			Handler:    _ApicenterGo_AddLevelupChildPresent_Handler,
		},
		{
			MethodName: "UpdateLevelupChildPresent",
			Handler:    _ApicenterGo_UpdateLevelupChildPresent_Handler,
		},
		{
			MethodName: "DeleteLevelupChildPresent",
			Handler:    _ApicenterGo_DeleteLevelupChildPresent_Handler,
		},
		{
			MethodName: "GetLevelupParentPresent",
			Handler:    _ApicenterGo_GetLevelupParentPresent_Handler,
		},
		{
			MethodName: "GetLevelupChildPresent",
			Handler:    _ApicenterGo_GetLevelupChildPresent_Handler,
		},
		{
			MethodName: "RecordTaxRate",
			Handler:    _ApicenterGo_RecordTaxRate_Handler,
		},
		{
			MethodName: "GetGuildTaxRate",
			Handler:    _ApicenterGo_GetGuildTaxRate_Handler,
		},
		{
			MethodName: "GetTaxRateList",
			Handler:    _ApicenterGo_GetTaxRateList_Handler,
		},
		{
			MethodName: "RecordExtraIncome",
			Handler:    _ApicenterGo_RecordExtraIncome_Handler,
		},
		{
			MethodName: "GetExtraIncomeRecordList",
			Handler:    _ApicenterGo_GetExtraIncomeRecordList_Handler,
		},
		{
			MethodName: "GetExtraIncomeDetailDeepCoop",
			Handler:    _ApicenterGo_GetExtraIncomeDetailDeepCoop_Handler,
		},
		{
			MethodName: "GetExtraIncomeDetailChannelSubsidy",
			Handler:    _ApicenterGo_GetExtraIncomeDetailChannelSubsidy_Handler,
		},
		{
			MethodName: "GetExtraIncomeDetailNewGuildSubsidy",
			Handler:    _ApicenterGo_GetExtraIncomeDetailNewGuildSubsidy_Handler,
		},
		{
			MethodName: "GetExtraIncomeDetailDeduct",
			Handler:    _ApicenterGo_GetExtraIncomeDetailDeduct_Handler,
		},
		{
			MethodName: "AddDarkGiftBonusBuffConf",
			Handler:    _ApicenterGo_AddDarkGiftBonusBuffConf_Handler,
		},
		{
			MethodName: "DelDarkGiftBonusBuffConf",
			Handler:    _ApicenterGo_DelDarkGiftBonusBuffConf_Handler,
		},
		{
			MethodName: "GetDarkGiftBonusBuffConf",
			Handler:    _ApicenterGo_GetDarkGiftBonusBuffConf_Handler,
		},
		{
			MethodName: "UpdateDarkGiftBonusBuffConf",
			Handler:    _ApicenterGo_UpdateDarkGiftBonusBuffConf_Handler,
		},
		{
			MethodName: "AddUserDecorationConfig",
			Handler:    _ApicenterGo_AddUserDecorationConfig_Handler,
		},
		{
			MethodName: "DelUserDecorationConfig",
			Handler:    _ApicenterGo_DelUserDecorationConfig_Handler,
		},
		{
			MethodName: "UpdateUserDecorationConfig",
			Handler:    _ApicenterGo_UpdateUserDecorationConfig_Handler,
		},
		{
			MethodName: "GetChannelEnterSpecialEffectConfig",
			Handler:    _ApicenterGo_GetChannelEnterSpecialEffectConfig_Handler,
		},
		{
			MethodName: "SetUserDecorationSpecialLevel",
			Handler:    _ApicenterGo_SetUserDecorationSpecialLevel_Handler,
		},
		{
			MethodName: "GrantDecorationToUserV2",
			Handler:    _ApicenterGo_GrantDecorationToUserV2_Handler,
		},
		{
			MethodName: "BatchGrantDecorationToUser",
			Handler:    _ApicenterGo_BatchGrantDecorationToUser_Handler,
		},
		{
			MethodName: "AddRareRelationship",
			Handler:    _ApicenterGo_AddRareRelationship_Handler,
		},
		{
			MethodName: "UpdateRareRelationship",
			Handler:    _ApicenterGo_UpdateRareRelationship_Handler,
		},
		{
			MethodName: "DelRareRelationship",
			Handler:    _ApicenterGo_DelRareRelationship_Handler,
		},
		{
			MethodName: "GetRareRelationshipList",
			Handler:    _ApicenterGo_GetRareRelationshipList_Handler,
		},
		{
			MethodName: "GetRareRelationship",
			Handler:    _ApicenterGo_GetRareRelationship_Handler,
		},
		{
			MethodName: "GetChannelRareRelationshipBindingList",
			Handler:    _ApicenterGo_GetChannelRareRelationshipBindingList_Handler,
		},
		{
			MethodName: "AddChannelRareRelationshipBinding",
			Handler:    _ApicenterGo_AddChannelRareRelationshipBinding_Handler,
		},
		{
			MethodName: "UpdateChannelRareRelationshipBinding",
			Handler:    _ApicenterGo_UpdateChannelRareRelationshipBinding_Handler,
		},
		{
			MethodName: "DelChannelRareRelationshipBinding",
			Handler:    _ApicenterGo_DelChannelRareRelationshipBinding_Handler,
		},
		{
			MethodName: "ListTopicChannel",
			Handler:    _ApicenterGo_ListTopicChannel_Handler,
		},
		{
			MethodName: "AnchorCheckUploadWhiteList",
			Handler:    _ApicenterGo_AnchorCheckUploadWhiteList_Handler,
		},
		{
			MethodName: "AnchorCheckGetCheckList",
			Handler:    _ApicenterGo_AnchorCheckGetCheckList_Handler,
		},
		{
			MethodName: "AnchorCheckSetCheckData",
			Handler:    _ApicenterGo_AnchorCheckSetCheckData_Handler,
		},
		{
			MethodName: "ExportWhiteExpireList",
			Handler:    _ApicenterGo_ExportWhiteExpireList_Handler,
		},
		{
			MethodName: "AddStickPiaRoom",
			Handler:    _ApicenterGo_AddStickPiaRoom_Handler,
		},
		{
			MethodName: "GetStickPiaRoom",
			Handler:    _ApicenterGo_GetStickPiaRoom_Handler,
		},
		{
			MethodName: "UpdateStickPiaRoom",
			Handler:    _ApicenterGo_UpdateStickPiaRoom_Handler,
		},
		{
			MethodName: "AddPiaRoomAd",
			Handler:    _ApicenterGo_AddPiaRoomAd_Handler,
		},
		{
			MethodName: "GetPiaRoomAd",
			Handler:    _ApicenterGo_GetPiaRoomAd_Handler,
		},
		{
			MethodName: "UpdatePiaRoomAd",
			Handler:    _ApicenterGo_UpdatePiaRoomAd_Handler,
		},
		{
			MethodName: "DelPiaRoomRecord",
			Handler:    _ApicenterGo_DelPiaRoomRecord_Handler,
		},
		{
			MethodName: "GrantPiaRoomPermission",
			Handler:    _ApicenterGo_GrantPiaRoomPermission_Handler,
		},
		{
			MethodName: "GetPiaRoomPermission",
			Handler:    _ApicenterGo_GetPiaRoomPermission_Handler,
		},
		{
			MethodName: "CancelPiaRoomPermission",
			Handler:    _ApicenterGo_CancelPiaRoomPermission_Handler,
		},
		{
			MethodName: "BatchGetGuildInfoByDisplayId",
			Handler:    _ApicenterGo_BatchGetGuildInfoByDisplayId_Handler,
		},
		{
			MethodName: "BatchGrantPiaRoomPermission",
			Handler:    _ApicenterGo_BatchGrantPiaRoomPermission_Handler,
		},
		{
			MethodName: "BatchCancelPiaRoomPermission",
			Handler:    _ApicenterGo_BatchCancelPiaRoomPermission_Handler,
		},
		{
			MethodName: "GetTimeOverlapped",
			Handler:    _ApicenterGo_GetTimeOverlapped_Handler,
		},
		{
			MethodName: "AddStickDrama",
			Handler:    _ApicenterGo_AddStickDrama_Handler,
		},
		{
			MethodName: "UpdateStickDrama",
			Handler:    _ApicenterGo_UpdateStickDrama_Handler,
		},
		{
			MethodName: "DeleteStickDrama",
			Handler:    _ApicenterGo_DeleteStickDrama_Handler,
		},
		{
			MethodName: "SearchStickDrama",
			Handler:    _ApicenterGo_SearchStickDrama_Handler,
		},
		{
			MethodName: "GetDramaInfoByDramaId",
			Handler:    _ApicenterGo_GetDramaInfoByDramaId_Handler,
		},
		{
			MethodName: "GetStickDramaTags",
			Handler:    _ApicenterGo_GetStickDramaTags_Handler,
		},
		{
			MethodName: "CheckStickDramaTime",
			Handler:    _ApicenterGo_CheckStickDramaTime_Handler,
		},
		{
			MethodName: "GetFlowCardLimitConfList",
			Handler:    _ApicenterGo_GetFlowCardLimitConfList_Handler,
		},
		{
			MethodName: "AddFlowCardLimitConf",
			Handler:    _ApicenterGo_AddFlowCardLimitConf_Handler,
		},
		{
			MethodName: "UpdateFlowCardLimitConf",
			Handler:    _ApicenterGo_UpdateFlowCardLimitConf_Handler,
		},
		{
			MethodName: "DelFlowCardLimitConf",
			Handler:    _ApicenterGo_DelFlowCardLimitConf_Handler,
		},
		{
			MethodName: "GetGrantFlowCardList",
			Handler:    _ApicenterGo_GetGrantFlowCardList_Handler,
		},
		{
			MethodName: "GrantFlowCard",
			Handler:    _ApicenterGo_GrantFlowCard_Handler,
		},
		{
			MethodName: "ReclaimGrantedFlowCard",
			Handler:    _ApicenterGo_ReclaimGrantedFlowCard_Handler,
		},
		{
			MethodName: "BanGrantedFlowCard",
			Handler:    _ApicenterGo_BanGrantedFlowCard_Handler,
		},
		{
			MethodName: "GetPrepareChannelList",
			Handler:    _ApicenterGo_GetPrepareChannelList_Handler,
		},
		{
			MethodName: "SetPrepareChannel",
			Handler:    _ApicenterGo_SetPrepareChannel_Handler,
		},
		{
			MethodName: "DelPrepareChannel",
			Handler:    _ApicenterGo_DelPrepareChannel_Handler,
		},
		{
			MethodName: "GetGuildChannelList",
			Handler:    _ApicenterGo_GetGuildChannelList_Handler,
		},
		{
			MethodName: "GetChannelPrepareList",
			Handler:    _ApicenterGo_GetChannelPrepareList_Handler,
		},
		{
			MethodName: "GetPrepareBackupList",
			Handler:    _ApicenterGo_GetPrepareBackupList_Handler,
		},
		{
			MethodName: "GetPrepareOperRecordList",
			Handler:    _ApicenterGo_GetPrepareOperRecordList_Handler,
		},
		{
			MethodName: "GetDisplaySceneInfoList",
			Handler:    _ApicenterGo_GetDisplaySceneInfoList_Handler,
		},
		{
			MethodName: "AddDisplaySceneInfo",
			Handler:    _ApicenterGo_AddDisplaySceneInfo_Handler,
		},
		{
			MethodName: "DelDisplaySceneInfo",
			Handler:    _ApicenterGo_DelDisplaySceneInfo_Handler,
		},
		{
			MethodName: "BatchGetChannelMode",
			Handler:    _ApicenterGo_BatchGetChannelMode_Handler,
		},
		{
			MethodName: "BatchOperateChannelMode",
			Handler:    _ApicenterGo_BatchOperateChannelMode_Handler,
		},
		{
			MethodName: "GetKnightScoreList",
			Handler:    _ApicenterGo_GetKnightScoreList_Handler,
		},
		{
			MethodName: "SearchPresent",
			Handler:    _ApicenterGo_SearchPresent_Handler,
		},
		{
			MethodName: "GetPresentFloatLayer",
			Handler:    _ApicenterGo_GetPresentFloatLayer_Handler,
		},
		{
			MethodName: "AddPresentFloatLayer",
			Handler:    _ApicenterGo_AddPresentFloatLayer_Handler,
		},
		{
			MethodName: "UpdatePresentFloatLayer",
			Handler:    _ApicenterGo_UpdatePresentFloatLayer_Handler,
		},
		{
			MethodName: "DelPresentFloatLayer",
			Handler:    _ApicenterGo_DelPresentFloatLayer_Handler,
		},
		{
			MethodName: "GetFlashEffectConfig",
			Handler:    _ApicenterGo_GetFlashEffectConfig_Handler,
		},
		{
			MethodName: "AddFlashEffectConfig",
			Handler:    _ApicenterGo_AddFlashEffectConfig_Handler,
		},
		{
			MethodName: "UpdateFlashEffectConfig",
			Handler:    _ApicenterGo_UpdateFlashEffectConfig_Handler,
		},
		{
			MethodName: "DelFlashEffectConfig",
			Handler:    _ApicenterGo_DelFlashEffectConfig_Handler,
		},
		{
			MethodName: "GetPresentFlashEffect",
			Handler:    _ApicenterGo_GetPresentFlashEffect_Handler,
		},
		{
			MethodName: "BoundPresentFlashEffect",
			Handler:    _ApicenterGo_BoundPresentFlashEffect_Handler,
		},
		{
			MethodName: "UpsertDecoration",
			Handler:    _ApicenterGo_UpsertDecoration_Handler,
		},
		{
			MethodName: "Decorations",
			Handler:    _ApicenterGo_Decorations_Handler,
		},
		{
			MethodName: "InsertDecoration",
			Handler:    _ApicenterGo_InsertDecoration_Handler,
		},
		{
			MethodName: "DelDecoration",
			Handler:    _ApicenterGo_DelDecoration_Handler,
		},
		{
			MethodName: "UserDecorations",
			Handler:    _ApicenterGo_UserDecorations_Handler,
		},
		{
			MethodName: "BatchUserDecorations",
			Handler:    _ApicenterGo_BatchUserDecorations_Handler,
		},
		{
			MethodName: "UpdateAlias",
			Handler:    _ApicenterGo_UpdateAlias_Handler,
		},
		{
			MethodName: "ChangeDisplayID",
			Handler:    _ApicenterGo_ChangeDisplayID_Handler,
		},
		{
			MethodName: "ListAlias",
			Handler:    _ApicenterGo_ListAlias_Handler,
		},
		{
			MethodName: "CreateUserByAlias",
			Handler:    _ApicenterGo_CreateUserByAlias_Handler,
		},
		{
			MethodName: "AddCustomizedPresentConfig",
			Handler:    _ApicenterGo_AddCustomizedPresentConfig_Handler,
		},
		{
			MethodName: "UpdateCustomizedPresentConfig",
			Handler:    _ApicenterGo_UpdateCustomizedPresentConfig_Handler,
		},
		{
			MethodName: "GetAllCustomizedPresentConfig",
			Handler:    _ApicenterGo_GetAllCustomizedPresentConfig_Handler,
		},
		{
			MethodName: "DelCustomizedPresentConfig",
			Handler:    _ApicenterGo_DelCustomizedPresentConfig_Handler,
		},
		{
			MethodName: "GetUserViolationsInfo",
			Handler:    _ApicenterGo_GetUserViolationsInfo_Handler,
		},
		{
			MethodName: "TakeHoldChannelMicByForce",
			Handler:    _ApicenterGo_TakeHoldChannelMicByForce_Handler,
		},
		{
			MethodName: "SetChannelMicStatus",
			Handler:    _ApicenterGo_SetChannelMicStatus_Handler,
		},
		{
			MethodName: "BatchGetChannelGameplayPerm",
			Handler:    _ApicenterGo_BatchGetChannelGameplayPerm_Handler,
		},
		{
			MethodName: "BatchOperateChannelGameplayPerm",
			Handler:    _ApicenterGo_BatchOperateChannelGameplayPerm_Handler,
		},
		{
			MethodName: "AddTicketPresentConfig",
			Handler:    _ApicenterGo_AddTicketPresentConfig_Handler,
		},
		{
			MethodName: "UpdateTicketPresentConfig",
			Handler:    _ApicenterGo_UpdateTicketPresentConfig_Handler,
		},
		{
			MethodName: "GetAllTicketPresentConfig",
			Handler:    _ApicenterGo_GetAllTicketPresentConfig_Handler,
		},
		{
			MethodName: "DelTicketPresentConfig",
			Handler:    _ApicenterGo_DelTicketPresentConfig_Handler,
		},
		{
			MethodName: "AddUserUnregWhite",
			Handler:    _ApicenterGo_AddUserUnregWhite_Handler,
		},
		{
			MethodName: "GetCurUnregUserInfo",
			Handler:    _ApicenterGo_GetCurUnregUserInfo_Handler,
		},
		{
			MethodName: "GetUnregWhiteUserInfo",
			Handler:    _ApicenterGo_GetUnregWhiteUserInfo_Handler,
		},
		{
			MethodName: "AddTreasurePrivilege",
			Handler:    _ApicenterGo_AddTreasurePrivilege_Handler,
		},
		{
			MethodName: "GetTreasurePrivilegeList",
			Handler:    _ApicenterGo_GetTreasurePrivilegeList_Handler,
		},
		{
			MethodName: "DelTreasurePrivilegeList",
			Handler:    _ApicenterGo_DelTreasurePrivilegeList_Handler,
		},
		{
			MethodName: "CheckTreasurePrivilege",
			Handler:    _ApicenterGo_CheckTreasurePrivilege_Handler,
		},
		{
			MethodName: "BatchAddMissingItemsConfig",
			Handler:    _ApicenterGo_BatchAddMissingItemsConfig_Handler,
		},
		{
			MethodName: "GetAllMissingItemsConfig",
			Handler:    _ApicenterGo_GetAllMissingItemsConfig_Handler,
		},
		{
			MethodName: "UpdateMissingItemsConfig",
			Handler:    _ApicenterGo_UpdateMissingItemsConfig_Handler,
		},
		{
			MethodName: "DeleteMissingItemsConfig",
			Handler:    _ApicenterGo_DeleteMissingItemsConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "apicenter-go/apicenter-go-main.proto",
}

func init() {
	proto.RegisterFile("apicenter-go/apicenter-go-main.proto", fileDescriptor_apicenter_go_main_eccfdf8854392213)
}

var fileDescriptor_apicenter_go_main_eccfdf8854392213 = []byte{
	// 5662 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x9d, 0x5b, 0x73, 0xdc, 0x38,
	0x76, 0xc7, 0xc7, 0x2f, 0x49, 0x85, 0xb3, 0x93, 0xd9, 0xed, 0xb9, 0x5a, 0x1e, 0xdb, 0x63, 0x49,
	0xb6, 0x34, 0x97, 0x96, 0x76, 0xbd, 0x99, 0x4a, 0x9e, 0x52, 0xd5, 0x52, 0xcb, 0x1a, 0xef, 0x58,
	0x33, 0x5a, 0x49, 0xf6, 0x64, 0xb3, 0x49, 0x5c, 0x30, 0x09, 0x75, 0xa3, 0x9a, 0x4d, 0x72, 0x48,
	0xb6, 0x6c, 0x4d, 0xe5, 0x56, 0x95, 0xaa, 0x54, 0xa5, 0x92, 0xa7, 0x54, 0xe5, 0x83, 0xe4, 0x8b,
	0xe5, 0x33, 0xa4, 0x00, 0x02, 0x6c, 0x00, 0xe7, 0x0f, 0x92, 0x9a, 0x6c, 0xde, 0xa4, 0x3e, 0x3f,
	0x9e, 0x83, 0x1b, 0x81, 0x03, 0xe0, 0x00, 0x8c, 0xb6, 0x59, 0x21, 0x62, 0x9e, 0xd5, 0xbc, 0x1c,
	0xcf, 0xf2, 0x7d, 0xfb, 0x9f, 0xf1, 0x92, 0x89, 0x6c, 0xaf, 0x28, 0xf3, 0x3a, 0x1f, 0xbd, 0xdd,
	0x0a, 0x66, 0xf9, 0xc6, 0x96, 0xf3, 0x48, 0x29, 0xaa, 0xc5, 0x38, 0xce, 0xb3, 0xba, 0xcc, 0xd3,
	0x31, 0x2b, 0x44, 0xf3, 0xc4, 0xc6, 0x7d, 0x07, 0x7a, 0xc5, 0xe2, 0x45, 0xc1, 0xe2, 0x85, 0x05,
	0x6c, 0x3a, 0xc0, 0xaa, 0xe2, 0x65, 0x51, 0xf2, 0x8a, 0x67, 0xb5, 0xc5, 0xdc, 0x73, 0x13, 0x17,
	0xc7, 0xf9, 0xca, 0x91, 0x3f, 0x70, 0xe4, 0x71, 0x9e, 0x5d, 0xf1, 0xb2, 0x12, 0x79, 0x66, 0x21,
	0x0f, 0x5d, 0x15, 0x59, 0x3c, 0xcf, 0x4b, 0x95, 0x5a, 0x16, 0xdb, 0x9a, 0x3e, 0x71, 0xb0, 0xd9,
	0x4a, 0xa4, 0x49, 0x67, 0x5a, 0x2b, 0x5e, 0x5e, 0x89, 0x98, 0x07, 0xd3, 0x1a, 0xcf, 0x59, 0x96,
	0x71, 0xbb, 0x40, 0xee, 0x78, 0x69, 0x5d, 0x2e, 0x5f, 0xae, 0x85, 0x9f, 0x3a, 0xc2, 0x3a, 0x2f,
	0x44, 0x3c, 0xd6, 0x2a, 0x60, 0x12, 0x8c, 0xfa, 0xa5, 0x88, 0x2d, 0x13, 0xbb, 0x88, 0x91, 0x45,
	0x3f, 0x2b, 0xf3, 0x55, 0x66, 0x67, 0xe8, 0xae, 0x43, 0x56, 0x9c, 0x95, 0xf1, 0xdc, 0x12, 0x6f,
	0x41, 0x63, 0xa9, 0xb8, 0xe2, 0x41, 0x6b, 0x4e, 0x9a, 0xc7, 0x35, 0x7b, 0x15, 0x6c, 0x0b, 0x59,
	0xfe, 0x4a, 0xa4, 0xa2, 0xbe, 0x0e, 0xda, 0x5b, 0xb2, 0x99, 0x88, 0xc7, 0x55, 0x21, 0x4a, 0x61,
	0x57, 0xd1, 0x23, 0x07, 0x4a, 0xf9, 0x15, 0x4f, 0x57, 0xc5, 0x98, 0x36, 0x9a, 0x3d, 0x98, 0xf8,
	0x82, 0x97, 0x55, 0x9e, 0xb1, 0x54, 0xfc, 0xc8, 0x6a, 0xaf, 0x85, 0x54, 0xbc, 0xae, 0x53, 0xbe,
	0x94, 0x5a, 0x5e, 0x89, 0x34, 0xdd, 0xf7, 0xfe, 0x37, 0x58, 0xc2, 0xca, 0xc5, 0x78, 0x26, 0x2e,
	0xeb, 0xf1, 0xab, 0x3c, 0x5b, 0x55, 0xfb, 0xde, 0xff, 0x30, 0xaf, 0x25, 0x4f, 0x7d, 0x73, 0xf7,
	0x41, 0x83, 0x7c, 0x19, 0xcf, 0x79, 0xbc, 0x80, 0x0d, 0xa5, 0x10, 0x6c, 0x5c, 0xe6, 0xf9, 0xb2,
	0xab, 0x8a, 0xc7, 0x25, 0x97, 0xed, 0x89, 0x3b, 0x55, 0xbc, 0x83, 0x1b, 0x4c, 0x9e, 0xf0, 0xf1,
	0x72, 0x56, 0x06, 0x0b, 0x7f, 0x91, 0x89, 0xd9, 0xbc, 0x1e, 0xcb, 0x16, 0x53, 0x58, 0x90, 0xdb,
	0x4d, 0x98, 0x42, 0xe7, 0x6f, 0xea, 0x92, 0x05, 0xab, 0x48, 0xbe, 0x27, 0xe3, 0x84, 0xc7, 0x79,
	0xe9, 0x97, 0x81, 0xfb, 0xb6, 0xb1, 0x54, 0xb0, 0x2a, 0x98, 0xf2, 0xba, 0x1e, 0x97, 0xfc, 0x6a,
	0x9c, 0x17, 0x9c, 0xa8, 0xf1, 0x5e, 0xcb, 0xac, 0xe4, 0xb3, 0xf1, 0xeb, 0xb9, 0xa8, 0xed, 0x56,
	0xfa, 0x99, 0xab, 0xac, 0xe4, 0xac, 0x5a, 0x95, 0x7c, 0x5c, 0x94, 0xe2, 0x4a, 0xa4, 0x7c, 0xc6,
	0x83, 0x05, 0x61, 0xf2, 0xf8, 0x9a, 0xa5, 0xd6, 0x6b, 0xfc, 0xf8, 0xbf, 0xff, 0xe7, 0x56, 0xf4,
	0xf6, 0xc4, 0x80, 0xc7, 0xf9, 0x88, 0x47, 0xef, 0x1f, 0xf3, 0xfa, 0xb4, 0xa1, 0x0f, 0xf3, 0xec,
	0x52, 0xcc, 0x0e, 0xae, 0x9f, 0x26, 0xa3, 0xed, 0x3d, 0xab, 0xcf, 0xdc, 0x43, 0xc8, 0x19, 0xff,
	0x61, 0xe3, 0xe1, 0x00, 0xaa, 0x2a, 0x36, 0xdf, 0x42, 0x66, 0x9e, 0x89, 0xaa, 0xee, 0x31, 0x23,
	0x91, 0x7e, 0x33, 0x0d, 0xa5, 0xcc, 0xfc, 0x2e, 0xfa, 0xf9, 0x24, 0x49, 0x1c, 0xe9, 0xe8, 0x53,
	0xe7, 0x61, 0x5f, 0x2c, 0xd5, 0x3f, 0xe8, 0x21, 0x8c, 0xea, 0x29, 0x4f, 0xbb, 0x54, 0xfb, 0x62,
	0xaa, 0x9a, 0x12, 0x4a, 0xf5, 0xab, 0xe8, 0xbd, 0xe7, 0x45, 0xc2, 0x6a, 0xee, 0x6a, 0xdf, 0x72,
	0x9e, 0x05, 0x84, 0x34, 0xb0, 0xdd, 0x0f, 0x29, 0x1b, 0x2f, 0xa3, 0xd1, 0x31, 0xaf, 0x9f, 0x57,
	0xbc, 0xd4, 0xd2, 0x73, 0x9e, 0x25, 0xa3, 0x4d, 0xbf, 0x60, 0x3d, 0x40, 0x5a, 0xd8, 0xea, 0x65,
	0x94, 0x81, 0x79, 0xf4, 0x81, 0x2b, 0x3b, 0xe3, 0x31, 0x17, 0x57, 0x7c, 0xf4, 0xb0, 0xe3, 0x79,
	0xcd, 0x48, 0x33, 0x8f, 0x86, 0x60, 0xa6, 0xb8, 0x8e, 0x79, 0x3d, 0x49, 0xd3, 0x27, 0x3c, 0x4d,
	0xf3, 0xd7, 0x9a, 0x19, 0x91, 0x74, 0xfa, 0x04, 0x2d, 0x2e, 0x08, 0x29, 0x1b, 0xa7, 0xd1, 0x3b,
	0xb2, 0x1d, 0xb0, 0x78, 0xc1, 0x66, 0xfc, 0xf0, 0x72, 0x36, 0xba, 0x4b, 0xda, 0x48, 0x2b, 0x93,
	0x7a, 0xef, 0x75, 0x89, 0x8d, 0x46, 0x59, 0xfd, 0x21, 0x8d, 0x8e, 0x8c, 0x6a, 0xf4, 0xc4, 0x46,
	0xa3, 0x7c, 0x15, 0x42, 0x1a, 0x1d, 0x19, 0xd5, 0xe8, 0x89, 0xad, 0x46, 0xb2, 0xfe, 0x59, 0xbe,
	0xc0, 0x15, 0x6d, 0x24, 0x1e, 0x00, 0x1b, 0x09, 0x61, 0x94, 0x81, 0xbf, 0x89, 0x7e, 0xb1, 0x2e,
	0x9b, 0xa7, 0x35, 0x5f, 0xca, 0x64, 0x3f, 0x08, 0x94, 0x9d, 0x96, 0x4b, 0xf5, 0x9b, 0x7d, 0x88,
	0xd1, 0x7e, 0x92, 0x77, 0x6b, 0x27, 0x72, 0xaa, 0x1d, 0x20, 0x46, 0xfb, 0xba, 0x16, 0xb0, 0x76,
	0x22, 0xa7, 0xda, 0x01, 0x62, 0xb4, 0xaf, 0x4b, 0x0d, 0x6b, 0x27, 0x72, 0xaa, 0x1d, 0x20, 0x4a,
	0xfb, 0x8b, 0xe8, 0xdd, 0x63, 0x71, 0xc5, 0xd5, 0x2b, 0xd5, 0xc8, 0x47, 0xf7, 0xdd, 0x07, 0x5d,
	0xa9, 0xd4, 0xfc, 0x69, 0x37, 0xa0, 0xf4, 0x9e, 0x47, 0x7f, 0x3a, 0x49, 0x92, 0x27, 0xab, 0x2c,
	0x3e, 0x64, 0x65, 0x22, 0x93, 0x4c, 0x5e, 0x04, 0x4b, 0x28, 0xb5, 0xde, 0xef, 0x94, 0x1b, 0xa5,
	0x53, 0x9e, 0x86, 0x95, 0xba, 0x42, 0xaa, 0xd4, 0x97, 0x1b, 0xa5, 0xc7, 0xbc, 0x0e, 0x2b, 0x75,
	0x85, 0x54, 0xa9, 0x2f, 0x6f, 0x8b, 0xb5, 0xe9, 0xa8, 0x0e, 0xf4, 0x0c, 0x61, 0x74, 0x1f, 0x75,
	0x63, 0x46, 0x0a, 0x8a, 0xd5, 0x07, 0xfc, 0xbe, 0xd4, 0x14, 0x77, 0x57, 0x5f, 0xea, 0x30, 0xe1,
	0xbe, 0xd4, 0xc3, 0x94, 0xa5, 0xdf, 0x44, 0x6f, 0x4f, 0x92, 0xe4, 0x60, 0x55, 0x89, 0x8c, 0x57,
	0xd5, 0xe8, 0x8e, 0x5f, 0x3b, 0x46, 0x22, 0xb5, 0x7e, 0x12, 0x16, 0x5a, 0x45, 0x3c, 0x49, 0xd3,
	0x56, 0xdd, 0x3d, 0xd0, 0xdb, 0xda, 0x1a, 0xef, 0x77, 0xca, 0xcd, 0xb0, 0x7b, 0xcc, 0x6b, 0xf3,
	0x63, 0xd3, 0x21, 0x91, 0x22, 0x74, 0xc4, 0x74, 0xd8, 0xa5, 0x84, 0x52, 0xfd, 0x3a, 0xda, 0xb0,
	0x33, 0x21, 0xaa, 0xc5, 0x61, 0x33, 0x0f, 0x94, 0x03, 0xe7, 0xe8, 0xf3, 0x60, 0x6e, 0x5d, 0x50,
	0x9a, 0xfb, 0x62, 0x30, 0x6b, 0x0c, 0x9f, 0xe4, 0x03, 0x0d, 0x87, 0x41, 0x6a, 0xb8, 0x8b, 0x35,
	0x86, 0xad, 0xb2, 0xe8, 0x36, 0x1c, 0x06, 0xa9, 0xe1, 0x2e, 0xd6, 0x34, 0x33, 0xdd, 0x0a, 0x9f,
	0x66, 0x97, 0xb9, 0xd7, 0xcc, 0x2c, 0x09, 0x6d, 0x66, 0x8e, 0xd0, 0xb4, 0x88, 0x03, 0x56, 0xc7,
	0x73, 0x5b, 0xa1, 0xdb, 0x22, 0x7c, 0x31, 0x6d, 0x11, 0x94, 0xb0, 0xbc, 0x54, 0xf9, 0xe3, 0xf7,
	0xa2, 0x9e, 0x1f, 0xc9, 0xe9, 0x81, 0x52, 0xbf, 0x8d, 0x92, 0xe4, 0x20, 0xd0, 0x4b, 0x05, 0x94,
	0x95, 0x03, 0xf3, 0xea, 0x5f, 0x5f, 0x5c, 0x88, 0x84, 0xe6, 0xc0, 0x11, 0xc3, 0x1c, 0x78, 0x84,
	0x35, 0x8c, 0xc8, 0x9f, 0x0f, 0xdb, 0xe5, 0x04, 0x3a, 0x8c, 0xb8, 0x72, 0x38, 0x8c, 0xf8, 0x88,
	0xf1, 0xbc, 0x54, 0xc9, 0x4d, 0x79, 0xb2, 0x8a, 0x9b, 0xc2, 0xab, 0xf9, 0xd2, 0xf3, 0xbc, 0x00,
	0x41, 0x3d, 0x2f, 0x08, 0x29, 0x1b, 0x27, 0xd1, 0xcf, 0x8e, 0x79, 0x7d, 0xbc, 0x12, 0x69, 0xa2,
	0x26, 0x22, 0xa4, 0x39, 0xb4, 0x22, 0xa9, 0xf5, 0x6e, 0x87, 0xd4, 0x38, 0x49, 0xa6, 0xb2, 0x95,
	0xc8, 0x73, 0x92, 0x1c, 0x19, 0x75, 0x92, 0x3c, 0xb1, 0xd2, 0x78, 0x16, 0xbd, 0xeb, 0xfc, 0xfc,
	0xe2, 0xf1, 0xff, 0x5d, 0xe7, 0x8b, 0xe8, 0xdd, 0x93, 0x3c, 0x11, 0x97, 0xd7, 0xea, 0xc7, 0x6f,
	0xd9, 0xd2, 0x1f, 0x9f, 0x3d, 0x29, 0x1d, 0x48, 0x08, 0x60, 0xf4, 0x9e, 0x6b, 0x4b, 0xe7, 0xf3,
	0xbc, 0xac, 0x9f, 0x26, 0x9e, 0x5e, 0x4f, 0x4a, 0xf5, 0x12, 0xc0, 0x34, 0xb3, 0xe7, 0x59, 0xe5,
	0x69, 0x76, 0x9b, 0x19, 0x91, 0xd3, 0x66, 0x06, 0x10, 0xf3, 0x1a, 0x5a, 0xd9, 0x39, 0x66, 0x4b,
	0xfe, 0x4c, 0x2c, 0x85, 0x3f, 0x59, 0x44, 0x08, 0x7d, 0x0d, 0x31, 0x65, 0x79, 0xbb, 0x93, 0xa2,
	0x48, 0xaf, 0xcf, 0xc5, 0x2c, 0x3b, 0x93, 0x53, 0x7d, 0x30, 0x25, 0xf2, 0x00, 0xe8, 0xed, 0x12,
	0x46, 0x19, 0xc8, 0xa2, 0x8f, 0xbe, 0xbb, 0xbc, 0x14, 0xb1, 0x60, 0xe9, 0xd7, 0x2c, 0x4b, 0x52,
	0xde, 0x62, 0xa3, 0x1d, 0x47, 0x43, 0x80, 0x92, 0xa6, 0x76, 0x87, 0x81, 0xa6, 0xdc, 0x4c, 0xe3,
	0x3a, 0xd4, 0x6b, 0x84, 0xa0, 0xfb, 0x42, 0x08, 0x2d, 0x37, 0x4c, 0x19, 0xef, 0xe4, 0x8c, 0xc7,
	0x29, 0x13, 0xcb, 0x89, 0x5a, 0xfe, 0x79, 0x9a, 0xf0, 0xac, 0x16, 0xf5, 0xb5, 0xe7, 0x9d, 0x40,
	0x86, 0x7a, 0x27, 0x01, 0x4c, 0x59, 0x5a, 0x44, 0x1f, 0x9a, 0x74, 0xa8, 0xbc, 0x1e, 0xa4, 0x2c,
	0x5e, 0xa4, 0xa2, 0xaa, 0x47, 0x8f, 0x60, 0x62, 0x5d, 0x48, 0xda, 0xda, 0x19, 0xc4, 0x99, 0xd2,
	0xb3, 0x8a, 0x55, 0x49, 0x41, 0xe9, 0x21, 0x84, 0x96, 0x1e, 0xa6, 0xac, 0x31, 0xe6, 0x99, 0xb8,
	0xe2, 0x4d, 0x96, 0x8f, 0xde, 0xb0, 0xa5, 0xc8, 0x38, 0x1d, 0x63, 0x08, 0x02, 0xc7, 0x18, 0x40,
	0x99, 0xb6, 0xd7, 0xf8, 0x53, 0xd4, 0xd2, 0x0e, 0xf0, 0xba, 0xa0, 0xb1, 0xdd, 0x61, 0xa0, 0xb2,
	0xf7, 0xcf, 0xb7, 0xa2, 0xbb, 0xcd, 0xea, 0x03, 0x21, 0xce, 0x6b, 0x56, 0xaf, 0xaa, 0xd1, 0x18,
	0xac, 0x54, 0x04, 0x58, 0x69, 0x7c, 0xef, 0x26, 0xb8, 0x4a, 0xc2, 0x8f, 0xd1, 0x9d, 0x00, 0x76,
	0x21, 0x96, 0x7c, 0xf4, 0xc5, 0x10, 0x85, 0x92, 0x94, 0xd6, 0xbf, 0x1c, 0x0e, 0x5b, 0xb5, 0xea,
	0x36, 0xe2, 0x67, 0xf9, 0x8c, 0xd6, 0x2a, 0x41, 0x60, 0xad, 0x02, 0x4a, 0x99, 0xf9, 0x97, 0x5b,
	0xd1, 0x3d, 0xb5, 0xfc, 0xc5, 0xe2, 0x5a, 0xd4, 0x22, 0xcf, 0x78, 0x79, 0x92, 0x67, 0xf5, 0x7c,
	0x12, 0xd7, 0xe2, 0x8a, 0xab, 0xe6, 0xba, 0x47, 0xd7, 0xca, 0x82, 0xb0, 0xb4, 0xbd, 0x7f, 0x23,
	0xbe, 0x5d, 0x65, 0x7b, 0xcd, 0xca, 0xe4, 0xe2, 0x80, 0xb3, 0xec, 0x22, 0x97, 0x03, 0xb8, 0xbf,
	0xca, 0xe6, 0x89, 0xc1, 0x2a, 0x1b, 0x21, 0x4c, 0x9f, 0xac, 0x24, 0x53, 0xc1, 0x96, 0x79, 0x96,
	0x68, 0xe5, 0x9b, 0xf4, 0x51, 0x07, 0xa0, 0x7d, 0x32, 0x62, 0xac, 0xa9, 0x95, 0x2d, 0xd6, 0xfd,
	0x3e, 0xad, 0x03, 0xc2, 0xc0, 0xa9, 0x15, 0xc2, 0xda, 0xb9, 0xb1, 0x94, 0x1d, 0xbd, 0x29, 0x74,
	0x36, 0xee, 0xd1, 0x24, 0xb6, 0x42, 0x30, 0x37, 0xf6, 0xe4, 0x96, 0x7f, 0x67, 0x44, 0x3a, 0xe9,
	0x0f, 0x60, 0x9a, 0x5a, 0x39, 0xf4, 0xef, 0x7c, 0xc4, 0x9a, 0x6c, 0x49, 0x73, 0xcf, 0xf2, 0x99,
	0xc8, 0x80, 0x6b, 0xed, 0x8b, 0xe1, 0x64, 0xcb, 0x23, 0xac, 0xa9, 0xf2, 0x01, 0xcb, 0x32, 0x6e,
	0x4a, 0x9c, 0xcc, 0xfe, 0x6c, 0x29, 0x9c, 0x2a, 0xbb, 0x80, 0x55, 0x20, 0x8d, 0xe0, 0x3b, 0xb5,
	0xc8, 0x9e, 0x97, 0xb4, 0x40, 0x5c, 0x39, 0x2c, 0x10, 0x1f, 0xb1, 0x5a, 0x4b, 0x23, 0x9a, 0x14,
	0x05, 0x67, 0x69, 0xa8, 0xb5, 0x50, 0x06, 0xb6, 0x16, 0x84, 0x29, 0x4b, 0x3f, 0x44, 0x1f, 0x37,
	0xbd, 0x0c, 0x30, 0xb6, 0x0b, 0x3a, 0x23, 0x6c, 0xef, 0xb3, 0x81, 0xa4, 0xd5, 0x67, 0x35, 0xe2,
	0xc3, 0x39, 0x8f, 0x17, 0xda, 0xdc, 0x36, 0x4e, 0xb4, 0x85, 0xc0, 0x3e, 0x0b, 0x50, 0x66, 0x24,
	0xb2, 0x13, 0x62, 0x5b, 0xda, 0x09, 0x26, 0xd7, 0x33, 0xb6, 0x3b, 0x0c, 0x34, 0x4e, 0x83, 0x9b,
	0x9a, 0xb6, 0x59, 0x3c, 0xea, 0x48, 0xb2, 0xdd, 0x36, 0x76, 0x06, 0x71, 0xde, 0xb2, 0xba, 0x6a,
	0xf0, 0x53, 0x7e, 0x25, 0x62, 0x8e, 0x97, 0xd5, 0x2d, 0x20, 0xb8, 0xac, 0xee, 0x30, 0x56, 0x6e,
	0x5a, 0x99, 0x9c, 0x4e, 0x6a, 0x23, 0x8f, 0x82, 0x0a, 0xd6, 0x10, 0xcc, 0x0d, 0xe4, 0x94, 0xb1,
	0xbf, 0x8c, 0xfe, 0xf8, 0x80, 0x65, 0xaa, 0xaf, 0xfa, 0xc8, 0x73, 0x9c, 0x32, 0xd3, 0x49, 0x7d,
	0x8c, 0x05, 0x66, 0x9a, 0x2f, 0xab, 0xe2, 0x8a, 0x97, 0x4a, 0xc7, 0x1d, 0xdf, 0xd1, 0x33, 0x12,
	0x3a, 0xcd, 0x77, 0x84, 0xce, 0x34, 0xdf, 0x56, 0x08, 0xa6, 0xf9, 0x9e, 0xd6, 0x07, 0x3d, 0x84,
	0x55, 0xa6, 0x4d, 0xce, 0x9f, 0xb1, 0xaa, 0x5e, 0x77, 0x76, 0xa4, 0x4c, 0x01, 0x04, 0xcb, 0x14,
	0x72, 0xca, 0xd8, 0x34, 0xfa, 0x93, 0xa6, 0x05, 0x49, 0x77, 0xe0, 0x36, 0x68, 0x59, 0xda, 0x07,
	0xd8, 0x08, 0x89, 0xbc, 0x9e, 0xf9, 0x80, 0x65, 0xda, 0xa1, 0x0a, 0xac, 0x24, 0x66, 0x6b, 0x1f,
	0xea, 0x41, 0x0f, 0xe1, 0x38, 0xd9, 0xba, 0x2a, 0x83, 0x2d, 0x0c, 0x43, 0x01, 0x27, 0x1b, 0x70,
	0x26, 0x1f, 0x87, 0x2c, 0xd3, 0x55, 0xa2, 0xcd, 0xb8, 0xf9, 0xf0, 0xc5, 0x34, 0x1f, 0x94, 0xf0,
	0x16, 0x6f, 0x9e, 0x66, 0x57, 0xa2, 0xe6, 0x5f, 0x8b, 0xaa, 0xce, 0xcb, 0x6b, 0xbc, 0x78, 0xe3,
	0x20, 0xc1, 0xc5, 0x1b, 0x8f, 0x32, 0x03, 0xce, 0x19, 0xaf, 0xcc, 0x92, 0x6a, 0x55, 0xbd, 0xa6,
	0x23, 0x30, 0x91, 0xd3, 0x01, 0x07, 0x20, 0x66, 0x98, 0x7c, 0x9e, 0xbd, 0x12, 0x59, 0xa2, 0x64,
	0xf3, 0x3c, 0xf3, 0x17, 0x02, 0x3c, 0x29, 0x1d, 0x26, 0x09, 0x60, 0x06, 0xb2, 0xc3, 0x94, 0xb3,
	0xf2, 0x9c, 0xc7, 0xab, 0x52, 0xd4, 0xd7, 0xbf, 0x5d, 0xf1, 0x4a, 0xba, 0x78, 0xde, 0x40, 0x06,
	0x19, 0x3a, 0x90, 0x05, 0x30, 0x93, 0x83, 0x29, 0xaf, 0x59, 0x3c, 0xbf, 0x98, 0x8b, 0x32, 0x29,
	0x58, 0x59, 0x8f, 0xfc, 0xe5, 0x79, 0x47, 0x4a, 0x73, 0x40, 0x00, 0x33, 0x40, 0xda, 0xab, 0x76,
	0x66, 0x26, 0x7c, 0xc8, 0xcb, 0xda, 0x1b, 0x20, 0x43, 0x18, 0x1d, 0x20, 0xc3, 0xa4, 0x32, 0x79,
	0x1c, 0x45, 0x5a, 0x78, 0xf4, 0xa6, 0x18, 0x6d, 0xa0, 0x16, 0xa2, 0x3c, 0xa8, 0x1f, 0x36, 0xee,
	0x04, 0x65, 0xde, 0x28, 0xa1, 0x97, 0x46, 0x55, 0x67, 0x13, 0x5a, 0x73, 0x33, 0x40, 0x70, 0x94,
	0x70, 0x18, 0x33, 0xc6, 0x1e, 0xb0, 0xfa, 0x8c, 0x2f, 0xf3, 0x66, 0x97, 0x46, 0x13, 0x29, 0xf7,
	0xc7, 0xd8, 0x00, 0x45, 0xc7, 0xd8, 0x20, 0x68, 0x2d, 0x24, 0x5f, 0xcc, 0xf9, 0xb9, 0x5a, 0x6b,
	0x62, 0xe9, 0xb7, 0x6c, 0xa9, 0x48, 0xb5, 0xa9, 0x4f, 0x16, 0x92, 0x03, 0x20, 0x5c, 0x48, 0x0e,
	0xb2, 0xca, 0x70, 0x1d, 0xdd, 0xd6, 0x85, 0x60, 0x80, 0xc9, 0xaa, 0x9e, 0xcb, 0x92, 0x78, 0xf1,
	0x78, 0xf4, 0x19, 0x2a, 0x2c, 0xca, 0x49, 0xb3, 0x9f, 0x0f, 0x45, 0x95, 0xd5, 0x7f, 0x88, 0x3e,
	0xb1, 0x9b, 0x8a, 0xcf, 0x8d, 0xbe, 0x0c, 0xb6, 0x2a, 0x1f, 0x95, 0xb6, 0xc7, 0x37, 0xa0, 0x4d,
	0xa6, 0xdb, 0xe5, 0x98, 0x26, 0x10, 0xe6, 0x5c, 0x2c, 0x8b, 0xb4, 0x99, 0xef, 0xe1, 0x16, 0x4d,
	0x38, 0x9a, 0xe9, 0x0e, 0xd4, 0x5d, 0x41, 0x38, 0xe3, 0x45, 0x5e, 0xd6, 0xba, 0x17, 0x54, 0x15,
	0x8c, 0x56, 0x10, 0x08, 0x15, 0x5a, 0x41, 0x00, 0xa0, 0xd5, 0x7f, 0xeb, 0xd4, 0x34, 0x90, 0x1c,
	0xa7, 0x68, 0xff, 0x4d, 0x10, 0xd8, 0x7f, 0x03, 0xca, 0xf4, 0x23, 0xbe, 0xf4, 0xb4, 0xcc, 0x63,
	0x95, 0xaf, 0xdd, 0x4e, 0x25, 0x06, 0xa3, 0xfd, 0x48, 0x98, 0x6c, 0x57, 0x61, 0x59, 0x16, 0xcb,
	0x4e, 0x52, 0x63, 0xfe, 0x2a, 0xac, 0x2b, 0x05, 0xab, 0xb0, 0x3e, 0x00, 0x1c, 0x78, 0x25, 0xc1,
	0x41, 0x35, 0x04, 0xe9, 0x74, 0xe0, 0x2d, 0xca, 0x34, 0x84, 0x49, 0x62, 0x7e, 0xff, 0x7e, 0x2e,
	0x6a, 0x2e, 0x85, 0xaa, 0xf1, 0xed, 0xf8, 0x1b, 0x5f, 0x88, 0xa2, 0x0d, 0x21, 0x08, 0x1a, 0x7b,
	0x53, 0x9e, 0x0e, 0xb0, 0x17, 0xa0, 0xa8, 0xbd, 0x20, 0x68, 0xc5, 0x93, 0xf8, 0x00, 0x8d, 0x27,
	0xf1, 0x09, 0x18, 0x4f, 0x42, 0x21, 0xda, 0xb8, 0xad, 0xb7, 0x37, 0xf4, 0xbc, 0xfb, 0xe2, 0x3e,
	0x1c, 0x40, 0x99, 0x81, 0xc6, 0x7b, 0xad, 0x2f, 0xd8, 0xcc, 0x1b, 0x68, 0x28, 0x40, 0x07, 0x1a,
	0xc4, 0x18, 0xff, 0xed, 0x74, 0x55, 0xcd, 0x4f, 0xaa, 0xd9, 0x45, 0x6e, 0xda, 0xb2, 0xdb, 0x54,
	0x7d, 0x31, 0xf5, 0xdf, 0x28, 0x61, 0x76, 0x6a, 0x5a, 0x89, 0xf2, 0xf6, 0xef, 0xe2, 0xa7, 0x8c,
	0xab, 0x7f, 0xaf, 0x4b, 0x6c, 0x2a, 0x56, 0xfe, 0x3c, 0x89, 0xeb, 0x83, 0x92, 0xb3, 0x85, 0xc8,
	0x66, 0xdf, 0xf2, 0xd7, 0x95, 0x57, 0xb1, 0x80, 0xa0, 0x15, 0x0b, 0x21, 0x3b, 0xd5, 0x4f, 0xca,
	0x7c, 0x79, 0xf4, 0x26, 0xe6, 0x29, 0x48, 0x75, 0x2b, 0xc3, 0xa9, 0xb6, 0xc4, 0x66, 0x12, 0xf6,
	0x75, 0xfe, 0x4a, 0x4a, 0xbc, 0x49, 0x98, 0xfe, 0x95, 0x4e, 0xc2, 0x5a, 0x81, 0x35, 0x55, 0x68,
	0x7f, 0x53, 0x13, 0x6d, 0x32, 0x55, 0x70, 0xc4, 0x70, 0xaa, 0xe0, 0x11, 0xd6, 0x62, 0x8b, 0xae,
	0xb6, 0x13, 0x11, 0x9f, 0xe4, 0x09, 0xa7, 0x8b, 0x2d, 0xae, 0x1c, 0x2e, 0xb6, 0xf8, 0x88, 0xe9,
	0x99, 0xd7, 0x1d, 0xc3, 0x41, 0x1b, 0xf7, 0xab, 0xf6, 0xa6, 0x43, 0xfd, 0x87, 0x8b, 0xd1, 0x9e,
	0x39, 0x4c, 0x1a, 0x93, 0xeb, 0xbe, 0xa1, 0xd3, 0x64, 0x08, 0xa3, 0x26, 0xc3, 0x24, 0x1d, 0x7f,
	0x3a, 0x4d, 0x86, 0xb0, 0xae, 0xf1, 0x07, 0x9a, 0xac, 0x54, 0xa0, 0x03, 0x24, 0x5e, 0x3c, 0xfe,
	0xff, 0x2a, 0xda, 0xbf, 0x37, 0xfb, 0x01, 0x21, 0xbb, 0x68, 0x3d, 0x3e, 0x68, 0xfa, 0xcb, 0xe1,
	0xb0, 0xc9, 0x72, 0xa8, 0x1e, 0x48, 0x96, 0xff, 0x60, 0x55, 0xdb, 0x78, 0xc5, 0x21, 0xa3, 0x9f,
	0x0f, 0xaa, 0xb2, 0xc6, 0x3d, 0xfd, 0x62, 0x30, 0xeb, 0x76, 0xfb, 0xe2, 0xaa, 0x2d, 0x15, 0xdc,
	0xed, 0xdb, 0x40, 0xa8, 0xdb, 0x77, 0x19, 0x33, 0x7c, 0x35, 0x65, 0x2e, 0x85, 0xd9, 0xda, 0x04,
	0x8a, 0x3e, 0x75, 0x11, 0x3a, 0x7c, 0x61, 0xca, 0x98, 0x99, 0xf2, 0x94, 0xf7, 0x98, 0x41, 0x08,
	0x35, 0x83, 0x29, 0x53, 0x5c, 0x72, 0x68, 0xf6, 0x8c, 0xb8, 0xc5, 0x45, 0x01, 0x5a, 0x5c, 0x88,
	0x31, 0xb3, 0xed, 0xf3, 0xd7, 0xa2, 0x8e, 0xe7, 0xe7, 0xea, 0x68, 0xc2, 0xda, 0xa7, 0x70, 0x93,
	0x08, 0x19, 0x3a, 0xdb, 0x0e, 0x60, 0x56, 0x6f, 0xe2, 0xc9, 0xf4, 0xfa, 0x10, 0xe9, 0x4d, 0x20,
	0x06, 0x7b, 0x93, 0x00, 0x69, 0xcd, 0x35, 0xe3, 0xf9, 0x24, 0x49, 0xfc, 0xec, 0x81, 0x85, 0x20,
	0x4a, 0xc1, 0xb9, 0x26, 0x06, 0x1d, 0x7b, 0x53, 0x9e, 0x0e, 0xb0, 0x47, 0xa9, 0x80, 0x3d, 0x04,
	0x5a, 0x93, 0x75, 0xdf, 0xd4, 0x66, 0x4f, 0x11, 0xc1, 0xc9, 0x3a, 0x36, 0x70, 0x1a, 0xbd, 0x73,
	0xf4, 0x46, 0xcd, 0x81, 0xf2, 0xfa, 0x7b, 0x39, 0x3a, 0xbb, 0x2e, 0x83, 0x23, 0xa3, 0x2e, 0x83,
	0x27, 0x56, 0x1a, 0xff, 0xed, 0x56, 0xf4, 0x69, 0xbb, 0x6b, 0x38, 0x65, 0x22, 0xbd, 0x6e, 0x86,
	0x6d, 0xb5, 0xfc, 0xc6, 0x74, 0x0e, 0x7e, 0x89, 0x37, 0x19, 0x03, 0xb8, 0x34, 0xfc, 0xab, 0x1b,
	0x3e, 0x61, 0x85, 0xe0, 0x4f, 0x8a, 0x22, 0x17, 0x59, 0x7d, 0xba, 0x00, 0x7b, 0x48, 0xbe, 0x18,
	0x86, 0xe0, 0x7b, 0x84, 0x1b, 0x27, 0xef, 0x6a, 0x47, 0x71, 0xf2, 0xc4, 0xc0, 0x76, 0x3f, 0x64,
	0x85, 0xf9, 0x77, 0x25, 0xdf, 0x17, 0xc3, 0x30, 0x7f, 0xa4, 0x5a, 0xef, 0x11, 0xdb, 0x12, 0x3c,
	0x5d, 0x23, 0x08, 0xde, 0x23, 0xa6, 0x94, 0xe9, 0x7c, 0xcc, 0x0b, 0xe5, 0x66, 0xe3, 0x21, 0x7c,
	0xe9, 0x48, 0x5e, 0x1e, 0x0d, 0xc1, 0xac, 0xe8, 0xdf, 0xd3, 0x54, 0x8e, 0xc5, 0xcd, 0x91, 0x05,
	0x1a, 0x06, 0xbf, 0x16, 0xc2, 0xe8, 0x5f, 0x47, 0xde, 0x86, 0x16, 0x35, 0xa7, 0x18, 0x2c, 0xbd,
	0x0f, 0xd0, 0x29, 0x07, 0x57, 0xf5, 0x66, 0x1f, 0x62, 0xc5, 0x16, 0x87, 0x93, 0xec, 0x0a, 0x61,
	0x6c, 0x31, 0x55, 0xaa, 0xc3, 0xe6, 0xd7, 0xbf, 0xe3, 0x1e, 0xc3, 0x03, 0x70, 0xd8, 0xbc, 0xcf,
	0xb4, 0x2e, 0x7d, 0xc9, 0x32, 0xfd, 0xfe, 0x29, 0xc6, 0x77, 0xe9, 0x3d, 0x31, 0x70, 0xe9, 0x09,
	0x61, 0x56, 0xff, 0xf5, 0x80, 0x2c, 0xe5, 0xbc, 0xa9, 0x10, 0xb0, 0x17, 0x82, 0x21, 0xba, 0xfa,
	0x1f, 0xe2, 0xcc, 0x0b, 0x3c, 0xe5, 0x29, 0xb1, 0xb4, 0xe5, 0x17, 0x31, 0x32, 0xb3, 0xdd, 0x0f,
	0x59, 0xb3, 0x79, 0x5b, 0x88, 0x67, 0xf3, 0x3e, 0x01, 0x67, 0xf3, 0x14, 0xc2, 0x15, 0xae, 0x22,
	0x15, 0xbb, 0x2a, 0xdc, 0xc4, 0x2b, 0x6e, 0xf5, 0x32, 0x66, 0xc5, 0xe8, 0x7b, 0xce, 0x4a, 0xbb,
	0xbe, 0xdd, 0x76, 0xe8, 0x49, 0xe9, 0x8a, 0x11, 0x01, 0xac, 0x00, 0x4e, 0xd2, 0x96, 0x88, 0x17,
	0x88, 0x9a, 0xd3, 0x76, 0x3f, 0xd4, 0xee, 0x99, 0x4b, 0xc9, 0x33, 0x71, 0xc5, 0x4f, 0x44, 0x55,
	0x89, 0x3c, 0x53, 0xc1, 0x06, 0xfe, 0x9e, 0x39, 0x62, 0xc0, 0x9e, 0x39, 0xc6, 0xac, 0x03, 0x30,
	0x4d, 0x0a, 0x54, 0x25, 0xdf, 0xc5, 0x03, 0x96, 0xa9, 0xde, 0x7b, 0x5d, 0x62, 0xab, 0xef, 0x5c,
	0xbb, 0xf9, 0xcf, 0x4c, 0x54, 0x0d, 0xe9, 0x3b, 0x29, 0x03, 0xfb, 0x4e, 0x84, 0x99, 0x6e, 0xee,
	0xbc, 0x75, 0xeb, 0xa5, 0xec, 0x82, 0xf9, 0xdd, 0x1c, 0x91, 0xd3, 0x6e, 0x0e, 0x20, 0xa6, 0x9b,
	0x6b, 0x3b, 0x6e, 0x95, 0xc7, 0xd1, 0x3d, 0xdc, 0xab, 0x2b, 0x21, 0xed, 0xe6, 0x7c, 0xb9, 0x7d,
	0xee, 0x4a, 0xfd, 0xf4, 0x5d, 0xc1, 0x4b, 0xbd, 0xb6, 0xb0, 0x85, 0x4b, 0x75, 0x4d, 0xe0, 0x73,
	0x57, 0x04, 0x32, 0x36, 0x26, 0x49, 0xf2, 0x42, 0x94, 0xf5, 0x8a, 0xa5, 0xba, 0x6d, 0xf1, 0xd2,
	0xb3, 0x01, 0x08, 0x6a, 0x03, 0x42, 0x6e, 0x97, 0x47, 0xcc, 0xa0, 0x2e, 0x0f, 0x59, 0xda, 0x19,
	0xc4, 0x59, 0x5d, 0x5e, 0x4f, 0x86, 0x00, 0x01, 0xbb, 0xbc, 0x80, 0x8d, 0x66, 0xa5, 0xde, 0x17,
	0xe2, 0x95, 0x7a, 0x44, 0xc1, 0x95, 0x7a, 0x0c, 0xb6, 0xfb, 0xe7, 0xf2, 0xb5, 0x7c, 0xc2, 0xb2,
	0xea, 0x58, 0x5c, 0xd6, 0xa7, 0xe6, 0x34, 0xea, 0x08, 0xbc, 0xbb, 0x04, 0x02, 0xfb, 0xe7, 0x01,
	0xce, 0x8e, 0xd2, 0x55, 0xc9, 0x38, 0x8f, 0xf3, 0x32, 0xe0, 0x8e, 0x7b, 0x00, 0x8e, 0xd2, 0xf5,
	0x19, 0x2b, 0x32, 0xe9, 0x44, 0x64, 0x79, 0x29, 0xea, 0xeb, 0x63, 0x1a, 0x7b, 0xed, 0x49, 0x61,
	0x64, 0x92, 0x0b, 0x18, 0xbd, 0x93, 0x24, 0xe9, 0xd0, 0xeb, 0x49, 0xa9, 0x5e, 0x02, 0x98, 0x02,
	0x69, 0x76, 0xe6, 0x1c, 0xd5, 0xfe, 0xf6, 0xb2, 0x0f, 0xd0, 0x02, 0x41, 0x8c, 0x31, 0x20, 0x3b,
	0x95, 0x59, 0x97, 0x01, 0x0a, 0x50, 0x03, 0x88, 0xb1, 0x4a, 0xe6, 0x82, 0x2f, 0x8b, 0x6f, 0xf5,
	0x61, 0x7a, 0x5a, 0x32, 0xb6, 0x14, 0x96, 0x8c, 0x0b, 0x58, 0x1d, 0x94, 0x2d, 0xc0, 0x43, 0xbf,
	0x4f, 0xc0, 0x0e, 0x8a, 0x42, 0x96, 0xcf, 0x7b, 0xc2, 0x66, 0x22, 0x3e, 0x57, 0x47, 0xfc, 0xa9,
	0xcf, 0x6b, 0x09, 0xa1, 0xcf, 0xeb, 0xc8, 0x2d, 0xaf, 0x34, 0xac, 0xd4, 0x15, 0x42, 0xaf, 0x14,
	0x2a, 0x95, 0x0d, 0x33, 0xa8, 0xd4, 0x15, 0xc2, 0xe3, 0x58, 0x54, 0x69, 0xeb, 0x9d, 0xdb, 0x7a,
	0x91, 0x77, 0xee, 0xa9, 0xde, 0xec, 0x43, 0xda, 0xe8, 0x4f, 0xa7, 0x7c, 0x4e, 0x73, 0x72, 0x48,
	0x99, 0x02, 0x20, 0xfa, 0x13, 0x30, 0x56, 0x67, 0xd2, 0x6d, 0x80, 0x02, 0xb0, 0x33, 0xc1, 0x06,
	0x9a, 0x80, 0x41, 0x4b, 0x26, 0x5d, 0xbb, 0x8b, 0x65, 0x41, 0x03, 0x06, 0x29, 0x03, 0x03, 0x06,
	0x11, 0x66, 0xed, 0xca, 0x59, 0xe2, 0x75, 0x70, 0xfc, 0x4e, 0x47, 0x61, 0x38, 0xd1, 0xf1, 0xbb,
	0xc3, 0x40, 0x6b, 0x90, 0x19, 0x60, 0x0f, 0x50, 0xc1, 0x41, 0x06, 0x83, 0xd6, 0x2e, 0xe0, 0x00,
	0x7b, 0x01, 0x0a, 0xee, 0x02, 0x76, 0xe4, 0xef, 0x34, 0x7a, 0x47, 0x7a, 0x53, 0xf9, 0x72, 0x99,
	0x67, 0x6a, 0x31, 0xfe, 0x2e, 0xf1, 0xb4, 0x5a, 0x19, 0x75, 0x26, 0x3d, 0xb1, 0xe5, 0x9e, 0x06,
	0x35, 0x1e, 0x77, 0x6b, 0x3c, 0x86, 0x1a, 0x3f, 0x39, 0xe6, 0xf5, 0xb3, 0xe6, 0xee, 0x10, 0x7d,
	0x60, 0xfd, 0x45, 0x73, 0x42, 0x4b, 0xf5, 0x74, 0xef, 0x3b, 0x1a, 0xcc, 0xc9, 0x2b, 0x77, 0xb3,
	0xc8, 0xe2, 0x95, 0xc6, 0x8f, 0x27, 0x49, 0x02, 0x35, 0x7a, 0xe1, 0x7b, 0x52, 0xdb, 0x8b, 0xf5,
	0x81, 0xb1, 0x0f, 0xdc, 0x45, 0xa9, 0x65, 0x51, 0x5f, 0x9f, 0x54, 0x33, 0xb5, 0xca, 0x71, 0xc7,
	0x4a, 0x23, 0x2b, 0x79, 0x66, 0x6e, 0x6b, 0x50, 0x49, 0xdc, 0xf0, 0xcf, 0xb3, 0x54, 0xbc, 0xbe,
	0xb8, 0x2e, 0xc0, 0x79, 0x83, 0xa3, 0xac, 0x16, 0x25, 0x77, 0x34, 0x4c, 0x59, 0xcd, 0x74, 0xc2,
	0xff, 0x56, 0xad, 0xb5, 0x6b, 0x33, 0x87, 0x73, 0x91, 0x26, 0xb6, 0x15, 0x5c, 0x10, 0x3b, 0x40,
	0xbf, 0xfd, 0xa8, 0xa5, 0xfe, 0xf7, 0xea, 0xed, 0x42, 0xb9, 0xa0, 0x6f, 0x17, 0xa2, 0x3a, 0x8b,
	0x88, 0x45, 0x1b, 0xfa, 0x44, 0x01, 0xd2, 0xff, 0x39, 0x3a, 0x7a, 0x70, 0x53, 0x13, 0xdf, 0xa8,
	0xfd, 0x0f, 0x1e, 0x30, 0x81, 0x8b, 0x27, 0xa8, 0xec, 0x77, 0xd1, 0x87, 0xeb, 0x6c, 0xda, 0x05,
	0xe6, 0xf9, 0x7b, 0x18, 0xea, 0x54, 0xfd, 0x32, 0xba, 0xed, 0xe4, 0xd0, 0xd1, 0xfe, 0x59, 0xb8,
	0x24, 0x06, 0x1b, 0xf8, 0x4d, 0x74, 0xdb, 0x29, 0x08, 0xc7, 0xc0, 0x0d, 0xcb, 0xe1, 0x7b, 0xd5,
	0x05, 0xde, 0xa0, 0x44, 0xb7, 0x87, 0x34, 0xe8, 0xcd, 0xb7, 0x46, 0xcf, 0x55, 0x40, 0xea, 0xf0,
	0x14, 0x6e, 0x0d, 0x68, 0xc8, 0x9b, 0x6f, 0x8d, 0xfe, 0x2a, 0x7a, 0xa7, 0x99, 0x5c, 0x5d, 0xb0,
	0x37, 0x67, 0x72, 0x9e, 0xff, 0x60, 0x6f, 0x7d, 0x39, 0xd0, 0x4b, 0x75, 0x39, 0x90, 0x23, 0x6f,
	0x06, 0xea, 0x1e, 0x44, 0x75, 0x44, 0x7f, 0xa7, 0x7c, 0x66, 0x75, 0xaa, 0xce, 0xe8, 0xde, 0x22,
	0x0f, 0x7a, 0x44, 0x53, 0x20, 0xbd, 0x90, 0xd2, 0xff, 0x7b, 0xe5, 0xbb, 0xe8, 0xdf, 0xb4, 0xc3,
	0x0f, 0x9e, 0xb4, 0x80, 0xa6, 0x58, 0xfa, 0x18, 0xa5, 0x3c, 0x89, 0x7e, 0xd1, 0xe4, 0x49, 0x9f,
	0xcb, 0x8d, 0xf3, 0x25, 0x1f, 0x3d, 0x0c, 0xe4, 0xdb, 0x62, 0x9a, 0xf1, 0x79, 0x00, 0xa6, 0xac,
	0x5c, 0xab, 0x9d, 0x19, 0xe7, 0x77, 0x09, 0xaa, 0xcc, 0x7c, 0x89, 0x12, 0x0a, 0xd1, 0x26, 0x5e,
	0x6c, 0x38, 0x6d, 0xc2, 0xd5, 0x5c, 0x62, 0xca, 0x6b, 0x26, 0xd2, 0x29, 0xe7, 0xc5, 0x61, 0x9e,
	0x17, 0xa3, 0xdd, 0x1e, 0x85, 0x0d, 0xde, 0xec, 0x00, 0x0c, 0x21, 0x8d, 0xe2, 0xf5, 0x6e, 0xc4,
	0x26, 0xc2, 0x4c, 0xd0, 0xca, 0xea, 0x55, 0x25, 0x92, 0xeb, 0x1b, 0xa4, 0xe2, 0xcf, 0x07, 0x91,
	0xae, 0x7a, 0x9d, 0x96, 0x7f, 0xbf, 0x15, 0x6d, 0x21, 0xf8, 0x5b, 0xfe, 0xba, 0x39, 0x78, 0x7a,
	0xe3, 0xc4, 0xfc, 0xc5, 0x20, 0xd2, 0xd3, 0xdf, 0xb6, 0x89, 0x0d, 0x5c, 0x7e, 0xc9, 0x2a, 0xae,
	0x6f, 0x90, 0x86, 0xfd, 0x81, 0xd5, 0x22, 0xd5, 0xb6, 0x9e, 0xaf, 0x1c, 0xe8, 0xa7, 0xac, 0x5c,
	0xc8, 0x49, 0xf6, 0x41, 0x9e, 0xad, 0xaa, 0x83, 0xd5, 0xe5, 0xa5, 0xf2, 0x4b, 0xee, 0xef, 0x25,
	0xac, 0x5c, 0xbc, 0x9c, 0x89, 0xcb, 0xfa, 0x65, 0x73, 0x19, 0x98, 0xba, 0xc2, 0xa0, 0x91, 0x36,
	0x93, 0xaf, 0x4e, 0xc0, 0x18, 0x98, 0xf2, 0x74, 0xa8, 0x81, 0x29, 0x4f, 0xbb, 0x0d, 0x38, 0x80,
	0x31, 0x70, 0xcc, 0xeb, 0xa1, 0x06, 0xd4, 0x95, 0x04, 0x5d, 0x06, 0x1c, 0x40, 0xfb, 0xee, 0xfa,
	0xfc, 0x20, 0xb6, 0xb1, 0x49, 0x54, 0xe8, 0x73, 0x28, 0x96, 0x99, 0xad, 0x5e, 0x46, 0x59, 0x4a,
	0x95, 0x77, 0xf1, 0xbc, 0xe2, 0xe5, 0xb4, 0xbd, 0x5f, 0x4c, 0x6f, 0x47, 0x10, 0xef, 0x02, 0x51,
	0xd0, 0x77, 0xc7, 0x60, 0x55, 0x48, 0x6b, 0x53, 0x9e, 0x0e, 0xb0, 0x16, 0xa0, 0xa0, 0x27, 0x1d,
	0xb4, 0xb6, 0x32, 0xce, 0x0d, 0x34, 0x88, 0x9c, 0x9b, 0x90, 0xcd, 0x2f, 0x06, 0xb3, 0x55, 0x31,
	0x12, 0xaa, 0xcf, 0xd1, 0x7d, 0xc0, 0x91, 0x7c, 0xe6, 0xbc, 0xe0, 0xb1, 0x60, 0xe9, 0xd1, 0xe5,
	0x25, 0x8f, 0xcd, 0x95, 0x5a, 0x78, 0x68, 0xf7, 0xa2, 0x4b, 0xfa, 0x94, 0xfc, 0x63, 0x74, 0xf7,
	0xbc, 0x89, 0xd7, 0x5d, 0xa7, 0x44, 0x73, 0x6a, 0x08, 0xf7, 0x4e, 0xba, 0x76, 0xb2, 0xf4, 0xa4,
	0x6b, 0x0f, 0x5e, 0x15, 0xa3, 0x79, 0xf4, 0x91, 0x5a, 0x31, 0x5b, 0x23, 0x4d, 0xd4, 0xdb, 0x8b,
	0xc7, 0x68, 0x89, 0xdd, 0xa7, 0x02, 0x4b, 0xec, 0x14, 0x6b, 0xea, 0xb2, 0x09, 0x09, 0x41, 0xc4,
	0x08, 0x85, 0x13, 0x87, 0x2c, 0x7e, 0x31, 0x98, 0xad, 0x0a, 0xbd, 0x08, 0x7c, 0xc6, 0x4a, 0x7e,
	0xa6, 0x2f, 0x20, 0xac, 0xe6, 0xa2, 0xf0, 0xd6, 0x71, 0x7c, 0xb1, 0x7c, 0x82, 0xb8, 0x5c, 0x10,
	0x72, 0x17, 0x81, 0x89, 0x99, 0x47, 0x9d, 0x1a, 0xf4, 0x43, 0x64, 0x36, 0x11, 0xe2, 0x74, 0xcf,
	0xf2, 0xde, 0x94, 0xa7, 0x37, 0xb4, 0xd4, 0xb8, 0xad, 0xfd, 0x96, 0x0c, 0xa7, 0x2d, 0x49, 0x17,
	0xd5, 0x47, 0xc0, 0x36, 0x33, 0x42, 0xe8, 0x36, 0x33, 0xa6, 0xac, 0xc5, 0xb6, 0x1b, 0x56, 0x92,
	0x7c, 0xa2, 0xb7, 0x92, 0x14, 0xa4, 0x6c, 0xfc, 0xe7, 0xad, 0xe8, 0xa1, 0x15, 0x5b, 0xed, 0x71,
	0x07, 0x22, 0x4b, 0x44, 0xd6, 0x6c, 0xb6, 0x3e, 0x46, 0x6f, 0x70, 0xc7, 0x03, 0x32, 0x15, 0xbf,
	0xbe, 0xf1, 0x33, 0x2a, 0x51, 0xff, 0x71, 0x2b, 0x7a, 0xb0, 0x8e, 0x7d, 0x0b, 0xf0, 0xa3, 0x5f,
	0xdd, 0x40, 0xb9, 0x6e, 0xba, 0x8f, 0x6f, 0xfa, 0x88, 0x4a, 0xce, 0x7f, 0xdd, 0x8a, 0xb6, 0x9d,
	0x78, 0xb8, 0x50, 0x8a, 0xfe, 0xec, 0x06, 0xea, 0xd7, 0xad, 0xfc, 0xab, 0x9f, 0xf0, 0x94, 0xa9,
	0xbb, 0x07, 0xeb, 0xdd, 0xaf, 0x3f, 0x44, 0xa2, 0xd6, 0x2f, 0xc4, 0x57, 0x3f, 0xe1, 0x29, 0xb3,
	0x91, 0x2e, 0x6b, 0xf2, 0x22, 0x2f, 0x44, 0x8c, 0xc3, 0x97, 0x7d, 0x31, 0xdd, 0x48, 0xa7, 0x84,
	0x5e, 0x9b, 0xd9, 0x68, 0xf6, 0x17, 0xd4, 0x31, 0xd1, 0xe7, 0x45, 0x9a, 0xb3, 0x64, 0x1d, 0x3e,
	0xe4, 0x1e, 0xfc, 0x73, 0xa2, 0x86, 0x82, 0xd3, 0xcd, 0x2c, 0xfa, 0xc8, 0xd2, 0xa8, 0xde, 0x03,
	0xde, 0x2c, 0x91, 0xf9, 0x5e, 0x02, 0xa6, 0x80, 0x97, 0x10, 0x02, 0xf5, 0xa4, 0xcb, 0xb6, 0x77,
	0xae, 0x01, 0x39, 0x97, 0x0c, 0xdb, 0xb3, 0xa9, 0xce, 0xcc, 0xcc, 0xa3, 0x0f, 0x9a, 0xc8, 0x25,
	0x95, 0xf7, 0xa3, 0x37, 0x85, 0x28, 0x51, 0x48, 0x1c, 0x64, 0xe8, 0x90, 0x15, 0xc0, 0x54, 0x36,
	0xbe, 0x51, 0xbb, 0x0b, 0xe7, 0xb5, 0x88, 0x17, 0xa7, 0x82, 0x9d, 0xe5, 0xf9, 0x92, 0xee, 0x2e,
	0xd8, 0xd2, 0xce, 0x64, 0x37, 0x9b, 0x43, 0x1d, 0xca, 0x3c, 0x29, 0xdc, 0x1c, 0x72, 0x01, 0x95,
	0xc8, 0xdf, 0x46, 0xa3, 0xe6, 0x6d, 0x71, 0x54, 0xa3, 0x55, 0xf2, 0xc1, 0x49, 0x3d, 0x8a, 0x7e,
	0x36, 0x49, 0x12, 0x4d, 0x4e, 0xfc, 0x8b, 0x93, 0x6c, 0x51, 0xa7, 0x9a, 0xe6, 0xfe, 0xa5, 0x90,
	0x1a, 0x5b, 0x04, 0xef, 0x5f, 0xb2, 0xa4, 0xa6, 0x36, 0x74, 0x2c, 0x4e, 0xab, 0xf1, 0x3e, 0x8a,
	0xd4, 0x19, 0x98, 0x36, 0x75, 0x07, 0xab, 0x29, 0x0c, 0x10, 0xda, 0xee, 0x8b, 0xfb, 0xd6, 0xb5,
	0x94, 0x13, 0xa2, 0x9f, 0x38, 0xe5, 0xe5, 0xb2, 0x09, 0x32, 0x40, 0xfb, 0x98, 0x04, 0xea, 0x59,
	0x05, 0x7d, 0x7f, 0x5d, 0x18, 0x96, 0xe2, 0xed, 0x40, 0x79, 0xb9, 0x6a, 0x1f, 0x0e, 0xa0, 0xcc,
	0x2b, 0x7b, 0xc8, 0xb2, 0xb8, 0xcd, 0xb4, 0x65, 0x69, 0xc7, 0x3f, 0x33, 0x8b, 0xa8, 0xce, 0x3c,
	0x58, 0xa7, 0xde, 0xd4, 0x5c, 0xf6, 0x69, 0x76, 0x99, 0x1f, 0x5c, 0x4f, 0x45, 0x55, 0xa4, 0xec,
	0xfa, 0x69, 0x12, 0x38, 0xf5, 0x86, 0xd0, 0xf0, 0xa9, 0x37, 0x4c, 0xab, 0xbc, 0xc5, 0xd1, 0x9d,
	0xb5, 0x9f, 0x48, 0xf3, 0x17, 0xf2, 0x28, 0x6f, 0x5a, 0x4f, 0x4d, 0x1e, 0x43, 0xa5, 0x08, 0xf2,
	0xf8, 0x53, 0x8a, 0xf2, 0x3b, 0x75, 0x70, 0xe2, 0x42, 0x2c, 0xf9, 0x77, 0x57, 0xbc, 0x4c, 0x59,
	0x51, 0x70, 0x70, 0x6d, 0x87, 0x2b, 0xef, 0x54, 0x78, 0xac, 0xee, 0xa7, 0x55, 0x7d, 0xc3, 0xb4,
	0x64, 0x4b, 0x46, 0xef, 0xa7, 0x5d, 0xcb, 0xfa, 0x5e, 0x29, 0xab, 0x9f, 0x69, 0x74, 0x7d, 0x1a,
	0xea, 0x86, 0x06, 0xa9, 0x6b, 0x06, 0xdc, 0xa0, 0x3a, 0x5f, 0xdc, 0xf3, 0x86, 0xfe, 0xbc, 0x89,
	0xa1, 0x0d, 0xaa, 0xf3, 0xc5, 0x74, 0xbc, 0xa6, 0x84, 0xb5, 0x53, 0xa7, 0x7e, 0xd1, 0xed, 0x4f,
	0xfd, 0x09, 0xae, 0xf6, 0xa0, 0x0c, 0xdc, 0xa9, 0x43, 0x98, 0x75, 0x6a, 0x66, 0x9d, 0x80, 0x0b,
	0x36, 0xab, 0x68, 0xe5, 0xbb, 0x72, 0x78, 0x6a, 0xc6, 0x47, 0xf4, 0x36, 0xef, 0x7b, 0xcd, 0x48,
	0xbc, 0x16, 0x8a, 0xa5, 0x1f, 0xd2, 0x05, 0x88, 0xce, 0x72, 0x6f, 0xc2, 0xca, 0x9f, 0xa4, 0xf9,
	0xeb, 0x43, 0x56, 0x26, 0xea, 0xd2, 0x34, 0x39, 0xf9, 0xc5, 0x87, 0x24, 0x21, 0x06, 0xc3, 0xca,
	0x03, 0xa4, 0x89, 0x8e, 0x9d, 0x24, 0x09, 0x21, 0x46, 0x24, 0xaa, 0x87, 0x20, 0xb4, 0xc7, 0xc4,
	0x94, 0x7b, 0x1b, 0x09, 0xb5, 0x84, 0xa2, 0x7a, 0xa0, 0xb1, 0xdd, 0x61, 0xa0, 0x75, 0xa4, 0xa1,
	0x2f, 0x5b, 0x08, 0x81, 0x47, 0x1a, 0x3a, 0xcc, 0x98, 0x70, 0xc5, 0x35, 0x82, 0x62, 0x8b, 0x09,
	0x02, 0xc7, 0x1b, 0x40, 0xb5, 0x5b, 0x9a, 0xb6, 0xc8, 0xdf, 0xd2, 0xb4, 0x65, 0x60, 0x4b, 0xd3,
	0x15, 0x9b, 0x79, 0xb8, 0xbe, 0x01, 0x4e, 0xc7, 0x5a, 0xb6, 0xaa, 0xe1, 0x35, 0x71, 0x1e, 0x04,
	0x66, 0xc7, 0x01, 0x6e, 0x7d, 0x4e, 0x26, 0xf3, 0x0d, 0xf9, 0xe7, 0x64, 0x32, 0x60, 0x64, 0xab,
	0x97, 0xb1, 0x3a, 0x95, 0xd3, 0x92, 0x17, 0xac, 0xe4, 0xf6, 0x91, 0x5c, 0x74, 0x83, 0xbd, 0xc7,
	0xc0, 0x4e, 0x05, 0x61, 0x56, 0xfc, 0xa0, 0x2b, 0xa6, 0xf1, 0x83, 0xae, 0x1c, 0xc6, 0x0f, 0xfa,
	0x88, 0x7d, 0xd7, 0x75, 0x97, 0x76, 0x22, 0xc7, 0x77, 0x5d, 0x23, 0xed, 0x3a, 0x44, 0x57, 0x0e,
	0xfd, 0x76, 0x19, 0x6d, 0xc1, 0xbb, 0x3c, 0xbd, 0x12, 0xda, 0xee, 0x87, 0xac, 0x9a, 0xd0, 0xbf,
	0xeb, 0x54, 0xe0, 0x9a, 0xa0, 0x0c, 0xac, 0x09, 0x84, 0xb9, 0x9f, 0x36, 0x90, 0xbf, 0x1f, 0xb0,
	0x78, 0xb1, 0x2a, 0x82, 0x9f, 0x36, 0x70, 0x91, 0xd0, 0xa7, 0x0d, 0x7c, 0xca, 0x3a, 0xe9, 0xa3,
	0xa5, 0xeb, 0xb8, 0x49, 0xdc, 0x25, 0x43, 0x0c, 0x76, 0xc9, 0x01, 0xd2, 0x0a, 0xf9, 0xd0, 0x7e,
	0xd9, 0x79, 0xcc, 0x33, 0xde, 0x9e, 0x59, 0xa0, 0x17, 0xe0, 0x00, 0x0a, 0x86, 0x7c, 0x60, 0xd0,
	0x0a, 0xfe, 0xf4, 0x01, 0x1a, 0xfc, 0xe9, 0x13, 0x30, 0xf8, 0x93, 0x42, 0x56, 0x3c, 0x66, 0x8f,
	0x0d, 0x40, 0xc0, 0x78, 0xcc, 0xb0, 0x0d, 0xef, 0x00, 0xb5, 0x3a, 0x28, 0xdb, 0x79, 0xc4, 0xda,
	0x1c, 0x95, 0xdd, 0xee, 0x87, 0x8c, 0xe7, 0xaf, 0x84, 0xcd, 0x7d, 0x54, 0xdc, 0xb6, 0x03, 0x4e,
	0x45, 0x51, 0xaa, 0x67, 0x57, 0x7e, 0x74, 0xcc, 0xeb, 0x6f, 0xd4, 0xb7, 0x54, 0x3a, 0x62, 0x2e,
	0x3d, 0x00, 0x86, 0x49, 0x11, 0x66, 0x1d, 0x6c, 0x23, 0xdd, 0x32, 0xb3, 0xcf, 0x7d, 0x17, 0xb8,
	0x6c, 0xd6, 0xf6, 0xfe, 0xbd, 0x2e, 0x31, 0xfd, 0xc0, 0xc8, 0x93, 0x34, 0x67, 0xf5, 0x33, 0x76,
	0xcd, 0xcb, 0xe0, 0x07, 0x46, 0xd6, 0x48, 0xd7, 0x07, 0x46, 0x6c, 0xca, 0xf2, 0x52, 0xfa, 0xcc,
	0x20, 0x04, 0x7a, 0x29, 0x21, 0x33, 0xad, 0x97, 0x42, 0x2d, 0xed, 0x84, 0x3f, 0xf8, 0xe1, 0x1a,
	0xdb, 0x1d, 0x06, 0x5a, 0x5e, 0x4a, 0x5f, 0xb6, 0x10, 0x02, 0xbd, 0x94, 0x0e, 0x33, 0xca, 0x0b,
	0x64, 0xd5, 0xdc, 0xd9, 0x51, 0xd9, 0xa6, 0x8e, 0xa2, 0x87, 0xc0, 0x4a, 0x02, 0x94, 0xe3, 0x4a,
	0x76, 0x9b, 0x41, 0x48, 0xc0, 0x95, 0xc4, 0x66, 0x2c, 0x57, 0xd2, 0xb7, 0x84, 0x5d, 0x49, 0x60,
	0x6c, 0x77, 0x18, 0xe8, 0xb8, 0x92, 0xdd, 0xd9, 0x42, 0x48, 0xc0, 0x95, 0xc4, 0x66, 0x5a, 0x1f,
	0xa6, 0xa9, 0xc2, 0x16, 0x1a, 0x85, 0x5f, 0x92, 0x96, 0x09, 0xf9, 0x30, 0x04, 0x6b, 0x4f, 0x76,
	0xe6, 0xab, 0x2c, 0x01, 0xb6, 0xbc, 0x3e, 0x0c, 0x53, 0xe0, 0x64, 0x67, 0x08, 0x34, 0xab, 0xbf,
	0xcf, 0x8b, 0x8a, 0x97, 0xd6, 0xae, 0x13, 0x99, 0xeb, 0xba, 0x62, 0x3a, 0x9b, 0xa4, 0x84, 0xb9,
	0xf9, 0x6e, 0xfd, 0x9b, 0xff, 0x1d, 0x05, 0x4b, 0x42, 0x6f, 0xbe, 0x73, 0x84, 0x26, 0x99, 0x4f,
	0xb3, 0xce, 0x64, 0xfa, 0x62, 0x9a, 0x4c, 0x4a, 0x58, 0x1f, 0xa1, 0xb1, 0xf4, 0x92, 0x8f, 0xd0,
	0xb8, 0x4a, 0xef, 0x75, 0x89, 0xdb, 0x0b, 0xcb, 0x9c, 0xed, 0xca, 0xca, 0x5f, 0xdf, 0x73, 0xa5,
	0xe0, 0xc2, 0x32, 0x1f, 0x50, 0x7a, 0x67, 0xfa, 0x2e, 0x6b, 0x5f, 0x39, 0x38, 0xc4, 0x88, 0x2a,
	0xed, 0xd1, 0x10, 0xcc, 0xd4, 0x9c, 0x3e, 0x0d, 0x9a, 0x0a, 0xe6, 0xd7, 0x9c, 0x25, 0xa1, 0x35,
	0xe7, 0x08, 0x4d, 0x61, 0x34, 0x41, 0xef, 0x66, 0x2d, 0x6b, 0xea, 0x15, 0x86, 0x27, 0xa5, 0x85,
	0x41, 0x00, 0x73, 0x87, 0xa0, 0x1c, 0x3c, 0x9b, 0x14, 0xde, 0x26, 0xbb, 0x11, 0x6d, 0xfa, 0x36,
	0x42, 0x22, 0xe3, 0xd4, 0x1f, 0x96, 0x5c, 0x6f, 0xa2, 0x1f, 0x5c, 0x37, 0xda, 0xbc, 0xab, 0xf5,
	0x7c, 0x39, 0x75, 0xea, 0x01, 0x62, 0x7d, 0x4d, 0xe3, 0x70, 0x55, 0xd5, 0xf9, 0x52, 0xfc, 0xc8,
	0xbd, 0x8f, 0x70, 0x91, 0xaf, 0x69, 0x04, 0x40, 0xf8, 0x35, 0x8d, 0x20, 0xeb, 0xdd, 0x3c, 0x1d,
	0x32, 0x8e, 0x6e, 0x9e, 0xee, 0xb0, 0xbf, 0x77, 0x13, 0xbc, 0x4d, 0x42, 0x73, 0xb9, 0xd5, 0xb0,
	0x24, 0x74, 0xb2, 0x34, 0x09, 0x3d, 0xb8, 0x29, 0xfe, 0x29, 0x4f, 0x87, 0x15, 0x7f, 0x18, 0xa4,
	0xc5, 0xdf, 0xc5, 0x7a, 0xdf, 0xaa, 0x79, 0x21, 0x72, 0xbd, 0xfb, 0x06, 0x8e, 0xcc, 0x41, 0x26,
	0xf8, 0xad, 0x1a, 0x1f, 0x53, 0x96, 0x8a, 0xe8, 0xf6, 0x05, 0x5b, 0xf0, 0xaf, 0xf3, 0x76, 0xbe,
	0x77, 0x22, 0xe2, 0x83, 0xeb, 0x27, 0x79, 0x19, 0xfb, 0x4e, 0xef, 0x5a, 0xae, 0xa4, 0xe6, 0x31,
	0x3a, 0x60, 0x04, 0x41, 0xe3, 0xc8, 0x9f, 0xdb, 0x97, 0xd5, 0xe8, 0x8b, 0x15, 0xb6, 0x02, 0x67,
	0xf0, 0x5a, 0x82, 0x3a, 0xf2, 0x10, 0x32, 0xb7, 0x96, 0x7b, 0x5e, 0xfe, 0x31, 0x5b, 0x72, 0xf9,
	0xfa, 0x9f, 0xf2, 0x72, 0x09, 0x97, 0xb9, 0x21, 0x49, 0x6f, 0x49, 0xe9, 0x84, 0x95, 0xed, 0x7f,
	0xbd, 0x15, 0xdd, 0x07, 0xf3, 0x03, 0x27, 0x01, 0xfb, 0x7d, 0xb3, 0x09, 0x3f, 0x11, 0xbf, 0xbc,
	0xd9, 0x03, 0x66, 0x15, 0x68, 0x92, 0x24, 0x17, 0x22, 0x5e, 0x78, 0xdf, 0xf6, 0xa3, 0x11, 0xc6,
	0x00, 0xa2, 0xab, 0x40, 0x21, 0xce, 0x5c, 0xa7, 0xd7, 0xbc, 0xd4, 0xc8, 0x1e, 0x8a, 0x39, 0x0e,
	0x98, 0xfc, 0x7c, 0x28, 0x6a, 0xdd, 0x5c, 0x38, 0x49, 0xd3, 0x7e, 0xab, 0x41, 0x0e, 0xde, 0x5c,
	0x18, 0x42, 0x4d, 0xc1, 0x4e, 0x79, 0xda, 0x5f, 0xb0, 0x18, 0xa2, 0x05, 0x1b, 0xe2, 0xac, 0xaf,
	0xbb, 0xc9, 0xf7, 0xf7, 0x79, 0x56, 0xf2, 0x99, 0xda, 0x9e, 0xa5, 0x5f, 0x77, 0x73, 0xe5, 0xf0,
	0xeb, 0x6e, 0x3e, 0x62, 0x5f, 0xd3, 0xb6, 0x6a, 0x24, 0xed, 0xa7, 0x7f, 0xe8, 0x35, 0x6d, 0x1e,
	0x81, 0xaf, 0x69, 0x23, 0x90, 0xdd, 0x99, 0xb5, 0xa6, 0x5b, 0x2b, 0xb4, 0x33, 0x23, 0x0c, 0xee,
	0xcc, 0x00, 0x66, 0xcd, 0x51, 0x2e, 0xf4, 0x07, 0x3d, 0xd7, 0x27, 0x28, 0xc9, 0x1c, 0x85, 0x20,
	0x70, 0x8e, 0x02, 0x28, 0x6b, 0xd5, 0x88, 0x48, 0xf1, 0xaa, 0x11, 0xc4, 0xe0, 0xaa, 0x51, 0x80,
	0xb4, 0xee, 0xd4, 0x1a, 0x62, 0x32, 0x84, 0xc1, 0x8b, 0x97, 0xba, 0x4c, 0x2e, 0xa2, 0x0f, 0xd5,
	0x0e, 0x07, 0x2d, 0xce, 0x47, 0x74, 0x1b, 0x04, 0x16, 0xe8, 0xce, 0x20, 0x4e, 0x19, 0xfb, 0x27,
	0x1d, 0x14, 0xa7, 0x4e, 0x67, 0x56, 0x95, 0xc8, 0x66, 0x4f, 0x6b, 0xbe, 0xac, 0xf4, 0x6b, 0xb5,
	0x07, 0xcf, 0x51, 0x53, 0xf0, 0x8c, 0xff, 0xb0, 0xe2, 0x55, 0xbd, 0xb1, 0x3f, 0x98, 0xaf, 0x8a,
	0x3c, 0xab, 0x78, 0x1b, 0x59, 0x3e, 0x49, 0x53, 0x60, 0xfe, 0x4b, 0xd0, 0x3b, 0x84, 0x8d, 0x8f,
	0x07, 0xd2, 0xb6, 0x69, 0x7d, 0x76, 0xaf, 0xcf, 0x74, 0x08, 0xc3, 0xa6, 0xc3, 0xb4, 0x6d, 0xba,
	0xd9, 0x3a, 0xec, 0x35, 0x1d, 0xc2, 0xb0, 0xe9, 0x30, 0x6d, 0x4c, 0x1f, 0xec, 0xff, 0xf5, 0x78,
	0x96, 0xa7, 0x2c, 0x9b, 0xed, 0x7d, 0xf5, 0xb8, 0xae, 0xf7, 0xe2, 0x7c, 0xb9, 0xaf, 0xbe, 0xa6,
	0x1b, 0xe7, 0xe9, 0xbe, 0xfe, 0x92, 0x76, 0xb5, 0x6f, 0xe9, 0x7c, 0xf5, 0x47, 0x4a, 0xfc, 0xeb,
	0xff, 0x0d, 0x00, 0x00, 0xff, 0xff, 0x45, 0x00, 0x22, 0xc5, 0x9e, 0x7c, 0x00, 0x00,
}
