// Code generated by protoc-gen-go. DO NOT EDIT.
// source: apicenter-go/risk-control-api.proto

package apicentergo // import "golang.52tt.com/protocol/services/apicentergo"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type BusinessConf struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Desc                 string   `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	CallbackUrl          string   `protobuf:"bytes,3,opt,name=callback_url,json=callbackUrl,proto3" json:"callback_url,omitempty"`
	SourceId             uint32   `protobuf:"varint,4,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	OperaUser            string   `protobuf:"bytes,5,opt,name=opera_user,json=operaUser,proto3" json:"opera_user,omitempty"`
	BeginTime            string   `protobuf:"bytes,6,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              string   `protobuf:"bytes,7,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	BusinessId           uint32   `protobuf:"varint,8,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	SecretKey            string   `protobuf:"bytes,9,opt,name=secret_key,json=secretKey,proto3" json:"secret_key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BusinessConf) Reset()         { *m = BusinessConf{} }
func (m *BusinessConf) String() string { return proto.CompactTextString(m) }
func (*BusinessConf) ProtoMessage()    {}
func (*BusinessConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_risk_control_api_2d38a53c5d876a3f, []int{0}
}
func (m *BusinessConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BusinessConf.Unmarshal(m, b)
}
func (m *BusinessConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BusinessConf.Marshal(b, m, deterministic)
}
func (dst *BusinessConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BusinessConf.Merge(dst, src)
}
func (m *BusinessConf) XXX_Size() int {
	return xxx_messageInfo_BusinessConf.Size(m)
}
func (m *BusinessConf) XXX_DiscardUnknown() {
	xxx_messageInfo_BusinessConf.DiscardUnknown(m)
}

var xxx_messageInfo_BusinessConf proto.InternalMessageInfo

func (m *BusinessConf) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *BusinessConf) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *BusinessConf) GetCallbackUrl() string {
	if m != nil {
		return m.CallbackUrl
	}
	return ""
}

func (m *BusinessConf) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *BusinessConf) GetOperaUser() string {
	if m != nil {
		return m.OperaUser
	}
	return ""
}

func (m *BusinessConf) GetBeginTime() string {
	if m != nil {
		return m.BeginTime
	}
	return ""
}

func (m *BusinessConf) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *BusinessConf) GetBusinessId() uint32 {
	if m != nil {
		return m.BusinessId
	}
	return 0
}

func (m *BusinessConf) GetSecretKey() string {
	if m != nil {
		return m.SecretKey
	}
	return ""
}

// 添加业务
type AddBusinessReq struct {
	BusinessConf         *BusinessConf `protobuf:"bytes,1,opt,name=business_conf,json=businessConf,proto3" json:"business_conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *AddBusinessReq) Reset()         { *m = AddBusinessReq{} }
func (m *AddBusinessReq) String() string { return proto.CompactTextString(m) }
func (*AddBusinessReq) ProtoMessage()    {}
func (*AddBusinessReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_risk_control_api_2d38a53c5d876a3f, []int{1}
}
func (m *AddBusinessReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddBusinessReq.Unmarshal(m, b)
}
func (m *AddBusinessReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddBusinessReq.Marshal(b, m, deterministic)
}
func (dst *AddBusinessReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddBusinessReq.Merge(dst, src)
}
func (m *AddBusinessReq) XXX_Size() int {
	return xxx_messageInfo_AddBusinessReq.Size(m)
}
func (m *AddBusinessReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddBusinessReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddBusinessReq proto.InternalMessageInfo

func (m *AddBusinessReq) GetBusinessConf() *BusinessConf {
	if m != nil {
		return m.BusinessConf
	}
	return nil
}

type AddBusinessResp struct {
	BusinessConf         *BusinessConf `protobuf:"bytes,1,opt,name=business_conf,json=businessConf,proto3" json:"business_conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *AddBusinessResp) Reset()         { *m = AddBusinessResp{} }
func (m *AddBusinessResp) String() string { return proto.CompactTextString(m) }
func (*AddBusinessResp) ProtoMessage()    {}
func (*AddBusinessResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_risk_control_api_2d38a53c5d876a3f, []int{2}
}
func (m *AddBusinessResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddBusinessResp.Unmarshal(m, b)
}
func (m *AddBusinessResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddBusinessResp.Marshal(b, m, deterministic)
}
func (dst *AddBusinessResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddBusinessResp.Merge(dst, src)
}
func (m *AddBusinessResp) XXX_Size() int {
	return xxx_messageInfo_AddBusinessResp.Size(m)
}
func (m *AddBusinessResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddBusinessResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddBusinessResp proto.InternalMessageInfo

func (m *AddBusinessResp) GetBusinessConf() *BusinessConf {
	if m != nil {
		return m.BusinessConf
	}
	return nil
}

// 获取所有业务
type GetAllBusinessReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllBusinessReq) Reset()         { *m = GetAllBusinessReq{} }
func (m *GetAllBusinessReq) String() string { return proto.CompactTextString(m) }
func (*GetAllBusinessReq) ProtoMessage()    {}
func (*GetAllBusinessReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_risk_control_api_2d38a53c5d876a3f, []int{3}
}
func (m *GetAllBusinessReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllBusinessReq.Unmarshal(m, b)
}
func (m *GetAllBusinessReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllBusinessReq.Marshal(b, m, deterministic)
}
func (dst *GetAllBusinessReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllBusinessReq.Merge(dst, src)
}
func (m *GetAllBusinessReq) XXX_Size() int {
	return xxx_messageInfo_GetAllBusinessReq.Size(m)
}
func (m *GetAllBusinessReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllBusinessReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllBusinessReq proto.InternalMessageInfo

type GetAllBusinessResp struct {
	BusinessList         []*BusinessConf `protobuf:"bytes,1,rep,name=business_list,json=businessList,proto3" json:"business_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetAllBusinessResp) Reset()         { *m = GetAllBusinessResp{} }
func (m *GetAllBusinessResp) String() string { return proto.CompactTextString(m) }
func (*GetAllBusinessResp) ProtoMessage()    {}
func (*GetAllBusinessResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_risk_control_api_2d38a53c5d876a3f, []int{4}
}
func (m *GetAllBusinessResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllBusinessResp.Unmarshal(m, b)
}
func (m *GetAllBusinessResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllBusinessResp.Marshal(b, m, deterministic)
}
func (dst *GetAllBusinessResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllBusinessResp.Merge(dst, src)
}
func (m *GetAllBusinessResp) XXX_Size() int {
	return xxx_messageInfo_GetAllBusinessResp.Size(m)
}
func (m *GetAllBusinessResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllBusinessResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllBusinessResp proto.InternalMessageInfo

func (m *GetAllBusinessResp) GetBusinessList() []*BusinessConf {
	if m != nil {
		return m.BusinessList
	}
	return nil
}

// 根据id列表获取业务配置
type GetBusinessByIdsReq struct {
	IdList               []uint32 `protobuf:"varint,1,rep,packed,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBusinessByIdsReq) Reset()         { *m = GetBusinessByIdsReq{} }
func (m *GetBusinessByIdsReq) String() string { return proto.CompactTextString(m) }
func (*GetBusinessByIdsReq) ProtoMessage()    {}
func (*GetBusinessByIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_risk_control_api_2d38a53c5d876a3f, []int{5}
}
func (m *GetBusinessByIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBusinessByIdsReq.Unmarshal(m, b)
}
func (m *GetBusinessByIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBusinessByIdsReq.Marshal(b, m, deterministic)
}
func (dst *GetBusinessByIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBusinessByIdsReq.Merge(dst, src)
}
func (m *GetBusinessByIdsReq) XXX_Size() int {
	return xxx_messageInfo_GetBusinessByIdsReq.Size(m)
}
func (m *GetBusinessByIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBusinessByIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBusinessByIdsReq proto.InternalMessageInfo

func (m *GetBusinessByIdsReq) GetIdList() []uint32 {
	if m != nil {
		return m.IdList
	}
	return nil
}

type GetBusinessByIdsResp struct {
	BusinessList         []*BusinessConf `protobuf:"bytes,1,rep,name=business_list,json=businessList,proto3" json:"business_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetBusinessByIdsResp) Reset()         { *m = GetBusinessByIdsResp{} }
func (m *GetBusinessByIdsResp) String() string { return proto.CompactTextString(m) }
func (*GetBusinessByIdsResp) ProtoMessage()    {}
func (*GetBusinessByIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_risk_control_api_2d38a53c5d876a3f, []int{6}
}
func (m *GetBusinessByIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBusinessByIdsResp.Unmarshal(m, b)
}
func (m *GetBusinessByIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBusinessByIdsResp.Marshal(b, m, deterministic)
}
func (dst *GetBusinessByIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBusinessByIdsResp.Merge(dst, src)
}
func (m *GetBusinessByIdsResp) XXX_Size() int {
	return xxx_messageInfo_GetBusinessByIdsResp.Size(m)
}
func (m *GetBusinessByIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBusinessByIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBusinessByIdsResp proto.InternalMessageInfo

func (m *GetBusinessByIdsResp) GetBusinessList() []*BusinessConf {
	if m != nil {
		return m.BusinessList
	}
	return nil
}

type BusinessRiskControlConf struct {
	BusinessId           uint32   `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	BgId                 uint32   `protobuf:"varint,2,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
	HourCntLimit         uint64   `protobuf:"varint,3,opt,name=hour_cnt_limit,json=hourCntLimit,proto3" json:"hour_cnt_limit,omitempty"`
	DayCntLimit          uint64   `protobuf:"varint,4,opt,name=day_cnt_limit,json=dayCntLimit,proto3" json:"day_cnt_limit,omitempty"`
	SingleCntLimit       uint32   `protobuf:"varint,5,opt,name=single_cnt_limit,json=singleCntLimit,proto3" json:"single_cnt_limit,omitempty"`
	SingleValueLimit     uint32   `protobuf:"varint,6,opt,name=single_value_limit,json=singleValueLimit,proto3" json:"single_value_limit,omitempty"`
	HourTbeanValueLimit  uint64   `protobuf:"varint,7,opt,name=hour_tbean_value_limit,json=hourTbeanValueLimit,proto3" json:"hour_tbean_value_limit,omitempty"`
	DayTbeanValueLimit   uint64   `protobuf:"varint,8,opt,name=day_tbean_value_limit,json=dayTbeanValueLimit,proto3" json:"day_tbean_value_limit,omitempty"`
	OperaUser            string   `protobuf:"bytes,9,opt,name=opera_user,json=operaUser,proto3" json:"opera_user,omitempty"`
	Id                   uint32   `protobuf:"varint,10,opt,name=id,proto3" json:"id,omitempty"`
	RestHourTbeanValue   int64    `protobuf:"varint,11,opt,name=rest_hour_tbean_value,json=restHourTbeanValue,proto3" json:"rest_hour_tbean_value,omitempty"`
	RestDayTbeanValue    int64    `protobuf:"varint,12,opt,name=rest_day_tbean_value,json=restDayTbeanValue,proto3" json:"rest_day_tbean_value,omitempty"`
	RestHourCnt          int64    `protobuf:"varint,13,opt,name=rest_hour_cnt,json=restHourCnt,proto3" json:"rest_hour_cnt,omitempty"`
	RestDayCnt           int64    `protobuf:"varint,14,opt,name=rest_day_cnt,json=restDayCnt,proto3" json:"rest_day_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BusinessRiskControlConf) Reset()         { *m = BusinessRiskControlConf{} }
func (m *BusinessRiskControlConf) String() string { return proto.CompactTextString(m) }
func (*BusinessRiskControlConf) ProtoMessage()    {}
func (*BusinessRiskControlConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_risk_control_api_2d38a53c5d876a3f, []int{7}
}
func (m *BusinessRiskControlConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BusinessRiskControlConf.Unmarshal(m, b)
}
func (m *BusinessRiskControlConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BusinessRiskControlConf.Marshal(b, m, deterministic)
}
func (dst *BusinessRiskControlConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BusinessRiskControlConf.Merge(dst, src)
}
func (m *BusinessRiskControlConf) XXX_Size() int {
	return xxx_messageInfo_BusinessRiskControlConf.Size(m)
}
func (m *BusinessRiskControlConf) XXX_DiscardUnknown() {
	xxx_messageInfo_BusinessRiskControlConf.DiscardUnknown(m)
}

var xxx_messageInfo_BusinessRiskControlConf proto.InternalMessageInfo

func (m *BusinessRiskControlConf) GetBusinessId() uint32 {
	if m != nil {
		return m.BusinessId
	}
	return 0
}

func (m *BusinessRiskControlConf) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

func (m *BusinessRiskControlConf) GetHourCntLimit() uint64 {
	if m != nil {
		return m.HourCntLimit
	}
	return 0
}

func (m *BusinessRiskControlConf) GetDayCntLimit() uint64 {
	if m != nil {
		return m.DayCntLimit
	}
	return 0
}

func (m *BusinessRiskControlConf) GetSingleCntLimit() uint32 {
	if m != nil {
		return m.SingleCntLimit
	}
	return 0
}

func (m *BusinessRiskControlConf) GetSingleValueLimit() uint32 {
	if m != nil {
		return m.SingleValueLimit
	}
	return 0
}

func (m *BusinessRiskControlConf) GetHourTbeanValueLimit() uint64 {
	if m != nil {
		return m.HourTbeanValueLimit
	}
	return 0
}

func (m *BusinessRiskControlConf) GetDayTbeanValueLimit() uint64 {
	if m != nil {
		return m.DayTbeanValueLimit
	}
	return 0
}

func (m *BusinessRiskControlConf) GetOperaUser() string {
	if m != nil {
		return m.OperaUser
	}
	return ""
}

func (m *BusinessRiskControlConf) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *BusinessRiskControlConf) GetRestHourTbeanValue() int64 {
	if m != nil {
		return m.RestHourTbeanValue
	}
	return 0
}

func (m *BusinessRiskControlConf) GetRestDayTbeanValue() int64 {
	if m != nil {
		return m.RestDayTbeanValue
	}
	return 0
}

func (m *BusinessRiskControlConf) GetRestHourCnt() int64 {
	if m != nil {
		return m.RestHourCnt
	}
	return 0
}

func (m *BusinessRiskControlConf) GetRestDayCnt() int64 {
	if m != nil {
		return m.RestDayCnt
	}
	return 0
}

type BusinessRiskControlConfList struct {
	BusinessRiskConfList []*BusinessRiskControlConf `protobuf:"bytes,1,rep,name=business_risk_conf_list,json=businessRiskConfList,proto3" json:"business_risk_conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *BusinessRiskControlConfList) Reset()         { *m = BusinessRiskControlConfList{} }
func (m *BusinessRiskControlConfList) String() string { return proto.CompactTextString(m) }
func (*BusinessRiskControlConfList) ProtoMessage()    {}
func (*BusinessRiskControlConfList) Descriptor() ([]byte, []int) {
	return fileDescriptor_risk_control_api_2d38a53c5d876a3f, []int{8}
}
func (m *BusinessRiskControlConfList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BusinessRiskControlConfList.Unmarshal(m, b)
}
func (m *BusinessRiskControlConfList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BusinessRiskControlConfList.Marshal(b, m, deterministic)
}
func (dst *BusinessRiskControlConfList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BusinessRiskControlConfList.Merge(dst, src)
}
func (m *BusinessRiskControlConfList) XXX_Size() int {
	return xxx_messageInfo_BusinessRiskControlConfList.Size(m)
}
func (m *BusinessRiskControlConfList) XXX_DiscardUnknown() {
	xxx_messageInfo_BusinessRiskControlConfList.DiscardUnknown(m)
}

var xxx_messageInfo_BusinessRiskControlConfList proto.InternalMessageInfo

func (m *BusinessRiskControlConfList) GetBusinessRiskConfList() []*BusinessRiskControlConf {
	if m != nil {
		return m.BusinessRiskConfList
	}
	return nil
}

// 添加背包风控业务配置
type AddBusinessRiskControlConfReq struct {
	BusinessRiskConf     *BusinessRiskControlConf `protobuf:"bytes,1,opt,name=business_risk_conf,json=businessRiskConf,proto3" json:"business_risk_conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *AddBusinessRiskControlConfReq) Reset()         { *m = AddBusinessRiskControlConfReq{} }
func (m *AddBusinessRiskControlConfReq) String() string { return proto.CompactTextString(m) }
func (*AddBusinessRiskControlConfReq) ProtoMessage()    {}
func (*AddBusinessRiskControlConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_risk_control_api_2d38a53c5d876a3f, []int{9}
}
func (m *AddBusinessRiskControlConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddBusinessRiskControlConfReq.Unmarshal(m, b)
}
func (m *AddBusinessRiskControlConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddBusinessRiskControlConfReq.Marshal(b, m, deterministic)
}
func (dst *AddBusinessRiskControlConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddBusinessRiskControlConfReq.Merge(dst, src)
}
func (m *AddBusinessRiskControlConfReq) XXX_Size() int {
	return xxx_messageInfo_AddBusinessRiskControlConfReq.Size(m)
}
func (m *AddBusinessRiskControlConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddBusinessRiskControlConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddBusinessRiskControlConfReq proto.InternalMessageInfo

func (m *AddBusinessRiskControlConfReq) GetBusinessRiskConf() *BusinessRiskControlConf {
	if m != nil {
		return m.BusinessRiskConf
	}
	return nil
}

type AddBusinessRiskControlConfResp struct {
	BusinessRiskConf     *BusinessRiskControlConf `protobuf:"bytes,1,opt,name=business_risk_conf,json=businessRiskConf,proto3" json:"business_risk_conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *AddBusinessRiskControlConfResp) Reset()         { *m = AddBusinessRiskControlConfResp{} }
func (m *AddBusinessRiskControlConfResp) String() string { return proto.CompactTextString(m) }
func (*AddBusinessRiskControlConfResp) ProtoMessage()    {}
func (*AddBusinessRiskControlConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_risk_control_api_2d38a53c5d876a3f, []int{10}
}
func (m *AddBusinessRiskControlConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddBusinessRiskControlConfResp.Unmarshal(m, b)
}
func (m *AddBusinessRiskControlConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddBusinessRiskControlConfResp.Marshal(b, m, deterministic)
}
func (dst *AddBusinessRiskControlConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddBusinessRiskControlConfResp.Merge(dst, src)
}
func (m *AddBusinessRiskControlConfResp) XXX_Size() int {
	return xxx_messageInfo_AddBusinessRiskControlConfResp.Size(m)
}
func (m *AddBusinessRiskControlConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddBusinessRiskControlConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddBusinessRiskControlConfResp proto.InternalMessageInfo

func (m *AddBusinessRiskControlConfResp) GetBusinessRiskConf() *BusinessRiskControlConf {
	if m != nil {
		return m.BusinessRiskConf
	}
	return nil
}

type ModBusinessRiskControlConfReq struct {
	BusinessRiskConf     *BusinessRiskControlConf `protobuf:"bytes,1,opt,name=business_risk_conf,json=businessRiskConf,proto3" json:"business_risk_conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *ModBusinessRiskControlConfReq) Reset()         { *m = ModBusinessRiskControlConfReq{} }
func (m *ModBusinessRiskControlConfReq) String() string { return proto.CompactTextString(m) }
func (*ModBusinessRiskControlConfReq) ProtoMessage()    {}
func (*ModBusinessRiskControlConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_risk_control_api_2d38a53c5d876a3f, []int{11}
}
func (m *ModBusinessRiskControlConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModBusinessRiskControlConfReq.Unmarshal(m, b)
}
func (m *ModBusinessRiskControlConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModBusinessRiskControlConfReq.Marshal(b, m, deterministic)
}
func (dst *ModBusinessRiskControlConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModBusinessRiskControlConfReq.Merge(dst, src)
}
func (m *ModBusinessRiskControlConfReq) XXX_Size() int {
	return xxx_messageInfo_ModBusinessRiskControlConfReq.Size(m)
}
func (m *ModBusinessRiskControlConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModBusinessRiskControlConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModBusinessRiskControlConfReq proto.InternalMessageInfo

func (m *ModBusinessRiskControlConfReq) GetBusinessRiskConf() *BusinessRiskControlConf {
	if m != nil {
		return m.BusinessRiskConf
	}
	return nil
}

type ModBusinessRiskControlConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModBusinessRiskControlConfResp) Reset()         { *m = ModBusinessRiskControlConfResp{} }
func (m *ModBusinessRiskControlConfResp) String() string { return proto.CompactTextString(m) }
func (*ModBusinessRiskControlConfResp) ProtoMessage()    {}
func (*ModBusinessRiskControlConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_risk_control_api_2d38a53c5d876a3f, []int{12}
}
func (m *ModBusinessRiskControlConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModBusinessRiskControlConfResp.Unmarshal(m, b)
}
func (m *ModBusinessRiskControlConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModBusinessRiskControlConfResp.Marshal(b, m, deterministic)
}
func (dst *ModBusinessRiskControlConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModBusinessRiskControlConfResp.Merge(dst, src)
}
func (m *ModBusinessRiskControlConfResp) XXX_Size() int {
	return xxx_messageInfo_ModBusinessRiskControlConfResp.Size(m)
}
func (m *ModBusinessRiskControlConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ModBusinessRiskControlConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_ModBusinessRiskControlConfResp proto.InternalMessageInfo

// 获取所有的背包风控业务配置
type GetBusinessRiskControlConfReq struct {
	BusinessId           uint32   `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	BusinessIdList       []uint32 `protobuf:"varint,2,rep,packed,name=business_id_list,json=businessIdList,proto3" json:"business_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBusinessRiskControlConfReq) Reset()         { *m = GetBusinessRiskControlConfReq{} }
func (m *GetBusinessRiskControlConfReq) String() string { return proto.CompactTextString(m) }
func (*GetBusinessRiskControlConfReq) ProtoMessage()    {}
func (*GetBusinessRiskControlConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_risk_control_api_2d38a53c5d876a3f, []int{13}
}
func (m *GetBusinessRiskControlConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBusinessRiskControlConfReq.Unmarshal(m, b)
}
func (m *GetBusinessRiskControlConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBusinessRiskControlConfReq.Marshal(b, m, deterministic)
}
func (dst *GetBusinessRiskControlConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBusinessRiskControlConfReq.Merge(dst, src)
}
func (m *GetBusinessRiskControlConfReq) XXX_Size() int {
	return xxx_messageInfo_GetBusinessRiskControlConfReq.Size(m)
}
func (m *GetBusinessRiskControlConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBusinessRiskControlConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBusinessRiskControlConfReq proto.InternalMessageInfo

func (m *GetBusinessRiskControlConfReq) GetBusinessId() uint32 {
	if m != nil {
		return m.BusinessId
	}
	return 0
}

func (m *GetBusinessRiskControlConfReq) GetBusinessIdList() []uint32 {
	if m != nil {
		return m.BusinessIdList
	}
	return nil
}

type GetBusinessRiskControlConfResp struct {
	BusinessRiskConfList   []*BusinessRiskControlConf              `protobuf:"bytes,1,rep,name=business_risk_conf_list,json=businessRiskConfList,proto3" json:"business_risk_conf_list,omitempty"`
	BusinessToRiskConfList map[uint32]*BusinessRiskControlConfList `protobuf:"bytes,2,rep,name=business_to_risk_conf_list,json=businessToRiskConfList,proto3" json:"business_to_risk_conf_list,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral   struct{}                                `json:"-"`
	XXX_unrecognized       []byte                                  `json:"-"`
	XXX_sizecache          int32                                   `json:"-"`
}

func (m *GetBusinessRiskControlConfResp) Reset()         { *m = GetBusinessRiskControlConfResp{} }
func (m *GetBusinessRiskControlConfResp) String() string { return proto.CompactTextString(m) }
func (*GetBusinessRiskControlConfResp) ProtoMessage()    {}
func (*GetBusinessRiskControlConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_risk_control_api_2d38a53c5d876a3f, []int{14}
}
func (m *GetBusinessRiskControlConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBusinessRiskControlConfResp.Unmarshal(m, b)
}
func (m *GetBusinessRiskControlConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBusinessRiskControlConfResp.Marshal(b, m, deterministic)
}
func (dst *GetBusinessRiskControlConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBusinessRiskControlConfResp.Merge(dst, src)
}
func (m *GetBusinessRiskControlConfResp) XXX_Size() int {
	return xxx_messageInfo_GetBusinessRiskControlConfResp.Size(m)
}
func (m *GetBusinessRiskControlConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBusinessRiskControlConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBusinessRiskControlConfResp proto.InternalMessageInfo

func (m *GetBusinessRiskControlConfResp) GetBusinessRiskConfList() []*BusinessRiskControlConf {
	if m != nil {
		return m.BusinessRiskConfList
	}
	return nil
}

func (m *GetBusinessRiskControlConfResp) GetBusinessToRiskConfList() map[uint32]*BusinessRiskControlConfList {
	if m != nil {
		return m.BusinessToRiskConfList
	}
	return nil
}

func init() {
	proto.RegisterType((*BusinessConf)(nil), "apicentergo.BusinessConf")
	proto.RegisterType((*AddBusinessReq)(nil), "apicentergo.AddBusinessReq")
	proto.RegisterType((*AddBusinessResp)(nil), "apicentergo.AddBusinessResp")
	proto.RegisterType((*GetAllBusinessReq)(nil), "apicentergo.GetAllBusinessReq")
	proto.RegisterType((*GetAllBusinessResp)(nil), "apicentergo.GetAllBusinessResp")
	proto.RegisterType((*GetBusinessByIdsReq)(nil), "apicentergo.GetBusinessByIdsReq")
	proto.RegisterType((*GetBusinessByIdsResp)(nil), "apicentergo.GetBusinessByIdsResp")
	proto.RegisterType((*BusinessRiskControlConf)(nil), "apicentergo.BusinessRiskControlConf")
	proto.RegisterType((*BusinessRiskControlConfList)(nil), "apicentergo.BusinessRiskControlConfList")
	proto.RegisterType((*AddBusinessRiskControlConfReq)(nil), "apicentergo.AddBusinessRiskControlConfReq")
	proto.RegisterType((*AddBusinessRiskControlConfResp)(nil), "apicentergo.AddBusinessRiskControlConfResp")
	proto.RegisterType((*ModBusinessRiskControlConfReq)(nil), "apicentergo.ModBusinessRiskControlConfReq")
	proto.RegisterType((*ModBusinessRiskControlConfResp)(nil), "apicentergo.ModBusinessRiskControlConfResp")
	proto.RegisterType((*GetBusinessRiskControlConfReq)(nil), "apicentergo.GetBusinessRiskControlConfReq")
	proto.RegisterType((*GetBusinessRiskControlConfResp)(nil), "apicentergo.GetBusinessRiskControlConfResp")
	proto.RegisterMapType((map[uint32]*BusinessRiskControlConfList)(nil), "apicentergo.GetBusinessRiskControlConfResp.BusinessToRiskConfListEntry")
}

func init() {
	proto.RegisterFile("apicenter-go/risk-control-api.proto", fileDescriptor_risk_control_api_2d38a53c5d876a3f)
}

var fileDescriptor_risk_control_api_2d38a53c5d876a3f = []byte{
	// 808 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x56, 0x5d, 0x8f, 0xdb, 0x44,
	0x14, 0x95, 0xbd, 0xd9, 0xdd, 0xe4, 0xe6, 0x83, 0x74, 0xb2, 0x74, 0xdd, 0x56, 0x59, 0x82, 0xe9,
	0x43, 0x1e, 0x48, 0x22, 0x5a, 0x21, 0x21, 0x1e, 0x2a, 0x75, 0x03, 0x5a, 0x56, 0x14, 0x09, 0xac,
	0xb4, 0x0f, 0xf0, 0x60, 0xd9, 0x9e, 0xa9, 0x19, 0xe2, 0xcc, 0xb8, 0x9e, 0x49, 0xa5, 0xf0, 0xca,
	0x0f, 0xe0, 0xbf, 0xf0, 0xeb, 0x78, 0x44, 0x73, 0x9d, 0x38, 0x8e, 0x13, 0xc2, 0x4a, 0xb0, 0xbc,
	0x79, 0xef, 0x39, 0xf7, 0x9c, 0xe3, 0xeb, 0x3b, 0xb3, 0x81, 0x4f, 0x82, 0x94, 0x47, 0x4c, 0x68,
	0x96, 0x8d, 0x62, 0x39, 0xc9, 0xb8, 0x9a, 0x8f, 0x22, 0x29, 0x74, 0x26, 0x93, 0x51, 0x90, 0xf2,
	0x71, 0x9a, 0x49, 0x2d, 0x49, 0xb3, 0x20, 0xc5, 0xd2, 0xfd, 0xdd, 0x86, 0xd6, 0xf5, 0x52, 0x71,
	0xc1, 0x94, 0x9a, 0x4a, 0xf1, 0x96, 0x10, 0xa8, 0x89, 0x60, 0xc1, 0x1c, 0x6b, 0x60, 0x0d, 0x1b,
	0x1e, 0x3e, 0x9b, 0x1a, 0x65, 0x2a, 0x72, 0xec, 0xbc, 0x66, 0x9e, 0xc9, 0xc7, 0xd0, 0x8a, 0x82,
	0x24, 0x09, 0x83, 0x68, 0xee, 0x2f, 0xb3, 0xc4, 0x39, 0x41, 0xac, 0xb9, 0xa9, 0xbd, 0xce, 0x12,
	0xf2, 0x04, 0x1a, 0x4a, 0x2e, 0xb3, 0x88, 0xf9, 0x9c, 0x3a, 0xb5, 0x81, 0x35, 0x6c, 0x7b, 0xf5,
	0xbc, 0x70, 0x4b, 0x49, 0x1f, 0x40, 0xa6, 0x2c, 0x0b, 0xfc, 0xa5, 0x62, 0x99, 0x73, 0x8a, 0xdd,
	0x0d, 0xac, 0xbc, 0x56, 0x2c, 0x33, 0x70, 0xc8, 0x62, 0x2e, 0x7c, 0xcd, 0x17, 0xcc, 0x39, 0xcb,
	0x61, 0xac, 0xcc, 0xf8, 0x82, 0x91, 0x47, 0x50, 0x67, 0x82, 0xe6, 0xe0, 0x39, 0x82, 0xe7, 0x4c,
	0x50, 0x84, 0x3e, 0x82, 0x66, 0xb8, 0x7e, 0x21, 0xe3, 0x5b, 0x47, 0x5f, 0xd8, 0x94, 0x72, 0x67,
	0xc5, 0xa2, 0x8c, 0x69, 0x7f, 0xce, 0x56, 0x4e, 0x23, 0x97, 0xce, 0x2b, 0xdf, 0xb2, 0x95, 0xfb,
	0x3d, 0x74, 0x5e, 0x52, 0xba, 0x99, 0x89, 0xc7, 0xde, 0x91, 0x17, 0xd0, 0x2e, 0x14, 0x23, 0x29,
	0xde, 0xe2, 0x6c, 0x9a, 0xcf, 0x1e, 0x8d, 0x4b, 0x83, 0x1c, 0x97, 0x87, 0xe8, 0xb5, 0xc2, 0xd2,
	0x5f, 0xee, 0x0f, 0xf0, 0xc1, 0x8e, 0xa2, 0x4a, 0xff, 0xb5, 0x64, 0x0f, 0x1e, 0xdc, 0x30, 0xfd,
	0x32, 0x49, 0x4a, 0x39, 0xdd, 0x19, 0x90, 0x6a, 0xb1, 0x62, 0x95, 0x70, 0xa5, 0x1d, 0x6b, 0x70,
	0x72, 0x47, 0xab, 0x57, 0x5c, 0x69, 0x77, 0x0c, 0xbd, 0x1b, 0xa6, 0x37, 0x84, 0xeb, 0xd5, 0x2d,
	0xc5, 0xa1, 0x5c, 0xc2, 0x39, 0xa7, 0x5b, 0xc1, 0xb6, 0x77, 0xc6, 0x29, 0xf2, 0xdf, 0xc0, 0xc5,
	0x3e, 0xff, 0x3f, 0xc8, 0xf1, 0x47, 0x0d, 0x2e, 0x8b, 0x17, 0xe3, 0x6a, 0x3e, 0xcd, 0x17, 0x1b,
	0x97, 0xb6, 0xf2, 0xcd, 0xad, 0xbd, 0x6f, 0xde, 0x83, 0xd3, 0x30, 0x36, 0x90, 0x8d, 0x50, 0x2d,
	0x8c, 0x6f, 0x29, 0x79, 0x0a, 0x9d, 0x9f, 0xe5, 0x32, 0xf3, 0x23, 0xa1, 0xfd, 0x84, 0x2f, 0xb8,
	0xc6, 0x25, 0xae, 0x79, 0x2d, 0x53, 0x9d, 0x0a, 0xfd, 0xca, 0xd4, 0x88, 0x0b, 0x6d, 0x1a, 0xac,
	0x4a, 0xa4, 0x1a, 0x92, 0x9a, 0x34, 0x58, 0x15, 0x9c, 0x21, 0x74, 0x15, 0x17, 0x71, 0xc2, 0x4a,
	0xb4, 0x53, 0x74, 0xea, 0xe4, 0xf5, 0x82, 0xf9, 0x29, 0x90, 0x35, 0xf3, 0x7d, 0x90, 0x2c, 0xd9,
	0x9a, 0x7b, 0x86, 0xdc, 0xb5, 0xc6, 0x1b, 0x03, 0xe4, 0xec, 0xe7, 0xf0, 0x10, 0x13, 0xea, 0x90,
	0x05, 0x62, 0xa7, 0xe3, 0x1c, 0x43, 0xf4, 0x0c, 0x3a, 0x33, 0x60, 0xa9, 0xe9, 0x33, 0xf8, 0xd0,
	0x04, 0xde, 0xef, 0xa9, 0x63, 0x0f, 0xa1, 0xc1, 0xaa, 0xda, 0xb2, 0x7b, 0x18, 0x1b, 0xd5, 0xc3,
	0xd8, 0x01, 0x9b, 0x53, 0x07, 0x30, 0xa4, 0xcd, 0xa9, 0x71, 0xc8, 0x98, 0xd2, 0x7e, 0x35, 0x9b,
	0xd3, 0x1c, 0x58, 0xc3, 0x13, 0x8f, 0x18, 0xf0, 0x9b, 0x9d, 0x64, 0x64, 0x02, 0x17, 0xd8, 0x52,
	0x49, 0xe6, 0xb4, 0xb0, 0xe3, 0x81, 0xc1, 0xbe, 0x2a, 0xe7, 0x32, 0x63, 0xdf, 0x7a, 0x44, 0x42,
	0x3b, 0x6d, 0x64, 0x36, 0x37, 0xda, 0x53, 0xa1, 0xc9, 0x00, 0x5a, 0x85, 0xa8, 0xa1, 0x74, 0x90,
	0x02, 0x6b, 0xb1, 0xa9, 0xd0, 0xee, 0xaf, 0xf0, 0xe4, 0x6f, 0x76, 0xc6, 0xec, 0x14, 0xf9, 0x09,
	0x2e, 0x8b, 0xbd, 0x31, 0xb7, 0x25, 0x9e, 0xc5, 0xf2, 0x76, 0x3e, 0x3d, 0xb8, 0x9d, 0x15, 0x29,
	0xef, 0x22, 0xdc, 0x05, 0x50, 0xdc, 0x55, 0xd0, 0x2f, 0x1f, 0xfb, 0x4a, 0x0f, 0x7b, 0x47, 0x3c,
	0x20, 0xfb, 0xee, 0xeb, 0x9b, 0xe0, 0x6e, 0xc6, 0xdd, 0xaa, 0xb1, 0xab, 0xe1, 0xea, 0x98, 0xa9,
	0x4a, 0xef, 0xc5, 0x55, 0x41, 0xff, 0x3b, 0xf9, 0x7f, 0xbf, 0xea, 0x00, 0xae, 0x8e, 0x99, 0xaa,
	0xd4, 0xfd, 0x05, 0xfa, 0xa5, 0xab, 0xe8, 0x40, 0xac, 0x7f, 0xbc, 0x37, 0x86, 0xd0, 0x2d, 0x11,
	0xf2, 0xcd, 0xb0, 0xf1, 0xba, 0xeb, 0x6c, 0x59, 0xf8, 0xb5, 0xff, 0xb4, 0xe1, 0xea, 0x98, 0x99,
	0x4a, 0xef, 0x75, 0xdb, 0xc8, 0x6f, 0x16, 0x3c, 0x2e, 0xd4, 0xb5, 0xac, 0x1a, 0xd8, 0x68, 0x70,
	0xb3, 0x63, 0x70, 0x3c, 0x6e, 0xe1, 0x3f, 0x93, 0x65, 0xa3, 0xaf, 0x85, 0xce, 0x56, 0xde, 0xc3,
	0xf0, 0x20, 0xf8, 0x58, 0x6d, 0xcf, 0xdb, 0x81, 0x36, 0xd2, 0x85, 0x13, 0xf3, 0x3f, 0x37, 0x9f,
	0xb3, 0x79, 0x24, 0x2f, 0xe0, 0x34, 0xbf, 0x08, 0x6c, 0xdc, 0x85, 0xe1, 0x5d, 0x26, 0x60, 0xf4,
	0xbc, 0xbc, 0xed, 0x4b, 0xfb, 0x0b, 0xeb, 0x7a, 0xf2, 0xe3, 0x28, 0x96, 0x49, 0x20, 0xe2, 0xf1,
	0xe7, 0xcf, 0xb4, 0x1e, 0x47, 0x72, 0x31, 0xc1, 0x5f, 0x3a, 0x91, 0x4c, 0x26, 0x8a, 0x65, 0xef,
	0x79, 0xc4, 0xd4, 0xa4, 0xa4, 0x1b, 0x9e, 0x21, 0xfc, 0xfc, 0xaf, 0x00, 0x00, 0x00, 0xff, 0xff,
	0x48, 0x1e, 0x5d, 0xc2, 0x2f, 0x09, 0x00, 0x00,
}
