// Code generated by protoc-gen-go. DO NOT EDIT.
// source: user-complaint/user-complaint.proto

package user_complaint // import "golang.52tt.com/protocol/services/user-complaint"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 投诉单归档处理
type HANDLE_TYPE int32

const (
	HANDLE_TYPE_HANDLE_TYPE_AGREE_ACCEPT HANDLE_TYPE = 0
	HANDLE_TYPE_HANDLE_TYPE_REJECT       HANDLE_TYPE = 1
)

var HANDLE_TYPE_name = map[int32]string{
	0: "HANDLE_TYPE_AGREE_ACCEPT",
	1: "HANDLE_TYPE_REJECT",
}
var HANDLE_TYPE_value = map[string]int32{
	"HANDLE_TYPE_AGREE_ACCEPT": 0,
	"HANDLE_TYPE_REJECT":       1,
}

func (x HANDLE_TYPE) String() string {
	return proto.EnumName(HANDLE_TYPE_name, int32(x))
}
func (HANDLE_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{0}
}

// 业务类型
type BUSINESS_TYPE int32

const (
	BUSINESS_TYPE_BUSINESS_TYPE_FUN   BUSINESS_TYPE = 0
	BUSINESS_TYPE_BUSINESS_TYPE_LIVE  BUSINESS_TYPE = 1
	BUSINESS_TYPE_BUSINESS_TYPE_OTHER BUSINESS_TYPE = 2
)

var BUSINESS_TYPE_name = map[int32]string{
	0: "BUSINESS_TYPE_FUN",
	1: "BUSINESS_TYPE_LIVE",
	2: "BUSINESS_TYPE_OTHER",
}
var BUSINESS_TYPE_value = map[string]int32{
	"BUSINESS_TYPE_FUN":   0,
	"BUSINESS_TYPE_LIVE":  1,
	"BUSINESS_TYPE_OTHER": 2,
}

func (x BUSINESS_TYPE) String() string {
	return proto.EnumName(BUSINESS_TYPE_name, int32(x))
}
func (BUSINESS_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{1}
}

// 工单状态
type COMPLAINT_OPR int32

const (
	COMPLAINT_OPR_COMPLAINT_OPR_NONE    COMPLAINT_OPR = 0
	COMPLAINT_OPR_COMPLAINT_OPR_HANDING COMPLAINT_OPR = 1
	COMPLAINT_OPR_COMPLAINT_OPR_DONE    COMPLAINT_OPR = 2
)

var COMPLAINT_OPR_name = map[int32]string{
	0: "COMPLAINT_OPR_NONE",
	1: "COMPLAINT_OPR_HANDING",
	2: "COMPLAINT_OPR_DONE",
}
var COMPLAINT_OPR_value = map[string]int32{
	"COMPLAINT_OPR_NONE":    0,
	"COMPLAINT_OPR_HANDING": 1,
	"COMPLAINT_OPR_DONE":    2,
}

func (x COMPLAINT_OPR) String() string {
	return proto.EnumName(COMPLAINT_OPR_name, int32(x))
}
func (COMPLAINT_OPR) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{2}
}

type QUERY_TYPE int32

const (
	QUERY_TYPE_QUERY_TYPE_TTID          QUERY_TYPE = 0
	QUERY_TYPE_QUERY_TYPE_GUILD_SHORTID QUERY_TYPE = 1
	QUERY_TYPE_QUERY_TYPE_GUILD_ID      QUERY_TYPE = 2
	QUERY_TYPE_QUERY_TYPE_ORDER_ID      QUERY_TYPE = 3
)

var QUERY_TYPE_name = map[int32]string{
	0: "QUERY_TYPE_TTID",
	1: "QUERY_TYPE_GUILD_SHORTID",
	2: "QUERY_TYPE_GUILD_ID",
	3: "QUERY_TYPE_ORDER_ID",
}
var QUERY_TYPE_value = map[string]int32{
	"QUERY_TYPE_TTID":          0,
	"QUERY_TYPE_GUILD_SHORTID": 1,
	"QUERY_TYPE_GUILD_ID":      2,
	"QUERY_TYPE_ORDER_ID":      3,
}

func (x QUERY_TYPE) String() string {
	return proto.EnumName(QUERY_TYPE_name, int32(x))
}
func (QUERY_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{3}
}

// 用户投诉类型
type COMPLAINT_TYPE int32

const (
	COMPLAINT_TYPE_COMPLAINT_TYPE_EMPTY   COMPLAINT_TYPE = 0
	COMPLAINT_TYPE_COMPLAINT_TYPE_SPITE   COMPLAINT_TYPE = 1
	COMPLAINT_TYPE_COMPLAINT_TYPE_TRADE   COMPLAINT_TYPE = 2
	COMPLAINT_TYPE_COMPLAINT_TYPE_VIOLATE COMPLAINT_TYPE = 3
	COMPLAINT_TYPE_COMPLAINT_TYPE_HANGUP  COMPLAINT_TYPE = 4
	COMPLAINT_TYPE_COMPLAINT_TYPE_OTHER   COMPLAINT_TYPE = 5
)

var COMPLAINT_TYPE_name = map[int32]string{
	0: "COMPLAINT_TYPE_EMPTY",
	1: "COMPLAINT_TYPE_SPITE",
	2: "COMPLAINT_TYPE_TRADE",
	3: "COMPLAINT_TYPE_VIOLATE",
	4: "COMPLAINT_TYPE_HANGUP",
	5: "COMPLAINT_TYPE_OTHER",
}
var COMPLAINT_TYPE_value = map[string]int32{
	"COMPLAINT_TYPE_EMPTY":   0,
	"COMPLAINT_TYPE_SPITE":   1,
	"COMPLAINT_TYPE_TRADE":   2,
	"COMPLAINT_TYPE_VIOLATE": 3,
	"COMPLAINT_TYPE_HANGUP":  4,
	"COMPLAINT_TYPE_OTHER":   5,
}

func (x COMPLAINT_TYPE) String() string {
	return proto.EnumName(COMPLAINT_TYPE_name, int32(x))
}
func (COMPLAINT_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{4}
}

type PAY_TYPE int32

const (
	PAY_TYPE_PAY_TYPE_EMPTY  PAY_TYPE = 0
	PAY_TYPE_PAY_TYPE_GIVE   PAY_TYPE = 1
	PAY_TYPE_PAY_TYPE_WECHAT PAY_TYPE = 2
	PAY_TYPE_PAY_TYPE_QQ     PAY_TYPE = 3
	PAY_TYPE_PAY_TYPE_ALIPAY PAY_TYPE = 4
)

var PAY_TYPE_name = map[int32]string{
	0: "PAY_TYPE_EMPTY",
	1: "PAY_TYPE_GIVE",
	2: "PAY_TYPE_WECHAT",
	3: "PAY_TYPE_QQ",
	4: "PAY_TYPE_ALIPAY",
}
var PAY_TYPE_value = map[string]int32{
	"PAY_TYPE_EMPTY":  0,
	"PAY_TYPE_GIVE":   1,
	"PAY_TYPE_WECHAT": 2,
	"PAY_TYPE_QQ":     3,
	"PAY_TYPE_ALIPAY": 4,
}

func (x PAY_TYPE) String() string {
	return proto.EnumName(PAY_TYPE_name, int32(x))
}
func (PAY_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{5}
}

// 违规内容
type ILLEGAL_TYPE int32

const (
	ILLEGAL_TYPE_ILLEGAL_TYPE_EMPTY   ILLEGAL_TYPE = 0
	ILLEGAL_TYPE_ILLEGAL_TYPE_PERFORM ILLEGAL_TYPE = 1
	ILLEGAL_TYPE_ILLEGAL_TYPE_TOPIC   ILLEGAL_TYPE = 2
	ILLEGAL_TYPE_ILLEGAL_TYPE_OTHER   ILLEGAL_TYPE = 3
)

var ILLEGAL_TYPE_name = map[int32]string{
	0: "ILLEGAL_TYPE_EMPTY",
	1: "ILLEGAL_TYPE_PERFORM",
	2: "ILLEGAL_TYPE_TOPIC",
	3: "ILLEGAL_TYPE_OTHER",
}
var ILLEGAL_TYPE_value = map[string]int32{
	"ILLEGAL_TYPE_EMPTY":   0,
	"ILLEGAL_TYPE_PERFORM": 1,
	"ILLEGAL_TYPE_TOPIC":   2,
	"ILLEGAL_TYPE_OTHER":   3,
}

func (x ILLEGAL_TYPE) String() string {
	return proto.EnumName(ILLEGAL_TYPE_name, int32(x))
}
func (ILLEGAL_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{6}
}

type COMPLAINT_STATUS_TYPE int32

const (
	COMPLAINT_STATUS_TYPE_COMPLAINT_STATUS_TYPE_UNDO    COMPLAINT_STATUS_TYPE = 0
	COMPLAINT_STATUS_TYPE_COMPLAINT_STATUS_TYPE_DONE    COMPLAINT_STATUS_TYPE = 1
	COMPLAINT_STATUS_TYPE_COMPLAINT_STATUS_TYPE_ARCHIVE COMPLAINT_STATUS_TYPE = 2
)

var COMPLAINT_STATUS_TYPE_name = map[int32]string{
	0: "COMPLAINT_STATUS_TYPE_UNDO",
	1: "COMPLAINT_STATUS_TYPE_DONE",
	2: "COMPLAINT_STATUS_TYPE_ARCHIVE",
}
var COMPLAINT_STATUS_TYPE_value = map[string]int32{
	"COMPLAINT_STATUS_TYPE_UNDO":    0,
	"COMPLAINT_STATUS_TYPE_DONE":    1,
	"COMPLAINT_STATUS_TYPE_ARCHIVE": 2,
}

func (x COMPLAINT_STATUS_TYPE) String() string {
	return proto.EnumName(COMPLAINT_STATUS_TYPE_name, int32(x))
}
func (COMPLAINT_STATUS_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{7}
}

// 操作类型
type OP_TYPE int32

const (
	OP_TYPE_OP_TYPE_NOTIFY         OP_TYPE = 0
	OP_TYPE_OP_TYPE_REMARK         OP_TYPE = 1
	OP_TYPE_OP_TYPE_APPLY_ARCHIVE  OP_TYPE = 2
	OP_TYPE_OP_TYPE_REJECT_ARCHIVE OP_TYPE = 3
)

var OP_TYPE_name = map[int32]string{
	0: "OP_TYPE_NOTIFY",
	1: "OP_TYPE_REMARK",
	2: "OP_TYPE_APPLY_ARCHIVE",
	3: "OP_TYPE_REJECT_ARCHIVE",
}
var OP_TYPE_value = map[string]int32{
	"OP_TYPE_NOTIFY":         0,
	"OP_TYPE_REMARK":         1,
	"OP_TYPE_APPLY_ARCHIVE":  2,
	"OP_TYPE_REJECT_ARCHIVE": 3,
}

func (x OP_TYPE) String() string {
	return proto.EnumName(OP_TYPE_name, int32(x))
}
func (OP_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{8}
}

type GetUserComplaintEntryReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildDisplayId       uint32   `protobuf:"varint,2,opt,name=guild_display_id,json=guildDisplayId,proto3" json:"guild_display_id,omitempty"`
	AnchorUid            uint32   `protobuf:"varint,3,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	ChannelName          string   `protobuf:"bytes,4,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	TerminalType         uint32   `protobuf:"varint,5,opt,name=terminalType,proto3" json:"terminalType,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserComplaintEntryReq) Reset()         { *m = GetUserComplaintEntryReq{} }
func (m *GetUserComplaintEntryReq) String() string { return proto.CompactTextString(m) }
func (*GetUserComplaintEntryReq) ProtoMessage()    {}
func (*GetUserComplaintEntryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{0}
}
func (m *GetUserComplaintEntryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserComplaintEntryReq.Unmarshal(m, b)
}
func (m *GetUserComplaintEntryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserComplaintEntryReq.Marshal(b, m, deterministic)
}
func (dst *GetUserComplaintEntryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserComplaintEntryReq.Merge(dst, src)
}
func (m *GetUserComplaintEntryReq) XXX_Size() int {
	return xxx_messageInfo_GetUserComplaintEntryReq.Size(m)
}
func (m *GetUserComplaintEntryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserComplaintEntryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserComplaintEntryReq proto.InternalMessageInfo

func (m *GetUserComplaintEntryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserComplaintEntryReq) GetGuildDisplayId() uint32 {
	if m != nil {
		return m.GuildDisplayId
	}
	return 0
}

func (m *GetUserComplaintEntryReq) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *GetUserComplaintEntryReq) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *GetUserComplaintEntryReq) GetTerminalType() uint32 {
	if m != nil {
		return m.TerminalType
	}
	return 0
}

type GetUserComplaintEntryResp struct {
	JumpUrl              string   `protobuf:"bytes,1,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserComplaintEntryResp) Reset()         { *m = GetUserComplaintEntryResp{} }
func (m *GetUserComplaintEntryResp) String() string { return proto.CompactTextString(m) }
func (*GetUserComplaintEntryResp) ProtoMessage()    {}
func (*GetUserComplaintEntryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{1}
}
func (m *GetUserComplaintEntryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserComplaintEntryResp.Unmarshal(m, b)
}
func (m *GetUserComplaintEntryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserComplaintEntryResp.Marshal(b, m, deterministic)
}
func (dst *GetUserComplaintEntryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserComplaintEntryResp.Merge(dst, src)
}
func (m *GetUserComplaintEntryResp) XXX_Size() int {
	return xxx_messageInfo_GetUserComplaintEntryResp.Size(m)
}
func (m *GetUserComplaintEntryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserComplaintEntryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserComplaintEntryResp proto.InternalMessageInfo

func (m *GetUserComplaintEntryResp) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

type ComplaintInfo struct {
	RecordId             uint32        `protobuf:"varint,1,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"`
	OrderId              string        `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Type                 uint32        `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	BusinessType         uint32        `protobuf:"varint,4,opt,name=business_type,json=businessType,proto3" json:"business_type,omitempty"`
	OpStatus             uint32        `protobuf:"varint,5,opt,name=op_status,json=opStatus,proto3" json:"op_status,omitempty"`
	GuildStatus          uint32        `protobuf:"varint,6,opt,name=guild_status,json=guildStatus,proto3" json:"guild_status,omitempty"`
	Uid                  uint32        `protobuf:"varint,7,opt,name=uid,proto3" json:"uid,omitempty"`
	Ttid                 string        `protobuf:"bytes,8,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Nickname             string        `protobuf:"bytes,9,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Account              string        `protobuf:"bytes,10,opt,name=account,proto3" json:"account,omitempty"`
	GuildDisplayId       uint32        `protobuf:"varint,11,opt,name=guild_display_id,json=guildDisplayId,proto3" json:"guild_display_id,omitempty"`
	GuildId              uint32        `protobuf:"varint,12,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ChannelName          string        `protobuf:"bytes,13,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	OccurrenceTime       uint32        `protobuf:"varint,14,opt,name=occurrence_time,json=occurrenceTime,proto3" json:"occurrence_time,omitempty"`
	TargetTtid           string        `protobuf:"bytes,15,opt,name=target_ttid,json=targetTtid,proto3" json:"target_ttid,omitempty"`
	TargetNickname       string        `protobuf:"bytes,16,opt,name=target_nickname,json=targetNickname,proto3" json:"target_nickname,omitempty"`
	Money                uint32        `protobuf:"varint,17,opt,name=money,proto3" json:"money,omitempty"`
	IllegalType          uint32        `protobuf:"varint,18,opt,name=illegal_type,json=illegalType,proto3" json:"illegal_type,omitempty"`
	Phone                string        `protobuf:"bytes,19,opt,name=phone,proto3" json:"phone,omitempty"`
	Tips                 string        `protobuf:"bytes,20,opt,name=tips,proto3" json:"tips,omitempty"`
	PicList              []string      `protobuf:"bytes,21,rep,name=pic_list,json=picList,proto3" json:"pic_list,omitempty"`
	CreateTime           uint32        `protobuf:"varint,22,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Feedback             string        `protobuf:"bytes,23,opt,name=feedback,proto3" json:"feedback,omitempty"`
	HandlerTime          uint32        `protobuf:"varint,24,opt,name=handler_time,json=handlerTime,proto3" json:"handler_time,omitempty"`
	IsNotify             bool          `protobuf:"varint,25,opt,name=is_notify,json=isNotify,proto3" json:"is_notify,omitempty"`
	OpMsg                string        `protobuf:"bytes,26,opt,name=op_msg,json=opMsg,proto3" json:"op_msg,omitempty"`
	Remark               string        `protobuf:"bytes,27,opt,name=remark,proto3" json:"remark,omitempty"`
	RecordInfoList       []*RecordInfo `protobuf:"bytes,28,rep,name=record_info_list,json=recordInfoList,proto3" json:"record_info_list,omitempty"`
	ArchiveHandler       string        `protobuf:"bytes,29,opt,name=archive_handler,json=archiveHandler,proto3" json:"archive_handler,omitempty"`
	ArchiveTime          uint32        `protobuf:"varint,30,opt,name=archive_time,json=archiveTime,proto3" json:"archive_time,omitempty"`
	UpdateTime           uint32        `protobuf:"varint,31,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	MediaList            []*MediaInfo  `protobuf:"bytes,32,rep,name=media_list,json=mediaList,proto3" json:"media_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ComplaintInfo) Reset()         { *m = ComplaintInfo{} }
func (m *ComplaintInfo) String() string { return proto.CompactTextString(m) }
func (*ComplaintInfo) ProtoMessage()    {}
func (*ComplaintInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{2}
}
func (m *ComplaintInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ComplaintInfo.Unmarshal(m, b)
}
func (m *ComplaintInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ComplaintInfo.Marshal(b, m, deterministic)
}
func (dst *ComplaintInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ComplaintInfo.Merge(dst, src)
}
func (m *ComplaintInfo) XXX_Size() int {
	return xxx_messageInfo_ComplaintInfo.Size(m)
}
func (m *ComplaintInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ComplaintInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ComplaintInfo proto.InternalMessageInfo

func (m *ComplaintInfo) GetRecordId() uint32 {
	if m != nil {
		return m.RecordId
	}
	return 0
}

func (m *ComplaintInfo) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *ComplaintInfo) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *ComplaintInfo) GetBusinessType() uint32 {
	if m != nil {
		return m.BusinessType
	}
	return 0
}

func (m *ComplaintInfo) GetOpStatus() uint32 {
	if m != nil {
		return m.OpStatus
	}
	return 0
}

func (m *ComplaintInfo) GetGuildStatus() uint32 {
	if m != nil {
		return m.GuildStatus
	}
	return 0
}

func (m *ComplaintInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ComplaintInfo) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *ComplaintInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ComplaintInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ComplaintInfo) GetGuildDisplayId() uint32 {
	if m != nil {
		return m.GuildDisplayId
	}
	return 0
}

func (m *ComplaintInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *ComplaintInfo) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *ComplaintInfo) GetOccurrenceTime() uint32 {
	if m != nil {
		return m.OccurrenceTime
	}
	return 0
}

func (m *ComplaintInfo) GetTargetTtid() string {
	if m != nil {
		return m.TargetTtid
	}
	return ""
}

func (m *ComplaintInfo) GetTargetNickname() string {
	if m != nil {
		return m.TargetNickname
	}
	return ""
}

func (m *ComplaintInfo) GetMoney() uint32 {
	if m != nil {
		return m.Money
	}
	return 0
}

func (m *ComplaintInfo) GetIllegalType() uint32 {
	if m != nil {
		return m.IllegalType
	}
	return 0
}

func (m *ComplaintInfo) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *ComplaintInfo) GetTips() string {
	if m != nil {
		return m.Tips
	}
	return ""
}

func (m *ComplaintInfo) GetPicList() []string {
	if m != nil {
		return m.PicList
	}
	return nil
}

func (m *ComplaintInfo) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *ComplaintInfo) GetFeedback() string {
	if m != nil {
		return m.Feedback
	}
	return ""
}

func (m *ComplaintInfo) GetHandlerTime() uint32 {
	if m != nil {
		return m.HandlerTime
	}
	return 0
}

func (m *ComplaintInfo) GetIsNotify() bool {
	if m != nil {
		return m.IsNotify
	}
	return false
}

func (m *ComplaintInfo) GetOpMsg() string {
	if m != nil {
		return m.OpMsg
	}
	return ""
}

func (m *ComplaintInfo) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *ComplaintInfo) GetRecordInfoList() []*RecordInfo {
	if m != nil {
		return m.RecordInfoList
	}
	return nil
}

func (m *ComplaintInfo) GetArchiveHandler() string {
	if m != nil {
		return m.ArchiveHandler
	}
	return ""
}

func (m *ComplaintInfo) GetArchiveTime() uint32 {
	if m != nil {
		return m.ArchiveTime
	}
	return 0
}

func (m *ComplaintInfo) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *ComplaintInfo) GetMediaList() []*MediaInfo {
	if m != nil {
		return m.MediaList
	}
	return nil
}

type MediaInfo struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Url                  string   `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MediaInfo) Reset()         { *m = MediaInfo{} }
func (m *MediaInfo) String() string { return proto.CompactTextString(m) }
func (*MediaInfo) ProtoMessage()    {}
func (*MediaInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{3}
}
func (m *MediaInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MediaInfo.Unmarshal(m, b)
}
func (m *MediaInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MediaInfo.Marshal(b, m, deterministic)
}
func (dst *MediaInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MediaInfo.Merge(dst, src)
}
func (m *MediaInfo) XXX_Size() int {
	return xxx_messageInfo_MediaInfo.Size(m)
}
func (m *MediaInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MediaInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MediaInfo proto.InternalMessageInfo

func (m *MediaInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MediaInfo) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

type RecordInfo struct {
	OpType               uint32   `protobuf:"varint,1,opt,name=op_type,json=opType,proto3" json:"op_type,omitempty"`
	Handler              string   `protobuf:"bytes,2,opt,name=handler,proto3" json:"handler,omitempty"`
	OpTime               uint32   `protobuf:"varint,3,opt,name=op_time,json=opTime,proto3" json:"op_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecordInfo) Reset()         { *m = RecordInfo{} }
func (m *RecordInfo) String() string { return proto.CompactTextString(m) }
func (*RecordInfo) ProtoMessage()    {}
func (*RecordInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{4}
}
func (m *RecordInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordInfo.Unmarshal(m, b)
}
func (m *RecordInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordInfo.Marshal(b, m, deterministic)
}
func (dst *RecordInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordInfo.Merge(dst, src)
}
func (m *RecordInfo) XXX_Size() int {
	return xxx_messageInfo_RecordInfo.Size(m)
}
func (m *RecordInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RecordInfo proto.InternalMessageInfo

func (m *RecordInfo) GetOpType() uint32 {
	if m != nil {
		return m.OpType
	}
	return 0
}

func (m *RecordInfo) GetHandler() string {
	if m != nil {
		return m.Handler
	}
	return ""
}

func (m *RecordInfo) GetOpTime() uint32 {
	if m != nil {
		return m.OpTime
	}
	return 0
}

type GetUserComplaintListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Page                 uint32   `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageNum              uint32   `protobuf:"varint,3,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	AllStatus            bool     `protobuf:"varint,4,opt,name=all_status,json=allStatus,proto3" json:"all_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserComplaintListReq) Reset()         { *m = GetUserComplaintListReq{} }
func (m *GetUserComplaintListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserComplaintListReq) ProtoMessage()    {}
func (*GetUserComplaintListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{5}
}
func (m *GetUserComplaintListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserComplaintListReq.Unmarshal(m, b)
}
func (m *GetUserComplaintListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserComplaintListReq.Marshal(b, m, deterministic)
}
func (dst *GetUserComplaintListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserComplaintListReq.Merge(dst, src)
}
func (m *GetUserComplaintListReq) XXX_Size() int {
	return xxx_messageInfo_GetUserComplaintListReq.Size(m)
}
func (m *GetUserComplaintListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserComplaintListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserComplaintListReq proto.InternalMessageInfo

func (m *GetUserComplaintListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserComplaintListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetUserComplaintListReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

func (m *GetUserComplaintListReq) GetAllStatus() bool {
	if m != nil {
		return m.AllStatus
	}
	return false
}

type GetUserComplaintListResp struct {
	Total                uint32           `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List                 []*ComplaintInfo `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetUserComplaintListResp) Reset()         { *m = GetUserComplaintListResp{} }
func (m *GetUserComplaintListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserComplaintListResp) ProtoMessage()    {}
func (*GetUserComplaintListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{6}
}
func (m *GetUserComplaintListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserComplaintListResp.Unmarshal(m, b)
}
func (m *GetUserComplaintListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserComplaintListResp.Marshal(b, m, deterministic)
}
func (dst *GetUserComplaintListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserComplaintListResp.Merge(dst, src)
}
func (m *GetUserComplaintListResp) XXX_Size() int {
	return xxx_messageInfo_GetUserComplaintListResp.Size(m)
}
func (m *GetUserComplaintListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserComplaintListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserComplaintListResp proto.InternalMessageInfo

func (m *GetUserComplaintListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetUserComplaintListResp) GetList() []*ComplaintInfo {
	if m != nil {
		return m.List
	}
	return nil
}

type CreateComplaintReq struct {
	Info                 *ComplaintInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *CreateComplaintReq) Reset()         { *m = CreateComplaintReq{} }
func (m *CreateComplaintReq) String() string { return proto.CompactTextString(m) }
func (*CreateComplaintReq) ProtoMessage()    {}
func (*CreateComplaintReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{7}
}
func (m *CreateComplaintReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateComplaintReq.Unmarshal(m, b)
}
func (m *CreateComplaintReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateComplaintReq.Marshal(b, m, deterministic)
}
func (dst *CreateComplaintReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateComplaintReq.Merge(dst, src)
}
func (m *CreateComplaintReq) XXX_Size() int {
	return xxx_messageInfo_CreateComplaintReq.Size(m)
}
func (m *CreateComplaintReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateComplaintReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateComplaintReq proto.InternalMessageInfo

func (m *CreateComplaintReq) GetInfo() *ComplaintInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type CreateComplaintResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateComplaintResp) Reset()         { *m = CreateComplaintResp{} }
func (m *CreateComplaintResp) String() string { return proto.CompactTextString(m) }
func (*CreateComplaintResp) ProtoMessage()    {}
func (*CreateComplaintResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{8}
}
func (m *CreateComplaintResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateComplaintResp.Unmarshal(m, b)
}
func (m *CreateComplaintResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateComplaintResp.Marshal(b, m, deterministic)
}
func (dst *CreateComplaintResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateComplaintResp.Merge(dst, src)
}
func (m *CreateComplaintResp) XXX_Size() int {
	return xxx_messageInfo_CreateComplaintResp.Size(m)
}
func (m *CreateComplaintResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateComplaintResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateComplaintResp proto.InternalMessageInfo

type GetGuildComplaintListReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Page                 uint32   `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageNum              uint32   `protobuf:"varint,3,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	Status               uint32   `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildComplaintListReq) Reset()         { *m = GetGuildComplaintListReq{} }
func (m *GetGuildComplaintListReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildComplaintListReq) ProtoMessage()    {}
func (*GetGuildComplaintListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{9}
}
func (m *GetGuildComplaintListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildComplaintListReq.Unmarshal(m, b)
}
func (m *GetGuildComplaintListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildComplaintListReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildComplaintListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildComplaintListReq.Merge(dst, src)
}
func (m *GetGuildComplaintListReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildComplaintListReq.Size(m)
}
func (m *GetGuildComplaintListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildComplaintListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildComplaintListReq proto.InternalMessageInfo

func (m *GetGuildComplaintListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildComplaintListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetGuildComplaintListReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

func (m *GetGuildComplaintListReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type GetGuildComplaintListResp struct {
	Total                uint32           `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List                 []*ComplaintInfo `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetGuildComplaintListResp) Reset()         { *m = GetGuildComplaintListResp{} }
func (m *GetGuildComplaintListResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildComplaintListResp) ProtoMessage()    {}
func (*GetGuildComplaintListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{10}
}
func (m *GetGuildComplaintListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildComplaintListResp.Unmarshal(m, b)
}
func (m *GetGuildComplaintListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildComplaintListResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildComplaintListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildComplaintListResp.Merge(dst, src)
}
func (m *GetGuildComplaintListResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildComplaintListResp.Size(m)
}
func (m *GetGuildComplaintListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildComplaintListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildComplaintListResp proto.InternalMessageInfo

func (m *GetGuildComplaintListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetGuildComplaintListResp) GetList() []*ComplaintInfo {
	if m != nil {
		return m.List
	}
	return nil
}

type SubmitGuildComplaintReq struct {
	RecordId             uint32   `protobuf:"varint,1,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"`
	Feedback             string   `protobuf:"bytes,2,opt,name=feedback,proto3" json:"feedback,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubmitGuildComplaintReq) Reset()         { *m = SubmitGuildComplaintReq{} }
func (m *SubmitGuildComplaintReq) String() string { return proto.CompactTextString(m) }
func (*SubmitGuildComplaintReq) ProtoMessage()    {}
func (*SubmitGuildComplaintReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{11}
}
func (m *SubmitGuildComplaintReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubmitGuildComplaintReq.Unmarshal(m, b)
}
func (m *SubmitGuildComplaintReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubmitGuildComplaintReq.Marshal(b, m, deterministic)
}
func (dst *SubmitGuildComplaintReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubmitGuildComplaintReq.Merge(dst, src)
}
func (m *SubmitGuildComplaintReq) XXX_Size() int {
	return xxx_messageInfo_SubmitGuildComplaintReq.Size(m)
}
func (m *SubmitGuildComplaintReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SubmitGuildComplaintReq.DiscardUnknown(m)
}

var xxx_messageInfo_SubmitGuildComplaintReq proto.InternalMessageInfo

func (m *SubmitGuildComplaintReq) GetRecordId() uint32 {
	if m != nil {
		return m.RecordId
	}
	return 0
}

func (m *SubmitGuildComplaintReq) GetFeedback() string {
	if m != nil {
		return m.Feedback
	}
	return ""
}

type SubmitGuildComplaintResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubmitGuildComplaintResp) Reset()         { *m = SubmitGuildComplaintResp{} }
func (m *SubmitGuildComplaintResp) String() string { return proto.CompactTextString(m) }
func (*SubmitGuildComplaintResp) ProtoMessage()    {}
func (*SubmitGuildComplaintResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{12}
}
func (m *SubmitGuildComplaintResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubmitGuildComplaintResp.Unmarshal(m, b)
}
func (m *SubmitGuildComplaintResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubmitGuildComplaintResp.Marshal(b, m, deterministic)
}
func (dst *SubmitGuildComplaintResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubmitGuildComplaintResp.Merge(dst, src)
}
func (m *SubmitGuildComplaintResp) XXX_Size() int {
	return xxx_messageInfo_SubmitGuildComplaintResp.Size(m)
}
func (m *SubmitGuildComplaintResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SubmitGuildComplaintResp.DiscardUnknown(m)
}

var xxx_messageInfo_SubmitGuildComplaintResp proto.InternalMessageInfo

type ArchiveUserComplaintReq struct {
	RecordId             uint32   `protobuf:"varint,1,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"`
	CreateTime           uint32   `protobuf:"varint,2,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Ttid                 string   `protobuf:"bytes,3,opt,name=ttid,proto3" json:"ttid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ArchiveUserComplaintReq) Reset()         { *m = ArchiveUserComplaintReq{} }
func (m *ArchiveUserComplaintReq) String() string { return proto.CompactTextString(m) }
func (*ArchiveUserComplaintReq) ProtoMessage()    {}
func (*ArchiveUserComplaintReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{13}
}
func (m *ArchiveUserComplaintReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArchiveUserComplaintReq.Unmarshal(m, b)
}
func (m *ArchiveUserComplaintReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArchiveUserComplaintReq.Marshal(b, m, deterministic)
}
func (dst *ArchiveUserComplaintReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArchiveUserComplaintReq.Merge(dst, src)
}
func (m *ArchiveUserComplaintReq) XXX_Size() int {
	return xxx_messageInfo_ArchiveUserComplaintReq.Size(m)
}
func (m *ArchiveUserComplaintReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ArchiveUserComplaintReq.DiscardUnknown(m)
}

var xxx_messageInfo_ArchiveUserComplaintReq proto.InternalMessageInfo

func (m *ArchiveUserComplaintReq) GetRecordId() uint32 {
	if m != nil {
		return m.RecordId
	}
	return 0
}

func (m *ArchiveUserComplaintReq) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *ArchiveUserComplaintReq) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

type ArchiveUserComplaintResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ArchiveUserComplaintResp) Reset()         { *m = ArchiveUserComplaintResp{} }
func (m *ArchiveUserComplaintResp) String() string { return proto.CompactTextString(m) }
func (*ArchiveUserComplaintResp) ProtoMessage()    {}
func (*ArchiveUserComplaintResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{14}
}
func (m *ArchiveUserComplaintResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArchiveUserComplaintResp.Unmarshal(m, b)
}
func (m *ArchiveUserComplaintResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArchiveUserComplaintResp.Marshal(b, m, deterministic)
}
func (dst *ArchiveUserComplaintResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArchiveUserComplaintResp.Merge(dst, src)
}
func (m *ArchiveUserComplaintResp) XXX_Size() int {
	return xxx_messageInfo_ArchiveUserComplaintResp.Size(m)
}
func (m *ArchiveUserComplaintResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ArchiveUserComplaintResp.DiscardUnknown(m)
}

var xxx_messageInfo_ArchiveUserComplaintResp proto.InternalMessageInfo

type GetUserComplaintByRecordIdReq struct {
	RecordId             uint32   `protobuf:"varint,1,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserComplaintByRecordIdReq) Reset()         { *m = GetUserComplaintByRecordIdReq{} }
func (m *GetUserComplaintByRecordIdReq) String() string { return proto.CompactTextString(m) }
func (*GetUserComplaintByRecordIdReq) ProtoMessage()    {}
func (*GetUserComplaintByRecordIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{15}
}
func (m *GetUserComplaintByRecordIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserComplaintByRecordIdReq.Unmarshal(m, b)
}
func (m *GetUserComplaintByRecordIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserComplaintByRecordIdReq.Marshal(b, m, deterministic)
}
func (dst *GetUserComplaintByRecordIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserComplaintByRecordIdReq.Merge(dst, src)
}
func (m *GetUserComplaintByRecordIdReq) XXX_Size() int {
	return xxx_messageInfo_GetUserComplaintByRecordIdReq.Size(m)
}
func (m *GetUserComplaintByRecordIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserComplaintByRecordIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserComplaintByRecordIdReq proto.InternalMessageInfo

func (m *GetUserComplaintByRecordIdReq) GetRecordId() uint32 {
	if m != nil {
		return m.RecordId
	}
	return 0
}

type GetUserComplaintByRecordIdResp struct {
	Info                 *ComplaintInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetUserComplaintByRecordIdResp) Reset()         { *m = GetUserComplaintByRecordIdResp{} }
func (m *GetUserComplaintByRecordIdResp) String() string { return proto.CompactTextString(m) }
func (*GetUserComplaintByRecordIdResp) ProtoMessage()    {}
func (*GetUserComplaintByRecordIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{16}
}
func (m *GetUserComplaintByRecordIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserComplaintByRecordIdResp.Unmarshal(m, b)
}
func (m *GetUserComplaintByRecordIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserComplaintByRecordIdResp.Marshal(b, m, deterministic)
}
func (dst *GetUserComplaintByRecordIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserComplaintByRecordIdResp.Merge(dst, src)
}
func (m *GetUserComplaintByRecordIdResp) XXX_Size() int {
	return xxx_messageInfo_GetUserComplaintByRecordIdResp.Size(m)
}
func (m *GetUserComplaintByRecordIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserComplaintByRecordIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserComplaintByRecordIdResp proto.InternalMessageInfo

func (m *GetUserComplaintByRecordIdResp) GetInfo() *ComplaintInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type ListUserComplaintReq struct {
	BusinessType         uint32   `protobuf:"varint,1,opt,name=business_type,json=businessType,proto3" json:"business_type,omitempty"`
	QuerysType           uint32   `protobuf:"varint,2,opt,name=querys_type,json=querysType,proto3" json:"querys_type,omitempty"`
	OpStatus             uint32   `protobuf:"varint,3,opt,name=op_status,json=opStatus,proto3" json:"op_status,omitempty"`
	Ids                  []uint64 `protobuf:"varint,4,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	Page                 uint32   `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`
	PageNum              uint32   `protobuf:"varint,6,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	OrderIds             []string `protobuf:"bytes,7,rep,name=order_ids,json=orderIds,proto3" json:"order_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListUserComplaintReq) Reset()         { *m = ListUserComplaintReq{} }
func (m *ListUserComplaintReq) String() string { return proto.CompactTextString(m) }
func (*ListUserComplaintReq) ProtoMessage()    {}
func (*ListUserComplaintReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{17}
}
func (m *ListUserComplaintReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListUserComplaintReq.Unmarshal(m, b)
}
func (m *ListUserComplaintReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListUserComplaintReq.Marshal(b, m, deterministic)
}
func (dst *ListUserComplaintReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListUserComplaintReq.Merge(dst, src)
}
func (m *ListUserComplaintReq) XXX_Size() int {
	return xxx_messageInfo_ListUserComplaintReq.Size(m)
}
func (m *ListUserComplaintReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListUserComplaintReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListUserComplaintReq proto.InternalMessageInfo

func (m *ListUserComplaintReq) GetBusinessType() uint32 {
	if m != nil {
		return m.BusinessType
	}
	return 0
}

func (m *ListUserComplaintReq) GetQuerysType() uint32 {
	if m != nil {
		return m.QuerysType
	}
	return 0
}

func (m *ListUserComplaintReq) GetOpStatus() uint32 {
	if m != nil {
		return m.OpStatus
	}
	return 0
}

func (m *ListUserComplaintReq) GetIds() []uint64 {
	if m != nil {
		return m.Ids
	}
	return nil
}

func (m *ListUserComplaintReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *ListUserComplaintReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

func (m *ListUserComplaintReq) GetOrderIds() []string {
	if m != nil {
		return m.OrderIds
	}
	return nil
}

type ListUserComplaintResp struct {
	Total                uint32           `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List                 []*ComplaintInfo `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *ListUserComplaintResp) Reset()         { *m = ListUserComplaintResp{} }
func (m *ListUserComplaintResp) String() string { return proto.CompactTextString(m) }
func (*ListUserComplaintResp) ProtoMessage()    {}
func (*ListUserComplaintResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{18}
}
func (m *ListUserComplaintResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListUserComplaintResp.Unmarshal(m, b)
}
func (m *ListUserComplaintResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListUserComplaintResp.Marshal(b, m, deterministic)
}
func (dst *ListUserComplaintResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListUserComplaintResp.Merge(dst, src)
}
func (m *ListUserComplaintResp) XXX_Size() int {
	return xxx_messageInfo_ListUserComplaintResp.Size(m)
}
func (m *ListUserComplaintResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListUserComplaintResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListUserComplaintResp proto.InternalMessageInfo

func (m *ListUserComplaintResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *ListUserComplaintResp) GetList() []*ComplaintInfo {
	if m != nil {
		return m.List
	}
	return nil
}

type GetUserComplaintInfoReq struct {
	RecordId             uint32   `protobuf:"varint,1,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserComplaintInfoReq) Reset()         { *m = GetUserComplaintInfoReq{} }
func (m *GetUserComplaintInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserComplaintInfoReq) ProtoMessage()    {}
func (*GetUserComplaintInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{19}
}
func (m *GetUserComplaintInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserComplaintInfoReq.Unmarshal(m, b)
}
func (m *GetUserComplaintInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserComplaintInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserComplaintInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserComplaintInfoReq.Merge(dst, src)
}
func (m *GetUserComplaintInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserComplaintInfoReq.Size(m)
}
func (m *GetUserComplaintInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserComplaintInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserComplaintInfoReq proto.InternalMessageInfo

func (m *GetUserComplaintInfoReq) GetRecordId() uint32 {
	if m != nil {
		return m.RecordId
	}
	return 0
}

type GetUserComplaintInfoResp struct {
	Info                 *ComplaintInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetUserComplaintInfoResp) Reset()         { *m = GetUserComplaintInfoResp{} }
func (m *GetUserComplaintInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserComplaintInfoResp) ProtoMessage()    {}
func (*GetUserComplaintInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{20}
}
func (m *GetUserComplaintInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserComplaintInfoResp.Unmarshal(m, b)
}
func (m *GetUserComplaintInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserComplaintInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserComplaintInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserComplaintInfoResp.Merge(dst, src)
}
func (m *GetUserComplaintInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserComplaintInfoResp.Size(m)
}
func (m *GetUserComplaintInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserComplaintInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserComplaintInfoResp proto.InternalMessageInfo

func (m *GetUserComplaintInfoResp) GetInfo() *ComplaintInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

// 批量处理归档
type BatchHandleUserComplaintReq struct {
	HandleType           uint32   `protobuf:"varint,1,opt,name=handle_type,json=handleType,proto3" json:"handle_type,omitempty"`
	RecordIds            []uint32 `protobuf:"varint,2,rep,packed,name=record_ids,json=recordIds,proto3" json:"record_ids,omitempty"`
	Handler              string   `protobuf:"bytes,3,opt,name=handler,proto3" json:"handler,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchHandleUserComplaintReq) Reset()         { *m = BatchHandleUserComplaintReq{} }
func (m *BatchHandleUserComplaintReq) String() string { return proto.CompactTextString(m) }
func (*BatchHandleUserComplaintReq) ProtoMessage()    {}
func (*BatchHandleUserComplaintReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{21}
}
func (m *BatchHandleUserComplaintReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchHandleUserComplaintReq.Unmarshal(m, b)
}
func (m *BatchHandleUserComplaintReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchHandleUserComplaintReq.Marshal(b, m, deterministic)
}
func (dst *BatchHandleUserComplaintReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchHandleUserComplaintReq.Merge(dst, src)
}
func (m *BatchHandleUserComplaintReq) XXX_Size() int {
	return xxx_messageInfo_BatchHandleUserComplaintReq.Size(m)
}
func (m *BatchHandleUserComplaintReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchHandleUserComplaintReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchHandleUserComplaintReq proto.InternalMessageInfo

func (m *BatchHandleUserComplaintReq) GetHandleType() uint32 {
	if m != nil {
		return m.HandleType
	}
	return 0
}

func (m *BatchHandleUserComplaintReq) GetRecordIds() []uint32 {
	if m != nil {
		return m.RecordIds
	}
	return nil
}

func (m *BatchHandleUserComplaintReq) GetHandler() string {
	if m != nil {
		return m.Handler
	}
	return ""
}

type BatchHandleUserComplaintResp struct {
	Total                uint32   `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	FailCnt              uint32   `protobuf:"varint,2,opt,name=fail_cnt,json=failCnt,proto3" json:"fail_cnt,omitempty"`
	FailOrderIds         []string `protobuf:"bytes,3,rep,name=fail_order_ids,json=failOrderIds,proto3" json:"fail_order_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchHandleUserComplaintResp) Reset()         { *m = BatchHandleUserComplaintResp{} }
func (m *BatchHandleUserComplaintResp) String() string { return proto.CompactTextString(m) }
func (*BatchHandleUserComplaintResp) ProtoMessage()    {}
func (*BatchHandleUserComplaintResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{22}
}
func (m *BatchHandleUserComplaintResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchHandleUserComplaintResp.Unmarshal(m, b)
}
func (m *BatchHandleUserComplaintResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchHandleUserComplaintResp.Marshal(b, m, deterministic)
}
func (dst *BatchHandleUserComplaintResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchHandleUserComplaintResp.Merge(dst, src)
}
func (m *BatchHandleUserComplaintResp) XXX_Size() int {
	return xxx_messageInfo_BatchHandleUserComplaintResp.Size(m)
}
func (m *BatchHandleUserComplaintResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchHandleUserComplaintResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchHandleUserComplaintResp proto.InternalMessageInfo

func (m *BatchHandleUserComplaintResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *BatchHandleUserComplaintResp) GetFailCnt() uint32 {
	if m != nil {
		return m.FailCnt
	}
	return 0
}

func (m *BatchHandleUserComplaintResp) GetFailOrderIds() []string {
	if m != nil {
		return m.FailOrderIds
	}
	return nil
}

// 批量发起归档
type BatchApplyUserComplaintReq struct {
	RecordIds            []uint32 `protobuf:"varint,1,rep,packed,name=record_ids,json=recordIds,proto3" json:"record_ids,omitempty"`
	Handler              string   `protobuf:"bytes,2,opt,name=handler,proto3" json:"handler,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchApplyUserComplaintReq) Reset()         { *m = BatchApplyUserComplaintReq{} }
func (m *BatchApplyUserComplaintReq) String() string { return proto.CompactTextString(m) }
func (*BatchApplyUserComplaintReq) ProtoMessage()    {}
func (*BatchApplyUserComplaintReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{23}
}
func (m *BatchApplyUserComplaintReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchApplyUserComplaintReq.Unmarshal(m, b)
}
func (m *BatchApplyUserComplaintReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchApplyUserComplaintReq.Marshal(b, m, deterministic)
}
func (dst *BatchApplyUserComplaintReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchApplyUserComplaintReq.Merge(dst, src)
}
func (m *BatchApplyUserComplaintReq) XXX_Size() int {
	return xxx_messageInfo_BatchApplyUserComplaintReq.Size(m)
}
func (m *BatchApplyUserComplaintReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchApplyUserComplaintReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchApplyUserComplaintReq proto.InternalMessageInfo

func (m *BatchApplyUserComplaintReq) GetRecordIds() []uint32 {
	if m != nil {
		return m.RecordIds
	}
	return nil
}

func (m *BatchApplyUserComplaintReq) GetHandler() string {
	if m != nil {
		return m.Handler
	}
	return ""
}

type BatchApplyUserComplaintResp struct {
	Total                uint32   `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	FailCnt              uint32   `protobuf:"varint,2,opt,name=fail_cnt,json=failCnt,proto3" json:"fail_cnt,omitempty"`
	FailOrderIds         []string `protobuf:"bytes,3,rep,name=fail_order_ids,json=failOrderIds,proto3" json:"fail_order_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchApplyUserComplaintResp) Reset()         { *m = BatchApplyUserComplaintResp{} }
func (m *BatchApplyUserComplaintResp) String() string { return proto.CompactTextString(m) }
func (*BatchApplyUserComplaintResp) ProtoMessage()    {}
func (*BatchApplyUserComplaintResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{24}
}
func (m *BatchApplyUserComplaintResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchApplyUserComplaintResp.Unmarshal(m, b)
}
func (m *BatchApplyUserComplaintResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchApplyUserComplaintResp.Marshal(b, m, deterministic)
}
func (dst *BatchApplyUserComplaintResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchApplyUserComplaintResp.Merge(dst, src)
}
func (m *BatchApplyUserComplaintResp) XXX_Size() int {
	return xxx_messageInfo_BatchApplyUserComplaintResp.Size(m)
}
func (m *BatchApplyUserComplaintResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchApplyUserComplaintResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchApplyUserComplaintResp proto.InternalMessageInfo

func (m *BatchApplyUserComplaintResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *BatchApplyUserComplaintResp) GetFailCnt() uint32 {
	if m != nil {
		return m.FailCnt
	}
	return 0
}

func (m *BatchApplyUserComplaintResp) GetFailOrderIds() []string {
	if m != nil {
		return m.FailOrderIds
	}
	return nil
}

// 批量通知用户
type BatchNotifyUsersReq struct {
	RecordIds            []uint32 `protobuf:"varint,1,rep,packed,name=record_ids,json=recordIds,proto3" json:"record_ids,omitempty"`
	OpMsg                string   `protobuf:"bytes,2,opt,name=op_msg,json=opMsg,proto3" json:"op_msg,omitempty"`
	Handler              string   `protobuf:"bytes,3,opt,name=handler,proto3" json:"handler,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchNotifyUsersReq) Reset()         { *m = BatchNotifyUsersReq{} }
func (m *BatchNotifyUsersReq) String() string { return proto.CompactTextString(m) }
func (*BatchNotifyUsersReq) ProtoMessage()    {}
func (*BatchNotifyUsersReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{25}
}
func (m *BatchNotifyUsersReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchNotifyUsersReq.Unmarshal(m, b)
}
func (m *BatchNotifyUsersReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchNotifyUsersReq.Marshal(b, m, deterministic)
}
func (dst *BatchNotifyUsersReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchNotifyUsersReq.Merge(dst, src)
}
func (m *BatchNotifyUsersReq) XXX_Size() int {
	return xxx_messageInfo_BatchNotifyUsersReq.Size(m)
}
func (m *BatchNotifyUsersReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchNotifyUsersReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchNotifyUsersReq proto.InternalMessageInfo

func (m *BatchNotifyUsersReq) GetRecordIds() []uint32 {
	if m != nil {
		return m.RecordIds
	}
	return nil
}

func (m *BatchNotifyUsersReq) GetOpMsg() string {
	if m != nil {
		return m.OpMsg
	}
	return ""
}

func (m *BatchNotifyUsersReq) GetHandler() string {
	if m != nil {
		return m.Handler
	}
	return ""
}

type BatchNotifyUsersResp struct {
	Total                uint32   `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	FailCnt              uint32   `protobuf:"varint,2,opt,name=fail_cnt,json=failCnt,proto3" json:"fail_cnt,omitempty"`
	FailOrderIds         []string `protobuf:"bytes,3,rep,name=fail_order_ids,json=failOrderIds,proto3" json:"fail_order_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchNotifyUsersResp) Reset()         { *m = BatchNotifyUsersResp{} }
func (m *BatchNotifyUsersResp) String() string { return proto.CompactTextString(m) }
func (*BatchNotifyUsersResp) ProtoMessage()    {}
func (*BatchNotifyUsersResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{26}
}
func (m *BatchNotifyUsersResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchNotifyUsersResp.Unmarshal(m, b)
}
func (m *BatchNotifyUsersResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchNotifyUsersResp.Marshal(b, m, deterministic)
}
func (dst *BatchNotifyUsersResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchNotifyUsersResp.Merge(dst, src)
}
func (m *BatchNotifyUsersResp) XXX_Size() int {
	return xxx_messageInfo_BatchNotifyUsersResp.Size(m)
}
func (m *BatchNotifyUsersResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchNotifyUsersResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchNotifyUsersResp proto.InternalMessageInfo

func (m *BatchNotifyUsersResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *BatchNotifyUsersResp) GetFailCnt() uint32 {
	if m != nil {
		return m.FailCnt
	}
	return 0
}

func (m *BatchNotifyUsersResp) GetFailOrderIds() []string {
	if m != nil {
		return m.FailOrderIds
	}
	return nil
}

// 编辑 业务运营处理说明
type UpdateOpMsgReq struct {
	RecordId             uint32   `protobuf:"varint,1,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"`
	OpMsg                string   `protobuf:"bytes,2,opt,name=op_msg,json=opMsg,proto3" json:"op_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateOpMsgReq) Reset()         { *m = UpdateOpMsgReq{} }
func (m *UpdateOpMsgReq) String() string { return proto.CompactTextString(m) }
func (*UpdateOpMsgReq) ProtoMessage()    {}
func (*UpdateOpMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{27}
}
func (m *UpdateOpMsgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateOpMsgReq.Unmarshal(m, b)
}
func (m *UpdateOpMsgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateOpMsgReq.Marshal(b, m, deterministic)
}
func (dst *UpdateOpMsgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateOpMsgReq.Merge(dst, src)
}
func (m *UpdateOpMsgReq) XXX_Size() int {
	return xxx_messageInfo_UpdateOpMsgReq.Size(m)
}
func (m *UpdateOpMsgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateOpMsgReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateOpMsgReq proto.InternalMessageInfo

func (m *UpdateOpMsgReq) GetRecordId() uint32 {
	if m != nil {
		return m.RecordId
	}
	return 0
}

func (m *UpdateOpMsgReq) GetOpMsg() string {
	if m != nil {
		return m.OpMsg
	}
	return ""
}

type UpdateOpMsgResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateOpMsgResp) Reset()         { *m = UpdateOpMsgResp{} }
func (m *UpdateOpMsgResp) String() string { return proto.CompactTextString(m) }
func (*UpdateOpMsgResp) ProtoMessage()    {}
func (*UpdateOpMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{28}
}
func (m *UpdateOpMsgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateOpMsgResp.Unmarshal(m, b)
}
func (m *UpdateOpMsgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateOpMsgResp.Marshal(b, m, deterministic)
}
func (dst *UpdateOpMsgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateOpMsgResp.Merge(dst, src)
}
func (m *UpdateOpMsgResp) XXX_Size() int {
	return xxx_messageInfo_UpdateOpMsgResp.Size(m)
}
func (m *UpdateOpMsgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateOpMsgResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateOpMsgResp proto.InternalMessageInfo

// 编辑 备注
type UpdateRemarkReq struct {
	RecordId             uint32   `protobuf:"varint,1,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"`
	Handler              string   `protobuf:"bytes,2,opt,name=handler,proto3" json:"handler,omitempty"`
	Remark               string   `protobuf:"bytes,3,opt,name=remark,proto3" json:"remark,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateRemarkReq) Reset()         { *m = UpdateRemarkReq{} }
func (m *UpdateRemarkReq) String() string { return proto.CompactTextString(m) }
func (*UpdateRemarkReq) ProtoMessage()    {}
func (*UpdateRemarkReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{29}
}
func (m *UpdateRemarkReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateRemarkReq.Unmarshal(m, b)
}
func (m *UpdateRemarkReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateRemarkReq.Marshal(b, m, deterministic)
}
func (dst *UpdateRemarkReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateRemarkReq.Merge(dst, src)
}
func (m *UpdateRemarkReq) XXX_Size() int {
	return xxx_messageInfo_UpdateRemarkReq.Size(m)
}
func (m *UpdateRemarkReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateRemarkReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateRemarkReq proto.InternalMessageInfo

func (m *UpdateRemarkReq) GetRecordId() uint32 {
	if m != nil {
		return m.RecordId
	}
	return 0
}

func (m *UpdateRemarkReq) GetHandler() string {
	if m != nil {
		return m.Handler
	}
	return ""
}

func (m *UpdateRemarkReq) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

type UpdateRemarkResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateRemarkResp) Reset()         { *m = UpdateRemarkResp{} }
func (m *UpdateRemarkResp) String() string { return proto.CompactTextString(m) }
func (*UpdateRemarkResp) ProtoMessage()    {}
func (*UpdateRemarkResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_complaint_381ac889a9006595, []int{30}
}
func (m *UpdateRemarkResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateRemarkResp.Unmarshal(m, b)
}
func (m *UpdateRemarkResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateRemarkResp.Marshal(b, m, deterministic)
}
func (dst *UpdateRemarkResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateRemarkResp.Merge(dst, src)
}
func (m *UpdateRemarkResp) XXX_Size() int {
	return xxx_messageInfo_UpdateRemarkResp.Size(m)
}
func (m *UpdateRemarkResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateRemarkResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateRemarkResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*GetUserComplaintEntryReq)(nil), "user_complaint.GetUserComplaintEntryReq")
	proto.RegisterType((*GetUserComplaintEntryResp)(nil), "user_complaint.GetUserComplaintEntryResp")
	proto.RegisterType((*ComplaintInfo)(nil), "user_complaint.ComplaintInfo")
	proto.RegisterType((*MediaInfo)(nil), "user_complaint.MediaInfo")
	proto.RegisterType((*RecordInfo)(nil), "user_complaint.RecordInfo")
	proto.RegisterType((*GetUserComplaintListReq)(nil), "user_complaint.GetUserComplaintListReq")
	proto.RegisterType((*GetUserComplaintListResp)(nil), "user_complaint.GetUserComplaintListResp")
	proto.RegisterType((*CreateComplaintReq)(nil), "user_complaint.CreateComplaintReq")
	proto.RegisterType((*CreateComplaintResp)(nil), "user_complaint.CreateComplaintResp")
	proto.RegisterType((*GetGuildComplaintListReq)(nil), "user_complaint.GetGuildComplaintListReq")
	proto.RegisterType((*GetGuildComplaintListResp)(nil), "user_complaint.GetGuildComplaintListResp")
	proto.RegisterType((*SubmitGuildComplaintReq)(nil), "user_complaint.SubmitGuildComplaintReq")
	proto.RegisterType((*SubmitGuildComplaintResp)(nil), "user_complaint.SubmitGuildComplaintResp")
	proto.RegisterType((*ArchiveUserComplaintReq)(nil), "user_complaint.ArchiveUserComplaintReq")
	proto.RegisterType((*ArchiveUserComplaintResp)(nil), "user_complaint.ArchiveUserComplaintResp")
	proto.RegisterType((*GetUserComplaintByRecordIdReq)(nil), "user_complaint.GetUserComplaintByRecordIdReq")
	proto.RegisterType((*GetUserComplaintByRecordIdResp)(nil), "user_complaint.GetUserComplaintByRecordIdResp")
	proto.RegisterType((*ListUserComplaintReq)(nil), "user_complaint.ListUserComplaintReq")
	proto.RegisterType((*ListUserComplaintResp)(nil), "user_complaint.ListUserComplaintResp")
	proto.RegisterType((*GetUserComplaintInfoReq)(nil), "user_complaint.GetUserComplaintInfoReq")
	proto.RegisterType((*GetUserComplaintInfoResp)(nil), "user_complaint.GetUserComplaintInfoResp")
	proto.RegisterType((*BatchHandleUserComplaintReq)(nil), "user_complaint.BatchHandleUserComplaintReq")
	proto.RegisterType((*BatchHandleUserComplaintResp)(nil), "user_complaint.BatchHandleUserComplaintResp")
	proto.RegisterType((*BatchApplyUserComplaintReq)(nil), "user_complaint.BatchApplyUserComplaintReq")
	proto.RegisterType((*BatchApplyUserComplaintResp)(nil), "user_complaint.BatchApplyUserComplaintResp")
	proto.RegisterType((*BatchNotifyUsersReq)(nil), "user_complaint.BatchNotifyUsersReq")
	proto.RegisterType((*BatchNotifyUsersResp)(nil), "user_complaint.BatchNotifyUsersResp")
	proto.RegisterType((*UpdateOpMsgReq)(nil), "user_complaint.UpdateOpMsgReq")
	proto.RegisterType((*UpdateOpMsgResp)(nil), "user_complaint.UpdateOpMsgResp")
	proto.RegisterType((*UpdateRemarkReq)(nil), "user_complaint.UpdateRemarkReq")
	proto.RegisterType((*UpdateRemarkResp)(nil), "user_complaint.UpdateRemarkResp")
	proto.RegisterEnum("user_complaint.HANDLE_TYPE", HANDLE_TYPE_name, HANDLE_TYPE_value)
	proto.RegisterEnum("user_complaint.BUSINESS_TYPE", BUSINESS_TYPE_name, BUSINESS_TYPE_value)
	proto.RegisterEnum("user_complaint.COMPLAINT_OPR", COMPLAINT_OPR_name, COMPLAINT_OPR_value)
	proto.RegisterEnum("user_complaint.QUERY_TYPE", QUERY_TYPE_name, QUERY_TYPE_value)
	proto.RegisterEnum("user_complaint.COMPLAINT_TYPE", COMPLAINT_TYPE_name, COMPLAINT_TYPE_value)
	proto.RegisterEnum("user_complaint.PAY_TYPE", PAY_TYPE_name, PAY_TYPE_value)
	proto.RegisterEnum("user_complaint.ILLEGAL_TYPE", ILLEGAL_TYPE_name, ILLEGAL_TYPE_value)
	proto.RegisterEnum("user_complaint.COMPLAINT_STATUS_TYPE", COMPLAINT_STATUS_TYPE_name, COMPLAINT_STATUS_TYPE_value)
	proto.RegisterEnum("user_complaint.OP_TYPE", OP_TYPE_name, OP_TYPE_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// UserComplaintClient is the client API for UserComplaint service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UserComplaintClient interface {
	GetUserComplaintEntry(ctx context.Context, in *GetUserComplaintEntryReq, opts ...grpc.CallOption) (*GetUserComplaintEntryResp, error)
	// 1. 查看我的投诉
	GetUserComplaintList(ctx context.Context, in *GetUserComplaintListReq, opts ...grpc.CallOption) (*GetUserComplaintListResp, error)
	// 2. 用户提交投诉表单
	CreateComplaint(ctx context.Context, in *CreateComplaintReq, opts ...grpc.CallOption) (*CreateComplaintResp, error)
	// 3. 公会侧获取投诉列表
	GetGuildComplaintList(ctx context.Context, in *GetGuildComplaintListReq, opts ...grpc.CallOption) (*GetGuildComplaintListResp, error)
	// 4. 公会侧提交反馈表单
	SubmitGuildComplaint(ctx context.Context, in *SubmitGuildComplaintReq, opts ...grpc.CallOption) (*SubmitGuildComplaintResp, error)
	GetUserComplaintByRecordId(ctx context.Context, in *GetUserComplaintByRecordIdReq, opts ...grpc.CallOption) (*GetUserComplaintByRecordIdResp, error)
	// 归档 、废弃
	ArchiveUserComplaint(ctx context.Context, in *ArchiveUserComplaintReq, opts ...grpc.CallOption) (*ArchiveUserComplaintResp, error)
	// 查询投诉单
	ListUserComplaint(ctx context.Context, in *ListUserComplaintReq, opts ...grpc.CallOption) (*ListUserComplaintResp, error)
	// 获取投诉单详情
	GetUserComplaintInfo(ctx context.Context, in *GetUserComplaintInfoReq, opts ...grpc.CallOption) (*GetUserComplaintInfoResp, error)
	// 批量通知用户
	BatchNotifyUsers(ctx context.Context, in *BatchNotifyUsersReq, opts ...grpc.CallOption) (*BatchNotifyUsersResp, error)
	// 批量发起归档
	BatchApplyUserComplaint(ctx context.Context, in *BatchApplyUserComplaintReq, opts ...grpc.CallOption) (*BatchApplyUserComplaintResp, error)
	// 批量处理归档
	BatchHandleUserComplaint(ctx context.Context, in *BatchHandleUserComplaintReq, opts ...grpc.CallOption) (*BatchHandleUserComplaintResp, error)
	// 编辑 业务运营处理说明
	UpdateOpMsg(ctx context.Context, in *UpdateOpMsgReq, opts ...grpc.CallOption) (*UpdateOpMsgResp, error)
	// 编辑 备注
	UpdateRemark(ctx context.Context, in *UpdateRemarkReq, opts ...grpc.CallOption) (*UpdateRemarkResp, error)
}

type userComplaintClient struct {
	cc *grpc.ClientConn
}

func NewUserComplaintClient(cc *grpc.ClientConn) UserComplaintClient {
	return &userComplaintClient{cc}
}

func (c *userComplaintClient) GetUserComplaintEntry(ctx context.Context, in *GetUserComplaintEntryReq, opts ...grpc.CallOption) (*GetUserComplaintEntryResp, error) {
	out := new(GetUserComplaintEntryResp)
	err := c.cc.Invoke(ctx, "/user_complaint.UserComplaint/GetUserComplaintEntry", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userComplaintClient) GetUserComplaintList(ctx context.Context, in *GetUserComplaintListReq, opts ...grpc.CallOption) (*GetUserComplaintListResp, error) {
	out := new(GetUserComplaintListResp)
	err := c.cc.Invoke(ctx, "/user_complaint.UserComplaint/GetUserComplaintList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userComplaintClient) CreateComplaint(ctx context.Context, in *CreateComplaintReq, opts ...grpc.CallOption) (*CreateComplaintResp, error) {
	out := new(CreateComplaintResp)
	err := c.cc.Invoke(ctx, "/user_complaint.UserComplaint/CreateComplaint", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userComplaintClient) GetGuildComplaintList(ctx context.Context, in *GetGuildComplaintListReq, opts ...grpc.CallOption) (*GetGuildComplaintListResp, error) {
	out := new(GetGuildComplaintListResp)
	err := c.cc.Invoke(ctx, "/user_complaint.UserComplaint/GetGuildComplaintList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userComplaintClient) SubmitGuildComplaint(ctx context.Context, in *SubmitGuildComplaintReq, opts ...grpc.CallOption) (*SubmitGuildComplaintResp, error) {
	out := new(SubmitGuildComplaintResp)
	err := c.cc.Invoke(ctx, "/user_complaint.UserComplaint/SubmitGuildComplaint", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userComplaintClient) GetUserComplaintByRecordId(ctx context.Context, in *GetUserComplaintByRecordIdReq, opts ...grpc.CallOption) (*GetUserComplaintByRecordIdResp, error) {
	out := new(GetUserComplaintByRecordIdResp)
	err := c.cc.Invoke(ctx, "/user_complaint.UserComplaint/GetUserComplaintByRecordId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userComplaintClient) ArchiveUserComplaint(ctx context.Context, in *ArchiveUserComplaintReq, opts ...grpc.CallOption) (*ArchiveUserComplaintResp, error) {
	out := new(ArchiveUserComplaintResp)
	err := c.cc.Invoke(ctx, "/user_complaint.UserComplaint/ArchiveUserComplaint", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userComplaintClient) ListUserComplaint(ctx context.Context, in *ListUserComplaintReq, opts ...grpc.CallOption) (*ListUserComplaintResp, error) {
	out := new(ListUserComplaintResp)
	err := c.cc.Invoke(ctx, "/user_complaint.UserComplaint/ListUserComplaint", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userComplaintClient) GetUserComplaintInfo(ctx context.Context, in *GetUserComplaintInfoReq, opts ...grpc.CallOption) (*GetUserComplaintInfoResp, error) {
	out := new(GetUserComplaintInfoResp)
	err := c.cc.Invoke(ctx, "/user_complaint.UserComplaint/GetUserComplaintInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userComplaintClient) BatchNotifyUsers(ctx context.Context, in *BatchNotifyUsersReq, opts ...grpc.CallOption) (*BatchNotifyUsersResp, error) {
	out := new(BatchNotifyUsersResp)
	err := c.cc.Invoke(ctx, "/user_complaint.UserComplaint/BatchNotifyUsers", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userComplaintClient) BatchApplyUserComplaint(ctx context.Context, in *BatchApplyUserComplaintReq, opts ...grpc.CallOption) (*BatchApplyUserComplaintResp, error) {
	out := new(BatchApplyUserComplaintResp)
	err := c.cc.Invoke(ctx, "/user_complaint.UserComplaint/BatchApplyUserComplaint", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userComplaintClient) BatchHandleUserComplaint(ctx context.Context, in *BatchHandleUserComplaintReq, opts ...grpc.CallOption) (*BatchHandleUserComplaintResp, error) {
	out := new(BatchHandleUserComplaintResp)
	err := c.cc.Invoke(ctx, "/user_complaint.UserComplaint/BatchHandleUserComplaint", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userComplaintClient) UpdateOpMsg(ctx context.Context, in *UpdateOpMsgReq, opts ...grpc.CallOption) (*UpdateOpMsgResp, error) {
	out := new(UpdateOpMsgResp)
	err := c.cc.Invoke(ctx, "/user_complaint.UserComplaint/UpdateOpMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userComplaintClient) UpdateRemark(ctx context.Context, in *UpdateRemarkReq, opts ...grpc.CallOption) (*UpdateRemarkResp, error) {
	out := new(UpdateRemarkResp)
	err := c.cc.Invoke(ctx, "/user_complaint.UserComplaint/UpdateRemark", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserComplaintServer is the server API for UserComplaint service.
type UserComplaintServer interface {
	GetUserComplaintEntry(context.Context, *GetUserComplaintEntryReq) (*GetUserComplaintEntryResp, error)
	// 1. 查看我的投诉
	GetUserComplaintList(context.Context, *GetUserComplaintListReq) (*GetUserComplaintListResp, error)
	// 2. 用户提交投诉表单
	CreateComplaint(context.Context, *CreateComplaintReq) (*CreateComplaintResp, error)
	// 3. 公会侧获取投诉列表
	GetGuildComplaintList(context.Context, *GetGuildComplaintListReq) (*GetGuildComplaintListResp, error)
	// 4. 公会侧提交反馈表单
	SubmitGuildComplaint(context.Context, *SubmitGuildComplaintReq) (*SubmitGuildComplaintResp, error)
	GetUserComplaintByRecordId(context.Context, *GetUserComplaintByRecordIdReq) (*GetUserComplaintByRecordIdResp, error)
	// 归档 、废弃
	ArchiveUserComplaint(context.Context, *ArchiveUserComplaintReq) (*ArchiveUserComplaintResp, error)
	// 查询投诉单
	ListUserComplaint(context.Context, *ListUserComplaintReq) (*ListUserComplaintResp, error)
	// 获取投诉单详情
	GetUserComplaintInfo(context.Context, *GetUserComplaintInfoReq) (*GetUserComplaintInfoResp, error)
	// 批量通知用户
	BatchNotifyUsers(context.Context, *BatchNotifyUsersReq) (*BatchNotifyUsersResp, error)
	// 批量发起归档
	BatchApplyUserComplaint(context.Context, *BatchApplyUserComplaintReq) (*BatchApplyUserComplaintResp, error)
	// 批量处理归档
	BatchHandleUserComplaint(context.Context, *BatchHandleUserComplaintReq) (*BatchHandleUserComplaintResp, error)
	// 编辑 业务运营处理说明
	UpdateOpMsg(context.Context, *UpdateOpMsgReq) (*UpdateOpMsgResp, error)
	// 编辑 备注
	UpdateRemark(context.Context, *UpdateRemarkReq) (*UpdateRemarkResp, error)
}

func RegisterUserComplaintServer(s *grpc.Server, srv UserComplaintServer) {
	s.RegisterService(&_UserComplaint_serviceDesc, srv)
}

func _UserComplaint_GetUserComplaintEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserComplaintEntryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserComplaintServer).GetUserComplaintEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_complaint.UserComplaint/GetUserComplaintEntry",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserComplaintServer).GetUserComplaintEntry(ctx, req.(*GetUserComplaintEntryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserComplaint_GetUserComplaintList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserComplaintListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserComplaintServer).GetUserComplaintList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_complaint.UserComplaint/GetUserComplaintList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserComplaintServer).GetUserComplaintList(ctx, req.(*GetUserComplaintListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserComplaint_CreateComplaint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateComplaintReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserComplaintServer).CreateComplaint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_complaint.UserComplaint/CreateComplaint",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserComplaintServer).CreateComplaint(ctx, req.(*CreateComplaintReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserComplaint_GetGuildComplaintList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildComplaintListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserComplaintServer).GetGuildComplaintList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_complaint.UserComplaint/GetGuildComplaintList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserComplaintServer).GetGuildComplaintList(ctx, req.(*GetGuildComplaintListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserComplaint_SubmitGuildComplaint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitGuildComplaintReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserComplaintServer).SubmitGuildComplaint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_complaint.UserComplaint/SubmitGuildComplaint",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserComplaintServer).SubmitGuildComplaint(ctx, req.(*SubmitGuildComplaintReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserComplaint_GetUserComplaintByRecordId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserComplaintByRecordIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserComplaintServer).GetUserComplaintByRecordId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_complaint.UserComplaint/GetUserComplaintByRecordId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserComplaintServer).GetUserComplaintByRecordId(ctx, req.(*GetUserComplaintByRecordIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserComplaint_ArchiveUserComplaint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArchiveUserComplaintReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserComplaintServer).ArchiveUserComplaint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_complaint.UserComplaint/ArchiveUserComplaint",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserComplaintServer).ArchiveUserComplaint(ctx, req.(*ArchiveUserComplaintReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserComplaint_ListUserComplaint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUserComplaintReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserComplaintServer).ListUserComplaint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_complaint.UserComplaint/ListUserComplaint",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserComplaintServer).ListUserComplaint(ctx, req.(*ListUserComplaintReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserComplaint_GetUserComplaintInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserComplaintInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserComplaintServer).GetUserComplaintInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_complaint.UserComplaint/GetUserComplaintInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserComplaintServer).GetUserComplaintInfo(ctx, req.(*GetUserComplaintInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserComplaint_BatchNotifyUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchNotifyUsersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserComplaintServer).BatchNotifyUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_complaint.UserComplaint/BatchNotifyUsers",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserComplaintServer).BatchNotifyUsers(ctx, req.(*BatchNotifyUsersReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserComplaint_BatchApplyUserComplaint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchApplyUserComplaintReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserComplaintServer).BatchApplyUserComplaint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_complaint.UserComplaint/BatchApplyUserComplaint",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserComplaintServer).BatchApplyUserComplaint(ctx, req.(*BatchApplyUserComplaintReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserComplaint_BatchHandleUserComplaint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchHandleUserComplaintReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserComplaintServer).BatchHandleUserComplaint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_complaint.UserComplaint/BatchHandleUserComplaint",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserComplaintServer).BatchHandleUserComplaint(ctx, req.(*BatchHandleUserComplaintReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserComplaint_UpdateOpMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateOpMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserComplaintServer).UpdateOpMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_complaint.UserComplaint/UpdateOpMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserComplaintServer).UpdateOpMsg(ctx, req.(*UpdateOpMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserComplaint_UpdateRemark_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRemarkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserComplaintServer).UpdateRemark(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_complaint.UserComplaint/UpdateRemark",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserComplaintServer).UpdateRemark(ctx, req.(*UpdateRemarkReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _UserComplaint_serviceDesc = grpc.ServiceDesc{
	ServiceName: "user_complaint.UserComplaint",
	HandlerType: (*UserComplaintServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserComplaintEntry",
			Handler:    _UserComplaint_GetUserComplaintEntry_Handler,
		},
		{
			MethodName: "GetUserComplaintList",
			Handler:    _UserComplaint_GetUserComplaintList_Handler,
		},
		{
			MethodName: "CreateComplaint",
			Handler:    _UserComplaint_CreateComplaint_Handler,
		},
		{
			MethodName: "GetGuildComplaintList",
			Handler:    _UserComplaint_GetGuildComplaintList_Handler,
		},
		{
			MethodName: "SubmitGuildComplaint",
			Handler:    _UserComplaint_SubmitGuildComplaint_Handler,
		},
		{
			MethodName: "GetUserComplaintByRecordId",
			Handler:    _UserComplaint_GetUserComplaintByRecordId_Handler,
		},
		{
			MethodName: "ArchiveUserComplaint",
			Handler:    _UserComplaint_ArchiveUserComplaint_Handler,
		},
		{
			MethodName: "ListUserComplaint",
			Handler:    _UserComplaint_ListUserComplaint_Handler,
		},
		{
			MethodName: "GetUserComplaintInfo",
			Handler:    _UserComplaint_GetUserComplaintInfo_Handler,
		},
		{
			MethodName: "BatchNotifyUsers",
			Handler:    _UserComplaint_BatchNotifyUsers_Handler,
		},
		{
			MethodName: "BatchApplyUserComplaint",
			Handler:    _UserComplaint_BatchApplyUserComplaint_Handler,
		},
		{
			MethodName: "BatchHandleUserComplaint",
			Handler:    _UserComplaint_BatchHandleUserComplaint_Handler,
		},
		{
			MethodName: "UpdateOpMsg",
			Handler:    _UserComplaint_UpdateOpMsg_Handler,
		},
		{
			MethodName: "UpdateRemark",
			Handler:    _UserComplaint_UpdateRemark_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "user-complaint/user-complaint.proto",
}

func init() {
	proto.RegisterFile("user-complaint/user-complaint.proto", fileDescriptor_user_complaint_381ac889a9006595)
}

var fileDescriptor_user_complaint_381ac889a9006595 = []byte{
	// 1972 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x59, 0xdd, 0x72, 0xa3, 0xc8,
	0xf5, 0xb7, 0x3e, 0xc6, 0x92, 0x8e, 0x2c, 0x1b, 0xb7, 0xbf, 0x30, 0x33, 0x1e, 0x7b, 0x99, 0xf9,
	0xd7, 0xf8, 0xaf, 0x64, 0x3d, 0x99, 0x49, 0x65, 0x2b, 0x17, 0xb9, 0xd1, 0x48, 0x8c, 0xad, 0x44,
	0x96, 0x34, 0x08, 0xed, 0xc6, 0x5b, 0xa9, 0x62, 0x31, 0x60, 0x9b, 0x18, 0x01, 0x03, 0x68, 0xb7,
	0x9c, 0x4a, 0x5e, 0x21, 0xaf, 0x92, 0x17, 0xc8, 0x7b, 0xe4, 0x22, 0x2f, 0x93, 0xea, 0xd3, 0x80,
	0x84, 0x40, 0x96, 0x77, 0x6b, 0xf7, 0xae, 0xfb, 0x77, 0x4e, 0x9f, 0x6f, 0x4e, 0x9f, 0x96, 0xe0,
	0xd5, 0x34, 0x30, 0xfd, 0x2f, 0x75, 0x77, 0xe2, 0xd9, 0x9a, 0xe5, 0x84, 0x6f, 0xd3, 0xdb, 0x33,
	0xcf, 0x77, 0x43, 0x97, 0x6c, 0x52, 0x54, 0x4d, 0x50, 0xf1, 0xdf, 0x05, 0xe0, 0xcf, 0xcd, 0x70,
	0x1c, 0x98, 0x7e, 0x3b, 0x06, 0x25, 0x27, 0xf4, 0x1f, 0x64, 0xf3, 0x33, 0xe1, 0xa0, 0x34, 0xb5,
	0x0c, 0xbe, 0x70, 0x52, 0x38, 0x6d, 0xc8, 0x74, 0x49, 0x4e, 0x81, 0xbb, 0x9d, 0x5a, 0xb6, 0xa1,
	0x1a, 0x56, 0xe0, 0xd9, 0xda, 0x83, 0x6a, 0x19, 0x7c, 0x11, 0xc9, 0x9b, 0x88, 0x77, 0x18, 0xdc,
	0x35, 0xc8, 0x11, 0x80, 0xe6, 0xe8, 0x77, 0xae, 0xaf, 0x52, 0x11, 0x25, 0xe4, 0xa9, 0x31, 0x64,
	0x6c, 0x19, 0xe4, 0x0b, 0xd8, 0xd0, 0xef, 0x34, 0xc7, 0x31, 0x6d, 0xd5, 0xd1, 0x26, 0x26, 0x5f,
	0x3e, 0x29, 0x9c, 0xd6, 0xe4, 0x7a, 0x84, 0xf5, 0xb5, 0x89, 0x49, 0x44, 0xd8, 0x08, 0x4d, 0x7f,
	0x62, 0x39, 0x9a, 0xad, 0x3c, 0x78, 0x26, 0xff, 0x0c, 0x65, 0xa4, 0x30, 0xf1, 0x2b, 0x38, 0x5c,
	0x62, 0x7d, 0xe0, 0x91, 0x43, 0xa8, 0xfe, 0x75, 0x3a, 0xf1, 0xd4, 0xa9, 0x6f, 0xa3, 0x0f, 0x35,
	0xb9, 0x42, 0xf7, 0x63, 0xdf, 0x16, 0xff, 0x59, 0x85, 0x46, 0x72, 0xa2, 0xeb, 0xdc, 0xb8, 0xe4,
	0x39, 0xd4, 0x7c, 0x53, 0x77, 0x7d, 0x43, 0x4d, 0x3c, 0xae, 0x32, 0xa0, 0x6b, 0x50, 0x49, 0xae,
	0x6f, 0x98, 0x7e, 0xec, 0x6e, 0x4d, 0xae, 0xe0, 0xbe, 0x6b, 0x10, 0x02, 0xe5, 0x90, 0x5a, 0xc7,
	0x3c, 0xc4, 0x35, 0x79, 0x05, 0x8d, 0xeb, 0x69, 0x60, 0x39, 0x66, 0x10, 0xa8, 0x48, 0x2c, 0x33,
	0xd3, 0x63, 0x90, 0x9a, 0x4e, 0x15, 0xba, 0x9e, 0x1a, 0x84, 0x5a, 0x38, 0x0d, 0x22, 0xdf, 0xaa,
	0xae, 0x37, 0xc2, 0x3d, 0x0d, 0x0f, 0x8b, 0x73, 0x44, 0x5f, 0x47, 0x7a, 0x1d, 0xb1, 0x88, 0x25,
	0x4a, 0x4e, 0x65, 0x96, 0x1c, 0x6a, 0x4a, 0x68, 0x19, 0x7c, 0x15, 0x2d, 0xc4, 0x35, 0x11, 0xa0,
	0xea, 0x58, 0xfa, 0x3d, 0xc6, 0xb8, 0x86, 0x78, 0xb2, 0x27, 0x3c, 0x54, 0x34, 0x5d, 0x77, 0xa7,
	0x4e, 0xc8, 0x03, 0x73, 0x2a, 0xda, 0xe6, 0xa6, 0xb9, 0x9e, 0x9b, 0xe6, 0x43, 0xa8, 0x32, 0x4e,
	0xcb, 0xe0, 0x37, 0x90, 0xa3, 0x82, 0xfb, 0x6e, 0x36, 0xc5, 0x8d, 0x6c, 0x8a, 0xdf, 0xc0, 0x96,
	0xab, 0xeb, 0x53, 0xdf, 0x37, 0x1d, 0xdd, 0x54, 0x43, 0x6b, 0x62, 0xf2, 0x9b, 0x4c, 0xcd, 0x0c,
	0x56, 0xac, 0x89, 0x49, 0x8e, 0xa1, 0x1e, 0x6a, 0xfe, 0xad, 0x19, 0xaa, 0xe8, 0xe1, 0x16, 0x8a,
	0x02, 0x06, 0x29, 0xd4, 0xcf, 0x37, 0xb0, 0x15, 0x31, 0x24, 0xee, 0x72, 0xc8, 0xb4, 0xc9, 0xe0,
	0x7e, 0xec, 0xf4, 0x2e, 0x3c, 0x9b, 0xb8, 0x8e, 0xf9, 0xc0, 0x6f, 0xa3, 0x22, 0xb6, 0xa1, 0xb6,
	0x5a, 0xb6, 0x6d, 0xde, 0x6a, 0x36, 0x4b, 0x18, 0x61, 0xf1, 0x8e, 0x30, 0xcc, 0xd7, 0x2e, 0x3c,
	0xf3, 0xee, 0x5c, 0xc7, 0xe4, 0x77, 0x50, 0x2e, 0xdb, 0x60, 0xcc, 0x2d, 0x2f, 0xe0, 0x77, 0xa3,
	0x98, 0x5b, 0x5e, 0x40, 0x63, 0xe2, 0x59, 0xba, 0x6a, 0x5b, 0x41, 0xc8, 0xef, 0x9d, 0x94, 0x68,
	0x60, 0x3d, 0x4b, 0xef, 0x59, 0x41, 0x48, 0xfd, 0xd0, 0x7d, 0x53, 0x0b, 0x23, 0x67, 0xf7, 0x51,
	0x0d, 0x30, 0x08, 0x1d, 0x15, 0xa0, 0x7a, 0x63, 0x9a, 0xc6, 0xb5, 0xa6, 0xdf, 0xf3, 0x07, 0x2c,
	0x5f, 0xf1, 0x9e, 0x1a, 0x79, 0xa7, 0x39, 0x86, 0x6d, 0xfa, 0xec, 0x34, 0xcf, 0x8c, 0x8c, 0x30,
	0x3c, 0xfe, 0x1c, 0x6a, 0x56, 0xa0, 0x3a, 0x6e, 0x68, 0xdd, 0x3c, 0xf0, 0x87, 0x27, 0x85, 0xd3,
	0xaa, 0x5c, 0xb5, 0x82, 0x3e, 0xee, 0xc9, 0x1e, 0xac, 0xbb, 0x9e, 0x3a, 0x09, 0x6e, 0x79, 0x81,
	0xb9, 0xe0, 0x7a, 0x97, 0xc1, 0x2d, 0xd9, 0x87, 0x75, 0xdf, 0x9c, 0x68, 0xfe, 0x3d, 0xff, 0x1c,
	0xe1, 0x68, 0x47, 0x3a, 0xc0, 0xc5, 0x5f, 0x84, 0x73, 0xe3, 0x32, 0x77, 0x5e, 0x9c, 0x94, 0x4e,
	0xeb, 0xef, 0x85, 0xb3, 0x74, 0x17, 0x39, 0x93, 0xd9, 0x87, 0xe2, 0xdc, 0xb8, 0xf2, 0xa6, 0x9f,
	0xac, 0xd1, 0xe3, 0x37, 0xb0, 0xa5, 0xf9, 0xfa, 0x9d, 0xf5, 0xbd, 0xa9, 0x46, 0x86, 0xf2, 0x47,
	0x2c, 0x31, 0x11, 0x7c, 0xc1, 0x50, 0xea, 0x5d, 0xcc, 0x88, 0xde, 0xbd, 0x64, 0xde, 0x45, 0x58,
	0x5c, 0x05, 0x53, 0xcf, 0x48, 0xa2, 0x77, 0xcc, 0xa2, 0xc7, 0x20, 0x64, 0xf8, 0x3d, 0xc0, 0xc4,
	0x34, 0x2c, 0x8d, 0x19, 0x7b, 0x82, 0xc6, 0x1e, 0x2e, 0x1a, 0x7b, 0x49, 0x39, 0xd0, 0xd6, 0x1a,
	0x32, 0x53, 0x33, 0xc5, 0x77, 0x50, 0x4b, 0x70, 0x9a, 0x54, 0xac, 0x20, 0xd6, 0x34, 0x70, 0x8d,
	0x9f, 0x9b, 0x6f, 0x47, 0x5f, 0x3f, 0x5d, 0x8a, 0x7f, 0x06, 0x98, 0xf9, 0x4d, 0x0e, 0xa0, 0xe2,
	0x7a, 0xac, 0x78, 0x58, 0xf7, 0x58, 0x77, 0x3d, 0xac, 0x1b, 0x1e, 0x2a, 0xb1, 0xe3, 0x51, 0xeb,
	0x88, 0xb6, 0xf1, 0x11, 0xea, 0x4a, 0x29, 0x39, 0x62, 0x4d, 0x4c, 0xf1, 0x01, 0x0e, 0x16, 0xbb,
	0x1a, 0x35, 0x32, 0xbf, 0x25, 0x13, 0x28, 0x7b, 0xda, 0xad, 0x19, 0xb5, 0x61, 0x5c, 0x63, 0x05,
	0x6a, 0xb7, 0xa6, 0xea, 0x4c, 0x27, 0x91, 0xe8, 0x0a, 0xdd, 0xf7, 0xa7, 0x13, 0xec, 0xcb, 0xb6,
	0x1d, 0xf7, 0x95, 0x32, 0x96, 0x48, 0x4d, 0xb3, 0x6d, 0xd6, 0x55, 0x44, 0x3d, 0x7b, 0x1d, 0x30,
	0xd5, 0x81, 0x47, 0xbf, 0x80, 0xd0, 0x0d, 0x35, 0x3b, 0xd2, 0xce, 0x36, 0xe4, 0x1d, 0x94, 0x31,
	0xda, 0x45, 0x8c, 0xf6, 0xd1, 0x62, 0xb4, 0x53, 0x5d, 0x56, 0x46, 0x56, 0xf1, 0x1c, 0x48, 0x1b,
	0x4b, 0x3e, 0x21, 0x52, 0xd7, 0xde, 0x41, 0x99, 0x16, 0x1a, 0x4a, 0x5f, 0x2d, 0x88, 0xb2, 0x8a,
	0x7b, 0xb0, 0x93, 0x11, 0x14, 0x78, 0xe2, 0xdf, 0xd1, 0x89, 0x73, 0xda, 0x87, 0x32, 0x01, 0x9c,
	0x6f, 0x58, 0x85, 0x74, 0xc3, 0xfa, 0x91, 0x91, 0xdc, 0x87, 0xf5, 0xb9, 0x28, 0x36, 0xe4, 0x68,
	0x27, 0x1a, 0x78, 0x27, 0xe5, 0x69, 0xff, 0x39, 0x63, 0x28, 0xc3, 0xc1, 0x68, 0x7a, 0x3d, 0xb1,
	0x16, 0x14, 0x51, 0x17, 0x1f, 0xbd, 0xca, 0xe6, 0x1b, 0x4c, 0x31, 0xdd, 0x60, 0x44, 0x01, 0xf8,
	0x7c, 0x99, 0x81, 0x27, 0xde, 0xc3, 0x41, 0x8b, 0x7d, 0x8a, 0xa9, 0xe2, 0x58, 0xa9, 0x6f, 0xa1,
	0xe3, 0x15, 0x33, 0x1d, 0x2f, 0xbe, 0xb5, 0x4a, 0xb3, 0x5b, 0x8b, 0x1a, 0x92, 0xaf, 0x2c, 0xf0,
	0xc4, 0x3f, 0xc0, 0xd1, 0x62, 0x85, 0x7e, 0x78, 0x88, 0x3e, 0x44, 0x63, 0x95, 0x39, 0xe2, 0x08,
	0x5e, 0x3e, 0x76, 0x3a, 0xf0, 0x7e, 0x4a, 0x19, 0xfe, 0xa7, 0x00, 0xbb, 0x34, 0xc3, 0x99, 0xc8,
	0x64, 0x06, 0x81, 0x42, 0xce, 0x20, 0x70, 0x0c, 0xf5, 0xcf, 0x53, 0xd3, 0x7f, 0x88, 0x58, 0xa2,
	0x08, 0x31, 0x28, 0x3b, 0x29, 0x94, 0x16, 0x26, 0x05, 0x0e, 0x4a, 0x96, 0x41, 0x4b, 0xb0, 0x74,
	0x5a, 0x96, 0xe9, 0x32, 0x29, 0xe3, 0x67, 0x4b, 0xca, 0x78, 0x3d, 0x5d, 0xc6, 0x54, 0x7a, 0x34,
	0xdb, 0x04, 0x7c, 0x05, 0xaf, 0xab, 0x6a, 0x34, 0xdc, 0x04, 0xe2, 0x77, 0xb0, 0x97, 0xe3, 0xd8,
	0xcf, 0x59, 0xc7, 0x5f, 0x65, 0x7b, 0x1d, 0x52, 0x57, 0x25, 0xf2, 0x32, 0xdb, 0xa8, 0xd8, 0xb9,
	0x9f, 0x96, 0xc2, 0x1f, 0xe0, 0xf9, 0x07, 0x2d, 0xd4, 0xef, 0xd8, 0x6d, 0x94, 0x49, 0xe4, 0x31,
	0x44, 0xd7, 0xec, 0x7c, 0x1a, 0x81, 0x41, 0x98, 0xa3, 0x23, 0x80, 0xc4, 0xd6, 0x00, 0xfd, 0x6f,
	0xc8, 0xb5, 0xd8, 0xd8, 0x60, 0xfe, 0x12, 0x28, 0xa5, 0x2e, 0x01, 0x71, 0x0a, 0x2f, 0x96, 0x2b,
	0x5e, 0x1a, 0xe8, 0x43, 0xa8, 0xde, 0x68, 0x96, 0xad, 0xea, 0x4e, 0x18, 0x15, 0x4c, 0x85, 0xee,
	0xdb, 0x4e, 0x48, 0x5e, 0xc3, 0x26, 0x92, 0x66, 0x49, 0x2d, 0x61, 0x52, 0x37, 0x28, 0x3a, 0x88,
	0x13, 0x3b, 0x06, 0x01, 0xd5, 0xb6, 0x3c, 0xcf, 0x7e, 0xc8, 0xb8, 0x9b, 0xf6, 0xa6, 0xf0, 0x88,
	0x37, 0xe9, 0x2b, 0x4d, 0x0c, 0xa3, 0x30, 0xe6, 0x89, 0xfd, 0xe5, 0x9c, 0x31, 0x61, 0x07, 0xb5,
	0xb2, 0x39, 0x87, 0xaa, 0x0d, 0x9e, 0xe0, 0xc5, 0x6c, 0x1c, 0x2a, 0xce, 0x8f, 0x43, 0xcb, 0x53,
	0x35, 0x81, 0xdd, 0xac, 0x9a, 0x5f, 0xce, 0xab, 0x0e, 0x6c, 0x8e, 0x71, 0xb4, 0x19, 0x50, 0xbb,
	0x56, 0x36, 0xda, 0x7c, 0x77, 0xc4, 0x6d, 0xd8, 0x4a, 0x49, 0x09, 0x3c, 0xf1, 0xbb, 0x18, 0x92,
	0x71, 0xd0, 0x5b, 0x29, 0x79, 0xf9, 0x04, 0x33, 0x1b, 0x1d, 0x4b, 0xf3, 0xa3, 0xa3, 0x48, 0x80,
	0x4b, 0x6b, 0x08, 0xbc, 0x66, 0x1b, 0xea, 0x17, 0xad, 0x7e, 0xa7, 0x27, 0xa9, 0xca, 0xd5, 0x50,
	0x22, 0x2f, 0x80, 0x9f, 0xdb, 0xaa, 0xad, 0x73, 0x59, 0x92, 0xd4, 0x56, 0xbb, 0x2d, 0x0d, 0x15,
	0x6e, 0x8d, 0xec, 0x03, 0x99, 0xa7, 0xca, 0xd2, 0x1f, 0xa5, 0xb6, 0xc2, 0x15, 0x9a, 0xdf, 0x40,
	0xe3, 0xc3, 0x78, 0xd4, 0xed, 0x4b, 0xa3, 0x11, 0x13, 0xb3, 0x07, 0xdb, 0x29, 0x40, 0xfd, 0x38,
	0xee, 0xb3, 0xf3, 0x69, 0xb8, 0xd7, 0xfd, 0x5a, 0xe2, 0x0a, 0xe4, 0x00, 0x76, 0xd2, 0xf8, 0x40,
	0xb9, 0x90, 0x64, 0xae, 0xd8, 0xfc, 0x16, 0x1a, 0xed, 0xc1, 0xe5, 0xb0, 0xd7, 0xea, 0xf6, 0x15,
	0x75, 0x30, 0x94, 0xa9, 0x84, 0x14, 0xa0, 0xf6, 0x07, 0x7d, 0x89, 0x5b, 0x23, 0x87, 0xb0, 0x97,
	0xc6, 0xa9, 0x9d, 0xdd, 0xfe, 0x39, 0x57, 0xc8, 0x1e, 0xe9, 0xd0, 0x23, 0xc5, 0xe6, 0x67, 0x80,
	0x4f, 0x63, 0x49, 0xbe, 0x62, 0x16, 0xef, 0xc0, 0xd6, 0x6c, 0xa7, 0x2a, 0x4a, 0xb7, 0xc3, 0xad,
	0xd1, 0x68, 0xcc, 0x81, 0xe7, 0xe3, 0x6e, 0xaf, 0xa3, 0x8e, 0x2e, 0x06, 0x32, 0xa5, 0xa2, 0xd5,
	0x19, 0x6a, 0xb7, 0xc3, 0x15, 0x17, 0x08, 0x03, 0xb9, 0x23, 0xc9, 0x94, 0x50, 0x6a, 0xfe, 0xab,
	0x00, 0x9b, 0x33, 0x5b, 0x50, 0x2f, 0x0f, 0xbb, 0x69, 0x44, 0x95, 0x2e, 0x87, 0xca, 0x15, 0xb7,
	0x96, 0x43, 0x19, 0x0d, 0xbb, 0x0a, 0x0d, 0x57, 0x96, 0xa2, 0xc8, 0xad, 0x8e, 0xc4, 0x15, 0x89,
	0x00, 0xfb, 0x0b, 0x94, 0xaf, 0xbb, 0x83, 0x5e, 0x4b, 0x91, 0xb8, 0x52, 0x3a, 0x44, 0x48, 0xbb,
	0x68, 0xf5, 0xcf, 0xc7, 0x43, 0xae, 0x9c, 0x23, 0x90, 0x25, 0xe0, 0x59, 0xd3, 0x86, 0xea, 0xb0,
	0x15, 0x85, 0x88, 0xc0, 0x66, 0xbc, 0x4e, 0x8c, 0xdc, 0x86, 0x46, 0x82, 0x9d, 0xb3, 0x64, 0xee,
	0xc0, 0x56, 0x02, 0x7d, 0x23, 0xb5, 0x2f, 0x5a, 0x0a, 0x57, 0x24, 0x5b, 0x50, 0x4f, 0xc0, 0x4f,
	0x9f, 0xb8, 0x52, 0x8a, 0xab, 0xd5, 0xeb, 0x0e, 0x5b, 0x57, 0x5c, 0xb9, 0xe9, 0xc1, 0x46, 0xb7,
	0xd7, 0x93, 0xce, 0x5b, 0x3d, 0xa6, 0x71, 0x1f, 0xc8, 0xfc, 0x7e, 0x3e, 0x34, 0x29, 0x7c, 0x28,
	0xc9, 0x1f, 0x07, 0xf2, 0x25, 0x4b, 0x76, 0x8a, 0xa2, 0x0c, 0x86, 0xdd, 0x36, 0x57, 0xcc, 0xe0,
	0xcc, 0xbf, 0x52, 0xf3, 0x6f, 0xf3, 0x41, 0x19, 0x29, 0x2d, 0x65, 0x1c, 0x55, 0xf0, 0x4b, 0x10,
	0x72, 0x09, 0xea, 0xb8, 0xdf, 0x19, 0x70, 0x6b, 0xcb, 0xe9, 0x58, 0x5d, 0x05, 0xf2, 0x05, 0x1c,
	0xe5, 0xd3, 0x5b, 0x72, 0xfb, 0x82, 0x06, 0xaa, 0xd8, 0xbc, 0x83, 0xca, 0x60, 0x98, 0x84, 0x36,
	0x5a, 0xaa, 0xfd, 0x81, 0xd2, 0xfd, 0x48, 0x9d, 0x9c, 0xc3, 0x64, 0xe9, 0xb2, 0x25, 0xff, 0x89,
	0x2b, 0xd0, 0x1c, 0xc6, 0x58, 0x6b, 0x38, 0xec, 0x5d, 0xcd, 0xa4, 0xd1, 0xd4, 0xcf, 0xd8, 0xe9,
	0x77, 0x99, 0xd0, 0x4a, 0xef, 0xff, 0x5b, 0x87, 0x46, 0xaa, 0xed, 0x13, 0x07, 0xf6, 0x72, 0x7f,
	0xa1, 0x21, 0xa7, 0x8b, 0xd7, 0xf2, 0xb2, 0x9f, 0xa1, 0x84, 0xff, 0x7f, 0x22, 0x67, 0xe0, 0x89,
	0x6b, 0xe4, 0x1e, 0x76, 0xf3, 0x1e, 0x30, 0xe4, 0xcd, 0x2a, 0x21, 0xd1, 0x03, 0x41, 0x38, 0x7d,
	0x1a, 0x23, 0x2a, 0xfb, 0x0b, 0x6c, 0x2d, 0xbc, 0x3f, 0x88, 0x98, 0x99, 0x36, 0x32, 0x2f, 0x1d,
	0xe1, 0xd5, 0x4a, 0x1e, 0x94, 0xce, 0x42, 0x97, 0x7d, 0x48, 0xe4, 0x86, 0x2e, 0xf7, 0xb5, 0x93,
	0x1b, 0xba, 0xfc, 0x97, 0x09, 0x0b, 0x5d, 0xde, 0xf8, 0x9f, 0x0d, 0xdd, 0x92, 0x87, 0x47, 0x36,
	0x74, 0x4b, 0x5f, 0x13, 0x6b, 0xe4, 0x1f, 0x20, 0x2c, 0x1f, 0xc4, 0xc9, 0x97, 0xab, 0x92, 0x90,
	0x1a, 0xf9, 0x85, 0xb3, 0x1f, 0xc3, 0x1e, 0xfb, 0x9a, 0xf7, 0xc2, 0xc8, 0xfa, 0xba, 0xe4, 0xd1,
	0x93, 0xf5, 0x75, 0xe9, 0x83, 0x65, 0x8d, 0x5c, 0xc3, 0x76, 0x66, 0x8a, 0x26, 0xaf, 0x17, 0x05,
	0xe4, 0xbd, 0x20, 0x84, 0xff, 0x7b, 0x02, 0xd7, 0xb2, 0xba, 0xc7, 0xdf, 0x25, 0x56, 0xd6, 0x7d,
	0x34, 0x6d, 0xaf, 0xae, 0xfb, 0x78, 0xbc, 0x16, 0xd7, 0x88, 0x0a, 0xdc, 0xe2, 0x24, 0x44, 0x32,
	0x45, 0x9d, 0x33, 0x92, 0x09, 0xaf, 0x57, 0x33, 0xa1, 0x82, 0x10, 0x0e, 0x96, 0xcc, 0x91, 0xa4,
	0x99, 0x2b, 0x22, 0x77, 0x8e, 0x15, 0x7e, 0xf5, 0x64, 0x5e, 0xd4, 0xfa, 0x03, 0xf0, 0xcb, 0x66,
	0x71, 0x92, 0x2f, 0x2a, 0xff, 0xb9, 0x20, 0xfc, 0xfa, 0xe9, 0xcc, 0xa8, 0x78, 0x08, 0xf5, 0xb9,
	0x21, 0x8d, 0xbc, 0x5c, 0x3c, 0x9e, 0x9e, 0x03, 0x85, 0xe3, 0x47, 0xe9, 0x28, 0x71, 0x04, 0x1b,
	0xf3, 0x13, 0x18, 0x59, 0x72, 0x24, 0x99, 0x00, 0x85, 0x93, 0xc7, 0x19, 0xa8, 0xd0, 0x0f, 0xef,
	0xbf, 0xfd, 0xcd, 0xad, 0x6b, 0x6b, 0xce, 0xed, 0xd9, 0xef, 0xde, 0x87, 0xe1, 0x99, 0xee, 0x4e,
	0xde, 0xe2, 0xbf, 0x0a, 0xba, 0x6b, 0xbf, 0x0d, 0x4c, 0xff, 0x7b, 0x4b, 0x37, 0x83, 0x85, 0xbf,
	0x1d, 0xae, 0xd7, 0x91, 0xe3, 0xb7, 0xff, 0x0b, 0x00, 0x00, 0xff, 0xff, 0x10, 0x77, 0x53, 0x8b,
	0x9e, 0x18, 0x00, 0x00,
}
