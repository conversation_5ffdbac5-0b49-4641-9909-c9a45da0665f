// Code generated by protoc-gen-gogo.
// source: src/backpacksvr/backpack.proto
// DO NOT EDIT!

/*
	Package backpack is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/backpacksvr/backpack.proto

	It has these top-level messages:
		PackageCfg
		PackageItemCfg
		FuncCardCfg
		LotteryFragmentCfg
		UserBackpackItem
		UserBackpackLog
		AddPackageCfgReq
		AddPackageCfgResp
		DelPackageCfgReq
		DelPackageCfgResp
		GetPackageCfgReq
		GetPackageCfgResp
		AddPackageItemCfgReq
		AddPackageItemCfgResp
		ModPackageItemCfgReq
		ModPackageItemCfgResp
		DelPackageItemCfgReq
		DelPackageItemCfgResp
		GetPackageItemCfgReq
		GetPackageItemCfgResp
		GiveUserPackageReq
		GiveUserPackageResp
		UseBackpackItemReq
		UseBackpackItemResp
		ProcBackpackItemTimeoutReq
		ProcBackpackItemTimeoutResp
		GetUserBackpackReq
		GetUserBackpackResp
		GetUserFuncCardUseReq
		GetUserFuncCardUseResp
		AddFuncCardCfgReq
		AddFuncCardCfgResp
		DelFuncCardCfgReq
		DelFuncCardCfgResp
		GetFuncCardCfgReq
		GetFuncCardCfgResp
		CheckUserFuncCardUseReq
		CheckUserFuncCardUseResp
		SetUserFuncCardUseReq
		SetUserFuncCardUseResp
		GetUserBackpackLogReq
		GetUserBackpackLogResp
		AddItemCfgReq
		AddItemCfgResp
		DelItemCfgReq
		DelItemCfgResp
		GetItemCfgReq
		GetItemCfgResp
		UseItemInfo
		TransactionInfo
		FreeZeItemReq
		FreeZeItemResp
		UserPackageSum
		GetUserPackageSumReq
		GetUserPackageSumResp
		GetOrderCountByTimeRangeReq
		GetOrderCountByTimeRangeResp
		GetOrderListByTimeRangeReq
		GetOrderListByTimeRangeResp
		ConversionItemReq
		ConversionItemResp
*/
package backpack

import (
	"fmt"

	"github.com/gogo/protobuf/proto"

	"math"

	_ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

	"golang.org/x/net/context"

	"google.golang.org/grpc"

	io1 "io"

	fmt2 "fmt"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type LogType int32

const (
	LogType_LOG_TYPE_INVALID           LogType = 0
	LogType_LOG_TYPE_USE               LogType = 1
	LogType_LOG_TYPE_EXPIRE            LogType = 2
	LogType_LOG_TYPE_FRAGMENT_EXCHANGE LogType = 3
	LogType_LOG_TYPE_FRAGMENT_ROLLBACK LogType = 4
	LogType_LOG_TYPE_ITEM_CONVERSION   LogType = 5
)

var LogType_name = map[int32]string{
	0: "LOG_TYPE_INVALID",
	1: "LOG_TYPE_USE",
	2: "LOG_TYPE_EXPIRE",
	3: "LOG_TYPE_FRAGMENT_EXCHANGE",
	4: "LOG_TYPE_FRAGMENT_ROLLBACK",
	5: "LOG_TYPE_ITEM_CONVERSION",
}
var LogType_value = map[string]int32{
	"LOG_TYPE_INVALID":           0,
	"LOG_TYPE_USE":               1,
	"LOG_TYPE_EXPIRE":            2,
	"LOG_TYPE_FRAGMENT_EXCHANGE": 3,
	"LOG_TYPE_FRAGMENT_ROLLBACK": 4,
	"LOG_TYPE_ITEM_CONVERSION":   5,
}

func (x LogType) String() string {
	return proto.EnumName(LogType_name, int32(x))
}
func (LogType) EnumDescriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{0} }

type PackageItemType int32

const (
	PackageItemType_UNKNOW_ITEM_TYPE          PackageItemType = 0
	PackageItemType_BACKPACK_PRESENT          PackageItemType = 1
	PackageItemType_BACKPACK_CARD_RICH_EXP    PackageItemType = 2
	PackageItemType_BACKPACK_CARD_CHARM_EXP   PackageItemType = 3
	PackageItemType_BACKPACK_LOTTERY_FRAGMENT PackageItemType = 4
)

var PackageItemType_name = map[int32]string{
	0: "UNKNOW_ITEM_TYPE",
	1: "BACKPACK_PRESENT",
	2: "BACKPACK_CARD_RICH_EXP",
	3: "BACKPACK_CARD_CHARM_EXP",
	4: "BACKPACK_LOTTERY_FRAGMENT",
}
var PackageItemType_value = map[string]int32{
	"UNKNOW_ITEM_TYPE":          0,
	"BACKPACK_PRESENT":          1,
	"BACKPACK_CARD_RICH_EXP":    2,
	"BACKPACK_CARD_CHARM_EXP":   3,
	"BACKPACK_LOTTERY_FRAGMENT": 4,
}

func (x PackageItemType) String() string {
	return proto.EnumName(PackageItemType_name, int32(x))
}
func (PackageItemType) EnumDescriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{1} }

// 包裹来源
type PackageSourceType int32

const (
	PackageSourceType_UNKNOW_PACKAGE_SOURCE                       PackageSourceType = 0
	PackageSourceType_PACKAGE_SOURCE_ACTIVITY_PRESENT             PackageSourceType = 1
	PackageSourceType_PACKAGE_SOURCE_DAILY_CHECKIN                PackageSourceType = 2
	PackageSourceType_PACKAGE_SOURCE_FIRST_RECHARGE               PackageSourceType = 3
	PackageSourceType_PACKAGE_SOURCE_OFFICIAL                     PackageSourceType = 4
	PackageSourceType_PACKAGE_SOURCE_SMASHEGG                     PackageSourceType = 5
	PackageSourceType_PACKAGE_SOURCE_CONVERSION                   PackageSourceType = 6
	PackageSourceType_PACKAGE_SOURCE_AWARD_CENTER                 PackageSourceType = 7
	PackageSourceType_PACKAGE_SOURCE_AWARD_YUYIN_LIVE_MISSION     PackageSourceType = 8
	PackageSourceType_PACKAGE_SOURCE_INTERACTION_INTIMACY_MISSION PackageSourceType = 9
	PackageSourceType_PACKAGE_SOURCE_ITEM_CONVERSION              PackageSourceType = 10
)

var PackageSourceType_name = map[int32]string{
	0:  "UNKNOW_PACKAGE_SOURCE",
	1:  "PACKAGE_SOURCE_ACTIVITY_PRESENT",
	2:  "PACKAGE_SOURCE_DAILY_CHECKIN",
	3:  "PACKAGE_SOURCE_FIRST_RECHARGE",
	4:  "PACKAGE_SOURCE_OFFICIAL",
	5:  "PACKAGE_SOURCE_SMASHEGG",
	6:  "PACKAGE_SOURCE_CONVERSION",
	7:  "PACKAGE_SOURCE_AWARD_CENTER",
	8:  "PACKAGE_SOURCE_AWARD_YUYIN_LIVE_MISSION",
	9:  "PACKAGE_SOURCE_INTERACTION_INTIMACY_MISSION",
	10: "PACKAGE_SOURCE_ITEM_CONVERSION",
}
var PackageSourceType_value = map[string]int32{
	"UNKNOW_PACKAGE_SOURCE":                       0,
	"PACKAGE_SOURCE_ACTIVITY_PRESENT":             1,
	"PACKAGE_SOURCE_DAILY_CHECKIN":                2,
	"PACKAGE_SOURCE_FIRST_RECHARGE":               3,
	"PACKAGE_SOURCE_OFFICIAL":                     4,
	"PACKAGE_SOURCE_SMASHEGG":                     5,
	"PACKAGE_SOURCE_CONVERSION":                   6,
	"PACKAGE_SOURCE_AWARD_CENTER":                 7,
	"PACKAGE_SOURCE_AWARD_YUYIN_LIVE_MISSION":     8,
	"PACKAGE_SOURCE_INTERACTION_INTIMACY_MISSION": 9,
	"PACKAGE_SOURCE_ITEM_CONVERSION":              10,
}

func (x PackageSourceType) String() string {
	return proto.EnumName(PackageSourceType_name, int32(x))
}
func (PackageSourceType) EnumDescriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{2} }

type FREEZETYPE int32

const (
	FREEZETYPE_FREEZETYPE_UNVALID  FREEZETYPE = 0
	FREEZETYPE_FREEZETYPE_PREPARE  FREEZETYPE = 1
	FREEZETYPE_FREEZETYPE_COMMIT   FREEZETYPE = 2
	FREEZETYPE_FREEZETYPE_ROLLBACK FREEZETYPE = 3
)

var FREEZETYPE_name = map[int32]string{
	0: "FREEZETYPE_UNVALID",
	1: "FREEZETYPE_PREPARE",
	2: "FREEZETYPE_COMMIT",
	3: "FREEZETYPE_ROLLBACK",
}
var FREEZETYPE_value = map[string]int32{
	"FREEZETYPE_UNVALID":  0,
	"FREEZETYPE_PREPARE":  1,
	"FREEZETYPE_COMMIT":   2,
	"FREEZETYPE_ROLLBACK": 3,
}

func (x FREEZETYPE) String() string {
	return proto.EnumName(FREEZETYPE_name, int32(x))
}
func (FREEZETYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{3} }

// 包裹配置
type PackageCfg struct {
	BgId  uint32 `protobuf:"varint,1,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
	Name  string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Desc  string `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	IsDel bool   `protobuf:"varint,4,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
}

func (m *PackageCfg) Reset()                    { *m = PackageCfg{} }
func (m *PackageCfg) String() string            { return proto.CompactTextString(m) }
func (*PackageCfg) ProtoMessage()               {}
func (*PackageCfg) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{0} }

func (m *PackageCfg) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

func (m *PackageCfg) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *PackageCfg) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *PackageCfg) GetIsDel() bool {
	if m != nil {
		return m.IsDel
	}
	return false
}

// 包裹项配置
type PackageItemCfg struct {
	BgItemId       uint32 `protobuf:"varint,1,opt,name=bg_item_id,json=bgItemId,proto3" json:"bg_item_id,omitempty"`
	BgId           uint32 `protobuf:"varint,2,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
	ItemType       uint32 `protobuf:"varint,3,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	SourceId       uint32 `protobuf:"varint,4,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	ItemCount      uint32 `protobuf:"varint,5,opt,name=item_count,json=itemCount,proto3" json:"item_count,omitempty"`
	FinTime        uint32 `protobuf:"varint,6,opt,name=fin_time,json=finTime,proto3" json:"fin_time,omitempty"`
	IsDel          bool   `protobuf:"varint,7,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
	Weight         uint32 `protobuf:"varint,8,opt,name=weight,proto3" json:"weight,omitempty"`
	DynamicFinTime uint32 `protobuf:"varint,9,opt,name=dynamic_fin_time,json=dynamicFinTime,proto3" json:"dynamic_fin_time,omitempty"`
}

func (m *PackageItemCfg) Reset()                    { *m = PackageItemCfg{} }
func (m *PackageItemCfg) String() string            { return proto.CompactTextString(m) }
func (*PackageItemCfg) ProtoMessage()               {}
func (*PackageItemCfg) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{1} }

func (m *PackageItemCfg) GetBgItemId() uint32 {
	if m != nil {
		return m.BgItemId
	}
	return 0
}

func (m *PackageItemCfg) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

func (m *PackageItemCfg) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *PackageItemCfg) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *PackageItemCfg) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

func (m *PackageItemCfg) GetFinTime() uint32 {
	if m != nil {
		return m.FinTime
	}
	return 0
}

func (m *PackageItemCfg) GetIsDel() bool {
	if m != nil {
		return m.IsDel
	}
	return false
}

func (m *PackageItemCfg) GetWeight() uint32 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *PackageItemCfg) GetDynamicFinTime() uint32 {
	if m != nil {
		return m.DynamicFinTime
	}
	return 0
}

// 功能卡片配置
type FuncCardCfg struct {
	CardId    uint32 `protobuf:"varint,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	CardType  uint32 `protobuf:"varint,2,opt,name=card_type,json=cardType,proto3" json:"card_type,omitempty"`
	CardName  string `protobuf:"bytes,3,opt,name=card_name,json=cardName,proto3" json:"card_name,omitempty"`
	CardDesc  string `protobuf:"bytes,4,opt,name=card_desc,json=cardDesc,proto3" json:"card_desc,omitempty"`
	CardUrl   string `protobuf:"bytes,5,opt,name=card_url,json=cardUrl,proto3" json:"card_url,omitempty"`
	CardTimes uint32 `protobuf:"varint,6,opt,name=card_times,json=cardTimes,proto3" json:"card_times,omitempty"`
	ValidTime uint32 `protobuf:"varint,7,opt,name=valid_time,json=validTime,proto3" json:"valid_time,omitempty"`
	IsDel     uint32 `protobuf:"varint,8,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
}

func (m *FuncCardCfg) Reset()                    { *m = FuncCardCfg{} }
func (m *FuncCardCfg) String() string            { return proto.CompactTextString(m) }
func (*FuncCardCfg) ProtoMessage()               {}
func (*FuncCardCfg) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{2} }

func (m *FuncCardCfg) GetCardId() uint32 {
	if m != nil {
		return m.CardId
	}
	return 0
}

func (m *FuncCardCfg) GetCardType() uint32 {
	if m != nil {
		return m.CardType
	}
	return 0
}

func (m *FuncCardCfg) GetCardName() string {
	if m != nil {
		return m.CardName
	}
	return ""
}

func (m *FuncCardCfg) GetCardDesc() string {
	if m != nil {
		return m.CardDesc
	}
	return ""
}

func (m *FuncCardCfg) GetCardUrl() string {
	if m != nil {
		return m.CardUrl
	}
	return ""
}

func (m *FuncCardCfg) GetCardTimes() uint32 {
	if m != nil {
		return m.CardTimes
	}
	return 0
}

func (m *FuncCardCfg) GetValidTime() uint32 {
	if m != nil {
		return m.ValidTime
	}
	return 0
}

func (m *FuncCardCfg) GetIsDel() uint32 {
	if m != nil {
		return m.IsDel
	}
	return 0
}

// 砸蛋得的抽奖碎片
type LotteryFragmentCfg struct {
	FragmentId    uint32 `protobuf:"varint,1,opt,name=fragment_id,json=fragmentId,proto3" json:"fragment_id,omitempty"`
	FragmentType  uint32 `protobuf:"varint,2,opt,name=fragment_type,json=fragmentType,proto3" json:"fragment_type,omitempty"`
	FragmentName  string `protobuf:"bytes,3,opt,name=fragment_name,json=fragmentName,proto3" json:"fragment_name,omitempty"`
	FragmentDesc  string `protobuf:"bytes,4,opt,name=fragment_desc,json=fragmentDesc,proto3" json:"fragment_desc,omitempty"`
	FragmentUrl   string `protobuf:"bytes,5,opt,name=fragment_url,json=fragmentUrl,proto3" json:"fragment_url,omitempty"`
	IsDel         uint32 `protobuf:"varint,6,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
	FragmentPrice uint32 `protobuf:"varint,7,opt,name=fragment_price,json=fragmentPrice,proto3" json:"fragment_price,omitempty"`
}

func (m *LotteryFragmentCfg) Reset()                    { *m = LotteryFragmentCfg{} }
func (m *LotteryFragmentCfg) String() string            { return proto.CompactTextString(m) }
func (*LotteryFragmentCfg) ProtoMessage()               {}
func (*LotteryFragmentCfg) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{3} }

func (m *LotteryFragmentCfg) GetFragmentId() uint32 {
	if m != nil {
		return m.FragmentId
	}
	return 0
}

func (m *LotteryFragmentCfg) GetFragmentType() uint32 {
	if m != nil {
		return m.FragmentType
	}
	return 0
}

func (m *LotteryFragmentCfg) GetFragmentName() string {
	if m != nil {
		return m.FragmentName
	}
	return ""
}

func (m *LotteryFragmentCfg) GetFragmentDesc() string {
	if m != nil {
		return m.FragmentDesc
	}
	return ""
}

func (m *LotteryFragmentCfg) GetFragmentUrl() string {
	if m != nil {
		return m.FragmentUrl
	}
	return ""
}

func (m *LotteryFragmentCfg) GetIsDel() uint32 {
	if m != nil {
		return m.IsDel
	}
	return 0
}

func (m *LotteryFragmentCfg) GetFragmentPrice() uint32 {
	if m != nil {
		return m.FragmentPrice
	}
	return 0
}

type UserBackpackItem struct {
	ItemType   uint32 `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	UserItemId uint32 `protobuf:"varint,2,opt,name=user_item_id,json=userItemId,proto3" json:"user_item_id,omitempty"`
	ItemCount  uint32 `protobuf:"varint,3,opt,name=item_count,json=itemCount,proto3" json:"item_count,omitempty"`
	FinTime    uint32 `protobuf:"varint,4,opt,name=fin_time,json=finTime,proto3" json:"fin_time,omitempty"`
	SourceId   uint32 `protobuf:"varint,5,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	Weight     uint32 `protobuf:"varint,6,opt,name=weight,proto3" json:"weight,omitempty"`
	ObtainTime uint32 `protobuf:"varint,7,opt,name=obtain_time,json=obtainTime,proto3" json:"obtain_time,omitempty"`
}

func (m *UserBackpackItem) Reset()                    { *m = UserBackpackItem{} }
func (m *UserBackpackItem) String() string            { return proto.CompactTextString(m) }
func (*UserBackpackItem) ProtoMessage()               {}
func (*UserBackpackItem) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{4} }

func (m *UserBackpackItem) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *UserBackpackItem) GetUserItemId() uint32 {
	if m != nil {
		return m.UserItemId
	}
	return 0
}

func (m *UserBackpackItem) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

func (m *UserBackpackItem) GetFinTime() uint32 {
	if m != nil {
		return m.FinTime
	}
	return 0
}

func (m *UserBackpackItem) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *UserBackpackItem) GetWeight() uint32 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *UserBackpackItem) GetObtainTime() uint32 {
	if m != nil {
		return m.ObtainTime
	}
	return 0
}

type UserBackpackLog struct {
	ItemType  uint32 `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	ItemCount uint32 `protobuf:"varint,2,opt,name=item_count,json=itemCount,proto3" json:"item_count,omitempty"`
	SourceId  uint32 `protobuf:"varint,3,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	LogType   uint32 `protobuf:"varint,4,opt,name=log_type,json=logType,proto3" json:"log_type,omitempty"`
	LogTime   uint32 `protobuf:"varint,5,opt,name=log_time,json=logTime,proto3" json:"log_time,omitempty"`
}

func (m *UserBackpackLog) Reset()                    { *m = UserBackpackLog{} }
func (m *UserBackpackLog) String() string            { return proto.CompactTextString(m) }
func (*UserBackpackLog) ProtoMessage()               {}
func (*UserBackpackLog) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{5} }

func (m *UserBackpackLog) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *UserBackpackLog) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

func (m *UserBackpackLog) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *UserBackpackLog) GetLogType() uint32 {
	if m != nil {
		return m.LogType
	}
	return 0
}

func (m *UserBackpackLog) GetLogTime() uint32 {
	if m != nil {
		return m.LogTime
	}
	return 0
}

type AddPackageCfgReq struct {
	Cfg *PackageCfg `protobuf:"bytes,1,opt,name=cfg" json:"cfg,omitempty"`
}

func (m *AddPackageCfgReq) Reset()                    { *m = AddPackageCfgReq{} }
func (m *AddPackageCfgReq) String() string            { return proto.CompactTextString(m) }
func (*AddPackageCfgReq) ProtoMessage()               {}
func (*AddPackageCfgReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{6} }

func (m *AddPackageCfgReq) GetCfg() *PackageCfg {
	if m != nil {
		return m.Cfg
	}
	return nil
}

type AddPackageCfgResp struct {
	Cfg *PackageCfg `protobuf:"bytes,1,opt,name=cfg" json:"cfg,omitempty"`
}

func (m *AddPackageCfgResp) Reset()                    { *m = AddPackageCfgResp{} }
func (m *AddPackageCfgResp) String() string            { return proto.CompactTextString(m) }
func (*AddPackageCfgResp) ProtoMessage()               {}
func (*AddPackageCfgResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{7} }

func (m *AddPackageCfgResp) GetCfg() *PackageCfg {
	if m != nil {
		return m.Cfg
	}
	return nil
}

type DelPackageCfgReq struct {
	BgId uint32 `protobuf:"varint,1,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
}

func (m *DelPackageCfgReq) Reset()                    { *m = DelPackageCfgReq{} }
func (m *DelPackageCfgReq) String() string            { return proto.CompactTextString(m) }
func (*DelPackageCfgReq) ProtoMessage()               {}
func (*DelPackageCfgReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{8} }

func (m *DelPackageCfgReq) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

type DelPackageCfgResp struct {
}

func (m *DelPackageCfgResp) Reset()                    { *m = DelPackageCfgResp{} }
func (m *DelPackageCfgResp) String() string            { return proto.CompactTextString(m) }
func (*DelPackageCfgResp) ProtoMessage()               {}
func (*DelPackageCfgResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{9} }

type GetPackageCfgReq struct {
}

func (m *GetPackageCfgReq) Reset()                    { *m = GetPackageCfgReq{} }
func (m *GetPackageCfgReq) String() string            { return proto.CompactTextString(m) }
func (*GetPackageCfgReq) ProtoMessage()               {}
func (*GetPackageCfgReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{10} }

type GetPackageCfgResp struct {
	CfgList []*PackageCfg `protobuf:"bytes,1,rep,name=cfg_list,json=cfgList" json:"cfg_list,omitempty"`
}

func (m *GetPackageCfgResp) Reset()                    { *m = GetPackageCfgResp{} }
func (m *GetPackageCfgResp) String() string            { return proto.CompactTextString(m) }
func (*GetPackageCfgResp) ProtoMessage()               {}
func (*GetPackageCfgResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{11} }

func (m *GetPackageCfgResp) GetCfgList() []*PackageCfg {
	if m != nil {
		return m.CfgList
	}
	return nil
}

type AddPackageItemCfgReq struct {
	ItemCfg *PackageItemCfg `protobuf:"bytes,1,opt,name=item_cfg,json=itemCfg" json:"item_cfg,omitempty"`
}

func (m *AddPackageItemCfgReq) Reset()                    { *m = AddPackageItemCfgReq{} }
func (m *AddPackageItemCfgReq) String() string            { return proto.CompactTextString(m) }
func (*AddPackageItemCfgReq) ProtoMessage()               {}
func (*AddPackageItemCfgReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{12} }

func (m *AddPackageItemCfgReq) GetItemCfg() *PackageItemCfg {
	if m != nil {
		return m.ItemCfg
	}
	return nil
}

type AddPackageItemCfgResp struct {
	ItemCfg *PackageItemCfg `protobuf:"bytes,1,opt,name=item_cfg,json=itemCfg" json:"item_cfg,omitempty"`
}

func (m *AddPackageItemCfgResp) Reset()                    { *m = AddPackageItemCfgResp{} }
func (m *AddPackageItemCfgResp) String() string            { return proto.CompactTextString(m) }
func (*AddPackageItemCfgResp) ProtoMessage()               {}
func (*AddPackageItemCfgResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{13} }

func (m *AddPackageItemCfgResp) GetItemCfg() *PackageItemCfg {
	if m != nil {
		return m.ItemCfg
	}
	return nil
}

type ModPackageItemCfgReq struct {
	ItemCfg *PackageItemCfg `protobuf:"bytes,1,opt,name=item_cfg,json=itemCfg" json:"item_cfg,omitempty"`
}

func (m *ModPackageItemCfgReq) Reset()                    { *m = ModPackageItemCfgReq{} }
func (m *ModPackageItemCfgReq) String() string            { return proto.CompactTextString(m) }
func (*ModPackageItemCfgReq) ProtoMessage()               {}
func (*ModPackageItemCfgReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{14} }

func (m *ModPackageItemCfgReq) GetItemCfg() *PackageItemCfg {
	if m != nil {
		return m.ItemCfg
	}
	return nil
}

type ModPackageItemCfgResp struct {
}

func (m *ModPackageItemCfgResp) Reset()                    { *m = ModPackageItemCfgResp{} }
func (m *ModPackageItemCfgResp) String() string            { return proto.CompactTextString(m) }
func (*ModPackageItemCfgResp) ProtoMessage()               {}
func (*ModPackageItemCfgResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{15} }

type DelPackageItemCfgReq struct {
	BgId     uint32 `protobuf:"varint,1,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
	BgItemId uint32 `protobuf:"varint,2,opt,name=bg_item_id,json=bgItemId,proto3" json:"bg_item_id,omitempty"`
}

func (m *DelPackageItemCfgReq) Reset()                    { *m = DelPackageItemCfgReq{} }
func (m *DelPackageItemCfgReq) String() string            { return proto.CompactTextString(m) }
func (*DelPackageItemCfgReq) ProtoMessage()               {}
func (*DelPackageItemCfgReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{16} }

func (m *DelPackageItemCfgReq) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

func (m *DelPackageItemCfgReq) GetBgItemId() uint32 {
	if m != nil {
		return m.BgItemId
	}
	return 0
}

type DelPackageItemCfgResp struct {
}

func (m *DelPackageItemCfgResp) Reset()                    { *m = DelPackageItemCfgResp{} }
func (m *DelPackageItemCfgResp) String() string            { return proto.CompactTextString(m) }
func (*DelPackageItemCfgResp) ProtoMessage()               {}
func (*DelPackageItemCfgResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{17} }

type GetPackageItemCfgReq struct {
	BgId uint32 `protobuf:"varint,1,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
}

func (m *GetPackageItemCfgReq) Reset()                    { *m = GetPackageItemCfgReq{} }
func (m *GetPackageItemCfgReq) String() string            { return proto.CompactTextString(m) }
func (*GetPackageItemCfgReq) ProtoMessage()               {}
func (*GetPackageItemCfgReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{18} }

func (m *GetPackageItemCfgReq) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

type GetPackageItemCfgResp struct {
	ItemCfgList []*PackageItemCfg `protobuf:"bytes,1,rep,name=item_cfg_list,json=itemCfgList" json:"item_cfg_list,omitempty"`
}

func (m *GetPackageItemCfgResp) Reset()                    { *m = GetPackageItemCfgResp{} }
func (m *GetPackageItemCfgResp) String() string            { return proto.CompactTextString(m) }
func (*GetPackageItemCfgResp) ProtoMessage()               {}
func (*GetPackageItemCfgResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{19} }

func (m *GetPackageItemCfgResp) GetItemCfgList() []*PackageItemCfg {
	if m != nil {
		return m.ItemCfgList
	}
	return nil
}

// user backpack
type GiveUserPackageReq struct {
	Uid            uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BgId           uint32 `protobuf:"varint,2,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
	Num            uint32 `protobuf:"varint,3,opt,name=num,proto3" json:"num,omitempty"`
	Source         uint32 `protobuf:"varint,4,opt,name=source,proto3" json:"source,omitempty"`
	OrderId        string `protobuf:"bytes,5,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	ExpireDuration uint32 `protobuf:"varint,6,opt,name=expire_duration,json=expireDuration,proto3" json:"expire_duration,omitempty"`
	// example:expire_duration=1,当前时间为 2019-10-28 10:42:00 这该物品过期时间为 2019-10-29 00:00:00
	// note :expire_duration>=1 则会忽略 包裹配置的 dynamic_fin_time
	SourceAppId string `protobuf:"bytes,7,opt,name=source_app_id,json=sourceAppId,proto3" json:"source_app_id,omitempty"`
	TotalPrice  uint32 `protobuf:"varint,8,opt,name=total_price,json=totalPrice,proto3" json:"total_price,omitempty"`
	OutsideTime uint32 `protobuf:"varint,9,opt,name=outside_time,json=outsideTime,proto3" json:"outside_time,omitempty"`
}

func (m *GiveUserPackageReq) Reset()                    { *m = GiveUserPackageReq{} }
func (m *GiveUserPackageReq) String() string            { return proto.CompactTextString(m) }
func (*GiveUserPackageReq) ProtoMessage()               {}
func (*GiveUserPackageReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{20} }

func (m *GiveUserPackageReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GiveUserPackageReq) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

func (m *GiveUserPackageReq) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *GiveUserPackageReq) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *GiveUserPackageReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *GiveUserPackageReq) GetExpireDuration() uint32 {
	if m != nil {
		return m.ExpireDuration
	}
	return 0
}

func (m *GiveUserPackageReq) GetSourceAppId() string {
	if m != nil {
		return m.SourceAppId
	}
	return ""
}

func (m *GiveUserPackageReq) GetTotalPrice() uint32 {
	if m != nil {
		return m.TotalPrice
	}
	return 0
}

func (m *GiveUserPackageReq) GetOutsideTime() uint32 {
	if m != nil {
		return m.OutsideTime
	}
	return 0
}

type GiveUserPackageResp struct {
}

func (m *GiveUserPackageResp) Reset()                    { *m = GiveUserPackageResp{} }
func (m *GiveUserPackageResp) String() string            { return proto.CompactTextString(m) }
func (*GiveUserPackageResp) ProtoMessage()               {}
func (*GiveUserPackageResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{21} }

type UseBackpackItemReq struct {
	Uid         uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ItemType    uint32   `protobuf:"varint,2,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	UserItemId  uint32   `protobuf:"varint,3,opt,name=user_item_id,json=userItemId,proto3" json:"user_item_id,omitempty"`
	SourceId    uint32   `protobuf:"varint,4,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	OrderId     string   `protobuf:"bytes,5,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	UseCount    uint32   `protobuf:"varint,6,opt,name=use_count,json=useCount,proto3" json:"use_count,omitempty"`
	OutsideTime uint32   `protobuf:"varint,7,opt,name=outside_time,json=outsideTime,proto3" json:"outside_time,omitempty"`
	OrderIdList []string `protobuf:"bytes,8,rep,name=order_id_list,json=orderIdList" json:"order_id_list,omitempty"`
}

func (m *UseBackpackItemReq) Reset()                    { *m = UseBackpackItemReq{} }
func (m *UseBackpackItemReq) String() string            { return proto.CompactTextString(m) }
func (*UseBackpackItemReq) ProtoMessage()               {}
func (*UseBackpackItemReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{22} }

func (m *UseBackpackItemReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UseBackpackItemReq) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *UseBackpackItemReq) GetUserItemId() uint32 {
	if m != nil {
		return m.UserItemId
	}
	return 0
}

func (m *UseBackpackItemReq) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *UseBackpackItemReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *UseBackpackItemReq) GetUseCount() uint32 {
	if m != nil {
		return m.UseCount
	}
	return 0
}

func (m *UseBackpackItemReq) GetOutsideTime() uint32 {
	if m != nil {
		return m.OutsideTime
	}
	return 0
}

func (m *UseBackpackItemReq) GetOrderIdList() []string {
	if m != nil {
		return m.OrderIdList
	}
	return nil
}

type UseBackpackItemResp struct {
	Remain uint32 `protobuf:"varint,1,opt,name=remain,proto3" json:"remain,omitempty"`
}

func (m *UseBackpackItemResp) Reset()                    { *m = UseBackpackItemResp{} }
func (m *UseBackpackItemResp) String() string            { return proto.CompactTextString(m) }
func (*UseBackpackItemResp) ProtoMessage()               {}
func (*UseBackpackItemResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{23} }

func (m *UseBackpackItemResp) GetRemain() uint32 {
	if m != nil {
		return m.Remain
	}
	return 0
}

type ProcBackpackItemTimeoutReq struct {
	UserItemId uint32 `protobuf:"varint,1,opt,name=user_item_id,json=userItemId,proto3" json:"user_item_id,omitempty"`
	Uid        uint32 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	ItemType   uint32 `protobuf:"varint,3,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	SourceId   uint32 `protobuf:"varint,4,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	ItemCount  uint32 `protobuf:"varint,5,opt,name=item_count,json=itemCount,proto3" json:"item_count,omitempty"`
	FinTime    uint32 `protobuf:"varint,6,opt,name=fin_time,json=finTime,proto3" json:"fin_time,omitempty"`
}

func (m *ProcBackpackItemTimeoutReq) Reset()         { *m = ProcBackpackItemTimeoutReq{} }
func (m *ProcBackpackItemTimeoutReq) String() string { return proto.CompactTextString(m) }
func (*ProcBackpackItemTimeoutReq) ProtoMessage()    {}
func (*ProcBackpackItemTimeoutReq) Descriptor() ([]byte, []int) {
	return fileDescriptorBackpack, []int{24}
}

func (m *ProcBackpackItemTimeoutReq) GetUserItemId() uint32 {
	if m != nil {
		return m.UserItemId
	}
	return 0
}

func (m *ProcBackpackItemTimeoutReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ProcBackpackItemTimeoutReq) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *ProcBackpackItemTimeoutReq) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *ProcBackpackItemTimeoutReq) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

func (m *ProcBackpackItemTimeoutReq) GetFinTime() uint32 {
	if m != nil {
		return m.FinTime
	}
	return 0
}

type ProcBackpackItemTimeoutResp struct {
}

func (m *ProcBackpackItemTimeoutResp) Reset()         { *m = ProcBackpackItemTimeoutResp{} }
func (m *ProcBackpackItemTimeoutResp) String() string { return proto.CompactTextString(m) }
func (*ProcBackpackItemTimeoutResp) ProtoMessage()    {}
func (*ProcBackpackItemTimeoutResp) Descriptor() ([]byte, []int) {
	return fileDescriptorBackpack, []int{25}
}

type GetUserBackpackReq struct {
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (m *GetUserBackpackReq) Reset()                    { *m = GetUserBackpackReq{} }
func (m *GetUserBackpackReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserBackpackReq) ProtoMessage()               {}
func (*GetUserBackpackReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{26} }

func (m *GetUserBackpackReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserBackpackResp struct {
	UserItemList []*UserBackpackItem `protobuf:"bytes,1,rep,name=user_item_list,json=userItemList" json:"user_item_list,omitempty"`
	LastObtainTs int64               `protobuf:"varint,2,opt,name=last_obtain_ts,json=lastObtainTs,proto3" json:"last_obtain_ts,omitempty"`
}

func (m *GetUserBackpackResp) Reset()                    { *m = GetUserBackpackResp{} }
func (m *GetUserBackpackResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserBackpackResp) ProtoMessage()               {}
func (*GetUserBackpackResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{27} }

func (m *GetUserBackpackResp) GetUserItemList() []*UserBackpackItem {
	if m != nil {
		return m.UserItemList
	}
	return nil
}

func (m *GetUserBackpackResp) GetLastObtainTs() int64 {
	if m != nil {
		return m.LastObtainTs
	}
	return 0
}

type GetUserFuncCardUseReq struct {
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (m *GetUserFuncCardUseReq) Reset()                    { *m = GetUserFuncCardUseReq{} }
func (m *GetUserFuncCardUseReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserFuncCardUseReq) ProtoMessage()               {}
func (*GetUserFuncCardUseReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{28} }

func (m *GetUserFuncCardUseReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserFuncCardUseResp struct {
	UserItemList []*FuncCardCfg `protobuf:"bytes,1,rep,name=user_item_list,json=userItemList" json:"user_item_list,omitempty"`
}

func (m *GetUserFuncCardUseResp) Reset()                    { *m = GetUserFuncCardUseResp{} }
func (m *GetUserFuncCardUseResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserFuncCardUseResp) ProtoMessage()               {}
func (*GetUserFuncCardUseResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{29} }

func (m *GetUserFuncCardUseResp) GetUserItemList() []*FuncCardCfg {
	if m != nil {
		return m.UserItemList
	}
	return nil
}

type AddFuncCardCfgReq struct {
	CardCfg *FuncCardCfg `protobuf:"bytes,1,opt,name=card_cfg,json=cardCfg" json:"card_cfg,omitempty"`
}

func (m *AddFuncCardCfgReq) Reset()                    { *m = AddFuncCardCfgReq{} }
func (m *AddFuncCardCfgReq) String() string            { return proto.CompactTextString(m) }
func (*AddFuncCardCfgReq) ProtoMessage()               {}
func (*AddFuncCardCfgReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{30} }

func (m *AddFuncCardCfgReq) GetCardCfg() *FuncCardCfg {
	if m != nil {
		return m.CardCfg
	}
	return nil
}

type AddFuncCardCfgResp struct {
}

func (m *AddFuncCardCfgResp) Reset()                    { *m = AddFuncCardCfgResp{} }
func (m *AddFuncCardCfgResp) String() string            { return proto.CompactTextString(m) }
func (*AddFuncCardCfgResp) ProtoMessage()               {}
func (*AddFuncCardCfgResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{31} }

type DelFuncCardCfgReq struct {
	CardId uint32 `protobuf:"varint,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
}

func (m *DelFuncCardCfgReq) Reset()                    { *m = DelFuncCardCfgReq{} }
func (m *DelFuncCardCfgReq) String() string            { return proto.CompactTextString(m) }
func (*DelFuncCardCfgReq) ProtoMessage()               {}
func (*DelFuncCardCfgReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{32} }

func (m *DelFuncCardCfgReq) GetCardId() uint32 {
	if m != nil {
		return m.CardId
	}
	return 0
}

type DelFuncCardCfgResp struct {
}

func (m *DelFuncCardCfgResp) Reset()                    { *m = DelFuncCardCfgResp{} }
func (m *DelFuncCardCfgResp) String() string            { return proto.CompactTextString(m) }
func (*DelFuncCardCfgResp) ProtoMessage()               {}
func (*DelFuncCardCfgResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{33} }

type GetFuncCardCfgReq struct {
	CardIdList []uint32 `protobuf:"varint,1,rep,packed,name=card_id_list,json=cardIdList" json:"card_id_list,omitempty"`
}

func (m *GetFuncCardCfgReq) Reset()                    { *m = GetFuncCardCfgReq{} }
func (m *GetFuncCardCfgReq) String() string            { return proto.CompactTextString(m) }
func (*GetFuncCardCfgReq) ProtoMessage()               {}
func (*GetFuncCardCfgReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{34} }

func (m *GetFuncCardCfgReq) GetCardIdList() []uint32 {
	if m != nil {
		return m.CardIdList
	}
	return nil
}

type GetFuncCardCfgResp struct {
	CardCfgList []*FuncCardCfg `protobuf:"bytes,1,rep,name=card_cfg_list,json=cardCfgList" json:"card_cfg_list,omitempty"`
}

func (m *GetFuncCardCfgResp) Reset()                    { *m = GetFuncCardCfgResp{} }
func (m *GetFuncCardCfgResp) String() string            { return proto.CompactTextString(m) }
func (*GetFuncCardCfgResp) ProtoMessage()               {}
func (*GetFuncCardCfgResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{35} }

func (m *GetFuncCardCfgResp) GetCardCfgList() []*FuncCardCfg {
	if m != nil {
		return m.CardCfgList
	}
	return nil
}

type CheckUserFuncCardUseReq struct {
	Uid      uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CardType uint32 `protobuf:"varint,2,opt,name=card_type,json=cardType,proto3" json:"card_type,omitempty"`
}

func (m *CheckUserFuncCardUseReq) Reset()         { *m = CheckUserFuncCardUseReq{} }
func (m *CheckUserFuncCardUseReq) String() string { return proto.CompactTextString(m) }
func (*CheckUserFuncCardUseReq) ProtoMessage()    {}
func (*CheckUserFuncCardUseReq) Descriptor() ([]byte, []int) {
	return fileDescriptorBackpack, []int{36}
}

func (m *CheckUserFuncCardUseReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckUserFuncCardUseReq) GetCardType() uint32 {
	if m != nil {
		return m.CardType
	}
	return 0
}

type CheckUserFuncCardUseResp struct {
	IsUse bool `protobuf:"varint,1,opt,name=is_use,json=isUse,proto3" json:"is_use,omitempty"`
}

func (m *CheckUserFuncCardUseResp) Reset()         { *m = CheckUserFuncCardUseResp{} }
func (m *CheckUserFuncCardUseResp) String() string { return proto.CompactTextString(m) }
func (*CheckUserFuncCardUseResp) ProtoMessage()    {}
func (*CheckUserFuncCardUseResp) Descriptor() ([]byte, []int) {
	return fileDescriptorBackpack, []int{37}
}

func (m *CheckUserFuncCardUseResp) GetIsUse() bool {
	if m != nil {
		return m.IsUse
	}
	return false
}

type SetUserFuncCardUseReq struct {
	Uid      uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CardType uint32 `protobuf:"varint,2,opt,name=card_type,json=cardType,proto3" json:"card_type,omitempty"`
	CardId   uint32 `protobuf:"varint,3,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
}

func (m *SetUserFuncCardUseReq) Reset()                    { *m = SetUserFuncCardUseReq{} }
func (m *SetUserFuncCardUseReq) String() string            { return proto.CompactTextString(m) }
func (*SetUserFuncCardUseReq) ProtoMessage()               {}
func (*SetUserFuncCardUseReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{38} }

func (m *SetUserFuncCardUseReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserFuncCardUseReq) GetCardType() uint32 {
	if m != nil {
		return m.CardType
	}
	return 0
}

func (m *SetUserFuncCardUseReq) GetCardId() uint32 {
	if m != nil {
		return m.CardId
	}
	return 0
}

type SetUserFuncCardUseResp struct {
}

func (m *SetUserFuncCardUseResp) Reset()                    { *m = SetUserFuncCardUseResp{} }
func (m *SetUserFuncCardUseResp) String() string            { return proto.CompactTextString(m) }
func (*SetUserFuncCardUseResp) ProtoMessage()               {}
func (*SetUserFuncCardUseResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{39} }

type GetUserBackpackLogReq struct {
	Uid       uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BeginTime uint32 `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime   uint32 `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	ItemType  uint32 `protobuf:"varint,4,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	SourceId  uint32 `protobuf:"varint,5,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	LogType   uint32 `protobuf:"varint,6,opt,name=log_type,json=logType,proto3" json:"log_type,omitempty"`
}

func (m *GetUserBackpackLogReq) Reset()                    { *m = GetUserBackpackLogReq{} }
func (m *GetUserBackpackLogReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserBackpackLogReq) ProtoMessage()               {}
func (*GetUserBackpackLogReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{40} }

func (m *GetUserBackpackLogReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserBackpackLogReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetUserBackpackLogReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetUserBackpackLogReq) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *GetUserBackpackLogReq) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *GetUserBackpackLogReq) GetLogType() uint32 {
	if m != nil {
		return m.LogType
	}
	return 0
}

type GetUserBackpackLogResp struct {
	LogList []*UserBackpackLog `protobuf:"bytes,1,rep,name=log_list,json=logList" json:"log_list,omitempty"`
}

func (m *GetUserBackpackLogResp) Reset()                    { *m = GetUserBackpackLogResp{} }
func (m *GetUserBackpackLogResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserBackpackLogResp) ProtoMessage()               {}
func (*GetUserBackpackLogResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{41} }

func (m *GetUserBackpackLogResp) GetLogList() []*UserBackpackLog {
	if m != nil {
		return m.LogList
	}
	return nil
}

// 此接口暂时不支持添加卡片
type AddItemCfgReq struct {
	ItemType uint32 `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	ItemCfg  []byte `protobuf:"bytes,2,opt,name=item_cfg,json=itemCfg,proto3" json:"item_cfg,omitempty"`
}

func (m *AddItemCfgReq) Reset()                    { *m = AddItemCfgReq{} }
func (m *AddItemCfgReq) String() string            { return proto.CompactTextString(m) }
func (*AddItemCfgReq) ProtoMessage()               {}
func (*AddItemCfgReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{42} }

func (m *AddItemCfgReq) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *AddItemCfgReq) GetItemCfg() []byte {
	if m != nil {
		return m.ItemCfg
	}
	return nil
}

type AddItemCfgResp struct {
	ItemType uint32 `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	ItemCfg  []byte `protobuf:"bytes,2,opt,name=item_cfg,json=itemCfg,proto3" json:"item_cfg,omitempty"`
}

func (m *AddItemCfgResp) Reset()                    { *m = AddItemCfgResp{} }
func (m *AddItemCfgResp) String() string            { return proto.CompactTextString(m) }
func (*AddItemCfgResp) ProtoMessage()               {}
func (*AddItemCfgResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{43} }

func (m *AddItemCfgResp) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *AddItemCfgResp) GetItemCfg() []byte {
	if m != nil {
		return m.ItemCfg
	}
	return nil
}

type DelItemCfgReq struct {
	ItemType uint32 `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	// uint32 item_source_type = 3;
	ItemCfg []byte `protobuf:"bytes,2,opt,name=item_cfg,json=itemCfg,proto3" json:"item_cfg,omitempty"`
}

func (m *DelItemCfgReq) Reset()                    { *m = DelItemCfgReq{} }
func (m *DelItemCfgReq) String() string            { return proto.CompactTextString(m) }
func (*DelItemCfgReq) ProtoMessage()               {}
func (*DelItemCfgReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{44} }

func (m *DelItemCfgReq) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *DelItemCfgReq) GetItemCfg() []byte {
	if m != nil {
		return m.ItemCfg
	}
	return nil
}

type DelItemCfgResp struct {
}

func (m *DelItemCfgResp) Reset()                    { *m = DelItemCfgResp{} }
func (m *DelItemCfgResp) String() string            { return proto.CompactTextString(m) }
func (*DelItemCfgResp) ProtoMessage()               {}
func (*DelItemCfgResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{45} }

type GetItemCfgReq struct {
	ItemType         uint32   `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	ItemSourceIdList []uint32 `protobuf:"varint,2,rep,packed,name=item_source_id_list,json=itemSourceIdList" json:"item_source_id_list,omitempty"`
	GetAll           bool     `protobuf:"varint,3,opt,name=get_all,json=getAll,proto3" json:"get_all,omitempty"`
}

func (m *GetItemCfgReq) Reset()                    { *m = GetItemCfgReq{} }
func (m *GetItemCfgReq) String() string            { return proto.CompactTextString(m) }
func (*GetItemCfgReq) ProtoMessage()               {}
func (*GetItemCfgReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{46} }

func (m *GetItemCfgReq) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *GetItemCfgReq) GetItemSourceIdList() []uint32 {
	if m != nil {
		return m.ItemSourceIdList
	}
	return nil
}

func (m *GetItemCfgReq) GetGetAll() bool {
	if m != nil {
		return m.GetAll
	}
	return false
}

type GetItemCfgResp struct {
	ItemCfgList [][]byte `protobuf:"bytes,1,rep,name=item_cfg_list,json=itemCfgList" json:"item_cfg_list,omitempty"`
}

func (m *GetItemCfgResp) Reset()                    { *m = GetItemCfgResp{} }
func (m *GetItemCfgResp) String() string            { return proto.CompactTextString(m) }
func (*GetItemCfgResp) ProtoMessage()               {}
func (*GetItemCfgResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{47} }

func (m *GetItemCfgResp) GetItemCfgList() [][]byte {
	if m != nil {
		return m.ItemCfgList
	}
	return nil
}

type UseItemInfo struct {
	ItemType   uint32 `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	UserItemId uint32 `protobuf:"varint,2,opt,name=user_item_id,json=userItemId,proto3" json:"user_item_id,omitempty"`
	SourceId   uint32 `protobuf:"varint,3,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	// string order_id=4;
	UseCount uint32 `protobuf:"varint,4,opt,name=use_count,json=useCount,proto3" json:"use_count,omitempty"`
}

func (m *UseItemInfo) Reset()                    { *m = UseItemInfo{} }
func (m *UseItemInfo) String() string            { return proto.CompactTextString(m) }
func (*UseItemInfo) ProtoMessage()               {}
func (*UseItemInfo) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{48} }

func (m *UseItemInfo) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *UseItemInfo) GetUserItemId() uint32 {
	if m != nil {
		return m.UserItemId
	}
	return 0
}

func (m *UseItemInfo) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *UseItemInfo) GetUseCount() uint32 {
	if m != nil {
		return m.UseCount
	}
	return 0
}

type TransactionInfo struct {
	FreezeType uint32 `protobuf:"varint,1,opt,name=freeze_type,json=freezeType,proto3" json:"freeze_type,omitempty"`
	OperTime   uint32 `protobuf:"varint,2,opt,name=oper_time,json=operTime,proto3" json:"oper_time,omitempty"`
	OrderId    string `protobuf:"bytes,3,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	ExpireTime uint32 `protobuf:"varint,4,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
}

func (m *TransactionInfo) Reset()                    { *m = TransactionInfo{} }
func (m *TransactionInfo) String() string            { return proto.CompactTextString(m) }
func (*TransactionInfo) ProtoMessage()               {}
func (*TransactionInfo) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{49} }

func (m *TransactionInfo) GetFreezeType() uint32 {
	if m != nil {
		return m.FreezeType
	}
	return 0
}

func (m *TransactionInfo) GetOperTime() uint32 {
	if m != nil {
		return m.OperTime
	}
	return 0
}

func (m *TransactionInfo) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *TransactionInfo) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

// only support BACKPACK_LOTTERY_FRAGMENT
// TODO support other type  (2019-10-22,T1035)
// doc: see FreeZeItem.md
type FreeZeItemReq struct {
	TansacationInfo *TransactionInfo `protobuf:"bytes,1,opt,name=tansacation_info,json=tansacationInfo" json:"tansacation_info,omitempty"`
	ItemInfoList    []*UseItemInfo   `protobuf:"bytes,2,rep,name=Item_info_list,json=ItemInfoList" json:"Item_info_list,omitempty"`
	Uid             uint32           `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (m *FreeZeItemReq) Reset()                    { *m = FreeZeItemReq{} }
func (m *FreeZeItemReq) String() string            { return proto.CompactTextString(m) }
func (*FreeZeItemReq) ProtoMessage()               {}
func (*FreeZeItemReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{50} }

func (m *FreeZeItemReq) GetTansacationInfo() *TransactionInfo {
	if m != nil {
		return m.TansacationInfo
	}
	return nil
}

func (m *FreeZeItemReq) GetItemInfoList() []*UseItemInfo {
	if m != nil {
		return m.ItemInfoList
	}
	return nil
}

func (m *FreeZeItemReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type FreeZeItemResp struct {
}

func (m *FreeZeItemResp) Reset()                    { *m = FreeZeItemResp{} }
func (m *FreeZeItemResp) String() string            { return proto.CompactTextString(m) }
func (*FreeZeItemResp) ProtoMessage()               {}
func (*FreeZeItemResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{51} }

type UserPackageSum struct {
	ItemType  uint32 `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	ItemId    uint32 `protobuf:"varint,2,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ItemCount uint32 `protobuf:"varint,3,opt,name=item_count,json=itemCount,proto3" json:"item_count,omitempty"`
}

func (m *UserPackageSum) Reset()                    { *m = UserPackageSum{} }
func (m *UserPackageSum) String() string            { return proto.CompactTextString(m) }
func (*UserPackageSum) ProtoMessage()               {}
func (*UserPackageSum) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{52} }

func (m *UserPackageSum) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *UserPackageSum) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *UserPackageSum) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

type GetUserPackageSumReq struct {
	Uid      uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ItemType uint32 `protobuf:"varint,2,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	ItemId   uint32 `protobuf:"varint,3,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
}

func (m *GetUserPackageSumReq) Reset()                    { *m = GetUserPackageSumReq{} }
func (m *GetUserPackageSumReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserPackageSumReq) ProtoMessage()               {}
func (*GetUserPackageSumReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{53} }

func (m *GetUserPackageSumReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserPackageSumReq) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *GetUserPackageSumReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

type GetUserPackageSumResp struct {
	ItemSum *UserPackageSum `protobuf:"bytes,1,opt,name=item_sum,json=itemSum" json:"item_sum,omitempty"`
}

func (m *GetUserPackageSumResp) Reset()                    { *m = GetUserPackageSumResp{} }
func (m *GetUserPackageSumResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserPackageSumResp) ProtoMessage()               {}
func (*GetUserPackageSumResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{54} }

func (m *GetUserPackageSumResp) GetItemSum() *UserPackageSum {
	if m != nil {
		return m.ItemSum
	}
	return nil
}

type GetOrderCountByTimeRangeReq struct {
	BeginTime uint32 `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime   uint32 `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	LogType   uint32 `protobuf:"varint,3,opt,name=log_type,json=logType,proto3" json:"log_type,omitempty"`
}

func (m *GetOrderCountByTimeRangeReq) Reset()         { *m = GetOrderCountByTimeRangeReq{} }
func (m *GetOrderCountByTimeRangeReq) String() string { return proto.CompactTextString(m) }
func (*GetOrderCountByTimeRangeReq) ProtoMessage()    {}
func (*GetOrderCountByTimeRangeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorBackpack, []int{55}
}

func (m *GetOrderCountByTimeRangeReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetOrderCountByTimeRangeReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetOrderCountByTimeRangeReq) GetLogType() uint32 {
	if m != nil {
		return m.LogType
	}
	return 0
}

type GetOrderCountByTimeRangeResp struct {
	Count uint32 `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
}

func (m *GetOrderCountByTimeRangeResp) Reset()         { *m = GetOrderCountByTimeRangeResp{} }
func (m *GetOrderCountByTimeRangeResp) String() string { return proto.CompactTextString(m) }
func (*GetOrderCountByTimeRangeResp) ProtoMessage()    {}
func (*GetOrderCountByTimeRangeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorBackpack, []int{56}
}

func (m *GetOrderCountByTimeRangeResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetOrderListByTimeRangeReq struct {
	BeginTime uint32 `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime   uint32 `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	LogType   uint32 `protobuf:"varint,3,opt,name=log_type,json=logType,proto3" json:"log_type,omitempty"`
}

func (m *GetOrderListByTimeRangeReq) Reset()         { *m = GetOrderListByTimeRangeReq{} }
func (m *GetOrderListByTimeRangeReq) String() string { return proto.CompactTextString(m) }
func (*GetOrderListByTimeRangeReq) ProtoMessage()    {}
func (*GetOrderListByTimeRangeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorBackpack, []int{57}
}

func (m *GetOrderListByTimeRangeReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetOrderListByTimeRangeReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetOrderListByTimeRangeReq) GetLogType() uint32 {
	if m != nil {
		return m.LogType
	}
	return 0
}

type GetOrderListByTimeRangeResp struct {
	OrderList []string `protobuf:"bytes,1,rep,name=order_list,json=orderList" json:"order_list,omitempty"`
}

func (m *GetOrderListByTimeRangeResp) Reset()         { *m = GetOrderListByTimeRangeResp{} }
func (m *GetOrderListByTimeRangeResp) String() string { return proto.CompactTextString(m) }
func (*GetOrderListByTimeRangeResp) ProtoMessage()    {}
func (*GetOrderListByTimeRangeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorBackpack, []int{58}
}

func (m *GetOrderListByTimeRangeResp) GetOrderList() []string {
	if m != nil {
		return m.OrderList
	}
	return nil
}

type ConversionItemReq struct {
	Uid              uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OutsideTime      uint32         `protobuf:"varint,2,opt,name=outside_time,json=outsideTime,proto3" json:"outside_time,omitempty"`
	BgId             uint32         `protobuf:"varint,3,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
	BgNum            uint32         `protobuf:"varint,4,opt,name=bg_num,json=bgNum,proto3" json:"bg_num,omitempty"`
	Source           uint32         `protobuf:"varint,5,opt,name=source,proto3" json:"source,omitempty"`
	MaterialPrice    uint32         `protobuf:"varint,6,opt,name=material_price,json=materialPrice,proto3" json:"material_price,omitempty"`
	ConversionPrice  uint32         `protobuf:"varint,7,opt,name=conversion_price,json=conversionPrice,proto3" json:"conversion_price,omitempty"`
	OrderId          string         `protobuf:"bytes,8,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	MaterialItemList []*UseItemInfo `protobuf:"bytes,9,rep,name=material_item_list,json=materialItemList" json:"material_item_list,omitempty"`
}

func (m *ConversionItemReq) Reset()                    { *m = ConversionItemReq{} }
func (m *ConversionItemReq) String() string            { return proto.CompactTextString(m) }
func (*ConversionItemReq) ProtoMessage()               {}
func (*ConversionItemReq) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{59} }

func (m *ConversionItemReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ConversionItemReq) GetOutsideTime() uint32 {
	if m != nil {
		return m.OutsideTime
	}
	return 0
}

func (m *ConversionItemReq) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

func (m *ConversionItemReq) GetBgNum() uint32 {
	if m != nil {
		return m.BgNum
	}
	return 0
}

func (m *ConversionItemReq) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *ConversionItemReq) GetMaterialPrice() uint32 {
	if m != nil {
		return m.MaterialPrice
	}
	return 0
}

func (m *ConversionItemReq) GetConversionPrice() uint32 {
	if m != nil {
		return m.ConversionPrice
	}
	return 0
}

func (m *ConversionItemReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *ConversionItemReq) GetMaterialItemList() []*UseItemInfo {
	if m != nil {
		return m.MaterialItemList
	}
	return nil
}

type ConversionItemResp struct {
}

func (m *ConversionItemResp) Reset()                    { *m = ConversionItemResp{} }
func (m *ConversionItemResp) String() string            { return proto.CompactTextString(m) }
func (*ConversionItemResp) ProtoMessage()               {}
func (*ConversionItemResp) Descriptor() ([]byte, []int) { return fileDescriptorBackpack, []int{60} }

func init() {
	proto.RegisterType((*PackageCfg)(nil), "backpack.PackageCfg")
	proto.RegisterType((*PackageItemCfg)(nil), "backpack.PackageItemCfg")
	proto.RegisterType((*FuncCardCfg)(nil), "backpack.FuncCardCfg")
	proto.RegisterType((*LotteryFragmentCfg)(nil), "backpack.LotteryFragmentCfg")
	proto.RegisterType((*UserBackpackItem)(nil), "backpack.UserBackpackItem")
	proto.RegisterType((*UserBackpackLog)(nil), "backpack.UserBackpackLog")
	proto.RegisterType((*AddPackageCfgReq)(nil), "backpack.AddPackageCfgReq")
	proto.RegisterType((*AddPackageCfgResp)(nil), "backpack.AddPackageCfgResp")
	proto.RegisterType((*DelPackageCfgReq)(nil), "backpack.DelPackageCfgReq")
	proto.RegisterType((*DelPackageCfgResp)(nil), "backpack.DelPackageCfgResp")
	proto.RegisterType((*GetPackageCfgReq)(nil), "backpack.GetPackageCfgReq")
	proto.RegisterType((*GetPackageCfgResp)(nil), "backpack.GetPackageCfgResp")
	proto.RegisterType((*AddPackageItemCfgReq)(nil), "backpack.AddPackageItemCfgReq")
	proto.RegisterType((*AddPackageItemCfgResp)(nil), "backpack.AddPackageItemCfgResp")
	proto.RegisterType((*ModPackageItemCfgReq)(nil), "backpack.ModPackageItemCfgReq")
	proto.RegisterType((*ModPackageItemCfgResp)(nil), "backpack.ModPackageItemCfgResp")
	proto.RegisterType((*DelPackageItemCfgReq)(nil), "backpack.DelPackageItemCfgReq")
	proto.RegisterType((*DelPackageItemCfgResp)(nil), "backpack.DelPackageItemCfgResp")
	proto.RegisterType((*GetPackageItemCfgReq)(nil), "backpack.GetPackageItemCfgReq")
	proto.RegisterType((*GetPackageItemCfgResp)(nil), "backpack.GetPackageItemCfgResp")
	proto.RegisterType((*GiveUserPackageReq)(nil), "backpack.GiveUserPackageReq")
	proto.RegisterType((*GiveUserPackageResp)(nil), "backpack.GiveUserPackageResp")
	proto.RegisterType((*UseBackpackItemReq)(nil), "backpack.UseBackpackItemReq")
	proto.RegisterType((*UseBackpackItemResp)(nil), "backpack.UseBackpackItemResp")
	proto.RegisterType((*ProcBackpackItemTimeoutReq)(nil), "backpack.ProcBackpackItemTimeoutReq")
	proto.RegisterType((*ProcBackpackItemTimeoutResp)(nil), "backpack.ProcBackpackItemTimeoutResp")
	proto.RegisterType((*GetUserBackpackReq)(nil), "backpack.GetUserBackpackReq")
	proto.RegisterType((*GetUserBackpackResp)(nil), "backpack.GetUserBackpackResp")
	proto.RegisterType((*GetUserFuncCardUseReq)(nil), "backpack.GetUserFuncCardUseReq")
	proto.RegisterType((*GetUserFuncCardUseResp)(nil), "backpack.GetUserFuncCardUseResp")
	proto.RegisterType((*AddFuncCardCfgReq)(nil), "backpack.AddFuncCardCfgReq")
	proto.RegisterType((*AddFuncCardCfgResp)(nil), "backpack.AddFuncCardCfgResp")
	proto.RegisterType((*DelFuncCardCfgReq)(nil), "backpack.DelFuncCardCfgReq")
	proto.RegisterType((*DelFuncCardCfgResp)(nil), "backpack.DelFuncCardCfgResp")
	proto.RegisterType((*GetFuncCardCfgReq)(nil), "backpack.GetFuncCardCfgReq")
	proto.RegisterType((*GetFuncCardCfgResp)(nil), "backpack.GetFuncCardCfgResp")
	proto.RegisterType((*CheckUserFuncCardUseReq)(nil), "backpack.CheckUserFuncCardUseReq")
	proto.RegisterType((*CheckUserFuncCardUseResp)(nil), "backpack.CheckUserFuncCardUseResp")
	proto.RegisterType((*SetUserFuncCardUseReq)(nil), "backpack.SetUserFuncCardUseReq")
	proto.RegisterType((*SetUserFuncCardUseResp)(nil), "backpack.SetUserFuncCardUseResp")
	proto.RegisterType((*GetUserBackpackLogReq)(nil), "backpack.GetUserBackpackLogReq")
	proto.RegisterType((*GetUserBackpackLogResp)(nil), "backpack.GetUserBackpackLogResp")
	proto.RegisterType((*AddItemCfgReq)(nil), "backpack.AddItemCfgReq")
	proto.RegisterType((*AddItemCfgResp)(nil), "backpack.AddItemCfgResp")
	proto.RegisterType((*DelItemCfgReq)(nil), "backpack.DelItemCfgReq")
	proto.RegisterType((*DelItemCfgResp)(nil), "backpack.DelItemCfgResp")
	proto.RegisterType((*GetItemCfgReq)(nil), "backpack.GetItemCfgReq")
	proto.RegisterType((*GetItemCfgResp)(nil), "backpack.GetItemCfgResp")
	proto.RegisterType((*UseItemInfo)(nil), "backpack.UseItemInfo")
	proto.RegisterType((*TransactionInfo)(nil), "backpack.TransactionInfo")
	proto.RegisterType((*FreeZeItemReq)(nil), "backpack.FreeZeItemReq")
	proto.RegisterType((*FreeZeItemResp)(nil), "backpack.FreeZeItemResp")
	proto.RegisterType((*UserPackageSum)(nil), "backpack.UserPackageSum")
	proto.RegisterType((*GetUserPackageSumReq)(nil), "backpack.GetUserPackageSumReq")
	proto.RegisterType((*GetUserPackageSumResp)(nil), "backpack.GetUserPackageSumResp")
	proto.RegisterType((*GetOrderCountByTimeRangeReq)(nil), "backpack.GetOrderCountByTimeRangeReq")
	proto.RegisterType((*GetOrderCountByTimeRangeResp)(nil), "backpack.GetOrderCountByTimeRangeResp")
	proto.RegisterType((*GetOrderListByTimeRangeReq)(nil), "backpack.GetOrderListByTimeRangeReq")
	proto.RegisterType((*GetOrderListByTimeRangeResp)(nil), "backpack.GetOrderListByTimeRangeResp")
	proto.RegisterType((*ConversionItemReq)(nil), "backpack.ConversionItemReq")
	proto.RegisterType((*ConversionItemResp)(nil), "backpack.ConversionItemResp")
	proto.RegisterEnum("backpack.LogType", LogType_name, LogType_value)
	proto.RegisterEnum("backpack.PackageItemType", PackageItemType_name, PackageItemType_value)
	proto.RegisterEnum("backpack.PackageSourceType", PackageSourceType_name, PackageSourceType_value)
	proto.RegisterEnum("backpack.FREEZETYPE", FREEZETYPE_name, FREEZETYPE_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for Backpack service

type BackpackClient interface {
	AddPackageCfg(ctx context.Context, in *AddPackageCfgReq, opts ...grpc.CallOption) (*AddPackageCfgResp, error)
	GetPackageCfg(ctx context.Context, in *GetPackageCfgReq, opts ...grpc.CallOption) (*GetPackageCfgResp, error)
	DelPackageCfg(ctx context.Context, in *DelPackageCfgReq, opts ...grpc.CallOption) (*DelPackageCfgResp, error)
	AddPackageItemCfg(ctx context.Context, in *AddPackageItemCfgReq, opts ...grpc.CallOption) (*AddPackageItemCfgResp, error)
	GetPackageItemCfg(ctx context.Context, in *GetPackageItemCfgReq, opts ...grpc.CallOption) (*GetPackageItemCfgResp, error)
	ModPackageItemCfg(ctx context.Context, in *ModPackageItemCfgReq, opts ...grpc.CallOption) (*ModPackageItemCfgResp, error)
	DelPackageItemCfg(ctx context.Context, in *DelPackageItemCfgReq, opts ...grpc.CallOption) (*DelPackageItemCfgResp, error)
	AddFuncCardCfg(ctx context.Context, in *AddFuncCardCfgReq, opts ...grpc.CallOption) (*AddFuncCardCfgResp, error)
	DelFuncCardCfg(ctx context.Context, in *DelFuncCardCfgReq, opts ...grpc.CallOption) (*DelFuncCardCfgResp, error)
	GetFuncCardCfg(ctx context.Context, in *GetFuncCardCfgReq, opts ...grpc.CallOption) (*GetFuncCardCfgResp, error)
	GiveUserPackage(ctx context.Context, in *GiveUserPackageReq, opts ...grpc.CallOption) (*GiveUserPackageResp, error)
	UseBackpackItem(ctx context.Context, in *UseBackpackItemReq, opts ...grpc.CallOption) (*UseBackpackItemResp, error)
	GetUserBackpack(ctx context.Context, in *GetUserBackpackReq, opts ...grpc.CallOption) (*GetUserBackpackResp, error)
	CheckUserFuncCardUse(ctx context.Context, in *CheckUserFuncCardUseReq, opts ...grpc.CallOption) (*CheckUserFuncCardUseResp, error)
	SetUserFuncCardUse(ctx context.Context, in *SetUserFuncCardUseReq, opts ...grpc.CallOption) (*SetUserFuncCardUseResp, error)
	GetUserFuncCardUse(ctx context.Context, in *GetUserFuncCardUseReq, opts ...grpc.CallOption) (*GetUserFuncCardUseResp, error)
	ProcBackpackItemTimeout(ctx context.Context, in *ProcBackpackItemTimeoutReq, opts ...grpc.CallOption) (*ProcBackpackItemTimeoutResp, error)
	GetUserBackpackLog(ctx context.Context, in *GetUserBackpackLogReq, opts ...grpc.CallOption) (*GetUserBackpackLogResp, error)
	AddItemCfg(ctx context.Context, in *AddItemCfgReq, opts ...grpc.CallOption) (*AddItemCfgResp, error)
	DelItemCfg(ctx context.Context, in *DelItemCfgReq, opts ...grpc.CallOption) (*DelItemCfgResp, error)
	GetItemCfg(ctx context.Context, in *GetItemCfgReq, opts ...grpc.CallOption) (*GetItemCfgResp, error)
	FreeZeItem(ctx context.Context, in *FreeZeItemReq, opts ...grpc.CallOption) (*FreeZeItemResp, error)
	GetUserPackageSum(ctx context.Context, in *GetUserPackageSumReq, opts ...grpc.CallOption) (*GetUserPackageSumResp, error)
	GetOrderCountByTimeRange(ctx context.Context, in *GetOrderCountByTimeRangeReq, opts ...grpc.CallOption) (*GetOrderCountByTimeRangeResp, error)
	GetOrderListByTimeRange(ctx context.Context, in *GetOrderListByTimeRangeReq, opts ...grpc.CallOption) (*GetOrderListByTimeRangeResp, error)
	ConversionItem(ctx context.Context, in *ConversionItemReq, opts ...grpc.CallOption) (*ConversionItemResp, error)
}

type backpackClient struct {
	cc *grpc.ClientConn
}

func NewBackpackClient(cc *grpc.ClientConn) BackpackClient {
	return &backpackClient{cc}
}

func (c *backpackClient) AddPackageCfg(ctx context.Context, in *AddPackageCfgReq, opts ...grpc.CallOption) (*AddPackageCfgResp, error) {
	out := new(AddPackageCfgResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/AddPackageCfg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) GetPackageCfg(ctx context.Context, in *GetPackageCfgReq, opts ...grpc.CallOption) (*GetPackageCfgResp, error) {
	out := new(GetPackageCfgResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/GetPackageCfg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) DelPackageCfg(ctx context.Context, in *DelPackageCfgReq, opts ...grpc.CallOption) (*DelPackageCfgResp, error) {
	out := new(DelPackageCfgResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/DelPackageCfg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) AddPackageItemCfg(ctx context.Context, in *AddPackageItemCfgReq, opts ...grpc.CallOption) (*AddPackageItemCfgResp, error) {
	out := new(AddPackageItemCfgResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/AddPackageItemCfg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) GetPackageItemCfg(ctx context.Context, in *GetPackageItemCfgReq, opts ...grpc.CallOption) (*GetPackageItemCfgResp, error) {
	out := new(GetPackageItemCfgResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/GetPackageItemCfg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) ModPackageItemCfg(ctx context.Context, in *ModPackageItemCfgReq, opts ...grpc.CallOption) (*ModPackageItemCfgResp, error) {
	out := new(ModPackageItemCfgResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/ModPackageItemCfg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) DelPackageItemCfg(ctx context.Context, in *DelPackageItemCfgReq, opts ...grpc.CallOption) (*DelPackageItemCfgResp, error) {
	out := new(DelPackageItemCfgResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/DelPackageItemCfg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) AddFuncCardCfg(ctx context.Context, in *AddFuncCardCfgReq, opts ...grpc.CallOption) (*AddFuncCardCfgResp, error) {
	out := new(AddFuncCardCfgResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/AddFuncCardCfg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) DelFuncCardCfg(ctx context.Context, in *DelFuncCardCfgReq, opts ...grpc.CallOption) (*DelFuncCardCfgResp, error) {
	out := new(DelFuncCardCfgResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/DelFuncCardCfg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) GetFuncCardCfg(ctx context.Context, in *GetFuncCardCfgReq, opts ...grpc.CallOption) (*GetFuncCardCfgResp, error) {
	out := new(GetFuncCardCfgResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/GetFuncCardCfg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) GiveUserPackage(ctx context.Context, in *GiveUserPackageReq, opts ...grpc.CallOption) (*GiveUserPackageResp, error) {
	out := new(GiveUserPackageResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/GiveUserPackage", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) UseBackpackItem(ctx context.Context, in *UseBackpackItemReq, opts ...grpc.CallOption) (*UseBackpackItemResp, error) {
	out := new(UseBackpackItemResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/UseBackpackItem", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) GetUserBackpack(ctx context.Context, in *GetUserBackpackReq, opts ...grpc.CallOption) (*GetUserBackpackResp, error) {
	out := new(GetUserBackpackResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/GetUserBackpack", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) CheckUserFuncCardUse(ctx context.Context, in *CheckUserFuncCardUseReq, opts ...grpc.CallOption) (*CheckUserFuncCardUseResp, error) {
	out := new(CheckUserFuncCardUseResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/CheckUserFuncCardUse", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) SetUserFuncCardUse(ctx context.Context, in *SetUserFuncCardUseReq, opts ...grpc.CallOption) (*SetUserFuncCardUseResp, error) {
	out := new(SetUserFuncCardUseResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/SetUserFuncCardUse", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) GetUserFuncCardUse(ctx context.Context, in *GetUserFuncCardUseReq, opts ...grpc.CallOption) (*GetUserFuncCardUseResp, error) {
	out := new(GetUserFuncCardUseResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/GetUserFuncCardUse", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) ProcBackpackItemTimeout(ctx context.Context, in *ProcBackpackItemTimeoutReq, opts ...grpc.CallOption) (*ProcBackpackItemTimeoutResp, error) {
	out := new(ProcBackpackItemTimeoutResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/ProcBackpackItemTimeout", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) GetUserBackpackLog(ctx context.Context, in *GetUserBackpackLogReq, opts ...grpc.CallOption) (*GetUserBackpackLogResp, error) {
	out := new(GetUserBackpackLogResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/GetUserBackpackLog", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) AddItemCfg(ctx context.Context, in *AddItemCfgReq, opts ...grpc.CallOption) (*AddItemCfgResp, error) {
	out := new(AddItemCfgResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/AddItemCfg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) DelItemCfg(ctx context.Context, in *DelItemCfgReq, opts ...grpc.CallOption) (*DelItemCfgResp, error) {
	out := new(DelItemCfgResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/DelItemCfg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) GetItemCfg(ctx context.Context, in *GetItemCfgReq, opts ...grpc.CallOption) (*GetItemCfgResp, error) {
	out := new(GetItemCfgResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/GetItemCfg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) FreeZeItem(ctx context.Context, in *FreeZeItemReq, opts ...grpc.CallOption) (*FreeZeItemResp, error) {
	out := new(FreeZeItemResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/FreeZeItem", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) GetUserPackageSum(ctx context.Context, in *GetUserPackageSumReq, opts ...grpc.CallOption) (*GetUserPackageSumResp, error) {
	out := new(GetUserPackageSumResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/GetUserPackageSum", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) GetOrderCountByTimeRange(ctx context.Context, in *GetOrderCountByTimeRangeReq, opts ...grpc.CallOption) (*GetOrderCountByTimeRangeResp, error) {
	out := new(GetOrderCountByTimeRangeResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/GetOrderCountByTimeRange", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) GetOrderListByTimeRange(ctx context.Context, in *GetOrderListByTimeRangeReq, opts ...grpc.CallOption) (*GetOrderListByTimeRangeResp, error) {
	out := new(GetOrderListByTimeRangeResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/GetOrderListByTimeRange", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backpackClient) ConversionItem(ctx context.Context, in *ConversionItemReq, opts ...grpc.CallOption) (*ConversionItemResp, error) {
	out := new(ConversionItemResp)
	err := grpc.Invoke(ctx, "/backpack.Backpack/ConversionItem", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for Backpack service

type BackpackServer interface {
	AddPackageCfg(context.Context, *AddPackageCfgReq) (*AddPackageCfgResp, error)
	GetPackageCfg(context.Context, *GetPackageCfgReq) (*GetPackageCfgResp, error)
	DelPackageCfg(context.Context, *DelPackageCfgReq) (*DelPackageCfgResp, error)
	AddPackageItemCfg(context.Context, *AddPackageItemCfgReq) (*AddPackageItemCfgResp, error)
	GetPackageItemCfg(context.Context, *GetPackageItemCfgReq) (*GetPackageItemCfgResp, error)
	ModPackageItemCfg(context.Context, *ModPackageItemCfgReq) (*ModPackageItemCfgResp, error)
	DelPackageItemCfg(context.Context, *DelPackageItemCfgReq) (*DelPackageItemCfgResp, error)
	AddFuncCardCfg(context.Context, *AddFuncCardCfgReq) (*AddFuncCardCfgResp, error)
	DelFuncCardCfg(context.Context, *DelFuncCardCfgReq) (*DelFuncCardCfgResp, error)
	GetFuncCardCfg(context.Context, *GetFuncCardCfgReq) (*GetFuncCardCfgResp, error)
	GiveUserPackage(context.Context, *GiveUserPackageReq) (*GiveUserPackageResp, error)
	UseBackpackItem(context.Context, *UseBackpackItemReq) (*UseBackpackItemResp, error)
	GetUserBackpack(context.Context, *GetUserBackpackReq) (*GetUserBackpackResp, error)
	CheckUserFuncCardUse(context.Context, *CheckUserFuncCardUseReq) (*CheckUserFuncCardUseResp, error)
	SetUserFuncCardUse(context.Context, *SetUserFuncCardUseReq) (*SetUserFuncCardUseResp, error)
	GetUserFuncCardUse(context.Context, *GetUserFuncCardUseReq) (*GetUserFuncCardUseResp, error)
	ProcBackpackItemTimeout(context.Context, *ProcBackpackItemTimeoutReq) (*ProcBackpackItemTimeoutResp, error)
	GetUserBackpackLog(context.Context, *GetUserBackpackLogReq) (*GetUserBackpackLogResp, error)
	AddItemCfg(context.Context, *AddItemCfgReq) (*AddItemCfgResp, error)
	DelItemCfg(context.Context, *DelItemCfgReq) (*DelItemCfgResp, error)
	GetItemCfg(context.Context, *GetItemCfgReq) (*GetItemCfgResp, error)
	FreeZeItem(context.Context, *FreeZeItemReq) (*FreeZeItemResp, error)
	GetUserPackageSum(context.Context, *GetUserPackageSumReq) (*GetUserPackageSumResp, error)
	GetOrderCountByTimeRange(context.Context, *GetOrderCountByTimeRangeReq) (*GetOrderCountByTimeRangeResp, error)
	GetOrderListByTimeRange(context.Context, *GetOrderListByTimeRangeReq) (*GetOrderListByTimeRangeResp, error)
	ConversionItem(context.Context, *ConversionItemReq) (*ConversionItemResp, error)
}

func RegisterBackpackServer(s *grpc.Server, srv BackpackServer) {
	s.RegisterService(&_Backpack_serviceDesc, srv)
}

func _Backpack_AddPackageCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPackageCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).AddPackageCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/AddPackageCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).AddPackageCfg(ctx, req.(*AddPackageCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_GetPackageCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPackageCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).GetPackageCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/GetPackageCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).GetPackageCfg(ctx, req.(*GetPackageCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_DelPackageCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelPackageCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).DelPackageCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/DelPackageCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).DelPackageCfg(ctx, req.(*DelPackageCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_AddPackageItemCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPackageItemCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).AddPackageItemCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/AddPackageItemCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).AddPackageItemCfg(ctx, req.(*AddPackageItemCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_GetPackageItemCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPackageItemCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).GetPackageItemCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/GetPackageItemCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).GetPackageItemCfg(ctx, req.(*GetPackageItemCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_ModPackageItemCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModPackageItemCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).ModPackageItemCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/ModPackageItemCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).ModPackageItemCfg(ctx, req.(*ModPackageItemCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_DelPackageItemCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelPackageItemCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).DelPackageItemCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/DelPackageItemCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).DelPackageItemCfg(ctx, req.(*DelPackageItemCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_AddFuncCardCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddFuncCardCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).AddFuncCardCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/AddFuncCardCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).AddFuncCardCfg(ctx, req.(*AddFuncCardCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_DelFuncCardCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelFuncCardCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).DelFuncCardCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/DelFuncCardCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).DelFuncCardCfg(ctx, req.(*DelFuncCardCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_GetFuncCardCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFuncCardCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).GetFuncCardCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/GetFuncCardCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).GetFuncCardCfg(ctx, req.(*GetFuncCardCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_GiveUserPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GiveUserPackageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).GiveUserPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/GiveUserPackage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).GiveUserPackage(ctx, req.(*GiveUserPackageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_UseBackpackItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UseBackpackItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).UseBackpackItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/UseBackpackItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).UseBackpackItem(ctx, req.(*UseBackpackItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_GetUserBackpack_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserBackpackReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).GetUserBackpack(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/GetUserBackpack",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).GetUserBackpack(ctx, req.(*GetUserBackpackReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_CheckUserFuncCardUse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserFuncCardUseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).CheckUserFuncCardUse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/CheckUserFuncCardUse",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).CheckUserFuncCardUse(ctx, req.(*CheckUserFuncCardUseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_SetUserFuncCardUse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserFuncCardUseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).SetUserFuncCardUse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/SetUserFuncCardUse",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).SetUserFuncCardUse(ctx, req.(*SetUserFuncCardUseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_GetUserFuncCardUse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserFuncCardUseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).GetUserFuncCardUse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/GetUserFuncCardUse",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).GetUserFuncCardUse(ctx, req.(*GetUserFuncCardUseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_ProcBackpackItemTimeout_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcBackpackItemTimeoutReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).ProcBackpackItemTimeout(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/ProcBackpackItemTimeout",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).ProcBackpackItemTimeout(ctx, req.(*ProcBackpackItemTimeoutReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_GetUserBackpackLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserBackpackLogReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).GetUserBackpackLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/GetUserBackpackLog",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).GetUserBackpackLog(ctx, req.(*GetUserBackpackLogReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_AddItemCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddItemCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).AddItemCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/AddItemCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).AddItemCfg(ctx, req.(*AddItemCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_DelItemCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelItemCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).DelItemCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/DelItemCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).DelItemCfg(ctx, req.(*DelItemCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_GetItemCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetItemCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).GetItemCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/GetItemCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).GetItemCfg(ctx, req.(*GetItemCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_FreeZeItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FreeZeItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).FreeZeItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/FreeZeItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).FreeZeItem(ctx, req.(*FreeZeItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_GetUserPackageSum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserPackageSumReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).GetUserPackageSum(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/GetUserPackageSum",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).GetUserPackageSum(ctx, req.(*GetUserPackageSumReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_GetOrderCountByTimeRange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderCountByTimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).GetOrderCountByTimeRange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/GetOrderCountByTimeRange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).GetOrderCountByTimeRange(ctx, req.(*GetOrderCountByTimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_GetOrderListByTimeRange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderListByTimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).GetOrderListByTimeRange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/GetOrderListByTimeRange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).GetOrderListByTimeRange(ctx, req.(*GetOrderListByTimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Backpack_ConversionItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConversionItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackpackServer).ConversionItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/backpack.Backpack/ConversionItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackpackServer).ConversionItem(ctx, req.(*ConversionItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Backpack_serviceDesc = grpc.ServiceDesc{
	ServiceName: "backpack.Backpack",
	HandlerType: (*BackpackServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddPackageCfg",
			Handler:    _Backpack_AddPackageCfg_Handler,
		},
		{
			MethodName: "GetPackageCfg",
			Handler:    _Backpack_GetPackageCfg_Handler,
		},
		{
			MethodName: "DelPackageCfg",
			Handler:    _Backpack_DelPackageCfg_Handler,
		},
		{
			MethodName: "AddPackageItemCfg",
			Handler:    _Backpack_AddPackageItemCfg_Handler,
		},
		{
			MethodName: "GetPackageItemCfg",
			Handler:    _Backpack_GetPackageItemCfg_Handler,
		},
		{
			MethodName: "ModPackageItemCfg",
			Handler:    _Backpack_ModPackageItemCfg_Handler,
		},
		{
			MethodName: "DelPackageItemCfg",
			Handler:    _Backpack_DelPackageItemCfg_Handler,
		},
		{
			MethodName: "AddFuncCardCfg",
			Handler:    _Backpack_AddFuncCardCfg_Handler,
		},
		{
			MethodName: "DelFuncCardCfg",
			Handler:    _Backpack_DelFuncCardCfg_Handler,
		},
		{
			MethodName: "GetFuncCardCfg",
			Handler:    _Backpack_GetFuncCardCfg_Handler,
		},
		{
			MethodName: "GiveUserPackage",
			Handler:    _Backpack_GiveUserPackage_Handler,
		},
		{
			MethodName: "UseBackpackItem",
			Handler:    _Backpack_UseBackpackItem_Handler,
		},
		{
			MethodName: "GetUserBackpack",
			Handler:    _Backpack_GetUserBackpack_Handler,
		},
		{
			MethodName: "CheckUserFuncCardUse",
			Handler:    _Backpack_CheckUserFuncCardUse_Handler,
		},
		{
			MethodName: "SetUserFuncCardUse",
			Handler:    _Backpack_SetUserFuncCardUse_Handler,
		},
		{
			MethodName: "GetUserFuncCardUse",
			Handler:    _Backpack_GetUserFuncCardUse_Handler,
		},
		{
			MethodName: "ProcBackpackItemTimeout",
			Handler:    _Backpack_ProcBackpackItemTimeout_Handler,
		},
		{
			MethodName: "GetUserBackpackLog",
			Handler:    _Backpack_GetUserBackpackLog_Handler,
		},
		{
			MethodName: "AddItemCfg",
			Handler:    _Backpack_AddItemCfg_Handler,
		},
		{
			MethodName: "DelItemCfg",
			Handler:    _Backpack_DelItemCfg_Handler,
		},
		{
			MethodName: "GetItemCfg",
			Handler:    _Backpack_GetItemCfg_Handler,
		},
		{
			MethodName: "FreeZeItem",
			Handler:    _Backpack_FreeZeItem_Handler,
		},
		{
			MethodName: "GetUserPackageSum",
			Handler:    _Backpack_GetUserPackageSum_Handler,
		},
		{
			MethodName: "GetOrderCountByTimeRange",
			Handler:    _Backpack_GetOrderCountByTimeRange_Handler,
		},
		{
			MethodName: "GetOrderListByTimeRange",
			Handler:    _Backpack_GetOrderListByTimeRange_Handler,
		},
		{
			MethodName: "ConversionItem",
			Handler:    _Backpack_ConversionItem_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/backpacksvr/backpack.proto",
}

func (m *PackageCfg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PackageCfg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BgId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.BgId))
	}
	if len(m.Name) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.Name)))
		i += copy(dAtA[i:], m.Name)
	}
	if len(m.Desc) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.Desc)))
		i += copy(dAtA[i:], m.Desc)
	}
	if m.IsDel {
		dAtA[i] = 0x20
		i++
		if m.IsDel {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	return i, nil
}

func (m *PackageItemCfg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PackageItemCfg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BgItemId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.BgItemId))
	}
	if m.BgId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.BgId))
	}
	if m.ItemType != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemType))
	}
	if m.SourceId != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.SourceId))
	}
	if m.ItemCount != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemCount))
	}
	if m.FinTime != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.FinTime))
	}
	if m.IsDel {
		dAtA[i] = 0x38
		i++
		if m.IsDel {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	if m.Weight != 0 {
		dAtA[i] = 0x40
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Weight))
	}
	if m.DynamicFinTime != 0 {
		dAtA[i] = 0x48
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.DynamicFinTime))
	}
	return i, nil
}

func (m *FuncCardCfg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FuncCardCfg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.CardId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.CardId))
	}
	if m.CardType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.CardType))
	}
	if len(m.CardName) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.CardName)))
		i += copy(dAtA[i:], m.CardName)
	}
	if len(m.CardDesc) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.CardDesc)))
		i += copy(dAtA[i:], m.CardDesc)
	}
	if len(m.CardUrl) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.CardUrl)))
		i += copy(dAtA[i:], m.CardUrl)
	}
	if m.CardTimes != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.CardTimes))
	}
	if m.ValidTime != 0 {
		dAtA[i] = 0x38
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ValidTime))
	}
	if m.IsDel != 0 {
		dAtA[i] = 0x40
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.IsDel))
	}
	return i, nil
}

func (m *LotteryFragmentCfg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LotteryFragmentCfg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.FragmentId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.FragmentId))
	}
	if m.FragmentType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.FragmentType))
	}
	if len(m.FragmentName) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.FragmentName)))
		i += copy(dAtA[i:], m.FragmentName)
	}
	if len(m.FragmentDesc) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.FragmentDesc)))
		i += copy(dAtA[i:], m.FragmentDesc)
	}
	if len(m.FragmentUrl) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.FragmentUrl)))
		i += copy(dAtA[i:], m.FragmentUrl)
	}
	if m.IsDel != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.IsDel))
	}
	if m.FragmentPrice != 0 {
		dAtA[i] = 0x38
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.FragmentPrice))
	}
	return i, nil
}

func (m *UserBackpackItem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserBackpackItem) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ItemType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemType))
	}
	if m.UserItemId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.UserItemId))
	}
	if m.ItemCount != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemCount))
	}
	if m.FinTime != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.FinTime))
	}
	if m.SourceId != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.SourceId))
	}
	if m.Weight != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Weight))
	}
	if m.ObtainTime != 0 {
		dAtA[i] = 0x38
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ObtainTime))
	}
	return i, nil
}

func (m *UserBackpackLog) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserBackpackLog) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ItemType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemType))
	}
	if m.ItemCount != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemCount))
	}
	if m.SourceId != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.SourceId))
	}
	if m.LogType != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.LogType))
	}
	if m.LogTime != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.LogTime))
	}
	return i, nil
}

func (m *AddPackageCfgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddPackageCfgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Cfg != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Cfg.Size()))
		n1, err := m.Cfg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *AddPackageCfgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddPackageCfgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Cfg != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Cfg.Size()))
		n2, err := m.Cfg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func (m *DelPackageCfgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelPackageCfgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BgId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.BgId))
	}
	return i, nil
}

func (m *DelPackageCfgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelPackageCfgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetPackageCfgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPackageCfgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetPackageCfgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPackageCfgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.CfgList) > 0 {
		for _, msg := range m.CfgList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintBackpack(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *AddPackageItemCfgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddPackageItemCfgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ItemCfg != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemCfg.Size()))
		n3, err := m.ItemCfg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	return i, nil
}

func (m *AddPackageItemCfgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddPackageItemCfgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ItemCfg != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemCfg.Size()))
		n4, err := m.ItemCfg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	return i, nil
}

func (m *ModPackageItemCfgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModPackageItemCfgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ItemCfg != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemCfg.Size()))
		n5, err := m.ItemCfg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	return i, nil
}

func (m *ModPackageItemCfgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModPackageItemCfgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *DelPackageItemCfgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelPackageItemCfgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BgId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.BgId))
	}
	if m.BgItemId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.BgItemId))
	}
	return i, nil
}

func (m *DelPackageItemCfgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelPackageItemCfgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetPackageItemCfgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPackageItemCfgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BgId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.BgId))
	}
	return i, nil
}

func (m *GetPackageItemCfgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPackageItemCfgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ItemCfgList) > 0 {
		for _, msg := range m.ItemCfgList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintBackpack(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GiveUserPackageReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GiveUserPackageReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Uid))
	}
	if m.BgId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.BgId))
	}
	if m.Num != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Num))
	}
	if m.Source != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Source))
	}
	if len(m.OrderId) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.OrderId)))
		i += copy(dAtA[i:], m.OrderId)
	}
	if m.ExpireDuration != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ExpireDuration))
	}
	if len(m.SourceAppId) > 0 {
		dAtA[i] = 0x3a
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.SourceAppId)))
		i += copy(dAtA[i:], m.SourceAppId)
	}
	if m.TotalPrice != 0 {
		dAtA[i] = 0x40
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.TotalPrice))
	}
	if m.OutsideTime != 0 {
		dAtA[i] = 0x48
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.OutsideTime))
	}
	return i, nil
}

func (m *GiveUserPackageResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GiveUserPackageResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *UseBackpackItemReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UseBackpackItemReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Uid))
	}
	if m.ItemType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemType))
	}
	if m.UserItemId != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.UserItemId))
	}
	if m.SourceId != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.SourceId))
	}
	if len(m.OrderId) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.OrderId)))
		i += copy(dAtA[i:], m.OrderId)
	}
	if m.UseCount != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.UseCount))
	}
	if m.OutsideTime != 0 {
		dAtA[i] = 0x38
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.OutsideTime))
	}
	if len(m.OrderIdList) > 0 {
		for _, s := range m.OrderIdList {
			dAtA[i] = 0x42
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *UseBackpackItemResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UseBackpackItemResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Remain != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Remain))
	}
	return i, nil
}

func (m *ProcBackpackItemTimeoutReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ProcBackpackItemTimeoutReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.UserItemId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.UserItemId))
	}
	if m.Uid != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Uid))
	}
	if m.ItemType != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemType))
	}
	if m.SourceId != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.SourceId))
	}
	if m.ItemCount != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemCount))
	}
	if m.FinTime != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.FinTime))
	}
	return i, nil
}

func (m *ProcBackpackItemTimeoutResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ProcBackpackItemTimeoutResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetUserBackpackReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserBackpackReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Uid))
	}
	return i, nil
}

func (m *GetUserBackpackResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserBackpackResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UserItemList) > 0 {
		for _, msg := range m.UserItemList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintBackpack(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.LastObtainTs != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.LastObtainTs))
	}
	return i, nil
}

func (m *GetUserFuncCardUseReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserFuncCardUseReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Uid))
	}
	return i, nil
}

func (m *GetUserFuncCardUseResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserFuncCardUseResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UserItemList) > 0 {
		for _, msg := range m.UserItemList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintBackpack(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *AddFuncCardCfgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddFuncCardCfgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.CardCfg != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.CardCfg.Size()))
		n6, err := m.CardCfg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	return i, nil
}

func (m *AddFuncCardCfgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddFuncCardCfgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *DelFuncCardCfgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelFuncCardCfgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.CardId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.CardId))
	}
	return i, nil
}

func (m *DelFuncCardCfgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelFuncCardCfgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetFuncCardCfgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFuncCardCfgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.CardIdList) > 0 {
		dAtA8 := make([]byte, len(m.CardIdList)*10)
		var j7 int
		for _, num := range m.CardIdList {
			for num >= 1<<7 {
				dAtA8[j7] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j7++
			}
			dAtA8[j7] = uint8(num)
			j7++
		}
		dAtA[i] = 0xa
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(j7))
		i += copy(dAtA[i:], dAtA8[:j7])
	}
	return i, nil
}

func (m *GetFuncCardCfgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFuncCardCfgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.CardCfgList) > 0 {
		for _, msg := range m.CardCfgList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintBackpack(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *CheckUserFuncCardUseReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUserFuncCardUseReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Uid))
	}
	if m.CardType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.CardType))
	}
	return i, nil
}

func (m *CheckUserFuncCardUseResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUserFuncCardUseResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.IsUse {
		dAtA[i] = 0x8
		i++
		if m.IsUse {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	return i, nil
}

func (m *SetUserFuncCardUseReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetUserFuncCardUseReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Uid))
	}
	if m.CardType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.CardType))
	}
	if m.CardId != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.CardId))
	}
	return i, nil
}

func (m *SetUserFuncCardUseResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetUserFuncCardUseResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetUserBackpackLogReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserBackpackLogReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Uid))
	}
	if m.BeginTime != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.BeginTime))
	}
	if m.EndTime != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.EndTime))
	}
	if m.ItemType != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemType))
	}
	if m.SourceId != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.SourceId))
	}
	if m.LogType != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.LogType))
	}
	return i, nil
}

func (m *GetUserBackpackLogResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserBackpackLogResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.LogList) > 0 {
		for _, msg := range m.LogList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintBackpack(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *AddItemCfgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddItemCfgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ItemType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemType))
	}
	if len(m.ItemCfg) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.ItemCfg)))
		i += copy(dAtA[i:], m.ItemCfg)
	}
	return i, nil
}

func (m *AddItemCfgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddItemCfgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ItemType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemType))
	}
	if len(m.ItemCfg) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.ItemCfg)))
		i += copy(dAtA[i:], m.ItemCfg)
	}
	return i, nil
}

func (m *DelItemCfgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelItemCfgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ItemType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemType))
	}
	if len(m.ItemCfg) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.ItemCfg)))
		i += copy(dAtA[i:], m.ItemCfg)
	}
	return i, nil
}

func (m *DelItemCfgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelItemCfgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetItemCfgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetItemCfgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ItemType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemType))
	}
	if len(m.ItemSourceIdList) > 0 {
		dAtA10 := make([]byte, len(m.ItemSourceIdList)*10)
		var j9 int
		for _, num := range m.ItemSourceIdList {
			for num >= 1<<7 {
				dAtA10[j9] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j9++
			}
			dAtA10[j9] = uint8(num)
			j9++
		}
		dAtA[i] = 0x12
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(j9))
		i += copy(dAtA[i:], dAtA10[:j9])
	}
	if m.GetAll {
		dAtA[i] = 0x18
		i++
		if m.GetAll {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	return i, nil
}

func (m *GetItemCfgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetItemCfgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ItemCfgList) > 0 {
		for _, b := range m.ItemCfgList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintBackpack(dAtA, i, uint64(len(b)))
			i += copy(dAtA[i:], b)
		}
	}
	return i, nil
}

func (m *UseItemInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UseItemInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ItemType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemType))
	}
	if m.UserItemId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.UserItemId))
	}
	if m.SourceId != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.SourceId))
	}
	if m.UseCount != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.UseCount))
	}
	return i, nil
}

func (m *TransactionInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TransactionInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.FreezeType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.FreezeType))
	}
	if m.OperTime != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.OperTime))
	}
	if len(m.OrderId) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.OrderId)))
		i += copy(dAtA[i:], m.OrderId)
	}
	if m.ExpireTime != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ExpireTime))
	}
	return i, nil
}

func (m *FreeZeItemReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FreeZeItemReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.TansacationInfo != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.TansacationInfo.Size()))
		n11, err := m.TansacationInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	if len(m.ItemInfoList) > 0 {
		for _, msg := range m.ItemInfoList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintBackpack(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.Uid != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Uid))
	}
	return i, nil
}

func (m *FreeZeItemResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FreeZeItemResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *UserPackageSum) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserPackageSum) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ItemType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemType))
	}
	if m.ItemId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemId))
	}
	if m.ItemCount != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemCount))
	}
	return i, nil
}

func (m *GetUserPackageSumReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserPackageSumReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Uid))
	}
	if m.ItemType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemType))
	}
	if m.ItemId != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemId))
	}
	return i, nil
}

func (m *GetUserPackageSumResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserPackageSumResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ItemSum != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ItemSum.Size()))
		n12, err := m.ItemSum.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n12
	}
	return i, nil
}

func (m *GetOrderCountByTimeRangeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOrderCountByTimeRangeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BeginTime != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.BeginTime))
	}
	if m.EndTime != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.EndTime))
	}
	if m.LogType != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.LogType))
	}
	return i, nil
}

func (m *GetOrderCountByTimeRangeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOrderCountByTimeRangeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Count != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Count))
	}
	return i, nil
}

func (m *GetOrderListByTimeRangeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOrderListByTimeRangeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BeginTime != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.BeginTime))
	}
	if m.EndTime != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.EndTime))
	}
	if m.LogType != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.LogType))
	}
	return i, nil
}

func (m *GetOrderListByTimeRangeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOrderListByTimeRangeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.OrderList) > 0 {
		for _, s := range m.OrderList {
			dAtA[i] = 0xa
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *ConversionItemReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ConversionItemReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Uid))
	}
	if m.OutsideTime != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.OutsideTime))
	}
	if m.BgId != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.BgId))
	}
	if m.BgNum != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.BgNum))
	}
	if m.Source != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.Source))
	}
	if m.MaterialPrice != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.MaterialPrice))
	}
	if m.ConversionPrice != 0 {
		dAtA[i] = 0x38
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(m.ConversionPrice))
	}
	if len(m.OrderId) > 0 {
		dAtA[i] = 0x42
		i++
		i = encodeVarintBackpack(dAtA, i, uint64(len(m.OrderId)))
		i += copy(dAtA[i:], m.OrderId)
	}
	if len(m.MaterialItemList) > 0 {
		for _, msg := range m.MaterialItemList {
			dAtA[i] = 0x4a
			i++
			i = encodeVarintBackpack(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ConversionItemResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ConversionItemResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func encodeFixed64Backpack(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Backpack(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintBackpack(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *PackageCfg) Size() (n int) {
	var l int
	_ = l
	if m.BgId != 0 {
		n += 1 + sovBackpack(uint64(m.BgId))
	}
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	l = len(m.Desc)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	if m.IsDel {
		n += 2
	}
	return n
}

func (m *PackageItemCfg) Size() (n int) {
	var l int
	_ = l
	if m.BgItemId != 0 {
		n += 1 + sovBackpack(uint64(m.BgItemId))
	}
	if m.BgId != 0 {
		n += 1 + sovBackpack(uint64(m.BgId))
	}
	if m.ItemType != 0 {
		n += 1 + sovBackpack(uint64(m.ItemType))
	}
	if m.SourceId != 0 {
		n += 1 + sovBackpack(uint64(m.SourceId))
	}
	if m.ItemCount != 0 {
		n += 1 + sovBackpack(uint64(m.ItemCount))
	}
	if m.FinTime != 0 {
		n += 1 + sovBackpack(uint64(m.FinTime))
	}
	if m.IsDel {
		n += 2
	}
	if m.Weight != 0 {
		n += 1 + sovBackpack(uint64(m.Weight))
	}
	if m.DynamicFinTime != 0 {
		n += 1 + sovBackpack(uint64(m.DynamicFinTime))
	}
	return n
}

func (m *FuncCardCfg) Size() (n int) {
	var l int
	_ = l
	if m.CardId != 0 {
		n += 1 + sovBackpack(uint64(m.CardId))
	}
	if m.CardType != 0 {
		n += 1 + sovBackpack(uint64(m.CardType))
	}
	l = len(m.CardName)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	l = len(m.CardDesc)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	l = len(m.CardUrl)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	if m.CardTimes != 0 {
		n += 1 + sovBackpack(uint64(m.CardTimes))
	}
	if m.ValidTime != 0 {
		n += 1 + sovBackpack(uint64(m.ValidTime))
	}
	if m.IsDel != 0 {
		n += 1 + sovBackpack(uint64(m.IsDel))
	}
	return n
}

func (m *LotteryFragmentCfg) Size() (n int) {
	var l int
	_ = l
	if m.FragmentId != 0 {
		n += 1 + sovBackpack(uint64(m.FragmentId))
	}
	if m.FragmentType != 0 {
		n += 1 + sovBackpack(uint64(m.FragmentType))
	}
	l = len(m.FragmentName)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	l = len(m.FragmentDesc)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	l = len(m.FragmentUrl)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	if m.IsDel != 0 {
		n += 1 + sovBackpack(uint64(m.IsDel))
	}
	if m.FragmentPrice != 0 {
		n += 1 + sovBackpack(uint64(m.FragmentPrice))
	}
	return n
}

func (m *UserBackpackItem) Size() (n int) {
	var l int
	_ = l
	if m.ItemType != 0 {
		n += 1 + sovBackpack(uint64(m.ItemType))
	}
	if m.UserItemId != 0 {
		n += 1 + sovBackpack(uint64(m.UserItemId))
	}
	if m.ItemCount != 0 {
		n += 1 + sovBackpack(uint64(m.ItemCount))
	}
	if m.FinTime != 0 {
		n += 1 + sovBackpack(uint64(m.FinTime))
	}
	if m.SourceId != 0 {
		n += 1 + sovBackpack(uint64(m.SourceId))
	}
	if m.Weight != 0 {
		n += 1 + sovBackpack(uint64(m.Weight))
	}
	if m.ObtainTime != 0 {
		n += 1 + sovBackpack(uint64(m.ObtainTime))
	}
	return n
}

func (m *UserBackpackLog) Size() (n int) {
	var l int
	_ = l
	if m.ItemType != 0 {
		n += 1 + sovBackpack(uint64(m.ItemType))
	}
	if m.ItemCount != 0 {
		n += 1 + sovBackpack(uint64(m.ItemCount))
	}
	if m.SourceId != 0 {
		n += 1 + sovBackpack(uint64(m.SourceId))
	}
	if m.LogType != 0 {
		n += 1 + sovBackpack(uint64(m.LogType))
	}
	if m.LogTime != 0 {
		n += 1 + sovBackpack(uint64(m.LogTime))
	}
	return n
}

func (m *AddPackageCfgReq) Size() (n int) {
	var l int
	_ = l
	if m.Cfg != nil {
		l = m.Cfg.Size()
		n += 1 + l + sovBackpack(uint64(l))
	}
	return n
}

func (m *AddPackageCfgResp) Size() (n int) {
	var l int
	_ = l
	if m.Cfg != nil {
		l = m.Cfg.Size()
		n += 1 + l + sovBackpack(uint64(l))
	}
	return n
}

func (m *DelPackageCfgReq) Size() (n int) {
	var l int
	_ = l
	if m.BgId != 0 {
		n += 1 + sovBackpack(uint64(m.BgId))
	}
	return n
}

func (m *DelPackageCfgResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetPackageCfgReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetPackageCfgResp) Size() (n int) {
	var l int
	_ = l
	if len(m.CfgList) > 0 {
		for _, e := range m.CfgList {
			l = e.Size()
			n += 1 + l + sovBackpack(uint64(l))
		}
	}
	return n
}

func (m *AddPackageItemCfgReq) Size() (n int) {
	var l int
	_ = l
	if m.ItemCfg != nil {
		l = m.ItemCfg.Size()
		n += 1 + l + sovBackpack(uint64(l))
	}
	return n
}

func (m *AddPackageItemCfgResp) Size() (n int) {
	var l int
	_ = l
	if m.ItemCfg != nil {
		l = m.ItemCfg.Size()
		n += 1 + l + sovBackpack(uint64(l))
	}
	return n
}

func (m *ModPackageItemCfgReq) Size() (n int) {
	var l int
	_ = l
	if m.ItemCfg != nil {
		l = m.ItemCfg.Size()
		n += 1 + l + sovBackpack(uint64(l))
	}
	return n
}

func (m *ModPackageItemCfgResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *DelPackageItemCfgReq) Size() (n int) {
	var l int
	_ = l
	if m.BgId != 0 {
		n += 1 + sovBackpack(uint64(m.BgId))
	}
	if m.BgItemId != 0 {
		n += 1 + sovBackpack(uint64(m.BgItemId))
	}
	return n
}

func (m *DelPackageItemCfgResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetPackageItemCfgReq) Size() (n int) {
	var l int
	_ = l
	if m.BgId != 0 {
		n += 1 + sovBackpack(uint64(m.BgId))
	}
	return n
}

func (m *GetPackageItemCfgResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ItemCfgList) > 0 {
		for _, e := range m.ItemCfgList {
			l = e.Size()
			n += 1 + l + sovBackpack(uint64(l))
		}
	}
	return n
}

func (m *GiveUserPackageReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovBackpack(uint64(m.Uid))
	}
	if m.BgId != 0 {
		n += 1 + sovBackpack(uint64(m.BgId))
	}
	if m.Num != 0 {
		n += 1 + sovBackpack(uint64(m.Num))
	}
	if m.Source != 0 {
		n += 1 + sovBackpack(uint64(m.Source))
	}
	l = len(m.OrderId)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	if m.ExpireDuration != 0 {
		n += 1 + sovBackpack(uint64(m.ExpireDuration))
	}
	l = len(m.SourceAppId)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	if m.TotalPrice != 0 {
		n += 1 + sovBackpack(uint64(m.TotalPrice))
	}
	if m.OutsideTime != 0 {
		n += 1 + sovBackpack(uint64(m.OutsideTime))
	}
	return n
}

func (m *GiveUserPackageResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *UseBackpackItemReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovBackpack(uint64(m.Uid))
	}
	if m.ItemType != 0 {
		n += 1 + sovBackpack(uint64(m.ItemType))
	}
	if m.UserItemId != 0 {
		n += 1 + sovBackpack(uint64(m.UserItemId))
	}
	if m.SourceId != 0 {
		n += 1 + sovBackpack(uint64(m.SourceId))
	}
	l = len(m.OrderId)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	if m.UseCount != 0 {
		n += 1 + sovBackpack(uint64(m.UseCount))
	}
	if m.OutsideTime != 0 {
		n += 1 + sovBackpack(uint64(m.OutsideTime))
	}
	if len(m.OrderIdList) > 0 {
		for _, s := range m.OrderIdList {
			l = len(s)
			n += 1 + l + sovBackpack(uint64(l))
		}
	}
	return n
}

func (m *UseBackpackItemResp) Size() (n int) {
	var l int
	_ = l
	if m.Remain != 0 {
		n += 1 + sovBackpack(uint64(m.Remain))
	}
	return n
}

func (m *ProcBackpackItemTimeoutReq) Size() (n int) {
	var l int
	_ = l
	if m.UserItemId != 0 {
		n += 1 + sovBackpack(uint64(m.UserItemId))
	}
	if m.Uid != 0 {
		n += 1 + sovBackpack(uint64(m.Uid))
	}
	if m.ItemType != 0 {
		n += 1 + sovBackpack(uint64(m.ItemType))
	}
	if m.SourceId != 0 {
		n += 1 + sovBackpack(uint64(m.SourceId))
	}
	if m.ItemCount != 0 {
		n += 1 + sovBackpack(uint64(m.ItemCount))
	}
	if m.FinTime != 0 {
		n += 1 + sovBackpack(uint64(m.FinTime))
	}
	return n
}

func (m *ProcBackpackItemTimeoutResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetUserBackpackReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovBackpack(uint64(m.Uid))
	}
	return n
}

func (m *GetUserBackpackResp) Size() (n int) {
	var l int
	_ = l
	if len(m.UserItemList) > 0 {
		for _, e := range m.UserItemList {
			l = e.Size()
			n += 1 + l + sovBackpack(uint64(l))
		}
	}
	if m.LastObtainTs != 0 {
		n += 1 + sovBackpack(uint64(m.LastObtainTs))
	}
	return n
}

func (m *GetUserFuncCardUseReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovBackpack(uint64(m.Uid))
	}
	return n
}

func (m *GetUserFuncCardUseResp) Size() (n int) {
	var l int
	_ = l
	if len(m.UserItemList) > 0 {
		for _, e := range m.UserItemList {
			l = e.Size()
			n += 1 + l + sovBackpack(uint64(l))
		}
	}
	return n
}

func (m *AddFuncCardCfgReq) Size() (n int) {
	var l int
	_ = l
	if m.CardCfg != nil {
		l = m.CardCfg.Size()
		n += 1 + l + sovBackpack(uint64(l))
	}
	return n
}

func (m *AddFuncCardCfgResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *DelFuncCardCfgReq) Size() (n int) {
	var l int
	_ = l
	if m.CardId != 0 {
		n += 1 + sovBackpack(uint64(m.CardId))
	}
	return n
}

func (m *DelFuncCardCfgResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetFuncCardCfgReq) Size() (n int) {
	var l int
	_ = l
	if len(m.CardIdList) > 0 {
		l = 0
		for _, e := range m.CardIdList {
			l += sovBackpack(uint64(e))
		}
		n += 1 + sovBackpack(uint64(l)) + l
	}
	return n
}

func (m *GetFuncCardCfgResp) Size() (n int) {
	var l int
	_ = l
	if len(m.CardCfgList) > 0 {
		for _, e := range m.CardCfgList {
			l = e.Size()
			n += 1 + l + sovBackpack(uint64(l))
		}
	}
	return n
}

func (m *CheckUserFuncCardUseReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovBackpack(uint64(m.Uid))
	}
	if m.CardType != 0 {
		n += 1 + sovBackpack(uint64(m.CardType))
	}
	return n
}

func (m *CheckUserFuncCardUseResp) Size() (n int) {
	var l int
	_ = l
	if m.IsUse {
		n += 2
	}
	return n
}

func (m *SetUserFuncCardUseReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovBackpack(uint64(m.Uid))
	}
	if m.CardType != 0 {
		n += 1 + sovBackpack(uint64(m.CardType))
	}
	if m.CardId != 0 {
		n += 1 + sovBackpack(uint64(m.CardId))
	}
	return n
}

func (m *SetUserFuncCardUseResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetUserBackpackLogReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovBackpack(uint64(m.Uid))
	}
	if m.BeginTime != 0 {
		n += 1 + sovBackpack(uint64(m.BeginTime))
	}
	if m.EndTime != 0 {
		n += 1 + sovBackpack(uint64(m.EndTime))
	}
	if m.ItemType != 0 {
		n += 1 + sovBackpack(uint64(m.ItemType))
	}
	if m.SourceId != 0 {
		n += 1 + sovBackpack(uint64(m.SourceId))
	}
	if m.LogType != 0 {
		n += 1 + sovBackpack(uint64(m.LogType))
	}
	return n
}

func (m *GetUserBackpackLogResp) Size() (n int) {
	var l int
	_ = l
	if len(m.LogList) > 0 {
		for _, e := range m.LogList {
			l = e.Size()
			n += 1 + l + sovBackpack(uint64(l))
		}
	}
	return n
}

func (m *AddItemCfgReq) Size() (n int) {
	var l int
	_ = l
	if m.ItemType != 0 {
		n += 1 + sovBackpack(uint64(m.ItemType))
	}
	l = len(m.ItemCfg)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	return n
}

func (m *AddItemCfgResp) Size() (n int) {
	var l int
	_ = l
	if m.ItemType != 0 {
		n += 1 + sovBackpack(uint64(m.ItemType))
	}
	l = len(m.ItemCfg)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	return n
}

func (m *DelItemCfgReq) Size() (n int) {
	var l int
	_ = l
	if m.ItemType != 0 {
		n += 1 + sovBackpack(uint64(m.ItemType))
	}
	l = len(m.ItemCfg)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	return n
}

func (m *DelItemCfgResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetItemCfgReq) Size() (n int) {
	var l int
	_ = l
	if m.ItemType != 0 {
		n += 1 + sovBackpack(uint64(m.ItemType))
	}
	if len(m.ItemSourceIdList) > 0 {
		l = 0
		for _, e := range m.ItemSourceIdList {
			l += sovBackpack(uint64(e))
		}
		n += 1 + sovBackpack(uint64(l)) + l
	}
	if m.GetAll {
		n += 2
	}
	return n
}

func (m *GetItemCfgResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ItemCfgList) > 0 {
		for _, b := range m.ItemCfgList {
			l = len(b)
			n += 1 + l + sovBackpack(uint64(l))
		}
	}
	return n
}

func (m *UseItemInfo) Size() (n int) {
	var l int
	_ = l
	if m.ItemType != 0 {
		n += 1 + sovBackpack(uint64(m.ItemType))
	}
	if m.UserItemId != 0 {
		n += 1 + sovBackpack(uint64(m.UserItemId))
	}
	if m.SourceId != 0 {
		n += 1 + sovBackpack(uint64(m.SourceId))
	}
	if m.UseCount != 0 {
		n += 1 + sovBackpack(uint64(m.UseCount))
	}
	return n
}

func (m *TransactionInfo) Size() (n int) {
	var l int
	_ = l
	if m.FreezeType != 0 {
		n += 1 + sovBackpack(uint64(m.FreezeType))
	}
	if m.OperTime != 0 {
		n += 1 + sovBackpack(uint64(m.OperTime))
	}
	l = len(m.OrderId)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	if m.ExpireTime != 0 {
		n += 1 + sovBackpack(uint64(m.ExpireTime))
	}
	return n
}

func (m *FreeZeItemReq) Size() (n int) {
	var l int
	_ = l
	if m.TansacationInfo != nil {
		l = m.TansacationInfo.Size()
		n += 1 + l + sovBackpack(uint64(l))
	}
	if len(m.ItemInfoList) > 0 {
		for _, e := range m.ItemInfoList {
			l = e.Size()
			n += 1 + l + sovBackpack(uint64(l))
		}
	}
	if m.Uid != 0 {
		n += 1 + sovBackpack(uint64(m.Uid))
	}
	return n
}

func (m *FreeZeItemResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *UserPackageSum) Size() (n int) {
	var l int
	_ = l
	if m.ItemType != 0 {
		n += 1 + sovBackpack(uint64(m.ItemType))
	}
	if m.ItemId != 0 {
		n += 1 + sovBackpack(uint64(m.ItemId))
	}
	if m.ItemCount != 0 {
		n += 1 + sovBackpack(uint64(m.ItemCount))
	}
	return n
}

func (m *GetUserPackageSumReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovBackpack(uint64(m.Uid))
	}
	if m.ItemType != 0 {
		n += 1 + sovBackpack(uint64(m.ItemType))
	}
	if m.ItemId != 0 {
		n += 1 + sovBackpack(uint64(m.ItemId))
	}
	return n
}

func (m *GetUserPackageSumResp) Size() (n int) {
	var l int
	_ = l
	if m.ItemSum != nil {
		l = m.ItemSum.Size()
		n += 1 + l + sovBackpack(uint64(l))
	}
	return n
}

func (m *GetOrderCountByTimeRangeReq) Size() (n int) {
	var l int
	_ = l
	if m.BeginTime != 0 {
		n += 1 + sovBackpack(uint64(m.BeginTime))
	}
	if m.EndTime != 0 {
		n += 1 + sovBackpack(uint64(m.EndTime))
	}
	if m.LogType != 0 {
		n += 1 + sovBackpack(uint64(m.LogType))
	}
	return n
}

func (m *GetOrderCountByTimeRangeResp) Size() (n int) {
	var l int
	_ = l
	if m.Count != 0 {
		n += 1 + sovBackpack(uint64(m.Count))
	}
	return n
}

func (m *GetOrderListByTimeRangeReq) Size() (n int) {
	var l int
	_ = l
	if m.BeginTime != 0 {
		n += 1 + sovBackpack(uint64(m.BeginTime))
	}
	if m.EndTime != 0 {
		n += 1 + sovBackpack(uint64(m.EndTime))
	}
	if m.LogType != 0 {
		n += 1 + sovBackpack(uint64(m.LogType))
	}
	return n
}

func (m *GetOrderListByTimeRangeResp) Size() (n int) {
	var l int
	_ = l
	if len(m.OrderList) > 0 {
		for _, s := range m.OrderList {
			l = len(s)
			n += 1 + l + sovBackpack(uint64(l))
		}
	}
	return n
}

func (m *ConversionItemReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovBackpack(uint64(m.Uid))
	}
	if m.OutsideTime != 0 {
		n += 1 + sovBackpack(uint64(m.OutsideTime))
	}
	if m.BgId != 0 {
		n += 1 + sovBackpack(uint64(m.BgId))
	}
	if m.BgNum != 0 {
		n += 1 + sovBackpack(uint64(m.BgNum))
	}
	if m.Source != 0 {
		n += 1 + sovBackpack(uint64(m.Source))
	}
	if m.MaterialPrice != 0 {
		n += 1 + sovBackpack(uint64(m.MaterialPrice))
	}
	if m.ConversionPrice != 0 {
		n += 1 + sovBackpack(uint64(m.ConversionPrice))
	}
	l = len(m.OrderId)
	if l > 0 {
		n += 1 + l + sovBackpack(uint64(l))
	}
	if len(m.MaterialItemList) > 0 {
		for _, e := range m.MaterialItemList {
			l = e.Size()
			n += 1 + l + sovBackpack(uint64(l))
		}
	}
	return n
}

func (m *ConversionItemResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func sovBackpack(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozBackpack(x uint64) (n int) {
	return sovBackpack(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *PackageCfg) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PackageCfg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PackageCfg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BgId", wireType)
			}
			m.BgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Desc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Desc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsDel", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsDel = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PackageItemCfg) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PackageItemCfg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PackageItemCfg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BgItemId", wireType)
			}
			m.BgItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BgItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BgId", wireType)
			}
			m.BgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceId", wireType)
			}
			m.SourceId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemCount", wireType)
			}
			m.ItemCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FinTime", wireType)
			}
			m.FinTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FinTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsDel", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsDel = bool(v != 0)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Weight", wireType)
			}
			m.Weight = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Weight |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DynamicFinTime", wireType)
			}
			m.DynamicFinTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DynamicFinTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FuncCardCfg) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FuncCardCfg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FuncCardCfg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardId", wireType)
			}
			m.CardId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CardId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardType", wireType)
			}
			m.CardType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CardType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CardName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CardDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CardUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardTimes", wireType)
			}
			m.CardTimes = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CardTimes |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ValidTime", wireType)
			}
			m.ValidTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ValidTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsDel", wireType)
			}
			m.IsDel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IsDel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LotteryFragmentCfg) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LotteryFragmentCfg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LotteryFragmentCfg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FragmentId", wireType)
			}
			m.FragmentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FragmentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FragmentType", wireType)
			}
			m.FragmentType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FragmentType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FragmentName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FragmentName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FragmentDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FragmentDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FragmentUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FragmentUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsDel", wireType)
			}
			m.IsDel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IsDel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FragmentPrice", wireType)
			}
			m.FragmentPrice = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FragmentPrice |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserBackpackItem) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserBackpackItem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserBackpackItem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserItemId", wireType)
			}
			m.UserItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UserItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemCount", wireType)
			}
			m.ItemCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FinTime", wireType)
			}
			m.FinTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FinTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceId", wireType)
			}
			m.SourceId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Weight", wireType)
			}
			m.Weight = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Weight |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ObtainTime", wireType)
			}
			m.ObtainTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ObtainTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserBackpackLog) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserBackpackLog: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserBackpackLog: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemCount", wireType)
			}
			m.ItemCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceId", wireType)
			}
			m.SourceId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LogType", wireType)
			}
			m.LogType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LogType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LogTime", wireType)
			}
			m.LogTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LogTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddPackageCfgReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddPackageCfgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddPackageCfgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Cfg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Cfg == nil {
				m.Cfg = &PackageCfg{}
			}
			if err := m.Cfg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddPackageCfgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddPackageCfgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddPackageCfgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Cfg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Cfg == nil {
				m.Cfg = &PackageCfg{}
			}
			if err := m.Cfg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelPackageCfgReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelPackageCfgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelPackageCfgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BgId", wireType)
			}
			m.BgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelPackageCfgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelPackageCfgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelPackageCfgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPackageCfgReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPackageCfgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPackageCfgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPackageCfgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPackageCfgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPackageCfgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CfgList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CfgList = append(m.CfgList, &PackageCfg{})
			if err := m.CfgList[len(m.CfgList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddPackageItemCfgReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddPackageItemCfgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddPackageItemCfgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemCfg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ItemCfg == nil {
				m.ItemCfg = &PackageItemCfg{}
			}
			if err := m.ItemCfg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddPackageItemCfgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddPackageItemCfgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddPackageItemCfgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemCfg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ItemCfg == nil {
				m.ItemCfg = &PackageItemCfg{}
			}
			if err := m.ItemCfg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModPackageItemCfgReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModPackageItemCfgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModPackageItemCfgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemCfg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ItemCfg == nil {
				m.ItemCfg = &PackageItemCfg{}
			}
			if err := m.ItemCfg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModPackageItemCfgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModPackageItemCfgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModPackageItemCfgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelPackageItemCfgReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelPackageItemCfgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelPackageItemCfgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BgId", wireType)
			}
			m.BgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BgItemId", wireType)
			}
			m.BgItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BgItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelPackageItemCfgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelPackageItemCfgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelPackageItemCfgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPackageItemCfgReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPackageItemCfgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPackageItemCfgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BgId", wireType)
			}
			m.BgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPackageItemCfgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPackageItemCfgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPackageItemCfgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemCfgList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemCfgList = append(m.ItemCfgList, &PackageItemCfg{})
			if err := m.ItemCfgList[len(m.ItemCfgList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GiveUserPackageReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GiveUserPackageReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GiveUserPackageReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BgId", wireType)
			}
			m.BgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Num", wireType)
			}
			m.Num = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Num |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			m.Source = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Source |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpireDuration", wireType)
			}
			m.ExpireDuration = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpireDuration |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceAppId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SourceAppId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalPrice", wireType)
			}
			m.TotalPrice = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalPrice |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OutsideTime", wireType)
			}
			m.OutsideTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OutsideTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GiveUserPackageResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GiveUserPackageResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GiveUserPackageResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UseBackpackItemReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UseBackpackItemReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UseBackpackItemReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserItemId", wireType)
			}
			m.UserItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UserItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceId", wireType)
			}
			m.SourceId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UseCount", wireType)
			}
			m.UseCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UseCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OutsideTime", wireType)
			}
			m.OutsideTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OutsideTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderIdList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderIdList = append(m.OrderIdList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UseBackpackItemResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UseBackpackItemResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UseBackpackItemResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Remain", wireType)
			}
			m.Remain = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Remain |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ProcBackpackItemTimeoutReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ProcBackpackItemTimeoutReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ProcBackpackItemTimeoutReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserItemId", wireType)
			}
			m.UserItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UserItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceId", wireType)
			}
			m.SourceId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemCount", wireType)
			}
			m.ItemCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FinTime", wireType)
			}
			m.FinTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FinTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ProcBackpackItemTimeoutResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ProcBackpackItemTimeoutResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ProcBackpackItemTimeoutResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserBackpackReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserBackpackReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserBackpackReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserBackpackResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserBackpackResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserBackpackResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserItemList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserItemList = append(m.UserItemList, &UserBackpackItem{})
			if err := m.UserItemList[len(m.UserItemList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastObtainTs", wireType)
			}
			m.LastObtainTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastObtainTs |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserFuncCardUseReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserFuncCardUseReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserFuncCardUseReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserFuncCardUseResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserFuncCardUseResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserFuncCardUseResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserItemList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserItemList = append(m.UserItemList, &FuncCardCfg{})
			if err := m.UserItemList[len(m.UserItemList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddFuncCardCfgReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddFuncCardCfgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddFuncCardCfgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardCfg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.CardCfg == nil {
				m.CardCfg = &FuncCardCfg{}
			}
			if err := m.CardCfg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddFuncCardCfgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddFuncCardCfgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddFuncCardCfgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelFuncCardCfgReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelFuncCardCfgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelFuncCardCfgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardId", wireType)
			}
			m.CardId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CardId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelFuncCardCfgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelFuncCardCfgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelFuncCardCfgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFuncCardCfgReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFuncCardCfgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFuncCardCfgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBackpack
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.CardIdList = append(m.CardIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBackpack
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthBackpack
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowBackpack
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.CardIdList = append(m.CardIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFuncCardCfgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFuncCardCfgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFuncCardCfgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardCfgList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CardCfgList = append(m.CardCfgList, &FuncCardCfg{})
			if err := m.CardCfgList[len(m.CardCfgList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckUserFuncCardUseReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckUserFuncCardUseReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckUserFuncCardUseReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardType", wireType)
			}
			m.CardType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CardType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckUserFuncCardUseResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckUserFuncCardUseResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckUserFuncCardUseResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsUse", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsUse = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetUserFuncCardUseReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetUserFuncCardUseReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetUserFuncCardUseReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardType", wireType)
			}
			m.CardType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CardType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardId", wireType)
			}
			m.CardId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CardId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetUserFuncCardUseResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetUserFuncCardUseResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetUserFuncCardUseResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserBackpackLogReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserBackpackLogReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserBackpackLogReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceId", wireType)
			}
			m.SourceId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LogType", wireType)
			}
			m.LogType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LogType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserBackpackLogResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserBackpackLogResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserBackpackLogResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LogList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LogList = append(m.LogList, &UserBackpackLog{})
			if err := m.LogList[len(m.LogList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddItemCfgReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddItemCfgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddItemCfgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemCfg", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemCfg = append(m.ItemCfg[:0], dAtA[iNdEx:postIndex]...)
			if m.ItemCfg == nil {
				m.ItemCfg = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddItemCfgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddItemCfgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddItemCfgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemCfg", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemCfg = append(m.ItemCfg[:0], dAtA[iNdEx:postIndex]...)
			if m.ItemCfg == nil {
				m.ItemCfg = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelItemCfgReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelItemCfgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelItemCfgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemCfg", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemCfg = append(m.ItemCfg[:0], dAtA[iNdEx:postIndex]...)
			if m.ItemCfg == nil {
				m.ItemCfg = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelItemCfgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelItemCfgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelItemCfgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetItemCfgReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetItemCfgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetItemCfgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBackpack
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ItemSourceIdList = append(m.ItemSourceIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBackpack
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthBackpack
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowBackpack
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ItemSourceIdList = append(m.ItemSourceIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemSourceIdList", wireType)
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GetAll", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.GetAll = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetItemCfgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetItemCfgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetItemCfgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemCfgList", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemCfgList = append(m.ItemCfgList, make([]byte, postIndex-iNdEx))
			copy(m.ItemCfgList[len(m.ItemCfgList)-1], dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UseItemInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UseItemInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UseItemInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserItemId", wireType)
			}
			m.UserItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UserItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceId", wireType)
			}
			m.SourceId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UseCount", wireType)
			}
			m.UseCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UseCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TransactionInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TransactionInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TransactionInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FreezeType", wireType)
			}
			m.FreezeType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FreezeType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperTime", wireType)
			}
			m.OperTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OperTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpireTime", wireType)
			}
			m.ExpireTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpireTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FreeZeItemReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FreeZeItemReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FreeZeItemReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TansacationInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.TansacationInfo == nil {
				m.TansacationInfo = &TransactionInfo{}
			}
			if err := m.TansacationInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemInfoList = append(m.ItemInfoList, &UseItemInfo{})
			if err := m.ItemInfoList[len(m.ItemInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FreeZeItemResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FreeZeItemResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FreeZeItemResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserPackageSum) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserPackageSum: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserPackageSum: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			m.ItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemCount", wireType)
			}
			m.ItemCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserPackageSumReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserPackageSumReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserPackageSumReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			m.ItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserPackageSumResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserPackageSumResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserPackageSumResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemSum", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ItemSum == nil {
				m.ItemSum = &UserPackageSum{}
			}
			if err := m.ItemSum.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOrderCountByTimeRangeReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOrderCountByTimeRangeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOrderCountByTimeRangeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LogType", wireType)
			}
			m.LogType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LogType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOrderCountByTimeRangeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOrderCountByTimeRangeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOrderCountByTimeRangeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOrderListByTimeRangeReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOrderListByTimeRangeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOrderListByTimeRangeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LogType", wireType)
			}
			m.LogType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LogType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOrderListByTimeRangeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOrderListByTimeRangeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOrderListByTimeRangeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderList = append(m.OrderList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ConversionItemReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ConversionItemReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ConversionItemReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OutsideTime", wireType)
			}
			m.OutsideTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OutsideTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BgId", wireType)
			}
			m.BgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BgNum", wireType)
			}
			m.BgNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BgNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			m.Source = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Source |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MaterialPrice", wireType)
			}
			m.MaterialPrice = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MaterialPrice |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConversionPrice", wireType)
			}
			m.ConversionPrice = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConversionPrice |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MaterialItemList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBackpack
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MaterialItemList = append(m.MaterialItemList, &UseItemInfo{})
			if err := m.MaterialItemList[len(m.MaterialItemList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ConversionItemResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ConversionItemResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ConversionItemResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipBackpack(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBackpack
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipBackpack(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowBackpack
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowBackpack
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthBackpack
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowBackpack
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipBackpack(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthBackpack = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowBackpack   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/backpacksvr/backpack.proto", fileDescriptorBackpack) }

var fileDescriptorBackpack = []byte{
	// 3253 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x5a, 0x4b, 0x6f, 0x1b, 0xd7,
	0xf5, 0xf7, 0x90, 0x12, 0x45, 0x1d, 0x49, 0xd4, 0xe8, 0x4a, 0xb2, 0x28, 0xca, 0x96, 0x26, 0x13,
	0x3b, 0x71, 0x9c, 0x3f, 0xed, 0x7f, 0x5e, 0x40, 0xc3, 0x10, 0x44, 0x69, 0x8a, 0x92, 0x09, 0x4b,
	0x94, 0x30, 0xa2, 0x9c, 0x28, 0x8f, 0x32, 0x14, 0x79, 0xc5, 0x4c, 0xcd, 0xc7, 0x98, 0x77, 0xe8,
	0xd8, 0x01, 0x82, 0xb6, 0x8b, 0x16, 0x69, 0xbb, 0x68, 0x1a, 0x34, 0x28, 0xd0, 0x4d, 0xb3, 0xf0,
	0x27, 0xe8, 0xaa, 0xab, 0xae, 0xba, 0xe8, 0xb2, 0x40, 0x57, 0xdd, 0xb5, 0x2e, 0x50, 0x78, 0xd3,
	0xef, 0x50, 0x9c, 0x7b, 0xe7, 0x71, 0x67, 0x38, 0xa4, 0xd5, 0x34, 0xcd, 0x8e, 0x73, 0xee, 0x9d,
	0xf3, 0xf8, 0xdd, 0x73, 0xce, 0x3d, 0xe7, 0x0c, 0x61, 0x93, 0x0d, 0x9a, 0x37, 0x4f, 0x1b, 0xcd,
	0x7b, 0x56, 0xa3, 0x79, 0x8f, 0x3d, 0x18, 0x78, 0xbf, 0x6f, 0x58, 0x83, 0xbe, 0xdd, 0x27, 0x49,
	0xf7, 0x39, 0x73, 0xa5, 0xd9, 0xef, 0x76, 0xfb, 0xbd, 0x9b, 0x76, 0xe7, 0x81, 0x65, 0x36, 0xef,
	0x75, 0xe8, 0x4d, 0x76, 0xef, 0x74, 0x68, 0x76, 0x6c, 0xb3, 0x67, 0x3f, 0xb2, 0xa8, 0xd8, 0xaf,
	0x7f, 0x08, 0x70, 0xd8, 0x68, 0xde, 0x6b, 0xb4, 0x69, 0xe9, 0xac, 0x4d, 0x96, 0x61, 0xfa, 0xb4,
	0x5d, 0x37, 0x5b, 0x69, 0x45, 0x53, 0xae, 0x2d, 0x18, 0x53, 0xa7, 0xed, 0x4a, 0x8b, 0x10, 0x98,
	0xea, 0x35, 0xba, 0x34, 0x1d, 0xd3, 0x94, 0x6b, 0xb3, 0x06, 0xff, 0x8d, 0xb4, 0x16, 0x65, 0xcd,
	0x74, 0x5c, 0xd0, 0xf0, 0x37, 0x59, 0x85, 0x84, 0xc9, 0xea, 0x2d, 0xda, 0x49, 0x4f, 0x69, 0xca,
	0xb5, 0xa4, 0x31, 0x6d, 0xb2, 0x6d, 0xda, 0xd1, 0x3f, 0x8f, 0x41, 0xca, 0x11, 0x51, 0xb1, 0x69,
	0x17, 0xc5, 0x5c, 0x02, 0x40, 0x31, 0x36, 0xed, 0xfa, 0xb2, 0x92, 0xa7, 0x6d, 0x5c, 0xae, 0xb4,
	0x7c, 0x25, 0x62, 0x92, 0x12, 0x1b, 0x30, 0xcb, 0xf7, 0xa3, 0xea, 0x5c, 0xea, 0x82, 0x91, 0x44,
	0x42, 0xed, 0x91, 0x45, 0x71, 0x91, 0xf5, 0x87, 0x83, 0x26, 0xc5, 0xb7, 0xa6, 0xc4, 0xa2, 0x20,
	0x54, 0x5a, 0xe4, 0x32, 0x00, 0x7f, 0xb3, 0xd9, 0x1f, 0xf6, 0xec, 0xf4, 0x34, 0x5f, 0xe5, 0xbc,
	0x4a, 0x48, 0x20, 0xeb, 0x90, 0x3c, 0x33, 0x7b, 0x75, 0xdb, 0xec, 0xd2, 0x74, 0x82, 0x2f, 0xce,
	0x9c, 0x99, 0xbd, 0x9a, 0xd9, 0xa5, 0x92, 0x41, 0x33, 0x92, 0x41, 0xe4, 0x22, 0x24, 0x3e, 0xa6,
	0x66, 0xfb, 0x23, 0x3b, 0x9d, 0xe4, 0xfb, 0x9d, 0x27, 0x72, 0x0d, 0xd4, 0xd6, 0xa3, 0x5e, 0xa3,
	0x6b, 0x36, 0xeb, 0x1e, 0xc7, 0x59, 0xbe, 0x23, 0xe5, 0xd0, 0x77, 0x04, 0x63, 0xfd, 0x5f, 0x0a,
	0xcc, 0xed, 0x0c, 0x7b, 0xcd, 0x52, 0x63, 0xd0, 0x42, 0x3c, 0xd6, 0x60, 0xa6, 0xd9, 0x18, 0xb4,
	0x7c, 0x30, 0x12, 0xf8, 0x28, 0xac, 0xe6, 0x0b, 0xdc, 0x6a, 0x01, 0x47, 0x12, 0x09, 0xae, 0xd5,
	0x7c, 0x91, 0x1f, 0x8e, 0x38, 0x08, 0xbe, 0x58, 0xc5, 0x03, 0x72, 0x17, 0xf9, 0x29, 0x4d, 0xf9,
	0x8b, 0xdb, 0x78, 0x52, 0xeb, 0xc0, 0x7f, 0xd7, 0x87, 0x83, 0x0e, 0x07, 0x64, 0xd6, 0xe0, 0xf2,
	0x8f, 0x07, 0x1d, 0x44, 0x4b, 0x48, 0x34, 0xbb, 0x94, 0x39, 0x80, 0x70, 0x4e, 0xa8, 0x38, 0xc3,
	0xe5, 0x07, 0x8d, 0x8e, 0x29, 0xd6, 0x39, 0x2c, 0x0b, 0xc6, 0x2c, 0xa7, 0x84, 0x10, 0x13, 0xd0,
	0x38, 0x2e, 0xf0, 0xe3, 0x18, 0x90, 0xbd, 0xbe, 0x6d, 0xd3, 0xc1, 0xa3, 0x9d, 0x41, 0xa3, 0xdd,
	0xa5, 0x3d, 0x1b, 0xcd, 0xde, 0x82, 0xb9, 0x33, 0xe7, 0xd1, 0x37, 0x1d, 0x5c, 0x52, 0xa5, 0x45,
	0x9e, 0x87, 0x05, 0x6f, 0x83, 0x04, 0xc1, 0xbc, 0x4b, 0xe4, 0x30, 0xc8, 0x9b, 0x24, 0x28, 0xbc,
	0x4d, 0x1c, 0x0e, 0x79, 0x93, 0x04, 0x89, 0xb7, 0x89, 0xc3, 0xf2, 0x1c, 0x78, 0xcf, 0x12, 0x34,
	0x9e, 0x8e, 0x08, 0x8f, 0x6f, 0x60, 0x42, 0x32, 0x90, 0x5c, 0x85, 0x94, 0xf7, 0xa6, 0x35, 0x30,
	0x9b, 0x2e, 0x34, 0x9e, 0xd0, 0x43, 0x24, 0xea, 0x7f, 0x57, 0x40, 0x3d, 0x66, 0x74, 0x70, 0xcb,
	0x89, 0x51, 0x74, 0xf8, 0xa0, 0x67, 0x2b, 0x21, 0xcf, 0xd6, 0x60, 0x7e, 0xc8, 0xe8, 0xc0, 0x8b,
	0x15, 0x01, 0x00, 0x20, 0xcd, 0x89, 0x96, 0xa0, 0x7b, 0xc7, 0x27, 0xb9, 0xf7, 0x54, 0xd0, 0xbd,
	0x03, 0x51, 0x33, 0x1d, 0x8a, 0x1a, 0xdf, 0xc9, 0x13, 0x01, 0x27, 0xdf, 0x82, 0xb9, 0xfe, 0xa9,
	0xdd, 0x70, 0x59, 0x0a, 0x33, 0x41, 0x90, 0xb8, 0x6f, 0x7f, 0xa5, 0xc0, 0xa2, 0x6c, 0xe3, 0x5e,
	0xbf, 0x3d, 0xd9, 0xc4, 0xa0, 0x01, 0xb1, 0xb0, 0x01, 0x01, 0x2d, 0xe3, 0x21, 0x2d, 0xd7, 0x21,
	0xd9, 0xe9, 0xb7, 0x05, 0x5f, 0xc7, 0xba, 0x4e, 0xbf, 0xcd, 0xd9, 0xba, 0x4b, 0xa8, 0xe5, 0xb4,
	0xbf, 0x84, 0x2a, 0xe6, 0x40, 0x2d, 0xb6, 0x5a, 0x7e, 0xda, 0x33, 0xe8, 0x7d, 0xf2, 0x02, 0xc4,
	0x9b, 0x67, 0x6d, 0xae, 0xdc, 0xdc, 0xab, 0x2b, 0x37, 0xbc, 0xac, 0x2a, 0xed, 0xc2, 0x0d, 0xfa,
	0x5b, 0xb0, 0x14, 0x7a, 0x97, 0x59, 0xe7, 0x7e, 0xf9, 0x45, 0x50, 0xb7, 0x69, 0x27, 0x28, 0x38,
	0x2a, 0xe5, 0xea, 0xcb, 0xb0, 0x14, 0xda, 0xc8, 0x2c, 0x9d, 0x80, 0xba, 0x4b, 0xed, 0xc0, 0xdb,
	0xfa, 0x36, 0x2c, 0x85, 0x68, 0xcc, 0x22, 0x37, 0x21, 0xd9, 0x3c, 0x6b, 0xd7, 0x3b, 0x26, 0xb3,
	0xd3, 0x8a, 0x16, 0x1f, 0xab, 0xd3, 0x4c, 0xf3, 0xac, 0xbd, 0x67, 0x32, 0x5b, 0xbf, 0x03, 0x2b,
	0xbe, 0x51, 0x4e, 0x92, 0x46, 0xdd, 0x5e, 0x83, 0xa4, 0x38, 0x1a, 0xcf, 0xb8, 0xf4, 0x08, 0x23,
	0x77, 0xfb, 0x8c, 0x29, 0x7e, 0xe8, 0x7b, 0xb0, 0x1a, 0xc1, 0x8c, 0x59, 0x5f, 0x8f, 0xdb, 0x1d,
	0x58, 0xd9, 0xef, 0x7f, 0x53, 0xaa, 0xad, 0xc1, 0x6a, 0x04, 0x33, 0x66, 0xe9, 0x15, 0x58, 0xf1,
	0xf1, 0x96, 0xa4, 0x44, 0xde, 0x87, 0xc1, 0xdb, 0x2b, 0x16, 0xbc, 0xbd, 0x50, 0x46, 0x04, 0x2b,
	0x66, 0xe9, 0x2f, 0xc3, 0x8a, 0x7f, 0x54, 0xcf, 0x90, 0xa1, 0x1f, 0xc3, 0x6a, 0xc4, 0x66, 0x66,
	0x91, 0x3c, 0x2c, 0xb8, 0x76, 0xcb, 0x07, 0x3c, 0xde, 0xf8, 0x39, 0xc7, 0x78, 0x7e, 0xd0, 0xbf,
	0x8c, 0x01, 0xd9, 0x35, 0x1f, 0x50, 0x0c, 0x50, 0x67, 0x1f, 0xaa, 0xa0, 0x42, 0x7c, 0xe8, 0x29,
	0x80, 0x3f, 0xa3, 0xef, 0x60, 0x15, 0xe2, 0xbd, 0x61, 0xd7, 0x09, 0x42, 0xfc, 0x89, 0x59, 0x42,
	0xc4, 0xa2, 0x13, 0x7d, 0xce, 0x13, 0x06, 0x5f, 0x7f, 0xd0, 0xc2, 0xbc, 0xd5, 0x72, 0x2f, 0x18,
	0xfe, 0x5c, 0x69, 0x91, 0x17, 0x61, 0x91, 0x3e, 0xb4, 0xcc, 0x01, 0xad, 0xb7, 0x86, 0x83, 0x86,
	0x6d, 0xf6, 0x7b, 0x4e, 0x86, 0x49, 0x09, 0xf2, 0xb6, 0x43, 0x25, 0x3a, 0x2c, 0x38, 0x81, 0xdf,
	0xb0, 0x2c, 0x64, 0x34, 0x23, 0xd2, 0xb1, 0x20, 0x16, 0x2d, 0xab, 0xd2, 0xc2, 0x6c, 0x64, 0xf7,
	0xed, 0x46, 0xc7, 0x49, 0xba, 0xe2, 0xd2, 0x01, 0x4e, 0xe2, 0x19, 0x17, 0x53, 0x7a, 0x7f, 0x68,
	0x33, 0xb3, 0x45, 0xe5, 0xfb, 0x78, 0xce, 0xa1, 0xf1, 0x6c, 0xb0, 0x0a, 0xcb, 0x23, 0x90, 0x30,
	0x4b, 0xff, 0x51, 0x0c, 0xc8, 0x31, 0xa3, 0x72, 0xaa, 0x8e, 0x86, 0x2a, 0x90, 0xdc, 0x62, 0xcf,
	0xc8, 0xdf, 0xf1, 0x91, 0xfc, 0x3d, 0xb1, 0x76, 0x99, 0x80, 0xe3, 0x06, 0xcc, 0x0e, 0x19, 0x75,
	0xb2, 0xa6, 0x40, 0x30, 0x39, 0x64, 0x54, 0x24, 0xcd, 0xb0, 0xd9, 0x33, 0x23, 0x66, 0x23, 0xbc,
	0x2e, 0x6b, 0xe1, 0x48, 0x49, 0x2d, 0x8e, 0xf0, 0x3a, 0xfc, 0xb9, 0xbb, 0x64, 0x61, 0x79, 0x04,
	0x02, 0x66, 0xe1, 0xa9, 0x0f, 0x68, 0xb7, 0x61, 0xf6, 0xdc, 0x6a, 0x45, 0x3c, 0xe9, 0x7f, 0x54,
	0x20, 0x73, 0x38, 0xe8, 0x37, 0xe5, 0x17, 0x50, 0x56, 0x7f, 0x68, 0x23, 0x74, 0x61, 0x2c, 0x94,
	0x11, 0x2c, 0x1c, 0x70, 0x63, 0x63, 0xc0, 0xfd, 0x76, 0xca, 0x3e, 0xfd, 0x32, 0x6c, 0x8c, 0xb5,
	0x82, 0x59, 0xfa, 0x0b, 0x40, 0x76, 0xa9, 0x2d, 0x5f, 0x71, 0x91, 0x7e, 0xa1, 0x7f, 0x0a, 0xcb,
	0x23, 0xfb, 0x98, 0x45, 0xbe, 0x0b, 0x29, 0x1f, 0x05, 0x29, 0x82, 0x33, 0x7e, 0x04, 0x87, 0x4b,
	0x04, 0x63, 0xde, 0xc5, 0x08, 0x4f, 0x85, 0x5c, 0x81, 0x54, 0xa7, 0xc1, 0xec, 0xba, 0x7b, 0x0f,
	0x33, 0x0e, 0x58, 0xdc, 0x98, 0x47, 0xea, 0x81, 0xb8, 0x89, 0x99, 0xfe, 0x12, 0xcf, 0x20, 0xc8,
	0xca, 0xad, 0x34, 0x8f, 0x59, 0x74, 0xb0, 0xeb, 0xc7, 0x70, 0x31, 0x6a, 0x2b, 0xb3, 0xc8, 0x5b,
	0x63, 0x94, 0x5d, 0xf5, 0x95, 0x95, 0xea, 0xd8, 0xa0, 0x9e, 0x7a, 0x99, 0x5f, 0x95, 0xf2, 0x3a,
	0xbd, 0x4f, 0xfe, 0xdf, 0x29, 0x3d, 0xfd, 0xbc, 0x3d, 0x86, 0x17, 0xaf, 0x48, 0x31, 0x69, 0xaf,
	0x00, 0x09, 0xb3, 0x61, 0x96, 0xfe, 0x7f, 0xfc, 0x86, 0x0c, 0x31, 0x1f, 0x57, 0x47, 0x23, 0x8f,
	0xf0, 0x6e, 0x66, 0xe9, 0x6f, 0xf0, 0xcb, 0x33, 0xc4, 0x43, 0x83, 0x79, 0x87, 0x87, 0x6f, 0xf0,
	0x82, 0x01, 0x82, 0x11, 0xb7, 0xeb, 0x80, 0x3b, 0x40, 0x88, 0x19, 0x79, 0x13, 0x16, 0x5c, 0xc3,
	0xce, 0x81, 0xd4, 0x9c, 0x63, 0x1d, 0x67, 0x78, 0x1b, 0xd6, 0x4a, 0x1f, 0xd1, 0xe6, 0xbd, 0xf3,
	0x1c, 0xd6, 0xc4, 0x96, 0x40, 0x7f, 0x05, 0xd2, 0xd1, 0x9c, 0x98, 0xe5, 0x94, 0xae, 0x43, 0x26,
	0x2a, 0x30, 0xde, 0xcd, 0x1c, 0x33, 0xaa, 0xd7, 0x61, 0xf5, 0xe8, 0x7c, 0x7e, 0x32, 0xb9, 0x1b,
	0x91, 0xb0, 0x8f, 0x07, 0xb0, 0x4f, 0xc3, 0xc5, 0xa3, 0x48, 0xef, 0xd2, 0x7f, 0xaf, 0x78, 0x3e,
	0x2a, 0x55, 0x8b, 0xd1, 0xb2, 0x2f, 0x03, 0x9c, 0xd2, 0xb6, 0x1b, 0xb1, 0x4e, 0x95, 0xc8, 0x29,
	0x3c, 0x9b, 0xad, 0x43, 0x92, 0xf6, 0x9c, 0xae, 0x44, 0x88, 0x9f, 0xa1, 0xbd, 0x96, 0x5b, 0xe6,
	0xfa, 0x29, 0x64, 0x6a, 0x52, 0x0a, 0x99, 0x9e, 0x50, 0x5d, 0x26, 0x02, 0xd5, 0xa5, 0x5e, 0xf5,
	0x42, 0x26, 0xa0, 0x39, 0xb3, 0xc8, 0xeb, 0xe2, 0x25, 0xc9, 0x05, 0xd6, 0xa3, 0x23, 0x1b, 0x5f,
	0x40, 0x7e, 0xdc, 0x05, 0x76, 0x61, 0xa1, 0xd8, 0x6a, 0x49, 0x55, 0xc1, 0xc4, 0x92, 0x79, 0x5d,
	0x2a, 0x7e, 0x10, 0x8a, 0x79, 0xbf, 0xc4, 0xb9, 0x0d, 0x29, 0x99, 0x11, 0xb3, 0xbe, 0x36, 0xa7,
	0x5d, 0x58, 0xd8, 0xa6, 0x9d, 0x6f, 0x40, 0x25, 0x15, 0x52, 0x32, 0x23, 0x66, 0xe9, 0x36, 0x2c,
	0xec, 0x52, 0xfb, 0xbc, 0xac, 0xb3, 0xb0, 0xcc, 0x17, 0xbd, 0x83, 0x12, 0xe0, 0xc6, 0x78, 0x60,
	0xaa, 0xb8, 0x74, 0xe4, 0x9c, 0x18, 0x4f, 0x8f, 0x6b, 0x30, 0xd3, 0xa6, 0x76, 0xbd, 0xd1, 0xe9,
	0x70, 0x4f, 0x48, 0x1a, 0x89, 0x36, 0xb5, 0x8b, 0x9d, 0x8e, 0xfe, 0x3a, 0xa4, 0x64, 0xa9, 0xcc,
	0xc2, 0x3b, 0x70, 0xb4, 0x98, 0x9a, 0x0f, 0x96, 0x4c, 0x3f, 0x51, 0x60, 0xee, 0x98, 0xf1, 0x72,
	0xaa, 0xd2, 0x3b, 0xeb, 0xff, 0xb7, 0xed, 0xda, 0xc4, 0x76, 0x26, 0x70, 0xa7, 0x4f, 0x05, 0xef,
	0x74, 0xfd, 0xe7, 0x0a, 0x2c, 0xd6, 0x06, 0x8d, 0x1e, 0x6b, 0x34, 0xb1, 0x3e, 0xe2, 0xca, 0xf0,
	0x0e, 0x9a, 0xd2, 0x4f, 0xa8, 0xac, 0x0e, 0x08, 0x92, 0xeb, 0xdf, 0x7d, 0x8b, 0x0e, 0xe4, 0xa8,
	0x49, 0x22, 0xc1, 0x0d, 0x1a, 0xaf, 0xba, 0x88, 0x07, 0xab, 0x8b, 0x2d, 0x98, 0x73, 0xaa, 0x34,
	0xa9, 0x73, 0x04, 0x41, 0xe2, 0x97, 0xe4, 0x63, 0x05, 0x16, 0x76, 0x06, 0x94, 0xbe, 0x4b, 0xdd,
	0xca, 0x68, 0x1b, 0x54, 0x9b, 0x6b, 0xc7, 0xcb, 0xb7, 0xba, 0xd9, 0x3b, 0xeb, 0x3b, 0x19, 0x5e,
	0x0a, 0x80, 0x90, 0x01, 0xc6, 0xa2, 0xf4, 0x0a, 0xb7, 0xe8, 0x2d, 0x48, 0x55, 0x38, 0x78, 0xbd,
	0xb3, 0xbe, 0x7f, 0xce, 0x81, 0x3c, 0x2a, 0x9d, 0x86, 0x31, 0xef, 0xfe, 0xe2, 0x47, 0xef, 0xa4,
	0x8d, 0xb8, 0x7f, 0xb5, 0xa9, 0x90, 0x92, 0xb5, 0x64, 0x96, 0x4e, 0x21, 0x25, 0x95, 0x7a, 0x47,
	0xc3, 0x67, 0x34, 0xe0, 0x6b, 0x30, 0x13, 0x3c, 0xcc, 0x84, 0x79, 0x9e, 0xbe, 0x5b, 0xff, 0x1e,
	0xaf, 0xf6, 0x83, 0x92, 0xbe, 0x46, 0xfd, 0x28, 0x89, 0x8f, 0xcb, 0xe2, 0xb1, 0xcb, 0x8a, 0xe0,
	0x2f, 0x75, 0x59, 0x6c, 0xd8, 0x1d, 0x6d, 0x8c, 0x42, 0xfb, 0x39, 0xef, 0xa3, 0x61, 0x57, 0x1f,
	0xc0, 0xc6, 0x2e, 0xb5, 0x0f, 0xf0, 0xf0, 0xb9, 0xfa, 0xb7, 0x1e, 0xe1, 0x21, 0x1b, 0x8d, 0x9e,
	0xe8, 0x0f, 0x82, 0xc9, 0x57, 0x99, 0x94, 0x7c, 0x63, 0xc1, 0xe4, 0x2b, 0xa7, 0xd0, 0x78, 0x30,
	0x85, 0xbe, 0x0e, 0x97, 0xc6, 0xcb, 0x64, 0x16, 0x59, 0x81, 0x69, 0x81, 0xad, 0x90, 0x27, 0x1e,
	0xf4, 0xfb, 0x90, 0x71, 0xdf, 0xc2, 0x23, 0xff, 0x36, 0x14, 0xcd, 0xfb, 0xe0, 0x8c, 0x88, 0x64,
	0x16, 0xca, 0x14, 0x51, 0xe4, 0x65, 0x90, 0x59, 0x63, 0xb6, 0xef, 0xee, 0xd6, 0xff, 0x10, 0x83,
	0xa5, 0x52, 0xbf, 0xf7, 0x80, 0x0e, 0x18, 0xfa, 0xf8, 0xd8, 0x36, 0x22, 0x5c, 0xb2, 0xc7, 0x46,
	0x4b, 0x76, 0xaf, 0x29, 0x8b, 0x4b, 0x4d, 0xd9, 0x2a, 0x24, 0x4e, 0xdb, 0x75, 0xec, 0xcb, 0x44,
	0x90, 0x4e, 0x9f, 0xb6, 0xab, 0x81, 0xce, 0x6c, 0x3a, 0xd0, 0x99, 0x5d, 0x85, 0x54, 0xb7, 0x61,
	0xd3, 0x81, 0xe9, 0x35, 0x4d, 0xe2, 0x66, 0x5b, 0x70, 0xa9, 0xa2, 0x6f, 0x7a, 0x09, 0xd4, 0xa6,
	0xa7, 0x74, 0x60, 0xa4, 0xb5, 0xe8, 0xd3, 0xc5, 0x56, 0x39, 0x8b, 0x24, 0x83, 0x59, 0xa4, 0x04,
	0xc4, 0x13, 0xe6, 0x97, 0x90, 0xb3, 0x93, 0x02, 0x5a, 0x75, 0x5f, 0xf0, 0xca, 0xc8, 0x15, 0x20,
	0x61, 0xfc, 0x98, 0x75, 0xfd, 0xb1, 0x02, 0x33, 0x7b, 0xce, 0xa8, 0x67, 0x05, 0xd4, 0xbd, 0x83,
	0xdd, 0x7a, 0xed, 0xe4, 0xb0, 0x5c, 0xaf, 0x54, 0xef, 0x16, 0xf7, 0x2a, 0xdb, 0xea, 0x05, 0xa2,
	0xc2, 0xbc, 0x47, 0x3d, 0x3e, 0x2a, 0xab, 0x0a, 0x59, 0x86, 0x45, 0x8f, 0x52, 0x7e, 0xe7, 0xb0,
	0x62, 0x94, 0xd5, 0x18, 0xd9, 0x84, 0x8c, 0x47, 0xdc, 0x31, 0x8a, 0xbb, 0xfb, 0xe5, 0x6a, 0xad,
	0x5e, 0x7e, 0xa7, 0x74, 0xbb, 0x58, 0xdd, 0x2d, 0xab, 0xf1, 0xe8, 0x75, 0xe3, 0x60, 0x6f, 0xef,
	0x56, 0xb1, 0x74, 0x47, 0x9d, 0x22, 0x97, 0x20, 0xed, 0x0b, 0xaf, 0x95, 0xf7, 0xeb, 0xa5, 0x83,
	0xea, 0xdd, 0xb2, 0x71, 0x54, 0x39, 0xa8, 0xaa, 0xd3, 0xd7, 0xbf, 0x54, 0x60, 0x51, 0x6a, 0xc8,
	0x5d, 0x75, 0x8f, 0xab, 0x77, 0xaa, 0x07, 0x6f, 0x8b, 0xfd, 0xf8, 0xa6, 0x7a, 0x01, 0xa9, 0xc8,
	0xf1, 0xb0, 0x58, 0xba, 0x53, 0x3f, 0x34, 0xca, 0x47, 0xe5, 0x6a, 0x4d, 0x55, 0x48, 0x06, 0x2e,
	0x7a, 0xd4, 0x52, 0xd1, 0xd8, 0xae, 0x1b, 0x95, 0xd2, 0x6d, 0x54, 0x5e, 0x8d, 0x91, 0x0d, 0x58,
	0x0b, 0xae, 0x95, 0x6e, 0x17, 0x8d, 0x7d, 0xbe, 0x18, 0x27, 0x97, 0x61, 0xdd, 0x5b, 0xdc, 0x3b,
	0xa8, 0xd5, 0xca, 0xc6, 0x89, 0xa7, 0xbe, 0x3a, 0x75, 0xfd, 0x57, 0x71, 0x58, 0x72, 0x13, 0x01,
	0x77, 0x0c, 0xe7, 0x12, 0x5f, 0x75, 0x34, 0xc3, 0xf7, 0x8a, 0xbb, 0xe5, 0xfa, 0xd1, 0xc1, 0xb1,
	0x51, 0x42, 0xf5, 0x9e, 0x87, 0xad, 0x20, 0xad, 0x5e, 0x2c, 0xd5, 0x2a, 0x77, 0x2b, 0xb5, 0x13,
	0x49, 0x5b, 0x0d, 0x2e, 0x85, 0x36, 0x6d, 0x17, 0x2b, 0x7b, 0x27, 0xf5, 0xd2, 0xed, 0x72, 0xe9,
	0x4e, 0xa5, 0xaa, 0xc6, 0xc8, 0x73, 0x70, 0x39, 0xb4, 0x63, 0xa7, 0x62, 0x1c, 0xd5, 0xea, 0x46,
	0x19, 0x95, 0xe7, 0x80, 0x6f, 0xc0, 0x5a, 0x68, 0xcb, 0xc1, 0xce, 0x4e, 0xa5, 0x54, 0x29, 0xee,
	0xa9, 0x53, 0x11, 0x8b, 0x47, 0xfb, 0xc5, 0xa3, 0xdb, 0xe5, 0xdd, 0x5d, 0x75, 0x1a, 0x6d, 0x0e,
	0x2d, 0x4a, 0x67, 0x91, 0x20, 0x5b, 0xb0, 0x11, 0x36, 0xe1, 0x6d, 0x0e, 0x5b, 0xb9, 0x5a, 0x2b,
	0x1b, 0xea, 0x0c, 0x79, 0x19, 0x5e, 0x8c, 0xdc, 0x70, 0x72, 0x7c, 0x52, 0xa9, 0xd6, 0xf7, 0x2a,
	0x77, 0xcb, 0xf5, 0xfd, 0xca, 0x11, 0xe7, 0x96, 0x24, 0x37, 0xe1, 0xe5, 0xd0, 0xe6, 0x0a, 0xb2,
	0x41, 0x54, 0x0e, 0xaa, 0xf8, 0xbb, 0xb2, 0x5f, 0x2c, 0x9d, 0x78, 0x2f, 0xcc, 0x12, 0x1d, 0x36,
	0xc3, 0x2f, 0x84, 0xdc, 0x05, 0xae, 0x77, 0x00, 0x76, 0x8c, 0x72, 0xf9, 0xdd, 0x32, 0x3a, 0x05,
	0xb9, 0x08, 0xc4, 0x7f, 0xaa, 0x1f, 0x7b, 0x9e, 0x1d, 0xa4, 0x1f, 0x1a, 0xe5, 0xc3, 0xa2, 0x81,
	0xfe, 0xbd, 0x0a, 0x4b, 0x12, 0xbd, 0x74, 0xb0, 0xbf, 0x5f, 0xa9, 0xa9, 0x31, 0xb2, 0x06, 0xcb,
	0x12, 0xd9, 0x73, 0xdd, 0xf8, 0xab, 0xbf, 0xdb, 0x80, 0xa4, 0x5b, 0x8d, 0x12, 0x8b, 0x57, 0xa0,
	0xd2, 0xb7, 0x20, 0xa9, 0x21, 0x0d, 0x4f, 0x4b, 0x33, 0x1b, 0x63, 0xd7, 0xb0, 0x41, 0xfe, 0xe1,
	0xe3, 0xa7, 0x71, 0xe5, 0x67, 0x8f, 0x9f, 0xc6, 0xa7, 0x7a, 0xb9, 0x56, 0xee, 0x8b, 0xc7, 0x4f,
	0xe3, 0xcb, 0xd9, 0x9e, 0x96, 0xef, 0x35, 0xba, 0xb4, 0xa0, 0x65, 0x5b, 0x5a, 0xbe, 0x45, 0x59,
	0xb3, 0x40, 0x4e, 0x78, 0x15, 0x18, 0x2d, 0x31, 0x3c, 0xe8, 0x94, 0x25, 0x8e, 0x0c, 0x3c, 0xf5,
	0x45, 0x94, 0x18, 0x43, 0x89, 0x17, 0x50, 0xda, 0x05, 0x42, 0x79, 0xed, 0x1a, 0xcd, 0x3a, 0x3c,
	0x81, 0x95, 0x59, 0x8f, 0x0e, 0x5d, 0x37, 0x90, 0x75, 0x1c, 0x59, 0xc7, 0x1e, 0x72, 0x53, 0x20,
	0xfb, 0x50, 0xcb, 0xf3, 0x34, 0x5c, 0x20, 0x7f, 0x51, 0xe4, 0x69, 0xb0, 0xfb, 0x75, 0x6b, 0x33,
	0x0a, 0x1c, 0xbf, 0xd8, 0xcd, 0x6c, 0x4d, 0x5c, 0x67, 0x96, 0xfe, 0x29, 0xca, 0x9c, 0x42, 0x99,
	0xa9, 0x87, 0x39, 0x3b, 0xc7, 0x72, 0xbd, 0x5c, 0x37, 0xf7, 0x71, 0xee, 0x11, 0x97, 0xff, 0xa1,
	0x2f, 0x5f, 0xcb, 0xda, 0x5a, 0x1e, 0x6f, 0xac, 0x82, 0x96, 0x65, 0x5a, 0xde, 0xab, 0x2b, 0x0b,
	0x1a, 0xc2, 0xcd, 0xef, 0xcb, 0x82, 0x96, 0xed, 0x6a, 0x79, 0x77, 0xd2, 0x51, 0xd0, 0xb2, 0x1f,
	0x6b, 0x79, 0x31, 0xc9, 0x2f, 0x68, 0xef, 0x65, 0x1f, 0x69, 0xf9, 0xf0, 0x17, 0xab, 0xc2, 0x07,
	0xe4, 0xbe, 0x3c, 0x53, 0x8e, 0x30, 0x2a, 0x6a, 0x8a, 0x29, 0x1b, 0x15, 0x39, 0xb8, 0x14, 0x40,
	0x4e, 0x8f, 0x01, 0xf2, 0x9f, 0x0a, 0x2c, 0x8d, 0x4c, 0x66, 0x65, 0x99, 0x51, 0x33, 0x60, 0x59,
	0x66, 0xf4, 0x58, 0xf7, 0x17, 0x0a, 0x0a, 0x4d, 0xa0, 0x50, 0xb5, 0x91, 0x1b, 0xc5, 0xd2, 0xca,
	0x36, 0x84, 0x0a, 0xa2, 0xc4, 0x2a, 0x68, 0xff, 0x73, 0x6c, 0x7f, 0x20, 0x0f, 0xf6, 0x23, 0xec,
	0x8c, 0x9a, 0x42, 0xcb, 0x76, 0x46, 0x8f, 0x96, 0x5f, 0x42, 0x33, 0x67, 0x78, 0xc4, 0x3d, 0xcc,
	0x31, 0x6e, 0xda, 0x45, 0xd9, 0x14, 0x96, 0x77, 0x2d, 0x24, 0x5f, 0x28, 0xbc, 0x41, 0x94, 0xbf,
	0x3e, 0x06, 0x83, 0x39, 0x38, 0x0e, 0xc9, 0x5c, 0x1a, 0xbf, 0xc8, 0x2c, 0xbd, 0x8c, 0x82, 0x93,
	0x28, 0x38, 0xf9, 0x30, 0xd7, 0xe3, 0xf8, 0xa2, 0xf0, 0x1b, 0x28, 0xdc, 0x01, 0xcf, 0x0f, 0x7c,
	0x04, 0xd4, 0xec, 0x52, 0x26, 0x10, 0xf5, 0x3f, 0x23, 0x16, 0xc8, 0xf7, 0x79, 0x87, 0x38, 0x46,
	0xa7, 0x91, 0x31, 0x8f, 0xac, 0x53, 0xc4, 0x54, 0xe7, 0x32, 0xea, 0x34, 0x2b, 0x39, 0xda, 0x3c,
	0x6a, 0xe3, 0xcc, 0x26, 0x0a, 0xe4, 0x7d, 0xde, 0x05, 0x8e, 0x91, 0x35, 0x32, 0x0e, 0x92, 0x65,
	0x8d, 0x0e, 0x7d, 0x44, 0xe2, 0x01, 0x29, 0xf1, 0x7c, 0xa5, 0xc0, 0x62, 0x68, 0x9a, 0x4c, 0x64,
	0x16, 0x23, 0xb3, 0xf7, 0xcc, 0xe5, 0x09, 0xab, 0xcc, 0xd2, 0x0f, 0x51, 0xc2, 0x1c, 0x4a, 0x98,
	0x1f, 0xe6, 0x4e, 0x73, 0xbd, 0x1c, 0xcb, 0xd9, 0x39, 0x61, 0xd7, 0x1b, 0xd9, 0xa1, 0x96, 0x1f,
	0xf2, 0x03, 0x3e, 0xf5, 0xcf, 0x1a, 0xf1, 0x1e, 0x76, 0x65, 0xaf, 0x45, 0xb7, 0xce, 0xbb, 0x25,
	0x5a, 0x81, 0xfc, 0x56, 0x7c, 0xa0, 0x0b, 0x7c, 0x83, 0xbc, 0x14, 0x28, 0xc6, 0x42, 0x33, 0x6f,
	0x59, 0xc5, 0x88, 0x71, 0xb0, 0x7e, 0x80, 0x2a, 0xce, 0x73, 0x27, 0x18, 0xe6, 0xec, 0x9c, 0xe9,
	0x38, 0xc1, 0x77, 0x7c, 0xf5, 0x6c, 0x2d, 0xef, 0xb5, 0x38, 0x05, 0x2d, 0x6b, 0x6a, 0x79, 0xb9,
	0x4b, 0x0e, 0x47, 0x18, 0x31, 0x61, 0x31, 0x34, 0x5c, 0x21, 0xc1, 0x63, 0x08, 0x0d, 0x5f, 0x03,
	0x18, 0x8e, 0x8e, 0x5c, 0xf5, 0x75, 0x54, 0x70, 0x81, 0x7b, 0xc4, 0x90, 0xab, 0x96, 0x74, 0x55,
	0x23, 0x3f, 0x55, 0x60, 0x25, 0x6a, 0x62, 0x46, 0x9e, 0xf3, 0x59, 0x8e, 0x99, 0xcd, 0x65, 0xf4,
	0x67, 0x6d, 0x61, 0x96, 0x7e, 0x1d, 0x45, 0xa7, 0x78, 0x64, 0x22, 0x36, 0x28, 0x7c, 0x2d, 0x80,
	0x8b, 0x37, 0x50, 0xe3, 0xa1, 0x49, 0x46, 0x27, 0x65, 0x44, 0x8a, 0xfe, 0xc8, 0x41, 0x5d, 0x46,
	0x9b, 0xbc, 0x81, 0x59, 0xfa, 0x9b, 0xa8, 0xc5, 0x22, 0x6a, 0x91, 0x40, 0x2d, 0xc4, 0x9d, 0x7c,
	0x65, 0x8c, 0x1e, 0xfc, 0x86, 0xf6, 0xc2, 0x65, 0xe0, 0x4d, 0xbb, 0xc7, 0xe8, 0xb4, 0xfb, 0x2c,
	0x9d, 0xa2, 0x47, 0xcb, 0xe2, 0x50, 0xd4, 0xc8, 0x43, 0xf9, 0xab, 0x02, 0x6b, 0x63, 0x26, 0xf0,
	0xe4, 0x8a, 0xf4, 0xa1, 0x6b, 0xec, 0xa7, 0x86, 0xcc, 0xd5, 0x73, 0xec, 0x62, 0x96, 0x7e, 0x1f,
	0x75, 0x58, 0xe2, 0xc1, 0x65, 0xe7, 0x86, 0xdc, 0x73, 0xf1, 0x7a, 0x40, 0x6d, 0xee, 0x22, 0x28,
	0x21, 0x2f, 0xf5, 0xf0, 0x32, 0x83, 0xfe, 0x3c, 0x7a, 0x3f, 0xf8, 0x93, 0x81, 0xd0, 0x25, 0x41,
	0x9e, 0x28, 0x23, 0x9f, 0x0f, 0xf6, 0xfa, 0xed, 0x08, 0x40, 0x83, 0x13, 0xd1, 0x08, 0x40, 0x43,
	0x83, 0x47, 0xfd, 0x33, 0x7e, 0xd9, 0x11, 0x27, 0x55, 0x88, 0x38, 0x3c, 0xcd, 0x51, 0x6e, 0x4d,
	0xd7, 0xd3, 0xfd, 0x3d, 0xb4, 0xcb, 0xed, 0x74, 0xb5, 0x57, 0x6e, 0x0c, 0x19, 0xd5, 0x5e, 0xbd,
	0x21, 0x06, 0x3b, 0x21, 0xcb, 0xb4, 0x57, 0x6e, 0x58, 0x03, 0xca, 0x28, 0x37, 0x22, 0x64, 0x23,
	0x26, 0x1c, 0xaf, 0xd1, 0x2e, 0x68, 0x59, 0xaa, 0xe5, 0xdd, 0xce, 0xba, 0xf0, 0x01, 0x39, 0x04,
	0xf0, 0x87, 0x90, 0x64, 0x2d, 0x70, 0x85, 0x48, 0xf7, 0x5a, 0x3a, 0x7a, 0xc1, 0xcd, 0xab, 0xcb,
	0x52, 0x5e, 0x3d, 0x04, 0xf0, 0x67, 0x88, 0x32, 0xc7, 0xc0, 0x88, 0x52, 0xe6, 0x18, 0x1a, 0x39,
	0x72, 0x8e, 0x2b, 0x12, 0xc7, 0x0f, 0x01, 0xfc, 0x69, 0xa0, 0xcc, 0x31, 0x30, 0x99, 0x94, 0x39,
	0x06, 0x87, 0x87, 0xfa, 0x16, 0x72, 0x5c, 0xe5, 0x0e, 0x2c, 0x02, 0x3b, 0x95, 0xb5, 0x73, 0x92,
	0x5f, 0xa0, 0xce, 0xfe, 0xec, 0x49, 0x96, 0x10, 0x98, 0x9b, 0xc9, 0x12, 0x42, 0xa3, 0x2a, 0xae,
	0xf3, 0xc5, 0x40, 0x59, 0xbb, 0x34, 0x32, 0xf4, 0x09, 0x55, 0x66, 0x23, 0x13, 0xa7, 0xcc, 0xd6,
	0xc4, 0x75, 0x57, 0xcc, 0x9a, 0x24, 0xe6, 0x37, 0x0a, 0xa4, 0xc7, 0x8d, 0x66, 0xc8, 0xd5, 0x00,
	0xbb, 0x71, 0x23, 0xa3, 0xcc, 0x0b, 0xe7, 0xd9, 0xc6, 0x2c, 0xfd, 0x26, 0x0a, 0x4f, 0xf3, 0x04,
	0xe9, 0x3a, 0xeb, 0xa5, 0x51, 0xef, 0xf2, 0x9d, 0x8b, 0xfc, 0x5a, 0x81, 0xb5, 0x31, 0xe3, 0x18,
	0x39, 0x39, 0x8c, 0x1f, 0x12, 0x65, 0xae, 0x9e, 0x63, 0x97, 0xab, 0xd9, 0xfa, 0x7f, 0xa0, 0xd9,
	0xfb, 0x90, 0x0a, 0x0e, 0x2a, 0xe4, 0xca, 0x62, 0x64, 0x04, 0x24, 0x57, 0x16, 0xa3, 0xf3, 0x0d,
	0x71, 0x28, 0x19, 0xff, 0x50, 0x32, 0x89, 0xcf, 0x1e, 0x3f, 0x8d, 0x7f, 0xf9, 0xc9, 0x2d, 0xf5,
	0x4f, 0x4f, 0x36, 0x95, 0x3f, 0x3f, 0xd9, 0x54, 0xfe, 0xf6, 0x64, 0x53, 0xf9, 0xfc, 0x1f, 0x9b,
	0x17, 0x4e, 0x13, 0xfc, 0x9f, 0x7c, 0xaf, 0xfd, 0x3b, 0x00, 0x00, 0xff, 0xff, 0xd3, 0xf1, 0xaf,
	0x07, 0x1b, 0x28, 0x00, 0x00,
}
