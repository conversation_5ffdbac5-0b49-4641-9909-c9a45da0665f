// Code generated by protoc-gen-go. DO NOT EDIT.
// source: presence/presence_v2.proto

package Presence // import "golang.52tt.com/protocol/services/presencesvr"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type PresKey struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ProxyIp              uint32   `protobuf:"varint,2,opt,name=proxy_ip,json=proxyIp,proto3" json:"proxy_ip,omitempty"`
	ProxyPort            uint32   `protobuf:"varint,3,opt,name=proxy_port,json=proxyPort,proto3" json:"proxy_port,omitempty"`
	ClientId             uint32   `protobuf:"varint,4,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresKey) Reset()         { *m = PresKey{} }
func (m *PresKey) String() string { return proto.CompactTextString(m) }
func (*PresKey) ProtoMessage()    {}
func (*PresKey) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{0}
}
func (m *PresKey) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresKey.Unmarshal(m, b)
}
func (m *PresKey) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresKey.Marshal(b, m, deterministic)
}
func (dst *PresKey) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresKey.Merge(dst, src)
}
func (m *PresKey) XXX_Size() int {
	return xxx_messageInfo_PresKey.Size(m)
}
func (m *PresKey) XXX_DiscardUnknown() {
	xxx_messageInfo_PresKey.DiscardUnknown(m)
}

var xxx_messageInfo_PresKey proto.InternalMessageInfo

func (m *PresKey) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *PresKey) GetProxyIp() uint32 {
	if m != nil {
		return m.ProxyIp
	}
	return 0
}

func (m *PresKey) GetProxyPort() uint32 {
	if m != nil {
		return m.ProxyPort
	}
	return 0
}

func (m *PresKey) GetClientId() uint32 {
	if m != nil {
		return m.ClientId
	}
	return 0
}

type PresVal struct {
	DeviceId             []byte   `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	ClientIp             uint32   `protobuf:"varint,2,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	OnlineTs             uint32   `protobuf:"varint,3,opt,name=online_ts,json=onlineTs,proto3" json:"online_ts,omitempty"`
	Terminal             uint32   `protobuf:"varint,4,opt,name=terminal,proto3" json:"terminal,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresVal) Reset()         { *m = PresVal{} }
func (m *PresVal) String() string { return proto.CompactTextString(m) }
func (*PresVal) ProtoMessage()    {}
func (*PresVal) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{1}
}
func (m *PresVal) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresVal.Unmarshal(m, b)
}
func (m *PresVal) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresVal.Marshal(b, m, deterministic)
}
func (dst *PresVal) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresVal.Merge(dst, src)
}
func (m *PresVal) XXX_Size() int {
	return xxx_messageInfo_PresVal.Size(m)
}
func (m *PresVal) XXX_DiscardUnknown() {
	xxx_messageInfo_PresVal.DiscardUnknown(m)
}

var xxx_messageInfo_PresVal proto.InternalMessageInfo

func (m *PresVal) GetDeviceId() []byte {
	if m != nil {
		return m.DeviceId
	}
	return nil
}

func (m *PresVal) GetClientIp() uint32 {
	if m != nil {
		return m.ClientIp
	}
	return 0
}

func (m *PresVal) GetOnlineTs() uint32 {
	if m != nil {
		return m.OnlineTs
	}
	return 0
}

func (m *PresVal) GetTerminal() uint32 {
	if m != nil {
		return m.Terminal
	}
	return 0
}

type PresInfo struct {
	Key                  *PresKey `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Val                  *PresVal `protobuf:"bytes,2,opt,name=val,proto3" json:"val,omitempty"`
	Offline              bool     `protobuf:"varint,3,opt,name=offline,proto3" json:"offline,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresInfo) Reset()         { *m = PresInfo{} }
func (m *PresInfo) String() string { return proto.CompactTextString(m) }
func (*PresInfo) ProtoMessage()    {}
func (*PresInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{2}
}
func (m *PresInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresInfo.Unmarshal(m, b)
}
func (m *PresInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresInfo.Marshal(b, m, deterministic)
}
func (dst *PresInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresInfo.Merge(dst, src)
}
func (m *PresInfo) XXX_Size() int {
	return xxx_messageInfo_PresInfo.Size(m)
}
func (m *PresInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PresInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PresInfo proto.InternalMessageInfo

func (m *PresInfo) GetKey() *PresKey {
	if m != nil {
		return m.Key
	}
	return nil
}

func (m *PresInfo) GetVal() *PresVal {
	if m != nil {
		return m.Val
	}
	return nil
}

func (m *PresInfo) GetOffline() bool {
	if m != nil {
		return m.Offline
	}
	return false
}

type PresKeyReadable struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ClientId             uint32   `protobuf:"varint,2,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	ProxyAddr            string   `protobuf:"bytes,3,opt,name=proxy_addr,json=proxyAddr,proto3" json:"proxy_addr,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresKeyReadable) Reset()         { *m = PresKeyReadable{} }
func (m *PresKeyReadable) String() string { return proto.CompactTextString(m) }
func (*PresKeyReadable) ProtoMessage()    {}
func (*PresKeyReadable) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{3}
}
func (m *PresKeyReadable) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresKeyReadable.Unmarshal(m, b)
}
func (m *PresKeyReadable) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresKeyReadable.Marshal(b, m, deterministic)
}
func (dst *PresKeyReadable) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresKeyReadable.Merge(dst, src)
}
func (m *PresKeyReadable) XXX_Size() int {
	return xxx_messageInfo_PresKeyReadable.Size(m)
}
func (m *PresKeyReadable) XXX_DiscardUnknown() {
	xxx_messageInfo_PresKeyReadable.DiscardUnknown(m)
}

var xxx_messageInfo_PresKeyReadable proto.InternalMessageInfo

func (m *PresKeyReadable) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *PresKeyReadable) GetClientId() uint32 {
	if m != nil {
		return m.ClientId
	}
	return 0
}

func (m *PresKeyReadable) GetProxyAddr() string {
	if m != nil {
		return m.ProxyAddr
	}
	return ""
}

type PresValReadable struct {
	DeviceId             []byte   `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	ClientIp             string   `protobuf:"bytes,2,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	OnlineTs             string   `protobuf:"bytes,3,opt,name=online_ts,json=onlineTs,proto3" json:"online_ts,omitempty"`
	Terminal             string   `protobuf:"bytes,4,opt,name=terminal,proto3" json:"terminal,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresValReadable) Reset()         { *m = PresValReadable{} }
func (m *PresValReadable) String() string { return proto.CompactTextString(m) }
func (*PresValReadable) ProtoMessage()    {}
func (*PresValReadable) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{4}
}
func (m *PresValReadable) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresValReadable.Unmarshal(m, b)
}
func (m *PresValReadable) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresValReadable.Marshal(b, m, deterministic)
}
func (dst *PresValReadable) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresValReadable.Merge(dst, src)
}
func (m *PresValReadable) XXX_Size() int {
	return xxx_messageInfo_PresValReadable.Size(m)
}
func (m *PresValReadable) XXX_DiscardUnknown() {
	xxx_messageInfo_PresValReadable.DiscardUnknown(m)
}

var xxx_messageInfo_PresValReadable proto.InternalMessageInfo

func (m *PresValReadable) GetDeviceId() []byte {
	if m != nil {
		return m.DeviceId
	}
	return nil
}

func (m *PresValReadable) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

func (m *PresValReadable) GetOnlineTs() string {
	if m != nil {
		return m.OnlineTs
	}
	return ""
}

func (m *PresValReadable) GetTerminal() string {
	if m != nil {
		return m.Terminal
	}
	return ""
}

type PresInfoReadable struct {
	Key                  *PresKeyReadable `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Val                  *PresValReadable `protobuf:"bytes,2,opt,name=val,proto3" json:"val,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *PresInfoReadable) Reset()         { *m = PresInfoReadable{} }
func (m *PresInfoReadable) String() string { return proto.CompactTextString(m) }
func (*PresInfoReadable) ProtoMessage()    {}
func (*PresInfoReadable) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{5}
}
func (m *PresInfoReadable) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresInfoReadable.Unmarshal(m, b)
}
func (m *PresInfoReadable) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresInfoReadable.Marshal(b, m, deterministic)
}
func (dst *PresInfoReadable) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresInfoReadable.Merge(dst, src)
}
func (m *PresInfoReadable) XXX_Size() int {
	return xxx_messageInfo_PresInfoReadable.Size(m)
}
func (m *PresInfoReadable) XXX_DiscardUnknown() {
	xxx_messageInfo_PresInfoReadable.DiscardUnknown(m)
}

var xxx_messageInfo_PresInfoReadable proto.InternalMessageInfo

func (m *PresInfoReadable) GetKey() *PresKeyReadable {
	if m != nil {
		return m.Key
	}
	return nil
}

func (m *PresInfoReadable) GetVal() *PresValReadable {
	if m != nil {
		return m.Val
	}
	return nil
}

type ProxyNode struct {
	Ip                   uint32   `protobuf:"varint,1,opt,name=ip,proto3" json:"ip,omitempty"`
	Port                 uint32   `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	Boot                 uint32   `protobuf:"varint,3,opt,name=boot,proto3" json:"boot,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProxyNode) Reset()         { *m = ProxyNode{} }
func (m *ProxyNode) String() string { return proto.CompactTextString(m) }
func (*ProxyNode) ProtoMessage()    {}
func (*ProxyNode) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{6}
}
func (m *ProxyNode) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProxyNode.Unmarshal(m, b)
}
func (m *ProxyNode) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProxyNode.Marshal(b, m, deterministic)
}
func (dst *ProxyNode) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProxyNode.Merge(dst, src)
}
func (m *ProxyNode) XXX_Size() int {
	return xxx_messageInfo_ProxyNode.Size(m)
}
func (m *ProxyNode) XXX_DiscardUnknown() {
	xxx_messageInfo_ProxyNode.DiscardUnknown(m)
}

var xxx_messageInfo_ProxyNode proto.InternalMessageInfo

func (m *ProxyNode) GetIp() uint32 {
	if m != nil {
		return m.Ip
	}
	return 0
}

func (m *ProxyNode) GetPort() uint32 {
	if m != nil {
		return m.Port
	}
	return 0
}

func (m *ProxyNode) GetBoot() uint32 {
	if m != nil {
		return m.Boot
	}
	return 0
}

type ProxyNodeStat struct {
	ProxyIp              uint32   `protobuf:"varint,1,opt,name=proxy_ip,json=proxyIp,proto3" json:"proxy_ip,omitempty"`
	ProxyPort            uint32   `protobuf:"varint,2,opt,name=proxy_port,json=proxyPort,proto3" json:"proxy_port,omitempty"`
	ProxyBoot            uint32   `protobuf:"varint,3,opt,name=proxy_boot,json=proxyBoot,proto3" json:"proxy_boot,omitempty"`
	OnlineCnt            uint32   `protobuf:"varint,4,opt,name=online_cnt,json=onlineCnt,proto3" json:"online_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProxyNodeStat) Reset()         { *m = ProxyNodeStat{} }
func (m *ProxyNodeStat) String() string { return proto.CompactTextString(m) }
func (*ProxyNodeStat) ProtoMessage()    {}
func (*ProxyNodeStat) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{7}
}
func (m *ProxyNodeStat) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProxyNodeStat.Unmarshal(m, b)
}
func (m *ProxyNodeStat) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProxyNodeStat.Marshal(b, m, deterministic)
}
func (dst *ProxyNodeStat) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProxyNodeStat.Merge(dst, src)
}
func (m *ProxyNodeStat) XXX_Size() int {
	return xxx_messageInfo_ProxyNodeStat.Size(m)
}
func (m *ProxyNodeStat) XXX_DiscardUnknown() {
	xxx_messageInfo_ProxyNodeStat.DiscardUnknown(m)
}

var xxx_messageInfo_ProxyNodeStat proto.InternalMessageInfo

func (m *ProxyNodeStat) GetProxyIp() uint32 {
	if m != nil {
		return m.ProxyIp
	}
	return 0
}

func (m *ProxyNodeStat) GetProxyPort() uint32 {
	if m != nil {
		return m.ProxyPort
	}
	return 0
}

func (m *ProxyNodeStat) GetProxyBoot() uint32 {
	if m != nil {
		return m.ProxyBoot
	}
	return 0
}

func (m *ProxyNodeStat) GetOnlineCnt() uint32 {
	if m != nil {
		return m.OnlineCnt
	}
	return 0
}

type PresInfoList struct {
	InfoList             []*PresInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *PresInfoList) Reset()         { *m = PresInfoList{} }
func (m *PresInfoList) String() string { return proto.CompactTextString(m) }
func (*PresInfoList) ProtoMessage()    {}
func (*PresInfoList) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{8}
}
func (m *PresInfoList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresInfoList.Unmarshal(m, b)
}
func (m *PresInfoList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresInfoList.Marshal(b, m, deterministic)
}
func (dst *PresInfoList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresInfoList.Merge(dst, src)
}
func (m *PresInfoList) XXX_Size() int {
	return xxx_messageInfo_PresInfoList.Size(m)
}
func (m *PresInfoList) XXX_DiscardUnknown() {
	xxx_messageInfo_PresInfoList.DiscardUnknown(m)
}

var xxx_messageInfo_PresInfoList proto.InternalMessageInfo

func (m *PresInfoList) GetInfoList() []*PresInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type PresInfoListReadable struct {
	InfoList             []*PresInfoReadable `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *PresInfoListReadable) Reset()         { *m = PresInfoListReadable{} }
func (m *PresInfoListReadable) String() string { return proto.CompactTextString(m) }
func (*PresInfoListReadable) ProtoMessage()    {}
func (*PresInfoListReadable) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{9}
}
func (m *PresInfoListReadable) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresInfoListReadable.Unmarshal(m, b)
}
func (m *PresInfoListReadable) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresInfoListReadable.Marshal(b, m, deterministic)
}
func (dst *PresInfoListReadable) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresInfoListReadable.Merge(dst, src)
}
func (m *PresInfoListReadable) XXX_Size() int {
	return xxx_messageInfo_PresInfoListReadable.Size(m)
}
func (m *PresInfoListReadable) XXX_DiscardUnknown() {
	xxx_messageInfo_PresInfoListReadable.DiscardUnknown(m)
}

var xxx_messageInfo_PresInfoListReadable proto.InternalMessageInfo

func (m *PresInfoListReadable) GetInfoList() []*PresInfoReadable {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type GetUserPresReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserPresReq) Reset()         { *m = GetUserPresReq{} }
func (m *GetUserPresReq) String() string { return proto.CompactTextString(m) }
func (*GetUserPresReq) ProtoMessage()    {}
func (*GetUserPresReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{10}
}
func (m *GetUserPresReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPresReq.Unmarshal(m, b)
}
func (m *GetUserPresReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPresReq.Marshal(b, m, deterministic)
}
func (dst *GetUserPresReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPresReq.Merge(dst, src)
}
func (m *GetUserPresReq) XXX_Size() int {
	return xxx_messageInfo_GetUserPresReq.Size(m)
}
func (m *GetUserPresReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPresReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPresReq proto.InternalMessageInfo

func (m *GetUserPresReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserPresResp struct {
	Info                 *PresInfoList `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetUserPresResp) Reset()         { *m = GetUserPresResp{} }
func (m *GetUserPresResp) String() string { return proto.CompactTextString(m) }
func (*GetUserPresResp) ProtoMessage()    {}
func (*GetUserPresResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{11}
}
func (m *GetUserPresResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPresResp.Unmarshal(m, b)
}
func (m *GetUserPresResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPresResp.Marshal(b, m, deterministic)
}
func (dst *GetUserPresResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPresResp.Merge(dst, src)
}
func (m *GetUserPresResp) XXX_Size() int {
	return xxx_messageInfo_GetUserPresResp.Size(m)
}
func (m *GetUserPresResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPresResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPresResp proto.InternalMessageInfo

func (m *GetUserPresResp) GetInfo() *PresInfoList {
	if m != nil {
		return m.Info
	}
	return nil
}

type GetUserPresReadableResp struct {
	Info                 *PresInfoListReadable `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetUserPresReadableResp) Reset()         { *m = GetUserPresReadableResp{} }
func (m *GetUserPresReadableResp) String() string { return proto.CompactTextString(m) }
func (*GetUserPresReadableResp) ProtoMessage()    {}
func (*GetUserPresReadableResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{12}
}
func (m *GetUserPresReadableResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPresReadableResp.Unmarshal(m, b)
}
func (m *GetUserPresReadableResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPresReadableResp.Marshal(b, m, deterministic)
}
func (dst *GetUserPresReadableResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPresReadableResp.Merge(dst, src)
}
func (m *GetUserPresReadableResp) XXX_Size() int {
	return xxx_messageInfo_GetUserPresReadableResp.Size(m)
}
func (m *GetUserPresReadableResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPresReadableResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPresReadableResp proto.InternalMessageInfo

func (m *GetUserPresReadableResp) GetInfo() *PresInfoListReadable {
	if m != nil {
		return m.Info
	}
	return nil
}

type BatchGetUserPresReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetUserPresReq) Reset()         { *m = BatchGetUserPresReq{} }
func (m *BatchGetUserPresReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserPresReq) ProtoMessage()    {}
func (*BatchGetUserPresReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{13}
}
func (m *BatchGetUserPresReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserPresReq.Unmarshal(m, b)
}
func (m *BatchGetUserPresReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserPresReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserPresReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserPresReq.Merge(dst, src)
}
func (m *BatchGetUserPresReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserPresReq.Size(m)
}
func (m *BatchGetUserPresReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserPresReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserPresReq proto.InternalMessageInfo

func (m *BatchGetUserPresReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchGetUserPresResp struct {
	InfoMap              map[uint32]*PresInfoList `protobuf:"bytes,1,rep,name=info_map,json=infoMap,proto3" json:"info_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *BatchGetUserPresResp) Reset()         { *m = BatchGetUserPresResp{} }
func (m *BatchGetUserPresResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserPresResp) ProtoMessage()    {}
func (*BatchGetUserPresResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{14}
}
func (m *BatchGetUserPresResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserPresResp.Unmarshal(m, b)
}
func (m *BatchGetUserPresResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserPresResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserPresResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserPresResp.Merge(dst, src)
}
func (m *BatchGetUserPresResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserPresResp.Size(m)
}
func (m *BatchGetUserPresResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserPresResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserPresResp proto.InternalMessageInfo

func (m *BatchGetUserPresResp) GetInfoMap() map[uint32]*PresInfoList {
	if m != nil {
		return m.InfoMap
	}
	return nil
}

type BatchGetUserPresReadableResp struct {
	InfoMap              map[uint32]*PresInfoListReadable `protobuf:"bytes,1,rep,name=info_map,json=infoMap,proto3" json:"info_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *BatchGetUserPresReadableResp) Reset()         { *m = BatchGetUserPresReadableResp{} }
func (m *BatchGetUserPresReadableResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserPresReadableResp) ProtoMessage()    {}
func (*BatchGetUserPresReadableResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{15}
}
func (m *BatchGetUserPresReadableResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserPresReadableResp.Unmarshal(m, b)
}
func (m *BatchGetUserPresReadableResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserPresReadableResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserPresReadableResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserPresReadableResp.Merge(dst, src)
}
func (m *BatchGetUserPresReadableResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserPresReadableResp.Size(m)
}
func (m *BatchGetUserPresReadableResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserPresReadableResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserPresReadableResp proto.InternalMessageInfo

func (m *BatchGetUserPresReadableResp) GetInfoMap() map[uint32]*PresInfoListReadable {
	if m != nil {
		return m.InfoMap
	}
	return nil
}

type PeekUserPresReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PeekUserPresReq) Reset()         { *m = PeekUserPresReq{} }
func (m *PeekUserPresReq) String() string { return proto.CompactTextString(m) }
func (*PeekUserPresReq) ProtoMessage()    {}
func (*PeekUserPresReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{16}
}
func (m *PeekUserPresReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PeekUserPresReq.Unmarshal(m, b)
}
func (m *PeekUserPresReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PeekUserPresReq.Marshal(b, m, deterministic)
}
func (dst *PeekUserPresReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PeekUserPresReq.Merge(dst, src)
}
func (m *PeekUserPresReq) XXX_Size() int {
	return xxx_messageInfo_PeekUserPresReq.Size(m)
}
func (m *PeekUserPresReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PeekUserPresReq.DiscardUnknown(m)
}

var xxx_messageInfo_PeekUserPresReq proto.InternalMessageInfo

func (m *PeekUserPresReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type PeekUserPresResp struct {
	Online               bool     `protobuf:"varint,1,opt,name=online,proto3" json:"online,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PeekUserPresResp) Reset()         { *m = PeekUserPresResp{} }
func (m *PeekUserPresResp) String() string { return proto.CompactTextString(m) }
func (*PeekUserPresResp) ProtoMessage()    {}
func (*PeekUserPresResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{17}
}
func (m *PeekUserPresResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PeekUserPresResp.Unmarshal(m, b)
}
func (m *PeekUserPresResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PeekUserPresResp.Marshal(b, m, deterministic)
}
func (dst *PeekUserPresResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PeekUserPresResp.Merge(dst, src)
}
func (m *PeekUserPresResp) XXX_Size() int {
	return xxx_messageInfo_PeekUserPresResp.Size(m)
}
func (m *PeekUserPresResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PeekUserPresResp.DiscardUnknown(m)
}

var xxx_messageInfo_PeekUserPresResp proto.InternalMessageInfo

func (m *PeekUserPresResp) GetOnline() bool {
	if m != nil {
		return m.Online
	}
	return false
}

type BatchPeekUserPresReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchPeekUserPresReq) Reset()         { *m = BatchPeekUserPresReq{} }
func (m *BatchPeekUserPresReq) String() string { return proto.CompactTextString(m) }
func (*BatchPeekUserPresReq) ProtoMessage()    {}
func (*BatchPeekUserPresReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{18}
}
func (m *BatchPeekUserPresReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchPeekUserPresReq.Unmarshal(m, b)
}
func (m *BatchPeekUserPresReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchPeekUserPresReq.Marshal(b, m, deterministic)
}
func (dst *BatchPeekUserPresReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchPeekUserPresReq.Merge(dst, src)
}
func (m *BatchPeekUserPresReq) XXX_Size() int {
	return xxx_messageInfo_BatchPeekUserPresReq.Size(m)
}
func (m *BatchPeekUserPresReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchPeekUserPresReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchPeekUserPresReq proto.InternalMessageInfo

func (m *BatchPeekUserPresReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchPeekUserPresResp struct {
	OnlineMap            map[uint32]bool `protobuf:"bytes,1,rep,name=online_map,json=onlineMap,proto3" json:"online_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatchPeekUserPresResp) Reset()         { *m = BatchPeekUserPresResp{} }
func (m *BatchPeekUserPresResp) String() string { return proto.CompactTextString(m) }
func (*BatchPeekUserPresResp) ProtoMessage()    {}
func (*BatchPeekUserPresResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{19}
}
func (m *BatchPeekUserPresResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchPeekUserPresResp.Unmarshal(m, b)
}
func (m *BatchPeekUserPresResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchPeekUserPresResp.Marshal(b, m, deterministic)
}
func (dst *BatchPeekUserPresResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchPeekUserPresResp.Merge(dst, src)
}
func (m *BatchPeekUserPresResp) XXX_Size() int {
	return xxx_messageInfo_BatchPeekUserPresResp.Size(m)
}
func (m *BatchPeekUserPresResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchPeekUserPresResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchPeekUserPresResp proto.InternalMessageInfo

func (m *BatchPeekUserPresResp) GetOnlineMap() map[uint32]bool {
	if m != nil {
		return m.OnlineMap
	}
	return nil
}

type SetUserPresReq struct {
	Uid                  uint32        `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Proxy                *ProxyNode    `protobuf:"bytes,2,opt,name=proxy,proto3" json:"proxy,omitempty"`
	Info                 *PresInfoList `protobuf:"bytes,3,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetUserPresReq) Reset()         { *m = SetUserPresReq{} }
func (m *SetUserPresReq) String() string { return proto.CompactTextString(m) }
func (*SetUserPresReq) ProtoMessage()    {}
func (*SetUserPresReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{20}
}
func (m *SetUserPresReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserPresReq.Unmarshal(m, b)
}
func (m *SetUserPresReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserPresReq.Marshal(b, m, deterministic)
}
func (dst *SetUserPresReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserPresReq.Merge(dst, src)
}
func (m *SetUserPresReq) XXX_Size() int {
	return xxx_messageInfo_SetUserPresReq.Size(m)
}
func (m *SetUserPresReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserPresReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserPresReq proto.InternalMessageInfo

func (m *SetUserPresReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserPresReq) GetProxy() *ProxyNode {
	if m != nil {
		return m.Proxy
	}
	return nil
}

func (m *SetUserPresReq) GetInfo() *PresInfoList {
	if m != nil {
		return m.Info
	}
	return nil
}

type SetUserPresResp struct {
	Online               bool     `protobuf:"varint,1,opt,name=online,proto3" json:"online,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserPresResp) Reset()         { *m = SetUserPresResp{} }
func (m *SetUserPresResp) String() string { return proto.CompactTextString(m) }
func (*SetUserPresResp) ProtoMessage()    {}
func (*SetUserPresResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{21}
}
func (m *SetUserPresResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserPresResp.Unmarshal(m, b)
}
func (m *SetUserPresResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserPresResp.Marshal(b, m, deterministic)
}
func (dst *SetUserPresResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserPresResp.Merge(dst, src)
}
func (m *SetUserPresResp) XXX_Size() int {
	return xxx_messageInfo_SetUserPresResp.Size(m)
}
func (m *SetUserPresResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserPresResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserPresResp proto.InternalMessageInfo

func (m *SetUserPresResp) GetOnline() bool {
	if m != nil {
		return m.Online
	}
	return false
}

type BatchSetUserPresReq struct {
	Proxy                *ProxyNode               `protobuf:"bytes,1,opt,name=proxy,proto3" json:"proxy,omitempty"`
	InfoMap              map[uint32]*PresInfoList `protobuf:"bytes,2,rep,name=info_map,json=infoMap,proto3" json:"info_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *BatchSetUserPresReq) Reset()         { *m = BatchSetUserPresReq{} }
func (m *BatchSetUserPresReq) String() string { return proto.CompactTextString(m) }
func (*BatchSetUserPresReq) ProtoMessage()    {}
func (*BatchSetUserPresReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{22}
}
func (m *BatchSetUserPresReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSetUserPresReq.Unmarshal(m, b)
}
func (m *BatchSetUserPresReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSetUserPresReq.Marshal(b, m, deterministic)
}
func (dst *BatchSetUserPresReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSetUserPresReq.Merge(dst, src)
}
func (m *BatchSetUserPresReq) XXX_Size() int {
	return xxx_messageInfo_BatchSetUserPresReq.Size(m)
}
func (m *BatchSetUserPresReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSetUserPresReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSetUserPresReq proto.InternalMessageInfo

func (m *BatchSetUserPresReq) GetProxy() *ProxyNode {
	if m != nil {
		return m.Proxy
	}
	return nil
}

func (m *BatchSetUserPresReq) GetInfoMap() map[uint32]*PresInfoList {
	if m != nil {
		return m.InfoMap
	}
	return nil
}

type BatchSetUserPresResp struct {
	Result               map[uint32]bool `protobuf:"bytes,1,rep,name=result,proto3" json:"result,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatchSetUserPresResp) Reset()         { *m = BatchSetUserPresResp{} }
func (m *BatchSetUserPresResp) String() string { return proto.CompactTextString(m) }
func (*BatchSetUserPresResp) ProtoMessage()    {}
func (*BatchSetUserPresResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{23}
}
func (m *BatchSetUserPresResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSetUserPresResp.Unmarshal(m, b)
}
func (m *BatchSetUserPresResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSetUserPresResp.Marshal(b, m, deterministic)
}
func (dst *BatchSetUserPresResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSetUserPresResp.Merge(dst, src)
}
func (m *BatchSetUserPresResp) XXX_Size() int {
	return xxx_messageInfo_BatchSetUserPresResp.Size(m)
}
func (m *BatchSetUserPresResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSetUserPresResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSetUserPresResp proto.InternalMessageInfo

func (m *BatchSetUserPresResp) GetResult() map[uint32]bool {
	if m != nil {
		return m.Result
	}
	return nil
}

type DelUserPresReq struct {
	Uid                  uint32        `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Proxy                *ProxyNode    `protobuf:"bytes,2,opt,name=proxy,proto3" json:"proxy,omitempty"`
	Info                 *PresInfoList `protobuf:"bytes,3,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *DelUserPresReq) Reset()         { *m = DelUserPresReq{} }
func (m *DelUserPresReq) String() string { return proto.CompactTextString(m) }
func (*DelUserPresReq) ProtoMessage()    {}
func (*DelUserPresReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{24}
}
func (m *DelUserPresReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelUserPresReq.Unmarshal(m, b)
}
func (m *DelUserPresReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelUserPresReq.Marshal(b, m, deterministic)
}
func (dst *DelUserPresReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelUserPresReq.Merge(dst, src)
}
func (m *DelUserPresReq) XXX_Size() int {
	return xxx_messageInfo_DelUserPresReq.Size(m)
}
func (m *DelUserPresReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelUserPresReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelUserPresReq proto.InternalMessageInfo

func (m *DelUserPresReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DelUserPresReq) GetProxy() *ProxyNode {
	if m != nil {
		return m.Proxy
	}
	return nil
}

func (m *DelUserPresReq) GetInfo() *PresInfoList {
	if m != nil {
		return m.Info
	}
	return nil
}

type DelUserPresResp struct {
	Offline              bool     `protobuf:"varint,1,opt,name=offline,proto3" json:"offline,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelUserPresResp) Reset()         { *m = DelUserPresResp{} }
func (m *DelUserPresResp) String() string { return proto.CompactTextString(m) }
func (*DelUserPresResp) ProtoMessage()    {}
func (*DelUserPresResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{25}
}
func (m *DelUserPresResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelUserPresResp.Unmarshal(m, b)
}
func (m *DelUserPresResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelUserPresResp.Marshal(b, m, deterministic)
}
func (dst *DelUserPresResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelUserPresResp.Merge(dst, src)
}
func (m *DelUserPresResp) XXX_Size() int {
	return xxx_messageInfo_DelUserPresResp.Size(m)
}
func (m *DelUserPresResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelUserPresResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelUserPresResp proto.InternalMessageInfo

func (m *DelUserPresResp) GetOffline() bool {
	if m != nil {
		return m.Offline
	}
	return false
}

type BatchDelUserPresReq struct {
	Proxy                *ProxyNode               `protobuf:"bytes,1,opt,name=proxy,proto3" json:"proxy,omitempty"`
	InfoMap              map[uint32]*PresInfoList `protobuf:"bytes,2,rep,name=info_map,json=infoMap,proto3" json:"info_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *BatchDelUserPresReq) Reset()         { *m = BatchDelUserPresReq{} }
func (m *BatchDelUserPresReq) String() string { return proto.CompactTextString(m) }
func (*BatchDelUserPresReq) ProtoMessage()    {}
func (*BatchDelUserPresReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{26}
}
func (m *BatchDelUserPresReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelUserPresReq.Unmarshal(m, b)
}
func (m *BatchDelUserPresReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelUserPresReq.Marshal(b, m, deterministic)
}
func (dst *BatchDelUserPresReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelUserPresReq.Merge(dst, src)
}
func (m *BatchDelUserPresReq) XXX_Size() int {
	return xxx_messageInfo_BatchDelUserPresReq.Size(m)
}
func (m *BatchDelUserPresReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelUserPresReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelUserPresReq proto.InternalMessageInfo

func (m *BatchDelUserPresReq) GetProxy() *ProxyNode {
	if m != nil {
		return m.Proxy
	}
	return nil
}

func (m *BatchDelUserPresReq) GetInfoMap() map[uint32]*PresInfoList {
	if m != nil {
		return m.InfoMap
	}
	return nil
}

type BatchDelUserPresResp struct {
	Result               map[uint32]bool `protobuf:"bytes,1,rep,name=result,proto3" json:"result,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatchDelUserPresResp) Reset()         { *m = BatchDelUserPresResp{} }
func (m *BatchDelUserPresResp) String() string { return proto.CompactTextString(m) }
func (*BatchDelUserPresResp) ProtoMessage()    {}
func (*BatchDelUserPresResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{27}
}
func (m *BatchDelUserPresResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelUserPresResp.Unmarshal(m, b)
}
func (m *BatchDelUserPresResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelUserPresResp.Marshal(b, m, deterministic)
}
func (dst *BatchDelUserPresResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelUserPresResp.Merge(dst, src)
}
func (m *BatchDelUserPresResp) XXX_Size() int {
	return xxx_messageInfo_BatchDelUserPresResp.Size(m)
}
func (m *BatchDelUserPresResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelUserPresResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelUserPresResp proto.InternalMessageInfo

func (m *BatchDelUserPresResp) GetResult() map[uint32]bool {
	if m != nil {
		return m.Result
	}
	return nil
}

type GetProxyPresReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetProxyPresReq) Reset()         { *m = GetProxyPresReq{} }
func (m *GetProxyPresReq) String() string { return proto.CompactTextString(m) }
func (*GetProxyPresReq) ProtoMessage()    {}
func (*GetProxyPresReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{28}
}
func (m *GetProxyPresReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetProxyPresReq.Unmarshal(m, b)
}
func (m *GetProxyPresReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetProxyPresReq.Marshal(b, m, deterministic)
}
func (dst *GetProxyPresReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetProxyPresReq.Merge(dst, src)
}
func (m *GetProxyPresReq) XXX_Size() int {
	return xxx_messageInfo_GetProxyPresReq.Size(m)
}
func (m *GetProxyPresReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetProxyPresReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetProxyPresReq proto.InternalMessageInfo

type GetProxyPresResp struct {
	InfoList             []*ProxyNodeStat `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetProxyPresResp) Reset()         { *m = GetProxyPresResp{} }
func (m *GetProxyPresResp) String() string { return proto.CompactTextString(m) }
func (*GetProxyPresResp) ProtoMessage()    {}
func (*GetProxyPresResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{29}
}
func (m *GetProxyPresResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetProxyPresResp.Unmarshal(m, b)
}
func (m *GetProxyPresResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetProxyPresResp.Marshal(b, m, deterministic)
}
func (dst *GetProxyPresResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetProxyPresResp.Merge(dst, src)
}
func (m *GetProxyPresResp) XXX_Size() int {
	return xxx_messageInfo_GetProxyPresResp.Size(m)
}
func (m *GetProxyPresResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetProxyPresResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetProxyPresResp proto.InternalMessageInfo

func (m *GetProxyPresResp) GetInfoList() []*ProxyNodeStat {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type GetUserOnlineCountReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserOnlineCountReq) Reset()         { *m = GetUserOnlineCountReq{} }
func (m *GetUserOnlineCountReq) String() string { return proto.CompactTextString(m) }
func (*GetUserOnlineCountReq) ProtoMessage()    {}
func (*GetUserOnlineCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{30}
}
func (m *GetUserOnlineCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserOnlineCountReq.Unmarshal(m, b)
}
func (m *GetUserOnlineCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserOnlineCountReq.Marshal(b, m, deterministic)
}
func (dst *GetUserOnlineCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserOnlineCountReq.Merge(dst, src)
}
func (m *GetUserOnlineCountReq) XXX_Size() int {
	return xxx_messageInfo_GetUserOnlineCountReq.Size(m)
}
func (m *GetUserOnlineCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserOnlineCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserOnlineCountReq proto.InternalMessageInfo

type GetUserOnlineCountResp struct {
	UserCount            uint32   `protobuf:"varint,1,opt,name=user_count,json=userCount,proto3" json:"user_count,omitempty"`
	ConnCount            uint32   `protobuf:"varint,2,opt,name=conn_count,json=connCount,proto3" json:"conn_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserOnlineCountResp) Reset()         { *m = GetUserOnlineCountResp{} }
func (m *GetUserOnlineCountResp) String() string { return proto.CompactTextString(m) }
func (*GetUserOnlineCountResp) ProtoMessage()    {}
func (*GetUserOnlineCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{31}
}
func (m *GetUserOnlineCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserOnlineCountResp.Unmarshal(m, b)
}
func (m *GetUserOnlineCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserOnlineCountResp.Marshal(b, m, deterministic)
}
func (dst *GetUserOnlineCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserOnlineCountResp.Merge(dst, src)
}
func (m *GetUserOnlineCountResp) XXX_Size() int {
	return xxx_messageInfo_GetUserOnlineCountResp.Size(m)
}
func (m *GetUserOnlineCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserOnlineCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserOnlineCountResp proto.InternalMessageInfo

func (m *GetUserOnlineCountResp) GetUserCount() uint32 {
	if m != nil {
		return m.UserCount
	}
	return 0
}

func (m *GetUserOnlineCountResp) GetConnCount() uint32 {
	if m != nil {
		return m.ConnCount
	}
	return 0
}

type UpdateUserPresReq struct {
	Proxy                *ProxyNode               `protobuf:"bytes,1,opt,name=proxy,proto3" json:"proxy,omitempty"`
	InfoMap              map[uint32]*PresInfoList `protobuf:"bytes,2,rep,name=info_map,json=infoMap,proto3" json:"info_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *UpdateUserPresReq) Reset()         { *m = UpdateUserPresReq{} }
func (m *UpdateUserPresReq) String() string { return proto.CompactTextString(m) }
func (*UpdateUserPresReq) ProtoMessage()    {}
func (*UpdateUserPresReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{32}
}
func (m *UpdateUserPresReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateUserPresReq.Unmarshal(m, b)
}
func (m *UpdateUserPresReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateUserPresReq.Marshal(b, m, deterministic)
}
func (dst *UpdateUserPresReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateUserPresReq.Merge(dst, src)
}
func (m *UpdateUserPresReq) XXX_Size() int {
	return xxx_messageInfo_UpdateUserPresReq.Size(m)
}
func (m *UpdateUserPresReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateUserPresReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateUserPresReq proto.InternalMessageInfo

func (m *UpdateUserPresReq) GetProxy() *ProxyNode {
	if m != nil {
		return m.Proxy
	}
	return nil
}

func (m *UpdateUserPresReq) GetInfoMap() map[uint32]*PresInfoList {
	if m != nil {
		return m.InfoMap
	}
	return nil
}

type UpdateUserPresResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateUserPresResp) Reset()         { *m = UpdateUserPresResp{} }
func (m *UpdateUserPresResp) String() string { return proto.CompactTextString(m) }
func (*UpdateUserPresResp) ProtoMessage()    {}
func (*UpdateUserPresResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{33}
}
func (m *UpdateUserPresResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateUserPresResp.Unmarshal(m, b)
}
func (m *UpdateUserPresResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateUserPresResp.Marshal(b, m, deterministic)
}
func (dst *UpdateUserPresResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateUserPresResp.Merge(dst, src)
}
func (m *UpdateUserPresResp) XXX_Size() int {
	return xxx_messageInfo_UpdateUserPresResp.Size(m)
}
func (m *UpdateUserPresResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateUserPresResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateUserPresResp proto.InternalMessageInfo

type UserKeepAliveReq struct {
	Proxy                *ProxyNode               `protobuf:"bytes,1,opt,name=proxy,proto3" json:"proxy,omitempty"`
	InfoMap              map[uint32]*PresInfoList `protobuf:"bytes,2,rep,name=info_map,json=infoMap,proto3" json:"info_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *UserKeepAliveReq) Reset()         { *m = UserKeepAliveReq{} }
func (m *UserKeepAliveReq) String() string { return proto.CompactTextString(m) }
func (*UserKeepAliveReq) ProtoMessage()    {}
func (*UserKeepAliveReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{34}
}
func (m *UserKeepAliveReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserKeepAliveReq.Unmarshal(m, b)
}
func (m *UserKeepAliveReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserKeepAliveReq.Marshal(b, m, deterministic)
}
func (dst *UserKeepAliveReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserKeepAliveReq.Merge(dst, src)
}
func (m *UserKeepAliveReq) XXX_Size() int {
	return xxx_messageInfo_UserKeepAliveReq.Size(m)
}
func (m *UserKeepAliveReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserKeepAliveReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserKeepAliveReq proto.InternalMessageInfo

func (m *UserKeepAliveReq) GetProxy() *ProxyNode {
	if m != nil {
		return m.Proxy
	}
	return nil
}

func (m *UserKeepAliveReq) GetInfoMap() map[uint32]*PresInfoList {
	if m != nil {
		return m.InfoMap
	}
	return nil
}

type UserKeepAliveResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserKeepAliveResp) Reset()         { *m = UserKeepAliveResp{} }
func (m *UserKeepAliveResp) String() string { return proto.CompactTextString(m) }
func (*UserKeepAliveResp) ProtoMessage()    {}
func (*UserKeepAliveResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{35}
}
func (m *UserKeepAliveResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserKeepAliveResp.Unmarshal(m, b)
}
func (m *UserKeepAliveResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserKeepAliveResp.Marshal(b, m, deterministic)
}
func (dst *UserKeepAliveResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserKeepAliveResp.Merge(dst, src)
}
func (m *UserKeepAliveResp) XXX_Size() int {
	return xxx_messageInfo_UserKeepAliveResp.Size(m)
}
func (m *UserKeepAliveResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserKeepAliveResp.DiscardUnknown(m)
}

var xxx_messageInfo_UserKeepAliveResp proto.InternalMessageInfo

type SetChannelPresReq struct {
	ProxyIp              uint32   `protobuf:"varint,1,opt,name=proxy_ip,json=proxyIp,proto3" json:"proxy_ip,omitempty"`
	ProxyPort            uint32   `protobuf:"varint,2,opt,name=proxy_port,json=proxyPort,proto3" json:"proxy_port,omitempty"`
	ProxyIndex           uint32   `protobuf:"varint,3,opt,name=proxy_index,json=proxyIndex,proto3" json:"proxy_index,omitempty"`
	ChannelId            uint32   `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelPresReq) Reset()         { *m = SetChannelPresReq{} }
func (m *SetChannelPresReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelPresReq) ProtoMessage()    {}
func (*SetChannelPresReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{36}
}
func (m *SetChannelPresReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelPresReq.Unmarshal(m, b)
}
func (m *SetChannelPresReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelPresReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelPresReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelPresReq.Merge(dst, src)
}
func (m *SetChannelPresReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelPresReq.Size(m)
}
func (m *SetChannelPresReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelPresReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelPresReq proto.InternalMessageInfo

func (m *SetChannelPresReq) GetProxyIp() uint32 {
	if m != nil {
		return m.ProxyIp
	}
	return 0
}

func (m *SetChannelPresReq) GetProxyPort() uint32 {
	if m != nil {
		return m.ProxyPort
	}
	return 0
}

func (m *SetChannelPresReq) GetProxyIndex() uint32 {
	if m != nil {
		return m.ProxyIndex
	}
	return 0
}

func (m *SetChannelPresReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type SetChannelPresResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelPresResp) Reset()         { *m = SetChannelPresResp{} }
func (m *SetChannelPresResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelPresResp) ProtoMessage()    {}
func (*SetChannelPresResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{37}
}
func (m *SetChannelPresResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelPresResp.Unmarshal(m, b)
}
func (m *SetChannelPresResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelPresResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelPresResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelPresResp.Merge(dst, src)
}
func (m *SetChannelPresResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelPresResp.Size(m)
}
func (m *SetChannelPresResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelPresResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelPresResp proto.InternalMessageInfo

type BatchSetChannelPresReq struct {
	InfoList             []*BatchSetChannelPresReq_Info `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *BatchSetChannelPresReq) Reset()         { *m = BatchSetChannelPresReq{} }
func (m *BatchSetChannelPresReq) String() string { return proto.CompactTextString(m) }
func (*BatchSetChannelPresReq) ProtoMessage()    {}
func (*BatchSetChannelPresReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{38}
}
func (m *BatchSetChannelPresReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSetChannelPresReq.Unmarshal(m, b)
}
func (m *BatchSetChannelPresReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSetChannelPresReq.Marshal(b, m, deterministic)
}
func (dst *BatchSetChannelPresReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSetChannelPresReq.Merge(dst, src)
}
func (m *BatchSetChannelPresReq) XXX_Size() int {
	return xxx_messageInfo_BatchSetChannelPresReq.Size(m)
}
func (m *BatchSetChannelPresReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSetChannelPresReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSetChannelPresReq proto.InternalMessageInfo

func (m *BatchSetChannelPresReq) GetInfoList() []*BatchSetChannelPresReq_Info {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type BatchSetChannelPresReq_Info struct {
	ProxyIp              uint32   `protobuf:"varint,1,opt,name=proxy_ip,json=proxyIp,proto3" json:"proxy_ip,omitempty"`
	ProxyPort            uint32   `protobuf:"varint,2,opt,name=proxy_port,json=proxyPort,proto3" json:"proxy_port,omitempty"`
	ProxyIndex           uint32   `protobuf:"varint,3,opt,name=proxy_index,json=proxyIndex,proto3" json:"proxy_index,omitempty"`
	ChannelId            uint32   `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchSetChannelPresReq_Info) Reset()         { *m = BatchSetChannelPresReq_Info{} }
func (m *BatchSetChannelPresReq_Info) String() string { return proto.CompactTextString(m) }
func (*BatchSetChannelPresReq_Info) ProtoMessage()    {}
func (*BatchSetChannelPresReq_Info) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{38, 0}
}
func (m *BatchSetChannelPresReq_Info) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSetChannelPresReq_Info.Unmarshal(m, b)
}
func (m *BatchSetChannelPresReq_Info) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSetChannelPresReq_Info.Marshal(b, m, deterministic)
}
func (dst *BatchSetChannelPresReq_Info) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSetChannelPresReq_Info.Merge(dst, src)
}
func (m *BatchSetChannelPresReq_Info) XXX_Size() int {
	return xxx_messageInfo_BatchSetChannelPresReq_Info.Size(m)
}
func (m *BatchSetChannelPresReq_Info) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSetChannelPresReq_Info.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSetChannelPresReq_Info proto.InternalMessageInfo

func (m *BatchSetChannelPresReq_Info) GetProxyIp() uint32 {
	if m != nil {
		return m.ProxyIp
	}
	return 0
}

func (m *BatchSetChannelPresReq_Info) GetProxyPort() uint32 {
	if m != nil {
		return m.ProxyPort
	}
	return 0
}

func (m *BatchSetChannelPresReq_Info) GetProxyIndex() uint32 {
	if m != nil {
		return m.ProxyIndex
	}
	return 0
}

func (m *BatchSetChannelPresReq_Info) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type BatchSetChannelPresResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchSetChannelPresResp) Reset()         { *m = BatchSetChannelPresResp{} }
func (m *BatchSetChannelPresResp) String() string { return proto.CompactTextString(m) }
func (*BatchSetChannelPresResp) ProtoMessage()    {}
func (*BatchSetChannelPresResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{39}
}
func (m *BatchSetChannelPresResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSetChannelPresResp.Unmarshal(m, b)
}
func (m *BatchSetChannelPresResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSetChannelPresResp.Marshal(b, m, deterministic)
}
func (dst *BatchSetChannelPresResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSetChannelPresResp.Merge(dst, src)
}
func (m *BatchSetChannelPresResp) XXX_Size() int {
	return xxx_messageInfo_BatchSetChannelPresResp.Size(m)
}
func (m *BatchSetChannelPresResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSetChannelPresResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSetChannelPresResp proto.InternalMessageInfo

type GetChannelProxyPresReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelProxyPresReq) Reset()         { *m = GetChannelProxyPresReq{} }
func (m *GetChannelProxyPresReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelProxyPresReq) ProtoMessage()    {}
func (*GetChannelProxyPresReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{40}
}
func (m *GetChannelProxyPresReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelProxyPresReq.Unmarshal(m, b)
}
func (m *GetChannelProxyPresReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelProxyPresReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelProxyPresReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelProxyPresReq.Merge(dst, src)
}
func (m *GetChannelProxyPresReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelProxyPresReq.Size(m)
}
func (m *GetChannelProxyPresReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelProxyPresReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelProxyPresReq proto.InternalMessageInfo

func (m *GetChannelProxyPresReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelProxyPresResp struct {
	ProxyList            []*GetChannelProxyPresResp_Info `protobuf:"bytes,1,rep,name=proxy_list,json=proxyList,proto3" json:"proxy_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *GetChannelProxyPresResp) Reset()         { *m = GetChannelProxyPresResp{} }
func (m *GetChannelProxyPresResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelProxyPresResp) ProtoMessage()    {}
func (*GetChannelProxyPresResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{41}
}
func (m *GetChannelProxyPresResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelProxyPresResp.Unmarshal(m, b)
}
func (m *GetChannelProxyPresResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelProxyPresResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelProxyPresResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelProxyPresResp.Merge(dst, src)
}
func (m *GetChannelProxyPresResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelProxyPresResp.Size(m)
}
func (m *GetChannelProxyPresResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelProxyPresResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelProxyPresResp proto.InternalMessageInfo

func (m *GetChannelProxyPresResp) GetProxyList() []*GetChannelProxyPresResp_Info {
	if m != nil {
		return m.ProxyList
	}
	return nil
}

type GetChannelProxyPresResp_Info struct {
	ProxyIp              uint32   `protobuf:"varint,1,opt,name=proxy_ip,json=proxyIp,proto3" json:"proxy_ip,omitempty"`
	ProxyPort            uint32   `protobuf:"varint,2,opt,name=proxy_port,json=proxyPort,proto3" json:"proxy_port,omitempty"`
	ProxyIndex           uint32   `protobuf:"varint,3,opt,name=proxy_index,json=proxyIndex,proto3" json:"proxy_index,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelProxyPresResp_Info) Reset()         { *m = GetChannelProxyPresResp_Info{} }
func (m *GetChannelProxyPresResp_Info) String() string { return proto.CompactTextString(m) }
func (*GetChannelProxyPresResp_Info) ProtoMessage()    {}
func (*GetChannelProxyPresResp_Info) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{41, 0}
}
func (m *GetChannelProxyPresResp_Info) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelProxyPresResp_Info.Unmarshal(m, b)
}
func (m *GetChannelProxyPresResp_Info) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelProxyPresResp_Info.Marshal(b, m, deterministic)
}
func (dst *GetChannelProxyPresResp_Info) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelProxyPresResp_Info.Merge(dst, src)
}
func (m *GetChannelProxyPresResp_Info) XXX_Size() int {
	return xxx_messageInfo_GetChannelProxyPresResp_Info.Size(m)
}
func (m *GetChannelProxyPresResp_Info) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelProxyPresResp_Info.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelProxyPresResp_Info proto.InternalMessageInfo

func (m *GetChannelProxyPresResp_Info) GetProxyIp() uint32 {
	if m != nil {
		return m.ProxyIp
	}
	return 0
}

func (m *GetChannelProxyPresResp_Info) GetProxyPort() uint32 {
	if m != nil {
		return m.ProxyPort
	}
	return 0
}

func (m *GetChannelProxyPresResp_Info) GetProxyIndex() uint32 {
	if m != nil {
		return m.ProxyIndex
	}
	return 0
}

type GetProxyChannelPresReq struct {
	ProxyIp              uint32   `protobuf:"varint,1,opt,name=proxy_ip,json=proxyIp,proto3" json:"proxy_ip,omitempty"`
	ProxyPort            uint32   `protobuf:"varint,2,opt,name=proxy_port,json=proxyPort,proto3" json:"proxy_port,omitempty"`
	ProxyIndex           uint32   `protobuf:"varint,3,opt,name=proxy_index,json=proxyIndex,proto3" json:"proxy_index,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetProxyChannelPresReq) Reset()         { *m = GetProxyChannelPresReq{} }
func (m *GetProxyChannelPresReq) String() string { return proto.CompactTextString(m) }
func (*GetProxyChannelPresReq) ProtoMessage()    {}
func (*GetProxyChannelPresReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{42}
}
func (m *GetProxyChannelPresReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetProxyChannelPresReq.Unmarshal(m, b)
}
func (m *GetProxyChannelPresReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetProxyChannelPresReq.Marshal(b, m, deterministic)
}
func (dst *GetProxyChannelPresReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetProxyChannelPresReq.Merge(dst, src)
}
func (m *GetProxyChannelPresReq) XXX_Size() int {
	return xxx_messageInfo_GetProxyChannelPresReq.Size(m)
}
func (m *GetProxyChannelPresReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetProxyChannelPresReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetProxyChannelPresReq proto.InternalMessageInfo

func (m *GetProxyChannelPresReq) GetProxyIp() uint32 {
	if m != nil {
		return m.ProxyIp
	}
	return 0
}

func (m *GetProxyChannelPresReq) GetProxyPort() uint32 {
	if m != nil {
		return m.ProxyPort
	}
	return 0
}

func (m *GetProxyChannelPresReq) GetProxyIndex() uint32 {
	if m != nil {
		return m.ProxyIndex
	}
	return 0
}

type GetProxyChannelPresResp struct {
	ChannelList          []uint32 `protobuf:"varint,1,rep,packed,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetProxyChannelPresResp) Reset()         { *m = GetProxyChannelPresResp{} }
func (m *GetProxyChannelPresResp) String() string { return proto.CompactTextString(m) }
func (*GetProxyChannelPresResp) ProtoMessage()    {}
func (*GetProxyChannelPresResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{43}
}
func (m *GetProxyChannelPresResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetProxyChannelPresResp.Unmarshal(m, b)
}
func (m *GetProxyChannelPresResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetProxyChannelPresResp.Marshal(b, m, deterministic)
}
func (dst *GetProxyChannelPresResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetProxyChannelPresResp.Merge(dst, src)
}
func (m *GetProxyChannelPresResp) XXX_Size() int {
	return xxx_messageInfo_GetProxyChannelPresResp.Size(m)
}
func (m *GetProxyChannelPresResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetProxyChannelPresResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetProxyChannelPresResp proto.InternalMessageInfo

func (m *GetProxyChannelPresResp) GetChannelList() []uint32 {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

type GetChannelProxyWithoutIndexReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelProxyWithoutIndexReq) Reset()         { *m = GetChannelProxyWithoutIndexReq{} }
func (m *GetChannelProxyWithoutIndexReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelProxyWithoutIndexReq) ProtoMessage()    {}
func (*GetChannelProxyWithoutIndexReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{44}
}
func (m *GetChannelProxyWithoutIndexReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelProxyWithoutIndexReq.Unmarshal(m, b)
}
func (m *GetChannelProxyWithoutIndexReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelProxyWithoutIndexReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelProxyWithoutIndexReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelProxyWithoutIndexReq.Merge(dst, src)
}
func (m *GetChannelProxyWithoutIndexReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelProxyWithoutIndexReq.Size(m)
}
func (m *GetChannelProxyWithoutIndexReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelProxyWithoutIndexReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelProxyWithoutIndexReq proto.InternalMessageInfo

func (m *GetChannelProxyWithoutIndexReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelProxyWithoutIndexResp struct {
	ProxyList            []*GetChannelProxyWithoutIndexResp_Info `protobuf:"bytes,1,rep,name=proxy_list,json=proxyList,proto3" json:"proxy_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                `json:"-"`
	XXX_unrecognized     []byte                                  `json:"-"`
	XXX_sizecache        int32                                   `json:"-"`
}

func (m *GetChannelProxyWithoutIndexResp) Reset()         { *m = GetChannelProxyWithoutIndexResp{} }
func (m *GetChannelProxyWithoutIndexResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelProxyWithoutIndexResp) ProtoMessage()    {}
func (*GetChannelProxyWithoutIndexResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{45}
}
func (m *GetChannelProxyWithoutIndexResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelProxyWithoutIndexResp.Unmarshal(m, b)
}
func (m *GetChannelProxyWithoutIndexResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelProxyWithoutIndexResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelProxyWithoutIndexResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelProxyWithoutIndexResp.Merge(dst, src)
}
func (m *GetChannelProxyWithoutIndexResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelProxyWithoutIndexResp.Size(m)
}
func (m *GetChannelProxyWithoutIndexResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelProxyWithoutIndexResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelProxyWithoutIndexResp proto.InternalMessageInfo

func (m *GetChannelProxyWithoutIndexResp) GetProxyList() []*GetChannelProxyWithoutIndexResp_Info {
	if m != nil {
		return m.ProxyList
	}
	return nil
}

type GetChannelProxyWithoutIndexResp_Info struct {
	ProxyIp              uint32   `protobuf:"varint,1,opt,name=proxy_ip,json=proxyIp,proto3" json:"proxy_ip,omitempty"`
	ProxyPort            uint32   `protobuf:"varint,2,opt,name=proxy_port,json=proxyPort,proto3" json:"proxy_port,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelProxyWithoutIndexResp_Info) Reset()         { *m = GetChannelProxyWithoutIndexResp_Info{} }
func (m *GetChannelProxyWithoutIndexResp_Info) String() string { return proto.CompactTextString(m) }
func (*GetChannelProxyWithoutIndexResp_Info) ProtoMessage()    {}
func (*GetChannelProxyWithoutIndexResp_Info) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{45, 0}
}
func (m *GetChannelProxyWithoutIndexResp_Info) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelProxyWithoutIndexResp_Info.Unmarshal(m, b)
}
func (m *GetChannelProxyWithoutIndexResp_Info) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelProxyWithoutIndexResp_Info.Marshal(b, m, deterministic)
}
func (dst *GetChannelProxyWithoutIndexResp_Info) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelProxyWithoutIndexResp_Info.Merge(dst, src)
}
func (m *GetChannelProxyWithoutIndexResp_Info) XXX_Size() int {
	return xxx_messageInfo_GetChannelProxyWithoutIndexResp_Info.Size(m)
}
func (m *GetChannelProxyWithoutIndexResp_Info) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelProxyWithoutIndexResp_Info.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelProxyWithoutIndexResp_Info proto.InternalMessageInfo

func (m *GetChannelProxyWithoutIndexResp_Info) GetProxyIp() uint32 {
	if m != nil {
		return m.ProxyIp
	}
	return 0
}

func (m *GetChannelProxyWithoutIndexResp_Info) GetProxyPort() uint32 {
	if m != nil {
		return m.ProxyPort
	}
	return 0
}

type GetProxyChannelWithScoreReq struct {
	ProxyIp              uint32   `protobuf:"varint,1,opt,name=proxy_ip,json=proxyIp,proto3" json:"proxy_ip,omitempty"`
	ProxyPort            uint32   `protobuf:"varint,2,opt,name=proxy_port,json=proxyPort,proto3" json:"proxy_port,omitempty"`
	ProxyIndex           uint32   `protobuf:"varint,3,opt,name=proxy_index,json=proxyIndex,proto3" json:"proxy_index,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetProxyChannelWithScoreReq) Reset()         { *m = GetProxyChannelWithScoreReq{} }
func (m *GetProxyChannelWithScoreReq) String() string { return proto.CompactTextString(m) }
func (*GetProxyChannelWithScoreReq) ProtoMessage()    {}
func (*GetProxyChannelWithScoreReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{46}
}
func (m *GetProxyChannelWithScoreReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetProxyChannelWithScoreReq.Unmarshal(m, b)
}
func (m *GetProxyChannelWithScoreReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetProxyChannelWithScoreReq.Marshal(b, m, deterministic)
}
func (dst *GetProxyChannelWithScoreReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetProxyChannelWithScoreReq.Merge(dst, src)
}
func (m *GetProxyChannelWithScoreReq) XXX_Size() int {
	return xxx_messageInfo_GetProxyChannelWithScoreReq.Size(m)
}
func (m *GetProxyChannelWithScoreReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetProxyChannelWithScoreReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetProxyChannelWithScoreReq proto.InternalMessageInfo

func (m *GetProxyChannelWithScoreReq) GetProxyIp() uint32 {
	if m != nil {
		return m.ProxyIp
	}
	return 0
}

func (m *GetProxyChannelWithScoreReq) GetProxyPort() uint32 {
	if m != nil {
		return m.ProxyPort
	}
	return 0
}

func (m *GetProxyChannelWithScoreReq) GetProxyIndex() uint32 {
	if m != nil {
		return m.ProxyIndex
	}
	return 0
}

type GetProxyChannelWithScoreResp struct {
	ChannelList          map[uint32]int64 `protobuf:"bytes,1,rep,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetProxyChannelWithScoreResp) Reset()         { *m = GetProxyChannelWithScoreResp{} }
func (m *GetProxyChannelWithScoreResp) String() string { return proto.CompactTextString(m) }
func (*GetProxyChannelWithScoreResp) ProtoMessage()    {}
func (*GetProxyChannelWithScoreResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_presence_v2_caaa956f012f450e, []int{47}
}
func (m *GetProxyChannelWithScoreResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetProxyChannelWithScoreResp.Unmarshal(m, b)
}
func (m *GetProxyChannelWithScoreResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetProxyChannelWithScoreResp.Marshal(b, m, deterministic)
}
func (dst *GetProxyChannelWithScoreResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetProxyChannelWithScoreResp.Merge(dst, src)
}
func (m *GetProxyChannelWithScoreResp) XXX_Size() int {
	return xxx_messageInfo_GetProxyChannelWithScoreResp.Size(m)
}
func (m *GetProxyChannelWithScoreResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetProxyChannelWithScoreResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetProxyChannelWithScoreResp proto.InternalMessageInfo

func (m *GetProxyChannelWithScoreResp) GetChannelList() map[uint32]int64 {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

func init() {
	proto.RegisterType((*PresKey)(nil), "Presence.PresKey")
	proto.RegisterType((*PresVal)(nil), "Presence.PresVal")
	proto.RegisterType((*PresInfo)(nil), "Presence.PresInfo")
	proto.RegisterType((*PresKeyReadable)(nil), "Presence.PresKeyReadable")
	proto.RegisterType((*PresValReadable)(nil), "Presence.PresValReadable")
	proto.RegisterType((*PresInfoReadable)(nil), "Presence.PresInfoReadable")
	proto.RegisterType((*ProxyNode)(nil), "Presence.ProxyNode")
	proto.RegisterType((*ProxyNodeStat)(nil), "Presence.ProxyNodeStat")
	proto.RegisterType((*PresInfoList)(nil), "Presence.PresInfoList")
	proto.RegisterType((*PresInfoListReadable)(nil), "Presence.PresInfoListReadable")
	proto.RegisterType((*GetUserPresReq)(nil), "Presence.GetUserPresReq")
	proto.RegisterType((*GetUserPresResp)(nil), "Presence.GetUserPresResp")
	proto.RegisterType((*GetUserPresReadableResp)(nil), "Presence.GetUserPresReadableResp")
	proto.RegisterType((*BatchGetUserPresReq)(nil), "Presence.BatchGetUserPresReq")
	proto.RegisterType((*BatchGetUserPresResp)(nil), "Presence.BatchGetUserPresResp")
	proto.RegisterMapType((map[uint32]*PresInfoList)(nil), "Presence.BatchGetUserPresResp.InfoMapEntry")
	proto.RegisterType((*BatchGetUserPresReadableResp)(nil), "Presence.BatchGetUserPresReadableResp")
	proto.RegisterMapType((map[uint32]*PresInfoListReadable)(nil), "Presence.BatchGetUserPresReadableResp.InfoMapEntry")
	proto.RegisterType((*PeekUserPresReq)(nil), "Presence.PeekUserPresReq")
	proto.RegisterType((*PeekUserPresResp)(nil), "Presence.PeekUserPresResp")
	proto.RegisterType((*BatchPeekUserPresReq)(nil), "Presence.BatchPeekUserPresReq")
	proto.RegisterType((*BatchPeekUserPresResp)(nil), "Presence.BatchPeekUserPresResp")
	proto.RegisterMapType((map[uint32]bool)(nil), "Presence.BatchPeekUserPresResp.OnlineMapEntry")
	proto.RegisterType((*SetUserPresReq)(nil), "Presence.SetUserPresReq")
	proto.RegisterType((*SetUserPresResp)(nil), "Presence.SetUserPresResp")
	proto.RegisterType((*BatchSetUserPresReq)(nil), "Presence.BatchSetUserPresReq")
	proto.RegisterMapType((map[uint32]*PresInfoList)(nil), "Presence.BatchSetUserPresReq.InfoMapEntry")
	proto.RegisterType((*BatchSetUserPresResp)(nil), "Presence.BatchSetUserPresResp")
	proto.RegisterMapType((map[uint32]bool)(nil), "Presence.BatchSetUserPresResp.ResultEntry")
	proto.RegisterType((*DelUserPresReq)(nil), "Presence.DelUserPresReq")
	proto.RegisterType((*DelUserPresResp)(nil), "Presence.DelUserPresResp")
	proto.RegisterType((*BatchDelUserPresReq)(nil), "Presence.BatchDelUserPresReq")
	proto.RegisterMapType((map[uint32]*PresInfoList)(nil), "Presence.BatchDelUserPresReq.InfoMapEntry")
	proto.RegisterType((*BatchDelUserPresResp)(nil), "Presence.BatchDelUserPresResp")
	proto.RegisterMapType((map[uint32]bool)(nil), "Presence.BatchDelUserPresResp.ResultEntry")
	proto.RegisterType((*GetProxyPresReq)(nil), "Presence.GetProxyPresReq")
	proto.RegisterType((*GetProxyPresResp)(nil), "Presence.GetProxyPresResp")
	proto.RegisterType((*GetUserOnlineCountReq)(nil), "Presence.GetUserOnlineCountReq")
	proto.RegisterType((*GetUserOnlineCountResp)(nil), "Presence.GetUserOnlineCountResp")
	proto.RegisterType((*UpdateUserPresReq)(nil), "Presence.UpdateUserPresReq")
	proto.RegisterMapType((map[uint32]*PresInfoList)(nil), "Presence.UpdateUserPresReq.InfoMapEntry")
	proto.RegisterType((*UpdateUserPresResp)(nil), "Presence.UpdateUserPresResp")
	proto.RegisterType((*UserKeepAliveReq)(nil), "Presence.UserKeepAliveReq")
	proto.RegisterMapType((map[uint32]*PresInfoList)(nil), "Presence.UserKeepAliveReq.InfoMapEntry")
	proto.RegisterType((*UserKeepAliveResp)(nil), "Presence.UserKeepAliveResp")
	proto.RegisterType((*SetChannelPresReq)(nil), "Presence.SetChannelPresReq")
	proto.RegisterType((*SetChannelPresResp)(nil), "Presence.SetChannelPresResp")
	proto.RegisterType((*BatchSetChannelPresReq)(nil), "Presence.BatchSetChannelPresReq")
	proto.RegisterType((*BatchSetChannelPresReq_Info)(nil), "Presence.BatchSetChannelPresReq.Info")
	proto.RegisterType((*BatchSetChannelPresResp)(nil), "Presence.BatchSetChannelPresResp")
	proto.RegisterType((*GetChannelProxyPresReq)(nil), "Presence.GetChannelProxyPresReq")
	proto.RegisterType((*GetChannelProxyPresResp)(nil), "Presence.GetChannelProxyPresResp")
	proto.RegisterType((*GetChannelProxyPresResp_Info)(nil), "Presence.GetChannelProxyPresResp.Info")
	proto.RegisterType((*GetProxyChannelPresReq)(nil), "Presence.GetProxyChannelPresReq")
	proto.RegisterType((*GetProxyChannelPresResp)(nil), "Presence.GetProxyChannelPresResp")
	proto.RegisterType((*GetChannelProxyWithoutIndexReq)(nil), "Presence.GetChannelProxyWithoutIndexReq")
	proto.RegisterType((*GetChannelProxyWithoutIndexResp)(nil), "Presence.GetChannelProxyWithoutIndexResp")
	proto.RegisterType((*GetChannelProxyWithoutIndexResp_Info)(nil), "Presence.GetChannelProxyWithoutIndexResp.Info")
	proto.RegisterType((*GetProxyChannelWithScoreReq)(nil), "Presence.GetProxyChannelWithScoreReq")
	proto.RegisterType((*GetProxyChannelWithScoreResp)(nil), "Presence.GetProxyChannelWithScoreResp")
	proto.RegisterMapType((map[uint32]int64)(nil), "Presence.GetProxyChannelWithScoreResp.ChannelListEntry")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// UserPresClient is the client API for UserPres service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UserPresClient interface {
	GetUserPres(ctx context.Context, in *GetUserPresReq, opts ...grpc.CallOption) (*GetUserPresResp, error)
	BatchGetUserPres(ctx context.Context, in *BatchGetUserPresReq, opts ...grpc.CallOption) (*BatchGetUserPresResp, error)
	GetUserPresReadable(ctx context.Context, in *GetUserPresReq, opts ...grpc.CallOption) (*GetUserPresReadableResp, error)
	BatchGetUserPresReadable(ctx context.Context, in *BatchGetUserPresReq, opts ...grpc.CallOption) (*BatchGetUserPresReadableResp, error)
	PeekUserPres(ctx context.Context, in *PeekUserPresReq, opts ...grpc.CallOption) (*PeekUserPresResp, error)
	BatchPeekUserPres(ctx context.Context, in *BatchPeekUserPresReq, opts ...grpc.CallOption) (*BatchPeekUserPresResp, error)
	SetUserPres(ctx context.Context, in *SetUserPresReq, opts ...grpc.CallOption) (*SetUserPresResp, error)
	BatchSetUserPres(ctx context.Context, in *BatchSetUserPresReq, opts ...grpc.CallOption) (*BatchSetUserPresResp, error)
	DelUserPres(ctx context.Context, in *DelUserPresReq, opts ...grpc.CallOption) (*DelUserPresResp, error)
	BatchDelUserPres(ctx context.Context, in *BatchDelUserPresReq, opts ...grpc.CallOption) (*BatchDelUserPresResp, error)
	GetProxyPres(ctx context.Context, in *GetProxyPresReq, opts ...grpc.CallOption) (*GetProxyPresResp, error)
	GetUserOnlineCount(ctx context.Context, in *GetUserOnlineCountReq, opts ...grpc.CallOption) (*GetUserOnlineCountResp, error)
	// 以下为兼容使用
	UpdateUserPres(ctx context.Context, in *UpdateUserPresReq, opts ...grpc.CallOption) (*UpdateUserPresResp, error)
	UserKeepAlive(ctx context.Context, in *UserKeepAliveReq, opts ...grpc.CallOption) (*UserKeepAliveResp, error)
}

type userPresClient struct {
	cc *grpc.ClientConn
}

func NewUserPresClient(cc *grpc.ClientConn) UserPresClient {
	return &userPresClient{cc}
}

func (c *userPresClient) GetUserPres(ctx context.Context, in *GetUserPresReq, opts ...grpc.CallOption) (*GetUserPresResp, error) {
	out := new(GetUserPresResp)
	err := c.cc.Invoke(ctx, "/Presence.UserPres/GetUserPres", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresClient) BatchGetUserPres(ctx context.Context, in *BatchGetUserPresReq, opts ...grpc.CallOption) (*BatchGetUserPresResp, error) {
	out := new(BatchGetUserPresResp)
	err := c.cc.Invoke(ctx, "/Presence.UserPres/BatchGetUserPres", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresClient) GetUserPresReadable(ctx context.Context, in *GetUserPresReq, opts ...grpc.CallOption) (*GetUserPresReadableResp, error) {
	out := new(GetUserPresReadableResp)
	err := c.cc.Invoke(ctx, "/Presence.UserPres/GetUserPresReadable", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresClient) BatchGetUserPresReadable(ctx context.Context, in *BatchGetUserPresReq, opts ...grpc.CallOption) (*BatchGetUserPresReadableResp, error) {
	out := new(BatchGetUserPresReadableResp)
	err := c.cc.Invoke(ctx, "/Presence.UserPres/BatchGetUserPresReadable", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresClient) PeekUserPres(ctx context.Context, in *PeekUserPresReq, opts ...grpc.CallOption) (*PeekUserPresResp, error) {
	out := new(PeekUserPresResp)
	err := c.cc.Invoke(ctx, "/Presence.UserPres/PeekUserPres", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresClient) BatchPeekUserPres(ctx context.Context, in *BatchPeekUserPresReq, opts ...grpc.CallOption) (*BatchPeekUserPresResp, error) {
	out := new(BatchPeekUserPresResp)
	err := c.cc.Invoke(ctx, "/Presence.UserPres/BatchPeekUserPres", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresClient) SetUserPres(ctx context.Context, in *SetUserPresReq, opts ...grpc.CallOption) (*SetUserPresResp, error) {
	out := new(SetUserPresResp)
	err := c.cc.Invoke(ctx, "/Presence.UserPres/SetUserPres", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresClient) BatchSetUserPres(ctx context.Context, in *BatchSetUserPresReq, opts ...grpc.CallOption) (*BatchSetUserPresResp, error) {
	out := new(BatchSetUserPresResp)
	err := c.cc.Invoke(ctx, "/Presence.UserPres/BatchSetUserPres", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresClient) DelUserPres(ctx context.Context, in *DelUserPresReq, opts ...grpc.CallOption) (*DelUserPresResp, error) {
	out := new(DelUserPresResp)
	err := c.cc.Invoke(ctx, "/Presence.UserPres/DelUserPres", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresClient) BatchDelUserPres(ctx context.Context, in *BatchDelUserPresReq, opts ...grpc.CallOption) (*BatchDelUserPresResp, error) {
	out := new(BatchDelUserPresResp)
	err := c.cc.Invoke(ctx, "/Presence.UserPres/BatchDelUserPres", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresClient) GetProxyPres(ctx context.Context, in *GetProxyPresReq, opts ...grpc.CallOption) (*GetProxyPresResp, error) {
	out := new(GetProxyPresResp)
	err := c.cc.Invoke(ctx, "/Presence.UserPres/GetProxyPres", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresClient) GetUserOnlineCount(ctx context.Context, in *GetUserOnlineCountReq, opts ...grpc.CallOption) (*GetUserOnlineCountResp, error) {
	out := new(GetUserOnlineCountResp)
	err := c.cc.Invoke(ctx, "/Presence.UserPres/GetUserOnlineCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresClient) UpdateUserPres(ctx context.Context, in *UpdateUserPresReq, opts ...grpc.CallOption) (*UpdateUserPresResp, error) {
	out := new(UpdateUserPresResp)
	err := c.cc.Invoke(ctx, "/Presence.UserPres/UpdateUserPres", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresClient) UserKeepAlive(ctx context.Context, in *UserKeepAliveReq, opts ...grpc.CallOption) (*UserKeepAliveResp, error) {
	out := new(UserKeepAliveResp)
	err := c.cc.Invoke(ctx, "/Presence.UserPres/UserKeepAlive", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserPresServer is the server API for UserPres service.
type UserPresServer interface {
	GetUserPres(context.Context, *GetUserPresReq) (*GetUserPresResp, error)
	BatchGetUserPres(context.Context, *BatchGetUserPresReq) (*BatchGetUserPresResp, error)
	GetUserPresReadable(context.Context, *GetUserPresReq) (*GetUserPresReadableResp, error)
	BatchGetUserPresReadable(context.Context, *BatchGetUserPresReq) (*BatchGetUserPresReadableResp, error)
	PeekUserPres(context.Context, *PeekUserPresReq) (*PeekUserPresResp, error)
	BatchPeekUserPres(context.Context, *BatchPeekUserPresReq) (*BatchPeekUserPresResp, error)
	SetUserPres(context.Context, *SetUserPresReq) (*SetUserPresResp, error)
	BatchSetUserPres(context.Context, *BatchSetUserPresReq) (*BatchSetUserPresResp, error)
	DelUserPres(context.Context, *DelUserPresReq) (*DelUserPresResp, error)
	BatchDelUserPres(context.Context, *BatchDelUserPresReq) (*BatchDelUserPresResp, error)
	GetProxyPres(context.Context, *GetProxyPresReq) (*GetProxyPresResp, error)
	GetUserOnlineCount(context.Context, *GetUserOnlineCountReq) (*GetUserOnlineCountResp, error)
	// 以下为兼容使用
	UpdateUserPres(context.Context, *UpdateUserPresReq) (*UpdateUserPresResp, error)
	UserKeepAlive(context.Context, *UserKeepAliveReq) (*UserKeepAliveResp, error)
}

func RegisterUserPresServer(s *grpc.Server, srv UserPresServer) {
	s.RegisterService(&_UserPres_serviceDesc, srv)
}

func _UserPres_GetUserPres_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserPresReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresServer).GetUserPres(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Presence.UserPres/GetUserPres",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresServer).GetUserPres(ctx, req.(*GetUserPresReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPres_BatchGetUserPres_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserPresReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresServer).BatchGetUserPres(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Presence.UserPres/BatchGetUserPres",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresServer).BatchGetUserPres(ctx, req.(*BatchGetUserPresReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPres_GetUserPresReadable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserPresReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresServer).GetUserPresReadable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Presence.UserPres/GetUserPresReadable",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresServer).GetUserPresReadable(ctx, req.(*GetUserPresReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPres_BatchGetUserPresReadable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserPresReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresServer).BatchGetUserPresReadable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Presence.UserPres/BatchGetUserPresReadable",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresServer).BatchGetUserPresReadable(ctx, req.(*BatchGetUserPresReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPres_PeekUserPres_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PeekUserPresReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresServer).PeekUserPres(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Presence.UserPres/PeekUserPres",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresServer).PeekUserPres(ctx, req.(*PeekUserPresReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPres_BatchPeekUserPres_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchPeekUserPresReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresServer).BatchPeekUserPres(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Presence.UserPres/BatchPeekUserPres",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresServer).BatchPeekUserPres(ctx, req.(*BatchPeekUserPresReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPres_SetUserPres_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserPresReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresServer).SetUserPres(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Presence.UserPres/SetUserPres",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresServer).SetUserPres(ctx, req.(*SetUserPresReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPres_BatchSetUserPres_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchSetUserPresReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresServer).BatchSetUserPres(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Presence.UserPres/BatchSetUserPres",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresServer).BatchSetUserPres(ctx, req.(*BatchSetUserPresReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPres_DelUserPres_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelUserPresReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresServer).DelUserPres(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Presence.UserPres/DelUserPres",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresServer).DelUserPres(ctx, req.(*DelUserPresReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPres_BatchDelUserPres_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDelUserPresReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresServer).BatchDelUserPres(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Presence.UserPres/BatchDelUserPres",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresServer).BatchDelUserPres(ctx, req.(*BatchDelUserPresReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPres_GetProxyPres_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetProxyPresReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresServer).GetProxyPres(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Presence.UserPres/GetProxyPres",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresServer).GetProxyPres(ctx, req.(*GetProxyPresReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPres_GetUserOnlineCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserOnlineCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresServer).GetUserOnlineCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Presence.UserPres/GetUserOnlineCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresServer).GetUserOnlineCount(ctx, req.(*GetUserOnlineCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPres_UpdateUserPres_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserPresReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresServer).UpdateUserPres(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Presence.UserPres/UpdateUserPres",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresServer).UpdateUserPres(ctx, req.(*UpdateUserPresReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPres_UserKeepAlive_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserKeepAliveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresServer).UserKeepAlive(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Presence.UserPres/UserKeepAlive",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresServer).UserKeepAlive(ctx, req.(*UserKeepAliveReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _UserPres_serviceDesc = grpc.ServiceDesc{
	ServiceName: "Presence.UserPres",
	HandlerType: (*UserPresServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserPres",
			Handler:    _UserPres_GetUserPres_Handler,
		},
		{
			MethodName: "BatchGetUserPres",
			Handler:    _UserPres_BatchGetUserPres_Handler,
		},
		{
			MethodName: "GetUserPresReadable",
			Handler:    _UserPres_GetUserPresReadable_Handler,
		},
		{
			MethodName: "BatchGetUserPresReadable",
			Handler:    _UserPres_BatchGetUserPresReadable_Handler,
		},
		{
			MethodName: "PeekUserPres",
			Handler:    _UserPres_PeekUserPres_Handler,
		},
		{
			MethodName: "BatchPeekUserPres",
			Handler:    _UserPres_BatchPeekUserPres_Handler,
		},
		{
			MethodName: "SetUserPres",
			Handler:    _UserPres_SetUserPres_Handler,
		},
		{
			MethodName: "BatchSetUserPres",
			Handler:    _UserPres_BatchSetUserPres_Handler,
		},
		{
			MethodName: "DelUserPres",
			Handler:    _UserPres_DelUserPres_Handler,
		},
		{
			MethodName: "BatchDelUserPres",
			Handler:    _UserPres_BatchDelUserPres_Handler,
		},
		{
			MethodName: "GetProxyPres",
			Handler:    _UserPres_GetProxyPres_Handler,
		},
		{
			MethodName: "GetUserOnlineCount",
			Handler:    _UserPres_GetUserOnlineCount_Handler,
		},
		{
			MethodName: "UpdateUserPres",
			Handler:    _UserPres_UpdateUserPres_Handler,
		},
		{
			MethodName: "UserKeepAlive",
			Handler:    _UserPres_UserKeepAlive_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "presence/presence_v2.proto",
}

// ChannelPresClient is the client API for ChannelPres service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelPresClient interface {
	SetChannelPres(ctx context.Context, in *SetChannelPresReq, opts ...grpc.CallOption) (*SetChannelPresResp, error)
	BatchSetChannelPres(ctx context.Context, in *BatchSetChannelPresReq, opts ...grpc.CallOption) (*BatchSetChannelPresResp, error)
	GetChannelProxyPres(ctx context.Context, in *GetChannelProxyPresReq, opts ...grpc.CallOption) (*GetChannelProxyPresResp, error)
	GetProxyChannelPres(ctx context.Context, in *GetProxyChannelPresReq, opts ...grpc.CallOption) (*GetProxyChannelPresResp, error)
	GetProxyChannelWithScore(ctx context.Context, in *GetProxyChannelWithScoreReq, opts ...grpc.CallOption) (*GetProxyChannelWithScoreResp, error)
	GetChannelProxyWithoutIndex(ctx context.Context, in *GetChannelProxyWithoutIndexReq, opts ...grpc.CallOption) (*GetChannelProxyWithoutIndexResp, error)
}

type channelPresClient struct {
	cc *grpc.ClientConn
}

func NewChannelPresClient(cc *grpc.ClientConn) ChannelPresClient {
	return &channelPresClient{cc}
}

func (c *channelPresClient) SetChannelPres(ctx context.Context, in *SetChannelPresReq, opts ...grpc.CallOption) (*SetChannelPresResp, error) {
	out := new(SetChannelPresResp)
	err := c.cc.Invoke(ctx, "/Presence.ChannelPres/SetChannelPres", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPresClient) BatchSetChannelPres(ctx context.Context, in *BatchSetChannelPresReq, opts ...grpc.CallOption) (*BatchSetChannelPresResp, error) {
	out := new(BatchSetChannelPresResp)
	err := c.cc.Invoke(ctx, "/Presence.ChannelPres/BatchSetChannelPres", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPresClient) GetChannelProxyPres(ctx context.Context, in *GetChannelProxyPresReq, opts ...grpc.CallOption) (*GetChannelProxyPresResp, error) {
	out := new(GetChannelProxyPresResp)
	err := c.cc.Invoke(ctx, "/Presence.ChannelPres/GetChannelProxyPres", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPresClient) GetProxyChannelPres(ctx context.Context, in *GetProxyChannelPresReq, opts ...grpc.CallOption) (*GetProxyChannelPresResp, error) {
	out := new(GetProxyChannelPresResp)
	err := c.cc.Invoke(ctx, "/Presence.ChannelPres/GetProxyChannelPres", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPresClient) GetProxyChannelWithScore(ctx context.Context, in *GetProxyChannelWithScoreReq, opts ...grpc.CallOption) (*GetProxyChannelWithScoreResp, error) {
	out := new(GetProxyChannelWithScoreResp)
	err := c.cc.Invoke(ctx, "/Presence.ChannelPres/GetProxyChannelWithScore", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPresClient) GetChannelProxyWithoutIndex(ctx context.Context, in *GetChannelProxyWithoutIndexReq, opts ...grpc.CallOption) (*GetChannelProxyWithoutIndexResp, error) {
	out := new(GetChannelProxyWithoutIndexResp)
	err := c.cc.Invoke(ctx, "/Presence.ChannelPres/GetChannelProxyWithoutIndex", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelPresServer is the server API for ChannelPres service.
type ChannelPresServer interface {
	SetChannelPres(context.Context, *SetChannelPresReq) (*SetChannelPresResp, error)
	BatchSetChannelPres(context.Context, *BatchSetChannelPresReq) (*BatchSetChannelPresResp, error)
	GetChannelProxyPres(context.Context, *GetChannelProxyPresReq) (*GetChannelProxyPresResp, error)
	GetProxyChannelPres(context.Context, *GetProxyChannelPresReq) (*GetProxyChannelPresResp, error)
	GetProxyChannelWithScore(context.Context, *GetProxyChannelWithScoreReq) (*GetProxyChannelWithScoreResp, error)
	GetChannelProxyWithoutIndex(context.Context, *GetChannelProxyWithoutIndexReq) (*GetChannelProxyWithoutIndexResp, error)
}

func RegisterChannelPresServer(s *grpc.Server, srv ChannelPresServer) {
	s.RegisterService(&_ChannelPres_serviceDesc, srv)
}

func _ChannelPres_SetChannelPres_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelPresReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPresServer).SetChannelPres(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Presence.ChannelPres/SetChannelPres",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPresServer).SetChannelPres(ctx, req.(*SetChannelPresReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPres_BatchSetChannelPres_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchSetChannelPresReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPresServer).BatchSetChannelPres(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Presence.ChannelPres/BatchSetChannelPres",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPresServer).BatchSetChannelPres(ctx, req.(*BatchSetChannelPresReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPres_GetChannelProxyPres_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelProxyPresReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPresServer).GetChannelProxyPres(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Presence.ChannelPres/GetChannelProxyPres",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPresServer).GetChannelProxyPres(ctx, req.(*GetChannelProxyPresReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPres_GetProxyChannelPres_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetProxyChannelPresReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPresServer).GetProxyChannelPres(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Presence.ChannelPres/GetProxyChannelPres",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPresServer).GetProxyChannelPres(ctx, req.(*GetProxyChannelPresReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPres_GetProxyChannelWithScore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetProxyChannelWithScoreReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPresServer).GetProxyChannelWithScore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Presence.ChannelPres/GetProxyChannelWithScore",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPresServer).GetProxyChannelWithScore(ctx, req.(*GetProxyChannelWithScoreReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPres_GetChannelProxyWithoutIndex_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelProxyWithoutIndexReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPresServer).GetChannelProxyWithoutIndex(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Presence.ChannelPres/GetChannelProxyWithoutIndex",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPresServer).GetChannelProxyWithoutIndex(ctx, req.(*GetChannelProxyWithoutIndexReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelPres_serviceDesc = grpc.ServiceDesc{
	ServiceName: "Presence.ChannelPres",
	HandlerType: (*ChannelPresServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetChannelPres",
			Handler:    _ChannelPres_SetChannelPres_Handler,
		},
		{
			MethodName: "BatchSetChannelPres",
			Handler:    _ChannelPres_BatchSetChannelPres_Handler,
		},
		{
			MethodName: "GetChannelProxyPres",
			Handler:    _ChannelPres_GetChannelProxyPres_Handler,
		},
		{
			MethodName: "GetProxyChannelPres",
			Handler:    _ChannelPres_GetProxyChannelPres_Handler,
		},
		{
			MethodName: "GetProxyChannelWithScore",
			Handler:    _ChannelPres_GetProxyChannelWithScore_Handler,
		},
		{
			MethodName: "GetChannelProxyWithoutIndex",
			Handler:    _ChannelPres_GetChannelProxyWithoutIndex_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "presence/presence_v2.proto",
}

func init() {
	proto.RegisterFile("presence/presence_v2.proto", fileDescriptor_presence_v2_caaa956f012f450e)
}

var fileDescriptor_presence_v2_caaa956f012f450e = []byte{
	// 1641 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x59, 0xcf, 0x73, 0x14, 0xc5,
	0x17, 0xdf, 0x49, 0x42, 0xb2, 0xfb, 0x36, 0x3f, 0x3b, 0x21, 0xd9, 0x4c, 0x20, 0x09, 0x4d, 0xc1,
	0x37, 0x90, 0x6f, 0x6d, 0x74, 0x41, 0x41, 0x45, 0x91, 0x04, 0xc4, 0x15, 0x03, 0xa9, 0x5d, 0x88,
	0x25, 0x65, 0x55, 0x6a, 0xd8, 0xe9, 0xc0, 0x14, 0xc3, 0xcc, 0x64, 0x7a, 0x76, 0x8b, 0x54, 0x79,
	0xf0, 0x60, 0x95, 0x27, 0x8f, 0xfe, 0x07, 0xfe, 0x05, 0x1c, 0xbd, 0x7a, 0xf3, 0x6e, 0x59, 0x96,
	0x07, 0xff, 0x0b, 0xcf, 0x56, 0xf7, 0xf4, 0xcc, 0xf6, 0xf4, 0xfc, 0xd8, 0x80, 0x10, 0x6e, 0x3b,
	0xfd, 0x5e, 0x7f, 0xde, 0xeb, 0xcf, 0x7b, 0xdd, 0xfd, 0xe9, 0x04, 0x74, 0xcf, 0x27, 0x94, 0x38,
	0x1d, 0xb2, 0x11, 0xfd, 0xd8, 0xeb, 0x35, 0xea, 0x9e, 0xef, 0x06, 0x2e, 0x2a, 0xef, 0x88, 0x21,
	0x7c, 0x08, 0x63, 0xec, 0xf7, 0x1d, 0x72, 0x88, 0x16, 0x60, 0xac, 0x4b, 0x89, 0xbf, 0x67, 0x99,
	0x35, 0x6d, 0x55, 0x5b, 0x9b, 0x68, 0x8d, 0xb2, 0xcf, 0xa6, 0x89, 0x16, 0xa1, 0xec, 0xf9, 0xee,
	0xf3, 0xc3, 0x3d, 0xcb, 0xab, 0x0d, 0x71, 0xcb, 0x18, 0xff, 0x6e, 0x7a, 0xe8, 0x34, 0x40, 0x68,
	0xf2, 0x5c, 0x3f, 0xa8, 0x0d, 0x73, 0x63, 0x85, 0x8f, 0xec, 0xb8, 0x7e, 0x80, 0x96, 0xa0, 0xd2,
	0xb1, 0x2d, 0xe2, 0x04, 0x0c, 0x74, 0x84, 0x5b, 0xcb, 0xe1, 0x40, 0xd3, 0xc4, 0xdf, 0x86, 0xa1,
	0x77, 0x0d, 0x9b, 0xf9, 0x99, 0xa4, 0x67, 0x75, 0x48, 0x14, 0x7c, 0xbc, 0x55, 0x0e, 0x07, 0x9a,
	0xa6, 0x0c, 0x12, 0xc5, 0x8f, 0x40, 0x3c, 0x66, 0x74, 0x1d, 0xdb, 0x72, 0xc8, 0x5e, 0x40, 0x45,
	0xfc, 0x72, 0x38, 0x70, 0x9f, 0x22, 0x1d, 0xca, 0x01, 0xf1, 0x9f, 0x59, 0x8e, 0x61, 0x47, 0xd1,
	0xa3, 0x6f, 0xec, 0x00, 0x27, 0xa1, 0xe9, 0xec, 0xbb, 0xe8, 0x2c, 0x0c, 0x3f, 0x25, 0x87, 0x3c,
	0x70, 0xb5, 0x31, 0x53, 0x8f, 0xc8, 0xa9, 0x0b, 0x66, 0x5a, 0xcc, 0xca, 0x9c, 0x7a, 0x86, 0xcd,
	0x13, 0x48, 0x39, 0xed, 0x1a, 0x76, 0x8b, 0x59, 0x51, 0x0d, 0xc6, 0xdc, 0xfd, 0x7d, 0x16, 0x9e,
	0x27, 0x53, 0x6e, 0x45, 0x9f, 0x78, 0x1f, 0xa6, 0x22, 0x38, 0x62, 0x98, 0xc6, 0x23, 0x9b, 0xe4,
	0x13, 0x9e, 0xa0, 0x2d, 0xb9, 0x62, 0xb3, 0x4f, 0xb9, 0x61, 0x9a, 0x3e, 0x8f, 0x52, 0x11, 0x94,
	0xdf, 0x30, 0x4d, 0x1f, 0x7f, 0xaf, 0x85, 0x81, 0x58, 0x4a, 0x51, 0xa0, 0x97, 0xa3, 0xb7, 0x52,
	0x44, 0x6f, 0xa5, 0x80, 0xde, 0x8a, 0x44, 0xaf, 0x0d, 0xd3, 0x11, 0xbd, 0x71, 0x1a, 0xeb, 0x32,
	0xcd, 0x8b, 0x69, 0x9a, 0x85, 0x5f, 0x48, 0xf7, 0xba, 0x4c, 0xf7, 0x62, 0x9a, 0xee, 0xd8, 0xb9,
	0x67, 0xd8, 0x78, 0x0b, 0x2a, 0x3b, 0x8c, 0x81, 0xbb, 0xae, 0x49, 0xd0, 0x24, 0x0c, 0x59, 0x9e,
	0x60, 0x74, 0xc8, 0xf2, 0x10, 0x82, 0x11, 0xde, 0x9d, 0x21, 0x91, 0xfc, 0x37, 0x1b, 0x7b, 0xe4,
	0xba, 0x51, 0xc7, 0xf2, 0xdf, 0xf8, 0x07, 0x0d, 0x26, 0x62, 0x94, 0x76, 0x60, 0x04, 0x89, 0xc6,
	0xd7, 0x8a, 0x1a, 0x7f, 0x48, 0x6d, 0xfc, 0xd8, 0x2c, 0x45, 0x09, 0xcd, 0x9b, 0xae, 0xcb, 0xcd,
	0x82, 0xd6, 0x8e, 0x13, 0x88, 0xd6, 0x14, 0x44, 0x6f, 0x39, 0x01, 0xbe, 0x0e, 0xe3, 0x11, 0x79,
	0x5f, 0x5a, 0x34, 0x40, 0x1b, 0x50, 0xb1, 0x9c, 0x7d, 0x77, 0xcf, 0xb6, 0x68, 0x50, 0xd3, 0x56,
	0x87, 0xd7, 0xaa, 0x0d, 0x94, 0x64, 0x84, 0xf3, 0x5c, 0xb6, 0xc4, 0x04, 0x7c, 0x0f, 0xe6, 0x64,
	0x80, 0xb8, 0x02, 0x57, 0xd2, 0x40, 0x7a, 0x06, 0x50, 0xc4, 0x6d, 0x1f, 0x10, 0xc3, 0xe4, 0x6d,
	0x12, 0x3c, 0xa0, 0xc4, 0x67, 0x4e, 0x2d, 0x72, 0x80, 0xa6, 0x61, 0xb8, 0x1b, 0x37, 0x2e, 0xfb,
	0x89, 0x3f, 0x86, 0xa9, 0x84, 0x0f, 0xf5, 0xd0, 0x45, 0x18, 0x61, 0x10, 0xa2, 0xe4, 0xf3, 0xe9,
	0x50, 0x3c, 0x3b, 0xee, 0x83, 0xb7, 0x61, 0x21, 0x31, 0x5d, 0xe4, 0xc0, 0x60, 0x1a, 0x09, 0x98,
	0xe5, 0x1c, 0x98, 0x68, 0x46, 0x08, 0xf7, 0x0e, 0xcc, 0x6e, 0x1a, 0x41, 0xe7, 0x89, 0x92, 0xf6,
	0x22, 0x94, 0xbb, 0x96, 0xd9, 0x27, 0x60, 0xa2, 0x35, 0xd6, 0xb5, 0x4c, 0xbe, 0xc6, 0x17, 0x1a,
	0xcc, 0xa5, 0xa7, 0x50, 0x0f, 0x7d, 0x06, 0x9c, 0x88, 0xbd, 0x67, 0x86, 0x27, 0x48, 0x5b, 0xef,
	0xa7, 0x90, 0x35, 0xa3, 0xce, 0x72, 0xda, 0x36, 0xbc, 0x5b, 0x4e, 0xe0, 0x1f, 0xb6, 0xc6, 0xac,
	0xf0, 0x4b, 0x6f, 0xc1, 0xb8, 0x6c, 0x60, 0x14, 0x46, 0xfb, 0x61, 0x22, 0x6c, 0xfa, 0xff, 0xc3,
	0x89, 0x9e, 0x61, 0x77, 0x89, 0x68, 0xfb, 0x3c, 0xc2, 0x42, 0xa7, 0x0f, 0x87, 0xae, 0x6a, 0xf8,
	0x37, 0x0d, 0x4e, 0xa5, 0x53, 0x90, 0xb8, 0xbb, 0x9b, 0x4a, 0xfe, 0x52, 0x51, 0xf2, 0xfd, 0x99,
	0x39, 0x8b, 0x78, 0x38, 0x70, 0x11, 0x97, 0x93, 0x8b, 0x18, 0x54, 0x2e, 0x69, 0x31, 0x67, 0x61,
	0x6a, 0x87, 0x90, 0xa7, 0xc5, 0x6d, 0x76, 0x11, 0xa6, 0x93, 0x4e, 0xd4, 0x43, 0xf3, 0x30, 0x1a,
	0xee, 0x1e, 0xee, 0x58, 0x6e, 0x89, 0x2f, 0xfc, 0xae, 0xa8, 0xa8, 0x8a, 0x5a, 0xd0, 0x05, 0x3f,
	0x6b, 0x70, 0x32, 0x63, 0x0e, 0xf5, 0xd0, 0x76, 0xbc, 0x69, 0xfb, 0x5c, 0xd6, 0x15, 0x2e, 0xd5,
	0x49, 0xf5, 0x7b, 0x7c, 0x46, 0x4c, 0xa3, 0xd8, 0xe4, 0x8c, 0xc8, 0x6b, 0x30, 0x99, 0x34, 0x66,
	0x50, 0x39, 0x27, 0x53, 0x59, 0x96, 0xa9, 0x3a, 0x84, 0xc9, 0xf6, 0x80, 0x0d, 0x89, 0x2e, 0xc0,
	0x09, 0x7e, 0xe4, 0x88, 0x42, 0xcc, 0xca, 0x85, 0x10, 0xc7, 0x5c, 0x2b, 0xf4, 0x88, 0x37, 0xea,
	0xf0, 0x11, 0x36, 0xea, 0x05, 0x98, 0x6a, 0x2b, 0x3b, 0x24, 0x8f, 0xff, 0xbf, 0x34, 0xb1, 0x0b,
	0x95, 0x5c, 0xe3, 0xcc, 0xb4, 0x81, 0x99, 0xdd, 0x92, 0xfa, 0x77, 0x88, 0x73, 0x7e, 0x51, 0xe1,
	0x3c, 0x89, 0x7d, 0x8c, 0x7b, 0xef, 0xa7, 0xe8, 0xc0, 0x50, 0xe9, 0xd8, 0x84, 0x51, 0x9f, 0xd0,
	0xae, 0x1d, 0x9d, 0xb1, 0x85, 0x19, 0x53, 0xaf, 0xde, 0xe2, 0xce, 0x61, 0xc6, 0x62, 0xa6, 0xfe,
	0x01, 0x54, 0xa5, 0xe1, 0x97, 0xed, 0x8d, 0x9b, 0xc4, 0x7e, 0x2b, 0xbd, 0xb1, 0x0e, 0x53, 0x89,
	0xd0, 0xd4, 0x93, 0x25, 0x91, 0x96, 0x94, 0x44, 0x71, 0x77, 0x28, 0xd9, 0xbe, 0xd6, 0xee, 0x48,
	0x62, 0xbf, 0x8d, 0xee, 0x50, 0x09, 0x19, 0xd4, 0x1d, 0x8a, 0xff, 0xeb, 0xee, 0x8e, 0x19, 0x7e,
	0x4d, 0x73, 0x26, 0x05, 0x29, 0xf8, 0x73, 0x98, 0x4e, 0x0e, 0x51, 0x0f, 0x5d, 0x4e, 0x4b, 0x85,
	0x85, 0x8c, 0x42, 0x30, 0x9d, 0x24, 0xe9, 0x84, 0x05, 0x38, 0x29, 0xae, 0x93, 0xf0, 0x6c, 0xdb,
	0x72, 0xbb, 0x4e, 0xc0, 0x42, 0xec, 0xc2, 0x7c, 0x96, 0x81, 0x72, 0x25, 0xc5, 0x55, 0x70, 0x87,
	0x8d, 0x88, 0x25, 0x54, 0xd8, 0x08, 0x77, 0x61, 0xe6, 0x8e, 0xeb, 0x38, 0xc2, 0x2c, 0x84, 0x16,
	0x1b, 0xe1, 0x66, 0xfc, 0x87, 0x06, 0x33, 0x0f, 0x3c, 0xd3, 0x08, 0xc8, 0x2b, 0x76, 0xd0, 0x56,
	0xaa, 0x83, 0xd6, 0xfa, 0xde, 0x29, 0xe4, 0x63, 0xec, 0x9f, 0x39, 0x40, 0x6a, 0x78, 0xea, 0xe1,
	0xdf, 0x35, 0x98, 0x66, 0x03, 0x77, 0x08, 0xf1, 0x6e, 0xd8, 0x56, 0x8f, 0xbc, 0xe4, 0x72, 0x37,
	0x53, 0xcb, 0xfd, 0x9f, 0xb4, 0x5c, 0x05, 0xf8, 0x18, 0x57, 0x3b, 0x0b, 0x33, 0x4a, 0x74, 0xea,
	0xe1, 0x1f, 0x35, 0x98, 0x69, 0x93, 0x60, 0xeb, 0x89, 0xe1, 0x38, 0xc4, 0x96, 0x2e, 0xef, 0x57,
	0x54, 0xe5, 0x2b, 0x50, 0x15, 0x33, 0x1d, 0x93, 0x3c, 0x17, 0xb2, 0x3c, 0x9c, 0xd1, 0x64, 0x23,
	0xbc, 0xd9, 0xc2, 0x60, 0xfd, 0x07, 0x6b, 0x45, 0x8c, 0x34, 0x4d, 0x56, 0x12, 0x35, 0x1d, 0xea,
	0xe1, 0xbf, 0x35, 0x98, 0x8f, 0x8e, 0x75, 0x25, 0xd5, 0xcd, 0xf4, 0x26, 0x3a, 0x97, 0xbe, 0x0b,
	0x92, 0x93, 0xea, 0x49, 0x2d, 0xaf, 0x7f, 0xa7, 0xc1, 0x08, 0x7f, 0xa5, 0xbe, 0xbd, 0x75, 0x2f,
	0xc2, 0x42, 0x66, 0xae, 0xd4, 0xc3, 0x57, 0xf8, 0xbe, 0x8e, 0x47, 0xfb, 0x87, 0x8a, 0x82, 0xa9,
	0xa9, 0x98, 0xbf, 0x6a, 0x5c, 0xef, 0xa7, 0x67, 0x52, 0x0f, 0xdd, 0x8a, 0x96, 0x23, 0xf1, 0x76,
	0xbe, 0xcf, 0x5b, 0xce, 0xb4, 0x90, 0xb8, 0x70, 0xd9, 0x9c, 0x39, 0xe3, 0x8d, 0x13, 0x87, 0x29,
	0x5f, 0x3e, 0x4f, 0xe3, 0xd8, 0xba, 0x14, 0x5f, 0xe3, 0xcc, 0xa5, 0x83, 0x52, 0x0f, 0x9d, 0x81,
	0xf1, 0x88, 0x74, 0x49, 0xdc, 0x56, 0xc5, 0x18, 0x3f, 0xa2, 0xaf, 0xc3, 0xb2, 0x42, 0xe0, 0x57,
	0x56, 0xf0, 0xc4, 0xed, 0x06, 0x1c, 0xfc, 0x08, 0x95, 0x7b, 0xa1, 0xc1, 0x4a, 0x21, 0x42, 0xa8,
	0x95, 0x53, 0x15, 0xac, 0xe7, 0x56, 0x50, 0x9d, 0x9e, 0xaa, 0xe4, 0xa7, 0xff, 0xb5, 0x92, 0xf8,
	0x39, 0x2c, 0x29, 0x9c, 0xb1, 0xa0, 0xed, 0x8e, 0xeb, 0x93, 0x37, 0x5c, 0xad, 0x5f, 0x34, 0x38,
	0x95, 0x1f, 0x9a, 0x7a, 0xe8, 0x61, 0x46, 0xcd, 0xaa, 0x8d, 0x2b, 0x09, 0xb6, 0x72, 0x67, 0xd7,
	0xb7, 0xfa, 0xa5, 0x0d, 0x8f, 0x69, 0xb9, 0xd8, 0xfa, 0x27, 0x30, 0xad, 0x3a, 0x0c, 0x12, 0x0b,
	0xc3, 0xd2, 0xb1, 0xdc, 0xf8, 0xa7, 0x0c, 0xe5, 0xe8, 0xfe, 0x41, 0x37, 0xa1, 0x2a, 0xbd, 0x15,
	0x51, 0x2d, 0x91, 0xa1, 0x74, 0x49, 0xea, 0x8b, 0x39, 0x16, 0xea, 0xe1, 0x12, 0x6a, 0xc3, 0xb4,
	0xfa, 0xec, 0x44, 0xa7, 0x8b, 0x9e, 0xa4, 0x07, 0xfa, 0x72, 0xf1, 0x73, 0x1b, 0x97, 0xd0, 0x7d,
	0x98, 0xcd, 0x78, 0xc6, 0x16, 0xa4, 0x78, 0x26, 0xc7, 0xd2, 0x7f, 0xff, 0xe2, 0x12, 0xea, 0x40,
	0x2d, 0xef, 0x85, 0x3c, 0x28, 0xe5, 0xf3, 0x47, 0x7b, 0x64, 0xe3, 0x12, 0xba, 0x0d, 0xe3, 0xf2,
	0xab, 0x11, 0xc9, 0x7f, 0xeb, 0x4a, 0x3e, 0x5b, 0x75, 0x3d, 0xcf, 0xc4, 0x81, 0x76, 0x61, 0x26,
	0xf5, 0x06, 0x45, 0xcb, 0x85, 0x0f, 0xd4, 0x03, 0x7d, 0x65, 0xc0, 0x03, 0x16, 0x97, 0x58, 0xd9,
	0xdb, 0xd9, 0x65, 0x6f, 0xe7, 0x96, 0xbd, 0x9d, 0x5b, 0xf6, 0x76, 0x41, 0xd9, 0xdb, 0xc5, 0x65,
	0x4f, 0x83, 0xde, 0x84, 0xaa, 0xa4, 0x96, 0xe5, 0xd4, 0x92, 0xb2, 0x5f, 0x4e, 0x4d, 0x91, 0xd7,
	0x52, 0x6a, 0x32, 0xd4, 0xe9, 0xc2, 0x67, 0x44, 0x2a, 0xb5, 0x34, 0xe8, 0x6d, 0x18, 0x97, 0x35,
	0x35, 0x5a, 0x4c, 0xef, 0xe7, 0x8c, 0xb2, 0xaa, 0x32, 0x1c, 0x97, 0xd0, 0xd7, 0x80, 0xd2, 0xca,
	0x19, 0xad, 0xa4, 0xfa, 0x37, 0x29, 0xb8, 0xf5, 0xd5, 0x62, 0x07, 0x0e, 0xbd, 0x0d, 0x93, 0x49,
	0x89, 0x89, 0x96, 0x0a, 0xb4, 0xaf, 0x7e, 0x2a, 0xdf, 0xc8, 0xe1, 0xbe, 0x80, 0x89, 0x84, 0x86,
	0x43, 0x7a, 0xbe, 0xb4, 0xd4, 0x97, 0x72, 0x6d, 0x0c, 0xab, 0xf1, 0xe7, 0x08, 0x54, 0xa5, 0xcb,
	0x8d, 0xa5, 0x9a, 0x54, 0x1f, 0x72, 0xaa, 0x29, 0x0d, 0x25, 0xa7, 0x9a, 0x21, 0x5a, 0x4a, 0xe8,
	0x9b, 0xfe, 0xdf, 0x25, 0x64, 0xcc, 0xd5, 0x41, 0xe2, 0x4c, 0x3e, 0x37, 0xf2, 0x24, 0x11, 0x47,
	0xcf, 0xd0, 0x28, 0x68, 0x75, 0x80, 0x84, 0x51, 0x4f, 0xa5, 0x2c, 0x91, 0x13, 0xa3, 0xab, 0xd7,
	0xbf, 0x82, 0x9e, 0x21, 0x49, 0x14, 0xf4, 0x2c, 0xfd, 0x80, 0x4b, 0xe8, 0x29, 0xd4, 0xf2, 0xee,
	0x1b, 0x74, 0xee, 0x28, 0x77, 0x52, 0xe2, 0xec, 0x2b, 0xba, 0xba, 0x70, 0x09, 0x05, 0xfc, 0x56,
	0xce, 0x93, 0x02, 0x68, 0xed, 0x88, 0x8a, 0xe1, 0x40, 0xbf, 0x70, 0x64, 0x6d, 0x81, 0x4b, 0x9b,
	0x57, 0x1f, 0xbe, 0xff, 0xd8, 0xb5, 0x0d, 0xe7, 0x71, 0xfd, 0xbd, 0x46, 0x10, 0xd4, 0x3b, 0xee,
	0xb3, 0x0d, 0xfe, 0x6f, 0xb1, 0x8e, 0x6b, 0x6f, 0x50, 0xe2, 0xf7, 0xac, 0x0e, 0xa1, 0xf1, 0x3f,
	0xcd, 0x68, 0xcf, 0xff, 0x28, 0xc2, 0x7e, 0x34, 0xca, 0xfd, 0x2e, 0xfd, 0x1b, 0x00, 0x00, 0xff,
	0xff, 0x78, 0x8b, 0xb6, 0x9b, 0x5c, 0x1b, 0x00, 0x00,
}
