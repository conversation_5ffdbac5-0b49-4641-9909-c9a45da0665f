// Code generated by protoc-gen-gogo.
// source: src/channelconvene/channelconvene.proto
// DO NOT EDIT!

/*
	Package channelconvene is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/channelconvene/channelconvene.proto

	It has these top-level messages:
		CollectChannelReq
		RemoveChannelCollectionReq
		HasCollectChannelReq
		HasCollectChannelResp
		BatchHasCollectChannelReq
		BatchHasCollectChannelResp
		GetChannelCollectionListByUidReq
		GetChannelCollectionListByUidResp
		StChannelCollectMember
		StChannelConveneMember
		StChannelConveneInfo
		GetChannelCollectionMemberListReq
		GetChannelCollectionMemberListResp
		GetChannelCollectionMemberCountReq
		GetChannelCollectionMemberCountResp
		GetChannelConveneInfoReq
		GetChannelConveneInfoResp
		ConveneChannelReq
		ConveneChannelResp
		ConveneChannelInTurnReq
		DisableConveneChannelReq
		DisableConveneChannelInTurnReq
		ConfirmChannelConveneReq
		ConfirmChannelConveneResp
		InformCreateChannelReq
		InformDismissChannelReq
		DismissChannelInTurnReq
		InformUserEnterChannelReq
		InformUserExitChannelReq
		UpdateUserConveneStatusReq
		GetConveneMemberListReq
		GetConveneMemberListResp
		GetUserConveneInfoReq
		GetUserConveneInfoResp
		GetConfirmCountByStatusReq
		GetConfirmCountByStatusResp
		ChannelConveneAsyncJobConveneChangeNotify
		ChannelConveneAsyncJobConveneInTurnNotify
		ChannelConveneAsyncJobDisableConveneInTurnNotify
		ChannelConveneAsyncJobConfirmConveneNotify
		ChannelConveneAsyncJobDismissChannelNotify
*/
package channelconvene

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// 收藏频道
type CollectChannelReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *CollectChannelReq) Reset()                    { *m = CollectChannelReq{} }
func (m *CollectChannelReq) String() string            { return proto.CompactTextString(m) }
func (*CollectChannelReq) ProtoMessage()               {}
func (*CollectChannelReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelconvene, []int{0} }

func (m *CollectChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// 取消收藏频道
type RemoveChannelCollectionReq struct {
	ChannelIdList []uint32 `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
}

func (m *RemoveChannelCollectionReq) Reset()         { *m = RemoveChannelCollectionReq{} }
func (m *RemoveChannelCollectionReq) String() string { return proto.CompactTextString(m) }
func (*RemoveChannelCollectionReq) ProtoMessage()    {}
func (*RemoveChannelCollectionReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{1}
}

func (m *RemoveChannelCollectionReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

// 是否已收藏频道
type HasCollectChannelReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *HasCollectChannelReq) Reset()         { *m = HasCollectChannelReq{} }
func (m *HasCollectChannelReq) String() string { return proto.CompactTextString(m) }
func (*HasCollectChannelReq) ProtoMessage()    {}
func (*HasCollectChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{2}
}

func (m *HasCollectChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type HasCollectChannelResp struct {
	IsCollected bool `protobuf:"varint,1,req,name=is_collected,json=isCollected" json:"is_collected"`
}

func (m *HasCollectChannelResp) Reset()         { *m = HasCollectChannelResp{} }
func (m *HasCollectChannelResp) String() string { return proto.CompactTextString(m) }
func (*HasCollectChannelResp) ProtoMessage()    {}
func (*HasCollectChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{3}
}

func (m *HasCollectChannelResp) GetIsCollected() bool {
	if m != nil {
		return m.IsCollected
	}
	return false
}

// 从指定的频道列表中获取收藏的频道
type BatchHasCollectChannelReq struct {
	ChannelIdList []uint32 `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
}

func (m *BatchHasCollectChannelReq) Reset()         { *m = BatchHasCollectChannelReq{} }
func (m *BatchHasCollectChannelReq) String() string { return proto.CompactTextString(m) }
func (*BatchHasCollectChannelReq) ProtoMessage()    {}
func (*BatchHasCollectChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{4}
}

func (m *BatchHasCollectChannelReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type BatchHasCollectChannelResp struct {
	ChannelIdList []uint32 `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
}

func (m *BatchHasCollectChannelResp) Reset()         { *m = BatchHasCollectChannelResp{} }
func (m *BatchHasCollectChannelResp) String() string { return proto.CompactTextString(m) }
func (*BatchHasCollectChannelResp) ProtoMessage()    {}
func (*BatchHasCollectChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{5}
}

func (m *BatchHasCollectChannelResp) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

// 获取用户的收藏频道列表
type GetChannelCollectionListByUidReq struct {
}

func (m *GetChannelCollectionListByUidReq) Reset()         { *m = GetChannelCollectionListByUidReq{} }
func (m *GetChannelCollectionListByUidReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelCollectionListByUidReq) ProtoMessage()    {}
func (*GetChannelCollectionListByUidReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{6}
}

type GetChannelCollectionListByUidResp struct {
	ChannelIdList []uint32 `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
}

func (m *GetChannelCollectionListByUidResp) Reset()         { *m = GetChannelCollectionListByUidResp{} }
func (m *GetChannelCollectionListByUidResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelCollectionListByUidResp) ProtoMessage()    {}
func (*GetChannelCollectionListByUidResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{7}
}

func (m *GetChannelCollectionListByUidResp) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type StChannelCollectMember struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	CollectTs uint32 `protobuf:"varint,2,req,name=collect_ts,json=collectTs" json:"collect_ts"`
}

func (m *StChannelCollectMember) Reset()         { *m = StChannelCollectMember{} }
func (m *StChannelCollectMember) String() string { return proto.CompactTextString(m) }
func (*StChannelCollectMember) ProtoMessage()    {}
func (*StChannelCollectMember) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{8}
}

func (m *StChannelCollectMember) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StChannelCollectMember) GetCollectTs() uint32 {
	if m != nil {
		return m.CollectTs
	}
	return 0
}

type StChannelConveneMember struct {
	Uid           uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ChannelId     uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	ConveneStatus uint32 `protobuf:"varint,3,opt,name=convene_status,json=conveneStatus" json:"convene_status"`
}

func (m *StChannelConveneMember) Reset()         { *m = StChannelConveneMember{} }
func (m *StChannelConveneMember) String() string { return proto.CompactTextString(m) }
func (*StChannelConveneMember) ProtoMessage()    {}
func (*StChannelConveneMember) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{9}
}

func (m *StChannelConveneMember) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StChannelConveneMember) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *StChannelConveneMember) GetConveneStatus() uint32 {
	if m != nil {
		return m.ConveneStatus
	}
	return 0
}

type StChannelConveneInfo struct {
	ChannelId          uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	LastConveneTs      uint32 `protobuf:"varint,2,opt,name=last_convene_ts,json=lastConveneTs" json:"last_convene_ts"`
	LastCancelTs       uint32 `protobuf:"varint,3,opt,name=last_cancel_ts,json=lastCancelTs" json:"last_cancel_ts"`
	ConfirmCount       uint32 `protobuf:"varint,4,opt,name=confirm_count,json=confirmCount" json:"confirm_count"`
	ConfirmCountReal   uint32 `protobuf:"varint,5,opt,name=confirm_count_real,json=confirmCountReal" json:"confirm_count_real"`
	ConveneMsg         []byte `protobuf:"bytes,6,opt,name=convene_msg,json=conveneMsg" json:"convene_msg"`
	ValidConveneTs     uint32 `protobuf:"varint,7,opt,name=valid_convene_ts,json=validConveneTs" json:"valid_convene_ts"`
	ConveneDurationMin uint32 `protobuf:"varint,8,opt,name=convene_duration_min,json=conveneDurationMin" json:"convene_duration_min"`
}

func (m *StChannelConveneInfo) Reset()         { *m = StChannelConveneInfo{} }
func (m *StChannelConveneInfo) String() string { return proto.CompactTextString(m) }
func (*StChannelConveneInfo) ProtoMessage()    {}
func (*StChannelConveneInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{10}
}

func (m *StChannelConveneInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *StChannelConveneInfo) GetLastConveneTs() uint32 {
	if m != nil {
		return m.LastConveneTs
	}
	return 0
}

func (m *StChannelConveneInfo) GetLastCancelTs() uint32 {
	if m != nil {
		return m.LastCancelTs
	}
	return 0
}

func (m *StChannelConveneInfo) GetConfirmCount() uint32 {
	if m != nil {
		return m.ConfirmCount
	}
	return 0
}

func (m *StChannelConveneInfo) GetConfirmCountReal() uint32 {
	if m != nil {
		return m.ConfirmCountReal
	}
	return 0
}

func (m *StChannelConveneInfo) GetConveneMsg() []byte {
	if m != nil {
		return m.ConveneMsg
	}
	return nil
}

func (m *StChannelConveneInfo) GetValidConveneTs() uint32 {
	if m != nil {
		return m.ValidConveneTs
	}
	return 0
}

func (m *StChannelConveneInfo) GetConveneDurationMin() uint32 {
	if m != nil {
		return m.ConveneDurationMin
	}
	return 0
}

// 获取收藏频道的用户列表
type GetChannelCollectionMemberListReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	Offset    uint32 `protobuf:"varint,2,opt,name=offset" json:"offset"`
	Limit     uint32 `protobuf:"varint,3,opt,name=limit" json:"limit"`
}

func (m *GetChannelCollectionMemberListReq) Reset()         { *m = GetChannelCollectionMemberListReq{} }
func (m *GetChannelCollectionMemberListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelCollectionMemberListReq) ProtoMessage()    {}
func (*GetChannelCollectionMemberListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{11}
}

func (m *GetChannelCollectionMemberListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelCollectionMemberListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetChannelCollectionMemberListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetChannelCollectionMemberListResp struct {
	MemberList []*StChannelCollectMember `protobuf:"bytes,1,rep,name=member_list,json=memberList" json:"member_list,omitempty"`
}

func (m *GetChannelCollectionMemberListResp) Reset()         { *m = GetChannelCollectionMemberListResp{} }
func (m *GetChannelCollectionMemberListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelCollectionMemberListResp) ProtoMessage()    {}
func (*GetChannelCollectionMemberListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{12}
}

func (m *GetChannelCollectionMemberListResp) GetMemberList() []*StChannelCollectMember {
	if m != nil {
		return m.MemberList
	}
	return nil
}

// 获取收藏频道的用户数
type GetChannelCollectionMemberCountReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *GetChannelCollectionMemberCountReq) Reset()         { *m = GetChannelCollectionMemberCountReq{} }
func (m *GetChannelCollectionMemberCountReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelCollectionMemberCountReq) ProtoMessage()    {}
func (*GetChannelCollectionMemberCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{13}
}

func (m *GetChannelCollectionMemberCountReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelCollectionMemberCountResp struct {
	MemberCount uint32 `protobuf:"varint,1,req,name=member_count,json=memberCount" json:"member_count"`
}

func (m *GetChannelCollectionMemberCountResp) Reset()         { *m = GetChannelCollectionMemberCountResp{} }
func (m *GetChannelCollectionMemberCountResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelCollectionMemberCountResp) ProtoMessage()    {}
func (*GetChannelCollectionMemberCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{14}
}

func (m *GetChannelCollectionMemberCountResp) GetMemberCount() uint32 {
	if m != nil {
		return m.MemberCount
	}
	return 0
}

// 获取频道的召集信息
type GetChannelConveneInfoReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *GetChannelConveneInfoReq) Reset()         { *m = GetChannelConveneInfoReq{} }
func (m *GetChannelConveneInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelConveneInfoReq) ProtoMessage()    {}
func (*GetChannelConveneInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{15}
}

func (m *GetChannelConveneInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelConveneInfoResp struct {
	ConveneInfo *StChannelConveneInfo `protobuf:"bytes,1,req,name=convene_info,json=conveneInfo" json:"convene_info,omitempty"`
}

func (m *GetChannelConveneInfoResp) Reset()         { *m = GetChannelConveneInfoResp{} }
func (m *GetChannelConveneInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelConveneInfoResp) ProtoMessage()    {}
func (*GetChannelConveneInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{16}
}

func (m *GetChannelConveneInfoResp) GetConveneInfo() *StChannelConveneInfo {
	if m != nil {
		return m.ConveneInfo
	}
	return nil
}

// 召集收藏频道的用户
type ConveneChannelReq struct {
	ChannelId            uint32   `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	ConveneMsg           string   `protobuf:"bytes,2,req,name=convene_msg,json=conveneMsg" json:"convene_msg"`
	ChannelOnlineMembers []uint32 `protobuf:"varint,3,rep,name=channel_online_members,json=channelOnlineMembers" json:"channel_online_members,omitempty"`
}

func (m *ConveneChannelReq) Reset()                    { *m = ConveneChannelReq{} }
func (m *ConveneChannelReq) String() string            { return proto.CompactTextString(m) }
func (*ConveneChannelReq) ProtoMessage()               {}
func (*ConveneChannelReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelconvene, []int{17} }

func (m *ConveneChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ConveneChannelReq) GetConveneMsg() string {
	if m != nil {
		return m.ConveneMsg
	}
	return ""
}

func (m *ConveneChannelReq) GetChannelOnlineMembers() []uint32 {
	if m != nil {
		return m.ChannelOnlineMembers
	}
	return nil
}

type ConveneChannelResp struct {
	ConveneInfo *StChannelConveneInfo `protobuf:"bytes,1,req,name=convene_info,json=conveneInfo" json:"convene_info,omitempty"`
}

func (m *ConveneChannelResp) Reset()         { *m = ConveneChannelResp{} }
func (m *ConveneChannelResp) String() string { return proto.CompactTextString(m) }
func (*ConveneChannelResp) ProtoMessage()    {}
func (*ConveneChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{18}
}

func (m *ConveneChannelResp) GetConveneInfo() *StChannelConveneInfo {
	if m != nil {
		return m.ConveneInfo
	}
	return nil
}

// 召集收藏频道的用户(异步队列的专用接口)
type ConveneChannelInTurnReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	Offset    uint32 `protobuf:"varint,2,req,name=offset" json:"offset"`
	Version   uint32 `protobuf:"varint,3,opt,name=version" json:"version"`
}

func (m *ConveneChannelInTurnReq) Reset()         { *m = ConveneChannelInTurnReq{} }
func (m *ConveneChannelInTurnReq) String() string { return proto.CompactTextString(m) }
func (*ConveneChannelInTurnReq) ProtoMessage()    {}
func (*ConveneChannelInTurnReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{19}
}

func (m *ConveneChannelInTurnReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ConveneChannelInTurnReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *ConveneChannelInTurnReq) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

// 取消召集
type DisableConveneChannelReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *DisableConveneChannelReq) Reset()         { *m = DisableConveneChannelReq{} }
func (m *DisableConveneChannelReq) String() string { return proto.CompactTextString(m) }
func (*DisableConveneChannelReq) ProtoMessage()    {}
func (*DisableConveneChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{20}
}

func (m *DisableConveneChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// 取消召集(异步队列的专用接口)
type DisableConveneChannelInTurnReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	Offset    uint32 `protobuf:"varint,2,req,name=offset" json:"offset"`
	Status    uint32 `protobuf:"varint,3,req,name=status" json:"status"`
}

func (m *DisableConveneChannelInTurnReq) Reset()         { *m = DisableConveneChannelInTurnReq{} }
func (m *DisableConveneChannelInTurnReq) String() string { return proto.CompactTextString(m) }
func (*DisableConveneChannelInTurnReq) ProtoMessage()    {}
func (*DisableConveneChannelInTurnReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{21}
}

func (m *DisableConveneChannelInTurnReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *DisableConveneChannelInTurnReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *DisableConveneChannelInTurnReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

// 用户响应频道召集
type ConfirmChannelConveneReq struct {
}

func (m *ConfirmChannelConveneReq) Reset()         { *m = ConfirmChannelConveneReq{} }
func (m *ConfirmChannelConveneReq) String() string { return proto.CompactTextString(m) }
func (*ConfirmChannelConveneReq) ProtoMessage()    {}
func (*ConfirmChannelConveneReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{22}
}

type ConfirmChannelConveneResp struct {
	ChannelList []*StChannelConveneInfo `protobuf:"bytes,1,rep,name=channel_list,json=channelList" json:"channel_list,omitempty"`
}

func (m *ConfirmChannelConveneResp) Reset()         { *m = ConfirmChannelConveneResp{} }
func (m *ConfirmChannelConveneResp) String() string { return proto.CompactTextString(m) }
func (*ConfirmChannelConveneResp) ProtoMessage()    {}
func (*ConfirmChannelConveneResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{23}
}

func (m *ConfirmChannelConveneResp) GetChannelList() []*StChannelConveneInfo {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

// 通知创建频道
type InformCreateChannelReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *InformCreateChannelReq) Reset()         { *m = InformCreateChannelReq{} }
func (m *InformCreateChannelReq) String() string { return proto.CompactTextString(m) }
func (*InformCreateChannelReq) ProtoMessage()    {}
func (*InformCreateChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{24}
}

func (m *InformCreateChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// 通知解散频道
type InformDismissChannelReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *InformDismissChannelReq) Reset()         { *m = InformDismissChannelReq{} }
func (m *InformDismissChannelReq) String() string { return proto.CompactTextString(m) }
func (*InformDismissChannelReq) ProtoMessage()    {}
func (*InformDismissChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{25}
}

func (m *InformDismissChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// 解散频道(异步队列的专用接口)
type DismissChannelInTurnReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	Offset    uint32 `protobuf:"varint,2,req,name=offset" json:"offset"`
}

func (m *DismissChannelInTurnReq) Reset()         { *m = DismissChannelInTurnReq{} }
func (m *DismissChannelInTurnReq) String() string { return proto.CompactTextString(m) }
func (*DismissChannelInTurnReq) ProtoMessage()    {}
func (*DismissChannelInTurnReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{26}
}

func (m *DismissChannelInTurnReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *DismissChannelInTurnReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

// 通知用户已进入频道
type InformUserEnterChannelReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *InformUserEnterChannelReq) Reset()         { *m = InformUserEnterChannelReq{} }
func (m *InformUserEnterChannelReq) String() string { return proto.CompactTextString(m) }
func (*InformUserEnterChannelReq) ProtoMessage()    {}
func (*InformUserEnterChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{27}
}

func (m *InformUserEnterChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// 通知用户已退出频道
type InformUserExitChannelReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *InformUserExitChannelReq) Reset()         { *m = InformUserExitChannelReq{} }
func (m *InformUserExitChannelReq) String() string { return proto.CompactTextString(m) }
func (*InformUserExitChannelReq) ProtoMessage()    {}
func (*InformUserExitChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{28}
}

func (m *InformUserExitChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// 更新用户的召集状态
type UpdateUserConveneStatusReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	Status    uint32 `protobuf:"varint,2,req,name=status" json:"status"`
}

func (m *UpdateUserConveneStatusReq) Reset()         { *m = UpdateUserConveneStatusReq{} }
func (m *UpdateUserConveneStatusReq) String() string { return proto.CompactTextString(m) }
func (*UpdateUserConveneStatusReq) ProtoMessage()    {}
func (*UpdateUserConveneStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{29}
}

func (m *UpdateUserConveneStatusReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UpdateUserConveneStatusReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

// 获取响应的召集成员列表
type GetConveneMemberListReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *GetConveneMemberListReq) Reset()         { *m = GetConveneMemberListReq{} }
func (m *GetConveneMemberListReq) String() string { return proto.CompactTextString(m) }
func (*GetConveneMemberListReq) ProtoMessage()    {}
func (*GetConveneMemberListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{30}
}

func (m *GetConveneMemberListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetConveneMemberListResp struct {
	MemberList []*StChannelConveneMember `protobuf:"bytes,1,rep,name=member_list,json=memberList" json:"member_list,omitempty"`
}

func (m *GetConveneMemberListResp) Reset()         { *m = GetConveneMemberListResp{} }
func (m *GetConveneMemberListResp) String() string { return proto.CompactTextString(m) }
func (*GetConveneMemberListResp) ProtoMessage()    {}
func (*GetConveneMemberListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{31}
}

func (m *GetConveneMemberListResp) GetMemberList() []*StChannelConveneMember {
	if m != nil {
		return m.MemberList
	}
	return nil
}

// 获取用户的频道召集响应信息
type GetUserConveneInfoReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *GetUserConveneInfoReq) Reset()         { *m = GetUserConveneInfoReq{} }
func (m *GetUserConveneInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserConveneInfoReq) ProtoMessage()    {}
func (*GetUserConveneInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{32}
}

func (m *GetUserConveneInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetUserConveneInfoResp struct {
	MemberInfo *StChannelConveneMember `protobuf:"bytes,1,opt,name=member_info,json=memberInfo" json:"member_info,omitempty"`
}

func (m *GetUserConveneInfoResp) Reset()         { *m = GetUserConveneInfoResp{} }
func (m *GetUserConveneInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserConveneInfoResp) ProtoMessage()    {}
func (*GetUserConveneInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{33}
}

func (m *GetUserConveneInfoResp) GetMemberInfo() *StChannelConveneMember {
	if m != nil {
		return m.MemberInfo
	}
	return nil
}

// 获取指定用户响应状态的用户数量
type GetConfirmCountByStatusReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	Status    uint32 `protobuf:"varint,2,req,name=status" json:"status"`
}

func (m *GetConfirmCountByStatusReq) Reset()         { *m = GetConfirmCountByStatusReq{} }
func (m *GetConfirmCountByStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetConfirmCountByStatusReq) ProtoMessage()    {}
func (*GetConfirmCountByStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{34}
}

func (m *GetConfirmCountByStatusReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetConfirmCountByStatusReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type GetConfirmCountByStatusResp struct {
	MemberCount uint32 `protobuf:"varint,1,opt,name=member_count,json=memberCount" json:"member_count"`
}

func (m *GetConfirmCountByStatusResp) Reset()         { *m = GetConfirmCountByStatusResp{} }
func (m *GetConfirmCountByStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetConfirmCountByStatusResp) ProtoMessage()    {}
func (*GetConfirmCountByStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{35}
}

func (m *GetConfirmCountByStatusResp) GetMemberCount() uint32 {
	if m != nil {
		return m.MemberCount
	}
	return 0
}

// ******** asyncjob notify ********
type ChannelConveneAsyncJobConveneChangeNotify struct {
	OptUid      uint32                `protobuf:"varint,1,req,name=opt_uid,json=optUid" json:"opt_uid"`
	ConveneInfo *StChannelConveneInfo `protobuf:"bytes,2,req,name=convene_info,json=conveneInfo" json:"convene_info,omitempty"`
}

func (m *ChannelConveneAsyncJobConveneChangeNotify) Reset() {
	*m = ChannelConveneAsyncJobConveneChangeNotify{}
}
func (m *ChannelConveneAsyncJobConveneChangeNotify) String() string { return proto.CompactTextString(m) }
func (*ChannelConveneAsyncJobConveneChangeNotify) ProtoMessage()    {}
func (*ChannelConveneAsyncJobConveneChangeNotify) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{36}
}

func (m *ChannelConveneAsyncJobConveneChangeNotify) GetOptUid() uint32 {
	if m != nil {
		return m.OptUid
	}
	return 0
}

func (m *ChannelConveneAsyncJobConveneChangeNotify) GetConveneInfo() *StChannelConveneInfo {
	if m != nil {
		return m.ConveneInfo
	}
	return nil
}

type ChannelConveneAsyncJobConveneInTurnNotify struct {
	OptUid        uint32   `protobuf:"varint,1,req,name=opt_uid,json=optUid" json:"opt_uid"`
	ChannelId     uint32   `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	ConveneTs     uint32   `protobuf:"varint,3,req,name=convene_ts,json=conveneTs" json:"convene_ts"`
	Offset        uint32   `protobuf:"varint,4,req,name=offset" json:"offset"`
	NotifyUidList []uint32 `protobuf:"varint,5,rep,name=notify_uid_list,json=notifyUidList" json:"notify_uid_list,omitempty"`
	Version       uint32   `protobuf:"varint,6,opt,name=version" json:"version"`
}

func (m *ChannelConveneAsyncJobConveneInTurnNotify) Reset() {
	*m = ChannelConveneAsyncJobConveneInTurnNotify{}
}
func (m *ChannelConveneAsyncJobConveneInTurnNotify) String() string { return proto.CompactTextString(m) }
func (*ChannelConveneAsyncJobConveneInTurnNotify) ProtoMessage()    {}
func (*ChannelConveneAsyncJobConveneInTurnNotify) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{37}
}

func (m *ChannelConveneAsyncJobConveneInTurnNotify) GetOptUid() uint32 {
	if m != nil {
		return m.OptUid
	}
	return 0
}

func (m *ChannelConveneAsyncJobConveneInTurnNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelConveneAsyncJobConveneInTurnNotify) GetConveneTs() uint32 {
	if m != nil {
		return m.ConveneTs
	}
	return 0
}

func (m *ChannelConveneAsyncJobConveneInTurnNotify) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *ChannelConveneAsyncJobConveneInTurnNotify) GetNotifyUidList() []uint32 {
	if m != nil {
		return m.NotifyUidList
	}
	return nil
}

func (m *ChannelConveneAsyncJobConveneInTurnNotify) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

type ChannelConveneAsyncJobDisableConveneInTurnNotify struct {
	OptUid        uint32   `protobuf:"varint,1,req,name=opt_uid,json=optUid" json:"opt_uid"`
	ChannelId     uint32   `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	Status        uint32   `protobuf:"varint,3,req,name=status" json:"status"`
	Offset        uint32   `protobuf:"varint,4,req,name=offset" json:"offset"`
	NotifyUidList []uint32 `protobuf:"varint,5,rep,name=notify_uid_list,json=notifyUidList" json:"notify_uid_list,omitempty"`
}

func (m *ChannelConveneAsyncJobDisableConveneInTurnNotify) Reset() {
	*m = ChannelConveneAsyncJobDisableConveneInTurnNotify{}
}
func (m *ChannelConveneAsyncJobDisableConveneInTurnNotify) String() string {
	return proto.CompactTextString(m)
}
func (*ChannelConveneAsyncJobDisableConveneInTurnNotify) ProtoMessage() {}
func (*ChannelConveneAsyncJobDisableConveneInTurnNotify) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{38}
}

func (m *ChannelConveneAsyncJobDisableConveneInTurnNotify) GetOptUid() uint32 {
	if m != nil {
		return m.OptUid
	}
	return 0
}

func (m *ChannelConveneAsyncJobDisableConveneInTurnNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelConveneAsyncJobDisableConveneInTurnNotify) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *ChannelConveneAsyncJobDisableConveneInTurnNotify) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *ChannelConveneAsyncJobDisableConveneInTurnNotify) GetNotifyUidList() []uint32 {
	if m != nil {
		return m.NotifyUidList
	}
	return nil
}

type ChannelConveneAsyncJobConfirmConveneNotify struct {
	Uid         uint32                  `protobuf:"varint,1,req,name=uid" json:"uid"`
	ChannelList []*StChannelConveneInfo `protobuf:"bytes,2,rep,name=channel_list,json=channelList" json:"channel_list,omitempty"`
}

func (m *ChannelConveneAsyncJobConfirmConveneNotify) Reset() {
	*m = ChannelConveneAsyncJobConfirmConveneNotify{}
}
func (m *ChannelConveneAsyncJobConfirmConveneNotify) String() string {
	return proto.CompactTextString(m)
}
func (*ChannelConveneAsyncJobConfirmConveneNotify) ProtoMessage() {}
func (*ChannelConveneAsyncJobConfirmConveneNotify) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{39}
}

func (m *ChannelConveneAsyncJobConfirmConveneNotify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelConveneAsyncJobConfirmConveneNotify) GetChannelList() []*StChannelConveneInfo {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

type ChannelConveneAsyncJobDismissChannelNotify struct {
	OptUid    uint32 `protobuf:"varint,1,req,name=opt_uid,json=optUid" json:"opt_uid"`
	ChannelId uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	Offset    uint32 `protobuf:"varint,3,req,name=offset" json:"offset"`
}

func (m *ChannelConveneAsyncJobDismissChannelNotify) Reset() {
	*m = ChannelConveneAsyncJobDismissChannelNotify{}
}
func (m *ChannelConveneAsyncJobDismissChannelNotify) String() string {
	return proto.CompactTextString(m)
}
func (*ChannelConveneAsyncJobDismissChannelNotify) ProtoMessage() {}
func (*ChannelConveneAsyncJobDismissChannelNotify) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelconvene, []int{40}
}

func (m *ChannelConveneAsyncJobDismissChannelNotify) GetOptUid() uint32 {
	if m != nil {
		return m.OptUid
	}
	return 0
}

func (m *ChannelConveneAsyncJobDismissChannelNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelConveneAsyncJobDismissChannelNotify) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func init() {
	proto.RegisterType((*CollectChannelReq)(nil), "channelconvene.CollectChannelReq")
	proto.RegisterType((*RemoveChannelCollectionReq)(nil), "channelconvene.RemoveChannelCollectionReq")
	proto.RegisterType((*HasCollectChannelReq)(nil), "channelconvene.HasCollectChannelReq")
	proto.RegisterType((*HasCollectChannelResp)(nil), "channelconvene.HasCollectChannelResp")
	proto.RegisterType((*BatchHasCollectChannelReq)(nil), "channelconvene.BatchHasCollectChannelReq")
	proto.RegisterType((*BatchHasCollectChannelResp)(nil), "channelconvene.BatchHasCollectChannelResp")
	proto.RegisterType((*GetChannelCollectionListByUidReq)(nil), "channelconvene.GetChannelCollectionListByUidReq")
	proto.RegisterType((*GetChannelCollectionListByUidResp)(nil), "channelconvene.GetChannelCollectionListByUidResp")
	proto.RegisterType((*StChannelCollectMember)(nil), "channelconvene.StChannelCollectMember")
	proto.RegisterType((*StChannelConveneMember)(nil), "channelconvene.StChannelConveneMember")
	proto.RegisterType((*StChannelConveneInfo)(nil), "channelconvene.StChannelConveneInfo")
	proto.RegisterType((*GetChannelCollectionMemberListReq)(nil), "channelconvene.GetChannelCollectionMemberListReq")
	proto.RegisterType((*GetChannelCollectionMemberListResp)(nil), "channelconvene.GetChannelCollectionMemberListResp")
	proto.RegisterType((*GetChannelCollectionMemberCountReq)(nil), "channelconvene.GetChannelCollectionMemberCountReq")
	proto.RegisterType((*GetChannelCollectionMemberCountResp)(nil), "channelconvene.GetChannelCollectionMemberCountResp")
	proto.RegisterType((*GetChannelConveneInfoReq)(nil), "channelconvene.GetChannelConveneInfoReq")
	proto.RegisterType((*GetChannelConveneInfoResp)(nil), "channelconvene.GetChannelConveneInfoResp")
	proto.RegisterType((*ConveneChannelReq)(nil), "channelconvene.ConveneChannelReq")
	proto.RegisterType((*ConveneChannelResp)(nil), "channelconvene.ConveneChannelResp")
	proto.RegisterType((*ConveneChannelInTurnReq)(nil), "channelconvene.ConveneChannelInTurnReq")
	proto.RegisterType((*DisableConveneChannelReq)(nil), "channelconvene.DisableConveneChannelReq")
	proto.RegisterType((*DisableConveneChannelInTurnReq)(nil), "channelconvene.DisableConveneChannelInTurnReq")
	proto.RegisterType((*ConfirmChannelConveneReq)(nil), "channelconvene.ConfirmChannelConveneReq")
	proto.RegisterType((*ConfirmChannelConveneResp)(nil), "channelconvene.ConfirmChannelConveneResp")
	proto.RegisterType((*InformCreateChannelReq)(nil), "channelconvene.InformCreateChannelReq")
	proto.RegisterType((*InformDismissChannelReq)(nil), "channelconvene.InformDismissChannelReq")
	proto.RegisterType((*DismissChannelInTurnReq)(nil), "channelconvene.DismissChannelInTurnReq")
	proto.RegisterType((*InformUserEnterChannelReq)(nil), "channelconvene.InformUserEnterChannelReq")
	proto.RegisterType((*InformUserExitChannelReq)(nil), "channelconvene.InformUserExitChannelReq")
	proto.RegisterType((*UpdateUserConveneStatusReq)(nil), "channelconvene.UpdateUserConveneStatusReq")
	proto.RegisterType((*GetConveneMemberListReq)(nil), "channelconvene.GetConveneMemberListReq")
	proto.RegisterType((*GetConveneMemberListResp)(nil), "channelconvene.GetConveneMemberListResp")
	proto.RegisterType((*GetUserConveneInfoReq)(nil), "channelconvene.GetUserConveneInfoReq")
	proto.RegisterType((*GetUserConveneInfoResp)(nil), "channelconvene.GetUserConveneInfoResp")
	proto.RegisterType((*GetConfirmCountByStatusReq)(nil), "channelconvene.GetConfirmCountByStatusReq")
	proto.RegisterType((*GetConfirmCountByStatusResp)(nil), "channelconvene.GetConfirmCountByStatusResp")
	proto.RegisterType((*ChannelConveneAsyncJobConveneChangeNotify)(nil), "channelconvene.ChannelConveneAsyncJobConveneChangeNotify")
	proto.RegisterType((*ChannelConveneAsyncJobConveneInTurnNotify)(nil), "channelconvene.ChannelConveneAsyncJobConveneInTurnNotify")
	proto.RegisterType((*ChannelConveneAsyncJobDisableConveneInTurnNotify)(nil), "channelconvene.ChannelConveneAsyncJobDisableConveneInTurnNotify")
	proto.RegisterType((*ChannelConveneAsyncJobConfirmConveneNotify)(nil), "channelconvene.ChannelConveneAsyncJobConfirmConveneNotify")
	proto.RegisterType((*ChannelConveneAsyncJobDismissChannelNotify)(nil), "channelconvene.ChannelConveneAsyncJobDismissChannelNotify")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for ChannelConvene service

type ChannelConveneClient interface {
	CollectChannel(ctx context.Context, in *CollectChannelReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	RemoveChannelCollection(ctx context.Context, in *RemoveChannelCollectionReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetChannelCollectionListByUid(ctx context.Context, in *GetChannelCollectionListByUidReq, opts ...grpc.CallOption) (*GetChannelCollectionListByUidResp, error)
	GetChannelCollectionMemberList(ctx context.Context, in *GetChannelCollectionMemberListReq, opts ...grpc.CallOption) (*GetChannelCollectionMemberListResp, error)
	GetChannelConveneInfo(ctx context.Context, in *GetChannelConveneInfoReq, opts ...grpc.CallOption) (*GetChannelConveneInfoResp, error)
	ConveneChannel(ctx context.Context, in *ConveneChannelReq, opts ...grpc.CallOption) (*ConveneChannelResp, error)
	ConveneChannelInTurn(ctx context.Context, in *ConveneChannelInTurnReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	DisableConveneChannel(ctx context.Context, in *DisableConveneChannelReq, opts ...grpc.CallOption) (*GetChannelConveneInfoResp, error)
	DisableConveneChannelInTurn(ctx context.Context, in *DisableConveneChannelInTurnReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	InformCreateChannel(ctx context.Context, in *InformCreateChannelReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	InformDismissChannel(ctx context.Context, in *InformDismissChannelReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	ConfirmChannelConvene(ctx context.Context, in *ConfirmChannelConveneReq, opts ...grpc.CallOption) (*ConfirmChannelConveneResp, error)
	InformUserEnterChannel(ctx context.Context, in *InformUserEnterChannelReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	InformUserExitChannel(ctx context.Context, in *InformUserExitChannelReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	UpdateUserConveneStatus(ctx context.Context, in *UpdateUserConveneStatusReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	HasCollectChannel(ctx context.Context, in *HasCollectChannelReq, opts ...grpc.CallOption) (*HasCollectChannelResp, error)
	GetConveneMemberList(ctx context.Context, in *GetConveneMemberListReq, opts ...grpc.CallOption) (*GetConveneMemberListResp, error)
	GetUserConveneInfo(ctx context.Context, in *GetUserConveneInfoReq, opts ...grpc.CallOption) (*GetUserConveneInfoResp, error)
	GetConfirmCountByStatus(ctx context.Context, in *GetConfirmCountByStatusReq, opts ...grpc.CallOption) (*GetConfirmCountByStatusResp, error)
	DismissChannelInTurn(ctx context.Context, in *DismissChannelInTurnReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetChannelCollectionMemberCount(ctx context.Context, in *GetChannelCollectionMemberCountReq, opts ...grpc.CallOption) (*GetChannelCollectionMemberCountResp, error)
	BatchHasCollectChannel(ctx context.Context, in *BatchHasCollectChannelReq, opts ...grpc.CallOption) (*BatchHasCollectChannelResp, error)
}

type channelConveneClient struct {
	cc *grpc.ClientConn
}

func NewChannelConveneClient(cc *grpc.ClientConn) ChannelConveneClient {
	return &channelConveneClient{cc}
}

func (c *channelConveneClient) CollectChannel(ctx context.Context, in *CollectChannelReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/channelconvene.ChannelConvene/CollectChannel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) RemoveChannelCollection(ctx context.Context, in *RemoveChannelCollectionReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/channelconvene.ChannelConvene/RemoveChannelCollection", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) GetChannelCollectionListByUid(ctx context.Context, in *GetChannelCollectionListByUidReq, opts ...grpc.CallOption) (*GetChannelCollectionListByUidResp, error) {
	out := new(GetChannelCollectionListByUidResp)
	err := grpc.Invoke(ctx, "/channelconvene.ChannelConvene/GetChannelCollectionListByUid", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) GetChannelCollectionMemberList(ctx context.Context, in *GetChannelCollectionMemberListReq, opts ...grpc.CallOption) (*GetChannelCollectionMemberListResp, error) {
	out := new(GetChannelCollectionMemberListResp)
	err := grpc.Invoke(ctx, "/channelconvene.ChannelConvene/GetChannelCollectionMemberList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) GetChannelConveneInfo(ctx context.Context, in *GetChannelConveneInfoReq, opts ...grpc.CallOption) (*GetChannelConveneInfoResp, error) {
	out := new(GetChannelConveneInfoResp)
	err := grpc.Invoke(ctx, "/channelconvene.ChannelConvene/GetChannelConveneInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) ConveneChannel(ctx context.Context, in *ConveneChannelReq, opts ...grpc.CallOption) (*ConveneChannelResp, error) {
	out := new(ConveneChannelResp)
	err := grpc.Invoke(ctx, "/channelconvene.ChannelConvene/ConveneChannel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) ConveneChannelInTurn(ctx context.Context, in *ConveneChannelInTurnReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/channelconvene.ChannelConvene/ConveneChannelInTurn", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) DisableConveneChannel(ctx context.Context, in *DisableConveneChannelReq, opts ...grpc.CallOption) (*GetChannelConveneInfoResp, error) {
	out := new(GetChannelConveneInfoResp)
	err := grpc.Invoke(ctx, "/channelconvene.ChannelConvene/DisableConveneChannel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) DisableConveneChannelInTurn(ctx context.Context, in *DisableConveneChannelInTurnReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/channelconvene.ChannelConvene/DisableConveneChannelInTurn", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) InformCreateChannel(ctx context.Context, in *InformCreateChannelReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/channelconvene.ChannelConvene/InformCreateChannel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) InformDismissChannel(ctx context.Context, in *InformDismissChannelReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/channelconvene.ChannelConvene/InformDismissChannel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) ConfirmChannelConvene(ctx context.Context, in *ConfirmChannelConveneReq, opts ...grpc.CallOption) (*ConfirmChannelConveneResp, error) {
	out := new(ConfirmChannelConveneResp)
	err := grpc.Invoke(ctx, "/channelconvene.ChannelConvene/ConfirmChannelConvene", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) InformUserEnterChannel(ctx context.Context, in *InformUserEnterChannelReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/channelconvene.ChannelConvene/InformUserEnterChannel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) InformUserExitChannel(ctx context.Context, in *InformUserExitChannelReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/channelconvene.ChannelConvene/InformUserExitChannel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) UpdateUserConveneStatus(ctx context.Context, in *UpdateUserConveneStatusReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/channelconvene.ChannelConvene/UpdateUserConveneStatus", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) HasCollectChannel(ctx context.Context, in *HasCollectChannelReq, opts ...grpc.CallOption) (*HasCollectChannelResp, error) {
	out := new(HasCollectChannelResp)
	err := grpc.Invoke(ctx, "/channelconvene.ChannelConvene/HasCollectChannel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) GetConveneMemberList(ctx context.Context, in *GetConveneMemberListReq, opts ...grpc.CallOption) (*GetConveneMemberListResp, error) {
	out := new(GetConveneMemberListResp)
	err := grpc.Invoke(ctx, "/channelconvene.ChannelConvene/GetConveneMemberList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) GetUserConveneInfo(ctx context.Context, in *GetUserConveneInfoReq, opts ...grpc.CallOption) (*GetUserConveneInfoResp, error) {
	out := new(GetUserConveneInfoResp)
	err := grpc.Invoke(ctx, "/channelconvene.ChannelConvene/GetUserConveneInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) GetConfirmCountByStatus(ctx context.Context, in *GetConfirmCountByStatusReq, opts ...grpc.CallOption) (*GetConfirmCountByStatusResp, error) {
	out := new(GetConfirmCountByStatusResp)
	err := grpc.Invoke(ctx, "/channelconvene.ChannelConvene/GetConfirmCountByStatus", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) DismissChannelInTurn(ctx context.Context, in *DismissChannelInTurnReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/channelconvene.ChannelConvene/DismissChannelInTurn", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) GetChannelCollectionMemberCount(ctx context.Context, in *GetChannelCollectionMemberCountReq, opts ...grpc.CallOption) (*GetChannelCollectionMemberCountResp, error) {
	out := new(GetChannelCollectionMemberCountResp)
	err := grpc.Invoke(ctx, "/channelconvene.ChannelConvene/GetChannelCollectionMemberCount", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) BatchHasCollectChannel(ctx context.Context, in *BatchHasCollectChannelReq, opts ...grpc.CallOption) (*BatchHasCollectChannelResp, error) {
	out := new(BatchHasCollectChannelResp)
	err := grpc.Invoke(ctx, "/channelconvene.ChannelConvene/BatchHasCollectChannel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for ChannelConvene service

type ChannelConveneServer interface {
	CollectChannel(context.Context, *CollectChannelReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	RemoveChannelCollection(context.Context, *RemoveChannelCollectionReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetChannelCollectionListByUid(context.Context, *GetChannelCollectionListByUidReq) (*GetChannelCollectionListByUidResp, error)
	GetChannelCollectionMemberList(context.Context, *GetChannelCollectionMemberListReq) (*GetChannelCollectionMemberListResp, error)
	GetChannelConveneInfo(context.Context, *GetChannelConveneInfoReq) (*GetChannelConveneInfoResp, error)
	ConveneChannel(context.Context, *ConveneChannelReq) (*ConveneChannelResp, error)
	ConveneChannelInTurn(context.Context, *ConveneChannelInTurnReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	DisableConveneChannel(context.Context, *DisableConveneChannelReq) (*GetChannelConveneInfoResp, error)
	DisableConveneChannelInTurn(context.Context, *DisableConveneChannelInTurnReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	InformCreateChannel(context.Context, *InformCreateChannelReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	InformDismissChannel(context.Context, *InformDismissChannelReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	ConfirmChannelConvene(context.Context, *ConfirmChannelConveneReq) (*ConfirmChannelConveneResp, error)
	InformUserEnterChannel(context.Context, *InformUserEnterChannelReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	InformUserExitChannel(context.Context, *InformUserExitChannelReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	UpdateUserConveneStatus(context.Context, *UpdateUserConveneStatusReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	HasCollectChannel(context.Context, *HasCollectChannelReq) (*HasCollectChannelResp, error)
	GetConveneMemberList(context.Context, *GetConveneMemberListReq) (*GetConveneMemberListResp, error)
	GetUserConveneInfo(context.Context, *GetUserConveneInfoReq) (*GetUserConveneInfoResp, error)
	GetConfirmCountByStatus(context.Context, *GetConfirmCountByStatusReq) (*GetConfirmCountByStatusResp, error)
	DismissChannelInTurn(context.Context, *DismissChannelInTurnReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetChannelCollectionMemberCount(context.Context, *GetChannelCollectionMemberCountReq) (*GetChannelCollectionMemberCountResp, error)
	BatchHasCollectChannel(context.Context, *BatchHasCollectChannelReq) (*BatchHasCollectChannelResp, error)
}

func RegisterChannelConveneServer(s *grpc.Server, srv ChannelConveneServer) {
	s.RegisterService(&_ChannelConvene_serviceDesc, srv)
}

func _ChannelConvene_CollectChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CollectChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).CollectChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelconvene.ChannelConvene/CollectChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).CollectChannel(ctx, req.(*CollectChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_RemoveChannelCollection_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveChannelCollectionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).RemoveChannelCollection(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelconvene.ChannelConvene/RemoveChannelCollection",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).RemoveChannelCollection(ctx, req.(*RemoveChannelCollectionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_GetChannelCollectionListByUid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelCollectionListByUidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).GetChannelCollectionListByUid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelconvene.ChannelConvene/GetChannelCollectionListByUid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).GetChannelCollectionListByUid(ctx, req.(*GetChannelCollectionListByUidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_GetChannelCollectionMemberList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelCollectionMemberListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).GetChannelCollectionMemberList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelconvene.ChannelConvene/GetChannelCollectionMemberList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).GetChannelCollectionMemberList(ctx, req.(*GetChannelCollectionMemberListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_GetChannelConveneInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelConveneInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).GetChannelConveneInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelconvene.ChannelConvene/GetChannelConveneInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).GetChannelConveneInfo(ctx, req.(*GetChannelConveneInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_ConveneChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConveneChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).ConveneChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelconvene.ChannelConvene/ConveneChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).ConveneChannel(ctx, req.(*ConveneChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_ConveneChannelInTurn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConveneChannelInTurnReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).ConveneChannelInTurn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelconvene.ChannelConvene/ConveneChannelInTurn",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).ConveneChannelInTurn(ctx, req.(*ConveneChannelInTurnReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_DisableConveneChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisableConveneChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).DisableConveneChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelconvene.ChannelConvene/DisableConveneChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).DisableConveneChannel(ctx, req.(*DisableConveneChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_DisableConveneChannelInTurn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisableConveneChannelInTurnReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).DisableConveneChannelInTurn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelconvene.ChannelConvene/DisableConveneChannelInTurn",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).DisableConveneChannelInTurn(ctx, req.(*DisableConveneChannelInTurnReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_InformCreateChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InformCreateChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).InformCreateChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelconvene.ChannelConvene/InformCreateChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).InformCreateChannel(ctx, req.(*InformCreateChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_InformDismissChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InformDismissChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).InformDismissChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelconvene.ChannelConvene/InformDismissChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).InformDismissChannel(ctx, req.(*InformDismissChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_ConfirmChannelConvene_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfirmChannelConveneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).ConfirmChannelConvene(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelconvene.ChannelConvene/ConfirmChannelConvene",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).ConfirmChannelConvene(ctx, req.(*ConfirmChannelConveneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_InformUserEnterChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InformUserEnterChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).InformUserEnterChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelconvene.ChannelConvene/InformUserEnterChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).InformUserEnterChannel(ctx, req.(*InformUserEnterChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_InformUserExitChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InformUserExitChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).InformUserExitChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelconvene.ChannelConvene/InformUserExitChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).InformUserExitChannel(ctx, req.(*InformUserExitChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_UpdateUserConveneStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserConveneStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).UpdateUserConveneStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelconvene.ChannelConvene/UpdateUserConveneStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).UpdateUserConveneStatus(ctx, req.(*UpdateUserConveneStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_HasCollectChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HasCollectChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).HasCollectChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelconvene.ChannelConvene/HasCollectChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).HasCollectChannel(ctx, req.(*HasCollectChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_GetConveneMemberList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConveneMemberListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).GetConveneMemberList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelconvene.ChannelConvene/GetConveneMemberList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).GetConveneMemberList(ctx, req.(*GetConveneMemberListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_GetUserConveneInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserConveneInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).GetUserConveneInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelconvene.ChannelConvene/GetUserConveneInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).GetUserConveneInfo(ctx, req.(*GetUserConveneInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_GetConfirmCountByStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConfirmCountByStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).GetConfirmCountByStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelconvene.ChannelConvene/GetConfirmCountByStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).GetConfirmCountByStatus(ctx, req.(*GetConfirmCountByStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_DismissChannelInTurn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DismissChannelInTurnReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).DismissChannelInTurn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelconvene.ChannelConvene/DismissChannelInTurn",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).DismissChannelInTurn(ctx, req.(*DismissChannelInTurnReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_GetChannelCollectionMemberCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelCollectionMemberCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).GetChannelCollectionMemberCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelconvene.ChannelConvene/GetChannelCollectionMemberCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).GetChannelCollectionMemberCount(ctx, req.(*GetChannelCollectionMemberCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_BatchHasCollectChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchHasCollectChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).BatchHasCollectChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelconvene.ChannelConvene/BatchHasCollectChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).BatchHasCollectChannel(ctx, req.(*BatchHasCollectChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelConvene_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channelconvene.ChannelConvene",
	HandlerType: (*ChannelConveneServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CollectChannel",
			Handler:    _ChannelConvene_CollectChannel_Handler,
		},
		{
			MethodName: "RemoveChannelCollection",
			Handler:    _ChannelConvene_RemoveChannelCollection_Handler,
		},
		{
			MethodName: "GetChannelCollectionListByUid",
			Handler:    _ChannelConvene_GetChannelCollectionListByUid_Handler,
		},
		{
			MethodName: "GetChannelCollectionMemberList",
			Handler:    _ChannelConvene_GetChannelCollectionMemberList_Handler,
		},
		{
			MethodName: "GetChannelConveneInfo",
			Handler:    _ChannelConvene_GetChannelConveneInfo_Handler,
		},
		{
			MethodName: "ConveneChannel",
			Handler:    _ChannelConvene_ConveneChannel_Handler,
		},
		{
			MethodName: "ConveneChannelInTurn",
			Handler:    _ChannelConvene_ConveneChannelInTurn_Handler,
		},
		{
			MethodName: "DisableConveneChannel",
			Handler:    _ChannelConvene_DisableConveneChannel_Handler,
		},
		{
			MethodName: "DisableConveneChannelInTurn",
			Handler:    _ChannelConvene_DisableConveneChannelInTurn_Handler,
		},
		{
			MethodName: "InformCreateChannel",
			Handler:    _ChannelConvene_InformCreateChannel_Handler,
		},
		{
			MethodName: "InformDismissChannel",
			Handler:    _ChannelConvene_InformDismissChannel_Handler,
		},
		{
			MethodName: "ConfirmChannelConvene",
			Handler:    _ChannelConvene_ConfirmChannelConvene_Handler,
		},
		{
			MethodName: "InformUserEnterChannel",
			Handler:    _ChannelConvene_InformUserEnterChannel_Handler,
		},
		{
			MethodName: "InformUserExitChannel",
			Handler:    _ChannelConvene_InformUserExitChannel_Handler,
		},
		{
			MethodName: "UpdateUserConveneStatus",
			Handler:    _ChannelConvene_UpdateUserConveneStatus_Handler,
		},
		{
			MethodName: "HasCollectChannel",
			Handler:    _ChannelConvene_HasCollectChannel_Handler,
		},
		{
			MethodName: "GetConveneMemberList",
			Handler:    _ChannelConvene_GetConveneMemberList_Handler,
		},
		{
			MethodName: "GetUserConveneInfo",
			Handler:    _ChannelConvene_GetUserConveneInfo_Handler,
		},
		{
			MethodName: "GetConfirmCountByStatus",
			Handler:    _ChannelConvene_GetConfirmCountByStatus_Handler,
		},
		{
			MethodName: "DismissChannelInTurn",
			Handler:    _ChannelConvene_DismissChannelInTurn_Handler,
		},
		{
			MethodName: "GetChannelCollectionMemberCount",
			Handler:    _ChannelConvene_GetChannelCollectionMemberCount_Handler,
		},
		{
			MethodName: "BatchHasCollectChannel",
			Handler:    _ChannelConvene_BatchHasCollectChannel_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/channelconvene/channelconvene.proto",
}

func (m *CollectChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CollectChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *RemoveChannelCollectionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveChannelCollectionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, num := range m.ChannelIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelconvene(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *HasCollectChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *HasCollectChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *HasCollectChannelResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *HasCollectChannelResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.IsCollected {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *BatchHasCollectChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchHasCollectChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, num := range m.ChannelIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelconvene(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *BatchHasCollectChannelResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchHasCollectChannelResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, num := range m.ChannelIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelconvene(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GetChannelCollectionListByUidReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelCollectionListByUidReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetChannelCollectionListByUidResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelCollectionListByUidResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, num := range m.ChannelIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelconvene(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *StChannelCollectMember) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StChannelCollectMember) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.CollectTs))
	return i, nil
}

func (m *StChannelConveneMember) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StChannelConveneMember) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.ConveneStatus))
	return i, nil
}

func (m *StChannelConveneInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StChannelConveneInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.LastConveneTs))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.LastCancelTs))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.ConfirmCount))
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.ConfirmCountReal))
	if m.ConveneMsg != nil {
		dAtA[i] = 0x32
		i++
		i = encodeVarintChannelconvene(dAtA, i, uint64(len(m.ConveneMsg)))
		i += copy(dAtA[i:], m.ConveneMsg)
	}
	dAtA[i] = 0x38
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.ValidConveneTs))
	dAtA[i] = 0x40
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.ConveneDurationMin))
	return i, nil
}

func (m *GetChannelCollectionMemberListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelCollectionMemberListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.Offset))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.Limit))
	return i, nil
}

func (m *GetChannelCollectionMemberListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelCollectionMemberListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.MemberList) > 0 {
		for _, msg := range m.MemberList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelconvene(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetChannelCollectionMemberCountReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelCollectionMemberCountReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *GetChannelCollectionMemberCountResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelCollectionMemberCountResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.MemberCount))
	return i, nil
}

func (m *GetChannelConveneInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelConveneInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *GetChannelConveneInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelConveneInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ConveneInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("convene_info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelconvene(dAtA, i, uint64(m.ConveneInfo.Size()))
		n1, err := m.ConveneInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *ConveneChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ConveneChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(len(m.ConveneMsg)))
	i += copy(dAtA[i:], m.ConveneMsg)
	if len(m.ChannelOnlineMembers) > 0 {
		for _, num := range m.ChannelOnlineMembers {
			dAtA[i] = 0x18
			i++
			i = encodeVarintChannelconvene(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *ConveneChannelResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ConveneChannelResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ConveneInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("convene_info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelconvene(dAtA, i, uint64(m.ConveneInfo.Size()))
		n2, err := m.ConveneInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func (m *ConveneChannelInTurnReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ConveneChannelInTurnReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.Offset))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.Version))
	return i, nil
}

func (m *DisableConveneChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DisableConveneChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *DisableConveneChannelInTurnReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DisableConveneChannelInTurnReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.Offset))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.Status))
	return i, nil
}

func (m *ConfirmChannelConveneReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ConfirmChannelConveneReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *ConfirmChannelConveneResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ConfirmChannelConveneResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelList) > 0 {
		for _, msg := range m.ChannelList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelconvene(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *InformCreateChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *InformCreateChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *InformDismissChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *InformDismissChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *DismissChannelInTurnReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DismissChannelInTurnReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.Offset))
	return i, nil
}

func (m *InformUserEnterChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *InformUserEnterChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *InformUserExitChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *InformUserExitChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *UpdateUserConveneStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateUserConveneStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.Status))
	return i, nil
}

func (m *GetConveneMemberListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetConveneMemberListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *GetConveneMemberListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetConveneMemberListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.MemberList) > 0 {
		for _, msg := range m.MemberList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelconvene(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetUserConveneInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserConveneInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *GetUserConveneInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserConveneInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.MemberInfo != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelconvene(dAtA, i, uint64(m.MemberInfo.Size()))
		n3, err := m.MemberInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	return i, nil
}

func (m *GetConfirmCountByStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetConfirmCountByStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.Status))
	return i, nil
}

func (m *GetConfirmCountByStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetConfirmCountByStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.MemberCount))
	return i, nil
}

func (m *ChannelConveneAsyncJobConveneChangeNotify) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelConveneAsyncJobConveneChangeNotify) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.OptUid))
	if m.ConveneInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("convene_info")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintChannelconvene(dAtA, i, uint64(m.ConveneInfo.Size()))
		n4, err := m.ConveneInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	return i, nil
}

func (m *ChannelConveneAsyncJobConveneInTurnNotify) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelConveneAsyncJobConveneInTurnNotify) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.OptUid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.ConveneTs))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.Offset))
	if len(m.NotifyUidList) > 0 {
		for _, num := range m.NotifyUidList {
			dAtA[i] = 0x28
			i++
			i = encodeVarintChannelconvene(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x30
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.Version))
	return i, nil
}

func (m *ChannelConveneAsyncJobDisableConveneInTurnNotify) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelConveneAsyncJobDisableConveneInTurnNotify) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.OptUid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.Offset))
	if len(m.NotifyUidList) > 0 {
		for _, num := range m.NotifyUidList {
			dAtA[i] = 0x28
			i++
			i = encodeVarintChannelconvene(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *ChannelConveneAsyncJobConfirmConveneNotify) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelConveneAsyncJobConfirmConveneNotify) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.Uid))
	if len(m.ChannelList) > 0 {
		for _, msg := range m.ChannelList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintChannelconvene(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ChannelConveneAsyncJobDismissChannelNotify) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelConveneAsyncJobDismissChannelNotify) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.OptUid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelconvene(dAtA, i, uint64(m.Offset))
	return i, nil
}

func encodeFixed64Channelconvene(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Channelconvene(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintChannelconvene(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *CollectChannelReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelconvene(uint64(m.ChannelId))
	return n
}

func (m *RemoveChannelCollectionReq) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, e := range m.ChannelIdList {
			n += 1 + sovChannelconvene(uint64(e))
		}
	}
	return n
}

func (m *HasCollectChannelReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelconvene(uint64(m.ChannelId))
	return n
}

func (m *HasCollectChannelResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	return n
}

func (m *BatchHasCollectChannelReq) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, e := range m.ChannelIdList {
			n += 1 + sovChannelconvene(uint64(e))
		}
	}
	return n
}

func (m *BatchHasCollectChannelResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, e := range m.ChannelIdList {
			n += 1 + sovChannelconvene(uint64(e))
		}
	}
	return n
}

func (m *GetChannelCollectionListByUidReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetChannelCollectionListByUidResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, e := range m.ChannelIdList {
			n += 1 + sovChannelconvene(uint64(e))
		}
	}
	return n
}

func (m *StChannelCollectMember) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelconvene(uint64(m.Uid))
	n += 1 + sovChannelconvene(uint64(m.CollectTs))
	return n
}

func (m *StChannelConveneMember) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelconvene(uint64(m.Uid))
	n += 1 + sovChannelconvene(uint64(m.ChannelId))
	n += 1 + sovChannelconvene(uint64(m.ConveneStatus))
	return n
}

func (m *StChannelConveneInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelconvene(uint64(m.ChannelId))
	n += 1 + sovChannelconvene(uint64(m.LastConveneTs))
	n += 1 + sovChannelconvene(uint64(m.LastCancelTs))
	n += 1 + sovChannelconvene(uint64(m.ConfirmCount))
	n += 1 + sovChannelconvene(uint64(m.ConfirmCountReal))
	if m.ConveneMsg != nil {
		l = len(m.ConveneMsg)
		n += 1 + l + sovChannelconvene(uint64(l))
	}
	n += 1 + sovChannelconvene(uint64(m.ValidConveneTs))
	n += 1 + sovChannelconvene(uint64(m.ConveneDurationMin))
	return n
}

func (m *GetChannelCollectionMemberListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelconvene(uint64(m.ChannelId))
	n += 1 + sovChannelconvene(uint64(m.Offset))
	n += 1 + sovChannelconvene(uint64(m.Limit))
	return n
}

func (m *GetChannelCollectionMemberListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.MemberList) > 0 {
		for _, e := range m.MemberList {
			l = e.Size()
			n += 1 + l + sovChannelconvene(uint64(l))
		}
	}
	return n
}

func (m *GetChannelCollectionMemberCountReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelconvene(uint64(m.ChannelId))
	return n
}

func (m *GetChannelCollectionMemberCountResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelconvene(uint64(m.MemberCount))
	return n
}

func (m *GetChannelConveneInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelconvene(uint64(m.ChannelId))
	return n
}

func (m *GetChannelConveneInfoResp) Size() (n int) {
	var l int
	_ = l
	if m.ConveneInfo != nil {
		l = m.ConveneInfo.Size()
		n += 1 + l + sovChannelconvene(uint64(l))
	}
	return n
}

func (m *ConveneChannelReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelconvene(uint64(m.ChannelId))
	l = len(m.ConveneMsg)
	n += 1 + l + sovChannelconvene(uint64(l))
	if len(m.ChannelOnlineMembers) > 0 {
		for _, e := range m.ChannelOnlineMembers {
			n += 1 + sovChannelconvene(uint64(e))
		}
	}
	return n
}

func (m *ConveneChannelResp) Size() (n int) {
	var l int
	_ = l
	if m.ConveneInfo != nil {
		l = m.ConveneInfo.Size()
		n += 1 + l + sovChannelconvene(uint64(l))
	}
	return n
}

func (m *ConveneChannelInTurnReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelconvene(uint64(m.ChannelId))
	n += 1 + sovChannelconvene(uint64(m.Offset))
	n += 1 + sovChannelconvene(uint64(m.Version))
	return n
}

func (m *DisableConveneChannelReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelconvene(uint64(m.ChannelId))
	return n
}

func (m *DisableConveneChannelInTurnReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelconvene(uint64(m.ChannelId))
	n += 1 + sovChannelconvene(uint64(m.Offset))
	n += 1 + sovChannelconvene(uint64(m.Status))
	return n
}

func (m *ConfirmChannelConveneReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *ConfirmChannelConveneResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelList) > 0 {
		for _, e := range m.ChannelList {
			l = e.Size()
			n += 1 + l + sovChannelconvene(uint64(l))
		}
	}
	return n
}

func (m *InformCreateChannelReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelconvene(uint64(m.ChannelId))
	return n
}

func (m *InformDismissChannelReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelconvene(uint64(m.ChannelId))
	return n
}

func (m *DismissChannelInTurnReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelconvene(uint64(m.ChannelId))
	n += 1 + sovChannelconvene(uint64(m.Offset))
	return n
}

func (m *InformUserEnterChannelReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelconvene(uint64(m.ChannelId))
	return n
}

func (m *InformUserExitChannelReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelconvene(uint64(m.ChannelId))
	return n
}

func (m *UpdateUserConveneStatusReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelconvene(uint64(m.ChannelId))
	n += 1 + sovChannelconvene(uint64(m.Status))
	return n
}

func (m *GetConveneMemberListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelconvene(uint64(m.ChannelId))
	return n
}

func (m *GetConveneMemberListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.MemberList) > 0 {
		for _, e := range m.MemberList {
			l = e.Size()
			n += 1 + l + sovChannelconvene(uint64(l))
		}
	}
	return n
}

func (m *GetUserConveneInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelconvene(uint64(m.ChannelId))
	return n
}

func (m *GetUserConveneInfoResp) Size() (n int) {
	var l int
	_ = l
	if m.MemberInfo != nil {
		l = m.MemberInfo.Size()
		n += 1 + l + sovChannelconvene(uint64(l))
	}
	return n
}

func (m *GetConfirmCountByStatusReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelconvene(uint64(m.ChannelId))
	n += 1 + sovChannelconvene(uint64(m.Status))
	return n
}

func (m *GetConfirmCountByStatusResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelconvene(uint64(m.MemberCount))
	return n
}

func (m *ChannelConveneAsyncJobConveneChangeNotify) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelconvene(uint64(m.OptUid))
	if m.ConveneInfo != nil {
		l = m.ConveneInfo.Size()
		n += 1 + l + sovChannelconvene(uint64(l))
	}
	return n
}

func (m *ChannelConveneAsyncJobConveneInTurnNotify) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelconvene(uint64(m.OptUid))
	n += 1 + sovChannelconvene(uint64(m.ChannelId))
	n += 1 + sovChannelconvene(uint64(m.ConveneTs))
	n += 1 + sovChannelconvene(uint64(m.Offset))
	if len(m.NotifyUidList) > 0 {
		for _, e := range m.NotifyUidList {
			n += 1 + sovChannelconvene(uint64(e))
		}
	}
	n += 1 + sovChannelconvene(uint64(m.Version))
	return n
}

func (m *ChannelConveneAsyncJobDisableConveneInTurnNotify) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelconvene(uint64(m.OptUid))
	n += 1 + sovChannelconvene(uint64(m.ChannelId))
	n += 1 + sovChannelconvene(uint64(m.Status))
	n += 1 + sovChannelconvene(uint64(m.Offset))
	if len(m.NotifyUidList) > 0 {
		for _, e := range m.NotifyUidList {
			n += 1 + sovChannelconvene(uint64(e))
		}
	}
	return n
}

func (m *ChannelConveneAsyncJobConfirmConveneNotify) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelconvene(uint64(m.Uid))
	if len(m.ChannelList) > 0 {
		for _, e := range m.ChannelList {
			l = e.Size()
			n += 1 + l + sovChannelconvene(uint64(l))
		}
	}
	return n
}

func (m *ChannelConveneAsyncJobDismissChannelNotify) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelconvene(uint64(m.OptUid))
	n += 1 + sovChannelconvene(uint64(m.ChannelId))
	n += 1 + sovChannelconvene(uint64(m.Offset))
	return n
}

func sovChannelconvene(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozChannelconvene(x uint64) (n int) {
	return sovChannelconvene(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *CollectChannelReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CollectChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CollectChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveChannelCollectionReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveChannelCollectionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveChannelCollectionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelconvene
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ChannelIdList = append(m.ChannelIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelconvene
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelconvene
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelconvene
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ChannelIdList = append(m.ChannelIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *HasCollectChannelReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: HasCollectChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: HasCollectChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *HasCollectChannelResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: HasCollectChannelResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: HasCollectChannelResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsCollected", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsCollected = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_collected")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchHasCollectChannelReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchHasCollectChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchHasCollectChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelconvene
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ChannelIdList = append(m.ChannelIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelconvene
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelconvene
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelconvene
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ChannelIdList = append(m.ChannelIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchHasCollectChannelResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchHasCollectChannelResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchHasCollectChannelResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelconvene
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ChannelIdList = append(m.ChannelIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelconvene
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelconvene
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelconvene
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ChannelIdList = append(m.ChannelIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelCollectionListByUidReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelCollectionListByUidReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelCollectionListByUidReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelCollectionListByUidResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelCollectionListByUidResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelCollectionListByUidResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelconvene
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ChannelIdList = append(m.ChannelIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelconvene
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelconvene
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelconvene
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ChannelIdList = append(m.ChannelIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StChannelCollectMember) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StChannelCollectMember: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StChannelCollectMember: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CollectTs", wireType)
			}
			m.CollectTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CollectTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("collect_ts")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StChannelConveneMember) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StChannelConveneMember: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StChannelConveneMember: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConveneStatus", wireType)
			}
			m.ConveneStatus = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConveneStatus |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StChannelConveneInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StChannelConveneInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StChannelConveneInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastConveneTs", wireType)
			}
			m.LastConveneTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastConveneTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastCancelTs", wireType)
			}
			m.LastCancelTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastCancelTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConfirmCount", wireType)
			}
			m.ConfirmCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConfirmCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConfirmCountReal", wireType)
			}
			m.ConfirmCountReal = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConfirmCountReal |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConveneMsg", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthChannelconvene
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ConveneMsg = append(m.ConveneMsg[:0], dAtA[iNdEx:postIndex]...)
			if m.ConveneMsg == nil {
				m.ConveneMsg = []byte{}
			}
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ValidConveneTs", wireType)
			}
			m.ValidConveneTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ValidConveneTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConveneDurationMin", wireType)
			}
			m.ConveneDurationMin = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConveneDurationMin |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelCollectionMemberListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelCollectionMemberListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelCollectionMemberListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelCollectionMemberListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelCollectionMemberListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelCollectionMemberListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MemberList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelconvene
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MemberList = append(m.MemberList, &StChannelCollectMember{})
			if err := m.MemberList[len(m.MemberList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelCollectionMemberCountReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelCollectionMemberCountReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelCollectionMemberCountReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelCollectionMemberCountResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelCollectionMemberCountResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelCollectionMemberCountResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MemberCount", wireType)
			}
			m.MemberCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MemberCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("member_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelConveneInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelConveneInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelConveneInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelConveneInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelConveneInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelConveneInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConveneInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelconvene
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ConveneInfo == nil {
				m.ConveneInfo = &StChannelConveneInfo{}
			}
			if err := m.ConveneInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("convene_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ConveneChannelReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ConveneChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ConveneChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConveneMsg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelconvene
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ConveneMsg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelconvene
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ChannelOnlineMembers = append(m.ChannelOnlineMembers, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelconvene
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelconvene
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelconvene
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ChannelOnlineMembers = append(m.ChannelOnlineMembers, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelOnlineMembers", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("convene_msg")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ConveneChannelResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ConveneChannelResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ConveneChannelResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConveneInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelconvene
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ConveneInfo == nil {
				m.ConveneInfo = &StChannelConveneInfo{}
			}
			if err := m.ConveneInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("convene_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ConveneChannelInTurnReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ConveneChannelInTurnReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ConveneChannelInTurnReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Version", wireType)
			}
			m.Version = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Version |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("offset")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DisableConveneChannelReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DisableConveneChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DisableConveneChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DisableConveneChannelInTurnReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DisableConveneChannelInTurnReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DisableConveneChannelInTurnReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("offset")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ConfirmChannelConveneReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ConfirmChannelConveneReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ConfirmChannelConveneReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ConfirmChannelConveneResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ConfirmChannelConveneResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ConfirmChannelConveneResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelconvene
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ChannelList = append(m.ChannelList, &StChannelConveneInfo{})
			if err := m.ChannelList[len(m.ChannelList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *InformCreateChannelReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: InformCreateChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: InformCreateChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *InformDismissChannelReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: InformDismissChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: InformDismissChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DismissChannelInTurnReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DismissChannelInTurnReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DismissChannelInTurnReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("offset")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *InformUserEnterChannelReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: InformUserEnterChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: InformUserEnterChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *InformUserExitChannelReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: InformUserExitChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: InformUserExitChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateUserConveneStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateUserConveneStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateUserConveneStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetConveneMemberListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetConveneMemberListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetConveneMemberListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetConveneMemberListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetConveneMemberListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetConveneMemberListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MemberList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelconvene
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MemberList = append(m.MemberList, &StChannelConveneMember{})
			if err := m.MemberList[len(m.MemberList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserConveneInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserConveneInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserConveneInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserConveneInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserConveneInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserConveneInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MemberInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelconvene
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.MemberInfo == nil {
				m.MemberInfo = &StChannelConveneMember{}
			}
			if err := m.MemberInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetConfirmCountByStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetConfirmCountByStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetConfirmCountByStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetConfirmCountByStatusResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetConfirmCountByStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetConfirmCountByStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MemberCount", wireType)
			}
			m.MemberCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MemberCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelConveneAsyncJobConveneChangeNotify) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelConveneAsyncJobConveneChangeNotify: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelConveneAsyncJobConveneChangeNotify: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OptUid", wireType)
			}
			m.OptUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OptUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConveneInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelconvene
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ConveneInfo == nil {
				m.ConveneInfo = &StChannelConveneInfo{}
			}
			if err := m.ConveneInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("opt_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("convene_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelConveneAsyncJobConveneInTurnNotify) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelConveneAsyncJobConveneInTurnNotify: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelConveneAsyncJobConveneInTurnNotify: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OptUid", wireType)
			}
			m.OptUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OptUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConveneTs", wireType)
			}
			m.ConveneTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConveneTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelconvene
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.NotifyUidList = append(m.NotifyUidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelconvene
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelconvene
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelconvene
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.NotifyUidList = append(m.NotifyUidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field NotifyUidList", wireType)
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Version", wireType)
			}
			m.Version = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Version |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("opt_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("convene_ts")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("offset")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelConveneAsyncJobDisableConveneInTurnNotify) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelConveneAsyncJobDisableConveneInTurnNotify: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelConveneAsyncJobDisableConveneInTurnNotify: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OptUid", wireType)
			}
			m.OptUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OptUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelconvene
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.NotifyUidList = append(m.NotifyUidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelconvene
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelconvene
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelconvene
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.NotifyUidList = append(m.NotifyUidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field NotifyUidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("opt_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("offset")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelConveneAsyncJobConfirmConveneNotify) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelConveneAsyncJobConfirmConveneNotify: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelConveneAsyncJobConfirmConveneNotify: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelconvene
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ChannelList = append(m.ChannelList, &StChannelConveneInfo{})
			if err := m.ChannelList[len(m.ChannelList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelConveneAsyncJobDismissChannelNotify) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelConveneAsyncJobDismissChannelNotify: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelConveneAsyncJobDismissChannelNotify: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OptUid", wireType)
			}
			m.OptUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OptUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelconvene(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelconvene
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("opt_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("offset")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipChannelconvene(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowChannelconvene
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChannelconvene
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthChannelconvene
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowChannelconvene
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipChannelconvene(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthChannelconvene = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowChannelconvene   = fmt2.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("src/channelconvene/channelconvene.proto", fileDescriptorChannelconvene)
}

var fileDescriptorChannelconvene = []byte{
	// 1706 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x59, 0x4b, 0x6f, 0x14, 0xc7,
	0x16, 0x76, 0xcd, 0xd8, 0x63, 0x73, 0xfc, 0xa4, 0x18, 0xdb, 0xe3, 0x06, 0x4c, 0xd3, 0x80, 0xb1,
	0x0d, 0x36, 0x5c, 0x73, 0x85, 0xae, 0x7c, 0xcd, 0x00, 0xb6, 0xb9, 0x5c, 0x87, 0x40, 0x22, 0x63,
	0xaf, 0x12, 0x34, 0x1a, 0xcf, 0xb4, 0x71, 0x8b, 0xe9, 0x07, 0x53, 0x3d, 0x56, 0x2c, 0x91, 0x84,
	0x24, 0x8b, 0xa0, 0x04, 0x10, 0x4a, 0x94, 0x75, 0x36, 0x56, 0x16, 0x51, 0x7e, 0x47, 0xc4, 0x32,
	0xeb, 0x2c, 0xa2, 0x88, 0x6c, 0xfc, 0x1b, 0xb2, 0x8a, 0xaa, 0xaa, 0x7b, 0xa6, 0x1f, 0xd5, 0x33,
	0xd5, 0x96, 0x97, 0x5d, 0x75, 0xce, 0xa9, 0xaf, 0xce, 0xfb, 0x54, 0xc3, 0x45, 0x52, 0xaf, 0x5c,
	0xa9, 0xec, 0x94, 0x2d, 0x4b, 0xaf, 0x55, 0x6c, 0x6b, 0x57, 0xb7, 0xf4, 0xc8, 0xe7, 0xbc, 0x53,
	0xb7, 0x5d, 0x1b, 0x0f, 0x85, 0x57, 0x95, 0xf3, 0x15, 0xdb, 0x34, 0x6d, 0xeb, 0x8a, 0x5b, 0xdb,
	0x75, 0x8c, 0xca, 0x93, 0x9a, 0x7e, 0x85, 0x3c, 0xd9, 0x6a, 0x18, 0x35, 0xd7, 0xb0, 0xdc, 0x3d,
	0xc7, 0xe3, 0xd2, 0xfe, 0x03, 0xc7, 0x57, 0xec, 0x5a, 0x4d, 0xaf, 0xb8, 0x2b, 0x9c, 0x7d, 0x5d,
	0x7f, 0x8a, 0xcf, 0x01, 0x78, 0xc2, 0x4a, 0x46, 0xb5, 0x80, 0xd4, 0xcc, 0xf4, 0xe0, 0x72, 0xf7,
	0xdb, 0x3f, 0xce, 0x74, 0xad, 0x1f, 0xf3, 0xd6, 0xd7, 0xaa, 0xda, 0x2a, 0x28, 0xeb, 0xba, 0x69,
	0xef, 0xea, 0x1e, 0xa3, 0x27, 0xc6, 0xb0, 0x2d, 0x2a, 0x62, 0x0a, 0x86, 0x5b, 0x22, 0x4a, 0x35,
	0x83, 0xb8, 0x05, 0xa4, 0x66, 0xa7, 0x07, 0xd7, 0x07, 0x9b, 0x12, 0xde, 0x37, 0x88, 0xab, 0xfd,
	0x17, 0xf2, 0xff, 0x2f, 0x93, 0x43, 0x42, 0xb8, 0x05, 0xa3, 0x02, 0x66, 0xe2, 0xe0, 0x8b, 0x30,
	0x60, 0x90, 0x52, 0x85, 0x6f, 0xe8, 0x9c, 0xbf, 0xcf, 0xe3, 0xef, 0x37, 0x7c, 0x0e, 0xbd, 0xaa,
	0xad, 0xc0, 0xc4, 0x72, 0xd9, 0xad, 0xec, 0x08, 0x31, 0xc8, 0xde, 0x61, 0x15, 0x94, 0x24, 0x21,
	0xc4, 0x91, 0x96, 0xa2, 0x81, 0x7a, 0x57, 0x77, 0x63, 0xca, 0xa4, 0x7b, 0xcb, 0x7b, 0x9b, 0x46,
	0x75, 0x5d, 0x7f, 0xaa, 0xdd, 0x83, 0xb3, 0x1d, 0x68, 0x52, 0x1c, 0xb8, 0x09, 0x63, 0x0f, 0x23,
	0xb2, 0xee, 0xeb, 0xe6, 0x96, 0x5e, 0xc7, 0x63, 0x90, 0x6d, 0x44, 0xb4, 0x4e, 0x17, 0x98, 0x51,
	0x38, 0x61, 0xc9, 0x25, 0x85, 0x4c, 0xc8, 0x28, 0x7c, 0x7d, 0x83, 0x68, 0x5f, 0xa2, 0x90, 0x5c,
	0xe6, 0x8c, 0x12, 0x72, 0x5b, 0xc6, 0xce, 0x08, 0x8d, 0x8d, 0x2f, 0xc1, 0x90, 0xe7, 0xda, 0x25,
	0xe2, 0x96, 0xdd, 0x06, 0x29, 0x64, 0x55, 0xd4, 0x24, 0x1c, 0xf4, 0xf6, 0x1e, 0xb2, 0x2d, 0xed,
	0xab, 0x2c, 0xe4, 0xa3, 0x20, 0xd6, 0xac, 0x6d, 0x5b, 0xca, 0xaf, 0xf0, 0x65, 0x18, 0xae, 0x95,
	0x89, 0x5b, 0xf2, 0xcf, 0x63, 0x97, 0x0d, 0x9c, 0x45, 0x37, 0x3d, 0xa1, 0x1b, 0x04, 0xcf, 0xc2,
	0x10, 0xa7, 0x2e, 0x5b, 0x15, 0xbd, 0x46, 0x89, 0x83, 0xc0, 0x06, 0x18, 0x31, 0xdb, 0xda, 0x20,
	0x78, 0x06, 0x28, 0xd0, 0x6d, 0xa3, 0x6e, 0x96, 0x2a, 0x76, 0xc3, 0x72, 0x0b, 0xdd, 0x41, 0x52,
	0x6f, 0x6b, 0x85, 0xee, 0xe0, 0x05, 0xc0, 0x21, 0xd2, 0x52, 0x5d, 0x2f, 0xd7, 0x0a, 0x3d, 0x01,
	0xfa, 0x91, 0x20, 0xfd, 0xba, 0x5e, 0xae, 0xe1, 0x0b, 0xd0, 0xef, 0x63, 0x36, 0xc9, 0xe3, 0x42,
	0x4e, 0x45, 0xd3, 0x03, 0x1e, 0x31, 0x78, 0x1b, 0xf7, 0xc9, 0x63, 0x3c, 0x0f, 0x23, 0xbb, 0xe5,
	0x9a, 0x51, 0x0d, 0x5e, 0xb0, 0x37, 0x20, 0x78, 0x88, 0xed, 0xb6, 0x6e, 0x78, 0x1d, 0xf2, 0x3e,
	0x65, 0xb5, 0x51, 0x2f, 0x53, 0x97, 0x2b, 0x99, 0x86, 0x55, 0xe8, 0x0b, 0xf0, 0x60, 0x8f, 0x62,
	0xd5, 0x23, 0xb8, 0x6f, 0x58, 0xd4, 0x15, 0x84, 0xfe, 0xca, 0xdd, 0x81, 0x3a, 0xa1, 0x6c, 0xa8,
	0xe3, 0x53, 0x90, 0xb3, 0xb7, 0xb7, 0x89, 0xee, 0x86, 0x2c, 0xe1, 0xad, 0x61, 0x05, 0x7a, 0x6a,
	0x86, 0x69, 0xb8, 0x21, 0xcd, 0xf3, 0x25, 0xcd, 0x04, 0xad, 0x13, 0x06, 0xe2, 0xe0, 0xbb, 0xd0,
	0x6f, 0xb2, 0x95, 0x56, 0xc0, 0xf4, 0x2f, 0x4c, 0xcd, 0x47, 0x32, 0xad, 0x38, 0x5e, 0xd6, 0xc1,
	0x6c, 0x0a, 0xd3, 0xd6, 0xda, 0x1d, 0xe7, 0x59, 0x4a, 0x32, 0xbd, 0x3d, 0x80, 0x73, 0x1d, 0x45,
	0xf1, 0x64, 0xe7, 0x41, 0xe7, 0x2e, 0x15, 0x94, 0xe6, 0x5d, 0x8a, 0x11, 0x6b, 0x37, 0xa1, 0x10,
	0x94, 0xd7, 0x0c, 0x0a, 0x69, 0x40, 0x55, 0x98, 0x48, 0x10, 0xc0, 0x34, 0x38, 0xe0, 0x3b, 0x89,
	0x61, 0x6d, 0xdb, 0x4c, 0x46, 0xff, 0xc2, 0xf9, 0x36, 0x2a, 0x6c, 0xf1, 0xfb, 0x5e, 0x4b, 0x3f,
	0xb4, 0x1f, 0x10, 0xad, 0x49, 0xec, 0x3b, 0x65, 0x41, 0x88, 0xfa, 0x3f, 0xcd, 0x24, 0xc7, 0x04,
	0xfe, 0xff, 0x6f, 0x18, 0xf3, 0x65, 0xd9, 0x56, 0xcd, 0xa0, 0xd4, 0x4c, 0x4d, 0x34, 0x72, 0x69,
	0xa2, 0xcc, 0x7b, 0xbb, 0x1f, 0xb0, 0x4d, 0xae, 0x6f, 0xa2, 0x3d, 0x02, 0x1c, 0x85, 0x75, 0x94,
	0xd7, 0x7e, 0x06, 0xe3, 0x61, 0xf1, 0x6b, 0xd6, 0x46, 0xa3, 0x6e, 0x1d, 0x2a, 0x42, 0x32, 0xb1,
	0x08, 0x99, 0x84, 0xde, 0x5d, 0xbd, 0x4e, 0x0c, 0xdb, 0x0a, 0xc5, 0x88, 0xbf, 0x48, 0x7d, 0x63,
	0xd5, 0x20, 0xe5, 0xad, 0x9a, 0x7e, 0x38, 0xd5, 0x6b, 0x5f, 0x20, 0x98, 0x14, 0x4a, 0x38, 0xd2,
	0x6b, 0x9c, 0x82, 0x5c, 0x33, 0xf9, 0x07, 0x76, 0xf9, 0x9a, 0xa6, 0x40, 0x61, 0xc5, 0x4b, 0x89,
	0x21, 0x65, 0xd3, 0xd2, 0x59, 0x85, 0x89, 0x84, 0x3d, 0xcf, 0x88, 0x1e, 0xb2, 0x40, 0xf8, 0xcb,
	0x1a, 0x91, 0xaf, 0xb1, 0xe8, 0xbf, 0x01, 0x63, 0x74, 0xb1, 0x6e, 0xae, 0xd4, 0xf5, 0xb2, 0x9b,
	0x5a, 0x89, 0x45, 0x18, 0xe7, 0xec, 0xab, 0x06, 0x31, 0x0d, 0x42, 0xd2, 0xf2, 0x7f, 0x0c, 0xe3,
	0x61, 0xce, 0xa3, 0x54, 0xbe, 0x76, 0x0b, 0x26, 0x38, 0xba, 0x4d, 0xa2, 0xd7, 0xef, 0x58, 0xae,
	0x5e, 0x4f, 0x8b, 0xef, 0x26, 0x14, 0x02, 0x12, 0x3e, 0x31, 0x52, 0x77, 0x7c, 0x25, 0x50, 0x36,
	0x9d, 0x6a, 0xd9, 0xd5, 0xa9, 0x80, 0x95, 0x60, 0xc9, 0x4f, 0x73, 0x47, 0xcf, 0x85, 0x32, 0x02,
	0x17, 0x2a, 0xc2, 0x38, 0x4d, 0x71, 0xc1, 0xb6, 0x25, 0x4d, 0x9d, 0xd2, 0x2a, 0x3c, 0xc7, 0xc6,
	0xf9, 0x0f, 0x53, 0x63, 0x02, 0x42, 0x42, 0x35, 0x66, 0x09, 0x46, 0xef, 0xea, 0x6e, 0x40, 0x05,
	0xa9, 0xb2, 0x78, 0x19, 0xc6, 0x44, 0xdc, 0x21, 0x80, 0x5e, 0x2a, 0x43, 0xe9, 0x01, 0xb2, 0x5c,
	0x56, 0x02, 0x85, 0x6b, 0xa1, 0xd9, 0x9e, 0x2c, 0xef, 0x1d, 0xa9, 0x99, 0xfe, 0x07, 0x27, 0x13,
	0x0f, 0x10, 0x96, 0x44, 0x24, 0x2e, 0x89, 0xdf, 0x23, 0x98, 0x09, 0xdf, 0xe6, 0x36, 0xd9, 0xb3,
	0x2a, 0xef, 0xd9, 0x5b, 0x81, 0x24, 0xf6, 0x58, 0x7f, 0x60, 0xbb, 0xc6, 0xf6, 0x1e, 0x3e, 0x0d,
	0xbd, 0xb6, 0xe3, 0x96, 0xa2, 0x3d, 0x6c, 0xce, 0x76, 0xdc, 0x4d, 0xa3, 0x1a, 0x2b, 0x05, 0x99,
	0xc3, 0x96, 0x82, 0xbf, 0x3b, 0xa1, 0xe2, 0x61, 0x2d, 0x87, 0x4a, 0xaa, 0xb9, 0x66, 0x9d, 0x7d,
	0xb3, 0x17, 0xcc, 0x86, 0x3b, 0x7b, 0xbf, 0x0d, 0x6c, 0x65, 0x87, 0x6e, 0x41, 0x6a, 0x9e, 0x82,
	0x61, 0x8b, 0x01, 0xa2, 0x48, 0xb8, 0x87, 0xf7, 0xf0, 0xb1, 0x83, 0x2f, 0x6f, 0x1a, 0x6c, 0xec,
	0x08, 0x56, 0xa2, 0x9c, 0xa8, 0x12, 0xfd, 0x8e, 0xe0, 0xaa, 0xf8, 0xf2, 0xe1, 0xf2, 0x72, 0xe4,
	0x3a, 0x68, 0x5b, 0x5b, 0x8e, 0xe6, 0xf2, 0xda, 0x2b, 0x04, 0xb3, 0x89, 0x96, 0xe5, 0xbe, 0xcc,
	0x56, 0xbd, 0x6b, 0x25, 0x0d, 0x4c, 0xd1, 0x7a, 0x95, 0x39, 0x6c, 0xbd, 0x7a, 0x9d, 0x88, 0x27,
	0x5c, 0x47, 0x8e, 0x56, 0xcd, 0x9e, 0x22, 0xb3, 0x71, 0x45, 0x2e, 0xbc, 0x98, 0x80, 0xa1, 0x30,
	0x20, 0xfc, 0x19, 0x0c, 0x85, 0xc7, 0x6a, 0x7c, 0x36, 0x7a, 0xd1, 0xd8, 0xec, 0xae, 0x9c, 0x9a,
	0x6f, 0xbe, 0x7b, 0xcc, 0x3f, 0xbc, 0xb7, 0xcc, 0xdf, 0x3d, 0xee, 0x98, 0x8e, 0xbb, 0x57, 0xfa,
	0x70, 0x59, 0xbb, 0xf4, 0x7c, 0xff, 0x20, 0x8b, 0xbe, 0xd9, 0x3f, 0xc8, 0x76, 0x37, 0x16, 0x77,
	0x16, 0xbf, 0xdb, 0x3f, 0xc8, 0x16, 0xe6, 0x1a, 0xea, 0x52, 0xc3, 0xa8, 0x16, 0xd5, 0xb9, 0x1d,
	0x75, 0xa9, 0x75, 0x8d, 0x22, 0x7e, 0x83, 0x60, 0x3c, 0xe1, 0xa5, 0x03, 0xcf, 0x46, 0x91, 0x24,
	0x3f, 0x89, 0xc8, 0x40, 0xca, 0x48, 0x42, 0xfa, 0x11, 0xc1, 0xe9, 0xb6, 0x0f, 0x01, 0xf8, 0x6a,
	0x14, 0x58, 0xa7, 0xb7, 0x05, 0xe5, 0x5f, 0x29, 0x39, 0x88, 0xa3, 0x4d, 0x50, 0xcc, 0x59, 0x8a,
	0x39, 0xd3, 0x60, 0x88, 0xfb, 0x7c, 0xc4, 0xf8, 0x57, 0x04, 0x93, 0xed, 0xc7, 0x2e, 0x2c, 0x75,
	0x60, 0xa8, 0x04, 0x2b, 0x0b, 0x69, 0x59, 0x88, 0xa3, 0xdd, 0xa0, 0x20, 0xbb, 0x29, 0xc8, 0xdc,
	0xce, 0xa2, 0xbd, 0x68, 0x31, 0xa0, 0xd3, 0x11, 0x8d, 0xaa, 0x1f, 0xcd, 0xd9, 0xea, 0x12, 0xf7,
	0xc1, 0xa2, 0x3a, 0x67, 0xa9, 0x4b, 0x6c, 0x78, 0x2c, 0x3e, 0xc2, 0xaf, 0x11, 0x2b, 0xb6, 0x82,
	0xa7, 0x84, 0xe9, 0x76, 0x60, 0x82, 0x65, 0x59, 0x99, 0x91, 0xa4, 0x24, 0x8e, 0xa6, 0x52, 0xb4,
	0x3d, 0x4c, 0xa5, 0xdc, 0x09, 0x86, 0xa3, 0xb6, 0x7f, 0x89, 0x68, 0x3c, 0x04, 0x3b, 0x6c, 0x51,
	0x3c, 0x44, 0x7a, 0x78, 0x45, 0xeb, 0x44, 0x42, 0x1c, 0xed, 0x1a, 0x3d, 0x3b, 0xc7, 0x5c, 0x70,
	0x67, 0xd1, 0x64, 0xa7, 0x47, 0x3d, 0x4f, 0x9d, 0x33, 0xd5, 0xa5, 0xc0, 0x88, 0x55, 0xc4, 0x16,
	0xe4, 0x45, 0xfd, 0x3e, 0xbe, 0xd8, 0xfe, 0xc0, 0x66, 0x63, 0xda, 0x21, 0x2c, 0x86, 0x29, 0xa6,
	0x5e, 0x8a, 0xa9, 0x8b, 0xe2, 0xe9, 0x62, 0xf6, 0x10, 0xce, 0x19, 0x71, 0x7b, 0x24, 0x0d, 0x34,
	0xa9, 0xed, 0xd1, 0xd7, 0xce, 0x1e, 0xcf, 0xe0, 0x64, 0x9b, 0xb9, 0x07, 0xcf, 0x4b, 0xa1, 0x4a,
	0xa5, 0x8e, 0x63, 0x01, 0x75, 0x7c, 0x0a, 0x27, 0x04, 0x03, 0x07, 0x8e, 0x35, 0x6d, 0xe2, 0xa9,
	0xa4, 0xc3, 0x69, 0xec, 0xf2, 0xd0, 0xee, 0xf2, 0x9f, 0x43, 0x5e, 0x34, 0xb0, 0xc4, 0xad, 0x9f,
	0x30, 0xd6, 0xc8, 0x00, 0xe8, 0x6f, 0x07, 0xe0, 0x6b, 0x04, 0xa3, 0xc2, 0xb9, 0x2e, 0xee, 0x0e,
	0x49, 0xa3, 0x61, 0xdc, 0x1d, 0x12, 0x07, 0x45, 0x9e, 0xf1, 0x06, 0x84, 0x19, 0xef, 0x35, 0xf2,
	0x67, 0xbf, 0xe8, 0x78, 0x84, 0x67, 0xc4, 0xda, 0x10, 0x8c, 0x51, 0x32, 0x45, 0x62, 0x50, 0xb2,
	0x48, 0xbc, 0x44, 0x30, 0x2a, 0x9c, 0xb6, 0xe2, 0xaa, 0x49, 0x1a, 0xca, 0x64, 0xe0, 0x0c, 0x49,
	0xc2, 0xf9, 0x09, 0xc1, 0x78, 0xc2, 0xec, 0x16, 0x2f, 0xa3, 0xc9, 0x43, 0x5e, 0x07, 0x48, 0xb7,
	0x29, 0xa4, 0x61, 0x96, 0xed, 0x29, 0x24, 0x97, 0x81, 0xba, 0x9c, 0x04, 0x4a, 0x9d, 0x73, 0x5b,
	0xe9, 0x8c, 0x37, 0x7a, 0x45, 0xfc, 0x0a, 0xc1, 0xf1, 0xd8, 0x53, 0x3e, 0x8e, 0x35, 0x57, 0xa2,
	0x5f, 0x06, 0xca, 0x05, 0x09, 0x2a, 0xe2, 0x70, 0xc5, 0x8d, 0x48, 0x2a, 0xee, 0x5b, 0x04, 0x79,
	0xd1, 0x4c, 0x19, 0x0f, 0xb2, 0x84, 0xc9, 0x55, 0x99, 0x96, 0x23, 0xf4, 0xd3, 0xdd, 0xf1, 0x76,
	0x01, 0xf7, 0x06, 0x01, 0x8e, 0x8f, 0x8f, 0xf8, 0x82, 0xe0, 0x88, 0xf8, 0x80, 0xaa, 0x4c, 0xc9,
	0x90, 0xf9, 0x0a, 0xc2, 0x92, 0x0a, 0xfa, 0x19, 0xf9, 0x43, 0x7b, 0x6c, 0x1a, 0x8c, 0x7b, 0x56,
	0xf2, 0x5c, 0xaa, 0x5c, 0x92, 0xa6, 0x25, 0x8e, 0x76, 0x9d, 0x22, 0x3c, 0xe1, 0x15, 0x4b, 0xee,
	0x66, 0xe7, 0x64, 0xbc, 0xcb, 0x82, 0xbc, 0xe8, 0x89, 0x26, 0x6e, 0xcc, 0x84, 0x87, 0x1c, 0x99,
	0x02, 0x91, 0x0f, 0x14, 0x88, 0x5f, 0x10, 0x9c, 0xe9, 0xf0, 0x8a, 0x8c, 0x53, 0xb4, 0x55, 0xfe,
	0x0b, 0xb6, 0x72, 0x2d, 0x35, 0x8f, 0xef, 0x5e, 0xa3, 0xed, 0xdc, 0xeb, 0x39, 0x82, 0x31, 0xf1,
	0xcf, 0xb4, 0x78, 0x16, 0x4d, 0xfc, 0x73, 0xa7, 0xcc, 0xca, 0x92, 0x12, 0x87, 0x6b, 0x6c, 0xac,
	0xa5, 0x31, 0x25, 0xf7, 0x62, 0xff, 0x20, 0xfb, 0x76, 0x6f, 0x79, 0xe4, 0xed, 0xbb, 0x49, 0xf4,
	0xdb, 0xbb, 0x49, 0xf4, 0xe7, 0xbb, 0x49, 0xf4, 0xe6, 0xaf, 0xc9, 0xae, 0x7f, 0x02, 0x00, 0x00,
	0xff, 0xff, 0x94, 0xa0, 0xc4, 0x27, 0x8c, 0x1d, 0x00, 0x00,
}
